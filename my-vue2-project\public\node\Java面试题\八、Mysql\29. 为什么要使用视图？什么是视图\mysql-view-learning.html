<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL视图 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .table-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .table-box {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 200px;
        }

        .table-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .table-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
            font-size: 1.1rem;
        }

        .table-data {
            border-collapse: collapse;
            width: 100%;
            font-size: 0.9rem;
        }

        .table-data th,
        .table-data td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }

        .table-data th {
            background: #667eea;
            color: white;
        }

        .arrow {
            font-size: 2rem;
            color: #667eea;
            animation: pulse 2s infinite;
        }

        .view-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: 3px dashed #fff;
        }

        .view-box .table-title {
            color: white;
        }

        .view-box .table-data th {
            background: rgba(255,255,255,0.2);
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .game-area {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .score {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }

        .question {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
        }

        .options {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .option {
            background: white;
            border: 2px solid #ddd;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .option:hover {
            border-color: #667eea;
            transform: scale(1.05);
        }

        .option.correct {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        .option.wrong {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #animationCanvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .explanation {
            background: rgba(255,255,255,0.9);
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 0.5s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">MySQL 视图学习之旅</h1>
            <p class="subtitle">通过动画和游戏，轻松掌握数据库视图概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是视图？</h2>
            <div class="explanation">
                <p><span class="highlight">视图</span>是MySQL数据库中的一个重要特性，它本质上是一种<span class="highlight">虚拟表</span>。</p>
                <p>想象一下，视图就像是一个<span class="highlight">魔法窗口</span>，透过这个窗口，你只能看到你需要的数据，而不是整个数据库的所有内容。</p>
            </div>
            
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="animationCanvas" width="800" height="400"></canvas>
                </div>
                <button class="interactive-btn" onclick="startViewAnimation()">🎬 播放视图动画</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🏗️ 视图的特点</h2>
            <div class="explanation">
                <p>视图有以下重要特点：</p>
                <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                    <li><span class="highlight">虚拟性</span>：视图在物理上不存在，不占用存储空间</li>
                    <li><span class="highlight">动态性</span>：视图的数据来自基本表，实时更新</li>
                    <li><span class="highlight">安全性</span>：只显示用户需要的数据，隐藏敏感信息</li>
                    <li><span class="highlight">简化性</span>：将复杂查询封装成简单的视图</li>
                </ul>
            </div>

            <div class="table-container">
                <div class="table-box" id="baseTable">
                    <div class="table-title">📊 基础表 (employees)</div>
                    <table class="table-data">
                        <tr><th>ID</th><th>姓名</th><th>部门</th><th>工资</th><th>电话</th></tr>
                        <tr><td>1</td><td>张三</td><td>技术部</td><td>8000</td><td>138****1234</td></tr>
                        <tr><td>2</td><td>李四</td><td>销售部</td><td>6000</td><td>139****5678</td></tr>
                        <tr><td>3</td><td>王五</td><td>技术部</td><td>9000</td><td>137****9012</td></tr>
                    </table>
                </div>

                <div class="arrow">→</div>

                <div class="table-box view-box" id="viewTable">
                    <div class="table-title">🔍 视图 (tech_employees)</div>
                    <table class="table-data">
                        <tr><th>姓名</th><th>部门</th></tr>
                        <tr><td>张三</td><td>技术部</td></tr>
                        <tr><td>王五</td><td>技术部</td></tr>
                    </table>
                </div>
            </div>

            <button class="interactive-btn" onclick="demonstrateViewFiltering()">🎯 演示视图过滤</button>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 互动小游戏</h2>
            <div class="game-area">
                <div class="score">得分: <span id="score">0</span></div>
                <div class="question" id="question">点击开始游戏！</div>
                <div class="options" id="options"></div>
                <button class="interactive-btn" onclick="startGame()">🎲 开始游戏</button>
                <button class="interactive-btn" onclick="nextQuestion()" id="nextBtn" style="display:none;">下一题</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔒 视图的安全性</h2>
            <div class="explanation">
                <p>视图提供了强大的<span class="highlight">数据安全保护</span>：</p>
                <p>• <strong>数据隐藏</strong>：敏感信息（如工资、电话）可以被隐藏</p>
                <p>• <strong>权限控制</strong>：不同用户看到不同的数据视图</p>
                <p>• <strong>操作限制</strong>：可以限制用户的增删改操作</p>
            </div>

            <div class="demo-area">
                <h3 style="text-align: center; margin-bottom: 20px;">🎭 角色扮演演示</h3>
                <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;">
                    <button class="interactive-btn" onclick="showManagerView()">👔 经理视图</button>
                    <button class="interactive-btn" onclick="showEmployeeView()">👤 员工视图</button>
                    <button class="interactive-btn" onclick="showHRView()">👥 HR视图</button>
                </div>
                <div id="roleView" style="margin-top: 20px;"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 学习总结</h2>
            <div class="explanation">
                <h3>🌟 关键要点回顾：</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h4>虚拟表</h4>
                        <p>视图不占用物理存储空间</p>
                    </div>
                    <div style="background: linear-gradient(135deg, #ff9a9e, #fecfef); color: #333; padding: 20px; border-radius: 10px; text-align: center;">
                        <h4>动态更新</h4>
                        <p>数据来自基础表，实时同步</p>
                    </div>
                    <div style="background: linear-gradient(135deg, #a8edea, #fed6e3); color: #333; padding: 20px; border-radius: 10px; text-align: center;">
                        <h4>安全保护</h4>
                        <p>隐藏敏感数据，控制访问权限</p>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <button class="interactive-btn" onclick="resetLearning()">🔄 重新学习</button>
                <button class="interactive-btn" onclick="showCertificate()">🏆 获取学习证书</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let score = 0;
        let currentQuestion = 0;
        let gameQuestions = [
            {
                question: "视图在物理上是否存在？",
                options: ["存在", "不存在", "有时存在", "取决于数据量"],
                correct: 1,
                explanation: "视图是虚拟表，在物理上不存在，不占用存储空间。"
            },
            {
                question: "视图的数据来源是什么？",
                options: ["独立存储", "基础表", "内存缓存", "临时文件"],
                correct: 1,
                explanation: "视图的数据来自定义视图时引用的基础表。"
            },
            {
                question: "视图的主要作用是什么？",
                options: ["加快查询速度", "提高安全性", "减少存储空间", "备份数据"],
                correct: 1,
                explanation: "视图主要用于提高数据安全性，隐藏敏感信息，简化复杂查询。"
            }
        ];

        // Canvas动画
        function startViewAnimation() {
            const canvas = document.getElementById('animationCanvas');
            const ctx = canvas.getContext('2d');

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 动画参数
            let frame = 0;
            const maxFrames = 180;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制基础表
                const tableX = 50;
                const tableY = 50;
                const tableWidth = 200;
                const tableHeight = 150;

                ctx.fillStyle = '#667eea';
                ctx.fillRect(tableX, tableY, tableWidth, tableHeight);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('基础表', tableX + tableWidth/2, tableY + 30);
                ctx.fillText('(完整数据)', tableX + tableWidth/2, tableY + 50);

                // 绘制数据行
                ctx.fillStyle = 'rgba(255,255,255,0.8)';
                for(let i = 0; i < 4; i++) {
                    ctx.fillRect(tableX + 10, tableY + 70 + i*15, tableWidth - 20, 12);
                }

                // 绘制视图（动画效果）
                if(frame > 60) {
                    const viewX = 450;
                    const viewY = 50;
                    const viewWidth = 200;
                    const viewHeight = 100;

                    // 视图边框动画
                    const alpha = Math.min((frame - 60) / 60, 1);
                    ctx.globalAlpha = alpha;

                    ctx.strokeStyle = '#764ba2';
                    ctx.setLineDash([10, 5]);
                    ctx.lineWidth = 3;
                    ctx.strokeRect(viewX, viewY, viewWidth, viewHeight);

                    ctx.fillStyle = 'rgba(118, 75, 162, 0.1)';
                    ctx.fillRect(viewX, viewY, viewWidth, viewHeight);

                    ctx.globalAlpha = 1;
                    ctx.fillStyle = '#764ba2';
                    ctx.fillText('视图', viewX + viewWidth/2, viewY + 30);
                    ctx.fillText('(过滤数据)', viewX + viewWidth/2, viewY + 50);

                    // 绘制过滤后的数据
                    ctx.fillStyle = 'rgba(118, 75, 162, 0.6)';
                    for(let i = 0; i < 2; i++) {
                        ctx.fillRect(viewX + 10, viewY + 65 + i*15, viewWidth - 20, 12);
                    }
                }

                // 绘制箭头动画
                if(frame > 30) {
                    const arrowProgress = Math.min((frame - 30) / 30, 1);
                    const startX = tableX + tableWidth;
                    const endX = 450;
                    const arrowX = startX + (endX - startX) * arrowProgress;
                    const arrowY = 125;

                    ctx.strokeStyle = '#ff6b6b';
                    ctx.lineWidth = 3;
                    ctx.setLineDash([]);

                    ctx.beginPath();
                    ctx.moveTo(startX, arrowY);
                    ctx.lineTo(arrowX, arrowY);
                    ctx.stroke();

                    // 箭头头部
                    if(arrowProgress > 0.8) {
                        ctx.beginPath();
                        ctx.moveTo(arrowX, arrowY);
                        ctx.lineTo(arrowX - 10, arrowY - 5);
                        ctx.lineTo(arrowX - 10, arrowY + 5);
                        ctx.closePath();
                        ctx.fillStyle = '#ff6b6b';
                        ctx.fill();
                    }
                }

                // 绘制说明文字
                if(frame > 120) {
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('视图从基础表中选择和过滤数据', canvas.width/2, 250);
                    ctx.fillText('只显示用户需要的信息，隐藏敏感数据', canvas.width/2, 270);
                }

                frame++;
                if(frame < maxFrames) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
            updateProgress(25);
        }

        // 演示视图过滤
        function demonstrateViewFiltering() {
            const baseTable = document.getElementById('baseTable');
            const viewTable = document.getElementById('viewTable');

            // 高亮动画
            baseTable.style.transform = 'scale(1.05)';
            baseTable.style.boxShadow = '0 15px 30px rgba(102, 126, 234, 0.3)';

            setTimeout(() => {
                viewTable.style.transform = 'scale(1.05)';
                viewTable.style.boxShadow = '0 15px 30px rgba(118, 75, 162, 0.3)';

                setTimeout(() => {
                    baseTable.style.transform = 'scale(1)';
                    baseTable.style.boxShadow = '0 10px 20px rgba(0,0,0,0.1)';
                    viewTable.style.transform = 'scale(1)';
                    viewTable.style.boxShadow = '0 10px 20px rgba(0,0,0,0.1)';
                }, 1000);
            }, 500);

            updateProgress(50);
        }

        // 开始游戏
        function startGame() {
            score = 0;
            currentQuestion = 0;
            document.getElementById('score').textContent = score;
            nextQuestion();
        }

        // 下一题
        function nextQuestion() {
            if(currentQuestion >= gameQuestions.length) {
                endGame();
                return;
            }

            const question = gameQuestions[currentQuestion];
            document.getElementById('question').textContent = question.question;

            const optionsDiv = document.getElementById('options');
            optionsDiv.innerHTML = '';

            question.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option';
                optionDiv.textContent = option;
                optionDiv.onclick = () => selectOption(index);
                optionsDiv.appendChild(optionDiv);
            });

            document.getElementById('nextBtn').style.display = 'none';
        }

        // 选择选项
        function selectOption(selectedIndex) {
            const question = gameQuestions[currentQuestion];
            const options = document.querySelectorAll('.option');

            options.forEach((option, index) => {
                option.onclick = null; // 禁用点击
                if(index === question.correct) {
                    option.classList.add('correct');
                } else if(index === selectedIndex) {
                    option.classList.add('wrong');
                }
            });

            if(selectedIndex === question.correct) {
                score += 10;
                document.getElementById('score').textContent = score;
            }

            // 显示解释
            setTimeout(() => {
                alert(question.explanation);
                currentQuestion++;
                document.getElementById('nextBtn').style.display = 'inline-block';
            }, 1000);
        }

        // 结束游戏
        function endGame() {
            document.getElementById('question').textContent = `游戏结束！你的总分是：${score}分`;
            document.getElementById('options').innerHTML = '';
            document.getElementById('nextBtn').style.display = 'none';
            updateProgress(75);
        }

        // 显示不同角色视图
        function showManagerView() {
            const roleView = document.getElementById('roleView');
            roleView.innerHTML = `
                <div class="table-box" style="margin: 0 auto; max-width: 400px;">
                    <div class="table-title">👔 经理视图 - 完整信息</div>
                    <table class="table-data">
                        <tr><th>姓名</th><th>部门</th><th>工资</th><th>绩效</th></tr>
                        <tr><td>张三</td><td>技术部</td><td>8000</td><td>优秀</td></tr>
                        <tr><td>李四</td><td>销售部</td><td>6000</td><td>良好</td></tr>
                        <tr><td>王五</td><td>技术部</td><td>9000</td><td>优秀</td></tr>
                    </table>
                </div>
            `;
        }

        function showEmployeeView() {
            const roleView = document.getElementById('roleView');
            roleView.innerHTML = `
                <div class="table-box" style="margin: 0 auto; max-width: 300px;">
                    <div class="table-title">👤 员工视图 - 基本信息</div>
                    <table class="table-data">
                        <tr><th>姓名</th><th>部门</th><th>职位</th></tr>
                        <tr><td>张三</td><td>技术部</td><td>开发工程师</td></tr>
                        <tr><td>李四</td><td>销售部</td><td>销售专员</td></tr>
                        <tr><td>王五</td><td>技术部</td><td>高级工程师</td></tr>
                    </table>
                </div>
            `;
        }

        function showHRView() {
            const roleView = document.getElementById('roleView');
            roleView.innerHTML = `
                <div class="table-box" style="margin: 0 auto; max-width: 350px;">
                    <div class="table-title">👥 HR视图 - 人事信息</div>
                    <table class="table-data">
                        <tr><th>姓名</th><th>入职日期</th><th>合同状态</th></tr>
                        <tr><td>张三</td><td>2023-01-15</td><td>正式</td></tr>
                        <tr><td>李四</td><td>2023-03-20</td><td>试用</td></tr>
                        <tr><td>王五</td><td>2022-08-10</td><td>正式</td></tr>
                    </table>
                </div>
            `;
        }

        // 更新进度条
        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // 重新学习
        function resetLearning() {
            score = 0;
            currentQuestion = 0;
            document.getElementById('score').textContent = score;
            document.getElementById('question').textContent = '点击开始游戏！';
            document.getElementById('options').innerHTML = '';
            document.getElementById('nextBtn').style.display = 'none';
            document.getElementById('roleView').innerHTML = '';
            updateProgress(0);

            // 滚动到顶部
            window.scrollTo({top: 0, behavior: 'smooth'});
        }

        // 显示学习证书
        function showCertificate() {
            updateProgress(100);
            alert('🎉 恭喜你完成了MySQL视图的学习！\n\n你已经掌握了：\n✅ 视图的基本概念\n✅ 视图的特点和作用\n✅ 视图的安全性\n✅ 视图的实际应用\n\n继续加油，探索更多数据库知识！');
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            // 添加一些交互提示
            setTimeout(() => {
                const sections = document.querySelectorAll('.section');
                sections.forEach((section, index) => {
                    section.addEventListener('mouseenter', () => {
                        section.style.transform = 'translateY(-5px)';
                    });
                    section.addEventListener('mouseleave', () => {
                        section.style.transform = 'translateY(0)';
                    });
                });
            }, 1000);
        };
    </script>
</body>
</html>
