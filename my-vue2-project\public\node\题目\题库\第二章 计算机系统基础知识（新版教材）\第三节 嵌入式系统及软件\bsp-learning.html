<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BSP板级支持包 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .option.selected {
            background: rgba(255,255,255,0.3);
            border-color: white;
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.3);
            border-color: #f44336;
        }

        .btn {
            background: white;
            color: #667eea;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            font-size: 1.1rem;
            text-align: center;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .result.show {
            opacity: 1;
        }

        .result.correct {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
        }

        .result.wrong {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #f44336;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">BSP 板级支持包</h1>
            <p class="subtitle">Board Support Package - 硬件与操作系统的桥梁</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">什么是BSP？</h2>
            <div class="canvas-container">
                <canvas id="bspCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <strong>BSP（板级支持包）</strong>是嵌入式系统中的关键组件，它就像一个"翻译官"，帮助操作系统与硬件进行沟通。
                想象一下：如果硬件是一个只说中文的人，操作系统是一个只说英文的人，那么BSP就是那个精通两种语言的翻译官！
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">BSP的特性分析</h2>
            <div class="canvas-container">
                <canvas id="characteristicsCanvas" width="800" height="500"></canvas>
            </div>
            <div class="explanation">
                <strong>硬件有关性：</strong>BSP必须了解具体硬件的细节，比如寄存器地址、中断号等。<br>
                <strong>操作系统有关性：</strong>不同的操作系统（如Linux、VxWorks）需要不同格式的BSP。
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title">测试时间！</h2>
            <div class="question">
                在嵌入式操作系统中，板级支持包BSP作为对硬件的抽象，实现了（ ）。
            </div>
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 硬件无关性，操作系统无关性
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 硬件有关性，操作系统有关性
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 硬件无关性，操作系统有关性
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 硬件有关性，操作系统无关性
                </div>
            </div>
            <button class="btn" onclick="checkAnswer()">提交答案</button>
            <div class="result" id="result"></div>
        </div>
    </div>

    <script>
        // BSP概念动画
        const bspCanvas = document.getElementById('bspCanvas');
        const bspCtx = bspCanvas.getContext('2d');
        
        // 特性分析动画
        const charCanvas = document.getElementById('characteristicsCanvas');
        const charCtx = charCanvas.getContext('2d');
        
        let animationFrame = 0;
        let selectedAnswer = null;
        
        // BSP概念动画函数
        function drawBSPConcept() {
            bspCtx.clearRect(0, 0, bspCanvas.width, bspCanvas.height);
            
            const time = animationFrame * 0.05;
            
            // 绘制硬件层
            bspCtx.fillStyle = '#FF6B6B';
            bspCtx.fillRect(50, 300, 200, 80);
            bspCtx.fillStyle = 'white';
            bspCtx.font = 'bold 16px Arial';
            bspCtx.textAlign = 'center';
            bspCtx.fillText('硬件层', 150, 345);
            
            // 绘制BSP层（动态效果）
            const bspY = 200 + Math.sin(time) * 5;
            bspCtx.fillStyle = '#4ECDC4';
            bspCtx.fillRect(50, bspY, 200, 80);
            bspCtx.fillStyle = 'white';
            bspCtx.fillText('BSP层', 150, bspY + 45);
            
            // 绘制操作系统层
            bspCtx.fillStyle = '#45B7D1';
            bspCtx.fillRect(50, 100, 200, 80);
            bspCtx.fillStyle = 'white';
            bspCtx.fillText('操作系统层', 150, 145);
            
            // 绘制连接箭头（动画效果）
            const arrowOffset = Math.sin(time * 2) * 3;
            bspCtx.strokeStyle = '#333';
            bspCtx.lineWidth = 3;
            bspCtx.beginPath();
            
            // 硬件到BSP的箭头
            bspCtx.moveTo(150 + arrowOffset, 300);
            bspCtx.lineTo(150 + arrowOffset, bspY + 80);
            bspCtx.stroke();
            
            // BSP到操作系统的箭头
            bspCtx.moveTo(150 - arrowOffset, bspY);
            bspCtx.lineTo(150 - arrowOffset, 180);
            bspCtx.stroke();
            
            // 绘制说明文字
            bspCtx.fillStyle = '#333';
            bspCtx.font = '14px Arial';
            bspCtx.textAlign = 'left';
            bspCtx.fillText('BSP是硬件和操作系统之间的桥梁', 300, 200);
            bspCtx.fillText('• 向下：直接操作硬件寄存器', 300, 230);
            bspCtx.fillText('• 向上：为操作系统提供标准接口', 300, 250);
            
            animationFrame++;
            requestAnimationFrame(drawBSPConcept);
        }
        
        // 特性分析动画函数
        function drawCharacteristics() {
            charCtx.clearRect(0, 0, charCanvas.width, charCanvas.height);
            
            const time = animationFrame * 0.03;
            
            // 绘制硬件相关性示例
            charCtx.fillStyle = '#FF6B6B';
            charCtx.fillRect(50, 50, 300, 180);
            charCtx.fillStyle = 'white';
            charCtx.font = 'bold 18px Arial';
            charCtx.textAlign = 'center';
            charCtx.fillText('硬件有关性', 200, 80);
            
            // 动态显示硬件细节
            const details1 = [
                '寄存器地址: 0x40000000',
                '中断号: IRQ_15',
                'GPIO端口: PORTA',
                '时钟频率: 100MHz'
            ];
            
            charCtx.font = '12px Arial';
            charCtx.textAlign = 'left';
            for (let i = 0; i < details1.length; i++) {
                const alpha = Math.max(0, Math.sin(time + i * 0.5));
                charCtx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
                charCtx.fillText(details1[i], 70, 110 + i * 25);
            }
            
            // 绘制操作系统相关性示例
            charCtx.fillStyle = '#45B7D1';
            charCtx.fillRect(450, 50, 300, 180);
            charCtx.fillStyle = 'white';
            charCtx.font = 'bold 18px Arial';
            charCtx.textAlign = 'center';
            charCtx.fillText('操作系统有关性', 600, 80);
            
            // 动态显示操作系统差异
            const details2 = [
                'Linux BSP: device_driver结构',
                'VxWorks BSP: BSP_FUNC表',
                'FreeRTOS BSP: HAL接口',
                'RT-Thread BSP: rt_device'
            ];
            
            charCtx.font = '12px Arial';
            charCtx.textAlign = 'left';
            for (let i = 0; i < details2.length; i++) {
                const alpha = Math.max(0, Math.sin(time + i * 0.5 + Math.PI));
                charCtx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
                charCtx.fillText(details2[i], 470, 110 + i * 25);
            }
            
            // 绘制中央连接
            charCtx.strokeStyle = '#4ECDC4';
            charCtx.lineWidth = 4;
            charCtx.setLineDash([10, 5]);
            charCtx.beginPath();
            charCtx.moveTo(350, 140);
            charCtx.lineTo(450, 140);
            charCtx.stroke();
            charCtx.setLineDash([]);
            
            // 绘制BSP标签
            const bspScale = 1 + Math.sin(time * 2) * 0.1;
            charCtx.save();
            charCtx.translate(400, 140);
            charCtx.scale(bspScale, bspScale);
            charCtx.fillStyle = '#4ECDC4';
            charCtx.fillRect(-30, -15, 60, 30);
            charCtx.fillStyle = 'white';
            charCtx.font = 'bold 14px Arial';
            charCtx.textAlign = 'center';
            charCtx.fillText('BSP', 0, 5);
            charCtx.restore();
            
            // 底部解释
            charCtx.fillStyle = '#333';
            charCtx.font = '16px Arial';
            charCtx.textAlign = 'center';
            charCtx.fillText('BSP必须同时了解硬件细节和操作系统接口规范', 400, 300);
            charCtx.fillText('因此具有硬件有关性和操作系统有关性', 400, 330);
            
            requestAnimationFrame(drawCharacteristics);
        }
        
        // 选项点击处理
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedAnswer = this.dataset.answer;
            });
        });
        
        // 检查答案
        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }
            
            const result = document.getElementById('result');
            const options = document.querySelectorAll('.option');
            
            // 显示正确答案
            options.forEach(option => {
                if (option.dataset.answer === 'B') {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && option.dataset.answer !== 'B') {
                    option.classList.add('wrong');
                }
            });
            
            if (selectedAnswer === 'B') {
                result.className = 'result show correct';
                result.innerHTML = `
                    <h3>🎉 恭喜你答对了！</h3>
                    <p><strong>解析：</strong>BSP具有硬件有关性，因为它需要直接操作具体硬件的寄存器、中断等；
                    同时具有操作系统有关性，因为不同操作系统需要不同格式的BSP接口。</p>
                `;
            } else {
                result.className = 'result show wrong';
                result.innerHTML = `
                    <h3>❌ 答案不正确</h3>
                    <p><strong>正确答案是B。</strong>BSP作为硬件抽象层，必须了解硬件细节（硬件有关性），
                    同时要适配特定操作系统的接口规范（操作系统有关性）。</p>
                `;
            }
        }
        
        // 启动动画
        drawBSPConcept();
        drawCharacteristics();
    </script>
</body>
</html>
