<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象编程 - 类的分类</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 60px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .nav-item {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 20px;
            padding: 15px 30px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.2);
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .content-section {
            display: none;
            animation: fadeIn 0.8s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .card-title {
            font-size: 2.2rem;
            color: #333;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
        }

        .card-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: 600;
        }

        .example-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }

        .example-title {
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 10px;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">面向对象编程</h1>
            <p class="subtitle">类的分类：实体类、边界类、控制类</p>
        </div>

        <div class="nav-menu">
            <div class="nav-item active" data-section="overview">概述</div>
            <div class="nav-item" data-section="entity">实体类</div>
            <div class="nav-item" data-section="boundary">边界类</div>
            <div class="nav-item" data-section="control">控制类</div>
            <div class="nav-item" data-section="example">综合示例</div>
        </div>

        <!-- 概述部分 -->
        <div class="content-section active" id="overview">
            <div class="card">
                <h2 class="card-title">什么是类的分类？</h2>
                <div class="description">
                    在面向对象程序设计中，<span class="highlight">类</span>是封装了信息和行为的重要组成部分。
                    根据类在系统中的作用和职责，我们可以将类分为三种主要类型：
                </div>
                
                <div class="canvas-container">
                    <canvas id="overviewCanvas" width="800" height="400"></canvas>
                </div>
                
                <div class="demo-area">
                    <button class="interactive-btn" onclick="animateOverview()">🎬 播放动画演示</button>
                    <button class="interactive-btn" onclick="resetOverview()">🔄 重置</button>
                </div>
                
                <div class="description">
                    点击上方按钮观看三种类型的动画演示，了解它们在系统中的不同角色！
                </div>
            </div>
        </div>

        <!-- 实体类部分 -->
        <div class="content-section" id="entity">
            <div class="card">
                <h2 class="card-title">实体类 (Entity Class)</h2>
                <div class="description">
                    <span class="highlight">实体类</span>映射需求中的每个实体，保存需要存储在永久存储体中的信息。
                    它是对用户来说最有意义的类，通常采用业务领域术语命名。
                </div>
                
                <div class="canvas-container">
                    <canvas id="entityCanvas" width="800" height="400"></canvas>
                </div>
                
                <div class="example-box">
                    <div class="example-title">特征：</div>
                    <ul>
                        <li>通常是名词（如：用户、订单、商品）</li>
                        <li>包含需要持久化的数据</li>
                        <li>在用例模型中，参与者一般对应实体类</li>
                        <li>具有明确的业务含义</li>
                    </ul>
                </div>
                
                <div class="demo-area">
                    <button class="interactive-btn" onclick="animateEntity()">👤 演示用户实体</button>
                    <button class="interactive-btn" onclick="showEntityData()">📊 显示数据结构</button>
                </div>
            </div>
        </div>

        <!-- 边界类部分 -->
        <div class="content-section" id="boundary">
            <div class="card">
                <h2 class="card-title">边界类 (Boundary Class)</h2>
                <div class="description">
                    <span class="highlight">边界类</span>用于封装在用例内、外流动的信息或数据流，
                    是系统与外部环境交互的接口，将系统内部与外部环境的变更隔离开。
                </div>

                <div class="canvas-container">
                    <canvas id="boundaryCanvas" width="800" height="400"></canvas>
                </div>

                <div class="example-box">
                    <div class="example-title">特征：</div>
                    <ul>
                        <li>处理用户界面交互（如：登录界面、查询界面）</li>
                        <li>管理外部系统通信（如：API接口、数据库连接）</li>
                        <li>封装输入输出操作</li>
                        <li>隔离系统内外部变更</li>
                    </ul>
                </div>

                <div class="demo-area">
                    <button class="interactive-btn" onclick="animateBoundary()">🖥️ 演示界面交互</button>
                    <button class="interactive-btn" onclick="showBoundaryFlow()">🔄 显示数据流</button>
                </div>
            </div>
        </div>

        <!-- 控制类部分 -->
        <div class="content-section" id="control">
            <div class="card">
                <h2 class="card-title">控制类 (Control Class)</h2>
                <div class="description">
                    <span class="highlight">控制类</span>用于控制用例工作的类，负责协调其他对象的行为，
                    通常由动宾结构的短语转化而来，具有协调性。
                </div>

                <div class="canvas-container">
                    <canvas id="controlCanvas" width="800" height="400"></canvas>
                </div>

                <div class="example-box">
                    <div class="example-title">特征：</div>
                    <ul>
                        <li>控制业务流程（如：身份验证、查询余额）</li>
                        <li>协调多个对象的交互</li>
                        <li>实现业务逻辑</li>
                        <li>通常包含动词（验证、处理、管理）</li>
                    </ul>
                </div>

                <div class="demo-area">
                    <button class="interactive-btn" onclick="animateControl()">⚙️ 演示控制流程</button>
                    <button class="interactive-btn" onclick="showControlLogic()">🧠 显示业务逻辑</button>
                </div>
            </div>
        </div>

        <!-- 综合示例部分 -->
        <div class="content-section" id="example">
            <div class="card">
                <h2 class="card-title">综合示例：银行系统</h2>
                <div class="description">
                    让我们通过一个完整的银行系统示例，看看三种类型的类是如何协作的：
                </div>

                <div class="canvas-container">
                    <canvas id="exampleCanvas" width="800" height="500"></canvas>
                </div>

                <div class="example-box">
                    <div class="example-title">题目分析：</div>
                    <p><strong>身份验证</strong> - 控制类（动宾结构，控制验证流程）</p>
                    <p><strong>用户</strong> - 实体类（名词，需要持久化的业务实体）</p>
                    <p><strong>通信协议</strong> - 边界类（系统与外部的交互接口）</p>
                    <p><strong>查询余额</strong> - 控制类（动宾结构，控制查询流程）</p>
                </div>

                <div class="demo-area">
                    <button class="interactive-btn" onclick="startBankDemo()">🏦 完整演示</button>
                    <button class="interactive-btn" onclick="stepByStep()">📝 分步解析</button>
                    <button class="interactive-btn" onclick="resetAll()">🔄 重置所有</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentAnimation = null;
        let animationStep = 0;

        // 导航功能
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有active类
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                document.querySelectorAll('.content-section').forEach(section => section.classList.remove('active'));

                // 添加active类
                this.classList.add('active');
                document.getElementById(this.dataset.section).classList.add('active');

                // 重置动画
                if (currentAnimation) {
                    clearInterval(currentAnimation);
                    currentAnimation = null;
                }
            });
        });

        // 概述动画
        function animateOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            if (currentAnimation) clearInterval(currentAnimation);

            currentAnimation = setInterval(() => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('面向对象编程中的三种类', canvas.width/2, 40);

                // 动画进度
                const progress = (frame % 180) / 180;

                // 实体类
                drawClassBox(ctx, 100, 100, '实体类', '#e3f2fd', '#1976d2', progress * 3);
                if (progress > 0.3) {
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillStyle = '#666';
                    ctx.textAlign = 'center';
                    ctx.fillText('用户、订单、商品', 150, 180);
                    ctx.fillText('(业务实体)', 150, 200);
                }

                // 边界类
                if (progress > 0.3) {
                    drawClassBox(ctx, 350, 100, '边界类', '#f3e5f5', '#7b1fa2', (progress - 0.3) * 3);
                    if (progress > 0.6) {
                        ctx.fillText('登录界面、API', 400, 180);
                        ctx.fillText('(系统接口)', 400, 200);
                    }
                }

                // 控制类
                if (progress > 0.6) {
                    drawClassBox(ctx, 600, 100, '控制类', '#e8f5e8', '#388e3c', (progress - 0.6) * 3);
                    if (progress > 0.9) {
                        ctx.fillText('身份验证、查询余额', 650, 180);
                        ctx.fillText('(业务逻辑)', 650, 200);
                    }
                }

                // 绘制连接线
                if (progress > 0.9) {
                    drawConnections(ctx, progress);
                }

                frame++;
                if (frame > 200) {
                    clearInterval(currentAnimation);
                    currentAnimation = null;
                }
            }, 50);
        }

        function drawClassBox(ctx, x, y, title, bgColor, borderColor, alpha) {
            if (alpha <= 0) return;

            ctx.save();
            ctx.globalAlpha = Math.min(alpha, 1);

            // 绘制阴影
            ctx.shadowColor = 'rgba(0,0,0,0.2)';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetY = 5;

            // 绘制背景
            ctx.fillStyle = bgColor;
            ctx.fillRect(x, y, 100, 60);

            // 绘制边框
            ctx.strokeStyle = borderColor;
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 100, 60);

            // 绘制文字
            ctx.shadowColor = 'transparent';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillStyle = borderColor;
            ctx.textAlign = 'center';
            ctx.fillText(title, x + 50, y + 35);

            ctx.restore();
        }

        function drawConnections(ctx, progress) {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);

            // 实体类到边界类
            ctx.beginPath();
            ctx.moveTo(200, 130);
            ctx.lineTo(350, 130);
            ctx.stroke();

            // 边界类到控制类
            ctx.beginPath();
            ctx.moveTo(450, 130);
            ctx.lineTo(600, 130);
            ctx.stroke();

            ctx.setLineDash([]);
        }

        function resetOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (currentAnimation) {
                clearInterval(currentAnimation);
                currentAnimation = null;
            }
        }

        // 实体类动画
        function animateEntity() {
            const canvas = document.getElementById('entityCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            if (currentAnimation) clearInterval(currentAnimation);

            currentAnimation = setInterval(() => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('实体类示例：用户类', canvas.width/2, 30);

                const progress = (frame % 120) / 120;

                // 绘制用户图标
                drawUserIcon(ctx, 150, 80, progress);

                // 绘制属性
                if (progress > 0.3) {
                    drawUserProperties(ctx, 400, 80, progress - 0.3);
                }

                // 绘制数据库连接
                if (progress > 0.6) {
                    drawDatabaseConnection(ctx, 150, 250, progress - 0.6);
                }

                frame++;
                if (frame > 150) {
                    clearInterval(currentAnimation);
                    currentAnimation = null;
                }
            }, 60);
        }

        function drawUserIcon(ctx, x, y, alpha) {
            if (alpha <= 0) return;

            ctx.save();
            ctx.globalAlpha = Math.min(alpha * 2, 1);

            // 用户头像
            ctx.fillStyle = '#2196f3';
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, Math.PI * 2);
            ctx.fill();

            // 用户身体
            ctx.fillStyle = '#1976d2';
            ctx.fillRect(x - 25, y + 20, 50, 40);

            // 标签
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('用户实体', x, y + 80);

            ctx.restore();
        }

        function drawUserProperties(ctx, x, y, alpha) {
            if (alpha <= 0) return;

            ctx.save();
            ctx.globalAlpha = Math.min(alpha * 2, 1);

            // 属性框
            ctx.fillStyle = '#e3f2fd';
            ctx.fillRect(x, y, 200, 120);
            ctx.strokeStyle = '#1976d2';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 200, 120);

            // 属性列表
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillStyle = '#1976d2';
            ctx.textAlign = 'left';
            ctx.fillText('用户属性:', x + 10, y + 25);

            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('• ID: 用户唯一标识', x + 10, y + 50);
            ctx.fillText('• 姓名: 用户姓名', x + 10, y + 70);
            ctx.fillText('• 账号: 登录账号', x + 10, y + 90);
            ctx.fillText('• 余额: 账户余额', x + 10, y + 110);

            ctx.restore();
        }

        function drawDatabaseConnection(ctx, x, y, alpha) {
            if (alpha <= 0) return;

            ctx.save();
            ctx.globalAlpha = Math.min(alpha * 2, 1);

            // 数据库图标
            ctx.fillStyle = '#4caf50';
            ctx.fillRect(x - 30, y, 60, 40);
            ctx.fillRect(x - 25, y - 5, 50, 10);

            // 连接线
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 3;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(x, y - 50);
            ctx.lineTo(x, y);
            ctx.stroke();
            ctx.setLineDash([]);

            // 标签
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('持久化存储', x, y + 60);

            ctx.restore();
        }

        function showEntityData() {
            const canvas = document.getElementById('entityCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('实体类数据结构', canvas.width/2, 30);

            // 代码示例
            ctx.font = '14px Consolas, monospace';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'left';

            const code = [
                'class User {',
                '    private int id;',
                '    private String name;',
                '    private String account;',
                '    private double balance;',
                '    ',
                '    // 构造函数、getter、setter...',
                '}'
            ];

            code.forEach((line, index) => {
                ctx.fillText(line, 50, 80 + index * 25);
            });

            // 说明
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.fillText('实体类特点：包含业务数据，需要持久化保存', 50, 300);
        }

        // 边界类动画
        function animateBoundary() {
            const canvas = document.getElementById('boundaryCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            if (currentAnimation) clearInterval(currentAnimation);

            currentAnimation = setInterval(() => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('边界类示例：登录界面', canvas.width/2, 30);

                const progress = (frame % 120) / 120;

                // 绘制用户界面
                drawUserInterface(ctx, 100, 80, progress);

                // 绘制系统内部
                if (progress > 0.4) {
                    drawSystemCore(ctx, 500, 80, progress - 0.4);
                }

                // 绘制数据流
                if (progress > 0.7) {
                    drawDataFlow(ctx, progress - 0.7);
                }

                frame++;
                if (frame > 150) {
                    clearInterval(currentAnimation);
                    currentAnimation = null;
                }
            }, 60);
        }

        function drawUserInterface(ctx, x, y, alpha) {
            if (alpha <= 0) return;

            ctx.save();
            ctx.globalAlpha = Math.min(alpha * 2, 1);

            // 界面框
            ctx.fillStyle = '#f3e5f5';
            ctx.fillRect(x, y, 150, 200);
            ctx.strokeStyle = '#7b1fa2';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 150, 200);

            // 标题
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillStyle = '#7b1fa2';
            ctx.textAlign = 'center';
            ctx.fillText('登录界面', x + 75, y + 25);

            // 输入框
            ctx.fillStyle = 'white';
            ctx.fillRect(x + 20, y + 50, 110, 25);
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 1;
            ctx.strokeRect(x + 20, y + 50, 110, 25);

            ctx.fillRect(x + 20, y + 90, 110, 25);
            ctx.strokeRect(x + 20, y + 90, 110, 25);

            // 按钮
            ctx.fillStyle = '#7b1fa2';
            ctx.fillRect(x + 50, y + 130, 50, 30);

            // 文字
            ctx.font = '12px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'left';
            ctx.fillText('用户名', x + 25, y + 45);
            ctx.fillText('密码', x + 25, y + 85);

            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.fillText('登录', x + 75, y + 150);

            ctx.restore();
        }

        function drawSystemCore(ctx, x, y, alpha) {
            if (alpha <= 0) return;

            ctx.save();
            ctx.globalAlpha = Math.min(alpha * 2, 1);

            // 系统核心
            ctx.fillStyle = '#e8f5e8';
            ctx.fillRect(x, y, 150, 200);
            ctx.strokeStyle = '#388e3c';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 150, 200);

            // 标题
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillStyle = '#388e3c';
            ctx.textAlign = 'center';
            ctx.fillText('系统内部', x + 75, y + 25);

            // 组件
            ctx.font = '12px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'left';
            ctx.fillText('• 用户验证', x + 20, y + 60);
            ctx.fillText('• 数据处理', x + 20, y + 80);
            ctx.fillText('• 业务逻辑', x + 20, y + 100);
            ctx.fillText('• 数据存储', x + 20, y + 120);

            ctx.restore();
        }

        function drawDataFlow(ctx, alpha) {
            if (alpha <= 0) return;

            ctx.save();
            ctx.globalAlpha = Math.min(alpha * 3, 1);

            // 数据流箭头
            ctx.strokeStyle = '#ff5722';
            ctx.lineWidth = 3;

            // 从界面到系统
            drawArrow(ctx, 250, 180, 500, 180);

            // 从系统到界面
            drawArrow(ctx, 500, 200, 250, 200);

            // 标签
            ctx.font = '12px Microsoft YaHei';
            ctx.fillStyle = '#ff5722';
            ctx.textAlign = 'center';
            ctx.fillText('用户输入', 375, 175);
            ctx.fillText('系统响应', 375, 215);

            ctx.restore();
        }

        function drawArrow(ctx, fromX, fromY, toX, toY) {
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fill();
        }

        function showBoundaryFlow() {
            const canvas = document.getElementById('boundaryCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('边界类数据流', canvas.width/2, 30);

            // 流程图
            const steps = [
                '用户输入',
                '界面验证',
                '数据传递',
                '系统处理',
                '结果返回'
            ];

            steps.forEach((step, index) => {
                const x = 50 + index * 140;
                const y = 150;

                // 步骤框
                ctx.fillStyle = index % 2 === 0 ? '#f3e5f5' : '#e8f5e8';
                ctx.fillRect(x, y, 120, 50);
                ctx.strokeStyle = index % 2 === 0 ? '#7b1fa2' : '#388e3c';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, 120, 50);

                // 文字
                ctx.font = '14px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText(step, x + 60, y + 30);

                // 箭头
                if (index < steps.length - 1) {
                    drawArrow(ctx, x + 120, y + 25, x + 140, y + 25);
                }
            });
        }

        // 控制类动画
        function animateControl() {
            const canvas = document.getElementById('controlCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            if (currentAnimation) clearInterval(currentAnimation);

            currentAnimation = setInterval(() => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('控制类示例：身份验证控制器', canvas.width/2, 30);

                const progress = (frame % 150) / 150;

                // 绘制控制流程
                drawControlFlow(ctx, progress);

                frame++;
                if (frame > 180) {
                    clearInterval(currentAnimation);
                    currentAnimation = null;
                }
            }, 60);
        }

        function drawControlFlow(ctx, progress) {
            const steps = [
                { name: '接收请求', x: 100, y: 100, color: '#e3f2fd' },
                { name: '验证输入', x: 300, y: 100, color: '#f3e5f5' },
                { name: '查询用户', x: 500, y: 100, color: '#e8f5e8' },
                { name: '验证密码', x: 300, y: 200, color: '#fff3e0' },
                { name: '返回结果', x: 100, y: 200, color: '#fce4ec' }
            ];

            steps.forEach((step, index) => {
                const stepProgress = Math.max(0, Math.min(1, (progress * 5) - index));

                if (stepProgress > 0) {
                    ctx.save();
                    ctx.globalAlpha = stepProgress;

                    // 步骤框
                    ctx.fillStyle = step.color;
                    ctx.fillRect(step.x - 50, step.y - 25, 100, 50);
                    ctx.strokeStyle = '#666';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(step.x - 50, step.y - 25, 100, 50);

                    // 文字
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillStyle = '#333';
                    ctx.textAlign = 'center';
                    ctx.fillText(step.name, step.x, step.y + 5);

                    ctx.restore();
                }

                // 连接线
                if (index < steps.length - 1 && stepProgress > 0.5) {
                    const nextStep = steps[index + 1];
                    drawArrow(ctx, step.x + 50, step.y, nextStep.x - 50, nextStep.y);
                }
            });
        }

        function showControlLogic() {
            const canvas = document.getElementById('controlCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('控制类业务逻辑', canvas.width/2, 30);

            // 代码示例
            ctx.font = '12px Consolas, monospace';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'left';

            const code = [
                'class AuthenticationController {',
                '    public boolean authenticate(String user, String pwd) {',
                '        // 1. 验证输入参数',
                '        if (user == null || pwd == null) return false;',
                '        ',
                '        // 2. 查询用户信息',
                '        User userInfo = userService.findByUsername(user);',
                '        ',
                '        // 3. 验证密码',
                '        return passwordEncoder.matches(pwd, userInfo.getPassword());',
                '    }',
                '}'
            ];

            code.forEach((line, index) => {
                ctx.fillText(line, 50, 80 + index * 20);
            });

            // 说明
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.fillText('控制类特点：协调其他对象，实现业务逻辑', 50, 320);
        }

        // 综合示例动画
        function startBankDemo() {
            const canvas = document.getElementById('exampleCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            if (currentAnimation) clearInterval(currentAnimation);

            currentAnimation = setInterval(() => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('银行系统完整示例', canvas.width/2, 30);

                const progress = (frame % 200) / 200;

                // 绘制完整系统
                drawBankSystem(ctx, progress);

                frame++;
                if (frame > 250) {
                    clearInterval(currentAnimation);
                    currentAnimation = null;
                }
            }, 80);
        }

        function drawBankSystem(ctx, progress) {
            // 用户（实体类）
            if (progress > 0) {
                drawClassBox(ctx, 50, 100, '用户', '#e3f2fd', '#1976d2', Math.min(progress * 4, 1));
                if (progress > 0.2) {
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillStyle = '#666';
                    ctx.textAlign = 'center';
                    ctx.fillText('实体类', 100, 180);
                }
            }

            // 通信协议（边界类）
            if (progress > 0.3) {
                drawClassBox(ctx, 250, 50, '通信协议', '#f3e5f5', '#7b1fa2', Math.min((progress - 0.3) * 4, 1));
                if (progress > 0.5) {
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillStyle = '#666';
                    ctx.textAlign = 'center';
                    ctx.fillText('边界类', 300, 130);
                }
            }

            // 身份验证（控制类）
            if (progress > 0.5) {
                drawClassBox(ctx, 450, 100, '身份验证', '#e8f5e8', '#388e3c', Math.min((progress - 0.5) * 4, 1));
                if (progress > 0.7) {
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillStyle = '#666';
                    ctx.textAlign = 'center';
                    ctx.fillText('控制类', 500, 180);
                }
            }

            // 查询余额（控制类）
            if (progress > 0.7) {
                drawClassBox(ctx, 650, 100, '查询余额', '#e8f5e8', '#388e3c', Math.min((progress - 0.7) * 4, 1));
                if (progress > 0.9) {
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillStyle = '#666';
                    ctx.textAlign = 'center';
                    ctx.fillText('控制类', 700, 180);
                }
            }

            // 绘制交互流程
            if (progress > 0.9) {
                drawSystemInteractions(ctx, progress - 0.9);
            }
        }

        function drawSystemInteractions(ctx, progress) {
            if (progress <= 0) return;

            ctx.save();
            ctx.globalAlpha = Math.min(progress * 5, 1);

            // 交互箭头
            ctx.strokeStyle = '#ff5722';
            ctx.lineWidth = 2;

            // 用户到通信协议
            drawArrow(ctx, 150, 130, 250, 80);

            // 通信协议到身份验证
            drawArrow(ctx, 350, 80, 450, 130);

            // 身份验证到查询余额
            drawArrow(ctx, 550, 130, 650, 130);

            // 标注
            ctx.font = '10px Microsoft YaHei';
            ctx.fillStyle = '#ff5722';
            ctx.textAlign = 'center';
            ctx.fillText('请求', 200, 100);
            ctx.fillText('验证', 400, 100);
            ctx.fillText('查询', 600, 120);

            ctx.restore();
        }

        function stepByStep() {
            const canvas = document.getElementById('exampleCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('题目分析步骤', canvas.width/2, 30);

            // 分析步骤
            const analysis = [
                { text: '1. 身份验证 - 动宾结构，控制验证流程 → 控制类', color: '#388e3c' },
                { text: '2. 用户 - 名词，业务实体，需要持久化 → 实体类', color: '#1976d2' },
                { text: '3. 通信协议 - 系统与外部交互接口 → 边界类', color: '#7b1fa2' },
                { text: '4. 查询余额 - 动宾结构，控制查询流程 → 控制类', color: '#388e3c' }
            ];

            analysis.forEach((item, index) => {
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = item.color;
                ctx.textAlign = 'left';
                ctx.fillText(item.text, 50, 100 + index * 40);
            });

            // 总结
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('答案：边界类 - 通信协议，实体类 - 用户', 50, 300);
        }

        function resetAll() {
            // 重置所有画布
            const canvases = ['overviewCanvas', 'entityCanvas', 'boundaryCanvas', 'controlCanvas', 'exampleCanvas'];
            canvases.forEach(id => {
                const canvas = document.getElementById(id);
                if (canvas) {
                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                }
            });

            if (currentAnimation) {
                clearInterval(currentAnimation);
                currentAnimation = null;
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            // 初始化概述动画
            setTimeout(animateOverview, 500);
        });
    </script>
</body>
</html>
