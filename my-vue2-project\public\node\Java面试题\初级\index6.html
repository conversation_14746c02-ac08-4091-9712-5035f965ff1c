<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java IO 流与并发模型</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }

        header {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 40px 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        header h1 {
            margin: 0;
            font-size: 2.5em;
        }

        header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        main {
            max-width: 1000px;
            margin: 20px auto;
            padding: 0 20px;
        }

        section {
            background-color: #ffffff;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            border-left: 5px solid #3498db;
        }

        section h2, section h3 {
            color: #2980b9;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
            margin-top: 0;
        }

        section ul {
            list-style-type: disc;
            margin-left: 25px;
            margin-bottom: 20px;
        }

        section ul li {
            margin-bottom: 8px;
        }

        .content-explanation p, .content-explanation ul {
            margin-bottom: 15px;
            font-size: 1.05em;
        }

        .canvas-container {
            text-align: center;
            margin-top: 25px;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 6px;
            box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.05);
            position: relative; /* For controls positioning */
        }

        canvas {
            display: block;
            margin: 0 auto;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #2980b9;
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .controls {
            margin-top: 15px;
        }

        #filesStatus {
            color: #e74c3c;
        }

        footer {
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            background-color: #2c3e50;
            color: #ecf0f1;
            font-size: 0.9em;
            border-top: 2px solid #34495e;
        }
    </style>
</head>
<body>
    <header>
        <h1>Java IO 流与并发模型详解</h1>
        <p>专为零基础设计，通过动画与交互理解复杂概念</p>
    </header>

    <main>
        <section id="io-streams">
            <h2>30、IO流</h2>
            <h3>1、Java 中 IO 流分为几种?</h3>
            <div class="content-explanation">
                <p>Java IO 流是处理输入输出的重要部分。根据不同的分类标准，它们可以被划分为不同的类型。理解这些分类有助于你更好地掌握IO流的体系结构。</p>
                <ul>
                    <li><b>按流向分：</b>
                        <ul>
                            <li><b>输入流 (InputStream/Reader):</b> 用于从数据源（如文件、网络、内存）读取数据到程序中。</li>
                            <li><b>输出流 (OutputStream/Writer):</b> 用于将程序中的数据写入到目标（如文件、网络、内存）。</li>
                        </ul>
                    </li>
                    <li><b>按操作单元划分：</b>
                        <ul>
                            <li><b>字节流 (InputStream/OutputStream):</b> 处理字节数据（8位），适用于所有类型的数据（文本、图片、音频、视频等）。</li>
                            <li><b>字符流 (Reader/Writer):</b> 处理字符数据（16位Unicode字符），专门用于处理文本数据，可以避免乱码问题。</li>
                        </ul>
                    </li>
                    <li><b>按流的角色划分：</b>
                        <ul>
                            <li><b>节点流 (Node Stream):</b> 直接与数据源（文件、内存、网络连接）相连，负责实际的数据读写。例如 `FileInputStream`。</li>
                            <li><b>处理流 (Processing Stream / Wrapper Stream):</b> 包装在节点流之上，提供额外的功能，如缓冲、转换、对象序列化等。例如 `BufferedReader`。</li>
                        </ul>
                    </li>
                </ul>
                <p>Java IO 流的40多个类都从 `InputStream`、`Reader`、`OutputStream`、`Writer` 这四个抽象基类派生而来，形成了一个有规律的体系。</p>
                <div class="canvas-container">
                    <canvas id="ioStreamCanvas" width="800" height="450"></canvas>
                    <button onclick="startIoStreamAnimation()">开始演示</button>
                </div>
            </div>
        </section>

        <section id="bio-nio-aio">
            <h3>2、BIO,NIO,AIO 有什么区别?</h3>
            <div class="content-explanation">
                <p>这三种模型是Java中处理I/O操作的不同方式，主要区别在于它们如何处理数据读写以及如何应对并发连接。理解它们能帮助你选择适合特定场景的I/O模型。</p>
                <ul>
                    <li><b>BIO (Blocking I/O - 同步阻塞I/O):</b>
                        <p>传统I/O模型。每个客户端连接都需要一个独立的线程来处理。当一个线程进行I/O操作（如读取数据）时，它会一直等待直到数据准备好或操作完成，期间该线程不能做其他事情。模式简单，但在高并发场景下效率低，因为会创建大量线程，导致系统资源消耗大。</p>
                    </li>
                    <li><b>NIO (Non-blocking I/O - 同步非阻塞I/O):</b>
                        <p>在Java 1.4中引入。主要特点是"非阻塞"和"多路复用"。它引入了<b>通道(Channel)</b>、<b>缓冲区(Buffer)</b>和<b>选择器(Selector)</b>的概念。一个线程可以通过选择器管理多个通道，当某个通道有数据准备好时，选择器会通知该线程进行处理，线程无需等待，从而提高了并发能力。</p>
                    </li>
                    <li><b>AIO (Asynchronous I/O - 异步非阻塞I/O):</b>
                        <p>也称NIO 2，在Java 7中引入。它实现了真正的异步I/O。应用发起I/O操作后会立即返回，无需等待。当I/O操作完成后，操作系统会通知应用，并通过回调函数或 Future 对象来获取结果。AIO特别适用于连接数多且连接时间长的应用。</p>
                    </li>
                </ul>
                <div class="canvas-container">
                    <canvas id="ioModelCanvas" width="900" height="550"></canvas>
                    <div class="controls">
                        <button onclick="startIoModelAnimation('bio')">BIO 演示</button>
                        <button onclick="startIoModelAnimation('nio')">NIO 演示</button>
                        <button onclick="startIoModelAnimation('aio')">AIO 演示</button>
                    </div>
                </div>
            </div>
        </section>

        <section id="files-methods">
            <h3>31、Files的常用方法都有哪些？</h3>
            <div class="content-explanation">
                <p>`java.nio.file.Files` 是Java 7中引入的一个实用工具类，它提供了大量静态方法来方便地操作文件和目录。这些方法比旧的 `java.io.File` 更加强大和易用，特别是在处理文件路径、创建、删除、复制、移动等方面。</p>
                <p>下面是一些常用的 `Files` 方法及其说明，你可以点击按钮进行模拟操作：</p>
                <ul>
                    <li><code>Files.exists(Path path)</code>: 检测文件或路径是否存在。</li>
                    <li><code>Files.createFile(Path path)</code>: 创建一个空文件。如果文件已存在，会抛出异常。</li>
                    <li><code>Files.createDirectory(Path dir)</code>: 创建一个新目录。如果目录已存在，会抛出异常。</li>
                    <li><code>Files.delete(Path path)</code>: 删除一个文件或空目录。</li>
                    <li><code>Files.copy(Path source, Path target)</code>: 复制文件。</li>
                    <li><code>Files.move(Path source, Path target)</code>: 移动或重命名文件/目录。</li>
                    <li><code>Files.size(Path path)</code>: 返回文件的大小（字节数）。</li>
                    <li><code>Files.readAllBytes(Path path)</code>: 读取文件的所有字节。</li>
                    <li><code>Files.write(Path path, byte[] bytes)</code>: 将字节写入文件，如果文件不存在则创建。</li>
                </ul>
                <div class="canvas-container">
                    <canvas id="filesCanvas" width="800" height="500"></canvas>
                    <div class="controls">
                        <button onclick="performFilesAction('exists')">exists()</button>
                        <button onclick="performFilesAction('createFile')">createFile()</button>
                        <button onclick="performFilesAction('createDirectory')">createDirectory()</button>
                        <button onclick="performFilesAction('delete')">delete()</button>
                        <button onclick="performFilesAction('copy')">copy()</button>
                        <button onclick="performFilesAction('move')">move()</button>
                        <button onclick="performFilesAction('size')">size()</button>
                        <button onclick="performFilesAction('read')">read()</button>
                        <button onclick="performFilesAction('write')">write()</button>
                        <button onclick="performFilesAction('reset')">重置文件系统</button>
                    </div>
                    <p id="filesStatus" style="font-weight: bold; margin-top: 10px;"></p>
                </div>
            </div>
        </section>

        <section id="hashmap-string-key">
            <h3>32、在使用 HashMap 的时候，用 String 做 key 有什么好处？</h3>
            <div class="content-explanation">
                <p>在Java中，`HashMap` 是一种非常常用的数据结构，它通过键（Key）的哈希码（Hash Code）来确定值（Value）的存储位置，从而实现快速存取。当我们将 `String` 对象作为 `HashMap` 的键时，它具有一个显著的性能优势。</p>
                <ul>
                    <li><b>字符串不可变性：</b>
                        <p>在Java中，`String` 类是不可变的（Immutable）。这意味着一旦一个 `String` 对象被创建，它的内容就不能被改变。例如，当你对一个字符串进行修改操作（如 `concat()`、`substring()`），实际上是创建了一个新的 `String` 对象，而不是修改了原对象。</p>
                    </li>
                    <li><b>哈希码的缓存机制：</b>
                        <p>由于 `String` 的不可变性，它的 `hashCode()` 方法在第一次被调用时，会计算并缓存其哈希码。这意味着，只要字符串内容不变，后续对 `hashCode()` 的调用都会直接返回这个缓存的值，而不需要重新计算。这大大提高了 `HashMap` 在查找、插入和删除操作时，计算键哈希码的效率。</p>
                    </li>
                </ul>
                <p>相比之下，如果使用可变对象（如 `StringBuilder` 或自定义的可变类）作为 `HashMap` 的键，一旦对象的内部状态发生改变，其哈希码也可能改变。如果哈希码改变，`HashMap` 可能无法正确找到该键对应的值，甚至导致数据丢失。因此，将不可变对象（尤其是 `String`）作为 `HashMap` 的键是一种非常推荐的做法。</p>
                <div class="canvas-container">
                    <canvas id="hashMapCanvas" width="800" height="400"></canvas>
                    <button onclick="startHashMapAnimation()">开始演示</button>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2024 Java IO 流学习 - 专为零基础设计</p>
    </footer>

    <script>
        // --- 通用绘图函数 ---
        function drawText(ctx, text, x, y, color = '#333', font = '16px Arial', align = 'center') {
            ctx.fillStyle = color;
            ctx.font = font;
            ctx.textAlign = align;
            ctx.fillText(text, x, y);
        }

        function drawRect(ctx, x, y, width, height, color = '#3498db', borderColor = '#2980b9', borderWidth = 2) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = borderColor;
            ctx.lineWidth = borderWidth;
            ctx.strokeRect(x, y, width, height);
        }

        function drawArrow(ctx, fromX, fromY, toX, toY, color = '#e74c3c', headlen = 10) {
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fill();
        }

        // --- 1. IO 流分类动画 ---
        const ioStreamCanvas = document.getElementById('ioStreamCanvas');
        const ioStreamCtx = ioStreamCanvas.getContext('2d');
        let ioStreamAnimationId = null;

        function clearIoStreamCanvas() {
            ioStreamCtx.clearRect(0, 0, ioStreamCanvas.width, ioStreamCanvas.height);
        }

        function drawIoStreamBaseClasses() {
            drawRect(ioStreamCtx, 50, 50, 120, 40, '#f39c12', '#d35400');
            drawText(ioStreamCtx, 'InputStream', 110, 75, 'white');
            drawRect(ioStreamCtx, 200, 50, 120, 40, '#f39c12', '#d35400');
            drawText(ioStreamCtx, 'OutputStream', 260, 75, 'white');

            drawRect(ioStreamCtx, 50, 100, 120, 40, '#e67e22', '#d35400');
            drawText(ioStreamCtx, 'Reader', 110, 125, 'white');
            drawRect(ioStreamCtx, 200, 100, 120, 40, '#e67e22', '#d35400');
            drawText(ioStreamCtx, 'Writer', 260, 125, 'white');

            drawText(ioStreamCtx, '四大基类', 180, 25, '#2c3e50', '20px Arial', 'center');
        }

        function drawIoStreamCategories(progress) {
            // Flow direction
            drawRect(ioStreamCtx, 400, 30, 150, 60, '#3498db', '#2980b9');
            drawText(ioStreamCtx, '流向', 475, 65, 'white');
            drawText(ioStreamCtx, '输入流', 475, 110, '#2c3e50', '14px Arial');
            drawText(ioStreamCtx, '输出流', 475, 130, '#2c3e50', '14px Arial');

            // Operation unit
            drawRect(ioStreamCtx, 600, 30, 150, 60, '#2ecc71', '#27ae60');
            drawText(ioStreamCtx, '操作单元', 675, 65, 'white');
            drawText(ioStreamCtx, '字节流', 675, 110, '#2c3e50', '14px Arial');
            drawText(ioStreamCtx, '字符流', 675, 130, '#2c3e50', '14px Arial');

            // Role
            drawRect(ioStreamCtx, 400, 200, 150, 60, '#9b59b6', '#8e44ad');
            drawText(ioStreamCtx, '流角色', 475, 235, 'white');
            drawText(ioStreamCtx, '节点流', 475, 280, '#2c3e50', '14px Arial');
            drawText(ioStreamCtx, '处理流', 475, 300, '#2c3e50', '14px Arial');

            // Examples
            if (progress > 0) {
                const exampleY = 200 + (1 - progress) * 200; // Animate from bottom
                drawRect(ioStreamCtx, 50, exampleY, 120, 30, '#f1c40f', '#f39c12');
                drawText(ioStreamCtx, 'FileInputStream', 110, exampleY + 20, '#333');
                drawArrow(ioStreamCtx, 170, exampleY + 15, 420 * progress + 170 * (1 - progress), exampleY + 15, '#e74c3c');
                drawText(ioStreamCtx, '输入流', 490, exampleY + 10, '#e74c3c', '12px Arial');
                drawText(ioStreamCtx, '字节流', 690, exampleY + 10, '#e74c3c', '12px Arial');
                drawText(ioStreamCtx, '节点流', 490, exampleY + 200, '#e74c3c', '12px Arial');


                drawRect(ioStreamCtx, 200, exampleY + 50, 120, 30, '#1abc9c', '#16a085');
                drawText(ioStreamCtx, 'BufferedReader', 260, exampleY + 70, '#333');
                drawArrow(ioStreamCtx, 320, exampleY + 65, 420 * progress + 320 * (1 - progress), exampleY + 65, '#e74c3c');
                drawText(ioStreamCtx, '输入流', 490, exampleY + 50, '#e74c3c', '12px Arial');
                drawText(ioStreamCtx, '字符流', 690, exampleY + 50, '#e74c3c', '12px Arial');
                drawText(ioStreamCtx, '处理流', 490, exampleY + 250, '#e74c3c', '12px Arial');

            }
        }

        let ioStreamAnimationStartTime = null;
        const ioStreamAnimationDuration = 3000;

        function animateIoStream(currentTime) {
            if (!ioStreamAnimationStartTime) ioStreamAnimationStartTime = currentTime;
            const elapsed = currentTime - ioStreamAnimationStartTime;
            const progress = Math.min(elapsed / ioStreamAnimationDuration, 1);

            clearIoStreamCanvas();
            drawIoStreamBaseClasses();
            drawIoStreamCategories(progress);

            if (progress < 1) {
                ioStreamAnimationId = requestAnimationFrame(animateIoStream);
            } else {
                ioStreamAnimationId = null;
            }
        }

        function startIoStreamAnimation() {
            if (ioStreamAnimationId) {
                cancelAnimationFrame(ioStreamAnimationId);
            }
            ioStreamAnimationStartTime = null;
            ioStreamAnimationId = requestAnimationFrame(animateIoStream);
        }

        // Initial draw
        clearIoStreamCanvas();
        drawIoStreamBaseClasses();
        drawIoStreamCategories(0); // Draw categories without examples

        // --- 2. BIO, NIO, AIO 动画 ---
        const ioModelCanvas = document.getElementById('ioModelCanvas');
        const ioModelCtx = ioModelCanvas.getContext('2d');
        let ioModelAnimationId = null;
        let currentIoModel = '';

        const clientCount = 5;
        const clientRadius = 15;
        const clientColors = ['#e74c3c', '#e67e22', '#f1c40f', '#2ecc71', '#3498db'];

        function clearIoModelCanvas() {
            ioModelCtx.clearRect(0, 0, ioModelCanvas.width, ioModelCanvas.height);
        }

        function drawClient(ctx, x, y, color, text) {
            ctx.beginPath();
            ctx.arc(x, y, clientRadius, 0, Math.PI * 2);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.stroke();
            drawText(ctx, text, x, y + 5, 'white', '12px Arial');
        }

        function drawBIO(progress) {
            drawText(ioModelCtx, 'BIO (Blocking I/O)', ioModelCanvas.width / 2, 30, '#e74c3c', '24px Arial');
            drawText(ioModelCtx, '同步阻塞 I/O', ioModelCanvas.width / 2, 60, '#333', '18px Arial');

            const serverX = ioModelCanvas.width / 2;
            const serverY = 150;
            drawRect(ioModelCtx, serverX - 60, serverY - 30, 120, 60, '#2c3e50', '#34495e');
            drawText(ioModelCtx, '服务器', serverX, serverY, 'white');

            const threadWidth = 100;
            const threadHeight = 40;
            const threadStartX = serverX - (threadWidth * 2 + 20) / 2;
            const threadY = 250;

            for (let i = 0; i < clientCount; i++) {
                const clientX = 100 + i * 150;
                const clientY = 450;
                drawClient(ioModelCtx, clientX, clientY, clientColors[i], `C${i + 1}`);
                drawText(ioModelCtx, `客户端 ${i + 1}`, clientX, clientY + clientRadius + 20, '#333', '14px Arial');

                const connectProgress = Math.min(progress * 5 - i, 1);
                if (connectProgress > 0) {
                    const currentY = clientY - (clientY - (threadY + threadHeight)) * connectProgress;
                    const currentX = clientX + (threadStartX + (i * (threadWidth + 10)) + threadWidth / 2 - clientX) * connectProgress;
                    drawArrow(ioModelCtx, clientX, clientY - clientRadius, currentX, currentY, '#3498db');
                }

                if (i < 3) { // Max 3 threads
                    drawRect(ioModelCtx, threadStartX + i * (threadWidth + 10), threadY, threadWidth, threadHeight, '#f1c40f', '#f39c12');
                    drawText(ioModelCtx, `线程 ${i + 1}`, threadStartX + i * (threadWidth + 10) + threadWidth / 2, threadY + threadHeight / 2 + 5, '#333');
                } else {
                    drawText(ioModelCtx, '等待...', threadStartX + i * (threadWidth + 10) + threadWidth / 2, threadY + threadHeight / 2 + 5, '#e74c3c');
                }
            }
        }

        function drawNIO(progress) {
            drawText(ioModelCtx, 'NIO (Non-blocking I/O)', ioModelCanvas.width / 2, 30, '#2ecc71', '24px Arial');
            drawText(ioModelCtx, '同步非阻塞 I/O (多路复用)', ioModelCanvas.width / 2, 60, '#333', '18px Arial');

            const selectorX = ioModelCanvas.width / 2;
            const selectorY = 200;
            drawRect(ioModelCtx, selectorX - 80, selectorY - 40, 160, 80, '#27ae60', '#16a085');
            drawText(ioModelCtx, 'Selector (选择器)', selectorX, selectorY - 10, 'white', '16px Arial');
            drawText(ioModelCtx, 'I/O 线程', selectorX, selectorY + 20, 'white', '14px Arial');

            const channelStartX = selectorX - 180;
            const channelWidth = 80;
            const channelHeight = 30;

            for (let i = 0; i < clientCount; i++) {
                const clientX = 100 + i * 150;
                const clientY = 450;
                drawClient(ioModelCtx, clientX, clientY, clientColors[i], `C${i + 1}`);
                drawText(ioModelCtx, `客户端 ${i + 1}`, clientX, clientY + clientRadius + 20, '#333', '14px Arial');

                const connectProgress = Math.min(progress * 5 - i, 1);
                if (connectProgress > 0) {
                    const currentY = clientY - (clientY - (selectorY + 40)) * connectProgress;
                    const currentX = clientX + (selectorX - clientX) * connectProgress;
                    drawArrow(ioModelCtx, clientX, clientY - clientRadius, currentX, currentY, '#3498db');
                }

                // Draw channels
                const channelY = selectorY - 80;
                drawRect(ioModelCtx, channelStartX + i * 100, channelY, channelWidth, channelHeight, '#f1c40f', '#f39c12');
                drawText(ioModelCtx, `通道${i + 1}`, channelStartX + i * 100 + channelWidth / 2, channelY + channelHeight / 2 + 5, '#333', '12px Arial');
                drawArrow(ioModelCtx, channelStartX + i * 100 + channelWidth / 2, channelY + channelHeight, selectorX, selectorY - 30, '#333', 7);
            }
        }

        function drawAIO(progress) {
            drawText(ioModelCtx, 'AIO (Asynchronous I/O)', ioModelCanvas.width / 2, 30, '#9b59b6', '24px Arial');
            drawText(ioModelCtx, '异步非阻塞 I/O', ioModelCanvas.width / 2, 60, '#333', '18px Arial');

            const ioEngineX = ioModelCanvas.width / 2;
            const ioEngineY = 200;
            drawRect(ioModelCtx, ioEngineX - 100, ioEngineY - 50, 200, 100, '#8e44ad', '#9b59b6');
            drawText(ioModelCtx, '操作系统 / 异步I/O引擎', ioEngineX, ioEngineY - 15, 'white', '16px Arial');
            drawText(ioModelCtx, '(事件和回调机制)', ioEngineX, ioEngineY + 15, 'white', '14px Arial');

            for (let i = 0; i < clientCount; i++) {
                const clientX = 100 + i * 150;
                const clientY = 450;
                drawClient(ioModelCtx, clientX, clientY, clientColors[i], `C${i + 1}`);
                drawText(ioModelCtx, `客户端 ${i + 1}`, clientX, clientY + clientRadius + 20, '#333', '14px Arial');

                const reqProgress = Math.min(progress * 5 - i, 1);
                if (reqProgress > 0) {
                    const currentY = clientY - (clientY - (ioEngineY + 50)) * reqProgress;
                    const currentX = clientX + (ioEngineX - clientX) * reqProgress;
                    drawArrow(ioModelCtx, clientX, clientY - clientRadius, currentX, currentY, '#e74c3c');
                    drawText(ioModelCtx, '发起请求', (clientX + currentX) / 2 + 10, (clientY + currentY) / 2 - 10, '#e74c3c', '12px Arial');
                }

                const callbackProgress = Math.min(Math.max(0, progress * 5 - i - 2), 1); // Callback starts later
                if (callbackProgress > 0) {
                    const currentY = ioEngineY + 50 - (ioEngineY + 50 - (clientY - clientRadius)) * callbackProgress;
                    const currentX = ioEngineX + (clientX - ioEngineX) * callbackProgress;
                    drawArrow(ioModelCtx, ioEngineX, ioEngineY + 50, currentX, currentY, '#2ecc71');
                    drawText(ioModelCtx, '回调/完成', (ioEngineX + currentX) / 2 + 10, (ioEngineY + 50 + currentY) / 2 - 10, '#2ecc71', '12px Arial');
                }
            }
        }

        let ioModelAnimationStartTime = null;
        const ioModelAnimationDuration = 4000;

        function animateIoModel(currentTime) {
            if (!ioModelAnimationStartTime) ioModelAnimationStartTime = currentTime;
            const elapsed = currentTime - ioModelAnimationStartTime;
            const progress = Math.min(elapsed / ioModelAnimationDuration, 1);

            clearIoModelCanvas();
            if (currentIoModel === 'bio') {
                drawBIO(progress);
            } else if (currentIoModel === 'nio') {
                drawNIO(progress);
            } else if (currentIoModel === 'aio') {
                drawAIO(progress);
            }

            if (progress < 1) {
                ioModelAnimationId = requestAnimationFrame(animateIoModel);
            } else {
                ioModelAnimationId = null;
            }
        }

        function startIoModelAnimation(model) {
            if (ioModelAnimationId) {
                cancelAnimationFrame(ioModelAnimationId);
            }
            currentIoModel = model;
            ioModelAnimationStartTime = null;
            ioModelAnimationId = requestAnimationFrame(animateIoModel);
        }

        // Initial draw
        clearIoModelCanvas();
        drawText(ioModelCtx, '点击上方按钮选择I/O模型演示', ioModelCanvas.width / 2, ioModelCanvas.height / 2, '#7f8c8d', '20px Arial');

        // --- 3. Files 方法动画 ---
        const filesCanvas = document.getElementById('filesCanvas');
        const filesCtx = filesCanvas.getContext('2d');
        const filesStatus = document.getElementById('filesStatus');

        let fileSystem = {}; // {'root/dir1': {type: 'directory', files: {}}, 'root/file1.txt': {type: 'file', content: ''}}
        let selectedPath = ''; // For copy/move source

        function clearFilesCanvas() {
            filesCtx.clearRect(0, 0, filesCanvas.width, filesCanvas.height);
        }

        function drawFileSystem() {
            clearFilesCanvas();
            drawText(filesCtx, '虚拟文件系统', filesCanvas.width / 2, 30, '#2c3e50', '24px Arial');

            const rootX = filesCanvas.width / 2;
            const rootY = 80;
            drawRect(filesCtx, rootX - 50, rootY, 100, 40, '#f1c40f', '#f39c12');
            drawText(filesCtx, 'root', rootX, rootY + 25, '#333');

            let currentY = rootY + 70;
            let currentX = rootX - 150;

            const drawEntry = (name, type, content = '', x, y, isSelected = false) => {
                const color = type === 'directory' ? '#3498db' : '#2ecc71';
                const borderColor = type === 'directory' ? '#2980b9' : '#27ae60';
                drawRect(filesCtx, x, y, 120, 40, color, isSelected ? '#e74c3c' : borderColor, isSelected ? 4 : 2);
                drawText(filesCtx, name, x + 60, y + 25, 'white');
                if (type === 'file' && content) {
                    drawText(filesCtx, `(${content.length} 字节)`, x + 60, y + 55, '#7f8c8d', '12px Arial');
                }
            };

            let i = 0;
            for (const path in fileSystem) {
                const parts = path.split('/');
                const name = parts[parts.length - 1];
                const entry = fileSystem[path];

                const x = currentX + (i % 4) * 160;
                const y = currentY + Math.floor(i / 4) * 90;

                drawEntry(name, entry.type, entry.content, x, y, path === selectedPath);
                i++;
            }
        }

        function updateFilesStatus(message, isError = false) {
            filesStatus.textContent = message;
            filesStatus.style.color = isError ? '#e74c3c' : '#2980b9';
        }

        function performFilesAction(action) {
            let success = false;
            let message = '';
            let animatePath = ''; // Path to animate for copy/move

            try {
                if (action === 'reset') {
                    fileSystem = {};
                    selectedPath = '';
                    updateFilesStatus('文件系统已重置。');
                    success = true;
                    return; // No further animation for reset
                }

                // For actions that need a target
                const targetName = prompt(`请输入要操作的${action === 'createFile' ? '文件' : action === 'createDirectory' ? '目录' : ''}名称 (例如: myFile.txt, myDir):`);
                if (!targetName) {
                    updateFilesStatus('操作取消。', true);
                    return;
                }
                const targetPath = `root/${targetName}`;

                switch (action) {
                    case 'exists':
                        success = !!fileSystem[targetPath];
                        message = `路径 '${targetPath}' ${success ? '存在' : '不存在'}。`;
                        break;
                    case 'createFile':
                        if (fileSystem[targetPath]) {
                            message = `文件 '${targetPath}' 已存在。`;
                            success = false;
                        } else {
                            fileSystem[targetPath] = { type: 'file', content: '' };
                            message = `文件 '${targetPath}' 创建成功。`;
                            success = true;
                        }
                        break;
                    case 'createDirectory':
                        if (fileSystem[targetPath]) {
                            message = `目录 '${targetPath}' 已存在。`;
                            success = false;
                        } else {
                            fileSystem[targetPath] = { type: 'directory', files: {} };
                            message = `目录 '${targetPath}' 创建成功。`;
                            success = true;
                        }
                        break;
                    case 'delete':
                        if (fileSystem[targetPath]) {
                            delete fileSystem[targetPath];
                            message = `路径 '${targetPath}' 删除成功。`;
                            success = true;
                        } else {
                            message = `路径 '${targetPath}' 不存在。`;
                            success = false;
                        }
                        break;
                    case 'copy':
                        if (!selectedPath) {
                            selectedPath = targetPath;
                            updateFilesStatus(`请再次点击 copy() 选择目标文件。源: ${selectedPath}`);
                            return;
                        } else {
                            if (!fileSystem[selectedPath]) {
                                message = `源文件 '${selectedPath}' 不存在。`;
                                success = false;
                            } else if (fileSystem[targetPath]) {
                                message = `目标文件 '${targetPath}' 已存在。`;
                                success = false;
                            } else {
                                fileSystem[targetPath] = { ...fileSystem[selectedPath] }; // Shallow copy
                                message = `文件从 '${selectedPath}' 复制到 '${targetPath}' 成功。`;
                                success = true;
                                animatePath = targetPath;
                            }
                            selectedPath = ''; // Reset selected path
                        }
                        break;
                    case 'move':
                        if (!selectedPath) {
                            selectedPath = targetPath;
                            updateFilesStatus(`请再次点击 move() 选择目标位置/名称。源: ${selectedPath}`);
                            return;
                        } else {
                            if (!fileSystem[selectedPath]) {
                                message = `源文件 '${selectedPath}' 不存在。`;
                                success = false;
                            } else {
                                fileSystem[targetPath] = { ...fileSystem[selectedPath] };
                                delete fileSystem[selectedPath];
                                message = `文件从 '${selectedPath}' 移动到 '${targetPath}' 成功。`;
                                success = true;
                                animatePath = targetPath; // Animate target as new location
                            }
                            selectedPath = ''; // Reset selected path
                        }
                        break;
                    case 'size':
                        if (fileSystem[targetPath] && fileSystem[targetPath].type === 'file') {
                            const size = (fileSystem[targetPath].content || '').length;
                            message = `文件 '${targetPath}' 大小为 ${size} 字节。`;
                            success = true;
                        } else {
                            message = `路径 '${targetPath}' 不是文件或不存在。`;
                            success = false;
                        }
                        break;
                    case 'read':
                        if (fileSystem[targetPath] && fileSystem[targetPath].type === 'file') {
                            const content = fileSystem[targetPath].content || '无内容';
                            message = `读取文件 '${targetPath}' 内容: '${content}'。`;
                            success = true;
                        } else {
                            message = `路径 '${targetPath}' 不是文件或不存在。`;
                            success = false;
                        }
                        break;
                    case 'write':
                        if (fileSystem[targetPath] && fileSystem[targetPath].type === 'file') {
                            const writeContent = prompt('请输入要写入的内容 (例如: Hello World):');
                            if (writeContent !== null) {
                                fileSystem[targetPath].content = writeContent;
                                message = `内容 '${writeContent}' 写入文件 '${targetPath}' 成功。`;
                                success = true;
                            } else {
                                message = '写入操作取消。';
                                success = false;
                            }
                        } else {
                            message = `路径 '${targetPath}' 不是文件或不存在。请先创建文件。`;
                            success = false;
                        }
                        break;
                    default:
                        message = '未知操作。';
                        success = false;
                }
            } catch (e) {
                message = `操作失败: ${e.message}`;
                success = false;
            }

            updateFilesStatus(message, !success);
            drawFileSystem(); // Redraw after changes

            // Basic animation for new/moved/copied files
            if (success && (action === 'createFile' || action === 'createDirectory' || animatePath)) {
                // Flash the created/moved/copied item
                const path = animatePath || targetPath;
                if (fileSystem[path]) {
                    const originalColor = fileSystem[path].type === 'directory' ? '#3498db' : '#2ecc71';
                    const flashColor = '#f1c40f';
                    const flashDuration = 300;
                    const startTime = performance.now();

                    const animateFlash = (currentTime) => {
                        const elapsed = currentTime - startTime;
                        if (elapsed < flashDuration * 2) {
                            const isOddFlash = Math.floor(elapsed / flashDuration) % 2 === 1;
                            const newColor = isOddFlash ? flashColor : originalColor;
                            filesCtx.clearRect(0, 0, filesCanvas.width, filesCanvas.height); // Clear and redraw all
                            drawFileSystem(); // Redraw everything
                            // Find the position of the animated item and redraw it with flash color
                            let idx = 0;
                            for (const p in fileSystem) {
                                if (p === path) {
                                    const rootX = filesCanvas.width / 2;
                                    const currentY = 80 + 70;
                                    const currentX = rootX - 150;
                                    const x = currentX + (idx % 4) * 160;
                                    const y = currentY + Math.floor(idx / 4) * 90;
                                    drawRect(filesCtx, x, y, 120, 40, newColor, '#e74c3c', 4);
                                    drawText(filesCtx, path.split('/').pop(), x + 60, y + 25, 'white');
                                    break;
                                }
                                idx++;
                            }
                            requestAnimationFrame(animateFlash);
                        } else {
                            drawFileSystem(); // Final redraw to ensure correct colors
                        }
                    };
                    requestAnimationFrame(animateFlash);
                }
            }
        }

        // Initial draw for Files
        drawFileSystem();
        updateFilesStatus('文件系统已初始化。点击按钮进行操作。');

        // --- 4. HashMap String Key 动画 ---
        const hashMapCanvas = document.getElementById('hashMapCanvas');
        const hashMapCtx = hashMapCanvas.getContext('2d');
        let hashMapAnimationId = null;

        function clearHashMapCanvas() {
            hashMapCtx.clearRect(0, 0, hashMapCanvas.width, hashMapCanvas.height);
        }

        function drawHashMapComponents(progress, highlightStep = 0) {
            // String Object
            const stringX = 100;
            const stringY = 100;
            drawRect(hashMapCtx, stringX, stringY, 150, 60, '#f39c12', '#d35400');
            drawText(hashMapCtx, 'String 对象', stringX + 75, stringY + 25, 'white', '16px Arial');
            drawText(hashMapCtx, '"Hello"', stringX + 75, stringY + 45, 'white', '14px Arial');

            // Hash Code Box
            const hashCodeX = stringX + 200;
            const hashCodeY = stringY + 20;
            drawRect(hashMapCtx, hashCodeX, hashCodeY, 100, 30, '#ecf0f1', '#bdc3c7');
            drawText(hashMapCtx, 'hashCode:', hashCodeX + 50, hashCodeY + 20, '#333', '14px Arial');
            let actualHashCode = '123456'; // Placeholder hash code
            if (highlightStep >= 2) {
                drawText(hashMapCtx, actualHashCode, hashCodeX + 50 + 40, hashCodeY + 20, '#e74c3c', '14px Arial');
            }

            // HashMap Structure (Array of Buckets)
            const mapX = 450;
            const mapY = 100;
            const bucketHeight = 40;
            const bucketWidth = 100;
            const numBuckets = 5;

            drawText(hashMapCtx, 'HashMap (内部数组)', mapX + bucketWidth * numBuckets / 2, mapY - 20, '#2c3e50', '18px Arial');

            for (let i = 0; i < numBuckets; i++) {
                drawRect(hashMapCtx, mapX + i * (bucketWidth + 5), mapY, bucketWidth, bucketHeight, '#3498db', '#2980b9');
                drawText(hashMapCtx, `Bucket ${i}`, mapX + i * (bucketWidth + 5) + bucketWidth / 2, mapY + bucketHeight / 2 + 5, 'white', '14px Arial');
                drawText(hashMapCtx, `链表/红黑树`, mapX + i * (bucketWidth + 5) + bucketWidth / 2, mapY + bucketHeight + 15, '#7f8c8d', '10px Arial');
            }

            // Explanations for each step
            let explanation = '';
            let arrowVisible = false;
            let bucketIndex = 0; // Simulate hash code mapping to bucket 2
            let hashArrowY = hashCodeY + 15;
            let bucketArrowX = mapX + bucketIndex * (bucketWidth + 5) + bucketWidth / 2;
            let bucketArrowY = mapY - 5;

            if (highlightStep === 1) {
                explanation = '1. 创建 String 对象。';
                drawRect(hashMapCtx, stringX, stringY, 150, 60, '#f1c40f', '#f39c12', 4); // Highlight String
            } else if (highlightStep === 2) {
                explanation = '2. 首次调用 hashCode()，计算并缓存哈希码。';
                arrowVisible = true;
                drawArrow(hashMapCtx, stringX + 150, stringY + 30, hashCodeX, hashCodeY + 15, '#e74c3c');
                drawRect(hashMapCtx, hashCodeX, hashCodeY, 100, 30, '#f1c40f', '#f39c12', 4); // Highlight hashCode
            } else if (highlightStep === 3) {
                explanation = '3. HashMap 使用缓存的哈希码快速定位到桶。';
                arrowVisible = true;
                drawArrow(hashMapCtx, hashCodeX + 50, hashArrowY, bucketArrowX, bucketArrowY, '#2ecc71');
                drawRect(hashMapCtx, mapX + bucketIndex * (bucketWidth + 5), mapY, bucketWidth, bucketHeight, '#f1c40f', '#f39c12', 4); // Highlight bucket
            } else if (highlightStep === 4) {
                explanation = '4. 后续访问直接使用缓存哈希码，效率高。';
                arrowVisible = true;
                drawArrow(hashMapCtx, hashCodeX + 50, hashArrowY, bucketArrowX, bucketArrowY, '#2ecc71');
                drawRect(hashMapCtx, stringX, stringY, 150, 60, '#f1c40f', '#f39c12', 2);
                drawRect(hashMapCtx, hashCodeX, hashCodeY, 100, 30, '#f1c40f', '#f39c12', 2);
                drawRect(hashMapCtx, mapX + bucketIndex * (bucketWidth + 5), mapY, bucketWidth, bucketHeight, '#f1c40f', '#f39c12', 4);
                drawText(hashMapCtx, '哈希码已缓存，无需重复计算！', hashCodeX + 50, hashCodeY + 45, '#3498db', '12px Arial');

                // Simulate mutable object comparison
                const mutableObjX = stringX;
                const mutableObjY = 250;
                drawRect(hashMapCtx, mutableObjX, mutableObjY, 150, 60, '#e74c3c', '#c0392b');
                drawText(hashMapCtx, '可变对象', mutableObjX + 75, mutableObjY + 25, 'white', '16px Arial');
                drawText(hashMapCtx, '(例如: StringBuilder)', mutableObjX + 75, mutableObjY + 45, 'white', '14px Arial');
                drawText(hashMapCtx, '状态改变可能导致 hashCode 重新计算', mutableObjX + 75, mutableObjY + 90, '#e74c3c', '14px Arial');
            }

            drawText(hashMapCtx, explanation, hashMapCanvas.width / 2, 350, '#333', '18px Arial', 'center');
        }

        let hashMapAnimationStartTime = null;
        let currentHashMapStep = 0;
        const hashMapAnimationDurations = [1500, 1500, 2000, 3000]; // Duration for each step

        function animateHashMap(currentTime) {
            if (!hashMapAnimationStartTime) hashMapAnimationStartTime = currentTime;
            const elapsed = currentTime - hashMapAnimationStartTime;

            let totalPreviousDuration = 0;
            for (let i = 0; i < currentHashMapStep; i++) {
                totalPreviousDuration += hashMapAnimationDurations[i];
            }

            const currentStepDuration = hashMapAnimationDurations[currentHashMapStep];
            const progressInStep = Math.min((elapsed - totalPreviousDuration) / currentStepDuration, 1);

            clearHashMapCanvas();
            drawHashMapComponents(progressInStep, currentHashMapStep + 1);

            if (currentHashMapStep < hashMapAnimationDurations.length - 1 && progressInStep >= 1) {
                currentHashMapStep++;
                hashMapAnimationStartTime = currentTime; // Reset start time for next step
            }

            if (currentHashMapStep < hashMapAnimationDurations.length) {
                hashMapAnimationId = requestAnimationFrame(animateHashMap);
            } else {
                hashMapAnimationId = null;
            }
        }

        function startHashMapAnimation() {
            if (hashMapAnimationId) {
                cancelAnimationFrame(hashMapAnimationId);
            }
            currentHashMapStep = 0;
            hashMapAnimationStartTime = null;
            hashMapAnimationId = requestAnimationFrame(animateHashMap);
        }

        // Initial draw for HashMap
        clearHashMapCanvas();
        drawHashMapComponents(0, 0);
        drawText(hashMapCtx, '点击按钮开始演示 String 作为 HashMap Key 的优势', hashMapCanvas.width / 2, hashMapCanvas.height / 2, '#7f8c8d', '20px Arial');

    </script>
</body>
</html> 