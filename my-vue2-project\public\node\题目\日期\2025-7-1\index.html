<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象分析模型 - 互动学习</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        :root {
            --primary-color: #007bff;
            --success-color: #28a745;
            --error-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --bg-color: #f0f2f5;
            --font-family: 'Noto Sans SC', sans-serif;
            --border-radius: 12px;
            --shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-color);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            color: var(--dark-color);
            padding: 20px;
            box-sizing: border-box;
        }

        .container {
            width: 100%;
            max-width: 800px;
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            padding: 30px 40px;
            overflow: hidden;
            transition: all 0.5s ease-in-out;
        }

        /* --- Quiz Section --- */
        #quiz-container {
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
        }
        
        #quiz-container.hidden {
            opacity: 0;
            transform: scale(0.9);
            position: absolute;
            pointer-events: none;
        }

        h1 {
            text-align: center;
            color: var(--dark-color);
            font-weight: 700;
            margin-bottom: 25px;
        }

        .question {
            font-size: 1.3em;
            font-weight: 500;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .question span {
            display: inline-block;
            width: 80px;
            height: 30px;
            border: 2px dashed var(--primary-color);
            border-radius: 8px;
            background-color: #e9f4ff;
            vertical-align: middle;
        }

        .options {
            display: grid;
            gap: 15px;
        }

        .option {
            background-color: var(--light-color);
            border: 2px solid #e0e0e0;
            border-radius: var(--border-radius);
            padding: 15px 20px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .option:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
            border-color: var(--primary-color);
        }
        
        .option-letter {
            font-weight: 700;
            color: var(--primary-color);
            margin-right: 15px;
            border: 2px solid var(--primary-color);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
        }

        /* Option States */
        .option.correct {
            background-color: #e9f7ef;
            border-color: var(--success-color);
            color: #1a5c34;
            animation: correct-pulse 0.6s ease;
        }
        .option.correct .option-letter {
            background-color: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .option.incorrect {
            background-color: #fbebed;
            border-color: var(--error-color);
            color: #8c2a32;
            animation: shake 0.5s ease;
        }
         .option.incorrect .option-letter {
            background-color: var(--error-color);
            border-color: var(--error-color);
            color: white;
        }
        
        .option.disabled {
            cursor: not-allowed;
            opacity: 0.7;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
        }
        
        @keyframes correct-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.03); }
            100% { transform: scale(1); }
        }

        /* --- Explanation Section --- */
        #explanation-container {
            opacity: 0;
            display: none;
            flex-direction: column;
            align-items: center;
            transition: opacity 0.5s ease-in;
        }
        
        #explanation-container.visible {
            display: flex;
            opacity: 1;
        }
        
        .explanation-title {
            font-size: 1.8em;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .explanation-intro {
            font-size: 1.1em;
            text-align: center;
            max-width: 600px;
            margin-bottom: 40px;
            line-height: 1.7;
        }
        
        .diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            position: relative;
        }

        .main-concept {
            background: var(--primary-color);
            color: white;
            padding: 15px 30px;
            border-radius: var(--border-radius);
            font-size: 1.2em;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(0, 123, 255, 0.4);
            transform: scale(0);
            animation: pop-in 0.5s 0.2s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
        }

        .components {
            display: flex;
            justify-content: space-around;
            width: 100%;
            margin-top: 80px;
            position: relative;
        }
        
        .component {
            background: white;
            border: 2px solid #ddd;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            z-index: 10;
            transform: translateY(20px);
            opacity: 0;
            animation: slide-up-fade-in 0.5s forwards;
        }
        
        .component:nth-child(1) { animation-delay: 0.8s; }
        .component:nth-child(2) { animation-delay: 1.0s; }
        .component:nth-child(3) { animation-delay: 1.2s; }

        .component:hover, .component.active {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-5px) scale(1.05);
            box-shadow: var(--shadow);
        }

        .connector {
            position: absolute;
            top: 45px; /* Half of .main-concept height approx */
            left: 50%;
            width: 2px;
            height: 80px;
            background: #ccc;
            transform-origin: top;
            transform: scaleY(0);
            animation: grow-line 0.5s 0.5s ease-out forwards;
        }

        .branch {
            position: absolute;
            top: 125px; /* end of connector */
            left: 50%;
            height: 2px;
            background: #ccc;
            transform-origin: left;
            transform: scaleX(0);
            animation: grow-line 0.5s forwards;
        }

        .branch.left { transform-origin: right; left: auto; right: 50%; animation-delay: 0.7s; width: 33%; }
        .branch.center { animation-delay: 0.7s; width: 0; }
        .branch.right { animation-delay: 0.7s; width: 33%;}
        
        .info-box {
            margin-top: 30px;
            background-color: var(--light-color);
            border: 1px solid #e0e0e0;
            border-radius: var(--border-radius);
            padding: 25px;
            width: 100%;
            height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.4s ease, transform 0.4s ease;
        }

        .info-box.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .info-box h3 {
            margin: 0 0 10px 0;
            color: var(--primary-color);
            font-weight: 500;
        }
        .info-box p {
            margin: 0;
            line-height: 1.6;
            font-size: 1em;
        }


        @keyframes pop-in {
            from { transform: scale(0); }
            to { transform: scale(1); }
        }
        
        @keyframes grow-line {
            from { transform: scale(0); }
            to { transform: scale(1); }
        }

        @keyframes slide-up-fade-in {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
                align-items: flex-start;
            }
            .container {
                padding: 20px;
            }
            .components {
                flex-direction: column;
                align-items: center;
                gap: 20px;
                margin-top: 30px;
            }
            .main-concept {
                margin-bottom: 30px;
            }
            .connector, .branch {
                display: none;
            }
            .info-box {
                height: auto;
            }
        }

    </style>
</head>
<body>

    <div class="container">
        
        <!-- Quiz Section -->
        <div id="quiz-container">
            <h1>互动小测验</h1>
            <p class="question">面向对象的分析模型主要由顶层架构图、用例与用例图和 (<span></span>) 构成。</p>
            <div class="options">
                <div class="option" data-answer="A">
                    <span class="option-letter">A</span>
                    <span class="option-text">数据流模型</span>
                </div>
                <div class="option" data-answer="B">
                    <span class="option-letter">B</span>
                    <span class="option-text">领域概念模型</span>
                </div>
                <div class="option" data-answer="C">
                    <span class="option-letter">C</span>
                    <span class="option-text">功能分解图</span>
                </div>
                <div class="option" data-answer="D">
                    <span class="option-letter">D</span>
                    <span class="option-text">功能需求模型</span>
                </div>
            </div>
        </div>

        <!-- Explanation Section -->
        <div id="explanation-container">
             <h2 class="explanation-title">知识点解析：面向对象分析模型</h2>
             <p class="explanation-intro">
                太棒了！让我们来深入了解一下。想象我们要盖一座房子，在动工前，我们需要一份清晰的蓝图。"面向对象分析模型"就是软件开发的早期"蓝图"，帮我们搞清楚要做的软件是什么样子，以及它应该如何工作，为后续的设计和实现打下坚实的基础。
             </p>

            <div class="diagram">
                <div class="main-concept">面向对象分析模型 (OOA)</div>
                
                <div class="connector"></div>
                <div class="branch left"></div>
                <div class="branch right"></div>

                <div class="components">
                    <div class="component" data-title="顶层架构图" data-explanation="这就像房子的整体外观和结构设计图，告诉我们软件有哪几个主要部分，它们之间如何协同工作。它为整个系统提供了一个高层次的概览。">
                        顶层架构图
                    </div>
                    <div class="component" data-title="用例与用例图" data-explanation="这描述了"谁"会使用这个软件，以及他们能用它做什么。比如，"网站访客"可以"浏览商品"、"添加购物车"。它从用户的角度定义了系统的功能需求。">
                        用例与用例图
                    </div>
                    <div class="component component-answer" data-title="领域概念模型 (正确答案)" data-explanation="这是对软件所涉及领域里所有重要"事物"的定义。比如在一个购物网站中，"商品"、"订单"、"用户"就是重要的概念。它揭示了问题的核心结构和业务实体。">
                        领域概念模型
                    </div>
                    <div class="component" data-title="类图 (可选扩展)" data-explanation="类图是领域概念模型更进一步的细化，它展示了系统中的类、它们的属性、方法以及类之间的关系（如继承、关联）。虽然它更多属于设计阶段，但在分析阶段进行初步的类识别也很有帮助。">
                        类图
                    </div>
                </div>

                <div class="info-box">
                    <h3>什么是面向对象分析模型？</h3>
                    <p>面向对象分析模型主要由顶层架构图、用例与用例图和领域概念模型构成，它们共同描绘了软件的蓝图。</p>
                </div>
            </div>

        </div>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const options = document.querySelectorAll('.option');
            const quizContainer = document.getElementById('quiz-container');
            const explanationContainer = document.getElementById('explanation-container');
            const correctAnswer = 'B';

            options.forEach(option => {
                option.addEventListener('click', handleOptionClick);
            });

            function handleOptionClick(event) {
                const selectedOption = event.currentTarget;
                if (selectedOption.classList.contains('disabled')) return;

                const selectedAnswer = selectedOption.dataset.answer;
                
                // Disable all options
                options.forEach(opt => opt.classList.add('disabled'));

                if (selectedAnswer === correctAnswer) {
                    selectedOption.classList.add('correct');
                    // Wait for animation then show explanation
                    setTimeout(() => {
                        quizContainer.classList.add('hidden');
                        explanationContainer.classList.add('visible');
                        setupExplanationInteraction();
                    }, 1000);
                } else {
                    selectedOption.classList.add('incorrect');
                    // Re-enable options after a delay so user can try again
                    setTimeout(() => {
                        selectedOption.classList.remove('incorrect');
                         options.forEach(opt => opt.classList.remove('disabled'));
                    }, 1500);
                }
            }
            
            function setupExplanationInteraction() {
                const components = document.querySelectorAll('.component');
                const infoBox = document.querySelector('.info-box');
                const infoTitle = infoBox.querySelector('h3');
                const infoText = infoBox.querySelector('p');
                
                // Highlight the correct answer component
                const answerComponent = document.querySelector('.component[data-answer="B"], .component-answer');
                if (answerComponent) {
                    answerComponent.classList.add('active');
                }


                components.forEach(component => {
                    component.addEventListener('click', () => {
                        // Update active state
                        components.forEach(c => c.classList.remove('active'));
                        component.classList.add('active');
                        
                        // Update info box
                        const title = component.dataset.title;
                        const explanation = component.dataset.explanation;

                        infoBox.classList.remove('visible');

                        setTimeout(() => {
                            infoTitle.textContent = title;
                            infoText.textContent = explanation;
                            infoBox.classList.add('visible');
                        }, 200); // A short delay for fade out/in effect
                    });
                });
                
                // Trigger initial display
                const initialComponent = document.querySelector('.component-answer'); // Changed to initially highlight the correct answer
                if (initialComponent) {
                    setTimeout(()=> {
                        initialComponent.click();
                    }, 1500); // wait for component animations
                }
            }
        });
    </script>
</body>
</html> 