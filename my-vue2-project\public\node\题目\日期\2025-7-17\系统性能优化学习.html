<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统性能优化 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .system-container {
            display: flex;
            justify-content: space-around;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 30px;
        }

        .system-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 30px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 300px;
            position: relative;
            overflow: hidden;
        }

        .system-box:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .system-box.database {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .system-box.application {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .system-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .parameter-list {
            list-style: none;
            padding: 0;
        }

        .parameter-item {
            background: rgba(255,255,255,0.2);
            margin: 10px 0;
            padding: 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .parameter-item:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(10px);
        }

        .parameter-item.highlight {
            background: rgba(255,255,255,0.4);
            animation: pulse 2s infinite;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
        }

        .question {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: white;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .option:hover {
            border-color: #667eea;
            transform: scale(1.05);
        }

        .option.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .option.correct {
            border-color: #4CAF50;
            background: #e8f5e8;
        }

        .option.wrong {
            border-color: #f44336;
            background: #ffebee;
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border: 3px solid #667eea;
            border-radius: 15px;
            background: white;
            cursor: pointer;
        }

        .explanation {
            background: #f8f9fa;
            border-left: 5px solid #667eea;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 10px 10px 0;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: slideInLeft 0.5s ease-out;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .floating-icon {
            position: absolute;
            font-size: 2rem;
            opacity: 0.3;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 系统性能优化</h1>
            <p class="subtitle">通过互动学习掌握数据库与应用系统的性能调整要点</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 知识概览</h2>
            <div class="system-container">
                <div class="system-box database" onclick="exploreSystem('database')">
                    <div class="floating-icon" style="top: 10px; right: 10px;">💾</div>
                    <h3 class="system-title">数据库系统</h3>
                    <ul class="parameter-list">
                        <li class="parameter-item">CPU/内存使用状况</li>
                        <li class="parameter-item highlight">查询语句性能 ⭐</li>
                        <li class="parameter-item">进程/线程使用状态</li>
                        <li class="parameter-item">日志文件大小</li>
                    </ul>
                </div>

                <div class="system-box application" onclick="exploreSystem('application')">
                    <div class="floating-icon" style="top: 10px; right: 10px;">📱</div>
                    <h3 class="system-title">应用系统</h3>
                    <ul class="parameter-list">
                        <li class="parameter-item">应用系统可用性</li>
                        <li class="parameter-item">响应时间</li>
                        <li class="parameter-item highlight">并发用户数 ⭐</li>
                        <li class="parameter-item">特定应用资源占用</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <h3 style="color: white; margin-bottom: 20px;">🎮 性能监控模拟器</h3>
            <canvas id="gameCanvas" width="800" height="400"></canvas>
            <br>
            <button class="btn" onclick="startSimulation()">开始模拟</button>
            <button class="btn" onclick="resetSimulation()">重置</button>
        </div>

        <div class="quiz-section">
            <h2 style="color: #333; margin-bottom: 30px;">🧠 知识测试</h2>
            <div class="question">
                对于数据库系统，主要包括CPU/内存使用状况、（请作答此空）、进程/线程使用状态、日志文件大小等。
            </div>
            <div class="options">
                <div class="option" onclick="selectOption(this, 'A')">A. 数据丢包率</div>
                <div class="option" onclick="selectOption(this, 'B')">B. 端口吞吐量</div>
                <div class="option" onclick="selectOption(this, 'C')">C. 数据处理速率</div>
                <div class="option" onclick="selectOption(this, 'D')">D. 查询语句性能</div>
            </div>
            <button class="btn" onclick="checkAnswer()" id="checkBtn" style="display: none;">检查答案</button>
        </div>

        <div class="explanation" id="explanation">
            <h3>💡 详细解析</h3>
            <p><strong>正确答案：D. 查询语句性能</strong></p>
            <p>数据库系统的性能优化主要关注以下几个方面：</p>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li><strong>CPU/内存使用状况</strong>：监控系统资源占用</li>
                <li><strong>查询语句性能</strong>：优化SQL查询效率，这是数据库性能的核心</li>
                <li><strong>进程/线程使用状态</strong>：监控并发处理能力</li>
                <li><strong>日志文件大小</strong>：管理存储空间和I/O性能</li>
            </ul>
            <p>而应用系统则关注：可用性、响应时间、<strong>并发用户数</strong>、资源占用等。</p>
        </div>
    </div>

    <script>
        let selectedAnswer = null;
        let progress = 0;
        let canvas, ctx;
        let animationId;
        let particles = [];

        // 初始化
        window.onload = function() {
            canvas = document.getElementById('gameCanvas');
            ctx = canvas.getContext('2d');
            updateProgress(25);
            initParticles();
        };

        // 粒子系统
        function initParticles() {
            particles = [];
            for(let i = 0; i < 50; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: Math.random() * 3 + 1,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`
                });
            }
        }

        function updateParticles() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景网格
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.1)';
            ctx.lineWidth = 1;
            for(let i = 0; i < canvas.width; i += 40) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for(let i = 0; i < canvas.height; i += 40) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制性能指标
            drawPerformanceMetrics();

            // 更新粒子
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;

                if(particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if(particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = particle.color;
                ctx.fill();
            });

            animationId = requestAnimationFrame(updateParticles);
        }

        function drawPerformanceMetrics() {
            const time = Date.now() * 0.001;
            
            // CPU使用率
            const cpuUsage = 50 + Math.sin(time) * 30;
            drawMetricBar(50, 50, 200, 20, cpuUsage, 'CPU使用率', '#ff6b6b');
            
            // 内存使用率
            const memUsage = 60 + Math.cos(time * 1.2) * 25;
            drawMetricBar(50, 100, 200, 20, memUsage, '内存使用率', '#4ecdc4');
            
            // 查询性能
            const queryPerf = 70 + Math.sin(time * 0.8) * 20;
            drawMetricBar(50, 150, 200, 20, queryPerf, '查询性能', '#45b7d1');
            
            // 响应时间
            const responseTime = 40 + Math.cos(time * 1.5) * 30;
            drawMetricBar(450, 50, 200, 20, responseTime, '响应时间', '#96ceb4');
            
            // 并发用户数
            const concurrentUsers = 55 + Math.sin(time * 0.6) * 35;
            drawMetricBar(450, 100, 200, 20, concurrentUsers, '并发用户数', '#feca57');
            
            // 系统可用性
            const availability = 85 + Math.cos(time * 0.4) * 10;
            drawMetricBar(450, 150, 200, 20, availability, '系统可用性', '#ff9ff3');
        }

        function drawMetricBar(x, y, width, height, value, label, color) {
            // 背景
            ctx.fillStyle = 'rgba(0,0,0,0.1)';
            ctx.fillRect(x, y, width, height);
            
            // 进度条
            ctx.fillStyle = color;
            ctx.fillRect(x, y, (width * value) / 100, height);
            
            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText(`${label}: ${Math.round(value)}%`, x, y - 5);
        }

        function startSimulation() {
            if(animationId) cancelAnimationFrame(animationId);
            updateParticles();
            updateProgress(50);
        }

        function resetSimulation() {
            if(animationId) cancelAnimationFrame(animationId);
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            initParticles();
            updateProgress(25);
        }

        function exploreSystem(type) {
            if(type === 'database') {
                alert('🔍 数据库系统性能优化重点：\n\n1. 查询语句性能优化是核心\n2. 索引设计和维护\n3. 数据库架构优化\n4. 资源监控和调优');
            } else {
                alert('🔍 应用系统性能优化重点：\n\n1. 并发用户数管理\n2. 响应时间优化\n3. 系统可用性保障\n4. 资源合理分配');
            }
            updateProgress(75);
        }

        function selectOption(element, answer) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // 选中当前选项
            element.classList.add('selected');
            selectedAnswer = answer;
            
            // 显示检查按钮
            document.getElementById('checkBtn').style.display = 'inline-block';
        }

        function checkAnswer() {
            const options = document.querySelectorAll('.option');
            
            options.forEach(option => {
                const letter = option.textContent.charAt(0);
                if(letter === 'D') {
                    option.classList.add('correct');
                } else if(option.classList.contains('selected') && letter !== 'D') {
                    option.classList.add('wrong');
                }
            });

            // 显示解析
            document.getElementById('explanation').classList.add('show');
            document.getElementById('checkBtn').style.display = 'none';
            
            updateProgress(100);
            
            // 庆祝动画
            if(selectedAnswer === 'D') {
                celebrateCorrectAnswer();
            }
        }

        function celebrateCorrectAnswer() {
            // 创建庆祝粒子
            for(let i = 0; i < 20; i++) {
                setTimeout(() => {
                    createCelebrationParticle();
                }, i * 100);
            }
        }

        function createCelebrationParticle() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
            particles.push({
                x: canvas.width / 2,
                y: canvas.height / 2,
                vx: (Math.random() - 0.5) * 10,
                vy: (Math.random() - 0.5) * 10,
                size: Math.random() * 5 + 3,
                color: colors[Math.floor(Math.random() * colors.length)]
            });
        }

        function updateProgress(value) {
            progress = value;
            document.getElementById('progressFill').style.width = value + '%';
        }

        // 添加鼠标交互
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 在点击位置创建粒子
            particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 5,
                vy: (Math.random() - 0.5) * 5,
                size: Math.random() * 4 + 2,
                color: `hsl(${Math.random() * 360}, 70%, 60%)`
            });
        });
    </script>
</body>
</html>
