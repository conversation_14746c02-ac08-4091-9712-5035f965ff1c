<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Benevolent</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #FEF9E7; /* A light, warm yellow */
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #5D4037; /* A warm brown */
            overflow: hidden;
        }
        #animation-container {
            text-align: center;
            position: relative;
        }
        canvas {
            background-color: #ffffff;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(93, 64, 55, 0.1);
            cursor: pointer;
        }
        #instruction {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 16px;
            color: #795548;
            background-color: rgba(255, 255, 255, 0.85);
            padding: 8px 15px;
            border-radius: 15px;
            animation: fadeIn 1s ease-in-out;
            opacity: 0;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="animation-container">
        <canvas id="word-canvas" width="800" height="600"></canvas>
        <div id="instruction">点击屏幕继续动画</div>
    </div>

    <script>
        const canvas = document.getElementById('word-canvas');
        const ctx = canvas.getContext('2d');
        const instructionEl = document.getElementById('instruction');

        let stage = 0;
        const totalStages = 7;

        const colors = {
            primary: '#FFC107', // Amber
            secondary: '#E91E63', // Pink
            dark: '#5D4037', // Brown
            light: '#FFF8E1', // Light Cream
            blue: '#4FC3F7', // Light Blue
        };

        let alpha = 0;
        let wordPos = { x: canvas.width / 2, y: canvas.height / 2 };
        let benePos = { x: canvas.width / 2, y: canvas.height / 2 };
        let volPos = { x: canvas.width / 2, y: canvas.height / 2 };
        let entPos = { x: canvas.width / 2, y: canvas.height / 2 };
        let sunRotation = 0;
        let heartScale = 1;
        let handY = canvas.height + 100;


        function lerp(start, end, t) {
            return start * (1 - t) + end * t;
        }

        function drawText(text, x, y, size = 60, color = colors.dark, a = 1) {
            ctx.save();
            ctx.globalAlpha = a;
            ctx.font = `bold ${size}px 'Segoe UI'`;
            ctx.fillStyle = color;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
            ctx.restore();
        }

        function drawExplanation(text, x, y, a = 1) {
            drawText(text, x, y, 24, colors.dark, a);
        }

        function drawSun(x, y, scale, rotation, a = 1) {
            ctx.save();
            ctx.globalAlpha = a;
            ctx.translate(x, y);
            ctx.scale(scale, scale);
            ctx.rotate(rotation);

            // Sun rays
            for (let i = 0; i < 8; i++) {
                ctx.fillStyle = colors.primary;
                ctx.beginPath();
                ctx.arc(0, 0, 80, (Math.PI / 180) * (i * 45 - 5), (Math.PI / 180) * (i * 45 + 5));
                ctx.arc(0, 0, 120, (Math.PI / 180) * (i * 45 + 17.5), (Math.PI / 180) * (i * 45 + 27.5));
                ctx.closePath();
                ctx.fill();
            }

            // Sun face
            ctx.fillStyle = colors.primary;
            ctx.beginPath();
            ctx.arc(0, 0, 80, 0, Math.PI * 2);
            ctx.fill();

            // Smile
            ctx.strokeStyle = colors.dark;
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(0, 20, 40, 0.2 * Math.PI, 0.8 * Math.PI);
            ctx.stroke();

            // Eyes
            ctx.fillStyle = colors.dark;
            ctx.beginPath();
            ctx.arc(-25, -15, 8, 0, Math.PI * 2);
            ctx.arc(25, -15, 8, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        }

        function drawHeart(x, y, scale, a = 1) {
            ctx.save();
            ctx.globalAlpha = a;
            ctx.translate(x, y);
            ctx.scale(scale, scale);
            ctx.fillStyle = colors.secondary;
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.bezierCurveTo(35, -70, 105, -35, 0, 70);
            ctx.bezierCurveTo(-105, -35, -35, -70, 0, 0);
            ctx.fill();
            ctx.restore();
        }

        function drawHand(x, y, a=1) {
            ctx.save();
            ctx.globalAlpha = a;
            ctx.fillStyle = '#F5DEB3'; // Wheat color for hand
            ctx.strokeStyle = colors.dark;
            ctx.lineWidth = 3;

            // Giving hand
            ctx.beginPath();
            ctx.moveTo(x - 80, y);
            ctx.lineTo(x, y - 20);
            ctx.lineTo(x + 150, y-20);
            ctx.quadraticCurveTo(x + 180, y, x + 150, y + 40);
            ctx.lineTo(x-80, y+40);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();

            // Receiving hand
             ctx.beginPath();
            ctx.moveTo(x + 280, y+80);
            ctx.lineTo(x + 200, y + 100);
            ctx.lineTo(x + 50, y+100);
            ctx.quadraticCurveTo(x + 20, y+80, x + 50, y + 40);
            ctx.lineTo(x+280, y+40);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();

            // Heart transfer
            drawHeart(x + 130, y - 20, 0.4);

            ctx.restore();
        }


        function intro() {
            alpha = lerp(alpha, 1, 0.05);
            drawText('benevolent', wordPos.x, wordPos.y, 80, colors.dark, alpha);
            if (alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }

        function splitWord() {
            instructionEl.style.opacity = 0;
            let targetBeneX = canvas.width / 2 - 200;
            let targetVolX = canvas.width / 2;
            let targetEntX = canvas.width / 2 + 180;
            
            benePos.x = lerp(benePos.x, targetBeneX, 0.1);
            volPos.x = lerp(volPos.x, targetVolX, 0.1);
            entPos.x = lerp(entPos.x, targetEntX, 0.1);

            drawText('bene', benePos.x, benePos.y, 70, colors.primary);
            drawText('vol', volPos.x, volPos.y, 70, colors.secondary);
            drawText('ent', entPos.x, entPos.y, 70, colors.blue);

            if (Math.abs(benePos.x - targetBeneX) < 1) {
                stage++;
                alpha = 0;
            }
        }

        function showBene() {
            drawText('bene', canvas.width / 4, 100, 80, colors.primary);
            alpha = lerp(alpha, 1, 0.05);
            drawExplanation('=  well, good (好)', canvas.width / 4 + 250, 100, alpha);
            
            sunRotation += 0.01;
            drawSun(canvas.width / 2, canvas.height / 2 + 100, 1, sunRotation, alpha);
            
            if(alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }

        function showVol() {
            instructionEl.style.opacity = 0;
            alpha = 0;
            drawText('vol', canvas.width / 4, 100, 80, colors.secondary);
            alpha = lerp(alpha, 1, 0.05);
            drawExplanation('=  wish, will (意愿)', canvas.width / 4 + 250, 100, alpha);
            
            heartScale = 1 + Math.sin(Date.now() * 0.005) * 0.1;
            drawHeart(canvas.width / 2, canvas.height / 2 + 80, heartScale, alpha);

            if(alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }

        function showEnt() {
            instructionEl.style.opacity = 0;
            alpha = 0;
            drawText('ent', canvas.width / 4, 100, 80, colors.blue);
            alpha = lerp(alpha, 1, 0.05);
            drawExplanation('adj. suffix (...的)', canvas.width / 4 + 250, 100, alpha);

             if(alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }
        
        function combine() {
            instructionEl.style.opacity = 0;
            alpha = lerp(alpha, 1, 0.05);
            
            let yPos = lerp(canvas.height / 2, 150, alpha);
            drawText('bene (好) + vol (意愿) + ent', canvas.width/2, yPos, 40, colors.dark, alpha);
            
            let targetHandY = canvas.height / 2 - 50;
            handY = lerp(handY, targetHandY, 0.05);
            drawHand(canvas.width / 2 - 100, handY, alpha);
            
            drawText('benevolent = 仁慈的，善意的', canvas.width/2, 500, 40, colors.dark, alpha);

            if(alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }

        function showExample() {
            instructionEl.style.opacity = 0;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawText('benevolent', canvas.width / 2, 100, 80, colors.dark);
            drawExplanation('adj. kind, helpful, and generous', canvas.width / 2, 200);
            
            ctx.font = "24px 'Segoe UI'";
            ctx.textAlign = 'center';
            ctx.fillStyle = colors.dark;
            ctx.fillText("Example: A benevolent smile.", canvas.width / 2, 350);
            ctx.fillText("一个善意的微笑。", canvas.width / 2, 400);

            ctx.fillText("The old man was a benevolent ruler.", canvas.width / 2, 480);
            ctx.fillText("那位老人是一位仁慈的统治者。", canvas.width / 2, 530);
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            switch (stage) {
                case 0: intro(); break;
                case 1: splitWord(); break;
                case 2: showBene(); break;
                case 3: showVol(); break;
                case 4: showEnt(); break;
                case 5: combine(); break;
                case 6: showExample(); break;
            }
            animationFrame = requestAnimationFrame(animate);
        }

        canvas.addEventListener('click', () => {
            if (stage < totalStages - 1) {
                stage++;
                alpha = 0;
            }
        });

        animate();
    </script>
</body>
</html> 