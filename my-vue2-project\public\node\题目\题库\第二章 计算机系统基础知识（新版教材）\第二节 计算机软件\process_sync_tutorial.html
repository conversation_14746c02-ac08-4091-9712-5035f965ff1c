<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进程同步与PV操作 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .process-graph {
            width: 100%;
            height: 300px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            margin: 20px 0;
            background: #f8f9fa;
        }

        .code-section {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 15px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }

        .code-line {
            margin: 5px 0;
            padding: 5px 10px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .code-line.highlight {
            background: rgba(102, 126, 234, 0.3);
            transform: scale(1.02);
        }

        .blank {
            background: #ffd700;
            color: #333;
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .blank:hover {
            background: #ffed4e;
            transform: scale(1.1);
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #4caf50;
            color: white;
        }

        .semaphore-display {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .semaphore {
            background: white;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 15px;
            margin: 5px;
            text-align: center;
            min-width: 80px;
            transition: all 0.3s ease;
        }

        .semaphore.active {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .process-node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .process-node:hover {
            transform: scale(1.1);
        }

        .process-node.running {
            background: linear-gradient(45deg, #4caf50, #45a049);
            animation: pulse 1s infinite;
        }

        .process-node.waiting {
            background: linear-gradient(45deg, #ff9800, #f57c00);
        }

        .process-node.completed {
            background: linear-gradient(45deg, #9e9e9e, #757575);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .arrow {
            position: absolute;
            stroke: #667eea;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .quiz-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option:hover {
            border-color: #667eea;
            background: #e8f0fe;
        }

        .option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .option.correct {
            border-color: #4caf50;
            background: #4caf50;
            color: white;
        }

        .option.incorrect {
            border-color: #f44336;
            background: #f44336;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 进程同步与PV操作</h1>
            <p>零基础学习操作系统进程同步机制</p>
        </div>

        <div class="step-indicator">
            <div class="step active" id="step1">1</div>
            <div class="step" id="step2">2</div>
            <div class="step" id="step3">3</div>
            <div class="step" id="step4">4</div>
            <div class="step" id="step5">5</div>
        </div>

        <div class="content-grid">
            <div class="card">
                <h2>📚 基础概念</h2>
                <div class="explanation" id="concept-explanation">
                    <h3>什么是进程同步？</h3>
                    <p>进程同步是指多个进程按照一定的顺序执行，确保程序的正确性。就像接力赛一样，每个选手必须等前一个选手跑完才能开始。</p>
                    
                    <h3>PV操作是什么？</h3>
                    <ul>
                        <li><strong>P操作（等待）</strong>：检查资源是否可用，如果不可用就等待</li>
                        <li><strong>V操作（通知）</strong>：释放资源，通知其他进程可以继续</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h2>🎯 进程前趋图</h2>
                <canvas class="process-graph" id="processGraph"></canvas>
                <p style="text-align: center; margin-top: 10px;">
                    点击进程节点查看详细信息
                </p>
            </div>
        </div>

        <div class="card full-width">
            <h2>💻 程序代码分析</h2>
            <div class="code-section" id="codeSection">
                <div class="code-line">begin</div>
                <div class="code-line">  S1，S2，S3，S4，S5，S6，S7：semaphore；   //定义信号量</div>
                <div class="code-line">  S1:=0；S2：=0；S3:=0；S4=0；S5:=0；S6:=0；S7:=0；</div>
                <div class="code-line">  Cobegin</div>
                <div class="code-line">    P1: begin P1执行; V(S1); V(S2); end;</div>
                <div class="code-line">    P2: begin P(S1); P2执行; <span class="blank" data-answer="V(S3)">空a</span>; end;</div>
                <div class="code-line">    P3: begin <span class="blank" data-answer="P(S2)">空b</span>; P3执行; <span class="blank" data-answer="V(S4)V(S5)">空c</span>; end;</div>
                <div class="code-line">    P4: begin <span class="blank" data-answer="P(S3)">空d</span>; <span class="blank" data-answer="P(S4)">空e</span>; P4执行; V(S6); end;</div>
                <div class="code-line">    P5: begin <span class="blank" data-answer="P(S5)">空f</span>; P5执行; <span class="blank" data-answer="V(S7)">空g</span>; end;</div>
                <div class="code-line">    P6: begin P(S6); P(S7); P6执行; end;</div>
                <div class="code-line">  Coend；</div>
                <div class="code-line">end</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="startAnimation()">🎬 开始动画演示</button>
            <button class="btn" onclick="resetAnimation()">🔄 重置</button>
            <button class="btn" onclick="showQuiz()">📝 开始答题</button>
            <button class="btn" onclick="showP4Analysis()">🔍 重点分析P4</button>
            <button class="btn" onclick="showP5P6Analysis()">🔍 分析P5和P6</button>
        </div>

        <div class="card full-width">
            <h2>📊 信号量状态</h2>
            <div class="semaphore-display" id="semaphoreDisplay">
                <div class="semaphore" id="s1">S1: 0</div>
                <div class="semaphore" id="s2">S2: 0</div>
                <div class="semaphore" id="s3">S3: 0</div>
                <div class="semaphore" id="s4">S4: 0</div>
                <div class="semaphore" id="s5">S5: 0</div>
                <div class="semaphore" id="s6">S6: 0</div>
                <div class="semaphore" id="s7">S7: 0</div>
            </div>
        </div>

        <div class="quiz-section" id="quizSection" style="display: none;">
            <h2>📝 练习题目</h2>
            <div id="quizContent"></div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let animationRunning = false;
        let semaphoreValues = {S1: 0, S2: 0, S3: 0, S4: 0, S5: 0, S6: 0, S7: 0};
        
        // 初始化画布
        function initCanvas() {
            const canvas = document.getElementById('processGraph');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            
            drawProcessGraph(ctx);
        }
        
        // 绘制进程前趋图
        function drawProcessGraph(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
            
            const processes = {
                P1: {x: 100, y: 150, status: 'ready'},
                P2: {x: 250, y: 100, status: 'ready'},
                P3: {x: 250, y: 200, status: 'ready'},
                P4: {x: 400, y: 100, status: 'ready'},
                P5: {x: 400, y: 200, status: 'ready'},
                P6: {x: 550, y: 150, status: 'ready'}
            };
            
            // 绘制箭头
            drawArrow(ctx, processes.P1, processes.P2);
            drawArrow(ctx, processes.P1, processes.P3);
            drawArrow(ctx, processes.P2, processes.P4);
            drawArrow(ctx, processes.P3, processes.P4);
            drawArrow(ctx, processes.P3, processes.P5);
            drawArrow(ctx, processes.P4, processes.P6);
            drawArrow(ctx, processes.P5, processes.P6);
            
            // 绘制进程节点
            Object.entries(processes).forEach(([name, process]) => {
                drawProcess(ctx, name, process.x, process.y, process.status);
            });
        }
        
        function drawProcess(ctx, name, x, y, status) {
            const colors = {
                ready: '#667eea',
                running: '#4caf50',
                waiting: '#ff9800',
                completed: '#9e9e9e'
            };
            
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, 2 * Math.PI);
            ctx.fillStyle = colors[status];
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(name, x, y);
        }
        
        function drawArrow(ctx, from, to) {
            const angle = Math.atan2(to.y - from.y, to.x - from.x);
            const startX = from.x + 30 * Math.cos(angle);
            const startY = from.y + 30 * Math.sin(angle);
            const endX = to.x - 30 * Math.cos(angle);
            const endY = to.y - 30 * Math.sin(angle);
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 箭头头部
            const headLength = 10;
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX - headLength * Math.cos(angle - Math.PI / 6), 
                      endY - headLength * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX - headLength * Math.cos(angle + Math.PI / 6), 
                      endY - headLength * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }
        
        // 更新信号量显示
        function updateSemaphoreDisplay() {
            Object.entries(semaphoreValues).forEach(([name, value]) => {
                const element = document.getElementById(name.toLowerCase());
                element.textContent = `${name}: ${value}`;
                element.className = 'semaphore';
                if (value > 0) {
                    element.classList.add('active');
                }
            });
        }
        
        // 动画演示
        async function startAnimation() {
            if (animationRunning) return;
            animationRunning = true;

            const steps = [
                {
                    step: 1,
                    title: "理解前趋关系",
                    explanation: "P1必须先执行，然后P2和P3才能执行。P4需要等待P2和P3都完成。P5需要等待P3完成。P6需要等待P4和P5都完成。",
                    action: () => {
                        highlightProcesses(['P1']);
                        animateProcessGraph('step1');
                    }
                },
                {
                    step: 2,
                    title: "P1执行完成，通知P2和P3",
                    explanation: "P1执行V(S1)和V(S2)操作，通知P2和P3可以开始执行。S1和S2的值变为1。",
                    action: () => {
                        semaphoreValues.S1 = 1;
                        semaphoreValues.S2 = 1;
                        updateSemaphoreDisplay();
                        highlightProcesses(['P2', 'P3']);
                        highlightCodeLine(4);
                    }
                },
                {
                    step: 3,
                    title: "分析P2和P3的空白处",
                    explanation: "P2执行P(S1)等待P1，完成后用V(S3)通知P4。P3执行P(S2)等待P1，完成后用V(S4)V(S5)通知P4和P5。",
                    action: () => {
                        semaphoreValues.S3 = 1;
                        semaphoreValues.S4 = 1;
                        semaphoreValues.S5 = 1;
                        updateSemaphoreDisplay();
                        highlightCodeLine(5);
                        highlightCodeLine(6);
                    }
                },
                {
                    step: 4,
                    title: "重点分析P4的同步条件",
                    explanation: "P4必须等待P2和P3都完成！所以空d是P(S3)等待P2，空e是P(S4)等待P3。P4完成后用V(S6)通知P6。",
                    action: () => {
                        semaphoreValues.S6 = 1;
                        updateSemaphoreDisplay();
                        highlightCodeLine(7);
                        showDetailedP4Analysis();
                    }
                },
                {
                    step: 5,
                    title: "P5和P6的同步",
                    explanation: "P5用P(S5)等待P3完成，完成后用V(S7)通知P6。P6用P(S6)和P(S7)等待P4和P5都完成。",
                    action: () => {
                        semaphoreValues.S7 = 1;
                        updateSemaphoreDisplay();
                        highlightCodeLine(8);
                        highlightCodeLine(9);
                    }
                }
            ];

            for (let i = 0; i < steps.length; i++) {
                updateStep(steps[i].step);
                updateExplanation(steps[i].title, steps[i].explanation);
                steps[i].action();
                await sleep(4000);
            }

            // 显示总结
            showFinalSummary();
            animationRunning = false;
        }

        function showDetailedP4Analysis() {
            const explanation = document.getElementById('concept-explanation');
            explanation.innerHTML = `
                <h3>🎯 P4进程的关键分析</h3>
                <div style="background: #fff3cd; padding: 15px; border-radius: 10px; margin: 10px 0;">
                    <h4>为什么P4需要两个P操作？</h4>
                    <ul>
                        <li><strong>P(S3)</strong>：等待P2进程完成（P2执行V(S3)通知）</li>
                        <li><strong>P(S4)</strong>：等待P3进程完成（P3执行V(S4)通知）</li>
                    </ul>
                    <p><strong>记忆技巧：</strong>P操作是"等待"，V操作是"通知"</p>
                </div>
                <div style="background: #d1ecf1; padding: 15px; border-radius: 10px;">
                    <h4>P4完成后做什么？</h4>
                    <p><strong>V(S6)</strong>：通知P6进程可以开始执行</p>
                </div>
            `;
        }

        function showFinalSummary() {
            const explanation = document.getElementById('concept-explanation');
            explanation.innerHTML = `
                <h3>📋 完整答案总结</h3>
                <div style="background: #d4edda; padding: 20px; border-radius: 10px;">
                    <h4>问题1：空a、空b、空c</h4>
                    <p><strong>答案：A. V(S3)、P(S2)和V(S4)V(S5)</strong></p>

                    <h4>问题2：空d、空e</h4>
                    <p><strong>答案：B. P(S3)P(S4)和V(S6)</strong></p>

                    <h4>问题3：空f、空g ✅ 你答对了！</h4>
                    <p><strong>答案：C. P(S5)和V(S7)</strong></p>
                </div>

                <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin-top: 15px;">
                    <h4>🎯 信号量对应关系表</h4>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                        <tr style="background: #f5f5f5;">
                            <th style="border: 1px solid #ddd; padding: 8px;">信号量</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">作用</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">V操作者</th>
                            <th style="border: 1px solid #ddd; padding: 8px;">P操作者</th>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">S1</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P1通知P2</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P1</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P2</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">S2</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P1通知P3</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P1</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P3</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">S3</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P2通知P4</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P2</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P4</td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">S4</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P3通知P4</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P3</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P4</td>
                        </tr>
                        <tr style="background: #fff3cd;">
                            <td style="border: 1px solid #ddd; padding: 8px;"><strong>S5</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;"><strong>P3通知P5</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;"><strong>P3</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;"><strong>P5</strong></td>
                        </tr>
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">S6</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P4通知P6</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P4</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">P6</td>
                        </tr>
                        <tr style="background: #fff3cd;">
                            <td style="border: 1px solid #ddd; padding: 8px;"><strong>S7</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;"><strong>P5通知P6</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;"><strong>P5</strong></td>
                            <td style="border: 1px solid #ddd; padding: 8px;"><strong>P6</strong></td>
                        </tr>
                    </table>
                </div>

                <div style="background: #f8d7da; padding: 15px; border-radius: 10px; margin-top: 15px;">
                    <h4>🔑 解题关键</h4>
                    <ul>
                        <li>看前趋图确定依赖关系</li>
                        <li>P操作用于等待前驱进程</li>
                        <li>V操作用于通知后继进程</li>
                        <li>每个信号量对应一个特定的同步关系</li>
                    </ul>
                </div>
            `;
        }

        function animateProcessGraph(step) {
            // 这里可以添加更复杂的图形动画
            const canvas = document.getElementById('processGraph');
            const ctx = canvas.getContext('2d');

            // 根据步骤高亮不同的进程和连接
            switch(step) {
                case 'step1':
                    // 高亮P1
                    break;
                case 'step2':
                    // 高亮P1到P2、P3的连接
                    break;
                // 可以添加更多步骤的动画
            }
        }
        
        function highlightProcesses(processes) {
            // 这里可以添加高亮进程的逻辑
        }
        
        function highlightCodeLine(lineNumber) {
            const lines = document.querySelectorAll('.code-line');
            lines.forEach(line => line.classList.remove('highlight'));
            if (lines[lineNumber]) {
                lines[lineNumber].classList.add('highlight');
            }
        }
        
        function updateStep(step) {
            document.querySelectorAll('.step').forEach(s => {
                s.classList.remove('active');
                if (parseInt(s.textContent) < step) {
                    s.classList.add('completed');
                }
            });
            document.getElementById(`step${step}`).classList.add('active');
        }
        
        function updateExplanation(title, text) {
            const explanation = document.getElementById('concept-explanation');
            explanation.innerHTML = `<h3>${title}</h3><p>${text}</p>`;
        }
        
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        function resetAnimation() {
            animationRunning = false;
            currentStep = 1;
            semaphoreValues = {S1: 0, S2: 0, S3: 0, S4: 0, S5: 0, S6: 0, S7: 0};
            updateSemaphoreDisplay();
            updateStep(1);
            document.querySelectorAll('.code-line').forEach(line => line.classList.remove('highlight'));
            initCanvas();
        }
        
        function showQuiz() {
            const quizSection = document.getElementById('quizSection');
            const quizContent = document.getElementById('quizContent');

            quizContent.innerHTML = `
                <h3>问题1：空a、空b和空c处应分别为？</h3>
                <div class="explanation" style="margin-bottom: 15px;">
                    <strong>分析思路：</strong>
                    <ul>
                        <li>空a：P2执行完后需要通知P4可以开始 → V(S3)</li>
                        <li>空b：P3开始前需要等待P1完成 → P(S2)</li>
                        <li>空c：P3执行完后需要通知P4和P5 → V(S4)V(S5)</li>
                    </ul>
                </div>
                <div class="option" onclick="selectOption(this, true, '正确！P2通知P4用V(S3)，P3等待P1用P(S2)，P3通知P4和P5用V(S4)V(S5)')">A. V(S3)、P(S2)和V(S4)V(S5)</div>
                <div class="option" onclick="selectOption(this, false, '错误！P2应该通知P4，不是等待')">B. P(S3)、P(S2)和V(S4)V(S5)</div>
                <div class="option" onclick="selectOption(this, false, '错误！P3应该等待P1，不是通知P1')">C. V(S2)、P(S3)和P(S4)P(S3)</div>
                <div class="option" onclick="selectOption(this, false, '错误！操作顺序和对象都不对')">D. V(S2)、V(S3)和P(S3)P(S4)</div>

                <h3 style="margin-top: 30px;">问题2：空d和空e处应分别为？</h3>
                <div class="explanation" style="margin-bottom: 15px;">
                    <strong>关键分析：</strong>
                    <ul>
                        <li>P4必须等待P2和P3都完成才能开始</li>
                        <li>空d：等待P2完成 → P(S3)</li>
                        <li>空e：等待P3完成 → P(S4)</li>
                        <li>P4完成后通知P6 → V(S6)</li>
                    </ul>
                </div>
                <div class="option" onclick="selectOption(this, false, '错误！P4应该等待，不是通知')">A. V(S3)V(S4)和V(S6)</div>
                <div class="option" onclick="selectOption(this, true, '正确！P4等待P2和P3完成，然后通知P6')">B. P(S3)P(S4)和V(S6)</div>
                <div class="option" onclick="selectOption(this, false, '错误！P4应该等待P3，不是通知P3')">C. P(S3)V(S4)和V(S6)</div>
                <div class="option" onclick="selectOption(this, false, '错误！P4应该通知P6，不是等待P6')">D. P(S3)V(S4)和P(S6)</div>

                <h3 style="margin-top: 30px;">问题3：空f和空g处应分别为？</h3>
                <div class="explanation" style="margin-bottom: 15px;">
                    <strong>🎯 关键分析（你答对了！）：</strong>
                    <ul>
                        <li>P5必须等待P3完成才能开始</li>
                        <li>空f：等待P3完成 → P(S5)</li>
                        <li>空g：P5完成后通知P6 → V(S7)</li>
                    </ul>
                    <div style="background: #d4edda; padding: 10px; border-radius: 8px; margin-top: 10px;">
                        <strong>✅ 恭喜你答对了！正确答案是C</strong>
                    </div>
                </div>
                <div class="option" onclick="selectOption(this, false, '错误！P5应该等待P3，不是通知P3')">A. V(S5)和V(S7)</div>
                <div class="option" onclick="selectOption(this, false, '错误！P5应该通知P6，不是等待P6')">B. P(S5)和P(S7)</div>
                <div class="option correct" onclick="selectOption(this, true, '正确！P5等待P3完成用P(S5)，通知P6用V(S7)')">C. P(S5)和V(S7) ✅ 你的答案</div>
                <div class="option" onclick="selectOption(this, false, '错误！操作类型完全搞反了')">D. V(S5)和P(S7)</div>
            `;

            quizSection.style.display = 'block';
            quizSection.scrollIntoView({behavior: 'smooth'});
        }
        
        function selectOption(element, isCorrect, explanation) {
            const siblings = element.parentNode.querySelectorAll('.option');
            siblings.forEach(option => {
                option.classList.remove('selected');
            });

            element.classList.add('selected');

            setTimeout(() => {
                if (isCorrect) {
                    element.classList.add('correct');
                    element.innerHTML += ' ✅ 正确！';
                } else {
                    element.classList.add('incorrect');
                    element.innerHTML += ' ❌ 错误';
                }

                // 显示详细解释
                if (explanation) {
                    const explanationDiv = document.createElement('div');
                    explanationDiv.className = 'explanation';
                    explanationDiv.style.marginTop = '10px';
                    explanationDiv.innerHTML = `<strong>解释：</strong> ${explanation}`;
                    element.parentNode.insertBefore(explanationDiv, element.nextSibling);
                }
            }, 500);
        }
        
        // 专门分析P4的函数
        function showP4Analysis() {
            const explanation = document.getElementById('concept-explanation');
            explanation.innerHTML = `
                <h3>🎯 P4进程详细分析</h3>
                <div style="background: #fff3cd; padding: 20px; border-radius: 15px; margin: 15px 0;">
                    <h4>📋 P4的前趋条件</h4>
                    <p>根据前趋图，P4必须等待<strong>P2和P3都完成</strong>才能开始执行。</p>

                    <h4>🔍 空d和空e的分析</h4>
                    <div style="background: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <p><strong>空d：P(S3)</strong></p>
                        <ul>
                            <li>P2执行完后会执行V(S3)，将S3的值从0变为1</li>
                            <li>P4执行P(S3)检查S3是否为1，如果是则继续，否则等待</li>
                            <li>这样确保P4等待P2完成</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <p><strong>空e：P(S4)</strong></p>
                        <ul>
                            <li>P3执行完后会执行V(S4)，将S4的值从0变为1</li>
                            <li>P4执行P(S4)检查S4是否为1，如果是则继续，否则等待</li>
                            <li>这样确保P4等待P3完成</li>
                        </ul>
                    </div>

                    <h4>✅ P4完成后</h4>
                    <p>P4执行完后需要执行<strong>V(S6)</strong>通知P6可以开始执行。</p>
                </div>

                <div style="background: #d1ecf1; padding: 15px; border-radius: 10px;">
                    <h4>💡 记忆口诀</h4>
                    <p><strong>"P操作等前驱，V操作通后继"</strong></p>
                    <ul>
                        <li>P4要等P2和P3 → P(S3)P(S4)</li>
                        <li>P4要通知P6 → V(S6)</li>
                    </ul>
                </div>
            `;

            // 高亮相关代码行
            highlightCodeLine(7);

            // 滚动到解释区域
            explanation.scrollIntoView({behavior: 'smooth'});
        }

        // 专门分析P5和P6的函数
        function showP5P6Analysis() {
            const explanation = document.getElementById('concept-explanation');
            explanation.innerHTML = `
                <h3>🎯 P5和P6进程详细分析</h3>

                <div style="background: #e8f5e8; padding: 20px; border-radius: 15px; margin: 15px 0;">
                    <h4>📋 P5进程分析（问题3重点）</h4>
                    <div style="background: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <p><strong>空f：P(S5) ✅</strong></p>
                        <ul>
                            <li><strong>为什么是P(S5)？</strong></li>
                            <li>P5必须等待P3完成才能开始执行</li>
                            <li>P3执行完后会执行V(S5)，将S5的值从0变为1</li>
                            <li>P5执行P(S5)检查S5是否为1，确保P3已完成</li>
                        </ul>
                    </div>

                    <div style="background: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <p><strong>空g：V(S7) ✅</strong></p>
                        <ul>
                            <li><strong>为什么是V(S7)？</strong></li>
                            <li>P5执行完后需要通知P6可以开始</li>
                            <li>P6需要等待P5完成，所以P6会执行P(S7)</li>
                            <li>P5执行V(S7)将S7从0变为1，通知P6</li>
                        </ul>
                    </div>
                </div>

                <div style="background: #f0f8ff; padding: 20px; border-radius: 15px; margin: 15px 0;">
                    <h4>📋 P6进程分析</h4>
                    <p>P6是最后执行的进程，需要等待<strong>P4和P5都完成</strong>：</p>
                    <div style="background: white; padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <ul>
                            <li><strong>P(S6)</strong>：等待P4完成（P4执行V(S6)通知）</li>
                            <li><strong>P(S7)</strong>：等待P5完成（P5执行V(S7)通知）</li>
                            <li>只有P4和P5都完成，P6才能开始执行</li>
                        </ul>
                    </div>
                </div>

                <div style="background: #ffe6e6; padding: 15px; border-radius: 10px;">
                    <h4>❌ 常见错误分析</h4>
                    <ul>
                        <li><strong>选A：V(S5)和V(S7)</strong> - 错误！P5应该等待P3，不是通知P3</li>
                        <li><strong>选B：P(S5)和P(S7)</strong> - 错误！P5应该通知P6，不是等待P6</li>
                        <li><strong>选D：V(S5)和P(S7)</strong> - 错误！操作类型完全搞反了</li>
                    </ul>
                </div>

                <div style="background: #d4edda; padding: 15px; border-radius: 10px; margin-top: 15px;">
                    <h4>🎯 正确答案：C. P(S5)和V(S7)</h4>
                    <p><strong>记忆技巧：</strong></p>
                    <ul>
                        <li>P5<strong>等待</strong>P3 → P(S5)</li>
                        <li>P5<strong>通知</strong>P6 → V(S7)</li>
                        <li>"等待用P，通知用V"</li>
                    </ul>
                </div>
            `;

            // 高亮相关代码行
            highlightCodeLine(8);
            highlightCodeLine(9);

            // 滚动到解释区域
            explanation.scrollIntoView({behavior: 'smooth'});
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initCanvas();
            updateSemaphoreDisplay();
        });

        // 窗口大小改变时重新绘制
        window.addEventListener('resize', () => {
            setTimeout(initCanvas, 100);
        });
    </script>
</body>
</html>
