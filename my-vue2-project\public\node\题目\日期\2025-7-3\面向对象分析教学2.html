<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象分析教学</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .question {
            background-color: #eef6ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 18px;
        }
        .explanation {
            margin: 20px 0;
            line-height: 1.6;
        }
        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .step-info {
            margin-top: 15px;
            font-weight: bold;
            text-align: center;
        }
        .progress-container {
            margin: 20px 0;
            height: 10px;
            background-color: #ddd;
            border-radius: 5px;
        }
        .progress-bar {
            height: 100%;
            background-color: #3498db;
            border-radius: 5px;
            width: 0%;
            transition: width 0.5s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>面向对象分析教学</h1>
        
        <div class="question">
            <p>在面向对象分析中，利用（ ）表示需求，并从中提炼出中（ ），以上两者形成（ ），之后再进行后续的开发工作。</p>
            <p>正确答案：A. 领域模型</p>
        </div>
        
        <div class="explanation">
            <h3>面向对象分析过程：</h3>
            <p>在面向对象分析中，我们先用<strong>用例与用例图</strong>表示需求，从用例模型中提炼<strong>领域模型</strong>，这两者共同形成<strong>体系结构图</strong>，然后进行后续开发。</p>
        </div>
        
        <div class="controls">
            <button id="prevBtn">上一步</button>
            <button id="nextBtn">下一步</button>
            <button id="resetBtn">重置</button>
        </div>
        
        <div class="progress-container">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="step-info" id="stepInfo">第1步：用例表示需求</div>
        
        <div class="canvas-container">
            <canvas id="animationCanvas" width="800" height="500"></canvas>
        </div>
    </div>

    <script>
        // 初始化变量
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let currentStep = 1;
        const totalSteps = 4;
        
        // 按钮事件
        document.getElementById('prevBtn').addEventListener('click', () => {
            if (currentStep > 1) {
                currentStep--;
                updateAnimation();
            }
        });
        
        document.getElementById('nextBtn').addEventListener('click', () => {
            if (currentStep < totalSteps) {
                currentStep++;
                updateAnimation();
            }
        });
        
        document.getElementById('resetBtn').addEventListener('click', () => {
            currentStep = 1;
            updateAnimation();
        });
        
        // 更新进度条和步骤信息
        function updateStepInfo() {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = (currentStep / totalSteps) * 100 + '%';
            
            const stepInfo = document.getElementById('stepInfo');
            switch(currentStep) {
                case 1:
                    stepInfo.textContent = '第1步：用例表示需求';
                    break;
                case 2:
                    stepInfo.textContent = '第2步：提炼领域模型';
                    break;
                case 3:
                    stepInfo.textContent = '第3步：形成体系结构图';
                    break;
                case 4:
                    stepInfo.textContent = '第4步：进行后续开发';
                    break;
            }
        }
        
        // 绘制用例图
        function drawUseCaseDiagram() {
            ctx.fillStyle = '#eaf2fd';
            ctx.fillRect(50, 50, 700, 400);
            
            // 绘制系统边界
            ctx.strokeStyle = '#333';
            ctx.beginPath();
            ctx.ellipse(400, 200, 250, 150, 0, 0, Math.PI * 2);
            ctx.stroke();
            
            // 绘制系统名称
            ctx.fillStyle = '#333';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('在线购物系统', 400, 80);
            
            // 绘制用例
            drawUseCase(320, 150, '浏览商品');
            drawUseCase(400, 220, '购买商品');
            drawUseCase(320, 290, '管理订单');
            drawUseCase(480, 150, '用户注册');
            drawUseCase(480, 290, '用户登录');
            
            // 绘制用户
            drawActor(100, 200, '顾客');
            drawActor(700, 200, '管理员');
            
            // 绘制关联线
            ctx.beginPath();
            ctx.moveTo(120, 200);
            ctx.lineTo(300, 150);
            ctx.moveTo(120, 200);
            ctx.lineTo(380, 220);
            ctx.moveTo(120, 200);
            ctx.lineTo(300, 290);
            ctx.moveTo(120, 200);
            ctx.lineTo(460, 150);
            ctx.moveTo(120, 200);
            ctx.lineTo(460, 290);
            
            ctx.moveTo(680, 200);
            ctx.lineTo(340, 290);
            ctx.stroke();
            
            // 绘制说明文字
            ctx.fillStyle = '#e74c3c';
            ctx.textAlign = 'center';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillText('用例图表示需求', 400, 400);
        }
        
        // 绘制领域模型
        function drawDomainModel() {
            ctx.fillStyle = '#f9f3e9';
            ctx.fillRect(50, 50, 700, 400);
            
            // 绘制类图框
            drawClass(200, 150, '用户', ['姓名', '账号', '密码'], ['注册()', '登录()']);
            drawClass(500, 150, '商品', ['商品名', '价格', '库存'], ['展示()', '更新()']);
            drawClass(200, 300, '订单', ['订单号', '日期', '状态'], ['创建()', '支付()']);
            drawClass(500, 300, '购物车', ['用户ID', '商品列表'], ['添加()', '结算()']);
            
            // 绘制关联线
            ctx.beginPath();
            ctx.moveTo(300, 170);
            ctx.lineTo(450, 170);
            ctx.moveTo(250, 210);
            ctx.lineTo(250, 280);
            ctx.moveTo(550, 210);
            ctx.lineTo(550, 280);
            ctx.moveTo(300, 320);
            ctx.lineTo(450, 320);
            ctx.stroke();
            
            // 绘制说明文字
            ctx.fillStyle = '#27ae60';
            ctx.textAlign = 'center';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillText('领域模型', 400, 400);
        }
        
        // 绘制体系结构图
        function drawArchitecture() {
            ctx.fillStyle = '#e8f4f8';
            ctx.fillRect(50, 50, 700, 400);
            
            // 绘制分层结构
            drawLayer(400, 120, 600, 60, '表示层（UI）');
            drawLayer(400, 200, 600, 60, '业务逻辑层');
            drawLayer(400, 280, 600, 60, '数据访问层');
            drawLayer(400, 360, 600, 60, '数据库');
            
            // 绘制连接线
            ctx.beginPath();
            ctx.moveTo(400, 150);
            ctx.lineTo(400, 170);
            ctx.moveTo(400, 230);
            ctx.lineTo(400, 250);
            ctx.moveTo(400, 310);
            ctx.lineTo(400, 330);
            
            // 绘制箭头
            drawArrow(400, 170);
            drawArrow(400, 250);
            drawArrow(400, 330);
            
            ctx.stroke();
            
            // 绘制说明文字
            ctx.fillStyle = '#8e44ad';
            ctx.textAlign = 'center';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillText('体系结构图', 400, 50);
        }
        
        // 绘制开发过程
        function drawDevelopment() {
            ctx.fillStyle = '#f9e9e9';
            ctx.fillRect(50, 50, 700, 400);
            
            // 绘制开发流程
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.font = '18px Microsoft YaHei';
            
            // 绘制开发阶段
            drawDevelopmentPhase(150, 150, '编码');
            drawDevelopmentPhase(300, 150, '测试');
            drawDevelopmentPhase(450, 150, '集成');
            drawDevelopmentPhase(600, 150, '部署');
            
            // 绘制流程箭头
            ctx.beginPath();
            ctx.moveTo(190, 150);
            ctx.lineTo(260, 150);
            ctx.moveTo(340, 150);
            ctx.lineTo(410, 150);
            ctx.moveTo(490, 150);
            ctx.lineTo(560, 150);
            
            // 绘制箭头
            drawArrow(260, 150);
            drawArrow(410, 150);
            drawArrow(560, 150);
            
            ctx.stroke();
            
            // 绘制代码示例
            ctx.fillStyle = '#333';
            ctx.textAlign = 'left';
            ctx.font = '14px Consolas';
            
            const code = [
                'class User {',
                '  constructor(name, account) {',
                '    this.name = name;',
                '    this.account = account;',
                '  }',
                '',
                '  register() { ... }',
                '  login() { ... }',
                '}'
            ];
            
            for (let i = 0; i < code.length; i++) {
                ctx.fillText(code[i], 100, 250 + i * 20);
            }
            
            // 绘制测试示例
            ctx.fillStyle = '#333';
            ctx.textAlign = 'left';
            
            const test = [
                'test("User Registration", () => {',
                '  const user = new User("张三", "zhangsan");',
                '  expect(user.register()).toBe(true);',
                '});'
            ];
            
            for (let i = 0; i < test.length; i++) {
                ctx.fillText(test[i], 400, 250 + i * 20);
            }
            
            // 绘制说明文字
            ctx.fillStyle = '#e67e22';
            ctx.textAlign = 'center';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillText('后续开发工作', 400, 400);
        }
        
        // 辅助函数：绘制用例椭圆
        function drawUseCase(x, y, text) {
            ctx.beginPath();
            ctx.ellipse(x, y, 70, 30, 0, 0, Math.PI * 2);
            ctx.fillStyle = 'white';
            ctx.fill();
            ctx.stroke();
            
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 5);
        }
        
        // 辅助函数：绘制参与者
        function drawActor(x, y, text) {
            // 绘制头部
            ctx.beginPath();
            ctx.arc(x, y - 40, 10, 0, Math.PI * 2);
            ctx.stroke();
            
            // 绘制身体
            ctx.beginPath();
            ctx.moveTo(x, y - 30);
            ctx.lineTo(x, y);
            
            // 绘制手臂
            ctx.moveTo(x - 20, y - 15);
            ctx.lineTo(x + 20, y - 15);
            
            // 绘制腿部
            ctx.moveTo(x, y);
            ctx.lineTo(x - 15, y + 30);
            
            ctx.moveTo(x, y);
            ctx.lineTo(x + 15, y + 30);
            
            ctx.stroke();
            
            // 绘制名称
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 50);
        }
        
        // 辅助函数：绘制类
        function drawClass(x, y, name, attributes, methods) {
            const width = 150;
            const headerHeight = 30;
            const attrHeight = attributes.length * 20;
            const methHeight = methods.length * 20;
            const totalHeight = headerHeight + attrHeight + methHeight;
            
            // 绘制类框
            ctx.fillStyle = 'white';
            ctx.fillRect(x, y, width, totalHeight);
            ctx.strokeRect(x, y, width, totalHeight);
            
            // 绘制分隔线
            ctx.beginPath();
            ctx.moveTo(x, y + headerHeight);
            ctx.lineTo(x + width, y + headerHeight);
            ctx.moveTo(x, y + headerHeight + attrHeight);
            ctx.lineTo(x + width, y + headerHeight + attrHeight);
            ctx.stroke();
            
            // 绘制类名
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillText(name, x + width/2, y + 20);
            
            // 绘制属性
            ctx.textAlign = 'left';
            ctx.font = '14px Microsoft YaHei';
            for (let i = 0; i < attributes.length; i++) {
                ctx.fillText(attributes[i], x + 10, y + headerHeight + 20 * (i + 0.7));
            }
            
            // 绘制方法
            for (let i = 0; i < methods.length; i++) {
                ctx.fillText(methods[i], x + 10, y + headerHeight + attrHeight + 20 * (i + 0.7));
            }
        }
        
        // 辅助函数：绘制层
        function drawLayer(x, y, width, height, text) {
            ctx.fillStyle = 'white';
            ctx.fillRect(x - width/2, y - height/2, width, height);
            ctx.strokeRect(x - width/2, y - height/2, width, height);
            
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText(text, x, y + 5);
        }
        
        // 辅助函数：绘制箭头
        function drawArrow(x, y) {
            ctx.moveTo(x, y);
            ctx.lineTo(x - 5, y - 10);
            ctx.moveTo(x, y);
            ctx.lineTo(x + 5, y - 10);
        }
        
        // 辅助函数：绘制开发阶段
        function drawDevelopmentPhase(x, y, text) {
            ctx.fillStyle = '#f8f9fa';
            ctx.beginPath();
            ctx.arc(x, y, 40, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
            
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText(text, x, y + 5);
        }
        
        // 更新动画
        function updateAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(currentStep) {
                case 1:
                    drawUseCaseDiagram();
                    break;
                case 2:
                    drawDomainModel();
                    break;
                case 3:
                    drawArchitecture();
                    break;
                case 4:
                    drawDevelopment();
                    break;
            }
            
            updateStepInfo();
        }
        
        // 初始化
        updateAnimation();
        
        // 添加交互性：允许拖动元素（示例功能）
        let isDragging = false;
        let selectedElement = null;
        let offsetX, offsetY;
        
        canvas.addEventListener('mousedown', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 简单判断是否点击在某个元素上
            if (currentStep === 2) {
                // 在领域模型步骤中判断点击的是哪个类
                const classes = [
                    {x: 200, y: 150, width: 150, height: 120},
                    {x: 500, y: 150, width: 150, height: 120},
                    {x: 200, y: 300, width: 150, height: 120},
                    {x: 500, y: 300, width: 150, height: 120}
                ];
                
                for (let i = 0; i < classes.length; i++) {
                    const cls = classes[i];
                    if (x >= cls.x && x <= cls.x + cls.width &&
                        y >= cls.y && y <= cls.y + cls.height) {
                        isDragging = true;
                        selectedElement = i;
                        offsetX = x - cls.x;
                        offsetY = y - cls.y;
                        break;
                    }
                }
            }
        });
        
        canvas.addEventListener('mousemove', (e) => {
            if (isDragging && selectedElement !== null) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // 这里可以添加拖动逻辑，但实现起来较复杂
                // 简单提示用户
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawDomainModel();
                ctx.fillStyle = 'rgba(255,0,0,0.2)';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('可以尝试拖动类图框！', 400, 450);
            }
        });
        
        canvas.addEventListener('mouseup', () => {
            isDragging = false;
            selectedElement = null;
        });
    </script>
</body>
</html> 