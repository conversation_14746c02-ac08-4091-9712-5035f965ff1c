<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-R图冲突解析</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            line-height: 1.6;
        }
        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-top: 0;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        h2 {
            font-size: 1.8em;
            margin-top: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        h3 {
            font-size: 1.4em;
            color: #3498db;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        p {
            margin-bottom: 10px;
        }
        .question-box {
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 8px;
            background-color: #f9f9f9;
            margin-bottom: 20px;
        }
        .explanation-box {
            margin-top: 25px;
            padding: 20px;
            background-color: #eaf6fd;
            border-left: 5px solid #3498db;
            border-radius: 5px;
        }
        .highlight {
            font-weight: bold;
            color: #e74c3c;
        }
        canvas {
            border: 2px solid #ccc;
            background-color: #fff;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background-color: #2980b9;
        }
        .correct-answer {
            color: #27ae60;
            font-weight: bold;
            text-align: center;
            margin-top: 15px;
            font-size: 1.1em;
        }
        .note {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>E-R图合并冲突问题解析</h1>

        <div class="question-box">
            <h2>题目</h2>
            <p>某高校信息系统设计的分E-R图中，人力部门定义的职工实体具有属性：<span class="highlight">职工号、姓名、性别和出生日期</span>；教学部门定义的教师实体具有属性：<span class="highlight">教师号、姓名和职称</span>。这种情况属于（），在合并E-R图时，（）解决这一冲突。</p>
            <p>A. 属性冲突</p>
            <p>B. 命名冲突</p>
            <p>C. 结构冲突</p>
            <p>D. 实体冲突</p>
            <div class="correct-answer">正确答案：C</div>
        </div>

        <div class="explanation-box">
            <h2>概念解释</h2>
            <h3>1. 属性冲突 (Attribute Conflict)</h3>
            <p>属性冲突是指在实体联系图中，<span class="highlight">属性值的类型、取值范围或取值集合不同</span>。例如，"零件号"有的定义为字符型，有的为数值型。</p>
            <p><strong>演示：</strong>（请点击下方按钮观看动画）</p>
            <div class="controls">
                <button onclick="demonstrateAttributeConflict()">演示属性冲突</button>
            </div>
            <canvas id="attributeCanvas" width="800" height="150"></canvas>

            <h3>2. 命名冲突 (Naming Conflict)</h3>
            <p>命名冲突是指<span class="highlight">不同的意义对象名称相同，或者，相同的意义不同的名称</span>。</p>
            <p><strong>演示：</strong>（请点击下方按钮观看动画）</p>
            <div class="controls">
                <button onclick="demonstrateNamingConflict()">演示命名冲突</button>
            </div>
            <canvas id="namingCanvas" width="800" height="150"></canvas>

            <h3>3. 结构冲突 (Structural Conflict)</h3>
            <p>结构冲突是指<span class="highlight">同一对象在不同应用中具有不同的抽象</span>。例如，"课程"在某一局部应用中被当做实体，而在另一局部应用中被当属性。</p>
            <p><strong>本题解析：</strong><br>在本题中，人力部门的"职工"和教学部门的"教师"虽然名称不同，但在实际中都指的是"学校的教职人员"这一<span class="highlight">同一对象</span>。然而，这两个部门对这个"同一对象"有着<span class="highlight">不同的抽象和关注点</span>（属性不同：职工号、性别、出生日期 vs 教师号、职称）。这正符合结构冲突的定义。</p>
            <p><strong>解决方法：</strong>通常通过<span class="highlight">调整结构，将同一对象统一抽象</span>来解决。例如，可以引入一个更通用的"人员"实体，或者将"职工"和"教师"合并成一个实体，并加入所有必要的属性，或者将其中一个实体作为另一个实体的子类。</p>
            <p><strong>演示：</strong>（请点击下方按钮观看动画）</p>
            <div class="controls">
                <button onclick="demonstrateStructuralConflict()">演示结构冲突</button>
            </div>
            <canvas id="structuralCanvas" width="800" height="300"></canvas>
            <p class="note">请注意：实体冲突通常指的是不同实体在语义上重叠，但本题情境更侧重于同一对象在不同抽象下的建模差异，因此结构冲突更为精确。</p>
        </div>
    </div>

    <script>
        // Canvas context for each demonstration
        const attrCanvas = document.getElementById('attributeCanvas');
        const attrCtx = attrCanvas.getContext('2d');
        const nameCanvas = document.getElementById('namingCanvas');
        const nameCtx = nameCanvas.getContext('2d');
        const structCanvas = document.getElementById('structuralCanvas');
        const structCtx = structCanvas.getContext('2d');

        // --- 辅助绘图函数 ---
        function drawRectangle(ctx, x, y, width, height, text, color = '#3498db') {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + width / 2, y + height / 2);
        }

        function drawEllipse(ctx, x, y, radiusX, radiusY, text, color = '#2ecc71') {
            ctx.beginPath();
            ctx.ellipse(x, y, radiusX, radiusY, 0, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#27ae60';
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
            ctx.closePath();
        }

        function drawLine(ctx, x1, y1, x2, y2, color = '#333') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }

        // --- 属性冲突演示 ---
        function demonstrateAttributeConflict() {
            attrCtx.clearRect(0, 0, attrCanvas.width, attrCanvas.height); // 清除画布

            // 初始状态
            drawRectangle(attrCtx, 50, 50, 100, 50, '零件');
            drawEllipse(attrCtx, 220, 75, 60, 25, '零件号 (字符型)');
            drawLine(attrCtx, 150, 75, 160, 75); // 连接线
            drawLine(attrCtx, 160, 75, 170, 75);

            drawRectangle(attrCtx, 450, 50, 100, 50, '零件');
            drawEllipse(attrCtx, 620, 75, 60, 25, '零件号 (数值型)');
            drawLine(attrCtx, 550, 75, 560, 75); // 连接线
            drawLine(attrCtx, 560, 75, 570, 75);

            attrCtx.fillStyle = '#e74c3c';
            attrCtx.font = '16px Arial';
            attrCtx.textAlign = 'center';
            attrCtx.fillText('-> 冲突: 零件号类型不一致', attrCanvas.width / 2, 25);
        }

        // --- 命名冲突演示 ---
        function demonstrateNamingConflict() {
            nameCtx.clearRect(0, 0, nameCanvas.width, nameCanvas.height); // 清除画布

            // 不同的意义对象名称相同
            drawRectangle(nameCtx, 50, 20, 100, 50, '客户');
            drawEllipse(nameCtx, 220, 45, 60, 25, '姓名');
            drawLine(nameCtx, 150, 45, 160, 45);
            drawEllipse(nameCtx, 220, 95, 60, 25, '地址');
            drawLine(nameCtx, 150, 45, 160, 45);

            drawRectangle(nameCtx, 450, 20, 100, 50, '客户');
            drawEllipse(nameCtx, 620, 45, 60, 25, '姓名');
            drawLine(nameCtx, 550, 45, 560, 45);
            drawEllipse(nameCtx, 620, 95, 60, 25, '电话');
            drawLine(nameCtx, 550, 45, 560, 45);

            nameCtx.fillStyle = '#e74c3c';
            nameCtx.font = '16px Arial';
            nameCtx.textAlign = 'center';
            nameCtx.fillText('-> 冲突: 两个"客户"实体代表不同业务对象', nameCanvas.width / 2, 120);

            // 相同的意义不同的名称 (简略示意)
            // nameCtx.fillText('或: "用户"和"顾客"指同一对象', nameCanvas.width / 2, 100);
        }


        // --- 结构冲突演示 ---
        function demonstrateStructuralConflict() {
            structCtx.clearRect(0, 0, structCanvas.width, structCanvas.height); // 清除画布

            const entityWidth = 80;
            const entityHeight = 40;
            const attrRadiusX = 50;
            const attrRadiusY = 20;

            // 阶段1: 分离的E-R图
            structCtx.fillStyle = '#2c3e50';
            structCtx.font = '18px Arial';
            structCtx.textAlign = 'center';
            structCtx.fillText('人力部门E-R图', structCanvas.width / 4, 25);
            structCtx.fillText('教学部门E-R图', structCanvas.width * 3 / 4, 25);

            // 人力部门 - 职工实体
            const empX = structCanvas.width / 4 - entityWidth / 2;
            const empY = 50;
            drawRectangle(structCtx, empX, empY, entityWidth, entityHeight, '职工');
            drawEllipse(structCtx, empX + entityWidth / 2, empY + entityHeight + 30, attrRadiusX, attrRadiusY, '职工号');
            drawLine(structCtx, empX + entityWidth / 2, empY + entityHeight, empX + entityWidth / 2, empY + entityHeight + 10);
            drawEllipse(structCtx, empX - 50, empY + entityHeight + 30, attrRadiusX, attrRadiusY, '姓名');
            drawLine(structCtx, empX + entityWidth / 2, empY + entityHeight, empX - 50, empY + entityHeight + 10);
            drawEllipse(structCtx, empX + entityWidth + 50, empY + entityHeight + 30, attrRadiusX, attrRadiusY, '性别');
            drawLine(structCtx, empX + entityWidth / 2, empY + entityHeight, empX + entityWidth + 50, empY + entityHeight + 10);
            drawEllipse(structCtx, empX + entityWidth / 2, empY + entityHeight + 80, attrRadiusX, attrRadiusY, '出生日期');
            drawLine(structCtx, empX + entityWidth / 2, empY + entityHeight + 40, empX + entityWidth / 2, empY + entityHeight + 60);

            // 教学部门 - 教师实体
            const teachX = structCanvas.width * 3 / 4 - entityWidth / 2;
            const teachY = 50;
            drawRectangle(structCtx, teachX, teachY, entityWidth, entityHeight, '教师');
            drawEllipse(structCtx, teachX + entityWidth / 2, teachY + entityHeight + 30, attrRadiusX, attrRadiusY, '教师号');
            drawLine(structCtx, teachX + entityWidth / 2, teachY + entityHeight, teachX + entityWidth / 2, teachY + entityHeight + 10);
            drawEllipse(structCtx, teachX - 50, teachY + entityHeight + 30, attrRadiusX, attrRadiusY, '姓名');
            drawLine(structCtx, teachX + entityWidth / 2, teachY + entityHeight, teachX - 50, teachY + entityHeight + 10);
            drawEllipse(structCtx, teachX + entityWidth + 50, teachY + entityHeight + 30, attrRadiusX, attrRadiusY, '职称');
            drawLine(structCtx, teachX + entityWidth / 2, teachY + entityHeight, teachX + entityWidth + 50, teachY + entityHeight + 10);

            // 动画步骤
            let step = 0;
            const maxSteps = 3; // 1: 初始，2: 冲突标记，3: 解决
            const interval = setInterval(() => {
                step++;
                structCtx.clearRect(0, structCanvas.height / 2 - 20, structCanvas.width, structCanvas.height / 2 + 20); // 清除下半部分

                if (step === 1) {
                    structCtx.fillStyle = '#e74c3c';
                    structCtx.font = '20px Arial';
                    structCtx.fillText('发现冲突: "职工" 和 "教师" 指同一对象但抽象不同！', structCanvas.width / 2, structCanvas.height / 2 - 30);
                    structCtx.fillText('↓↓↓', structCanvas.width / 2, structCanvas.height / 2);
                } else if (step === 2) {
                    structCtx.fillStyle = '#2c3e50';
                    structCtx.font = '18px Arial';
                    structCtx.fillText('冲突解决: 合并为统一实体', structCanvas.width / 2, structCanvas.height / 2 + 30);

                    // 合并后的实体
                    const mergedX = structCanvas.width / 2 - entityWidth / 2;
                    const mergedY = structCanvas.height / 2 + 60;
                    drawRectangle(structCtx, mergedX, mergedY, entityWidth + 40, entityHeight + 10, '教职员工');

                    // 合并后的属性
                    drawEllipse(structCtx, mergedX + (entityWidth + 40) / 2, mergedY + entityHeight + 30, attrRadiusX, attrRadiusY, '职工号');
                    drawLine(structCtx, mergedX + (entityWidth + 40) / 2, mergedY + entityHeight, mergedX + (entityWidth + 40) / 2, mergedY + entityHeight + 10);
                    drawEllipse(structCtx, mergedX + (entityWidth + 40) / 2 + 100, mergedY + entityHeight + 30, attrRadiusX, attrRadiusY, '教师号');
                    drawLine(structCtx, mergedX + (entityWidth + 40) / 2, mergedY + entityHeight, mergedX + (entityWidth + 40) / 2 + 100, mergedY + entityHeight + 10);


                    drawEllipse(structCtx, mergedX - 50, mergedY + entityHeight + 30, attrRadiusX, attrRadiusY, '姓名');
                    drawLine(structCtx, mergedX + (entityWidth + 40) / 2, mergedY + entityHeight, mergedX - 50, mergedY + entityHeight + 10);

                    drawEllipse(structCtx, mergedX + (entityWidth + 40) / 2, mergedY + entityHeight + 80, attrRadiusX, attrRadiusY, '性别');
                    drawLine(structCtx, mergedX + (entityWidth + 40) / 2, mergedY + entityHeight + 40, mergedX + (entityWidth + 40) / 2, mergedY + entityHeight + 60);

                    drawEllipse(structCtx, mergedX + (entityWidth + 40) / 2 + 100, mergedY + entityHeight + 80, attrRadiusX, attrRadiusY, '出生日期');
                    drawLine(structCtx, mergedX + (entityWidth + 40) / 2 + 100, mergedY + entityHeight + 40, mergedX + (entityWidth + 40) / 2 + 100, mergedY + entityHeight + 60);

                    drawEllipse(structCtx, mergedX - 50, mergedY + entityHeight + 80, attrRadiusX, attrRadiusY, '职称');
                    drawLine(structCtx, mergedX + (entityWidth + 40) / 2, mergedY + entityHeight + 40, mergedX - 50, mergedY + entityHeight + 60);

                    clearInterval(interval);
                } else if (step > maxSteps) {
                    clearInterval(interval);
                }
            }, 1500); // 每1.5秒一步
        }

        // 页面加载完成后，默认演示结构冲突
        document.addEventListener('DOMContentLoaded', () => {
            //demonstrateStructuralConflict(); // 页面加载时自动播放一次
        });
    </script>
</body>
</html> 