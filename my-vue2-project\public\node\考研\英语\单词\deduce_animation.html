<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Deduce</title>
    <!-- 引入第三方库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <script type="importmap">
        {
            "imports": {
                "three": "https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.module.js",
                "three/addons/": "https://cdn.jsdelivr.net/npm/three@0.128.0/examples/jsm/"
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
        :root {
            --primary-color: #455a64; /* 蓝灰色，代表逻辑与严谨 */
            --secondary-color: #37474f;
            --accent-color: #ffab40; /* 琥珀色，点亮结论 */
            --light-bg: #eceff1;
            --panel-bg: #ffffff;
            --text-color: #263238;
        }
        body { font-family: 'Roboto', 'Noto Sans SC', sans-serif; background-color: #cfd8dc; color: var(--text-color); display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; overflow: hidden; }
        .container { display: flex; flex-direction: row; width: 95%; max-width: 1400px; height: 90vh; max-height: 800px; background-color: var(--panel-bg); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .word-panel { flex: 1; padding: 40px; display: flex; flex-direction: column; justify-content: center; background-color: var(--light-bg); overflow-y: auto; }
        .word-panel h1 { font-size: 3.5em; color: var(--primary-color); }
        .word-panel .pronunciation { font-size: 1.5em; color: #546e7a; margin-bottom: 20px; }
        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--primary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: var(--secondary-color); }
        .breakdown-section { margin-top: 25px; padding: 20px; background-color: #fafafa; border-radius: 10px; }
        .breakdown-section h3 { margin-top: 0; color: var(--secondary-color); font-size: 1.3em; margin-bottom: 15px; }
        .morpheme-btn { padding: 8px 15px; border: 2px solid var(--primary-color); border-radius: 20px; background-color: transparent; color: var(--primary-color); font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; }
        .morpheme-btn:hover, .morpheme-btn.active { background-color: var(--primary-color); color: white; transform: translateY(-2px); }
        .animation-panel { flex: 2; padding: 20px; display: flex; flex-direction: column; justify-content: center; align-items: center; position: relative; background: #37474f; }
        .activity-title { font-size: 1.8em; color: var(--light-bg); margin-bottom: 15px; text-align: center; }
        .activity-wrapper { display: none; width: 100%; height: calc(100% - 100px); flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        .game-container { width: 100%; height: 100%; position: relative; display: flex; align-items: center; justify-content: center; border-radius: 15px; background: linear-gradient(135deg, #455a64, #263238); }
        .control-button { margin-top: 20px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; }
        
        /* 逻辑漏斗 */
        #funnel-container { width: 300px; height: 300px; position: relative; }
        #funnel { width: 100%; height: 100%; background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="0 0, 100 0, 70 100, 30 100" style="fill:rgba(255,255,255,0.1)"/></svg>') no-repeat center center; background-size: contain; }
        .clue { position: absolute; font-size: 2em; transition: all 2s ease-in-out; }
        .conclusion { position: absolute; font-size: 3em; color: var(--accent-color); bottom: -20px; left: 50%; transform: translate(-50%, 100%); opacity: 0; transition: all 1s 2s; }
        .funnel-run .clue:nth-child(1) { top: 75%; left: 45%; }
        .funnel-run .clue:nth-child(2) { top: 75%; left: 45%; }
        .funnel-run .clue:nth-child(3) { top: 75%; left: 45%; }
        .funnel-run .conclusion { transform: translate(-50%, 0); opacity: 1; }

        #deduce-canvas { display: block; width: 100%; height: 100%; cursor: grab; }
        #deduce-canvas:active { cursor: grabbing; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>deduce</h1>
            <p class="pronunciation">[dɪˈdjuːs]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 推论，演绎</p>
                <p><strong>含义：</strong> 基于已有的信息或证据，通过逻辑推理得出结论。</p>
                <div class="example">
                    <p><strong>例句：</strong> From the evidence, the detective deduced that the butler was the culprit.</p>
                    <p><strong>翻译：</strong>根据证据，侦探推断管家就是罪犯。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>词缀解析 (2D)</h3>
                <button class="morpheme-btn" data-activity="full-animation">动画: 逻辑漏斗</button>
            </div>
            <div class="breakdown-section">
                <h3>完整单词活动 (3D)</h3>
                <button class="morpheme-btn" data-activity="full-3d-animation">互动: 解开谜题</button>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p style="color:white;">点击左侧按钮，开始演绎推理之旅！</p></div>
            
            <div id="full-animation" class="activity-wrapper">
                <div class="game-container">
                    <div id="funnel-container">
                        <div class="clue" style="top: 10%; left: 10%;">👣</div>
                        <div class="clue" style="top: 0%; left: 45%;">❓</div>
                        <div class="clue" style="top: 10%; right: 10%;">⏱️</div>
                        <div class="conclusion">💡</div>
                        <div id="funnel"></div>
                    </div>
                </div>
                <button class="control-button" id="deduce-btn">推导</button>
            </div>
            
            <div id="full-3d-animation" class="activity-wrapper">
                <div class="game-container"><canvas id="deduce-canvas"></canvas></div>
                <div style="color: white; position: absolute; bottom: 20px;">拖动鼠标旋转，从正确的角度找到答案。</div>
            </div>
        </div>
    </div>
    <script type="module">
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';
        import { FontLoader } from 'three/addons/loaders/FontLoader.js';

        // --- 切换逻辑 ---
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                document.getElementById(btn.dataset.activity)?.classList.add('active');
            });
        });

        // --- 2D 逻辑漏斗 ---
        document.getElementById('deduce-btn').addEventListener('click', (e) => {
            const container = document.getElementById('funnel-container');
            container.classList.toggle('funnel-run');
            e.target.textContent = container.classList.contains('funnel-run') ? '重置' : '推导';
        });

        // --- 3D 解开谜题 ---
        function setupDeduceAnimation() {
            const canvas = document.getElementById('deduce-canvas');
            if (!canvas) return;

            let scene, camera, renderer, controls, puzzleGroup, conclusionBox, isSolved = false;

            function init() {
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x263238);
                
                const container = canvas.parentElement;
                renderer = new THREE.WebGLRenderer({ canvas, antialias: true, alpha: true });
                renderer.setSize(container.clientWidth, container.clientHeight);
                renderer.setPixelRatio(window.devicePixelRatio);
                
                camera = new THREE.PerspectiveCamera(50, container.clientWidth / container.clientHeight, 0.1, 1000);
                camera.position.set(0, 0, 18);
                
                controls = new OrbitControls(camera, renderer.domElement);
                controls.enableDamping = true;
                controls.enablePan = false;
                controls.minDistance = 10;
                controls.maxDistance = 30;
                
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
                scene.add(ambientLight);
                const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
                dirLight.position.set(5, 10, 7.5);
                scene.add(dirLight);

                createPuzzle();
                createConclusionBox();
            }

            function createPuzzle() {
                puzzleGroup = new THREE.Group();
                const material = new THREE.MeshStandardMaterial({ 
                    color: 0xffffff, 
                    transparent: true, 
                    opacity: 0.5,
                    emissive: new THREE.Color(0xffab40), 
                    emissiveIntensity: 0
                });

                // Create key shape parts
                const barGeo = new THREE.BoxGeometry(0.5, 3, 0.5);
                const part1 = new THREE.Mesh(barGeo, material.clone());
                part1.position.set(0, -1, 0);

                const circleGeo = new THREE.RingGeometry(1, 1.5, 32);
                const part2 = new THREE.Mesh(circleGeo, material.clone());
                part2.position.set(0, 2, 0);

                // Scatter them
                part1.position.set(-5, 3, -4);
                part1.rotation.set(Math.random(), Math.random(), Math.random());
                part2.position.set(6, -2, 5);
                part2.rotation.set(Math.random(), Math.random(), Math.random());
                
                puzzleGroup.add(part1, part2);
                scene.add(puzzleGroup);
            }

            function createConclusionBox() {
                const geo = new THREE.BoxGeometry(4, 4, 4);
                const mat = new THREE.MeshStandardMaterial({ color: 0x455a64 });
                conclusionBox = new THREE.Mesh(geo, mat);
                scene.add(conclusionBox);
            }
            
            function animate() {
                if (isSolved) return;
                requestAnimationFrame(animate);
                controls.update();

                // Check for solution
                const targetRotation = new THREE.Euler(0, 0, 0); // Solution angle
                const currentRotation = camera.rotation;
                const tolerance = 0.1;
                
                if (Math.abs(currentRotation.x - targetRotation.x) < tolerance &&
                    Math.abs(currentRotation.y - targetRotation.y) < tolerance &&
                    Math.abs(currentRotation.z - targetRotation.z) < tolerance) {
                    solvePuzzle();
                }

                renderer.render(scene, camera);
            }
            
            function solvePuzzle() {
                if (isSolved) return;
                isSolved = true;
                controls.enabled = false;

                gsap.to(puzzleGroup.children[0].position, { duration: 1, x: 0, y: -1, z: 8, ease: "power3.inOut" });
                gsap.to(puzzleGroup.children[0].rotation, { duration: 1, x: 0, y: 0, z: 0, ease: "power3.inOut" });
                gsap.to(puzzleGroup.children[1].position, { duration: 1, x: 0, y: 2, z: 8, ease: "power3.inOut" });
                gsap.to(puzzleGroup.children[1].rotation, { duration: 1, x: 0, y: 0, z: 0, ease: "power3.inOut" });

                puzzleGroup.children.forEach(child => {
                    gsap.to(child.material, { duration: 1, opacity: 1 });
                    gsap.to(child.material, { duration: 0.5, emissiveIntensity: 1, delay: 1, yoyo: true, repeat: 1 });
                });
                
                gsap.to(conclusionBox.scale, { duration: 1, x:0.1, y:0.1, z:0.1, delay: 2, onComplete: showConclusionText });
            }

            function showConclusionText() {
                const loader = new FontLoader();
                loader.load('https://cdn.jsdelivr.net/npm/three@0.128.0/examples/fonts/helvetiker_bold.typeface.json', function (font) {
                    const textGeo = new TextGeometry('DEDUCE', {
                        font: font,
                        size: 1.5,
                        height: 0.2,
                    });
                    textGeo.center();
                    const textMat = new THREE.MeshStandardMaterial({ color: 0xffab40 });
                    const textMesh = new THREE.Mesh(textGeo, textMat);
                    scene.add(textMesh);
                    gsap.from(textMesh.scale, { duration: 1, x: 0, y: 0, z: 0 });
                });
            }

            const observer = new MutationObserver((mutations) => {
                const isActive = document.getElementById('full-3d-animation').classList.contains('active');
                if (isActive && !renderer) {
                   init();
                   animate();
                }
            });
            observer.observe(document.getElementById('full-3d-animation'), { attributes: true, attributeFilter: ['class'] });
        }
        
        setupDeduceAnimation();
    </script>
</body>
</html> 