<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Adapt</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
        :root {
            --primary-color: #43a047; /* 绿色，代表生命与适应 */
            --secondary-color: #388e3c;
            --accent-color: #fdd835; /* 黄色，点缀 */
            --light-bg: #e8f5e9;
            --panel-bg: #ffffff;
            --text-color: #1b5e20;
        }
        body { font-family: 'Roboto', 'Noto Sans SC', sans-serif; background-color: #c8e6c9; color: var(--text-color); display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; overflow: hidden; }
        .container { display: flex; flex-direction: row; width: 95%; max-width: 1400px; height: 90vh; max-height: 800px; background-color: var(--panel-bg); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .word-panel { flex: 1; padding: 40px; display: flex; flex-direction: column; justify-content: center; background-color: var(--light-bg); overflow-y: auto; }
        .word-panel h1 { font-size: 3.5em; color: var(--primary-color); }
        .word-panel .pronunciation { font-size: 1.5em; color: var(--secondary-color); margin-bottom: 20px; }
        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--secondary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: #2e7d32; }
        .breakdown-section { margin-top: 25px; padding: 20px; background-color: #ffffff; border-radius: 10px; }
        .morpheme-btn { margin: 5px; padding: 8px 15px; border: 2px solid var(--primary-color); border-radius: 20px; background-color: transparent; color: var(--primary-color); font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; }
        .morpheme-btn:hover, .morpheme-btn.active { background-color: var(--primary-color); color: white; transform: translateY(-2px); }
        .animation-panel { flex: 2; padding: 20px; display: flex; flex-direction: column; justify-content: center; align-items: center; position: relative; background: #fafafa; }
        .activity-title { font-size: 1.8em; color: var(--primary-color); margin-bottom: 15px; text-align: center; }
        .activity-wrapper { display: none; width: 100%; height: calc(100% - 100px); flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        .game-container { width: 100%; height: 100%; position: relative; display: flex; align-items: center; justify-content: center; border-radius: 15px; background: #f1f8e9; border: 1px solid #dcedc8; overflow: hidden; }
        .control-button { margin-top: 20px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; }
        
        /* Shifter Game */
        #shifter-object { position: absolute; left: 15%; top: 50%; transform: translateY(-50%); width: 100px; height: 100px; background-color: var(--accent-color); clip-path: circle(50% at 50% 50%); }
        #target-hole { position: absolute; right: 15%; top: 50%; transform: translateY(-50%); width: 100px; height: 100px; background-color: #c8e6c9; clip-path: polygon(50% 0%, 0% 100%, 100% 100%); }
        .status-text { position: absolute; top: 10%; left: 50%; transform: translateX(-50%); font-size: 1.5em; color: var(--primary-color); opacity: 0; }

        /* Chameleon Game */
        #chameleon-canvas { background: #fff; cursor: grab; }
        #chameleon-canvas:active { cursor: grabbing; }

        /* Circuit Game */
        .circuit-container { display: grid; grid-template-columns: 1fr 1fr 1fr; grid-template-rows: 1fr auto 1fr; width: 80%; height: 60%; align-items: center; justify-items: center; }
        .circuit-element { width: 80px; height: 80px; border: 3px solid var(--secondary-color); border-radius: 10px; display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center; font-weight: bold; background: #fff; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        #power-source { grid-column: 1; grid-row: 2; font-size: 1.5em; }
        #device { grid-column: 3; grid-row: 2; font-size: 1.5em; color: #9e9e9e; }
        #device.active { color: #fdd835; text-shadow: 0 0 10px #fdd835, 0 0 20px #fdd835; }
        #adapter { position: absolute; left: 100px; top: 100px; cursor: grab; background: var(--accent-color); z-index: 10; }
        #adapter:active { cursor: grabbing; }
        #drop-zone { grid-column: 2; grid-row: 2; width: 90px; height: 90px; border: 3px dashed var(--primary-color); border-radius: 10px; background: rgba(67, 160, 71, 0.1); }
        #circuit-status { grid-column: 1 / span 3; grid-row: 1; align-self: start; font-size: 1.2em; color: var(--primary-color); }
        .connection-line { position: absolute; height: 5px; background: #9e9e9e; transform-origin: left center; z-index: -1; }
        .connection-line.active { background: linear-gradient(90deg, #fdd835, var(--primary-color)); }

        /* Morpheme Animations */
        .ad-container, .apt-container { position: relative; }
        .ad-arrow { font-size: 80px; color: var(--primary-color); position: absolute; left: 20%; top: 50%; transform: translateY(-50%); }
        .ad-target { width: 80px; height: 80px; border: 5px solid var(--secondary-color); border-radius: 50%; position: absolute; right: 20%; top: 50%; transform: translateY(-50%); }
        .apt-piece { width: 80px; height: 80px; background: var(--accent-color); clip-path: polygon(0 25%, 50% 25%, 50% 0, 100% 50%, 50% 100%, 50% 75%, 0 75%); position: absolute; left: 20%; top: 50%; transform: translateY(-50%); }
        .apt-hole { width: 80px; height: 80px; background: #e8f5e9; border: 2px dashed var(--primary-color); clip-path: polygon(0 25%, 50% 25%, 50% 0, 100% 50%, 50% 100%, 50% 75%, 0 75%); position: absolute; right: 20%; top: 50%; transform: translateY(-50%); }

        /* Responsive Design Game */
        #responsive-game { flex-direction: column; justify-content: flex-start; padding-top: 20px; }
        .responsive-container { width: 90%; max-width: 700px; height: 70%; display: flex; justify-content: center; align-items: center; background: #37474f; border-radius: 10px; padding: 20px; border: 10px solid #263238; }
        .screen { width: 100%; height: 100%; background: #cfd8dc; border-radius: 5px; display: grid; grid-template-areas: "header header" "sidebar main" "footer footer"; grid-template-rows: 60px 1fr 50px; grid-template-columns: 1fr 3fr; gap: 10px; padding: 10px; transition: all 0.5s ease; }
        .screen.tablet { grid-template-areas: "header" "main" "sidebar" "footer"; grid-template-rows: 60px 1fr 80px 50px; grid-template-columns: 1fr; }
        .screen.mobile { grid-template-areas: "header" "main" "sidebar" "footer"; grid-template-rows: 50px 1fr 1fr 50px; grid-template-columns: 1fr; }
        .content-block { border-radius: 5px; background-color: var(--primary-color); color: white; display: flex; justify-content: center; align-items: center; font-size: 1.1em; font-weight: bold; }
        .header { grid-area: header; }
        .sidebar { grid-area: sidebar; background-color: var(--secondary-color); }
        .main { grid-area: main; }
        .footer { grid-area: footer; }
        #responsive-game .controls { margin-top: 20px; }
        #responsive-game .control-button { margin: 0 10px; padding: 10px 20px; font-size: 1em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>adapt</h1>
            <p class="pronunciation">[əˈdæpt]</p>
            <div class="details">
                <p><strong>词性：</strong> v. (使)适应；修改，改编</p>
                <p><strong>词源:</strong> ad-(朝向) + apt(适合) → 使朝向适合</p>
                <p><strong>含义：</strong><br>1. 为了新的用途或目的而修改。<br>2. 使自己适应新的环境。</p>
                <div class="example">
                    <p><strong>例句1:</strong> We must adapt to the new market conditions.</p>
                    <p><strong>翻译1:</strong> 我们必须适应新的市场环境。</p>
                    <p><strong>例句2:</strong> The novel was adapted for television.</p>
                    <p><strong>翻译2:</strong> 这本小说被改编成了电视剧。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>词根词缀动画</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="ad-anim">词缀: ad- (朝向)</button>
                    <button class="morpheme-btn" data-activity="apt-anim">词根: apt (适合)</button>
                </div>
                <h3 style="margin-top: 20px;">整词互动</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="shifter-game">变形记</button>
                    <button class="morpheme-btn" data-activity="chameleon-game">变色龙伪装</button>
                    <button class="morpheme-btn" data-activity="circuit-game">电路适配</button>
                    <button class="morpheme-btn" data-activity="responsive-game">响应式设计</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p>点击左侧按钮，体验"适应"的智慧。</p></div>
            
            <div id="shifter-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="shifter-object"></div>
                    <div id="target-hole"></div>
                    <div id="status-text" class="status-text"></div>
                </div>
                <button class="control-button" id="adapt-btn">适应</button>
            </div>
            
            <div id="chameleon-game" class="activity-wrapper">
                <div class="game-container"><canvas id="chameleon-canvas"></canvas></div>
            </div>

            <div id="circuit-game" class="activity-wrapper">
                <div class="game-container circuit-container">
                    <div id="power-source" class="circuit-element">⚡️<br>12V</div>
                    <div id="drop-zone"></div>
                    <div id="device" class="circuit-element">💡<br>5V</div>
                    <div id="adapter" class="circuit-element adapter">适配器</div>
                    <div id="circuit-status" class="status-text">请拖动适配器连接电路</div>
                </div>
            </div>

            <div id="ad-anim" class="activity-wrapper">
                <div class="game-container ad-container">
                    <div class="ad-arrow">→</div>
                    <div class="ad-target"></div>
                </div>
                <button class="control-button" id="play-ad-anim">播放</button>
            </div>

            <div id="apt-anim" class="activity-wrapper">
                <div class="game-container apt-container">
                    <div class="apt-piece"></div>
                    <div class="apt-hole"></div>
                </div>
                <button class="control-button" id="play-apt-anim">播放</button>
            </div>

            <div id="responsive-game" class="activity-wrapper">
                <div class="responsive-container">
                    <div class="screen">
                        <div class="content-block header">Header</div>
                        <div class="content-block sidebar">Sidebar</div>
                        <div class="content-block main">Main</div>
                        <div class="content-block footer">Footer</div>
                    </div>
                </div>
                <div class="controls">
                    <button class="control-button" data-mode="desktop">桌面</button>
                    <button class="control-button" data-mode="tablet">平板</button>
                    <button class="control-button" data-mode="mobile">手机</button>
                </div>
            </div>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');
        let currentCleanup = null;

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                if (currentCleanup) currentCleanup();
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                const targetId = btn.dataset.activity;
                document.getElementById(targetId)?.classList.add('active');
                
                if (targetId === 'shifter-game') currentCleanup = setupShifterGame();
                else if (targetId === 'chameleon-game') currentCleanup = setupChameleonGame();
                else if (targetId === 'circuit-game') currentCleanup = setupCircuitGame();
                else if (targetId === 'ad-anim') currentCleanup = setupAdAnimation();
                else if (targetId === 'apt-anim') currentCleanup = setupAptAnimation();
                else if (targetId === 'responsive-game') currentCleanup = setupResponsiveDesign();
                else currentCleanup = null;
            });
        });

        function setupShifterGame() {
            const btn = document.getElementById('adapt-btn');
            const shifter = document.getElementById('shifter-object');
            const target = document.getElementById('target-hole');
            const status = document.getElementById('status-text');
            let isAdapted = false;
            let tl;

            function animate(adapt) {
                if(tl) tl.kill();
                btn.disabled = true;
                const targetRect = target.getBoundingClientRect();
                const shifterRect = shifter.getBoundingClientRect();
                const targetX = targetRect.left - shifterRect.left;

                if (adapt) {
                    tl = gsap.timeline({ onComplete: () => btn.disabled = false });
                    tl.to(shifter, { duration: 1, clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)', ease: 'power4.inOut'})
                      .to(shifter, { duration: 1, x: targetX, ease: 'bounce.out' }, '+=0.2')
                      .to(status, { text: '适配成功!', opacity: 1, duration: 0.5}, '-=0.5')
                      .to(status, { opacity: 0, duration: 0.5, delay: 1});
                } else {
                    tl = gsap.timeline({ onComplete: () => btn.disabled = false });
                    tl.to(shifter, { duration: 1, x: 0, ease: 'power4.inOut'})
                      .to(shifter, { duration: 1, clipPath: 'circle(50% at 50% 50%)', ease: 'power4.inOut'});
                }
            }

            btn.onclick = () => {
                isAdapted = !isAdapted;
                animate(isAdapted);
                btn.textContent = isAdapted ? '重置' : '适应';
            };
            
            return () => { if(tl) tl.kill(); };
        }

        function setupChameleonGame() {
            const canvas = document.getElementById('chameleon-canvas');
            if (!canvas) return null;
            const ctx = canvas.getContext('2d');
            let isDragging = false;
            const bgColors = ['#81c784', '#a1887f', '#64b5f6', '#fff176'];
            const chameleon = { x: 100, y: 100, radius: 30, color: '#ffffff' };
            
            function init() {
                const container = canvas.parentElement;
                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;
                chameleon.x = canvas.width / 2;
                chameleon.y = canvas.height / 2;
                draw();
            }

            function draw() {
                // Draw background
                const stripeHeight = canvas.height / bgColors.length;
                bgColors.forEach((color, i) => {
                    ctx.fillStyle = color;
                    ctx.fillRect(0, i * stripeHeight, canvas.width, stripeHeight);
                });

                // Get adaptive color
                const pixel = ctx.getImageData(chameleon.x, chameleon.y, 1, 1).data;
                chameleon.color = `rgb(${pixel[0]}, ${pixel[1]}, ${pixel[2]})`;

                // Draw chameleon
                ctx.fillStyle = chameleon.color;
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(chameleon.x, chameleon.y, chameleon.radius, 0, Math.PI * 2);
                ctx.fill();
                ctx.stroke();

                // Draw eye
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(chameleon.x + 10, chameleon.y - 10, 8, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = 'black';
                ctx.beginPath();
                ctx.arc(chameleon.x + 12, chameleon.y - 10, 4, 0, Math.PI * 2);
                ctx.fill();
            }
            
            function handleMouseDown(e) {
                const rect = canvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;
                if (Math.hypot(mouseX - chameleon.x, mouseY - chameleon.y) < chameleon.radius) {
                    isDragging = true;
                }
            }
            function handleMouseMove(e) {
                if (isDragging) {
                    const rect = canvas.getBoundingClientRect();
                    chameleon.x = e.clientX - rect.left;
                    chameleon.y = e.clientY - rect.top;
                    draw();
                }
            }
            function handleMouseUp() {
                isDragging = false;
            }

            canvas.addEventListener('mousedown', handleMouseDown);
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('mouseup', handleMouseUp);
            canvas.addEventListener('mouseleave', handleMouseUp);
            
            init();
            
            return () => {
                canvas.removeEventListener('mousedown', handleMouseDown);
                canvas.removeEventListener('mousemove', handleMouseMove);
                canvas.removeEventListener('mouseup', handleMouseUp);
                canvas.removeEventListener('mouseleave', handleMouseUp);
            };
        }

        function setupCircuitGame() {
            const adapter = document.querySelector('#circuit-game .adapter');
            const dropZone = document.getElementById('drop-zone');
            const device = document.getElementById('device');
            const status = document.querySelector('#circuit-game .status-text');
            let isDragging = false;
            let offsetX, offsetY;
            let originalX, originalY;

            function onDragStart(e) {
                isDragging = true;
                adapter.style.cursor = 'grabbing';
                const rect = adapter.getBoundingClientRect();
                originalX = rect.left;
                originalY = rect.top;
                offsetX = e.clientX - rect.left;
                offsetY = e.clientY - rect.top;
                document.addEventListener('mousemove', onDrag);
                document.addEventListener('mouseup', onDragEnd);
            }

            function onDrag(e) {
                if (!isDragging) return;
                adapter.style.left = `${e.clientX - offsetX}px`;
                adapter.style.top = `${e.clientY - offsetY}px`;
            }

            function onDragEnd(e) {
                isDragging = false;
                adapter.style.cursor = 'grab';
                document.removeEventListener('mousemove', onDrag);
                document.removeEventListener('mouseup', onDragEnd);

                const adapterRect = adapter.getBoundingClientRect();
                const dropZoneRect = dropZone.getBoundingClientRect();

                // Check for overlap
                const overlap = !(adapterRect.right < dropZoneRect.left || 
                                  adapterRect.left > dropZoneRect.right || 
                                  adapterRect.bottom < dropZoneRect.top || 
                                  adapterRect.top > dropZoneRect.bottom);

                if (overlap) {
                    gsap.to(adapter, {
                        x: dropZoneRect.left - originalX + (dropZoneRect.width - adapterRect.width) / 2,
                        y: dropZoneRect.top - originalY + (dropZoneRect.height - adapterRect.height) / 2,
                        duration: 0.3,
                        ease: 'power2.out',
                        onComplete: () => {
                            adapter.style.pointerEvents = 'none';
                            device.classList.add('active');
                            gsap.to(status, { text: '适配成功! 灯亮了!', opacity: 1, duration: 0.5 });
                        }
                    });
                } else {
                    gsap.to(adapter, {
                        x: 0,
                        y: 0,
                        duration: 0.5,
                        ease: 'bounce.out'
                    });
                }
            }
            
            adapter.addEventListener('mousedown', onDragStart);

            return () => {
                adapter.removeEventListener('mousedown', onDragStart);
                device.classList.remove('active');
                adapter.style.pointerEvents = 'auto';
                gsap.set(adapter, { x: 0, y: 0 });
                gsap.set(status, { text: '请拖动适配器连接电路', opacity: 1 });
            };
        }

        function setupAdAnimation() {
            const btn = document.getElementById('play-ad-anim');
            const arrow = document.querySelector('.ad-arrow');
            let tl;

            const onClick = () => {
                if (tl && tl.isActive()) return;
                btn.disabled = true;
                gsap.set(arrow, { x: 0 });
                tl = gsap.fromTo(arrow, {x: 0}, {
                    x: () => document.querySelector('.ad-container').clientWidth * 0.6 - arrow.clientWidth,
                    duration: 1.5,
                    ease: 'power2.inOut',
                    onComplete: () => { btn.disabled = false; }
                });
            };
            
            btn.addEventListener('click', onClick);

            return () => {
                if(tl) tl.kill();
                btn.removeEventListener('click', onClick);
            }
        }

        function setupAptAnimation() {
            const btn = document.getElementById('play-apt-anim');
            const piece = document.querySelector('.apt-piece');
            const hole = document.querySelector('.apt-hole');
            let tl;

            const onClick = () => {
                if (tl && tl.isActive()) return;
                btn.disabled = true;
                const holeRect = hole.getBoundingClientRect();
                const pieceRect = piece.getBoundingClientRect();
                const targetX = holeRect.left - pieceRect.left;
                
                gsap.set(piece, { x: 0, rotation: 0 });

                tl = gsap.to(piece, {
                    x: targetX,
                    rotation: 360,
                    duration: 1.5,
                    ease: 'back.inOut(1.7)',
                    onComplete: () => { btn.disabled = false; }
                });
            };
            
            btn.addEventListener('click', onClick);

            return () => {
                if(tl) tl.kill();
                btn.removeEventListener('click', onClick);
            }
        }

        function setupResponsiveDesign() {
            const controls = document.querySelector('#responsive-game .controls');
            const screen = document.querySelector('.screen');
            const responsiveContainer = document.querySelector('.responsive-container');
            let tl;

            const onClick = (e) => {
                const button = e.target.closest('[data-mode]');
                if (!button) return;
                if (tl && tl.isActive()) tl.kill();

                const mode = button.dataset.mode;
                
                // Remove old classes
                screen.classList.remove('tablet', 'mobile');

                let targetWidth;
                if (mode === 'desktop') {
                    targetWidth = '90%';
                } else if (mode === 'tablet') {
                    targetWidth = '60%';
                    screen.classList.add('tablet');
                } else if (mode === 'mobile') {
                    targetWidth = '35%';
                    screen.classList.add('mobile');
                }
                
                tl = gsap.to(responsiveContainer, { width: targetWidth, duration: 0.8, ease: 'back.inOut(1.7)' });
            };
            
            controls.addEventListener('click', onClick);

            return () => {
                if (tl) tl.kill();
                controls.removeEventListener('click', onClick);
                screen.classList.remove('tablet', 'mobile');
                gsap.set(responsiveContainer, {width: '90%'});
            };
        }
    });
    </script>
</body>
</html> 