<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库设计阶段 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transform: translateY(50px);
            opacity: 0;
            animation: slideUp 1s ease-out 0.6s forwards;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .stages-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .stage-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 30px 20px;
            text-align: center;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: scale(0.9);
            opacity: 0;
            position: relative;
            overflow: hidden;
        }

        .stage-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s;
            opacity: 0;
        }

        .stage-card:hover::before {
            animation: shine 0.6s ease-in-out;
        }

        .stage-card:hover {
            transform: scale(1.05) translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .stage-card.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            transform: scale(1.1);
        }

        .stage-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .stage-name {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .stage-desc {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            cursor: pointer;
        }

        .explanation {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            backdrop-filter: blur(10px);
        }

        .explanation h3 {
            color: #4facfe;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
        }

        .answer-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            text-align: center;
        }

        .correct-answer {
            color: #27ae60;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .game-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
        }

        .game-section h3 {
            color: #8e44ad;
            margin-bottom: 15px;
            text-align: center;
        }

        .game-container {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin: 20px 0;
        }

        .documents-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .document-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            cursor: grab;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: bold;
            user-select: none;
        }

        .document-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .document-item:active {
            cursor: grabbing;
            transform: scale(0.95);
        }

        .document-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }

        .drop-zones {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .drop-zone {
            border: 2px dashed #bdc3c7;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
            min-height: 120px;
        }

        .drop-zone h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 0.9rem;
        }

        .drop-zone.drag-over {
            border-color: #3498db;
            background: rgba(52, 152, 219, 0.1);
            transform: scale(1.05);
        }

        .drop-zone.correct {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .drop-zone.incorrect {
            border-color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
            animation: shake 0.5s ease-in-out;
        }

        .drop-area {
            min-height: 80px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            align-items: center;
            justify-content: center;
        }

        .dropped-document {
            background: #27ae60;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 0.8rem;
            margin: 2px 0;
        }

        .game-score {
            text-align: center;
            margin-top: 20px;
        }

        .game-score span {
            font-size: 1.2rem;
            font-weight: bold;
            color: #8e44ad;
            margin-right: 20px;
        }

        .game-score button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .game-score button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 数据库设计阶段学习</h1>
            <p>通过互动动画理解数据库设计的四个阶段</p>
        </div>

        <div class="question-card">
            <div class="question-title">
                📝 题目：在数据库设计的需求分析阶段应当形成（ ），这些文档可以作为（ ）阶段的设计依据。
            </div>
            
            <div class="stages-container">
                <div class="stage-card" data-stage="1">
                    <div class="stage-number">1️⃣</div>
                    <div class="stage-name">需求分析</div>
                    <div class="stage-desc">分析用户需求，形成文档</div>
                </div>
                <div class="stage-card" data-stage="2">
                    <div class="stage-number">2️⃣</div>
                    <div class="stage-name">概念结构设计</div>
                    <div class="stage-desc">建立概念模型</div>
                </div>
                <div class="stage-card" data-stage="3">
                    <div class="stage-number">3️⃣</div>
                    <div class="stage-name">逻辑结构设计</div>
                    <div class="stage-desc">转换为逻辑模型</div>
                </div>
                <div class="stage-card" data-stage="4">
                    <div class="stage-number">4️⃣</div>
                    <div class="stage-name">物理结构设计</div>
                    <div class="stage-desc">确定物理存储</div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas"></canvas>
            </div>
        </div>

        <div class="explanation">
            <h3>💡 知识点解析</h3>
            <p>数据库设计是一个<span class="highlight">系统化的过程</span>，分为四个主要阶段：</p>
            <p><strong>1. 需求分析阶段：</strong>这是数据库设计的起点，需要深入了解用户的业务需求，形成<span class="highlight">需求说明文档、数据字典和数据流程图</span>。</p>
            <p><strong>2. 概念结构设计：</strong>基于需求分析的结果，建立<span class="highlight">概念模型</span>（如E-R图），这个阶段不依赖于具体的数据库管理系统。</p>
            <p><strong>3. 逻辑结构设计：</strong>将概念模型转换为<span class="highlight">逻辑模型</span>（如关系模型），确定数据的逻辑结构。</p>
            <p><strong>4. 物理结构设计：</strong>确定数据在存储设备上的<span class="highlight">物理存储结构</span>和存取方法。</p>
        </div>

        <div class="game-section">
            <h3>🎮 互动小游戏：文档配对</h3>
            <p>将左侧的文档拖拽到正确的设计阶段！</p>
            <div class="game-container">
                <div class="documents-list">
                    <div class="document-item" draggable="true" data-stage="0">📋 需求说明文档</div>
                    <div class="document-item" draggable="true" data-stage="1">🔗 E-R图</div>
                    <div class="document-item" draggable="true" data-stage="0">📊 数据字典</div>
                    <div class="document-item" draggable="true" data-stage="2">🗂️ 关系模式</div>
                    <div class="document-item" draggable="true" data-stage="0">📈 数据流程图</div>
                    <div class="document-item" draggable="true" data-stage="3">💾 存储结构</div>
                </div>
                <div class="drop-zones">
                    <div class="drop-zone" data-stage="0">
                        <h4>需求分析</h4>
                        <div class="drop-area"></div>
                    </div>
                    <div class="drop-zone" data-stage="1">
                        <h4>概念结构设计</h4>
                        <div class="drop-area"></div>
                    </div>
                    <div class="drop-zone" data-stage="2">
                        <h4>逻辑结构设计</h4>
                        <div class="drop-area"></div>
                    </div>
                    <div class="drop-zone" data-stage="3">
                        <h4>物理结构设计</h4>
                        <div class="drop-area"></div>
                    </div>
                </div>
            </div>
            <div class="game-score">
                <span id="score">得分: 0</span>
                <button id="resetGame">重新开始</button>
            </div>
        </div>

        <div class="answer-section">
            <div class="correct-answer">✅ 正确答案：B - 概念结构设计</div>
            <p>需求分析阶段形成的文档（需求说明文档、数据字典、数据流程图）是概念结构设计阶段的重要依据！</p>
        </div>
    </div>

    <script>
        // 动画初始化
        function initAnimations() {
            const stageCards = document.querySelectorAll('.stage-card');
            stageCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.animation = `slideUp 0.6s ease-out forwards`;
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1)';
                }, index * 200 + 1000);
            });
        }

        // Canvas 游戏动画
        function initCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            // 设置canvas尺寸
            function resizeCanvas() {
                canvas.width = canvas.offsetWidth;
                canvas.height = canvas.offsetHeight;
            }
            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);

            let currentStage = 0;
            let animationFrame = 0;
            let particles = [];
            let gameState = 'playing'; // playing, completed

            const stages = [
                {
                    name: '需求分析',
                    color: '#e74c3c',
                    docs: ['需求说明文档', '数据字典', '数据流程图'],
                    description: '分析用户需求，收集业务信息'
                },
                {
                    name: '概念结构设计',
                    color: '#3498db',
                    docs: ['E-R图', '概念模型', '实体关系'],
                    description: '建立概念模型，设计E-R图'
                },
                {
                    name: '逻辑结构设计',
                    color: '#2ecc71',
                    docs: ['关系模式', '逻辑模型', '数据表结构'],
                    description: '转换为关系模型，设计表结构'
                },
                {
                    name: '物理结构设计',
                    color: '#f39c12',
                    docs: ['存储结构', '访问方法', '索引设计'],
                    description: '确定物理存储和访问方式'
                }
            ];

            // 粒子系统
            class Particle {
                constructor(x, y, color) {
                    this.x = x;
                    this.y = y;
                    this.vx = (Math.random() - 0.5) * 4;
                    this.vy = (Math.random() - 0.5) * 4;
                    this.color = color;
                    this.life = 1;
                    this.decay = 0.02;
                }

                update() {
                    this.x += this.vx;
                    this.y += this.vy;
                    this.life -= this.decay;
                    this.vy += 0.1; // 重力
                }

                draw() {
                    ctx.save();
                    ctx.globalAlpha = this.life;
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, 3, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.restore();
                }
            }

            function createParticles(x, y, color, count = 10) {
                for (let i = 0; i < count; i++) {
                    particles.push(new Particle(x, y, color));
                }
            }

            function drawFlowChart() {
                const stageWidth = canvas.width / 4;
                const stageHeight = 120;
                const startY = 50;

                stages.forEach((stage, index) => {
                    const x = index * stageWidth + stageWidth / 2;
                    const y = startY + stageHeight / 2;
                    const isActive = index === currentStage;
                    const isCompleted = index < currentStage;

                    // 绘制连接线
                    if (index < stages.length - 1) {
                        ctx.strokeStyle = isCompleted ? '#27ae60' : '#bdc3c7';
                        ctx.lineWidth = 4;
                        ctx.beginPath();
                        ctx.moveTo(x + 60, y);
                        ctx.lineTo(x + stageWidth - 60, y);
                        ctx.stroke();

                        // 箭头
                        const arrowX = x + stageWidth - 60;
                        ctx.beginPath();
                        ctx.moveTo(arrowX, y);
                        ctx.lineTo(arrowX - 10, y - 5);
                        ctx.moveTo(arrowX, y);
                        ctx.lineTo(arrowX - 10, y + 5);
                        ctx.stroke();
                    }

                    // 绘制阶段圆圈
                    ctx.beginPath();
                    ctx.arc(x, y, 50, 0, 2 * Math.PI);

                    if (isActive) {
                        const pulse = Math.sin(animationFrame * 0.1) * 0.2 + 1;
                        ctx.save();
                        ctx.scale(pulse, pulse);
                        ctx.translate((x * (1 - pulse)) / pulse, (y * (1 - pulse)) / pulse);
                    }

                    ctx.fillStyle = isCompleted ? '#27ae60' : (isActive ? stage.color : '#ecf0f1');
                    ctx.fill();

                    if (isActive) {
                        ctx.restore();
                    }

                    // 绘制阶段编号
                    ctx.fillStyle = isCompleted || isActive ? 'white' : '#7f8c8d';
                    ctx.font = 'bold 20px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(index + 1, x, y + 7);

                    // 绘制阶段名称
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(stage.name, x, y + 80);

                    // 绘制文档（仅当前阶段）
                    if (isActive) {
                        stage.docs.forEach((doc, docIndex) => {
                            const docY = y + 120 + docIndex * 25;
                            ctx.fillStyle = 'rgba(52, 152, 219, 0.1)';
                            ctx.fillRect(x - 60, docY - 10, 120, 20);
                            ctx.fillStyle = '#3498db';
                            ctx.font = '12px Microsoft YaHei';
                            ctx.fillText(doc, x, docY + 3);
                        });
                    }
                });

                // 绘制当前阶段描述
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(stages[currentStage].description, canvas.width / 2, canvas.height - 40);
            }

            function updateParticles() {
                particles = particles.filter(particle => {
                    particle.update();
                    particle.draw();
                    return particle.life > 0;
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                drawFlowChart();
                updateParticles();

                animationFrame++;
                requestAnimationFrame(animate);
            }

            // 点击切换阶段
            canvas.addEventListener('click', (e) => {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 检测点击的阶段
                const stageWidth = canvas.width / 4;
                const clickedStage = Math.floor(x / stageWidth);

                if (clickedStage >= 0 && clickedStage < 4) {
                    currentStage = clickedStage;

                    // 创建点击特效
                    createParticles(x, y, stages[currentStage].color, 15);

                    // 更新卡片状态
                    document.querySelectorAll('.stage-card').forEach((card, index) => {
                        card.classList.toggle('active', index === currentStage);
                    });
                }
            });

            animate();
        }

        // 卡片点击事件
        function initCardEvents() {
            const stageCards = document.querySelectorAll('.stage-card');
            stageCards.forEach((card, index) => {
                card.addEventListener('click', () => {
                    stageCards.forEach(c => c.classList.remove('active'));
                    card.classList.add('active');

                    // 更新canvas显示
                    currentStage = index;
                });
            });
        }

        // 拖拽游戏逻辑
        function initDragGame() {
            let score = 0;
            let draggedElement = null;

            const documents = document.querySelectorAll('.document-item');
            const dropZones = document.querySelectorAll('.drop-zone');
            const scoreElement = document.getElementById('score');
            const resetButton = document.getElementById('resetGame');

            // 拖拽开始
            documents.forEach(doc => {
                doc.addEventListener('dragstart', (e) => {
                    draggedElement = e.target;
                    e.target.classList.add('dragging');
                });

                doc.addEventListener('dragend', (e) => {
                    e.target.classList.remove('dragging');
                    draggedElement = null;
                });
            });

            // 拖拽区域事件
            dropZones.forEach(zone => {
                zone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    zone.classList.add('drag-over');
                });

                zone.addEventListener('dragleave', () => {
                    zone.classList.remove('drag-over');
                });

                zone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    zone.classList.remove('drag-over');

                    if (draggedElement) {
                        const docStage = parseInt(draggedElement.dataset.stage);
                        const zoneStage = parseInt(zone.dataset.stage);
                        const dropArea = zone.querySelector('.drop-area');

                        if (docStage === zoneStage) {
                            // 正确匹配
                            score += 10;
                            zone.classList.add('correct');

                            // 创建已放置的文档元素
                            const droppedDoc = document.createElement('div');
                            droppedDoc.className = 'dropped-document';
                            droppedDoc.textContent = draggedElement.textContent;
                            dropArea.appendChild(droppedDoc);

                            // 隐藏原文档
                            draggedElement.style.display = 'none';

                            // 成功动画
                            droppedDoc.style.transform = 'scale(0)';
                            setTimeout(() => {
                                droppedDoc.style.transform = 'scale(1)';
                                droppedDoc.style.transition = 'transform 0.3s ease';
                            }, 100);

                        } else {
                            // 错误匹配
                            score = Math.max(0, score - 5);
                            zone.classList.add('incorrect');
                            setTimeout(() => {
                                zone.classList.remove('incorrect');
                            }, 500);
                        }

                        scoreElement.textContent = `得分: ${score}`;

                        // 检查游戏完成
                        const remainingDocs = Array.from(documents).filter(doc =>
                            doc.style.display !== 'none'
                        );

                        if (remainingDocs.length === 0) {
                            setTimeout(() => {
                                alert(`🎉 恭喜完成！最终得分: ${score}分\n\n你已经掌握了数据库设计的四个阶段！`);
                            }, 500);
                        }
                    }
                });
            });

            // 重置游戏
            resetButton.addEventListener('click', () => {
                score = 0;
                scoreElement.textContent = `得分: ${score}`;

                // 重置所有文档
                documents.forEach(doc => {
                    doc.style.display = 'block';
                });

                // 清空放置区域
                dropZones.forEach(zone => {
                    zone.classList.remove('correct', 'incorrect');
                    const dropArea = zone.querySelector('.drop-area');
                    dropArea.innerHTML = '';
                });
            });
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initAnimations();
            initCanvas();
            initCardEvents();
            initDragGame();
        });
    </script>
</body>
</html>
