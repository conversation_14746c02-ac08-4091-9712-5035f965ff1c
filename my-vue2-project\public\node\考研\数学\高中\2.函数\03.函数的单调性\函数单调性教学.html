<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函数单调性 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .chapter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .chapter-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .chapter-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .chapter-card:hover::before {
            left: 100%;
        }

        .chapter-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .chapter-number {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .chapter-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .chapter-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .demo-canvas {
            width: 100%;
            height: 200px;
            border-radius: 10px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            margin-bottom: 15px;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chapter-card {
            animation: fadeInUp 0.6s ease-out forwards;
            animation-delay: calc(var(--delay) * 0.1s);
            opacity: 0;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">函数的单调性</h1>
            <p class="subtitle">零基础交互式学习 - 从概念到应用的完整旅程</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="chapter-grid" id="chapterGrid">
            <!-- 章节内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 章节数据
        const chapters = [
            {
                number: "01",
                title: "单调性的概念",
                description: "什么是函数的单调性？通过直观的图形和动画理解递增和递减的概念。",
                demoType: "concept"
            },
            {
                number: "02", 
                title: "定义法证明函数单调性",
                description: "学习如何用数学定义严格证明函数的单调性，掌握证明的基本步骤。",
                demoType: "proof"
            },
            {
                number: "03",
                title: "一次、反比例函数的单调性", 
                description: "探索最基本函数类型的单调性规律，建立直观认识。",
                demoType: "linear"
            },
            {
                number: "04",
                title: "二次函数的单调性",
                description: "理解抛物线的单调性特点，学会分析对称轴对单调性的影响。",
                demoType: "quadratic"
            },
            {
                number: "05",
                title: "复合函数的概念",
                description: "什么是复合函数？通过动画演示函数的复合过程。",
                demoType: "composite"
            },
            {
                number: "06",
                title: "简单复合函数的单调性",
                description: "学习复合函数单调性的判断规律：同增异减。",
                demoType: "compositeMonotone"
            },
            {
                number: "07",
                title: "单调性的加减性质",
                description: "探索函数加减运算对单调性的影响规律。",
                demoType: "arithmetic"
            },
            {
                number: "08",
                title: "对勾函数的单调性",
                description: "分析形如 f(x) = x + a/x 的对勾函数的单调性特点。",
                demoType: "hook"
            },
            {
                number: "09",
                title: "分式函数的单调性",
                description: "学习分式函数单调性的分析方法和技巧。",
                demoType: "rational"
            },
            {
                number: "10",
                title: "抽象函数的单调性",
                description: "处理没有具体表达式的抽象函数的单调性问题。",
                demoType: "abstract"
            },
            {
                number: "11",
                title: "单调性与不等式",
                description: "利用函数单调性解决不等式问题的方法和技巧。",
                demoType: "inequality"
            },
            {
                number: "12",
                title: "结合函数方程的单调性综合题",
                description: "综合运用单调性知识解决复杂的函数方程问题。",
                demoType: "comprehensive"
            }
        ];

        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 15; i++) {
                const circle = document.createElement('div');
                circle.className = 'floating-circle';
                circle.style.width = Math.random() * 60 + 20 + 'px';
                circle.style.height = circle.style.width;
                circle.style.left = Math.random() * 100 + '%';
                circle.style.top = Math.random() * 100 + '%';
                circle.style.animationDelay = Math.random() * 6 + 's';
                circle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(circle);
            }
        }

        // 创建章节卡片
        function createChapterCards() {
            const grid = document.getElementById('chapterGrid');
            
            chapters.forEach((chapter, index) => {
                const card = document.createElement('div');
                card.className = 'chapter-card';
                card.style.setProperty('--delay', index);
                
                card.innerHTML = `
                    <div class="chapter-number">${chapter.number}</div>
                    <h3 class="chapter-title">${chapter.title}</h3>
                    <p class="chapter-description">${chapter.description}</p>
                    <canvas class="demo-canvas" id="canvas${index}"></canvas>
                    <button class="interactive-btn" onclick="startDemo(${index})">开始演示</button>
                    <button class="interactive-btn" onclick="showExplanation(${index})">详细解释</button>
                `;
                
                grid.appendChild(card);
                
                // 初始化画布演示
                setTimeout(() => {
                    initCanvasDemo(index, chapter.demoType);
                }, 100 * index);
            });
        }

        // 初始化画布演示
        function initCanvasDemo(index, demoType) {
            const canvas = document.getElementById(`canvas${index}`);
            const ctx = canvas.getContext('2d');
            
            // 设置画布尺寸
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            
            // 根据演示类型绘制不同内容
            switch(demoType) {
                case 'concept':
                    drawConceptDemo(ctx, canvas.width, canvas.height);
                    break;
                case 'linear':
                    drawLinearDemo(ctx, canvas.width, canvas.height);
                    break;
                case 'quadratic':
                    drawQuadraticDemo(ctx, canvas.width, canvas.height);
                    break;
                default:
                    drawDefaultDemo(ctx, canvas.width, canvas.height, index);
            }
        }

        // 绘制概念演示
        function drawConceptDemo(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);
            
            // 绘制坐标轴
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(50, height - 50);
            ctx.lineTo(width - 50, height - 50);
            ctx.moveTo(50, 50);
            ctx.lineTo(50, height - 50);
            ctx.stroke();
            
            // 绘制递增函数
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(60, height - 60);
            ctx.quadraticCurveTo(width/2, height/2, width - 60, 60);
            ctx.stroke();
            
            // 添加标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('递增函数', width - 120, 80);
        }

        // 绘制线性函数演示
        function drawLinearDemo(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);
            
            // 绘制坐标轴
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(50, height - 50);
            ctx.lineTo(width - 50, height - 50);
            ctx.moveTo(50, 50);
            ctx.lineTo(50, height - 50);
            ctx.stroke();
            
            // 绘制正比例函数
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(60, height - 60);
            ctx.lineTo(width - 60, 60);
            ctx.stroke();
            
            // 绘制反比例函数
            ctx.strokeStyle = '#764ba2';
            ctx.lineWidth = 3;
            ctx.beginPath();
            for(let x = 60; x < width - 60; x += 2) {
                const y = height - 50 - 3000 / (x - 50);
                if(y > 50 && y < height - 50) {
                    if(x === 60) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
            }
            ctx.stroke();
        }

        // 绘制二次函数演示
        function drawQuadraticDemo(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);
            
            // 绘制坐标轴
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(50, height - 50);
            ctx.lineTo(width - 50, height - 50);
            ctx.moveTo(50, 50);
            ctx.lineTo(50, height - 50);
            ctx.stroke();
            
            // 绘制抛物线
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            const centerX = width / 2;
            for(let x = 60; x < width - 60; x += 2) {
                const normalizedX = (x - centerX) / 50;
                const y = height - 100 - normalizedX * normalizedX * 30;
                if(x === 60) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.stroke();
            
            // 标记对称轴
            ctx.strokeStyle = '#ff6b6b';
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(centerX, 60);
            ctx.lineTo(centerX, height - 60);
            ctx.stroke();
            ctx.setLineDash([]);
        }

        // 绘制默认演示
        function drawDefaultDemo(ctx, width, height, index) {
            ctx.clearRect(0, 0, width, height);
            
            // 绘制坐标轴
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(50, height - 50);
            ctx.lineTo(width - 50, height - 50);
            ctx.moveTo(50, 50);
            ctx.lineTo(50, height - 50);
            ctx.stroke();
            
            // 绘制示例函数
            ctx.strokeStyle = `hsl(${index * 30}, 70%, 60%)`;
            ctx.lineWidth = 3;
            ctx.beginPath();
            for(let x = 60; x < width - 60; x += 2) {
                const t = (x - 60) / (width - 120);
                const y = height - 60 - Math.sin(t * Math.PI * 2 + index) * 40;
                if(x === 60) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.stroke();
        }

        // 开始演示
        function startDemo(index) {
            const canvas = document.getElementById(`canvas${index}`);
            const ctx = canvas.getContext('2d');
            const demoType = chapters[index].demoType;

            // 根据不同类型执行不同的动画演示
            switch(demoType) {
                case 'concept':
                    animateConceptDemo(ctx, canvas.width, canvas.height);
                    break;
                case 'proof':
                    animateProofDemo(ctx, canvas.width, canvas.height);
                    break;
                case 'linear':
                    animateLinearDemo(ctx, canvas.width, canvas.height);
                    break;
                case 'quadratic':
                    animateQuadraticDemo(ctx, canvas.width, canvas.height);
                    break;
                case 'composite':
                    animateCompositeDemo(ctx, canvas.width, canvas.height);
                    break;
                case 'hook':
                    animateHookDemo(ctx, canvas.width, canvas.height);
                    break;
                default:
                    animateDefaultDemo(ctx, canvas.width, canvas.height, index);
            }

            // 更新进度条
            updateProgress(index);
        }

        // 概念演示动画
        function animateConceptDemo(ctx, width, height) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, width, height);

                // 绘制坐标轴
                drawAxes(ctx, width, height);

                // 绘制递增函数动画
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();

                const progress = Math.min(frame / 100, 1);
                for(let i = 0; i <= progress * 100; i++) {
                    const x = 60 + i * (width - 120) / 100;
                    const y = height - 60 - i * (height - 120) / 100;
                    if(i === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.stroke();

                // 添加动态点
                if(progress > 0) {
                    const currentX = 60 + progress * (width - 120);
                    const currentY = height - 60 - progress * (height - 120);

                    ctx.fillStyle = '#ff6b6b';
                    ctx.beginPath();
                    ctx.arc(currentX, currentY, 6, 0, Math.PI * 2);
                    ctx.fill();

                    // 添加标签
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('单调递增', currentX + 10, currentY - 10);
                }

                frame++;
                if(frame < 150) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 证明演示动画
        function animateProofDemo(ctx, width, height) {
            let step = 0;
            const steps = [
                '取 x₁ < x₂',
                '计算 f(x₂) - f(x₁)',
                '判断符号',
                '得出结论'
            ];

            const animate = () => {
                ctx.clearRect(0, 0, width, height);
                drawAxes(ctx, width, height);

                // 绘制函数
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.beginPath();
                for(let x = 60; x < width - 60; x += 2) {
                    const t = (x - 60) / (width - 120);
                    const y = height - 60 - t * t * (height - 120);
                    if(x === 60) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.stroke();

                if(step < steps.length) {
                    // 显示当前步骤
                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText(`步骤 ${step + 1}: ${steps[step]}`, 70, 30);

                    // 根据步骤显示不同内容
                    if(step >= 0) {
                        // 标记两点
                        const x1 = width * 0.3;
                        const x2 = width * 0.7;
                        const y1 = height - 60 - Math.pow((x1 - 60) / (width - 120), 2) * (height - 120);
                        const y2 = height - 60 - Math.pow((x2 - 60) / (width - 120), 2) * (height - 120);

                        ctx.fillStyle = '#ff6b6b';
                        ctx.beginPath();
                        ctx.arc(x1, y1, 5, 0, Math.PI * 2);
                        ctx.fill();
                        ctx.fillText('x₁', x1 - 10, y1 + 20);

                        ctx.beginPath();
                        ctx.arc(x2, y2, 5, 0, Math.PI * 2);
                        ctx.fill();
                        ctx.fillText('x₂', x2 - 10, y2 + 20);
                    }

                    if(step >= 1) {
                        // 显示差值
                        ctx.strokeStyle = '#ff6b6b';
                        ctx.setLineDash([5, 5]);
                        ctx.beginPath();
                        ctx.moveTo(x2, y1);
                        ctx.lineTo(x2, y2);
                        ctx.stroke();
                        ctx.setLineDash([]);

                        ctx.fillStyle = '#ff6b6b';
                        ctx.fillText('f(x₂) - f(x₁)', x2 + 10, (y1 + y2) / 2);
                    }
                }

                setTimeout(() => {
                    step++;
                    if(step <= steps.length) {
                        requestAnimationFrame(animate);
                    }
                }, 1500);
            };
            animate();
        }

        // 线性函数演示动画
        function animateLinearDemo(ctx, width, height) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, width, height);
                drawAxes(ctx, width, height);

                const progress = Math.min(frame / 80, 1);

                // 绘制正比例函数
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();
                const endX = 60 + progress * (width - 120);
                const endY = height - 60 - progress * (height - 120);
                ctx.moveTo(60, height - 60);
                ctx.lineTo(endX, endY);
                ctx.stroke();

                // 添加标签
                if(progress > 0.5) {
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('y = kx (k > 0)', width - 120, 80);
                    ctx.fillText('单调递增', width - 100, 100);
                }

                frame++;
                if(frame < 120) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 二次函数演示动画
        function animateQuadraticDemo(ctx, width, height) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, width, height);
                drawAxes(ctx, width, height);

                const progress = Math.min(frame / 100, 1);
                const centerX = width / 2;

                // 绘制抛物线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();

                let started = false;
                for(let x = 60; x < width - 60; x += 2) {
                    if(x <= 60 + progress * (width - 120)) {
                        const normalizedX = (x - centerX) / 50;
                        const y = height - 100 - normalizedX * normalizedX * 30;
                        if(!started) {
                            ctx.moveTo(x, y);
                            started = true;
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                }
                ctx.stroke();

                // 标记对称轴
                if(progress > 0.3) {
                    ctx.strokeStyle = '#ff6b6b';
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.moveTo(centerX, 60);
                    ctx.lineTo(centerX, height - 60);
                    ctx.stroke();
                    ctx.setLineDash([]);

                    ctx.fillStyle = '#ff6b6b';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('对称轴', centerX + 5, 80);
                }

                // 添加单调性标注
                if(progress > 0.7) {
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('递减', centerX - 80, height - 80);
                    ctx.fillText('递增', centerX + 40, height - 80);
                }

                frame++;
                if(frame < 150) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 复合函数演示动画
        function animateCompositeDemo(ctx, width, height) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, width, height);

                // 分成三个区域显示
                const w = width / 3;

                // 绘制内层函数 u = g(x)
                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText('u = g(x)', 10, 20);

                drawAxes(ctx, w, height, 0);
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.beginPath();
                for(let x = 10; x < w - 10; x += 2) {
                    const t = (x - 10) / (w - 20);
                    const y = height - 30 - t * (height - 60);
                    if(x === 10) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.stroke();

                // 绘制外层函数 y = f(u)
                ctx.fillText('y = f(u)', w + 10, 20);
                drawAxes(ctx, w, height, w);
                ctx.strokeStyle = '#764ba2';
                ctx.lineWidth = 2;
                ctx.beginPath();
                for(let x = w + 10; x < 2*w - 10; x += 2) {
                    const t = (x - w - 10) / (w - 20);
                    const y = height - 30 - Math.sin(t * Math.PI) * (height - 60) / 2;
                    if(x === w + 10) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.stroke();

                // 绘制复合函数 y = f(g(x))
                ctx.fillText('y = f(g(x))', 2*w + 10, 20);
                drawAxes(ctx, w, height, 2*w);
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 2;
                ctx.beginPath();

                const progress = Math.min(frame / 100, 1);
                for(let x = 2*w + 10; x < width - 10; x += 2) {
                    if(x <= 2*w + 10 + progress * (w - 20)) {
                        const t = (x - 2*w - 10) / (w - 20);
                        const u = t; // g(x) = x for simplicity
                        const y = height - 30 - Math.sin(u * Math.PI) * (height - 60) / 2;
                        if(x === 2*w + 10) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();

                frame++;
                if(frame < 150) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 对勾函数演示动画
        function animateHookDemo(ctx, width, height) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, width, height);
                drawAxes(ctx, width, height);

                const progress = Math.min(frame / 120, 1);
                const a = 4; // 参数a

                // 绘制对勾函数 y = x + a/x
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;

                // 正半部分
                ctx.beginPath();
                let started = false;
                for(let x = 0.5; x <= 6; x += 0.1) {
                    if(x <= 0.5 + progress * 5.5) {
                        const canvasX = width/2 + x * 30;
                        const y = x + a/x;
                        const canvasY = height/2 - y * 15;

                        if(canvasX >= 60 && canvasX <= width - 60 && canvasY >= 60 && canvasY <= height - 60) {
                            if(!started) {
                                ctx.moveTo(canvasX, canvasY);
                                started = true;
                            } else {
                                ctx.lineTo(canvasX, canvasY);
                            }
                        }
                    }
                }
                ctx.stroke();

                // 负半部分
                ctx.beginPath();
                started = false;
                for(let x = -6; x <= -0.5; x += 0.1) {
                    if(x >= -6 + progress * 5.5) {
                        const canvasX = width/2 + x * 30;
                        const y = x + a/x;
                        const canvasY = height/2 - y * 15;

                        if(canvasX >= 60 && canvasX <= width - 60 && canvasY >= 60 && canvasY <= height - 60) {
                            if(!started) {
                                ctx.moveTo(canvasX, canvasY);
                                started = true;
                            } else {
                                ctx.lineTo(canvasX, canvasY);
                            }
                        }
                    }
                }
                ctx.stroke();

                // 标记极值点
                if(progress > 0.7) {
                    const extremeX1 = width/2 + Math.sqrt(a) * 30;
                    const extremeY1 = height/2 - 2 * Math.sqrt(a) * 15;
                    const extremeX2 = width/2 - Math.sqrt(a) * 30;
                    const extremeY2 = height/2 + 2 * Math.sqrt(a) * 15;

                    ctx.fillStyle = '#ff6b6b';
                    ctx.beginPath();
                    ctx.arc(extremeX1, extremeY1, 5, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.beginPath();
                    ctx.arc(extremeX2, extremeY2, 5, 0, Math.PI * 2);
                    ctx.fill();

                    ctx.fillStyle = '#333';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('极小值', extremeX1 + 10, extremeY1);
                    ctx.fillText('极大值', extremeX2 + 10, extremeY2);
                }

                frame++;
                if(frame < 180) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 默认演示动画
        function animateDefaultDemo(ctx, width, height, index) {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, width, height);
                drawAxes(ctx, width, height);

                const progress = Math.min(frame / 100, 1);

                ctx.strokeStyle = `hsl(${index * 30}, 70%, 60%)`;
                ctx.lineWidth = 3;
                ctx.beginPath();

                for(let x = 60; x < width - 60; x += 2) {
                    if(x <= 60 + progress * (width - 120)) {
                        const t = (x - 60) / (width - 120);
                        const y = height - 60 - Math.sin(t * Math.PI * 2 + index) * 40;
                        if(x === 60) ctx.moveTo(x, y);
                        else ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();

                frame++;
                if(frame < 150) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 绘制坐标轴辅助函数
        function drawAxes(ctx, width, height, offsetX = 0) {
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.beginPath();
            // x轴
            ctx.moveTo(offsetX + 50, height - 50);
            ctx.lineTo(offsetX + width - 50, height - 50);
            // y轴
            ctx.moveTo(offsetX + 50, 50);
            ctx.lineTo(offsetX + 50, height - 50);
            ctx.stroke();

            // 添加箭头
            ctx.fillStyle = '#999';
            ctx.beginPath();
            ctx.moveTo(offsetX + width - 50, height - 50);
            ctx.lineTo(offsetX + width - 60, height - 55);
            ctx.lineTo(offsetX + width - 60, height - 45);
            ctx.fill();

            ctx.beginPath();
            ctx.moveTo(offsetX + 50, 50);
            ctx.lineTo(offsetX + 45, 60);
            ctx.lineTo(offsetX + 55, 60);
            ctx.fill();
        }

        // 显示详细解释
        function showExplanation(index) {
            const chapter = chapters[index];

            // 创建模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 40px;
                border-radius: 20px;
                max-width: 800px;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
                animation: slideIn 0.3s ease;
            `;

            content.innerHTML = getDetailedExplanation(index);

            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 15px;
                right: 20px;
                background: none;
                border: none;
                font-size: 30px;
                cursor: pointer;
                color: #999;
            `;

            closeBtn.onclick = () => document.body.removeChild(modal);
            content.appendChild(closeBtn);
            modal.appendChild(content);
            document.body.appendChild(modal);

            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from { transform: translateY(-50px); opacity: 0; }
                    to { transform: translateY(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        // 获取详细解释内容
        function getDetailedExplanation(index) {
            const explanations = [
                // 01. 单调性的概念
                `<h2>单调性的概念</h2>
                <p><strong>定义：</strong>设函数f(x)在区间I上有定义</p>
                <ul>
                    <li><strong>单调递增：</strong>对于区间I内任意两点x₁ < x₂，都有f(x₁) ≤ f(x₂)</li>
                    <li><strong>严格单调递增：</strong>对于区间I内任意两点x₁ < x₂，都有f(x₁) < f(x₂)</li>
                    <li><strong>单调递减：</strong>对于区间I内任意两点x₁ < x₂，都有f(x₁) ≥ f(x₂)</li>
                    <li><strong>严格单调递减：</strong>对于区间I内任意两点x₁ < x₂，都有f(x₁) > f(x₂)</li>
                </ul>
                <p><strong>几何意义：</strong>函数图像从左到右的变化趋势</p>
                <p><strong>实例：</strong>y = x 在R上严格单调递增</p>`,

                // 02. 定义法证明函数单调性
                `<h2>定义法证明函数单调性</h2>
                <p><strong>证明步骤：</strong></p>
                <ol>
                    <li>在给定区间内任取两点x₁ < x₂</li>
                    <li>计算f(x₁) - f(x₂)或f(x₂) - f(x₁)</li>
                    <li>判断差值的符号</li>
                    <li>得出单调性结论</li>
                </ol>
                <p><strong>示例：</strong>证明f(x) = x² 在[0,+∞)上单调递增</p>
                <p>证明：设0 ≤ x₁ < x₂，则</p>
                <p>f(x₂) - f(x₁) = x₂² - x₁² = (x₂ - x₁)(x₂ + x₁)</p>
                <p>因为x₂ > x₁ ≥ 0，所以x₂ - x₁ > 0，x₂ + x₁ > 0</p>
                <p>因此f(x₂) - f(x₁) > 0，即f(x₂) > f(x₁)</p>
                <p>所以f(x) = x² 在[0,+∞)上严格单调递增</p>`,

                // 03. 一次、反比例函数的单调性
                `<h2>一次、反比例函数的单调性</h2>
                <h3>一次函数 f(x) = kx + b (k ≠ 0)</h3>
                <ul>
                    <li>当k > 0时，函数在R上严格单调递增</li>
                    <li>当k < 0时，函数在R上严格单调递减</li>
                </ul>
                <h3>反比例函数 f(x) = k/x (k ≠ 0)</h3>
                <ul>
                    <li>当k > 0时，函数在(-∞,0)和(0,+∞)上都严格单调递减</li>
                    <li>当k < 0时，函数在(-∞,0)和(0,+∞)上都严格单调递增</li>
                </ul>
                <p><strong>注意：</strong>反比例函数在整个定义域上不具有单调性</p>`,

                // 04. 二次函数的单调性
                `<h2>二次函数的单调性</h2>
                <p><strong>标准形式：</strong>f(x) = ax² + bx + c (a ≠ 0)</p>
                <p><strong>对称轴：</strong>x = -b/(2a)</p>
                <h3>单调性规律：</h3>
                <ul>
                    <li>当a > 0时：
                        <ul>
                            <li>在(-∞, -b/(2a)]上严格单调递减</li>
                            <li>在[-b/(2a), +∞)上严格单调递增</li>
                        </ul>
                    </li>
                    <li>当a < 0时：
                        <ul>
                            <li>在(-∞, -b/(2a)]上严格单调递增</li>
                            <li>在[-b/(2a), +∞)上严格单调递减</li>
                        </ul>
                    </li>
                </ul>
                <p><strong>记忆方法：</strong>开口向上先减后增，开口向下先增后减</p>`,

                // 05. 复合函数的概念
                `<h2>复合函数的概念</h2>
                <p><strong>定义：</strong>设y = f(u)，u = g(x)，如果g(x)的值域与f(u)的定义域有交集，则称y = f(g(x))为复合函数</p>
                <p><strong>记号：</strong>y = f(g(x)) 或 y = (f∘g)(x)</p>
                <p><strong>构成要素：</strong></p>
                <ul>
                    <li>外层函数：f(u)</li>
                    <li>内层函数：g(x)</li>
                    <li>中间变量：u</li>
                </ul>
                <p><strong>示例：</strong></p>
                <ul>
                    <li>y = sin(2x) = sin(u)，其中u = 2x</li>
                    <li>y = (x+1)² = u²，其中u = x+1</li>
                    <li>y = log₂(x²+1) = log₂(u)，其中u = x²+1</li>
                </ul>`,

                // 06. 简单复合函数的单调性
                `<h2>简单复合函数的单调性</h2>
                <p><strong>判断法则：</strong>设y = f(g(x))，在某区间上</p>
                <ul>
                    <li>若f(u)和g(x)都单调递增，则f(g(x))单调递增</li>
                    <li>若f(u)和g(x)都单调递减，则f(g(x))单调递增</li>
                    <li>若f(u)单调递增，g(x)单调递减，则f(g(x))单调递减</li>
                    <li>若f(u)单调递减，g(x)单调递增，则f(g(x))单调递减</li>
                </ul>
                <p><strong>口诀：</strong>同增异减</p>
                <p><strong>示例：</strong>判断y = (2x-1)²的单调性</p>
                <p>设u = 2x-1，则y = u²</p>
                <ul>
                    <li>u = 2x-1在R上单调递增</li>
                    <li>y = u²在[0,+∞)上单调递增，在(-∞,0]上单调递减</li>
                    <li>当u ≥ 0即x ≥ 1/2时，y = (2x-1)²单调递增</li>
                    <li>当u ≤ 0即x ≤ 1/2时，y = (2x-1)²单调递减</li>
                </ul>`,

                // 07. 单调性的加减性质
                `<h2>单调性的加减性质</h2>
                <p><strong>加法性质：</strong></p>
                <ul>
                    <li>若f(x)和g(x)在区间I上都单调递增，则f(x) + g(x)在I上单调递增</li>
                    <li>若f(x)和g(x)在区间I上都单调递减，则f(x) + g(x)在I上单调递减</li>
                </ul>
                <p><strong>减法性质：</strong></p>
                <ul>
                    <li>若f(x)在区间I上单调递增，g(x)在I上单调递减，则f(x) - g(x)在I上单调递增</li>
                    <li>若f(x)在区间I上单调递减，g(x)在I上单调递增，则f(x) - g(x)在I上单调递减</li>
                </ul>
                <p><strong>注意：</strong>乘法和除法的单调性规律较复杂，需要考虑函数值的正负性</p>
                <p><strong>示例：</strong>f(x) = x + 1/x 在(0,1)上的单调性</p>`,

                // 08. 对勾函数的单调性
                `<h2>对勾函数的单调性</h2>
                <p><strong>标准形式：</strong>f(x) = x + a/x (a > 0, x ≠ 0)</p>
                <p><strong>图像特征：</strong>形似对勾，故称对勾函数</p>
                <h3>单调性分析：</h3>
                <p>f'(x) = 1 - a/x² = (x² - a)/x²</p>
                <ul>
                    <li>当x ∈ (-∞, -√a)时，f'(x) > 0，函数单调递增</li>
                    <li>当x ∈ (-√a, 0)时，f'(x) < 0，函数单调递减</li>
                    <li>当x ∈ (0, √a)时，f'(x) < 0，函数单调递减</li>
                    <li>当x ∈ (√a, +∞)时，f'(x) > 0，函数单调递增</li>
                </ul>
                <p><strong>极值：</strong>在x = ±√a处取得极值</p>
                <p><strong>应用：</strong>常用于求最值问题</p>`,

                // 09. 分式函数的单调性
                `<h2>分式函数的单调性</h2>
                <p><strong>一般形式：</strong>f(x) = (ax + b)/(cx + d) (c ≠ 0, ad - bc ≠ 0)</p>
                <h3>分析方法：</h3>
                <ol>
                    <li><strong>定义法：</strong>直接用单调性定义证明</li>
                    <li><strong>导数法：</strong>求导判断导数符号</li>
                    <li><strong>图像法：</strong>通过图像变换分析</li>
                </ol>
                <p><strong>示例：</strong>f(x) = (2x + 1)/(x - 1)</p>
                <p>f'(x) = [2(x-1) - (2x+1)]/(x-1)² = -3/(x-1)²</p>
                <p>因为(x-1)² > 0，所以f'(x) < 0</p>
                <p>因此f(x)在(-∞,1)和(1,+∞)上都严格单调递减</p>
                <p><strong>注意：</strong>要注意定义域的间断点</p>`,

                // 10. 抽象函数的单调性
                `<h2>抽象函数的单调性</h2>
                <p><strong>特点：</strong>没有具体的函数表达式，只给出函数的某些性质</p>
                <h3>常见题型：</h3>
                <ol>
                    <li><strong>利用函数方程：</strong>如f(x+y) = f(x) + f(y)</li>
                    <li><strong>利用不等式条件：</strong>如对任意x₁ < x₂，有f(x₁) < f(x₂)</li>
                    <li><strong>利用特殊值：</strong>如f(0) = 0, f(1) = 1等</li>
                </ol>
                <p><strong>解题策略：</strong></p>
                <ul>
                    <li>充分利用已知条件</li>
                    <li>构造合适的自变量</li>
                    <li>运用单调性定义</li>
                    <li>注意定义域的限制</li>
                </ul>
                <p><strong>示例：</strong>已知f(x)在R上单调递增，且f(x+y) = f(x) + f(y)，求证f(0) = 0</p>`,

                // 11. 单调性与不等式
                `<h2>单调性与不等式</h2>
                <p><strong>基本原理：</strong>利用函数的单调性将函数不等式转化为自变量的不等式</p>
                <h3>解题步骤：</h3>
                <ol>
                    <li>判断函数的单调性</li>
                    <li>根据单调性转化不等式</li>
                    <li>解自变量的不等式</li>
                    <li>检验定义域</li>
                </ol>
                <p><strong>常用结论：</strong></p>
                <ul>
                    <li>若f(x)在I上单调递增，则f(a) > f(b) ⟺ a > b (a,b ∈ I)</li>
                    <li>若f(x)在I上单调递减，则f(a) > f(b) ⟺ a < b (a,b ∈ I)</li>
                </ul>
                <p><strong>示例：</strong>解不等式log₂(x-1) > log₂(3-x)</p>
                <p>因为y = log₂x在(0,+∞)上单调递增，所以原不等式等价于：</p>
                <p>x-1 > 3-x 且 x-1 > 0 且 3-x > 0</p>
                <p>解得：1 < x < 2</p>`,

                // 12. 结合函数方程的单调性综合题
                `<h2>结合函数方程的单调性综合题</h2>
                <p><strong>题型特点：</strong>将函数方程与单调性结合，考查综合运用能力</p>
                <h3>常见函数方程类型：</h3>
                <ul>
                    <li><strong>加法型：</strong>f(x+y) = f(x) + f(y)</li>
                    <li><strong>乘法型：</strong>f(xy) = f(x) + f(y)</li>
                    <li><strong>复合型：</strong>f(f(x)) = x</li>
                    <li><strong>递推型：</strong>f(x+1) = af(x) + b</li>
                </ul>
                <h3>解题策略：</h3>
                <ol>
                    <li>利用函数方程求出函数的特殊值</li>
                    <li>利用单调性和函数方程的性质</li>
                    <li>构造合适的等式或不等式</li>
                    <li>综合运用各种性质得出结论</li>
                </ol>
                <p><strong>示例：</strong>设f(x)在R上单调递增，且满足f(x+y) = f(x) + f(y)，f(1) = 2</p>
                <p>求：(1) f(0)的值 (2) f(x)的表达式 (3) 解不等式f(x²-2x) < 4</p>`
            ];

            return explanations[index] || `<h2>${chapters[index].title}</h2><p>${chapters[index].description}</p>`;
        }

        // 更新进度条
        function updateProgress(completedIndex) {
            const progress = ((completedIndex + 1) / chapters.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            if(e.key === 'Escape') {
                // 关闭所有模态框
                const modals = document.querySelectorAll('[style*="position: fixed"]');
                modals.forEach(modal => {
                    if(modal.parentNode) {
                        modal.parentNode.removeChild(modal);
                    }
                });
            }

            if(e.key >= '1' && e.key <= '9') {
                const index = parseInt(e.key) - 1;
                if(index < chapters.length) {
                    startDemo(index);
                }
            }

            if(e.key === 'h' || e.key === 'H') {
                showHelp();
            }
        });

        // 显示帮助信息
        function showHelp() {
            const helpContent = `
                <h2>🎯 学习指南</h2>
                <h3>📚 如何使用这个学习工具：</h3>
                <ul>
                    <li><strong>点击卡片：</strong>浏览不同的函数单调性概念</li>
                    <li><strong>开始演示：</strong>观看动画演示理解概念</li>
                    <li><strong>详细解释：</strong>查看深入的数学推导</li>
                    <li><strong>键盘快捷键：</strong>
                        <ul>
                            <li>数字键 1-9：快速开始对应章节演示</li>
                            <li>H 键：显示此帮助信息</li>
                            <li>ESC 键：关闭弹窗</li>
                        </ul>
                    </li>
                </ul>
                <h3>🎓 学习建议：</h3>
                <ol>
                    <li><strong>循序渐进：</strong>按照章节顺序学习，每个概念都建立在前面的基础上</li>
                    <li><strong>动手实践：</strong>观看演示后，尝试在纸上画出函数图像</li>
                    <li><strong>理解本质：</strong>不要只记住结论，要理解为什么函数具有这样的单调性</li>
                    <li><strong>多做练习：</strong>学完每个概念后，找相关题目练习巩固</li>
                </ol>
                <h3>💡 学习重点：</h3>
                <ul>
                    <li>掌握单调性的定义和几何意义</li>
                    <li>学会用定义法证明函数单调性</li>
                    <li>理解复合函数单调性的判断规律</li>
                    <li>能够运用单调性解决实际问题</li>
                </ul>
            `;

            showModal('学习帮助', helpContent);
        }

        // 通用模态框函数
        function showModal(title, content) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                padding: 40px;
                border-radius: 20px;
                max-width: 800px;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
                animation: slideIn 0.3s ease;
                box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            `;

            modalContent.innerHTML = `
                <h1 style="color: #667eea; margin-bottom: 20px;">${title}</h1>
                ${content}
            `;

            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '×';
            closeBtn.style.cssText = `
                position: absolute;
                top: 15px;
                right: 20px;
                background: none;
                border: none;
                font-size: 30px;
                cursor: pointer;
                color: #999;
                transition: color 0.3s ease;
            `;

            closeBtn.onmouseover = () => closeBtn.style.color = '#ff6b6b';
            closeBtn.onmouseout = () => closeBtn.style.color = '#999';
            closeBtn.onclick = () => document.body.removeChild(modal);

            modalContent.appendChild(closeBtn);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 点击背景关闭
            modal.onclick = (e) => {
                if(e.target === modal) {
                    document.body.removeChild(modal);
                }
            };
        }

        // 添加学习进度跟踪
        let completedChapters = new Set();

        function markChapterComplete(index) {
            completedChapters.add(index);
            const card = document.querySelector(`[data-chapter="${index}"]`);
            if(card) {
                card.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
                card.style.color = 'white';

                // 添加完成标记
                const checkMark = document.createElement('div');
                checkMark.innerHTML = '✓';
                checkMark.style.cssText = `
                    position: absolute;
                    top: 10px;
                    right: 15px;
                    font-size: 24px;
                    color: #4CAF50;
                    font-weight: bold;
                `;
                card.appendChild(checkMark);
            }

            updateOverallProgress();
        }

        function updateOverallProgress() {
            const progress = (completedChapters.size / chapters.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';

            if(completedChapters.size === chapters.length) {
                setTimeout(() => {
                    showCongratulations();
                }, 500);
            }
        }

        function showCongratulations() {
            const congratsContent = `
                <div style="text-align: center;">
                    <h2 style="color: #4CAF50; font-size: 2.5rem; margin-bottom: 20px;">🎉 恭喜完成学习！</h2>
                    <p style="font-size: 1.2rem; margin-bottom: 30px;">您已经完成了函数单调性的全部学习内容！</p>
                    <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 15px; margin: 20px 0;">
                        <h3>🏆 学习成就</h3>
                        <ul style="text-align: left; margin: 15px 0;">
                            <li>✅ 掌握了单调性的基本概念</li>
                            <li>✅ 学会了证明函数单调性的方法</li>
                            <li>✅ 理解了各种函数的单调性规律</li>
                            <li>✅ 能够解决单调性相关的综合问题</li>
                        </ul>
                    </div>
                    <p style="margin-top: 30px; color: #666;">继续加油，在数学的道路上不断前进！</p>
                </div>
            `;

            showModal('学习完成', congratsContent);
        }

        // 添加章节数据属性
        function createChapterCards() {
            const grid = document.getElementById('chapterGrid');

            chapters.forEach((chapter, index) => {
                const card = document.createElement('div');
                card.className = 'chapter-card';
                card.setAttribute('data-chapter', index);
                card.style.setProperty('--delay', index);

                card.innerHTML = `
                    <div class="chapter-number">${chapter.number}</div>
                    <h3 class="chapter-title">${chapter.title}</h3>
                    <p class="chapter-description">${chapter.description}</p>
                    <canvas class="demo-canvas" id="canvas${index}"></canvas>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="interactive-btn" onclick="startDemo(${index})">🎬 开始演示</button>
                        <button class="interactive-btn" onclick="showExplanation(${index})">📖 详细解释</button>
                        <button class="interactive-btn" onclick="markChapterComplete(${index})">✅ 标记完成</button>
                    </div>
                `;

                grid.appendChild(card);

                // 初始化画布演示
                setTimeout(() => {
                    initCanvasDemo(index, chapter.demoType);
                }, 100 * index);
            });
        }

        // 添加帮助按钮到页面
        function addHelpButton() {
            const helpBtn = document.createElement('button');
            helpBtn.innerHTML = '❓ 帮助';
            helpBtn.style.cssText = `
                position: fixed;
                bottom: 30px;
                right: 30px;
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 50px;
                cursor: pointer;
                font-size: 16px;
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
                transition: all 0.3s ease;
                z-index: 100;
            `;

            helpBtn.onmouseover = () => {
                helpBtn.style.transform = 'translateY(-3px)';
                helpBtn.style.boxShadow = '0 12px 35px rgba(102, 126, 234, 0.4)';
            };

            helpBtn.onmouseout = () => {
                helpBtn.style.transform = 'translateY(0)';
                helpBtn.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.3)';
            };

            helpBtn.onclick = showHelp;
            document.body.appendChild(helpBtn);
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            createFloatingElements();
            createChapterCards();
            addHelpButton();

            // 显示欢迎信息
            setTimeout(() => {
                showModal('欢迎学习函数单调性！', `
                    <div style="text-align: center;">
                        <h3>🎯 学习目标</h3>
                        <p>通过交互式动画和详细解释，全面掌握函数单调性的概念、证明方法和应用技巧。</p>
                        <h3>🚀 开始学习</h3>
                        <p>点击任意章节卡片开始您的学习之旅，建议按顺序学习以获得最佳效果。</p>
                        <p style="margin-top: 20px; color: #666; font-size: 0.9rem;">
                            💡 提示：按 H 键可随时查看帮助信息
                        </p>
                    </div>
                `);
            }, 1000);
        });

        // 响应式处理
        window.addEventListener('resize', () => {
            chapters.forEach((_, index) => {
                const canvas = document.getElementById(`canvas${index}`);
                if(canvas) {
                    canvas.width = canvas.offsetWidth;
                    canvas.height = canvas.offsetHeight;
                    initCanvasDemo(index, chapters[index].demoType);
                }
            });
        });
    </script>
</body>
</html>
