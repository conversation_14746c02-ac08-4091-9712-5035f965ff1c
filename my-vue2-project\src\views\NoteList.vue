<template>
  <div class="note-list-layout" :class="{ 'mobile-layout': isMobile }">
    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && sidebarVisible"
      class="mobile-overlay"
      @click="closeSidebar"
    ></div>

    <!-- 侧边栏菜单 -->
    <div class="sidebar" :class="{ 'sidebar-mobile': isMobile, 'sidebar-visible': sidebarVisible }">
      <div class="sidebar-header">
        <h1 class="sidebar-title">我的知识库</h1>
        <!-- 移动端关闭按钮 -->
        <button
          v-if="isMobile"
          class="sidebar-close-btn"
          @click="closeSidebar"
        >
          <i class="el-icon-close"></i>
        </button>
      </div>
      <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical-demo"
        @select="handleMenuSelect"
        background-color="transparent"
        text-color="#ffffff"
        active-text-color="#409EFF"
      >
        <el-menu-item index="all">
          <i class="el-icon-menu"></i>
          <span slot="title">全部笔记</span>
        </el-menu-item>
        <sub-menu-item
          v-for="item in menuItems"
          :key="item.name"
          :item="item"
        />
      </el-menu>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 移动端顶部栏 -->
      <div v-if="isMobile" class="mobile-header">
        <button class="mobile-menu-btn" @click="toggleSidebar">
          <i class="el-icon-menu"></i>
        </button>
        <h1 class="mobile-title">知识库</h1>
        <button class="mobile-home-btn" @click="goHome">
          <i class="el-icon-s-home"></i>
        </button>
      </div>

      <!-- 桌面端页头 -->
      <PageHeader v-if="!isMobile">
        <template #actions>
          <TextButton type="default" icon="el-icon-s-home" @click="goHome">返回首页</TextButton>
        </template>
      </PageHeader>

      <div class="container">
        <!-- 文件列表视图 -->
        <div v-if="!selectedFile">
          <div class="list-header fade-in">
            <div class="list-header-left">
              <h2 class="page-title">{{ activeMenu === 'all' ? '全部文件' : activeMenu }}</h2>
              <p class="page-subtitle">{{ currentFiles.length }} 个文件</p>
            </div>
            <div class="list-header-right">
            </div>
          </div>
          
          <div v-if="currentFiles.length === 0 && !loading" class="empty-state fade-in" style="animation-delay: 0.2s;">
            <el-empty description="暂无文件" :image-size="120">
            </el-empty>
          </div>
          
          <transition-group 
            name="note-list" 
            tag="div" 
            class="notes-grid"
            v-else-if="!loading"
          >
            <div
              v-for="(file, index) in currentFiles"
              :key="file.path"
              class="note-card-wrapper"
              :style="{ animationDelay: `${index * 0.05}s` }"
            >
              <div
                class="note-item fade-in"
                :class="{ 'note-item-read': isFileRead(file.path) }"
                @click="viewFile(file)"
              >
                <h3 class="note-title">
                  {{ file.name }}
                  <i v-if="isFileRead(file.path)" class="el-icon-view read-indicator" title="已阅读"></i>
                </h3>
                <p class="note-content">路径: {{ file.path }}</p>
                <div v-if="isFileRead(file.path)" class="read-time">
                  上次阅读: {{ formatLastReadTime(file.path) }}
                </div>
              </div>
            </div>
          </transition-group>
          
          <div v-else class="loading-container fade-in">
            <div class="loading-spinner"></div>
            <p class="loading-text">正在加载笔记...</p>
          </div>
        </div>

        <!-- HTML阅读器视图 -->
        <div v-else class="html-viewer-container fade-in">
          <div class="html-viewer-header" :class="{ 'mobile-viewer-header': isMobile }">
            <TextButton type="default" icon="el-icon-arrow-left" @click="backToList">返回列表</TextButton>
            <h2 class="viewer-title" :class="{ 'mobile-viewer-title': isMobile }">{{ selectedFile.name }}</h2>
            <div class="header-controls" :class="{ 'mobile-header-controls': isMobile }">
              <!-- 轮播控制按钮 -->
              <div class="carousel-controls" :class="{ 'mobile-carousel-controls': isMobile }">
                <el-button
                  :size="isMobile ? 'small' : 'mini'"
                  :type="carouselSettings.enabled ? 'primary' : 'default'"
                  icon="el-icon-video-play"
                  @click="toggleCarousel"
                  :title="carouselSettings.enabled ? '停止轮播' : '开始轮播'"
                >
                  {{ isMobile ? (carouselSettings.enabled ? '停止' : '轮播') : (carouselSettings.enabled ? '停止' : '轮播') }}
                </el-button>
                <el-button
                  v-if="!isMobile"
                  size="mini"
                  :type="pipMode ? 'primary' : 'default'"
                  icon="el-icon-picture-outline"
                  @click="togglePictureInPicture"
                  :title="pipMode ? '退出画中画' : '开启画中画'"
                  :disabled="!pipSupported"
                >
                  画中画
                </el-button>
                <el-button
                  :size="isMobile ? 'small' : 'mini'"
                  icon="el-icon-setting"
                  @click="showCarouselSettings = true"
                  title="轮播设置"
                >
                  {{ isMobile ? '设置' : '设置' }}
                </el-button>
                <el-button
                  v-if="isMobile"
                  size="small"
                  :type="mobileReadingMode ? 'primary' : 'default'"
                  icon="el-icon-full-screen"
                  @click="toggleMobileReadingMode"
                  title="沉浸式阅读"
                >
                  沉浸
                </el-button>
              </div>
              <div class="reading-progress" :class="{ 'mobile-reading-progress': isMobile }">
                <span class="progress-text">{{ currentFileIndex + 1 }} / {{ currentFiles.length }}</span>
              </div>
            </div>
          </div>
          <div
            class="html-viewer-content"
            :class="{
              'pip-mode': pipMode,
              'mobile-reading-mode': isMobile && mobileReadingMode
            }"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
            ref="htmlViewerContent"
          >
            <HtmlViewer :filePath="selectedFile.path" ref="htmlViewer" />

            <!-- 左右导航箭头 -->
            <div class="navigation-arrows">
              <div
                class="nav-arrow nav-arrow-left"
                v-if="hasPrevFile"
                @click="prevFile"
                :class="{ 'nav-arrow-disabled': !hasPrevFile }"
              >
                <i class="el-icon-arrow-left"></i>
              </div>
              <div
                class="nav-arrow nav-arrow-right"
                v-if="hasNextFile"
                @click="nextFile"
                :class="{ 'nav-arrow-disabled': !hasNextFile }"
              >
                <i class="el-icon-arrow-right"></i>
              </div>

            </div>
          </div>
          
          <!-- 打分组件 -->
          <div class="rating-container fade-in" style="animation-delay: 0.3s;">
            <div class="rating-stars">
              <div
                v-for="n in 10"
                :key="n"
                class="rating-star"
                :class="{ 'active': n <= currentRating, 'hover': n <= hoverRating }"
                @click="rateContent(n)"
                @mouseenter="hoverRating = n"
                @mouseleave="hoverRating = 0"
              >
                <i class="el-icon-star-on"></i>
              </div>
            </div>
            <transition name="fade">
              <div v-if="ratingSubmitted" class="rating-success">
                <i class="el-icon-check"></i> 评分已提交
              </div>
            </transition>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端底部导航栏 -->
    <div v-if="isMobile && selectedFile && !mobileReadingMode" class="mobile-bottom-nav">
      <button
        class="mobile-nav-btn"
        :disabled="!hasPrevFile"
        @click="prevFile"
      >
        <i class="el-icon-arrow-left"></i>
        <span>上一个</span>
      </button>
      <button
        class="mobile-nav-btn mobile-nav-center"
        @click="toggleCarousel"
      >
        <i :class="carouselSettings.enabled ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
        <span>{{ carouselSettings.enabled ? '暂停' : '播放' }}</span>
      </button>
      <button
        class="mobile-nav-btn"
        :disabled="!hasNextFile"
        @click="nextFile"
      >
        <i class="el-icon-arrow-right"></i>
        <span>下一个</span>
      </button>
    </div>

    <!-- 移动端沉浸式阅读模式的浮动控制按钮 -->
    <div v-if="isMobile && selectedFile && mobileReadingMode" class="mobile-floating-controls">
      <button class="floating-btn" @click="toggleMobileReadingMode" title="退出沉浸模式">
        <i class="el-icon-close"></i>
      </button>
      <button class="floating-btn" @click="prevFile" :disabled="!hasPrevFile" title="上一个">
        <i class="el-icon-arrow-left"></i>
      </button>
      <button class="floating-btn" @click="nextFile" :disabled="!hasNextFile" title="下一个">
        <i class="el-icon-arrow-right"></i>
      </button>
      <button class="floating-btn" @click="toggleCarousel" :title="carouselSettings.enabled ? '暂停轮播' : '开始轮播'">
        <i :class="carouselSettings.enabled ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
      </button>
    </div>

    <!-- 轮播设置对话框 -->
    <el-dialog
      title="轮播设置"
      :visible.sync="showCarouselSettings"
      width="500px"
      :before-close="handleCarouselSettingsClose"
    >
      <div class="carousel-settings">
        <el-form :model="carouselSettings" label-width="120px">
          <el-form-item label="轮播间隔">
            <el-slider
              v-model="carouselSettings.interval"
              :min="1"
              :max="60"
              :step="1"
              show-input
              :format-tooltip="formatTooltip"
            />
            <div class="setting-description">每隔 {{ carouselSettings.interval }} 秒自动切换到下一个文件</div>
          </el-form-item>

          <el-form-item label="轮播方向">
            <el-radio-group v-model="carouselSettings.direction">
              <el-radio label="forward">向前轮播</el-radio>
              <el-radio label="backward">向后轮播</el-radio>
              <el-radio label="random">随机轮播</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="循环播放">
            <el-switch v-model="carouselSettings.loop"></el-switch>
            <div class="setting-description">到达最后一个文件时是否回到第一个文件继续播放</div>
          </el-form-item>

          <el-form-item label="鼠标悬停">
            <el-switch v-model="carouselSettings.pauseOnHover"></el-switch>
            <div class="setting-description">鼠标悬停在内容区域时暂停轮播</div>
          </el-form-item>

          <el-form-item label="键盘控制">
            <el-switch v-model="carouselSettings.keyboardControl"></el-switch>
            <div class="setting-description">使用方向键控制轮播（上下左右键）</div>
          </el-form-item>

          <el-form-item label="画中画模式">
            <div class="pip-info">
              <el-tag :type="pipSupported ? 'success' : 'danger'" size="mini">
                {{ pipSupported ? '✓ 支持' : '✗ 不支持' }}
              </el-tag>
              <div class="setting-description">
                {{ pipSupported ? '可以在小窗口中继续观看内容，同时浏览其他页面' : '您的浏览器不支持画中画功能' }}
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="resetCarouselSettings">重置默认</el-button>
        <el-button @click="showCarouselSettings = false">取消</el-button>
        <el-button type="primary" @click="saveCarouselSettings">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import menuData from '@/assets/menu.json'
import SubMenuItem from '@/components/common/SubMenuItem.vue'
import HtmlViewer from '@/views/HtmlViewer.vue'

export default {
  name: 'NoteList',
  components: {
    SubMenuItem,
    HtmlViewer
  },
  data() {
    return {
      activeMenu: 'all', // 默认选中的菜单
      menuItems: [],
      selectedFile: null,
      currentRating: 0,
      hoverRating: 0,
      ratingSubmitted: false,
      // 移动端相关数据
      isMobile: false,
      sidebarVisible: false,
      touchStartX: 0,
      touchStartY: 0,
      mobileReadingMode: false, // 移动端沉浸式阅读模式
      // 轮播相关数据
      showCarouselSettings: false,
      carouselTimer: null,
      carouselPaused: false,
      carouselSettings: {
        enabled: false,
        interval: 5, // 秒
        direction: 'forward', // forward, backward, random
        loop: true,
        pauseOnHover: true,
        keyboardControl: true
      },
      // 画中画相关数据
      pipMode: false,
      pipSupported: false,
      pipVideo: null
    }
  },
  computed: {
    ...mapState(['darkMode', 'loading', 'readingHistory']),
    currentFiles() {
      if (this.activeMenu === 'all') {
        const allFiles = [];
        const traverse = (items) => {
          for (const item of items) {
            if (item.files) {
              allFiles.push(...item.files);
            }
            if (item.children) {
              traverse(item.children);
            }
          }
        };
        traverse(this.menuItems);
        return allFiles;
      }

      const findNode = (nodes, name) => {
        for (const node of nodes) {
          if (node.name === name) {
            return node;
          }
          if (node.children) {
            const found = findNode(node.children, name);
            if (found) return found;
          }
        }
        return null;
      }
      
      const selectedNode = findNode(this.menuItems, this.activeMenu);
      
      if (!selectedNode) {
          return [];
      }

      const files = [];
      const collectFiles = (node) => {
          if (node.files) {
              files.push(...node.files);
          }
          if (node.children) {
              for (const child of node.children) {
                  collectFiles(child);
              }
          }
      };
      collectFiles(selectedNode);
      return files;
    },
    currentFileIndex() {
      if (!this.selectedFile) return -1;
      return this.currentFiles.findIndex(file => file.path === this.selectedFile.path);
    },
    hasPrevFile() {
      return this.currentFileIndex > 0;
    },
    hasNextFile() {
      return this.currentFileIndex >= 0 && this.currentFileIndex < this.currentFiles.length - 1;
    }
  },
  methods: {
    ...mapActions(['toggleDarkMode']),
    // 移动端相关方法
    checkMobile() {
      this.isMobile = window.innerWidth <= 768;
    },
    handleResize() {
      this.checkMobile();
      if (!this.isMobile) {
        this.sidebarVisible = false;
      }
    },
    toggleSidebar() {
      this.sidebarVisible = !this.sidebarVisible;
    },
    closeSidebar() {
      this.sidebarVisible = false;
    },
    toggleMobileReadingMode() {
      this.mobileReadingMode = !this.mobileReadingMode;
    },
    handleTouchStart(event) {
      if (!this.isMobile) return;
      this.touchStartX = event.touches[0].clientX;
      this.touchStartY = event.touches[0].clientY;
    },
    handleTouchEnd(event) {
      if (!this.isMobile) return;
      const touchEndX = event.changedTouches[0].clientX;
      const touchEndY = event.changedTouches[0].clientY;
      const deltaX = touchEndX - this.touchStartX;
      const deltaY = touchEndY - this.touchStartY;

      // 水平滑动距离大于垂直滑动距离，且滑动距离超过50px
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        if (deltaX > 0 && this.touchStartX < 50 && !this.selectedFile) {
          // 从左边缘向右滑动，打开侧边栏（仅在文件列表页面）
          this.sidebarVisible = true;
        } else if (deltaX < 0 && this.sidebarVisible) {
          // 向左滑动，关闭侧边栏
          this.sidebarVisible = false;
        } else if (this.selectedFile && !this.sidebarVisible) {
          // 在阅读器页面，左右滑动切换文件
          if (deltaX > 0 && this.hasPrevFile) {
            // 向右滑动，上一个文件
            this.prevFile();
          } else if (deltaX < 0 && this.hasNextFile) {
            // 向左滑动，下一个文件
            this.nextFile();
          }
        }
      }
    },
    // 检查文件是否已读过
    isFileRead(filePath) {
      return this.readingHistory.some(item => item.path === filePath);
    },
    // 获取文件的最后阅读时间
    getLastReadTime(filePath) {
      const historyItem = this.readingHistory.find(item => item.path === filePath);
      return historyItem ? historyItem.lastRead : null;
    },
    // 格式化最后阅读时间
    formatLastReadTime(filePath) {
      const lastRead = this.getLastReadTime(filePath);
      return lastRead ? this.formatDate(lastRead) : '';
    },
    handleMenuSelect(index) {
      this.activeMenu = index;
      // 移动端选择菜单后自动关闭侧边栏
      if (this.isMobile) {
        this.closeSidebar();
      }
    },
    goHome() {
      this.$router.push('/')
    },
    viewFile(file) {
      this.selectedFile = file;
      this.currentRating = 0;
      this.ratingSubmitted = false;
      // 添加到阅读历史记录
      this.$store.dispatch('addReadingHistory', {
        path: file.path,
        name: file.name,
        lastRead: new Date().toISOString()
      });
    },
    backToList() {
      this.selectedFile = null;
    },
    prevFile() {
      if (this.hasPrevFile) {
        const file = this.currentFiles[this.currentFileIndex - 1];
        this.selectedFile = file;
        this.currentRating = 0;
        this.ratingSubmitted = false;
        // 添加到阅读历史记录
        this.$store.dispatch('addReadingHistory', {
          path: file.path,
          name: file.name,
          lastRead: new Date().toISOString()
        });
      }
    },
    nextFile() {
      if (this.hasNextFile) {
        const file = this.currentFiles[this.currentFileIndex + 1];
        this.selectedFile = file;
        this.currentRating = 0;
        this.ratingSubmitted = false;
        // 添加到阅读历史记录
        this.$store.dispatch('addReadingHistory', {
          path: file.path,
          name: file.name,
          lastRead: new Date().toISOString()
        });
      }
    },
    rateContent(rating) {
      this.currentRating = rating;
      this.ratingSubmitted = true;
      
      // 这里可以添加将评分发送到后端的逻辑
      console.log(`为文件 ${this.selectedFile.name} 评分: ${rating}`);
      
      // 3秒后隐藏成功提示
      setTimeout(() => {
        this.ratingSubmitted = false;
      }, 3000);
    },
    handleKeyDown(event) {
      if (!this.selectedFile || !this.carouselSettings.keyboardControl) return;

      if (event.key === 'ArrowLeft') {
        this.prevFile();
      } else if (event.key === 'ArrowRight') {
        this.nextFile();
      } else if (event.key === ' ') { // 空格键暂停/继续轮播
        event.preventDefault();
        this.toggleCarousel();
      }
    },
    truncateContent(content) {
      return content.length > 100 ? content.substring(0, 100) + '...' : content
    },
    formatDate(dateString) {
      if (!dateString) {
        return '日期未知';
      }
      const date = new Date(dateString)
      if (isNaN(date.getTime())) {
        return '日期无效';
      }
      const now = new Date()
      const diff = now.getTime() - date.getTime()
      
      const diffMinutes = Math.floor(diff / (1000 * 60))
      const diffHours = Math.floor(diff / (1000 * 60 * 60))
      const diffDays = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (diffMinutes < 1) {
        return '刚刚'
      } else if (diffMinutes < 60) {
        return `${diffMinutes} 分钟前`
      } else if (diffHours < 24) {
        return `${diffHours} 小时前`
      } else if (diffDays < 7) {
        return `${diffDays} 天前`
      } else {
        // For dates more than a week old, show the actual date
        return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' });
      }
    },
    // 轮播相关方法
    toggleCarousel() {
      this.carouselSettings.enabled = !this.carouselSettings.enabled;
      if (this.carouselSettings.enabled) {
        this.startCarousel();
      } else {
        this.stopCarousel();
      }
    },
    startCarousel() {
      if (this.carouselTimer) {
        clearInterval(this.carouselTimer);
      }

      this.carouselTimer = setInterval(() => {
        if (!this.carouselPaused) {
          this.nextCarouselFile();
        }
      }, this.carouselSettings.interval * 1000);
    },
    stopCarousel() {
      if (this.carouselTimer) {
        clearInterval(this.carouselTimer);
        this.carouselTimer = null;
      }
    },
    nextCarouselFile() {
      if (this.carouselSettings.direction === 'random') {
        this.randomFile();
      } else if (this.carouselSettings.direction === 'backward') {
        if (this.hasPrevFile) {
          this.prevFile();
        } else if (this.carouselSettings.loop) {
          // 跳到最后一个文件
          const lastFile = this.currentFiles[this.currentFiles.length - 1];
          this.viewFile(lastFile);
        } else {
          this.stopCarousel();
          this.carouselSettings.enabled = false;
        }
      } else { // forward
        if (this.hasNextFile) {
          this.nextFile();
        } else if (this.carouselSettings.loop) {
          // 跳到第一个文件
          const firstFile = this.currentFiles[0];
          this.viewFile(firstFile);
        } else {
          this.stopCarousel();
          this.carouselSettings.enabled = false;
        }
      }
    },
    randomFile() {
      if (this.currentFiles.length <= 1) return;

      let randomIndex;
      do {
        randomIndex = Math.floor(Math.random() * this.currentFiles.length);
      } while (randomIndex === this.currentFileIndex);

      const randomFile = this.currentFiles[randomIndex];
      this.viewFile(randomFile);
    },
    pauseCarousel() {
      this.carouselPaused = true;
    },
    resumeCarousel() {
      this.carouselPaused = false;
    },
    handleCarouselSettingsClose() {
      this.showCarouselSettings = false;
    },
    saveCarouselSettings() {
      this.showCarouselSettings = false;
      // 如果轮播正在运行，重新启动以应用新设置
      if (this.carouselSettings.enabled) {
        this.startCarousel();
      }
      // 保存设置到本地存储
      localStorage.setItem('carouselSettings', JSON.stringify(this.carouselSettings));
    },
    resetCarouselSettings() {
      this.carouselSettings = {
        enabled: false,
        interval: 5,
        direction: 'forward',
        loop: true,
        pauseOnHover: true,
        keyboardControl: true
      };
    },
    formatTooltip(value) {
      return `${value}秒`;
    },
    handleMouseEnter() {
      if (this.carouselSettings.pauseOnHover) {
        this.pauseCarousel();
      }
    },
    handleMouseLeave() {
      if (this.carouselSettings.pauseOnHover) {
        this.resumeCarousel();
      }
    },
    // 画中画相关方法
    async togglePictureInPicture() {
      if (!this.pipSupported) {
        this.$message.warning('您的浏览器不支持画中画功能');
        return;
      }

      try {
        if (this.pipMode) {
          await this.exitPictureInPicture();
        } else {
          await this.enterPictureInPicture();
        }
      } catch (error) {
        console.error('画中画操作失败:', error);
        this.$message.error('画中画操作失败: ' + error.message);
      }
    },
    async enterPictureInPicture() {
      try {
        console.log('开始进入画中画模式...');
        this.$message.info('正在启动画中画...');

        // 方法1: 尝试使用现有的iframe内容
        const htmlViewer = this.$refs.htmlViewer;
        if (htmlViewer && htmlViewer.$el) {
          const iframe = htmlViewer.$el.querySelector('iframe');
          if (iframe) {
            console.log('尝试使用iframe内容');
            try {
              await this.createPipFromIframe(iframe);
              return;
            } catch (error) {
              console.warn('iframe方法失败，尝试canvas方法:', error);
            }
          }
        }

        // 方法2: 使用canvas绘制
        console.log('使用canvas方法');
        await this.createPipFromCanvas();

      } catch (error) {
        console.error('进入画中画失败:', error);
        this.$message.error('画中画启动失败: ' + error.message);
        throw error;
      }
    },
    async createPipFromCanvas() {
      // 创建video元素
      const video = document.createElement('video');
      video.width = 800;
      video.height = 600;
      video.muted = true;
      video.autoplay = true;
      video.style.display = 'none';

      // 创建canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = 800;
      canvas.height = 600;

      // 绘制初始内容
      this.drawPipContent(ctx, canvas);

      // 将canvas转换为video流
      const stream = canvas.captureStream(15);
      video.srcObject = stream;

      document.body.appendChild(video);

      // 等待video准备就绪
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Video加载超时'));
        }, 5000);

        video.addEventListener('loadedmetadata', () => {
          clearTimeout(timeout);
          console.log('Video元数据加载完成');
          resolve();
        }, { once: true });

        video.addEventListener('error', (e) => {
          clearTimeout(timeout);
          console.error('Video加载错误:', e);
          reject(new Error('Video加载失败'));
        }, { once: true });
      });

      // 进入画中画模式
      console.log('请求画中画...');
      const pipWindow = await video.requestPictureInPicture();

      this.pipVideo = video;
      this.pipMode = true;

      // 开始内容更新
      this.startPipContentUpdate(ctx, canvas);

      // 监听退出事件
      video.addEventListener('leavepictureinpicture', () => {
        console.log('画中画窗口关闭');
        this.exitPictureInPicture();
      }, { once: true });

      this.$message.success('画中画已启动！');
      console.log('画中画启动成功', pipWindow);
    },
    async createPipFromIframe() {
      // 这个方法暂时不实现，因为跨域限制
      throw new Error('iframe方法暂不支持');
    },
    async exitPictureInPicture() {
      if (this.pipVideo) {
        try {
          if (document.pictureInPictureElement) {
            await document.exitPictureInPicture();
          }
        } catch (error) {
          console.error('退出画中画失败:', error);
        }

        // 清理video元素
        if (this.pipVideo.parentNode) {
          this.pipVideo.parentNode.removeChild(this.pipVideo);
        }
        this.pipVideo = null;
      }

      this.pipMode = false;
      this.$message.success('已退出画中画模式');
    },
    startPipContentUpdate(ctx, canvas) {
      let frameCount = 0;
      let lastScreenshotTime = 0;
      const screenshotInterval = 2000; // 每2秒尝试截图一次

      const updateContent = () => {
        if (!this.pipMode || !this.pipVideo) return;

        try {
          this.drawPipContent(ctx, canvas, frameCount);
          frameCount++;

          // 定期尝试捕获HTML内容
          const now = Date.now();
          if (now - lastScreenshotTime > screenshotInterval) {
            lastScreenshotTime = now;
            this.tryCapturePage(ctx, canvas);
          }
        } catch (error) {
          console.warn('画中画内容更新失败:', error);
        }

        // 每秒更新15次
        if (this.pipMode) {
          setTimeout(updateContent, 67);
        }
      };

      updateContent();
    },
    async tryCapturePage(ctx, canvas) {
      try {
        // 直接使用备用方案，因为html2canvas需要额外安装
        this.tryAlternativeCapture(ctx, canvas);
      } catch (error) {
        console.warn('页面捕获失败:', error);
      }
    },
    tryAlternativeCapture(ctx, canvas) {
      // 备用方案：绘制更详细的模拟内容
      const contentArea = {
        x: 30,
        y: 230,
        width: canvas.width - 60,
        height: canvas.height - 290
      };

      // 绘制内容区域背景
      ctx.fillStyle = '#fafafa';
      ctx.fillRect(contentArea.x, contentArea.y, contentArea.width, contentArea.height);

      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 1;
      ctx.strokeRect(contentArea.x, contentArea.y, contentArea.width, contentArea.height);

      // 绘制模拟的网页内容
      this.drawWebPageMockup(ctx, contentArea);
    },
    drawWebPageMockup(ctx, area) {
      const padding = 15;
      const startX = area.x + padding;
      const startY = area.y + padding;
      const lineHeight = 18;

      // 绘制标题
      ctx.fillStyle = '#2c3e50';
      ctx.font = 'bold 16px Microsoft YaHei';
      ctx.textAlign = 'left';
      ctx.fillText('📄 ' + (this.selectedFile?.name || '文档标题'), startX, startY + 20);

      // 绘制内容行
      ctx.fillStyle = '#34495e';
      ctx.font = '12px Microsoft YaHei';

      const contentLines = [
        '这是文档的主要内容区域。',
        '由于浏览器安全限制，无法直接',
        '捕获iframe中的实际HTML内容。',
        '',
        '当前显示的是模拟内容，包括：',
        '• 文档标题和基本信息',
        '• 阅读进度和状态指示',
        '• 实时时间和动画效果',
        '',
        '您可以通过以下方式控制：',
        '→ 方向键：切换文档',
        '→ 空格键：暂停/继续轮播',
        '→ 鼠标：调整画中画窗口'
      ];

      contentLines.forEach((line, index) => {
        const y = startY + 50 + index * lineHeight;
        if (y < area.y + area.height - padding) {
          ctx.fillText(line, startX, y);
        }
      });

      // 绘制一些装饰性元素
      this.drawDecorations(ctx, area);
    },
    drawDecorations(ctx, area) {
      // 绘制进度条背景
      const progressBarY = area.y + area.height - 50;
      const progressWidth = (area.width - 30) * (this.currentFileIndex + 1) / this.currentFiles.length;

      ctx.fillStyle = '#ecf0f1';
      ctx.fillRect(area.x + 15, progressBarY, area.width - 30, 8);

      // 绘制进度条
      ctx.fillStyle = '#3498db';
      ctx.fillRect(area.x + 15, progressBarY, progressWidth, 8);

      // 绘制进度条边框
      ctx.strokeStyle = '#bdc3c7';
      ctx.lineWidth = 1;
      ctx.strokeRect(area.x + 15, progressBarY, area.width - 30, 8);

      // 绘制状态图标和信息
      const iconY = area.y + area.height - 30;
      ctx.font = '12px Microsoft YaHei';
      ctx.fillStyle = '#7f8c8d';

      // 左侧：进度信息
      ctx.textAlign = 'left';
      ctx.fillText('📊', area.x + 15, iconY);
      ctx.fillText(`${this.currentFileIndex + 1}/${this.currentFiles.length}`, area.x + 35, iconY);

      // 中间：轮播状态
      ctx.textAlign = 'center';
      if (this.carouselSettings.enabled) {
        ctx.fillStyle = '#27ae60';
        ctx.fillText('🔄 轮播中', area.x + area.width / 2, iconY);
      } else {
        ctx.fillStyle = '#e74c3c';
        ctx.fillText('⏸️ 已暂停', area.x + area.width / 2, iconY);
      }

      // 右侧：时间信息
      ctx.textAlign = 'right';
      ctx.fillStyle = '#7f8c8d';
      const now = new Date();
      ctx.fillText('🕐', area.x + area.width - 35, iconY);
      ctx.fillText(now.toLocaleTimeString().substring(0, 5), area.x + area.width - 15, iconY);

      // 绘制阅读历史指示
      const historyY = area.y + area.height - 15;
      ctx.font = '10px Microsoft YaHei';
      ctx.fillStyle = '#95a5a6';
      ctx.textAlign = 'left';
      ctx.fillText(`已读: ${this.readingHistory.length} | 间隔: ${this.carouselSettings.interval}s`, area.x + 15, historyY);
    },
    drawPipContent(ctx, canvas, frameCount = 0) {
      try {
        // 清空canvas
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制标题栏
        ctx.fillStyle = '#409EFF';
        ctx.fillRect(0, 0, canvas.width, 80);

        // 绘制标题
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 24px Microsoft YaHei';
        ctx.textAlign = 'left';
        ctx.fillText('📚 知识库阅读器', 20, 35);

        // 绘制时间
        const now = new Date();
        ctx.textAlign = 'right';
        ctx.font = '16px Microsoft YaHei';
        ctx.fillText(now.toLocaleTimeString(), canvas.width - 20, 35);

        // 绘制文件信息
        ctx.textAlign = 'left';
        ctx.font = '18px Microsoft YaHei';
        const fileName = this.selectedFile?.name || '未选择文件';
        ctx.fillText(fileName.length > 40 ? fileName.substring(0, 40) + '...' : fileName, 20, 65);

        // 绘制内容区域背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(20, 100, canvas.width - 40, canvas.height - 160);

        ctx.strokeStyle = '#e0e0e0';
        ctx.lineWidth = 2;
        ctx.strokeRect(20, 100, canvas.width - 40, canvas.height - 160);

        // 绘制进度信息
        ctx.fillStyle = '#333333';
        ctx.font = 'bold 24px Microsoft YaHei';
        ctx.textAlign = 'center';
        ctx.fillText(`${this.currentFileIndex + 1} / ${this.currentFiles.length}`, canvas.width / 2, 140);

        // 绘制轮播状态
        ctx.font = '18px Microsoft YaHei';
        if (this.carouselSettings.enabled) {
          ctx.fillStyle = '#67C23A';
          ctx.fillText('🔄 轮播中', canvas.width / 2, 170);
        } else {
          ctx.fillStyle = '#E6A23C';
          ctx.fillText('⏸️ 已暂停', canvas.width / 2, 170);
        }

        // 尝试捕获实际的HTML内容
        this.drawHtmlContent(ctx, canvas);

        // 绘制动画效果
        if (frameCount > 0) {
          const centerX = canvas.width / 2;
          const centerY = canvas.height / 2 + 100;
          const radius = 15 + Math.sin(frameCount * 0.1) * 5;

          ctx.fillStyle = `hsl(${(frameCount * 2) % 360}, 70%, 60%)`;
          ctx.beginPath();
          ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
          ctx.fill();
        }

        // 绘制底部控制信息
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(0, canvas.height - 60, canvas.width, 60);

        ctx.fillStyle = '#ffffff';
        ctx.font = '14px Microsoft YaHei';
        ctx.textAlign = 'center';
        ctx.fillText('使用主窗口控制轮播 | 方向键切换文件', canvas.width / 2, canvas.height - 35);
        ctx.fillText('点击画中画窗口可返回主窗口', canvas.width / 2, canvas.height - 15);

      } catch (error) {
        console.error('绘制画中画内容失败:', error);
        this.drawErrorContent(ctx, canvas);
      }
    },
    drawHtmlContent(ctx, canvas) {
      // 绘制文件路径
      ctx.fillStyle = '#666666';
      ctx.font = '12px Microsoft YaHei';
      ctx.textAlign = 'left';
      const filePath = this.selectedFile?.path || '';
      const displayPath = filePath.length > 60 ? '...' + filePath.substring(filePath.length - 60) : filePath;
      ctx.fillText(displayPath, 30, 210);

      // 绘制内容预览区域
      const contentY = 230;
      const contentHeight = canvas.height - 290;
      const lineHeight = 20;
      const maxLines = Math.floor(contentHeight / lineHeight);

      // 尝试获取实际的HTML内容
      const htmlViewer = this.$refs.htmlViewer;
      let hasRealContent = false;

      if (htmlViewer && htmlViewer.$el) {
        const iframe = htmlViewer.$el.querySelector('iframe');
        if (iframe) {
          try {
            // 尝试获取iframe内容（可能因跨域限制失败）
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (iframeDoc) {
              const bodyText = iframeDoc.body ? iframeDoc.body.innerText : '';
              if (bodyText) {
                this.drawRealContent(ctx, bodyText, contentY, maxLines, lineHeight);
                hasRealContent = true;
              }
            }
          } catch (error) {
            console.warn('无法访问iframe内容（跨域限制）:', error);
          }
        }
      }

      // 如果无法获取真实内容，绘制模拟内容
      if (!hasRealContent) {
        this.drawMockContent(ctx, contentY, maxLines, lineHeight);
      }
    },
    drawRealContent(ctx, content, startY, maxLines, lineHeight) {
      ctx.fillStyle = '#333333';
      ctx.font = '14px Microsoft YaHei';
      ctx.textAlign = 'left';

      const lines = content.split('\n').slice(0, maxLines);
      lines.forEach((line, index) => {
        const y = startY + index * lineHeight;

        // 截断过长的行
        if (line.length > 50) {
          line = line.substring(0, 50) + '...';
        }

        ctx.fillText(line, 30, y);
      });
    },
    drawMockContent(ctx, startY, maxLines, lineHeight) {
      ctx.fillStyle = '#333333';
      ctx.font = '14px Microsoft YaHei';
      ctx.textAlign = 'left';

      // 根据当前文件生成更有用的内容
      const fileName = this.selectedFile?.name || '未知文件';
      const fileType = fileName.split('.').pop()?.toLowerCase() || 'unknown';

      let mockLines = [
        `📄 ${fileName}`,
        '',
        `文件类型: ${fileType.toUpperCase()}`,
        `当前进度: ${this.currentFileIndex + 1}/${this.currentFiles.length}`,
        '',
        '📊 阅读统计:',
        `• 总文件数: ${this.currentFiles.length}`,
        `• 已阅读: ${this.readingHistory.length}`,
        `• 轮播间隔: ${this.carouselSettings.interval}秒`,
        '',
        '🎮 快捷键:',
        '• ← → 切换文件',
        '• ↑ ↓ 上下切换',
        '• 空格 暂停/继续',
        '',
        '💡 提示: 在主窗口中可以查看',
        '完整的文档内容和进行交互操作。'
      ];

      // 如果是轮播模式，添加轮播信息
      if (this.carouselSettings.enabled) {
        mockLines.splice(10, 0,
          '🔄 轮播模式已启用',
          `• 方向: ${this.getDirectionText()}`,
          `• 循环: ${this.carouselSettings.loop ? '是' : '否'}`
        );
      }

      mockLines.slice(0, maxLines).forEach((line, index) => {
        const y = startY + index * lineHeight;
        ctx.fillText(line, 30, y);
      });
    },
    getDirectionText() {
      switch (this.carouselSettings.direction) {
        case 'forward': return '向前';
        case 'backward': return '向后';
        case 'random': return '随机';
        default: return '未知';
      }
    },
    drawErrorContent(ctx, canvas) {
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.fillStyle = '#E6A23C';
      ctx.font = 'bold 24px Microsoft YaHei';
      ctx.textAlign = 'center';
      ctx.fillText('⚠️ 内容加载中...', canvas.width / 2, canvas.height / 2);

      ctx.fillStyle = '#666666';
      ctx.font = '16px Microsoft YaHei';
      ctx.fillText('请稍候或返回主窗口查看完整内容', canvas.width / 2, canvas.height / 2 + 40);
    },


    checkPipSupport() {
      // 检查浏览器是否支持画中画
      this.pipSupported = 'pictureInPictureEnabled' in document &&
                         document.pictureInPictureEnabled &&
                         'requestPictureInPicture' in HTMLVideoElement.prototype;

      console.log('画中画支持检测:', {
        pictureInPictureEnabled: 'pictureInPictureEnabled' in document,
        documentPipEnabled: document.pictureInPictureEnabled,
        videoSupport: 'requestPictureInPicture' in HTMLVideoElement.prototype,
        finalSupport: this.pipSupported
      });
    },

  },
  created() {
    this.menuItems = menuData;

    // 检查移动端
    this.checkMobile();

    // 检查画中画支持
    this.checkPipSupport();

    // 从本地存储加载轮播设置
    const savedSettings = localStorage.getItem('carouselSettings');
    if (savedSettings) {
      try {
        this.carouselSettings = { ...this.carouselSettings, ...JSON.parse(savedSettings) };
      } catch (e) {
        console.warn('Failed to load carousel settings from localStorage:', e);
      }
    }

    // 检查是否有通过查询参数指定要查看的文件
    const filePath = this.$route.query.filePath;
    if (filePath) {
      // 查找对应的文件
      const allFiles = [];
      const traverse = (items) => {
        for (const item of items) {
          if (item.files) {
            allFiles.push(...item.files);
          }
          if (item.children) {
            traverse(item.children);
          }
        }
      };
      traverse(this.menuItems);

      // 找到匹配的文件并查看它
      const fileToView = allFiles.find(file => file.path === filePath);
      if (fileToView) {
        this.viewFile(fileToView);
      }
    }
  },
  mounted() {
    // 添加键盘事件监听
    window.addEventListener('keydown', this.handleKeyDown);

    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize);

    // 添加触摸事件监听（移动端）
    if (this.isMobile) {
      document.addEventListener('touchstart', this.handleTouchStart, { passive: true });
      document.addEventListener('touchend', this.handleTouchEnd, { passive: true });
    }
  },
  beforeDestroy() {
    // 移除键盘事件监听
    window.removeEventListener('keydown', this.handleKeyDown);

    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize);

    // 移除触摸事件监听
    document.removeEventListener('touchstart', this.handleTouchStart);
    document.removeEventListener('touchend', this.handleTouchEnd);

    // 停止轮播
    this.stopCarousel();

    // 退出画中画
    if (this.pipMode) {
      this.exitPictureInPicture();
    }
  }
}
</script>

<style scoped>
.note-list-layout {
  display: flex;
  height: 100vh;
  background-color: var(--bg-color);
}

.sidebar {
  width: 240px;
  background-color: var(--sidebar-bg);
  color: #fff;
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.sidebar-header {
  margin-bottom: 40px;
  padding-left: 10px;
}

.sidebar-title {
  font-size: 22px;
  font-weight: 500;
  margin: 0;
  letter-spacing: 0.5px;
}

.el-menu {
  border-right: none;
  margin-top: 10px;
}

.el-menu-item {
  font-size: 15px;
  border-radius: 8px;
  margin-bottom: 8px;
  height: 44px;
  line-height: 44px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-menu-item i {
  margin-right: 12px;
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.el-menu-item.is-active {
  background-color: var(--primary-color-light) !important;
  color: #fff !important;
  transform: translateX(5px);
}

.el-menu-item:hover {
  background-color: var(--primary-color-dark) !important;
  transform: translateX(5px);
}

.main-content {
  flex-grow: 1;
  overflow-y: auto;
  background-color: var(--bg-color);
  display: flex;
  flex-direction: column;
}

.container {
  flex-grow: 1;
  padding: 20px 60px 60px;
  display: flex;
  flex-direction: column;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.page-title {
  font-size: 32px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-color);
  letter-spacing: -0.5px;
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-light);
  margin: 0;
  font-weight: 400;
}

.create-btn {
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.create-btn:hover {
  transform: translateY(-3px);
  filter: brightness(1.1);
}

.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 40px;
}

.note-card-wrapper {
  height: 78%;
  /* The fade-in will be applied to the note-item itself */
}

.note-item {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 30px;
  border-radius: 16px;
  border: 1px solid transparent;
  background-color: var(--card-bg);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
}

.note-item:hover {
  transform: translateY(-8px);
  border-color: var(--primary-color);
  box-shadow: 0 20px 30px -15px rgba(52, 152, 219, 0.15);
}

/* 已读笔记样式 */
.note-item-read {
  background-color: var(--card-bg-read);
  border-color: rgba(52, 152, 219, 0.2);
  opacity: 0.85;
}

.note-item-read .note-title {
  color: var(--text-color-read);
  position: relative;
}

.note-item-read .note-content {
  color: var(--text-light-read);
}

.note-item-read:hover {
  opacity: 1;
  transform: translateY(-6px);
  box-shadow: 0 15px 25px -10px rgba(52, 152, 219, 0.1);
}

/* 已读指示器 */
.read-indicator {
  color: var(--primary-color);
  margin-left: 8px;
  font-size: 16px;
  opacity: 0.7;
}

/* 阅读时间显示 */
.read-time {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  opacity: 0.8;
}

.note-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  color: var(--text-color);
  letter-spacing: -0.3px;
}

.note-content {
  flex-grow: 1;
  margin-bottom: 24px;
  font-size: 15px;
  color: var(--text-light);
  line-height: 1.7;
  opacity: 0.8;
}

.note-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  color: var(--text-light);
}

.note-meta {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.note-meta i {
  margin-right: 8px;
}

.delete-btn {
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.note-item:hover .delete-btn {
  opacity: 1;
  transform: translateX(0);
}

.empty-state {
  margin-top: 60px;
  padding: 40px;
}

.empty-text {
  margin-top: 10px;
  margin-bottom: 20px;
  color: var(--text-light);
}

/* HTML 阅读器样式 */
.html-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.html-viewer-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  flex-shrink: 0;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.viewer-title {
  font-size: 24px;
  font-weight: 500;
  margin: 0 0 0 20px;
  color: var(--text-color);
  letter-spacing: -0.3px;
  flex-grow: 1;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-left: auto;
}

.carousel-controls {
  display: flex;
  gap: 8px;
}

.carousel-controls .el-button {
  padding: 8px 12px;
  font-size: 12px;
}

.reading-progress {
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
  transition: all 0.3s ease;
}

.reading-progress:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.progress-text {
  letter-spacing: 0.5px;
}

.html-viewer-content {
  flex-grow: 1;
  border-radius: 16px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative; /* 为导航箭头定位提供参考 */
}

.html-viewer-content.pip-mode {
  border: 3px solid #409EFF;
  box-shadow: 0 0 20px rgba(64, 158, 255, 0.3);
}

.html-viewer-content.pip-mode::before {
  content: '📺 画中画模式';
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(64, 158, 255, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 100;
  pointer-events: none;
}

/* 导航箭头样式 */
.navigation-arrows {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 允许点击穿透到iframe */
  z-index: 10;
}

.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto; /* 恢复箭头的点击事件 */
  opacity: 0.7;
}

.nav-arrow:hover {
  opacity: 1;
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.nav-arrow i {
  font-size: 24px;
  color: var(--primary-color);
}

.nav-arrow-left {
  left: 20px;
}

.nav-arrow-right {
  right: 20px;
}



.nav-arrow-disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-arrow-disabled:hover {
  transform: translateY(-50%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}



/* 打分组件样式 */
.rating-container {
  margin-top: 40px;
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-top: 1px solid var(--border-color);
}

.rating-title {
  font-size: 16px;
  color: var(--text-light);
  margin-bottom: 20px;
  font-weight: 400;
  letter-spacing: 0.3px;
}

.rating-stars {
  display: flex;
  gap: 12px;
  margin-bottom: -100px;
}

.rating-star {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.rating-star i {
  font-size: 24px;
  color: #e0e0e0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.rating-star.active i {
  color: var(--primary-color);
}

.rating-star.hover i {
  color: var(--primary-color);
  opacity: 0.8;
}

.rating-star:hover {
  transform: scale(1.15);
}

.rating-value {
  font-size: 28px;
  font-weight: 500;
  color: var(--primary-color);
  margin-top: 5px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rating-success {
  margin-top: 15px;
  color: #67c23a;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background-color: rgba(103, 194, 58, 0.1);
  border-radius: 20px;
}

.rating-success i {
  font-size: 16px;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(52, 152, 219, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  margin-bottom: 20px;
}

.loading-text {
  color: var(--text-light);
  font-size: 16px;
  font-weight: 400;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 列表动画 */
.note-list-enter-active, .note-list-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
.note-list-enter, .note-list-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(40px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 对话框样式 */
.create-note-dialog {
  border-radius: 16px;
  overflow: hidden;
}

.create-note-dialog .el-dialog__header {
  padding: 25px 25px 15px;
  text-align: center;
}

.create-note-dialog .el-dialog__body {
  padding: 20px 30px 30px;
}

.create-note-dialog .el-dialog__footer {
  padding: 15px 30px 25px;
  text-align: right;
}

/* 轮播设置对话框样式 */
.carousel-settings {
  padding: 20px 0;
}

.carousel-settings .el-form-item {
  margin-bottom: 30px;
}

.carousel-settings .el-form-item__label {
  font-weight: 500;
  color: var(--text-color);
}

.setting-description {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 8px;
  line-height: 1.4;
}

.carousel-settings .el-slider {
  margin: 10px 0;
}

.carousel-settings .el-radio-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.carousel-settings .el-radio {
  margin-right: 0;
  margin-bottom: 8px;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 画中画相关样式 */
.pip-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pip-info .el-tag {
  align-self: flex-start;
}

.carousel-controls .el-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.carousel-controls .el-button[disabled]:hover {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

/* 移动端样式 */
.mobile-layout {
  flex-direction: column;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 998;
  transition: opacity 0.3s ease;
}

.sidebar-mobile {
  position: fixed;
  top: 0;
  left: -280px;
  width: 280px;
  height: 100vh;
  z-index: 999;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-mobile.sidebar-visible {
  left: 0;
}

.sidebar-close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  color: #fff;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.sidebar-close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.mobile-menu-btn,
.mobile-home-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.mobile-menu-btn:hover,
.mobile-home-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  flex-grow: 1;
  text-align: center;
}

.mobile-viewer-header {
  flex-direction: column;
  gap: 15px;
  align-items: stretch;
}

.mobile-viewer-title {
  font-size: 18px;
  margin: 0;
  text-align: center;
  order: -1;
}

.mobile-header-controls {
  flex-direction: column;
  gap: 10px;
  align-items: center;
  width: 100%;
}

.mobile-carousel-controls {
  justify-content: center;
  width: 100%;
}

.mobile-reading-progress {
  order: -1;
  margin-bottom: 10px;
}

/* 移动端底部导航栏 */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid var(--border-color);
  display: flex;
  padding: 10px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  padding-bottom: calc(10px + env(safe-area-inset-bottom, 0px));
}

.mobile-nav-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  background: none;
  border: none;
  color: var(--text-color);
  font-size: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  touch-action: manipulation;
  min-height: 60px;
}

.mobile-nav-btn:not(:disabled):active {
  background-color: var(--primary-color-light);
  color: white;
  transform: scale(0.95);
}

.mobile-nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.mobile-nav-btn i {
  font-size: 20px;
  margin-bottom: 4px;
}

.mobile-nav-center {
  background-color: var(--primary-color);
  color: white;
  margin: 0 8px;
  border-radius: 12px;
}

.mobile-nav-center:not(:disabled):active {
  background-color: var(--primary-color-dark);
}

/* 移动端沉浸式阅读模式 */
.mobile-reading-mode {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 1000 !important;
  background-color: #fff !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

.mobile-reading-mode + .rating-container {
  display: none;
}

/* 当开启沉浸式阅读模式时，隐藏其他元素 */
.mobile-layout:has(.mobile-reading-mode) .mobile-header,
.mobile-layout:has(.mobile-reading-mode) .html-viewer-header,
.mobile-layout:has(.mobile-reading-mode) .mobile-bottom-nav {
  display: none;
}

/* 移动端浮动控制按钮 */
.mobile-floating-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1001;
}

.floating-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(64, 158, 255, 0.9);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  -webkit-tap-highlight-color: transparent;
}

.floating-btn:active {
  transform: scale(0.9);
  background-color: rgba(64, 158, 255, 1);
}

.floating-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.floating-btn:disabled:active {
  transform: none;
}

@media (max-width: 768px) {
  .note-list-layout:not(.mobile-layout) .sidebar {
    display: none;
  }

  .main-content {
    width: 100%;
    padding: 0;
  }

  .container {
    padding: 20px 15px 40px;
  }

  .notes-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .note-item {
    padding: 20px;
    border-radius: 12px;
  }

  .note-title {
    font-size: 16px;
  }

  .note-content {
    font-size: 14px;
  }

  .rating-stars {
    gap: 6px;
    justify-content: center;
  }

  .rating-star i {
    font-size: 18px;
  }

  .html-viewer-content {
    border-radius: 8px;
    margin: 0 -5px;
  }

  .nav-arrow {
    width: 40px;
    height: 40px;
  }

  .nav-arrow i {
    font-size: 18px;
  }

  .nav-arrow-left {
    left: 10px;
  }

  .nav-arrow-right {
    right: 10px;
  }

  .carousel-settings .el-form-item__label {
    font-size: 14px;
  }

  .carousel-settings .el-radio-group {
    gap: 8px;
  }

  .list-header {
    margin-bottom: 30px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .list-header-right {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  /* 移动端触摸优化 */
  .note-item {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .nav-arrow {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }

  .rating-star {
    touch-action: manipulation;
    padding: 4px;
    -webkit-tap-highlight-color: transparent;
  }

  .mobile-nav-btn {
    -webkit-tap-highlight-color: transparent;
  }

  .mobile-menu-btn,
  .mobile-home-btn,
  .sidebar-close-btn {
    -webkit-tap-highlight-color: transparent;
  }

  /* 移动端滚动优化 */
  .main-content {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .html-viewer-content {
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端文本选择优化 */
  .note-title,
  .note-content {
    -webkit-user-select: text;
    user-select: text;
  }

  /* 移动端动画性能优化 */
  .note-item,
  .nav-arrow,
  .mobile-nav-btn {
    will-change: transform;
  }

  .sidebar-mobile {
    will-change: left;
  }

  /* 移动端字体大小调整 */
  .sidebar-title {
    font-size: 20px;
  }

  .el-menu-item {
    font-size: 14px;
    height: 40px;
    line-height: 40px;
  }

  /* 移动端对话框调整 */
  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .carousel-settings {
    padding: 10px 0;
  }

  .carousel-settings .el-form-item {
    margin-bottom: 20px;
  }
}
</style> 