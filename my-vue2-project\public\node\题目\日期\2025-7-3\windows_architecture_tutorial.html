<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Windows操作系统架构风格学习</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h1, h2 {
            color: #0078d7;
            text-align: center;
        }
        
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .explanation {
            background-color: #e6f2ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .canvas-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        canvas {
            background-color: #fff;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 15px 0;
        }
        
        button {
            background-color: #0078d7;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #005a9e;
        }
        
        .highlight {
            background-color: #fffacd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .quiz-container {
            margin-top: 30px;
            padding: 15px;
            background-color: #f0f8ff;
            border-radius: 8px;
        }
        
        .quiz-options {
            margin-top: 10px;
        }
        
        .quiz-option {
            display: block;
            margin: 10px 0;
            padding: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .quiz-option:hover {
            background-color: #f0f0f0;
        }
        
        .correct {
            background-color: #dff0d8;
            border-color: #d6e9c6;
        }
        
        .incorrect {
            background-color: #f2dede;
            border-color: #ebccd1;
        }
        
        .feedback {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Windows操作系统架构风格学习</h1>
        
        <div class="explanation">
            <h2>Windows图形用户界面的核心架构风格</h2>
            <p>Windows操作系统在图形用户界面处理方面采用的核心架构风格是<span class="highlight">事件驱动</span>风格。</p>
            <p>事件驱动架构的特点：</p>
            <ul>
                <li>系统首先注册事件处理的回调函数</li>
                <li>当某个事件发生时（如鼠标点击、键盘输入），系统会查找并调用对应的处理函数</li>
                <li>程序不需要持续轮询，只在事件发生时才执行相应代码</li>
                <li>这种架构非常适合用户界面程序的开发</li>
            </ul>
            <p>Java语言同样采用事件驱动的特性，在Java虚拟机上运行，通过虚拟机屏蔽不同硬件环境的差异。</p>
        </div>
        
        <div class="canvas-container">
            <canvas id="demoCanvas" width="1000" height="400"></canvas>
        </div>
        
        <div class="controls">
            <button id="clickEvent">模拟鼠标点击</button>
            <button id="keyEvent">模拟键盘输入</button>
            <button id="resetDemo">重置演示</button>
        </div>
        
        <div class="quiz-container">
            <h2>快速测验</h2>
            <p>Windows操作系统在图形用户界面处理方面采用的核心架构风格是：</p>
            <div class="quiz-options">
                <div class="quiz-option" data-correct="true">A. 事件驱动</div>
                <div class="quiz-option">B. 管道-过滤器</div>
                <div class="quiz-option">C. 分层架构</div>
                <div class="quiz-option">D. 微内核-扩展</div>
            </div>
            <div class="feedback"></div>
        </div>
    </div>

    <script>
        // 获取Canvas元素和上下文
        const canvas = document.getElementById('demoCanvas');
        const ctx = canvas.getContext('2d');
        
        // 定义颜色
        const colors = {
            background: '#f0f8ff',
            system: '#0078d7',
            event: '#ff6347',
            callback: '#4caf50',
            text: '#333333',
            highlight: '#ffeb3b'
        };
        
        // 系统组件
        const system = {
            x: 150,
            y: 100,
            width: 700,
            height: 250,
            draw: function() {
                ctx.fillStyle = colors.background;
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.strokeStyle = colors.system;
                ctx.lineWidth = 3;
                ctx.strokeRect(this.x, this.y, this.width, this.height);
                
                // 标题
                ctx.fillStyle = colors.system;
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.fillText('Windows 操作系统', this.x + 20, this.y + 30);
                
                // 事件队列
                ctx.fillStyle = colors.text;
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('事件队列', this.x + 20, this.y + 70);
                
                // 事件处理器
                ctx.fillText('事件处理器', this.x + 500, this.y + 70);
            }
        };
        
        // 事件队列
        const eventQueue = {
            x: 180,
            y: 150,
            width: 200,
            height: 150,
            events: [],
            maxEvents: 5,
            draw: function() {
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.strokeStyle = '#666666';
                ctx.lineWidth = 1;
                ctx.strokeRect(this.x, this.y, this.width, this.height);
                
                // 绘制事件
                for (let i = 0; i < this.events.length; i++) {
                    const event = this.events[i];
                    ctx.fillStyle = event.color;
                    ctx.fillRect(this.x + 10, this.y + 10 + i * 30, this.width - 20, 25);
                    ctx.fillStyle = '#ffffff';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(event.name, this.x + 20, this.y + 27 + i * 30);
                }
            },
            addEvent: function(name, color) {
                if (this.events.length < this.maxEvents) {
                    this.events.push({ name, color });
                }
            },
            removeEvent: function() {
                if (this.events.length > 0) {
                    return this.events.shift();
                }
                return null;
            }
        };
        
        // 回调函数表
        const callbackTable = {
            x: 500,
            y: 150,
            width: 300,
            height: 150,
            callbacks: [
                { event: '鼠标点击', handler: '处理鼠标点击' },
                { event: '键盘输入', handler: '处理键盘输入' },
                { event: '窗口调整', handler: '处理窗口大小' }
            ],
            activeRow: -1,
            draw: function() {
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.strokeStyle = '#666666';
                ctx.lineWidth = 1;
                ctx.strokeRect(this.x, this.y, this.width, this.height);
                
                // 表头
                ctx.fillStyle = '#eeeeee';
                ctx.fillRect(this.x, this.y, this.width, 30);
                ctx.fillStyle = colors.text;
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.fillText('事件类型', this.x + 20, this.y + 20);
                ctx.fillText('回调函数', this.x + 170, this.y + 20);
                
                // 分隔线
                ctx.beginPath();
                ctx.moveTo(this.x + 150, this.y);
                ctx.lineTo(this.x + 150, this.y + this.height);
                ctx.stroke();
                
                // 表格内容
                ctx.font = '14px Microsoft YaHei';
                for (let i = 0; i < this.callbacks.length; i++) {
                    const y = this.y + 30 + i * 30;
                    
                    // 高亮当前激活的行
                    if (i === this.activeRow) {
                        ctx.fillStyle = colors.highlight;
                        ctx.fillRect(this.x, y, this.width, 30);
                    }
                    
                    ctx.fillStyle = colors.text;
                    ctx.fillText(this.callbacks[i].event, this.x + 20, y + 20);
                    ctx.fillText(this.callbacks[i].handler, this.x + 170, y + 20);
                    
                    // 行分隔线
                    ctx.beginPath();
                    ctx.moveTo(this.x, y + 30);
                    ctx.lineTo(this.x + this.width, y + 30);
                    ctx.stroke();
                }
            },
            highlightRow: function(eventType) {
                for (let i = 0; i < this.callbacks.length; i++) {
                    if (this.callbacks[i].event === eventType) {
                        this.activeRow = i;
                        return;
                    }
                }
                this.activeRow = -1;
            }
        };
        
        // 动画对象
        let animation = {
            currentEvent: null,
            eventMoving: false,
            eventX: 0,
            eventY: 0,
            targetX: 0,
            targetY: 0,
            speed: 5,
            processing: false,
            processingTime: 0,
            maxProcessingTime: 30,
            showResult: false,
            resultTime: 0,
            maxResultTime: 60,
            result: '',
            update: function() {
                if (this.eventMoving) {
                    // 移动事件到目标位置
                    const dx = this.targetX - this.eventX;
                    const dy = this.targetY - this.eventY;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance > this.speed) {
                        this.eventX += (dx / distance) * this.speed;
                        this.eventY += (dy / distance) * this.speed;
                    } else {
                        this.eventX = this.targetX;
                        this.eventY = this.targetY;
                        this.eventMoving = false;
                        this.processing = true;
                    }
                } else if (this.processing) {
                    this.processingTime++;
                    if (this.processingTime >= this.maxProcessingTime) {
                        this.processing = false;
                        this.showResult = true;
                        this.processingTime = 0;
                        
                        if (this.currentEvent.name === '鼠标点击') {
                            this.result = '处理完成：显示上下文菜单';
                        } else if (this.currentEvent.name === '键盘输入') {
                            this.result = '处理完成：显示输入的字符';
                        }
                    }
                } else if (this.showResult) {
                    this.resultTime++;
                    if (this.resultTime >= this.maxResultTime) {
                        this.showResult = false;
                        this.resultTime = 0;
                        this.currentEvent = null;
                        callbackTable.activeRow = -1;
                    }
                }
            },
            draw: function() {
                if (this.eventMoving && this.currentEvent) {
                    // 绘制移动中的事件
                    ctx.fillStyle = this.currentEvent.color;
                    ctx.fillRect(this.eventX, this.eventY, 180, 25);
                    ctx.fillStyle = '#ffffff';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(this.currentEvent.name, this.eventX + 10, this.eventY + 17);
                }
                
                if (this.processing) {
                    // 绘制处理指示器
                    ctx.fillStyle = colors.callback;
                    ctx.beginPath();
                    ctx.arc(650, 300, 20, 0, Math.PI * 2 * (this.processingTime / this.maxProcessingTime));
                    ctx.lineTo(650, 300);
                    ctx.fill();
                    
                    ctx.fillStyle = colors.text;
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('处理中...', 620, 340);
                }
                
                if (this.showResult) {
                    // 显示处理结果
                    ctx.fillStyle = 'rgba(76, 175, 80, 0.8)';
                    ctx.fillRect(400, 320, 300, 40);
                    ctx.fillStyle = '#ffffff';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText(this.result, 420, 345);
                }
            },
            startEventAnimation: function(event) {
                this.currentEvent = event;
                this.eventMoving = true;
                this.eventX = eventQueue.x + 10;
                this.eventY = eventQueue.y + 10;
                this.targetX = callbackTable.x + 20;
                this.targetY = callbackTable.y + 60 + callbackTable.activeRow * 30;
            }
        };
        
        // 初始化和绘制场景
        function drawScene() {
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制系统组件
            system.draw();
            eventQueue.draw();
            callbackTable.draw();
            
            // 绘制动画
            animation.update();
            animation.draw();
            
            // 继续动画
            requestAnimationFrame(drawScene);
        }
        
        // 初始化
        drawScene();
        
        // 事件处理
        document.getElementById('clickEvent').addEventListener('click', function() {
            if (!animation.currentEvent) {
                const event = { name: '鼠标点击', color: '#ff6347' };
                eventQueue.addEvent(event.name, event.color);
                callbackTable.highlightRow('鼠标点击');
                setTimeout(() => {
                    const removedEvent = eventQueue.removeEvent();
                    if (removedEvent) {
                        animation.startEventAnimation(removedEvent);
                    }
                }, 500);
            }
        });
        
        document.getElementById('keyEvent').addEventListener('click', function() {
            if (!animation.currentEvent) {
                const event = { name: '键盘输入', color: '#2196f3' };
                eventQueue.addEvent(event.name, event.color);
                callbackTable.highlightRow('键盘输入');
                setTimeout(() => {
                    const removedEvent = eventQueue.removeEvent();
                    if (removedEvent) {
                        animation.startEventAnimation(removedEvent);
                    }
                }, 500);
            }
        });
        
        document.getElementById('resetDemo').addEventListener('click', function() {
            eventQueue.events = [];
            animation.currentEvent = null;
            animation.eventMoving = false;
            animation.processing = false;
            animation.showResult = false;
            callbackTable.activeRow = -1;
        });
        
        // 测验功能
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.addEventListener('click', function() {
                const isCorrect = this.getAttribute('data-correct') === 'true';
                const feedback = document.querySelector('.feedback');
                
                // 移除之前的样式
                document.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.classList.remove('correct', 'incorrect');
                });
                
                // 添加新样式
                if (isCorrect) {
                    this.classList.add('correct');
                    feedback.textContent = '正确！Windows操作系统在图形用户界面处理方面采用的是事件驱动架构风格。';
                    feedback.style.backgroundColor = '#dff0d8';
                } else {
                    this.classList.add('incorrect');
                    feedback.textContent = '不正确，请再试一次。提示：考虑用户界面如何响应鼠标点击和键盘输入。';
                    feedback.style.backgroundColor = '#f2dede';
                }
                
                feedback.style.display = 'block';
            });
        });
    </script>
</body>
</html> 