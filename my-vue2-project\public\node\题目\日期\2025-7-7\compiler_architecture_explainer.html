<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编译器架构风格详解</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        .problem-section, .explanation-section, .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 6px;
            border: 1px solid #eee;
        }
        .question-text {
            background-color: #e8f0f8;
            padding: 15px;
            border-left: 5px solid #3498db;
            margin-bottom: 20px;
        }
        .concept-card {
            background-color: #ffffff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .concept-card h4 {
            color: #34495e;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        canvas {
            display: block;
            background-color: #fff;
            border: 1px solid #ccc;
            margin-top: 20px;
            border-radius: 4px;
        }
        .controls {
            margin-top: 15px;
        }
        .controls button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin-right: 10px;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #45a049;
        }
        .answer-section {
            background-color: #e6f7ee;
            padding: 20px;
            border-left: 5px solid #27ae60;
            margin-top: 40px;
            border-radius: 6px;
        }
        .answer-section h3 {
            color: #27ae60;
            border-bottom: none;
        }
        .answer-section strong {
            color: #1a7b45;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>编译器架构风格详解</h1>

        <div class="problem-section">
            <h2>原问题</h2>
            <div class="question-text">
                <p>"编译器"是一种非常重要的基础软件，其核心功能是对源代码形态的单个或一组源程序依次进行预处理、词法分析、语法分析、语义分析、代码生成、代码优化等处理，最终生成目标机器的可执行代码。考虑以下与编译器相关的软件架构设计场景：</p>
                <p>传统的编译器设计中，上述处理过程都以独立功能模块的形式存在，程序源代码作为一个整体，依次在不同模块中进行传递，最终完成编译过程。针对这种设计思路，传统的编译器采用（请作答此空）架构风格比较合适。</p>
                <p>随着编译、链接、调试、执行等开发过程的一体化趋势发展，集成开发环境(IDE.随之出现。IDE集成了编译器、连接器、调试器等多种工具，支持代码的增量修改与处理，能够实现不同工具之间的信息交互，覆盖整个软件开发生命周期。针对这种需求，IDE采用（ ）架构风格比较合适。IDE强调交互式编程，用户在修改程序代码后，会同时触发语法高亮显示、语法错误提示、程序结构更新等多种功能的调用与结果呈现，针对这种需求，通常采用（ ）架构风格比较合适。</p>
                <p>某公司已经开发了一款针对某种嵌入式操作系统专用编程语言的IDE，随着一种新的嵌入式操作系统上市并迅速占领市场，公司决定对IDE进行适应性改造，支持采用现有编程语言进行编程，生成符合新操作系统要求的运行代码，并能够在现有操作系统上模拟出新操作系统的运行环境，以支持代码调试工作。针对上述要求，为了使IDE能够生成符合新操作系统要求的运行代码，采用基于（ ）的架构设计策略比较合适；为了模拟新操作系统的运行环境，通常采用（ ）架构风格比较合适。</p>
            </div>
        </div>

        <div class="explanation-section">
            <h2>知识点与交互演示</h2>

            <div class="concept-card">
                <h4>1. 传统编译器与"顺序批处理"架构风格</h4>
                <p><strong>概念解释:</strong> 顺序批处理（Sequential Batch Processing）架构风格适用于处理一系列独立的、按顺序执行的步骤，其中每个步骤都接收前一步骤的完整输出作为输入，并生成新的完整输出。数据作为一个整体，在不同的处理模块之间依次传递，不需要实时的交互或反馈。</p>
                <p><strong>为什么适用于传统编译器:</strong> 传统编译器将源代码视为一个整体，经过预处理、词法分析、语法分析、语义分析、代码生成、代码优化等一系列独立且顺序的阶段，每个阶段都对前一阶段的完整输出进行处理，最终生成可执行代码。这完美符合顺序批处理的特点。</p>
                <canvas id="sequentialBatchCanvas" width="800" height="200"></canvas>
                <div class="controls">
                    <button onclick="startSequentialBatchDemo()">开始演示</button>
                    <button onclick="resetSequentialBatchDemo()">重置演示</button>
                </div>
            </div>

            <div class="concept-card">
                <h4>2. IDE 与"数据共享"及"隐式调用"架构风格</h4>
                <p><strong>概念解释 - 数据共享（Data-Centered / Repository）:</strong> 数据共享架构风格以中央数据存储（如数据库、文件系统或内存中的数据结构）为核心，所有组件都通过访问和修改这个共享数据来协同工作。这种风格强调数据的独立性和数据的集中管理，适合处理大量需要协同访问和操作的数据的系统。</p>
                <p><strong>为什么适用于 IDE（数据共享）:</strong> IDE 集成了多种工具（编译器、连接器、调试器等），这些工具都需要访问和修改相同的源代码、符号表、错误列表等数据。采用数据共享风格可以实现不同工具之间的高效信息交互和协同工作。</p>
                <canvas id="dataSharingCanvas" width="800" height="300"></canvas>
                <div class="controls">
                    <button onclick="startDataSharingDemo()">开始演示</button>
                    <button onclick="resetDataSharingDemo()">重置演示</button>
                </div>
            </div>

            <div class="concept-card">
                <h4>2. IDE 与"隐式调用"架构风格（续）</h4>
                <p><strong>概念解释 - 隐式调用（Implicit Invocation / Event-Driven）:</strong> 隐式调用架构风格（也称为事件驱动）中，组件不直接调用其他组件，而是通过发布或订阅事件来通信。当某个事件发生时，所有对该事件感兴趣的组件都会被"隐式"地调用。这种风格提高了系统的可扩展性和可维护性，因为组件之间解耦度高。</p>
                <p><strong>为什么适用于 IDE（隐式调用）:</strong> IDE 强调交互式编程，用户对代码的修改（事件）会触发多种功能的并发调用（如语法高亮、错误提示、程序结构更新）。这些功能不需要直接了解彼此，只需响应特定的事件即可。隐式调用非常适合这种高度并发和事件驱动的交互模式。</p>
                <canvas id="implicitInvocationCanvas" width="800" height="300"></canvas>
                <div class="controls">
                    <button onclick="startImplicitInvocationDemo()">开始演示</button>
                    <button onclick="resetImplicitInvocationDemo()">重置演示</button>
                </div>
            </div>

            <div class="concept-card">
                <h4>3. IDE 适应性改造与"适配器"及"虚拟机"架构风格</h4>
                <p><strong>概念解释 - 适配器（Adapter）:</strong> 适配器是一种设计模式，它允许不兼容的接口之间进行协作。在架构层面，适配器风格通过引入一个中间层，将现有系统的接口转换为新系统所需的接口，从而实现两个不兼容系统之间的无缝集成。</p>
                <p><strong>为什么适用于生成新 OS 代码（适配器）:</strong> 为了使现有 IDE 生成符合新操作系统要求的运行代码，需要将现有编程语言的编译输出适配到新操作系统的二进制格式或系统调用接口。适配器模式可以在不修改现有 IDE 核心逻辑的情况下，实现这种兼容性转换。</p>
                <canvas id="adapterCanvas" width="800" height="250"></canvas>
                <div class="controls">
                    <button onclick="startAdapterDemo()">开始演示</button>
                    <button onclick="resetAdapterDemo()">重置演示</button>
                </div>
            </div>

            <div class="concept-card">
                <h4>3. IDE 适应性改造与"虚拟机"架构风格（续）</h4>
                <p><strong>概念解释 - 虚拟机（Virtual Machine）:</strong> 虚拟机架构风格通过在现有硬件或操作系统之上创建一个虚拟的运行环境，来模拟另一个操作系统或硬件平台。这使得应用程序可以在一个与其原生环境不同的环境中运行，提供了良好的隔离性和跨平台能力。</p>
                <p><strong>为什么适用于模拟新 OS 环境（虚拟机）:</strong> 为了在新操作系统上市前或为了调试方便，在现有操作系统上模拟新操作系统的运行环境，虚拟机是理想的选择。它提供了一个独立且受控的沙盒环境，可以在其中安全地运行和调试针对新操作系统的代码，而无需实际部署到新硬件上。</p>
                <canvas id="virtualMachineCanvas" width="800" height="250"></canvas>
                <div class="controls">
                    <button onclick="startVirtualMachineDemo()">开始演示</button>
                    <button onclick="resetVirtualMachineDemo()">重置演示</button>
                </div>
            </div>
        </div>

        <div class="answer-section">
            <h3>答案</h3>
            <p><strong>问题1：</strong>传统的编译器采用（<strong>顺序批处理</strong>）架构风格比较合适。</p>
            <p><strong>解析：</strong>传统编译器设计中，编译处理过程都以独立功能模块的形式存在，程序源代码作为一个整体，依次在不同模块中进行传递，最终完成编译过程。针对这种设计思路，传统的编译器采用顺序批处理架构风格比较合适，因为在顺序批处理架构风格中，数据以整体的方式在不同的处理模块之间传递，符合题目要求。</p>
            <p><strong>问题2：</strong>针对IDE集成多种工具的需求，IDE采用（<strong>数据共享</strong>）架构风格比较合适。IDE强调交互式编程，用户在修改程序代码后，会同时触发语法高亮显示、语法错误提示、程序结构更新等多种功能的调用与结果呈现，针对这种需求，通常采用（<strong>隐式调用</strong>）架构风格比较合适。</p>
            <p><strong>解析：</strong>集成开发环境(IDE)需要面对不同的数据结构，不同的数据类型与形态，在这种以数据为核心的系统中，采用数据共享机制显然是最为合适的。IDE强调交互式编程，用户在修改程序代码后，会同时触发语法高亮显示、语法错误提示、程序结构更新等多种功能的调用与结果呈现，这一需求的核心在于根据事件进行动作响应，采用隐式调用的架构风格最为合适。</p>
            <p><strong>问题3：</strong>为了使IDE能够生成符合新操作系统要求的运行代码，采用基于（<strong>适配器</strong>）的架构设计策略比较合适；为了模拟新操作系统的运行环境，通常采用（<strong>虚拟机</strong>）架构风格比较合适。</p>
            <p><strong>解析：</strong>公司需要对IDE进行适应性改造，支持采用现有编程语言进行编程，生成符合新操作系统要求的运行代码，并能够在现有操作系统上模拟出新操作系统的运行环境，以支持代码调试工作。针对上述要求，为了使IDE能够生成符合新操作系统要求的运行代码，应该是现有操作系统对新系统的一个适配过程，因此应该采用适配器架构设计策略比较合适，模拟新操作系统的运行模式通常会采用虚拟机架构风格。</p>
        </div>
    </div>

    <script>
        // Common drawing functions
        function drawArrow(ctx, fromX, fromY, toX, toY, color = '#333') {
            const headlen = 10; // length of head in pixels
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        function drawBox(ctx, x, y, width, height, text, fillColor = '#3498db', textColor = 'white', borderColor = '#2980b9') {
            ctx.fillStyle = fillColor;
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = borderColor;
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);

            ctx.fillStyle = textColor;
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + width / 2, y + height / 2);
        }

        function drawDataPacket(ctx, x, y, radius, color = '#e74c3c') {
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#c0392b';
            ctx.lineWidth = 1;
            ctx.stroke();
        }

        // Sequential Batch Processing Demo
        const sbCanvas = document.getElementById('sequentialBatchCanvas');
        const sbCtx = sbCanvas.getContext('2d');
        let sbAnimationId;
        let sbDataX = 50;
        const sbModules = [
            { name: '预处理', x: 100, y: 75, width: 100, height: 50 },
            { name: '词法分析', x: 250, y: 75, width: 100, height: 50 },
            { name: '语法分析', x: 400, y: 75, width: 100, height: 50 },
            { name: '语义分析', x: 550, y: 75, width: 100, height: 50 },
            { name: '代码生成', x: 700, y: 75, width: 100, height: 50 }
        ];

        function drawSequentialBatch() {
            sbCtx.clearRect(0, 0, sbCanvas.width, sbCanvas.height);

            // Draw modules
            sbModules.forEach(mod => {
                drawBox(sbCtx, mod.x, mod.y, mod.width, mod.height, mod.name);
            });

            // Draw arrows between modules
            for (let i = 0; i < sbModules.length - 1; i++) {
                drawArrow(sbCtx, sbModules[i].x + sbModules[i].width, sbModules[i].y + sbModules[i].height / 2,
                               sbModules[i+1].x, sbModules[i+1].y + sbModules[i+1].height / 2);
            }

            // Draw data packet
            drawDataPacket(sbCtx, sbDataX, 100, 10);
        }

        function animateSequentialBatch() {
            sbDataX += 2;
            if (sbDataX > sbCanvas.width + 10) {
                sbDataX = 0;
            }
            drawSequentialBatch();
            sbAnimationId = requestAnimationFrame(animateSequentialBatch);
        }

        function startSequentialBatchDemo() {
            if (sbAnimationId) cancelAnimationFrame(sbAnimationId);
            sbDataX = 50;
            animateSequentialBatch();
        }

        function resetSequentialBatchDemo() {
            if (sbAnimationId) cancelAnimationFrame(sbAnimationId);
            sbDataX = 50;
            drawSequentialBatch();
        }
        drawSequentialBatch(); // Initial draw

        // Data Sharing Demo
        const dsCanvas = document.getElementById('dataSharingCanvas');
        const dsCtx = dsCanvas.getContext('2d');
        let dsAnimationId;
        let dsDataOffset = 0;

        function drawDataSharing() {
            dsCtx.clearRect(0, 0, dsCanvas.width, dsCanvas.height);

            // Central Data Repository
            dsCtx.fillStyle = '#f39c12';
            dsCtx.fillRect(300, 100, 200, 100);
            dsCtx.strokeStyle = '#d35400';
            dsCtx.lineWidth = 3;
            dsCtx.strokeRect(300, 100, 200, 100);
            dsCtx.fillStyle = 'white';
            dsCtx.font = '16px Arial';
            dsCtx.textAlign = 'center';
            dsCtx.textBaseline = 'middle';
            dsCtx.fillText('共享数据中心', 400, 150);

            // Tools (Components)
            const tools = [
                { name: '编译器', x: 50, y: 50, width: 100, height: 50 },
                { name: '连接器', x: 50, y: 200, width: 100, height: 50 },
                { name: '调试器', x: 650, y: 50, width: 100, height: 50 },
                { name: '编辑器', x: 650, y: 200, width: 100, height: 50 }
            ];

            tools.forEach(tool => {
                drawBox(dsCtx, tool.x, tool.y, tool.width, tool.height, tool.name, '#2ecc71', 'white', '#27ae60');
                // Arrows to data center
                drawArrow(dsCtx, tool.x + tool.width / 2, tool.y + tool.height, 400, 100 + dsDataOffset, '#2c3e50'); // To data center
                drawArrow(dsCtx, 400, 200 - dsDataOffset, tool.x + tool.width / 2, tool.y, '#2c3e50'); // From data center
            });
        }

        function animateDataSharing() {
            dsDataOffset = (dsDataOffset + 0.5) % 10; // Simple pulsating effect
            drawDataSharing();
            dsAnimationId = requestAnimationFrame(animateDataSharing);
        }

        function startDataSharingDemo() {
            if (dsAnimationId) cancelAnimationFrame(dsAnimationId);
            dsDataOffset = 0;
            animateDataSharing();
        }

        function resetDataSharingDemo() {
            if (dsAnimationId) cancelAnimationFrame(dsAnimationId);
            dsDataOffset = 0;
            drawDataSharing();
        }
        drawDataSharing(); // Initial draw

        // Implicit Invocation Demo
        const iiCanvas = document.getElementById('implicitInvocationCanvas');
        const iiCtx = iiCanvas.getContext('2d');
        let iiAnimationId;
        let iiEventOpacity = 0; // Opacity for the event pulse
        let iiTriggeredComponentIndex = -1; // Which component is "triggered"
        let iiFrameCount = 0;

        const iiEventSource = { x: 400, y: 50, radius: 20, name: '用户修改代码 (事件)' };
        const iiComponents = [
            { name: '语法高亮', x: 100, y: 150, width: 120, height: 50 },
            { name: '语法错误提示', x: 340, y: 150, width: 120, height: 50 },
            { name: '程序结构更新', x: 580, y: 150, width: 120, height: 50 }
        ];

        function drawImplicitInvocation() {
            iiCtx.clearRect(0, 0, iiCanvas.width, iiCanvas.height);

            // Draw event source
            iiCtx.beginPath();
            iiCtx.arc(iiEventSource.x, iiEventSource.y, iiEventSource.radius, 0, Math.PI * 2);
            iiCtx.fillStyle = '#e74c3c';
            iiCtx.fill();
            iiCtx.strokeStyle = '#c0392b';
            iiCtx.lineWidth = 2;
            iiCtx.stroke();
            iiCtx.fillStyle = 'white';
            iiCtx.font = '14px Arial';
            iiCtx.textAlign = 'center';
            iiCtx.textBaseline = 'middle';
            iiCtx.fillText(iiEventSource.name, iiEventSource.x, iiEventSource.y);

            // Draw event pulse
            if (iiEventOpacity > 0) {
                iiCtx.beginPath();
                iiCtx.arc(iiEventSource.x, iiEventSource.y, iiEventSource.radius + (1 - iiEventOpacity) * 30, 0, Math.PI * 2);
                iiCtx.strokeStyle = `rgba(231, 76, 60, ${iiEventOpacity})`;
                iiCtx.lineWidth = 3;
                iiCtx.stroke();
            }

            // Draw components and connections
            iiComponents.forEach((comp, index) => {
                const fillColor = index === iiTriggeredComponentIndex ? '#3498db' : '#95a5a6';
                const borderColor = index === iiTriggeredComponentIndex ? '#2980b9' : '#7f8c8d';
                drawBox(iiCtx, comp.x, comp.y, comp.width, comp.height, comp.name, fillColor, 'white', borderColor);

                // Implied connection / Event flow
                iiCtx.beginPath();
                iiCtx.moveTo(iiEventSource.x, iiEventSource.y + iiEventSource.radius);
                iiCtx.lineTo(comp.x + comp.width / 2, comp.y);
                iiCtx.strokeStyle = '#666';
                iiCtx.setLineDash([5, 5]); // Dashed line for implicit
                iiCtx.stroke();
                iiCtx.setLineDash([]); // Reset line dash
            });

            // Draw triggered effect
            if (iiTriggeredComponentIndex !== -1) {
                const comp = iiComponents[iiTriggeredComponentIndex];
                iiCtx.fillStyle = 'rgba(255, 255, 0, 0.5)'; // Yellow flash
                iiCtx.fillRect(comp.x, comp.y, comp.width, comp.height);
            }
        }

        function animateImplicitInvocation() {
            iiFrameCount++;

            // Event pulse animation
            if (iiFrameCount % 60 === 0) { // Every second
                iiEventOpacity = 1;
                iiTriggeredComponentIndex = -1; // Reset triggered state
            }
            if (iiEventOpacity > 0) {
                iiEventOpacity -= 0.05;
                if (iiEventOpacity <= 0) {
                    iiEventOpacity = 0;
                    // After event, trigger components sequentially
                    if (iiTriggeredComponentIndex < iiComponents.length - 1) {
                        iiTriggeredComponentIndex++;
                    } else {
                        iiTriggeredComponentIndex = -1; // Reset after all triggered
                    }
                }
            }

            drawImplicitInvocation();
            iiAnimationId = requestAnimationFrame(animateImplicitInvocation);
        }

        function startImplicitInvocationDemo() {
            if (iiAnimationId) cancelAnimationFrame(iiAnimationId);
            iiEventOpacity = 0;
            iiTriggeredComponentIndex = -1;
            iiFrameCount = 0;
            animateImplicitInvocation();
        }

        function resetImplicitInvocationDemo() {
            if (iiAnimationId) cancelAnimationFrame(iiAnimationId);
            iiEventOpacity = 0;
            iiTriggeredComponentIndex = -1;
            iiFrameCount = 0;
            drawImplicitInvocation();
        }
        drawImplicitInvocation(); // Initial draw

        // Adapter Demo
        const adapterCanvas = document.getElementById('adapterCanvas');
        const adapterCtx = adapterCanvas.getContext('2d');
        let adapterAnimationId;
        let adapterDataOffset = 0;

        function drawAdapter() {
            adapterCtx.clearRect(0, 0, adapterCanvas.width, adapterCanvas.height);

            // Existing IDE
            drawBox(adapterCtx, 50, 80, 120, 80, '现有 IDE', '#1abc9c', 'white', '#16a085');

            // New OS
            drawBox(adapterCtx, 630, 80, 120, 80, '新操作系统', '#3498db', 'white', '#2980b9');

            // Adapter
            adapterCtx.fillStyle = '#f1c40f';
            adapterCtx.fillRect(300, 60, 200, 120);
            adapterCtx.strokeStyle = '#f39c12';
            adapterCtx.lineWidth = 3;
            adapterCtx.strokeRect(300, 60, 200, 120);
            adapterCtx.fillStyle = '#333';
            adapterCtx.font = '16px Arial';
            adapterCtx.textAlign = 'center';
            adapterCtx.textBaseline = 'middle';
            adapterCtx.fillText('适配器 (Adapter)', 400, 90);
            adapterCtx.font = '12px Arial';
            adapterCtx.fillText('将现有输出转换为新OS格式', 400, 120);
            adapterCtx.fillText('生成符合新OS要求的代码', 400, 150);


            // Arrows
            drawArrow(adapterCtx, 170, 120, 300, 120, '#333'); // IDE to Adapter
            drawArrow(adapterCtx, 500, 120, 630, 120, '#333'); // Adapter to New OS

            // Data flow (packet)
            drawDataPacket(adapterCtx, 50 + adapterDataOffset, 120, 8);
        }

        function animateAdapter() {
            adapterDataOffset += 2;
            if (adapterDataOffset > 700) {
                adapterDataOffset = 0;
            }
            drawAdapter();
            adapterAnimationId = requestAnimationFrame(animateAdapter);
        }

        function startAdapterDemo() {
            if (adapterAnimationId) cancelAnimationFrame(adapterAnimationId);
            adapterDataOffset = 0;
            animateAdapter();
        }

        function resetAdapterDemo() {
            if (adapterAnimationId) cancelAnimationFrame(adapterAnimationId);
            adapterDataOffset = 0;
            drawAdapter();
        }
        drawAdapter(); // Initial draw

        // Virtual Machine Demo
        const vmCanvas = document.getElementById('virtualMachineCanvas');
        const vmCtx = vmCanvas.getContext('2d');
        let vmAnimationId;
        let vmPulse = 0;

        function drawVirtualMachine() {
            vmCtx.clearRect(0, 0, vmCanvas.width, vmCanvas.height);

            // Hardware Layer
            vmCtx.fillStyle = '#bdc3c7';
            vmCtx.fillRect(50, 180, 700, 50);
            vmCtx.strokeStyle = '#95a5a6';
            vmCtx.lineWidth = 2;
            vmCtx.strokeRect(50, 180, 700, 50);
            vmCtx.fillStyle = '#333';
            vmCtx.font = '16px Arial';
            vmCtx.textAlign = 'center';
            vmCtx.textBaseline = 'middle';
            vmCtx.fillText('硬件层 (现有物理机)', 400, 205);

            // Existing OS Layer
            vmCtx.fillStyle = '#95a5a6';
            vmCtx.fillRect(75, 120, 650, 50);
            vmCtx.strokeStyle = '#7f8c8d';
            vmCtx.lineWidth = 2;
            vmCtx.strokeRect(75, 120, 650, 50);
            vmCtx.fillStyle = 'white';
            vmCtx.font = '16px Arial';
            vmCtx.fillText('现有操作系统层 (Host OS)', 400, 145);

            // Virtual Machine Layer (New OS Environment)
            vmCtx.fillStyle = `rgba(52, 152, 219, ${1 - vmPulse})`; // Pulse effect
            vmCtx.fillRect(100, 60, 600, 50);
            vmCtx.strokeStyle = `rgba(41, 128, 185, ${1 - vmPulse})`;
            vmCtx.lineWidth = 3;
            vmCtx.strokeRect(100, 60, 600, 50);
            vmCtx.fillStyle = 'white';
            vmCtx.font = '16px Arial';
            vmCtx.fillText('虚拟机层 (模拟新操作系统环境)', 400, 85);

            // Application Layer (running in VM)
            vmCtx.fillStyle = '#2ecc71';
            vmCtx.fillRect(250, 20, 300, 30);
            vmCtx.strokeStyle = '#27ae60';
            vmCtx.lineWidth = 2;
            vmCtx.strokeRect(250, 20, 300, 30);
            vmCtx.fillStyle = 'white';
            vmCtx.font = '14px Arial';
            vmCtx.fillText('针对新OS的应用/代码调试', 400, 35);

            // Arrows showing interaction
            drawArrow(vmCtx, 400, 50, 400, 60, '#333'); // App to VM
            drawArrow(vmCtx, 400, 110, 400, 120, '#333'); // VM to Host OS
            drawArrow(vmCtx, 400, 170, 400, 180, '#333'); // Host OS to Hardware
        }

        function animateVirtualMachine() {
            vmPulse = (Math.sin(Date.now() / 300) + 1) / 2; // Oscillates between 0 and 1
            drawVirtualMachine();
            vmAnimationId = requestAnimationFrame(animateVirtualMachine);
        }

        function startVirtualMachineDemo() {
            if (vmAnimationId) cancelAnimationFrame(vmAnimationId);
            animateVirtualMachine();
        }

        function resetVirtualMachineDemo() {
            if (vmAnimationId) cancelAnimationFrame(vmAnimationId);
            vmPulse = 0;
            drawVirtualMachine();
        }
        drawVirtualMachine(); // Initial draw

    </script>
</body>
</html> 