<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计约束演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f4f7f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        .explanation {
            background: #e9f7fd;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .highlight {
            background-color: #fffacd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .example-box {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            background-color: #f9f9f9;
        }
        .example-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .constraint {
            color: #e74c3c;
        }
        .requirement {
            color: #27ae60;
        }
        .interactive-demo {
            margin: 20px 0;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .scenario-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background-color: #fff;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .scenario-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .scenario-card.selected {
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
        }
        .scenario-text {
            margin-bottom: 10px;
        }
        .scenario-type {
            font-weight: bold;
            text-align: right;
        }
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .feedback.correct {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .feedback.incorrect {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .btn-check {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
            transition: background-color 0.3s;
        }
        .btn-check:hover {
            background-color: #2980b9;
        }
        .btn-check:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .tab-container {
            margin-top: 20px;
        }
        .tab-buttons {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        .tab-button {
            padding: 10px 20px;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
        }
        .tab-button.active {
            background-color: white;
            font-weight: bold;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 5px;
            background-color: white;
        }
        .tab-content.active {
            display: block;
        }
        .database-demo {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }
        .db-option {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: #fff;
        }
        .db-option-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .db-option-title {
            font-weight: bold;
            font-size: 18px;
        }
        .db-option-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .badge-constraint {
            background-color: #f8d7da;
            color: #721c24;
        }
        .badge-option {
            background-color: #d4edda;
            color: #155724;
        }
        .db-option-features {
            margin-top: 10px;
        }
        .db-option-feature {
            margin-bottom: 5px;
        }
        .db-option-actions {
            margin-top: 15px;
            text-align: right;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .message.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .message.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .message.info {
            background-color: #e9f7fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .project-status {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .status-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status-item {
            margin-bottom: 5px;
        }
        .status-good {
            color: #28a745;
        }
        .status-warning {
            color: #ffc107;
        }
        .status-bad {
            color: #dc3545;
        }
        .flow-diagram {
            margin: 20px 0;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-align: center;
        }
        .flow-step {
            display: inline-block;
            width: 150px;
            padding: 10px;
            margin: 0 10px;
            background-color: #e9f7fd;
            border: 1px solid #bee5eb;
            border-radius: 5px;
        }
        .flow-arrow {
            display: inline-block;
            margin: 0 5px;
            font-size: 20px;
        }
        .constraint-box {
            border: 2px dashed #e74c3c;
            padding: 10px;
            margin: 15px 0;
            border-radius: 5px;
            background-color: #fff;
        }
        .constraint-title {
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>设计约束演示</h1>
        
        <div class="explanation">
            <h2>什么是设计约束？</h2>
            <p>设计约束（Design Constraints）是在软件开发过程中，<strong>限制系统设计和实现方式的条件或规则</strong>。这些约束通常来自于外部因素，不是系统本身的功能要求。</p>
            <p>设计约束与功能需求和非功能需求不同，它们是<strong>必须遵守的限制条件</strong>，而不是系统应该做什么或如何做。</p>
        </div>
        
        <div class="example-box">
            <div class="example-title">设计约束的特点：</div>
            <ul>
                <li>通常由<strong>外部因素</strong>决定，而不是用户的直接需求</li>
                <li>对设计和实现方式施加<strong>限制</strong>，而不是描述系统的功能或品质</li>
                <li>开发团队<strong>必须遵守</strong>，没有选择的余地</li>
                <li>往往与<strong>法律、政策、标准、兼容性</strong>等因素相关</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>为什么"使用国有知识产权的数据库"属于设计约束？</h2>
        
        <div class="explanation">
            <p>"使用国有知识产权的数据库"是一个典型的设计约束，因为：</p>
            <ol>
                <li>它<strong>限制了</strong>系统可以使用的数据库技术选择</li>
                <li>这个限制来自于<strong>外部政策或规定</strong>，而不是系统本身的功能需求</li>
                <li>它是<strong>必须遵守</strong>的规则，开发团队没有选择的余地</li>
                <li>它与<strong>知识产权保护和国家政策</strong>相关，而不是系统的功能或性能</li>
            </ol>
        </div>

        <div class="comparison-table">
            <table>
                <tr>
                    <th>类型</th>
                    <th>描述</th>
                    <th>示例</th>
                </tr>
                <tr>
                    <td>功能需求</td>
                    <td>系统应该做什么</td>
                    <td class="requirement">系统应该能够存储和检索用户数据</td>
                </tr>
                <tr>
                    <td>非功能需求</td>
                    <td>系统应该如何做</td>
                    <td class="requirement">数据库查询响应时间不应超过1秒</td>
                </tr>
                <tr>
                    <td>设计约束</td>
                    <td>限制系统设计和实现方式的条件</td>
                    <td class="constraint">系统必须使用国有知识产权的数据库</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="container">
        <h2>设计约束的实际影响</h2>
        
        <div class="flow-diagram">
            <div class="flow-step">确定系统需求</div>
            <div class="flow-arrow">→</div>
            <div class="flow-step">设计系统架构</div>
            <div class="flow-arrow">→</div>
            <div class="flow-step">选择技术栈</div>
            <div class="flow-arrow">→</div>
            <div class="flow-step">实现系统</div>
            
            <div class="constraint-box" style="margin-top: 20px; width: 70%; margin-left: auto; margin-right: auto;">
                <div class="constraint-title">设计约束: 必须使用国有知识产权的数据库</div>
                <p>这个约束限制了技术选择的范围，无论其他需求如何，都必须遵守这个规则。</p>
            </div>
        </div>
        
        <div class="interactive-demo">
            <h3>数据库选择演示</h3>
            <p>在这个演示中，你将看到设计约束如何影响技术选择决策。假设你正在为一个政府项目选择数据库：</p>
            
            <div class="database-demo">
                <div class="db-option">
                    <div class="db-option-header">
                        <div class="db-option-title">MySQL</div>
                        <div class="db-option-badge badge-option">可选项</div>
                    </div>
                    <div class="db-option-features">
                        <div class="db-option-feature">✓ 开源免费</div>
                        <div class="db-option-feature">✓ 全球广泛使用</div>
                        <div class="db-option-feature">✓ 性能优秀</div>
                        <div class="db-option-feature">✗ 非国有知识产权</div>
                    </div>
                    <div class="db-option-actions">
                        <button class="btn-check" id="select-mysql">选择MySQL</button>
                    </div>
                </div>
                
                <div class="db-option">
                    <div class="db-option-header">
                        <div class="db-option-title">Oracle Database</div>
                        <div class="db-option-badge badge-option">可选项</div>
                    </div>
                    <div class="db-option-features">
                        <div class="db-option-feature">✓ 企业级稳定性</div>
                        <div class="db-option-feature">✓ 高级功能丰富</div>
                        <div class="db-option-feature">✗ 价格昂贵</div>
                        <div class="db-option-feature">✗ 非国有知识产权</div>
                    </div>
                    <div class="db-option-actions">
                        <button class="btn-check" id="select-oracle">选择Oracle</button>
                    </div>
                </div>
                
                <div class="db-option">
                    <div class="db-option-header">
                        <div class="db-option-title">达梦数据库 (DM)</div>
                        <div class="db-option-badge badge-constraint">符合约束</div>
                    </div>
                    <div class="db-option-features">
                        <div class="db-option-feature">✓ 国有知识产权</div>
                        <div class="db-option-feature">✓ 符合国家安全要求</div>
                        <div class="db-option-feature">✓ 技术支持本地化</div>
                        <div class="db-option-feature">✗ 生态系统相对较小</div>
                    </div>
                    <div class="db-option-actions">
                        <button class="btn-check" id="select-dm">选择达梦</button>
                    </div>
                </div>
                
                <div class="db-option">
                    <div class="db-option-header">
                        <div class="db-option-title">人大金仓数据库 (KingbaseES)</div>
                        <div class="db-option-badge badge-constraint">符合约束</div>
                    </div>
                    <div class="db-option-features">
                        <div class="db-option-feature">✓ 国有知识产权</div>
                        <div class="db-option-feature">✓ 兼容PostgreSQL</div>
                        <div class="db-option-feature">✓ 国家信息安全认证</div>
                        <div class="db-option-feature">✗ 部分高级功能有限</div>
                    </div>
                    <div class="db-option-actions">
                        <button class="btn-check" id="select-kingbase">选择人大金仓</button>
                    </div>
                </div>
            </div>
            
            <div class="message error" id="constraint-message" style="display: none;">
                错误：该选择不符合"必须使用国有知识产权的数据库"的设计约束！
            </div>
            
            <div class="message success" id="success-message" style="display: none;">
                成功：该选择符合"必须使用国有知识产权的数据库"的设计约束！
            </div>
            
            <div class="project-status">
                <div class="status-title">项目状态</div>
                <div class="status-item" id="status-constraint">设计约束符合度: <span class="status-warning">未选择数据库</span></div>
                <div class="status-item" id="status-performance">性能需求满足度: <span class="status-warning">未选择数据库</span></div>
                <div class="status-item" id="status-ecosystem">生态系统成熟度: <span class="status-warning">未选择数据库</span></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>设计约束与其他需求的区别</h2>
        
        <div class="tab-container">
            <div class="tab-buttons">
                <div class="tab-button active" data-tab="tab-constraints">设计约束</div>
                <div class="tab-button" data-tab="tab-functional">功能需求</div>
                <div class="tab-button" data-tab="tab-non-functional">非功能需求</div>
            </div>
            
            <div class="tab-content active" id="tab-constraints">
                <h4>设计约束示例</h4>
                <ul>
                    <li class="constraint">系统必须使用国有知识产权的数据库</li>
                    <li class="constraint">系统必须在Windows Server 2019上运行</li>
                    <li class="constraint">系统必须符合GDPR数据保护规定</li>
                    <li class="constraint">系统必须使用Java 11开发</li>
                    <li class="constraint">系统必须与现有的ERP系统集成</li>
                </ul>
                <p>
                    设计约束是<strong>必须遵守的限制条件</strong>，通常由外部因素决定，如政策、法规、组织标准等。它们限制了系统的设计和实现方式，而不是描述系统的功能或品质。
                </p>
            </div>
            
            <div class="tab-content" id="tab-functional">
                <h4>功能需求示例</h4>
                <ul>
                    <li class="requirement">系统应该能够存储用户数据</li>
                    <li class="requirement">系统应该允许用户搜索记录</li>
                    <li class="requirement">系统应该生成月度报表</li>
                    <li class="requirement">系统应该支持用户权限管理</li>
                    <li class="requirement">系统应该能够导出数据为Excel格式</li>
                </ul>
                <p>
                    功能需求描述系统<strong>应该做什么</strong>，即系统应该提供的功能或服务。它们定义了系统的行为，用户可以使用系统做什么。
                </p>
            </div>
            
            <div class="tab-content" id="tab-non-functional">
                <h4>非功能需求示例</h4>
                <ul>
                    <li class="requirement">系统响应时间不应超过1秒</li>
                    <li class="requirement">系统应该能够同时处理1000个用户的请求</li>
                    <li class="requirement">系统应该有99.9%的可用性</li>
                    <li class="requirement">系统应该易于使用，新用户培训时间不超过2小时</li>
                    <li class="requirement">系统应该使用SSL加密所有传输的数据</li>
                </ul>
                <p>
                    非功能需求描述系统<strong>应该如何做</strong>，即系统应该具有的品质属性或特性。它们定义了系统的品质和约束，而不是具体的功能。
                </p>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>总结</h2>
        
        <div class="explanation">
            <p>"使用国有知识产权的数据库"属于设计约束，因为：</p>
            <ol>
                <li>它<strong>限制了技术选择</strong>，而不是描述系统功能或品质</li>
                <li>它来自于<strong>外部政策或规定</strong>，而不是用户的直接需求</li>
                <li>它是<strong>必须遵守的规则</strong>，开发团队没有选择的余地</li>
                <li>它与<strong>国家安全和知识产权保护</strong>相关，这些是外部因素</li>
            </ol>
            <p>理解设计约束对于项目规划和资源分配非常重要，因为它们会直接影响技术选择、开发流程和项目成本。</p>
        </div>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有标签页的活动状态
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // 设置当前标签页为活动状态
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });
        
        // 数据库选择演示
        const selectMysql = document.getElementById('select-mysql');
        const selectOracle = document.getElementById('select-oracle');
        const selectDm = document.getElementById('select-dm');
        const selectKingbase = document.getElementById('select-kingbase');
        const constraintMessage = document.getElementById('constraint-message');
        const successMessage = document.getElementById('success-message');
        const statusConstraint = document.getElementById('status-constraint');
        const statusPerformance = document.getElementById('status-performance');
        const statusEcosystem = document.getElementById('status-ecosystem');
        
        selectMysql.addEventListener('click', function() {
            constraintMessage.style.display = 'block';
            successMessage.style.display = 'none';
            statusConstraint.innerHTML = '设计约束符合度: <span class="status-bad">不符合</span>';
            statusPerformance.innerHTML = '性能需求满足度: <span class="status-good">优秀</span>';
            statusEcosystem.innerHTML = '生态系统成熟度: <span class="status-good">优秀</span>';
        });
        
        selectOracle.addEventListener('click', function() {
            constraintMessage.style.display = 'block';
            successMessage.style.display = 'none';
            statusConstraint.innerHTML = '设计约束符合度: <span class="status-bad">不符合</span>';
            statusPerformance.innerHTML = '性能需求满足度: <span class="status-good">优秀</span>';
            statusEcosystem.innerHTML = '生态系统成熟度: <span class="status-good">优秀</span>';
        });
        
        selectDm.addEventListener('click', function() {
            constraintMessage.style.display = 'none';
            successMessage.style.display = 'block';
            statusConstraint.innerHTML = '设计约束符合度: <span class="status-good">符合</span>';
            statusPerformance.innerHTML = '性能需求满足度: <span class="status-warning">良好</span>';
            statusEcosystem.innerHTML = '生态系统成熟度: <span class="status-warning">一般</span>';
        });
        
        selectKingbase.addEventListener('click', function() {
            constraintMessage.style.display = 'none';
            successMessage.style.display = 'block';
            statusConstraint.innerHTML = '设计约束符合度: <span class="status-good">符合</span>';
            statusPerformance.innerHTML = '性能需求满足度: <span class="status-warning">良好</span>';
            statusEcosystem.innerHTML = '生态系统成熟度: <span class="status-warning">一般</span>';
        });
    </script>

</body>
</html> 