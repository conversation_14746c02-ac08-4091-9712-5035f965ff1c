<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CISC vs RISC 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .question-card {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            padding: 20px;
            border: 2px solid #e0e6ed;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .option.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .option.correct {
            border-color: #27ae60;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }

        .option.wrong {
            border-color: #e74c3c;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        canvas {
            width: 100%;
            height: 400px;
            border-radius: 12px;
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .explanation {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            backdrop-filter: blur(10px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            display: none;
            opacity: 0;
        }

        .explanation.show {
            display: block;
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .concept-card {
            background: linear-gradient(135deg, #f8f9ff, #e8f0ff);
            border-radius: 16px;
            padding: 30px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .concept-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .concept-content {
            font-size: 1.1rem;
            line-height: 1.7;
            color: #34495e;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            width: 0%;
            transition: width 1s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">CISC vs RISC</h1>
            <p class="subtitle">复杂指令集 vs 精简指令集 - 交互式学习体验</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                以下关于CISC(Complex Instruction Set Computer，复杂指令集计算机)和RISC(Reduced Instruction Set Computer，精简指令集计算机)的叙述中，<strong>错误的是</strong>：
            </div>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 在CISC中，复杂指令都采用硬布线逻辑来执行
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 一般而言，采用CISC技术的CPU，其芯片设计复杂度更高
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 在RISC中，更适合采用硬布线逻辑执行指令
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 采用RISC技术，指令系统中的指令种类和寻址方式更少
                </div>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="checkAnswer()">提交答案</button>
                <button class="btn btn-secondary" onclick="showAnimation()">观看动画演示</button>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="animationCanvas"></canvas>
            <div class="controls">
                <button class="btn btn-primary" onclick="startCISCDemo()">CISC演示</button>
                <button class="btn btn-secondary" onclick="startRISCDemo()">RISC演示</button>
                <button class="btn btn-primary" onclick="compareDemo()">对比演示</button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h2 style="color: #2c3e50; margin-bottom: 30px; font-size: 2rem;">📚 知识解析</h2>
            
            <div class="concept-card">
                <div class="concept-title">🔍 正确答案：A</div>
                <div class="concept-content">
                    <strong>错误原因：</strong>在CISC中，复杂指令通常采用<strong>微程序技术</strong>来执行，而不是硬布线逻辑。每条复杂指令都需要通过执行一段解释性微程序才能完成，这需要多个CPU周期。
                </div>
            </div>

            <div class="concept-card">
                <div class="concept-title">🏗️ CISC特点</div>
                <div class="concept-content">
                    • <strong>指令复杂：</strong>指令数目通常300条以上，有的超过500条<br>
                    • <strong>微程序控制：</strong>复杂指令通过微程序技术执行<br>
                    • <strong>多周期执行：</strong>一条指令需要多个CPU周期<br>
                    • <strong>设计复杂：</strong>芯片设计复杂度高，研制周期长
                </div>
            </div>

            <div class="concept-card">
                <div class="concept-title">⚡ RISC特点</div>
                <div class="concept-content">
                    • <strong>指令简单：</strong>指令数目少，功能简化<br>
                    • <strong>硬布线控制：</strong>适合采用硬布线逻辑执行指令<br>
                    • <strong>单周期执行：</strong>指令能在单周期内执行<br>
                    • <strong>设计简化：</strong>降低硬件设计复杂度
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAnswer = null;
        let animationRunning = false;
        let canvas, ctx;
        let currentStep = 0;

        // 初始化
        window.onload = function() {
            canvas = document.getElementById('animationCanvas');
            ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = 400;
            
            // 绑定选项点击事件
            document.querySelectorAll('.option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedAnswer = this.dataset.answer;
                });
            });

            drawInitialState();
        };

        function drawInitialState() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击下方按钮开始动画演示', canvas.width/2, canvas.height/2);
            
            ctx.font = '16px Arial';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText('通过动画了解CISC和RISC的工作原理', canvas.width/2, canvas.height/2 + 40);
        }

        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }

            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                if (option.dataset.answer === 'A') {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== 'A') {
                    option.classList.add('wrong');
                }
            });

            // 显示解析
            document.getElementById('explanation').classList.add('show');
            
            // 更新进度条
            document.getElementById('progressFill').style.width = '100%';

            setTimeout(() => {
                if (selectedAnswer === 'A') {
                    alert('🎉 恭喜答对了！CISC中复杂指令采用微程序技术执行，而不是硬布线逻辑。');
                } else {
                    alert('❌ 答案错误。正确答案是A。CISC中复杂指令采用微程序技术执行。');
                }
            }, 500);
        }

        function showAnimation() {
            document.getElementById('explanation').classList.add('show');
            document.getElementById('progressFill').style.width = '50%';
            compareDemo();
        }

        function startCISCDemo() {
            if (animationRunning) return;
            animationRunning = true;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                ctx.fillStyle = '#fff5f5';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 标题
                ctx.fillStyle = '#e74c3c';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('CISC - 复杂指令集计算机', canvas.width/2, 30);
                
                // 绘制复杂指令
                drawComplexInstruction(step);
                
                // 绘制微程序执行过程
                drawMicroprogramExecution(step);
                
                step++;
                if (step < 100) {
                    requestAnimationFrame(animate);
                } else {
                    animationRunning = false;
                }
            };
            
            animate();
        }

        function startRISCDemo() {
            if (animationRunning) return;
            animationRunning = true;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                ctx.fillStyle = '#f0fff4';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 标题
                ctx.fillStyle = '#27ae60';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('RISC - 精简指令集计算机', canvas.width/2, 30);
                
                // 绘制简单指令
                drawSimpleInstruction(step);
                
                // 绘制硬布线执行过程
                drawHardwiredExecution(step);
                
                step++;
                if (step < 100) {
                    requestAnimationFrame(animate);
                } else {
                    animationRunning = false;
                }
            };
            
            animate();
        }

        function compareDemo() {
            if (animationRunning) return;
            animationRunning = true;
            
            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 分割线
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(canvas.width/2, 0);
                ctx.lineTo(canvas.width/2, canvas.height);
                ctx.stroke();
                
                // CISC部分
                ctx.fillStyle = '#fff5f5';
                ctx.fillRect(0, 0, canvas.width/2, canvas.height);
                
                ctx.fillStyle = '#e74c3c';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('CISC', canvas.width/4, 25);
                
                // RISC部分
                ctx.fillStyle = '#f0fff4';
                ctx.fillRect(canvas.width/2, 0, canvas.width/2, canvas.height);
                
                ctx.fillStyle = '#27ae60';
                ctx.fillText('RISC', canvas.width*3/4, 25);
                
                // 对比动画
                drawComparison(step);
                
                step++;
                if (step < 200) {
                    requestAnimationFrame(animate);
                } else {
                    animationRunning = false;
                }
            };
            
            animate();
        }

        function drawComplexInstruction(step) {
            const x = 50;
            const y = 80;
            
            // 复杂指令框
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(x, y, 200, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('复杂指令 (300+条)', x + 10, y + 25);
            
            // 微程序存储器
            if (step > 20) {
                ctx.fillStyle = '#f39c12';
                ctx.fillRect(x, y + 60, 200, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('微程序存储器', x + 10, y + 85);
                ctx.fillText('微指令序列', x + 10, y + 105);
                ctx.fillText('多周期执行', x + 10, y + 125);
            }
            
            // 执行流程箭头
            if (step > 40) {
                drawArrow(x + 100, y + 40, x + 100, y + 60, '#e74c3c');
            }
        }

        function drawMicroprogramExecution(step) {
            if (step < 60) return;
            
            const cycles = Math.floor((step - 60) / 10) + 1;
            const maxCycles = 4;
            
            for (let i = 0; i < Math.min(cycles, maxCycles); i++) {
                ctx.fillStyle = i < cycles - 1 ? '#27ae60' : '#f39c12';
                ctx.fillRect(50 + i * 50, 200, 40, 30);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`周期${i+1}`, 70 + i * 50, 220);
            }
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('需要多个CPU周期完成一条指令', 50, 260);
        }

        function drawSimpleInstruction(step) {
            const x = 50;
            const y = 80;
            
            // 简单指令框
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(x, y, 200, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('简单指令 (精简)', x + 10, y + 25);
            
            // 硬布线控制器
            if (step > 20) {
                ctx.fillStyle = '#3498db';
                ctx.fillRect(x, y + 60, 200, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('硬布线控制器', x + 10, y + 85);
                ctx.fillText('直接执行', x + 10, y + 105);
            }
            
            // 执行流程箭头
            if (step > 40) {
                drawArrow(x + 100, y + 40, x + 100, y + 60, '#27ae60');
            }
        }

        function drawHardwiredExecution(step) {
            if (step < 60) return;
            
            // 单周期执行
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(50, 200, 80, 30);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('单周期', 90, 220);
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('一个CPU周期完成一条指令', 50, 260);
        }

        function drawComparison(step) {
            const leftX = canvas.width/4;
            const rightX = canvas.width*3/4;
            const y = 60;
            
            // CISC特点
            if (step > 20) {
                ctx.fillStyle = '#e74c3c';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('• 指令复杂 (300+)', leftX, y + 20);
                ctx.fillText('• 微程序控制', leftX, y + 40);
                ctx.fillText('• 多周期执行', leftX, y + 60);
                ctx.fillText('• 设计复杂', leftX, y + 80);
            }
            
            // RISC特点
            if (step > 40) {
                ctx.fillStyle = '#27ae60';
                ctx.fillText('• 指令简单', rightX, y + 20);
                ctx.fillText('• 硬布线控制', rightX, y + 40);
                ctx.fillText('• 单周期执行', rightX, y + 60);
                ctx.fillText('• 设计简化', rightX, y + 80);
            }
            
            // 性能对比动画
            if (step > 80) {
                const ciscHeight = 120 + Math.sin(step * 0.1) * 20;
                const riscHeight = 80 + Math.sin(step * 0.15) * 10;
                
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(leftX - 30, 300 - ciscHeight, 60, ciscHeight);
                
                ctx.fillStyle = '#27ae60';
                ctx.fillRect(rightX - 30, 300 - riscHeight, 60, riscHeight);
                
                ctx.fillStyle = '#2c3e50';
                ctx.font = '12px Arial';
                ctx.fillText('复杂度', leftX, 320);
                ctx.fillText('执行效率', rightX, 320);
            }
        }

        function drawArrow(fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }

        // 窗口大小调整
        window.addEventListener('resize', function() {
            canvas.width = canvas.offsetWidth;
            if (!animationRunning) {
                drawInitialState();
            }
        });
    </script>
</body>
</html>
