<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构件分类方法</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .intro {
            margin-bottom: 30px;
            line-height: 1.6;
            text-align: center;
            color: #555;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
        }
        
        .tab-btn {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: none;
            outline: none;
            transition: 0.3s;
            font-size: 16px;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }
        
        .tab-btn:hover {
            background-color: #ddd;
        }
        
        .tab-btn.active {
            background-color: #4CAF50;
            color: white;
        }
        
        .tab-content {
            display: none;
            padding: 20px;
            animation: fadeIn 0.5s;
            border: 1px solid #ddd;
            border-radius: 0 0 5px 5px;
        }
        
        @keyframes fadeIn {
            from {opacity: 0;}
            to {opacity: 1;}
        }
        
        .tab-content.active {
            display: block;
        }
        
        .canvas-container {
            margin: 20px 0;
            text-align: center;
        }
        
        canvas {
            border: 1px solid #ddd;
            background-color: #fff;
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        button {
            padding: 8px 16px;
            margin: 0 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: 0.3s;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-left: 4px solid #4CAF50;
            border-radius: 0 5px 5px 0;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>构件分类方法</h1>
        
        <p class="intro">在基于构件的软件开发中，构件分类方法可以归纳为三大类。通过动画和交互，您将了解每种方法的特点和应用场景。</p>
        
        <div class="tabs">
            <button class="tab-btn active" onclick="openTab('keyword')">关键字分类法</button>
            <button class="tab-btn" onclick="openTab('facet')">刻面分类法</button>
            <button class="tab-btn" onclick="openTab('hypertext')">超文本方法</button>
        </div>
        
        <div id="keyword" class="tab-content active">
            <h2>关键字分类法</h2>
            <p>根据领域分析的结果将应用领域的概念按照从抽象到具体的顺序逐次分解为树形或有向无回路图结构。</p>
            <div class="canvas-container">
                <canvas id="keywordCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button onclick="startKeywordAnimation()">演示分类过程</button>
                <button onclick="resetKeywordCanvas()">重置</button>
            </div>
            <div class="explanation">
                <h3>关键字分类法的特点：</h3>
                <ul>
                    <li>层次结构清晰，从抽象到具体</li>
                    <li>适合有明确分类体系的领域</li>
                    <li>便于查找和组织构件</li>
                </ul>
            </div>
        </div>
        
        <div id="facet" class="tab-content">
            <h2>刻面分类法</h2>
            <p>利用Facet（刻面）描述构件执行的功能、被操作的数据、构件应用的语境或任意其他特征。</p>
            <div class="canvas-container">
                <canvas id="facetCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button onclick="startFacetAnimation()">演示刻面分类</button>
                <button onclick="resetFacetCanvas()">重置</button>
            </div>
            <div class="explanation">
                <h3>刻面分类法的特点：</h3>
                <ul>
                    <li>多维度描述构件特性</li>
                    <li>更灵活的分类方式</li>
                    <li>适合复杂构件的分类需求</li>
                </ul>
            </div>
        </div>
        
        <div id="hypertext" class="tab-content">
            <h2>超文本方法</h2>
            <p>基于全文检索技术，使得检索者在阅读文档过程中可以按照人类的联想思维方式任意跳转到包含相关概念或构件的文档。</p>
            <div class="canvas-container">
                <canvas id="hypertextCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button onclick="startHypertextAnimation()">演示超文本检索</button>
                <button onclick="resetHypertextCanvas()">重置</button>
                <button onclick="interactHypertext()">交互体验</button>
            </div>
            <div class="explanation">
                <h3>超文本方法的特点：</h3>
                <ul>
                    <li>非线性浏览和检索</li>
                    <li>关联性强，符合人类思维</li>
                    <li>适合大规模构件库的检索</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页功能
        function openTab(tabName) {
            const tabContents = document.getElementsByClassName("tab-content");
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove("active");
            }
            
            const tabButtons = document.getElementsByClassName("tab-btn");
            for (let i = 0; i < tabButtons.length; i++) {
                tabButtons[i].classList.remove("active");
            }
            
            document.getElementById(tabName).classList.add("active");
            event.currentTarget.classList.add("active");
            
            // 重置当前标签页的画布
            if (tabName === "keyword") {
                resetKeywordCanvas();
            } else if (tabName === "facet") {
                resetFacetCanvas();
            } else if (tabName === "hypertext") {
                resetHypertextCanvas();
            }
        }
        
        // 关键字分类法 Canvas 动画
        const keywordCanvas = document.getElementById("keywordCanvas");
        const keywordCtx = keywordCanvas.getContext("2d");
        let keywordAnimationId;
        
        function resetKeywordCanvas() {
            if (keywordAnimationId) {
                cancelAnimationFrame(keywordAnimationId);
            }
            keywordCtx.clearRect(0, 0, keywordCanvas.width, keywordCanvas.height);
            drawKeywordTree();
        }
        
        function drawKeywordTree() {
            keywordCtx.fillStyle = "#333";
            keywordCtx.font = "bold 16px Arial";
            keywordCtx.textAlign = "center";
            
            // 绘制根节点
            keywordCtx.fillStyle = "#4CAF50";
            roundRect(keywordCtx, 350, 30, 100, 40, 5, true);
            keywordCtx.fillStyle = "white";
            keywordCtx.fillText("软件构件", 400, 55);
            
            // 绘制第一层节点
            keywordCtx.fillStyle = "#2196F3";
            roundRect(keywordCtx, 200, 120, 100, 40, 5, true);
            roundRect(keywordCtx, 400, 120, 100, 40, 5, true);
            roundRect(keywordCtx, 600, 120, 100, 40, 5, true);
            
            keywordCtx.fillStyle = "white";
            keywordCtx.fillText("UI构件", 250, 145);
            keywordCtx.fillText("业务构件", 450, 145);
            keywordCtx.fillText("数据构件", 650, 145);
            
            // 绘制连线
            keywordCtx.strokeStyle = "#999";
            keywordCtx.beginPath();
            keywordCtx.moveTo(400, 70);
            keywordCtx.lineTo(250, 120);
            keywordCtx.moveTo(400, 70);
            keywordCtx.lineTo(450, 120);
            keywordCtx.moveTo(400, 70);
            keywordCtx.lineTo(650, 120);
            keywordCtx.stroke();
            
            // 绘制第二层节点（简化版）
            keywordCtx.fillStyle = "#FF9800";
            roundRect(keywordCtx, 150, 210, 100, 40, 5, true);
            roundRect(keywordCtx, 250, 210, 100, 40, 5, true);
            roundRect(keywordCtx, 400, 210, 100, 40, 5, true);
            roundRect(keywordCtx, 600, 210, 100, 40, 5, true);
            
            keywordCtx.fillStyle = "white";
            keywordCtx.fillText("表单", 200, 235);
            keywordCtx.fillText("图表", 300, 235);
            keywordCtx.fillText("业务逻辑", 450, 235);
            keywordCtx.fillText("数据访问", 650, 235);
            
            // 绘制第二层连线
            keywordCtx.beginPath();
            keywordCtx.moveTo(250, 160);
            keywordCtx.lineTo(200, 210);
            keywordCtx.moveTo(250, 160);
            keywordCtx.lineTo(300, 210);
            keywordCtx.moveTo(450, 160);
            keywordCtx.lineTo(450, 210);
            keywordCtx.moveTo(650, 160);
            keywordCtx.lineTo(650, 210);
            keywordCtx.stroke();
        }
        
        function startKeywordAnimation() {
            resetKeywordCanvas();
            
            let step = 0;
            let highlight = (node, color) => {
                keywordCtx.fillStyle = color;
                if (node === "root") {
                    roundRect(keywordCtx, 350, 30, 100, 40, 5, true);
                    keywordCtx.fillStyle = "white";
                    keywordCtx.fillText("软件构件", 400, 55);
                } else if (node === "ui") {
                    roundRect(keywordCtx, 200, 120, 100, 40, 5, true);
                    keywordCtx.fillStyle = "white";
                    keywordCtx.fillText("UI构件", 250, 145);
                } else if (node === "business") {
                    roundRect(keywordCtx, 400, 120, 100, 40, 5, true);
                    keywordCtx.fillStyle = "white";
                    keywordCtx.fillText("业务构件", 450, 145);
                } else if (node === "data") {
                    roundRect(keywordCtx, 600, 120, 100, 40, 5, true);
                    keywordCtx.fillStyle = "white";
                    keywordCtx.fillText("数据构件", 650, 145);
                } else if (node === "form") {
                    roundRect(keywordCtx, 150, 210, 100, 40, 5, true);
                    keywordCtx.fillStyle = "white";
                    keywordCtx.fillText("表单", 200, 235);
                } else if (node === "chart") {
                    roundRect(keywordCtx, 250, 210, 100, 40, 5, true);
                    keywordCtx.fillStyle = "white";
                    keywordCtx.fillText("图表", 300, 235);
                } else if (node === "logic") {
                    roundRect(keywordCtx, 400, 210, 100, 40, 5, true);
                    keywordCtx.fillStyle = "white";
                    keywordCtx.fillText("业务逻辑", 450, 235);
                } else if (node === "dataAccess") {
                    roundRect(keywordCtx, 600, 210, 100, 40, 5, true);
                    keywordCtx.fillStyle = "white";
                    keywordCtx.fillText("数据访问", 650, 235);
                }
            };
            
            function animate() {
                if (step === 0) {
                    highlight("root", "#ff5722");
                } else if (step === 10) {
                    drawKeywordTree();
                    highlight("ui", "#ff5722");
                } else if (step === 20) {
                    drawKeywordTree();
                    highlight("business", "#ff5722");
                } else if (step === 30) {
                    drawKeywordTree();
                    highlight("data", "#ff5722");
                } else if (step === 40) {
                    drawKeywordTree();
                    highlight("form", "#ff5722");
                } else if (step === 50) {
                    drawKeywordTree();
                    highlight("chart", "#ff5722");
                } else if (step === 60) {
                    drawKeywordTree();
                    highlight("logic", "#ff5722");
                } else if (step === 70) {
                    drawKeywordTree();
                    highlight("dataAccess", "#ff5722");
                } else if (step >= 80) {
                    drawKeywordTree();
                    step = -1;
                }
                
                step++;
                keywordAnimationId = requestAnimationFrame(animate);
            }
            
            animate();
        }
        
        // 刻面分类法 Canvas 动画
        const facetCanvas = document.getElementById("facetCanvas");
        const facetCtx = facetCanvas.getContext("2d");
        let facetAnimationId;
        
        function resetFacetCanvas() {
            if (facetAnimationId) {
                cancelAnimationFrame(facetAnimationId);
            }
            facetCtx.clearRect(0, 0, facetCanvas.width, facetCanvas.height);
            drawFacetDiagram();
        }
        
        function drawFacetDiagram() {
            facetCtx.fillStyle = "#333";
            facetCtx.font = "bold 16px Arial";
            facetCtx.textAlign = "center";
            
            // 绘制中心构件
            facetCtx.fillStyle = "#4CAF50";
            roundRect(facetCtx, 350, 150, 100, 60, 5, true);
            facetCtx.fillStyle = "white";
            facetCtx.fillText("构件", 400, 185);
            
            // 绘制四个刻面
            facetCtx.fillStyle = "#2196F3";
            roundRect(facetCtx, 200, 50, 120, 50, 5, true);
            facetCtx.fillStyle = "white";
            facetCtx.fillText("功能刻面", 260, 80);
            
            facetCtx.fillStyle = "#FF9800";
            roundRect(facetCtx, 200, 250, 120, 50, 5, true);
            facetCtx.fillStyle = "white";
            facetCtx.fillText("数据刻面", 260, 280);
            
            facetCtx.fillStyle = "#E91E63";
            roundRect(facetCtx, 550, 50, 120, 50, 5, true);
            facetCtx.fillStyle = "white";
            facetCtx.fillText("语境刻面", 610, 80);
            
            facetCtx.fillStyle = "#9C27B0";
            roundRect(facetCtx, 550, 250, 120, 50, 5, true);
            facetCtx.fillStyle = "white";
            facetCtx.fillText("其他特征", 610, 280);
            
            // 绘制连线
            facetCtx.strokeStyle = "#999";
            facetCtx.beginPath();
            facetCtx.moveTo(400, 150);
            facetCtx.lineTo(260, 100);
            facetCtx.moveTo(400, 210);
            facetCtx.lineTo(260, 250);
            facetCtx.moveTo(450, 150);
            facetCtx.lineTo(610, 100);
            facetCtx.moveTo(450, 210);
            facetCtx.lineTo(610, 250);
            facetCtx.stroke();
            
            // 添加说明文字
            facetCtx.fillStyle = "#555";
            facetCtx.font = "14px Arial";
            facetCtx.textAlign = "left";
            facetCtx.fillText("计算、转换", 140, 35);
            facetCtx.fillText("输入、输出", 140, 320);
            facetCtx.fillText("应用场景", 580, 35);
            facetCtx.fillText("版本、作者", 580, 320);
        }
        
        function startFacetAnimation() {
            resetFacetCanvas();
            let step = 0;
            let angle = 0;
            
            function animate() {
                facetCtx.clearRect(0, 0, facetCanvas.width, facetCanvas.height);
                
                // 绘制旋转的连接线
                facetCtx.save();
                facetCtx.translate(400, 180);
                facetCtx.rotate(angle);
                
                // 绘制中心构件
                facetCtx.fillStyle = "#4CAF50";
                roundRect(facetCtx, -50, -30, 100, 60, 5, true);
                facetCtx.fillStyle = "white";
                facetCtx.font = "bold 16px Arial";
                facetCtx.textAlign = "center";
                facetCtx.fillText("构件", 0, 5);
                
                // 绘制四条线
                facetCtx.strokeStyle = "#999";
                facetCtx.beginPath();
                facetCtx.moveTo(0, -30);
                facetCtx.lineTo(0, -100);
                facetCtx.moveTo(0, 30);
                facetCtx.lineTo(0, 100);
                facetCtx.moveTo(50, 0);
                facetCtx.lineTo(160, 0);
                facetCtx.moveTo(-50, 0);
                facetCtx.lineTo(-160, 0);
                facetCtx.stroke();
                
                // 绘制四个刻面
                facetCtx.fillStyle = "#2196F3";
                roundRect(facetCtx, -60, -150, 120, 50, 5, true);
                facetCtx.fillStyle = "#FF9800";
                roundRect(facetCtx, -60, 100, 120, 50, 5, true);
                facetCtx.fillStyle = "#E91E63";
                roundRect(facetCtx, 100, -25, 120, 50, 5, true);
                facetCtx.fillStyle = "#9C27B0";
                roundRect(facetCtx, -220, -25, 120, 50, 5, true);
                
                facetCtx.fillStyle = "white";
                facetCtx.fillText("功能刻面", 0, -125);
                facetCtx.fillText("数据刻面", 0, 125);
                facetCtx.fillText("语境刻面", 160, 0);
                facetCtx.fillText("其他特征", -160, 0);
                
                facetCtx.restore();
                
                // 旋转角度
                angle += 0.005;
                if (angle >= Math.PI * 2) {
                    angle = 0;
                }
                
                step++;
                facetAnimationId = requestAnimationFrame(animate);
            }
            
            animate();
        }
        
        // 超文本方法 Canvas 动画
        const hypertextCanvas = document.getElementById("hypertextCanvas");
        const hypertextCtx = hypertextCanvas.getContext("2d");
        let hypertextAnimationId;
        let hypertextInteractive = false;
        let nodes = [];
        let links = [];
        
        function resetHypertextCanvas() {
            if (hypertextAnimationId) {
                cancelAnimationFrame(hypertextAnimationId);
            }
            hypertextCtx.clearRect(0, 0, hypertextCanvas.width, hypertextCanvas.height);
            hypertextInteractive = false;
            
            // 初始化节点
            nodes = [
                { id: 0, x: 400, y: 200, label: "中心构件", color: "#4CAF50", radius: 30 },
                { id: 1, x: 200, y: 100, label: "相关构件1", color: "#2196F3", radius: 25 },
                { id: 2, x: 300, y: 350, label: "相关构件2", color: "#FF9800", radius: 25 },
                { id: 3, x: 600, y: 150, label: "相关构件3", color: "#E91E63", radius: 25 },
                { id: 4, x: 550, y: 300, label: "相关构件4", color: "#9C27B0", radius: 25 },
                { id: 5, x: 100, y: 250, label: "相关构件5", color: "#607D8B", radius: 25 }
            ];
            
            // 初始化连接
            links = [
                { source: 0, target: 1, active: false },
                { source: 0, target: 2, active: false },
                { source: 0, target: 3, active: false },
                { source: 0, target: 4, active: false },
                { source: 0, target: 5, active: false },
                { source: 1, target: 2, active: false },
                { source: 3, target: 4, active: false },
                { source: 2, target: 5, active: false }
            ];
            
            drawHypertextNetwork();
        }
        
        function drawHypertextNetwork() {
            hypertextCtx.clearRect(0, 0, hypertextCanvas.width, hypertextCanvas.height);
            
            // 绘制连接线
            for (let i = 0; i < links.length; i++) {
                const source = nodes[links[i].source];
                const target = nodes[links[i].target];
                
                hypertextCtx.beginPath();
                hypertextCtx.moveTo(source.x, source.y);
                hypertextCtx.lineTo(target.x, target.y);
                
                if (links[i].active) {
                    hypertextCtx.strokeStyle = "#ff5722";
                    hypertextCtx.lineWidth = 3;
                } else {
                    hypertextCtx.strokeStyle = "#ccc";
                    hypertextCtx.lineWidth = 1;
                }
                
                hypertextCtx.stroke();
            }
            
            // 绘制节点
            for (let i = 0; i < nodes.length; i++) {
                hypertextCtx.beginPath();
                hypertextCtx.arc(nodes[i].x, nodes[i].y, nodes[i].radius, 0, Math.PI * 2);
                hypertextCtx.fillStyle = nodes[i].color;
                hypertextCtx.fill();
                
                hypertextCtx.font = "14px Arial";
                hypertextCtx.fillStyle = "white";
                hypertextCtx.textAlign = "center";
                hypertextCtx.textBaseline = "middle";
                hypertextCtx.fillText(nodes[i].label, nodes[i].x, nodes[i].y);
            }
        }
        
        function startHypertextAnimation() {
            resetHypertextCanvas();
            let step = 0;
            
            function animate() {
                step++;
                
                // 每10帧激活一条新连接
                if (step % 10 === 0) {
                    let linkIndex = Math.floor(step / 10) % links.length;
                    links[linkIndex].active = true;
                }
                
                // 重置动画
                if (step >= links.length * 10 + 30) {
                    resetHypertextCanvas();
                    step = 0;
                }
                
                drawHypertextNetwork();
                hypertextAnimationId = requestAnimationFrame(animate);
            }
            
            animate();
        }
        
        function interactHypertext() {
            resetHypertextCanvas();
            hypertextInteractive = true;
            
            hypertextCanvas.addEventListener("mousemove", handleMouseMove);
            hypertextCanvas.addEventListener("click", handleMouseClick);
            
            function handleMouseMove(e) {
                if (!hypertextInteractive) return;
                
                const rect = hypertextCanvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // 检测鼠标是否悬停在节点上
                let hoveredNode = false;
                for (let i = 0; i < nodes.length; i++) {
                    const dx = nodes[i].x - x;
                    const dy = nodes[i].y - y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < nodes[i].radius) {
                        hoveredNode = true;
                        hypertextCanvas.style.cursor = "pointer";
                        break;
                    }
                }
                
                if (!hoveredNode) {
                    hypertextCanvas.style.cursor = "default";
                }
            }
            
            function handleMouseClick(e) {
                if (!hypertextInteractive) return;
                
                const rect = hypertextCanvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // 检测点击的节点
                for (let i = 0; i < nodes.length; i++) {
                    const dx = nodes[i].x - x;
                    const dy = nodes[i].y - y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance < nodes[i].radius) {
                        // 点击节点，激活连接到该节点的所有连接
                        for (let j = 0; j < links.length; j++) {
                            if (links[j].source === i || links[j].target === i) {
                                links[j].active = !links[j].active;
                            }
                        }
                        
                        drawHypertextNetwork();
                        break;
                    }
                }
            }
        }
        
        // 绘制圆角矩形的辅助函数
        function roundRect(ctx, x, y, width, height, radius, fill) {
            if (typeof radius === 'undefined') {
                radius = 5;
            }
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
            if (fill) {
                ctx.fill();
            } else {
                ctx.stroke();
            }
        }
        
        // 页面加载完成后初始化画布
        window.onload = function() {
            resetKeywordCanvas();
            resetFacetCanvas();
            resetHypertextCanvas();
        }
    </script>
</body>
</html> 