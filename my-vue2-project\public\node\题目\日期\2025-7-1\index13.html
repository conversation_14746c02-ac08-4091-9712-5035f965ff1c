<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式学习：外观模式 (Facade Pattern)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 900px;
            width: 100%;
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #1a237e;
            border-bottom: 2px solid #e8eaf6;
            padding-bottom: 10px;
        }
        .question, .explanation, .interactive-demo {
            margin-bottom: 25px;
        }
        .question p {
            background-color: #e8eaf6;
            border-left: 5px solid #3f51b5;
            padding: 15px;
            border-radius: 5px;
            font-size: 1.1em;
        }
        strong {
            color: #c51162;
        }
        canvas {
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #ddd;
            width: 100%;
            height: auto;
        }
        .controls {
            text-align: center;
            margin-top: 15px;
        }
        button {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin: 0 10px;
        }
        button:hover {
            background-color: #303f9f;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #9fa8da;
            cursor: not-allowed;
        }
        .code-like {
            font-family: 'Courier New', Courier, monospace;
            background-color: #eee;
            padding: 2px 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>题目复现</h1>
    <div class="question">
        <p>若系统中的某个子模块需要为其他模块提供访问不同数据库系统的功能，这些数据库系统提供的访问接口有一定的差异，但访问过程却都是相同的，例如，先连接数据库，再打开数据库，最后对数据进行查询。针对上述需求，可以采用 <strong>(外观模式)</strong> 设计模式抽象出相同的数据库访问过程，该设计模式是 <strong>(外观模式)</strong>。</p>
    </div>

    <h1>知识点讲解：外观模式 (Facade)</h1>
    <div class="explanation">
        <h3>这是什么？</h3>
        <p>想象一下你去餐厅吃饭。你不需要分别告诉厨师、服务员、收银员该做什么。你只需要跟 <strong>服务员 (外观)</strong> 打交道，他/她会帮你协调厨房和收银台的所有事情。</p>
        <p><strong>外观模式</strong> 就是这个"服务员"。它为一个复杂的系统（比如多个数据库）提供一个简单、统一的接口。使用者（客户端代码）只需要跟这个简单的接口打交道，而不用关心背后复杂的实现细节。</p>
        <h3>解决什么问题？</h3>
        <p>解决"使用者"和"复杂系统"之间的紧密耦合和复杂交互问题。就像题目里说的，直接访问不同数据库很麻烦，因为它们的接口（函数名、步骤）可能都不一样。</p>
    </div>

    <h1>交互式动画演示</h1>
    <div class="interactive-demo">
        <canvas id="facadeCanvas" width="900" height="450"></canvas>
        <div class="controls">
            <button id="runWithoutFacade">模拟：直接访问系统</button>
            <button id="runWithFacade">模拟：通过外观模式访问</button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('facadeCanvas');
    const ctx = canvas.getContext('2d');
    const runWithoutFacadeBtn = document.getElementById('runWithoutFacade');
    const runWithFacadeBtn = document.getElementById('runWithFacade');

    let animationFrameId;

    // --- 配置 ---
    const config = {
        client: { x: 50, y: 200, width: 100, height: 50, color: '#4CAF50', label: '客户端' },
        db1: { x: 750, y: 80, width: 100, height: 120, color: '#FF9800', label: '数据库A' },
        db2: { x: 750, y: 250, width: 100, height: 120, color: '#03A9F4', label: '数据库B' },
        facade: { x: 400, y: 200, width: 100, height: 50, color: '#9C27B0', label: '外观' },
        particleColor: '#E91E63',
        font: '16px Arial',
        textColor: '#FFFFFF',
        labelColor: '#000000'
    };

    // --- 绘图工具 ---
    function drawBox(item) {
        ctx.fillStyle = item.color;
        ctx.fillRect(item.x, item.y, item.width, item.height);
        ctx.fillStyle = config.textColor;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(item.label, item.x + item.width / 2, item.y + item.height / 2);
    }
    
    function drawLabel(text, x, y) {
        ctx.fillStyle = config.labelColor;
        ctx.textAlign = 'center';
        ctx.font = '14px Arial';
        ctx.fillText(text, x, y);
    }

    function drawArrow(fromX, fromY, toX, toY, text) {
        const headlen = 10;
        const angle = Math.atan2(toY - fromY, toX - fromX);
        ctx.beginPath();
        ctx.moveTo(fromX, fromY);
        ctx.lineTo(toX, toY);
        ctx.strokeStyle = config.particleColor;
        ctx.lineWidth = 2;
        ctx.stroke();
        
        ctx.beginPath();
        ctx.moveTo(toX, toY);
        ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
        ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
        ctx.lineTo(toX, toY);
        ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
        ctx.fillStyle = config.particleColor;
        ctx.fill();

        if (text) {
             const midX = fromX + (toX - fromX) / 2;
             const midY = fromY + (toY - fromY) / 2;
             ctx.fillStyle = '#000';
             ctx.textAlign = 'center';
             ctx.fillText(text, midX, midY - 10);
        }
    }


    // --- 动画逻辑 ---
    function animateCall(from, to, steps, onComplete) {
        let progress = 0;
        const animate = () => {
            progress += 0.02; // 速度
            if (progress > 1) {
                progress = 1;
            }
            
            clearCanvas();
            drawInitialState(steps.includes('facade'));
            
            const startX = from.x + from.width;
            const startY = from.y + from.height / 2;
            const endX = to.x;
            const endY = to.y + to.height / 2;

            const currentX = startX + (endX - startX) * progress;
            const currentY = startY + (endY - startY) * progress;
            
            drawArrow(startX, startY, currentX, currentY);

            if (progress < 1) {
                animationFrameId = requestAnimationFrame(animate);
            } else {
                onComplete();
            }
        };
        animate();
    }

    function animateComplexCalls(onComplete) {
        const client = config.client;
        const db1 = config.db1;
        
        const call1 = () => drawArrow(client.x + client.width, client.y + client.height/2, db1.x, db1.y + 30, "1. connect_A()");
        const call2 = () => drawArrow(client.x + client.width, client.y + client.height/2, db1.x, db1.y + 60, "2. open_A()");
        const call3 = () => drawArrow(client.x + client.width, client.y + client.height/2, db1.x, db1.y + 90, "3. query_A()");
        
        const steps = [call1, call2, call3];
        let currentStep = 0;

        function nextStep() {
            if (currentStep < steps.length) {
                clearCanvas();
                drawInitialState(false);
                for(let i = 0; i <= currentStep; i++) {
                    steps[i]();
                }
                currentStep++;
                setTimeout(nextStep, 800);
            } else {
                onComplete();
            }
        }
        nextStep();
    }
    
    function animateFacadeCall(onComplete) {
         const client = config.client;
         const facade = config.facade;
         const db1 = config.db1;

         // 1. Client -> Facade
         animateCall(client, facade, ['facade'], () => {
             // 2. Facade -> DB1 (multiple steps visualized)
             setTimeout(() => {
                clearCanvas();
                drawInitialState(true);
                drawArrow(client.x + client.width, client.y + client.height/2, facade.x, facade.y + facade.height / 2, "queryData()");
                
                const f_out_x = facade.x + facade.width;
                const f_out_y = facade.y + facade.height / 2;

                drawArrow(f_out_x, f_out_y, db1.x, db1.y + 30, "1. connect_A()");
                drawArrow(f_out_x, f_out_y, db1.x, db1.y + 60, "2. open_A()");
                drawArrow(f_out_x, f_out_y, db1.x, db1.y + 90, "3. query_A()");
                
                drawLabel("外观内部处理复杂调用", f_out_x + (db1.x - f_out_x)/2, f_out_y - 80);
                
                onComplete();
             }, 500);
         });
    }

    // --- 状态管理 ---
    function clearCanvas() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }
    
    function drawInitialState(withFacade) {
        drawBox(config.client);
        drawBox(config.db1);
        drawLabel("接口: connect_A(), open_A(), ...", config.db1.x + config.db1.width/2, config.db1.y - 20);
        drawBox(config.db2);
        drawLabel("接口: connect_B(), open_B(), ...", config.db2.x + config.db2.width/2, config.db2.y - 20);
        
        if (withFacade) {
            drawBox(config.facade);
            drawLabel("接口: queryData()", config.facade.x + config.facade.width/2, config.facade.y - 20);
        }
    }
    
    function resetAnimation() {
        cancelAnimationFrame(animationFrameId);
        clearCanvas();
        drawInitialState(false); // Default view
        drawInitialState(true); // Draw all components initially
        runWithoutFacadeBtn.disabled = false;
        runWithFacadeBtn.disabled = false;
    }

    // --- 事件绑定 ---
    runWithoutFacadeBtn.addEventListener('click', () => {
        cancelAnimationFrame(animationFrameId);
        runWithoutFacadeBtn.disabled = true;
        runWithFacadeBtn.disabled = true;
        
        clearCanvas();
        drawInitialState(false);
        drawLabel("客户端需要知道所有数据库的复杂接口", canvas.width / 2, 30);
        
        animateComplexCalls(() => {
            runWithoutFacadeBtn.disabled = false;
            runWithFacadeBtn.disabled = false;
        });
    });

    runWithFacadeBtn.addEventListener('click', () => {
        cancelAnimationFrame(animationFrameId);
        runWithoutFacadeBtn.disabled = true;
        runWithFacadeBtn.disabled = true;

        clearCanvas();
        drawInitialState(true);
        drawLabel("客户端只需调用外观的简单接口", canvas.width / 2, 30);
        
        animateFacadeCall(() => {
            runWithoutFacadeBtn.disabled = false;
            runWithFacadeBtn.disabled = false;
        });
    });

    // --- 初始绘制 ---
    resetAnimation();
});
</script>

</body>
</html>
