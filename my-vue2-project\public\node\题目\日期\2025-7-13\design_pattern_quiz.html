<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式互动学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
            box-sizing: border-box;
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .question-section, .explanation-section, .canvas-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fdfdfd;
        }

        .question-text {
            font-size: 1.25em;
            margin-bottom: 20px;
            font-weight: bold;
            color: #555;
        }

        .options-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .option-item {
            background-color: #e9ecef;
            padding: 12px 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s, transform 0.1s;
            display: flex;
            align-items: center;
        }

        .option-item:hover {
            background-color: #d1d8df;
            transform: translateY(-2px);
        }

        .option-item.selected {
            background-color: #a0d8f0;
            border: 1px solid #5ab8ec;
        }

        .option-item.correct {
            background-color: #d4edda;
            border: 1px solid #28a745;
            color: #155724;
            font-weight: bold;
        }

        .option-item.incorrect {
            background-color: #f8d7da;
            border: 1px solid #dc3545;
            color: #721c24;
            font-weight: bold;
        }

        .option-prefix {
            font-weight: bold;
            margin-right: 10px;
            color: #6c757d;
        }

        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            display: none; /* Hidden by default */
        }

        .feedback.correct {
            background-color: #d4edda;
            color: #28a745;
        }

        .feedback.incorrect {
            background-color: #f8d7da;
            color: #dc3545;
        }

        .explanation-text {
            font-size: 1.1em;
            color: #444;
            margin-top: 15px;
            opacity: 0; /* Hidden by default for animation */
            transition: opacity 1s ease-in-out;
        }

        .explanation-text.visible {
            opacity: 1;
        }

        .explanation-text p {
            margin-bottom: 10px;
        }

        .explanation-text strong {
            color: #007bff;
        }

        #quiz-button {
            display: block;
            margin: 20px auto 0;
            padding: 12px 25px;
            font-size: 1.1em;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.1s;
        }

        #quiz-button:hover {
            background-color: #0056b3;
            transform: translateY(-1px);
        }

        canvas {
            border: 2px solid #cccccc;
            background: linear-gradient(135deg, #f9f9f9 0%, #e8f4f8 100%);
            display: block;
            margin: 20px auto;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            cursor: crosshair;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .canvas-controls {
            text-align: center;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .canvas-controls button {
            padding: 12px 24px;
            font-size: 1em;
            font-weight: 600;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
            position: relative;
            overflow: hidden;
        }

        .canvas-controls button:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .canvas-controls button:active {
            transform: translateY(0);
        }

        .canvas-controls button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .canvas-controls button.completed {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            gap: 20px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #6c757d;
            transition: all 0.3s ease;
            position: relative;
        }

        .step.active {
            background-color: #007bff;
            color: white;
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(0, 123, 255, 0.5);
        }

        .step.completed {
            background-color: #28a745;
            color: white;
        }

        .step-connector {
            width: 60px;
            height: 3px;
            background-color: #e9ecef;
            transition: all 0.3s ease;
        }

        .step-connector.completed {
            background-color: #28a745;
        }

        .code-demo {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .code-demo.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .code-demo h4 {
            margin-top: 0;
            color: #495057;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { background-color: #fff3cd; }
            50% { background-color: #ffeaa7; }
            100% { background-color: #fff3cd; }
        }

        .tooltip {
            position: absolute;
            background-color: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .tooltip.visible {
            opacity: 1;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .question-text {
                font-size: 1.1em;
            }
            .option-item {
                padding: 10px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设计模式互动学习</h1>

        <div class="question-section">
            <h2>问题 1: 单选题</h2>
            <div class="question-text" id="question">
                设计模式可以分为创建型、()和行为型三种类型。其中()属于创建型模式，()属于行为型模式。()模式可以将一个复杂的组件分成功能性抽象和内部实现两个独立的但又相关的继承层次结构，从而可以实现接口与实现分离。
            </div>
            <ul class="options-list" id="options">
                <li class="option-item" data-value="A"><span class="option-prefix">A.</span> 合成型</li>
                <li class="option-item" data-value="B"><span class="option-prefix">B.</span> 组合型</li>
                <li class="option-item" data-value="C"><span class="option-prefix">C.</span> 结构型</li>
                <li class="option-item" data-value="D"><span class="option-prefix">D.</span> 聚合型</li>
            </ul>
            <div class="feedback" id="feedback"></div>
            <button id="quiz-button">提交答案</button>
        </div>

        <div class="explanation-section">
            <h2>答案解析</h2>
            <div class="explanation-text" id="explanation-text">
                <p>设计模式基于面向对象技术，是人们在长期的开发实践中良好经验的结晶，提供了一个简单、统一的描述方法，使得人们可以复用这些软件设计办法、过程管理经验。按照设计模式的目的进行划分，现有的设计模式可以分为创建型、<strong>结构型</strong>和行为型三种模式。</p>
                <p>其中创建型模式主要包括abstract factory、builder、factory method、prototype、singleton等，<strong>结构型模式</strong>主要包括adaptor、bridge、composite、decorator、facade、flyweight和proxy，行为型模式主要包括chain of responsibility、command、interpreter、iterator、mediator、memento、observer、state、strategy、template method、visitor等。</p>
                <p><strong>桥接模式（Bridge Pattern）</strong>可以将每一个复杂的组件分成功能性抽象和内部实现两个独立的但又相关的继承层次结构，改变组件的这两个层次结构很简单，以至于它们可以相互独立地变化，采用bridge模式可以将接口与实现分离，提高了可扩展性，并对客户端隐藏了实现的细节。</p>
            </div>
        </div>

        <div class="canvas-section">
            <h2>互动演示：桥接模式</h2>
            <p>点击“构建抽象”和“构建实现”，然后点击“连接桥梁”来理解桥接模式如何分离接口和实现！</p>
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step-connector" id="connector1"></div>
                <div class="step" id="step2">2</div>
                <div class="step-connector" id="connector2"></div>
                <div class="step" id="step3">3</div>
                <div class="step-connector" id="connector3"></div>
                <div class="step" id="step4">4</div>
            </div>

            <canvas id="bridgeCanvas" width="800" height="450"></canvas>
            <div class="canvas-controls">
                <button id="buildAbstraction" data-step="1">🏗️ 构建抽象层</button>
                <button id="buildImplementation" data-step="2" disabled>⚙️ 构建实现层</button>
                <button id="connectBridge" data-step="3" disabled>🌉 连接桥梁</button>
                <button id="showCode" data-step="4" disabled>📝 查看代码</button>
                <button id="resetCanvas">🔄 重置演示</button>
            </div>

            <div class="code-demo" id="codeDemo">
                <h4>桥接模式代码示例：</h4>
                <pre><code>// <span class="highlight">抽象层 (Abstraction)</span>
class Shape {
    constructor(drawingAPI) {
        this.drawingAPI = drawingAPI;
    }

    draw() {
        // 委托给实现层
        this.drawingAPI.drawShape(this);
    }
}

// <span class="highlight">实现层接口 (Implementation)</span>
class DrawingAPI {
    drawShape(shape) {
        throw new Error("必须实现 drawShape 方法");
    }
}

// <span class="highlight">具体实现 (Concrete Implementation)</span>
class CanvasAPI extends DrawingAPI {
    drawShape(shape) {
        console.log("使用 Canvas 绘制图形");
        // Canvas 具体绘制逻辑
    }
}

class SVGDrawingAPI extends DrawingAPI {
    drawShape(shape) {
        console.log("使用 SVG 绘制图形");
        // SVG 具体绘制逻辑
    }
}

// <span class="highlight">使用桥接模式</span>
const canvasAPI = new CanvasAPI();
const svgAPI = new SVGDrawingAPI();

const shape1 = new Shape(canvasAPI);
const shape2 = new Shape(svgAPI);

shape1.draw(); // 使用 Canvas 绘制
shape2.draw(); // 使用 SVG 绘制</code></pre>
            </div>
        </div>

        <div class="tooltip" id="tooltip"></div>
    </div>

    <script>
        const optionsList = document.getElementById('options');
        const feedbackDiv = document.getElementById('feedback');
        const explanationText = document.getElementById('explanation-text');
        const quizButton = document.getElementById('quiz-button');
        const canvas = document.getElementById('bridgeCanvas');
        const ctx = canvas.getContext('2d');
        const buildAbstractionBtn = document.getElementById('buildAbstraction');
        const buildImplementationBtn = document.getElementById('buildImplementation');
        const connectBridgeBtn = document.getElementById('connectBridge');
        const showCodeBtn = document.getElementById('showCode');
        const resetCanvasBtn = document.getElementById('resetCanvas');
        const codeDemo = document.getElementById('codeDemo');
        const tooltip = document.getElementById('tooltip');

        let selectedOption = null;
        const correctAnswer = 'C'; // 结构型

        // Quiz Logic
        optionsList.addEventListener('click', (event) => {
            const target = event.target.closest('.option-item');
            if (target) {
                if (selectedOption) {
                    selectedOption.classList.remove('selected');
                }
                selectedOption = target;
                selectedOption.classList.add('selected');
            }
        });

        quizButton.addEventListener('click', () => {
            if (!selectedOption) {
                alert('请选择一个答案！');
                return;
            }

            // Remove previous feedback classes
            optionsList.querySelectorAll('.option-item').forEach(item => {
                item.classList.remove('correct', 'incorrect');
            });
            feedbackDiv.classList.remove('correct', 'incorrect');
            feedbackDiv.style.display = 'block';

            if (selectedOption.dataset.value === correctAnswer) {
                feedbackDiv.textContent = '恭喜你，回答正确！';
                feedbackDiv.classList.add('correct');
                selectedOption.classList.add('correct');
            } else {
                feedbackDiv.textContent = '很遗憾，回答错误。正确答案是 C. 结构型。';
                feedbackDiv.classList.add('incorrect');
                selectedOption.classList.add('incorrect');
                // Highlight the correct answer
                optionsList.querySelector(`[data-value="${correctAnswer}"]`).classList.add('correct');
            }
            explanationText.classList.add('visible'); // Show explanation after answer
            quizButton.disabled = true; // Disable button after submission
            optionsList.removeEventListener('click', handleOptionClick); // Prevent further changes
        });

        // Function to re-enable click listener (for potential future questions)
        function handleOptionClick(event) {
            const target = event.target.closest('.option-item');
            if (target) {
                if (selectedOption) {
                    selectedOption.classList.remove('selected');
                }
                selectedOption = target;
                selectedOption.classList.add('selected');
            }
        }
        optionsList.addEventListener('click', handleOptionClick);


        // Canvas Animation and Interaction Logic
        let currentStep = 1;
        let abstractionBuilt = false;
        let implementationBuilt = false;
        let bridgeConnected = false;
        let codeShown = false;
        let animationFrame = null;

        // Animation properties
        let abstractionOpacity = 0;
        let implementationOpacity = 0;
        let bridgeOpacity = 0;
        let abstractionScale = 0;
        let implementationScale = 0;
        let bridgeProgress = 0;

        const abstraction = {
            x: 100, y: 180, width: 180, height: 100,
            text: '功能抽象层\n(Abstraction)',
            details: ['定义高级接口', '委托给实现层', '独立于具体实现']
        };
        const implementation = {
            x: 520, y: 180, width: 180, height: 100,
            text: '实现层\n(Implementation)',
            details: ['具体实现逻辑', '可独立变化', '隐藏实现细节']
        };

        // Enhanced drawing functions with animations
        function drawRect(obj, color, opacity = 1, scale = 1, textColor = 'white') {
            ctx.save();
            ctx.globalAlpha = opacity;

            const centerX = obj.x + obj.width / 2;
            const centerY = obj.y + obj.height / 2;
            const scaledWidth = obj.width * scale;
            const scaledHeight = obj.height * scale;
            const scaledX = centerX - scaledWidth / 2;
            const scaledY = centerY - scaledHeight / 2;

            // Draw shadow
            ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetX = 3;
            ctx.shadowOffsetY = 3;

            // Draw gradient background
            const gradient = ctx.createLinearGradient(scaledX, scaledY, scaledX, scaledY + scaledHeight);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, darkenColor(color, 0.2));

            ctx.fillStyle = gradient;
            ctx.fillRect(scaledX, scaledY, scaledWidth, scaledHeight);

            // Draw border
            ctx.shadowColor = 'transparent';
            ctx.strokeStyle = darkenColor(color, 0.3);
            ctx.lineWidth = 3;
            ctx.strokeRect(scaledX, scaledY, scaledWidth, scaledHeight);

            // Draw text
            ctx.fillStyle = textColor;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const lines = obj.text.split('\n');
            lines.forEach((line, index) => {
                const y = centerY + (index - (lines.length - 1) / 2) * 20;
                ctx.fillText(line, centerX, y);
            });

            ctx.restore();
        }

        function darkenColor(color, factor) {
            // Simple color darkening function
            if (color === '#007bff') return '#0056b3';
            if (color === '#ffc107') return '#e0a800';
            return color;
        }

        function drawBridge(color, progress = 1, opacity = 1) {
            ctx.save();
            ctx.globalAlpha = opacity;

            const startX = abstraction.x + abstraction.width;
            const startY = abstraction.y + abstraction.height / 2;
            const endX = implementation.x;
            const endY = implementation.y + implementation.height / 2;

            const currentEndX = startX + (endX - startX) * progress;
            const currentEndY = startY + (endY - startY) * progress;

            // Draw animated bridge line
            const gradient = ctx.createLinearGradient(startX, startY, currentEndX, currentEndY);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, '#20c997');

            ctx.strokeStyle = gradient;
            ctx.lineWidth = 8;
            ctx.lineCap = 'round';
            ctx.shadowColor = 'rgba(40, 167, 69, 0.5)';
            ctx.shadowBlur = 10;

            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(currentEndX, currentEndY);
            ctx.stroke();

            // Draw connection points
            if (progress > 0.5) {
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(startX, startY, 6, 0, Math.PI * 2);
                ctx.fill();

                if (progress === 1) {
                    ctx.beginPath();
                    ctx.arc(endX, endY, 6, 0, Math.PI * 2);
                    ctx.fill();
                }
            }

            // Bridge label with animation
            if (progress > 0.7) {
                ctx.shadowColor = 'transparent';
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'bottom';
                const labelX = (startX + endX) / 2;
                const labelY = (startY + endY) / 2 - 15;
                ctx.fillText('桥接模式 (Bridge)', labelX, labelY);

                // Draw small arrows
                drawArrow(startX + 20, startY - 5, 15, '#28a745');
                drawArrow(endX - 35, endY - 5, -15, '#28a745');
            }

            ctx.restore();
        }

        function drawArrow(x, y, length, color) {
            ctx.save();
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;

            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + length, y);
            ctx.stroke();

            // Arrow head
            const headSize = 5;
            ctx.beginPath();
            if (length > 0) {
                ctx.moveTo(x + length, y);
                ctx.lineTo(x + length - headSize, y - headSize);
                ctx.lineTo(x + length - headSize, y + headSize);
            } else {
                ctx.moveTo(x + length, y);
                ctx.lineTo(x + length + headSize, y - headSize);
                ctx.lineTo(x + length + headSize, y + headSize);
            }
            ctx.closePath();
            ctx.fill();

            ctx.restore();
        }

        function animateCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw background grid
            drawGrid();

            // Draw title
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('桥接模式演示', canvas.width / 2, 40);

            // Draw components with animations
            if (abstractionBuilt) {
                drawRect(abstraction, '#007bff', abstractionOpacity, abstractionScale);
                if (abstractionOpacity < 1) {
                    abstractionOpacity = Math.min(1, abstractionOpacity + 0.05);
                }
                if (abstractionScale < 1) {
                    abstractionScale = Math.min(1, abstractionScale + 0.05);
                }
            }

            if (implementationBuilt) {
                drawRect(implementation, '#ffc107', implementationOpacity, implementationScale, '#333');
                if (implementationOpacity < 1) {
                    implementationOpacity = Math.min(1, implementationOpacity + 0.05);
                }
                if (implementationScale < 1) {
                    implementationScale = Math.min(1, implementationScale + 0.05);
                }
            }

            if (bridgeConnected) {
                drawBridge('#28a745', bridgeProgress, bridgeOpacity);
                if (bridgeProgress < 1) {
                    bridgeProgress = Math.min(1, bridgeProgress + 0.03);
                }
                if (bridgeOpacity < 1) {
                    bridgeOpacity = Math.min(1, bridgeOpacity + 0.05);
                }
            }

            // Draw benefits text with fade-in effect
            if (bridgeConnected && bridgeProgress > 0.5) {
                const benefitsOpacity = Math.min(1, (bridgeProgress - 0.5) * 2);
                ctx.save();
                ctx.globalAlpha = benefitsOpacity;

                ctx.fillStyle = '#28a745';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'left';

                const benefits = [
                    '✓ 接口与实现分离',
                    '✓ 提高可扩展性',
                    '✓ 隐藏实现细节',
                    '✓ 运行时切换实现'
                ];

                benefits.forEach((benefit, index) => {
                    ctx.fillText(benefit, 50, 350 + index * 25);
                });

                ctx.restore();
            }

            // Continue animation if needed
            if ((abstractionBuilt && abstractionOpacity < 1) ||
                (implementationBuilt && implementationOpacity < 1) ||
                (bridgeConnected && bridgeProgress < 1)) {
                animationFrame = requestAnimationFrame(animateCanvas);
            }
        }

        function drawGrid() {
            ctx.save();
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            ctx.globalAlpha = 0.3;

            // Vertical lines
            for (let x = 0; x <= canvas.width; x += 40) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }

            // Horizontal lines
            for (let y = 0; y <= canvas.height; y += 40) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }

            ctx.restore();
        }

        // Step management functions
        function updateStepIndicator(step) {
            for (let i = 1; i <= 4; i++) {
                const stepEl = document.getElementById(`step${i}`);
                const connectorEl = document.getElementById(`connector${i}`);

                stepEl.classList.remove('active', 'completed');
                if (connectorEl) connectorEl.classList.remove('completed');

                if (i < step) {
                    stepEl.classList.add('completed');
                    if (connectorEl) connectorEl.classList.add('completed');
                } else if (i === step) {
                    stepEl.classList.add('active');
                }
            }
        }

        function enableNextButton() {
            const buttons = [buildAbstractionBtn, buildImplementationBtn, connectBridgeBtn, showCodeBtn];
            if (currentStep <= buttons.length) {
                buttons[currentStep - 1].disabled = false;
                buttons[currentStep - 1].classList.remove('completed');
            }
        }

        function completeCurrentStep() {
            const buttons = [buildAbstractionBtn, buildImplementationBtn, connectBridgeBtn, showCodeBtn];
            if (currentStep <= buttons.length) {
                buttons[currentStep - 1].disabled = true;
                buttons[currentStep - 1].classList.add('completed');
            }
            currentStep++;
            updateStepIndicator(currentStep);
            enableNextButton();
        }

        // Enhanced event handlers with animations and tooltips
        buildAbstractionBtn.addEventListener('click', () => {
            abstractionBuilt = true;
            abstractionOpacity = 0;
            abstractionScale = 0;
            animateCanvas();
            completeCurrentStep();
            showTooltip('抽象层构建完成！这是高级接口层。', buildAbstractionBtn);
        });

        buildImplementationBtn.addEventListener('click', () => {
            implementationBuilt = true;
            implementationOpacity = 0;
            implementationScale = 0;
            animateCanvas();
            completeCurrentStep();
            showTooltip('实现层构建完成！这包含具体的实现逻辑。', buildImplementationBtn);
        });

        connectBridgeBtn.addEventListener('click', () => {
            if (abstractionBuilt && implementationBuilt) {
                bridgeConnected = true;
                bridgeProgress = 0;
                bridgeOpacity = 0;
                animateCanvas();
                completeCurrentStep();
                showTooltip('桥梁连接成功！抽象层和实现层现在可以独立变化。', connectBridgeBtn);
            } else {
                alert('请先完成前面的步骤！');
            }
        });

        showCodeBtn.addEventListener('click', () => {
            codeShown = true;
            codeDemo.classList.add('visible');
            completeCurrentStep();
            showTooltip('代码示例已显示！查看桥接模式的具体实现。', showCodeBtn);
        });

        resetCanvasBtn.addEventListener('click', () => {
            // Reset all states
            currentStep = 1;
            abstractionBuilt = false;
            implementationBuilt = false;
            bridgeConnected = false;
            codeShown = false;

            // Reset animation properties
            abstractionOpacity = 0;
            implementationOpacity = 0;
            bridgeOpacity = 0;
            abstractionScale = 0;
            implementationScale = 0;
            bridgeProgress = 0;

            // Reset UI
            codeDemo.classList.remove('visible');
            updateStepIndicator(1);

            // Reset buttons
            [buildImplementationBtn, connectBridgeBtn, showCodeBtn].forEach(btn => {
                btn.disabled = true;
                btn.classList.remove('completed');
            });
            buildAbstractionBtn.disabled = false;
            buildAbstractionBtn.classList.remove('completed');

            // Cancel any ongoing animation
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }

            animateCanvas();
            hideTooltip();
        });

        // Tooltip functions
        function showTooltip(text, element) {
            tooltip.textContent = text;
            tooltip.classList.add('visible');

            const rect = element.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();

            tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 10) + 'px';

            setTimeout(hideTooltip, 3000);
        }

        function hideTooltip() {
            tooltip.classList.remove('visible');
        }

        // Canvas mouse interaction
        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            let hoverText = '';

            // Check if hovering over abstraction
            if (abstractionBuilt &&
                x >= abstraction.x && x <= abstraction.x + abstraction.width &&
                y >= abstraction.y && y <= abstraction.y + abstraction.height) {
                hoverText = '抽象层：定义高级接口，委托给实现层';
                canvas.style.cursor = 'pointer';
            }
            // Check if hovering over implementation
            else if (implementationBuilt &&
                     x >= implementation.x && x <= implementation.x + implementation.width &&
                     y >= implementation.y && y <= implementation.y + implementation.height) {
                hoverText = '实现层：包含具体实现逻辑，可独立变化';
                canvas.style.cursor = 'pointer';
            }
            // Check if hovering over bridge area
            else if (bridgeConnected &&
                     x >= abstraction.x + abstraction.width && x <= implementation.x &&
                     y >= abstraction.y && y <= abstraction.y + abstraction.height) {
                hoverText = '桥接：连接抽象和实现，实现分离';
                canvas.style.cursor = 'pointer';
            } else {
                canvas.style.cursor = 'crosshair';
            }

            if (hoverText) {
                tooltip.textContent = hoverText;
                tooltip.classList.add('visible');
                tooltip.style.left = (e.clientX - tooltip.offsetWidth / 2) + 'px';
                tooltip.style.top = (e.clientY - tooltip.offsetHeight - 10) + 'px';
            } else {
                hideTooltip();
            }
        });

        canvas.addEventListener('mouseleave', hideTooltip);

        // Canvas click interaction for educational details
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Click on abstraction
            if (abstractionBuilt &&
                x >= abstraction.x && x <= abstraction.x + abstraction.width &&
                y >= abstraction.y && y <= abstraction.y + abstraction.height) {
                alert('抽象层特点：\n• 定义高级接口\n• 委托给实现层\n• 独立于具体实现\n• 客户端只需要知道抽象接口');
            }
            // Click on implementation
            else if (implementationBuilt &&
                     x >= implementation.x && x <= implementation.x + implementation.width &&
                     y >= implementation.y && y <= implementation.y + implementation.height) {
                alert('实现层特点：\n• 包含具体实现逻辑\n• 可以独立变化\n• 隐藏实现细节\n• 可以有多种不同实现');
            }
        });

        // Initialize
        updateStepIndicator(1);
        enableNextButton();
        animateCanvas();

    </script>
</body>
</html> 