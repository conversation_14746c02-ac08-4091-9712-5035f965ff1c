<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作概念学习 - SO架构最低层</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .story-canvas {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            overflow: hidden;
            position: relative;
        }

        canvas {
            display: block;
            width: 100%;
            height: 500px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .btn.active {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .concept-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            transform: translateX(-100px);
            opacity: 0;
            transition: all 0.6s ease;
        }

        .concept-card.show {
            transform: translateX(0);
            opacity: 1;
        }

        .concept-card.wrong {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .concept-card.correct {
            background: linear-gradient(135deg, #00b894, #00a085);
        }

        .concept-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .concept-icon {
            background: rgba(255,255,255,0.2);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .concept-description {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .concept-example {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            font-style: italic;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .comparison-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .comparison-item {
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .comparison-item.wrong {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .comparison-item.correct {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }

        .interactive-demo {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            text-align: center;
        }

        .demo-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
            margin: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #74b9ff, #0984e3);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>理解"操作"概念</h1>
            <p>为什么SO架构的最低层是"操作"而不是"类"？</p>
        </div>

        <div class="story-canvas">
            <canvas id="storyCanvas" width="1000" height="500"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="showEvolution()">显示架构演进</button>
            <button class="btn" onclick="showOperation()">聚焦操作层</button>
            <button class="btn" onclick="showComparison()">对比分析</button>
            <button class="btn" onclick="startDemo()">交互演示</button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="explanation">
            <h2 style="color: #2d3436; margin-bottom: 30px;">概念解析：从面向对象到面向服务</h2>
            <div id="conceptCards"></div>
        </div>

        <div class="comparison-section">
            <h3 style="color: #2d3436; margin-bottom: 20px; text-align: center;">为什么是"操作"而不是"类"？</h3>
            <div class="comparison-grid">
                <div class="comparison-item wrong">
                    <h4>❌ 错误选择：类 (Class)</h4>
                    <p>类是面向对象的概念，属于更底层的实现细节。在SO架构中，我们关注的是功能而非实现。</p>
                </div>
                <div class="comparison-item correct">
                    <h4>✅ 正确答案：操作 (Operation)</h4>
                    <p>操作是功能的最小单元，代表可以执行的具体动作，是面向服务架构的基础构建块。</p>
                </div>
            </div>
        </div>

        <div class="interactive-demo">
            <h3 style="color: #2d3436; margin-bottom: 20px;">交互式操作演示</h3>
            <p style="margin-bottom: 20px;">点击下面的操作来看看它们如何工作：</p>
            <div id="operationDemo"></div>
            <div id="demoResult" style="margin-top: 20px; min-height: 100px;"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        let currentScene = 0;
        let animationFrame = 0;

        // 架构演进数据
        const evolutionStages = [
            {
                name: "面向对象 (OO)",
                description: "基于类和对象构建应用",
                color: "#e17055",
                level: "实现层面"
            },
            {
                name: "基于构件 (Component)",
                description: "将相关对象按业务功能分组",
                color: "#74b9ff",
                level: "组织层面"
            },
            {
                name: "面向服务 (SO)",
                description: "接口定义与实现解耦，暴露功能调用",
                color: "#00b894",
                level: "服务层面"
            }
        ];

        // 操作示例数据
        const operationExamples = [
            {
                name: "用户登录",
                input: "用户名、密码",
                output: "登录状态、用户信息",
                description: "验证用户身份的单个逻辑单元"
            },
            {
                name: "查询订单",
                input: "订单ID",
                output: "订单详情",
                description: "获取特定订单信息的操作"
            },
            {
                name: "更新库存",
                input: "商品ID、数量变化",
                output: "更新结果",
                description: "修改商品库存的原子操作"
            }
        ];

        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            if (currentScene === 0) {
                drawEvolution();
            } else if (currentScene === 1) {
                drawOperationFocus();
            } else if (currentScene === 2) {
                drawComparison();
            } else if (currentScene === 3) {
                drawInteractiveDemo();
            }

            animationFrame++;
        }

        function drawEvolution() {
            // 绘制架构演进
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('软件架构演进：三个递进的抽象层次', canvas.width / 2, 40);

            evolutionStages.forEach((stage, index) => {
                const x = 150 + index * 250;
                const y = 200;
                
                // 绘制阶段框
                ctx.fillStyle = stage.color;
                ctx.fillRect(x - 80, y - 40, 160, 80);
                
                // 绘制文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(stage.name, x, y - 10);
                
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(stage.level, x, y + 10);
                
                // 绘制描述
                ctx.fillStyle = '#2d3436';
                ctx.font = '14px Microsoft YaHei';
                const lines = wrapText(stage.description, 140);
                lines.forEach((line, lineIndex) => {
                    ctx.fillText(line, x, y + 60 + lineIndex * 20);
                });
                
                // 绘制箭头
                if (index < evolutionStages.length - 1) {
                    drawArrow(x + 90, y, 0);
                }
            });

            // 强调SO架构
            const pulse = Math.sin(animationFrame * 0.1) * 10 + 10;
            ctx.strokeStyle = '#fd79a8';
            ctx.lineWidth = 3;
            ctx.setLineDash([5, 5]);
            ctx.strokeRect(650 - 80 - pulse/2, 200 - 40 - pulse/2, 160 + pulse, 80 + pulse);
            ctx.setLineDash([]);
        }

        function drawOperationFocus() {
            // 聚焦操作层
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('SO架构的三层抽象 - 聚焦操作层', canvas.width / 2, 40);

            // 绘制三层结构
            const layers = [
                {name: "业务流程", y: 120, color: "#00b894", opacity: 0.3},
                {name: "服务", y: 200, color: "#74b9ff", opacity: 0.3},
                {name: "操作", y: 280, color: "#e17055", opacity: 1.0}
            ];

            layers.forEach((layer, index) => {
                const pulse = index === 2 ? Math.sin(animationFrame * 0.1) * 5 + 5 : 0;
                
                ctx.globalAlpha = layer.opacity;
                ctx.fillStyle = layer.color;
                ctx.fillRect(200 - pulse, layer.y - 25 - pulse/2, 600 + pulse*2, 50 + pulse);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(layer.name, canvas.width / 2, layer.y + 5);
                
                ctx.globalAlpha = 1.0;
            });

            // 操作特征说明
            ctx.fillStyle = '#2d3436';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'left';
            const features = [
                "• 单个逻辑单元的事物",
                "• 包含特定的结构化接口", 
                "• 返回结构化的响应",
                "• 类似于对象的方法"
            ];
            
            features.forEach((feature, index) => {
                ctx.fillText(feature, 250, 350 + index * 25);
            });
        }

        function drawComparison() {
            // 对比分析
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('概念对比：类 vs 操作', canvas.width / 2, 40);

            // 绘制对比表格
            const comparisons = [
                {
                    aspect: "抽象层次",
                    classDesc: "实现细节",
                    operationDesc: "功能接口"
                },
                {
                    aspect: "关注点",
                    classDesc: "数据和方法的封装",
                    operationDesc: "可执行的业务动作"
                },
                {
                    aspect: "在SO中的角色",
                    classDesc: "底层实现机制",
                    operationDesc: "服务的基本构建块"
                }
            ];

            // 表头
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(100, 80, 200, 40);
            ctx.fillRect(300, 80, 200, 40);
            ctx.fillRect(500, 80, 200, 40);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillText('对比维度', 200, 105);
            ctx.fillText('类 (错误)', 400, 105);
            ctx.fillText('操作 (正确)', 600, 105);

            // 表格内容
            comparisons.forEach((comp, index) => {
                const y = 120 + index * 60;
                
                // 背景
                ctx.fillStyle = index % 2 === 0 ? '#f8f9fa' : 'white';
                ctx.fillRect(100, y, 600, 60);
                
                // 边框
                ctx.strokeStyle = '#dee2e6';
                ctx.lineWidth = 1;
                ctx.strokeRect(100, y, 200, 60);
                ctx.strokeRect(300, y, 200, 60);
                ctx.strokeRect(500, y, 200, 60);
                
                // 文字
                ctx.fillStyle = '#2d3436';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                
                const aspectLines = wrapText(comp.aspect, 180);
                aspectLines.forEach((line, lineIndex) => {
                    ctx.fillText(line, 200, y + 25 + lineIndex * 18);
                });
                
                ctx.fillStyle = '#e74c3c';
                const classLines = wrapText(comp.classDesc, 180);
                classLines.forEach((line, lineIndex) => {
                    ctx.fillText(line, 400, y + 25 + lineIndex * 18);
                });
                
                ctx.fillStyle = '#27ae60';
                const opLines = wrapText(comp.operationDesc, 180);
                opLines.forEach((line, lineIndex) => {
                    ctx.fillText(line, 600, y + 25 + lineIndex * 18);
                });
            });
        }

        function drawInteractiveDemo() {
            // 交互演示
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('操作示例演示', canvas.width / 2, 40);

            // 绘制操作流程图
            const steps = ['输入', '处理', '输出'];
            steps.forEach((step, index) => {
                const x = 200 + index * 200;
                const y = 200;
                
                // 步骤圆圈
                ctx.fillStyle = '#74b9ff';
                ctx.beginPath();
                ctx.arc(x, y, 40, 0, 2 * Math.PI);
                ctx.fill();
                
                // 步骤文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(step, x, y + 5);
                
                // 箭头
                if (index < steps.length - 1) {
                    drawArrow(x + 60, y, 0);
                }
            });

            // 示例说明
            ctx.fillStyle = '#2d3436';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('每个操作都有明确的输入、处理逻辑和输出', canvas.width / 2, 300);
            ctx.fillText('这是面向服务架构的最小功能单元', canvas.width / 2, 325);
        }

        function drawArrow(x, y, rotation) {
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(rotation);
            
            ctx.fillStyle = '#74b9ff';
            ctx.beginPath();
            ctx.moveTo(0, -8);
            ctx.lineTo(-15, 8);
            ctx.lineTo(15, 8);
            ctx.closePath();
            ctx.fill();
            
            ctx.restore();
        }

        function wrapText(text, maxWidth) {
            const words = text.split('');
            const lines = [];
            let currentLine = '';
            
            for (let i = 0; i < words.length; i++) {
                const testLine = currentLine + words[i];
                const metrics = ctx.measureText(testLine);
                
                if (metrics.width > maxWidth && currentLine !== '') {
                    lines.push(currentLine);
                    currentLine = words[i];
                } else {
                    currentLine = testLine;
                }
            }
            lines.push(currentLine);
            return lines;
        }

        function showEvolution() {
            currentScene = 0;
            updateConceptCards();
            updateProgress(25);
            updateButtonState(0);
        }

        function showOperation() {
            currentScene = 1;
            updateConceptCards();
            updateProgress(50);
            updateButtonState(1);
        }

        function showComparison() {
            currentScene = 2;
            updateConceptCards();
            updateProgress(75);
            updateButtonState(2);
        }

        function startDemo() {
            currentScene = 3;
            createOperationDemo();
            updateProgress(100);
            updateButtonState(3);
        }

        function updateButtonState(activeIndex) {
            document.querySelectorAll('.btn').forEach((btn, index) => {
                btn.classList.toggle('active', index === activeIndex);
            });
        }

        function updateConceptCards() {
            const container = document.getElementById('conceptCards');
            container.innerHTML = '';
            
            const concepts = [
                {
                    title: "架构演进理解",
                    description: "面向对象 → 基于构件 → 面向服务，这是三个递进的抽象层次。每一层都建立在前一层的基础上，但关注点不同。",
                    example: "类和对象是实现基础，构件是组织方式，服务是对外接口",
                    type: "correct"
                },
                {
                    title: "为什么不是'类'？",
                    description: "类属于面向对象的概念，是实现层面的细节。在面向服务架构中，我们关注的是功能接口，而不是具体的实现方式。",
                    example: "类是'怎么做'，操作是'做什么'",
                    type: "wrong"
                },
                {
                    title: "操作的核心特征",
                    description: "操作是单个逻辑单元，有明确的输入输出接口，代表可以执行的具体功能。它是构建服务的基本单元。",
                    example: "登录操作、查询操作、更新操作等",
                    type: "correct"
                }
            ];

            concepts.forEach((concept, index) => {
                setTimeout(() => {
                    const card = document.createElement('div');
                    card.className = `concept-card ${concept.type}`;
                    card.innerHTML = `
                        <div class="concept-title">
                            <div class="concept-icon">${concept.type === 'correct' ? '✓' : '✗'}</div>
                            ${concept.title}
                        </div>
                        <div class="concept-description">${concept.description}</div>
                        <div class="concept-example">
                            <strong>示例：</strong>${concept.example}
                        </div>
                    `;
                    container.appendChild(card);
                    
                    setTimeout(() => {
                        card.classList.add('show');
                    }, 100);
                }, index * 300);
            });
        }

        function createOperationDemo() {
            const demoContainer = document.getElementById('operationDemo');
            demoContainer.innerHTML = '';
            
            operationExamples.forEach((op, index) => {
                const btn = document.createElement('button');
                btn.className = 'btn demo-btn';
                btn.textContent = op.name;
                btn.onclick = () => showOperationDetail(op);
                demoContainer.appendChild(btn);
            });
        }

        function showOperationDetail(operation) {
            const resultContainer = document.getElementById('demoResult');
            resultContainer.innerHTML = `
                <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; text-align: left;">
                    <h4 style="color: #2d3436; margin-bottom: 15px;">操作详情：${operation.name}</h4>
                    <p style="margin-bottom: 10px;"><strong>输入：</strong>${operation.input}</p>
                    <p style="margin-bottom: 10px;"><strong>输出：</strong>${operation.output}</p>
                    <p style="margin-bottom: 15px;"><strong>描述：</strong>${operation.description}</p>
                    <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; border-left: 4px solid #2196f3;">
                        <strong>关键理解：</strong>这是一个完整的功能单元，有明确的接口定义，可以独立执行，这就是SO架构中"操作"的本质。
                    </div>
                </div>
            `;
        }

        function updateProgress(percentage) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = percentage + '%';
        }

        // 初始化
        function init() {
            showEvolution();
            
            // 开始动画循环
            function animate() {
                drawScene();
                requestAnimationFrame(animate);
            }
            animate();
        }

        init();
    </script>
</body>
</html>
