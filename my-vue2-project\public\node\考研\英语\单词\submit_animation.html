<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Submit</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f4f8;
            color: #333;
        }
        .container {
            width: 800px;
            height: 600px;
            background-color: #fff;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            background-color: #e9ecef;
        }
        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        .controls {
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        button {
            padding: 12px 24px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            background-color: #007bff;
            color: white;
            border-radius: 8px;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.2);
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .tooltip {
            position: absolute;
            background-color: rgba(0,0,0,0.75);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 14px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="canvas-container">
            <canvas id="wordCanvas"></canvas>
            <div id="tooltip" class="tooltip"></div>
        </div>
        <div class="controls">
            <button id="playBtn">开始动画</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('wordCanvas');
        const ctx = canvas.getContext('2d');
        const playBtn = document.getElementById('playBtn');
        const tooltip = document.getElementById('tooltip');
        
        let dpr = window.devicePixelRatio || 1;
        let rect = canvas.getBoundingClientRect();
        canvas.width = rect.width * dpr;
        canvas.height = rect.height * dpr;
        ctx.scale(dpr, dpr);

        const width = canvas.width / dpr;
        const height = canvas.height / dpr;

        let animationState = 0;
        let startTime = 0;
        let objects = [];

        function easeInOutCubic(t) {
            return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
        }

        function drawText(text, x, y, size = 50, color = '#333', alpha = 1) {
            ctx.globalAlpha = alpha;
            ctx.fillStyle = color;
            ctx.font = `bold ${size}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
            ctx.globalAlpha = 1;
        }

        function drawBox(x, y, w, h, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, w, h);
        }

        function drawPerson(x, y) {
            // Head
            ctx.beginPath();
            ctx.arc(x, y - 40, 15, 0, Math.PI * 2);
            ctx.fillStyle = '#f2d5b1';
            ctx.fill();
            // Body
            ctx.fillStyle = '#4a90e2';
            ctx.fillRect(x - 15, y - 25, 30, 40);
            // Legs
            ctx.fillStyle = '#333';
            ctx.fillRect(x - 10, y + 15, 8, 30);
            ctx.fillRect(x + 2, y + 15, 8, 30);
        }

        function drawDocument(x, y, rotation = 0) {
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(rotation);
            ctx.fillStyle = 'white';
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 1;
            ctx.fillRect(-12, -18, 24, 36);
            ctx.strokeRect(-12, -18, 24, 36);
            ctx.beginPath();
            for(let i = -14; i <= 10; i+=4) {
                ctx.moveTo(-8, i);
                ctx.lineTo(8, i);
            }
            ctx.strokeStyle = '#b0b0b0';
            ctx.lineWidth = 0.5;
            ctx.stroke();
            ctx.restore();
        }

        function drawDoor(x, y) {
            // Door
            ctx.fillStyle = '#8b5a2b';
            ctx.fillRect(x - 75, y - 150, 150, 250);
            // Frame
            ctx.strokeStyle = '#5a3d1a';
            ctx.lineWidth = 10;
            ctx.strokeRect(x - 80, y - 155, 160, 260);
            // Sign
            ctx.fillStyle = '#f0e68c';
            ctx.fillRect(x - 40, y - 80, 80, 30);
            drawText("主管办公室", x, y - 65, 10, '#333');
            // Doorknob
            ctx.beginPath();
            ctx.arc(x + 55, y - 20, 8, 0, Math.PI * 2);
            ctx.fillStyle = '#ffd700';
            ctx.fill();
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function animate(time) {
            if (!startTime) startTime = time;
            let progress = (time - startTime) / 1000;
            clearCanvas();
            
            // Shared objects
            if(animationState >= 4) {
                 drawDoor(width / 2, height - 100);
            }

            switch(animationState) {
                case 1: // Show "submit"
                    let fadeIn = Math.min(progress, 1);
                    drawText('submit', width / 2, height / 2, 80, '#007bff', fadeIn);
                    drawText('v. 提交；屈服', width / 2, height / 2 + 60, 30, '#555', fadeIn);
                    if(progress > 3) {
                        startTime = time;
                        animationState = 2;
                    }
                    break;
                case 2: // Decompose
                    let t = Math.min(progress, 1);
                    let easedT = easeInOutCubic(t);
                    let subX = width / 2 - (easedT * 150);
                    let mitX = width / 2 + (easedT * 150);
                    
                    drawText('sub', subX, height / 2, 80, '#d9534f');
                    drawText('mit', mitX, height / 2, 80, '#5cb85c');

                    if(t >= 1) {
                         drawText('在...下面 (under)', subX, height / 2 + 60, 25, '#555');
                         drawText('发送 (send)', mitX, height / 2 + 60, 25, '#555');
                    }
                     if(progress > 4) {
                        startTime = time;
                        animationState = 3;
                        playBtn.innerText = '演示故事';
                        playBtn.disabled = false;
                    }
                    break;
                case 3: // Waiting for interaction
                     drawText('sub', width / 2 - 150, height / 2, 80, '#d9534f');
                     drawText('在...下面 (under)', width / 2 - 150, height / 2 + 60, 25, '#555');
                     drawText('mit', width / 2 + 150, height / 2, 80, '#5cb85c');
                     drawText('发送 (send)', width / 2 + 150, height / 2 + 60, 25, '#555');
                     drawText('点击按钮，观看 "submit" 的故事', width / 2, height - 50, 20, '#007bff');
                    break;
                case 4: // Scene setup
                    drawDoor(width/2, height - 100);
                    drawPerson(100, height - 55);
                    drawDocument(130, height - 80);
                    drawText('一位职员需要向上级提交一份重要文件', width / 2, 50, 20);
                     if(progress > 3) {
                        startTime = time;
                        animationState = 5;
                        playBtn.innerText = '提交文件';
                        playBtn.disabled = false;
                    }
                    break;
                case 5: // Waiting for user to submit
                     drawDoor(width / 2, height - 100);
                     drawPerson(100, height - 55);
                     drawDocument(130, height - 80);
                     drawText('点击按钮，帮他把文件"提交"进去', width / 2, 50, 20, '#007bff');
                    break;
                case 6: // Animation of submitting
                    let moveT = Math.min(progress / 2, 1);
                    let easedMoveT = easeInOutCubic(moveT);
                    let personX = 100 + easedMoveT * (width/2 - 200);
                    drawDoor(width / 2, height - 100);
                    drawPerson(personX, height - 55);
                    
                    if (moveT < 1) {
                        drawDocument(personX + 30, height - 80);
                    } else {
                        let slideT = Math.min((progress - 2) / 1.5, 1);
                        let easedSlideT = easeInOutCubic(slideT);
                        let docX = personX + 30 + easedSlideT * 80;
                        let docY = height - 80 + easedSlideT * 25;
                        let docRotation = easedSlideT * Math.PI / 4;
                        drawDocument(docX, docY, docRotation);
                        
                        // Show sub + mit explanation
                        ctx.save();
                        ctx.globalAlpha = Math.min((progress - 2.5), 1);
                        drawText('sub (下面)', width/2 - 100, height/2 - 40, 25, '#d9534f');
                        drawText('+ mit (发送)', width/2 + 100, height/2 - 40, 25, '#5cb85c');
                        ctx.beginPath();
                        ctx.moveTo(width/2, height/2 - 10);
                        ctx.lineTo(width/2, height - 120);
                        ctx.strokeStyle = '#007bff';
                        ctx.lineWidth = 2;
                        ctx.stroke();
                        ctx.restore();
                    }

                    if(progress > 4) {
                        startTime = time;
                        animationState = 7;
                    }
                    break;
                case 7: // Final conclusion
                    let finalT = Math.min(progress, 1);
                    drawText('submit', width / 2, height / 2, 100, '#007bff', finalT);
                    drawText('提交 = 从下面(sub)发送(mit)', width / 2, height / 2 + 80, 30, '#333', finalT);
                    if(progress > 3) {
                        playBtn.innerText = "重新播放";
                        playBtn.disabled = false;
                        animationState = 0; // End
                    }
                    break;
            }
            if(animationState !== 0) requestAnimationFrame(animate);
        }

        playBtn.addEventListener('click', () => {
            playBtn.disabled = true;
            startTime = 0;
            if (animationState === 0) {
                animationState = 1;
            } else if (animationState === 3) {
                 animationState = 4;
            } else if (animationState === 5) {
                animationState = 6;
            }
            requestAnimationFrame(animate);
        });
    </script>
</body>
</html> 