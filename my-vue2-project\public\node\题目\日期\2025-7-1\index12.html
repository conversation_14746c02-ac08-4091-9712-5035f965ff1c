<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进程同步与PV操作动画演示</title>
    <style>
        :root {
            --bg-color: #f0f4f8;
            --text-color: #333;
            --header-color: #4a5568;
            --primary-color: #4299e1;
            --secondary-color: #4fd1c5;
            --accent-color: #ed8936;
            --border-color: #e2e8f0;
            --white-color: #fff;
            --code-bg: #1a202c;
            --code-text: #e2e8f0;
            --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            background-color: var(--white-color);
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        header {
            background-color: var(--primary-color);
            color: var(--white-color);
            padding: 20px;
            text-align: center;
        }

        header h1 {
            margin: 0;
            font-size: 1.8rem;
        }

        main {
            display: flex;
            flex-wrap: wrap;
            padding: 20px;
        }

        .left-panel, .right-panel {
            box-sizing: border-box;
            padding: 15px;
        }

        .left-panel {
            flex: 1;
            min-width: 500px;
        }

        .right-panel {
            flex: 1;
            min-width: 400px;
        }

        #animation-canvas {
            width: 100%;
            height: auto;
            aspect-ratio: 1/0.6;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: #fafafa;
        }

        .controls, .explanation, .code-display {
            background-color: var(--bg-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        h2 {
            margin-top: 0;
            color: var(--header-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            font-size: 1.4rem;
        }
        
        h3 {
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .controls button {
            background-color: var(--primary-color);
            color: var(--white-color);
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            margin-right: 10px;
            transition: background-color 0.3s, transform 0.2s;
        }

        .controls button:hover {
            background-color: #2b6cb0;
        }

        .controls button:active {
            transform: scale(0.98);
        }
        
        .controls button:disabled {
            background-color: #a0aec0;
            cursor: not-allowed;
        }

        .explanation p {
            line-height: 1.6;
        }
        
        .explanation strong {
            color: var(--accent-color);
        }

        .code-display pre {
            background-color: var(--code-bg);
            color: var(--code-text);
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: "Fira Code", "Courier New", monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            position: relative;
        }
        
        .code-display pre code span {
            display: block;
            transition: background-color 0.3s;
        }
        
        .highlight {
            background-color: rgba(255, 255, 0, 0.3);
        }
        
        .note {
            background-color: #fffbeb;
            border-left: 4px solid #fbbf24;
            padding: 15px;
            margin-top: 15px;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .interactive-diagram {
            display: flex;
            justify-content: space-around;
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .process-column {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            min-width: 100px;
            padding: 0 5px;
        }

        .process-column h4 {
            margin: 0 0 10px 0;
            color: var(--header-color);
        }

        .code-box, .op-box, .exec-box {
            border: 1px solid var(--border-color);
            padding: 8px 12px;
            margin-bottom: 8px;
            width: 90%;
            text-align: center;
            border-radius: 4px;
            font-family: "Fira Code", monospace;
            font-size: 0.85rem;
        }
        
        .exec-box {
            background-color: #ebf8ff;
            border-style: dashed;
        }

        .op-box {
            background-color: #f0fff4;
        }

        .interactive-box {
            background-color: var(--accent-color);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }

        .interactive-box:hover {
            transform: scale(1.05);
        }

        .explanation-area-hidden {
            display: none;
        }

        .explanation-content {
            display: none; /* Hidden by default */
            margin-top: 15px;
            padding: 15px;
            background: #e6fffa;
            border-left: 4px solid var(--secondary-color);
            border-radius: 4px;
        }
        .explanation-content.active {
            display: block; /* Shown when active */
        }
        .explanation-content .note {
             margin-top: 10px;
             font-size: 0.85rem;
        }

    </style>
</head>
<body>

    <div class="container">
        <header>
            <h1>进程同步与PV操作动画演示</h1>
        </header>

        <main>
            <div class="left-panel">
                <h2>题目与动画演示</h2>
                <p><strong>原题：</strong>进程P1, P2, P3, P4, P5和P6的前驱图如下图所示。若用PV操作控制这6个进程的同步与互斥，请分析其执行过程。</p>
                <canvas id="animation-canvas"></canvas>
                <div class="controls">
                    <button id="start-btn">开始动画</button>
                    <button id="next-step-btn">下一步</button>
                    <button id="reset-btn">重置</button>
                </div>
            </div>

            <div class="right-panel">
                <div class="explanation">
                    <h2>知识点讲解</h2>
                    <h3>1. 什么是前驱图?</h3>
                    <p><strong>前驱图</strong>是一个有向无环图，用来描述多个进程之间必须遵守的执行顺序。图中的每个<strong>节点</strong>代表一个进程，节点间的<strong>箭头</strong>代表了"先于"关系。例如，从P1指向P2的箭头意味着P1进程必须执行完毕，P2进程才能开始执行。</p>
                    
                    <h3>2. 什么是PV操作?</h3>
                    <p>PV操作是用来管理<strong>信号量 (Semaphore)</strong> 的两种原子操作，是解决进程同步问题的经典方法。</p>
                    <ul>
                        <li><p><strong>信号量 (S):</strong> 可以理解成一个特殊的"计数器"，记录了可用资源的数量。在这里，我们用它来记录某个前序进程是否已完成。</p></li>
                        <li><p><strong>P(S) 操作 (等待):</strong> 如果S的值大于0，就把它减1，然后继续执行。如果S的值为0，进程就会被<strong>阻塞(等待)</strong>，直到S的值大于0。</p></li>
                        <li><p><strong>V(S) 操作 (发送信号):</strong> 把S的值加1。如果此时有进程因为等待S而被阻塞，V操作会唤醒其中一个进程。</p></li>
                    </ul>
                </div>

                <div class="explanation">
                    <h2>解题思路</h2>
                    <p>要正确地为这个前驱图填上PV操作，核心思想是：<strong>为每一条依赖关系（箭头）设置一个专属的信号量。</strong></p>
                    <ol>
                        <li><strong>识别依赖关系：</strong> 首先，我们看前驱图中有多少条箭头，每一条都代表一个"等待"关系。
                            <ul>
                                <li>P1 → P2, P1 → P3</li>
                                <li>P2 → P4, P2 → P5</li>
                                <li>P3 → P5</li>
                                <li>P4 → P6, P5 → P6</li>
                            </ul>
                            总共有 <strong>7</strong> 条箭头，所以我们需要 <strong>7</strong> 个信号量（我们命名为S1到S7）。每个信号量的初始值都设为0。
                        </li>
                        <li><strong>前序进程发信号 (V操作)：</strong> 当一个进程完成了自己的工作，它需要"通知"所有依赖它的后续进程。这个"通知"就是 <strong>V操作</strong>。
                            <ul>
                                <li>P1完成后，P2和P3可以开始了。所以P1的末尾需要执行 <code>V(S1)</code> (通知P2) 和 <code>V(S2)</code> (通知P3)。</li>
                                <li>P2完成后，P4和P5可以开始了。所以P2的末尾需要执行 <code>V(S3)</code> (通知P4) 和 <code>V(S4)</code> (通知P5)。</li>
                                <li>...以此类推。</li>
                            </ul>
                        </li>
                        <li><strong>后继进程等待信号 (P操作)：</strong> 当一个进程准备开始时，它必须先"检查"所有它依赖的前序进程是否已完成。这个"检查"就是 <strong>P操作</strong>。
                            <ul>
                                <li>P2开始前，必须等待P1完成。所以P2的开头需要执行 <code>P(S1)</code> (等待P1的信号)。</li>
                                <li>P5开始前，必须等待P2和P3都完成。所以P5的开头需要执行 <code>P(S4)</code> (等P2) 和 <code>P(S5)</code> (等P3)。<strong>这两个P操作的顺序无所谓，但必须都在P5的核心工作开始之前完成。</strong></li>
                                <li>...以此类推。</li>
                            </ul>
                        </li>
                    </ol>
                    <p>遵循这个思路，我们就能构建出下面展示的"正确实现代码"，它能完美地保证所有进程都按前驱图的顺序执行。</p>
                </div>


                <div class="code-display">
                    <h2>原题代码回顾</h2>
                    <div class="note">
                        <p><strong>请注意：</strong> 这是原题中给出的代码框架。经过分析，我们发现这个框架的逻辑与前驱图存在多处矛盾（例如P2和P3都等待S2，P4等待P5等），直接填空无法正确实现同步。因此，我们上面的解题思路是基于前驱图本身，而非这个有问题的框架。</p>
                    </div>
                    <pre><code>S1..S8: semaphore := 0;
Cobegin
    process P1: begin ①; V(S1); end;
    process P2: begin P(S2); V(S3); V(S4); end;
    process P3: begin P(S2); ②; end;
    process P4: begin P(S4); P(S5); V(S8); end;
    process P5: begin P(S6); ③; end;
    process P6: begin ④; P(S8); end;
Coend
</code></pre>
                    <h2>正确实现代码</h2>
                    <div class="note">
                        <strong>对照解题思路：</strong>下面的代码是根据我们刚刚分析的思路得出的正确实现。你可以看到每个P操作和V操作都和前驱图的箭头一一对应。
                    </div>
                    <pre><code id="code-block">// 定义7个信号量，分别对应7条有向边
// 初始值都为0，表示一开始没有任何进程完成
S1, S2, S3, S4, S5, S6, S7 = 0, 0, 0, 0, 0, 0, 0;

Cobegin // 表示以下进程可以并发执行
    <span data-proc="P1">process P1 {</span>
    <span data-proc="P1">    // P1的工作...</span>
    <span data-proc="P1" data-op="V">    V(S1); // 完成，通知P2</span>
    <span data-proc="P1" data-op="V">    V(S2); // 完成，通知P3</span>
    <span data-proc="P1">}</span>

    <span data-proc="P2">process P2 {</span>
    <span data-proc="P2" data-op="P">    P(S1); // 等待P1</span>
    <span data-proc="P2">    // P2的工作...</span>
    <span data-proc="P2" data-op="V">    V(S3); // 完成，通知P4</span>
    <span data-proc="P2" data-op="V">    V(S4); // 完成，通知P5</span>
    <span data-proc="P2">}</span>

    <span data-proc="P3">process P3 {</span>
    <span data-proc="P3" data-op="P">    P(S2); // 等待P1</span>
    <span data-proc="P3">    // P3的工作...</span>
    <span data-proc="P3" data-op="V">    V(S5); // 完成，通知P5</span>
    <span data-proc="P3">}</span>

    <span data-proc="P4">process P4 {</span>
    <span data-proc="P4" data-op="P">    P(S3); // 等待P2</span>
    <span data-proc="P4">    // P4的工作...</span>
    <span data-proc="P4" data-op="V">    V(S6); // 完成，通知P6</span>
    <span data-proc="P4">}</span>

    <span data-proc="P5">process P5 {</span>
    <span data-proc="P5" data-op="P">    P(S4); // 等待P2</span>
    <span data-proc="P5" data-op="P">    P(S5); // 等待P3</span>
    <span data-proc="P5">    // P5的工作...</span>
    <span data-proc="P5" data-op="V">    V(S7); // 完成，通知P6</span>
    <span data-proc="P5">}</span>

    <span data-proc="P6">process P6 {</span>
    <span data-proc="P6" data-op="P">    P(S6); // 等待P4</span>
    <span data-proc="P6" data-op="P">    P(S7); // 等待P5</span>
    <span data-proc="P6">    // P6的工作...</span>
    <span data-proc="P6">}</span>
Coend
</code></pre>
                </div>

                <div class="explanation">
                    <h2>如何看懂原题图示？ (交互式解说)</h2>
                    <p>下面这个区域模拟了原题图片的排版。这张图将6个并发进程按列排开，试图展示它们的执行流程。你可以点击每个数字框，查看该位置的详细分析。</p>
                    <div class="interactive-diagram">
                        <div class="process-column">
                            <h4>Process P1</h4>
                            <div class="code-box">begin</div>
                            <div class="exec-box">P1 执行</div>
                            <div class="op-box interactive-box" data-target="exp-1">①</div>
                            <div class="code-box">end;</div>
                        </div>
                        <div class="process-column">
                            <h4>Process P2</h4>
                            <div class="code-box">begin</div>
                            <div class="op-box interactive-box" data-target="exp-2">②</div>
                            <div class="exec-box">P2 执行</div>
                            <div class="op-box">V(S3);</div>
                            <div class="op-box">V(S4);</div>
                            <div class="code-box">end;</div>
                        </div>
                        <div class="process-column">
                            <h4>Process P3</h4>
                            <div class="code-box">begin</div>
                            <div class="op-box">P(S2);</div>
                            <div class="op-box interactive-box" data-target="exp-3">③</div>
                             <div class="exec-box">P3 执行</div>
                            <div class="op-box interactive-box" data-target="exp-4">④</div>
                            <div class="code-box">end;</div>
                        </div>
                        <div class="process-column">
                            <h4>Process P4</h4>
                            <div class="code-box">begin</div>
                            <div class="op-box">P(S4);</div>
                            <div class="op-box">P(S5);</div>
                            <div class="exec-box">P4 执行</div>
                            <div class="op-box interactive-box" data-target="exp-5">⑤</div>
                            <div class="code-box">end;</div>
                        </div>
                        <div class="process-column">
                            <h4>Process P5</h4>
                            <div class="code-box">begin</div>
                            <div class="op-box">P(S6);</div>
                            <div class="exec-box">P5 执行</div>
                            <div class="op-box">V(S8);</div>
                            <div class="code-box">end;</div>
                        </div>
                        <div class="process-column">
                            <h4>Process P6</h4>
                            <div class="code-box">begin</div>
                            <div class="op-box interactive-box" data-target="exp-6">⑥</div>
                            <div class="exec-box">P6 执行</div>
                            <div class="code-box">end;</div>
                        </div>
                    </div>
                    <div id="explanation-area" class="explanation-area-hidden">
                        <div id="exp-1" class="explanation-content">
                            <h4>框① (P1) 分析</h4>
                            <p><strong>作用：</strong>P1是起始进程，它执行完后需要通知P2和P3。图中已有<code>V(S1)</code>，这是通知P2的信号。因此，这里还需要一个发给P3的信号。</p>
                            <p><strong>应填：</strong><code>V(S2)</code> (如果S2代表P1→P3)。</p>
                        </div>
                        <div id="exp-2" class="explanation-content">
                            <h4>框② (P2) 分析</h4>
                             <p><strong>作用：</strong>P2在执行前，需要等待它的前序进程P1完成。</p>
                             <p><strong>应填：</strong><code>P(S1)</code> (等待P1发出的S1信号)。</p>
                             <p class="note"><strong>注意：</strong>原题代码此处为<code>P(S2)</code>，这是与前驱图矛盾的。如果P2和P3都等待S2，那么P1发出V(S2)后只有一个能被唤醒，这不符合并发要求。</p>
                        </div>
                        <div id="exp-3" class="explanation-content">
                            <h4>框③ (P3) 分析</h4>
                            <p><strong>作用：</strong>P3在执行前，需要等待它的前序进程P1完成。</p>
                            <p><strong>应填：</strong><code>P(S2)</code> (等待P1发出的S2信号)。</p>
                             <p class="note"><strong>注意：</strong>原题代码中P3的<code>begin</code>后已经有<code>P(S2)</code>了，所以这个框③的位置很奇怪，逻辑上不应有操作。这再次说明原题的图示和代码框架是有问题的。</p>
                        </div>
                        <div id="exp-4" class="explanation-content">
                            <h4>框④ (P3) 分析</h4>
                            <p><strong>作用：</strong>P3执行完后，需要通知它的后继进程P5。</p>
                            <p><strong>应填：</strong><code>V(S5)</code> (如果S5代表P3→P5)。</p>
                        </div>
                        <div id="exp-5" class="explanation-content">
                            <h4>框⑤ (P4) 分析</h4>
                            <p><strong>作用：</strong>P4执行完后，需要通知它的后继进程P6。</p>
                            <p><strong>应填：</strong><code>V(S6)</code> (如果S6代表P4→P6)。</p>
                             <p class="note"><strong>注意：</strong>原题代码此处为<code>V(S8)</code>，并且P4等待了P5（<code>P(S5)</code>），这都和前驱图完全不符。</p>
                        </div>
                         <div id="exp-6" class="explanation-content">
                            <h4>框⑥ (P6) 分析</h4>
                            <p><strong>作用：</strong>P6在执行前，需要等待它所有的前序进程(P4和P5)完成。</p>
                            <p><strong>应填：</strong><code>P(S6)</code> (等P4) 和 <code>P(S7)</code> (等P5)。</p>
                             <p class="note"><strong>注意：</strong>原题代码中已有<code>P(S8)</code>，这似乎是想用一个信号量代表"P4或P5完成"，但这是错误的。正确的做法是P6必须分别等待来自P4和P5的信号。</p>
                        </div>
                    </div>
                </div>


                <div class="code-display">
                    <h2>正确实现代码</h2>
                    <div class="note">
                        <strong>对照解题思路：</strong>下面的代码是根据我们刚刚分析的思路得出的正确实现。你可以看到每个P操作和V操作都和前驱图的箭头一一对应。
                    </div>
                    <pre><code id="code-block">// 定义7个信号量，分别对应7条有向边
// 初始值都为0，表示一开始没有任何进程完成
S1, S2, S3, S4, S5, S6, S7 = 0, 0, 0, 0, 0, 0, 0;

Cobegin // 表示以下进程可以并发执行
    <span data-proc="P1">process P1 {</span>
    <span data-proc="P1">    // P1的工作...</span>
    <span data-proc="P1" data-op="V">    V(S1); // 完成，通知P2</span>
    <span data-proc="P1" data-op="V">    V(S2); // 完成，通知P3</span>
    <span data-proc="P1">}</span>

    <span data-proc="P2">process P2 {</span>
    <span data-proc="P2" data-op="P">    P(S1); // 等待P1</span>
    <span data-proc="P2">    // P2的工作...</span>
    <span data-proc="P2" data-op="V">    V(S3); // 完成，通知P4</span>
    <span data-proc="P2" data-op="V">    V(S4); // 完成，通知P5</span>
    <span data-proc="P2">}</span>

    <span data-proc="P3">process P3 {</span>
    <span data-proc="P3" data-op="P">    P(S2); // 等待P1</span>
    <span data-proc="P3">    // P3的工作...</span>
    <span data-proc="P3" data-op="V">    V(S5); // 完成，通知P5</span>
    <span data-proc="P3">}</span>

    <span data-proc="P4">process P4 {</span>
    <span data-proc="P4" data-op="P">    P(S3); // 等待P2</span>
    <span data-proc="P4">    // P4的工作...</span>
    <span data-proc="P4" data-op="V">    V(S6); // 完成，通知P6</span>
    <span data-proc="P4">}</span>

    <span data-proc="P5">process P5 {</span>
    <span data-proc="P5" data-op="P">    P(S4); // 等待P2</span>
    <span data-proc="P5" data-op="P">    P(S5); // 等待P3</span>
    <span data-proc="P5">    // P5的工作...</span>
    <span data-proc="P5" data-op="V">    V(S7); // 完成，通知P6</span>
    <span data-proc="P5">}</span>

    <span data-proc="P6">process P6 {</span>
    <span data-proc="P6" data-op="P">    P(S6); // 等待P4</span>
    <span data-proc="P6" data-op="P">    P(S7); // 等待P5</span>
    <span data-proc="P6">    // P6的工作...</span>
    <span data-proc="P6">}</span>
Coend
</code></pre>
                </div>
            </div>
        </main>
    </div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('animation-canvas');
    const ctx = canvas.getContext('2d');
    
    const startBtn = document.getElementById('start-btn');
    const nextStepBtn = document.getElementById('next-step-btn');
    const resetBtn = document.getElementById('reset-btn');
    const codeLines = document.querySelectorAll('#code-block span');

    // Make canvas sharp on HDPI displays
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);
    
    const STATUS = {
        LOCKED: 'locked',     // 未满足前置条件
        READY: 'ready',       // 等待执行
        RUNNING: 'running',   // 正在执行
        FINISHED: 'finished'  // 执行完毕
    };

    const COLORS = {
        [STATUS.LOCKED]: { bg: '#a0aec0', text: '#fff' },
        [STATUS.READY]: { bg: '#63b3ed', text: '#fff' },
        [STATUS.RUNNING]: { bg: '#ed8936', text: '#fff' },
        [STATUS.FINISHED]: { bg: '#48bb78', text: '#fff' },
        line: '#cbd5e0',
        line_highlight: '#4299e1'
    };
    
    let processes = {};
    let semaphores = {};
    let animationFrameId;
    let runningAnimation = null;

    const getProcessPositions = (width, height) => ({
        P1: { x: width * 0.1, y: height * 0.5 },
        P2: { x: width * 0.35, y: height * 0.25 },
        P3: { x: width * 0.35, y: height * 0.75 },
        P4: { x: width * 0.6, y: height * 0.15 },
        P5: { x: width * 0.6, y: height * 0.5 },
        P6: { x: width * 0.85, y: height * 0.5 }
    });

    const dependencies = {
        P1: [],
        P2: ['S1'],
        P3: ['S2'],
        P4: ['S3'],
        P5: ['S4', 'S5'],
        P6: ['S6', 'S7']
    };

    const signals = {
        P1: ['S1', 'S2'],
        P2: ['S3', 'S4'],
        P3: ['S5'],
        P4: ['S6'],
        P5: ['S7'],
        P6: []
    };
    
    const semaphoreToEdge = {
        S1: ['P1', 'P2'], S2: ['P1', 'P3'],
        S3: ['P2', 'P4'], S4: ['P2', 'P5'],
        S5: ['P3', 'P5'], S6: ['P4', 'P6'],
        S7: ['P5', 'P6']
    };

    function resetState() {
        if (runningAnimation) {
            clearTimeout(runningAnimation);
            runningAnimation = null;
        }
        
        Object.keys(semaphoreToEdge).forEach(key => semaphores[key] = { value: 0, animated: false });

        Object.keys(dependencies).forEach(id => {
            processes[id] = {
                id,
                ...getProcessPositions(canvas.clientWidth, canvas.clientHeight)[id],
                radius: 25,
                status: STATUS.LOCKED,
                runProgress: 0,
                waitsFor: new Set(dependencies[id]),
                signals: signals[id]
            };
        });
        processes['P1'].status = STATUS.READY;

        updateButtons();
        draw();
    }
    
    function highlightCode(procId, opType = null) {
        codeLines.forEach(line => line.classList.remove('highlight'));
        if(procId) {
            const linesToHighlight = document.querySelectorAll(`#code-block span[data-proc="${procId}"]` + (opType ? `[data-op="${opType}"]` : ''));
            linesToHighlight.forEach(line => line.classList.add('highlight'));
        }
    }
    
    function updateButtons() {
        const canStep = Object.values(processes).some(p => p.status === STATUS.READY);
        const isDone = Object.values(processes).every(p => p.status === STATUS.FINISHED);
        
        startBtn.disabled = isDone || runningAnimation !== null;
        nextStepBtn.disabled = isDone || runningAnimation !== null || !canStep;
    }

    function drawProcess(p) {
        // Draw run progress
        if (p.status === STATUS.RUNNING) {
            ctx.beginPath();
            ctx.arc(p.x, p.y, p.radius + 4, -Math.PI / 2, -Math.PI / 2 + (Math.PI * 2 * p.runProgress), false);
            ctx.strokeStyle = COLORS[STATUS.RUNNING].bg;
            ctx.lineWidth = 4;
            ctx.stroke();
        }

        // Draw node
        ctx.beginPath();
        ctx.arc(p.x, p.y, p.radius, 0, 2 * Math.PI);
        ctx.fillStyle = COLORS[p.status].bg;
        ctx.fill();
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw text
        ctx.fillStyle = COLORS[p.status].text;
        ctx.font = 'bold 16px ' + getComputedStyle(document.body).fontFamily;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(p.id, p.x, p.y);
    }
    
    function drawArrow(from, to, highlighted = false) {
        ctx.save();
        const headlen = 10;
        const dx = to.x - from.x;
        const dy = to.y - from.y;
        const angle = Math.atan2(dy, dx);

        const startOffset = from.radius + 2;
        const endOffset = to.radius + 5;
        
        const startX = from.x + Math.cos(angle) * startOffset;
        const startY = from.y + Math.sin(angle) * startOffset;
        const endX = to.x - Math.cos(angle) * endOffset;
        const endY = to.y - Math.sin(angle) * endOffset;

        ctx.beginPath();
        ctx.moveTo(startX, startY);
        ctx.lineTo(endX, endY);
        
        ctx.lineTo(endX - headlen * Math.cos(angle - Math.PI / 6), endY - headlen * Math.sin(angle - Math.PI / 6));
        ctx.moveTo(endX, endY);
        ctx.lineTo(endX - headlen * Math.cos(angle + Math.PI / 6), endY - headlen * Math.sin(angle + Math.PI / 6));

        ctx.strokeStyle = highlighted ? COLORS.line_highlight : COLORS.line;
        ctx.lineWidth = highlighted ? 3 : 2;
        ctx.stroke();
        ctx.restore();
    }
    
    function drawSemaphoreSignal(sem) {
        const edge = semaphoreToEdge[sem];
        if (!edge) return;
        
        const from = processes[edge[0]];
        const to = processes[edge[1]];
        
        ctx.beginPath();
        ctx.arc(from.x, from.y, 5, 0, 2 * Math.PI);
        ctx.fillStyle = COLORS.line_highlight;
        ctx.fill();
    }


    function draw() {
        if (!ctx) return;
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        const baseWidth = 500;
        const baseHeight = 300;
        ctx.save();
        ctx.scale(canvas.clientWidth / baseWidth, canvas.clientHeight / baseHeight);
        
        // Draw all arrows first
        Object.entries(semaphoreToEdge).forEach(([sem, edge]) => {
            const from = processes[edge[0]];
            const to = processes[edge[1]];
            const isHighlighted = semaphores[sem].animated;
            drawArrow(from, to, isHighlighted);
        });
        
        // Draw processes
        Object.values(processes).forEach(p => drawProcess(p));

        ctx.restore();
    }
    
    function step() {
        let somethingHappened = false;

        // Phase 1: Check for finished processes and perform V operations
        const justFinished = Object.values(processes).filter(p => p.status === STATUS.RUNNING && p.runProgress >= 1);
        justFinished.forEach(p => {
            p.status = STATUS.FINISHED;
            highlightCode(p.id);
            p.signals.forEach(sem => {
                semaphores[sem].value++;
                semaphores[sem].animated = true; // For animation
                // Check consumers of this semaphore
                Object.values(processes).forEach(consumer => {
                    if(consumer.waitsFor.has(sem)) {
                        consumer.waitsFor.delete(sem);
                    }
                });
            });
            somethingHappened = true;
        });

        // Phase 2: Check for newly ready processes (P operations)
        const newlyReady = Object.values(processes).filter(p => p.status === STATUS.LOCKED && p.waitsFor.size === 0);
        newlyReady.forEach(p => {
            p.status = STATUS.READY;
            somethingHappened = true;
        });

        // Phase 3: Start running a ready process
        if (!somethingHappened) {
            const processToRun = Object.values(processes).find(p => p.status === STATUS.READY);
            if (processToRun) {
                processToRun.status = STATUS.RUNNING;
                highlightCode(processToRun.id);
                // Simulate running time
                const runInterval = setInterval(() => {
                    processToRun.runProgress += 0.05;
                    if(processToRun.runProgress >= 1) {
                        clearInterval(runInterval);
                        processToRun.runProgress = 1;
                        // V operations will happen in the next step() call
                        highlightCode(processToRun.id, 'V');
                    }
                    draw();
                }, 50);
                somethingHappened = true;
            }
        }
        
        draw();
        updateButtons();
        return somethingHappened;
    }
    
    function animateStepByStep() {
       if (step()) {
         runningAnimation = setTimeout(animateStepByStep, 1500);
       } else {
         runningAnimation = null;
         updateButtons();
         highlightCode(null); // Clear highlight when done
       }
    }

    startBtn.addEventListener('click', () => {
        if(runningAnimation) return;
        startBtn.disabled = true;
        nextStepBtn.disabled = true;
        animateStepByStep();
    });

    nextStepBtn.addEventListener('click', () => {
        step();
    });
    
    resetBtn.addEventListener('click', () => {
        if (animationFrameId) cancelAnimationFrame(animationFrameId);
        resetState();
        highlightCode(null);
    });

    // Interactive Diagram Logic
    const interactiveBoxes = document.querySelectorAll('.interactive-box');
    const explanationArea = document.getElementById('explanation-area');
    const explanationContents = document.querySelectorAll('.explanation-content');

    interactiveBoxes.forEach(box => {
        box.addEventListener('click', () => {
            const targetId = box.dataset.target;
            
            // Show the main explanation area
            explanationArea.classList.remove('explanation-area-hidden');

            // Hide all explanation contents
            explanationContents.forEach(content => {
                content.classList.remove('active');
            });

            // Show the target explanation content
            const targetContent = document.getElementById(targetId);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });

    resetState();
});
</script>

</body>
</html>
