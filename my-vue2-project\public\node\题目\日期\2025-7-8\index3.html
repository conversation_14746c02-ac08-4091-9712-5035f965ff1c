<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件设计知识学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .question-section, .explanation-section {
            margin-bottom: 30px;
        }
        .question p {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 15px;
        }
        .options div {
            margin-bottom: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .options div:hover {
            background-color: #e9f7ef;
        }
        .options .selected {
            background-color: #d4edda;
            border-color: #28a745;
            font-weight: bold;
        }
        .options .correct {
            background-color: #d4edda;
            border-color: #28a745;
            font-weight: bold;
        }
        .explanation-content {
            border-top: 1px dashed #ccc;
            padding-top: 20px;
        }
        .explanation-step {
            margin-bottom: 25px;
            padding: 15px;
            background-color: #f8f8f8;
            border-left: 5px solid #3498db;
            border-radius: 5px;
            display: none; /* Hidden by default */
        }
        .explanation-step.active {
            display: block;
        }
        canvas {
            border: 1px solid #eee;
            background-color: #fcfcfc;
            display: block;
            margin-top: 20px;
            border-radius: 5px;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        button {
            padding: 12px 25px;
            font-size: 1em;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin: 0 10px;
        }
        button:hover {
            background-color: #218838;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .correct-answer-display {
            font-weight: bold;
            color: #28a745;
            margin-top: 15px;
        }
        .explanation-step h3 {
            color: #3498db;
            margin-top: 0;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件设计知识学习与交互演示</h1>

        <div class="question-section">
            <h2>题目回顾</h2>
            <div class="question">
                <p>问题1 [单选题]</p>
                <p>软件设计活动中，（）描述了软件内部、软件和操作系统之间如何通信；（）定义软件系统各主要部件之间的关系。</p>
            </div>
            <div class="options">
                <div id="optionA" data-value="A">A 数据架构设计</div>
                <div id="optionB" data-value="B">B 算法设计</div>
                <div id="optionC" data-value="C">C 过程设计</div>
                <div id="optionD" data-value="D">D 接口设计</div>
            </div>
            <div class="correct-answer-display" style="display: none;">正确答案是：D</div>
        </div>

        <div class="explanation-section">
            <h2>题目解析与知识点</h2>
            <div class="explanation-content">
                <div id="intro-explanation" class="explanation-step active">
                    <h3>引言：软件设计的重要性</h3>
                    <p>软件设计是软件开发过程中至关重要的一步，它将需求转化为可执行的蓝图。软件设计活动主要包括体系结构设计、接口设计、数据设计和过程设计。接下来，我们将逐一学习这些设计活动，并通过动画直观理解它们的作用。</p>
                    <p>请点击“下一步”开始学习。</p>
                </div>

                <div id="step1-structural" class="explanation-step">
                    <h3>1. 结构设计 (Structural Design)</h3>
                    <p><strong>定义：</strong> 结构设计，也称为体系结构设计，主要负责定义软件系统的各个主要部件（或模块）之间是如何组织和相互关联的。它关注的是系统的高层结构，就像一个建筑物的整体框架图。</p>
                    <p><strong>动画演示：</strong> 下面的 Canvas 区域将展示不同模块如何连接，点击模块可以高亮显示其关联。</p>
                    <canvas id="structuralCanvas" width="800" height="400"></canvas>
                </div>

                <div id="step2-data" class="explanation-step">
                    <h3>2. 数据设计 (Data Design)</h3>
                    <p><strong>定义：</strong> 数据设计是将抽象的数据模型（如实体关系图）转换为具体的数据结构定义，例如数据库表、类的数据成员或文件格式。好的数据设计可以优化程序的结构和模块划分，有效降低整个系统的复杂性。</p>
                    <p><strong>动画演示：</strong> 下面的 Canvas 区域将模拟数据结构的创建和操作，帮助您理解数据是如何组织和流动的。</p>
                    <canvas id="dataCanvas" width="800" height="400"></canvas>
                </div>

                <div id="step3-interface" class="explanation-step">
                    <h3>3. 接口设计 (Interface Design)</h3>
                    <p><strong>定义：</strong> 接口设计定义了软件内部不同组件之间、软件与外部系统（如操作系统、其他软件）之间，以及软件与用户之间如何进行通信。它是实现模块间协同工作的桥梁，确保信息能够正确、有效地传递。</p>
                    <p><strong>动画演示：</strong> 下面的 Canvas 区域将通过三个场景来演示接口设计的概念：</p>
                    <ul>
                        <li><strong>场景一：</strong> 软件内部模块间通信</li>
                        <li><strong>场景二：</strong> 软件与操作系统通信</li>
                        <li><strong>场景三：</strong> 软件与用户（人机界面）通信</li>
                    </ul>
                    <canvas id="interfaceCanvas" width="800" height="400"></canvas>
                </div>

                <div id="step4-process" class="explanation-step">
                    <h3>4. 过程设计 (Process Design)</h3>
                    <p><strong>定义：</strong> 过程设计描述了软件模块内部的详细实现逻辑，包括算法、数据处理步骤和控制流程等。它关注的是“如何做”的问题，确保功能能够正确执行。</p>
                    <p><strong>动画演示：</strong> 尽管题目中未直接考查，但在实际软件设计中，过程设计同样重要。下面的 Canvas 区域将模拟一个简单的流程图，展示一个任务的执行步骤。</p>
                    <canvas id="processCanvas" width="800" height="400"></canvas>
                </div>

                <div id="step5-conclusion" class="explanation-step">
                    <h3>总结与回顾</h3>
                    <p>通过上面的学习，您应该对软件设计的各个主要活动有了更深入的理解。</p>
                    <p>回到题目：</p>
                    <p><strong>“描述了软件内部、软件和操作系统之间如何通信”</strong> —— 这正是<strong>接口设计</strong>的职责，它负责定义不同实体间的交互方式。</p>
                    <p><strong>“定义软件系统各主要部件之间的关系”</strong> —— 这正是<strong>结构设计（体系结构设计）</strong>的职责，它关注系统的高层组织。</p>
                    <p>因此，正确答案是 <strong>D 接口设计</strong>。</p>
                    <p>希望这个交互页面能帮助您更好地理解软件设计的基本概念！</p>
                    <p>您可以点击“重置”按钮从头开始学习。</p>
                </div>

            </div>
            <div class="controls">
                <button id="prevBtn" disabled>上一步</button>
                <button id="nextBtn">下一步</button>
                <button id="resetBtn" style="background-color: #f44336;">重置</button>
            </div>
        </div>
    </div>

    <script>
        const explanationSteps = [
            'intro-explanation',
            'step1-structural',
            'step2-data',
            'step3-interface',
            'step4-process',
            'step5-conclusion'
        ];
        let currentStepIndex = 0;

        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const resetBtn = document.getElementById('resetBtn');
        const optionsDiv = document.querySelector('.options');
        const correctAnswerDisplay = document.querySelector('.correct-answer-display');

        // Canvas contexts
        let structuralCtx, dataCtx, interfaceCtx, processCtx;
        let structuralAnimationId, dataAnimationId, interfaceAnimationId, processAnimationId;

        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Canvas contexts
            structuralCtx = document.getElementById('structuralCanvas').getContext('2d');
            dataCtx = document.getElementById('dataCanvas').getContext('2d');
            interfaceCtx = document.getElementById('interfaceCanvas').getContext('2d');
            processCtx = document.getElementById('processCanvas').getContext('2d');

            showCurrentStep();

            optionsDiv.addEventListener('click', (event) => {
                const target = event.target;
                if (target.tagName === 'DIV' && target.parentElement === optionsDiv) {
                    // Remove 'selected' from all options
                    Array.from(optionsDiv.children).forEach(opt => {
                        opt.classList.remove('selected');
                        opt.classList.remove('correct'); // Clear correct highlight on re-selection
                    });
                    // Add 'selected' to clicked option
                    target.classList.add('selected');

                    // Check if it's the correct answer (D)
                    if (target.dataset.value === 'D') {
                        target.classList.add('correct');
                        correctAnswerDisplay.style.display = 'block';
                    } else {
                        correctAnswerDisplay.style.display = 'none';
                    }
                }
            });

            nextBtn.addEventListener('click', () => {
                if (currentStepIndex < explanationSteps.length - 1) {
                    currentStepIndex++;
                    showCurrentStep();
                }
            });

            prevBtn.addEventListener('click', () => {
                if (currentStepIndex > 0) {
                    currentStepIndex--;
                    showCurrentStep();
                }
            });

            resetBtn.addEventListener('click', () => {
                currentStepIndex = 0;
                showCurrentStep();
                // Clear selected option and correct answer display
                Array.from(optionsDiv.children).forEach(opt => {
                    opt.classList.remove('selected');
                    opt.classList.remove('correct');
                });
                correctAnswerDisplay.style.display = 'none';
            });
        });

        function showCurrentStep() {
            // Hide all steps
            document.querySelectorAll('.explanation-step').forEach(step => {
                step.classList.remove('active');
            });

            // Show current step
            document.getElementById(explanationSteps[currentStepIndex]).classList.add('active');

            // Update button states
            prevBtn.disabled = (currentStepIndex === 0);
            nextBtn.disabled = (currentStepIndex === explanationSteps.length - 1);

            // Stop all previous animations
            cancelAnimationFrame(structuralAnimationId);
            cancelAnimationFrame(dataAnimationId);
            cancelAnimationFrame(interfaceAnimationId);
            cancelAnimationFrame(processAnimationId);

            // Run animation for the current step if applicable
            switch (explanationSteps[currentStepIndex]) {
                case 'step1-structural':
                    drawStructuralAnimation();
                    break;
                case 'step2-data':
                    drawDataAnimation();
                    break;
                case 'step3-interface':
                    drawInterfaceAnimation();
                    break;
                case 'step4-process':
                    drawProcessAnimation();
                    break;
            }
        }

        // --- Canvas Drawing Functions ---

        function clearCanvas(ctx, canvas) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // Structural Design Animation
        function drawStructuralAnimation() {
            const canvas = structuralCtx.canvas;
            clearCanvas(structuralCtx, canvas);

            const modules = [
                { name: "模块 A", x: 100, y: 150, width: 100, height: 60, color: "#e74c3c" },
                { name: "模块 B", x: 350, y: 50, width: 100, height: 60, color: "#2ecc71" },
                { name: "模块 C", x: 350, y: 250, width: 100, height: 60, color: "#f1c40f" },
                { name: "模块 D", x: 600, y: 150, width: 100, height: 60, color: "#9b59b6" }
            ];

            const connections = [
                { from: 0, to: 1, text: "调用" }, // A -> B
                { from: 0, to: 2, text: "使用" }, // A -> C
                { from: 1, to: 3, text: "提供数据" }, // B -> D
                { from: 2, to: 3, text: "请求服务" }  // C -> D
            ];

            let highlightedModule = -1;

            function drawModule(ctx, module, isHighlighted = false) {
                ctx.fillStyle = module.color;
                ctx.strokeStyle = isHighlighted ? "#000" : "#555";
                ctx.lineWidth = isHighlighted ? 4 : 2;
                ctx.fillRect(module.x, module.y, module.width, module.height);
                ctx.strokeRect(module.x, module.y, module.width, module.height);

                ctx.fillStyle = "white";
                ctx.font = "16px Arial";
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";
                ctx.fillText(module.name, module.x + module.width / 2, module.y + module.height / 2);
            }

            function drawConnection(ctx, conn) {
                const startModule = modules[conn.from];
                const endModule = modules[conn.to];

                const startX = startModule.x + startModule.width / 2;
                const startY = startModule.y + startModule.height / 2;
                const endX = endModule.x + endModule.width / 2;
                const endY = endModule.y + endModule.height / 2;

                ctx.strokeStyle = "#7f8c8d";
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();

                // Draw arrow head
                const angle = Math.atan2(endY - startY, endX - startX);
                ctx.beginPath();
                ctx.moveTo(endX, endY);
                ctx.lineTo(endX - 10 * Math.cos(angle - Math.PI / 6), endY - 10 * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(endX, endY);
                ctx.lineTo(endX - 10 * Math.cos(angle + Math.PI / 6), endY - 10 * Math.sin(angle + Math.PI / 6));
                ctx.stroke();

                // Draw text
                ctx.fillStyle = "#34495e";
                ctx.font = "14px Arial";
                ctx.textAlign = "center";
                ctx.fillText(conn.text, (startX + endX) / 2, (startY + endY) / 2 - 10);
            }

            function animate() {
                clearCanvas(structuralCtx, canvas);
                connections.forEach(conn => drawConnection(structuralCtx, conn));
                modules.forEach((mod, index) => drawModule(structuralCtx, mod, index === highlightedModule));
                structuralAnimationId = requestAnimationFrame(animate);
            }

            canvas.onclick = (e) => {
                const rect = canvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                highlightedModule = -1;
                for (let i = 0; i < modules.length; i++) {
                    const mod = modules[i];
                    if (mouseX > mod.x && mouseX < mod.x + mod.width &&
                        mouseY > mod.y && mouseY < mod.y + mod.height) {
                        highlightedModule = i;
                        break;
                    }
                }
            };

            animate();
        }

        // Data Design Animation (Simple Stack)
        function drawDataAnimation() {
            const canvas = dataCtx.canvas;
            clearCanvas(dataCtx, canvas);

            let stack = [];
            const MAX_STACK_SIZE = 5;
            const elementHeight = 40;
            const stackX = canvas.width / 2 - 50;
            const stackBottomY = canvas.height - 50;

            let pushAnimationProgress = 0;
            let popAnimationProgress = 0;
            let currentOperation = null; // 'push' or 'pop'
            let popValue = null;

            function drawStack(ctx) {
                ctx.strokeStyle = "#3498db";
                ctx.lineWidth = 3;
                // Draw stack base
                ctx.beginPath();
                ctx.moveTo(stackX, stackBottomY);
                ctx.lineTo(stackX + 100, stackBottomY);
                ctx.stroke();
                // Draw left wall
                ctx.beginPath();
                ctx.moveTo(stackX, stackBottomY);
                ctx.lineTo(stackX, stackBottomY - MAX_STACK_SIZE * elementHeight - 10);
                ctx.stroke();
                // Draw right wall
                ctx.beginPath();
                ctx.moveTo(stackX + 100, stackBottomY);
                ctx.lineTo(stackX + 100, stackBottomY - MAX_STACK_SIZE * elementHeight - 10);
                ctx.stroke();

                // Draw stack elements
                for (let i = 0; i < stack.length; i++) {
                    let yPos = stackBottomY - (i + 1) * elementHeight;
                    if (currentOperation === 'push' && i === stack.length - 1) {
                        yPos = stackBottomY - (i + 1) * elementHeight + (1 - pushAnimationProgress) * elementHeight;
                    }
                    ctx.fillStyle = "#2ecc71";
                    ctx.fillRect(stackX, yPos, 100, elementHeight);
                    ctx.strokeStyle = "#27ae60";
                    ctx.strokeRect(stackX, yPos, 100, elementHeight);

                    ctx.fillStyle = "white";
                    ctx.font = "20px Arial";
                    ctx.textAlign = "center";
                    ctx.textBaseline = "middle";
                    ctx.fillText(stack[i], stackX + 50, yPos + elementHeight / 2);
                }

                // Draw pop animation
                if (currentOperation === 'pop' && popValue !== null) {
                    ctx.fillStyle = "#e74c3c";
                    const popY = stackBottomY - (stack.length + 1) * elementHeight + popAnimationProgress * 50;
                    ctx.fillRect(stackX, popY, 100, elementHeight);
                    ctx.strokeStyle = "#c0392b";
                    ctx.strokeRect(stackX, popY, 100, elementHeight);

                    ctx.fillStyle = "white";
                    ctx.font = "20px Arial";
                    ctx.textAlign = "center";
                    ctx.textBaseline = "middle";
                    ctx.fillText(popValue, stackX + 50, popY + elementHeight / 2);
                }

                ctx.fillStyle = "#333";
                ctx.font = "16px Arial";
                ctx.textAlign = "left";
                ctx.fillText("模拟栈数据结构", 20, 30);
                ctx.fillText("点击下方按钮操作栈", 20, 60);

                // Draw controls
                ctx.fillStyle = "#28a745";
                ctx.fillRect(50, 300, 100, 40);
                ctx.fillStyle = "white";
                ctx.fillText("入栈 (Push)", 55, 325);

                ctx.fillStyle = "#f1c40f";
                ctx.fillRect(170, 300, 100, 40);
                ctx.fillStyle = "white";
                ctx.fillText("出栈 (Pop)", 175, 325);
            }

            function pushValue() {
                if (stack.length < MAX_STACK_SIZE && currentOperation === null) {
                    const newValue = Math.floor(Math.random() * 100);
                    stack.push(newValue);
                    pushAnimationProgress = 0;
                    currentOperation = 'push';
                }
            }

            function popValueFunc() {
                if (stack.length > 0 && currentOperation === null) {
                    popValue = stack.pop();
                    popAnimationProgress = 0;
                    currentOperation = 'pop';
                }
            }

            canvas.onclick = (e) => {
                const rect = canvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                if (mouseX > 50 && mouseX < 150 && mouseY > 300 && mouseY < 340) {
                    pushValue();
                } else if (mouseX > 170 && mouseX < 270 && mouseY > 300 && mouseY < 340) {
                    popValueFunc();
                }
            };

            function animate() {
                clearCanvas(dataCtx, canvas);
                drawStack(dataCtx);

                if (currentOperation === 'push') {
                    pushAnimationProgress += 0.05;
                    if (pushAnimationProgress >= 1) {
                        currentOperation = null;
                        pushAnimationProgress = 0;
                    }
                } else if (currentOperation === 'pop') {
                    popAnimationProgress += 0.05;
                    if (popAnimationProgress >= 1) {
                        currentOperation = null;
                        popAnimationProgress = 0;
                        popValue = null;
                    }
                }

                dataAnimationId = requestAnimationFrame(animate);
            }

            animate();
        }

        // Interface Design Animation
        function drawInterfaceAnimation() {
            const canvas = interfaceCtx.canvas;
            clearCanvas(interfaceCtx, canvas);

            let currentInterfaceScene = 0; // 0: internal, 1: sw-os, 2: sw-human
            let animationFrame = 0;
            const maxFrames = 100;

            function drawArrow(ctx, startX, startY, endX, endY, label, color = "#2c3e50") {
                ctx.strokeStyle = color;
                ctx.fillStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();

                const headlen = 10;
                const angle = Math.atan2(endY - startY, endX - startX);
                ctx.beginPath();
                ctx.moveTo(endX, endY);
                ctx.lineTo(endX - headlen * Math.cos(angle - Math.PI / 6), endY - headlen * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(endX, endY);
                ctx.lineTo(endX - headlen * Math.cos(angle + Math.PI / 6), endY - headlen * Math.sin(angle + Math.PI / 6));
                ctx.stroke();

                ctx.font = "16px Arial";
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";
                ctx.fillText(label, (startX + endX) / 2, (startY + endY) / 2 - 15);
            }

            function drawBox(ctx, text, x, y, width, height, bgColor = "#3498db", textColor = "white") {
                ctx.fillStyle = bgColor;
                ctx.fillRect(x, y, width, height);
                ctx.strokeStyle = "#2980b9";
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, width, height);

                ctx.fillStyle = textColor;
                ctx.font = "18px Arial";
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";
                ctx.fillText(text, x + width / 2, y + height / 2);
            }

            function drawScene1Internal(ctx) {
                drawBox(ctx, "模块 A", 100, 150, 150, 80, "#2ecc71");
                drawBox(ctx, "模块 B", 550, 150, 150, 80, "#e74c3c");

                const startX = 250;
                const endX = 550;
                const y = 190;

                let arrowX = startX + (endX - startX) * (animationFrame / maxFrames);
                drawArrow(ctx, startX, y, arrowX, y, "接口调用 (API)");
            }

            function drawScene2SWOS(ctx) {
                drawBox(ctx, "应用程序", 100, 150, 150, 80, "#f1c40f");
                drawBox(ctx, "操作系统 (OS)", 550, 150, 180, 80, "#9b59b6");

                const startX = 250;
                const endX = 550;
                const y = 190;

                let arrowX = startX + (endX - startX) * (animationFrame / maxFrames);
                drawArrow(ctx, startX, y, arrowX, y, "系统调用 (System Call)");
            }

            function drawScene3SWHuman(ctx) {
                drawBox(ctx, "软件 (UI)", 100, 150, 150, 80, "#3498db");

                // Draw a simple human icon
                ctx.beginPath();
                ctx.arc(600, 160, 30, 0, Math.PI * 2); // Head
                ctx.fillStyle = "#ecf0f1";
                ctx.fill();
                ctx.strokeStyle = "#7f8c8d";
                ctx.lineWidth = 2;
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(600, 190);
                ctx.lineTo(600, 260); // Body
                ctx.moveTo(570, 220);
                ctx.lineTo(630, 220); // Arms
                ctx.moveTo(600, 260);
                ctx.lineTo(580, 300); // Legs
                ctx.moveTo(600, 260);
                ctx.lineTo(620, 300);
                ctx.stroke();
                ctx.fillStyle = "#333";
                ctx.font = "16px Arial";
                ctx.fillText("用户", 600, 320);

                const startX = 250;
                const endX = 570;
                const y = 190;

                let arrowX = startX + (endX - startX) * (animationFrame / maxFrames);
                drawArrow(ctx, startX, y, arrowX, y, "用户交互 (UI)");
            }

            function animateInterface() {
                clearCanvas(interfaceCtx, canvas);

                switch (currentInterfaceScene) {
                    case 0:
                        drawScene1Internal(interfaceCtx);
                        break;
                    case 1:
                        drawScene2SWOS(interfaceCtx);
                        break;
                    case 2:
                        drawScene3SWHuman(interfaceCtx);
                        break;
                }

                animationFrame++;
                if (animationFrame > maxFrames) {
                    animationFrame = 0;
                    currentInterfaceScene = (currentInterfaceScene + 1) % 3; // Cycle through scenes
                }

                interfaceAnimationId = requestAnimationFrame(animateInterface);
            }

            animateInterface();
        }

        // Process Design Animation (Simple Flowchart)
        function drawProcessAnimation() {
            const canvas = processCtx.canvas;
            clearCanvas(processCtx, canvas);

            const nodeWidth = 150;
            const nodeHeight = 60;
            const spacingY = 100;
            const startX = canvas.width / 2;

            const nodes = [
                { text: "开始", type: "start", x: startX, y: 50 },
                { text: "读取数据", type: "process", x: startX, y: 50 + spacingY },
                { text: "数据有效?", type: "decision", x: startX, y: 50 + 2 * spacingY },
                { text: "处理数据", type: "process", x: startX - 100, y: 50 + 3 * spacingY },
                { text: "发送错误", type: "process", x: startX + 100, y: 50 + 3 * spacingY },
                { text: "结束", type: "end", x: startX, y: 50 + 4 * spacingY }
            ];

            function drawNode(ctx, node) {
                ctx.fillStyle = "#3498db";
                ctx.strokeStyle = "#2980b9";
                ctx.lineWidth = 2;

                let x = node.x - nodeWidth / 2;
                let y = node.y;

                if (node.type === "start" || node.type === "end") {
                    ctx.beginPath();
                    ctx.ellipse(node.x, y + nodeHeight / 2, nodeWidth / 2, nodeHeight / 2, 0, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.stroke();
                } else if (node.type === "process") {
                    ctx.fillRect(x, y, nodeWidth, nodeHeight);
                    ctx.strokeRect(x, y, nodeWidth, nodeHeight);
                } else if (node.type === "decision") {
                    ctx.save();
                    ctx.translate(node.x, y + nodeHeight / 2);
                    ctx.rotate(Math.PI / 4);
                    ctx.fillRect(-nodeWidth / 2, -nodeHeight / 2, nodeWidth, nodeHeight);
                    ctx.strokeRect(-nodeWidth / 2, -nodeHeight / 2, nodeWidth, nodeHeight);
                    ctx.restore();
                }

                ctx.fillStyle = "white";
                ctx.font = "16px Arial";
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";
                ctx.fillText(node.text, node.x, y + nodeHeight / 2);
            }

            function drawFlowArrow(ctx, fromNode, toNode, label = "") {
                const fromX = fromNode.x;
                const fromY = fromNode.y + nodeHeight / 2;
                const toX = toNode.x;
                let toY = toNode.y + nodeHeight / 2;

                if (fromNode.type === "decision") { // For decision nodes, adjust start points
                    if (toNode.x < fromNode.x) { // "否" path
                        fromX = fromNode.x - (nodeWidth / 2) * Math.cos(Math.PI / 4);
                        fromY = fromNode.y + (nodeHeight / 2) * Math.sin(Math.PI / 4);
                    } else if (toNode.x > fromNode.x) { // "是" path (for this example, it's the right branch)
                         fromX = fromNode.x + (nodeWidth / 2) * Math.cos(Math.PI / 4);
                         fromY = fromNode.y + (nodeHeight / 2) * Math.sin(Math.PI / 4);
                    } else { // "是" path (straight down)
                        fromY = fromNode.y + nodeHeight; // bottom of diamond
                    }
                }

                ctx.strokeStyle = "#2c3e50";
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                if (fromNode.type === "decision" && toNode.x !== fromNode.x) {
                    ctx.lineTo(toX, fromY); // Horizontal line
                    ctx.lineTo(toX, toY); // Vertical line
                } else {
                    ctx.lineTo(toX, toY);
                }
                ctx.stroke();

                const headlen = 10;
                const angle = Math.atan2(toY - fromY, toX - fromX); // This angle calculation might need adjustment for complex paths
                // Simple arrow head pointing to the end of the last segment
                let finalAngle = Math.atan2(toY - (fromNode.type === "decision" && toNode.x !== fromNode.x ? fromY : fromY), toX - (fromNode.type === "decision" && toNode.x !== fromNode.x ? toX : fromX));
                if (fromNode.type === "decision" && toNode.x !== fromNode.x) { // for right/left branches
                    finalAngle = toNode.x < fromNode.x ? Math.PI : 0; // Point left or right
                    if (toY > fromY) finalAngle = Math.PI/2; // If also going down
                    ctx.beginPath();
                    ctx.moveTo(toX, toY);
                    ctx.lineTo(toX - headlen * Math.cos(finalAngle - Math.PI / 6), toY - headlen * Math.sin(finalAngle - Math.PI / 6));
                    ctx.moveTo(toX, toY);
                    ctx.lineTo(toX - headlen * Math.cos(finalAngle + Math.PI / 6), toY - headlen * Math.sin(finalAngle + Math.PI / 6));
                    ctx.stroke();

                    // Text for branches
                    ctx.fillStyle = "#333";
                    ctx.font = "14px Arial";
                    if (toNode.x < fromNode.x) { // "否"
                        ctx.textAlign = "right";
                        ctx.fillText("否", (fromNode.x + toNode.x) / 2 + 10, fromNode.y + nodeHeight / 2 - 20);
                    } else if (toNode.x > fromNode.x) { // "是"
                        ctx.textAlign = "left";
                        ctx.fillText("是", (fromNode.x + toNode.x) / 2 - 10, fromNode.y + nodeHeight / 2 - 20);
                    }
                } else { // Straight arrows
                    ctx.beginPath();
                    ctx.moveTo(toX, toY);
                    ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
                    ctx.moveTo(toX, toY);
                    ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
                    ctx.stroke();
                }


            }

            function animateProcess() {
                clearCanvas(processCtx, canvas);

                nodes.forEach(node => drawNode(processCtx, node));

                // Draw connections
                drawFlowArrow(processCtx, nodes[0], nodes[1]);
                drawFlowArrow(processCtx, nodes[1], nodes[2]);
                drawFlowArrow(processCtx, nodes[2], nodes[3], "是"); // Yes branch
                drawFlowArrow(processCtx, nodes[2], nodes[4], "否"); // No branch
                drawFlowArrow(processCtx, nodes[3], nodes[5]);
                drawFlowArrow(processCtx, nodes[4], nodes[5]);


                processAnimationId = requestAnimationFrame(animateProcess);
            }
            animateProcess();
        }

    </script>
</body>
</html> 