<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>5G网络速率学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .question-box h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-align: center;
            font-size: 1.1rem;
            font-weight: bold;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d4aa;
            background: rgba(0, 212, 170, 0.3);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.3);
            animation: shake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        canvas {
            width: 100%;
            height: 500px;
            border-radius: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            line-height: 1.6;
        }

        .generation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .generation-card {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .generation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(108, 92, 231, 0.3);
        }

        .generation-card.g1 {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .generation-card.g2 {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .generation-card.g3 {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .generation-card.g4 {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .generation-card.g5 {
            background: linear-gradient(135deg, #e17055, #d63031);
        }

        .generation-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .generation-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .generation-number {
            position: absolute;
            top: 10px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 3px 8px;
            border-radius: 5px;
            color: #2d3436;
            font-weight: bold;
        }

        .step {
            background: rgba(116, 185, 255, 0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateX(5px);
        }

        .speed-demo {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .speed-demo h4 {
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .speed-bar {
            background: rgba(255, 255, 255, 0.2);
            height: 30px;
            border-radius: 15px;
            margin: 10px 0;
            position: relative;
            overflow: hidden;
        }

        .speed-fill {
            height: 100%;
            background: linear-gradient(90deg, #74b9ff, #0984e3);
            border-radius: 15px;
            transition: width 2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #01a3a4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .speed-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .concept-box {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
        }

        .concept-box h4 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .comparison-table .highlight-row {
            background: rgba(0, 212, 170, 0.1);
            font-weight: bold;
        }

        .download-demo {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .download-item {
            text-align: center;
            margin: 10px;
            padding: 20px;
            background: rgba(116, 185, 255, 0.1);
            border-radius: 15px;
            min-width: 150px;
        }

        .download-item .icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .download-item .time {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📶 5G网络速率学习</h1>
            <p>探索5G时代的超高速网络体验</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2>📝 考试题目</h2>
                <p><strong>2019年我国将在多地展开5G试点，届时将在人口密集区为用户提供（ ）bps的用户体验速率。</strong></p>
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 100M bps
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 1G bps
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 10G bps
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 1T bps
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 什么是5G网络？</h2>
            <div class="explanation">
                <p><span class="highlight">5G</span>是第五代移动通信技术，相比4G具有<strong>超高速率、超低延迟、超大连接</strong>的特点。</p>
                <p>🔑 <strong>核心特性：</strong>在人口密集区，5G可以为用户提供1Gbps的体验速率，这是4G的10倍以上！</p>
            </div>
            
            <div class="speed-demo">
                <h4>📊 5G vs 4G速率对比</h4>
                <div>4G速率：100Mbps</div>
                <div class="speed-bar">
                    <div class="speed-fill" style="width: 10%;">100M</div>
                </div>
                <div>5G速率：1Gbps</div>
                <div class="speed-bar">
                    <div class="speed-fill" style="width: 100%;">1G</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📱 移动通信技术发展历程</h2>
            <div class="generation-grid">
                <div class="generation-card g1" onclick="showGenerationDemo('1G')">
                    <div class="generation-number">1</div>
                    <span class="icon">📞</span>
                    <h3>1G</h3>
                    <p>模拟语音</p>
                    <p>2.4Kbps</p>
                </div>
                <div class="generation-card g2" onclick="showGenerationDemo('2G')">
                    <div class="generation-number">2</div>
                    <span class="icon">📱</span>
                    <h3>2G</h3>
                    <p>数字语音+短信</p>
                    <p>64Kbps</p>
                </div>
                <div class="generation-card g3" onclick="showGenerationDemo('3G')">
                    <div class="generation-number">3</div>
                    <span class="icon">🌐</span>
                    <h3>3G</h3>
                    <p>移动互联网</p>
                    <p>2Mbps</p>
                </div>
                <div class="generation-card g4" onclick="showGenerationDemo('4G')">
                    <div class="generation-number">4</div>
                    <span class="icon">📺</span>
                    <h3>4G</h3>
                    <p>高清视频</p>
                    <p>100Mbps</p>
                </div>
                <div class="generation-card g5" onclick="showGenerationDemo('5G')">
                    <div class="generation-number">5</div>
                    <span class="icon">🚀</span>
                    <h3>5G</h3>
                    <p>万物互联</p>
                    <p>1Gbps</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 5G速率体验演示</h2>
            <div class="canvas-container">
                <canvas id="speedCanvas"></canvas>
                <div class="speed-indicator" id="speedIndicator">
                    🎯 点击按钮开始演示
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startSpeedDemo()">🚀 速率对比演示</button>
                <button class="btn" onclick="showDownloadDemo()">📥 下载时间对比</button>
                <button class="btn" onclick="show5GApplications()">🌐 5G应用场景</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2>🔍 详细解析每个选项</h2>

            <div class="step">
                <h3>选项A：100M bps ❌</h3>
                <p><strong>错误原因：</strong>100Mbps是<span class="highlight">4G网络</span>的典型速率</p>
                <ul>
                    <li>📱 <strong>4G速率：</strong>4G LTE网络的典型用户体验速率约为100Mbps</li>
                    <li>📊 <strong>技术对比：</strong>这个速率对于4G来说已经很不错，但对5G来说太低了</li>
                    <li>🎯 <strong>应用场景：</strong>100Mbps可以支持高清视频，但无法满足5G的应用需求</li>
                    <li>⚡ <strong>发展趋势：</strong>5G的目标就是要大幅超越4G的性能</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项B：1G bps ✅</h3>
                <p><strong>正确答案！</strong>5G在人口密集区的<span class="highlight">用户体验速率</span>目标是1Gbps</p>
                <ul>
                    <li>🚀 <strong>5G标准：</strong>根据ITU-R IMT-2020标准，5G用户体验速率为1Gbps</li>
                    <li>🏙️ <strong>人口密集区：</strong>在城市中心等人口密集区域可以达到这个速率</li>
                    <li>📈 <strong>性能提升：</strong>相比4G的100Mbps，提升了10倍</li>
                    <li>🌐 <strong>实际意义：</strong>这个速率可以支持4K/8K视频、VR/AR等应用</li>
                </ul>
                <div class="concept-box">
                    <h4>🔑 关键理解</h4>
                    <p>1Gbps是5G在人口密集区的用户体验速率目标，这是5G相比4G的重大突破，为各种新兴应用提供了基础。</p>
                </div>
            </div>

            <div class="step">
                <h3>选项C：10G bps ❌</h3>
                <p><strong>错误原因：</strong>10Gbps是5G的<span class="highlight">峰值速率</span>，不是用户体验速率</p>
                <ul>
                    <li>⚡ <strong>峰值 vs 体验：</strong>10Gbps是理论峰值，实际用户很难达到</li>
                    <li>📊 <strong>技术区别：</strong>峰值速率是实验室条件下的最高速率</li>
                    <li>🏙️ <strong>实际应用：</strong>在人口密集区，用户体验速率通常是1Gbps</li>
                    <li>🎯 <strong>标准定义：</strong>ITU标准明确区分了峰值速率和用户体验速率</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项D：1T bps ❌</h3>
                <p><strong>错误原因：</strong>1Tbps远超当前5G技术能力，<span class="highlight">过于夸大</span></p>
                <ul>
                    <li>🚫 <strong>技术限制：</strong>目前的5G技术无法达到1Tbps的速率</li>
                    <li>📡 <strong>物理约束：</strong>受到频谱、功率等物理因素限制</li>
                    <li>🔮 <strong>未来技术：</strong>可能需要6G或更先进的技术才能实现</li>
                    <li>💰 <strong>成本考虑：</strong>即使技术可行，成本也会非常高昂</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📊 5G技术指标详解</h2>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>技术指标</th>
                        <th>4G LTE</th>
                        <th class="highlight-row">5G目标</th>
                        <th>提升倍数</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="highlight-row">
                        <td><strong>用户体验速率</strong></td>
                        <td>100 Mbps</td>
                        <td><strong>1 Gbps</strong></td>
                        <td>10倍</td>
                    </tr>
                    <tr>
                        <td>峰值速率</td>
                        <td>1 Gbps</td>
                        <td>10 Gbps</td>
                        <td>10倍</td>
                    </tr>
                    <tr>
                        <td>延迟</td>
                        <td>10 ms</td>
                        <td>1 ms</td>
                        <td>10倍</td>
                    </tr>
                    <tr>
                        <td>连接密度</td>
                        <td>10万/km²</td>
                        <td>100万/km²</td>
                        <td>10倍</td>
                    </tr>
                    <tr>
                        <td>能效</td>
                        <td>基准</td>
                        <td>100倍提升</td>
                        <td>100倍</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🌐 5G三大应用场景</h2>

            <div class="concept-box">
                <h4>📱 eMBB（增强移动宽带）</h4>
                <p><strong>特点：</strong>超高速率，支持4K/8K视频、VR/AR应用</p>
                <p><strong>速率要求：</strong>用户体验速率1Gbps，峰值速率10Gbps</p>
            </div>

            <div class="concept-box">
                <h4>🚗 uRLLC（超可靠低延迟通信）</h4>
                <p><strong>特点：</strong>超低延迟，支持自动驾驶、工业控制</p>
                <p><strong>延迟要求：</strong>端到端延迟1ms，可靠性99.999%</p>
            </div>

            <div class="concept-box">
                <h4>🏭 mMTC（大规模机器类通信）</h4>
                <p><strong>特点：</strong>大连接，支持物联网、智慧城市</p>
                <p><strong>连接要求：</strong>每平方公里支持100万个连接</p>
            </div>
        </div>

        <div class="section">
            <h2>📥 实际应用：下载时间对比</h2>

            <div class="download-demo">
                <div class="download-item">
                    <div class="icon">🎵</div>
                    <div>音乐 (5MB)</div>
                    <div class="time">4G: 0.4秒</div>
                    <div class="time">5G: 0.04秒</div>
                </div>
                <div class="download-item">
                    <div class="icon">📱</div>
                    <div>APP (100MB)</div>
                    <div class="time">4G: 8秒</div>
                    <div class="time">5G: 0.8秒</div>
                </div>
                <div class="download-item">
                    <div class="icon">🎬</div>
                    <div>电影 (1GB)</div>
                    <div class="time">4G: 80秒</div>
                    <div class="time">5G: 8秒</div>
                </div>
                <div class="download-item">
                    <div class="icon">🎮</div>
                    <div>游戏 (10GB)</div>
                    <div class="time">4G: 13分钟</div>
                    <div class="time">5G: 1.3分钟</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 记忆技巧和考试要点</h2>

            <div class="explanation">
                <h3>🧠 记忆口诀</h3>
                <p style="font-size: 1.3rem; text-align: center; font-weight: bold; margin: 20px 0;">
                    "5G网络真神奇，人口密集一个G，<br>
                    比起4G快十倍，万物互联新时代"
                </p>

                <h3>🔑 关键词记忆法</h3>
                <p><strong>5G特点：</strong>"超高速率"、"超低延迟"、"超大连接"</p>
                <p><strong>用户体验速率：</strong>"1Gbps"、"人口密集区"、"实际体验"</p>
                <p><strong>技术提升：</strong>"比4G快10倍"、"万物互联"、"新应用"</p>

                <h3>🎯 考试技巧</h3>
                <ul>
                    <li>看到"5G"、"人口密集区"、"用户体验速率" → <span class="highlight">1Gbps</span></li>
                    <li>区分"用户体验速率"和"峰值速率" → <span class="highlight">1G vs 10G</span></li>
                    <li>记住5G相比4G的提升 → <span class="highlight">10倍速率提升</span></li>
                    <li>了解5G的三大应用场景 → <span class="highlight">eMBB、uRLLC、mMTC</span></li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎉 学习总结</h2>
            <div class="explanation">
                <h3>📚 核心知识点</h3>
                <ul>
                    <li><span class="highlight">5G用户体验速率</span>：在人口密集区达到1Gbps</li>
                    <li><span class="highlight">技术提升</span>：相比4G的100Mbps提升10倍</li>
                    <li><span class="highlight">三大特点</span>：超高速率、超低延迟、超大连接</li>
                    <li><span class="highlight">应用场景</span>：eMBB、uRLLC、mMTC三大类</li>
                </ul>

                <h3>⚡ 实际应用</h3>
                <ul>
                    <li>高清视频：支持4K/8K视频流畅播放</li>
                    <li>VR/AR：提供沉浸式虚拟现实体验</li>
                    <li>自动驾驶：超低延迟保证行车安全</li>
                    <li>物联网：支持大规模设备连接</li>
                </ul>
            </div>

            <div class="controls">
                <button class="btn" onclick="reviewQuestion()">🔄 重新答题</button>
                <button class="btn" onclick="showSummary()">📋 显示总结</button>
            </div>
        </div>
    </div>

    <script>
        // Canvas相关变量
        const canvas = document.getElementById('speedCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;
        let currentDemo = 'none';

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 题目交互逻辑
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'B') {
                    this.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n解释：根据5G技术标准，在人口密集区5G网络可以为用户提供1Gbps的体验速率，这是5G相比4G的重大提升。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    progressFill.style.width = '25%';
                    setTimeout(() => {
                        let hint = '';
                        switch(answer) {
                            case 'A':
                                hint = '100Mbps是4G网络的典型速率，5G要比这快得多。';
                                break;
                            case 'C':
                                hint = '10Gbps是5G理论峰值速率，但用户体验速率通常是1Gbps。';
                                break;
                            case 'D':
                                hint = '1Tbps太高了，目前的5G技术还达不到这个速率。';
                                break;
                        }
                        alert('❌ 答案不正确！\n\n提示：' + hint + '\n\n记住：5G在人口密集区的用户体验速率是1Gbps！');
                    }, 500);
                }
            });
        });

        // 绘制速度条
        function drawSpeedBar(x, y, width, height, speed, maxSpeed, label, color) {
            ctx.save();

            // 背景
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, width, height);

            // 速度填充
            const fillWidth = (speed / maxSpeed) * width;
            ctx.fillStyle = color;
            ctx.fillRect(x, y, fillWidth, height);

            // 标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText(label, x, y - 5);

            // 速度值
            ctx.textAlign = 'right';
            ctx.fillText(speed >= 1000 ? (speed/1000).toFixed(1) + 'G' : speed + 'M', x + width + 5, y + height/2 + 5);

            ctx.restore();
        }

        // 绘制下载进度
        function drawDownloadProgress(x, y, progress, filename, color) {
            ctx.save();

            const width = 200;
            const height = 20;

            // 文件图标和名称
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('📁 ' + filename, x, y - 5);

            // 进度条背景
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(x, y, width, height);

            // 进度条填充
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width * progress, height);

            // 进度百分比
            ctx.fillStyle = '#2c3e50';
            ctx.font = '10px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(Math.round(progress * 100) + '%', x + width/2, y + height/2 + 3);

            ctx.restore();
        }

        // 绘制5G应用场景
        function drawApplication(x, y, icon, title, description, active = false) {
            ctx.save();

            const size = 80;

            // 背景圆圈
            ctx.fillStyle = active ? '#00d4aa' : '#f8f9fa';
            ctx.beginPath();
            ctx.arc(x, y, size/2, 0, Math.PI * 2);
            ctx.fill();

            if (active) {
                ctx.strokeStyle = '#00d4aa';
                ctx.lineWidth = 3;
                ctx.stroke();
            }

            // 图标
            ctx.font = '30px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(icon, x, y);

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(title, x, y + size/2 + 15);

            // 描述
            ctx.font = '10px Microsoft YaHei';
            ctx.fillText(description, x, y + size/2 + 30);

            ctx.restore();
        }

        // 速率对比演示
        function startSpeedDemo() {
            currentDemo = 'speed';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const speedIndicator = document.getElementById('speedIndicator');
            speedIndicator.textContent = '🚀 移动通信技术速率对比';

            animateSpeedDemo();
        }

        function animateSpeedDemo() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            const step = Math.floor(animationStep / 60);
            const progress = (animationStep % 60) / 60;

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('移动通信技术速率对比', centerX, 50);

            const generations = [
                { name: '1G', speed: 0.0024, color: '#fd79a8', icon: '📞' },
                { name: '2G', speed: 0.064, color: '#fdcb6e', icon: '📱' },
                { name: '3G', speed: 2, color: '#74b9ff', icon: '🌐' },
                { name: '4G', speed: 100, color: '#00d4aa', icon: '📺' },
                { name: '5G', speed: 1000, color: '#e17055', icon: '🚀' }
            ];

            const maxSpeed = 1000;
            const barHeight = 40;
            const barSpacing = 60;
            const startY = 100;

            generations.forEach((gen, index) => {
                if (index <= step) {
                    const y = startY + index * barSpacing;
                    const currentSpeed = index === step ? gen.speed * progress : gen.speed;

                    // 图标
                    ctx.font = '20px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(gen.icon, centerX - 200, y + barHeight/2 + 5);

                    // 速度条
                    drawSpeedBar(centerX - 150, y, 300, barHeight, currentSpeed, maxSpeed, gen.name, gen.color);
                }
            });

            if (step >= generations.length) {
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('5G速率是4G的10倍，是1G的400万倍！', centerX, startY + generations.length * barSpacing + 50);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateSpeedDemo);
        }

        // 下载时间对比演示
        function showDownloadDemo() {
            currentDemo = 'download';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const speedIndicator = document.getElementById('speedIndicator');
            speedIndicator.textContent = '📥 不同网络下载时间对比';

            animateDownloadDemo();
        }

        function animateDownloadDemo() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            const step = Math.floor(animationStep / 120);
            const progress = (animationStep % 120) / 120;

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('下载1GB电影所需时间对比', centerX, 50);

            const scenarios = [
                { name: '4G网络', time: '80秒', speed: 100, color: '#74b9ff' },
                { name: '5G网络', time: '8秒', speed: 1000, color: '#00d4aa' }
            ];

            scenarios.forEach((scenario, index) => {
                if (index <= step) {
                    const y = 120 + index * 150;
                    const downloadProgress = index === step ? progress : 1;

                    // 网络类型
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = 'bold 16px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText(scenario.name, centerX - 200, y);

                    // 下载进度
                    drawDownloadProgress(centerX - 200, y + 20, downloadProgress, '电影.mp4 (1GB)', scenario.color);

                    // 时间显示
                    ctx.fillStyle = scenario.color;
                    ctx.font = 'bold 18px Microsoft YaHei';
                    ctx.textAlign = 'right';
                    ctx.fillText(scenario.time, centerX + 200, y + 35);
                }
            });

            if (step >= scenarios.length) {
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('5G下载速度是4G的10倍！', centerX, 450);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateDownloadDemo);
        }

        // 5G应用场景演示
        function show5GApplications() {
            currentDemo = 'applications';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const speedIndicator = document.getElementById('speedIndicator');
            speedIndicator.textContent = '🌐 5G典型应用场景';

            animate5GApplications();
        }

        function animate5GApplications() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            const step = Math.floor(animationStep / 60);

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('5G典型应用场景', centerX, 50);

            const applications = [
                { icon: '🚗', title: '自动驾驶', desc: '超低延迟控制', x: centerX - 150, y: centerY - 80 },
                { icon: '🏥', title: '远程医疗', desc: '高清视频传输', x: centerX + 150, y: centerY - 80 },
                { icon: '🏭', title: '工业4.0', desc: '万物互联', x: centerX - 150, y: centerY + 80 },
                { icon: '🎮', title: 'VR/AR', desc: '沉浸式体验', x: centerX + 150, y: centerY + 80 },
                { icon: '🌐', title: '智慧城市', desc: '全面连接', x: centerX, y: centerY }
            ];

            applications.forEach((app, index) => {
                const active = index <= step;
                drawApplication(app.x, app.y, app.icon, app.title, app.desc, active);

                // 连接线到中心
                if (active && index < 4) {
                    ctx.strokeStyle = '#00d4aa';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(app.x, app.y);
                    ctx.lineTo(centerX, centerY);
                    ctx.stroke();
                }
            });

            if (step >= applications.length) {
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('5G开启万物互联新时代！', centerX, centerY + 150);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animate5GApplications);
        }

        // 代际演示
        function showGenerationDemo(generation) {
            currentDemo = generation;
            if (animationId) cancelAnimationFrame(animationId);

            const speedIndicator = document.getElementById('speedIndicator');
            const descriptions = {
                '1G': '📞 1G：模拟语音通信，速率2.4Kbps',
                '2G': '📱 2G：数字语音+短信，速率64Kbps',
                '3G': '🌐 3G：移动互联网，速率2Mbps',
                '4G': '📺 4G：高清视频，速率100Mbps',
                '5G': '🚀 5G：万物互联，速率1Gbps'
            };
            speedIndicator.textContent = descriptions[generation];

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(descriptions[generation], centerX, centerY);
        }

        // 重置演示
        function resetDemo() {
            if (animationId) cancelAnimationFrame(animationId);
            currentDemo = 'none';
            animationStep = 0;

            const speedIndicator = document.getElementById('speedIndicator');
            speedIndicator.textContent = '🎯 点击按钮开始演示';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('选择上方按钮查看5G网络演示', centerX, centerY);
        }

        // 重新答题功能
        function reviewQuestion() {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            document.getElementById('progressFill').style.width = '0%';

            document.querySelector('.question-box').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            setTimeout(() => {
                document.querySelector('.question-box').classList.add('pulse');
                setTimeout(() => {
                    document.querySelector('.question-box').classList.remove('pulse');
                }, 2000);
            }, 500);
        }

        // 显示总结
        function showSummary() {
            const summary = `
🎯 5G网络速率学习总结

✅ 正确答案：B - 1G bps

📚 核心概念：
• 5G是第五代移动通信技术
• 具有超高速率、超低延迟、超大连接三大特点
• 在人口密集区用户体验速率达到1Gbps

📊 5G技术指标：
• 用户体验速率：1 Gbps（人口密集区）
• 峰值速率：10 Gbps（理论最高）
• 延迟：1 ms（超低延迟）
• 连接密度：100万/km²（大规模连接）

🔍 选项分析：
✅ B. 1G bps - 正确
   这是5G在人口密集区的用户体验速率目标

❌ A. 100M bps - 错误
   这是4G网络的典型速率

❌ C. 10G bps - 错误
   这是5G的峰值速率，不是用户体验速率

❌ D. 1T bps - 错误
   远超当前5G技术能力

📱 移动通信发展历程：
1G (1980s): 📞 模拟语音 - 2.4Kbps
2G (1990s): 📱 数字语音+短信 - 64Kbps
3G (2000s): 🌐 移动互联网 - 2Mbps
4G (2010s): 📺 高清视频 - 100Mbps
5G (2020s): 🚀 万物互联 - 1Gbps

🌐 5G三大应用场景：
• eMBB（增强移动宽带）：4K/8K视频、VR/AR
• uRLLC（超可靠低延迟）：自动驾驶、工业控制
• mMTC（大规模机器通信）：物联网、智慧城市

📥 下载时间对比（1GB电影）：
• 4G网络：约80秒
• 5G网络：约8秒
• 速度提升：10倍

🧠 记忆技巧：
• "5G网络真神奇，人口密集一个G"
• "比起4G快十倍，万物互联新时代"
• 关键词：1Gbps、人口密集区、用户体验速率

⚡ 考试要点：
• 看到"5G"+"人口密集区"+"用户体验速率" → 1Gbps
• 区分用户体验速率(1G)和峰值速率(10G)
• 记住5G相比4G的10倍速率提升
• 了解5G的三大特点和应用场景

🔑 关键理解：
5G在人口密集区的用户体验速率是1Gbps，这是
相比4G的重大突破，为VR/AR、自动驾驶、物联网
等新兴应用提供了技术基础，开启万物互联时代。

🎉 恭喜掌握5G网络速率知识！
            `;

            alert(summary);
        }

        // 添加CSS动画类
        const style = document.createElement('style');
        style.textContent = `
            .pulse {
                animation: pulse 1s ease-in-out 3;
            }

            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.02);
                    box-shadow: 0 25px 50px rgba(255, 107, 107, 0.4);
                }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的欢迎提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                welcome.innerHTML = `
                    <h3>🌟 欢迎来到5G学习世界！</h3>
                    <p>让我们一起探索5G时代的超高速网络</p>
                    <button onclick="this.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 15px;
                        margin-top: 15px;
                        cursor: pointer;
                    ">开始学习 🚀</button>
                `;
                document.body.appendChild(welcome);
            }, 1000);
        });

        // 初始化
        resetDemo();
    </script>
</body>
</html>
