<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RISC指令系统 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: justify;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .quiz-container {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .quiz-question {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            background: white;
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .comparison-table {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .comparison-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .comparison-card:hover {
            transform: translateY(-5px);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .risc-card .card-title {
            color: #28a745;
        }

        .cisc-card .card-title {
            color: #dc3545;
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">RISC 指令系统</h1>
            <p class="subtitle">精简指令集计算机 - 交互式学习体验</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是RISC？</h2>
            <div class="canvas-container">
                <canvas id="introCanvas" width="800" height="400"></canvas>
            </div>
            <p class="explanation">
                <span class="highlight">RISC（Reduced Instruction Set Computer）</span>是精简指令集计算机的缩写。
                它的核心思想是：<strong>简化指令集，提高执行效率</strong>。就像一个高效的工厂，
                每个工人只专注做一件事，但做得非常快！
            </p>
            <button class="interactive-btn" onclick="startIntroAnimation()">开始动画演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">RISC vs CISC 对比</h2>
            <div class="comparison-table">
                <div class="comparison-card risc-card">
                    <h3 class="card-title">RISC (精简指令集)</h3>
                    <ul class="feature-list">
                        <li>✅ 指令种类少，功能简单</li>
                        <li>✅ 指令长度固定</li>
                        <li>✅ 只有取数/存数指令访问存储器</li>
                        <li>✅ 大量通用寄存器</li>
                        <li>✅ 执行速度快</li>
                    </ul>
                </div>
                <div class="comparison-card cisc-card">
                    <h3 class="card-title">CISC (复杂指令集)</h3>
                    <ul class="feature-list">
                        <li>❌ 指令种类多，功能强</li>
                        <li>❌ 指令长度可变</li>
                        <li>❌ 多种指令可访问存储器</li>
                        <li>❌ 寄存器数量较少</li>
                        <li>❌ 指令复杂，执行较慢</li>
                    </ul>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="comparisonCanvas" width="800" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="startComparisonAnimation()">对比动画演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">RISC的四大特点</h2>
            <div class="canvas-container">
                <canvas id="featuresCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                让我们通过互动游戏来理解RISC的四个核心特点：
            </p>
            <button class="interactive-btn" onclick="startFeaturesGame()">开始特点游戏</button>
        </div>

        <div class="section">
            <h2 class="section-title">题目解析</h2>
            <div class="quiz-container">
                <div class="quiz-question">
                    以下关于精简指令集计算机（RISC）指令系统特点的叙述中，错误的是（ ）
                </div>
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectOption(this, false)">
                        A. 对存储器操作进行限制，使控制简单化
                    </div>
                    <div class="quiz-option" onclick="selectOption(this, true)">
                        B. 指令种类多，指令功能强
                    </div>
                    <div class="quiz-option" onclick="selectOption(this, false)">
                        C. 设置大量通用寄存器
                    </div>
                    <div class="quiz-option" onclick="selectOption(this, false)">
                        D. 选取使用频率较高的一些指令，提高执行速度
                    </div>
                </div>
                <div id="quizExplanation" style="display: none;">
                    <h3>解析：</h3>
                    <p><strong>正确答案：B</strong></p>
                    <p>RISC的核心特点是<span class="highlight">指令种类少、功能简单</span>，而不是"指令种类多，指令功能强"。
                    选项B描述的是CISC（复杂指令集）的特点，与RISC相反。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationId;
        let currentStep = 0;

        // 获取Canvas元素
        const introCanvas = document.getElementById('introCanvas');
        const introCtx = introCanvas.getContext('2d');
        const comparisonCanvas = document.getElementById('comparisonCanvas');
        const comparisonCtx = comparisonCanvas.getContext('2d');
        const featuresCanvas = document.getElementById('featuresCanvas');
        const featuresCtx = featuresCanvas.getContext('2d');

        // 动画参数
        let time = 0;
        let particles = [];

        // 初始化
        function init() {
            drawIntroScene();
            drawComparisonScene();
            drawFeaturesScene();
        }

        // 绘制介绍场景
        function drawIntroScene() {
            introCtx.clearRect(0, 0, introCanvas.width, introCanvas.height);

            // 背景渐变
            const gradient = introCtx.createLinearGradient(0, 0, introCanvas.width, introCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            introCtx.fillStyle = gradient;
            introCtx.fillRect(0, 0, introCanvas.width, introCanvas.height);

            // 绘制RISC标题
            introCtx.font = 'bold 48px Arial';
            introCtx.fillStyle = '#667eea';
            introCtx.textAlign = 'center';
            introCtx.fillText('RISC', introCanvas.width / 2, 80);

            // 绘制副标题
            introCtx.font = '24px Arial';
            introCtx.fillStyle = '#6c757d';
            introCtx.fillText('Reduced Instruction Set Computer', introCanvas.width / 2, 120);

            // 绘制简单的处理器示意图
            drawProcessor(introCtx, introCanvas.width / 2, 250, 150, 80);
        }

        // 绘制处理器
        function drawProcessor(ctx, x, y, width, height) {
            // 处理器外框
            ctx.fillStyle = '#495057';
            ctx.fillRect(x - width/2, y - height/2, width, height);

            // 内部电路
            ctx.strokeStyle = '#28a745';
            ctx.lineWidth = 2;
            for (let i = 0; i < 5; i++) {
                ctx.beginPath();
                ctx.moveTo(x - width/2 + 20, y - height/2 + 15 + i * 12);
                ctx.lineTo(x + width/2 - 20, y - height/2 + 15 + i * 12);
                ctx.stroke();
            }

            // 标签
            ctx.font = '16px Arial';
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.fillText('CPU', x, y + 5);
        }

        // 开始介绍动画
        function startIntroAnimation() {
            let step = 0;
            const maxSteps = 100;

            function animate() {
                introCtx.clearRect(0, 0, introCanvas.width, introCanvas.height);

                // 背景
                const gradient = introCtx.createLinearGradient(0, 0, introCanvas.width, introCanvas.height);
                gradient.addColorStop(0, '#f8f9fa');
                gradient.addColorStop(1, '#e9ecef');
                introCtx.fillStyle = gradient;
                introCtx.fillRect(0, 0, introCanvas.width, introCanvas.height);

                // 动画效果
                const progress = step / maxSteps;

                // 标题动画
                introCtx.save();
                introCtx.translate(introCanvas.width / 2, 80);
                introCtx.scale(1 + Math.sin(step * 0.1) * 0.1, 1 + Math.sin(step * 0.1) * 0.1);
                introCtx.font = 'bold 48px Arial';
                introCtx.fillStyle = `hsl(${240 + step * 2}, 70%, 60%)`;
                introCtx.textAlign = 'center';
                introCtx.fillText('RISC', 0, 0);
                introCtx.restore();

                // 粒子效果
                if (particles.length < 20) {
                    particles.push({
                        x: Math.random() * introCanvas.width,
                        y: Math.random() * introCanvas.height,
                        vx: (Math.random() - 0.5) * 2,
                        vy: (Math.random() - 0.5) * 2,
                        life: 100
                    });
                }

                particles.forEach((particle, index) => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    particle.life--;

                    if (particle.life <= 0) {
                        particles.splice(index, 1);
                        return;
                    }

                    introCtx.fillStyle = `rgba(102, 126, 234, ${particle.life / 100})`;
                    introCtx.beginPath();
                    introCtx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                    introCtx.fill();
                });

                // 处理器动画
                drawAnimatedProcessor(introCtx, introCanvas.width / 2, 250, 150, 80, step);

                step++;
                if (step < maxSteps) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 绘制动画处理器
        function drawAnimatedProcessor(ctx, x, y, width, height, step) {
            // 处理器外框
            ctx.fillStyle = '#495057';
            ctx.fillRect(x - width/2, y - height/2, width, height);

            // 动画电路
            ctx.strokeStyle = '#28a745';
            ctx.lineWidth = 3;
            for (let i = 0; i < 5; i++) {
                const alpha = Math.sin(step * 0.2 + i * 0.5) * 0.5 + 0.5;
                ctx.globalAlpha = alpha;
                ctx.beginPath();
                ctx.moveTo(x - width/2 + 20, y - height/2 + 15 + i * 12);
                ctx.lineTo(x + width/2 - 20, y - height/2 + 15 + i * 12);
                ctx.stroke();
            }
            ctx.globalAlpha = 1;

            // 标签
            ctx.font = '16px Arial';
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.fillText('RISC CPU', x, y + 5);
        }

        // 绘制对比场景
        function drawComparisonScene() {
            comparisonCtx.clearRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);

            // 背景
            const gradient = comparisonCtx.createLinearGradient(0, 0, comparisonCanvas.width, comparisonCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            comparisonCtx.fillStyle = gradient;
            comparisonCtx.fillRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);

            // RISC侧
            drawComparisonSide(comparisonCtx, 200, 150, 'RISC', '#28a745', true);

            // CISC侧
            drawComparisonSide(comparisonCtx, 600, 150, 'CISC', '#dc3545', false);

            // VS标志
            comparisonCtx.font = 'bold 36px Arial';
            comparisonCtx.fillStyle = '#6c757d';
            comparisonCtx.textAlign = 'center';
            comparisonCtx.fillText('VS', comparisonCanvas.width / 2, 160);
        }

        // 绘制对比侧
        function drawComparisonSide(ctx, x, y, title, color, isSimple) {
            // 标题
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = color;
            ctx.textAlign = 'center';
            ctx.fillText(title, x, y - 80);

            // 处理器框
            ctx.fillStyle = color;
            ctx.fillRect(x - 60, y - 40, 120, 80);

            // 内部结构
            ctx.fillStyle = 'white';
            if (isSimple) {
                // RISC - 简单结构
                for (let i = 0; i < 3; i++) {
                    ctx.fillRect(x - 40 + i * 25, y - 20, 15, 40);
                }
            } else {
                // CISC - 复杂结构
                for (let i = 0; i < 6; i++) {
                    for (let j = 0; j < 3; j++) {
                        ctx.fillRect(x - 45 + i * 15, y - 25 + j * 17, 10, 12);
                    }
                }
            }

            // 标签
            ctx.font = '12px Arial';
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.fillText(isSimple ? '简单' : '复杂', x, y + 5);
        }

        // 开始对比动画
        function startComparisonAnimation() {
            let step = 0;
            const maxSteps = 150;

            function animate() {
                comparisonCtx.clearRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);

                // 背景
                const gradient = comparisonCtx.createLinearGradient(0, 0, comparisonCanvas.width, comparisonCanvas.height);
                gradient.addColorStop(0, '#f8f9fa');
                gradient.addColorStop(1, '#e9ecef');
                comparisonCtx.fillStyle = gradient;
                comparisonCtx.fillRect(0, 0, comparisonCanvas.width, comparisonCanvas.height);

                // 动画对比
                drawAnimatedComparison(comparisonCtx, step);

                step++;
                if (step < maxSteps) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 绘制动画对比
        function drawAnimatedComparison(ctx, step) {
            const progress = step / 150;

            // RISC侧 - 快速简单动画
            drawAnimatedSide(ctx, 200, 150, 'RISC', '#28a745', true, step, 0.3);

            // CISC侧 - 慢速复杂动画
            drawAnimatedSide(ctx, 600, 150, 'CISC', '#dc3545', false, step, 0.1);

            // 动画VS标志
            ctx.save();
            ctx.translate(comparisonCanvas.width / 2, 160);
            ctx.scale(1 + Math.sin(step * 0.2) * 0.2, 1 + Math.sin(step * 0.2) * 0.2);
            ctx.font = 'bold 36px Arial';
            ctx.fillStyle = `hsl(${step * 2}, 70%, 50%)`;
            ctx.textAlign = 'center';
            ctx.fillText('VS', 0, 0);
            ctx.restore();
        }

        // 绘制动画侧
        function drawAnimatedSide(ctx, x, y, title, color, isSimple, step, speed) {
            // 标题
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = color;
            ctx.textAlign = 'center';
            ctx.fillText(title, x, y - 80);

            // 处理器框
            ctx.fillStyle = color;
            ctx.fillRect(x - 60, y - 40, 120, 80);

            // 动画内部结构
            ctx.fillStyle = 'white';
            if (isSimple) {
                // RISC - 简单快速动画
                for (let i = 0; i < 3; i++) {
                    const alpha = Math.sin(step * speed + i * 1) * 0.5 + 0.5;
                    ctx.globalAlpha = alpha;
                    ctx.fillRect(x - 40 + i * 25, y - 20, 15, 40);
                }
            } else {
                // CISC - 复杂慢速动画
                for (let i = 0; i < 6; i++) {
                    for (let j = 0; j < 3; j++) {
                        const alpha = Math.sin(step * speed + i * 0.5 + j * 0.3) * 0.3 + 0.7;
                        ctx.globalAlpha = alpha;
                        ctx.fillRect(x - 45 + i * 15, y - 25 + j * 17, 10, 12);
                    }
                }
            }
            ctx.globalAlpha = 1;

            // 标签
            ctx.font = '12px Arial';
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.fillText(isSimple ? '简单快速' : '复杂缓慢', x, y + 5);
        }

        // 绘制特点场景
        function drawFeaturesScene() {
            featuresCtx.clearRect(0, 0, featuresCanvas.width, featuresCanvas.height);

            // 背景
            const gradient = featuresCtx.createLinearGradient(0, 0, featuresCanvas.width, featuresCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            featuresCtx.fillStyle = gradient;
            featuresCtx.fillRect(0, 0, featuresCanvas.width, featuresCanvas.height);

            // 绘制四个特点
            const features = [
                { text: '指令种类少\n功能简单', x: 200, y: 120, color: '#28a745' },
                { text: '指令长度固定\n格式统一', x: 600, y: 120, color: '#17a2b8' },
                { text: '存储器访问\n仅限取存指令', x: 200, y: 350, color: '#ffc107' },
                { text: '大量通用寄存器\n提高效率', x: 600, y: 350, color: '#dc3545' }
            ];

            features.forEach((feature, index) => {
                drawFeatureBox(featuresCtx, feature.x, feature.y, feature.text, feature.color, index);
            });

            // 中心连接
            featuresCtx.strokeStyle = '#6c757d';
            featuresCtx.lineWidth = 2;
            featuresCtx.setLineDash([5, 5]);
            featuresCtx.beginPath();
            featuresCtx.moveTo(200, 120);
            featuresCtx.lineTo(600, 350);
            featuresCtx.moveTo(600, 120);
            featuresCtx.lineTo(200, 350);
            featuresCtx.stroke();
            featuresCtx.setLineDash([]);
        }

        // 绘制特点框
        function drawFeatureBox(ctx, x, y, text, color, index) {
            // 框
            ctx.fillStyle = color;
            ctx.fillRect(x - 80, y - 40, 160, 80);

            // 边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(x - 80, y - 40, 160, 80);

            // 文字
            ctx.font = '14px Arial';
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            const lines = text.split('\n');
            lines.forEach((line, i) => {
                ctx.fillText(line, x, y - 10 + i * 20);
            });

            // 编号
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.fillText((index + 1).toString(), x - 60, y - 20);
        }

        // 开始特点游戏
        function startFeaturesGame() {
            let gameStep = 0;
            let selectedFeature = -1;
            const maxSteps = 200;

            // 添加点击事件监听器
            featuresCanvas.onclick = function(e) {
                const rect = featuresCanvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 检查点击的特点框
                const features = [
                    { x: 200, y: 120 },
                    { x: 600, y: 120 },
                    { x: 200, y: 350 },
                    { x: 600, y: 350 }
                ];

                features.forEach((feature, index) => {
                    if (x >= feature.x - 80 && x <= feature.x + 80 &&
                        y >= feature.y - 40 && y <= feature.y + 40) {
                        selectedFeature = index;
                        showFeatureDetail(index);
                    }
                });
            };

            function animate() {
                featuresCtx.clearRect(0, 0, featuresCanvas.width, featuresCanvas.height);

                // 背景
                const gradient = featuresCtx.createLinearGradient(0, 0, featuresCanvas.width, featuresCanvas.height);
                gradient.addColorStop(0, '#f8f9fa');
                gradient.addColorStop(1, '#e9ecef');
                featuresCtx.fillStyle = gradient;
                featuresCtx.fillRect(0, 0, featuresCanvas.width, featuresCanvas.height);

                // 绘制动画特点
                drawAnimatedFeatures(featuresCtx, gameStep, selectedFeature);

                gameStep++;
                if (gameStep < maxSteps) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 绘制动画特点
        function drawAnimatedFeatures(ctx, step, selected) {
            const features = [
                { text: '指令种类少\n功能简单', x: 200, y: 120, color: '#28a745' },
                { text: '指令长度固定\n格式统一', x: 600, y: 120, color: '#17a2b8' },
                { text: '存储器访问\n仅限取存指令', x: 200, y: 350, color: '#ffc107' },
                { text: '大量通用寄存器\n提高效率', x: 600, y: 350, color: '#dc3545' }
            ];

            features.forEach((feature, index) => {
                const isSelected = selected === index;
                const scale = isSelected ? 1.1 + Math.sin(step * 0.3) * 0.1 : 1;
                const alpha = isSelected ? 1 : 0.7 + Math.sin(step * 0.1 + index) * 0.3;

                ctx.save();
                ctx.translate(feature.x, feature.y);
                ctx.scale(scale, scale);
                ctx.globalAlpha = alpha;

                drawFeatureBox(ctx, 0, 0, feature.text, feature.color, index);

                ctx.restore();
            });

            // 动画连接线
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 2;
            ctx.globalAlpha = 0.5 + Math.sin(step * 0.1) * 0.3;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(200, 120);
            ctx.lineTo(600, 350);
            ctx.moveTo(600, 120);
            ctx.lineTo(200, 350);
            ctx.stroke();
            ctx.setLineDash([]);
            ctx.globalAlpha = 1;

            // 提示文字
            ctx.font = '18px Arial';
            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('点击任意特点框了解详情', featuresCanvas.width / 2, 50);
        }

        // 显示特点详情
        function showFeatureDetail(index) {
            const details = [
                '特点1：指令种类少，功能简单\n\nRISC只保留最常用的指令，每条指令功能单一，\n执行速度快，硬件实现简单。',
                '特点2：指令长度固定，格式统一\n\n所有指令都是相同长度（如32位），\n简化了指令解码过程，提高执行效率。',
                '特点3：存储器访问仅限取存指令\n\n只有LOAD和STORE指令可以访问内存，\n其他运算都在寄存器间进行，提高速度。',
                '特点4：大量通用寄存器\n\n提供更多寄存器（如32个），减少内存访问，\n提高数据处理效率。'
            ];

            alert(details[index]);
        }

        // 题目选择函数
        function selectOption(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 标记选择
            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    document.getElementById('quizExplanation').style.display = 'block';
                }, 500);
            } else {
                element.classList.add('wrong');
                setTimeout(() => {
                    alert('答案错误，请重新思考RISC的特点！');
                }, 500);
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            init();
        };
    </script>
</body>
</html>
