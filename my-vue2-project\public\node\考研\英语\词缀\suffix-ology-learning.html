<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：-ology（学科、研究）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        #storyCanvas {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .word-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .word-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(20px);
        }

        .word-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .word-card.show {
            opacity: 1;
            transform: translateY(0);
        }

        .word-card h3 {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .word-parts {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 15px 0;
            flex-wrap: wrap;
            gap: 10px;
        }

        .root-part {
            background: #ff6b6b;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        .suffix-part {
            background: #4ecdc4;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        .plus {
            font-size: 1.2rem;
            color: #666;
            margin: 0 5px;
        }

        .meaning {
            color: #666;
            font-size: 1rem;
            margin-top: 10px;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #ffa500;
            font-size: 1rem;
            line-height: 1.7;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .interactive-hint {
            text-align: center;
            color: #667eea;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>词缀探索：-ology</h1>
            <p>跟随小探险家，发现知识的奥秘</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🔍 知识探索之旅</h2>
                <p>小明是一个充满好奇心的探险家，他发现了一个神奇的知识王国。在这个王国里，每个学科都有自己的城堡，而这些城堡都有一个共同的标志："-ology"！</p>
            </div>

            <div class="canvas-container">
                <canvas id="storyCanvas"></canvas>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择这个故事？</h3>
                <p><strong>教学设计理念：</strong>我用"知识王国"的比喻，是因为"-ology"这个词缀代表"学科、研究"，就像不同的城堡代表不同的知识领域。通过视觉化的城堡和探险家，让抽象的语言概念变得具体可感。每个城堡都标有"-ology"，强化词缀的视觉记忆。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="startAnimation()">开始探索</button>
                <button class="btn" onclick="showWords()">显示单词</button>
                <button class="btn" onclick="resetAnimation()">重新开始</button>
            </div>

            <div class="interactive-hint">
                💡 点击按钮开始动画，观察小探险家如何发现不同的学科城堡
            </div>
        </div>

        <div class="word-breakdown" id="wordCards">
            <div class="word-card">
                <h3>Biology</h3>
                <div class="word-parts">
                    <span class="root-part">bio</span>
                    <span class="plus">+</span>
                    <span class="suffix-part">-ology</span>
                </div>
                <div class="meaning">生命 + 学科 = 生物学</div>
                <p style="margin-top: 10px; color: #666;">研究生命和生物体的科学</p>
            </div>

            <div class="word-card">
                <h3>Psychology</h3>
                <div class="word-parts">
                    <span class="root-part">psycho</span>
                    <span class="plus">+</span>
                    <span class="suffix-part">-ology</span>
                </div>
                <div class="meaning">心理 + 学科 = 心理学</div>
                <p style="margin-top: 10px; color: #666;">研究人类行为和心理过程的科学</p>
            </div>

            <div class="word-card">
                <h3>Geology</h3>
                <div class="word-parts">
                    <span class="root-part">geo</span>
                    <span class="plus">+</span>
                    <span class="suffix-part">-ology</span>
                </div>
                <div class="meaning">地球 + 学科 = 地质学</div>
                <p style="margin-top: 10px; color: #666;">研究地球结构和历史的科学</p>
            </div>

            <div class="word-card">
                <h3>Technology</h3>
                <div class="word-parts">
                    <span class="root-part">techno</span>
                    <span class="plus">+</span>
                    <span class="suffix-part">-ology</span>
                </div>
                <div class="meaning">技术 + 学科 = 技术学</div>
                <p style="margin-top: 10px; color: #666;">应用科学知识解决实际问题</p>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别模式：</strong>当你看到以"-ology"结尾的单词时，它通常表示一门学科或研究领域。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li>找到词根（如bio、psycho、geo）</li>
                <li>理解词根含义（生命、心理、地球）</li>
                <li>加上"-ology"的含义（学科、研究）</li>
                <li>组合成完整意思（生物学、心理学、地质学）</li>
            </ol>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let explorer = { x: 50, y: canvas.height - 100, targetX: 50, targetY: canvas.height - 100 };
        let castles = [
            { x: 200, y: canvas.height - 150, name: 'Biology', color: '#ff6b6b', visible: false },
            { x: 400, y: canvas.height - 150, name: 'Psychology', color: '#4ecdc4', visible: false },
            { x: 600, y: canvas.height - 150, name: 'Geology', color: '#45b7d1', visible: false },
            { x: 800, y: canvas.height - 150, name: 'Technology', color: '#96ceb4', visible: false }
        ];
        let currentCastle = 0;

        function drawExplorer(x, y) {
            // 探险家身体
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(x - 10, y - 30, 20, 30);
            
            // 头部
            ctx.fillStyle = '#FDBCB4';
            ctx.beginPath();
            ctx.arc(x, y - 40, 12, 0, Math.PI * 2);
            ctx.fill();
            
            // 帽子
            ctx.fillStyle = '#228B22';
            ctx.fillRect(x - 15, y - 50, 30, 8);
            ctx.beginPath();
            ctx.arc(x, y - 46, 15, Math.PI, 0);
            ctx.fill();
            
            // 眼睛
            ctx.fillStyle = 'black';
            ctx.beginPath();
            ctx.arc(x - 5, y - 42, 2, 0, Math.PI * 2);
            ctx.arc(x + 5, y - 42, 2, 0, Math.PI * 2);
            ctx.fill();
            
            // 背包
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(x + 8, y - 25, 8, 15);
        }

        function drawCastle(castle) {
            if (!castle.visible) return;
            
            const { x, y, name, color } = castle;
            
            // 城堡主体
            ctx.fillStyle = color;
            ctx.fillRect(x - 40, y, 80, 60);
            
            // 城堡塔楼
            ctx.fillRect(x - 50, y - 20, 20, 80);
            ctx.fillRect(x + 30, y - 20, 20, 80);
            ctx.fillRect(x - 15, y - 30, 30, 90);
            
            // 旗帜
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(x - 5, y - 50, 20, 15);
            
            // 门
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(x - 10, y + 30, 20, 30);
            
            // 标签
            ctx.fillStyle = 'white';
            ctx.fillRect(x - 35, y + 70, 70, 25);
            ctx.fillStyle = 'black';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y + 85);
            ctx.fillText('-ology', x, y + 95);
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#87CEEB');
            gradient.addColorStop(1, '#98FB98');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制地面
            ctx.fillStyle = '#90EE90';
            ctx.fillRect(0, canvas.height - 80, canvas.width, 80);
            
            // 绘制城堡
            castles.forEach(drawCastle);
            
            // 绘制探险家
            drawExplorer(explorer.x, explorer.y);
            
            // 绘制说明文字
            if (animationState === 'exploring') {
                ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                ctx.fillRect(10, 10, 300, 60);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('小探险家正在探索知识王国...', 20, 30);
                ctx.fillText('每个城堡代表一门学科！', 20, 50);
            }
        }

        function animateExplorer() {
            if (animationState === 'exploring' && currentCastle < castles.length) {
                const target = castles[currentCastle];
                const dx = target.x - explorer.x;
                const dy = target.y + 50 - explorer.y;
                
                if (Math.abs(dx) > 5 || Math.abs(dy) > 5) {
                    explorer.x += dx * 0.05;
                    explorer.y += dy * 0.05;
                } else {
                    // 到达城堡，显示城堡
                    castles[currentCastle].visible = true;
                    currentCastle++;
                    
                    if (currentCastle >= castles.length) {
                        animationState = 'completed';
                    }
                }
            }
            
            drawScene();
            requestAnimationFrame(animateExplorer);
        }

        function startAnimation() {
            animationState = 'exploring';
            currentCastle = 0;
            explorer.x = 50;
            explorer.y = canvas.height - 100;
            castles.forEach(castle => castle.visible = false);
        }

        function showWords() {
            const cards = document.querySelectorAll('.word-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('show');
                }, index * 200);
            });
        }

        function resetAnimation() {
            animationState = 'idle';
            currentCastle = 0;
            explorer.x = 50;
            explorer.y = canvas.height - 100;
            castles.forEach(castle => castle.visible = false);
            
            const cards = document.querySelectorAll('.word-card');
            cards.forEach(card => card.classList.remove('show'));
        }

        // 初始化
        drawScene();
        animateExplorer();

        // 点击单词卡片的交互
        document.querySelectorAll('.word-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.animation = 'bounce 0.6s ease';
                setTimeout(() => {
                    this.style.animation = '';
                }, 600);
            });
        });
    </script>
</body>
</html>
