<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>矩阵是什么 - 零基础互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            opacity: 0;
            animation: fadeInDown 1.2s ease-out forwards;
        }

        .main-title {
            font-size: 4rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 6px 30px rgba(0,0,0,0.3);
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        .subtitle {
            font-size: 1.4rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
            font-weight: 300;
        }

        .learning-card {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 50px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            opacity: 0;
            transform: translateY(60px);
            animation: slideUp 1s ease-out forwards;
        }

        .learning-card:nth-child(2) { animation-delay: 0.3s; }
        .learning-card:nth-child(3) { animation-delay: 0.6s; }
        .learning-card:nth-child(4) { animation-delay: 0.9s; }
        .learning-card:nth-child(5) { animation-delay: 1.2s; }

        .card-title {
            font-size: 2.5rem;
            color: #2d3436;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
        }

        .card-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #74b9ff, #6c5ce7);
            border-radius: 2px;
        }

        .explanation {
            font-size: 1.3rem;
            line-height: 2;
            color: #636e72;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, rgba(116, 185, 255, 0.1), rgba(108, 92, 231, 0.1));
            border-radius: 15px;
            border-left: 5px solid #74b9ff;
        }

        .canvas-wrapper {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        canvas {
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: all 0.4s ease;
            background: white;
        }

        canvas:hover {
            transform: scale(1.02) translateY(-5px);
            box-shadow: 0 20px 45px rgba(0,0,0,0.25);
        }

        .control-panel {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 40px 0;
        }

        .magic-btn {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 30px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(116, 185, 255, 0.4);
            position: relative;
            overflow: hidden;
        }

        .magic-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .magic-btn:hover::before {
            left: 100%;
        }

        .magic-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(116, 185, 255, 0.6);
        }

        .magic-btn:active {
            transform: translateY(-1px);
        }

        .step-counter {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        .step-dot {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 15px;
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
            transition: all 0.4s ease;
            cursor: pointer;
        }

        .step-dot.active {
            background: linear-gradient(135deg, #00b894, #00cec9);
            transform: scale(1.3);
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.5);
        }

        .highlight-box {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
            font-size: 1.2rem;
            font-weight: 600;
            box-shadow: 0 10px 25px rgba(253, 203, 110, 0.4);
            animation: gentlePulse 2s ease-in-out infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(60px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes titleGlow {
            from {
                text-shadow: 0 6px 30px rgba(255,255,255,0.3);
            }
            to {
                text-shadow: 0 6px 40px rgba(255,255,255,0.6);
            }
        }

        @keyframes gentlePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes matrixGlow {
            0%, 100% { 
                box-shadow: 0 0 20px rgba(116, 185, 255, 0.3);
            }
            50% { 
                box-shadow: 0 0 40px rgba(116, 185, 255, 0.6);
            }
        }

        .matrix-glow {
            animation: matrixGlow 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">矩阵是什么？</h1>
            <p class="subtitle">从零开始的数学魔法之旅 ✨</p>
        </div>

        <div class="learning-card">
            <h2 class="card-title">🎯 矩阵的本质</h2>
            <div class="explanation">
                <strong>矩阵就像一个数字的停车场！</strong><br>
                想象一个停车场，有很多行和列，每个停车位都有一个数字。矩阵就是把数字整齐地排列在行和列中的表格。
            </div>
            <div class="canvas-wrapper">
                <canvas id="introCanvas" width="700" height="400"></canvas>
            </div>
            <div class="control-panel">
                <button class="magic-btn" onclick="startIntroAnimation()">🎬 开始动画演示</button>
            </div>
        </div>

        <div class="learning-card">
            <h2 class="card-title">📚 分步学习矩阵</h2>
            <div class="step-counter">
                <div class="step-dot active" id="step1" onclick="goToStep(1)">1</div>
                <div class="step-dot" id="step2" onclick="goToStep(2)">2</div>
                <div class="step-dot" id="step3" onclick="goToStep(3)">3</div>
                <div class="step-dot" id="step4" onclick="goToStep(4)">4</div>
                <div class="step-dot" id="step5" onclick="goToStep(5)">5</div>
            </div>
            <div class="canvas-wrapper">
                <canvas id="stepCanvas" width="800" height="500"></canvas>
            </div>
            <div class="control-panel">
                <button class="magic-btn" onclick="nextStep()">下一步 ➡️</button>
                <button class="magic-btn" onclick="resetSteps()">🔄 重新开始</button>
            </div>
        </div>

        <div class="learning-card">
            <h2 class="card-title">🎮 互动练习</h2>
            <div class="highlight-box">
                点击下面的按钮，看看不同大小的矩阵长什么样！
            </div>
            <div class="canvas-wrapper">
                <canvas id="practiceCanvas" width="900" height="600"></canvas>
            </div>
            <div class="control-panel">
                <button class="magic-btn" onclick="createMatrix(2,2)">2×2 矩阵</button>
                <button class="magic-btn" onclick="createMatrix(3,3)">3×3 矩阵</button>
                <button class="magic-btn" onclick="createMatrix(2,4)">2×4 矩阵</button>
                <button class="magic-btn" onclick="createMatrix(4,2)">4×2 矩阵</button>
                <button class="magic-btn" onclick="animateMatrixFill()">🎨 填充动画</button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let animationFrame;
        let currentMatrix = { rows: 2, cols: 2 };

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initAllCanvas();
        });

        function initAllCanvas() {
            drawIntroCanvas();
            drawStepCanvas();
            drawPracticeCanvas();
        }

        // 介绍画布 - 展示矩阵的基本概念
        function drawIntroCanvas() {
            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.textAlign = 'center';
            ctx.fillText('矩阵 = 数字的整齐队列', canvas.width/2, 50);

            // 绘制一个简单的3x3矩阵示例
            const startX = 250;
            const startY = 100;
            const cellSize = 60;
            const gap = 5;

            // 绘制矩阵边框
            ctx.strokeStyle = '#74b9ff';
            ctx.lineWidth = 4;
            ctx.strokeRect(startX - 10, startY - 10, 3 * cellSize + 2 * gap + 20, 3 * cellSize + 2 * gap + 20);

            // 绘制矩阵元素
            const matrixValues = [
                [1, 2, 3],
                [4, 5, 6],
                [7, 8, 9]
            ];

            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';

            for(let i = 0; i < 3; i++) {
                for(let j = 0; j < 3; j++) {
                    const x = startX + j * (cellSize + gap);
                    const y = startY + i * (cellSize + gap);

                    // 绘制单元格背景
                    ctx.fillStyle = '#ddd';
                    ctx.fillRect(x, y, cellSize, cellSize);

                    // 绘制单元格边框
                    ctx.strokeStyle = '#74b9ff';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(x, y, cellSize, cellSize);

                    // 绘制数字
                    ctx.fillStyle = '#2d3436';
                    ctx.fillText(matrixValues[i][j], x + cellSize/2, y + cellSize/2 + 8);
                }
            }

            // 添加说明文字
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#636e72';
            ctx.fillText('这是一个 3×3 矩阵', canvas.width/2, 320);
            ctx.fillText('3行 × 3列 = 9个数字', canvas.width/2, 350);
        }

        // 开始介绍动画
        function startIntroAnimation() {
            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = 'bold 28px Microsoft YaHei';
                ctx.fillStyle = '#2d3436';
                ctx.textAlign = 'center';
                ctx.fillText('矩阵 = 数字的整齐队列', canvas.width/2, 50);

                const startX = 250;
                const startY = 100;
                const cellSize = 60;
                const gap = 5;

                // 动画进度
                const progress = Math.min(frame / 120, 1);

                // 绘制矩阵边框（带动画）
                if(progress > 0.1) {
                    ctx.strokeStyle = '#74b9ff';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(startX - 10, startY - 10, 3 * cellSize + 2 * gap + 20, 3 * cellSize + 2 * gap + 20);
                }

                // 逐个绘制矩阵元素
                const matrixValues = [
                    [1, 2, 3],
                    [4, 5, 6],
                    [7, 8, 9]
                ];

                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.textAlign = 'center';

                for(let i = 0; i < 3; i++) {
                    for(let j = 0; j < 3; j++) {
                        const cellIndex = i * 3 + j;
                        const cellProgress = Math.max(0, Math.min(1, (progress - cellIndex * 0.08) * 5));

                        if(cellProgress > 0) {
                            const x = startX + j * (cellSize + gap);
                            const y = startY + i * (cellSize + gap);

                            // 动画缩放效果
                            const scale = cellProgress;
                            const scaledSize = cellSize * scale;
                            const offsetX = (cellSize - scaledSize) / 2;
                            const offsetY = (cellSize - scaledSize) / 2;

                            // 绘制单元格背景
                            ctx.fillStyle = `rgba(221, 221, 221, ${cellProgress})`;
                            ctx.fillRect(x + offsetX, y + offsetY, scaledSize, scaledSize);

                            // 绘制单元格边框
                            ctx.strokeStyle = '#74b9ff';
                            ctx.lineWidth = 2;
                            ctx.strokeRect(x + offsetX, y + offsetY, scaledSize, scaledSize);

                            // 绘制数字
                            if(cellProgress > 0.5) {
                                ctx.fillStyle = '#2d3436';
                                ctx.globalAlpha = (cellProgress - 0.5) * 2;
                                ctx.fillText(matrixValues[i][j], x + cellSize/2, y + cellSize/2 + 8);
                                ctx.globalAlpha = 1;
                            }
                        }
                    }
                }

                // 添加说明文字
                if(progress > 0.8) {
                    const textAlpha = (progress - 0.8) * 5;
                    ctx.globalAlpha = textAlpha;
                    ctx.font = '18px Microsoft YaHei';
                    ctx.fillStyle = '#636e72';
                    ctx.fillText('这是一个 3×3 矩阵', canvas.width/2, 320);
                    ctx.fillText('3行 × 3列 = 9个数字', canvas.width/2, 350);
                    ctx.globalAlpha = 1;
                }

                frame++;
                if(frame < 150) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 分步学习画布
        function drawStepCanvas() {
            const canvas = document.getElementById('stepCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            switch(currentStep) {
                case 1:
                    drawStep1(ctx);
                    break;
                case 2:
                    drawStep2(ctx);
                    break;
                case 3:
                    drawStep3(ctx);
                    break;
                case 4:
                    drawStep4(ctx);
                    break;
                case 5:
                    drawStep5(ctx);
                    break;
            }
        }

        // 步骤1：什么是行和列
        function drawStep1(ctx) {
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.textAlign = 'center';
            ctx.fillText('步骤1：认识行和列', 400, 40);

            // 绘制行的示例
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#e17055';
            ctx.fillText('行（横着的）', 150, 100);

            // 绘制3个水平的数字行
            const numbers = [1, 2, 3, 4, 5];
            for(let i = 0; i < 3; i++) {
                for(let j = 0; j < 5; j++) {
                    const x = 50 + j * 50;
                    const y = 120 + i * 50;

                    ctx.fillStyle = '#fdcb6e';
                    ctx.fillRect(x, y, 40, 40);
                    ctx.strokeStyle = '#e17055';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(x, y, 40, 40);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(numbers[j], x + 20, y + 25);
                }

                // 行标签
                ctx.fillStyle = '#e17055';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(`第${i+1}行`, 15, y + 25);
            }

            // 绘制列的示例
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#74b9ff';
            ctx.textAlign = 'center';
            ctx.fillText('列（竖着的）', 550, 100);

            // 绘制5个垂直的数字列
            for(let j = 0; j < 5; j++) {
                for(let i = 0; i < 3; i++) {
                    const x = 450 + j * 50;
                    const y = 120 + i * 50;

                    ctx.fillStyle = '#a8e6cf';
                    ctx.fillRect(x, y, 40, 40);
                    ctx.strokeStyle = '#74b9ff';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(x, y, 40, 40);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(i + 1, x + 20, y + 25);
                }

                // 列标签
                ctx.fillStyle = '#74b9ff';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(`第${j+1}列`, x + 20, 300);
            }

            // 底部说明
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#636e72';
            ctx.textAlign = 'center';
            ctx.fillText('矩阵就是把数字按行和列整齐排列！', 400, 350);
        }

        // 步骤2：矩阵的大小
        function drawStep2(ctx) {
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.textAlign = 'center';
            ctx.fillText('步骤2：矩阵的大小', 400, 40);

            // 绘制不同大小的矩阵示例
            const examples = [
                { rows: 2, cols: 2, x: 100, y: 100, name: '2×2矩阵' },
                { rows: 3, cols: 2, x: 300, y: 100, name: '3×2矩阵' },
                { rows: 2, cols: 4, x: 500, y: 100, name: '2×4矩阵' }
            ];

            examples.forEach(example => {
                // 绘制矩阵
                const cellSize = 35;
                const gap = 2;

                for(let i = 0; i < example.rows; i++) {
                    for(let j = 0; j < example.cols; j++) {
                        const x = example.x + j * (cellSize + gap);
                        const y = example.y + i * (cellSize + gap);

                        ctx.fillStyle = '#ddd';
                        ctx.fillRect(x, y, cellSize, cellSize);
                        ctx.strokeStyle = '#74b9ff';
                        ctx.lineWidth = 1;
                        ctx.strokeRect(x, y, cellSize, cellSize);

                        ctx.fillStyle = '#2d3436';
                        ctx.font = '12px Microsoft YaHei';
                        ctx.textAlign = 'center';
                        ctx.fillText('•', x + cellSize/2, y + cellSize/2 + 3);
                    }
                }

                // 标签
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = '#74b9ff';
                ctx.textAlign = 'center';
                const centerX = example.x + (example.cols * (cellSize + gap) - gap) / 2;
                ctx.fillText(example.name, centerX, example.y + example.rows * (cellSize + gap) + 25);

                // 说明
                ctx.font = '12px Microsoft YaHei';
                ctx.fillStyle = '#636e72';
                ctx.fillText(`${example.rows}行×${example.cols}列`, centerX, example.y + example.rows * (cellSize + gap) + 45);
            });

            // 底部说明
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#636e72';
            ctx.textAlign = 'center';
            ctx.fillText('矩阵的大小用 "行数×列数" 表示', 400, 350);
            ctx.fillText('比如 3×2 表示 3行2列', 400, 380);
        }

        // 步骤3：矩阵元素的位置
        function drawStep3(ctx) {
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.textAlign = 'center';
            ctx.fillText('步骤3：元素的位置', 400, 40);

            // 绘制一个3x3矩阵，突出显示位置
            const startX = 300;
            const startY = 100;
            const cellSize = 50;
            const gap = 3;

            const matrixValues = [
                ['a₁₁', 'a₁₂', 'a₁₃'],
                ['a₂₁', 'a₂₂', 'a₂₃'],
                ['a₃₁', 'a₃₂', 'a₃₃']
            ];

            for(let i = 0; i < 3; i++) {
                for(let j = 0; j < 3; j++) {
                    const x = startX + j * (cellSize + gap);
                    const y = startY + i * (cellSize + gap);

                    // 特殊高亮中心元素
                    if(i === 1 && j === 1) {
                        ctx.fillStyle = '#fdcb6e';
                    } else {
                        ctx.fillStyle = '#ddd';
                    }

                    ctx.fillRect(x, y, cellSize, cellSize);
                    ctx.strokeStyle = '#74b9ff';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(x, y, cellSize, cellSize);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(matrixValues[i][j], x + cellSize/2, y + cellSize/2 + 5);
                }
            }

            // 行号标签
            for(let i = 0; i < 3; i++) {
                ctx.fillStyle = '#e17055';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(i + 1, startX - 30, startY + i * (cellSize + gap) + cellSize/2 + 5);
            }

            // 列号标签
            for(let j = 0; j < 3; j++) {
                ctx.fillStyle = '#74b9ff';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(j + 1, startX + j * (cellSize + gap) + cellSize/2, startY - 20);
            }

            // 箭头和说明
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(startX + cellSize + cellSize/2, startY + cellSize + cellSize/2);
            ctx.lineTo(startX + cellSize + cellSize/2 + 80, startY + cellSize + cellSize/2 - 50);
            ctx.stroke();

            ctx.fillStyle = '#e17055';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('a₂₂ 表示第2行第2列的元素', startX + cellSize + cellSize/2 + 90, startY + cellSize + cellSize/2 - 45);

            // 底部说明
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#636e72';
            ctx.textAlign = 'center';
            ctx.fillText('每个数字都有自己的"地址"：行号和列号', 400, 350);
        }

        // 步骤4：矩阵的表示方法
        function drawStep4(ctx) {
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.textAlign = 'center';
            ctx.fillText('步骤4：矩阵的表示方法', 400, 40);

            // 绘制矩阵的不同表示方法
            const startX = 200;
            const startY = 100;

            // 方法1：方括号
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#74b9ff';
            ctx.textAlign = 'left';
            ctx.fillText('方法1：用方括号 [ ]', startX, startY);

            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.fillText('A = [', startX + 20, startY + 40);
            ctx.fillText('1  2  3', startX + 60, startY + 40);
            ctx.fillText('4  5  6', startX + 60, startY + 65);
            ctx.fillText(']', startX + 140, startY + 65);

            // 方法2：圆括号
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#e17055';
            ctx.fillText('方法2：用圆括号 ( )', startX, startY + 120);

            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.fillText('B = (', startX + 20, startY + 160);
            ctx.fillText('7  8', startX + 60, startY + 160);
            ctx.fillText('9  10', startX + 60, startY + 185);
            ctx.fillText(')', startX + 120, startY + 185);

            // 方法3：竖线
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#00b894';
            ctx.fillText('方法3：用竖线 | |', startX, startY + 240);

            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.fillText('C = |', startX + 20, startY + 280);
            ctx.fillText('11  12  13', startX + 60, startY + 280);
            ctx.fillText('14  15  16', startX + 60, startY + 305);
            ctx.fillText('|', startX + 160, startY + 305);

            // 底部说明
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#636e72';
            ctx.textAlign = 'center';
            ctx.fillText('这三种方法都可以表示矩阵，最常用的是方括号', 400, 380);
        }

        // 步骤5：矩阵的应用
        function drawStep5(ctx) {
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.textAlign = 'center';
            ctx.fillText('步骤5：矩阵在生活中的应用', 400, 40);

            // 应用示例
            const examples = [
                { title: '📊 成绩表', x: 100, y: 100, data: [['数学', '语文'], ['90', '85'], ['78', '92']] },
                { title: '🎮 游戏地图', x: 350, y: 100, data: [['🌳', '🏠'], ['🌊', '⛰️']] },
                { title: '📱 像素图片', x: 600, y: 100, data: [['⬛', '⬜'], ['⬜', '⬛']] }
            ];

            examples.forEach(example => {
                // 标题
                ctx.font = '18px Microsoft YaHei';
                ctx.fillStyle = '#74b9ff';
                ctx.textAlign = 'center';
                ctx.fillText(example.title, example.x + 50, example.y);

                // 绘制矩阵
                const cellSize = 40;
                const gap = 2;

                for(let i = 0; i < example.data.length; i++) {
                    for(let j = 0; j < example.data[i].length; j++) {
                        const x = example.x + j * (cellSize + gap);
                        const y = example.y + 20 + i * (cellSize + gap);

                        ctx.fillStyle = '#f8f9fa';
                        ctx.fillRect(x, y, cellSize, cellSize);
                        ctx.strokeStyle = '#74b9ff';
                        ctx.lineWidth = 1;
                        ctx.strokeRect(x, y, cellSize, cellSize);

                        ctx.fillStyle = '#2d3436';
                        ctx.font = '14px Microsoft YaHei';
                        ctx.textAlign = 'center';
                        ctx.fillText(example.data[i][j], x + cellSize/2, y + cellSize/2 + 5);
                    }
                }
            });

            // 底部说明
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#636e72';
            ctx.textAlign = 'center';
            ctx.fillText('矩阵无处不在！从成绩单到游戏，从图片到数据分析', 400, 350);
            ctx.fillText('掌握矩阵，就掌握了数字世界的秘密！', 400, 380);
        }

        // 步骤控制函数
        function nextStep() {
            if(currentStep < 5) {
                document.getElementById(`step${currentStep}`).classList.remove('active');
                currentStep++;
                document.getElementById(`step${currentStep}`).classList.add('active');
                drawStepCanvas();
            }
        }

        function goToStep(step) {
            document.getElementById(`step${currentStep}`).classList.remove('active');
            currentStep = step;
            document.getElementById(`step${currentStep}`).classList.add('active');
            drawStepCanvas();
        }

        function resetSteps() {
            document.getElementById(`step${currentStep}`).classList.remove('active');
            currentStep = 1;
            document.getElementById('step1').classList.add('active');
            drawStepCanvas();
        }

        // 练习画布
        function drawPracticeCanvas() {
            const canvas = document.getElementById('practiceCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.fillStyle = '#2d3436';
            ctx.textAlign = 'center';
            ctx.fillText('创建你的矩阵', canvas.width/2, 50);

            // 绘制当前矩阵
            drawMatrix(ctx, currentMatrix.rows, currentMatrix.cols);
        }

        function drawMatrix(ctx, rows, cols) {
            const startX = (900 - cols * 70) / 2;
            const startY = 150;
            const cellSize = 60;
            const gap = 10;

            // 绘制矩阵标题
            ctx.font = '20px Microsoft YaHei';
            ctx.fillStyle = '#74b9ff';
            ctx.textAlign = 'center';
            ctx.fillText(`${rows}×${cols} 矩阵`, 450, 120);

            for(let i = 0; i < rows; i++) {
                for(let j = 0; j < cols; j++) {
                    const x = startX + j * (cellSize + gap);
                    const y = startY + i * (cellSize + gap);

                    // 渐变背景
                    const gradient = ctx.createLinearGradient(x, y, x + cellSize, y + cellSize);
                    gradient.addColorStop(0, '#74b9ff');
                    gradient.addColorStop(1, '#0984e3');

                    ctx.fillStyle = gradient;
                    ctx.fillRect(x, y, cellSize, cellSize);

                    // 边框
                    ctx.strokeStyle = '#2d3436';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(x, y, cellSize, cellSize);

                    // 元素值
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    const value = i * cols + j + 1;
                    ctx.fillText(value, x + cellSize/2, y + cellSize/2 + 6);
                }
            }

            // 行列标签
            ctx.fillStyle = '#e17055';
            ctx.font = '14px Microsoft YaHei';
            for(let i = 0; i < rows; i++) {
                ctx.fillText(`行${i+1}`, startX - 40, startY + i * (cellSize + gap) + cellSize/2 + 5);
            }

            ctx.fillStyle = '#74b9ff';
            for(let j = 0; j < cols; j++) {
                ctx.fillText(`列${j+1}`, startX + j * (cellSize + gap) + cellSize/2, startY - 20);
            }
        }

        function createMatrix(rows, cols) {
            currentMatrix = { rows, cols };
            drawPracticeCanvas();
        }

        function animateMatrixFill() {
            const canvas = document.getElementById('practiceCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = 'bold 28px Microsoft YaHei';
                ctx.fillStyle = '#2d3436';
                ctx.textAlign = 'center';
                ctx.fillText('矩阵填充动画', canvas.width/2, 50);

                const rows = currentMatrix.rows;
                const cols = currentMatrix.cols;
                const startX = (900 - cols * 70) / 2;
                const startY = 150;
                const cellSize = 60;
                const gap = 10;

                const totalCells = rows * cols;
                const progress = Math.min(frame / (totalCells * 8), 1);

                for(let i = 0; i < rows; i++) {
                    for(let j = 0; j < cols; j++) {
                        const cellIndex = i * cols + j;
                        const cellProgress = Math.max(0, Math.min(1, (progress - cellIndex / totalCells) * totalCells));

                        if(cellProgress > 0) {
                            const x = startX + j * (cellSize + gap);
                            const y = startY + i * (cellSize + gap);

                            // 动画缩放
                            const scale = cellProgress;
                            const scaledSize = cellSize * scale;
                            const offsetX = (cellSize - scaledSize) / 2;
                            const offsetY = (cellSize - scaledSize) / 2;

                            // 彩虹色效果
                            const hue = (cellIndex * 30 + frame * 2) % 360;
                            ctx.fillStyle = `hsl(${hue}, 70%, 60%)`;
                            ctx.fillRect(x + offsetX, y + offsetY, scaledSize, scaledSize);

                            ctx.strokeStyle = '#2d3436';
                            ctx.lineWidth = 2;
                            ctx.strokeRect(x + offsetX, y + offsetY, scaledSize, scaledSize);

                            if(cellProgress > 0.5) {
                                ctx.fillStyle = 'white';
                                ctx.font = 'bold 16px Microsoft YaHei';
                                ctx.textAlign = 'center';
                                const value = i * cols + j + 1;
                                ctx.globalAlpha = (cellProgress - 0.5) * 2;
                                ctx.fillText(value, x + cellSize/2, y + cellSize/2 + 6);
                                ctx.globalAlpha = 1;
                            }
                        }
                    }
                }

                frame++;
                if(frame < totalCells * 8 + 30) {
                    requestAnimationFrame(animate);
                } else {
                    // 动画结束后恢复正常显示
                    setTimeout(() => drawPracticeCanvas(), 1000);
                }
            }

            animate();
        }
    </script>
</body>
</html>
