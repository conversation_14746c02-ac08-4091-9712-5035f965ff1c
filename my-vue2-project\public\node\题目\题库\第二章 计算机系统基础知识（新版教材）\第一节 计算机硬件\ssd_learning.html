<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>固态硬盘知识探索</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .comparison-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 30px;
        }

        .drive-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 250px;
            position: relative;
            overflow: hidden;
        }

        .drive-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .drive-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            position: relative;
        }

        .ssd-icon {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }

        .hdd-icon {
            background: linear-gradient(45deg, #FF6B6B, #ee5a52);
        }

        .drive-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .drive-features {
            list-style: none;
            text-align: left;
        }

        .drive-features li {
            padding: 8px 0;
            color: #666;
            position: relative;
            padding-left: 25px;
        }

        .drive-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }

        .quiz-container {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
        }

        .question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.02);
        }

        .option.selected {
            border-color: #FFD700;
            background: rgba(255,215,0,0.3);
        }

        .option.correct {
            border-color: #4CAF50;
            background: rgba(76,175,80,0.3);
        }

        .option.wrong {
            border-color: #f44336;
            background: rgba(244,67,54,0.3);
        }

        .submit-btn {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #333;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .explanation {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            display: none;
        }

        .explanation.show {
            display: block;
            animation: fadeIn 0.5s ease-out;
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        #animationCanvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            background: white;
        }

        .control-panel {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .control-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2rem;
            }
            
            .comparison-container {
                flex-direction: column;
            }
            
            .options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 固态硬盘知识探索</h1>
            <p class="subtitle">通过动画和交互学习存储设备的奥秘</p>
        </div>

        <div class="section">
            <h2 class="section-title">💾 存储设备对比</h2>
            <div class="comparison-container">
                <div class="drive-card" onclick="showDriveAnimation('ssd')">
                    <div class="drive-icon ssd-icon">💎</div>
                    <div class="drive-name">固态硬盘 (SSD)</div>
                    <ul class="drive-features">
                        <li>闪存芯片存储</li>
                        <li>读写速度快</li>
                        <li>功耗低</li>
                        <li>无噪音</li>
                        <li>抗震性强</li>
                    </ul>
                </div>
                
                <div class="drive-card" onclick="showDriveAnimation('hdd')">
                    <div class="drive-icon hdd-icon">💿</div>
                    <div class="drive-name">机械硬盘 (HDD)</div>
                    <ul class="drive-features">
                        <li>磁表面存储</li>
                        <li>读写速度慢</li>
                        <li>功耗高</li>
                        <li>有噪音</li>
                        <li>怕震动</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 动画演示</h2>
            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
                <div class="control-panel">
                    <button class="control-btn" onclick="showDriveAnimation('ssd')">SSD工作原理</button>
                    <button class="control-btn" onclick="showDriveAnimation('hdd')">HDD工作原理</button>
                    <button class="control-btn" onclick="showPowerComparison()">功耗对比</button>
                    <button class="control-btn" onclick="showSpeedComparison()">速度对比</button>
                </div>
            </div>
        </div>

        <div class="quiz-container">
            <h2 class="section-title" style="color: white;">📝 知识测试</h2>
            <div class="question">
                以下关于主流固态硬盘的叙述中，正确的是（ ）。
            </div>
            <div class="options">
                <div class="option" data-answer="A" onclick="selectOption(this)">
                    A. 存储介质是磁表面存储器，比机械硬盘功耗高
                </div>
                <div class="option" data-answer="B" onclick="selectOption(this)">
                    B. 存储介质是磁表面存储器，比机械硬盘功耗低
                </div>
                <div class="option" data-answer="C" onclick="selectOption(this)">
                    C. 存储介质是闪存芯片，比机械硬盘功耗高
                </div>
                <div class="option" data-answer="D" onclick="selectOption(this)">
                    D. 存储介质是闪存芯片，比机械硬盘功耗低
                </div>
            </div>
            <button class="submit-btn" onclick="submitAnswer()" disabled>提交答案</button>
            
            <div class="explanation" id="explanation">
                <h3>📚 详细解析</h3>
                <p><strong>正确答案：D</strong></p>
                <p>固态硬盘（SSD）的特点：</p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li><strong>存储介质</strong>：使用闪存芯片（NAND Flash），不是磁表面存储器</li>
                    <li><strong>功耗</strong>：比机械硬盘功耗更低，因为没有机械运动部件</li>
                    <li><strong>优势</strong>：读写速度快、防震抗摔、无噪音、工作温度范围大</li>
                </ul>
                <p>机械硬盘（HDD）使用磁表面存储器，需要马达驱动磁盘旋转，功耗较高。</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let selectedAnswer = null;

        // 调整canvas大小适应屏幕
        function resizeCanvas() {
            const container = canvas.parentElement;
            const maxWidth = Math.min(800, container.clientWidth - 40);
            canvas.width = maxWidth;
            canvas.height = maxWidth * 0.5;
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // 选择答案
        function selectOption(element) {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            element.classList.add('selected');
            selectedAnswer = element.dataset.answer;
            document.querySelector('.submit-btn').disabled = false;
        }

        // 提交答案
        function submitAnswer() {
            if (!selectedAnswer) return;
            
            const options = document.querySelectorAll('.option');
            options.forEach(opt => {
                if (opt.dataset.answer === 'D') {
                    opt.classList.add('correct');
                } else if (opt.classList.contains('selected') && opt.dataset.answer !== 'D') {
                    opt.classList.add('wrong');
                }
            });
            
            document.querySelector('.submit-btn').disabled = true;
            document.getElementById('explanation').classList.add('show');
            
            // 播放结果动画
            if (selectedAnswer === 'D') {
                showSuccessAnimation();
            } else {
                showTryAgainAnimation();
            }
        }

        // 成功动画
        function showSuccessAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#4CAF50';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🎉 答对了！', canvas.width/2, canvas.height/2);
        }

        // 重试动画
        function showTryAgainAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#FF6B6B';
            ctx.font = 'bold 32px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('💡 学习一下正确答案吧', canvas.width/2, canvas.height/2);
        }

        // SSD工作原理动画
        function showDriveAnimation(type) {
            if (animationId) cancelAnimationFrame(animationId);
            
            if (type === 'ssd') {
                animateSSD();
            } else {
                animateHDD();
            }
        }

        // SSD动画
        function animateSSD() {
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                ctx.fillStyle = '#f0f8ff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // SSD外壳
                ctx.fillStyle = '#2c3e50';
                ctx.fillRect(canvas.width/2 - 150, canvas.height/2 - 60, 300, 120);
                
                // 闪存芯片
                for (let i = 0; i < 6; i++) {
                    const x = canvas.width/2 - 120 + i * 40;
                    const y = canvas.height/2 - 30;
                    
                    // 芯片发光效果
                    const glow = Math.sin(frame * 0.1 + i * 0.5) * 0.5 + 0.5;
                    ctx.fillStyle = `rgba(76, 175, 80, ${0.3 + glow * 0.7})`;
                    ctx.fillRect(x - 2, y - 2, 34, 64);
                    
                    ctx.fillStyle = '#4CAF50';
                    ctx.fillRect(x, y, 30, 60);
                    
                    // 数据流动效果
                    ctx.fillStyle = '#FFD700';
                    const dataY = y + (frame * 2 + i * 10) % 60;
                    ctx.fillRect(x + 10, dataY, 10, 5);
                }
                
                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('固态硬盘 (SSD)', canvas.width/2, 50);
                ctx.font = '16px Arial';
                ctx.fillText('闪存芯片 • 无机械部件 • 低功耗', canvas.width/2, canvas.height - 30);
                
                frame++;
                animationId = requestAnimationFrame(animate);
            }
            
            animate();
        }

        // HDD动画
        function animateHDD() {
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                ctx.fillStyle = '#fff5f5';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // HDD外壳
                ctx.fillStyle = '#34495e';
                ctx.fillRect(canvas.width/2 - 150, canvas.height/2 - 80, 300, 160);
                
                // 磁盘
                const centerX = canvas.width/2 - 50;
                const centerY = canvas.height/2;
                const rotation = frame * 0.2;
                
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.rotate(rotation);
                
                // 磁盘盘片
                ctx.fillStyle = '#7f8c8d';
                ctx.beginPath();
                ctx.arc(0, 0, 60, 0, Math.PI * 2);
                ctx.fill();
                
                // 磁盘纹理
                for (let i = 1; i <= 4; i++) {
                    ctx.strokeStyle = '#95a5a6';
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.arc(0, 0, i * 15, 0, Math.PI * 2);
                    ctx.stroke();
                }
                
                ctx.restore();
                
                // 读写头
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(centerX + 70, centerY - 5, 40, 10);
                ctx.fillRect(centerX + 65, centerY - 2, 10, 4);
                
                // 读写头臂
                ctx.strokeStyle = '#c0392b';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(centerX + 75, centerY);
                ctx.lineTo(centerX + 120, centerY);
                ctx.stroke();
                
                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('机械硬盘 (HDD)', canvas.width/2, 50);
                ctx.font = '16px Arial';
                ctx.fillText('磁表面存储 • 机械运动 • 高功耗', canvas.width/2, canvas.height - 30);
                
                frame++;
                animationId = requestAnimationFrame(animate);
            }
            
            animate();
        }

        // 功耗对比动画
        function showPowerComparison() {
            if (animationId) cancelAnimationFrame(animationId);
            
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('功耗对比', canvas.width/2, 50);
                
                // SSD功耗条
                const ssdPower = 30 + Math.sin(frame * 0.1) * 5; // 2-3W
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(100, 120, ssdPower * 3, 40);
                ctx.fillStyle = '#2c3e50';
                ctx.font = '18px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('SSD: 2-3W', 100, 110);
                
                // HDD功耗条
                const hddPower = 120 + Math.sin(frame * 0.08) * 10; // 6-8W
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(100, 200, hddPower * 3, 40);
                ctx.fillText('HDD: 6-8W', 100, 190);
                
                // 节能图标
                ctx.font = '40px Arial';
                ctx.fillText('🔋', 50, 150);
                ctx.fillText('⚡', 50, 230);
                
                frame++;
                animationId = requestAnimationFrame(animate);
            }
            
            animate();
        }

        // 速度对比动画
        function showSpeedComparison() {
            if (animationId) cancelAnimationFrame(animationId);
            
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('读写速度对比', canvas.width/2, 50);
                
                // 赛道
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(100, 150);
                ctx.lineTo(canvas.width - 100, 150);
                ctx.moveTo(100, 250);
                ctx.lineTo(canvas.width - 100, 250);
                ctx.stroke();
                
                // SSD小车（快）
                const ssdX = 100 + (frame * 8) % (canvas.width - 200);
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(ssdX, 135, 30, 30);
                ctx.fillStyle = '#2c3e50';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('SSD 💨', 100, 130);
                
                // HDD小车（慢）
                const hddX = 100 + (frame * 2) % (canvas.width - 200);
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(hddX, 235, 30, 30);
                ctx.fillText('HDD 🐌', 100, 230);
                
                frame++;
                animationId = requestAnimationFrame(animate);
            }
            
            animate();
        }

        // 初始化显示SSD动画
        showDriveAnimation('ssd');
    </script>
</body>
</html>
