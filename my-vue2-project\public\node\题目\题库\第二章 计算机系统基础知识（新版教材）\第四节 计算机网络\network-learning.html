<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络IP地址获取原理 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .option:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #4caf50;
            background: #e8f5e8;
        }

        .option.wrong {
            border-color: #f44336;
            background: #ffebee;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        #networkCanvas {
            width: 100%;
            height: 400px;
            border-radius: 12px;
            background: #f8f9fa;
        }

        .controls {
            text-align: center;
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.9s forwards;
        }

        .step {
            margin-bottom: 30px;
            padding: 20px;
            border-left: 4px solid #667eea;
            background: #f8f9fa;
            border-radius: 0 12px 12px 0;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.5s ease;
        }

        .step.active {
            opacity: 1;
            transform: translateX(0);
        }

        .step-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .step-content {
            color: #666;
            line-height: 1.6;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🌐 网络IP地址获取原理</h1>
            <p class="subtitle">零基础学习 · 动画演示 · 交互体验</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>如果一台配置成自动获取IP地址的计算机，开机后得到的IP地址是<span class="highlight">************</span>（即没有DHCP服务器为其提供IP地址），则首先应该（ ）。
            </div>
            
            <div class="options">
                <div class="option" data-answer="A">A. 检查网络连接电缆</div>
                <div class="option" data-answer="B">B. 检查网卡的工作状态</div>
                <div class="option" data-answer="C">C. 检查DNS服务器的配置</div>
                <div class="option" data-answer="D">D. 查杀病毒</div>
            </div>
            
            <div id="result" style="display: none; padding: 20px; border-radius: 12px; margin-top: 20px;"></div>
        </div>

        <div class="canvas-container">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">🎯 网络连接过程动画演示</h3>
            <canvas id="networkCanvas"></canvas>
            <div class="controls">
                <button class="btn" onclick="startAnimation()">🚀 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重新开始</button>
                <button class="btn" onclick="showProblem()">⚠️ 显示问题</button>
            </div>
        </div>

        <div class="explanation">
            <h3 style="text-align: center; margin-bottom: 30px; color: #333;">📚 知识点详解</h3>
            
            <div class="step" id="step1">
                <div class="step-title">1️⃣ 什么是DHCP？</div>
                <div class="step-content">
                    <strong>DHCP（动态主机配置协议）</strong>是一种网络服务，它可以自动为计算机分配IP地址、子网掩码、网关等网络配置信息。就像酒店前台为客人分配房间号一样！
                </div>
            </div>

            <div class="step" id="step2">
                <div class="step-title">2️⃣ 什么是APIPA地址？</div>
                <div class="step-content">
                    <strong>APIPA（自动专用IP地址）</strong>是Windows系统的一个功能。当计算机无法从DHCP服务器获取IP地址时，系统会自动分配一个<span class="highlight">169.254.x.x</span>范围的IP地址，这样计算机至少可以与同一网段的其他设备通信。
                </div>
            </div>

            <div class="step" id="step3">
                <div class="step-title">3️⃣ 为什么会出现************？</div>
                <div class="step-content">
                    出现这个地址说明：<br>
                    ✅ 网卡工作正常（能够生成APIPA地址）<br>
                    ❌ 无法连接到DHCP服务器<br>
                    最可能的原因是<strong>网络连接断开</strong>，比如网线松动、交换机故障等。
                </div>
            </div>

            <div class="step" id="step4">
                <div class="step-title">4️⃣ 解题思路</div>
                <div class="step-content">
                    <strong>排除法分析：</strong><br>
                    🔸 B选项：网卡工作状态 - 如果网卡坏了，连APIPA地址都不会有<br>
                    🔸 C选项：DNS服务器 - DNS是域名解析，与获取IP地址无关<br>
                    🔸 D选项：查杀病毒 - 病毒一般不会影响DHCP获取<br>
                    ✅ A选项：检查网络连接电缆 - 最直接、最可能的原因
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('networkCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        let animationState = 'idle';
        let animationFrame = 0;
        
        // 绘制计算机
        function drawComputer(x, y, status = 'normal') {
            ctx.save();
            
            // 电脑主机
            ctx.fillStyle = status === 'problem' ? '#ffcdd2' : '#e3f2fd';
            ctx.fillRect(x - 30, y - 20, 60, 40);
            ctx.strokeStyle = status === 'problem' ? '#f44336' : '#2196f3';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 30, y - 20, 60, 40);
            
            // 显示器
            ctx.fillStyle = status === 'problem' ? '#ffcdd2' : '#f5f5f5';
            ctx.fillRect(x - 25, y - 40, 50, 30);
            ctx.strokeStyle = status === 'problem' ? '#f44336' : '#666';
            ctx.strokeRect(x - 25, y - 40, 50, 30);
            
            // IP地址显示
            ctx.fillStyle = status === 'problem' ? '#f44336' : '#333';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(status === 'problem' ? '************' : '等待IP...', x, y - 25);
            
            ctx.restore();
        }
        
        // 绘制DHCP服务器
        function drawDHCPServer(x, y) {
            ctx.save();
            
            // 服务器机箱
            ctx.fillStyle = '#e8f5e8';
            ctx.fillRect(x - 25, y - 30, 50, 60);
            ctx.strokeStyle = '#4caf50';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 25, y - 30, 50, 60);
            
            // 服务器标识
            ctx.fillStyle = '#4caf50';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('DHCP', x, y - 10);
            ctx.fillText('服务器', x, y + 5);
            
            ctx.restore();
        }
        
        // 绘制网络连接
        function drawConnection(x1, y1, x2, y2, status = 'normal') {
            ctx.save();
            
            if (status === 'broken') {
                // 断开的连接
                ctx.setLineDash([10, 10]);
                ctx.strokeStyle = '#f44336';
                ctx.lineWidth = 3;
                
                // 绘制部分连接
                const midX = (x1 + x2) / 2;
                const midY = (y1 + y2) / 2;
                
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(midX - 20, midY);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(midX + 20, midY);
                ctx.lineTo(x2, y2);
                ctx.stroke();
                
                // 断开标识
                ctx.fillStyle = '#f44336';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('❌', midX, midY + 5);
                
            } else {
                // 正常连接
                ctx.setLineDash([]);
                ctx.strokeStyle = status === 'active' ? '#4caf50' : '#666';
                ctx.lineWidth = status === 'active' ? 4 : 2;
                
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
            
            ctx.restore();
        }
        
        // 绘制数据包
        function drawPacket(x, y, type = 'request') {
            ctx.save();
            
            ctx.fillStyle = type === 'request' ? '#2196f3' : '#4caf50';
            ctx.beginPath();
            ctx.arc(x, y, 8, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(type === 'request' ? 'REQ' : 'IP', x, y + 3);
            
            ctx.restore();
        }
        
        // 主绘制函数
        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const computerX = 150;
            const computerY = 200;
            const serverX = 450;
            const serverY = 200;
            
            // 绘制组件
            drawComputer(computerX, computerY, animationState === 'problem' ? 'problem' : 'normal');
            drawDHCPServer(serverX, serverY);
            
            if (animationState === 'problem') {
                drawConnection(computerX + 30, computerY, serverX - 25, serverY, 'broken');
            } else {
                drawConnection(computerX + 30, computerY, serverX - 25, serverY, 
                    animationState === 'requesting' ? 'active' : 'normal');
            }
            
            // 动画效果
            if (animationState === 'requesting' && animationFrame < 100) {
                const progress = animationFrame / 100;
                const packetX = computerX + 30 + (serverX - 25 - computerX - 30) * progress;
                drawPacket(packetX, computerY, 'request');
            }
            
            if (animationState === 'responding' && animationFrame < 100) {
                const progress = animationFrame / 100;
                const packetX = serverX - 25 - (serverX - 25 - computerX - 30) * progress;
                drawPacket(packetX, serverY, 'response');
            }
        }
        
        // 动画控制
        function animate() {
            draw();
            
            if (animationState !== 'idle') {
                animationFrame++;
                
                if (animationState === 'requesting' && animationFrame >= 100) {
                    animationState = 'responding';
                    animationFrame = 0;
                } else if (animationState === 'responding' && animationFrame >= 100) {
                    animationState = 'idle';
                    animationFrame = 0;
                }
                
                requestAnimationFrame(animate);
            }
        }
        
        function startAnimation() {
            animationState = 'requesting';
            animationFrame = 0;
            animate();
        }
        
        function resetAnimation() {
            animationState = 'idle';
            animationFrame = 0;
            draw();
        }
        
        function showProblem() {
            animationState = 'problem';
            animationFrame = 0;
            draw();
        }
        
        // 初始绘制
        draw();
        
        // 题目交互
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const result = document.getElementById('result');
                
                // 清除之前的样式
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    result.innerHTML = '🎉 <strong>正确！</strong> 当出现APIPA地址时，首先应该检查网络连接电缆，因为这是最常见的原因。';
                    result.style.background = '#e8f5e8';
                    result.style.color = '#2e7d32';
                    result.style.border = '2px solid #4caf50';
                } else {
                    this.classList.add('wrong');
                    document.querySelector('[data-answer="A"]').classList.add('correct');
                    result.innerHTML = '❌ <strong>不正确。</strong> 正确答案是A。网卡能生成APIPA地址说明工作正常，问题在于无法连接DHCP服务器。';
                    result.style.background = '#ffebee';
                    result.style.color = '#c62828';
                    result.style.border = '2px solid #f44336';
                }
                
                result.style.display = 'block';
                
                // 显示解释步骤
                setTimeout(() => showSteps(), 1000);
            });
        });
        
        // 逐步显示解释
        function showSteps() {
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.classList.add('active');
                }, index * 800);
            });
        }
        
        // 页面加载完成后显示第一个步骤
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.getElementById('step1').classList.add('active');
            }, 2000);
        });
    </script>
</body>
</html>
