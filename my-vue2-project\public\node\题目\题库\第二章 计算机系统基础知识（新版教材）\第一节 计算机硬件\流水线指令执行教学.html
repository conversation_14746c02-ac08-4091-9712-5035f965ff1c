<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流水线指令执行 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateY(20px);
            opacity: 0;
            animation: slideUp 0.8s ease-out forwards;
        }

        @keyframes slideUp {
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .question-title {
            font-size: 1.5em;
            color: #4a5568;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: bold;
        }

        .option:hover {
            background: #edf2f7;
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .option.correct {
            background: #c6f6d5;
            border-color: #38a169;
            color: #22543d;
        }

        .pipeline-demo {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .pipeline-title {
            text-align: center;
            font-size: 1.8em;
            color: #4a5568;
            margin-bottom: 30px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        #pipelineCanvas {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: #f8f9fa;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .explanation p {
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .formula {
            background: #e6fffa;
            border: 2px solid #38b2ac;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            text-align: center;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            color: #234e52;
        }

        .highlight {
            background: #fed7d7;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .stage-info {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .stage {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            min-width: 120px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .stage h4 {
            margin-bottom: 5px;
            font-size: 1.1em;
        }

        .stage p {
            font-size: 0.9em;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            #pipelineCanvas {
                width: 100%;
                max-width: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 流水线指令执行</h1>
            <p>通过动画学习计算机指令的流水线处理过程</p>
        </div>

        <div class="question-card">
            <div class="question-title">
                📝 <strong>题目：</strong>执行指令时，将每一节指令都分解为取指、分析和执行三步，已知取指时间t取指=5△t，分析时间t分析=2△t，执行时间t执行=3△t。如果按照流水线方式执行指令，从头到尾执行完500条指令需要多少△t？
            </div>
            
            <div class="options">
                <div class="option" onclick="selectOption(this, false)">A. 2500</div>
                <div class="option" onclick="selectOption(this, true)">B. 2505</div>
                <div class="option" onclick="selectOption(this, false)">C. 2510</div>
                <div class="option" onclick="selectOption(this, false)">D. 2515</div>
            </div>
        </div>

        <div class="pipeline-demo">
            <div class="pipeline-title">🎮 流水线动画演示</div>
            
            <div class="stage-info">
                <div class="stage">
                    <h4>取指 (IF)</h4>
                    <p>5△t</p>
                </div>
                <div class="stage">
                    <h4>分析 (ID)</h4>
                    <p>2△t</p>
                </div>
                <div class="stage">
                    <h4>执行 (EX)</h4>
                    <p>3△t</p>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="pipelineCanvas" width="800" height="400"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置</button>
                <button class="btn" onclick="toggleSpeed()">⚡ 调速</button>
            </div>

            <div class="explanation">
                <h3>💡 流水线原理解析</h3>
                <p><strong>什么是流水线？</strong></p>
                <p>流水线就像工厂的装配线，每个工人专门负责一个步骤，多个产品可以同时在不同阶段进行处理。</p>
                
                <p><strong>三个阶段：</strong></p>
                <p>• <span class="highlight">取指(IF)</span>：从内存中获取指令 - 需要5△t</p>
                <p>• <span class="highlight">分析(ID)</span>：解码指令，准备操作数 - 需要2△t</p>
                <p>• <span class="highlight">执行(EX)</span>：执行指令操作 - 需要3△t</p>

                <div class="formula">
                    流水线周期 = max(5△t, 2△t, 3△t) = 5△t
                </div>

                <p><strong>计算公式：</strong></p>
                <div class="formula">
                    总时间 = 第一条指令完成时间 + (剩余指令数 × 流水线周期)<br>
                    = (5 + 2 + 3) + (500 - 1) × 5<br>
                    = 10 + 499 × 5<br>
                    = 10 + 2495<br>
                    = <strong>2505△t</strong>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('pipelineCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationId;
        let currentTime = 0;
        let animationSpeed = 1;
        let isAnimating = false;

        // 指令队列
        const instructions = [];
        for (let i = 1; i <= 8; i++) {
            instructions.push({
                id: i,
                stages: [
                    { name: 'IF', duration: 5, color: '#ff6b6b', startTime: null, endTime: null },
                    { name: 'ID', duration: 2, color: '#4ecdc4', startTime: null, endTime: null },
                    { name: 'EX', duration: 3, color: '#45b7d1', startTime: null, endTime: null }
                ]
            });
        }

        function drawPipeline() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制时间轴
            drawTimeAxis();
            
            // 绘制指令流水线
            drawInstructions();
            
            // 绘制当前时间指示器
            drawTimeIndicator();
        }

        function drawTimeAxis() {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(50, 350);
            ctx.lineTo(750, 350);
            ctx.stroke();

            // 时间刻度
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            for (let t = 0; t <= 30; t += 5) {
                const x = 50 + t * 20;
                ctx.beginPath();
                ctx.moveTo(x, 345);
                ctx.lineTo(x, 355);
                ctx.stroke();
                ctx.fillText(t + '△t', x - 10, 370);
            }
        }

        function drawInstructions() {
            instructions.forEach((instruction, index) => {
                const y = 50 + index * 35;
                
                // 绘制指令标签
                ctx.fillStyle = '#333';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(`指令${instruction.id}`, 10, y + 20);
                
                // 绘制各阶段
                instruction.stages.forEach((stage, stageIndex) => {
                    const startTime = getStageStartTime(instruction.id, stageIndex);
                    const endTime = startTime + stage.duration;
                    
                    if (currentTime >= startTime && currentTime < endTime) {
                        // 正在执行的阶段
                        const progress = (currentTime - startTime) / stage.duration;
                        drawStageBlock(startTime, y, stage.duration * progress, stage.color, stage.name, true);
                    } else if (currentTime >= endTime) {
                        // 已完成的阶段
                        drawStageBlock(startTime, y, stage.duration, stage.color, stage.name, false);
                    }
                });
            });
        }

        function getStageStartTime(instructionId, stageIndex) {
            if (stageIndex === 0) {
                // IF阶段：每条指令间隔5△t开始
                return (instructionId - 1) * 5;
            } else if (stageIndex === 1) {
                // ID阶段：在IF阶段完成后开始
                return (instructionId - 1) * 5 + 5;
            } else {
                // EX阶段：在ID阶段完成后开始
                return (instructionId - 1) * 5 + 7;
            }
        }

        function drawStageBlock(startTime, y, duration, color, name, isActive) {
            const x = 50 + startTime * 20;
            const width = duration * 20;
            
            // 绘制方块
            ctx.fillStyle = isActive ? color : color + '80';
            ctx.fillRect(x, y, width, 25);
            
            // 绘制边框
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, 25);
            
            // 绘制文字
            ctx.fillStyle = isActive ? 'white' : '#333';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(name, x + width / 2, y + 17);
            ctx.textAlign = 'left';
            
            // 活跃状态的光效
            if (isActive) {
                ctx.shadowColor = color;
                ctx.shadowBlur = 10;
                ctx.fillStyle = color + '40';
                ctx.fillRect(x, y, width, 25);
                ctx.shadowBlur = 0;
            }
        }

        function drawTimeIndicator() {
            const x = 50 + currentTime * 20;
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x, 30);
            ctx.lineTo(x, 360);
            ctx.stroke();
            
            // 时间标签
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 14px Arial';
            ctx.fillText(`${currentTime}△t`, x - 15, 25);
        }

        function startAnimation() {
            if (isAnimating) return;
            
            isAnimating = true;
            currentTime = 0;
            
            function animate() {
                drawPipeline();
                currentTime += 0.1 * animationSpeed;
                
                if (currentTime < 30) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    isAnimating = false;
                    showResult();
                }
            }
            
            animate();
        }

        function resetAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            isAnimating = false;
            currentTime = 0;
            drawPipeline();
        }

        function toggleSpeed() {
            animationSpeed = animationSpeed === 1 ? 2 : animationSpeed === 2 ? 0.5 : 1;
            const btn = event.target;
            btn.textContent = animationSpeed === 2 ? '⚡ 快速' : animationSpeed === 0.5 ? '🐌 慢速' : '⚡ 调速';
        }

        function showResult() {
            setTimeout(() => {
                alert('🎉 演示完成！\n\n从动画中可以看到：\n• 第一条指令在10△t时完成\n• 之后每5△t完成一条指令\n• 500条指令总时间 = 10 + (500-1)×5 = 2505△t');
            }, 500);
        }

        function selectOption(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct');
                opt.style.background = '#f7fafc';
            });
            
            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    alert('🎉 恭喜答对了！\n\n正确答案是 B. 2505\n\n解析：流水线周期为5△t，总时间 = 10 + 499×5 = 2505△t');
                }, 300);
            } else {
                element.style.background = '#fed7d7';
                element.style.borderColor = '#e53e3e';
                setTimeout(() => {
                    alert('❌ 答案不正确，请重新思考！\n\n提示：流水线的关键是理解周期时间和重叠执行的概念。');
                }, 300);
            }
        }

        // 初始化
        drawPipeline();
        
        // 响应式处理
        function resizeCanvas() {
            const container = canvas.parentElement;
            const maxWidth = container.clientWidth - 40;
            if (maxWidth < 800) {
                canvas.style.width = maxWidth + 'px';
                canvas.style.height = (maxWidth * 0.5) + 'px';
            }
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
    </script>
</body>
</html>
