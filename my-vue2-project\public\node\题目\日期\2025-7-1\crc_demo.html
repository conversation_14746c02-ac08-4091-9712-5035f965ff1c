<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRC 校验码生成器 (零基础教学)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            background-color: #f4f7f6;
            color: #333;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #0056b3;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .container {
            background-color: #fff;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        input[type="text"], select {
            width: calc(100% - 22px);
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        #result {
            margin-top: 20px;
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }
        #result-hex {
            font-family: 'Courier New', Courier, monospace;
            background-color: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
        }
        pre {
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', Courier, monospace;
        }
        code.comment {
            color: #75715e; /* 注释颜色 */
        }
        .animation-container {
            position: relative;
            height: 180px; /* Adjust height as needed */
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            overflow: hidden; /* Important for transitions */
            margin-top: 10px;
        }
        .step {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s ease-in-out, visibility 0.5s;
            text-align: center;
        }
        .step.active {
            opacity: 1;
            visibility: visible;
        }
        .animation-controls {
            text-align: center;
            margin-top: 15px;
        }
        .animation-controls button {
            margin: 0 10px;
        }
        .flex-center {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            flex-wrap: wrap; /* Allow wrapping on small screens */
        }
        .data-box {
            border: 2px solid #007bff;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 5px;
            background-color: #e7f3ff;
            font-weight: 500;
            transition: all 0.5s ease;
            opacity: 0; /* Initially hidden for animation */
            transform: scale(0.8); /* Initially smaller for animation */
        }
        .step.active .data-box {
            animation: fadeInScale 0.6s forwards;
        }
        .data-box.poly {
            border-color: #6c757d;
            background-color: #f8f9fa;
        }
        .data-box.crc {
            border-color: #28a745;
            background-color: #e9f7ef;
        }
        .data-box.merged-packet {
            border-color: #fd7e14;
            background-color: #fff8e1;
        }
        .data-box.final-result {
            border-color: #28a745;
            background-color: #e9f7ef;
            color: #155724;
        }
        .op {
            font-size: 24px;
            font-weight: bold;
            margin: 0 15px;
            color: #6c757d;
            opacity: 0;
            transform: scale(0.8);
        }
        .step.active .op {
            animation: fadeInScale 0.6s forwards;
        }
        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }
        /* Staggered animation delays */
        .step.active .flex-center > *:nth-child(2) { animation-delay: 0.2s; }
        .step.active .flex-center > *:nth-child(3) { animation-delay: 0.4s; }
        .step.active .flex-center > *:nth-child(4) { animation-delay: 0.6s; }
        .step.active .flex-center > *:nth-child(5) { animation-delay: 0.8s; }

        /* Styles for Division Animation */
        .binary-long-division {
            font-family: 'Courier New', Courier, monospace;
            font-size: 1.2em;
            text-align: left;
            width: 300px;
            margin: 0 auto;
            position: relative;
        }
        .binary-long-division .dividend {
            margin-bottom: 5px;
        }
        .binary-long-division .divisor {
            position: absolute;
            top: 45px;
            left: 50px;
            color: #dc3545;
            transition: all 0.8s ease-in-out;
            opacity: 0;
        }
        .binary-long-division .xor-line {
            border-bottom: 1px solid #000;
            position: absolute;
            top: 70px;
            left: 50px;
            width: 55px;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        .binary-long-division .remainder {
            position: absolute;
            top: 80px;
            left: 50px;
            color: #007bff;
            opacity: 0;
            transition: opacity 0.5s ease-in-out 0.5s; /* Delay to show after XOR line */
        }
        
        .step.active .binary-long-division .divisor {
            animation: slideAndXor 6s forwards;
        }
        .step.active .binary-long-division .xor-line {
            animation: showXorLine 6s forwards;
        }
        .step.active .binary-long-division .remainder {
            animation: showRemainder 6s forwards;
        }

        @keyframes slideAndXor {
            0% { left: 50px; opacity: 0; }
            10% { left: 50px; opacity: 1; }
            30% { left: 50px; opacity: 1; } /* Wait after first XOR */
            40% { left: 62px; opacity: 1; } /* Slide to next position */
            60% { left: 62px; opacity: 1; } /* Wait */
            70% { left: 86px; opacity: 1; } /* Slide again */
            90% { left: 86px; opacity: 1; }
            100% { left: 86px; opacity: 0; }
        }
        @keyframes showXorLine {
            10%, 30%, 40%, 60%, 70%, 90% { opacity: 1; }
            95% { opacity: 0; }
            0%, 39%, 69% { opacity: 0; }
        }
        @keyframes showRemainder {
            15%, 30%, 45%, 60%, 75%, 90% { opacity: 1; }
            95% { opacity: 0; }
            0%, 14%, 44%, 74% { opacity: 0; }
        }

        /* Styles for Slow XOR Demo */
        #slow-xor-demo .calculation-area {
            position: relative;
            height: 150px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 1.4em;
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        #slow-xor-demo .calc-line {
            position: absolute;
            transition: all 0.5s ease-in-out;
        }
        #slow-xor-demo .dividend { top: 20px; left: 25px; }
        #slow-xor-demo .divisor { top: 50px; left: 25px; color: #dc3545; }
        #slow-xor-demo .line { 
            top: 75px; 
            left: 25px; 
            border-bottom: 2px solid black;
            width: 48px;
        }
        #slow-xor-demo .result { top: 85px; left: 25px; color: #007bff; }
        #slow-xor-demo .comment {
            top: 130px;
            left: 25px;
            font-size: 0.8em;
            color: #28a745;
            font-family: sans-serif;
            transition: all 0.3s ease-in-out;
        }
        #slow-xor-demo .mono-data {
             background-color: #e7f3ff;
             padding: 2px 6px;
             border-radius: 4px;
             border: 1px solid #6c757d;
        }
         #slow-xor-demo .mono-poly {
             background-color: #f8f9fa;
             padding: 2px 6px;
             border-radius: 4px;
             border: 1px solid #6c757d;
        }
        #alignment-box {
            position: absolute;
            height: 28px;
            border: 2px solid #fd7e14;
            border-radius: 4px;
            background-color: rgba(253, 126, 20, 0.1);
            transition: all 0.8s ease-in-out;
        }
        @keyframes highlight-align {
            0%, 100% {
                top: 50px;
                left: 20px;
                width: 48px;
            }
            50% {
                top: 20px;
                left: 20px;
                width: 48px;
            }
        }

        /* Styles for XOR Explanation Animation */
        #xor-explainer-container .xor-anim-area {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 150px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-top: 20px;
            position: relative;
        }
        #xor-explainer-container .bit-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2em;
            font-weight: bold;
            font-family: 'Courier New', Courier, monospace;
            border: 3px solid;
            position: absolute;
            transition: all 0.5s ease-in-out;
        }
        #xor-explainer-container .bit-a {
            left: 30%;
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        #xor-explainer-container .bit-b {
            right: 30%;
            border-color: #dc3545;
            background-color: #fdeaea;
        }
        #xor-explainer-container .result-bit {
            font-size: 3em;
            font-weight: bold;
            color: #28a745;
            opacity: 0;
            transform: scale(0.5);
            transition: all 0.3s ease-in-out;
            position: absolute;
        }
        #xor-explainer-container .result-text {
            position: absolute;
            bottom: 15px;
            font-size: 1.1em;
            font-weight: bold;
            color: #17a2b8;
            opacity: 0;
            transition: all 0.3s ease-in-out 0.5s;
        }
        /* Animation classes */
        .colliding .bit-a { transform: translateX(50px); }
        .colliding .bit-b { transform: translateX(-50px); }
        .colliding .result-bit { opacity: 1; transform: scale(1); transition-delay: 0.5s; }
        .colliding .result-text { opacity: 1; }

        @keyframes spark {
            0% { box-shadow: 0 0 0 0 rgba(253, 126, 20, 0.7); }
            70% { box-shadow: 0 0 20px 15px rgba(253, 126, 20, 0); }
            100% { box-shadow: 0 0 0 0 rgba(253, 126, 20, 0); }
        }
        .sparkle-effect {
            animation: spark 0.8s ease-out;
        }

        /* Styles for Why-XOR Animation */
        #why-xor-container .actor {
            text-align: center;
            font-weight: bold;
        }
        #why-xor-container .actor img {
            width: 80px;
            margin-bottom: 10px;
        }
        #why-xor-container .data-flow {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin-top: 20px;
            position: relative;
        }
         #why-xor-container .op-symbol {
            font-size: 2.5em;
            font-weight: bold;
            color: #dc3545;
        }
        #why-xor-container .data-box {
            border: 2px solid #007bff;
            padding: 10px 15px;
            border-radius: 5px;
            background-color: #e7f3ff;
            font-weight: 500;
            font-family: 'Courier New', Courier, monospace;
            min-width: 80px;
            text-align: center;
        }
        #why-xor-container .key {
            border-color: #6c757d;
            background-color: #f8f9fa;
        }
        #why-xor-container .encrypted {
            border-color: #fd7e14;
            background-color: #fff8e1;
        }
        #why-xor-container .final-result {
             border-color: #28a745;
             background-color: #e9f7ef;
             color: #155724;
        }
        #why-xor-container .arrow {
            position: absolute;
            font-size: 3em;
            color: #6c757d;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        #why-xor-container .step.active .arrow {
            opacity: 1;
        }

        /* Styles for XOR Game */
        #xor-game-container .problem-area {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            font-family: 'Courier New', Courier, monospace;
            font-size: 1.5em;
        }
        #xor-game-container .xor-number {
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            letter-spacing: 2px;
        }
        #xor-game-container .xor-op, #xor-game-container .xor-equals {
            margin: 0 15px;
            font-weight: bold;
        }
        #xor-game-container .xor-answer {
            font-family: 'Courier New', Courier, monospace;
            font-size: 1em; /* a bit smaller to fit */
            width: 150px;
            text-align: center;
            letter-spacing: 2px;
            border: 2px solid #007bff;
        }
        #xor-game-container .game-feedback {
            text-align: center;
            margin-top: 10px;
        }
        #xor-game-container #xor-feedback {
            margin-left: 15px;
            font-weight: bold;
        }
        #xor-game-container .game-status {
            text-align: right;
            margin-top: 15px;
            font-size: 1.1em;
        }
        #xor-game-container .timer-bar {
            width: 100%;
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            margin-top: 20px;
            overflow: hidden;
        }
        #xor-game-container .timer-bar-inner {
            height: 100%;
            width: 100%;
            background-color: #28a745;
            border-radius: 4px;
            transition: width 0.1s linear;
        }
        @keyframes shake {
            10%, 90% { transform: translate3d(-1px, 0, 0); }
            20%, 80% { transform: translate3d(2px, 0, 0); }
            30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
            40%, 60% { transform: translate3d(4px, 0, 0); }
        }
        .shake-animation {
            animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
        }

        /* Styles for XOR Name Animation */
        #xor-name-container .side-by-side {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }
        #xor-name-container .panel {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        #xor-name-container .panel h4 {
            text-align: center;
            margin-top: 0;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        #xor-name-container .gate-area {
            height: 120px;
            background: #f8f9fa;
            border-radius: 8px;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        #xor-name-container .gate-area .input {
            position: absolute;
            font-size: 2em;
            font-weight: bold;
            font-family: 'Courier New', Courier, monospace;
            transition: all 0.5s ease-in-out;
        }
        #xor-name-container .gate-area .input-a { top: 20%; left: 20%; }
        #xor-name-container .gate-area .input-b { bottom: 20%; left: 20%; }
        #xor-name-container .gate-area .gate-output {
            font-size: 3em;
            font-weight: bold;
            position: absolute;
            right: 25%;
            color: #6c757d;
            transition: all 0.3s ease-in-out;
        }
        #xor-name-container .panel.or-panel .gate-output.active { color: #28a745; transform: scale(1.2); }
        #xor-name-container .panel.xor-panel .gate-output.active { color: #28a745; transform: scale(1.2); }
        
        #xor-name-container .input.colliding-xor {
            animation: collide 0.8s forwards;
        }
        @keyframes collide {
            50% { transform: translate(60px, 0); color: #dc3545; }
            100% { transform: translate(0, 0); }
        }
        #xor-name-container .result-text {
            text-align: center;
            font-weight: bold;
            margin-top: 10px;
            height: 40px;
        }

        /* Styles for Word Explainer Animation */
        #word-explainer-container .choice-area {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        #word-explainer-container .choice-item {
            border: 3px dashed #ccc;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease-in-out;
        }
        #word-explainer-container .choice-item:hover {
            border-color: #007bff;
            transform: translateY(-5px);
        }
        #word-explainer-container .choice-item.selected {
            border-style: solid;
            border-color: #28a745;
            background-color: #e9f7ef;
        }
        #word-explainer-container .choice-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: #f8f9fa;
            border-color: #ccc;
            transform: none;
        }
        #word-explainer-container .choice-item img {
            width: 80px;
            height: 80px;
        }
        #word-explainer-container .explanation-text {
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
            margin-top: 20px;
            color: #0056b3;
            height: 30px;
        }

        /* Styles for Chinese XOR Explainer */
         #chinese-xor-explainer .anim-area {
            display: flex;
            justify-content: space-around;
            align-items: center;
            height: 150px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-top: 20px;
            position: relative;
        }
        #chinese-xor-explainer .bit-display {
             width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2.5em;
            font-weight: bold;
            font-family: 'Courier New', Courier, monospace;
            border: 3px solid;
        }
        #chinese-xor-explainer .bit-a {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        #chinese-xor-explainer .bit-b {
            border-color: #dc3545;
            background-color: #fdeaea;
        }
        #chinese-xor-explainer .logic-char {
            font-size: 4em;
            font-weight: bold;
            font-family: 'KaiTi', 'STKaiti', '华文楷体', serif;
            color: #ffc107;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
            opacity: 0;
            transform: scale(0.5);
            transition: all 0.4s ease-in-out;
        }
        #chinese-xor-explainer .logic-char.active {
            opacity: 1;
            transform: scale(1.2);
        }
        #chinese-xor-explainer .result-display {
            border-color: #28a745;
            background-color: #e9f7ef;
        }

        /* Styles for Alignment Principle Animation */
        #alignment-principle-container .side-by-side {
            display: flex;
            justify-content: space-around;
            gap: 20px;
        }
        #alignment-principle-container .panel {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        #alignment-principle-container .division-area {
            font-family: 'Courier New', Courier, monospace;
            font-size: 1.5em;
            position: relative;
            height: 60px;
            border-bottom: 2px solid #333;
            margin: 10px auto;
            width: 150px;
        }
        #alignment-principle-container .divisor-label {
            position: absolute;
            left: -40px;
            top: 15px;
        }
        #alignment-principle-container .dividend-label {
            position: absolute;
            left: 20px;
            top: 15px;
        }
        #alignment-principle-container .division-line {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            border-left: 2px solid #333;
        }
        #alignment-principle-container .highlight-box {
            position: absolute;
            top: 10px;
            height: 30px;
            border: 2px solid #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
            border-radius: 4px;
        }
        #alignment-principle-container .decimal-area .highlight-box {
             left: 20px; width: 36px; /* for "95" */
             animation: decimal-align-anim 2s ease-in-out forwards;
        }
         #alignment-principle-container .binary-area .highlight-box {
            left: 20px; width: 54px; /* for "110" */
            animation: binary-align-anim 2s ease-in-out forwards;
        }
        #alignment-principle-container .explanation {
            font-size: 0.9em;
            color: #333;
            min-height: 50px;
        }

        @keyframes decimal-align-anim {
            0% { left: 20px; width: 18px; } /* "9" */
            40% { left: 20px; width: 18px; } /* pause on "9" */
            100% { left: 20px; width: 36px; } /* expand to "95" */
        }
         @keyframes binary-align-anim {
            0% { left: 20px; width: 18px; } /* "1" */
            25% { left: 20px; width: 36px; } /* expand to "11" */
            50% { left: 20px; width: 36px; } /* pause on "11" */
            100% { left: 20px; width: 54px; } /* expand to "110" */
        }

        /* Styles for Left-vs-Right Payment Animation */
        .payment-scenario {
            display: flex;
            justify-content: space-around;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .bill, .wallet {
            font-family: 'Courier New', Courier, monospace;
        }
        .bill-title, .wallet-title { font-weight: bold; margin-bottom: 5px; }
        .bill-amount { font-size: 2em; color: #dc3545; }
        .voucher { font-size: 2em; color: #007bff; }
        #payment-animation-area {
            position: relative;
            height: 80px;
            text-align: center;
        }
        .payment-explanation {
            font-size: 1.2em;
            font-weight: bold;
            padding-top: 20px;
        }
         .payment-explanation .correct {
            color: #28a745;
        }
        .payment-explanation .wrong {
            color: #dc3545;
            animation: shake 0.5s;
        }

        /* Styles for Division Symbol Animation */
        .symbol-animation-area {
            position: relative;
            height: 200px;
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #dee2e6;
        }
        .number-item {
            position: absolute;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 1.2em;
            transition: all 1s ease-in-out;
        }
        .dividend-item { top: 20px; left: 20px; background-color: #e9f7ef; border: 1px solid #28a745;}
        .divisor-item { top: 80px; left: 40px; background-color: #e7f3ff; border: 1px solid #007bff;}
        .quotient-item { top: 140px; left: 60px; background-color: #fff8e1; border: 1px solid #fd7e14;}
        
        .division-workbench {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 100px;
            border-left: 3px solid #6c757d;
            border-top: 3px solid #6c757d;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }
        .symbol-animation-area.active .division-workbench { opacity: 1; }

        /* Final positions */
        .symbol-animation-area.active #symbol-dividend { top: 80px; left: calc(50% - 20px); }
        .symbol-animation-area.active #symbol-divisor { top: 80px; left: calc(50% - 130px); }
        .symbol-animation-area.active #symbol-quotient { top: 20px; left: calc(50% - 20px); }

        .symbol-explanation {
            position: absolute;
            bottom: 10px;
            left: 0;
            right: 0;
            text-align: center;
            font-weight: bold;
            color: #0056b3;
            opacity: 0;
            transition: opacity 0.5s ease-in-out 1s;
        }
         .symbol-animation-area.active .symbol-explanation { opacity: 1; }

        /* Styles for the Final Easter Egg Animation */
        #final-easter-egg-container .monster-area {
            position: relative;
            height: 250px;
            background: #f0f2f5;
            border-radius: 8px;
            border: 1px solid #d9dce1;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        #final-easter-egg-container .math-monster {
            font-size: 5em;
            font-family: 'Times New Roman', Times, serif;
            color: #5a677d;
            transition: all 1s ease-in-out;
        }
        #final-easter-egg-container .math-monster.tamed {
            font-size: 3em;
            color: #28a745;
            transform: rotate(360deg) scale(0.8);
        }
        #final-easter-egg-container .character {
            font-size: 4em;
            position: absolute;
            left: 20%;
            bottom: 20px;
            transition: all 1s ease-in-out;
        }
        #final-easter-egg-container .toolbox {
            position: absolute;
            right: 15%;
            bottom: 20px;
            font-size: 2em;
            cursor: pointer;
            transition: transform 0.3s;
        }
         #final-easter-egg-container .toolbox:hover {
            transform: scale(1.2);
        }
        #final-easter-egg-container .explanation-text {
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
            margin-top: 15px;
            color: #0056b3;
            height: 50px;
        }

        /* Styles for the Ultimate Skill Animation */
        #ultimate-skill-container .yarn-ball-area {
            position: relative;
            height: 300px;
            background: #e9ecef;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', Courier, monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            overflow: hidden;
        }
        #ultimate-skill-container .yarn-monster {
            font-size: 1.1em;
            line-height: 1.5;
            color: #6c757d;
            transition: all 0.8s ease-in-out;
            opacity: 1;
        }
        #ultimate-skill-container .yarn-monster.fade {
            opacity: 0.2;
        }
        #ultimate-skill-container .yarn-monster .thread {
            background-color: yellow;
            font-weight: bold;
            color: black;
            padding: 2px 4px;
            border-radius: 3px;
        }
        #ultimate-skill-container .unraveled-list {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            list-style-type: none;
            padding: 0;
            font-size: 1.3em;
            opacity: 0;
            transition: opacity 1s ease-in-out 0.5s;
        }
        #ultimate-skill-container .unraveled-list li {
            background: #e9f7ef;
            border: 1px solid #28a745;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            opacity: 0;
            transform: translateX(-30px);
            animation: fadeInListItem 0.8s forwards;
        }
        @keyframes fadeInListItem {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        #ultimate-skill-container .character-final {
            position: absolute;
            font-size: 4em;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
        }

        /* Styles for Habit Formation Animation */
        #habit-formation-container .stage-area {
            position: relative;
            height: 300px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        #habit-formation-container .stage {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.6s, visibility 0.6s;
        }
        #habit-formation-container .stage.active {
            opacity: 1;
            visibility: visible;
        }
        #habit-formation-container .stage-icon {
            font-size: 6em;
            margin-bottom: 20px;
        }
        #habit-formation-container .stage-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #0056b3;
        }
        #habit-formation-container .stage-desc {
            font-style: italic;
            color: #6c757d;
        }

        /* Styles for Subtraction Comparison Animation */
        #subtraction-comparison-container .side-by-side {
            display: flex;
            justify-content: space-around;
            gap: 20px;
            margin-top: 15px;
        }
        #subtraction-comparison-container .panel {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        #subtraction-comparison-container .panel h5 {
            text-align: center;
            margin-top: 0;
            margin-bottom: 20px;
        }
        #subtraction-comparison-container .subtraction-area {
            font-family: 'Courier New', Courier, monospace;
            font-size: 1.5em;
            position: relative;
            padding: 10px;
        }
        #subtraction-comparison-container .bits-row {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        #subtraction-comparison-container .bits-row span {
            display: inline-block;
            width: 20px;
            text-align: center;
        }
        #subtraction-comparison-container .bottom-row {
            margin-top: 5px;
        }
        #subtraction-comparison-container .op-label {
            color: #dc3545;
            font-size: 0.8em;
            font-weight: bold;
        }
        #subtraction-comparison-container .sub-line {
            margin: 5px 0;
        }
        #subtraction-comparison-container .result-row span {
             transition: opacity 0.5s;
             opacity: 0;
        }

        /* Normal Subtraction Animation */
        #normal-subtraction-area .borrow-animation {
            position: absolute;
            opacity: 0;
            color: #dc3545;
            font-weight: bold;
            text-align: center;
        }
        #normal-subtraction-area .borrow-arrow {
            font-size: 2em;
            line-height: 0.5;
        }
        @keyframes borrow-step-1 { /* 0-1 fails, borrow from next 0 */
            0% { opacity: 0; top: -10px; left: 68px; }
            100% { opacity: 1; top: -20px; left: 68px; }
        }
        @keyframes borrow-step-2 { /* that 0 fails, borrow from 1 */
             0% { opacity: 0; top: -10px; left: 48px; }
            100% { opacity: 1; top: -20px; left: 48px; }
        }

        /* XOR Subtraction Animation */
        #xor-subtraction-area .xor-highlight {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 30px; /* span width + gap */
            background: rgba(255, 193, 7, 0.3);
            border-radius: 4px;
            transition: left 0.5s ease-in-out;
            opacity: 0;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>CRC 校验码生成器</h1>
        <p>输入任意文本，选择一个 CRC 算法，然后点击按钮生成校验码。</p>
        
        <label for="inputText">输入数据:</label>
        <input type="text" id="inputText" value="Hello, World!">

        <label for="crcAlgorithm">选择CRC算法:</label>
        <select id="crcAlgorithm">
            <option value="crc32">CRC-32</option>
            <option value="crc16">CRC-16</option>
            <option value="crc8">CRC-8</option>
            <option value="crc1">CRC-1</option>
        </select>

        <button id="calculateBtn">计算CRC</button>

        <p id="result">计算结果 (16进制): <span id="result-hex"></span></p>
    </div>

    <div class="container">
        <h2>CRC 是如何计算的？(通俗版)</h2>
        <p>CRC 的计算过程在数学上是基于"多项式除法"的，但我们可以把它通俗地理解为一个"求余数"的过程。请按"下一步"查看动画分解：</p>
        
        <div class="animation-container">
            <div class="step active" id="step-1">
                <h4>第一步：约定一个"除数"</h4>
                <p>通信双方约定一个固定的"除数"，也就是 <strong>生成多项式</strong>。</p>
                <div class="flex-center">
                    <div class="data-box poly">生成多项式 (除数)</div>
                </div>
            </div>
            <div class="step" id="step-2">
                <h4>第二步：计算CRC的过程</h4>
                <p>这个过程是"模2除法"，即用XOR(异或)代替减法来计算余数。</p>
                <div class="binary-long-division">
                    <div class="dividend">数据: &nbsp; 110101101</div>
                    <div class="divisor">除数: 1011</div>
                    <div class="xor-line"></div>
                    <div class="remainder">结果: &nbsp; &nbsp; &nbsp; 0110</div>
                    <p style="font-size: 0.8em; margin-top: 100px; opacity: 0; animation: fadeInScale 1s forwards 5s;">(动画演示了前几步，实际会一直计算到最后)</p>
                </div>
            </div>
            <div class="step" id="step-3">
                <h4>第三步：得到余数 (CRC)</h4>
                <p>经过一系列计算后，最终得到的余数就是CRC校验码。</p>
                <div class="flex-center process">
                    <div class="data-box data">原始数据</div>
                    <div class="op">÷</div>
                    <div class="data-box poly">生成多项式</div>
                    <div class="op">=</div>
                    <div class="data-box crc"><strong>余数 (CRC)</strong></div>
                </div>
            </div>
            <div class="step" id="step-4">
                <h4>第四步：发送数据</h4>
                <p>将原始数据和CRC校验码打包一起发送。</p>
                <div class="flex-center packet-creation">
                     <div class="data-box data">原始数据</div>
                     <div class="op">+</div>
                     <div class="data-box crc">余数 (CRC)</div>
                     <div class="op">→</div>
                     <div class="data-box merged-packet">原始数据 + CRC</div>
                </div>
            </div>
            <div class="step" id="step-5">
                <h4>第五步：接收方校验</h4>
                <p>接收方用收到的完整数据，再除以"生成多项式"，余数应为0。</p>
                 <div class="flex-center process">
                    <div class="data-box merged-packet">原始数据 + CRC</div>
                    <div class="op">÷</div>
                    <div class="data-box poly">生成多项式</div>
                    <div class="op">=</div>
                    <div class="data-box final-result"><strong>余数为 0 ✔</strong></div>
                </div>
            </div>
        </div>
        <div class="animation-controls">
            <button id="prev-step" disabled>上一步</button>
            <button id="next-step">下一步</button>
        </div>
    </div>

    <div class="container" id="interactive-calc-container">
        <h2>深入理解模2除法 (XOR 除法)</h2>
        <p>没关系，这个概念确实是CRC中最难理解的部分。我们把它彻底分解，像在纸上演算一样，一步步来。</p>
        <p>核心规则：<strong>模2除法 = 没有进位/借位的二进制长除法，其中"减法"用"XOR"操作代替。</strong></p>
        <p><strong>XOR (异或) 规则:</strong> 两位相比，<strong>相同为0，不同为1</strong>。 (<code>1 XOR 1 = 0</code>, <code>0 XOR 0 = 0</code>, <code>1 XOR 0 = 1</code>)</p>
        
        <hr style="margin: 20px 0;">

        <h4>演算示例：</h4>
        <p>假设我们的 <strong>数据是 <code class="data-box data" style="opacity:1; transform: none; padding: 2px 6px;">110101</code></strong>，<strong>除数(生成多项式)是 <code class="data-box poly" style="opacity:1; transform: none; padding: 2px 6px;">1011</code></strong>。</p>
        
        <pre style="font-size: 1.2em; line-height: 1.6;">
  110101    <span class="comment">// 原始数据</span>
<span style="color:#dc3545;">- 1011</span>      <span class="comment">// 第一次XOR：用前4位和除数进行XOR</span>
  ----
  0110      <span class="comment">// 结果是 0110 (1101 XOR 1011)</span>
   1100     <span class="comment">// 把结果前面的0去掉，并从原始数据拿下一位'0'，变成1100</span>
<span style="color:#dc3545;">-  1011</span>     <span class="comment">// 第二次XOR</span>
   ----
   0111     <span class="comment">// 结果是 0111 (1100 XOR 1011)</span>
    1111    <span class="comment">// 把结果前面的0去掉，并从原始数据拿下一位'1'，变成1111</span>
<span style="color:#dc3545;">-   1011</span>    <span class="comment">// 第三次XOR</span>
    ----
    0100    <span class="comment">// 结果是 0100 (1111 XOR 1011)</span>
     
     <strong style="color: #28a745">100</strong>     <span class="comment">// 最后的结果(去掉前面的0)不够4位了，无法再进行除法。</span>
              <span class="comment">// 所以，最终的"余数" (CRC校验码) 就是 100。</span>
        </pre>
        <p><strong>总结：</strong>整个过程就是不断地 "<strong>对齐 -> 做XOR -> 拿下一位 -> 再对齐...</strong>"，直到数据的所有位都被用过，且剩下的结果比除数短，这个剩下的结果就是CRC。上面的动画，其实就是这个过程的快放版本。</p>

        <hr style="margin: 30px 0;">

        <div id="subtraction-comparison-container">
            <h4>动画对比：模2减法 vs 普通减法</h4>
            <p>为了真正理解"模2除法"，关键是理解它的"减法"部分。下面，我们用动画对比一下 <code>1100 - 1011</code> 在两种规则下的计算过程。</p>
            <div class="side-by-side">
                <div class="panel">
                    <h5>普通二进制减法 (带借位)</h5>
                    <div class="subtraction-area" id="normal-subtraction-area">
                        <div class="bits-row top-row">
                            <span>1</span><span>1</span><span>0</span><span>0</span>
                        </div>
                        <div class="bits-row bottom-row">
                            <span>-</span><span>1</span><span>0</span><span>1</span><span>1</span>
                        </div>
                        <hr class="sub-line">
                        <div class="bits-row result-row" id="normal-result">
                            <span>?</span><span>?</span><span>?</span><span>?</span>
                        </div>
                        <div class="borrow-animation" id="borrow-anim">
                            <div class="borrow-arrow">⤴</div>
                            <div class="borrow-text">借位!</div>
                        </div>
                    </div>
                </div>
                <div class="panel">
                    <h5>模2减法 (XOR)</h5>
                    <div class="subtraction-area" id="xor-subtraction-area">
                        <div class="bits-row top-row">
                            <span>1</span><span>1</span><span>0</span><span>0</span>
                        </div>
                        <div class="bits-row bottom-row">
                           <span class="op-label">XOR</span><span>1</span><span>0</span><span>1</span><span>1</span>
                        </div>
                        <hr class="sub-line">
                        <div class="bits-row result-row" id="xor-result">
                           <span>?</span><span>?</span><span>?</span><span>?</span>
                        </div>
                        <div class="xor-highlight" id="xor-highlight"></div>
                    </div>
                </div>
            </div>
             <div class="animation-controls" style="text-align: center;">
                <button id="play-subtraction-anim-btn">播放对比动画</button>
            </div>
            <p style="margin-top: 15px;"><strong>结论：</strong>看到了吗？普通减法因为"借位"而变得复杂，而 **XOR 运算的每一列都是独立计算的，这使得它在硬件电路中实现起来极其简单和快速**。这就是CRC选择它的根本原因！</p>
        </div>
    </div>

    <div class="container" id="division-symbol-container">
        <h2>这个奇怪的"厂"字符号是做什么的？</h2>
        <p>你问到了一个非常好的问题！这个长得像"厂"字的符号叫做**长除法符号 (Long Division Symbol)**。你觉得它"难受"是有道理的，因为它被发明出来的目的不是为了好看，而是为了**在纸上计算时，充当一个摆放数字的"收纳盒"或"工作台"**。</p>
        <p>它本身没有任何计算功能，只是一个历史悠久的格式工具，用来清晰地分隔三个关键部分。请点击按钮观看动画。</p>

        <div class="symbol-animation-area">
            <div class="number-item dividend-item" id="symbol-dividend">被除数: 1101</div>
            <div class="number-item divisor-item" id="symbol-divisor">除数: 101</div>
            <div class="number-item quotient-item" id="symbol-quotient">商: ?</div>
            
            <div class="division-workbench" id="workbench">
                <div class="workbench-dividend-slot"></div>
                <div class="workbench-divisor-slot"></div>
                <div class="workbench-quotient-slot"></div>
            </div>
            <div class="symbol-explanation" id="symbol-explanation-text">点击下方按钮开始...</div>
        </div>

        <div class="animation-controls" style="text-align: center;">
            <button id="play-symbol-anim-btn">播放"收纳盒"动画</button>
        </div>
    </div>

    <div class="container" id="left-vs-right-container">
        <h2>从左还是从右？为什么必须从"最高位"开始？</h2>
        <p>你的直觉完全正确：<strong>必须先处理"大数"</strong>。因为数字中越左边的位，代表的"权重"或"面值"越大。除法就是为了最高效地"啃掉"一个大数，所以必须从最大面额的部分开始。</p>
        <p>这就像付账单一样。下面的动画将模拟这个过程。</p>
        
        <h4>动画：支付 ¥1101 元账单</h4>
        <div class="payment-scenario">
            <div class="bill">
                <div class="bill-title">账单</div>
                <div class="bill-amount">¥ 1101</div>
            </div>
            <div class="wallet">
                <div class="wallet-title">你的代金券</div>
                <div class="voucher">¥ 101</div>
            </div>
        </div>

        <div id="payment-animation-area">
            <div class="payment-explanation" id="payment-explanation-text"></div>
        </div>

        <div class="animation-controls" style="text-align: center;">
            <button id="run-payment-wrong">演示错误方法 (从右边)</button>
            <button id="run-payment-correct" style="background-color: #28a745;">演示正确方法 (从左边)</button>
        </div>
    </div>

    <div class="container" id="alignment-principle-container">
        <h2>对齐的原理：与小学除法的比较</h2>
        <p>为什么计算时必须先"对齐"？原理和我们做十进制除法完全一样：<strong>为了让"减法"有意义，必须操作位数相当的数字。</strong></p>
        <p>下面的动画会并排比较这两种除法，你会发现它们的"对齐"逻辑是相通的。请点击"重播动画"来观察。</p>
        
        <div class="side-by-side">
            <div class="panel">
                <h4>1. 十进制除法 (你熟悉的)</h4>
                <div class="division-area decimal-area">
                    <span class="divisor-label">28</span><span class="division-line"></span><span class="dividend-label">952</span>
                    <div class="highlight-box" id="decimal-align-box"></div>
                </div>
                <p class="explanation">计算 <code>952 ÷ 28</code> 时，我们会自然地将 <code>28</code> 与 <code>95</code> 对齐，而不是与 <code>9</code> 对齐。</p>
            </div>
            <div class="panel">
                <h4>2. 二进制XOR除法 (我们学的)</h4>
                <div class="division-area binary-area">
                    <span class="divisor-label">101</span><span class="division-line"></span><span class="dividend-label">1101</span>
                    <div class="highlight-box" id="binary-align-box"></div>
                </div>
                <p class="explanation">同样，计算 <code>1101 ÷ 101</code> 时，我们将 <code>101</code> 与 <code>110</code> 对齐，因为它们的位数相同。</p>
            </div>
        </div>
        <div class="animation-controls" style="text-align: center;">
            <button id="replay-alignment-anim">重播对齐动画</button>
        </div>
    </div>

    <div class="container" id="slow-xor-demo">
        <h2>分解教学：一步一步理解XOR除法</h2>
        <p>我们用一个最简单的例子，把每一步都拆开。请点击按钮，控制每一步的演算。</p>
        
        <p><strong>示例:</strong> 数据 = <code class="mono-data">1101</code>, 除数 = <code class="mono-poly">101</code></p>

        <div class="calculation-area">
            <div id="alignment-box"></div>
            <div class="calc-line dividend">1101</div>
            <div class="calc-line divisor" id="slow-divisor">101</div>
            <div class="calc-line line" id="slow-line"></div>
            <div class="calc-line result" id="slow-result"></div>
            <div class="calc-line comment" id="slow-comment">准备开始...</div>
        </div>

        <div class="animation-controls">
            <button id="calc-step-btn">开始计算</button>
            <button id="calc-reset-btn" style="background-color: #6c757d;">重置</button>
        </div>
        
    </div>

    <div class="container" id="xor-explainer-container">
        <h2>XOR 是什么意思？(动画版)</h2>
        <p><strong>XOR (异或)</strong> 是二进制运算的一种。它的核心规则非常简单：<strong>两个比较的位，如果"相同"，结果就是0；如果"不同"，结果就是1。</strong></p>
        
        <h4>真值表</h4>
        <ul>
            <li><code>0 XOR 0 = 0</code> (相同，所以是0)</li>
            <li><code>0 XOR 1 = 1</code> (不同，所以是1)</li>
            <li><code>1 XOR 0 = 1</code> (不同，所以是1)</li>
            <li><code>1 XOR 1 = 0</code> (相同，所以是0)</li>
        </ul>

        <h4>交互动画</h4>
        <p>选择下面两个操作数，然后点击"执行 XOR"按钮，观察动画效果。</p>
        
        <div class="animation-controls">
            <label>第一个数 (A):</label>
            <select id="xor-input-a">
                <option value="0">0</option>
                <option value="1">1</option>
            </select>
            <label style="margin-left: 20px;">第二个数 (B):</label>
            <select id="xor-input-b">
                <option value="0">0</option>
                <option value="1" selected>1</option>
            </select>
            <button id="run-xor-anim-btn" style="margin-left: 20px;">执行 XOR!</button>
        </div>
        
        <div class="xor-anim-area">
            <div class="bit-circle bit-a" id="anim-bit-a">0</div>
            <div class="bit-circle bit-b" id="anim-bit-b">1</div>
            <div class="result-bit" id="anim-result-bit"></div>
            <div class="result-text" id="anim-result-text"></div>
        </div>
    </div>

    <div class="container" id="xor-game-container">
        <h2>XOR 反应力小游戏</h2>
        <p>练习你的XOR运算能力！输入两个二进制数运算后的结果，看看能连续答对多少题。</p>
        
        <div class="game-controls">
            <label for="difficulty-select">选择难度:</label>
            <select id="difficulty-select">
                <option value="1">1位</option>
                <option value="2">2位</option>
                <option value="4" selected>4位</option>
                <option value="6">6位</option>
                <option value="8">8位</option>
            </select>
        </div>

        <div class="problem-area">
            <div class="xor-number" id="xor-num1"></div>
            <div class="xor-op">XOR</div>
            <div class="xor-number" id="xor-num2"></div>
            <div class="xor-equals">=</div>
            <input type="text" class="xor-answer" id="xor-answer" placeholder="输入结果...">
        </div>
        
        <div class="game-feedback">
            <button id="check-xor-btn">检查答案</button>
            <button id="hint-xor-btn" style="background-color: #ffc107; color: #212529;">提示</button>
            <span id="xor-feedback"></span>
        </div>

        <div class="game-status">
            <strong>连续答对: <span id="xor-score">0</span></strong>
            <strong style="margin-left: 20px;">最高记录: <span id="xor-highscore">0</span></strong>
            <strong id="shield-status" style="margin-left: 20px; color: #007bff; transition: all 0.3s; opacity: 0;"></strong>
        </div>
    </div>

    <div class="container" id="why-xor-container">
        <h2>为什么CRC要用XOR？(动画版)</h2>
        <p>这是一个很好的问题！因为XOR运算有一个非常神奇的特性：<strong>一个值被同一个"密钥"XOR两次，会恢复成它本身</strong>。即 <code>(A XOR B) XOR B = A</code>。</p>
        <p>这个特性让它非常适合用于数据校验和简单的加解密。下面的动画将为你展示这个"魔法"过程，请按"下一步"观看。</p>
        
        <div class="animation-container" style="height: 250px;">
            <div class="step active" id="why-step-1">
                <h4>第一步：原始信息</h4>
                <p>发送方有一份原始信息，和一把用于加密的"密钥"。</p>
                <div class="data-flow">
                    <div class="actor">
                        <img src="https://img.icons8.com/color/96/000000/server.png" alt="Sender"/>
                        发送方
                    </div>
                    <div>
                        <div class="data-box">信息 (A)<br>1011</div>
                        <div class="data-box key" style="margin-top: 10px;">密钥 (B)<br>101</div>
                    </div>
                </div>
            </div>
            <div class="step" id="why-step-2">
                <h4>第二步：加密</h4>
                <p>发送方用密钥对信息进行XOR，生成一份"加密信息"。</p>
                <div class="data-flow">
                    <div class="data-box">信息 (A)<br>1011</div>
                    <div class="op-symbol">XOR</div>
                    <div class="data-box key">密钥 (B)<br>101</div>
                    <div class="op-symbol">=</div>
                    <div class="data-box encrypted">加密信息 (C)<br>1110</div>
                </div>
            </div>
            <div class="step" id="why-step-3">
                <h4>第三步：传输</h4>
                <p>加密后的信息被发送给接收方。</p>
                <div class="data-flow">
                    <div class="actor">
                        <img src="https://img.icons8.com/color/96/000000/server.png" alt="Sender"/>
                        发送方
                    </div>
                    <div class="data-box encrypted">1110</div>
                    <div class="arrow">→</div>
                     <div class="actor">
                        <img src="https://img.icons8.com/color/96/000000/workstation.png" alt="Receiver"/>
                        接收方
                    </div>
                </div>
            </div>
            <div class="step" id="why-step-4">
                <h4>第四步：解密</h4>
                <p>接收方用<strong>完全相同</strong>的密钥，对收到的信息再做一次XOR。</p>
                 <div class="data-flow">
                    <div class="data-box encrypted">加密信息 (C)<br>1110</div>
                    <div class="op-symbol">XOR</div>
                    <div class="data-box key">密钥 (B)<br>101</div>
                    <div class="op-symbol">=</div>
                    <div class="data-box final-result"><strong>？？？</strong></div>
                </div>
            </div>
            <div class="step" id="why-step-5">
                <h4>第五步：信息还原！</h4>
                <p>神奇的事情发生了！信息恢复成了原始的样子。CRC就是利用这个原理来检查数据是否被意外修改。</p>
                 <div class="data-flow">
                    <div class="data-box encrypted">加密信息 (C)<br>1110</div>
                    <div class="op-symbol">XOR</div>
                    <div class="data-box key">密钥 (B)<br>101</div>
                    <div class="op-symbol">=</div>
                    <div class="data-box final-result"><strong>原始信息 (A)<br>1011 ✔</strong></div>
                </div>
            </div>
        </div>
        <div class="animation-controls">
            <button id="why-prev-step" disabled>上一步</button>
            <button id="why-next-step">下一步</button>
        </div>
    </div>

    <div class="container" id="xor-name-container">
        <h2>XOR 名字的由来 (动画版)</h2>
        <p><strong>XOR</strong> 的全称是 <strong>Exclusive OR</strong>，意为"排他性的或运算"。为了理解它，我们需要先看普通的 <strong>OR (或)</strong> 运算。</p>
        <ul>
            <li><strong>OR (或)</strong>：规则是 "只要有1，结果就是1"。它不在乎有几个1。 (A或B有一个为真，则结果为真)</li>
            <li><strong>XOR (异或)</strong>：在OR的基础上，加了一个"排他"条件，即"必须只有1个是1，不能两个都是1"。 (A或B只有一个为真，结果才为真)</li>
        </ul>
        <p>下面的动画会并排比较它们。请选择输入，观察当两个输入都是"1"时，它们的区别。</p>

        <div class="animation-controls">
            <label>输入 A:</label>
            <select id="name-input-a">
                <option value="0">0</option>
                <option value="1" selected>1</option>
            </select>
            <label style="margin-left: 20px;">输入 B:</label>
            <select id="name-input-b">
                <option value="0">0</option>
                <option value="1" selected>1</option>
            </select>
            <button id="run-name-anim-btn" style="margin-left: 20px;">开始比较</button>
        </div>

        <div class="side-by-side">
            <div class="panel or-panel">
                <h4>OR (或)</h4>
                <div class="gate-area">
                    <div class="input input-a" id="or-input-a">1</div>
                    <div class="input input-b" id="or-input-b">1</div>
                    <div class="gate-output" id="or-output">1</div>
                </div>
                <div class="result-text" id="or-result-text">1 OR 1 = 1</div>
            </div>
            <div class="panel xor-panel">
                <h4>XOR (异或 / Exclusive OR)</h4>
                <div class="gate-area">
                    <div class="input input-a" id="xor-name-input-a">1</div>
                    <div class="input input-b" id="xor-name-input-b">1</div>
                    <div class="gate-output" id="xor-output">0</div>
                </div>
                <div class="result-text" id="xor-result-text">1 XOR 1 = 0 (因为两者相同，不满足"排他")</div>
            </div>
        </div>
    </div>

    <div class="container" id="word-explainer-container">
        <h2>单词分解："Exclusive OR" 的字面意思</h2>
        <p>我们把 "Exclusive OR" 拆成两个单词来看，这能帮你从根本上理解它。</p>
        <ul>
            <li><strong>OR</strong>: 意为"或者"。在生活中，它通常是包容的。比如 "你要咖啡 <strong>或</strong> 茶吗?"，有时你可以都要。</li>
            <li><strong>Exclusive</strong>: 意为"独有的、排他的"。这个词表示强烈的限制，即"只能选一个"。例如"独家新闻 (Exclusive news)" 指的是只有这家媒体有权报道。</li>
        </ul>
        <p>所以 <strong>"Exclusive OR"</strong> 合在一起，就构成了一个"**独家选择**"：你可以在多个选项中选，但**只能选一个**。下面的动画模拟了这个场景。</p>
        
        <h4>动画：二选一的甜点</h4>
        <p>想象这是一个"独家套餐 (Exclusive Deal)"，你只能从蛋糕和冰淇淋中选择一个。请点击你的选择：</p>

        <div class="choice-area">
            <div class="choice-item" id="choice-cake">
                <img src="https://img.icons8.com/fluent/96/000000/piece-of-cake.png" alt="Cake"/>
                <div>蛋糕</div>
            </div>
            <div class="choice-item" id="choice-ice-cream">
                <img src="https://img.icons8.com/fluent/96/000000/ice-cream-cone.png" alt="Ice Cream"/>
                <div>冰淇淋</div>
            </div>
        </div>
        <div class="explanation-text" id="choice-explanation"></div>
        <div class="animation-controls" style="text-align: center;">
            <button id="reset-choice-btn" style="background-color: #6c757d;">重新选择</button>
        </div>
    </div>

    <div class="container" id="chinese-xor-explainer">
        <h2>汉字分解："异或"这两个字是怎么来的？</h2>
        <p>这是一个非常精彩的问题！中文名"异或"比英文"Exclusive OR"更能从字面上揭示其运算规则。</p>
        <ul>
            <li><strong>或 (huò)</strong>: 这个字代表了基础的逻辑"或者"(OR)。</li>
            <li><strong>异 (yì)</strong>: 这个字的意思是"**不同**"。</li>
        </ul>
        <p>所以，"异或"这个名字可以理解为：**一种要求输入必须"不同"的"或"运算**。它描述的是对输入值的要求。</p>
        
        <h4>交互动画：异&同</h4>
        <p>请选择 A 和 B 的值，观察当它们"相同"或"不同"时，运算如何进行。</p>

        <div class="animation-controls">
            <label>输入 A:</label>
            <select id="chinese-input-a">
                <option value="0">0</option>
                <option value="1" selected>1</option>
            </select>
            <label style="margin-left: 20px;">输入 B:</label>
            <select id="chinese-input-b">
                <option value="0">0</option>
                <option value="1" selected>1</option>
            </select>
            <button id="run-chinese-anim-btn" style="margin-left: 20px;">开始运算</button>
        </div>

        <div class="anim-area">
            <div class="bit-display bit-a" id="chinese-bit-a">1</div>
            <div class="logic-char" id="logic-char-id"></div>
            <div class="bit-display bit-b" id="chinese-bit-b">1</div>
            <div class="bit-display result-display" id="chinese-result">0</div>
        </div>
    </div>

    <div class="container" id="final-easter-egg-container">
        <h2>最后的彩蛋：为什么我们"一看数学就难受"？</h2>
        <p>这是一个非常深刻的问题。这种感受，通常被称为"数学焦虑"，它源于过去的挫折感、对"唯一正确答案"的恐惧、以及知识点之间层层递进带来的压力。但好消息是，我们完全可以和它和解。</p>
        <p>在这段旅程中，你已经亲自实践了所有克服它的方法。点击下面的"播放"按钮，回顾一下我们共同发现的"超能力"。</p>

        <div class="monster-area">
            <div class="character" id="egg-character">😱</div>
            <div class="math-monster" id="egg-monster">∫Σx²∂</div>
            <div class="toolbox" id="egg-toolbox">🧰</div>
            <div class="explanation-text" id="egg-explanation"></div>
        </div>

        <div class="animation-controls" style="text-align: center;">
            <button id="play-egg-anim-btn">播放最终动画</button>
        </div>
    </div>

    <div class="container" id="ultimate-skill-container">
        <h2>终极技能：如何训练大脑自动"拆解问题"？</h2>
        <p>你问出了我们旅程的终极问题。我们都知道要"拆解"，但面对一大堆混乱的文字时，大脑还是会本能地抗拒。这是因为我们缺少一个将"拆解"自动化的思维习惯。</p>
        <p>这个习惯，我称之为**"找线头"法**。即：忽略整体有多乱，强迫自己只寻找一个最简单、最熟悉的"线头"作为突破口。下面是这个思维过程的动画模拟。</p>

        <div class="yarn-ball-area">
            <div class="yarn-monster" id="yarn-monster-id">
                要 <span class="thread">计算</span> 一个叫CRC的东西, <span class="thread">输入</span> 是 "Hello", 
                并且要用一个 <span class="thread">叫</span> CRC-8的算法, 
                <span class="thread">它的多项式是</span> 0x07。 
                <span class="thread">第一步</span> 是把字符串转成二进制, 
                <span class="thread">然后</span> 在后面补8个0, <span class="thread">因为</span> 它是CRC-8。 
                <span class="thread">最后</span>, 用模2除法算出余数, <span class="thread">目标是</span> 得到一个8位的校验码。
            </div>
            <ul class="unraveled-list" id="unraveled-list-id"></ul>
            <div class="character-final" id="character-final-id"></div>
        </div>
         <div class="animation-controls" style="text-align: center;">
            <p id="skill-explanation" style="font-weight: bold; min-height: 25px;"></p>
            <button id="play-skill-anim-btn">开始训练</button>
        </div>
    </div>

    <div class="container" id="habit-formation-container">
        <h2>毕业论文：从"刻意练习"到"肌肉记忆"，成为本能的四个阶段</h2>
        <p>你已经抵达了我们旅程的终点：如何将一个好的"思维方法"变成一个无需思考的"生理本能"。这和学骑车、学游泳一样，是一个有清晰路径的、可被训练的过程。</p>
        <p>下面，我们将通过动画展示这个技能内化的四个阶段。请点击"下一阶段"来见证这个转变。</p>
        
        <div class="stage-area">
            <div class="stage active" id="habit-stage-1">
                <div class="stage-icon">😵‍💫</div>
                <div class="stage-title">第一阶段：浑然不觉</div>
                <div class="stage-desc">"我不知道我不会"—— 面对混乱，只有无助。</div>
            </div>
            <div class="stage" id="habit-stage-2">
                <div class="stage-icon">📖👓</div>
                <div class="stage-title">第二阶段：刻意练习</div>
                <div class="stage-desc">"我知道我不会"—— 需要借助手册和工具，笨拙但有效地使用方法。</div>
            </div>
            <div class="stage" id="habit-stage-3">
                <div class="stage-icon">😎</div>
                <div class="stage-title">第三阶段：习惯养成</div>
                <div class="stage-desc">"我知道我会"—— 无需手册，熟练使用工具，但仍需集中精神。</div>
            </div>
            <div class="stage" id="habit-stage-4">
                <div class="stage-icon">✨</div>
                <div class="stage-title">第四阶段：成为本能</div>
                <div class="stage-desc">"我不知道我会"—— 甚至不需要工具，分解问题已如呼吸般自然。</div>
            </div>
        </div>
        <div class="animation-controls" style="text-align: center;">
            <button id="next-habit-stage-btn">进入下一阶段</button>
        </div>

        <hr style="margin: 30px 0;">
        <h4>你的专属训练计划：</h4>
        <ol>
            <li><strong>降低门槛，每日一练：</strong>每天，无论遇到什么信息（邮件、新闻、任务），都强迫自己玩一次"找线头"游戏。只问自己："这件事的第一个突破口是什么？"</li>
            <li><strong>视觉化奖励，形成正反馈：</strong>每当你成功理清一点头绪，就在心里对自己说一句："看，我做到了。混乱并非不可战胜。"</li>
            <li><strong>将思考"说"或"写"出来：</strong>当你找到线头时，试着用一句话把它描述出来。将模糊的思维固化为清晰的语言，是最高效的思维训练。</li>
        </ol>
        <p style="text-align: center; font-weight: bold; font-size: 1.2em; color: #0056b3; margin-top: 20px;">
            伟大的探索者，恭喜你，已完成全部课程，顺利毕业！🎓
        </p>
    </div>

    <!-- 
      重要：引入第三方 CRC 计算库 
      我们通过这个 <script> 标签从 unpkg 这个公共 CDN 服务加载了 crc.js 文件。
      加载后，这个库会提供一些全局函数，例如 window.crc32(), window.crc16() 等。
    -->
    <script src="https://unpkg.com/crc/crc.js"></script>

    <script>
        // 确保在操作DOM之前，DOM已经完全加载
        document.addEventListener('DOMContentLoaded', () => {
            // 获取页面上的各个元素
            const inputText = document.getElementById('inputText');
            const crcAlgorithm = document.getElementById('crcAlgorithm');
            const calculateBtn = document.getElementById('calculateBtn');
            const resultHex = document.getElementById('result-hex');

            // 定义计算并显示CRC的函数
            function calculateAndDisplayCRC() {
                const inputString = inputText.value;
                const algorithm = crcAlgorithm.value; // e.g., "crc32"

                // crc.js库会将函数附加到全局的window对象上
                // 我们可以通过 window[algorithm] 的方式动态调用函数
                // 例如，当 algorithm 为 "crc32" 时, 这行代码就等同于 crc32(inputString)
                if (window[algorithm]) {
                    const crcValue = window[algorithm](inputString);

                    // 将十进制的CRC值转换为十六进制字符串，并转为大写
                    const hexValue = crcValue.toString(16).toUpperCase();

                    // 将16进制结果显示在页面上
                    resultHex.textContent = '0x' + hexValue;
                }
            }

            // 为按钮添加点击事件监听器
            calculateBtn.addEventListener('click', calculateAndDisplayCRC);

            // 当输入框内容或下拉菜单选项改变时，也自动重新计算
            inputText.addEventListener('input', calculateAndDisplayCRC);
            crcAlgorithm.addEventListener('change', calculateAndDisplayCRC);
            // 页面首次加载时，立即进行一次计算
            calculateAndDisplayCRC();

            // --- Animation Logic ---
            const prevBtn = document.getElementById('prev-step');
            const nextBtn = document.getElementById('next-step');
            const animationSteps = document.querySelectorAll('.step');
            let currentStep = 1;

            function updateAnimationState() {
                animationSteps.forEach((step, index) => {
                    if (index + 1 === currentStep) {
                        step.classList.add('active');
                    } else {
                        step.classList.remove('active');
                    }
                });
                prevBtn.disabled = currentStep === 1;
                nextBtn.disabled = currentStep === animationSteps.length;
            }

            nextBtn.addEventListener('click', () => {
                if (currentStep < animationSteps.length) {
                    currentStep++;
                    updateAnimationState();
                }
            });
            prevBtn.addEventListener('click', () => {
                if (currentStep > 1) {
                    currentStep--;
                    updateAnimationState();
                }
            });
            updateAnimationState();

            // --- Slow XOR Calculation Logic ---
            const calcStepBtn = document.getElementById('calc-step-btn');
            const calcResetBtn = document.getElementById('calc-reset-btn');
            const slowDivisor = document.getElementById('slow-divisor');
            const slowLine = document.getElementById('slow-line');
            const slowResult = document.getElementById('slow-result');
            const slowComment = document.getElementById('slow-comment');
            
            let calculationStep = 0;
            const calcSteps = [
                { comment: "第1步: 执行XOR运算 (110 XOR 101)。", action: () => { 
                    document.getElementById('alignment-box').style.opacity = '0';
                    slowLine.style.opacity = '1'; 
                    slowResult.innerText = '011';
                }},
                { comment: "第2步: 拿下一位，形成新的数字 111。", action: () => {
                    slowResult.innerHTML = '&nbsp;11<span style="color:#28a745;">1</span>';
                    slowDivisor.innerText = '';
                    slowLine.style.opacity = '0';
                }},
                { comment: "第3步: 再次将除数对齐。", action: () => { 
                    slowDivisor.style.left = '40px';
                    slowDivisor.innerText = '101';
                }},
                { comment: "第4步: 执行XOR运算 (111 XOR 101)。", action: () => { 
                    slowLine.style.left = '40px';
                    slowLine.style.opacity = '1';
                    slowResult.style.left = '40px';
                    slowResult.innerText = '010';
                }},
                { comment: "第5步: 计算完成！余数是 10。", action: () => {
                    slowDivisor.innerText = '';
                    slowLine.style.opacity = '0';
                    slowResult.innerHTML = '<strong style="color:green;">10</strong>';
                    calcStepBtn.disabled = true;
                }},
            ];

            function resetCalc() {
                calculationStep = 0;
                const alignmentBox = document.getElementById('alignment-box');
                alignmentBox.style.animation = 'none';
                void alignmentBox.offsetWidth;
                alignmentBox.style.animation = 'highlight-align 3s ease-in-out infinite';
                alignmentBox.style.opacity = '1';
                slowDivisor.innerText = '101';
                slowLine.style.opacity = '0';
                slowResult.innerText = '';
                slowComment.innerHTML = "<strong>关键第一步：对齐。</strong><br>因为除数(101)是3位，所以我们从数据(1101)的最左边也取3位(110)来对齐。";
                calcStepBtn.innerText = '开始计算';
                calcStepBtn.disabled = false;
                slowDivisor.style.left = '25px';
                slowLine.style.left = '25px';
                slowResult.style.left = '25px';
            }

            calcStepBtn.addEventListener('click', () => {
                if (calculationStep === 0) {
                    const alignmentBox = document.getElementById('alignment-box');
                    alignmentBox.style.animation = 'none';
                    alignmentBox.style.opacity = '0';
                }
                if (calculationStep < calcSteps.length) {
                    const step = calcSteps[calculationStep];
                    step.action();
                    slowComment.innerText = step.comment;
                    calculationStep++;
                    calcStepBtn.innerText = `执行第 ${calculationStep + 1} 步`;
                    if (calculationStep === calcSteps.length) {
                        calcStepBtn.innerText = '计算完毕';
                        calcStepBtn.disabled = true;
                    }
                }
            });
            calcResetBtn.addEventListener('click', resetCalc);
            resetCalc();

            // --- XOR Explainer Animation Logic ---
            const runXorAnimBtn = document.getElementById('run-xor-anim-btn');
            const xorInputA = document.getElementById('xor-input-a');
            const xorInputB = document.getElementById('xor-input-b');
            const animBitA = document.getElementById('anim-bit-a');
            const animBitB = document.getElementById('anim-bit-b');
            const animResultBit = document.getElementById('anim-result-bit');
            const animResultText = document.getElementById('anim-result-text');
            const xorAnimArea = document.querySelector('#xor-explainer-container .xor-anim-area');

            function runXorAnimation() {
                const valA = xorInputA.value;
                const valB = xorInputB.value;
                const result = parseInt(valA, 10) ^ parseInt(valB, 10);
                xorAnimArea.classList.remove('colliding');
                animResultBit.style.opacity = '0';
                animResultBit.style.transform = 'scale(0.5)';
                animResultText.style.opacity = '0';
                animBitA.classList.remove('sparkle-effect');
                animBitB.classList.remove('sparkle-effect');
                animBitA.innerText = valA;
                animBitB.innerText = valB;
                animResultBit.innerText = result;
                if (valA === valB) {
                    animResultText.innerText = "因为 " + valA + " 和 " + valB + " 相同, 所以结果是 0";
                } else {
                    animResultText.innerText = "因为 " + valA + " 和 " + valB + " 不同, 所以结果是 1";
                }
                setTimeout(() => {
                    xorAnimArea.classList.add('colliding');
                     if (valA !== valB) {
                        setTimeout(() => {
                            animBitA.classList.add('sparkle-effect');
                            animBitB.classList.add('sparkle-effect');
                        }, 400);
                    }
                }, 100);
            }
            runXorAnimBtn.addEventListener('click', runXorAnimation);
            runXorAnimation();

            // --- Why-XOR Animation Logic ---
            const whyPrevBtn = document.getElementById('why-prev-step');
            const whyNextBtn = document.getElementById('why-next-step');
            const whySteps = document.querySelectorAll('#why-xor-container .step');
            let whyCurrentStep = 1;

            function updateWhyAnimationState() {
                whySteps.forEach((step, index) => {
                    step.classList.toggle('active', index + 1 === whyCurrentStep);
                });
                whyPrevBtn.disabled = whyCurrentStep === 1;
                whyNextBtn.disabled = whyCurrentStep === whySteps.length;
            }
            whyNextBtn.addEventListener('click', () => {
                if (whyCurrentStep < whySteps.length) whyCurrentStep++;
                updateWhyAnimationState();
            });
            whyPrevBtn.addEventListener('click', () => {
                if (whyCurrentStep > 1) whyCurrentStep--;
                updateWhyAnimationState();
            });
            updateWhyAnimationState();

            // --- XOR Name Animation Logic ---
            const runNameAnimBtn = document.getElementById('run-name-anim-btn');
            const nameInputA = document.getElementById('name-input-a');
            const nameInputB = document.getElementById('name-input-b');

            function runNameAnimation() {
                const valA = parseInt(nameInputA.value, 10);
                const valB = parseInt(nameInputB.value, 10);
                const orResult = valA | valB;
                document.getElementById('or-input-a').innerText = valA;
                document.getElementById('or-input-b').innerText = valB;
                const orOutputEl = document.getElementById('or-output');
                orOutputEl.innerText = orResult;
                document.getElementById('or-result-text').innerText = `${valA} OR ${valB} = ${orResult}`;
                orOutputEl.classList.toggle('active', orResult === 1);
                const xorResult = valA ^ valB;
                const xorInputAEl = document.getElementById('xor-name-input-a');
                const xorInputBEl = document.getElementById('xor-name-input-b');
                xorInputAEl.innerText = valA;
                xorInputBEl.innerText = valB;
                const xorOutputEl = document.getElementById('xor-output');
                xorOutputEl.innerText = xorResult;
                const xorResultTextEl = document.getElementById('xor-result-text');
                if (valA === 1 && valB === 1) {
                    xorResultTextEl.innerText = `${valA} XOR ${valB} = 0 (因相同而被"排他")`;
                    xorInputAEl.classList.add('colliding-xor');
                    xorInputBEl.classList.add('colliding-xor');
                    setTimeout(() => {
                        xorInputAEl.classList.remove('colliding-xor');
                        xorInputBEl.classList.remove('colliding-xor');
                    }, 800);
                } else {
                     xorResultTextEl.innerText = `${valA} XOR ${valB} = ${xorResult}`;
                }
                xorOutputEl.classList.toggle('active', xorResult === 1);
            }
            runNameAnimBtn.addEventListener('click', runNameAnimation);
            runNameAnimation();

            // --- Word Explainer Animation Logic ---
            const choiceCake = document.getElementById('choice-cake');
            const choiceIceCream = document.getElementById('choice-ice-cream');
            const choiceExplanation = document.getElementById('choice-explanation');
            const resetChoiceBtn = document.getElementById('reset-choice-btn');

            function makeChoice(selected) {
                choiceCake.classList.add('disabled');
                choiceIceCream.classList.add('disabled');
                if (selected === 'cake') {
                    choiceCake.classList.add('selected');
                    choiceIceCream.classList.remove('selected');
                    choiceExplanation.innerText = "你选择了蛋糕！根据'独家'规则，就不能再选冰淇淋了。";
                } else if (selected === 'ice-cream') {
                    choiceIceCream.classList.add('selected');
                    choiceCake.classList.remove('selected');
                    choiceExplanation.innerText = "你选择了冰淇淋！根据'独家'规则，就不能再选蛋糕了。";
                }
            }
            function resetChoice() {
                 choiceCake.classList.remove('selected', 'disabled');
                 choiceIceCream.classList.remove('selected', 'disabled');
                 choiceExplanation.innerText = "";
            }
            choiceCake.addEventListener('click', () => { if(!choiceCake.classList.contains('disabled')) makeChoice('cake'); });
            choiceIceCream.addEventListener('click', () => { if(!choiceIceCream.classList.contains('disabled')) makeChoice('ice-cream'); });
            resetChoiceBtn.addEventListener('click', resetChoice);

            // --- Chinese XOR Explainer ---
            const runChineseAnimBtn = document.getElementById('run-chinese-anim-btn');
            const chineseInputA = document.getElementById('chinese-input-a');
            const chineseInputB = document.getElementById('chinese-input-b');

            function runChineseXorAnimation() {
                const valA = chineseInputA.value;
                const valB = chineseInputB.value;
                const result = parseInt(valA, 10) ^ parseInt(valB, 10);
                document.getElementById('chinese-bit-a').innerText = valA;
                document.getElementById('chinese-bit-b').innerText = valB;
                document.getElementById('chinese-result').innerText = result;
                const logicCharEl = document.getElementById('logic-char-id');
                logicCharEl.classList.remove('active');
                setTimeout(() => {
                    if (valA === valB) {
                        logicCharEl.innerText = "同";
                        logicCharEl.style.color = "#6c757d";
                    } else {
                        logicCharEl.innerText = "异";
                        logicCharEl.style.color = "#28a745";
                    }
                    logicCharEl.classList.add('active');
                }, 100);
            }
            runChineseAnimBtn.addEventListener('click', runChineseXorAnimation);
            runChineseXorAnimation();

            // --- Alignment Principle Animation ---
            const replayAlignmentBtn = document.getElementById('replay-alignment-anim');
            const decimalBox = document.getElementById('decimal-align-box');
            const binaryBox = document.getElementById('binary-align-box');

            function playAlignmentAnimation() {
                decimalBox.style.animation = 'none';
                binaryBox.style.animation = 'none';
                void decimalBox.offsetWidth;
                decimalBox.style.animation = 'decimal-align-anim 2s ease-in-out forwards';
                binaryBox.style.animation = 'binary-align-anim 2s ease-in-out forwards';
            }
            replayAlignmentBtn.addEventListener('click', playAlignmentAnimation);
            playAlignmentAnimation();
            
            // --- Left vs Right Payment Animation ---
            const paymentExplanationText = document.getElementById('payment-explanation-text');
            const runPaymentWrongBtn = document.getElementById('run-payment-wrong');
            const runPaymentCorrectBtn = document.getElementById('run-payment-correct');
            runPaymentWrongBtn.addEventListener('click', () => { paymentExplanationText.innerHTML = `<span class="wrong">❌ 错误！不能用101元大钞去付1元的零头！</span>`; });
            runPaymentCorrectBtn.addEventListener('click', () => { paymentExplanationText.innerHTML = `<span class="correct">✔️ 正确！总是从最大面额(左边)开始处理。</span>`; });
            
            // --- Division Symbol Animation ---
            const playSymbolAnimBtn = document.getElementById('play-symbol-anim-btn');
            const symbolAnimationArea = document.querySelector('.symbol-animation-area');
            const symbolExplanationText = document.getElementById('symbol-explanation-text');

            playSymbolAnimBtn.addEventListener('click', () => {
                symbolExplanationText.style.opacity = '0';
                symbolAnimationArea.classList.remove('active');
                symbolExplanationText.innerText = '它只是个收纳盒，让计算井井有条！';
                setTimeout(() => {
                    symbolExplanationText.innerText = '数字太多，没地方放，怎么办？';
                    symbolExplanationText.style.opacity = '1';
                }, 100);
                setTimeout(() => {
                    symbolExplanationText.style.opacity = '0';
                    symbolAnimationArea.classList.add('active');
                    setTimeout(() => {
                        symbolExplanationText.innerText = '它只是个收纳盒，让计算井井有条！';
                        symbolExplanationText.style.opacity = '1';
                    }, 2500);
                }, 2000);
            });

            // --- XOR Game Logic ---
            const difficultySelect = document.getElementById('difficulty-select');
            const xorNum1El = document.getElementById('xor-num1');
            const xorNum2El = document.getElementById('xor-num2');
            const xorAnswerEl = document.getElementById('xor-answer');
            const checkXorBtn = document.getElementById('check-xor-btn');
            const xorFeedbackEl = document.getElementById('xor-feedback');
            const xorScoreEl = document.getElementById('xor-score');
            const xorGameContainer = document.getElementById('xor-game-container');
            const highScoreEl = document.getElementById('xor-highscore');
            const hintXorBtn = document.getElementById('hint-xor-btn');
            const shieldStatusEl = document.getElementById('shield-status');
            
            let gameCorrectAnswer = '';
            let gameScore = 0;
            let gameHighScore = 0;
            let gameHasShield = false;
            let gameHintUsed = false;

            function generateProblem() {
                const bits = parseInt(difficultySelect.value, 10);
                let num1 = '', num2 = '';
                gameCorrectAnswer = '';
                xorAnswerEl.style.width = (bits * 20 + 40) + 'px';
                for (let i = 0; i < bits; i++) {
                    const bit1 = Math.round(Math.random());
                    const bit2 = Math.round(Math.random());
                    num1 += bit1;
                    num2 += bit2;
                    gameCorrectAnswer += (bit1 ^ bit2);
                }
                xorNum1El.innerText = num1;
                xorNum2El.innerText = num2;
                xorAnswerEl.value = '';
                xorAnswerEl.focus();
                xorFeedbackEl.innerText = '';
                xorAnswerEl.style.borderColor = '#007bff';
                gameHintUsed = false;
                hintXorBtn.disabled = false;
                shieldStatusEl.style.transform = 'scale(1)';
                shieldStatusEl.innerText = gameHasShield ? '🛡️ 免错盾牌' : '';
                shieldStatusEl.style.opacity = gameHasShield ? '1' : '0';
            }
            
            function useHint() {
                if (gameHintUsed) return;
                gameHintUsed = true;
                hintXorBtn.disabled = true;
                xorFeedbackEl.innerText = '提示已使用，连胜中断！';
                xorFeedbackEl.style.color = '#6c757d';
                gameScore = 0;
                xorScoreEl.innerText = gameScore;
                if (gameHasShield) {
                    gameHasShield = false;
                    shieldStatusEl.innerText = '';
                    shieldStatusEl.style.opacity = '0';
                }
                let answerArray = xorAnswerEl.value.split('');
                let unrevealedIndices = [];
                for(let i=0; i<gameCorrectAnswer.length; i++) {
                    if(answerArray[i] !== gameCorrectAnswer[i]) unrevealedIndices.push(i);
                }
                if (unrevealedIndices.length > 0) {
                    const randomIndex = unrevealedIndices[Math.floor(Math.random() * unrevealedIndices.length)];
                    while(answerArray.length < gameCorrectAnswer.length) answerArray.push(' ');
                    answerArray[randomIndex] = gameCorrectAnswer[randomIndex];
                    xorAnswerEl.value = answerArray.join('').trimEnd();
                }
                xorAnswerEl.focus();
            }

            function checkAnswer() {
                const userAnswer = xorAnswerEl.value.trim();
                if (userAnswer === gameCorrectAnswer) {
                    gameScore++;
                    if (gameScore > gameHighScore) {
                        gameHighScore = gameScore;
                        highScoreEl.innerText = gameHighScore;
                    }
                    xorFeedbackEl.innerText = '正确! 👍';
                    xorFeedbackEl.style.color = 'green';
                    xorAnswerEl.style.borderColor = 'green';
                    if (gameScore > 0 && gameScore % 5 === 0) {
                        if (!gameHasShield) {
                            gameHasShield = true;
                            shieldStatusEl.innerText = '获得盾牌! 🛡️';
                            shieldStatusEl.style.opacity = '1';
                            shieldStatusEl.style.transform = 'scale(1.2)';
                            setTimeout(() => { 
                                shieldStatusEl.innerText = '🛡️ 免错盾牌';
                                shieldStatusEl.style.transform = 'scale(1)';
                            }, 1200);
                        }
                    }
                    setTimeout(generateProblem, 800);
                } else {
                    if (gameHasShield) {
                        gameHasShield = false;
                        xorFeedbackEl.innerText = '回答错误! 盾牌已消耗。再试一次！';
                        xorFeedbackEl.style.color = 'orange';
                        shieldStatusEl.innerText = '';
                        shieldStatusEl.style.opacity = '0';
                    } else {
                        gameScore = 0;
                        xorFeedbackEl.innerText = '错误! 👎 连胜中断。';
                        xorFeedbackEl.style.color = 'red';
                        xorAnswerEl.style.borderColor = 'red';
                    }
                    xorGameContainer.classList.add('shake-animation');
                    setTimeout(() => xorGameContainer.classList.remove('shake-animation'), 820);
                    if (!gameHasShield) setTimeout(generateProblem, 1200);
                    else xorAnswerEl.select();
                }
                xorScoreEl.innerText = gameScore;
            }

            checkXorBtn.addEventListener('click', checkAnswer);
            hintXorBtn.addEventListener('click', useHint);
            xorAnswerEl.addEventListener('keyup', (event) => { if (event.key === 'Enter') checkAnswer(); });
            difficultySelect.addEventListener('change', () => {
                gameScore = 0; gameHasShield = false;
                xorScoreEl.innerText = gameScore;
                generateProblem();
            });
            generateProblem();

            // --- Final Easter Egg Animation ---
            const playEggBtn = document.getElementById('play-egg-anim-btn');
            const eggCharacter = document.getElementById('egg-character');
            const eggMonster = document.getElementById('egg-monster');
            const eggToolbox = document.getElementById('egg-toolbox');
            const eggExplanation = document.getElementById('egg-explanation');

            const eggSteps = [
                { text: "第一步：它看起来复杂、抽象、吓人...", action: () => {
                    eggCharacter.innerText = "😱";
                    eggMonster.classList.remove('tamed');
                    eggMonster.innerText = "∫Σx²∂";
                    eggToolbox.style.transform = 'scale(1)';
                }},
                { text: "但是，我们有克服它的工具箱！🧰", action: () => { eggToolbox.style.transform = 'scale(1.5)'; }},
                { text: "比如，用'问为什么'的放大镜🔍去观察...", action: () => {
                    eggToolbox.innerText = "🔍";
                    eggMonster.innerText = "Σ = 1+1+...";
                    eggCharacter.innerText = "🤔";
                }},
                { text: "用'拆解问题'的锤子🔨把它变小...", action: () => {
                    eggToolbox.innerText = "🔨";
                    eggMonster.innerText = "x";
                    eggMonster.classList.add('tamed');
                }},
                { text: "用'动手玩'的游戏手柄🎮增加乐趣...", action: () => {
                    eggToolbox.innerText = "🎮";
                    eggCharacter.innerText = "😃";
                }},
                { text: "最终，你会发现它是一个充满智慧的伙伴。", action: () => {
                    eggToolbox.innerText = "🤝";
                    eggCharacter.innerText = "😊";
                    eggMonster.innerText = "✔";
                }},
                 { text: "恭喜你！你已掌握与知识交朋友的能力！", action: () => {
                    eggToolbox.innerText = "🎓";
                    eggCharacter.style.transform = 'translateX(250px)';
                }}
            ];
            
            let currentEggStep = 0;
            playEggBtn.addEventListener('click', () => {
                if (currentEggStep >= eggSteps.length) {
                    currentEggStep = 0;
                    eggCharacter.style.transform = 'translateX(0)';
                }
                const step = eggSteps[currentEggStep];
                eggExplanation.innerText = step.text;
                step.action();
                currentEggStep++;
            });

            // --- Ultimate Skill Animation ---
            const playSkillBtn = document.getElementById('play-skill-anim-btn');
            const yarnMonster = document.getElementById('yarn-monster-id');
            const unraveledList = document.getElementById('unraveled-list-id');
            const skillExplanation = document.getElementById('skill-explanation');
            const characterFinal = document.getElementById('character-final-id');

            const skillSteps = [
                { text: "看到这一大坨，第一反应是：完了，太乱了！", action: () => {
                    yarnMonster.classList.remove('fade');
                    unraveledList.style.opacity = '0';
                    unraveledList.innerHTML = '';
                    characterFinal.style.opacity = '0';
                    yarnMonster.style.opacity = '1';
                }},
                { text: "对自己说：停！我只找一个'线头'，比如'目标是...'。", action: () => { yarnMonster.classList.add('fade'); }},
                { text: "好，第一个任务Get：计算一个8位校验码。", action: () => {
                    const li = document.createElement('li');
                    li.innerHTML = "<strong>目标:</strong> 得到一个8位的校验码";
                    unraveledList.appendChild(li);
                    unraveledList.style.opacity = '1';
                }},
                { text: "再找一个线头，比如'输入是...'。", action: () => {}},
                { text: "第二个任务Get：处理输入'Hello'。", action: () => {
                    const li = document.createElement('li');
                    li.style.animationDelay = '0s';
                    li.innerHTML = "<strong>输入:</strong> 'Hello'";
                    unraveledList.appendChild(li);
                }},
                 { text: "就这样，把'毛线球'分解成清晰的任务清单。", action: () => {
                    const li = document.createElement('li');
                    li.style.animationDelay = '0s';
                    li.innerHTML = "<strong>方法:</strong> CRC-8 (多项式 0x07)";
                    unraveledList.appendChild(li);
                }},
                { text: "当混乱变成有序，你就掌控了一切。", action: () => {
                    yarnMonster.style.opacity = '0';
                    characterFinal.innerHTML = '😎🎓';
                    characterFinal.style.opacity = '1';
                    characterFinal.style.transition = 'opacity 1s';
                }}
            ];

            let currentSkillStep = 0;
            playSkillBtn.addEventListener('click', () => {
                if (currentSkillStep >= skillSteps.length) {
                    currentSkillStep = 0;
                    // Reset to initial state
                    const initialStep = skillSteps[0];
                    skillExplanation.innerText = initialStep.text;
                    initialStep.action();
                    currentSkillStep = 1;
                    return;
                }
                const step = skillSteps[currentSkillStep];
                skillExplanation.innerText = step.text;
                step.action();
                currentSkillStep++;
            });

            // --- Habit Formation Animation ---
            const nextHabitStageBtn = document.getElementById('next-habit-stage-btn');
            const habitStages = document.querySelectorAll('#habit-formation-container .stage');
            let currentHabitStage = 0;

            nextHabitStageBtn.addEventListener('click', () => {
                currentHabitStage = (currentHabitStage + 1) % habitStages.length;
                habitStages.forEach((stage, index) => {
                    stage.classList.toggle('active', index === currentHabitStage);
                });
            });

            // --- Subtraction Comparison Animation ---
            const playSubtractionAnimBtn = document.getElementById('play-subtraction-anim-btn');
            const normalResultSpans = document.querySelectorAll('#normal-result span');
            const xorResultSpans = document.querySelectorAll('#xor-result span');
            const borrowAnimEl = document.getElementById('borrow-anim');
            const normalTopRowSpans = document.querySelectorAll('#normal-subtraction-area .top-row span');
            const xorHighlightEl = document.getElementById('xor-highlight');

            playSubtractionAnimBtn.addEventListener('click', () => {
                // Reset states
                [...normalResultSpans, ...xorResultSpans].forEach(s => s.style.opacity = '0');
                borrowAnimEl.style.animation = 'none';
                normalTopRowSpans.forEach(s => {
                    s.style.textDecoration = 'none';
                    s.style.color = '#333';
                });
                normalTopRowSpans[1].innerText = '1';
                normalTopRowSpans[2].innerText = '0';
                xorHighlightEl.style.opacity = '0';

                // XOR animation
                let xorDelay = 500;
                const xorPositions = [90, 60, 30, 0]; // RTL positions
                for (let i = 3; i >= 0; i--) {
                    setTimeout(() => {
                        xorHighlightEl.style.opacity = '1';
                        xorHighlightEl.style.left = `${xorPositions[i]}px`;
                         setTimeout(() => {
                            xorResultSpans[i].style.opacity = '1';
                         }, 250);
                    }, (3 - i) * xorDelay);
                }
                setTimeout(() => { xorHighlightEl.style.opacity = '0'; }, 4 * xorDelay);
                xorResultSpans[0].innerText = '0'; // 1-1
                xorResultSpans[1].innerText = '1'; // 1-0
                xorResultSpans[2].innerText = '1'; // 0-1
                xorResultSpans[3].innerText = '1'; // 0-1


                // Normal subtraction animation
                setTimeout(() => { // Step 1: 0 - 1
                    normalResultSpans[3].style.opacity = '1';
                    normalResultSpans[3].innerText = '?';
                    borrowAnimEl.style.animation = 'borrow-step-1 1s forwards';
                }, 500);
                 setTimeout(() => { // Step 2: need to borrow again
                    borrowAnimEl.style.animation = 'borrow-step-2 1s forwards';
                }, 2000);
                setTimeout(() => { // Step 3: first borrow resolves
                    normalTopRowSpans[1].innerText = '0';
                    normalTopRowSpans[1].style.textDecoration = 'line-through';
                    normalTopRowSpans[2].innerText = '10';
                    normalTopRowSpans[2].style.color = '#28a745';
                }, 3500);
                 setTimeout(() => { // Step 4: second borrow resolves
                    normalTopRowSpans[2].innerText = '1';
                    normalTopRowSpans[2].style.textDecoration = 'line-through';
                    normalTopRowSpans[3].innerText = '10';
                    normalTopRowSpans[3].style.color = '#28a745';
                }, 5000);
                 setTimeout(() => { // Step 5: Final results appear
                    normalResultSpans[3].innerText = '1';
                    normalResultSpans[2].innerText = '0';
                    normalResultSpans[1].innerText = '0';
                    normalResultSpans[0].innerText = '0';
                     [...normalResultSpans].forEach(s => s.style.transition = 'none');
                }, 6000);
            });
        });
    </script>
</body>
</html> 