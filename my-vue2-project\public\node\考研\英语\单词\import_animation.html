<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画：Import</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f4f8;
            color: #333;
            margin: 0;
            flex-direction: column;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background: #fff;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #0056b3;
            font-size: 3em;
            margin-bottom: 20px;
        }
        canvas {
            background: #e9f2fa;
            border-radius: 8px;
            width: 100%;
        }
        .explanation {
            text-align: left;
            margin-top: 20px;
            line-height: 1.8;
            font-size: 1.1em;
        }
        .explanation h2 {
            color: #0056b3;
            border-bottom: 2px solid #0056b3;
            padding-bottom: 5px;
            margin-top: 20px;
        }
        .explanation p {
            margin-bottom: 15px;
        }
        strong {
            color: #d63384;
        }
        .button-container {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s, transform 0.2s;
            font-weight: bold;
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
        }
        .interactive-zone {
            margin-top: 20px;
            padding: 20px;
            border: 2px dashed #007bff;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .interactive-zone p {
            font-weight: bold;
            color: #0056b3;
        }
        #drag-item {
            display: inline-block;
            padding: 10px;
            background: #28a745;
            color: white;
            border-radius: 5px;
            cursor: grab;
            user-select: none;
        }
        #drop-zone {
            margin-top: 10px;
            padding: 30px;
            border: 2px solid #ccc;
            border-radius: 5px;
            background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='5' ry='5' stroke='%23CCCFF' stroke-width='4' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>import</h1>
        <canvas id="wordCanvas" width="800" height="300"></canvas>
        <div class="button-container">
            <button id="playBtn">播放动画</button>
            <button id="resetBtn">重置</button>
        </div>

        <div class="explanation">
            <h2>单词分解 (Etymology)</h2>
            <p>
                单词 <strong>import</strong> 是一个非常典型的"前缀+词根"结构的单词。
            </p>
            <ul>
                <li><strong>im-</strong>: 这是一个前缀，是 <strong>in-</strong> 的变体，表示"向内，进入" (into, in)。就像我们说 "in the box" (在盒子里)。</li>
                <li><strong>port</strong>: 这是词根，意为"搬运，携带" (to carry)。想一想码头/港口 (port)，那里就是货物被运来运去的地方。我们熟悉的单词 a<u>port</u>ment，re<u>port</u>，sup<u>port</u>都与这个词根有关。</li>
            </ul>
            
            <h2>故事联想法</h2>
            <p>
                想象一艘满载着咖啡豆的货轮 (ship)，它正"搬运"着货物。当这艘货轮"进入"我们国家的港口时，这个行为就是 <strong>import</strong>。
            </p>
            <p>
                所以，<strong>im (进入) + port (搬运) &rarr; 把东西搬运进来 &rarr; 进口，引进</strong>。
            </p>

            <h2>词义与用法</h2>
            <p><strong>1. import (动词): 进口，引进</strong></p>
            <p><em>例句: The country needs to <strong>import</strong> oil from other countries.</em><br>
            翻译: 这个国家需要从其他国家<strong>进口</strong>石油。</p>
            
            <p><strong>2. import (名词): 进口，进口商品</strong></p>
            <p><em>例句: The government put a ban on the <strong>import</strong> of certain goods.</em><br>
            翻译: 政府禁止了某些商品的<strong>进口</strong>。</p>
        </div>

        <div class="interactive-zone">
            <p>互动练习：拖拽下面的"货物"到国境线内，完成一次"进口"！</p>
            <div id="drag-item" draggable="true">&#x1F4E6; 货物</div>
            <div id="drop-zone">国境线</div>
        </div>

    </div>

    <script>
        const canvas = document.getElementById('wordCanvas');
        const ctx = canvas.getContext('2d');
        const playBtn = document.getElementById('playBtn');
        const resetBtn = document.getElementById('resetBtn');

        let animationState = 'initial'; // initial, prefix, root, combine, final
        let progress = 0;
        let animationFrameId;

        const boat = {
            x: -80,
            y: 200,
            width: 80,
            height: 40,
            cargo: true
        };

        function drawText(text, x, y, size = 60, color = '#333') {
            ctx.fillStyle = color;
            ctx.font = `bold ${size}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
        }

        function drawBoat() {
            // hull
            ctx.fillStyle = '#8B4513';
            ctx.beginPath();
            ctx.moveTo(boat.x, boat.y);
            ctx.lineTo(boat.x + boat.width, boat.y);
            ctx.lineTo(boat.x + boat.width - 10, boat.y + boat.height);
            ctx.lineTo(boat.x + 10, boat.y + boat.height);
            ctx.closePath();
            ctx.fill();

            // cargo
            if(boat.cargo){
                ctx.fillStyle = '#D2B48C';
                ctx.fillRect(boat.x + 20, boat.y - 20, 40, 20);
                ctx.fillStyle = '#000';
                ctx.font = '12px Arial';
                ctx.fillText('Goods', boat.x + 40, boat.y-9);
            }

             // water
            ctx.fillStyle = 'rgba(0, 119, 191, 0.5)';
            ctx.beginPath();
            ctx.rect(0, 230, canvas.width, 70);
            ctx.fill();
        }
        
        function drawPort() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawText('port', 400, 100, 80, '#0056b3');
            drawText('="to carry" (搬运)', 400, 180, 30);
            drawBoat();
        }

        function drawIm() {
             ctx.clearRect(0, 0, canvas.width, canvas.height);
             drawText('im-', 400, 100, 80, '#d63384');
             drawText('="in, into" (进入)', 400, 180, 30);
             // Draw a box and an arrow
             ctx.strokeStyle = '#333';
             ctx.lineWidth = 3;
             ctx.strokeRect(550, 200, 100, 80);
             ctx.fillText('国家', 600, 240);

             ctx.beginPath();
             ctx.moveTo(450, 240);
             ctx.lineTo(540, 240);
             ctx.lineTo(520, 230);
             ctx.moveTo(540, 240);
             ctx.lineTo(520, 250);
             ctx.stroke();
        }

        function drawInitialState() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawText('import', 400, 150);
        }

        function animate() {
            progress += 1;
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (animationState === 'prefix') {
                drawIm();
                if(progress > 120) {
                    progress = 0;
                    animationState = 'root';
                }
            } else if (animationState === 'root') {
                drawPort();
                boat.x += 1;
                if(boat.x > canvas.width) boat.x = -boat.width;
                if(progress > 120) {
                    progress = 0;
                    animationState = 'combine';
                    boat.x = 100; // Reset boat position
                }
            } else if (animationState === 'combine') {
                drawText('im', 250, 150, 80, '#d63384');
                drawText('+', 350, 150, 80);
                drawText('port', 480, 150, 80, '#0056b3');
                if(progress > 120) {
                    progress = 0;
                    animationState = 'final';
                    boat.x = 100;
                }
            } else if (animationState === 'final') {
                drawText('import = "进口"', 400, 100);
                // Animate boat entering a "port"
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 3;
                ctx.strokeRect(550, 180, 150, 100);
                ctx.fillText('国家/港口', 625, 230);
                
                drawBoat();
                if (boat.x < 580) {
                    boat.x += 2;
                } else {
                    boat.cargo = false; // Unload cargo
                }
                
                if (progress > 300) {
                    playBtn.disabled = false;
                    cancelAnimationFrame(animationFrameId);
                    return;
                }
            }
            animationFrameId = requestAnimationFrame(animate);
        }

        playBtn.addEventListener('click', () => {
            if (animationState === 'initial' || progress > 300) {
                progress = 0;
                animationState = 'prefix';
                boat.x = -80;
                boat.cargo = true;
                playBtn.disabled = true;
                animate();
            }
        });

        resetBtn.addEventListener('click', () => {
            cancelAnimationFrame(animationFrameId);
            animationState = 'initial';
            progress = 0;
            boat.x = -80;
            boat.cargo = true;
            drawInitialState();
            playBtn.disabled = false;
        });

        // Interactive Drag and Drop
        const dragItem = document.getElementById('drag-item');
        const dropZone = document.getElementById('drop-zone');

        dragItem.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', 'cargo');
            dropZone.style.backgroundColor = '#e9f2fa';
        });

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.style.backgroundColor = '#f8f9fa';
        });



        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.style.backgroundColor = '#d4edda';
            dropZone.textContent = '成功进口！这就是 "Import"！';
            dragItem.style.display = 'none';
        });

        drawInitialState();

    </script>
</body>
</html> 