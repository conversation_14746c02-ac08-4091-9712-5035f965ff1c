<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>层次化路由选择学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .question-box h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d4aa;
            background: rgba(0, 212, 170, 0.3);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.3);
            animation: shake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        canvas {
            width: 100%;
            height: 500px;
            border-radius: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            line-height: 1.6;
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .concept-card {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(108, 92, 231, 0.3);
        }

        .concept-card.hierarchical {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .concept-card.addressing {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .concept-card.routing {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .concept-card.scalability {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .concept-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .concept-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 3px 8px;
            border-radius: 5px;
            color: #2d3436;
            font-weight: bold;
        }

        .step {
            background: rgba(116, 185, 255, 0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateX(5px);
        }

        .network-diagram {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 30px 0;
        }

        .network-level {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            width: 100%;
        }

        .network-node {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin: 0 15px;
            text-align: center;
            min-width: 120px;
            position: relative;
            transition: all 0.3s ease;
        }

        .network-node:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(116, 185, 255, 0.3);
        }

        .network-node.headquarters {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .network-node.branch {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .connection-line {
            width: 2px;
            height: 40px;
            background: #74b9ff;
            margin: 0 auto;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #01a3a4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .routing-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .concept-box {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
        }

        .concept-box h4 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .address-example {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }

        .address-example h4 {
            margin-bottom: 15px;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        .address-line {
            margin: 8px 0;
            padding: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 层次化路由选择学习</h1>
            <p>探索网络层次化设计的智慧</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2>📝 考试题目</h2>
                <p><strong>某企业通过一台路由器上联总部，下联4个分支结构，设计人员分配给下级机构一个连续的地址空间，采用一个子网或者超网段表示。这样的主要作用是（ ）</strong></p>
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 层次化路由选择
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 易于管理和性能优化
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 基于故障排查
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 使用较少的资源
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 什么是层次化路由？</h2>
            <div class="explanation">
                <p><span class="highlight">层次化路由</span>是指对网络拓扑结构和配置的了解是<strong>局部的</strong>，一台路由器不需要知道所有的路由信息，只需要了解其管辖的路由信息。</p>
                <p>🔑 <strong>核心理念：</strong>就像公司的组织架构，每个部门经理只需要管理自己部门的事务，不需要了解其他部门的详细情况。</p>
            </div>
            
            <div class="concept-box">
                <h4>🏗️ 层次化路由的关键要素</h4>
                <p>• <strong>层次化地址编码：</strong>子网和超网提供地址层次结构</p>
                <p>• <strong>局部路由信息：</strong>路由器只需了解管辖范围内的路由</p>
                <p>• <strong>路由聚合：</strong>多个子网可以用一个超网表示</p>
                <p>• <strong>可扩展性：</strong>网络规模增长时路由表不会爆炸性增长</p>
            </div>
        </div>

        <div class="section">
            <h2>🔧 网络层次化设计示例</h2>
            <div class="network-diagram">
                <div class="network-level">
                    <div class="network-node headquarters">
                        总部
                        <br>***********/16
                    </div>
                </div>
                <div class="connection-line"></div>
                <div class="network-level">
                    <div class="network-node">
                        路由器
                        <br>聚合路由
                    </div>
                </div>
                <div class="connection-line"></div>
                <div class="network-level">
                    <div class="network-node branch">分支1<br>***********/24</div>
                    <div class="network-node branch">分支2<br>***********/24</div>
                    <div class="network-node branch">分支3<br>***********/24</div>
                    <div class="network-node branch">分支4<br>***********/24</div>
                </div>
            </div>
            
            <div class="address-example">
                <h4>📋 地址分配示例</h4>
                <div class="address-line">总部看到的路由：***********/16 (包含所有分支)</div>
                <div class="address-line">分支1：***********/24 (256个地址)</div>
                <div class="address-line">分支2：***********/24 (256个地址)</div>
                <div class="address-line">分支3：***********/24 (256个地址)</div>
                <div class="address-line">分支4：***********/24 (256个地址)</div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 层次化路由核心概念</h2>
            <div class="concept-grid">
                <div class="concept-card hierarchical" onclick="showConceptDemo('hierarchical')">
                    <span class="icon">🏗️</span>
                    <h3>层次化结构</h3>
                    <p>分层管理网络</p>
                    <p>局部路由信息</p>
                </div>
                <div class="concept-card addressing" onclick="showConceptDemo('addressing')">
                    <span class="icon">📍</span>
                    <h3>地址聚合</h3>
                    <p>连续地址空间</p>
                    <p>子网超网表示</p>
                </div>
                <div class="concept-card routing" onclick="showConceptDemo('routing')">
                    <span class="icon">🛣️</span>
                    <h3>路由简化</h3>
                    <p>减少路由条目</p>
                    <p>提高效率</p>
                </div>
                <div class="concept-card scalability" onclick="showConceptDemo('scalability')">
                    <span class="icon">📈</span>
                    <h3>可扩展性</h3>
                    <p>支持网络增长</p>
                    <p>避免路由爆炸</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 层次化路由演示</h2>
            <div class="canvas-container">
                <canvas id="routingCanvas"></canvas>
                <div class="routing-indicator" id="routingIndicator">
                    🎯 点击按钮开始演示
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startHierarchicalDemo()">🏗️ 层次化结构演示</button>
                <button class="btn" onclick="showAddressAggregation()">📍 地址聚合演示</button>
                <button class="btn" onclick="showRoutingComparison()">🛣️ 路由对比演示</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2>🔍 详细解析每个选项</h2>

            <div class="step">
                <h3>选项A：层次化路由选择 ✅</h3>
                <p><strong>正确答案！</strong>这正是<span class="highlight">连续地址空间和子网/超网</span>的主要作用</p>
                <ul>
                    <li>🏗️ <strong>层次化结构：</strong>通过连续地址空间建立网络层次</li>
                    <li>📍 <strong>地址聚合：</strong>多个子网可以用一个超网表示</li>
                    <li>🛣️ <strong>路由简化：</strong>路由器只需了解聚合后的路由信息</li>
                    <li>📈 <strong>可扩展性：</strong>支持网络规模的增长而不会导致路由表爆炸</li>
                </ul>
                <div class="concept-box">
                    <h4>🔑 关键理解</h4>
                    <p>层次化路由选择需要配合层次化的地址编码，而子网或超网就属于层次化地址编码行为。</p>
                </div>
            </div>

            <div class="step">
                <h3>选项B：易于管理和性能优化 ❌</h3>
                <p><strong>错误原因：</strong>这是层次化设计的<span class="highlight">副作用</span>，不是主要作用</p>
                <ul>
                    <li>✅ <strong>确实有益：</strong>层次化设计确实有助于管理和性能</li>
                    <li>❌ <strong>不是主要目的：</strong>主要目的是实现层次化路由选择</li>
                    <li>🎯 <strong>因果关系：</strong>因为有了层次化路由，所以管理更容易</li>
                    <li>📊 <strong>优先级：</strong>管理便利是结果，不是设计的主要驱动力</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项C：基于故障排查 ❌</h3>
                <p><strong>错误原因：</strong>故障排查是<span class="highlight">间接好处</span>，不是主要作用</p>
                <ul>
                    <li>🔧 <strong>有助故障排查：</strong>层次化确实有助于定位问题</li>
                    <li>❌ <strong>不是主要目的：</strong>设计连续地址空间不是为了故障排查</li>
                    <li>🎯 <strong>设计初衷：</strong>主要是为了实现路由的层次化管理</li>
                    <li>📋 <strong>附加价值：</strong>故障排查便利是附加的好处</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项D：使用较少的资源 ❌</h3>
                <p><strong>错误原因：</strong>节省资源是<span class="highlight">结果</span>，不是主要作用</p>
                <ul>
                    <li>💾 <strong>确实节省资源：</strong>减少路由表条目，节省内存和处理时间</li>
                    <li>❌ <strong>不是主要目的：</strong>主要目的是实现层次化路由架构</li>
                    <li>🎯 <strong>设计目标：</strong>首要目标是可扩展的路由架构</li>
                    <li>📈 <strong>效果体现：</strong>资源节省是层次化路由的效果</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🧠 层次化路由深度解析</h2>

            <div class="concept-box">
                <h4>🎯 层次化路由的核心原理</h4>
                <p><strong>局部性原理：</strong>网络中的通信具有局部性，大部分流量在本地网络内</p>
                <p><strong>抽象层次：</strong>上层路由器不需要知道下层网络的详细拓扑</p>
                <p><strong>地址聚合：</strong>连续的地址空间可以聚合为更大的网络前缀</p>
            </div>

            <div class="comparison-table">
                <thead>
                    <tr>
                        <th>对比项目</th>
                        <th>平面化路由</th>
                        <th>层次化路由</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>路由表大小</strong></td>
                        <td>随网络规模线性增长</td>
                        <td>增长缓慢，可控制</td>
                    </tr>
                    <tr>
                        <td><strong>路由查找</strong></td>
                        <td>查找时间长</td>
                        <td>查找时间短</td>
                    </tr>
                    <tr>
                        <td><strong>网络可扩展性</strong></td>
                        <td>扩展性差</td>
                        <td>扩展性好</td>
                    </tr>
                    <tr>
                        <td><strong>路由更新</strong></td>
                        <td>更新频繁，影响大</td>
                        <td>更新局部化，影响小</td>
                    </tr>
                    <tr>
                        <td><strong>故障影响</strong></td>
                        <td>故障影响全网</td>
                        <td>故障影响局部</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>📍 地址聚合实例分析</h2>

            <div class="address-example">
                <h4>🔢 二进制地址分析</h4>
                <div class="address-line">***********/24 = 11000000.10101000.00000001.00000000</div>
                <div class="address-line">***********/24 = 11000000.10101000.00000010.00000000</div>
                <div class="address-line">***********/24 = 11000000.10101000.00000011.00000000</div>
                <div class="address-line">***********/24 = 11000000.10101000.00000100.00000000</div>
                <div class="address-line">聚合结果：***********/22 = 11000000.10101000.000000xx.xxxxxxxx</div>
            </div>

            <div class="concept-box">
                <h4>🎯 聚合的条件和好处</h4>
                <p><strong>聚合条件：</strong>地址必须是连续的，且具有相同的路由策略</p>
                <p><strong>聚合好处：</strong>减少路由表条目，降低路由协议开销，提高查找效率</p>
                <p><strong>聚合限制：</strong>聚合后失去了细粒度的路由控制能力</p>
            </div>
        </div>

        <div class="section">
            <h2>🌐 实际应用场景</h2>

            <div class="step">
                <h3>🏢 企业网络</h3>
                <p>企业总部通过一个聚合路由（如10.0.0.0/8）管理所有分支机构，每个分支分配一个子网（如********/16, ********/16等）。</p>
            </div>

            <div class="step">
                <h3>🌍 互联网骨干网</h3>
                <p>ISP使用CIDR（无类域间路由）进行地址聚合，将多个客户网络聚合为更大的前缀向上级ISP通告。</p>
            </div>

            <div class="step">
                <h3>🏠 家庭网络</h3>
                <p>家用路由器将内网的所有设备（***********/24）聚合为一个网络向ISP通告，ISP只需要一条路由指向这个家庭网络。</p>
            </div>
        </div>

        <div class="section">
            <h2>🎯 记忆技巧和考试要点</h2>

            <div class="explanation">
                <h3>🧠 记忆口诀</h3>
                <p style="font-size: 1.3rem; text-align: center; font-weight: bold; margin: 20px 0;">
                    "连续地址做聚合，层次路由是目标，<br>
                    局部信息就够用，网络扩展不用愁"
                </p>

                <h3>🔑 关键词记忆法</h3>
                <p><strong>层次化路由：</strong>"连续地址"、"子网超网"、"路由聚合"</p>
                <p><strong>局部性原理：</strong>"局部路由信息"、"不需要全部"、"分层管理"</p>
                <p><strong>可扩展性：</strong>"网络增长"、"路由表可控"、"避免爆炸"</p>

                <h3>🎯 考试技巧</h3>
                <ul>
                    <li>看到"连续地址空间"、"子网超网" → <span class="highlight">层次化路由选择</span></li>
                    <li>看到"管理便利"、"性能优化" → <span class="highlight">是结果不是目的</span></li>
                    <li>看到"故障排查"、"资源节省" → <span class="highlight">是副作用不是主要作用</span></li>
                    <li>记住：<span class="highlight">主要作用是实现层次化路由架构</span></li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎉 学习总结</h2>
            <div class="explanation">
                <h3>📚 核心知识点</h3>
                <ul>
                    <li><span class="highlight">层次化路由</span>：路由器只需了解局部路由信息</li>
                    <li><span class="highlight">地址聚合</span>：连续地址空间支持路由聚合</li>
                    <li><span class="highlight">可扩展性</span>：避免路由表随网络规模爆炸性增长</li>
                    <li><span class="highlight">局部性原理</span>：网络通信具有局部性特征</li>
                </ul>

                <h3>⚡ 实际应用</h3>
                <ul>
                    <li>企业网络的分支机构地址规划</li>
                    <li>互联网骨干网的CIDR路由聚合</li>
                    <li>数据中心网络的层次化设计</li>
                    <li>云网络的虚拟网络分段</li>
                </ul>
            </div>

            <div class="controls">
                <button class="btn" onclick="reviewQuestion()">🔄 重新答题</button>
                <button class="btn" onclick="showSummary()">📋 显示总结</button>
            </div>
        </div>
    </div>

    <script>
        // Canvas相关变量
        const canvas = document.getElementById('routingCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;
        let currentDemo = 'none';

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 题目交互逻辑
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n解释：分配连续地址空间并采用子网或超网表示的主要作用是实现层次化路由选择。这样可以让路由器只需了解局部路由信息，而不需要知道所有详细路由。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    progressFill.style.width = '25%';
                    setTimeout(() => {
                        let hint = '';
                        switch(answer) {
                            case 'B':
                                hint = '虽然层次化设计确实有助于管理和性能，但这不是主要作用，主要作用是实现层次化路由。';
                                break;
                            case 'C':
                                hint = '故障排查是层次化设计的副作用，不是主要作用。';
                                break;
                            case 'D':
                                hint = '节省资源是结果，不是主要作用。主要作用是实现层次化路由选择。';
                                break;
                        }
                        alert('❌ 答案不正确！\n\n提示：' + hint + '\n\n记住：连续地址空间的主要作用是支持层次化路由选择！');
                    }, 500);
                }
            });
        });

        // 绘制网络节点
        function drawNetworkNode(x, y, label, type, active = false) {
            ctx.save();

            const colors = {
                'headquarters': '#fd79a8',
                'router': '#667eea',
                'branch': '#00d4aa'
            };

            // 节点背景
            ctx.fillStyle = active ? colors[type] : '#bdc3c7';
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, Math.PI * 2);
            ctx.fill();

            if (active) {
                // 发光效果
                ctx.shadowColor = colors[type];
                ctx.shadowBlur = 15;
                ctx.beginPath();
                ctx.arc(x, y, 30, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }

            // 节点图标
            const icons = {
                'headquarters': '🏢',
                'router': '🔀',
                'branch': '🏪'
            };

            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(icons[type], x, y);

            // 标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 50);

            ctx.restore();
        }

        // 绘制连接线
        function drawConnection(fromX, fromY, toX, toY, active = false, label = '') {
            ctx.save();

            ctx.strokeStyle = active ? '#667eea' : '#bdc3c7';
            ctx.lineWidth = active ? 4 : 2;

            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 连接标签
            if (label && active) {
                const midX = (fromX + toX) / 2;
                const midY = (fromY + toY) / 2;

                ctx.fillStyle = '#667eea';
                ctx.font = '10px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(label, midX, midY - 5);
            }

            ctx.restore();
        }

        // 绘制路由表
        function drawRoutingTable(x, y, routes, title) {
            ctx.save();

            // 表格背景
            const tableWidth = 180;
            const tableHeight = 20 + routes.length * 25;

            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(x, y, tableWidth, tableHeight);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, tableWidth, tableHeight);

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(title, x + tableWidth/2, y + 15);

            // 路由条目
            ctx.font = '10px Microsoft YaHei';
            ctx.textAlign = 'left';
            routes.forEach((route, index) => {
                ctx.fillText(route, x + 5, y + 35 + index * 20);
            });

            ctx.restore();
        }

        // 层次化结构演示
        function startHierarchicalDemo() {
            currentDemo = 'hierarchical';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const routingIndicator = document.getElementById('routingIndicator');
            routingIndicator.textContent = '🏗️ 层次化路由结构演示';

            animateHierarchical();
        }

        function animateHierarchical() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            const step = Math.floor(animationStep / 60);

            // 总部
            if (step >= 0) {
                drawNetworkNode(centerX, centerY - 150, '总部', 'headquarters', true);
            }

            // 路由器
            if (step >= 1) {
                drawNetworkNode(centerX, centerY - 50, '路由器', 'router', true);
                drawConnection(centerX, centerY - 120, centerX, centerY - 80, true);
            }

            // 分支机构
            if (step >= 2) {
                const branches = [
                    {x: centerX - 150, y: centerY + 50, label: '分支1'},
                    {x: centerX - 50, y: centerY + 50, label: '分支2'},
                    {x: centerX + 50, y: centerY + 50, label: '分支3'},
                    {x: centerX + 150, y: centerY + 50, label: '分支4'}
                ];

                branches.forEach(branch => {
                    drawNetworkNode(branch.x, branch.y, branch.label, 'branch', true);
                    drawConnection(centerX, centerY - 20, branch.x, branch.y - 30, true);
                });
            }

            // 路由表
            if (step >= 3) {
                // 总部路由表
                drawRoutingTable(50, 50, [
                    '***********/16 → 路由器',
                    '其他网络 → 外部'
                ], '总部路由表');

                // 路由器路由表
                drawRoutingTable(centerX + 200, centerY - 100, [
                    '***********/24 → 分支1',
                    '***********/24 → 分支2',
                    '***********/24 → 分支3',
                    '***********/24 → 分支4',
                    '0.0.0.0/0 → 总部'
                ], '路由器路由表');
            }

            // 完成说明
            if (step >= 4) {
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('✅ 层次化路由：每个节点只需了解局部路由信息', centerX, centerY + 150);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateHierarchical);
        }

        // 地址聚合演示
        function showAddressAggregation() {
            currentDemo = 'aggregation';
            if (animationId) cancelAnimationFrame(animationId);

            const routingIndicator = document.getElementById('routingIndicator');
            routingIndicator.textContent = '📍 地址聚合演示：多个子网聚合为超网';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 绘制聚合前后对比
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('地址聚合示例', centerX, 50);

            // 聚合前
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillText('聚合前（4条路由）', centerX - 200, 100);

            const beforeRoutes = [
                '***********/24',
                '***********/24',
                '***********/24',
                '***********/24'
            ];

            beforeRoutes.forEach((route, index) => {
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(centerX - 300, 120 + index * 40, 200, 30);
                ctx.fillStyle = '#fff';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(route, centerX - 200, 140 + index * 40);
            });

            // 箭头
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(centerX - 80, centerY);
            ctx.lineTo(centerX - 20, centerY);
            ctx.stroke();

            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(centerX - 20, centerY);
            ctx.lineTo(centerX - 35, centerY - 10);
            ctx.moveTo(centerX - 20, centerY);
            ctx.lineTo(centerX - 35, centerY + 10);
            ctx.stroke();

            // 聚合后
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('聚合后（1条路由）', centerX + 200, 100);

            ctx.fillStyle = '#00d4aa';
            ctx.fillRect(centerX + 100, centerY - 15, 200, 30);
            ctx.fillStyle = '#fff';
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('***********/22', centerX + 200, centerY + 5);

            // 说明
            ctx.fillStyle = '#667eea';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('4个/24子网聚合为1个/22超网', centerX, centerY + 80);
            ctx.fillText('路由表条目减少75%！', centerX, centerY + 100);
        }

        // 路由对比演示
        function showRoutingComparison() {
            currentDemo = 'comparison';
            if (animationId) cancelAnimationFrame(animationId);

            const routingIndicator = document.getElementById('routingIndicator');
            routingIndicator.textContent = '🛣️ 层次化 vs 平面化路由对比';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('路由方式对比', centerX, 50);

            // 平面化路由
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillStyle = '#ff6b6b';
            ctx.textAlign = 'center';
            ctx.fillText('❌ 平面化路由', centerX - 200, 100);

            drawRoutingTable(centerX - 300, 120, [
                '192.168.1.1/32',
                '192.168.1.2/32',
                '192.168.1.3/32',
                '192.168.2.1/32',
                '192.168.2.2/32',
                '192.168.3.1/32',
                '... 更多主机路由'
            ], '路由表（数千条）');

            // 层次化路由
            ctx.fillStyle = '#00d4aa';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 层次化路由', centerX + 200, 100);

            drawRoutingTable(centerX + 100, 120, [
                '***********/16',
                '10.0.0.0/8',
                '172.16.0.0/12',
                '0.0.0.0/0'
            ], '路由表（几条）');

            // 对比说明
            ctx.fillStyle = '#667eea';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('层次化路由大大减少了路由表大小', centerX, centerY + 120);
            ctx.fillText('提高了路由查找效率和网络可扩展性', centerX, centerY + 140);
        }

        // 概念演示
        function showConceptDemo(concept) {
            currentDemo = concept;
            if (animationId) cancelAnimationFrame(animationId);

            const routingIndicator = document.getElementById('routingIndicator');
            const descriptions = {
                'hierarchical': '🏗️ 层次化结构：分层管理网络，局部路由信息',
                'addressing': '📍 地址聚合：连续地址空间，子网超网表示',
                'routing': '🛣️ 路由简化：减少路由条目，提高效率',
                'scalability': '📈 可扩展性：支持网络增长，避免路由爆炸'
            };
            routingIndicator.textContent = descriptions[concept];

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(descriptions[concept], centerX, centerY);
        }

        // 重置演示
        function resetDemo() {
            if (animationId) cancelAnimationFrame(animationId);
            currentDemo = 'none';
            animationStep = 0;

            const routingIndicator = document.getElementById('routingIndicator');
            routingIndicator.textContent = '🎯 点击按钮开始演示';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('选择上方按钮查看层次化路由演示', centerX, centerY);
        }

        // 重新答题功能
        function reviewQuestion() {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            document.getElementById('progressFill').style.width = '0%';

            document.querySelector('.question-box').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            setTimeout(() => {
                document.querySelector('.question-box').classList.add('pulse');
                setTimeout(() => {
                    document.querySelector('.question-box').classList.remove('pulse');
                }, 2000);
            }, 500);
        }

        // 显示总结
        function showSummary() {
            const summary = `
🎯 层次化路由选择学习总结

✅ 正确答案：A - 层次化路由选择

📚 核心概念：
• 层次化路由是指路由器只需了解局部路由信息
• 不需要知道所有网络的详细路由信息
• 通过连续地址空间和子网/超网实现

🏗️ 层次化路由的关键要素：

📍 地址聚合：
• 连续的地址空间可以聚合为更大的网络前缀
• 多个/24子网可以聚合为一个/22超网
• 例：***********/24 + ***********/24 → ***********/22

🛣️ 路由简化：
• 上层路由器只需要聚合后的路由信息
• 大大减少路由表的条目数量
• 提高路由查找效率

📈 可扩展性：
• 避免路由表随网络规模爆炸性增长
• 支持大规模网络的部署
• 网络故障影响局部化

🔍 选项分析：
✅ A. 层次化路由选择 - 正确
   这是连续地址空间的主要作用

❌ B. 易于管理和性能优化 - 错误
   这是副作用，不是主要作用

❌ C. 基于故障排查 - 错误
   故障排查是间接好处，不是主要目的

❌ D. 使用较少的资源 - 错误
   节省资源是结果，不是主要作用

🧠 记忆技巧：
• "连续地址做聚合，层次路由是目标"
• "局部信息就够用，网络扩展不用愁"
• 关键词：连续地址、子网超网、路由聚合

⚡ 考试要点：
• 看到"连续地址空间"、"子网超网" → 层次化路由选择
• 主要作用 vs 副作用的区分很重要
• 层次化路由需要配合层次化地址编码

🌐 实际应用：
• 企业网络：总部管理分支机构网络
• 互联网：ISP使用CIDR进行路由聚合
• 数据中心：层次化网络架构设计

🔑 关键理解：
层次化路由选择的核心是让每个路由器只需要了解
其管辖范围内的路由信息，而连续的地址空间和
子网/超网技术正是实现这一目标的关键手段。

🎉 恭喜掌握层次化路由选择知识！
            `;

            alert(summary);
        }

        // 添加CSS动画类
        const style = document.createElement('style');
        style.textContent = `
            .pulse {
                animation: pulse 1s ease-in-out 3;
            }

            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.02);
                    box-shadow: 0 25px 50px rgba(255, 107, 107, 0.4);
                }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的欢迎提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                welcome.innerHTML = `
                    <h3>🌟 欢迎来到层次化路由学习世界！</h3>
                    <p>让我们一起探索网络层次化设计的智慧</p>
                    <button onclick="this.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 15px;
                        margin-top: 15px;
                        cursor: pointer;
                    ">开始学习 🚀</button>
                `;
                document.body.appendChild(welcome);
            }, 1000);
        });

        // 初始化
        resetDemo();
    </script>
</body>
</html>
