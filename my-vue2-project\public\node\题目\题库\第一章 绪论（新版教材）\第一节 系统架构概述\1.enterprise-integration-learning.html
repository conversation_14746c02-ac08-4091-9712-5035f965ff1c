<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业应用集成 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .integration-canvas {
            width: 100%;
            height: 400px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .integration-canvas:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateY(-5px);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            color: white;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .option.selected {
            border-color: #fff;
            background: rgba(255,255,255,0.4);
        }

        .option.correct {
            border-color: #4CAF50;
            background: rgba(76,175,80,0.3);
        }

        .option.wrong {
            border-color: #f44336;
            background: rgba(244,67,54,0.3);
        }

        .explanation {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            display: none;
        }

        .explanation.show {
            display: block;
            animation: fadeIn 0.5s ease-out;
        }

        .control-panel {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 1s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .floating-element {
            position: absolute;
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">企业应用集成</h1>
            <p class="subtitle">通过动画和交互学习企业系统集成的核心概念</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">什么是企业应用集成？</h2>
            <p style="text-align: center; font-size: 1.1rem; color: #666; margin-bottom: 30px;">
                点击下方画布，观看动画演示企业应用集成的工作原理
            </p>
            <canvas id="integrationCanvas" class="integration-canvas"></canvas>
            <div class="control-panel">
                <button class="btn" onclick="startAnimation()">开始演示</button>
                <button class="btn" onclick="resetAnimation()">重置</button>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title">知识测试</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="quiz-question">
                企业应用集成是一个战略意义上的方法，它从服务和信息角度将多个信息系统绑定在一起，提供实时交换信息和影响流程的能力。<strong>（请作答此空）</strong>提供企业之间的信息共享能力，<strong>（界面集成）</strong>在用户使用角度能够对集成系统产生一个"整体"的感觉。
            </div>
            <div class="options">
                <div class="option" onclick="selectOption(this, 'A')">
                    <strong>A. API集成</strong><br>
                    通过应用程序接口连接系统
                </div>
                <div class="option" onclick="selectOption(this, 'B')">
                    <strong>B. 数据集成</strong><br>
                    整合不同系统的数据资源
                </div>
                <div class="option" onclick="selectOption(this, 'C')">
                    <strong>C. 界面集成</strong><br>
                    统一用户界面和入口
                </div>
                <div class="option" onclick="selectOption(this, 'D')">
                    <strong>D. 过程集成</strong><br>
                    整合业务流程和工作流
                </div>
            </div>
            <div class="explanation" id="explanation">
                <h3>📚 知识解析</h3>
                <p><strong>正确答案：B. 数据集成</strong></p>
                <p>根据题目描述，第一个空白处强调的是"提供企业之间的信息共享能力"，这正是<strong>数据集成</strong>的核心功能。</p>
                <br>
                <h4>四种集成类型详解：</h4>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li><strong>数据集成</strong>：整合不同系统的数据，实现信息共享</li>
                    <li><strong>界面集成</strong>：统一用户界面，提供整体感觉</li>
                    <li><strong>过程集成</strong>：整合业务流程，实现端到端管理</li>
                    <li><strong>API集成</strong>：通过接口实现系统间通信</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('integrationCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let animationStep = 0;
        let selectedAnswer = null;

        // 设置画布尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 系统组件类
        class System {
            constructor(x, y, name, color) {
                this.x = x;
                this.y = y;
                this.name = name;
                this.color = color;
                this.scale = 1;
                this.opacity = 1;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.opacity;
                ctx.translate(this.x, this.y);
                ctx.scale(this.scale, this.scale);
                
                // 绘制系统框
                ctx.fillStyle = this.color;
                ctx.fillRect(-50, -30, 100, 60);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(-50, -30, 100, 60);
                
                // 绘制系统名称
                ctx.fillStyle = '#fff';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(this.name, 0, 5);
                
                ctx.restore();
            }

            animate(targetScale, targetOpacity) {
                this.scale += (targetScale - this.scale) * 0.1;
                this.opacity += (targetOpacity - this.opacity) * 0.1;
            }
        }

        // 数据流类
        class DataFlow {
            constructor(startX, startY, endX, endY) {
                this.startX = startX;
                this.startY = startY;
                this.endX = endX;
                this.endY = endY;
                this.progress = 0;
                this.particles = [];
            }

            update() {
                this.progress += 0.02;
                if (this.progress > 1) this.progress = 0;

                // 添加粒子
                if (Math.random() < 0.3) {
                    this.particles.push({
                        x: this.startX,
                        y: this.startY,
                        progress: 0,
                        life: 1
                    });
                }

                // 更新粒子
                this.particles.forEach(particle => {
                    particle.progress += 0.02;
                    particle.life -= 0.01;
                    particle.x = this.startX + (this.endX - this.startX) * particle.progress;
                    particle.y = this.startY + (this.endY - this.startY) * particle.progress;
                });

                // 移除死亡粒子
                this.particles = this.particles.filter(p => p.life > 0);
            }

            draw() {
                // 绘制连接线
                ctx.strokeStyle = 'rgba(102, 126, 234, 0.5)';
                ctx.lineWidth = 3;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(this.startX, this.startY);
                ctx.lineTo(this.endX, this.endY);
                ctx.stroke();
                ctx.setLineDash([]);

                // 绘制粒子
                this.particles.forEach(particle => {
                    ctx.save();
                    ctx.globalAlpha = particle.life;
                    ctx.fillStyle = '#667eea';
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, 4, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                });
            }
        }

        // 初始化系统和数据流
        const systems = [
            new System(150, 100, 'CRM系统', '#ff6b6b'),
            new System(450, 100, 'ERP系统', '#4ecdc4'),
            new System(750, 100, 'HR系统', '#45b7d1'),
            new System(150, 300, '财务系统', '#96ceb4'),
            new System(450, 300, '数据集成层', '#feca57'),
            new System(750, 300, '供应链系统', '#ff9ff3')
        ];

        const dataFlows = [
            new DataFlow(200, 100, 400, 300), // CRM到集成层
            new DataFlow(450, 130, 450, 270), // ERP到集成层
            new DataFlow(700, 100, 500, 300), // HR到集成层
            new DataFlow(200, 300, 400, 300), // 财务到集成层
            new DataFlow(500, 300, 700, 300)  // 集成层到供应链
        ];

        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景网格
            ctx.strokeStyle = 'rgba(255,255,255,0.1)';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('企业应用集成架构图', canvas.width / 2, 40);

            // 绘制数据流
            dataFlows.forEach(flow => {
                flow.update();
                flow.draw();
            });

            // 绘制系统
            systems.forEach(system => {
                system.draw();
            });

            // 绘制集成类型说明
            if (animationStep > 100) {
                drawIntegrationTypes();
            }
        }

        function drawIntegrationTypes() {
            const types = [
                { name: '数据集成', desc: '信息共享', color: '#feca57', x: 100, y: canvas.height - 80 },
                { name: '界面集成', desc: '统一入口', color: '#ff6b6b', x: 300, y: canvas.height - 80 },
                { name: '过程集成', desc: '流程管理', color: '#4ecdc4', x: 500, y: canvas.height - 80 },
                { name: 'API集成', desc: '接口连接', color: '#45b7d1', x: 700, y: canvas.height - 80 }
            ];

            types.forEach((type, index) => {
                const alpha = Math.max(0, (animationStep - 100 - index * 20) / 50);
                ctx.save();
                ctx.globalAlpha = alpha;
                
                ctx.fillStyle = type.color;
                ctx.fillRect(type.x - 40, type.y - 20, 80, 40);
                
                ctx.fillStyle = '#fff';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(type.name, type.x, type.y - 5);
                ctx.fillText(type.desc, type.x, type.y + 10);
                
                ctx.restore();
            });
        }

        function animate() {
            animationStep++;
            
            // 系统动画
            systems.forEach((system, index) => {
                const delay = index * 20;
                if (animationStep > delay) {
                    system.animate(1.1, 1);
                }
            });

            drawScene();
            animationId = requestAnimationFrame(animate);
        }

        function startAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animationStep = 0;
            animate();
        }

        function resetAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animationStep = 0;
            systems.forEach(system => {
                system.scale = 1;
                system.opacity = 1;
            });
            drawScene();
        }

        function selectOption(element, answer) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'wrong');
            });

            // 标记当前选择
            element.classList.add('selected');
            selectedAnswer = answer;

            // 显示结果
            setTimeout(() => {
                showAnswer();
            }, 500);
        }

        function showAnswer() {
            const options = document.querySelectorAll('.option');
            const explanation = document.getElementById('explanation');
            const progressFill = document.getElementById('progressFill');

            // 显示正确答案
            options.forEach((opt, index) => {
                const answers = ['A', 'B', 'C', 'D'];
                if (answers[index] === 'B') {
                    opt.classList.add('correct');
                } else if (answers[index] === selectedAnswer && selectedAnswer !== 'B') {
                    opt.classList.add('wrong');
                }
            });

            // 显示解析
            explanation.classList.add('show');
            
            // 更新进度条
            progressFill.style.width = selectedAnswer === 'B' ? '100%' : '50%';
        }

        // 初始化
        drawScene();
        
        // 画布点击事件
        canvas.addEventListener('click', startAnimation);
    </script>
</body>
</html>
