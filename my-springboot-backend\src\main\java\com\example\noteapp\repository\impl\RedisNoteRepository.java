package com.example.noteapp.repository.impl;

import com.example.noteapp.model.Note;
import com.example.noteapp.repository.NoteRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

@Repository
@ConditionalOnBean(RedisConnectionFactory.class)
public class RedisNoteRepository implements NoteRepository {

    private static final Logger logger = LoggerFactory.getLogger(RedisNoteRepository.class);
    private static final String KEY_PREFIX = "note:";
    private static final String ID_KEY = "note:id";
    private static final String NOTES_KEY = "notes";

    private final RedisTemplate<String, Object> redisTemplate;
    private final AtomicLong idCounter;

    @Autowired
    public RedisNoteRepository(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        
        // 初始化 ID 计数器
        Long maxId = redisTemplate.opsForValue().get(ID_KEY) != null ? 
                     (Long) redisTemplate.opsForValue().get(ID_KEY) : 0L;
        this.idCounter = new AtomicLong(maxId != null ? maxId : 0L);
        logger.info("使用 Redis 存储作为笔记仓库");
    }

    @Override
    public List<Note> findAll() {
        List<Object> noteIds = redisTemplate.opsForList().range(NOTES_KEY, 0, -1);
        List<Note> notes = new ArrayList<>();
        
        if (noteIds != null) {
            for (Object id : noteIds) {
                Optional<Note> note = findById(Long.valueOf(id.toString()));
                note.ifPresent(notes::add);
            }
        }
        
        return notes;
    }

    @Override
    public Optional<Note> findById(Long id) {
        Note note = (Note) redisTemplate.opsForValue().get(KEY_PREFIX + id);
        return Optional.ofNullable(note);
    }

    @Override
    public Note save(Note note) {
        if (note.getId() == null) {
            // 新笔记，分配新 ID
            Long newId = idCounter.incrementAndGet();
            note.setId(newId);
            
            // 更新最大 ID
            redisTemplate.opsForValue().set(ID_KEY, newId);
            
            // 将 ID 添加到笔记列表
            redisTemplate.opsForList().rightPush(NOTES_KEY, newId);
        }
        
        // 保存笔记
        redisTemplate.opsForValue().set(KEY_PREFIX + note.getId(), note);
        
        return note;
    }

    @Override
    public void deleteById(Long id) {
        // 删除笔记
        redisTemplate.delete(KEY_PREFIX + id);
        
        // 从列表中移除 ID
        redisTemplate.opsForList().remove(NOTES_KEY, 0, id);
    }

    @Override
    public boolean existsById(Long id) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(KEY_PREFIX + id));
    }
} 