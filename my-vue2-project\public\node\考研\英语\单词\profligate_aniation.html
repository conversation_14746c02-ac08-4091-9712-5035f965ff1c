<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词 Profligate 学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        header {
            background-color: #4CAF50;
            color: white;
            padding: 20px 0;
            width: 100%;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .container {
            background-color: #fff;
            margin: 20px;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            box-sizing: border-box;
        }
        h1, h2 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 25px;
        }
        .word-display {
            font-size: 3em;
            font-weight: bold;
            color: #2196F3;
            text-align: center;
            margin: 30px 0;
            position: relative;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        .word-display:hover {
            transform: translateY(-5px);
        }
        .translation, .explanation {
            font-size: 1.2em;
            line-height: 1.8;
            margin-bottom: 20px;
            text-align: justify;
        }
        .explanation strong {
            color: #FF5722;
        }
        .affix-section {
            margin-top: 30px;
            border-top: 2px solid #eee;
            padding-top: 20px;
        }
        .affix-item {
            margin-bottom: 20px;
        }
        .affix-item h3 {
            color: #673AB7;
            font-size: 1.5em;
            margin-bottom: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .affix-item h3:hover {
            text-decoration: underline;
        }
        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #fcfcfc;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
        }
        .interactive-section {
            margin-top: 30px;
            border-top: 2px solid #eee;
            padding-top: 20px;
            text-align: center;
        }
        .interactive-section button {
            background-color: #00BCD4;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.1em;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin: 10px;
        }
        .interactive-section button:hover {
            background-color: #0097A7;
            transform: translateY(-2px);
        }
        .example-sentences {
            margin-top: 30px;
            border-top: 2px solid #eee;
            padding-top: 20px;
        }
        .example-sentences h2 {
            color: #4CAF50;
        }
        .example-sentences ul {
            list-style: none;
            padding: 0;
        }
        .example-sentences li {
            background-color: #e8f5e9;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 5px solid #8BC34A;
            border-radius: 5px;
        }
        footer {
            margin-top: 40px;
            padding: 20px;
            text-align: center;
            color: #777;
            font-size: 0.9em;
            width: 100%;
            background-color: #e0e0e0;
        }
    </style>
</head>
<body>
    <header>
        <h1>考研单词学习：Profligate</h1>
    </header>

    <div class="container">
        <h2 class="word-display">Profligate</h2>
        <p class="translation">翻译：adj. 挥霍的，浪费的；n. 挥霍者，浪子</p>
        
        <div class="explanation">
            <h2>词义解释</h2>
            <p><strong>Profligate (adj.) 挥霍的，浪费的：</strong> 指一个人在金钱、资源或时间上极其浪费，不加节制，通常带有道德上的贬义，暗示这种挥霍可能导致堕落或不负责任的行为。</p>
            <p><strong>Profligate (n.) 挥霍者，浪子：</strong> 指那些过着奢侈生活，沉迷于享乐，不计后果地浪费财富的人。</p>
            <p>这个词强调的是“过度”和“不负责任”的浪费，不仅仅是简单的“花钱多”，而是到了“败家”的地步。</p>
        </div>

        <div class="affix-section">
            <h2>词缀故事与动画演示</h2>
            <div class="affix-item">
                <h3>前缀：pro-</h3>
                <div class="canvas-container">
                    <canvas id="proCanvas" width="600" height="200"></canvas>
                </div>
                <p class="explanation">
                    <strong>词缀故事：</strong> 想象一下，有一个小喷泉，它叫做“pro-”。“pro-”的意思是“向前”、“向外”。这个喷泉的水总是不断地“向前”喷涌，“向外”流淌，从来不停歇。这就像“profligate”中的“pro-”，暗示着金钱或资源像喷泉一样“向前”流出，而且流得很“多”。
                </p>
                <div class="interactive-section">
                    <button onclick="animatePro()">开始演示 'pro-'</button>
                </div>
            </div>

            <div class="affix-item">
                <h3>词根：flig-</h3>
                <div class="canvas-container">
                    <canvas id="fligCanvas" width="600" height="200"></canvas>
                </div>
                <p class="explanation">
                    <strong>词缀故事：</strong> 喷泉的水流出去之后，它就“飞”走了，或者说是“击”中了地面，然后四处溅开。这里的“flig-”来源于拉丁语的“fligere”，意思是“to strike”（打击，击打），引申为“to throw down”（扔掉，抛弃）。水被“抛弃”到地面，溅得到处都是，完全没有被收集利用。这形象地描述了“浪费”的行为。
                </p>
                <div class="interactive-section">
                    <button onclick="animateFlig()">开始演示 'flig-'</button>
                </div>
            </div>

            <div class="affix-item">
                <h3>后缀：-ate</h3>
                <div class="canvas-container">
                    <canvas id="ateCanvas" width="600" height="200"></canvas>
                </div>
                <p class="explanation">
                    <strong>词缀故事：：</strong> 最终，所有这些“向前喷涌”（pro-）和“抛弃溅开”（flig-）的水，形成了一个状态——“ate”。“-ate”作为一个后缀，通常表示“……的”、“使……成为”或者动词化。在这里，它使整个单词变成了形容词或名词，描述了“挥霍的”这种状态或“挥霍者”这样的人。
                </p>
                <div class="interactive-section">
                    <button onclick="animateAte()">开始演示 '-ate'</button>
                </div>
            </div>
        </div>

        <div class="example-sentences">
            <h2>例句：加深理解</h2>
            <ul>
                <li>The young heir quickly squandered his inheritance with a <strong>profligate</strong> lifestyle. (这位年轻的继承人过着挥霍无度的生活，很快就挥霍掉了他的遗产。)</li>
                <li>Her <strong>profligate</strong> spending habits led her deeply into debt. (她挥霍无度的消费习惯使她债台高筑。)</li>
                <li>He was criticized for his <strong>profligate</strong> use of natural resources. (他因对自然资源的滥用而受到批评。)</li>
                <li>The city council was accused of <strong>profligate</strong> spending on unnecessary projects. (市议会被指控在不必要的项目上挥霍。)</li>
            </ul>
        </div>
    </div>

    <footer>
        <p>&copy; 2024 考研单词专家. 保留所有权利.</p>
    </footer>

    <script>
        // Canvas动画函数 - pro-
        function animatePro() {
            const canvas = document.getElementById('proCanvas');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let waterX = 0;
            let waterY = height / 2;
            let radius = 10;
            let flowSpeed = 3;
            let animationFrameId;

            function draw() {
                ctx.clearRect(0, 0, width, height);

                // Draw source
                ctx.fillStyle = '#2196F3';
                ctx.beginPath();
                ctx.arc(50, height / 2, 20, 0, Math.PI * 2);
                ctx.fill();
                ctx.font = '20px Arial';
                ctx.fillText('源头', 30, height / 2 + 30);

                // Draw flowing water
                ctx.fillStyle = '#03A9F4';
                ctx.beginPath();
                ctx.arc(waterX, waterY, radius, 0, Math.PI * 2);
                ctx.fill();

                waterX += flowSpeed;
                if (waterX - radius > width) {
                    waterX = 0; // Reset when out of bounds
                }
                
                animationFrameId = requestAnimationFrame(draw);
            }
            
            // Stop any existing animation before starting a new one
            if (window.proAnimation) {
                cancelAnimationFrame(window.proAnimation);
            }
            window.proAnimation = animationFrameId;
            draw();
        }

        // Canvas动画函数 - flig-
        function animateFlig() {
            const canvas = document.getElementById('fligCanvas');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let particles = [];
            const numParticles = 30;
            const originX = width / 2;
            const originY = height / 2;
            let animationFrameId;

            // Create initial particles
            for (let i = 0; i < numParticles; i++) {
                particles.push({
                    x: originX,
                    y: originY,
                    vx: (Math.random() - 0.5) * 8, // Random horizontal velocity
                    vy: (Math.random() - 0.5) * 8, // Random vertical velocity
                    radius: Math.random() * 3 + 2,
                    alpha: 1
                });
            }

            function draw() {
                ctx.clearRect(0, 0, width, height);

                // Draw source
                ctx.fillStyle = '#FF5722';
                ctx.beginPath();
                ctx.arc(originX, originY, 20, 0, Math.PI * 2);
                ctx.fill();
                ctx.font = '20px Arial';
                ctx.fillText('击打/抛弃', originX - 40, originY + 30);

                ctx.fillStyle = '#FF9800';
                for (let i = 0; i < particles.length; i++) {
                    let p = particles[i];
                    ctx.globalAlpha = p.alpha;
                    ctx.beginPath();
                    ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
                    ctx.fill();

                    p.x += p.vx;
                    p.y += p.vy;
                    p.alpha -= 0.02; // Fade out

                    // Remove faded particles and add new ones
                    if (p.alpha <= 0) {
                        particles.splice(i, 1);
                        i--;
                        particles.push({
                            x: originX,
                            y: originY,
                            vx: (Math.random() - 0.5) * 8,
                            vy: (Math.random() - 0.5) * 8,
                            radius: Math.random() * 3 + 2,
                            alpha: 1
                        });
                    }
                }
                ctx.globalAlpha = 1; // Reset alpha

                animationFrameId = requestAnimationFrame(draw);
            }

            // Stop any existing animation before starting a new one
            if (window.fligAnimation) {
                cancelAnimationFrame(window.fligAnimation);
            }
            window.fligAnimation = animationFrameId;
            draw();
        }

        // Canvas动画函数 - ate
        function animateAte() {
            const canvas = document.getElementById('ateCanvas');
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let texts = ['Profligate', '挥霍的', '浪费的', '浪子'];
            let currentTextIndex = 0;
            let fadeIn = true;
            let alpha = 0;
            let animationFrameId;

            function draw() {
                ctx.clearRect(0, 0, width, height);

                ctx.font = 'bold 40px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillStyle = `rgba(103, 58, 183, ${alpha})`;
                ctx.fillText(texts[currentTextIndex], width / 2, height / 2);

                if (fadeIn) {
                    alpha += 0.02;
                    if (alpha >= 1) {
                        fadeIn = false;
                        setTimeout(() => {
                            fadeIn = false; // Ensure it stays false while fading out
                        }, 1000); // Display for 1 second
                    }
                } else {
                    alpha -= 0.02;
                    if (alpha <= 0) {
                        fadeIn = true;
                        currentTextIndex = (currentTextIndex + 1) % texts.length;
                    }
                }
                animationFrameId = requestAnimationFrame(draw);
            }

            // Stop any existing animation before starting a new one
            if (window.ateAnimation) {
                cancelAnimationFrame(window.ateAnimation);
            }
            window.ateAnimation = animationFrameId;
            draw();
        }
    </script>
</body>
</html>