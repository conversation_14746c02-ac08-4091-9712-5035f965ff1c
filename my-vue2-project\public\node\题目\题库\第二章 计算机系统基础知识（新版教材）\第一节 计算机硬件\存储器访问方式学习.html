<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储器访问方式 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        }

        .explanation {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .explanation p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .quiz-question {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .quiz-option {
            padding: 10px 15px;
            background: white;
            border: 2px solid transparent;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .quiz-option.selected {
            background: #667eea;
            color: white;
        }

        .quiz-option.correct {
            background: #28a745;
            color: white;
        }

        .quiz-option.incorrect {
            background: #dc3545;
            color: white;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .score-display {
            text-align: center;
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="floating">🧠 存储器访问方式学习</h1>
            <p>通过动画和交互了解四种存储器访问方式</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="content-section">
            <h2 class="section-title">📚 知识概览</h2>
            <div class="explanation">
                <h3>什么是存储器访问方式？</h3>
                <p>存储器访问方式是指<span class="highlight">根据所访问的内容来决定要访问的存储单元</span>的方法。不同的存储器采用不同的访问方式来优化性能。</p>
                <p>计算机系统中主要有四种访问方式：</p>
                <ul style="margin-left: 20px; color: #666;">
                    <li><strong>顺序存取</strong> - 按线性顺序访问（如磁带）</li>
                    <li><strong>直接存取</strong> - 直接移动到目标位置（如磁盘）</li>
                    <li><strong>随机存取</strong> - 任意位置等时间访问（如内存）</li>
                    <li><strong>相联存取</strong> - 根据内容而非地址访问（如Cache）</li>
                </ul>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎮 交互式演示</h2>
            <div class="canvas-container">
                <canvas id="demoCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="demonstrateSequential()">顺序存取</button>
                <button class="btn btn-primary" onclick="demonstrateDirect()">直接存取</button>
                <button class="btn btn-secondary" onclick="demonstrateRandom()">随机存取</button>
                <button class="btn btn-secondary" onclick="demonstrateAssociative()">相联存取</button>
                <button class="btn btn-primary" onclick="resetDemo()">重置</button>
            </div>
            <div id="demoExplanation" class="explanation" style="display: none;">
                <h3 id="demoTitle">演示说明</h3>
                <p id="demoText">点击上方按钮开始演示</p>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">📖 详细知识解析</h2>

            <div class="explanation">
                <h3>1. 顺序存取 (Sequential Access)</h3>
                <p><span class="highlight">特点</span>：数据按线性顺序组织，必须按特定顺序访问</p>
                <p><span class="highlight">典型应用</span>：磁带存储器</p>
                <p><span class="highlight">访问时间</span>：可变，取决于数据在序列中的位置</p>
                <p><span class="highlight">优缺点</span>：成本低，但访问速度慢，不支持随机访问</p>
            </div>

            <div class="explanation">
                <h3>2. 直接存取 (Direct Access)</h3>
                <p><span class="highlight">特点</span>：每个数据块有唯一地址，可直接移动到目标位置</p>
                <p><span class="highlight">典型应用</span>：磁盘存储器（硬盘、光盘）</p>
                <p><span class="highlight">访问时间</span>：可变，但比顺序存取快</p>
                <p><span class="highlight">优缺点</span>：支持跳跃访问，但仍需物理移动时间</p>
            </div>

            <div class="explanation">
                <h3>3. 随机存取 (Random Access)</h3>
                <p><span class="highlight">特点</span>：每个单元有独立地址和访问电路，访问时间相同</p>
                <p><span class="highlight">典型应用</span>：主存储器（RAM）</p>
                <p><span class="highlight">访问时间</span>：固定，与位置无关</p>
                <p><span class="highlight">优缺点</span>：访问速度快且一致，但成本较高</p>
            </div>

            <div class="explanation">
                <h3>4. 相联存取 (Associative Access) ⭐</h3>
                <p><span class="highlight">特点</span>：根据内容而非地址选择存储单元</p>
                <p><span class="highlight">典型应用</span>：Cache存储器（这是题目答案！）</p>
                <p><span class="highlight">访问时间</span>：固定，所有单元同时比较</p>
                <p><span class="highlight">优缺点</span>：查找速度极快，但硬件复杂，成本很高</p>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎯 互动测验</h2>
            <div class="quiz-section">
                <div class="quiz-question">
                    题目：计算机系统中，（ ）方式是根据所访问的内容来决定要访问的存储单元，常用在（ ）存储器中。
                </div>
                <div class="quiz-options" id="quizOptions">
                    <div class="quiz-option" onclick="selectOption(this, 'A')">A. DRAM</div>
                    <div class="quiz-option" onclick="selectOption(this, 'B')">B. Cache</div>
                    <div class="quiz-option" onclick="selectOption(this, 'C')">C. EEPROM</div>
                    <div class="quiz-option" onclick="selectOption(this, 'D')">D. CD-ROM</div>
                </div>
                <button class="btn btn-primary" onclick="checkAnswer()" id="checkBtn" style="display: none;">检查答案</button>
                <div id="quizResult" style="display: none; margin-top: 15px;"></div>
            </div>

            <div class="quiz-section" style="margin-top: 20px;">
                <div class="quiz-question">
                    练习题：以下哪种存储器采用顺序存取方式？
                </div>
                <div class="quiz-options" id="quiz2Options">
                    <div class="quiz-option" onclick="selectOption2(this, 'A')">A. 磁带存储器</div>
                    <div class="quiz-option" onclick="selectOption2(this, 'B')">B. 硬盘存储器</div>
                    <div class="quiz-option" onclick="selectOption2(this, 'C')">C. 内存</div>
                    <div class="quiz-option" onclick="selectOption2(this, 'D')">D. Cache</div>
                </div>
                <button class="btn btn-primary" onclick="checkAnswer2()" id="check2Btn" style="display: none;">检查答案</button>
                <div id="quiz2Result" style="display: none; margin-top: 15px;"></div>
            </div>

            <div class="score-display" id="scoreDisplay">
                总分：<span id="totalScore">0</span> / 2
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎊 学习总结</h2>
            <div class="explanation">
                <h3>🔑 关键要点回顾</h3>
                <p>1. <strong>相联存取</strong>是根据<span class="highlight">内容</span>而非地址来访问数据的方式</p>
                <p>2. <strong>Cache存储器</strong>采用相联存取方式来快速查找数据</p>
                <p>3. 四种访问方式的速度排序：<span class="highlight">相联存取 ≈ 随机存取 > 直接存取 > 顺序存取</span></p>
                <p>4. 不同存储器根据用途和成本选择合适的访问方式</p>
            </div>

            <div class="explanation">
                <h3>💡 记忆技巧</h3>
                <p>• <strong>顺序存取</strong> = 磁带播放，必须按顺序</p>
                <p>• <strong>直接存取</strong> = CD播放，可以跳曲但需要时间</p>
                <p>• <strong>随机存取</strong> = 书架取书，任意位置时间相同</p>
                <p>• <strong>相联存取</strong> = 图书馆按内容找书，所有书同时检索</p>
            </div>

            <div class="controls">
                <button class="btn btn-secondary pulse" onclick="playAllDemos()">🎬 播放完整演示</button>
                <button class="btn btn-primary" onclick="resetAll()">🔄 重新开始学习</button>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('demoCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentDemo = null;

        // 初始化画布
        function initCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 40) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 40) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
        }

        // 绘制存储单元
        function drawStorageUnit(x, y, width, height, color, label, isActive = false) {
            ctx.fillStyle = isActive ? '#ff6b6b' : color;
            ctx.fillRect(x, y, width, height);
            
            ctx.strokeStyle = isActive ? '#ff4757' : '#333';
            ctx.lineWidth = isActive ? 3 : 1;
            ctx.strokeRect(x, y, width, height);
            
            ctx.fillStyle = isActive ? 'white' : '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + width/2, y + height/2 + 5);
        }

        // 绘制访问指针
        function drawPointer(x, y, color = '#667eea') {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(x, y - 10);
            ctx.lineTo(x - 8, y - 25);
            ctx.lineTo(x + 8, y - 25);
            ctx.closePath();
            ctx.fill();
            
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x, y - 10);
            ctx.lineTo(x, y - 30);
            ctx.stroke();
        }

        // 顺序存取演示
        function demonstrateSequential() {
            currentDemo = 'sequential';
            showExplanation('顺序存取 - 磁带存储器', 
                '数据按线性顺序存储，必须从头开始依次访问。就像播放磁带一样，要听第10首歌必须先经过前9首。访问时间取决于数据在序列中的位置。');
            
            let step = 0;
            const totalSteps = 8;
            
            function animate() {
                initCanvas();
                
                // 绘制磁带卷轴
                const tapeY = 200;
                const unitWidth = 80;
                
                for (let i = 0; i < totalSteps; i++) {
                    const x = 50 + i * unitWidth;
                    const isActive = i === step;
                    drawStorageUnit(x, tapeY, unitWidth - 10, 60, '#e1f5fe', `数据${i+1}`, isActive);
                }
                
                // 绘制读写头
                if (step < totalSteps) {
                    const headX = 50 + step * unitWidth + (unitWidth - 10) / 2;
                    drawPointer(headX, tapeY, '#ff6b6b');
                    
                    // 绘制移动轨迹
                    ctx.strokeStyle = '#ff6b6b';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.moveTo(50 + (unitWidth - 10) / 2, tapeY - 40);
                    ctx.lineTo(headX, tapeY - 40);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }
                
                // 绘制说明文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`正在访问第 ${step + 1} 个数据单元`, canvas.width / 2, 100);
                ctx.fillText('必须按顺序逐个访问，无法跳跃', canvas.width / 2, 120);
                
                step++;
                if (step <= totalSteps) {
                    setTimeout(() => {
                        animationId = requestAnimationFrame(animate);
                    }, 800);
                }
            }
            
            animate();
        }

        // 直接存取演示
        function demonstrateDirect() {
            currentDemo = 'direct';
            showExplanation('直接存取 - 磁盘存储器', 
                '每个数据块都有唯一地址，读写头可以直接移动到目标位置。就像CD播放器可以直接跳到任意曲目，但仍需要物理移动时间。');
            
            let step = 0;
            const targets = [2, 6, 1, 4, 7];
            
            function animate() {
                initCanvas();
                
                // 绘制磁盘扇区
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = 120;
                const sectors = 8;
                
                for (let i = 0; i < sectors; i++) {
                    const angle = (i * 2 * Math.PI) / sectors;
                    const x = centerX + Math.cos(angle) * radius - 30;
                    const y = centerY + Math.sin(angle) * radius - 20;
                    const isActive = step < targets.length && i === targets[step];
                    
                    ctx.fillStyle = isActive ? '#ff6b6b' : '#e8f5e8';
                    ctx.fillRect(x, y, 60, 40);
                    ctx.strokeStyle = isActive ? '#ff4757' : '#333';
                    ctx.lineWidth = isActive ? 3 : 1;
                    ctx.strokeRect(x, y, 60, 40);
                    
                    ctx.fillStyle = isActive ? 'white' : '#333';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`扇区${i}`, x + 30, y + 25);
                }
                
                // 绘制读写头轨迹
                if (step < targets.length) {
                    const targetAngle = (targets[step] * 2 * Math.PI) / sectors;
                    const headX = centerX + Math.cos(targetAngle) * (radius - 60);
                    const headY = centerY + Math.sin(targetAngle) * (radius - 60);
                    
                    // 绘制从中心到目标的线
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(headX, headY);
                    ctx.stroke();
                    
                    // 绘制读写头
                    ctx.fillStyle = '#667eea';
                    ctx.beginPath();
                    ctx.arc(headX, headY, 8, 0, 2 * Math.PI);
                    ctx.fill();
                }
                
                // 绘制说明文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                if (step < targets.length) {
                    ctx.fillText(`直接访问扇区 ${targets[step]}`, centerX, 50);
                    ctx.fillText('读写头直接移动到目标位置', centerX, 70);
                } else {
                    ctx.fillText('访问完成！可以直接跳转到任意扇区', centerX, 50);
                }
                
                step++;
                if (step <= targets.length) {
                    setTimeout(() => {
                        animationId = requestAnimationFrame(animate);
                    }, 1200);
                }
            }
            
            animate();
        }

        // 随机存取演示
        function demonstrateRandom() {
            currentDemo = 'random';
            showExplanation('随机存取 - 主存储器', 
                '每个存储单元都有独立的地址和访问电路，可以在相同时间内访问任意位置。就像书架上的书，每本都可以直接取出，访问时间相同。');
            
            let step = 0;
            const accessPattern = [3, 7, 1, 5, 2, 6, 0, 4];
            
            function animate() {
                initCanvas();
                
                // 绘制内存单元网格
                const rows = 2;
                const cols = 4;
                const unitWidth = 120;
                const unitHeight = 80;
                const startX = 100;
                const startY = 160;
                
                for (let i = 0; i < rows * cols; i++) {
                    const row = Math.floor(i / cols);
                    const col = i % cols;
                    const x = startX + col * unitWidth;
                    const y = startY + row * unitHeight;
                    const isActive = step < accessPattern.length && i === accessPattern[step];
                    
                    drawStorageUnit(x, y, unitWidth - 10, unitHeight - 10, '#fff3e0', `地址${i}`, isActive);
                    
                    // 绘制地址线
                    ctx.strokeStyle = isActive ? '#ff6b6b' : '#ccc';
                    ctx.lineWidth = isActive ? 3 : 1;
                    ctx.beginPath();
                    ctx.moveTo(x + (unitWidth - 10) / 2, y - 20);
                    ctx.lineTo(x + (unitWidth - 10) / 2, y);
                    ctx.stroke();
                }
                
                // 绘制地址总线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(startX, startY - 30);
                ctx.lineTo(startX + cols * unitWidth - 10, startY - 30);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(startX, startY + unitHeight + 20);
                ctx.lineTo(startX + cols * unitWidth - 10, startY + unitHeight + 20);
                ctx.stroke();
                
                // 绘制说明文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                if (step < accessPattern.length) {
                    ctx.fillText(`随机访问地址 ${accessPattern[step]} - 访问时间相同`, canvas.width / 2, 100);
                    ctx.fillText('所有单元都有独立的访问电路', canvas.width / 2, 120);
                } else {
                    ctx.fillText('随机访问完成！任意地址访问时间都相同', canvas.width / 2, 100);
                }
                
                step++;
                if (step <= accessPattern.length) {
                    setTimeout(() => {
                        animationId = requestAnimationFrame(animate);
                    }, 1000);
                }
            }
            
            animate();
        }

        // 相联存取演示
        function demonstrateAssociative() {
            currentDemo = 'associative';
            showExplanation('相联存取 - Cache存储器', 
                '根据存储内容而不是地址来选择存储单元。所有单元同时进行内容比较，找到匹配的数据。这是题目的正确答案！Cache采用这种方式来快速查找数据。');
            
            let step = 0;
            const searchTargets = ['数据A', '数据C', '数据B'];
            const cacheData = ['数据A', '数据B', '数据C', '数据D'];
            
            function animate() {
                initCanvas();
                
                // 绘制Cache单元
                const unitWidth = 150;
                const unitHeight = 60;
                const startX = 50;
                const startY = 150;
                
                for (let i = 0; i < cacheData.length; i++) {
                    const x = startX + i * (unitWidth + 20);
                    const y = startY;
                    const isMatch = step < searchTargets.length && cacheData[i] === searchTargets[step];
                    
                    drawStorageUnit(x, y, unitWidth, unitHeight, '#f3e5f5', cacheData[i], isMatch);
                    
                    // 绘制比较器
                    ctx.fillStyle = isMatch ? '#4caf50' : '#e0e0e0';
                    ctx.fillRect(x + 20, y - 40, unitWidth - 40, 30);
                    ctx.strokeStyle = '#333';
                    ctx.strokeRect(x + 20, y - 40, unitWidth - 40, 30);
                    
                    ctx.fillStyle = isMatch ? 'white' : '#666';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('比较器', x + unitWidth/2, y - 20);
                    
                    // 绘制比较结果
                    if (step < searchTargets.length) {
                        ctx.fillStyle = isMatch ? '#4caf50' : '#f44336';
                        ctx.font = '14px Arial';
                        ctx.fillText(isMatch ? '匹配!' : '不匹配', x + unitWidth/2, y + unitHeight + 20);
                    }
                }
                
                // 绘制搜索目标
                if (step < searchTargets.length) {
                    ctx.fillStyle = '#667eea';
                    ctx.fillRect(300, 50, 200, 40);
                    ctx.strokeStyle = '#333';
                    ctx.strokeRect(300, 50, 200, 40);
                    
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`搜索目标: ${searchTargets[step]}`, 400, 75);
                    
                    // 绘制搜索线
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);
                    for (let i = 0; i < cacheData.length; i++) {
                        const x = startX + i * (unitWidth + 20) + unitWidth/2;
                        ctx.beginPath();
                        ctx.moveTo(400, 90);
                        ctx.lineTo(x, startY - 40);
                        ctx.stroke();
                    }
                    ctx.setLineDash([]);
                }
                
                // 绘制说明文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                if (step < searchTargets.length) {
                    ctx.fillText('所有单元同时进行内容比较', canvas.width / 2, 300);
                    ctx.fillText('根据内容而非地址进行访问', canvas.width / 2, 320);
                } else {
                    ctx.fillText('相联访问完成！这就是Cache的工作原理', canvas.width / 2, 300);
                }
                
                step++;
                if (step <= searchTargets.length) {
                    setTimeout(() => {
                        animationId = requestAnimationFrame(animate);
                    }, 1500);
                }
            }
            
            animate();
        }

        function showExplanation(title, text) {
            const explanationDiv = document.getElementById('demoExplanation');
            const titleElement = document.getElementById('demoTitle');
            const textElement = document.getElementById('demoText');
            
            titleElement.textContent = title;
            textElement.textContent = text;
            explanationDiv.style.display = 'block';
            
            // 更新进度条
            const progress = document.getElementById('progressFill');
            const demos = ['sequential', 'direct', 'random', 'associative'];
            const currentIndex = demos.indexOf(currentDemo);
            if (currentIndex !== -1) {
                progress.style.width = `${((currentIndex + 1) / demos.length) * 100}%`;
            }
        }

        function resetDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            initCanvas();
            document.getElementById('demoExplanation').style.display = 'none';
            document.getElementById('progressFill').style.width = '0%';
            currentDemo = null;
        }

        // 初始化
        initCanvas();
        
        // 测验功能
        let selectedAnswer = null;
        let selectedAnswer2 = null;
        let totalScore = 0;

        function selectOption(element, answer) {
            // 清除之前的选择
            document.querySelectorAll('#quizOptions .quiz-option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // 选中当前选项
            element.classList.add('selected');
            selectedAnswer = answer;
            document.getElementById('checkBtn').style.display = 'inline-block';
        }

        function selectOption2(element, answer) {
            // 清除之前的选择
            document.querySelectorAll('#quiz2Options .quiz-option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // 选中当前选项
            element.classList.add('selected');
            selectedAnswer2 = answer;
            document.getElementById('check2Btn').style.display = 'inline-block';
        }

        function checkAnswer() {
            const resultDiv = document.getElementById('quizResult');
            const options = document.querySelectorAll('#quizOptions .quiz-option');

            options.forEach(opt => {
                opt.classList.remove('correct', 'incorrect');
                if (opt.textContent.startsWith('B')) {
                    opt.classList.add('correct');
                } else if (opt.classList.contains('selected') && selectedAnswer !== 'B') {
                    opt.classList.add('incorrect');
                }
            });

            if (selectedAnswer === 'B') {
                resultDiv.innerHTML = `
                    <div style="color: #28a745; font-weight: bold;">✅ 正确！</div>
                    <p>相联存取方式是根据所访问的内容来决定要访问的存储单元，Cache存储器采用这种方式来快速查找数据。</p>
                `;
                totalScore++;
            } else {
                resultDiv.innerHTML = `
                    <div style="color: #dc3545; font-weight: bold;">❌ 错误</div>
                    <p>正确答案是B. Cache。相联存取是根据内容而非地址来访问数据，Cache存储器使用这种方式来提高访问速度。</p>
                `;
            }

            resultDiv.style.display = 'block';
            document.getElementById('checkBtn').style.display = 'none';
            updateScore();
        }

        function checkAnswer2() {
            const resultDiv = document.getElementById('quiz2Result');
            const options = document.querySelectorAll('#quiz2Options .quiz-option');

            options.forEach(opt => {
                opt.classList.remove('correct', 'incorrect');
                if (opt.textContent.startsWith('A')) {
                    opt.classList.add('correct');
                } else if (opt.classList.contains('selected') && selectedAnswer2 !== 'A') {
                    opt.classList.add('incorrect');
                }
            });

            if (selectedAnswer2 === 'A') {
                resultDiv.innerHTML = `
                    <div style="color: #28a745; font-weight: bold;">✅ 正确！</div>
                    <p>磁带存储器采用顺序存取方式，数据必须按线性顺序访问，无法跳跃。</p>
                `;
                totalScore++;
            } else {
                resultDiv.innerHTML = `
                    <div style="color: #dc3545; font-weight: bold;">❌ 错误</div>
                    <p>正确答案是A. 磁带存储器。磁带存储器必须按顺序访问数据，就像播放磁带一样。</p>
                `;
            }

            resultDiv.style.display = 'block';
            document.getElementById('check2Btn').style.display = 'none';
            updateScore();
        }

        function updateScore() {
            document.getElementById('totalScore').textContent = totalScore;

            if (totalScore === 2) {
                setTimeout(() => {
                    alert('🎉 恭喜！您已完全掌握存储器访问方式的知识！');
                }, 500);
            }
        }

        // 播放完整演示
        async function playAllDemos() {
            const demos = [
                { func: demonstrateSequential, name: '顺序存取' },
                { func: demonstrateDirect, name: '直接存取' },
                { func: demonstrateRandom, name: '随机存取' },
                { func: demonstrateAssociative, name: '相联存取' }
            ];

            for (let i = 0; i < demos.length; i++) {
                // 显示当前演示名称
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    color: white;
                    padding: 15px 25px;
                    border-radius: 25px;
                    font-weight: bold;
                    z-index: 1000;
                    animation: fadeInDown 0.5s ease-out;
                `;
                notification.textContent = `正在演示: ${demos[i].name}`;
                document.body.appendChild(notification);

                // 执行演示
                demos[i].func();

                // 等待演示完成
                await new Promise(resolve => setTimeout(resolve, 8000));

                // 移除通知
                document.body.removeChild(notification);

                // 演示间隔
                if (i < demos.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }

            // 显示完成消息
            const completeNotification = document.createElement('div');
            completeNotification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(45deg, #28a745, #20c997);
                color: white;
                padding: 20px 30px;
                border-radius: 15px;
                font-size: 18px;
                font-weight: bold;
                z-index: 1000;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            `;
            completeNotification.innerHTML = '🎊 完整演示结束！<br>现在您可以进行测验了！';
            document.body.appendChild(completeNotification);

            setTimeout(() => {
                document.body.removeChild(completeNotification);
            }, 3000);
        }

        function resetAll() {
            resetDemo();
            selectedAnswer = null;
            selectedAnswer2 = null;
            totalScore = 0;

            // 重置测验选项
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'incorrect');
            });

            // 隐藏结果和按钮
            document.getElementById('quizResult').style.display = 'none';
            document.getElementById('quiz2Result').style.display = 'none';
            document.getElementById('checkBtn').style.display = 'none';
            document.getElementById('check2Btn').style.display = 'none';

            updateScore();

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 添加画布点击事件
        canvas.addEventListener('click', function(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // 添加点击效果
            ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
            ctx.beginPath();
            ctx.arc(x, y, 20, 0, 2 * Math.PI);
            ctx.fill();

            setTimeout(() => {
                if (currentDemo) {
                    // 重新绘制当前演示
                    switch(currentDemo) {
                        case 'sequential': demonstrateSequential(); break;
                        case 'direct': demonstrateDirect(); break;
                        case 'random': demonstrateRandom(); break;
                        case 'associative': demonstrateAssociative(); break;
                    }
                } else {
                    initCanvas();
                }
            }, 200);
        });
    </script>
</body>
</html>
