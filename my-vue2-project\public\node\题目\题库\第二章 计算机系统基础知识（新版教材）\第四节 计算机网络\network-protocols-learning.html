<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络协议学习 - 组播协议探索</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .question-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-text {
            font-size: 1.4rem;
            color: #2c3e50;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .option.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-color: #4CAF50;
        }

        .option.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            border-color: #f44336;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            text-align: center;
        }

        #networkCanvas {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            max-width: 100%;
            height: auto;
        }

        .explanation-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .protocol-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .protocol-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .protocol-card:hover {
            transform: translateX(10px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .protocol-name {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .protocol-desc {
            color: #7f8c8d;
            line-height: 1.5;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🌐 网络协议学习</h1>
            <p class="subtitle">通过动画理解组播协议的奥秘</p>
        </div>

        <div class="question-card">
            <h2 style="color: #667eea; margin-bottom: 20px;">📝 题目分析</h2>
            <div class="question-text">
                下列协议中，（ ）定义了组播中组的成员加入和退出机制。
            </div>
            
            <div class="options-container" id="optionsContainer">
                <div class="option" data-answer="A">
                    <strong>A. RARP</strong>
                    <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                        反向地址转换协议
                    </div>
                </div>
                <div class="option" data-answer="B">
                    <strong>B. ARP</strong>
                    <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                        地址解析协议
                    </div>
                </div>
                <div class="option" data-answer="C">
                    <strong>C. IGMP</strong>
                    <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                        Internet组管理协议
                    </div>
                </div>
                <div class="option" data-answer="D">
                    <strong>D. ICMP</strong>
                    <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                        网间控制报文协议
                    </div>
                </div>
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="showAnswer()">🎯 查看正确答案</button>
                <button class="btn" onclick="startAnimation()">🎬 开始动画演示</button>
            </div>
        </div>

        <div class="canvas-container">
            <h3 style="color: #2c3e50; margin-bottom: 20px;">🎭 网络协议动画演示</h3>
            <canvas id="networkCanvas" width="800" height="500"></canvas>
            
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>
        </div>

        <div class="explanation-card">
            <h2 style="color: #667eea; margin-bottom: 30px;">🧠 知识点详解</h2>
            
            <div class="highlight">
                <h3>💡 关键概念：组播（Multicast）</h3>
                <p>组播是一种网络通信方式，允许一个发送者同时向多个接收者发送数据，就像老师在课堂上讲课，多个学生同时听讲一样。</p>
            </div>

            <div class="protocol-grid">
                <div class="protocol-card" onclick="explainProtocol('RARP')">
                    <div class="protocol-name">🔄 RARP</div>
                    <div class="protocol-desc">反向地址转换协议 - 通过物理地址获取IP地址</div>
                </div>
                
                <div class="protocol-card" onclick="explainProtocol('ARP')">
                    <div class="protocol-name">🎯 ARP</div>
                    <div class="protocol-desc">地址解析协议 - 通过IP地址获取物理地址</div>
                </div>
                
                <div class="protocol-card" onclick="explainProtocol('IGMP')" style="border-left-color: #4CAF50;">
                    <div class="protocol-name">👥 IGMP ✅</div>
                    <div class="protocol-desc">Internet组管理协议 - 管理组播组的成员加入和退出</div>
                </div>
                
                <div class="protocol-card" onclick="explainProtocol('ICMP')">
                    <div class="protocol-name">📢 ICMP</div>
                    <div class="protocol-desc">网间控制报文协议 - 报告网络错误和异常情况</div>
                </div>
            </div>

            <div class="highlight">
                <h3>🎯 解题思路</h3>
                <p><strong>关键词识别：</strong>"组播"、"组的成员"、"加入和退出机制"</p>
                <p><strong>逻辑推理：</strong>题目问的是管理组播组成员的协议，只有IGMP专门负责这个功能。</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('networkCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;

        // 设置画布尺寸
        function resizeCanvas() {
            const container = canvas.parentElement;
            const maxWidth = Math.min(container.clientWidth - 60, 800);
            canvas.width = maxWidth;
            canvas.height = maxWidth * 0.625; // 保持16:10比例
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 绘制网络节点
        function drawNode(x, y, label, color = '#667eea', size = 30) {
            ctx.beginPath();
            ctx.arc(x, y, size, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 4);
        }

        // 绘制连接线
        function drawConnection(x1, y1, x2, y2, color = '#ddd', width = 2) {
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.strokeStyle = color;
            ctx.lineWidth = width;
            ctx.stroke();
        }

        // 绘制数据包
        function drawPacket(x, y, label, color = '#4CAF50') {
            ctx.beginPath();
            ctx.roundRect(x - 25, y - 15, 50, 30, 5);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 3);
        }

        // 动画演示
        function startAnimation() {
            animationStep = 0;
            animate();
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 更新步骤指示器
            updateStepIndicator();
            
            switch(animationStep) {
                case 0:
                    drawStep1(centerX, centerY);
                    break;
                case 1:
                    drawStep2(centerX, centerY);
                    break;
                case 2:
                    drawStep3(centerX, centerY);
                    break;
                case 3:
                    drawStep4(centerX, centerY);
                    break;
            }
            
            setTimeout(() => {
                animationStep = (animationStep + 1) % 4;
                animate();
            }, 3000);
        }

        function drawStep1(centerX, centerY) {
            // 绘制标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('步骤1: 网络拓扑结构', centerX, 30);
            
            // 绘制路由器
            drawNode(centerX, centerY, 'Router', '#e74c3c', 35);
            
            // 绘制客户端
            const clients = [
                {x: centerX - 150, y: centerY - 100, label: 'PC1'},
                {x: centerX + 150, y: centerY - 100, label: 'PC2'},
                {x: centerX - 150, y: centerY + 100, label: 'PC3'},
                {x: centerX + 150, y: centerY + 100, label: 'PC4'}
            ];
            
            clients.forEach(client => {
                drawNode(client.x, client.y, client.label, '#3498db', 25);
                drawConnection(centerX, centerY, client.x, client.y);
            });
        }

        function drawStep2(centerX, centerY) {
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('步骤2: IGMP加入组播组', centerX, 30);
            
            drawNode(centerX, centerY, 'Router', '#e74c3c', 35);
            
            const clients = [
                {x: centerX - 150, y: centerY - 100, label: 'PC1', joining: true},
                {x: centerX + 150, y: centerY - 100, label: 'PC2', joining: true},
                {x: centerX - 150, y: centerY + 100, label: 'PC3', joining: false},
                {x: centerX + 150, y: centerY + 100, label: 'PC4', joining: false}
            ];
            
            clients.forEach(client => {
                const color = client.joining ? '#4CAF50' : '#95a5a6';
                drawNode(client.x, client.y, client.label, color, 25);
                drawConnection(centerX, centerY, client.x, client.y);
                
                if (client.joining) {
                    drawPacket(client.x + 30, client.y - 30, 'JOIN', '#4CAF50');
                }
            });
        }

        function drawStep3(centerX, centerY) {
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('步骤3: 组播数据传输', centerX, 30);
            
            drawNode(centerX, centerY, 'Router', '#e74c3c', 35);
            
            // 绘制组播源
            drawNode(centerX, centerY - 150, 'Source', '#f39c12', 30);
            drawConnection(centerX, centerY - 150, centerX, centerY);
            
            const clients = [
                {x: centerX - 150, y: centerY - 100, label: 'PC1', inGroup: true},
                {x: centerX + 150, y: centerY - 100, label: 'PC2', inGroup: true},
                {x: centerX - 150, y: centerY + 100, label: 'PC3', inGroup: false},
                {x: centerX + 150, y: centerY + 100, label: 'PC4', inGroup: false}
            ];
            
            clients.forEach(client => {
                const color = client.inGroup ? '#4CAF50' : '#95a5a6';
                drawNode(client.x, client.y, client.label, color, 25);
                const lineColor = client.inGroup ? '#4CAF50' : '#ddd';
                const lineWidth = client.inGroup ? 4 : 2;
                drawConnection(centerX, centerY, client.x, client.y, lineColor, lineWidth);
                
                if (client.inGroup) {
                    drawPacket((centerX + client.x) / 2, (centerY + client.y) / 2, 'DATA', '#f39c12');
                }
            });
        }

        function drawStep4(centerX, centerY) {
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('步骤4: IGMP离开组播组', centerX, 30);
            
            drawNode(centerX, centerY, 'Router', '#e74c3c', 35);
            
            const clients = [
                {x: centerX - 150, y: centerY - 100, label: 'PC1', leaving: false, inGroup: true},
                {x: centerX + 150, y: centerY - 100, label: 'PC2', leaving: true, inGroup: false},
                {x: centerX - 150, y: centerY + 100, label: 'PC3', leaving: false, inGroup: false},
                {x: centerX + 150, y: centerY + 100, label: 'PC4', leaving: false, inGroup: false}
            ];
            
            clients.forEach(client => {
                let color = '#95a5a6';
                if (client.inGroup) color = '#4CAF50';
                if (client.leaving) color = '#f44336';
                
                drawNode(client.x, client.y, client.label, color, 25);
                drawConnection(centerX, centerY, client.x, client.y);
                
                if (client.leaving) {
                    drawPacket(client.x + 30, client.y - 30, 'LEAVE', '#f44336');
                }
            });
        }

        function updateStepIndicator() {
            for (let i = 1; i <= 4; i++) {
                const step = document.getElementById(`step${i}`);
                if (i === animationStep + 1) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            }
        }

        // 显示答案
        function showAnswer() {
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                const answer = option.getAttribute('data-answer');
                if (answer === 'C') {
                    option.classList.add('correct');
                } else {
                    option.classList.add('wrong');
                }
            });
        }

        // 协议解释
        function explainProtocol(protocol) {
            const explanations = {
                'RARP': '🔄 RARP (反向地址转换协议)\n\n当计算机只知道自己的物理地址（MAC地址），需要获取IP地址时使用。就像你知道自己的身份证号，但需要查询自己的学号一样。',
                'ARP': '🎯 ARP (地址解析协议)\n\n当计算机知道目标的IP地址，需要获取对应的物理地址时使用。就像你知道同学的学号，需要找到他的座位号一样。',
                'IGMP': '👥 IGMP (Internet组管理协议) ✅\n\n专门管理组播组的协议！负责：\n• 主机加入组播组\n• 主机离开组播组\n• 路由器查询组成员\n• 维护组播组状态\n\n这就是正确答案！',
                'ICMP': '📢 ICMP (网间控制报文协议)\n\n用于报告网络错误和异常情况。就像网络的"报警器"，当出现问题时会发出警报，比如"目标不可达"、"超时"等。'
            };
            
            alert(explanations[protocol]);
        }

        // 初始化
        drawStep1(canvas.width / 2, canvas.height / 2);
    </script>
</body>
</html>
