<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统测试知识学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .option.selected {
            border-color: #4CAF50;
            background: #e8f5e8;
        }

        .option.correct {
            border-color: #4CAF50;
            background: #d4edda;
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: #f8d7da;
            animation: wrongShake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            background: linear-gradient(45deg, #f0f2f5, #e9ecef);
        }

        .explanation {
            background: #fff3cd;
            border-left: 5px solid #ffc107;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .explanation h3 {
            color: #856404;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation p {
            color: #856404;
            line-height: 1.7;
            font-size: 1.1rem;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 系统测试知识学习</h1>
            <p class="subtitle">通过互动动画学习系统测试的核心概念</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>系统测试将软件、硬件、网络等其它因素结合，对整个软件进行测试。（ ）不是系统测试的内容。
            </div>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 路径测试
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 可靠性测试
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 安装测试
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 安全测试
                </div>
            </div>
            
            <button class="btn" onclick="checkAnswer()">提交答案</button>
            <button class="btn" onclick="showExplanation()">查看解析</button>
            <button class="btn" onclick="startAnimation()">开始动画演示</button>
        </div>

        <div class="canvas-container">
            <canvas id="gameCanvas"></canvas>
        </div>

        <div class="explanation" id="explanation" style="display: none;">
            <h3>📚 知识解析</h3>
            <p>
                <strong>系统测试</strong>是将已经确认的软件、计算机硬件、外设和网络等其他因素结合在一起，进行信息系统的各种集成测试和确认测试。
                <br><br>
                <strong>系统测试的主要内容包括：</strong>
                <br>• 恢复测试 - 测试系统的故障恢复能力
                <br>• 安全性测试 - 验证系统的安全防护机制
                <br>• 压力测试 - 测试系统在高负载下的表现
                <br>• 性能测试 - 评估系统的响应速度和吞吐量
                <br>• 可靠性测试 - 验证系统的稳定性和可靠性
                <br>• 可用性测试 - 测试用户界面的易用性
                <br>• 可维护性测试 - 评估系统的维护难易程度
                <br>• 安装测试 - 验证软件的安装和卸载过程
                <br><br>
                <strong>路径测试</strong>属于白盒测试技术，主要用于单元测试阶段，不属于系统测试的范畴。
            </p>
        </div>
    </div>

    <script>
        let selectedAnswer = null;
        let canvas, ctx;
        let animationId;
        let particles = [];
        let testTypes = [];

        // 初始化
        window.onload = function() {
            canvas = document.getElementById('gameCanvas');
            ctx = canvas.getContext('2d');
            
            // 设置canvas尺寸
            resizeCanvas();
            window.addEventListener('resize', resizeCanvas);
            
            // 创建浮动元素
            createFloatingElements();
            
            // 初始化测试类型
            initTestTypes();
            
            // 开始背景动画
            animate();
        };

        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth - 60;
            canvas.height = 400;
        }

        function createFloatingElements() {
            const container = document.querySelector('.floating-elements');
            for(let i = 0; i < 8; i++) {
                const circle = document.createElement('div');
                circle.className = 'floating-circle';
                circle.style.width = Math.random() * 100 + 50 + 'px';
                circle.style.height = circle.style.width;
                circle.style.left = Math.random() * 100 + '%';
                circle.style.top = Math.random() * 100 + '%';
                circle.style.animationDelay = Math.random() * 6 + 's';
                circle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(circle);
            }
        }

        // 选择答案
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedAnswer = this.dataset.answer;
            });
        });

        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }
            
            const correctAnswer = 'A';
            document.querySelectorAll('.option').forEach(option => {
                if (option.dataset.answer === correctAnswer) {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== correctAnswer) {
                    option.classList.add('wrong');
                }
            });
            
            setTimeout(() => {
                if (selectedAnswer === correctAnswer) {
                    alert('🎉 恭喜答对了！路径测试属于白盒测试，不是系统测试的内容。');
                } else {
                    alert('❌ 答案错误，正确答案是A。路径测试属于单元测试阶段的白盒测试技术。');
                }
            }, 1000);
        }

        function showExplanation() {
            const explanation = document.getElementById('explanation');
            explanation.style.display = 'block';
            explanation.scrollIntoView({ behavior: 'smooth' });
        }

        function initTestTypes() {
            testTypes = [
                { name: '路径测试', type: 'unit', color: '#ff6b6b', x: 100, y: 200, isSystemTest: false },
                { name: '可靠性测试', type: 'system', color: '#4ecdc4', x: 300, y: 150, isSystemTest: true },
                { name: '安装测试', type: 'system', color: '#45b7d1', x: 500, y: 200, isSystemTest: true },
                { name: '安全测试', type: 'system', color: '#96ceb4', x: 700, y: 150, isSystemTest: true },
                { name: '性能测试', type: 'system', color: '#feca57', x: 400, y: 300, isSystemTest: true },
                { name: '压力测试', type: 'system', color: '#ff9ff3', x: 600, y: 300, isSystemTest: true }
            ];
        }

        function startAnimation() {
            // 清除画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制系统测试框架
            drawSystemTestFramework();
            
            // 动画展示测试类型
            animateTestTypes();
        }

        function drawSystemTestFramework() {
            // 绘制系统测试大框
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.setLineDash([]);
            ctx.strokeRect(50, 50, canvas.width - 100, canvas.height - 100);
            
            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('系统测试范围', canvas.width / 2, 40);
            
            // 绘制单元测试区域（在系统测试外）
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.strokeRect(20, 20, 150, 100);
            
            ctx.fillStyle = '#ff6b6b';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('单元测试', 95, 15);
        }

        function animateTestTypes() {
            let index = 0;
            
            function drawNextType() {
                if (index < testTypes.length) {
                    const test = testTypes[index];
                    
                    // 绘制测试类型圆圈
                    ctx.beginPath();
                    ctx.arc(test.x, test.y, 40, 0, 2 * Math.PI);
                    ctx.fillStyle = test.color;
                    ctx.fill();
                    ctx.strokeStyle = test.isSystemTest ? '#4CAF50' : '#f44336';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    
                    // 绘制文字
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(test.name, test.x, test.y + 5);
                    
                    // 添加动画效果
                    if (test.isSystemTest) {
                        // 系统测试项目闪烁绿色
                        setTimeout(() => {
                            ctx.beginPath();
                            ctx.arc(test.x, test.y, 45, 0, 2 * Math.PI);
                            ctx.strokeStyle = '#4CAF50';
                            ctx.lineWidth = 5;
                            ctx.stroke();
                        }, 500);
                    } else {
                        // 非系统测试项目显示红色X
                        setTimeout(() => {
                            ctx.strokeStyle = '#f44336';
                            ctx.lineWidth = 4;
                            ctx.beginPath();
                            ctx.moveTo(test.x - 15, test.y - 15);
                            ctx.lineTo(test.x + 15, test.y + 15);
                            ctx.moveTo(test.x + 15, test.y - 15);
                            ctx.lineTo(test.x - 15, test.y + 15);
                            ctx.stroke();
                        }, 500);
                    }
                    
                    index++;
                    setTimeout(drawNextType, 800);
                }
            }
            
            drawNextType();
        }

        function animate() {
            // 背景渐变动画
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f0f2f5');
            gradient.addColorStop(1, '#e9ecef');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制装饰性粒子
            for (let i = 0; i < 5; i++) {
                if (Math.random() < 0.02) {
                    particles.push({
                        x: Math.random() * canvas.width,
                        y: canvas.height,
                        vx: (Math.random() - 0.5) * 2,
                        vy: -Math.random() * 3 - 1,
                        life: 100,
                        color: `hsl(${Math.random() * 60 + 200}, 70%, 70%)`
                    });
                }
            }
            
            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;
                
                ctx.globalAlpha = particle.life / 100;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, 3, 0, 2 * Math.PI);
                ctx.fill();
                ctx.globalAlpha = 1;
                
                return particle.life > 0 && particle.y > 0;
            });
            
            animationId = requestAnimationFrame(animate);
        }
    </script>
</body>
</html>
