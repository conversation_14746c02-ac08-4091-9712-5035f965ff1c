<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AQS 交互式学习 - 从零开始理解Java并发</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: justify;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
            color: #333;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .interactive-btn:active {
            transform: translateY(-1px);
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .game-score {
            text-align: center;
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">AQS 交互式学习</h1>
            <p class="subtitle">AbstractQueuedSynchronizer - Java并发编程的核心</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section" id="section1">
            <h2 class="section-title">🎯 什么是AQS？</h2>
            <div class="explanation">
                <p><span class="highlight">AQS（AbstractQueuedSynchronizer）</span>是Java并发包中的一个抽象类，它就像是一个<strong>智能的排队管理系统</strong>。</p>
                <p>想象一下银行排队：当你去银行办业务时，如果柜台忙碌，你需要排队等待。AQS就是这样一个排队系统，但它管理的是<strong>线程</strong>而不是人。</p>
            </div>
            <div class="canvas-container">
                <canvas id="introCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="interactive-btn" onclick="startIntroAnimation()">🎬 开始动画演示</button>
                <button class="interactive-btn" onclick="resetIntroAnimation()">🔄 重置</button>
            </div>
        </div>

        <div class="section" id="section2">
            <h2 class="section-title">🏗️ AQS的核心组件</h2>
            <div class="explanation">
                <p>AQS主要由两个核心部分组成：</p>
                <p>1. <span class="highlight">状态变量（state）</span>：一个整数，表示资源的状态（比如锁是否被占用）</p>
                <p>2. <span class="highlight">等待队列（FIFO Queue）</span>：一个先进先出的队列，存储等待的线程</p>
            </div>
            <div class="canvas-container">
                <canvas id="componentsCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="interactive-btn" onclick="showState()">📊 查看状态变量</button>
                <button class="interactive-btn" onclick="showQueue()">🔗 查看等待队列</button>
                <button class="interactive-btn" onclick="showBoth()">👁️ 查看全部</button>
            </div>
        </div>

        <div class="section" id="section3">
            <h2 class="section-title">🎮 互动游戏：线程排队模拟</h2>
            <div class="explanation">
                <p>现在让我们通过一个有趣的游戏来理解AQS的工作原理！</p>
                <p>点击"添加线程"来模拟多个线程竞争资源，观察它们如何排队和获取锁。</p>
            </div>
            <div class="game-score" id="gameScore">得分: 0 | 成功获取锁的线程: 0</div>
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="interactive-btn" onclick="addThread()">➕ 添加线程</button>
                <button class="interactive-btn" onclick="releaseLock()">🔓 释放锁</button>
                <button class="interactive-btn" onclick="resetGame()">🎮 重新开始</button>
            </div>
        </div>

        <div class="section" id="section4">
            <h2 class="section-title">🔧 AQS的实际应用</h2>
            <div class="explanation">
                <p>AQS是许多Java并发工具的基础，包括：</p>
                <p>• <span class="highlight">ReentrantLock</span>：可重入锁</p>
                <p>• <span class="highlight">Semaphore</span>：信号量，控制同时访问资源的线程数</p>
                <p>• <span class="highlight">CountDownLatch</span>：倒计时门闩，等待多个线程完成</p>
                <p>• <span class="highlight">CyclicBarrier</span>：循环屏障，同步多个线程</p>
            </div>
            <div class="canvas-container">
                <canvas id="applicationsCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="interactive-btn" onclick="showReentrantLock()">🔒 ReentrantLock</button>
                <button class="interactive-btn" onclick="showSemaphore()">🚦 Semaphore</button>
                <button class="interactive-btn" onclick="showCountDownLatch()">⏳ CountDownLatch</button>
            </div>
        </div>

        <div class="section" id="section5">
            <h2 class="section-title">🎯 知识总结</h2>
            <div class="explanation">
                <p>通过这个交互式学习，你已经掌握了AQS的核心概念：</p>
                <p>✅ AQS是Java并发编程的基础框架</p>
                <p>✅ 它使用状态变量和FIFO队列管理线程</p>
                <p>✅ 线程通过排队的方式有序地获取和释放资源</p>
                <p>✅ 许多常用的并发工具都基于AQS实现</p>
            </div>
            <div class="canvas-container">
                <canvas id="summaryCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="interactive-btn pulse" onclick="celebrateCompletion()">🎉 完成学习！</button>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // 全局变量
        let currentSection = 0;
        let gameScore = 0;
        let successfulThreads = 0;
        let threads = [];
        let lockOwner = null;
        let animationId = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeAnimations();
            setupScrollAnimation();
            updateProgress();
        });

        // 滚动动画
        function setupScrollAnimation() {
            const sections = document.querySelectorAll('.section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        currentSection = Array.from(sections).indexOf(entry.target);
                        updateProgress();
                    }
                });
            }, { threshold: 0.3 });

            sections.forEach(section => observer.observe(section));
        }

        // 更新进度条
        function updateProgress() {
            const progress = ((currentSection + 1) / 5) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 初始化所有画布
        function initializeAnimations() {
            drawIntroScene();
            drawComponentsScene();
            drawGameScene();
            drawApplicationsScene();
            drawSummaryScene();
        }

        // 第一部分：介绍动画
        function drawIntroScene() {
            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制银行场景
            drawBank(ctx);
            drawPeople(ctx, []);
        }

        function drawBank(ctx) {
            // 银行建筑
            ctx.fillStyle = '#4a90e2';
            ctx.fillRect(300, 100, 200, 150);
            
            // 银行标志
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('AQS银行', 400, 140);
            
            // 柜台
            ctx.fillStyle = '#8b4513';
            ctx.fillRect(350, 200, 100, 50);
            
            // 柜员
            ctx.fillStyle = '#ffdbac';
            ctx.beginPath();
            ctx.arc(400, 180, 15, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawPeople(ctx, people) {
            people.forEach((person, index) => {
                const x = 150 + index * 60;
                const y = 300;
                
                // 人物身体
                ctx.fillStyle = person.color || '#ff6b6b';
                ctx.fillRect(x - 10, y - 30, 20, 40);
                
                // 人物头部
                ctx.fillStyle = '#ffdbac';
                ctx.beginPath();
                ctx.arc(x, y - 40, 12, 0, Math.PI * 2);
                ctx.fill();
                
                // 线程ID
                ctx.fillStyle = 'black';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`T${person.id}`, x, y + 20);
            });
        }

        function startIntroAnimation() {
            const people = [
                {id: 1, color: '#ff6b6b'},
                {id: 2, color: '#4ecdc4'},
                {id: 3, color: '#45b7d1'},
                {id: 4, color: '#96ceb4'}
            ];
            
            let step = 0;
            const animate = () => {
                const canvas = document.getElementById('introCanvas');
                const ctx = canvas.getContext('2d');
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawBank(ctx);
                
                const visiblePeople = people.slice(0, Math.min(step + 1, people.length));
                drawPeople(ctx, visiblePeople);
                
                // 添加说明文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`线程排队等待获取资源 (${visiblePeople.length}/${people.length})`, 50, 50);
                
                step++;
                if (step < people.length + 5) {
                    setTimeout(animate, 800);
                }
            };
            
            animate();
        }

        function resetIntroAnimation() {
            drawIntroScene();
        }

        // 第二部分：组件展示
        function drawComponentsScene() {
            const canvas = document.getElementById('componentsCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制状态变量
            drawStateVariable(ctx, false);
            
            // 绘制队列
            drawWaitQueue(ctx, false);
        }

        function drawStateVariable(ctx, highlight) {
            const x = 150, y = 200;
            
            // 状态变量框
            ctx.fillStyle = highlight ? '#ff6b6b' : '#e8e8e8';
            ctx.fillRect(x - 50, y - 30, 100, 60);
            
            ctx.strokeStyle = highlight ? '#d63031' : '#999';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 50, y - 30, 100, 60);
            
            // 状态值
            ctx.fillStyle = 'black';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('0', x, y + 8);
            
            // 标签
            ctx.font = '14px Arial';
            ctx.fillText('状态变量(state)', x, y + 50);
        }

        function drawWaitQueue(ctx, highlight) {
            const startX = 400, y = 200;
            
            // 队列节点
            for (let i = 0; i < 4; i++) {
                const x = startX + i * 80;
                
                ctx.fillStyle = highlight ? '#74b9ff' : '#ddd';
                ctx.fillRect(x - 25, y - 20, 50, 40);
                
                ctx.strokeStyle = highlight ? '#0984e3' : '#999';
                ctx.lineWidth = 2;
                ctx.strokeRect(x - 25, y - 20, 50, 40);
                
                // 线程ID
                ctx.fillStyle = 'black';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`T${i + 1}`, x, y + 5);
                
                // 箭头
                if (i < 3) {
                    ctx.strokeStyle = highlight ? '#0984e3' : '#999';
                    ctx.beginPath();
                    ctx.moveTo(x + 25, y);
                    ctx.lineTo(x + 55, y);
                    ctx.moveTo(x + 50, y - 5);
                    ctx.lineTo(x + 55, y);
                    ctx.lineTo(x + 50, y + 5);
                    ctx.stroke();
                }
            }
            
            // 标签
            ctx.fillStyle = 'black';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('等待队列(FIFO)', startX + 120, y + 50);
        }

        function showState() {
            const canvas = document.getElementById('componentsCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawStateVariable(ctx, true);
            drawWaitQueue(ctx, false);
        }

        function showQueue() {
            const canvas = document.getElementById('componentsCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawStateVariable(ctx, false);
            drawWaitQueue(ctx, true);
        }

        function showBoth() {
            const canvas = document.getElementById('componentsCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawStateVariable(ctx, true);
            drawWaitQueue(ctx, true);
        }

        // 第三部分：游戏
        function drawGameScene() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制锁
            drawLock(ctx);
            
            // 绘制线程
            drawThreads(ctx);
            
            // 绘制队列
            drawThreadQueue(ctx);
        }

        function drawLock(ctx) {
            const x = 400, y = 100;
            
            // 锁的主体
            ctx.fillStyle = lockOwner ? '#e74c3c' : '#2ecc71';
            ctx.fillRect(x - 30, y - 20, 60, 40);
            
            // 锁的状态
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(lockOwner ? '🔒' : '🔓', x, y + 5);
            
            // 锁的拥有者
            if (lockOwner) {
                ctx.fillStyle = 'black';
                ctx.font = '12px Arial';
                ctx.fillText(`线程${lockOwner}持有`, x, y + 50);
            }
        }

        function drawThreads(ctx) {
            threads.forEach((thread, index) => {
                const x = thread.x;
                const y = thread.y;
                
                // 线程圆圈
                ctx.fillStyle = thread.color;
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, Math.PI * 2);
                ctx.fill();
                
                // 线程ID
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(thread.id, x, y + 5);
                
                // 状态指示
                ctx.fillStyle = 'black';
                ctx.font = '10px Arial';
                ctx.fillText(thread.status, x, y + 35);
            });
        }

        function drawThreadQueue(ctx) {
            const queueThreads = threads.filter(t => t.status === '等待中');
            
            queueThreads.forEach((thread, index) => {
                const x = 100 + index * 60;
                const y = 350;
                
                // 更新线程位置
                thread.x = x;
                thread.y = y;
                
                // 绘制队列位置
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 2;
                ctx.strokeRect(x - 25, y - 25, 50, 50);
                
                // 队列编号
                ctx.fillStyle = '#7f8c8d';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`位置${index + 1}`, x, y + 45);
            });
        }

        function addThread() {
            const threadId = threads.length + 1;
            const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c'];
            
            const newThread = {
                id: threadId,
                x: 50,
                y: 250,
                color: colors[threadId % colors.length],
                status: '新建'
            };
            
            threads.push(newThread);
            
            // 动画移动到队列
            setTimeout(() => {
                if (!lockOwner) {
                    // 直接获取锁
                    newThread.status = '运行中';
                    newThread.x = 400;
                    newThread.y = 200;
                    lockOwner = threadId;
                    gameScore += 10;
                } else {
                    // 进入等待队列
                    newThread.status = '等待中';
                }
                
                updateGameScore();
                drawGameScene();
            }, 500);
            
            drawGameScene();
        }

        function releaseLock() {
            if (!lockOwner) return;
            
            // 找到当前持有锁的线程
            const currentThread = threads.find(t => t.id === lockOwner);
            if (currentThread) {
                currentThread.status = '完成';
                currentThread.x = 700;
                currentThread.y = 250;
                successfulThreads++;
            }
            
            lockOwner = null;
            
            // 唤醒队列中的第一个线程
            const waitingThreads = threads.filter(t => t.status === '等待中');
            if (waitingThreads.length > 0) {
                const nextThread = waitingThreads[0];
                nextThread.status = '运行中';
                nextThread.x = 400;
                nextThread.y = 200;
                lockOwner = nextThread.id;
                gameScore += 10;
            }
            
            updateGameScore();
            drawGameScene();
        }

        function resetGame() {
            threads = [];
            lockOwner = null;
            gameScore = 0;
            successfulThreads = 0;
            updateGameScore();
            drawGameScene();
        }

        function updateGameScore() {
            document.getElementById('gameScore').textContent = 
                `得分: ${gameScore} | 成功获取锁的线程: ${successfulThreads}`;
        }

        // 工具提示
        function showTooltip(event, text) {
            const tooltip = document.getElementById('tooltip');
            tooltip.textContent = text;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 30 + 'px';
            tooltip.style.opacity = '1';
        }

        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = '0';
        }

        // 为画布添加鼠标事件
        document.getElementById('gameCanvas').addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 检查是否悬停在锁上
            if (x >= 370 && x <= 430 && y >= 80 && y <= 120) {
                showTooltip(e, lockOwner ? `锁被线程${lockOwner}持有` : '锁可用');
            } else {
                hideTooltip();
            }
        });

        document.getElementById('gameCanvas').addEventListener('mouseleave', hideTooltip);

        // 第四部分：应用场景
        function drawApplicationsScene() {
            const canvas = document.getElementById('applicationsCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制AQS在中心
            drawAQSCore(ctx);

            // 绘制周围的应用
            drawApplications(ctx);
        }

        function drawAQSCore(ctx) {
            const x = 400, y = 200;

            // AQS核心圆圈
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(x, y, 60, 0, Math.PI * 2);
            ctx.fill();

            // AQS文字
            ctx.fillStyle = 'white';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('AQS', x, y + 6);
        }

        function drawApplications(ctx) {
            const applications = [
                {name: 'ReentrantLock', x: 200, y: 100, color: '#e74c3c'},
                {name: 'Semaphore', x: 600, y: 100, color: '#2ecc71'},
                {name: 'CountDownLatch', x: 200, y: 300, color: '#f39c12'},
                {name: 'CyclicBarrier', x: 600, y: 300, color: '#9b59b6'}
            ];

            applications.forEach(app => {
                // 应用圆圈
                ctx.fillStyle = app.color;
                ctx.beginPath();
                ctx.arc(app.x, app.y, 40, 0, Math.PI * 2);
                ctx.fill();

                // 应用名称
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(app.name, app.x, app.y + 4);

                // 连接线
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(400, 200);
                ctx.lineTo(app.x, app.y);
                ctx.stroke();
            });
        }

        function showReentrantLock() {
            const canvas = document.getElementById('applicationsCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawAQSCore(ctx);
            drawApplications(ctx);

            // 高亮ReentrantLock
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(200, 100, 45, 0, Math.PI * 2);
            ctx.stroke();

            // 添加说明
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('ReentrantLock: 可重入的互斥锁', 50, 50);
            ctx.fillText('同一线程可以多次获取同一把锁', 50, 70);
        }

        function showSemaphore() {
            const canvas = document.getElementById('applicationsCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawAQSCore(ctx);
            drawApplications(ctx);

            // 高亮Semaphore
            ctx.strokeStyle = '#2ecc71';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(600, 100, 45, 0, Math.PI * 2);
            ctx.stroke();

            // 添加说明
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('Semaphore: 信号量，控制同时访问的线程数', 450, 50);
            ctx.fillText('例如：限制最多3个线程同时访问数据库', 450, 70);
        }

        function showCountDownLatch() {
            const canvas = document.getElementById('applicationsCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawAQSCore(ctx);
            drawApplications(ctx);

            // 高亮CountDownLatch
            ctx.strokeStyle = '#f39c12';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(200, 300, 45, 0, Math.PI * 2);
            ctx.stroke();

            // 添加说明
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('CountDownLatch: 倒计时门闩', 50, 350);
            ctx.fillText('等待多个线程完成后再继续执行', 50, 370);
        }

        // 第五部分：总结
        function drawSummaryScene() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制学习路径
            drawLearningPath(ctx);
        }

        function drawLearningPath(ctx) {
            const steps = [
                {text: '理解概念', x: 100, y: 150},
                {text: '学习组件', x: 250, y: 150},
                {text: '实践操作', x: 400, y: 150},
                {text: '了解应用', x: 550, y: 150},
                {text: '掌握AQS', x: 700, y: 150}
            ];

            steps.forEach((step, index) => {
                // 步骤圆圈
                ctx.fillStyle = '#2ecc71';
                ctx.beginPath();
                ctx.arc(step.x, step.y, 30, 0, Math.PI * 2);
                ctx.fill();

                // 步骤编号
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(index + 1, step.x, step.y + 5);

                // 步骤文字
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText(step.text, step.x, step.y + 50);

                // 连接线
                if (index < steps.length - 1) {
                    ctx.strokeStyle = '#2ecc71';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(step.x + 30, step.y);
                    ctx.lineTo(steps[index + 1].x - 30, steps[index + 1].y);
                    ctx.stroke();
                }
            });
        }

        function celebrateCompletion() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');

            // 添加庆祝动画
            let particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 4,
                    vy: (Math.random() - 0.5) * 4,
                    color: ['#e74c3c', '#2ecc71', '#3498db', '#f39c12', '#9b59b6'][Math.floor(Math.random() * 5)],
                    life: 100
                });
            }

            function animateParticles() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawLearningPath(ctx);

                particles.forEach((particle, index) => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    particle.life--;

                    if (particle.life > 0) {
                        ctx.fillStyle = particle.color;
                        ctx.globalAlpha = particle.life / 100;
                        ctx.beginPath();
                        ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                        ctx.fill();
                    } else {
                        particles.splice(index, 1);
                    }
                });

                ctx.globalAlpha = 1;

                // 恭喜文字
                ctx.fillStyle = '#2ecc71';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 恭喜你完成了AQS的学习！ 🎉', canvas.width / 2, 50);

                if (particles.length > 0) {
                    requestAnimationFrame(animateParticles);
                }
            }

            animateParticles();
        }
    </script>
</body>
</html>
