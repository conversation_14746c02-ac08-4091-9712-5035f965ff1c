<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件设计概念解析 - 学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e6eb;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .question-section {
            background-color: #e8f0fe;
            border-left: 5px solid #4a90e2;
            padding: 15px;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        .options label {
            display: block;
            margin-bottom: 10px;
            font-size: 1.1em;
            cursor: pointer;
            padding: 8px 12px;
            border: 1px solid #cce0ff;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .options label:hover {
            background-color: #dbeaff;
        }
        .options input[type="radio"] {
            margin-right: 10px;
        }
        .answer {
            margin-top: 20px;
            font-weight: bold;
            color: #28a745;
        }
        .explanation-section {
            margin-top: 30px;
        }
        .concept-card {
            background-color: #f8fcfd;
            border: 1px solid #e1ecf4;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .concept-card h3 {
            margin-top: 0;
            color: #34495e;
            border-bottom: 1px dashed #d5dee7;
            padding-bottom: 8px;
            margin-bottom: 15px;
        }
        .concept-card p {
            margin-bottom: 15px;
        }
        canvas {
            display: block;
            margin: 20px auto;
            border: 1px solid #ccc;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .interactive-controls {
            text-align: center;
            margin-top: 15px;
        }
        .interactive-controls button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
            margin: 5px;
        }
        .interactive-controls button:hover {
            background-color: #357ABD;
        }
        .interactive-info {
            background-color: #fff9e6;
            border-left: 4px solid #f0ad4e;
            padding: 10px;
            margin-top: 15px;
            border-radius: 5px;
            font-size: 0.95em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件设计概念解析</h1>

        <div class="question-section">
            <h2>题目</h2>
            <p><strong>软件设计活动中，（）描述了软件内部、软件和操作系统之间如何通信；（）定义了软件系统各主要部件之间的关系。</strong></p>
            <div class="options">
                <label><input type="radio" name="q2" value="A"> A. 性能设计</label>
                <label><input type="radio" name="q2" value="B"> B. 模块化设计</label>
                <label><input type="radio" name="q2" value="C"> C. 软件结构设计</label>
                <label><input type="radio" name="q2" value="D"> D. 数据流设计</label>
            </div>
            <p class="answer">正确答案：C</p>
        </div>

        <div class="explanation-section">
            <h2>题目解析与知识点</h2>
            <p>软件设计是一个复杂的过程，它将系统需求转化为软件的蓝图。它通常包含以下几个关键部分：</p>

            <!-- 软件结构设计 / 体系结构设计 -->
            <div class="concept-card">
                <h3>1. 软件结构设计 / 体系结构设计</h3>
                <p><strong>定义：</strong> 软件结构设计（也称体系结构设计）主要关注系统的高层结构，定义软件系统各个主要部件（例如模块、子系统、组件）之间的关系、职责和交互方式。</p>
                <p>这就像盖房子前要画一个总体的平面图，确定客厅在哪里，厨房在哪里，它们之间怎么连接。</p>
                <p><strong>题目对应：</strong> 题目中的“定义了软件系统各主要部件之间的关系”正是结构设计所涵盖的范畴。</p>
                <canvas id="archCanvas" width="700" height="300"></canvas>
                <div class="interactive-controls">
                    <button onclick="drawArchitecture()">开始演示</button>
                    <button onclick="resetCanvas('archCanvas')">重置</button>
                </div>
                <div class="interactive-info">点击“开始演示”按钮，查看软件模块如何组织和连接。</div>
            </div>

            <!-- 接口设计 -->
            <div class="concept-card">
                <h3>2. 接口设计 (人机界面设计)</h3>
                <p><strong>定义：</strong> 接口设计关注软件内部模块之间、软件与操作系统（或其他外部系统）之间，以及软件与用户之间如何进行通信和数据交换。</p>
                <p>这就像房间之间的门、插座，或者你和智能音箱对话的麦克风和喇叭，它们是信息进出的通道。</p>
                <p><strong>题目对应：</strong> 题目中的“描述了软件内部、软件和操作系统之间如何通信”正是接口设计所关注的。</p>
                <canvas id="interfaceCanvas" width="700" height="300"></canvas>
                <div class="interactive-controls">
                    <button onclick="startInterfaceDemo()">开始演示</button>
                    <button onclick="resetCanvas('interfaceCanvas')">重置</button>
                </div>
                <div class="interactive-info">点击“开始演示”按钮，观察不同类型的接口通信演示。</div>
            </div>

            <!-- 数据设计 -->
            <div class="concept-card">
                <h3>3. 数据设计</h3>
                <p><strong>定义：</strong> 数据设计是将系统的概念模型转换为具体的、可存储的数据结构定义的过程。一个好的数据设计能够优化程序的结构和模块划分，有效降低系统的复杂性。</p>
                <p>这就像你整理抽屉，把衣服、书籍、零食分门别类放好，方便查找和使用。</p>
                <canvas id="dataCanvas" width="700" height="300"></canvas>
                <div class="interactive-controls">
                    <button onclick="startDataDemo()">开始演示</button>
                    <button onclick="resetCanvas('dataCanvas')">重置</button>
                </div>
                <div class="interactive-info">点击“开始演示”按钮，查看数据如何被组织和转换。</div>
            </div>

            <!-- 过程设计 -->
            <div class="concept-card">
                <h3>4. 过程设计</h3>
                <p><strong>定义：</strong> 过程设计描述了如何将软件系统的各个结构部件（模块、组件）一步步地转换成可执行的软件代码和程序。它关注算法、流程和控制逻辑的细节。</p>
                <p>这就像你有了房子平面图和家具清单后，开始制定装修步骤，先做哪一步，后做哪一步。</p>
                <canvas id="processCanvas" width="700" height="300"></canvas>
                <div class="interactive-controls">
                    <button onclick="startProcessDemo()">开始演示</button>
                    <button onclick="resetCanvas('processCanvas')">重置</button>
                </div>
                <div class="interactive-info">点击“开始演示”按钮，了解软件构建的流程。</div>
            </div>
        </div>
    </div>

    <script>
        function resetCanvas(canvasId) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear canvas
            // Re-draw any static background elements if needed
        }

        // --- 1. 软件结构设计 / 体系结构设计 动画 ---
        let archAnimationFrameId = null;
        function drawArchitecture() {
            resetCanvas('archCanvas');
            const canvas = document.getElementById('archCanvas');
            const ctx = canvas.getContext('2d');

            const components = [
                { name: "前端UI", x: 100, y: 50, width: 120, height: 60 },
                { name: "后端API", x: 300, y: 150, width: 120, height: 60 },
                { name: "数据库", x: 500, y: 50, width: 120, height: 60 },
                { name: "第三方服务", x: 100, y: 200, width: 120, height: 60 }
            ];

            const connections = [
                { from: 0, to: 1, label: "API请求" }, // UI to Backend
                { from: 1, to: 2, label: "数据读写" }, // Backend to DB
                { from: 1, to: 3, label: "服务调用" }  // Backend to Third-party
            ];

            let animationStep = 0;
            const maxSteps = components.length + connections.length;
            const animationSpeed = 1000; // milliseconds per step

            function animate() {
                resetCanvas('archCanvas'); // Clear for redraw
                ctx.font = "14px Arial";
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";

                // Draw components
                for (let i = 0; i < components.length; i++) {
                    const comp = components[i];
                    if (animationStep >= i) {
                        ctx.fillStyle = '#ADD8E6'; // Light blue
                        ctx.strokeStyle = '#336699';
                        ctx.lineWidth = 2;
                        ctx.fillRect(comp.x, comp.y, comp.width, comp.height);
                        ctx.strokeRect(comp.x, comp.y, comp.width, comp.height);
                        ctx.fillStyle = '#333';
                        ctx.fillText(comp.name, comp.x + comp.width / 2, comp.y + comp.height / 2);
                    }
                }

                // Draw connections
                for (let i = 0; i < connections.length; i++) {
                    if (animationStep >= components.length + i) {
                        const conn = connections[i];
                        const fromComp = components[conn.from];
                        const toComp = components[conn.to];

                        const startX = fromComp.x + fromComp.width / 2;
                        const startY = fromComp.y + fromComp.height / 2;
                        const endX = toComp.x + toComp.width / 2;
                        const endY = toComp.y + toComp.height / 2;

                        ctx.strokeStyle = '#FF6347'; // Tomato red
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(startX, startY);
                        ctx.lineTo(endX, endY);
                        ctx.stroke();

                        // Draw arrow head
                        const headlen = 10; // length of head in pixels
                        const angle = Math.atan2(endY - startY, endX - startX);
                        ctx.beginPath();
                        ctx.moveTo(endX, endY);
                        ctx.lineTo(endX - headlen * Math.cos(angle - Math.PI / 6), endY - headlen * Math.sin(angle - Math.PI / 6));
                        ctx.moveTo(endX, endY);
                        ctx.lineTo(endX - headlen * Math.cos(angle + Math.PI / 6), endY - headlen * Math.sin(angle + Math.PI / 6));
                        ctx.stroke();

                        // Label
                        const midX = (startX + endX) / 2;
                        const midY = (startY + endY) / 2;
                        ctx.fillStyle = '#333';
                        ctx.fillText(conn.label, midX, midY - 10);
                    }
                }

                animationStep++;
                if (animationStep < maxSteps + 2) { // +2 for a small pause at the end
                    archAnimationFrameId = setTimeout(animate, animationSpeed);
                } else {
                    archAnimationFrameId = null;
                }
            }
            if (archAnimationFrameId) clearTimeout(archAnimationFrameId); // Stop any existing animation
            animationStep = 0; // Reset animation step
            animate();
        }

        // --- 2. 接口设计 动画 ---
        let interfaceAnimationFrameId = null;
        function startInterfaceDemo() {
            resetCanvas('interfaceCanvas');
            const canvas = document.getElementById('interfaceCanvas');
            const ctx = canvas.getContext('2d');

            const software = { x: 275, y: 100, width: 150, height: 100, name: "我的软件" };
            const os = { x: 50, y: 100, width: 100, height: 50, name: "操作系统" };
            const user = { x: 550, y: 100, width: 50, height: 50, name: "用户" };
            const moduleA = { x: 200, y: 50, width: 80, height: 40, name: "模块A" };
            const moduleB = { x: 400, y: 50, width: 80, height: 40, name: "模块B" };

            let animationStage = 0;
            const stages = [
                "drawSoftware",
                "internalCommunication",
                "osCommunication",
                "userCommunication"
            ];
            const animationSpeed = 2000;

            function drawArrow(ctx, fromX, fromY, toX, toY, label) {
                const headlen = 10;
                const angle = Math.atan2(toY - fromY, toX - fromX);
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(toX, toY);
                ctx.strokeStyle = '#007BFF'; // Blue
                ctx.lineWidth = 2;
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
                ctx.stroke();

                ctx.fillStyle = '#007BFF';
                ctx.fillText(label, (fromX + toX) / 2, (fromY + toY) / 2 - 10);
            }

            function animateInterface() {
                resetCanvas('interfaceCanvas');

                ctx.font = "14px Arial";
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";

                // Draw main software box
                ctx.fillStyle = '#D6ECF4';
                ctx.strokeStyle = '#2B7BB9';
                ctx.lineWidth = 2;
                ctx.fillRect(software.x, software.y, software.width, software.height);
                ctx.strokeRect(software.x, software.y, software.width, software.height);
                ctx.fillStyle = '#333';
                ctx.fillText(software.name, software.x + software.width / 2, software.y + software.height / 2);

                if (animationStage >= 1) { // Internal Communication
                    ctx.fillStyle = '#E0F2F7';
                    ctx.strokeStyle = '#2B7BB9';
                    ctx.fillRect(software.x + 20, software.y + 20, moduleA.width, moduleA.height);
                    ctx.strokeRect(software.x + 20, software.y + 20, moduleA.width, moduleA.height);
                    ctx.fillText(moduleA.name, software.x + 20 + moduleA.width / 2, software.y + 20 + moduleA.height / 2);

                    ctx.fillRect(software.x + software.width - moduleB.width - 20, software.y + 20, moduleB.width, moduleB.height);
                    ctx.strokeRect(software.x + software.width - moduleB.width - 20, software.y + 20, moduleB.width, moduleB.height);
                    ctx.fillText(moduleB.name, software.x + software.width - moduleB.width - 20 + moduleB.width / 2, software.y + 20 + moduleB.height / 2);

                    drawArrow(ctx, software.x + 20 + moduleA.width, software.y + 20 + moduleA.height / 2,
                              software.x + software.width - moduleB.width - 20, software.y + 20 + moduleB.height / 2, "内部调用");
                }

                if (animationStage >= 2) { // OS Communication
                    ctx.fillStyle = '#E6F7E9';
                    ctx.strokeStyle = '#4CAF50';
                    ctx.fillRect(os.x, os.y, os.width, os.height);
                    ctx.strokeRect(os.x, os.y, os.width, os.height);
                    ctx.fillText(os.name, os.x + os.width / 2, os.y + os.height / 2);

                    drawArrow(ctx, os.x + os.width, os.y + os.height / 2, software.x, software.y + software.height / 2, "系统调用");
                    drawArrow(ctx, software.x, software.y + software.height / 2, os.x + os.width, os.y + os.height / 2, "系统返回");
                }

                if (animationStage >= 3) { // User Communication
                    ctx.fillStyle = '#FFF8E1';
                    ctx.strokeStyle = '#FFC107';
                    ctx.beginPath();
                    ctx.arc(user.x + user.width / 2, user.y + user.height / 2, user.width / 2, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.stroke();
                    ctx.fillStyle = '#333';
                    ctx.fillText(user.name, user.x + user.width / 2, user.y + user.height / 2 + user.width / 2 + 10); // Label below circle

                    // Simple stick figure head
                    ctx.beginPath();
                    ctx.arc(user.x + user.width / 2, user.y + user.height / 2 - 20, 15, 0, Math.PI * 2);
                    ctx.stroke();
                    ctx.beginPath();
                    ctx.moveTo(user.x + user.width / 2, user.y + user.height / 2);
                    ctx.lineTo(user.x + user.width / 2, user.y + user.height / 2 + 30);
                    ctx.stroke();
                    ctx.beginPath();
                    ctx.moveTo(user.x + user.width / 2 - 15, user.y + user.height / 2 + 10);
                    ctx.lineTo(user.x + user.width / 2 + 15, user.y + user.height / 2 + 10);
                    ctx.stroke();

                    drawArrow(ctx, software.x + software.width, software.y + software.height / 2, user.x, user.y + user.height / 2, "界面交互");
                    drawArrow(ctx, user.x, user.y + user.height / 2, software.x + software.width, software.y + software.height / 2, "用户输入");
                }

                animationStage++;
                if (animationStage < stages.length + 1) {
                    interfaceAnimationFrameId = setTimeout(animateInterface, animationSpeed);
                } else {
                    interfaceAnimationFrameId = null;
                }
            }
            if (interfaceAnimationFrameId) clearTimeout(interfaceAnimationFrameId);
            animationStage = 0;
            animateInterface();
        }

        // --- 3. 数据设计 动画 ---
        let dataAnimationFrameId = null;
        function startDataDemo() {
            resetCanvas('dataCanvas');
            const canvas = document.getElementById('dataCanvas');
            const ctx = canvas.getContext('2d');

            const rawData = { x: 100, y: 100, radius: 40, name: "原始数据" };
            const processing = { x: 300, y: 100, width: 100, height: 60, name: "数据处理" };
            const structuredData = { x: 500, y: 100, width: 150, height: 80, name: "结构化数据 (数据库)" };

            let animationStage = 0;
            const animationSpeed = 1500;

            function drawArrow(ctx, fromX, fromY, toX, toY, label) {
                const headlen = 10;
                const angle = Math.atan2(toY - fromY, toX - fromX);
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(toX, toY);
                ctx.strokeStyle = '#FFA500'; // Orange
                ctx.lineWidth = 2;
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
                ctx.stroke();

                ctx.fillStyle = '#FFA500';
                ctx.fillText(label, (fromX + toX) / 2, (fromY + toY) / 2 - 10);
            }

            function animateData() {
                resetCanvas('dataCanvas');
                ctx.font = "14px Arial";
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";

                // Stage 0: Draw raw data
                ctx.fillStyle = '#FFE0B2';
                ctx.strokeStyle = '#FF9800';
                ctx.beginPath();
                ctx.arc(rawData.x, rawData.y, rawData.radius, 0, Math.PI * 2);
                ctx.fill();
                ctx.stroke();
                ctx.fillStyle = '#333';
                ctx.fillText(rawData.name, rawData.x, rawData.y);

                if (animationStage >= 1) { // Stage 1: Draw processing
                    ctx.fillStyle = '#E8F5E9';
                    ctx.strokeStyle = '#4CAF50';
                    ctx.fillRect(processing.x - processing.width / 2, processing.y - processing.height / 2, processing.width, processing.height);
                    ctx.strokeRect(processing.x - processing.width / 2, processing.y - processing.height / 2, processing.width, processing.height);
                    ctx.fillStyle = '#333';
                    ctx.fillText(processing.name, processing.x, processing.y);

                    drawArrow(ctx, rawData.x + rawData.radius, rawData.y, processing.x - processing.width / 2, processing.y, "转换");
                }

                if (animationStage >= 2) { // Stage 2: Draw structured data
                    ctx.fillStyle = '#BBDEFB';
                    ctx.strokeStyle = '#2196F3';
                    ctx.fillRect(structuredData.x - structuredData.width / 2, structuredData.y - structuredData.height / 2, structuredData.width, structuredData.height);
                    ctx.strokeRect(structuredData.x - structuredData.width / 2, structuredData.y - structuredData.height / 2, structuredData.width, structuredData.height);
                    ctx.fillStyle = '#333';
                    ctx.fillText(structuredData.name, structuredData.x, structuredData.y);
                    ctx.font = "12px Arial";
                    ctx.fillText("ID: INT, Name: STR, Age: INT", structuredData.x, structuredData.y + 20); // Example structure
                    ctx.font = "14px Arial";


                    drawArrow(ctx, processing.x + processing.width / 2, processing.y, structuredData.x - structuredData.width / 2, structuredData.y, "存储");
                }

                animationStage++;
                if (animationStage < 3) {
                    dataAnimationFrameId = setTimeout(animateData, animationSpeed);
                } else {
                    dataAnimationFrameId = null;
                }
            }
            if (dataAnimationFrameId) clearTimeout(dataAnimationFrameId);
            animationStage = 0;
            animateData();
        }

        // --- 4. 过程设计 动画 ---
        let processAnimationFrameId = null;
        function startProcessDemo() {
            resetCanvas('processCanvas');
            const canvas = document.getElementById('processCanvas');
            const ctx = canvas.getContext('2d');

            const steps = [
                { name: "需求分析", x: 100, y: 50, width: 100, height: 50 },
                { name: "软件设计", x: 250, y: 50, width: 100, height: 50 },
                { name: "编码实现", x: 400, y: 50, width: 100, height: 50 },
                { name: "测试", x: 550, y: 50, width: 100, height: 50 },
                { name: "部署", x: 325, y: 150, width: 100, height: 50 } // Example branching/loop back
            ];

            const connections = [
                { from: 0, to: 1, label: "确定设计" },
                { from: 1, to: 2, label: "编写代码" },
                { from: 2, to: 3, label: "发现问题" },
                { from: 3, to: 2, label: "修复bug" }, // Loop back
                { from: 3, to: 4, label: "通过测试" }
            ];

            let animationStep = 0;
            const animationSpeed = 1200;

            function drawArrow(ctx, fromX, fromY, toX, toY, label, color = '#6A5ACD') { // SlateBlue
                const headlen = 10;
                const angle = Math.atan2(toY - fromY, toX - fromX);
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(toX, toY);
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
                ctx.stroke();

                ctx.fillStyle = color;
                ctx.fillText(label, (fromX + toX) / 2, (fromY + toY) / 2 - 10);
            }

            function animateProcess() {
                resetCanvas('processCanvas');
                ctx.font = "14px Arial";
                ctx.textAlign = "center";
                ctx.textBaseline = "middle";

                // Draw steps
                for (let i = 0; i < steps.length; i++) {
                    const step = steps[i];
                    ctx.fillStyle = '#E6E6FA'; // Lavender
                    ctx.strokeStyle = '#6A5ACD';
                    ctx.fillRect(step.x, step.y, step.width, step.height);
                    ctx.strokeRect(step.x, step.y, step.width, step.height);
                    ctx.fillStyle = '#333';
                    ctx.fillText(step.name, step.x + step.width / 2, step.y + step.height / 2);
                }

                // Animate connections
                if (animationStep >= 0) {
                    // Step 1: Req -> Design
                    const conn = connections[0];
                    const from = steps[conn.from];
                    const to = steps[conn.to];
                    drawArrow(ctx, from.x + from.width, from.y + from.height / 2, to.x, to.y + to.height / 2, conn.label);
                }
                if (animationStep >= 1) {
                    // Step 2: Design -> Code
                    const conn = connections[1];
                    const from = steps[conn.from];
                    const to = steps[conn.to];
                    drawArrow(ctx, from.x + from.width, from.y + from.height / 2, to.x, to.y + to.height / 2, conn.label);
                }
                if (animationStep >= 2) {
                    // Step 3: Code -> Test
                    const conn = connections[2];
                    const from = steps[conn.from];
                    const to = steps[conn.to];
                    drawArrow(ctx, from.x + from.width, from.y + from.height / 2, to.x, to.y + to.height / 2, conn.label);
                }
                if (animationStep >= 3) {
                    // Step 4: Test -> Code (Bug Fix Loop)
                    const conn = connections[3];
                    const from = steps[conn.from];
                    const to = steps[conn.to];
                    // Custom arrow for loop back
                    ctx.strokeStyle = '#DC143C'; // Crimson
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(from.x + from.width / 2, from.y + from.height);
                    ctx.lineTo(from.x + from.width / 2, from.y + from.height + 30);
                    ctx.lineTo(to.x + to.width / 2, to.y + to.height + 30);
                    ctx.lineTo(to.x + to.width / 2, to.y + to.height);
                    ctx.stroke();
                    // Arrow head at the end of the loop
                    const headlen = 10;
                    const endX = to.x + to.width / 2;
                    const endY = to.y + to.height;
                    const angle = Math.atan2(endY - (to.y + to.height + 30), endX - (to.x + to.width / 2)); // Angle from the last segment
                    ctx.beginPath();
                    ctx.moveTo(endX, endY);
                    ctx.lineTo(endX + headlen * Math.cos(angle - Math.PI / 6), endY + headlen * Math.sin(angle - Math.PI / 6));
                    ctx.moveTo(endX, endY);
                    ctx.lineTo(endX + headlen * Math.cos(angle + Math.PI / 6), endY + headlen * Math.sin(angle + Math.PI / 6));
                    ctx.stroke();

                    ctx.fillStyle = '#DC143C';
                    ctx.fillText(conn.label, (from.x + to.x) / 2 + 50, from.y + from.height + 40);
                }
                if (animationStep >= 4) {
                    // Step 5: Test -> Deploy
                    const conn = connections[4];
                    const from = steps[conn.from];
                    const to = steps[conn.to];
                    drawArrow(ctx, from.x + from.width / 2, from.y + from.height, to.x + to.width / 2, to.y, conn.label, '#3CB371'); // MediumSeaGreen
                }


                animationStep++;
                if (animationStep < connections.length + 2) {
                    processAnimationFrameId = setTimeout(animateProcess, animationSpeed);
                } else {
                    processAnimationFrameId = null;
                }
            }
            if (processAnimationFrameId) clearTimeout(processAnimationFrameId);
            animationStep = 0;
            animateProcess();
        }

        // Initial calls if needed (or wait for user to click buttons)
        // drawArchitecture();
        // startInterfaceDemo();
        // startDataDemo();
        // startProcessDemo();

    </script>
</body>
</html> 