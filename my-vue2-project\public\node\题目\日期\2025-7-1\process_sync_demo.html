<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进程同步 P/V 操作演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            user-select: none;
        }

        .container {
            width: 90%;
            max-width: 900px;
            background-color: #fff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
        }

        h1 {
            color: #1a73e8;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .problem-desc {
            font-size: 1.1em;
            color: #5f6368;
            margin-bottom: 25px;
        }

        .simulation-area {
            position: relative;
            width: 100%;
            height: 220px;
            margin: 20px auto;
            transform: scale(0.9);
        }

        .process {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: #e0e0e0;
            border: 2px solid #9e9e9e;
            color: #616161;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 1.2em;
            transition: background-color 0.5s, border-color 0.5s, transform 0.3s;
        }
        
        .process.waiting { background-color: #e0e0e0; border-color: #9e9e9e; }
        .process.running { background-color: #ffeb3b; border-color: #fbc02d; transform: scale(1.1); }
        .process.finished { background-color: #81c784; border-color: #4caf50; }

        #p1 { top: 70px; left: 50px; }
        #p2 { top: 0px; left: 250px; }
        #p3 { top: 140px; left: 250px; }
        #p4 { top: 70px; left: 450px; }
        #p5 { top: 70px; left: 650px; }

        .arrow {
            position: absolute;
            stroke: #616161;
            stroke-width: 2;
            fill: none;
        }

        .signal {
            position: absolute;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, #ffeb3b, #fbc02d);
            border-radius: 50%;
            opacity: 0;
            transition: all 1s ease-out;
            font-size: 0.8em;
            text-align: center;
            line-height: 20px;
            color: #333;
            font-weight: bold;
        }

        .interactive-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .question h3 {
            margin-top: 0;
        }

        .options label {
            display: block;
            margin: 10px auto;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s, border-color 0.3s;
            max-width: 300px;
        }
        .options label:hover {
            background-color: #e9f1ff;
            border-color: #1a73e8;
        }
        .options input {
            margin-right: 10px;
        }
        
        .controls {
            margin-top: 20px;
        }

        button {
            padding: 12px 25px;
            font-size: 1em;
            cursor: pointer;
            border: none;
            border-radius: 8px;
            color: #fff;
            background-color: #1a73e8;
            transition: background-color 0.3s, transform 0.2s;
        }
        button:hover {
            background-color: #155ab6;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            margin-top: 15px;
            font-size: 1.1em;
            font-weight: bold;
            height: 25px;
        }
        .message.correct { color: #2e7d32; }
        .message.incorrect { color: #d32f2f; }
        
        .explanation {
            margin-top: 25px;
            padding: 15px;
            background-color: #e9f1ff;
            border-left: 5px solid #1a73e8;
            text-align: left;
            display: none;
        }

    </style>
</head>
<body>

    <div class="container">
        <h1>P/V 操作交互式学习</h1>
        <p class="problem-desc">根据前趋图，P1 进程运行结束后，需要利用什么操作来通知 P2 和 P3 进程？</p>

        <div class="simulation-area">
            <!-- Processes -->
            <div id="p1" class="process">P1</div>
            <div id="p2" class="process">P2</div>
            <div id="p3" class="process">P3</div>
            <div id="p4" class="process">P4</div>
            <div id="p5" class="process">P5</div>

            <!-- Arrows -->
            <svg class="arrow" style="width:100%; height:100%; position:absolute; top:0; left:0; pointer-events: none;">
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#616161" />
                    </marker>
                </defs>
                <line x1="115" y1="100" x2="245" y2="30" marker-end="url(#arrowhead)" />
                <line x1="115" y1="100" x2="245" y2="170" marker-end="url(#arrowhead)" />
                <line x1="315" y1="30" x2="445" y2="100" marker-end="url(#arrowhead)" />
                <line x1="315" y1="170" x2="445" y2="100" marker-end="url(#arrowhead)" />
                <line x1="515" y1="100" x2="645" y2="100" marker-end="url(#arrowhead)" />
            </svg>
            
            <div id="s1" class="signal">S1</div>
            <div id="s2" class="signal">S2</div>
        </div>

        <div class="interactive-section">
             <div class="question" id="question-section" style="display:none;">
                <h3>P1 已完成！它应该执行什么操作？</h3>
                <div class="options">
                    <label><input type="radio" name="operation" value="A"> A) P(S1) 和 P(S2)</label>
                    <label><input type="radio" name="operation" value="B"> B) V(S1) 和 V(S2)</label>
                    <label><input type="radio" name="operation" value="C"> C) P(S1) 和 V(S2)</label>
                    <label><input type="radio" name="operation" value="D"> D) P(S2) 和 V(S1)</label>
                </div>
            </div>
             <div class="controls">
                <button id="start-btn">开始模拟</button>
                <button id="confirm-btn" style="display:none;">确认选择</button>
            </div>
            <p id="message" class="message"></p>
        </div>

        <div id="explanation" class="explanation">
            <h4>知识点解析</h4>
            <p><strong>前趋图:</strong> 描述进程执行的先后顺序。箭头表示依赖关系，例如 P1 → P2 意味着 P1 必须先完成，P2 才能开始。</p>
            <p><strong>V 操作 (Signal / 唤醒):</strong> 当一个进程完成任务后，用来通知（唤醒）其他等待它的进程。好比接力赛中，跑完的选手把接力棒交给下一位选手。</p>
            <p><strong>P 操作 (Wait / 等待):</strong> 如果一个进程需要等待某个条件才能继续，它就会执行 P 操作。好比下一位选手在起跑线前"等待"接力棒。</p>
            <p><strong>在本例中:</strong> P1 完成后，P2 和 P3 才能开始。所以 P1 需要用 V 操作来"通知"P2 和 P3。因为要通知两个不同的进程，所以需要两个独立的 V 操作：V(S1) 和 V(S2)。</p>
        </div>
    </div>

    <script>
        const p1 = document.getElementById('p1');
        const p2 = document.getElementById('p2');
        const p3 = document.getElementById('p3');
        const p4 = document.getElementById('p4');
        const p5 = document.getElementById('p5');
        const s1 = document.getElementById('s1');
        const s2 = document.getElementById('s2');

        const startBtn = document.getElementById('start-btn');
        const confirmBtn = document.getElementById('confirm-btn');
        const questionSection = document.getElementById('question-section');
        const messageEl = document.getElementById('message');
        const explanationEl = document.getElementById('explanation');

        const processes = [p1, p2, p3, p4, p5];

        function resetState() {
            processes.forEach(p => p.className = 'process waiting');
            messageEl.textContent = '';
            messageEl.className = 'message';
            startBtn.style.display = 'inline-block';
            startBtn.disabled = false;
            confirmBtn.style.display = 'none';
            questionSection.style.display = 'none';
            explanationEl.style.display = 'none';
            s1.style.opacity = 0;
            s2.style.opacity = 0;
            const radios = document.querySelectorAll('input[name="operation"]');
            radios.forEach(r => r.checked = false);
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        async function runProcess(p, duration = 1500) {
            messageEl.textContent = `${p.id.toUpperCase()} 正在运行...`;
            p.classList.remove('waiting');
            p.classList.add('running');
            await sleep(duration);
            p.classList.remove('running');
            p.classList.add('finished');
            messageEl.textContent = `${p.id.toUpperCase()} 运行结束.`;
        }

        async function triggerSignal(signalEl, fromEl, toEl) {
            signalEl.style.transition = 'none';
            signalEl.style.left = fromEl.offsetLeft + 20 + 'px';
            signalEl.style.top = fromEl.offsetTop + 20 + 'px';
            signalEl.style.opacity = 1;
            
            await sleep(10); // allow styles to apply
            
            signalEl.style.transition = 'all 1s ease-out';
            signalEl.style.left = toEl.offsetLeft + 20 + 'px';
            signalEl.style.top = toEl.offsetTop + 20 + 'px';
            
            await sleep(1000);
            signalEl.style.opacity = 0;
        }

        startBtn.addEventListener('click', async () => {
            resetState();
            startBtn.disabled = true;
            messageEl.textContent = '模拟开始！';
            
            await runProcess(p1);
            
            messageEl.textContent = 'P1 已完成。请选择它接下来的操作。';
            questionSection.style.display = 'block';
            startBtn.style.display = 'none';
            confirmBtn.style.display = 'inline-block';
        });

        confirmBtn.addEventListener('click', async () => {
            const selected = document.querySelector('input[name="operation"]:checked');
            if (!selected) {
                messageEl.textContent = '请先选择一个选项！';
                messageEl.className = 'message incorrect';
                return;
            }

            if (selected.value === 'B') {
                confirmBtn.disabled = true;
                messageEl.textContent = '正确！P1 使用 V 操作来通知 P2 和 P3。';
                messageEl.className = 'message correct';
                questionSection.style.display = 'none';
                explanationEl.style.display = 'block';

                await sleep(1000);

                messageEl.textContent = 'P1 发出信号 S1 和 S2...';
                await Promise.all([
                    triggerSignal(s1, p1, p2),
                    triggerSignal(s2, p1, p3)
                ]);

                // P2 and P3 run in parallel
                const p2Promise = runProcess(p2);
                const p3Promise = runProcess(p3);
                await Promise.all([p2Promise, p3Promise]);
                
                // P4 runs
                await runProcess(p4);
                
                // P5 runs
                await runProcess(p5);
                
                messageEl.textContent = '所有进程执行完毕！';
                startBtn.textContent = '重新开始';
                startBtn.disabled = false;
                startBtn.style.display = 'inline-block';
                confirmBtn.style.display = 'none';

            } else {
                messageEl.textContent = '错误！P 操作是等待，V 操作才是通知。请再想想。';
                messageEl.className = 'message incorrect';
                explanationEl.style.display = 'block';
            }
        });

        // Initial setup
        resetState();
    </script>
</body>
</html> 