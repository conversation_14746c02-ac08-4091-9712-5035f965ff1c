<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL 互动学习之旅</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 4px 8px rgba(0,0,0,0.3); }
            to { text-shadow: 0 4px 20px rgba(255,255,255,0.5); }
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .text-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: justify;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .interactive-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .game-area {
            background: rgba(240,240,240,0.8);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 1s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            to { opacity: 1; transform: translateY(0); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">MySQL 学习之旅</h1>
            <p class="subtitle">探索关系型数据库的奇妙世界</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">什么是MySQL？</h2>
            <div class="canvas-container">
                <canvas id="mysqlIntroCanvas" width="600" height="300"></canvas>
            </div>
            <div class="text-content">
                <span class="highlight">MySQL</span>是一个<span class="highlight">关系型数据库管理系统</span>，
                由瑞典MySQLAB公司开发，现在属于Oracle旗下产品。想象一下，数据库就像一个超级智能的图书馆，
                而MySQL就是这个图书馆的管理员，帮助我们存储、查找和管理数据。
            </div>
            <button class="interactive-button" onclick="startMySQLAnimation()">开始MySQL动画演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">为什么MySQL如此流行？</h2>
            <div class="canvas-container">
                <canvas id="popularityCanvas" width="600" height="300"></canvas>
            </div>
            <div class="text-content">
                MySQL是<span class="highlight">最流行的关系型数据库管理系统之一</span>，
                在WEB应用方面表现卓越。它就像是数据世界的"瑞士军刀"，功能强大且易于使用。
            </div>
            <div class="game-area">
                <h3>互动小游戏：MySQL优势连连看</h3>
                <button class="interactive-button" onclick="startAdvantageGame()">开始游戏</button>
                <div id="gameResult"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">MySQL在Java开发中的应用</h2>
            <div class="canvas-container">
                <canvas id="javaCanvas" width="600" height="300"></canvas>
            </div>
            <div class="text-content">
                在<span class="highlight">Java企业级开发</span>中，MySQL非常常用。
                这是因为MySQL是<span class="highlight">开源免费</span>的，并且<span class="highlight">方便扩展</span>。
                就像乐高积木一样，你可以根据需要自由组合和扩展。
            </div>
            <button class="interactive-button" onclick="showJavaIntegration()">查看Java集成演示</button>
        </div>
    </div>

    <script>
        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 20; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(element);
            }
        }

        // MySQL介绍动画
        function startMySQLAnimation() {
            const canvas = document.getElementById('mysqlIntroCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制MySQL logo动画
                ctx.save();
                ctx.translate(300, 150);
                ctx.rotate(frame * 0.02);
                
                // MySQL圆形背景
                ctx.beginPath();
                ctx.arc(0, 0, 80 + Math.sin(frame * 0.1) * 10, 0, Math.PI * 2);
                ctx.fillStyle = `hsl(${frame % 360}, 70%, 60%)`;
                ctx.fill();
                
                // MySQL文字
                ctx.restore();
                ctx.fillStyle = '#333';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('MySQL', 300, 160);
                
                // 数据流动效果
                for (let i = 0; i < 5; i++) {
                    const x = 100 + (frame + i * 50) % 400;
                    const y = 100 + Math.sin((frame + i * 50) * 0.02) * 20;
                    
                    ctx.beginPath();
                    ctx.arc(x, y, 5, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(102, 126, 234, ${0.8 - (frame + i * 50) % 400 / 500})`;
                    ctx.fill();
                }
                
                frame++;
                if (frame < 300) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
            updateProgress(33);
        }

        // 流行度展示动画
        function startPopularityAnimation() {
            const canvas = document.getElementById('popularityCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制流行度图表
                const databases = ['MySQL', 'PostgreSQL', 'Oracle', 'MongoDB'];
                const popularity = [85, 65, 45, 35];
                
                databases.forEach((db, index) => {
                    const barHeight = (popularity[index] / 100) * 200 * Math.min(frame / 60, 1);
                    const x = 100 + index * 120;
                    const y = 250 - barHeight;
                    
                    // 绘制柱状图
                    ctx.fillStyle = `hsl(${index * 60}, 70%, 60%)`;
                    ctx.fillRect(x, y, 80, barHeight);
                    
                    // 绘制标签
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(db, x + 40, 270);
                    ctx.fillText(popularity[index] + '%', x + 40, y - 10);
                });
                
                frame++;
                if (frame < 120) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 优势连连看游戏
        function startAdvantageGame() {
            const advantages = ['开源免费', '高性能', '易于使用', '广泛支持', '可扩展性强'];
            const gameResult = document.getElementById('gameResult');
            let score = 0;
            
            gameResult.innerHTML = '<h4>点击MySQL的优势特点：</h4>';
            
            advantages.forEach((advantage, index) => {
                const button = document.createElement('button');
                button.className = 'interactive-button';
                button.textContent = advantage;
                button.onclick = () => {
                    score++;
                    button.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
                    button.disabled = true;
                    
                    if (score === advantages.length) {
                        gameResult.innerHTML += '<p style="color: #4CAF50; font-weight: bold;">恭喜！你已经掌握了MySQL的所有优势！</p>';
                        updateProgress(66);
                    }
                };
                gameResult.appendChild(button);
            });
        }

        // Java集成演示
        function showJavaIntegration() {
            const canvas = document.getElementById('javaCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // Java logo
                ctx.fillStyle = '#f89820';
                ctx.fillRect(100, 100, 100, 100);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('JAVA', 150, 160);
                
                // 连接线动画
                const progress = Math.min(frame / 60, 1);
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(200, 150);
                ctx.lineTo(200 + 200 * progress, 150);
                ctx.stroke();
                
                // MySQL logo
                if (progress > 0.5) {
                    ctx.fillStyle = '#00758f';
                    ctx.fillRect(400, 100, 100, 100);
                    ctx.fillStyle = 'white';
                    ctx.fillText('MySQL', 450, 160);
                }
                
                // 数据传输动画
                if (progress === 1) {
                    const dataX = 250 + Math.sin(frame * 0.1) * 50;
                    ctx.beginPath();
                    ctx.arc(dataX, 150, 8, 0, Math.PI * 2);
                    ctx.fillStyle = '#4CAF50';
                    ctx.fill();
                }
                
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress(100);
                }
            }
            animate();
        }

        // 更新进度条
        function updateProgress(percentage) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = percentage + '%';
        }

        // 初始化
        window.onload = function() {
            createFloatingElements();
            startPopularityAnimation();
            
            // 自动开始介绍动画
            setTimeout(() => {
                startMySQLAnimation();
            }, 1000);
        };
    </script>
</body>
</html>
