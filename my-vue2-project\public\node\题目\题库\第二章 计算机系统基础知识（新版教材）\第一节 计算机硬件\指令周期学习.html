<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机指令周期 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInDown 1s ease-out;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInUp 1s ease-out;
        }

        .question-text {
            font-size: 1.4em;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #333;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            padding: 20px;
            border: 3px solid #e0e0e0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.2em;
            background: #f8f9fa;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #28a745;
            background: #d4edda;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #f8d7da;
            animation: shake 0.6s ease-in-out;
        }

        .simulation-area {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .cpu-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 30px;
        }

        .component {
            background: linear-gradient(145deg, #f0f0f0, #ffffff);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .component:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.15);
        }

        .component h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .component-value {
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            color: #667eea;
            font-weight: bold;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-top: 10px;
        }

        .memory-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 10px;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .memory-cell {
            aspect-ratio: 1;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .memory-cell.active {
            border-color: #667eea;
            background: #e3f2fd;
            animation: glow 1s ease-in-out infinite alternate;
        }

        .bus {
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            margin: 20px 0;
            border-radius: 2px;
            position: relative;
            overflow: hidden;
        }

        .bus::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
            animation: busFlow 2s linear infinite;
        }

        .control-panel {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .step {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .step.active {
            opacity: 1;
            transform: translateX(10px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes glow {
            from { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
            to { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
        }

        @keyframes busFlow {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .highlight {
            background: #fff3cd !important;
            border-color: #ffc107 !important;
            animation: highlight 1s ease-in-out;
        }

        @keyframes highlight {
            0%, 100% { background: #fff3cd; }
            50% { background: #ffeaa7; }
        }

        @media (max-width: 768px) {
            .cpu-container {
                flex-direction: column;
            }
            
            .options {
                grid-template-columns: 1fr;
            }
            
            .memory-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ 计算机指令周期学习</h1>
            <p>让我们一起探索CPU是如何执行指令的神奇过程！</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>计算机执行程序时，在一个指令周期的过程中，为了能够从内存中读指令操作码，首先是将（ ）的内容送到地址总线上。
            </div>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A</strong> 程序计数器PC
                </div>
                <div class="option" data-answer="B">
                    <strong>B</strong> 指令寄存器IR
                </div>
                <div class="option" data-answer="C">
                    <strong>C</strong> 状态寄存器SR
                </div>
                <div class="option" data-answer="D">
                    <strong>D</strong> 通用寄存器GR
                </div>
            </div>
            
            <div id="result" style="text-align: center; font-size: 1.2em; margin-top: 20px;"></div>
        </div>

        <div class="simulation-area">
            <h2 style="text-align: center; color: #333; margin-bottom: 30px;">🎯 CPU指令执行模拟器</h2>
            
            <div class="cpu-container">
                <div class="component" id="pc">
                    <h3>程序计数器 (PC)</h3>
                    <div class="component-value" id="pc-value">1000</div>
                    <small>存储下一条指令的地址</small>
                </div>
                
                <div class="component" id="ir">
                    <h3>指令寄存器 (IR)</h3>
                    <div class="component-value" id="ir-value">空</div>
                    <small>存储当前执行的指令</small>
                </div>
                
                <div class="component" id="sr">
                    <h3>状态寄存器 (SR)</h3>
                    <div class="component-value" id="sr-value">0000</div>
                    <small>存储CPU状态信息</small>
                </div>
                
                <div class="component" id="gr">
                    <h3>通用寄存器 (GR)</h3>
                    <div class="component-value" id="gr-value">0</div>
                    <small>存储数据和运算结果</small>
                </div>
            </div>

            <div class="bus" id="address-bus"></div>
            <div style="text-align: center; margin: 10px 0; color: #666;">📡 地址总线</div>

            <div class="memory-grid" id="memory">
                <!-- 内存单元将通过JavaScript生成 -->
            </div>
            <div style="text-align: center; color: #666;">💾 内存区域</div>

            <div class="control-panel">
                <button class="btn" onclick="startSimulation()">🚀 开始模拟</button>
                <button class="btn" onclick="resetSimulation()">🔄 重置</button>
                <button class="btn" onclick="stepByStep()">👣 单步执行</button>
            </div>
        </div>

        <div class="explanation">
            <h2 style="color: #333; margin-bottom: 20px;">📚 知识点详解</h2>
            
            <div class="step" id="step1">
                <h3>🎯 第一步：理解程序计数器(PC)</h3>
                <p>程序计数器就像是一个"地址指针"，它总是指向下一条要执行的指令在内存中的位置。就像你看书时用手指指着下一行要读的文字一样！</p>
            </div>
            
            <div class="step" id="step2">
                <h3>🔍 第二步：取指令过程</h3>
                <p>当CPU要执行指令时，首先需要知道指令在内存的哪个位置。这时候就需要把PC中存储的地址发送到地址总线上，告诉内存"我要读取这个地址的内容"。</p>
            </div>
            
            <div class="step" id="step3">
                <h3>📖 第三步：其他寄存器的作用</h3>
                <p>• IR(指令寄存器)：存储从内存读取到的指令<br>
                • SR(状态寄存器)：记录CPU的工作状态<br>
                • GR(通用寄存器)：存储数据和计算结果</p>
            </div>
            
            <div class="step" id="step4">
                <h3>💡 解题思路</h3>
                <p>关键词是"首先"和"读指令操作码"。在指令周期开始时，CPU首先要知道去哪里读取指令，这个"哪里"就是地址，而地址存储在程序计数器PC中！</p>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let simulationRunning = false;

        // 初始化内存网格
        function initMemory() {
            const memory = document.getElementById('memory');
            memory.innerHTML = '';
            
            for (let i = 0; i < 32; i++) {
                const cell = document.createElement('div');
                cell.className = 'memory-cell';
                cell.textContent = (1000 + i).toString();
                cell.id = `mem-${1000 + i}`;
                memory.appendChild(cell);
            }
            
            // 在地址1000处放置一条示例指令
            document.getElementById('mem-1000').textContent = 'ADD R1,R2';
            document.getElementById('mem-1000').style.background = '#e8f5e8';
        }

        // 题目选择处理
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const result = document.getElementById('result');
                
                // 清除之前的样式
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    result.innerHTML = '🎉 <span style="color: #28a745;">正确！</span> 程序计数器PC确实是首先将内容送到地址总线的！';
                    setTimeout(() => {
                        document.getElementById('step1').classList.add('active');
                    }, 1000);
                } else {
                    this.classList.add('wrong');
                    result.innerHTML = '❌ <span style="color: #dc3545;">不正确</span>，再想想看，哪个寄存器存储的是指令的地址？';
                }
            });
        });

        // 开始模拟
        function startSimulation() {
            if (simulationRunning) return;
            simulationRunning = true;
            currentStep = 0;
            
            resetHighlights();
            
            // 步骤1：PC发送地址到地址总线
            setTimeout(() => {
                highlightComponent('pc');
                document.getElementById('step1').classList.add('active');
                showMessage('PC将地址1000发送到地址总线');
            }, 500);
            
            // 步骤2：地址总线传输
            setTimeout(() => {
                document.getElementById('address-bus').style.animation = 'busFlow 1s linear 3';
                document.getElementById('step2').classList.add('active');
                showMessage('地址总线传输地址信息到内存');
            }, 2000);
            
            // 步骤3：内存响应
            setTimeout(() => {
                document.getElementById('mem-1000').classList.add('active');
                document.getElementById('step3').classList.add('active');
                showMessage('内存根据地址1000找到对应的指令');
            }, 4000);
            
            // 步骤4：指令传输到IR
            setTimeout(() => {
                document.getElementById('ir-value').textContent = 'ADD R1,R2';
                highlightComponent('ir');
                document.getElementById('step4').classList.add('active');
                showMessage('指令被读取并存储到指令寄存器IR中');
            }, 6000);
            
            // 步骤5：PC自增
            setTimeout(() => {
                document.getElementById('pc-value').textContent = '1001';
                highlightComponent('pc');
                showMessage('PC自动增加，指向下一条指令的地址');
                simulationRunning = false;
            }, 8000);
        }

        // 重置模拟
        function resetSimulation() {
            simulationRunning = false;
            currentStep = 0;
            
            document.getElementById('pc-value').textContent = '1000';
            document.getElementById('ir-value').textContent = '空';
            document.getElementById('sr-value').textContent = '0000';
            document.getElementById('gr-value').textContent = '0';
            
            resetHighlights();
            
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active');
            });
            
            initMemory();
        }

        // 单步执行
        function stepByStep() {
            if (simulationRunning) return;
            
            const steps = [
                () => {
                    highlightComponent('pc');
                    document.getElementById('step1').classList.add('active');
                    showMessage('第1步：PC准备发送地址');
                },
                () => {
                    document.getElementById('address-bus').style.animation = 'busFlow 1s linear 1';
                    document.getElementById('step2').classList.add('active');
                    showMessage('第2步：地址通过地址总线传输');
                },
                () => {
                    document.getElementById('mem-1000').classList.add('active');
                    document.getElementById('step3').classList.add('active');
                    showMessage('第3步：内存定位到指定地址');
                },
                () => {
                    document.getElementById('ir-value').textContent = 'ADD R1,R2';
                    highlightComponent('ir');
                    document.getElementById('step4').classList.add('active');
                    showMessage('第4步：指令读取到IR寄存器');
                }
            ];
            
            if (currentStep < steps.length) {
                steps[currentStep]();
                currentStep++;
            } else {
                showMessage('单步执行完成！点击重置开始新的演示');
            }
        }

        // 高亮组件
        function highlightComponent(id) {
            resetHighlights();
            document.getElementById(id).classList.add('highlight');
        }

        // 重置高亮
        function resetHighlights() {
            document.querySelectorAll('.component, .memory-cell').forEach(el => {
                el.classList.remove('highlight', 'active');
            });
        }

        // 显示消息
        function showMessage(message) {
            // 可以在这里添加消息显示逻辑
            console.log(message);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initMemory();
            
            // 添加一些随机的内存内容
            setTimeout(() => {
                for (let i = 1001; i <= 1010; i++) {
                    const cell = document.getElementById(`mem-${i}`);
                    if (cell) {
                        const instructions = ['MOV', 'ADD', 'SUB', 'JMP', 'CMP', 'LOAD', 'STORE'];
                        cell.textContent = instructions[Math.floor(Math.random() * instructions.length)];
                    }
                }
            }, 1000);
        });
    </script>
</body>
</html>
