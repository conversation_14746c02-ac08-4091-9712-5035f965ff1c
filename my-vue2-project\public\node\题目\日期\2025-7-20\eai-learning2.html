<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统测试内容学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .question-card {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-text {
            font-size: 1.4rem;
            color: #2d3748;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border: 2px solid transparent;
            border-radius: 16px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .option.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.05);
        }

        .option.correct {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .option.wrong {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 12px;
            cursor: pointer;
        }

        .explanation {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            backdrop-filter: blur(10px);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.9s forwards;
        }

        .explanation h3 {
            color: #2d3748;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .integration-type {
            background: #f7fafc;
            border-radius: 16px;
            padding: 24px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .integration-type:hover {
            transform: translateX(8px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .integration-type h4 {
            color: #2d3748;
            font-size: 1.3rem;
            margin-bottom: 12px;
        }

        .integration-type p {
            color: #4a5568;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 600;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">系统测试内容学习</h1>
            <p class="subtitle">通过互动动画学习系统测试的各种类型</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>基于SOA和Web Service技术的企业应用集成(EAI)模式是______。
            </div>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 面向信息的集成技术
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 面向过程的集成技术
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 面向计划的集成技术
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 面向服务的集成技术
                </div>
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="checkAnswer()">提交答案</button>
                <button class="btn" onclick="startAnimation()">开始动画演示</button>
                <button class="btn" onclick="resetGame()">重新开始</button>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="gameCanvas"></canvas>
        </div>

        <div class="explanation">
            <h3>🎯 知识点详解</h3>
            
            <div class="integration-type">
                <h4>🔄 面向信息的集成技术</h4>
                <p>主要通过<span class="highlight">数据复制、数据聚合和接口集成</span>等技术实现。使用适配器作为代理，通过开放接口提取和交互信息。</p>
            </div>

            <div class="integration-type">
                <h4>⚙️ 面向过程的集成技术</h4>
                <p>采用<span class="highlight">过程流集成</span>思想，将系统间的过程逻辑与核心业务逻辑分离，在接口集成基础上定义过程逻辑层。</p>
            </div>

            <div class="integration-type">
                <h4>🌐 面向服务的集成技术 (正确答案)</h4>
                <p>基于<span class="highlight">SOA架构和Web Services技术</span>，是新一代应用集成技术。使用HTTP、XML等开放标准，支持服务描述分离、集中化存储、自动查找和动态绑定。</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        let selectedAnswer = null;
        let animationRunning = false;
        let particles = [];
        let services = [];
        let connections = [];

        // 粒子系统
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = (Math.random() - 0.5) * 4;
                this.color = color;
                this.life = 1;
                this.decay = 0.02;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.vx *= 0.99;
                this.vy *= 0.99;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 服务节点
        class Service {
            constructor(x, y, name, type) {
                this.x = x;
                this.y = y;
                this.name = name;
                this.type = type;
                this.radius = 40;
                this.pulse = 0;
                this.connected = false;
            }

            update() {
                this.pulse += 0.1;
            }

            draw() {
                const pulseSize = Math.sin(this.pulse) * 5;
                
                // 绘制服务节点
                ctx.save();
                ctx.fillStyle = this.getColor();
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius + pulseSize, 0, Math.PI * 2);
                ctx.fill();
                
                // 绘制边框
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 3;
                ctx.stroke();
                
                // 绘制文字
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(this.name, this.x, this.y + 4);
                ctx.restore();
            }

            getColor() {
                switch(this.type) {
                    case 'info': return '#3182ce';
                    case 'process': return '#38a169';
                    case 'service': return '#805ad5';
                    default: return '#718096';
                }
            }
        }

        // 连接线
        class Connection {
            constructor(from, to, type) {
                this.from = from;
                this.to = to;
                this.type = type;
                this.progress = 0;
                this.active = false;
            }

            update() {
                if (this.active && this.progress < 1) {
                    this.progress += 0.02;
                }
            }

            draw() {
                if (this.progress > 0) {
                    const currentX = this.from.x + (this.to.x - this.from.x) * this.progress;
                    const currentY = this.from.y + (this.to.y - this.from.y) * this.progress;
                    
                    ctx.save();
                    ctx.strokeStyle = this.getColor();
                    ctx.lineWidth = 4;
                    ctx.beginPath();
                    ctx.moveTo(this.from.x, this.from.y);
                    ctx.lineTo(currentX, currentY);
                    ctx.stroke();
                    
                    // 绘制数据流动效果
                    if (this.progress > 0.5) {
                        ctx.fillStyle = this.getColor();
                        ctx.beginPath();
                        ctx.arc(currentX, currentY, 6, 0, Math.PI * 2);
                        ctx.fill();
                    }
                    ctx.restore();
                }
            }

            getColor() {
                switch(this.type) {
                    case 'info': return '#3182ce';
                    case 'process': return '#38a169';
                    case 'service': return '#805ad5';
                    default: return '#718096';
                }
            }
        }

        // 初始化场景
        function initScene() {
            services = [];
            connections = [];
            particles = [];
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 创建不同类型的服务
            services.push(new Service(centerX - 200, centerY - 100, '数据库', 'info'));
            services.push(new Service(centerX, centerY - 100, 'API网关', 'process'));
            services.push(new Service(centerX + 200, centerY - 100, 'Web服务', 'service'));
            
            services.push(new Service(centerX - 100, centerY + 100, '业务逻辑', 'process'));
            services.push(new Service(centerX + 100, centerY + 100, 'SOA服务', 'service'));
        }

        // 动画循环
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f7fafc');
            gradient.addColorStop(1, '#edf2f7');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
            
            // 更新和绘制连接
            connections.forEach(connection => {
                connection.update();
                connection.draw();
            });
            
            // 更新和绘制服务
            services.forEach(service => {
                service.update();
                service.draw();
            });
            
            if (animationRunning) {
                requestAnimationFrame(animate);
            }
        }

        // 选择答案
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedAnswer = this.dataset.answer;
            });
        });

        // 检查答案
        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }
            
            document.querySelectorAll('.option').forEach(option => {
                if (option.dataset.answer === 'D') {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== 'D') {
                    option.classList.add('wrong');
                }
            });
            
            // 添加粒子效果
            const correctOption = document.querySelector('[data-answer="D"]');
            const rect = correctOption.getBoundingClientRect();
            for (let i = 0; i < 20; i++) {
                particles.push(new Particle(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    '#48bb78'
                ));
            }
            
            if (selectedAnswer === 'D') {
                setTimeout(() => alert('🎉 恭喜答对了！面向服务的集成技术是基于SOA和Web Service的新一代集成技术。'), 500);
            } else {
                setTimeout(() => alert('❌ 答案错误。正确答案是D：面向服务的集成技术。请查看下方的详细解释。'), 500);
            }
        }

        // 开始动画演示
        function startAnimation() {
            animationRunning = true;
            initScene();
            
            // 延迟激活连接
            setTimeout(() => {
                connections.push(new Connection(services[0], services[1], 'info'));
                connections[0].active = true;
            }, 1000);
            
            setTimeout(() => {
                connections.push(new Connection(services[1], services[2], 'process'));
                connections[1].active = true;
            }, 2000);
            
            setTimeout(() => {
                connections.push(new Connection(services[2], services[4], 'service'));
                connections[2].active = true;
            }, 3000);
            
            animate();
        }

        // 重置游戏
        function resetGame() {
            selectedAnswer = null;
            animationRunning = false;
            particles = [];
            services = [];
            connections = [];
            
            document.querySelectorAll('.option').forEach(option => {
                option.classList.remove('selected', 'correct', 'wrong');
            });
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            initScene();
        }

        // 初始化
        initScene();
        
        // 鼠标交互
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 添加点击粒子效果
            for (let i = 0; i < 10; i++) {
                particles.push(new Particle(x, y, '#667eea'));
            }
            
            if (!animationRunning) {
                animationRunning = true;
                animate();
                setTimeout(() => animationRunning = false, 3000);
            }
        });
    </script>
</body>
</html>
