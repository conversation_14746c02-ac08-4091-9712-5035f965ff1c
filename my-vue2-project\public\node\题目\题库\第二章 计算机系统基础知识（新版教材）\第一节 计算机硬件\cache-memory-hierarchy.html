<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache-主存层次结构 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section h2 {
            color: #4a5568;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .memory-system {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .memory-unit {
            background: linear-gradient(145deg, #f0f4f8, #d6e8f5);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 150px;
        }

        .memory-unit:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .cache {
            background: linear-gradient(145deg, #ffd89b, #19547b);
            color: white;
        }

        .main-memory {
            background: linear-gradient(145deg, #a8edea, #fed6e3);
        }

        .cpu {
            background: linear-gradient(145deg, #ff9a9e, #fecfef);
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            display: block;
            margin: 0 auto;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: linear-gradient(145deg, #f7fafc, #edf2f7);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .quiz-section {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-option {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .quiz-option.correct {
            background: rgba(72, 187, 120, 0.8);
            border-color: #48bb78;
        }

        .quiz-option.wrong {
            background: rgba(245, 101, 101, 0.8);
            border-color: #f56565;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Cache-主存层次结构</h1>
            <p>通过动画和交互学习计算机存储系统</p>
        </div>

        <div class="section">
            <h2>📚 基础概念介绍</h2>
            <div class="explanation">
                <h3>什么是Cache-主存层次结构？</h3>
                <p>想象一下你的书桌和书柜：</p>
                <p>• <span class="highlight">书桌（Cache）</span>：放着你正在使用的几本书，拿取很快</p>
                <p>• <span class="highlight">书柜（主存）</span>：放着所有的书，容量大但拿取较慢</p>
                <p>• <span class="highlight">CPU</span>：就是你这个"读书人"，需要快速获取信息</p>
            </div>

            <div class="memory-system">
                <div class="memory-unit cpu" onclick="highlightUnit('cpu')">
                    <h3>🔥 CPU</h3>
                    <p>处理器</p>
                    <p>需要数据</p>
                </div>
                <div style="font-size: 2rem; color: #667eea;">→</div>
                <div class="memory-unit cache" onclick="highlightUnit('cache')">
                    <h3>⚡ Cache</h3>
                    <p>高速缓存</p>
                    <p>小而快</p>
                </div>
                <div style="font-size: 2rem; color: #667eea;">↔</div>
                <div class="memory-unit main-memory" onclick="highlightUnit('memory')">
                    <h3>💾 主存</h3>
                    <p>主存储器</p>
                    <p>大而慢</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎮 交互式地址转换演示</h2>
            <div class="canvas-container">
                <canvas id="addressCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="startAddressTranslation()">开始地址转换</button>
                <button class="btn" onclick="showHardwareProcess()">硬件处理过程</button>
                <button class="btn" onclick="resetAnimation()">重置动画</button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 为什么是硬件完成？</h2>
            <div class="explanation">
                <h3>硬件 vs 软件的速度对比</h3>
                <p>地址转换需要<span class="highlight">极高的速度</span>，因为：</p>
                <p>• CPU每秒要访问内存数十亿次</p>
                <p>• 软件处理会增加额外的指令执行时间</p>
                <p>• 硬件可以并行处理，一个时钟周期内完成</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="speedCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="compareSpeed()">速度对比演示</button>
            </div>
        </div>

        <div class="quiz-section">
            <h2>🎯 知识检测</h2>
            <p style="margin-bottom: 20px;">在Cache-主存层次结构中，主存单元到Cache单元的地址转换由（ ）完成。</p>
            
            <div class="quiz-option" onclick="selectAnswer(this, false)">
                A. 寻址方式
            </div>
            <div class="quiz-option" onclick="selectAnswer(this, true)">
                B. 硬件
            </div>
            <div class="quiz-option" onclick="selectAnswer(this, false)">
                C. 软件和少量的辅助硬件
            </div>
            <div class="quiz-option" onclick="selectAnswer(this, false)">
                D. 微程序
            </div>
            
            <div id="quizResult" style="margin-top: 20px; font-size: 1.1rem;"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationId;
        let currentStep = 0;
        const totalSteps = 5;

        // Canvas 初始化
        const addressCanvas = document.getElementById('addressCanvas');
        const addressCtx = addressCanvas.getContext('2d');
        const speedCanvas = document.getElementById('speedCanvas');
        const speedCtx = speedCanvas.getContext('2d');

        // 初始化画布
        function initCanvas() {
            // 地址转换画布
            addressCtx.fillStyle = '#f7fafc';
            addressCtx.fillRect(0, 0, addressCanvas.width, addressCanvas.height);
            
            // 绘制基本组件
            drawComponent(addressCtx, 50, 150, 120, 80, 'CPU', '#ff9a9e');
            drawComponent(addressCtx, 300, 100, 150, 100, 'Cache', '#ffd89b');
            drawComponent(addressCtx, 550, 150, 150, 80, '主存', '#a8edea');
            
            // 速度对比画布
            speedCtx.fillStyle = '#f7fafc';
            speedCtx.fillRect(0, 0, speedCanvas.width, speedCanvas.height);
        }

        // 绘制组件
        function drawComponent(ctx, x, y, width, height, text, color) {
            // 绘制圆角矩形
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, 15);
            ctx.fill();
            
            // 绘制文字
            ctx.fillStyle = '#2d3748';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + width/2, y + height/2 + 5);
        }

        // 绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY, color = '#667eea', animated = false) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const headLength = 15;
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI/6), toY - headLength * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI/6), toY - headLength * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        // 开始地址转换动画
        function startAddressTranslation() {
            currentStep = 0;
            animateAddressTranslation();
        }

        function animateAddressTranslation() {
            addressCtx.clearRect(0, 0, addressCanvas.width, addressCanvas.height);
            initCanvas();
            
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            
            switch(currentStep) {
                case 0:
                    // CPU发出地址请求
                    drawComponent(addressCtx, 50, 150, 120, 80, 'CPU', '#ff6b6b');
                    addressCtx.fillStyle = '#ff6b6b';
                    addressCtx.font = '12px Microsoft YaHei';
                    addressCtx.fillText('发出地址请求', 110, 120);
                    break;
                    
                case 1:
                    // 地址传输到硬件转换器
                    drawArrow(addressCtx, 170, 190, 280, 150);
                    addressCtx.fillStyle = '#667eea';
                    addressCtx.font = '12px Microsoft YaHei';
                    addressCtx.fillText('地址信号', 225, 165);
                    break;
                    
                case 2:
                    // 硬件进行地址转换
                    drawComponent(addressCtx, 300, 100, 150, 100, 'Cache\n硬件转换', '#4ecdc4');
                    addressCtx.fillStyle = '#4ecdc4';
                    addressCtx.font = '12px Microsoft YaHei';
                    addressCtx.fillText('硬件转换中...', 375, 250);
                    break;
                    
                case 3:
                    // 检查Cache是否命中
                    drawComponent(addressCtx, 300, 100, 150, 100, 'Cache\n检查命中', '#45b7d1');
                    addressCtx.fillStyle = '#45b7d1';
                    addressCtx.font = '12px Microsoft YaHei';
                    addressCtx.fillText('检查是否命中', 375, 250);
                    break;
                    
                case 4:
                    // 返回数据或访问主存
                    drawArrow(addressCtx, 450, 150, 550, 190);
                    addressCtx.fillStyle = '#f39c12';
                    addressCtx.font = '12px Microsoft YaHei';
                    addressCtx.fillText('未命中，访问主存', 500, 165);
                    break;
            }
            
            currentStep++;
            if (currentStep <= totalSteps) {
                setTimeout(animateAddressTranslation, 1500);
            }
        }

        // 显示硬件处理过程
        function showHardwareProcess() {
            addressCtx.clearRect(0, 0, addressCanvas.width, addressCanvas.height);
            
            // 绘制硬件组件详细图
            drawComponent(addressCtx, 100, 50, 200, 60, '地址译码器', '#e74c3c');
            drawComponent(addressCtx, 100, 150, 200, 60, '比较器', '#3498db');
            drawComponent(addressCtx, 100, 250, 200, 60, '控制逻辑', '#2ecc71');
            
            drawComponent(addressCtx, 400, 100, 200, 100, 'Cache存储阵列', '#f39c12');
            
            // 绘制连接线
            drawArrow(addressCtx, 300, 80, 400, 120);
            drawArrow(addressCtx, 300, 180, 400, 140);
            drawArrow(addressCtx, 300, 280, 400, 160);
            
            addressCtx.fillStyle = '#2d3748';
            addressCtx.font = 'bold 14px Microsoft YaHei';
            addressCtx.fillText('硬件地址转换组件', 300, 30);
        }

        // 速度对比演示
        function compareSpeed() {
            speedCtx.clearRect(0, 0, speedCanvas.width, speedCanvas.height);
            
            // 绘制速度对比图
            const hardwareTime = 50;
            const softwareTime = 300;
            
            // 硬件处理时间
            speedCtx.fillStyle = '#2ecc71';
            speedCtx.fillRect(100, 50, hardwareTime, 40);
            speedCtx.fillStyle = '#2d3748';
            speedCtx.font = '14px Microsoft YaHei';
            speedCtx.fillText('硬件处理: 1个时钟周期', 100, 110);
            
            // 软件处理时间
            speedCtx.fillStyle = '#e74c3c';
            speedCtx.fillRect(100, 150, softwareTime, 40);
            speedCtx.fillText('软件处理: 多个时钟周期', 100, 210);
            
            // 标题
            speedCtx.font = 'bold 16px Microsoft YaHei';
            speedCtx.fillText('处理速度对比', 300, 30);
            
            // 动画效果
            let width = 0;
            const animateBar = () => {
                if (width < hardwareTime) {
                    speedCtx.fillStyle = '#2ecc71';
                    speedCtx.fillRect(100, 50, width, 40);
                    width += 2;
                    requestAnimationFrame(animateBar);
                } else {
                    // 开始软件动画
                    let softWidth = 0;
                    const animateSoftBar = () => {
                        if (softWidth < softwareTime) {
                            speedCtx.fillStyle = '#e74c3c';
                            speedCtx.fillRect(100, 150, softWidth, 40);
                            softWidth += 3;
                            requestAnimationFrame(animateSoftBar);
                        }
                    };
                    setTimeout(animateSoftBar, 500);
                }
            };
            animateBar();
        }

        // 重置动画
        function resetAnimation() {
            currentStep = 0;
            document.getElementById('progressFill').style.width = '0%';
            initCanvas();
        }

        // 高亮单元
        function highlightUnit(unit) {
            const units = document.querySelectorAll('.memory-unit');
            units.forEach(u => u.classList.remove('pulse'));
            
            const targetUnit = document.querySelector(`.${unit}`);
            if (targetUnit) {
                targetUnit.classList.add('pulse');
                setTimeout(() => targetUnit.classList.remove('pulse'), 2000);
            }
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (opt.textContent.includes('B. 硬件')) {
                    opt.classList.add('correct');
                }
            });
            
            const result = document.getElementById('quizResult');
            if (isCorrect) {
                result.innerHTML = '🎉 正确！地址转换由硬件完成，确保了极高的处理速度。';
                result.style.color = '#48bb78';
            } else {
                result.innerHTML = '❌ 不正确。正确答案是B-硬件。硬件处理可以在一个时钟周期内完成地址转换。';
                result.style.color = '#f56565';
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            initCanvas();
        };
    </script>
</body>
</html>
