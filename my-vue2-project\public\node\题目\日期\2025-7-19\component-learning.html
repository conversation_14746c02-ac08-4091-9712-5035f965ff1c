<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件构件（Component）互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .demo-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .component-demo {
            text-align: center;
            margin: 30px 0;
        }

        #componentCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .demo-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 25px 0;
            flex-wrap: wrap;
        }

        .demo-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
        }

        .component-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .object-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .service-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .subroutine-btn {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }

        .demo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .concept-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .concept-card.component {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .concept-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .concept-card p {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-component {
            color: #0984e3;
            font-weight: bold;
            background: rgba(116,185,255,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-component {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            animation: floatComponent 15s infinite ease-in-out;
        }

        .comp1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .comp2 {
            top: 70%;
            right: 15%;
            animation-delay: 5s;
        }

        .comp3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatComponent {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .concept-cards {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-component comp1"></div>
        <div class="floating-component comp2"></div>
        <div class="floating-component comp3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🧩 软件构件学习实验室</h1>
            <p>通过互动动画深度理解Component的自包容与可复用特性</p>
        </div>

        <div class="main-grid">
            <div class="demo-section">
                <h2 class="section-title">🔬 构件特性演示</h2>
                
                <div class="component-demo">
                    <canvas id="componentCanvas" width="500" height="350"></canvas>
                </div>

                <div class="demo-controls">
                    <button class="demo-btn component-btn" onclick="demonstrateComponent()">构件演示</button>
                    <button class="demo-btn object-btn" onclick="demonstrateObject()">对象演示</button>
                    <button class="demo-btn service-btn" onclick="demonstrateService()">服务演示</button>
                    <button class="demo-btn subroutine-btn" onclick="demonstrateSubroutine()">子程序演示</button>
                </div>

                <div class="concept-cards">
                    <div class="concept-card component" onclick="showConceptInfo('component')">
                        <h3>🧩 构件 (Component)</h3>
                        <p>自包容、可复用的程序集<br>通过接口提供服务</p>
                    </div>
                    <div class="concept-card" onclick="showConceptInfo('object')">
                        <h3>🎯 对象 (Object)</h3>
                        <p>面向对象编程的基本单元<br>封装数据和方法</p>
                    </div>
                    <div class="concept-card" onclick="showConceptInfo('service')">
                        <h3>🌐 服务 (Service)</h3>
                        <p>网络上的功能提供者<br>通过协议访问</p>
                    </div>
                    <div class="concept-card" onclick="showConceptInfo('subroutine')">
                        <h3>⚙️ 子程序</h3>
                        <p>模块化编程的基本单元<br>执行特定功能</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 （　　）是一个自包容、可复用的程序集，外界通过接口访问其提供的服务。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. 面向对象系统中的对象（Object）
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 模块化程序设计中的子程序（Subroutine）
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        C. 基于构件开发中的构件（Component）
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        D. 面向服务开发中的服务（Service）
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：C. 基于构件开发中的构件（Component）</strong></p>
                    <ul>
                        <li><span class="highlight-component">构件（Component）</span>的核心特征：
                            <br>• <strong>自包容</strong>：内部实现完全封装，外部无法直接访问
                            <br>• <strong>可复用</strong>：可以在不同系统中重复使用
                            <br>• <strong>程序集</strong>：由多个程序组成的集合
                            <br>• <strong>接口访问</strong>：只能通过统一接口访问服务</li>
                        <li><strong>与其他概念的区别</strong>：
                            <br>• 对象：面向对象编程的基本单元，粒度较小
                            <br>• 子程序：模块化编程的功能单元，不强调复用性
                            <br>• 服务：网络环境下的功能提供者，更偏向分布式</li>
                    </ul>
                    <p><strong>关键理解</strong>：构件是比对象更大粒度的软件单元，强调自包容性和可复用性，是现代软件工程中重要的复用技术。</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了软件构件的核心概念！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('componentCanvas');
        const ctx = canvas.getContext('2d');
        let currentDemo = null;

        // 绘制基础结构
        function drawBaseStructure() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 演示构件特性
        function demonstrateComponent() {
            if (currentDemo) clearInterval(currentDemo);
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制构件外壳
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(150, 80, 200, 180);
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 4;
            ctx.strokeRect(150, 80, 200, 180);

            // 构件标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Component 构件', 250, 110);

            // 内部程序集
            const programs = [
                { x: 170, y: 130, name: 'Program A' },
                { x: 270, y: 130, name: 'Program B' },
                { x: 170, y: 180, name: 'Program C' },
                { x: 270, y: 180, name: 'Program D' }
            ];

            programs.forEach(prog => {
                ctx.fillStyle = '#e9ecef';
                ctx.fillRect(prog.x, prog.y, 60, 30);
                ctx.strokeStyle = '#6c757d';
                ctx.lineWidth = 2;
                ctx.strokeRect(prog.x, prog.y, 60, 30);
                
                ctx.fillStyle = '#495057';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(prog.name, prog.x + 30, prog.y + 20);
            });

            // 接口
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(220, 60, 60, 20);
            ctx.strokeStyle = '#e84393';
            ctx.lineWidth = 2;
            ctx.strokeRect(220, 60, 60, 20);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Interface', 250, 74);

            // 外部访问箭头
            ctx.strokeStyle = '#00b894';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(250, 30);
            ctx.lineTo(250, 60);
            ctx.stroke();
            
            // 箭头头部
            ctx.fillStyle = '#00b894';
            ctx.beginPath();
            ctx.moveTo(250, 60);
            ctx.lineTo(245, 50);
            ctx.lineTo(255, 50);
            ctx.closePath();
            ctx.fill();

            // 特性标签
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 自包容', 380, 120);
            ctx.fillText('✓ 可复用', 380, 140);
            ctx.fillText('✓ 程序集', 380, 160);
            ctx.fillText('✓ 接口访问', 380, 180);

            // 动画效果
            let alpha = 0;
            let increasing = true;
            
            currentDemo = setInterval(() => {
                // 重绘基础结构
                demonstrateComponent();
                
                // 闪烁效果
                ctx.globalAlpha = alpha;
                ctx.fillStyle = '#fdcb6e';
                ctx.fillRect(220, 60, 60, 20);
                ctx.globalAlpha = 1;
                
                if (increasing) {
                    alpha += 0.1;
                    if (alpha >= 1) increasing = false;
                } else {
                    alpha -= 0.1;
                    if (alpha <= 0) increasing = true;
                }
            }, 100);
        }

        // 演示对象特性
        function demonstrateObject() {
            if (currentDemo) clearInterval(currentDemo);
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制对象
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(200, 100, 100, 120);
            ctx.strokeStyle = '#e84393';
            ctx.lineWidth = 3;
            ctx.strokeRect(200, 100, 100, 120);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Object', 250, 125);

            // 数据区域
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(210, 140, 80, 30);
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 1;
            ctx.strokeRect(210, 140, 80, 30);
            
            ctx.fillStyle = '#495057';
            ctx.font = '12px Arial';
            ctx.fillText('Data', 250, 158);

            // 方法区域
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(210, 180, 80, 30);
            ctx.strokeRect(210, 180, 80, 30);
            ctx.fillText('Methods', 250, 198);

            // 特性
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 封装数据和方法', 320, 140);
            ctx.fillText('• 面向对象基本单元', 320, 160);
            ctx.fillText('• 粒度较小', 320, 180);
        }

        // 演示服务特性
        function demonstrateService() {
            if (currentDemo) clearInterval(currentDemo);
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制服务
            ctx.fillStyle = '#00b894';
            ctx.fillRect(200, 100, 100, 120);
            ctx.strokeStyle = '#00a085';
            ctx.lineWidth = 3;
            ctx.strokeRect(200, 100, 100, 120);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Service', 250, 125);

            // 网络图标
            ctx.font = '24px Arial';
            ctx.fillText('🌐', 250, 170);

            // 协议访问
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('HTTP/SOAP', 250, 200);

            // 特性
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 网络功能提供者', 320, 140);
            ctx.fillText('• 通过协议访问', 320, 160);
            ctx.fillText('• 分布式环境', 320, 180);
        }

        // 演示子程序特性
        function demonstrateSubroutine() {
            if (currentDemo) clearInterval(currentDemo);
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制子程序
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(200, 100, 100, 120);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 3;
            ctx.strokeRect(200, 100, 100, 120);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Subroutine', 250, 125);

            // 功能图标
            ctx.font = '24px Arial';
            ctx.fillText('⚙️', 250, 170);

            // 特性
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 执行特定功能', 320, 140);
            ctx.fillText('• 模块化编程单元', 320, 160);
            ctx.fillText('• 不强调复用性', 320, 180);
        }

        // 显示概念信息
        function showConceptInfo(type) {
            const info = {
                component: '构件（Component）：自包容、可复用的程序集，通过统一接口向外提供服务，是软件复用的重要技术。',
                object: '对象（Object）：面向对象编程的基本单元，封装数据和操作数据的方法，支持继承和多态。',
                service: '服务（Service）：网络环境下的功能提供者，通过标准协议（如HTTP、SOAP）提供服务。',
                subroutine: '子程序（Subroutine）：模块化程序设计中的功能单元，执行特定任务，提高代码组织性。'
            };
            
            alert(info[type]);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('C. 基于构件开发中的构件')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画
                    demonstrateComponent();
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateComponent();
            
            // 自动演示序列
            setTimeout(() => {
                demonstrateObject();
            }, 4000);
            
            setTimeout(() => {
                demonstrateService();
            }, 8000);
            
            setTimeout(() => {
                demonstrateSubroutine();
            }, 12000);
            
            setTimeout(() => {
                demonstrateComponent();
            }, 16000);
        };
    </script>
</body>
</html>
