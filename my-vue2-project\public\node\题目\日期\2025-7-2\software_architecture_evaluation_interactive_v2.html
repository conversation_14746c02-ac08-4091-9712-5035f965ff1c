<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构评估方法 - 交互式学习</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.8;
            background-color: #f0f4f8;
            color: #333;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 1000px;
            width: 100%;
            background-color: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        h1, h2, h3 {
            color: #0056b3;
            text-align: center;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 0;
        }
        h1 {
            font-size: 2.2em;
        }
        h2 {
            font-size: 1.8em;
            margin-top: 40px;
        }
        p, li {
            font-size: 1.1em;
            color: #555;
        }
        .question-box {
            background-color: #e9f5ff;
            border: 1px solid #b3d9ff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .options-list {
            list-style-type: none;
            padding: 0;
        }
        .options-list li {
            margin: 10px 0;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .options-list li:hover {
            background-color: #d4eaff;
            border-color: #007bff;
        }
        .options-list li.selected {
            background-color: #007bff;
            color: white;
            border-color: #0056b3;
        }
        .feedback {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-weight: bold;
            display: none;
        }
        .feedback.correct {
            background-color: #d4edda;
            color: #155724;
            display: block;
        }
        .feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
            display: block;
        }
        .canvas-container {
            text-align: center;
            margin-top: 20px;
        }
        canvas {
            border: 2px solid #0056b3;
            border-radius: 8px;
            background-color: #ffffff;
        }
        .controls {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        button {
            padding: 10px 20px;
            font-size: 1em;
            color: white;
            background-color: #007bff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        button:hover, button.active {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #a0c7e4;
            cursor: not-allowed;
        }
        .explanation-box {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 5px solid #007bff;
        }
        .explanation-box h3 {
            margin-top: 0;
            color: #0056b3;
            border-bottom: none;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件架构评估方法 交互式学习</h1>
        
        <div class="question-box">
            <h3>让我们先来做一道题</h3>
            <p><strong>题目：</strong>基于场景的方式可进一步分为SAAM、ATAM和CBAM，其中，（&nbsp;&nbsp;&nbsp;&nbsp;）是用来对架构建立的成本来进行设计和建模，让决策者根据投资收益率来选择合适的架构。</p>
            <ul class="options-list" id="quiz-options">
                <li data-option="A">A. SAAM</li>
                <li data-option="B">B. ATAM</li>
                <li data-option="C">C. CBAM</li>
                <li data-option="D">D. 问卷调查</li>
            </ul>
            <div id="quiz-feedback" class="feedback"></div>
        </div>

        <h2>交互式动画演示</h2>
        <p style="text-align: center;">点击下面的按钮，探索不同的软件架构评估方法！</p>
        <div class="canvas-container">
            <canvas id="architecture-canvas" width="800" height="500"></canvas>
        </div>
        <div class="controls">
            <button id="btn-reset">重置动画</button>
            <button id="btn-questionnaire" class="main-btn">调查问卷法</button>
            <button id="btn-metric" class="main-btn">度量法</button>
            <button id="btn-scenario" class="main-btn">场景法</button>
        </div>
        <div class="controls" id="scenario-controls" style="display: none;">
            <button id="btn-saam">SAAM</button>
            <button id="btn-atam">ATAM</button>
            <button id="btn-cbam">CBAM</button>
        </div>
    </div>

    <div class="container">
        <h2>知识点详解</h2>
        <div class="explanation-box">
            <h3>🤔 什么是软件架构评估？</h3>
            <p>就像盖房子前要先审查设计图，确保结构合理、功能完善。软件架构评估，就是在软件大规模开发前，对"设计图"（软件架构）进行审查，以确保它能满足未来的各种需求（如性能、安全、成本等），尽早发现潜在问题。</p>
        </div>

        <div class="explanation-box">
            <h3>三种主要的评估方法</h3>
            <p>主要有三种评估方法，各有特点和适用时期：</p>
            <ul>
                <li><strong>调查问卷法 (Questionnaire-based)</strong>: 最简单直接的方法。就像做个快速问卷，问问设计师几个关键问题，快速了解大概情况。适合项目最开始，大家对架构还不太清楚的时候。优点是快，缺点是不够深入和客观。</li>
                <li><strong>度量法 (Metric-based)</strong>: 用数据说话。通过一些数学模型和工具，计算出架构的一些指标，比如预计的响应时间、可靠性等。这就像用尺子精确测量，结果很客观，但通常需要对架构有更精确的了解。</li>
                <li><strong>场景法 (Scenario-based)</strong>: 这是最常用也最重要的一族方法。它不是空谈，而是设想未来软件会遇到的各种具体情况（就是"场景"），然后看架构能不能很好地应对。</li>
            </ul>
        </div>
        
        <div class="explanation-box">
            <h3>场景法的三剑客：SAAM, ATAM, CBAM</h3>
            <p>场景法因为非常实用，衍生出了几种具体的分析方法：</p>
            <ul>
                <li><strong>SAAM (软件架构分析法)</strong>: 场景法的"入门版"。主要就是大家一起头脑风暴，想出各种重要的业务场景，然后看看架构设计是否都考虑到了。它的核心是"这个功能能不能实现？"。</li>
                <li><strong>ATAM (架构权衡分析法)</strong>: SAAM的"进阶版"。它不仅看架构能不能实现功能，更关注各种"质量属性"之间的平衡。比如，为了让性能更好，是不是会牺牲一些安全性？或者增加很多成本？ATAM就是帮助大家找到那个最佳的"平衡点"（Trade-off）。</li>
                <li><strong>CBAM (成本效益分析法)</strong>: ATAM的"补充版"，专门<strong>算经济账</strong>。它把ATAM里讨论的那些"平衡"用"成本"和"收益"量化出来。比如，花多少钱去提升性能，能带来多大的商业收益？这能帮助管理者做出最划算的决策。<strong>这也就是我们开头那道题的答案来源。</strong></li>
            </ul>
        </div>
    </div>

    <script>
        // Quiz Logic
        const options = document.getElementById('quiz-options').querySelectorAll('li');
        const feedback = document.getElementById('quiz-feedback');
        const correctAnswer = 'C';

        options.forEach(option => {
            option.addEventListener('click', () => {
                // Remove selection from others
                options.forEach(opt => opt.classList.remove('selected'));
                // Select clicked one
                option.classList.add('selected');
                
                const selectedOption = option.getAttribute('data-option');
                if (selectedOption === correctAnswer) {
                    feedback.textContent = '回答正确！CBAM (成本效益分析法) 关注的就是架构决策中的成本和收益。';
                    feedback.className = 'feedback correct';
                } else {
                    feedback.textContent = '再想想哦~ 提示：哪个方法听起来像是在"算经济账"？';
                    feedback.className = 'feedback incorrect';
                }
            });
        });

        // Canvas Animation Logic
        const canvas = document.getElementById('architecture-canvas');
        const ctx = canvas.getContext('2d');
        const scenarioControls = document.getElementById('scenario-controls');
        const mainButtons = document.querySelectorAll('.main-btn');
        const scenarioButtons = scenarioControls.querySelectorAll('button');

        let animationFrameId;

        function drawText(text, x, y, size = 16, color = '#333') {
            ctx.fillStyle = color;
            ctx.font = `bold ${size}px 'Microsoft YaHei'`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
        }

        function drawBox(text, x, y, w, h, color = '#007bff') {
            ctx.strokeStyle = color;
            ctx.fillStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.rect(x - w / 2, y - h / 2, w, h);
            ctx.stroke();
            ctx.fill();
            drawText(text, x, y, 16, color);
        }
        
        function drawLine(fromX, fromY, toX, toY, color = '#007bff') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
        }

        const elements = {
            center: { x: 400, y: 100 },
            questionnaire: { x: 150, y: 250 },
            metric: { x: 400, y: 250 },
            scenario: { x: 650, y: 250 },
            saam: { x: 500, y: 400 },
            atam: { x: 650, y: 400 },
            cbam: { x: 800, y: 400 } // move it to the right
        };

        function drawInitialState() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawBox('软件架构', elements.center.x, elements.center.y, 140, 50);
            drawText('点击下方按钮开始探索', 400, 50, 20, '#0056b3');
        }

        function setActiveButton(activeBtn) {
            mainButtons.forEach(btn => btn.classList.remove('active'));
            scenarioButtons.forEach(btn => btn.classList.remove('active'));
            if(activeBtn) {
                activeBtn.classList.add('active');
            }
        }

        function animateLine(fromX, fromY, toX, toY, color, callback) {
            let progress = 0;
            function animate() {
                if (progress >= 1) {
                    if (callback) callback();
                    return;
                }
                progress += 0.05;
                const currentX = fromX + (toX - fromX) * progress;
                const currentY = fromY + (toY - fromY) * progress;
                
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(currentX, currentY);
                ctx.stroke();
                
                animationFrameId = requestAnimationFrame(animate);
            }
            animate();
        }

        function showQuestionnaire() {
            resetToMainPath();
            setActiveButton(document.getElementById('btn-questionnaire'));
            scenarioControls.style.display = 'none';

            animateLine(elements.center.x, elements.center.y + 25, elements.questionnaire.x, elements.questionnaire.y - 25, '#28a745', () => {
                drawBox('调查问卷法', elements.questionnaire.x, elements.questionnaire.y, 120, 50, '#28a745');
                drawText('早期、粗略、主观', elements.questionnaire.x, elements.questionnaire.y + 50, 14, '#155724');
            });
        }

        function showMetric() {
            resetToMainPath();
            setActiveButton(document.getElementById('btn-metric'));
            scenarioControls.style.display = 'none';
            
            animateLine(elements.center.x, elements.center.y + 25, elements.metric.x, elements.metric.y - 25, '#ffc107', () => {
                drawBox('度量法', elements.metric.x, elements.metric.y, 120, 50, '#ffc107');
                drawText('精确、客观', elements.metric.x, elements.metric.y + 50, 14, '#856404');
            });
        }

        function showScenario() {
            resetToMainPath();
            setActiveButton(document.getElementById('btn-scenario'));
            scenarioControls.style.display = 'flex';
            
            animateLine(elements.center.x, elements.center.y + 25, elements.scenario.x, elements.scenario.y - 25, '#dc3545', () => {
                drawBox('场景法', elements.scenario.x, elements.scenario.y, 120, 50, '#dc3545');
                drawText('最常用！', elements.scenario.x, elements.scenario.y + 50, 14, '#721c24');
                drawText('点击下方 SAAM/ATAM/CBAM 查看详情', 400, 480, 16, '#0056b3');
            });
        }
        
        function resetToMainPath() {
             cancelAnimationFrame(animationFrameId);
             ctx.clearRect(0, 0, canvas.width, canvas.height);
             drawInitialState();
        }

        function showSAAM() {
            showScenario();
            setTimeout(() => {
                setActiveButton(document.getElementById('btn-saam'));
                 animateLine(elements.scenario.x, elements.scenario.y + 25, elements.saam.x, elements.saam.y - 25, '#17a2b8', () => {
                    drawBox('SAAM', elements.saam.x, elements.saam.y, 100, 50, '#17a2b8');
                    drawText('识别关键场景', elements.saam.x, elements.saam.y + 50, 14, '#0d6775');
                });
            }, 300);
        }

        function showATAM() {
            showScenario();
            setTimeout(() => {
                setActiveButton(document.getElementById('btn-atam'));
                animateLine(elements.scenario.x, elements.scenario.y + 25, elements.atam.x, elements.atam.y - 25, '#fd7e14', () => {
                    drawBox('ATAM', elements.atam.x, elements.atam.y, 100, 50, '#fd7e14');
                    drawText('权衡质量属性', elements.atam.x, elements.atam.y + 50, 14, '#984c0c');
                });
            }, 300);
        }

        function showCBAM() {
            showScenario();
            setTimeout(() => {
                setActiveButton(document.getElementById('btn-cbam'));
                animateLine(elements.scenario.x, elements.scenario.y + 25, elements.cbam.x - 50, elements.cbam.y - 25, '#6f42c1', () => {
                    drawBox('CBAM', elements.cbam.x - 50, elements.cbam.y, 100, 50, '#6f42c1');
                    drawText('分析成本与收益', elements.cbam.x - 50, elements.cbam.y + 50, 14, '#422876');
                });
            }, 300);
        }

        // Event Listeners
        document.getElementById('btn-reset').addEventListener('click', () => {
            resetToMainPath();
            scenarioControls.style.display = 'none';
            setActiveButton(null);
        });
        document.getElementById('btn-questionnaire').addEventListener('click', showQuestionnaire);
        document.getElementById('btn-metric').addEventListener('click', showMetric);
        document.getElementById('btn-scenario').addEventListener('click', showScenario);
        document.getElementById('btn-saam').addEventListener('click', showSAAM);
        document.getElementById('btn-atam').addEventListener('click', showATAM);
        document.getElementById('btn-cbam').addEventListener('click', showCBAM);

        // Initial Draw
        drawInitialState();

    </script>
</body>
</html> 