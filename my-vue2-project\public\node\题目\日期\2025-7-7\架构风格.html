<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>架构师成长记</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        #game-container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
        }
        h1 {
            color: #1a73e8;
            font-size: 24px;
            margin-bottom: 15px;
        }
        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 25px;
        }
        .options {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .options.row {
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: center;
        }
        button, .option-label {
            background-color: #1a73e8;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            width: 100%;
            box-sizing: border-box;
        }
        button:hover {
            background-color: #155ab6;
            transform: translateY(-2px);
        }
        .option-label {
            background-color: #e8f0fe;
            color: #1a73e8;
            border: 1px solid #1a73e8;
            text-align: left;
            display: block;
        }
        .option-label input {
            margin-right: 10px;
        }
        #summary {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            text-align: left;
        }
        #summary h2 {
            color: #1a73e8;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 10px;
        }
        #summary p {
            font-size: 15px;
        }
        .explanation {
            font-style: italic;
            color: #5f6368;
            margin-top: 10px;
            font-size: 14px;
        }
        strong {
            color: #d93025;
        }
        #house-canvas {
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>

    <div id="game-container">
        <!-- 内容会由JavaScript动态生成 -->
    </div>

    <script>
        const gameContainer = document.getElementById('game-container');
        const userChoices = {};

        // 游戏步骤
        const steps = [
            {
                id: 'intro',
                title: '欢迎来到"架构师成长记"！',
                text: '你好，我是你的向导。在这个游戏中，你将扮演一位新晋软件架构师"小王"，体验如何从零开始设计一个"软件房子"。准备好了吗？',
                options: [{ text: '我准备好了！', next: 'step1_decomposition' }]
            },
            {
                id: 'step1_decomposition',
                title: '第一步：功能分解',
                text: '首先，我们要明确"房子"需要哪些功能。这在软件开发中叫做<strong>功能分解</strong>，就是把一个大系统拆分成多个小部分。请选择你的房子需要哪些房间：',
                type: 'checkbox',
                options: ['厨房', '卧室', '客厅', '卫生间', '书房'],
                buttonText: '选好了',
                next: 'step2_style',
                explanation: '<strong>知识点：</strong>功能分解是ABSD方法的三个基础之一。它帮助我们理解和组织系统的复杂性。'
            },
            {
                id: 'step2_style',
                title: '第二步：选择架构风格',
                text: '很好！现在，为你的房子选择一种"风格"。这决定了房子的整体结构和特性，就像软件的<strong>架构风格</strong>。不同的风格有不同的优缺点。',
                type: 'radio',
                options: {
                    '平房': '优点: 简单实用, 缺点: 不易扩展',
                    '别墅': '优点: 空间大舒适, 缺点: 成本高昂',
                    '摩天大楼': '优点: 容纳量大, 缺点: 结构极其复杂'
                },
                buttonText: '就选这个风格',
                next: 'step3_risk',
                explanation: '<strong>知识点：</strong>采用合适的<strong>架构风格</strong>是ABSD的第二个基础。它用来实现软件的"质量属性"（如性能、成本、可扩展性等）和商业需求。这是原题中第一个填空题的答案。'
            },
            {
                id: 'step3_risk',
                title: '第三步：风险评估',
                text: '"天气预报说未来有特大台风！"<br>作为架构师，你需要提前识别潜在的风险。针对你选择的<strong>{style}</strong>，最大的风险是什么？',
                type: 'radio',
                getOptions: (choices) => {
                    const styleOptions = {
                        '平房': ['地基被淹没', '屋顶被掀翻', '墙壁漏水'],
                        '别墅': ['花园被毁', '落地窗破碎', '地下室进水'],
                        '摩天大楼': ['玻璃幕墙碎裂', '高层晃动剧烈', '电梯停运']
                    };
                    return styleOptions[choices.style];
                },
                buttonText: '确认风险',
                next: 'step4_change',
                explanation: '<strong>知识点：</strong>这叫<strong>架构评估</strong>或<strong>风险识别</strong>。它是ABSD的主要活动之一，目标是及早发现设计中的缺陷和风险。这是原题中第二个填空题的答案。'
            },
            {
                id: 'step4_change',
                title: '第四步：应对需求变化',
                text: '"客户突然来电：我们家要添一个宝宝，需要一间婴儿房！"<br>这是<strong>需求变更</strong>。作为架构师，你需要修改设计来满足新需求。你打算怎么办？',
                type: 'radio',
                options: ['在现有楼层旁加盖一间', '把书房改造成婴儿房', '告诉客户"设计已定，无法修改"'],
                buttonText: '就这样改',
                next: 'step5_docs',
                explanation: '<strong>知识点：</strong><strong>维护和演进架构</strong>是ABSD的另一个关键活动，它能保证软件在不断变化的需求中保持生命力。这是原题中第三个填空题的答案。'
            },
            {
                id: 'step5_docs',
                title: '第五步：架构文档化',
                text: '项目经理找你要房子的设计图。你会怎么给他？<br>请记住，架构师的工作需要清晰地记录下来。',
                type: 'radio',
                options: [
                    '给他一份整洁、详细、带说明的蓝图',
                    '把画在餐巾纸上的草稿给他',
                    '告诉他"别担心，都记在我脑子里了"'
                ],
                buttonText: '提交文档',
                next: 'summary',
                explanation: '<strong>知识点：</strong>不规范的文档（比如餐巾纸草稿或只记在脑中）不符合架构文档化原则。这是原题中第四个填空题涉及的概念。'
            },
            {
                id: 'summary',
                title: '恭喜！你完成了架构设计！',
                text: '你已经体验了软件架构设计的核心流程。一份优秀的架构文档，不仅包含<strong>架构规格说明书</strong>（你的蓝图），还应包含<strong>设计原理/决策</strong>（你为什么这么设计）。',
                showSummary: true,
                explanation: '<strong>知识点：</strong>架构文档的主要输出是规格说明书和设计原理（或叫设计决策）。这是原题最后一个空缺的答案。'
            }
        ];

        function renderStep(stepId) {
            const step = steps.find(s => s.id === stepId);
            if (!step) return;

            // 为动画准备Canvas
            const canvasHtml = `<canvas id="house-canvas" width="540" height="200" style="display: none;"></canvas>`;

            let html = `<h1>${step.title}</h1>`;
            let processedText = step.text.replace('{style}', userChoices.style);
            html += `<p>${processedText}</p>`;
            
            if (step.explanation) {
                html += `<p class="explanation">${step.explanation}</p>`;
            }

            if (step.type) {
                html += '<div class="options">';
                let options = step.options;
                if (typeof step.getOptions === 'function') {
                    options = step.getOptions(userChoices);
                }

                if (step.type === 'checkbox') {
                    options.forEach((option, index) => {
                        html += `<label class="option-label"><input type="checkbox" name="option" value="${option}"> ${option}</label>`;
                    });
                    html += `<button onclick="handleChoice('${step.id}', '${step.next}')">${step.buttonText}</button>`;
                } else if (step.type === 'radio') {
                    if (Array.isArray(options)) {
                         options.forEach((option, index) => {
                            html += `<button onclick="handleChoice('${step.id}', '${step.next}', '${option}')">${option}</button>`;
                        });
                    } else {
                        for (const key in options) {
                            html += `<button onclick="handleChoice('${step.id}', '${step.next}', '${key}')">${key}<br><span style="font-size:12px; color: #eee;">(${options[key]})</span></button>`;
                        }
                    }
                }
                html += '</div>';
            } else if (step.options) {
                 html += '<div class="options">';
                 step.options.forEach(option => {
                    html += `<button onclick="renderStep('${option.next}')">${option.text}</button>`;
                 });
                 html += '</div>';
            }

            if (step.showSummary) {
                html += `
                    <div id="summary">
                        <h2>你的软件房子 - 架构规格说明书</h2>
                        <p><strong>功能列表:</strong> ${userChoices.decomposition.join(', ') || '未选择'}</p>
                        <p><strong>架构风格:</strong> ${userChoices.style || '未选择'}</p>
                        <p><strong>主要风险:</strong> 已识别到"${userChoices.risk}"</p>
                        <p><strong>需求变更对策:</strong> ${userChoices.change}</p>
                        <p><strong>文档交付方式:</strong> ${userChoices.docs}</p>
                    </div>
                `;
                 html += `<div class="options" style="margin-top: 20px;"><button onclick="renderStep('intro')">再玩一次</button></div>`;
            }

            gameContainer.innerHTML = canvasHtml + html;

            // --- Canvas渲染逻辑 ---
            const canvas = document.getElementById('house-canvas');
            const ctx = canvas.getContext('2d');
            
            // 根据游戏步骤决定是否显示Canvas及绘制内容
            if (stepId !== 'intro' && step.id !=='step1_decomposition') {
                canvas.style.display = 'block';
                // 在进入下一步时，绘制上一步选择的结果
                if (stepId === 'step2_style') {
                    drawRooms(ctx, userChoices.decomposition);
                } else if (stepId === 'step3_risk') {
                    drawHouse(ctx, userChoices.style);
                } else if (stepId === 'step4_change') {
                    animateRisk(ctx, userChoices.style, userChoices.risk);
                } else if (stepId === 'step5_docs') {
                    animateChange(ctx, userChoices.style, userChoices.change, userChoices.decomposition);
                } else if (stepId === 'summary') {
                    drawHouse(ctx, userChoices.style, true, userChoices.change);
                }
            }
        }

        function handleChoice(stepId, nextStep, value) {
            const step = steps.find(s => s.id === stepId);
            
            if (step.type === 'checkbox') {
                const checked = Array.from(document.querySelectorAll('input[name="option"]:checked')).map(el => el.value);
                userChoices.decomposition = checked;
            } else if (step.id === 'step2_style') {
                userChoices.style = value;
            } else if (step.id === 'step3_risk') {
                userChoices.risk = value;
            } else if (step.id === 'step4_change') {
                userChoices.change = value;
            } else if (step.id === 'step5_docs') {
                 userChoices.docs = value;
            }
            
            renderStep(nextStep);
        }

        // --- Canvas动画与绘图函数 ---
        const canvasConfig = {
            width: 540,
            height: 200,
            colors: {
                wall: '#f2d7b5', roof: '#c0392b', window: '#3498db',
                room: '#d6eaf8', text: '#2c3e50', extraRoom: '#2ecc71',
                ground: '#8c5e3c'
            }
        };

        function drawRooms(ctx, rooms = []) {
            ctx.clearRect(0, 0, canvasConfig.width, canvasConfig.height);
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'center';

            if (!rooms || rooms.length === 0) {
                ctx.fillStyle = canvasConfig.colors.text;
                ctx.fillText('你还没选择任何房间呢!', canvasConfig.width / 2, canvasConfig.height / 2);
                return;
            }
            
            const roomWidth = 80, roomHeight = 60, gap = 15;
            const totalWidth = rooms.length * roomWidth + (rooms.length - 1) * gap;
            let startX = (canvasConfig.width - totalWidth) / 2;

            rooms.forEach((room, i) => {
                const x = startX + i * (roomWidth + gap);
                const y = (canvasConfig.height - roomHeight) / 2;
                ctx.fillStyle = canvasConfig.colors.room;
                ctx.fillRect(x, y, roomWidth, roomHeight);
                ctx.strokeStyle = '#34495e';
                ctx.strokeRect(x, y, roomWidth, roomHeight);
                ctx.fillStyle = canvasConfig.colors.text;
                ctx.fillText(room, x + roomWidth / 2, y + roomHeight / 2 + 5);
            });
        }

        function drawHouse(ctx, style, final = false, change) {
            ctx.clearRect(0, 0, canvasConfig.width, canvasConfig.height);
            const { colors } = canvasConfig;
            const centerX = canvasConfig.width / 2;
            const groundY = canvasConfig.height - 30;

            ctx.fillStyle = colors.ground;
            ctx.fillRect(0, groundY, canvasConfig.width, 30);

            if (change === '在现有楼层旁加盖一间') {
                ctx.fillStyle = colors.extraRoom;
                if (style === '平房') ctx.fillRect(centerX + 100, groundY - 60, 60, 60);
                else if (style === '别墅') ctx.fillRect(centerX - 80 - 70, groundY - 70, 70, 70);
            }

            switch (style) {
                case '平房':
                    ctx.fillStyle = colors.wall;
                    ctx.fillRect(centerX - 100, groundY - 80, 200, 80);
                    ctx.fillStyle = colors.roof;
                    ctx.beginPath();
                    ctx.moveTo(centerX - 120, groundY - 80);
                    ctx.lineTo(centerX, groundY - 120);
                    ctx.lineTo(centerX + 120, groundY - 80);
                    ctx.closePath();
                    ctx.fill();
                    break;
                case '别墅':
                    ctx.fillStyle = colors.wall;
                    ctx.fillRect(centerX - 80, groundY - 120, 160, 120);
                    ctx.fillStyle = colors.roof;
                    ctx.beginPath();
                    ctx.moveTo(centerX - 90, groundY - 120);
                    ctx.lineTo(centerX, groundY - 150);
                    ctx.lineTo(centerX + 90, groundY - 120);
                    ctx.closePath();
                    ctx.fill();
                    ctx.fillRect(centerX + 80, groundY - 60, 70, 60);
                    break;
                case '摩天大楼':
                    ctx.fillStyle = colors.wall;
                    ctx.fillRect(centerX - 50, groundY - 160, 100, 160);
                    ctx.fillStyle = colors.window;
                    for (let i = 0; i < 8; i++) {
                        for (let j = 0; j < 3; j++) {
                            ctx.fillRect(centerX - 40 + j * 30, groundY - 150 + i * 20, 15, 10);
                        }
                    }
                    break;
            }
             if (final) {
                ctx.font = '20px "Comic Sans MS", cursive, sans-serif';
                ctx.fillStyle = "gold";
                ctx.textAlign = "center";
                ctx.fillText("✨ 恭喜, 完美竣工! ✨", centerX, 30);
            }
        }

        function animateRisk(ctx, style, risk) {
            let frame = 0;
            const duration = 120;
            
            function animate() {
                if (frame > duration) {
                    drawHouse(ctx, style); // 动画结束后恢复原状
                    return;
                }
                
                const centerX = canvasConfig.width / 2;
                const groundY = canvasConfig.height - 30;

                ctx.save();
                // 先画一个没有问题的房子
                drawHouse(ctx, style);
                // 再在上面叠加动画效果
                ctx.restore(); // restore to avoid unintended style inheritance
                ctx.save();
                
                if (style === '平房' && risk === '屋顶被掀翻') {
                    ctx.translate(centerX, groundY - 100);
                    ctx.rotate(frame * 0.02);
                    ctx.translate(-centerX, -(groundY - 100));
                    ctx.clearRect(centerX-125, groundY-125, 250, 50); //擦掉旧屋顶
                    
                    ctx.translate(frame * 1.5, -frame * 1.5); // 移动
                    
                    ctx.fillStyle = canvasConfig.colors.roof;
                    ctx.beginPath();
                    ctx.moveTo(centerX - 120, groundY - 80);
                    ctx.lineTo(centerX, groundY - 120);
                    ctx.lineTo(centerX + 120, groundY - 80);
                    ctx.closePath();
                    ctx.fill();
                } else if (style === '摩天大楼' && risk === '高层晃动剧烈') {
                     const amplitude = Math.sin(frame * 0.1) * 5;
                     ctx.translate(amplitude, 0);
                     drawHouse(ctx, style);
                } else {
                     const shakeX = (Math.random() - 0.5) * 4;
                     ctx.translate(shakeX, 0);
                     drawHouse(ctx, style);
                }
                ctx.restore();
                frame++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        function animateChange(ctx, style, change, rooms) {
            drawHouse(ctx, style);
            if (change !== '在现有楼层旁加盖一间') return;

            let frame = 0;
            const duration = 80;

            function animate() {
                if (frame > duration) {
                    drawHouse(ctx, style, false, change); // 最终状态
                    return;
                }
                drawHouse(ctx, style); // 重绘基础房子
                
                const centerX = canvasConfig.width / 2;
                const groundY = canvasConfig.height - 30;
                const progress = frame / duration;

                ctx.globalAlpha = progress;
                ctx.fillStyle = canvasConfig.colors.extraRoom;

                if (style === '平房') {
                    const newWidth = 60 * progress;
                    ctx.fillRect(centerX + 100, groundY - 60, newWidth, 60);
                } else if (style === '别墅') {
                     const newHeight = 70 * progress;
                     ctx.fillRect(centerX - 80 - 70, groundY - newHeight, 70, newHeight);
                }
                ctx.globalAlpha = 1.0;
                frame++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 开始游戏
        renderStep('intro');
    </script>

</body>
</html> 