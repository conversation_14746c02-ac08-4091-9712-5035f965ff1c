<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Assemble</title>
    <!-- 引入第三方库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <script type="importmap">
        {
            "imports": {
                "three": "https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.module.js",
                "three/addons/": "https://cdn.jsdelivr.net/npm/three@0.128.0/examples/jsm/"
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
        :root {
            --primary-color: #42a5f5; /* 蓝色，代表技术与精确 */
            --secondary-color: #1e88e5;
            --accent-color: #ffab40; /* 橙色，点缀 */
            --light-bg: #e3f2fd;
            --panel-bg: #ffffff;
            --text-color: #0d47a1;
        }
        /* ... [其他通用样式与之前文件类似] ... */
        body, .container, .word-panel, h1, p, .breakdown-section, .morpheme-btn, .animation-panel, .activity-title, .activity-wrapper, .control-button { box-sizing: border-box; }
        body { font-family: 'Roboto', 'Noto Sans SC', sans-serif; background-color: #bbdefb; color: var(--text-color); display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; overflow: hidden; }
        .container { display: flex; flex-direction: row; width: 95%; max-width: 1400px; height: 90vh; max-height: 800px; background-color: var(--panel-bg); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .word-panel { flex: 1; padding: 40px; display: flex; flex-direction: column; justify-content: center; background-color: var(--light-bg); overflow-y: auto; }
        .word-panel h1 { font-size: 3.5em; color: var(--primary-color); }
        .word-panel .pronunciation { font-size: 1.5em; color: var(--secondary-color); margin-bottom: 20px; }
        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--secondary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: #1a237e; }
        .breakdown-section { margin-top: 25px; padding: 20px; background-color: #ffffff; border-radius: 10px; }
        .morpheme-btn { margin: 5px; padding: 8px 15px; border: 2px solid var(--primary-color); border-radius: 20px; background-color: transparent; color: var(--primary-color); font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; }
        .morpheme-btn:hover, .morpheme-btn.active { background-color: var(--primary-color); color: white; transform: translateY(-2px); }
        .animation-panel { flex: 2; padding: 20px; display: flex; flex-direction: column; justify-content: center; align-items: center; position: relative; background: #37474f; }
        .activity-title { font-size: 1.8em; color: var(--light-bg); margin-bottom: 15px; text-align: center; }
        .activity-wrapper { display: none; width: 100%; height: calc(100% - 100px); flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        .game-container { width: 100%; height: 100%; position: relative; display: flex; align-items: center; justify-content: center; border-radius: 15px; background: #263238; overflow: hidden; }
        #assembly-canvas, #story-canvas { width: 100%; height: 100%; }
        .story-narration {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            background: rgba(40, 53, 147, 0.7);
            color: white;
            border-radius: 8px;
            font-size: 1.2em;
            text-align: center;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s, visibility 0.5s;
        }
        .story-narration.visible {
            opacity: 1;
            visibility: visible;
        }
        .control-button { margin-top: 20px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>assemble</h1>
            <p class="pronunciation">[əˈsembl]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 集合，聚集；组装</p>
                <p><strong>词源:</strong> as-(朝向) + semble(相像) → 使部分朝向一起变得相像 → 组装</p>
                <p><strong>含义：</strong><br>1. 将（人或物）聚集在一起。<br>2. 将（机器、家具等）的部件组装起来。</p>
                <div class="example">
                    <p><strong>例句1:</strong> A crowd had assembled outside the gates.</p>
                    <p><strong>翻译1:</strong> 一群人已聚集在门外。</p>
                    <p><strong>例句2:</strong> The wardrobe is easy to assemble.</p>
                    <p><strong>翻译2:</strong> 这个衣柜很容易组装。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>单词动画</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn active" data-activity="assembly-game" data-title="3D组装动画">3D组装动画</button>
                    <button class="morpheme-btn" data-activity="morpheme-story" data-title="词源故事动画">词源故事动画</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">3D组装动画</h2>
            <div id="assembly-game" class="activity-wrapper active">
                <div class="game-container"><div id="assembly-canvas"></div></div>
                <button class="control-button" id="assemble-btn">组装</button>
            </div>
            <div id="morpheme-story" class="activity-wrapper">
                <div class="game-container">
                    <div id="story-canvas"></div>
                    <div id="story-narration" class="story-narration"></div>
                </div>
                <button class="control-button" id="play-story-btn">开始故事</button>
            </div>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        // --- Activity Switching Logic ---
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitleElem = document.getElementById('activity-title');

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                if (btn.classList.contains('active')) return;

                const targetActivity = btn.dataset.activity;
                const targetTitle = btn.dataset.title;

                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                activityTitleElem.textContent = targetTitle;

                activityWrappers.forEach(wrapper => {
                    if (wrapper.id === targetActivity) {
                        wrapper.classList.add('active');
                    } else {
                        wrapper.classList.remove('active');
                    }
                });
            });
        });

        // --- Animation 1: Assembly Game ---
        const assemblyGame = {
            canvasContainer: document.getElementById('assembly-canvas'),
            scene: null, camera: null, renderer: null, cubeParts: [],
            isAssembled: false,
            init() {
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, this.canvasContainer.clientWidth / this.canvasContainer.clientHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                this.renderer.setSize(this.canvasContainer.clientWidth, this.canvasContainer.clientHeight);
                this.canvasContainer.appendChild(this.renderer.domElement);

                this.camera.position.z = 5;

                const faceGeo = new THREE.PlaneGeometry(1, 1);
                const colors = [0xff0000, 0x00ff00, 0x0000ff, 0xffff00, 0xff00ff, 0x00ffff];
                const positions = [
                    { p: [0, 0, 0.5], r: [0, 0, 0] }, { p: [0, 0, -0.5], r: [0, Math.PI, 0] },
                    { p: [0, 0.5, 0], r: [-Math.PI / 2, 0, 0] }, { p: [0, -0.5, 0], r: [Math.PI / 2, 0, 0] },
                    { p: [0.5, 0, 0], r: [0, Math.PI / 2, 0] }, { p: [-0.5, 0, 0], r: [0, -Math.PI / 2, 0] }
                ];

                for (let i = 0; i < 6; i++) {
                    const material = new THREE.MeshStandardMaterial({ color: colors[i], side: THREE.DoubleSide });
                    const plane = new THREE.Mesh(faceGeo, material);
                    plane.userData.target = {
                        position: new THREE.Vector3(...positions[i].p),
                        rotation: new THREE.Euler(...positions[i].r)
                    };
                    plane.position.set((Math.random() - 0.5) * 6, (Math.random() - 0.5) * 6, (Math.random() - 0.5) * 6);
                    plane.rotation.set(Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI);
                    this.cubeParts.push(plane);
                    this.scene.add(plane);
                }

                const light = new THREE.AmbientLight(0x404040, 2);
                this.scene.add(light);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
                directionalLight.position.set(5, 5, 5);
                this.scene.add(directionalLight);
                
                this.animate();
            },
            animate() {
                requestAnimationFrame(() => this.animate());
                if (document.getElementById('assembly-game').classList.contains('active')) {
                    if (!this.isAssembled) {
                        this.cubeParts.forEach(part => {
                            part.rotation.x += 0.005;
                            part.rotation.y += 0.005;
                        });
                    }
                    this.renderer.render(this.scene, this.camera);
                }
            },
            toggleAssembly() {
                this.isAssembled = !this.isAssembled;
                document.getElementById('assemble-btn').textContent = this.isAssembled ? '拆分' : '组装';
                this.cubeParts.forEach(part => {
                    const targetPos = this.isAssembled ? part.userData.target.position : new THREE.Vector3((Math.random() - 0.5) * 8, (Math.random() - 0.5) * 8, (Math.random() - 0.5) * 8);
                    const targetRot = this.isAssembled ? part.userData.target.rotation : new THREE.Euler(Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI);
                    
                    gsap.to(part.position, { ...targetPos, duration: 1.5, ease: 'back.inOut(1.7)' });
                    gsap.to(part.rotation, { ...targetRot, duration: 1.5, ease: 'back.inOut(1.7)' });
                });
            },
            onResize() {
                if (!this.renderer) return;
                this.camera.aspect = this.canvasContainer.clientWidth / this.canvasContainer.clientHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(this.canvasContainer.clientWidth, this.canvasContainer.clientHeight);
            }
        };

        // --- Animation 2: Morpheme Story ---
        const morphemeStory = {
            canvasContainer: document.getElementById('story-canvas'),
            btn: document.getElementById('play-story-btn'),
            narrationElem: document.getElementById('story-narration'),
            scene: null, camera: null, renderer: null, spheres: [],
            isPlaying: false,
            init() {
                this.scene = new THREE.Scene();
                this.camera = new THREE.PerspectiveCamera(75, this.canvasContainer.clientWidth / this.canvasContainer.clientHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                this.renderer.setSize(this.canvasContainer.clientWidth, this.canvasContainer.clientHeight);
                this.canvasContainer.appendChild(this.renderer.domElement);
                this.camera.position.z = 10;

                const geometry = new THREE.SphereGeometry(0.5, 32, 32);
                const colors = [0xf44336, 0x4caf50, 0x2196f3, 0xffeb3b, 0x9c27b0];

                for (let i = 0; i < 5; i++) {
                    const material = new THREE.MeshStandardMaterial({ color: colors[i], metalness: 0.5, roughness: 0.5 });
                    const sphere = new THREE.Mesh(geometry, material);
                    
                    const phi = Math.acos(-1 + (2 * i) / 4);
                    const theta = Math.sqrt(5 * Math.PI) * phi;

                    sphere.userData.initial = {
                        position: new THREE.Vector3((Math.random() - 0.5) * 12, (Math.random() - 0.5) * 12, (Math.random() - 0.5) * 12),
                        color: new THREE.Color(colors[i])
                    };
                    sphere.userData.targetPosition = new THREE.Vector3(3 * Math.cos(theta) * Math.sin(phi), 3 * Math.sin(theta) * Math.sin(phi), 3 * Math.cos(phi));
                    
                    sphere.position.copy(sphere.userData.initial.position);
                    this.spheres.push(sphere);
                    this.scene.add(sphere);
                }

                const light = new THREE.AmbientLight(0xcccccc, 1);
                this.scene.add(light);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 2);
                directionalLight.position.set(5, 10, 7.5);
                this.scene.add(directionalLight);

                this.animate();
            },
            animate() {
                requestAnimationFrame(() => this.animate());
                 if (document.getElementById('morpheme-story').classList.contains('active')) {
                    this.spheres.forEach(sphere => {
                        sphere.rotation.y += 0.005;
                    });
                    this.renderer.render(this.scene, this.camera);
                }
            },
            showNarration(text) {
                this.narrationElem.textContent = text;
                this.narrationElem.classList.add('visible');
            },
            hideNarration() {
                this.narrationElem.classList.remove('visible');
            },
            play() {
                if (this.isPlaying) return;
                this.isPlaying = true;
                this.btn.textContent = '演示中...';
                this.btn.disabled = true;

                const tl = gsap.timeline({
                    onComplete: () => {
                        this.btn.textContent = '重置';
                        this.btn.disabled = false;
                        this.btn.onclick = () => this.reset();
                    }
                });
                
                this.showNarration("词根 as- (ad-) 意为 '朝向，去...'");
                
                tl.to(this.camera.position, { z: 12, duration: 1, ease: 'power2.inOut' })
                  .to(this.spheres.map(s => s.position), {
                    x: 0, y: 0, z: 0,
                    duration: 2,
                    stagger: 0.1,
                    ease: 'power2.inOut'
                }, ">")
                .add(() => this.showNarration("词根 semble 意为 '相像'"), ">1")
                .to(this.spheres.map(s => s.material.color), {
                    r: 0.25, g: 0.64, b: 0.96, // an approximation of --primary-color #42a5f5
                    duration: 1.5,
                    stagger: 0.1,
                    ease: 'sine.inOut'
                }, ">")
                .add(() => this.showNarration("assemble: 使部分朝向一起变得相像 → 组装"), ">1")
                .to(this.spheres.map(s => s.position), {
                    x: (i) => this.spheres[i].userData.targetPosition.x,
                    y: (i) => this.spheres[i].userData.targetPosition.y,
                    z: (i) => this.spheres[i].userData.targetPosition.z,
                    duration: 1.5,
                    ease: 'back.out(1.7)'
                }, ">");
            },
            reset() {
                this.isPlaying = false;
                this.btn.textContent = '开始故事';
                this.btn.disabled = false;
                this.btn.onclick = () => this.play();
                
                this.hideNarration();
                this.spheres.forEach(sphere => {
                    gsap.to(sphere.position, { ...sphere.userData.initial.position, duration: 1.5, ease: 'power2.inOut' });
                    gsap.to(sphere.material.color, { ...sphere.userData.initial.color, duration: 1.5 });
                });
            },
            onResize() {
                if (!this.renderer) return;
                this.camera.aspect = this.canvasContainer.clientWidth / this.canvasContainer.clientHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(this.canvasContainer.clientWidth, this.canvasContainer.clientHeight);
            }
        };

        // --- Global Controls & Init ---
        document.getElementById('assemble-btn').onclick = () => assemblyGame.toggleAssembly();
        document.getElementById('play-story-btn').onclick = () => morphemeStory.play();

        window.onresize = () => {
            assemblyGame.onResize();
            morphemeStory.onResize();
        };

        assemblyGame.init();
        // Defer story init slightly if needed, but should be fine
        morphemeStory.init();
    });
    </script>
</body>
</html> 