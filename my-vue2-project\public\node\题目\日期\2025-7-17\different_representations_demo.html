<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>不同表示的对象构造</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f4f7f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .demo-area {
            display: flex;
            flex-direction: column;
            margin: 30px 0;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
        }
        .explanation {
            background: #e9f7fd;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .highlight {
            background-color: #fffacd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .code-block {
            background: #f8f9fa;
            border-left: 5px solid #2ecc71;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre;
            overflow-x: auto;
            font-size: 14px;
        }
        .interactive-demo {
            margin: 20px 0;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            margin-bottom: 15px;
            flex: 1;
            min-width: 200px;
        }
        .control-group h4 {
            margin-top: 0;
            margin-bottom: 10px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin: 5px 0;
        }
        button:hover {
            background-color: #2980b9;
        }
        button.selected {
            background-color: #27ae60;
        }
        .house-display {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        .house {
            width: 250px;
            height: 250px;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            background-color: white;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .house-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 18px;
        }
        .house-image {
            width: 200px;
            height: 150px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 100px;
            line-height: 1;
        }
        .house-details {
            font-size: 14px;
            text-align: center;
        }
        .step-display {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f4f8;
            border-radius: 5px;
            border: 1px solid #bde0ec;
        }
        .step-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .step-list {
            margin: 0;
            padding-left: 20px;
        }
        .step-list li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>当构造过程必须允许被构造的对象有不同的表示时</h1>
        
        <div class="explanation">
            <p>这句话是<strong>建造者模式（Builder Pattern）</strong>的一个重要应用场景。它的意思是：</p>
            <p><span class="highlight">在创建一个复杂对象的过程中，我们需要能够根据不同的需求或配置，生成外观或内部结构不同的对象，但这些对象的构建过程基本相似。</span></p>
        </div>

        <h2>概念解释</h2>
        
        <div class="explanation">
            <h3>关键点：</h3>
            <ol>
                <li><strong>相同的构造过程</strong>：对象的创建步骤基本相同</li>
                <li><strong>不同的表示</strong>：最终产品在外观、结构或功能上有差异</li>
                <li><strong>灵活性</strong>：可以根据需求选择不同的表示</li>
            </ol>
            <p>举例来说，想象我们在建造房子。无论是别墅、公寓还是平房，建造过程都包括打地基、砌墙、安装门窗、装修等步骤，但最终的房子外观和结构会有很大不同。</p>
        </div>

        <h2>交互演示：房屋建造者</h2>
        
        <div class="interactive-demo">
            <div class="controls">
                <div class="control-group">
                    <h4>选择房屋类型：</h4>
                    <button id="type-villa" class="house-type">别墅</button>
                    <button id="type-apartment" class="house-type">公寓</button>
                    <button id="type-cottage" class="house-type">平房</button>
                </div>
                
                <div class="control-group">
                    <h4>建造步骤：</h4>
                    <button id="step-foundation" disabled>1. 打地基</button>
                    <button id="step-walls" disabled>2. 砌墙</button>
                    <button id="step-windows" disabled>3. 安装门窗</button>
                    <button id="step-roof" disabled>4. 建造屋顶</button>
                    <button id="step-interior" disabled>5. 室内装修</button>
                </div>
            </div>
            
            <div class="step-display">
                <div class="step-title">建造过程：</div>
                <ul class="step-list" id="step-list">
                    <li>请先选择房屋类型</li>
                </ul>
            </div>
            
            <div class="house-display">
                <div class="house">
                    <div class="house-title">建造中的房屋</div>
                    <div class="house-image" id="house-image">🏗️</div>
                    <div class="house-details" id="house-details">
                        请选择房屋类型并开始建造
                    </div>
                </div>
            </div>
        </div>

        <h2>代码实现</h2>
        
        <div class="code-block">
// 房屋产品
class House {
    private parts: string[] = [];
    private type: string = '';
    
    setType(type: string) {
        this.type = type;
    }
    
    addPart(part: string) {
        this.parts.push(part);
    }
    
    showHouse() {
        console.log(`房屋类型: ${this.type}`);
        console.log(`包含部分: ${this.parts.join(', ')}`);
    }
}

// 抽象建造者
interface HouseBuilder {
    buildFoundation(): void;
    buildWalls(): void;
    buildWindows(): void;
    buildRoof(): void;
    buildInterior(): void;
    getHouse(): House;
}

// 别墅建造者
class VillaBuilder implements HouseBuilder {
    private house = new House();
    
    constructor() {
        this.house.setType('别墅');
    }
    
    buildFoundation() {
        this.house.addPart('深层加固地基');
    }
    
    buildWalls() {
        this.house.addPart('高品质砖石墙');
    }
    
    buildWindows() {
        this.house.addPart('大型落地窗');
    }
    
    buildRoof() {
        this.house.addPart('复杂坡顶');
    }
    
    buildInterior() {
        this.house.addPart('豪华装修');
    }
    
    getHouse(): House {
        return this.house;
    }
}

// 公寓建造者
class ApartmentBuilder implements HouseBuilder {
    private house = new House();
    
    constructor() {
        this.house.setType('公寓');
    }
    
    buildFoundation() {
        this.house.addPart('标准地基');
    }
    
    buildWalls() {
        this.house.addPart('混凝土墙');
    }
    
    buildWindows() {
        this.house.addPart('标准窗户');
    }
    
    buildRoof() {
        this.house.addPart('平顶');
    }
    
    buildInterior() {
        this.house.addPart('现代简约装修');
    }
    
    getHouse(): House {
        return this.house;
    }
}

// 平房建造者
class CottageBuilder implements HouseBuilder {
    private house = new House();
    
    constructor() {
        this.house.setType('平房');
    }
    
    buildFoundation() {
        this.house.addPart('简易地基');
    }
    
    buildWalls() {
        this.house.addPart('木质墙');
    }
    
    buildWindows() {
        this.house.addPart('小型窗户');
    }
    
    buildRoof() {
        this.house.addPart('简单坡顶');
    }
    
    buildInterior() {
        this.house.addPart('舒适乡村风格装修');
    }
    
    getHouse(): House {
        return this.house;
    }
}

// 指导者
class Director {
    private builder: HouseBuilder;
    
    setBuilder(builder: HouseBuilder) {
        this.builder = builder;
    }
    
    constructHouse() {
        this.builder.buildFoundation();
        this.builder.buildWalls();
        this.builder.buildWindows();
        this.builder.buildRoof();
        this.builder.buildInterior();
    }
}

// 客户端代码
const director = new Director();
const villaBuilder = new VillaBuilder();
const apartmentBuilder = new ApartmentBuilder();

// 建造别墅
director.setBuilder(villaBuilder);
director.constructHouse();
const villa = villaBuilder.getHouse();
villa.showHouse();

// 建造公寓
director.setBuilder(apartmentBuilder);
director.constructHouse();
const apartment = apartmentBuilder.getHouse();
apartment.showHouse();</div>

        <h2>详细解释</h2>
        
        <div class="explanation">
            <h3>为什么需要"不同的表示"？</h3>
            <p>在软件开发中，我们经常需要创建同一类型但细节不同的对象。例如：</p>
            <ul>
                <li>不同配置的汽车（轿车、SUV、卡车）</li>
                <li>不同风格的用户界面（暗色主题、亮色主题）</li>
                <li>不同格式的文档（PDF、HTML、纯文本）</li>
            </ul>
            <p>如果直接在构造函数中处理所有这些变化，代码会变得非常复杂。建造者模式通过将构建过程与表示分离，解决了这个问题。</p>
            
            <h3>建造者模式的优势</h3>
            <ol>
                <li><strong>分步构建</strong>：可以一步一步创建复杂对象</li>
                <li><strong>隐藏细节</strong>：客户端不需要知道对象的具体构建过程</li>
                <li><strong>代码复用</strong>：相同的构建过程可以创建不同的表示</li>
                <li><strong>灵活性</strong>：可以轻松添加新的表示而不改变现有代码</li>
            </ol>
        </div>
        
        <div class="explanation">
            <h3>总结</h3>
            <p>"当构造过程必须允许被构造的对象有不同的表示时"意味着：</p>
            <p>我们需要一种机制，使得<span class="highlight">相同的构建步骤可以产生不同的最终产品</span>。建造者模式正是为了解决这个问题而设计的。</p>
            <p>通过将构建过程与最终产品的表示分离，我们可以使用相同的构建代码创建不同的对象，这大大提高了代码的灵活性和可维护性。</p>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const typeButtons = document.querySelectorAll('.house-type');
        const stepButtons = document.querySelectorAll('[id^="step-"]');
        const stepList = document.getElementById('step-list');
        const houseImage = document.getElementById('house-image');
        const houseDetails = document.getElementById('house-details');
        
        // 房屋数据
        const houseTypes = {
            villa: {
                name: '别墅',
                finalImage: '🏛️',
                steps: {
                    foundation: { text: '打造深层加固地基', image: '🏗️' },
                    walls: { text: '砌筑高品质砖石墙', image: '🧱' },
                    windows: { text: '安装大型落地窗', image: '🪟' },
                    roof: { text: '建造复杂坡顶', image: '🏠' },
                    interior: { text: '完成豪华装修', image: '🏛️' }
                }
            },
            apartment: {
                name: '公寓',
                finalImage: '🏢',
                steps: {
                    foundation: { text: '打造标准地基', image: '🏗️' },
                    walls: { text: '浇筑混凝土墙', image: '🧱' },
                    windows: { text: '安装标准窗户', image: '🪟' },
                    roof: { text: '建造平顶', image: '🏠' },
                    interior: { text: '完成现代简约装修', image: '🏢' }
                }
            },
            cottage: {
                name: '平房',
                finalImage: '🏡',
                steps: {
                    foundation: { text: '打造简易地基', image: '🏗️' },
                    walls: { text: '搭建木质墙', image: '🧱' },
                    windows: { text: '安装小型窗户', image: '🪟' },
                    roof: { text: '建造简单坡顶', image: '🏠' },
                    interior: { text: '完成舒适乡村风格装修', image: '🏡' }
                }
            }
        };
        
        // 当前状态
        let currentType = null;
        let currentStep = 0;
        const totalSteps = 5;
        
        // 初始化步骤列表
        function resetStepList() {
            stepList.innerHTML = '<li>请先选择房屋类型</li>';
        }
        
        // 选择房屋类型
        typeButtons.forEach(button => {
            button.addEventListener('click', () => {
                // 重置当前状态
                currentStep = 0;
                
                // 获取选择的类型
                const typeId = button.id.split('-')[1];
                currentType = typeId;
                
                // 更新UI
                typeButtons.forEach(btn => btn.classList.remove('selected'));
                button.classList.add('selected');
                
                // 启用第一个步骤按钮
                document.getElementById('step-foundation').disabled = false;
                document.getElementById('step-walls').disabled = true;
                document.getElementById('step-windows').disabled = true;
                document.getElementById('step-roof').disabled = true;
                document.getElementById('step-interior').disabled = true;
                
                // 更新步骤列表
                stepList.innerHTML = `<li>选择了 ${houseTypes[typeId].name} 类型</li>`;
                
                // 更新房屋显示
                houseImage.innerHTML = '🏗️';
                houseDetails.innerText = `准备建造 ${houseTypes[typeId].name}`;
            });
        });
        
        // 执行建造步骤
        document.getElementById('step-foundation').addEventListener('click', () => executeStep('foundation', 1));
        document.getElementById('step-walls').addEventListener('click', () => executeStep('walls', 2));
        document.getElementById('step-windows').addEventListener('click', () => executeStep('windows', 3));
        document.getElementById('step-roof').addEventListener('click', () => executeStep('roof', 4));
        document.getElementById('step-interior').addEventListener('click', () => executeStep('interior', 5));
        
        function executeStep(stepName, stepNumber) {
            if (!currentType || stepNumber !== currentStep + 1) return;
            
            currentStep = stepNumber;
            
            // 获取步骤信息
            const step = houseTypes[currentType].steps[stepName];
            
            // 添加到步骤列表
            const listItem = document.createElement('li');
            listItem.innerText = `步骤 ${stepNumber}: ${step.text}`;
            stepList.appendChild(listItem);
            
            // 更新房屋图像
            houseImage.innerHTML = step.image;
            
            // 更新房屋详情
            if (stepNumber === totalSteps) {
                houseDetails.innerText = `${houseTypes[currentType].name} 建造完成！`;
            } else {
                houseDetails.innerText = `${houseTypes[currentType].name} 建造中...`;
            }
            
            // 启用下一个步骤按钮
            if (stepNumber < totalSteps) {
                const nextStepId = Object.keys(houseTypes[currentType].steps)[stepNumber];
                document.getElementById(`step-${nextStepId}`).disabled = false;
            }
            
            // 禁用当前步骤按钮
            document.getElementById(`step-${stepName}`).disabled = true;
        }
        
        // 初始化
        resetStepList();
    </script>

</body>
</html> 