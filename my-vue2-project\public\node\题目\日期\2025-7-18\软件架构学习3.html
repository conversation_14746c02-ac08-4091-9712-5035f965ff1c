<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件系统架构 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .learning-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .concept-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .concept-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        .explanation {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .interactive-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid transparent;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .quiz-option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .quiz-option.correct {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.3);
        }

        .quiz-option.wrong {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.3);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>软件系统架构</h1>
            <p>通过动画和交互学习软件架构的核心概念</p>
        </div>

        <div class="learning-section">
            <h2 class="concept-title">什么是软件系统架构？</h2>
            <div class="canvas-container">
                <canvas id="architectureCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-buttons">
                <button class="btn" onclick="showStructure()">显示结构</button>
                <button class="btn" onclick="showBehavior()">显示行为</button>
                <button class="btn" onclick="showProperties()">显示属性</button>
                <button class="btn" onclick="showInteraction()">组件交互</button>
            </div>
            <div class="explanation">
                <h3>核心概念解析</h3>
                <p>软件系统架构是关于软件系统的<strong>结构、行为和属性</strong>的高级抽象。它就像建筑的蓝图，定义了系统的整体框架和组件之间的关系。</p>
                <br>
                <h4>🏗️ 描述阶段 vs 实现阶段</h4>
                <p><strong>描述阶段：</strong>主要描述抽象组件和连接规则，特别关注<span style="color: #667eea; font-weight: bold;">组件的交互关系</span></p>
                <p><strong>实现阶段：</strong>将抽象组件细化为具体的类或对象</p>
                <br>
                <h4>🎯 架构的三大要素</h4>
                <p>• <strong>结构：</strong>组件的组织方式和层次关系</p>
                <p>• <strong>行为：</strong>组件之间的交互和数据流动</p>
                <p>• <strong>属性：</strong>性能、安全性、可维护性等质量特征</p>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="concept-title">深入理解：组件交互关系</h2>
            <div class="canvas-container">
                <canvas id="interactionCanvas" width="800" height="300"></canvas>
            </div>
            <div class="interactive-buttons">
                <button class="btn" onclick="showDataFlow()">数据流动</button>
                <button class="btn" onclick="showMessagePassing()">消息传递</button>
                <button class="btn" onclick="showEventDriven()">事件驱动</button>
                <button class="btn" onclick="resetInteraction()">重置</button>
            </div>
            <div class="explanation">
                <h3>为什么是"交互关系"而不是其他选项？</h3>
                <p><strong>🔄 交互关系：</strong>描述组件之间如何通信、协作和数据交换，这是架构设计的核心关注点</p>
                <p><strong>⚙️ 实现关系：</strong>属于具体实现层面，不是架构描述阶段的重点</p>
                <p><strong>📊 数据依赖：</strong>只是交互关系的一个方面，不够全面</p>
                <p><strong>🎯 功能依赖：</strong>更多关注功能层面，而非架构层面的组件关系</p>
            </div>
        </div>

        <div class="learning-section quiz-section">
            <h2 class="concept-title">知识测试</h2>
            <div class="quiz-question">
                软件系统架构是关于软件系统的结构、（ ）和属性的高级抽象。在描述阶段，主要描述直接构成系统的抽象组件以及各个组件之间的连接规则，特别是相对细致地描述组件的（ ）。
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 交互关系</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">B. 实现关系</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">C. 数据依赖</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 功能依赖</div>
            </div>
            <div id="quizResult" style="margin-top: 20px; font-size: 1.2rem;"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');
        let animationFrame;
        let currentDemo = 'structure';

        // 组件类
        class Component {
            constructor(x, y, width, height, name, color) {
                this.x = x;
                this.y = y;
                this.width = width;
                this.height = height;
                this.name = name;
                this.color = color;
                this.scale = 1;
                this.opacity = 1;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.opacity;
                ctx.translate(this.x + this.width/2, this.y + this.height/2);
                ctx.scale(this.scale, this.scale);
                
                // 绘制组件
                ctx.fillStyle = this.color;
                ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
                
                // 绘制边框
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);
                
                // 绘制文字
                ctx.fillStyle = '#333';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(this.name, 0, 5);
                
                ctx.restore();
            }

            animate(targetScale, targetOpacity) {
                this.scale += (targetScale - this.scale) * 0.1;
                this.opacity += (targetOpacity - this.opacity) * 0.1;
            }
        }

        // 创建组件
        const components = [
            new Component(100, 100, 120, 80, 'UI层', '#FFB6C1'),
            new Component(300, 100, 120, 80, '业务层', '#98FB98'),
            new Component(500, 100, 120, 80, '数据层', '#87CEEB'),
            new Component(200, 250, 120, 80, '服务A', '#DDA0DD'),
            new Component(400, 250, 120, 80, '服务B', '#F0E68C')
        ];

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawConnections() {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            
            // 绘制连接线
            ctx.beginPath();
            ctx.moveTo(220, 140);
            ctx.lineTo(300, 140);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(420, 140);
            ctx.lineTo(500, 140);
            ctx.stroke();
            
            ctx.setLineDash([]);
        }

        function showStructure() {
            currentDemo = 'structure';
            components.forEach((comp, index) => {
                comp.animate(1, 1);
            });
        }

        function showBehavior() {
            currentDemo = 'behavior';
            // 动画显示数据流
            let time = 0;
            function behaviorAnimation() {
                clearCanvas();
                drawGrid();
                drawConnections();

                // 绘制流动的数据包
                const dataPacket = {
                    x: 160 + Math.sin(time * 0.05) * 100,
                    y: 140,
                    size: 8
                };

                ctx.fillStyle = '#FF6B6B';
                ctx.beginPath();
                ctx.arc(dataPacket.x, dataPacket.y, dataPacket.size, 0, Math.PI * 2);
                ctx.fill();

                components.forEach(comp => comp.draw());

                time++;
                if (currentDemo === 'behavior') {
                    requestAnimationFrame(behaviorAnimation);
                }
            }
            behaviorAnimation();
        }

        function showProperties() {
            currentDemo = 'properties';
            components.forEach((comp, index) => {
                comp.animate(1.2, 0.8);
                setTimeout(() => {
                    comp.animate(1, 1);
                }, 500 + index * 200);
            });
        }

        function showInteraction() {
            currentDemo = 'interaction';
            let step = 0;
            function interactionAnimation() {
                clearCanvas();
                drawGrid();

                // 绘制动态连接线
                ctx.strokeStyle = `hsl(${step * 2}, 70%, 50%)`;
                ctx.lineWidth = 3;
                ctx.setLineDash([]);

                ctx.beginPath();
                ctx.moveTo(220, 140);
                ctx.lineTo(300, 140);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(420, 140);
                ctx.lineTo(500, 140);
                ctx.stroke();

                // 绘制脉冲效果
                const pulseRadius = (step % 50) * 2;
                ctx.strokeStyle = `rgba(255, 107, 107, ${1 - (step % 50) / 50})`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(260, 140, pulseRadius, 0, Math.PI * 2);
                ctx.stroke();

                components.forEach(comp => comp.draw());

                step++;
                if (currentDemo === 'interaction') {
                    requestAnimationFrame(interactionAnimation);
                }
            }
            interactionAnimation();
        }

        function drawGrid() {
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for(let i = 0; i < canvas.width; i += 40) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for(let i = 0; i < canvas.height; i += 40) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
        }

        function animate() {
            if (currentDemo === 'structure') {
                clearCanvas();
                drawGrid();
                drawConnections();

                components.forEach(comp => {
                    comp.animate(1, 1);
                    comp.draw();
                });

                animationFrame = requestAnimationFrame(animate);
            }
        }

        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (opt.textContent.includes('交互关系')) {
                    opt.classList.add('correct');
                }
            });
            
            const result = document.getElementById('quizResult');
            if (isCorrect) {
                result.innerHTML = '🎉 正确！答案是A. 交互关系<br>软件架构主要关注组件之间的交互关系，这是架构设计的核心。';
                result.style.color = '#4CAF50';
            } else {
                result.innerHTML = '❌ 很遗憾，正确答案是A. 交互关系<br>在描述阶段，架构特别关注组件的交互关系，而不是具体的实现细节。';
                result.style.color = '#f44336';
            }
        }

        // 第二个画布的功能
        const interactionCanvas = document.getElementById('interactionCanvas');
        const interactionCtx = interactionCanvas.getContext('2d');

        function showDataFlow() {
            let time = 0;
            function dataFlowAnimation() {
                interactionCtx.clearRect(0, 0, interactionCanvas.width, interactionCanvas.height);

                // 绘制组件
                interactionCtx.fillStyle = '#FFB6C1';
                interactionCtx.fillRect(50, 100, 100, 60);
                interactionCtx.fillStyle = '#98FB98';
                interactionCtx.fillRect(350, 100, 100, 60);
                interactionCtx.fillStyle = '#87CEEB';
                interactionCtx.fillRect(650, 100, 100, 60);

                // 绘制标签
                interactionCtx.fillStyle = '#333';
                interactionCtx.font = '14px Microsoft YaHei';
                interactionCtx.textAlign = 'center';
                interactionCtx.fillText('组件A', 100, 135);
                interactionCtx.fillText('组件B', 400, 135);
                interactionCtx.fillText('组件C', 700, 135);

                // 绘制流动的数据
                const dataX1 = 150 + Math.sin(time * 0.1) * 100;
                const dataX2 = 450 + Math.sin(time * 0.1 + Math.PI) * 100;

                interactionCtx.fillStyle = '#FF6B6B';
                interactionCtx.beginPath();
                interactionCtx.arc(dataX1, 130, 6, 0, Math.PI * 2);
                interactionCtx.fill();

                interactionCtx.fillStyle = '#4ECDC4';
                interactionCtx.beginPath();
                interactionCtx.arc(dataX2, 130, 6, 0, Math.PI * 2);
                interactionCtx.fill();

                time++;
                requestAnimationFrame(dataFlowAnimation);
            }
            dataFlowAnimation();
        }

        function showMessagePassing() {
            interactionCtx.clearRect(0, 0, interactionCanvas.width, interactionCanvas.height);

            // 绘制组件
            interactionCtx.fillStyle = '#DDA0DD';
            interactionCtx.fillRect(100, 50, 120, 80);
            interactionCtx.fillRect(300, 150, 120, 80);
            interactionCtx.fillRect(500, 50, 120, 80);

            // 绘制消息箭头
            interactionCtx.strokeStyle = '#FF6B6B';
            interactionCtx.lineWidth = 3;
            interactionCtx.setLineDash([]);

            // 箭头1
            interactionCtx.beginPath();
            interactionCtx.moveTo(220, 90);
            interactionCtx.lineTo(300, 170);
            interactionCtx.stroke();

            // 箭头2
            interactionCtx.beginPath();
            interactionCtx.moveTo(420, 190);
            interactionCtx.lineTo(500, 110);
            interactionCtx.stroke();

            // 绘制标签
            interactionCtx.fillStyle = '#333';
            interactionCtx.font = '12px Microsoft YaHei';
            interactionCtx.textAlign = 'center';
            interactionCtx.fillText('服务A', 160, 95);
            interactionCtx.fillText('服务B', 360, 195);
            interactionCtx.fillText('服务C', 560, 95);
        }

        function showEventDriven() {
            let pulseTime = 0;
            function eventAnimation() {
                interactionCtx.clearRect(0, 0, interactionCanvas.width, interactionCanvas.height);

                // 中心事件总线
                const centerX = 400;
                const centerY = 150;
                const pulseRadius = 30 + Math.sin(pulseTime * 0.1) * 10;

                interactionCtx.fillStyle = '#FFD700';
                interactionCtx.beginPath();
                interactionCtx.arc(centerX, centerY, pulseRadius, 0, Math.PI * 2);
                interactionCtx.fill();

                interactionCtx.fillStyle = '#333';
                interactionCtx.font = '12px Microsoft YaHei';
                interactionCtx.textAlign = 'center';
                interactionCtx.fillText('事件总线', centerX, centerY + 5);

                // 周围的组件
                const components = [
                    {x: 200, y: 80, name: '监听器A'},
                    {x: 600, y: 80, name: '监听器B'},
                    {x: 200, y: 220, name: '发布者A'},
                    {x: 600, y: 220, name: '发布者B'}
                ];

                components.forEach((comp, index) => {
                    interactionCtx.fillStyle = '#E6E6FA';
                    interactionCtx.fillRect(comp.x - 40, comp.y - 20, 80, 40);

                    interactionCtx.fillStyle = '#333';
                    interactionCtx.fillText(comp.name, comp.x, comp.y + 5);

                    // 绘制连接线
                    interactionCtx.strokeStyle = `hsl(${pulseTime * 2 + index * 90}, 70%, 50%)`;
                    interactionCtx.lineWidth = 2;
                    interactionCtx.beginPath();
                    interactionCtx.moveTo(comp.x, comp.y);
                    interactionCtx.lineTo(centerX, centerY);
                    interactionCtx.stroke();
                });

                pulseTime++;
                requestAnimationFrame(eventAnimation);
            }
            eventAnimation();
        }

        function resetInteraction() {
            interactionCtx.clearRect(0, 0, interactionCanvas.width, interactionCanvas.height);

            interactionCtx.fillStyle = '#f0f0f0';
            interactionCtx.fillRect(0, 0, interactionCanvas.width, interactionCanvas.height);

            interactionCtx.fillStyle = '#666';
            interactionCtx.font = '20px Microsoft YaHei';
            interactionCtx.textAlign = 'center';
            interactionCtx.fillText('点击上方按钮查看不同的交互模式', interactionCanvas.width/2, interactionCanvas.height/2);
        }

        // 启动动画
        animate();
        showStructure();
        resetInteraction();
    </script>
</body>
</html>
