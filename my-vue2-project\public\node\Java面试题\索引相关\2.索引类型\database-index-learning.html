<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库索引学习 - 交互式教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .index-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .index-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .index-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .index-card:hover::before {
            transform: scaleX(1);
        }

        .index-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .index-card h3 {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .index-card p {
            color: #666;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .demo-button:active {
            transform: translateY(0);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .game-score {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="game-score">
        学习进度: <span id="score">0</span>/100
    </div>

    <div class="container">
        <div class="header">
            <h1>数据库索引学习之旅</h1>
            <p>通过动画和交互，轻松掌握数据库索引知识</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 数据结构维度 -->
        <div class="section">
            <h2 class="section-title">数据结构维度</h2>
            <div class="canvas-container">
                <canvas id="dataStructureCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animateBPlusTree()">B+树演示</button>
                <button class="demo-button" onclick="animateHashIndex()">哈希索引演示</button>
                <button class="demo-button" onclick="animateFullTextIndex()">全文索引演示</button>
                <button class="demo-button" onclick="animateRTreeIndex()">R-Tree演示</button>
            </div>
            <div class="index-grid">
                <div class="index-card" onclick="showBPlusTreeDetail()">
                    <h3>🌳 B+树索引</h3>
                    <p>所有数据存储在叶子节点，复杂度为O(logn)，适合范围查询。B+树是一种平衡多路搜索树，非常适合数据库和文件系统。</p>
                </div>
                <div class="index-card" onclick="showHashIndexDetail()">
                    <h3>🔍 哈希索引</h3>
                    <p>适合等值查询，检索效率高，一次到位。通过哈希函数将键值映射到特定位置，查询速度极快。</p>
                </div>
                <div class="index-card" onclick="showFullTextDetail()">
                    <h3>📝 全文索引</h3>
                    <p>MyISAM和InnoDB中都支持使用全文索引，一般在文本类型char,text,varchar类型上创建，用于文本搜索。</p>
                </div>
                <div class="index-card" onclick="showRTreeDetail()">
                    <h3>🗺️ R-Tree索引</h3>
                    <p>用来对GIS数据类型创建SPATIAL索引，专门处理空间数据的多维索引结构。</p>
                </div>
            </div>
        </div>

        <!-- 物理存储维度 -->
        <div class="section">
            <h2 class="section-title">物理存储维度</h2>
            <div class="canvas-container">
                <canvas id="storageCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animateClusteredIndex()">聚集索引演示</button>
                <button class="demo-button" onclick="animateNonClusteredIndex()">非聚集索引演示</button>
                <button class="demo-button" onclick="compareIndexTypes()">对比演示</button>
            </div>
            <div class="index-grid">
                <div class="index-card" onclick="showClusteredDetail()">
                    <h3>🎯 聚集索引</h3>
                    <p>聚集索引就是以主键创建的索引，在叶子节点存储的是表中的数据。数据行的物理顺序与索引顺序相同。</p>
                </div>
                <div class="index-card" onclick="showNonClusteredDetail()">
                    <h3>🔗 非聚集索引</h3>
                    <p>非聚集索引就是以非主键创建的索引，在叶子节点存储的是主键和索引列。需要回表查询获取完整数据。</p>
                </div>
            </div>
        </div>

        <!-- 逻辑维度 -->
        <div class="section">
            <h2 class="section-title">逻辑维度</h2>
            <div class="canvas-container">
                <canvas id="logicalCanvas" width="800" height="500"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animatePrimaryIndex()">主键索引</button>
                <button class="demo-button" onclick="animateNormalIndex()">普通索引</button>
                <button class="demo-button" onclick="animateCompositeIndex()">联合索引</button>
                <button class="demo-button" onclick="animateUniqueIndex()">唯一索引</button>
                <button class="demo-button" onclick="animateSpatialIndex()">空间索引</button>
            </div>
            <div class="index-grid">
                <div class="index-card" onclick="showPrimaryDetail()">
                    <h3>🔑 主键索引</h3>
                    <p>一种特殊的唯一索引，不允许有空值。每个表只能有一个主键索引，是表中记录的唯一标识。</p>
                </div>
                <div class="index-card" onclick="showNormalDetail()">
                    <h3>📋 普通索引</h3>
                    <p>MySQL中基本索引类型，允许空值和重复值。最常用的索引类型，用于加速查询。</p>
                </div>
                <div class="index-card" onclick="showCompositeDetail()">
                    <h3>🔗 联合索引</h3>
                    <p>多个字段创建的索引，使用时遵循最左前缀原则。可以减少索引数量，提高查询效率。</p>
                </div>
                <div class="index-card" onclick="showUniqueDetail()">
                    <h3>⭐ 唯一索引</h3>
                    <p>索引列中的值必须是唯一的，但是允许为空值。保证数据的唯一性约束。</p>
                </div>
                <div class="index-card" onclick="showSpatialDetail()">
                    <h3>🌍 空间索引</h3>
                    <p>MySQL5.7之后支持空间索引，在空间索引这方面遵循OpenGIS几何数据模型规则，用于地理信息系统。</p>
                </div>
            </div>
        </div>

        <!-- 互动游戏区域 -->
        <div class="section">
            <h2 class="section-title">🎮 索引知识挑战</h2>
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="startQuizGame()">开始知识问答</button>
                <button class="demo-button" onclick="startIndexMatchGame()">索引匹配游戏</button>
                <button class="demo-button" onclick="startPerformanceGame()">性能优化挑战</button>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // 全局变量
        let score = 0;
        let currentAnimation = null;
        let gameState = {
            currentQuestion: 0,
            correctAnswers: 0,
            gameMode: null
        };

        // 工具函数
        function updateScore(points) {
            score += points;
            document.getElementById('score').textContent = score;
            document.getElementById('progressFill').style.width = Math.min(score, 100) + '%';
        }

        function showTooltip(text, x, y) {
            const tooltip = document.getElementById('tooltip');
            tooltip.textContent = text;
            tooltip.style.left = x + 'px';
            tooltip.style.top = y + 'px';
            tooltip.style.opacity = '1';
            setTimeout(() => {
                tooltip.style.opacity = '0';
            }, 2000);
        }

        // Canvas 动画基础类
        class CanvasAnimation {
            constructor(canvasId) {
                this.canvas = document.getElementById(canvasId);
                this.ctx = this.canvas.getContext('2d');
                this.animationId = null;
                this.particles = [];
            }

            clear() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            }

            drawNode(x, y, radius, text, color = '#667eea') {
                this.ctx.beginPath();
                this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
                this.ctx.fillStyle = color;
                this.ctx.fill();
                this.ctx.strokeStyle = '#fff';
                this.ctx.lineWidth = 3;
                this.ctx.stroke();

                this.ctx.fillStyle = '#fff';
                this.ctx.font = '14px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(text, x, y + 5);
            }

            drawLine(x1, y1, x2, y2, color = '#333') {
                this.ctx.beginPath();
                this.ctx.moveTo(x1, y1);
                this.ctx.lineTo(x2, y2);
                this.ctx.strokeStyle = color;
                this.ctx.lineWidth = 2;
                this.ctx.stroke();
            }

            drawArrow(x1, y1, x2, y2, color = '#667eea') {
                const angle = Math.atan2(y2 - y1, x2 - x1);
                const arrowLength = 15;

                this.drawLine(x1, y1, x2, y2, color);

                this.ctx.beginPath();
                this.ctx.moveTo(x2, y2);
                this.ctx.lineTo(x2 - arrowLength * Math.cos(angle - Math.PI / 6),
                               y2 - arrowLength * Math.sin(angle - Math.PI / 6));
                this.ctx.moveTo(x2, y2);
                this.ctx.lineTo(x2 - arrowLength * Math.cos(angle + Math.PI / 6),
                               y2 - arrowLength * Math.sin(angle + Math.PI / 6));
                this.ctx.strokeStyle = color;
                this.ctx.lineWidth = 2;
                this.ctx.stroke();
            }

            addParticle(x, y, color = '#667eea') {
                this.particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 4,
                    vy: (Math.random() - 0.5) * 4,
                    life: 1.0,
                    color: color
                });
            }

            updateParticles() {
                this.particles = this.particles.filter(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    particle.life -= 0.02;

                    if (particle.life > 0) {
                        this.ctx.globalAlpha = particle.life;
                        this.ctx.fillStyle = particle.color;
                        this.ctx.beginPath();
                        this.ctx.arc(particle.x, particle.y, 3, 0, 2 * Math.PI);
                        this.ctx.fill();
                        this.ctx.globalAlpha = 1;
                        return true;
                    }
                    return false;
                });
            }
        }

        // B+树动画
        function animateBPlusTree() {
            const animation = new CanvasAnimation('dataStructureCanvas');
            let step = 0;

            function animate() {
                animation.clear();

                // 绘制B+树结构
                const rootY = 80;
                const leafY = 300;
                const nodeSpacing = 150;

                // 根节点
                animation.drawNode(400, rootY, 30, '50', '#667eea');

                // 内部节点
                if (step > 0) {
                    animation.drawNode(250, rootY + 80, 25, '25', '#764ba2');
                    animation.drawNode(550, rootY + 80, 25, '75', '#764ba2');
                    animation.drawLine(400, rootY + 30, 250, rootY + 50);
                    animation.drawLine(400, rootY + 30, 550, rootY + 50);
                }

                // 叶子节点
                if (step > 1) {
                    const leafNodes = [
                        {x: 150, data: '10,20'},
                        {x: 300, data: '30,40'},
                        {x: 450, data: '60,70'},
                        {x: 600, data: '80,90'}
                    ];

                    leafNodes.forEach((node, i) => {
                        animation.drawNode(node.x, leafY, 35, node.data, '#52c41a');
                        if (i < leafNodes.length - 1) {
                            animation.drawArrow(node.x + 35, leafY, leafNodes[i + 1].x - 35, leafY, '#52c41a');
                        }
                    });

                    // 连接内部节点到叶子节点
                    animation.drawLine(250, rootY + 105, 150, leafY - 35);
                    animation.drawLine(250, rootY + 105, 300, leafY - 35);
                    animation.drawLine(550, rootY + 105, 450, leafY - 35);
                    animation.drawLine(550, rootY + 105, 600, leafY - 35);
                }

                // 搜索动画
                if (step > 2) {
                    const searchValue = 65;
                    animation.ctx.fillStyle = '#ff4d4f';
                    animation.ctx.font = '16px Arial';
                    animation.ctx.fillText(`搜索值: ${searchValue}`, 50, 50);

                    // 高亮搜索路径
                    if (step > 3) {
                        animation.drawNode(400, rootY, 30, '50', '#ff4d4f');
                        animation.addParticle(400, rootY, '#ff4d4f');
                    }
                    if (step > 4) {
                        animation.drawNode(550, rootY + 80, 25, '75', '#ff4d4f');
                        animation.addParticle(550, rootY + 80, '#ff4d4f');
                    }
                    if (step > 5) {
                        animation.drawNode(450, leafY, 35, '60,70', '#ff4d4f');
                        animation.addParticle(450, leafY, '#ff4d4f');
                    }
                }

                animation.updateParticles();

                step++;
                if (step < 8) {
                    setTimeout(() => requestAnimationFrame(animate), 1000);
                } else {
                    updateScore(10);
                    showTooltip('B+树演示完成！+10分', 400, 200);
                }
            }

            animate();
        }

        // 哈希索引动画
        function animateHashIndex() {
            const animation = new CanvasAnimation('dataStructureCanvas');
            let step = 0;

            function animate() {
                animation.clear();

                // 绘制哈希表
                const buckets = 8;
                const bucketHeight = 40;
                const startY = 50;

                for (let i = 0; i < buckets; i++) {
                    const y = startY + i * bucketHeight;
                    animation.ctx.fillStyle = '#f0f0f0';
                    animation.ctx.fillRect(300, y, 200, 35);
                    animation.ctx.strokeStyle = '#333';
                    animation.ctx.strokeRect(300, y, 200, 35);
                    animation.ctx.fillStyle = '#333';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText(`Bucket ${i}`, 310, y + 22);
                }

                if (step > 0) {
                    // 演示哈希函数
                    const key = 'name';
                    const hashValue = 3; // hash(name) = 3

                    animation.ctx.fillStyle = '#667eea';
                    animation.ctx.font = '16px Arial';
                    animation.ctx.fillText(`Key: "${key}"`, 50, 100);
                    animation.ctx.fillText(`Hash(${key}) = ${hashValue}`, 50, 130);

                    if (step > 1) {
                        // 高亮对应的bucket
                        const targetY = startY + hashValue * bucketHeight;
                        animation.ctx.fillStyle = 'rgba(255, 77, 79, 0.3)';
                        animation.ctx.fillRect(300, targetY, 200, 35);

                        // 绘制箭头
                        animation.drawArrow(200, 115, 300, targetY + 17, '#ff4d4f');

                        if (step > 2) {
                            // 显示数据
                            animation.ctx.fillStyle = '#52c41a';
                            animation.ctx.fillText('Data: John', 520, targetY + 22);
                            animation.addParticle(450, targetY + 17, '#52c41a');
                        }
                    }
                }

                animation.updateParticles();

                step++;
                if (step < 5) {
                    setTimeout(() => requestAnimationFrame(animate), 1200);
                } else {
                    updateScore(10);
                    showTooltip('哈希索引演示完成！+10分', 400, 200);
                }
            }

            animate();
        }

        // 全文索引动画
        function animateFullTextIndex() {
            const animation = new CanvasAnimation('dataStructureCanvas');
            let step = 0;

            function animate() {
                animation.clear();

                const text = "MySQL supports full text indexing";
                const words = text.split(' ');

                // 显示原文本
                animation.ctx.fillStyle = '#333';
                animation.ctx.font = '18px Arial';
                animation.ctx.fillText('原文本: ' + text, 50, 50);

                if (step > 0) {
                    // 分词过程
                    animation.ctx.fillStyle = '#667eea';
                    animation.ctx.font = '16px Arial';
                    animation.ctx.fillText('分词结果:', 50, 100);

                    words.forEach((word, i) => {
                        const x = 50 + i * 100;
                        const y = 130;
                        if (step > i + 1) {
                            animation.ctx.fillStyle = '#52c41a';
                            animation.ctx.fillRect(x, y, 80, 30);
                            animation.ctx.fillStyle = '#fff';
                            animation.ctx.fillText(word, x + 5, y + 20);
                            animation.addParticle(x + 40, y + 15, '#52c41a');
                        }
                    });
                }

                if (step > words.length + 2) {
                    // 倒排索引
                    animation.ctx.fillStyle = '#764ba2';
                    animation.ctx.font = '16px Arial';
                    animation.ctx.fillText('倒排索引:', 50, 200);

                    const index = [
                        {word: 'MySQL', docs: '[1,3,5]'},
                        {word: 'full', docs: '[1,2]'},
                        {word: 'text', docs: '[1,2,4]'},
                        {word: 'indexing', docs: '[1,3]'}
                    ];

                    index.forEach((item, i) => {
                        const y = 230 + i * 30;
                        if (step > words.length + 3 + i) {
                            animation.ctx.fillStyle = '#ff4d4f';
                            animation.ctx.fillText(`${item.word} → ${item.docs}`, 70, y);
                            animation.addParticle(200, y - 10, '#ff4d4f');
                        }
                    });
                }

                animation.updateParticles();

                step++;
                if (step < words.length + 8) {
                    setTimeout(() => requestAnimationFrame(animate), 800);
                } else {
                    updateScore(10);
                    showTooltip('全文索引演示完成！+10分', 400, 200);
                }
            }

            animate();
        }

        // R-Tree索引动画
        function animateRTreeIndex() {
            const animation = new CanvasAnimation('dataStructureCanvas');
            let step = 0;

            function animate() {
                animation.clear();

                // 绘制坐标系
                animation.drawLine(50, 350, 750, 350, '#ccc'); // X轴
                animation.drawLine(50, 50, 50, 350, '#ccc'); // Y轴

                // 标记坐标
                animation.ctx.fillStyle = '#666';
                animation.ctx.font = '12px Arial';
                animation.ctx.fillText('X', 760, 355);
                animation.ctx.fillText('Y', 45, 45);

                // 空间对象
                const objects = [
                    {x: 100, y: 100, w: 80, h: 60, name: 'A'},
                    {x: 200, y: 150, w: 70, h: 50, name: 'B'},
                    {x: 400, y: 80, w: 90, h: 70, name: 'C'},
                    {x: 500, y: 200, w: 60, h: 80, name: 'D'}
                ];

                if (step > 0) {
                    objects.forEach((obj, i) => {
                        if (step > i + 1) {
                            animation.ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                            animation.ctx.fillRect(obj.x, obj.y, obj.w, obj.h);
                            animation.ctx.strokeStyle = '#667eea';
                            animation.ctx.strokeRect(obj.x, obj.y, obj.w, obj.h);
                            animation.ctx.fillStyle = '#333';
                            animation.ctx.fillText(obj.name, obj.x + obj.w/2, obj.y + obj.h/2);
                            animation.addParticle(obj.x + obj.w/2, obj.y + obj.h/2, '#667eea');
                        }
                    });
                }

                if (step > objects.length + 2) {
                    // 绘制MBR (Minimum Bounding Rectangle)
                    animation.ctx.strokeStyle = '#ff4d4f';
                    animation.ctx.lineWidth = 3;
                    animation.ctx.strokeRect(90, 70, 200, 150); // MBR1
                    animation.ctx.strokeRect(390, 70, 180, 160); // MBR2

                    animation.ctx.fillStyle = '#ff4d4f';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText('MBR1', 150, 65);
                    animation.ctx.fillText('MBR2', 450, 65);
                }

                animation.updateParticles();

                step++;
                if (step < objects.length + 6) {
                    setTimeout(() => requestAnimationFrame(animate), 1000);
                } else {
                    updateScore(10);
                    showTooltip('R-Tree索引演示完成！+10分', 400, 200);
                }
            }

            animate();
        }

        // 聚集索引动画
        function animateClusteredIndex() {
            const animation = new CanvasAnimation('storageCanvas');
            let step = 0;

            function animate() {
                animation.clear();

                // 标题
                animation.ctx.fillStyle = '#333';
                animation.ctx.font = '18px Arial';
                animation.ctx.fillText('聚集索引 - 数据与索引存储在一起', 200, 30);

                // 绘制B+树结构
                const rootY = 80;
                const leafY = 280;

                // 根节点
                if (step > 0) {
                    animation.drawNode(400, rootY, 25, '50', '#667eea');
                }

                // 内部节点
                if (step > 1) {
                    animation.drawNode(250, rootY + 60, 20, '25', '#764ba2');
                    animation.drawNode(550, rootY + 60, 20, '75', '#764ba2');
                    animation.drawLine(400, rootY + 25, 250, rootY + 40);
                    animation.drawLine(400, rootY + 25, 550, rootY + 40);
                }

                // 叶子节点（包含完整数据）
                if (step > 2) {
                    const leafData = [
                        {id: 10, name: 'Alice', age: 25},
                        {id: 20, name: 'Bob', age: 30},
                        {id: 60, name: 'Charlie', age: 35},
                        {id: 80, name: 'David', age: 40}
                    ];

                    leafData.forEach((data, i) => {
                        const x = 150 + i * 150;
                        // 绘制数据块
                        animation.ctx.fillStyle = '#52c41a';
                        animation.ctx.fillRect(x - 60, leafY - 20, 120, 40);
                        animation.ctx.strokeStyle = '#fff';
                        animation.ctx.strokeRect(x - 60, leafY - 20, 120, 40);

                        animation.ctx.fillStyle = '#fff';
                        animation.ctx.font = '10px Arial';
                        animation.ctx.fillText(`ID:${data.id}`, x - 50, leafY - 5);
                        animation.ctx.fillText(`${data.name}`, x - 50, leafY + 8);
                        animation.ctx.fillText(`Age:${data.age}`, x - 50, leafY + 21);

                        if (step > 3 + i) {
                            animation.addParticle(x, leafY, '#52c41a');
                        }
                    });

                    // 连接线
                    animation.drawLine(250, rootY + 80, 150, leafY - 20);
                    animation.drawLine(250, rootY + 80, 300, leafY - 20);
                    animation.drawLine(550, rootY + 80, 450, leafY - 20);
                    animation.drawLine(550, rootY + 80, 600, leafY - 20);
                }

                if (step > 7) {
                    animation.ctx.fillStyle = '#ff4d4f';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText('✓ 叶子节点直接存储完整数据行', 50, 350);
                    animation.ctx.fillText('✓ 数据物理顺序与索引顺序一致', 50, 370);
                }

                animation.updateParticles();

                step++;
                if (step < 10) {
                    setTimeout(() => requestAnimationFrame(animate), 800);
                } else {
                    updateScore(15);
                    showTooltip('聚集索引演示完成！+15分', 400, 200);
                }
            }

            animate();
        }

        // 非聚集索引动画
        function animateNonClusteredIndex() {
            const animation = new CanvasAnimation('storageCanvas');
            let step = 0;

            function animate() {
                animation.clear();

                // 标题
                animation.ctx.fillStyle = '#333';
                animation.ctx.font = '18px Arial';
                animation.ctx.fillText('非聚集索引 - 索引与数据分离存储', 200, 30);

                // 索引结构
                if (step > 0) {
                    animation.ctx.fillStyle = '#764ba2';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText('索引结构 (按name排序)', 50, 80);

                    const indexEntries = [
                        {name: 'Alice', id: 10},
                        {name: 'Bob', id: 20},
                        {name: 'Charlie', id: 60},
                        {name: 'David', id: 80}
                    ];

                    indexEntries.forEach((entry, i) => {
                        const y = 100 + i * 30;
                        if (step > i + 1) {
                            animation.ctx.fillStyle = '#e6f7ff';
                            animation.ctx.fillRect(50, y, 150, 25);
                            animation.ctx.strokeStyle = '#764ba2';
                            animation.ctx.strokeRect(50, y, 150, 25);
                            animation.ctx.fillStyle = '#333';
                            animation.ctx.fillText(`${entry.name} → ID:${entry.id}`, 60, y + 17);
                        }
                    });
                }

                // 数据表
                if (step > 5) {
                    animation.ctx.fillStyle = '#667eea';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText('数据表 (按ID排序)', 400, 80);

                    const tableData = [
                        {id: 10, name: 'Alice', age: 25},
                        {id: 20, name: 'Bob', age: 30},
                        {id: 60, name: 'Charlie', age: 35},
                        {id: 80, name: 'David', age: 40}
                    ];

                    tableData.forEach((data, i) => {
                        const y = 100 + i * 30;
                        if (step > 5 + i) {
                            animation.ctx.fillStyle = '#f6ffed';
                            animation.ctx.fillRect(400, y, 200, 25);
                            animation.ctx.strokeStyle = '#52c41a';
                            animation.ctx.strokeRect(400, y, 200, 25);
                            animation.ctx.fillStyle = '#333';
                            animation.ctx.fillText(`${data.id} | ${data.name} | ${data.age}`, 410, y + 17);
                        }
                    });
                }

                // 回表查询演示
                if (step > 10) {
                    animation.ctx.fillStyle = '#ff4d4f';
                    animation.ctx.font = '12px Arial';
                    animation.ctx.fillText('查询 name="Charlie"', 50, 250);

                    if (step > 11) {
                        // 高亮索引中的Charlie
                        animation.ctx.fillStyle = 'rgba(255, 77, 79, 0.3)';
                        animation.ctx.fillRect(50, 190, 150, 25);

                        if (step > 12) {
                            // 绘制箭头到数据表
                            animation.drawArrow(200, 202, 400, 190, '#ff4d4f');
                            animation.ctx.fillStyle = '#ff4d4f';
                            animation.ctx.fillText('回表查询', 250, 185);

                            if (step > 13) {
                                // 高亮数据表中的记录
                                animation.ctx.fillStyle = 'rgba(255, 77, 79, 0.3)';
                                animation.ctx.fillRect(400, 190, 200, 25);
                                animation.addParticle(500, 202, '#ff4d4f');
                            }
                        }
                    }
                }

                animation.updateParticles();

                step++;
                if (step < 16) {
                    setTimeout(() => requestAnimationFrame(animate), 600);
                } else {
                    updateScore(15);
                    showTooltip('非聚集索引演示完成！+15分', 400, 300);
                }
            }

            animate();
        }

        // 对比演示
        function compareIndexTypes() {
            const animation = new CanvasAnimation('storageCanvas');
            let step = 0;

            function animate() {
                animation.clear();

                animation.ctx.fillStyle = '#333';
                animation.ctx.font = '18px Arial';
                animation.ctx.fillText('聚集索引 vs 非聚集索引对比', 250, 30);

                // 左侧：聚集索引
                animation.ctx.fillStyle = '#667eea';
                animation.ctx.font = '16px Arial';
                animation.ctx.fillText('聚集索引', 150, 70);

                if (step > 0) {
                    animation.ctx.fillStyle = '#52c41a';
                    animation.ctx.fillRect(50, 90, 250, 120);
                    animation.ctx.fillStyle = '#fff';
                    animation.ctx.font = '12px Arial';
                    animation.ctx.fillText('• 数据与索引存储在一起', 60, 110);
                    animation.ctx.fillText('• 叶子节点包含完整数据', 60, 130);
                    animation.ctx.fillText('• 查询速度快，无需回表', 60, 150);
                    animation.ctx.fillText('• 每表只能有一个', 60, 170);
                    animation.ctx.fillText('• 插入可能导致页分裂', 60, 190);
                }

                // 右侧：非聚集索引
                animation.ctx.fillStyle = '#764ba2';
                animation.ctx.font = '16px Arial';
                animation.ctx.fillText('非聚集索引', 550, 70);

                if (step > 1) {
                    animation.ctx.fillStyle = '#ff7875';
                    animation.ctx.fillRect(450, 90, 250, 120);
                    animation.ctx.fillStyle = '#fff';
                    animation.ctx.font = '12px Arial';
                    animation.ctx.fillText('• 索引与数据分离存储', 460, 110);
                    animation.ctx.fillText('• 叶子节点存储主键引用', 460, 130);
                    animation.ctx.fillText('• 需要回表查询完整数据', 460, 150);
                    animation.ctx.fillText('• 每表可以有多个', 460, 170);
                    animation.ctx.fillText('• 维护成本相对较低', 460, 190);
                }

                // 性能对比图表
                if (step > 2) {
                    animation.ctx.fillStyle = '#333';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText('查询性能对比', 320, 250);

                    // 绘制柱状图
                    const barWidth = 60;
                    const maxHeight = 80;

                    // 聚集索引性能
                    animation.ctx.fillStyle = '#52c41a';
                    animation.ctx.fillRect(250, 350 - 70, barWidth, 70);
                    animation.ctx.fillStyle = '#333';
                    animation.ctx.fillText('聚集索引', 240, 370);
                    animation.ctx.fillText('查询速度', 245, 385);

                    // 非聚集索引性能
                    animation.ctx.fillStyle = '#ff7875';
                    animation.ctx.fillRect(400, 350 - 50, barWidth, 50);
                    animation.ctx.fillStyle = '#333';
                    animation.ctx.fillText('非聚集索引', 385, 370);
                    animation.ctx.fillText('查询速度', 395, 385);
                }

                step++;
                if (step < 5) {
                    setTimeout(() => requestAnimationFrame(animate), 1500);
                } else {
                    updateScore(20);
                    showTooltip('索引对比演示完成！+20分', 400, 200);
                }
            }

            animate();
        }

        // 主键索引动画
        function animatePrimaryIndex() {
            const animation = new CanvasAnimation('logicalCanvas');
            let step = 0;

            function animate() {
                animation.clear();

                animation.ctx.fillStyle = '#333';
                animation.ctx.font = '18px Arial';
                animation.ctx.fillText('主键索引 - 唯一且非空', 280, 30);

                // 绘制表结构
                if (step > 0) {
                    animation.ctx.fillStyle = '#667eea';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText('用户表', 50, 80);

                    // 表头
                    const headers = ['ID (主键)', 'Name', 'Email'];
                    headers.forEach((header, i) => {
                        animation.ctx.fillStyle = '#1890ff';
                        animation.ctx.fillRect(50 + i * 120, 100, 120, 30);
                        animation.ctx.fillStyle = '#fff';
                        animation.ctx.fillText(header, 60 + i * 120, 120);
                    });

                    // 数据行
                    const data = [
                        [1, 'Alice', '<EMAIL>'],
                        [2, 'Bob', '<EMAIL>'],
                        [3, 'Charlie', '<EMAIL>']
                    ];

                    data.forEach((row, i) => {
                        if (step > i + 1) {
                            row.forEach((cell, j) => {
                                animation.ctx.fillStyle = j === 0 ? '#fff2e8' : '#f6ffed';
                                animation.ctx.fillRect(50 + j * 120, 130 + i * 30, 120, 30);
                                animation.ctx.strokeStyle = '#d9d9d9';
                                animation.ctx.strokeRect(50 + j * 120, 130 + i * 30, 120, 30);
                                animation.ctx.fillStyle = '#333';
                                animation.ctx.fillText(cell.toString(), 60 + j * 120, 150 + i * 30);

                                if (j === 0) { // 主键列高亮
                                    animation.addParticle(110 + j * 120, 145 + i * 30, '#ff4d4f');
                                }
                            });
                        }
                    });
                }

                // 特性说明
                if (step > 4) {
                    animation.ctx.fillStyle = '#ff4d4f';
                    animation.ctx.font = '16px Arial';
                    animation.ctx.fillText('主键索引特性:', 450, 120);

                    const features = [
                        '✓ 唯一性：不允许重复值',
                        '✓ 非空性：不允许NULL值',
                        '✓ 自动创建：定义主键时自动创建',
                        '✓ 聚集索引：通常是聚集索引',
                        '✓ 唯一标识：每行记录的唯一标识'
                    ];

                    features.forEach((feature, i) => {
                        if (step > 5 + i) {
                            animation.ctx.fillStyle = '#52c41a';
                            animation.ctx.font = '14px Arial';
                            animation.ctx.fillText(feature, 450, 150 + i * 25);
                        }
                    });
                }

                animation.updateParticles();

                step++;
                if (step < 12) {
                    setTimeout(() => requestAnimationFrame(animate), 800);
                } else {
                    updateScore(10);
                    showTooltip('主键索引演示完成！+10分', 400, 250);
                }
            }

            animate();
        }

        // 联合索引动画
        function animateCompositeIndex() {
            const animation = new CanvasAnimation('logicalCanvas');
            let step = 0;

            function animate() {
                animation.clear();

                animation.ctx.fillStyle = '#333';
                animation.ctx.font = '18px Arial';
                animation.ctx.fillText('联合索引 - 最左前缀原则', 250, 30);

                if (step > 0) {
                    // 创建联合索引
                    animation.ctx.fillStyle = '#667eea';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText('CREATE INDEX idx_name_age ON users(name, age)', 50, 70);

                    // 索引结构
                    animation.ctx.fillText('联合索引结构 (name, age):', 50, 110);

                    const indexData = [
                        {name: 'Alice', age: 25, id: 1},
                        {name: 'Alice', age: 30, id: 4},
                        {name: 'Bob', age: 20, id: 2},
                        {name: 'Charlie', age: 35, id: 3}
                    ];

                    indexData.forEach((item, i) => {
                        if (step > i + 1) {
                            const y = 130 + i * 30;
                            animation.ctx.fillStyle = '#e6f7ff';
                            animation.ctx.fillRect(50, y, 300, 25);
                            animation.ctx.strokeStyle = '#1890ff';
                            animation.ctx.strokeRect(50, y, 300, 25);
                            animation.ctx.fillStyle = '#333';
                            animation.ctx.fillText(`${item.name} | ${item.age} → ID:${item.id}`, 60, y + 17);
                            animation.addParticle(200, y + 12, '#1890ff');
                        }
                    });
                }

                // 查询示例
                if (step > 5) {
                    animation.ctx.fillStyle = '#52c41a';
                    animation.ctx.font = '16px Arial';
                    animation.ctx.fillText('查询示例:', 400, 110);

                    const queries = [
                        {sql: "WHERE name='Alice'", canUse: true, desc: '✓ 可以使用索引'},
                        {sql: "WHERE name='Alice' AND age=25", canUse: true, desc: '✓ 可以使用索引'},
                        {sql: "WHERE age=25", canUse: false, desc: '✗ 不能使用索引'},
                        {sql: "WHERE name='Alice' AND email='...'", canUse: true, desc: '✓ 部分使用索引'}
                    ];

                    queries.forEach((query, i) => {
                        if (step > 6 + i) {
                            const y = 140 + i * 40;
                            animation.ctx.fillStyle = query.canUse ? '#f6ffed' : '#fff2f0';
                            animation.ctx.fillRect(400, y, 350, 35);
                            animation.ctx.strokeStyle = query.canUse ? '#52c41a' : '#ff4d4f';
                            animation.ctx.strokeRect(400, y, 350, 35);

                            animation.ctx.fillStyle = '#333';
                            animation.ctx.font = '12px Arial';
                            animation.ctx.fillText(query.sql, 410, y + 15);
                            animation.ctx.fillStyle = query.canUse ? '#52c41a' : '#ff4d4f';
                            animation.ctx.fillText(query.desc, 410, y + 30);
                        }
                    });
                }

                // 最左前缀原则说明
                if (step > 10) {
                    animation.ctx.fillStyle = '#ff4d4f';
                    animation.ctx.font = '16px Arial';
                    animation.ctx.fillText('最左前缀原则:', 50, 320);
                    animation.ctx.fillStyle = '#333';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText('查询条件必须从索引的最左列开始，且不能跳过中间列', 50, 350);
                }

                animation.updateParticles();

                step++;
                if (step < 13) {
                    setTimeout(() => requestAnimationFrame(animate), 800);
                } else {
                    updateScore(15);
                    showTooltip('联合索引演示完成！+15分', 400, 250);
                }
            }

            animate();
        }

        // 游戏功能
        function startQuizGame() {
            const animation = new CanvasAnimation('gameCanvas');
            gameState.gameMode = 'quiz';
            gameState.currentQuestion = 0;
            gameState.correctAnswers = 0;

            const questions = [
                {
                    question: "B+树索引的时间复杂度是多少？",
                    options: ["O(1)", "O(logn)", "O(n)", "O(n²)"],
                    correct: 1,
                    explanation: "B+树是平衡多路搜索树，查询复杂度为O(logn)"
                },
                {
                    question: "哪种索引适合等值查询？",
                    options: ["B+树索引", "哈希索引", "全文索引", "R-Tree索引"],
                    correct: 1,
                    explanation: "哈希索引通过哈希函数直接定位，适合等值查询"
                },
                {
                    question: "聚集索引的叶子节点存储什么？",
                    options: ["索引键", "主键", "完整数据行", "指针"],
                    correct: 2,
                    explanation: "聚集索引的叶子节点直接存储完整的数据行"
                }
            ];

            function showQuestion() {
                animation.clear();

                const q = questions[gameState.currentQuestion];

                // 显示问题
                animation.ctx.fillStyle = '#333';
                animation.ctx.font = '18px Arial';
                animation.ctx.fillText(`问题 ${gameState.currentQuestion + 1}/3:`, 50, 50);
                animation.ctx.font = '16px Arial';
                animation.ctx.fillText(q.question, 50, 80);

                // 显示选项
                q.options.forEach((option, i) => {
                    const y = 120 + i * 40;
                    animation.ctx.fillStyle = '#f0f0f0';
                    animation.ctx.fillRect(50, y, 700, 35);
                    animation.ctx.strokeStyle = '#d9d9d9';
                    animation.ctx.strokeRect(50, y, 700, 35);
                    animation.ctx.fillStyle = '#333';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText(`${String.fromCharCode(65 + i)}. ${option}`, 60, y + 22);
                });

                // 添加点击事件
                animation.canvas.onclick = function(e) {
                    const rect = animation.canvas.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const optionIndex = Math.floor((y - 120) / 40);
                    if (optionIndex >= 0 && optionIndex < 4 && x >= 50 && x <= 750) {
                        checkAnswer(optionIndex, q);
                    }
                };
            }

            function checkAnswer(selected, question) {
                animation.canvas.onclick = null;

                // 显示答案
                question.options.forEach((option, i) => {
                    const y = 120 + i * 40;
                    if (i === question.correct) {
                        animation.ctx.fillStyle = '#f6ffed';
                        animation.ctx.fillRect(50, y, 700, 35);
                        animation.ctx.strokeStyle = '#52c41a';
                        animation.ctx.strokeRect(50, y, 700, 35);
                    } else if (i === selected && selected !== question.correct) {
                        animation.ctx.fillStyle = '#fff2f0';
                        animation.ctx.fillRect(50, y, 700, 35);
                        animation.ctx.strokeStyle = '#ff4d4f';
                        animation.ctx.strokeRect(50, y, 700, 35);
                    }
                    animation.ctx.fillStyle = '#333';
                    animation.ctx.font = '14px Arial';
                    animation.ctx.fillText(`${String.fromCharCode(65 + i)}. ${option}`, 60, y + 22);
                });

                // 显示解释
                animation.ctx.fillStyle = '#1890ff';
                animation.ctx.font = '14px Arial';
                animation.ctx.fillText('解释: ' + question.explanation, 50, 300);

                if (selected === question.correct) {
                    gameState.correctAnswers++;
                    updateScore(10);
                    showTooltip('回答正确！+10分', 400, 350);
                } else {
                    showTooltip('回答错误，继续加油！', 400, 350);
                }

                setTimeout(() => {
                    gameState.currentQuestion++;
                    if (gameState.currentQuestion < questions.length) {
                        showQuestion();
                    } else {
                        showGameResult();
                    }
                }, 2000);
            }

            function showGameResult() {
                animation.clear();
                animation.ctx.fillStyle = '#333';
                animation.ctx.font = '24px Arial';
                animation.ctx.fillText('游戏结束！', 320, 150);
                animation.ctx.font = '18px Arial';
                animation.ctx.fillText(`正确答案: ${gameState.correctAnswers}/3`, 300, 200);

                const percentage = (gameState.correctAnswers / 3) * 100;
                let message = '';
                if (percentage >= 80) {
                    message = '优秀！你已经掌握了索引知识！';
                    animation.ctx.fillStyle = '#52c41a';
                } else if (percentage >= 60) {
                    message = '良好！继续学习会更好！';
                    animation.ctx.fillStyle = '#1890ff';
                } else {
                    message = '需要加强学习哦！';
                    animation.ctx.fillStyle = '#ff4d4f';
                }

                animation.ctx.fillText(message, 250, 250);
                updateScore(gameState.correctAnswers * 5);
            }

            showQuestion();
        }

        // 其他动画函数的简化实现
        function animateNormalIndex() {
            const animation = new CanvasAnimation('logicalCanvas');
            animation.clear();
            animation.ctx.fillStyle = '#333';
            animation.ctx.font = '18px Arial';
            animation.ctx.fillText('普通索引 - 允许重复值和空值', 250, 200);
            updateScore(10);
            showTooltip('普通索引演示完成！+10分', 400, 250);
        }

        function animateUniqueIndex() {
            const animation = new CanvasAnimation('logicalCanvas');
            animation.clear();
            animation.ctx.fillStyle = '#333';
            animation.ctx.font = '18px Arial';
            animation.ctx.fillText('唯一索引 - 保证数据唯一性', 250, 200);
            updateScore(10);
            showTooltip('唯一索引演示完成！+10分', 400, 250);
        }

        function animateSpatialIndex() {
            const animation = new CanvasAnimation('logicalCanvas');
            animation.clear();
            animation.ctx.fillStyle = '#333';
            animation.ctx.font = '18px Arial';
            animation.ctx.fillText('空间索引 - 用于地理信息系统', 250, 200);
            updateScore(10);
            showTooltip('空间索引演示完成！+10分', 400, 250);
        }

        function startIndexMatchGame() {
            const animation = new CanvasAnimation('gameCanvas');
            animation.clear();
            animation.ctx.fillStyle = '#333';
            animation.ctx.font = '18px Arial';
            animation.ctx.fillText('索引匹配游戏开发中...', 300, 200);
            updateScore(5);
        }

        function startPerformanceGame() {
            const animation = new CanvasAnimation('gameCanvas');
            animation.clear();
            animation.ctx.fillStyle = '#333';
            animation.ctx.font = '18px Arial';
            animation.ctx.fillText('性能优化挑战开发中...', 280, 200);
            updateScore(5);
        }

        // 详情展示函数
        function showBPlusTreeDetail() {
            alert('B+树索引详情：\n\n• 所有数据存储在叶子节点\n• 内部节点只存储键值\n• 叶子节点之间有指针连接\n• 适合范围查询和排序\n• 时间复杂度O(logn)');
            updateScore(5);
        }

        function showHashIndexDetail() {
            alert('哈希索引详情：\n\n• 基于哈希表实现\n• 等值查询速度极快O(1)\n• 不支持范围查询\n• 不支持排序\n• 存在哈希冲突问题');
            updateScore(5);
        }

        function showFullTextDetail() {
            alert('全文索引详情：\n\n• 用于文本搜索\n• 支持自然语言搜索\n• 基于倒排索引实现\n• 支持相关性排序\n• 适用于CHAR、VARCHAR、TEXT类型');
            updateScore(5);
        }

        function showRTreeDetail() {
            alert('R-Tree索引详情：\n\n• 专门用于空间数据\n• 支持多维数据索引\n• 使用最小边界矩形(MBR)\n• 适用于GIS应用\n• 支持空间查询操作');
            updateScore(5);
        }

        function showClusteredDetail() {
            alert('聚集索引详情：\n\n• 数据行按索引键排序存储\n• 叶子节点包含完整数据行\n• 每个表只能有一个聚集索引\n• 通常是主键索引\n• 查询速度快，无需回表');
            updateScore(5);
        }

        function showNonClusteredDetail() {
            alert('非聚集索引详情：\n\n• 索引与数据分离存储\n• 叶子节点存储主键引用\n• 需要回表查询完整数据\n• 每个表可以有多个\n• 维护成本相对较低');
            updateScore(5);
        }

        function showPrimaryDetail() {
            alert('主键索引详情：\n\n• 唯一标识表中每一行\n• 不允许NULL值\n• 不允许重复值\n• 自动创建索引\n• 通常是聚集索引');
            updateScore(5);
        }

        function showNormalDetail() {
            alert('普通索引详情：\n\n• 最基本的索引类型\n• 允许重复值\n• 允许NULL值\n• 用于加速查询\n• 可以创建多个');
            updateScore(5);
        }

        function showCompositeDetail() {
            alert('联合索引详情：\n\n• 多个列组成的索引\n• 遵循最左前缀原则\n• 可以减少索引数量\n• 提高复合查询效率\n• 列的顺序很重要');
            updateScore(5);
        }

        function showUniqueDetail() {
            alert('唯一索引详情：\n\n• 保证列值的唯一性\n• 允许一个NULL值\n• 自动创建约束\n• 可以有多个唯一索引\n• 提高数据完整性');
            updateScore(5);
        }

        function showSpatialDetail() {
            alert('空间索引详情：\n\n• MySQL 5.7+支持\n• 用于地理空间数据\n• 遵循OpenGIS标准\n• 支持空间函数\n• 适用于地图应用');
            updateScore(5);
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 添加一些初始动画效果
            setTimeout(() => {
                updateScore(0); // 初始化进度条
            }, 1000);

            // 添加鼠标悬停效果
            document.querySelectorAll('.index-card').forEach(card => {
                card.addEventListener('mouseenter', function(e) {
                    this.classList.add('pulse');
                });

                card.addEventListener('mouseleave', function(e) {
                    this.classList.remove('pulse');
                });
            });
        });
    </script>
</body>
</html>
