<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            line-height: 1.6;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .pattern-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .pattern-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .pattern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .pattern-card:hover::before {
            left: 100%;
        }

        .pattern-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .pattern-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            animation: pulse 2s infinite;
        }

        .creative { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
        .structural { background: linear-gradient(135deg, #4834d4, #686de0); }
        .behavioral { background: linear-gradient(135deg, #00d2d3, #54a0ff); }

        .pattern-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }

        .pattern-desc {
            color: #666;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .interactive-demo {
            margin-top: 40px;
            text-align: center;
        }

        .demo-canvas {
            border: 2px solid #ddd;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px auto;
            display: block;
        }

        .control-panel {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #00d2d3, #54a0ff);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.3rem;
            color: #2d3436;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .quiz-option {
            padding: 15px 25px;
            background: white;
            border: 2px solid transparent;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: scale(1.05);
        }

        .quiz-option.correct {
            background: #00b894;
            color: white;
            animation: correctAnswer 0.5s ease;
        }

        .quiz-option.wrong {
            background: #e17055;
            color: white;
            animation: wrongAnswer 0.5s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideUp {
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        @keyframes wrongAnswer {
            0% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
            100% { transform: translateX(0); }
        }

        .explanation {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            text-align: left;
            line-height: 1.8;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: slideUp 0.5s ease;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎨 设计模式互动学习</h1>
            <p class="subtitle">
                设计模式基于面向对象技术，是人们在长期的开发实践中良好经验的结晶，<br>
                提供了一个简单、统一的描述方法，使得人们可以复用这些软件设计方法、过程管理经验。
            </p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 设计模式三大分类</h2>
            <p style="text-align: center; font-size: 1.1rem; color: #666; margin-bottom: 30px;">
                按照设计模式的目的进行划分，现有的设计模式可以分为以下三种类型：
            </p>
            
            <div class="pattern-grid">
                <div class="pattern-card" onclick="showPatternDemo('creative')">
                    <div class="pattern-icon creative">🏭</div>
                    <div class="pattern-name">创建型模式</div>
                    <div class="pattern-desc">
                        负责对象的创建过程，将对象的创建和使用分离，提高系统的灵活性和可维护性。
                        包括：Abstract Factory、Builder、Factory Method、Prototype、Singleton等。
                    </div>
                </div>

                <div class="pattern-card" onclick="showPatternDemo('structural')">
                    <div class="pattern-icon structural">🏗️</div>
                    <div class="pattern-name">结构型模式</div>
                    <div class="pattern-desc">
                        处理类和对象的组合，通过继承和组合来组织接口和实现。
                        包括：Adapter、Bridge、Composite、Decorator、Facade、Flyweight、Proxy等。
                    </div>
                </div>

                <div class="pattern-card" onclick="showPatternDemo('behavioral')">
                    <div class="pattern-icon behavioral">⚡</div>
                    <div class="pattern-name">行为型模式</div>
                    <div class="pattern-desc">
                        关注对象之间的通信和职责分配，描述对象和类的交互以及职责分配。
                        包括：Chain of Responsibility、Command、Iterator、Observer、Strategy等。
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🌉 Bridge模式详解</h2>
            <p style="text-align: center; font-size: 1.1rem; color: #666; margin-bottom: 30px;">
                Bridge模式可以将一个复杂的组件分成<span class="highlight">功能性抽象</span>和<span class="highlight">内部实现</span>两个独立的但又相关的继承层次结构
            </p>
            
            <div class="interactive-demo">
                <canvas id="bridgeCanvas" class="demo-canvas" width="800" height="400"></canvas>
                <div class="control-panel">
                    <button class="btn btn-primary" onclick="animateBridge()">🎬 演示Bridge模式</button>
                    <button class="btn btn-secondary" onclick="showSeparation()">🔄 接口与实现分离</button>
                    <button class="btn btn-success" onclick="resetDemo()">🔄 重置演示</button>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 style="margin-bottom: 30px; color: #2d3436;">🎯 知识测验</h2>
            <div class="quiz-question">
                按照设计模式的目的进行划分，现有的设计模式可以分为创建型、（ ）和行为型三种类型。
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 合成型</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">B. 组合型</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">C. 结构型</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 聚合型</div>
            </div>
            
            <div class="explanation" id="explanation">
                <h3>📖 详细解析</h3>
                <p>
                    <strong>正确答案：C. 结构型</strong><br><br>
                    设计模式基于面向对象技术，按照目的可以分为三大类：<br><br>
                    
                    <span class="highlight">1. 创建型模式</span>：主要包括Abstract Factory、Builder、Factory Method、Prototype、Singleton等，
                    负责对象的创建过程。<br><br>
                    
                    <span class="highlight">2. 结构型模式</span>：主要包括Adapter、Bridge、Composite、Decorator、Facade、Flyweight和Proxy，
                    处理类和对象的组合。<br><br>
                    
                    <span class="highlight">3. 行为型模式</span>：主要包括Chain of Responsibility、Command、Interpreter、Iterator、Mediator、
                    Memento、Observer、State、Strategy、Template Method、Visitor等，关注对象之间的通信。<br><br>
                    
                    <strong>Bridge模式</strong>可以将复杂组件分成功能性抽象和内部实现两个独立但相关的继承层次结构，
                    实现接口与实现的分离，提高可扩展性，并对客户端隐藏实现细节。
                </p>
            </div>
        </div>
    </div>

    <script>
        // Canvas动画相关变量
        const canvas = document.getElementById('bridgeCanvas');
        const ctx = canvas.getContext('2d');
        let animationFrame;
        let currentStep = 0;

        // 初始化Canvas
        function initCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('Bridge模式：接口与实现分离', canvas.width/2, 40);
        }

        // 绘制Bridge模式演示
        function drawBridgePattern(step) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 标题
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('Bridge模式演示', canvas.width/2, 30);
            
            if (step >= 1) {
                // 抽象层
                ctx.fillStyle = '#667eea';
                ctx.fillRect(150, 80, 200, 80);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('抽象层', 250, 125);
                ctx.font = '12px Arial';
                ctx.fillText('(Abstraction)', 250, 145);
            }
            
            if (step >= 2) {
                // 实现层
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(450, 80, 200, 80);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('实现层', 550, 125);
                ctx.font = '12px Arial';
                ctx.fillText('(Implementation)', 550, 145);
            }
            
            if (step >= 3) {
                // 连接桥梁
                ctx.strokeStyle = '#00d2d3';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(350, 120);
                ctx.lineTo(450, 120);
                ctx.stroke();
                
                // 桥梁标签
                ctx.fillStyle = '#00d2d3';
                ctx.font = 'bold 14px Arial';
                ctx.fillText('Bridge', 400, 110);
            }
            
            if (step >= 4) {
                // 具体抽象
                ctx.fillStyle = '#a8a8ff';
                ctx.fillRect(100, 200, 120, 60);
                ctx.fillRect(280, 200, 120, 60);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText('具体抽象A', 160, 235);
                ctx.fillText('具体抽象B', 340, 235);
            }
            
            if (step >= 5) {
                // 具体实现
                ctx.fillStyle = '#ffaaaa';
                ctx.fillRect(500, 200, 120, 60);
                ctx.fillRect(630, 200, 120, 60);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText('具体实现1', 560, 235);
                ctx.fillText('具体实现2', 690, 235);
            }
            
            if (step >= 6) {
                // 连接线
                ctx.strokeStyle = '#ccc';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                
                // 抽象到具体抽象
                ctx.beginPath();
                ctx.moveTo(200, 160);
                ctx.lineTo(160, 200);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(300, 160);
                ctx.lineTo(340, 200);
                ctx.stroke();
                
                // 实现到具体实现
                ctx.beginPath();
                ctx.moveTo(520, 160);
                ctx.lineTo(560, 200);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(580, 160);
                ctx.lineTo(690, 200);
                ctx.stroke();
                
                ctx.setLineDash([]);
            }
            
            if (step >= 7) {
                // 优势说明
                ctx.fillStyle = '#00d2d3';
                ctx.font = 'bold 14px Arial';
                ctx.fillText('✓ 接口与实现分离', 400, 320);
                ctx.fillText('✓ 提高可扩展性', 400, 340);
                ctx.fillText('✓ 隐藏实现细节', 400, 360);
            }
        }

        // 动画演示Bridge模式
        function animateBridge() {
            currentStep = 0;
            const animate = () => {
                drawBridgePattern(currentStep);
                currentStep++;
                if (currentStep <= 7) {
                    setTimeout(() => requestAnimationFrame(animate), 800);
                }
            };
            animate();
        }

        // 显示分离效果
        function showSeparation() {
            ctx.clearRect(0, 0, canvas.width, canvas.width);
            
            // 标题
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('接口与实现分离的好处', canvas.width/2, 30);
            
            // 分离前
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(100, 80, 250, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('紧耦合', 225, 125);
            ctx.font = '12px Arial';
            ctx.fillText('接口和实现混合', 225, 145);
            ctx.fillText('难以扩展和维护', 225, 160);
            
            // 箭头
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('→', 380, 130);
            
            // 分离后
            ctx.fillStyle = '#00d2d3';
            ctx.fillRect(450, 60, 100, 60);
            ctx.fillRect(450, 140, 100, 60);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('接口', 500, 95);
            ctx.fillText('实现', 500, 175);
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('独立变化', 600, 90);
            ctx.fillText('易于扩展', 600, 105);
            ctx.fillText('松耦合', 600, 170);
            ctx.fillText('可维护', 600, 185);
        }

        // 重置演示
        function resetDemo() {
            currentStep = 0;
            initCanvas();
        }

        // 显示模式演示
        function showPatternDemo(type) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            
            switch(type) {
                case 'creative':
                    ctx.fillText('创建型模式示例', canvas.width/2, 40);
                    
                    // 工厂模式演示
                    ctx.fillStyle = '#ff6b6b';
                    ctx.fillRect(200, 100, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.fillText('工厂', 250, 135);
                    
                    // 产品
                    ctx.fillStyle = '#ffeaa7';
                    ctx.fillRect(150, 200, 60, 40);
                    ctx.fillRect(250, 200, 60, 40);
                    ctx.fillRect(350, 200, 60, 40);
                    
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Arial';
                    ctx.fillText('产品A', 180, 225);
                    ctx.fillText('产品B', 280, 225);
                    ctx.fillText('产品C', 380, 225);
                    break;
                    
                case 'structural':
                    ctx.fillText('结构型模式示例', canvas.width/2, 40);
                    
                    // 适配器模式
                    ctx.fillStyle = '#4834d4';
                    ctx.fillRect(150, 120, 80, 60);
                    ctx.fillRect(350, 120, 80, 60);
                    ctx.fillRect(550, 120, 80, 60);
                    
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText('客户端', 190, 155);
                    ctx.fillText('适配器', 390, 155);
                    ctx.fillText('被适配者', 590, 155);
                    
                    // 连接线
                    ctx.strokeStyle = '#686de0';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(230, 150);
                    ctx.lineTo(350, 150);
                    ctx.moveTo(430, 150);
                    ctx.lineTo(550, 150);
                    ctx.stroke();
                    break;
                    
                case 'behavioral':
                    ctx.fillText('行为型模式示例', canvas.width/2, 40);
                    
                    // 观察者模式
                    ctx.fillStyle = '#00d2d3';
                    ctx.fillRect(350, 80, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.fillText('主题', 400, 115);
                    
                    // 观察者
                    ctx.fillStyle = '#54a0ff';
                    ctx.fillRect(200, 200, 80, 50);
                    ctx.fillRect(360, 200, 80, 50);
                    ctx.fillRect(520, 200, 80, 50);
                    
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText('观察者1', 240, 230);
                    ctx.fillText('观察者2', 400, 230);
                    ctx.fillText('观察者3', 560, 230);
                    
                    // 通知线
                    ctx.strokeStyle = '#00d2d3';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([3, 3]);
                    ctx.beginPath();
                    ctx.moveTo(380, 140);
                    ctx.lineTo(240, 200);
                    ctx.moveTo(400, 140);
                    ctx.lineTo(400, 200);
                    ctx.moveTo(420, 140);
                    ctx.lineTo(560, 200);
                    ctx.stroke();
                    ctx.setLineDash([]);
                    break;
            }
        }

        // 测验答案选择
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('C. 结构型')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').classList.add('show');
            }, 1000);
        }

        // 初始化
        initCanvas();
        
        // 添加一些交互效果
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 创建点击效果
            ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
            ctx.beginPath();
            ctx.arc(x, y, 20, 0, 2 * Math.PI);
            ctx.fill();
            
            setTimeout(() => {
                if (currentStep === 0) initCanvas();
            }, 500);
        });
    </script>
</body>
</html>
