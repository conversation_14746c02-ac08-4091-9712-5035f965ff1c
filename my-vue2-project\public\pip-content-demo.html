<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>画中画内容展示演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        button {
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .primary {
            background: #409EFF !important;
            border-color: #409EFF !important;
        }
        
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
        }
        
        .info { background: rgba(64, 158, 255, 0.2); border: 1px solid rgba(64, 158, 255, 0.5); }
        .success { background: rgba(103, 194, 58, 0.2); border: 1px solid rgba(103, 194, 58, 0.5); }
        .warning { background: rgba(230, 162, 60, 0.2); border: 1px solid rgba(230, 162, 60, 0.5); }
        .error { background: rgba(245, 108, 108, 0.2); border: 1px solid rgba(245, 108, 108, 0.5); }
        
        .content-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        
        .main-content, .pip-preview {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 12px;
            padding: 20px;
            min-height: 400px;
        }
        
        .pip-preview {
            background: #2c3e50;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .pip-header {
            background: #409EFF;
            margin: -20px -20px 20px -20px;
            padding: 15px 20px;
            font-weight: bold;
        }
        
        .pip-content {
            line-height: 1.6;
        }
        
        .pip-footer {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            font-size: 12px;
            text-align: center;
        }
        
        .demo-text {
            line-height: 1.8;
        }
        
        .highlight {
            background: rgba(255, 235, 59, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            margin: 15px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #3498db;
            border-radius: 3px;
            transition: width 0.3s ease;
            animation: progress 3s ease-in-out infinite;
        }
        
        @keyframes progress {
            0%, 100% { width: 30%; }
            50% { width: 80%; }
        }
        
        .animated-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 20px auto;
            animation: colorChange 2s ease-in-out infinite;
        }
        
        @keyframes colorChange {
            0%, 100% { background: #3498db; }
            25% { background: #e74c3c; }
            50% { background: #f39c12; }
            75% { background: #27ae60; }
        }
        
        video {
            max-width: 100%;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 画中画内容展示演示</h1>
        
        <div class="demo-section">
            <h2>📺 画中画内容设计</h2>
            <p>这个演示展示了如何在画中画窗口中显示丰富的内容，即使无法直接捕获HTML页面。</p>
            
            <div class="controls">
                <button onclick="checkSupport()">🔍 检查支持</button>
                <button id="startBtn" onclick="startAdvancedPip()" class="primary" disabled>🚀 启动高级画中画</button>
                <button id="stopBtn" onclick="stopPip()" disabled>⏹️ 停止画中画</button>
            </div>
            
            <div id="status" class="status info">点击"检查支持"开始测试</div>
        </div>
        
        <div class="demo-section">
            <h2>🎯 内容对比</h2>
            <div class="content-preview">
                <div class="main-content">
                    <h3>📄 主窗口内容</h3>
                    <div class="demo-text">
                        <p>这是主窗口中显示的<span class="highlight">完整内容</span>。</p>
                        <p>包含了详细的文档信息、图片、表格等复杂元素。</p>
                        <p>用户可以在这里进行完整的阅读和交互。</p>
                        <ul>
                            <li>📚 完整的文档内容</li>
                            <li>🖼️ 图片和媒体元素</li>
                            <li>📊 表格和图表</li>
                            <li>🔗 链接和交互元素</li>
                        </ul>
                        <p>当用户开启画中画模式后，可以继续在其他窗口工作，同时通过画中画窗口监控阅读进度。</p>
                    </div>
                </div>
                
                <div class="pip-preview">
                    <div class="pip-header">
                        📚 知识库阅读器 | <span id="pipTime"></span>
                    </div>
                    <div class="pip-content">
                        <h4 id="pipFileName">当前文档：示例文档.html</h4>
                        <p><strong>进度：</strong> <span id="pipProgress">3 / 15</span></p>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <p><strong>状态：</strong> <span id="pipStatus">🔄 轮播中</span></p>
                        <p><strong>路径：</strong> /docs/example.html</p>
                        
                        <div style="margin: 20px 0;">
                            <h5>📄 内容摘要</h5>
                            <p style="font-size: 12px; line-height: 1.4;">
                                这是文档的主要内容预览。画中画窗口显示关键信息，
                                让用户在处理其他任务时也能了解当前阅读状态。
                            </p>
                        </div>
                        
                        <div class="animated-circle"></div>
                    </div>
                    <div class="pip-footer">
                        方向键切换 | 空格暂停 | 点击返回主窗口
                    </div>
                </div>
            </div>
        </div>
        
        <video id="pipVideo" style="display: none;"></video>
    </div>

    <script>
        let video = null;
        let canvas = null;
        let ctx = null;
        let updateInterval = null;
        let frameCount = 0;

        // 模拟数据
        const mockData = {
            currentFile: 3,
            totalFiles: 15,
            fileName: "Vue.js 组件开发指南.html",
            filePath: "/docs/frontend/vue-components.html",
            isCarouselActive: true
        };

        function updateTime() {
            document.getElementById('pipTime').textContent = new Date().toLocaleTimeString();
        }

        function updateMockData() {
            document.getElementById('pipFileName').textContent = `当前文档：${mockData.fileName}`;
            document.getElementById('pipProgress').textContent = `${mockData.currentFile} / ${mockData.totalFiles}`;
            document.getElementById('pipStatus').textContent = mockData.isCarouselActive ? '🔄 轮播中' : '⏸️ 已暂停';
        }

        function checkSupport() {
            const supported = 'pictureInPictureEnabled' in document && 
                            document.pictureInPictureEnabled &&
                            'requestPictureInPicture' in HTMLVideoElement.prototype;

            if (supported) {
                updateStatus('✅ 您的浏览器支持画中画功能！', 'success');
                document.getElementById('startBtn').disabled = false;
            } else {
                updateStatus('❌ 您的浏览器不支持画中画功能', 'error');
            }
        }

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        async function startAdvancedPip() {
            try {
                updateStatus('🚀 正在启动高级画中画...', 'warning');

                // 创建canvas
                canvas = document.createElement('canvas');
                ctx = canvas.getContext('2d');
                canvas.width = 800;
                canvas.height = 600;

                // 获取video元素
                video = document.getElementById('pipVideo');
                video.style.display = 'block';
                video.muted = true;
                video.autoplay = true;

                // 开始绘制内容
                drawAdvancedContent();

                // 创建视频流
                const stream = canvas.captureStream(30);
                video.srcObject = stream;

                // 等待video准备就绪
                await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('视频加载超时'));
                    }, 5000);

                    video.addEventListener('loadedmetadata', () => {
                        clearTimeout(timeout);
                        resolve();
                    }, { once: true });
                });

                // 启动画中画
                await video.requestPictureInPicture();

                // 更新UI状态
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;

                // 开始内容更新
                startAdvancedUpdate();

                // 监听退出事件
                video.addEventListener('leavepictureinpicture', stopPip, { once: true });

                updateStatus('🎉 高级画中画启动成功！', 'success');

            } catch (error) {
                console.error('画中画启动失败:', error);
                updateStatus('❌ 画中画启动失败: ' + error.message, 'error');
                cleanup();
            }
        }

        function stopPip() {
            try {
                if (document.pictureInPictureElement) {
                    document.exitPictureInPicture();
                }
            } catch (error) {
                console.error('退出画中画失败:', error);
            }

            cleanup();
            updateStatus('画中画已停止', 'info');
        }

        function cleanup() {
            if (updateInterval) {
                clearInterval(updateInterval);
                updateInterval = null;
            }

            if (video) {
                video.style.display = 'none';
                video.srcObject = null;
            }

            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
        }

        function startAdvancedUpdate() {
            updateInterval = setInterval(() => {
                drawAdvancedContent();
                frameCount++;
                
                // 模拟数据变化
                if (frameCount % 100 === 0) {
                    mockData.currentFile = (mockData.currentFile % mockData.totalFiles) + 1;
                    mockData.isCarouselActive = Math.random() > 0.3;
                }
            }, 100);
        }

        function drawAdvancedContent() {
            if (!ctx || !canvas) return;

            // 清空canvas
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制标题栏
            ctx.fillStyle = '#409EFF';
            ctx.fillRect(0, 0, canvas.width, 80);

            // 绘制标题和时间
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('📚 知识库阅读器', 20, 35);

            ctx.textAlign = 'right';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText(new Date().toLocaleTimeString(), canvas.width - 20, 35);

            // 绘制文件信息
            ctx.textAlign = 'left';
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText(mockData.fileName, 20, 65);

            // 绘制内容区域
            drawContentArea();

            // 绘制底部信息
            drawFooter();
        }

        function drawContentArea() {
            // 内容区域背景
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(20, 100, canvas.width - 40, canvas.height - 160);
            
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 2;
            ctx.strokeRect(20, 100, canvas.width - 40, canvas.height - 160);

            // 进度信息
            ctx.fillStyle = '#333333';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(`${mockData.currentFile} / ${mockData.totalFiles}`, canvas.width / 2, 150);

            // 状态信息
            ctx.font = '20px Microsoft YaHei';
            if (mockData.isCarouselActive) {
                ctx.fillStyle = '#67C23A';
                ctx.fillText('🔄 轮播中', canvas.width / 2, 180);
            } else {
                ctx.fillStyle = '#E6A23C';
                ctx.fillText('⏸️ 已暂停', canvas.width / 2, 180);
            }

            // 绘制进度条
            const progressWidth = (canvas.width - 80) * mockData.currentFile / mockData.totalFiles;
            ctx.fillStyle = '#ecf0f1';
            ctx.fillRect(40, 220, canvas.width - 80, 8);
            ctx.fillStyle = '#3498db';
            ctx.fillRect(40, 220, progressWidth, 8);

            // 文件路径
            ctx.fillStyle = '#666666';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(mockData.filePath, canvas.width / 2, 250);

            // 动画元素
            const centerX = canvas.width / 2;
            const centerY = 320;
            const radius = 25 + Math.sin(frameCount * 0.1) * 8;
            const hue = (frameCount * 2) % 360;

            ctx.fillStyle = `hsl(${hue}, 70%, 60%)`;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fill();

            // 内容预览文本
            ctx.fillStyle = '#555555';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'left';
            const contentLines = [
                '📄 文档内容摘要：',
                '• 当前章节：组件生命周期',
                '• 主要概念：mounted, updated, destroyed',
                '• 代码示例：3个',
                '• 预计阅读时间：5分钟'
            ];

            contentLines.forEach((line, index) => {
                ctx.fillText(line, 50, 380 + index * 25);
            });
        }

        function drawFooter() {
            // 底部控制栏
            ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
            ctx.fillRect(0, canvas.height - 60, canvas.width, 60);

            ctx.fillStyle = '#ffffff';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('方向键切换文档 | 空格键暂停/继续 | 点击窗口返回主页面', canvas.width / 2, canvas.height - 35);
            
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText(`帧数: ${frameCount} | 更新频率: 10fps`, canvas.width / 2, canvas.height - 15);
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            checkSupport();
            setInterval(updateTime, 1000);
            setInterval(updateMockData, 2000);
            updateTime();
            updateMockData();
        });
    </script>
</body>
</html>
