<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IDE中的数据共享 - 交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 900px;
            width: 100%;
            background: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            text-align: center;
            color: #1a73e8;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .explanation {
            background-color: #e8f0fe;
            border-left: 5px solid #1a73e8;
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 5px;
        }
        .explanation ul {
            padding-left: 20px;
            margin: 0;
        }
        .simulator {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .editor-pane, .output-pane {
            flex: 1;
            min-width: 300px;
        }
        h3 {
            color: #5f6368;
            margin-top: 0;
        }
        #code-editor {
            width: 100%;
            height: 250px;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 14px;
            resize: vertical;
            box-sizing: border-box;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        .controls button {
            flex-grow: 1;
            padding: 10px 15px;
            border: none;
            background-color: #1a73e8;
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #1558b8;
        }
        #output-display {
            width: 100%;
            height: 250px;
            background: #282c34;
            color: #abb2bf;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 14px;
            white-space: pre-wrap;
            overflow-y: auto;
            box-sizing: border-box;
        }
        .output-display .keyword { color: #c678dd; }
        .output-display .string { color: #98c379; }
        .output-display .number { color: #d19a66; }
        .output-display .comment { color: #5c6370; }
        .output-display .error { color: #e06c75; font-weight: bold; }
        .output-display .success { color: #98c379; }
        .output-display .info { color: #61afef;}

        /* --- 游戏化学习的样式 --- */
        .game-simulator {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 20px;
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        .game-controls, .game-world {
            flex: 1;
            min-width: 300px;
        }
        #command-input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border-radius: 5px;
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-family: 'Courier New', Courier, monospace;
        }
        #run-command-btn {
            width: 100%;
            padding: 10px 15px;
            border: none;
            background-color: #28a745; /* Green color for game */
            color: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }
        #run-command-btn:hover {
            background-color: #218838;
        }
        #game-status {
            margin-top: 15px;
            font-weight: bold;
            color: #333;
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        #grid-container {
            display: grid;
            grid-template-columns: repeat(5, 50px);
            grid-template-rows: repeat(5, 50px);
            border: 2px solid #5f6368;
            width: 252px; /* 5 * 50 + 2 */
            height: 252px; /* 5 * 50 + 2 */
            margin: 0 auto;
            background-color: #fff;
        }
        .grid-cell {
            width: 50px;
            height: 50px;
            border: 1px solid #eee;
            box-sizing: border-box;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .player {
            width: 40px;
            height: 40px;
            background-color: #1a73e8;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        .goal {
            font-size: 30px;
        }
        #game-canvas {
            border: 2px solid #5f6368;
            margin: 0 auto;
            display: block; /* Helps with centering */
            background-color: #fff;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>理解IDE中的"数据共享"</h1>
        <div class="explanation">
            <p><strong>简单来说：</strong> IDE里的所有工具（错误检查、代码补全、运行等）都在看和使用你正在编辑的<strong>同一份代码数据</strong>。</p>
            <ul>
                <li>你在编辑器里写代码。</li>
                <li>"错误检查"工具会立刻读取这份代码，告诉你哪里可能错了。</li>
                <li>"语法高亮"工具也读取它，给代码上色，方便阅读。</li>
            </ul>
            <p>这种信息实时互通就是<strong>数据共享</strong>，它让开发变得高效、流畅。</p>
        </div>

        <h2>迷你IDE模拟器</h2>
        <div class="simulator">
            <div class="editor-pane">
                <h3>1. 代码编辑器</h3>
                <textarea id="code-editor">// 这是一个计算圆面积的函数
function calculateArea(radius) {
    // 使用 let 定义变量
    let pi = 3.14;
    let area = pi * radius * radius;
    return area;
}

// 调用函数并打印结果
let result = calculateArea(10);</textarea>
                <div class="controls">
                    <button id="highlight-btn">语法高亮</button>
                    <button id="lint-btn">语法检查</button>
                    <button id="run-btn">运行代码</button>
                </div>
            </div>
            <div class="output-pane">
                <h3>2. 工具输出</h3>
                <div id="output-display">点击左侧的按钮，这里会显示来自不同"工具"的结果...</div>
            </div>
        </div>

        <h2 style="margin-top: 40px;">互动小游戏：共享状态驱动的游戏</h2>
        <div class="explanation">
            <p>这个小游戏演示了另一个层面的"数据共享"。游戏的所有部分——你的输入、角色的移动、屏幕上的显示——都依赖于一个共同的、实时更新的<strong>"游戏状态"数据</strong>。</p>
            <ul>
                <li><strong>共享的游戏状态 (Shared State):</strong> 一个JavaScript对象，记录了角色当前的位置 (如 <code>{ x: 2, y: 2 }</code>)。</li>
                <li><strong>控制台 (Console):</strong> 你在这里输入指令，比如 <code>move("right")</code>。这是修改状态的"意图"。</li>
                <li><strong>游戏引擎 (Game Engine):</strong> 读取你的指令，并<strong>修改</strong>共享的"游戏状态"。</li>
                <li><strong>渲染器 (Renderer):</strong> <strong>读取</strong>新的"游戏状态"，然后在下面的网格上重新绘制角色。</li>
            </ul>
            <p>当你执行一个命令时，引擎和渲染器都在操作同一份数据，实现了状态和视图的同步。这和IDE里多个工具看同一份代码是同样的道理。</p>
        </div>

        <div class="game-simulator">
            <div class="game-controls">
                <h3>1. 控制台</h3>
                <input type="text" id="command-input" placeholder="输入指令: move('up'), move('down'), ...">
                <button id="run-command-btn">执行指令</button>
                <div id="game-status">角色位置: (2, 2)</div>
            </div>
            <div class="game-world">
                <h3>2. 游戏世界 (视图)</h3>
                <canvas id="game-canvas" width="250" height="250"></canvas>
                <div id="grid-container" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        const editor = document.getElementById('code-editor');
        const output = document.getElementById('output-display');
        const highlightBtn = document.getElementById('highlight-btn');
        const lintBtn = document.getElementById('lint-btn');
        const runBtn = document.getElementById('run-btn');

        // 数据：从编辑器获取的代码。这是所有工具共享的核心数据！
        function getSharedData() {
            return editor.value;
        }

        // --- 工具1：语法高亮 ---
        highlightBtn.addEventListener('click', () => {
            const code = getSharedData();
            let html = code
                .replace(/\/\/.*/g, '<span class="comment">$&</span>') // 注释
                .replace(/\b(function|let|const|var|return)\b/g, '<span class="keyword">$1</span>') // 关键词
                .replace(/(\d+(\.\d+)?)/g, '<span class="number">$1</span>'); // 数字
            
            output.innerHTML = `<span class="info">[语法高亮工具]:</span><br>已将代码着色，方便阅读。 <br><br>${html}`;
        });

        // --- 工具2：语法检查 ---
        lintBtn.addEventListener('click', () => {
            const code = getSharedData();
            let message = '<span class="info">[语法检查工具]:</span><br>';
            if (!code.includes(';')) {
                message += '<span class="error">检查结果: 发现问题！代码行末尾缺少分号 (;) 是一种潜在风险。</span>';
            } else if (code.match(/function/g)?.length > 1) {
                message += '<span class="error">检查结果: 发现问题！为保持简单，此模拟器只允许一个函数。</span>';
            }
            else {
                message += '<span class="success">检查结果: 代码看起来很棒，没有发现明显问题！</span>';
            }
            output.innerHTML = message;
        });

        // --- 工具3：运行代码 (模拟) ---
        runBtn.addEventListener('click', () => {
            const code = getSharedData();
            let message = '<span class="info">[代码运行工具]:</span><br>';
            try {
                // 安全的模拟执行：我们不使用eval，而是解析特定模式
                const radiusMatch = /calculateArea\((\d+)\)/.exec(code);
                if (radiusMatch && radiusMatch[1]) {
                    const radius = parseInt(radiusMatch[1], 10);
                    const area = 3.14 * radius * radius;
                    message += `模拟执行成功！<br>调用 calculateArea(${radius}) 的结果是: <span class="number">${area}</span>`;
                } else {
                    message += '无法模拟执行。未找到 "calculateArea(数字)" 的调用格式。';
                }
            } catch(e) {
                message += `<span class="error">代码运行时出错: ${e.message}</span>`;
            }
            output.innerHTML = message;
        });

        // --- 互动小游戏逻辑 ---
        const commandInput = document.getElementById('command-input');
        const runCommandBtn = document.getElementById('run-command-btn');
        const gameStatus = document.getElementById('game-status');
        const gridContainer = document.getElementById('grid-container');
        const canvas = document.getElementById('game-canvas');
        const ctx = canvas.getContext('2d');

        const gridSize = 5;
        const cellSize = canvas.width / gridSize;

        // 这是"共享数据"：游戏状态对象。所有游戏相关的部分都依赖它。
        const gameState = {
            player: { x: 2, y: 2 },
            goal: { x: 4, y: 4 },
            commands: 0,
            isAnimating: false // 新增状态，防止动画期间重复执行命令
        };

        // "工具"1: 游戏渲染器 (只负责读取 gameState 来绘图)
        function renderGame() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw grid
            for (let i = 0; i <= gridSize; i++) {
                ctx.moveTo(i * cellSize, 0);
                ctx.lineTo(i * cellSize, canvas.height);
                ctx.moveTo(0, i * cellSize);
                ctx.lineTo(canvas.width, i * cellSize);
            }
            ctx.strokeStyle = '#eee';
            ctx.stroke();

            // Draw Goal
            ctx.font = '30px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('★', gameState.goal.x * cellSize + cellSize / 2, gameState.goal.y * cellSize + cellSize / 2);

            // Draw Player
            drawPlayer(gameState.player.x * cellSize + cellSize/2, gameState.player.y * cellSize + cellSize/2);
            
            // 同样，状态显示也依赖于共享的 gameState
            gameStatus.innerHTML = `角色位置: (${gameState.player.x}, ${gameState.player.y}) | 命令计数: ${gameState.commands}`;
        }
        
        function drawPlayer(px, py) {
            ctx.fillStyle = '#1a73e8';
            ctx.beginPath();
            ctx.arc(px, py, cellSize * 0.4, 0, 2 * Math.PI);
            ctx.fill();
        }

        // "工具"2: 游戏引擎 (读取输入, 并修改 gameState)
        function processCommand() {
            if (gameState.isAnimating) return; // 如果正在动画，则不执行新命令

            const command = commandInput.value.trim(); // 从输入框获取数据
            const moveMatch = /move\(['"](up|down|left|right)['"]\)/.exec(command);
            
            if (moveMatch && moveMatch[1]) {
                const direction = moveMatch[1];
                gameState.commands++; // 修改状态
                
                const oldPos = { x: gameState.player.x, y: gameState.player.y };
                let { x, y } = gameState.player;

                if (direction === 'up' && y > 0) gameState.player.y--;
                else if (direction === 'down' && y < gridSize - 1) gameState.player.y++;
                else if (direction === 'left' && x > 0) gameState.player.x--;
                else if (direction === 'right' && x < gridSize - 1) gameState.player.x++;

                const newPos = { x: gameState.player.x, y: gameState.player.y };
                
                // 如果位置有变化，执行动画
                if(oldPos.x !== newPos.x || oldPos.y !== newPos.y) {
                    animateMove(oldPos, newPos);
                } else {
                    renderGame(); // 位置没变也要更新命令计数
                }

            } else {
                alert("无效指令！请输入如: move('right')");
            }

            commandInput.value = ''; // 清空输入框
            commandInput.focus();
        }

        function animateMove(from, to) {
            let startTime = null;
            const duration = 200; // ms
            gameState.isAnimating = true;

            function animationFrame(timestamp) {
                if (!startTime) startTime = timestamp;
                const elapsed = timestamp - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 计算当前帧的玩家位置
                const currentX = from.x + (to.x - from.x) * progress;
                const currentY = from.y + (to.y - from.y) * progress;

                renderGameBackground(); // 重绘背景和网格
                drawPlayer(currentX * cellSize + cellSize/2, currentY * cellSize + cellSize/2);

                if (progress < 1) {
                    requestAnimationFrame(animationFrame);
                } else {
                    gameState.isAnimating = false;
                    renderGame(); // 动画结束，最终渲染
                     // 检查胜利条件
                    if (gameState.player.x === gameState.goal.x && gameState.player.y === gameState.goal.y) {
                        playVictoryAnimation();
                    }
                }
            }
            requestAnimationFrame(animationFrame);
        }

        function playVictoryAnimation() {
            let particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push({
                    x: gameState.goal.x * cellSize + cellSize/2,
                    y: gameState.goal.y * cellSize + cellSize/2,
                    vx: (Math.random() - 0.5) * 4,
                    vy: (Math.random() - 0.5) * 4,
                    size: Math.random() * 5 + 2,
                    alpha: 1,
                    color: `hsl(${Math.random() * 360}, 90%, 70%)`
                });
            }
            
            let frame = 0;
            function animate() {
                renderGame();
                particles.forEach(p => {
                    p.x += p.vx;
                    p.y += p.vy;
                    p.alpha -= 0.02;
                    ctx.globalAlpha = p.alpha;
                    ctx.fillStyle = p.color;
                    ctx.beginPath();
                    ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
                    ctx.fill();
                });
                ctx.globalAlpha = 1;
                frame++;
                if (frame < 60) requestAnimationFrame(animate);
                else {
                     setTimeout(() => {
                        alert(`恭喜！你用了 ${gameState.commands} 次命令到达了终点！游戏状态将重置。`);
                        gameState.player = { x: 2, y: 2 };
                        gameState.commands = 0;
                        renderGame(); 
                    }, 100);
                }
            }
            animate();
        }

        function renderGameBackground() {
             ctx.clearRect(0, 0, canvas.width, canvas.height);
            // Draw grid
            ctx.beginPath();
            for (let i = 0; i <= gridSize; i++) {
                ctx.moveTo(i * cellSize, 0);
                ctx.lineTo(i * cellSize, canvas.height);
                ctx.moveTo(0, i * cellSize);
                ctx.lineTo(canvas.width, i * cellSize);
            }
            ctx.strokeStyle = '#eee';
            ctx.stroke();
            // Draw Goal
            ctx.font = '30px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('★', gameState.goal.x * cellSize + cellSize / 2, gameState.goal.y * cellSize + cellSize / 2);
        }

        runCommandBtn.addEventListener('click', processCommand);
        commandInput.addEventListener('keyup', (e) => {
            if (e.key === 'Enter') {
                processCommand();
            }
        });

        // 页面加载后，进行首次渲染
        renderGame();

    </script>

</body>
</html> 