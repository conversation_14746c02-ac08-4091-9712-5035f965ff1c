<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏化学习：保持文档与架构同步</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f4f7f9;
        }
        h1, h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .game-area {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
        }
        .house-representation {
            width: 45%;
            text-align: center;
        }
        .house-representation h3 {
            color: #34495e;
        }
        .house-art {
            font-size: 100px;
            line-height: 1;
            border: 2px dashed #bdc3c7;
            padding: 20px;
            border-radius: 8px;
            background: #fff;
            min-height: 150px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .status-panel {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-panel.sync {
            background-color: #e8f5e9;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .status-panel.async {
            background-color: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        .status-panel p {
            margin: 0;
            font-weight: bold;
            font-size: 1.2em;
        }
        .controls {
            text-align: center;
        }
        .controls h3 {
             color: #2c3e50;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .explanation {
            background: #e9f7fd;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>知识点：保持文档与架构同步</h1>
        
        <div class="explanation">
            <p>想象一下，你有一张房子的设计图 (<strong>文档</strong>)，还有一个施工队根据设计图盖好的房子 (<strong>代码/架构</strong>)。</p>
            <p>如果有人修改了房子（比如多开了一扇窗），但没有更新设计图，那么下次来维修或扩建的工人就会被旧的设计图误导，造成混乱和错误。</p>
            <p>在软件开发中也是一样！<strong>文档</strong> (设计、说明) 必须和 <strong>代码</strong> (实际功能) 时刻保持一致，否则就会带来巨大的麻烦。让我们通过下面这个小游戏来体验一下吧！</p>
        </div>

        <h2>小游戏：建筑师的挑战</h2>

        <div id="status" class="status-panel">
            <p id="status-text"></p>
            <span id="status-explanation"></span>
        </div>

        <div class="game-area">
            <div class="house-representation">
                <h3>📜 设计图 (文档)</h3>
                <div id="doc" class="house-art"></div>
            </div>
            <div class="house-representation">
                <h3>🏠 实际的房子 (代码)</h3>
                <div id="house" class="house-art"></div>
            </div>
        </div>

        <div class="controls">
            <h3 id="request"></h3>
            <button id="action1"></button>
            <button id="action2"></button>
            <br>
            <button id="reset" style="background-color:#e74c3c;">重新开始</button>
        </div>
    </div>

    <script>
        const docElement = document.getElementById('doc');
        const houseElement = document.getElementById('house');
        const statusElement = document.getElementById('status');
        const statusTextElement = document.getElementById('status-text');
        const statusExplanationElement = document.getElementById('status-explanation');
        const requestElement = document.getElementById('request');
        const action1Button = document.getElementById('action1');
        const action2Button = document.getElementById('action2');
        const resetButton = document.getElementById('reset');

        let gameState = {
            doc: 'house_v1',
            house: 'house_v1',
            level: 1
        };

        const houseArt = {
            'house_v1': '🚪🪟🪟', // 1门2窗
            'house_v2': '🚪🪟🪟🪟', // 1门3窗
            'house_v3': '🚗🪟🪟🪟' // 车库门3窗
        };
        
        const scenarios = {
            1: {
                request: '新需求：客户想要再加一扇窗户。',
                action1Text: '只改造房子 (错误做法)',
                action2Text: '先更新图纸，再改造 (正确做法)',
                action1: () => {
                    gameState.house = 'house_v2';
                    updateUI();
                    statusExplanationElement.innerText = '⚠️ 警告：房子已经变了，但设计图还是旧的！新来的工程师会感到困惑。';
                },
                action2: () => {
                    gameState.doc = 'house_v2';
                    gameState.house = 'house_v2';
                    gameState.level = 2;
                    setTimeout(() => {
                        updateUI();
                        statusExplanationElement.innerText = '✅ 做得好！设计图和房子保持同步，可以准备下一个需求了。';
                    }, 500);
                }
            },
            2: {
                request: '新需求：客户想把门换成车库门。',
                action1Text: '只改造房子 (错误做法)',
                action2Text: '先更新图纸，再改造 (正确做法)',
                action1: () => {
                    gameState.house = 'house_v3';
                    updateUI();
                    statusExplanationElement.innerText = '⛔ 危险：设计图和实际严重不符！这可能导致严重的工程事故。';
                },
                action2: () => {
                    gameState.doc = 'house_v3';
                    gameState.house = 'house_v3';
                    gameState.level = 3;
                    updateUI();
                }
            },
             3: {
                request: '恭喜你！你已掌握了保持文档和架构同步的秘诀！',
                action1Text: '太棒了',
                action2Text: '再玩一次',
                action1: () => {
                   action1Button.disabled = true;
                   action2Button.disabled = true;
                },
                action2: () => {
                   initGame();
                }
            }
        };

        function updateUI() {
            docElement.innerText = houseArt[gameState.doc];
            houseElement.innerText = houseArt[gameState.house];

            const isSync = gameState.doc === gameState.house;
            
            if (isSync) {
                statusElement.className = 'status-panel sync';
                statusTextElement.innerText = '状态：同步 ✅';
                if (gameState.level < 3) {
                     statusExplanationElement.innerText = '设计图和房子完全一致，干得漂亮！';
                } else {
                     statusExplanationElement.innerText = '你总是先更新图纸，再进行施工，这保证了项目的健康！';
                }
            } else {
                statusElement.className = 'status-panel async';
                statusTextElement.innerText = '状态：不同步 ❌';
            }

            const currentScenario = scenarios[gameState.level];
            if (currentScenario) {
                requestElement.innerText = currentScenario.request;
                action1Button.innerText = currentScenario.action1Text;
                action2Button.innerText = currentScenario.action2Text;
                action1Button.onclick = currentScenario.action1;
                action2Button.onclick = currentScenario.action2;
                action1Button.disabled = false;
                action2Button.disabled = false;
            }
        }
        
        function initGame() {
            gameState = {
                doc: 'house_v1',
                house: 'house_v1',
                level: 1
            };
            updateUI();
        }

        resetButton.onclick = initGame;

        initGame();
    </script>

</body>
</html> 