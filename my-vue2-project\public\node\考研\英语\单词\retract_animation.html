<!DOCTYPE html>
<html>
<head>
<title>Retract Animation</title>
<meta charset="UTF-8">
<style>
    body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #e0eee0; /* Pale Green */
    }
    #canvas {
        border: 1px solid #000;
        background-color: #ffffff;
    }
    #explanation {
        width: 800px;
        text-align: left;
        margin-top: 20px;
    }
    #controls {
        margin-top: 10px;
    }
    button {
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
    }
</style>
</head>
<body>

<h1>Word Animation: Retract (撤回, 缩回)</h1>
<canvas id="canvas" width="800" height="400"></canvas>
<div id="controls">
    <button id="playBtn">Play Animation</button>
</div>
<div id="explanation">
    <h2>Retract (撤回) = re- (向后) + tract (拉)</h2>
    <p><b>故事:</b> 想象一只乌龟，它正悠闲地伸着头。突然，它感觉到了危险（比如一个阴影靠近），于是迅速地把头"拉"了"回去"，缩进了安全的龟壳里。这个"向后拉回"的动作就是 "retract"。</p>
    <p><b>re- (向后):</b> 乌龟的头是<b>向后</b>缩进壳里的。</p>
    <p><b>tract (拉):</b> 乌龟的肌肉用力把头<b>拉</b>回来以求自保。</p>
    <p><b>交互:</b> 点击 "Play Animation" 按钮，观看乌龟感觉到危险并把头缩回壳里的过程。</p>
</div>

<script>
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
const playBtn = document.getElementById('playBtn');

let animationId;
let progress = 0; // 0 to 1

const turtle = {
    shellX: 400,
    shellY: 250,
    shellRadius: 80,
    headStartX: 400 + 120, 
    headEndX: 400 + 30,
    headY: 250
};

const danger = {
    startX: 700,
    startY: 100,
    endX: 550,
    endY: 150,
    radius: 30
};

function drawTurtleShell() {
    ctx.beginPath();
    ctx.arc(turtle.shellX, turtle.shellY, turtle.shellRadius, 0, Math.PI * 2);
    ctx.fillStyle = '#228B22'; // ForestGreen
    ctx.fill();
    // Shell pattern
    ctx.strokeStyle = '#006400'; // DarkGreen
    ctx.lineWidth = 2;
    for(let i = 0; i < 6; i++) {
        ctx.beginPath();
        ctx.moveTo(turtle.shellX + turtle.shellRadius * Math.cos(i * Math.PI / 3), turtle.shellY + turtle.shellRadius * Math.sin(i * Math.PI / 3));
        ctx.lineTo(turtle.shellX, turtle.shellY);
        ctx.stroke();
    }
}

function drawTurtleHead(x, y) {
    // Neck
    ctx.beginPath();
    ctx.moveTo(turtle.shellX + turtle.shellRadius - 10, turtle.shellY);
    ctx.lineTo(x, y);
    ctx.strokeStyle = '#9ACD32'; // YellowGreen
    ctx.lineWidth = 20;
    ctx.stroke();
    
    // Head
    ctx.beginPath();
    ctx.arc(x, y, 15, 0, Math.PI * 2);
    ctx.fillStyle = '#6B8E23'; // OliveDrab
    ctx.fill();
    
    // Eye
    ctx.beginPath();
    ctx.arc(x + 5, y - 5, 3, 0, Math.PI * 2);
    ctx.fillStyle = 'white';
    ctx.fill();
    ctx.beginPath();
    ctx.arc(x + 6, y - 5, 1, 0, Math.PI * 2);
    ctx.fillStyle = 'black';
    ctx.fill();
}

function drawDanger(x, y, radius) {
    ctx.beginPath();
    ctx.arc(x, y, radius, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'; // Semi-transparent black
    ctx.fill();
    ctx.font = '16px Arial';
    ctx.fillStyle = 'black';
    ctx.fillText('危险!', x, y + radius + 20);
}

function drawArrow(startX, startY, endX, endY, text) {
    const headlen = 10;
    const angle = Math.atan2(endY - startY, endX - startX);
    
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.strokeStyle = 'blue';
    ctx.lineWidth = 3;
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(endX - headlen * Math.cos(angle - Math.PI / 6), endY - headlen * Math.sin(angle - Math.PI / 6));
    ctx.lineTo(endX - headlen * Math.cos(angle + Math.PI / 6), endY - headlen * Math.sin(angle + Math.PI / 6));
    ctx.fillStyle = 'blue';
    ctx.fill();

    ctx.font = '24px Arial';
    ctx.fillStyle = 'blue';
    ctx.textAlign = 'center';
    ctx.fillText(text, (startX + endX) / 2, startY - 20);
}

function animate() {
    progress += 0.01;
    if (progress > 1) {
        progress = 1;
    }

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Animate danger and turtle head based on progress
    let headX, dangerX, dangerY, dangerRadius;

    if (progress < 0.5) {
        // Phase 1: Danger approaches
        const phaseProgress = progress * 2;
        headX = turtle.headStartX;
        dangerX = danger.startX + (danger.endX - danger.startX) * phaseProgress;
        dangerY = danger.startY + (danger.endY - danger.startY) * phaseProgress;
        dangerRadius = danger.radius + 10 * phaseProgress;
    } else {
        // Phase 2: Turtle retracts head
        const phaseProgress = (progress - 0.5) * 2;
        headX = turtle.headStartX + (turtle.headEndX - turtle.headStartX) * phaseProgress;
        dangerX = danger.endX;
        dangerY = danger.endY;
        dangerRadius = danger.radius + 10;

        // Show annotations during retraction
        drawArrow(headX + 50, turtle.headY, headX, turtle.headY, 're- (向后)');
        ctx.fillStyle = 'purple';
        ctx.font = '24px Arial';
        ctx.fillText('tract (拉)', headX + 30, turtle.headY + 40);
    }
    
    drawTurtleShell();
    drawTurtleHead(headX, turtle.headY);
    drawDanger(dangerX, dangerY, dangerRadius);
    
    if (progress >= 1) {
        cancelAnimationFrame(animationId);
        playBtn.disabled = false;
        playBtn.textContent = "Play Again";
        ctx.fillStyle = 'green';
        ctx.font = '20px Arial';
        ctx.fillText('已缩回!', turtle.shellX, turtle.shellY - turtle.shellRadius - 20);
    } else {
        animationId = requestAnimationFrame(animate);
    }
}

function startAnimation() {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
    progress = 0;
    playBtn.disabled = true;
    playBtn.textContent = "Animating...";
    animate();
}

playBtn.addEventListener('click', startAnimation);

// Initial draw
ctx.clearRect(0, 0, canvas.width, canvas.height);
drawTurtleShell();
drawTurtleHead(turtle.headStartX, turtle.headY);
drawDanger(danger.startX, danger.startY, danger.radius);

</script>

</body>
</html> 