<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式数据库知识解析</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        h1, h2, h3 {
            color: #007bff;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .question-section, .explanation-section {
            margin-bottom: 30px;
        }
        .question {
            font-size: 1.3em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .options div {
            margin-bottom: 10px;
            background-color: #f9f9f9;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .options div:hover {
            background-color: #eef;
        }
        .options div.selected {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
        }
        .options div.correct {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .options div.wrong {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .answer-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 8px;
            display: none; /* Hidden by default */
        }
        .explanation-point {
            margin-bottom: 25px;
            background-color: #f0f8ff;
            padding: 20px;
            border-left: 5px solid #007bff;
            border-radius: 8px;
        }
        .explanation-point h3 {
            color: #007bff;
            margin-top: 0;
            border-bottom: none;
            padding-bottom: 0;
        }
        .explanation-point p {
            margin-bottom: 15px;
        }
        canvas {
            border: 1px solid #ddd;
            background-color: #fff;
            display: block;
            margin: 20px auto;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
            margin-top: 15px;
        }
        button:hover {
            background-color: #218838;
        }
        .action-button-group {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>嵌入式数据库知识解析</h1>

        <div class="question-section">
            <h2>题目</h2>
            <p class="question">与传统数据库相比，嵌入式数据库的主要特点不包括（）；基于文件的嵌入式数据库系统典型产品是（）。</p>

            <div class="options">
                <div data-option="A">A. 嵌入式</div>
                <div data-option="B">B. 实时性</div>
                <div data-option="C">C. 伸缩性</div>
                <div data-option="D">D. 分布性</div>
            </div>
            <div class="action-button-group">
                <button id="submitAnswer">提交答案</button>
            </div>


            <div class="answer-area" id="answerArea">
                <h3>正确答案</h3>
                <p>第一个空：<span id="correctAnswer1"></span></p>
                <p>第二个空：<span id="correctAnswer2"></span></p>
                <p>请点击下面的按钮，查看详细的知识点解析和动画演示！</p>
                <div class="action-button-group">
                    <button id="showExplanation">查看解析和演示</button>
                </div>
            </div>
        </div>

        <div class="explanation-section" id="explanationSection" style="display: none;">
            <h2>知识点解析与动画演示</h2>

            <div class="explanation-point" id="explanationPoint_embedded">
                <h3>1. 嵌入式 (Embedded)</h3>
                <p><b>什么是嵌入式数据库？</b> 嵌入式数据库是一种可以紧密集成到应用程序中的数据库，它不作为一个独立的服务运行，而是作为应用程序的一部分。这意味着它不需要单独的安装或配置，可以直接随应用程序一起部署。</p>
                <p>它就像一个"迷你"数据库引擎，直接"住"在你的程序内部。</p>
                <canvas id="canvasEmbedded" width="500" height="200"></canvas>
                <div class="action-button-group">
                    <button onclick="startEmbeddedAnimation()">开始演示：嵌入</button>
                </div>
            </div>

            <div class="explanation-point" id="explanationPoint_realtime">
                <h3>2. 实时性 (Real-time)</h3>
                <p><b>嵌入式数据库有实时性吗？</b> 实时性是指数据库能够迅速响应请求，并在严格的时间限制内完成操作。虽然有些嵌入式数据库可以实现实时性，但这并非其固有特性，需要额外的设计和优化才能达到。</p>
                <p>想象一下，你发出一个命令，数据库必须立刻给出回应！</p>
                <canvas id="canvasRealtime" width="500" height="200"></canvas>
                <div class="action-button-group">
                    <button onclick="startRealtimeAnimation()">开始演示：实时响应</button>
                </div>
            </div>

            <div class="explanation-point" id="explanationPoint_mobility">
                <h3>3. 移动性 (Mobility)</h3>
                <p><b>嵌入式数据库与移动设备的关系？</b> 嵌入式数据库因其轻量级、无需独立部署的特点，非常适合移动设备和物联网设备。它们可以在资源有限的环境中稳定运行，使得数据处理更贴近设备本身。</p>
                <p>它能轻松地跟着你的手机、手表等设备一起走，随时随地处理数据。</p>
                <canvas id="canvasMobility" width="500" height="200"></canvas>
                <div class="action-button-group">
                    <button onclick="startMobilityAnimation()">开始演示：移动伴侣</button>
                </div>
            </div>

            <div class="explanation-point" id="explanationPoint_scalability">
                <h3>4. 伸缩性 (Scalability)</h3>
                <p><b>伸缩性对嵌入式数据库重要吗？</b> 伸缩性是指数据库在负载增加时，能够通过增加资源（如CPU、内存）来保持性能的能力。在嵌入式场景中，硬件和软件平台差异很大，用户可以根据需求选择不同伸缩性特点的嵌入式数据库。</p>
                <p>它能根据你的需求，"变大变小"以适应不同的数据量。</p>
                <canvas id="canvasScalability" width="500" height="200"></canvas>
                <div class="action-button-group">
                    <button onclick="startScalabilityAnimation()">开始演示：灵活伸缩</button>
                </div>
            </div>

            <div class="explanation-point" id="explanationPoint_distributed">
                <h3>5. 分布性 (Distributed) - 非特点</h3>
                <p><b>为什么分布性不是嵌入式数据库的特点？</b> 分布式数据库将数据分散存储在多台计算机上，以提高性能、可用性和容错能力。而嵌入式数据库通常是单机、本地运行的，不具备分布式系统的特性。因此，"分布性"不属于嵌入式数据库的主要特点。</p>
                <p>嵌入式数据库通常是"一个人"工作，而不是"一群人"协同工作。</p>
                <canvas id="canvasDistributed" width="500" height="200"></canvas>
                <div class="action-button-group">
                    <button onclick="startDistributedAnimation()">开始演示：并非分布式</button>
                </div>
            </div>

            <div class="explanation-point" id="explanationPoint_filebased">
                <h3>6. 文件型嵌入式数据库 (File-based Embedded Database)</h3>
                <p><b>文件型嵌入式数据库有哪些？</b> 这类数据库将所有数据存储在一个或多个文件中，而不是通过独立的服务器进程管理。典型的产品包括 <b>SQLite</b> 和 <b>Berkeley DB</b>。</p>
                <p>它们直接把数据写进文件，就像你把信息写进笔记本一样简单。</p>
                <canvas id="canvasFilebased" width="500" height="200"></canvas>
                <div class="action-button-group">
                    <button onclick="startFilebasedAnimation()">开始演示：文件存储</button>
                </div>
            </div>

        </div>
    </div>

    <script>
        const options = document.querySelectorAll('.options div');
        const submitButton = document.getElementById('submitAnswer');
        const showExplanationButton = document.getElementById('showExplanation');
        const answerArea = document.getElementById('answerArea');
        const explanationSection = document.getElementById('explanationSection');
        const correctAnswer1Span = document.getElementById('correctAnswer1');
        const correctAnswer2Span = document.getElementById('correctAnswer2');

        let selectedOption = null;

        options.forEach(option => {
            option.addEventListener('click', () => {
                options.forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
                selectedOption = option.dataset.option;
            });
        });

        submitButton.addEventListener('click', () => {
            if (selectedOption) {
                // Remove previous answer highlights
                options.forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });

                if (selectedOption === 'D') {
                    document.querySelector(`[data-option="${selectedOption}"]`).classList.add('correct');
                    correctAnswer1Span.textContent = "D. 分布性";
                    correctAnswer2Span.textContent = "SQLite / Berkeley DB (任选其一)"; // Based on explanation
                    answerArea.style.display = 'block';
                } else {
                    document.querySelector(`[data-option="${selectedOption}"]`).classList.add('wrong');
                    alert('答案错误，请再想想！');
                    // Still show correct answer after wrong attempt for learning
                    correctAnswer1Span.textContent = "D. 分布性";
                    correctAnswer2Span.textContent = "SQLite / Berkeley DB (任选其一)";
                    answerArea.style.display = 'block';
                }
            } else {
                alert('请选择一个选项！');
            }
        });

        showExplanationButton.addEventListener('click', () => {
            explanationSection.style.display = 'block';
            // Scroll to the explanation section
            explanationSection.scrollIntoView({ behavior: 'smooth' });
        });

        // --- Canvas Animations ---

        // Helper for drawing rounded rectangles
        function roundRect(ctx, x, y, width, height, radius) {
            if (width < 2 * radius) radius = width / 2;
            if (height < 2 * radius) radius = height / 2;
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.arcTo(x + width, y, x + width, y + height, radius);
            ctx.arcTo(x + width, y + height, x, y + height, radius);
            ctx.arcTo(x, y + height, x, y, radius);
            ctx.arcTo(x, y, x + width, y, radius);
            ctx.closePath();
            return ctx;
        }

        // Embedded Animation
        let embeddedAnimationId = null;
        function startEmbeddedAnimation() {
            if (embeddedAnimationId) cancelAnimationFrame(embeddedAnimationId); // Stop previous if running
            const canvas = document.getElementById('canvasEmbedded');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear canvas

            const appRect = { x: 50, y: 30, width: 200, height: 140, radius: 15 };
            const dbRect = { x: appRect.x + appRect.width / 2 - 40, y: appRect.y + appRect.height / 2 - 30, width: 80, height: 60, radius: 8 };

            let progress = 0;
            const duration = 1500; // milliseconds

            function animate(currentTime) {
                if (!animate.startTime) animate.startTime = currentTime;
                progress = (currentTime - animate.startTime) / duration;

                if (progress > 1) progress = 1;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Draw application box
                ctx.fillStyle = '#ADD8E6'; // Light blue
                roundRect(ctx, appRect.x, appRect.y, appRect.width, appRect.height, appRect.radius).fill();
                ctx.strokeStyle = '#007bff';
                ctx.lineWidth = 2;
                roundRect(ctx, appRect.x, appRect.y, appRect.width, appRect.height, appRect.radius).stroke();
                ctx.fillStyle = '#333';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('应用程序', appRect.x + appRect.width / 2, appRect.y + 25);

                // Calculate current position of DB
                const startX = canvas.width - dbRect.width - 50; // Start outside to the right
                const startY = 70;
                const currentX = startX - (startX - dbRect.x) * progress;
                const currentY = startY - (startY - dbRect.y) * progress;

                // Draw database box
                ctx.fillStyle = '#FFA07A'; // Light orange
                roundRect(ctx, currentX, currentY, dbRect.width, dbRect.height, dbRect.radius).fill();
                ctx.strokeStyle = '#FF4500';
                roundRect(ctx, currentX, currentY, dbRect.width, dbRect.height, dbRect.radius).stroke();
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据库', currentX + dbRect.width / 2, currentY + dbRect.height / 2 + 5);

                if (progress < 1) {
                    embeddedAnimationId = requestAnimationFrame(animate);
                } else {
                    animate.startTime = null; // Reset for next run
                }
            }
            animate.startTime = null; // Initialize startTime
            embeddedAnimationId = requestAnimationFrame(animate);
        }

        // Real-time Animation
        let realtimeAnimationId = null;
        function startRealtimeAnimation() {
            if (realtimeAnimationId) cancelAnimationFrame(realtimeAnimationId);
            const canvas = document.getElementById('canvasRealtime');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const sender = { x: 50, y: 75, width: 80, height: 50 };
            const receiver = { x: canvas.width - 130, y: 75, width: 80, height: 50 };

            ctx.fillStyle = '#FFE5B4'; // Light yellow
            ctx.strokeStyle = '#FFCC00';
            roundRect(ctx, sender.x, sender.y, sender.width, sender.height, 5).fill();
            roundRect(ctx, sender.x, sender.y, sender.width, sender.height, 5).stroke();
            ctx.fillText('请求', sender.x + sender.width / 2, sender.y + sender.height / 2 + 5);

            ctx.fillStyle = '#D4EDDA'; // Light green
            ctx.strokeStyle = '#28A745';
            roundRect(ctx, receiver.x, receiver.y, receiver.width, receiver.height, 5).fill();
            roundRect(ctx, receiver.x, receiver.y, receiver.width, receiver.height, 5).stroke();
            ctx.fillText('响应', receiver.x + receiver.width / 2, receiver.y + receiver.height / 2 + 5);

            let arrowX = sender.x + sender.width;
            let arrowOpacity = 0;
            const arrowSpeed = 5; // pixels per frame
            const fadeDuration = 50; // frames for fade in/out

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Redraw sender and receiver
                ctx.fillStyle = '#FFE5B4';
                ctx.strokeStyle = '#FFCC00';
                roundRect(ctx, sender.x, sender.y, sender.width, sender.height, 5).fill();
                roundRect(ctx, sender.x, sender.y, sender.width, sender.height, 5).stroke();
                ctx.fillStyle = '#333';
                ctx.fillText('请求', sender.x + sender.width / 2, sender.y + sender.height / 2 + 5);

                ctx.fillStyle = '#D4EDDA';
                ctx.strokeStyle = '#28A745';
                roundRect(ctx, receiver.x, receiver.y, receiver.width, receiver.height, 5).fill();
                roundRect(ctx, receiver.x, receiver.y, receiver.width, receiver.height, 5).stroke();
                ctx.fillStyle = '#333';
                ctx.fillText('响应', receiver.x + receiver.width / 2, receiver.y + receiver.height / 2 + 5);

                // Draw arrow
                ctx.strokeStyle = `rgba(255, 0, 0, ${arrowOpacity})`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(arrowX, canvas.height / 2);
                ctx.lineTo(arrowX + 20, canvas.height / 2);
                ctx.stroke();

                // Draw arrowhead
                ctx.beginPath();
                ctx.moveTo(arrowX + 20, canvas.height / 2);
                ctx.lineTo(arrowX + 10, canvas.height / 2 - 5);
                ctx.moveTo(arrowX + 20, canvas.height / 2);
                ctx.lineTo(arrowX + 10, canvas.height / 2 + 5);
                ctx.stroke();


                arrowX += arrowSpeed;

                if (arrowX > receiver.x) {
                    arrowOpacity -= 1 / fadeDuration; // Fade out
                    if (arrowOpacity <= 0) {
                        arrowX = sender.x + sender.width; // Reset position
                        arrowOpacity = 1; // Start fade in
                    }
                } else if (arrowX <= sender.x + sender.width + 20) {
                    arrowOpacity = 1; // Keep visible during travel
                }

                realtimeAnimationId = requestAnimationFrame(animate);
            }
            realtimeAnimationId = requestAnimationFrame(animate);
        }

        // Mobility Animation
        let mobilityAnimationId = null;
        function startMobilityAnimation() {
            if (mobilityAnimationId) cancelAnimationFrame(mobilityAnimationId);
            const canvas = document.getElementById('canvasMobility');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const phone = { x: 50, y: 50, width: 40, height: 80 };
            const db = { x: phone.x + 5, y: phone.y + 40, width: 30, height: 20 };
            let currentX = phone.x;

            function drawPhone(x, y) {
                ctx.fillStyle = '#555';
                roundRect(ctx, x, y, phone.width, phone.height, 5).fill();
                ctx.fillStyle = '#eee';
                ctx.fillRect(x + 5, y + 5, phone.width - 10, phone.height - 15); // Screen
                ctx.fillStyle = '#FFA07A'; // DB color
                roundRect(ctx, x + 5, y + 40, db.width, db.height, 3).fill();
                ctx.strokeStyle = '#FF4500';
                roundRect(ctx, x + 5, y + 40, db.width, db.height, 3).stroke();
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText('DB', x + 5 + db.width / 2, y + 40 + db.height / 2 + 4);
            }

            let direction = 1; // 1 for right, -1 for left
            let speed = 2; // pixels per frame

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                drawPhone(currentX, phone.y);

                currentX += speed * direction;

                if (currentX + phone.width > canvas.width - 50 || currentX < 50) {
                    direction *= -1; // Reverse direction
                }

                mobilityAnimationId = requestAnimationFrame(animate);
            }
            mobilityAnimationId = requestAnimationFrame(animate);
        }

        // Scalability Animation
        let scalabilityAnimationId = null;
        function startScalabilityAnimation() {
            if (scalabilityAnimationId) cancelAnimationFrame(scalabilityAnimationId);
            const canvas = document.getElementById('canvasScalability');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const initialDb = { x: canvas.width / 2 - 50, y: 70, width: 100, height: 60 };
            let currentWidth = initialDb.width;
            let currentHeight = initialDb.height;
            let expanding = true;
            const expandFactor = 0.5; // pixels per frame
            const maxWidth = 180;
            const minWidth = 80;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#B0E0E6'; // Powdeblue
                ctx.strokeStyle = '#4682B4'; // Steelblue
                ctx.lineWidth = 2;

                const dbX = canvas.width / 2 - currentWidth / 2;
                const dbY = initialDb.y + (initialDb.height - currentHeight) / 2;

                roundRect(ctx, dbX, dbY, currentWidth, currentHeight, 8).fill();
                roundRect(ctx, dbX, dbY, currentWidth, currentHeight, 8).stroke();
                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据库', dbX + currentWidth / 2, dbY + currentHeight / 2 + 5);

                if (expanding) {
                    currentWidth += expandFactor;
                    currentHeight += expandFactor * 0.6; // Keep aspect ratio somewhat
                    if (currentWidth >= maxWidth) {
                        expanding = false;
                    }
                } else {
                    currentWidth -= expandFactor;
                    currentHeight -= expandFactor * 0.6;
                    if (currentWidth <= minWidth) {
                        expanding = true;
                    }
                }

                scalabilityAnimationId = requestAnimationFrame(animate);
            }
            scalabilityAnimationId = requestAnimationFrame(animate);
        }

        // Distributed Animation
        let distributedAnimationId = null;
        function startDistributedAnimation() {
            if (distributedAnimationId) cancelAnimationFrame(distributedAnimationId);
            const canvas = document.getElementById('canvasDistributed');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const dbColor = '#FFA07A'; // Light orange
            const connectionColor = '#999';

            function drawDbNode(x, y, label) {
                ctx.fillStyle = dbColor;
                ctx.strokeStyle = '#FF4500';
                ctx.lineWidth = 2;
                roundRect(ctx, x, y, 70, 50, 8).fill();
                roundRect(ctx, x, y, 70, 50, 8).stroke();
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(label, x + 35, y + 30);
            }

            function drawConnection(x1, y1, x2, y2) {
                ctx.strokeStyle = connectionColor;
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]); // Dashed line
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
                ctx.setLineDash([]); // Reset line dash
            }

            drawDbNode(50, 75, '数据库'); // Main DB

            let fadeOutProgress = 0;
            const fadeOutDuration = 2000; // milliseconds

            function animate(currentTime) {
                if (!animate.startTime) animate.startTime = currentTime;
                fadeOutProgress = (currentTime - animate.startTime) / fadeOutDuration;

                if (fadeOutProgress > 1) fadeOutProgress = 1;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Draw main DB
                drawDbNode(50, 75, '嵌入式数据库');

                // Draw "distributed" elements fading out
                const alpha = 1 - fadeOutProgress;
                if (alpha > 0) {
                    ctx.save();
                    ctx.globalAlpha = alpha;

                    drawDbNode(250, 30, 'DB节点1');
                    drawDbNode(250, 120, 'DB节点2');
                    drawDbNode(400, 75, 'DB节点3');

                    drawConnection(50 + 70, 75 + 25, 250, 30 + 25);
                    drawConnection(50 + 70, 75 + 25, 250, 120 + 25);
                    drawConnection(250 + 70, 30 + 25, 400, 75 + 25);
                    drawConnection(250 + 70, 120 + 25, 400, 75 + 25);

                    ctx.restore();
                }

                if (fadeOutProgress < 1) {
                    distributedAnimationId = requestAnimationFrame(animate);
                } else {
                    ctx.fillStyle = '#FF0000';
                    ctx.font = '24px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('X (不具备分布性)', canvas.width / 2 + 50, canvas.height / 2);
                    animate.startTime = null; // Reset for next run
                }
            }
            animate.startTime = null; // Initialize startTime
            distributedAnimationId = requestAnimationFrame(animate);
        }

        // File-based Animation
        let filebasedAnimationId = null;
        function startFilebasedAnimation() {
            if (filebasedAnimationId) cancelAnimationFrame(filebasedAnimationId);
            const canvas = document.getElementById('canvasFilebased');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const db = { x: 50, y: 75, width: 80, height: 50 };
            const file = { x: canvas.width - 130, y: 75, width: 60, height: 70 };

            function drawDatabase(x, y) {
                ctx.fillStyle = '#FFA07A';
                ctx.strokeStyle = '#FF4500';
                roundRect(ctx, x, y, db.width, db.height, 8).fill();
                roundRect(ctx, x, y, db.width, db.height, 8).stroke();
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.fillText('数据库', x + db.width / 2, y + db.height / 2 + 5);
            }

            function drawFile(x, y) {
                ctx.fillStyle = '#D3D3D3'; // Light gray
                ctx.strokeStyle = '#A9A9A9'; // Darker gray
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x + 10, y);
                ctx.lineTo(x + file.width, y);
                ctx.lineTo(x + file.width, y + file.height);
                ctx.lineTo(x, y + file.height);
                ctx.lineTo(x, y + 10);
                ctx.lineTo(x + 10, y);
                ctx.closePath();
                ctx.fill();
                ctx.stroke();

                ctx.fillStyle = '#ADD8E6';
                ctx.fillRect(x + 5, y + 5, 20, 10); // Paper fold effect
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText('data.db', x + file.width / 2, y + file.height - 20);
            }

            let pulseScale = 1;
            let pulseDirection = 1;
            const pulseSpeed = 0.005;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                drawDatabase(db.x, db.y);
                drawFile(file.x, file.y);

                // Simulate data flow
                ctx.strokeStyle = `rgba(0, 123, 255, ${pulseScale})`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(db.x + db.width, db.y + db.height / 2);
                ctx.lineTo(file.x, file.y + file.height / 2);
                ctx.stroke();

                pulseScale += pulseSpeed * pulseDirection;
                if (pulseScale > 1.2 || pulseScale < 0.8) {
                    pulseDirection *= -1;
                }

                filebasedAnimationId = requestAnimationFrame(animate);
            }
            filebasedAnimationId = requestAnimationFrame(animate);
        }

    </script>
</body>
</html> 