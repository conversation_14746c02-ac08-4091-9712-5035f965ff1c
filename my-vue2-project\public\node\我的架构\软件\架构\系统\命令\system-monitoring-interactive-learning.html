<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控学习 - 互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .monitoring-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .method-card {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 3px solid transparent;
        }

        .method-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .method-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            animation: pulse 2s infinite;
        }

        .method1 { background: linear-gradient(45deg, #ff6b6b, #ee5a24); }
        .method2 { background: linear-gradient(45deg, #4ecdc4, #44a08d); }
        .method3 { background: linear-gradient(45deg, #a8edea, #fed6e3); color: #333 !important; }

        .quiz-container {
            background: #f0f2ff;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .question {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: white;
            border: 2px solid #e0e6ed;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            border-color: #667eea;
            transform: scale(1.02);
        }

        .option.selected {
            border-color: #667eea;
            background: #f0f2ff;
        }

        .option.correct {
            border-color: #27ae60;
            background: #d5f4e6;
            animation: correctAnswer 0.6s ease;
        }

        .option.wrong {
            border-color: #e74c3c;
            background: #ffeaea;
            animation: wrongAnswer 0.6s ease;
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        #animationCanvas {
            border: 2px solid #e0e6ed;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .explanation {
            background: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            display: none;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(102, 126, 234, 0.3);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongAnswer {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
            100% { transform: translateX(0); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .game-mode {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .score {
            font-size: 1.5rem;
            color: #333;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🖥️ 系统监控学习</h1>
            <p class="subtitle">通过动画和互动学习系统监控的三种方式</p>
            <div class="game-mode">
                <div class="score">🎮 学习积分: <span id="score">0</span></div>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 知识讲解</h2>
            
            <div class="monitoring-methods">
                <div class="method-card" onclick="showMethodAnimation(1)">
                    <div class="method-icon method1">💻</div>
                    <h3>方式一：命令行工具</h3>
                    <p>通过系统命令查看进程和状态<br>如：ps、top、netstat等</p>
                </div>
                
                <div class="method-card" onclick="showMethodAnimation(2)">
                    <div class="method-icon method2">📋</div>
                    <h3>方式二：系统日志文件</h3>
                    <p>查阅系统记录文件<br>了解特定时间内的运行状态</p>
                </div>
                
                <div class="method-card" onclick="showMethodAnimation(3)">
                    <div class="method-icon method3">📊</div>
                    <h3>方式三：可视化监控工具</h3>
                    <p>集成命令、文件记录和可视化<br>如：Perfmon等专业工具</p>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="explanation" id="explanation">
                <h3>💡 详细解释</h3>
                <p id="explanationText">点击上方的监控方式卡片，查看动画演示和详细解释！</p>
            </div>
        </div>

        <div class="quiz-container">
            <h2 class="section-title">🎯 练习题目</h2>
            
            <div class="question">
                进行系统监视通常有三种方式：一是通过（ ），如UNIX/Linux系统中的ps、last等；二是通过系统记录文件查阅系统在特定时间内的运行状态；三是集成命令、文件记录和可视化技术的监控工具，如（ ）。
            </div>
            
            <div class="options">
                <div class="option" onclick="selectOption(this, 'A')">
                    <strong>A. Windows的netstat</strong><br>
                    <small>查看端口占用情况</small>
                </div>
                <div class="option" onclick="selectOption(this, 'B')">
                    <strong>B. Linux的iptables</strong><br>
                    <small>包过滤防火墙工具</small>
                </div>
                <div class="option" onclick="selectOption(this, 'C')">
                    <strong>C. Windows的Perfmon</strong><br>
                    <small>性能监控工具</small>
                </div>
                <div class="option" onclick="selectOption(this, 'D')">
                    <strong>D. Linux的top</strong><br>
                    <small>实时显示进程资源占用</small>
                </div>
            </div>
            
            <button class="btn" onclick="checkAnswer()">提交答案</button>
            <button class="btn" onclick="showDetailedExplanation()">查看详细解析</button>
            <button class="btn" onclick="resetQuiz()">重新答题</button>
            
            <div class="explanation" id="detailedExplanation">
                <h3>📖 详细解析</h3>
                <p><strong>正确答案：C</strong></p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li><strong>netstat</strong>：用来查看端口号占用情况，不是可视化监控工具</li>
                    <li><strong>iptables</strong>：Linux防火墙工具，用于包过滤，不是监控工具</li>
                    <li><strong>Perfmon</strong>：Windows性能监控器，提供图表化实时监控，符合题意</li>
                    <li><strong>top</strong>：Linux命令行工具，属于第一种方式，不是可视化工具</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let score = 0;
        
        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 6; i++) {
                const circle = document.createElement('div');
                circle.className = 'floating-circle';
                circle.style.width = Math.random() * 100 + 50 + 'px';
                circle.style.height = circle.style.width;
                circle.style.left = Math.random() * 100 + '%';
                circle.style.top = Math.random() * 100 + '%';
                circle.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(circle);
            }
        }

        function updateScore(points) {
            score += points;
            document.getElementById('score').textContent = score;
        }

        // Canvas动画
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentAnimation = 0;

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function showMethodAnimation(method) {
            clearCanvas();
            cancelAnimationFrame(animationId);
            currentAnimation = method;
            updateScore(10); // 观看动画获得积分

            const explanation = document.getElementById('explanation');
            const explanationText = document.getElementById('explanationText');

            explanation.style.display = 'block';

            switch(method) {
                case 1:
                    explanationText.innerHTML = `
                        <strong>命令行工具监控</strong><br>
                        • 使用系统内置命令查看状态<br>
                        • 实时性强，资源占用少<br>
                        • 需要一定的命令行知识<br>
                        • 常用命令：ps（进程）、top（资源）、netstat（网络）
                    `;
                    animateCommandLine();
                    break;
                case 2:
                    explanationText.innerHTML = `
                        <strong>系统日志文件监控</strong><br>
                        • 通过日志文件了解历史状态<br>
                        • 可以追溯问题发生时间<br>
                        • 适合事后分析和审计<br>
                        • 常见位置：/var/log/、Windows事件查看器
                    `;
                    animateLogFiles();
                    break;
                case 3:
                    explanationText.innerHTML = `
                        <strong>可视化监控工具</strong><br>
                        • 图形化界面，直观易懂<br>
                        • 集成多种监控功能<br>
                        • 支持实时监控和历史数据<br>
                        • 代表工具：Perfmon、Grafana、Zabbix
                    `;
                    animateVisualization();
                    break;
            }
        }

        function animateCommandLine() {
            let frame = 0;
            function draw() {
                clearCanvas();

                // 绘制终端窗口
                ctx.fillStyle = '#2c3e50';
                ctx.fillRect(50, 50, 700, 300);

                // 绘制标题栏
                ctx.fillStyle = '#34495e';
                ctx.fillRect(50, 50, 700, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText('Terminal - 系统监控命令', 60, 70);

                // 绘制终端文本
                ctx.fillStyle = '#2ecc71';
                ctx.font = '16px monospace';

                const commands = [
                    '$ ps aux',
                    'USER  PID  %CPU %MEM COMMAND',
                    'root   1   0.1  0.2  /sbin/init',
                    'user  1234 2.5  1.8  firefox',
                    '$ top',
                    'PID USER %CPU %MEM COMMAND',
                    '1234 user 25.0  5.2  chrome',
                    '$ netstat -an | grep LISTEN'
                ];

                for (let i = 0; i < Math.min(commands.length, Math.floor(frame / 30)); i++) {
                    ctx.fillText(commands[i], 70, 110 + i * 25);
                }

                // 闪烁光标
                if (Math.floor(frame / 15) % 2 === 0) {
                    const currentLine = Math.min(commands.length - 1, Math.floor(frame / 30));
                    ctx.fillText('_', 70 + ctx.measureText(commands[currentLine] || '').width, 110 + currentLine * 25);
                }

                frame++;
                if (frame < 300) {
                    animationId = requestAnimationFrame(draw);
                }
            }
            draw();
        }

        function animateLogFiles() {
            let frame = 0;
            function draw() {
                clearCanvas();

                // 绘制文件夹
                ctx.fillStyle = '#f39c12';
                ctx.fillRect(100, 100, 150, 120);
                ctx.fillStyle = '#e67e22';
                ctx.fillRect(100, 100, 150, 20);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText('/var/log/', 110, 115);

                // 绘制文件
                const files = ['system.log', 'error.log', 'access.log'];
                for (let i = 0; i < files.length; i++) {
                    ctx.fillStyle = '#ecf0f1';
                    ctx.fillRect(120 + i * 20, 140 + i * 15, 100, 60);
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '12px Arial';
                    ctx.fillText(files[i], 125 + i * 20, 175 + i * 15);
                }

                // 绘制日志内容
                if (frame > 60) {
                    ctx.fillStyle = '#34495e';
                    ctx.fillRect(350, 80, 400, 240);
                    ctx.fillStyle = '#ecf0f1';
                    ctx.fillRect(350, 80, 400, 30);
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px Arial';
                    ctx.fillText('系统日志查看器', 360, 100);

                    ctx.fillStyle = '#2ecc71';
                    ctx.font = '12px monospace';

                    const logs = [
                        '2024-01-15 10:30:25 INFO: System started',
                        '2024-01-15 10:31:02 WARN: High CPU usage',
                        '2024-01-15 10:32:15 ERROR: Connection failed',
                        '2024-01-15 10:33:45 INFO: Service restarted',
                        '2024-01-15 10:34:12 INFO: Normal operation'
                    ];

                    for (let i = 0; i < Math.min(logs.length, Math.floor((frame - 60) / 30)); i++) {
                        const color = logs[i].includes('ERROR') ? '#e74c3c' :
                                     logs[i].includes('WARN') ? '#f39c12' : '#2ecc71';
                        ctx.fillStyle = color;
                        ctx.fillText(logs[i], 360, 130 + i * 20);
                    }
                }

                frame++;
                if (frame < 300) {
                    animationId = requestAnimationFrame(draw);
                }
            }
            draw();
        }

        function animateVisualization() {
            let frame = 0;
            function draw() {
                clearCanvas();

                // 绘制监控界面
                ctx.fillStyle = '#ecf0f1';
                ctx.fillRect(50, 50, 700, 300);

                // 绘制标题栏
                ctx.fillStyle = '#3498db';
                ctx.fillRect(50, 50, 700, 40);
                ctx.fillStyle = 'white';
                ctx.font = '18px Arial';
                ctx.fillText('Performance Monitor - Perfmon', 60, 75);

                // 绘制CPU图表
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(80, 120, 200, 100);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText('CPU Usage', 85, 140);

                // 动态CPU使用率
                const cpuUsage = 50 + 30 * Math.sin(frame * 0.1);
                ctx.fillStyle = '#27ae60';
                ctx.fillRect(85, 220 - cpuUsage, 190, cpuUsage);
                ctx.fillStyle = 'black';
                ctx.fillText(Math.round(cpuUsage) + '%', 85, 240);

                // 绘制内存图表
                ctx.fillStyle = '#9b59b6';
                ctx.fillRect(300, 120, 200, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('Memory Usage', 305, 140);

                // 动态内存使用率
                const memUsage = 40 + 20 * Math.sin(frame * 0.08);
                ctx.fillStyle = '#f39c12';
                ctx.fillRect(305, 220 - memUsage, 190, memUsage);
                ctx.fillStyle = 'black';
                ctx.fillText(Math.round(memUsage) + '%', 305, 240);

                // 绘制网络图表
                ctx.fillStyle = '#1abc9c';
                ctx.fillRect(520, 120, 200, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('Network I/O', 525, 140);

                // 动态网络流量
                for (let i = 0; i < 50; i++) {
                    const height = 20 * Math.sin((frame + i * 5) * 0.2);
                    ctx.fillStyle = '#16a085';
                    ctx.fillRect(525 + i * 3.8, 200 - Math.abs(height), 2, Math.abs(height));
                }

                frame++;
                if (frame < 500) {
                    animationId = requestAnimationFrame(draw);
                }
            }
            draw();
        }

        // 题目相关功能
        let selectedOption = null;
        let answered = false;

        function selectOption(element, option) {
            if (answered) return;

            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // 选择当前选项
            element.classList.add('selected');
            selectedOption = option;
        }

        function checkAnswer() {
            if (!selectedOption) {
                alert('请先选择一个答案！');
                return;
            }

            answered = true;
            const options = document.querySelectorAll('.option');

            options.forEach(option => {
                const optionLetter = option.textContent.trim().charAt(0);
                if (optionLetter === 'C') {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && optionLetter !== 'C') {
                    option.classList.add('wrong');
                }
            });

            setTimeout(() => {
                if (selectedOption === 'C') {
                    updateScore(50);
                    alert('🎉 恭喜答对了！获得50积分！Perfmon是Windows的可视化性能监控工具。');
                } else {
                    alert('❌ 答案错误。正确答案是C：Windows的Perfmon。');
                }
            }, 1000);
        }

        function showDetailedExplanation() {
            const explanation = document.getElementById('detailedExplanation');
            explanation.style.display = explanation.style.display === 'none' ? 'block' : 'none';
            if (explanation.style.display === 'block') {
                updateScore(20);
            }
        }

        function resetQuiz() {
            answered = false;
            selectedOption = null;
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'wrong');
            });
            document.getElementById('detailedExplanation').style.display = 'none';
        }

        // 初始化
        createFloatingElements();

        // 默认显示第一个动画
        setTimeout(() => {
            showMethodAnimation(1);
        }, 1000);
    </script>
</body>
</html>
