<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内存地址计算 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .explanation {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            margin: 2px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            gap: 15px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            transform: scale(1.2);
        }

        .result-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-size: 1.3rem;
            margin: 20px 0;
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.5s ease;
        }

        .result-display.show {
            opacity: 1;
            transform: scale(1);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .interactive-demo {
            background: #f0f4ff;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .hex-input {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .hex-input input {
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1.2rem;
            text-align: center;
            font-family: 'Courier New', monospace;
            background: white;
            transition: border-color 0.3s ease;
        }

        .hex-input input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }

        .conversion-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .conversion-step {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .conversion-step:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🧠 内存地址计算 - 交互式学习</h1>

        <!-- 问题展示区 -->
        <div class="section">
            <h2 class="section-title">📝 题目分析</h2>
            <div class="explanation">
                <p><strong>题目：</strong>内存按字节编址，地址从<span class="highlight">B0000H</span>到<span class="highlight">DFFFFH</span>的内存，共有（ ）字节，若用存储容量为<span class="highlight">64k×4bit</span>的存储器芯片构成该内存空间，至少需要（ ）片。</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="problemCanvas" width="800" height="200"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startAnimation()">🎬 开始动画演示</button>
                <button class="btn btn-secondary" onclick="resetAnimation()">🔄 重置</button>
            </div>
        </div>

        <!-- 十六进制转换学习区 -->
        <div class="section">
            <h2 class="section-title">🔢 十六进制基础知识</h2>
            
            <div class="canvas-container">
                <canvas id="hexCanvas" width="800" height="300"></canvas>
            </div>
            
            <div class="interactive-demo">
                <h3 style="text-align: center; margin-bottom: 20px;">🎮 互动练习：十六进制转换</h3>
                <div class="hex-input">
                    <input type="text" id="hexInput" placeholder="输入十六进制数" maxlength="6">
                    <button class="btn btn-primary" onclick="convertHex()">转换</button>
                </div>
                <div id="conversionResult" class="result-display"></div>
            </div>
        </div>

        <!-- 地址计算区 -->
        <div class="section">
            <h2 class="section-title">🧮 地址范围计算</h2>
            
            <div class="step-indicator">
                <div class="step" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>
            
            <div class="canvas-container">
                <canvas id="calculationCanvas" width="900" height="400"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startCalculation()">🚀 开始计算</button>
                <button class="btn btn-secondary" onclick="nextStep()">➡️ 下一步</button>
            </div>
            
            <div class="conversion-steps">
                <div class="conversion-step">
                    <h4>步骤1: 理解地址范围</h4>
                    <p>起始地址：B0000H<br>结束地址：DFFFFH</p>
                </div>
                <div class="conversion-step">
                    <h4>步骤2: 计算地址差</h4>
                    <p>DFFFFH - B0000H + 1</p>
                </div>
                <div class="conversion-step">
                    <h4>步骤3: 十六进制运算</h4>
                    <p>= 30000H 个字节</p>
                </div>
                <div class="conversion-step">
                    <h4>步骤4: 转换为十进制</h4>
                    <p>= 192K 字节</p>
                </div>
            </div>
        </div>

        <!-- 存储器芯片计算区 -->
        <div class="section">
            <h2 class="section-title">💾 存储器芯片计算</h2>
            
            <div class="canvas-container">
                <canvas id="chipCanvas" width="800" height="350"></canvas>
            </div>
            
            <div class="explanation">
                <p><strong>关键概念：</strong></p>
                <ul style="margin-left: 20px; line-height: 2;">
                    <li>每个存储器芯片容量：<span class="highlight">64K × 4bit</span></li>
                    <li>总内存需求：<span class="highlight">192K × 8bit</span>（因为按字节编址，1字节=8bit）</li>
                    <li>所需芯片数 = 总容量 ÷ 单片容量</li>
                </ul>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="calculateChips()">🔧 计算芯片数量</button>
            </div>
            
            <div id="finalResult" class="result-display">
                <h3>🎉 最终答案</h3>
                <p>内存总容量：<strong>192K 字节</strong></p>
                <p>所需芯片数量：<strong>6 片</strong></p>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let animationRunning = false;

        // 初始化画布
        function initCanvases() {
            drawProblemVisualization();
            drawHexLearning();
            drawCalculationArea();
            drawChipVisualization();
        }

        // 绘制问题可视化
        function drawProblemVisualization() {
            const canvas = document.getElementById('problemCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制内存条
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(100, 50, 600, 100);
            
            // 添加标签
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('内存地址范围', 400, 30);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('B0000H', 150, 105);
            ctx.fillText('DFFFFH', 650, 105);
            
            // 绘制箭头
            drawArrow(ctx, 150, 130, 650, 130, '#333');

            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('需要计算这段内存的总字节数', 400, 180);
        }

        // 绘制十六进制学习区
        function drawHexLearning() {
            const canvas = document.getElementById('hexCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制十六进制对照表
            const hexDigits = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'];
            const decValues = [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15];

            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('十六进制 ↔ 十进制对照表', 400, 30);

            for(let i = 0; i < 16; i++) {
                const x = 50 + (i % 8) * 90;
                const y = i < 8 ? 80 : 150;

                // 绘制卡片背景
                const gradient = ctx.createLinearGradient(x-30, y-20, x+30, y+20);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');

                ctx.fillStyle = gradient;
                ctx.fillRect(x-30, y-20, 60, 40);

                // 添加文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(hexDigits[i], x, y-5);
                ctx.font = '12px Arial';
                ctx.fillText(decValues[i].toString(), x, y+10);
            }

            // 添加说明
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.fillText('点击上方卡片或在下方输入框中练习转换', 400, 220);

            // 绘制转换示例
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('示例：B0000H = 11×16⁴ + 0×16³ + 0×16² + 0×16¹ + 0×16⁰', 400, 260);
        }

        // 绘制计算区域
        function drawCalculationArea() {
            const canvas = document.getElementById('calculationCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制计算步骤框架
            const steps = [
                '起始地址: B0000H',
                '结束地址: DFFFFH',
                '地址差: DFFFFH - B0000H + 1',
                '结果: 30000H = 192K字节'
            ];

            for(let i = 0; i < steps.length; i++) {
                const x = 50 + i * 200;
                const y = 100;

                // 绘制步骤框
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, 180, 80);

                ctx.fillStyle = '#f8f9ff';
                ctx.fillRect(x, y, 180, 80);

                // 添加步骤文字
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';

                const lines = steps[i].split(': ');
                ctx.fillText(lines[0] + ':', x + 90, y + 30);
                if(lines[1]) {
                    ctx.font = 'bold 14px Arial';
                    ctx.fillText(lines[1], x + 90, y + 55);
                }
            }

            // 绘制连接箭头
            for(let i = 0; i < 3; i++) {
                drawArrow(ctx, 230 + i * 200, 140, 250 + i * 200, 140, '#667eea');
            }

            // 添加详细计算过程
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('详细计算过程：', 50, 220);
            ctx.fillText('1. DFFFFH - B0000H = 30000H - 1 = 2FFFFH', 70, 250);
            ctx.fillText('2. 2FFFFH + 1 = 30000H', 70, 270);
            ctx.fillText('3. 30000H = 3 × 16⁴ = 3 × 65536 = 196608 字节 = 192K字节', 70, 290);
        }

        // 绘制芯片可视化
        function drawChipVisualization() {
            const canvas = document.getElementById('chipCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制总内存需求
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('内存需求 vs 芯片容量', 400, 30);

            // 绘制总需求
            const gradient1 = ctx.createLinearGradient(50, 60, 350, 60);
            gradient1.addColorStop(0, '#ff6b6b');
            gradient1.addColorStop(1, '#ee5a24');

            ctx.fillStyle = gradient1;
            ctx.fillRect(50, 60, 300, 60);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('总需求: 192K × 8bit', 200, 95);

            // 绘制单个芯片
            for(let i = 0; i < 6; i++) {
                const x = 450 + (i % 3) * 110;
                const y = 60 + Math.floor(i / 3) * 80;

                const gradient2 = ctx.createLinearGradient(x, y, x + 80, y + 60);
                gradient2.addColorStop(0, '#667eea');
                gradient2.addColorStop(1, '#764ba2');

                ctx.fillStyle = gradient2;
                ctx.fillRect(x, y, 80, 60);

                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.fillText('64K×4bit', x + 40, y + 35);
            }

            // 添加计算公式
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('计算公式：', 50, 220);
            ctx.fillText('所需芯片数 = (192K × 8bit) ÷ (64K × 4bit) = 1536K ÷ 256K = 6片', 70, 250);

            // 绘制等号和箭头
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('=', 400, 95);
            drawArrow(ctx, 350, 120, 450, 120, '#333');
        }

        // 绘制箭头函数
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;

            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 绘制箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fill();
        }

        // 开始动画
        function startAnimation() {
            if(animationRunning) return;
            animationRunning = true;

            // 重置步骤指示器
            document.querySelectorAll('.step').forEach(step => step.classList.remove('active'));

            // 逐步激活步骤
            let step = 0;
            const interval = setInterval(() => {
                if(step < 4) {
                    document.getElementById(`step${step + 1}`).classList.add('active');
                    step++;
                } else {
                    clearInterval(interval);
                    animationRunning = false;
                    document.getElementById('finalResult').classList.add('show');
                }
            }, 1000);
        }

        // 重置动画
        function resetAnimation() {
            animationRunning = false;
            document.querySelectorAll('.step').forEach(step => step.classList.remove('active'));
            document.getElementById('finalResult').classList.remove('show');
            currentStep = 0;
        }

        // 十六进制转换
        function convertHex() {
            const input = document.getElementById('hexInput').value.toUpperCase();
            const resultDiv = document.getElementById('conversionResult');

            if(!input) {
                resultDiv.innerHTML = '<p>请输入一个十六进制数！</p>';
                resultDiv.classList.add('show');
                return;
            }

            try {
                const decimal = parseInt(input, 16);
                const binary = decimal.toString(2);

                resultDiv.innerHTML = `
                    <h4>转换结果</h4>
                    <p><strong>${input}H</strong> (十六进制)</p>
                    <p>= <strong>${decimal}</strong> (十进制)</p>
                    <p>= <strong>${binary}</strong> (二进制)</p>
                    <p>= <strong>${(decimal/1024).toFixed(2)}K</strong> (如果是字节数)</p>
                `;
                resultDiv.classList.add('show');
            } catch(e) {
                resultDiv.innerHTML = '<p>输入的不是有效的十六进制数！</p>';
                resultDiv.classList.add('show');
            }
        }

        // 开始计算
        function startCalculation() {
            currentStep = 0;
            nextStep();
        }

        // 下一步
        function nextStep() {
            if(currentStep < 4) {
                document.querySelectorAll('.step').forEach(step => step.classList.remove('active'));
                document.getElementById(`step${currentStep + 1}`).classList.add('active');

                // 更新计算画布显示当前步骤
                highlightCalculationStep(currentStep);
                currentStep++;
            }
        }

        // 高亮计算步骤
        function highlightCalculationStep(step) {
            const canvas = document.getElementById('calculationCanvas');
            const ctx = canvas.getContext('2d');

            // 重绘基础内容
            drawCalculationArea();

            // 高亮当前步骤
            const x = 50 + step * 200;
            const y = 100;

            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 4;
            ctx.strokeRect(x - 2, y - 2, 184, 84);

            // 添加闪烁效果
            ctx.shadowColor = '#ff6b6b';
            ctx.shadowBlur = 10;
            ctx.strokeRect(x - 2, y - 2, 184, 84);
            ctx.shadowBlur = 0;
        }

        // 计算芯片数量
        function calculateChips() {
            const canvas = document.getElementById('chipCanvas');
            const ctx = canvas.getContext('2d');

            // 重绘并添加动画效果
            drawChipVisualization();

            // 添加计算动画
            setTimeout(() => {
                ctx.fillStyle = 'rgba(255, 107, 107, 0.3)';
                ctx.fillRect(50, 60, 300, 60);

                setTimeout(() => {
                    for(let i = 0; i < 6; i++) {
                        setTimeout(() => {
                            const x = 450 + (i % 3) * 110;
                            const y = 60 + Math.floor(i / 3) * 80;

                            ctx.strokeStyle = '#ff6b6b';
                            ctx.lineWidth = 3;
                            ctx.strokeRect(x - 2, y - 2, 84, 64);
                        }, i * 200);
                    }
                }, 500);
            }, 300);

            // 显示最终结果
            setTimeout(() => {
                document.getElementById('finalResult').classList.add('show');
            }, 2000);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initCanvases();

            // 添加画布点击事件
            document.getElementById('hexCanvas').addEventListener('click', (e) => {
                const rect = e.target.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 检测点击的十六进制卡片
                for(let i = 0; i < 16; i++) {
                    const cardX = 50 + (i % 8) * 90;
                    const cardY = i < 8 ? 80 : 150;

                    if(x >= cardX - 30 && x <= cardX + 30 && y >= cardY - 20 && y <= cardY + 20) {
                        const hexDigits = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'];
                        document.getElementById('hexInput').value = hexDigits[i];
                        convertHex();
                        break;
                    }
                }
            });

            // 添加回车键支持
            document.getElementById('hexInput').addEventListener('keypress', (e) => {
                if(e.key === 'Enter') {
                    convertHex();
                }
            });
        });
    </script>
</body>
</html>
