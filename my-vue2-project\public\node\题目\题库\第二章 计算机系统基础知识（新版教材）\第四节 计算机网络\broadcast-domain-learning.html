<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络设备广播域隔离学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .question-box h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-align: center;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d4aa;
            background: rgba(0, 212, 170, 0.3);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.3);
            animation: shake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        canvas {
            width: 100%;
            height: 450px;
            border-radius: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            line-height: 1.6;
        }

        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .device-card {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .device-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(108, 92, 231, 0.3);
        }

        .device-card.router {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .device-card.bridge {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .device-card.switch {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .device-card.hub {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .device-card h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .device-card .icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 3px 8px;
            border-radius: 5px;
            color: #2d3436;
            font-weight: bold;
        }

        .step {
            background: rgba(116, 185, 255, 0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateX(5px);
        }

        .broadcast-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 107, 107, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .broadcast-indicator.blocked {
            background: rgba(0, 212, 170, 0.9);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes broadcastWave {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(3);
                opacity: 0;
            }
        }

        .broadcast-wave {
            animation: broadcastWave 2s ease-out infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #01a3a4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .concept-box {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
        }

        .concept-box h4 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 网络设备广播域隔离学习</h1>
            <p>探索不同网络设备对广播帧的处理方式</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2>📝 考试题目</h2>
                <p><strong>下列（ ）设备可以隔离广播帧。</strong></p>
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 路由器
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 网桥
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 以太网交换机
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 集线器
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 什么是广播域？</h2>
            <div class="explanation">
                <p><span class="highlight">广播域</span>是指网络中能够接收到同一广播消息的所有设备的集合。就像在一个大教室里，老师说话时所有学生都能听到，这个教室就是一个"广播域"。</p>
                <p>🔑 <strong>关键概念：</strong>广播帧会在同一个广播域内的所有设备间传播，但不能跨越广播域的边界。</p>
            </div>
            
            <div class="concept-box">
                <h4>📡 广播帧的特点</h4>
                <p>• 目标MAC地址为 FF:FF:FF:FF:FF:FF</p>
                <p>• 在同一广播域内所有设备都会收到</p>
                <p>• 用于ARP请求、DHCP发现等协议</p>
            </div>
        </div>

        <div class="section">
            <h2>🔧 网络设备对比</h2>
            <div class="device-grid">
                <div class="device-card router" onclick="showDeviceDemo('router')">
                    <div class="icon">🛡️</div>
                    <h3>路由器</h3>
                    <p><strong>网络层设备</strong></p>
                    <p>✅ 隔离广播域</p>
                    <p>工作在第3层</p>
                </div>
                <div class="device-card bridge" onclick="showDeviceDemo('bridge')">
                    <div class="icon">🌉</div>
                    <h3>网桥</h3>
                    <p><strong>数据链路层设备</strong></p>
                    <p>❌ 不隔离广播域</p>
                    <p>工作在第2层</p>
                </div>
                <div class="device-card switch" onclick="showDeviceDemo('switch')">
                    <div class="icon">🔀</div>
                    <h3>以太网交换机</h3>
                    <p><strong>数据链路层设备</strong></p>
                    <p>❌ 不隔离广播域</p>
                    <p>工作在第2层</p>
                </div>
                <div class="device-card hub" onclick="showDeviceDemo('hub')">
                    <div class="icon">⭐</div>
                    <h3>集线器</h3>
                    <p><strong>物理层设备</strong></p>
                    <p>❌ 不隔离广播域</p>
                    <p>工作在第1层</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 广播传播演示</h2>
            <div class="canvas-container">
                <canvas id="broadcastCanvas"></canvas>
                <div class="broadcast-indicator" id="broadcastStatus">
                    📡 广播传播中...
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="showRouterDemo()">🛡️ 路由器隔离演示</button>
                <button class="btn" onclick="showSwitchDemo()">🔀 交换机传播演示</button>
                <button class="btn" onclick="showHubDemo()">⭐ 集线器传播演示</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2>🔍 详细解析每个选项</h2>

            <div class="step">
                <h3>选项A：路由器 ✅</h3>
                <p><strong>正确答案！</strong></p>
                <ul>
                    <li>🛡️ <strong>工作层次：</strong>网络层（第3层）</li>
                    <li>🌐 <strong>功能：</strong>连接不同网络，每个接口是独立的广播域</li>
                    <li>🚫 <strong>广播处理：</strong>不转发广播帧，在接口处终止</li>
                    <li>📍 <strong>应用场景：</strong>连接不同子网，如***********/24和***********/24</li>
                </ul>
                <div class="concept-box">
                    <h4>🔑 关键原理</h4>
                    <p>路由器根据IP地址进行转发决策，广播帧的目标IP是本网段的广播地址，不会被路由到其他网段。</p>
                </div>
            </div>

            <div class="step">
                <h3>选项B：网桥 ❌</h3>
                <p><strong>错误原因：</strong></p>
                <ul>
                    <li>🌉 <strong>工作层次：</strong>数据链路层（第2层）</li>
                    <li>🔄 <strong>功能：</strong>连接两个网段，学习MAC地址</li>
                    <li>📡 <strong>广播处理：</strong>转发所有广播帧到所有端口</li>
                    <li>⚠️ <strong>局限性：</strong>无法隔离广播域，只能分割冲突域</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项C：以太网交换机 ❌</h3>
                <p><strong>错误原因：</strong></p>
                <ul>
                    <li>🔀 <strong>工作层次：</strong>数据链路层（第2层）</li>
                    <li>💾 <strong>功能：</strong>学习MAC地址，建立MAC地址表</li>
                    <li>📡 <strong>广播处理：</strong>泛洪广播帧到除接收端口外的所有端口</li>
                    <li>🏢 <strong>VLAN例外：</strong>三层交换机的VLAN功能可以分割广播域</li>
                </ul>
                <div class="concept-box">
                    <h4>💡 特殊情况</h4>
                    <p>虽然普通交换机不能隔离广播域，但支持VLAN的三层交换机可以创建虚拟广播域。</p>
                </div>
            </div>

            <div class="step">
                <h3>选项D：集线器 ❌</h3>
                <p><strong>错误原因：</strong></p>
                <ul>
                    <li>⭐ <strong>工作层次：</strong>物理层（第1层）</li>
                    <li>🔁 <strong>功能：</strong>简单的信号放大和重复</li>
                    <li>📡 <strong>广播处理：</strong>重复所有信号到所有端口</li>
                    <li>⚡ <strong>特点：</strong>所有端口共享同一个冲突域和广播域</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🧠 核心概念深度理解</h2>

            <div class="concept-box">
                <h4>🌐 广播域 vs 冲突域</h4>
                <p><strong>广播域：</strong>能够接收到同一广播消息的设备集合</p>
                <p><strong>冲突域：</strong>可能发生数据冲突的设备集合</p>
                <p><strong>关系：</strong>一个广播域可以包含多个冲突域</p>
            </div>

            <div class="concept-box">
                <h4>📡 广播帧的用途</h4>
                <p>• <strong>ARP请求：</strong>根据IP地址查找MAC地址</p>
                <p>• <strong>DHCP发现：</strong>客户端寻找DHCP服务器</p>
                <p>• <strong>网络发现：</strong>发现网络中的其他设备</p>
                <p>• <strong>协议通告：</strong>路由协议的邻居发现</p>
            </div>

            <div class="concept-box">
                <h4>🛡️ 为什么需要隔离广播域？</h4>
                <p>• <strong>减少网络拥塞：</strong>限制广播流量的传播范围</p>
                <p>• <strong>提高安全性：</strong>防止广播信息泄露到其他网段</p>
                <p>• <strong>优化性能：</strong>减少不必要的广播处理</p>
                <p>• <strong>网络分段：</strong>实现逻辑网络隔离</p>
            </div>
        </div>

        <div class="section">
            <h2>🎯 记忆技巧和考试要点</h2>

            <div class="explanation">
                <h3>🧠 记忆口诀</h3>
                <p style="font-size: 1.3rem; text-align: center; font-weight: bold; margin: 20px 0;">
                    "路由器隔离广播域，交换网桥都转发，<br>
                    集线器是中继器，广播冲突一起传"
                </p>

                <h3>🔢 层次记忆法</h3>
                <p><strong>第1层（物理层）：</strong>集线器 - 什么都不懂，全部重复</p>
                <p><strong>第2层（数据链路层）：</strong>交换机、网桥 - 懂MAC，转发广播</p>
                <p><strong>第3层（网络层）：</strong>路由器 - 懂IP，隔离广播域</p>

                <h3>🎯 考试技巧</h3>
                <ul>
                    <li>看到"隔离广播域"、"阻止广播帧" → <span class="highlight">路由器</span></li>
                    <li>看到"转发广播帧"、"泛洪广播" → <span class="highlight">交换机/网桥</span></li>
                    <li>看到"重复信号"、"共享介质" → <span class="highlight">集线器</span></li>
                    <li>记住：<span class="highlight">只有网络层设备才能隔离广播域</span></li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎉 学习总结</h2>
            <div class="explanation">
                <h3>📚 核心知识点</h3>
                <ul>
                    <li><span class="highlight">广播域</span>：能接收同一广播消息的设备集合</li>
                    <li><span class="highlight">路由器</span>：唯一能隔离广播域的设备（工作在第3层）</li>
                    <li><span class="highlight">交换机/网桥</span>：转发广播帧，不能隔离广播域</li>
                    <li><span class="highlight">集线器</span>：重复所有信号，共享广播域和冲突域</li>
                </ul>

                <h3>⚡ 实际应用</h3>
                <ul>
                    <li>企业网络使用路由器分割不同部门的网络</li>
                    <li>交换机在同一网段内提供高速交换</li>
                    <li>VLAN技术可以在交换机上创建虚拟广播域</li>
                    <li>现代网络很少使用集线器（已被交换机取代）</li>
                </ul>
            </div>

            <div class="controls">
                <button class="btn" onclick="reviewQuestion()">🔄 重新答题</button>
                <button class="btn" onclick="showSummary()">📋 显示总结</button>
            </div>
        </div>
    </div>

    <script>
        // Canvas相关变量
        const canvas = document.getElementById('broadcastCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;
        let currentDemo = 'none';

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 绘制计算机
        function drawComputer(x, y, label, color = '#3498db', broadcasting = false) {
            ctx.save();
            
            // 计算机主体
            ctx.fillStyle = color;
            ctx.fillRect(x - 20, y - 15, 40, 30);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 20, y - 15, 40, 30);
            
            // 屏幕
            ctx.fillStyle = '#fff';
            ctx.fillRect(x - 15, y - 10, 30, 20);
            
            // 广播波纹效果
            if (broadcasting) {
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 2;
                ctx.globalAlpha = 0.6;
                for (let i = 1; i <= 3; i++) {
                    ctx.beginPath();
                    ctx.arc(x, y, i * 15, 0, Math.PI * 2);
                    ctx.stroke();
                }
                ctx.globalAlpha = 1;
            }
            
            // 标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 45);
            
            ctx.restore();
        }

        // 绘制网络设备
        function drawNetworkDevice(x, y, type, label, color = '#e67e22') {
            ctx.save();
            
            // 设备主体
            ctx.fillStyle = color;
            if (type === 'router') {
                // 路由器 - 圆角矩形
                ctx.beginPath();
                ctx.roundRect(x - 30, y - 20, 60, 40, 10);
                ctx.fill();
                ctx.strokeStyle = '#2c3e50';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // 天线
                ctx.strokeStyle = '#2c3e50';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(x - 20, y - 20);
                ctx.lineTo(x - 20, y - 35);
                ctx.moveTo(x + 20, y - 20);
                ctx.lineTo(x + 20, y - 35);
                ctx.stroke();
            } else {
                // 其他设备 - 矩形
                ctx.fillRect(x - 30, y - 20, 60, 40);
                ctx.strokeStyle = '#2c3e50';
                ctx.lineWidth = 2;
                ctx.strokeRect(x - 30, y - 20, 60, 40);
            }
            
            // 设备图标
            ctx.fillStyle = '#fff';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            const icons = {
                'router': '🛡️',
                'switch': '🔀',
                'hub': '⭐',
                'bridge': '🌉'
            };
            ctx.fillText(icons[type] || '📦', x, y + 5);
            
            // 标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText(label, x, y + 55);
            
            ctx.restore();
        }

        // 题目交互逻辑
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n解释：路由器工作在网络层（第3层），可以隔离广播域。广播帧不会穿过路由器，每个路由器接口连接的网段都是独立的广播域。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    progressFill.style.width = '25%';
                    setTimeout(() => {
                        let hint = '';
                        switch(answer) {
                            case 'B':
                                hint = '网桥工作在数据链路层，会转发广播帧，不能隔离广播域。';
                                break;
                            case 'C':
                                hint = '以太网交换机工作在数据链路层，同一VLAN内的广播帧会被转发到所有端口。';
                                break;
                            case 'D':
                                hint = '集线器工作在物理层，只是简单的信号放大器，所有端口共享同一个冲突域和广播域。';
                                break;
                        }
                        alert('❌ 答案不正确！\n\n提示：' + hint + '\n\n记住：只有工作在网络层的设备才能隔离广播域！');
                    }, 500);
                }
            });
        });

        // 绘制连接线
        function drawConnection(fromX, fromY, toX, toY, active = false) {
            ctx.save();
            ctx.strokeStyle = active ? '#e74c3c' : '#bdc3c7';
            ctx.lineWidth = active ? 3 : 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            ctx.restore();
        }

        // 绘制广播帧
        function drawBroadcastFrame(x, y, blocked = false) {
            ctx.save();

            if (blocked) {
                // 被阻止的广播帧
                ctx.fillStyle = '#ff4757';
                ctx.strokeStyle = '#ff4757';
                ctx.lineWidth = 3;

                // 绘制X标记
                ctx.beginPath();
                ctx.moveTo(x - 10, y - 10);
                ctx.lineTo(x + 10, y + 10);
                ctx.moveTo(x + 10, y - 10);
                ctx.lineTo(x - 10, y + 10);
                ctx.stroke();

                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('❌ 被阻止', x, y + 25);
            } else {
                // 正常传播的广播帧
                ctx.fillStyle = '#e74c3c';
                ctx.beginPath();
                ctx.arc(x, y, 8, 0, Math.PI * 2);
                ctx.fill();

                // 广播波纹
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 2;
                ctx.globalAlpha = 0.5;
                for (let i = 1; i <= 2; i++) {
                    ctx.beginPath();
                    ctx.arc(x, y, i * 12, 0, Math.PI * 2);
                    ctx.stroke();
                }
                ctx.globalAlpha = 1;

                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('📡 广播帧', x, y + 25);
            }

            ctx.restore();
        }

        // 路由器隔离演示
        function showRouterDemo() {
            currentDemo = 'router';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const status = document.getElementById('broadcastStatus');
            status.textContent = '🛡️ 路由器隔离广播域';
            status.className = 'broadcast-indicator blocked';

            animateRouter();
        }

        function animateRouter() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 绘制路由器
            drawNetworkDevice(centerX, centerY, 'router', '路由器', '#00d4aa');

            // 左侧网段
            drawComputer(centerX - 150, centerY - 80, 'PC1', '#3498db', animationStep < 60);
            drawComputer(centerX - 150, centerY + 80, 'PC2', '#3498db');

            // 右侧网段
            drawComputer(centerX + 150, centerY - 80, 'PC3', '#9b59b6');
            drawComputer(centerX + 150, centerY + 80, 'PC4', '#9b59b6');

            // 连接线
            drawConnection(centerX - 130, centerY - 80, centerX - 30, centerY);
            drawConnection(centerX - 130, centerY + 80, centerX - 30, centerY);
            drawConnection(centerX + 30, centerY, centerX + 130, centerY - 80);
            drawConnection(centerX + 30, centerY, centerX + 130, centerY + 80);

            // 广播帧动画
            if (animationStep < 60) {
                // PC1发送广播
                drawBroadcastFrame(centerX - 100, centerY - 40);
            } else if (animationStep < 120) {
                // 广播到达路由器但被阻止
                drawBroadcastFrame(centerX - 50, centerY, true);
            }

            // 添加说明文字
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('广播域1 (***********/24)', centerX - 150, centerY + 130);
            ctx.fillText('广播域2 (***********/24)', centerX + 150, centerY + 130);

            if (animationStep >= 120) {
                ctx.fillStyle = '#00d4aa';
                ctx.font = '18px Microsoft YaHei';
                ctx.fillText('✅ 路由器成功隔离广播域！', centerX, 50);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateRouter);
        }

        // 交换机传播演示
        function showSwitchDemo() {
            currentDemo = 'switch';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const status = document.getElementById('broadcastStatus');
            status.textContent = '📡 交换机转发广播帧';
            status.className = 'broadcast-indicator';

            animateSwitch();
        }

        function animateSwitch() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 绘制交换机
            drawNetworkDevice(centerX, centerY, 'switch', '以太网交换机', '#fdcb6e');

            // 连接的计算机
            const computers = [
                {x: centerX - 120, y: centerY - 100, label: 'PC1', broadcasting: animationStep < 30},
                {x: centerX + 120, y: centerY - 100, label: 'PC2'},
                {x: centerX - 120, y: centerY + 100, label: 'PC3'},
                {x: centerX + 120, y: centerY + 100, label: 'PC4'}
            ];

            computers.forEach(pc => {
                drawComputer(pc.x, pc.y, pc.label, '#3498db', pc.broadcasting);
                drawConnection(pc.x, pc.y, centerX, centerY, animationStep > 30 && animationStep < 120);
            });

            // 广播帧传播动画
            if (animationStep >= 30 && animationStep < 90) {
                computers.slice(1).forEach((pc, index) => {
                    if (animationStep > 30 + index * 20) {
                        drawBroadcastFrame(pc.x, pc.y - 30);
                    }
                });
            }

            // 添加说明
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('同一个广播域', centerX, centerY + 150);

            if (animationStep >= 120) {
                ctx.fillStyle = '#fdcb6e';
                ctx.font = '18px Microsoft YaHei';
                ctx.fillText('📡 交换机转发广播帧到所有端口！', centerX, 50);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateSwitch);
        }

        // 集线器传播演示
        function showHubDemo() {
            currentDemo = 'hub';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const status = document.getElementById('broadcastStatus');
            status.textContent = '📡 集线器广播所有信号';
            status.className = 'broadcast-indicator';

            animateHub();
        }

        function animateHub() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 绘制集线器
            drawNetworkDevice(centerX, centerY, 'hub', '集线器', '#74b9ff');

            // 连接的计算机（星型拓扑）
            const angles = [0, Math.PI/2, Math.PI, 3*Math.PI/2];
            const radius = 120;

            angles.forEach((angle, index) => {
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                const broadcasting = index === 0 && animationStep < 30;

                drawComputer(x, y, `PC${index + 1}`, '#3498db', broadcasting);
                drawConnection(x, y, centerX, centerY, animationStep > 30);

                // 所有端口同时接收广播
                if (animationStep >= 30 && animationStep < 90 && index > 0) {
                    drawBroadcastFrame(x, y - 30);
                }
            });

            // 添加说明
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('共享冲突域和广播域', centerX, centerY + 180);

            if (animationStep >= 120) {
                ctx.fillStyle = '#74b9ff';
                ctx.font = '18px Microsoft YaHei';
                ctx.fillText('📡 集线器重复所有信号到所有端口！', centerX, 50);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateHub);
        }

        // 设备演示
        function showDeviceDemo(deviceType) {
            switch(deviceType) {
                case 'router':
                    showRouterDemo();
                    break;
                case 'switch':
                    showSwitchDemo();
                    break;
                case 'hub':
                    showHubDemo();
                    break;
                case 'bridge':
                    showSwitchDemo(); // 网桥行为类似交换机
                    break;
            }
        }

        // 重置演示
        function resetDemo() {
            if (animationId) cancelAnimationFrame(animationId);
            currentDemo = 'none';
            animationStep = 0;

            const status = document.getElementById('broadcastStatus');
            status.textContent = '📡 选择设备查看演示';
            status.className = 'broadcast-indicator';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制初始状态
            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('点击上方按钮查看不同设备的广播处理方式', centerX, centerY);
        }

        // 重新答题功能
        function reviewQuestion() {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            document.getElementById('progressFill').style.width = '0%';

            document.querySelector('.question-box').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            setTimeout(() => {
                document.querySelector('.question-box').classList.add('pulse');
                setTimeout(() => {
                    document.querySelector('.question-box').classList.remove('pulse');
                }, 2000);
            }, 500);
        }

        // 显示总结
        function showSummary() {
            const summary = `
🎯 网络设备广播域隔离学习总结

✅ 正确答案：A - 路由器

📚 核心概念：
• 广播域：能接收同一广播消息的设备集合
• 广播帧：目标MAC地址为FF:FF:FF:FF:FF:FF的帧

🔧 设备对比：
• 路由器（第3层）：✅ 隔离广播域
  - 每个接口是独立的广播域
  - 不转发广播帧到其他接口
  - 用于连接不同网络

• 交换机（第2层）：❌ 不隔离广播域
  - 转发广播帧到所有端口（除接收端口）
  - 学习MAC地址，建立转发表
  - VLAN功能可创建虚拟广播域

• 网桥（第2层）：❌ 不隔离广播域
  - 连接两个网段
  - 转发广播帧
  - 类似于两端口交换机

• 集线器（第1层）：❌ 不隔离广播域
  - 简单的信号重复器
  - 所有端口共享冲突域和广播域
  - 现代网络已很少使用

🧠 记忆技巧：
• "路由器隔离广播域，交换网桥都转发"
• 只有网络层设备才能隔离广播域
• 层次越高，功能越强，隔离能力越强

⚡ 考试要点：
• 看到"隔离广播域" → 路由器
• 看到"转发广播帧" → 交换机/网桥
• 看到"重复信号" → 集线器

🎉 恭喜掌握网络设备广播域处理机制！
            `;

            alert(summary);
        }

        // 添加CSS动画类
        const style = document.createElement('style');
        style.textContent = `
            .pulse {
                animation: pulse 1s ease-in-out 3;
            }

            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.02);
                    box-shadow: 0 25px 50px rgba(255, 107, 107, 0.4);
                }
            }
        `;
        document.head.appendChild(style);

        // 初始化
        resetDemo();

        // 页面加载完成后的欢迎提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                welcome.innerHTML = `
                    <h3>🌟 欢迎来到网络广播域学习世界！</h3>
                    <p>让我们一起探索网络设备如何处理广播帧</p>
                    <button onclick="this.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 15px;
                        margin-top: 15px;
                        cursor: pointer;
                    ">开始学习 🚀</button>
                `;
                document.body.appendChild(welcome);
            }, 1000);
        });
    </script>
</body>
</html>
