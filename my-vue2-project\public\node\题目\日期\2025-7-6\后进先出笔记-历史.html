<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后进先出笔记</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f4f4;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            color: #333;
        }

        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            box-sizing: border-box;
            margin-bottom: 20px;
        }

        h1 {
            color: #0056b3;
            text-align: center;
            margin-bottom: 20px;
        }

        textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1em;
            resize: vertical;
            min-height: 100px;
            box-sizing: border-box;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            width: 100%;
        }

        button:hover {
            background-color: #0056b3;
        }

        .notes-container {
            width: 100%;
            max-width: 600px;
            display: flex;
            flex-direction: column-reverse; /* This is key for LIFO! */
            gap: 15px;
        }

        .note-item {
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            word-wrap: break-word; /* Ensures long words wrap */
            white-space: pre-wrap; /* Preserves whitespace and line breaks */
        }

        .note-timestamp {
            font-size: 0.8em;
            color: #6c757d;
            text-align: right;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>后进先出笔记</h1>
        <textarea id="noteInput" placeholder="写下你的笔记..."></textarea>
        <button id="addNoteBtn">添加笔记</button>
    </div>

    <div class="notes-container" id="notesContainer">
        <!-- 笔记将在这里显示 -->
    </div>

    <script>
        const noteInput = document.getElementById('noteInput');
        const addNoteBtn = document.getElementById('addNoteBtn');
        const notesContainer = document.getElementById('notesContainer');

        // Function to format timestamp
        function formatTimestamp(date) {
            const options = {
                year: 'numeric', month: 'numeric', day: 'numeric',
                hour: '2-digit', minute: '2-digit', second: '2-digit'
            };
            return new Intl.DateTimeFormat('zh-CN', options).format(date);
        }

        addNoteBtn.addEventListener('click', () => {
            const noteText = noteInput.value.trim();

            if (noteText) {
                const noteItem = document.createElement('div');
                noteItem.classList.add('note-item');

                const timestamp = document.createElement('div');
                timestamp.classList.add('note-timestamp');
                timestamp.textContent = formatTimestamp(new Date());

                // Create a div for the note content
                const noteContent = document.createElement('div');
                noteContent.textContent = noteText;

                noteItem.appendChild(noteContent);
                noteItem.appendChild(timestamp);

                // Prepend the new note to the container to achieve LIFO
                // notesContainer.prepend(noteItem); // This is an alternative
                // With flex-direction: column-reverse, append also works as the order is reversed
                notesContainer.appendChild(noteItem);

                noteInput.value = ''; // Clear the input field
            } else {
                alert('笔记内容不能为空！');
            }
        });
    </script>
</body>
</html> 