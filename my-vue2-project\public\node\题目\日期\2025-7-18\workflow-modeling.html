<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流建模技术 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .game-board {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 40px;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 16px;
            border-left: 4px solid #667eea;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            background: white;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .option-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .option-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .option-card.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            transform: scale(1.05);
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            animation: shake 0.5s ease-in-out;
        }

        .option-card.workflow-method {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
        }

        .option-letter {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 12px;
        }

        .option-text {
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease-out;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .explanation h3 {
            color: #1976d2;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .workflow-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .method-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            text-align: center;
            cursor: pointer;
        }

        .method-card:hover {
            transform: translateY(-5px);
        }

        .method-card.can-describe {
            border: 2px solid #4CAF50;
        }

        .method-card.cannot-describe {
            border: 2px solid #f44336;
        }

        .method-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .activity-diagram { background: linear-gradient(135deg, #4CAF50, #45a049); }
        .bpmn { background: linear-gradient(135deg, #2196F3, #1976D2); }
        .use-case { background: linear-gradient(135deg, #f44336, #d32f2f); }
        .petri-net { background: linear-gradient(135deg, #FF9800, #F57C00); }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes workflowFlow {
            0% { transform: translateX(-20px); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(20px); opacity: 0; }
        }

        .workflow-particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            animation: workflowFlow 2s ease-in-out infinite;
        }

        .comparison-table {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            overflow-x: auto;
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }

        .can-workflow {
            color: #4CAF50;
            font-weight: bold;
        }

        .cannot-workflow {
            color: #f44336;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔄 工作流建模技术</h1>
            <p class="subtitle">探索业务过程模型的图形化描述方法</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-text">
                    <strong>题目：</strong>工作流表示的是业务过程模型，通常使用图形形式来描述，以下不可用来描述工作流的是（ ）
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="700" height="400"></canvas>
            </div>

            <div class="options-grid">
                <div class="option-card" data-answer="A">
                    <div class="option-letter">A</div>
                    <div class="option-text">活动图</div>
                </div>
                <div class="option-card" data-answer="B">
                    <div class="option-letter">B</div>
                    <div class="option-text">BPMN</div>
                </div>
                <div class="option-card" data-answer="C">
                    <div class="option-letter">C</div>
                    <div class="option-text">用例图</div>
                </div>
                <div class="option-card" data-answer="D">
                    <div class="option-letter">D</div>
                    <div class="option-text">Petri-Net</div>
                </div>
            </div>

            <div class="interactive-demo">
                <h4>🎮 互动演示：工作流建模方法对比</h4>
                <button class="demo-button" onclick="demoActivityDiagram()">活动图演示</button>
                <button class="demo-button" onclick="demoBPMN()">BPMN演示</button>
                <button class="demo-button" onclick="demoUseCase()">用例图演示</button>
                <button class="demo-button" onclick="demoPetriNet()">Petri网演示</button>
            </div>

            <div class="explanation" id="explanation">
                <h3>🎉 正确答案：C - 用例图</h3>
                <p><strong>解析：</strong>用例图主要用于描述系统功能需求和用户交互，不适合描述业务过程的工作流。</p>
                
                <div class="workflow-methods">
                    <div class="method-card can-describe">
                        <div class="method-icon activity-diagram">📊</div>
                        <h4>活动图 ✓</h4>
                        <p>UML活动图专门用于描述业务流程和工作流，展示活动的顺序和并发关系</p>
                    </div>
                    <div class="method-card can-describe">
                        <div class="method-icon bpmn">🔄</div>
                        <h4>BPMN ✓</h4>
                        <p>业务流程建模标记法，专为业务流程建模设计的图形化标准</p>
                    </div>
                    <div class="method-card cannot-describe">
                        <div class="method-icon use-case">👤</div>
                        <h4>用例图 ✗</h4>
                        <p>描述系统功能和用户交互，关注"做什么"而非"怎么做"</p>
                    </div>
                    <div class="method-card can-describe">
                        <div class="method-icon petri-net">🕸️</div>
                        <h4>Petri网 ✓</h4>
                        <p>数学建模工具，可以精确描述并发、同步等复杂工作流特性</p>
                    </div>
                </div>

                <div class="comparison-table">
                    <table>
                        <thead>
                            <tr>
                                <th>建模方法</th>
                                <th>主要用途</th>
                                <th>能否描述工作流</th>
                                <th>特点</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>活动图</td>
                                <td>业务流程建模</td>
                                <td class="can-workflow">✓ 可以</td>
                                <td>展示活动顺序、分支、并发</td>
                            </tr>
                            <tr>
                                <td>BPMN</td>
                                <td>业务流程标准化建模</td>
                                <td class="can-workflow">✓ 可以</td>
                                <td>国际标准，专业工作流建模</td>
                            </tr>
                            <tr>
                                <td>用例图</td>
                                <td>功能需求分析</td>
                                <td class="cannot-workflow">✗ 不可以</td>
                                <td>描述系统功能，不涉及流程</td>
                            </tr>
                            <tr>
                                <td>Petri网</td>
                                <td>并发系统建模</td>
                                <td class="can-workflow">✓ 可以</td>
                                <td>数学严谨，支持复杂并发</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const options = document.querySelectorAll('.option-card');
        const explanation = document.getElementById('explanation');

        let animationFrame;
        let particles = [];
        let currentDemo = null;
        let workflowAnimation = false;

        // 初始化画布
        function initCanvas() {
            drawWorkflowComparison();
            animate();
        }

        function drawWorkflowComparison() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制四种建模方法
            drawModelingMethod(50, 50, 140, 100, '活动图', '#4CAF50', '📊', true);
            drawModelingMethod(220, 50, 140, 100, 'BPMN', '#2196F3', '🔄', true);
            drawModelingMethod(390, 50, 140, 100, '用例图', '#f44336', '👤', false);
            drawModelingMethod(560, 50, 140, 100, 'Petri网', '#FF9800', '🕸️', true);

            // 绘制工作流示例
            drawWorkflowExample();
            
            // 绘制说明文字
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('工作流建模方法对比', canvas.width/2, 30);
            
            // 绘制当前演示
            if (currentDemo) {
                drawCurrentDemo();
            }
        }

        function drawModelingMethod(x, y, width, height, name, color, icon, canDescribeWorkflow) {
            // 绘制背景
            ctx.fillStyle = canDescribeWorkflow ? color : '#f44336';
            ctx.globalAlpha = 0.1;
            ctx.fillRect(x, y, width, height);
            ctx.globalAlpha = 1;
            
            // 绘制边框
            ctx.strokeStyle = canDescribeWorkflow ? color : '#f44336';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            
            // 绘制图标
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(icon, x + width/2, y + 35);
            
            // 绘制名称
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 14px Arial';
            ctx.fillText(name, x + width/2, y + 55);
            
            // 绘制状态
            ctx.font = '12px Arial';
            ctx.fillStyle = canDescribeWorkflow ? '#4CAF50' : '#f44336';
            ctx.fillText(canDescribeWorkflow ? '✓ 可描述工作流' : '✗ 不可描述工作流', x + width/2, y + 75);
        }

        function drawWorkflowExample() {
            // 绘制简单的工作流示例
            const startY = 200;
            const stepWidth = 100;
            const stepHeight = 60;
            
            // 绘制工作流步骤
            const steps = ['开始', '审批', '执行', '结束'];
            steps.forEach((step, index) => {
                const x = 100 + index * 150;
                const y = startY;
                
                // 绘制步骤框
                ctx.fillStyle = '#e3f2fd';
                ctx.fillRect(x, y, stepWidth, stepHeight);
                ctx.strokeStyle = '#2196F3';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, stepWidth, stepHeight);
                
                // 绘制步骤文字
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(step, x + stepWidth/2, y + stepHeight/2 + 5);
                
                // 绘制箭头
                if (index < steps.length - 1) {
                    drawArrow(x + stepWidth, y + stepHeight/2, x + 150, y + stepHeight/2, '#2196F3');
                }
            });
            
            // 绘制说明
            ctx.fillStyle = '#666';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('典型工作流示例：业务过程的顺序执行', canvas.width/2, startY + stepHeight + 30);
        }

        function drawArrow(x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 绘制箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle - Math.PI/6), y2 - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle + Math.PI/6), y2 - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function drawCurrentDemo() {
            if (!currentDemo) return;
            
            // 高亮当前演示的方法
            const positions = {
                'activity': {x: 50, y: 50},
                'bpmn': {x: 220, y: 50},
                'usecase': {x: 390, y: 50},
                'petri': {x: 560, y: 50}
            };
            
            const pos = positions[currentDemo];
            if (pos) {
                ctx.strokeStyle = '#FFD700';
                ctx.lineWidth = 4;
                ctx.strokeRect(pos.x - 2, pos.y - 2, 144, 104);
                
                // 绘制闪烁效果
                const time = Date.now() * 0.005;
                ctx.globalAlpha = 0.5 + 0.3 * Math.sin(time);
                ctx.fillStyle = '#FFD700';
                ctx.fillRect(pos.x - 2, pos.y - 2, 144, 104);
                ctx.globalAlpha = 1;
            }
        }

        function createParticles(x, y, color) {
            for (let i = 0; i < 15; i++) {
                particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 8,
                    vy: (Math.random() - 0.5) * 8,
                    life: 1,
                    color: color
                });
            }
        }

        function updateParticles() {
            particles = particles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life -= 0.02;
                particle.vy += 0.1;
                
                if (particle.life > 0) {
                    ctx.save();
                    ctx.globalAlpha = particle.life;
                    ctx.fillStyle = particle.color;
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                    return true;
                }
                return false;
            });
        }

        function animate() {
            drawWorkflowComparison();
            updateParticles();
            animationFrame = requestAnimationFrame(animate);
        }

        // 选项点击事件
        options.forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                
                options.forEach(opt => {
                    opt.classList.remove('correct', 'wrong', 'workflow-method');
                });
                
                // 标记工作流方法
                if (answer === 'A' || answer === 'B' || answer === 'D') {
                    this.classList.add('workflow-method');
                }
                
                if (answer === 'C') {
                    this.classList.add('correct');
                    explanation.classList.add('show');
                    createParticles(canvas.width/2, canvas.height/2, '#4CAF50');
                } else {
                    this.classList.add('wrong');
                    createParticles(canvas.width/2, canvas.height/2, '#f44336');
                }
            });
        });

        // 演示函数
        function demoActivityDiagram() {
            currentDemo = 'activity';
            createParticles(120, 100, '#4CAF50');
            showTooltip('活动图：UML图，专门描述业务流程和活动顺序');
            setTimeout(() => currentDemo = null, 3000);
        }

        function demoBPMN() {
            currentDemo = 'bpmn';
            createParticles(290, 100, '#2196F3');
            showTooltip('BPMN：业务流程建模标记法，工作流建模的国际标准');
            setTimeout(() => currentDemo = null, 3000);
        }

        function demoUseCase() {
            currentDemo = 'usecase';
            createParticles(460, 100, '#f44336');
            showTooltip('用例图：描述系统功能需求，不涉及业务流程的执行顺序');
            setTimeout(() => currentDemo = null, 3000);
        }

        function demoPetriNet() {
            currentDemo = 'petri';
            createParticles(630, 100, '#FF9800');
            showTooltip('Petri网：数学建模工具，可精确描述并发和同步的工作流');
            setTimeout(() => currentDemo = null, 3000);
        }

        function showTooltip(text) {
            const tooltip = document.createElement('div');
            tooltip.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeInOut 3s ease-in-out;
                max-width: 350px;
                text-align: center;
            `;
            tooltip.textContent = text;
            document.body.appendChild(tooltip);
            
            setTimeout(() => {
                if (document.body.contains(tooltip)) {
                    document.body.removeChild(tooltip);
                }
            }, 3000);
        }

        // Canvas点击交互
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            if (x >= 50 && x <= 190 && y >= 50 && y <= 150) {
                demoActivityDiagram();
            } else if (x >= 220 && x <= 360 && y >= 50 && y <= 150) {
                demoBPMN();
            } else if (x >= 390 && x <= 530 && y >= 50 && y <= 150) {
                demoUseCase();
            } else if (x >= 560 && x <= 700 && y >= 50 && y <= 150) {
                demoPetriNet();
            }
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            }
        `;
        document.head.appendChild(style);

        // 初始化
        initCanvas();
    </script>
</body>
</html>
