<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件系统索引节点演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .section-title {
            color: #3498db;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        canvas {
            display: block;
            margin: 20px auto;
            border: 1px solid #ddd;
            background-color: white;
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 15px 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .explanation {
            background-color: #e8f4fc;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .quiz {
            margin-top: 30px;
            background-color: #f0f8ff;
            padding: 20px;
            border-radius: 5px;
        }
        .quiz-options {
            margin-top: 15px;
        }
        .quiz-option {
            margin: 10px 0;
            padding: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .quiz-option:hover {
            background-color: #f0f0f0;
        }
        .selected {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .correct {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .incorrect {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文件系统索引节点演示</h1>

        <div class="section">
            <h2 class="section-title">索引节点基本概念</h2>
            <p>索引节点(inode)是文件系统中用于存储文件元数据的数据结构，每个文件都对应一个索引节点。索引节点包含了文件的各种属性信息以及指向文件数据的指针。</p>
            <div class="explanation">
                <p>在本例中，索引节点有8个地址项(addr[0]~addr[7])：</p>
                <ul>
                    <li>addr[0]~addr[5]：直接地址索引，每项指向4KB数据</li>
                    <li>addr[6]：一级间接地址索引，指向地址表</li>
                    <li>addr[7]：二级间接地址索引，指向地址表的地址表</li>
                </ul>
            </div>
            <canvas id="inodeCanvas" width="800" height="400"></canvas>
            <div class="controls">
                <button id="showDirectBtn">演示直接索引</button>
                <button id="showIndirectBtn">演示一级间接索引</button>
                <button id="showDoubleIndirectBtn">演示二级间接索引</button>
                <button id="resetBtn">重置</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">文件大小计算</h2>
            <p>文件系统能表示的单个文件最大长度取决于索引节点的结构。在我们的例子中：</p>
            <canvas id="calculationCanvas" width="800" height="300"></canvas>
            <div class="controls">
                <button id="calcBtn">计算最大文件大小</button>
            </div>
            <div class="explanation" id="calcExplanation">
                <p>点击上方按钮查看计算过程</p>
            </div>
        </div>

        <div class="quiz">
            <h2 class="section-title">测试你的理解</h2>
            <p>假设文件索引节点中有8个地址项addr[0]~addr[7]，每个地址项大小为4字节，其中地址项addr[0]~addr[5]为直接地址索引，addr[6]是一级间接地址索引，addr[7]是二级间接地址索引，磁盘索引块和磁盘数据块大小均为4KB。该文件系统可表示的单个文件最大长度是：</p>
            <div class="quiz-options">
                <div class="quiz-option" data-value="1030">A. 1030 KB</div>
                <div class="quiz-option" data-value="65796">B. 65796 KB</div>
                <div class="quiz-option" data-value="1049606">C. 1049606 KB</div>
                <div class="quiz-option" data-value="4198424">D. 4198424 KB</div>
            </div>
            <div class="result"></div>
        </div>
    </div>

    <script>
        // 获取Canvas元素
        const inodeCanvas = document.getElementById('inodeCanvas');
        const inodeCtx = inodeCanvas.getContext('2d');
        const calcCanvas = document.getElementById('calculationCanvas');
        const calcCtx = calcCanvas.getContext('2d');

        // 颜色定义
        const colors = {
            inode: '#3498db',
            directBlock: '#2ecc71',
            indirectBlock: '#e74c3c',
            doubleIndirectBlock: '#9b59b6',
            pointer: '#f39c12',
            text: '#333333',
            highlight: '#ff0000'
        };

        // 绘制索引节点基本结构
        function drawInode() {
            inodeCtx.clearRect(0, 0, inodeCanvas.width, inodeCanvas.height);
            
            // 绘制索引节点
            inodeCtx.fillStyle = colors.inode;
            inodeCtx.fillRect(50, 100, 120, 200);
            
            // 绘制索引节点标题
            inodeCtx.fillStyle = colors.text;
            inodeCtx.font = '16px Arial';
            inodeCtx.fillText('索引节点', 70, 90);
            
            // 绘制地址项
            inodeCtx.font = '14px Arial';
            for (let i = 0; i < 8; i++) {
                inodeCtx.fillStyle = i < 6 ? colors.directBlock : (i === 6 ? colors.indirectBlock : colors.doubleIndirectBlock);
                inodeCtx.fillRect(60, 110 + i * 22, 100, 18);
                inodeCtx.fillStyle = 'white';
                inodeCtx.fillText(`addr[${i}]`, 85, 124 + i * 22);
            }
            
            // 添加说明
            inodeCtx.fillStyle = colors.text;
            inodeCtx.fillText('直接索引 (0-5)', 180, 140);
            inodeCtx.fillText('一级间接索引 (6)', 180, 240);
            inodeCtx.fillText('二级间接索引 (7)', 180, 270);
        }

        // 演示直接索引
        function showDirectIndexing() {
            drawInode();
            
            // 高亮直接索引项
            for (let i = 0; i < 6; i++) {
                inodeCtx.fillStyle = colors.highlight;
                inodeCtx.strokeStyle = colors.highlight;
                inodeCtx.lineWidth = 2;
                inodeCtx.strokeRect(60, 110 + i * 22, 100, 18);
                
                // 绘制数据块
                inodeCtx.fillStyle = colors.directBlock;
                inodeCtx.fillRect(300 + (i % 3) * 150, 80 + Math.floor(i / 3) * 100, 100, 80);
                
                // 绘制指针
                inodeCtx.beginPath();
                inodeCtx.moveTo(160, 119 + i * 22);
                inodeCtx.lineTo(300 + (i % 3) * 150, 120 + Math.floor(i / 3) * 100);
                inodeCtx.stroke();
                
                // 添加标签
                inodeCtx.fillStyle = colors.text;
                inodeCtx.fillText(`4KB 数据块`, 310 + (i % 3) * 150, 120 + Math.floor(i / 3) * 100);
            }
            
            // 添加说明
            inodeCtx.fillStyle = colors.text;
            inodeCtx.font = '16px Arial';
            inodeCtx.fillText('直接索引：每个地址项直接指向一个4KB的数据块', 300, 350);
            inodeCtx.fillText('最多可以索引 6 × 4KB = 24KB 的数据', 300, 380);
        }

        // 演示一级间接索引
        function showIndirectIndexing() {
            drawInode();
            
            // 高亮一级间接索引项
            inodeCtx.fillStyle = colors.highlight;
            inodeCtx.strokeStyle = colors.highlight;
            inodeCtx.lineWidth = 2;
            inodeCtx.strokeRect(60, 110 + 6 * 22, 100, 18);
            
            // 绘制间接索引块
            inodeCtx.fillStyle = colors.indirectBlock;
            inodeCtx.fillRect(250, 150, 120, 120);
            
            // 绘制指针
            inodeCtx.beginPath();
            inodeCtx.moveTo(160, 119 + 6 * 22);
            inodeCtx.lineTo(250, 200);
            inodeCtx.stroke();
            
            // 添加标签
            inodeCtx.fillStyle = colors.text;
            inodeCtx.fillText('间接索引块', 270, 140);
            inodeCtx.fillText('(4KB)', 290, 210);
            
            // 绘制索引项
            for (let i = 0; i < 5; i++) {
                inodeCtx.fillStyle = colors.pointer;
                inodeCtx.fillRect(260, 160 + i * 20, 100, 15);
                
                if (i < 3) {
                    // 绘制数据块
                    inodeCtx.fillStyle = colors.directBlock;
                    inodeCtx.fillRect(450, 120 + i * 60, 100, 50);
                    
                    // 绘制指针
                    inodeCtx.beginPath();
                    inodeCtx.moveTo(360, 167 + i * 20);
                    inodeCtx.lineTo(450, 145 + i * 60);
                    inodeCtx.stroke();
                    
                    // 添加标签
                    inodeCtx.fillStyle = colors.text;
                    inodeCtx.fillText(`4KB 数据块`, 460, 145 + i * 60);
                } else if (i === 3) {
                    inodeCtx.fillStyle = colors.text;
                    inodeCtx.fillText('...', 310, 167 + i * 20);
                }
            }
            
            // 添加说明
            inodeCtx.fillStyle = colors.text;
            inodeCtx.font = '16px Arial';
            inodeCtx.fillText('一级间接索引：地址项指向一个索引块，索引块中包含多个指向数据块的指针', 250, 350);
            inodeCtx.fillText('每个索引块大小为4KB，每个指针大小为4B，所以一个索引块可以包含 4KB/4B = 1024 个指针', 250, 380);
            inodeCtx.fillText('一级间接索引可以索引 1024 × 4KB = 4096KB 的数据', 250, 410);
        }

        // 演示二级间接索引
        function showDoubleIndirectIndexing() {
            drawInode();
            
            // 高亮二级间接索引项
            inodeCtx.fillStyle = colors.highlight;
            inodeCtx.strokeStyle = colors.highlight;
            inodeCtx.lineWidth = 2;
            inodeCtx.strokeRect(60, 110 + 7 * 22, 100, 18);
            
            // 绘制二级间接索引块
            inodeCtx.fillStyle = colors.doubleIndirectBlock;
            inodeCtx.fillRect(250, 200, 100, 100);
            
            // 绘制指针
            inodeCtx.beginPath();
            inodeCtx.moveTo(160, 119 + 7 * 22);
            inodeCtx.lineTo(250, 250);
            inodeCtx.stroke();
            
            // 添加标签
            inodeCtx.fillStyle = colors.text;
            inodeCtx.fillText('二级间接', 260, 190);
            inodeCtx.fillText('索引块', 270, 210);
            
            // 绘制一级索引块
            inodeCtx.fillStyle = colors.indirectBlock;
            inodeCtx.fillRect(400, 150, 80, 80);
            
            // 绘制指针
            inodeCtx.beginPath();
            inodeCtx.moveTo(350, 230);
            inodeCtx.lineTo(400, 190);
            inodeCtx.stroke();
            
            // 添加标签
            inodeCtx.fillStyle = colors.text;
            inodeCtx.fillText('一级索引块', 400, 140);
            
            // 绘制数据块
            inodeCtx.fillStyle = colors.directBlock;
            inodeCtx.fillRect(550, 150, 80, 80);
            
            // 绘制指针
            inodeCtx.beginPath();
            inodeCtx.moveTo(480, 190);
            inodeCtx.lineTo(550, 190);
            inodeCtx.stroke();
            
            // 添加标签
            inodeCtx.fillStyle = colors.text;
            inodeCtx.fillText('数据块', 565, 140);
            
            // 添加说明
            inodeCtx.fillStyle = colors.text;
            inodeCtx.font = '16px Arial';
            inodeCtx.fillText('二级间接索引：地址项指向一个索引块，该索引块指向多个一级索引块，每个一级索引块又指向多个数据块', 200, 350);
            inodeCtx.fillText('一个二级索引块可以包含 1024 个指向一级索引块的指针', 200, 380);
            inodeCtx.fillText('每个一级索引块可以包含 1024 个指向数据块的指针', 200, 410);
            inodeCtx.fillText('二级间接索引可以索引 1024 × 1024 × 4KB = 4194304KB 的数据', 200, 440);
        }

        // 计算最大文件大小
        function calculateMaxFileSize() {
            calcCtx.clearRect(0, 0, calcCanvas.width, calcCanvas.height);
            
            // 设置字体
            calcCtx.font = '16px Arial';
            calcCtx.fillStyle = colors.text;
            
            // 绘制计算过程
            let y = 30;
            calcCtx.fillText('文件系统可表示的单个文件最大长度计算：', 20, y);
            y += 40;
            
            // 直接索引部分
            calcCtx.fillText('1. 直接索引部分：6 × 4KB = 24KB', 40, y);
            y += 30;
            
            // 一级间接索引部分
            calcCtx.fillText('2. 一级间接索引部分：(4KB/4B) × 4KB = 1024 × 4KB = 4096KB', 40, y);
            y += 30;
            
            // 二级间接索引部分
            calcCtx.fillText('3. 二级间接索引部分：(4KB/4B) × (4KB/4B) × 4KB = 1024 × 1024 × 4KB = 4194304KB', 40, y);
            y += 30;
            
            // 总大小
            calcCtx.fillText('4. 总大小 = 24KB + 4096KB + 4194304KB = 4198424KB', 40, y);
            y += 40;
            
            // 结论
            calcCtx.fillStyle = colors.highlight;
            calcCtx.fillText('因此，该文件系统可表示的单个文件最大长度为 4198424KB', 40, y);
            
            // 更新解释文本
            document.getElementById('calcExplanation').innerHTML = `
                <p><strong>计算过程：</strong></p>
                <p>1. 直接索引：6个地址项，每个指向4KB数据 = 6 × 4KB = 24KB</p>
                <p>2. 一级间接索引：1个地址项指向一个4KB的索引块，每个索引块可存储4KB/4B=1024个指针，每个指针指向4KB数据 = 1024 × 4KB = 4096KB</p>
                <p>3. 二级间接索引：1个地址项指向一个4KB的索引块，该索引块有1024个指针，每个指针指向一个一级索引块，每个一级索引块又有1024个指针指向4KB数据 = 1024 × 1024 × 4KB = 4194304KB</p>
                <p>4. 总大小 = 24KB + 4096KB + 4194304KB = 4198424KB</p>
            `;
        }

        // 初始化
        drawInode();

        // 添加按钮事件监听
        document.getElementById('showDirectBtn').addEventListener('click', showDirectIndexing);
        document.getElementById('showIndirectBtn').addEventListener('click', showIndirectIndexing);
        document.getElementById('showDoubleIndirectBtn').addEventListener('click', showDoubleIndirectIndexing);
        document.getElementById('resetBtn').addEventListener('click', drawInode);
        document.getElementById('calcBtn').addEventListener('click', calculateMaxFileSize);

        // 添加测验选项事件监听
        document.querySelectorAll('.quiz-option').forEach(option => {
            option.addEventListener('click', function() {
                // 清除之前的选择
                document.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.classList.remove('selected', 'correct', 'incorrect');
                });
                
                // 添加新的选择
                this.classList.add('selected');
                
                // 检查答案
                const selectedValue = this.getAttribute('data-value');
                const resultElement = document.querySelector('.result');
                
                if (selectedValue === '4198424') {
                    this.classList.add('correct');
                    resultElement.innerHTML = '正确！文件系统可表示的单个文件最大长度为 4198424KB。';
                    resultElement.style.backgroundColor = '#d4edda';
                    resultElement.style.color = '#155724';
                } else {
                    this.classList.add('incorrect');
                    resultElement.innerHTML = '不正确，请再试一次。提示：需要计算直接索引、一级间接索引和二级间接索引可以表示的总大小。';
                    resultElement.style.backgroundColor = '#f8d7da';
                    resultElement.style.color = '#721c24';
                }
                
                resultElement.style.display = 'block';
            });
        });
    </script>
</body>
</html> 