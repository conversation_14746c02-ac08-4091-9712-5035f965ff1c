<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件方法学趣味学习</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700&display=swap');

        :root {
            --primary-color: #4CAF50; /* 绿色系 */
            --secondary-color: #2196F3; /* 蓝色系 */
            --background-color: #F8F8F8;
            --text-color: #333;
            --light-text-color: #666;
            --border-color: #E0E0E0;
            --card-background: #FFFFFF;
            --shadow-light: 0 4px 8px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 8px 16px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            overflow-x: hidden;
        }

        header {
            background-color: var(--card-background);
            padding: 20px 0;
            box-shadow: var(--shadow-light);
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        header h1 {
            margin: 0;
            color: var(--primary-color);
            font-weight: 700;
            letter-spacing: 1px;
            font-size: 2.5em;
        }

        .container {
            max-width: 1000px;
            margin: 40px auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: 1fr;
            gap: 40px;
            flex-grow: 1;
        }

        .card {
            background-color: var(--card-background);
            border-radius: 12px;
            box-shadow: var(--shadow-medium);
            padding: 30px;
            transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        h2 {
            color: var(--secondary-color);
            font-size: 2em;
            margin-top: 0;
            margin-bottom: 25px;
            text-align: center;
            font-weight: 700;
        }

        p {
            margin-bottom: 15px;
        }

        .question-section .question-text {
            font-size: 1.2em;
            font-weight: 500;
            margin-bottom: 25px;
            color: var(--text-color);
        }

        .options label {
            display: block;
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            padding: 15px 20px;
            margin-bottom: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1em;
            display: flex;
            align-items: center;
        }

        .options label:hover {
            background-color: #EFEFEF;
            border-color: var(--secondary-color);
            transform: translateX(5px);
        }

        .options input[type="radio"] {
            margin-right: 15px;
            transform: scale(1.3);
            accent-color: var(--primary-color);
        }

        .options input[type="radio"]:checked + span {
            color: var(--primary-color);
            font-weight: 700;
        }

        .btn-check-answer {
            display: block;
            width: fit-content;
            margin: 30px auto 0;
            padding: 15px 30px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: var(--shadow-light);
        }

        .btn-check-answer:hover {
            background-color: #43A047; /* 更深的绿色 */
            transform: translateY(-2px);
        }

        .answer-feedback {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            font-size: 1.1em;
            text-align: center;
            display: none; /* 初始隐藏 */
            animation: fadeIn 0.5s ease-out;
        }

        .answer-feedback.correct {
            background-color: #E8F5E9; /* 浅绿色背景 */
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .answer-feedback.incorrect {
            background-color: #FFEBEE; /* 浅红色背景 */
            color: #D32F2F; /* 红色 */
            border: 1px solid #D32F2F;
        }

        .explanation-section {
            padding-top: 20px;
        }

        .explanation-section h3 {
            color: var(--primary-color);
            font-size: 1.6em;
            margin-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 10px;
        }

        .explanation-section canvas {
            display: block;
            background-color: #F8F8F8;
            border-radius: 8px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
            margin: 20px auto;
            border: 1px solid var(--border-color);
        }

        .explanation-content {
            margin-bottom: 40px;
            padding: 20px;
            background-color: #F8F8F8;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .explanation-content h4 {
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .explanation-content p {
            margin-bottom: 10px;
        }

        .quiz-section {
            text-align: center;
        }

        .quiz-section .game-title {
            font-size: 1.8em;
            color: var(--secondary-color);
            margin-bottom: 25px;
        }

        .game-canvas-container {
            position: relative;
            width: 100%;
            padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
            height: 0;
            overflow: hidden;
            background-color: #F0F0F0;
            border-radius: 8px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
            margin: 20px auto;
            border: 1px solid var(--border-color);
        }

        #gameCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: block;
        }


        footer {
            text-align: center;
            padding: 30px;
            margin-top: 50px;
            color: var(--light-text-color);
            font-size: 0.9em;
            border-top: 1px solid var(--border-color);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            header h1 {
                font-size: 2em;
            }
            .container {
                margin: 20px auto;
                padding: 0 15px;
            }
            .card {
                padding: 20px;
            }
            h2 {
                font-size: 1.8em;
            }
            .question-section .question-text {
                font-size: 1.1em;
            }
            .options label {
                font-size: 1em;
                padding: 12px 15px;
            }
            .btn-check-answer {
                padding: 12px 25px;
                font-size: 1em;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>软件方法学趣味学习</h1>
    </header>

    <main class="container">
        <section class="card question-section">
            <h2>开始挑战！</h2>
            <p class="question-text">
                软件方法学是以软件开发方法为研究对象的学科。其中，<span id="blank1">（ ）</span>是先对最高居次中的问题进行定义、设计、编程和测试，而将其中未解决的问题作为一个子任务放到下一层次中去解决。<span id="blank2">（ ）</span>是根据系统功能要求，从具体的器件、逻辑部件或者相似系统开始，通过对其进行相互连接、修改和扩大，构成所要求的系统。<span id="blank3">（ ）</span>是建立在严格数学基础上的软件开发方法。
            </p>

            <h3>问题1：第一个括号中应填写哪种方法？</h3>
            <div class="options">
                <label>
                    <input type="radio" name="question1" value="A">
                    <span>A 面向对象开发方法</span>
                </label>
                <label>
                    <input type="radio" name="question1" value="B">
                    <span>B 形式化开发方法</span>
                </label>
                <label>
                    <input type="radio" name="question1" value="C">
                    <span>C 非形式化开发方法</span>
                </label>
                <label>
                    <input type="radio" name="question1" value="D">
                    <span>D 自顶向下开发方法</span>
                </label>
            </div>
            <button class="btn-check-answer" onclick="checkAnswer()">提交答案</button>
            <div class="answer-feedback" id="answerFeedback"></div>
        </section>

        <section class="card explanation-section">
            <h2>知识点讲解：深入理解软件方法学</h2>

            <div class="explanation-content" id="topDownExplanation">
                <h4>自顶向下开发方法 (Top-down Development)</h4>
                <p>想象一下，你有一个巨大的乐高城堡要搭建，但你不知道从何开始。自顶向下方法就像是：首先，你把搭建整个城堡看作一个大任务。然后，你决定先搭城堡的主体，再搭塔楼，最后搭城墙和小装饰。每搭完一部分，如果发现还有更小的、未完成的细节（比如塔楼上的旗帜），就把这些小细节放给下一层去解决。这样，一个大问题就被层层分解成无数个小问题，直到所有小问题都变得容易解决为止。</p>
                <p>这种方法强调<b>先整体后局部，先抽象后具体</b>，适用于需求明确、结构清晰的项目。它能帮助我们更好地组织和管理复杂的开发过程。</p>
                <canvas id="topDownCanvas" width="800" height="450"></canvas>
            </div>

            <div class="explanation-content" id="bottomUpExplanation">
                <h4>自底向上开发方法 (Bottom-up Development)</h4>
                <p>如果说自顶向下是拆解大问题，那自底向上就是<b>从最基础的小零件开始，逐步组装成一个完整的大系统</b>。就像你玩乐高，手里只有一堆散落的砖块，你先拼出一个小窗户，再拼出一扇门，然后把窗户和门组合成一个小房间，最后把所有房间组装成一个完整的房子。你从最小、最简单的功能单元开始，确保它们都能正常工作，然后将它们组合成更大的、更复杂的功能模块，最终形成整个系统。</p>
                <p>这种方法适用于模块化程度高、有现成组件或库可以复用的项目。它有助于确保基础模块的稳定性和可重用性。</p>
                <canvas id="bottomUpCanvas" width="800" height="450"></canvas>
            </div>

            <div class="explanation-content" id="formalExplanation">
                <h4>形式化开发方法 (Formal Development)</h4>
                <p>想象一下，你不是在写一篇散文，而是在编写一个极其严谨的数学证明。形式化开发方法就是这样！它<b>建立在严格的数学基础之上</b>，使用像数学公式、逻辑符号和专门的规约语言来精确地描述软件的需求、设计和行为。这就像给软件设计了一套“数学蓝图”，每一部分都必须符合精确的数学定义，不允许有任何歧义。</p>
                <p>这种方法虽然复杂，但能帮助我们<b>在开发早期发现和消除错误，极大地提高软件的可靠性和安全性</b>，常用于开发那些对安全性、可靠性要求极高的系统，比如航空航天、医疗设备或金融交易系统。</p>
                <canvas id="formalCanvas" width="800" height="450"></canvas>
            </div>

            <div class="explanation-content" id="otherMethodsBrief">
                <h4>其他相关概念</h4>
                <ul>
                    <li><b>面向对象开发方法 (Object-Oriented Development)</b>：这是一种将软件系统看作是“对象”的集合，通过对象之间的交互来完成任务的方法。它强调封装、继承和多态等特性，常用于构建复杂且易于维护的系统。</li>
                    <li><b>非形式化开发方法 (Informal Development)</b>：与形式化方法相对，它通常使用自然语言、图表、流程图等相对不那么严格的方式来描述软件。虽然简单易懂，但在复杂系统开发中可能存在歧义和不精确性。</li>
                </ul>
            </div>
        </section>

        <section class="card quiz-section">
            <h2 class="game-title">小游戏：填空大挑战！</h2>
            <p>点击下方区域开始你的挑战！</p>
            <div class="game-canvas-container">
                <canvas id="gameCanvas"></canvas>
            </div>
            <button class="btn-check-answer" onclick="startGame()">开始游戏</button>
            <p id="gameFeedback" style="margin-top: 20px; font-weight: bold;"></p>
        </section>
    </main>

    <footer>
        <p>&copy; 2023 软件方法学趣味学习. 版权所有.</p>
    </footer>

    <script>
        // JavaScript for animations and interactivity will go here.
        // It will be added in subsequent steps.

        document.addEventListener('DOMContentLoaded', () => {
            // Initial setup for canvases to ensure they render correctly
            drawTopDownAnimation();
            drawBottomUpAnimation();
            drawFormalAnimation();
            initGameCanvas(); // Initialize game canvas, but don't start game automatically
        });

        function checkAnswer() {
            const selectedOption = document.querySelector('input[name="question1"]:checked');
            const feedbackDiv = document.getElementById('answerFeedback');
            const blank1 = document.getElementById('blank1');
            const blank2 = document.getElementById('blank2');
            const blank3 = document.getElementById('blank3');

            if (!selectedOption) {
                feedbackDiv.textContent = '请选择一个答案！';
                feedbackDiv.className = 'answer-feedback incorrect';
                feedbackDiv.style.display = 'block';
                return;
            }

            const correctAnswer = 'D'; // From the problem analysis
            if (selectedOption.value === correctAnswer) {
                feedbackDiv.textContent = '恭喜你，回答正确！';
                feedbackDiv.className = 'answer-feedback correct';
            } else {
                feedbackDiv.textContent = `很遗憾，答案错误。`;
                feedbackDiv.className = 'answer-feedback incorrect';
            }
            feedbackDiv.style.display = 'block';
            scrollToElement(feedbackDiv);

            // Reveal all blanks' correct answers after checking the main question
            blank1.textContent = '自顶向下开发方法';
            blank1.style.fontWeight = 'bold';
            blank1.style.color = 'var(--primary-color)';

            blank2.textContent = '自底向上开发方法';
            blank2.style.fontWeight = 'bold';
            blank2.style.color = 'var(--primary-color)';

            blank3.textContent = '形式化开发方法';
            blank3.style.fontWeight = 'bold';
            blank3.style.color = 'var(--primary-color)';
        }

        function scrollToElement(element) {
            window.scrollTo({
                top: element.offsetTop - 50, // Adjust offset as needed
                behavior: 'smooth'
            });
        }

        // --- Canvas Animations ---

        // Top-down Animation
        function drawTopDownAnimation() {
            const canvas = document.getElementById('topDownCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let animationFrameId;
            let step = 0; // 0: initial, 1: big problem, 2: split, 3: sub-problems

            const colors = ['#f44336', '#FF9800', '#FFEB3B', '#4CAF50', '#2196F3', '#9C27B0'];

            function clearCanvas() {
                ctx.clearRect(0, 0, width, height);
            }

            function drawText(text, x, y, size = 20, color = 'var(--text-color)', align = 'center') {
                ctx.font = `${size}px Noto Sans SC`;
                ctx.fillStyle = color;
                ctx.textAlign = align;
                ctx.fillText(text, x, y);
            }

            function drawRectangle(x, y, w, h, color, borderColor = 'rgba(0,0,0,0.2)', borderWidth = 2) {
                ctx.fillStyle = color;
                ctx.strokeStyle = borderColor;
                ctx.lineWidth = borderWidth;
                ctx.fillRect(x, y, w, h);
                ctx.strokeRect(x, y, w, h);
            }

            function drawArrow(fromX, fromY, toX, toY, color = 'var(--light-text-color)', headLength = 10) {
                const angle = Math.atan2(toY - fromY, toX - fromX);
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(toX, toY);
                ctx.stroke();
                ctx.beginPath();
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 6), toY - headLength * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 6), toY - headLength * Math.sin(angle + Math.PI / 6));
                ctx.stroke();
            }

            function animate() {
                clearCanvas();

                const bigProblem = { x: width / 2 - 80, y: 50, w: 160, h: 80 };
                const subProblem1 = { x: width / 2 - 200, y: 200, w: 120, h: 60 };
                const subProblem2 = { x: width / 2 - 60, y: 200, w: 120, h: 60 };
                const subProblem3 = { x: width / 2 + 80, y: 200, w: 120, h: 60 };

                // Step 1: Big Problem
                drawRectangle(bigProblem.x, bigProblem.y, bigProblem.w, bigProblem.h, '#FFDDC1');
                drawText('大问题', bigProblem.x + bigProblem.w / 2, bigProblem.y + bigProblem.h / 2 + 5, 24, '#D32F2F');

                // Step 2: Split
                if (step >= 1) {
                    drawArrow(bigProblem.x + bigProblem.w / 2, bigProblem.y + bigProblem.h, subProblem1.x + subProblem1.w / 2, subProblem1.y, 'var(--primary-color)');
                    drawArrow(bigProblem.x + bigProblem.w / 2, bigProblem.y + bigProblem.h, subProblem2.x + subProblem2.w / 2, subProblem2.y, 'var(--primary-color)');
                    drawArrow(bigProblem.x + bigProblem.w / 2, bigProblem.y + bigProblem.h, subProblem3.x + subProblem3.w / 2, subProblem3.y, 'var(--primary-color)');
                }

                // Step 3: Sub-problems
                if (step >= 2) {
                    drawRectangle(subProblem1.x, subProblem1.y, subProblem1.w, subProblem1.h, '#C8E6C9');
                    drawText('子问题A', subProblem1.x + subProblem1.w / 2, subProblem1.y + subProblem1.h / 2 + 5, 18, 'var(--text-color)');

                    drawRectangle(subProblem2.x, subProblem2.y, subProblem2.w, subProblem2.h, '#BBDEFB');
                    drawText('子问题B', subProblem2.x + subProblem2.w / 2, subProblem2.y + subProblem2.h / 2 + 5, 18, 'var(--text-color)');

                    drawRectangle(subProblem3.x, subProblem3.y, subProblem3.w, subProblem3.h, '#FFCCBC');
                    drawText('子问题C', subProblem3.x + subProblem3.w / 2, subProblem3.y + subProblem3.h / 2 + 5, 18, 'var(--text-color)');

                    // Further decomposition example
                    if (step >= 3) {
                        const microProblem1_1 = { x: subProblem1.x + subProblem1.w / 2 - 40, y: 320, w: 80, h: 40 };
                        const microProblem1_2 = { x: subProblem1.x + subProblem1.w / 2 + 40, y: 320, w: 80, h: 40 };

                        drawArrow(subProblem1.x + subProblem1.w / 2, subProblem1.y + subProblem1.h, microProblem1_1.x + microProblem1_1.w / 2, microProblem1_1.y, 'var(--secondary-color)');
                        drawArrow(subProblem1.x + subProblem1.w / 2, subProblem1.y + subProblem1.h, microProblem1_2.x + microProblem1_2.w / 2, microProblem1_2.y, 'var(--secondary-color)');

                        drawRectangle(microProblem1_1.x, microProblem1_1.y, microProblem1_1.w, microProblem1_1.h, '#DCEDC8');
                        drawText('更小问题', microProblem1_1.x + microProblem1_1.w / 2, microProblem1_1.y + microProblem1_1.h / 2 + 3, 14, 'var(--light-text-color)');
                        drawRectangle(microProblem1_2.x, microProblem1_2.y, microProblem1_2.w, microProblem1_2.h, '#BBDEFB');
                        drawText('更小问题', microProblem1_2.x + microProblem1_2.w / 2, microProblem1_2.y + microProblem1_2.h / 2 + 3, 14, 'var(--light-text-color)');
                    }
                }
            }

            // Interactive steps for top-down
            canvas.addEventListener('click', () => {
                step = (step + 1) % 4; // Cycle through 0, 1, 2, 3
                animate();
            });

            // Initial draw
            animate();
            // To make it continuously animated or interactive on load,
            // we can remove requestAnimationFrame and make it purely click-driven,
            // or use setInterval for a subtle loop
            // For now, it's click-driven.
        }

        // Bottom-up Animation
        function drawBottomUpAnimation() {
            const canvas = document.getElementById('bottomUpCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let step = 0; // 0: initial, 1: small components, 2: assemble modules, 3: full system

            function clearCanvas() {
                ctx.clearRect(0, 0, width, height);
            }

            function drawText(text, x, y, size = 18, color = 'var(--text-color)', align = 'center') {
                ctx.font = `${size}px Noto Sans SC`;
                ctx.fillStyle = color;
                ctx.textAlign = align;
                ctx.fillText(text, x, y);
            }

            function drawCircle(x, y, r, color, borderColor = 'rgba(0,0,0,0.2)', borderWidth = 2) {
                ctx.fillStyle = color;
                ctx.strokeStyle = borderColor;
                ctx.lineWidth = borderWidth;
                ctx.beginPath();
                ctx.arc(x, y, r, 0, Math.PI * 2);
                ctx.fill();
                ctx.stroke();
            }

            function drawArrow(fromX, fromY, toX, toY, color = 'var(--light-text-color)', headLength = 10) {
                const angle = Math.atan2(toY - fromY, toX - fromX);
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(toX, toY);
                ctx.stroke();
                ctx.beginPath();
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 6), toY - headLength * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 6), toY - headLength * Math.sin(angle + Math.PI / 6));
                ctx.stroke();
            }

            const components = [
                { x: width * 0.2, y: 100, r: 25, color: '#FFDDC1', text: '小组件A' },
                { x: width * 0.4, y: 100, r: 25, color: '#C8E6C9', text: '小组件B' },
                { x: width * 0.6, y: 100, r: 25, color: '#BBDEFB', text: '小组件C' },
                { x: width * 0.8, y: 100, r: 25, color: '#FFCCBC', text: '小组件D' }
            ];

            const modules = [
                { x: width * 0.3, y: 250, w: 120, h: 70, color: '#90CAF9', text: '模块1' },
                { x: width * 0.7, y: 250, w: 120, h: 70, color: '#A5D6A7', text: '模块2' }
            ];

            const finalSystem = { x: width / 2 - 100, y: 380, w: 200, h: 100, color: '#81C784', text: '完整系统' };


            function animate() {
                clearCanvas();

                // Step 1: Small Components
                components.forEach(comp => {
                    drawCircle(comp.x, comp.y, comp.r, comp.color);
                    drawText(comp.text, comp.x, comp.y + 5, 14);
                });

                if (step >= 1) {
                    // Draw connections to modules
                    drawArrow(components[0].x, components[0].y + components[0].r, modules[0].x + modules[0].w / 2, modules[0].y, 'var(--primary-color)');
                    drawArrow(components[1].x, components[1].y + components[1].r, modules[0].x + modules[0].w / 2, modules[0].y, 'var(--primary-color)');
                    drawArrow(components[2].x, components[2].y + components[2].r, modules[1].x + modules[1].w / 2, modules[1].y, 'var(--primary-color)');
                    drawArrow(components[3].x, components[3].y + components[3].r, modules[1].x + modules[1].w / 2, modules[1].y, 'var(--primary-color)');

                    // Step 2: Assemble Modules
                    modules.forEach(mod => {
                        ctx.fillStyle = mod.color;
                        ctx.strokeStyle = 'rgba(0,0,0,0.2)';
                        ctx.lineWidth = 2;
                        ctx.fillRect(mod.x - mod.w / 2, mod.y, mod.w, mod.h);
                        ctx.strokeRect(mod.x - mod.w / 2, mod.y, mod.w, mod.h);
                        drawText(mod.text, mod.x, mod.y + mod.h / 2 + 5, 20);
                    });
                }

                if (step >= 2) {
                    // Draw connections to final system
                    drawArrow(modules[0].x, modules[0].y + modules[0].h, finalSystem.x + finalSystem.w / 2, finalSystem.y, 'var(--secondary-color)');
                    drawArrow(modules[1].x, modules[1].y + modules[1].h, finalSystem.x + finalSystem.w / 2, finalSystem.y, 'var(--secondary-color)');

                    // Step 3: Full System
                    ctx.fillStyle = finalSystem.color;
                    ctx.strokeStyle = 'rgba(0,0,0,0.2)';
                    ctx.lineWidth = 2;
                    ctx.fillRect(finalSystem.x, finalSystem.y, finalSystem.w, finalSystem.h);
                    ctx.strokeRect(finalSystem.x, finalSystem.y, finalSystem.w, finalSystem.h);
                    drawText(finalSystem.text, finalSystem.x + finalSystem.w / 2, finalSystem.y + finalSystem.h / 2 + 5, 28, 'white');
                }
            }

            canvas.addEventListener('click', () => {
                step = (step + 1) % 3; // Cycle through 0, 1, 2
                animate();
            });

            animate();
        }

        // Formal Animation
        function drawFormalAnimation() {
            const canvas = document.getElementById('formalCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let step = 0; // 0: initial, 1: axioms, 2: proofs, 3: system

            function clearCanvas() {
                ctx.clearRect(0, 0, width, height);
            }

            function drawText(text, x, y, size = 18, color = 'var(--text-color)', align = 'center') {
                ctx.font = `${size}px Noto Sans SC`;
                ctx.fillStyle = color;
                ctx.textAlign = align;
                ctx.fillText(text, x, y);
            }

            function drawBox(x, y, w, h, color, text, textColor = 'white', borderColor = 'rgba(0,0,0,0.2)') {
                ctx.fillStyle = color;
                ctx.strokeStyle = borderColor;
                ctx.lineWidth = 2;
                ctx.fillRect(x, y, w, h);
                ctx.strokeRect(x, y, w, h);
                drawText(text, x + w / 2, y + h / 2 + 5, 18, textColor);
            }

            function drawEquation(text, x, y, size = 20, color = 'var(--text-color)') {
                ctx.font = `${size}px serif`; // Use a serif font for math
                ctx.fillStyle = color;
                ctx.textAlign = 'center';
                ctx.fillText(text, x, y);
            }

            function drawArrow(fromX, fromY, toX, toY, color = 'var(--light-text-color)', headLength = 10) {
                const angle = Math.atan2(toY - fromY, toX - fromX);
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(toX, toY);
                ctx.stroke();
                ctx.beginPath();
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 6), toY - headLength * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 6), toY - headLength * Math.sin(angle + Math.PI / 6));
                ctx.stroke();
            }

            const axioms = [
                { x: width * 0.2, y: 80, w: 100, h: 50, text: '公理 A', eq: '∀x (P(x) → Q(x))' },
                { x: width * 0.4, y: 80, w: 100, h: 50, text: '公理 B', eq: '∃y (R(y) ∧ S(y))' },
                { x: width * 0.6, y: 80, w: 100, h: 50, text: '定理 C', eq: 'A ∧ B → C' },
                { x: width * 0.8, y: 80, w: 100, h: 50, text: '规则 D', eq: 'If X then Y' }
            ];

            const proofs = [
                { x: width * 0.3, y: 220, w: 150, h: 80, text: '数学推导 1', eq: 'Axiom A + Rule D → Lemma 1' },
                { x: width * 0.7, y: 220, w: 150, h: 80, text: '数学推导 2', eq: 'Axiom B + Theorem C → Lemma 2' }
            ];

            const system = { x: width / 2 - 120, y: 360, w: 240, h: 100, text: '严谨的软件系统', color: '#66BB6A' };

            function animate() {
                clearCanvas();

                // Step 1: Axioms/Rules
                axioms.forEach((ax, i) => {
                    drawBox(ax.x - ax.w / 2, ax.y, ax.w, ax.h, '#CFD8DC', ax.text, 'var(--text-color)');
                    if (step >= 0) {
                        drawEquation(ax.eq, ax.x, ax.y + ax.h + 20, 16, 'var(--light-text-color)');
                    }
                });


                if (step >= 1) {
                    // Draw connections to proofs
                    drawArrow(axioms[0].x, axioms[0].y + axioms[0].h + 20, proofs[0].x, proofs[0].y, 'var(--primary-color)');
                    drawArrow(axioms[3].x, axioms[3].y + axioms[3].h + 20, proofs[0].x, proofs[0].y, 'var(--primary-color)');

                    drawArrow(axioms[1].x, axioms[1].y + axioms[1].h + 20, proofs[1].x, proofs[1].y, 'var(--primary-color)');
                    drawArrow(axioms[2].x, axioms[2].y + axioms[2].h + 20, proofs[1].x, proofs[1].y, 'var(--primary-color)');

                    // Step 2: Proofs/Lemmas
                    proofs.forEach(pr => {
                        drawBox(pr.x - pr.w / 2, pr.y, pr.w, pr.h, '#B3E5FC', pr.text, 'var(--text-color)');
                        if (step >= 1) {
                            drawEquation(pr.eq, pr.x, pr.y + pr.h + 20, 14, 'var(--light-text-color)');
                        }
                    });
                }


                if (step >= 2) {
                    // Draw connections to system
                    drawArrow(proofs[0].x, proofs[0].y + proofs[0].h + 20, system.x + system.w / 2, system.y, 'var(--secondary-color)');
                    drawArrow(proofs[1].x, proofs[1].y + proofs[1].h + 20, system.x + system.w / 2, system.y, 'var(--secondary-color)');

                    // Step 3: Formal System
                    drawBox(system.x, system.y, system.w, system.h, system.color, system.text);
                }
            }

            canvas.addEventListener('click', () => {
                step = (step + 1) % 3; // Cycle through 0, 1, 2
                animate();
            });

            animate();
        }

        // --- Game Section ---
        let gameCanvas, gameCtx;
        let gameQuestion = {
            text: "哪个词描述了“先将大问题分解成小问题，再逐一解决”的开发方法？",
            options: ["A. 自底向上", "B. 形式化", "C. 自顶向下", "D. 面向对象"],
            correctAnswer: "C",
            userAnswer: null
        };
        let gameActive = false;
        let gameFeedbackText = "";

        function initGameCanvas() {
            gameCanvas = document.getElementById('gameCanvas');
            if (!gameCanvas) return;
            gameCtx = gameCanvas.getContext('2d');
            // Make canvas responsive
            resizeGameCanvas();
            window.addEventListener('resize', resizeGameCanvas);
            drawGame();
        }

        function resizeGameCanvas() {
            const container = gameCanvas.parentElement;
            gameCanvas.width = container.clientWidth;
            gameCanvas.height = container.clientHeight;
            if (gameActive) drawGame(); // Redraw if game is active
        }

        function startGame() {
            gameActive = true;
            gameQuestion.userAnswer = null;
            gameFeedbackText = "";
            document.getElementById('gameFeedback').textContent = "";
            drawGame();
            alert('游戏开始！点击选项进行作答。'); // Simple alert to start
        }

        function drawGame() {
            gameCtx.clearRect(0, 0, gameCanvas.width, gameCanvas.height);
            gameCtx.font = '24px Noto Sans SC';
            gameCtx.fillStyle = 'var(--text-color)';
            gameCtx.textAlign = 'center';

            // Draw question
            gameCtx.fillText(gameQuestion.text, gameCanvas.width / 2, 50);

            // Draw options
            const optionWidth = 200;
            const optionHeight = 50;
            const startX = (gameCanvas.width - optionWidth * 2 - 40) / 2; // Two columns, 40px gap
            const startY = 120;
            const padding = 20;

            gameQuestion.options.forEach((option, index) => {
                const col = index % 2;
                const row = Math.floor(index / 2);
                const x = startX + col * (optionWidth + padding);
                const y = startY + row * (optionHeight + padding);

                gameCtx.fillStyle = gameQuestion.userAnswer === option[0] ? 'var(--secondary-color)' : 'var(--card-background)';
                gameCtx.strokeStyle = 'var(--border-color)';
                gameCtx.lineWidth = 2;
                gameCtx.fillRect(x, y, optionWidth, optionHeight);
                gameCtx.strokeRect(x, y, optionWidth, optionHeight);

                gameCtx.fillStyle = gameQuestion.userAnswer === option[0] ? 'white' : 'var(--text-color)';
                gameCtx.font = '20px Noto Sans SC';
                gameCtx.fillText(option, x + optionWidth / 2, y + optionHeight / 2 + 5);

                // Store hit areas for click detection
                option.hitArea = { x, y, width: optionWidth, height: optionHeight, value: option[0] };
            });

            if (gameFeedbackText) {
                gameCtx.fillStyle = 'var(--primary-color)';
                gameCtx.font = '28px Noto Sans SC';
                gameCtx.fillText(gameFeedbackText, gameCanvas.width / 2, gameCanvas.height - 50);
            }
        }

        gameCanvas.addEventListener('click', (event) => {
            if (!gameActive) return;

            const rect = gameCanvas.getBoundingClientRect();
            const scaleX = gameCanvas.width / rect.width;
            const scaleY = gameCanvas.height / rect.height;
            const mouseX = (event.clientX - rect.left) * scaleX;
            const mouseY = (event.clientY - rect.top) * scaleY;

            gameQuestion.options.forEach(option => {
                if (mouseX >= option.hitArea.x && mouseX <= option.hitArea.x + option.hitArea.width &&
                    mouseY >= option.hitArea.y && mouseY <= option.hitArea.y + option.hitArea.height) {
                    gameQuestion.userAnswer = option.hitArea.value;
                    checkGameAnswer();
                    drawGame();
                }
            });
        });

        function checkGameAnswer() {
            if (gameQuestion.userAnswer === gameQuestion.correctAnswer) {
                gameFeedbackText = "太棒了，你答对了！";
                document.getElementById('gameFeedback').textContent = "太棒了，你答对了！";
                document.getElementById('gameFeedback').style.color = 'var(--primary-color)';
                gameActive = false;
            } else {
                gameFeedbackText = `不对哦，正确答案是 ${gameQuestion.correctAnswer}。再想想！`;
                document.getElementById('gameFeedback').textContent = `不对哦，正确答案是 ${gameQuestion.correctAnswer}。再想想！`;
                document.getElementById('gameFeedback').style.color = '#D32F2F';
            }
        }
    </script>
</body>
</html> 