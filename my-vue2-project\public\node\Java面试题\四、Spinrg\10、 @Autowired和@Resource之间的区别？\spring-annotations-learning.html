<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring注解学习 - @Autowired vs @Resource</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .learning-section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section-title {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        canvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #4a5568;
            margin: 30px 0;
            padding: 30px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 16px;
            border-left: 4px solid #667eea;
        }

        .interactive-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }

        .demo-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .demo-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .demo-icon {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .autowired-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .resource-icon {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .game-area {
            background: rgba(255,255,255,0.9);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
        }

        .game-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .game-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .score-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .interactive-demo {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 2.5rem;
            }
            
            .learning-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Spring注解学习</h1>
            <p class="subtitle">@Autowired vs @Resource 交互式教程</p>
        </div>

        <div class="learning-section" style="animation-delay: 0.2s;">
            <h2 class="section-title">🎯 学习目标</h2>
            <div class="explanation">
                <p><strong>欢迎来到Spring注解的奇妙世界！</strong></p>
                <p>今天我们将通过动画和游戏来学习两个重要的注解：</p>
                <ul style="margin-top: 15px; padding-left: 20px;">
                    <li>@Autowired - 按类型自动装配</li>
                    <li>@Resource - 按名称自动装配</li>
                </ul>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="learning-section" style="animation-delay: 0.4s;">
            <h2 class="section-title">🔧 @Autowired 可用于</h2>
            <div class="canvas-container">
                <canvas id="autowiredCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><strong>@Autowired注解可以用在三个地方：</strong></p>
                <p>1. <strong>构造函数</strong> - 在创建对象时自动注入依赖</p>
                <p>2. <strong>成员变量</strong> - 直接在字段上注入依赖</p>
                <p>3. <strong>Setter方法</strong> - 通过setter方法注入依赖</p>
            </div>
        </div>

        <div class="learning-section" style="animation-delay: 0.6s;">
            <h2 class="section-title">⚡ @Autowired vs @Resource</h2>
            <div class="interactive-demo">
                <div class="demo-card" onclick="showAutowiredDemo()">
                    <div class="demo-title">
                        <div class="demo-icon autowired-icon">@A</div>
                        @Autowired
                    </div>
                    <p>默认按<strong>类型</strong>装配注入</p>
                    <p>要求依赖对象必须存在</p>
                    <p>可设置required=false</p>
                </div>
                <div class="demo-card" onclick="showResourceDemo()">
                    <div class="demo-title">
                        <div class="demo-icon resource-icon">@R</div>
                        @Resource
                    </div>
                    <p>默认按<strong>名称</strong>装配注入</p>
                    <p>找不到名称时按类型装配</p>
                    <p>更加灵活的装配策略</p>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="comparisonCanvas" width="800" height="500"></canvas>
            </div>
        </div>

        <div class="learning-section game-area" style="animation-delay: 0.8s;">
            <h2 class="section-title">🎮 互动游戏：注解配对</h2>
            <p>点击正确的注解来完成依赖注入！</p>
            <div class="score-display">得分: <span id="score">0</span></div>
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="400"></canvas>
            </div>
            <button class="game-button" onclick="startGame()">开始游戏</button>
            <button class="game-button" onclick="resetGame()">重新开始</button>
        </div>

        <div class="learning-section" style="animation-delay: 1s;">
            <h2 class="section-title">📚 知识总结</h2>
            <div class="explanation">
                <h3>🔍 核心区别：</h3>
                <p><strong>@Autowired：</strong></p>
                <ul>
                    <li>Spring框架提供的注解</li>
                    <li>默认按类型(byType)进行装配</li>
                    <li>如果找到多个相同类型的Bean，会报错</li>
                    <li>可以配合@Qualifier指定具体的Bean</li>
                </ul>
                <br>
                <p><strong>@Resource：</strong></p>
                <ul>
                    <li>Java标准注解(JSR-250)</li>
                    <li>默认按名称(byName)进行装配</li>
                    <li>找不到对应名称时，再按类型装配</li>
                    <li>更加智能和灵活的装配策略</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 0;
        let gameScore = 0;
        let gameActive = false;
        let gameObjects = [];
        let particles = [];

        // 更新进度条
        function updateProgress() {
            const progress = (currentStep / 5) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // @Autowired 动画演示
        function drawAutowiredDemo() {
            const canvas = document.getElementById('autowiredCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f7fafc');
            gradient.addColorStop(1, '#edf2f7');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制三个使用场景
            const scenarios = [
                { x: 130, y: 200, label: '构造函数', icon: '🏗️' },
                { x: 400, y: 200, label: '成员变量', icon: '📦' },
                { x: 670, y: 200, label: 'Setter方法', icon: '⚙️' }
            ];

            const time = Date.now() / 1000;

            scenarios.forEach((scenario, index) => {
                // 动画效果：轮流高亮
                const isActive = Math.floor(time * 0.8) % 3 === index;
                const scale = isActive ? 1.1 : 1;
                const radius = 60 * scale;

                // 绘制圆形背景
                ctx.beginPath();
                ctx.arc(scenario.x, scenario.y, radius, 0, 2 * Math.PI);
                ctx.fillStyle = isActive ? '#667eea' : '#e2e8f0';
                ctx.fill();

                // 添加阴影效果
                if (isActive) {
                    ctx.shadowColor = 'rgba(102, 126, 234, 0.4)';
                    ctx.shadowBlur = 20;
                    ctx.shadowOffsetX = 0;
                    ctx.shadowOffsetY = 10;
                }

                // 绘制图标
                ctx.font = `${30 * scale}px Arial`;
                ctx.textAlign = 'center';
                ctx.fillStyle = isActive ? 'white' : '#4a5568';
                ctx.fillText(scenario.icon, scenario.x, scenario.y + 10);

                // 重置阴影
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;

                // 绘制标签
                ctx.font = 'bold 16px Arial';
                ctx.fillStyle = '#2d3748';
                ctx.fillText(scenario.label, scenario.x, scenario.y + 90);

                // 绘制@Autowired注解
                ctx.font = 'bold 14px Arial';
                ctx.fillStyle = '#667eea';
                ctx.fillText('@Autowired', scenario.x, scenario.y - 80);
            });

            // 绘制连接线动画
            const wave = Math.sin(time * 2) * 10;

            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.setLineDash([5, 5]);
            ctx.lineDashOffset = -time * 20;

            // 连接线
            ctx.beginPath();
            ctx.moveTo(190, 200 + wave);
            ctx.lineTo(340, 200 - wave);
            ctx.moveTo(460, 200 + wave);
            ctx.lineTo(610, 200 - wave);
            ctx.stroke();

            ctx.setLineDash([]);
        }

        // 比较演示动画
        function drawComparisonDemo() {
            const canvas = document.getElementById('comparisonCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f7fafc');
            gradient.addColorStop(1, '#edf2f7');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() / 1000;

            // @Autowired 演示 (左侧)
            const autowiredGradient = ctx.createLinearGradient(50, 50, 350, 450);
            autowiredGradient.addColorStop(0, '#667eea');
            autowiredGradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = autowiredGradient;
            ctx.fillRect(50, 50, 300, 400);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('@Autowired', 200, 90);

            // 类型匹配动画
            const typeY = 150 + Math.sin(time * 2) * 10;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(80, typeY, 240, 60);
            ctx.fillStyle = '#2d3748';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('按类型匹配 (byType)', 200, typeY + 35);

            // Bean对象动画
            const beanScale1 = 1 + Math.sin(time * 3) * 0.1;
            const beanScale2 = 1 + Math.sin(time * 3 + Math.PI) * 0.1;

            ctx.fillStyle = '#38a169';
            ctx.fillRect(100, 280, 80 * beanScale1, 80 * beanScale1);
            ctx.fillRect(220, 280, 80 * beanScale2, 80 * beanScale2);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('UserService', 140, 325);
            ctx.fillText('OrderService', 260, 325);

            // @Resource 演示 (右侧)
            const resourceGradient = ctx.createLinearGradient(450, 50, 750, 450);
            resourceGradient.addColorStop(0, '#f093fb');
            resourceGradient.addColorStop(1, '#f5576c');
            ctx.fillStyle = resourceGradient;
            ctx.fillRect(450, 50, 300, 400);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('@Resource', 600, 90);

            // 名称匹配动画
            const nameY = 150 + Math.sin(time * 2 + Math.PI) * 10;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(480, nameY, 240, 60);
            ctx.fillStyle = '#2d3748';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('按名称匹配 (byName)', 600, nameY + 35);

            // Bean对象
            ctx.fillStyle = '#d69e2e';
            ctx.fillRect(500, 280, 80, 80);
            ctx.fillRect(620, 280, 80, 80);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('userDao', 540, 325);
            ctx.fillText('orderDao', 660, 325);

            // 绘制箭头指示
            drawArrow(ctx, 200, 240, 200, 270, '#4299e1');
            drawArrow(ctx, 600, 240, 600, 270, '#ed64a6');
        }

        // 绘制箭头函数
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            const headlen = 10;
            const dx = toX - fromX;
            const dy = toY - fromY;
            const angle = Math.atan2(dy, dx);

            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // 游戏相关函数
        function initGame() {
            gameObjects = [
                { x: 100, y: 100, type: 'bean', name: 'UserService', color: '#4299e1', matched: false },
                { x: 300, y: 100, type: 'bean', name: 'OrderService', color: '#38a169', matched: false },
                { x: 500, y: 100, type: 'bean', name: 'PaymentService', color: '#d69e2e', matched: false },
                { x: 150, y: 250, type: 'annotation', name: '@Autowired', color: '#667eea', target: 'type' },
                { x: 350, y: 250, type: 'annotation', name: '@Resource', color: '#f093fb', target: 'name' }
            ];
            particles = [];
        }

        function drawGame() {
            const canvas = document.getElementById('gameCanvas');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f7fafc');
            gradient.addColorStop(1, '#edf2f7');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制连接线
            if (gameActive) {
                ctx.strokeStyle = 'rgba(102, 126, 234, 0.3)';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                gameObjects.forEach(obj => {
                    if (obj.type === 'annotation') {
                        gameObjects.forEach(bean => {
                            if (bean.type === 'bean' && !bean.matched) {
                                ctx.beginPath();
                                ctx.moveTo(obj.x + 70, obj.y + 25);
                                ctx.lineTo(bean.x + 60, bean.y + 30);
                                ctx.stroke();
                            }
                        });
                    }
                });
                ctx.setLineDash([]);
            }

            // 绘制游戏对象
            gameObjects.forEach(obj => {
                const time = Date.now() / 1000;
                const hover = Math.sin(time * 3) * 2;

                ctx.fillStyle = obj.matched ? '#a0aec0' : obj.color;
                if (obj.type === 'bean') {
                    ctx.fillRect(obj.x, obj.y + hover, 120, 60);
                } else {
                    ctx.fillRect(obj.x, obj.y + hover, 140, 50);
                }

                // 添加边框
                ctx.strokeStyle = obj.matched ? '#718096' : 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(obj.x, obj.y + hover, obj.type === 'bean' ? 120 : 140, obj.type === 'bean' ? 60 : 50);

                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(obj.name, obj.x + (obj.type === 'bean' ? 60 : 70), obj.y + 35 + hover);
            });

            // 绘制粒子效果
            particles.forEach((particle, index) => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life -= 0.02;
                particle.size *= 0.98;

                if (particle.life <= 0) {
                    particles.splice(index, 1);
                    return;
                }

                ctx.fillStyle = `rgba(${particle.color}, ${particle.life})`;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, 2 * Math.PI);
                ctx.fill();
            });

            // 绘制说明文字
            ctx.fillStyle = '#2d3748';
            ctx.font = '18px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('🎯 点击注解来学习它们的特点！', 50, 40);

            if (gameActive) {
                ctx.fillText('💡 @Autowired按类型匹配，@Resource按名称匹配', 50, 370);
            }
        }

        function startGame() {
            gameActive = true;
            gameScore = 0;
            document.getElementById('score').textContent = gameScore;
            initGame();
            currentStep = Math.min(currentStep + 1, 5);
            updateProgress();
        }

        function resetGame() {
            gameActive = false;
            gameScore = 0;
            document.getElementById('score').textContent = gameScore;
            initGame();
        }

        // 创建粒子效果
        function createParticles(x, y, color = '102, 126, 234') {
            for (let i = 0; i < 15; i++) {
                particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 8,
                    vy: (Math.random() - 0.5) * 8,
                    size: Math.random() * 6 + 2,
                    life: 1,
                    color: color
                });
            }
        }

        // 演示函数
        function showAutowiredDemo() {
            currentStep = Math.min(currentStep + 1, 5);
            updateProgress();

            // 添加视觉反馈
            const card = event.currentTarget;
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'translateY(-5px)';
            }, 150);
        }

        function showResourceDemo() {
            currentStep = Math.min(currentStep + 1, 5);
            updateProgress();

            // 添加视觉反馈
            const card = event.currentTarget;
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'translateY(-5px)';
            }, 150);
        }

        // 初始化和事件监听
        window.addEventListener('load', function() {
            // 启动动画循环
            function animate() {
                drawAutowiredDemo();
                drawComparisonDemo();
                drawGame();
                requestAnimationFrame(animate);
            }
            animate();

            // 初始化游戏
            initGame();

            // 添加点击事件监听
            const gameCanvas = document.getElementById('gameCanvas');
            if (gameCanvas) {
                gameCanvas.addEventListener('click', function(e) {
                    const rect = gameCanvas.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    // 检查点击的对象
                    gameObjects.forEach(obj => {
                        const width = obj.type === 'bean' ? 120 : 140;
                        const height = obj.type === 'bean' ? 60 : 50;

                        if (x >= obj.x && x <= obj.x + width && y >= obj.y && y <= obj.y + height) {
                            if (obj.type === 'annotation' && !obj.matched) {
                                gameScore += 10;
                                document.getElementById('score').textContent = gameScore;

                                // 创建粒子效果
                                const particleColor = obj.name === '@Autowired' ? '102, 126, 234' : '240, 147, 251';
                                createParticles(obj.x + width/2, obj.y + height/2, particleColor);

                                // 显示注解信息
                                showAnnotationInfo(obj);

                                obj.matched = true;
                                currentStep = Math.min(currentStep + 1, 5);
                                updateProgress();
                            }
                        }
                    });
                });
            }

            // 添加键盘事件
            document.addEventListener('keydown', function(e) {
                if (e.key === ' ') {
                    e.preventDefault();
                    if (gameActive) {
                        resetGame();
                    } else {
                        startGame();
                    }
                }
            });

            // 添加滚动动画
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 观察所有学习区域
            document.querySelectorAll('.learning-section').forEach(section => {
                observer.observe(section);
            });
        });

        // 显示注解信息
        function showAnnotationInfo(annotation) {
            const info = {
                '@Autowired': {
                    title: '@Autowired 详解',
                    content: '🔍 按类型自动装配\n✅ Spring框架注解\n⚠️ 默认required=true\n🎯 可配合@Qualifier使用'
                },
                '@Resource': {
                    title: '@Resource 详解',
                    content: '🏷️ 按名称自动装配\n✅ Java标准注解(JSR-250)\n🔄 名称找不到时按类型装配\n🎯 更灵活的装配策略'
                }
            };

            const annotationInfo = info[annotation.name];
            if (annotationInfo) {
                // 创建临时提示框
                const tooltip = document.createElement('div');
                tooltip.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: white;
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                    z-index: 1000;
                    max-width: 300px;
                    text-align: left;
                    font-family: inherit;
                    animation: fadeInScale 0.3s ease-out;
                `;

                tooltip.innerHTML = `
                    <h3 style="margin: 0 0 15px 0; color: ${annotation.color}; font-size: 18px;">${annotationInfo.title}</h3>
                    <p style="margin: 0; line-height: 1.6; white-space: pre-line; color: #4a5568;">${annotationInfo.content}</p>
                    <button onclick="this.parentElement.remove()" style="
                        margin-top: 15px;
                        background: ${annotation.color};
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                    ">知道了</button>
                `;

                document.body.appendChild(tooltip);

                // 3秒后自动移除
                setTimeout(() => {
                    if (tooltip.parentElement) {
                        tooltip.remove();
                    }
                }, 3000);
            }
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInScale {
                from {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.8);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%) scale(1);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
