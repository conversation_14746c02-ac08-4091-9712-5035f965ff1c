<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式系统存储速度探索之旅</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .race-track {
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .storage-item {
            display: flex;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .storage-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .storage-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin-right: 30px;
            position: relative;
        }

        .register { background: linear-gradient(45deg, #ff6b6b, #ee5a24); }
        .cache { background: linear-gradient(45deg, #feca57, #ff9ff3); }
        .memory { background: linear-gradient(45deg, #48dbfb, #0abde3); }
        .flash { background: linear-gradient(45deg, #1dd1a1, #10ac84); }

        .storage-info {
            flex: 1;
        }

        .storage-name {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .storage-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }

        .speed-bar {
            width: 200px;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .speed-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 2s ease-out;
        }

        .game-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
        }

        .game-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .race-canvas {
            border: 3px solid #3498db;
            border-radius: 15px;
            background: #f8f9fa;
            margin: 20px 0;
        }

        .control-panel {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }

        .explanation {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: left;
        }

        .explanation h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #34495e;
            line-height: 1.8;
            margin-bottom: 15px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .winner {
            background: linear-gradient(45deg, #f39c12, #e67e22) !important;
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 嵌入式系统存储速度探索之旅</h1>
        
        <div class="race-track">
            <div class="storage-item" data-speed="100" data-name="寄存器组">
                <div class="storage-icon register">🏆</div>
                <div class="storage-info">
                    <div class="storage-name">寄存器组 (Register)</div>
                    <div class="storage-desc">CPU内部的超高速存储，直接与处理器核心连接</div>
                    <div class="speed-bar">
                        <div class="speed-fill register" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <div class="storage-item" data-speed="80" data-name="Cache">
                <div class="storage-icon cache">⚡</div>
                <div class="storage-info">
                    <div class="storage-name">Cache (高速缓存)</div>
                    <div class="storage-desc">位于CPU和内存之间的高速缓冲存储器</div>
                    <div class="speed-bar">
                        <div class="speed-fill cache" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <div class="storage-item" data-speed="40" data-name="内存">
                <div class="storage-icon memory">💾</div>
                <div class="storage-info">
                    <div class="storage-name">内存 (RAM)</div>
                    <div class="storage-desc">系统主存储器，用于临时存储程序和数据</div>
                    <div class="speed-bar">
                        <div class="speed-fill memory" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <div class="storage-item" data-speed="20" data-name="Flash">
                <div class="storage-icon flash">💿</div>
                <div class="storage-info">
                    <div class="storage-name">Flash (闪存)</div>
                    <div class="storage-desc">非易失性存储器，断电后数据不丢失</div>
                    <div class="speed-bar">
                        <div class="speed-fill flash" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="game-section">
            <h2 class="game-title">🎮 存储速度竞赛游戏</h2>
            <canvas id="raceCanvas" class="race-canvas" width="800" height="400"></canvas>
            
            <div class="control-panel">
                <button class="btn btn-primary" onclick="startRace()">开始竞赛</button>
                <button class="btn btn-primary" onclick="resetRace()">重新开始</button>
            </div>
        </div>

        <div class="explanation">
            <h3>📚 知识详解</h3>
            <p><strong>为什么寄存器组最快？</strong></p>
            <p>寄存器组直接集成在CPU内部，与处理器核心的距离最近，访问时间通常只需要1个时钟周期。它们是CPU进行运算时的"工作台"。</p>
            
            <p><strong>Cache的作用是什么？</strong></p>
            <p>Cache是一个聪明的"预测者"，它会提前把CPU可能需要的数据从内存中复制过来，这样CPU就不用等待慢速的内存访问了。</p>
            
            <p><strong>内存和Flash的区别？</strong></p>
            <p>内存(RAM)速度较快但断电丢失数据，主要用于运行程序；Flash速度较慢但能永久保存数据，主要用于存储程序代码。</p>
        </div>
    </div>

    <script>
        // 存储竞赛动画
        const canvas = document.getElementById('raceCanvas');
        const ctx = canvas.getContext('2d');
        
        const racers = [
            { name: '寄存器', speed: 8, x: 50, y: 80, color: '#ff6b6b', emoji: '🏆' },
            { name: 'Cache', speed: 6, x: 50, y: 160, color: '#feca57', emoji: '⚡' },
            { name: '内存', speed: 3, x: 50, y: 240, color: '#48dbfb', emoji: '💾' },
            { name: 'Flash', speed: 1.5, x: 50, y: 320, color: '#1dd1a1', emoji: '💿' }
        ];

        let raceActive = false;
        let animationId;

        function drawRaceTrack() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制赛道
            for (let i = 0; i < 4; i++) {
                const y = 80 + i * 80;
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(30, y + 20);
                ctx.lineTo(canvas.width - 30, y + 20);
                ctx.stroke();
                
                // 终点线
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(canvas.width - 50, y - 20);
                ctx.lineTo(canvas.width - 50, y + 40);
                ctx.stroke();
            }
        }

        function drawRacers() {
            racers.forEach(racer => {
                // 绘制赛车
                ctx.fillStyle = racer.color;
                ctx.fillRect(racer.x - 15, racer.y - 10, 30, 20);
                ctx.fillStyle = 'white';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(racer.emoji, racer.x, racer.y + 5);
                
                // 绘制名称
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Arial';
                ctx.fillText(racer.name, racer.x, racer.y - 25);
            });
        }

        function animateRace() {
            if (!raceActive) return;
            
            drawRaceTrack();
            
            let allFinished = true;
            racers.forEach(racer => {
                if (racer.x < canvas.width - 80) {
                    racer.x += racer.speed;
                    allFinished = false;
                }
            });
            
            drawRacers();
            
            if (allFinished) {
                raceActive = false;
                showWinner();
            } else {
                animationId = requestAnimationFrame(animateRace);
            }
        }

        function startRace() {
            if (raceActive) return;
            
            resetRace();
            raceActive = true;
            
            // 启动速度条动画
            document.querySelectorAll('.speed-fill').forEach((fill, index) => {
                const speeds = [100, 80, 40, 20];
                setTimeout(() => {
                    fill.style.width = speeds[index] + '%';
                }, index * 200);
            });
            
            animateRace();
        }

        function resetRace() {
            raceActive = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            racers.forEach(racer => {
                racer.x = 50;
            });
            
            drawRaceTrack();
            drawRacers();
            
            // 重置速度条
            document.querySelectorAll('.speed-fill').forEach(fill => {
                fill.style.width = '0%';
            });
        }

        function showWinner() {
            const winner = racers.reduce((prev, current) => 
                (prev.x > current.x) ? prev : current
            );
            
            setTimeout(() => {
                alert(`🎉 恭喜！${winner.name} 获得第一名！\n\n这就是为什么在嵌入式系统中，寄存器组的存取速度最快的原因！`);
            }, 500);
        }

        // 初始化
        drawRaceTrack();
        drawRacers();

        // 点击存储项目的交互效果
        document.querySelectorAll('.storage-item').forEach(item => {
            item.addEventListener('click', function() {
                const name = this.dataset.name;
                const descriptions = {
                    '寄存器组': '寄存器组是CPU内部的存储单元，直接与ALU(算术逻辑单元)连接，访问速度最快，通常只需1个时钟周期！',
                    'Cache': 'Cache采用SRAM技术，比内存快10-100倍，通过预测和缓存机制大大提升系统性能！',
                    '内存': '内存使用DRAM技术，需要定期刷新，访问时间通常为几十纳秒，是程序运行的主要场所！',
                    'Flash': 'Flash是非易失性存储器，采用浮栅技术存储数据，虽然速度较慢但能长期保存数据！'
                };
                
                alert(`💡 ${name} 详解：\n\n${descriptions[name]}`);
            });
        });
    </script>
</body>
</html>
