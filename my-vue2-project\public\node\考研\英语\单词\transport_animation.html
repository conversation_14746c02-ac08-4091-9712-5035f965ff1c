<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Transport</title>
    <!-- 引入 GSAP 动画库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #3498db; /* Sky Blue */
            --secondary-color: #2980b9;
            --glow-color: #5dade2;
            --light-bg: #ecf0f1;
            --panel-bg: #ffffff;
            --text-color: #2c3e50;
            --canvas-bg: #f0f9ff; 
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #dde4e7;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--primary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #dbe8f0;
            border-radius: 10px;
        }

        .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 0 10px var(--glow-color);
            transform: translateY(-2px);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            background: var(--canvas-bg);
        }

        #animation-canvas {
            width: 100%;
            height: calc(100% - 80px);
            border-radius: 15px;
            background-color: #ffffff;
        }
        
        .control-button {
            position: absolute;
            bottom: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 0 20px var(--glow-color);
            z-index: 10;
        }
        .control-button:hover { background-color: var(--secondary-color); }
        .control-button.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>transport</h1>
            <p class="pronunciation">[trænˈspɔːt]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 运输, 传送, 搬运</p>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析 (GSAP 动画)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="trans-game">trans- (across, 横跨)</button>
                    <button class="morpheme-btn" data-activity="port-game">-port- (to carry, 搬运)</button>
                </div>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动 (GSAP 动画)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示：无人机快递</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="control-btn" class="control-button hidden">Transport!</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const controlBtn = document.getElementById('control-btn');
        let currentTicker = null; // 用于存储当前的 GSAP Ticker

        const panel = canvas.parentElement;
        canvas.width = panel.clientWidth;
        canvas.height = panel.clientHeight - 80;

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        function stopCurrentAnimation() {
            if (currentTicker) {
                gsap.ticker.remove(currentTicker);
                currentTicker = null;
            }
            gsap.globalTimeline.clear(); // 清除所有活动的 tweens 和 timelines
        }

        // --- Morpheme Games ---
        function initTransGame() {
            stopCurrentAnimation();
            let particle = { x: 50, y: canvas.height/2, radius: 10, color: 'var(--primary-color)' };
            
            function draw() {
                clearCanvas();
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
                ctx.fill();
            }

            currentTicker = draw;
            gsap.ticker.add(currentTicker);
            draw();

            controlBtn.textContent = '横跨 (trans-)';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                gsap.fromTo(particle, {x: 50}, { x: canvas.width - 50, duration: 2, ease: 'power2.inOut' });
            };
        }

        function initPortGame() {
             stopCurrentAnimation();
             let crane = { hookY: 100, ropeLength: canvas.height - 250 };
             let box = { x: canvas.width/2, y: canvas.height - 50, width: 50, height: 50 };
             
             function draw() {
                clearCanvas();
                // Draw Crane Arm
                ctx.fillStyle = '#bdc3c7';
                ctx.fillRect(canvas.width/2 - 150, 80, 300, 20);
                // Draw Rope & Hook
                ctx.strokeStyle = '#34495e';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(canvas.width/2, 100);
                ctx.lineTo(canvas.width/2, crane.hookY);
                ctx.stroke();
                ctx.fillRect(canvas.width/2 - 15, crane.hookY, 30, 15);
                // Draw Box
                ctx.fillStyle = 'var(--primary-color)';
                ctx.fillRect(box.x - box.width/2, box.y - box.height, box.width, box.height);
             }

            currentTicker = draw;
            gsap.ticker.add(currentTicker);
            draw();

            controlBtn.textContent = '搬运 (port-)';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                const tl = gsap.timeline();
                tl.to(crane, { hookY: box.y - box.height, duration: 1.5, ease: 'power1.in' }) // Hook down
                  .to(box, { y: crane.hookY + box.height, duration: 0.1 }, "<") // Attach box to hook
                  .to([crane, box], { y: 150, duration: 2, ease: 'power1.out' }); // Lift up
            };
        }
        
        // --- Full Animation ---
        function initFullAnimation() {
            stopCurrentAnimation();
            const startPos = { x: 100, y: canvas.height - 100 };
            const endPos = { x: canvas.width - 100, y: canvas.height - 100 };
            
            let drone = { x: startPos.x, y: startPos.y - 50, width: 60, height: 20 };
            let parcel = { x: startPos.x, y: startPos.y - 25, width: 30, height: 20 };

            function draw() {
                clearCanvas();
                // Draw platforms
                ctx.fillStyle = '#2ecc71';
                ctx.fillRect(startPos.x - 50, startPos.y, 100, 20);
                ctx.fillRect(endPos.x - 50, endPos.y, 100, 20);
                // Draw Drone
                ctx.fillStyle = '#34495e';
                ctx.fillRect(drone.x - drone.width/2, drone.y - drone.height/2, drone.width, drone.height);
                // Draw Parcel
                ctx.fillStyle = 'var(--primary-color)';
                ctx.fillRect(parcel.x - parcel.width/2, parcel.y - parcel.height/2, parcel.width, parcel.height);
            }

            currentTicker = draw;
            gsap.ticker.add(currentTicker);
            draw();

            controlBtn.textContent = 'Transport!';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                // Reset positions before animating
                gsap.set([drone, parcel], { x: startPos.x, y: (i) => startPos.y - (i === 0 ? 50 : 25) });

                const tl = gsap.timeline();
                tl.to([drone, parcel], { y: '-=150', duration: 1, ease: 'power1.out' }) // Lift off
                  .to([drone, parcel], { x: endPos.x, duration: 3, ease: 'sine.inOut' }) // Move across
                  .to([drone, parcel], { y: (i) => endPos.y - (i === 0 ? 50 : 25), duration: 1, ease: 'power1.in' }); // Land
            };
        }

        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                const activity = btn.dataset.activity;
                if (activity === 'trans-game') initTransGame();
                else if (activity === 'port-game') initPortGame();
                else if (activity === 'full-animation') initFullAnimation();
            });
        });

        initTransGame(); // Start with a default animation
    });
    </script>
</body>
</html> 