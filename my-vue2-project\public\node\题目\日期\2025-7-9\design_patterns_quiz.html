<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式小测验 - 软考达人</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            padding: 30px 40px;
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
            box-sizing: border-box;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.2em;
        }
        .question-section {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .question-text {
            font-size: 1.15em;
            line-height: 1.8;
            margin-bottom: 25px;
            background-color: #e8f0fe;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #4CAF50;
        }
        .options-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .options-list li {
            margin-bottom: 15px;
        }
        .options-list label {
            display: flex;
            align-items: center;
            font-size: 1.05em;
            cursor: pointer;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .options-list label:hover {
            background-color: #f0f0f0;
            border-color: #c0c0c0;
        }
        .options-list input[type="radio"] {
            margin-right: 15px;
            transform: scale(1.3);
        }
        .submit-btn {
            display: block;
            width: 100%;
            padding: 15px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin-top: 30px;
        }
        .submit-btn:hover {
            background-color: #45a049;
            transform: translateY(-2px);
        }
        .result-section {
            margin-top: 30px;
            padding: 25px;
            border-radius: 8px;
            font-size: 1.1em;
            line-height: 1.7;
            display: none; /* Hidden by default */
            box-sizing: border-box;
            width: 100%;
        }
        .result-section.correct {
            background-color: #e6ffe6;
            border: 1px solid #4CAF50;
            color: #28a745;
        }
        .result-section.incorrect {
            background-color: #ffe6e6;
            border: 1px solid #dc3545;
            color: #dc3545;
        }
        .explanation-title {
            font-size: 1.3em;
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #ddd;
            padding-bottom: 10px;
        }
        .explanation-content {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            border: 1px dashed #ccc;
            margin-bottom: 20px;
        }
        .explanation-content h3 {
            color: #4CAF50;
            margin-top: 0;
            font-size: 1.2em;
        }
        .explanation-content p {
            margin-bottom: 10px;
        }

        #animationCanvas {
            display: block;
            background-color: #e0f2f7;
            border: 2px solid #a7d9ed;
            border-radius: 8px;
            margin-top: 20px;
            width: 100%;
            max-width: 800px;
            height: 450px; /* Fixed height for consistency */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px 25px;
            }
            h1 {
                font-size: 1.8em;
            }
            .question-text {
                font-size: 1em;
            }
            .options-list label {
                font-size: 0.95em;
                padding: 10px 12px;
            }
            .submit-btn {
                padding: 12px 15px;
                font-size: 1em;
            }
            .result-section {
                font-size: 1em;
                padding: 15px;
            }
            .explanation-title {
                font-size: 1.1em;
            }
            .explanation-content h3 {
                font-size: 1.1em;
            }
            #animationCanvas {
                height: 300px; /* Adjust height for smaller screens */
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设计模式小测验</h1>

        <div class="question-section">
            <p class="question-text">
                按照设计模式的目的进行划分，现有的设计模式可以分为三类。其中创建型模式通过采用抽象类所定义的接口，封装了系统中对象如何创建、组合等信息，其代表有 Singleton 模式等；（请作答此空）模式主要用于如何组合已有的类和对象以获得更大的结构，其代表有 Adapter 模式等；行为型模式主要用于对象之间的职责及其提供服务的分配方式，其代表有 Visitor 模式等。
            </p>
            <ul class="options-list">
                <li>
                    <label>
                        <input type="radio" name="designPatternType" value="A">
                        A. 合成型
                    </label>
                </li>
                <li>
                    <label>
                        <input type="radio" name="designPatternType" value="B">
                        B. 组合型
                    </label>
                </li>
                <li>
                    <label>
                        <input type="radio" name="designPatternType" value="C">
                        C. 结构型
                    </label>
                </li>
                <li>
                    <label>
                        <input type="radio" name="designPatternType" value="D">
                        D. 聚合型
                    </label>
                </li>
            </ul>
            <button class="submit-btn" id="submitAnswer">提交答案</button>
        </div>

        <div class="result-section" id="quizResult">
            <!-- Result and explanation will be inserted here by JavaScript -->
        </div>

        <h2 class="explanation-title">设计模式家族揭秘！</h2>
        <div class="explanation-content">
            <p>
                设计模式就像是软件开发中的“最佳实践”模板，是解决常见问题的成熟方案。它们被分成了不同的家族，每个家族都有自己的特长。让我们一起来看看！
            </p>
            <div id="patternExplanation">
                <!-- Detailed explanation for each pattern type will be inserted here -->
                <h3>🤔 什么是设计模式？</h3>
                <p>
                    设计模式是软件开发中常见问题的通用、可重用的解决方案。它们不是可以直接拿来用的代码，而是一种指导思想或模板，帮助我们编写出更灵活、可维护、可扩展的代码。
                </p>
            </div>
        </div>

        <h2 class="explanation-title">动画演示：设计模式的“变身”！</h2>
        <p style="text-align: center; margin-bottom: 20px; font-size: 1.05em; color: #555;">
            点击下面的模式名称，看看它们是如何“变身”的！
        </p>
        <canvas id="animationCanvas" width="800" height="450"></canvas>
        <div style="text-align: center; margin-top: 15px;">
            <button class="submit-btn" id="showCreational" style="width: auto; margin-right: 10px;">创建型模式</button>
            <button class="submit-btn" id="showStructural" style="width: auto; margin-right: 10px;">结构型模式</button>
            <button class="submit-btn" id="showBehavioral" style="width: auto;">行为型模式</button>
        </div>
    </div>

    <script>
        const submitButton = document.getElementById('submitAnswer');
        const quizResult = document.getElementById('quizResult');
        const patternExplanationDiv = document.getElementById('patternExplanation');
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');

        const showCreationalBtn = document.getElementById('showCreational');
        const showStructuralBtn = document.getElementById('showStructural');
        const showBehavioralBtn = document.getElementById('showBehavioral');

        // Initial explanation content
        patternExplanationDiv.innerHTML = `
            <h3>🤔 什么是设计模式？</h3>
            <p>
                设计模式是软件开发中常见问题的通用、可重用的解决方案。它们不是可以直接拿来用的代码，而是一种指导思想或模板，帮助我们编写出更灵活、可维护、可扩展的代码。
            </p>
        `;

        submitButton.addEventListener('click', () => {
            const selectedOption = document.querySelector('input[name="designPatternType"]:checked');
            if (selectedOption) {
                const answer = selectedOption.value;
                if (answer === 'C') {
                    quizResult.className = 'result-section correct';
                    quizResult.innerHTML = '<strong>🎉 恭喜你，回答正确！</strong>';
                    showDetailedExplanation('structural'); // Show explanation for Structural
                } else {
                    quizResult.className = 'result-section incorrect';
                    quizResult.innerHTML = '<strong>❌ 回答错误。</strong> 正确答案是：C. 结构型';
                    showDetailedExplanation('all'); // Show all explanations
                }
                quizResult.style.display = 'block';
            } else {
                alert('请选择一个答案！');
            }
        });

        function showDetailedExplanation(type) {
            let explanationHtml = '';
            if (type === 'structural' || type === 'all') {
                explanationHtml += `
                    <h3>🧱 结构型模式 (Structural Patterns)</h3>
                    <p>这类模式主要关注如何<strong>组合</strong>类和对象，形成更大的结构。它们帮助你把不同的部分“拼装”起来，让它们一起工作，同时保持结构的灵活性和效率。</p>
                    <p><strong>核心思想：</strong> 如何将类或对象组合成更大的结构，同时保持灵活性和效率。</p>
                    <p><strong>典型代表：</strong></p>
                    <ul>
                        <li><strong>Adapter (适配器模式):</strong> 让两个不兼容的接口能够协同工作，就像一个“转换插头”。</li>
                        <li><strong>Decorator (装饰器模式):</strong> 动态地给对象添加新的行为，就像给礼物包上漂亮的包装纸。</li>
                        <li><strong>Composite (组合模式):</strong> 将对象组合成树形结构以表示“部分-整体”的层次结构，让用户对单个对象和组合对象的使用具有一致性。</li>
                        <li><strong>Proxy (代理模式):</strong> 为另一个对象提供一个替身或占位符以控制对这个对象的访问。</li>
                        <li><strong>Facade (外观模式):</strong> 为子系统中的一组接口提供一个统一的接口，简化复杂系统的使用。</li>
                    </ul>
                `;
                if (type === 'structural') {
                    explanationHtml += `<p>现在，点击“结构型模式”按钮，看看它的动画演示吧！</p>`;
                }
            }
            if (type === 'all') {
                explanationHtml += `
                    <h3>✨ 创建型模式 (Creational Patterns)</h3>
                    <p>这类模式主要关注<strong>对象如何被创建</strong>。它们封装了对象创建的复杂过程，让你不用关心对象是怎么来的，只需要直接使用就行。</p>
                    <p><strong>核心思想：</strong> 隐藏对象创建的复杂性，提供更灵活、可控的对象创建方式。</p>
                    <p><strong>典型代表：</strong></p>
                    <ul>
                        <li><strong>Singleton (单例模式):</strong> 确保一个类只有一个实例，并提供一个全局访问点，就像你家里只有一把唯一的钥匙。</li>
                        <li><strong>Factory Method (工厂方法模式):</strong> 定义一个创建对象的接口，但让子类决定实例化哪一个类。</li>
                        <li><strong>Abstract Factory (抽象工厂模式):</strong> 提供一个接口，用于创建相关或依赖对象的家族，而无需指定它们具体的类。</li>
                    </ul>
                    <p>现在，点击“创建型模式”按钮，看看它的动画演示吧！</p>
                `;
                explanationHtml += `
                    <h3>🔄 行为型模式 (Behavioral Patterns)</h3>
                    <p>这类模式主要关注对象之间<strong>如何协作和分配职责</strong>。它们描述了对象如何相互通信以及如何组织它们的行为，以完成复杂的任务。</p>
                    <p><strong>核心思想：</strong> 管理对象间的通信和职责分配，增强协作性。</p>
                    <p><strong>典型代表：</strong></p>
                    <ul>
                        <li><strong>Visitor (访问者模式):</strong> 表示一个作用于某对象结构中的各元素的操作。</li>
                        <li><strong>Strategy (策略模式):</strong> 定义一系列算法，并将它们封装起来，使它们可以互相替换。</li>
                        <li><strong>Observer (观察者模式):</strong> 定义对象间的一种一对多的依赖关系，当一个对象的状态发生改变时，所有依赖于它的对象都得到通知并自动更新。</li>
                    </ul>
                    <p>现在，点击“行为型模式”按钮，看看它的动画演示吧！</p>
                `;
            }
            patternExplanationDiv.innerHTML = explanationHtml;
        }

        // Canvas animation logic
        let animationId;
        const objects = [];
        const colors = ['#FFC107', '#2196F3', '#4CAF50', '#9C27B0', '#FF5722'];
        const creationalTexts = ["正在创建...", "新对象诞生！", "准备就绪！"];
        const structuralObjects = []; // For structural pattern animation
        const behavioralObjects = []; // For behavioral pattern animation

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawObject(obj) {
            ctx.fillStyle = obj.color;
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            if (obj.shape === 'circle') {
                ctx.beginPath();
                ctx.arc(obj.x, obj.y, obj.radius, 0, Math.PI * 2);
                ctx.fill();
                ctx.stroke();
            } else if (obj.shape === 'rect') {
                ctx.fillRect(obj.x - obj.width / 2, obj.y - obj.height / 2, obj.width, obj.height);
                ctx.strokeRect(obj.x - obj.width / 2, obj.y - obj.height / 2, obj.width, obj.height);
            } else if (obj.shape === 'text') {
                ctx.font = obj.font;
                ctx.textAlign = 'center';
                ctx.fillStyle = obj.color;
                ctx.fillText(obj.text, obj.x, obj.y);
            }
        }

        function animateCreational() {
            cancelAnimationFrame(animationId);
            clearCanvas();
            objects.length = 0; // Clear previous objects

            let step = 0;
            const maxObjects = 5;
            let currentTextIndex = 0;

            function createObjectAnimation() {
                if (objects.length < maxObjects) {
                    const newObj = {
                        x: canvas.width / 2,
                        y: canvas.height - 50,
                        radius: 10,
                        color: colors[objects.length % colors.length],
                        shape: 'circle',
                        targetY: 50 + objects.length * 70, // Stack objects
                        speed: 3
                    };
                    objects.push(newObj);
                }

                clearCanvas();
                // Draw current text
                ctx.font = '30px Arial';
                ctx.textAlign = 'center';
                ctx.fillStyle = '#333';
                ctx.fillText(creationalTexts[currentTextIndex], canvas.width / 2, 30);

                objects.forEach(obj => {
                    if (obj.y > obj.targetY) {
                        obj.y -= obj.speed;
                        if (obj.y < obj.targetY) obj.y = obj.targetY; // Prevent overshoot
                    }
                    drawObject(obj);
                });

                if (objects.length === maxObjects && objects.every(obj => obj.y <= obj.targetY)) {
                    // All objects created and in place, cycle text
                    currentTextIndex = (currentTextIndex + 1) % creationalTexts.length;
                    setTimeout(() => { // Restart animation after a short pause
                        objects.length = 0;
                        createObjectAnimation();
                    }, 1500);
                } else {
                    animationId = requestAnimationFrame(createObjectAnimation);
                }
            }
            createObjectAnimation();
        }

        function animateStructural() {
            cancelAnimationFrame(animationId);
            clearCanvas();
            structuralObjects.length = 0; // Clear previous objects

            // Create initial independent objects (rectangles)
            for (let i = 0; i < 3; i++) {
                structuralObjects.push({
                    x: 100 + i * 250,
                    y: canvas.height / 2,
                    width: 80,
                    height: 80,
                    color: colors[i % colors.length],
                    shape: 'rect',
                    state: 'independent' // 'independent', 'combining', 'combined'
                });
            }

            // Adapter object (circle)
            structuralObjects.push({
                x: canvas.width / 2,
                y: canvas.height / 2,
                radius: 40,
                color: '#FFD700', // Gold color for adapter
                shape: 'circle',
                state: 'adapter', // 'adapter'
                opacity: 0
            });

            let animationPhase = 0; // 0: independent, 1: adapter appears, 2: combining, 3: combined
            const initialX = [100, 350, 600];
            const targetX = [250, 400, 550]; // Target X for combined state

            function structuralAnimationLoop() {
                clearCanvas();

                // Draw text hints
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillStyle = '#333';

                if (animationPhase === 0) {
                    ctx.fillText("独立的对象们 (Independent Objects)", canvas.width / 2, 30);
                } else if (animationPhase === 1) {
                    ctx.fillText("引入“适配器” (Introducing Adapter)", canvas.width / 2, 30);
                } else if (animationPhase === 2) {
                    ctx.fillText("对象正在组合中... (Objects Combining...)", canvas.width / 2, 30);
                } else if (animationPhase === 3) {
                    ctx.fillText("组合完成！(Combined Structure!)", canvas.width / 2, 30);
                }


                structuralObjects.forEach(obj => {
                    if (obj.shape === 'rect') {
                        if (animationPhase === 2) { // Move towards combination
                            const index = initialX.indexOf(obj.x); // Find original index
                            if (index !== -1) {
                                const target = targetX[index];
                                if (Math.abs(obj.x - target) > 1) {
                                    obj.x += (target - obj.x) * 0.05;
                                }
                            }
                        }
                    } else if (obj.shape === 'circle' && obj.state === 'adapter') {
                        if (animationPhase === 1) {
                            obj.opacity += 0.02;
                            if (obj.opacity > 1) obj.opacity = 1;
                        } else if (animationPhase === 2 || animationPhase === 3) {
                             obj.opacity = 1; // Keep visible
                        }
                        ctx.save();
                        ctx.globalAlpha = obj.opacity;
                        drawObject(obj);
                        ctx.restore();
                    }
                    if(obj.shape !== 'circle' || (obj.shape === 'circle' && obj.opacity > 0)) { // Don't draw invisible adapter
                        drawObject(obj);
                    }
                });

                if (animationPhase === 0) {
                    setTimeout(() => animationPhase = 1, 2000); // Wait then introduce adapter
                } else if (animationPhase === 1 && structuralObjects[3].opacity >= 1) {
                    setTimeout(() => animationPhase = 2, 1000); // Wait then start combining
                } else if (animationPhase === 2 && structuralObjects.slice(0, 3).every((obj, i) => Math.abs(obj.x - targetX[i]) <= 2)) {
                    // All rects are close to their target positions
                    setTimeout(() => animationPhase = 3, 1000); // Wait then combined state
                } else if (animationPhase === 3) {
                    // Loop back after a pause
                    setTimeout(() => {
                        animationPhase = 0;
                        // Reset positions for rectangles for next loop
                        structuralObjects.forEach((obj, i) => {
                            if (obj.shape === 'rect') obj.x = initialX[i];
                        });
                        structuralObjects[3].opacity = 0; // Hide adapter
                    }, 3000);
                }

                animationId = requestAnimationFrame(structuralAnimationLoop);
            }
            structuralAnimationLoop();
        }


        function animateBehavioral() {
            cancelAnimationFrame(animationId);
            clearCanvas();
            behavioralObjects.length = 0;

            // Create interacting objects
            for (let i = 0; i < 4; i++) {
                behavioralObjects.push({
                    x: canvas.width / 2 + Math.cos(i * Math.PI / 2) * 150,
                    y: canvas.height / 2 + Math.sin(i * Math.PI / 2) * 100,
                    radius: 30,
                    color: colors[i % colors.length],
                    shape: 'circle',
                    direction: { x: (Math.random() - 0.5) * 2, y: (Math.random() - 0.5) * 2 },
                    state: 'active'
                });
            }

            let messageQueue = []; // To simulate messages between objects
            let currentMessage = null;
            let animationState = 'initial'; // 'initial', 'sending', 'received', 'loop'

            function drawArrow(x1, y1, x2, y2, color) {
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();

                // Arrowhead
                const headlen = 10;
                const angle = Math.atan2(y2 - y1, x2 - x1);
                ctx.lineTo(x2 - headlen * Math.cos(angle - Math.PI / 6), y2 - headlen * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(x2, y2);
                ctx.lineTo(x2 - headlen * Math.cos(angle + Math.PI / 6), y2 - headlen * Math.sin(angle + Math.PI / 6));
                ctx.stroke();
            }

            function behavioralAnimationLoop() {
                clearCanvas();

                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillStyle = '#333';
                ctx.fillText("对象之间协作与职责分配 (Collaboration & Responsibility)", canvas.width / 2, 30);


                behavioralObjects.forEach(obj => {
                    // Simple boundary collision
                    if (obj.x + obj.radius > canvas.width || obj.x - obj.radius < 0) obj.direction.x *= -1;
                    if (obj.y + obj.radius > canvas.height || obj.y - obj.radius < 0) obj.direction.y *= -1;

                    obj.x += obj.direction.x;
                    obj.y += obj.direction.y;

                    drawObject(obj);
                });

                if (animationState === 'initial' && !currentMessage) {
                    setTimeout(() => {
                        animationState = 'sending';
                        // Pick random sender and receiver
                        const sender = behavioralObjects[Math.floor(Math.random() * behavioralObjects.length)];
                        let receiver;
                        do {
                            receiver = behavioralObjects[Math.floor(Math.random() * behavioralObjects.length)];
                        } while (receiver === sender);

                        currentMessage = {
                            x: sender.x,
                            y: sender.y,
                            targetX: receiver.x,
                            targetY: receiver.y,
                            sender: sender,
                            receiver: receiver,
                            progress: 0, // 0 to 1
                            text: "发送消息"
                        };
                    }, 2000);
                }

                if (currentMessage) {
                    currentMessage.progress += 0.02; // Message speed
                    if (currentMessage.progress >= 1) {
                        // Message arrived
                        ctx.font = '25px Arial';
                        ctx.fillStyle = '#DC3545';
                        ctx.fillText("消息到达！", currentMessage.receiver.x, currentMessage.receiver.y - currentMessage.receiver.radius - 10);
                        animationState = 'received';
                        setTimeout(() => {
                            currentMessage = null;
                            animationState = 'initial'; // Reset for next message
                        }, 1000);
                    } else {
                        // Animate message moving
                        const msgX = currentMessage.x + (currentMessage.targetX - currentMessage.x) * currentMessage.progress;
                        const msgY = currentMessage.y + (currentMessage.targetY - currentMessage.y) * currentMessage.progress;

                        ctx.beginPath();
                        ctx.arc(msgX, msgY, 8, 0, Math.PI * 2);
                        ctx.fillStyle = 'purple';
                        ctx.fill();

                        ctx.font = '15px Arial';
                        ctx.fillStyle = '#555';
                        ctx.fillText(currentMessage.text, msgX, msgY - 15);
                        drawArrow(currentMessage.sender.x, currentMessage.sender.y, currentMessage.receiver.x, currentMessage.receiver.y, 'rgba(0, 0, 0, 0.2)');
                    }
                }

                animationId = requestAnimationFrame(behavioralAnimationLoop);
            }
            behavioralAnimationLoop();
        }

        // Event listeners for animation buttons
        showCreationalBtn.addEventListener('click', () => animateCreational());
        showStructuralBtn.addEventListener('click', () => animateStructural());
        showBehavioralBtn.addEventListener('click', () => animateBehavioral());

        // Initial animation
        animateCreational(); // Start with Creational as default
    </script>
</body>
</html> 