<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件方法学 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .blank {
            display: inline-block;
            min-width: 120px;
            height: 30px;
            border-bottom: 2px solid #667eea;
            margin: 0 5px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .blank:hover {
            border-bottom-color: #764ba2;
            transform: translateY(-2px);
        }

        .blank.filled {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            line-height: 30px;
            border-radius: 15px;
            border-bottom: none;
            animation: fillAnimation 0.5s ease-out;
        }

        .methods-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .method-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .method-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .method-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .method-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .method-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .canvas-container {
            width: 100%;
            height: 200px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .demo-canvas {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            width: 100%;
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(102, 126, 234, 0.3);
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 30px 0;
        }

        .option {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #4CAF50;
            background: #E8F5E8;
            animation: correctAnswer 0.6s ease-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: #FFEBEE;
            animation: wrongAnswer 0.6s ease-out;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fillAnimation {
            from { transform: scale(0.8); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongAnswer {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
            100% { transform: translateX(0); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.6);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">软件方法学</h1>
            <p class="subtitle">通过动画和交互学习三种开发方法</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                软件方法学是以软件开发方法为研究对象的学科。其中，
                <span class="blank" data-answer="自顶向下开发方法" id="blank1"></span>
                是先对最高层次中的问题进行定义、设计、编程和测试，而将其中未解决的问题作为一个子任务放到下一层次中去解决。
                <span class="blank" data-answer="自底向上开发方法" id="blank2"></span>
                是根据系统功能要求，从具体的器件、逻辑部件或者相似系统开始，通过对其进行相互连接、修改和扩大，构成所要求的系统。
                <span class="blank" data-answer="形式化开发方法" id="blank3"></span>
                是建立在严格数学基础上的软件开发方法。
            </div>
        </div>

        <div class="methods-container">
            <div class="method-card" data-method="topdown">
                <h3 class="method-title">🔝 自顶向下开发方法</h3>
                <p class="method-description">
                    从整体到局部，先解决主要问题，再逐步细化子问题。就像建房子，先设计整体结构，再细化每个房间。
                </p>
                <div class="canvas-container">
                    <canvas class="demo-canvas" id="topdownCanvas" width="400" height="200"></canvas>
                </div>
                <button class="interactive-btn" onclick="animateTopDown()">🎬 观看动画演示</button>
            </div>

            <div class="method-card" data-method="bottomup">
                <h3 class="method-title">🔺 自底向上开发方法</h3>
                <p class="method-description">
                    从基础组件开始，逐步组合成复杂系统。就像搭积木，先准备好各种零件，再组装成完整作品。
                </p>
                <div class="canvas-container">
                    <canvas class="demo-canvas" id="bottomupCanvas" width="400" height="200"></canvas>
                </div>
                <button class="interactive-btn" onclick="animateBottomUp()">🎬 观看动画演示</button>
            </div>

            <div class="method-card" data-method="formal">
                <h3 class="method-title">📐 形式化开发方法</h3>
                <p class="method-description">
                    基于严格的数学理论，用数学公式和逻辑来描述和验证软件系统，确保系统的正确性。
                </p>
                <div class="canvas-container">
                    <canvas class="demo-canvas" id="formalCanvas" width="400" height="200"></canvas>
                </div>
                <button class="interactive-btn" onclick="animateFormal()">🎬 观看动画演示</button>
            </div>
        </div>

        <div class="question-card">
            <h3 style="margin-bottom: 20px; color: #333;">🎯 测试你的理解</h3>
            <p style="margin-bottom: 20px; color: #666;">点击正确的选项来填空：</p>
            <div class="options-container" id="optionsContainer">
                <div class="option" data-value="自顶向下开发方法">A. 自顶向下开发方法</div>
                <div class="option" data-value="自底向上开发方法">B. 自底向上开发方法</div>
                <div class="option" data-value="形式化开发方法">C. 形式化开发方法</div>
                <div class="option" data-value="非形式化开发方法">D. 非形式化开发方法</div>
            </div>
        </div>
    </div>

    <script>
        // 创建浮动粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 游戏状态
        let currentBlank = 1;
        let correctAnswers = 0;
        const totalBlanks = 3;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            setupOptionListeners();
            updateProgress();
        });

        // 设置选项监听器
        function setupOptionListeners() {
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.addEventListener('click', function() {
                    const value = this.dataset.value;
                    const currentBlankElement = document.getElementById(`blank${currentBlank}`);
                    
                    if (currentBlankElement && currentBlankElement.dataset.answer === value) {
                        // 正确答案
                        this.classList.add('correct');
                        currentBlankElement.textContent = value;
                        currentBlankElement.classList.add('filled');
                        correctAnswers++;
                        currentBlank++;
                        
                        setTimeout(() => {
                            this.classList.remove('correct');
                            if (currentBlank <= totalBlanks) {
                                updateProgress();
                            } else {
                                showCompletion();
                            }
                        }, 1000);
                    } else {
                        // 错误答案
                        this.classList.add('wrong');
                        setTimeout(() => {
                            this.classList.remove('wrong');
                        }, 600);
                    }
                });
            });
        }

        // 更新进度条
        function updateProgress() {
            const progress = (correctAnswers / totalBlanks) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 显示完成效果
        function showCompletion() {
            updateProgress();
            setTimeout(() => {
                alert('🎉 恭喜！你已经掌握了软件方法学的三种开发方法！');
            }, 500);
        }

        // 自顶向下动画演示
        function animateTopDown() {
            const canvas = document.getElementById('topdownCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制层次结构
                const levels = [
                    { y: 30, width: 200, label: '整体系统', color: '#667eea' },
                    { y: 80, width: 120, label: '子系统', color: '#764ba2' },
                    { y: 130, width: 80, label: '模块', color: '#9575cd' },
                    { y: 180, width: 50, label: '函数', color: '#ba68c8' }
                ];

                levels.forEach((level, index) => {
                    if (step >= index) {
                        // 绘制矩形
                        ctx.fillStyle = level.color;
                        const x = (canvas.width - level.width) / 2;
                        ctx.fillRect(x, level.y - 15, level.width, 20);

                        // 绘制文字
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Microsoft YaHei';
                        ctx.textAlign = 'center';
                        ctx.fillText(level.label, canvas.width / 2, level.y - 2);

                        // 绘制箭头
                        if (index < levels.length - 1 && step > index) {
                            ctx.strokeStyle = '#333';
                            ctx.lineWidth = 2;
                            ctx.beginPath();
                            ctx.moveTo(canvas.width / 2, level.y + 8);
                            ctx.lineTo(canvas.width / 2, level.y + 25);
                            ctx.stroke();

                            // 箭头头部
                            ctx.beginPath();
                            ctx.moveTo(canvas.width / 2 - 5, level.y + 20);
                            ctx.lineTo(canvas.width / 2, level.y + 25);
                            ctx.lineTo(canvas.width / 2 + 5, level.y + 20);
                            ctx.stroke();
                        }
                    }
                });

                step++;
                if (step <= levels.length + 1) {
                    setTimeout(draw, 800);
                }
            }

            draw();
        }

        // 自底向上动画演示
        function animateBottomUp() {
            const canvas = document.getElementById('bottomupCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const components = [
                    { x: 50, y: 150, size: 30, label: '组件A', color: '#4CAF50' },
                    { x: 120, y: 150, size: 30, label: '组件B', color: '#2196F3' },
                    { x: 190, y: 150, size: 30, label: '组件C', color: '#FF9800' },
                    { x: 260, y: 150, size: 30, label: '组件D', color: '#E91E63' }
                ];

                // 绘制基础组件
                components.forEach((comp, index) => {
                    if (step >= index) {
                        ctx.fillStyle = comp.color;
                        ctx.fillRect(comp.x, comp.y, comp.size, comp.size);

                        ctx.fillStyle = 'white';
                        ctx.font = '10px Microsoft YaHei';
                        ctx.textAlign = 'center';
                        ctx.fillText(comp.label, comp.x + comp.size/2, comp.y + comp.size/2 + 3);
                    }
                });

                // 组合阶段
                if (step >= 4) {
                    // 绘制组合箭头
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    components.forEach(comp => {
                        ctx.beginPath();
                        ctx.moveTo(comp.x + comp.size/2, comp.y);
                        ctx.lineTo(comp.x + comp.size/2, comp.y - 30);
                        ctx.stroke();
                    });
                }

                if (step >= 5) {
                    // 绘制组合后的系统
                    ctx.fillStyle = '#667eea';
                    ctx.fillRect(80, 80, 160, 40);

                    ctx.fillStyle = 'white';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('完整系统', 160, 105);
                }

                step++;
                if (step <= 6) {
                    setTimeout(draw, 1000);
                }
            }

            draw();
        }

        // 形式化方法动画演示
        function animateFormal() {
            const canvas = document.getElementById('formalCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const stages = [
                    { text: '数学规约', formula: 'P(x) → Q(x)', y: 40 },
                    { text: '形式验证', formula: '∀x: P(x) ⊢ Q(x)', y: 80 },
                    { text: '代码生成', formula: 'if P(x) then Q(x)', y: 120 },
                    { text: '测试验证', formula: 'Test: P(x) ✓ Q(x)', y: 160 }
                ];

                stages.forEach((stage, index) => {
                    if (step >= index) {
                        // 绘制阶段标题
                        ctx.fillStyle = '#333';
                        ctx.font = 'bold 14px Microsoft YaHei';
                        ctx.textAlign = 'left';
                        ctx.fillText(stage.text, 20, stage.y);

                        // 绘制数学公式
                        ctx.fillStyle = '#667eea';
                        ctx.font = '12px Courier New';
                        ctx.fillText(stage.formula, 120, stage.y);

                        // 绘制连接线
                        if (index < stages.length - 1 && step > index) {
                            ctx.strokeStyle = '#764ba2';
                            ctx.lineWidth = 2;
                            ctx.beginPath();
                            ctx.moveTo(300, stage.y + 5);
                            ctx.lineTo(320, stage.y + 5);
                            ctx.lineTo(320, stage.y + 25);
                            ctx.lineTo(300, stage.y + 25);
                            ctx.stroke();
                        }
                    }
                });

                step++;
                if (step <= stages.length + 1) {
                    setTimeout(draw, 1200);
                }
            }

            draw();
        }
    </script>
</body>
</html>
