<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库设计知识问答</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 20px;
        }

        h1, h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
        }

        .question-section {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }

        .question-text {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #34495e;
        }

        .options label {
            display: block;
            margin-bottom: 15px;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s, border-color 0.2s;
        }

        .options label:hover {
            background-color: #e8f0fe;
            border-color: #a7d0fd;
        }

        .options input[type="radio"] {
            margin-right: 10px;
            transform: scale(1.2);
            vertical-align: middle;
        }

        .submit-btn {
            display: block;
            width: 200px;
            padding: 12px 20px;
            margin: 20px auto 0;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .submit-btn:hover {
            background-color: #2980b9;
        }

        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            display: none; /* Hidden by default */
        }

        .feedback.correct {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .explanation-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .explanation-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }

        .knowledge-point {
            background-color: #f9f9f9;
            padding: 15px;
            border-left: 5px solid #3498db;
            margin-bottom: 15px;
            border-radius: 5px;
        }
        
        canvas {
            border: 1px solid #ccc;
            background-color: #fff;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
        }

        .animation-controls {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        .animation-controls button {
            padding: 10px 20px;
            font-size: 1em;
            margin: 0 10px;
            background-color: #2ecc71;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .animation-controls button:hover {
            background-color: #27ae60;
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const submitBtn = document.getElementById('submit-answer');
            const feedbackDiv = document.getElementById('feedback');
            const showExplanationBtn = document.getElementById('show-explanation');
            const explanationSection = document.getElementById('explanation-section');
            const startAnimationBtn = document.getElementById('start-animation');
            const nextStepBtn = document.getElementById('next-step');
            const prevStepBtn = document.getElementById('prev-step');
            const canvas = document.getElementById('designCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 800;
            canvas.height = 400;

            let animationStep = 0;
            const maxAnimationSteps = 3; // Total steps for the main animation

            // Function to draw text on canvas
            function drawText(text, x, y, color = '#333', font = '18px Arial') {
                ctx.fillStyle = color;
                ctx.font = font;
                ctx.textAlign = 'center';
                ctx.fillText(text, x, y);
            }

            // Function to draw a rounded rectangle
            function drawRoundedRect(x, y, width, height, radius, fillColor, strokeColor) {
                ctx.beginPath();
                ctx.moveTo(x + radius, y);
                ctx.lineTo(x + width - radius, y);
                ctx.qBozicCurveTo(x + width, y, x + width, y + radius);
                ctx.lineTo(x + width, y + height - radius);
                ctx.qBozicCurveTo(x + width, y + height, x + width - radius, y + height);
                ctx.lineTo(x + radius, y + height);
                ctx.qBozicCurveTo(x, y + height, x, y + height - radius);
                ctx.lineTo(x, y + radius);
                ctx.qBozicCurveTo(x, y, x + radius, y);
                ctx.closePath();
                if (fillColor) {
                    ctx.fillStyle = fillColor;
                    ctx.fill();
                }
                if (strokeColor) {
                    ctx.strokeStyle = strokeColor;
                    ctx.lineWidth = 2;
                    ctx.stroke();
                }
            }

            // Function to draw an arrow
            function drawArrow(fromX, fromY, toX, toY, color = '#3498db') {
                const headlen = 10; // length of head in pixels
                const angle = Math.atan2(toY - fromY, toX - fromX);
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(toX, toY);
                ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
                ctx.stroke();
            }

            // Animation rendering function
            function renderAnimation() {
                ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear canvas

                // Define common elements
                const boxWidth = 150;
                const boxHeight = 60;
                const phaseY = canvas.height / 2 - boxHeight / 2 - 50; // Y for phases
                const docY = canvas.height / 2 + 50; // Y for documents

                // Phase 1: 数据库设计阶段 (Database Design Phases)
                // Draw all phases faintly initially
                const phaseColors = ['#e0f7fa', '#e0f2f7', '#e3f2fd', '#e8f5e9'];
                const phaseStrokeColors = ['#00bcd4', '#03a9f4', '#2196f3', '#4caf50'];
                const phases = [
                    { name: '用户需求分析', x: 80 },
                    { name: '概念结构设计', x: 250 },
                    { name: '逻辑结构设计', x: 420 },
                    { name: '物理结构设计', x: 590 }
                ];

                phases.forEach((phase, index) => {
                    drawRoundedRect(phase.x, phaseY, boxWidth, boxHeight, 10, phaseColors[index], phaseStrokeColors[index]);
                    drawText(phase.name, phase.x + boxWidth / 2, phaseY + boxHeight / 2 + 5, '#555', '16px Arial');
                });

                // Arrows between phases
                for (let i = 0; i < phases.length - 1; i++) {
                    drawArrow(phases[i].x + boxWidth, phaseY + boxHeight / 2, phases[i+1].x, phaseY + boxHeight / 2, '#999');
                }

                // Animation Step Logic
                if (animationStep >= 0) {
                    // Highlight "用户需求分析"
                    drawRoundedRect(phases[0].x, phaseY, boxWidth, boxHeight, 10, '#ccf', '#3498db');
                    drawText(phases[0].name, phases[0].x + boxWidth / 2, phaseY + boxHeight / 2 + 5, '#000', '16px Arial');
                    drawText('第一阶段：用户需求分析', canvas.width / 2, 30, '#333', '24px Arial');
                    drawText('分析用户需求，为数据库设计打基础', canvas.width / 2, 60, '#555', '16px Arial');
                }

                if (animationStep >= 1) {
                    // Show documents formed in Requirement Analysis
                    const docColors = ['#ffe0b2', '#c8e6c9', '#bbdefb'];
                    const docStrokeColors = ['#ff9800', '#4caf50', '#2196f3'];
                    const documents = [
                        { name: '需求说明文档', x: 150 },
                        { name: '数据字典', x: 350 },
                        { name: '数据流图', x: 550 }
                    ];

                    documents.forEach((doc, index) => {
                        drawRoundedRect(doc.x, docY, boxWidth, boxHeight, 10, docColors[index], docStrokeColors[index]);
                        drawText(doc.name, doc.x + boxWidth / 2, docY + boxHeight / 2 + 5, '#333', '16px Arial');
                    });
                    drawArrow(phases[0].x + boxWidth / 2, phaseY + boxHeight, documents[1].x + boxWidth / 2, docY, '#3498db');
                    drawText('该阶段产出三种核心文档', canvas.width / 2, docY - 20, '#555', '16px Arial');
                }

                if (animationStep >= 2) {
                    // Show flow to Conceptual Structure Design
                    drawArrow(documents[1].x + boxWidth / 2, docY + boxHeight, phases[1].x + boxWidth / 2, phaseY + boxHeight, '#27ae60');
                    drawText('这些文档将作为概念结构设计的依据', canvas.width / 2, docY + boxHeight + 40, '#27ae60', '16px Arial');

                    // Highlight "概念结构设计"
                    drawRoundedRect(phases[1].x, phaseY, boxWidth, boxHeight, 10, '#ccf', '#3498db');
                    drawText(phases[1].name, phases[1].x + boxWidth / 2, phaseY + boxHeight / 2 + 5, '#000', '16px Arial');
                }

                if (animationStep === maxAnimationSteps) {
                    drawText('动画结束！', canvas.width / 2, canvas.height - 20, '#e74c3c', '20px Arial');
                }
            }

            // Event Listeners for controls
            startAnimationBtn.addEventListener('click', () => {
                animationStep = 0;
                renderAnimation();
                nextStepBtn.disabled = false;
                prevStepBtn.disabled = true;
            });

            nextStepBtn.addEventListener('click', () => {
                if (animationStep < maxAnimationSteps) {
                    animationStep++;
                    renderAnimation();
                    prevStepBtn.disabled = false;
                }
                if (animationStep === maxAnimationSteps) {
                    nextStepBtn.disabled = true;
                }
            });

            prevStepBtn.addEventListener('click', () => {
                if (animationStep > 0) {
                    animationStep--;
                    renderAnimation();
                    nextStepBtn.disabled = false;
                }
                if (animationStep === 0) {
                    prevStepBtn.disabled = true;
                }
            });

            submitBtn.addEventListener('click', () => {
                const selectedOption = document.querySelector('input[name="question1"]:checked');
                feedbackDiv.style.display = 'block';
                if (selectedOption && selectedOption.value === 'C') {
                    feedbackDiv.className = 'feedback correct';
                    feedbackDiv.textContent = '恭喜你，回答正确！';
                    showExplanationBtn.style.display = 'block';
                } else {
                    feedbackDiv.className = 'feedback incorrect';
                    feedbackDiv.textContent = '抱歉，回答错误。请再思考一下，或点击下方的"显示详细解释"按钮学习！';
                    showExplanationBtn.style.display = 'block'; // Always show explanation after attempt
                }
            });

            showExplanationBtn.addEventListener('click', () => {
                explanationSection.style.display = 'block';
                showExplanationBtn.style.display = 'none'; // Hide the button after showing explanation
                // Start initial animation render
                animationStep = 0;
                renderAnimation();
                prevStepBtn.disabled = true;
            });

            // Initial state: hide explanation and feedback
            explanationSection.style.display = 'none';
            feedbackDiv.style.display = 'none';
            showExplanationBtn.style.display = 'none';
            nextStepBtn.disabled = false; // Enable next step for initial animation
            prevStepBtn.disabled = true; // Disable previous step initially
        });
    </script>
</head>
<body>
    <div class="container">
        <h1>数据库设计知识问答</h1>

        <div class="question-section">
            <p class="question-text">在数据库设计的需求分析阶段应该形成 ( )，这些文档可以作为 ( ) 阶段的设计依据。</p>
            <div class="options">
                <label>
                    <input type="radio" name="question1" value="A"> A. 程序文档、数据字典和数据流图
                </label>
                <label>
                    <input type="radio" name="question1" value="B"> B. 需求说明文档、程序文档和数据流图
                </label>
                <label>
                    <input type="radio" name="question1" value="C"> C. 需求说明文档、数据字典和数据流图
                </label>
                <label>
                    <input type="radio" name="question1" value="D"> D. 需求说明文档、数据字典和程序文档
                </label>
            </div>
            <button class="submit-btn" id="submit-answer">提交答案</button>
            <div class="feedback" id="feedback"></div>
            <button class="submit-btn" id="show-explanation" style="display: none;">显示详细解释</button>
        </div>

        <div class="explanation-section" id="explanation-section">
            <h2>知识点解析与动画演示</h2>
            <p>这个题目考察的是数据库设计中的核心阶段和每个阶段的产物。</gt;</p>

            <div class="knowledge-point">
                <h3>第一部分：数据库设计有哪些阶段？</h3>
                <p>数据库设计通常分为以下四个主要阶段：</p>
                <ul>
                    <li><strong>用户需求分析：</strong> 收集、分析用户对数据库的需求。</li>
                    <li><strong>概念结构设计：</strong> 将需求抽象成独立于具体数据库管理系统的概念模型，常用E-R图。</li>
                    <li><strong>逻辑结构设计：</strong> 将概念模型转换成特定数据库管理系统（如关系型数据库）支持的数据模型。</li>
                    <li><strong>物理结构设计：</strong> 考虑数据库在物理存储上的细节，如存储方式、索引等。</li>
                </ul>
                <p><strong>动画演示：数据库设计流程</strong></p>
            </div>

            <div class="animation-controls">
                <button id="start-animation">重新开始动画</button>
                <button id="prev-step">上一步</button>
                <button id="next-step">下一步</button>
            </div>
            <canvas id="designCanvas"></canvas>
            <p style="text-align: center; margin-top: 10px; font-style: italic;">（点击"下一步"观看动画演示）</p>

            <div class="knowledge-point">
                <h3>第二部分：需求分析阶段产生什么文档？</h3>
                <p>在<strong>用户需求分析阶段</strong>，数据库设计人员会深入了解应用系统的功能、性能和限制，并形成以下关键文档：</p>
                <ul>
                    <li><strong>需求说明文档：</strong> 详细描述了用户的功能和非功能需求，是整个项目的基础。</li>
                    <li><strong>数据字典：</strong> 记录了数据库中所有数据项的详细信息，如数据项名称、含义、数据类型、取值范围等，是理解数据的重要工具。</li>
                    <li><strong>数据流图 (DFD)：</strong> 用图形方式描绘数据在系统中的流动和处理过程，帮助理解系统功能。</li>
                </ul>
            </div>

            <div class="knowledge-point">
                <h3>第三部分：这些文档用于哪个阶段？</h3>
                <p>需求分析阶段形成的<strong>需求说明文档、数据字典和数据流图</strong>是后续设计阶段的重要依据。</p>
                <p>它们主要作为<strong>概念结构设计</strong>阶段的输入，帮助设计人员构建出反映真实世界实体的概念模型（如E-R图），确保数据库设计符合用户实际需求。</p>
            </div>

            <div class="knowledge-point">
                <h3>总结：</h3>
                <p>因此，正确答案是：在数据库设计的需求分析阶段应该形成 <strong>需求说明文档、数据字典和数据流图</strong>，这些文档可以作为 <strong>概念结构设计</strong> 阶段的设计依据。</p>
            </div>
        </div>
    </div>
</body>
</html> 