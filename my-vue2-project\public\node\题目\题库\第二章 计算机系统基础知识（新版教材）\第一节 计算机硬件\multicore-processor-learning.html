<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多核处理器互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #555;
        }

        .quiz-container {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            color: white;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: rgba(255,255,255,0.2);
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(5px);
        }

        .quiz-option.selected {
            border-color: white;
            background: rgba(255,255,255,0.4);
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .quiz-option.incorrect {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .score-display {
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 20px 0;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title floating">🖥️ 多核处理器学习之旅</h1>
            <p class="subtitle">通过动画和交互，轻松掌握多核处理器知识</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是多核处理器？</h2>
            <div class="canvas-container">
                <canvas id="processorCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="animateProcessorEvolution()">🔄 处理器演进动画</button>
                <button class="btn" onclick="showCoreComparison()">⚖️ 单核vs多核对比</button>
            </div>
            <div class="explanation">
                <strong>💡 核心概念：</strong>多核处理器是将两个或更多的独立处理器（CPU核心）封装在一起，集成在一个电路中的技术。就像把多个大脑放在一个头颅里，可以同时思考多个问题！
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🏗️ 多核处理器的三种架构</h2>
            <div class="canvas-container">
                <canvas id="architectureCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showSMP()">🔗 SMP模式</button>
                <button class="btn" onclick="showBMP()">🎯 BMP模式</button>
                <button class="btn" onclick="showAMP()">⚡ AMP模式</button>
                <button class="btn" onclick="compareArchitectures()">📊 架构对比</button>
            </div>
            <div class="explanation">
                <strong>🎨 架构说明：</strong>
                <br>• <strong>SMP (对称多处理)</strong>：所有核心地位平等，共享内存和资源
                <br>• <strong>BMP (绑定多处理)</strong>：特定任务绑定到特定核心
                <br>• <strong>AMP (非对称多处理)</strong>：有主核心和从核心的层级结构
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚡ 多核处理器的优势</h2>
            <div class="canvas-container">
                <canvas id="benefitsCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showPowerEfficiency()">🔋 功耗优势</button>
                <button class="btn" onclick="showParallelProcessing()">🚀 并行处理</button>
                <button class="btn" onclick="showSizeAdvantage()">📦 体积优势</button>
            </div>
            <div class="explanation">
                <strong>✨ 主要优势：</strong>
                <br>• <strong>降低功耗</strong>：多个低频核心比单个高频核心更节能
                <br>• <strong>减小体积</strong>：集成设计比多个独立处理器更紧凑
                <br>• <strong>并行处理</strong>：可同时执行多个进程和线程
            </div>
        </div>

        <div class="quiz-container">
            <h2 class="section-title" style="color: white;">🎮 知识测验</h2>
            <div class="score-display">得分: <span id="score">0</span>/100</div>

            <div class="quiz-question" id="quizQuestion">
                以下关于多核处理器的说法中，不正确的是？
            </div>

            <div class="quiz-options" id="quizOptions">
                <div class="quiz-option" onclick="selectOption(this, false)">
                    A. 采用多核处理器可以降低计算机系统的功耗和体积
                </div>
                <div class="quiz-option" onclick="selectOption(this, true)">
                    B. SMP、BMP和AMP是多核处理器系统通常采用的三种结构，采用哪种结构与应用场景相关，而无须考虑硬件的组成差异
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    C. 在多核处理器中，计算机可以同时执行多个进程，而操作系统中的多个线程也可以并行执行
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    D. 多核处理器是将两个或更多的独立处理器封装在一起，集成在一个电路中
                </div>
            </div>

            <div class="explanation" id="quizExplanation" style="display: none; background: rgba(255,255,255,0.9); color: #333;">
                <strong>📚 详细解析：</strong>
                <br>选项B是错误的。虽然SMP、BMP和AMP确实与应用场景相关，但选择哪种架构<strong>必须考虑硬件的组成差异</strong>。不同的硬件配置决定了能够支持哪种多核架构模式。
                <br><br>
                <strong>🔍 其他选项解释：</strong>
                <br>• A正确：多核设计确实能降低功耗和体积
                <br>• C正确：多核支持真正的并行处理
                <br>• D正确：这是多核处理器的基本定义
            </div>
        </div>
    </div>

    <script>
        let score = 0;
        let currentProgress = 0;

        // 更新进度条
        function updateProgress(progress) {
            currentProgress = Math.min(progress, 100);
            document.getElementById('progressFill').style.width = currentProgress + '%';
        }

        // 处理器演进动画
        function animateProcessorEvolution() {
            const canvas = document.getElementById('processorCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#f0f2ff');
                gradient.addColorStop(1, '#e6e9ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 单核处理器 (左侧)
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(50, 150, 120, 100);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('单核CPU', 110, 205);

                // 箭头动画
                const arrowX = 200 + Math.sin(frame * 0.1) * 10;
                ctx.fillStyle = '#4ecdc4';
                ctx.beginPath();
                ctx.moveTo(arrowX, 190);
                ctx.lineTo(arrowX + 30, 180);
                ctx.lineTo(arrowX + 30, 200);
                ctx.closePath();
                ctx.fill();

                // 多核处理器 (右侧)
                const colors = ['#4ecdc4', '#45b7aa', '#96ceb4', '#feca57'];
                for (let i = 0; i < 4; i++) {
                    const x = 300 + (i % 2) * 80;
                    const y = 130 + Math.floor(i / 2) * 80;
                    const scale = 0.8 + Math.sin(frame * 0.05 + i) * 0.1;

                    ctx.save();
                    ctx.translate(x + 35, y + 35);
                    ctx.scale(scale, scale);
                    ctx.fillStyle = colors[i];
                    ctx.fillRect(-35, -35, 70, 70);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.fillText(`核心${i+1}`, 0, 5);
                    ctx.restore();
                }

                // 标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 24px Arial';
                ctx.fillText('处理器演进：单核 → 多核', canvas.width/2, 50);

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress(25);
                }
            }
            animate();
        }

        // 单核vs多核对比
        function showCoreComparison() {
            const canvas = document.getElementById('processorCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#fff5f5');
                gradient.addColorStop(1, '#f0fff4');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 任务队列
                const tasks = ['任务1', '任务2', '任务3', '任务4'];

                // 单核处理 (左侧)
                ctx.fillStyle = '#333';
                ctx.font = 'bold 18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('单核处理器', 200, 30);

                // 单核CPU
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(150, 60, 100, 60);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText('CPU', 200, 95);

                // 单核任务处理动画
                const currentTask = Math.floor(frame / 30) % 4;
                for (let i = 0; i < 4; i++) {
                    ctx.fillStyle = i === currentTask ? '#feca57' : '#ddd';
                    ctx.fillRect(120 + i * 25, 140, 20, 20);
                    ctx.fillStyle = '#333';
                    ctx.font = '10px Arial';
                    ctx.fillText(`T${i+1}`, 130 + i * 25, 153);
                }

                // 多核处理 (右侧)
                ctx.fillStyle = '#333';
                ctx.font = 'bold 18px Arial';
                ctx.fillText('多核处理器', 600, 30);

                // 多核CPU
                const coreColors = ['#4ecdc4', '#45b7aa', '#96ceb4', '#feca57'];
                for (let i = 0; i < 4; i++) {
                    const x = 550 + (i % 2) * 60;
                    const y = 60 + Math.floor(i / 2) * 40;
                    ctx.fillStyle = coreColors[i];
                    ctx.fillRect(x, y, 50, 30);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 10px Arial';
                    ctx.fillText(`核${i+1}`, x + 25, y + 20);
                }

                // 多核并行处理动画
                for (let i = 0; i < 4; i++) {
                    const x = 550 + (i % 2) * 60;
                    const y = 140;
                    const taskActive = (frame + i * 10) % 60 < 30;
                    ctx.fillStyle = taskActive ? coreColors[i] : '#ddd';
                    ctx.fillRect(x, y, 50, 20);
                    ctx.fillStyle = '#333';
                    ctx.font = '10px Arial';
                    ctx.fillText(`任务${i+1}`, x + 25, y + 13);
                }

                // 性能对比
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('处理速度对比', canvas.width/2, 220);

                // 进度条
                const singleProgress = Math.min((frame % 120) / 120, 1);
                const multiProgress = Math.min((frame % 30) / 30, 1);

                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(100, 250, singleProgress * 200, 20);
                ctx.fillStyle = '#4ecdc4';
                ctx.fillRect(500, 250, multiProgress * 200, 20);

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText('单核：串行处理', 200, 290);
                ctx.fillText('多核：并行处理', 600, 290);

                frame++;
                if (frame < 240) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress(50);
                }
            }
            animate();
        }

        // SMP架构演示
        function showSMP() {
            const canvas = document.getElementById('architectureCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#e8f5e8');
                gradient.addColorStop(1, '#f0fff0');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2e7d32';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('SMP - 对称多处理架构', canvas.width/2, 40);

                // 共享内存
                ctx.fillStyle = '#81c784';
                ctx.fillRect(300, 80, 200, 60);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('共享内存', 400, 115);

                // CPU核心
                const coreColors = ['#4caf50', '#66bb6a', '#81c784', '#a5d6a7'];
                for (let i = 0; i < 4; i++) {
                    const x = 200 + i * 100;
                    const y = 200;
                    const pulse = 1 + Math.sin(frame * 0.1 + i) * 0.1;

                    ctx.save();
                    ctx.translate(x + 40, y + 40);
                    ctx.scale(pulse, pulse);
                    ctx.fillStyle = coreColors[i];
                    ctx.fillRect(-40, -40, 80, 80);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 14px Arial';
                    ctx.fillText(`CPU${i+1}`, 0, 5);
                    ctx.restore();

                    // 连接线到共享内存
                    ctx.strokeStyle = '#4caf50';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(x + 40, y);
                    ctx.lineTo(400, 140);
                    ctx.stroke();
                }

                // 特点说明
                ctx.fillStyle = '#2e7d32';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('• 所有CPU核心地位平等', 50, 350);
                ctx.fillText('• 共享同一块内存空间', 50, 380);
                ctx.fillText('• 任何核心都可以访问任何内存位置', 50, 410);
                ctx.fillText('• 适合通用计算任务', 50, 440);

                frame++;
                if (frame < 100) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress(65);
                }
            }
            animate();
        }

        // BMP架构演示
        function showBMP() {
            const canvas = document.getElementById('architectureCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#e3f2fd');
                gradient.addColorStop(1, '#f0f8ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#1976d2';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('BMP - 绑定多处理架构', canvas.width/2, 40);

                // CPU核心和专用任务
                const tasks = ['图形处理', '音频处理', '网络处理', '系统管理'];
                const colors = ['#2196f3', '#03a9f4', '#00bcd4', '#009688'];

                for (let i = 0; i < 4; i++) {
                    const x = 150 + i * 150;
                    const y = 150;
                    const pulse = 1 + Math.sin(frame * 0.15 + i * 0.5) * 0.1;

                    // CPU核心
                    ctx.save();
                    ctx.translate(x, y);
                    ctx.scale(pulse, pulse);
                    ctx.fillStyle = colors[i];
                    ctx.fillRect(-40, -40, 80, 80);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`CPU${i+1}`, 0, -10);
                    ctx.restore();

                    // 专用任务
                    ctx.fillStyle = colors[i];
                    ctx.fillRect(x - 50, y + 80, 100, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(tasks[i], x, y + 105);

                    // 绑定线
                    ctx.strokeStyle = colors[i];
                    ctx.lineWidth = 4;
                    ctx.beginPath();
                    ctx.moveTo(x, y + 40);
                    ctx.lineTo(x, y + 80);
                    ctx.stroke();
                }

                // 特点说明
                ctx.fillStyle = '#1976d2';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('• 特定任务绑定到特定CPU核心', 50, 300);
                ctx.fillText('• 每个核心专门处理某类任务', 50, 330);
                ctx.fillText('• 提高特定应用的性能', 50, 360);
                ctx.fillText('• 适合专业化工作负载', 50, 390);

                frame++;
                if (frame < 100) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress(75);
                }
            }
            animate();
        }

        // AMP架构演示
        function showAMP() {
            const canvas = document.getElementById('architectureCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#fff3e0');
                gradient.addColorStop(1, '#ffeaa7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#f57c00';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('AMP - 非对称多处理架构', canvas.width/2, 40);

                // 主CPU核心
                const masterPulse = 1 + Math.sin(frame * 0.2) * 0.15;
                ctx.save();
                ctx.translate(400, 120);
                ctx.scale(masterPulse, masterPulse);
                ctx.fillStyle = '#ff9800';
                ctx.fillRect(-50, -30, 100, 60);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('主CPU', 0, 5);
                ctx.restore();

                // 从CPU核心
                const slaveColors = ['#ffb74d', '#ffcc02', '#ffd54f'];
                for (let i = 0; i < 3; i++) {
                    const angle = (i * 120 + frame * 2) * Math.PI / 180;
                    const x = 400 + Math.cos(angle) * 120;
                    const y = 250 + Math.sin(angle) * 60;
                    const pulse = 0.8 + Math.sin(frame * 0.1 + i) * 0.1;

                    ctx.save();
                    ctx.translate(x, y);
                    ctx.scale(pulse, pulse);
                    ctx.fillStyle = slaveColors[i];
                    ctx.fillRect(-35, -25, 70, 50);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.fillText(`从CPU${i+1}`, 0, 5);
                    ctx.restore();

                    // 连接线
                    ctx.strokeStyle = '#ff9800';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(400, 150);
                    ctx.lineTo(x, y - 25);
                    ctx.stroke();

                    // 箭头
                    const arrowAngle = Math.atan2(y - 175, x - 400);
                    ctx.save();
                    ctx.translate(x - Math.cos(arrowAngle) * 35, y - 25 - Math.sin(arrowAngle) * 35);
                    ctx.rotate(arrowAngle);
                    ctx.fillStyle = '#ff9800';
                    ctx.beginPath();
                    ctx.moveTo(0, 0);
                    ctx.lineTo(-10, -5);
                    ctx.lineTo(-10, 5);
                    ctx.closePath();
                    ctx.fill();
                    ctx.restore();
                }

                // 特点说明
                ctx.fillStyle = '#f57c00';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('• 有主CPU和从CPU的层级结构', 50, 380);
                ctx.fillText('• 主CPU负责系统管理和任务分配', 50, 410);
                ctx.fillText('• 从CPU执行主CPU分配的任务', 50, 440);
                ctx.fillText('• 适合嵌入式和实时系统', 50, 470);

                frame++;
                if (frame < 150) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress(85);
                }
            }
            animate();
        }

        // 架构对比
        function compareArchitectures() {
            const canvas = document.getElementById('architectureCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('多核架构对比', canvas.width/2, 40);

            // 三种架构对比
            const architectures = [
                { name: 'SMP', color: '#4caf50', x: 130 },
                { name: 'BMP', color: '#2196f3', x: 400 },
                { name: 'AMP', color: '#ff9800', x: 670 }
            ];

            architectures.forEach((arch, index) => {
                // 架构名称
                ctx.fillStyle = arch.color;
                ctx.font = 'bold 20px Arial';
                ctx.fillText(arch.name, arch.x, 80);

                // 简化图示
                if (arch.name === 'SMP') {
                    // SMP - 平等的核心
                    for (let i = 0; i < 4; i++) {
                        ctx.fillStyle = arch.color;
                        ctx.fillRect(arch.x - 60 + i * 30, 100, 25, 25);
                    }
                } else if (arch.name === 'BMP') {
                    // BMP - 绑定的核心
                    for (let i = 0; i < 4; i++) {
                        ctx.fillStyle = arch.color;
                        ctx.fillRect(arch.x - 60 + i * 30, 100, 25, 25);
                        ctx.fillRect(arch.x - 60 + i * 30, 130, 25, 15);
                    }
                } else {
                    // AMP - 主从结构
                    ctx.fillStyle = arch.color;
                    ctx.fillRect(arch.x - 15, 90, 30, 30); // 主核心
                    for (let i = 0; i < 3; i++) {
                        ctx.fillRect(arch.x - 45 + i * 30, 130, 20, 20); // 从核心
                    }
                }

                // 特点
                ctx.fillStyle = '#666';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                const features = [
                    ['平等共享', '通用计算', '易编程'],
                    ['任务绑定', '专业优化', '高效率'],
                    ['主从结构', '实时性好', '功耗低']
                ];

                features[index].forEach((feature, i) => {
                    ctx.fillText(feature, arch.x, 180 + i * 20);
                });
            });

            updateProgress(95);
        }

        // 功耗优势演示
        function showPowerEfficiency() {
            const canvas = document.getElementById('benefitsCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#e8f5e8');
                gradient.addColorStop(1, '#f0fff0');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2e7d32';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🔋 功耗对比：单核 vs 多核', canvas.width/2, 40);

                // 单核高频处理器
                ctx.fillStyle = '#f44336';
                ctx.fillRect(150, 100, 120, 80);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText('单核 3.5GHz', 210, 145);

                // 功耗指示器 - 单核
                const singlePower = 80 + Math.sin(frame * 0.2) * 20;
                ctx.fillStyle = '#ff5722';
                ctx.fillRect(120, 200, 180, 20);
                ctx.fillStyle = '#ffeb3b';
                ctx.fillRect(120, 200, (singlePower / 100) * 180, 20);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText(`功耗: ${singlePower.toFixed(0)}W`, 210, 235);

                // 多核低频处理器
                const coreColors = ['#4caf50', '#66bb6a', '#81c784', '#a5d6a7'];
                for (let i = 0; i < 4; i++) {
                    const x = 450 + (i % 2) * 70;
                    const y = 100 + Math.floor(i / 2) * 50;
                    ctx.fillStyle = coreColors[i];
                    ctx.fillRect(x, y, 60, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 10px Arial';
                    ctx.fillText('1.8GHz', x + 30, y + 25);
                }

                // 功耗指示器 - 多核
                const multiPower = 45 + Math.sin(frame * 0.15) * 10;
                ctx.fillStyle = '#4caf50';
                ctx.fillRect(450, 200, 180, 20);
                ctx.fillStyle = '#8bc34a';
                ctx.fillRect(450, 200, (multiPower / 100) * 180, 20);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText(`功耗: ${multiPower.toFixed(0)}W`, 540, 235);

                // 效率说明
                ctx.fillStyle = '#2e7d32';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('多核设计通过降低频率减少功耗，同时保持性能', canvas.width/2, 280);

                frame++;
                if (frame < 120) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 并行处理演示
        function showParallelProcessing() {
            const canvas = document.getElementById('benefitsCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#e3f2fd');
                gradient.addColorStop(1, '#f0f8ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#1976d2';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🚀 并行处理能力', canvas.width/2, 40);

                // 进程和线程
                const processes = ['进程A', '进程B', '进程C', '进程D'];
                const threads = ['线程1', '线程2', '线程3', '线程4'];
                const colors = ['#2196f3', '#03a9f4', '#00bcd4', '#009688'];

                for (let i = 0; i < 4; i++) {
                    const x = 150 + i * 150;
                    const y = 100;

                    // CPU核心
                    ctx.fillStyle = colors[i];
                    ctx.fillRect(x - 40, y, 80, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.fillText(`核心${i+1}`, x, y + 35);

                    // 进程
                    const processActive = (frame + i * 20) % 80 < 40;
                    ctx.fillStyle = processActive ? colors[i] : '#ddd';
                    ctx.fillRect(x - 30, y + 80, 60, 25);
                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.fillText(processes[i], x, y + 95);

                    // 线程
                    const threadActive = (frame + i * 15) % 60 < 30;
                    ctx.fillStyle = threadActive ? colors[i] : '#eee';
                    ctx.fillRect(x - 30, y + 120, 60, 25);
                    ctx.fillStyle = 'white';
                    ctx.fillText(threads[i], x, y + 135);
                }

                // 说明文字
                ctx.fillStyle = '#1976d2';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('每个核心可以独立执行不同的进程和线程', canvas.width/2, 280);
                ctx.fillText('真正实现并行计算，提高系统整体性能', canvas.width/2, 310);

                frame++;
                if (frame < 160) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 体积优势演示
        function showSizeAdvantage() {
            const canvas = document.getElementById('benefitsCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#fff3e0');
                gradient.addColorStop(1, '#ffeaa7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#f57c00';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('📦 体积对比', canvas.width/2, 40);

                // 多个独立处理器 (左侧)
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('多个独立处理器', 200, 80);

                for (let i = 0; i < 4; i++) {
                    const x = 100 + (i % 2) * 120;
                    const y = 100 + Math.floor(i / 2) * 80;
                    ctx.fillStyle = '#ff7043';
                    ctx.fillRect(x, y, 80, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.fillText(`CPU${i+1}`, x + 40, y + 35);
                }

                // 箭头
                const arrowX = 350 + Math.sin(frame * 0.1) * 10;
                ctx.fillStyle = '#4caf50';
                ctx.beginPath();
                ctx.moveTo(arrowX, 150);
                ctx.lineTo(arrowX + 30, 140);
                ctx.lineTo(arrowX + 30, 160);
                ctx.closePath();
                ctx.fill();

                // 多核处理器 (右侧)
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('多核处理器', 600, 80);

                const scale = 0.9 + Math.sin(frame * 0.05) * 0.1;
                ctx.save();
                ctx.translate(600, 150);
                ctx.scale(scale, scale);
                ctx.fillStyle = '#4caf50';
                ctx.fillRect(-60, -40, 120, 80);

                // 内部核心
                const coreColors = ['#66bb6a', '#81c784', '#a5d6a7', '#c8e6c9'];
                for (let i = 0; i < 4; i++) {
                    const x = -45 + (i % 2) * 45;
                    const y = -25 + Math.floor(i / 2) * 25;
                    ctx.fillStyle = coreColors[i];
                    ctx.fillRect(x, y, 20, 20);
                }
                ctx.restore();

                // 优势说明
                ctx.fillStyle = '#f57c00';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('集成设计大大减少了主板空间占用', canvas.width/2, 280);
                ctx.fillText('降低了系统复杂度和制造成本', canvas.width/2, 310);

                frame++;
                if (frame < 120) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 测验功能
        function selectOption(element, isCorrect) {
            // 移除之前的选择
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('selected', 'correct', 'incorrect');
            });

            // 标记当前选择
            element.classList.add('selected');

            // 延迟显示结果
            setTimeout(() => {
                document.querySelectorAll('.quiz-option').forEach(option => {
                    const optionText = option.textContent.trim();
                    if (optionText.startsWith('B.')) {
                        option.classList.add('correct');
                    } else if (option === element && !isCorrect) {
                        option.classList.add('incorrect');
                    }
                });

                // 显示解析
                document.getElementById('quizExplanation').style.display = 'block';

                // 更新分数
                if (isCorrect) {
                    score += 25;
                    document.getElementById('score').textContent = score;
                    updateProgress(100);

                    // 成功动画
                    element.style.animation = 'pulse 0.5s ease-in-out';

                    // 显示祝贺信息
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！你已经掌握了多核处理器的核心知识！');
                    }, 1000);
                } else {
                    // 错误提示
                    element.style.animation = 'shake 0.5s ease-in-out';
                }
            }, 500);
        }

        // 添加摇摆动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 自动播放第一个动画
            setTimeout(() => {
                animateProcessorEvolution();
            }, 1000);

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                switch(e.key) {
                    case '1':
                        animateProcessorEvolution();
                        break;
                    case '2':
                        showCoreComparison();
                        break;
                    case '3':
                        showSMP();
                        break;
                    case '4':
                        showBMP();
                        break;
                    case '5':
                        showAMP();
                        break;
                    case '6':
                        compareArchitectures();
                        break;
                }
            });

            // 添加提示信息
            const hint = document.createElement('div');
            hint.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 10px 15px;
                border-radius: 10px;
                font-size: 12px;
                z-index: 1000;
            `;
            hint.textContent = '💡 提示：使用数字键1-6快速切换演示';
            document.body.appendChild(hint);

            // 3秒后隐藏提示
            setTimeout(() => {
                hint.style.opacity = '0';
                hint.style.transition = 'opacity 1s ease-out';
                setTimeout(() => hint.remove(), 1000);
            }, 3000);
        });

        // 添加鼠标悬停效果
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.05)';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
