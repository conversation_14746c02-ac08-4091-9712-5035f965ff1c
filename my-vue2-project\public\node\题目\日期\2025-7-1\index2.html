<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UML包图交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 800px;
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1, h2 {
            text-align: center;
            color: #1a2a4c;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 18px;
            margin-bottom: 15px;
            border-bottom: 2px solid #e8e8e8;
            padding-bottom: 10px;
        }
        .question-box {
            background-color: #f9f9f9;
            border: 1px solid #e8e8e8;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .question-box p {
            margin: 0;
        }
        .options {
            margin-top: 15px;
            padding-left: 20px;
        }
        .correct-answer {
            color: #4CAF50;
            font-weight: bold;
        }
        .interactive-section {
            text-align: center;
        }
        canvas {
            border: 1px solid #ccc;
            border-radius: 8px;
            background-color: #ffffff;
        }
        .controls {
            margin: 15px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .explanation {
            margin-top: 15px;
            padding: 15px;
            background-color: #e9f5ff;
            border-left: 5px solid #007bff;
            text-align: left;
            border-radius: 0 8px 8px 0;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>面向对象建模问题</h1>
    <div class="question-box">
        <p><strong>题目：</strong>面向对象的分析模型主要由顶层架构图、用例与用例图和（&nbsp;&nbsp;）构成；设计模型则包含以（&nbsp;C&nbsp;）表示的软件体系机构图、以交互图表示的用例实现图、完整精确的类图、描述复杂对象的（&nbsp;&nbsp;）和用以描述流程化处理过程的活动图等。</p>
        <div class="options">
            A. 模型视图控制器 <br>
            B. 组件图 <br>
            C. <span class="correct-answer">包图</span> <br>
            D. 2 层、3 层或 N 层
        </div>
    </div>

    <div class="interactive-section">
        <h2>交互式理解：什么是"包图"？</h2>
        <canvas id="umlCanvas" width="700" height="400"></canvas>
        <div class="controls">
            <button id="actionBtn">开始演示</button>
        </div>
        <div class="explanation" id="explanationText">
            <p>点击按钮，看看一个复杂的软件系统是如何通过"包"来组织的。</p>
        </div>
    </div>
</div>

<script>
    // --- Canvas和动画逻辑 ---
    const canvas = document.getElementById('umlCanvas');
    const ctx = canvas.getContext('2d');
    const actionBtn = document.getElementById('actionBtn');
    const explanationText = document.getElementById('explanationText');

    let animationState = 0; // 0: initial, 1: organizing, 2: organized, 3: show dependencies

    const colors = {
        blue: '#3498db',
        green: '#2ecc71',
        purple: '#9b59b6',
        white: '#ecf0f1',
        dark: '#34495e',
        line: '#95a5a6'
    };

    // 元素定义
    const elements = [
        { name: '用户界面', group: 'UI', x: 100, y: 150, targetX: 150, targetY: 100, color: colors.blue },
        { name: '订单处理', group: 'Business', x: 200, y: 80, targetX: 350, targetY: 100, color: colors.green },
        { name: '库存管理', group: 'Business', x: 300, y: 250, targetX: 350, targetY: 170, color: colors.green },
        { name: '数据库连接', group: 'Data', x: 450, y: 120, targetX: 550, targetY: 100, color: colors.purple },
        { name: '用户认证', group: 'Business', x: 500, y: 300, targetX: 350, targetY: 240, color: colors.green },
        { name: '支付接口', group: 'Data', x: 600, y: 180, targetX: 550, targetY: 170, color: colors.purple },
        { name: '渲染引擎', group: 'UI', x: 150, y: 320, targetX: 150, targetY: 170, color: colors.blue }
    ];

    const packages = {
        'UI': { name: '界面层', x: 50, y: 50, width: 200, height: 200 },
        'Business': { name: '业务逻辑层', x: 275, y: 50, width: 200, height: 280 },
        'Data': { name: '数据访问层', x: 500, y: 50, width: 200, height: 200 }
    };

    // --- 绘图函数 ---
    function drawClass(el) {
        ctx.fillStyle = el.color;
        ctx.strokeStyle = colors.dark;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.rect(el.x - 45, el.y - 15, 90, 30);
        ctx.fill();
        ctx.stroke();
        
        ctx.fillStyle = colors.dark;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.font = '12px sans-serif';
        ctx.fillText(el.name, el.x, el.y);
    }

    function drawPackage(pkg) {
        ctx.strokeStyle = colors.dark;
        ctx.lineWidth = 1.5;
        
        // "文件夹"上方的标签
        ctx.beginPath();
        ctx.rect(pkg.x, pkg.y, 80, 25);
        ctx.stroke();
        
        // 主体框
        ctx.beginPath();
        ctx.rect(pkg.x, pkg.y, pkg.width, pkg.height);
        ctx.stroke();

        ctx.fillStyle = colors.dark;
        ctx.textAlign = 'left';
        ctx.textBaseline = 'middle';
        ctx.font = 'bold 13px sans-serif';
        ctx.fillText(pkg.name, pkg.x + 10, pkg.y + 12.5);
    }

    function drawDependency(fromPkg, toPkg, label) {
        const fromX = fromPkg.x + fromPkg.width;
        const fromY = fromPkg.y + fromPkg.height / 2;
        const toX = toPkg.x;
        const toY = toPkg.y + toPkg.height / 2;

        ctx.strokeStyle = colors.line;
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);

        // 箭头
        const headlen = 10;
        const angle = Math.atan2(toY - fromY, toX - fromX);
        
        ctx.beginPath();
        ctx.moveTo(fromX, fromY);
        ctx.lineTo(toX, toY);
        ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
        ctx.moveTo(toX, toY);
        ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
        ctx.stroke();
        
        ctx.setLineDash([]); // Reset dash
    }


    // --- 动画循环 ---
    let animationFrameId;
    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        if (animationState >= 1) { // 如果开始整理
            elements.forEach(el => {
                // 缓动动画
                el.x += (el.targetX - el.x) * 0.05;
                el.y += (el.targetY - el.y) * 0.05;
            });
        }
        
        if (animationState >= 2) { // 如果已整理好
             Object.values(packages).forEach(drawPackage);
        }
        
        if (animationState === 3) { // 显示依赖关系
            drawDependency(packages['UI'], packages['Business']);
            drawDependency(packages['Business'], packages['Data']);
        }

        elements.forEach(drawClass);
        
        animationFrameId = requestAnimationFrame(animate);
    }


    // --- 控制逻辑 ---
    function updateState() {
        animationState++;
        actionBtn.disabled = true;

        switch (animationState) {
            case 1:
                explanationText.innerHTML = '<p><b>第一步：整理。</b>就像整理文件一样，我们把功能相近的代码（类）放到一个"包"里。</p>';
                actionBtn.innerText = '正在整理...';
                // 为元素随机分配初始位置
                elements.forEach(el => {
                    el.x = Math.random() * (canvas.width - 100) + 50;
                    el.y = Math.random() * (canvas.height - 50) + 25;
                });
                
                setTimeout(() => {
                    actionBtn.disabled = false;
                    actionBtn.innerText = '画出包的边界';
                    updateState(); // Auto-proceed after a delay
                }, 50); // Start animation immediately after randomization
                
                setTimeout(() => { // Wait for elements to mostly settle
                     actionBtn.disabled = false;
                     actionBtn.innerText = '画出包的边界';
                }, 3000);

                break;
            case 2:
                explanationText.innerHTML = '<p><b>第二步：定义包。</b>现在，代码整洁多了！每个"包"就是一个逻辑单元，比如"界面层"、"业务逻辑层"。这就是包图的基本形态。</p>';
                actionBtn.innerText = '显示包之间的关系';
                setTimeout(() => {
                    actionBtn.disabled = false;
                }, 1000);
                break;
            case 3:
                explanationText.innerHTML = '<p><b>第三步：显示依赖。</b>虚线箭头表示"依赖"。"界面层"需要调用"业务逻辑层"的功能，而"业务逻辑层"又需要从"数据访问层"获取数据。</p>';
                actionBtn.innerText = '重置';
                actionBtn.disabled = false;
                break;
            case 4:
                // Reset
                animationState = 0;
                 elements.forEach(el => {
                    el.x = Math.random() * (canvas.width - 100) + 50;
                    el.y = Math.random() * (canvas.height - 50) + 25;
                });
                explanationText.innerHTML = '<p>点击按钮，重新看看一个复杂的软件系统是如何通过"包"来组织的。</p>';
                actionBtn.innerText = '开始演示';
                actionBtn.disabled = false;
                break;
        }
    }
    
    actionBtn.addEventListener('click', updateState);

    // --- 初始绘制 ---
    // Start with random positions
    elements.forEach(el => {
        el.x = Math.random() * (canvas.width - 100) + 50;
        el.y = Math.random() * (canvas.height - 50) + 25;
    });
    animate();

</script>

</body>
</html>
