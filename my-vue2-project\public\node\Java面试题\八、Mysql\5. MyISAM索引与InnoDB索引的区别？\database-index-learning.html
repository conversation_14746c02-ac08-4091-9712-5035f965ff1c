<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库索引学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: center;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .interactive-btn:active {
            transform: translateY(-1px);
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">数据库索引学习</h1>
            <p class="subtitle">通过动画和交互理解InnoDB与MyISAM索引的区别</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🌟 索引类型概览</h2>
            <div class="canvas-container">
                <canvas id="overviewCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">InnoDB索引是聚簇索引</span>，<span class="highlight">MyISAM索引是非聚簇索引</span></p>
                <p>点击上方动画了解两种索引的基本结构差异</p>
            </div>
            <div class="controls">
                <button class="interactive-btn" onclick="startOverviewAnimation()">开始演示</button>
                <button class="interactive-btn" onclick="resetOverview()">重置</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔍 InnoDB主键索引</h2>
            <div class="canvas-container">
                <canvas id="innodbCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">InnoDB的主键索引的叶子节点存储着行数据</span>，因此主键索引非常高效</p>
                <p>观看动画了解数据是如何直接存储在索引叶子节点中的</p>
            </div>
            <div class="controls">
                <button class="interactive-btn" onclick="startInnoDBAnimation()">查看InnoDB索引</button>
                <button class="interactive-btn" onclick="simulateInnoDBQuery()">模拟查询过程</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📍 MyISAM索引结构</h2>
            <div class="canvas-container">
                <canvas id="myisamCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">MyISAM索引的叶子节点存储的是行数据地址</span>，需要再寻址一次才能得到数据</p>
                <p>体验MyISAM索引的两步查询过程</p>
            </div>
            <div class="controls">
                <button class="interactive-btn" onclick="startMyISAMAnimation()">查看MyISAM索引</button>
                <button class="interactive-btn" onclick="simulateMyISAMQuery()">模拟查询过程</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚡ 覆盖索引优化</h2>
            <div class="canvas-container">
                <canvas id="coveringCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">InnoDB非主键索引的叶子节点存储的是主键和其他带索引的列数据</span></p>
                <p>因此查询时做到覆盖索引会非常高效</p>
            </div>
            <div class="controls">
                <button class="interactive-btn" onclick="startCoveringIndexAnimation()">演示覆盖索引</button>
                <button class="interactive-btn" onclick="compareCoveringIndex()">对比查询效率</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 0;
        const totalSteps = 4;
        let animationFrameId;

        // 更新进度条
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 概览动画
        function startOverviewAnimation() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.font = 'bold 24px Arial';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('索引类型对比', canvas.width / 2, 40);

                // InnoDB聚簇索引
                drawClusteredIndex(ctx, 150, 80, frame);
                
                // MyISAM非聚簇索引
                drawNonClusteredIndex(ctx, 550, 80, frame);

                frame++;
                if (frame < 200) {
                    animationFrameId = requestAnimationFrame(animate);
                } else {
                    currentStep = Math.max(currentStep, 1);
                    updateProgress();
                }
            }
            animate();
        }

        function drawClusteredIndex(ctx, x, y, frame) {
            // 标题
            ctx.font = '18px Arial';
            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('InnoDB (聚簇索引)', x, y);

            // 绘制树结构
            const opacity = Math.min(frame / 50, 1);
            ctx.globalAlpha = opacity;

            // 根节点
            drawNode(ctx, x, y + 40, '根节点', '#667eea');
            
            // 叶子节点（包含数据）
            const leafY = y + 120;
            for (let i = 0; i < 3; i++) {
                const leafX = x - 60 + i * 60;
                drawDataNode(ctx, leafX, leafY, `数据${i + 1}`, '#4CAF50');
                
                // 连接线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x, y + 60);
                ctx.lineTo(leafX, leafY - 20);
                ctx.stroke();
            }

            ctx.globalAlpha = 1;
        }

        function drawNonClusteredIndex(ctx, x, y, frame) {
            // 标题
            ctx.font = '18px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.textAlign = 'center';
            ctx.fillText('MyISAM (非聚簇索引)', x, y);

            const opacity = Math.min(frame / 50, 1);
            ctx.globalAlpha = opacity;

            // 根节点
            drawNode(ctx, x, y + 40, '根节点', '#764ba2');
            
            // 叶子节点（包含地址）
            const leafY = y + 120;
            for (let i = 0; i < 3; i++) {
                const leafX = x - 60 + i * 60;
                drawAddressNode(ctx, leafX, leafY, `地址${i + 1}`, '#FF9800');
                
                // 连接线
                ctx.strokeStyle = '#764ba2';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x, y + 60);
                ctx.lineTo(leafX, leafY - 20);
                ctx.stroke();
            }

            // 数据文件（分离的）
            const dataY = y + 200;
            for (let i = 0; i < 3; i++) {
                const dataX = x - 60 + i * 60;
                drawDataNode(ctx, dataX, dataY, `数据${i + 1}`, '#4CAF50');
                
                // 指向数据的箭头
                if (frame > 100) {
                    drawArrow(ctx, dataX, leafY + 20, dataX, dataY - 20, '#FF5722');
                }
            }

            ctx.globalAlpha = 1;
        }

        function drawNode(ctx, x, y, text, color) {
            // 节点背景
            ctx.fillStyle = color;
            ctx.fillRect(x - 30, y - 15, 60, 30);
            
            // 节点文字
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 5);
        }

        function drawDataNode(ctx, x, y, text, color) {
            // 数据节点（圆形）
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 25, 0, 2 * Math.PI);
            ctx.fill();
            
            // 文字
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 3);
        }

        function drawAddressNode(ctx, x, y, text, color) {
            // 地址节点（菱形）
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(x, y - 20);
            ctx.lineTo(x + 20, y);
            ctx.lineTo(x, y + 20);
            ctx.lineTo(x - 20, y);
            ctx.closePath();
            ctx.fill();
            
            // 文字
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 3);
        }

        function drawArrow(ctx, x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle - Math.PI / 6), y2 - 10 * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(x2 - 10 * Math.cos(angle + Math.PI / 6), y2 - 10 * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }

        function resetOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
        }

        // InnoDB动画
        function startInnoDBAnimation() {
            const canvas = document.getElementById('innodbCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 标题
                ctx.font = 'bold 24px Arial';
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('InnoDB 主键索引结构', canvas.width / 2, 40);

                drawInnoDBStructure(ctx, frame);

                frame++;
                if (frame < 150) {
                    animationFrameId = requestAnimationFrame(animate);
                } else {
                    currentStep = Math.max(currentStep, 2);
                    updateProgress();
                }
            }
            animate();
        }

        function drawInnoDBStructure(ctx, frame) {
            const canvas = document.getElementById('innodbCanvas');
            const centerX = canvas.width / 2;
            const opacity = Math.min(frame / 30, 1);
            ctx.globalAlpha = opacity;

            // B+树结构
            // 根节点
            drawNode(ctx, centerX, 100, 'Root', '#667eea');
            
            // 中间节点
            const midY = 160;
            for (let i = 0; i < 3; i++) {
                const midX = centerX - 100 + i * 100;
                drawNode(ctx, midX, midY, `Node${i + 1}`, '#667eea');
                
                // 连接线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(centerX, 120);
                ctx.lineTo(midX, midY - 20);
                ctx.stroke();
            }

            // 叶子节点（包含完整数据）
            const leafY = 250;
            for (let i = 0; i < 5; i++) {
                const leafX = centerX - 200 + i * 100;
                
                // 动画效果：逐个显示
                if (frame > 50 + i * 10) {
                    drawInnoDBLeaf(ctx, leafX, leafY, `ID:${i + 1}`, `完整行数据${i + 1}`);
                }
                
                // 连接到中间节点
                const parentIndex = Math.floor(i / 2);
                if (parentIndex < 3) {
                    const parentX = centerX - 100 + parentIndex * 100;
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(parentX, midY + 20);
                    ctx.lineTo(leafX, leafY - 30);
                    ctx.stroke();
                }
            }

            // 说明文字
            if (frame > 100) {
                ctx.font = '16px Arial';
                ctx.fillStyle = '#4CAF50';
                ctx.textAlign = 'center';
                ctx.fillText('叶子节点直接存储完整的行数据', centerX, 350);
                ctx.fillText('一次查找即可获得所有数据！', centerX, 375);
            }

            ctx.globalAlpha = 1;
        }

        function drawInnoDBLeaf(ctx, x, y, key, data) {
            // 叶子节点背景
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(x - 40, y - 25, 80, 50);
            
            // 边框
            ctx.strokeStyle = '#2E7D32';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 40, y - 25, 80, 50);
            
            // 键值
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(key, x, y - 8);
            
            // 数据
            ctx.font = '10px Arial';
            ctx.fillText(data, x, y + 8);
        }

        // MyISAM动画
        function startMyISAMAnimation() {
            const canvas = document.getElementById('myisamCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 标题
                ctx.font = 'bold 24px Arial';
                ctx.fillStyle = '#764ba2';
                ctx.textAlign = 'center';
                ctx.fillText('MyISAM 索引结构', canvas.width / 2, 40);

                drawMyISAMStructure(ctx, frame);

                frame++;
                if (frame < 200) {
                    animationFrameId = requestAnimationFrame(animate);
                } else {
                    currentStep = Math.max(currentStep, 3);
                    updateProgress();
                }
            }
            animate();
        }

        function drawMyISAMStructure(ctx, frame) {
            const canvas = document.getElementById('myisamCanvas');
            const centerX = canvas.width / 2;
            const opacity = Math.min(frame / 30, 1);
            ctx.globalAlpha = opacity;

            // 索引树
            drawNode(ctx, centerX - 200, 100, 'Index Root', '#764ba2');
            
            // 索引叶子节点
            const indexLeafY = 180;
            for (let i = 0; i < 3; i++) {
                const leafX = centerX - 280 + i * 80;
                if (frame > 30 + i * 15) {
                    drawMyISAMIndexLeaf(ctx, leafX, indexLeafY, `Key${i + 1}`, `Addr${i + 1}`);
                }
                
                // 连接线
                ctx.strokeStyle = '#764ba2';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(centerX - 200, 120);
                ctx.lineTo(leafX, indexLeafY - 25);
                ctx.stroke();
            }

            // 数据文件
            ctx.font = '18px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('数据文件', centerX + 200, 100);
            
            const dataY = 180;
            for (let i = 0; i < 3; i++) {
                const dataX = centerX + 120 + i * 80;
                if (frame > 60 + i * 15) {
                    drawDataRecord(ctx, dataX, dataY, `完整行数据${i + 1}`);
                }
            }

            // 指向箭头动画
            if (frame > 120) {
                for (let i = 0; i < 3; i++) {
                    const indexX = centerX - 280 + i * 80;
                    const dataX = centerX + 120 + i * 80;
                    
                    // 动画箭头
                    const arrowProgress = Math.min((frame - 120 - i * 10) / 30, 1);
                    if (arrowProgress > 0) {
                        drawAnimatedArrow(ctx, indexX + 30, indexLeafY, dataX - 30, dataY, arrowProgress, '#FF5722');
                    }
                }
            }

            // 说明文字
            if (frame > 160) {
                ctx.font = '16px Arial';
                ctx.fillStyle = '#FF5722';
                ctx.textAlign = 'center';
                ctx.fillText('索引存储地址，需要二次查找获取数据', centerX, 300);
                ctx.fillText('两步查询：索引 → 地址 → 数据', centerX, 325);
            }

            ctx.globalAlpha = 1;
        }

        function drawMyISAMIndexLeaf(ctx, x, y, key, addr) {
            // 索引节点
            ctx.fillStyle = '#FF9800';
            ctx.fillRect(x - 30, y - 20, 60, 40);
            
            ctx.strokeStyle = '#F57C00';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 30, y - 20, 60, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(key, x, y - 5);
            ctx.fillText(addr, x, y + 8);
        }

        function drawDataRecord(ctx, x, y, data) {
            // 数据记录
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(x - 35, y - 20, 70, 40);
            
            ctx.strokeStyle = '#2E7D32';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 35, y - 20, 70, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(data, x, y + 3);
        }

        function drawAnimatedArrow(ctx, x1, y1, x2, y2, progress, color) {
            const currentX = x1 + (x2 - x1) * progress;
            const currentY = y1 + (y2 - y1) * progress;
            
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(currentX, currentY);
            ctx.stroke();
            
            if (progress >= 1) {
                // 箭头头部
                const angle = Math.atan2(y2 - y1, x2 - x1);
                ctx.beginPath();
                ctx.moveTo(x2, y2);
                ctx.lineTo(x2 - 10 * Math.cos(angle - Math.PI / 6), y2 - 10 * Math.sin(angle - Math.PI / 6));
                ctx.lineTo(x2 - 10 * Math.cos(angle + Math.PI / 6), y2 - 10 * Math.sin(angle + Math.PI / 6));
                ctx.closePath();
                ctx.fillStyle = color;
                ctx.fill();
            }
        }

        // 模拟查询
        function simulateInnoDBQuery() {
            alert('🔍 InnoDB查询模拟：\n\n1. 通过B+树索引定位到叶子节点\n2. 直接从叶子节点获取完整行数据\n3. 查询完成！\n\n✅ 只需一次磁盘IO，效率很高！');
            currentStep = Math.max(currentStep, 2);
            updateProgress();
        }

        function simulateMyISAMQuery() {
            alert('🔍 MyISAM查询模拟：\n\n1. 通过索引树找到数据地址\n2. 根据地址到数据文件中查找\n3. 获取完整行数据\n4. 查询完成！\n\n⚠️ 需要两次磁盘IO，相对较慢');
            currentStep = Math.max(currentStep, 3);
            updateProgress();
        }

        // 覆盖索引动画
        function startCoveringIndexAnimation() {
            const canvas = document.getElementById('coveringCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 标题
                ctx.font = 'bold 24px Arial';
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('InnoDB 覆盖索引优化', canvas.width / 2, 40);

                drawCoveringIndexStructure(ctx, frame);

                frame++;
                if (frame < 180) {
                    animationFrameId = requestAnimationFrame(animate);
                } else {
                    currentStep = Math.max(currentStep, 4);
                    updateProgress();
                }
            }
            animate();
        }

        function drawCoveringIndexStructure(ctx, frame) {
            const canvas = document.getElementById('coveringCanvas');
            const centerX = canvas.width / 2;
            
            // 非主键索引树
            ctx.font = '18px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('非主键索引 (name)', centerX, 80);
            
            // 索引节点
            const indexY = 120;
            for (let i = 0; i < 4; i++) {
                const indexX = centerX - 150 + i * 100;
                if (frame > 20 + i * 10) {
                    drawCoveringIndexNode(ctx, indexX, indexY, `name${i + 1}`, `pk${i + 1}`, `age${i + 1}`);
                }
            }

            // 查询示例
            if (frame > 80) {
                ctx.font = '16px Arial';
                ctx.fillStyle = '#4CAF50';
                ctx.textAlign = 'center';
                ctx.fillText('SELECT name, age FROM table WHERE name = "张三"', centerX, 200);
                
                // 高亮匹配的索引节点
                if (frame > 100) {
                    ctx.strokeStyle = '#FF5722';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(centerX - 150 - 40, indexY - 25, 80, 50);
                    
                    // 说明
                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#FF5722';
                    ctx.fillText('✅ 索引节点包含所需数据', centerX, 240);
                    ctx.fillText('无需回表查询主键索引！', centerX, 260);
                }
            }

            // 对比普通查询
            if (frame > 140) {
                ctx.font = '16px Arial';
                ctx.fillStyle = '#FF9800';
                ctx.textAlign = 'center';
                ctx.fillText('SELECT * FROM table WHERE name = "张三"', centerX, 300);
                ctx.font = '14px Arial';
                ctx.fillText('❌ 需要回表查询获取其他列数据', centerX, 320);
                
                // 绘制回表箭头
                drawArrow(ctx, centerX - 110, indexY + 30, centerX + 100, 340, '#FF9800');
                ctx.fillText('回表查询', centerX + 120, 350);
            }
        }

        function drawCoveringIndexNode(ctx, x, y, name, pk, age) {
            // 节点背景
            ctx.fillStyle = '#E3F2FD';
            ctx.fillRect(x - 40, y - 25, 80, 50);
            
            // 边框
            ctx.strokeStyle = '#1976D2';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 40, y - 25, 80, 50);
            
            // 内容
            ctx.fillStyle = '#1976D2';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y - 10);
            ctx.fillText(`PK:${pk}`, x, y);
            ctx.fillText(`Age:${age}`, x, y + 10);
        }

        function compareCoveringIndex() {
            alert('⚡ 覆盖索引优化对比：\n\n🚀 使用覆盖索引：\n- 只需查询非主键索引\n- 索引节点包含所需数据\n- 一次IO完成查询\n\n🐌 普通查询：\n- 先查询非主键索引获取主键\n- 再查询主键索引获取完整数据\n- 需要两次IO（回表查询）\n\n💡 优化建议：合理设计复合索引！');
            currentStep = Math.max(currentStep, 4);
            updateProgress();
        }

        // 初始化
        window.onload = function() {
            updateProgress();
            
            // 添加画布点击事件
            document.getElementById('overviewCanvas').addEventListener('click', startOverviewAnimation);
            document.getElementById('innodbCanvas').addEventListener('click', startInnoDBAnimation);
            document.getElementById('myisamCanvas').addEventListener('click', startMyISAMAnimation);
            document.getElementById('coveringCanvas').addEventListener('click', startCoveringIndexAnimation);
        };
    </script>
</body>
</html>
