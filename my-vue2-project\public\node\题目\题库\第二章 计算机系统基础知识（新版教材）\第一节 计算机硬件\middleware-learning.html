<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式中间件学习 - 交互式教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateY(-5px);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .quiz-container {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 25px;
            font-weight: bold;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }

        .quiz-option {
            background: white;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .quiz-option:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .quiz-option.correct {
            border-color: #4CAF50;
            background: #e8f5e8;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            border-color: #f44336;
            background: #ffeaea;
            animation: wrongShake 0.6s ease-out;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 1s ease-out;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .section { padding: 25px; }
            canvas { width: 100%; max-width: 400px; }
        }
    </style>
</head>
<body>
    <canvas class="floating-particles" id="particleCanvas"></canvas>
    
    <div class="container">
        <div class="header">
            <h1>🚀 嵌入式中间件学习之旅</h1>
            <p>通过动画和交互，轻松掌握中间件的核心概念！让我们一起探索这个神奇的技术世界吧～</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是嵌入式中间件？</h2>
            <div class="canvas-container">
                <canvas id="middlewareCanvas" width="600" height="300"></canvas>
            </div>
            <div class="explanation">
                <strong>嵌入式中间件</strong>就像是软件世界的"翻译官"和"邮递员"！它帮助不同的应用程序之间进行沟通和协作。
                <br><br>
                想象一下：如果应用程序是不同国家的人，那么中间件就是精通多国语言的翻译，让大家能够顺畅交流！
            </div>
            <button class="interactive-btn" onclick="animateMiddleware()">🎬 播放动画演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">📨 消息中间件详解</h2>
            <div class="canvas-container">
                <canvas id="messageCanvas" width="600" height="350"></canvas>
            </div>
            <div class="explanation">
                <strong>消息中间件</strong>是消息传输过程中保存消息的容器，就像邮局一样！
                <br><br>
                <strong>🔑 两个基本特点：</strong>
                <br>1️⃣ <strong>异步处理模式</strong>：发送者不需要等待接收者立即回复
                <br>2️⃣ <strong>松耦合关系</strong>：应用程序之间的依赖关系很松散，更灵活
            </div>
            <button class="interactive-btn" onclick="animateMessageMiddleware()">📬 演示消息传递</button>
            <button class="interactive-btn" onclick="showAsyncDemo()">⚡ 异步处理演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">🌐 分布式对象中间件</h2>
            <div class="canvas-container">
                <canvas id="distributedCanvas" width="600" height="300"></canvas>
            </div>
            <div class="explanation">
                <strong>分布式对象中间件</strong>主要由一组对象来提供系统服务，这些对象能够跨平台通信！
                <br><br>
                就像是一个国际会议，来自不同国家（平台）的代表（对象）可以无障碍地交流合作！
            </div>
            <button class="interactive-btn" onclick="animateDistributedObjects()">🔗 跨平台通信演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">📡 消息传递服务模型</h2>
            <div class="canvas-container">
                <canvas id="modelCanvas" width="600" height="400"></canvas>
            </div>
            <div class="explanation">
                消息中间件有两种主要的传递模型：
                <br><br>
                <strong>🎯 点对点模型（Point-to-Point）</strong>：就像写信，一对一的私密通信
                <br><strong>📢 发布-订阅模型（Publish-Subscribe）</strong>：就像广播电台，一对多的信息发布
            </div>
            <button class="interactive-btn" onclick="showP2PModel()">📮 点对点演示</button>
            <button class="interactive-btn" onclick="showPubSubModel()">📻 发布订阅演示</button>
        </div>

        <div class="quiz-container">
            <h2 class="section-title">🎮 知识挑战游戏</h2>
            <div class="quiz-question" id="quizQuestion">
                以下有关消息中间件的描述中，不正确的是？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    A. 消息中间件是消息传输过程中保存消息的一种容器
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    B. 消息中间件具有异步处理模式和松耦合关系两个基本特点
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">
                    C. 消息中间件主要由一组对象来提供系统服务，对象间能够跨平台通信
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    D. 消息中间件的消息传递服务模型有点对点模型和发布-订阅模型之分
                </div>
            </div>
            <div id="quizResult"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentProgress = 0;
        let animationRunning = false;

        // 粒子背景动画
        function initParticles() {
            const canvas = document.getElementById('particleCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            const particles = [];
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5,
                    size: Math.random() * 3 + 1,
                    opacity: Math.random() * 0.5 + 0.2
                });
            }

            function animateParticles() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;

                    if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
                    ctx.fill();
                });

                requestAnimationFrame(animateParticles);
            }

            animateParticles();
        }

        // 更新进度条
        function updateProgress(progress) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = progress + '%';
        }

        // 中间件概念动画
        function animateMiddleware() {
            const canvas = document.getElementById('middlewareCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制应用程序A
                ctx.fillStyle = '#FF6B6B';
                ctx.fillRect(50, 100, 100, 80);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('应用程序A', 100, 145);

                // 绘制应用程序B
                ctx.fillStyle = '#4ECDC4';
                ctx.fillRect(450, 100, 100, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('应用程序B', 500, 145);

                // 绘制中间件
                ctx.fillStyle = '#45B7D1';
                ctx.fillRect(250, 120, 100, 40);
                ctx.fillStyle = 'white';
                ctx.fillText('中间件', 300, 145);

                // 动画箭头
                const arrowOffset = Math.sin(frame * 0.1) * 10;

                // 左箭头
                ctx.strokeStyle = '#FF6B6B';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(150, 140);
                ctx.lineTo(240 + arrowOffset, 140);
                ctx.stroke();

                // 箭头头部
                ctx.beginPath();
                ctx.moveTo(235 + arrowOffset, 135);
                ctx.lineTo(240 + arrowOffset, 140);
                ctx.lineTo(235 + arrowOffset, 145);
                ctx.stroke();

                // 右箭头
                ctx.strokeStyle = '#4ECDC4';
                ctx.beginPath();
                ctx.moveTo(360 - arrowOffset, 140);
                ctx.lineTo(450, 140);
                ctx.stroke();

                // 箭头头部
                ctx.beginPath();
                ctx.moveTo(445, 135);
                ctx.lineTo(450, 140);
                ctx.lineTo(445, 145);
                ctx.stroke();

                // 添加说明文字
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText('数据转换', 300, 110);
                ctx.fillText('协议适配', 300, 170);

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                }
            }

            draw();
            updateProgress(25);
        }

        // 消息中间件动画
        function animateMessageMiddleware() {
            const canvas = document.getElementById('messageCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            const messages = [];

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制发送者
                ctx.fillStyle = '#FF6B6B';
                ctx.fillRect(50, 150, 80, 60);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('发送者', 90, 185);

                // 绘制消息队列
                ctx.fillStyle = '#FFA726';
                ctx.fillRect(200, 100, 200, 150);
                ctx.fillStyle = 'white';
                ctx.fillText('消息队列', 300, 130);
                ctx.fillText('(中间件)', 300, 145);

                // 绘制接收者
                ctx.fillStyle = '#4ECDC4';
                ctx.fillRect(470, 150, 80, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('接收者', 510, 185);

                // 创建消息
                if (frame % 60 === 0) {
                    messages.push({
                        x: 130,
                        y: 175,
                        stage: 0, // 0: 发送中, 1: 在队列中, 2: 接收中
                        id: Math.random()
                    });
                }

                // 绘制和移动消息
                messages.forEach((msg, index) => {
                    ctx.fillStyle = '#E91E63';

                    if (msg.stage === 0) {
                        // 发送阶段
                        msg.x += 2;
                        if (msg.x >= 200) {
                            msg.stage = 1;
                            msg.waitTime = 60; // 在队列中等待时间
                        }
                    } else if (msg.stage === 1) {
                        // 在队列中等待
                        msg.x = 220 + (index % 5) * 30;
                        msg.y = 160 + Math.floor(index / 5) * 20;
                        msg.waitTime--;
                        if (msg.waitTime <= 0) {
                            msg.stage = 2;
                        }
                    } else if (msg.stage === 2) {
                        // 接收阶段
                        msg.x += 3;
                        msg.y = 175;
                        if (msg.x >= 470) {
                            messages.splice(index, 1);
                        }
                    }

                    ctx.beginPath();
                    ctx.arc(msg.x, msg.y, 8, 0, Math.PI * 2);
                    ctx.fill();
                });

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                }
            }

            draw();
            updateProgress(50);
        }

        // 异步处理演示
        function showAsyncDemo() {
            const canvas = document.getElementById('messageCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制发送者
                ctx.fillStyle = '#FF6B6B';
                ctx.fillRect(50, 100, 100, 50);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('发送者', 100, 130);

                // 绘制接收者
                ctx.fillStyle = '#4ECDC4';
                ctx.fillRect(450, 100, 100, 50);
                ctx.fillStyle = 'white';
                ctx.fillText('接收者', 500, 130);

                // 异步标识
                const pulse = Math.sin(frame * 0.2) * 0.3 + 0.7;
                ctx.fillStyle = `rgba(255, 193, 7, ${pulse})`;
                ctx.fillRect(200, 200, 200, 40);
                ctx.fillStyle = '#333';
                ctx.fillText('异步处理 - 无需等待!', 300, 225);

                // 发送者继续工作的动画
                ctx.fillStyle = '#FF6B6B';
                const workAnimation = Math.sin(frame * 0.3) * 5;
                ctx.fillRect(60 + workAnimation, 170, 80, 20);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText('继续工作中...', 100, 185);

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                }
            }

            draw();
        }

        // 分布式对象中间件动画
        function animateDistributedObjects() {
            const canvas = document.getElementById('distributedCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制不同平台
                const platforms = [
                    { name: 'Windows', color: '#0078D4', x: 50, y: 50 },
                    { name: 'Linux', color: '#FCC624', x: 250, y: 50 },
                    { name: 'MacOS', color: '#007AFF', x: 450, y: 50 },
                    { name: 'Android', color: '#3DDC84', x: 150, y: 180 },
                    { name: 'iOS', color: '#FF3B30', x: 350, y: 180 }
                ];

                platforms.forEach((platform, index) => {
                    ctx.fillStyle = platform.color;
                    ctx.fillRect(platform.x, platform.y, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(platform.name, platform.x + 50, platform.y + 35);

                    // 连接线动画
                    const centerX = 300;
                    const centerY = 150;
                    const lineOpacity = Math.sin(frame * 0.1 + index) * 0.3 + 0.7;

                    ctx.strokeStyle = `rgba(102, 126, 234, ${lineOpacity})`;
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(platform.x + 50, platform.y + 30);
                    ctx.lineTo(centerX, centerY);
                    ctx.stroke();
                });

                // 中心节点
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(300, 150, 20, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText('中间件', 300, 155);

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                }
            }

            draw();
            updateProgress(75);
        }

        // 点对点模型演示
        function showP2PModel() {
            const canvas = document.getElementById('modelCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('点对点模型 (Point-to-Point)', 300, 30);

                // 发送者
                ctx.fillStyle = '#FF6B6B';
                ctx.fillRect(50, 150, 80, 60);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText('发送者', 90, 185);

                // 消息队列
                ctx.fillStyle = '#FFA726';
                ctx.fillRect(200, 150, 200, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('消息队列', 300, 175);
                ctx.fillText('(一对一)', 300, 195);

                // 接收者
                ctx.fillStyle = '#4ECDC4';
                ctx.fillRect(470, 150, 80, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('接收者', 510, 185);

                // 动画消息
                const messageX = 130 + Math.sin(frame * 0.05) * 300;
                ctx.fillStyle = '#E91E63';
                ctx.beginPath();
                ctx.arc(messageX, 180, 10, 0, Math.PI * 2);
                ctx.fill();

                // 特点说明
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText('✓ 一对一通信', 300, 280);
                ctx.fillText('✓ 消息只能被一个接收者消费', 300, 310);
                ctx.fillText('✓ 类似于邮件系统', 300, 340);

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                }
            }

            draw();
        }

        // 发布订阅模型演示
        function showPubSubModel() {
            const canvas = document.getElementById('modelCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('发布-订阅模型 (Publish-Subscribe)', 300, 30);

                // 发布者
                ctx.fillStyle = '#FF6B6B';
                ctx.fillRect(50, 100, 80, 60);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText('发布者', 90, 135);

                // 消息代理
                ctx.fillStyle = '#FFA726';
                ctx.fillRect(250, 100, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('消息代理', 300, 135);

                // 多个订阅者
                const subscribers = [
                    { x: 450, y: 50, name: '订阅者1' },
                    { x: 450, y: 120, name: '订阅者2' },
                    { x: 450, y: 190, name: '订阅者3' }
                ];

                subscribers.forEach((sub, index) => {
                    ctx.fillStyle = '#4ECDC4';
                    ctx.fillRect(sub.x, sub.y, 80, 50);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(sub.name, sub.x + 40, sub.y + 30);

                    // 动画消息传递
                    const messageProgress = (frame + index * 20) % 100;
                    if (messageProgress < 50) {
                        const messageX = 350 + (messageProgress / 50) * 100;
                        const messageY = 130 + (sub.y + 25 - 130) * (messageProgress / 50);

                        ctx.fillStyle = '#E91E63';
                        ctx.beginPath();
                        ctx.arc(messageX, messageY, 6, 0, Math.PI * 2);
                        ctx.fill();
                    }
                });

                // 发布者到代理的消息
                const pubMessageX = 130 + Math.sin(frame * 0.1) * 60;
                ctx.fillStyle = '#E91E63';
                ctx.beginPath();
                ctx.arc(pubMessageX, 130, 8, 0, Math.PI * 2);
                ctx.fill();

                // 特点说明
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText('✓ 一对多通信', 300, 280);
                ctx.fillText('✓ 消息可以被多个订阅者接收', 300, 310);
                ctx.fillText('✓ 类似于广播电台', 300, 340);

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                }
            }

            draw();
        }

        // 答题功能
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const resultDiv = document.getElementById('quizResult');

            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    if (isCorrect) {
                        option.classList.add('correct');
                        resultDiv.innerHTML = `
                            <div style="color: #4CAF50; font-size: 1.2rem; margin-top: 20px;">
                                🎉 恭喜答对了！<br>
                                <div style="font-size: 1rem; margin-top: 10px; color: #666;">
                                    选项C描述的是<strong>分布式对象中间件</strong>的特点，而不是消息中间件的特点。
                                    消息中间件主要关注消息的传递和存储，而分布式对象中间件才是通过对象提供跨平台服务。
                                </div>
                            </div>
                        `;
                        updateProgress(100);
                        // 播放庆祝动画
                        celebrateAnimation();
                    } else {
                        option.classList.add('wrong');
                        resultDiv.innerHTML = `
                            <div style="color: #f44336; font-size: 1.2rem; margin-top: 20px;">
                                ❌ 答案不正确，再想想看！<br>
                                <div style="font-size: 1rem; margin-top: 10px; color: #666;">
                                    提示：仔细区分消息中间件和分布式对象中间件的不同特点
                                </div>
                            </div>
                        `;
                    }
                }
            });

            setTimeout(() => {
                options.forEach(option => {
                    option.style.pointerEvents = 'auto';
                    option.classList.remove('correct', 'wrong');
                });
                resultDiv.innerHTML = '';
            }, 3000);
        }

        // 庆祝动画
        function celebrateAnimation() {
            const canvas = document.createElement('canvas');
            canvas.style.position = 'fixed';
            canvas.style.top = '0';
            canvas.style.left = '0';
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.pointerEvents = 'none';
            canvas.style.zIndex = '9999';
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            document.body.appendChild(canvas);

            const ctx = canvas.getContext('2d');
            const confetti = [];

            for (let i = 0; i < 100; i++) {
                confetti.push({
                    x: Math.random() * canvas.width,
                    y: -10,
                    vx: (Math.random() - 0.5) * 4,
                    vy: Math.random() * 3 + 2,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`,
                    size: Math.random() * 8 + 4
                });
            }

            function animateConfetti() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                confetti.forEach((piece, index) => {
                    piece.x += piece.vx;
                    piece.y += piece.vy;
                    piece.vy += 0.1; // 重力

                    ctx.fillStyle = piece.color;
                    ctx.fillRect(piece.x, piece.y, piece.size, piece.size);

                    if (piece.y > canvas.height) {
                        confetti.splice(index, 1);
                    }
                });

                if (confetti.length > 0) {
                    requestAnimationFrame(animateConfetti);
                } else {
                    document.body.removeChild(canvas);
                }
            }

            animateConfetti();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initParticles();

            // 自动播放第一个动画
            setTimeout(() => {
                animateMiddleware();
            }, 1000);
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            const particleCanvas = document.getElementById('particleCanvas');
            particleCanvas.width = window.innerWidth;
            particleCanvas.height = window.innerHeight;
        });
    </script>
</body>
</html>
