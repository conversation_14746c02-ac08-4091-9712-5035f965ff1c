<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考研英语单词动画演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background-color: #fff;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            font-size: 1.5rem;
            color: #555;
            margin-bottom: 0.5rem;
        }
        h2 {
            font-size: 2.5rem;
            color: #007BFF;
            margin-bottom: 1.5rem;
            font-weight: bold;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        #explanation {
            margin-top: 1rem;
            font-size: 1.1rem;
            color: #666;
            min-height: 50px;
            line-height: 1.6;
        }
        button {
            background-color: #007BFF;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 1rem;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>考研例句：The future is unpredictable, but with hard work, we can shape it.</h1>
        <h2>unpredictable</h2>
        <canvas id="wordCanvas" width="700" height="350"></canvas>
        <div id="explanation">点击按钮，开始学习单词 "unpredictable" 的故事。</div>
        <button id="startAnimationBtn">开始动画</button>
    </div>

    <script>
        const canvas = document.getElementById('wordCanvas');
        const ctx = canvas.getContext('2d');
        const explanationDiv = document.getElementById('explanation');
        const startBtn = document.getElementById('startAnimationBtn');

        let animationState = {
            scene: 0, // 0: idle, 1: pre, 2: dict, 3: able, 4: un, 5: final
            progress: 0,
            startTime: null,
            duration: 2000 // ms for each scene
        };

        function drawText(text, x, y, size = 20, color = '#333') {
            ctx.fillStyle = color;
            ctx.font = `${size}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制智者 (简笔画)
        function drawWizard(x, y) {
            // Body
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x - 20, y + 60);
            ctx.lineTo(x + 20, y + 60);
            ctx.closePath();
            ctx.fillStyle = '#8e44ad';
            ctx.fill();

            // Head
            ctx.beginPath();
            ctx.arc(x, y - 15, 15, 0, Math.PI * 2);
            ctx.fillStyle = '#f1c40f';
            ctx.fill();
            
            // Hat
            ctx.beginPath();
            ctx.moveTo(x - 20, y - 25);
            ctx.lineTo(x + 20, y - 25);
            ctx.lineTo(x, y - 55);
            ctx.closePath();
            ctx.fillStyle = '#8e44ad';
            ctx.fill();
        }

        // 绘制学生
        function drawStudent(x, y) {
            // Body
             ctx.beginPath();
            ctx.rect(x-10, y, 20, 40);
            ctx.fillStyle = '#3498db';
            ctx.fill();
            // Head
            ctx.beginPath();
            ctx.arc(x, y - 10, 10, 0, Math.PI * 2);
            ctx.fillStyle = '#f39c12';
            ctx.fill();
        }

        // 绘制水晶球
        function drawCrystalBall(x, y, radius, opacity = 1) {
            ctx.save();
            ctx.globalAlpha = opacity;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            const gradient = ctx.createRadialGradient(x, y, radius * 0.1, x, y, radius);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
            gradient.addColorStop(0.8, 'rgba(200, 200, 255, 0.7)');
            gradient.addColorStop(1, 'rgba(150, 150, 255, 0.5)');
            ctx.fillStyle = gradient;
            ctx.fill();
            ctx.strokeStyle = 'rgba(100, 100, 200, 0.6)';
            ctx.stroke();
            ctx.restore();
        }

        function animate(timestamp) {
            if (!animationState.startTime) {
                animationState.startTime = timestamp;
            }
            const elapsed = timestamp - animationState.startTime;
            animationState.progress = Math.min(elapsed / animationState.duration, 1);

            clearCanvas();

            const wizardX = canvas.width * 0.7;
            const wizardY = canvas.height * 0.6;
            const studentX = canvas.width * 0.3;
            const studentY = canvas.height * 0.7;
            const ballX = canvas.width * 0.5;
            const ballY = canvas.height * 0.6;

            drawWizard(wizardX, wizardY);
            drawStudent(studentX, studentY);
            
            let nextFrame = true;

            switch (animationState.scene) {
                case 1: // pre- (before)
                    explanationDiv.innerHTML = `故事开始了。一位学生来找智者，想要 <b style="color:#e67e22;">pre-</b> (预先) 知道未来。`;
                    drawText('pre- (预先)', studentX, studentY - 40, 24, '#e67e22');
                    drawCrystalBall(ballX, ballY, 40);
                    break;
                case 2: // dict- (say)
                    explanationDiv.innerHTML = `智者代表着 <b style="color:#8e44ad;">dict</b> (说) 的能力，他能"说"出一些事情。`;
                    drawText('dict (说)', wizardX, wizardY - 60, 24, '#8e44ad');
                    drawCrystalBall(ballX, ballY, 40);
                    break;
                case 3: // -able (can be done)
                    explanationDiv.innerHTML = `智者说，未来是... <b style="color:#2ecc71;">-able</b> (能够) 被预见的。水晶球里出现了微弱的影像。`;
                    drawCrystalBall(ballX, ballY, 40);
                    // Draw a faint image inside the ball
                    ctx.save();
                    ctx.globalAlpha = animationState.progress * 0.5;
                    drawText('未来', ballX, ballY, 20, '#16a085');
                    ctx.restore();
                    break;
                case 4: // un- (not)
                    explanationDiv.innerHTML = `但突然间，<b style="color:#c0392b;">un-</b> (不)！一阵迷雾遮蔽了未来。未来是"不"能被预知的。`;
                    drawCrystalBall(ballX, ballY, 40);
                    drawText('未来', ballX, ballY, 20, '#16a085');
                    // Draw fog
                    ctx.save();
                    ctx.fillStyle = `rgba(189, 195, 199, ${animationState.progress * 0.8})`;
                    ctx.beginPath();
                    ctx.arc(ballX, ballY, 45, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                    break;
                case 5: // final word
                    explanationDiv.innerHTML = `所以，<b style="color:#c0392b;">un</b>-<b style="color:#e67e22;">pre</b>-<b style="color:#8e44ad;">dict</b>-<b style="color:#2ecc71;">able</b> 的意思就是：<br>"不能被预先说出的"，即"不可预测的"。`;
                    drawText('unpredictable', canvas.width / 2, canvas.height / 2, 40, '#007BFF');
                    nextFrame = false;
                    startBtn.disabled = false;
                    startBtn.textContent = '重新播放';
                    break;
                default:
                    explanationDiv.textContent = '点击按钮，开始学习单词 "unpredictable" 的故事。';
                    drawText('点击"开始动画"', canvas.width/2, canvas.height/2, 25, '#999');
                    nextFrame = false;
            }

            if (animationState.progress >= 1) {
                animationState.scene++;
                animationState.startTime = null;
                animationState.progress = 0;
            }

            if (nextFrame) {
                requestAnimationFrame(animate);
            }
        }

        startBtn.addEventListener('click', () => {
            startBtn.disabled = true;
            animationState.scene = 1;
            animationState.startTime = null;
            animationState.progress = 0;
            requestAnimationFrame(animate);
        });

        // Initial drawing
        animate();
    </script>
</body>
</html> 