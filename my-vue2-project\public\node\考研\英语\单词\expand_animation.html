<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Expand</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #4CAF50; /* A green color for "expand" */
            --secondary-color: #2E7D32;
            --light-bg: #E8F5E9;
            --panel-bg: #ffffff;
            --text-color: #333;
            --text-muted: #7f8c8d;
            --canvas-bg: #f0f4c3; /* Light, natural background */
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--secondary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--text-muted);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #555;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #C8E6C9;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .breakdown-section .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            background: var(--canvas-bg);
        }

        #animation-canvas {
            width: 100%;
            height: calc(100% - 80px);
            border-radius: 15px;
            background: #111;
        }
        
        .control-button {
            position: absolute;
            bottom: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.5);
            z-index: 10;
        }
        .control-button:hover { background-color: #388E3C; transform: translateY(-2px); }
        .control-button.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>expand</h1>
            <p class="pronunciation">[ɪkˈspænd]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 扩大, 扩张, 展开</p>
                <div class="example">
                    <p><strong>例句：</strong> The universe continues to expand.</p>
                    <p><strong>翻译：</strong> 宇宙在持续扩张。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="ex-game">ex- (向外)</button>
                    <button class="morpheme-btn" data-activity="pand-game">-pand- (展开)</button>
                </div>
                <p class="insight">点击上方词缀，体验Canvas绘制的互动游戏！</p>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">粒子大爆炸</button>
                    <button class="morpheme-btn" data-activity="vine-animation">藤蔓生长</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="control-btn" class="control-button hidden">Expand!</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const controlBtn = document.getElementById('control-btn');
        let animationFrameId;

        const panel = canvas.parentElement;
        canvas.width = panel.clientWidth;
        canvas.height = panel.clientHeight - 80;

        let particles = [];
        let vines = [];
        
        class Particle {
            constructor(x, y, vx, vy, color, size, life = 1) {
                this.x = x; this.y = y; this.vx = vx; this.vy = vy;
                this.color = color; this.size = size; this.life = life;
            }
            update() {
                this.x += this.vx; this.y += this.vy;
                this.life -= 0.005;
            }
            draw() {
                ctx.globalAlpha = Math.max(0, this.life);
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;
            }
        }

        class Vine {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.history = [{x: this.x, y: this.y}];
                this.angle = Math.random() * Math.PI - Math.PI / 2 - Math.PI/4;
                this.speed = Math.random() * 2 + 1;
                this.life = 1;
                this.lineWidth = Math.random() * 3 + 1;
            }
            update() {
                this.life -= 0.005;
                const vx = Math.cos(this.angle) * this.speed;
                const vy = Math.sin(this.angle) * this.speed;
                this.x += vx;
                this.y += vy;
                this.angle += (Math.random() - 0.5) * 0.2;
                this.history.push({x: this.x, y: this.y});
                if(this.history.length > 50) {
                    this.history.shift();
                }

                // Chance to fork
                if(Math.random() < 0.02 && vines.length < 100) {
                    vines.push(new Vine(this.x, this.y));
                }

                // Chance to flower
                if(Math.random() < 0.05) {
                    particles.push(new Particle(this.x, this.y, 0, 0, `hsl(${Math.random() * 360}, 100%, 70%)`, 3, 0.5));
                }
            }
            draw() {
                 ctx.strokeStyle = `hsla(120, 100%, 50%, ${this.life})`;
                 ctx.lineWidth = this.lineWidth;
                 ctx.beginPath();
                 ctx.moveTo(this.history[0].x, this.history[0].y);
                 for(let i=1; i<this.history.length; i++) {
                     ctx.lineTo(this.history[i].x, this.history[i].y);
                 }
                 ctx.stroke();
            }
        }
        
        function welcomeScreen() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#fff';
            ctx.font = '24px Roboto';
            ctx.textAlign = 'center';
            ctx.fillText('新篇章：第二个单词！', canvas.width / 2, canvas.height / 2 - 20);
            ctx.fillText('请选择一个活动来开始。', canvas.width / 2, canvas.height / 2 + 20);
            controlBtn.classList.add('hidden');
        }

        // --- Morpheme Games ---
        function initExGame() {
            particles = [];
            controlBtn.textContent = '向外发射';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                const cx = canvas.width / 2;
                const cy = canvas.height / 2;
                for (let i = 0; i < 100; i++) {
                    const angle = Math.random() * Math.PI * 2;
                    particles.push(new Particle(cx, cy, Math.cos(angle) * 3, Math.sin(angle) * 3, `hsl(120, 100%, ${60 + Math.random() * 40}%)`, 2));
                }
            };
            animateParticles();
        }
        
        function initPandGame() {
            particles = [];
            controlBtn.textContent = '展开粒子';
            controlBtn.classList.remove('hidden');
             controlBtn.onclick = () => {
                const cx = canvas.width / 2;
                const cy = canvas.height / 2;
                if(particles.length < 100){
                    for (let i = 0; i < 10; i++) {
                         const angle = Math.random() * Math.PI * 2;
                         const speed = Math.random() * 2;
                        particles.push(new Particle(cx, cy, Math.cos(angle) * speed, Math.sin(angle) * speed, `hsl(200, 100%, ${60 + Math.random() * 40}%)`, 3, 1));
                    }
                }
            };
            animateParticles();
        }

        function animateParticles() {
            ctx.fillStyle = 'rgba(17, 17, 17, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            particles.forEach((p, index) => {
                if (p.life > 0) { p.update(); p.draw(); }
                else { particles.splice(index, 1); }
            });
            animationFrameId = requestAnimationFrame(animateParticles);
        }
        
        // --- Full Animation ---
        function initFullAnimation() {
            particles = [];
            controlBtn.textContent = 'Expand!';
            controlBtn.classList.remove('hidden');
            
            // Initial compressed state
            for (let i = 0; i < 300; i++) {
                particles.push(new Particle(canvas.width / 2, canvas.height / 2, 0, 0, `hsl(${180 + Math.random() * 60}, 100%, 70%)`, 2, 1));
            }

            controlBtn.onclick = () => {
                 particles.forEach(p => {
                    const angle = Math.random() * Math.PI * 2;
                    const speed = Math.random() * 4 + 1;
                    p.vx = Math.cos(angle) * speed;
                    p.vy = Math.sin(angle) * speed;
                    p.life = 1; // Reset life on expansion
                 });
            };
            animateFull();
        }
        
        function animateFull() {
            ctx.fillStyle = 'rgba(17, 17, 17, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            particles.forEach((p, index) => {
                if (p.life > 0) {
                    p.vx *= 0.99; // friction
                    p.vy *= 0.99;
                    p.update();
                    p.draw();
                }
            });
            
            animationFrameId = requestAnimationFrame(animateFull);
        }

        // --- New Vine Animation ---
        function initVineAnimation() {
            vines = [];
            particles = [];
            controlBtn.textContent = '开始生长';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                if (vines.length === 0) {
                     vines.push(new Vine(canvas.width/2, canvas.height));
                }
            };
            animateVine();
        }

        function animateVine() {
            ctx.fillStyle = 'rgba(17, 17, 17, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            vines.forEach((v, index) => {
                 if(v.life > 0) {
                    v.update();
                    v.draw();
                 } else {
                    vines.splice(index, 1);
                 }
            });

             particles.forEach((p, index) => {
                if (p.life > 0) {
                    p.life -= 0.01;
                    p.draw();
                } else {
                    particles.splice(index, 1);
                }
            });

            animationFrameId = requestAnimationFrame(animateVine);
        }

        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                if (animationFrameId) cancelAnimationFrame(animationFrameId);
                const activity = btn.dataset.activity;
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                 particles = []; // Clear particles for new activity
                if (activity === 'ex-game') initExGame();
                else if (activity === 'pand-game') initPandGame();
                else if (activity === 'full-animation') initFullAnimation();
                else if (activity === 'vine-animation') initVineAnimation();
            });
        });

        welcomeScreen();
    });
    </script>
</body>
</html>