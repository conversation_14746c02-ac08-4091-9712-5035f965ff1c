<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java反射与深浅拷贝</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #0066cc;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .concept {
            background-color: #f9f9f9;
            padding: 15px;
            border-left: 4px solid #0066cc;
            margin-bottom: 20px;
        }
        .demo-container {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px auto;
            display: block;
        }
        .buttons {
            text-align: center;
            margin: 15px 0;
        }
        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 8px 15px;
            margin: 0 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #004c99;
        }
        .code {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre;
            overflow-x: auto;
            line-height: 1.4;
        }
        .tab-container {
            display: flex;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #eee;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #0066cc;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Java核心概念：反射与拷贝</h1>
        
        <div class="tab-container">
            <div class="tab active" onclick="openTab('reflection')">反射</div>
            <div class="tab" onclick="openTab('copy')">浅拷贝与深拷贝</div>
        </div>

        <!-- 反射部分 -->
        <div id="reflection" class="tab-content active">
            <h2>Java反射机制</h2>
            
            <div class="concept">
                <p><strong>反射的定义：</strong>在运行过程中，对于任何一个类都能够获取它的属性和方法，任何一个对象都能调用其方法，这种动态获取信息和动态调用的能力，就是反射。</p>
            </div>
            
            <h3>Java获取反射的三种方法</h3>
            <div class="demo-container">
                <canvas id="reflectionCanvas" width="800" height="400"></canvas>
                <div class="buttons">
                    <button onclick="showReflectionMethod(1)">通过new对象</button>
                    <button onclick="showReflectionMethod(2)">通过路径</button>
                    <button onclick="showReflectionMethod(3)">通过类名</button>
                </div>
            </div>
            
            <div class="code">
// 方法一：通过new对象实现反射
Student student = new Student();
Class clazz1 = student.getClass();

// 方法二：通过路径实现反射
Class clazz2 = Class.forName("com.example.Student");

// 方法三：通过类名实现反射
Class clazz3 = Student.class;
            </div>
        </div>

        <!-- 浅拷贝与深拷贝部分 -->
        <div id="copy" class="tab-content">
            <h2>浅拷贝与深拷贝</h2>
            
            <div class="concept">
                <p><strong>浅拷贝：</strong>基础数据类型复制值，引用类型复制引用地址，修改一个对象的值，另一个对象也随之改变。</p>
                <p><strong>深拷贝：</strong>基础数据类型复制值，引用类型在新的内存空间复制值，新老对象不共享内存，修改一个值，不影响另一个。</p>
            </div>

            <div class="demo-container">
                <canvas id="copyCanvas" width="800" height="500"></canvas>
                <div class="buttons">
                    <button onclick="demonstrateShallowCopy()">演示浅拷贝</button>
                    <button onclick="demonstrateDeepCopy()">演示深拷贝</button>
                    <button onclick="modifyObject()">修改对象</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function openTab(tabName) {
            const tabContents = document.getElementsByClassName('tab-content');
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove('active');
            }
            
            const tabs = document.getElementsByClassName('tab');
            for (let i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            document.getElementById(tabName).classList.add('active');
            
            const activeTab = document.querySelector(`.tab[onclick="openTab('${tabName}')"]`);
            if (activeTab) {
                activeTab.classList.add('active');
            }
            
            if (tabName === 'reflection') {
                initReflectionCanvas();
            } else if (tabName === 'copy') {
                initCopyCanvas();
            }
        }
        
        // 反射相关
        let reflectionCtx;
        
        function initReflectionCanvas() {
            const canvas = document.getElementById('reflectionCanvas');
            reflectionCtx = canvas.getContext('2d');
            clearCanvas(reflectionCtx, canvas);
            
            reflectionCtx.fillStyle = '#333';
            reflectionCtx.font = '20px Microsoft YaHei';
            reflectionCtx.textAlign = 'center';
            reflectionCtx.fillText('请点击上方按钮查看反射的不同方法', canvas.width/2, canvas.height/2);
        }
        
        function showReflectionMethod(method) {
            const canvas = document.getElementById('reflectionCanvas');
            reflectionCtx = canvas.getContext('2d');
            clearCanvas(reflectionCtx, canvas);
            
            reflectionCtx.fillStyle = '#333';
            reflectionCtx.font = '18px Microsoft YaHei';
            
            drawClass(reflectionCtx, 200, 150, 'Student');
            
            switch(method) {
                case 1:
                    // 通过new对象
                    reflectionCtx.fillStyle = '#333';
                    reflectionCtx.textAlign = 'center';
                    reflectionCtx.fillText('方法一: 通过new对象实现反射', canvas.width/2, 50);
                    
                    drawObject(reflectionCtx, 200, 300, 'student');
                    drawArrow(reflectionCtx, 200, 250, 200, 280);
                    
                    drawClass(reflectionCtx, 550, 300, 'Class<Student>');
                    drawArrow(reflectionCtx, 300, 300, 450, 300);
                    
                    reflectionCtx.fillStyle = '#0066cc';
                    reflectionCtx.textAlign = 'center';
                    reflectionCtx.font = '16px Microsoft YaHei';
                    reflectionCtx.fillText('Student student = new Student();', 200, 350);
                    reflectionCtx.fillText('Class clazz1 = student.getClass();', 400, 370);
                    break;
                    
                case 2:
                    // 通过路径
                    reflectionCtx.fillStyle = '#333';
                    reflectionCtx.textAlign = 'center';
                    reflectionCtx.fillText('方法二: 通过路径实现反射', canvas.width/2, 50);
                    
                    drawClasspath(reflectionCtx, 200, 300, 'com.example.Student');
                    drawClass(reflectionCtx, 550, 300, 'Class<Student>');
                    drawArrow(reflectionCtx, 350, 300, 450, 300);
                    
                    reflectionCtx.fillStyle = '#0066cc';
                    reflectionCtx.textAlign = 'center';
                    reflectionCtx.font = '16px Microsoft YaHei';
                    reflectionCtx.fillText('Class clazz2 = Class.forName("com.example.Student");', 400, 370);
                    break;
                    
                case 3:
                    // 通过类名
                    reflectionCtx.fillStyle = '#333';
                    reflectionCtx.textAlign = 'center';
                    reflectionCtx.fillText('方法三: 通过类名实现反射', canvas.width/2, 50);
                    
                    reflectionCtx.fillStyle = '#0066cc';
                    reflectionCtx.font = '16px Microsoft YaHei';
                    reflectionCtx.fillText('Student.class', 200, 250);
                    
                    drawClass(reflectionCtx, 550, 200, 'Class<Student>');
                    drawArrow(reflectionCtx, 250, 200, 450, 200);
                    
                    reflectionCtx.fillStyle = '#0066cc';
                    reflectionCtx.textAlign = 'center';
                    reflectionCtx.fillText('Class clazz3 = Student.class;', 400, 300);
                    break;
            }
        }
        
        // 浅拷贝与深拷贝相关
        let copyCtx;
        let originalObject, copiedObject;
        let copyType = '';
        
        function initCopyCanvas() {
            const canvas = document.getElementById('copyCanvas');
            copyCtx = canvas.getContext('2d');
            clearCanvas(copyCtx, canvas);
            
            copyCtx.fillStyle = '#333';
            copyCtx.font = '20px Microsoft YaHei';
            copyCtx.textAlign = 'center';
            copyCtx.fillText('请点击上方按钮查看浅拷贝和深拷贝的区别', canvas.width/2, canvas.height/2);
            
            // 初始化对象
            originalObject = {
                name: "对象1",
                value: 100,
                reference: { id: 1, data: "数据" }
            };
            
            copiedObject = null;
        }
        
        function demonstrateShallowCopy() {
            copyType = 'shallow';
            copiedObject = shallowCopy(originalObject);
            drawCopyDemo();
        }
        
        function demonstrateDeepCopy() {
            copyType = 'deep';
            copiedObject = deepCopy(originalObject);
            drawCopyDemo();
        }
        
        function modifyObject() {
            if (!copiedObject) {
                alert('请先进行拷贝操作！');
                return;
            }
            
            // 修改对象
            copiedObject.value = 200;
            copiedObject.reference.data = "修改后的数据";
            
            drawCopyDemo();
        }
        
        function shallowCopy(obj) {
            return {...obj};
        }
        
        function deepCopy(obj) {
            return JSON.parse(JSON.stringify(obj));
        }
        
        function drawCopyDemo() {
            const canvas = document.getElementById('copyCanvas');
            copyCtx = canvas.getContext('2d');
            clearCanvas(copyCtx, canvas);
            
            copyCtx.fillStyle = '#333';
            copyCtx.font = '20px Microsoft YaHei';
            copyCtx.textAlign = 'center';
            
            if (copyType === 'shallow') {
                copyCtx.fillText('浅拷贝演示', canvas.width/2, 30);
            } else {
                copyCtx.fillText('深拷贝演示', canvas.width/2, 30);
            }
            
            // 绘制原始对象
            copyCtx.fillStyle = '#333';
            copyCtx.font = '16px Microsoft YaHei';
            copyCtx.textAlign = 'left';
            copyCtx.fillText('原始对象：', 50, 80);
            
            drawComplexObject(copyCtx, 150, 150, originalObject, 'original');
            
            // 绘制拷贝对象
            copyCtx.fillStyle = '#333';
            copyCtx.font = '16px Microsoft YaHei';
            copyCtx.textAlign = 'left';
            copyCtx.fillText('拷贝对象：', 450, 80);
            
            drawComplexObject(copyCtx, 550, 150, copiedObject, 'copied');
            
            // 绘制引用关系
            if (copyType === 'shallow') {
                // 浅拷贝时引用类型指向同一地址
                drawArrow(copyCtx, 250, 250, 650, 250, '#e74c3c');
                
                copyCtx.fillStyle = '#e74c3c';
                copyCtx.font = '14px Microsoft YaHei';
                copyCtx.textAlign = 'center';
                copyCtx.fillText('共享同一引用', 450, 240);
            }
        }
        
        function drawComplexObject(ctx, x, y, object, type) {
            // 主对象框
            ctx.fillStyle = type === 'original' ? '#3498db' : '#9b59b6';
            ctx.fillRect(x - 80, y - 50, 160, 180);
            
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(object.name, x, y - 30);
            
            // 基本类型
            ctx.fillStyle = '#ecf0f1';
            ctx.fillRect(x - 70, y - 10, 140, 30);
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText(`value: ${object.value}`, x - 60, y + 10);
            
            // 引用类型
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(x - 70, y + 30, 140, 80);
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('reference', x, y + 50);
            ctx.fillText(`id: ${object.reference.id}`, x, y + 70);
            ctx.fillText(`data: ${object.reference.data}`, x, y + 90);
        }
        
        // 绘图辅助函数
        function clearCanvas(ctx, canvas) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        function drawClass(ctx, x, y, name) {
            ctx.fillStyle = '#3498db';
            ctx.fillRect(x - 80, y - 40, 160, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y);
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('Class', x, y + 25);
        }
        
        function drawObject(ctx, x, y, name) {
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(x - 80, y - 40, 160, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y);
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('Object', x, y + 25);
        }
        
        function drawClasspath(ctx, x, y, path) {
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(x - 120, y - 40, 240, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(path, x, y);
            ctx.fillText('路径', x, y + 25);
        }
        
        function drawArrow(ctx, fromX, fromY, toX, toY, color = '#333') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            
            // 箭头
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const length = 15;
            
            ctx.lineTo(toX - length * Math.cos(angle - Math.PI / 6), toY - length * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - length * Math.cos(angle + Math.PI / 6), toY - length * Math.sin(angle + Math.PI / 6));
            
            ctx.stroke();
        }
        
        // 初始化
        window.onload = function() {
            initReflectionCanvas();
        };
    </script>
</body>
</html> 