<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银行家算法 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .content-section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8em;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
        }

        .concept-box {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        th, td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
            transition: background-color 0.3s ease;
        }

        th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .process-row {
            cursor: pointer;
        }

        .process-row:hover {
            background-color: #f0f8ff;
        }

        .highlight {
            background-color: #90EE90 !important;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .canvas-container {
            text-align: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #667eea;
            border-radius: 10px;
            background: white;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .explanation {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .resource-visual {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }

        .resource-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            min-width: 100px;
        }

        .resource-count {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 银行家算法学习</h1>
            <p>操作系统死锁避免算法的可视化教学</p>
        </div>

        <div class="content-section">
            <h2 class="section-title">📚 什么是银行家算法？</h2>
            <div class="concept-box">
                <h3>💡 核心概念</h3>
                <p>银行家算法是操作系统中用来避免死锁的经典算法。就像银行放贷一样，系统需要确保在分配资源时，始终保持在"安全状态"，避免进程因为等待资源而陷入死锁。</p>
            </div>
            
            <div class="explanation">
                <h4>🎯 算法目标</h4>
                <ul>
                    <li>确保系统始终处于安全状态</li>
                    <li>找到一个安全序列，让所有进程都能顺利完成</li>
                    <li>避免死锁的发生</li>
                </ul>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">📊 题目数据分析</h2>
            
            <div class="resource-visual">
                <div class="resource-item">
                    <div class="resource-count" id="r1-count">9</div>
                    <div>R1资源</div>
                </div>
                <div class="resource-item">
                    <div class="resource-count" id="r2-count">8</div>
                    <div>R2资源</div>
                </div>
                <div class="resource-item">
                    <div class="resource-count" id="r3-count">5</div>
                    <div>R3资源</div>
                </div>
            </div>

            <h3>最大需求量表</h3>
            <div class="table-container">
                <table id="max-table">
                    <thead>
                        <tr>
                            <th>进程</th>
                            <th>R1</th>
                            <th>R2</th>
                            <th>R3</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="process-row" data-process="P1">
                            <td>P1</td>
                            <td>6</td>
                            <td>5</td>
                            <td>2</td>
                        </tr>
                        <tr class="process-row" data-process="P2">
                            <td>P2</td>
                            <td>2</td>
                            <td>2</td>
                            <td>1</td>
                        </tr>
                        <tr class="process-row" data-process="P3">
                            <td>P3</td>
                            <td>8</td>
                            <td>1</td>
                            <td>1</td>
                        </tr>
                        <tr class="process-row" data-process="P4">
                            <td>P4</td>
                            <td>1</td>
                            <td>2</td>
                            <td>1</td>
                        </tr>
                        <tr class="process-row" data-process="P5">
                            <td>P5</td>
                            <td>3</td>
                            <td>4</td>
                            <td>4</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h3>已分配资源表</h3>
            <div class="table-container">
                <table id="allocated-table">
                    <thead>
                        <tr>
                            <th>进程</th>
                            <th>R1</th>
                            <th>R2</th>
                            <th>R3</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="process-row" data-process="P1">
                            <td>P1</td>
                            <td>1</td>
                            <td>2</td>
                            <td>1</td>
                        </tr>
                        <tr class="process-row" data-process="P2">
                            <td>P2</td>
                            <td>2</td>
                            <td>1</td>
                            <td>1</td>
                        </tr>
                        <tr class="process-row" data-process="P3">
                            <td>P3</td>
                            <td>2</td>
                            <td>1</td>
                            <td>0</td>
                        </tr>
                        <tr class="process-row" data-process="P4">
                            <td>P4</td>
                            <td>1</td>
                            <td>2</td>
                            <td>0</td>
                        </tr>
                        <tr class="process-row" data-process="P5">
                            <td>P5</td>
                            <td>1</td>
                            <td>1</td>
                            <td>3</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🧮 需求矩阵计算</h2>
            <div class="explanation">
                <h4>💡 计算公式</h4>
                <p><strong>需求矩阵 = 最大需求矩阵 - 已分配矩阵</strong></p>
                <p>这表示每个进程还需要多少资源才能完成任务</p>
            </div>

            <div class="table-container">
                <table id="need-table">
                    <thead>
                        <tr>
                            <th>进程</th>
                            <th>R1需求</th>
                            <th>R2需求</th>
                            <th>R3需求</th>
                            <th>计算过程</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>P1</td>
                            <td class="need-cell">5</td>
                            <td class="need-cell">3</td>
                            <td class="need-cell">1</td>
                            <td>(6-1, 5-2, 2-1)</td>
                        </tr>
                        <tr>
                            <td>P2</td>
                            <td class="need-cell">0</td>
                            <td class="need-cell">1</td>
                            <td class="need-cell">0</td>
                            <td>(2-2, 2-1, 1-1)</td>
                        </tr>
                        <tr>
                            <td>P3</td>
                            <td class="need-cell">6</td>
                            <td class="need-cell">0</td>
                            <td class="need-cell">1</td>
                            <td>(8-2, 1-1, 1-0)</td>
                        </tr>
                        <tr>
                            <td>P4</td>
                            <td class="need-cell">0</td>
                            <td class="need-cell">0</td>
                            <td class="need-cell">1</td>
                            <td>(1-1, 2-2, 1-0)</td>
                        </tr>
                        <tr>
                            <td>P5</td>
                            <td class="need-cell">2</td>
                            <td class="need-cell">3</td>
                            <td class="need-cell">1</td>
                            <td>(3-1, 4-1, 4-3)</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎮 交互式算法演示</h2>

            <div class="step-indicator">
                <div class="step active" id="step-1">1</div>
                <div class="step" id="step-2">2</div>
                <div class="step" id="step-3">3</div>
                <div class="step" id="step-4">4</div>
                <div class="step" id="step-5">5</div>
            </div>

            <div class="canvas-container">
                <canvas id="algorithmCanvas" width="800" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="startDemo()">🚀 开始演示</button>
                <button class="btn" onclick="nextStep()">➡️ 下一步</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
                <button class="btn" onclick="showAnswer()">💡 查看答案</button>
            </div>

            <div id="current-explanation" class="explanation">
                <h4>📝 当前步骤说明</h4>
                <p>点击"开始演示"来学习银行家算法的执行过程</p>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎯 解题思路详解</h2>

            <div class="concept-box">
                <h3>🔍 解题步骤</h3>
                <ol style="text-align: left; margin-left: 20px;">
                    <li><strong>计算需求矩阵：</strong>Need = Max - Allocation</li>
                    <li><strong>计算可用资源：</strong>Available = Total - 所有已分配资源之和</li>
                    <li><strong>寻找安全序列：</strong>找到Need ≤ Available的进程</li>
                    <li><strong>模拟执行：</strong>进程完成后释放资源，更新Available</li>
                    <li><strong>重复步骤3-4：</strong>直到所有进程完成</li>
                </ol>
            </div>

            <div class="explanation">
                <h4>🧠 关键理解点</h4>
                <ul>
                    <li><strong>安全状态：</strong>存在一个进程执行序列，使得所有进程都能顺利完成</li>
                    <li><strong>资源分配原则：</strong>只有当分配后系统仍处于安全状态时，才能分配资源</li>
                    <li><strong>贪心策略：</strong>优先选择需求最少的进程执行</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 算法数据
        const data = {
            max: [[6,5,2], [2,2,1], [8,1,1], [1,2,1], [3,4,4]],
            allocated: [[1,2,1], [2,1,1], [2,1,0], [1,2,0], [1,1,3]],
            available: [2,1,0], // 初始可用资源
            processes: ['P1', 'P2', 'P3', 'P4', 'P5'],
            need: [], // 将计算得出
            safeSequence: [],
            currentStep: 0,
            currentProcess: -1 // 当前正在执行的进程
        };

        // 计算需求矩阵
        function calculateNeed() {
            data.need = [];
            for(let i = 0; i < 5; i++) {
                data.need[i] = [];
                for(let j = 0; j < 3; j++) {
                    data.need[i][j] = data.max[i][j] - data.allocated[i][j];
                }
            }
        }

        // 初始化
        calculateNeed();

        // Canvas相关 - 等待DOM加载完成
        let canvas, ctx;

        function initCanvas() {
            canvas = document.getElementById('algorithmCanvas');
            if (canvas) {
                ctx = canvas.getContext('2d');
                drawVisualization();
            }
        }

        function drawVisualization() {
            if (!canvas || !ctx) return;
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('🏦 银行家算法执行过程', canvas.width/2, 30);

            // 绘制当前可用资源 - 更醒目的显示
            drawResourceBox(50, 50, data.available, '当前可用资源');

            // 绘制进程状态
            const processY = 120;
            const processHeight = 80;
            const processWidth = 140;

            for(let i = 0; i < 5; i++) {
                const x = 20 + i * 150;
                const y = processY;

                // 进程框 - 根据状态使用不同颜色
                let fillColor = '#f8f9fa';
                let strokeColor = '#667eea';

                if(data.safeSequence.includes(i)) {
                    fillColor = '#d4edda';
                    strokeColor = '#28a745';
                } else if(data.currentProcess === i) {
                    fillColor = '#fff3cd';
                    strokeColor = '#ffc107';
                }

                ctx.fillStyle = fillColor;
                ctx.fillRect(x, y, processWidth, processHeight);
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 3;
                ctx.strokeRect(x, y, processWidth, processHeight);

                // 进程名
                ctx.fillStyle = '#333';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(data.processes[i], x + processWidth/2, y + 25);

                // 需求资源
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(`需要: (${data.need[i].join(',')})`, x + processWidth/2, y + 45);

                // 状态标识
                if(data.safeSequence.includes(i)) {
                    ctx.fillStyle = '#28a745';
                    ctx.fillText('✓ 已完成', x + processWidth/2, y + 65);
                } else if(data.currentProcess === i) {
                    ctx.fillStyle = '#ffc107';
                    ctx.fillText('⚡ 执行中', x + processWidth/2, y + 65);
                }
            }

            // 绘制安全序列
            if(data.safeSequence.length > 0) {
                const sequenceY = 250;
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#28a745';
                ctx.textAlign = 'center';
                ctx.fillText('🎯 安全序列', canvas.width/2, sequenceY);

                // 绘制序列箭头
                const sequence = data.safeSequence.map(i => data.processes[i]);
                const startX = (canvas.width - sequence.length * 80) / 2;

                for(let i = 0; i < sequence.length; i++) {
                    const x = startX + i * 80;
                    const y = sequenceY + 30;

                    // 进程圆圈
                    ctx.beginPath();
                    ctx.arc(x + 25, y, 20, 0, 2 * Math.PI);
                    ctx.fillStyle = '#28a745';
                    ctx.fill();
                    ctx.strokeStyle = '#fff';
                    ctx.lineWidth = 2;
                    ctx.stroke();

                    // 进程名
                    ctx.fillStyle = '#fff';
                    ctx.font = 'bold 14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(sequence[i], x + 25, y + 5);

                    // 箭头
                    if(i < sequence.length - 1) {
                        drawArrow(x + 45, y, x + 55, y);
                    }
                }
            }

            // 绘制算法步骤提示
            if(data.currentStep > 0) {
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = '#6c757d';
                ctx.textAlign = 'center';
                ctx.fillText(`第 ${data.currentStep} 步`, canvas.width/2, canvas.height - 20);
            }
        }

        function drawResourceBox(x, y, resources, title) {
            const boxWidth = 300;
            const boxHeight = 50;

            // 背景框
            ctx.fillStyle = '#e3f2fd';
            ctx.fillRect(x, y, boxWidth, boxHeight);
            ctx.strokeStyle = '#2196f3';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, boxWidth, boxHeight);

            // 标题
            ctx.fillStyle = '#1976d2';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText(title, x + 10, y + 20);

            // 资源数量
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillText(`R1:${resources[0]} R2:${resources[1]} R3:${resources[2]}`, x + 10, y + 40);
        }

        function drawArrow(fromX, fromY, toX, toY) {
            const headlen = 8;
            const angle = Math.atan2(toY - fromY, toX - fromX);

            ctx.strokeStyle = '#28a745';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // 算法步骤说明
        const stepExplanations = [
            "初始状态：计算每个进程还需要的资源数量",
            "寻找可以满足的进程：找到需求资源 ≤ 可用资源的进程",
            "执行进程：选中的进程完成后释放所有资源",
            "更新可用资源：将释放的资源加入可用资源池",
            "重复过程：继续寻找下一个可以执行的进程"
        ];

        let demoStep = 0;
        let isRunning = false;

        function startDemo() {
            if(isRunning) return;
            isRunning = true;
            demoStep = 0;
            data.safeSequence = [];
            data.available = [2,1,0]; // 重置可用资源
            
            updateStepIndicator(1);
            updateExplanation(stepExplanations[0]);
            drawVisualization();
            
            // 开始算法演示
            setTimeout(runBankerAlgorithm, 1000);
        }

        function runBankerAlgorithm() {
            const finished = new Array(5).fill(false);
            const tempAvailable = [...data.available];
            const sequence = [];

            // 模拟算法执行过程
            const steps = [
                () => checkProcess(1, finished, tempAvailable, sequence), // P2
                () => checkProcess(3, finished, tempAvailable, sequence), // P4
                () => checkProcess(4, finished, tempAvailable, sequence), // P5
                () => checkProcess(0, finished, tempAvailable, sequence), // P1
                () => checkProcess(2, finished, tempAvailable, sequence)  // P3
            ];

            let stepIndex = 0;
            data.currentStep = 1;

            function executeNextStep() {
                if(stepIndex < steps.length) {
                    data.currentStep = stepIndex + 1;
                    steps[stepIndex]();
                    stepIndex++;
                    setTimeout(executeNextStep, 3000);
                } else {
                    updateExplanation(`
                        <h4>🎉 算法完成！</h4>
                        <p><strong>找到安全序列：P2→P4→P5→P1→P3</strong></p>
                        <p>这个序列保证了所有进程都能顺利完成，系统处于安全状态。</p>
                        <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-top: 10px;">
                            <strong>答案解析：</strong>选项C正确 - P2→P4→P5→P1→P3
                        </div>
                    `);
                    data.currentProcess = -1;
                    isRunning = false;
                }
            }

            executeNextStep();
        }

        function checkProcess(processIndex, finished, available, sequence) {
            if(finished[processIndex]) return;

            // 设置当前执行的进程
            data.currentProcess = processIndex;

            // 检查是否可以满足需求
            let canExecute = true;
            for(let j = 0; j < 3; j++) {
                if(data.need[processIndex][j] > available[j]) {
                    canExecute = false;
                    break;
                }
            }

            if(canExecute) {
                // 先显示正在检查的状态
                updateExplanation(`
                    <h4>🔍 检查进程 ${data.processes[processIndex]}</h4>
                    <p><strong>需要资源：</strong>(${data.need[processIndex].join(',')})</p>
                    <p><strong>可用资源：</strong>(${available.join(',')})</p>
                    <p><strong>判断：</strong>需要 ≤ 可用 ✓ 可以执行</p>
                `);
                drawVisualization();

                // 延迟执行，让用户看到检查过程
                setTimeout(() => {
                    // 执行进程
                    finished[processIndex] = true;
                    sequence.push(processIndex);
                    data.safeSequence.push(processIndex);

                    // 释放资源
                    for(let j = 0; j < 3; j++) {
                        available[j] += data.allocated[processIndex][j];
                    }

                    data.available = [...available];

                    updateStepIndicator(Math.min(sequence.length + 1, 5));
                    updateExplanation(`
                        <h4>✅ 进程 ${data.processes[processIndex]} 执行完成</h4>
                        <p><strong>释放资源：</strong>(${data.allocated[processIndex].join(',')})</p>
                        <p><strong>更新后可用资源：</strong>(${available.join(',')})</p>
                        <p><strong>当前安全序列：</strong>${data.safeSequence.map(i => data.processes[i]).join(' → ')}</p>
                    `);

                    // 高亮当前进程
                    highlightProcess(processIndex);
                    drawVisualization();
                }, 1000);
            } else {
                updateExplanation(`
                    <h4>❌ 进程 ${data.processes[processIndex]} 无法执行</h4>
                    <p><strong>需要资源：</strong>(${data.need[processIndex].join(',')})</p>
                    <p><strong>可用资源：</strong>(${available.join(',')})</p>
                    <p><strong>判断：</strong>需要 > 可用，暂时无法满足</p>
                `);
            }
        }

        function highlightProcess(processIndex) {
            // 移除之前的高亮
            document.querySelectorAll('.process-row').forEach(row => {
                row.classList.remove('highlight');
            });
            
            // 添加新的高亮
            document.querySelectorAll(`[data-process="${data.processes[processIndex]}"]`).forEach(row => {
                row.classList.add('highlight');
            });
        }

        function updateStepIndicator(step) {
            document.querySelectorAll('.step').forEach((el, index) => {
                el.classList.toggle('active', index + 1 === step);
            });
        }

        function updateExplanation(text) {
            document.getElementById('current-explanation').innerHTML = `
                <h4>📝 当前步骤说明</h4>
                <p>${text}</p>
            `;
        }

        function nextStep() {
            // 手动步进功能
            if(!isRunning && demoStep < stepExplanations.length) {
                updateExplanation(stepExplanations[demoStep]);
                updateStepIndicator(demoStep + 1);
                demoStep++;
            }
        }

        function resetDemo() {
            isRunning = false;
            demoStep = 0;
            data.safeSequence = [];
            data.available = [2,1,0];
            data.currentStep = 0;
            data.currentProcess = -1;

            document.querySelectorAll('.process-row').forEach(row => {
                row.classList.remove('highlight');
            });

            updateStepIndicator(1);
            updateExplanation("点击\"开始演示\"来学习银行家算法的执行过程");
            drawVisualization();
        }

        function showAnswer() {
            updateExplanation(`
                <h4>💡 完整解答过程</h4>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                    <h5>第一步：计算需求矩阵</h5>
                    <p>Need = Max - Allocation</p>
                    <ul>
                        <li>P1: (6-1, 5-2, 2-1) = (5,3,1)</li>
                        <li>P2: (2-2, 2-1, 1-1) = (0,1,0)</li>
                        <li>P3: (8-2, 1-1, 1-0) = (6,0,1)</li>
                        <li>P4: (1-1, 2-2, 1-0) = (0,0,1)</li>
                        <li>P5: (3-1, 4-1, 4-3) = (2,3,1)</li>
                    </ul>

                    <h5>第二步：计算初始可用资源</h5>
                    <p>Available = Total - 所有已分配 = (9,8,5) - (7,7,5) = (2,1,0)</p>

                    <h5>第三步：寻找安全序列</h5>
                    <ol>
                        <li><strong>P2:</strong> 需要(0,1,0) ≤ 可用(2,1,0) ✓ → 完成后释放(2,1,1) → 可用(4,2,1)</li>
                        <li><strong>P4:</strong> 需要(0,0,1) ≤ 可用(4,2,1) ✓ → 完成后释放(1,2,0) → 可用(5,4,1)</li>
                        <li><strong>P5:</strong> 需要(2,3,1) ≤ 可用(5,4,1) ✓ → 完成后释放(1,1,3) → 可用(6,5,4)</li>
                        <li><strong>P1:</strong> 需要(5,3,1) ≤ 可用(6,5,4) ✓ → 完成后释放(1,2,1) → 可用(7,7,5)</li>
                        <li><strong>P3:</strong> 需要(6,0,1) ≤ 可用(7,7,5) ✓ → 完成</li>
                    </ol>

                    <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-top: 15px;">
                        <strong>🎯 答案：C - P2→P4→P5→P1→P3</strong>
                    </div>
                </div>
            `);

            // 显示完整的安全序列
            data.safeSequence = [1, 3, 4, 0, 2]; // P2, P4, P5, P1, P3
            drawVisualization();
        }

        // 添加表格交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Canvas
            initCanvas();

            // 为需求矩阵单元格添加动画效果
            const needCells = document.querySelectorAll('.need-cell');
            needCells.forEach((cell, index) => {
                setTimeout(() => {
                    cell.style.animation = 'fadeIn 0.5s ease-in';
                    cell.style.background = '#e3f2fd';
                }, index * 100);
            });

            // 添加进程行的悬停效果
            document.querySelectorAll('.process-row').forEach(row => {
                row.addEventListener('mouseenter', function() {
                    const process = this.dataset.process;
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.2s ease';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .need-cell {
                font-weight: bold;
                color: #1976d2;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
