<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函数学习 - 零基础交互教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .topics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .topic-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .topic-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .topic-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .topic-card:hover::before {
            left: 100%;
        }

        .topic-number {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            font-weight: bold;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
        }

        .topic-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .topic-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .demo-canvas {
            width: 100%;
            height: 200px;
            border: 2px solid #eee;
            border-radius: 10px;
            background: #f9f9f9;
            margin-bottom: 15px;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .interactive-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 40px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .topic-card {
            animation: slideInUp 0.6s ease-out;
        }

        .topic-card:nth-child(even) {
            animation-delay: 0.2s;
        }

        .topic-card:nth-child(3n) {
            animation-delay: 0.4s;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1>📚 函数学习之旅</h1>
            <p>从零开始，通过动画和交互操作，轻松掌握函数的基本概念和应用</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="topics-grid" id="topicsGrid">
            <!-- 动态生成内容 -->
        </div>
    </div>

    <script>
        // 动画状态管理
        const animations = {};

        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 15; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.width = (Math.random() * 60 + 20) + 'px';
                element.style.height = element.style.width;
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(element);
            }
        }

        // 创建主题卡片
        function createTopicCards() {
            const grid = document.getElementById('topicsGrid');

            allTopics.forEach((topic, index) => {
                const card = document.createElement('div');
                card.className = 'topic-card';
                card.style.animationDelay = (index * 0.1) + 's';

                card.innerHTML = `
                    <div class="topic-number">${topic.number}</div>
                    <h3 class="topic-title">${topic.title}</h3>
                    <p class="topic-description">${topic.description}</p>
                    <canvas class="demo-canvas" id="canvas${topic.number}"></canvas>
                    <button class="interactive-btn" onclick="startDemo(${topic.number})">开始演示</button>
                    <button class="interactive-btn" onclick="resetDemo(${topic.number})">重置</button>
                `;

                grid.appendChild(card);

                // 初始化画布演示
                setTimeout(() => {
                    initDemo(topic.number, topic.demoType);
                }, index * 200);
            });
        }

        // 初始化演示
        function initDemo(topicNumber, demoType) {
            const canvas = document.getElementById(`canvas${topicNumber}`);
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // 设置画布尺寸
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            // 根据演示类型初始化
            switch(demoType) {
                case 'functionMachine':
                    drawFunctionMachine(ctx, canvas.width, canvas.height);
                    break;
                case 'interval':
                    drawInterval(ctx, canvas.width, canvas.height);
                    break;
                case 'domain':
                    drawDomain(ctx, canvas.width, canvas.height);
                    break;
                case 'abstractDomain':
                    drawAbstractDomain(ctx, canvas.width, canvas.height);
                    break;
                case 'sameFunction':
                    drawSameFunction(ctx, canvas.width, canvas.height);
                    break;
                case 'functionValue':
                    drawFunctionValue(ctx, canvas.width, canvas.height);
                    break;
                case 'substitution':
                    drawSubstitution(ctx, canvas.width, canvas.height);
                    break;
                case 'quadraticRange':
                    drawQuadraticRange(ctx, canvas.width, canvas.height);
                    break;
                case 'substitutionRange':
                    drawSubstitutionRange(ctx, canvas.width, canvas.height);
                    break;
                case 'rationalRange':
                    drawRationalRange(ctx, canvas.width, canvas.height);
                    break;
                case 'mapping':
                    drawMapping(ctx, canvas.width, canvas.height);
                    break;
                case 'mappingCount':
                    drawMappingCount(ctx, canvas.width, canvas.height);
                    break;
                case 'rationalQuadratic':
                    drawRationalQuadratic(ctx, canvas.width, canvas.height);
                    break;
            }
        }

        // 函数机器演示
        function drawFunctionMachine(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            // 绘制函数机器
            const centerX = width / 2;
            const centerY = height / 2;

            // 机器主体
            ctx.fillStyle = '#667eea';
            ctx.fillRect(centerX - 60, centerY - 40, 120, 80);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = 2x + 1', centerX, centerY);

            // 输入箭头
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX - 120, centerY);
            ctx.lineTo(centerX - 70, centerY);
            ctx.stroke();

            // 输出箭头
            ctx.beginPath();
            ctx.moveTo(centerX + 70, centerY);
            ctx.lineTo(centerX + 120, centerY);
            ctx.stroke();

            // 输入值
            ctx.fillStyle = '#333';
            ctx.fillText('x = 3', centerX - 140, centerY - 10);

            // 输出值
            ctx.fillText('f(3) = 7', centerX + 140, centerY - 10);
        }

        // 区间演示
        function drawInterval(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            const centerY = height / 2;
            const startX = 50;
            const endX = width - 50;

            // 绘制数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, centerY);
            ctx.lineTo(endX, centerY);
            ctx.stroke();

            // 绘制刻度
            for (let i = 0; i <= 4; i++) {
                const x = startX + (endX - startX) * i / 4;
                ctx.beginPath();
                ctx.moveTo(x, centerY - 10);
                ctx.lineTo(x, centerY + 10);
                ctx.stroke();

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(i.toString(), x, centerY + 25);
            }

            // 绘制区间 [1, 3]
            const interval1 = startX + (endX - startX) * 1 / 4;
            const interval2 = startX + (endX - startX) * 3 / 4;

            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(interval1, centerY);
            ctx.lineTo(interval2, centerY);
            ctx.stroke();

            // 端点
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(interval1, centerY, 5, 0, 2 * Math.PI);
            ctx.fill();
            ctx.beginPath();
            ctx.arc(interval2, centerY, 5, 0, 2 * Math.PI);
            ctx.fill();

            // 标签
            ctx.fillStyle = '#333';
            ctx.fillText('[1, 3]', width / 2, centerY - 30);
        }

        // 定义域演示
        function drawDomain(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            // 绘制函数 f(x) = 1/(x-2)
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = 1/(x-2)', width / 2, 30);

            const centerY = height / 2 + 20;
            const startX = 50;
            const endX = width - 50;

            // 绘制数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, centerY);
            ctx.lineTo(endX, centerY);
            ctx.stroke();

            // 标记x=2处不能取值
            const x2 = startX + (endX - startX) * 0.5;
            ctx.strokeStyle = '#ff4757';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x2 - 10, centerY - 10);
            ctx.lineTo(x2 + 10, centerY + 10);
            ctx.moveTo(x2 - 10, centerY + 10);
            ctx.lineTo(x2 + 10, centerY - 10);
            ctx.stroke();

            ctx.fillStyle = '#ff4757';
            ctx.fillText('x ≠ 2', x2, centerY + 30);

            // 定义域表示
            ctx.fillStyle = '#667eea';
            ctx.fillText('定义域: (-∞, 2) ∪ (2, +∞)', width / 2, height - 20);
        }

        // 抽象函数定义域演示
        function drawAbstractDomain(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('已知 f(x) 的定义域为 [0, 2]', width / 2, 25);
            ctx.fillText('求 f(2x-1) 的定义域', width / 2, 45);

            // 绘制推理过程
            ctx.textAlign = 'left';
            ctx.fillText('解: 由 0 ≤ 2x-1 ≤ 2', 20, 80);
            ctx.fillText('得: 1 ≤ 2x ≤ 3', 20, 100);
            ctx.fillText('所以: 1/2 ≤ x ≤ 3/2', 20, 120);

            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('答案: [1/2, 3/2]', width / 2, 160);
        }

        // 判断同一函数演示
        function drawSameFunction(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('判断下列是否为同一函数:', width / 2, 25);

            ctx.textAlign = 'left';
            ctx.fillText('f(x) = x', 20, 60);
            ctx.fillText('g(x) = x²/x', 20, 80);

            // 分析过程
            ctx.fillStyle = '#ff4757';
            ctx.fillText('f(x) 定义域: R', 20, 110);
            ctx.fillText('g(x) 定义域: x ≠ 0', 20, 130);

            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('定义域不同 → 不是同一函数', width / 2, 170);
        }

        // 函数值演示
        function drawFunctionValue(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = x² - 3x + 2', width / 2, 30);
            ctx.fillText('求 f(2) = ?', width / 2, 55);

            // 计算过程动画
            ctx.textAlign = 'left';
            ctx.font = '14px Arial';
            ctx.fillText('f(2) = 2² - 3×2 + 2', 20, 90);
            ctx.fillText('    = 4 - 6 + 2', 20, 110);
            ctx.fillText('    = 0', 20, 130);

            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.font = '18px Arial';
            ctx.fillText('答案: f(2) = 0', width / 2, 170);
        }

        // 换元法求解析式演示
        function drawSubstitution(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('已知 f(x+1) = x² + 2x, 求 f(x)', width / 2, 25);

            ctx.textAlign = 'left';
            ctx.fillText('解: 设 t = x + 1, 则 x = t - 1', 20, 60);
            ctx.fillText('f(t) = (t-1)² + 2(t-1)', 20, 80);
            ctx.fillText('    = t² - 2t + 1 + 2t - 2', 20, 100);
            ctx.fillText('    = t² - 1', 20, 120);

            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('所以 f(x) = x² - 1', width / 2, 160);
        }

        // 二次函数值域演示
        function drawQuadraticRange(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = x² - 4x + 3, x ∈ [0, 3]', width / 2, 25);

            // 绘制简化的抛物线
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.beginPath();

            const centerX = width / 2;
            const centerY = height / 2;

            // 抛物线的几个关键点
            for (let i = 0; i <= 20; i++) {
                const x = i / 20 * 3; // x从0到3
                const y = x * x - 4 * x + 3;
                const canvasX = centerX - 60 + i * 6;
                const canvasY = centerY + 20 - y * 10;

                if (i === 0) {
                    ctx.moveTo(canvasX, canvasY);
                } else {
                    ctx.lineTo(canvasX, canvasY);
                }
            }
            ctx.stroke();

            // 标记最值点
            ctx.fillStyle = '#ff4757';
            ctx.beginPath();
            ctx.arc(centerX, centerY + 30, 4, 0, 2 * Math.PI); // 顶点 (2, -1)
            ctx.fill();

            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('最小值: -1', centerX, centerY + 50);
            ctx.fillText('值域: [-1, 3]', width / 2, height - 20);
        }

        // 换元法求值域演示
        function drawSubstitutionRange(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = √(x-1) + √(3-x)', width / 2, 25);

            ctx.textAlign = 'left';
            ctx.fillText('定义域: 1 ≤ x ≤ 3', 20, 60);
            ctx.fillText('设 t = √(x-1), 则 x = t² + 1', 20, 80);
            ctx.fillText('√(3-x) = √(2-t²)', 20, 100);
            ctx.fillText('f = t + √(2-t²), t ∈ [0, √2]', 20, 120);

            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('通过求导得最大值为 2', width / 2, 160);
            ctx.fillText('值域: [√2, 2]', width / 2, 180);
        }

        // 分式函数值域演示
        function drawRationalRange(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = (2x+1)/(x-1), x ≠ 1', width / 2, 25);

            ctx.textAlign = 'left';
            ctx.fillText('设 y = (2x+1)/(x-1)', 20, 60);
            ctx.fillText('y(x-1) = 2x + 1', 20, 80);
            ctx.fillText('yx - y = 2x + 1', 20, 100);
            ctx.fillText('x(y-2) = y + 1', 20, 120);
            ctx.fillText('x = (y+1)/(y-2)', 20, 140);

            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('要使x有意义，需 y ≠ 2', width / 2, 170);
            ctx.fillText('值域: (-∞, 2) ∪ (2, +∞)', width / 2, 190);
        }

        // 映射概念演示
        function drawMapping(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('映射的概念', width / 2, 25);

            // 绘制两个集合
            const leftX = width * 0.2;
            const rightX = width * 0.8;
            const centerY = height / 2;

            // 集合A
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(leftX, centerY, 60, 0, 2 * Math.PI);
            ctx.stroke();

            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('集合A', leftX, centerY - 80);

            // 集合B
            ctx.strokeStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(rightX, centerY, 60, 0, 2 * Math.PI);
            ctx.stroke();

            ctx.fillText('集合B', rightX, centerY - 80);

            // 元素
            const elementsA = [{x: leftX - 20, y: centerY - 20, label: '1'},
                              {x: leftX + 20, y: centerY, label: '2'},
                              {x: leftX - 10, y: centerY + 25, label: '3'}];

            const elementsB = [{x: rightX - 15, y: centerY - 25, label: 'a'},
                              {x: rightX + 15, y: centerY + 15, label: 'b'}];

            // 绘制元素
            elementsA.forEach(elem => {
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(elem.x, elem.y, 8, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(elem.label, elem.x, elem.y + 4);
            });

            elementsB.forEach(elem => {
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(elem.x, elem.y, 8, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(elem.label, elem.x, elem.y + 4);
            });

            // 绘制映射箭头
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            elementsA.forEach((elemA, i) => {
                const elemB = elementsB[i % elementsB.length];
                ctx.beginPath();
                ctx.moveTo(elemA.x + 8, elemA.y);
                ctx.lineTo(elemB.x - 8, elemB.y);
                ctx.stroke();

                // 箭头
                const angle = Math.atan2(elemB.y - elemA.y, elemB.x - elemA.x);
                ctx.beginPath();
                ctx.moveTo(elemB.x - 8, elemB.y);
                ctx.lineTo(elemB.x - 15, elemB.y - 5);
                ctx.lineTo(elemB.x - 15, elemB.y + 5);
                ctx.closePath();
                ctx.fill();
            });
        }

        // 映射个数演示
        function drawMappingCount(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('从集合A到集合B的映射个数', width / 2, 25);
            ctx.fillText('A = {1, 2}, B = {a, b, c}', width / 2, 45);

            ctx.textAlign = 'left';
            ctx.fillText('每个A中的元素都可以映射到B中的任意元素', 20, 80);
            ctx.fillText('元素1有3种选择: a, b, c', 20, 100);
            ctx.fillText('元素2有3种选择: a, b, c', 20, 120);

            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.font = '18px Arial';
            ctx.fillText('总映射个数: 3² = 9', width / 2, 160);

            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('一般地，|A|=m, |B|=n，映射个数为 nᵐ', width / 2, 185);
        }

        // 二次比二次型函数值域演示
        function drawRationalQuadratic(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = (x²+2x+3)/(x²+2x+2)', width / 2, 25);

            ctx.textAlign = 'left';
            ctx.fillText('设 t = x² + 2x + 2 = (x+1)² + 1', 20, 60);
            ctx.fillText('则 t ≥ 1', 20, 80);
            ctx.fillText('f(x) = (t+1)/t = 1 + 1/t', 20, 100);
            ctx.fillText('当 t ≥ 1 时，1/t ∈ (0, 1]', 20, 120);

            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('所以 f(x) ∈ (1, 2]', width / 2, 160);
            ctx.fillText('值域: (1, 2]', width / 2, 180);
        }

        // 开始演示动画
        function startDemo(topicNumber) {
            const canvas = document.getElementById(`canvas${topicNumber}`);
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            // 停止之前的动画
            if (animations[topicNumber]) {
                clearInterval(animations[topicNumber]);
            }

            let step = 0;
            animations[topicNumber] = setInterval(() => {
                // 根据主题类型执行不同的动画
                switch(topicNumber) {
                    case 1:
                        animateFunctionMachine(ctx, canvas.width, canvas.height, step);
                        break;
                    case 2:
                        animateInterval(ctx, canvas.width, canvas.height, step);
                        break;
                    case 3:
                        animateDomain(ctx, canvas.width, canvas.height, step);
                        break;
                    case 4:
                        animateAbstractDomain(ctx, canvas.width, canvas.height, step);
                        break;
                    case 5:
                        animateSameFunction(ctx, canvas.width, canvas.height, step);
                        break;
                    case 6:
                        animateFunctionValue(ctx, canvas.width, canvas.height, step);
                        break;
                    case 7:
                        animateSubstitution(ctx, canvas.width, canvas.height, step);
                        break;
                    case 8:
                        animateQuadraticRange(ctx, canvas.width, canvas.height, step);
                        break;
                    case 9:
                        animateSubstitutionRange(ctx, canvas.width, canvas.height, step);
                        break;
                    case 10:
                        animateRationalRange(ctx, canvas.width, canvas.height, step);
                        break;
                    case 11:
                        animateMapping(ctx, canvas.width, canvas.height, step);
                        break;
                    case 12:
                        animateMappingCount(ctx, canvas.width, canvas.height, step);
                        break;
                    case 13:
                        animateRationalQuadratic(ctx, canvas.width, canvas.height, step);
                        break;
                    default:
                        // 重新绘制静态内容
                        const topic = topics.find(t => t.number === topicNumber);
                        if (topic) {
                            initDemo(topicNumber, topic.demoType);
                        }
                        clearInterval(animations[topicNumber]);
                        return;
                }

                step++;
                if (step > 100) {
                    clearInterval(animations[topicNumber]);
                }
            }, 100);
        }

        // 函数机器动画
        function animateFunctionMachine(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            const centerX = width / 2;
            const centerY = height / 2;

            // 机器主体
            ctx.fillStyle = '#667eea';
            ctx.fillRect(centerX - 60, centerY - 40, 120, 80);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = 2x + 1', centerX, centerY);

            // 动画输入
            const inputX = Math.max(centerX - 120, centerX - 120 + step * 2);
            if (inputX < centerX - 70) {
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(inputX, centerY, 8, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = '#333';
                ctx.fillText('3', inputX, centerY - 15);
            }

            // 动画输出
            if (step > 25) {
                const outputX = Math.min(centerX + 70 + (step - 25) * 2, centerX + 120);
                ctx.fillStyle = '#4ecdc4';
                ctx.beginPath();
                ctx.arc(outputX, centerY, 8, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = '#333';
                ctx.fillText('7', outputX, centerY - 15);
            }

            // 箭头
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX - 120, centerY);
            ctx.lineTo(centerX - 70, centerY);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(centerX + 70, centerY);
            ctx.lineTo(centerX + 120, centerY);
            ctx.stroke();
        }

        // 区间动画
        function animateInterval(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            const centerY = height / 2;
            const startX = 50;
            const endX = width - 50;

            // 绘制数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, centerY);
            ctx.lineTo(endX, centerY);
            ctx.stroke();

            // 绘制刻度
            for (let i = 0; i <= 4; i++) {
                const x = startX + (endX - startX) * i / 4;
                ctx.beginPath();
                ctx.moveTo(x, centerY - 10);
                ctx.lineTo(x, centerY + 10);
                ctx.stroke();

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(i.toString(), x, centerY + 25);
            }

            // 动画绘制区间
            const interval1 = startX + (endX - startX) * 1 / 4;
            const interval2 = startX + (endX - startX) * 3 / 4;
            const currentLength = Math.min(step * 3, interval2 - interval1);

            if (currentLength > 0) {
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(interval1, centerY);
                ctx.lineTo(interval1 + currentLength, centerY);
                ctx.stroke();

                // 端点
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(interval1, centerY, 5, 0, 2 * Math.PI);
                ctx.fill();

                if (currentLength >= interval2 - interval1) {
                    ctx.beginPath();
                    ctx.arc(interval2, centerY, 5, 0, 2 * Math.PI);
                    ctx.fill();

                    // 标签
                    ctx.fillStyle = '#333';
                    ctx.fillText('[1, 3]', width / 2, centerY - 30);
                }
            }
        }

        // 定义域动画
        function animateDomain(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            // 绘制函数 f(x) = 1/(x-2)
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = 1/(x-2)', width / 2, 30);

            const centerY = height / 2 + 20;
            const startX = 50;
            const endX = width - 50;

            // 绘制数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, centerY);
            ctx.lineTo(endX, centerY);
            ctx.stroke();

            // 动画标记x=2处不能取值
            const x2 = startX + (endX - startX) * 0.5;
            if (step > 20) {
                ctx.strokeStyle = '#ff4757';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(x2 - 10, centerY - 10);
                ctx.lineTo(x2 + 10, centerY + 10);
                ctx.moveTo(x2 - 10, centerY + 10);
                ctx.lineTo(x2 + 10, centerY - 10);
                ctx.stroke();

                ctx.fillStyle = '#ff4757';
                ctx.fillText('x ≠ 2', x2, centerY + 30);
            }

            if (step > 50) {
                ctx.fillStyle = '#667eea';
                ctx.fillText('定义域: (-∞, 2) ∪ (2, +∞)', width / 2, height - 20);
            }
        }

        // 抽象函数定义域动画
        function animateAbstractDomain(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('已知 f(x) 的定义域为 [0, 2]', width / 2, 25);
            ctx.fillText('求 f(2x-1) 的定义域', width / 2, 45);

            ctx.textAlign = 'left';
            if (step > 20) ctx.fillText('解: 由 0 ≤ 2x-1 ≤ 2', 20, 80);
            if (step > 40) ctx.fillText('得: 1 ≤ 2x ≤ 3', 20, 100);
            if (step > 60) ctx.fillText('所以: 1/2 ≤ x ≤ 3/2', 20, 120);

            if (step > 80) {
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('答案: [1/2, 3/2]', width / 2, 160);
            }
        }

        // 判断同一函数动画
        function animateSameFunction(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('判断下列是否为同一函数:', width / 2, 25);

            ctx.textAlign = 'left';
            if (step > 10) ctx.fillText('f(x) = x', 20, 60);
            if (step > 20) ctx.fillText('g(x) = x²/x', 20, 80);

            if (step > 40) {
                ctx.fillStyle = '#ff4757';
                ctx.fillText('f(x) 定义域: R', 20, 110);
            }
            if (step > 60) {
                ctx.fillText('g(x) 定义域: x ≠ 0', 20, 130);
            }

            if (step > 80) {
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('定义域不同 → 不是同一函数', width / 2, 170);
            }
        }

        // 函数值动画
        function animateFunctionValue(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = x² - 3x + 2', width / 2, 30);
            ctx.fillText('求 f(2) = ?', width / 2, 55);

            ctx.textAlign = 'left';
            ctx.font = '14px Arial';
            if (step > 20) ctx.fillText('f(2) = 2² - 3×2 + 2', 20, 90);
            if (step > 40) ctx.fillText('    = 4 - 6 + 2', 20, 110);
            if (step > 60) ctx.fillText('    = 0', 20, 130);

            if (step > 80) {
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.font = '18px Arial';
                ctx.fillText('答案: f(2) = 0', width / 2, 170);
            }
        }

        // 换元法动画
        function animateSubstitution(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('已知 f(x+1) = x² + 2x, 求 f(x)', width / 2, 25);

            ctx.textAlign = 'left';
            if (step > 20) ctx.fillText('解: 设 t = x + 1, 则 x = t - 1', 20, 60);
            if (step > 40) ctx.fillText('f(t) = (t-1)² + 2(t-1)', 20, 80);
            if (step > 60) ctx.fillText('    = t² - 2t + 1 + 2t - 2', 20, 100);
            if (step > 80) ctx.fillText('    = t² - 1', 20, 120);

            if (step > 90) {
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('所以 f(x) = x² - 1', width / 2, 160);
            }
        }

        // 二次函数值域动画
        function animateQuadraticRange(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = x² - 4x + 3, x ∈ [0, 3]', width / 2, 25);

            // 绘制抛物线动画
            if (step > 20) {
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.beginPath();

                const centerX = width / 2;
                const centerY = height / 2;
                const maxPoints = Math.min(step - 20, 20);

                for (let i = 0; i <= maxPoints; i++) {
                    const x = i / 20 * 3;
                    const y = x * x - 4 * x + 3;
                    const canvasX = centerX - 60 + i * 6;
                    const canvasY = centerY + 20 - y * 10;

                    if (i === 0) {
                        ctx.moveTo(canvasX, canvasY);
                    } else {
                        ctx.lineTo(canvasX, canvasY);
                    }
                }
                ctx.stroke();
            }

            if (step > 60) {
                ctx.fillStyle = '#ff4757';
                ctx.beginPath();
                ctx.arc(width / 2, height / 2 + 30, 4, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('最小值: -1', width / 2, height / 2 + 50);
            }

            if (step > 80) {
                ctx.fillText('值域: [-1, 3]', width / 2, height - 20);
            }
        }

        // 换元法求值域动画
        function animateSubstitutionRange(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = √(x-1) + √(3-x)', width / 2, 25);

            ctx.textAlign = 'left';
            if (step > 20) ctx.fillText('定义域: 1 ≤ x ≤ 3', 20, 60);
            if (step > 40) ctx.fillText('设 t = √(x-1), 则 x = t² + 1', 20, 80);
            if (step > 60) ctx.fillText('√(3-x) = √(2-t²)', 20, 100);
            if (step > 80) ctx.fillText('f = t + √(2-t²), t ∈ [0, √2]', 20, 120);

            if (step > 90) {
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('通过求导得最大值为 2', width / 2, 160);
                ctx.fillText('值域: [√2, 2]', width / 2, 180);
            }
        }

        // 分式函数值域动画
        function animateRationalRange(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = (2x+1)/(x-1), x ≠ 1', width / 2, 25);

            ctx.textAlign = 'left';
            if (step > 20) ctx.fillText('设 y = (2x+1)/(x-1)', 20, 60);
            if (step > 30) ctx.fillText('y(x-1) = 2x + 1', 20, 80);
            if (step > 40) ctx.fillText('yx - y = 2x + 1', 20, 100);
            if (step > 50) ctx.fillText('x(y-2) = y + 1', 20, 120);
            if (step > 60) ctx.fillText('x = (y+1)/(y-2)', 20, 140);

            if (step > 80) {
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('要使x有意义，需 y ≠ 2', width / 2, 170);
                ctx.fillText('值域: (-∞, 2) ∪ (2, +∞)', width / 2, 190);
            }
        }

        // 映射动画
        function animateMapping(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('映射的概念', width / 2, 25);

            const leftX = width * 0.2;
            const rightX = width * 0.8;
            const centerY = height / 2;

            // 绘制集合
            if (step > 10) {
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(leftX, centerY, 60, 0, 2 * Math.PI);
                ctx.stroke();

                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('集合A', leftX, centerY - 80);
            }

            if (step > 20) {
                ctx.strokeStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(rightX, centerY, 60, 0, 2 * Math.PI);
                ctx.stroke();

                ctx.fillText('集合B', rightX, centerY - 80);
            }

            // 动画绘制元素和箭头
            const elementsA = [{x: leftX - 20, y: centerY - 20, label: '1'},
                              {x: leftX + 20, y: centerY, label: '2'},
                              {x: leftX - 10, y: centerY + 25, label: '3'}];

            const elementsB = [{x: rightX - 15, y: centerY - 25, label: 'a'},
                              {x: rightX + 15, y: centerY + 15, label: 'b'}];

            if (step > 30) {
                elementsA.forEach((elem, i) => {
                    if (step > 30 + i * 10) {
                        ctx.fillStyle = '#667eea';
                        ctx.beginPath();
                        ctx.arc(elem.x, elem.y, 8, 0, 2 * Math.PI);
                        ctx.fill();
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.fillText(elem.label, elem.x, elem.y + 4);
                    }
                });

                elementsB.forEach((elem, i) => {
                    if (step > 40 + i * 10) {
                        ctx.fillStyle = '#ff6b6b';
                        ctx.beginPath();
                        ctx.arc(elem.x, elem.y, 8, 0, 2 * Math.PI);
                        ctx.fill();
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.fillText(elem.label, elem.x, elem.y + 4);
                    }
                });
            }

            // 绘制映射箭头
            if (step > 70) {
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                elementsA.forEach((elemA, i) => {
                    const elemB = elementsB[i % elementsB.length];
                    ctx.beginPath();
                    ctx.moveTo(elemA.x + 8, elemA.y);
                    ctx.lineTo(elemB.x - 8, elemB.y);
                    ctx.stroke();
                });
            }
        }

        // 映射个数动画
        function animateMappingCount(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('从集合A到集合B的映射个数', width / 2, 25);
            if (step > 10) ctx.fillText('A = {1, 2}, B = {a, b, c}', width / 2, 45);

            ctx.textAlign = 'left';
            if (step > 30) ctx.fillText('每个A中的元素都可以映射到B中的任意元素', 20, 80);
            if (step > 50) ctx.fillText('元素1有3种选择: a, b, c', 20, 100);
            if (step > 70) ctx.fillText('元素2有3种选择: a, b, c', 20, 120);

            if (step > 90) {
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.font = '18px Arial';
                ctx.fillText('总映射个数: 3² = 9', width / 2, 160);

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText('一般地，|A|=m, |B|=n，映射个数为 nᵐ', width / 2, 185);
            }
        }

        // 二次比二次型函数值域动画
        function animateRationalQuadratic(ctx, width, height, step) {
            ctx.clearRect(0, 0, width, height);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = (x²+2x+3)/(x²+2x+2)', width / 2, 25);

            ctx.textAlign = 'left';
            if (step > 20) ctx.fillText('设 t = x² + 2x + 2 = (x+1)² + 1', 20, 60);
            if (step > 40) ctx.fillText('则 t ≥ 1', 20, 80);
            if (step > 60) ctx.fillText('f(x) = (t+1)/t = 1 + 1/t', 20, 100);
            if (step > 80) ctx.fillText('当 t ≥ 1 时，1/t ∈ (0, 1]', 20, 120);

            if (step > 90) {
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('所以 f(x) ∈ (1, 2]', width / 2, 160);
                ctx.fillText('值域: (1, 2]', width / 2, 180);
            }
        }

        // 重置演示
        function resetDemo(topicNumber) {
            if (animations[topicNumber]) {
                clearInterval(animations[topicNumber]);
            }
            const topic = allTopics.find(t => t.number === topicNumber);
            if (topic) {
                initDemo(topicNumber, topic.demoType);
            }
        }

        // 更新进度条
        function updateProgress() {
            const progressFill = document.getElementById('progressFill');
            let completedTopics = 0;

            // 这里可以根据用户交互来更新进度
            // 暂时设置为随时间增长
            setTimeout(() => {
                progressFill.style.width = '20%';
            }, 1000);
        }

        // 完整的主题数据（重新定义以确保包含所有13个主题）
        const allTopics = [
            {
                number: 1,
                title: "函数是什么",
                description: "理解函数的基本概念，就像一台神奇的机器，输入一个数，输出另一个数",
                demoType: "functionMachine"
            },
            {
                number: 2,
                title: "区间",
                description: "学习数轴上的区间表示，开区间、闭区间的区别和表示方法",
                demoType: "interval"
            },
            {
                number: 3,
                title: "具体函数的定义域",
                description: "找出函数有意义的x值范围，避开让分母为0或根号下为负的情况",
                demoType: "domain"
            },
            {
                number: 4,
                title: "抽象函数的定义域",
                description: "通过已知函数的定义域，求出复合函数的定义域",
                demoType: "abstractDomain"
            },
            {
                number: 5,
                title: "判断是否为同一函数",
                description: "比较两个函数的定义域和对应关系，判断它们是否相同",
                demoType: "sameFunction"
            },
            {
                number: 6,
                title: "求函数值",
                description: "给定x值，计算对应的函数值f(x)",
                demoType: "functionValue"
            },
            {
                number: 7,
                title: "用换元法求函数解析式",
                description: "通过换元的方法，从已知条件推导出函数的解析式",
                demoType: "substitution"
            },
            {
                number: 8,
                title: "二次函数的值域",
                description: "利用二次函数的图像特点，求出函数的值域范围",
                demoType: "quadraticRange"
            },
            {
                number: 9,
                title: "换元法转化为二次函数求值域",
                description: "通过换元将复杂函数转化为二次函数，再求值域",
                demoType: "substitutionRange"
            },
            {
                number: 10,
                title: "分式函数的值域",
                description: "掌握分式函数值域的求解方法和技巧",
                demoType: "rationalRange"
            },
            {
                number: 11,
                title: "映射的概念",
                description: "理解映射的定义，区分映射、函数的关系",
                demoType: "mapping"
            },
            {
                number: 12,
                title: "映射的个数",
                description: "计算从一个集合到另一个集合的映射个数",
                demoType: "mappingCount"
            },
            {
                number: 13,
                title: "二次比二次型函数的值域",
                description: "求解形如 (ax+b)/(cx+d) 类型函数的值域",
                demoType: "rationalQuadratic"
            }
        ];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingElements();
            createTopicCards();
            updateProgress();

            // 添加窗口大小改变事件监听
            window.addEventListener('resize', function() {
                allTopics.forEach((topic, index) => {
                    const canvas = document.getElementById(`canvas${topic.number}`);
                    if (canvas) {
                        canvas.width = canvas.offsetWidth;
                        canvas.height = canvas.offsetHeight;
                        initDemo(topic.number, topic.demoType);
                    }
                });
            });
        });
    </script>
</body>
</html>
