<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倒数笔记系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #0056b3;
        }
        textarea {
            width: 100%;
            height: 300px;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            resize: vertical;
        }
        button {
            padding: 10px 20px;
            margin-right: 10px;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
            font-size: 1.1em;
            text-align: center;
        }
        #eyeTrackingSimulation {
            margin-top: 15px;
            padding: 10px;
            background-color: #ffeeba;
            border: 1px solid #ffda6a;
            border-radius: 5px;
            color: #856404;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>我的阅读笔记系统</h1>
        <textarea id="noteContent" placeholder="在此输入或粘贴您的笔记内容..."></textarea>
        <div style="margin-bottom: 10px;">
            <label for="targetTime">目标阅读时间 (秒): </label>
            <input type="number" id="targetTime" value="0" min="0" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;"/>
        </div>
        <button id="startButton">开始阅读</button>
        <button id="stopButton" disabled>停止阅读</button>
        <div id="results">
            <p>阅读速度: <span id="wpmDisplay">0</span> 字/分钟</p>
            <p>已阅读字数: <span id="wordCountDisplay">0</span> 字</p>
            <p>耗时: <span id="timeElapsedDisplay">0</span> 秒</p>
        </div>
        <div id="eyeTrackingSimulation">
            <p>眼球追踪模拟: 当前正在阅读区域...</p>
        </div>
        <div id="readingHistory" style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
            <h2>阅读历史</h2>
            <ul id="historyList">
                <!-- History items will be inserted here -->
            </ul>
            <button id="clearHistoryButton" style="margin-top: 10px;">清除历史</button>
        </div>
    </div>

    <script>
        const noteContent = document.getElementById('noteContent');
        const startButton = document.getElementById('startButton');
        const stopButton = document.getElementById('stopButton');
        const wpmDisplay = document.getElementById('wpmDisplay');
        const wordCountDisplay = document.getElementById('wordCountDisplay');
        const timeElapsedDisplay = document.getElementById('timeElapsedDisplay');
        const historyList = document.getElementById('historyList');
        const clearHistoryButton = document.getElementById('clearHistoryButton');
        const targetTimeInput = document.getElementById('targetTime');

        let startTime;
        let timerInterval;
        let isReading = false;
        let targetReadingTime = 0; // seconds

        function getWordCount(text) {
            // 优化字数统计：计算所有非空白字符，包括中文、英文、数字等
            const characters = text.replace(/\s/g, ''); // 移除所有空白字符
            return characters.length;
        }

        function startReading() {
            if (isReading) return;

            targetReadingTime = parseInt(targetTimeInput.value) || 0; // Get target time from input

            isReading = true;
            startTime = new Date().getTime();
            startButton.disabled = true;
            stopButton.disabled = false;
            targetTimeInput.disabled = true; // Disable input during reading

            timerInterval = setInterval(() => {
                const currentTime = new Date().getTime();
                const elapsedTime = (currentTime - startTime) / 1000; // in seconds

                if (targetReadingTime > 0) {
                    const remainingTime = targetReadingTime - elapsedTime;
                    if (remainingTime <= 0) {
                        timeElapsedDisplay.textContent = '0';
                        stopReading(); // Stop automatically when time is up
                        return;
                    } else {
                        timeElapsedDisplay.textContent = remainingTime.toFixed(0); // Display remaining time
                    }
                } else {
                    timeElapsedDisplay.textContent = elapsedTime.toFixed(0); // Display elapsed time if no target
                }

                const text = noteContent.value;
                const wordCount = getWordCount(text);
                wordCountDisplay.textContent = wordCount;

                if (elapsedTime > 0) {
                    const wpm = (wordCount / elapsedTime) * 60;
                    wpmDisplay.textContent = wpm.toFixed(2);
                }

                // 模拟眼球追踪：根据阅读进度显示当前行
                const lines = text.split(/\r?\n/); // 分割行，兼容不同换行符
                const totalLines = lines.length;
                if (totalLines > 0 && wordCount > 0 && elapsedTime > 0) {
                    const wordsPerSecond = wordCount / elapsedTime; // Use actual elapsed time for per-second calculation
                    // 假设每行平均15个字进行模拟，这个值可以根据实际情况调整
                    const estimatedWordsPerLine = 15;
                    const estimatedLinesRead = (wordsPerSecond * elapsedTime) / estimatedWordsPerLine;
                    const simulatedLineIndex = Math.min(Math.floor(estimatedLinesRead), totalLines - 1);
                    document.getElementById('eyeTrackingSimulation').querySelector('p').textContent = `模拟眼球焦点: 第 ${simulatedLineIndex + 1} 行`;
                } else {
                    document.getElementById('eyeTrackingSimulation').querySelector('p').textContent = `眼球追踪模拟: 无内容或未开始阅读`;
                }

            }, 1000); // Update every second
        }

        function stopReading() {
            if (!isReading) return;

            isReading = false;
            clearInterval(timerInterval);
            startButton.disabled = false;
            stopButton.disabled = true;
            targetTimeInput.disabled = false; // Enable input after stopping

            // Final calculation
            const currentTime = new Date().getTime();
            const elapsedTime = (currentTime - startTime) / 1000;

            // Ensure elapsed time is accurate for final calculation, especially if auto-stopped
            const finalElapsedTime = targetReadingTime > 0 ? Math.min(elapsedTime, targetReadingTime) : elapsedTime;

            timeElapsedDisplay.textContent = finalElapsedTime.toFixed(0);

            const text = noteContent.value;
            const wordCount = getWordCount(text);
            wordCountDisplay.textContent = wordCount;

            if (finalElapsedTime > 0) {
                const wpm = (wordCount / finalElapsedTime) * 60;
                wpmDisplay.textContent = wpm.toFixed(2);

                // 保存阅读数据到 localStorage
                const sessionData = {
                    timestamp: new Date().toISOString(),
                    wordCount: wordCount,
                    timeElapsed: finalElapsedTime.toFixed(0),
                    wpm: wpm.toFixed(2),
                    targetTime: targetReadingTime > 0 ? targetReadingTime : '无目标'
                };

                let readingHistory = JSON.parse(localStorage.getItem('readingHistory')) || [];
                readingHistory.push(sessionData);
                localStorage.setItem('readingHistory', JSON.stringify(readingHistory));

                // 重新渲染历史记录
                renderReadingHistory();
            }
        }

        function renderReadingHistory() {
            historyList.innerHTML = ''; // 清除之前的条目
            let readingHistory = JSON.parse(localStorage.getItem('readingHistory')) || [];

            if (readingHistory.length === 0) {
                historyList.innerHTML = '<li>暂无阅读记录。</li>';
                return;
            }

            // 倒序显示最新的记录
            readingHistory.slice().reverse().forEach(session => {
                const li = document.createElement('li');
                li.textContent = `${new Date(session.timestamp).toLocaleString()} - 字数: ${session.wordCount} | 耗时: ${session.timeElapsed} 秒 | 速度: ${session.wpm} 字/分钟` + 
                                 (session.targetTime !== '无目标' ? ` | 目标时间: ${session.targetTime} 秒` : '');
                historyList.appendChild(li);
            });
        }

        function clearReadingHistory() {
            localStorage.removeItem('readingHistory');
            renderReadingHistory(); // 重新渲染以显示空状态
        }

        startButton.addEventListener('click', startReading);
        stopButton.addEventListener('click', stopReading);
        clearHistoryButton.addEventListener('click', clearReadingHistory);

        // 页面加载时初始化状态并渲染历史记录
        stopButton.disabled = true;
        renderReadingHistory();
    </script>
</body>
</html>
