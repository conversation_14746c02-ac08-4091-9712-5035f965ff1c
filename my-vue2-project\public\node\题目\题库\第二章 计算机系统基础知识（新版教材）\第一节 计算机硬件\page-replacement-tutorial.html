<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面置换算法 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2em;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        .section h2 {
            color: #667eea;
            font-size: 2em;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .memory-demo {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            margin: 40px 0;
            flex-wrap: wrap;
        }

        .page-table {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .table-header {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-bottom: 15px;
            font-weight: bold;
            color: #667eea;
        }

        .table-row {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-bottom: 10px;
            animation: slideInLeft 0.5s ease-out;
        }

        .cell {
            padding: 12px;
            text-align: center;
            border-radius: 8px;
            background: white;
            border: 2px solid #e0e6ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .cell:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .cell.in-memory {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .cell.not-in-memory {
            background: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }

        .cell.accessed {
            background: #fff3e0;
            border-color: #ff9800;
            color: #e65100;
        }

        .cell.modified {
            background: #f3e5f5;
            border-color: #9c27b0;
            color: #6a1b9a;
        }

        .memory-blocks {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .memory-block {
            width: 120px;
            height: 80px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: bold;
            color: white;
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .memory-block:hover {
            transform: scale(1.05);
        }

        .memory-block.empty {
            background: #ddd;
            color: #999;
        }

        .memory-block.victim {
            animation: shake 0.5s ease-in-out infinite;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .explanation {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            animation: fadeIn 0.8s ease-out;
        }

        .step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border-left: 4px solid #4caf50;
            opacity: 0;
            animation: slideInRight 0.5s ease-out forwards;
        }

        .step.active {
            background: #e8f5e8;
            border-left-color: #2e7d32;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .highlight {
            animation: pulse 1s ease-in-out infinite;
        }

        .legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 15px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: white;
        }

        .quiz-section h2 {
            color: white;
        }

        .quiz-option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(10px);
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4caf50;
        }

        .quiz-option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 页面置换算法学习</h1>
            <p>通过动画和交互，轻松掌握操作系统中的页面置换机制</p>
        </div>

        <div class="section">
            <h2>📚 基础概念</h2>
            <div class="explanation">
                <h3>什么是页面置换？</h3>
                <p>当程序需要访问的页面不在内存中时，操作系统需要从磁盘加载该页面。如果内存已满，就需要选择一个页面移出内存，这个过程叫做<strong>页面置换</strong>。</p>
                
                <h3>页表中的重要标志位：</h3>
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #4caf50;"></div>
                        <span>状态位：1=在内存，0=不在内存</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #ff9800;"></div>
                        <span>访问位：1=最近访问过，0=未访问</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #9c27b0;"></div>
                        <span>修改位：1=被修改过，0=未修改</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎮 交互式演示</h2>
            <div class="explanation">
                <p>下面是题目中的页表，点击页面可以查看详细信息：</p>
            </div>

            <div class="memory-demo">
                <div class="page-table">
                    <div class="table-header">
                        <div>页号</div>
                        <div>页帧号</div>
                        <div>状态位</div>
                        <div>访问位</div>
                        <div>修改位</div>
                    </div>
                    <div class="table-row" data-page="0">
                        <div class="cell">0</div>
                        <div class="cell">8</div>
                        <div class="cell in-memory">1</div>
                        <div class="cell accessed">1</div>
                        <div class="cell">0</div>
                    </div>
                    <div class="table-row" data-page="1">
                        <div class="cell">1</div>
                        <div class="cell">—</div>
                        <div class="cell not-in-memory">0</div>
                        <div class="cell">0</div>
                        <div class="cell">0</div>
                    </div>
                    <div class="table-row" data-page="2">
                        <div class="cell">2</div>
                        <div class="cell">3</div>
                        <div class="cell in-memory">1</div>
                        <div class="cell accessed">1</div>
                        <div class="cell modified">1</div>
                    </div>
                    <div class="table-row" data-page="3">
                        <div class="cell">3</div>
                        <div class="cell">—</div>
                        <div class="cell not-in-memory">0</div>
                        <div class="cell">0</div>
                        <div class="cell">0</div>
                    </div>
                    <div class="table-row" data-page="4">
                        <div class="cell">4</div>
                        <div class="cell">13</div>
                        <div class="cell in-memory">1</div>
                        <div class="cell accessed">1</div>
                        <div class="cell modified">1</div>
                    </div>
                </div>

                <div class="memory-blocks">
                    <h3 style="text-align: center; margin-bottom: 15px;">内存块 (3个)</h3>
                    <div class="memory-block" data-block="0">页面 0</div>
                    <div class="memory-block" data-block="1">页面 2</div>
                    <div class="memory-block" data-block="2">页面 4</div>
                </div>
            </div>

            <div class="controls">
                <button class="btn" onclick="startDemo()">🎬 开始演示</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
                <button class="btn" onclick="showAnswer()">💡 查看答案</button>
            </div>

            <div id="steps-container"></div>
        </div>

        <div class="section">
            <h2>🧮 置换算法详解</h2>
            <div class="explanation">
                <h3>改进型时钟算法 (Enhanced Clock Algorithm)</h3>
                <p>这是一种基于访问位和修改位的页面置换算法，优先级如下：</p>

                <div class="step">
                    <strong>第1优先级：</strong> 状态位=0 (不在内存) - 跳过
                </div>
                <div class="step">
                    <strong>第2优先级：</strong> 访问位=0, 修改位=0 (未访问且未修改) - 最优选择
                </div>
                <div class="step">
                    <strong>第3优先级：</strong> 访问位=0, 修改位=1 (未访问但已修改) - 需要写回磁盘
                </div>
                <div class="step">
                    <strong>第4优先级：</strong> 访问位=1, 修改位=0 (已访问但未修改) - 清除访问位后重新考虑
                </div>
                <div class="step">
                    <strong>第5优先级：</strong> 访问位=1, 修改位=1 (已访问且已修改) - 最不优选择
                </div>
            </div>
        </div>

        <div class="section quiz-section">
            <h2>🎯 互动测试</h2>
            <div class="explanation">
                <h3>根据页表信息，当访问页面3时，应该淘汰哪个页面？</h3>
                <div class="quiz-option" onclick="selectAnswer('A', this)">A. 页面 0</div>
                <div class="quiz-option" onclick="selectAnswer('B', this)">B. 页面 1</div>
                <div class="quiz-option" onclick="selectAnswer('C', this)">C. 页面 2</div>
                <div class="quiz-option" onclick="selectAnswer('D', this)">D. 页面 4</div>
                <div id="quiz-result" style="margin-top: 20px; font-size: 1.2em; text-align: center;"></div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let demoSteps = [
            {
                title: "步骤1：识别问题",
                content: "页面3不在内存中，需要加载。但内存已满(3个块都被占用)，必须选择一个页面淘汰。",
                highlight: ["3"]
            },
            {
                title: "步骤2：筛选候选页面",
                content: "只有在内存中的页面才能被淘汰。页面0、2、4在内存中，页面1、3不在内存中。",
                highlight: ["0", "2", "4"]
            },
            {
                title: "步骤3：检查访问位",
                content: "页面0、2、4的访问位都是1，说明它们最近都被访问过。按算法需要进一步检查修改位。",
                highlight: ["0", "2", "4"]
            },
            {
                title: "步骤4：检查修改位",
                content: "页面0的修改位是0(未修改)，页面2和4的修改位是1(已修改)。优先淘汰未修改的页面。",
                highlight: ["0"]
            },
            {
                title: "步骤5：确定答案",
                content: "页面0是最佳淘汰选择：在内存中、已访问但未修改。淘汰它不需要写回磁盘，效率最高。",
                highlight: ["0"],
                victim: "0"
            }
        ];

        function startDemo() {
            currentStep = 0;
            showStep();
        }

        function showStep() {
            if (currentStep >= demoSteps.length) {
                document.getElementById('steps-container').innerHTML =
                    '<div class="explanation"><h3>🎉 演示完成！</h3><p>现在你应该理解为什么答案是页面0了。</p></div>';
                return;
            }

            const step = demoSteps[currentStep];
            const container = document.getElementById('steps-container');

            container.innerHTML = `
                <div class="explanation">
                    <h3>${step.title}</h3>
                    <p>${step.content}</p>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn" onclick="nextStep()" ${currentStep === demoSteps.length - 1 ? 'style="display:none"' : ''}>下一步 →</button>
                    </div>
                </div>
            `;

            // 高亮相关页面
            document.querySelectorAll('.table-row').forEach(row => {
                row.classList.remove('highlight');
                const pageNum = row.dataset.page;
                if (step.highlight && step.highlight.includes(pageNum)) {
                    row.classList.add('highlight');
                }
            });

            // 标记受害者页面
            document.querySelectorAll('.memory-block').forEach(block => {
                block.classList.remove('victim');
                if (step.victim && block.textContent.includes(`页面 ${step.victim}`)) {
                    block.classList.add('victim');
                }
            });
        }

        function nextStep() {
            currentStep++;
            setTimeout(showStep, 300);
        }

        function resetDemo() {
            currentStep = 0;
            document.getElementById('steps-container').innerHTML = '';
            document.querySelectorAll('.table-row').forEach(row => {
                row.classList.remove('highlight');
            });
            document.querySelectorAll('.memory-block').forEach(block => {
                block.classList.remove('victim');
            });
        }

        function showAnswer() {
            document.getElementById('steps-container').innerHTML = `
                <div class="explanation">
                    <h3>💡 正确答案：A (页面0)</h3>
                    <p><strong>解析：</strong></p>
                    <p>1. 首先筛选在内存中的页面：0、2、4</p>
                    <p>2. 这些页面的访问位都是1，需要看修改位</p>
                    <p>3. 页面0的修改位是0，页面2和4的修改位是1</p>
                    <p>4. 优先淘汰未修改的页面，避免写回磁盘的开销</p>
                    <p>5. 因此选择页面0</p>
                </div>
            `;
        }

        function selectAnswer(option, element) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            const resultDiv = document.getElementById('quiz-result');

            if (option === 'A') {
                element.classList.add('correct');
                resultDiv.innerHTML = '🎉 正确！页面0是最佳选择，因为它在内存中、已访问但未修改。';
                resultDiv.style.color = '#4caf50';
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                document.querySelectorAll('.quiz-option').forEach(opt => {
                    if (opt.textContent.startsWith('A.')) {
                        opt.classList.add('correct');
                    }
                });
                resultDiv.innerHTML = '❌ 不正确。正确答案是A。页面0未被修改，淘汰它不需要写回磁盘。';
                resultDiv.style.color = '#f44336';
            }
        }

        // 页面加载完成后的动画
        window.addEventListener('load', function() {
            // 为步骤添加延迟动画
            document.querySelectorAll('.step').forEach((step, index) => {
                step.style.animationDelay = `${index * 0.2}s`;
            });

            // 添加表格行的交互
            document.querySelectorAll('.table-row').forEach(row => {
                row.addEventListener('click', function() {
                    const pageNum = this.dataset.page;
                    showPageInfo(pageNum);
                });
            });
        });

        function showPageInfo(pageNum) {
            const pageData = {
                '0': { frame: '8', status: '在内存', access: '已访问', modify: '未修改' },
                '1': { frame: '—', status: '不在内存', access: '未访问', modify: '未修改' },
                '2': { frame: '3', status: '在内存', access: '已访问', modify: '已修改' },
                '3': { frame: '—', status: '不在内存', access: '未访问', modify: '未修改' },
                '4': { frame: '13', status: '在内存', access: '已访问', modify: '已修改' }
            };

            const data = pageData[pageNum];
            alert(`页面 ${pageNum} 详细信息：\n页帧号: ${data.frame}\n状态: ${data.status}\n访问情况: ${data.access}\n修改情况: ${data.modify}`);
        }

        // 添加更多交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为内存块添加点击效果
            document.querySelectorAll('.memory-block').forEach(block => {
                block.addEventListener('click', function() {
                    this.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 200);
                });
            });

            // 添加页表行的悬停效果
            document.querySelectorAll('.table-row').forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                    this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                    this.style.boxShadow = 'none';
                });
            });
        });
    </script>
</body>
</html>
