<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象三大特性与五大原则解释</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 1200px;
            width: 100%;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #34495e;
            font-size: 1.8em;
            border-bottom: 2px solid #a2d9ce;
            padding-bottom: 10px;
        }
        p {
            margin-bottom: 15px;
            text-align: justify;
        }
        canvas {
            display: block;
            border: 2px solid #a2d9ce;
            border-radius: 8px;
            margin: 20px auto;
            background-color: #e8f6f3;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .controls {
            text-align: center;
            margin-top: 15px;
        }
        .controls button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1em;
            margin: 5px;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #218838;
        }
        .explanation-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 0.95em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>面向对象三大特性与五大原则</h1>

        <div class="section">
            <h2>1. 面向对象的特征有哪些方面？</h2>
            <p>面向对象的特征主要有以下几个方面：</p>
            <h3>抽象 (Abstraction)</h3>
            <p>抽象是将一类对象的共同特征总结出来构造类的过程，包括数据抽象和行为抽象两方面。抽象只关注对象有哪些属性和行为，并不关注这些行为的细节是什么。</p>
            <h3>封装 (Encapsulation)</h3>
            <p>封装把一个对象的属性私有化，同时提供一些可以被外界访问的属性的方法，如果属性不想被外界访问，我们大可不必提供方法给外界访问。但是如果一个类没有提供给外界访问的方法，那么这个类也没有什么意义了。</p>
            <div class="controls">
                <button onclick="startEncapsulationDemo()">开始封装演示</button>
            </div>
            <div class="explanation-box" id="encapsulation-explanation"></div>
        </div>

        <div class="section">
            <h3>继承 (Inheritance)</h3>
            <p>继承是使用已存在的类的定义作为基础建立新类的技术，新类的定义可以增加新的数据或新的功能，也可以用父类的功能，但不能选择性地继承父类。通过使用继承我们能够非常方便地复用以前的代码。</p>
            <p>关于继承如下 3 点请记住：</p>
            <ul>
                <li>子类拥有父类非 private 的属性和方法。</li>
                <li>子类可以拥有自己属性和方法，即子类可以对父类进行扩展。</li>
                <li>子类可以用自己的方式实现父类的方法。</li>
            </ul>
            <div class="controls">
                <button onclick="startInheritanceDemo()">开始继承演示</button>
            </div>
            <div class="explanation-box" id="inheritance-explanation"></div>
        </div>

        <div class="section">
            <h3>多态 (Polymorphism)</h3>
            <p>所谓多态就是指程序中定义的引用变量所指向的具体类型和通过该引用变量发出的方法调用在编程时并不确定，而是在程序运行期间才确定，即一个引用变量到底会指向哪个类的实例对象，该引用变量发出的方法调用到底是哪个类中实现的方法，必须在由程序运行期间才能决定。</p>
            <p>在Java中有两种形式可以实现多态：继承（多个子类对同一方法的重写）和接口（实现接口并覆盖接口中同一方法）。</p>
            <div class="controls">
                <button onclick="startPolymorphismDemo()">开始多态演示</button>
            </div>
            <div class="explanation-box" id="polymorphism-explanation"></div>
        </div>

        <div class="section">
            <h2>2. 什么是多态机制？Java语言是如何实现多态的？</h2>
            <p>所谓多态就是指程序中定义的引用变量所指向的具体类型和通过该引用变量发出的方法调用在编程时并不确定，而是在程序运行期间才确定，即一个引用变量到底会指向哪个类的实例对象，该引用变量发出的方法调用到底是哪个类中实现的方法，必须在由程序运行期间才能决定。因为在程序运行时才确定具体的类，这样，不用修改源程序代码，就可以让引用变量绑定到各种不同的类实现上，从而导致该引用调用的具体方法随之改变，即不修改程序代码就可以改变程序运行时所绑定的具体代码，让程序可以选择多个运行状态，这就是多态性。</p>
            <p>多态分为编译时多态和运行时多态。其中编辑时多态是静态的，主要是指方法的重载，它是根据参数列表的不同来区分不同的函数，通过编辑之后会变成两个不同的函数，在运行时谈不上多态。而运行时多态是动态的，它是通过动态绑定来实现的，也就是我们所说的多态性。</p>
        </div>

        <div class="section">
            <h2>3. 多态的实现</h2>
            <p>Java实现多态有三个必要条件：继承、重写、向上转型。</p>
            <ul>
                <li><strong>继承：</strong>在多态中必须存在有继承关系的子类和父类。</li>
                <li><strong>重写：</strong>子类对父类中某些方法进行重新定义，在调用这些方法时就会调用子类的方法。</li>
                <li><strong>向上转型：</strong>在多态中需要将子类的引用赋给父类对象，只有这样该引用才能够具备技能调用父类的方法和子类的方法。</li>
            </ul>
            <p>只有满足了上述三个条件，我们才能够在同一个继承结构中使用统一的逻辑实现代码处理不同的对象，从而达到执行不同的行为。</p>
            <p>对于Java而言，它多态的实现机制遵循一个原则：当超类对象引用变量引用子类对象时，被引用对象的类型而不是引用变量的类型决定了调用谁的成员方法，但是这个被调用的方法必须是在超类中定义过的，也就是说被子类覆盖的方法。</p>
        </div>

        <div class="section">
            <h2>4. 面向对象五大基本原则是什么？(SOLID)</h2>
            <p>面向对象设计有五大基本原则，通常称为 SOLID 原则：</p>
            <h3>单一职责原则 (SRP - Single Responsibility Principle)</h3>
            <p>类的功能要单一，不能包罗万象，跟杂货铺似的。</p>
            <div class="controls">
                <button onclick="startSRPDemo()">开始SRP演示</button>
            </div>
            <div class="explanation-box" id="srp-explanation"></div>

            <h3>开放封闭原则 (OCP - Open-Closed Principle)</h3>
            <p>一个模块对于拓展是开放的，对于修改是封闭的，想要增加功能热烈欢迎，想要修改，哼，一万个不乐意。</p>
            <div class="controls">
                <button onclick="startOCPDemo()">开始OCP演示</button>
            </div>
            <div class="explanation-box" id="ocp-explanation"></div>

            <h3>里式替换原则 (LSP - Liskov Substitution Principle)</h3>
            <p>子类可以替换父类出现在父类能够出现的任何地方。比如你能代表你爸去你姥姥家干活。哈哈~~</p>
            <div class="controls">
                <button onclick="startLSPDemo()">开始LSP演示</button>
            </div>
            <div class="explanation-box" id="lsp-explanation"></div>

            <h3>依赖倒置原则 (DIP - Dependency Inversion Principle)</h3>
            <p>高层次的模块不应该依赖于低层次的模块，他们都应该依赖于抽象。抽象不应该依赖于具体实现，具体实现应该依赖于抽象。就是你出国要说你是中国人，而不能说你是哪个村子的。比如说中国人是抽象的，下面有具体的xx省，xx市，xx县。你要依赖的抽象是中国人，而不是你是xx村的。</p>
            <div class="controls">
                <button onclick="startDIPDemo()">开始DIP演示</button>
            </div>
            <div class="explanation-box" id="dip-explanation"></div>

            <h3>接口分离原则 (ISP - Interface Segregation Principle)</h3>
            <p>设计时采用多个与特定客户类有关的接口比采用一个通用的接口要好。就比如一个手机拥有打电话，看视频，玩游戏等功能，把这几个功能拆分成不同的接口，比在一个接口里要好的多。</p>
            <div class="controls">
                <button onclick="startISPDemo()">开始ISP演示</button>
            </div>
            <div class="explanation-box" id="isp-explanation"></div>
        </div>

        <canvas id="oopCanvas" width="800" height="450"></canvas>
    </div>

    <script>
        const canvas = document.getElementById('oopCanvas');
        const ctx = canvas.getContext('2d');
        const W = canvas.width;
        const H = canvas.height;

        // 清空画布
        function clearCanvas() {
            ctx.clearRect(0, 0, W, H);
            ctx.fillStyle = '#e8f6f3'; // Canvas background color
            ctx.fillRect(0, 0, W, H);
        }

        // 动画帧管理器
        let animationFrameId;
        function stopAnimation() {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
        }

        // =========================================================
        // 封装演示
        // =========================================================
        let encapsulationStep = 0;
        let userData = 0;
        const explanationBoxEncapsulation = document.getElementById('encapsulation-explanation');

        function drawEncapsulation(step) {
            clearCanvas();
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 绘制类（方框）
            ctx.fillStyle = '#3498db';
            ctx.fillRect(W / 2 - 100, H / 2 - 70, 200, 140);
            ctx.strokeStyle = '#2980b9';
            ctx.lineWidth = 3;
            ctx.strokeRect(W / 2 - 100, H / 2 - 70, 200, 140);

            ctx.fillStyle = 'white';
            ctx.fillText('MyClass', W / 2, H / 2 - 50);

            // 绘制私有属性
            ctx.fillStyle = '#e74c3c'; // Private color
            ctx.fillText(`- privateData: ${userData}`, W / 2, H / 2 - 10);

            // 绘制公共方法
            ctx.fillStyle = '#2ecc71'; // Public color
            ctx.fillText('+ setData(value)', W / 2, H / 2 + 30);
            ctx.fillText('+ getData()', W / 2, H / 2 + 60);

            let explanationText = '';

            if (step === 0) {
                explanationText = '<strong>封装:</strong> 将数据和方法捆绑在一个单元中。在这个例子中，`privateData` 是类的内部数据，外部无法直接访问。';
            } else if (step === 1) {
                ctx.strokeStyle = '#f39c12';
                ctx.lineWidth = 5;
                // 绘制尝试访问私有数据的线
                ctx.beginPath();
                ctx.moveTo(W / 2 + 150, H / 2 - 10);
                ctx.lineTo(W / 2 + 50, H / 2 - 10);
                ctx.stroke();
                ctx.fillStyle = '#f39c12';
                ctx.fillText('外部', W / 2 + 180, H / 2 - 10);
                explanationText = '<strong>尝试直接访问私有数据失败:</strong> 外部代码不能直接修改 `privateData`。封装保护了数据的完整性。';
            } else if (step === 2) {
                ctx.strokeStyle = '#2ecc71';
                ctx.lineWidth = 5;
                // 绘制通过公共方法setData访问的线
                ctx.beginPath();
                ctx.moveTo(W / 2 + 150, H / 2 + 30);
                ctx.lineTo(W / 2 + 50, H / 2 + 30);
                ctx.stroke();
                ctx.fillStyle = '#2ecc71';
                ctx.fillText('外部', W / 2 + 180, H / 2 + 30);
                explanationText = '<strong>通过公共方法访问:</strong> 我们可以通过公共方法 `setData()` 来安全地修改 `privateData`。';
                userData = 100; // Simulate data change
            } else if (step === 3) {
                ctx.strokeStyle = '#2ecc71';
                ctx.lineWidth = 5;
                // 绘制通过公共方法getData访问的线
                ctx.beginPath();
                ctx.moveTo(W / 2 + 150, H / 2 + 60);
                ctx.lineTo(W / 2 + 50, H / 2 + 60);
                ctx.stroke();
                ctx.fillStyle = '#2ecc71';
                ctx.fillText('外部', W / 2 + 180, H / 2 + 60);
                explanationText = `<strong>通过公共方法获取数据:</strong> 我们可以通过公共方法 \`getData()\` 来获取 \`privateData\` 的值。当前数据: ${userData}`;
            }

            explanationBoxEncapsulation.innerHTML = explanationText;
        }

        function startEncapsulationDemo() {
            stopAnimation(); // 停止之前的动画
            encapsulationStep = 0;
            userData = 0;
            playEncapsulationStep();
        }

        function playEncapsulationStep() {
            drawEncapsulation(encapsulationStep);
            encapsulationStep++;
            if (encapsulationStep <= 3) { // 调整为3步，因为有0,1,2,3 共4步
                animationFrameId = setTimeout(playEncapsulationStep, 2000); // 每2秒切换一步
            } else {
                explanationBoxEncapsulation.innerHTML += '<p><strong>总结：</strong>封装确保了数据的安全性和程序的模块化。</p>';
            }
        }

        // =========================================================
        // 继承演示
        // =========================================================
        let inheritanceStep = 0;
        const explanationBoxInheritance = document.getElementById('inheritance-explanation');

        function drawClass(x, y, name, methods, isParent = false, highlightMethod = null) {
            ctx.fillStyle = isParent ? '#3498db' : '#9b59b6'; // Parent blue, Child purple
            ctx.fillRect(x, y, 180, 150);
            ctx.strokeStyle = isParent ? '#2980b9' : '#8e44ad';
            ctx.lineWidth = 3;
            ctx.strokeRect(x, y, 180, 150);

            ctx.fillStyle = 'white';
            ctx.fillText(name, x + 90, y + 25);

            ctx.font = '16px Arial';
            methods.forEach((method, index) => {
                const isHighlight = highlightMethod && method.includes(highlightMethod);
                ctx.fillStyle = isHighlight ? '#f1c40f' : 'white';
                ctx.fillText(method, x + 90, y + 60 + index * 30);
            });
            ctx.font = '20px Arial';
        }

        function drawInheritance(step) {
            clearCanvas();
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let explanationText = '';

            const parentX = W / 2 - 90;
            const parentY = H / 2 - 180;
            const child1X = W / 4 - 90;
            const child1Y = H / 2 + 20;
            const child2X = W * 3 / 4 - 90;
            const child2Y = H / 2 + 20;

            if (step === 0) {
                // 绘制父类
                drawClass(parentX, parentY, 'Animal (父类)', ['+ eat()', '+ speak()'], true);
                explanationText = '<strong>继承:</strong> 定义一个父类 `Animal`，它有两个方法：`eat()` 和 `speak()`。';
            } else if (step === 1) {
                // 绘制父类
                drawClass(parentX, parentY, 'Animal (父类)', ['+ eat()', '+ speak()'], true);
                // 绘制子类1 (Dog)
                drawClass(child1X, child1Y, 'Dog (子类)', ['+ eat()', '+ speak()', '+ bark()']);
                // 绘制继承线
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(parentX + 90, parentY + 150);
                ctx.lineTo(W / 2 - 20, H / 2 + 20); // 连接到Dog类
                ctx.stroke();
                explanationText = '<strong>子类继承父类:</strong> `Dog` 类继承自 `Animal`。它拥有 `Animal` 的 `eat()` 和 `speak()` 方法。';
            } else if (step === 2) {
                // 绘制父类
                drawClass(parentX, parentY, 'Animal (父类)', ['+ eat()', '+ speak()'], true);
                // 绘制子类1 (Dog)
                drawClass(child1X, child1Y, 'Dog (子类)', ['+ eat()', '+ speak()', '+ bark()'], false, 'speak()');
                // 绘制子类2 (Cat)
                drawClass(child2X, child2Y, 'Cat (子类)', ['+ eat()', '+ speak()', '+ meow()'], false, 'speak()');

                // 绘制继承线
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(parentX + 90, parentY + 150);
                ctx.lineTo(W / 2 - 20, H / 2 + 20); // 连接到Dog类
                ctx.moveTo(parentX + 90, parentY + 150);
                ctx.lineTo(W / 2 + 20, H / 2 + 20); // 连接到Cat类
                ctx.stroke();

                explanationText = '<strong>子类重写方法:</strong> `Dog` 和 `Cat` 都重写了 `speak()` 方法，实现了自己的叫声 (`bark()` 和 `meow()`)。子类也可以有自己的独特方法。';
            }

            explanationBoxInheritance.innerHTML = explanationText;
        }

        function startInheritanceDemo() {
            stopAnimation();
            inheritanceStep = 0;
            playInheritanceStep();
        }

        function playInheritanceStep() {
            drawInheritance(inheritanceStep);
            inheritanceStep++;
            if (inheritanceStep <= 2) {
                animationFrameId = setTimeout(playInheritanceStep, 2500);
            } else {
                explanationBoxInheritance.innerHTML += '<p><strong>总结：</strong>继承实现了代码复用和扩展。子类可以在不修改父类的情况下，增加新功能或改变父类方法的行为。</p>';
            }
        }

        // =========================================================
        // 多态演示
        // =========================================================
        let polymorphismStep = 0;
        const explanationBoxPolymorphism = document.getElementById('polymorphism-explanation');

        class Shape {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.color = color;
            }
            draw() {
                // Abstract method
            }
            sayName() {
                // Abstract method
            }
        }

        class Circle extends Shape {
            constructor(x, y, radius, color) {
                super(x, y, color);
                this.radius = radius;
            }
            draw() {
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = this.color;
                ctx.fill();
                ctx.strokeStyle = 'black';
                ctx.lineWidth = 2;
                ctx.stroke();
                ctx.fillStyle = 'white';
                ctx.fillText('圆形', this.x, this.y);
            }
            sayName() {
                return "我是圆形";
            }
        }

        class Square extends Shape {
            constructor(x, y, size, color) {
                super(x, y, color);
                this.size = size;
            }
            draw() {
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x - this.size / 2, this.y - this.size / 2, this.size, this.size);
                ctx.strokeStyle = 'black';
                ctx.lineWidth = 2;
                ctx.strokeRect(this.x - this.size / 2, this.y - this.size / 2, this.size, this.size);
                ctx.fillStyle = 'white';
                ctx.fillText('方形', this.x, this.y);
            }
            sayName() {
                return "我是方形";
            }
        }

        class Triangle extends Shape {
            constructor(x, y, size, color) {
                super(x, y, color);
                this.size = size;
            }
            draw() {
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.moveTo(this.x, this.y - this.size / 2);
                ctx.lineTo(this.x - this.size / 2, this.y + this.size / 2);
                ctx.lineTo(this.x + this.size / 2, this.y + this.size / 2);
                ctx.closePath();
                ctx.fill();
                ctx.strokeStyle = 'black';
                ctx.lineWidth = 2;
                ctx.stroke();
                ctx.fillStyle = 'white';
                ctx.fillText('三角形', this.x, this.y);
            }
            sayName() {
                return "我是三角形";
            }
        }

        const shapes = [
            new Circle(W / 4, H / 2, 40, '#f1c40f'),
            new Square(W / 2, H / 2, 80, '#e74c3c'),
            new Triangle(W * 3 / 4, H / 2 + 10, 80, '#3498db')
        ];

        function drawPolymorphism(step) {
            clearCanvas();
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let explanationText = '';

            if (step === 0) {
                explanationText = '<strong>多态:</strong> 父类引用指向子类对象。这里我们有一个 `Shape` 类型的引用。';
                ctx.fillStyle = '#34495e';
                ctx.fillText('Shape myShape = ?;', W / 2, 50);
            } else if (step === 1) {
                explanationText = '<strong>多态:</strong> 当 `myShape` 引用指向 `Circle` 对象时，调用 `draw()` 方法，会执行 `Circle` 的绘制逻辑。';
                ctx.fillStyle = '#34495e';
                ctx.fillText('Shape myShape = new Circle();', W / 2, 50);
                shapes[0].draw(); // Draw Circle
                ctx.fillStyle = '#0c5460';
                ctx.fillText(`myShape.sayName() 输出: ${shapes[0].sayName()}`, W / 2, H - 50);
            } else if (step === 2) {
                explanationText = '<strong>多态:</strong> 当 `myShape` 引用指向 `Square` 对象时，调用 `draw()` 方法，会执行 `Square` 的绘制逻辑。';
                ctx.fillStyle = '#34495e';
                ctx.fillText('Shape myShape = new Square();', W / 2, 50);
                shapes[1].draw(); // Draw Square
                ctx.fillStyle = '#0c5460';
                ctx.fillText(`myShape.sayName() 输出: ${shapes[1].sayName()}`, W / 2, H - 50);
            } else if (step === 3) {
                explanationText = '<strong>多态:</strong> 当 `myShape` 引用指向 `Triangle` 对象时，调用 `draw()` 方法，会执行 `Triangle` 的绘制逻辑。';
                ctx.fillStyle = '#34495e';
                ctx.fillText('Shape myShape = new Triangle();', W / 2, 50);
                shapes[2].draw(); // Draw Triangle
                ctx.fillStyle = '#0c5460';
                ctx.fillText(`myShape.sayName() 输出: ${shapes[2].sayName()}`, W / 2, H - 50);
            } else if (step === 4) {
                explanationText = '<strong>总结：</strong> 同样的引用变量，在运行时根据实际指向的对象类型，表现出不同的行为。这就是多态的魅力！';
                shapes.forEach(shape => shape.draw());
            }

            explanationBoxPolymorphism.innerHTML = explanationText;
        }

        function startPolymorphismDemo() {
            stopAnimation();
            polymorphismStep = 0;
            playPolymorphismStep();
        }

        function playPolymorphismStep() {
            drawPolymorphism(polymorphismStep);
            polymorphismStep++;
            if (polymorphismStep <= 4) {
                animationFrameId = setTimeout(playPolymorphismStep, 2500);
            }
        }


        // =========================================================
        // SOLID 原则演示 (简化版) - 仅做概念性动画
        // =========================================================

        const explanationBoxSRP = document.getElementById('srp-explanation');
        let srpStep = 0;
        function drawSRP(step) {
            clearCanvas();
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let explanationText = '';

            if (step === 0) {
                // 大而全的类
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(W / 2 - 150, H / 2 - 100, 300, 200);
                ctx.strokeStyle = '#c0392b';
                ctx.lineWidth = 3;
                ctx.strokeRect(W / 2 - 150, H / 2 - 100, 300, 200);
                ctx.fillStyle = 'white';
                ctx.fillText('GodClass (上帝类)', W / 2, H / 2 - 50);
                ctx.font = '16px Arial';
                ctx.fillText('负责数据存储', W / 2, H / 2 - 10);
                ctx.fillText('负责业务逻辑', W / 2, H / 2 + 20);
                ctx.fillText('负责用户界面', W / 2, H / 2 + 50);
                explanationText = '<strong>单一职责原则 (SRP):</strong> 最初，一个类可能承担了过多的职责，比如数据存储、业务逻辑和用户界面处理，成为一个"上帝类"。这会使代码难以维护和扩展。';
            } else if (step === 1) {
                // 拆分后的类
                ctx.fillStyle = '#2ecc71';
                ctx.fillRect(W / 4 - 80, H / 2 - 50, 160, 100);
                ctx.strokeStyle = '#27ae60';
                ctx.strokeRect(W / 4 - 80, H / 2 - 50, 160, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('数据存储类', W / 4, H / 2);

                ctx.fillStyle = '#f1c40f';
                ctx.fillRect(W / 2 - 80, H / 2 - 50, 160, 100);
                ctx.strokeStyle = '#f39c12';
                ctx.strokeRect(W / 2 - 80, H / 2 - 50, 160, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('业务逻辑类', W / 2, H / 2);

                ctx.fillStyle = '#3498db';
                ctx.fillRect(W * 3 / 4 - 80, H / 2 - 50, 160, 100);
                ctx.strokeStyle = '#2980b9';
                ctx.strokeRect(W * 3 / 4 - 80, H / 2 - 50, 160, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('用户界面类', W * 3 / 4, H / 2);

                explanationText = '<strong>单一职责原则 (SRP) 应用:</strong> 我们应该将这些职责拆分到不同的类中，每个类只负责一个单一的功能。这样每个类都更小、更专注于自己的任务，易于理解和修改。';
            }
            explanationBoxSRP.innerHTML = explanationText;
        }

        function startSRPDemo() {
            stopAnimation();
            srpStep = 0;
            playSRPStep();
        }

        function playSRPStep() {
            drawSRP(srpStep);
            srpStep++;
            if (srpStep <= 1) {
                animationFrameId = setTimeout(playSRPStep, 2500);
            } else {
                explanationBoxSRP.innerHTML += '<p><strong>总结：</strong>一个类只负责一件事。更改一个功能不会影响其他不相关的功能。</p>';
            }
        }

        const explanationBoxOCP = document.getElementById('ocp-explanation');
        let ocpStep = 0;
        function drawOCP(step) {
            clearCanvas();
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let explanationText = '';

            // 基础模块
            ctx.fillStyle = '#9b59b6';
            ctx.fillRect(W / 2 - 80, H / 2 - 100, 160, 80);
            ctx.strokeStyle = '#8e44ad';
            ctx.strokeRect(W / 2 - 80, H / 2 - 100, 160, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('核心逻辑 (封闭)', W / 2, H / 2 - 60);
            ctx.fillText('不会改变', W / 2, H / 2 - 30);


            if (step === 0) {
                explanationText = '<strong>开放封闭原则 (OCP):</strong> 一个好的软件设计应该对扩展开放，对修改封闭。这意味着当需求变化时，你应该通过添加新代码来扩展功能，而不是修改现有代码。';
            } else if (step === 1) {
                // 扩展模块
                ctx.fillStyle = '#2ecc71';
                ctx.fillRect(W / 2 - 80, H / 2 + 20, 160, 80);
                ctx.strokeStyle = '#27ae60';
                ctx.strokeRect(W / 2 - 80, H / 2 + 20, 160, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('新功能模块 (开放)', W / 2, H / 2 + 60);

                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(W / 2, H / 2 - 20);
                ctx.lineTo(W / 2, H / 2 + 20);
                ctx.stroke();
                ctx.fillText('扩展', W / 2 + 50, H / 2);
                explanationText = '<strong>开放封闭原则 (OCP) 应用:</strong> 当需要添加新功能时，我们不是修改已有的核心逻辑，而是通过扩展（例如继承、实现接口）来增加新功能。核心逻辑保持不变，确保了稳定性。';
            }
            explanationBoxOCP.innerHTML = explanationText;
        }

        function startOCPDemo() {
            stopAnimation();
            ocpStep = 0;
            playOCPStep();
        }

        function playOCPStep() {
            drawOCP(ocpStep);
            ocpStep++;
            if (ocpStep <= 1) {
                animationFrameId = setTimeout(playOCPStep, 2500);
            } else {
                explanationBoxOCP.innerHTML += '<p><strong>总结：</strong>增加新功能不修改旧代码，通过扩展实现。</p>';
            }
        }

        const explanationBoxLSP = document.getElementById('lsp-explanation');
        let lspStep = 0;

        function drawLSP(step) {
            clearCanvas();
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let explanationText = '';

            const birdX = W / 2 - 100;
            const birdY = H / 2 - 150;

            // Bird class
            ctx.fillStyle = '#f1c40f';
            ctx.fillRect(birdX, birdY, 200, 100);
            ctx.strokeStyle = '#f39c12';
            ctx.strokeRect(birdX, birdY, 200, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('Bird (鸟类)', birdX + 100, birdY + 30);
            ctx.fillText('+ fly()', birdX + 100, birdY + 70);

            if (step === 0) {
                explanationText = '<strong>里式替换原则 (LSP):</strong> 子类型必须能够替换掉它们的基类型（父类）而不会破坏程序的正确性。简单来说，子类应该能够在任何父类能出现的地方使用。';
            } else if (step === 1) {
                // Penguin inheriting from Bird
                const penguinX = W / 2 - 100;
                const penguinY = H / 2 + 50;

                ctx.fillStyle = '#3498db';
                ctx.fillRect(penguinX, penguinY, 200, 100);
                ctx.strokeStyle = '#2980b9';
                ctx.strokeRect(penguinX, penguinY, 200, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('Penguin (企鹅)', penguinX + 100, penguinY + 30);
                ctx.fillText('+ swim()', penguinX + 100, penguinY + 70);
                ctx.fillStyle = '#e74c3c';
                ctx.fillText('- fly() (不能飞)', penguinX + 100, penguinY + 90);


                ctx.strokeStyle = '#c0392b';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(birdX + 100, birdY + 100);
                ctx.lineTo(penguinX + 100, penguinY);
                ctx.stroke();
                ctx.fillText('继承', W / 2 + 50, H / 2);

                explanationText = '<strong>里式替换原则 (LSP) 违反示例:</strong> `Penguin` 是 `Bird` 的子类，但 `Penguin` 不会飞。如果一个函数期望 `Bird` 并调用 `fly()`，当传入 `Penguin` 时，就会有问题。这违反了LSP。';
            }
            explanationBoxLSP.innerHTML = explanationText;
        }

        function startLSPDemo() {
            stopAnimation();
            lspStep = 0;
            playLSPStep();
        }

        function playLSPStep() {
            drawLSP(lspStep);
            lspStep++;
            if (lspStep <= 1) {
                animationFrameId = setTimeout(playLSPStep, 2500);
            } else {
                explanationBoxLSP.innerHTML += '<p><strong>总结：</strong>子类必须能够无缝替换父类。如果子类不能完成父类能做的所有事情，则违反了此原则。</p>';
            }
        }

        const explanationBoxDIP = document.getElementById('dip-explanation');
        let dipStep = 0;

        function drawDIP(step) {
            clearCanvas();
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let explanationText = '';

            if (step === 0) {
                // 传统依赖：高层模块直接依赖底层模块
                ctx.fillStyle = '#f1c40f';
                ctx.fillRect(W / 2 - 100, H / 2 - 150, 200, 80);
                ctx.strokeStyle = '#f39c12';
                ctx.strokeRect(W / 2 - 100, H / 2 - 150, 200, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('高层模块 (应用逻辑)', W / 2, H / 2 - 110);

                ctx.fillStyle = '#3498db';
                ctx.fillRect(W / 2 - 100, H / 2 + 50, 200, 80);
                ctx.strokeStyle = '#2980b9';
                ctx.strokeRect(W / 2 - 100, H / 2 + 50, 200, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('底层模块 (数据库)', W / 2, H / 2 + 90);

                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(W / 2, H / 2 - 70);
                ctx.lineTo(W / 2, H / 2 + 50);
                ctx.stroke();
                ctx.fillText('直接依赖', W / 2 + 80, H / 2 - 10);
                explanationText = '<strong>依赖倒置原则 (DIP):</strong> 高层模块不应该依赖于低层模块，两者都应该依赖于抽象。最初，高层模块（如业务逻辑）可能直接依赖于低层模块（如具体的数据库实现）。这使得修改底层实现很困难。';
            } else if (step === 1) {
                // 引入抽象：高层和底层都依赖抽象
                ctx.fillStyle = '#9b59b6';
                ctx.fillRect(W / 2 - 100, H / 2 - 50, 200, 80);
                ctx.strokeStyle = '#8e44ad';
                ctx.strokeRect(W / 2 - 100, H / 2 - 50, 200, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('抽象 (接口/抽象类)', W / 2, H / 2 - 10);

                ctx.fillStyle = '#f1c40f'; // 高层
                ctx.fillRect(W / 4 - 100, H / 2 - 150, 200, 80);
                ctx.strokeStyle = '#f39c12';
                ctx.strokeRect(W / 4 - 100, H / 2 - 150, 200, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('高层模块', W / 4, H / 2 - 110);

                ctx.fillStyle = '#3498db'; // 底层
                ctx.fillRect(W * 3 / 4 - 100, H / 2 - 150, 200, 80);
                ctx.strokeStyle = '#2980b9';
                ctx.strokeRect(W * 3 / 4 - 100, H / 2 - 150, 200, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('底层模块 A (MySQL)', W * 3 / 4, H / 2 - 110);

                ctx.fillStyle = '#2ecc71'; // 另一个底层
                ctx.fillRect(W * 3 / 4 - 100, H / 2 + 50, 200, 80);
                ctx.strokeStyle = '#27ae60';
                ctx.strokeRect(W * 3 / 4 - 100, H / 2 + 50, 200, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('底层模块 B (MongoDB)', W * 3 / 4, H / 2 + 90);

                // 高层依赖抽象
                ctx.strokeStyle = '#2ecc71';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(W / 4, H / 2 - 70);
                ctx.lineTo(W / 2 - 50, H / 2 - 50);
                ctx.stroke();
                ctx.fillText('依赖抽象', W / 4 + 70, H / 2 - 80);

                // 抽象被底层实现
                ctx.strokeStyle = '#2ecc71';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(W / 2 + 50, H / 2 - 50);
                ctx.lineTo(W * 3 / 4, H / 2 - 70);
                ctx.stroke();
                ctx.fillText('实现抽象', W * 3 / 4 - 70, H / 2 - 80);

                ctx.beginPath();
                ctx.moveTo(W / 2 + 50, H / 2 + 30);
                ctx.lineTo(W * 3 / 4, H / 2 + 50);
                ctx.stroke();
                explanationText = '<strong>依赖倒置原则 (DIP) 应用:</strong> 高层和低层模块都依赖于一个抽象（接口）。这样，高层模块不知道具体的底层实现，只需要知道接口，从而实现了模块间的解耦。替换底层数据库变得非常容易。';
            }
            explanationBoxDIP.innerHTML = explanationText;
        }

        function startDIPDemo() {
            stopAnimation();
            dipStep = 0;
            playDIPStep();
        }

        function playDIPStep() {
            drawDIP(dipStep);
            dipStep++;
            if (dipStep <= 1) {
                animationFrameId = setTimeout(playDIPStep, 2500);
            } else {
                explanationBoxDIP.innerHTML += '<p><strong>总结：</strong>依赖抽象而不是具体实现。这让系统更灵活，易于变更。</p>';
            }
        }

        const explanationBoxISP = document.getElementById('isp-explanation');
        let ispStep = 0;

        function drawISP(step) {
            clearCanvas();
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let explanationText = '';

            if (step === 0) {
                // 臃肿的接口
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(W / 2 - 100, H / 2 - 80, 200, 160);
                ctx.strokeStyle = '#c0392b';
                ctx.strokeRect(W / 2 - 100, H / 2 - 80, 200, 160);
                ctx.fillStyle = 'white';
                ctx.fillText('臃肿的接口', W / 2, H / 2 - 50);
                ctx.font = '16px Arial';
                ctx.fillText('+ call()', W / 2, H / 2 - 10);
                ctx.fillText('+ watchVideo()', W / 2, H / 2 + 20);
                ctx.fillText('+ playGame()', W / 2, H / 2 + 50);
                ctx.fillText('+ browseWeb()', W / 2, H / 2 + 80);

                explanationText = '<strong>接口分离原则 (ISP):</strong> 不应该强迫客户端依赖它们不使用的方法。一个臃肿的接口包含太多不相关的方法，导致实现它的类必须实现所有方法，即使有些功能用不到。';
            } else if (step === 1) {
                // 分离后的接口
                ctx.font = '20px Arial';
                // 接口 1
                ctx.fillStyle = '#2ecc71';
                ctx.fillRect(W / 4 - 80, H / 2 - 100, 160, 80);
                ctx.strokeStyle = '#27ae60';
                ctx.strokeRect(W / 4 - 80, H / 2 - 100, 160, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('IPhoneCall', W / 4, H / 2 - 70);
                ctx.font = '16px Arial';
                ctx.fillText('+ call()', W / 4, H / 2 - 40);

                // 接口 2
                ctx.font = '20px Arial';
                ctx.fillStyle = '#f1c40f';
                ctx.fillRect(W / 2 - 80, H / 2 - 100, 160, 80);
                ctx.strokeStyle = '#f39c12';
                ctx.strokeRect(W / 2 - 80, H / 2 - 100, 160, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('IVideoPlayer', W / 2, H / 2 - 70);
                ctx.font = '16px Arial';
                ctx.fillText('+ watchVideo()', W / 2, H / 2 - 40);

                // 接口 3
                ctx.font = '20px Arial';
                ctx.fillStyle = '#3498db';
                ctx.fillRect(W * 3 / 4 - 80, H / 2 - 100, 160, 80);
                ctx.strokeStyle = '#2980b9';
                ctx.strokeRect(W * 3 / 4 - 80, H / 2 - 100, 160, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('IGamePlayer', W * 3 / 4, H / 2 - 70);
                ctx.font = '16px Arial';
                ctx.fillText('+ playGame()', W * 3 / 4, H / 2 - 40);

                ctx.font = '20px Arial';
                ctx.fillStyle = '#555';
                ctx.fillText('手机 (实现部分接口)', W / 2, H / 2 + 80);
                ctx.strokeStyle = '#555';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(W / 4, H / 2 - 20);
                ctx.lineTo(W / 2 - 80, H / 2 + 50);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(W / 2, H / 2 - 20);
                ctx.lineTo(W / 2, H / 2 + 50);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(W * 3 / 4, H / 2 - 20);
                ctx.lineTo(W / 2 + 80, H / 2 + 50);
                ctx.stroke();


                explanationText = '<strong>接口分离原则 (ISP) 应用:</strong> 更好的做法是将大接口拆分成更小、更具体的接口。这样，需要实现特定功能的类只需实现它真正需要的接口，避免了不必要的依赖。例如，手机可以只实现 `IPhoneCall` 和 `IVideoPlayer`。';
            }
            explanationBoxISP.innerHTML = explanationText;
        }

        function startISPDemo() {
            stopAnimation();
            ispStep = 0;
            playISPStep();
        }

        function playISPStep() {
            drawISP(ispStep);
            ispStep++;
            if (ispStep <= 1) {
                animationFrameId = setTimeout(playISPStep, 2500);
            } else {
                explanationBoxISP.innerHTML += '<p><strong>总结：</strong>客户端不应该被迫依赖它不使用的方法。将大接口拆分成小接口。</p>';
            }
        }


        // 初始清空画布
        clearCanvas();

    </script>
</body>
</html> 