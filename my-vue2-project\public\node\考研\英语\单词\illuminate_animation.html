<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Illuminate</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #ffc107; /* A gold color for "illuminate" */
            --secondary-color: #ff8f00;
            --light-bg: #fffde7;
            --panel-bg: #ffffff;
            --text-color: #333;
            --text-muted: #7f8c8d;
            --canvas-bg: #000;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--secondary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--text-muted);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #555;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #fff9c4;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .breakdown-section .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: #111;
            transform: translateY(-2px);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            background: var(--canvas-bg);
        }

        #animation-canvas {
            width: 100%;
            height: calc(100% - 80px);
            border-radius: 15px;
        }
        
        .control-button {
            position: absolute;
            bottom: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #111;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.5);
            z-index: 10;
        }
        .control-button:hover { background-color: #ffa000; transform: translateY(-2px); }
        .control-button.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>illuminate</h1>
            <p class="pronunciation">[ɪˈluːmɪneɪt]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 照亮, 阐明, 启发</p>
                <div class="example">
                    <p><strong>例句：</strong> This discovery will illuminate the origins of the universe.</p>
                    <p><strong>翻译：</strong> 这个发现将阐明宇宙的起源。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="il-game">il- (在...之上)</button>
                    <button class="morpheme-btn" data-activity="lumin-game">-lumin- (光)</button>
                </div>
                <p class="insight">点击上方词缀，体验Canvas绘制的互动游戏！</p>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="control-btn" class="control-button hidden">Illuminate!</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const controlBtn = document.getElementById('control-btn');
        let animationFrameId;

        const panel = canvas.parentElement;
        canvas.width = panel.clientWidth;
        canvas.height = panel.clientHeight - 80;

        let particles = [];
        let lightRays = [];
        let maze = { grid: [], solution: [], lightSource: null };

        class Particle {
            constructor(x, y, vx, vy, color, size, life = 1) {
                this.x = x; this.y = y; this.vx = vx; this.vy = vy;
                this.color = color; this.size = size; this.life = life;
            }
            update() { this.x += this.vx; this.y += this.vy; this.life -= 0.01; }
            draw() {
                ctx.globalAlpha = Math.max(0, this.life);
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;
            }
        }
        
        function welcomeScreen() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#fff';
            ctx.font = '24px Roboto';
            ctx.textAlign = 'center';
            ctx.fillText('祝贺！这是"10词挑战"的最后一个单词！', canvas.width / 2, canvas.height / 2 - 20);
             ctx.fillText('请选择一个活动来开始。', canvas.width / 2, canvas.height / 2 + 20);
            controlBtn.classList.add('hidden');
        }

        // --- Morpheme Games ---
        function initIlGame() {
            particles = [];
            controlBtn.textContent = '投射光芒于其上';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                for (let i = 0; i < 50; i++) {
                    particles.push(new Particle(Math.random() * canvas.width, 0, 0, Math.random() * 2 + 1, `hsl(50, 100%, ${50 + Math.random() * 50}%)`, Math.random() * 2));
                }
            };
            animateIlGame();
        }
        function animateIlGame() {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#555';
            ctx.fillRect(canvas.width/2 - 100, canvas.height/2, 200, 50);
            particles.forEach((p, index) => {
                if (p.y > canvas.height/2 && p.y < canvas.height/2 + 50 && p.x > canvas.width/2 - 100 && p.x < canvas.width/2 + 100) {
                     p.life = 0;
                     ctx.fillStyle = p.color;
                     ctx.fillRect(canvas.width/2 - 100, canvas.height/2, 200, 50);
                }
                if (p.life > 0) { p.update(); p.draw(); } 
                else { particles.splice(index, 1); }
            });
            animationFrameId = requestAnimationFrame(animateIlGame);
        }

        function initLuminGame() {
            lightRays = [];
            controlBtn.textContent = '释放光芒';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                for (let i = 0; i < 36; i++) {
                    const angle = i * 10 * Math.PI / 180;
                    lightRays.push({ angle, length: 0, life: 1 });
                }
            };
            animateLuminGame();
        }
        function animateLuminGame() {
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            const cx = canvas.width / 2;
            const cy = canvas.height / 2;
            lightRays.forEach((ray, index) => {
                if (ray.life > 0) {
                    ray.length += 5;
                    ray.life -= 0.01;
                    ctx.strokeStyle = `hsla(50, 100%, 70%, ${ray.life})`;
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(cx, cy);
                    ctx.lineTo(cx + Math.cos(ray.angle) * ray.length, cy + Math.sin(ray.angle) * ray.length);
                    ctx.stroke();
                } else {
                    lightRays.splice(index, 1);
                }
            });
            animationFrameId = requestAnimationFrame(animateLuminGame);
        }
        
        // --- Full Animation ---
        function initFullAnimation() {
            // Simplified maze generation
            const cols = 25, rows = 15;
            const cellSize = 30;
            maze.grid = Array(rows).fill(null).map(() => Array(cols).fill(1)); // 1 = wall
            maze.solution = [];
            let cx = 1, cy = 1;
            maze.grid[cy][cx] = 0;
            maze.solution.push({x: cx, y: cy});
            
            for(let i = 0; i < 100; i++){ // Create a simple path
                 if(Math.random() > 0.5 && cx < cols-2) cx++; else if(cy<rows-2)cy++;
                 maze.grid[cy][cx] = 0;
                 maze.solution.push({x: cx, y: cy});
            }

            maze.lightSource = { pathIndex: 0, x: 0, y: 0 };
            controlBtn.textContent = 'Illuminate!';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                maze.lightSource.pathIndex = 0;
            };
            animateFull();
        }
        
        function animateFull() {
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            const cellSize = 30;
            const offsetX = (canvas.width - maze.grid[0].length * cellSize) / 2;
            const offsetY = (canvas.height - maze.grid.length * cellSize) / 2;

            // Draw maze (dimly)
            for (let y = 0; y < maze.grid.length; y++) {
                for (let x = 0; x < maze.grid[y].length; x++) {
                    if (maze.grid[y][x] === 1) {
                        ctx.fillStyle = '#222';
                        ctx.fillRect(offsetX + x * cellSize, offsetY + y * cellSize, cellSize, cellSize);
                    }
                }
            }
            
            // Draw illuminated path
            const ls = maze.lightSource;
            if (ls.pathIndex < maze.solution.length -1) {
                ls.pathIndex++;
            }
            const currentPos = maze.solution[ls.pathIndex];
            ls.x = offsetX + currentPos.x * cellSize + cellSize/2;
            ls.y = offsetY + currentPos.y * cellSize + cellSize/2;

            for(let i=0; i<=ls.pathIndex; i++){
                const pos = maze.solution[i];
                 ctx.fillStyle = '#554';
                 ctx.fillRect(offsetX + pos.x * cellSize, offsetY + pos.y * cellSize, cellSize, cellSize);
            }
            
            // Draw light source
            const gradient = ctx.createRadialGradient(ls.x, ls.y, 5, ls.x, ls.y, 50);
            gradient.addColorStop(0, 'rgba(255, 223, 186, 1)');
            gradient.addColorStop(1, 'rgba(255, 193, 7, 0)');
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(ls.x, ls.y, 50, 0, Math.PI * 2);
            ctx.fill();

            animationFrameId = requestAnimationFrame(animateFull);
        }

        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                if (animationFrameId) cancelAnimationFrame(animationFrameId);
                const activity = btn.dataset.activity;
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                if (activity === 'il-game') initIlGame();
                else if (activity === 'lumin-game') initLuminGame();
                else if (activity === 'full-animation') initFullAnimation();
            });
        });

        welcomeScreen();
    });
    </script>
</body>
</html> 