<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Transcend Animation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        .container {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #e0f7fa; /* Sky blue background */
        }
        .translation {
            margin-top: 10px;
            font-size: 1.2em;
        }
        .explanation {
            margin-top: 10px;
            text-align: left;
            max-width: 600px;
        }
        button {
            margin-top: 10px;
            padding: 10px 20px;
            font-size: 1em;
            cursor: pointer;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>Transcend</h1>
    <canvas id="wordCanvas" width="600" height="300"></canvas>
    <div class="translation">
        <p><strong>翻译:</strong> v. 超越；克服；胜过</p>
    </div>
    <div class="explanation">
        <p><strong>词源拆解:</strong></p>
        <p>单词 <strong>transcend</strong> 由两部分组成：</p>
        <ul>
            <li><strong>trans-</strong>: 前缀，意思是 "横跨" 或 "超越" (across, beyond)。</li>
            <li><strong>scend</strong>: 词根，源自拉丁语 'scandere'，意思是 "攀爬" (to climb)。</li>
        </ul>
        <p><strong>故事记忆法:</strong></p>
        <p>想象一个人正在努力地<strong>攀爬</strong> (`scend`) 一堵代表"局限"的高墙。当他爬到顶端并翻越过去后，他就<strong>超越</strong> (`trans-`) 了这堵墙，看到了一个全新的世界。这个"攀爬并超越"的动作，完美地诠释了 "transcend" 的含义——超越极限，达到新的高度。</p>
    </div>
    <button id="replay">Replay Animation</button>
</div>

<script>
    const canvas = document.getElementById('wordCanvas');
    const ctx = canvas.getContext('2d');
    const replayBtn = document.getElementById('replay');

    let animationFrameId;

    const climber = {
        x: 290,
        y: 280,
        targetY: 100,
        speed: 0.8,
        phase: 'climbing', // climbing, crossing, done
        draw() {
            // Body
            ctx.beginPath();
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x, this.y - 20); // shorter body
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 3;
            ctx.stroke();
            // Head
            ctx.beginPath();
            ctx.arc(this.x, this.y - 25, 5, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();
            // Arms & Legs for climbing
            ctx.beginPath();
            ctx.moveTo(this.x, this.y - 15);
            ctx.lineTo(this.x - 10, this.y - 10);
            ctx.moveTo(this.x, this.y - 15);
            ctx.lineTo(this.x + 10, this.y - 10);
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x - 10, this.y + 5);
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x + 10, this.y + 5);
            ctx.stroke();
        }
    };
    
    const wall = {
        x: 300,
        y: 150,
        width: 20,
        height: 150,
        draw() {
            ctx.fillStyle = '#a0a0a0';
            ctx.fillRect(this.x, this.y, this.width, this.height);
            ctx.font = '16px Arial';
            ctx.fillStyle = 'black';
            ctx.fillText('Limits', this.x - 50, this.y + 70);
        }
    }

    const beyond = {
        alpha: 0,
        draw() {
            if (this.alpha <= 0) return;
            ctx.fillStyle = `rgba(255, 255, 0, ${this.alpha})`;
            ctx.fillRect(wall.x + wall.width, 0, canvas.width - wall.x - wall.width, canvas.height);
            
            ctx.font = 'bold 30px Arial';
            ctx.fillStyle = `rgba(0, 128, 0, ${this.alpha})`;
            ctx.fillText('New Realm', 400, 100);
        }
    }

    let transText = { alpha: 0 };
    let scendText = { alpha: 0 };

    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        beyond.draw();
        wall.draw();
        climber.draw();
        
        // Draw "scend"
        ctx.font = 'bold 40px Arial';
        ctx.fillStyle = `rgba(255, 0, 0, ${scendText.alpha})`;
        ctx.fillText('scend', 150, 100);

        // Draw "trans-"
        ctx.font = 'bold 40px Arial';
        ctx.fillStyle = `rgba(0, 0, 255, ${transText.alpha})`;
        ctx.fillText('trans-', 350, 50);

    }

    function animate() {
        // Update state
        if (climber.phase === 'climbing') {
            climber.y -= climber.speed;
            scendText.alpha = Math.min(1, scendText.alpha + 0.01);
            if (climber.y <= wall.y - 25) {
                climber.phase = 'crossing';
            }
        } else if (climber.phase === 'crossing') {
            climber.x += climber.speed;
            transText.alpha = Math.min(1, transText.alpha + 0.02);
            beyond.alpha = Math.min(0.5, beyond.alpha + 0.01);
            if (climber.x >= wall.x + 40) {
                climber.phase = 'done';
            }
        }

        draw();

        if (climber.phase !== 'done') {
            animationFrameId = requestAnimationFrame(animate);
        }
    }
    
    function startAnimation() {
        cancelAnimationFrame(animationFrameId);
        climber.x = 290;
        climber.y = 280;
        climber.phase = 'climbing';
        transText.alpha = 0;
        scendText.alpha = 0;
        beyond.alpha = 0;
        animate();
    }

    replayBtn.addEventListener('click', startAnimation);

    // Initial animation start
    startAnimation();

</script>
</body>
</html> 