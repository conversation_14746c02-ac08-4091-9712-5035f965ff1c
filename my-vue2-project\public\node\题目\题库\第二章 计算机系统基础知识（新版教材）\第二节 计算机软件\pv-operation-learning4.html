<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PV操作与进程同步 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8em;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(245, 87, 108, 0.4);
        }

        .explanation {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            font-size: 16px;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .process-box {
            display: inline-block;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-align: center;
            line-height: 60px;
            font-weight: bold;
            color: white;
            margin: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .process-waiting {
            background: #ff6b6b;
            animation: pulse 2s infinite;
        }

        .process-running {
            background: #4ecdc4;
            animation: rotate 1s linear infinite;
        }

        .process-completed {
            background: #45b7d1;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .semaphore-display {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .semaphore {
            background: white;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            min-width: 80px;
            transition: all 0.3s ease;
        }

        .semaphore.active {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(10px);
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .quiz-option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #4CAF50;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 PV操作与进程同步</h1>
            <p>零基础交互式学习 - 用动画理解操作系统核心概念</p>
        </div>

        <!-- 基础概念介绍 -->
        <div class="section">
            <h2 class="section-title">📚 什么是PV操作？</h2>
            <div class="explanation">
                <p><span class="highlight">PV操作</span>是操作系统中用来控制进程同步的重要机制：</p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><strong>P操作（等待）</strong>：信号量减1，如果结果<0则阻塞进程</li>
                    <li><strong>V操作（释放）</strong>：信号量加1，如果有等待进程则唤醒</li>
                    <li><strong>信号量</strong>：一个整数变量，表示可用资源数量</li>
                </ul>
            </div>
            
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="800" height="300"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="animatePOperation()">演示P操作</button>
                <button class="btn btn-secondary" onclick="animateVOperation()">演示V操作</button>
                <button class="btn btn-primary" onclick="resetConcept()">重置</button>
            </div>
        </div>

        <!-- 进程前趋图 -->
        <div class="section">
            <h2 class="section-title">🔗 进程前趋图分析</h2>
            <div class="explanation">
                <p>根据题目，我们有5个进程P1-P5，它们的执行顺序关系如下：</p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li>P1和P2可以并发执行（没有前趋）</li>
                    <li>P3必须等待P1和P2都完成</li>
                    <li>P4必须等待P1和P3都完成</li>
                    <li>P5必须等待P2和P3都完成</li>
                </ul>
            </div>
            
            <div class="canvas-container">
                <canvas id="processCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startProcessAnimation()">开始执行</button>
                <button class="btn btn-secondary" onclick="stepByStep()">单步执行</button>
                <button class="btn btn-primary" onclick="resetProcess()">重置</button>
            </div>
            
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
                <div class="step" id="step5">5</div>
            </div>
        </div>

        <!-- 信号量状态显示 -->
        <div class="section">
            <h2 class="section-title">📊 信号量状态监控</h2>
            <div class="explanation">
                <p>6个信号量S1-S6对应6条前趋关系：</p>
                <p>S1(P1→P3), S2(P1→P4), S3(P2→P3), S4(P2→P5), S5(P3→P4), S6(P3→P5)</p>
            </div>
            
            <div class="semaphore-display">
                <div class="semaphore" id="s1">
                    <div>S1</div>
                    <div id="s1-value">0</div>
                </div>
                <div class="semaphore" id="s2">
                    <div>S2</div>
                    <div id="s2-value">0</div>
                </div>
                <div class="semaphore" id="s3">
                    <div>S3</div>
                    <div id="s3-value">0</div>
                </div>
                <div class="semaphore" id="s4">
                    <div>S4</div>
                    <div id="s4-value">0</div>
                </div>
                <div class="semaphore" id="s5">
                    <div>S5</div>
                    <div id="s5-value">0</div>
                </div>
                <div class="semaphore" id="s6">
                    <div>S6</div>
                    <div id="s6-value">0</div>
                </div>
            </div>
        </div>

        <!-- 答题解析 -->
        <div class="quiz-section">
            <h2 class="section-title" style="color: white;">🎯 题目解析与答案</h2>

            <!-- 问题1 -->
            <div style="margin: 20px 0;">
                <h3>问题1：a和b处应分别填写什么？</h3>
                <div class="quiz-option" onclick="selectAnswer(this, false, 1)">
                    A. P(S1)P(S2)和P(S3)P(S4)
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false, 1)">
                    B. P(S1)V(S2)和P(S2)V(S1)
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, true, 1)">
                    C. V(S1)V(S2)和V(S3)V(S4) ✓
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false, 1)">
                    D. P(S1)P(S2)和V(S1)V(S2)
                </div>
            </div>

            <div class="explanation" style="background: rgba(255,255,255,0.1); border-left-color: white;">
                <h4>💡 解题思路：</h4>
                <p><strong>a处（P1结束后）</strong>：P1完成后要通知P3和P4可以开始，所以执行V(S1)V(S2)</p>
                <p><strong>b处（P2结束后）</strong>：P2完成后要通知P3和P5可以开始，所以执行V(S3)V(S4)</p>
                <p><strong>关键理解</strong>：进程结束后要"释放"资源，让等待的进程知道前趋条件已满足！</p>
            </div>

            <!-- 问题2 -->
            <div style="margin: 30px 0; border-top: 2px solid rgba(255,255,255,0.3); padding-top: 20px;">
                <h3>问题2：c和d处应分别填写什么？</h3>
                <div class="quiz-option" onclick="selectAnswer(this, false, 2)">
                    A. P(S1)P(S2)和V(S3)V(S4)
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, true, 2)">
                    B. P(S1)P(S3)和V(S5)V(S6) ✓
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false, 2)">
                    C. V(S1)V(S2)和P(S3)P(S4)
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false, 2)">
                    D. P(S1)V(S3)和P(S2)V(S4)
                </div>
            </div>

            <div class="explanation" style="background: rgba(255,255,255,0.1); border-left-color: white;">
                <h4>💡 解题思路：</h4>
                <p><strong>c处（P3开始前）</strong>：P3需要等待P1和P2都完成，所以执行P(S1)P(S3)</p>
                <p><strong>d处（P3结束后）</strong>：P3完成后要通知P4和P5可以开始，所以执行V(S5)V(S6)</p>
                <p><strong>关键理解</strong>：进程开始前要"等待"前趋完成，结束后要"通知"后继进程！</p>
            </div>

            <!-- 问题3 -->
            <div style="margin: 30px 0; border-top: 2px solid rgba(255,255,255,0.3); padding-top: 20px;">
                <h3>问题3：e和f处应分别填写什么？</h3>
                <div class="quiz-option" onclick="selectAnswer(this, false, 3)">
                    A. P(S3)P(S4)和V(S5)V(S6)
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false, 3)">
                    B. V(S5)V(S6)和P(S5)P(S6)
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, true, 3)">
                    C. P(S2)P(S5)和P(S4)P(S6) ✓
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false, 3)">
                    D. P(S4)V(S5)和P(S5)V(S6)
                </div>
            </div>

            <div class="explanation" style="background: rgba(255,255,255,0.1); border-left-color: white;">
                <h4>💡 解题思路：</h4>
                <p><strong>e处（P4开始前）</strong>：P4需要等待P1和P3都完成，所以执行P(S2)P(S5)</p>
                <p><strong>f处（P5开始前）</strong>：P5需要等待P2和P3都完成，所以执行P(S4)P(S6)</p>
                <p><strong>关键理解</strong>：P4和P5都只需要等待，不需要通知其他进程，所以都是P操作！</p>
            </div>
        </div>

        <!-- 详细执行流程演示 -->
        <div class="section">
            <h2 class="section-title">🔍 详细执行流程分析</h2>
            <div class="canvas-container">
                <canvas id="detailCanvas" width="800" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="showP3Analysis()">分析P3执行</button>
                <button class="btn btn-secondary" onclick="showP4Analysis()">分析P4执行</button>
                <button class="btn btn-primary" onclick="showP5Analysis()">分析P5执行</button>
                <button class="btn btn-secondary" onclick="showCompleteFlow()">完整流程</button>
                <button class="btn btn-primary" onclick="resetDetailAnalysis()">重置分析</button>
            </div>

            <div class="explanation">
                <h4>🎯 信号量对应关系：</h4>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><strong>S1</strong>：P1 → P3 的同步信号</li>
                    <li><strong>S2</strong>：P1 → P4 的同步信号</li>
                    <li><strong>S3</strong>：P2 → P3 的同步信号</li>
                    <li><strong>S4</strong>：P2 → P5 的同步信号</li>
                    <li><strong>S5</strong>：P3 → P4 的同步信号</li>
                    <li><strong>S6</strong>：P3 → P5 的同步信号</li>
                </ul>
            </div>
        </div>

        <!-- 总结与规律 -->
        <div class="section">
            <h2 class="section-title">📋 解题规律总结</h2>
            <div class="explanation">
                <h4>🔑 核心规律：</h4>
                <ol style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                    <li><strong>进程结束后</strong>：执行V操作，通知所有后继进程</li>
                    <li><strong>进程开始前</strong>：执行P操作，等待所有前趋进程完成</li>
                    <li><strong>信号量对应</strong>：每条前趋关系对应一个信号量</li>
                    <li><strong>初值为0</strong>：确保严格按照前趋关系执行</li>
                </ol>

                <h4>💡 记忆口诀：</h4>
                <div style="background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%); padding: 15px; border-radius: 10px; margin: 15px 0;">
                    <p style="font-size: 18px; font-weight: bold; text-align: center; margin: 0;">
                        "结束V通知，开始P等待"<br>
                        "前趋完成才能开始，后继等待我通知"
                    </p>
                </div>

                <h4>🎯 答案汇总：</h4>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><strong>a和b处</strong>：V(S1)V(S2) 和 V(S3)V(S4) - 进程结束后的通知</li>
                    <li><strong>c和d处</strong>：P(S1)P(S3) 和 V(S5)V(S6) - P3的等待和通知</li>
                    <li><strong>e和f处</strong>：P(S2)P(S5) 和 P(S4)P(S6) - P4和P5的等待</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 0;
        let semaphoreValues = [0, 0, 0, 0, 0, 0]; // S1-S6
        let processStates = ['waiting', 'waiting', 'waiting', 'waiting', 'waiting']; // P1-P5
        let animationRunning = false;

        // 概念演示画布
        const conceptCanvas = document.getElementById('conceptCanvas');
        const conceptCtx = conceptCanvas.getContext('2d');

        // 进程图画布
        const processCanvas = document.getElementById('processCanvas');
        const processCtx = processCanvas.getContext('2d');

        // 详细分析画布
        const detailCanvas = document.getElementById('detailCanvas');
        const detailCtx = detailCanvas.getContext('2d');

        // 初始化
        function init() {
            drawConceptDiagram();
            drawProcessDiagram();
            drawDetailAnalysis();
        }

        // 绘制概念图
        function drawConceptDiagram() {
            conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);
            
            // 绘制信号量
            conceptCtx.fillStyle = '#667eea';
            conceptCtx.fillRect(350, 100, 100, 60);
            conceptCtx.fillStyle = 'white';
            conceptCtx.font = '20px Arial';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('信号量: 2', 400, 135);
            
            // 绘制P操作
            conceptCtx.fillStyle = '#ff6b6b';
            conceptCtx.fillRect(100, 200, 120, 50);
            conceptCtx.fillStyle = 'white';
            conceptCtx.fillText('P操作(-1)', 160, 230);
            
            // 绘制V操作
            conceptCtx.fillStyle = '#4ecdc4';
            conceptCtx.fillRect(580, 200, 120, 50);
            conceptCtx.fillStyle = 'white';
            conceptCtx.fillText('V操作(+1)', 640, 230);
            
            // 绘制箭头
            drawArrow(conceptCtx, 220, 225, 350, 150);
            drawArrow(conceptCtx, 450, 150, 580, 225);
        }

        // 绘制进程图
        function drawProcessDiagram() {
            processCtx.clearRect(0, 0, processCanvas.width, processCanvas.height);
            
            // 进程位置
            const positions = {
                P1: {x: 150, y: 100},
                P2: {x: 150, y: 250},
                P3: {x: 400, y: 175},
                P4: {x: 650, y: 100},
                P5: {x: 650, y: 250}
            };
            
            // 绘制进程
            Object.keys(positions).forEach((process, index) => {
                const pos = positions[process];
                const state = processStates[index];
                
                // 设置颜色
                if (state === 'waiting') {
                    processCtx.fillStyle = '#ff6b6b';
                } else if (state === 'running') {
                    processCtx.fillStyle = '#4ecdc4';
                } else {
                    processCtx.fillStyle = '#45b7d1';
                }
                
                processCtx.beginPath();
                processCtx.arc(pos.x, pos.y, 30, 0, 2 * Math.PI);
                processCtx.fill();
                
                processCtx.fillStyle = 'white';
                processCtx.font = '16px Arial';
                processCtx.textAlign = 'center';
                processCtx.fillText(process, pos.x, pos.y + 5);
            });
            
            // 绘制前趋关系箭头
            drawArrow(processCtx, 180, 100, 370, 160); // P1→P3
            drawArrow(processCtx, 180, 100, 620, 110); // P1→P4
            drawArrow(processCtx, 180, 250, 370, 190); // P2→P3
            drawArrow(processCtx, 180, 250, 620, 240); // P2→P5
            drawArrow(processCtx, 430, 175, 620, 110); // P3→P4
            drawArrow(processCtx, 430, 175, 620, 240); // P3→P5
        }

        // 绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY) {
            const headlen = 10;
            const dx = toX - fromX;
            const dy = toY - fromY;
            const angle = Math.atan2(dy, dx);
            
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // P操作动画
        function animatePOperation() {
            if (animationRunning) return;
            animationRunning = true;
            
            // 模拟P操作
            setTimeout(() => {
                conceptCtx.fillStyle = '#ff6b6b';
                conceptCtx.fillRect(350, 100, 100, 60);
                conceptCtx.fillStyle = 'white';
                conceptCtx.fillText('信号量: 1', 400, 135);
                animationRunning = false;
            }, 500);
        }

        // V操作动画
        function animateVOperation() {
            if (animationRunning) return;
            animationRunning = true;
            
            // 模拟V操作
            setTimeout(() => {
                conceptCtx.fillStyle = '#4ecdc4';
                conceptCtx.fillRect(350, 100, 100, 60);
                conceptCtx.fillStyle = 'white';
                conceptCtx.fillText('信号量: 3', 400, 135);
                animationRunning = false;
            }, 500);
        }

        // 重置概念图
        function resetConcept() {
            drawConceptDiagram();
        }

        // 开始进程动画
        function startProcessAnimation() {
            if (animationRunning) return;
            currentStep = 0;
            resetProcess();
            
            const steps = [
                () => { // P1, P2开始
                    processStates[0] = 'running';
                    processStates[1] = 'running';
                    updateStepIndicator(1);
                },
                () => { // P1完成，执行V(S1)V(S2)
                    processStates[0] = 'completed';
                    semaphoreValues[0] = 1; // S1
                    semaphoreValues[1] = 1; // S2
                    updateSemaphoreDisplay();
                    updateStepIndicator(2);
                },
                () => { // P2完成，执行V(S3)V(S4)
                    processStates[1] = 'completed';
                    semaphoreValues[2] = 1; // S3
                    semaphoreValues[3] = 1; // S4
                    updateSemaphoreDisplay();
                    updateStepIndicator(3);
                },
                () => { // P3开始（P(S1)P(S3)）
                    processStates[2] = 'running';
                    semaphoreValues[0] = 0; // S1
                    semaphoreValues[2] = 0; // S3
                    updateSemaphoreDisplay();
                    updateStepIndicator(4);
                },
                () => { // P3完成，P4和P5开始
                    processStates[2] = 'completed';
                    processStates[3] = 'running';
                    processStates[4] = 'running';
                    semaphoreValues[4] = 1; // S5
                    semaphoreValues[5] = 1; // S6
                    updateSemaphoreDisplay();
                    updateStepIndicator(5);
                }
            ];
            
            let stepIndex = 0;
            const executeStep = () => {
                if (stepIndex < steps.length) {
                    steps[stepIndex]();
                    drawProcessDiagram();
                    stepIndex++;
                    setTimeout(executeStep, 1500);
                }
            };
            
            executeStep();
        }

        // 单步执行
        function stepByStep() {
            // 实现单步执行逻辑
            startProcessAnimation();
        }

        // 重置进程
        function resetProcess() {
            processStates = ['waiting', 'waiting', 'waiting', 'waiting', 'waiting'];
            semaphoreValues = [0, 0, 0, 0, 0, 0];
            currentStep = 0;
            updateSemaphoreDisplay();
            updateStepIndicator(0);
            drawProcessDiagram();
        }

        // 更新信号量显示
        function updateSemaphoreDisplay() {
            for (let i = 0; i < 6; i++) {
                const element = document.getElementById(`s${i+1}-value`);
                element.textContent = semaphoreValues[i];
                
                const semElement = document.getElementById(`s${i+1}`);
                if (semaphoreValues[i] > 0) {
                    semElement.classList.add('active');
                } else {
                    semElement.classList.remove('active');
                }
            }
        }

        // 更新步骤指示器
        function updateStepIndicator(step) {
            for (let i = 1; i <= 5; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.classList.remove('active', 'completed');
                
                if (i < step) {
                    stepElement.classList.add('completed');
                } else if (i === step) {
                    stepElement.classList.add('active');
                }
            }
        }

        // 选择答案
        function selectAnswer(element, isCorrect, questionNum) {
            // 移除同一问题所有选项的样式
            const allOptions = element.parentElement.querySelectorAll('.quiz-option');
            allOptions.forEach(option => {
                option.classList.remove('correct', 'wrong');
            });

            // 添加相应样式
            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    if (questionNum === 1) {
                        alert('🎉 恭喜！答案正确！\n\n解释：\na处：P1结束后执行V(S1)V(S2)，通知P3和P4\nb处：P2结束后执行V(S3)V(S4)，通知P3和P5\n\n关键：进程结束后要"释放"信号量！');
                    } else if (questionNum === 2) {
                        alert('🎉 恭喜！答案正确！\n\n解释：\nc处：P3开始前执行P(S1)P(S3)，等待P1和P2完成\nd处：P3结束后执行V(S5)V(S6)，通知P4和P5\n\n关键：进程开始前要"等待"，结束后要"通知"！');
                    } else if (questionNum === 3) {
                        alert('🎉 恭喜！答案正确！\n\n解释：\ne处：P4开始前执行P(S2)P(S5)，等待P1和P3完成\nf处：P5开始前执行P(S4)P(S6)，等待P2和P3完成\n\n关键：P4和P5是最后执行的进程，只需要等待，不需要通知！');
                    }
                }, 300);
            } else {
                element.classList.add('wrong');
                setTimeout(() => {
                    if (questionNum === 1) {
                        alert('❌ 答案错误，请重新思考！\n\n提示：进程结束后应该"释放"信号量，让等待的进程知道可以开始了。');
                    } else if (questionNum === 2) {
                        alert('❌ 答案错误，请重新思考！\n\n提示：P3开始前要等待前趋完成（P操作），结束后要通知后继（V操作）。');
                    } else if (questionNum === 3) {
                        alert('❌ 答案错误，请重新思考！\n\n提示：P4需要等待P1和P3，P5需要等待P2和P3。它们都是最后执行的进程，只需要P操作等待！');
                    }
                }, 300);
            }
        }

        // 绘制详细分析图
        function drawDetailAnalysis() {
            detailCtx.clearRect(0, 0, detailCanvas.width, detailCanvas.height);

            // 绘制标题
            detailCtx.fillStyle = '#333';
            detailCtx.font = 'bold 20px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('PV操作详细分析', 400, 30);

            // 绘制说明文字
            detailCtx.font = '16px Arial';
            detailCtx.fillText('点击按钮查看各进程的PV操作分析', 400, 60);
        }

        // 分析P3执行
        function showP3Analysis() {
            detailCtx.clearRect(0, 0, detailCanvas.width, detailCanvas.height);

            // 标题
            detailCtx.fillStyle = '#667eea';
            detailCtx.font = 'bold 24px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('P3进程执行分析', 400, 40);

            // P3进程框
            detailCtx.fillStyle = '#4ecdc4';
            detailCtx.fillRect(350, 80, 100, 60);
            detailCtx.fillStyle = 'white';
            detailCtx.font = 'bold 18px Arial';
            detailCtx.fillText('P3', 400, 115);

            // 前趋条件
            detailCtx.fillStyle = '#333';
            detailCtx.font = '16px Arial';
            detailCtx.textAlign = 'left';
            detailCtx.fillText('前趋条件：P1和P2必须完成', 50, 180);

            // P操作
            detailCtx.fillStyle = '#ff6b6b';
            detailCtx.fillRect(100, 200, 120, 40);
            detailCtx.fillStyle = 'white';
            detailCtx.font = 'bold 14px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('P(S1)', 160, 225);

            detailCtx.fillStyle = '#ff6b6b';
            detailCtx.fillRect(250, 200, 120, 40);
            detailCtx.fillStyle = 'white';
            detailCtx.fillText('P(S3)', 310, 225);

            // 箭头指向P3
            drawArrow(detailCtx, 220, 220, 350, 120);
            drawArrow(detailCtx, 310, 220, 380, 120);

            // V操作
            detailCtx.fillStyle = '#4ecdc4';
            detailCtx.fillRect(480, 200, 120, 40);
            detailCtx.fillStyle = 'white';
            detailCtx.fillText('V(S5)', 540, 225);

            detailCtx.fillStyle = '#4ecdc4';
            detailCtx.fillRect(620, 200, 120, 40);
            detailCtx.fillStyle = 'white';
            detailCtx.fillText('V(S6)', 680, 225);

            // 箭头从P3指出
            drawArrow(detailCtx, 450, 120, 540, 200);
            drawArrow(detailCtx, 450, 120, 680, 200);

            // 说明文字
            detailCtx.fillStyle = '#333';
            detailCtx.font = '14px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('等待P1完成', 160, 260);
            detailCtx.fillText('等待P2完成', 310, 260);
            detailCtx.fillText('通知P4', 540, 260);
            detailCtx.fillText('通知P5', 680, 260);

            // 执行流程
            detailCtx.fillStyle = '#667eea';
            detailCtx.font = 'bold 16px Arial';
            detailCtx.fillText('执行顺序：P(S1) → P(S3) → P3执行 → V(S5) → V(S6)', 400, 320);

            // 解释
            detailCtx.fillStyle = '#333';
            detailCtx.font = '14px Arial';
            detailCtx.fillText('c处填写：P(S1)P(S3) - 等待前趋完成', 400, 350);
            detailCtx.fillText('d处填写：V(S5)V(S6) - 通知后继进程', 400, 380);
        }

        // 分析P4执行
        function showP4Analysis() {
            detailCtx.clearRect(0, 0, detailCanvas.width, detailCanvas.height);

            detailCtx.fillStyle = '#667eea';
            detailCtx.font = 'bold 24px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('P4进程执行分析', 400, 40);

            // P4进程框
            detailCtx.fillStyle = '#45b7d1';
            detailCtx.fillRect(350, 80, 100, 60);
            detailCtx.fillStyle = 'white';
            detailCtx.font = 'bold 18px Arial';
            detailCtx.fillText('P4', 400, 115);

            // 前趋条件
            detailCtx.fillStyle = '#333';
            detailCtx.font = '16px Arial';
            detailCtx.textAlign = 'left';
            detailCtx.fillText('前趋条件：P1和P3必须完成', 50, 180);

            // P操作
            detailCtx.fillStyle = '#ff6b6b';
            detailCtx.fillRect(200, 200, 120, 40);
            detailCtx.fillStyle = 'white';
            detailCtx.font = 'bold 14px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('P(S2)', 260, 225);

            detailCtx.fillStyle = '#ff6b6b';
            detailCtx.fillRect(480, 200, 120, 40);
            detailCtx.fillStyle = 'white';
            detailCtx.fillText('P(S5)', 540, 225);

            // 箭头指向P4
            drawArrow(detailCtx, 320, 220, 380, 140);
            drawArrow(detailCtx, 480, 220, 420, 140);

            // 说明文字
            detailCtx.fillStyle = '#333';
            detailCtx.font = '14px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('等待P1完成', 260, 260);
            detailCtx.fillText('等待P3完成', 540, 260);

            detailCtx.fillStyle = '#667eea';
            detailCtx.font = 'bold 16px Arial';
            detailCtx.fillText('执行顺序：P(S2) → P(S5) → P4执行', 400, 320);

            detailCtx.fillStyle = '#333';
            detailCtx.font = '14px Arial';
            detailCtx.fillText('e处填写：P(S2)P(S5) - 等待P1和P3完成', 400, 350);
        }

        // 分析P5执行
        function showP5Analysis() {
            detailCtx.clearRect(0, 0, detailCanvas.width, detailCanvas.height);

            detailCtx.fillStyle = '#667eea';
            detailCtx.font = 'bold 24px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('P5进程执行分析', 400, 40);

            // P5进程框
            detailCtx.fillStyle = '#f093fb';
            detailCtx.fillRect(350, 80, 100, 60);
            detailCtx.fillStyle = 'white';
            detailCtx.font = 'bold 18px Arial';
            detailCtx.fillText('P5', 400, 115);

            // 前趋条件
            detailCtx.fillStyle = '#333';
            detailCtx.font = '16px Arial';
            detailCtx.textAlign = 'left';
            detailCtx.fillText('前趋条件：P2和P3必须完成', 50, 180);

            // P操作
            detailCtx.fillStyle = '#ff6b6b';
            detailCtx.fillRect(200, 200, 120, 40);
            detailCtx.fillStyle = 'white';
            detailCtx.font = 'bold 14px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('P(S4)', 260, 225);

            detailCtx.fillStyle = '#ff6b6b';
            detailCtx.fillRect(480, 200, 120, 40);
            detailCtx.fillStyle = 'white';
            detailCtx.fillText('P(S6)', 540, 225);

            // 箭头指向P5
            drawArrow(detailCtx, 320, 220, 380, 140);
            drawArrow(detailCtx, 480, 220, 420, 140);

            // 说明文字
            detailCtx.fillStyle = '#333';
            detailCtx.font = '14px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('等待P2完成', 260, 260);
            detailCtx.fillText('等待P3完成', 540, 260);

            detailCtx.fillStyle = '#667eea';
            detailCtx.font = 'bold 16px Arial';
            detailCtx.fillText('执行顺序：P(S4) → P(S6) → P5执行', 400, 320);

            detailCtx.fillStyle = '#333';
            detailCtx.font = '14px Arial';
            detailCtx.fillText('f处填写：P(S4)P(S6) - 等待P2和P3完成', 400, 350);
        }

        // 显示完整流程
        function showCompleteFlow() {
            detailCtx.clearRect(0, 0, detailCanvas.width, detailCanvas.height);

            detailCtx.fillStyle = '#667eea';
            detailCtx.font = 'bold 24px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('完整PV操作流程', 400, 40);

            // 绘制时间轴
            detailCtx.strokeStyle = '#333';
            detailCtx.lineWidth = 3;
            detailCtx.beginPath();
            detailCtx.moveTo(50, 100);
            detailCtx.lineTo(750, 100);
            detailCtx.stroke();

            // 时间点
            const timePoints = [
                {x: 100, label: 'P1,P2开始', color: '#4ecdc4'},
                {x: 250, label: 'P1结束\nV(S1)V(S2)', color: '#ff6b6b'},
                {x: 350, label: 'P2结束\nV(S3)V(S4)', color: '#ff6b6b'},
                {x: 450, label: 'P3开始\nP(S1)P(S3)', color: '#45b7d1'},
                {x: 550, label: 'P3结束\nV(S5)V(S6)', color: '#ff6b6b'},
                {x: 650, label: 'P4,P5开始\nP(S2)P(S5)\nP(S4)P(S6)', color: '#f093fb'}
            ];

            timePoints.forEach(point => {
                // 绘制时间点
                detailCtx.fillStyle = point.color;
                detailCtx.beginPath();
                detailCtx.arc(point.x, 100, 8, 0, 2 * Math.PI);
                detailCtx.fill();

                // 绘制标签
                detailCtx.fillStyle = '#333';
                detailCtx.font = '12px Arial';
                detailCtx.textAlign = 'center';
                const lines = point.label.split('\n');
                lines.forEach((line, index) => {
                    detailCtx.fillText(line, point.x, 130 + index * 15);
                });
            });

            // 绘制信号量状态变化
            detailCtx.fillStyle = '#667eea';
            detailCtx.font = 'bold 16px Arial';
            detailCtx.fillText('信号量变化过程：', 400, 220);

            const semaphoreSteps = [
                '初始：S1=S2=S3=S4=S5=S6=0',
                'P1结束：S1=1, S2=1',
                'P2结束：S3=1, S4=1',
                'P3开始：S1=0, S3=0 (执行P操作)',
                'P3结束：S5=1, S6=1',
                'P4,P5开始：S2=0, S5=0, S4=0, S6=0'
            ];

            detailCtx.fillStyle = '#333';
            detailCtx.font = '14px Arial';
            detailCtx.textAlign = 'left';
            semaphoreSteps.forEach((step, index) => {
                detailCtx.fillText(step, 50, 250 + index * 25);
            });

            // 关键提示
            detailCtx.fillStyle = '#ff6b6b';
            detailCtx.font = 'bold 16px Arial';
            detailCtx.textAlign = 'center';
            detailCtx.fillText('关键：V操作释放资源，P操作等待资源！', 400, 420);
        }

        // 重置详细分析
        function resetDetailAnalysis() {
            drawDetailAnalysis();
        }

        // 页面加载完成后初始化
        window.onload = function() {
            init();
        };
    </script>
</body>
</html>
