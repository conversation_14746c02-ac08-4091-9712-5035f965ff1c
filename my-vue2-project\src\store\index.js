import Vue from 'vue'
import Vuex from 'vuex'
import noteApi from '../services/api'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    darkMode: false,
    notes: [],
    loading: false,
    error: null,
    readingHistory: [],
    isMonitoringMode: false // 学习监控模式状态
  },
  getters: {
    getNoteById: (state) => (id) => {
      return state.notes.find(note => note.id === parseInt(id))
    },
    getReadingHistory: (state) => {
      return state.readingHistory
    }
  },
  mutations: {
    toggleDarkMode(state) {
      state.darkMode = !state.darkMode
      // 保存到本地存储
      localStorage.setItem('darkMode', state.darkMode)
      // 更新文档根元素的类
      if (state.darkMode) {
        document.documentElement.classList.add('dark-mode')
      } else {
        document.documentElement.classList.remove('dark-mode')
      }
    },
    initDarkMode(state) {
      // 从本地存储中获取暗黑模式状态
      const darkMode = localStorage.getItem('darkMode')
      if (darkMode !== null) {
        state.darkMode = darkMode === 'true'
        // 更新文档根元素的类
        if (state.darkMode) {
          document.documentElement.classList.add('dark-mode')
        } else {
          document.documentElement.classList.remove('dark-mode')
        }
      }
    },
    setNotes(state, notes) {
      state.notes = notes
    },
    addNoteToState(state, note) {
      state.notes.push(note)
    },
    updateNoteInState(state, updatedNote) {
      const index = state.notes.findIndex(n => n.id === updatedNote.id)
      if (index !== -1) {
        state.notes.splice(index, 1, updatedNote)
      }
    },
    deleteNoteFromState(state, id) {
      const index = state.notes.findIndex(n => n.id === id)
      if (index !== -1) {
        state.notes.splice(index, 1)
      }
    },
    setLoading(state, status) {
      state.loading = status
    },
    setError(state, error) {
      state.error = error
    },
    setMonitoringMode(state, isMonitoring) {
      state.isMonitoringMode = isMonitoring
      // 更新文档根元素的类，用于全局样式控制
      if (isMonitoring) {
        document.documentElement.classList.add('monitoring-mode')
      } else {
        document.documentElement.classList.remove('monitoring-mode')
      }
    },
    // 阅读历史相关的mutations
    setReadingHistory(state, history) {
      state.readingHistory = history
    },
    addToReadingHistory(state, note) {
      // 如果已经在历史记录中，先移除旧记录
      const existingIndex = state.readingHistory.findIndex(item => item.path === note.path)
      if (existingIndex !== -1) {
        state.readingHistory.splice(existingIndex, 1)
      }
      
      // 添加到历史记录的开头
      state.readingHistory.unshift({
        path: note.path,
        name: note.name,
        lastRead: note.lastRead || new Date().toISOString()
      })
      
      // 保存到localStorage
      localStorage.setItem('readingHistory', JSON.stringify(state.readingHistory))
    },
    clearReadingHistory(state) {
      state.readingHistory = []
      localStorage.removeItem('readingHistory')
    }
  },
  actions: {
    toggleDarkMode({ commit }) {
      commit('toggleDarkMode')
    },
    initDarkMode({ commit }) {
      commit('initDarkMode')
    },
    async initApp({ dispatch }) {
      // 初始化应用
      await dispatch('initDarkMode')
      await dispatch('loadReadingHistory')
      await dispatch('fetchNotes')
    },
    async fetchNotes({ commit }) {
      commit('setLoading', true)
      commit('setError', null)
      
      try {
        const notes = await noteApi.getAllNotes()
        commit('setNotes', notes)
      } catch (error) {
        console.error('获取笔记失败:', error)
        commit('setError', '获取笔记失败，请稍后再试')
      } finally {
        commit('setLoading', false)
      }
    },
    async addNote({ commit }, note) {
      commit('setLoading', true)
      commit('setError', null)
      
      try {
        const newNote = await noteApi.createNote(note)
        commit('addNoteToState', newNote)
        return newNote
      } catch (error) {
        console.error('添加笔记失败:', error)
        commit('setError', '添加笔记失败，请稍后再试')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    async updateNote({ commit }, note) {
      commit('setLoading', true)
      commit('setError', null)
      
      try {
        const updatedNote = await noteApi.updateNote(note.id, {
          title: note.title,
          content: note.content
        })
        commit('updateNoteInState', updatedNote)
        return updatedNote
      } catch (error) {
        console.error('更新笔记失败:', error)
        commit('setError', '更新笔记失败，请稍后再试')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    async deleteNote({ commit }, id) {
      commit('setLoading', true)
      commit('setError', null)
      
      try {
        await noteApi.deleteNote(id)
        commit('deleteNoteFromState', id)
        return true
      } catch (error) {
        console.error('删除笔记失败:', error)
        commit('setError', '删除笔记失败，请稍后再试')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    // 阅读历史相关的actions
    loadReadingHistory({ commit }) {
      try {
        const historyJson = localStorage.getItem('readingHistory')
        if (historyJson) {
          const history = JSON.parse(historyJson)
          commit('setReadingHistory', history)
        }
      } catch (error) {
        console.error('加载阅读历史失败:', error)
        // 如果解析失败，清除损坏的数据
        localStorage.removeItem('readingHistory')
      }
    },
    addReadingHistory({ commit }, note) {
      commit('addToReadingHistory', note)
    },
    clearReadingHistory({ commit }) {
      commit('clearReadingHistory')
    }
  }
}) 