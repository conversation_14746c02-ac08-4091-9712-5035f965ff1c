<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件质量属性与架构策略 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .scenario-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .scenario-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .scenario-item {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .scenario-item h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .scenario-item p {
            color: #555;
            line-height: 1.6;
            font-size: 1.1rem;
        }

        .demo-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .demo-title {
            font-size: 1.8rem;
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        #qualityCanvas {
            border: 2px solid #eee;
            border-radius: 15px;
            background: #fafafa;
            cursor: pointer;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .btn.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .quiz-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .quiz-item {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #eee;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .quiz-item:hover {
            border-color: #667eea;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .quiz-question {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            border: none;
        }

        .quiz-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .quiz-option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 0.6s ease-in-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            animation: shake 0.5s ease-in-out;
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            display: none;
        }

        .explanation h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .highlight {
            background: rgba(255, 255, 0, 0.3);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        .strategy-list {
            background: rgba(255,255,255,0.9);
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .strategy-list h5 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .strategy-list ul {
            list-style: none;
            padding: 0;
        }

        .strategy-list li {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            margin: 5px 0;
            border-radius: 20px;
            display: inline-block;
            margin-right: 10px;
            font-size: 0.9rem;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .progress-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .progress-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .progress-dot.active {
            background: #4facfe;
            transform: scale(1.2);
        }

        .progress-dot.completed {
            background: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件质量属性与架构策略</h1>
            <p class="subtitle">理解网上商城系统的质量属性需求</p>
        </div>

        <div class="scenario-card">
            <h2 class="scenario-title">🏪 网上商城系统质量属性场景</h2>
            
            <div class="scenario-item">
                <h4>🔄 场景1：系统可用性</h4>
                <p>"系统主站断电后，能够在2分钟内自动切换到备用站点，并恢复正常运行"</p>
            </div>
            
            <div class="scenario-item">
                <h4>⚡ 场景2：系统性能</h4>
                <p>"在并发用户数不超过1000人时，用户的交易请求应该在0.5s内完成"</p>
            </div>
            
            <div class="scenario-item">
                <h4>🛡️ 场景3：系统安全性</h4>
                <p>"系统应该能够抵挡恶意用户的入侵行为，并进行报警和记录"</p>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">🎯 质量属性可视化演示</h2>
            <div class="canvas-container">
                <canvas id="qualityCanvas" width="1000" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showQuality('availability')">🔄 可用性</button>
                <button class="btn" onclick="showQuality('performance')">⚡ 性能</button>
                <button class="btn" onclick="showQuality('security')">🛡️ 安全性</button>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="demo-title">📝 互动练习</h2>
            
            <div class="progress-indicator">
                <div class="progress-dot" id="dot1"></div>
                <div class="progress-dot" id="dot2"></div>
                <div class="progress-dot" id="dot3"></div>
            </div>

            <div class="quiz-item" id="quiz1">
                <div class="quiz-question">
                    场景1："系统主站断电后，能够在2分钟内自动切换到备用站点"主要与（ ）质量属性相关？
                </div>
                <div class="quiz-options">
                    <button class="quiz-option" onclick="selectAnswer(1, 'A', '性能')">A. 性能</button>
                    <button class="quiz-option" onclick="selectAnswer(1, 'B', '可用性')">B. 可用性</button>
                    <button class="quiz-option" onclick="selectAnswer(1, 'C', '易用性')">C. 易用性</button>
                    <button class="quiz-option" onclick="selectAnswer(1, 'D', '可修改性')">D. 可修改性</button>
                </div>
                <div class="explanation" id="explanation1"></div>
            </div>

            <div class="quiz-item" id="quiz2" style="display: none;">
                <div class="quiz-question">
                    场景2："用户的交易请求应该在0.5s内完成"主要与（ ）质量属性相关？
                </div>
                <div class="quiz-options">
                    <button class="quiz-option" onclick="selectAnswer(2, 'A', '性能')">A. 性能</button>
                    <button class="quiz-option" onclick="selectAnswer(2, 'B', '可用性')">B. 可用性</button>
                    <button class="quiz-option" onclick="selectAnswer(2, 'C', '安全性')">C. 安全性</button>
                    <button class="quiz-option" onclick="selectAnswer(2, 'D', '可修改性')">D. 可修改性</button>
                </div>
                <div class="explanation" id="explanation2"></div>
            </div>

            <div class="quiz-item" id="quiz3" style="display: none;">
                <div class="quiz-question">
                    场景3："抵挡恶意用户的入侵行为，并进行报警和记录"主要与（ ）质量属性相关？
                </div>
                <div class="quiz-options">
                    <button class="quiz-option" onclick="selectAnswer(3, 'A', '性能')">A. 性能</button>
                    <button class="quiz-option" onclick="selectAnswer(3, 'B', '可用性')">B. 可用性</button>
                    <button class="quiz-option" onclick="selectAnswer(3, 'C', '安全性')">C. 安全性</button>
                    <button class="quiz-option" onclick="selectAnswer(3, 'D', '易用性')">D. 易用性</button>
                </div>
                <div class="explanation" id="explanation3"></div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('qualityCanvas');
        const ctx = canvas.getContext('2d');
        let currentQuiz = 1;
        const answers = {1: null, 2: null, 3: null};
        const correctAnswers = {1: 'B', 2: 'A', 3: 'C'};

        function showQuality(type) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 移除所有按钮的active状态
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            
            switch(type) {
                case 'availability':
                    drawAvailability();
                    document.querySelector('.btn').classList.add('active');
                    break;
                case 'performance':
                    drawPerformance();
                    document.querySelectorAll('.btn')[1].classList.add('active');
                    break;
                case 'security':
                    drawSecurity();
                    document.querySelectorAll('.btn')[2].classList.add('active');
                    break;
            }
        }

        function drawAvailability() {
            // 主站
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(100, 200, 150, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主站', 175, 240);
            ctx.fillText('(断电)', 175, 260);
            
            // 备用站
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(400, 200, 150, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('备用站', 475, 240);
            ctx.fillText('(运行中)', 475, 260);
            
            // 用户
            ctx.fillStyle = '#3498db';
            ctx.fillRect(750, 200, 150, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('用户', 825, 250);
            
            // 切换箭头
            drawArrow(250, 250, 400, 250, '#f39c12', '2分钟内切换');
            drawArrow(550, 250, 750, 250, '#27ae60', '恢复服务');
            
            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('可用性：系统故障恢复能力', 500, 50);
            
            // 策略
            ctx.font = '14px Arial';
            ctx.fillText('架构策略：心跳检测、主动冗余、故障转移', 500, 400);
        }

        function drawPerformance() {
            // 服务器集群
            for(let i = 0; i < 3; i++) {
                ctx.fillStyle = '#9b59b6';
                ctx.fillRect(200 + i * 80, 150, 60, 80);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('服务器', 230 + i * 80, 185);
                ctx.fillText(i + 1, 230 + i * 80, 200);
            }
            
            // 负载均衡器
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(450, 170, 100, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('负载均衡', 500, 195);
            
            // 用户请求
            for(let i = 0; i < 5; i++) {
                ctx.fillStyle = '#3498db';
                ctx.fillRect(700, 120 + i * 25, 80, 20);
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.fillText('用户' + (i + 1), 740, 133 + i * 25);
            }
            
            // 响应时间指示
            ctx.fillStyle = '#27ae60';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('< 0.5秒响应', 500, 300);
            
            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('性能：响应时间和吞吐量', 500, 50);
            
            // 策略
            ctx.font = '14px Arial';
            ctx.fillText('架构策略：负载均衡、缓存、并发处理、资源调度', 500, 400);
        }

        function drawSecurity() {
            // 防火墙
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(150, 150, 100, 200);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('防火墙', 200, 250);
            
            // 应用服务器
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(400, 200, 150, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('应用服务器', 475, 240);
            ctx.fillText('(受保护)', 475, 260);
            
            // 入侵检测
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(700, 150, 120, 80);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('入侵检测', 760, 185);
            ctx.fillText('& 审计', 760, 200);
            
            // 恶意用户
            ctx.fillStyle = '#8e44ad';
            ctx.fillRect(50, 50, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('恶意用户', 90, 85);
            
            // 阻挡箭头
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(130, 80);
            ctx.lineTo(150, 180);
            ctx.stroke();
            
            // X标记
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 6;
            ctx.beginPath();
            ctx.moveTo(135, 120);
            ctx.lineTo(155, 140);
            ctx.moveTo(155, 120);
            ctx.lineTo(135, 140);
            ctx.stroke();
            
            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('安全性：抵御攻击和入侵', 500, 50);
            
            // 策略
            ctx.font = '14px Arial';
            ctx.fillText('架构策略：认证授权、入侵检测、审计追踪、加密传输', 500, 400);
        }

        function drawArrow(fromX, fromY, toX, toY, color, label) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI/6), toY - 15 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI/6), toY - 15 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
            
            // 标签
            if (label) {
                ctx.fillStyle = color;
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(label, (fromX + toX) / 2, (fromY + toY) / 2 - 10);
            }
        }

        function selectAnswer(quizNum, option, text) {
            // 移除当前题目所有选项的状态
            const currentQuizElement = document.getElementById(`quiz${quizNum}`);
            currentQuizElement.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });
            
            const explanation = document.getElementById(`explanation${quizNum}`);
            const isCorrect = option === correctAnswers[quizNum];
            
            // 设置选中选项的状态
            event.target.classList.add(isCorrect ? 'correct' : 'wrong');
            
            answers[quizNum] = option;
            
            // 更新进度指示器
            const dot = document.getElementById(`dot${quizNum}`);
            dot.classList.add(isCorrect ? 'completed' : 'active');
            
            // 显示解释
            explanation.style.display = 'block';
            explanation.innerHTML = getExplanation(quizNum, isCorrect, option);
            
            // 如果答对了，延迟显示下一题
            if (isCorrect && quizNum < 3) {
                setTimeout(() => {
                    document.getElementById(`quiz${quizNum + 1}`).style.display = 'block';
                    currentQuiz = quizNum + 1;
                }, 2000);
            }
        }

        function getExplanation(quizNum, isCorrect, selectedOption) {
            const explanations = {
                1: {
                    correct: `
                        <h4>🎉 正确！可用性是关键</h4>
                        <p><span class="highlight">可用性</span>关注系统在故障情况下的恢复能力和持续服务能力。</p>
                        <div class="strategy-list">
                            <h5>🔧 可用性架构策略：</h5>
                            <ul>
                                <li>心跳检测</li>
                                <li>Ping/Echo</li>
                                <li>主动冗余</li>
                                <li>被动冗余</li>
                                <li>故障转移</li>
                                <li>选举机制</li>
                            </ul>
                        </div>
                    `,
                    wrong: `
                        <h4>❌ 不正确，正确答案是B：可用性</h4>
                        <p>"2分钟内自动切换到备用站点"明确体现了<span class="highlight">系统故障恢复</span>的需求，这是可用性的核心特征。</p>
                        <p>可用性 = 系统正常运行时间 / 总时间</p>
                    `
                },
                2: {
                    correct: `
                        <h4>🎉 正确！性能是核心</h4>
                        <p><span class="highlight">性能</span>关注系统的响应时间、吞吐量和资源利用率。</p>
                        <div class="strategy-list">
                            <h5>⚡ 性能架构策略：</h5>
                            <ul>
                                <li>增加计算资源</li>
                                <li>减少计算开销</li>
                                <li>引入并发机制</li>
                                <li>负载均衡</li>
                                <li>缓存策略</li>
                                <li>资源调度</li>
                            </ul>
                        </div>
                    `,
                    wrong: `
                        <h4>❌ 不正确，正确答案是A：性能</h4>
                        <p>"0.5s内完成"和"1000并发用户"都是<span class="highlight">性能指标</span>，关注的是系统的响应速度和处理能力。</p>
                    `
                },
                3: {
                    correct: `
                        <h4>🎉 正确！安全性至关重要</h4>
                        <p><span class="highlight">安全性</span>关注系统抵御攻击、保护数据和追踪审计的能力。</p>
                        <div class="strategy-list">
                            <h5>🛡️ 安全性架构策略：</h5>
                            <ul>
                                <li>入侵检测</li>
                                <li>用户认证</li>
                                <li>用户授权</li>
                                <li>追踪审计</li>
                                <li>加密传输</li>
                                <li>访问控制</li>
                            </ul>
                        </div>
                    `,
                    wrong: `
                        <h4>❌ 不正确，正确答案是C：安全性</h4>
                        <p>"抵挡恶意用户入侵"和"报警记录"都是<span class="highlight">安全防护</span>的典型需求。</p>
                    `
                }
            };
            
            return explanations[quizNum][isCorrect ? 'correct' : 'wrong'];
        }

        // 初始化
        document.getElementById('dot1').classList.add('active');
        showQuality('availability');
    </script>
</body>
</html>
