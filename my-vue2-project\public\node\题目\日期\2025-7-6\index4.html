<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构件组装知识解析与演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-section, .explanation-section, .canvas-section {
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .question-section:last-child, .explanation-section:last-child, .canvas-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .question-text {
            font-size: 1.25em;
            margin-bottom: 20px;
            font-weight: bold;
            color: #34495e;
        }
        .options label {
            display: block;
            background-color: #ecf0f1;
            padding: 15px 20px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            font-size: 1.1em;
            display: flex;
            align-items: center;
        }
        .options label:hover {
            background-color: #dfe6e9;
            transform: translateY(-2px);
        }
        .options input[type="radio"] {
            margin-right: 15px;
            transform: scale(1.2);
        }
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            display: none; /* Hidden by default */
        }
        .feedback.correct {
            background-color: #e6ffe6;
            color: #28a745;
            border: 1px solid #28a745;
        }
        .feedback.incorrect {
            background-color: #ffe6e6;
            color: #dc3545;
            border: 1px solid #dc3545;
        }
        .knowledge-explanation p {
            font-size: 1.1em;
            margin-bottom: 15px;
            text-indent: 2em;
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        .controls button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        .controls button:hover {
            background-color: #2980b9;
            transform: translateY(-1px);
        }
        .controls button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .section-title {
            font-size: 1.5em;
            color: #34495e;
            margin-bottom: 15px;
            border-left: 5px solid #3498db;
            padding-left: 10px;
        }
        .highlight {
            color: #e74c3c;
            font-weight: bold;
        }
        footer {
            margin-top: 40px;
            font-size: 0.9em;
            color: #777;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>构件组装知识解析与演示</h1>

        <div class="question-section">
            <div class="section-title">题目回顾</div>
            <div class="question-text">
                问题 1: 构件组装是指构件相互直接集成或是用"胶水代码"将其整合在一起，创造一个系统或另一个构件的过程。其中，构件组装常见的方式不包括（）组装。
            </div>
            <div class="options">
                <label>
                    <input type="radio" name="q1" value="A"> A. 层次 (Hierarchy)
                </label>
                <label>
                    <input type="radio" name="q1" value="B"> B. 叠加 (Overlay)
                </label>
                <label>
                    <input type="radio" name="q1" value="C"> C. 顺序 (Sequence)
                </label>
                <label>
                    <input type="radio" name="q1" value="D"> D. 循环 (Loop)
                </label>
            </div>
            <div class="feedback" id="feedback"></div>
        </div>

        <div class="explanation-section">
            <div class="section-title">知识点解释</div>
            <div class="knowledge-explanation">
                <p>在软件工程中，<span class="highlight">构件组装</span>（Component Assembly）是将独立开发的功能模块（构件）组合起来，形成一个更大、更完整的系统或新构件的过程。这就像搭积木一样，将不同的积木（构件）按照一定的规则组合起来，形成一个完整的模型。</p>
                <p>构件组装的目的是提高软件的<span class="highlight">重用性</span>、<span class="highlight">可维护性</span>和<span class="highlight">开发效率</span>。通过使用已经测试和验证过的构件，可以大大缩短开发周期并减少错误。</p>
                <p>常见的构件组装方式有以下几种：</p>
                <ul>
                    <li><p><span class="highlight">层次组装（Hierarchy）</span>：构件按照依赖关系形成一个层次结构，上层构件依赖于下层构件，但下层构件不依赖上层构件。这就像一个组织架构，总经理依赖部门经理，部门经理依赖员工，但员工不依赖总经理。这种方式清晰、稳定，便于管理和理解。</p></li>
                    <li><p><span class="highlight">叠加组装（Overlay）</span>：多个构件在功能上相互补充或增强，形成更复杂的功能。它们可能并存，共同完成任务。例如，在一个图像处理软件中，可以叠加不同的滤镜效果。</p></li>
                    <li><p><span class="highlight">顺序组装（Sequence）</span>：构件按照特定的流程或步骤依次执行，前一个构件的输出作为后一个构件的输入。这就像一个生产流水线，每个工序（构件）都按照严格的顺序完成。</p></li>
                </ul>
                <p>而<span class="highlight">循环组装（Loop）</span>，通常指的是构件之间形成循环依赖，即A依赖B，B依赖C，C又依赖A的情况。在构件组装中，<span class="highlight">循环依赖是非常危险和应尽量避免的</span>。因为它会导致以下问题：</p>
                <ul>
                    <li><p><b>难以理解和维护</b>：循环依赖使得代码的流向变得模糊，很难追踪数据的来源和去向。</p></li>
                    <li><p><b>耦合度高</b>：构件之间紧密耦合，修改一个构件可能会影响到循环中的所有其他构件，增加了修改的风险。</p></li>
                    <li><p><b>测试困难</b>：难以独立测试单个构件，通常需要将整个循环依赖的构件集一起测试。</p></li>
                    <li><p><b>部署和更新复杂</b>：部署或更新任何一个构件都可能需要同时更新循环中的所有构件，增加了复杂性。</p></li>
                </ul>
                <p>因此，在健康的软件架构中，我们通常会避免循环依赖，这也是为什么题目中说"循环"不属于构件组装的常见方式。</p>
            </div>
        </div>

        <div class="canvas-section">
            <div class="section-title">动画与交互演示</div>
            <p>点击下方按钮，观看不同构件组装方式的动画演示，帮助您直观理解。</p>
            <canvas id="assemblyCanvas" width="800" height="450"></canvas>
            <div class="controls">
                <button id="hierarchyBtn">层次组装</button>
                <button id="overlayBtn">叠加组装</button>
                <button id="sequenceBtn">顺序组装</button>
                <button id="loopBtn">循环组装 (避免)</button>
                <button id="resetBtn">重置</button>
            </div>
        </div>
    </div>

    <footer>
        <p>&copy; 2023 构件组装知识解析</p>
    </footer>

    <script>
        const feedbackDiv = document.getElementById('feedback');
        const options = document.querySelectorAll('input[name="q1"]');
        const canvas = document.getElementById('assemblyCanvas');
        const ctx = canvas.getContext('2d');
        const hierarchyBtn = document.getElementById('hierarchyBtn');
        const overlayBtn = document.getElementById('overlayBtn');
        const sequenceBtn = document.getElementById('sequenceBtn');
        const loopBtn = document.getElementById('loopBtn');
        const resetBtn = document.getElementById('resetBtn');

        // --- 题目交互逻辑 ---
        options.forEach(option => {
            option.addEventListener('change', function() {
                if (this.value === 'D') {
                    feedbackDiv.textContent = '恭喜您，回答正确！';
                    feedbackDiv.className = 'feedback correct';
                } else {
                    feedbackDiv.textContent = '很抱歉，回答错误。正确答案是 D。';
                    feedbackDiv.className = 'feedback incorrect';
                }
                feedbackDiv.style.display = 'block';
                // 禁用所有选项，防止重复选择
                options.forEach(opt => opt.disabled = true);
            });
        });

        // --- Canvas 动画逻辑 ---
        let animationFrameId;
        const componentSize = 80;
        const padding = 20;
        const colors = ['#f39c12', '#2ecc71', '#9b59b6', '#3498db', '#e74c3c'];

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
        }

        function drawComponent(x, y, text, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, componentSize, componentSize);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, componentSize, componentSize);
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + componentSize / 2, y + componentSize / 2);
        }

        function drawArrow(startX, startY, endX, endY, color = '#333') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // Arrowhead
            const headlen = 10; // length of head in pixels
            const angle = Math.atan2(endY - startY, endX - startX);
            ctx.lineTo(endX - headlen * Math.cos(angle - Math.PI / 6), endY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX - headlen * Math.cos(angle + Math.PI / 6), endY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        function animateAssembly(type) {
            clearCanvas();
            let components = [];

            if (type === 'hierarchy') {
                // Component C (bottom)
                components.push({ x: canvas.width / 2 - componentSize / 2, y: canvas.height - componentSize - padding, text: '构件 C', color: colors[2] });
                // Component B (middle, depends on C)
                components.push({ x: canvas.width / 2 - componentSize / 2 - componentSize - padding, y: canvas.height - 2 * componentSize - 2 * padding, text: '构件 B', color: colors[1] });
                // Component A (top, depends on B)
                components.push({ x: canvas.width / 2 - componentSize / 2 + componentSize + padding, y: canvas.height - 2 * componentSize - 2 * padding, text: '构件 A', color: colors[0] });

                const draw = (progress) => {
                    clearCanvas();
                    components.forEach(comp => drawComponent(comp.x, comp.y, comp.text, comp.color));

                    // Draw dependencies with animation
                    if (progress > 0.3) { // B -> C
                        const startX = components[1].x + componentSize / 2;
                        const startY = components[1].y + componentSize;
                        const endX = components[0].x + componentSize / 2;
                        const endY = components[0].y;
                        drawArrow(startX, startY, startX + (endX - startX) * (progress - 0.3) / 0.3, startY + (endY - startY) * (progress - 0.3) / 0.3, colors[1]);
                    }
                    if (progress > 0.6) { // A -> C
                        const startX = components[2].x + componentSize / 2;
                        const startY = components[2].y + componentSize;
                        const endX = components[0].x + componentSize / 2;
                        const endY = components[0].y;
                        drawArrow(startX, startY, startX + (endX - startX) * (progress - 0.6) / 0.3, startY + (endY - startY) * (progress - 0.6) / 0.3, colors[0]);
                    }
                    if (progress > 0.9) { // A -> B
                        const startX = components[2].x + componentSize / 2;
                        const startY = components[2].y + componentSize;
                        const endX = components[1].x + componentSize / 2;
                        const endY = components[1].y;
                        drawArrow(startX, startY, startX + (endX - startX) * (progress - 0.9) / 0.1, startY + (endY - startY) * (progress - 0.9) / 0.1, colors[0]);
                    }


                    ctx.fillStyle = '#333';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('层次组装：下层构件被上层构件依赖，形成清晰的结构。', canvas.width / 2, 30);
                };
                animate(draw, 3000); // 3 seconds animation
            } else if (type === 'overlay') {
                components.push({ x: canvas.width / 2 - componentSize - padding / 2, y: canvas.height / 2 - componentSize / 2, text: '构件 A', color: colors[0] });
                components.push({ x: canvas.width / 2 + padding / 2, y: canvas.height / 2 - componentSize / 2, text: '构件 B', color: colors[1] });
                // Combined effect / new component
                const combinedX = canvas.width / 2 - componentSize / 2;
                const combinedY = canvas.height / 2 - componentSize / 2 - componentSize - padding * 2;
                components.push({ x: combinedX, y: combinedY, text: '组合功能', color: colors[3], isCombined: true });


                const draw = (progress) => {
                    clearCanvas();
                    drawComponent(components[0].x, components[0].y, components[0].text, components[0].color);
                    drawComponent(components[1].x, components[1].y, components[1].text, components[1].color);

                    if (progress > 0.5) {
                        ctx.fillStyle = colors[3];
                        const currentSize = componentSize * (progress - 0.5) * 2;
                        ctx.globalAlpha = (progress - 0.5) * 2;
                        ctx.fillRect(combinedX, combinedY, currentSize, currentSize);
                        ctx.globalAlpha = 1;

                        ctx.strokeStyle = '#333';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(combinedX, combinedY, currentSize, currentSize);
                        ctx.fillStyle = 'white';
                        ctx.font = '20px Arial';
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        if (currentSize > 60) {
                            ctx.fillText('组合功能', combinedX + currentSize / 2, combinedY + currentSize / 2);
                        }

                        drawArrow(components[0].x + componentSize / 2, components[0].y, combinedX + componentSize / 4, combinedY + componentSize, colors[0]);
                        drawArrow(components[1].x + componentSize / 2, components[1].y, combinedX + 3 * componentSize / 4, combinedY + componentSize, colors[1]);
                    }

                    ctx.fillStyle = '#333';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('叠加组装：多个构件共同作用，形成更复杂的功能。', canvas.width / 2, 30);
                };
                animate(draw, 3000);
            } else if (type === 'sequence') {
                const startY = canvas.height / 2 - componentSize / 2;
                components.push({ x: padding, y: startY, text: '构件 A', color: colors[0] });
                components.push({ x: padding + componentSize + 50, y: startY, text: '构件 B', color: colors[1] });
                components.push({ x: padding + 2 * (componentSize + 50), y: startY, text: '构件 C', color: colors[2] });

                const draw = (progress) => {
                    clearCanvas();
                    components.forEach(comp => drawComponent(comp.x, comp.y, comp.text, comp.color));

                    if (progress > 0.3) { // A -> B
                        const startX = components[0].x + componentSize;
                        const startY = components[0].y + componentSize / 2;
                        const endX = components[1].x;
                        const endY = components[1].y + componentSize / 2;
                        drawArrow(startX, startY, startX + (endX - startX) * (progress - 0.3) / 0.3, startY + (endY - startY) * (progress - 0.3) / 0.3, colors[0]);
                    }
                    if (progress > 0.6) { // B -> C
                        const startX = components[1].x + componentSize;
                        const startY = components[1].y + componentSize / 2;
                        const endX = components[2].x;
                        const endY = components[2].y + componentSize / 2;
                        drawArrow(startX, startY, startX + (endX - startX) * (progress - 0.6) / 0.4, startY + (endY - startY) * (progress - 0.6) / 0.4, colors[1]);
                    }

                    ctx.fillStyle = '#333';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('顺序组装：构件按流程依次执行，前一构件输出是后一构件输入。', canvas.width / 2, 30);
                };
                animate(draw, 3000);
            } else if (type === 'loop') {
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = 100;
                const angleOffset = Math.PI / 2; // Start from top
                
                components.push({ text: '构件 A', color: colors[0] });
                components.push({ text: '构件 B', color: colors[1] });
                components.push({ text: '构件 C', color: colors[2] });

                // Calculate positions in a circle
                components.forEach((comp, i) => {
                    const angle = i * (2 * Math.PI / components.length) + angleOffset;
                    comp.x = centerX + radius * Math.cos(angle) - componentSize / 2;
                    comp.y = centerY + radius * Math.sin(angle) - componentSize / 2;
                });

                const draw = (progress) => {
                    clearCanvas();
                    components.forEach(comp => drawComponent(comp.x, comp.y, comp.text, comp.color));

                    // A -> B
                    if (progress > 0.1 && progress < 0.9) {
                        const start = components[0];
                        const end = components[1];
                        const cp1x = start.x + componentSize / 2 + 50;
                        const cp1y = start.y + componentSize / 2 - 50;
                        const cp2x = end.x + componentSize / 2 + 50;
                        const cp2y = end.y + componentSize / 2 - 50;
                        drawCurvedArrow(start.x + componentSize/2, start.y + componentSize/2, end.x + componentSize/2, end.y + componentSize/2, 0.4, colors[0]);
                    }

                    // B -> C
                    if (progress > 0.3 && progress < 1.0) {
                        const start = components[1];
                        const end = components[2];
                        drawCurvedArrow(start.x + componentSize/2, start.y + componentSize/2, end.x + componentSize/2, end.y + componentSize/2, 0.4, colors[1]);
                    }
                    
                    // C -> A (The problematic loop)
                    if (progress > 0.6) {
                        const start = components[2];
                        const end = components[0];
                        drawCurvedArrow(start.x + componentSize/2, start.y + componentSize/2, end.x + componentSize/2, end.y + componentSize/2, 0.4, colors[2]);
                    }

                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('循环组装：构件间形成循环依赖，应尽量避免！', canvas.width / 2, 30);
                };
                animate(draw, 4000); // Longer animation for emphasis
            }
        }

        // Helper for curved arrows (for loop visualization)
        function drawCurvedArrow(x1, y1, x2, y2, curveStrength, color = '#333') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);

            const midX = (x1 + x2) / 2;
            const midY = (y1 + y2) / 2;
            const dx = x2 - x1;
            const dy = y2 - y1;
            const dist = Math.sqrt(dx * dx + dy * dy);
            const normalX = -dy / dist;
            const normalY = dx / dist;

            const controlX = midX + normalX * dist * curveStrength;
            const controlY = midY + normalY * dist * curveStrength;

            ctx.quadraticCurveTo(controlX, controlY, x2, y2);
            ctx.stroke();

            // Arrowhead
            const headlen = 10;
            const angle = Math.atan2(y2 - controlY, x2 - controlX); // Angle from control point to end point
            ctx.lineTo(x2 - headlen * Math.cos(angle - Math.PI / 6), y2 - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - headlen * Math.cos(angle + Math.PI / 6), y2 - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        function animate(drawFunc, duration) {
            let start = null;

            function step(timestamp) {
                if (!start) start = timestamp;
                const progress = (timestamp - start) / duration;
                if (progress < 1) {
                    drawFunc(progress);
                    animationFrameId = requestAnimationFrame(step);
                } else {
                    drawFunc(1); // Ensure final state is drawn
                }
            }
            animationFrameId = requestAnimationFrame(step);
        }

        // --- 事件监听 ---
        hierarchyBtn.addEventListener('click', () => animateAssembly('hierarchy'));
        overlayBtn.addEventListener('click', () => animateAssembly('overlay'));
        sequenceBtn.addEventListener('click', () => animateAssembly('sequence'));
        loopBtn.addEventListener('click', () => animateAssembly('loop'));
        resetBtn.addEventListener('click', clearCanvas);

        // Initial clear
        clearCanvas();
    </script>
</body>
</html> 