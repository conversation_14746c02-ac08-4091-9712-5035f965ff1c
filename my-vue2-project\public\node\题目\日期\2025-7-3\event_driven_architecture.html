<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事件驱动架构风格解析</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h1, h2 {
            color: #2c3e50;
            text-align: center;
        }
        
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .explanation {
            margin-bottom: 20px;
        }
        
        #canvas-container {
            position: relative;
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
            background-color: #f9f9f9;
        }
        
        canvas {
            display: block;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        
        .step {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background-color: #ddd;
            margin: 0 5px;
            cursor: pointer;
        }
        
        .step.active {
            background-color: #3498db;
        }
        
        .info-box {
            position: absolute;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            max-width: 200px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Windows图形用户界面的事件驱动架构</h1>
        
        <div class="explanation">
            <p><strong>事件驱动架构</strong>是Windows操作系统在图形用户界面处理方面采用的核心架构风格。在这种架构中，系统会注册事件处理的回调函数，当特定事件发生时（如鼠标点击、键盘输入），系统会自动调用相应的处理函数。</p>
        </div>
        
        <div id="canvas-container">
            <canvas id="demoCanvas" width="1000" height="400"></canvas>
            <div class="info-box" id="infoBox"></div>
        </div>
        
        <div class="step-indicator">
            <div class="step active" data-step="1"></div>
            <div class="step" data-step="2"></div>
            <div class="step" data-step="3"></div>
            <div class="step" data-step="4"></div>
        </div>
        
        <div class="controls">
            <button id="prevBtn">上一步</button>
            <button id="nextBtn">下一步</button>
            <button id="interactBtn">交互演示</button>
        </div>
    </div>
    
    <div class="container">
        <h2>事件驱动架构的特点</h2>
        <ul>
            <li><strong>响应式处理</strong>：系统不会主动执行操作，而是等待用户事件触发后再响应</li>
            <li><strong>松耦合设计</strong>：事件源和事件处理逻辑相互独立，便于维护和扩展</li>
            <li><strong>异步执行</strong>：事件处理可以异步进行，不阻塞主程序流程</li>
            <li><strong>事件队列</strong>：系统维护一个事件队列，按顺序处理各种事件</li>
        </ul>
    </div>

    <script>
        // 获取Canvas元素和上下文
        const canvas = document.getElementById('demoCanvas');
        const ctx = canvas.getContext('2d');
        const infoBox = document.getElementById('infoBox');
        
        // 定义颜色
        const colors = {
            background: '#f9f9f9',
            system: '#3498db',
            event: '#e74c3c',
            handler: '#2ecc71',
            text: '#2c3e50',
            highlight: '#f39c12'
        };
        
        // 当前步骤
        let currentStep = 1;
        const totalSteps = 4;
        
        // 演示模式
        let demoMode = true;
        let interactionMode = false;
        
        // 系统组件
        const system = {
            x: 150,
            y: 100,
            width: 200,
            height: 200,
            label: '操作系统',
            draw() {
                ctx.fillStyle = colors.system;
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(this.label, this.x + this.width/2, this.y + this.height/2);
            }
        };
        
        // 事件队列
        const eventQueue = {
            x: 400,
            y: 150,
            width: 200,
            height: 100,
            events: [],
            label: '事件队列',
            draw() {
                ctx.fillStyle = '#f1c40f';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(this.label, this.x + this.width/2, this.y + 25);
                
                // 绘制队列中的事件
                ctx.font = '14px Microsoft YaHei';
                for(let i = 0; i < this.events.length; i++) {
                    ctx.fillText(this.events[i], this.x + this.width/2, this.y + 50 + i * 20);
                }
            },
            addEvent(event) {
                this.events.push(event);
                if(this.events.length > 3) {
                    this.events.shift();
                }
            }
        };
        
        // 事件处理器
        const eventHandler = {
            x: 650,
            y: 100,
            width: 200,
            height: 200,
            label: '事件处理器',
            active: false,
            draw() {
                ctx.fillStyle = this.active ? colors.highlight : colors.handler;
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(this.label, this.x + this.width/2, this.y + this.height/2);
                
                if(this.active) {
                    ctx.fillText('处理中...', this.x + this.width/2, this.y + this.height/2 + 30);
                }
            }
        };
        
        // 用户界面元素
        const uiElements = {
            x: 150,
            y: 350,
            width: 700,
            height: 40,
            elements: [
                { label: '按钮', x: 200, width: 80, event: '点击事件' },
                { label: '文本框', x: 350, width: 100, event: '输入事件' },
                { label: '下拉菜单', x: 520, width: 120, event: '选择事件' },
                { label: '窗口', x: 700, width: 60, event: '调整大小事件' }
            ],
            draw() {
                ctx.fillStyle = '#95a5a6';
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('用户界面', this.x + 50, this.y + 25);
                
                // 绘制UI元素
                for(const el of this.elements) {
                    ctx.fillStyle = '#7f8c8d';
                    ctx.fillRect(el.x, this.y + 5, el.width, 30);
                    
                    ctx.fillStyle = 'white';
                    ctx.fillText(el.label, el.x + el.width/2, this.y + 25);
                }
            },
            checkClick(x, y) {
                if(y >= this.y && y <= this.y + this.height) {
                    for(const el of this.elements) {
                        if(x >= el.x && x <= el.x + el.width) {
                            return el;
                        }
                    }
                }
                return null;
            }
        };
        
        // 事件动画
        let eventAnimation = {
            active: false,
            x: 0,
            y: 0,
            targetX: 0,
            targetY: 0,
            speed: 5,
            event: '',
            start(startX, startY, endX, endY, eventType) {
                this.active = true;
                this.x = startX;
                this.y = startY;
                this.targetX = endX;
                this.targetY = endY;
                this.event = eventType;
            },
            update() {
                if(!this.active) return;
                
                const dx = this.targetX - this.x;
                const dy = this.targetY - this.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if(distance < this.speed) {
                    this.x = this.targetX;
                    this.y = this.targetY;
                    this.active = false;
                    
                    // 如果到达事件队列
                    if(this.targetX === eventQueue.x + eventQueue.width / 2) {
                        eventQueue.addEvent(this.event);
                        setTimeout(() => {
                            // 从队列到处理器
                            eventAnimation.start(
                                eventQueue.x + eventQueue.width / 2,
                                eventQueue.y + 50,
                                eventHandler.x + eventHandler.width / 2,
                                eventHandler.y + eventHandler.height / 2,
                                this.event
                            );
                        }, 500);
                    }
                    // 如果到达处理器
                    else if(this.targetX === eventHandler.x + eventHandler.width / 2) {
                        eventHandler.active = true;
                        setTimeout(() => {
                            eventHandler.active = false;
                        }, 1000);
                    }
                    
                    return;
                }
                
                const angle = Math.atan2(dy, dx);
                this.x += Math.cos(angle) * this.speed;
                this.y += Math.sin(angle) * this.speed;
            },
            draw() {
                if(!this.active) return;
                
                ctx.beginPath();
                ctx.arc(this.x, this.y, 10, 0, Math.PI * 2);
                ctx.fillStyle = colors.event;
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = '10px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('事件', this.x, this.y + 3);
            }
        };
        
        // 绘制场景
        function drawScene() {
            // 清空画布
            ctx.fillStyle = colors.background;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 根据当前步骤绘制
            if(currentStep >= 1) {
                system.draw();
            }
            
            if(currentStep >= 2) {
                uiElements.draw();
            }
            
            if(currentStep >= 3) {
                eventQueue.draw();
                eventHandler.draw();
                
                // 绘制连接线
                ctx.strokeStyle = '#95a5a6';
                ctx.lineWidth = 2;
                
                // 系统到事件队列
                ctx.beginPath();
                ctx.moveTo(system.x + system.width, system.y + system.height/2);
                ctx.lineTo(eventQueue.x, eventQueue.y + eventQueue.height/2);
                ctx.stroke();
                
                // 事件队列到处理器
                ctx.beginPath();
                ctx.moveTo(eventQueue.x + eventQueue.width, eventQueue.y + eventQueue.height/2);
                ctx.lineTo(eventHandler.x, eventHandler.y + eventHandler.height/2);
                ctx.stroke();
            }
            
            // 绘制事件动画
            eventAnimation.draw();
            
            // 绘制步骤说明
            ctx.fillStyle = colors.text;
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'left';
            
            let stepText = '';
            switch(currentStep) {
                case 1:
                    stepText = '步骤1: 操作系统提供事件处理框架';
                    break;
                case 2:
                    stepText = '步骤2: 用户与图形界面元素交互产生事件';
                    break;
                case 3:
                    stepText = '步骤3: 事件被放入事件队列等待处理';
                    break;
                case 4:
                    stepText = '步骤4: 系统调用相应的事件处理函数';
                    break;
            }
            
            ctx.fillText(stepText, 20, 30);
            
            // 交互模式提示
            if(interactionMode) {
                ctx.fillStyle = colors.highlight;
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('点击界面元素触发事件', canvas.width/2, 30);
            }
        }
        
        // 更新动画
        function update() {
            eventAnimation.update();
        }
        
        // 动画循环
        function animate() {
            update();
            drawScene();
            requestAnimationFrame(animate);
        }
        
        // 初始化
        function init() {
            animate();
            
            // 演示事件流程
            if(demoMode && currentStep === 4) {
                setTimeout(() => {
                    const element = uiElements.elements[0];
                    triggerEvent(element);
                }, 1000);
            }
        }
        
        // 触发事件
        function triggerEvent(element) {
            // 从UI元素到系统
            eventAnimation.start(
                element.x + element.width/2,
                uiElements.y,
                system.x + system.width/2,
                system.y + system.height/2,
                element.event
            );
            
            // 显示信息框
            showInfoBox(element.x + element.width/2, uiElements.y - 40, `触发了 ${element.event}`);
            
            // 从系统到事件队列
            setTimeout(() => {
                eventAnimation.start(
                    system.x + system.width/2,
                    system.y + system.height/2,
                    eventQueue.x + eventQueue.width/2,
                    eventQueue.y + 50,
                    element.event
                );
            }, 1000);
        }
        
        // 显示信息框
        function showInfoBox(x, y, text) {
            infoBox.style.left = x + 'px';
            infoBox.style.top = y + 'px';
            infoBox.textContent = text;
            infoBox.style.display = 'block';
            
            setTimeout(() => {
                infoBox.style.display = 'none';
            }, 2000);
        }
        
        // 事件监听
        document.getElementById('nextBtn').addEventListener('click', () => {
            if(currentStep < totalSteps) {
                currentStep++;
                updateStepIndicators();
                
                // 如果到了最后一步，自动演示
                if(currentStep === 4 && demoMode) {
                    setTimeout(() => {
                        const element = uiElements.elements[0];
                        triggerEvent(element);
                    }, 1000);
                }
            }
        });
        
        document.getElementById('prevBtn').addEventListener('click', () => {
            if(currentStep > 1) {
                currentStep--;
                updateStepIndicators();
            }
        });
        
        document.getElementById('interactBtn').addEventListener('click', () => {
            if(currentStep < 4) {
                currentStep = 4;
                updateStepIndicators();
            }
            
            interactionMode = !interactionMode;
            document.getElementById('interactBtn').textContent = interactionMode ? '退出交互' : '交互演示';
        });
        
        // 更新步骤指示器
        function updateStepIndicators() {
            const steps = document.querySelectorAll('.step');
            steps.forEach(step => {
                if(parseInt(step.dataset.step) <= currentStep) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });
        }
        
        // 点击步骤指示器
        document.querySelectorAll('.step').forEach(step => {
            step.addEventListener('click', () => {
                currentStep = parseInt(step.dataset.step);
                updateStepIndicators();
            });
        });
        
        // Canvas点击事件
        canvas.addEventListener('click', (e) => {
            if(!interactionMode || currentStep < 4) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const element = uiElements.checkClick(x, y);
            if(element) {
                triggerEvent(element);
            }
        });
        
        // 初始化
        init();
    </script>
</body>
</html> 