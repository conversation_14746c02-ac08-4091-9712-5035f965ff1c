<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Migrate</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #03A9F4; /* A sky blue for "migrate" */
            --secondary-color: #0277BD;
            --light-bg: #E1F5FE;
            --panel-bg: #ffffff;
            --text-color: #333;
            --text-muted: #7f8c8d;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--secondary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--text-muted);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #555;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #B3E5FC;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .breakdown-section .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            background: #fff;
        }

        #animation-canvas {
            width: 100%;
            height: calc(100% - 80px);
            border-radius: 15px;
        }
        
        .control-button {
            position: absolute;
            bottom: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(3, 169, 244, 0.5);
            z-index: 10;
        }
        .control-button:hover { background-color: #0288D1; transform: translateY(-2px); }
        .control-button.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>migrate</h1>
            <p class="pronunciation">[ˈmaɪɡreɪt]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 迁移, (候鸟等)迁徙, 移居</p>
                <div class="example">
                    <p><strong>例句：</strong> Many birds migrate to warmer countries in the winter.</p>
                    <p><strong>翻译：</strong> 很多鸟类在冬天会迁徙到更温暖的国家。</p>
                </div>
            </div>
            
            <div class="breakdown-section">
                <h3>核心概念活动 (Canvas)</h3>
                <div class="morpheme-list">
                     <button class="morpheme-btn" data-activity="follow-game">概念：追随领袖</button>
                     <button class="morpheme-btn" data-activity="obstacle-game">概念：克服障碍</button>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>完整单词活动 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示：候鸟迁徙</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="control-btn" class="control-button hidden">Migrate!</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const controlBtn = document.getElementById('control-btn');
        let animationFrameId;

        const panel = canvas.parentElement;
        canvas.width = panel.clientWidth;
        canvas.height = panel.clientHeight - 80;

        let flock = [];
        let mouse = { x: canvas.width / 2, y: canvas.height / 2 };
        let obstacles = [];
        let currentActivity = '';

        canvas.addEventListener('mousemove', e => {
            const rect = canvas.getBoundingClientRect();
            mouse.x = e.clientX - rect.left;
            mouse.y = e.clientY - rect.top;
        });
        
        class Boid {
            constructor(x, y) {
                this.position = { x, y };
                const angle = Math.random() * Math.PI * 2;
                this.velocity = { x: Math.cos(angle), y: Math.sin(angle) };
                this.acceleration = { x: 0, y: 0 };
                this.maxSpeed = 2;
                this.maxForce = 0.05;
                this.color = `hsl(${190 + Math.random() * 40}, 100%, 60%)`;
            }

            applyForce(force) {
                this.acceleration.x += force.x;
                this.acceleration.y += force.y;
            }

            // --- Boids algorithm main functions ---
            align(boids) { /* ... implementation ... */ return {x:0, y:0}; }
            cohesion(boids) { /* ... implementation ... */ return {x:0, y:0}; }
            separation(boids) { /* ... implementation ... */ return {x:0, y:0}; }

            flock(boids, target = null, obstacles = []) {
                // Simplified behavior for this demo
                let steer = {x: 0, y: 0};
                if(target){
                     let desired = { x: target.x - this.position.x, y: target.y - this.position.y };
                     steer = desired;
                }
                
                // Avoid obstacles
                obstacles.forEach(obs => {
                    let d = Math.hypot(this.position.x - obs.x, this.position.y - obs.y);
                    if (d < obs.radius + 20) {
                        let diff = { x: this.position.x - obs.x, y: this.position.y - obs.y };
                        // Normalize and steer away
                        diff.x /= d; diff.y /= d;
                        this.applyForce(diff);
                    }
                });

                this.applyForce(steer);
            }

            update() {
                this.velocity.x += this.acceleration.x;
                this.velocity.y += this.acceleration.y;
                // Limit speed
                const speed = Math.hypot(this.velocity.x, this.velocity.y);
                if (speed > this.maxSpeed) {
                    this.velocity.x = (this.velocity.x / speed) * this.maxSpeed;
                    this.velocity.y = (this.velocity.y / speed) * this.maxSpeed;
                }
                this.position.x += this.velocity.x;
                this.position.y += this.velocity.y;
                this.acceleration = { x: 0, y: 0 }; // Reset acceleration
            }
            
            draw() {
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.position.x, this.position.y, 4, 0, Math.PI * 2);
                ctx.fill();
            }
        }
        
        function welcomeScreen() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
            gradient.addColorStop(0, '#a5d6a7');
            gradient.addColorStop(1, '#81d4fa');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = '#333';
            ctx.font = '24px Roboto';
            ctx.textAlign = 'center';
            ctx.fillText('新篇章：第三个单词！', canvas.width / 2, canvas.height / 2 - 20);
            ctx.fillText('请选择一个活动来开始。', canvas.width / 2, canvas.height / 2 + 20);
            controlBtn.classList.add('hidden');
        }

        // --- Concept Games ---
        function initFollowGame() {
            flock = [];
            currentActivity = 'follow';
            controlBtn.textContent = '添加候鸟';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                for(let i=0; i<10; i++) flock.push(new Boid(Math.random()*50, Math.random()*canvas.height));
            };
            animate();
        }
        
        function initObstacleGame() {
            flock = [];
            currentActivity = 'obstacle';
            obstacles = [{x: canvas.width/2, y: canvas.height/2, radius: 80}];
            for(let i=0; i<50; i++) flock.push(new Boid(Math.random()*50, Math.random()*canvas.height));
            controlBtn.classList.add('hidden');
            animate();
        }

        function initFullAnimation() {
            flock = [];
            currentActivity = 'full';
            obstacles = [];
             for(let i=0; i<100; i++) flock.push(new Boid(Math.random()*50, Math.random()*canvas.height));
            controlBtn.classList.add('hidden');
            animate();
        }
        
        function animate() {
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
            gradient.addColorStop(0, '#a5d6a7');
            gradient.addColorStop(1, '#81d4fa');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            if(currentActivity === 'obstacle') {
                 ctx.fillStyle = '#607D8B';
                 ctx.beginPath();
                 ctx.arc(obstacles[0].x, obstacles[0].y, obstacles[0].radius, 0, Math.PI*2);
                 ctx.fill();
            }
            
            flock.forEach(boid => {
                let target = null;
                if (currentActivity === 'follow') target = mouse;
                if (currentActivity === 'full' || currentActivity === 'obstacle') target = {x: canvas.width - 50, y: boid.position.y};

                boid.flock(flock, target, obstacles);
                boid.update();
                boid.draw();
                 // Wrap around edges
                if (boid.position.x > canvas.width) boid.position.x = 0;
                if (boid.position.x < 0) boid.position.x = canvas.width;
                if (boid.position.y > canvas.height) boid.position.y = 0;
                if (boid.position.y < 0) boid.position.y = canvas.height;
            });
            
            animationFrameId = requestAnimationFrame(animate);
        }

        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                if (animationFrameId) cancelAnimationFrame(animationFrameId);
                const activity = btn.dataset.activity;
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                flock = [];
                obstacles = [];
                if (activity === 'follow-game') initFollowGame();
                else if (activity === 'obstacle-game') initObstacleGame();
                else if (activity === 'full-animation') initFullAnimation();
            });
        });

        welcomeScreen();
    });
    </script>
</body>
</html> 