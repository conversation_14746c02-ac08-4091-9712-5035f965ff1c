<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔记系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }
        h1 {
            text-align: center;
            color: #0056b3;
            margin-bottom: 20px;
        }
        .input-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #e9ecef;
        }
        .input-section label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        .input-section input[type="text"],
        .input-section textarea {
            width: calc(100% - 20px);
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .input-section button {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .input-section button:hover {
            background-color: #0056b3;
        }
        .notes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
        .note-box {
            background-color: #f9f9f9;
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            position: relative;
        }
        .note-box h3 {
            margin-top: 0;
            color: #333;
        }
        .note-box p {
            font-size: 0.9em;
            line-height: 1.5;
            color: #555;
        }
        .note-box .note-actions {
            margin-top: 10px;
            text-align: right;
        }
        .note-box .note-actions button {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.8em;
            margin-left: 5px;
        }
        .note-box .note-actions button.edit {
            background-color: #ffc107;
            color: #333;
        }
        .note-box .note-actions button:hover {
            opacity: 0.9;
        }

        .controls-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .controls-section label {
            font-weight: bold;
        }
        .controls-section select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        .controls-section button {
            background-color: #28a745;
            color: white;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .controls-section button:hover {
            background-color: #218838;
        }
        .note-box .note-selection {
            position: absolute;
            top: 10px;
            right: 10px;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>我的笔记系统</h1>

        <div class="input-section">
            <label for="noteTitle">笔记标题:</label>
            <input type="text" id="noteTitle" placeholder="输入笔记标题">
            <label for="noteContent">笔记内容:</label>
            <textarea id="noteContent" rows="5" placeholder="输入笔记内容"></textarea>
            <button id="addNoteBtn">添加笔记</button>
        </div>

        <div class="controls-section">
            <label for="sortOrder">排序方式:</label>
            <select id="sortOrder">
                <option value="newest">按日期 (最新)</option>
                <option value="oldest">按日期 (最旧)</option>
                <option value="title-asc">按标题 (A-Z)</option>
                <option value="title-desc">按标题 (Z-A)</option>
            </select>
            <button id="combineNotesBtn">组合选定笔记</button>
        </div>

        <div class="notes-grid" id="notesContainer">
            <!-- 笔记将在这里动态添加 -->
        </div>
    </div>

    <script>
        // JavaScript 代码将在这里添加
        document.addEventListener('DOMContentLoaded', () => {
            const notesContainer = document.getElementById('notesContainer');
            const addNoteBtn = document.getElementById('addNoteBtn');
            const noteTitleInput = document.getElementById('noteTitle');
            const noteContentInput = document.getElementById('noteContent');

            // 笔记数据存储
            let notes = [];

            // 笔记对象类
            class Note {
                constructor(title, content) {
                    this.id = Date.now(); // 简单生成唯一ID
                    this.title = title;
                    this.content = content;
                    this.createdAt = new Date();
                    this.updatedAt = new Date();
                }

                update(newTitle, newContent) {
                    this.title = newTitle;
                    this.content = newContent;
                    this.updatedAt = new Date();
                }
            }

            // 渲染所有笔记
            function renderNotes() {
                notesContainer.innerHTML = ''; // 清空现有笔记
                notes.forEach(note => {
                    const noteBox = document.createElement('div');
                    noteBox.classList.add('note-box');
                    noteBox.dataset.id = note.id; // 用于查找笔记

                    noteBox.innerHTML = `
                        <input type="checkbox" class="note-selection" data-id="${note.id}">
                        <h3>${note.title}</h3>
                        <p>${note.content}</p>
                        <div class="note-actions">
                            <button class="edit" data-id="${note.id}">编辑</button>
                            <button class="delete" data-id="${note.id}">删除</button>
                        </div>
                    `;
                    notesContainer.appendChild(noteBox);
                });
            }

            // 添加笔记
            addNoteBtn.addEventListener('click', () => {
                const title = noteTitleInput.value.trim();
                const content = noteContentInput.value.trim();

                if (title && content) {
                    const newNote = new Note(title, content);
                    notes.push(newNote);
                    renderNotes();
                    noteTitleInput.value = '';
                    noteContentInput.value = '';
                } else {
                    alert('笔记标题和内容都不能为空！');
                }
            });

            // 排序功能
            const sortOrderSelect = document.getElementById('sortOrder');
            sortOrderSelect.addEventListener('change', () => {
                const sortBy = sortOrderSelect.value;
                if (sortBy === 'newest') {
                    notes.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
                } else if (sortBy === 'oldest') {
                    notes.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
                } else if (sortBy === 'title-asc') {
                    notes.sort((a, b) => a.title.localeCompare(b.title));
                } else if (sortBy === 'title-desc') {
                    notes.sort((a, b) => b.title.localeCompare(a.title));
                }
                renderNotes();
            });

            // 组合笔记功能
            const combineNotesBtn = document.getElementById('combineNotesBtn');
            combineNotesBtn.addEventListener('click', () => {
                const selectedNoteIds = Array.from(document.querySelectorAll('.note-selection:checked'))
                                          .map(checkbox => parseInt(checkbox.dataset.id));

                if (selectedNoteIds.length < 2) {
                    alert('请选择至少两篇笔记进行组合。');
                    return;
                }

                const selectedNotes = notes.filter(note => selectedNoteIds.includes(note.id));
                const combinedTitle = selectedNotes.map(note => note.title).join(' | ');
                const combinedContent = selectedNotes.map(note => note.content).join('\n\n---\n\n');

                const newCombinedNote = new Note(combinedTitle, combinedContent);
                notes.push(newCombinedNote);

                // 删除被组合的原始笔记
                notes = notes.filter(note => !selectedNoteIds.includes(note.id));

                renderNotes();
            });

            // 编辑和删除笔记的事件委托
            notesContainer.addEventListener('click', (event) => {
                if (event.target.classList.contains('delete')) {
                    const idToDelete = parseInt(event.target.dataset.id);
                    notes = notes.filter(note => note.id !== idToDelete);
                    renderNotes();
                } else if (event.target.classList.contains('edit')) {
                    const idToEdit = parseInt(event.target.dataset.id);
                    const noteToEdit = notes.find(note => note.id === idToEdit);

                    if (noteToEdit) {
                        const newTitle = prompt('编辑笔记标题:', noteToEdit.title);
                        const newContent = prompt('编辑笔记内容:', noteToEdit.content);

                        if (newTitle !== null && newContent !== null) { // 用户没有取消
                            noteToEdit.update(newTitle.trim(), newContent.trim());
                            renderNotes();
                        }
                    }
                }
            });
            // 初始渲染（如果以后有从localStorage加载数据，可以在这里调用）
            renderNotes();
        });
    </script>
</body>
</html> 