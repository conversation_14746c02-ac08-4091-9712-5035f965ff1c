<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>什么是"访管中断"？</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f4f8;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        .container {
            background-color: #fff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
        }
        h1 {
            color: #0056b3;
            margin-bottom: 20px;
        }
        p, li {
            text-align: left;
            line-height: 1.8;
            font-size: 16px;
        }
        canvas {
            background-color: #ffffff;
            border: 2px solid #dde8f0;
            border-radius: 8px;
            margin-top: 20px;
            cursor: pointer;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin-top: 20px;
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #a0cfff;
            cursor: not-allowed;
        }
        .explanation-box {
            text-align: left;
            background-color: #e9f5ff;
            border-left: 5px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>应用程序在用户态使用特权指令进行系统调用，是 (C) 访管中断</h1>
        
        <div class="explanation-box">
            <p><strong>简单来说：</strong> 您的程序（比如游戏或浏览器）生活在"用户区"，权限受限。而操作系统核心则在"内核区"，拥有最高权限。当您的程序需要做一些它没权限做的"大事"（比如读写文件、访问硬件）时，它不能直接做，必须请求"内核"帮忙。这个请求过程，就像按下一个特殊的门铃，这个门铃就是"访管中断"。</p>
        </div>

        <canvas id="animationCanvas" width="800" height="400"></canvas>
        <button id="startAnimationBtn">播放动画演示</button>

        <h2>知识点解析</h2>
        <p>想象一下，你的电脑里有两个世界："用户世界"和"内核世界"。</p>
        <ul>
            <li><strong>用户模式 (User Mode):</strong> 你平时运行的应用程序，比如浏览器、游戏，都生活在这里。它们权限有限，不能为所欲-为，以防它们搞破坏。</li>
            <li><strong>内核模式 (Kernel Mode):</strong> 操作系统（Windows, macOS 等）的核心生活在这里。它拥有最高权限，可以控制电脑的所有硬件和资源。</li>
        </ul>
        <p>"访管中断"就是连接这两个世界的"魔法通道"，学名叫做 <strong>系统调用 (System Call)</strong>。</p>
        <p><strong>动画演示的过程就是：</strong></p>
        <ol>
            <li>应用程序需要读文件，但它没有权限。</li>
            <li>它执行一条特殊的"访管指令 (Trap Instruction)"，这会触发一个"中断"。</li>
            <li>CPU立刻暂停程序，切换到"内核模式"，让操作系统接管。</li>
            <li>操作系统代替程序读取文件。</li>
            <li>完成后，操作系统把结果和控制权还给程序，程序继续执行。</li>
        </ol>
        <p>这个由程序<strong>主动</strong>发起的，为了请求系统服务的"中断"，就是<strong>访管中断</strong>。它就像是用户程序访问操作系统管理员（"访管"）的门铃。</p>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        const startBtn = document.getElementById('startAnimationBtn');

        const width = canvas.width;
        const height = canvas.height;

        let animationState = 'idle'; // idle, running, finished
        let step = 0;
        let progress = 0;
        const speed = 0.01;

        // Colors and positions
        const userSpace = { x: 0, y: 0, w: width, h: height / 2, color: '#e3f2fd', label: '用户空间 (User Space)' };
        const kernelSpace = { x: 0, y: height / 2, w: width, h: height / 2, color: '#fff3e0', label: '内核空间 (Kernel Space)' };
        const app = { x: width * 0.2, y: userSpace.h * 0.5, w: 100, h: 60, color: '#42a5f5' };
        const os = { x: width * 0.8, y: userSpace.h + kernelSpace.h * 0.5, w: 100, h: 60, color: '#ffa726' };
        
        let message = { text: '', x: 0, y: 0, alpha: 0 };
        let arrow = { fromX: 0, fromY: 0, toX: 0, toY: 0, alpha: 0 };

        function drawRect(r, label) {
            ctx.fillStyle = r.color;
            ctx.fillRect(r.x, r.y, r.w, r.h);
            if (label) {
                ctx.fillStyle = '#666';
                ctx.font = 'bold 18px sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText(label, r.x + r.w / 2, r.y + 30);
            }
        }

        function drawComponent(c, label) {
            ctx.fillStyle = c.color;
            ctx.fillRect(c.x - c.w / 2, c.y - c.h / 2, c.w, c.h);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(label, c.x, c.y + 5);
        }
        
        function drawArrow(fromx, fromy, tox, toy, alpha) {
            if (alpha <= 0) return;
            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.strokeStyle = '#d32f2f';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromx, fromy);
            ctx.lineTo(tox, toy);
            ctx.stroke();
            
            // arrowhead
            const headlen = 10;
            const angle = Math.atan2(toy - fromy, tox - fromx);
            ctx.beginPath();
            ctx.moveTo(tox, toy);
            ctx.lineTo(tox - headlen * Math.cos(angle - Math.PI / 6), toy - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(tox - headlen * Math.cos(angle + Math.PI / 6), toy - headlen * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = '#d32f2f';
            ctx.fill();
            ctx.restore();
        }

        function drawMessage() {
            if (message.alpha <= 0) return;
            ctx.save();
            ctx.globalAlpha = message.alpha;
            ctx.fillStyle = 'black';
            ctx.font = '15px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(message.text, message.x, message.y);
            ctx.restore();
        }

        function drawInitialState() {
            ctx.clearRect(0, 0, width, height);
            drawRect(userSpace, userSpace.label);
            drawRect(kernelSpace, kernelSpace.label);
            drawComponent(app, '应用程序');
            drawComponent(os, '操作系统');
        }
        
        function update() {
            if (animationState !== 'running') return;

            progress += speed;

            switch(step) {
                case 0: // App wants to read file
                    message.text = '需要读取文件...';
                    message.x = app.x;
                    message.y = app.y - app.h / 2 - 10;
                    message.alpha = Math.min(1, progress * 10);
                    if (progress > 0.3) { progress = 0; step++; }
                    break;
                case 1: // App issues TRAP instruction
                    message.text = '执行"访管指令 (Trap)"';
                    message.alpha = Math.min(1, progress * 10);
                    
                    arrow.fromX = app.x + app.w/2;
                    arrow.fromY = app.y;
                    arrow.toX = app.x + app.w/2 + 30;
                    arrow.toY = userSpace.h;
                    arrow.alpha = Math.min(1, progress * 10);
                    
                    if (progress > 0.5) { progress = 0; step++; }
                    break;
                case 2: // Control transfers to Kernel
                    message.alpha = Math.max(0, message.alpha - speed * 5); // fade out old message
                    arrow.fromX = app.x + app.w/2 + 30;
                    arrow.fromY = userSpace.h;
                    arrow.toX = os.x - os.w / 2 - 10;
                    arrow.toY = os.y;
                    arrow.alpha = 1;
                    if (progress > 0.2) { progress = 0; step++; }
                    break;
                case 3: // OS handles the request
                     arrow.alpha = Math.max(0, arrow.alpha - speed * 5); // fade out arrow
                    message.text = '内核处理请求中...';
                    message.x = os.x;
                    message.y = os.y - os.h / 2 - 10;
                    message.alpha = Math.min(1, progress * 10);
                    if (progress > 0.5) { progress = 0; step++; }
                    break;
                case 4: // OS returns result
                    message.text = '返回结果和控制权';
                    arrow.fromX = os.x - os.w / 2;
                    arrow.fromY = os.y;
                    arrow.toX = app.x + app.w / 2 + 10;
                    arrow.toY = app.y;
                    arrow.alpha = Math.min(1, progress * 10);
                    if (progress > 0.5) { progress = 0; step++; }
                    break;
                case 5: // App continues execution
                    arrow.alpha = Math.max(0, arrow.alpha - speed * 5);
                    message.text = '程序继续执行...';
                    message.x = app.x;
                    message.y = app.y - app.h / 2 - 10;
                    message.alpha = 1;
                    if (progress > 0.5) { progress = 0; step++; animationState = 'finished'; }
                    break;
            }
        }
        
        function gameLoop() {
            drawInitialState();
            update();
            drawMessage();
            drawArrow(arrow.fromX, arrow.fromY, arrow.toX, arrow.toY, arrow.alpha);
            
            if(animationState === 'running') {
                requestAnimationFrame(gameLoop);
            } else if (animationState === 'finished') {
                setTimeout(() => {
                    startBtn.disabled = false;
                    startBtn.textContent = '重新播放';
                    animationState = 'idle';
                    step = 0;
                    progress = 0;
                    message.alpha = 0;
                    arrow.alpha = 0;
                    drawInitialState();
                }, 2000);
            }
        }
        
        startBtn.addEventListener('click', () => {
            if (animationState === 'idle') {
                animationState = 'running';
                startBtn.disabled = true;
                step = 0;
                progress = 0;
                gameLoop();
            }
        });

        // Initial draw
        drawInitialState();

    </script>
</body>
</html> 