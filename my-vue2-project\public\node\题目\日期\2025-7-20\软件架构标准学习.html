<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ANSI/IEEE 1471-2000 软件架构标准学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .option:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-2px);
        }

        .option.selected {
            background: #2196f3;
            color: white;
            border-color: #2196f3;
        }

        .option.correct {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        .option.wrong {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        #architectureCanvas {
            width: 100%;
            height: 500px;
            border-radius: 12px;
            cursor: pointer;
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .concept-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0;
            animation: slideInUp 0.6s ease-out forwards;
        }

        .concept-card:nth-child(1) { animation-delay: 0.1s; }
        .concept-card:nth-child(2) { animation-delay: 0.2s; }
        .concept-card:nth-child(3) { animation-delay: 0.3s; }
        .concept-card:nth-child(4) { animation-delay: 0.4s; }

        .concept-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .concept-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .concept-desc {
            font-size: 0.9rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .floating-element {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 3px;
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="floating-element" style="top: 10%; left: 5%; animation-delay: 0s;"></div>
    <div class="floating-element" style="top: 20%; right: 10%; animation-delay: 2s;"></div>
    <div class="floating-element" style="bottom: 30%; left: 8%; animation-delay: 4s;"></div>

    <div class="container">
        <div class="header">
            <h1 class="title">🏗️ 软件架构标准学习</h1>
            <p class="subtitle">ANSI/IEEE 1471-2000 标准互动学习</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>ANSI/IEEE 1471-2000是对软件密集型系统的架构进行描述的标准。在该标准中，（ ）这一概念主要用于描述软件架构模型。在此基础上，通常采用（ ）描述某个利益相关人（Stakeholder）所关注架构模型的某一方面。（ ）则是对所有利益相关人关注点的响应和回答。
            </div>
            
            <div class="options">
                <div class="option" data-value="A">A. 环境</div>
                <div class="option" data-value="B">B. 资源</div>
                <div class="option" data-value="C">C. 视角</div>
                <div class="option" data-value="D">D. 场景</div>
            </div>
            
            <button class="btn" onclick="checkAnswer()">提交答案</button>
            <button class="btn" onclick="showExplanation()">查看解析</button>
        </div>

        <div class="canvas-container">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">🎯 架构标准概念可视化</h3>
            <canvas id="architectureCanvas"></canvas>
            <div style="text-align: center; margin-top: 15px;">
                <button class="btn" onclick="startAnimation()">开始动画演示</button>
                <button class="btn" onclick="resetAnimation()">重置</button>
            </div>
        </div>

        <div class="explanation" id="explanationSection" style="display: none;">
            <h3 style="color: #333; margin-bottom: 20px;">📚 知识解析</h3>
            <p style="line-height: 1.8; color: #555; margin-bottom: 20px;">
                在ANSI/IEEE 1471-2000标准中，<strong>视角（Viewpoint）</strong>是核心概念，用于描述软件架构模型。每个视角代表了特定利益相关人关注的架构方面，通过不同的视图来展现架构的各个层面。
            </p>
            
            <div class="concept-grid">
                <div class="concept-card" onclick="highlightConcept('stakeholder')">
                    <div class="concept-title">👥 利益相关人</div>
                    <div class="concept-desc">对系统有关注点的个人、团队或组织</div>
                </div>
                <div class="concept-card" onclick="highlightConcept('viewpoint')">
                    <div class="concept-title">🔍 视角</div>
                    <div class="concept-desc">描述架构某一方面的规范和约定</div>
                </div>
                <div class="concept-card" onclick="highlightConcept('view')">
                    <div class="concept-title">📊 视图</div>
                    <div class="concept-desc">从特定视角表述的架构表示</div>
                </div>
                <div class="concept-card" onclick="highlightConcept('architecture')">
                    <div class="concept-title">🏛️ 架构描述</div>
                    <div class="concept-desc">对所有关注点的响应和回答</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedOption = null;
        let animationStep = 0;
        let canvas, ctx;
        let animationId;

        // 初始化
        window.onload = function() {
            canvas = document.getElementById('architectureCanvas');
            ctx = canvas.getContext('2d');
            
            // 设置canvas尺寸
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            
            drawInitialDiagram();
            updateProgress(25);
        };

        // 选择答案
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedOption = this.dataset.value;
            });
        });

        // 检查答案
        function checkAnswer() {
            if (!selectedOption) {
                alert('请先选择一个答案！');
                return;
            }
            
            document.querySelectorAll('.option').forEach(option => {
                if (option.dataset.value === 'C') {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && option.dataset.value !== 'C') {
                    option.classList.add('wrong');
                }
            });
            
            updateProgress(50);
            
            setTimeout(() => {
                if (selectedOption === 'C') {
                    alert('🎉 恭喜答对了！视角（Viewpoint）是正确答案。');
                } else {
                    alert('❌ 答案错误。正确答案是C：视角（Viewpoint）。');
                }
            }, 500);
        }

        // 显示解析
        function showExplanation() {
            document.getElementById('explanationSection').style.display = 'block';
            document.getElementById('explanationSection').scrollIntoView({ behavior: 'smooth' });
            updateProgress(75);
        }

        // 绘制初始图表
        function drawInitialDiagram() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('ANSI/IEEE 1471-2000 架构标准概念图', canvas.width/2, 40);
            
            // 绘制基础元素
            drawElement(canvas.width/2, 120, 80, '系统', '#667eea');
            drawElement(150, 220, 60, '利益相关人', '#4caf50');
            drawElement(canvas.width-150, 220, 60, '环境', '#ff9800');
            drawElement(canvas.width/2, 320, 70, '架构', '#e91e63');
            drawElement(canvas.width/2, 420, 65, '架构描述', '#9c27b0');
        }

        // 绘制元素
        function drawElement(x, y, radius, text, color) {
            // 绘制圆形
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 绘制文字
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 5);
        }

        // 开始动画演示
        function startAnimation() {
            animationStep = 0;
            animate();
            updateProgress(100);
        }

        // 动画函数
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawInitialDiagram();
            
            switch(animationStep) {
                case 0:
                    // 高亮利益相关人
                    drawPulse(150, 220, 60, '#4caf50');
                    drawText('利益相关人有各自的关注点', canvas.width/2, 480);
                    break;
                case 1:
                    // 显示视角概念
                    drawElement(300, 320, 50, '视角', '#2196f3');
                    drawArrow(200, 240, 250, 300);
                    drawText('视角用于描述架构模型的某一方面', canvas.width/2, 480);
                    break;
                case 2:
                    // 显示视图
                    drawElement(450, 320, 50, '视图', '#ff5722');
                    drawArrow(350, 320, 400, 320);
                    drawText('视图是从特定视角表述的架构', canvas.width/2, 480);
                    break;
                case 3:
                    // 显示完整关系
                    drawConnections();
                    drawText('架构描述响应所有利益相关人的关注点', canvas.width/2, 480);
                    break;
            }
            
            animationStep++;
            if (animationStep <= 3) {
                setTimeout(() => animate(), 2000);
            }
        }

        // 绘制脉冲效果
        function drawPulse(x, y, radius, color) {
            for (let i = 0; i < 3; i++) {
                ctx.beginPath();
                ctx.arc(x, y, radius + i * 10, 0, 2 * Math.PI);
                ctx.strokeStyle = color;
                ctx.globalAlpha = 0.3 - i * 0.1;
                ctx.lineWidth = 2;
                ctx.stroke();
            }
            ctx.globalAlpha = 1;
        }

        // 绘制箭头
        function drawArrow(x1, y1, x2, y2) {
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle - Math.PI/6), y2 - 10 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(x2 - 10 * Math.cos(angle + Math.PI/6), y2 - 10 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = '#333';
            ctx.fill();
        }

        // 绘制连接线
        function drawConnections() {
            // 利益相关人到视角
            drawArrow(200, 240, 250, 300);
            // 视角到视图
            drawArrow(350, 320, 400, 320);
            // 视图到架构描述
            drawArrow(450, 370, canvas.width/2 + 30, 380);
        }

        // 绘制文字
        function drawText(text, x, y) {
            ctx.fillStyle = '#333';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        // 重置动画
        function resetAnimation() {
            animationStep = 0;
            drawInitialDiagram();
        }

        // 高亮概念
        function highlightConcept(concept) {
            // 这里可以添加更多交互效果
            console.log('高亮概念:', concept);
        }

        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            drawInitialDiagram();
        });
    </script>
</body>
</html>
