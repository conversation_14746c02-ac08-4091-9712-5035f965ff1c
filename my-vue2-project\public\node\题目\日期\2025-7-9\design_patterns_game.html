<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式学习小游戏</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
            flex-direction: column;
        }
        .container {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 900px;
            width: 100%;
            text-align: center;
            margin-bottom: 20px;
        }
        h1 {
            color: #0056b3;
            margin-bottom: 25px;
            font-size: 2em;
        }
        .question-section {
            margin-bottom: 30px;
            text-align: left;
            line-height: 1.8;
            font-size: 1.1em;
            background-color: #e9f5ff;
            border-left: 5px solid #007bff;
            padding: 15px 20px;
            border-radius: 8px;
        }
        .options-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 30px;
        }
        .option-button {
            background-color: #f8f9fa;
            border: 2px solid #ccc;
            border-radius: 8px;
            padding: 15px 20px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            color: #555;
            display: flex;
            align-items: center;
        }
        .option-button:hover {
            background-color: #e2e6ea;
            border-color: #888;
        }
        .option-button.selected {
            border-color: #007bff;
            background-color: #e9f5ff;
            color: #007bff;
        }
        .option-button.correct {
            background-color: #d4edda;
            border-color: #28a745;
            color: #28a745;
            font-weight: bold;
        }
        .option-button.incorrect {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #dc3545;
            font-weight: bold;
        }
        .feedback {
            margin-top: 20px;
            font-size: 1.2em;
            font-weight: bold;
            min-height: 30px;
        }
        .explanation-section {
            margin-top: 40px;
            text-align: left;
            background-color: #fdfefe;
            border: 1px solid #eee;
            padding: 25px;
            border-radius: 8px;
        }
        .explanation-section h2 {
            color: #0056b3;
            margin-bottom: 15px;
            font-size: 1.8em;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .explanation-section h3 {
            color: #007bff;
            margin-top: 25px;
            margin-bottom: 10px;
            font-size: 1.4em;
        }
        .explanation-section p {
            margin-bottom: 10px;
            line-height: 1.7;
        }
        canvas {
            border: 1px solid #ddd;
            background-color: #f8f8f8;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
        }
        .next-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            margin-top: 30px;
            transition: background-color 0.3s ease;
        }
        .next-button:hover {
            background-color: #0056b3;
        }
        .hidden {
            display: none;
        }
        .loader {
            border: 8px solid #f3f3f3;
            border-top: 8px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设计模式趣味学习站</h1>

        <div id="quiz-section">
            <div class="question-section">
                <p>按照设计模式的目的进行划分，现有设计模式可以分为三类。其中创建型模式通过采用抽象类所定义的接口，封装了系统中对象如何创建、组合等信息，其代表有 Singleton 模式等；</p>
                <p>（<span id="blank1" class="blank-fill"></span>）模式主要用于如何组合已有的类和对象以获得更大的结构，其代表有 Adapter 模式等；</p>
                <p>（<span id="blank2" class="blank-fill">请作答此空</span>）模式主要用于对象之间的职责及其提供服务的分配方式，其代表有 （<span id="blank3" class="blank-fill"></span>）模式等。</p>
            </div>

            <div class="options-section" id="options">
                <button class="option-button" data-value="A">A. 行为型</button>
                <button class="option-button" data-value="B">B. 交互型</button>
                <button class="option-button" data-value="C">C. 耦合性</button>
                <button class="option-button" data-value="D">D. 关联型</button>
            </div>
            <div class="feedback" id="feedback"></div>
            <button class="next-button hidden" id="show-explanation-button">查看解析</button>
        </div>

        <div id="explanation-section" class="explanation-section hidden">
            <h2>设计模式知识解析</h2>
            <p>设计模式通常分为三大类，每种类型都有其独特的目标和应用场景。</p>

            <h3>1. 创建型模式 (Creational Patterns)</h3>
            <p><strong>目标：</strong> 封装了系统中对象如何创建、组合等信息。</p>
            <p><strong>作用：</strong> 它们主要关注对象的实例化过程，使系统在创建对象时更具弹性、解耦和可控性。</p>
            <p><strong>代表模式：</strong> <span class="pattern-name">Singleton (单例模式)</span>, Factory Method (工厂方法模式), Abstract Factory (抽象工厂模式), Builder (建造者模式), Prototype (原型模式) 等。</p>
            <canvas id="creationalCanvas" width="600" height="200"></canvas>
            <p><em>💡 <strong>创建型模式 (Singleton):</strong> 点击 Canvas 区域创建单例实例！观察它如何确保全局唯一，多次点击会有什么不同？</em></p>

            <h3>2. 结构型模式 (Structural Patterns)</h3>
            <p><strong>目标：</strong> 用于如何组合已有的类和对象以获得更大的结构。</p>
            <p><strong>作用：</strong> 它们关注类和对象的组合，通过继承、组合等方式将不同的功能组合在一起，形成更大的结构。</p>
            <p><strong>代表模式：</b> <span class="pattern-name">Adapter (适配器模式)</span>, Bridge (桥接模式), Composite (组合模式), Decorator (装饰器模式), Facade (外观模式), Flyweight (享元模式), Proxy (代理模式) 等。</p>
            <canvas id="structuralCanvas" width="600" height="200"></canvas>
            <p><em>💡 <strong>结构型模式 (Adapter):</strong> 点击 Canvas 区域模拟适配过程！看方形插头如何通过适配器完美匹配圆形插座，解决不兼容问题！</em></p>

            <h3>3. 行为型模式 (Behavioral Patterns)</h3>
            <p><strong>目标：</strong> 主要用于对象之间的职责及其提供服务的分配方式。</p>
            <p><strong>作用：</strong> 它们关注对象之间的职责划分和交互行为，使系统中的对象能够有效地协同工作，提高系统的灵活性和可维护性。</p>
            <p><strong>代表模式：</b> <span class="pattern-name">Visitor (访问者模式)</span>, Chain of Responsibility (责任链模式), Command (命令模式), Interpreter (解释器模式), Iterator (迭代器模式), Mediator (中介者模式), Memento (备忘录模式), Observer (观察者模式), State (状态模式), Strategy (策略模式), Template Method (模板方法模式) 等。</p>
            <canvas id="behavioralCanvas" width="600" height="200"></canvas>
            <p><em>💡 <strong>行为型模式 (Visitor):</strong> 点击 Canvas 区域，指挥小机器人依次“拜访”不同的形状！它将根据形状执行专属任务，体验职责分离的奇妙！</em></p>

            <button class="next-button" id="restart-button">重新开始</button>
        </div>
    </div>

    <script>
        const correctOption = 'A'; // 行为型
        const blank1Text = '结构型';
        const blank3Text = 'Visitor';

        const optionsContainer = document.getElementById('options');
        const feedbackDiv = document.getElementById('feedback');
        const showExplanationButton = document.getElementById('show-explanation-button');
        const quizSection = document.getElementById('quiz-section');
        const explanationSection = document.getElementById('explanation-section');
        const restartButton = document.getElementById('restart-button');

        const blank1Span = document.getElementById('blank1');
        const blank2Span = document.getElementById('blank2');
        const blank3Span = document.getElementById('blank3');

        // Initial state
        blank1Span.textContent = '';
        blank2Span.textContent = '请作答此空';
        blank3Span.textContent = '';


        optionsContainer.addEventListener('click', (event) => {
            const clickedButton = event.target.closest('.option-button');
            if (clickedButton && !clickedButton.classList.contains('selected')) {
                // Remove selected class from others
                Array.from(optionsContainer.children).forEach(btn => {
                    btn.classList.remove('selected', 'correct', 'incorrect');
                });

                clickedButton.classList.add('selected');

                if (clickedButton.dataset.value === correctOption) {
                    feedbackDiv.textContent = '🎉 回答正确！棒极了！';
                    feedbackDiv.style.color = '#28a745';
                    clickedButton.classList.add('correct');
                    showExplanationButton.classList.remove('hidden');
                    // Fill in the blanks after correct answer
                    blank1Span.textContent = blank1Text;
                    blank2Span.textContent = clickedButton.textContent.replace('A. ', ''); // Get just the text
                    blank3Span.textContent = blank3Text;

                    // Disable further clicks on options
                    Array.from(optionsContainer.children).forEach(btn => btn.disabled = true);

                } else {
                    feedbackDiv.textContent = '❌ 回答错误。再想想看哦！';
                    feedbackDiv.style.color = '#dc3545';
                    clickedButton.classList.add('incorrect');
                }
            }
        });

        showExplanationButton.addEventListener('click', () => {
            quizSection.classList.add('hidden');
            explanationSection.classList.remove('hidden');
            // Initialize Canvas demos
            initCreationalCanvas();
            initStructuralCanvas();
            initBehavioralCanvas();
        });

        restartButton.addEventListener('click', () => {
            quizSection.classList.remove('hidden');
            explanationSection.classList.add('hidden');
            feedbackDiv.textContent = '';
            showExplanationButton.classList.add('hidden');
            Array.from(optionsContainer.children).forEach(btn => {
                btn.classList.remove('selected', 'correct', 'incorrect');
                btn.disabled = false; // Re-enable options
            });
            // Reset blanks
            blank1Span.textContent = '';
            blank2Span.textContent = '请作答此空';
            blank3Span.textContent = '';
        });


        // Canvas Animations
        // 1. Creational Pattern (Singleton Example)
        function initCreationalCanvas() {
            const canvas = document.getElementById('creationalCanvas');
            const ctx = canvas.getContext('2d');
            let instance = null;
            let circleX = canvas.width / 2;
            let circleY = canvas.height / 2;
            let instanceCount = 0;

            function drawSingleton() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillStyle = '#333';
                ctx.fillText('点击此处尝试创建实例', canvas.width / 2, 20);

                if (instance) {
                    ctx.beginPath();
                    ctx.arc(circleX, circleY, 30, 0, Math.PI * 2);
                    ctx.fillStyle = '#3498db';
                    ctx.fill();
                    ctx.strokeStyle = '#2980b9';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    ctx.fillStyle = '#fff';
                    ctx.fillText('单例实例', circleX, circleY + 5);
                }

                ctx.fillStyle = '#333';
                ctx.fillText(`已创建实例数: ${instanceCount}`, canvas.width / 2, canvas.height - 20);
            }

            canvas.addEventListener('click', () => {
                if (!instance) {
                    instance = { id: 1 }; // Create the first instance
                    instanceCount = 1;
                    drawSingleton();
                    feedbackDiv.textContent = '🎉 成功创建第一个单例实例！';
                    feedbackDiv.style.color = '#28a745';
                } else {
                    feedbackDiv.textContent = '⚠️ 实例已存在，无法再次创建！';
                    feedbackDiv.style.color = '#dc3545';
                }
            });

            drawSingleton();
        }

        // 2. Structural Pattern (Adapter Example)
        function initStructuralCanvas() {
            const canvas = document.getElementById('structuralCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 600;
            canvas.height = 200;

            const squarePlug = { x: 100, y: 70, size: 40, color: '#e74c3c' };
            const roundSocket = { x: 500, y: 70, radius: 30, color: '#2ecc71' };
            const adapter = { x: 300, y: 70, width: 40, height: 60, color: '#f1c40f' };

            let currentPhase = 0; // 0: initial, 1: plug to adapter, 2: adapter+plug to socket, 3: completed
            let animationStartTime = null;
            const phaseDurations = {
                1: 1000, // Plug to adapter
                2: 1500  // Adapter+Plug to socket
            };

            function lerp(start, end, t) {
                return start + (end - start) * t;
            }

            function draw(progress = 0) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillStyle = '#333';
                ctx.fillText('点击 Canvas 区域开始模拟', canvas.width / 2, 20);

                // Draw round socket (always in place)
                ctx.beginPath();
                ctx.arc(roundSocket.x, roundSocket.y + roundSocket.radius / 2, roundSocket.radius, 0, Math.PI * 2);
                ctx.fillStyle = roundSocket.color;
                ctx.fill();
                ctx.strokeStyle = '#27ae60';
                ctx.lineWidth = 3;
                ctx.stroke();
                ctx.fillStyle = '#fff';
                ctx.fillText('圆形插座', roundSocket.x, roundSocket.y + roundSocket.radius / 2 + 5);

                let plugRenderX = squarePlug.x;
                let plugRenderY = squarePlug.y;
                let adapterRenderX = adapter.x;
                let adapterRenderY = adapter.y;

                if (currentPhase === 1) {
                    plugRenderX = lerp(squarePlug.x, adapter.x + adapter.width/2 - squarePlug.size/2, progress);
                    plugRenderY = lerp(squarePlug.y, adapter.y + adapter.height - squarePlug.size, progress);
                } else if (currentPhase === 2) {
                    plugRenderX = adapter.x + adapter.width/2 - squarePlug.size/2; // Plug is with adapter
                    plugRenderY = adapter.y + adapter.height - squarePlug.size;

                    const adapterTargetX = roundSocket.x - adapter.width / 2;
                    const adapterTargetY = roundSocket.y + roundSocket.radius / 2 - adapter.height / 2;

                    adapterRenderX = lerp(adapter.x, adapterTargetX, progress);
                    adapterRenderY = lerp(adapter.y, adapterTargetY, progress);

                    // Re-calculate plug position relative to animated adapter
                    plugRenderX = adapterRenderX + adapter.width/2 - squarePlug.size/2;
                    plugRenderY = adapterRenderY + adapter.height - squarePlug.size;
                } else if (currentPhase === 3) {
                    adapterRenderX = roundSocket.x - adapter.width / 2;
                    adapterRenderY = roundSocket.y + roundSocket.radius / 2 - adapter.height / 2;

                    plugRenderX = adapterRenderX + adapter.width/2 - squarePlug.size/2;
                    plugRenderY = adapterRenderY + adapter.height - squarePlug.size;

                    ctx.fillStyle = '#28a745';
                    ctx.fillText('✅ 成功适配！', canvas.width / 2, canvas.height - 20);
                }

                // Draw adapter
                ctx.fillStyle = adapter.color;
                ctx.fillRect(adapterRenderX, adapterRenderY, adapter.width, adapter.height);
                ctx.fillStyle = '#fff';
                ctx.fillText('适配器', adapterRenderX + adapter.width / 2, adapterRenderY + adapter.height / 2 + 5);

                // Draw square plug
                ctx.fillStyle = squarePlug.color;
                ctx.fillRect(plugRenderX, plugRenderY, squarePlug.size, squarePlug.size);
                ctx.fillStyle = '#fff';
                ctx.fillText('方形插头', plugRenderX + squarePlug.size / 2, plugRenderY + squarePlug.size / 2 + 5);

                if (currentPhase === 1) {
                    ctx.fillStyle = '#007bff';
                    ctx.fillText('🔌 方形插头正向适配器移动...', canvas.width / 2, canvas.height - 20);
                } else if (currentPhase === 2) {
                    ctx.fillStyle = '#007bff';
                    ctx.fillText('🔗 适配器与插头结合，正向插座移动...', canvas.width / 2, canvas.height - 20);
                }
            }

            function animate(currentTime) {
                if (currentPhase === 0 || currentPhase === 3) {
                    return;
                }

                if (!animationStartTime) animationStartTime = currentTime;
                const elapsed = currentTime - animationStartTime;
                const duration = phaseDurations[currentPhase];
                const progress = Math.min(elapsed / duration, 1);

                draw(progress);

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    if (currentPhase === 1) {
                        currentPhase = 2;
                        animationStartTime = null;
                        requestAnimationFrame(animate);
                    } else if (currentPhase === 2) {
                        currentPhase = 3;
                        animationStartTime = null;
                        draw();
                    }
                }
            }

            canvas.addEventListener('click', () => {
                if (currentPhase === 0 || currentPhase === 3) {
                    currentPhase = 1;
                    animationStartTime = null;
                    requestAnimationFrame(animate);
                }
            });
            draw();
        }

        // 3. Behavioral Pattern (Visitor Example)
        function initBehavioralCanvas() {
            const canvas = document.getElementById('behavioralCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 600;
            canvas.height = 200;

            class Shape {
                constructor(x, y, color) {
                    this.x = x;
                    this.y = y;
                    this.color = color;
                    this.originalColor = color;
                    this.isVisited = false;
                    this.visitAnimationProgress = 0; // For flashing effect
                }
                draw(ctx) { /* Abstract */ }
                accept(visitor) {
                    visitor.visit(this);
                    this.isVisited = true;
                    this.visitAnimationProgress = 0; // Start flash animation
                }
            }

            class Circle extends Shape {
                constructor(x, y, radius, color) {
                    super(x, y, color);
                    this.radius = radius;
                }
                draw(ctx) {
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                    ctx.fillStyle = this.color;
                    ctx.fill();
                    ctx.strokeStyle = this.isVisited && this.visitAnimationProgress < 1 ? `rgba(255, 255, 0, ${1 - this.visitAnimationProgress})` : '#555'; // Yellow flash
                    ctx.lineWidth = this.isVisited && this.visitAnimationProgress < 1 ? 5 : 2;
                    ctx.stroke();
                    ctx.fillStyle = '#fff';
                    ctx.fillText('圆形', this.x, this.y + 5);
                }
            }

            class Square extends Shape {
                constructor(x, y, size, color) {
                    super(x, y, color);
                    this.size = size;
                }
                draw(ctx) {
                    ctx.fillStyle = this.color;
                    ctx.fillRect(this.x - this.size / 2, this.y - this.size / 2, this.size, this.size);
                    ctx.strokeStyle = this.isVisited && this.visitAnimationProgress < 1 ? `rgba(255, 255, 0, ${1 - this.visitAnimationProgress})` : '#555'; // Yellow flash
                    ctx.lineWidth = this.isVisited && this.visitAnimationProgress < 1 ? 5 : 2;
                    ctx.strokeRect(this.x - this.size / 2, this.y - this.size / 2, this.size, this.size);
                    ctx.fillStyle = '#fff';
                    ctx.fillText('方形', this.x, this.y + 5);
                }
            }

            class RobotVisitor {
                constructor(x, y, color) {
                    this.x = x;
                    this.y = y;
                    this.color = color;
                    this.currentShape = null;
                    this.isVisiting = false; // To control robot's action state
                }
                draw(ctx) {
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, 15, 0, Math.PI * 2);
                    ctx.fillStyle = this.color;
                    ctx.fill();
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    ctx.fillStyle = '#fff';
                    ctx.fillText('机器人', this.x, this.y + 5);

                    if (this.isVisiting) {
                        ctx.fillStyle = '#007bff';
                        if (this.currentShape instanceof Circle) {
                            ctx.fillText('测量周长!', this.x, this.y - 25);
                        } else if (this.currentShape instanceof Square) {
                            ctx.fillText('计算面积!', this.x, this.y - 25);
                        }
                    }
                }
                visit(shape) {
                    this.currentShape = shape;
                    this.isVisiting = true;
                    if (shape instanceof Circle) {
                        feedbackDiv.textContent = `🤖 机器人抵达圆形，正在测量周长！`;
                    } else if (shape instanceof Square) {
                        feedbackDiv.textContent = `🤖 机器人抵达方形，正在计算面积！`;
                    }
                    feedbackDiv.style.color = '#007bff';
                    setTimeout(() => {
                        this.isVisiting = false; // End visiting action after a short delay
                        drawLoop();
                    }, 1000);
                }
            }

            const shapes = [
                new Circle(150, canvas.height / 2, 30, '#9b59b6'),
                new Square(450, canvas.height / 2, 60, '#1abc9c')
            ];
            const robot = new RobotVisitor(50, canvas.height / 2, '#34495e');

            let currentShapeIndex = 0;
            let robotTargetX = robot.x;
            let animationFrameId;

            function drawLoop() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillStyle = '#333';
                ctx.fillText('点击 Canvas 区域让机器人访问形状', canvas.width / 2, 20);

                shapes.forEach(s => {
                    s.draw(ctx);
                    if (s.isVisited && s.visitAnimationProgress < 1) {
                        s.visitAnimationProgress += 0.02; // Animate flash out
                    }
                });
                robot.draw(ctx);

                if (Math.abs(robot.x - robotTargetX) > 1) {
                    robot.x += (robotTargetX - robot.x) * 0.05; // Move smoothly
                } else if (robot.currentShape && !robot.isVisiting) { // Trigger visit only when robot arrived and not already visiting
                    robot.accept(robot.currentShape);
                    robot.currentShape = null; // Reset for next visit
                }
                animationFrameId = requestAnimationFrame(drawLoop);
            }

            canvas.addEventListener('click', () => {
                if (!robot.isVisiting) { // Only allow click if robot is not currently visiting
                    if (currentShapeIndex < shapes.length) {
                        const targetShape = shapes[currentShapeIndex];
                        robotTargetX = targetShape.x;
                        robot.currentShape = targetShape; // Set target for visitor
                        currentShapeIndex++;
                    } else {
                        currentShapeIndex = 0; // Reset for loop
                        robotTargetX = 50; // Move robot back to start
                        shapes.forEach(s => { // Reset shapes visited state
                            s.isVisited = false;
                            s.visitAnimationProgress = 0;
                        });
                        feedbackDiv.textContent = '🔄 演示已重置，点击重新开始。';
                        feedbackDiv.style.color = '#333';
                    }
                    if (!animationFrameId) {
                        drawLoop();
                    }
                }
            });
            drawLoop(); // Start initial drawing
        }
    </script>
</body>
</html> 