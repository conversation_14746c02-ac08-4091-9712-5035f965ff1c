<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抽象函数的定义域 - 零基础学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-50px);
            animation: fadeInDown 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .explanation {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.2);
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 5px 10px;
            border-radius: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎯 抽象函数的定义域</h1>
        
        <div class="section">
            <h2 class="section-title">📚 什么是抽象函数？</h2>
            <div class="explanation">
                <strong>抽象函数</strong>就像一个神秘的黑盒子📦，我们不知道里面具体的公式，但我们知道它的一些性质和规律。
                比如：f(x+1) = 2f(x)，这就是一个抽象函数的性质。
            </div>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="animateConcept()">🎬 播放概念动画</button>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 定义域是什么？</h2>
            <div class="explanation">
                <span class="highlight">定义域</span>就是函数f(x)中x可以取的所有值的集合。
                就像游乐园的身高限制一样，只有符合条件的x才能"进入"函数！
            </div>
            <div class="canvas-container">
                <canvas id="domainCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="animateDomain()">🎢 演示定义域</button>
        </div>

        <div class="section">
            <h2 class="section-title">🧮 解题步骤</h2>
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>
            <div class="canvas-container">
                <canvas id="stepsCanvas" width="700" height="400"></canvas>
            </div>
            <button class="interactive-btn" onclick="nextStep()">▶️ 下一步</button>
            <button class="interactive-btn" onclick="resetSteps()">🔄 重新开始</button>
        </div>

        <div class="section">
            <h2 class="section-title">💡 经典例题</h2>
            <div class="explanation">
                <strong>题目：</strong>已知函数f(x)的定义域为[1,3]，求f(2x-1)的定义域。
            </div>
            <div class="canvas-container">
                <canvas id="exampleCanvas" width="700" height="350"></canvas>
            </div>
            <button class="interactive-btn" onclick="solveExample()">🚀 开始解题</button>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let animationId;

        // 概念动画
        function animateConcept() {
            const canvas = document.getElementById('conceptCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制黑盒子
                ctx.fillStyle = '#333';
                ctx.fillRect(250, 100, 100, 100);
                ctx.fillStyle = 'white';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('f(x)', 300, 160);

                // 输入箭头
                const inputX = 150 + Math.sin(frame * 0.1) * 10;
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(inputX, 150);
                ctx.lineTo(240, 150);
                ctx.stroke();
                
                // 输入标签
                ctx.fillStyle = '#667eea';
                ctx.fillText('输入 x', inputX - 30, 140);

                // 输出箭头
                const outputX = 360 + Math.sin(frame * 0.1) * 10;
                ctx.beginPath();
                ctx.moveTo(360, 150);
                ctx.lineTo(outputX, 150);
                ctx.stroke();
                
                // 输出标签
                ctx.fillText('输出 f(x)', outputX + 30, 140);

                frame++;
                if (frame < 100) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 定义域动画
        function animateDomain() {
            const canvas = document.getElementById('domainCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制数轴
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(50, 150);
                ctx.lineTo(550, 150);
                ctx.stroke();

                // 绘制定义域区间
                const startX = 150;
                const endX = 450;
                ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                ctx.fillRect(startX, 130, endX - startX, 40);
                
                // 边界点
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(startX, 150, 8, 0, 2 * Math.PI);
                ctx.fill();
                ctx.beginPath();
                ctx.arc(endX, 150, 8, 0, 2 * Math.PI);
                ctx.fill();

                // 标签
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('a', startX, 190);
                ctx.fillText('b', endX, 190);
                ctx.fillText('定义域: [a, b]', 300, 220);

                // 动态小球
                const ballX = startX + (endX - startX) * (Math.sin(frame * 0.05) + 1) / 2;
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(ballX, 150, 6, 0, 2 * Math.PI);
                ctx.fill();

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 步骤动画
        function nextStep() {
            if (currentStep < 4) {
                document.getElementById(`step${currentStep}`).classList.remove('active');
                currentStep++;
                document.getElementById(`step${currentStep}`).classList.add('active');
                drawStep();
            }
        }

        function resetSteps() {
            document.getElementById(`step${currentStep}`).classList.remove('active');
            currentStep = 1;
            document.getElementById('step1').classList.add('active');
            drawStep();
        }

        function drawStep() {
            const canvas = document.getElementById('stepsCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.font = '18px Arial';
            ctx.fillStyle = '#333';

            switch(currentStep) {
                case 1:
                    ctx.fillText('步骤1: 找出已知函数的定义域', 50, 50);
                    ctx.fillText('例如：f(x)的定义域为[1,3]', 50, 100);
                    // 绘制区间
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(100, 150);
                    ctx.lineTo(400, 150);
                    ctx.stroke();
                    ctx.fillStyle = '#667eea';
                    ctx.fillText('1', 90, 180);
                    ctx.fillText('3', 390, 180);
                    break;
                case 2:
                    ctx.fillText('步骤2: 写出复合函数的形式', 50, 50);
                    ctx.fillText('f(2x-1) 中，内层函数是 g(x) = 2x-1', 50, 100);
                    // 绘制函数关系
                    ctx.fillStyle = '#ff6b6b';
                    ctx.fillRect(100, 150, 80, 50);
                    ctx.fillStyle = 'white';
                    ctx.fillText('2x-1', 140, 180);
                    break;
                case 3:
                    ctx.fillText('步骤3: 建立不等式', 50, 50);
                    ctx.fillText('要使f(2x-1)有意义，需要：1 ≤ 2x-1 ≤ 3', 50, 100);
                    ctx.fillStyle = '#333';
                    ctx.font = '24px Arial';
                    ctx.fillText('1 ≤ 2x-1 ≤ 3', 200, 200);
                    break;
                case 4:
                    ctx.fillText('步骤4: 解不等式求定义域', 50, 50);
                    ctx.fillText('1 ≤ 2x-1 ≤ 3', 50, 100);
                    ctx.fillText('2 ≤ 2x ≤ 4', 50, 130);
                    ctx.fillText('1 ≤ x ≤ 2', 50, 160);
                    ctx.fillStyle = '#4CAF50';
                    ctx.font = '20px Arial';
                    ctx.fillText('答案：定义域为[1,2]', 200, 250);
                    break;
            }
        }

        // 例题解答
        function solveExample() {
            const canvas = document.getElementById('exampleCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;

            function animateSolution() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                ctx.font = '16px Arial';
                ctx.fillStyle = '#333';

                if (step >= 0) {
                    ctx.fillText('已知：f(x)的定义域为[1,3]', 50, 30);
                    ctx.fillText('求：f(2x-1)的定义域', 50, 60);
                }

                if (step >= 1) {
                    ctx.fillStyle = '#667eea';
                    ctx.fillText('解题过程：', 50, 100);
                    ctx.fillStyle = '#333';
                    ctx.fillText('要使f(2x-1)有意义，需要2x-1在f(x)的定义域内', 50, 130);
                }

                if (step >= 2) {
                    ctx.fillText('即：1 ≤ 2x-1 ≤ 3', 50, 160);
                    // 绘制不等式
                    ctx.strokeStyle = '#ff6b6b';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(200, 180);
                    ctx.lineTo(400, 180);
                    ctx.stroke();
                }

                if (step >= 3) {
                    ctx.fillText('解不等式：', 50, 210);
                    ctx.fillText('1 + 1 ≤ 2x ≤ 3 + 1', 100, 240);
                    ctx.fillText('2 ≤ 2x ≤ 4', 100, 270);
                    ctx.fillText('1 ≤ x ≤ 2', 100, 300);
                }

                if (step >= 4) {
                    ctx.fillStyle = '#4CAF50';
                    ctx.font = '20px Arial';
                    ctx.fillText('∴ f(2x-1)的定义域为[1,2]', 200, 330);
                }

                step++;
                if (step <= 5) {
                    setTimeout(animateSolution, 1500);
                }
            }
            animateSolution();
        }

        // 初始化
        window.onload = function() {
            drawStep();
        };
    </script>
</body>
</html>
