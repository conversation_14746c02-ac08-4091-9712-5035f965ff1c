<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java面试题：this与super关键字详解</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        h1, h2 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h3 {
            color: #34495e;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        p {
            margin-bottom: 10px;
        }
        .concept-section {
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
        }
        .example-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9fbfb;
            border: 1px dashed #bdc3c7;
            border-radius: 5px;
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #fff;
            display: block;
            margin-top: 15px;
            border-radius: 5px;
        }
        .code-block {
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
            margin-top: 15px;
        }
        .interactive-controls button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            margin-right: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        .interactive-controls button:hover {
            background-color: #2980b9;
        }
        .interactive-controls button:active {
            background-color: #2471a3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Java面试题：this与super关键字详解</h1>

        <div class="concept-section">
            <h2>22、this关键字的用法</h2>
            <p><code>this</code>是Java中的一个特殊关键字，它代表“自身”的一个对象，可以理解为：指向当前对象本身的一个引用。在面向对象编程中，<code>this</code>用于明确地引用当前类的实例变量或调用当前类的其他方法或构造函数。</p>

            <h3>用法一：普通的直接引用，<code>this</code>相当于是指向当前对象本身。</h3>
            <p>当在一个类的非静态方法内部使用<code>this</code>时，它指向调用该方法的当前对象实例。</p>
            <div class="example-area">
                <p><strong>概念演示：</strong>点击下方按钮，观察<code>this</code>如何指向当前对象。</p>
                <div class="interactive-controls">
                    <button onclick="demonstrateThisBasic()">开始演示</button>
                    <button onclick="resetThisBasic()">重置</button>
                </div>
                <canvas id="thisBasicCanvas" width="500" height="200"></canvas>
            </div>
            <div class="code-block">
                <pre><code>
class MyClass {
    int value = 10;
    void printValue() {
        System.out.println("当前对象的值: " + this.value); // 使用 this.value 明确引用当前对象的 value
    }
    void printValueWithoutThis() {
        System.out.println("当前对象的值: " + value); // 不使用 this 也可以，但 this 更明确
    }
}
// 在 main 方法中:
// MyClass obj = new MyClass();
// obj.printValue();
                </code></pre>
            </div>

            <h3>用法二：形参与成员名字重名，用<code>this</code>来区分：</h3>
            <p>当方法的参数名与类的成员变量名相同时，为了避免混淆，可以使用<code>this.</code>来明确指定是类的成员变量。</p>
            <div class="example-area">
                <p><strong>概念演示：</strong>点击下方按钮，观察<code>this</code>如何区分同名变量。</p>
                <div class="interactive-controls">
                    <button onclick="demonstrateThisParameter()">开始演示</button>
                    <button onclick="resetThisParameter()">重置</button>
                </div>
                <canvas id="thisParameterCanvas" width="500" height="250"></canvas>
            </div>
            <div class="code-block">
                <pre><code>
class Person {
    String name; // 成员变量
    int age;     // 成员变量

    // 构造函数，参数名与成员变量名重名
    Person(String name, int age) {
        this.name = name; // this.name 指的是成员变量 name
        this.age = age;   // this.age 指的是成员变量 age
        // name 和 age (没有this前缀) 指的是构造函数的形参
    }
    void showInfo() {
        System.out.println("姓名: " + this.name + ", 年龄: " + this.age);
    }
}
// 在 main 方法中:
// Person p = new Person("张三", 25);
// p.showInfo();
                </code></pre>
            </div>

            <h3>用法三：引用本类的构造函数（<code>this(...)</code>）：</h3>
            <p><code>this(...)</code>可以用于在一个构造函数中调用当前类的另一个构造函数。这种用法必须是构造函数中的第一条语句。</p>
            <div class="example-area">
                <p><strong>概念演示：</strong>点击下方按钮，观察<code>this(...)</code>如何实现构造函数链式调用。</p>
                <div class="interactive-controls">
                    <button onclick="demonstrateThisConstructor()">开始演示</button>
                    <button onclick="resetThisConstructor()">重置</button>
                </div>
                <canvas id="thisConstructorCanvas" width="600" height="300"></canvas>
            </div>
            <div class="code-block">
                <pre><code>
class Box {
    double width;
    double height;
    double depth;

    // 构造函数1
    Box() {
        this(10, 10, 10); // 调用构造函数2
        System.out.println("无参构造函数被调用");
    }

    // 构造函数2
    Box(double width, double height, double depth) {
        this.width = width;
        this.height = height;
        this.depth = depth;
        System.out.println("带参构造函数被调用");
    }
}
// 在 main 方法中:
// Box mybox = new Box(); // 调用无参构造函数，然后它会调用带参构造函数
                </code></pre>
            </div>

            <h3>新增示例：<code>Book</code> 类的构造函数链式调用</h3>
            <p>这个例子展示了一个 <code>Book</code> 类如何通过 <code>this(...)</code> 实现构造函数的链式调用，确保对象初始化的一致性。</p>
            <div class="example-area">
                <p><strong>概念演示：</strong>点击下方按钮，观察 <code>this(...)</code> 如何在 <code>Book</code> 类中传递默认参数。</p>
                <div class="interactive-controls">
                    <button onclick="demonstrateBookConstructor()">开始演示</button>
                    <button onclick="resetBookConstructor()">重置</button>
                </div>
                <canvas id="bookConstructorCanvas" width="700" height="350"></canvas>
            </div>
            <div class="code-block">
                <pre><code>
class Book {
    String title;
    String author;
    double price;

    // 构造函数1：只接受书名和作者，价格默认为 0.0
    Book(String title, String author) {
        this(title, author, 0.0); // 调用构造函数2
        System.out.println("Book(title, author) 构造函数被调用");
    }

    // 构造函数2：接受书名、作者和价格
    Book(String title, String author, double price) {
        this.title = title;
        this.author = author;
        this.price = price;
        System.out.println("Book(title, author, price) 构造函数被调用");
    }

    // 构造函数3：无参构造函数，提供默认书名和作者，价格默认为 0.0
    Book() {
        this("未知书名", "佚名"); // 调用构造函数1
        System.out.println("Book() 无参构造函数被调用");
    }

    void displayInfo() {
        System.out.println("书名: " + title + ", 作者: " + author + ", 价格: " + price);
    }
}
// 在 main 方法中:
// Book book1 = new Book("Java编程思想", "Bruce Eckel"); // 调用构造函数1，然后构造函数2
// Book book2 = new Book(); // 调用构造函数3，然后构造函数1，最后构造函数2
                </code></pre>
            </div>
        </div>

        <div class="concept-section">
            <h2>23、super关键字的用法</h2>
            <p><code>super</code>关键字可以理解为是指向自己超（父）类对象的一个引用，而这个超类指的是离自己最近的一个父类。它用于访问父类的成员（变量或方法）或调用父类的构造函数。</p>

            <h3>用法一：普通的直接引用，<code>super</code>相当于是指向当前对象的父类的引用。</h3>
            <p>这样就可以用<code>super.xxx</code>来引用父类的成员变量或方法。</p>
            <div class="example-area">
                <p><strong>概念演示：</strong>点击下方按钮，观察<code>super</code>如何引用父类成员。</p>
                <div class="interactive-controls">
                    <button onclick="demonstrateSuperBasic()">开始演示</button>
                    <button onclick="resetSuperBasic()">重置</button>
                </div>
                <canvas id="superBasicCanvas" width="500" height="200"></canvas>
            </div>
            <div class="code-block">
                <pre><code>
class Animal {
    String type = "动物";
    void eat() {
        System.out.println("动物正在吃东西...");
    }
}

class Dog extends Animal {
    String type = "狗"; // 子类也有一个同名成员变量
    void showType() {
        System.out.println("子类类型: " + this.type); // 引用子类的 type
        System.out.println("父类类型: " + super.type); // 引用父类的 type
    }
    void callParentEat() {
        super.eat(); // 调用父类的 eat 方法
    }
}
// 在 main 方法中:
// Dog myDog = new Dog();
// myDog.showType();
// myDog.callParentEat();
                </code></pre>
            </div>

            <h3>用法二：子类中的成员变量或方法与父类中的成员变量或方法同名时，用<code>super</code>进行区分。</h3>
            <p>与<code>this</code>类似，当子类中定义了与父类同名的成员或方法时，可以使用<code>super.</code>来明确访问父类的版本。</p>
            <div class="example-area">
                <p><strong>概念演示：</strong>点击下方按钮，观察<code>super</code>如何区分同名变量和方法。</p>
                <div class="interactive-controls">
                    <button onclick="demonstrateSuperOverride()">开始演示</button>
                    <button onclick="resetSuperOverride()">重置</button>
                </div>
                <canvas id="superOverrideCanvas" width="500" height="250"></canvas>
            </div>
            <div class="code-block">
                <pre><code>
class Vehicle {
    void move() {
        System.out.println("交通工具在移动。");
    }
}

class Car extends Vehicle {
    void move() { // 子类重写了父类的方法
        System.out.println("汽车在公路上行驶。");
    }

    void start() {
        move();      // 调用子类的 move 方法
        super.move(); // 调用父类的 move 方法
    }
}
// 在 main 方法中:
// Car myCar = new Car();
// myCar.start();
                </code></pre>
            </div>

            <h3>用法三：引用父类构造函数（<code>super(...)</code>）：</h3>
            <p><code>super(...)</code>用于在子类的构造函数中调用父类的构造函数。它也必须是子类构造函数中的第一条语句。</p>
            <div class="example-area">
                <p><strong>概念演示：</strong>点击下方按钮，观察<code>super(...)</code>如何实现父类构造函数的调用。</p>
                <div class="interactive-controls">
                    <button onclick="demonstrateSuperConstructor()">开始演示</button>
                    <button onclick="resetSuperConstructor()">重置</button>
                </div>
                <canvas id="superConstructorCanvas" width="600" height="300"></canvas>
            </div>
            <div class="code-block">
                <pre><code>
class Shape {
    String color;
    Shape(String color) {
        this.color = color;
        System.out.println("Shape 构造函数被调用，颜色: " + color);
    }
}

class Circle extends Shape {
    double radius;
    // 子类构造函数
    Circle(String color, double radius) {
        super(color); // 调用父类 Shape 的构造函数
        this.radius = radius;
        System.out.println("Circle 构造函数被调用，半径: " + radius);
    }
}
// 在 main 方法中:
// Circle c = new Circle("red", 5.0); // 调用 Circle 构造函数，它会先调用 Shape 构造函数
                </code></pre>
            </div>
        </div>
    </div>

    <script>
        // --- this 关键字演示 ---

        // 用法一：普通的直接引用
        const thisBasicCanvas = document.getElementById('thisBasicCanvas');
        const thisBasicCtx = thisBasicCanvas.getContext('2d');
        let thisBasicAnimationId = null;

        function drawObject(ctx, x, y, label, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 80, 80);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 80, 80);
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(label, x + 40, y + 40);
        }

        function drawThisArrow(ctx, fromX, fromY, toX, toY, text) {
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // Arrowhead
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();

            ctx.fillStyle = '#e74c3c';
            ctx.font = '16px Arial';
            ctx.fillText(text, (fromX + toX) / 2, (fromY + toY) / 2 - 10);
        }

        function animateThisBasic(step) {
            thisBasicCtx.clearRect(0, 0, thisBasicCanvas.width, thisBasicCanvas.height);
            drawObject(thisBasicCtx, 200, 60, '当前对象 (this)', '#3498db'); // Current object

            if (step >= 1) {
                drawThisArrow(thisBasicCtx, 240, 150, 240, 140, 'this'); // Pointing to itself
                drawThisArrow(thisBasicCtx, 240, 140, 240, 120, '');
                drawThisArrow(thisBasicCtx, 240, 120, 240, 100, '');
            }

            if (step < 2) {
                thisBasicAnimationId = requestAnimationFrame(() => animateThisBasic(step + 0.05));
            }
        }

        function demonstrateThisBasic() {
            resetThisBasic();
            animateThisBasic(0);
        }

        function resetThisBasic() {
            cancelAnimationFrame(thisBasicAnimationId);
            thisBasicCtx.clearRect(0, 0, thisBasicCanvas.width, thisBasicCanvas.height);
            drawObject(thisBasicCtx, 200, 60, '当前对象 (this)', '#3498db');
        }

        // 用法二：形参与成员名字重名
        const thisParameterCanvas = document.getElementById('thisParameterCanvas');
        const thisParameterCtx = thisParameterCanvas.getContext('2d');
        let thisParameterAnimationId = null;

        function drawVariable(ctx, x, y, name, value, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 120, 50);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, 120, 50);
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(`${name}: ${value}`, x + 60, y + 25);
        }

        function animateThisParameter(step) {
            thisParameterCtx.clearRect(0, 0, thisParameterCanvas.width, thisParameterCanvas.height);

            // Member variable
            drawVariable(thisParameterCtx, 50, 50, 'this.name', '未赋值', '#2ecc71');
            drawVariable(thisParameterCtx, 50, 120, 'this.age', '未赋值', '#2ecc71');

            // Parameter variable
            drawVariable(thisParameterCtx, 300, 80, 'name', '张三', '#9b59b6');
            drawVariable(thisParameterCtx, 300, 150, 'age', '25', '#9b59b6');

            thisParameterCtx.fillStyle = '#333';
            thisParameterCtx.font = '16px Arial';
            thisParameterCtx.fillText('成员变量', 110, 30);
            thisParameterCtx.fillText('形参', 360, 60);

            if (step >= 0.5) {
                thisParameterCtx.strokeStyle = '#e74c3c';
                thisParameterCtx.lineWidth = 2;
                thisParameterCtx.beginPath();
                thisParameterCtx.moveTo(300, 105);
                thisParameterCtx.lineTo(170, 75);
                thisParameterCtx.stroke();
                thisParameterCtx.fillText('this.name = name;', 230, 90);

                thisParameterCtx.beginPath();
                thisParameterCtx.moveTo(300, 175);
                thisParameterCtx.lineTo(170, 145);
                thisParameterCtx.stroke();
                thisParameterCtx.fillText('this.age = age;', 230, 160);

                // Update member variables
                thisParameterCtx.clearRect(50, 50, 120, 50);
                drawVariable(thisParameterCtx, 50, 50, 'this.name', '张三', '#2ecc71');
                thisParameterCtx.clearRect(50, 120, 120, 50);
                drawVariable(thisParameterCtx, 50, 120, 'this.age', '25', '#2ecc71');
            }

            if (step < 1) {
                thisParameterAnimationId = requestAnimationFrame(() => animateThisParameter(step + 0.02));
            }
        }

        function demonstrateThisParameter() {
            resetThisParameter();
            animateThisParameter(0);
        }

        function resetThisParameter() {
            cancelAnimationFrame(thisParameterAnimationId);
            thisParameterCtx.clearRect(0, 0, thisParameterCanvas.width, thisParameterCanvas.height);
            drawVariable(thisParameterCtx, 50, 50, 'this.name', '未赋值', '#2ecc71');
            drawVariable(thisParameterCtx, 50, 120, 'this.age', '未赋值', '#2ecc71');
            drawVariable(thisParameterCtx, 300, 80, 'name', '张三', '#9b59b6');
            drawVariable(thisParameterCtx, 300, 150, 'age', '25', '#9b59b6');
            thisParameterCtx.fillStyle = '#333';
            thisParameterCtx.font = '16px Arial';
            thisParameterCtx.fillText('成员变量', 110, 30);
            thisParameterCtx.fillText('形参', 360, 60);
        }

        // 用法三：引用本类的构造函数 (this(...))
        const thisConstructorCanvas = document.getElementById('thisConstructorCanvas');
        const thisConstructorCtx = thisConstructorCanvas.getContext('2d');
        let thisConstructorAnimationId = null;

        function drawBox(ctx, x, y, label, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 150, 70);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 150, 70);
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(label, x + 75, y + 35);
        }

        function animateThisConstructor(step) {
            thisConstructorCtx.clearRect(0, 0, thisConstructorCanvas.width, thisConstructorCanvas.height);

            drawBox(thisConstructorCtx, 50, 50, '构造函数 Box()', '#f1c40f');
            drawBox(thisConstructorCtx, 350, 150, '构造函数 Box(w,h,d)', '#27ae60');
            
            thisConstructorCtx.fillStyle = '#333';
            thisConstructorCtx.font = '16px Arial';
            thisConstructorCtx.fillText('调用顺序', 280, 100);

            if (step >= 0.2) {
                thisConstructorCtx.strokeStyle = '#e74c3c';
                thisConstructorCtx.lineWidth = 3;
                thisConstructorCtx.setLineDash([5, 5]);
                thisConstructorCtx.beginPath();
                thisConstructorCtx.moveTo(125, 120);
                thisConstructorCtx.lineTo(125, 150);
                thisConstructorCtx.lineTo(350, 185);
                thisConstructorCtx.stroke();
                thisConstructorCtx.fillText('this(10,10,10);', 250, 140);
            }

            if (step >= 0.5) {
                thisConstructorCtx.setLineDash([]);
                thisConstructorCtx.fillStyle = '#16a085';
                thisConstructorCtx.font = '18px Arial';
                thisConstructorCtx.fillText('Box(w,h,d) 被执行', 425, 230);
            }

            if (step >= 0.8) {
                thisConstructorCtx.fillStyle = '#f39c12';
                thisConstructorCtx.font = '18px Arial';
                thisConstructorCtx.fillText('Box() 被执行', 125, 200);
            }

            if (step < 1) {
                thisConstructorAnimationId = requestAnimationFrame(() => animateThisConstructor(step + 0.01));
            }
        }

        function demonstrateThisConstructor() {
            resetThisConstructor();
            animateThisConstructor(0);
        }

        function resetThisConstructor() {
            cancelAnimationFrame(thisConstructorAnimationId);
            thisConstructorCtx.clearRect(0, 0, thisConstructorCanvas.width, thisConstructorCanvas.height);
            drawBox(thisConstructorCtx, 50, 50, '构造函数 Box()', '#f1c40f');
            drawBox(thisConstructorCtx, 350, 150, '构造函数 Box(w,h,d)', '#27ae60');
            thisConstructorCtx.fillStyle = '#333';
            thisConstructorCtx.font = '16px Arial';
            thisConstructorCtx.fillText('调用顺序', 280, 100);
        }

        // 新增示例：Book 类的构造函数链式调用
        const bookConstructorCanvas = document.getElementById('bookConstructorCanvas');
        const bookConstructorCtx = bookConstructorCanvas.getContext('2d');
        let bookConstructorAnimationId = null;

        function drawBookBox(ctx, x, y, label, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 190, 70);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 190, 70);
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(label, x + 95, y + 35);
        }

        function animateBookConstructor(step) {
            bookConstructorCtx.clearRect(0, 0, bookConstructorCanvas.width, bookConstructorCanvas.height);

            drawBookBox(bookConstructorCtx, 50, 50, '构造函数 Book()', '#f1c40f');
            drawBookBox(bookConstructorCtx, 250, 150, '构造函数 Book(title, author)', '#27ae60');
            drawBookBox(bookConstructorCtx, 450, 250, '构造函数 Book(title, author, price)', '#3498db');
            
            bookConstructorCtx.fillStyle = '#333';
            bookConstructorCtx.font = '16px Arial';
            bookConstructorCtx.fillText('调用顺序', 350, 30);
            bookConstructorCtx.fillText('--- 调用 Book() ---', 150, 20);

            if (step >= 0.1) {
                // Book() -> Book(title, author)
                bookConstructorCtx.strokeStyle = '#e74c3c';
                bookConstructorCtx.lineWidth = 3;
                bookConstructorCtx.setLineDash([5, 5]);
                bookConstructorCtx.beginPath();
                bookConstructorCtx.moveTo(145, 120);
                bookConstructorCtx.lineTo(145, 150);
                bookConstructorCtx.lineTo(250, 185);
                bookConstructorCtx.stroke();
                bookConstructorCtx.fillText('this("未知书名", "佚名");', 170, 140);
            }

            if (step >= 0.4) {
                // Book(title, author) -> Book(title, author, price)
                bookConstructorCtx.strokeStyle = '#e74c3c';
                bookConstructorCtx.lineWidth = 3;
                bookConstructorCtx.setLineDash([5, 5]);
                bookConstructorCtx.beginPath();
                bookConstructorCtx.moveTo(345, 220);
                bookConstructorCtx.lineTo(345, 250);
                bookConstructorCtx.lineTo(450, 285);
                bookConstructorCtx.stroke();
                bookConstructorCtx.fillText('this(title, author, 0.0);', 370, 240);
            }

            if (step >= 0.7) {
                bookConstructorCtx.setLineDash([]);
                bookConstructorCtx.fillStyle = '#16a085';
                bookConstructorCtx.font = '18px Arial';
                bookConstructorCtx.fillText('Book(title, author, price) 被执行', 550, 330);
            }

            if (step >= 1.0) {
                bookConstructorCtx.fillStyle = '#2980b9';
                bookConstructorCtx.font = '18px Arial';
                bookConstructorCtx.fillText('Book(title, author) 被执行', 350, 310);
            }

            if (step >= 1.3) {
                bookConstructorCtx.fillStyle = '#f39c12';
                bookConstructorCtx.font = '18px Arial';
                bookConstructorCtx.fillText('Book() 被执行', 150, 290);
            }

            if (step < 1.5) {
                bookConstructorAnimationId = requestAnimationFrame(() => animateBookConstructor(step + 0.01));
            } else {
                // Final state after animation completes
                bookConstructorCtx.clearRect(0, 0, bookConstructorCanvas.width, bookConstructorCanvas.height);
                resetBookConstructor();
                bookConstructorCtx.fillStyle = '#16a085';
                bookConstructorCtx.font = '18px Arial';
                bookConstructorCtx.fillText('Book(title, author, price) 被执行', 550, 330);
                bookConstructorCtx.fillStyle = '#2980b9';
                bookConstructorCtx.font = '18px Arial';
                bookConstructorCtx.fillText('Book(title, author) 被执行', 350, 310);
                bookConstructorCtx.fillStyle = '#f39c12';
                bookConstructorCtx.font = '18px Arial';
                bookConstructorCtx.fillText('Book() 被执行', 150, 290);
            }
        }

        function demonstrateBookConstructor() {
            resetBookConstructor();
            animateBookConstructor(0);
        }

        function resetBookConstructor() {
            cancelAnimationFrame(bookConstructorAnimationId);
            bookConstructorCtx.clearRect(0, 0, bookConstructorCanvas.width, bookConstructorCanvas.height);
            drawBookBox(bookConstructorCtx, 50, 50, '构造函数 Book()', '#f1c40f');
            drawBookBox(bookConstructorCtx, 250, 150, '构造函数 Book(title, author)', '#27ae60');
            drawBookBox(bookConstructorCtx, 450, 250, '构造函数 Book(title, author, price)', '#3498db');
            bookConstructorCtx.fillStyle = '#333';
            bookConstructorCtx.font = '16px Arial';
            bookConstructorCtx.fillText('调用顺序', 350, 30);
            bookConstructorCtx.fillText('--- 调用 Book() ---', 150, 20);
        }

        // --- super 关键字演示 ---

        // 用法一：普通的直接引用
        const superBasicCanvas = document.getElementById('superBasicCanvas');
        const superBasicCtx = superBasicCanvas.getContext('2d');
        let superBasicAnimationId = null;

        function drawClassBox(ctx, x, y, className, isParent, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 150, 70);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 150, 70);
            ctx.fillStyle = '#fff';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(className, x + 75, y + 35);
            if (isParent) {
                ctx.font = '12px Arial';
                ctx.fillText('(父类)', x + 75, y + 55);
            } else {
                ctx.font = '12px Arial';
                ctx.fillText('(子类)', x + 75, y + 55);
            }
        }

        function animateSuperBasic(step) {
            superBasicCtx.clearRect(0, 0, superBasicCanvas.width, superBasicCanvas.height);

            drawClassBox(superBasicCtx, 175, 20, 'Animal', true, '#e67e22');
            drawClassBox(superBasicCtx, 175, 110, 'Dog', false, '#3498db');

            superBasicCtx.fillStyle = '#333';
            superBasicCtx.font = '14px Arial';
            superBasicCtx.fillText('继承关系', 245, 90);
            superBasicCtx.strokeStyle = '#888';
            superBasicCtx.lineWidth = 2;
            superBasicCtx.beginPath();
            superBasicCtx.moveTo(250, 90);
            superBasicCtx.lineTo(250, 110);
            superBasicCtx.stroke();

            if (step >= 0.5) {
                superBasicCtx.strokeStyle = '#e74c3c';
                superBasicCtx.lineWidth = 3;
                superBasicCtx.beginPath();
                superBasicCtx.moveTo(250, 150); // From Dog
                superBasicCtx.lineTo(250, 120);
                superBasicCtx.lineTo(250, 100);
                superBasicCtx.lineTo(250, 80); // Towards Animal
                superBasicCtx.stroke();
                superBasicCtx.fillStyle = '#e74c3c';
                superBasicCtx.font = '16px Arial';
                superBasicCtx.fillText('super', 280, 130);
            }
            if (step < 1) {
                superBasicAnimationId = requestAnimationFrame(() => animateSuperBasic(step + 0.02));
            }
        }

        function demonstrateSuperBasic() {
            resetSuperBasic();
            animateSuperBasic(0);
        }

        function resetSuperBasic() {
            cancelAnimationFrame(superBasicAnimationId);
            superBasicCtx.clearRect(0, 0, superBasicCanvas.width, superBasicCanvas.height);
            drawClassBox(superBasicCtx, 175, 20, 'Animal', true, '#e67e22');
            drawClassBox(superBasicCtx, 175, 110, 'Dog', false, '#3498db');
            superBasicCtx.fillStyle = '#333';
            superBasicCtx.font = '14px Arial';
            superBasicCtx.fillText('继承关系', 245, 90);
            superBasicCtx.strokeStyle = '#888';
            superBasicCtx.lineWidth = 2;
            superBasicCtx.beginPath();
            superBasicCtx.moveTo(250, 90);
            superBasicCtx.lineTo(250, 110);
            superBasicCtx.stroke();
        }

        // 用法二：子类中的成员变量或方法与父类中的成员变量或方法同名时
        const superOverrideCanvas = document.getElementById('superOverrideCanvas');
        const superOverrideCtx = superOverrideCanvas.getContext('2d');
        let superOverrideAnimationId = null;

        function animateSuperOverride(step) {
            superOverrideCtx.clearRect(0, 0, superOverrideCanvas.width, superOverrideCanvas.height);

            drawClassBox(superOverrideCtx, 50, 50, 'Vehicle (move())', true, '#e67e22');
            drawClassBox(superOverrideCtx, 300, 150, 'Car (move(), start())', false, '#3498db');

            superOverrideCtx.fillStyle = '#333';
            superOverrideCtx.font = '14px Arial';
            superOverrideCtx.fillText('继承关系', 225, 120);
            superOverrideCtx.strokeStyle = '#888';
            superOverrideCtx.lineWidth = 2;
            superOverrideCtx.beginPath();
            superOverrideCtx.moveTo(125, 120);
            superOverrideCtx.lineTo(375, 150);
            superOverrideCtx.stroke();

            superOverrideCtx.font = '16px Arial';

            if (step >= 0.2 && step < 0.6) {
                superOverrideCtx.strokeStyle = '#2ecc71';
                superOverrideCtx.lineWidth = 3;
                superOverrideCtx.beginPath();
                superOverrideCtx.moveTo(375, 185);
                superOverrideCtx.lineTo(375, 210 + (step - 0.2) * 50); // Move down
                superOverrideCtx.stroke();
                superOverrideCtx.fillText('move()', 300, 200); // Calls Car's move()
                superOverrideCtx.fillStyle = '#2ecc71';
                superOverrideCtx.fillText('调用子类 move()', 375, 230);
            } else if (step >= 0.6) {
                superOverrideCtx.fillStyle = '#2ecc71';
                superOverrideCtx.fillText('调用子类 move()', 375, 230); // Stay
                superOverrideCtx.strokeStyle = '#e74c3c';
                superOverrideCtx.lineWidth = 3;
                superOverrideCtx.beginPath();
                superOverrideCtx.moveTo(375, 185);
                superOverrideCtx.lineTo(375, 150);
                superOverrideCtx.lineTo(125, 85); // Calls Vehicle's move()
                superOverrideCtx.stroke();
                superOverrideCtx.fillStyle = '#e74c3c';
                superOverrideCtx.fillText('super.move()', 250, 120);
                superOverrideCtx.fillText('调用父类 move()', 375, 210);
            }

            if (step < 1) {
                superOverrideAnimationId = requestAnimationFrame(() => animateSuperOverride(step + 0.01));
            }
        }

        function demonstrateSuperOverride() {
            resetSuperOverride();
            animateSuperOverride(0);
        }

        function resetSuperOverride() {
            cancelAnimationFrame(superOverrideAnimationId);
            superOverrideCtx.clearRect(0, 0, superOverrideCanvas.width, superOverrideCanvas.height);
            drawClassBox(superOverrideCtx, 50, 50, 'Vehicle (move())', true, '#e67e22');
            drawClassBox(superOverrideCtx, 300, 150, 'Car (move(), start())', false, '#3498db');
            superOverrideCtx.fillStyle = '#333';
            superOverrideCtx.font = '14px Arial';
            superOverrideCtx.fillText('继承关系', 225, 120);
            superOverrideCtx.strokeStyle = '#888';
            superOverrideCtx.lineWidth = 2;
            superOverrideCtx.beginPath();
            superOverrideCtx.moveTo(125, 120);
            superOverrideCtx.lineTo(375, 150);
            superOverrideCtx.stroke();
        }

        // 用法三：引用父类构造函数 (super(...))
        const superConstructorCanvas = document.getElementById('superConstructorCanvas');
        const superConstructorCtx = superConstructorCanvas.getContext('2d');
        let superConstructorAnimationId = null;

        function animateSuperConstructor(step) {
            superConstructorCtx.clearRect(0, 0, superConstructorCanvas.width, superConstructorCanvas.height);

            drawBox(superConstructorCtx, 50, 50, '父类构造函数 Shape(color)', '#e67e22');
            drawBox(superConstructorCtx, 350, 150, '子类构造函数 Circle(color,radius)', '#3498db');
            
            superConstructorCtx.fillStyle = '#333';
            superConstructorCtx.font = '16px Arial';
            superConstructorCtx.fillText('调用顺序', 280, 100);

            if (step >= 0.2) {
                superConstructorCtx.strokeStyle = '#e74c3c';
                superConstructorCtx.lineWidth = 3;
                superConstructorCtx.setLineDash([5, 5]);
                superConstructorCtx.beginPath();
                superConstructorCtx.moveTo(425, 150); // From Circle constructor
                superConstructorCtx.lineTo(425, 120);
                superConstructorCtx.lineTo(200, 85); // To Shape constructor
                superConstructorCtx.stroke();
                superConstructorCtx.fillText('super(color);', 320, 110);
            }

            if (step >= 0.5) {
                superConstructorCtx.setLineDash([]);
                superConstructorCtx.fillStyle = '#c0392b';
                superConstructorCtx.font = '18px Arial';
                superConstructorCtx.fillText('Shape(color) 被执行', 125, 200);
            }

            if (step >= 0.8) {
                superConstructorCtx.fillStyle = '#2980b9';
                superConstructorCtx.font = '18px Arial';
                superConstructorCtx.fillText('Circle(color,radius) 被执行', 425, 230);
            }

            if (step < 1) {
                superConstructorAnimationId = requestAnimationFrame(() => animateSuperConstructor(step + 0.01));
            }
        }

        function demonstrateSuperConstructor() {
            resetSuperConstructor();
            animateSuperConstructor(0);
        }

        function resetSuperConstructor() {
            cancelAnimationFrame(superConstructorAnimationId);
            superConstructorCtx.clearRect(0, 0, superConstructorCanvas.width, superConstructorCanvas.height);
            drawBox(superConstructorCtx, 50, 50, '父类构造函数 Shape(color)', '#e67e22');
            drawBox(superConstructorCtx, 350, 150, '子类构造函数 Circle(color,radius)', '#3498db');
            superConstructorCtx.fillStyle = '#333';
            superConstructorCtx.font = '16px Arial';
            superConstructorCtx.fillText('调用顺序', 280, 100);
        }

        // Initial reset for all canvases
        document.addEventListener('DOMContentLoaded', () => {
            resetThisBasic();
            resetThisParameter();
            resetThisConstructor();
            resetBookConstructor(); // Add new reset function
            resetSuperBasic();
            resetSuperOverride();
            resetSuperConstructor();
        });
    </script>
</body>
</html>