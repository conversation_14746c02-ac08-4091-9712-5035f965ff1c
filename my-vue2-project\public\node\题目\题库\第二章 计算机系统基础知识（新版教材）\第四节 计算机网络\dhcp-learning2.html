<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DHCP协议互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        #dhcpCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            color: #333;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .explanation {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .quiz-section {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
        }

        .quiz-question {
            font-size: 1.5rem;
            margin-bottom: 30px;
            text-align: center;
            font-weight: bold;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .quiz-option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: bold;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .quiz-option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00f2fe, #4facfe);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            gap: 20px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #4CAF50;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🌐 DHCP协议互动学习</h1>
            <p class="subtitle">零基础也能轻松掌握网络协议的奥秘</p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 什么是DHCP？</h2>
            <div class="explanation">
                <p><strong>DHCP</strong>（Dynamic Host Configuration Protocol）动态主机配置协议，就像是网络世界的"房屋中介"！</p>
                <p>🏠 想象一下：你刚搬到一个新小区，需要一个门牌号、知道垃圾站在哪、邮局在哪...</p>
                <p>💻 在网络中，设备就像新住户，需要IP地址（门牌号）、网关（出入口）、DNS服务器（电话簿）等信息</p>
                <p>🤝 DHCP服务器就是这个"热心的中介"，自动为每个设备分配这些必要信息！</p>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎬 DHCP工作过程动画演示</h2>
            <div class="canvas-container">
                <canvas id="dhcpCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="startAnimation()">🎯 开始演示</button>
                <button class="btn btn-secondary" onclick="resetAnimation()">🔄 重新开始</button>
                <button class="btn btn-secondary" onclick="pauseAnimation()">⏸️ 暂停</button>
            </div>
            <div class="step-indicator">
                <div class="step" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🧠 知识要点解析</h2>
            <div class="explanation">
                <h3>🔄 DHCP四步握手过程：</h3>
                <p><strong>1. DHCP Discover</strong> - 客户端广播："有人在吗？我需要网络配置！"</p>
                <p><strong>2. DHCP Offer</strong> - 服务器回应："我在！这里有个IP地址给你！"</p>
                <p><strong>3. DHCP Request</strong> - 客户端确认："好的，我要这个IP地址！"</p>
                <p><strong>4. DHCP Ack</strong> - 服务器确认："成交！配置信息已发送！"</p>
                <br>
                <p><strong>💡 题目解析：</strong></p>
                <p>当DHCP服务器收到客户端的<strong>Discover请求</strong>时，会发送<strong>Offer报文</strong>作为回应！</p>
                <p>就像有人问"这里有房子出租吗？"，中介会回答"有的！这里有个房子给你看看！"</p>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title">🎯 互动测试</h2>
            <div class="quiz-question">
                DHCP 服务器收到客户端的请求时，会向客户端发送（ ）报文。
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer('A', false)">
                    <strong>A. DhcpAck</strong><br>
                    <small>确认报文</small>
                </div>
                <div class="quiz-option" onclick="selectAnswer('B', true)">
                    <strong>B. DhcpOffer</strong><br>
                    <small>提供报文</small>
                </div>
                <div class="quiz-option" onclick="selectAnswer('C', false)">
                    <strong>C. DhcpDecline</strong><br>
                    <small>拒绝报文</small>
                </div>
                <div class="quiz-option" onclick="selectAnswer('D', false)">
                    <strong>D. DhcpNack</strong><br>
                    <small>否定确认报文</small>
                </div>
            </div>
            <div id="answerFeedback" style="margin-top: 20px; text-align: center; font-size: 1.2rem; font-weight: bold;"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('dhcpCanvas');
        const ctx = canvas.getContext('2d');

        let animationStep = 0;
        let animationRunning = false;
        let animationPaused = false;

        // 动画对象
        const client = { x: 100, y: 250, radius: 40 };
        const server = { x: 700, y: 250, radius: 40 };
        const message = { x: 100, y: 250, targetX: 700, text: '', visible: false, color: '#4CAF50' };

        // 绘制设备
        function drawDevice(device, label, color, icon) {
            // 设备圆圈
            ctx.beginPath();
            ctx.arc(device.x, device.y, device.radius, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.stroke();

            // 图标
            ctx.font = '24px Arial';
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.fillText(icon, device.x, device.y + 8);

            // 标签
            ctx.font = 'bold 16px Arial';
            ctx.fillStyle = '#333';
            ctx.fillText(label, device.x, device.y + device.radius + 25);
        }

        // 绘制消息
        function drawMessage() {
            if (!message.visible) return;

            // 消息气泡
            const bubbleWidth = 200;
            const bubbleHeight = 60;
            const bubbleX = message.x - bubbleWidth / 2;
            const bubbleY = message.y - 100;

            // 气泡背景
            ctx.fillStyle = message.color;
            ctx.beginPath();
            ctx.roundRect(bubbleX, bubbleY, bubbleWidth, bubbleHeight, 15);
            ctx.fill();

            // 气泡尾巴
            ctx.beginPath();
            ctx.moveTo(message.x - 10, bubbleY + bubbleHeight);
            ctx.lineTo(message.x + 10, bubbleY + bubbleHeight);
            ctx.lineTo(message.x, bubbleY + bubbleHeight + 15);
            ctx.closePath();
            ctx.fill();

            // 消息文本
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            const lines = message.text.split('\n');
            lines.forEach((line, index) => {
                ctx.fillText(line, message.x, bubbleY + 25 + index * 18);
            });
        }

        // 绘制连接线
        function drawConnection() {
            ctx.beginPath();
            ctx.moveTo(client.x + client.radius, client.y);
            ctx.lineTo(server.x - server.radius, server.y);
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.stroke();
            ctx.setLineDash([]);
        }

        // 清除画布并绘制基础元素
        function clearAndDraw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            drawConnection();
            drawDevice(client, 'DHCP客户端', '#2196F3', '💻');
            drawDevice(server, 'DHCP服务器', '#FF9800', '🖥️');
            drawMessage();
        }

        // 动画步骤
        const animationSteps = [
            {
                step: 1,
                message: 'DHCP Discover\n"我需要IP地址！"',
                fromX: client.x,
                toX: server.x,
                color: '#2196F3',
                description: '客户端广播发现请求'
            },
            {
                step: 2,
                message: 'DHCP Offer\n"这里有个IP给你！"',
                fromX: server.x,
                toX: client.x,
                color: '#4CAF50',
                description: '服务器提供IP地址'
            },
            {
                step: 3,
                message: 'DHCP Request\n"我要这个IP！"',
                fromX: client.x,
                toX: server.x,
                color: '#FF5722',
                description: '客户端请求确认'
            },
            {
                step: 4,
                message: 'DHCP Ack\n"配置完成！"',
                fromX: server.x,
                toX: client.x,
                color: '#9C27B0',
                description: '服务器确认分配'
            }
        ];

        // 执行动画步骤
        function executeStep(stepIndex) {
            if (stepIndex >= animationSteps.length) {
                animationRunning = false;
                return;
            }

            const step = animationSteps[stepIndex];

            // 更新步骤指示器
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            document.getElementById(`step${step.step}`).classList.add('active');

            // 设置消息
            message.text = step.message;
            message.color = step.color;
            message.x = step.fromX;
            message.targetX = step.toX;
            message.visible = true;

            // 动画移动消息
            const duration = 2000; // 2秒
            const startTime = Date.now();
            const startX = step.fromX;

            function animateMessage() {
                if (animationPaused) {
                    setTimeout(animateMessage, 100);
                    return;
                }

                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // 缓动函数
                const easeProgress = 1 - Math.pow(1 - progress, 3);

                message.x = startX + (step.toX - startX) * easeProgress;

                clearAndDraw();

                if (progress < 1 && animationRunning) {
                    requestAnimationFrame(animateMessage);
                } else {
                    // 消息到达后停留1秒
                    setTimeout(() => {
                        if (animationRunning) {
                            message.visible = false;
                            clearAndDraw();
                            setTimeout(() => executeStep(stepIndex + 1), 500);
                        }
                    }, 1000);
                }
            }

            animateMessage();
        }

        // 开始动画
        function startAnimation() {
            animationRunning = true;
            animationPaused = false;
            animationStep = 0;
            executeStep(0);
        }

        // 重置动画
        function resetAnimation() {
            animationRunning = false;
            animationPaused = false;
            message.visible = false;
            document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
            clearAndDraw();
        }

        // 暂停/继续动画
        function pauseAnimation() {
            animationPaused = !animationPaused;
            const btn = event.target;
            btn.textContent = animationPaused ? '▶️ 继续' : '⏸️ 暂停';
        }

        // 测试答案选择
        function selectAnswer(option, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const feedback = document.getElementById('answerFeedback');
            const progressFill = document.getElementById('progressFill');

            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt.textContent.includes(option)) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                }
            });

            if (isCorrect) {
                feedback.innerHTML = '🎉 恭喜答对了！<br>DHCP Offer 是服务器对客户端请求的回应报文！';
                feedback.style.color = '#4CAF50';
                progressFill.style.width = '100%';
            } else {
                feedback.innerHTML = '❌ 答案不对哦！<br>正确答案是 B. DhcpOffer<br>这是DHCP四步握手的第二步！';
                feedback.style.color = '#f44336';
                progressFill.style.width = '25%';

                // 显示正确答案
                setTimeout(() => {
                    options.forEach(opt => {
                        if (opt.textContent.includes('B.')) {
                            opt.classList.add('correct');
                        }
                    });
                }, 1000);
            }
        }

        // 初始化
        clearAndDraw();
    </script>
</body>
</html>
