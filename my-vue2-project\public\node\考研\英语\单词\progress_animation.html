<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画：progress</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            font-size: 3em;
            color: #1a73e8;
            margin-bottom: 10px;
        }
        #word-pronunciation {
            font-size: 1.2em;
            color: #5f6368;
            margin-bottom: 20px;
        }
        canvas {
            background: #ffffff;
            border: 1px solid #dcdcdc;
            border-radius: 8px;
            margin-bottom: 20px;
            cursor: pointer;
        }
        .explanation {
            text-align: left;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
        }
        .explanation h2 {
            color: #1a73e8;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 5px;
            margin-top: 0;
        }
        .explanation p {
            font-size: 1.1em;
            line-height: 1.8;
        }
        .explanation .highlight {
            font-weight: bold;
            color: #d93025;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin-top: 20px;
        }
        button:hover {
            background-color: #155ab6;
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        #interactive-instruction {
            color: #5f6368;
            margin-top: 10px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>progress</h1>
        <p id="word-pronunciation">[ˈprəʊɡres] / "普肉GRESS"</p>
        
        <canvas id="wordCanvas" width="600" height="300"></canvas>
        <p id="interactive-instruction">点击画布或按钮开始动画</p>
        <button id="playButton">播放动画</button>

        <div class="explanation">
            <h2>单词拆解教学 📖</h2>
            <p>
                你好！今天我们来学习"进步"的旅程，这个单词就是 <span class="highlight">progress</span>。
                它也是由两块积木组成的哦！
            </p>
            <p>
                1. 前缀 <span class="highlight">pro-</span>：这个前缀表示"<span class="highlight">向前</span>"。比如，`propel`（推进），`promote`（促进）。
            </p>
            <p>
                2. 词根 <span class="highlight">-gress-</span>：这个词根来自拉丁语，意思是"<span class="highlight">行走，前进</span>"。是不是很有动感？比如 `aggressive`（有攻击性的，有冲劲的），`congress`（走到一起来，国会）。
            </p>
            <p>
                所以，我们把它们合在一起：<span class="highlight">pro (向前)</span> + <span class="highlight">gress (行走)</span> = <span class="highlight">progress (向前走)</span>。
                这不就是"前进"、"进步"的意思吗？一下子就记住了！
            </p>
            <h2>翻译与用法 ✍️</h2>
            <p>
                <b>名词 (n.):</b> [ˈprəʊɡres]
                <ul>
                    <li><b>进步，进展:</b> We have made great <em>progress</em> in technology. (我们在科技上取得了巨大进步。)</li>
                    <li><b>前进:</b> The book describes the <em>progress</em> of the storm. (这本书描述了风暴的前进过程。)</li>
                </ul>
            </p>
            <p>
                <b>不及物动词 (vi.):</b> [prəˈɡres] (注意发音变化)
                <ul>
                    <li><b>前进，前行:</b> As the river <em>progresses</em>, it gets wider. (随着河流的前进，河面变宽了。)</li>
                    <li><b>进步，进展:</b> His English is <em>progressing</em> steadily. (他的英语在稳步进步。)</li>
                </ul>
            </p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('wordCanvas');
        const ctx = canvas.getContext('2d');
        const playButton = document.getElementById('playButton');

        let animationState = 'initial';
        let frame = 0;
        const totalFrames = 450;

        function drawCharacter(x, y) {
            // Body
            ctx.beginPath();
            ctx.moveTo(x, y - 40);
            ctx.lineTo(x, y);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 4;
            ctx.stroke();
            // Head
            ctx.beginPath();
            ctx.arc(x, y - 50, 10, 0, Math.PI * 2);
            ctx.fillStyle = '#333';
            ctx.fill();
            // Legs
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x - 10, y + 20);
            ctx.moveTo(x, y);
            ctx.lineTo(x + 10, y + 20);
            ctx.stroke();
            // Arms
            ctx.beginPath();
            ctx.moveTo(x, y - 30);
            ctx.lineTo(x - 15, y - 15);
            ctx.moveTo(x, y - 30);
            ctx.lineTo(x + 15, y - 15);
            ctx.stroke();
        }

        function drawInitial() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            // Ground
            ctx.beginPath();
            ctx.moveTo(0, 250);
            ctx.lineTo(canvas.width, 250);
            ctx.strokeStyle = '#999';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            drawCharacter(100, 230);
            
            ctx.font = '24px Arial';
            ctx.fillStyle = '#555';
            ctx.textAlign = 'center';
            ctx.fillText('起点', 100, 150);
        }

        function animate() {
            frame++;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Ground
            ctx.beginPath();
            ctx.moveTo(0, 250);
            ctx.lineTo(canvas.width, 250);
            ctx.strokeStyle = '#999';
            ctx.lineWidth = 2;
            ctx.stroke();

            if (animationState === 'initial') {
                drawInitial();
                return;
            }
            
            let charX = 100;

            // Phase 1: Show "pro-"
            if(frame > 30 && frame < 300) {
                 const alpha = Math.min(1, (frame - 30) / 50);
                 ctx.font = 'bold 70px Arial';
                 ctx.fillStyle = `rgba(217, 48, 37, ${alpha})`;
                 ctx.fillText('pro-', 250, 120);
                 ctx.font = 'bold 30px Arial';
                 ctx.fillText('向前', 250, 170);
            }
            
            // Phase 2: Show "gress" and move
            if(frame > 120 && frame < 300) {
                 const alpha = Math.min(1, (frame - 120) / 50);
                 ctx.font = 'bold 70px Arial';
                 ctx.fillStyle = `rgba(26, 115, 232, ${alpha})`;
                 ctx.fillText('-gress', 450, 120);
                 ctx.font = 'bold 30px Arial';
                 ctx.fillText('行走', 450, 170);
            }

            if(frame > 200 && frame < 350) {
                const progress = (frame - 200) / 150;
                charX = 100 + progress * 300;
            } else if (frame >= 350) {
                charX = 400;
            }
            
            drawCharacter(charX, 230);
            ctx.fillText('起点', 100, 150);


            // Phase 3: Show "progress"
            if (frame > 300) {
                 ctx.clearRect(0, 0, canvas.width, canvas.height);
                 const alpha = Math.min(1, (frame - 300) / 80);
                 ctx.font = 'bold 90px Arial';
                 ctx.fillStyle = `rgba(26, 115, 232, ${alpha})`;
                 ctx.textAlign = 'center';
                 ctx.fillText('progress', canvas.width / 2, 150);
                 
                 ctx.font = '40px Arial';
                 ctx.fillStyle = `rgba(51, 51, 51, ${alpha})`;
                 ctx.fillText('向前走 = 进步 / 发展', canvas.width / 2, 230);
            }

            if (frame < totalFrames) {
                requestAnimationFrame(animate);
            } else {
                animationState = 'finished';
                playButton.textContent = '重新播放';
            }
        }

        function startAnimation() {
            if (animationState === 'finished' || animationState === 'initial') {
                frame = 0;
                animationState = 'playing';
                playButton.textContent = '播放中...';
                requestAnimationFrame(animate);
            }
        }

        canvas.addEventListener('click', startAnimation);
        playButton.addEventListener('click', startAnimation);

        drawInitial();
    </script>
</body>
</html> 