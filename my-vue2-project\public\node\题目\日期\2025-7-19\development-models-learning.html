<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件开发模型互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .models-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .models-demo {
            text-align: center;
            margin: 30px 0;
        }

        #modelsCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .model-controls {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 25px 0;
        }

        .model-btn {
            padding: 15px 10px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .rad-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .spiral-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .rup-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .fountain-btn {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }

        .model-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .model-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .model-features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .feature-card.rad {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .feature-card.spiral {
            border-color: #fd79a8;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .feature-card.rup {
            border-color: #00b894;
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }

        .feature-card.fountain {
            border-color: #fdcb6e;
            background: linear-gradient(135deg, #fdcb6e, #e17055);
            color: white;
        }

        .quiz-question {
            font-size: 1.2rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-fountain {
            color: #e17055;
            font-weight: bold;
            background: rgba(253,203,110,0.2);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-model {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: floatModel 20s infinite ease-in-out;
        }

        .model1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .model2 {
            top: 70%;
            right: 15%;
            animation-delay: 7s;
        }

        .model3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 14s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatModel {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-40px) rotate(90deg); }
            50% { transform: translateY(20px) rotate(180deg); }
            75% { transform: translateY(-20px) rotate(270deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .model-controls {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .model-features {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-model model1"></div>
        <div class="floating-model model2"></div>
        <div class="floating-model model3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🔄 软件开发模型学习</h1>
            <p>通过互动动画深度理解各种开发模型的特点与适用场景</p>
        </div>

        <div class="main-grid">
            <div class="models-section">
                <h2 class="section-title">🛠️ 开发模型演示</h2>
                
                <div class="models-demo">
                    <canvas id="modelsCanvas" width="700" height="400"></canvas>
                </div>

                <div class="model-controls">
                    <button class="model-btn rad-btn" onclick="demonstrateModel('rad')">
                        RAD模型<br><small>快速应用开发</small>
                    </button>
                    <button class="model-btn spiral-btn" onclick="demonstrateModel('spiral')">
                        螺旋模型<br><small>风险驱动</small>
                    </button>
                    <button class="model-btn rup-btn" onclick="demonstrateModel('rup')">
                        RUP模型<br><small>统一过程</small>
                    </button>
                    <button class="model-btn fountain-btn" onclick="demonstrateModel('fountain')">
                        喷泉模型<br><small>面向对象</small>
                    </button>
                </div>

                <div class="model-features">
                    <div class="feature-card rad">
                        <h3>🚀 RAD模型</h3>
                        <p>• 复用好<br>• 开发过程无间隙<br>• 节省时间<br>• 快速原型</p>
                    </div>
                    <div class="feature-card spiral">
                        <h3>🌀 螺旋模型</h3>
                        <p>• 瀑布与原型结合<br>• 适用复杂项目<br>• 风险驱动<br>• 迭代开发</p>
                    </div>
                    <div class="feature-card rup">
                        <h3>🎯 RUP模型</h3>
                        <p>• 用例驱动<br>• 架构为中心<br>• 迭代增量<br>• 统一过程</p>
                    </div>
                    <div class="feature-card fountain">
                        <h3>⛲ 喷泉模型</h3>
                        <p>• 需要用户参与<br>• 模块化要求高<br>• 不适用新技术<br>• 面向对象</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 对于开发模型来说，<strong>（　　）复用好、开发过程无间隙、节省时间</strong>。（　　）是瀑布与原型（演化）模型结合体，适用于复杂项目。（　　）需要用户参与，模块化要求高，不适用新技术。（　　）是用例驱动、架构为中心、迭代、增量。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. RAD模型
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 螺旋模型
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. RUP模型
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        D. 喷泉模型
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：D. 喷泉模型</strong></p>
                    <p>根据题目描述的特征匹配：</p>
                    <ul>
                        <li><strong>第一空</strong>：<span class="highlight-fountain">喷泉模型</span> - 复用好、开发过程无间隙、节省时间</li>
                        <li><strong>第二空</strong>：螺旋模型 - 瀑布与原型模型结合体，适用于复杂项目</li>
                        <li><strong>第三空</strong>：喷泉模型 - 需要用户参与，模块化要求高，不适用新技术</li>
                        <li><strong>第四空</strong>：RUP模型 - 用例驱动、架构为中心、迭代、增量</li>
                    </ul>
                    <p><strong>喷泉模型特点</strong>：</p>
                    <ul>
                        <li>面向对象的开发模型</li>
                        <li>各个开发阶段没有明显的界限，可以交叉进行</li>
                        <li>支持软件重用，提高开发效率</li>
                        <li>需要用户的积极参与</li>
                        <li>对模块化程度要求较高</li>
                    </ul>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了各种开发模型的特点！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('modelsCanvas');
        const ctx = canvas.getContext('2d');
        let currentModel = 'fountain';
        let animationId = null;

        // 演示不同开发模型
        function demonstrateModel(modelType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            currentModel = modelType;
            
            // 更新按钮状态
            document.querySelectorAll('.model-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${modelType}-btn`).classList.add('active');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(modelType) {
                case 'rad':
                    drawRADModel();
                    break;
                case 'spiral':
                    drawSpiralModel();
                    break;
                case 'rup':
                    drawRUPModel();
                    break;
                case 'fountain':
                    drawFountainModel();
                    break;
            }
        }

        // 绘制RAD模型
        function drawRADModel() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('RAD模型 - 快速应用开发', 350, 40);

            // 绘制快速开发流程
            const phases = ['需求分析', '快速设计', '构造原型', '用户评估'];
            const colors = ['#74b9ff', '#0984e3', '#74b9ff', '#0984e3'];
            
            phases.forEach((phase, index) => {
                const x = 100 + index * 130;
                const y = 150;
                
                ctx.fillStyle = colors[index];
                ctx.fillRect(x, y, 100, 60);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 3;
                ctx.strokeRect(x, y, 100, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(phase, x + 50, y + 35);
                
                // 箭头
                if (index < phases.length - 1) {
                    ctx.strokeStyle = '#2d3436';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(x + 100, y + 30);
                    ctx.lineTo(x + 130, y + 30);
                    ctx.stroke();
                    
                    // 箭头头部
                    ctx.fillStyle = '#2d3436';
                    ctx.beginPath();
                    ctx.moveTo(x + 130, y + 30);
                    ctx.lineTo(x + 120, y + 25);
                    ctx.lineTo(x + 120, y + 35);
                    ctx.closePath();
                    ctx.fill();
                }
            });

            // 特点标注
            ctx.fillStyle = '#e17055';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 复用好', 100, 280);
            ctx.fillText('✓ 开发过程无间隙', 250, 280);
            ctx.fillText('✓ 节省时间', 450, 280);
        }

        // 绘制螺旋模型
        function drawSpiralModel() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('螺旋模型 - 风险驱动', 350, 40);

            // 绘制螺旋
            const centerX = 350;
            const centerY = 200;
            let radius = 20;
            let angle = 0;
            
            ctx.strokeStyle = '#fd79a8';
            ctx.lineWidth = 4;
            ctx.beginPath();
            
            for (let i = 0; i < 200; i++) {
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                
                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
                
                angle += 0.1;
                radius += 0.5;
            }
            ctx.stroke();

            // 象限标注
            const quadrants = ['计划', '风险分析', '实施', '评估'];
            const positions = [
                {x: 450, y: 120},
                {x: 450, y: 280},
                {x: 250, y: 280},
                {x: 250, y: 120}
            ];
            
            quadrants.forEach((quad, index) => {
                ctx.fillStyle = '#e84393';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(quad, positions[index].x, positions[index].y);
            });

            // 特点
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('瀑布与原型结合 • 适用复杂项目', 350, 350);
        }

        // 绘制RUP模型
        function drawRUPModel() {
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('RUP模型 - 统一过程', 350, 40);

            // 绘制四个阶段
            const phases = ['初始', '细化', '构造', '移交'];
            const workflows = ['需求', '分析设计', '实现', '测试'];
            
            // 绘制阶段
            phases.forEach((phase, index) => {
                const x = 100 + index * 130;
                ctx.fillStyle = '#00b894';
                ctx.fillRect(x, 80, 100, 40);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, 80, 100, 40);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(phase, x + 50, 105);
            });

            // 绘制工作流
            workflows.forEach((workflow, index) => {
                const y = 150 + index * 40;
                ctx.fillStyle = '#00a085';
                ctx.fillRect(50, y, 80, 30);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(50, y, 80, 30);
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(workflow, 90, y + 20);
                
                // 绘制迭代线
                ctx.strokeStyle = '#74b9ff';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(130, y + 15);
                ctx.lineTo(630, y + 15);
                ctx.stroke();
            });

            // 特点
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('用例驱动 • 架构为中心 • 迭代增量', 350, 350);
        }

        // 绘制喷泉模型
        function drawFountainModel() {
            ctx.fillStyle = '#fdcb6e';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('喷泉模型 - 面向对象', 350, 40);

            // 绘制喷泉效果
            const centerX = 350;
            const centerY = 300;
            
            // 绘制水滴
            for (let i = 0; i < 50; i++) {
                const angle = (Math.PI * 2 * i) / 50;
                const distance = 80 + Math.sin(Date.now() * 0.01 + i) * 20;
                const x = centerX + Math.cos(angle) * distance;
                const y = centerY - Math.abs(Math.sin(angle)) * distance;
                
                ctx.fillStyle = `rgba(253, 203, 110, ${0.3 + Math.sin(Date.now() * 0.01 + i) * 0.3})`;
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, Math.PI * 2);
                ctx.fill();
            }

            // 中心圆
            ctx.fillStyle = '#e17055';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 30, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('核心', centerX, centerY + 5);

            // 周围的开发活动
            const activities = ['分析', '设计', '编码', '测试'];
            activities.forEach((activity, index) => {
                const angle = (Math.PI * 2 * index) / activities.length;
                const x = centerX + Math.cos(angle) * 120;
                const y = centerY + Math.sin(angle) * 120;
                
                ctx.fillStyle = '#fdcb6e';
                ctx.beginPath();
                ctx.arc(x, y, 25, 0, Math.PI * 2);
                ctx.fill();
                ctx.strokeStyle = '#e17055';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(activity, x, y + 4);
            });

            // 特点
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('用户参与 • 模块化要求高 • 不适用新技术', 350, 80);

            // 继续动画
            animationId = requestAnimationFrame(drawFountainModel);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('D. 喷泉模型')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画
                    demonstrateModel('fountain');
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateModel('fountain');
            
            // 自动演示序列
            setTimeout(() => demonstrateModel('rad'), 3000);
            setTimeout(() => demonstrateModel('spiral'), 6000);
            setTimeout(() => demonstrateModel('rup'), 9000);
            setTimeout(() => demonstrateModel('fountain'), 12000);
        };
    </script>
</body>
</html>
