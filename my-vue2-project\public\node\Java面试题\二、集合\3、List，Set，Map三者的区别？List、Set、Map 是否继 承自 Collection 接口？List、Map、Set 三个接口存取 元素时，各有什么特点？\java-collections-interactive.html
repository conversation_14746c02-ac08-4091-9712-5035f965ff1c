<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java集合框架 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2.2rem;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: 600;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .game-score {
            text-align: center;
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
            margin: 10px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Java集合框架探索之旅</h1>
            <p>通过动画和交互游戏，轻松掌握List、Set、Map的核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🏗️ 集合框架总览</h2>
            <div class="explanation">
                <p><span class="highlight">Java集合框架</span>是Java中用于存储和操作对象集合的统一架构。它分为两大类：</p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><strong>Collection接口</strong>：单一元素的集合（List、Set、Queue）</li>
                    <li><strong>Map接口</strong>：键值对的集合（不继承Collection）</li>
                </ul>
            </div>
            <div class="canvas-container">
                <canvas id="overviewCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="animateOverview()">🎬 播放动画</button>
                <button class="btn" onclick="resetOverview()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📋 List - 有序可重复列表</h2>
            <div class="explanation">
                <p><span class="highlight">List特点</span>：有序（存入和取出顺序一致）、可重复、可插入多个null、元素有索引</p>
                <p><strong>常用实现类</strong>：ArrayList、LinkedList、Vector</p>
            </div>
            <div class="canvas-container">
                <canvas id="listCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="addToList()">➕ 添加元素</button>
                <button class="btn" onclick="removeFromList()">➖ 删除元素</button>
                <button class="btn" onclick="clearList()">🗑️ 清空</button>
            </div>
            <div class="game-score" id="listScore">操作次数: 0</div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 Set - 无序不重复集合</h2>
            <div class="explanation">
                <p><span class="highlight">Set特点</span>：无序（存入和取出顺序可能不一致）、不可重复、只允许一个null、保证元素唯一性</p>
                <p><strong>常用实现类</strong>：HashSet、LinkedHashSet、TreeSet</p>
            </div>
            <div class="canvas-container">
                <canvas id="setCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="addToSet()">➕ 添加元素</button>
                <button class="btn" onclick="addDuplicateToSet()">🔄 添加重复元素</button>
                <button class="btn" onclick="clearSet()">🗑️ 清空</button>
            </div>
            <div class="game-score" id="setScore">唯一元素: 0</div>
        </div>

        <div class="section">
            <h2 class="section-title">🗺️ Map - 键值对映射</h2>
            <div class="explanation">
                <p><span class="highlight">Map特点</span>：键值对存储、Key无序且唯一、Value允许重复、不继承Collection接口</p>
                <p><strong>常用实现类</strong>：HashMap、TreeMap、HashTable、LinkedHashMap、ConcurrentHashMap</p>
            </div>
            <div class="canvas-container">
                <canvas id="mapCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="addToMap()">➕ 添加键值对</button>
                <button class="btn" onclick="updateMapValue()">🔄 更新值</button>
                <button class="btn" onclick="clearMap()">🗑️ 清空</button>
            </div>
            <div class="game-score" id="mapScore">键值对数量: 0</div>
        </div>

        <div class="section">
            <h2 class="section-title">🏗️ Map实现类详解</h2>
            <div class="explanation">
                <p>不同的Map实现类有着不同的特性和适用场景，让我们通过交互演示来了解它们的区别：</p>
            </div>

            <!-- Map实现类选择器 -->
            <div class="controls">
                <button class="btn" onclick="switchMapType('HashMap')" id="hashMapBtn">HashMap</button>
                <button class="btn" onclick="switchMapType('TreeMap')" id="treeMapBtn">TreeMap</button>
                <button class="btn" onclick="switchMapType('HashTable')" id="hashTableBtn">HashTable</button>
                <button class="btn" onclick="switchMapType('LinkedHashMap')" id="linkedHashMapBtn">LinkedHashMap</button>
                <button class="btn" onclick="switchMapType('ConcurrentHashMap')" id="concurrentHashMapBtn">ConcurrentHashMap</button>
            </div>

            <div class="canvas-container">
                <canvas id="mapTypesCanvas" width="900" height="400"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="addToCurrentMap()">➕ 添加元素</button>
                <button class="btn" onclick="demonstrateFeature()">✨ 演示特性</button>
                <button class="btn" onclick="clearCurrentMap()">🗑️ 清空</button>
            </div>

            <div id="mapTypeInfo" class="explanation">
                <h3 id="currentMapTitle">选择一个Map实现类开始探索</h3>
                <div id="currentMapDescription"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📋 Map实现类对比表</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>实现类</th>
                        <th>是否有序</th>
                        <th>线程安全</th>
                        <th>null值支持</th>
                        <th>性能特点</th>
                        <th>适用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>HashMap</strong></td>
                        <td>❌ 无序</td>
                        <td>❌ 非线程安全</td>
                        <td>✅ Key和Value都可为null</td>
                        <td>O(1) 查找，最快</td>
                        <td>单线程环境，高性能需求</td>
                    </tr>
                    <tr>
                        <td><strong>TreeMap</strong></td>
                        <td>✅ 按Key排序</td>
                        <td>❌ 非线程安全</td>
                        <td>❌ Key不可为null</td>
                        <td>O(log n) 查找</td>
                        <td>需要排序的场景</td>
                    </tr>
                    <tr>
                        <td><strong>HashTable</strong></td>
                        <td>❌ 无序</td>
                        <td>✅ 线程安全</td>
                        <td>❌ Key和Value都不可为null</td>
                        <td>O(1) 查找，但有同步开销</td>
                        <td>多线程环境（已过时）</td>
                    </tr>
                    <tr>
                        <td><strong>LinkedHashMap</strong></td>
                        <td>✅ 插入顺序或访问顺序</td>
                        <td>❌ 非线程安全</td>
                        <td>✅ Key和Value都可为null</td>
                        <td>O(1) 查找，略慢于HashMap</td>
                        <td>需要保持插入顺序</td>
                    </tr>
                    <tr>
                        <td><strong>ConcurrentHashMap</strong></td>
                        <td>❌ 无序</td>
                        <td>✅ 线程安全</td>
                        <td>❌ Key和Value都不可为null</td>
                        <td>O(1) 查找，高并发性能好</td>
                        <td>高并发多线程环境</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2 class="section-title">📊 三者特性对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>特性</th>
                        <th>List</th>
                        <th>Set</th>
                        <th>Map</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>是否有序</strong></td>
                        <td>✅ 有序（按插入顺序）</td>
                        <td>❌ 无序（HashSet）</td>
                        <td>❌ Key无序</td>
                    </tr>
                    <tr>
                        <td><strong>是否允许重复</strong></td>
                        <td>✅ 允许重复元素</td>
                        <td>❌ 不允许重复</td>
                        <td>Key不重复，Value可重复</td>
                    </tr>
                    <tr>
                        <td><strong>null值</strong></td>
                        <td>✅ 可插入多个null</td>
                        <td>✅ 只允许一个null</td>
                        <td>Key可以有一个null</td>
                    </tr>
                    <tr>
                        <td><strong>索引访问</strong></td>
                        <td>✅ 支持索引访问</td>
                        <td>❌ 不支持索引</td>
                        <td>❌ 通过Key访问</td>
                    </tr>
                    <tr>
                        <td><strong>继承关系</strong></td>
                        <td>继承Collection</td>
                        <td>继承Collection</td>
                        <td>❌ 不继承Collection</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 知识测试游戏</h2>
            <div class="explanation">
                <p>通过下面的小游戏来测试你对Java集合的理解！</p>
            </div>
            <div id="quizContainer">
                <div id="question" style="font-size: 1.2rem; margin: 20px 0; text-align: center;"></div>
                <div class="controls">
                    <button class="btn" onclick="startQuiz()">🎯 开始测试</button>
                    <button class="btn" onclick="nextQuestion()" id="nextBtn" style="display: none;">➡️ 下一题</button>
                </div>
                <div id="quizResult" style="text-align: center; margin: 20px 0; font-size: 1.1rem;"></div>
            </div>
        </div>
    </div>

    <script>
        let progress = 0;
        let listOperations = 0;
        let listData = [];
        let setData = new Set();
        let setOperations = 0;
        let mapData = new Map();
        let mapOperations = 0;
        let currentMapType = 'HashMap';
        let mapTypeData = {
            HashMap: new Map(),
            TreeMap: new Map(),
            HashTable: new Map(),
            LinkedHashMap: new Map(),
            ConcurrentHashMap: new Map()
        };
        let mapTypeDescriptions = {
            HashMap: {
                title: 'HashMap - 高性能哈希表',
                description: '基于哈希表实现，提供O(1)的查找性能。无序存储，允许null键和值。是最常用的Map实现。',
                features: ['无序存储', '允许null', '非线程安全', 'O(1)性能', '最常用']
            },
            TreeMap: {
                title: 'TreeMap - 有序的红黑树',
                description: '基于红黑树实现，按键的自然顺序或自定义比较器排序。查找性能O(log n)。',
                features: ['按键排序', '不允许null键', '非线程安全', 'O(log n)性能', '有序遍历']
            },
            HashTable: {
                title: 'HashTable - 线程安全的哈希表',
                description: '线程安全的哈希表实现，但性能较差。现在推荐使用ConcurrentHashMap。',
                features: ['线程安全', '不允许null', '同步开销大', '已过时', '遗留类']
            },
            LinkedHashMap: {
                title: 'LinkedHashMap - 保持插入顺序',
                description: '继承HashMap，额外维护插入顺序或访问顺序。适合需要保持顺序的场景。',
                features: ['保持插入顺序', '允许null', '非线程安全', '略慢于HashMap', 'LRU缓存']
            },
            ConcurrentHashMap: {
                title: 'ConcurrentHashMap - 高并发哈希表',
                description: '线程安全的高性能Map实现，使用分段锁技术，适合高并发场景。',
                features: ['线程安全', '不允许null', '高并发性能', '分段锁', '推荐使用']
            }
        };
        let quizQuestions = [
            {
                question: "List的主要特点是什么？",
                options: ["有序、可重复", "无序、不重复", "键值对存储"],
                correct: 0
            },
            {
                question: "Set集合允许存储重复元素吗？",
                options: ["允许", "不允许", "有时允许"],
                correct: 1
            },
            {
                question: "Map接口继承自Collection接口吗？",
                options: ["是", "否", "部分继承"],
                correct: 1
            },
            {
                question: "哪个集合支持通过索引访问元素？",
                options: ["Set", "Map", "List"],
                correct: 2
            },
            {
                question: "HashMap和TreeMap的主要区别是什么？",
                options: ["HashMap有序，TreeMap无序", "HashMap无序，TreeMap按键排序", "没有区别"],
                correct: 1
            },
            {
                question: "哪个Map实现类是线程安全的？",
                options: ["HashMap", "TreeMap", "ConcurrentHashMap"],
                correct: 2
            },
            {
                question: "TreeMap不允许什么类型的键？",
                options: ["String类型", "null值", "Integer类型"],
                correct: 1
            },
            {
                question: "LinkedHashMap的特殊之处是什么？",
                options: ["线程安全", "保持插入顺序", "自动排序"],
                correct: 1
            },
            {
                question: "现在推荐使用哪个替代HashTable？",
                options: ["HashMap", "TreeMap", "ConcurrentHashMap"],
                correct: 2
            },
            {
                question: "哪个Map实现类查找性能是O(log n)？",
                options: ["HashMap", "TreeMap", "LinkedHashMap"],
                correct: 1
            }
        ];
        let currentQuestionIndex = 0;
        let quizScore = 0;

        // 更新进度条
        function updateProgress(value) {
            progress = Math.min(100, progress + value);
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 总览动画
        function animateOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制Collection分支
                drawAnimatedBox(ctx, 150, 100, 200, 80, 'Collection接口', '#667eea', frame);
                
                // 绘制子接口
                if (frame > 30) {
                    drawAnimatedBox(ctx, 50, 220, 120, 60, 'List', '#4CAF50', frame - 30);
                    drawAnimatedBox(ctx, 200, 220, 120, 60, 'Set', '#FF9800', frame - 30);
                    drawAnimatedBox(ctx, 350, 220, 120, 60, 'Queue', '#9C27B0', frame - 30);
                }
                
                // 绘制Map
                if (frame > 60) {
                    drawAnimatedBox(ctx, 550, 100, 200, 80, 'Map接口', '#F44336', frame - 60);
                    
                    // 绘制连接线和说明
                    ctx.strokeStyle = '#666';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.moveTo(350, 140);
                    ctx.lineTo(550, 140);
                    ctx.stroke();
                    
                    ctx.fillStyle = '#666';
                    ctx.font = '14px Arial';
                    ctx.fillText('不继承Collection', 400, 130);
                }
                
                frame++;
                if (frame < 120) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress(20);
                }
            }
            animate();
        }

        function drawAnimatedBox(ctx, x, y, width, height, text, color, frame) {
            const scale = Math.min(1, frame / 20);
            const currentWidth = width * scale;
            const currentHeight = height * scale;
            const currentX = x + (width - currentWidth) / 2;
            const currentY = y + (height - currentHeight) / 2;
            
            // 绘制阴影
            ctx.fillStyle = 'rgba(0,0,0,0.1)';
            ctx.fillRect(currentX + 3, currentY + 3, currentWidth, currentHeight);
            
            // 绘制主体
            ctx.fillStyle = color;
            ctx.fillRect(currentX, currentY, currentWidth, currentHeight);
            
            // 绘制边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.strokeRect(currentX, currentY, currentWidth, currentHeight);
            
            // 绘制文字
            if (scale > 0.5) {
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(text, x + width/2, y + height/2 + 5);
            }
        }

        function resetOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // List操作
        function addToList() {
            const canvas = document.getElementById('listCanvas');
            const ctx = canvas.getContext('2d');
            
            const newElement = Math.floor(Math.random() * 100);
            listData.push(newElement);
            listOperations++;
            
            drawList(ctx);
            updateListScore();
            updateProgress(5);
        }

        function removeFromList() {
            if (listData.length > 0) {
                listData.pop();
                listOperations++;
                
                const canvas = document.getElementById('listCanvas');
                const ctx = canvas.getContext('2d');
                drawList(ctx);
                updateListScore();
                updateProgress(5);
            }
        }

        function clearList() {
            listData = [];
            listOperations++;
            
            const canvas = document.getElementById('listCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            updateListScore();
            updateProgress(10);
        }

        function drawList(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
            
            // 绘制List标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('List - 有序可重复', ctx.canvas.width/2, 30);
            
            // 绘制索引和元素
            const startX = 50;
            const startY = 80;
            const boxWidth = 60;
            const boxHeight = 40;
            
            for (let i = 0; i < listData.length && i < 10; i++) {
                const x = startX + i * (boxWidth + 10);
                
                // 绘制索引
                ctx.fillStyle = '#667eea';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`[${i}]`, x + boxWidth/2, startY - 10);
                
                // 绘制元素框
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(x, startY, boxWidth, boxHeight);
                
                // 绘制边框
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, startY, boxWidth, boxHeight);
                
                // 绘制元素值
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(listData[i], x + boxWidth/2, startY + boxHeight/2 + 5);
                
                // 添加动画效果
                if (i === listData.length - 1) {
                    ctx.strokeStyle = '#FFD700';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(x - 2, startY - 2, boxWidth + 4, boxHeight + 4);
                }
            }
            
            // 绘制特性说明
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 有序：按添加顺序排列', 50, startY + 80);
            ctx.fillText('✓ 可重复：允许相同元素', 50, startY + 100);
            ctx.fillText('✓ 有索引：可通过索引访问', 50, startY + 120);
        }

        function updateListScore() {
            document.getElementById('listScore').textContent = `操作次数: ${listOperations}`;
        }

        // Set操作
        function addToSet() {
            const newElement = Math.floor(Math.random() * 50);
            const sizeBefore = setData.size;
            setData.add(newElement);

            if (setData.size > sizeBefore) {
                setOperations++;
                showSetMessage(`添加了新元素: ${newElement}`, '#4CAF50');
            } else {
                showSetMessage(`元素 ${newElement} 已存在，Set不允许重复！`, '#FF9800');
            }

            const canvas = document.getElementById('setCanvas');
            const ctx = canvas.getContext('2d');
            drawSet(ctx);
            updateSetScore();
            updateProgress(5);
        }

        function addDuplicateToSet() {
            if (setData.size > 0) {
                const existingElement = Array.from(setData)[0];
                setData.add(existingElement);
                showSetMessage(`尝试添加重复元素 ${existingElement}，但Set拒绝了！`, '#F44336');

                const canvas = document.getElementById('setCanvas');
                const ctx = canvas.getContext('2d');
                drawSet(ctx);
            } else {
                showSetMessage('请先添加一些元素！', '#666');
            }
        }

        function clearSet() {
            setData.clear();
            setOperations++;

            const canvas = document.getElementById('setCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            updateSetScore();
            updateProgress(10);
        }

        function drawSet(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            // 绘制Set标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Set - 无序不重复', ctx.canvas.width/2, 30);

            // 绘制元素（圆形表示无序）
            const elements = Array.from(setData);
            const centerX = ctx.canvas.width / 2;
            const centerY = 120;
            const radius = 80;

            elements.forEach((element, index) => {
                const angle = (index / elements.length) * 2 * Math.PI;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;

                // 绘制元素圆圈
                ctx.beginPath();
                ctx.arc(x, y, 25, 0, 2 * Math.PI);
                ctx.fillStyle = '#FF9800';
                ctx.fill();
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 3;
                ctx.stroke();

                // 绘制元素值
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(element, x, y + 5);
            });

            // 绘制特性说明
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 无序：元素位置随机', 50, 220);
            ctx.fillText('✓ 唯一：自动去重', 50, 240);
            ctx.fillText('✓ 高效：快速查找', 50, 260);
        }

        function showSetMessage(message, color) {
            const canvas = document.getElementById('setCanvas');
            const ctx = canvas.getContext('2d');

            // 显示消息
            ctx.fillStyle = color;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(message, canvas.width/2, 50);

            // 3秒后清除消息
            setTimeout(() => {
                drawSet(ctx);
            }, 3000);
        }

        function updateSetScore() {
            document.getElementById('setScore').textContent = `唯一元素: ${setData.size}`;
        }

        // Map操作
        function addToMap() {
            const keys = ['姓名', '年龄', '城市', '职业', '爱好', '学历'];
            const values = ['张三', '25', '北京', '程序员', '编程', '本科'];

            const randomKey = keys[Math.floor(Math.random() * keys.length)];
            const randomValue = values[Math.floor(Math.random() * values.length)];

            mapData.set(randomKey, randomValue);
            mapOperations++;

            const canvas = document.getElementById('mapCanvas');
            const ctx = canvas.getContext('2d');
            drawMap(ctx);
            updateMapScore();
            updateProgress(5);
        }

        function updateMapValue() {
            if (mapData.size > 0) {
                const keys = Array.from(mapData.keys());
                const randomKey = keys[Math.floor(Math.random() * keys.length)];
                const newValue = '更新值' + Math.floor(Math.random() * 100);

                mapData.set(randomKey, newValue);
                mapOperations++;

                const canvas = document.getElementById('mapCanvas');
                const ctx = canvas.getContext('2d');
                drawMap(ctx);
                showMapMessage(`更新了 ${randomKey} 的值`, '#4CAF50');
            } else {
                showMapMessage('请先添加一些键值对！', '#666');
            }
        }

        function clearMap() {
            mapData.clear();
            mapOperations++;

            const canvas = document.getElementById('mapCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            updateMapScore();
            updateProgress(10);
        }

        function drawMap(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            // 绘制Map标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Map - 键值对映射', ctx.canvas.width/2, 30);

            // 绘制键值对
            const entries = Array.from(mapData.entries());
            const startY = 70;
            const rowHeight = 35;

            entries.forEach(([key, value], index) => {
                const y = startY + index * rowHeight;

                // 绘制Key
                ctx.fillStyle = '#667eea';
                ctx.fillRect(100, y, 120, 25);
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 2;
                ctx.strokeRect(100, y, 120, 25);

                ctx.fillStyle = '#fff';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(key, 160, y + 17);

                // 绘制箭头
                ctx.fillStyle = '#333';
                ctx.font = '20px Arial';
                ctx.fillText('→', 240, y + 17);

                // 绘制Value
                ctx.fillStyle = '#764ba2';
                ctx.fillRect(280, y, 120, 25);
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 2;
                ctx.strokeRect(280, y, 120, 25);

                ctx.fillStyle = '#fff';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(value, 340, y + 17);
            });

            // 绘制特性说明
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 键值对：Key → Value映射', 450, 80);
            ctx.fillText('✓ Key唯一：相同Key会覆盖', 450, 100);
            ctx.fillText('✓ Value可重复', 450, 120);
        }

        function showMapMessage(message, color) {
            const canvas = document.getElementById('mapCanvas');
            const ctx = canvas.getContext('2d');

            ctx.fillStyle = color;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(message, canvas.width/2, 50);

            setTimeout(() => {
                drawMap(ctx);
            }, 3000);
        }

        function updateMapScore() {
            document.getElementById('mapScore').textContent = `键值对数量: ${mapData.size}`;
        }

        // Map实现类功能
        function switchMapType(mapType) {
            currentMapType = mapType;

            // 更新按钮样式
            document.querySelectorAll('.controls button').forEach(btn => {
                btn.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
            });
            document.getElementById(mapType.toLowerCase() + 'Btn').style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';

            // 更新信息显示
            const info = mapTypeDescriptions[mapType];
            document.getElementById('currentMapTitle').textContent = info.title;
            document.getElementById('currentMapDescription').innerHTML = `
                <p style="margin: 15px 0;">${info.description}</p>
                <div style="display: flex; gap: 10px; flex-wrap: wrap; margin: 15px 0;">
                    ${info.features.map(feature =>
                        `<span style="background: #e3f2fd; color: #1976d2; padding: 5px 10px; border-radius: 15px; font-size: 0.9rem;">${feature}</span>`
                    ).join('')}
                </div>
            `;

            // 绘制当前Map类型
            const canvas = document.getElementById('mapTypesCanvas');
            const ctx = canvas.getContext('2d');
            drawMapType(ctx);
            updateProgress(5);
        }

        function addToCurrentMap() {
            const currentMap = mapTypeData[currentMapType];
            const keys = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'];
            const values = [10, 20, 30, 40, 50, 60, 70, 80];

            let key, value;

            // TreeMap特殊处理 - 不允许null键
            if (currentMapType === 'TreeMap') {
                key = keys[Math.floor(Math.random() * keys.length)];
                value = values[Math.floor(Math.random() * values.length)];
            } else if (currentMapType === 'HashTable' || currentMapType === 'ConcurrentHashMap') {
                // HashTable和ConcurrentHashMap不允许null
                key = keys[Math.floor(Math.random() * keys.length)];
                value = values[Math.floor(Math.random() * values.length)];
            } else {
                // HashMap和LinkedHashMap允许null
                const allKeys = [...keys, null];
                const allValues = [...values, null];
                key = allKeys[Math.floor(Math.random() * allKeys.length)];
                value = allValues[Math.floor(Math.random() * allValues.length)];
            }

            currentMap.set(key, value);

            const canvas = document.getElementById('mapTypesCanvas');
            const ctx = canvas.getContext('2d');
            drawMapType(ctx);
            updateProgress(3);
        }

        function demonstrateFeature() {
            const canvas = document.getElementById('mapTypesCanvas');
            const ctx = canvas.getContext('2d');

            switch(currentMapType) {
                case 'HashMap':
                    demonstrateHashMapFeature(ctx);
                    break;
                case 'TreeMap':
                    demonstrateTreeMapFeature(ctx);
                    break;
                case 'HashTable':
                    demonstrateHashTableFeature(ctx);
                    break;
                case 'LinkedHashMap':
                    demonstrateLinkedHashMapFeature(ctx);
                    break;
                case 'ConcurrentHashMap':
                    demonstrateConcurrentHashMapFeature(ctx);
                    break;
            }
            updateProgress(10);
        }

        function clearCurrentMap() {
            mapTypeData[currentMapType].clear();
            const canvas = document.getElementById('mapTypesCanvas');
            const ctx = canvas.getContext('2d');
            drawMapType(ctx);
        }

        function drawMapType(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            // 绘制标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(currentMapType, ctx.canvas.width/2, 30);

            const currentMap = mapTypeData[currentMapType];
            const entries = Array.from(currentMap.entries());

            if (entries.length === 0) {
                ctx.fillStyle = '#999';
                ctx.font = '18px Arial';
                ctx.fillText('点击"添加元素"开始演示', ctx.canvas.width/2, ctx.canvas.height/2);
                return;
            }

            // 根据不同类型绘制不同的布局
            switch(currentMapType) {
                case 'HashMap':
                case 'HashTable':
                case 'ConcurrentHashMap':
                    drawHashLayout(ctx, entries);
                    break;
                case 'TreeMap':
                    drawTreeLayout(ctx, entries);
                    break;
                case 'LinkedHashMap':
                    drawLinkedLayout(ctx, entries);
                    break;
            }
        }

        // 不同Map类型的绘制方法
        function drawHashLayout(ctx, entries) {
            // 哈希表布局 - 随机分布
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

            entries.forEach(([key, value], index) => {
                const angle = (index / entries.length) * 2 * Math.PI;
                const radius = 120;
                const x = ctx.canvas.width/2 + Math.cos(angle) * radius;
                const y = 150 + Math.sin(angle) * radius;

                drawKeyValuePair(ctx, x, y, key, value, colors[index % colors.length]);
            });

            // 绘制说明
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('哈希表：元素随机分布，O(1)查找', ctx.canvas.width/2, 320);
        }

        function drawTreeLayout(ctx, entries) {
            // 树形布局 - 按键排序
            const sortedEntries = entries.sort((a, b) => {
                if (a[0] === null) return 1;
                if (b[0] === null) return -1;
                return a[0] > b[0] ? 1 : -1;
            });

            const treeColor = '#8E44AD';
            const levels = Math.ceil(Math.log2(sortedEntries.length + 1));

            sortedEntries.forEach((entry, index) => {
                const level = Math.floor(Math.log2(index + 1));
                const posInLevel = index - (Math.pow(2, level) - 1);
                const totalInLevel = Math.pow(2, level);

                const x = (ctx.canvas.width / (totalInLevel + 1)) * (posInLevel + 1);
                const y = 80 + level * 60;

                drawKeyValuePair(ctx, x, y, entry[0], entry[1], treeColor);

                // 绘制连接线
                if (index > 0) {
                    const parentIndex = Math.floor((index - 1) / 2);
                    const parentLevel = Math.floor(Math.log2(parentIndex + 1));
                    const parentPosInLevel = parentIndex - (Math.pow(2, parentLevel) - 1);
                    const parentTotalInLevel = Math.pow(2, parentLevel);
                    const parentX = (ctx.canvas.width / (parentTotalInLevel + 1)) * (parentPosInLevel + 1);
                    const parentY = 80 + parentLevel * 60;

                    ctx.strokeStyle = '#BDC3C7';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(parentX, parentY + 15);
                    ctx.lineTo(x, y - 15);
                    ctx.stroke();
                }
            });

            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('红黑树：按键排序，O(log n)查找', ctx.canvas.width/2, 320);
        }

        function drawLinkedLayout(ctx, entries) {
            // 链表布局 - 保持插入顺序
            const linkedColor = '#E67E22';
            const startX = 50;
            const y = 150;
            const spacing = 100;

            entries.forEach(([key, value], index) => {
                const x = startX + index * spacing;
                drawKeyValuePair(ctx, x, y, key, value, linkedColor);

                // 绘制箭头
                if (index < entries.length - 1) {
                    ctx.strokeStyle = '#34495E';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(x + 35, y);
                    ctx.lineTo(x + spacing - 35, y);
                    ctx.stroke();

                    // 箭头头部
                    ctx.beginPath();
                    ctx.moveTo(x + spacing - 35, y);
                    ctx.lineTo(x + spacing - 45, y - 5);
                    ctx.moveTo(x + spacing - 35, y);
                    ctx.lineTo(x + spacing - 45, y + 5);
                    ctx.stroke();
                }
            });

            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('链表结构：保持插入顺序', ctx.canvas.width/2, 220);
        }

        function drawKeyValuePair(ctx, x, y, key, value, color) {
            // 绘制键值对
            ctx.fillStyle = color;
            ctx.fillRect(x - 30, y - 15, 60, 30);
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 30, y - 15, 60, 30);

            ctx.fillStyle = '#fff';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            const displayKey = key === null ? 'null' : key;
            const displayValue = value === null ? 'null' : value;
            ctx.fillText(`${displayKey}:${displayValue}`, x, y + 4);
        }

        // 特性演示功能
        function demonstrateHashMapFeature(ctx) {
            showMapMessage(ctx, 'HashMap: 允许null键值，无序存储，O(1)性能', '#4CAF50');
        }

        function demonstrateTreeMapFeature(ctx) {
            showMapMessage(ctx, 'TreeMap: 按键自动排序，不允许null键', '#8E44AD');
        }

        function demonstrateHashTableFeature(ctx) {
            showMapMessage(ctx, 'HashTable: 线程安全，不允许null，已过时', '#F44336');
        }

        function demonstrateLinkedHashMapFeature(ctx) {
            showMapMessage(ctx, 'LinkedHashMap: 保持插入顺序，允许null', '#E67E22');
        }

        function demonstrateConcurrentHashMapFeature(ctx) {
            showMapMessage(ctx, 'ConcurrentHashMap: 高并发安全，分段锁技术', '#2196F3');
        }

        function showMapMessage(ctx, message, color) {
            ctx.fillStyle = color;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(message, ctx.canvas.width/2, 50);

            setTimeout(() => {
                drawMapType(ctx);
            }, 3000);
        }

        // 测试游戏
        function startQuiz() {
            currentQuestionIndex = 0;
            quizScore = 0;
            showQuestion();
        }

        function showQuestion() {
            if (currentQuestionIndex < quizQuestions.length) {
                const question = quizQuestions[currentQuestionIndex];
                const questionDiv = document.getElementById('question');

                questionDiv.innerHTML = `
                    <h3>问题 ${currentQuestionIndex + 1}/${quizQuestions.length}</h3>
                    <p style="margin: 20px 0; font-size: 1.1rem;">${question.question}</p>
                    <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                        ${question.options.map((option, index) =>
                            `<button class="btn" onclick="selectAnswer(${index})" style="margin: 5px;">
                                ${String.fromCharCode(65 + index)}. ${option}
                            </button>`
                        ).join('')}
                    </div>
                `;

                document.getElementById('nextBtn').style.display = 'none';
            } else {
                showQuizResult();
            }
        }

        function selectAnswer(selectedIndex) {
            const question = quizQuestions[currentQuestionIndex];
            const resultDiv = document.getElementById('quizResult');

            if (selectedIndex === question.correct) {
                quizScore++;
                resultDiv.innerHTML = `<span style="color: #4CAF50; font-weight: bold;">✅ 正确！</span>`;
                updateProgress(15);
            } else {
                resultDiv.innerHTML = `<span style="color: #F44336; font-weight: bold;">❌ 错误！正确答案是：${question.options[question.correct]}</span>`;
            }

            currentQuestionIndex++;
            document.getElementById('nextBtn').style.display = 'inline-block';
        }

        function nextQuestion() {
            document.getElementById('quizResult').innerHTML = '';
            showQuestion();
        }

        function showQuizResult() {
            const percentage = (quizScore / quizQuestions.length) * 100;
            let message = '';
            let color = '';

            if (percentage >= 80) {
                message = '🎉 优秀！你已经很好地掌握了Java集合的核心概念！';
                color = '#4CAF50';
                updateProgress(25);
            } else if (percentage >= 60) {
                message = '👍 良好！继续加油，你对Java集合有了基本理解！';
                color = '#FF9800';
                updateProgress(15);
            } else {
                message = '💪 继续努力！建议重新学习一下集合的特性。';
                color = '#F44336';
                updateProgress(5);
            }

            document.getElementById('question').innerHTML = `
                <h3>测试完成！</h3>
                <p style="font-size: 1.2rem; margin: 20px 0;">
                    得分：${quizScore}/${quizQuestions.length} (${percentage.toFixed(0)}%)
                </p>
                <p style="color: ${color}; font-weight: bold;">${message}</p>
            `;

            document.getElementById('nextBtn').style.display = 'none';
        }

        // 初始化
        window.onload = function() {
            // 自动播放总览动画
            setTimeout(animateOverview, 500);

            // 添加一些示例数据
            setTimeout(() => {
                // List示例
                listData = [10, 20, 10, 30];
                const listCanvas = document.getElementById('listCanvas');
                drawList(listCanvas.getContext('2d'));

                // Set示例
                setData.add(15);
                setData.add(25);
                setData.add(35);
                const setCanvas = document.getElementById('setCanvas');
                drawSet(setCanvas.getContext('2d'));

                // Map示例
                mapData.set('姓名', '李四');
                mapData.set('年龄', '28');
                const mapCanvas = document.getElementById('mapCanvas');
                drawMap(mapCanvas.getContext('2d'));

                // Map实现类示例数据
                mapTypeData.HashMap.set('A', 10);
                mapTypeData.HashMap.set('B', 20);
                mapTypeData.HashMap.set(null, 30);

                mapTypeData.TreeMap.set('C', 30);
                mapTypeData.TreeMap.set('A', 10);
                mapTypeData.TreeMap.set('B', 20);

                mapTypeData.HashTable.set('X', 100);
                mapTypeData.HashTable.set('Y', 200);

                mapTypeData.LinkedHashMap.set('First', 1);
                mapTypeData.LinkedHashMap.set('Second', 2);
                mapTypeData.LinkedHashMap.set('Third', 3);

                mapTypeData.ConcurrentHashMap.set('Thread1', 'Data1');
                mapTypeData.ConcurrentHashMap.set('Thread2', 'Data2');

                // 默认选择HashMap
                switchMapType('HashMap');

                updateListScore();
                updateSetScore();
                updateMapScore();
            }, 2000);
        };
    </script>
</body>
</html>
