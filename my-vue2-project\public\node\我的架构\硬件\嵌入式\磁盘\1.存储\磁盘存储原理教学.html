<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>磁盘存储原理 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .disk-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            position: relative;
        }

        .disk {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle, #2c3e50 20%, #34495e 40%, #2c3e50 60%);
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: spin 4s linear infinite;
        }

        .disk.fast {
            animation: spin 2s linear infinite;
        }

        .disk-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            background: #1a252f;
            border-radius: 50%;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.5);
        }

        .track {
            position: absolute;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .track1 { width: 200px; height: 200px; }
        .track2 { width: 160px; height: 160px; }
        .track3 { width: 120px; height: 120px; }
        .track4 { width: 80px; height: 80px; }

        .read-head {
            position: absolute;
            top: 50%;
            right: -20px;
            width: 40px;
            height: 8px;
            background: #e74c3c;
            border-radius: 4px;
            transform: translateY(-50%);
            box-shadow: 0 2px 8px rgba(231,76,60,0.5);
            animation: headMove 3s ease-in-out infinite alternate;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-title {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 10px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .metric-change {
            font-size: 0.9rem;
            padding: 5px 10px;
            border-radius: 20px;
            display: inline-block;
        }

        .decrease {
            background: #d4edda;
            color: #155724;
        }

        .increase {
            background: #f8d7da;
            color: #721c24;
        }

        .no-change {
            background: #e2e3e5;
            color: #383d41;
        }

        .explanation {
            background: linear-gradient(135deg, #667eea20, #764ba220);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .quiz-section {
            background: linear-gradient(135deg, #f093fb20, #f5576c20);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #333;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            padding: 15px 20px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            border-color: #667eea;
            background: #667eea10;
        }

        .quiz-option.correct {
            border-color: #28a745;
            background: #d4edda;
        }

        .quiz-option.wrong {
            border-color: #dc3545;
            background: #f8d7da;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes headMove {
            0% { right: -20px; }
            100% { right: 120px; }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">💾 磁盘存储原理</h1>
            <p class="subtitle">通过动画和交互学习磁盘转速对性能的影响</p>
        </div>

        <div class="section">
            <h2 class="section-title">🔄 磁盘工作原理演示</h2>
            <div class="disk-container">
                <div class="disk" id="disk">
                    <div class="disk-center"></div>
                    <div class="track track1"></div>
                    <div class="track track2"></div>
                    <div class="track track3"></div>
                    <div class="track track4"></div>
                    <div class="read-head"></div>
                </div>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="setSpeed('normal')">正常转速</button>
                <button class="btn btn-secondary" onclick="setSpeed('fast')">转速提高一倍</button>
            </div>

            <div class="metrics" id="metrics">
                <div class="metric-card">
                    <div class="metric-title">平均寻道时间</div>
                    <div class="metric-value" id="seekTime">10ms</div>
                    <div class="metric-change no-change" id="seekChange">不变</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">旋转等待时间</div>
                    <div class="metric-value" id="rotationTime">8.33ms</div>
                    <div class="metric-change" id="rotationChange">基准值</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">数据传输速率</div>
                    <div class="metric-value" id="transferRate">100MB/s</div>
                    <div class="metric-change" id="transferChange">基准值</div>
                </div>
                <div class="metric-card">
                    <div class="metric-title">平均存取时间</div>
                    <div class="metric-value" id="accessTime">18.33ms</div>
                    <div class="metric-change" id="accessChange">基准值</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📚 核心概念详解</h2>

            <div class="explanation">
                <h3>🎯 平均寻道时间 (Average Seek Time)</h3>
                <p>磁头从当前位置移动到目标磁道所需的平均时间。这个时间主要取决于：</p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><span class="highlight">机械结构</span>：磁头臂的移动速度</li>
                    <li><span class="highlight">磁道间距</span>：磁道之间的物理距离</li>
                    <li><span class="highlight">控制算法</span>：寻道优化算法</li>
                </ul>
                <p><strong>重要：</strong>寻道时间与磁盘转速<span class="highlight">无关</span>，因为它是磁头的径向移动，不是圆周运动！</p>
            </div>

            <div class="explanation">
                <h3>🔄 旋转等待时间 (Rotational Latency)</h3>
                <p>磁头到达目标磁道后，等待目标扇区旋转到磁头下方的时间。</p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><span class="highlight">平均值</span>：转一圈时间的一半</li>
                    <li><span class="highlight">计算公式</span>：30秒 ÷ 转速(RPM)</li>
                    <li><span class="highlight">转速影响</span>：转速越快，等待时间越短</li>
                </ul>
                <p><strong>例如：</strong>7200RPM硬盘的平均旋转等待时间 = 30÷7200 = 4.17ms</p>
            </div>

            <div class="explanation">
                <h3>📊 数据传输速率 (Data Transfer Rate)</h3>
                <p>磁头读取或写入数据的速度，通常以MB/s为单位。</p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><span class="highlight">影响因素</span>：磁盘转速、数据密度、接口带宽</li>
                    <li><span class="highlight">转速关系</span>：转速提高，传输速率通常也会提高</li>
                    <li><span class="highlight">实际应用</span>：影响大文件的读写性能</li>
                </ul>
            </div>

            <div class="explanation">
                <h3>⏱️ 平均存取时间 (Average Access Time)</h3>
                <p>完成一次数据访问所需的总时间，包括：</p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><span class="highlight">寻道时间</span>：磁头移动到目标磁道</li>
                    <li><span class="highlight">旋转等待时间</span>：等待目标扇区</li>
                    <li><span class="highlight">数据传输时间</span>：实际读写数据</li>
                </ul>
                <p><strong>公式：</strong>平均存取时间 = 寻道时间 + 旋转等待时间 + 传输时间</p>
            </div>
        </div>

        <div class="section quiz-section">
            <h2 class="section-title">🎮 互动测验</h2>
            <div class="quiz-question">
                若磁盘的转速提高一倍，则（）
            </div>
            <div class="quiz-options" id="quizOptions">
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    A. 平均存取时间减半
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    B. 平均寻道时间加倍
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">
                    C. 旋转等待时间减半
                </div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">
                    D. 数据传输速率加倍
                </div>
            </div>
            <div id="quizExplanation" style="display: none; margin-top: 20px; padding: 20px; background: #d4edda; border-radius: 10px;">
                <h4>✅ 正确答案解析：</h4>
                <p><strong>答案C正确！</strong></p>
                <p>当磁盘转速提高一倍时：</p>
                <ul style="margin: 10px 0; padding-left: 30px;">
                    <li>🔄 <strong>旋转等待时间减半</strong>：转速快了，等待目标扇区的时间就短了</li>
                    <li>➡️ <strong>寻道时间不变</strong>：磁头移动速度与磁盘转速无关</li>
                    <li>📊 <strong>传输速率</strong>：可能提高，但不一定加倍</li>
                    <li>⏱️ <strong>存取时间</strong>：会减少，但不会减半（因为只有旋转等待时间减半）</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 实践应用</h2>
            <div class="explanation">
                <h3>💡 为什么这个知识很重要？</h3>
                <p>理解磁盘性能参数有助于：</p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><span class="highlight">选择硬盘</span>：根据应用需求选择合适转速的硬盘</li>
                    <li><span class="highlight">性能优化</span>：了解性能瓶颈，优化数据访问模式</li>
                    <li><span class="highlight">系统设计</span>：合理配置存储系统架构</li>
                    <li><span class="highlight">故障诊断</span>：分析存储性能问题的根本原因</li>
                </ul>
            </div>

            <div class="explanation">
                <h3>🚀 现代存储技术对比</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h4>💿 机械硬盘</h4>
                        <p>5400-15000 RPM</p>
                        <p>存在寻道和旋转延迟</p>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h4>⚡ 固态硬盘</h4>
                        <p>无机械部件</p>
                        <p>无寻道和旋转延迟</p>
                    </div>
                    <div style="background: white; padding: 20px; border-radius: 10px; text-align: center;">
                        <h4>🔥 NVMe SSD</h4>
                        <p>PCIe接口</p>
                        <p>极低延迟，高带宽</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSpeed = 'normal';

        function setSpeed(speed) {
            const disk = document.getElementById('disk');
            currentSpeed = speed;

            if (speed === 'fast') {
                disk.classList.add('fast');
                updateMetrics(true);
            } else {
                disk.classList.remove('fast');
                updateMetrics(false);
            }
        }

        function updateMetrics(isFast) {
            const seekTime = document.getElementById('seekTime');
            const seekChange = document.getElementById('seekChange');
            const rotationTime = document.getElementById('rotationTime');
            const rotationChange = document.getElementById('rotationChange');
            const transferRate = document.getElementById('transferRate');
            const transferChange = document.getElementById('transferChange');
            const accessTime = document.getElementById('accessTime');
            const accessChange = document.getElementById('accessChange');

            if (isFast) {
                // 转速提高一倍的效果
                seekTime.textContent = '10ms';
                seekChange.textContent = '不变';
                seekChange.className = 'metric-change no-change';

                rotationTime.textContent = '4.17ms';
                rotationChange.textContent = '减半 ↓';
                rotationChange.className = 'metric-change decrease';

                transferRate.textContent = '150MB/s';
                transferChange.textContent = '提高 ↑';
                transferChange.className = 'metric-change increase';

                accessTime.textContent = '14.17ms';
                accessChange.textContent = '减少 ↓';
                accessChange.className = 'metric-change decrease';
            } else {
                // 正常转速
                seekTime.textContent = '10ms';
                seekChange.textContent = '不变';
                seekChange.className = 'metric-change no-change';

                rotationTime.textContent = '8.33ms';
                rotationChange.textContent = '基准值';
                rotationChange.className = 'metric-change';

                transferRate.textContent = '100MB/s';
                transferChange.textContent = '基准值';
                transferChange.className = 'metric-change';

                accessTime.textContent = '18.33ms';
                accessChange.textContent = '基准值';
                accessChange.className = 'metric-change';
            }
        }

        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const explanation = document.getElementById('quizExplanation');

            // 清除之前的选择
            options.forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 标记所有选项
            options.forEach(opt => {
                if (opt === element) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (opt.textContent.includes('C. 旋转等待时间减半')) {
                    opt.classList.add('correct');
                } else {
                    opt.classList.add('wrong');
                }
            });

            // 显示解析
            explanation.style.display = 'block';
            explanation.scrollIntoView({ behavior: 'smooth' });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些交互提示
            const disk = document.getElementById('disk');
            disk.addEventListener('click', function() {
                if (currentSpeed === 'normal') {
                    setSpeed('fast');
                } else {
                    setSpeed('normal');
                }
            });

            // 添加鼠标悬停效果
            disk.style.cursor = 'pointer';
            disk.title = '点击切换转速';
        });
    </script>
</body>
</html>
