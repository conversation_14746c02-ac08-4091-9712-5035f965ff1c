/* 全局样式 - 极简风格 + 响应式设计 */
:root {
  --primary-color: #3498db;
  --primary-color-light: #5dade2;
  --primary-color-dark: #2980b9;
  --primary-color-translucent: rgba(52, 152, 219, 0.1);
  --primary-color-light-translucent: rgba(52, 152, 219, 0.05);
  --text-color: #2c3e50;
  --text-light: #7f8c8d;
  --text-dark: #34495e;
  --bg-color: #ffffff;
  --card-bg: #ffffff;
  --card-bg-color: #ffffff;
  --border-color: #ecf0f1;
  --border-color-light: #e8ecef;
  --header-background: rgba(255, 255, 255, 0.95);
  --header-bg-color: rgba(255, 255, 255, 0.95);
  --shadow-color: rgba(0, 0, 0, 0.05);
  --transition-duration: 0.4s;
  --border-radius: 8px;
  --hover-bg-color: #f5f7fa;

  /* 已读笔记颜色 */
  --card-bg-read: #f8f9fa;
  --text-color-read: #6c757d;
  --text-light-read: #adb5bd;

  /* 响应式变量 */
  --window-width: 100vw;
  --window-height: 100vh;
  --container-padding: 30px;
  --card-columns: 3;
  --font-size-base: 16px;
  --header-height: 60px;
  --sidebar-width: 250px;
  --content-max-width: 1200px;
}

/* 监控模式样式 - 其他模块灰色处理 */
.monitoring-mode {
  --monitoring-overlay-color: rgba(128, 128, 128, 0.7);
  --monitoring-text-color: #999999;
  --monitoring-bg-color: #f5f5f5;
}

/* 监控模式下的页面禁用效果 */
.monitoring-disabled {
  position: relative;
  pointer-events: none;
  user-select: none;
}

.monitoring-disabled::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--monitoring-overlay-color);
  z-index: 999;
  backdrop-filter: blur(2px);
}

.monitoring-disabled::after {
  content: '学习监控模式已启用\A其他功能暂时禁用';
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px 30px;
  border-radius: 10px;
  font-size: 18px;
  text-align: center;
  white-space: pre-line;
  z-index: 1001;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 监控模式下的容器覆盖层 */
.monitoring-overlay {
  filter: grayscale(80%) blur(1px);
  opacity: 0.5;
  transition: all 0.3s ease;
}

/* 学习监控页面在监控模式下保持正常 */
.monitoring-mode .learning-monitoring-layout {
  position: relative;
  z-index: 1002;
  pointer-events: auto;
  user-select: auto;
  filter: none !important;
  opacity: 1 !important;
}

/* 暗黑模式样式 - 极简风格 */
.dark-mode {
  --primary-color: #3498db;
  --primary-color-light: #5dade2;
  --primary-color-dark: #2980b9;
  --primary-color-translucent: rgba(52, 152, 219, 0.15);
  --primary-color-light-translucent: rgba(52, 152, 219, 0.08);
  --text-color: #ecf0f1;
  --text-light: #bdc3c7;
  --text-dark: #95a5a6;
  --bg-color: #1a1a1a;
  --card-bg: #2c2c2c;
  --card-bg-color: #2c2c2c;
  --border-color: #34495e;
  --border-color-light: #404040;
  --header-background: rgba(26, 26, 26, 0.95);
  --header-bg-color: rgba(26, 26, 26, 0.95);
  --shadow-color: rgba(0, 0, 0, 0.2);
  --hover-bg-color: #363636;
  --dark-hover-bg-color: #363636;

  /* 暗黑模式下的已读笔记颜色 */
  --card-bg-read: #1e1e1e;
  --text-color-read: #95a5a6;
  --text-light-read: #7f8c8d;
}

/* 基础样式 - 响应式 */
* {
  box-sizing: border-box;
}

html {
  font-size: var(--font-size-base);
  overflow-x: hidden;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  color: var(--text-color);
  background-color: var(--bg-color);
  transition: background-color var(--transition-duration), color var(--transition-duration);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  min-height: 100vh;
}

/* 响应式容器样式 */
.app-container {
  min-height: 100vh;
  transition: background-color var(--transition-duration);
  width: 100%;
  overflow-x: hidden;
}

/* 主容器 */
.main-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
}

/* 内容容器 */
.content-container {
  flex: 1;
  padding: var(--container-padding);
  max-width: var(--content-max-width);
  margin: 0 auto;
  width: 100%;
}

/* 响应式网格布局 */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(var(--card-columns), 1fr);
  gap: 20px;
  width: 100%;
}

/* 响应式卡片容器 */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: var(--container-padding);
}

/* 卡片样式 - 极简风格 */
.card {
  background-color: var(--card-bg-color);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px var(--shadow-color);
  transition: all var(--transition-duration);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px var(--shadow-color);
}

/* 头部样式 - 极简风格 */
.header {
  background-color: var(--header-bg-color);
  padding: 20px 40px;
  box-shadow: 0 2px 10px var(--shadow-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all var(--transition-duration);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* 笔记项样式 - 极简风格 */
.note-item {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
  border: none;
  box-shadow: 0 1px 3px var(--shadow-color);
}

.note-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px var(--shadow-color);
}

.note-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.note-item:hover::after {
  transform: scaleX(1);
}

/* 标题样式 */
.note-title {
  margin-bottom: 15px;
  color: var(--text-color);
  transition: color var(--transition-duration);
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 内容样式 */
.note-content {
  color: var(--text-light);
  transition: color var(--transition-duration);
  font-weight: 300;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 0;
  color: var(--text-light);
  transition: color var(--transition-duration);
}

/* 按钮样式覆盖 */
.el-button {
  border-radius: var(--border-radius);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border: none;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.el-button--primary {
  background-color: var(--primary-color);
}

.el-button--primary:hover, 
.el-button--primary:focus {
  background-color: var(--primary-light);
}

/* 输入框样式覆盖 */
.el-input__inner {
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  padding: 12px 15px;
  transition: all var(--transition-duration);
}

.el-input__inner:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.el-textarea__inner {
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  padding: 12px 15px;
  transition: all var(--transition-duration);
}

.el-textarea__inner:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 表单项样式 */
.el-form-item {
  margin-bottom: 25px;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.6s ease forwards;
}

/* 页面过渡动画 */
.page-enter-active, .page-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}

.page-enter, .page-leave-to {
  opacity: 0;
  transform: translateY(30px);
}

/* 暗黑模式切换按钮样式 */
.mode-switch {
  position: relative;
  display: inline-flex;
  align-items: center;
}

/* 容器样式 */
.container {
  max-width: 1200px;
  margin: 40px auto;
  padding: 0 30px;
}

/* 笔记详情页样式 */
.note-detail-content {
  font-size: 16px;
  line-height: 1.8;
  white-space: pre-line;
}

/* 笔记元数据样式 */
.note-meta {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 30px;
  display: flex;
  align-items: center;
}

.note-meta i {
  margin-right: 6px;
}

/* 笔记操作按钮样式 */
.note-actions {
  display: flex;
  gap: 15px;
}

/* 卡片标题样式 */
.card-title {
  margin-top: 0;
  margin-bottom: 25px;
  font-weight: 500;
  position: relative;
  padding-bottom: 12px;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: var(--primary-color);
  border-radius: 3px;
}

/* ==================== 响应式断点样式 ==================== */

/* 移动设备样式 (< 768px) */
.device-mobile {
  --container-padding: 15px;
  --card-columns: 1;
  --font-size-base: 14px;
  --header-height: 50px;
  --border-radius: 6px;
}

.device-mobile .header {
  padding: 15px 20px;
  flex-direction: column;
  gap: 10px;
}

.device-mobile .card {
  padding: 20px;
  margin-bottom: 15px;
}

.device-mobile .container {
  padding: 0 15px;
  margin: 20px auto;
}

.device-mobile .responsive-grid {
  grid-template-columns: 1fr;
  gap: 15px;
}

.device-mobile .card-container {
  grid-template-columns: 1fr;
  gap: 15px;
  padding: 15px;
}

/* 平板设备样式 (768px - 1024px) */
.device-tablet {
  --container-padding: 20px;
  --card-columns: 2;
  --font-size-base: 15px;
  --header-height: 55px;
}

.device-tablet .header {
  padding: 18px 30px;
}

.device-tablet .card {
  padding: 25px;
  margin-bottom: 20px;
}

.device-tablet .container {
  padding: 0 20px;
  margin: 30px auto;
}

.device-tablet .responsive-grid {
  grid-template-columns: repeat(2, 1fr);
  gap: 18px;
}

.device-tablet .card-container {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 18px;
  padding: 20px;
}

/* 桌面设备样式 (1024px - 1440px) */
.device-desktop {
  --container-padding: 30px;
  --card-columns: 3;
  --font-size-base: 16px;
  --header-height: 60px;
}

/* 大屏桌面设备样式 (> 1440px) */
.device-large-desktop {
  --container-padding: 40px;
  --card-columns: 4;
  --font-size-base: 16px;
  --header-height: 65px;
  --content-max-width: 1600px;
}

.device-large-desktop .card-container {
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
}

/* 窗口状态样式 */
.window-maximized {
  --container-padding: 40px;
}

.window-fullscreen {
  --container-padding: 20px;
}

.window-fullscreen .header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.window-fullscreen .content-container {
  padding-top: calc(var(--header-height) + 20px);
}

/* ==================== 媒体查询补充 ==================== */

/* 超小屏幕 */
@media (max-width: 480px) {
  :root {
    --container-padding: 10px;
    --font-size-base: 13px;
    --header-height: 45px;
  }

  .header {
    padding: 10px 15px !important;
  }

  .card {
    padding: 15px !important;
    margin-bottom: 10px !important;
  }

  .card-container {
    padding: 10px !important;
    gap: 10px !important;
  }
}

/* 中等屏幕 */
@media (min-width: 768px) and (max-width: 1023px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* 大屏幕 */
@media (min-width: 1024px) and (max-width: 1439px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

/* 超大屏幕 */
@media (min-width: 1440px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

/* 高度自适应 */
@media (max-height: 600px) {
  .card {
    padding: 20px !important;
    margin-bottom: 15px !important;
  }

  .container {
    margin: 15px auto !important;
  }
}