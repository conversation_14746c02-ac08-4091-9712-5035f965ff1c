<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微内核操作系统 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 30px;
        }

        .question-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        canvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            cursor: pointer;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
            border: none;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }

        .option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            animation: shake 0.6s ease-in-out;
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.7;
            margin-bottom: 15px;
        }

        .interactive-demo {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .demo-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .demo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🖥️ 微内核操作系统</h1>
            <p class="subtitle">通过动画和交互学习操作系统结构</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>微内核的操作系统（OS）结构如下图所示，图中①和②分别工作在（请作答此空）方式下，与传统的OS结构模式相比，采用微内核的OS结构模式的优点是提高了系统的灵活性、可扩充性。
            </div>

            <div class="canvas-container">
                <canvas id="osCanvas"></canvas>
            </div>

            <div class="options">
                <button class="option" data-answer="A">A. 核心态和用户态</button>
                <button class="option" data-answer="B">B. 用户态和核心态</button>
                <button class="option" data-answer="C">C. 用户态和用户态</button>
                <button class="option" data-answer="D">D. 核心态和核心态</button>
            </div>

            <div class="explanation" id="explanation">
                <h3>🎯 正确答案：B. 用户态和核心态</h3>
                <p><strong>解析：</strong></p>
                <p>在微内核操作系统中：</p>
                <p>• <strong>①微内核</strong>：工作在<span style="color: #e74c3c;">核心态</span>，拥有最高权限，直接与硬件交互</p>
                <p>• <strong>②服务器进程</strong>：工作在<span style="color: #3498db;">用户态</span>，通过消息传递与微内核通信</p>
                <p><strong>微内核的优点：</strong></p>
                <p>✅ 提高系统灵活性和可扩充性<br>✅ 增强系统稳定性和安全性<br>✅ 便于维护和调试<br>✅ 支持分布式计算</p>
            </div>
        </div>

        <div class="interactive-demo">
            <h3>🎮 交互式演示</h3>
            <div class="demo-controls">
                <button class="demo-btn" onclick="showTraditionalOS()">传统OS结构</button>
                <button class="demo-btn" onclick="showMicrokernelOS()">微内核OS结构</button>
                <button class="demo-btn" onclick="animateMessagePassing()">消息传递演示</button>
                <button class="demo-btn" onclick="showComparison()">优势对比</button>
            </div>
            <canvas id="demoCanvas" width="800" height="300"></canvas>
        </div>
    </div>

    <script>
        // 创建浮动元素
        function createFloatingElements() {
            const container = document.querySelector('.floating-elements');
            for (let i = 0; i < 6; i++) {
                const circle = document.createElement('div');
                circle.className = 'floating-circle';
                circle.style.width = Math.random() * 100 + 50 + 'px';
                circle.style.height = circle.style.width;
                circle.style.left = Math.random() * 100 + '%';
                circle.style.top = Math.random() * 100 + '%';
                circle.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(circle);
            }
        }

        // Canvas 绘制
        const canvas = document.getElementById('osCanvas');
        const ctx = canvas.getContext('2d');
        const demoCanvas = document.getElementById('demoCanvas');
        const demoCtx = demoCanvas.getContext('2d');

        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            demoCanvas.width = demoCanvas.offsetWidth;
            demoCanvas.height = demoCanvas.offsetHeight;
            drawMicrokernelStructure();
        }

        // 绘制微内核结构
        function drawMicrokernelStructure() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 绘制微内核（核心态）
            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 60, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('微内核', centerX, centerY - 5);
            ctx.fillText('(核心态)', centerX, centerY + 15);
            ctx.fillText('①', centerX - 80, centerY);
            
            // 绘制服务器进程（用户态）
            const servers = [
                { name: '文件服务器', x: centerX - 150, y: centerY - 100 },
                { name: '网络服务器', x: centerX + 150, y: centerY - 100 },
                { name: '设备驱动', x: centerX - 150, y: centerY + 100 },
                { name: '窗口服务器', x: centerX + 150, y: centerY + 100 }
            ];
            
            servers.forEach((server, index) => {
                ctx.fillStyle = '#3498db';
                ctx.fillRect(server.x - 50, server.y - 25, 100, 50);
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(server.name, server.x, server.y - 5);
                ctx.fillText('(用户态)', server.x, server.y + 10);
                
                if (index === 0) {
                    ctx.fillStyle = '#333';
                    ctx.fillText('②', server.x - 70, server.y);
                }
                
                // 绘制连接线
                ctx.strokeStyle = '#95a5a6';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(server.x, server.y);
                ctx.stroke();
                ctx.setLineDash([]);
            });
            
            // 添加标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('微内核操作系统结构', centerX, 30);
        }

        // 选项点击处理
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const explanation = document.getElementById('explanation');
                
                // 重置所有选项
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'B') {
                    this.classList.add('correct');
                    explanation.classList.add('show');
                    
                    // 添加庆祝动画
                    setTimeout(() => {
                        createCelebration();
                    }, 300);
                } else {
                    this.classList.add('wrong');
                    
                    // 显示正确答案
                    setTimeout(() => {
                        document.querySelector('[data-answer="B"]').classList.add('correct');
                        explanation.classList.add('show');
                    }, 1000);
                }
            });
        });

        // 庆祝动画
        function createCelebration() {
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.style.position = 'fixed';
                    confetti.style.left = Math.random() * 100 + '%';
                    confetti.style.top = '-10px';
                    confetti.style.width = '10px';
                    confetti.style.height = '10px';
                    confetti.style.background = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 5)];
                    confetti.style.borderRadius = '50%';
                    confetti.style.pointerEvents = 'none';
                    confetti.style.zIndex = '1000';
                    confetti.style.animation = 'fall 3s linear forwards';
                    
                    document.body.appendChild(confetti);
                    
                    setTimeout(() => {
                        confetti.remove();
                    }, 3000);
                }, i * 100);
            }
        }

        // 演示功能
        function showTraditionalOS() {
            demoCtx.clearRect(0, 0, demoCanvas.width, demoCanvas.height);
            
            // 绘制传统OS结构
            demoCtx.fillStyle = '#e74c3c';
            demoCtx.fillRect(50, 50, demoCanvas.width - 100, 200);
            
            demoCtx.fillStyle = 'white';
            demoCtx.font = 'bold 16px Arial';
            demoCtx.textAlign = 'center';
            demoCtx.fillText('传统操作系统内核（单体内核）', demoCanvas.width / 2, 100);
            demoCtx.fillText('所有服务都在核心态运行', demoCanvas.width / 2, 130);
            demoCtx.fillText('文件系统 | 网络 | 设备驱动 | 内存管理', demoCanvas.width / 2, 160);
            demoCtx.fillText('进程调度 | 系统调用 | 中断处理', demoCanvas.width / 2, 190);
        }

        function showMicrokernelOS() {
            drawMicrokernelDemo();
        }

        function drawMicrokernelDemo() {
            demoCtx.clearRect(0, 0, demoCanvas.width, demoCanvas.height);
            
            // 微内核
            demoCtx.fillStyle = '#e74c3c';
            demoCtx.fillRect(demoCanvas.width / 2 - 60, 120, 120, 60);
            demoCtx.fillStyle = 'white';
            demoCtx.font = 'bold 14px Arial';
            demoCtx.textAlign = 'center';
            demoCtx.fillText('微内核', demoCanvas.width / 2, 145);
            demoCtx.fillText('(核心态)', demoCanvas.width / 2, 165);
            
            // 服务器进程
            const services = [
                { name: '文件服务', x: 100, y: 50 },
                { name: '网络服务', x: 300, y: 50 },
                { name: '设备驱动', x: 500, y: 50 },
                { name: '窗口服务', x: 700, y: 50 }
            ];
            
            services.forEach(service => {
                demoCtx.fillStyle = '#3498db';
                demoCtx.fillRect(service.x - 40, service.y, 80, 40);
                demoCtx.fillStyle = 'white';
                demoCtx.font = '12px Arial';
                demoCtx.fillText(service.name, service.x, service.y + 15);
                demoCtx.fillText('(用户态)', service.x, service.y + 30);
                
                // 连接线
                demoCtx.strokeStyle = '#95a5a6';
                demoCtx.lineWidth = 2;
                demoCtx.beginPath();
                demoCtx.moveTo(service.x, service.y + 40);
                demoCtx.lineTo(demoCanvas.width / 2, 120);
                demoCtx.stroke();
            });
        }

        function animateMessagePassing() {
            drawMicrokernelDemo();
            
            // 动画消息传递
            let step = 0;
            const animate = () => {
                if (step < 100) {
                    demoCtx.fillStyle = '#f39c12';
                    demoCtx.beginPath();
                    demoCtx.arc(100 + step * 3, 90 + Math.sin(step * 0.1) * 20, 5, 0, 2 * Math.PI);
                    demoCtx.fill();
                    
                    step++;
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        function showComparison() {
            demoCtx.clearRect(0, 0, demoCanvas.width, demoCanvas.height);
            
            demoCtx.fillStyle = '#2c3e50';
            demoCtx.font = 'bold 16px Arial';
            demoCtx.textAlign = 'left';
            demoCtx.fillText('微内核 vs 传统内核', 50, 30);
            
            demoCtx.font = '14px Arial';
            demoCtx.fillStyle = '#27ae60';
            demoCtx.fillText('✓ 更高的稳定性', 50, 60);
            demoCtx.fillText('✓ 更好的可扩展性', 50, 80);
            demoCtx.fillText('✓ 更容易维护', 50, 100);
            demoCtx.fillText('✓ 更强的安全性', 50, 120);
            
            demoCtx.fillStyle = '#e74c3c';
            demoCtx.fillText('✗ 性能开销较大', 400, 60);
            demoCtx.fillText('✗ 消息传递延迟', 400, 80);
            demoCtx.fillText('✗ 实现复杂度高', 400, 100);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fall {
                to {
                    transform: translateY(100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // 初始化
        window.addEventListener('load', () => {
            createFloatingElements();
            resizeCanvas();
        });

        window.addEventListener('resize', resizeCanvas);
    </script>
</body>
</html>
