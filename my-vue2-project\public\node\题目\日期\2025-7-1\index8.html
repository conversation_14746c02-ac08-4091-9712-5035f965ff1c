<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTOS 实时性指标交互学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        .container {
            width: 100%;
            max-width: 800px;
            background-color: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        h1 {
            text-align: center;
            color: #1a2a4c;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .question {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 25px;
            line-height: 1.6;
            color: #333;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 25px;
        }

        .option {
            padding: 15px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 16px;
            display: flex;
            align-items: center;
        }

        .option:hover {
            border-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        }

        .option.correct {
            background-color: #f6ffed;
            border-color: #b7eb8f;
        }

        .option.incorrect {
            background-color: #fff1f0;
            border-color: #ffa39e;
        }

        .option-key {
            font-weight: bold;
            margin-right: 12px;
            color: #1a2a4c;
        }

        .canvas-container {
            margin-bottom: 20px;
        }
        
        canvas {
            width: 100%;
            background-color: #fafafa;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
        }

        .explanation {
            min-height: 60px;
            padding: 15px;
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 8px;
            font-size: 15px;
            line-height: 1.7;
            color: #0050b3;
            transition: all 0.3s ease;
        }

        .final-answer {
            margin-top: 20px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            display: none; /* Initially hidden */
        }
        
        .final-answer h3 {
            margin-top: 0;
            color: #1a2a4c;
        }

    </style>
</head>
<body>

    <div class="container">
        <h1>嵌入式实时操作系统(RTOS)</h1>
        <div class="question">
            <strong>题目：</strong> ( ) 不是反映嵌入式实时操作系统实时性的评价指标。
        </div>

        <div class="options">
            <div class="option" data-key="A" data-correct="true">
                <span class="option-key">A.</span> 任务执行时间
            </div>
            <div class="option" data-key="B" data-correct="false">
                <span class="option-key">B.</span> 中断响应和延迟时间
            </div>
            <div class="option" data-key="C" data-correct="false">
                <span class="option-key">C.</span> 任务切换时间
            </div>
            <div class="option" data-key="D" data-correct="false">
                <span class="option-key">D.</span> 信号量混洗时间
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="rtos-canvas" width="740" height="200"></canvas>
        </div>

        <div class="explanation" id="explanation-box">
            将鼠标悬停在选项上，或点击选项来查看动画和解释。
        </div>

        <div class="final-answer" id="final-answer-box">
            <h3>正确答案解析</h3>
            <p><strong>正确答案是 A. 任务执行时间。</strong></p>
            <p><strong>为什么？</strong> 实时操作系统的"实时性"强调的是"可预测性"和"及时性"，即系统能否在严格的时间限制内完成任务，而不是任务本身执行得有多快。</p>
            <ul>
                <li><strong>中断延迟(B)、任务切换(C)、信号量通信(D)</strong> 这些都是衡量系统处理外部事件、调度任务的固定开销。这些时间越短、越稳定，系统的行为就越可预测，也就更能保证任务在截止日期前完成。</li>
                <li><strong>任务执行时间(A)</strong> 是任务自身的逻辑复杂度决定的，一个功能复杂的任务自然需要更长的执行时间。它衡量的是任务的"工作量"，而不是操作系统的"响应能力"。一个实时性好的系统，可以确保即使在执行一个长任务时，也能及时响应更高优先级的事件。</li>
            </ul>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('rtos-canvas');
        const ctx = canvas.getContext('2d');
        const explanationBox = document.getElementById('explanation-box');
        const options = document.querySelectorAll('.option');
        const finalAnswerBox = document.getElementById('final-answer-box');

        let currentAnimation = null;
        const animationSpeed = 2;

        const explanations = {
            'A': '任务执行时间：任务完成自身功能所花费的时间。这主要取决于任务的复杂度，而非操作系统本身的实时性能。',
            'B': '中断延迟：从中断信号发生，到系统开始执行中断服务程序(ISR)之间的时间。这是衡量系统响应速度的关键指标。',
            'C': '任务切换时间：系统从一个正在运行的任务，切换到另一个任务所需的时间。这个开销越小，系统效率越高。',
            'D': '信号量混洗时间：一个任务释放信号量，到另一个等待该信号量的任务被激活之间的时间。它反映了任务间同步的效率。',
            'initial': '将鼠标悬停在选项上，或点击选项来查看动画和解释。'
        };

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawTimeline() {
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(30, 180);
            ctx.lineTo(canvas.width - 30, 180);
            ctx.stroke();
            ctx.fillStyle = '#666';
            ctx.font = '12px Arial';
            ctx.fillText('时间轴', canvas.width / 2 - 20, 195);
        }

        function drawTask(x, y, width, height, color, label) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            const textWidth = ctx.measureText(label).width;
            ctx.fillText(label, x + (width - textWidth) / 2, y + height / 2 + 5);
        }

        function drawArrow(fromx, fromy, tox, toy, text) {
            const headlen = 10;
            const angle = Math.atan2(toy - fromy, tox - fromx);
            ctx.strokeStyle = '#e63946';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromx, fromy);
            ctx.lineTo(tox, toy);
            ctx.lineTo(tox - headlen * Math.cos(angle - Math.PI / 6), toy - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(tox, toy);
            ctx.lineTo(tox - headlen * Math.cos(angle + Math.PI / 6), toy - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();

            if (text) {
                ctx.fillStyle = '#e63946';
                ctx.font = 'bold 14px Arial';
                const textWidth = ctx.measureText(text).width;
                ctx.fillText(text, fromx + (tox - fromx) / 2 - textWidth / 2, fromy - 15);
            }
        }
        
        // --- Animation Logic ---

        function animate(key, progress) {
            clearCanvas();
            drawTimeline();
            ctx.globalAlpha = 1;

            switch (key) {
                case 'A': { // 任务执行时间
                    ctx.globalAlpha = Math.min(1, progress / 30);
                    drawTask(80, 120, 150, 50, '#1d3557', '任务 A');
                    drawTask(300, 120, 350, 50, '#457b9d', '任务 B (更长)');
                    if (progress > 50) {
                       ctx.fillStyle = '#333';
                       ctx.font = '14px Arial';
                       ctx.fillText('任务执行时间不同，取决于任务本身', 250, 50);
                    }
                    break;
                }
                case 'B': { // 中断延迟
                    const interruptX = 150;
                    ctx.globalAlpha = Math.min(1, progress / 30);
                    
                    // Draw Interrupt
                    ctx.fillStyle = '#e63946';
                    ctx.beginPath();
                    ctx.moveTo(interruptX, 30); ctx.lineTo(interruptX - 10, 50); ctx.lineTo(interruptX + 10, 50);
                    ctx.lineTo(interruptX, 70); ctx.closePath(); ctx.fill();
                    ctx.fillText('中断发生!', interruptX + 15, 55);

                    const startDelay = 30;
                    if(progress > startDelay) {
                        const delayProgress = Math.min(1, (progress - startDelay) / 50);
                        const delayWidth = 80 * delayProgress;
                        drawArrow(interruptX, 110, interruptX + delayWidth, 110, '中断延迟');
                        
                        if (delayProgress >= 1) {
                            drawTask(interruptX + 80, 95, 200, 50, '#a8dadc', '中断服务 (ISR)');
                        }
                    }
                    break;
                }
                case 'C': { // 任务切换
                    const task1Width = 200;
                    const switchWidth = 80;
                    ctx.globalAlpha = Math.min(1, progress / 30);
                    drawTask(80, 120, task1Width, 50, '#1d3557', '任务 1');

                    const startSwitch = 40;
                     if(progress > startSwitch) {
                        const switchProgress = Math.min(1, (progress - startSwitch) / 50);
                        const currentSwitchWidth = switchWidth * switchProgress;
                        drawArrow(80 + task1Width, 145, 80 + task1Width + currentSwitchWidth, 145, '任务切换时间');
                        
                        if(switchProgress >= 1) {
                            drawTask(80 + task1Width + switchWidth, 120, 250, 50, '#457b9d', '任务 2');
                        }
                     }
                    break;
                }
                case 'D': { // 信号量
                    ctx.globalAlpha = Math.min(1, progress / 30);
                    drawTask(80, 120, 200, 50, '#1d3557', '任务 1 (释放信号量)');
                    drawTask(420, 60, 200, 50, '#f1faee', '任务 2 (等待)');
                    ctx.strokeStyle = '#ccc';
                    ctx.strokeRect(420, 60, 200, 50);


                    const startShuffle = 40;
                    if (progress > startShuffle) {
                        const shuffleProgress = Math.min(1, (progress - startShuffle) / 50);
                        const shuffleWidth = 140 * shuffleProgress;
                        drawArrow(280, 145, 280 + shuffleWidth, 85, '信号量混洗时间');
                        
                        if(shuffleProgress >= 1) {
                           drawTask(420, 60, 200, 50, '#457b9d', '任务 2 (激活)');
                        }
                    }
                    break;
                }
            }
        }
        
        let animationFrameId;
        function runAnimation(key) {
            if (currentAnimation === key) return;
            currentAnimation = key;
            
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }

            let progress = 0;
            function loop() {
                progress += animationSpeed;
                animate(key, progress);
                if (progress < 120) { // Limit animation duration
                    animationFrameId = requestAnimationFrame(loop);
                }
            }
            loop();
        }

        function handleInteraction(optionElement) {
            const key = optionElement.dataset.key;
            
            // Show explanation and run animation
            explanationBox.textContent = explanations[key];
            runAnimation(key);

            // Handle click logic
            if (event.type === 'click') {
                 if (optionElement.dataset.correct === 'true') {
                    optionElement.classList.add('correct');
                    finalAnswerBox.style.display = 'block';
                } else {
                    optionElement.classList.add('incorrect');
                }
                // Disable further clicks
                options.forEach(opt => opt.style.pointerEvents = 'none');
            }
        }

        options.forEach(option => {
            option.addEventListener('mouseenter', () => handleInteraction(option));
            option.addEventListener('click', () => handleInteraction(option));
        });

        // Initial state
        clearCanvas();
        drawTimeline();

    });
    </script>
</body>
</html>
