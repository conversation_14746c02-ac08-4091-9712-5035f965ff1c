<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SysML vs UML 完整学习平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 4rem;
            color: white;
            margin-bottom: 10px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.4rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .nav-container {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .nav-tabs {
            display: flex;
            gap: 15px;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 50px;
            backdrop-filter: blur(20px);
        }

        .tab-btn {
            padding: 12px 25px;
            background: transparent;
            border: 2px solid transparent;
            border-radius: 30px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .tab-btn:hover, .tab-btn.active {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .content-section {
            display: none;
            animation: fadeInUp 0.8s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .main-card {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .question-highlight {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin: 30px 0;
            text-align: center;
            animation: pulse 3s infinite;
        }

        .question-text {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .answer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .answer-option {
            background: rgba(102, 126, 234, 0.1);
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .answer-option:hover {
            border-color: #667eea;
            transform: scale(1.05);
            background: rgba(102, 126, 234, 0.2);
        }

        .answer-option.correct {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.2);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            background: white;
        }

        .diagram-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .diagram-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .diagram-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .diagram-card:hover::before {
            animation: shine 0.8s ease-in-out;
        }

        .diagram-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .diagram-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 20px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .comparison-table tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .interactive-demo {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            color: white;
        }

        .demo-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .demo-btn {
            padding: 12px 25px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-weight: 500;
        }

        .demo-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes shine {
            0% { opacity: 0; transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { opacity: 1; }
            100% { opacity: 0; transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .highlight-box {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .learning-path {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .path-step {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            flex: 1;
            min-width: 200px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .path-step:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .step-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .title { font-size: 2.5rem; }
            .nav-tabs { flex-direction: column; align-items: center; }
            .answer-grid { grid-template-columns: 1fr; }
            .learning-path { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">SysML vs UML</h1>
            <p class="subtitle">🎓 零基础系统建模语言交互式学习平台</p>
        </div>

        <div class="nav-container">
            <div class="nav-tabs">
                <div class="tab-btn active" onclick="showSection('question')">📝 题目解析</div>
                <div class="tab-btn" onclick="showSection('overview')">🔍 概念对比</div>
                <div class="tab-btn" onclick="showSection('diagrams')">📊 图表详解</div>
                <div class="tab-btn" onclick="showSection('requirements')">📋 需求建模</div>
                <div class="tab-btn" onclick="showSection('interactive')">🎮 交互演示</div>
                <div class="tab-btn" onclick="showSection('summary')">📚 知识总结</div>
            </div>
        </div>

        <!-- 题目解析部分 -->
        <div id="question" class="content-section active">
            <div class="main-card">
                <div class="question-highlight">
                    <div class="question-text">
                        和 UML 相比 SysML 新增了<strong>（问题 1）</strong>，其中<strong>（问题 2）</strong>用于描绘需求。
                    </div>
                    <p>🎯 让我们通过动画和交互来理解这道题！</p>
                </div>

                <div class="canvas-container">
                    <canvas id="questionCanvas" width="900" height="400"></canvas>
                </div>

                <h3>📝 问题1：SysML相比UML新增了什么？</h3>
                <div class="answer-grid">
                    <div class="answer-option" onclick="selectAnswer(1, 'A', this)">
                        <h4>A. 需求图和参数图</h4>
                        <p>专门用于系统工程的新图表</p>
                    </div>
                    <div class="answer-option" onclick="selectAnswer(1, 'B', this)">
                        <h4>B. 用例图</h4>
                        <p>UML中已有的图表</p>
                    </div>
                    <div class="answer-option" onclick="selectAnswer(1, 'C', this)">
                        <h4>C. 活动图</h4>
                        <p>UML中已有的图表</p>
                    </div>
                    <div class="answer-option" onclick="selectAnswer(1, 'D', this)">
                        <h4>D. 时序图</h4>
                        <p>UML中已有的图表</p>
                    </div>
                </div>

                <h3>📝 问题2：哪种图表用于描绘需求？</h3>
                <div class="answer-grid">
                    <div class="answer-option" onclick="selectAnswer(2, 'A', this)">
                        <h4>A. 需求图</h4>
                        <p>专门用于需求管理</p>
                    </div>
                    <div class="answer-option" onclick="selectAnswer(2, 'B', this)">
                        <h4>B. 用例图</h4>
                        <p>描述系统功能</p>
                    </div>
                    <div class="answer-option" onclick="selectAnswer(2, 'C', this)">
                        <h4>C. 活动图</h4>
                        <p>描述业务流程</p>
                    </div>
                    <div class="answer-option" onclick="selectAnswer(2, 'D', this)">
                        <h4>D. 时序图</h4>
                        <p>描述时间序列</p>
                    </div>
                </div>

                <div id="answerFeedback" style="margin-top: 30px; display: none;"></div>
            </div>
        </div>

        <!-- 概念对比部分 -->
        <div id="overview" class="content-section">
            <div class="main-card">
                <h2>🔍 UML vs SysML 深度对比</h2>

                <div class="learning-path">
                    <div class="path-step">
                        <div class="step-number">1</div>
                        <h4>UML基础</h4>
                        <p>统一建模语言<br>软件系统建模</p>
                    </div>
                    <div class="path-step">
                        <div class="step-number">2</div>
                        <h4>SysML扩展</h4>
                        <p>系统建模语言<br>复杂系统工程</p>
                    </div>
                    <div class="path-step">
                        <div class="step-number">3</div>
                        <h4>新增图表</h4>
                        <p>需求图、参数图<br>块定义图等</p>
                    </div>
                    <div class="path-step">
                        <div class="step-number">4</div>
                        <h4>应用领域</h4>
                        <p>航空航天、汽车<br>医疗设备等</p>
                    </div>
                </div>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>对比维度</th>
                            <th>UML (统一建模语言)</th>
                            <th>SysML (系统建模语言)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>发布时间</strong></td>
                            <td>1997年</td>
                            <td>2007年</td>
                        </tr>
                        <tr>
                            <td><strong>主要用途</strong></td>
                            <td>软件系统建模和设计</td>
                            <td>复杂系统工程建模</td>
                        </tr>
                        <tr>
                            <td><strong>图表数量</strong></td>
                            <td>14种标准图表</td>
                            <td>9种主要图表</td>
                        </tr>
                        <tr>
                            <td><strong>核心特色</strong></td>
                            <td>面向对象软件设计</td>
                            <td>需求管理 + 参数约束</td>
                        </tr>
                        <tr>
                            <td><strong>应用领域</strong></td>
                            <td>Web应用、企业软件、移动应用</td>
                            <td>航空航天、汽车、国防、医疗设备</td>
                        </tr>
                        <tr>
                            <td><strong>新增图表</strong></td>
                            <td>-</td>
                            <td>需求图、参数图、块定义图、内部块图</td>
                        </tr>
                    </tbody>
                </table>

                <div class="highlight-box">
                    💡 关键理解：SysML是UML的扩展和专业化，专门针对复杂系统工程需求而设计！
                </div>
            </div>
        </div>

        <!-- 图表详解部分 -->
        <div id="diagrams" class="content-section">
            <div class="main-card">
                <h2>📊 SysML 九种图表完整解析</h2>

                <div class="canvas-container">
                    <canvas id="diagramCanvas" width="900" height="500"></canvas>
                </div>

                <div class="diagram-showcase">
                    <div class="diagram-card" onclick="animateDiagram('requirements')" style="background: linear-gradient(135deg, #ff6b6b, #ffa500);">
                        <span class="diagram-icon">📋</span>
                        <h3>需求图</h3>
                        <p>Requirements Diagram</p>
                        <small>🎯 捕获和管理系统需求</small>
                    </div>
                    <div class="diagram-card" onclick="animateDiagram('usecase')" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                        <span class="diagram-icon">👥</span>
                        <h3>用例图</h3>
                        <p>Use Case Diagram</p>
                        <small>🎭 描述系统功能和用户交互</small>
                    </div>
                    <div class="diagram-card" onclick="animateDiagram('block')" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                        <span class="diagram-icon">🧱</span>
                        <h3>块定义图</h3>
                        <p>Block Definition Diagram</p>
                        <small>🏗️ 建模系统结构和组件</small>
                    </div>
                    <div class="diagram-card" onclick="animateDiagram('internal')" style="background: linear-gradient(135deg, #fa709a, #fee140);">
                        <span class="diagram-icon">🔗</span>
                        <h3>内部块图</h3>
                        <p>Internal Block Diagram</p>
                        <small>🔌 内部结构和连接关系</small>
                    </div>
                    <div class="diagram-card" onclick="animateDiagram('parametric')" style="background: linear-gradient(135deg, #a8edea, #fed6e3);">
                        <span class="diagram-icon">📊</span>
                        <h3>参数图</h3>
                        <p>Parametric Diagram</p>
                        <small>📐 约束系统参数和公式</small>
                    </div>
                    <div class="diagram-card" onclick="animateDiagram('activity')" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <span class="diagram-icon">⚡</span>
                        <h3>活动图</h3>
                        <p>Activity Diagram</p>
                        <small>🔄 建模系统行为和流程</small>
                    </div>
                    <div class="diagram-card" onclick="animateDiagram('sequence')" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                        <span class="diagram-icon">📈</span>
                        <h3>序列图</h3>
                        <p>Sequence Diagram</p>
                        <small>⏰ 时间序列交互</small>
                    </div>
                    <div class="diagram-card" onclick="animateDiagram('state')" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                        <span class="diagram-icon">🔄</span>
                        <h3>状态机图</h3>
                        <p>State Machine Diagram</p>
                        <small>🎛️ 状态转换和生命周期</small>
                    </div>
                    <div class="diagram-card" onclick="animateDiagram('package')" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                        <span class="diagram-icon">📦</span>
                        <h3>包图</h3>
                        <p>Package Diagram</p>
                        <small>📁 组织和管理模型元素</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 需求建模部分 -->
        <div id="requirements" class="content-section">
            <div class="main-card">
                <h2>📋 需求图深度解析</h2>

                <div class="highlight-box">
                    🎯 需求图是SysML最重要的新增图表，专门用于系统工程中的需求管理！
                </div>

                <div class="interactive-demo">
                    <h3>需求图核心要素</h3>
                    <div class="demo-controls">
                        <button class="demo-btn" onclick="showRequirement('functional')">功能需求</button>
                        <button class="demo-btn" onclick="showRequirement('performance')">性能需求</button>
                        <button class="demo-btn" onclick="showRequirement('interface')">接口需求</button>
                        <button class="demo-btn" onclick="showRequirement('design')">设计约束</button>
                    </div>
                    <div id="requirementDemo" style="min-height: 200px; background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px; margin: 20px 0;">
                        <p style="text-align: center; margin-top: 80px; font-size: 1.1rem;">点击上方按钮查看不同类型的需求示例</p>
                    </div>
                </div>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>需求类型</th>
                            <th>描述</th>
                            <th>示例</th>
                            <th>验证方法</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>功能需求</strong></td>
                            <td>系统必须执行的功能</td>
                            <td>系统应能处理用户登录</td>
                            <td>功能测试</td>
                        </tr>
                        <tr>
                            <td><strong>性能需求</strong></td>
                            <td>系统性能指标</td>
                            <td>响应时间不超过2秒</td>
                            <td>性能测试</td>
                        </tr>
                        <tr>
                            <td><strong>接口需求</strong></td>
                            <td>与外部系统的接口</td>
                            <td>支持REST API接口</td>
                            <td>接口测试</td>
                        </tr>
                        <tr>
                            <td><strong>设计约束</strong></td>
                            <td>设计和实现的限制</td>
                            <td>必须使用Java语言开发</td>
                            <td>代码审查</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 交互演示部分 -->
        <div id="interactive" class="content-section">
            <div class="main-card">
                <h2>🎮 交互式学习演示</h2>

                <div class="interactive-demo">
                    <h3>SysML vs UML 动态对比演示</h3>
                    <div class="demo-controls">
                        <button class="demo-btn" onclick="startEvolutionDemo()">🚀 演化历程</button>
                        <button class="demo-btn" onclick="startComparisonDemo()">⚖️ 对比分析</button>
                        <button class="demo-btn" onclick="startApplicationDemo()">🏭 应用场景</button>
                        <button class="demo-btn" onclick="resetAllDemos()">🔄 重置演示</button>
                    </div>
                    <div id="interactiveDemo" style="min-height: 350px; background: rgba(255,255,255,0.1); border-radius: 15px; padding: 25px; margin: 20px 0; position: relative; overflow: hidden;">
                        <div id="demoContent" style="text-align: center; margin-top: 150px; font-size: 1.1rem;">
                            选择上方按钮开始交互式学习体验
                        </div>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="interactiveCanvas" width="900" height="400"></canvas>
                </div>
            </div>
        </div>

        <!-- 知识总结部分 -->
        <div id="summary" class="content-section">
            <div class="main-card">
                <h2>📚 知识点总结</h2>

                <div class="learning-path">
                    <div class="path-step" style="background: linear-gradient(135deg, #ff6b6b, #ffa500); color: white;">
                        <div class="step-number">✅</div>
                        <h4>核心答案</h4>
                        <p>问题1: 需求图和参数图<br>问题2: 需求图</p>
                    </div>
                    <div class="path-step" style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white;">
                        <div class="step-number">📊</div>
                        <h4>图表对比</h4>
                        <p>UML: 14种图表<br>SysML: 9种图表</p>
                    </div>
                    <div class="path-step" style="background: linear-gradient(135deg, #43e97b, #38f9d7); color: white;">
                        <div class="step-number">🎯</div>
                        <h4>应用领域</h4>
                        <p>UML: 软件系统<br>SysML: 复杂系统工程</p>
                    </div>
                    <div class="path-step" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                        <div class="step-number">🚀</div>
                        <h4>发展趋势</h4>
                        <p>系统工程建模<br>数字化转型</p>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3>🎓 学习成果检验</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                        <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 10px;">
                            <h4>✅ 掌握要点</h4>
                            <ul style="list-style: none; padding: 0; margin: 10px 0;">
                                <li>• SysML是UML的扩展</li>
                                <li>• 新增需求图和参数图</li>
                                <li>• 专门用于系统工程</li>
                                <li>• 需求图用于需求管理</li>
                            </ul>
                        </div>
                        <div style="background: rgba(255,255,255,0.2); padding: 20px; border-radius: 10px;">
                            <h4>🎯 应用场景</h4>
                            <ul style="list-style: none; padding: 0; margin: 10px 0;">
                                <li>• 航空航天系统</li>
                                <li>• 汽车电子系统</li>
                                <li>• 医疗设备开发</li>
                                <li>• 国防军工项目</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAnswers = {};
        let animationId;
        let currentDemo = null;

        // 答案选择功能
        function selectAnswer(questionNum, answer, element) {
            selectedAnswers[questionNum] = answer;

            // 移除同组其他选项的选中状态
            const siblings = element.parentNode.children;
            for (let sibling of siblings) {
                sibling.classList.remove('correct');
            }

            // 标记当前选项
            element.classList.add('correct');

            // 检查是否两题都已回答
            if (selectedAnswers[1] && selectedAnswers[2]) {
                setTimeout(() => showAnswerFeedback(), 500);
            }
        }

        function showAnswerFeedback() {
            const feedback = document.getElementById('answerFeedback');
            const q1Correct = selectedAnswers[1] === 'A';
            const q2Correct = selectedAnswers[2] === 'A';

            let feedbackHTML = `
                <div style="background: ${q1Correct && q2Correct ? 'rgba(40, 167, 69, 0.2)' : 'rgba(255, 193, 7, 0.2)'};
                     border-left: 4px solid ${q1Correct && q2Correct ? '#28a745' : '#ffc107'};
                     padding: 25px; border-radius: 15px; margin-bottom: 20px;">
                    <h3>${q1Correct && q2Correct ? '🎉 全部正确！恭喜您！' : '📚 让我们一起学习'}</h3>
                    <p><strong>问题1：</strong> ${q1Correct ? '✅ 正确' : '❌ 错误'} - 您选择了 ${selectedAnswers[1]}</p>
                    <p><strong>问题2：</strong> ${q2Correct ? '✅ 正确' : '❌ 错误'} - 您选择了 ${selectedAnswers[2]}</p>
                </div>

                <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 15px;">
                    <h3>📖 详细解析</h3>
                    <div style="margin: 20px 0;">
                        <h4>🎯 问题1解析：SysML新增的图表</h4>
                        <p style="margin: 10px 0;">SysML相对于UML主要新增了以下4种图表：</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>需求图 (Requirements Diagram)</strong> - 专门用于捕获和管理系统需求</li>
                            <li><strong>参数图 (Parametric Diagram)</strong> - 用于约束系统参数和数学关系</li>
                            <li><strong>块定义图 (Block Definition Diagram)</strong> - 建模系统结构</li>
                            <li><strong>内部块图 (Internal Block Diagram)</strong> - 描述内部连接</li>
                        </ul>
                    </div>
                    <div style="margin: 20px 0;">
                        <h4>📋 问题2解析：需求图的作用</h4>
                        <p><strong>需求图</strong>是SysML最重要的新增图表，专门用于：</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>📝 捕获功能需求、性能需求、接口需求、设计约束</li>
                            <li>🔗 建立需求之间的关系和依赖</li>
                            <li>📊 支持需求的层次化组织和分类</li>
                            <li>✅ 需求验证和追踪管理</li>
                        </ul>
                    </div>
                </div>
            `;

            feedback.innerHTML = feedbackHTML;
            feedback.style.display = 'block';
            feedback.style.animation = 'fadeInUp 0.8s ease-out';

            // 启动题目画布动画
            setTimeout(() => animateQuestionCanvas(), 1000);
        }

        // 页面切换功能
        function showSection(sectionId) {
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => section.classList.remove('active'));

            const tabs = document.querySelectorAll('.tab-btn');
            tabs.forEach(tab => tab.classList.remove('active'));

            document.getElementById(sectionId).classList.add('active');
            event.target.classList.add('active');

            // 清除之前的动画
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        }

        // Canvas动画功能
        function animateQuestionCanvas() {
            const canvas = document.getElementById('questionCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawUMLToSysMLEvolution(ctx, frame);
                frame++;
                animationId = requestAnimationFrame(animate);
            }

            animate();
        }

        function drawUMLToSysMLEvolution(ctx, frame) {
            // 设置字体
            ctx.textAlign = 'center';

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 28px Arial';
            ctx.fillText('UML → SysML 演化过程', 450, 40);

            // UML部分
            const umlX = 80;
            const umlY = 120;
            ctx.fillStyle = '#667eea';
            ctx.fillRect(umlX, umlY, 180, 120);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('UML', umlX + 90, umlY + 40);
            ctx.font = '16px Arial';
            ctx.fillText('统一建模语言', umlX + 90, umlY + 65);
            ctx.fillText('14种图表', umlX + 90, umlY + 85);
            ctx.fillText('软件建模', umlX + 90, umlY + 105);

            // 动态箭头
            const arrowX = 300 + Math.sin(frame * 0.1) * 15;
            ctx.fillStyle = '#ffa500';
            ctx.beginPath();
            ctx.moveTo(arrowX, 180);
            ctx.lineTo(arrowX + 40, 170);
            ctx.lineTo(arrowX + 40, 190);
            ctx.closePath();
            ctx.fill();

            // 新增图表展示
            const newDiagrams = [
                { name: '需求图', y: 100, color: '#ff6b6b' },
                { name: '参数图', y: 140, color: '#4facfe' },
                { name: '块定义图', y: 180, color: '#43e97b' },
                { name: '内部块图', y: 220, color: '#f093fb' }
            ];

            newDiagrams.forEach((diagram, index) => {
                const x = 380 + Math.sin(frame * 0.05 + index) * 8;
                const scale = 1 + Math.sin(frame * 0.08 + index) * 0.1;

                ctx.save();
                ctx.translate(x + 60, diagram.y + 15);
                ctx.scale(scale, scale);

                ctx.fillStyle = diagram.color;
                ctx.fillRect(-60, -15, 120, 30);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(diagram.name, 0, 5);

                ctx.restore();
            });

            // SysML部分
            const sysmlX = 640;
            const sysmlY = 120;
            ctx.fillStyle = '#764ba2';
            ctx.fillRect(sysmlX, sysmlY, 180, 120);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('SysML', sysmlX + 90, sysmlY + 40);
            ctx.font = '16px Arial';
            ctx.fillText('系统建模语言', sysmlX + 90, sysmlY + 65);
            ctx.fillText('9种图表', sysmlX + 90, sysmlY + 85);
            ctx.fillText('系统工程', sysmlX + 90, sysmlY + 105);

            // 连接线动画
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.setLineDash([5, 5]);
            ctx.lineDashOffset = -frame * 0.5;
            ctx.beginPath();
            ctx.moveTo(520, 140);
            ctx.lineTo(640, 140);
            ctx.moveTo(520, 180);
            ctx.lineTo(640, 180);
            ctx.moveTo(520, 220);
            ctx.lineTo(640, 220);
            ctx.stroke();
            ctx.setLineDash([]);

            // 底部说明
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.fillText('SysML在UML基础上新增了专门用于系统工程的图表', 450, 320);
            ctx.fillStyle = '#ff6b6b';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('其中需求图专门用于描绘和管理系统需求', 450, 350);
        }

        // 图表动画功能
        function animateDiagram(diagramType) {
            const canvas = document.getElementById('diagramCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            const animations = {
                requirements: () => drawRequirementsAnimation(ctx),
                usecase: () => drawUseCaseAnimation(ctx),
                block: () => drawBlockAnimation(ctx),
                internal: () => drawInternalBlockAnimation(ctx),
                parametric: () => drawParametricAnimation(ctx),
                activity: () => drawActivityAnimation(ctx),
                sequence: () => drawSequenceAnimation(ctx),
                state: () => drawStateAnimation(ctx),
                package: () => drawPackageAnimation(ctx)
            };

            if (animations[diagramType]) {
                animations[diagramType]();
            }
        }

        function drawRequirementsAnimation(ctx) {
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

                // 标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('需求图 - 系统需求管理', 450, 40);

                // 主需求
                const x1 = 150 + Math.sin(frame * 0.05) * 10;
                const y1 = 100;

                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(x1, y1, 200, 80);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('需求: 用户认证系统', x1 + 10, y1 + 25);
                ctx.fillText('ID: REQ-001', x1 + 10, y1 + 45);
                ctx.fillText('优先级: 高', x1 + 10, y1 + 65);

                // 子需求1
                const x2 = 150 + Math.cos(frame * 0.03) * 8;
                const y2 = 220;

                ctx.fillStyle = '#4facfe';
                ctx.fillRect(x2, y2, 180, 70);
                ctx.fillStyle = 'white';
                ctx.fillText('子需求: 密码验证', x2 + 10, y2 + 20);
                ctx.fillText('ID: REQ-001.1', x2 + 10, y2 + 40);
                ctx.fillText('类型: 功能需求', x2 + 10, y2 + 60);

                // 子需求2
                const x3 = 400 + Math.sin(frame * 0.04) * 8;
                const y3 = 220;

                ctx.fillStyle = '#43e97b';
                ctx.fillRect(x3, y3, 180, 70);
                ctx.fillStyle = 'white';
                ctx.fillText('子需求: 会话管理', x3 + 10, y3 + 20);
                ctx.fillText('ID: REQ-001.2', x3 + 10, y3 + 40);
                ctx.fillText('类型: 功能需求', x3 + 10, y3 + 60);

                // 性能需求
                const x4 = 650 + Math.cos(frame * 0.06) * 10;
                const y4 = 100;

                ctx.fillStyle = '#f093fb';
                ctx.fillRect(x4, y4, 180, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('性能需求', x4 + 10, y4 + 20);
                ctx.fillText('响应时间 < 2秒', x4 + 10, y4 + 40);
                ctx.fillText('ID: REQ-P-001', x4 + 10, y4 + 60);

                // 连接线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();
                // 主需求到子需求1
                ctx.moveTo(x1 + 100, y1 + 80);
                ctx.lineTo(x2 + 90, y2);
                // 主需求到子需求2
                ctx.moveTo(x1 + 100, y1 + 80);
                ctx.lineTo(x3 + 90, y3);
                ctx.stroke();

                // 箭头
                drawArrow(ctx, x1 + 100, y1 + 80, x2 + 90, y2);
                drawArrow(ctx, x1 + 100, y1 + 80, x3 + 90, y3);

                frame++;
                animationId = requestAnimationFrame(animate);
            }

            animate();
        }

        function drawUseCaseAnimation(ctx) {
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

                // 标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('用例图 - 系统功能建模', 450, 40);

                // 用例椭圆
                const centerX = 450;
                const centerY = 200;
                const radiusX = 100 + Math.sin(frame * 0.1) * 10;
                const radiusY = 50;

                ctx.fillStyle = '#4facfe';
                ctx.beginPath();
                ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('用户登录', centerX, centerY - 10);
                ctx.font = '12px Arial';
                ctx.fillText('Use Case', centerX, centerY + 10);

                // 参与者
                const actorX = 150;
                const actorY = 200;

                drawActor(ctx, actorX, actorY, frame);

                // 系统边界
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.strokeRect(300, 100, 300, 200);

                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText('登录系统', 450, 90);

                // 连接线
                ctx.strokeStyle = '#764ba2';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(actorX + 30, actorY);
                ctx.lineTo(centerX - radiusX, centerY);
                ctx.stroke();

                frame++;
                animationId = requestAnimationFrame(animate);
            }

            animate();
        }

        function drawActor(ctx, x, y, frame) {
            const bounce = Math.sin(frame * 0.1) * 3;

            ctx.strokeStyle = '#00f2fe';
            ctx.lineWidth = 3;
            ctx.fillStyle = '#00f2fe';

            // 头部
            ctx.beginPath();
            ctx.arc(x, y - 40 + bounce, 15, 0, 2 * Math.PI);
            ctx.stroke();

            // 身体
            ctx.beginPath();
            ctx.moveTo(x, y - 25 + bounce);
            ctx.lineTo(x, y + 20 + bounce);
            ctx.stroke();

            // 手臂
            ctx.beginPath();
            ctx.moveTo(x - 20, y - 10 + bounce);
            ctx.lineTo(x + 20, y - 10 + bounce);
            ctx.stroke();

            // 腿
            ctx.beginPath();
            ctx.moveTo(x, y + 20 + bounce);
            ctx.lineTo(x - 15, y + 40 + bounce);
            ctx.moveTo(x, y + 20 + bounce);
            ctx.lineTo(x + 15, y + 40 + bounce);
            ctx.stroke();

            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('用户', x, y + 60);
        }

        function drawArrow(ctx, fromX, fromY, toX, toY) {
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const arrowLength = 10;

            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle - Math.PI / 6),
                      toY - arrowLength * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - arrowLength * Math.cos(angle + Math.PI / 6),
                      toY - arrowLength * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fill();
        }

        // 其他图表动画（简化版本）
        function drawBlockAnimation(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('块定义图 - 系统结构建模', 450, 40);

            // 主块
            ctx.fillStyle = '#43e97b';
            ctx.fillRect(350, 150, 200, 120);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('系统块', 450, 180);
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('属性:', 360, 200);
            ctx.fillText('- 名称: String', 360, 215);
            ctx.fillText('- 版本: String', 360, 230);
            ctx.fillText('操作:', 360, 245);
            ctx.fillText('+ 启动()', 360, 260);
        }

        function drawInternalBlockAnimation(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('内部块图 - 内部结构连接', 450, 40);

            // 外部边界
            ctx.strokeStyle = '#fa709a';
            ctx.lineWidth = 4;
            ctx.strokeRect(200, 100, 500, 300);

            // 内部组件
            const components = [
                { x: 230, y: 130, w: 100, h: 60, name: '处理器' },
                { x: 370, y: 130, w: 100, h: 60, name: '内存' },
                { x: 510, y: 130, w: 100, h: 60, name: '存储' },
                { x: 300, y: 250, w: 100, h: 60, name: '接口' }
            ];

            components.forEach(comp => {
                ctx.fillStyle = '#fee140';
                ctx.fillRect(comp.x, comp.y, comp.w, comp.h);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(comp.name, comp.x + comp.w/2, comp.y + comp.h/2 + 5);
            });

            // 连接线
            ctx.strokeStyle = '#fa709a';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(330, 160);
            ctx.lineTo(370, 160);
            ctx.moveTo(470, 160);
            ctx.lineTo(510, 160);
            ctx.moveTo(350, 190);
            ctx.lineTo(350, 250);
            ctx.stroke();
        }

        function drawParametricAnimation(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('参数图 - 参数约束建模', 450, 40);

            // 约束块
            ctx.fillStyle = '#a8edea';
            ctx.fillRect(300, 150, 300, 120);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(300, 150, 300, 120);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('牛顿第二定律', 450, 180);
            ctx.font = '20px Arial';
            ctx.fillText('F = m × a', 450, 210);
            ctx.font = '12px Arial';
            ctx.fillText('力 = 质量 × 加速度', 450, 240);

            // 参数
            ctx.fillStyle = '#fed6e3';
            ctx.fillRect(150, 200, 80, 40);
            ctx.fillRect(670, 200, 80, 40);

            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('质量 (m)', 190, 225);
            ctx.fillText('力 (F)', 710, 225);
        }

        function drawActivityAnimation(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('活动图 - 业务流程建模', 450, 40);

            // 开始节点
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(150, 200, 20, 0, 2 * Math.PI);
            ctx.fill();

            // 活动节点
            ctx.fillStyle = '#764ba2';
            ctx.fillRect(250, 180, 150, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('验证用户身份', 325, 205);

            ctx.fillStyle = '#764ba2';
            ctx.fillRect(500, 180, 150, 40);
            ctx.fillStyle = 'white';
            ctx.fillText('授权访问', 575, 205);

            // 结束节点
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(750, 200, 20, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(750, 200, 12, 0, 2 * Math.PI);
            ctx.fill();

            // 连接线和箭头
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(170, 200);
            ctx.lineTo(250, 200);
            ctx.moveTo(400, 200);
            ctx.lineTo(500, 200);
            ctx.moveTo(650, 200);
            ctx.lineTo(730, 200);
            ctx.stroke();
        }

        function drawSequenceAnimation(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('序列图 - 时间序列交互', 450, 40);

            // 对象
            const objects = [
                { x: 200, name: '用户' },
                { x: 450, name: '系统' },
                { x: 700, name: '数据库' }
            ];

            objects.forEach(obj => {
                ctx.fillStyle = '#f093fb';
                ctx.fillRect(obj.x - 50, 80, 100, 40);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText(obj.name, obj.x, 105);

                // 生命线
                ctx.strokeStyle = '#f5576c';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(obj.x, 120);
                ctx.lineTo(obj.x, 400);
                ctx.stroke();
                ctx.setLineDash([]);
            });

            // 消息
            const messages = [
                { from: 200, to: 450, y: 180, text: '登录请求' },
                { from: 450, to: 700, y: 220, text: '验证用户' },
                { from: 700, to: 450, y: 260, text: '返回结果' },
                { from: 450, to: 200, y: 300, text: '登录成功' }
            ];

            messages.forEach(msg => {
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(msg.from, msg.y);
                ctx.lineTo(msg.to, msg.y);
                ctx.stroke();

                // 箭头
                const direction = msg.to > msg.from ? 1 : -1;
                ctx.fillStyle = '#333';
                ctx.beginPath();
                ctx.moveTo(msg.to, msg.y);
                ctx.lineTo(msg.to - 10 * direction, msg.y - 5);
                ctx.lineTo(msg.to - 10 * direction, msg.y + 5);
                ctx.closePath();
                ctx.fill();

                // 消息文本
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText(msg.text, (msg.from + msg.to) / 2, msg.y - 10);
            });
        }

        function drawStateAnimation(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('状态机图 - 状态转换建模', 450, 40);

            // 状态
            const states = [
                { x: 200, y: 200, w: 120, h: 60, name: '未登录' },
                { x: 500, y: 200, w: 120, h: 60, name: '已登录' }
            ];

            states.forEach(state => {
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(state.x, state.y, state.w, state.h);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText(state.name, state.x + state.w/2, state.y + state.h/2 + 5);
            });

            // 转换
            ctx.strokeStyle = '#00f2fe';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(320, 230);
            ctx.lineTo(500, 230);
            ctx.stroke();

            // 箭头
            ctx.fillStyle = '#00f2fe';
            ctx.beginPath();
            ctx.moveTo(500, 230);
            ctx.lineTo(485, 225);
            ctx.lineTo(485, 235);
            ctx.closePath();
            ctx.fill();

            // 转换标签
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('登录成功', 410, 220);
        }

        function drawPackageAnimation(ctx) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('包图 - 模型元素组织', 450, 40);

            // 包
            ctx.fillStyle = '#43e97b';
            ctx.fillRect(300, 150, 300, 200);

            // 包标签
            ctx.fillStyle = '#38f9d7';
            ctx.fillRect(300, 120, 80, 30);

            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('用户管理', 340, 140);

            // 包内容
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('+ User类', 320, 180);
            ctx.fillText('+ UserService类', 320, 200);
            ctx.fillText('+ UserRepository接口', 320, 220);
            ctx.fillText('+ UserController类', 320, 240);
            ctx.fillText('+ UserDTO类', 320, 260);
        }

        // 需求演示功能
        function showRequirement(type) {
            const demo = document.getElementById('requirementDemo');
            const requirements = {
                functional: {
                    title: '功能需求示例',
                    content: `
                        <div style="background: linear-gradient(135deg, #ff6b6b, #ffa500); color: white; padding: 20px; border-radius: 15px;">
                            <h4>🎯 功能需求 (Functional Requirements)</h4>
                            <div style="margin: 15px 0; background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px;">
                                <p><strong>需求ID:</strong> REQ-F-001</p>
                                <p><strong>需求名称:</strong> 用户身份验证</p>
                                <p><strong>描述:</strong> 系统应当支持用户通过用户名和密码进行登录验证</p>
                                <p><strong>优先级:</strong> 高</p>
                                <p><strong>验收标准:</strong> 用户输入正确凭据后能成功登录系统，错误凭据显示错误信息</p>
                            </div>
                        </div>
                    `
                },
                performance: {
                    title: '性能需求示例',
                    content: `
                        <div style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; padding: 20px; border-radius: 15px;">
                            <h4>⚡ 性能需求 (Performance Requirements)</h4>
                            <div style="margin: 15px 0; background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px;">
                                <p><strong>需求ID:</strong> REQ-P-001</p>
                                <p><strong>需求名称:</strong> 系统响应时间</p>
                                <p><strong>描述:</strong> 系统登录响应时间不得超过2秒</p>
                                <p><strong>测量标准:</strong> 在正常负载下95%的登录请求响应时间 < 2秒</p>
                                <p><strong>测试条件:</strong> 并发用户数不超过1000人</p>
                            </div>
                        </div>
                    `
                },
                interface: {
                    title: '接口需求示例',
                    content: `
                        <div style="background: linear-gradient(135deg, #43e97b, #38f9d7); color: white; padding: 20px; border-radius: 15px;">
                            <h4>🔌 接口需求 (Interface Requirements)</h4>
                            <div style="margin: 15px 0; background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px;">
                                <p><strong>需求ID:</strong> REQ-I-001</p>
                                <p><strong>需求名称:</strong> API接口规范</p>
                                <p><strong>描述:</strong> 系统应提供RESTful API接口供第三方系统调用</p>
                                <p><strong>协议:</strong> HTTPS</p>
                                <p><strong>数据格式:</strong> JSON</p>
                                <p><strong>认证方式:</strong> OAuth 2.0</p>
                            </div>
                        </div>
                    `
                },
                design: {
                    title: '设计约束示例',
                    content: `
                        <div style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; padding: 20px; border-radius: 15px;">
                            <h4>🏗️ 设计约束 (Design Constraints)</h4>
                            <div style="margin: 15px 0; background: rgba(255,255,255,0.2); padding: 15px; border-radius: 10px;">
                                <p><strong>需求ID:</strong> REQ-D-001</p>
                                <p><strong>需求名称:</strong> 技术栈约束</p>
                                <p><strong>描述:</strong> 系统必须使用指定的技术栈开发</p>
                                <p><strong>编程语言:</strong> Java 11或更高版本</p>
                                <p><strong>数据库:</strong> MySQL 8.0</p>
                                <p><strong>部署方式:</strong> Docker容器化部署</p>
                            </div>
                        </div>
                    `
                }
            };

            if (requirements[type]) {
                demo.innerHTML = requirements[type].content;
                demo.style.animation = 'fadeInUp 0.8s ease-out';
            }
        }

        // 交互演示功能
        function startEvolutionDemo() {
            const demo = document.getElementById('demoContent');
            demo.innerHTML = `
                <div style="text-align: center;">
                    <h3 style="margin-bottom: 30px;">🚀 SysML发展历程</h3>
                    <div style="display: flex; justify-content: space-around; margin: 30px 0; flex-wrap: wrap; gap: 20px;">
                        <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 15px; min-width: 150px; animation: fadeInUp 0.5s ease-out;">
                            <h4>1997年</h4>
                            <p>UML 1.0发布</p>
                            <small>统一建模语言诞生</small>
                        </div>
                        <div style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; padding: 20px; border-radius: 15px; min-width: 150px; animation: fadeInUp 0.5s ease-out 0.2s; animation-fill-mode: both;">
                            <h4>2003年</h4>
                            <p>SysML项目启动</p>
                            <small>系统工程需求驱动</small>
                        </div>
                        <div style="background: linear-gradient(135deg, #43e97b, #38f9d7); color: white; padding: 20px; border-radius: 15px; min-width: 150px; animation: fadeInUp 0.5s ease-out 0.4s; animation-fill-mode: both;">
                            <h4>2007年</h4>
                            <p>SysML 1.0发布</p>
                            <small>正式标准化</small>
                        </div>
                        <div style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; padding: 20px; border-radius: 15px; min-width: 150px; animation: fadeInUp 0.5s ease-out 0.6s; animation-fill-mode: both;">
                            <h4>现在</h4>
                            <p>SysML 1.6版本</p>
                            <small>持续演进中</small>
                        </div>
                    </div>
                    <div style="margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 15px;">
                        <p style="font-size: 1.1rem;">SysML是在UML基础上发展而来，专门为复杂系统工程设计的建模语言</p>
                    </div>
                </div>
            `;
            currentDemo = 'evolution';
        }

        function startComparisonDemo() {
            const demo = document.getElementById('demoContent');
            demo.innerHTML = `
                <div style="text-align: center;">
                    <h3 style="margin-bottom: 30px;">⚖️ UML vs SysML 对比分析</h3>
                    <div style="display: flex; justify-content: space-around; align-items: center; margin: 30px 0; flex-wrap: wrap; gap: 30px;">
                        <div style="text-align: center; animation: pulse 2s infinite;">
                            <div style="width: 200px; height: 150px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 20px; margin: 0 auto 20px; display: flex; flex-direction: column; align-items: center; justify-content: center; color: white; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                                <h3>UML</h3>
                                <p style="margin: 10px 0;">14种图表</p>
                                <small>软件系统建模</small>
                            </div>
                            <div style="background: rgba(102, 126, 234, 0.1); padding: 15px; border-radius: 10px;">
                                <h5>适用场景</h5>
                                <ul style="list-style: none; padding: 0; margin: 5px 0; font-size: 0.9rem;">
                                    <li>• Web应用开发</li>
                                    <li>• 移动应用开发</li>
                                    <li>• 企业软件系统</li>
                                </ul>
                            </div>
                        </div>

                        <div style="font-size: 3rem; color: #ffa500; animation: pulse 1.5s infinite;">→</div>

                        <div style="text-align: center; animation: pulse 2s infinite 0.5s;">
                            <div style="width: 200px; height: 150px; background: linear-gradient(135deg, #f093fb, #f5576c); border-radius: 20px; margin: 0 auto 20px; display: flex; flex-direction: column; align-items: center; justify-content: center; color: white; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                                <h3>SysML</h3>
                                <p style="margin: 10px 0;">9种图表</p>
                                <small>系统工程建模</small>
                            </div>
                            <div style="background: rgba(240, 147, 251, 0.1); padding: 15px; border-radius: 10px;">
                                <h5>适用场景</h5>
                                <ul style="list-style: none; padding: 0; margin: 5px 0; font-size: 0.9rem;">
                                    <li>• 航空航天系统</li>
                                    <li>• 汽车电子系统</li>
                                    <li>• 医疗设备</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 30px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 15px;">
                        <h4>🎯 核心区别</h4>
                        <p>SysML新增了<strong>需求图</strong>和<strong>参数图</strong>，专门用于复杂系统工程中的需求管理和参数约束</p>
                    </div>
                </div>
            `;
            currentDemo = 'comparison';
        }

        function startApplicationDemo() {
            const demo = document.getElementById('demoContent');
            demo.innerHTML = `
                <div>
                    <h3 style="text-align: center; margin-bottom: 30px;">🏭 实际应用场景展示</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 25px; margin: 20px 0;">
                        <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 15px; animation: fadeInUp 0.6s ease-out;">
                            <h4>🚁 航空航天</h4>
                            <ul style="list-style: none; padding: 0; margin: 15px 0;">
                                <li>✈️ 飞机控制系统</li>
                                <li>🚀 卫星通信系统</li>
                                <li>🛰️ 导航系统设计</li>
                                <li>📡 雷达系统建模</li>
                            </ul>
                            <p style="font-size: 0.9rem; opacity: 0.9;">需求复杂、安全性要求极高</p>
                        </div>
                        <div style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; padding: 25px; border-radius: 15px; animation: fadeInUp 0.6s ease-out 0.2s; animation-fill-mode: both;">
                            <h4>🚗 汽车工业</h4>
                            <ul style="list-style: none; padding: 0; margin: 15px 0;">
                                <li>🚙 自动驾驶系统</li>
                                <li>🔋 电动车控制</li>
                                <li>📱 车载娱乐系统</li>
                                <li>🛡️ 安全辅助系统</li>
                            </ul>
                            <p style="font-size: 0.9rem; opacity: 0.9;">硬件软件高度集成</p>
                        </div>
                        <div style="background: linear-gradient(135deg, #43e97b, #38f9d7); color: white; padding: 25px; border-radius: 15px; animation: fadeInUp 0.6s ease-out 0.4s; animation-fill-mode: both;">
                            <h4>🏥 医疗设备</h4>
                            <ul style="list-style: none; padding: 0; margin: 15px 0;">
                                <li>🩺 医疗监护设备</li>
                                <li>💉 药物输送系统</li>
                                <li>🔬 诊断设备</li>
                                <li>🤖 手术机器人</li>
                            </ul>
                            <p style="font-size: 0.9rem; opacity: 0.9;">生命安全相关系统</p>
                        </div>
                        <div style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; padding: 25px; border-radius: 15px; animation: fadeInUp 0.6s ease-out 0.6s; animation-fill-mode: both;">
                            <h4>🛡️ 国防军工</h4>
                            <ul style="list-style: none; padding: 0; margin: 15px 0;">
                                <li>🎯 武器控制系统</li>
                                <li>📡 通信指挥系统</li>
                                <li>🛰️ 军用卫星系统</li>
                                <li>🚢 舰船管理系统</li>
                            </ul>
                            <p style="font-size: 0.9rem; opacity: 0.9;">高可靠性、保密性要求</p>
                        </div>
                    </div>
                </div>
            `;
            currentDemo = 'application';
        }

        function resetAllDemos() {
            const demo = document.getElementById('demoContent');
            demo.innerHTML = '<div style="text-align: center; margin-top: 150px; font-size: 1.1rem;">选择上方按钮开始交互式学习体验</div>';

            const canvas = document.getElementById('interactiveCanvas');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            }

            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            currentDemo = null;
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 启动初始动画
            setTimeout(() => {
                animateQuestionCanvas();
            }, 1000);

            // 添加卡片动画
            const cards = document.querySelectorAll('.main-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.2}s`;
                card.style.animation = 'fadeInUp 0.8s ease-out forwards';
            });

            // 添加图表卡片悬停效果
            const diagramCards = document.querySelectorAll('.diagram-card');
            diagramCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.05)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
