<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式实时系统学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2em;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }
        .section:nth-child(6) { animation-delay: 1.0s; }

        .section-title {
            font-size: 1.8em;
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .question-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .question-title {
            font-size: 1.5em;
            color: #8b4513;
            margin-bottom: 15px;
            text-align: center;
        }

        .question-content {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 20px;
            line-height: 1.8;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .hardware-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .hardware-item {
            background: #f7fafc;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .hardware-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .hardware-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5em;
            animation: pulse 2s infinite;
        }

        .explanation {
            background: #edf2f7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            line-height: 1.6;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .demo-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .task-timeline {
            position: relative;
            margin: 30px 0;
        }

        .timeline-container {
            background: #f7fafc;
            border-radius: 15px;
            padding: 20px;
            overflow-x: auto;
        }

        .timeline {
            display: flex;
            align-items: center;
            min-width: 1000px;
            height: 120px;
            position: relative;
            justify-content: space-around;
        }

        .timeline-line {
            position: absolute;
            top: 50%;
            left: 5%;
            right: 5%;
            height: 3px;
            background: linear-gradient(90deg, #e2e8f0, #cbd5e0);
            border-radius: 2px;
        }

        .task-point {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 2;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .task-5ms { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
        .task-20ms { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        .task-60ms { background: linear-gradient(135deg, #45b7d1, #3498db); }
        .task-1s { background: linear-gradient(135deg, #96ceb4, #85c1a3); }

        .task-point:hover {
            transform: scale(1.15);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .task-point .task-name {
            font-size: 0.9em;
            margin-bottom: 2px;
        }

        .task-point .task-time {
            font-size: 0.7em;
            opacity: 0.9;
        }

        .vs-comparison {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            margin: 20px 0;
        }

        .vs-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .vs-divider {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .concept-card:hover {
            transform: translateX(5px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .concept-title {
            font-size: 1.2em;
            color: #4a5568;
            margin-bottom: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 嵌入式实时系统学习</h1>
            <p>从零开始，用动画理解复杂的实时控制系统</p>
        </div>

        <!-- 题目展示 -->
        <div class="question-section">
            <div class="question-title">📋 题目内容</div>
            <div class="question-content">
                <p><strong>背景：</strong>A公司承担了一项嵌入式实时控制系统的软件开发任务，需要按固定时间序列采集、处理、输出数据，实现对多个设备的综合控制。</p>
                <br>
                <p><strong>硬件配置：</strong></p>
                <ul style="margin-left: 20px;">
                    <li>处理机：PowerPC603e，主频133 MHz</li>
                    <li>双口存储器：1024 KB</li>
                    <li>4路422半双工串口接口（2路115200 Hz，2路38400 Hz）</li>
                    <li>2路A/D、D/A数模转换器</li>
                    <li>10路离散量接口</li>
                </ul>
                <br>
                <p><strong>软件需求：</strong></p>
                <ul style="margin-left: 20px;">
                    <li><span class="highlight">5ms任务</span>：从双口存储器采集1024KB数据，处理时间1ms</li>
                    <li><span class="highlight">20ms任务</span>：从422接口(115200Hz)采集64B数据，处理4ms，输出16B控制命令</li>
                    <li><span class="highlight">60ms任务</span>：从422接口(38400Hz)采集6B数据，从A/D采集28位数据，处理2ms，输出2B控制命令和28位D/A数据，输出8路离散量</li>
                    <li><span class="highlight">1s任务</span>：系统软硬件状态测试和记录，处理时间5ms</li>
                </ul>
            </div>
        </div>

        <!-- 硬件系统演示 -->
        <div class="section">
            <h2 class="section-title">🔧 硬件系统组成</h2>
            <div class="explanation">
                <p>让我们先了解这个实时系统的硬件组成。点击下面的硬件组件，看看它们的作用！</p>
            </div>
            <div class="hardware-demo">
                <div class="hardware-item" onclick="showHardwareInfo('cpu')">
                    <div class="hardware-icon">🖥️</div>
                    <h3>处理器</h3>
                    <p>PowerPC603e<br>133MHz</p>
                </div>
                <div class="hardware-item" onclick="showHardwareInfo('memory')">
                    <div class="hardware-icon">💾</div>
                    <h3>双口存储器</h3>
                    <p>1024KB<br>数据缓存</p>
                </div>
                <div class="hardware-item" onclick="showHardwareInfo('serial')">
                    <div class="hardware-icon">🔌</div>
                    <h3>串口接口</h3>
                    <p>4路422接口<br>不同频率</p>
                </div>
                <div class="hardware-item" onclick="showHardwareInfo('converter')">
                    <div class="hardware-icon">🔄</div>
                    <h3>A/D D/A转换器</h3>
                    <p>模拟数字<br>信号转换</p>
                </div>
                <div class="hardware-item" onclick="showHardwareInfo('discrete')">
                    <div class="hardware-icon">⚡</div>
                    <h3>离散量接口</h3>
                    <p>10路数字<br>控制信号</p>
                </div>
            </div>
            <div id="hardware-info" class="explanation" style="display: none;">
                <p id="hardware-description"></p>
            </div>
        </div>

        <!-- 实时系统概念解释 -->
        <div class="section">
            <h2 class="section-title">🎯 什么是实时系统？</h2>
            <div class="explanation">
                <p>实时系统不是"运行很快的系统"，而是"能在规定时间内完成任务的系统"！</p>
            </div>

            <div class="vs-comparison">
                <div class="vs-item">
                    <h3>❌ 普通系统</h3>
                    <p>只要结果正确就行<br>不关心完成时间<br>可以慢慢处理</p>
                </div>
                <div class="vs-divider">VS</div>
                <div class="vs-item">
                    <h3>✅ 实时系统</h3>
                    <p>必须在规定时间内完成<br>超时就是失败<br>时间比速度更重要</p>
                </div>
            </div>

            <div class="concept-card">
                <div class="concept-title">🚨 为什么需要实时性？</div>
                <p>想象一下：飞机的飞行控制系统如果延迟1秒才响应，飞机可能已经坠毁了！汽车的刹车系统如果延迟0.1秒，可能就撞车了！这就是为什么实时系统如此重要。</p>
            </div>
        </div>

        <!-- 任务时间线演示 -->
        <div class="section">
            <h2 class="section-title">⏰ 任务时间线</h2>
            <div class="explanation">
                <p>系统有4种不同周期的任务，它们必须按时执行。点击任务圆圈查看详情！</p>
            </div>

            <div class="task-timeline">
                <div class="timeline-container">
                    <div class="timeline">
                        <div class="timeline-line"></div>
                        <div class="task-point task-5ms" onclick="showTaskInfo('5ms')">
                            <div class="task-name">5ms</div>
                            <div class="task-time">1ms处理</div>
                        </div>
                        <div class="task-point task-20ms" onclick="showTaskInfo('20ms')">
                            <div class="task-name">20ms</div>
                            <div class="task-time">4ms处理</div>
                        </div>
                        <div class="task-point task-60ms" onclick="showTaskInfo('60ms')">
                            <div class="task-name">60ms</div>
                            <div class="task-time">2ms处理</div>
                        </div>
                        <div class="task-point task-1s" onclick="showTaskInfo('1s')">
                            <div class="task-name">1s</div>
                            <div class="task-time">5ms处理</div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="task-info" class="explanation" style="display: none;">
                <p id="task-description"></p>
            </div>
        </div>

        <!-- 操作系统选择 -->
        <div class="section">
            <h2 class="section-title">🤔 裸机开发 VS 实时操作系统</h2>
            <div class="explanation">
                <p>李工和王工的争论：到底选择哪种开发方式？</p>
            </div>

            <div class="vs-comparison">
                <div class="vs-item">
                    <h3>🔧 裸机开发（李工的观点）</h3>
                    <ul style="text-align: left; margin-left: 20px;">
                        <li>直接控制硬件</li>
                        <li>响应速度快</li>
                        <li>资源占用少</li>
                        <li>但是开发复杂</li>
                    </ul>
                </div>
                <div class="vs-divider">VS</div>
                <div class="vs-item">
                    <h3>🖥️ 实时操作系统（王工的观点）</h3>
                    <ul style="text-align: left; margin-left: 20px;">
                        <li>任务调度管理</li>
                        <li>资源共享保护</li>
                        <li>开发效率高</li>
                        <li>可靠性更好</li>
                    </ul>
                </div>
            </div>

            <div class="concept-card">
                <div class="concept-title">🏆 最终选择：实时操作系统</div>
                <p>王工获胜！因为这个系统有多个不同周期的任务，需要复杂的调度管理。实时操作系统能更好地处理任务间的协调和资源共享问题。</p>
            </div>
        </div>

        <!-- 动画演示区域 -->
        <div class="section">
            <h2 class="section-title">🎬 系统工作流程动画</h2>
            <div class="explanation">
                <p>看看系统是如何工作的！点击按钮启动不同的演示。</p>
            </div>

            <div class="demo-controls">
                <button class="btn" onclick="startTaskScheduling()">🔄 任务调度演示</button>
                <button class="btn" onclick="startDataFlow()">📊 数据流演示</button>
                <button class="btn" onclick="startTimelineDemo()">⏱️ 时间线演示</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>

            <div class="canvas-container">
                <canvas id="systemCanvas" width="800" height="400"></canvas>
            </div>
        </div>
    </div>

    <script>
        // 硬件信息展示
        function showHardwareInfo(type) {
            const infoDiv = document.getElementById('hardware-info');
            const descDiv = document.getElementById('hardware-description');

            const descriptions = {
                cpu: '🖥️ <strong>处理器 (PowerPC603e 133MHz)</strong><br>这是系统的"大脑"，负责执行所有的计算任务。133MHz的频率意味着每秒可以执行1.33亿次基本操作。在实时系统中，处理器必须能够在严格的时间限制内完成所有任务。',
                memory: '💾 <strong>双口存储器 (1024KB)</strong><br>这是一种特殊的存储器，可以同时被两个不同的设备访问。在我们的系统中，它用来缓存需要处理的大量数据。1024KB虽然看起来不大，但在嵌入式系统中已经相当可观了！',
                serial: '🔌 <strong>串口接口 (4路422)</strong><br>这些接口用于与外部设备通信。422是一种工业标准的串行通信协议，具有很强的抗干扰能力。不同的频率(115200Hz和38400Hz)用于不同类型的数据传输。',
                converter: '🔄 <strong>A/D D/A转换器</strong><br>A/D转换器将模拟信号(如温度、压力)转换为数字信号供计算机处理；D/A转换器则相反，将数字控制信号转换为模拟信号来控制实际设备。',
                discrete: '⚡ <strong>离散量接口 (10路)</strong><br>用于处理开关量信号，比如设备的开/关状态、报警信号等。每一路都可以独立控制一个数字设备的状态。'
            };

            descDiv.innerHTML = descriptions[type];
            infoDiv.style.display = 'block';
            infoDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // 任务信息展示
        function showTaskInfo(taskType) {
            const infoDiv = document.getElementById('task-info');
            const descDiv = document.getElementById('task-description');

            const descriptions = {
                '5ms': '🔴 <strong>5ms任务 - 高频数据采集</strong><br>每5毫秒执行一次，从双口存储器读取1024KB数据，处理时间只需1ms。这是系统中最频繁的任务，类似于心跳，必须非常稳定。',
                '20ms': '🟢 <strong>20ms任务 - 串口通信</strong><br>每20毫秒执行一次，从高速串口(115200Hz)接收64B数据，处理4ms后输出16B控制命令。这个任务负责与重要设备的快速通信。',
                '60ms': '🔵 <strong>60ms任务 - 综合控制</strong><br>每60毫秒执行一次，从低速串口和A/D转换器采集数据，处理2ms后输出控制命令和离散量信号。这是最复杂的任务。',
                '1s': '🟡 <strong>1s任务 - 系统监控</strong><br>每1秒执行一次，进行系统健康检查和状态记录，处理时间5ms。虽然频率最低，但对系统安全至关重要。'
            };

            descDiv.innerHTML = descriptions[taskType];
            infoDiv.style.display = 'block';
            infoDiv.scrollIntoView({ behavior: 'smooth' });
        }

        // Canvas动画相关
        const canvas = document.getElementById('systemCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentDemo = null;

        // 任务调度演示
        function startTaskScheduling() {
            resetDemo();
            currentDemo = 'scheduling';
            animateTaskScheduling();
        }

        // 数据流演示
        function startDataFlow() {
            resetDemo();
            currentDemo = 'dataflow';
            animateDataFlow();
        }

        // 时间线演示
        function startTimelineDemo() {
            resetDemo();
            currentDemo = 'timeline';
            animateTimeline();
        }

        // 重置演示
        function resetDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            currentDemo = null;
        }

        // 任务调度动画
        function animateTaskScheduling() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制CPU
            ctx.fillStyle = '#667eea';
            ctx.fillRect(50, 150, 100, 100);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('CPU', 85, 205);

            // 绘制任务队列
            const tasks = ['5ms', '20ms', '60ms', '1s'];
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'];

            for (let i = 0; i < tasks.length; i++) {
                ctx.fillStyle = colors[i];
                ctx.fillRect(200 + i * 80, 180, 60, 40);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(tasks[i], 215 + i * 80, 205);
            }

            // 绘制箭头和说明
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(160, 200);
            ctx.lineTo(190, 200);
            ctx.stroke();

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('任务按优先级排队执行', 250, 150);
            ctx.fillText('5ms任务优先级最高', 250, 270);

            if (currentDemo === 'scheduling') {
                animationId = requestAnimationFrame(animateTaskScheduling);
            }
        }

        // 数据流动画
        function animateDataFlow() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制数据源
            ctx.fillStyle = '#ffd700';
            ctx.fillRect(50, 50, 80, 60);
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('双口存储器', 55, 85);

            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(50, 150, 80, 60);
            ctx.fillText('422接口', 65, 185);

            ctx.fillStyle = '#4ecdc4';
            ctx.fillRect(50, 250, 80, 60);
            ctx.fillText('A/D转换器', 55, 285);

            // 绘制处理器
            ctx.fillStyle = '#667eea';
            ctx.fillRect(300, 150, 100, 100);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('处理器', 325, 205);

            // 绘制输出
            ctx.fillStyle = '#96ceb4';
            ctx.fillRect(550, 100, 80, 60);
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('控制命令', 560, 135);

            ctx.fillStyle = '#45b7d1';
            ctx.fillRect(550, 200, 80, 60);
            ctx.fillText('D/A输出', 565, 235);

            // 绘制数据流箭头
            drawArrow(ctx, 140, 80, 290, 180, '#ffd700');
            drawArrow(ctx, 140, 180, 290, 200, '#ff6b6b');
            drawArrow(ctx, 140, 280, 290, 220, '#4ecdc4');

            drawArrow(ctx, 410, 150, 540, 130, '#96ceb4');
            drawArrow(ctx, 410, 220, 540, 230, '#45b7d1');

            if (currentDemo === 'dataflow') {
                animationId = requestAnimationFrame(animateDataFlow);
            }
        }

        // 时间线动画
        let timelineTime = 0;
        function animateTimeline() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制时间轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(50, 200);
            ctx.lineTo(750, 200);
            ctx.stroke();

            // 绘制时间刻度
            for (let i = 0; i <= 20; i++) {
                const x = 50 + i * 35;
                ctx.beginPath();
                ctx.moveTo(x, 195);
                ctx.lineTo(x, 205);
                ctx.stroke();

                if (i % 4 === 0) {
                    ctx.fillStyle = '#333';
                    ctx.font = '10px Arial';
                    ctx.fillText(i * 5 + 'ms', x - 10, 220);
                }
            }

            // 绘制任务执行
            const currentTime = timelineTime % 100;

            // 5ms任务
            if (currentTime % 5 === 0) {
                const x = 50 + (currentTime / 5) * 35;
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(x, 150, 30, 20);
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.fillText('5ms', x + 5, 165);
            }

            // 20ms任务
            if (currentTime % 20 === 0) {
                const x = 50 + (currentTime / 5) * 35;
                ctx.fillStyle = '#4ecdc4';
                ctx.fillRect(x, 120, 30, 20);
                ctx.fillStyle = 'white';
                ctx.fillText('20ms', x + 2, 135);
            }

            timelineTime += 1;

            if (currentDemo === 'timeline') {
                animationId = requestAnimationFrame(animateTimeline);
            }
        }

        // 绘制箭头的辅助函数
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI / 6), toY - 10 * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI / 6), toY - 10 * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // 页面加载动画
        window.addEventListener('load', function() {
            // 添加一些动态效果
            const hardwareItems = document.querySelectorAll('.hardware-item');
            hardwareItems.forEach((item, index) => {
                setTimeout(() => {
                    item.style.animation = 'fadeInUp 0.6s ease-out forwards';
                }, index * 200);
            });
        });
    </script>

    <!-- 问题解答部分 -->
    <div class="section">
        <h2 class="section-title">📝 问题1：为什么选择实时操作系统？</h2>
        <div class="concept-card">
            <div class="concept-title">🎯 王工的理由（采用嵌入式实时操作系统的优点）</div>
            <div style="margin: 15px 0;">
                <h4>1. 🔄 任务调度管理</h4>
                <p>操作系统提供强大的任务调度功能，可以有效管理本系统中的多周期任务（5ms、20ms、60ms、1s），确保每个任务都能按时执行。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>2. 🤝 资源共享与互斥</h4>
                <p>操作系统提供事件、信号和任务间通信机制，有效解决系统中资源共享的互斥问题，避免数据冲突。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>3. 🛡️ 提高可靠性</h4>
                <p>采用商品化的成熟软件，经过大量测试验证，可靠性远高于从零开发的裸机程序。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>4. 💰 降低开发成本</h4>
                <p>简化软件开发流程，提高开发效率，缩短项目周期，降低整体成本。</p>
            </div>
        </div>

        <div class="concept-card">
            <div class="concept-title">🔍 选择操作系统时需要重点考虑的功能与性能</div>
            <div style="margin: 15px 0;">
                <h4>1. ⚡ 实时性能指标</h4>
                <ul style="margin-left: 20px;">
                    <li><strong>任务上下文切换时间</strong>：越短越好，减少系统开销</li>
                    <li><strong>中断响应时间</strong>：越快越好，保证实时响应</li>
                    <li><strong>内核代码占用空间</strong>：越小越好，节省存储资源</li>
                </ul>
            </div>
            <div style="margin: 15px 0;">
                <h4>2. 🔧 功能丰富性</h4>
                <ul style="margin-left: 20px;">
                    <li><strong>服务接口丰富</strong>：提供完善的API，便于灵活使用</li>
                    <li><strong>接口开放性</strong>：支持标准接口，便于移植和扩展</li>
                </ul>
            </div>
            <div style="margin: 15px 0;">
                <h4>3. 🛡️ 可靠性保障</h4>
                <ul style="margin-left: 20px;">
                    <li><strong>故障检测与恢复</strong>：具备自我诊断和恢复能力</li>
                    <li><strong>内存保护</strong>：防止任务间相互干扰</li>
                </ul>
            </div>
            <div style="margin: 15px 0;">
                <h4>4. 🎛️ 易用性与可配置性</h4>
                <ul style="margin-left: 20px;">
                    <li><strong>使用简单</strong>：学习成本低，开发效率高</li>
                    <li><strong>支撑环境配套</strong>：有完善的开发工具链</li>
                    <li><strong>可配置、可剪裁</strong>：能根据需求定制功能</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 问题2 -->
    <div class="section">
        <h2 class="section-title">📊 问题2：时间性能评估</h2>
        <div class="concept-card">
            <div class="concept-title">⏱️ 时间性能评估的主要考虑因素</div>
            <div style="margin: 15px 0;">
                <h4>1. 📈 系统时间开销</h4>
                <p>操作系统本身的时间开销一般不应超过整个控制系统运行时间总开销的20%。这确保了大部分CPU时间用于实际的控制任务。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>2. ✅ 可调度性评估</h4>
                <p>保证所有任务都能在规定的时间期限内完成。需要进行数学分析，确认在最坏情况下所有任务都能按时完成。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>3. 🔄 上下文切换时间</h4>
                <p>任务切换和中断响应的时间开销。这些时间越短，系统的实时性能越好。</p>
            </div>
        </div>

        <div class="concept-card">
            <div class="concept-title">🕐 系统工作时序关系</div>
            <p><strong>系统设计原则：</strong></p>
            <ul style="margin-left: 20px; margin-top: 10px;">
                <li><strong>最小时间节拍：</strong>5ms（系统的基本时间单位）</li>
                <li><strong>最大工作周期：</strong>1s（主时间框架）</li>
                <li><strong>调度策略：</strong>在每个5ms起点，优先执行5ms任务</li>
            </ul>

            <div style="margin: 20px 0; padding: 15px; background: #f0f8ff; border-radius: 10px;">
                <h4>📋 具体工作流程：</h4>
                <ol style="margin-left: 20px;">
                    <li>每个5ms时刻，5ms任务首先运行（占用1ms）</li>
                    <li>5ms任务完成后，处理被中断的其他任务</li>
                    <li>如果当前时刻是20ms的起点，启动20ms任务</li>
                    <li>如果当前时刻是60ms的起点，启动60ms任务</li>
                    <li>如果当前时刻是1s的起点，启动1s任务</li>
                    <li>确保在1s周期内所有任务按规定时间序列执行</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- 问题3 -->
    <div class="section">
        <h2 class="section-title">🎯 问题3：系统设计方案</h2>
        <div class="concept-card">
            <div class="concept-title">🏆 优先级分配策略</div>
            <p><strong>采用小周期优先策略：</strong>任务的周期越小，优先级越高</p>
            <div style="margin: 20px 0;">
                <div style="display: flex; justify-content: space-around; align-items: center;">
                    <div style="text-align: center;">
                        <div style="background: #ff6b6b; color: white; padding: 10px; border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">1</div>
                        <strong>5ms任务</strong><br>最高优先级
                    </div>
                    <div style="font-size: 2em; color: #667eea;">→</div>
                    <div style="text-align: center;">
                        <div style="background: #4ecdc4; color: white; padding: 10px; border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">2</div>
                        <strong>20ms任务</strong><br>高优先级
                    </div>
                    <div style="font-size: 2em; color: #667eea;">→</div>
                    <div style="text-align: center;">
                        <div style="background: #45b7d1; color: white; padding: 10px; border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">3</div>
                        <strong>60ms任务</strong><br>中优先级
                    </div>
                    <div style="font-size: 2em; color: #667eea;">→</div>
                    <div style="text-align: center;">
                        <div style="background: #96ceb4; color: white; padding: 10px; border-radius: 50%; width: 60px; height: 60px; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">4</div>
                        <strong>1s任务</strong><br>最低优先级
                    </div>
                </div>
            </div>
        </div>

        <div class="concept-card">
            <div class="concept-title">🔌 数据输入输出方法选择</div>
            <div style="margin: 15px 0;">
                <h4>1. 422接口 → 中断方式 ✅</h4>
                <p><strong>理由：</strong>处理机速度（133MHz）远远快于数据传输速度，采用中断方式不会浪费CPU时间等待数据。查询方式会因为等待数据而消耗大量处理机时间。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>2. 双口存储器 → 查询方式 ✅</h4>
                <p><strong>理由：</strong>双口存储器是直接访问存储器，数据随时可用，不需要等待，采用查询方式更简单高效。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>3. 离散量接口 → 查询方式 ✅</h4>
                <p><strong>理由：</strong>离散量接口也是直接访问存储器映射的I/O，数据立即可用，查询方式最合适。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>4. A/D、D/A转换器 → 定时查询 ✅</h4>
                <p><strong>理由：</strong>A/D、D/A转换需要一定的转换时间，在等待转换完成期间应该交出处理机时间给其他任务，采用定时查询方式最合理。</p>
            </div>
        </div>
    </div>

    <!-- 学习总结 -->
    <div class="section">
        <h2 class="section-title">🎓 学习总结</h2>
        <div class="concept-card">
            <div class="concept-title">🔑 关键知识点</div>
            <div style="margin: 15px 0;">
                <h4>1. 实时系统的本质</h4>
                <p>不是"快"，而是"准时"！必须在规定时间内完成任务。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>2. 任务调度策略</h4>
                <p>小周期优先：周期越短，优先级越高，确保高频任务不被延误。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>3. I/O方式选择</h4>
                <p>根据设备特性选择：快速设备用中断，慢速设备用查询，转换设备用定时查询。</p>
            </div>
            <div style="margin: 15px 0;">
                <h4>4. 操作系统的价值</h4>
                <p>在复杂的多任务系统中，操作系统的调度管理和资源保护功能不可替代。</p>
            </div>
        </div>

        <div class="explanation">
            <p><strong>🎯 做题思路总结：</strong></p>
            <ol style="margin-left: 20px;">
                <li>先理解系统需求和硬件配置</li>
                <li>分析各任务的时间特性和优先级</li>
                <li>选择合适的开发平台（裸机vs操作系统）</li>
                <li>设计任务调度策略</li>
                <li>选择合适的I/O处理方式</li>
                <li>进行时间性能评估验证</li>
            </ol>
        </div>
    </div>
</body>
</html>
