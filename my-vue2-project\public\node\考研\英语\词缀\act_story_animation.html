<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词根“act”的故事：行动的力量</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f8f8;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            overflow: hidden; /* Prevent scroll */
            padding: 20px;
            box-sizing: border-box;
        }

        .container {
            background-color: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: column;
            width: 90%;
            max-width: 1200px;
            overflow: hidden;
            position: relative;
            min-height: 700px;
        }

        header {
            padding: 30px 40px;
            background-color: #e0f2f7;
            color: #2196f3;
            font-size: 1.8em;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid #dcdcdc;
        }

        .content {
            display: flex;
            flex-grow: 1;
            padding: 40px;
            gap: 40px;
            position: relative;
        }

        .story-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding-right: 20px;
            line-height: 1.8;
        }

        .story-title {
            font-size: 2.2em;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .story-text {
            font-size: 1.2em;
            color: #555;
            margin-bottom: 25px;
        }

        .word-highlight {
            font-weight: bold;
            color: #e91e63; /* Pink for emphasis */
        }

        .translation {
            font-size: 1em;
            color: #888;
            margin-top: 10px;
            display: block;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }

        button {
            background-color: #2196f3;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        button:hover {
            background-color: #1976d2;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
        }

        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        .canvas-section {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f8ff; /* Light blue background for canvas */
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.05);
            min-height: 400px;
            position: relative;
        }

        canvas {
            display: block;
            background-color: transparent; /* Canvas background will be controlled by drawing */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .content {
                flex-direction: column;
                padding: 20px;
                gap: 20px;
            }

            .story-section, .canvas-section {
                padding: 0;
                width: 100%;
            }

            header {
                font-size: 1.5em;
                padding: 20px 30px;
            }

            .story-title {
                font-size: 1.8em;
            }

            .story-text {
                font-size: 1em;
            }

            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            词根“act”的故事：行动的力量
        </header>
        <div class="content">
            <div class="story-section">
                <h1 class="story-title"></h1>
                <p class="story-text"></p>
                <div class="controls">
                    <button id="prevBtn" disabled>上一步</button>
                    <button id="nextBtn">下一步</button>
                </div>
            </div>
            <div class="canvas-section">
                <canvas id="storyCanvas"></canvas>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        const storyTitle = document.querySelector('.story-title');
        const storyText = document.querySelector('.story-text');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');

        let currentSceneIndex = 0;

        const scenes = [
            {
                title: "词根“act”：行动的起源",
                text: `很久很久以前，有一个古老的词根，叫做<span class="word-highlight">act</span>。它的本意是“<span class="word-highlight">to do, to drive</span>”，也就是“<span class="word-highlight">做；使</span>”。想象一下，它就像一股原始的、推动万物运转的力量。`,
                translation: `The ancient root <span class="word-highlight">act</span> means 'to do, to drive'. It's like a primal force that sets everything in motion.`,
                animate: (c, x) => {
                    // Animation for the root 'act' - a general sense of doing/driving
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;

                    // Draw a pulsating circle to represent energy/action
                    const radius = 50 + 20 * Math.sin(Date.now() * 0.005);
                    x.beginPath();
                    x.arc(centerX, centerY, radius, 0, Math.PI * 2);
                    x.fillStyle = `rgba(33, 150, 243, ${0.5 + 0.3 * Math.sin(Date.now() * 0.005)})`;
                    x.fill();
                    x.strokeStyle = '#2196f3';
                    x.lineWidth = 5;
                    x.stroke();

                    x.font = 'bold 48px Arial';
                    x.fillStyle = '#fff';
                    x.textAlign = 'center';
                    x.textBaseline = 'middle';
                    x.fillText('ACT', centerX, centerY);

                    x.font = '24px Arial';
                    x.fillStyle = '#333';
                    x.fillText('to do, to drive', centerX, centerY + 60);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            },
            {
                title: "反作用：counteract",
                text: `当“act”遇到了前缀<span class="word-highlight">counter-</span>（反对），就有了<span class="word-highlight">counteract</span>。它表示“<span class="word-highlight">抵消；对抗</span>”。就像两股力量相互作用，最终抵消。`,
                translation: `<span class="word-highlight">Counteract</span>: 'to act against, to neutralize'. Like two opposing forces cancelling each other out.`,
                animate: (c, x) => {
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;

                    const time = Date.now() * 0.003;
                    const offset = 50 * Math.sin(time);

                    // Force 1
                    x.beginPath();
                    x.arc(centerX - 80 + offset, centerY, 40, 0, Math.PI * 2);
                    x.fillStyle = '#ff5722'; // Orange
                    x.fill();
                    x.font = 'bold 24px Arial';
                    x.fillStyle = '#fff';
                    x.textAlign = 'center';
                    x.textBaseline = 'middle';
                    x.fillText('FORCE 1', centerX - 80 + offset, centerY);

                    // Force 2
                    x.beginPath();
                    x.arc(centerX + 80 - offset, centerY, 40, 0, Math.PI * 2);
                    x.fillStyle = '#4caf50'; // Green
                    x.fill();
                    x.font = 'bold 24px Arial';
                    x.fillStyle = '#fff';
                    x.textAlign = 'center';
                    x.textBaseline = 'middle';
                    x.fillText('FORCE 2', centerX + 80 - offset, centerY);

                    x.font = 'bold 36px Arial';
                    x.fillStyle = '#333';
                    x.fillText('counteract', centerX, centerY + 100);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            },
            {
                title: "过度行动：overact",
                text: `When 'act' met the prefix <span class="word-highlight">over-</span> (excessive), it became <span class="word-highlight">overact</span>, meaning 'to act or react excessively'. Imagine someone being overly dramatic on stage.`,
                translation: `Overact: 'to act or react excessively'. Imagine someone being overly dramatic on stage.`,
                animate: (c, x) => {
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;

                    const time = Date.now() * 0.003;

                    // Simple stick figure acting
                    x.strokeStyle = '#333';
                    x.lineWidth = 3;
                    x.lineCap = 'round';

                    // Body
                    x.beginPath();
                    x.moveTo(centerX, centerY + 30);
                    x.lineTo(centerX, centerY + 100);
                    x.stroke();

                    // Head
                    x.beginPath();
                    x.arc(centerX, centerY, 20, 0, Math.PI * 2);
                    x.stroke();

                    // Arms (exaggerated)
                    x.beginPath();
                    x.moveTo(centerX, centerY + 50);
                    x.lineTo(centerX - 50 + 20 * Math.sin(time * 2), centerY + 70 - 20 * Math.cos(time * 2));
                    x.moveTo(centerX, centerY + 50);
                    x.lineTo(centerX + 50 - 20 * Math.sin(time * 2), centerY + 70 + 20 * Math.cos(time * 2));
                    x.stroke();

                    // Legs (exaggerated)
                    x.beginPath();
                    x.moveTo(centerX, centerY + 100);
                    x.lineTo(centerX - 30 + 10 * Math.cos(time * 3), centerY + 150);
                    x.moveTo(centerX, centerY + 100);
                    x.lineTo(centerX + 30 - 10 * Math.cos(time * 3), centerY + 150);
                    x.stroke();

                    x.font = 'bold 36px Arial';
                    x.fillStyle = '#333';
                    x.fillText('overact', centerX, centerY + 200);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            },
            {
                title: "放射性：radioactive",
                text: `The combination of <span class="word-highlight">radio-</span> (radiation) and 'act' forms <span class="word-highlight">radioactive</span>, meaning 'emitting radiation'. This refers to a substance that actively gives off rays.`,
                translation: `This animation shows rays actively being emitted from a central core, symbolizing radioactivity.`,
                animate: (c, x) => {
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;

                    // Core
                    x.beginPath();
                    x.arc(centerX, centerY, 30, 0, Math.PI * 2);
                    x.fillStyle = '#ffeb3b'; // Yellow
                    x.fill();
                    x.strokeStyle = '#fbc02d';
                    x.lineWidth = 3;
                    x.stroke();

                    // Radiation lines
                    const numLines = 8;
                    const time = Date.now() * 0.001;
                    for (let i = 0; i < numLines; i++) {
                        const angle = (i * Math.PI * 2 / numLines) + time * 0.1;
                        const startX = centerX + 30 * Math.cos(angle);
                        const startY = centerY + 30 * Math.sin(angle);
                        const endX = centerX + (80 + 10 * Math.sin(time * 2 + i)) * Math.cos(angle);
                        const endY = centerY + (80 + 10 * Math.sin(time * 2 + i)) * Math.sin(angle);

                        x.beginPath();
                        x.moveTo(startX, startY);
                        x.lineTo(endX, endY);
                        x.strokeStyle = `rgba(255, 235, 59, ${0.8 - 0.3 * Math.sin(time * 2 + i)})`;
                        x.lineWidth = 2;
                        x.stroke();
                    }

                    x.font = 'bold 36px Arial';
                    x.fillStyle = '#333';
                    x.fillText('radioactive', centerX, centerY + 100);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            },
            {
                title: "使活跃：activate",
                text: `<span class="word-highlight">activate</span> 是“act”加上后缀 -ate（使...），意思是“<span class="word-highlight">激活；使活跃</span>”。想象一个沉睡的机器被按下了启动按钮。`,
                translation: `A button being pressed to power on a machine, symbolizing activation and making something active.`,
                animate: (c, x) => {
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;
                    const time = Date.now() * 0.003;

                    // Machine body
                    x.fillStyle = '#bdbdbd';
                    x.fillRect(centerX - 80, centerY - 50, 160, 100);
                    x.strokeStyle = '#757575';
                    x.lineWidth = 3;
                    x.strokeRect(centerX - 80, centerY - 50, 160, 100);

                    // Button
                    x.beginPath();
                    x.arc(centerX + 50, centerY - 20, 15, 0, Math.PI * 2);
                    x.fillStyle = '#f44336'; // Red for inactive
                    if (time % (Math.PI * 2) > Math.PI) { // Pulsating effect
                        x.fillStyle = '#4caf50'; // Green when active
                    }
                    x.fill();
                    x.strokeStyle = '#c62828';
                    x.stroke();

                    // Light/energy
                    if (time % (Math.PI * 2) > Math.PI) {
                        x.beginPath();
                        x.arc(centerX, centerY, 70, 0, Math.PI * 2);
                        x.fillStyle = 'rgba(76, 175, 80, 0.2)'; // Green glow
                        x.fill();
                    }

                    x.font = 'bold 36px Arial';
                    x.fillStyle = '#333';
                    x.fillText('activate', centerX, centerY + 100);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            },
            {
                title: "活跃的：active",
                text: `<span class="word-highlight">active</span> 是“act”加上形容词后缀 -ive，表示“<span class="word-highlight">活跃的；积极的</span>”。它描述的是一种持续的、充满能量的状态。`,
                translation: `A lively figure constantly in motion, representing an active and energetic state.`,
                animate: (c, x) => {
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;
                    const time = Date.now() * 0.005;

                    // Jumping person
                    const jumpHeight = 30 * Math.sin(time * 2);
                    const personY = centerY + 50 - jumpHeight;

                    x.strokeStyle = '#333';
                    x.lineWidth = 3;
                    x.lineCap = 'round';

                    // Body
                    x.beginPath();
                    x.moveTo(centerX, personY);
                    x.lineTo(centerX, personY + 70);
                    x.stroke();

                    // Head
                    x.beginPath();
                    x.arc(centerX, personY - 20, 20, 0, Math.PI * 2);
                    x.stroke();

                    // Arms
                    x.beginPath();
                    x.moveTo(centerX, personY + 20);
                    x.lineTo(centerX - 40, personY + 50);
                    x.moveTo(centerX, personY + 20);
                    x.lineTo(centerX + 40, personY + 50);
                    x.stroke();

                    // Legs
                    x.beginPath();
                    x.moveTo(centerX, personY + 70);
                    x.lineTo(centerX - 30, personY + 120);
                    x.moveTo(centerX, personY + 70);
                    x.lineTo(centerX + 30, personY + 120);
                    x.stroke();

                    x.font = 'bold 36px Arial';
                    x.fillStyle = '#333';
                    x.fillText('active', centerX, centerY + 100);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            },
            {
                title: "促使；驱动：actuate",
                text: `<span class="word-highlight">actuate</span> 同样源于“act”，表示“<span class="word-highlight">促使；驱动</span>”。它强调的是从外部施加力量，使某物行动起来。`,
                translation: `A lever mechanism being actuated to push an object, demonstrating an external force causing action.`,
                animate: (c, x) => {
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;
                    const time = Date.now() * 0.005;

                    // A mechanism being actuated
                    x.fillStyle = '#9e9e9e'; // Grey for static parts
                    x.fillRect(centerX - 100, centerY, 200, 30); // Base

                    x.strokeStyle = '#607d8b'; // Blue-grey for moving parts
                    x.lineWidth = 5;

                    // Lever
                    const leverAngle = Math.PI / 4 + Math.sin(time * 2) * 0.3;
                    const leverLength = 100;
                    const pivotX = centerX;
                    const pivotY = centerY;

                    x.beginPath();
                    x.moveTo(pivotX, pivotY);
                    x.lineTo(pivotX + leverLength * Math.cos(leverAngle), pivotY - leverLength * Math.sin(leverAngle));
                    x.stroke();

                    // Object being pushed
                    const objectX = pivotX + leverLength * Math.cos(leverAngle);
                    const objectY = pivotY - leverLength * Math.sin(leverAngle) - 20;
                    x.fillStyle = '#ff9800'; // Orange
                    x.fillRect(objectX - 20, objectY, 40, 40);
                    x.strokeStyle = '#f57c00';
                    x.lineWidth = 3;
                    x.strokeRect(objectX - 20, objectY, 40, 40);

                    x.font = 'bold 36px Arial';
                    x.fillStyle = '#333';
                    x.fillText('actuate', centerX, centerY + 100);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            },
            {
                title: "交易：transact",
                text: `The prefix <span class="word-highlight">trans-</span> (across; transfer) combines with 'act' to form <span class="word-highlight">transact</span>, meaning 'to carry out (business); to conduct'. This usually refers to actions between two parties.`,
                translation: `Two figures exchanging an item, symbolizing a transaction or an action conducted between parties.`,
                animate: (c, x) => {
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;
                    const time = Date.now() * 0.005;

                    // Person 1
                    x.beginPath();
                    x.arc(centerX - 100, centerY - 20, 20, 0, Math.PI * 2);
                    x.fillStyle = '#673ab7'; // Deep purple
                    x.fill();
                    x.font = '16px Arial';
                    x.fillStyle = '#fff';
                    x.fillText('A', centerX - 100, centerY - 20);

                    // Person 2
                    x.beginPath();
                    x.arc(centerX + 100, centerY - 20, 20, 0, Math.PI * 2);
                    x.fillStyle = '#00bcd4'; // Cyan
                    x.fill();
                    x.font = '16px Arial';
                    x.fillStyle = '#fff';
                    x.fillText('B', centerX + 100, centerY - 20);

                    // Transaction item
                    const itemX = centerX + 50 * Math.sin(time);
                    const itemY = centerY + 50 * Math.cos(time);
                    x.fillStyle = '#ffc107'; // Amber
                    x.beginPath();
                    x.rect(itemX - 10, itemY - 10, 20, 20);
                    x.fill();
                    x.strokeStyle = '#ffa000';
                    x.lineWidth = 2;
                    x.stroke();

                    // Arrows to show transaction flow
                    x.strokeStyle = '#757575';
                    x.lineWidth = 2;
                    x.lineCap = 'round';
                    x.beginPath();
                    x.moveTo(centerX - 70, centerY + 20);
                    x.lineTo(centerX + 70, centerY + 20);
                    x.moveTo(centerX + 70, centerY + 20);
                    x.lineTo(centerX + 60, centerY + 10);
                    x.moveTo(centerX + 70, centerY + 20);
                    x.lineTo(centerX + 60, centerY + 30);
                    x.stroke();

                    x.font = 'bold 36px Arial';
                    x.fillStyle = '#333';
                    x.fillText('transact', centerX, centerY + 100);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            },
            {
                title: "反动的：reactionary",
                text: `<span class="word-highlight">reactionary</span> contains <span class="word-highlight">re-</span> (back; again) and 'act', meaning 'opposing progress or reform'. It describes someone or an ideology that is against change.`,
                translation: `A figure struggling against a solid wall, representing resistance to change and a reactionary stance.`,
                animate: (c, x) => {
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;
                    const time = Date.now() * 0.003;

                    // A large block representing "status quo"
                    x.fillStyle = '#b0bec5'; // Blue-grey
                    x.fillRect(centerX - 100, centerY - 50, 200, 100);
                    x.strokeStyle = '#78909c';
                    x.lineWidth = 3;
                    x.strokeRect(centerX - 100, centerY - 50, 200, 100);

                    x.font = 'bold 30px Arial';
                    x.fillStyle = '#fff';
                    x.textAlign = 'center';
                    x.textBaseline = 'middle';
                    x.fillText('STATUS QUO', centerX, centerY);

                    // A small, struggling arrow trying to push forward
                    const arrowBaseX = centerX;
                    const arrowBaseY = centerY + 100;
                    const arrowTipX = centerX;
                    const arrowTipY = centerY + 50 + 10 * Math.sin(time * 2); // Pulsating against the block

                    x.strokeStyle = '#e53935'; // Red for resistance
                    x.lineWidth = 4;
                    x.lineCap = 'round';

                    x.beginPath();
                    x.moveTo(arrowBaseX, arrowBaseY);
                    x.lineTo(arrowTipX, arrowTipY);
                    x.stroke();

                    // Arrowhead
                    x.beginPath();
                    x.moveTo(arrowTipX, arrowTipY);
                    x.lineTo(arrowTipX - 10, arrowTipY + 15);
                    x.moveTo(arrowTipX, arrowTipY);
                    x.lineTo(arrowTipX + 10, arrowTipY + 15);
                    x.stroke();


                    x.font = 'bold 36px Arial';
                    x.fillStyle = '#333';
                    x.fillText('reactionary', centerX, centerY + 200);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            },
            {
                title: "总结：行动无处不在",
                text: `通过这些词汇，我们看到“act”作为“做”和“使”的核心，如何与不同的前缀和后缀结合，创造出丰富多样的意义。从对抗到激活，从交易到反动，行动的力量无处不在。`,
                translation: `Through these words, we see how 'act', as the core of 'to do' and 'to drive', combines with various prefixes and suffixes to create a rich diversity of meanings. From counteracting to activating, from transacting to being reactionary, the power of action is everywhere.`,
                animate: (c, x) => {
                    c.clearRect(0, 0, x.canvas.width, x.canvas.height);
                    const centerX = x.canvas.width / 2;
                    const centerY = x.canvas.height / 2;
                    const time = Date.now() * 0.001;

                    // Floating words, orbiting the center
                    const summaryWords = ['act', 'do', 'drive', 'action', 'energy', 'change'];
                    summaryWords.forEach((word, i) => {
                        const angle = (i * Math.PI * 2 / summaryWords.length) + time;
                        const radius = 80 + 30 * Math.sin(time * 0.5 + i);
                        const wordX = centerX + radius * Math.cos(angle);
                        const wordY = centerY + radius * Math.sin(angle);

                        x.font = 'bold 24px Arial';
                        x.fillStyle = '#607d8b'; // Blue-grey
                        x.textAlign = 'center';
                        x.textBaseline = 'middle';
                        x.fillText(word, wordX, wordY);
                    });

                    // Central glowing 'ACT'
                    const coreRadius = 40 + 10 * Math.sin(time * 2);
                    x.beginPath();
                    x.arc(centerX, centerY, coreRadius, 0, Math.PI * 2);
                    x.fillStyle = `rgba(255, 193, 7, ${0.7 + 0.2 * Math.sin(time * 2)})`;
                    x.fill();
                    x.font = 'bold 48px Arial';
                    x.fillStyle = '#fff';
                    x.fillText('ACT', centerX, centerY);

                    requestAnimationFrame(() => scenes[currentSceneIndex].animate(c, x));
                }
            }
        ];

        function resizeCanvas() {
            canvas.width = canvas.parentElement.clientWidth;
            canvas.height = canvas.parentElement.clientHeight;
            displayScene(); // Redraw scene on resize
        }

        function displayScene() {
            const scene = scenes[currentSceneIndex];
            storyTitle.innerHTML = scene.title;
            storyText.innerHTML = scene.text + '<span class="translation">' + scene.translation + '</span>';

            prevBtn.disabled = currentSceneIndex === 0;
            nextBtn.disabled = currentSceneIndex === scenes.length - 1;

            if (scene.animate) {
                // Clear previous animation frame requests
                cancelAnimationFrame(window.currentAnimationFrame);
                window.currentAnimationFrame = requestAnimationFrame(() => scene.animate(ctx, ctx));
            }
        }

        prevBtn.addEventListener('click', () => {
            if (currentSceneIndex > 0) {
                currentSceneIndex--;
                displayScene();
            }
        });

        nextBtn.addEventListener('click', () => {
            if (currentSceneIndex < scenes.length - 1) {
                currentSceneIndex++;
                displayScene();
            }
        });

        window.addEventListener('resize', resizeCanvas);

        // Initial setup
        resizeCanvas();
        displayScene();
    </script>
</body>
</html> 