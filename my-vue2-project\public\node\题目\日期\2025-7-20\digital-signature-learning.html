<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字签名验证学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .signature-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .signature-demo {
            text-align: center;
            margin: 30px 0;
        }

        #signatureCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .process-controls {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 25px 0;
        }

        .process-btn {
            padding: 20px 15px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .sign-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .verify-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .process-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .process-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .key-explanation {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .key-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .key-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .key-card.sign {
            border-color: #fd79a8;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .key-card.verify {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .key-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .key-card p {
            font-size: 1rem;
            line-height: 1.6;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-correct {
            color: #00a085;
            font-weight: bold;
            background: rgba(0,184,148,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-key {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            animation: floatKey 15s infinite ease-in-out;
        }

        .key1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .key2 {
            top: 70%;
            right: 15%;
            animation-delay: 5s;
        }

        .key3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatKey {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-25px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .process-controls {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-key key1"></div>
        <div class="floating-key key2"></div>
        <div class="floating-key key3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🔐 数字签名验证学习</h1>
            <p>深度理解数字签名的加密解密过程</p>
        </div>

        <div class="main-grid">
            <div class="signature-section">
                <h2 class="section-title">🔑 数字签名过程演示</h2>
                
                <div class="signature-demo">
                    <canvas id="signatureCanvas" width="700" height="350"></canvas>
                </div>

                <div class="process-controls">
                    <button class="process-btn sign-btn" onclick="demonstrateProcess('sign')">
                        数字签名过程<br><small>甲用私钥加密</small>
                    </button>
                    <button class="process-btn verify-btn" onclick="demonstrateProcess('verify')">
                        签名验证过程<br><small>乙用甲的公钥解密</small>
                    </button>
                </div>

                <div class="key-explanation">
                    <div class="key-card sign">
                        <h3>🔏 数字签名过程</h3>
                        <p><strong>甲（发送方）的操作</strong><br>
                        • 使用自己的私钥对消息进行加密<br>
                        • 生成数字签名<br>
                        • 将签名和消息一起发送给乙<br>
                        • 目的：证明消息的真实性和完整性</p>
                    </div>
                    <div class="key-card verify">
                        <h3>🔓 签名验证过程</h3>
                        <p><strong>乙（接收方）的操作</strong><br>
                        • 使用甲的公钥对签名进行解密<br>
                        • 验证签名的有效性<br>
                        • 确认消息来源和完整性<br>
                        • 目的：验证消息确实来自甲且未被篡改</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 甲向乙发送其数字签名，要验证该签名，乙可使用（　　）对该签名进行解密。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. 甲的私钥
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        B. 甲的公钥
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. 乙的私钥
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        D. 乙的公钥
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：B. 甲的公钥</strong></p>
                    <p>数字签名采用非对称加密算法，遵循以下原则：</p>
                    <ul>
                        <li><strong>签名过程</strong>：
                            <br>• 甲（发送方）使用<span class="highlight-correct">自己的私钥</span>对消息进行加密
                            <br>• 生成数字签名
                            <br>• 将签名连同消息发送给乙</li>
                        <li><strong>验证过程</strong>：
                            <br>• 乙（接收方）使用<span class="highlight-correct">甲的公钥</span>对签名进行解密
                            <br>• 验证解密结果与原消息是否一致
                            <br>• 确认消息的真实性和完整性</li>
                    </ul>
                    <p><strong>非对称加密的核心原理</strong>：</p>
                    <ul>
                        <li><strong>密钥对关系</strong>：一个用来加密，另一个用来解密</li>
                        <li><strong>数字签名</strong>：私钥加密，公钥解密</li>
                        <li><strong>数据加密</strong>：公钥加密，私钥解密</li>
                    </ul>
                    <p><strong>为什么不是其他选项</strong>：</p>
                    <ul>
                        <li><strong>A. 甲的私钥</strong>：私钥只有甲自己拥有，乙无法获得</li>
                        <li><strong>C. 乙的私钥</strong>：乙的私钥与甲的签名无关</li>
                        <li><strong>D. 乙的公钥</strong>：乙的公钥与甲的签名无关</li>
                    </ul>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了数字签名的验证原理！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('signatureCanvas');
        const ctx = canvas.getContext('2d');
        let currentProcess = 'verify';
        let animationId = null;

        // 演示不同过程
        function demonstrateProcess(processType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            currentProcess = processType;

            // 更新按钮状态
            document.querySelectorAll('.process-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${processType}-btn`).classList.add('active');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            switch(processType) {
                case 'sign':
                    drawSignProcess();
                    break;
                case 'verify':
                    drawVerifyProcess();
                    break;
            }
        }

        // 绘制数字签名过程
        function drawSignProcess() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('数字签名过程 - 甲用私钥加密', 350, 40);

            // 甲（发送方）
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(50, 80, 120, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 80, 120, 100);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('甲', 110, 110);
            ctx.font = '12px Arial';
            ctx.fillText('(发送方)', 110, 130);
            ctx.fillText('拥有密钥对', 110, 150);
            ctx.fillText('公钥 + 私钥', 110, 170);

            // 原始消息
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(50, 200, 120, 60);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 200, 120, 60);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('原始消息', 110, 225);
            ctx.font = '12px Arial';
            ctx.fillText('"Hello Bob"', 110, 245);

            // 甲的私钥
            ctx.fillStyle = '#e17055';
            ctx.fillRect(250, 120, 100, 60);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 120, 100, 60);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('甲的私钥', 300, 145);
            ctx.font = '12px Arial';
            ctx.fillText('(保密)', 300, 165);

            // 加密过程
            ctx.fillStyle = '#e84393';
            ctx.fillRect(400, 140, 120, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(400, 140, 120, 80);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('加密过程', 460, 170);
            ctx.font = '12px Arial';
            ctx.fillText('私钥加密', 460, 190);
            ctx.fillText('生成签名', 460, 205);

            // 数字签名
            ctx.fillStyle = '#00b894';
            ctx.fillRect(570, 140, 100, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(570, 140, 100, 80);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('数字签名', 620, 170);
            ctx.font = '12px Arial';
            ctx.fillText('加密结果', 620, 190);
            ctx.fillText('发送给乙', 620, 205);

            // 连接线
            ctx.strokeStyle = '#fd79a8';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(170, 230);
            ctx.lineTo(400, 180);
            ctx.stroke();

            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(350, 150);
            ctx.lineTo(400, 160);
            ctx.stroke();

            ctx.strokeStyle = '#e84393';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(520, 180);
            ctx.lineTo(570, 180);
            ctx.stroke();

            // 箭头
            drawArrow(ctx, 350, 150, 400, 160, '#e17055');
            drawArrow(ctx, 520, 180, 570, 180, '#e84393');

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('甲使用自己的私钥对消息进行加密，生成数字签名', 350, 300);

            ctx.fillStyle = '#e17055';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('关键：发送方用自己的私钥加密', 350, 330);
        }

        // 绘制签名验证过程（重点）
        function drawVerifyProcess() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('签名验证过程 - 乙用甲的公钥解密', 350, 40);

            // 答案标识
            ctx.fillStyle = '#00b894';
            ctx.fillRect(250, 60, 200, 30);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 60, 200, 30);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('🎯 正确答案', 350, 80);

            // 乙（接收方）
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(550, 100, 120, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(550, 100, 120, 100);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('乙', 610, 130);
            ctx.font = '12px Arial';
            ctx.fillText('(接收方)', 610, 150);
            ctx.fillText('需要验证', 610, 170);
            ctx.fillText('签名真实性', 610, 185);

            // 接收到的签名
            ctx.fillStyle = '#00b894';
            ctx.fillRect(50, 120, 100, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 120, 100, 80);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('数字签名', 100, 150);
            ctx.font = '12px Arial';
            ctx.fillText('来自甲', 100, 170);
            ctx.fillText('需要验证', 100, 185);

            // 甲的公钥（重点突出）
            ctx.fillStyle = '#00b894';
            ctx.fillRect(200, 120, 120, 80);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 4;
            ctx.strokeRect(200, 120, 120, 80);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('甲的公钥', 260, 150);
            ctx.font = '12px Arial';
            ctx.fillText('(公开可获得)', 260, 170);
            ctx.fillText('用于解密验证', 260, 185);

            // 解密过程
            ctx.fillStyle = '#0984e3';
            ctx.fillRect(370, 120, 120, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(370, 120, 120, 80);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('解密验证', 430, 150);
            ctx.font = '12px Arial';
            ctx.fillText('公钥解密', 430, 170);
            ctx.fillText('验证签名', 430, 185);

            // 验证结果
            ctx.fillStyle = '#00b894';
            ctx.fillRect(300, 240, 200, 60);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(300, 240, 200, 60);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('验证成功', 400, 265);
            ctx.font = '12px Arial';
            ctx.fillText('确认消息来自甲', 400, 285);

            // 连接线
            ctx.strokeStyle = '#74b9ff';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(150, 160);
            ctx.lineTo(200, 160);
            ctx.stroke();

            ctx.strokeStyle = '#00b894';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(320, 160);
            ctx.lineTo(370, 160);
            ctx.stroke();

            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(430, 200);
            ctx.lineTo(400, 240);
            ctx.stroke();

            // 箭头
            drawArrow(ctx, 150, 160, 200, 160, '#74b9ff');
            drawArrow(ctx, 320, 160, 370, 160, '#00b894');
            drawArrow(ctx, 430, 200, 400, 240, '#0984e3');

            // 说明（重点突出）
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('乙使用甲的公钥对签名进行解密验证', 350, 320);

            ctx.fillStyle = '#e17055';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('关键：接收方用发送方的公钥解密', 350, 345);
        }

        // 绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            const headlen = 10;
            const dx = toX - fromX;
            const dy = toY - fromY;
            const angle = Math.atan2(dy, dx);

            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fill();
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('B. 甲的公钥')) {
                    option.classList.add('correct');
                }
            });

            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画，重点展示验证过程
                    demonstrateProcess('verify');
                    setTimeout(() => demonstrateProcess('sign'), 3000);
                    setTimeout(() => demonstrateProcess('verify'), 6000);
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateProcess('verify');

            // 自动演示序列，重点展示验证过程
            setTimeout(() => demonstrateProcess('sign'), 4000);
            setTimeout(() => demonstrateProcess('verify'), 8000);
            setTimeout(() => demonstrateProcess('verify'), 12000);
        };
    </script>
</body>
</html>
