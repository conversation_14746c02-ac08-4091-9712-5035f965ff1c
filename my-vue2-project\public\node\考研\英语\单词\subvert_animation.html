<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Subvert</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f2f5;
            color: #333;
            margin: 0;
            flex-direction: column;
        }
        .container {
            text-align: center;
            background-color: #fff;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
            margin: 1rem;
        }
        h1 {
            color: #1a237e;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        canvas {
            background-color: #e8eaf6;
            border-radius: 8px;
            margin-top: 1rem;
            cursor: pointer;
        }
        .controls {
            margin-top: 1.5rem;
        }
        button {
            background-color: #3f51b5;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 1rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }
        button:hover {
            background-color: #303f9f;
            transform: translateY(-2px);
        }
        .explanation {
            margin-top: 2rem;
            text-align: left;
            line-height: 1.8;
            background-color: #f9f9f9;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 5px solid #3f51b5;
        }
        .explanation h2 {
            color: #1a237e;
            margin-top: 0;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>Subvert (v.) 颠覆，推翻</h1>
    <canvas id="wordCanvas" width="600" height="400"></canvas>
    <div class="controls">
        <button id="playBtn">开始动画</button>
    </div>
    <div class="explanation">
        <h2>为什么要这样设计？</h2>
        <p>你好！今天我们学习的单词是 <strong>subvert</strong>。</p>
        <p>
            <strong>单词拆解：</strong><br>
            <ul>
                <li><strong>sub-</strong>: 一个常见的前缀，意思是"在...下面"(under)。就像 'submarine'(潜水艇)是在水下，'subway'(地铁)是在地下。</li>
                <li><strong>vert</strong>: 这是一个词根，意思是"转动"(to turn)。我们可以在 'convert'(转变)或 'introvert'(内向的人，思想转向内心)等词中看到它。</li>
            </ul>
        </p>
        <p>
            <strong>动画故事：</strong><br>
            为了让你更好地记住 'subvert'(颠覆)这个词，我设计了这个动画：
            <ol>
                <li>一开始，你会看到一个稳固的"王座"，它代表着一个既有的权力或系统。</li>
                <li>接着，代表前缀 <code>sub-</code>(在...下面)的力量会从王座的下方出现。</li>
                <li>然后，这些力量会开始"转动"(<code>vert</code>)王座的基石。</li>
                <li>最后，当基石被转动、破坏后，整个王座就会倾倒、崩塌。</li>
            </ol>
        </p>
        <p>这个过程就是 <strong>"从下面(sub-)把东西转动(vert)过来"</strong>，从而引申出"颠覆、推翻"的含义。希望这个故事能帮你牢牢记住 'subvert'！请点击"开始动画"按钮，亲自体验一下"颠覆"的过程吧！</p>
    </div>
</div>

<script>
const canvas = document.getElementById('wordCanvas');
const ctx = canvas.getContext('2d');
const playBtn = document.getElementById('playBtn');

let stage = 'initial'; // initial, sub, vert, collapse, end
let animationFrameId;

// Animation objects
const throne = {
    x: canvas.width / 2,
    y: canvas.height - 50,
    baseWidth: 150,
    baseHeight: 20,
    pillarHeight: 80,
    pillarWidth: 20,
    seatWidth: 100,
    seatHeight: 20,
    backHeight: 120,
    backWidth: 15,
    rotation: 0,
    collapseSpeed: 0,
    pillarRotation: [0, 0, 0]
};

const subverters = [
    { x: throne.x - 60, y: canvas.height + 20, finalY: throne.y - 10, size: 10, alpha: 0 },
    { x: throne.x, y: canvas.height + 20, finalY: throne.y - 10, size: 10, alpha: 0 },
    { x: throne.x + 60, y: canvas.height + 20, finalY: throne.y - 10, size: 10, alpha: 0 }
];

function drawText(text, x, y, size = 20, color = '#000', alpha = 1) {
    ctx.globalAlpha = alpha;
    ctx.fillStyle = color;
    ctx.font = `bold ${size}px Arial`;
    ctx.textAlign = 'center';
    ctx.fillText(text, x, y);
    ctx.globalAlpha = 1;
}

function drawThrone() {
    ctx.save();
    ctx.translate(throne.x, throne.y);
    ctx.rotate(throne.rotation);
    ctx.translate(-throne.x, -throne.y);

    // Base
    ctx.fillStyle = '#6D4C41';
    ctx.fillRect(throne.x - throne.baseWidth / 2, throne.y - throne.baseHeight, throne.baseWidth, throne.baseHeight);

    // Pillars
    const pillarPositions = [
        throne.x - throne.baseWidth / 2 + 25,
        throne.x,
        throne.x + throne.baseWidth / 2 - 25
    ];
    pillarPositions.forEach((posX, i) => {
        ctx.save();
        ctx.translate(posX, throne.y - throne.baseHeight - throne.pillarHeight / 2);
        ctx.rotate(throne.pillarRotation[i]);
        ctx.fillStyle = '#8D6E63';
        ctx.fillRect(-throne.pillarWidth / 2, -throne.pillarHeight / 2, throne.pillarWidth, throne.pillarHeight);
        ctx.restore();
    });

    const seatY = throne.y - throne.baseHeight - throne.pillarHeight;
    // Seat
    ctx.fillStyle = '#A1887F';
    ctx.fillRect(throne.x - throne.seatWidth / 2, seatY - throne.seatHeight, throne.seatWidth, throne.seatHeight);
    
    // Back
    ctx.fillStyle = '#5D4037';
    ctx.fillRect(throne.x - throne.seatWidth / 2, seatY - throne.seatHeight - throne.backHeight, throne.backWidth, throne.backHeight);
    ctx.fillRect(throne.x + throne.seatWidth / 2 - throne.backWidth, seatY - throne.seatHeight - throne.backHeight, throne.backWidth, throne.backHeight);
    ctx.fillRect(throne.x - throne.seatWidth/2, seatY - throne.seatHeight - throne.backHeight, throne.seatWidth, throne.backWidth);

    ctx.restore();
}

function drawSubverters() {
    subverters.forEach(s => {
        ctx.beginPath();
        ctx.arc(s.x, s.y, s.size, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(198, 40, 40, ${s.alpha})`;
        ctx.fill();
    });
}

function reset() {
    if (animationFrameId) cancelAnimationFrame(animationFrameId);
    stage = 'initial';
    throne.rotation = 0;
    throne.collapseSpeed = 0;
    throne.pillarRotation = [0, 0, 0];
    subverters.forEach(s => {
        s.y = canvas.height + 20;
        s.alpha = 0;
    });
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    drawThrone();
    drawText('一个稳固的权力象征', canvas.width / 2, 50);
}

function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    switch(stage) {
        case 'initial':
            drawThrone();
            drawText('点击开始', canvas.width / 2, 50);
            break;
            
        case 'sub':
            drawThrone();
            let allSubInPlace = true;
            subverters.forEach(s => {
                if (s.y > s.finalY) {
                    s.y -= 2;
                    allSubInPlace = false;
                }
                s.alpha = Math.min(1, s.alpha + 0.02);
            });
            drawSubverters();
            drawText('sub: under (在...下面)', canvas.width / 2, 50, 30, '#c62828');
            if (allSubInPlace) {
                stage = 'sub_done';
                setTimeout(() => {
                    drawText('点击基座，执行 "转动"', canvas.width / 2, 90, 20, '#3f51b5');
                }, 500);
            }
            break;

        case 'sub_done':
             drawThrone();
             drawSubverters();
             drawText('sub: under (在...下面)', canvas.width / 2, 50, 30, '#c62828');
             drawText('点击基座，执行 "转动"', canvas.width / 2, 90, 20, '#3f51b5');
            break;

        case 'vert':
            let allPillarsRotated = true;
            for(let i = 0; i < throne.pillarRotation.length; i++) {
                if(throne.pillarRotation[i] < Math.PI / 6) {
                    throne.pillarRotation[i] += 0.02;
                    allPillarsRotated = false;
                }
            }
            drawThrone();
            drawSubverters();
            drawText('vert: turn (转动)', canvas.width / 2, 50, 30, '#283593');
            if(allPillarsRotated) {
                stage = 'collapse';
            }
            break;
            
        case 'collapse':
            throne.collapseSpeed += 0.0005;
            throne.rotation += throne.collapseSpeed;
            if (throne.y < canvas.height + 200) {
                 throne.y += 2;
            }
            drawThrone();
            drawSubverters();
            if (throne.rotation > Math.PI / 4) {
                stage = 'end';
            }
            break;
            
        case 'end':
            drawThrone();
             drawText('subvert: 颠覆，推翻', canvas.width / 2, canvas.height/2, 40, '#b71c1c');
            playBtn.innerText = '重新播放';
            playBtn.style.display = 'inline-block';
            break;
    }
    
    animationFrameId = requestAnimationFrame(animate);
}

playBtn.addEventListener('click', () => {
    reset();
    stage = 'sub';
    playBtn.style.display = 'none';
    animate();
});

canvas.addEventListener('click', (e) => {
    if (stage === 'sub_done') {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const baseLeft = throne.x - throne.baseWidth / 2;
        const baseRight = throne.x + throne.baseWidth / 2;
        const baseTop = throne.y - throne.baseHeight;
        const baseBottom = throne.y;

        if (x > baseLeft && x < baseRight && y > baseTop && y < baseBottom) {
             stage = 'vert';
        }
    }
});


reset();

</script>

</body>
</html> 