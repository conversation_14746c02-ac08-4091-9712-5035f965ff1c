<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Generate</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #00bcd4; /* 青色主题，代表科技与生成 */
            --secondary-color: #0097a7;
            --accent-color: #ffeb3b; /* 黄色点缀，代表能量 */
            --dark-bg: #263238;
            --light-bg: #f8f9fa;
            --panel-bg: #ffffff;
            --text-color: #333;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #eceff1;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--primary-color);
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: #546e7a;
            margin-bottom: 20px;
        }

        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--secondary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: #555; }
        .breakdown-section { margin-top: 25px; padding: 20px; background-color: #cfd8dc; border-radius: 10px; }
        .breakdown-section h3 { margin-top: 0; color: var(--secondary-color); font-size: 1.3em; margin-bottom: 15px; }
        .morpheme-btn { padding: 8px 15px; border: 2px solid var(--primary-color); border-radius: 20px; background-color: transparent; color: var(--primary-color); font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; }
        .morpheme-btn:hover, .morpheme-btn.active { background-color: var(--primary-color); color: white; transform: translateY(-2px); box-shadow: 0 0 10px var(--primary-color);}

        .animation-panel {
            flex: 2;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            background: var(--dark-bg);
        }

        .activity-title { font-size: 1.8em; color: var(--light-bg); margin-bottom: 20px; text-align: center; }
        .activity-wrapper { display: none; width: 100%; height: 100%; flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        .game-container { width: 90%; max-width: 600px; height: 400px; border: 2px dashed #546e7a; border-radius: 15px; position: relative; overflow: hidden; display: flex; align-items: center; justify-content: center; background: #37474f; }
        .control-button { margin-top: 30px; padding: 15px 30px; font-size: 1.2em; color: var(--dark-bg); background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; box-shadow: 0 4px 15px rgba(0, 188, 212, 0.4); }

        /* 词缀 gen- (生产) */
        #gen-factory { width: 100px; height: 80px; background: #78909c; position: absolute; left: 10%; bottom: 40%; }
        #gen-conveyor { width: 60%; height: 10px; background: #546e7a; position: absolute; left: calc(10% + 100px); bottom: calc(40% + 10px); }
        .gen-block { width: 30px; height: 30px; background: var(--accent-color); position: absolute; left: calc(10% + 50px); bottom: calc(40% + 20px); animation: move-block 3s linear; }
        @keyframes move-block {
            from { transform: translateX(0); }
            to { transform: translateX(250px); opacity: 0; }
        }

        /* 词缀 -ate (使成为) */
        #ate-stamp { width: 80px; height: 80px; border: 5px solid var(--accent-color); color: var(--accent-color); font-size: 2em; font-weight: bold; display: flex; justify-content: center; align-items: center; position: absolute; top: -100px; left: calc(50% - 40px); transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55); }
        #ate-text { color: white; font-size: 3em; transition: all 0.5s; }
        .ate-game-run #ate-stamp { top: 40%; }
        .ate-game-run #ate-text { transform: scale(1.2); color: var(--accent-color); }
        
        /* 完整动画: 电网发电 */
        @keyframes spin { from { transform: translate(-50%, -50%) rotate(0deg); } to { transform: translate(-50%, -50%) rotate(360deg); } }
        @keyframes pulse { 0%, 100% { opacity: 0; } 50% { opacity: 1; } }
        #power-generator { width: 80px; height: 80px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }
        .core { position: absolute; top: 0; left: 0; width: 100%; height: 100%; border-radius: 50%; border: 5px solid; animation: spin 4s linear infinite; }
        #core1 { border-color: var(--primary-color) transparent; animation-direction: reverse; }
        #core2 { border-color: var(--accent-color) transparent; transform: scale(0.7); }
        .power-line { position: absolute; top: 50%; left: 50%; height: 4px; background: var(--primary-color); transform-origin: left; }
        .city { position: absolute; width: 30px; height: 30px; border-radius: 50%; background: #546e7a; transition: background-color 0.5s; }
        .energy-pulse { position: absolute; width: 10px; height: 10px; background: var(--accent-color); border-radius: 50%; animation: pulse 2s ease-in-out infinite; }
        .power-run .city { background: var(--accent-color); box-shadow: 0 0 15px var(--accent-color); }

        /* Canvas */
        canvas { background: var(--dark-bg); border-radius: 15px; }

    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>generate</h1>
            <p class="pronunciation">[ˈdʒenəreɪt]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 产生, 造成, 引起</p>
                <p><strong>含义：</strong> 指创造或产生某物，可以是具体的能量、热量，也可以是抽象的情感、想法。</p>
                <div class="example">
                    <p><strong>例句：</strong> The wind turbines generate electricity for the local community.</p>
                    <p><strong>翻译：</strong>风力涡轮机为当地社区发电。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>交互式词缀解析</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="gen-game">gen- (生产)</button>
                    <button class="morpheme-btn" data-activity="ate-game">-ate (使...)</button>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>完整单词活动</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画: 电网发电</button>
                    <button class="morpheme-btn" data-activity="canvas-animation">互动: 生成艺术</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p style="color: white;">点击左侧按钮，开始"生成"之旅！</p></div>
            
            <div id="gen-game" class="activity-wrapper">
                <div class="game-container" id="gen-container">
                    <div id="gen-factory"></div><div id="gen-conveyor"></div>
                </div>
                <button class="control-button" id="gen-btn">生产 (gen-)</button>
            </div>
            
            <div id="ate-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="ate-stamp">ATE</div><span id="ate-text">Origin</span>
                </div>
                <button class="control-button" id="ate-btn">动词化 (-ate)</button>
            </div>

            <div id="full-animation" class="activity-wrapper">
                <div class="game-container" id="power-container">
                    <div id="power-generator"><div id="core1" class="core"></div><div id="core2" class="core"></div></div>
                </div>
                <button class="control-button" id="power-btn">发电</button>
            </div>

            <div id="canvas-animation" class="activity-wrapper">
                <div class="game-container"><canvas id="generative-canvas" width="600" height="400"></canvas></div>
                <button class="control-button" id="generative-btn">开始生成</button>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                document.getElementById(btn.dataset.activity)?.classList.add('active');
            });
        });

        // gen- game
        document.getElementById('gen-btn').addEventListener('click', () => {
            const container = document.getElementById('gen-container');
            const block = document.createElement('div');
            block.className = 'gen-block';
            container.appendChild(block);
            setTimeout(() => block.remove(), 3000);
        });

        // ate- game
        document.getElementById('ate-btn').addEventListener('click', () => {
            const container = document.querySelector('#ate-game .game-container');
            const textEl = document.getElementById('ate-text');
            container.classList.add('ate-game-run');
            setTimeout(() => {
                textEl.textContent = 'Originate';
                setTimeout(() => container.classList.remove('ate-game-run'), 500);
            }, 500);
        });

        // power grid game
        function setupPowerGrid() {
            const container = document.getElementById('power-container');
            const btn = document.getElementById('power-btn');
            const cities = [];
            for (let i = 0; i < 5; i++) {
                const angle = (i / 5) * Math.PI * 2;
                const dist = 150;
                const line = document.createElement('div');
                line.className = 'power-line';
                line.style.width = `${dist}px`;
                line.style.transform = `rotate(${angle * 180 / Math.PI}deg)`;
                
                const city = document.createElement('div');
                city.className = 'city';
                city.style.left = `${container.clientWidth/2 + Math.cos(angle) * dist - 15}px`;
                city.style.top = `${container.clientHeight/2 + Math.sin(angle) * dist - 15}px`;
                
                const pulse = document.createElement('div');
                pulse.className = 'energy-pulse';
                pulse.style.animationDelay = `${i * 0.4}s`;
                line.appendChild(pulse);
                
                container.appendChild(line);
                container.appendChild(city);
                cities.push(city);
            }
            btn.addEventListener('click', () => {
                container.classList.toggle('power-run');
                btn.textContent = container.classList.contains('power-run') ? '停止供电' : '发电';
            });
        }
        setupPowerGrid();

        // canvas game
        function setupGenerativeArt() {
            const canvas = document.getElementById('generative-canvas');
            const btn = document.getElementById('generative-btn');
            if (!canvas || !btn) return;
            const ctx = canvas.getContext('2d');
            let walkers = [];
            let animationId = null;

            class Walker {
                constructor() {
                    this.x = Math.random() * canvas.width;
                    this.y = Math.random() * canvas.height;
                    this.color = `hsl(${Math.random() * 60 + 180}, 100%, 70%)`; // Shades of cyan/blue
                }
                step() {
                    const choice = Math.floor(Math.random() * 4);
                    if (choice === 0) this.x += 2;
                    else if (choice === 1) this.x -= 2;
                    else if (choice === 2) this.y += 2;
                    else this.y -= 2;
                    
                    this.x = Math.max(0, Math.min(this.x, canvas.width));
                    this.y = Math.max(0, Math.min(this.y, canvas.height));
                }
                draw() {
                    ctx.fillStyle = this.color;
                    ctx.fillRect(this.x, this.y, 2, 2);
                }
            }
            
            function init() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                walkers = [];
                for (let i = 0; i < 10; i++) {
                    walkers.push(new Walker());
                }
            }

            function animate() {
                walkers.forEach(w => {
                    w.step();
                    w.draw();
                });
                animationId = requestAnimationFrame(animate);
            }

            btn.addEventListener('click', () => {
                init(); // Always restart to generate a new piece
                if (!animationId) {
                    animate();
                }
            });
            
            const observer = new MutationObserver(() => {
                const isActive = document.getElementById('canvas-animation').classList.contains('active');
                if (isActive) {
                    init();
                    if (!animationId) animate();
                } else {
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                        animationId = null;
                    }
                }
            });
            observer.observe(document.getElementById('canvas-animation'), { attributes: true, attributeFilter: ['class'] });
        }
        setupGenerativeArt();
    });
    </script>
</body>
</html> 