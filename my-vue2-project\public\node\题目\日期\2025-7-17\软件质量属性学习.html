<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件质量属性 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            cursor: pointer;
        }

        .concepts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .concept-card:hover::before {
            left: 100%;
        }

        .concept-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .concept-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .concept-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .concept-desc {
            color: #666;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .answer-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .answer-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .correct-answer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .explanation {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.7;
            margin-bottom: 15px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .interactive-hint {
            text-align: center;
            color: rgba(255,255,255,0.8);
            margin-top: 20px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件质量属性学习</h1>
            <p class="subtitle">通过互动动画理解软件系统的质量特征</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>【软考达人-回忆版】</strong><br><br>
                软件系统质量属性（Quality Attribute）是一个系统的可测量或者可测试的属性，它被用来描述系统满足利益相关者需求的程度，其中：<br><br>
                <span class="highlight">（ ）</span> 关注的是当需要修改缺陷、增加功能、提高质量属性时，定位修改点并实施修改的难易程度<br><br>
                <span class="highlight">（ ）</span> 关注的是当用户数和数据量增加时，软件系统维持高服务质量的能力
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="gameCanvas" width="800" height="400"></canvas>
        </div>
        <div class="interactive-hint">
            💡 点击画布中的质量属性图标来学习每个概念！
        </div>

        <div class="concepts-grid">
            <div class="concept-card" data-concept="reliability">
                <span class="concept-icon">🛡️</span>
                <h3 class="concept-title">可靠性 (Reliability)</h3>
                <p class="concept-desc">系统在规定条件下和规定时间内完成规定功能的能力</p>
            </div>
            
            <div class="concept-card" data-concept="testability">
                <span class="concept-icon">🔍</span>
                <h3 class="concept-title">可测试性 (Testability)</h3>
                <p class="concept-desc">软件能够被有效测试的程度，便于发现和定位缺陷</p>
            </div>
            
            <div class="concept-card" data-concept="maintainability">
                <span class="concept-icon">🔧</span>
                <h3 class="concept-title">可维护性 (Maintainability)</h3>
                <p class="concept-desc">修改软件以纠正缺陷、改进性能或适应环境变化的容易程度</p>
            </div>
            
            <div class="concept-card" data-concept="reusability">
                <span class="concept-icon">♻️</span>
                <h3 class="concept-title">可重用性 (Reusability)</h3>
                <p class="concept-desc">软件组件能够在不同环境中重复使用的程度</p>
            </div>
        </div>

        <div class="answer-section">
            <h2 class="answer-title">📝 题目解析</h2>
            <div class="correct-answer">
                正确答案：第一空 - C (可维护性)，第二空 - 可扩展性/性能
            </div>
            <div class="explanation">
                <h3>🎯 知识点解析：</h3>
                <p><strong>可维护性 (Maintainability)</strong>：关注修改缺陷、增加功能、提高质量属性时的难易程度。包括可理解性、可修改性、可测试性等子特性。</p>
                <p><strong>可扩展性/性能 (Scalability/Performance)</strong>：关注系统在用户数和数据量增加时维持服务质量的能力。</p>
                <h3>🔍 记忆技巧：</h3>
                <p>• <strong>可维护性</strong> = "维修工人" → 修改和维护的容易程度</p>
                <p>• <strong>可扩展性</strong> = "弹性橡皮筋" → 能够承受更大负载</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 质量属性数据
        const attributes = [
            { name: '可靠性', icon: '🛡️', x: 150, y: 100, color: '#FF6B6B', active: false },
            { name: '可测试性', icon: '🔍', x: 350, y: 100, color: '#4ECDC4', active: false },
            { name: '可维护性', icon: '🔧', x: 550, y: 100, color: '#45B7D1', active: false },
            { name: '可重用性', icon: '♻️', x: 650, y: 100, color: '#96CEB4', active: false }
        ];
        
        let animationFrame = 0;
        let selectedAttribute = null;
        
        function drawBackground() {
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
        
        function drawAttribute(attr, index) {
            const pulse = Math.sin(animationFrame * 0.05 + index) * 0.1 + 1;
            const size = attr.active ? 80 * pulse : 60 * pulse;
            
            // 绘制圆形背景
            ctx.beginPath();
            ctx.arc(attr.x, attr.y, size, 0, Math.PI * 2);
            ctx.fillStyle = attr.active ? attr.color : attr.color + '80';
            ctx.fill();
            ctx.strokeStyle = attr.color;
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 绘制图标
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(attr.icon, attr.x, attr.y - 5);
            
            // 绘制名称
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText(attr.name, attr.x, attr.y + size + 20);
            
            // 如果被选中，显示详细信息
            if (attr.active) {
                drawDetailInfo(attr);
            }
        }
        
        function drawDetailInfo(attr) {
            const infoY = attr.y + 150;
            
            // 绘制信息框
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.fillRect(attr.x - 100, infoY, 200, 80);
            ctx.strokeStyle = attr.color;
            ctx.lineWidth = 2;
            ctx.strokeRect(attr.x - 100, infoY, 200, 80);
            
            // 绘制详细信息
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            
            let description = '';
            switch(attr.name) {
                case '可靠性':
                    description = '系统稳定运行\n不出故障的能力';
                    break;
                case '可测试性':
                    description = '便于测试和\n发现问题的程度';
                    break;
                case '可维护性':
                    description = '修改和维护\n的容易程度';
                    break;
                case '可重用性':
                    description = '组件重复使用\n的可能性';
                    break;
            }
            
            const lines = description.split('\n');
            lines.forEach((line, i) => {
                ctx.fillText(line, attr.x, infoY + 25 + i * 20);
            });
        }
        
        function drawConnections() {
            // 绘制题目关键词连接
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            
            // 连接可维护性到"修改缺陷"
            if (attributes[2].active) {
                ctx.beginPath();
                ctx.moveTo(attributes[2].x, attributes[2].y + 60);
                ctx.lineTo(200, 300);
                ctx.stroke();
                
                ctx.fillStyle = '#667eea';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'left';
                ctx.fillText('修改缺陷、增加功能', 210, 305);
            }
            
            ctx.setLineDash([]);
        }
        
        function animate() {
            animationFrame++;
            
            drawBackground();
            drawConnections();
            
            attributes.forEach((attr, index) => {
                drawAttribute(attr, index);
            });
            
            requestAnimationFrame(animate);
        }
        
        // 鼠标点击事件
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            attributes.forEach(attr => {
                const distance = Math.sqrt((x - attr.x) ** 2 + (y - attr.y) ** 2);
                if (distance < 60) {
                    // 重置所有属性
                    attributes.forEach(a => a.active = false);
                    // 激活点击的属性
                    attr.active = true;
                    selectedAttribute = attr;
                    
                    // 触发概念卡片高亮
                    highlightConceptCard(attr.name);
                }
            });
        });
        
        function highlightConceptCard(name) {
            const cards = document.querySelectorAll('.concept-card');
            cards.forEach(card => {
                card.classList.remove('pulse');
                const title = card.querySelector('.concept-title').textContent;
                if (title.includes(name)) {
                    card.classList.add('pulse');
                    card.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            });
        }
        
        // 概念卡片点击事件
        document.querySelectorAll('.concept-card').forEach(card => {
            card.addEventListener('click', () => {
                const concept = card.dataset.concept;
                const title = card.querySelector('.concept-title').textContent;
                
                // 在画布中激活对应属性
                attributes.forEach(attr => {
                    attr.active = false;
                    if (title.includes(attr.name)) {
                        attr.active = true;
                    }
                });
                
                // 添加点击效果
                card.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    card.style.transform = '';
                }, 150);
            });
        });
        
        // 开始动画
        animate();
        
        // 页面加载完成后的提示动画
        setTimeout(() => {
            attributes[2].active = true; // 默认激活可维护性
            highlightConceptCard('可维护性');
        }, 2000);
    </script>
</body>
</html>
