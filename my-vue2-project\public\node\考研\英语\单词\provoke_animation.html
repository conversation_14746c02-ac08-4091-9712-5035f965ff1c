<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Provoke Animation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        .container {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #fff;
        }
        .translation {
            margin-top: 10px;
            font-size: 1.2em;
        }
        .explanation {
            margin-top: 10px;
            text-align: left;
            max-width: 600px;
        }
        button {
            margin-top: 10px;
            padding: 10px 20px;
            font-size: 1em;
            cursor: pointer;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>Provoke</h1>
    <canvas id="wordCanvas" width="600" height="300"></canvas>
    <div class="translation">
        <p><strong>翻译:</strong> v. 激怒；惹起；驱使</p>
    </div>
    <div class="explanation">
        <p><strong>词源拆解:</strong></p>
        <p>单词 <strong>provoke</strong> 由两部分组成：</p>
        <ul>
            <li><strong>pro-</strong>: 一个前缀，意思是 "向前" (forward) 或 "出来" (forth)。</li>
            <li><strong>voke</strong>: 一个词根，源自拉丁语 'vocare'，意思是 "呼唤" (to call)。</li>
        </ul>
        <p><strong>故事记忆法:</strong></p>
        <p>想象一个场景：一个人（the provoker）<strong>向前</strong>（`pro-`）跨出一步，用言语或行动去**呼唤**（`voke`）另一个人的情绪。他的目的是将对方潜藏的怒火或反应"叫"出来。这个行为就是 "provoke" —— 激起、惹怒。动画将展示一个人如何通过"召唤"来激怒另一个人。</p>
    </div>
    <button id="replay">Replay Animation</button>
</div>

<script>
    const canvas = document.getElementById('wordCanvas');
    const ctx = canvas.getContext('2d');
    const replayBtn = document.getElementById('replay');

    let animationFrameId;

    const provoker = {
        x: 150,
        y: 200,
        initialX: 100,
        hasMoved: false,
        draw() {
            // Body
            ctx.beginPath();
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x, this.y - 40);
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 3;
            ctx.stroke();

            // Head
            ctx.beginPath();
            ctx.arc(this.x, this.y - 50, 10, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();

            // Arms
            ctx.beginPath();
            ctx.moveTo(this.x, this.y - 35);
            ctx.lineTo(this.x - 15, this.y - 20);
            ctx.moveTo(this.x, this.y - 35);
            ctx.lineTo(this.x + 15, this.y - 20);
            ctx.stroke();

            // Legs
            ctx.beginPath();
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x - 10, this.y + 20);
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x + 10, this.y + 20);
            ctx.stroke();
        }
    };
    
    const target = {
        x: 450,
        y: 200,
        color: 'black',
        isProvoked: false,
        draw() {
            // Body
            ctx.beginPath();
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x, this.y - 40);
            ctx.strokeStyle = this.color;
            ctx.lineWidth = 3;
            ctx.stroke();

            // Head
            ctx.beginPath();
            ctx.arc(this.x, this.y - 50, 10, 0, Math.PI * 2);
            ctx.fillStyle = this.color;
            ctx.fill();

            // Arms
            ctx.beginPath();
            ctx.moveTo(this.x, this.y - 35);
            ctx.lineTo(this.x - 15, this.y - 20);
            ctx.moveTo(this.x, this.y - 35);
            ctx.lineTo(this.x + 15, this.y - 20);
            ctx.strokeStyle = this.color;
            ctx.stroke();

            // Legs
            ctx.beginPath();
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x - 10, this.y + 20);
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x + 10, this.y + 20);
            ctx.stroke();
            
            // Angry lines if provoked
            if(this.isProvoked){
                ctx.beginPath();
                ctx.moveTo(this.x - 15, this.y - 65);
                ctx.lineTo(this.x + 15, this.y - 55);
                ctx.moveTo(this.x + 15, this.y - 65);
                ctx.lineTo(this.x - 15, this.y - 55);
                ctx.strokeStyle = 'red';
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        }
    }

    class Taunt {
        constructor(x, y) {
            this.x = x;
            this.y = y;
            this.targetX = target.x - 20;
            this.speed = 3;
            this.alpha = 1;
        }

        update() {
            this.x += this.speed;
            if (this.x > this.targetX) {
                this.alpha = 0; // Disappear on hit
                target.isProvoked = true;
                target.color = 'red';
            }
        }

        draw() {
            if (this.alpha <= 0) return;
            ctx.font = 'bold 16px Arial';
            ctx.fillStyle = `rgba(139, 0, 0, ${this.alpha})`;
            ctx.fillText('!', this.x, this.y);
        }
    }

    let taunts = [];
    let proText = { alpha: 0 };
    let vokeText = { alpha: 0 };
    let animationPhase = 'start'; // start, move, taunt, end

    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        provoker.draw();
        target.draw();
        taunts.forEach(t => t.draw());

        // Draw "pro-"
        ctx.font = 'bold 40px Arial';
        ctx.fillStyle = `rgba(255, 0, 0, ${proText.alpha})`;
        ctx.fillText('pro-', 200, 100);
        
        // Draw "voke"
        ctx.font = 'bold 40px Arial';
        ctx.fillStyle = `rgba(0, 0, 255, ${vokeText.alpha})`;
        ctx.fillText('voke', 280, 100);
    }

    function animate() {
        if (animationPhase === 'start' && provoker.x > provoker.initialX) {
            provoker.x -= 1;
            if (provoker.x <= provoker.initialX) {
                animationPhase = 'move';
            }
        } else if (animationPhase === 'move') {
            provoker.x += 2;
            proText.alpha = Math.min(1, proText.alpha + 0.05);
            if (provoker.x >= 150) {
                provoker.hasMoved = true;
                animationPhase = 'taunt';
                taunts.push(new Taunt(provoker.x + 20, provoker.y - 45));
            }
        } else if (animationPhase === 'taunt') {
            vokeText.alpha = Math.min(1, vokeText.alpha + 0.05);
            taunts.forEach(t => t.update());
            taunts = taunts.filter(t => t.alpha > 0);
            if (taunts.length === 0) {
                animationPhase = 'end';
            }
        }
        
        draw();
        
        if (animationPhase !== 'end') {
            animationFrameId = requestAnimationFrame(animate);
        }
    }
    
    function startAnimation() {
        cancelAnimationFrame(animationFrameId);
        provoker.x = 150;
        provoker.hasMoved = false;
        target.isProvoked = false;
        target.color = 'black';
        taunts = [];
        proText.alpha = 0;
        vokeText.alpha = 0;
        animationPhase = 'move';
        animate();
    }

    replayBtn.addEventListener('click', startAnimation);
    startAnimation();

</script>
</body>
</html> 