<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机系统层次结构与微程序动画演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 30px;
            text-align: center;
        }
        .question-box {
            text-align: left;
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        .question-box p {
            font-size: 1.1em;
            margin: 0 0 15px 0;
        }
        .question-box .options {
            list-style-type: none;
            padding-left: 0;
        }
        .question-box .options li {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .question-box .options .correct {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
         .question-box .options .incorrect {
            color: #777;
        }
        canvas {
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1em;
            border-radius: 25px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        #explanation {
            font-size: 1.2em;
            font-weight: bold;
            color: #0056b3;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: opacity 0.5s;
        }
        .final-answer {
            margin-top: 20px;
            padding: 15px;
            background-color: #d4edda;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
        }
        .final-answer h3 {
            margin-top: 0;
            color: #155724;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="question-box">
        <p><strong>题目：</strong>计算机系统是一个硬件和软件综合体，位于硬联逻辑层面上的微程序是用微指令编写的。以下叙述中，正确的是（）。</p>
        <ul class="options">
            <li id="option-a" class="correct">A: 微程序一般由硬件执行</li>
            <li id="option-b" class="incorrect">B: 微程序一般是由操作系统来调度和执行</li>
            <li id="option-c" class="incorrect">C: 微程序一般用高级语言构造的编译器期译后米执行</li>
            <li id="option-d" class="incorrect">D: 微程序一般用高级语言构造的解释器来解释执行</li>
        </ul>
    </div>
    
    <h2>交互式动画：代码是如何被计算机执行的？</h2>
    <canvas id="computerCanvas" width="840" height="450"></canvas>
    <div id="explanation">点击"下一步"开始观看动画</div>
    <div class="controls">
        <button id="nextBtn">下一步</button>
        <button id="resetBtn">重置</button>
    </div>
    <div id="finalAnswer" class="final-answer" style="display: none;">
        <h3>结论</h3>
        <p>动画演示了，程序员写的高级代码需要经过层层"翻译"，最终变成微指令，由最底层的硬件直接执行。因此，<strong>正确答案是 A</strong>。</p>
        <p>微程序是硬件的一部分，它像一个内置的翻译官，帮助硬件理解并执行机器指令。</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('computerCanvas');
    const ctx = canvas.getContext('2d');
    const nextBtn = document.getElementById('nextBtn');
    const resetBtn = document.getElementById('resetBtn');
    const explanationDiv = document.getElementById('explanation');
    const finalAnswerDiv = document.getElementById('finalAnswer');

    const layers = [
        { name: '高级语言 (程序员编写)', y: 50, color: '#28a745' },
        { name: '编译器/解释器', y: 120, color: '#17a2b8' },
        { name: '机器语言 (机器指令)', y: 190, color: '#ffc107' },
        { name: '微指令 (由微程序提供)', y: 260, color: '#fd7e14' },
        { name: '硬件 (CPU内部电路)', y: 330, color: '#dc3545' }
    ];

    const steps = [
        { code: 'A = B + C', y: layers[0].y, text: '1. 程序员用高级语言 (如C++, Java, Python) 编写代码。' },
        { code: '...编译中...', y: layers[1].y, text: '2. 编译器将高级代码翻译成机器能直接看懂的语言。' },
        { code: 'ADD R1, R2', y: layers[2].y, text: '3. 翻译结果是机器指令，例如 "把两个数加起来"。' },
        { code: '...解释中...', y: layers[3].y, text: '4. 一条机器指令对应一组更基本的微指令。' },
        { code: '执行微指令序列', y: layers[4].y, text: '5. 硬件(CPU)直接读取并执行这些微指令来完成计算！' }
    ];

    let currentStep = -1;
    let codeBall = { x: canvas.width / 2, y: 0, targetY: 0, text: '', alpha: 0 };

    function drawLayer(layer) {
        ctx.fillStyle = layer.color;
        ctx.fillRect(50, layer.y - 25, canvas.width - 100, 50);
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 16px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(layer.name, canvas.width / 2, layer.y);
    }
    
    function drawArrow(fromY, toY) {
        ctx.beginPath();
        ctx.moveTo(canvas.width / 2, fromY + 30);
        ctx.lineTo(canvas.width / 2, toY - 30);
        ctx.strokeStyle = '#555';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        ctx.beginPath();
        ctx.moveTo(canvas.width / 2, toY - 30);
        ctx.lineTo(canvas.width / 2 - 5, toY - 40);
        ctx.lineTo(canvas.width / 2 + 5, toY - 40);
        ctx.closePath();
        ctx.fillStyle = '#555';
        ctx.fill();
    }

    function drawCodeBall() {
        if (codeBall.alpha > 0) {
            ctx.globalAlpha = codeBall.alpha;
            ctx.fillStyle = 'rgba(0, 123, 255, 0.8)';
            ctx.strokeStyle = '#0056b3';
            ctx.lineWidth = 2;
            
            const text = codeBall.text;
            const textWidth = ctx.measureText(text).width;
            const rectWidth = textWidth + 30;
            const rectHeight = 35;
            
            ctx.beginPath();
            ctx.roundRect(codeBall.x - rectWidth/2, codeBall.y - rectHeight/2, rectWidth, rectHeight, [15]);
            ctx.fill();
            ctx.stroke();

            ctx.fillStyle = '#fff';
            ctx.font = 'bold 14px monospace';
            ctx.fillText(text, codeBall.x, codeBall.y);
            ctx.globalAlpha = 1.0;
        }
    }

    function animate() {
        const dy = codeBall.targetY - codeBall.y;
        if (Math.abs(dy) > 1) {
            codeBall.y += dy * 0.1;
        } else {
            codeBall.y = codeBall.targetY;
        }
        
        if (currentStep > -1 && codeBall.alpha < 1) {
            codeBall.alpha += 0.05;
        }

        draw();
        requestAnimationFrame(animate);
    }
    
    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        layers.forEach((layer, index) => {
            drawLayer(layer);
            if (index < layers.length - 1) {
                drawArrow(layer.y, layers[index+1].y);
            }
        });
        drawCodeBall();
    }
    
    function proceedToNextStep() {
        if (currentStep < steps.length - 1) {
            currentStep++;
            const step = steps[currentStep];
            explanationDiv.style.opacity = '0';

            setTimeout(() => {
                codeBall.targetY = step.y;
                codeBall.text = step.code;
                explanationDiv.innerText = step.text;
                explanationDiv.style.opacity = '1';
                 if (currentStep === 0) {
                    codeBall.alpha = 0;
                    codeBall.y = step.y;
                }
            }, 300);

        }
        
        if (currentStep === steps.length - 1) {
            nextBtn.disabled = true;
            setTimeout(() => {
                finalAnswerDiv.style.display = 'block';
            }, 1000);
        }
    }

    function resetAnimation() {
        currentStep = -1;
        codeBall.alpha = 0;
        explanationDiv.innerText = '点击"下一步"开始观看动画';
        finalAnswerDiv.style.display = 'none';
        nextBtn.disabled = false;
        draw();
    }

    nextBtn.addEventListener('click', proceedToNextStep);
    resetBtn.addEventListener('click', resetAnimation);

    // Initial setup
    animate();
</script>

</body>
</html> 