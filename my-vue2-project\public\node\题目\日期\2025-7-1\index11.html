<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式学习：软件构件 vs 对象</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        #main-container {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            padding: 30px;
            width: 100%;
            max-width: 900px;
            transition: all 0.3s ease;
        }
        h1, h2 {
            color: #1a2a4c;
            border-bottom: 2px solid #e0e6ed;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        #question-box p {
            font-size: 1.2em;
            line-height: 1.6;
            color: #455a73;
        }
        #options-box {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 25px 0;
        }
        .option {
            padding: 15px;
            border: 1px solid #d9e2ec;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
        }
        .option:hover {
            border-color: #4a90e2;
            background-color: #f4f8ff;
        }
        .option-char {
            font-weight: bold;
            margin-right: 15px;
            color: #4a90e2;
            font-size: 1.1em;
        }
        .option.selected {
            background-color: #e7f3ff;
            border-color: #4a90e2;
            box-shadow: 0 4px 10px rgba(74, 144, 226, 0.2);
        }
        .option.correct {
            background-color: #e4f8f0;
            border-color: #4caf50;
        }
        .option.incorrect {
            background-color: #fdecea;
            border-color: #f44336;
        }
        #controls-box {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        button:hover {
            background-color: #357abd;
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        #demo-canvas {
            width: 100%;
            height: auto;
            aspect-ratio: 2 / 1;
            background-color: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e0e6ed;
        }
        #explanation-box {
            margin-top: 25px;
            padding: 20px;
            background-color: #f7f9fc;
            border-radius: 8px;
            border-left: 5px solid #4a90e2;
            display: none; /* Initially hidden */
        }
        #explanation-box h2 {
            margin-top: 0;
            color: #4a90e2;
            border: none;
        }
        #explanation-text {
            color: #3c4a5c;
            line-height: 1.7;
        }
    </style>
</head>
<body>

<div id="main-container">
    <div id="question-box">
        <h1>题目 14</h1>
        <p>软件构件是一个独立可部署的软件单元，与程序设计中的对象不同，构件 ( )。</p>
    </div>

    <div id="options-box">
        <div class="option" data-option="A">
            <span class="option-char">A</span>
            <span class="option-text">是一个实例单元，具有唯一的标志</span>
        </div>
        <div class="option" data-option="B">
            <span class="option-char">B</span>
            <span class="option-text">可以利用容器管理自身对外的可见状态</span>
        </div>
        <div class="option" data-option="C">
            <span class="option-char">C</span>
            <span class="option-text">利用工厂方法（如构造函数）来创建自己的实例</span>
        </div>
        <div class="option" data-option="D">
            <span class="option-char">D</span>
            <span class="option-text">之间可以共享一个类元素</span>
        </div>
    </div>

    <div id="controls-box">
        <button id="showObjectBtn">动画演示：对象 (Object)</button>
        <button id="showComponentBtn">动画演示：构件 (Component)</button>
    </div>

    <canvas id="demo-canvas"></canvas>

    <div id="explanation-box">
        <h2>答案解析</h2>
        <p id="explanation-text"></p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('demo-canvas');
    const ctx = canvas.getContext('2d');
    
    // Set canvas size for high DPI displays
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);

    let animationFrameId;

    // --- Drawing Utilities ---
    const drawBox = (x, y, w, h, color, text = '', textColor = '#fff') => {
        ctx.fillStyle = color;
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.rect(x, y, w, h);
        ctx.fill();
        ctx.stroke();
        
        if (text) {
            ctx.fillStyle = textColor;
            ctx.font = 'bold 14px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + w / 2, y + h / 2);
        }
    };
    
    const drawText = (text, x, y, color = '#333', size = 16, align = 'center') => {
        ctx.fillStyle = color;
        ctx.font = `${size}px sans-serif`;
        ctx.textAlign = align;
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x, y);
    };

    const drawArrow = (fromX, fromY, toX, toY) => {
        ctx.strokeStyle = '#f44336';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(fromX, fromY);
        ctx.lineTo(toX, toY);
        ctx.stroke();
        // Arrowhead
        const headlen = 10;
        const angle = Math.atan2(toY - fromY, toX - fromX);
        ctx.beginPath();
        ctx.moveTo(toX, toY);
        ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
        ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
        ctx.closePath();
        ctx.fillStyle = '#f44336';
        ctx.fill();
    };

    const clearCanvas = () => {
        cancelAnimationFrame(animationFrameId);
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    };

    // --- Animation Logic ---
    const animateObject = () => {
        clearCanvas();
        let step = 0;
        const x = rect.width / 2;
        const y = rect.height / 2;

        function animationLoop() {
            clearCanvas();
            drawText('演示: 对象 (Object)', rect.width / 2, 30, '#1a2a4c', 20);

            // Step 1: Show constructor
            drawText("const myObj = new Object();", x, 80, '#007acc', 16);
            
            if (step > 20) {
                 drawArrow(x, 100, x, 140);
            }
            if (step > 40) {
                 // Step 2: Draw object and its state
                drawBox(x - 60, 150, 120, 80, '#4a90e2', 'Object Instance');
                drawText('状态直接在内部', x, 250, '#333', 14);
            }
             if (step > 60) {
                 drawBox(x - 40, 165, 30, 30, '#fff', 'ID', '#333');
                 drawText('具有唯一标志 (A)', x - 130, 180, '#e85a5a', 14, 'left');
            }
             if (step > 80) {
                drawBox(x + 10, 165, 30, 30, '#fff', '...', '#333');
                drawText('通过构造函数创建 (C)', x + 50, 180, '#e85a5a', 14, 'left');
            }
            
            step++;
            if (step < 120) {
                animationFrameId = requestAnimationFrame(animationLoop);
            } else {
                drawText('对象的特征：有自己的状态，通过构造函数直接创建。', x, rect.height - 30, '#333', 14);
            }
        }
        animationLoop();
    };
    
    const animateComponent = () => {
        clearCanvas();
        let step = 0;
        const x = rect.width / 2;
        const y = rect.height / 2;
        
        function animationLoop() {
            clearCanvas();
            drawText('演示: 构件 (Component)', rect.width / 2, 30, '#1a2a4c', 20);

            // Step 1: Draw Container
            if (step > 10) {
                 ctx.globalAlpha = Math.min(1, (step - 10) / 30);
                 drawBox(x - 150, y - 80, 300, 160, '#4caf50', '容器 (Container)');
                 ctx.globalAlpha = 1;
            }
           
            // Step 2: Draw Component inside
            if (step > 40) {
                ctx.globalAlpha = Math.min(1, (step - 40) / 30);
                drawBox(x - 60, y - 40, 120, 80, '#333', '构件', '#fff');
                drawText('(黑盒, 内部状态不可见)', x, y + 65, '#555', 14);
                ctx.globalAlpha = 1;
            }

            // Step 3: Show managed state
            if (step > 70) {
                ctx.globalAlpha = Math.min(1, (step - 70) / 30);
                drawText('外部请求', 50, y, '#f44336', 16, 'left');
                drawArrow(130, y, x - 150, y);
            }
            
            if (step > 100) {
                ctx.globalAlpha = Math.min(1, (step - 100) / 30);
                drawBox(x + 150, y - 25, 100, 50, '#ff9800', '可见状态');
                drawArrow(x + 120, y, x + 150, y);
                 drawText('容器管理状态 (B)', x + 200, y + 45, '#4caf50', 14);
            }

            step++;
            if (step < 150) {
                animationFrameId = requestAnimationFrame(animationLoop);
            } else {
                 drawText('构件的特征：是黑盒，其状态由容器管理和暴露。', x, rect.height - 30, '#333', 14);
            }
        }
        animationLoop();
    };

    // --- Event Listeners ---
    document.getElementById('showObjectBtn').addEventListener('click', animateObject);
    document.getElementById('showComponentBtn').addEventListener('click', animateComponent);
    
    const options = document.querySelectorAll('.option');
    const explanationBox = document.getElementById('explanation-box');
    const explanationText = document.getElementById('explanation-text');

    options.forEach(option => {
        option.addEventListener('click', () => {
            // Remove previous selections and feedback
            options.forEach(opt => {
                opt.classList.remove('selected', 'correct', 'incorrect');
            });

            option.classList.add('selected');
            const selectedOption = option.getAttribute('data-option');
            
            explanationBox.style.display = 'block';

            if (selectedOption === 'B') {
                option.classList.add('correct');
                explanationText.innerHTML = `
                    <strong>回答正确！</strong><br><br>
                    软件构件是一个独立可部署的软件单元。与对象不同，它强调的是"黑盒"复用，其内部状态通常是不直接暴露的。构件运行在一个"容器"中（例如服务器、框架），由容器来管理它的生命周期、依赖关系以及对外可见的状态。
                    <br><br>
                    <strong>其他选项为什么错误:</strong><br>
                    A, C, D 描述的更多是<strong>对象</strong>的典型特征：对象是类的实例，有唯一标识，通过构造函数创建。
                `;
                explanationBox.style.borderColor = '#4caf50';
                explanationBox.querySelector('h2').style.color = '#4caf50';
            } else {
                option.classList.add('incorrect');
                explanationText.innerHTML = `
                    <strong>回答错误，再想想看。</strong><br><br>
                    您选择的这个选项（${selectedOption}）更像是<strong>对象 (Object) </strong>的特征。请仔细观察两个动画演示，留意"容器"在构件模型中所扮演的关键角色。
                `;
                explanationBox.style.borderColor = '#f44336';
                explanationBox.querySelector('h2').style.color = '#f44336';
            }
        });
    });

    // Initial state
    drawText('点击上方按钮，开始动画演示', rect.width/2, rect.height/2, '#aaa', 18);
});
</script>

</body>
</html>
