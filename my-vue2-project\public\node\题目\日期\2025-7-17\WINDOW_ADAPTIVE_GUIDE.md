# 窗口全局自适应功能使用指南

## 🎯 功能概述

本应用实现了完整的窗口全局自适应功能，能够根据窗口大小、设备类型和窗口状态自动调整界面布局和样式。

## 🔧 核心组件

### 1. Electron 主进程增强 (`electron-main.js`)
- 自动检测屏幕分辨率并设置合适的初始窗口大小
- 支持窗口大小变化、最大化、全屏等状态监听
- 提供完整的菜单栏和快捷键支持
- 多显示器支持

### 2. 窗口自适应混入 (`src/mixins/windowAdaptive.js`)
- 提供响应式属性和方法
- 自动检测设备类型（移动/平板/桌面/大屏）
- 实时监听窗口变化事件
- 提供可重写的回调方法

### 3. 响应式CSS系统 (`src/styles/main.css`)
- 基于CSS变量的动态样式系统
- 完整的响应式断点设计
- 设备特定的样式优化
- 窗口状态相关的样式调整

## 📱 设备类型自动检测

| 设备类型 | 屏幕宽度 | 列数 | 字体大小 | 内边距 |
|---------|---------|------|---------|--------|
| 移动设备 | < 768px | 1列 | 14px | 15px |
| 平板设备 | 768px - 1024px | 2列 | 15px | 20px |
| 桌面设备 | 1024px - 1440px | 3列 | 16px | 30px |
| 大屏桌面 | > 1440px | 4列 | 16px | 40px |

## 🖥️ 窗口状态响应

### 最大化状态
- 自动增加内边距以优化大屏显示
- 调整布局间距和字体大小

### 全屏模式
- 隐藏或调整标题栏
- 优化内容区域显示
- 自动调整头部位置

### 窗口缩放
- 支持 Ctrl+0/+/- 缩放快捷键
- 保持布局比例和可读性

## 🎮 快捷键支持

| 快捷键 | 功能 |
|--------|------|
| `F11` | 切换全屏模式 |
| `Ctrl+0` | 重置缩放到100% |
| `Ctrl++` | 放大界面 |
| `Ctrl+-` | 缩小界面 |
| `F12` | 开发者工具 |
| `Ctrl+R` | 重新加载 |
| `Ctrl+Shift+R` | 强制重新加载 |
| `Ctrl+M` | 最小化窗口 |

## 💻 在组件中使用

### 基本使用
```javascript
import windowAdaptive from '@/mixins/windowAdaptive'

export default {
  mixins: [windowAdaptive],
  
  computed: {
    // 自动获得以下响应式属性：
    // - deviceType: 'mobile' | 'tablet' | 'desktop' | 'large-desktop'
    // - isMobile, isTablet, isDesktop: boolean
    // - windowInfo: { width, height, isMaximized, isFullScreen }
    // - containerStyle: 动态CSS变量对象
  }
}
```

### 高级使用
```javascript
export default {
  mixins: [windowAdaptive],
  
  methods: {
    // 重写窗口变化回调
    onWindowResize(size) {
      console.log('窗口大小变化:', size)
      // 在这里添加自定义的窗口大小变化处理逻辑
    },
    
    onWindowMaximize(isMaximized) {
      console.log('窗口最大化状态:', isMaximized)
      // 处理最大化状态变化
    },
    
    onWindowFullScreen(isFullScreen) {
      console.log('全屏状态:', isFullScreen)
      // 处理全屏状态变化
    },
    
    onMenuAction(action) {
      console.log('菜单操作:', action)
      // 处理菜单操作
    }
  },
  
  template: `
    <div :class="getResponsiveClass()" :style="containerStyle">
      <!-- 你的组件内容 -->
    </div>
  `
}
```

## 🎨 CSS响应式变量

应用会自动设置以下CSS变量，可以在样式中直接使用：

```css
.my-component {
  padding: var(--container-padding);
  font-size: var(--font-size-base);
  
  /* 使用响应式网格 */
  display: grid;
  grid-template-columns: repeat(var(--card-columns), 1fr);
  gap: 20px;
}

/* 设备特定样式 */
.device-mobile .my-component {
  /* 移动设备特定样式 */
}

.device-tablet .my-component {
  /* 平板设备特定样式 */
}

.window-maximized .my-component {
  /* 最大化窗口特定样式 */
}

.window-fullscreen .my-component {
  /* 全屏模式特定样式 */
}
```

## 🧪 测试和调试

### 开发模式测试
```bash
npm run electron:serve
```

### 生产模式测试
```bash
npm run build
set NODE_ENV=production
electron .
```

### 完整测试
```bash
test-responsive.bat
```

### 调试信息
在开发模式下，应用会在右下角显示窗口信息，包括：
- 当前设备类型
- 窗口尺寸
- 最大化状态
- 全屏状态
- 运行环境

## 📋 最佳实践

1. **使用混入**: 在需要响应式功能的组件中引入 `windowAdaptive` 混入
2. **CSS变量**: 优先使用CSS变量而不是硬编码的尺寸值
3. **渐进增强**: 确保在所有设备类型上都有良好的用户体验
4. **性能优化**: 避免在窗口变化回调中执行重量级操作
5. **测试覆盖**: 在不同窗口大小下测试应用功能

## 🔍 故障排除

### 常见问题
1. **窗口信息不更新**: 检查是否正确引入了混入
2. **样式不响应**: 确认CSS变量是否正确设置
3. **快捷键不工作**: 检查Electron主进程是否正确注册了快捷键
4. **布局错乱**: 检查响应式断点和CSS媒体查询

### 调试技巧
- 使用开发者工具检查CSS变量值
- 在控制台查看窗口变化事件日志
- 使用窗口信息显示功能监控状态变化
