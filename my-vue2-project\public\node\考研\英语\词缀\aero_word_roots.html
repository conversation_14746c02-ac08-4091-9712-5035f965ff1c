<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词根动画故事 - Aero (空气/飞行)</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        
        header {
            width: 100%;
            padding: 2rem 0;
            text-align: center;
            background-color: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        h1 {
            font-weight: 300;
            font-size: 2.5rem;
            margin: 0;
            color: #2c3e50;
        }
        
        .subtitle {
            color: #7f8c8d;
            font-weight: 300;
            margin-top: 0.5rem;
        }
        
        .container {
            width: 90%;
            max-width: 1000px;
            margin: 2rem auto;
            padding: 2rem;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.05);
        }
        
        .canvas-container {
            width: 100%;
            height: 400px;
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            background-color: #f0f8ff;
        }
        
        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        .word-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .word-list {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .word-card {
            background-color: white;
            padding: 1rem;
            border-radius: 6px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            flex: 1 1 200px;
        }
        
        .word-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .word-card h3 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        
        .word-card p {
            color: #7f8c8d;
            margin-bottom: 0;
        }
        
        .explanation {
            margin: 2rem 0;
            line-height: 1.7;
        }
        
        .story-text {
            padding: 1rem;
            background-color: #f0f8ff;
            border-left: 4px solid #3498db;
            margin: 1rem 0;
            font-size: 1.1rem;
            line-height: 1.8;
        }
        
        footer {
            text-align: center;
            padding: 2rem;
            color: #7f8c8d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <header>
        <h1>词根动画故事</h1>
        <p class="subtitle">Aero (aer, aeri) - 空气、飞行</p>
    </header>
    
    <div class="container">
        <div class="explanation">
            <h2>词根解析</h2>
            <p>词根 <strong>aero</strong>（变形：aer, aeri）源自希腊语，表示"空气"或"飞行"的含义。通过了解这个词根，我们可以轻松记忆和理解许多英语单词。</p>
            <p>下面我们将通过一个生动的故事，帮助你理解和记忆这个词根及其相关词汇。</p>
        </div>
        
        <div class="story-text">
            <p>在一个名为"Aerosphere"（大气层）的世界里，生活着一群"Aerial"（空中的）生物。他们通过"Aeration"（通气）呼吸，形态各异但都是"Aeriform"（气态的）。这个世界由"Aerology"（气象学）专家研究，他们使用"Aeromechanics"（空气力学）原理设计出各种飞行器，让人类能够在"Aerospace"（航空航天）中自由飞翔，并从"Aeroview"（空中视角）欣赏地球的美丽。</p>
        </div>
        
        <div class="canvas-container">
            <canvas id="storyCanvas"></canvas>
        </div>
        
        <div class="controls">
            <button id="playBtn">播放故事</button>
            <button id="resetBtn">重置</button>
        </div>
        
        <div class="word-section">
            <h2>词汇解析</h2>
            <div class="word-list">
                <div class="word-card" data-word="aeroview">
                    <h3>aeroview</h3>
                    <p>空中视角，从高处看到的景象</p>
                    <p><strong>构成</strong>: aero (空气) + view (视图)</p>
                </div>
                <div class="word-card" data-word="aerosphere">
                    <h3>aerosphere</h3>
                    <p>大气层，地球周围的空气层</p>
                    <p><strong>构成</strong>: aero (空气) + sphere (球体)</p>
                </div>
                <div class="word-card" data-word="aerology">
                    <h3>aerology</h3>
                    <p>气象学，研究大气的科学</p>
                    <p><strong>构成</strong>: aero (空气) + logy (学科)</p>
                </div>
                <div class="word-card" data-word="aerial">
                    <h3>aerial</h3>
                    <p>空中的，天线</p>
                    <p><strong>构成</strong>: aeri (空气) + al (形容词后缀)</p>
                </div>
                <div class="word-card" data-word="aerate">
                    <h3>aerate</h3>
                    <p>充气，使通气</p>
                    <p><strong>构成</strong>: aer (空气) + ate (使...)</p>
                </div>
                <div class="word-card" data-word="aeriform">
                    <h3>aeriform</h3>
                    <p>气态的，如气体的</p>
                    <p><strong>构成</strong>: aeri (空气) + form (形态)</p>
                </div>
                <div class="word-card" data-word="aeromechanics">
                    <h3>aeromechanics</h3>
                    <p>空气力学</p>
                    <p><strong>构成</strong>: aero (空气) + mechanics (力学)</p>
                </div>
                <div class="word-card" data-word="aerospace">
                    <h3>aerospace</h3>
                    <p>航空航天，大气层和外层空间</p>
                    <p><strong>构成</strong>: aero (空气) + space (空间)</p>
                </div>
            </div>
        </div>
        
        <div class="word-section">
            <h2>相关词缀</h2>
            <div class="word-list">
                <div class="word-card">
                    <h3>-sphere (球体)</h3>
                    <p>例如：hemisphere (半球)</p>
                    <p>来自希腊语 "sphaira"，表示"球体"</p>
                </div>
                <div class="word-card">
                    <h3>-logy (学科)</h3>
                    <p>例如：mythology (神话学)</p>
                    <p>来自希腊语 "logia"，表示"学说、研究"</p>
                </div>
                <div class="word-card">
                    <h3>-naut (航行者)</h3>
                    <p>例如：aeronaut (飞行员)</p>
                    <p>来自希腊语 "nautes"，表示"航行者"</p>
                </div>
                <div class="word-card">
                    <h3>-drome (跑道)</h3>
                    <p>例如：aerodrome (飞机场)</p>
                    <p>来自希腊语 "dromos"，表示"赛道、跑道"</p>
                </div>
                <div class="word-card">
                    <h3>-phobia (恐惧)</h3>
                    <p>例如：aerophobia (恐高症)</p>
                    <p>来自希腊语 "phobos"，表示"恐惧"</p>
                </div>
                <div class="word-card">
                    <h3>-dynamic (动力的)</h3>
                    <p>例如：aerodynamic (空气动力学的)</p>
                    <p>来自希腊语 "dynamikos"，表示"有力的"</p>
                </div>
            </div>
        </div>
        
        <div class="explanation">
            <h2>学习方法</h2>
            <p>记忆词根是学习英语词汇的有效方法。当你遇到含有"aero"词根的单词时，可以联想到"空气"或"飞行"的概念：</p>
            <ol>
                <li>识别词根：看到aero/aer/aeri时，立即联想到"空气/飞行"</li>
                <li>分析词缀：了解词根后面的后缀或前面的前缀含义</li>
                <li>组合理解：将词根和词缀的含义组合起来理解整个单词</li>
                <li>联想记忆：通过我们的故事场景，将抽象概念具象化</li>
            </ol>
        </div>
    </div>
    
    <footer>
        <p>词根学习动画 | 通过故事理解词根词缀</p>
    </footer>

    <script>
        // 获取Canvas元素和上下文
        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置Canvas的实际尺寸
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
        }
        
        // 页面加载和调整大小时重设Canvas尺寸
        window.addEventListener('load', resizeCanvas);
        window.addEventListener('resize', resizeCanvas);
        
        // 定义故事场景中的对象
        let clouds = [];
        let birds = [];
        let airplane = {
            x: -100,
            y: 200,
            speed: 2,
            size: 30,
            draw: function() {
                ctx.fillStyle = '#3498db';
                ctx.beginPath();
                ctx.moveTo(this.x, this.y);
                ctx.lineTo(this.x + this.size, this.y + this.size/2);
                ctx.lineTo(this.x, this.y + this.size);
                ctx.closePath();
                ctx.fill();
                
                // 机翼
                ctx.beginPath();
                ctx.moveTo(this.x - this.size/3, this.y + this.size/2);
                ctx.lineTo(this.x + this.size/2, this.y + this.size/2);
                ctx.lineTo(this.x + this.size/3, this.y + this.size/1.2);
                ctx.lineTo(this.x - this.size/3, this.y + this.size/1.2);
                ctx.closePath();
                ctx.fill();
            },
            update: function() {
                this.x += this.speed;
                if (this.x > canvas.width + 100) {
                    this.x = -100;
                }
            }
        };
        
        // 创建云朵
        function createClouds() {
            clouds = [];
            for (let i = 0; i < 5; i++) {
                clouds.push({
                    x: Math.random() * canvas.width,
                    y: 50 + Math.random() * 100,
                    radius: 20 + Math.random() * 30,
                    speed: 0.2 + Math.random() * 0.5,
                    word: getRandomWord(),
                    alpha: 0.7 + Math.random() * 0.3
                });
            }
        }
        
        // 创建鸟
        function createBirds() {
            birds = [];
            for (let i = 0; i < 3; i++) {
                birds.push({
                    x: Math.random() * canvas.width,
                    y: 150 + Math.random() * 100,
                    size: 5 + Math.random() * 10,
                    speed: 1 + Math.random() * 2,
                    wingUp: false,
                    wingCounter: 0
                });
            }
        }
        
        // 获取随机单词
        function getRandomWord() {
            const words = ['aeroview', 'aerosphere', 'aerology', 'aerial', 'aerate', 'aeriform', 'aeromechanics', 'aerospace'];
            return words[Math.floor(Math.random() * words.length)];
        }
        
        // 绘制背景
        function drawBackground() {
            // 创建天空渐变
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#b3e0ff');
            gradient.addColorStop(1, '#e6f7ff');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制地面
            ctx.fillStyle = '#a8e6cf';
            ctx.fillRect(0, canvas.height - 50, canvas.width, 50);
        }
        
        // 绘制云朵
        function drawClouds() {
            clouds.forEach(cloud => {
                ctx.fillStyle = `rgba(255, 255, 255, ${cloud.alpha})`;
                ctx.beginPath();
                ctx.arc(cloud.x, cloud.y, cloud.radius, 0, Math.PI * 2);
                ctx.arc(cloud.x + cloud.radius * 0.5, cloud.y - cloud.radius * 0.4, cloud.radius * 0.7, 0, Math.PI * 2);
                ctx.arc(cloud.x + cloud.radius * 1.1, cloud.y, cloud.radius * 0.8, 0, Math.PI * 2);
                ctx.closePath();
                ctx.fill();
                
                // 绘制单词
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(cloud.word, cloud.x, cloud.y);
            });
        }
        
        // 绘制鸟
        function drawBirds() {
            birds.forEach(bird => {
                ctx.fillStyle = '#333';
                
                // 鸟的身体
                ctx.beginPath();
                ctx.ellipse(bird.x, bird.y, bird.size, bird.size/2, 0, 0, Math.PI * 2);
                ctx.fill();
                
                // 鸟的翅膀
                ctx.beginPath();
                if (bird.wingUp) {
                    ctx.moveTo(bird.x, bird.y);
                    ctx.lineTo(bird.x - bird.size, bird.y - bird.size);
                    ctx.lineTo(bird.x + bird.size, bird.y - bird.size);
                } else {
                    ctx.moveTo(bird.x, bird.y);
                    ctx.lineTo(bird.x - bird.size, bird.y + bird.size/2);
                    ctx.lineTo(bird.x + bird.size, bird.y + bird.size/2);
                }
                ctx.closePath();
                ctx.fill();
                
                // 每10帧切换翅膀状态
                bird.wingCounter++;
                if (bird.wingCounter > 10) {
                    bird.wingUp = !bird.wingUp;
                    bird.wingCounter = 0;
                }
            });
        }
        
        // 更新场景
        function updateScene() {
            // 更新云朵位置
            clouds.forEach(cloud => {
                cloud.x += cloud.speed;
                if (cloud.x > canvas.width + cloud.radius * 2) {
                    cloud.x = -cloud.radius * 2;
                    cloud.y = 50 + Math.random() * 100;
                    cloud.word = getRandomWord();
                }
            });
            
            // 更新鸟的位置
            birds.forEach(bird => {
                bird.x += bird.speed;
                if (bird.x > canvas.width + bird.size) {
                    bird.x = -bird.size;
                    bird.y = 150 + Math.random() * 100;
                }
            });
            
            // 更新飞机位置
            airplane.update();
        }
        
        // 绘制场景
        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            drawBackground();
            drawClouds();
            drawBirds();
            airplane.draw();
            
            // 绘制标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Aero - 空气/飞行的世界', canvas.width/2, 40);
        }
        
        // 动画循环
        let animationId;
        function animate() {
            updateScene();
            drawScene();
            animationId = requestAnimationFrame(animate);
        }
        
        // 初始化场景
        function initScene() {
            resizeCanvas();
            createClouds();
            createBirds();
            drawScene();
        }
        
        // 播放按钮事件
        document.getElementById('playBtn').addEventListener('click', function() {
            // 如果动画已经在运行，先取消
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
                this.textContent = '播放故事';
            } else {
                animate();
                this.textContent = '暂停';
            }
        });
        
        // 重置按钮事件
        document.getElementById('resetBtn').addEventListener('click', function() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
                document.getElementById('playBtn').textContent = '播放故事';
            }
            airplane.x = -100;
            initScene();
        });
        
        // 单词卡片点击事件
        document.querySelectorAll('.word-card').forEach(card => {
            card.addEventListener('click', function() {
                const word = this.dataset.word;
                if (word) {
                    // 高亮显示相应的云朵
                    clouds.forEach(cloud => {
                        if (cloud.word === word) {
                            // 创建一个临时的放大效果
                            const originalRadius = cloud.radius;
                            const originalAlpha = cloud.alpha;
                            
                            cloud.radius *= 1.5;
                            cloud.alpha = 1;
                            
                            // 绘制一次场景
                            drawScene();
                            
                            // 恢复原始大小
                            setTimeout(() => {
                                cloud.radius = originalRadius;
                                cloud.alpha = originalAlpha;
                                drawScene();
                            }, 1000);
                        }
                    });
                }
            });
        });
        
        // 页面加载完成后初始化场景
        window.addEventListener('load', initScene);
    </script>
</body>
</html> 