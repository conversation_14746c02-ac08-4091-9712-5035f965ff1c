<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画: Convene</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');

        :root {
            --primary-color: #4a90e2;
            --secondary-color: #f5a623;
            --background-color: #f8f9fa;
            --text-color: #333;
            --card-bg: #ffffff;
            --shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.8;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background: var(--card-bg);
            border-radius: 20px;
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        header {
            background-color: var(--primary-color);
            color: white;
            padding: 30px;
            text-align: center;
        }

        header h1 {
            margin: 0;
            font-size: 3em;
            font-weight: 700;
            letter-spacing: 2px;
        }

        header p {
            margin: 5px 0 0;
            font-size: 1.5em;
            opacity: 0.9;
        }

        main {
            display: flex;
            flex-wrap: wrap;
            padding: 30px;
        }

        .explanation, .animation-container {
            flex: 1;
            padding: 20px;
            min-width: 300px;
        }

        .explanation h2 {
            color: var(--primary-color);
            font-size: 2em;
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 10px;
            margin-top: 0;
        }
        
        .explanation p, .explanation li {
            font-size: 1.1em;
        }
        
        .morpheme {
            background-color: #eaf2fb;
            border-left: 5px solid var(--secondary-color);
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
        }

        .morpheme strong {
            color: var(--secondary-color);
            font-size: 1.2em;
        }

        .example {
            font-style: italic;
            background-color: #fef8e1;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .example p {
           margin: 0;
        }


        .animation-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        canvas {
            border: 1px solid #ddd;
            border-radius: 15px;
            background-color: #fff;
        }

        button {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        button:hover {
            background-color: #d88c0a;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>

    <div class="container">
        <header>
            <h1>convene</h1>
            <p>/kənˈviːn/</p>
        </header>
        <main>
            <div class="explanation">
                <h2>单词拆解教学</h2>
                <p><strong>Convene</strong> 是一个动词，意思是"召集"或"集合"，通常指为了某个正式的会议或活动而聚在一起。</p>
                
                <div class="morpheme">
                    <strong>前缀: con-</strong>
                    <p>表示"共同, 一起"。想象一下两个人手拉手，这就是 `con-` 的感觉。</p>
                </div>

                <div class="morpheme">
                    <strong>词根: -vene</strong>
                    <p>来自拉丁语 `venire`，意思是"来"。你可以把它想象成一个指向你的箭头。</p>
                </div>

                <p>所以，<strong>con + vene</strong> 的字面意思就是"<strong>一起来</strong>"，这自然就引申为"召集"或"开会"。</p>

                <div class="example">
                    <p><strong>例句:</strong></p>
                    <p>The committee will <strong>convene</strong> at 11:30 a.m. tomorrow.</p>
                    <p><em>委员会将于明天上午11:30召开会议。</em></p>
                </div>
            </div>
            <div class="animation-container">
                <canvas id="wordAnimation" width="500" height="400"></canvas>
                <button id="playButton">开始动画</button>
            </div>
        </main>
    </div>

    <script>
        const canvas = document.getElementById('wordAnimation');
        const ctx = canvas.getContext('2d');
        const playButton = document.getElementById('playButton');

        const width = canvas.width;
        const height = canvas.height;
        const centerX = width / 2;
        const centerY = height / 2;
        
        let animationFrameId;
        let stage = 0; // 0: initial, 1: con-, 2: vene, 3: convene, 4: final
        let particles = [];
        const numParticles = 15;
        let textAlpha = 0;
        let stageTime = 0;

        class Particle {
            constructor() {
                this.reset();
                this.x = Math.random() * width;
                this.y = Math.random() * height;
            }

            reset() {
                this.sx = Math.random() * width;
                this.sy = Math.random() * height;
                this.x = this.sx;
                this.y = this.sy;
                const angle = Math.random() * Math.PI * 2;
                const radius = Math.random() * 50 + 80;
                this.tx = centerX + Math.cos(angle) * radius;
                this.ty = centerY + Math.sin(angle) * radius;
                this.speed = Math.random() * 0.03 + 0.02;
                this.color = `hsl(${Math.random() * 60 + 200}, 80%, 60%)`;
            }

            draw() {
                ctx.beginPath();
                ctx.arc(this.x, this.y, 5, 0, Math.PI * 2);
                ctx.fillStyle = this.color;
                ctx.fill();
            }

            update() {
                if (stage >= 3) {
                    this.x += (this.tx - this.x) * this.speed;
                    this.y += (this.ty - this.y) * this.speed;
                }
            }
        }

        function init() {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            stage = 0;
            stageTime = 0;
            particles = [];
            for (let i = 0; i < numParticles; i++) {
                particles.push(new Particle());
            }
            draw();
            playButton.textContent = "开始动画";
            playButton.onclick = startAnimation;
        }

        function drawText(text, yOffset, alpha, size = 30) {
            ctx.fillStyle = `rgba(51, 51, 51, ${alpha})`;
            ctx.font = `bold ${size}px 'Noto Sans SC', sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, centerX, yOffset);
        }
        
        function drawStoryText(text) {
             ctx.fillStyle = `rgba(100, 100, 100, 1)`;
             ctx.font = `16px 'Noto Sans SC', sans-serif`;
             ctx.textAlign = 'center';
             ctx.textBaseline = 'bottom';
             ctx.fillText(text, centerX, height - 10);
        }

        function draw() {
            ctx.clearRect(0, 0, width, height);
            
            particles.forEach(p => {
                p.draw();
            });

            drawStoryText("想象一下，许多人（小球）分散在各处。");
        }

        function animate() {
            ctx.clearRect(0, 0, width, height);
            
            particles.forEach(p => {
                p.update();
                p.draw();
            });

            stageTime++;
            
            // Animation Logic
            if (stage === 1) { // con-
                if (textAlpha < 1) textAlpha += 0.02;
                drawText("con-", centerY, textAlpha, 60);
                drawStoryText("前缀 'con-' 出现，意思是 '共同'。");
                if (stageTime > 120) { stageTime = 0; stage++; textAlpha = 0; }
            } else if (stage === 2) { // vene
                drawText("con-", centerY, 1, 60);
                if (textAlpha < 1) textAlpha += 0.02;
                ctx.fillStyle = `rgba(245, 166, 35, ${textAlpha})`;
                ctx.font = `bold 60px 'Noto Sans SC', sans-serif`;
                ctx.textAlign = 'left';
                ctx.fillText("vene", centerX + 25, centerY);
                drawStoryText("词根 '-vene' 出现，意思是 '来'。");

                if (stageTime > 120) { stageTime = 0; stage++; textAlpha = 0; }
            } else if (stage === 3) { // convene
                ctx.font = `bold 60px 'Noto Sans SC', sans-serif`;
                ctx.textAlign = 'center';
                ctx.fillStyle = `rgba(51, 51, 51, 1)`;
                ctx.fillText("convene", centerX, centerY);
                drawStoryText("当它们组合在一起，人们便'一起来' -> '集合'。");

                if (stageTime > 240) { stageTime = 0; stage++; }
            } else if (stage === 4) { // final
                 ctx.font = `bold 60px 'Noto Sans SC', sans-serif`;
                ctx.textAlign = 'center';
                ctx.fillStyle = `rgba(51, 51, 51, 1)`;
                ctx.fillText("convene", centerX, centerY);
                drawStoryText("'Convene' 就是召集、集合。点击按钮可重播。");
                playButton.textContent = "重新播放";
                playButton.onclick = init;
                cancelAnimationFrame(animationFrameId);
                return;
            }


            animationFrameId = requestAnimationFrame(animate);
        }
        
        function startAnimation() {
            if (stage > 0) return;
            stage = 1;
            stageTime = 0;
            textAlpha = 0;
            particles.forEach(p => p.reset());
            animate();
        }

        init();
    </script>
</body>
</html> 