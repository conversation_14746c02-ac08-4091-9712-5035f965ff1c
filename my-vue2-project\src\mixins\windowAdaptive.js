// 窗口自适应混入
export default {
  data() {
    return {
      windowInfo: {
        width: window.innerWidth,
        height: window.innerHeight,
        isMaximized: false,
        isFullScreen: false
      },
      isElectron: false
    }
  },
  
  computed: {
    // 根据窗口宽度判断设备类型
    deviceType() {
      const width = this.windowInfo.width
      if (width < 768) return 'mobile'
      if (width < 1024) return 'tablet'
      if (width < 1440) return 'desktop'
      return 'large-desktop'
    },
    
    // 是否为小屏设备
    isMobile() {
      return this.deviceType === 'mobile'
    },
    
    // 是否为平板设备
    isTablet() {
      return this.deviceType === 'tablet'
    },
    
    // 是否为桌面设备
    isDesktop() {
      return this.deviceType === 'desktop' || this.deviceType === 'large-desktop'
    },
    
    // 动态计算容器样式
    containerStyle() {
      const { width, height } = this.windowInfo
      return {
        '--window-width': `${width}px`,
        '--window-height': `${height}px`,
        '--container-padding': this.isMobile ? '10px' : this.isTablet ? '20px' : '30px',
        '--card-columns': this.isMobile ? '1' : this.isTablet ? '2' : '3',
        '--font-size-base': this.isMobile ? '14px' : '16px',
        '--header-height': this.isMobile ? '50px' : '60px'
      }
    }
  },
  
  mounted() {
    this.initWindowAdaptive()
  },
  
  beforeDestroy() {
    this.cleanupWindowAdaptive()
  },
  
  methods: {
    // 初始化窗口自适应
    initWindowAdaptive() {
      // 检测是否在Electron环境中
      this.isElectron = window.require !== undefined
      
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleWindowResize)
      
      // 如果在Electron环境中，监听Electron事件
      if (this.isElectron) {
        const { ipcRenderer } = window.require('electron')
        
        // 监听窗口信息
        ipcRenderer.on('window-info', (event, info) => {
          this.windowInfo = { ...this.windowInfo, ...info }
        })
        
        // 监听窗口大小变化
        ipcRenderer.on('window-resize', (event, size) => {
          this.windowInfo.width = size.width
          this.windowInfo.height = size.height
          this.onWindowResize(size)
        })
        
        // 监听窗口最大化状态
        ipcRenderer.on('window-maximize', (event, isMaximized) => {
          this.windowInfo.isMaximized = isMaximized
          this.onWindowMaximize(isMaximized)
        })
        
        // 监听全屏状态
        ipcRenderer.on('window-fullscreen', (event, isFullScreen) => {
          this.windowInfo.isFullScreen = isFullScreen
          this.onWindowFullScreen(isFullScreen)
        })
        
        // 监听菜单操作
        ipcRenderer.on('menu-action', (event, action) => {
          this.onMenuAction(action)
        })
      }
      
      // 初始化窗口信息
      this.updateWindowInfo()
    },
    
    // 清理窗口自适应
    cleanupWindowAdaptive() {
      window.removeEventListener('resize', this.handleWindowResize)
      
      if (this.isElectron) {
        const { ipcRenderer } = window.require('electron')
        ipcRenderer.removeAllListeners('window-info')
        ipcRenderer.removeAllListeners('window-resize')
        ipcRenderer.removeAllListeners('window-maximize')
        ipcRenderer.removeAllListeners('window-fullscreen')
        ipcRenderer.removeAllListeners('menu-action')
      }
    },
    
    // 处理窗口大小变化
    handleWindowResize() {
      this.updateWindowInfo()
      this.onWindowResize({
        width: window.innerWidth,
        height: window.innerHeight
      })
    },
    
    // 更新窗口信息
    updateWindowInfo() {
      this.windowInfo.width = window.innerWidth
      this.windowInfo.height = window.innerHeight
    },
    
    // 窗口大小变化回调（可在组件中重写）
    onWindowResize(size) {
      // 子组件可以重写此方法来处理窗口大小变化
      console.log('Window resized:', size)
    },
    
    // 窗口最大化状态变化回调（可在组件中重写）
    onWindowMaximize(isMaximized) {
      // 子组件可以重写此方法来处理窗口最大化状态变化
      console.log('Window maximized:', isMaximized)
    },
    
    // 全屏状态变化回调（可在组件中重写）
    onWindowFullScreen(isFullScreen) {
      // 子组件可以重写此方法来处理全屏状态变化
      console.log('Window fullscreen:', isFullScreen)
    },
    
    // 菜单操作回调（可在组件中重写）
    onMenuAction(action) {
      // 子组件可以重写此方法来处理菜单操作
      console.log('Menu action:', action)
    },
    
    // 获取响应式类名
    getResponsiveClass() {
      return [
        `device-${this.deviceType}`,
        this.windowInfo.isMaximized ? 'window-maximized' : '',
        this.windowInfo.isFullScreen ? 'window-fullscreen' : ''
      ].filter(Boolean).join(' ')
    }
  }
}
