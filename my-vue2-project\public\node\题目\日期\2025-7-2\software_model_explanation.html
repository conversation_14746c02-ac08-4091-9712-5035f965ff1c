<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件开发模型交互式解释</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background-color: #f0f4f8;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 25px;
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #0056b3;
            text-align: center;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .question-box {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 1.1em;
            line-height: 1.6;
        }
        .question-box strong {
            color: #d9534f;
        }
        canvas {
            background-color: #ffffff;
            border: 1px solid #ccc;
            border-radius: 8px;
            display: block;
            margin: 0 auto;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 12px 25px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin: 0 10px;
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #a0c7e4;
            cursor: not-allowed;
        }
        #explanationText {
            background-color: #e7f3ff;
            border-left: 5px solid #007bff;
            padding: 15px 20px;
            margin-top: 15px;
            border-radius: 5px;
            min-height: 50px;
            text-align: center;
            font-size: 1.1em;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>问题来了</h1>
        <div class="question-box">
            <p>某公司要开发一个软件产品，产品的某些需求是明确的，而某些需求则需要进一步细化。由于市场竞争的压力，产品需要尽快上市，则开发该软件产品<strong>最不适合</strong>采用 ( ) 模型。</p>
            <p>A. 瀑布&nbsp;&nbsp;&nbsp;&nbsp;B. 原型&nbsp;&nbsp;&nbsp;&nbsp;C. 增量&nbsp;&nbsp;&nbsp;&nbsp;D. 螺旋</p>
            <p><strong>正确答案：A</strong></p>
        </div>
    </div>

    <div class="container">
        <h1>交互式动画讲解 (建房子比喻)</h1>
        <canvas id="animationCanvas" width="800" height="400"></canvas>
        <div class="controls">
            <button id="stepBtn">开始讲解</button>
            <button id="resetBtn">重新开始</button>
        </div>
        <div id="explanationText">
            <p>准备好了吗？点击“开始讲解”，让我们用建房子的方式理解软件开发吧！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        const stepBtn = document.getElementById('stepBtn');
        const resetBtn = document.getElementById('resetBtn');
        const explanationDiv = document.getElementById('explanationText');

        let step = 0;

        const house = {
            baseX: 350,
            baseY: 350,
            width: 150,
            height: 100,
            roofHeight: 50,
            doorWidth: 30,
            doorHeight: 60
        };

        const person = {
            x: 100,
            y: 350
        };

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawGround() {
            ctx.strokeStyle = '#6B4226';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, 350);
            ctx.lineTo(canvas.width, 350);
            ctx.stroke();
        }

        function drawPerson(x, y, text = null) {
            // Body
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x, y - 40);
            ctx.stroke();
            // Head
            ctx.beginPath();
            ctx.arc(x, y - 50, 10, 0, Math.PI * 2);
            ctx.fillStyle = '#f0db4f';
            ctx.fill();
            ctx.stroke();
            // Arms
            ctx.beginPath();
            ctx.moveTo(x, y - 35);
            ctx.lineTo(x - 15, y - 20);
            ctx.moveTo(x, y - 35);
            ctx.lineTo(x + 15, y - 20);
            ctx.stroke();
            if (text) {
                ctx.font = '14px sans-serif';
                ctx.fillStyle = 'black';
                ctx.textAlign = 'center';
                ctx.fillText(text, x, y - 70);
            }
        }
        
        function drawBlueprint(x, y) {
            ctx.fillStyle = 'white';
            ctx.fillRect(x, y, 80, 50);
            ctx.strokeStyle = 'blue';
            ctx.strokeRect(x, y, 80, 50);
            ctx.beginPath();
            ctx.moveTo(x+5, y+10); ctx.lineTo(x+75, y+10);
            ctx.moveTo(x+5, y+20); ctx.lineTo(x+75, y+20);
            ctx.moveTo(x+5, y+30); ctx.lineTo(x+35, y+30);
            ctx.moveTo(x+5, y+40); ctx.lineTo(x+75, y+40);
            ctx.stroke();
            ctx.font = '12px sans-serif';
            ctx.fillStyle = 'blue';
            ctx.textAlign = 'center';
            ctx.fillText('完美设计图', x + 40, y - 5);
        }

        function drawHousePart(part) {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            
            if (part === 'foundation') {
                ctx.strokeRect(house.baseX - 5, house.baseY - 10, house.width + 10, 15);
            }
            if (part === 'walls') {
                ctx.strokeRect(house.baseX, house.baseY - house.height, house.width, house.height);
                ctx.strokeRect(house.baseX + (house.width - house.doorWidth)/2, house.baseY - house.doorHeight, house.doorWidth, house.doorHeight);
            }
            if (part === 'roof') {
                ctx.beginPath();
                ctx.moveTo(house.baseX - 10, house.baseY - house.height);
                ctx.lineTo(house.baseX + house.width / 2, house.baseY - house.height - house.roofHeight);
                ctx.lineTo(house.baseX + house.width + 10, house.baseY - house.height);
                ctx.closePath();
                ctx.stroke();
            }
        }

        function drawWreckingBall(x, y) {
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + 50, y - 50);
            ctx.stroke();
            ctx.beginPath();
            ctx.arc(x + 50, y - 50, 20, 0, Math.PI * 2);
            ctx.fillStyle = 'gray';
            ctx.fill();
        }

        function drawSimpleHouseSketch(x, y, text) {
             ctx.strokeStyle = '#333';
             ctx.strokeRect(x, y, 100, 70);
             ctx.beginPath();
             ctx.moveTo(x, y);
             ctx.lineTo(x+50, y-30);
             ctx.lineTo(x+100, y);
             ctx.stroke();
             ctx.font = '14px sans-serif';
             ctx.fillStyle = 'black';
             ctx.textAlign = 'center';
             ctx.fillText(text, x+50, y - 40);
        }

        function updateExplanation(text) {
            explanationDiv.innerHTML = `<p>${text}</p>`;
        }

        const animationSteps = [
            // Step 0: Initial state
            () => {
                clearCanvas();
                drawGround();
                updateExplanation('我们先看看题目中最不推荐的“瀑布模型”为什么不行。');
                stepBtn.textContent = "下一步";
            },
            // Step 1: Waterfall - Requirements
            () => {
                clearCanvas();
                drawGround();
                drawPerson(person.x, person.y, '客户');
                drawBlueprint(200, 280);
                updateExplanation('<strong>瀑布模型第一步：需求分析。</strong>就像建房前必须有份100%完美的最终设计图，不允许任何修改。');
            },
            // Step 2: Waterfall - Building
            () => {
                clearCanvas();
                drawGround();
                drawPerson(person.x, person.y, '客户');
                drawHousePart('foundation');
                drawHousePart('walls');
                drawHousePart('roof');
                updateExplanation('<strong>瀑布模型接着严格按图施工：</strong>打地基 → 砌墙 → 封顶。一步接一步，过程无法回头。');
            },
            // Step 3: Waterfall - Problem
            () => {
                clearCanvas();
                drawGround();
                drawHousePart('foundation');
                drawHousePart('walls');
                drawHousePart('roof');
                drawPerson(person.x, person.y, '等等！我想要个阳台！');
                updateExplanation('<strong>房子建好了，客户突然想改需求。</strong>但在瀑布模型里，这几乎是不可能的！');
            },
            // Step 4: Waterfall - Consequence
            () => {
                drawWreckingBall(600, 100);
                ctx.font = 'bold 30px sans-serif';
                ctx.fillStyle = 'red';
                ctx.fillText('改动 = 推倒重来！成本极高！', 400, 150);
                updateExplanation('<strong>结论：</strong>瀑布模型太死板，不适合需求会变动、或一开始就不明确的项目。<strong>所以它是“最不适合”的。</strong>');
            },
            // Step 5: Iterative Intro
            () => {
                clearCanvas();
                drawGround();
                updateExplanation('那该怎么办呢？来看看更灵活的模型，比如“原型”或“增量”模型。');
            },
             // Step 6: Prototype
            () => {
                clearCanvas();
                drawGround();
                drawPerson(person.x, person.y, '我想要个房子...');
                drawPerson(600, 350, '开发');
                drawSimpleHouseSketch(350, 280, '是这样吗？(原型)');
                updateExplanation('<strong>原型模型：</strong>不急着施工，先画个草图（原型）给客户确认。方便快速沟通和修改。');
            },
            // Step 7: Incremental
            () => {
                clearCanvas();
                drawGround();
                drawPerson(person.x, person.y, '客户');
                // Draw part of the house
                ctx.save();
                ctx.globalAlpha = 0.5;
                drawHousePart('walls');
                drawHousePart('roof');
                ctx.restore();
                drawHousePart('foundation');
                
                ctx.font = '14px sans-serif';
                ctx.fillStyle = 'black';
                ctx.textAlign = 'center';
                ctx.fillText('第一版 (核心功能)', house.baseX + house.width/2, house.baseY + 20);
                updateExplanation('<strong>增量模型：</strong>先把最核心的部分建好（比如地基和框架），让产品能尽快上线。这就像先建好毛坯房，就能用了。');
            },
            // Step 8: Incremental 2
            () => {
                drawHousePart('walls');
                drawHousePart('roof');
                 ctx.font = '14px sans-serif';
                ctx.fillStyle = 'black';
                ctx.textAlign = 'center';
                ctx.fillText('第二版 (增加功能)', house.baseX + house.width/2, house.baseY + 20);
                updateExplanation('然后根据市场反馈，再逐步添加新功能（装修、加盖屋顶）。产品在不断迭代中完善。');
            },
            // Step 9: Final Conclusion
            () => {
                clearCanvas();
                ctx.font = 'bold 20px sans-serif';
                ctx.fillStyle = '#0056b3';
                ctx.textAlign = 'center';
                ctx.fillText('总结', canvas.width / 2, 100);
                ctx.font = '16px sans-serif';
                ctx.fillStyle = 'black';
                ctx.fillText('题目中“需求需细化”意味着瀑布模型的“完美图纸”行不通。', canvas.width / 2, 150);
                ctx.fillText('“尽快上市”要求我们使用原型、增量等灵活快速的模型。', canvas.width / 2, 180);
                ctx.fillText('因此，僵化的瀑布模型是最不适合的选择。', canvas.width / 2, 210);
                updateExplanation('现在您理解了吗？原型、增量、螺旋等模型都更灵活，适合应对需求变化和快速开发。');
                stepBtn.disabled = true;
                stepBtn.textContent = "讲解结束";
            }
        ];

        function runAnimationStep() {
            if (step < animationSteps.length) {
                animationSteps[step]();
                step++;
            }
        }
        
        function resetAnimation() {
            step = 0;
            clearCanvas();
            drawGround();
            updateExplanation('准备好了吗？点击“开始讲解”，让我们用建房子的方式理解软件开发吧！');
            stepBtn.disabled = false;
            stepBtn.textContent = "开始讲解";
        }
        
        stepBtn.addEventListener('click', runAnimationStep);
        resetBtn.addEventListener('click', resetAnimation);

        // Initial paint
        drawGround();

    </script>
</body>
</html> 