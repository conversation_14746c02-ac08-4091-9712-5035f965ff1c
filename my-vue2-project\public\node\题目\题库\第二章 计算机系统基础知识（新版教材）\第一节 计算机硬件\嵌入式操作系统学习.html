<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式操作系统特点学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            padding: 20px;
            border: 2px solid #e0e6ed;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            text-align: center;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .option.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .option.correct {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        .option.wrong {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        canvas {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            background: #f8f9ff;
        }

        .explanation-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
            opacity: 0;
            transform: translateY(30px);
        }

        .explanation-card.show {
            opacity: 1;
            transform: translateY(0);
            transition: all 0.8s ease-out;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .feature-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transform: scale(0.9);
            opacity: 0;
            transition: all 0.5s ease;
        }

        .feature-item.animate {
            transform: scale(1);
            opacity: 1;
        }

        .feature-item h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .feature-item p {
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 嵌入式操作系统特点学习</h1>
            <p class="subtitle">通过动画和交互理解嵌入式系统的核心特征</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>"从减少成本和缩短研发周期考虑，要求嵌入式操作系统能运行在不同的微处理器平台上。能针对硬件变化进行结构与功能上的配置"是属于嵌入式操作系统（ ）特点。
            </div>
            
            <div class="options-container">
                <div class="option" data-answer="A">
                    <strong>A. 可定制</strong>
                </div>
                <div class="option" data-answer="B">
                    <strong>B. 实时性</strong>
                </div>
                <div class="option" data-answer="C">
                    <strong>C. 可靠性</strong>
                </div>
                <div class="option" data-answer="D">
                    <strong>D. 易移植性</strong>
                </div>
            </div>
            
            <div style="text-align: center;">
                <button class="btn" onclick="checkAnswer()">提交答案</button>
                <button class="btn" onclick="showExplanation()">查看解析</button>
                <button class="btn" onclick="startAnimation()">开始动画演示</button>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="animationCanvas"></canvas>
        </div>

        <div class="explanation-card" id="explanationCard">
            <h2 style="color: #667eea; margin-bottom: 25px;">📚 知识解析</h2>
            
            <div class="highlight">
                <strong>正确答案：A. 可定制</strong><br>
                题目中的关键词："减少成本"、"缩短研发周期"、"运行在不同微处理器平台"、"针对硬件变化进行配置"
            </div>

            <h3 style="color: #333; margin: 25px 0 15px 0;">🎯 做题思路</h3>
            <p style="line-height: 1.8; color: #555;">
                1. <strong>关键词识别</strong>：题目强调"不同平台"和"配置"<br>
                2. <strong>概念匹配</strong>：可定制性就是指能够根据不同需求进行配置<br>
                3. <strong>排除干扰</strong>：实时性、可靠性、易移植性都不是重点
            </p>

            <h3 style="color: #333; margin: 25px 0 15px 0;">🔍 嵌入式操作系统五大特点</h3>
            <div class="feature-grid" id="featureGrid">
                <div class="feature-item">
                    <h3>🔧 可定制</h3>
                    <p>能运行在不同微处理器平台上，针对硬件变化进行结构与功能配置，满足不同应用需求</p>
                </div>
                <div class="feature-item">
                    <h3>📦 微型化</h3>
                    <p>占用资源少，系统代码量小，适合资源受限的嵌入式设备</p>
                </div>
                <div class="feature-item">
                    <h3>⚡ 实时性</h3>
                    <p>应用于过程控制、数据采集等需要迅速响应的场合，对实时性要求高</p>
                </div>
                <div class="feature-item">
                    <h3>🛡️ 可靠性</h3>
                    <p>系统构件、模块必须达到应有的可靠性，关键应用需要容错和防故障措施</p>
                </div>
                <div class="feature-item">
                    <h3>🔄 易移植性</h3>
                    <p>采用硬件抽象层和板级支持包技术，提高系统在不同硬件平台间的移植性</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAnswer = null;
        let canvas, ctx;
        let animationId;

        // 初始化画布
        function initCanvas() {
            canvas = document.getElementById('animationCanvas');
            ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = 400;
        }

        // 选择答案
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedAnswer = this.dataset.answer;
            });
        });

        // 检查答案
        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }

            document.querySelectorAll('.option').forEach(option => {
                if (option.dataset.answer === 'A') {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== 'A') {
                    option.classList.add('wrong');
                }
            });

            setTimeout(() => {
                if (selectedAnswer === 'A') {
                    alert('🎉 恭喜答对了！可定制是正确答案。');
                } else {
                    alert('❌ 答错了，正确答案是A. 可定制。请查看解析了解原因。');
                }
            }, 500);
        }

        // 显示解析
        function showExplanation() {
            const explanationCard = document.getElementById('explanationCard');
            explanationCard.classList.add('show');
            
            // 动画显示特点卡片
            setTimeout(() => {
                const featureItems = document.querySelectorAll('.feature-item');
                featureItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('animate');
                    }, index * 200);
                });
            }, 500);
        }

        // 动画演示
        function startAnimation() {
            if (!ctx) initCanvas();
            
            let frame = 0;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制中心的嵌入式系统
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(centerX, centerY, 60 + Math.sin(frame * 0.1) * 5, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('嵌入式', centerX, centerY - 5);
                ctx.fillText('操作系统', centerX, centerY + 15);

                // 绘制五个特点围绕中心旋转
                const features = ['可定制', '微型化', '实时性', '可靠性', '易移植性'];
                const colors = ['#4CAF50', '#FF9800', '#2196F3', '#9C27B0', '#F44336'];
                
                features.forEach((feature, index) => {
                    const angle = (frame * 0.02) + (index * Math.PI * 2 / 5);
                    const radius = 120;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    // 突出显示可定制
                    const size = feature === '可定制' ? 35 + Math.sin(frame * 0.2) * 8 : 25;
                    
                    ctx.fillStyle = colors[index];
                    ctx.beginPath();
                    ctx.arc(x, y, size, 0, Math.PI * 2);
                    ctx.fill();
                    
                    ctx.fillStyle = 'white';
                    ctx.font = feature === '可定制' ? 'bold 14px Microsoft YaHei' : '12px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(feature, x, y + 4);
                    
                    // 绘制连接线
                    ctx.strokeStyle = 'rgba(102, 126, 234, 0.3)';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(x, y);
                    ctx.stroke();
                });

                // 绘制题目关键词
                if (frame > 100) {
                    const keywords = ['不同平台', '硬件配置', '成本控制'];
                    keywords.forEach((keyword, index) => {
                        const alpha = Math.min(1, (frame - 100 - index * 30) / 60);
                        if (alpha > 0) {
                            ctx.fillStyle = `rgba(255, 87, 34, ${alpha})`;
                            ctx.font = '14px Microsoft YaHei';
                            ctx.textAlign = 'center';
                            ctx.fillText(keyword, centerX + (index - 1) * 100, 50);
                        }
                    });
                }

                frame++;
                animationId = requestAnimationFrame(animate);
                
                // 5秒后停止动画
                if (frame > 300) {
                    cancelAnimationFrame(animationId);
                }
            }

            animate();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initCanvas();
            
            // 自动开始轻微的动画效果
            setTimeout(() => {
                if (!animationId) {
                    startAnimation();
                }
            }, 2000);
        });

        // 响应式调整
        window.addEventListener('resize', function() {
            if (canvas) {
                canvas.width = canvas.offsetWidth;
                canvas.height = 400;
            }
        });
    </script>
</body>
</html>
