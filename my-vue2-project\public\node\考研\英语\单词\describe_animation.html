<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画：describe</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            font-size: 3em;
            color: #1a73e8;
            margin-bottom: 10px;
        }
        #word-pronunciation {
            font-size: 1.2em;
            color: #5f6368;
            margin-bottom: 20px;
        }
        canvas {
            background: #ffffff;
            border: 1px solid #dcdcdc;
            border-radius: 8px;
            margin-bottom: 20px;
            cursor: pointer;
        }
        .explanation {
            text-align: left;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
        }
        .explanation h2 {
            color: #1a73e8;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 5px;
            margin-top: 0;
        }
        .explanation p {
            font-size: 1.1em;
            line-height: 1.8;
        }
        .explanation .highlight {
            font-weight: bold;
            color: #d93025;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin-top: 20px;
        }
        button:hover {
            background-color: #155ab6;
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        #interactive-instruction {
            color: #5f6368;
            margin-top: 10px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>describe</h1>
        <p id="word-pronunciation">[dɪˈskraɪb] / "迪SKRI伯"</p>
        
        <canvas id="wordCanvas" width="600" height="300"></canvas>
        <p id="interactive-instruction">点击画布或按钮开始动画</p>
        <button id="playButton">播放动画</button>

        <div class="explanation">
            <h2>单词拆解教学 📖</h2>
            <p>
                你好！今天我们来学习一个用来"画"出语言的词：<span class="highlight">describe</span>。
                老规矩，我们把它拆开来看：
            </p>
            <p>
                1. 前缀 <span class="highlight">de-</span>：这个前缀有"<span class="highlight">向下</span>"或者"<span class="highlight">加强语气</span>"的意思。在这里，你可以理解为"<span class="highlight">完全地、彻底地</span>"写下来。
            </p>
            <p>
                2. 词根 <span class="highlight">-scribe-</span>：这是一个非常重要的词根，源自拉丁语，意思是"<span class="highlight">写</span>"。比如 `subscribe` (在下面写 -> 订阅)，`inscribe` (在内部写 -> 铭刻)。
            </p>
            <p>
                所以，组合起来就是：<span class="highlight">de (向下、完全地)</span> + <span class="highlight">scribe (写)</span> = <span class="highlight">describe (写下来)</span>。
                把一件事物、一个人的样貌、一个场景的细节"写下来"，这不就是在做"<span class="highlight">描述</span>"或者"<span class="highlight">描绘</span>"吗？
            </p>
            <h2>翻译与用法 ✍️</h2>
            <p>
                <b>及物动词 (vt.):</b>
                <ul>
                    <li><b>描述，形容:</b> Can you <em>describe</em> the man you saw? (你能描述一下你看到的那个男人吗？)</li>
                    <li><b>描绘，画出(图形):</b> The teacher asked the students to <em>describe</em> a circle. (老师让学生们画一个圆。)</li>
                </ul>
            </p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('wordCanvas');
        const ctx = canvas.getContext('2d');
        const playButton = document.getElementById('playButton');

        let animationState = 'initial';
        let frame = 0;
        const totalFrames = 450;
        
        // Pen object
        const pen = {
            x: 450,
            y: 80,
            angle: -Math.PI / 6,
            draw() {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.angle);
                ctx.fillStyle = '#333';
                ctx.fillRect(-5, -70, 10, 70); // pen body
                ctx.fillStyle = '#f0f0f0';
                ctx.fillRect(-5, -70, 10, 10);
                ctx.beginPath(); // pen tip
                ctx.moveTo(-5, 0);
                ctx.lineTo(5, 0);
                ctx.lineTo(0, 10);
                ctx.closePath();
                ctx.fillStyle = '#666';
                ctx.fill();
                ctx.restore();
            }
        };

        // Paper object
        const paper = {
            x: 100,
            y: 200,
            width: 400,
            height: 80,
            lines: [],
            draw() {
                ctx.fillStyle = 'white';
                ctx.strokeStyle = '#ccc';
                ctx.lineWidth = 1;
                ctx.fillRect(this.x, this.y, this.width, this.height);
                ctx.strokeRect(this.x, this.y, this.width, this.height);
                
                // Draw written lines
                ctx.strokeStyle = '#555';
                ctx.lineWidth = 1.5;
                this.lines.forEach(line => {
                    ctx.beginPath();
                    ctx.moveTo(line.startX, line.startY);
                    line.points.forEach(p => {
                        ctx.lineTo(p.x, p.y);
                    });
                    ctx.stroke();
                });
            }
        };

        function drawInitial() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            paper.lines = [];
            paper.draw();
            pen.y = 80;
            pen.draw();

            ctx.font = '30px Arial';
            ctx.fillStyle = '#555';
            ctx.textAlign = 'center';
            ctx.fillText('写 (scribe)', 450, 180);
        }

        function animate() {
            frame++;
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (animationState === 'initial') {
                drawInitial();
                return;
            }

            paper.draw();

            // Phase 1: Show "de-"
            if (frame > 30) {
                const alpha = Math.min(1, (frame - 30) / 50);
                ctx.font = 'bold 80px Arial';
                ctx.fillStyle = `rgba(217, 48, 37, ${alpha})`;
                ctx.fillText('de-', 150, 120);
                ctx.font = 'bold 30px Arial';
                ctx.fillText('向下', 150, 170);
            }

            // Phase 2: Pen moves down and writes
            if (frame > 100 && frame < 300) {
                const progress = Math.min(1, (frame - 100) / 80);
                pen.y = 80 + progress * 130; // Move pen down
                
                // Start writing after moving down
                if (progress >= 1) {
                    const writeProgress = (frame - 180) / 120;
                    if (writeProgress > 0 && paper.lines.length === 0) {
                        paper.lines.push({ startX: 200, startY: 240, points: [] });
                    }
                    if (writeProgress > 0.5 && paper.lines.length === 1) {
                        paper.lines.push({ startX: 200, startY: 260, points: [] });
                    }

                    paper.lines.forEach((line, index) => {
                        let currentProgress = writeProgress - (index * 0.5);
                        if (currentProgress > 0) {
                            let pCount = Math.floor(currentProgress * 20);
                            if (pCount > line.points.length) {
                                let lastPoint = line.points[line.points.length - 1] || {x: line.startX, y: line.startY};
                                line.points.push({
                                    x: lastPoint.x + 10,
                                    y: line.startY + Math.sin(pCount) * 3
                                });
                            }
                        }
                    });
                }
            } else if (frame >= 300) {
                 pen.y = 210;
            }
            
            pen.draw();
            ctx.font = '30px Arial';
            ctx.fillStyle = '#555';
            ctx.textAlign = 'center';
            ctx.fillText('写 (scribe)', 450, 180);

            // Phase 3: Show final word
            if (frame > 320) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const alpha = Math.min(1, (frame - 320) / 80);
                ctx.font = 'bold 90px Arial';
                ctx.fillStyle = `rgba(26, 115, 232, ${alpha})`;
                ctx.textAlign = 'center';
                ctx.fillText('describe', canvas.width / 2, 150);
                
                ctx.font = '40px Arial';
                ctx.fillStyle = `rgba(51, 51, 51, ${alpha})`;
                ctx.fillText('写下来 = 描述 / 形容', canvas.width / 2, 230);
            }


            if (frame < totalFrames) {
                requestAnimationFrame(animate);
            } else {
                animationState = 'finished';
                playButton.textContent = '重新播放';
            }
        }

        function startAnimation() {
            if (animationState === 'finished' || animationState === 'initial') {
                frame = 0;
                animationState = 'playing';
                playButton.textContent = '播放中...';
                drawInitial();
                requestAnimationFrame(animate);
            }
        }

        canvas.addEventListener('click', startAnimation);
        playButton.addEventListener('click', startAnimation);

        drawInitial();
    </script>
</body>
</html> 