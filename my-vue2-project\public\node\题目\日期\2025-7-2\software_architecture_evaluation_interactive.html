<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构评估方法 - 交互式学习</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f4f8;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            text-align: center;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            padding: 25px;
            border: 1px solid #e0e0e0;
        }
        h1, h2 {
            color: #0056b3;
        }
        #question-box {
            background-color: #e7f3ff;
            border-left: 5px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
            border-radius: 5px;
            font-size: 1.1em;
        }
        canvas {
            background-color: #ffffff;
            border-radius: 8px;
            cursor: pointer;
            border: 1px solid #ccc;
            margin: 15px 0;
            width: 100%;
            height: auto;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 25px;
            font-size: 1em;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .btn:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        .btn.active {
            background-color: #28a745;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        #explanation {
            margin-top: 20px;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 8px;
            border: 1px solid #eee;
            min-height: 100px;
            text-align: left;
            line-height: 1.6;
        }
        .footer {
            margin-top: 25px;
            font-size: 0.9em;
            color: #777;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>软件架构评估方法 交互式指南</h1>
        <p>让我们用生动的方式来理解这个概念！</p>

        <div id="question-box">
            <strong>原题回顾:</strong> 软件架构评估方式主要有三种：基于调查问卷方式、基于度量的方式和基于场景的方式。（___）用于实施阶段的早期，评估者对架构粗略了解，较主观。
        </div>

        <canvas id="interactive-canvas" width="800" height="400"></canvas>
        
        <div class="controls">
            <button id="intro-btn" class="btn">三种方法概览</button>
            <button id="questionnaire-btn" class="btn">调查问卷法</button>
            <button id="scenario-btn" class="btn">场景法</button>
            <button id="metrics-btn" class="btn">度量法</button>
        </div>

        <div id="explanation">
            <h2>说明</h2>
            <p id="explanation-text">点击上面的按钮，开始探索不同的软件架构评估方法。让我为你形象地解释它们是如何工作的！</p>
        </div>
    </div>
    
    <div class="footer">
        <p>一个为您量身打造的交互式学习体验</p>
    </div>

    <script>
        const canvas = document.getElementById('interactive-canvas');
        const ctx = canvas.getContext('2d');
        const explanationText = document.getElementById('explanation-text');
        const h2 = document.querySelector('#explanation h2');

        const buttons = {
            intro: document.getElementById('intro-btn'),
            questionnaire: document.getElementById('questionnaire-btn'),
            scenario: document.getElementById('scenario-btn'),
            metrics: document.getElementById('metrics-btn'),
        };

        let activeScene = 'intro';

        // --- 核心绘图函数 ---
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawBuilding(x, y, scale) {
            ctx.fillStyle = '#8DB6CD'; // LightSteelBlue
            ctx.fillRect(x, y - 150 * scale, 100 * scale, 150 * scale);
            ctx.fillStyle = '#607B8B'; // SlateGray4
            ctx.beginPath();
            ctx.moveTo(x - 10 * scale, y - 150 * scale);
            ctx.lineTo(x + 50 * scale, y - 200 * scale);
            ctx.lineTo(x + 110 * scale, y - 150 * scale);
            ctx.closePath();
            ctx.fill();
            // Windows
            ctx.fillStyle = '#E0FFFF'; // LightCyan
            for (let i = 0; i < 3; i++) {
                for (let j = 0; j < 3; j++) {
                    ctx.fillRect(x + 15 * scale + i * 25 * scale, y - 130 * scale + j * 40 * scale, 15 * scale, 20 * scale);
                }
            }
        }

        function drawPerson(x, y, scale, color = '#FFC107') {
            ctx.fillStyle = color;
            // Head
            ctx.beginPath();
            ctx.arc(x, y - 25 * scale, 10 * scale, 0, Math.PI * 2);
            ctx.fill();
            // Body
            ctx.beginPath();
            ctx.moveTo(x, y - 15 * scale);
            ctx.lineTo(x, y);
            ctx.strokeStyle = color;
            ctx.lineWidth = 5 * scale;
            ctx.stroke();
        }
        
        function drawClipboard(x, y, scale) {
            ctx.fillStyle = '#D2B48C'; // Tan
            ctx.fillRect(x, y, 20 * scale, 25 * scale);
            ctx.fillStyle = '#8B4513'; // SaddleBrown
            ctx.fillRect(x, y, 20 * scale, 5 * scale);
            ctx.fillStyle = 'white';
            ctx.fillRect(x + 2*scale, y + 7*scale, 16*scale, 16*scale);
        }

        function drawRuler(x, y, scale) {
            ctx.fillStyle = '#F0E68C'; // Khaki
            ctx.fillRect(x, y, 100 * scale, 10 * scale);
            ctx.fillStyle = 'black';
            ctx.font = `${8*scale}px Arial`;
            for (let i = 0; i <= 10; i++) {
                ctx.fillRect(x + i * 10 * scale, y, 1 * scale, 5 * scale);
                if(i%2==0) ctx.fillText(i, x + i * 10 * scale - 2*scale, y + 20*scale);
            }
        }
        
        function drawArrow(fromx, fromy, tox, toy, text) {
            const headlen = 10;
            const angle = Math.atan2(toy - fromy, tox - fromx);
            ctx.beginPath();
            ctx.moveTo(fromx, fromy);
            ctx.lineTo(tox, toy);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(tox, toy);
            ctx.lineTo(tox - headlen * Math.cos(angle - Math.PI / 6), toy - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(tox - headlen * Math.cos(angle + Math.PI / 6), toy - headlen * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = '#333';
            ctx.fill();
            if (text) {
                ctx.font = '16px Arial';
                ctx.fillStyle = '#0056b3';
                ctx.textAlign = 'center';
                ctx.fillText(text, (fromx + tox) / 2, (fromy + toy) / 2 - 10);
            }
        }
        
        // --- 场景动画函数 ---
        let animationFrameId;

        function animateIntro() {
            clearCanvas();
            h2.textContent = '三种评估方法概览';
            explanationText.innerHTML = `
                <p>想象一下，软件架构就像一座大厦的设计蓝图。在动工前，我们需要评估这份蓝图好不好。主要有三种方法：</p>
                <ul>
                    <li><b>调查问卷法：</b>像是在早期问问大家的意见，比较主观。</li>
                    <li><b>场景法：</b>模拟未来可能发生的各种情况，比如火灾、大风。</li>
                    <li><b>度量法：</b>用专业的工具，精确测量承重、材料等数据。</li>
                </ul>
                <p>点击其他按钮，查看每种方法的动画演示！</p>
            `;
            
            // Questionnaire
            drawBuilding(150, 250, 0.7);
            drawPerson(50, 250, 2);
            drawClipboard(75, 230, 1.2);
            ctx.font = 'bold 16px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('调查问卷法', 150, 300);

            // Scenario
            drawBuilding(400, 250, 0.7);
            drawPerson(350, 250, 1, '#DC3545'); // Red
            drawPerson(450, 250, 1, '#17A2B8'); // Cyan
            ctx.fillText('场景法', 400, 300);

            // Metrics
            drawBuilding(650, 250, 0.7);
            drawRuler(600, 260, 0.8);
            ctx.fillText('度量法', 650, 300);
        }

        function animateQuestionnaire() {
            clearCanvas();
            h2.textContent = '基于调查问卷的方法';
            explanationText.innerHTML = `
                <p>这种方法就像一个<b>快速访谈</b>。</p>
                <ul>
                    <li><b>何时用：</b>项目早期，大家对架构只有个大概了解。</li>
                    <li><b>特点：</b>操作简单，成本低，但结果很<b>主观</b>，非常依赖评估者的经验。</li>
                    <li><b>好比：</b>买房前，简单问问前房主"这房子住着怎么样？"得到的答案很主观，但能快速有个印象。</li>
                </ul>
                <p style="font-weight:bold; color:#0056b3;">这正好回答了题目中的问题：在早期、粗略了解、较主观的评估方式就是"调查问卷法"。</p>
            `;

            let personX = 50;
            let thoughtAlpha = 0;
            
            function animate() {
                clearCanvas();
                drawBuilding(500, 300, 1.2);
                drawPerson(personX, 300, 3);
                drawClipboard(personX + 30, 270, 1.5);

                if (personX < 200) {
                    personX += 1;
                } else {
                    if (thoughtAlpha < 1) thoughtAlpha += 0.01;
                    ctx.globalAlpha = thoughtAlpha;
                    ctx.strokeStyle = '#999';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.arc(280, 200, 40, 0, Math.PI * 2);
                    ctx.stroke();
                    ctx.font = 'bold 30px Arial';
                    ctx.fillStyle = '#666';
                    ctx.fillText('?', 280, 210);
                    ctx.globalAlpha = 1;
                    ctx.font = '18px Arial';
                    ctx.fillStyle = '#0056b3';
                    ctx.fillText('主观、快速、早期', 500, 50);
                }
                animationFrameId = requestAnimationFrame(animate);
            }
            animate();
        }
        
        function animateScenario() {
            clearCanvas();
            h2.textContent = '基于场景的方法';
            explanationText.innerHTML = `
                <p>这种方法是设想<b>"如果...会怎样？"</b></p>
                <ul>
                    <li><b>何时用：</b>当我们需要深入理解架构在特定情况下的表现时。</li>
                    <li><b>特点：</b>更具体，能揭示潜在的设计问题。它不只是一个方法，而是一个家族，包括SAAM, ATAM, CBAM等。</li>
                    <li><b>好比：</b>买房时，模拟各种生活场景。比如"如果开派对，隔音好吗？"（性能场景），"如果刮台风，窗户牢固吗？"（可靠性场景）。</li>
                </ul>
                <p>其中 <b>CBAM</b> 专门用来分析"成本效益"，即花多少钱升级架构，能带来多少好处。</p>
            `;

            let userX = 100, attackX = 700;
            function animate() {
                clearCanvas();
                drawBuilding(400, 300, 1.3);
                
                // User scenario
                drawPerson(userX, 300, 2, '#28A745'); // Green user
                drawArrow(userX + 20, 270, 350, 270, '正常使用');
                if (userX < 250) userX += 1;

                // Attack scenario
                drawPerson(attackX, 300, 2, '#DC3545'); // Red attacker
                drawArrow(attackX-20, 270, 470, 270, '黑客攻击');
                 if (attackX > 550) attackX -= 1;
                 
                ctx.font = '18px Arial';
                ctx.fillStyle = '#0056b3';
                ctx.fillText('模拟各种具体情况', 400, 50);

                animationFrameId = requestAnimationFrame(animate);
            }
            animate();
        }

        function animateMetrics() {
             clearCanvas();
            h2.textContent = '基于度量的方法';
            explanationText.innerHTML = `
                <p>这种方法追求<b>"用数据说话"</b>。</p>
                <ul>
                    <li><b>何时用：</b>当我们需要客观、量化的数据来评估架构时。</li>
                    <li><b>特点：</b>结果精确、<b>客观</b>，但可能需要复杂的工具和模型，实施成本较高。</li>
                    <li><b>好比：</b>请来专业的验房师，用仪器测量墙体湿度、电路电压、水管压力等。得到的是一堆精确的数字，非常可靠。</li>
                </ul>
            `;
            
            let rulerY = 350;
            function animate() {
                clearCanvas();
                drawBuilding(400, 300, 1.3);
                
                if(rulerY > 250) rulerY -= 1;
                drawRuler(200, rulerY, 1);
                
                ctx.font = 'bold 20px Arial';
                ctx.fillStyle = '#28a745';
                ctx.fillText('99.9%', 550, 180);
                drawArrow(560, 190, 480, 240, '可靠性');
                
                ctx.font = '18px Arial';
                ctx.fillStyle = '#0056b3';
                ctx.fillText('客观、量化、精确', 400, 50);

                animationFrameId = requestAnimationFrame(animate);
            }
            animate();
        }

        // --- 事件监听 ---
        function setActiveButton(scene) {
            Object.values(buttons).forEach(button => button.classList.remove('active'));
            if (buttons[scene]) {
                buttons[scene].classList.add('active');
            }
        }

        function handleSceneChange(scene) {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            activeScene = scene;
            setActiveButton(scene);

            switch (scene) {
                case 'intro':
                    animateIntro();
                    break;
                case 'questionnaire':
                    animateQuestionnaire();
                    break;
                case 'scenario':
                    animateScenario();
                    break;
                case 'metrics':
                    animateMetrics();
                    break;
            }
        }

        buttons.intro.addEventListener('click', () => handleSceneChange('intro'));
        buttons.questionnaire.addEventListener('click', () => handleSceneChange('questionnaire'));
        buttons.scenario.addEventListener('click', () => handleSceneChange('scenario'));
        buttons.metrics.addEventListener('click', () => handleSceneChange('metrics'));

        // --- 初始加载 ---
        window.onload = () => {
             handleSceneChange('intro');
        };

    </script>
</body>
</html> 