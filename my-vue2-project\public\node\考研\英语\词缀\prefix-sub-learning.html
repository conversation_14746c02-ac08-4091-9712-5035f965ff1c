<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：sub-（在下面、次级）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: linear-gradient(to bottom, #87CEEB 0%, #4682B4 30%, #191970 100%);
        }

        #oceanCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .submarine-fleet {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .submarine-dock {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .submarine-dock::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(70, 130, 180, 0.1), transparent);
            transition: left 0.6s;
        }

        .submarine-dock:hover::before {
            left: 100%;
        }

        .submarine-dock:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .submarine-dock.surfaced {
            opacity: 1;
            transform: translateY(0);
        }

        .diving-journey {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(to bottom, #e3f2fd, #bbdefb);
            border-radius: 10px;
            position: relative;
        }

        .surface-word {
            background: #2196f3;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .surface-word::after {
            content: '海面';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #1976d2;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .submarine-vessel {
            width: 60px;
            height: 40px;
            background: linear-gradient(45deg, #607d8b, #455a64);
            border-radius: 30px;
            position: relative;
            margin: 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: dive 3s ease-in-out infinite;
            box-shadow: 0 5px 15px rgba(96, 125, 139, 0.4);
        }

        .submarine-vessel::before {
            content: '🚢';
            font-size: 1.2rem;
        }

        .submarine-vessel::after {
            content: '';
            position: absolute;
            width: 80px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #2196f3, transparent);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: sonar 2s infinite;
        }

        .underwater-word {
            background: #0d47a1;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .underwater-word::after {
            content: '深海';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #0a3d91;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .prefix-highlight {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .depth-explanation {
            background: rgba(33, 150, 243, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .exploration-log {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #ffc107;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #2196f3, #1976d2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #ffc107;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .depth-gauge {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #90a4ae;
            transition: all 0.3s ease;
        }

        .depth-gauge.diving {
            background: #2196f3;
            animation: depthPulse 1.5s infinite;
        }

        .depth-gauge.deep {
            background: #0d47a1;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes dive {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(10px);
            }
        }

        @keyframes sonar {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scaleX(0);
            }
            50% {
                opacity: 1;
                transform: translate(-50%, -50%) scaleX(1);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scaleX(1.5);
            }
        }

        @keyframes depthPulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.3);
            }
        }

        @keyframes bubbleRise {
            0% {
                opacity: 0;
                transform: translateY(20px) scale(0);
            }
            50% {
                opacity: 1;
                transform: translateY(-10px) scale(1);
            }
            100% {
                opacity: 0;
                transform: translateY(-40px) scale(0.5);
            }
        }

        .interactive-hint {
            text-align: center;
            color: #2196f3;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .ocean-bubbles {
            position: absolute;
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            pointer-events: none;
            animation: bubbleRise 3s infinite;
        }

        .depth-chart {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 15px;
        }

        .depth-level {
            width: 25px;
            height: 8px;
            background: #e3f2fd;
            border-radius: 4px;
            transition: all 0.3s ease;
            position: relative;
        }

        .depth-level.active {
            background: linear-gradient(90deg, #2196f3, #1976d2);
        }

        .depth-level.deep {
            background: linear-gradient(90deg, #1976d2, #0d47a1);
        }

        .depth-level::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #bbdefb;
            transform: translateY(-50%);
        }

        .depth-level:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>深海前缀：sub-</h1>
            <p>在深海探索潜水艇中学会"深入下层"的奥秘</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🚢 深海探索潜水艇的故事</h2>
                <p>在广阔的海洋中，有一支神奇的深海探索舰队。这些潜水艇专门探索海面以下的神秘世界。舰队的特殊能力是"sub-"深潜技术，它能让普通的海面词汇潜入深海，获得"在下面"、"次级"或"分支"的新含义。当词汇通过潜水艇的深潜系统时，就会从海面层次下降到深海层次，发现全新的意义！</p>
            </div>

            <div class="canvas-container">
                <canvas id="oceanCanvas"></canvas>
                <div class="depth-chart" id="depthChart">
                    <div class="depth-level"></div>
                    <div class="depth-level"></div>
                    <div class="depth-level"></div>
                    <div class="depth-level"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择深海探索潜水艇的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"深海探索潜水艇"的比喻，是因为"sub-"前缀的核心含义就是"在下面"、"次级"，这与潜水艇从海面潜入深海的过程完美契合。海洋的分层结构（海面→浅海→深海）直观展示了"sub-"表示的层次关系。潜水艇的下潜过程象征着词汇从表面含义深入到更深层的含义，而深海探索的神秘感让抽象的语法概念变得生动有趣。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="startDiving()">开始深潜</button>
                <button class="btn" onclick="surfaceFleet()">舰队上浮</button>
                <button class="btn" onclick="resetOcean()">重置海洋</button>
            </div>

            <div class="interactive-hint">
                🌊 点击"开始深潜"观看词汇深潜过程，点击潜水艇查看探索日志
            </div>
        </div>

        <div class="submarine-fleet" id="submarineFleet">
            <div class="submarine-dock">
                <div class="depth-gauge"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Marine → Submarine</h3>
                <div class="diving-journey">
                    <div class="surface-word">marine</div>
                    <div class="submarine-vessel"></div>
                    <div class="underwater-word"><span class="prefix-highlight">sub</span>marine</div>
                </div>
                <div class="depth-explanation">
                    海洋的 → <span class="prefix-highlight">海面下</span>的
                </div>
                <div class="exploration-log">
                    <strong>深海探索日志：</strong><br>
                    <strong>海面：</strong>Marine life is diverse. (海洋生物很多样。)<br>
                    <strong>深海：</strong>The submarine explored the ocean. (潜水艇探索了海洋。)<br>
                    <strong>解析：</strong>"marine"表示海洋的，加上"sub-"变成"submarine"，表示海面下的、潜水艇。从海面层次深入到海底层次。
                </div>
            </div>

            <div class="submarine-dock">
                <div class="depth-gauge"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Way → Subway</h3>
                <div class="diving-journey">
                    <div class="surface-word">way</div>
                    <div class="submarine-vessel"></div>
                    <div class="underwater-word"><span class="prefix-highlight">sub</span>way</div>
                </div>
                <div class="depth-explanation">
                    道路 → <span class="prefix-highlight">地下</span>道路
                </div>
                <div class="exploration-log">
                    <strong>深海探索日志：</strong><br>
                    <strong>海面：</strong>This is the way to school. (这是去学校的路。)<br>
                    <strong>深海：</strong>Take the subway to downtown. (坐地铁去市中心。)<br>
                    <strong>解析：</strong>"way"表示道路，加上"sub-"变成"subway"，表示地下铁路、地铁。从地面道路深入到地下交通。
                </div>
            </div>

            <div class="submarine-dock">
                <div class="depth-gauge"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Title → Subtitle</h3>
                <div class="diving-journey">
                    <div class="surface-word">title</div>
                    <div class="submarine-vessel"></div>
                    <div class="underwater-word"><span class="prefix-highlight">sub</span>title</div>
                </div>
                <div class="depth-explanation">
                    标题 → <span class="prefix-highlight">副</span>标题
                </div>
                <div class="exploration-log">
                    <strong>深海探索日志：</strong><br>
                    <strong>海面：</strong>The title of this book is interesting. (这本书的标题很有趣。)<br>
                    <strong>深海：</strong>Read the subtitle for more details. (阅读副标题了解更多细节。)<br>
                    <strong>解析：</strong>"title"表示标题，加上"sub-"变成"subtitle"，表示副标题、次级标题。从主标题层次下降到次级标题。
                </div>
            </div>

            <div class="submarine-dock">
                <div class="depth-gauge"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Divide → Subdivide</h3>
                <div class="diving-journey">
                    <div class="surface-word">divide</div>
                    <div class="submarine-vessel"></div>
                    <div class="underwater-word"><span class="prefix-highlight">sub</span>divide</div>
                </div>
                <div class="depth-explanation">
                    分割 → <span class="prefix-highlight">再次</span>分割
                </div>
                <div class="exploration-log">
                    <strong>深海探索日志：</strong><br>
                    <strong>海面：</strong>Divide the cake into pieces. (把蛋糕分成几块。)<br>
                    <strong>深海：</strong>Subdivide each section further. (进一步细分每个部分。)<br>
                    <strong>解析：</strong>"divide"表示分割，加上"sub-"变成"subdivide"，表示再次分割、细分。从粗分层次深入到细分层次。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"sub-"前缀表示在下面、次级、分支、再次的含义。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"sub-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"sub-"后的词根基本含义</li>
                <li><strong>应用层次概念：</strong>在词根意思前加上"下"、"副"、"次"、"分"</li>
                <li><strong>语境调整：</strong>根据句子语境选择最合适的中文表达</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象深海探索潜水艇，"sub-"就像深潜技术，让词汇从表面深入到更深层次！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('oceanCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentDive = 0;
        let bubbles = [];
        let submarines = [];
        let waveOffset = 0;
        
        const dives = [
            { surface: 'marine', underwater: 'submarine', x: 150, depth: 100 },
            { surface: 'way', underwater: 'subway', x: 350, depth: 150 },
            { surface: 'title', underwater: 'subtitle', x: 550, depth: 200 },
            { surface: 'divide', underwater: 'subdivide', x: 750, depth: 250 }
        ];

        class Bubble {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.size = Math.random() * 6 + 2;
                this.speed = Math.random() * 2 + 1;
                this.opacity = Math.random() * 0.5 + 0.3;
            }

            update() {
                this.y -= this.speed;
                this.x += Math.sin(this.y * 0.01) * 0.5;
                if (this.y < 0) {
                    this.y = canvas.height;
                    this.x = Math.random() * canvas.width;
                }
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.opacity;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        class Submarine {
            constructor(x, y, targetDepth) {
                this.x = x;
                this.y = y;
                this.targetDepth = targetDepth;
                this.currentDepth = y;
                this.diving = false;
            }

            update() {
                if (this.diving && this.currentDepth < this.targetDepth) {
                    this.currentDepth += 2;
                }
            }

            draw() {
                ctx.fillStyle = '#607d8b';
                ctx.fillRect(this.x - 25, this.currentDepth - 10, 50, 20);
                
                // 潜水艇窗口
                ctx.fillStyle = '#81c784';
                ctx.beginPath();
                ctx.arc(this.x, this.currentDepth, 6, 0, Math.PI * 2);
                ctx.fill();
                
                // 声纳波
                if (this.diving) {
                    ctx.strokeStyle = 'rgba(33, 150, 243, 0.5)';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.arc(this.x, this.currentDepth, 30 + Math.sin(Date.now() * 0.01) * 10, 0, Math.PI * 2);
                    ctx.stroke();
                }
            }

            startDiving() {
                this.diving = true;
            }
        }

        function initBubbles() {
            bubbles = [];
            for (let i = 0; i < 20; i++) {
                bubbles.push(new Bubble(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height
                ));
            }
        }

        function initSubmarines() {
            submarines = [];
            dives.forEach(dive => {
                submarines.push(new Submarine(dive.x, 80, dive.depth));
            });
        }

        function drawOceanLayers() {
            // 海面层
            const surfaceGradient = ctx.createLinearGradient(0, 0, 0, 100);
            surfaceGradient.addColorStop(0, '#87CEEB');
            surfaceGradient.addColorStop(1, '#4682B4');
            ctx.fillStyle = surfaceGradient;
            ctx.fillRect(0, 0, canvas.width, 100);
            
            // 中层海水
            const middleGradient = ctx.createLinearGradient(0, 100, 0, 300);
            middleGradient.addColorStop(0, '#4682B4');
            middleGradient.addColorStop(1, '#2F4F4F');
            ctx.fillStyle = middleGradient;
            ctx.fillRect(0, 100, canvas.width, 200);
            
            // 深海层
            const deepGradient = ctx.createLinearGradient(0, 300, 0, canvas.height);
            deepGradient.addColorStop(0, '#2F4F4F');
            deepGradient.addColorStop(1, '#191970');
            ctx.fillStyle = deepGradient;
            ctx.fillRect(0, 300, canvas.width, canvas.height - 300);
            
            // 海面波浪
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            for (let x = 0; x <= canvas.width; x += 10) {
                const y = 80 + Math.sin((x + waveOffset) * 0.02) * 5;
                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();
            
            waveOffset += 2;
        }

        function drawWordTransformation() {
            if (currentDive < dives.length && animationState === 'diving') {
                const dive = dives[currentDive];
                
                // 海面词汇
                ctx.fillStyle = 'rgba(33, 150, 243, 0.8)';
                ctx.fillRect(dive.x - 40, 50, 80, 25);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(dive.surface, dive.x, 67);
                
                // 深海词汇
                if (submarines[currentDive] && submarines[currentDive].currentDepth > 120) {
                    ctx.fillStyle = 'rgba(13, 71, 161, 0.9)';
                    ctx.fillRect(dive.x - 50, dive.depth + 30, 100, 25);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    
                    // 高亮sub-前缀
                    ctx.fillStyle = '#ffc107';
                    ctx.fillText('sub', dive.x - 20, dive.depth + 47);
                    ctx.fillStyle = 'white';
                    ctx.fillText(dive.surface, dive.x + 15, dive.depth + 47);
                }
            }
        }

        function updateDepthChart() {
            const levels = document.querySelectorAll('.depth-level');
            levels.forEach((level, index) => {
                level.classList.remove('active', 'deep');
                if (index < currentDive) {
                    level.classList.add('deep');
                } else if (index === currentDive && animationState === 'diving') {
                    level.classList.add('active');
                }
            });
        }

        function updateDockStatus() {
            const docks = document.querySelectorAll('.submarine-dock');
            const gauges = document.querySelectorAll('.depth-gauge');
            
            docks.forEach((dock, index) => {
                const gauge = gauges[index];
                if (index < currentDive) {
                    gauge.classList.remove('diving');
                    gauge.classList.add('deep');
                } else if (index === currentDive && animationState === 'diving') {
                    gauge.classList.add('diving');
                    gauge.classList.remove('deep');
                } else {
                    gauge.classList.remove('diving', 'deep');
                }
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制海洋分层
            drawOceanLayers();
            
            // 绘制气泡
            bubbles.forEach(bubble => {
                bubble.update();
                bubble.draw();
            });
            
            // 绘制潜水艇
            submarines.forEach(submarine => {
                submarine.update();
                submarine.draw();
            });
            
            // 绘制词汇转换
            drawWordTransformation();
            
            // 更新界面状态
            updateDepthChart();
            updateDockStatus();
        }

        function animate() {
            drawScene();
            
            if (animationState === 'diving' && currentDive < dives.length) {
                // 启动当前潜水艇
                if (submarines[currentDive]) {
                    submarines[currentDive].startDiving();
                }
                
                // 自动切换到下一次深潜
                setTimeout(() => {
                    currentDive++;
                    if (currentDive >= dives.length) {
                        animationState = 'completed';
                    }
                }, 3000);
            }
            
            requestAnimationFrame(animate);
        }

        function startDiving() {
            animationState = 'diving';
            currentDive = 0;
            initSubmarines();
        }

        function surfaceFleet() {
            const docks = document.querySelectorAll('.submarine-dock');
            docks.forEach((dock, index) => {
                setTimeout(() => {
                    dock.classList.add('surfaced');
                }, index * 400);
            });
        }

        function resetOcean() {
            animationState = 'idle';
            currentDive = 0;
            initSubmarines();
            
            const docks = document.querySelectorAll('.submarine-dock');
            docks.forEach(dock => dock.classList.remove('surfaced'));
            
            const gauges = document.querySelectorAll('.depth-gauge');
            gauges.forEach(gauge => {
                gauge.classList.remove('diving', 'deep');
            });
        }

        // 初始化
        initBubbles();
        initSubmarines();
        animate();

        // 点击潜水艇坞站的交互
        document.querySelectorAll('.submarine-dock').forEach(dock => {
            dock.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
