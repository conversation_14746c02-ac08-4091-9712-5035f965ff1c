<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构风格 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .blank {
            display: inline-block;
            min-width: 120px;
            height: 40px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            margin: 0 5px;
            position: relative;
            vertical-align: middle;
            background: rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .blank.filled {
            background: #667eea;
            color: white;
            border: 2px solid #667eea;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .option {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            font-size: 1.1rem;
            border: none;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.2);
        }

        .option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 0.6s ease-in-out;
        }

        .architecture-demo {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .demo-title {
            font-size: 1.8rem;
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        #architectureCanvas {
            border: 2px solid #eee;
            border-radius: 15px;
            background: #fafafa;
            cursor: pointer;
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            animation: fadeIn 1s ease-out;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.6;
            font-size: 1.1rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.8s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .game-controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件架构风格学习</h1>
            <p class="subtitle">通过互动动画理解软件架构的奥秘</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                软件架构风格描述某一特定领域中的系统组织方式和惯用模式，反映了领域中众多系统所共有的 
                <span class="blank" id="blank1"></span> 特征。对于语音识别、知识推理等问题复杂、解空间很大、求解过程不确定的这一类软件系统，通常会采用 
                <span class="blank" id="blank2"></span> 架构风格。
            </div>
            
            <div class="options">
                <button class="option" onclick="selectAnswer(this, '结构和语义', 1)">结构和语义</button>
                <button class="option" onclick="selectAnswer(this, '功能和性能', 1)">功能和性能</button>
                <button class="option" onclick="selectAnswer(this, '管道-过滤器', 2)">管道-过滤器</button>
                <button class="option" onclick="selectAnswer(this, '解释器', 2)">解释器</button>
                <button class="option" onclick="selectAnswer(this, '黑板', 2)">黑板</button>
                <button class="option" onclick="selectAnswer(this, '过程控制', 2)">过程控制</button>
            </div>
        </div>

        <div class="architecture-demo">
            <h2 class="demo-title">🎮 架构风格互动演示</h2>
            <div class="canvas-container">
                <canvas id="architectureCanvas" width="800" height="500"></canvas>
            </div>
            <div class="game-controls">
                <button class="btn" onclick="showArchitecture('blackboard')">黑板架构</button>
                <button class="btn" onclick="showArchitecture('pipeline')">管道-过滤器</button>
                <button class="btn" onclick="showArchitecture('interpreter')">解释器</button>
                <button class="btn" onclick="showArchitecture('process')">过程控制</button>
            </div>
        </div>

        <div class="explanation" id="explanation" style="display: none;">
            <h3>📚 知识解析</h3>
            <p id="explanationText"></p>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let answers = { 1: null, 2: null };
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');
        
        function updateProgress() {
            const progress = (currentStep / 2) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function selectAnswer(element, answer, questionNum) {
            // 移除其他选项的选中状态
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct');
            });
            
            answers[questionNum] = answer;
            element.classList.add('correct');
            
            // 填入空白处
            const blank = document.getElementById(`blank${questionNum}`);
            blank.textContent = answer;
            blank.classList.add('filled');
            
            if (!currentStep || currentStep < questionNum) {
                currentStep = questionNum;
                updateProgress();
            }
            
            // 显示解释
            showExplanation(questionNum, answer);
            
            // 如果都答对了，显示完整解释
            if (answers[1] === '结构和语义' && answers[2] === '黑板') {
                setTimeout(() => {
                    showCompleteExplanation();
                }, 1000);
            }
        }

        function showExplanation(questionNum, answer) {
            const explanationDiv = document.getElementById('explanation');
            const explanationText = document.getElementById('explanationText');
            
            let text = '';
            if (questionNum === 1) {
                if (answer === '结构和语义') {
                    text = '✅ 正确！软件架构风格确实反映了系统的结构和语义特征。结构指的是组件的组织方式，语义指的是组件间的交互规则和约束。';
                } else {
                    text = '❌ 不对哦！软件架构风格主要关注的是系统的结构（如何组织）和语义（如何交互）特征。';
                }
            } else if (questionNum === 2) {
                if (answer === '黑板') {
                    text = '🎉 太棒了！黑板架构最适合处理复杂、不确定的问题。它就像一个智能的公告板，多个专家（知识源）可以在上面协作解决问题！';
                } else {
                    text = `❌ ${answer}架构不是最佳选择。对于语音识别、知识推理这类复杂问题，需要多个专家协作，黑板架构是最合适的！`;
                }
            }
            
            explanationText.textContent = text;
            explanationDiv.style.display = 'block';
        }

        function showCompleteExplanation() {
            const explanationText = document.getElementById('explanationText');
            explanationText.innerHTML = `
                <strong>🎊 恭喜你全部答对！</strong><br><br>
                <strong>黑板架构的特点：</strong><br>
                • 🧠 多个知识源（专家）协同工作<br>
                • 📋 共享的黑板作为通信媒介<br>
                • 🔄 动态的问题求解过程<br>
                • 🎯 特别适合AI、语音识别、专家系统等领域<br><br>
                点击下方按钮体验不同架构风格的动画演示！
            `;
        }

        function showArchitecture(type) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(type) {
                case 'blackboard':
                    animateBlackboard();
                    break;
                case 'pipeline':
                    animatePipeline();
                    break;
                case 'interpreter':
                    animateInterpreter();
                    break;
                case 'process':
                    animateProcess();
                    break;
            }
        }

        function animateBlackboard() {
            // 绘制黑板
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(300, 150, 200, 200);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('黑板', 400, 180);
            ctx.fillText('(共享数据)', 400, 200);
            
            // 绘制知识源
            const experts = [
                {x: 100, y: 100, name: '语音专家'},
                {x: 100, y: 300, name: '语法专家'},
                {x: 600, y: 100, name: '语义专家'},
                {x: 600, y: 300, name: '推理专家'}
            ];
            
            experts.forEach((expert, index) => {
                setTimeout(() => {
                    // 绘制专家
                    ctx.fillStyle = '#3498db';
                    ctx.fillRect(expert.x, expert.y, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(expert.name, expert.x + 50, expert.y + 35);
                    
                    // 绘制箭头
                    drawArrow(expert.x + 50, expert.y + 30, 400, 250);
                }, index * 500);
            });
        }

        function animatePipeline() {
            const stages = ['输入', '过滤器1', '过滤器2', '过滤器3', '输出'];
            stages.forEach((stage, index) => {
                setTimeout(() => {
                    const x = 50 + index * 140;
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillRect(x, 200, 120, 80);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(stage, x + 60, 245);
                    
                    if (index < stages.length - 1) {
                        drawArrow(x + 120, 240, x + 140, 240);
                    }
                }, index * 400);
            });
        }

        function animateInterpreter() {
            // 解释器核心
            ctx.fillStyle = '#9b59b6';
            ctx.fillRect(300, 200, 200, 100);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('解释器引擎', 400, 250);
            
            // 程序代码
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(50, 100, 150, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('程序代码', 125, 145);
            
            // 执行结果
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(600, 100, 150, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('执行结果', 675, 145);
            
            drawArrow(200, 140, 300, 220);
            drawArrow(500, 220, 600, 140);
        }

        function animateProcess() {
            // 控制器
            ctx.fillStyle = '#34495e';
            ctx.fillRect(300, 50, 200, 80);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('过程控制器', 400, 95);
            
            // 受控过程
            const processes = [
                {x: 100, y: 200, name: '过程1'},
                {x: 300, y: 200, name: '过程2'},
                {x: 500, y: 200, name: '过程3'}
            ];
            
            processes.forEach((proc, index) => {
                setTimeout(() => {
                    ctx.fillStyle = '#16a085';
                    ctx.fillRect(proc.x, proc.y, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(proc.name, proc.x + 50, proc.y + 35);
                    
                    // 控制线
                    drawArrow(400, 130, proc.x + 50, proc.y);
                }, index * 300);
            });
        }

        function drawArrow(fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#7f8c8d';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        // 初始化
        updateProgress();
        
        // 欢迎动画
        setTimeout(() => {
            showArchitecture('blackboard');
        }, 2000);
    </script>
</body>
</html>
