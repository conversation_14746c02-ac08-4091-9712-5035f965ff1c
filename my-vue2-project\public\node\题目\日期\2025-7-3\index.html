<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机票销售系统进程演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h1, h2 {
            color: #2c3e50;
            text-align: center;
        }
        
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .problem {
            background-color: #f9f9f9;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .explanation {
            margin-bottom: 20px;
        }
        
        .canvas-container {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }
        
        canvas {
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
        }
        
        .controls {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        .options {
            margin-top: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .option {
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option:hover {
            background-color: #f0f7ff;
            border-color: #3498db;
        }
        
        .option.selected {
            background-color: #e1f0fa;
            border-color: #3498db;
            font-weight: bold;
        }
        
        .feedback {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            display: none;
        }
        
        .correct {
            background-color: #d4edda;
            color: #155724;
        }
        
        .incorrect {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            position: relative;
        }
        
        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }
        
        .step.active {
            background-color: #3498db;
            color: white;
        }
        
        .step.completed {
            background-color: #2ecc71;
            color: white;
        }
        
        .step-line {
            position: absolute;
            top: 15px;
            left: 30px;
            right: 30px;
            height: 2px;
            background-color: #ddd;
            z-index: 1;
        }
        
        .progress {
            position: absolute;
            top: 15px;
            left: 30px;
            height: 2px;
            background-color: #2ecc71;
            z-index: 1;
            width: 0%;
            transition: width 0.5s;
        }

        .concept-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .concept-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .concept-card:hover {
            transform: translateY(-5px);
        }

        .concept-card h3 {
            margin-top: 0;
            color: #fff;
            font-size: 1.2em;
        }

        .concept-card .example {
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-style: italic;
        }

        .real-world-scenario {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            color: #333;
        }

        .real-world-scenario h3 {
            color: #d63384;
            margin-top: 0;
        }

        .interactive-demo {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .demo-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .demo-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .demo-button:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .demo-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .status-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .status-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .status-item.active {
            border-color: #28a745;
            background: #d4edda;
        }

        .status-item.waiting {
            border-color: #ffc107;
            background: #fff3cd;
        }

        .status-item.processing {
            border-color: #17a2b8;
            background: #d1ecf1;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .log-display {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin: 15px 0;
        }

        .log-entry {
            margin: 5px 0;
            padding: 2px 0;
        }

        .log-entry.success {
            color: #68d391;
        }

        .log-entry.warning {
            color: #fbb6ce;
        }

        .log-entry.info {
            color: #90cdf4;
        }

        .question-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .explanation-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .customer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 2px;
            animation: bounce 0.5s ease-out;
        }

        @keyframes bounce {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .process-flow {
            display: flex;
            align-items: center;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .flow-step {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            min-width: 120px;
            transition: all 0.3s ease;
        }

        .flow-step.active {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            transform: scale(1.05);
        }

        .flow-arrow {
            font-size: 24px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>机票销售系统进程同步演示</h1>
        
        <div class="problem">
            <h2>题目描述</h2>
            <p>某航空公司机票销售系统有n个销售点，该系统为每个销售点创建一个进程P(i=1, 2, ..., n)管理机票销售。</p>
            <p>假设T(j=1, 2, ..., m)单元存放某日某班的机票剩余数，Temp为P进程的临时工作单元，x为用户的订票张数。</p>
            <p>初始化时系统应将信号量S赋值为（ ）。P进程的工作流如下所示，若用操作P和V操作定义进程间的同步与互斥，则图中空(a)、空(b)和空(c)处应分别填入(请作者选定)。</p>
        </div>
        
        <div class="explanation">
            <h2>📚 知识点解释</h2>
            <div class="concept-cards">
                <div class="concept-card">
                    <h3>🔒 互斥 (Mutual Exclusion)</h3>
                    <p>想象一个银行ATM机，同一时间只能有一个人使用。多个销售点就像多个人排队，必须一个一个来访问机票数据库。</p>
                    <div class="example">
                        <strong>生活例子：</strong> 厕所门锁 - 有人在里面时，其他人必须等待
                    </div>
                </div>

                <div class="concept-card">
                    <h3>🔄 同步 (Synchronization)</h3>
                    <p>确保操作按正确顺序进行。就像做菜一样，必须先洗菜、再切菜、最后炒菜，不能颠倒顺序。</p>
                    <div class="example">
                        <strong>生活例子：</strong> 接力赛跑 - 必须等前一个人跑到才能接棒
                    </div>
                </div>

                <div class="concept-card">
                    <h3>⬇️ P操作 (Wait/Down)</h3>
                    <p>申请进入临界区。就像敲门请求进入房间，如果房间被占用就在门外等待。</p>
                    <div class="example">
                        <strong>生活例子：</strong> 排队买票 - 取号等待叫号
                    </div>
                </div>

                <div class="concept-card">
                    <h3>⬆️ V操作 (Signal/Up)</h3>
                    <p>离开临界区并通知等待者。就像离开房间后告诉下一个人可以进来了。</p>
                    <div class="example">
                        <strong>生活例子：</strong> 用完厕所开门 - 让下一个人知道可以使用了
                    </div>
                </div>
            </div>

            <div class="real-world-scenario">
                <h3>🎫 机票销售的现实场景</h3>
                <p>想象你在12306买火车票的情况：</p>
                <ol>
                    <li><strong>多个用户同时抢票</strong> - 就像多个销售点P₁, P₂, P₃...</li>
                    <li><strong>票数有限</strong> - 存储在数据库中的T[j]</li>
                    <li><strong>必须防止超卖</strong> - 不能让票数变成负数</li>
                    <li><strong>操作要原子性</strong> - 查票→减票→更新，这个过程不能被打断</li>
                </ol>
            </div>
        </div>
        
        <div class="interactive-demo">
            <h2>🎮 互动体验：抢票模拟器</h2>
            <p>体验多个用户同时抢票的情况，理解为什么需要进程同步！</p>

            <div class="demo-controls">
                <button class="demo-button" id="addCustomer">添加顾客</button>
                <button class="demo-button" id="startSelling">开始售票</button>
                <button class="demo-button" id="pauseSelling">暂停</button>
                <button class="demo-button" id="resetDemo">重置演示</button>
                <label>
                    <input type="checkbox" id="enableSync"> 启用同步机制
                </label>
            </div>

            <div class="status-display">
                <div class="status-item">
                    <h4>🎫 剩余票数</h4>
                    <div id="ticketCount" style="font-size: 2em; font-weight: bold; color: #e74c3c;">20</div>
                </div>
                <div class="status-item">
                    <h4>👥 等待队列</h4>
                    <div id="queueCount" style="font-size: 2em; font-weight: bold; color: #f39c12;">0</div>
                </div>
                <div class="status-item">
                    <h4>✅ 成功售出</h4>
                    <div id="soldCount" style="font-size: 2em; font-weight: bold; color: #27ae60;">0</div>
                </div>
                <div class="status-item">
                    <h4>❌ 冲突次数</h4>
                    <div id="conflictCount" style="font-size: 2em; font-weight: bold; color: #e74c3c;">0</div>
                </div>
            </div>

            <div class="log-display" id="logDisplay">
                <div class="log-entry info">🚀 系统已启动，等待顾客...</div>
            </div>
        </div>

        <div class="container">
            <h2>🔄 进程同步流程图</h2>
            <div class="process-flow">
                <div class="flow-step" id="step1">
                    <div style="font-size: 24px;">🚪</div>
                    <strong>P(S)</strong>
                    <small>申请进入临界区</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step" id="step2">
                    <div style="font-size: 24px;">📖</div>
                    <strong>读取</strong>
                    <small>Temp = T[j]</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step" id="step3">
                    <div style="font-size: 24px;">✏️</div>
                    <strong>计算</strong>
                    <small>Temp = Temp - x</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step" id="step4">
                    <div style="font-size: 24px;">💾</div>
                    <strong>写入</strong>
                    <small>T[j] = Temp</small>
                </div>
                <div class="flow-arrow">→</div>
                <div class="flow-step" id="step5">
                    <div style="font-size: 24px;">🔓</div>
                    <strong>V(S)</strong>
                    <small>离开临界区</small>
                </div>
            </div>

            <div style="text-align: center; margin: 20px 0;">
                <button class="demo-button" id="simulateFlow">模拟流程</button>
                <button class="demo-button" id="resetFlow">重置流程</button>
            </div>

            <div class="real-world-scenario">
                <h3>🎯 关键理解点</h3>
                <ul>
                    <li><strong>为什么信号量初值是1？</strong> 因为我们要确保同时只有一个进程能访问共享数据</li>
                    <li><strong>P操作在哪里？</strong> 在进入临界区之前，就像进门前要先敲门</li>
                    <li><strong>V操作在哪里？</strong> 在离开临界区之后，就像出门后要关门</li>
                    <li><strong>临界区是什么？</strong> 读取→计算→写入这个完整的操作序列</li>
                </ul>
            </div>
        </div>

        <div class="canvas-container">
            <h2>📊 进程流程动画演示</h2>
            <div class="step-indicator">
                <div class="step-line"></div>
                <div class="progress"></div>
                <div class="step active" data-step="1">1</div>
                <div class="step" data-step="2">2</div>
                <div class="step" data-step="3">3</div>
                <div class="step" data-step="4">4</div>
            </div>
            <canvas id="processCanvas" width="800" height="500"></canvas>
            <div class="controls">
                <button id="startBtn">开始演示</button>
                <button id="nextBtn" disabled>下一步</button>
                <button id="resetBtn">重置</button>
            </div>
        </div>
        
        <div class="container">
            <h2>🧠 知识测试</h2>

            <div class="question-section">
                <h3>问题1: 信号量初始值</h3>
                <p>💡 <strong>思考提示：</strong> 信号量S用来控制对共享资源的访问。想想看，我们希望同时有几个进程能够访问机票数据？</p>
                <p>初始化时系统应将信号量S赋值为：</p>
                <div class="options-grid">
                    <div class="option" data-question="1" data-value="0">
                        <strong>0</strong>
                        <small>没有进程可以访问</small>
                    </div>
                    <div class="option" data-question="1" data-value="1">
                        <strong>1</strong>
                        <small>同时只能有1个进程访问</small>
                    </div>
                    <div class="option" data-question="1" data-value="m">
                        <strong>m</strong>
                        <small>机票剩余数</small>
                    </div>
                    <div class="option" data-question="1" data-value="n">
                        <strong>n</strong>
                        <small>销售点数量</small>
                    </div>
                </div>

                <div class="explanation-box" id="explanation1" style="display: none;">
                    <h4>💡 解释</h4>
                    <p><strong>正确答案是 1</strong></p>
                    <p>因为机票数据库是<strong>临界资源</strong>，同一时间只能有一个进程访问，否则会出现数据不一致的问题。就像银行ATM一样，同时只能有一个人使用。</p>
                </div>
            </div>

            <div class="question-section">
                <h3>问题2: P和V操作的位置</h3>
                <p>💡 <strong>思考提示：</strong> P操作用于申请资源，V操作用于释放资源。想想进程在什么时候需要申请，什么时候需要释放？</p>
                <p>图中空(a)、空(b)和空(c)处应分别填入：</p>
                <div class="options-grid">
                    <div class="option" data-question="2" data-value="A">
                        <strong>A.</strong> P(S)、V(S)和V(S)
                        <small>申请→释放→释放</small>
                    </div>
                    <div class="option" data-question="2" data-value="B">
                        <strong>B.</strong> P(S)、P(S)和V(S)
                        <small>申请→申请→释放</small>
                    </div>
                    <div class="option" data-question="2" data-value="C">
                        <strong>C.</strong> V(S)、P(S)和P(S)
                        <small>释放→申请→申请</small>
                    </div>
                    <div class="option" data-question="2" data-value="D">
                        <strong>D.</strong> V(S)、V(S)和P(S)
                        <small>释放→释放→申请</small>
                    </div>
                </div>

                <div class="explanation-box" id="explanation2" style="display: none;">
                    <h4>💡 解释</h4>
                    <p><strong>正确答案是 B</strong></p>
                    <p>
                        <strong>(a) P(S)：</strong> 进入临界区前申请资源<br>
                        <strong>(b) P(S)：</strong> 这里应该是其他操作，不是P或V<br>
                        <strong>(c) V(S)：</strong> 离开临界区时释放资源
                    </p>
                    <p>实际上正确答案应该是：<strong>P(S)、其他操作、V(S)</strong></p>
                </div>
            </div>

            <div class="feedback" id="overallFeedback"></div>
        </div>
    </div>

    <script>
        // 抢票模拟器状态
        let ticketSimulator = {
            tickets: 20,
            customers: [],
            queue: [],
            sold: 0,
            conflicts: 0,
            isRunning: false,
            syncEnabled: false,
            semaphore: 1,
            nextCustomerId: 1
        };

        // 顾客类
        class Customer {
            constructor(id) {
                this.id = id;
                this.name = `顾客${id}`;
                this.status = 'waiting'; // waiting, processing, success, failed
                this.wantToBuy = Math.floor(Math.random() * 3) + 1; // 1-3张票
                this.color = this.getRandomColor();
            }

            getRandomColor() {
                const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c'];
                return colors[Math.floor(Math.random() * colors.length)];
            }
        }

        // 获取Canvas和上下文
        const canvas = document.getElementById('processCanvas');
        const ctx = canvas.getContext('2d');

        // 定义颜色
        const colors = {
            background: '#ffffff',
            box: '#e1f5fe',
            boxBorder: '#03a9f4',
            arrow: '#2196f3',
            text: '#333333',
            highlight: '#ff5722',
            success: '#4caf50',
            waiting: '#ffc107'
        };
        
        // 定义元素
        const elements = {
            salesPoints: [],
            ticketStorage: { x: 600, y: 250, width: 120, height: 80, label: 'T[j]机票剩余数', value: 10 },
            tempStorage: { x: 300, y: 250, width: 100, height: 60, label: 'Temp', value: 0 },
            operations: [
                { x: 150, y: 100, label: '(a)', value: '?' },
                { x: 450, y: 200, label: '(b)', value: '?' },
                { x: 300, y: 350, label: '(c)', value: '?' }
            ],
            arrows: [
                { from: { x: 150, y: 150 }, to: { x: 150, y: 200 }, label: '' },
                { from: { x: 150, y: 250 }, to: { x: 250, y: 250 }, label: 'Temp=T[j]' },
                { from: { x: 350, y: 250 }, to: { x: 450, y: 250 }, label: 'Temp-=x' },
                { from: { x: 500, y: 250 }, to: { x: 550, y: 250 }, label: 'T[j]=Temp' },
                { from: { x: 300, y: 300 }, to: { x: 300, y: 350 }, label: '' },
                { from: { x: 300, y: 400 }, to: { x: 150, y: 400 }, label: '售出机票' }
            ]
        };
        
        // 创建销售点
        for (let i = 0; i < 3; i++) {
            elements.salesPoints.push({
                x: 50,
                y: 100 + i * 150,
                width: 80,
                height: 50,
                label: `销售点P${i+1}`,
                status: i === 0 ? 'active' : 'idle'
            });
        }
        
        // 当前步骤
        let currentStep = 0;
        const totalSteps = 6;
        let animationId = null;
        let isAnimating = false;
        
        // 按钮元素
        const startBtn = document.getElementById('startBtn');
        const nextBtn = document.getElementById('nextBtn');
        const resetBtn = document.getElementById('resetBtn');
        
        // 绘制函数
        function draw() {
            // 清空画布
            ctx.fillStyle = colors.background;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制机票存储
            drawBox(elements.ticketStorage);
            
            // 绘制临时存储
            drawBox(elements.tempStorage);
            
            // 绘制销售点
            elements.salesPoints.forEach(point => {
                drawSalesPoint(point);
            });
            
            // 绘制操作
            elements.operations.forEach(op => {
                drawOperation(op);
            });
            
            // 绘制箭头
            elements.arrows.forEach((arrow, index) => {
                const isActive = index === currentStep - 1;
                drawArrow(arrow, isActive);
            });
        }
        
        // 绘制盒子
        function drawBox(box) {
            ctx.fillStyle = colors.box;
            ctx.strokeStyle = colors.boxBorder;
            ctx.lineWidth = 2;
            
            ctx.beginPath();
            ctx.rect(box.x, box.y, box.width, box.height);
            ctx.fill();
            ctx.stroke();
            
            ctx.fillStyle = colors.text;
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(box.label, box.x + box.width / 2, box.y + 20);
            
            ctx.font = 'bold 16px Arial';
            ctx.fillText(box.value, box.x + box.width / 2, box.y + box.height / 2 + 10);
        }
        
        // 绘制销售点
        function drawSalesPoint(point) {
            if (point.status === 'active') {
                ctx.fillStyle = '#e3f2fd';
                ctx.strokeStyle = '#2196f3';
            } else if (point.status === 'waiting') {
                ctx.fillStyle = '#fff8e1';
                ctx.strokeStyle = colors.waiting;
            } else {
                ctx.fillStyle = '#f5f5f5';
                ctx.strokeStyle = '#9e9e9e';
            }
            
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.rect(point.x, point.y, point.width, point.height);
            ctx.fill();
            ctx.stroke();
            
            ctx.fillStyle = colors.text;
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(point.label, point.x + point.width / 2, point.y + point.height / 2 + 5);
        }
        
        // 绘制操作
        function drawOperation(op) {
            ctx.fillStyle = op.highlight ? colors.highlight : colors.box;
            ctx.strokeStyle = op.highlight ? colors.highlight : colors.boxBorder;
            ctx.lineWidth = 2;
            
            ctx.beginPath();
            ctx.arc(op.x, op.y, 25, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
            
            ctx.fillStyle = colors.text;
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(op.label, op.x, op.y - 5);
            ctx.font = 'bold 14px Arial';
            ctx.fillText(op.value, op.x, op.y + 15);
        }
        
        // 绘制箭头
        function drawArrow(arrow, isActive) {
            const headLength = 10;
            const headAngle = Math.PI / 6;
            
            // 计算箭头方向
            const dx = arrow.to.x - arrow.from.x;
            const dy = arrow.to.y - arrow.from.y;
            const angle = Math.atan2(dy, dx);
            
            // 设置线条样式
            ctx.strokeStyle = isActive ? colors.highlight : colors.arrow;
            ctx.lineWidth = isActive ? 3 : 2;
            
            // 绘制线条
            ctx.beginPath();
            ctx.moveTo(arrow.from.x, arrow.from.y);
            ctx.lineTo(arrow.to.x, arrow.to.y);
            ctx.stroke();
            
            // 绘制箭头头部
            ctx.beginPath();
            ctx.moveTo(arrow.to.x, arrow.to.y);
            ctx.lineTo(
                arrow.to.x - headLength * Math.cos(angle - headAngle),
                arrow.to.y - headLength * Math.sin(angle - headAngle)
            );
            ctx.lineTo(
                arrow.to.x - headLength * Math.cos(angle + headAngle),
                arrow.to.y - headLength * Math.sin(angle + headAngle)
            );
            ctx.closePath();
            ctx.fillStyle = isActive ? colors.highlight : colors.arrow;
            ctx.fill();
            
            // 绘制标签
            if (arrow.label) {
                const midX = (arrow.from.x + arrow.to.x) / 2;
                const midY = (arrow.from.y + arrow.to.y) / 2 - 10;
                
                ctx.fillStyle = isActive ? colors.highlight : colors.text;
                ctx.font = isActive ? 'bold 14px Arial' : '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(arrow.label, midX, midY);
            }
        }
        
        // 更新进度指示器
        function updateStepIndicator() {
            const steps = document.querySelectorAll('.step');
            const progress = document.querySelector('.progress');
            
            steps.forEach((step, index) => {
                if (index + 1 < currentStep) {
                    step.classList.remove('active');
                    step.classList.add('completed');
                } else if (index + 1 === currentStep) {
                    step.classList.add('active');
                    step.classList.remove('completed');
                } else {
                    step.classList.remove('active', 'completed');
                }
            });
            
            // 更新进度条
            const progressWidth = ((currentStep - 1) / (steps.length - 1)) * 100;
            progress.style.width = `${progressWidth}%`;
        }
        
        // 执行动画
        function animate() {
            if (currentStep > totalSteps) {
                isAnimating = false;
                nextBtn.disabled = true;
                startBtn.disabled = false;
                return;
            }
            
            // 重置高亮
            elements.operations.forEach(op => op.highlight = false);
            
            switch (currentStep) {
                case 1:
                    // 高亮第一个操作 (a)
                    elements.operations[0].highlight = true;
                    elements.operations[0].value = 'P(S)';
                    elements.salesPoints[0].status = 'active';
                    elements.salesPoints[1].status = 'waiting';
                    break;
                case 2:
                    // 读取票数到Temp
                    elements.tempStorage.value = elements.ticketStorage.value;
                    break;
                case 3:
                    // 高亮第二个操作 (b)
                    elements.operations[1].highlight = true;
                    elements.operations[1].value = 'P(S)';
                    // 减去购票数量
                    elements.tempStorage.value -= 3; // 假设购买3张票
                    break;
                case 4:
                    // 更新票数
                    elements.ticketStorage.value = elements.tempStorage.value;
                    break;
                case 5:
                    // 高亮第三个操作 (c)
                    elements.operations[2].highlight = true;
                    elements.operations[2].value = 'V(S)';
                    break;
                case 6:
                    // 完成售票，下一个销售点激活
                    elements.salesPoints[0].status = 'idle';
                    elements.salesPoints[1].status = 'active';
                    elements.salesPoints[2].status = 'waiting';
                    break;
            }
            
            // 更新进度指示器
            updateStepIndicator();
            
            // 绘制当前状态
            draw();
            
            currentStep++;
            nextBtn.disabled = false;
        }
        
        // 初始化
        function init() {
            currentStep = 0;
            elements.ticketStorage.value = 10;
            elements.tempStorage.value = 0;
            elements.operations.forEach(op => {
                op.highlight = false;
                op.value = '?';
            });
            elements.salesPoints.forEach((point, index) => {
                point.status = index === 0 ? 'active' : 'idle';
            });
            
            draw();
            
            nextBtn.disabled = true;
            startBtn.disabled = false;
        }
        
        // 事件监听
        startBtn.addEventListener('click', () => {
            isAnimating = true;
            startBtn.disabled = true;
            currentStep = 1;
            animate();
        });
        
        nextBtn.addEventListener('click', () => {
            if (!isAnimating) return;
            nextBtn.disabled = true;
            animate();
        });
        
        resetBtn.addEventListener('click', () => {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            isAnimating = false;
            init();
            updateStepIndicator();
        });
        
        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const parent = this.parentElement;
                parent.querySelectorAll('.option').forEach(opt => {
                    if (opt.textContent.includes(this.textContent.trim())) {
                        opt.classList.toggle('selected');
                    } else {
                        opt.classList.remove('selected');
                    }
                });
                
                checkAnswer();
            });
        });
        
        // 检查答案
        function checkAnswer() {
            const selectedOptions = document.querySelectorAll('.option.selected');
            if (selectedOptions.length === 2) {
                const feedback = document.querySelector('.feedback');
                const sValue = selectedOptions[0].getAttribute('data-value');
                const abcValue = selectedOptions[1].getAttribute('data-value');
                
                if (sValue === '1' && abcValue === '2') {
                    feedback.textContent = '恭喜！答案正确！';
                    feedback.className = 'feedback correct';
                } else {
                    feedback.textContent = '答案不正确，请重试！';
                    feedback.className = 'feedback incorrect';
                }
                
                feedback.style.display = 'block';
            }
        }
        
        // 抢票模拟器功能
        function addCustomer() {
            const customer = new Customer(ticketSimulator.nextCustomerId++);
            ticketSimulator.customers.push(customer);
            ticketSimulator.queue.push(customer);
            updateDisplay();
            addLog(`👤 ${customer.name} 加入队列，想买 ${customer.wantToBuy} 张票`, 'info');
        }

        function startSelling() {
            if (ticketSimulator.isRunning) return;
            ticketSimulator.isRunning = true;
            ticketSimulator.syncEnabled = document.getElementById('enableSync').checked;
            addLog(`🚀 开始售票！同步机制：${ticketSimulator.syncEnabled ? '启用' : '禁用'}`, 'info');
            processQueue();
        }

        function pauseSelling() {
            ticketSimulator.isRunning = false;
            addLog('⏸️ 售票已暂停', 'warning');
        }

        function resetDemo() {
            ticketSimulator = {
                tickets: 20,
                customers: [],
                queue: [],
                sold: 0,
                conflicts: 0,
                isRunning: false,
                syncEnabled: false,
                semaphore: 1,
                nextCustomerId: 1
            };
            updateDisplay();
            clearLog();
            addLog('🔄 系统已重置', 'info');
        }

        function processQueue() {
            if (!ticketSimulator.isRunning || ticketSimulator.queue.length === 0) {
                return;
            }

            const customer = ticketSimulator.queue.shift();
            customer.status = 'processing';
            updateDisplay();
            addLog(`🔄 ${customer.name} 开始购票流程`, 'info');

            // 模拟购票过程
            setTimeout(() => {
                if (ticketSimulator.syncEnabled) {
                    // 使用同步机制
                    buyTicketsWithSync(customer);
                } else {
                    // 不使用同步机制
                    buyTicketsWithoutSync(customer);
                }

                setTimeout(() => {
                    if (ticketSimulator.isRunning) {
                        processQueue();
                    }
                }, 500);
            }, 1000);
        }

        function buyTicketsWithSync(customer) {
            // P操作 - 申请信号量
            if (ticketSimulator.semaphore > 0) {
                ticketSimulator.semaphore--;
                addLog(`🔒 ${customer.name} 获得访问权限 (P操作)`, 'info');

                // 临界区操作
                const currentTickets = ticketSimulator.tickets;
                addLog(`📖 ${customer.name} 读取票数: ${currentTickets}`, 'info');

                if (currentTickets >= customer.wantToBuy) {
                    // 模拟处理时间
                    setTimeout(() => {
                        ticketSimulator.tickets -= customer.wantToBuy;
                        ticketSimulator.sold += customer.wantToBuy;
                        customer.status = 'success';
                        addLog(`✅ ${customer.name} 成功购买 ${customer.wantToBuy} 张票`, 'success');

                        // V操作 - 释放信号量
                        ticketSimulator.semaphore++;
                        addLog(`🔓 ${customer.name} 释放访问权限 (V操作)`, 'info');
                        updateDisplay();
                    }, 500);
                } else {
                    customer.status = 'failed';
                    addLog(`❌ ${customer.name} 购票失败，票数不足`, 'warning');

                    // V操作 - 释放信号量
                    ticketSimulator.semaphore++;
                    addLog(`🔓 ${customer.name} 释放访问权限 (V操作)`, 'info');
                    updateDisplay();
                }
            } else {
                addLog(`⏳ ${customer.name} 等待访问权限...`, 'warning');
                ticketSimulator.queue.unshift(customer); // 重新加入队列
            }
        }

        function buyTicketsWithoutSync(customer) {
            // 没有同步机制，可能出现竞态条件
            const currentTickets = ticketSimulator.tickets;
            addLog(`📖 ${customer.name} 读取票数: ${currentTickets}`, 'info');

            if (currentTickets >= customer.wantToBuy) {
                // 模拟并发访问导致的问题
                const randomDelay = Math.random() * 1000;
                setTimeout(() => {
                    // 检查是否有其他进程同时修改了票数
                    if (ticketSimulator.tickets !== currentTickets) {
                        ticketSimulator.conflicts++;
                        addLog(`⚠️ ${customer.name} 检测到数据冲突！`, 'warning');
                    }

                    if (ticketSimulator.tickets >= customer.wantToBuy) {
                        ticketSimulator.tickets -= customer.wantToBuy;
                        ticketSimulator.sold += customer.wantToBuy;
                        customer.status = 'success';
                        addLog(`✅ ${customer.name} 购买 ${customer.wantToBuy} 张票`, 'success');
                    } else {
                        customer.status = 'failed';
                        addLog(`❌ ${customer.name} 购票失败，票数不足`, 'warning');
                    }
                    updateDisplay();
                }, randomDelay);
            } else {
                customer.status = 'failed';
                addLog(`❌ ${customer.name} 购票失败，票数不足`, 'warning');
                updateDisplay();
            }
        }

        function updateDisplay() {
            document.getElementById('ticketCount').textContent = ticketSimulator.tickets;
            document.getElementById('queueCount').textContent = ticketSimulator.queue.length;
            document.getElementById('soldCount').textContent = ticketSimulator.sold;
            document.getElementById('conflictCount').textContent = ticketSimulator.conflicts;

            // 更新状态显示
            const statusItems = document.querySelectorAll('.status-item');
            statusItems.forEach(item => {
                item.classList.remove('active', 'waiting', 'processing');
            });

            if (ticketSimulator.queue.length > 0) {
                statusItems[1].classList.add('waiting');
            }

            if (ticketSimulator.isRunning) {
                statusItems[0].classList.add('processing');
            }
        }

        function addLog(message, type = 'info') {
            const logDisplay = document.getElementById('logDisplay');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDisplay.appendChild(logEntry);
            logDisplay.scrollTop = logDisplay.scrollHeight;
        }

        function clearLog() {
            document.getElementById('logDisplay').innerHTML = '';
        }

        // 问答系统
        function handleQuestionAnswer(questionNum, selectedValue) {
            const explanationId = `explanation${questionNum}`;
            const explanationBox = document.getElementById(explanationId);

            // 显示解释
            explanationBox.style.display = 'block';

            // 检查答案
            let isCorrect = false;
            if (questionNum === 1 && selectedValue === '1') {
                isCorrect = true;
            } else if (questionNum === 2 && selectedValue === 'B') {
                isCorrect = true;
            }

            // 更新反馈
            const feedback = document.getElementById('overallFeedback');
            if (isCorrect) {
                feedback.textContent = '🎉 答案正确！你已经理解了进程同步的基本概念！';
                feedback.className = 'feedback correct';
            } else {
                feedback.textContent = '🤔 答案不正确，请仔细阅读解释后再试一次！';
                feedback.className = 'feedback incorrect';
            }
            feedback.style.display = 'block';
        }

        // 流程模拟功能
        let flowSimulation = {
            currentStep: 0,
            isRunning: false
        };

        function simulateFlow() {
            if (flowSimulation.isRunning) return;

            flowSimulation.isRunning = true;
            flowSimulation.currentStep = 0;

            // 重置所有步骤
            document.querySelectorAll('.flow-step').forEach(step => {
                step.classList.remove('active');
            });

            // 开始模拟
            nextFlowStep();
        }

        function nextFlowStep() {
            if (flowSimulation.currentStep > 0) {
                // 移除前一步的高亮
                const prevStep = document.getElementById(`step${flowSimulation.currentStep}`);
                if (prevStep) {
                    prevStep.classList.remove('active');
                }
            }

            flowSimulation.currentStep++;

            if (flowSimulation.currentStep <= 5) {
                // 高亮当前步骤
                const currentStep = document.getElementById(`step${flowSimulation.currentStep}`);
                if (currentStep) {
                    currentStep.classList.add('active');
                }

                // 添加日志
                const stepMessages = {
                    1: '🚪 执行P(S)操作 - 申请进入临界区',
                    2: '📖 读取当前票数到临时变量',
                    3: '✏️ 计算新的票数（减去购买数量）',
                    4: '💾 将计算结果写回数据库',
                    5: '🔓 执行V(S)操作 - 释放临界区'
                };

                addLog(stepMessages[flowSimulation.currentStep], 'info');

                // 继续下一步
                setTimeout(nextFlowStep, 2000);
            } else {
                // 模拟完成
                flowSimulation.isRunning = false;
                addLog('✅ 流程模拟完成！', 'success');
            }
        }

        function resetFlow() {
            flowSimulation.isRunning = false;
            flowSimulation.currentStep = 0;

            // 移除所有高亮
            document.querySelectorAll('.flow-step').forEach(step => {
                step.classList.remove('active');
            });

            addLog('🔄 流程已重置', 'info');
        }

        // 事件监听器
        document.getElementById('addCustomer').addEventListener('click', addCustomer);
        document.getElementById('startSelling').addEventListener('click', startSelling);
        document.getElementById('pauseSelling').addEventListener('click', pauseSelling);
        document.getElementById('resetDemo').addEventListener('click', resetDemo);
        document.getElementById('simulateFlow').addEventListener('click', simulateFlow);
        document.getElementById('resetFlow').addEventListener('click', resetFlow);

        // 问题选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const question = this.getAttribute('data-question');
                const value = this.getAttribute('data-value');

                if (question) {
                    // 移除同一问题的其他选中状态
                    document.querySelectorAll(`[data-question="${question}"]`).forEach(opt => {
                        opt.classList.remove('selected');
                    });

                    // 选中当前选项
                    this.classList.add('selected');

                    // 处理答案
                    handleQuestionAnswer(parseInt(question), value);
                }
            });
        });

        // 初始化
        init();
        updateDisplay();
    </script>
</body>
</html> 