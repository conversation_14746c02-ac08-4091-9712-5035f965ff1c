<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>串行总线互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .bus-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        .device {
            width: 100px;
            height: 80px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .device:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .wire {
            height: 4px;
            background: #333;
            margin: 0 20px;
            position: relative;
            border-radius: 2px;
        }

        .data-bit {
            width: 20px;
            height: 20px;
            background: #ff6b6b;
            border-radius: 50%;
            position: absolute;
            top: -8px;
            animation: moveBit 3s linear infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        @keyframes moveBit {
            0% { left: 0; }
            100% { left: calc(100% - 20px); }
        }

        .control-panel {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .explanation {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .option {
            background: white;
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateX(10px);
        }

        .option.correct {
            border-color: #4caf50;
            background: #e8f5e8;
        }

        .option.wrong {
            border-color: #f44336;
            background: #ffebee;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚌 串行总线学习之旅</h1>
            <p class="subtitle">让我们一起探索数据传输的奥秘</p>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是串行总线？</h2>
            <div class="explanation">
                <strong>串行总线</strong>就像一条单车道的马路，数据像汽车一样一个接一个地排队通过。
                与之相对的是<strong>并行总线</strong>，就像多车道的高速公路，可以同时传输多个数据。
            </div>
            
            <div class="demo-area">
                <h3 style="text-align: center; margin-bottom: 20px;">🚗 串行传输演示</h3>
                <div class="bus-container">
                    <div class="device">发送端</div>
                    <div class="wire" style="width: 300px;">
                        <div class="data-bit">1</div>
                    </div>
                    <div class="device">接收端</div>
                </div>
                <p style="text-align: center; color: #666;">数据按位（bit）一个一个传输</p>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔄 全双工 vs 半双工</h2>
            <div class="canvas-container">
                <canvas id="duplexCanvas" width="600" height="300"></canvas>
            </div>
            <div class="control-panel">
                <button class="btn" onclick="showFullDuplex()">全双工演示</button>
                <button class="btn" onclick="showHalfDuplex()">半双工演示</button>
            </div>
            <div class="explanation">
                <strong>全双工：</strong>就像双向车道，可以同时发送和接收数据<br>
                <strong>半双工：</strong>就像单向车道，同一时间只能发送或接收
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚡ 波特率 - 数据传输速度</h2>
            <div class="demo-area">
                <h3 style="text-align: center; margin-bottom: 20px;">调整波特率看效果</h3>
                <div class="control-panel">
                    <button class="btn" onclick="setBaudRate(1)">慢速 (1 bps)</button>
                    <button class="btn" onclick="setBaudRate(5)">中速 (5 bps)</button>
                    <button class="btn" onclick="setBaudRate(10)">高速 (10 bps)</button>
                </div>
                <div class="bus-container">
                    <div class="device">发送端</div>
                    <div class="wire" style="width: 400px;" id="baudWire">
                        <div class="data-bit" id="baudBit">0</div>
                    </div>
                    <div class="device">接收端</div>
                </div>
                <p style="text-align: center; color: #666;" id="baudInfo">当前波特率: 1 bps</p>
            </div>
            <div class="explanation">
                <strong>波特率</strong>是每秒传输的位数。现代串口可以动态调整波特率，不是固定不变的！
                常见波特率：9600、19200、38400、115200 bps
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🛡️ 校验码 - 数据保护神</h2>
            <div class="canvas-container">
                <canvas id="parityCanvas" width="700" height="200"></canvas>
            </div>
            <div class="control-panel">
                <button class="btn" onclick="demonstrateParity()">演示校验过程</button>
                <button class="btn" onclick="simulateError()">模拟传输错误</button>
            </div>
            <div class="explanation">
                校验码就像数据的"保镖"，帮助检测传输过程中的错误。
                常见的有<strong>奇偶校验</strong>、<strong>CRC校验</strong>等。
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">💻 数据收发方式</h2>
            <div class="demo-area">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div style="text-align: center;">
                        <h4>🔍 查询方式</h4>
                        <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin: 10px 0;">
                            CPU不断询问："有数据吗？有数据吗？"
                        </div>
                        <button class="btn" onclick="demonstratePolling()">演示查询方式</button>
                    </div>
                    <div style="text-align: center;">
                        <h4>⚡ 中断方式</h4>
                        <div style="background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 10px 0;">
                            数据到达时主动通知CPU："我来了！"
                        </div>
                        <button class="btn" onclick="demonstrateInterrupt()">演示中断方式</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title" style="color: #333;">📝 现在来做题吧！</h2>
            <div style="background: white; padding: 30px; border-radius: 15px; margin: 20px 0;">
                <h3 style="margin-bottom: 20px;">以下关于串行总线的说法中，正确的是（ ）</h3>

                <div class="option" onclick="selectOption(this, false)">
                    <strong>A.</strong> 串行总线一般都是全双工总线，适宜于长距离传输数据
                    <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                        ❌ 错误：串行总线有全双工和半双工两种
                    </div>
                </div>

                <div class="option" onclick="selectOption(this, false)">
                    <strong>B.</strong> 串行总线传输的波特率是总线初始化时预先定义好的，使用中不可改变
                    <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                        ❌ 错误：波特率可以动态调整
                    </div>
                </div>

                <div class="option" onclick="selectOption(this, true)">
                    <strong>C.</strong> 串行总线是按位（bit）传输数据的，其数据的正确性依赖于校验码纠正
                    <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                        ✅ 正确：串行总线确实按位传输，校验码保证数据正确性
                    </div>
                </div>

                <div class="option" onclick="selectOption(this, false)">
                    <strong>D.</strong> 串行总线的数据发送和接收是以软件查询方式工作
                    <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                        ❌ 错误：可以用查询方式，也可以用中断方式
                    </div>
                </div>
            </div>

            <div id="result" style="text-align: center; margin-top: 30px; font-size: 1.2rem;"></div>
        </div>
    </div>

    <script>
        let currentBaudRate = 1;
        let animationId;

        // 全双工演示
        function showFullDuplex() {
            const canvas = document.getElementById('duplexCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制设备
            drawDevice(ctx, 50, 100, '设备A', '#4facfe');
            drawDevice(ctx, 450, 100, '设备B', '#00f2fe');

            // 绘制双向线路
            drawWire(ctx, 150, 120, 350, 120, '#333');
            drawWire(ctx, 150, 180, 350, 180, '#333');

            // 添加标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('发送线', 220, 110);
            ctx.fillText('接收线', 220, 200);

            // 动画数据传输
            animateDataFlow(ctx, 150, 120, 350, 120, '#ff6b6b', 0);
            animateDataFlow(ctx, 350, 180, 150, 180, '#4caf50', 1000);
        }

        // 半双工演示
        function showHalfDuplex() {
            const canvas = document.getElementById('duplexCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            drawDevice(ctx, 50, 125, '设备A', '#4facfe');
            drawDevice(ctx, 450, 125, '设备B', '#00f2fe');

            drawWire(ctx, 150, 150, 350, 150, '#333');

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('共享线路', 220, 140);

            // 交替传输
            animateDataFlow(ctx, 150, 150, 350, 150, '#ff6b6b', 0);
            setTimeout(() => {
                animateDataFlow(ctx, 350, 150, 150, 150, '#4caf50', 0);
            }, 2000);
        }

        function drawDevice(ctx, x, y, text, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 80, 50);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + 40, y + 30);
            ctx.textAlign = 'left';
        }

        function drawWire(ctx, x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }

        function animateDataFlow(ctx, startX, startY, endX, endY, color, delay) {
            setTimeout(() => {
                let x = startX;
                const speed = 2;
                const animate = () => {
                    // 清除之前的点
                    ctx.clearRect(x - 15, startY - 15, 30, 30);
                    drawWire(ctx, startX, startY, endX, endY, '#333');

                    x += speed;
                    if (x <= endX) {
                        // 绘制数据点
                        ctx.fillStyle = color;
                        ctx.beginPath();
                        ctx.arc(x, startY, 8, 0, 2 * Math.PI);
                        ctx.fill();
                        requestAnimationFrame(animate);
                    }
                };
                animate();
            }, delay);
        }

        // 波特率演示
        function setBaudRate(rate) {
            currentBaudRate = rate;
            document.getElementById('baudInfo').textContent = `当前波特率: ${rate} bps`;

            if (animationId) {
                clearInterval(animationId);
            }

            const bit = document.getElementById('baudBit');
            let position = 0;
            const maxPosition = 380;

            animationId = setInterval(() => {
                position += 5;
                if (position > maxPosition) {
                    position = 0;
                    bit.textContent = Math.random() > 0.5 ? '1' : '0';
                }
                bit.style.left = position + 'px';
            }, 1000 / rate / 5);
        }

        // 校验演示
        function demonstrateParity() {
            const canvas = document.getElementById('parityCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const data = '1011001';
            let x = 50;

            ctx.font = '20px Arial';
            ctx.fillStyle = '#333';
            ctx.fillText('原始数据: ', 50, 50);

            // 绘制数据位
            for (let i = 0; i < data.length; i++) {
                ctx.fillStyle = data[i] === '1' ? '#4caf50' : '#f44336';
                ctx.fillRect(x + 150 + i * 40, 30, 30, 30);
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.fillText(data[i], x + 165 + i * 40, 50);
            }

            // 计算校验位
            const ones = data.split('1').length - 1;
            const parity = ones % 2;

            ctx.fillStyle = '#ff9800';
            ctx.fillRect(x + 150 + data.length * 40 + 20, 30, 30, 30);
            ctx.fillStyle = 'white';
            ctx.fillText(parity.toString(), x + 165 + data.length * 40 + 20, 50);

            ctx.textAlign = 'left';
            ctx.fillStyle = '#333';
            ctx.fillText('校验位', x + 150 + data.length * 40 + 60, 50);

            ctx.fillText(`1的个数: ${ones} (${ones % 2 === 0 ? '偶数' : '奇数'})`, 50, 100);
            ctx.fillText('奇偶校验位: ' + parity, 50, 130);
        }

        function simulateError() {
            const canvas = document.getElementById('parityCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.font = '20px Arial';
            ctx.fillStyle = '#f44336';
            ctx.fillText('❌ 检测到传输错误！', 50, 50);
            ctx.fillText('校验码不匹配，数据可能损坏', 50, 100);

            // 闪烁效果
            let flash = true;
            const flashInterval = setInterval(() => {
                ctx.fillStyle = flash ? '#f44336' : '#fff';
                ctx.fillRect(40, 20, 400, 100);
                ctx.fillStyle = flash ? '#fff' : '#f44336';
                ctx.fillText('❌ 检测到传输错误！', 50, 50);
                ctx.fillText('校验码不匹配，数据可能损坏', 50, 100);
                flash = !flash;
            }, 500);

            setTimeout(() => clearInterval(flashInterval), 3000);
        }

        // 查询和中断演示
        function demonstratePolling() {
            alert('🔍 查询方式演示：\nCPU: "有数据吗？"\n串口: "没有..."\nCPU: "有数据吗？"\n串口: "没有..."\n(持续询问，占用CPU资源)');
        }

        function demonstrateInterrupt() {
            alert('⚡ 中断方式演示：\nCPU: 忙其他工作...\n串口: "数据来了！触发中断！"\nCPU: 立即处理数据\n(高效，不浪费CPU资源)');
        }

        // 题目选择
        function selectOption(element, isCorrect) {
            // 清除所有选项的样式
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 添加对应样式
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('result').innerHTML = '🎉 恭喜你答对了！<br>串行总线确实是按位传输，校验码保证数据正确性！';
                document.getElementById('result').style.color = '#4caf50';
            } else {
                element.classList.add('wrong');
                document.getElementById('result').innerHTML = '❌ 再想想看，回顾一下上面的知识点！';
                document.getElementById('result').style.color = '#f44336';
            }
        }

        // 初始化
        window.onload = function() {
            setBaudRate(1);
            showFullDuplex();
        };
    </script>
</body>
</html>
