{"name": "my-vue2-project", "version": "0.1.0", "description": "A Vue 2 desktop application", "author": "Your Name", "private": true, "scripts": {"prebuild": "node ./scripts/generate-menu.js", "preserve": "node ./scripts/generate-menu.js", "serve": "node ./scripts/generate-menu.js && vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "electron:serve": "concurrently \"npm run serve\" \"wait-on http://localhost:8080 && electron .\"", "electron:build": "npm run build && electron-builder", "electron:pack": "npm run build && npx electron-packager . MyVue2Project --platform=win32 --arch=x64 --out=dist-electron --overwrite", "electron:pack-all": "npm run build && npx electron-packager . MyVue2Project --all --out=dist-electron --overwrite"}, "main": "electron-main.js", "build": {"appId": "com.example.myvue2project", "productName": "MyVue2Project", "directories": {"output": "electron-build"}, "files": ["dist/**/*", "electron-main.js", "package.json"]}, "dependencies": {"core-js": "^3.8.3", "echarts": "^5.6.0", "element-ui": "^2.15.14", "vue": "^2.6.14", "vue-echarts": "^7.0.3", "vue-router": "^3.6.5", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "concurrently": "^9.2.0", "electron": "^37.2.1", "electron-builder": "^26.0.12", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "node-polyfill-webpack-plugin": "^4.1.0", "vue-template-compiler": "^2.6.14", "wait-on": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}