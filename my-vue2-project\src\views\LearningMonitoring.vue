<template>
  <div class="learning-monitoring-layout">
    <PageHeader v-if="!isMonitoring">
      <template #actions>
        <TextButton type="default" icon="el-icon-s-home" @click="goHome">返回首页</TextButton>
        <TextButton type="default" icon="el-icon-notebook-1" @click="goToNotes">我的笔记</TextButton>
      </template>
    </PageHeader>

    <div class="container">
      <div class="dashboard-header fade-in" :class="{'monitoring-active-header': isMonitoring}">
        <h2 v-if="!isMonitoring" class="dashboard-title">智能学习助手</h2>
        <p v-if="!isMonitoring" class="dashboard-subtitle">{{ isMonitoring ? '正在实时监控您的学习状态...' : '点击开始监控，洞察学习效率' }}</p>
        <el-button 
          :type="isMonitoring ? 'danger' : 'primary'"
          :icon="isMonitoring ? 'el-icon-loading' : 'el-icon-video-play'"
          round
          class="control-button"
          @click="toggleMonitoring"
        >
          {{ isMonitoring ? '停止监控' : '开始监控' }}
        </el-button>
      </div>

      <transition name="fade" mode="out-in">
        <div v-if="isMonitoring" class="monitoring-active fade-in" key="active">
          <div class="summary-section">
            <div class="summary-card">
              <div class="card-icon"><i class="el-icon-data-board"></i></div>
              <h3 class="card-title">当前状态</h3>
              <p class="summary-text">{{ currentActivity }}</p>
            </div>
            <div class="summary-card">
              <div class="card-icon"><i class="el-icon-medal"></i></div>
              <h3 class="card-title">专注度</h3>
              <el-progress :percentage="focusLevel" :stroke-width="10" :show-text="false"></el-progress>
              <p class="summary-value">{{ focusLevel }}%</p>
            </div>
            <div class="summary-card">
              <div class="card-icon"><i class="el-icon-user"></i></div>
              <h3 class="card-title">情绪状态</h3>
              <p class="summary-text">{{ emotionalState }}</p>
            </div>
            <div class="summary-card">
              <div class="card-icon"><i class="el-icon-time"></i></div>
              <h3 class="card-title">学习时长</h3>
              <p class="summary-value">{{ learningDuration }} 小时</p>
            </div>
            <div class="summary-card">
              <div class="card-icon"><i class="el-icon-upload"></i></div>
              <h3 class="card-title">学习效率</h3>
              <p class="summary-value">{{ learningEfficiency }} %</p>
            </div>
          </div>

          <div class="detail-sections">
            <div class="detail-card fade-in" style="animation-delay: 0.1s;">
              <h4 class="card-title">面部表情与疲劳度</h4>
              <p class="detail-text"><i class="el-icon-video-camera"></i> 摄像头监控：{{ faceExpression }}</p>
              <p class="detail-text"><i class="el-icon-coffee-cup"></i> 疲劳度：{{ fatigueLevel }}</p>
            </div>

            <div class="detail-card fade-in" style="animation-delay: 0.2s;">
              <h4 class="card-title">眼动与注意力分布</h4>
              <p class="detail-text"><i class="el-icon-view"></i> 视线停留：{{ eyeGaze }}</p>
              <p class="detail-text"><i class="el-icon-full-screen"></i> 正在查看屏幕：{{ activeScreen }}</p>
            </div>

            <div class="detail-card fade-in" style="animation-delay: 0.3s;">
              <h4 class="card-title">鼠标与键盘活动</h4>
              <p class="detail-text"><i class="el-icon-mouse"></i> 鼠标：{{ mouseActivity }}</p>
              <p class="detail-text"><i class="el-icon-keyboard"></i> 键盘：{{ keyboardActivity }}</p>
            </div>
            
            <div class="detail-card fade-in" style="animation-delay: 0.4s;">
              <h4 class="card-title">环境与生理信号</h4>
              <p class="detail-text"><i class="el-icon-mic"></i> 环境噪声：{{ environmentNoise }}</p>
              <p class="detail-text"><i class="el-icon-sunny"></i> 光线：{{ lightCondition }}</p>
              <p class="detail-text"><i class="el-icon-heart"></i> 心率：{{ heartRate }} bpm</p>
            </div>
            
            <div class="detail-card fade-in" style="animation-delay: 0.5s;">
              <h4 class="card-title">应用程序与网站使用</h4>
              <p class="detail-text"><i class="el-icon-monitor"></i> 当前应用：{{ currentApplication }}</p>
              <p class="detail-text"><i class="el-icon-link"></i> 活跃网站：{{ activeWebsite }}</p>
            </div>
          </div>

          <div class="chart-section fade-in" style="animation-delay: 0.6s;">
            <div class="chart-card">
              <h3 class="card-title">专注度趋势</h3>
              <v-chart class="chart" :option="focusChartOption" autoresize />
            </div>
            <div class="chart-card">
              <h3 class="card-title">学习活动分布</h3>
              <v-chart class="chart" :option="activityChartOption" autoresize />
            </div>
          </div>

          <div class="event-log-section fade-in" style="animation-delay: 0.7s;">
            <h3 class="card-title">行为日志</h3>
            <div class="event-log-list">
              <div v-for="(event, index) in eventLog" :key="index" class="log-item">
                <span class="log-time">[{{ event.time }}]</span>
                <span class="log-description">{{ event.description }}</span>
              </div>
            </div>
          </div>

          <div class="recommendation-section fade-in" style="animation-delay: 0.8s;">
            <h3 class="card-title">个性化建议</h3>
            <ul class="recommendation-list">
              <li v-for="(rec, index) in recommendations" :key="index">
                <i class="el-icon-caret-right"></i> {{ rec }}
              </li>
            </ul>
          </div>

        </div>

        <div v-else class="monitoring-inactive fade-in" key="inactive">
          <el-empty description="未开始监控" :image-size="180">
            <p class="empty-text">点击上方按钮开始实时学习行为监控</p>
          </el-empty>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import VChart from 'vue-echarts';
import { use } from 'echarts/core';
import { LineChart, PieChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { mapMutations } from 'vuex';
import applicationMonitor from '@/services/applicationMonitor';

use([
  CanvasRenderer,
  LineChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent
]);

export default {
  name: 'LearningMonitoring',
  components: {
    VChart
  },
  data() {
    return {
      isMonitoring: false,
      monitorInterval: null,
      currentActivity: '未开始',
      focusLevel: 0,
      emotionalState: '中性',
      faceExpression: '未检测到',
      fatigueLevel: '未检测到',
      eyeGaze: '未检测到',
      activeScreen: '未检测到',
      mouseActivity: '静止',
      keyboardActivity: '无输入',
      environmentNoise: '未检测到',
      lightCondition: '未检测到',
      heartRate: 0,
      learningDuration: 0,
      learningEfficiency: 0,
      currentApplication: '无',
      activeWebsite: '无',
      eventLog: [],
      recommendations: [],

      // 图表数据和配置
      focusTrendData: [],
      focusTrendTime: [],
      activityCounts: {
        '阅读': 0,
        '编程': 0,
        '课程': 0,
        '笔记': 0,
        '规划': 0,
        '浏览': 0,
        '音乐': 0,
        '休息': 0,
        '多任务': 0,
        '题目': 0,
        '历史': 0
      },
      
      focusChartOption: {},
      activityChartOption: {}
    };
  },
  methods: {
    ...mapMutations(['setMonitoringMode']),
    goHome() {
      this.$router.push('/');
    },
    goToNotes() {
      this.$router.push('/notes');
    },
    toggleMonitoring() {
      if (this.isMonitoring) {
        this.stopMonitoring();
      } else {
        this.startMonitoring();
      }
    },
    startMonitoring() {
      this.isMonitoring = true;
      // 设置全局监控模式，使其他模块变灰
      this.setMonitoringMode(true);
      this.eventLog = []; // Clear log on start
      this.recommendations = []; // Clear recommendations
      this.learningDuration = 0;
      this.learningEfficiency = 0;
      this.focusTrendData = []; // Clear chart data
      this.focusTrendTime = []; // Clear chart time
      this.activityCounts = { // Reset activity counts
        '阅读': 0, '编程': 0, '课程': 0, '笔记': 0, '规划': 0, '浏览': 0,
        '音乐': 0, '休息': 0, '多任务': 0, '题目': 0, '历史': 0
      };
      this.addLog('系统', '开始学习行为监控。');
      this.initCharts(); // Initialize charts when monitoring starts
      this.startApplicationMonitoring(); // 开始应用程序监控

      this.monitorInterval = setInterval(() => {
        this.simulateData();
        this.monitorApplications(); // 监控应用程序使用
      }, 3000); // Update every 3 seconds
    },
    stopMonitoring() {
      this.isMonitoring = false;
      // 取消全局监控模式，恢复其他模块正常状态
      this.setMonitoringMode(false);
      clearInterval(this.monitorInterval);
      this.stopApplicationMonitoring(); // 停止应用程序监控
      this.addLog('系统', '停止学习行为监控。');
      this.currentActivity = '已停止';
      this.focusLevel = 0;
      this.emotionalState = '中性';
      this.faceExpression = '未检测到';
      this.fatigueLevel = '未检测到';
      this.eyeGaze = '未检测到';
      this.activeScreen = '未检测到';
      this.mouseActivity = '静止';
      this.keyboardActivity = '无输入';
      this.environmentNoise = '未检测到';
      this.lightCondition = '未检测到';
      this.heartRate = 0;
      this.learningDuration = 0;
      this.learningEfficiency = 0;
      this.currentApplication = '无';
      this.activeWebsite = '无';
    },
    addLog(source, description) {
      const now = new Date();
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
      this.eventLog.unshift({ time, description: `${source}: ${description}` });
      if (this.eventLog.length > 50) { // Keep log concise
        this.eventLog.pop();
      }
    },
    addRecommendation(text) {
      if (!this.recommendations.includes(text)) {
        this.recommendations.unshift(text);
        if (this.recommendations.length > 5) { // Keep recommendations concise
          this.recommendations.pop();
        }
      }
    },
    initCharts() {
      // 专注度折线图配置
      this.focusChartOption = {
        tooltip: {
          trigger: 'axis',
          formatter: '{b}<br/>专注度: {c}%'
        },
        xAxis: {
          type: 'category',
          data: this.focusTrendTime,
          axisLabel: {
            color: 'var(--text-light)'
          }
        },
        yAxis: {
          type: 'value',
          name: '专注度 (%)',
          min: 0,
          max: 100,
          axisLabel: {
            formatter: '{value} %',
            color: 'var(--text-light)'
          },
          splitLine: {
            lineStyle: {
              color: 'var(--border-color-light)'
            }
          }
        },
        series: [
          {
            name: '专注度',
            type: 'line',
            data: this.focusTrendData,
            smooth: true,
            lineStyle: {
              width: 3,
              color: 'var(--primary-color)'
            },
            itemStyle: {
              color: 'var(--primary-color)'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(64, 158, 255, 0.3)' // Placeholder: Semi-transparent primary color
                }, {
                  offset: 1, color: 'transparent' // Fully transparent
                }],
                global: false
              }
            }
          }
        ],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        }
      };

      // 学习活动分布饼图配置
      this.activityChartOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          textStyle: {
            color: 'var(--text-dark)'
          }
        },
        series: [
          {
            name: '活动分布',
            type: 'pie',
            radius: '60%',
            data: Object.keys(this.activityCounts).map(key => ({
              name: key,
              value: this.activityCounts[key]
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              color: 'var(--text-dark)'
            }
          }
        ]
      };
    },
    simulateData() {
      // --- 模拟当前活动 --- 
      const activities = [
        '正在阅读技术文档：Vue Router 深度解析',
        '正在编写代码：实现前端组件',
        '正在观看在线课程：算法基础',
        '正在整理笔记：知识点归纳',
        '正在进行项目规划',
        '正在浏览相关论坛',
        '正在听学习音乐',
        '正在休息中...',
        '正在一边学习Vue一边查看邮件',
        '正在学习编程题目：数组去重',
        '正在学习历史文献：清朝对外政策'
      ];
      this.currentActivity = activities[Math.floor(Math.random() * activities.length)];

      // 更新活动分布数据
      if (this.currentActivity.includes('阅读')) this.activityCounts['阅读']++;
      else if (this.currentActivity.includes('编写代码')) this.activityCounts['编程']++;
      else if (this.currentActivity.includes('观看在线课程')) this.activityCounts['课程']++;
      else if (this.currentActivity.includes('整理笔记')) this.activityCounts['笔记']++;
      else if (this.currentActivity.includes('项目规划')) this.activityCounts['规划']++;
      else if (this.currentActivity.includes('浏览')) this.activityCounts['浏览']++;
      else if (this.currentActivity.includes('听学习音乐')) this.activityCounts['音乐']++;
      else if (this.currentActivity.includes('休息')) this.activityCounts['休息']++;
      else if (this.currentActivity.includes('一边学习Vue一边查看邮件')) this.activityCounts['多任务']++;
      else if (this.currentActivity.includes('题目')) this.activityCounts['题目']++;
      else if (this.currentActivity.includes('历史文献')) this.activityCounts['历史']++;
      else this.activityCounts['其他'] = (this.activityCounts['其他'] || 0) + 1; // Fallback for unmatched activities
      
      // --- 模拟专注度 --- 
      const prevFocus = this.focusLevel;
      if (this.currentActivity.includes('休息') || this.currentActivity.includes('邮件')) {
        this.focusLevel = Math.max(0, this.focusLevel - Math.floor(Math.random() * 10) - 5);
      } else if (this.currentActivity.includes('游戏')) {
        this.focusLevel = Math.min(100, this.focusLevel + Math.floor(Math.random() * 15) + 10);
      } else {
        this.focusLevel = Math.min(100, this.focusLevel + Math.floor(Math.random() * 20));
      }

      // 模拟专注度变化事件
      if (this.focusLevel < 30 && prevFocus >= 30) {
        this.addLog('系统警报', '专注度显著下降，建议调整状态。');
        this.addRecommendation('建议休息5-10分钟，放松眼睛和身体。');
      } else if (this.focusLevel > 70 && prevFocus <= 70) {
        this.addLog('系统提示', '专注度较高，学习效率良好。');
      } else if (this.focusLevel < 10 && prevFocus >= 10) {
        this.addLog('严重分心', '专注度极低，系统判断您可能已严重分心。通知。');
        this.addRecommendation('请立即中断干扰，重新回到学习状态。');
      }

      // 更新专注度趋势图数据
      const now = new Date();
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
      this.focusTrendTime.push(time);
      this.focusTrendData.push(this.focusLevel);
      
      // 保持图表数据量在一定范围内，避免内存溢出
      const maxDataPoints = 20;
      if (this.focusTrendTime.length > maxDataPoints) {
        this.focusTrendTime.shift();
        this.focusTrendData.shift();
      }
      
      // 更新图表选项以触发 ECharts 重新渲染
      this.focusChartOption = { ...this.focusChartOption,
        xAxis: { ...this.focusChartOption.xAxis, data: this.focusTrendTime },
        series: [{ ...this.focusChartOption.series[0], data: this.focusTrendData }]
      };
      this.activityChartOption = { ...this.activityChartOption,
        series: [{
          ...this.activityChartOption.series[0],
          data: Object.keys(this.activityCounts).map(key => ({
            name: key,
            value: this.activityCounts[key]
          }))
        }]
      };

      // --- 模拟情绪状态 --- 
      const emotionalStates = ['中性', '轻松', '疑惑', '略微紧张', '愉悦', '疲惫', '烦躁', '兴奋'];
      this.emotionalState = emotionalStates[Math.floor(Math.random() * emotionalStates.length)];
      
      if (this.emotionalState === '疲惫' && Math.random() < 0.3) {
        this.addLog('情绪检测', '检测到疲劳迹象，建议适当休息。');
        this.addRecommendation('长时间学习可能导致疲劳，起来活动一下或闭目养神。');
      } else if (this.emotionalState === '疑惑' && Math.random() < 0.2) {
        this.addLog('情绪检测', '检测到疑惑表情，可能遇到学习难点。');
        this.addRecommendation('尝试回顾基础知识或查阅相关资料，必要时寻求帮助。');
      } else if (this.emotionalState === '烦躁' && Math.random() < 0.4) {
        this.addLog('情绪检测', '检测到烦躁情绪，可能对当前学习内容感到不适。');
        this.addRecommendation('尝试更换学习内容或稍作放松，调整心态。');
      }

      // --- 模拟面部表情与疲劳度 --- 
      const expressions = ['专注', '中性', '微皱眉', '微笑', '打哈欠', '困倦', '思考中', '面无表情'];
      this.faceExpression = expressions[Math.floor(Math.random() * expressions.length)];
      this.fatigueLevel = Math.random() > 0.7 ? '重度疲劳' : (Math.random() > 0.4 ? '中度疲劳' : '轻度疲劳');
      if (this.fatigueLevel === '重度疲劳' && Math.random() < 0.5) {
        this.addLog('疲劳检测', `检测到${this.fatigueLevel}，建议立即休息。`);
        this.addRecommendation('身体是革命的本钱，强制休息以避免过度劳累。');
      }

      // --- 模拟眼动与注意力分布 --- 
      const eyeGazes = [
        '正在聚焦屏幕中央',
        '视线快速扫动',
        '视线长时间停留于屏幕左上角 (可能是通知区域)',
        '视线集中于主屏幕文本区域',
        '视线频繁在不同屏幕间切换',
        '视线游离，无明显焦点'
      ];
      this.eyeGaze = eyeGazes[Math.floor(Math.random() * eyeGazes.length)];

      const screens = [
        '主屏幕 (学习应用)',
        '副屏幕 (参考资料)',
        '副屏幕 (社交媒体/视频网站)',
        '主屏幕 (游戏应用)'
      ];
      this.activeScreen = screens[Math.floor(Math.random() * screens.length)];

      if (this.activeScreen.includes('游戏') && Math.random() < 0.6) {
        this.addLog('多任务警报', `检测到您正在查看游戏应用，专注度可能受到影响。`);
        this.addRecommendation('学习和娱乐应分开进行，请关闭游戏应用专注于学习。');
      } else if (this.activeScreen.includes('社交媒体') && Math.random() < 0.5) {
        this.addLog('多任务警报', `检测到您正在查看社交媒体/视频网站，请注意学习效率。`);
        this.addRecommendation('为了高效学习，建议暂时关闭社交媒体或视频网站。');
      } else if (this.eyeGaze.includes('频繁在不同屏幕间切换') && Math.random() < 0.3) {
        this.addLog('行为分析', '多屏幕切换频繁，可能在进行多任务处理。');
        this.addRecommendation('多任务处理会显著降低学习效率，建议一次只专注于一项任务。');
      }

      // --- 模拟鼠标与键盘活动 --- 
      const mouseActivities = [
        '静止 (无操作)',
        '缓慢移动 (阅读中)',
        '快速滑动 (浏览页面)',
        '点击频繁 (交互中)',
        '选中文字并复制 (查阅资料)',
        '拖拽文件 (整理文件)'
      ];
      this.mouseActivity = mouseActivities[Math.floor(Math.random() * mouseActivities.length)];

      const keyboardActivities = [
        '无输入',
        '稳定输入中 (打字)',
        '输入暂停 (思考中)',
        '快速输入并回车 (搜索/提交)',
        '使用快捷键 (效率操作)',
        '频繁删除/修改 (内容调整)'
      ];
      this.keyboardActivity = keyboardActivities[Math.floor(Math.random() * keyboardActivities.length)];

      if (this.mouseActivity.includes('拖拽文件') && this.keyboardActivity.includes('无输入') && Math.random() < 0.2) {
        this.addLog('行为分析', '检测到文件整理行为，可能与学习任务相关，但也可能分散注意力。');
        this.addRecommendation('建议将文件整理等辅助性工作集中处理，避免打断学习流。');
      } else if (this.mouseActivity.includes('静止') && this.keyboardActivity.includes('无输入') && this.focusLevel > 50 && Math.random() < 0.1) {
        this.addLog('行为分析', '长时间无操作但专注度高，可能在深度思考。');
      }

      // --- 模拟环境与生理信号 --- 
      const noises = ['安静 (25dB)', '轻微背景音 (40dB)', '有对话声 (60dB)', '嘈杂 (80dB)'];
      this.environmentNoise = noises[Math.floor(Math.random() * noises.length)];

      const lights = ['适中 (300 Lux)', '偏亮 (500 Lux)', '偏暗 (100 Lux)', '刺眼 (800 Lux)'];
      this.lightCondition = lights[Math.floor(Math.random() * lights.length)];

      this.heartRate = 60 + Math.floor(Math.random() * 40); // 60-100 bpm
      if (this.heartRate > 90 && Math.random() < 0.3) {
        this.addLog('生理指标', `心率 ${this.heartRate} bpm，可能情绪紧张或激动。`);
        this.addRecommendation('尝试深呼吸、短暂闭目或听轻柔音乐，平复心情。');
      }

      // --- 模拟学习时长与效率 ---
      if (this.isMonitoring) {
        this.learningDuration = parseFloat((this.learningDuration + (3 / 3600)).toFixed(2)); // 每3秒增加1/120小时
        this.learningEfficiency = Math.min(100, Math.max(0, this.focusLevel + (Math.random() * 20 - 10))).toFixed(0); // 基于专注度浮动
        if (this.learningEfficiency < 30 && Math.random() < 0.1) {
          this.addLog('效率警报', '学习效率较低，可能需要调整学习方法。');
          this.addRecommendation('尝试番茄工作法或思维导图，提高学习效率。');
        }
      }

      // --- 模拟应用程序与网站使用 ---
      const applications = ['Visual Studio Code', 'Chrome 浏览器', 'Typora', 'Obsidian', 'Microsoft Word', 'Steam'];
      const websites = ['Vue.js 官网', 'MDN Web Docs', 'GitHub', 'Bilibili', '知乎', 'Stack Overflow'];
      this.currentApplication = applications[Math.floor(Math.random() * applications.length)];
      this.activeWebsite = websites[Math.floor(Math.random() * websites.length)];

      if (this.currentApplication === 'Steam' && Math.random() < 0.7) {
        this.addLog('应用监控', `检测到您正在使用 ${this.currentApplication}，请注意学习任务。`);
        this.addRecommendation('游戏时间会严重影响学习专注，建议在完成学习任务后再放松。');
      } else if (this.activeWebsite === 'Bilibili' && Math.random() < 0.6) {
        this.addLog('网站监控', `检测到您正在访问 ${this.activeWebsite}，请注意学习效率。`);
        this.addRecommendation('短视频平台容易分散注意力，建议利用碎片时间而非学习时间观看。');
      }

      // 模拟学习题目（如果适用）
      if (this.currentActivity.includes('题目')) {
        const topic = this.currentActivity.split('：')[1];
        this.addLog('学习内容', `正在攻克题目：${topic}`);
        if (this.emotionalState === '疑惑' && Math.random() < 0.3) {
          this.addRecommendation(`关于${topic}，建议查阅相关例题或概念，加强理解。`);
        }
      }
    },

    // 开始应用程序监控
    startApplicationMonitoring() {
      this.addLog('应用监控', '开始监控应用程序和网站使用情况。');

      // 注册应用变化回调
      applicationMonitor.onAppChange(this.handleApplicationChange);
      applicationMonitor.onWebsiteChange(this.handleWebsiteChange);

      // 开始监控
      applicationMonitor.startMonitoring();
    },

    // 停止应用程序监控
    stopApplicationMonitoring() {
      this.addLog('应用监控', '停止监控应用程序和网站使用情况。');

      // 停止监控
      applicationMonitor.stopMonitoring();

      // 移除回调
      applicationMonitor.removeCallback('onAppChange', this.handleApplicationChange);
      applicationMonitor.removeCallback('onWebsiteChange', this.handleWebsiteChange);
    },

    // 监控应用程序使用（实时检测）
    monitorApplications() {
      // 获取当前应用和网站信息
      const currentApp = applicationMonitor.getCurrentApp();
      const currentWebsite = applicationMonitor.getCurrentWebsite();

      if (currentApp) {
        this.currentApplication = currentApp.name || '未知应用';
      }

      if (currentWebsite) {
        this.activeWebsite = currentWebsite.domain || currentWebsite.title || '未知网站';
      }
    },

    // 处理应用程序变化
    handleApplicationChange(appInfo) {
      const { name, title } = appInfo;
      this.currentApplication = name || '未知应用';

      this.addLog('应用监控', `切换到应用: ${this.currentApplication}${title ? ` - ${title}` : ''}`);

      // 根据应用类型给出建议
      this.analyzeApplicationUsage(name);
    },

    // 处理网站变化
    handleWebsiteChange(websiteInfo) {
      const { url, title, domain } = websiteInfo;
      this.activeWebsite = domain || title || '未知网站';

      this.addLog('网站监控', `访问网站: ${this.activeWebsite}`);

      // 根据网站类型给出建议
      this.analyzeWebsiteUsage(url, domain, title);
    },

    // 分析应用程序使用情况
    analyzeApplicationUsage(appName) {
      const learningApps = ['Visual Studio Code', 'IntelliJ IDEA', 'PyCharm', 'Sublime Text', 'Atom', 'Notepad++', 'Typora', 'Obsidian', 'Notion'];
      const distractingApps = ['Steam', 'WeChat', 'QQ', 'TikTok', 'Douyin', 'NetEase Music', 'QQ Music'];
      const browserApps = ['Chrome', 'Firefox', 'Edge', 'Safari'];

      if (learningApps.some(app => appName.includes(app))) {
        this.addLog('应用分析', `检测到学习相关应用: ${appName}，专注度可能提升。`);
        if (Math.random() < 0.3) {
          this.addRecommendation('正在使用学习工具，保持专注，合理安排休息时间。');
        }
      } else if (distractingApps.some(app => appName.includes(app))) {
        this.addLog('应用警告', `检测到娱乐应用: ${appName}，可能影响学习专注度。`);
        this.addRecommendation(`建议关闭 ${appName}，专注于学习任务。娱乐可以安排在学习间隙。`);
      } else if (browserApps.some(app => appName.includes(app))) {
        this.addLog('应用监控', `检测到浏览器: ${appName}，请注意浏览内容。`);
        if (Math.random() < 0.4) {
          this.addRecommendation('使用浏览器时，建议专注于学习相关内容，避免被无关信息分散注意力。');
        }
      }
    },

    // 分析网站使用情况
    analyzeWebsiteUsage(url, domain, title) {
      const learningDomains = ['github.com', 'stackoverflow.com', 'developer.mozilla.org', 'w3schools.com', 'coursera.org', 'edx.org', 'khan academy'];
      const socialDomains = ['facebook.com', 'twitter.com', 'instagram.com', 'weibo.com', 'zhihu.com'];
      const videoDomains = ['youtube.com', 'bilibili.com', 'iqiyi.com', 'youku.com', 'netflix.com'];
      const shoppingDomains = ['taobao.com', 'tmall.com', 'jd.com', 'amazon.com'];

      if (learningDomains.some(d => domain && domain.includes(d))) {
        this.addLog('网站分析', `访问学习网站: ${domain}，有助于知识获取。`);
        if (Math.random() < 0.2) {
          this.addRecommendation('正在浏览学习资源，建议做好笔记记录重要内容。');
        }
      } else if (socialDomains.some(d => domain && domain.includes(d))) {
        this.addLog('网站警告', `访问社交网站: ${domain}，可能分散学习注意力。`);
        this.addRecommendation(`建议减少在 ${domain} 的浏览时间，专注于学习任务。`);
      } else if (videoDomains.some(d => domain && domain.includes(d))) {
        this.addLog('网站警告', `访问视频网站: ${domain}，请注意时间管理。`);
        this.addRecommendation(`视频内容容易让人沉迷，建议设置观看时间限制。`);
      } else if (shoppingDomains.some(d => domain && domain.includes(d))) {
        this.addLog('网站提醒', `访问购物网站: ${domain}，请注意时间分配。`);
        this.addRecommendation('购物浏览会占用学习时间，建议安排在学习任务完成后。');
      } else if (url && title) {
        // 使用url和title参数避免警告
        console.log(`访问网站: ${url}, 标题: ${title}`);
      }
    }
  },
  beforeDestroy() {
    this.stopMonitoring();
  }
};
</script>

<style scoped>
.learning-monitoring-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.container {
  flex-grow: 1;
  max-width: 1600px; /* 增加最大宽度，提供更多留白 */
  margin: 0 auto;
  padding: 40px 30px 60px;
  display: flex;
  flex-direction: column;
  gap: 40px; /* 增加整体间距 */
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px; /* 减少与下方内容的距离，增加留白 */
  animation: fadeInUp 0.7s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}

.dashboard-header.monitoring-active-header {
  margin-bottom: 0; /* 监控开启时减少顶部空间 */
}

.dashboard-title {
  font-size: 48px; /* 增大标题字体 */
  font-weight: 600; /* 加粗标题 */
  margin-bottom: 15px; /* 增加标题与副标题间距 */
  letter-spacing: -1.5px; /* 调整字间距 */
  color: var(--primary-color);
}

.dashboard-subtitle {
  font-size: 20px; /* 增大副标题字体 */
  color: var(--text-light);
  font-weight: 300;
  margin-bottom: 40px; /* 增加副标题与按钮间距 */
}

.control-button {
  font-size: 18px; /* 增大按钮字体 */
  padding: 14px 35px; /* 增加按钮内边距 */
  border-radius: 30px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08); /* 调整阴影 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: var(--primary-color); /* 确保主色调一致 */
  color: #ffffff;
  border: none;
}

.control-button:hover {
  transform: translateY(-5px); /* 增加悬停位移 */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15); /* 增加悬停阴影 */
}

.monitoring-active {
  display: flex;
  flex-direction: column;
  gap: 80px; /* 增加不同监控区域的间距，提升留白 */
}

.summary-section {
  display: grid;
  grid-template-columns: repeat(5, 1fr); /* 调整为每行显示5个 */
  gap: 40px; /* 概览卡片间距保持不变，或根据实际效果再调整 */
}

.summary-card {
  background-color: var(--card-bg);
  border-radius: 20px; /* 增大圆角 */
  padding: 40px; /* 统一增加内边距 */
  text-align: center;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.05); /* 调整阴影 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 优化过渡 */
  border: 1px solid var(--border-color-light); /* 添加细边框 */
}

.summary-card:hover {
  transform: translateY(-8px); /* 增加悬停位移 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08); /* 增加悬停阴影 */
  border-color: var(--primary-color-translucent); /* 悬停时边框颜色变化 */
}

.summary-card .card-icon {
  width: 60px; /* 增大图标背景尺寸 */
  height: 60px;
  background-color: var(--primary-color-light-translucent); /* 确保半透明效果 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px; /* 调整图标与标题间距 */
}

.summary-card .card-icon i {
  font-size: 30px; /* 增大图标字体 */
  color: var(--primary-color);
}

.summary-card .card-title {
  font-size: 22px; /* 增大标题字体 */
  font-weight: 500;
  margin-bottom: 15px; /* 调整标题与文本间距 */
}

.summary-card .summary-text {
  font-size: 16px; /* 减小文本字体 */
  color: var(--text-dark);
  line-height: 1.6;
  opacity: 0.9; /* 降低透明度 */
}

.summary-card .summary-value {
  font-size: 30px; /* 减小数值字体 */
  font-weight: 700; /* 加粗数值 */
  color: var(--primary-color-dark); /* 调整颜色 */
  margin-top: 15px; /* 调整间距 */
}

.el-progress--line {
  height: 10px;
  border-radius: 5px;
  overflow: hidden;
  background-color: var(--border-color-light);
}

.el-progress-bar__outer {
  background-color: var(--border-color-light) !important;
}

.el-progress-bar__inner {
  background-color: var(--primary-color) !important;
  border-radius: 5px; /* 进度条圆角 */
}

.detail-sections {
  display: grid;
  grid-template-columns: repeat(5, 1fr); /* 调整为每行显示5个 */
  gap: 40px; /* 增加细节卡片间距 */
}

.detail-card {
  background-color: var(--card-bg);
  border-radius: 20px; /* 增大圆角 */
  padding: 40px; /* 增加内边距 */
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.05); /* 调整阴影 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 优化过渡 */
  border: 1px solid var(--border-color-light); /* 添加细边框 */
}

.detail-card:hover {
  transform: translateY(-8px); /* 增加悬停位移 */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08); /* 增加悬停阴影 */
  border-color: var(--primary-color-translucent); /* 悬停时边框颜色变化 */
}

.detail-card .card-title {
  font-size: 22px; /* 增大标题字体 */
  font-weight: 500;
  margin-bottom: 30px; /* 增加标题与文本间距 */
  color: var(--text-color);
}

.detail-card .detail-text {
  font-size: 15px; /* 减小文本字体 */
  color: var(--text-dark);
  margin-bottom: 15px; /* 调整文本行间距 */
  display: flex;
  align-items: flex-start; /* 调整图标和文本对齐 */
  line-height: 1.6;
}

.detail-card .detail-text i {
  margin-right: 12px; /* 调整图标与文本间距 */
  color: var(--primary-color);
  font-size: 20px; /* 增大图标 */
  flex-shrink: 0;
}

.chart-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr)); /* 图表部分每行2个 */
  gap: 40px;
}

.chart-card {
  background-color: var(--card-bg);
  border-radius: 20px;
  padding: 40px; /* 增加内边距 */
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-color-light);
  min-height: 400px; /* 确保图表有足够高度 */
  display: flex;
  flex-direction: column;
}

.chart-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
  border-color: var(--primary-color-translucent);
}

.chart-card .card-title {
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 30px; /* 增加标题与文本间距 */
  color: var(--text-color);
  text-align: center;
}

.chart {
  flex-grow: 1;
  width: 100%;
}

.event-log-section {
  background-color: var(--card-bg);
  border-radius: 20px; /* 增大圆角 */
  padding: 40px; /* 增加内边距 */
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.05); /* 调整阴影 */
  border: 1px solid var(--border-color-light); /* 添加细边框 */
}

.event-log-section .card-title {
  font-size: 22px; /* 增大标题字体 */
  margin-bottom: 30px; /* 增加标题与日志列表间距 */
}

.event-log-list {
  max-height: 350px; /* 增加日志列表高度 */
  overflow-y: auto;
  padding-right: 15px;
}

.log-item {
  font-size: 14px; /* 减小日志字体 */
  color: var(--text-dark);
  margin-bottom: 10px; /* 调整日志行间距 */
  line-height: 1.6;
}

.log-time {
  font-weight: 500;
  color: var(--text-color);
  margin-right: 10px; /* 调整时间与描述间距 */
}

.log-description {
  color: var(--text-light);
}

.recommendation-section {
  background-color: var(--card-bg);
  border-radius: 20px; /* 增大圆角 */
  padding: 40px; /* 增加内边距 */
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.05); /* 调整阴影 */
  border: 1px solid var(--border-color-light); /* 添加细边框 */
}

.recommendation-section .card-title {
  font-size: 22px; /* 增大标题字体 */
  margin-bottom: 30px; /* 增加标题与建议列表间距 */
}

.recommendation-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendation-list li {
  font-size: 15px; /* 减小建议字体 */
  color: var(--text-dark);
  margin-bottom: 12px; /* 调整建议行间距 */
  display: flex;
  align-items: flex-start;
  line-height: 1.7;
}

.recommendation-list li i {
  color: var(--primary-color);
  margin-right: 12px; /* 调整图标与文本间距 */
  flex-shrink: 0;
  margin-top: 4px; /* 微调对齐 */
  font-size: 18px; /* 增大图标 */
}

.monitoring-inactive {
  display: flex;
  flex-direction: column; /* 使内容垂直居中 */
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px; /* 增加内边距 */
  background-color: var(--card-bg);
  border-radius: 20px; /* 增大圆角 */
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.05); /* 调整阴影 */
  border: 1px solid var(--border-color-light); /* 添加细边框 */
}

.empty-text {
  margin-top: 20px; /* 增加与上方图标的间距 */
  color: var(--text-light);
  font-size: 17px; /* 增大字体 */
}

/* Animations */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease; /* 增加transform过渡 */
}
.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateY(20px); /* 增加进入/离开动画的位移 */
}

.fade-in {
  animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(40px); }
  to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 768px) {
  .dashboard-title {
    font-size: 36px; /* 调整移动端标题大小 */
  }
  .dashboard-subtitle {
    font-size: 15px;
  }
  .container {
    padding: 25px 15px 30px;
    gap: 40px; /* 调整移动端整体间距 */
  }
  .summary-section {
    grid-template-columns: 1fr;
    gap: 25px; /* 调整移动端概览卡片间距 */
  }
  .detail-sections {
    grid-template-columns: 1fr;
    gap: 25px; /* 调整移动端细节卡片间距 */
  }
  .chart-section {
    grid-template-columns: 1fr; /* 移动端图表部分每行1个 */
    gap: 25px;
  }
  .summary-card, .detail-card, .chart-card, .event-log-section, .recommendation-section {
    padding: 30px; /* 调整移动端卡片内边距 */
    border-radius: 16px; /* 调整移动端圆角 */
  }
  .summary-card .card-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 15px;
  }
  .summary-card .card-icon i {
    font-size: 24px;
  }
  .summary-card .card-title, .detail-card .card-title, .chart-card .card-title, .event-log-section .card-title, .recommendation-section .card-title {
    font-size: 20px;
    margin-bottom: 20px; /* 调整移动端标题与内容间距 */
  }
  .summary-card .summary-text, .detail-card .detail-text, .log-item, .recommendation-list li, .empty-text {
    font-size: 14px; /* 移动端日志、建议和空状态文本 */
  }
  .summary-card .summary-value {
    font-size: 28px;
  }
  .detail-card .detail-text i, .recommendation-list li i {
    font-size: 16px;
    margin-right: 8px;
  }
  .event-log-list {
    max-height: 250px;
  }
  .chart-card {
    min-height: 300px; /* 移动端图表高度 */
  }
}
</style> 