<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式的故事世界</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
        }

        .story-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .story-section:nth-child(2) { animation-delay: 0.2s; }
        .story-section:nth-child(3) { animation-delay: 0.4s; }
        .story-section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .pattern-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .pattern-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .pattern-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .pattern-type {
            font-weight: bold;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .creational { color: #e74c3c; }
        .structural { color: #3498db; }
        .behavioral { color: #2ecc71; }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🏗️ 设计模式的奇幻世界</h1>

        <div class="story-section">
            <h2 class="section-title">第一章：设计模式王国的诞生</h2>
            <div class="canvas-container">
                <canvas id="introCanvas" width="600" height="300"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">设计模式</span>就像是建筑师的蓝图，是软件工程师们在长期实践中总结出的<span class="highlight">最佳解决方案</span>。</p>
                <p>想象一下，在一个神奇的王国里，有三个不同的部落：<span class="highlight creational">创建型部落</span>、<span class="highlight structural">结构型部落</span>和<span class="highlight behavioral">行为型部落</span>。</p>
                <p>每个部落都有自己独特的魔法，帮助程序员们解决不同类型的问题。</p>
            </div>
            <button class="interactive-btn" onclick="startIntroAnimation()">🎬 开始故事</button>
        </div>

        <div class="story-section">
            <h2 class="section-title">第二章：三大部落的特色</h2>
            <div class="pattern-grid">
                <div class="pattern-card" onclick="showPatternDetails('creational')">
                    <div class="pattern-type creational">🏭 创建型部落</div>
                    <p>专门负责"生产"对象的魔法师们</p>
                    <small>Singleton, Factory, Builder...</small>
                </div>
                <div class="pattern-card" onclick="showPatternDetails('structural')">
                    <div class="pattern-type structural">🏗️ 结构型部落</div>
                    <p>擅长"组装"和"连接"的建筑师们</p>
                    <small>Bridge, Adapter, Facade...</small>
                </div>
                <div class="pattern-card" onclick="showPatternDetails('behavioral')">
                    <div class="pattern-type behavioral">🎭 行为型部落</div>
                    <p>掌控"交流"和"协作"的外交官们</p>
                    <small>Command, Observer, Strategy...</small>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="tribesCanvas" width="600" height="300"></canvas>
            </div>
        </div>

        <div class="story-section">
            <h2 class="section-title">第三章：Bridge桥梁魔法师的传说</h2>
            <div class="canvas-container">
                <canvas id="bridgeCanvas" width="600" height="350"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">Bridge模式</span>是结构型部落中最神奇的魔法师之一。</p>
                <p>他的特殊能力是：<span class="highlight">将抽象与实现分离</span>，就像在两座山之间架起一座桥梁。</p>
                <p>这样，山的两边可以<span class="highlight">独立变化</span>，而桥梁确保它们始终保持连接。</p>
                <p>在编程世界中，这意味着接口和实现可以分别演化，而不会相互影响。</p>
            </div>
            <button class="interactive-btn" onclick="demonstrateBridge()">🌉 演示桥梁魔法</button>
        </div>
    </div>

    <script>
        // 获取画布和上下文
        const introCanvas = document.getElementById('introCanvas');
        const introCtx = introCanvas.getContext('2d');
        const tribesCanvas = document.getElementById('tribesCanvas');
        const tribesCtx = tribesCanvas.getContext('2d');
        const bridgeCanvas = document.getElementById('bridgeCanvas');
        const bridgeCtx = bridgeCanvas.getContext('2d');

        let animationFrame = 0;
        let isAnimating = false;

        // 开场动画
        function startIntroAnimation() {
            if (isAnimating) return;
            isAnimating = true;
            animationFrame = 0;
            
            function animate() {
                introCtx.clearRect(0, 0, introCanvas.width, introCanvas.height);
                
                // 绘制背景
                const gradient = introCtx.createLinearGradient(0, 0, 600, 300);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                introCtx.fillStyle = gradient;
                introCtx.fillRect(0, 0, 600, 300);
                
                // 绘制标题文字
                introCtx.fillStyle = 'white';
                introCtx.font = 'bold 24px Microsoft YaHei';
                introCtx.textAlign = 'center';
                introCtx.fillText('设计模式王国', 300, 50);
                
                // 绘制三个部落的城堡
                const castles = [
                    { x: 100, y: 150, color: '#e74c3c', name: '创建型' },
                    { x: 300, y: 150, color: '#3498db', name: '结构型' },
                    { x: 500, y: 150, color: '#2ecc71', name: '行为型' }
                ];
                
                castles.forEach((castle, index) => {
                    const delay = index * 30;
                    if (animationFrame > delay) {
                        const progress = Math.min((animationFrame - delay) / 60, 1);
                        const scale = progress * 0.8 + 0.2;
                        
                        introCtx.save();
                        introCtx.translate(castle.x, castle.y);
                        introCtx.scale(scale, scale);
                        
                        // 绘制城堡
                        introCtx.fillStyle = castle.color;
                        introCtx.fillRect(-30, -40, 60, 60);
                        introCtx.fillRect(-20, -60, 40, 20);
                        
                        // 绘制标签
                        introCtx.fillStyle = 'white';
                        introCtx.font = '12px Microsoft YaHei';
                        introCtx.textAlign = 'center';
                        introCtx.fillText(castle.name, 0, 40);
                        
                        introCtx.restore();
                    }
                });
                
                animationFrame++;
                if (animationFrame < 150) {
                    requestAnimationFrame(animate);
                } else {
                    isAnimating = false;
                    drawTribes();
                }
            }
            
            animate();
        }

        // 绘制三大部落
        function drawTribes() {
            tribesCtx.clearRect(0, 0, tribesCanvas.width, tribesCanvas.height);
            
            // 背景
            const gradient = tribesCtx.createLinearGradient(0, 0, 600, 300);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            tribesCtx.fillStyle = gradient;
            tribesCtx.fillRect(0, 0, 600, 300);
            
            // 绘制部落区域
            const tribes = [
                { x: 100, y: 150, color: '#e74c3c', name: '创建型部落', patterns: ['Singleton', 'Factory', 'Builder'] },
                { x: 300, y: 150, color: '#3498db', name: '结构型部落', patterns: ['Bridge', 'Adapter', 'Facade'] },
                { x: 500, y: 150, color: '#2ecc71', name: '行为型部落', patterns: ['Command', 'Observer', 'Strategy'] }
            ];
            
            tribes.forEach(tribe => {
                // 绘制部落圆圈
                tribesCtx.beginPath();
                tribesCtx.arc(tribe.x, tribe.y, 60, 0, Math.PI * 2);
                tribesCtx.fillStyle = tribe.color + '20';
                tribesCtx.fill();
                tribesCtx.strokeStyle = tribe.color;
                tribesCtx.lineWidth = 3;
                tribesCtx.stroke();
                
                // 绘制部落名称
                tribesCtx.fillStyle = tribe.color;
                tribesCtx.font = 'bold 14px Microsoft YaHei';
                tribesCtx.textAlign = 'center';
                tribesCtx.fillText(tribe.name, tribe.x, tribe.y - 10);
                
                // 绘制模式列表
                tribesCtx.font = '10px Microsoft YaHei';
                tribe.patterns.forEach((pattern, index) => {
                    tribesCtx.fillText(pattern, tribe.x, tribe.y + 10 + index * 12);
                });
            });
        }

        // 演示Bridge模式
        function demonstrateBridge() {
            bridgeCtx.clearRect(0, 0, bridgeCanvas.width, bridgeCanvas.height);
            
            let step = 0;
            
            function animateBridge() {
                bridgeCtx.clearRect(0, 0, bridgeCanvas.width, bridgeCanvas.height);
                
                // 背景
                const gradient = bridgeCtx.createLinearGradient(0, 0, 600, 350);
                gradient.addColorStop(0, '#87CEEB');
                gradient.addColorStop(1, '#98FB98');
                bridgeCtx.fillStyle = gradient;
                bridgeCtx.fillRect(0, 0, 600, 350);
                
                // 绘制两座山
                bridgeCtx.fillStyle = '#8B4513';
                bridgeCtx.fillRect(50, 200, 150, 150);
                bridgeCtx.fillRect(400, 200, 150, 150);
                
                // 山的标签
                bridgeCtx.fillStyle = 'white';
                bridgeCtx.font = 'bold 16px Microsoft YaHei';
                bridgeCtx.textAlign = 'center';
                bridgeCtx.fillText('抽象层', 125, 230);
                bridgeCtx.fillText('实现层', 475, 230);
                
                // 绘制桥梁（分步骤）
                if (step > 30) {
                    bridgeCtx.strokeStyle = '#FF6B6B';
                    bridgeCtx.lineWidth = 8;
                    bridgeCtx.lineCap = 'round';
                    
                    const progress = Math.min((step - 30) / 60, 1);
                    const bridgeLength = 200 * progress;
                    
                    bridgeCtx.beginPath();
                    bridgeCtx.moveTo(200, 200);
                    bridgeCtx.lineTo(200 + bridgeLength, 200);
                    bridgeCtx.stroke();
                    
                    // 桥梁完成后显示文字
                    if (progress >= 1) {
                        bridgeCtx.fillStyle = '#FF6B6B';
                        bridgeCtx.font = 'bold 14px Microsoft YaHei';
                        bridgeCtx.textAlign = 'center';
                        bridgeCtx.fillText('Bridge桥梁', 300, 190);
                        bridgeCtx.font = '12px Microsoft YaHei';
                        bridgeCtx.fillText('连接抽象与实现', 300, 210);
                    }
                }
                
                // 绘制说明文字
                bridgeCtx.fillStyle = '#333';
                bridgeCtx.font = '14px Microsoft YaHei';
                bridgeCtx.textAlign = 'left';
                bridgeCtx.fillText('Bridge模式的魔法：', 50, 50);
                bridgeCtx.fillText('• 将抽象与实现分离', 50, 70);
                bridgeCtx.fillText('• 两边可以独立变化', 50, 90);
                bridgeCtx.fillText('• 提高系统的可扩展性', 50, 110);
                
                step++;
                if (step < 120) {
                    requestAnimationFrame(animateBridge);
                }
            }
            
            animateBridge();
        }

        // 显示模式详情
        function showPatternDetails(type) {
            const details = {
                creational: {
                    title: '创建型模式详解',
                    description: '负责对象的创建过程，包括：Singleton（单例）、Factory（工厂）、Builder（建造者）等。',
                    example: '就像工厂生产产品一样，确保对象的创建过程标准化。'
                },
                structural: {
                    title: '结构型模式详解', 
                    description: '处理类和对象的组合，包括：Bridge（桥接）、Adapter（适配器）、Facade（外观）等。',
                    example: '就像建筑师设计房屋结构，确保各部分协调工作。'
                },
                behavioral: {
                    title: '行为型模式详解',
                    description: '关注对象间的通信和职责分配，包括：Command（命令）、Observer（观察者）等。',
                    example: '就像外交官协调各国关系，确保有效沟通。'
                }
            };
            
            const detail = details[type];
            alert(`${detail.title}\n\n${detail.description}\n\n${detail.example}`);
        }

        // 初始化
        window.onload = function() {
            drawTribes();
            
            // 为画布添加点击事件
            tribesCanvas.addEventListener('click', function(e) {
                const rect = tribesCanvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // 检测点击的部落
                if (Math.sqrt((x - 100) ** 2 + (y - 150) ** 2) < 60) {
                    showPatternDetails('creational');
                } else if (Math.sqrt((x - 300) ** 2 + (y - 150) ** 2) < 60) {
                    showPatternDetails('structural');
                } else if (Math.sqrt((x - 500) ** 2 + (y - 150) ** 2) < 60) {
                    showPatternDetails('behavioral');
                }
            });
        };
    </script>
</body>
</html>
