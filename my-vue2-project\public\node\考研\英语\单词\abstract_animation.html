<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Abstract</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #8e44ad; /* A purple color for "abstract" */
            --secondary-color: #2c3e50;
            --danger-color: #e74c3c;
            --success-color: #2ecc71;
            --light-bg: #f8f9fa;
            --panel-bg: #ffffff;
            --text-color: #333;
            --text-muted: #7f8c8d;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--secondary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--text-muted);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #555;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .breakdown-section .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .breakdown-section .insight {
            margin-top: 15px;
            font-style: italic;
            color: #555;
        }

        .animation-panel {
            flex: 2;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
        }

        .activity-title {
            font-size: 1.8em;
            color: var(--secondary-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .activity-wrapper {
            display: none;
            width: 100%;
            height: 100%;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .activity-wrapper.active {
            display: flex;
        }
        
        .game-container {
            width: 90%;
            max-width: 500px;
            height: 350px;
            border: 2px dashed #bdc3c7;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #ecf0f1, #ffffff);
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 10px;
            gap: 10px;
        }

        .mini-game {
            width: 32%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
        }

        .control-button {
            margin-top: 30px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(142, 68, 173, 0.4);
        }
        .control-button:hover {
            background-color: #7d3c98;
            transform: translateY(-2px);
        }
        .control-button:active { transform: translateY(1px); }

        #welcome-screen p { font-size: 1.2em; color: var(--text-muted); text-align: center; }

        /* --- Morpheme Game Styles for Abstract --- */
        .abs-center { position: absolute; width: 60px; height: 60px; background: var(--secondary-color); border-radius: 50%; }
        .abs-satellite { position: absolute; width: 30px; height: 30px; background: var(--primary-color); border-radius: 50%; transition: all 1s ease-in-out; }
        #abs-sat1 { top: 50px; left: calc(50% - 15px); }
        #abs-sat2 { bottom: 50px; left: calc(50% - 15px); }
        #abs-sat3 { left: 50px; top: calc(50% - 15px); }
        #abs-sat4 { right: 50px; top: calc(50% - 15px); }
        .abs-game-run #abs-sat1 { top: -40px; }
        .abs-game-run #abs-sat2 { bottom: -40px; }
        .abs-game-run #abs-sat3 { left: -40px; }
        .abs-game-run #abs-sat4 { right: -40px; }
        
        /* New abs- animation */
        .block-stack { position: absolute; bottom: 20px; left: 20px; }
        .stack-block { width: 40px; height: 20px; background: #3498db; border: 1px solid #2980b9; margin-top: -10px; transition: all 1s ease-in-out;}
        #sb-2 { position: relative; z-index: 2; }
        .abs-hand { position: absolute; width: 30px; height: 40px; background: #f1c40f; border-radius: 5px; z-index: 3; bottom: 42px; left: 65px; transition: all 1s ease-in-out;}
        .abs-game-run .abs-hand { left: 150px; }
        .abs-game-run #sb-2 { transform: translateX(85px); }

        /* Rocket Launch Game for abs- */
        .launchpad { position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); width: 70px; height: 10px; background: #7f8c8d; border-radius: 2px; }
        .rocket { position: absolute; bottom: 30px; left: 50%; transform: translateX(-50%); font-size: 40px; transition: transform 1.5s ease-in; }
        .abs-game-run .rocket { transform: translate(-50%, -250px) scale(0.5); }
        .smoke { position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); width: 20px; height: 20px; background: #bdc3c7; border-radius: 50%; opacity: 0; }
        .abs-game-run .smoke { animation: smoke-puff 1.5s ease-out forwards; }
        .smoke.s2 { animation-delay: 0.2s; }
        @keyframes smoke-puff {
            0% { transform: translate(-50%, 0) scale(0.5); opacity: 1; }
            100% { transform: translate(-50%, -40px) scale(4); opacity: 0; }
        }

        #tract-hook { position: absolute; width: 40px; height: 40px; border: 5px solid var(--primary-color); border-bottom-color: transparent; border-radius: 50%; top: 50px; right: -50px; transition: all 1s; }
        #tract-object { position: absolute; width: 50px; height: 50px; background: #f39c12; border-radius: 10px; left: 50px; top: calc(50% - 25px); transition: all 1s; }
        .tract-game-run #tract-hook { right: calc(100% - 90px); transform: rotate(-360deg); }
        .tract-game-run #tract-object { left: calc(100% - 100px); background: #e67e22; }

        /* Fishing Game for -tract */
        .water { position: absolute; bottom: 0; left: 0; width: 100%; height: 60%; background: linear-gradient(to top, #3498db, #5dade2); border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; }
        .fishing-rod { position: absolute; top: 30px; right: 5px; width: 100px; height: 4px; background: #8D6E63; transform-origin: bottom right; transform: rotate(-45deg); transition: transform 1s ease-in-out; }
        .tract-game-run .fishing-rod { transform: rotate(-60deg); }
        .fishing-line { position: absolute; top: 32px; right: 73px; width: 1px; height: 100px; background: var(--secondary-color); transform-origin: top; transition: height 1s ease-in-out; }
        .tract-game-run .fishing-line { height: 50px; }
        .fish { position: absolute; bottom: 40px; left: 20px; font-size: 30px; transition: transform 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55); transform: rotate(0); }
        .tract-game-run .fish { transform: translateY(-90px) rotate(-20deg); }

        /* --- Full Animation for Abstract --- */
        #abstract-container { justify-content: space-around; }
        .concrete-area, .abstract-area { width: 45%; height: 100%; display:flex; flex-direction: column; align-items: center; justify-content: center;}
        .art-canvas { width: 180px; height: 180px; border: 2px solid #ccc; display:flex; align-items: center; justify-content: center; background-size: contain; background-repeat: no-repeat; background-position: center; }
        #concrete-apple { background-image: url('https://static.vecteezy.com/system/resources/previews/008/848/360/original/fresh-red-apple-fruit-free-png.png'); }
        .canvas-label { margin-top: 10px; font-size: 1em; color: var(--text-muted); }
        .abstract-element { position: absolute; border-radius: 50%; transition: all 1.5s cubic-bezier(.6,-0.28,.74,1.2); }
        #abs-el-red { width: 100px; height: 100px; background: var(--danger-color); top: 125px; left: 65px; opacity: 0; }
        #abs-el-leaf { width: 20px; height: 40px; background: var(--success-color); border-radius: 20% 80%; top: 100px; left: 140px; opacity: 0; }
        
        .abstract-animation-run #abs-el-red { left: 300px; top: 150px; opacity: 1; }
        .abstract-animation-run #abs-el-leaf { left: 360px; top: 120px; opacity: 1; transform: rotate(45deg); }
        
        #abstraction-canvas, #particle-canvas, #generalization-canvas {
            background-color: #fdfdfd;
            border-radius: 10px;
        }
        
        /* Text Summary Game */
        .summary-container { display: flex; flex-direction: row; justify-content: space-between; width: 100%; height: 100%; gap: 20px;}
        .full-text, .summary-box { padding: 15px; border-radius: 8px; overflow: auto; }
        .full-text { flex: 3; background: #fff; border: 1px solid #e0e0e0; font-size: 0.9em; line-height: 1.5; color: var(--text-muted); }
        .summary-box { flex: 2; background: var(--light-bg); border: 2px solid var(--primary-color); }
        .full-text p { margin: 0 0 10px 0; }
        .key-sentence { font-weight: bold; color: var(--secondary-color); position: relative; }
    </style>
</head>
<body>

    <div class="container">
        <div class="word-panel">
            <h1>abstract</h1>
            <p class="pronunciation">[ˈæbstrækt]</p>
            <div class="details">
                <p><strong>词性：</strong> adj. 抽象的; v. 提取; n. 摘要</p>
                <p><strong>含义：</strong> 从具体事物中抽离出概念或精华。</p>
                <div class="example">
                    <p><strong>例句：</strong> The artist's work became more abstract over time.</p>
                    <p><strong>翻译：</strong> 随着时间的推移，这位艺术家的作品变得越来越抽象。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="abs-game">abs- (离开)</button>
                    <button class="morpheme-btn" data-activity="tract-game">-tract (拉)</button>
                </div>
                <p class="insight">点击上方词缀，体验其含义的互动游戏！</p>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示</button>
                    <button class="morpheme-btn" data-activity="canvas-animation">Canvas 抽象化</button>
                    <button class="morpheme-btn" data-activity="particle-animation">Canvas 提取精华</button>
                    <button class="morpheme-btn" data-activity="generalization-animation">Canvas 归纳总结</button>
                    <button class="morpheme-btn" data-activity="summary-animation">n. 文本摘要</button>
                </div>
            </div>

        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎！</h2>

            <div id="welcome-screen" class="activity-wrapper active">
                <p>点击左侧的词缀按钮<br>开始你的交互式学习之旅！</p>
            </div>

            <!-- Morpheme Games -->
            <div id="abs-game" class="activity-wrapper">
                <div class="game-container" id="abs-container">
                    <div class="mini-game">
                        <div class="abs-center"></div>
                        <div id="abs-sat1" class="abs-satellite"></div>
                        <div id="abs-sat2" class="abs-satellite"></div>
                        <div id="abs-sat3" class="abs-satellite"></div>
                        <div id="abs-sat4" class="abs-satellite"></div>
                    </div>
                    <div class="mini-game">
                        <div class="block-stack">
                            <div class="stack-block"></div>
                            <div id="sb-2" class="stack-block"></div>
                            <div class="stack-block"></div>
                        </div>
                        <div class="abs-hand"></div>
                    </div>
                    <div class="mini-game">
                        <div class="launchpad"></div>
                        <div class="rocket">🚀</div>
                        <div class="smoke s1"></div>
                        <div class="smoke s2"></div>
                    </div>
                </div>
                <button class="control-button" id="abs-btn">演示 "离开" (abs-)</button>
            </div>
            
            <div id="tract-game" class="activity-wrapper">
                 <div class="game-container" id="tract-container">
                    <div class="mini-game">
                        <div id="tract-hook"></div>
                        <div id="tract-object"></div>
                    </div>
                    <div class="mini-game">
                        <div class="tract-magnet"></div>
                        <div id="f1" class="filing"></div>
                        <div id="f2" class="filing"></div>
                        <div id="f3" class="filing"></div>
                        <div id="f4" class="filing"></div>
                    </div>
                    <div class="mini-game">
                        <div class="water"></div>
                        <div class="fishing-rod"></div>
                        <div class="fishing-line"></div>
                        <div class="fish">🐟</div>
                    </div>
                </div>
                <button class="control-button" id="tract-btn">演示 "拉" (-tract)</button>
            </div>

            <!-- Full Word Activities -->
            <div id="full-animation" class="activity-wrapper">
                <div class="game-container" id="abstract-container">
                    <div class="concrete-area">
                        <div class="art-canvas" id="concrete-apple"></div>
                        <span class="canvas-label">具体的苹果</span>
                    </div>
                     <div class="abstract-area">
                        <div class="art-canvas" id="abstract-canvas"></div>
                        <span class="canvas-label">抽象的苹果</span>
                    </div>
                    <!-- Elements to be abstracted -->
                    <div id="abs-el-red" class="abstract-element"></div>
                    <div id="abs-el-leaf" class="abstract-element"></div>
                </div>
                <button class="control-button" id="abstract-btn">开始抽象化！</button>
            </div>

            <!-- Canvas Activity -->
            <div id="canvas-animation" class="activity-wrapper">
                <div class="game-container">
                    <canvas id="abstraction-canvas" width="500" height="350"></canvas>
                </div>
                <button class="control-button" id="canvas-btn">执行Canvas抽象</button>
            </div>

            <!-- Particle Activity -->
            <div id="particle-animation" class="activity-wrapper">
                <div class="game-container">
                    <canvas id="particle-canvas" width="500" height="350"></canvas>
                </div>
                <button class="control-button" id="particle-btn">提取精华</button>
            </div>

            <!-- Generalization Activity -->
            <div id="generalization-animation" class="activity-wrapper">
                <div class="game-container">
                    <canvas id="generalization-canvas" width="500" height="350"></canvas>
                </div>
                <button class="control-button" id="generalization-btn">开始归纳</button>
            </div>

            <!-- Summary Activity -->
            <div id="summary-animation" class="activity-wrapper">
                <div class="game-container summary-container">
                    <div class="full-text">
                        <p>在深入探讨项目管理的方法论时，我们必须认识到其核心在于资源的有效配置和时间的精确控制。<span class="key-sentence" data-summary-id="s1">项目的成功不仅取决于前期的规划，更在于执行过程中的动态调整和风险管理。</span>我们发现，许多项目失败的根源并非技术不足，而是沟通不畅和目标模糊。</p>
                        <p>因此，建立一个清晰的沟通机制至关重要。团队成员需要对共同的目标有统一的认识。<span class="key-sentence" data-summary-id="s2">定期的进度回顾和开放的反馈渠道是确保项目航向正确的两个基本支柱。</span></p>
                        <p>最后，技术的选型和工具的应用也是不可忽视的一环。合适的工具能极大提升效率，但工具终究是为人服务的。<span class="key-sentence" data-summary-id="s3">归根结底，人的因素，即团队的专业能力和协作精神，才是决定项目成败的根本。</span></p>
                    </div>
                    <div class="summary-box"></div>
                </div>
                <button class="control-button" id="summary-btn">生成摘要</button>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');

        // --- Activity Switching Logic ---
        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetId = btn.dataset.activity;
                const targetWrapper = document.getElementById(targetId);
                
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                activityWrappers.forEach(w => w.classList.remove('active'));
                targetWrapper.classList.add('active');
                
                activityTitle.textContent = btn.textContent;

                // Cleanup previous animation if exists
                if(window.cleanup) {
                    window.cleanup();
                    window.cleanup = null;
                }

                switch(targetId) {
                    case 'abs-game':
                    case 'tract-game':
                        // These are CSS-based, no specific JS setup/cleanup needed besides the toggle
                        break;
                    case 'full-animation':
                        window.cleanup = setupFullAnimation();
                        break;
                    case 'canvas-animation':
                        window.cleanup = setupCanvasAnimation();
                        break;
                    case 'particle-animation':
                        window.cleanup = setupParticleAnimation();
                        break;
                    case 'generalization-animation':
                        window.cleanup = setupGeneralizationAnimation();
                        break;
                    case 'summary-animation':
                        window.cleanup = setupSummaryAnimation();
                        break;
                }
            });
        });

        // --- Morpheme Games Logic ---
        function setupMorphemeGames() {
            // ABS- Game
            const absBtn = document.getElementById('abs-btn');
            const absContainer = document.getElementById('abs-container');
            absBtn.addEventListener('click', () => {
                absContainer.classList.toggle('abs-game-run');
                absBtn.textContent = absContainer.classList.contains('abs-game-run') ? '复位' : '演示 "离开" (abs-)';
            });

            // TRACT- Game
            const tractBtn = document.getElementById('tract-btn');
            const tractContainer = document.getElementById('tract-container');
            tractBtn.addEventListener('click', () => {
                tractContainer.classList.toggle('tract-game-run');
                tractBtn.textContent = tractContainer.classList.contains('tract-game-run') ? '复位' : '演示 "拉" (-tract)';
            });
        }
        
        // --- Full Word Activities Logic ---
        function setupFullAnimation() {
            const abstractBtn = document.getElementById('abstract-btn');
            const container = document.getElementById('abstract-container');
            let isRun = false;

            const clickHandler = () => {
                isRun = !isRun;
                if(isRun) {
                    container.classList.add('abstract-animation-run');
                } else {
                    container.classList.remove('abstract-animation-run');
                }
                abstractBtn.textContent = isRun ? "复位" : "开始抽象化！";
            };

            abstractBtn.addEventListener('click', clickHandler);

            return () => {
                 abstractBtn.removeEventListener('click', clickHandler);
            }
        }
        
        // --- Canvas Animation Logic ---
        function setupCanvasAnimation() {
            const canvasBtn = document.getElementById('canvas-btn');
            const canvas = document.getElementById('abstraction-canvas');
            const activityWrapper = document.getElementById('canvas-animation');
            
            if (!canvas || !canvasBtn || !activityWrapper) return;
            
            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let animationFrameId = null;

            const concreteState = {
                roof: { type: 'triangle', color: '#c0392b', points: [{x: 250, y: 50}, {x: 150, y: 150}, {x: 350, y: 150}] },
                base: { type: 'rect', color: '#7f8c8d', x: 160, y: 150, w: 180, h: 150 },
                door: { type: 'rect', color: '#2980b9', x: 235, y: 220, w: 30, h: 80 }
            };

            const abstractState = {
                roof: { type: 'triangle', color: '#c0392b', points: [{x: 100, y: 50}, {x: 0, y: 150}, {x: 200, y: 150}] },
                base: { type: 'rect', color: '#7f8c8d', x: 250, y: 180, w: 180, h: 150 },
                door: { type: 'rect', color: '#2980b9', x: 440, y: 250, w: 30, h: 80 }
            };

            let currentShapes = JSON.parse(JSON.stringify(concreteState));
            let isAbstract = false;

            function draw() {
                ctx.clearRect(0, 0, width, height);
                for (const key in currentShapes) {
                    const shape = currentShapes[key];
                    ctx.fillStyle = shape.color;
                    if (shape.type === 'rect') {
                        ctx.fillRect(shape.x, shape.y, shape.w, shape.h);
                    } else if (shape.type === 'triangle') {
                        ctx.beginPath();
                        ctx.moveTo(shape.points[0].x, shape.points[0].y);
                        ctx.lineTo(shape.points[1].x, shape.points[1].y);
                        ctx.lineTo(shape.points[2].x, shape.points[2].y);
                        ctx.closePath();
                        ctx.fill();
                    }
                }
            }
            
            function easeInOutCubic(t) {
                return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
            }

            function animate(startTime, duration, fromState, toState) {
                const elapsed = performance.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const easedProgress = easeInOutCubic(progress);

                for (const key in currentShapes) {
                    const startShape = fromState[key];
                    const endShape = toState[key];
                    const currentShape = currentShapes[key];

                    if (currentShape.type === 'rect') {
                        currentShape.x = startShape.x + (endShape.x - startShape.x) * easedProgress;
                        currentShape.y = startShape.y + (endShape.y - startShape.y) * easedProgress;
                    } else if (currentShape.type === 'triangle') {
                        for (let i = 0; i < 3; i++) {
                            currentShape.points[i].x = startShape.points[i].x + (endShape.points[i].x - startShape.points[i].x) * easedProgress;
                            currentShape.points[i].y = startShape.points[i].y + (endShape.points[i].y - startShape.points[i].y) * easedProgress;
                        }
                    }
                }
                
                draw();

                if (progress < 1) {
                    animationFrameId = requestAnimationFrame(() => animate(startTime, duration, fromState, toState));
                }
            }

            canvasBtn.addEventListener('click', () => {
                if (animationFrameId) cancelAnimationFrame(animationFrameId);
                
                const from = isAbstract ? abstractState : concreteState;
                const to = isAbstract ? concreteState : abstractState;
                
                isAbstract = !isAbstract;
                canvasBtn.textContent = isAbstract ? "还原为具体" : "执行Canvas抽象";
                
                animate(performance.now(), 1500, from, to);
            });
            
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.attributeName === 'class' && activityWrapper.classList.contains('active')) {
                        isAbstract = false;
                        canvasBtn.textContent = "执行Canvas抽象";
                        currentShapes = JSON.parse(JSON.stringify(concreteState));
                        draw();
                    }
                });
            });

            observer.observe(activityWrapper, { attributes: true });
            
            draw();

            return () => {
                if (animationFrameId) cancelAnimationFrame(animationFrameId);
                observer.disconnect();
            }
        }

        // --- Particle Animation Logic ---
        function setupParticleAnimation() {
            const particleBtn = document.getElementById('particle-btn');
            const canvas = document.getElementById('particle-canvas');
            const activityWrapper = document.getElementById('particle-animation');
            if (!canvas || !particleBtn || !activityWrapper) return;

            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;

            let particles = [];
            const numParticles = 200;
            const coreColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color');
            const noiseColors = ['#bdc3c7', '#95a5a6', '#7f8c8d'];

            let animationId = null;
            let isAbstracted = false;

            function random(min, max) { return Math.random() * (max - min) + min; }

            function Particle(x, y, vx, vy, radius, color, isCore) {
                this.x = x; this.y = y;
                this.vx = vx; this.vy = vy;
                this.originRadius = radius;
                this.radius = 0;
                this.targetRadius = radius;
                this.color = color;
                this.isCore = isCore;
                this.opacity = 0;
                this.targetOpacity = 1;
            }

            Particle.prototype.draw = function() {
                ctx.save();
                ctx.globalAlpha = this.opacity;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = this.color;
                ctx.fill();
                ctx.restore();
            }

            Particle.prototype.update = function() {
                if (Math.abs(this.radius - this.targetRadius) > 0.01) {
                    this.radius += (this.targetRadius - this.radius) * 0.05;
                }
                if (Math.abs(this.opacity - this.targetOpacity) > 0.01) {
                    this.opacity += (this.targetOpacity - this.opacity) * 0.05;
                } else {
                    this.opacity = this.targetOpacity;
                }

                if (this.x + this.radius > width || this.x - this.radius < 0) { this.vx = -this.vx; }
                if (this.y + this.radius > height || this.y - this.radius < 0) { this.vy = -this.vy; }
                this.x += this.vx;
                this.y += this.vy;
            }

            function init() {
                particles = [];
                for (let i = 0; i < numParticles; i++) {
                    const radius = random(2, 5);
                    const x = random(radius, width - radius);
                    const y = random(radius, height - radius);
                    const vx = random(-0.5, 0.5);
                    const vy = random(-0.5, 0.5);
                    
                    if (Math.random() < 0.2) { // 20% are core
                         particles.push(new Particle(x, y, vx, vy, radius, coreColor, true));
                    } else {
                         const color = noiseColors[Math.floor(random(0, noiseColors.length))];
                         particles.push(new Particle(x, y, vx, vy, radius, color, false));
                    }
                }
            }

            function animate() {
                ctx.clearRect(0, 0, width, height);
                particles.forEach(p => {
                    p.update();
                    p.draw();
                });
                animationId = requestAnimationFrame(animate);
            }
            
            particleBtn.addEventListener('click', () => {
                isAbstracted = !isAbstracted;
                particleBtn.textContent = isAbstracted ? "显示全部" : "提取精华";
                particles.forEach(p => {
                    if (isAbstracted) {
                        p.targetOpacity = p.isCore ? 1 : 0;
                        p.targetRadius = p.isCore ? p.originRadius : 0;
                    } else {
                        p.targetOpacity = 1;
                        p.targetRadius = p.originRadius;
                    }
                });
            });

            const observer = new MutationObserver(() => {
                if (activityWrapper.classList.contains('active')) {
                    init();
                    isAbstracted = false;
                    particleBtn.textContent = "提取精华";
                    if (!animationId) animate();
                } else {
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                        animationId = null;
                    }
                }
            });
            observer.observe(activityWrapper, { attributes: true, attributeFilter: ['class'] });
        }

        // --- Generalization Animation Logic ---
        function setupGeneralizationAnimation() {
            const genBtn = document.getElementById('generalization-btn');
            const canvas = document.getElementById('generalization-canvas');
            const activityWrapper = document.getElementById('generalization-animation');
            if (!canvas || !genBtn || !activityWrapper) return;

            const ctx = canvas.getContext('2d');
            const width = canvas.width;
            const height = canvas.height;
            let animationId = null;
            let isGeneralized = false;

            const trunkColor = '#8D6E63';
            const leafColor1 = '#4CAF50'; // Deciduous
            const leafColor2 = '#2E7D32'; // Pine
            const leafColor3 = '#689F38'; // Willow

            // State definitions
            const concreteState = {
                tree1: { // Deciduous
                    trunk: { x: 100, y: 200, w: 20, h: 80 },
                    canopy: { type: 'circle', x: 110, y: 170, r: 50, color: leafColor1 }
                },
                tree2: { // Pine
                    trunk: { x: 240, y: 200, w: 20, h: 80 },
                    canopy: { type: 'triangle', p1: {x:250, y:120}, p2: {x:200, y:210}, p3: {x:300, y:210}, color: leafColor2 }
                },
                tree3: { // Willow-like
                    trunk: { x: 380, y: 200, w: 20, h: 80 },
                    canopy: { type: 'lines', x: 390, y: 170, num: 15, len: 60, color: leafColor3 }
                }
            };

            const abstractState = {
                trunk: { x: 240, y: 200, w: 20, h: 80 },
                canopy: { type: 'circle', x: 250, y: 170, r: 50, color: leafColor1 }
            };
            
            let currentTrees = JSON.parse(JSON.stringify(concreteState));

            function lerp(start, end, amt) { return (1 - amt) * start + amt * end; }
            function easeInOutQuad(t) { return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t; }

            function draw() {
                ctx.clearRect(0, 0, width, height);
                // Draw each tree
                Object.values(currentTrees).forEach(tree => {
                    // Draw trunk
                    ctx.fillStyle = trunkColor;
                    ctx.fillRect(tree.trunk.x, tree.trunk.y, tree.trunk.w, tree.trunk.h);
                    
                    // Draw canopy
                    ctx.fillStyle = tree.canopy.color;
                    if (tree.canopy.type === 'circle') {
                        ctx.beginPath();
                        ctx.arc(tree.canopy.x, tree.canopy.y, tree.canopy.r, 0, Math.PI * 2);
                        ctx.fill();
                    } else if (tree.canopy.type === 'triangle') {
                        ctx.beginPath();
                        ctx.moveTo(tree.canopy.p1.x, tree.canopy.p1.y);
                        ctx.lineTo(tree.canopy.p2.x, tree.canopy.p2.y);
                        ctx.lineTo(tree.canopy.p3.x, tree.canopy.p3.y);
                        ctx.closePath();
                        ctx.fill();
                    } else if (tree.canopy.type === 'lines') {
                        ctx.strokeStyle = tree.canopy.color;
                        ctx.lineWidth = 2;
                        for(let i = 0; i < tree.canopy.num; i++) {
                            ctx.beginPath();
                            ctx.moveTo(tree.canopy.x, tree.canopy.y);
                            const angle = (i / tree.canopy.num) * Math.PI + Math.PI / 4;
                            const endX = tree.canopy.x + Math.cos(angle) * tree.canopy.len;
                            const endY = tree.canopy.y + Math.sin(angle) * tree.canopy.len * 0.7;
                            ctx.lineTo(endX, endY);
                            ctx.stroke();
                        }
                    }
                });
            }

            function animate(startTime, duration) {
                const elapsed = performance.now() - startTime;
                let progress = Math.min(elapsed / duration, 1);
                progress = easeInOutQuad(progress);

                const targetProgress = isGeneralized ? progress : 1 - progress;

                // Animate each tree towards/away from the abstract state
                Object.keys(concreteState).forEach((key, index) => {
                    const concreteTree = concreteState[key];
                    const currentTree = currentTrees[key];

                    // Morph trunk
                    currentTree.trunk.x = lerp(concreteTree.trunk.x, abstractState.trunk.x, targetProgress);
                    
                    // Morph canopy
                    if (currentTree.canopy.type === 'circle') {
                        currentTree.canopy.x = lerp(concreteTree.canopy.x, abstractState.canopy.x, targetProgress);
                    } else if (currentTree.canopy.type === 'triangle') {
                        currentTree.canopy.p1.x = lerp(concreteTree.canopy.p1.x, abstractState.canopy.x, targetProgress);
                        currentTree.canopy.p1.y = lerp(concreteTree.canopy.p1.y, abstractState.canopy.y - abstractState.canopy.r, targetProgress);
                        // more complex lerping for other points to form a circle... for simplicity, we just move them
                        currentTree.canopy.p2.x = lerp(concreteTree.canopy.p2.x, abstractState.canopy.x - abstractState.canopy.r, targetProgress);
                        currentTree.canopy.p2.y = lerp(concreteTree.canopy.p2.y, abstractState.canopy.y, targetProgress);
                        currentTree.canopy.p3.x = lerp(concreteTree.canopy.p3.x, abstractState.canopy.x + abstractState.canopy.r, targetProgress);
                        currentTree.canopy.p3.y = lerp(concreteTree.canopy.p3.y, abstractState.canopy.y, targetProgress);
                    } else if (currentTree.canopy.type === 'lines') {
                        currentTree.canopy.x = lerp(concreteTree.canopy.x, abstractState.canopy.x, targetProgress);
                        currentTree.canopy.len = lerp(concreteTree.canopy.len, 0, targetProgress);
                    }
                });
                
                draw();

                if (progress < 1) {
                    animationId = requestAnimationFrame(() => animate(startTime, duration));
                }
            }

            genBtn.addEventListener('click', () => {
                isGeneralized = !isGeneralized;
                genBtn.textContent = isGeneralized ? "显示实例" : "开始归纳";
                if (animationId) cancelAnimationFrame(animationId);
                animate(performance.now(), 2000);
            });

            const observer = new MutationObserver(() => {
                if (activityWrapper.classList.contains('active')) {
                    isGeneralized = false;
                    genBtn.textContent = "开始归纳";
                    currentTrees = JSON.parse(JSON.stringify(concreteState));
                    draw();
                } else {
                     if (animationId) cancelAnimationFrame(animationId);
                     animationId = null;
                }
            });
            observer.observe(activityWrapper, { attributes: true, attributeFilter: ['class'] });
            
            draw(); // Initial draw
        }

        function setupSummaryAnimation() {
            const summaryBtn = document.getElementById('summary-btn');
            const fullText = document.querySelector('.full-text');
            const summaryBox = document.querySelector('.summary-box');
            const sentences = Array.from(fullText.querySelectorAll('.key-sentence'));
            let isSummarized = false;
            let tl;

            const originalStates = new Map();

            const clickHandler = () => {
                if (tl && tl.isActive()) return;

                isSummarized = !isSummarized;
                summaryBtn.disabled = true;

                if (isSummarized) {
                    summaryBtn.textContent = '还原';
                    // Save original state and get positions
                    sentences.forEach(s => originalStates.set(s, s.cloneNode(true)));
                    
                    const nonKeyText = fullText.querySelectorAll('p');
                    
                    tl = gsap.timeline({ onComplete: () => summaryBtn.disabled = false });
                    
                    tl.to(Array.from(nonKeyText).flatMap(p => p.childNodes).filter(node => node.nodeName === "#text"), {
                        opacity: 0.2,
                        duration: 0.5
                    });
                    
                    sentences.forEach((s, i) => {
                        const clone = s.cloneNode(true);
                        clone.style.position = 'absolute';
                        clone.style.top = s.offsetTop + 'px';
                        clone.style.left = s.offsetLeft + 'px';
                        clone.style.width = s.offsetWidth + 'px';
                        clone.style.margin = 0;
                        clone.style.padding = 0;
                        fullText.appendChild(clone);
                        
                        s.style.opacity = 0;
                        
                        tl.to(clone, {
                            top: i * 50 + 20,
                            left: fullText.offsetWidth + 40,
                            scale: 0.8,
                            duration: 1,
                            ease: 'power3.inOut',
                            onComplete: () => {
                                summaryBox.appendChild(s);
                                s.style.opacity = 1;
                                clone.remove();
                            }
                        }, "-=0.7");
                    });

                } else {
                    summaryBtn.textContent = '生成摘要';
                    // Restore
                    tl = gsap.timeline({ onComplete: () => {
                        summaryBtn.disabled = false;
                        summaryBox.innerHTML = '';
                        fullText.innerHTML = '';
                        // Restore from saved state
                         const p1 = document.createElement('p');
                         p1.innerHTML = `在深入探讨项目管理的方法论时，我们必须认识到其核心在于资源的有效配置和时间的精确控制。<span class="key-sentence" data-summary-id="s1">项目的成功不仅取决于前期的规划，更在于执行过程中的动态调整和风险管理。</span>我们发现，许多项目失败的根源并非技术不足，而是沟通不畅和目标模糊。`;
                         const p2 = document.createElement('p');
                         p2.innerHTML = `因此，建立一个清晰的沟通机制至关重要。团队成员需要对共同的目标有统一的认识。<span class="key-sentence" data-summary-id="s2">定期的进度回顾和开放的反馈渠道是确保项目航向正确的两个基本支柱。</span>`;
                         const p3 = document.createElement('p');
                         p3.innerHTML = `最后，技术的选型和工具的应用也是不可忽视的一环。合适的工具能极大提升效率，但工具终究是为人服务的。<span class="key-sentence" data-summary-id="s3">归根结底，人的因素，即团队的专业能力和协作精神，才是决定项目成败的根本。</span>`;
                         fullText.append(p1, p2, p3);
                    }});
                    tl.to('.full-text', {opacity: 1, duration: 0.5});
                }
            };
            
            summaryBtn.addEventListener('click', clickHandler);
            
            return () => {
                summaryBtn.removeEventListener('click', clickHandler);
                 if(tl) tl.kill();
                 // Add full restoration logic here if needed
            };
        }

        setupMorphemeGames();
        // Initial setup for the active button, can be refined
        const initialActiveBtn = document.querySelector('.morpheme-btn.active');
        if (initialActiveBtn) {
            initialActiveBtn.click();
            initialActiveBtn.click(); // Hack to trigger observer correctly on first load
        }
    });
    </script>

</body>
</html> 