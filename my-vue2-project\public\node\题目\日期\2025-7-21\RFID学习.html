<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RFID vs 条形码 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 50px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 40px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .option {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border: 3px solid transparent;
            border-radius: 20px;
            padding: 30px;
            cursor: pointer;
            transition: all 0.4s ease;
            text-align: left;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .option:hover::before {
            left: 100%;
        }

        .option:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .option.selected {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
        }

        .option.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            animation: correctPulse 0.8s ease-in-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            animation: wrongShake 0.8s ease-in-out;
        }

        .option-label {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .option-text {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.6;
        }

        .demo-section {
            background: white;
            border-radius: 25px;
            padding: 40px;
            margin: 50px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .demo-title {
            text-align: center;
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
        }

        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }

        .tech-demo {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .tech-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #333;
        }

        #demoCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            max-width: 100%;
            height: auto;
            margin: 20px 0;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }

        .explanation {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 25px;
            padding: 50px;
            margin: 50px 0;
            animation: fadeIn 1s ease-out;
            display: none;
        }

        .explanation h2 {
            font-size: 2.2rem;
            margin-bottom: 30px;
            text-align: center;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .feature-card {
            background: rgba(255,255,255,0.15);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: scale(1.05);
            background: rgba(255,255,255,0.25);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255,255,255,0.3);
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.8s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.08); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-15px); }
            75% { transform: translateX(15px); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .comparison-container {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 2.5rem;
            }
            
            .options-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">📡 RFID vs 条形码</h1>
            <p class="subtitle">通过互动演示理解物联网关键技术</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>射频识别（RFID）是物联网的关键技术。RFID标签又称电子标签，关于电子标签与条形码（barcode）标签的叙述，（ ）是正确的。
            </div>
            
            <div class="options-container">
                <div class="option" data-option="A">
                    <div class="option-label">A. 电子标签建置成本低，多个标签可被同时读取</div>
                    <div class="option-text">💰 成本低 + 📡 批量读取</div>
                </div>
                <div class="option" data-option="B">
                    <div class="option-label">B. 条形码标签容量小，但难以被复制</div>
                    <div class="option-text">💾 容量小 + 🔒 防复制</div>
                </div>
                <div class="option" data-option="C">
                    <div class="option-label">C. 电子标签通信距离短，但对环境变化有较高的忍受能力</div>
                    <div class="option-text">📏 距离短 + 🌡️ 抗环境</div>
                </div>
                <div class="option" data-option="D">
                    <div class="option-label">D. 电子标签容量大，可同时读取多个标签并且难以被复制</div>
                    <div class="option-text">💾 大容量 + 📡 批量读取 + 🔒 防复制</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">🎮 RFID vs 条形码 互动演示</h2>

            <div class="comparison-container">
                <div class="tech-demo">
                    <h3 class="tech-title">📊 条形码技术</h3>
                    <canvas id="barcodeCanvas" width="400" height="300"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="startBarcodeDemo()">演示条形码</button>
                    </div>
                </div>

                <div class="tech-demo">
                    <h3 class="tech-title">📡 RFID技术</h3>
                    <canvas id="rfidCanvas" width="400" height="300"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="startRFIDDemo()">演示RFID</button>
                    </div>
                </div>
            </div>

            <div class="controls">
                <button class="btn" onclick="startComparison()">对比演示</button>
                <button class="btn" onclick="resetAll()">重置所有</button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h2>💡 知识解析</h2>
            <p style="font-size: 1.2rem; margin-bottom: 30px; text-align: center;">
                <span class="highlight">正确答案：D</span> - 电子标签容量大，可同时读取多个标签并且难以被复制
            </p>

            <div class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">💾</span>
                    <h3>存储容量</h3>
                    <p><strong>RFID：</strong>几KB到几MB<br><strong>条形码：</strong>几十字节</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">📡</span>
                    <h3>读取方式</h3>
                    <p><strong>RFID：</strong>无接触，批量读取<br><strong>条形码：</strong>光学扫描，逐个读取</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🔒</span>
                    <h3>安全性</h3>
                    <p><strong>RFID：</strong>可加密，难复制<br><strong>条形码：</strong>易复制，无加密</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🌡️</span>
                    <h3>环境适应</h3>
                    <p><strong>RFID：</strong>防水防磁耐高温<br><strong>条形码：</strong>易损坏，怕污染</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">📏</span>
                    <h3>读取距离</h3>
                    <p><strong>RFID：</strong>几厘米到几十米<br><strong>条形码：</strong>需要近距离对准</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">💰</span>
                    <h3>成本对比</h3>
                    <p><strong>RFID：</strong>成本较高<br><strong>条形码：</strong>成本极低</p>
                </div>
            </div>

            <div style="background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin-top: 30px;">
                <h3 style="margin-bottom: 20px;">🎯 为什么选择D？</h3>
                <ul style="font-size: 1.1rem; line-height: 1.8;">
                    <li>✅ <strong>容量大：</strong>RFID可存储KB到MB级数据，远超条形码的几十字节</li>
                    <li>✅ <strong>批量读取：</strong>RFID读写器可同时识别多个标签，提高效率</li>
                    <li>✅ <strong>难以复制：</strong>RFID支持加密和认证，安全性高</li>
                    <li>❌ <strong>A选项错误：</strong>RFID建置成本实际上比条形码高</li>
                    <li>❌ <strong>B选项错误：</strong>条形码容易被复制，安全性低</li>
                    <li>❌ <strong>C选项错误：</strong>RFID通信距离实际上比条形码远</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const barcodeCanvas = document.getElementById('barcodeCanvas');
        const rfidCanvas = document.getElementById('rfidCanvas');
        const barcodeCtx = barcodeCanvas.getContext('2d');
        const rfidCtx = rfidCanvas.getContext('2d');

        let barcodeAnimation = null;
        let rfidAnimation = null;
        let progress = 0;

        // 选项点击处理
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('selected', 'correct', 'wrong');
                });

                // 标记当前选择
                this.classList.add('selected');

                // 检查答案
                setTimeout(() => {
                    checkAnswer(this.dataset.option);
                }, 300);
            });
        });

        function checkAnswer(selected) {
            const correct = 'D';
            const options = document.querySelectorAll('.option');

            options.forEach(option => {
                if (option.dataset.option === correct) {
                    option.classList.add('correct');
                } else if (option.dataset.option === selected && selected !== correct) {
                    option.classList.add('wrong');
                }
            });

            // 显示解析
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                updateProgress(100);
            }, 1000);
        }

        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        function startBarcodeDemo() {
            resetBarcode();
            animateBarcode();
        }

        function startRFIDDemo() {
            resetRFID();
            animateRFID();
        }

        function startComparison() {
            resetAll();
            animateBarcode();
            animateRFID();
        }

        function resetBarcode() {
            if (barcodeAnimation) {
                cancelAnimationFrame(barcodeAnimation);
            }
            barcodeCtx.clearRect(0, 0, barcodeCanvas.width, barcodeCanvas.height);
        }

        function resetRFID() {
            if (rfidAnimation) {
                cancelAnimationFrame(rfidAnimation);
            }
            rfidCtx.clearRect(0, 0, rfidCanvas.width, rfidCanvas.height);
        }

        function resetAll() {
            resetBarcode();
            resetRFID();
            progress = 0;
        }

        function animateBarcode() {
            barcodeCtx.clearRect(0, 0, barcodeCanvas.width, barcodeCanvas.height);

            // 绘制条形码
            drawBarcode();

            progress += 0.02;
            if (progress < 2) {
                barcodeAnimation = requestAnimationFrame(animateBarcode);
            }
        }

        function animateRFID() {
            rfidCtx.clearRect(0, 0, rfidCanvas.width, rfidCanvas.height);

            // 绘制RFID演示
            drawRFID();

            progress += 0.02;
            if (progress < 2) {
                rfidAnimation = requestAnimationFrame(animateRFID);
            }
        }

        function drawBarcode() {
            const centerX = barcodeCanvas.width / 2;
            const centerY = barcodeCanvas.height / 2;

            // 绘制条形码
            barcodeCtx.fillStyle = '#333';
            const barWidth = 3;
            const barCount = 20;
            const totalWidth = barCount * barWidth * 2;
            const startX = centerX - totalWidth / 2;

            for (let i = 0; i < barCount; i++) {
                if (i % 2 === 0) {
                    barcodeCtx.fillRect(startX + i * barWidth * 2, centerY - 40, barWidth, 80);
                }
            }

            // 绘制扫描器
            const scannerX = startX + (progress * totalWidth) % totalWidth;
            barcodeCtx.fillStyle = 'rgba(255, 0, 0, 0.7)';
            barcodeCtx.fillRect(scannerX - 2, centerY - 50, 4, 100);

            // 绘制扫描光线
            barcodeCtx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
            barcodeCtx.lineWidth = 2;
            barcodeCtx.beginPath();
            barcodeCtx.moveTo(scannerX, centerY - 80);
            barcodeCtx.lineTo(scannerX, centerY + 80);
            barcodeCtx.stroke();

            // 标签
            barcodeCtx.fillStyle = '#333';
            barcodeCtx.font = '14px Arial';
            barcodeCtx.textAlign = 'center';
            barcodeCtx.fillText('条形码扫描', centerX, centerY + 120);
            barcodeCtx.fillText('逐个读取，需要对准', centerX, centerY + 140);
        }

        function drawRFID() {
            const centerX = rfidCanvas.width / 2;
            const centerY = rfidCanvas.height / 2;

            // 绘制RFID读写器
            rfidCtx.fillStyle = '#4CAF50';
            rfidCtx.fillRect(centerX - 30, centerY - 15, 60, 30);
            rfidCtx.fillStyle = 'white';
            rfidCtx.font = '12px Arial';
            rfidCtx.textAlign = 'center';
            rfidCtx.fillText('RFID', centerX, centerY + 5);

            // 绘制多个RFID标签
            const tags = [
                { x: centerX - 80, y: centerY - 80, id: 'Tag1' },
                { x: centerX + 80, y: centerY - 80, id: 'Tag2' },
                { x: centerX - 80, y: centerY + 80, id: 'Tag3' },
                { x: centerX + 80, y: centerY + 80, id: 'Tag4' }
            ];

            tags.forEach((tag, index) => {
                const alpha = Math.sin(progress * Math.PI * 2 + index * Math.PI / 2) * 0.3 + 0.7;

                // 标签
                rfidCtx.fillStyle = `rgba(33, 150, 243, ${alpha})`;
                rfidCtx.fillRect(tag.x - 15, tag.y - 10, 30, 20);

                rfidCtx.fillStyle = 'white';
                rfidCtx.font = '10px Arial';
                rfidCtx.fillText(tag.id, tag.x, tag.y + 3);

                // 无线信号
                rfidCtx.strokeStyle = `rgba(76, 175, 80, ${alpha})`;
                rfidCtx.lineWidth = 2;
                rfidCtx.beginPath();
                rfidCtx.arc(tag.x, tag.y, 20 + Math.sin(progress * Math.PI * 4) * 10, 0, Math.PI * 2);
                rfidCtx.stroke();

                // 连接线
                rfidCtx.strokeStyle = `rgba(76, 175, 80, ${alpha * 0.5})`;
                rfidCtx.lineWidth = 1;
                rfidCtx.beginPath();
                rfidCtx.moveTo(tag.x, tag.y);
                rfidCtx.lineTo(centerX, centerY);
                rfidCtx.stroke();
            });

            // 标签
            rfidCtx.fillStyle = '#333';
            rfidCtx.font = '14px Arial';
            rfidCtx.textAlign = 'center';
            rfidCtx.fillText('RFID无线识别', centerX, centerY + 120);
            rfidCtx.fillText('同时读取多个标签', centerX, centerY + 140);
        }

        // 初始化进度
        updateProgress(25);
    </script>
</body>
</html>
