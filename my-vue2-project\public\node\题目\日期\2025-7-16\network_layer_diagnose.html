<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>网络分层诊断趣味学习</title>
  <style>
    body {
      background: #f8f9fa;
      font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      color: #222;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
    }
    .container {
      max-width: 700px;
      margin: 40px auto 0 auto;
      background: #fff;
      border-radius: 18px;
      box-shadow: 0 4px 32px rgba(0,0,0,0.07);
      padding: 40px 32px 32px 32px;
      display: flex;
      flex-direction: column;
      gap: 32px;
    }
    h1 {
      font-size: 2.2rem;
      font-weight: 700;
      margin: 0 0 12px 0;
      letter-spacing: 2px;
    }
    .question {
      font-size: 1.1rem;
      line-height: 1.7;
      background: #f3f6fa;
      border-radius: 10px;
      padding: 18px 20px;
      margin-bottom: 0;
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }
    .options {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 12px;
    }
    .option-btn {
      background: #f8f9fa;
      border: 1.5px solid #e0e3e8;
      border-radius: 8px;
      padding: 14px 18px;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.18s cubic-bezier(.4,0,.2,1);
      outline: none;
      text-align: left;
      position: relative;
      overflow: hidden;
    }
    .option-btn.selected {
      border-color: #007aff;
      background: #eaf4ff;
      color: #007aff;
    }
    .option-btn.correct {
      border-color: #2ecc40;
      background: #eaffea;
      color: #2ecc40;
    }
    .option-btn.wrong {
      border-color: #ff4d4f;
      background: #fff0f0;
      color: #ff4d4f;
    }
    .explain {
      background: #f6f7fb;
      border-left: 4px solid #007aff;
      padding: 18px 20px;
      border-radius: 0 10px 10px 0;
      font-size: 1rem;
      margin-top: 18px;
      animation: fadeIn 0.7s;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .canvas-demo {
      width: 100%;
      height: 220px;
      background: #f3f6fa;
      border-radius: 12px;
      margin: 0 auto 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }
    .canvas-btns {
      display: flex;
      gap: 12px;
      margin-top: 10px;
      justify-content: center;
    }
    .canvas-btn {
      background: #fff;
      border: 1.5px solid #e0e3e8;
      border-radius: 8px;
      padding: 7px 18px;
      font-size: 0.98rem;
      cursor: pointer;
      transition: all 0.18s cubic-bezier(.4,0,.2,1);
      outline: none;
    }
    .canvas-btn.active {
      border-color: #007aff;
      background: #eaf4ff;
      color: #007aff;
    }
    .game-section {
      margin-top: 30px;
      background: #f3f6fa;
      border-radius: 12px;
      padding: 24px 18px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      text-align: center;
    }
    .game-title {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .game-canvas {
      width: 100%;
      height: 180px;
      background: #fff;
      border-radius: 10px;
      margin: 0 auto 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    }
    .game-btn {
      margin-top: 14px;
      background: #007aff;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 8px 22px;
      font-size: 1rem;
      cursor: pointer;
      transition: background 0.18s;
    }
    .game-btn:active {
      background: #005bb5;
    }
    .footer {
      margin: 40px 0 10px 0;
      color: #aaa;
      font-size: 0.95rem;
      text-align: center;
    }
    @media (max-width: 800px) {
      .container { padding: 18px 4vw; }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>网络分层诊断趣味学习</h1>
    <div class="question" id="question-block">
      <b>题目：</b><br>
      网络故障需按照协议层次进行分层诊断， 找出故障原因并进行相应处理。查看端口状态、协议建立状态和 EIA 状态 属于（  ）诊断。<br><br>
      <b>选项：</b><br>
      A 物理层<br>
      B 数据链路层<br>
      C 网络层<br>
      D 应用层
    </div>
    <div class="options" id="options-block">
      <button class="option-btn" data-value="A">A 物理层</button>
      <button class="option-btn" data-value="B">B 数据链路层</button>
      <button class="option-btn" data-value="C">C 网络层</button>
      <button class="option-btn" data-value="D">D 应用层</button>
    </div>
    <div id="explain-block"></div>
    <div class="canvas-demo">
      <canvas id="layerCanvas" width="600" height="200"></canvas>
      <div style="position:absolute;top:10px;right:10px;z-index:2;">
        <div class="canvas-btns">
          <button class="canvas-btn active" id="btn-phy">物理层动画</button>
          <button class="canvas-btn" id="btn-link">数据链路层动画</button>
          <button class="canvas-btn" id="btn-net">网络层动画</button>
        </div>
      </div>
    </div>
    <div class="game-section">
      <div class="game-title">分层诊断小游戏：找出物理层故障</div>
      <div class="game-canvas" id="game-canvas-wrap">
        <canvas id="gameCanvas" width="600" height="150"></canvas>
      </div>
      <button class="game-btn" id="gameStartBtn">开始诊断</button>
      <div id="gameResult" style="margin-top:10px;font-size:1rem;"></div>
    </div>
  </div>
  <div class="footer">© 网络分层诊断趣味学习 | 极简风格 | 动画与交互演示 | 解释详尽</div>
  <script>
    // 选项交互与解释
    const options = document.querySelectorAll('.option-btn');
    const explainBlock = document.getElementById('explain-block');
    let answered = false;
    options.forEach(btn => {
      btn.onclick = () => {
        if(answered) return;
        answered = true;
        options.forEach(b => b.classList.remove('selected'));
        btn.classList.add('selected');
        // 答案A
        if(btn.dataset.value === 'A') {
          btn.classList.add('correct');
          explainBlock.innerHTML = `<div class='explain'><b>解析：</b><br>正确答案是 <b>A 物理层</b>。<br>物理层是OSI分层结构体系中最基础的一层，负责物理连接和信号传输。<br>查看端口状态、协议建立状态和EIA状态，都是判断物理连接是否正常的典型方法。<br>如使用show interface命令检查端口状态，属于物理层诊断。</div>`;
        } else {
          btn.classList.add('wrong');
          explainBlock.innerHTML = `<div class='explain'><b>解析：</b><br>正确答案是 <b>A 物理层</b>。<br>查看端口状态、协议建立状态和EIA状态，都是物理层的诊断内容。物理层主要关注物理连接和信号传输。</div>`;
        }
      }
    });
    // 分层动画演示
    const layerCanvas = document.getElementById('layerCanvas');
    const lctx = layerCanvas.getContext('2d');
    function drawLayerPhy() {
      lctx.clearRect(0,0,layerCanvas.width,layerCanvas.height);
      // 画物理层
      lctx.save();
      lctx.fillStyle = '#2ecc40';
      lctx.fillRect(80, 140, 440, 30);
      lctx.fillStyle = '#fff';
      lctx.font = 'bold 18px sans-serif';
      lctx.fillText('物理层', 270, 162);
      lctx.restore();
      // 画信号线
      lctx.save();
      lctx.strokeStyle = '#007aff';
      lctx.lineWidth = 3;
      lctx.beginPath();
      lctx.moveTo(120, 155);
      lctx.lineTo(480, 155);
      lctx.stroke();
      lctx.restore();
      // 画端口
      lctx.save();
      lctx.fillStyle = '#ffb300';
      lctx.fillRect(110, 145, 20, 20);
      lctx.fillRect(470, 145, 20, 20);
      lctx.fillStyle = '#fff';
      lctx.font = '12px sans-serif';
      lctx.fillText('端口', 112, 158);
      lctx.fillText('端口', 472, 158);
      lctx.restore();
      // 画EIA
      lctx.save();
      lctx.fillStyle = '#888';
      lctx.font = '13px sans-serif';
      lctx.fillText('EIA状态', 250, 135);
      lctx.restore();
    }
    function drawLayerLink() {
      lctx.clearRect(0,0,layerCanvas.width,layerCanvas.height);
      // 画数据链路层
      lctx.save();
      lctx.fillStyle = '#007aff';
      lctx.fillRect(80, 100, 440, 30);
      lctx.fillStyle = '#fff';
      lctx.font = 'bold 18px sans-serif';
      lctx.fillText('数据链路层', 250, 122);
      lctx.restore();
      // 画帧
      lctx.save();
      lctx.strokeStyle = '#2ecc40';
      lctx.strokeRect(200, 110, 200, 20);
      lctx.font = '13px sans-serif';
      lctx.fillStyle = '#2ecc40';
      lctx.fillText('帧（Frame）', 270, 125);
      lctx.restore();
    }
    function drawLayerNet() {
      lctx.clearRect(0,0,layerCanvas.width,layerCanvas.height);
      // 画网络层
      lctx.save();
      lctx.fillStyle = '#ffb300';
      lctx.fillRect(80, 60, 440, 30);
      lctx.fillStyle = '#fff';
      lctx.font = 'bold 18px sans-serif';
      lctx.fillText('网络层', 270, 82);
      lctx.restore();
      // 画IP包
      lctx.save();
      lctx.strokeStyle = '#007aff';
      lctx.strokeRect(250, 70, 100, 20);
      lctx.font = '13px sans-serif';
      lctx.fillStyle = '#007aff';
      lctx.fillText('IP包', 285, 85);
      lctx.restore();
    }
    document.getElementById('btn-phy').onclick = function() {
      this.classList.add('active');
      document.getElementById('btn-link').classList.remove('active');
      document.getElementById('btn-net').classList.remove('active');
      drawLayerPhy();
    };
    document.getElementById('btn-link').onclick = function() {
      this.classList.add('active');
      document.getElementById('btn-phy').classList.remove('active');
      document.getElementById('btn-net').classList.remove('active');
      drawLayerLink();
    };
    document.getElementById('btn-net').onclick = function() {
      this.classList.add('active');
      document.getElementById('btn-phy').classList.remove('active');
      document.getElementById('btn-link').classList.remove('active');
      drawLayerNet();
    };
    drawLayerPhy();
    // 游戏化诊断演示
    const gameCanvas = document.getElementById('gameCanvas');
    const gctx = gameCanvas.getContext('2d');
    let gameState = 0; // 0:未开始 1:诊断中 2:完成
    let portStatus = [false, false];
    function drawGame() {
      gctx.clearRect(0,0,gameCanvas.width,gameCanvas.height);
      // 画两台设备
      gctx.save();
      gctx.fillStyle = '#007aff';
      gctx.fillRect(80, 60, 60, 40);
      gctx.fillRect(460, 60, 60, 40);
      gctx.fillStyle = '#fff';
      gctx.font = 'bold 14px sans-serif';
      gctx.fillText('设备A', 90, 85);
      gctx.fillText('设备B', 470, 85);
      gctx.restore();
      // 画物理连接线
      gctx.save();
      gctx.strokeStyle = portStatus[0]&&portStatus[1] ? '#2ecc40' : '#ff4d4f';
      gctx.lineWidth = 4;
      gctx.beginPath();
      gctx.moveTo(140, 80);
      gctx.lineTo(460, 80);
      gctx.stroke();
      gctx.restore();
      // 画端口
      gctx.save();
      gctx.fillStyle = portStatus[0] ? '#2ecc40' : '#ffb300';
      gctx.fillRect(135, 75, 10, 10);
      gctx.fillStyle = portStatus[1] ? '#2ecc40' : '#ffb300';
      gctx.fillRect(455, 75, 10, 10);
      gctx.restore();
      // 端口状态说明
      gctx.save();
      gctx.font = '13px sans-serif';
      gctx.fillStyle = '#888';
      gctx.fillText('端口A状态: '+(portStatus[0]?'正常':'异常'), 80, 120);
      gctx.fillText('端口B状态: '+(portStatus[1]?'正常':'异常'), 400, 120);
      gctx.restore();
    }
    gameCanvas.addEventListener('click', function(e) {
      if(gameState!==1) return;
      const rect = gameCanvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      // 判断是否点击端口
      if(x>=135&&x<=145&&y>=75&&y<=85) {
        portStatus[0] = !portStatus[0];
        drawGame();
      } else if(x>=455&&x<=465&&y>=75&&y<=85) {
        portStatus[1] = !portStatus[1];
        drawGame();
      }
      if(portStatus[0]&&portStatus[1]) {
        gameState = 2;
        document.getElementById('gameResult').innerHTML = '<span style="color:#2ecc40">诊断成功！物理层连接恢复，端口状态正常，网络畅通！</span>';
      }
    });
    document.getElementById('gameStartBtn').onclick = function() {
      if(gameState===1) return;
      portStatus = [false, false];
      gameState = 1;
      document.getElementById('gameResult').innerHTML = '请点击端口切换状态，修复物理层故障';
      drawGame();
    };
    drawGame();
  </script>
</body>
</html> 