<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统可靠性互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .quiz-container {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
            animation: shake 0.6s ease-in-out;
        }

        .game-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 系统可靠性学习</h1>
            <p class="subtitle">通过动画和游戏理解系统可靠性的核心概念</p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 什么是系统可靠性？</h2>
            <div class="explanation">
                <strong>系统可靠性</strong>是指在规定的时间内和规定条件下，系统能有效地实现规定功能的能力。
                就像一台汽车，我们希望它在正常使用条件下，能够稳定运行而不出故障。
            </div>
            <div class="canvas-container">
                <canvas id="reliabilityCanvas" width="600" height="300"></canvas>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📊 可靠性度量指标</h2>
            <div class="canvas-container">
                <canvas id="metricsCanvas" width="700" height="400"></canvas>
            </div>
            <div class="game-controls">
                <button class="btn" onclick="showMetric('failure')">故障率演示</button>
                <button class="btn" onclick="showMetric('mttr')">平均失效等待时间</button>
                <button class="btn" onclick="showMetric('mtbf')">平均失效间隔时间</button>
                <button class="btn" onclick="showMetric('reliability')">可靠度演示</button>
            </div>
        </div>

        <div class="section quiz-container">
            <h2 class="section-title" style="color: white;">🎯 互动测试</h2>
            <div class="quiz-question" id="quizQuestion">
                系统（可靠性）是指在规定的时间内和规定条件下能有效地实现规定功能的能力。其中，（　）是系统在规定工作时间内无故障的概率。
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="options" id="options">
                <div class="option" onclick="selectOption(this, false)">A. 失效率</div>
                <div class="option" onclick="selectOption(this, false)">B. 平均失效等待时间</div>
                <div class="option" onclick="selectOption(this, false)">C. 平均失效间隔时间</div>
                <div class="option" onclick="selectOption(this, true)">D. 可靠度</div>
            </div>
            <div id="explanation" style="display: none; margin-top: 20px; padding: 20px; background: rgba(255,255,255,0.2); border-radius: 15px;">
                <strong>正确答案：D. 可靠度</strong><br>
                可靠度是系统在规定条件下、规定时间内不发生失效的概率，这正是题目中描述的"系统在规定工作时间内无故障的概率"。
            </div>
        </div>
    </div>

    <script>
        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 15; i++) {
                const circle = document.createElement('div');
                circle.className = 'floating-circle';
                circle.style.width = Math.random() * 60 + 20 + 'px';
                circle.style.height = circle.style.width;
                circle.style.left = Math.random() * 100 + '%';
                circle.style.top = Math.random() * 100 + '%';
                circle.style.animationDelay = Math.random() * 6 + 's';
                circle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(circle);
            }
        }

        // 可靠性概念动画
        function drawReliabilityAnimation() {
            const canvas = document.getElementById('reliabilityCanvas');
            const ctx = canvas.getContext('2d');
            let time = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制系统框
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 100, 150, 100);
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('系统', 125, 155);

                // 绘制输入输出
                ctx.fillStyle = '#4CAF50';
                ctx.beginPath();
                ctx.arc(250 + Math.sin(time * 0.1) * 10, 150, 15, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.fillText('输入', 250 + Math.sin(time * 0.1) * 10, 155);

                ctx.fillStyle = '#FF9800';
                ctx.beginPath();
                ctx.arc(400 + Math.sin(time * 0.1) * 10, 150, 15, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.fillText('输出', 400 + Math.sin(time * 0.1) * 10, 155);

                // 绘制箭头
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(280, 150);
                ctx.lineTo(370, 150);
                ctx.stroke();
                
                // 箭头头部
                ctx.beginPath();
                ctx.moveTo(365, 145);
                ctx.lineTo(370, 150);
                ctx.lineTo(365, 155);
                ctx.stroke();

                // 绘制可靠性指示器
                const reliability = (Math.sin(time * 0.05) + 1) * 0.4 + 0.2;
                ctx.fillStyle = reliability > 0.7 ? '#4CAF50' : reliability > 0.4 ? '#FF9800' : '#f44336';
                ctx.fillRect(450, 50, 100, 20);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(`可靠度: ${(reliability * 100).toFixed(1)}%`, 500, 65);

                time++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 度量指标动画
        let currentMetric = 'reliability';
        function drawMetricsAnimation() {
            const canvas = document.getElementById('metricsCanvas');
            const ctx = canvas.getContext('2d');
            let animationTime = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                switch(currentMetric) {
                    case 'failure':
                        drawFailureRate(ctx, animationTime);
                        break;
                    case 'mttr':
                        drawMTTR(ctx, animationTime);
                        break;
                    case 'mtbf':
                        drawMTBF(ctx, animationTime);
                        break;
                    case 'reliability':
                        drawReliability(ctx, animationTime);
                        break;
                }
                
                animationTime++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        function drawReliability(ctx, time) {
            ctx.fillStyle = '#333';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('可靠度 = 无故障概率', 350, 40);
            
            // 绘制概率曲线
            ctx.strokeStyle = '#4CAF50';
            ctx.lineWidth = 3;
            ctx.beginPath();
            for (let x = 50; x < 650; x += 2) {
                const t = (x - 50) / 600;
                const y = 200 - Math.exp(-t * 2) * 100 + Math.sin(time * 0.1 + t * 10) * 5;
                if (x === 50) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.stroke();
            
            // 绘制当前时间点
            const currentX = 50 + (time * 2) % 600;
            ctx.fillStyle = '#f44336';
            ctx.beginPath();
            ctx.arc(currentX, 200 - Math.exp(-(currentX - 50) / 600 * 2) * 100, 8, 0, Math.PI * 2);
            ctx.fill();
        }

        function showMetric(metric) {
            currentMetric = metric;
        }

        // 测试功能
        function selectOption(element, isCorrect) {
            const options = document.querySelectorAll('.option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (opt.textContent.includes('D. 可靠度')) {
                    opt.classList.add('correct');
                }
            });
            
            // 显示解释
            document.getElementById('explanation').style.display = 'block';
            
            // 更新进度条
            document.getElementById('progressFill').style.width = '100%';
            
            setTimeout(() => {
                if (isCorrect) {
                    alert('🎉 恭喜答对了！可靠度确实是系统在规定时间内无故障的概率！');
                } else {
                    alert('💡 答案是D哦！让我们重新理解一下可靠度的概念。');
                }
            }, 1000);
        }

        // 初始化
        window.onload = function() {
            createFloatingElements();
            drawReliabilityAnimation();
            drawMetricsAnimation();
        };

        // 其他度量指标绘制函数
        function drawFailureRate(ctx, time) {
            ctx.fillStyle = '#333';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('故障率 = 单位时间内的故障次数', 350, 40);
            
            // 绘制故障事件
            for (let i = 0; i < 10; i++) {
                const x = 70 + i * 60;
                const isFailed = Math.sin(time * 0.1 + i) > 0.3;
                ctx.fillStyle = isFailed ? '#f44336' : '#4CAF50';
                ctx.fillRect(x, 150, 40, 40);
                ctx.fillStyle = 'white';
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(isFailed ? '故障' : '正常', x + 20, 175);
            }
        }

        function drawMTTR(ctx, time) {
            ctx.fillStyle = '#333';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('平均失效等待时间 = 修复时间', 350, 40);
            
            // 绘制修复过程
            const repairProgress = (Math.sin(time * 0.05) + 1) / 2;
            ctx.fillStyle = '#f44336';
            ctx.fillRect(200, 150, 100, 40);
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(200, 150, 100 * repairProgress, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText(`修复进度: ${(repairProgress * 100).toFixed(0)}%`, 250, 175);
        }

        function drawMTBF(ctx, time) {
            ctx.fillStyle = '#333';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('平均失效间隔时间 = 两次故障间的时间', 350, 40);
            
            // 绘制时间轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(50, 200);
            ctx.lineTo(650, 200);
            ctx.stroke();
            
            // 绘制故障点
            const failurePoints = [150, 350, 550];
            failurePoints.forEach((x, i) => {
                ctx.fillStyle = '#f44336';
                ctx.beginPath();
                ctx.arc(x, 200, 8, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(`故障${i + 1}`, x, 230);
            });
        }
    </script>
</body>
</html>
