<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于构件的开发模型 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .blank {
            display: inline-block;
            min-width: 120px;
            height: 40px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            margin: 0 5px;
            position: relative;
            vertical-align: middle;
            background: rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .blank.filled {
            background: #667eea;
            color: white;
            border: 2px solid #667eea;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .option {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            user-select: none;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.2);
        }

        .option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            animation: wrongShake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }

        #gameCanvas {
            border-radius: 15px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            animation: fadeIn 1s ease-out;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.8;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .stage-list {
            list-style: none;
            margin: 20px 0;
        }

        .stage-item {
            background: white;
            margin: 10px 0;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            animation: slideInLeft 0.6s ease-out;
            animation-fill-mode: both;
        }

        .stage-item:nth-child(1) { animation-delay: 0.1s; }
        .stage-item:nth-child(2) { animation-delay: 0.2s; }
        .stage-item:nth-child(3) { animation-delay: 0.3s; }
        .stage-item:nth-child(4) { animation-delay: 0.4s; }
        .stage-item:nth-child(5) { animation-delay: 0.5s; }

        .start-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
        }

        .start-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🏗️ 基于构件的开发模型</h1>
            <p class="subtitle">通过动画和游戏学习软件开发的五个阶段</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                基于构件的开发模型包括软件的需求分析定义、<span class="blank" id="blank1"></span>、
                <span class="blank" id="blank2"></span>、<span class="blank" id="blank3"></span>，以及测试和发布5个顺序执行的阶段。
            </div>
            
            <div class="options-container">
                <div class="option" data-answer="体系结构设计">体系结构设计</div>
                <div class="option" data-answer="构件库建立">构件库建立</div>
                <div class="option" data-answer="应用软件构建">应用软件构建</div>
                <div class="option" data-answer="业务过程建模">业务过程建模</div>
            </div>
        </div>

        <div class="canvas-container">
            <h3>🎮 构件开发流程动画演示</h3>
            <canvas id="gameCanvas" width="800" height="400"></canvas>
            <br>
            <button class="start-btn" onclick="startAnimation()">开始动画演示</button>
            <button class="start-btn" onclick="startGame()">开始拖拽游戏</button>
        </div>

        <div class="explanation">
            <h3>📚 知识解析</h3>
            <p><strong>基于构件的开发模型</strong>是一种利用模块化方法将整个系统模块化，并在一定构件模型的支持下复用构件库中的一个或多个软件构件，通过组合手段高效率、高质量地构造应用软件系统的过程。</p>
            
            <p>这个模型融合了螺旋模型的许多特征，本质上是演化形的，开发过程是迭代的。</p>
            
            <h4>🔄 五个阶段详解：</h4>
            <ul class="stage-list">
                <li class="stage-item">1️⃣ <strong>需求分析定义</strong> - 明确系统需要实现的功能和性能要求</li>
                <li class="stage-item">2️⃣ <strong>体系结构设计</strong> - 设计系统的整体架构和模块划分</li>
                <li class="stage-item">3️⃣ <strong>构件库建立</strong> - 创建和管理可重用的软件构件</li>
                <li class="stage-item">4️⃣ <strong>应用软件构建</strong> - 使用构件库中的构件组装应用系统</li>
                <li class="stage-item">5️⃣ <strong>测试和发布</strong> - 验证系统功能并发布给用户使用</li>
            </ul>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            currentBlank: 0,
            correctAnswers: ['体系结构设计', '构件库建立', '应用软件构建'],
            userAnswers: [],
            score: 0
        };

        // Canvas 相关
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let gameMode = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            drawInitialCanvas();
        });

        function setupEventListeners() {
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.addEventListener('click', handleOptionClick);
            });
        }

        function handleOptionClick(e) {
            const selectedAnswer = e.target.dataset.answer;
            const correctAnswer = gameState.correctAnswers[gameState.currentBlank];
            
            if (selectedAnswer === correctAnswer) {
                e.target.classList.add('correct');
                fillBlank(gameState.currentBlank, selectedAnswer);
                gameState.userAnswers.push(selectedAnswer);
                gameState.currentBlank++;
                gameState.score += 20;
                updateProgress();
                
                setTimeout(() => {
                    e.target.style.display = 'none';
                }, 600);
                
                if (gameState.currentBlank >= 3) {
                    setTimeout(showCompletion, 1000);
                }
            } else {
                e.target.classList.add('wrong');
                setTimeout(() => {
                    e.target.classList.remove('wrong');
                }, 600);
            }
        }

        function fillBlank(index, text) {
            const blank = document.getElementById(`blank${index + 1}`);
            blank.textContent = text;
            blank.classList.add('filled');
        }

        function updateProgress() {
            const progress = (gameState.currentBlank / 3) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function showCompletion() {
            alert('🎉 恭喜完成！你已经掌握了基于构件开发模型的五个阶段！');
        }

        // Canvas 动画相关
        function drawInitialCanvas() {
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = '#333';
            ctx.font = '24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('点击按钮开始学习构件开发模型', canvas.width/2, canvas.height/2);
        }

        function startAnimation() {
            gameMode = false;
            animateStages();
        }

        function startGame() {
            gameMode = true;
            initDragGame();
        }

        function animateStages() {
            let currentStage = 0;
            const stages = [
                { name: '需求分析定义', color: '#ff6b6b', icon: '📋' },
                { name: '体系结构设计', color: '#4ecdc4', icon: '🏗️' },
                { name: '构件库建立', color: '#45b7d1', icon: '📦' },
                { name: '应用软件构建', color: '#96ceb4', icon: '🔧' },
                { name: '测试和发布', color: '#feca57', icon: '🚀' }
            ];

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 绘制已完成的阶段
                for (let i = 0; i <= currentStage && i < stages.length; i++) {
                    const x = 100 + i * 140;
                    const y = canvas.height / 2;
                    
                    // 绘制圆形
                    ctx.beginPath();
                    ctx.arc(x, y, 40, 0, 2 * Math.PI);
                    ctx.fillStyle = stages[i].color;
                    ctx.fill();
                    
                    // 绘制图标
                    ctx.font = '30px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillStyle = 'white';
                    ctx.fillText(stages[i].icon, x, y + 10);
                    
                    // 绘制文字
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillStyle = '#333';
                    ctx.fillText(stages[i].name, x, y + 70);
                    
                    // 绘制连接线
                    if (i < stages.length - 1) {
                        ctx.beginPath();
                        ctx.moveTo(x + 40, y);
                        ctx.lineTo(x + 100, y);
                        ctx.strokeStyle = '#ddd';
                        ctx.lineWidth = 3;
                        ctx.stroke();
                    }
                }
                
                currentStage++;
                if (currentStage < stages.length) {
                    setTimeout(animate, 1000);
                }
            }
            
            animate();
        }

        function initDragGame() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制拖拽区域
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(50, 50, 700, 100);
            ctx.strokeStyle = '#dee2e6';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 50, 700, 100);
            
            ctx.fillStyle = '#333';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('将正确的阶段拖拽到这里', canvas.width/2, 110);
            
            // 绘制可拖拽的选项
            const options = ['需求分析', '体系结构设计', '构件库建立', '应用软件构建', '测试发布', '业务建模'];
            options.forEach((option, index) => {
                const x = 50 + (index % 3) * 250;
                const y = 200 + Math.floor(index / 3) * 80;
                
                ctx.fillStyle = '#667eea';
                ctx.fillRect(x, y, 200, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(option, x + 100, y + 35);
            });
        }
    </script>
</body>
</html>
