<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Sustain</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
        :root {
            --primary-color: #4caf50; /* 绿色，代表生命与维持 */
            --secondary-color: #388e3c;
            --accent-color: #ffc107; /* 黄色，点缀 */
            --light-bg: #e8f5e9;
            --panel-bg: #ffffff;
            --text-color: #1b5e20;
        }
        body { font-family: 'Roboto', 'Noto Sans SC', sans-serif; background-color: #c8e6c9; color: var(--text-color); display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; overflow: hidden; }
        .container { display: flex; flex-direction: row; width: 95%; max-width: 1400px; height: 90vh; max-height: 800px; background-color: var(--panel-bg); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .word-panel { flex: 1; padding: 40px; display: flex; flex-direction: column; justify-content: flex-start; background-color: var(--light-bg); overflow-y: auto; }
        .word-panel h1 { font-size: 3.5em; color: var(--primary-color); }
        .word-panel .pronunciation { font-size: 1.5em; color: var(--secondary-color); margin-bottom: 20px; }
        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--secondary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: #2e7d32; }
        .breakdown-section { margin-top: 25px; padding: 20px; background-color: #ffffff; border-radius: 10px; }
        .morpheme-btn { margin: 5px; padding: 8px 15px; border: 2px solid var(--primary-color); border-radius: 20px; background-color: transparent; color: var(--primary-color); font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; }
        .morpheme-btn:hover, .morpheme-btn.active { background-color: var(--primary-color); color: white; transform: translateY(-2px); }
        
        .video-section { margin-top: 25px; padding: 20px; background-color: #ffffff; border-radius: 10px; }
        .video-placeholder { width: 100%; height: 150px; background-color: #e0e0e0; border-radius: 10px; display: flex; justify-content: center; align-items: center; color: #757575; text-align: center; margin-bottom: 15px; }
        .video-link-btn { display: block; width: 100%; text-align: center; padding: 10px; background-color: var(--primary-color); color: white; border-radius: 8px; text-decoration: none; font-weight: bold; transition: background-color 0.3s; }
        .video-link-btn:hover { background-color: var(--secondary-color); }

        .animation-panel { flex: 2; padding: 20px; display: flex; flex-direction: column; justify-content: center; align-items: center; position: relative; background: #fafafa; }
        .activity-title { font-size: 1.8em; color: var(--primary-color); margin-bottom: 15px; text-align: center; }
        .activity-wrapper { display: none; width: 100%; height: calc(100% - 100px); flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        .game-container { width: 100%; height: 100%; position: relative; display: flex; align-items: center; justify-content: center; border-radius: 15px; background: #f1f8e9; border: 1px solid #dcedc8; overflow: hidden; }
        .control-button { margin-top: 20px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; }
        
        /* Pillar Game */
        #heavy-block { width: 100px; height: 100px; background: #a1887f; position: absolute; top: 10%; left: calc(50% - 50px); animation: shake 2s infinite ease-in-out; }
        @keyframes shake { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(10px); } }
        #pillar { width: 40px; height: 0; background: var(--secondary-color); position: absolute; bottom: 0; left: calc(50% - 20px); transition: height 0.5s cubic-bezier(0.23, 1, 0.32, 1); }
        .pillar-game-run #pillar { height: 75%; }
        .pillar-game-run #heavy-block { animation-play-state: paused; transform: translateY(0); }

        /* Flow Game */
        #flow-canvas { background-color: #37474f; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>sustain</h1>
            <p class="pronunciation">[səˈsteɪn]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 维持，支撑</p>
                <p><strong>含义：</strong> 使（生命、力量、精神等）持续；支撑（重量）。</p>
                <div class="example">
                    <p><strong>例句1:</strong> These columns sustain the entire roof.</p>
                    <p><strong>翻译1:</strong> 这些柱子支撑着整个屋顶。</p>
                    <p><strong>例句2:</strong> Hope sustained them during their ordeal.</p>
                    <p><strong>翻译2:</strong> 在苦难中，希望支撑着他们。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>单词动画</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="pillar-game">动画: 支撑之柱</button>
                    <button class="morpheme-btn" data-activity="flow-game">互动: 生命之流</button>
                </div>
            </div>
            <div class="video-section">
                <h3>相关视频</h3>
                <div class="video-placeholder">您可以将视频嵌入代码(iframe)粘贴到此处进行学习。</div>
                <a href="https://www.douyin.com/search/sustain" target="_blank" class="video-link-btn">去抖音搜索"sustain"</a>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p>点击左侧按钮，体验"支撑"与"维持"的力量。</p></div>
            
            <div id="pillar-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="heavy-block"></div>
                    <div id="pillar"></div>
                </div>
                <button class="control-button" id="sustain-pillar-btn">支撑</button>
            </div>
            
            <div id="flow-game" class="activity-wrapper">
                <div class="game-container"><canvas id="flow-canvas"></canvas></div>
                <button class="control-button" id="sustain-flow-btn">维持供给</button>
            </div>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');
        let currentCleanup = null;

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                if (currentCleanup) currentCleanup();
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                const targetId = btn.dataset.activity;
                document.getElementById(targetId)?.classList.add('active');
                
                if (targetId === 'pillar-game') currentCleanup = setupPillarGame();
                else if (targetId === 'flow-game') currentCleanup = setupFlowGame();
                else currentCleanup = null;
            });
        });

        function setupPillarGame() {
            const btn = document.getElementById('sustain-pillar-btn');
            const gameContainer = document.querySelector('#pillar-game .game-container');
            const reset = () => gameContainer.classList.remove('pillar-game-run');
            btn.onclick = () => {
                if (gameContainer.classList.contains('pillar-game-run')) {
                    reset();
                } else {
                    gameContainer.classList.add('pillar-game-run');
                }
            };
            return reset;
        }

        function setupFlowGame() {
            const canvas = document.getElementById('flow-canvas');
            const btn = document.getElementById('sustain-flow-btn');
            if (!canvas) return null;

            const ctx = canvas.getContext('2d');
            let animationId;
            let particles = [];
            let isSustaining = true;

            const lifeForm = {
                x: 0, y: 0, width: 0, height: 30,
                life: 1, maxLife: 1,
                color: '',
                update: function() {
                    if (!isSustaining) this.life -= 0.002;
                    this.life = Math.max(0, this.life);
                    const lifeColor = Math.round(120 * this.life); // Green to Red
                    this.color = `hsl(${lifeColor}, 100%, 50%)`;
                },
                draw: function() {
                    ctx.fillStyle = this.color;
                    ctx.fillRect(this.x, this.y, this.width * this.life, this.height);
                    ctx.strokeStyle = '#fff';
                    ctx.strokeRect(this.x, this.y, this.width, this.height);
                }
            };

            class Particle {
                constructor() {
                    this.x = Math.random() * canvas.width; this.y = 0;
                    this.vx = 0; this.vy = Math.random() * 2 + 1;
                    this.radius = Math.random() * 3 + 2;
                    this.color = 'rgba(173, 216, 230, 0.7)';
                }
                update() { this.x += this.vx; this.y += this.vy; }
                draw() { ctx.fillStyle = this.color; ctx.beginPath(); ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2); ctx.fill(); }
            }

            function init() {
                const container = canvas.parentElement;
                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;
                lifeForm.x = 0;
                lifeForm.y = canvas.height - 30;
                lifeForm.width = canvas.width;
                lifeForm.life = 1;
                particles = [];
                isSustaining = true;
            }

            function animate() {
                ctx.fillStyle = 'rgba(55, 71, 79, 0.2)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                if (isSustaining && Math.random() < 0.3) particles.push(new Particle());

                particles.forEach((p, index) => {
                    p.update();
                    p.draw();
                    if (p.y > lifeForm.y) {
                        lifeForm.life = Math.min(lifeForm.maxLife, lifeForm.life + 0.05);
                        particles.splice(index, 1);
                    }
                });
                
                lifeForm.update();
                lifeForm.draw();
                animationId = requestAnimationFrame(animate);
            }

            btn.onclick = () => {
                isSustaining = !isSustaining;
                btn.textContent = isSustaining ? '停止供给' : '维持供给';
            };

            init();
            animate();

            return () => {
                if (animationId) cancelAnimationFrame(animationId);
            };
        }
    });
    </script>
</body>
</html> 