<template>
  <div class="multimodal-page">
    <PageHeader>
      <template #actions>
        <TextButton type="default" icon="el-icon-s-home" @click="goHome">返回首页</TextButton>
      </template>
    </PageHeader>
    <div class="container">
      <div class="content-wrapper fade-in">
        <div class="feature-icon">
          <i class="el-icon-picture-outline"></i>
        </div>
        <h2 class="title">多模态笔记</h2>
        <p class="subtitle">一个功能强大的新模块即将到来</p>
        <p class="description">
          我们正在努力开发一个全新的笔记体验，它将允许您无缝地整合文本、图片、待办事项列表等多种内容形式。
          <br>
          敬请期待，一个更丰富的创作空间即将解锁。
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MultimodalPage',
  methods: {
    goHome() {
      this.$router.push('/');
    }
  }
}
</script>

<style scoped>
.multimodal-page {
  text-align: center;
}

.content-wrapper {
  max-width: 600px;
  margin: 80px auto;
  padding: 40px;
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(52, 152, 219, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30px;
}

.feature-icon i {
  font-size: 40px;
  color: var(--primary-color);
}

.title {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
}

.subtitle {
  font-size: 18px;
  color: var(--text-light);
  margin-bottom: 30px;
  font-weight: 300;
}

.description {
  font-size: 16px;
  line-height: 1.8;
  color: var(--text-light);
}

.fade-in {
  animation: fadeIn 0.8s ease forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
</style> 