<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态图学习 - 复杂对象的生命周期</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            font-weight: 300;
            letter-spacing: 3px;
        }

        .subtitle {
            font-size: 1.4rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 300;
        }

        .main-demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(15px);
            animation: slideInUp 1s ease-out 0.3s both;
        }

        .demo-title {
            text-align: center;
            font-size: 2.2rem;
            color: #667eea;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .state-machine-canvas {
            width: 100%;
            height: 450px;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9ff 0%, #e8f0ff 100%);
            border: 3px solid #667eea;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .state-machine-canvas:hover {
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
            transform: translateY(-3px);
        }

        .object-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .example-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .example-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s;
        }

        .example-card:hover::before {
            left: 100%;
        }

        .example-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .example-card.active {
            background: rgba(102, 126, 234, 0.1);
            border-color: #667eea;
            transform: scale(1.05);
        }

        .example-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
            animation: bounce 2s infinite;
        }

        .example-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .example-desc {
            font-size: 1rem;
            color: #666;
            line-height: 1.5;
        }

        .controls-panel {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .control-btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .control-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .quiz-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(15px);
        }

        .quiz-title {
            text-align: center;
            font-size: 2.2rem;
            color: #667eea;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .quiz-question {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 35px;
            padding: 25px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 4px 12px;
            border-radius: 8px;
            font-weight: 700;
            color: #667eea;
            animation: pulse 2s infinite;
        }

        .quiz-options {
            display: grid;
            gap: 20px;
            margin-bottom: 35px;
        }

        .option {
            display: flex;
            align-items: center;
            padding: 20px 25px;
            background: white;
            border: 3px solid #e0e0e0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.5s;
        }

        .option:hover::after {
            left: 100%;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
        }

        .option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            transform: scale(1.02);
        }

        .option.correct {
            border-color: #4caf50;
            background: rgba(76, 175, 80, 0.15);
            animation: correctPulse 1s ease-out;
        }

        .option.incorrect {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.15);
            animation: incorrectShake 0.8s ease-out;
        }

        .option-label {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            font-weight: bold;
            font-size: 1.2rem;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .option.correct .option-label {
            background: #4caf50;
        }

        .option.incorrect .option-label {
            background: #f44336;
        }

        .option-text {
            font-size: 1.1rem;
            color: #333;
            font-weight: 500;
        }

        .quiz-result {
            text-align: center;
            padding: 35px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-top: 25px;
            display: none;
            animation: slideInUp 0.5s ease-out;
        }

        .result-correct {
            color: #4caf50;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .result-incorrect {
            color: #f44336;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .explanation {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 35px;
            margin-top: 35px;
            border-left: 6px solid #667eea;
        }

        .state-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 1.1rem;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-40px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(40px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes incorrectShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">状态图学习</h1>
            <p class="subtitle">State Diagram - 描述复杂对象的生命周期</p>
        </div>

        <div class="main-demo-section">
            <h2 class="demo-title">🔄 交互式状态机演示</h2>
            <div class="state-machine-canvas" id="stateMachineCanvas">
                <canvas id="stateCanvas"></canvas>
                <div class="state-indicator" id="stateIndicator">点击选择对象开始演示</div>
            </div>
            
            <div class="controls-panel">
                <button class="control-btn" onclick="triggerEvent('start')">▶️ 开始</button>
                <button class="control-btn" onclick="triggerEvent('pause')">⏸️ 暂停</button>
                <button class="control-btn" onclick="triggerEvent('stop')">⏹️ 停止</button>
                <button class="control-btn" onclick="triggerEvent('reset')">🔄 重置</button>
                <button class="control-btn" onclick="showAllStates()">🎯 显示全部状态</button>
            </div>
        </div>

        <div class="object-examples">
            <div class="example-card" onclick="selectObject('order')" data-object="order">
                <span class="example-icon">📦</span>
                <div class="example-title">订单对象</div>
                <div class="example-desc">从创建到完成的完整生命周期</div>
            </div>

            <div class="example-card" onclick="selectObject('user')" data-object="user">
                <span class="example-icon">👤</span>
                <div class="example-title">用户对象</div>
                <div class="example-desc">注册、激活、活跃、休眠状态</div>
            </div>

            <div class="example-card" onclick="selectObject('document')" data-object="document">
                <span class="example-icon">📄</span>
                <div class="example-title">文档对象</div>
                <div class="example-desc">草稿、审核、发布、归档流程</div>
            </div>

            <div class="example-card" onclick="selectObject('connection')" data-object="connection">
                <span class="example-icon">🔗</span>
                <div class="example-title">连接对象</div>
                <div class="example-desc">建立、传输、断开连接状态</div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="quiz-title">📝 知识测试</h2>

            <div class="quiz-question">
                面向对象的分析模型主要由顶层架构图、用例与用例图和（ ）构成；设计模型则包含以（ ）表示的软件体系机构图、以交互图表示的用例实现图、完整精确的类图、描述复杂对象的<span class="highlight">（请作答此空）</span>和用以描述流程化处理过程的活动图等。
            </div>

            <div class="quiz-options">
                <div class="option" data-answer="A" onclick="selectOption(this)">
                    <span class="option-label">A</span>
                    <span class="option-text">序列图</span>
                </div>
                <div class="option" data-answer="B" onclick="selectOption(this)">
                    <span class="option-label">B</span>
                    <span class="option-text">协作图</span>
                </div>
                <div class="option" data-answer="C" onclick="selectOption(this)">
                    <span class="option-label">C</span>
                    <span class="option-text">流程图</span>
                </div>
                <div class="option" data-answer="D" onclick="selectOption(this)">
                    <span class="option-label">D</span>
                    <span class="option-text">状态图</span>
                </div>
            </div>

            <div class="quiz-result" id="quizResult">
                <div class="result-content" id="resultContent"></div>
                <button class="control-btn" onclick="resetQuiz()">重新答题</button>
                <button class="control-btn" onclick="showDetailedExplanation()">详细解析</button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3>💡 学习提示</h3>
            <p>点击上方的对象卡片，观看不同复杂对象的状态图演示！状态图专门用于描述对象在其生命周期中的状态变化。</p>
        </div>
    </div>

    <script>
        // Canvas 设置
        const canvas = document.getElementById('stateCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;

        function resizeCanvas() {
            const container = document.getElementById('stateMachineCanvas');
            canvas.width = container.offsetWidth * window.devicePixelRatio;
            canvas.height = container.offsetHeight * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
            canvas.style.width = container.offsetWidth + 'px';
            canvas.style.height = container.offsetHeight + 'px';
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // 状态机状态
        let stateMachine = {
            currentObject: null,
            currentState: 'initial',
            states: [],
            transitions: [],
            particles: [],
            time: 0,
            animationSpeed: 1
        };

        // 状态类
        class State {
            constructor(name, x, y, color, isInitial = false, isFinal = false) {
                this.name = name;
                this.x = x;
                this.y = y;
                this.color = color;
                this.isInitial = isInitial;
                this.isFinal = isFinal;
                this.radius = 40;
                this.isActive = false;
                this.scale = 1;
                this.alpha = 1;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.alpha;

                // 绘制状态圆圈
                ctx.fillStyle = this.isActive ? '#667eea' : this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.radius * this.scale, 0, Math.PI * 2);
                ctx.fill();

                // 绘制边框
                ctx.strokeStyle = this.isActive ? '#fff' : 'rgba(255,255,255,0.8)';
                ctx.lineWidth = this.isActive ? 4 : 2;
                ctx.stroke();

                // 初始状态标记
                if (this.isInitial) {
                    ctx.fillStyle = '#333';
                    ctx.beginPath();
                    ctx.arc(this.x - this.radius - 20, this.y, 8, 0, Math.PI * 2);
                    ctx.fill();

                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(this.x - this.radius - 12, this.y);
                    ctx.lineTo(this.x - this.radius, this.y);
                    ctx.stroke();
                }

                // 最终状态标记
                if (this.isFinal) {
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.radius - 8, 0, Math.PI * 2);
                    ctx.stroke();
                }

                // 状态名称
                ctx.fillStyle = this.isActive ? '#fff' : '#333';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(this.name, this.x, this.y + 4);

                ctx.restore();
            }

            contains(x, y) {
                const dx = x - this.x;
                const dy = y - this.y;
                return dx * dx + dy * dy <= this.radius * this.radius;
            }
        }

        // 转换类
        class Transition {
            constructor(from, to, event, color = '#667eea') {
                this.from = from;
                this.to = to;
                this.event = event;
                this.color = color;
                this.alpha = 1;
                this.animationOffset = 0;
                this.isActive = false;
            }

            draw() {
                if (!this.from || !this.to) return;

                ctx.save();
                ctx.globalAlpha = this.alpha;

                // 计算箭头位置
                const dx = this.to.x - this.from.x;
                const dy = this.to.y - this.from.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const unitX = dx / distance;
                const unitY = dy / distance;

                const startX = this.from.x + unitX * this.from.radius;
                const startY = this.from.y + unitY * this.from.radius;
                const endX = this.to.x - unitX * this.to.radius;
                const endY = this.to.y - unitY * this.to.radius;

                // 绘制转换线
                ctx.strokeStyle = this.isActive ? '#ff6b6b' : this.color;
                ctx.lineWidth = this.isActive ? 4 : 2;
                ctx.setLineDash(this.isActive ? [] : [8, 4]);
                ctx.lineDashOffset = this.animationOffset;

                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();

                // 绘制箭头
                const arrowSize = 12;
                const arrowX = endX - unitX * arrowSize;
                const arrowY = endY - unitY * arrowSize;

                ctx.setLineDash([]);
                ctx.fillStyle = this.isActive ? '#ff6b6b' : this.color;
                ctx.beginPath();
                ctx.moveTo(endX, endY);
                ctx.lineTo(arrowX - unitY * arrowSize/2, arrowY + unitX * arrowSize/2);
                ctx.lineTo(arrowX + unitY * arrowSize/2, arrowY - unitX * arrowSize/2);
                ctx.closePath();
                ctx.fill();

                // 事件标签
                const midX = (startX + endX) / 2;
                const midY = (startY + endY) / 2;

                ctx.fillStyle = this.isActive ? '#ff6b6b' : '#333';
                ctx.font = '11px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(this.event, midX, midY - 10);

                ctx.restore();
            }

            update() {
                this.animationOffset += 2;
            }
        }

        // 粒子类
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = (Math.random() - 0.5) * 4;
                this.color = color;
                this.life = 1;
                this.decay = 0.02;
                this.size = Math.random() * 4 + 2;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.vx *= 0.98;
                this.vy *= 0.98;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 对象定义
        const objectDefinitions = {
            order: {
                name: '订单对象',
                states: [
                    new State('创建', 150, 100, '#ff6b6b', true),
                    new State('支付', 350, 100, '#4ecdc4'),
                    new State('处理', 550, 100, '#45b7d1'),
                    new State('发货', 350, 250, '#9b59b6'),
                    new State('完成', 550, 250, '#2ecc71', false, true),
                    new State('取消', 150, 250, '#e74c3c', false, true)
                ],
                transitions: [
                    { from: 0, to: 1, event: '提交支付' },
                    { from: 1, to: 2, event: '支付成功' },
                    { from: 2, to: 3, event: '开始发货' },
                    { from: 3, to: 4, event: '确认收货' },
                    { from: 0, to: 5, event: '用户取消' },
                    { from: 1, to: 5, event: '支付失败' }
                ]
            },
            user: {
                name: '用户对象',
                states: [
                    new State('注册', 200, 150, '#ff6b6b', true),
                    new State('激活', 400, 150, '#4ecdc4'),
                    new State('活跃', 600, 150, '#2ecc71'),
                    new State('休眠', 400, 300, '#f39c12'),
                    new State('禁用', 200, 300, '#e74c3c', false, true)
                ],
                transitions: [
                    { from: 0, to: 1, event: '邮箱验证' },
                    { from: 1, to: 2, event: '首次登录' },
                    { from: 2, to: 3, event: '长期未活动' },
                    { from: 3, to: 2, event: '重新登录' },
                    { from: 1, to: 4, event: '违规操作' },
                    { from: 2, to: 4, event: '违规操作' }
                ]
            },
            document: {
                name: '文档对象',
                states: [
                    new State('草稿', 150, 200, '#ff6b6b', true),
                    new State('审核中', 350, 150, '#f39c12'),
                    new State('已发布', 550, 150, '#2ecc71'),
                    new State('已归档', 550, 300, '#95a5a6', false, true),
                    new State('被拒绝', 150, 300, '#e74c3c')
                ],
                transitions: [
                    { from: 0, to: 1, event: '提交审核' },
                    { from: 1, to: 2, event: '审核通过' },
                    { from: 1, to: 4, event: '审核拒绝' },
                    { from: 4, to: 0, event: '重新编辑' },
                    { from: 2, to: 3, event: '归档' }
                ]
            },
            connection: {
                name: '连接对象',
                states: [
                    new State('断开', 200, 200, '#95a5a6', true),
                    new State('连接中', 400, 150, '#f39c12'),
                    new State('已连接', 600, 200, '#2ecc71'),
                    new State('传输中', 400, 250, '#3498db'),
                    new State('错误', 200, 300, '#e74c3c')
                ],
                transitions: [
                    { from: 0, to: 1, event: '发起连接' },
                    { from: 1, to: 2, event: '连接成功' },
                    { from: 2, to: 3, event: '开始传输' },
                    { from: 3, to: 2, event: '传输完成' },
                    { from: 2, to: 0, event: '主动断开' },
                    { from: 1, to: 4, event: '连接失败' },
                    { from: 4, to: 0, event: '重置' }
                ]
            }
        };

        // 主动画循环
        function animate() {
            const width = canvas.offsetWidth;
            const height = canvas.offsetHeight;

            ctx.clearRect(0, 0, width, height);

            // 绘制背景网格
            drawGrid(width, height);

            // 绘制状态
            stateMachine.states.forEach(state => {
                state.draw();
            });

            // 绘制转换
            stateMachine.transitions.forEach(transition => {
                transition.update();
                transition.draw();
            });

            // 更新和绘制粒子
            stateMachine.particles = stateMachine.particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });

            stateMachine.time += 0.02;
            animationId = requestAnimationFrame(animate);
        }

        function drawGrid(width, height) {
            ctx.save();
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.08)';
            ctx.lineWidth = 1;

            const gridSize = 25;
            for (let x = 0; x < width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            for (let y = 0; y < height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
            ctx.restore();
        }

        // 对象选择功能
        function selectObject(objectType) {
            // 清除其他卡片的active状态
            document.querySelectorAll('.example-card').forEach(card => {
                card.classList.remove('active');
            });

            // 设置当前卡片为active
            document.querySelector(`[data-object="${objectType}"]`).classList.add('active');

            const objDef = objectDefinitions[objectType];
            if (!objDef) return;

            stateMachine.currentObject = objectType;
            stateMachine.states = objDef.states;
            stateMachine.transitions = [];

            // 创建转换对象
            objDef.transitions.forEach(transDef => {
                const fromState = objDef.states[transDef.from];
                const toState = objDef.states[transDef.to];
                stateMachine.transitions.push(new Transition(fromState, toState, transDef.event));
            });

            // 设置初始状态
            stateMachine.currentState = 0;
            updateActiveState();

            // 更新状态指示器
            document.getElementById('stateIndicator').textContent =
                `${objDef.name} - 当前状态: ${objDef.states[0].name}`;

            // 添加粒子效果
            addParticles(objDef.states[0].x, objDef.states[0].y, objDef.states[0].color);

            updateExplanation('object', objectType);
        }

        // 事件触发功能
        function triggerEvent(eventType) {
            if (!stateMachine.currentObject || !stateMachine.states.length) {
                alert('请先选择一个对象！');
                return;
            }

            const currentState = stateMachine.states[stateMachine.currentState];
            let nextStateIndex = -1;
            let triggeredTransition = null;

            // 根据事件类型查找可能的转换
            switch(eventType) {
                case 'start':
                    // 查找从当前状态出发的第一个转换
                    for (let i = 0; i < stateMachine.transitions.length; i++) {
                        if (stateMachine.transitions[i].from === currentState) {
                            triggeredTransition = stateMachine.transitions[i];
                            nextStateIndex = stateMachine.states.indexOf(triggeredTransition.to);
                            break;
                        }
                    }
                    break;

                case 'pause':
                    // 暂停动画
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                        animationId = null;
                    }
                    return;

                case 'stop':
                    // 停止并回到初始状态
                    stateMachine.currentState = 0;
                    updateActiveState();
                    return;

                case 'reset':
                    // 重置到初始状态
                    stateMachine.currentState = 0;
                    updateActiveState();
                    if (!animationId) {
                        animate();
                    }
                    return;
            }

            // 执行状态转换
            if (nextStateIndex >= 0 && triggeredTransition) {
                // 高亮转换
                triggeredTransition.isActive = true;

                setTimeout(() => {
                    stateMachine.currentState = nextStateIndex;
                    updateActiveState();
                    triggeredTransition.isActive = false;

                    const newState = stateMachine.states[nextStateIndex];
                    addParticles(newState.x, newState.y, newState.color);

                    // 更新状态指示器
                    const objDef = objectDefinitions[stateMachine.currentObject];
                    document.getElementById('stateIndicator').textContent =
                        `${objDef.name} - 当前状态: ${newState.name}`;

                }, 500);
            }

            // 确保动画在运行
            if (!animationId) {
                animate();
            }
        }

        function updateActiveState() {
            stateMachine.states.forEach((state, index) => {
                state.isActive = (index === stateMachine.currentState);
            });
        }

        function showAllStates() {
            if (!stateMachine.currentObject) {
                alert('请先选择一个对象！');
                return;
            }

            // 依次激活所有状态
            let stateIndex = 0;
            const showNextState = () => {
                if (stateIndex < stateMachine.states.length) {
                    stateMachine.currentState = stateIndex;
                    updateActiveState();

                    const state = stateMachine.states[stateIndex];
                    addParticles(state.x, state.y, state.color);

                    const objDef = objectDefinitions[stateMachine.currentObject];
                    document.getElementById('stateIndicator').textContent =
                        `${objDef.name} - 展示状态: ${state.name}`;

                    stateIndex++;
                    setTimeout(showNextState, 1000);
                } else {
                    // 回到初始状态
                    stateMachine.currentState = 0;
                    updateActiveState();

                    const objDef = objectDefinitions[stateMachine.currentObject];
                    document.getElementById('stateIndicator').textContent =
                        `${objDef.name} - 演示完成，回到初始状态`;
                }
            };

            showNextState();
        }

        function addParticles(x, y, color) {
            for (let i = 0; i < 15; i++) {
                stateMachine.particles.push(new Particle(x, y, color));
            }
        }

        // 选择题功能
        let quizAnswered = false;
        let selectedAnswer = null;

        function selectOption(option) {
            if (quizAnswered) return;

            // 清除其他选择
            document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
            option.classList.add('selected');
            selectedAnswer = option.dataset.answer;

            setTimeout(() => {
                checkQuizAnswer();
            }, 800);
        }

        function checkQuizAnswer() {
            if (quizAnswered) return;

            quizAnswered = true;
            const correctAnswer = 'D';
            const options = document.querySelectorAll('.option');
            const resultDiv = document.getElementById('quizResult');
            const resultContent = document.getElementById('resultContent');

            options.forEach(option => {
                if (option.dataset.answer === correctAnswer) {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== correctAnswer) {
                    option.classList.add('incorrect');
                }
            });

            if (selectedAnswer === correctAnswer) {
                resultContent.innerHTML = `
                    <div class="result-correct">
                        🎉 恭喜答对了！
                    </div>
                    <p>正确答案是 <strong>D. 状态图</strong></p>
                    <p>您已经掌握了状态图在描述复杂对象中的重要作用！</p>
                `;

                // 添加庆祝粒子效果
                if (stateMachine.states.length > 0) {
                    stateMachine.states.forEach(state => {
                        addParticles(state.x, state.y, '#4caf50');
                    });
                }
            } else {
                resultContent.innerHTML = `
                    <div class="result-incorrect">
                        ❌ 答案不正确
                    </div>
                    <p>正确答案是 <strong>D. 状态图</strong></p>
                    <p>让我们重新学习状态图的概念和作用吧！</p>
                `;
            }

            resultDiv.style.display = 'block';
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function resetQuiz() {
            quizAnswered = false;
            selectedAnswer = null;

            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.classList.remove('selected', 'correct', 'incorrect');
            });

            document.getElementById('quizResult').style.display = 'none';
        }

        function showDetailedExplanation() {
            updateExplanation('detailed');
            document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
        }

        // 解析更新函数
        function updateExplanation(type, objectType = null) {
            const explanations = {
                object: {
                    order: {
                        title: '📦 订单对象状态图',
                        content: '订单对象展示了电商系统中订单的完整生命周期：从创建开始，经过支付、处理、发货，最终到达完成状态。同时也包含了取消的异常流程，这是典型的复杂对象状态变化。'
                    },
                    user: {
                        title: '👤 用户对象状态图',
                        content: '用户对象描述了用户账户的状态变化：注册后需要激活，激活后变为活跃用户，长期不活动会进入休眠状态，违规操作会被禁用。这展示了用户生命周期管理。'
                    },
                    document: {
                        title: '📄 文档对象状态图',
                        content: '文档对象展示了内容管理系统中文档的审核流程：从草稿开始，提交审核，审核通过后发布，最终归档。被拒绝的文档可以重新编辑，体现了工作流程的复杂性。'
                    },
                    connection: {
                        title: '🔗 连接对象状态图',
                        content: '连接对象描述了网络连接的状态变化：从断开状态发起连接，连接成功后可以进行数据传输，传输完成后回到连接状态，最终主动断开。连接失败会进入错误状态。'
                    }
                },
                detailed: {
                    title: '📚 详细解析',
                    content: `
                        <div style="text-align: left;">
                            <h4>🎯 题目解析</h4>
                            <p><strong>正确答案：D. 状态图</strong></p>

                            <h4>📖 核心知识点</h4>

                            <div style="margin: 20px 0; padding: 15px; background: rgba(102, 126, 234, 0.1); border-radius: 8px;">
                                <strong>🎨 设计模型的五大组成</strong><br>
                                • <strong>包图</strong>：表示软件体系架构<br>
                                • <strong>交互图</strong>：表示用例实现<br>
                                • <strong>类图</strong>：完整精确的类定义<br>
                                • <strong>状态图</strong>：描述复杂对象的状态变化<br>
                                • <strong>活动图</strong>：描述流程化处理过程
                            </div>

                            <div style="margin: 20px 0; padding: 15px; background: rgba(255, 107, 107, 0.1); border-radius: 8px;">
                                <strong>🔄 状态图的特点</strong><br>
                                • 描述对象的生命周期<br>
                                • 展示状态之间的转换<br>
                                • 包含触发事件和条件<br>
                                • 适用于复杂业务对象
                            </div>

                            <h4>🔍 为什么选择"状态图"？</h4>
                            <p>题目明确提到"描述复杂对象的（ ）"，这正是<strong>状态图</strong>的专门用途。状态图专门用于描述对象在其生命周期中的状态变化和转换过程。</p>

                            <h4>🚫 其他选项分析</h4>
                            <p>• <strong>序列图</strong>：描述对象间的交互序列，不是描述单个对象的状态<br>
                            • <strong>协作图</strong>：描述对象间的协作关系，重点在交互<br>
                            • <strong>流程图</strong>：描述处理流程，不是UML中的标准图形</p>

                            <h4>💡 记忆技巧</h4>
                            <p>记住：<strong>状态图 = 复杂对象的生命周期</strong>。就像人的一生有婴儿、儿童、青年、成年、老年等状态，复杂对象也有自己的状态变化过程。</p>

                            <h4>🎯 实际应用</h4>
                            <p>状态图广泛应用于：订单管理、用户生命周期、工作流程、设备状态管理、游戏角色状态等场景。</p>
                        </div>
                    `
                }
            };

            let explanation;
            if (type === 'object' && objectType && explanations.object[objectType]) {
                explanation = explanations.object[objectType];
            } else if (explanations[type]) {
                explanation = explanations[type];
            } else {
                explanation = {
                    title: '💡 学习提示',
                    content: '点击上方的对象卡片，观看不同复杂对象的状态图演示！状态图专门用于描述对象在其生命周期中的状态变化。'
                };
            }

            document.getElementById('explanation').innerHTML = `
                <h3>${explanation.title}</h3>
                <div>${explanation.content}</div>
            `;
        }

        // 画布点击事件
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检查是否点击了状态
            stateMachine.states.forEach((state, index) => {
                if (state.contains(x, y)) {
                    stateMachine.currentState = index;
                    updateActiveState();
                    addParticles(state.x, state.y, state.color);

                    const objDef = objectDefinitions[stateMachine.currentObject];
                    if (objDef) {
                        document.getElementById('stateIndicator').textContent =
                            `${objDef.name} - 当前状态: ${state.name}`;
                    }
                }
            });
        });

        // 初始化
        function init() {
            animate();
            updateExplanation('default');

            // 默认选择订单对象
            setTimeout(() => {
                selectObject('order');
            }, 1000);
        }

        // 启动应用
        init();
    </script>
</body>
</html>
