<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java 多线程支持 - 交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            padding: 30px 40px;
            width: 100%;
            max-width: 800px;
            transition: all 0.3s ease;
        }
        h1, h2 {
            color: #1a2a4c;
            border-left: 5px solid #007bff;
            padding-left: 15px;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 1.8em;
            margin-top: 40px;
        }
        p, li {
            font-size: 1.1em;
            line-height: 1.7;
            color: #555;
        }
        ul {
            list-style-type: '✓ ';
            padding-left: 20px;
        }
        .code-block {
            background-color: #2d2d2d;
            color: #f8f8f2;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Fira Code', 'Courier New', monospace;
            font-size: 1em;
            margin-top: 20px;
        }
        canvas {
            background-color: #e9ecef;
            border-radius: 8px;
            display: block;
            margin: 20px 0;
        }
        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            height: 120px;
            overflow-y: auto;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.95em;
            line-height: 1.5;
            color: #212529;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
        }
        .log-message {
            white-space: pre-wrap;
        }
        .log-message.main { color: #0056b3; }
        .log-message.worker { color: #28a745; }
    </style>
</head>
<body>

    <div class="container">
        <h1>5. 多线程支持</h1>
        <p>Java 是一个原生支持多线程的编程语言。这意味着我们可以很方便地编写程序，让它能够"同时"执行多个任务。</p>
        <ul>
            <li><strong>内置支持：</strong> Java 语言层面就提供了多线程功能，无需依赖外部库。</li>
            <li><strong>创建方式：</strong> 主要通过继承 <code>Thread</code> 类或实现 <code>Runnable</code> 接口来创建新线程。</li>
            <li><strong>同步机制：</strong> 提供 <code>synchronized</code> 和 <code>Lock</code> 等工具，来确保多个线程在访问共享数据时的安全。</li>
        </ul>

        <h2>交互动画：理解线程是如何"同时"工作的</h2>
        <p>下面的动画模拟了一个主线程和一个工作线程。点击"启动线程"按钮，观察它们如何并行执行任务。</p>
        <canvas id="threadCanvas" width="720" height="150"></canvas>
        <div class="controls">
            <button id="startButton">启动线程</button>
            <button id="resetButton">重置动画</button>
        </div>
        <div class="log-container" id="logContainer"></div>

        <h2>示例代码</h2>
        <p>这就像是为我们的"工作线程"编写了任务说明书。下面的代码就定义了一个简单的任务：在屏幕上打印一句话。</p>
        <div class="code-block">
<pre><code>public class MyThread implements Runnable {
    @Override
    public void run() {
        System.out.println("线程正在执行");
    }
}

// 如何使用它:
// MyThread myTask = new MyThread();
// Thread workerThread = new Thread(myTask);
// workerThread.start(); // 启动线程！</code></pre>
        </div>
        
        <h2>知识点小结</h2>
        <p><strong>🤔 到底什么是线程？</strong></p>
        <p>想象一下你在准备一顿大餐。你（主线程）需要一边煮汤，一边切菜。为了提高效率，你喊来一位朋友（工作线程）帮忙切菜。现在，煮汤和切菜两个任务就在"同时"进行了。程序中的线程也是如此，它就是程序内部的一条独立执行路径。</p>
        <p><strong>💡 为什么需要多线程？</strong></p>
        <p>就像请朋友帮忙能让你更快地准备好晚餐一样，多线程能让程序更高效、响应更迅速。比如，一个桌面应用可以在一个线程中下载文件，同时在另一个线程（主线程）中响应你的鼠标点击，界面就不会"卡住"。</p>

    </div>

    <script>
        const canvas = document.getElementById('threadCanvas');
        const ctx = canvas.getContext('2d');
        const startButton = document.getElementById('startButton');
        const resetButton = document.getElementById('resetButton');
        const logContainer = document.getElementById('logContainer');

        const trackY = { main: 50, worker: 100 };
        const trackWidth = canvas.width - 40;
        let progress = { main: 0, worker: 0 };
        let animationFrameId;
        let isRunning = false;

        function drawTrack(y, label, color) {
            ctx.fillStyle = '#ced4da';
            ctx.fillRect(20, y - 2.5, trackWidth, 5);
            ctx.fillStyle = color;
            ctx.font = 'bold 14px sans-serif';
            ctx.fillText(label, 20, y - 20);
        }

        function drawRunner(y, p, color) {
            const x = 20 + p * trackWidth;
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 10, 0, Math.PI * 2);
            ctx.fill();
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.stroke();
        }
        
        function addLog(message, threadType) {
            const logMessage = document.createElement('div');
            logMessage.className = `log-message ${threadType}`;
            logMessage.textContent = `[${threadType.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}`;
            logContainer.appendChild(logMessage);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function reset() {
            cancelAnimationFrame(animationFrameId);
            isRunning = false;
            progress = { main: 0, worker: 0 };
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawInitialState();
            startButton.disabled = false;
            logContainer.innerHTML = '';
            addLog("动画已重置，等待开始...", 'system');
        }

        function drawInitialState() {
            drawTrack(trackY.main, '主线程', '#007bff');
            drawTrack(trackY.worker, '工作线程 (Runnable)', '#28a745');
            drawRunner(trackY.main, progress.main, '#007bff');
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            drawTrack(trackY.main, '主线程', '#007bff');
            drawTrack(trackY.worker, '工作线程 (Runnable)', '#28a745');
            
            // Move main thread
            if (progress.main < 1) {
                progress.main += 0.005;
            }
            drawRunner(trackY.main, Math.min(1, progress.main), '#007bff');

            // Move worker thread if running
            if (isRunning) {
                if (progress.worker === 0) {
                     addLog("已创建！开始执行任务...", 'worker');
                }
                if (progress.worker < 1) {
                    progress.worker += 0.008; // a bit faster
                }
                drawRunner(trackY.worker, Math.min(1, progress.worker), '#28a745');
            }

            if(Math.random() < 0.01 && progress.main < 0.95) {
                addLog("正在执行其他任务...", 'main');
            }

            if(isRunning && Math.abs(progress.worker - 0.5) < 0.01) {
                if(!document.querySelector('.log-message.worker.exec')) {
                    const msg = document.createElement('div');
                    msg.className = 'log-message worker exec';
                    msg.textContent = `[WORKER] ${new Date().toLocaleTimeString()}: System.out.println("线程正在执行");`
                    logContainer.appendChild(msg);
                    logContainer.scrollTop = logContainer.scrollHeight;
                }
            }


            if (progress.main < 1 || (isRunning && progress.worker < 1)) {
                animationFrameId = requestAnimationFrame(animate);
            } else {
                 addLog("所有任务已完成。", 'system');
            }
        }

        startButton.addEventListener('click', () => {
            if (!isRunning) {
                isRunning = true;
                startButton.disabled = true;
                addLog("收到指令，正在启动新线程...", 'main');
                // Start animation loop if it's not already running
                if (!animationFrameId || progress.main >= 1) {
                    reset();
                    isRunning = true;
                    startButton.disabled = true;
                    addLog("收到指令，正在启动新线程...", 'main');
                    animate();
                }
            }
        });

        resetButton.addEventListener('click', reset);

        // Initial draw
        window.addEventListener('load', reset);

    </script>
</body>
</html> 