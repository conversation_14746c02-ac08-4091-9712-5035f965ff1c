<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业应用集成 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .explanation {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
        }

        .quiz-title {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 30px;
        }

        .question {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .option.selected {
            border-color: #fff;
            background: rgba(255,255,255,0.4);
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">企业应用集成</h1>
            <p class="subtitle">通过动画和交互学习企业系统整合的奥秘</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是企业应用集成？</h2>
            <div class="canvas-container">
                <canvas id="integrationCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <h3>🏢 企业应用集成的核心概念</h3>
                <p>企业应用集成是一个战略性方法，它将多个独立的信息系统连接起来，让它们能够实时交换信息和协调业务流程。就像搭建一座桥梁，连接原本孤立的"信息孤岛"。</p>
            </div>
            <button class="btn" onclick="startIntegrationAnimation()">🎬 观看集成动画</button>
        </div>

        <div class="section">
            <h2 class="section-title">四种集成类型</h2>
            <div class="canvas-container">
                <canvas id="typesCanvas" width="800" height="500"></canvas>
            </div>
            <div class="explanation">
                <h3>🔗 集成类型详解</h3>
                <p><strong>数据集成：</strong>让不同系统的数据能够互相访问和共享</p>
                <p><strong>API集成：</strong>通过接口让系统之间能够调用彼此的功能</p>
                <p><strong>界面集成：</strong>统一用户入口，让用户感觉在使用一个整体系统</p>
                <p><strong>过程集成：</strong>协调不同系统的业务流程，实现端到端的自动化</p>
            </div>
            <button class="btn" onclick="showIntegrationTypes()">🎯 探索集成类型</button>
        </div>

        <div class="quiz-section">
            <h2 class="quiz-title">🎮 知识挑战</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="question">
                <h3>企业应用集成是一个战略意义上的方法，它从服务和信息角度将多个信息系统绑定在一起，提供实时交换信息和影响流程的能力。（ ）提供企业之间的信息共享能力，（ ）在用户使用角度能够对集成系统产生一个"整体"的感觉。</h3>
                <div class="options">
                    <div class="option" onclick="selectOption(this, 'A')">A. API集成</div>
                    <div class="option" onclick="selectOption(this, 'B')">B. 数据集成</div>
                    <div class="option" onclick="selectOption(this, 'C')">C. 界面集成</div>
                    <div class="option" onclick="selectOption(this, 'D')">D. 过程集成</div>
                </div>
                <button class="btn" onclick="checkAnswer()" id="checkBtn" style="display:none;">检查答案</button>
                <div id="explanation" style="display:none; margin-top:20px; padding:20px; background:rgba(255,255,255,0.1); border-radius:10px;">
                    <h4>✅ 正确答案：C. 界面集成</h4>
                    <p>界面集成把各应用系统的界面集成起来，统一入口，使用户能够对集成系统产生一个整体的感觉。这正是题目中描述的"在用户使用角度能够对集成系统产生一个'整体'的感觉"。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAnswer = null;
        let animationRunning = false;

        // 企业应用集成动画
        function startIntegrationAnimation() {
            const canvas = document.getElementById('integrationCanvas');
            const ctx = canvas.getContext('2d');
            
            if (animationRunning) return;
            animationRunning = true;

            let frame = 0;
            const systems = [
                {x: 100, y: 150, name: 'CRM系统', color: '#FF6B6B'},
                {x: 300, y: 100, name: 'ERP系统', color: '#4ECDC4'},
                {x: 500, y: 150, name: '财务系统', color: '#45B7D1'},
                {x: 700, y: 100, name: 'HR系统', color: '#96CEB4'}
            ];

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制系统
                systems.forEach((system, index) => {
                    const pulse = Math.sin(frame * 0.1 + index) * 5;
                    
                    // 系统圆圈
                    ctx.beginPath();
                    ctx.arc(system.x, system.y, 40 + pulse, 0, 2 * Math.PI);
                    ctx.fillStyle = system.color;
                    ctx.fill();
                    ctx.strokeStyle = '#fff';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    
                    // 系统名称
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(system.name, system.x, system.y + 70);
                });

                // 绘制连接线（逐渐出现）
                if (frame > 60) {
                    const progress = Math.min((frame - 60) / 120, 1);
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 3;
                    ctx.setLineDash([5, 5]);
                    ctx.lineDashOffset = -frame * 0.5;
                    
                    for (let i = 0; i < systems.length - 1; i++) {
                        const start = systems[i];
                        const end = systems[i + 1];
                        const currentProgress = Math.max(0, Math.min(1, (progress - i * 0.2) * 2));
                        
                        if (currentProgress > 0) {
                            ctx.beginPath();
                            ctx.moveTo(start.x, start.y);
                            ctx.lineTo(
                                start.x + (end.x - start.x) * currentProgress,
                                start.y + (end.y - start.y) * currentProgress
                            );
                            ctx.stroke();
                        }
                    }
                }

                // 绘制中心集成平台
                if (frame > 180) {
                    const centerX = 400;
                    const centerY = 200;
                    const scale = Math.min((frame - 180) / 60, 1);
                    
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, 60 * scale, 0, 2 * Math.PI);
                    ctx.fillStyle = 'rgba(102, 126, 234, 0.8)';
                    ctx.fill();
                    ctx.strokeStyle = '#fff';
                    ctx.lineWidth = 4;
                    ctx.stroke();
                    
                    if (scale >= 1) {
                        ctx.fillStyle = '#fff';
                        ctx.font = 'bold 16px Microsoft YaHei';
                        ctx.textAlign = 'center';
                        ctx.fillText('集成平台', centerX, centerY);
                    }
                }

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(animate);
                } else {
                    animationRunning = false;
                }
            }

            animate();
        }

        // 集成类型展示
        function showIntegrationTypes() {
            const canvas = document.getElementById('typesCanvas');
            const ctx = canvas.getContext('2d');
            
            let currentType = 0;
            const types = [
                {
                    name: '数据集成',
                    color: '#FF6B6B',
                    description: '数据共享',
                    icon: '📊'
                },
                {
                    name: 'API集成',
                    color: '#4ECDC4',
                    description: '功能调用',
                    icon: '🔗'
                },
                {
                    name: '界面集成',
                    color: '#45B7D1',
                    description: '统一入口',
                    icon: '🖥️'
                },
                {
                    name: '过程集成',
                    color: '#96CEB4',
                    description: '流程协调',
                    icon: '⚙️'
                }
            ];

            function showType() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const type = types[currentType];
                const centerX = 400;
                const centerY = 250;
                
                // 绘制主圆圈
                ctx.beginPath();
                ctx.arc(centerX, centerY, 100, 0, 2 * Math.PI);
                ctx.fillStyle = type.color;
                ctx.fill();
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 5;
                ctx.stroke();
                
                // 绘制图标
                ctx.font = '48px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(type.icon, centerX, centerY + 15);
                
                // 绘制名称
                ctx.fillStyle = '#333';
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.fillText(type.name, centerX, centerY + 150);
                
                // 绘制描述
                ctx.font = '18px Microsoft YaHei';
                ctx.fillStyle = '#666';
                ctx.fillText(type.description, centerX, centerY + 180);
                
                // 绘制进度指示器
                for (let i = 0; i < types.length; i++) {
                    ctx.beginPath();
                    ctx.arc(300 + i * 50, 50, 8, 0, 2 * Math.PI);
                    ctx.fillStyle = i === currentType ? type.color : '#ddd';
                    ctx.fill();
                }
                
                currentType = (currentType + 1) % types.length;
                
                if (currentType === 0) {
                    setTimeout(showType, 2000);
                } else {
                    setTimeout(showType, 2000);
                }
            }
            
            showType();
        }

        // 选择答案
        function selectOption(element, answer) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            element.classList.add('selected');
            selectedAnswer = answer;
            document.getElementById('checkBtn').style.display = 'inline-block';
        }

        // 检查答案
        function checkAnswer() {
            const options = document.querySelectorAll('.option');
            const correctAnswer = 'C';
            
            options.forEach(option => {
                const answer = option.textContent.charAt(0);
                if (answer === correctAnswer) {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && answer !== correctAnswer) {
                    option.classList.add('wrong');
                }
            });
            
            document.getElementById('explanation').style.display = 'block';
            document.getElementById('checkBtn').style.display = 'none';
            
            // 更新进度条
            document.getElementById('progressFill').style.width = '100%';
        }

        // 页面加载完成后自动开始动画
        window.addEventListener('load', function() {
            setTimeout(startIntegrationAnimation, 1000);
            setTimeout(showIntegrationTypes, 3000);
        });

        // 添加画布点击交互
        document.getElementById('integrationCanvas').addEventListener('click', startIntegrationAnimation);
        document.getElementById('typesCanvas').addEventListener('click', showIntegrationTypes);
    </script>
</body>
</html>
