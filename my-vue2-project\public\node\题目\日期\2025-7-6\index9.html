<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ANSI/IEEE 1471-2000 互动学习</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            background-color: #f8f8f8;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        h1, h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 2.8em;
            font-weight: 700;
            letter-spacing: 1px;
            line-height: 1.2;
            margin-top: 40px;
            margin-bottom: 30px;
        }

        h2 {
            font-size: 2em;
            font-weight: 600;
            margin-top: 30px;
            margin-bottom: 25px;
        }

        p {
            font-size: 1.1em;
            line-height: 1.8;
            margin-bottom: 15px;
            text-align: justify;
            max-width: 800px;
        }

        canvas {
            border: 1px solid #eee;
            background-color: #fdfdfd;
            border-radius: 8px;
            margin-top: 30px;
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
            max-width: 100%;
            height: auto;
        }

        .controls {
            margin-top: 30px;
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .control-button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .control-button:hover {
            background-color: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .control-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        .quiz-section {
            margin-top: 40px;
            width: 100%;
            max-width: 900px;
            background-color: #fdfdfd;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        .question-text {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 25px;
            line-height: 1.6;
            color: #2c3e50;
            text-align: center;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .option-button {
            background-color: #e8f5e9;
            color: #388e3c;
            border: 2px solid #a5d6a7;
            padding: 15px 20px;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .option-button:hover {
            background-color: #dcedc8;
            border-color: #81c784;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .option-button.selected {
            background-color: #a5d6a7;
            border-color: #4CAF50;
            color: #fff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .option-button.correct {
            background-color: #4CAF50;
            border-color: #388e3c;
            color: white;
            box-shadow: 0 0 15px #4CAF5088;
            animation: pulse-correct 0.8s ease-out forwards;
        }

        .option-button.incorrect {
            background-color: #f44336;
            border-color: #d32f2f;
            color: white;
            box-shadow: 0 0 15px #f4433688;
            animation: shake 0.5s;
        }

        @keyframes pulse-correct {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0% { transform: translateX(0); }
            20% { transform: translateX(-5px); }
            40% { transform: translateX(5px); }
            60% { transform: translateX(-5px); }
            80% { transform: translateX(5px); }
            100% { transform: translateX(0); }
        }

        .feedback-section {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 1.2em;
            font-weight: 500;
            display: none; /* Initially hidden */
        }

        .feedback-correct {
            background-color: #e6ffe6;
            color: #2e7d32;
            border: 2px solid #a5d6a7;
        }

        .feedback-incorrect {
            background-color: #ffe6e6;
            color: #d32f2f;
            border: 2px solid #ef9a9a;
        }

        .explanation-section {
            margin-top: 40px;
            background-color: #f0f8ff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            line-height: 1.8;
            text-align: justify;
            max-width: 900px;
            display: none; /* Initially hidden */
        }

        .explanation-section h3 {
            color: #2c3e50;
            font-size: 1.6em;
            margin-bottom: 20px;
            text-align: center;
        }

        .explanation-section p {
            font-size: 1.1em;
            margin-bottom: 15px;
            color: #555;
        }

        .next-step-button {
            margin-top: 30px;
            background-color: #007bff;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .next-step-button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        .start-button {
            background-color: #6a1b9a; /* Deep Purple */
            color: white;
            padding: 20px 40px;
            border: none;
            border-radius: 10px;
            font-size: 1.5em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            margin-top: 50px;
            margin-bottom: 50px;
        }

        .start-button:hover {
            background-color: #4a148c; /* Darker Purple */
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            h1 {
                font-size: 2.2em;
            }
            h2 {
                font-size: 1.8em;
            }
            p {
                font-size: 1em;
            }
            .control-button, .option-button, .start-button {
                padding: 12px 25px;
                font-size: 1em;
            }
            .question-text {
                font-size: 1.1em;
            }
            .controls {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ANSI/IEEE 1471-2000 互动学习之旅</h1>
        <p>欢迎来到架构的世界！在这个互动页面中，我们将一起探索ANSI/IEEE 1471-2000标准，它定义了描述软件密集型系统架构的方式。准备好了吗？让我们开始一场有趣的学习冒险吧！</p>

        <button class="start-button" id="startButton">开始学习！</button>

        <div id="learningContent" style="display: none;">
            <canvas id="architectureCanvas" width="900" height="500"></canvas>
            <div class="controls">
                <button id="prevButton" class="control-button" disabled>上一步</button>
                <button id="nextButton" class="control-button">下一步</button>
            </div>

            <div class="quiz-section" id="quizSection" style="display: none;">
                <h2>问题环节</h2>
                <p class="question-text">
                    [ANSI/IEEE 1471-2000是对软件密集型系统的架构进行描述的标准。在该标准中， （ ）这一概念主要用于描述软件架构模型_。在此基础上，通常采用（ ）描述某个利益相关人（Stakeholder)所关注架构模型的某一方面。（ ）则是对所有利益相关人关注点的响应和回答。
                </p>
                <div class="options-grid">
                    <button class="option-button" data-answer="A">A. 上下文</button>
                    <button class="option-button" data-answer="B">B. 架构风格</button>
                    <button class="option-button" data-answer="C">C. 组件</button>
                    <button class="option-button" data-answer="D">D. 视图</button>
                </div>
                <div class="feedback-section" id="quizFeedback"></div>
                <button id="showExplanationButton" class="next-step-button" style="display: none;">查看解析</button>
            </div>

            <div class="explanation-section" id="explanationSection">
                <h3>题目解析</h3>
                <p>本题主要考查ANSI/IEEE 1471-2000标准的相关知识。在该标准中，系统是为了达成利益相关人（Stakeholder)的某些使命（Mission)，在特定环境(Environment)中构建的。</p>
                <p>每一个系统都有一个架构（Architecture)。架构是对所有利益相关人关注点（Concern)的响应和回答，通过架构描述（Architecture Description)来说明。每一个利益相关人都有各自的关注点。这些关注点是指对其重要的，与系统的开发、运营或其他方面相关的利益。</p>
                <p>架构描述（Architecture Description)本质上是多视图的。每一个视图（View)是从一个特定的视角（Viewpoint)来表述架构的某一个独立的方面。试图用一个单一的视图来覆盖所有的关注点当然是最好的，但实际上这种表述方式将很难理解。</p>
                <p>视角（Viewpoint)的选择，基于要解决哪些利益相关人的哪些关注点。它决定了用来创建视图的语言、符号和模型等，以及任何与创建视图相关的建模方法或者分析技术。</p>
                <p>一个视图（View)包括一个或者多个架构模型（Model)，一个模型也可能参与多个视图。模型较文本的表述的好处在于，可以更容易的可视化、检查、分析、管理和集成。</p>
                <p>因此，题目的空缺处应分别为：<br/>
                （<span style="font-weight: bold; color: #4CAF50;">视图</span>）这一概念主要用于描述软件架构模型。<br/>
                在此基础上，通常采用（<span style="font-weight: bold; color: #4CAF50;">视角</span>）描述某个利益相关人（Stakeholder)所关注架构模型的某一方面。<br/>
                （<span style="font-weight: bold; color: #4CAF50;">架构</span>）则是对所有利益相关人关注点的响应和回答。</p>
                <p style="font-weight: bold; color: #4CAF50; text-align: center; margin-top: 20px;">正确答案：D</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');
        const nextButton = document.getElementById('nextButton');
        const prevButton = document.getElementById('prevButton');
        const startButton = document.getElementById('startButton');
        const learningContent = document.getElementById('learningContent');
        const quizSection = document.getElementById('quizSection');
        const quizFeedback = document.getElementById('quizFeedback');
        const optionsButtons = document.querySelectorAll('.option-button');
        const showExplanationButton = document.getElementById('showExplanationButton');
        const explanationSection = document.getElementById('explanationSection');

        let currentStep = 0;
        const totalSteps = 7; // 步骤总数，包括最后的题目

        // 动画参数
        let animationProgress = 0;
        let animationDuration = 60; // 动画帧数
        let animationId;
        let isAnimating = false;

        // 辅助绘图函数
        function roundRect(x, y, width, height, radius, fill, stroke) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.arcTo(x + width, y, x + width, y + height, radius);
            ctx.arcTo(x + width, y + height, x, y + height, radius);
            ctx.arcTo(x, y + height, x, y, radius);
            ctx.arcTo(x, y, x + width, y, radius);
            ctx.closePath();
            if (fill) {
                ctx.fill();
            }
            if (stroke) {
                ctx.stroke();
            }
        }

        function drawArrow(fromX, fromY, toX, toY, color = '#666', headSize = 10, lineWidth = 2) {
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = lineWidth;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // Arrowhead
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headSize * Math.cos(angle - Math.PI / 6), toY - headSize * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - headSize * Math.cos(angle + Math.PI / 6), toY - headSize * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fill();
        }

        function drawText(text, x, y, color = '#333', fontSize = 20, align = 'center', baseline = 'middle') {
            ctx.fillStyle = color;
            ctx.font = `${fontSize}px 'Noto Sans SC'`;
            ctx.textAlign = align;
            ctx.textBaseline = baseline;
            ctx.fillText(text, x, y);
        }

        function animate(drawFunction, duration, onComplete) {
            if (isAnimating) {
                cancelAnimationFrame(animationId);
            }
            isAnimating = true;
            let start = null;

            function step(timestamp) {
                if (!start) start = timestamp;
                const progress = (timestamp - start) / (duration * 1000 / 60); // Normalize to frames
                animationProgress = Math.min(progress, 1);

                ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear previous frame
                drawFunction();

                if (progress < 1) {
                    animationId = requestAnimationFrame(step);
                } else {
                    isAnimating = false;
                    if (onComplete) onComplete();
                }
            }
            animationId = requestAnimationFrame(step);
        }

        // 概念绘制函数
        function drawStep(stepNumber) {
            ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear canvas for new step

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            const stakeholderPos = { x: 100, y: centerY };
            const concernPos = { x: 280, y: centerY - 50 };
            const viewpointPos = { x: 450, y: centerY + 50 };
            const viewPos = { x: 650, y: centerY - 50 };
            const modelPos = { x: 800, y: centerY + 50 };
            const architectureDescPos = { x: centerX, y: 50 }; // For overall architecture description

            // Step 0: 初始状态，介绍标准
            if (stepNumber >= 0) {
                drawText("ANSI/IEEE 1471-2000 标准", centerX, centerY - 80, '#2c3e50', 36);
                drawText("描述软件密集型系统的架构", centerX, centerY + 20, '#555', 24);
                drawText("理解复杂系统的蓝图", centerX, centerY + 70, '#555', 20);
            }

            // Step 1: 利益相关人 (Stakeholder)
            if (stepNumber >= 1) {
                ctx.save();
                ctx.globalAlpha = stepNumber === 1 ? animationProgress : 1;
                drawText("利益相关人 (Stakeholder)", stakeholderPos.x, stakeholderPos.y - 60, '#3498db', 24);
                drawText("👨‍💻", stakeholderPos.x, stakeholderPos.y, '#3498db', 80); // Icon for Stakeholder
                ctx.restore();
            }

            // Step 2: 关注点 (Concern)
            if (stepNumber >= 2) {
                ctx.save();
                ctx.globalAlpha = stepNumber === 2 ? animationProgress : 1;
                drawArrow(stakeholderPos.x + 40, stakeholderPos.y - 20, concernPos.x - 60, concernPos.y + 20, '#3498db');
                ctx.fillStyle = 'rgba(255, 255, 204, 0.8)';
                ctx.strokeStyle = '#f1c40f';
                ctx.lineWidth = 2;
                roundRect(concernPos.x - 80, concernPos.y - 40, 180, 80, 15, true, true);
                drawText("关注点 (Concern)", concernPos.x, concernPos.y - 10, '#e67e22', 20);
                drawText("我的需求？", concernPos.x, concernPos.y + 20, '#e67e22', 16);
                ctx.restore();
            }

            // Step 3: 视角 (Viewpoint)
            if (stepNumber >= 3) {
                ctx.save();
                ctx.globalAlpha = stepNumber === 3 ? animationProgress : 1;
                drawArrow(concernPos.x + 30, concernPos.y + 20, viewpointPos.x - 70, viewpointPos.y, '#e67e22');
                ctx.fillStyle = 'rgba(204, 255, 204, 0.8)';
                ctx.strokeStyle = '#27ae60';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(viewpointPos.x, viewpointPos.y, 60, 0, Math.PI * 2);
                ctx.fill();
                ctx.stroke();
                drawText("视角 (Viewpoint)", viewpointPos.x, viewpointPos.y - 20, '#27ae60', 20);
                drawText("🔎", viewpointPos.x, viewpointPos.y + 20, '#27ae60', 40); // Magnifying glass icon
                drawText("如何看？", viewpointPos.x, viewpointPos.y + 50, '#27ae60', 16);
                ctx.restore();
            }

            // Step 4: 视图 (View)
            if (stepNumber >= 4) {
                ctx.save();
                ctx.globalAlpha = stepNumber === 4 ? animationProgress : 1;
                drawArrow(viewpointPos.x + 50, viewpointPos.y, viewPos.x - 80, viewPos.y, '#27ae60');
                ctx.fillStyle = 'rgba(204, 204, 255, 0.8)';
                ctx.strokeStyle = '#9b59b6';
                ctx.lineWidth = 2;
                roundRect(viewPos.x - 100, viewPos.y - 60, 200, 120, 15, true, true);
                drawText("视图 (View)", viewPos.x, viewPos.y - 30, '#9b59b6', 24);
                drawText("架构的某一方面描述", viewPos.x, viewPos.y + 10, '#9b59b6', 16);
                drawText("📄", viewPos.x - 50, viewPos.y + 50, '#9b59b6', 30);
                ctx.restore();
            }

            // Step 5: 架构模型 (Architecture Model)
            if (stepNumber >= 5) {
                ctx.save();
                ctx.globalAlpha = stepNumber === 5 ? animationProgress : 1;
                drawArrow(viewPos.x + 80, viewPos.y + 20, modelPos.x - 50, modelPos.y - 20, '#9b59b6');
                ctx.fillStyle = 'rgba(255, 230, 230, 0.8)';
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 2;
                roundRect(modelPos.x - 70, modelPos.y - 40, 140, 80, 10, true, true);
                drawText("架构模型 (Model)", modelPos.x, modelPos.y - 10, '#e74c3c', 18);
                drawText("📊", modelPos.x, modelPos.y + 20, '#e74c3c', 30);
                ctx.restore();
            }

            // Step 6: 架构描述 (Architecture Description) - 整合多个视图
            if (stepNumber >= 6) {
                ctx.save();
                ctx.globalAlpha = stepNumber === 6 ? animationProgress : 1;
                drawText("架构描述 (Architecture Description)", architectureDescPos.x, architectureDescPos.y - 20, '#1abc9c', 28);
                ctx.fillStyle = 'rgba(204, 255, 204, 0.8)'; // Greenish for overall coherence
                ctx.strokeStyle = '#1abc9c';
                ctx.lineWidth = 3;
                roundRect(architectureDescPos.x - 200, architectureDescPos.y + 20, 400, 100, 20, true, true);
                drawText("是所有利益相关人关注点的响应", architectureDescPos.x, architectureDescPos.y + 60, '#1abc9c', 20);

                // Simulate multiple views contributing to Architecture Description
                const view1 = { x: centerX - 150, y: centerY + 120 };
                const view2 = { x: centerX + 150, y: centerY + 120 };

                // Draw simplified views
                ctx.fillStyle = 'rgba(204, 204, 255, 0.8)';
                ctx.strokeStyle = '#9b59b6';
                ctx.lineWidth = 2;
                roundRect(view1.x - 70, view1.y - 40, 140, 80, 10, true, true);
                drawText("视图A", view1.x, view1.y, '#9b59b6', 18);

                roundRect(view2.x - 70, view2.y - 40, 140, 80, 10, true, true);
                drawText("视图B", view2.x, view2.y, '#9b59b6', 18);

                drawArrow(view1.x, view1.y - 30, architectureDescPos.x - 80, architectureDescPos.y + 20, '#1abc9c', 8, 2);
                drawArrow(view2.x, view2.y - 30, architectureDescPos.x + 80, architectureDescPos.y + 20, '#1abc9c', 8, 2);
                ctx.restore();
            }
        }

        // 更新按钮状态
        function updateButtons() {
            prevButton.disabled = currentStep === 0;
            nextButton.disabled = currentStep >= totalSteps;
            if (currentStep === totalSteps) {
                nextButton.style.display = 'none';
                quizSection.style.display = 'block';
            } else {
                nextButton.style.display = 'block';
                quizSection.style.display = 'none';
                explanationSection.style.display = 'none';
                showExplanationButton.style.display = 'none';
                quizFeedback.style.display = 'none';
                optionsButtons.forEach(btn => {
                    btn.classList.remove('selected', 'correct', 'incorrect');
                    btn.disabled = false;
                });
            }
        }

        // 按钮点击事件
        nextButton.addEventListener('click', () => {
            if (currentStep < totalSteps) {
                currentStep++;
                animate(() => drawStep(currentStep), animationDuration, updateButtons);
            }
        });

        prevButton.addEventListener('click', () => {
            if (currentStep > 0) {
                currentStep--;
                animate(() => drawStep(currentStep), animationDuration, updateButtons);
            }
        });

        startButton.addEventListener('click', () => {
            startButton.style.display = 'none';
            learningContent.style.display = 'flex';
            currentStep = 0; // Start from step 0
            animate(() => drawStep(currentStep), animationDuration, updateButtons);
        });

        // 测验逻辑
        let selectedAnswer = null;
        const correctAnswer = 'D';

        optionsButtons.forEach(button => {
            button.addEventListener('click', () => {
                optionsButtons.forEach(btn => btn.classList.remove('selected', 'correct', 'incorrect'));
                button.classList.add('selected');
                selectedAnswer = button.dataset.answer;

                quizFeedback.style.display = 'block';
                showExplanationButton.style.display = 'block';

                if (selectedAnswer === correctAnswer) {
                    button.classList.add('correct');
                    quizFeedback.className = 'feedback-section feedback-correct';
                    quizFeedback.textContent = '恭喜你，回答正确！🎉';
                } else {
                    button.classList.add('incorrect');
                    quizFeedback.className = 'feedback-section feedback-incorrect';
                    quizFeedback.textContent = '很遗憾，回答错误。再想想看哦！🤔';
                }
                // Disable all options after selection
                optionsButtons.forEach(btn => btn.disabled = true);
            });
        });

        showExplanationButton.addEventListener('click', () => {
            explanationSection.style.display = 'block';
            showExplanationButton.style.display = 'none';
            // Scroll to explanation
            explanationSection.scrollIntoView({ behavior: 'smooth' });
        });

        // Initial setup
        // Initial call to draw the first step (or empty canvas if startButton is present)
        // If startButton is visible, the canvas and controls are hidden, so no need to draw initially.
        // The first draw happens when startButton is clicked.
    </script>
</body>
</html> 