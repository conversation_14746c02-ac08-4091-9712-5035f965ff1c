<template>
  <button
    :class="['text-button', `text-button--${type}`]"
    @click="handleClick"
  >
    <i v-if="icon" :class="icon"></i>
    <span v-if="$slots.default" class="text-button-text">
      <slot></slot>
    </span>
  </button>
</template>

<script>
export default {
  name: 'TextButton',
  props: {
    type: {
      type: String,
      default: 'default' // primary, danger, default
    },
    icon: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleClick(evt) {
      this.$emit('click', evt);

      // Ripple effect
      const button = evt.currentTarget;
      const circle = document.createElement("span");
      const diameter = Math.max(button.clientWidth, button.clientHeight);
      const radius = diameter / 2;

      circle.style.width = circle.style.height = `${diameter}px`;
      circle.style.left = `${evt.clientX - button.getBoundingClientRect().left - radius}px`;
      circle.style.top = `${evt.clientY - button.getBoundingClientRect().top - radius}px`;
      circle.classList.add("ripple");

      const ripple = button.getElementsByClassName("ripple")[0];
      if (ripple) {
        ripple.remove();
      }

      button.appendChild(circle);
    }
  }
}
</script>

<style scoped>
.text-button {
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  background-color: transparent;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: var(--text-color);
}

.text-button-text::after {
  content: '';
  position: absolute;
  width: 100%;
  transform: scaleX(0);
  height: 1px;
  bottom: 0;
  left: 0;
  background-color: var(--text-color);
  transform-origin: bottom right;
  transition: transform 0.3s ease-out;
}

.text-button:hover .text-button-text::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}


.text-button--primary {
  color: var(--primary-color);
}
.text-button--primary .text-button-text::after {
  background-color: var(--primary-color);
}


.text-button--danger {
  color: #f56c6c;
}
.text-button--danger .text-button-text::after {
  background-color: #f56c6c;
}

.text-button:disabled {
  color: var(--text-light);
  cursor: not-allowed;
  opacity: 0.5;
}

.text-button:disabled .text-button-text::after {
  display: none;
}

/* Ripple effect */
.text-button .ripple {
  position: absolute;
  border-radius: 50%;
  transform: scale(0);
  animation: ripple 600ms linear;
  background-color: rgba(255, 255, 255, 0.7);
}

.text-button--primary .ripple {
  background-color: rgba(52, 152, 219, 0.2);
}

.text-button--danger .ripple {
  background-color: rgba(245, 108, 108, 0.2);
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
</style> 