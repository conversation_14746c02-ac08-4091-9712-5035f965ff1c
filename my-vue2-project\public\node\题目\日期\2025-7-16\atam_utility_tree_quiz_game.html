<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATAM效用树结构趣味游戏</title>
    <style>
        body {
            background: #f7f8fa;
            font-family: 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif;
            color: #222;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 40px 20px 60px 20px;
        }
        .title {
            font-size: 2.4rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 18px;
            letter-spacing: 2px;
        }
        .desc {
            text-align: center;
            color: #888;
            font-size: 1.1rem;
            margin-bottom: 40px;
        }
        .quiz-box {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.06);
            padding: 32px 20px 24px 20px;
            margin-bottom: 36px;
            transition: box-shadow 0.3s;
        }
        .quiz-q {
            font-size: 1.18rem;
            margin-bottom: 22px;
            text-align: center;
            line-height: 1.7;
        }
        .quiz-options {
            display: flex;
            flex-direction: column;
            gap: 16px;
            align-items: center;
        }
        .quiz-option {
            background: #f5f6fa;
            border: 2px solid #e3e6ee;
            border-radius: 12px;
            padding: 14px 32px;
            font-size: 1.08rem;
            cursor: pointer;
            min-width: 320px;
            text-align: left;
            transition: all 0.2s;
            position: relative;
        }
        .quiz-option.selected {
            border-color: #6c63ff;
            background: #e6e7ff;
        }
        .quiz-option.correct {
            border-color: #4caf50;
            background: #e8f5e9;
            color: #388e3c;
        }
        .quiz-option.incorrect {
            border-color: #f44336;
            background: #ffebee;
            color: #c62828;
        }
        .quiz-feedback {
            text-align: center;
            margin-top: 18px;
            font-size: 1.08rem;
            min-height: 28px;
            transition: color 0.2s;
        }
        .quiz-feedback.correct { color: #388e3c; }
        .quiz-feedback.incorrect { color: #c62828; }
        .canvas-section {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.06);
            padding: 32px 20px 24px 20px;
            margin-bottom: 36px;
        }
        .canvas-title {
            font-size: 1.3rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 18px;
        }
        .canvas-controls {
            display: flex;
            justify-content: center;
            gap: 12px;
            margin-bottom: 18px;
        }
        .canvas-btn {
            background: linear-gradient(90deg, #6c63ff 0%, #48c6ef 100%);
            color: #fff;
            border: none;
            border-radius: 20px;
            padding: 10px 28px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s, transform 0.2s;
        }
        .canvas-btn:disabled {
            background: #e0e0e0;
            color: #aaa;
            cursor: not-allowed;
        }
        .canvas-btn.pulse {
            animation: pulse 1.2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.08); }
        }
        .canvas-wrap {
            display: flex;
            justify-content: center;
            align-items: center;
            background: #f7f8fa;
            border-radius: 12px;
            padding: 18px 0;
        }
        #tree-canvas {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(76,99,255,0.06);
        }
        .canvas-step-desc {
            text-align: center;
            color: #6c63ff;
            font-size: 1.08rem;
            min-height: 32px;
            margin-top: 10px;
        }
        .explain-section {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.06);
            padding: 32px 20px 24px 20px;
        }
        .explain-title {
            font-size: 1.3rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 18px;
        }
        .explain-card {
            background: #f5f6fa;
            border-radius: 12px;
            padding: 18px 22px;
            margin-bottom: 18px;
        }
        .explain-card:last-child { margin-bottom: 0; }
        .explain-card strong { color: #6c63ff; }
        @media (max-width: 600px) {
            .container { padding: 12px 2vw; }
            .quiz-option { min-width: 0; width: 100%; }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="title">ATAM效用树结构趣味游戏</div>
    <div class="desc">零基础也能轻松掌握软件架构评估的核心工具！</div>
    <div class="quiz-box">
        <div class="quiz-q">
            在软件架构评估中，<span style="color:#6c63ff;font-weight:600;">ATAM</span>方法采用效用树这一工具来对质量属性进行分类和优先级排序。<br><br>
            效用树的结构包括：<span style="background:#e6e7ff;padding:2px 8px;border-radius:6px;">（____）</span>
        </div>
        <div class="quiz-options">
            <div class="quiz-option" data-option="A">A. 树根--质量属性--属性分类--质量属性场景(叶子节点)</div>
            <div class="quiz-option" data-option="B">B. 树根--属性分类--属性描述--质量属性场景(叶子节点)</div>
            <div class="quiz-option" data-option="C">C. 树根--质量属性--属性描述--质量属性场景(叶子节点)</div>
            <div class="quiz-option" data-option="D">D. 树根--功能需求--需求描述--质量属性场景(叶子节点)</div>
        </div>
        <div class="quiz-feedback" id="quiz-feedback"></div>
    </div>
    <div class="canvas-section">
        <div class="canvas-title">🌳 效用树结构分步动画演示</div>
        <div class="canvas-controls">
            <button class="canvas-btn" id="btn-step1">1️⃣ 树根</button>
            <button class="canvas-btn" id="btn-step2" disabled>2️⃣ 质量属性</button>
            <button class="canvas-btn" id="btn-step3" disabled>3️⃣ 属性分类</button>
            <button class="canvas-btn" id="btn-step4" disabled>4️⃣ 质量场景</button>
            <button class="canvas-btn" id="btn-reset">🔄 重置</button>
        </div>
        <div class="canvas-wrap">
            <canvas id="tree-canvas" width="800" height="420"></canvas>
        </div>
        <div class="canvas-step-desc" id="canvas-step-desc">请先完成上方答题，解锁动画演示！</div>
    </div>
    <div class="explain-section">
        <div class="explain-title">📚 知识详解</div>
        <div class="explain-card">
            <strong>什么是效用树？</strong><br>
            效用树（Utility Tree）是ATAM方法的核心工具，帮助我们系统地梳理和优先级排序软件系统的质量需求。
        </div>
        <div class="explain-card">
            <strong>效用树的四层结构：</strong><br>
            <strong>1. 树根：</strong> 系统的总体质量目标，如“构建高质量的在线教育平台”。<br>
            <strong>2. 质量属性：</strong> 具体的质量维度，如性能、安全性、可用性、可维护性。<br>
            <strong>3. 属性分类：</strong> 对质量属性的细分，如性能→响应时间、吞吐量。<br>
            <strong>4. 质量属性场景：</strong> 具体可测试的场景，如“1000用户同时在线时，页面响应时间<2秒”。
        </div>
        <div class="explain-card">
            <strong>为什么这样分层？</strong><br>
            1. <strong>从抽象到具体：</strong> 帮助团队从宏观目标逐步细化到可执行的具体要求。<br>
            2. <strong>便于优先级排序：</strong> 可以在每一层进行重要性评估和排序。<br>
            3. <strong>支持架构决策：</strong> 具体的场景可以直接指导架构设计和技术选型。
        </div>
        <div class="explain-card">
            <strong>举例：</strong><br>
            <strong>树根：</strong> 构建高质量的电商平台<br>
            <strong>质量属性：</strong> 性能、安全性、可用性<br>
            <strong>属性分类：</strong> 性能→响应时间、吞吐量；安全性→数据加密、访问控制<br>
            <strong>质量场景：</strong> “双11期间10万用户同时下单，系统响应时间<3秒，成功率>99.9%”
        </div>
    </div>
</div>
<script>
// 题目答题逻辑
const quizOptions = document.querySelectorAll('.quiz-option');
const quizFeedback = document.getElementById('quiz-feedback');
const correctAnswer = 'A';
let quizPassed = false;
quizOptions.forEach(option => {
    option.addEventListener('click', () => {
        if (quizPassed) return;
        quizOptions.forEach(opt => opt.classList.remove('selected', 'correct', 'incorrect'));
        option.classList.add('selected');
        const selected = option.dataset.option;
        setTimeout(() => {
            if (selected === correctAnswer) {
                option.classList.add('correct');
                quizFeedback.textContent = '🎉 正确！效用树结构是：树根→质量属性→属性分类→质量属性场景。';
                quizFeedback.className = 'quiz-feedback correct';
                quizPassed = true;
                unlockCanvas();
            } else {
                option.classList.add('incorrect');
                quizOptions.forEach(opt => {
                    if (opt.dataset.option === correctAnswer) opt.classList.add('correct');
                });
                quizFeedback.textContent = '💡 不对哦！正确答案是A。效用树的层次是：树根→质量属性→属性分类→质量属性场景。';
                quizFeedback.className = 'quiz-feedback incorrect';
            }
        }, 250);
    });
});
// canvas动画与交互
const btns = [
    document.getElementById('btn-step1'),
    document.getElementById('btn-step2'),
    document.getElementById('btn-step3'),
    document.getElementById('btn-step4')
];
const btnReset = document.getElementById('btn-reset');
const canvas = document.getElementById('tree-canvas');
const ctx = canvas.getContext('2d');
const stepDesc = document.getElementById('canvas-step-desc');
let currentStep = 0;
function unlockCanvas() {
    btns[0].disabled = false;
    btns[0].classList.add('pulse');
    stepDesc.textContent = '点击“树根”按钮，开始分步构建效用树！';
}
function resetCanvas() {
    currentStep = 0;
    btns.forEach((b,i) => { b.disabled = i!==0; b.classList.remove('pulse'); });
    btns[0].classList.add('pulse');
    ctx.clearRect(0,0,canvas.width,canvas.height);
    stepDesc.textContent = '点击“树根”按钮，开始分步构建效用树！';
}
function nextStep(step) {
    if (step !== currentStep+1) return;
    currentStep = step;
    btns.forEach((b,i) => { b.disabled = i!==step; b.classList.remove('pulse'); });
    if (step<4) btns[step].classList.add('pulse');
    drawTree(step);
}
btns[0].addEventListener('click',()=>nextStep(1));
btns[1].addEventListener('click',()=>nextStep(2));
btns[2].addEventListener('click',()=>nextStep(3));
btns[3].addEventListener('click',()=>nextStep(4));
btnReset.addEventListener('click',resetCanvas);
function drawTree(step) {
    ctx.clearRect(0,0,canvas.width,canvas.height);
    // 树根
    if (step>=1) {
        drawNode(canvas.width/2, 70, '🎯 树根\n高质量软件系统', '#6c63ff', '#fff', 180, 54, 20);
        stepDesc.textContent = '第1层：树根——系统的总体质量目标';
    }
    // 质量属性
    if (step>=2) {
        const y2 = 170;
        const attrs = [
            {text:'🚀 性能',x:canvas.width/2-160,color:'#4caf50'},
            {text:'🔒 安全性',x:canvas.width/2,color:'#ff9800'},
            {text:'⚡ 可用性',x:canvas.width/2+160,color:'#2196f3'}
        ];
        attrs.forEach(attr=>{
            drawLine(canvas.width/2, 97, attr.x, y2-27, '#bbb', 2);
            drawNode(attr.x, y2, attr.text, attr.color, '#fff', 120, 44, 14);
        });
        stepDesc.textContent = '第2层：质量属性——系统关注的主要质量维度';
    }
    // 属性分类
    if (step>=3) {
        const y2 = 170, y3 = 270;
        const classifies = [
            {text:'响应时间',x:canvas.width/2-200,parentX:canvas.width/2-160,color:'#4caf50'},
            {text:'吞吐量',x:canvas.width/2-120,parentX:canvas.width/2-160,color:'#4caf50'},
            {text:'数据加密',x:canvas.width/2-40,parentX:canvas.width/2,color:'#ff9800'},
            {text:'访问控制',x:canvas.width/2+40,parentX:canvas.width/2,color:'#ff9800'},
            {text:'故障恢复',x:canvas.width/2+120,parentX:canvas.width/2+160,color:'#2196f3'},
            {text:'负载均衡',x:canvas.width/2+200,parentX:canvas.width/2+160,color:'#2196f3'}
        ];
        classifies.forEach(cls=>{
            drawLine(cls.parentX, y2+22, cls.x, y3-18, '#bbb', 2);
            drawNode(cls.x, y3, cls.text, cls.color, '#fff', 90, 36, 10);
        });
        stepDesc.textContent = '第3层：属性分类——质量属性的细分';
    }
    // 质量场景
    if (step>=4) {
        const y3 = 270, y4 = 360;
        const scenes = [
            {text:'1000用户<2秒',x:canvas.width/2-200,parentX:canvas.width/2-200,color:'#81c784'},
            {text:'10000并发',x:canvas.width/2-120,parentX:canvas.width/2-120,color:'#81c784'},
            {text:'SSL加密',x:canvas.width/2-40,parentX:canvas.width/2-40,color:'#ffb74d'},
            {text:'权限验证',x:canvas.width/2+40,parentX:canvas.width/2+40,color:'#ffb74d'},
            {text:'30秒恢复',x:canvas.width/2+120,parentX:canvas.width/2+120,color:'#64b5f6'},
            {text:'自动扩容',x:canvas.width/2+200,parentX:canvas.width/2+200,color:'#64b5f6'}
        ];
        scenes.forEach(sce=>{
            drawLine(sce.parentX, y3+16, sce.x, y4-14, '#bbb', 2);
            drawNode(sce.x, y4, sce.text, sce.color, '#fff', 80, 30, 8);
        });
        stepDesc.textContent = '第4层：质量场景——具体可测试的场景（叶子节点）';
    }
}
function drawNode(x, y, text, bgColor, textColor, w, h, r) {
    ctx.save();
    ctx.beginPath();
    roundRect(ctx, x-w/2, y-h/2, w, h, r);
    ctx.fillStyle = bgColor;
    ctx.fill();
    ctx.strokeStyle = bgColor;
    ctx.lineWidth = 2;
    ctx.stroke();
    ctx.fillStyle = textColor;
    ctx.font = 'bold 15px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    text.split('\n').forEach((line,i,arr)=>{
        ctx.fillText(line, x, y-h/4+ i*18);
    });
    ctx.restore();
}
function drawLine(x1, y1, x2, y2, color, width) {
    ctx.save();
    ctx.strokeStyle = color;
    ctx.lineWidth = width;
    ctx.beginPath();
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
    ctx.restore();
}
function roundRect(ctx, x, y, w, h, r) {
    ctx.moveTo(x+r, y);
    ctx.lineTo(x+w-r, y);
    ctx.quadraticCurveTo(x+w, y, x+w, y+r);
    ctx.lineTo(x+w, y+h-r);
    ctx.quadraticCurveTo(x+w, y+h, x+w-r, y+h);
    ctx.lineTo(x+r, y+h);
    ctx.quadraticCurveTo(x, y+h, x, y+h-r);
    ctx.lineTo(x, y+r);
    ctx.quadraticCurveTo(x, y, x+r, y);
}
// 初始化
btns.forEach((b,i)=>b.disabled=true);
btnReset.disabled=false;
</script>
</body>
</html> 