<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DSP处理器体系结构 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .question-card {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.6;
            color: #2c3e50;
            margin-bottom: 30px;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 3px solid transparent;
            border-radius: 16px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            font-size: 1.1rem;
            font-weight: 500;
            color: #495057;
        }

        .option:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .option.correct {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-color: #28a745;
            color: #155724;
        }

        .option.wrong {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border-color: #dc3545;
            color: #721c24;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        #architectureCanvas {
            width: 100%;
            height: 500px;
            border-radius: 12px;
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            backdrop-filter: blur(20px);
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .explanation h3 {
            color: #2c3e50;
            font-size: 1.8rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .explanation p {
            color: #495057;
            line-height: 1.7;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 1s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">DSP处理器体系结构</h1>
            <p class="subtitle">通过动画和交互学习处理器架构知识</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>目前处理器市场中存在CPU和DSP两种类型处理器，分别用于不同场景，这两种处理器具有不同的体系结构，DSP采用（ ）。
            </div>
            
            <div class="options-container">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 冯·诺伊曼结构
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 哈佛结构
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> FPGA结构
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 与GPU相同结构
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="architectureCanvas"></canvas>
            <div class="controls">
                <button class="btn" onclick="showVonNeumann()">演示冯·诺伊曼结构</button>
                <button class="btn" onclick="showHarvard()">演示哈佛结构</button>
                <button class="btn" onclick="compareArchitectures()">对比两种结构</button>
                <button class="btn" onclick="showDSPFeatures()">DSP特性演示</button>
            </div>
        </div>

        <div class="explanation">
            <h3>🎯 知识解析</h3>
            <p><span class="highlight">正确答案：B - 哈佛结构</span></p>
            
            <p><strong>为什么DSP采用哈佛结构？</strong></p>
            <p>DSP（数字信号处理器）需要进行大量的数学运算，特别是乘法和累加运算。哈佛结构将<span class="highlight">程序存储器</span>和<span class="highlight">数据存储器</span>分离，使用独立的总线，这样可以：</p>
            
            <p>✨ <strong>同时访问</strong>：CPU可以同时从程序存储器取指令，从数据存储器读取数据</p>
            <p>⚡ <strong>提高效率</strong>：避免了冯·诺伊曼结构中程序和数据争用同一总线的瓶颈</p>
            <p>🚀 <strong>实现单周期MAC</strong>：乘法累加运算可以在一个时钟周期内完成</p>
            
            <p><strong>其他选项为什么不对？</strong></p>
            <p>• <strong>冯·诺伊曼结构</strong>：程序和数据共享存储空间和总线，会产生瓶颈</p>
            <p>• <strong>FPGA结构</strong>：FPGA是可编程逻辑器件，不是处理器架构</p>
            <p>• <strong>GPU结构</strong>：GPU有自己的并行处理架构，与DSP不同</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentDemo = 'none';
        
        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
        
        // 动画变量
        let time = 0;
        let dataFlow = [];
        let instructionFlow = [];
        
        // 初始化
        function init() {
            showWelcome();
            setupQuestionInteraction();
        }
        
        function showWelcome() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('点击下方按钮开始学习处理器架构', canvas.width/2, canvas.height/2);
            
            ctx.font = '16px SF Pro Display';
            ctx.fillText('通过动画理解冯·诺伊曼结构与哈佛结构的区别', canvas.width/2, canvas.height/2 + 40);
        }
        
        function setupQuestionInteraction() {
            const options = document.querySelectorAll('.option');
            const progressFill = document.getElementById('progressFill');
            
            options.forEach(option => {
                option.addEventListener('click', function() {
                    const answer = this.dataset.answer;
                    
                    // 清除之前的状态
                    options.forEach(opt => {
                        opt.classList.remove('correct', 'wrong');
                    });
                    
                    if (answer === 'B') {
                        this.classList.add('correct');
                        progressFill.style.width = '100%';
                        setTimeout(() => {
                            showHarvard();
                        }, 1000);
                    } else {
                        this.classList.add('wrong');
                        // 显示正确答案
                        options.forEach(opt => {
                            if (opt.dataset.answer === 'B') {
                                opt.classList.add('correct');
                            }
                        });
                        progressFill.style.width = '50%';
                    }
                });
            });
        }
        
        // 冯·诺伊曼结构演示
        function showVonNeumann() {
            currentDemo = 'vonNeumann';
            cancelAnimationFrame(animationId);
            time = 0;
            animate();
        }
        
        // 哈佛结构演示
        function showHarvard() {
            currentDemo = 'harvard';
            cancelAnimationFrame(animationId);
            time = 0;
            animate();
        }
        
        // 对比演示
        function compareArchitectures() {
            currentDemo = 'compare';
            cancelAnimationFrame(animationId);
            time = 0;
            animate();
        }
        
        // DSP特性演示
        function showDSPFeatures() {
            currentDemo = 'dspFeatures';
            cancelAnimationFrame(animationId);
            time = 0;
            animate();
        }
        
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(currentDemo) {
                case 'vonNeumann':
                    drawVonNeumann();
                    break;
                case 'harvard':
                    drawHarvard();
                    break;
                case 'compare':
                    drawComparison();
                    break;
                case 'dspFeatures':
                    drawDSPFeatures();
                    break;
            }
            
            time += 0.02;
            animationId = requestAnimationFrame(animate);
        }
        
        function drawVonNeumann() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('冯·诺伊曼结构', centerX, 40);
            
            // CPU
            drawRoundedRect(ctx, centerX - 60, centerY - 40, 120, 80, 10, '#667eea', 'white');
            ctx.fillStyle = 'white';
            ctx.font = '16px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('CPU', centerX, centerY);
            
            // 统一存储器
            drawRoundedRect(ctx, centerX - 80, centerY + 100, 160, 60, 10, '#28a745', 'white');
            ctx.fillStyle = 'white';
            ctx.fillText('统一存储器', centerX, centerY + 135);
            ctx.font = '12px SF Pro Display';
            ctx.fillText('(程序+数据)', centerX, centerY + 150);
            
            // 总线
            drawAnimatedBus(centerX, centerY + 40, centerX, centerY + 100, '#dc3545', time);
            
            // 数据流动画
            drawDataFlow(centerX, centerY + 70, Math.sin(time * 2) * 20, '#ff6b6b');
            
            // 说明文字
            ctx.fillStyle = '#6c757d';
            ctx.font = '14px SF Pro Display';
            ctx.textAlign = 'left';
            ctx.fillText('特点：程序和数据共享存储空间和总线', 50, canvas.height - 60);
            ctx.fillText('缺点：存在总线冲突，影响性能', 50, canvas.height - 40);
            ctx.fillText('优点：结构简单，成本较低', 50, canvas.height - 20);
        }
        
        function drawHarvard() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('哈佛结构 (DSP采用)', centerX, 40);
            
            // CPU
            drawRoundedRect(ctx, centerX - 60, centerY - 40, 120, 80, 10, '#667eea', 'white');
            ctx.fillStyle = 'white';
            ctx.font = '16px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('DSP', centerX, centerY);
            ctx.font = '12px SF Pro Display';
            ctx.fillText('处理器', centerX, centerY + 15);
            
            // 程序存储器
            drawRoundedRect(ctx, centerX - 200, centerY - 20, 100, 60, 10, '#28a745', 'white');
            ctx.fillStyle = 'white';
            ctx.font = '14px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('程序存储器', centerX - 150, centerY + 10);
            
            // 数据存储器
            drawRoundedRect(ctx, centerX + 100, centerY - 20, 100, 60, 10, '#ffc107', 'black');
            ctx.fillStyle = 'black';
            ctx.fillText('数据存储器', centerX + 150, centerY + 10);
            
            // 程序总线
            drawAnimatedBus(centerX - 100, centerY, centerX - 60, centerY - 20, '#28a745', time);
            ctx.fillStyle = '#28a745';
            ctx.font = '12px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('程序总线', centerX - 130, centerY - 30);
            
            // 数据总线
            drawAnimatedBus(centerX + 60, centerY + 20, centerX + 100, centerY, '#ffc107', time + Math.PI);
            ctx.fillStyle = '#ffc107';
            ctx.fillText('数据总线', centerX + 130, centerY + 40);
            
            // 同时数据流
            drawDataFlow(centerX - 80, centerY - 10, Math.sin(time * 3) * 15, '#28a745');
            drawDataFlow(centerX + 80, centerY + 10, Math.sin(time * 3 + Math.PI) * 15, '#ffc107');
            
            // 说明文字
            ctx.fillStyle = '#6c757d';
            ctx.font = '14px SF Pro Display';
            ctx.textAlign = 'left';
            ctx.fillText('特点：程序和数据分离存储，独立总线', 50, canvas.height - 80);
            ctx.fillText('优点：可同时访问程序和数据，无总线冲突', 50, canvas.height - 60);
            ctx.fillText('优点：支持单周期MAC运算，性能更高', 50, canvas.height - 40);
            ctx.fillText('应用：DSP、微控制器等需要高性能的场合', 50, canvas.height - 20);
        }
        
        function drawComparison() {
            const leftX = canvas.width / 4;
            const rightX = canvas.width * 3 / 4;
            const centerY = canvas.height / 2;
            
            // 背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('架构对比', canvas.width / 2, 40);
            
            // 左侧：冯·诺伊曼
            ctx.font = 'bold 16px SF Pro Display';
            ctx.fillText('冯·诺伊曼结构', leftX, 80);
            
            // 右侧：哈佛
            ctx.fillText('哈佛结构 (DSP)', rightX, 80);
            
            // 简化的架构图...
            // (这里可以添加更详细的对比动画)
            
            // 性能对比动画
            drawPerformanceComparison(time);
        }
        
        function drawDSPFeatures() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('DSP处理器特性', centerX, 40);
            
            // DSP核心
            drawRoundedRect(ctx, centerX - 80, centerY - 60, 160, 120, 15, '#667eea', 'white');
            ctx.fillStyle = 'white';
            ctx.font = '16px SF Pro Display';
            ctx.fillText('DSP核心', centerX, centerY - 30);
            
            // 硬件乘法器
            drawRoundedRect(ctx, centerX - 60, centerY - 10, 120, 30, 8, '#28a745', 'white');
            ctx.fillStyle = 'white';
            ctx.font = '12px SF Pro Display';
            ctx.fillText('硬件乘法器', centerX, centerY + 8);
            
            // MAC运算演示
            drawMACAnimation(centerX, centerY + 40, time);
            
            // 流水线演示
            drawPipelineAnimation(centerX - 200, centerY, time);
            
            // DMA控制器
            drawRoundedRect(ctx, centerX + 120, centerY - 30, 80, 60, 10, '#ffc107', 'black');
            ctx.fillStyle = 'black';
            ctx.font = '12px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('DMA', centerX + 160, centerY - 5);
            ctx.fillText('控制器', centerX + 160, centerY + 10);
        }
        
        // 辅助绘图函数
        function drawRoundedRect(ctx, x, y, width, height, radius, fillColor, textColor) {
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, radius);
            ctx.fillStyle = fillColor;
            ctx.fill();
            ctx.strokeStyle = '#dee2e6';
            ctx.lineWidth = 2;
            ctx.stroke();
        }
        
        function drawAnimatedBus(x1, y1, x2, y2, color, phase) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 数据包动画
            const progress = (Math.sin(phase) + 1) / 2;
            const x = x1 + (x2 - x1) * progress;
            const y = y1 + (y2 - y1) * progress;
            
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 6, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function drawDataFlow(x, y, offset, color) {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x + offset, y, 4, 0, Math.PI * 2);
            ctx.fill();
        }
        
        function drawMACAnimation(x, y, time) {
            ctx.fillStyle = '#6c757d';
            ctx.font = '12px SF Pro Display';
            ctx.textAlign = 'center';
            
            const result = Math.sin(time * 2) * 50 + 100;
            ctx.fillText(`MAC: A×B+C = ${result.toFixed(0)}`, x, y);
            
            // 动画效果
            const scale = 1 + Math.sin(time * 4) * 0.1;
            ctx.save();
            ctx.translate(x, y);
            ctx.scale(scale, scale);
            ctx.fillStyle = '#28a745';
            ctx.fillText('单周期完成', 0, 15);
            ctx.restore();
        }
        
        function drawPipelineAnimation(x, y, time) {
            ctx.fillStyle = '#6c757d';
            ctx.font = '12px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('流水线', x, y - 20);
            
            // 流水线阶段
            const stages = ['取指', '译码', '执行', '写回'];
            stages.forEach((stage, i) => {
                const stageY = y + i * 25;
                const progress = (Math.sin(time * 2 + i * 0.5) + 1) / 2;
                
                ctx.fillStyle = `hsl(${200 + i * 30}, 70%, ${50 + progress * 20}%)`;
                drawRoundedRect(ctx, x - 40, stageY - 8, 80, 16, 8, ctx.fillStyle, 'white');
                
                ctx.fillStyle = 'white';
                ctx.font = '10px SF Pro Display';
                ctx.fillText(stage, x, stageY + 3);
            });
        }
        
        function drawPerformanceComparison(time) {
            const leftX = canvas.width / 4;
            const rightX = canvas.width * 3 / 4;
            const baseY = canvas.height * 0.7;
            
            // 性能条
            const vonNeumannPerf = 0.6;
            const harvardPerf = 0.9;
            
            // 冯·诺伊曼性能
            ctx.fillStyle = '#dc3545';
            ctx.fillRect(leftX - 50, baseY, 100 * vonNeumannPerf, 20);
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px SF Pro Display';
            ctx.textAlign = 'center';
            ctx.fillText('性能: 60%', leftX, baseY + 35);
            
            // 哈佛性能
            ctx.fillStyle = '#28a745';
            ctx.fillRect(rightX - 50, baseY, 100 * harvardPerf, 20);
            ctx.fillText('性能: 90%', rightX, baseY + 35);
            
            // 动画效果
            const pulse = Math.sin(time * 3) * 0.1 + 1;
            ctx.save();
            ctx.translate(rightX, baseY + 10);
            ctx.scale(pulse, 1);
            ctx.fillStyle = 'rgba(40, 167, 69, 0.3)';
            ctx.fillRect(-50, -10, 100 * harvardPerf, 20);
            ctx.restore();
        }
        
        // 初始化
        init();
    </script>
</body>
</html>
