<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函数是什么？- 零基础互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            animation: fadeInDown 1.2s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        .subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
            margin-bottom: 30px;
        }

        .fun-emoji {
            font-size: 4rem;
            animation: bounce 2s infinite;
        }

        .section {
            background: white;
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 50px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: fadeInUp 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #74b9ff, #0984e3, #6c5ce7);
        }

        .section-title {
            font-size: 2.2rem;
            color: #2d3436;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #74b9ff, #6c5ce7);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        canvas {
            border: 3px solid #ddd;
            border-radius: 20px;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        canvas:hover {
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            transform: translateY(-8px);
        }

        .explanation {
            font-size: 1.2rem;
            line-height: 2;
            color: #2d3436;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border-left: 6px solid #74b9ff;
            position: relative;
        }

        .explanation::before {
            content: '💡';
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 2rem;
            animation: pulse 2s infinite;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #74b9ff, #6c5ce7);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 30px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.4s ease;
            margin: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }

        .interactive-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .interactive-btn:hover::before {
            left: 100%;
        }

        .interactive-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }

        .analogy-box {
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
            padding: 40px;
            border-radius: 20px;
            margin: 30px 0;
            text-align: center;
            font-size: 1.4rem;
            color: white;
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            position: relative;
        }

        .analogy-box::before {
            content: '🎭';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 3rem;
            background: white;
            border-radius: 50%;
            padding: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .highlight {
            background: linear-gradient(45deg, #ffeaa7, #fab1a0);
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: bold;
            animation: highlightPulse 2s infinite;
            display: inline-block;
            margin: 0 5px;
        }

        .step-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .step-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
            border-top: 4px solid #74b9ff;
        }

        .step-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #74b9ff, #6c5ce7);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto 20px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes titleGlow {
            0% { text-shadow: 2px 2px 8px rgba(0,0,0,0.3); }
            100% { text-shadow: 2px 2px 20px rgba(255,255,255,0.5); }
        }

        @keyframes highlightPulse {
            0%, 100% { transform: scale(1); background: linear-gradient(45deg, #ffeaa7, #fab1a0); }
            50% { transform: scale(1.05); background: linear-gradient(45deg, #fdcb6e, #fd79a8); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #74b9ff, #6c5ce7);
            border-radius: 4px;
            transition: width 1s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">函数是什么？</h1>
            <p class="subtitle">让我们用最简单的方式理解这个神奇的数学概念！</p>
            <div class="fun-emoji">🎪</div>
        </div>

        <div class="section">
            <h2 class="section-title">🎭 生活中的函数</h2>
            <div class="analogy-box">
                <strong>函数就像一台神奇的机器！</strong><br>
                你放入一个东西（输入），它就给你另一个东西（输出）
            </div>
            
            <div class="canvas-container">
                <canvas id="machineCanvas" width="700" height="350"></canvas>
            </div>
            
            <div class="explanation">
                想象一下：<br>
                🍎 <span class="highlight">自动售货机</span> - 投入硬币，得到饮料<br>
                📱 <span class="highlight">计算器</span> - 输入数字，得到结果<br>
                🏭 <span class="highlight">工厂</span> - 输入原料，得到产品<br><br>
                函数就是这样的"规则"，给它一个x，它就给你一个y！
            </div>
            
            <button class="interactive-btn" onclick="startMachineAnimation()">🎬 看机器工作</button>
        </div>

        <div class="section">
            <h2 class="section-title">📊 数学中的函数</h2>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressBar" style="width: 0%"></div>
            </div>
            
            <div class="canvas-container">
                <canvas id="mathCanvas" width="700" height="400"></canvas>
            </div>
            
            <div class="explanation">
                数学函数的写法：<span class="highlight">y = f(x)</span><br>
                • <strong>x</strong> 是输入（自变量）<br>
                • <strong>y</strong> 是输出（因变量）<br>
                • <strong>f</strong> 是函数名（规则的名字）<br><br>
                比如：f(x) = 2x + 1，当x=3时，y = 2×3 + 1 = 7
            </div>
            
            <button class="interactive-btn" onclick="startMathDemo()">🧮 互动计算</button>
            <button class="interactive-btn" onclick="drawGraph()">📈 画函数图像</button>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 理解函数的关键</h2>
            
            <div class="step-container">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h3>一对一关系</h3>
                    <p>每个x只能对应一个y<br>就像每个人只有一个身份证号</p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">2</div>
                    <h3>有规律可循</h3>
                    <p>不是随便配对的<br>必须按照固定的规则</p>
                </div>
                
                <div class="step-card">
                    <div class="step-number">3</div>
                    <h3>可以预测</h3>
                    <p>知道输入就能算出输出<br>这就是函数的神奇之处</p>
                </div>
            </div>
            
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="700" height="300"></canvas>
            </div>
            
            <button class="interactive-btn" onclick="demonstrateConcept()">✨ 概念演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 互动练习</h2>
            
            <div class="analogy-box">
                <strong>现在你来当函数机器的操作员！</strong><br>
                选择一个函数规则，然后看看它如何工作
            </div>
            
            <div class="canvas-container">
                <canvas id="practiceCanvas" width="700" height="400"></canvas>
            </div>
            
            <button class="interactive-btn" onclick="setFunction('linear')">📏 线性函数 y=2x+1</button>
            <button class="interactive-btn" onclick="setFunction('square')">⬜ 平方函数 y=x²</button>
            <button class="interactive-btn" onclick="setFunction('custom')">🎨 自定义函数</button>
        </div>
    </div>

    <script>
        let currentAnimation = null;
        let currentFunction = 'linear';
        
        // 机器动画
        function startMachineAnimation() {
            const canvas = document.getElementById('machineCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制机器主体
                ctx.fillStyle = '#74b9ff';
                ctx.fillRect(250, 100, 200, 150);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('函数机器', 350, 180);
                
                // 输入动画
                const inputX = 100 + (frame % 100) * 1.5;
                if (inputX < 250) {
                    ctx.fillStyle = '#fd79a8';
                    ctx.beginPath();
                    ctx.arc(inputX, 175, 20, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText('x=3', inputX, 180);
                }
                
                // 输出动画
                if (frame > 50) {
                    const outputX = 450 + ((frame - 50) % 100) * 1.5;
                    if (outputX < 600) {
                        ctx.fillStyle = '#00cec9';
                        ctx.beginPath();
                        ctx.arc(outputX, 175, 20, 0, 2 * Math.PI);
                        ctx.fill();
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Arial';
                        ctx.fillText('y=7', outputX, 180);
                    }
                }
                
                // 标签
                ctx.fillStyle = '#2d3436';
                ctx.font = '16px Arial';
                ctx.fillText('输入', 100, 120);
                ctx.fillText('输出', 550, 120);
                
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }
        
        // 数学演示
        function startMathDemo() {
            const canvas = document.getElementById('mathCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;
            
            function demo() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制函数表达式
                ctx.fillStyle = '#2d3436';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('f(x) = 2x + 1', 350, 50);
                
                if (step >= 1) {
                    ctx.fillStyle = '#fd79a8';
                    ctx.fillText('当 x = 3 时：', 350, 100);
                }
                
                if (step >= 2) {
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillText('f(3) = 2 × 3 + 1', 350, 150);
                }
                
                if (step >= 3) {
                    ctx.fillStyle = '#00cec9';
                    ctx.fillText('f(3) = 6 + 1 = 7', 350, 200);
                }
                
                if (step >= 4) {
                    ctx.fillStyle = '#6c5ce7';
                    ctx.font = 'bold 28px Arial';
                    ctx.fillText('所以 y = 7', 350, 280);
                    
                    // 绘制箭头和结果
                    ctx.strokeStyle = '#6c5ce7';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(200, 320);
                    ctx.lineTo(500, 320);
                    ctx.stroke();
                    
                    // 箭头头部
                    ctx.beginPath();
                    ctx.moveTo(490, 310);
                    ctx.lineTo(500, 320);
                    ctx.lineTo(490, 330);
                    ctx.stroke();
                }
                
                step++;
                if (step <= 4) {
                    setTimeout(demo, 1500);
                }
                
                // 更新进度条
                document.getElementById('progressBar').style.width = (step * 25) + '%';
            }
            demo();
        }
        
        // 绘制函数图像
        function drawGraph() {
            const canvas = document.getElementById('mathCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制坐标轴
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(50, 200);
            ctx.lineTo(650, 200);
            ctx.moveTo(350, 50);
            ctx.lineTo(350, 350);
            ctx.stroke();
            
            // 标注
            ctx.fillStyle = '#2d3436';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('x', 660, 205);
            ctx.fillText('y', 355, 45);
            ctx.fillText('0', 340, 220);
            
            // 绘制函数线 y = 2x + 1
            ctx.strokeStyle = '#74b9ff';
            ctx.lineWidth = 3;
            ctx.beginPath();
            for (let x = -5; x <= 5; x += 0.1) {
                const canvasX = 350 + x * 30;
                const y = 2 * x + 1;
                const canvasY = 200 - y * 20;
                
                if (x === -5) {
                    ctx.moveTo(canvasX, canvasY);
                } else {
                    ctx.lineTo(canvasX, canvasY);
                }
            }
            ctx.stroke();
            
            // 标注函数
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('y = 2x + 1', 500, 100);
        }
        
        // 概念演示
        function demonstrateConcept() {
            const canvas = document.getElementById('conceptCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制输入集合
                ctx.fillStyle = '#fd79a8';
                ctx.fillRect(50, 50, 150, 200);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('输入 X', 125, 30);
                
                // 绘制输出集合
                ctx.fillStyle = '#00cec9';
                ctx.fillRect(500, 50, 150, 200);
                ctx.fillStyle = 'white';
                ctx.fillText('输出 Y', 575, 30);
                
                // 绘制连接线（动画）
                const inputs = [1, 2, 3, 4];
                const outputs = [3, 5, 7, 9]; // y = 2x + 1
                
                for (let i = 0; i < inputs.length; i++) {
                    const progress = Math.min(1, (frame - i * 20) / 60);
                    if (progress > 0) {
                        const startX = 200;
                        const startY = 80 + i * 40;
                        const endX = 500;
                        const endY = 80 + i * 40;
                        
                        const currentX = startX + (endX - startX) * progress;
                        
                        ctx.strokeStyle = '#74b9ff';
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(startX, startY);
                        ctx.lineTo(currentX, endY);
                        ctx.stroke();
                        
                        // 绘制点
                        ctx.fillStyle = '#2d3436';
                        ctx.font = '14px Arial';
                        ctx.fillText(inputs[i], 125, startY + 5);
                        if (progress > 0.8) {
                            ctx.fillText(outputs[i], 575, endY + 5);
                        }
                    }
                }
                
                frame++;
                if (frame < 150) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }
        
        // 设置练习函数
        function setFunction(type) {
            currentFunction = type;
            const canvas = document.getElementById('practiceCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制坐标轴
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(50, 200);
            ctx.lineTo(650, 200);
            ctx.moveTo(350, 50);
            ctx.lineTo(350, 350);
            ctx.stroke();
            
            // 绘制函数
            ctx.strokeStyle = '#6c5ce7';
            ctx.lineWidth = 3;
            ctx.beginPath();
            
            for (let x = -5; x <= 5; x += 0.1) {
                const canvasX = 350 + x * 30;
                let y;
                
                switch(type) {
                    case 'linear':
                        y = 2 * x + 1;
                        break;
                    case 'square':
                        y = x * x;
                        break;
                    default:
                        y = Math.sin(x);
                }
                
                const canvasY = 200 - y * 20;
                
                if (x === -5) {
                    ctx.moveTo(canvasX, canvasY);
                } else {
                    ctx.lineTo(canvasX, canvasY);
                }
            }
            ctx.stroke();
            
            // 添加点击交互
            canvas.onclick = function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = (e.clientX - rect.left - 350) / 30;
                let y;
                
                switch(currentFunction) {
                    case 'linear':
                        y = 2 * x + 1;
                        break;
                    case 'square':
                        y = x * x;
                        break;
                    default:
                        y = Math.sin(x);
                }
                
                // 绘制点
                ctx.fillStyle = '#fd79a8';
                ctx.beginPath();
                ctx.arc(350 + x * 30, 200 - y * 20, 8, 0, 2 * Math.PI);
                ctx.fill();
                
                // 显示坐标
                ctx.fillStyle = '#2d3436';
                ctx.font = '14px Arial';
                ctx.fillText(`(${x.toFixed(1)}, ${y.toFixed(1)})`, 350 + x * 30 + 15, 200 - y * 20 - 15);
            };
        }
        
        // 初始化
        window.onload = function() {
            startMachineAnimation();
            setTimeout(() => {
                setFunction('linear');
            }, 1000);
        };
    </script>
</body>
</html>
