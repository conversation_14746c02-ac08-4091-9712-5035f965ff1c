<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存穿透 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .architecture {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .component {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 150px;
        }

        .component:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .client { background: linear-gradient(135deg, #ff9a9e, #fecfef); }
        .cache { background: linear-gradient(135deg, #a8edea, #fed6e3); }
        .database { background: linear-gradient(135deg, #ffecd2, #fcb69f); }

        .arrow {
            font-size: 2rem;
            color: #667eea;
            animation: pulse 2s infinite;
        }

        .request-flow {
            position: relative;
            height: 200px;
            margin: 30px 0;
        }

        .request {
            position: absolute;
            width: 30px;
            height: 30px;
            background: #ff6b6b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: all 0.5s ease;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .solution-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .solution-card:hover {
            transform: translateX(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .bloom-filter {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 5px;
            margin: 20px 0;
        }

        .bloom-bit {
            width: 30px;
            height: 30px;
            background: #e9ecef;
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .bloom-bit.active {
            background: #667eea;
            color: white;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes moveRequest {
            0% { left: 0; }
            50% { left: 50%; }
            100% { left: 100%; }
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        canvas {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .cache-visualization {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
        }

        .cache-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
        }

        .cache-item span {
            padding: 5px 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        #bloomInput {
            padding: 12px 15px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            font-size: 1rem;
            width: 200px;
            transition: border-color 0.3s ease;
        }

        #bloomInput:focus {
            outline: none;
            border-color: #667eea;
        }

        #bloomResult {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            font-weight: bold;
            text-align: center;
        }

        .request-flow {
            position: relative;
            height: 80px;
            background: linear-gradient(90deg, #e3f2fd 0%, #fff3e0 50%, #e8f5e8 100%);
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
        }

        .request-flow::before {
            content: '客户端 → 校验层 → 通过';
            position: absolute;
            top: 10px;
            left: 20px;
            font-size: 0.9rem;
            color: #666;
        }

        ul {
            list-style: none;
            padding-left: 0;
        }

        ul li {
            position: relative;
            padding-left: 30px;
            margin: 15px 0;
        }

        ul li::before {
            content: '🔹';
            position: absolute;
            left: 0;
            top: 0;
        }

        @media (max-width: 768px) {
            .title { font-size: 2rem; }
            .architecture { flex-direction: column; }
            .component { min-width: 200px; }
            .bloom-filter { grid-template-columns: repeat(5, 1fr); }
            .cache-item { flex-direction: column; gap: 10px; }
            #bloomInput { width: 100%; }
            canvas { width: 100%; height: auto; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 缓存穿透 - 交互式学习之旅</h1>

        <!-- 概念介绍 -->
        <div class="section">
            <h2 class="section-title">🎯 什么是缓存穿透？</h2>
            <div class="explanation">
                <p><span class="highlight">缓存穿透</span>是指缓存和数据库中都没有的数据，导致所有的请求都落到数据库上，造成数据库短时间内承受大量请求而崩掉。</p>
            </div>
            
            <div class="demo-area">
                <h3>🏗️ 系统架构演示</h3>
                <div class="architecture">
                    <div class="component client">
                        <h4>👤 客户端</h4>
                        <p>发送请求</p>
                    </div>
                    <div class="arrow">→</div>
                    <div class="component cache">
                        <h4>💾 缓存</h4>
                        <p>快速存储</p>
                    </div>
                    <div class="arrow">→</div>
                    <div class="component database">
                        <h4>🗄️ 数据库</h4>
                        <p>持久化存储</p>
                    </div>
                </div>
                
                <button class="btn" onclick="simulateNormalFlow()">🔄 模拟正常流程</button>
                <button class="btn" onclick="simulatePenetration()">⚠️ 模拟缓存穿透</button>
                
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number" id="cacheHits">0</div>
                        <div class="stat-label">缓存命中</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="cacheMisses">0</div>
                        <div class="stat-label">缓存未命中</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="dbQueries">0</div>
                        <div class="stat-label">数据库查询</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 问题演示 -->
        <div class="section">
            <h2 class="section-title">⚡ 问题演示</h2>
            <div class="canvas-container">
                <h3>点击画布模拟恶意请求攻击</h3>
                <canvas id="attackCanvas" width="800" height="400"></canvas>
                <p>红色圆点代表恶意请求，蓝色柱状图代表数据库压力</p>
            </div>
        </div>

        <!-- 解决方案 -->
        <div class="section">
            <h2 class="section-title">🛡️ 解决方案</h2>

            <div class="solution-card">
                <h3>1️⃣ 接口层校验</h3>
                <div class="explanation">
                    <p>在接口层增加校验，如用户鉴权校验，ID做基础校验，ID≤0的直接拦截</p>
                </div>
                <button class="btn" onclick="demonstrateValidation()">🔍 演示校验过程</button>
                <div id="validationDemo" class="demo-area" style="display: none;">
                    <div class="request-flow">
                        <div id="validationRequest" class="request">R</div>
                    </div>
                    <p id="validationResult"></p>
                </div>
            </div>

            <div class="solution-card">
                <h3>2️⃣ 缓存空值</h3>
                <div class="explanation">
                    <p>从缓存取不到的数据，在数据库中也没有取到，这时也可以将key-value对写为key-null，缓存有效时间可以设置短点，如30秒</p>
                </div>
                <button class="btn" onclick="demonstrateNullCache()">💾 演示空值缓存</button>
                <div id="nullCacheDemo" class="demo-area" style="display: none;">
                    <div class="cache-visualization">
                        <div class="cache-item">
                            <span>Key: user_999</span>
                            <span id="cacheValue">Value: null</span>
                            <span id="cacheTTL">TTL: 30s</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="solution-card">
                <h3>3️⃣ 布隆过滤器</h3>
                <div class="explanation">
                    <p>将所有可能存在的数据哈希到一个足够大的bitmap中，一个一定不存在的数据会被这个bitmap拦截掉</p>
                </div>
                <button class="btn" onclick="demonstrateBloomFilter()">🎯 演示布隆过滤器</button>
                <div id="bloomDemo" class="demo-area" style="display: none;">
                    <h4>布隆过滤器位图 (简化版)</h4>
                    <div class="bloom-filter" id="bloomFilter"></div>
                    <div style="margin-top: 20px;">
                        <input type="text" id="bloomInput" placeholder="输入要检查的数据" style="padding: 10px; border-radius: 5px; border: 1px solid #ddd; margin-right: 10px;">
                        <button class="btn" onclick="checkBloomFilter()">检查是否存在</button>
                    </div>
                    <p id="bloomResult"></p>
                </div>
            </div>
        </div>

        <!-- 效果对比 -->
        <div class="section">
            <h2 class="section-title">📊 效果对比</h2>
            <div class="demo-area">
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number" id="beforeOptimization">100%</div>
                        <div class="stat-label">优化前数据库压力</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="afterOptimization">5%</div>
                        <div class="stat-label">优化后数据库压力</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="performanceGain">95%</div>
                        <div class="stat-label">性能提升</div>
                    </div>
                </div>

                <canvas id="comparisonCanvas" width="800" height="300"></canvas>
                <button class="btn" onclick="startComparison()">🚀 开始对比演示</button>
            </div>
        </div>

        <!-- 总结 -->
        <div class="section">
            <h2 class="section-title">🎓 学习总结</h2>
            <div class="explanation">
                <h3>🔑 关键要点：</h3>
                <ul style="margin: 20px 0; padding-left: 30px; line-height: 2;">
                    <li><span class="highlight">缓存穿透</span>：请求的数据在缓存和数据库中都不存在</li>
                    <li><span class="highlight">危害</span>：大量无效请求直接冲击数据库，可能导致系统崩溃</li>
                    <li><span class="highlight">解决方案</span>：接口校验 + 空值缓存 + 布隆过滤器</li>
                    <li><span class="highlight">效果</span>：显著减少数据库压力，提升系统稳定性</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <button class="btn" onclick="resetAllDemos()">🔄 重置所有演示</button>
                <button class="btn" onclick="showCongratulations()">🎉 完成学习</button>
            </div>
        </div>
    </div>

    <script>
        let cacheHits = 0;
        let cacheMisses = 0;
        let dbQueries = 0;
        let attackRequests = [];
        let dbPressure = 0;

        // 更新统计数据
        function updateStats() {
            document.getElementById('cacheHits').textContent = cacheHits;
            document.getElementById('cacheMisses').textContent = cacheMisses;
            document.getElementById('dbQueries').textContent = dbQueries;
        }

        // 模拟正常流程
        function simulateNormalFlow() {
            const components = document.querySelectorAll('.component');
            
            // 重置样式
            components.forEach(comp => comp.style.background = '');
            
            // 动画效果
            setTimeout(() => {
                components[0].style.background = 'linear-gradient(135deg, #ff9a9e, #fecfef)';
                components[0].style.transform = 'scale(1.1)';
            }, 100);
            
            setTimeout(() => {
                components[0].style.transform = 'scale(1)';
                components[1].style.background = 'linear-gradient(135deg, #a8edea, #fed6e3)';
                components[1].style.transform = 'scale(1.1)';
                cacheHits++;
                updateStats();
            }, 600);
            
            setTimeout(() => {
                components[1].style.transform = 'scale(1)';
            }, 1100);
        }

        // 模拟缓存穿透
        function simulatePenetration() {
            const components = document.querySelectorAll('.component');
            
            setTimeout(() => {
                components[0].style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a24)';
                components[0].style.transform = 'scale(1.1)';
            }, 100);
            
            setTimeout(() => {
                components[0].style.transform = 'scale(1)';
                components[1].style.background = 'linear-gradient(135deg, #ddd, #bbb)';
                components[1].style.transform = 'scale(1.1)';
                cacheMisses++;
                updateStats();
            }, 600);
            
            setTimeout(() => {
                components[1].style.transform = 'scale(1)';
                components[2].style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a24)';
                components[2].style.transform = 'scale(1.2)';
                dbQueries++;
                updateStats();
            }, 1100);
            
            setTimeout(() => {
                components[2].style.transform = 'scale(1)';
            }, 1600);
        }

        // Canvas 攻击演示
        const canvas = document.getElementById('attackCanvas');
        const ctx = canvas.getContext('2d');

        function drawAttackSimulation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制数据库压力柱状图
            const barHeight = (dbPressure / 100) * 300;
            ctx.fillStyle = dbPressure > 80 ? '#ff6b6b' : dbPressure > 50 ? '#ffa726' : '#4fc3f7';
            ctx.fillRect(canvas.width - 100, canvas.height - barHeight - 50, 60, barHeight);
            
            // 绘制压力标签
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText('数据库压力', canvas.width - 120, canvas.height - 20);
            ctx.fillText(dbPressure + '%', canvas.width - 90, canvas.height - barHeight - 60);
            
            // 绘制攻击请求
            attackRequests.forEach((request, index) => {
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(request.x, request.y, 8, 0, 2 * Math.PI);
                ctx.fill();
                
                // 移动请求
                request.x += 2;
                if (request.x > canvas.width - 150) {
                    dbPressure = Math.min(100, dbPressure + 5);
                    attackRequests.splice(index, 1);
                }
            });
            
            // 压力自然衰减
            if (dbPressure > 0) {
                dbPressure = Math.max(0, dbPressure - 0.5);
            }
            
            requestAnimationFrame(drawAttackSimulation);
        }

        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            attackRequests.push({ x: x, y: y });
        });

        // 启动动画
        drawAttackSimulation();

        // 演示接口校验
        function demonstrateValidation() {
            const demo = document.getElementById('validationDemo');
            const request = document.getElementById('validationRequest');
            const result = document.getElementById('validationResult');

            demo.style.display = 'block';
            request.style.left = '0px';
            request.style.background = '#ff6b6b';
            request.textContent = 'R';

            // 模拟请求移动
            setTimeout(() => {
                request.style.left = '200px';
                request.style.background = '#ffa726';
                result.textContent = '🔍 正在校验请求...';
            }, 500);

            setTimeout(() => {
                request.style.left = '400px';
                request.style.background = '#4caf50';
                request.textContent = '✓';
                result.textContent = '✅ 校验通过！请求ID有效，允许继续处理';
            }, 1500);
        }

        // 演示空值缓存
        function demonstrateNullCache() {
            const demo = document.getElementById('nullCacheDemo');
            const cacheValue = document.getElementById('cacheValue');
            const cacheTTL = document.getElementById('cacheTTL');

            demo.style.display = 'block';

            let ttl = 30;
            const countdown = setInterval(() => {
                ttl--;
                cacheTTL.textContent = `TTL: ${ttl}s`;
                cacheTTL.style.color = ttl < 10 ? '#ff6b6b' : '#333';

                if (ttl <= 0) {
                    clearInterval(countdown);
                    cacheValue.textContent = 'Value: 已过期';
                    cacheValue.style.color = '#999';
                    cacheTTL.textContent = 'TTL: 0s';
                }
            }, 100);
        }

        // 布隆过滤器演示
        let bloomBits = new Array(50).fill(0);
        const existingData = ['user_1', 'user_2', 'user_3', 'product_100', 'order_500'];

        function demonstrateBloomFilter() {
            const demo = document.getElementById('bloomDemo');
            const bloomFilter = document.getElementById('bloomFilter');

            demo.style.display = 'block';
            bloomFilter.innerHTML = '';

            // 创建位图
            for (let i = 0; i < 50; i++) {
                const bit = document.createElement('div');
                bit.className = 'bloom-bit';
                bit.textContent = bloomBits[i];
                bit.id = `bit-${i}`;
                bloomFilter.appendChild(bit);
            }

            // 初始化已存在的数据
            existingData.forEach(data => {
                const hash1 = simpleHash(data, 50);
                const hash2 = simpleHash(data + 'salt', 50);
                bloomBits[hash1] = 1;
                bloomBits[hash2] = 1;
                document.getElementById(`bit-${hash1}`).classList.add('active');
                document.getElementById(`bit-${hash2}`).classList.add('active');
                document.getElementById(`bit-${hash1}`).textContent = '1';
                document.getElementById(`bit-${hash2}`).textContent = '1';
            });
        }

        function simpleHash(str, size) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                hash = ((hash << 5) - hash + str.charCodeAt(i)) & 0xffffffff;
            }
            return Math.abs(hash) % size;
        }

        function checkBloomFilter() {
            const input = document.getElementById('bloomInput').value;
            const result = document.getElementById('bloomResult');

            if (!input) {
                result.textContent = '请输入要检查的数据';
                return;
            }

            const hash1 = simpleHash(input, 50);
            const hash2 = simpleHash(input + 'salt', 50);

            // 高亮相关位
            document.querySelectorAll('.bloom-bit').forEach(bit => {
                bit.style.background = '';
            });

            document.getElementById(`bit-${hash1}`).style.background = '#ffeb3b';
            document.getElementById(`bit-${hash2}`).style.background = '#ffeb3b';

            if (bloomBits[hash1] === 1 && bloomBits[hash2] === 1) {
                result.textContent = `✅ "${input}" 可能存在（需要进一步查询缓存/数据库）`;
                result.style.color = '#4caf50';
            } else {
                result.textContent = `❌ "${input}" 一定不存在（直接拦截，不查询数据库）`;
                result.style.color = '#ff6b6b';
            }
        }

        // 效果对比演示
        function startComparison() {
            const canvas = document.getElementById('comparisonCanvas');
            const ctx = canvas.getContext('2d');

            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制优化前
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(50, 50, 300, Math.sin(frame * 0.1) * 50 + 200);
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.fillText('优化前：数据库压力', 50, 40);

                // 绘制优化后
                ctx.fillStyle = '#4caf50';
                ctx.fillRect(450, 50, 300, 20);
                ctx.fillText('优化后：数据库压力', 450, 40);

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            };

            animate();
        }

        // 重置所有演示
        function resetAllDemos() {
            cacheHits = 0;
            cacheMisses = 0;
            dbQueries = 0;
            updateStats();

            document.querySelectorAll('.demo-area').forEach(demo => {
                demo.style.display = 'none';
            });

            attackRequests = [];
            dbPressure = 0;
            bloomBits = new Array(50).fill(0);
        }

        // 完成学习
        function showCongratulations() {
            alert('🎉 恭喜您完成了缓存穿透的学习！\n\n您已经掌握了：\n✅ 缓存穿透的概念和危害\n✅ 三种主要解决方案\n✅ 实际应用场景\n\n继续加油，成为更优秀的开发者！');
        }

        // 页面加载动画
        window.addEventListener('load', () => {
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.animationDelay = `${index * 0.2}s`;
                }, index * 100);
            });
        });
    </script>
</body>
</html>
