<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机指令执行流水线 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        .section h2 {
            font-size: 2rem;
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
        }

        .instruction-stages {
            display: flex;
            justify-content: space-around;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stage {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stage:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .stage h3 {
            font-size: 1.3rem;
            margin-bottom: 10px;
        }

        .stage p {
            font-size: 1.1rem;
            font-weight: bold;
        }

        .animation-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            min-height: 300px;
        }

        .control-panel {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .instruction-box {
            width: 80px;
            height: 50px;
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            position: absolute;
            transition: all 0.8s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .pipeline-track {
            height: 60px;
            background: rgba(255,255,255,0.8);
            border-radius: 30px;
            margin: 20px 0;
            position: relative;
            border: 2px dashed #ccc;
        }

        .stage-label {
            position: absolute;
            top: -30px;
            font-weight: bold;
            color: #666;
            font-size: 0.9rem;
        }

        .time-display {
            background: #2d3748;
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .highlight {
            background: #fff3cd !important;
            animation: pulse 1s infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes moveRight {
            from { transform: translateX(0); }
            to { transform: translateX(100px); }
        }

        .quiz-section {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.2rem;
            margin-bottom: 20px;
            color: #2d3748;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: white;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .instruction-stages { flex-direction: column; }
            .stage { min-width: auto; }
            .container { padding: 20px 10px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ 计算机指令执行流水线</h1>
            <p>通过动画和交互体验，深入理解计算机如何执行指令，掌握顺序执行与流水线执行的区别</p>
        </div>

        <div class="section">
            <h2>📚 基础知识：指令执行的三个阶段</h2>
            <p style="text-align: center; margin-bottom: 30px; font-size: 1.1rem; color: #666;">
                计算机执行每条指令都需要经过三个基本阶段，让我们来了解它们：
            </p>
            
            <div class="instruction-stages">
                <div class="stage" onclick="highlightStage(0)">
                    <h3>🔍 取指令 (Fetch)</h3>
                    <p>时间：4Δt</p>
                    <div style="margin-top: 10px; font-size: 0.9rem;">
                        从内存中读取指令到CPU
                    </div>
                </div>
                <div class="stage" onclick="highlightStage(1)">
                    <h3>🧠 分析指令 (Decode)</h3>
                    <p>时间：2Δt</p>
                    <div style="margin-top: 10px; font-size: 0.9rem;">
                        解析指令含义和操作类型
                    </div>
                </div>
                <div class="stage" onclick="highlightStage(2)">
                    <h3>⚡ 执行指令 (Execute)</h3>
                    <p>时间：3Δt</p>
                    <div style="margin-top: 10px; font-size: 0.9rem;">
                        实际执行指令操作
                    </div>
                </div>
            </div>

            <div class="time-display">
                💡 每条指令总时间 = 4Δt + 2Δt + 3Δt = 9Δt
            </div>
        </div>

        <div class="section">
            <h2>🐌 顺序执行方式演示</h2>
            <p style="text-align: center; margin-bottom: 30px; color: #666;">
                传统方式：必须完全执行完一条指令后，才能开始下一条指令
            </p>
            
            <div class="animation-container" id="sequentialAnimation">
                <div class="pipeline-track">
                    <div class="stage-label" style="left: 50px;">取指令区域</div>
                </div>
                <div class="pipeline-track">
                    <div class="stage-label" style="left: 50px;">分析指令区域</div>
                </div>
                <div class="pipeline-track">
                    <div class="stage-label" style="left: 50px;">执行指令区域</div>
                </div>
            </div>

            <div class="control-panel">
                <button class="btn" onclick="startSequentialDemo()">🎬 开始顺序执行演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置</button>
            </div>

            <div class="time-display" id="sequentialTime">
                ⏱️ 顺序执行600条指令总时间：9Δt × 600 = 5400Δt
            </div>
        </div>

        <div class="section">
            <h2>🚀 流水线执行方式演示</h2>
            <p style="text-align: center; margin-bottom: 30px; color: #666;">
                高效方式：同时进行取指令、分析指令、执行指令，就像工厂流水线一样！
            </p>

            <div class="animation-container" id="pipelineAnimation">
                <div class="pipeline-track">
                    <div class="stage-label" style="left: 50px;">取指令 (4Δt)</div>
                </div>
                <div class="pipeline-track">
                    <div class="stage-label" style="left: 50px;">分析指令 (2Δt)</div>
                </div>
                <div class="pipeline-track">
                    <div class="stage-label" style="left: 50px;">执行指令 (3Δt)</div>
                </div>
            </div>

            <div class="control-panel">
                <button class="btn" onclick="startPipelineDemo()">🎬 开始流水线演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置</button>
            </div>

            <div class="time-display" id="pipelineTime">
                ⏱️ 流水线执行600条指令总时间：4Δt × 600 + 2Δt + 3Δt = 2405Δt
            </div>

            <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3 style="color: #2d5016; margin-bottom: 15px;">💡 流水线原理解释：</h3>
                <p style="color: #2d5016; line-height: 1.6;">
                    • 第1条指令：取指令需要4Δt<br>
                    • 第2条指令：在第1条指令分析时开始取指令<br>
                    • 第3条指令：在第1条指令执行、第2条指令分析时开始取指令<br>
                    • 之后每4Δt就能完成一条指令！<br>
                    • 最后还需要2Δt+3Δt来完成最后的分析和执行
                </p>
            </div>
        </div>

        <div class="section">
            <h2>📊 性能对比分析</h2>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>执行方式</th>
                        <th>单条指令时间</th>
                        <th>600条指令总时间</th>
                        <th>效率提升</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>顺序执行</strong></td>
                        <td>9Δt</td>
                        <td>5400Δt</td>
                        <td>基准</td>
                    </tr>
                    <tr class="highlight">
                        <td><strong>流水线执行</strong></td>
                        <td>4Δt (稳定后)</td>
                        <td>2405Δt</td>
                        <td>提升 55.5%</td>
                    </tr>
                </tbody>
            </table>

            <div style="text-align: center; margin: 30px 0;">
                <div style="background: linear-gradient(135deg, #ff9a9e, #fecfef); padding: 25px; border-radius: 15px; display: inline-block;">
                    <h3 style="color: #8b5a3c; margin-bottom: 10px;">🎯 关键洞察</h3>
                    <p style="color: #8b5a3c; font-size: 1.1rem;">
                        流水线让CPU利用率从 <strong>33.3%</strong> 提升到 <strong>100%</strong>！
                    </p>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2>🎮 互动测验</h2>
            <div class="quiz-question">
                <strong>问题：</strong>如果有1000条指令需要执行，使用流水线方式需要多少时间？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="checkAnswer(this, false)">
                    A. 9000Δt
                </div>
                <div class="quiz-option" onclick="checkAnswer(this, false)">
                    B. 4000Δt
                </div>
                <div class="quiz-option" onclick="checkAnswer(this, true)">
                    C. 4005Δt
                </div>
                <div class="quiz-option" onclick="checkAnswer(this, false)">
                    D. 3000Δt
                </div>
            </div>
            <div id="quizResult" style="margin-top: 20px; padding: 15px; border-radius: 10px; display: none;"></div>
        </div>

        <div class="section">
            <h2>🎯 学习总结</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0;">
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 15px;">
                    <h3>🔍 顺序执行</h3>
                    <p>• 简单直观<br>• 资源利用率低<br>• 适合简单系统</p>
                </div>
                <div style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; padding: 25px; border-radius: 15px;">
                    <h3>🚀 流水线执行</h3>
                    <p>• 并行处理<br>• 高效利用资源<br>• 现代CPU标准</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentAnimation = null;
        let instructionCounter = 0;

        function highlightStage(stageIndex) {
            const stages = document.querySelectorAll('.stage');
            stages.forEach((stage, index) => {
                if (index === stageIndex) {
                    stage.style.transform = 'scale(1.1)';
                    stage.style.boxShadow = '0 20px 40px rgba(0,0,0,0.3)';
                } else {
                    stage.style.transform = 'scale(1)';
                    stage.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
                }
            });

            setTimeout(() => {
                stages[stageIndex].style.transform = 'scale(1)';
                stages[stageIndex].style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
            }, 1000);
        }

        function createInstructionBox(id, text) {
            const box = document.createElement('div');
            box.className = 'instruction-box';
            box.id = id;
            box.textContent = text;
            return box;
        }

        function startSequentialDemo() {
            resetAnimation();
            const container = document.getElementById('sequentialAnimation');
            const tracks = container.querySelectorAll('.pipeline-track');

            let instructionNum = 1;
            let totalTime = 0;

            function processInstruction() {
                if (instructionNum > 3) return; // 只演示3条指令

                const instruction = createInstructionBox(`seq-inst-${instructionNum}`, `指令${instructionNum}`);
                container.appendChild(instruction);

                // 取指令阶段
                instruction.style.left = '20px';
                instruction.style.top = `${20 + (0 * 80)}px`;
                instruction.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a24)';

                setTimeout(() => {
                    // 分析指令阶段
                    instruction.style.top = `${20 + (1 * 80)}px`;
                    instruction.style.background = 'linear-gradient(135deg, #feca57, #ff9ff3)';
                }, 4000);

                setTimeout(() => {
                    // 执行指令阶段
                    instruction.style.top = `${20 + (2 * 80)}px`;
                    instruction.style.background = 'linear-gradient(135deg, #48dbfb, #0abde3)';
                }, 6000);

                setTimeout(() => {
                    instruction.style.opacity = '0.3';
                    instructionNum++;
                    if (instructionNum <= 3) {
                        processInstruction();
                    }
                }, 9000);
            }

            processInstruction();
        }

        function startPipelineDemo() {
            resetAnimation();
            const container = document.getElementById('pipelineAnimation');

            let instructionNum = 1;
            const maxInstructions = 5; // 演示5条指令

            function processInstructionPipeline() {
                if (instructionNum > maxInstructions) return;

                const instruction = createInstructionBox(`pipe-inst-${instructionNum}`, `指令${instructionNum}`);
                container.appendChild(instruction);

                // 取指令阶段 (4Δt)
                instruction.style.left = '20px';
                instruction.style.top = '20px';
                instruction.style.background = 'linear-gradient(135deg, #ff6b6b, #ee5a24)';

                setTimeout(() => {
                    // 分析指令阶段 (2Δt)
                    instruction.style.top = '100px';
                    instruction.style.background = 'linear-gradient(135deg, #feca57, #ff9ff3)';
                }, 4000);

                setTimeout(() => {
                    // 执行指令阶段 (3Δt)
                    instruction.style.top = '180px';
                    instruction.style.background = 'linear-gradient(135deg, #48dbfb, #0abde3)';
                }, 6000);

                setTimeout(() => {
                    instruction.style.opacity = '0.3';
                }, 9000);

                // 每4Δt启动下一条指令
                setTimeout(() => {
                    instructionNum++;
                    if (instructionNum <= maxInstructions) {
                        processInstructionPipeline();
                    }
                }, 4000);
            }

            processInstructionPipeline();
        }

        function resetAnimation() {
            const containers = ['sequentialAnimation', 'pipelineAnimation'];
            containers.forEach(containerId => {
                const container = document.getElementById(containerId);
                const instructions = container.querySelectorAll('.instruction-box');
                instructions.forEach(inst => inst.remove());
            });
            instructionCounter = 0;
        }

        function checkAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const result = document.getElementById('quizResult');

            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    if (isCorrect) {
                        option.classList.add('correct');
                        result.innerHTML = '🎉 <strong>正确！</strong> 流水线执行1000条指令需要：4Δt × 1000 + 2Δt + 3Δt = 4005Δt';
                        result.style.background = '#d4edda';
                        result.style.color = '#155724';
                    } else {
                        option.classList.add('wrong');
                        result.innerHTML = '❌ <strong>不正确。</strong> 记住流水线公式：4Δt × 指令数 + 2Δt + 3Δt';
                        result.style.background = '#f8d7da';
                        result.style.color = '#721c24';
                    }
                } else if (isCorrect) {
                    // 如果用户选错了，高亮正确答案
                    const correctText = element.textContent.trim();
                    if (option.textContent.includes('4005Δt')) {
                        option.classList.add('correct');
                    }
                }
            });

            result.style.display = 'block';

            // 3秒后重置
            setTimeout(() => {
                options.forEach(option => {
                    option.style.pointerEvents = 'auto';
                    option.classList.remove('correct', 'wrong');
                });
                result.style.display = 'none';
            }, 5000);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些交互提示
            const stages = document.querySelectorAll('.stage');
            stages.forEach((stage, index) => {
                stage.addEventListener('mouseenter', () => {
                    stage.style.transform = 'translateY(-5px) scale(1.02)';
                });
                stage.addEventListener('mouseleave', () => {
                    stage.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
