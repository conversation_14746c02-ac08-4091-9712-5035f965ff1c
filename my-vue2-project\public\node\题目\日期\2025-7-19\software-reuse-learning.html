<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件重用学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .reuse-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .reuse-demo {
            text-align: center;
            margin: 30px 0;
        }

        #reuseCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .element-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 25px 0;
        }

        .element-btn {
            padding: 15px 10px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .requirements-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .design-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .code-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .test-btn {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }

        .knowledge-btn {
            background: linear-gradient(45deg, #a29bfe, #6c5ce7);
        }

        .element-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .element-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .element-cards {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .element-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .element-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .element-card.correct {
            border-color: #00b894;
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }

        .element-card.incorrect {
            border-color: #e17055;
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
        }

        .element-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .element-card p {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-correct {
            color: #00a085;
            font-weight: bold;
            background: rgba(0,184,148,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-component {
            position: absolute;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            animation: floatComponent 14s infinite ease-in-out;
        }

        .comp1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .comp2 {
            top: 70%;
            right: 15%;
            animation-delay: 5s;
        }

        .comp3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatComponent {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(10px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .element-controls {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .element-cards {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-component comp1"></div>
        <div class="floating-component comp2"></div>
        <div class="floating-component comp3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>♻️ 软件重用学习</h1>
            <p>深度理解软件重用的概念与可重用的软件元素</p>
        </div>

        <div class="main-grid">
            <div class="reuse-section">
                <h2 class="section-title">🔄 软件重用演示</h2>
                
                <div class="reuse-demo">
                    <canvas id="reuseCanvas" width="700" height="350"></canvas>
                </div>

                <div class="element-controls">
                    <button class="element-btn requirements-btn" onclick="demonstrateElement('requirements')">
                        需求分析文档<br><small>Requirements</small>
                    </button>
                    <button class="element-btn design-btn" onclick="demonstrateElement('design')">
                        设计文档<br><small>Design</small>
                    </button>
                    <button class="element-btn code-btn" onclick="demonstrateElement('code')">
                        程序代码<br><small>Code</small>
                    </button>
                    <button class="element-btn test-btn" onclick="demonstrateElement('test')">
                        测试用例<br><small>Test Cases</small>
                    </button>
                    <button class="element-btn knowledge-btn" onclick="demonstrateElement('knowledge')">
                        领域知识<br><small>Domain Knowledge</small>
                    </button>
                </div>

                <div class="element-cards">
                    <div class="element-card correct">
                        <h3>✅ 需求分析文档</h3>
                        <p>需求规格说明、用户故事、功能需求等可重用的需求文档</p>
                    </div>
                    <div class="element-card correct">
                        <h3>✅ 设计文档</h3>
                        <p>架构设计、详细设计、接口设计等可重用的设计文档</p>
                    </div>
                    <div class="element-card correct">
                        <h3>✅ 程序代码</h3>
                        <p>函数库、类库、组件、模块等可重用的代码资产</p>
                    </div>
                    <div class="element-card correct">
                        <h3>✅ 测试用例</h3>
                        <p>测试脚本、测试数据、测试场景等可重用的测试资产</p>
                    </div>
                    <div class="element-card correct">
                        <h3>✅ 领域知识</h3>
                        <p>业务规则、领域模型、专业知识等可重用的知识资产</p>
                    </div>
                    <div class="element-card incorrect">
                        <h3>❌ 项目范围定义</h3>
                        <p>项目特定的范围定义通常不具备重用价值</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 软件重用是指在两次或多次不同的软件开发过程中重复使用相同或相似软件元素的过程。软件元素包括（　　）、测试用例和领域知识等。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. 项目范围定义、需求分析文档、设计文档
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        B. 需求分析文档、设计文档、程序代码
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. 设计文档、程序代码、界面原型
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        D. 程序代码、界面原型、数据表结构
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：B. 需求分析文档、设计文档、程序代码</strong></p>
                    <p>软件重用中的可重用软件元素包括：</p>
                    <ul>
                        <li><span class="highlight-correct">需求分析文档</span>：
                            <br>• 需求规格说明书
                            <br>• 用户故事和用例
                            <br>• 功能需求和非功能需求</li>
                        <li><span class="highlight-correct">设计文档</span>：
                            <br>• 系统架构设计
                            <br>• 详细设计文档
                            <br>• 接口设计规范</li>
                        <li><span class="highlight-correct">程序代码</span>：
                            <br>• 函数库和类库
                            <br>• 软件组件和模块
                            <br>• 框架和工具包</li>
                        <li><span class="highlight-correct">测试用例</span>：
                            <br>• 测试脚本和测试数据
                            <br>• 测试场景和测试计划</li>
                        <li><span class="highlight-correct">领域知识</span>：
                            <br>• 业务规则和流程
                            <br>• 领域模型和专业知识</li>
                    </ul>
                    <p><strong>软件重用的优势</strong>：提高生产率、降低成本、缩短周期、改善质量、提高标准化程度。</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了软件重用的核心概念！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('reuseCanvas');
        const ctx = canvas.getContext('2d');
        let currentElement = 'requirements';
        let animationId = null;

        // 演示不同软件元素
        function demonstrateElement(elementType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            currentElement = elementType;
            
            // 更新按钮状态
            document.querySelectorAll('.element-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${elementType}-btn`).classList.add('active');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(elementType) {
                case 'requirements':
                    drawRequirementsElement();
                    break;
                case 'design':
                    drawDesignElement();
                    break;
                case 'code':
                    drawCodeElement();
                    break;
                case 'test':
                    drawTestElement();
                    break;
                case 'knowledge':
                    drawKnowledgeElement();
                    break;
            }
        }

        // 绘制需求分析文档
        function drawRequirementsElement() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('需求分析文档重用', 350, 40);

            // 原始项目
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(50, 80, 150, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 80, 150, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('项目A', 125, 110);
            ctx.font = '12px Arial';
            ctx.fillText('需求文档', 125, 130);
            ctx.fillText('用户故事', 125, 150);
            ctx.fillText('功能需求', 125, 170);

            // 重用箭头
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(200, 130);
            ctx.lineTo(280, 130);
            ctx.stroke();
            
            // 箭头头部
            ctx.fillStyle = '#0984e3';
            ctx.beginPath();
            ctx.moveTo(280, 130);
            ctx.lineTo(270, 125);
            ctx.lineTo(270, 135);
            ctx.closePath();
            ctx.fill();

            // 重用标识
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(220, 110, 80, 40);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 2;
            ctx.strokeRect(220, 110, 80, 40);
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('重用', 260, 135);

            // 新项目
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(320, 80, 150, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(320, 80, 150, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('项目B', 395, 110);
            ctx.font = '12px Arial';
            ctx.fillText('复用需求文档', 395, 130);
            ctx.fillText('适配用户故事', 395, 150);
            ctx.fillText('扩展功能需求', 395, 170);

            // 更多项目
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(500, 80, 150, 100);
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 2;
            ctx.strokeRect(500, 80, 150, 100);
            
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('项目C、D...', 575, 130);

            // 重用优势
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('✓ 减少需求分析工作量', 50, 220);
            ctx.fillText('✓ 提高需求质量', 250, 220);
            ctx.fillText('✓ 加速项目启动', 450, 220);

            // 重用类型
            const types = ['需求规格', '用例文档', '业务规则'];
            types.forEach((type, index) => {
                const x = 150 + index * 150;
                const y = 260;
                
                ctx.fillStyle = '#74b9ff';
                ctx.fillRect(x, y, 100, 40);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 1;
                ctx.strokeRect(x, y, 100, 40);
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(type, x + 50, y + 25);
            });
        }

        // 绘制设计文档
        function drawDesignElement() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('设计文档重用', 350, 40);

            // 设计模式库
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(100, 80, 200, 120);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(100, 80, 200, 120);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('设计模式库', 200, 110);

            // 设计模式
            const patterns = ['单例模式', '工厂模式', '观察者模式'];
            patterns.forEach((pattern, index) => {
                const y = 130 + index * 20;
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(`• ${pattern}`, 120, y);
            });

            // 重用到多个项目
            const projects = [
                {x: 400, y: 80, name: '项目X'},
                {x: 400, y: 140, name: '项目Y'},
                {x: 400, y: 200, name: '项目Z'}
            ];

            projects.forEach(project => {
                ctx.fillStyle = '#e84393';
                ctx.fillRect(project.x, project.y, 120, 40);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(project.x, project.y, 120, 40);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(project.name, project.x + 60, project.y + 25);
                
                // 连接线
                ctx.strokeStyle = '#fd79a8';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(300, 140);
                ctx.lineTo(project.x, project.y + 20);
                ctx.stroke();
            });

            // 架构图标
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(580, 120, 80, 60);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 2;
            ctx.strokeRect(580, 120, 80, 60);
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('架构设计', 620, 155);

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('设计文档和架构模式的重用提高开发效率', 350, 280);
        }

        // 绘制程序代码
        function drawCodeElement() {
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('程序代码重用', 350, 40);

            // 代码库
            ctx.fillStyle = '#00b894';
            ctx.fillRect(50, 80, 180, 150);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 80, 180, 150);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('代码库', 140, 110);

            // 代码组件
            const components = [
                {name: '工具类库', y: 130},
                {name: '数据访问层', y: 150},
                {name: 'UI组件', y: 170},
                {name: '业务组件', y: 190},
                {name: '通信模块', y: 210}
            ];

            components.forEach(comp => {
                ctx.fillStyle = '#00a085';
                ctx.fillRect(70, comp.y, 140, 15);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 1;
                ctx.strokeRect(70, comp.y, 140, 15);
                
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(comp.name, 140, comp.y + 10);
            });

            // 应用项目
            const apps = [
                {x: 300, y: 100, name: 'Web应用'},
                {x: 450, y: 100, name: '移动应用'},
                {x: 300, y: 180, name: '桌面应用'},
                {x: 450, y: 180, name: '服务应用'}
            ];

            apps.forEach(app => {
                ctx.fillStyle = '#74b9ff';
                ctx.fillRect(app.x, app.y, 100, 60);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(app.x, app.y, 100, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(app.name, app.x + 50, app.y + 35);
                
                // 重用箭头
                ctx.strokeStyle = '#00b894';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(230, 155);
                ctx.lineTo(app.x, app.y + 30);
                ctx.stroke();
            });

            // 重用统计
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(250, 270, 200, 40);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 270, 200, 40);
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('代码重用率：80%+', 350, 295);
        }

        // 绘制测试用例
        function drawTestElement() {
            ctx.fillStyle = '#fdcb6e';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('测试用例重用', 350, 40);

            // 测试用例库
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(150, 80, 200, 120);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(150, 80, 200, 120);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('测试用例库', 250, 110);

            // 测试类型
            const testTypes = ['单元测试', '集成测试', '系统测试', '性能测试'];
            testTypes.forEach((type, index) => {
                const y = 130 + index * 15;
                ctx.fillStyle = 'white';
                ctx.font = '11px Arial';
                ctx.fillText(`• ${type}`, 170, y);
            });

            // 自动化测试
            ctx.fillStyle = '#e17055';
            ctx.fillRect(400, 100, 120, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(400, 100, 120, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('自动化测试', 460, 130);
            ctx.font = '12px Arial';
            ctx.fillText('脚本重用', 460, 150);
            ctx.fillText('数据重用', 460, 170);

            // 测试数据
            ctx.fillStyle = '#a29bfe';
            ctx.fillRect(50, 120, 80, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 120, 80, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('测试数据', 90, 150);
            ctx.font = '10px Arial';
            ctx.fillText('边界值', 90, 170);
            ctx.fillText('异常值', 90, 185);

            // 连接线
            ctx.strokeStyle = '#fdcb6e';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(130, 160);
            ctx.lineTo(150, 140);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(350, 140);
            ctx.lineTo(400, 140);
            ctx.stroke();

            // 重用效益
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('测试用例重用减少测试工作量50%以上', 350, 250);
        }

        // 绘制领域知识
        function drawKnowledgeElement() {
            ctx.fillStyle = '#a29bfe';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('领域知识重用', 350, 40);

            // 知识库
            ctx.fillStyle = '#a29bfe';
            ctx.beginPath();
            ctx.arc(350, 150, 80, 0, Math.PI * 2);
            ctx.fill();
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 4;
            ctx.stroke();
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('领域知识库', 350, 160);

            // 知识类型
            const knowledgeTypes = [
                {name: '业务规则', angle: 0, color: '#74b9ff'},
                {name: '领域模型', angle: Math.PI / 2, color: '#fd79a8'},
                {name: '专业知识', angle: Math.PI, color: '#00b894'},
                {name: '最佳实践', angle: 3 * Math.PI / 2, color: '#fdcb6e'}
            ];

            knowledgeTypes.forEach(knowledge => {
                const x = 350 + Math.cos(knowledge.angle) * 150;
                const y = 150 + Math.sin(knowledge.angle) * 150;
                
                ctx.fillStyle = knowledge.color;
                ctx.fillRect(x - 50, y - 20, 100, 40);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(x - 50, y - 20, 100, 40);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(knowledge.name, x, y + 5);
                
                // 连接线
                ctx.strokeStyle = '#6c5ce7';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(350 + Math.cos(knowledge.angle) * 80, 150 + Math.sin(knowledge.angle) * 80);
                ctx.lineTo(350 + Math.cos(knowledge.angle) * 130, 150 + Math.sin(knowledge.angle) * 130);
                ctx.stroke();
            });

            // 知识传承
            ctx.fillStyle = '#e17055';
            ctx.fillRect(250, 280, 200, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 280, 200, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('知识传承与积累', 350, 305);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('B. 需求分析文档、设计文档、程序代码')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画序列
                    demonstrateElement('requirements');
                    setTimeout(() => demonstrateElement('design'), 2000);
                    setTimeout(() => demonstrateElement('code'), 4000);
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateElement('requirements');
            
            // 自动演示序列
            setTimeout(() => demonstrateElement('design'), 3000);
            setTimeout(() => demonstrateElement('code'), 6000);
            setTimeout(() => demonstrateElement('test'), 9000);
            setTimeout(() => demonstrateElement('knowledge'), 12000);
            setTimeout(() => demonstrateElement('requirements'), 15000);
        };
    </script>
</body>
</html>
