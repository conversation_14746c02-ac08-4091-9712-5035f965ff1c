<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机分级存储体系互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .storage-pyramid {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 40px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .storage-level {
            position: relative;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .storage-level:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .level-cpu {
            width: 120px;
            height: 60px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            animation: pulse 2s infinite;
        }

        .level-cache {
            width: 200px;
            height: 70px;
            background: linear-gradient(45deg, #feca57, #ff9ff3);
        }

        .level-main {
            width: 300px;
            height: 80px;
            background: linear-gradient(45deg, #48dbfb, #0abde3);
        }

        .level-secondary {
            width: 400px;
            height: 90px;
            background: linear-gradient(45deg, #1dd1a1, #10ac84);
        }

        .info-panel {
            position: absolute;
            right: -350px;
            top: 50%;
            transform: translateY(-50%);
            width: 300px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .info-panel.active {
            opacity: 1;
            right: -320px;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .option.selected {
            background: rgba(255,255,255,0.3);
            border-color: #feca57;
        }

        .option.correct {
            background: rgba(26, 188, 156, 0.8);
            border-color: #1abc9c;
        }

        .option.wrong {
            background: rgba(231, 76, 60, 0.8);
            border-color: #e74c3c;
        }

        .submit-btn {
            background: linear-gradient(45deg, #feca57, #ff9ff3);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 15px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .explanation {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            display: none;
        }

        .explanation.show {
            display: block;
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .data-flow {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #feca57;
            border-radius: 50%;
            opacity: 0;
        }

        .data-flow.animate {
            animation: dataFlow 2s ease-in-out;
        }

        @keyframes dataFlow {
            0% {
                opacity: 1;
                transform: scale(0.5);
            }
            50% {
                opacity: 0.8;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(0.5);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 10px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .storage-pyramid {
                height: 400px;
            }
            
            .info-panel {
                position: static;
                width: 100%;
                margin-top: 20px;
                opacity: 1;
                transform: none;
            }
            
            .options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🏗️ 计算机分级存储体系</h1>
            <p class="subtitle">探索存储器的层次结构，理解容量、速度与价格的平衡艺术</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 知识概览</h2>
            <p style="font-size: 1.1rem; line-height: 1.8; color: #666; text-align: center; margin-bottom: 30px;">
                计算机采用分级存储体系是为了解决存储器在<strong>容量</strong>、<strong>速度</strong>和<strong>价格</strong>之间的矛盾。
                <br>让我们通过互动动画来深入理解这个重要概念！
            </p>

            <div class="storage-pyramid">
                <div class="storage-level level-cpu" data-level="cpu">
                    <span>CPU寄存器</span>
                </div>
                <div class="storage-level level-cache" data-level="cache">
                    <span>高速缓存 Cache</span>
                </div>
                <div class="storage-level level-main" data-level="main">
                    <span>主存储器 MM</span>
                </div>
                <div class="storage-level level-secondary" data-level="secondary">
                    <span>辅助存储器</span>
                </div>

                <div class="info-panel" id="infoPanel">
                    <h3 id="levelTitle">点击存储层级</h3>
                    <div id="levelInfo">选择左侧的存储层级来查看详细信息</div>
                    <div id="levelStats"></div>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title" style="color: white;">🎯 知识检测</h2>
            <div class="question">
                计算机采用分级存储体系的主要目的是为了（ ）。
            </div>
            
            <div class="options">
                <div class="option" data-option="A">
                    <strong>A.</strong> 解决主存容量不足的问题
                </div>
                <div class="option" data-option="B">
                    <strong>B.</strong> 提高存储器读写可靠性
                </div>
                <div class="option" data-option="C">
                    <strong>C.</strong> 提高外设访问效率
                </div>
                <div class="option" data-option="D">
                    <strong>D.</strong> 解决存储的容量、价格和速度之间的矛盾
                </div>
            </div>

            <button class="submit-btn" onclick="checkAnswer()">提交答案</button>
            <button class="submit-btn" onclick="showAnimation()" style="background: linear-gradient(45deg, #48dbfb, #0abde3);">观看数据流动画</button>

            <div class="explanation" id="explanation">
                <h3>💡 答案解析</h3>
                <p><strong>正确答案：D</strong></p>
                <p>分级存储体系的核心目的是解决存储器在容量、价格和速度之间的矛盾：</p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li><strong>高速存储器</strong>：容量小、速度快、价格高（如CPU寄存器、Cache）</li>
                    <li><strong>低速存储器</strong>：容量大、速度慢、价格低（如硬盘、光盘）</li>
                    <li><strong>中速存储器</strong>：容量适中、速度适中、价格适中（如内存）</li>
                </ul>
                <p>通过合理的层次结构，既能保证系统的高性能，又能提供大容量存储，同时控制成本。</p>
            </div>
        </div>
    </div>

    <script>
        // 存储层级信息
        const storageInfo = {
            cpu: {
                title: "🔥 CPU寄存器",
                info: "最接近CPU的存储单元，速度最快但容量最小",
                stats: {
                    "访问速度": "1个时钟周期",
                    "容量": "几十到几百字节",
                    "价格": "最高",
                    "特点": "直接集成在CPU内部"
                }
            },
            cache: {
                title: "⚡ 高速缓存",
                info: "位于CPU和主存之间的高速存储器，平衡速度与容量",
                stats: {
                    "访问速度": "2-10个时钟周期", 
                    "容量": "几KB到几MB",
                    "价格": "很高",
                    "特点": "采用SRAM技术，分为L1、L2、L3多级"
                }
            },
            main: {
                title: "💾 主存储器",
                info: "计算机的主要工作存储器，程序和数据的临时存放地",
                stats: {
                    "访问速度": "几十到几百个时钟周期",
                    "容量": "几GB到几十GB", 
                    "价格": "中等",
                    "特点": "采用DRAM技术，断电后数据丢失"
                }
            },
            secondary: {
                title: "💿 辅助存储器",
                info: "大容量的永久存储设备，用于长期保存数据和程序",
                stats: {
                    "访问速度": "毫秒级",
                    "容量": "几百GB到几TB",
                    "价格": "最低", 
                    "特点": "非易失性存储，包括硬盘、SSD、光盘等"
                }
            }
        };

        let selectedOption = null;

        // 初始化事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 存储层级点击事件
            document.querySelectorAll('.storage-level').forEach(level => {
                level.addEventListener('click', function() {
                    const levelType = this.dataset.level;
                    showLevelInfo(levelType);
                    createDataFlow(this);
                });
            });

            // 选项点击事件
            document.querySelectorAll('.option').forEach(option => {
                option.addEventListener('click', function() {
                    // 移除之前的选择
                    document.querySelectorAll('.option').forEach(opt => {
                        opt.classList.remove('selected');
                    });
                    
                    // 添加当前选择
                    this.classList.add('selected');
                    selectedOption = this.dataset.option;
                });
            });
        });

        // 显示层级信息
        function showLevelInfo(levelType) {
            const info = storageInfo[levelType];
            const panel = document.getElementById('infoPanel');
            const title = document.getElementById('levelTitle');
            const infoDiv = document.getElementById('levelInfo');
            const statsDiv = document.getElementById('levelStats');

            title.textContent = info.title;
            infoDiv.textContent = info.info;
            
            let statsHTML = '<div style="margin-top: 15px;">';
            for (const [key, value] of Object.entries(info.stats)) {
                statsHTML += `<div style="margin: 8px 0; padding: 5px 10px; background: rgba(102, 126, 234, 0.1); border-radius: 5px;">
                    <strong>${key}:</strong> ${value}
                </div>`;
            }
            statsHTML += '</div>';
            statsDiv.innerHTML = statsHTML;

            panel.classList.add('active');
        }

        // 创建数据流动画
        function createDataFlow(element) {
            const rect = element.getBoundingClientRect();
            const containerRect = element.parentElement.getBoundingClientRect();
            
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    const dataFlow = document.createElement('div');
                    dataFlow.className = 'data-flow animate';
                    dataFlow.style.left = (rect.left - containerRect.left + rect.width/2) + 'px';
                    dataFlow.style.top = (rect.top - containerRect.top + rect.height/2) + 'px';
                    
                    element.parentElement.appendChild(dataFlow);
                    
                    setTimeout(() => {
                        dataFlow.remove();
                    }, 2000);
                }, i * 200);
            }
        }

        // 检查答案
        function checkAnswer() {
            if (!selectedOption) {
                alert('请先选择一个答案！');
                return;
            }

            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                const optionValue = option.dataset.option;
                if (optionValue === 'D') {
                    option.classList.add('correct');
                } else if (optionValue === selectedOption && optionValue !== 'D') {
                    option.classList.add('wrong');
                }
            });

            document.getElementById('explanation').classList.add('show');
            
            // 显示结果提示
            if (selectedOption === 'D') {
                setTimeout(() => {
                    alert('🎉 恭喜你答对了！你已经理解了分级存储体系的核心概念。');
                }, 500);
            } else {
                setTimeout(() => {
                    alert('💡 答案不正确，请查看解析了解正确答案。');
                }, 500);
            }
        }

        // 显示数据流动画
        function showAnimation() {
            const levels = document.querySelectorAll('.storage-level');
            
            // 模拟数据从CPU到辅存的流动
            levels.forEach((level, index) => {
                setTimeout(() => {
                    level.style.transform = 'scale(1.1)';
                    level.style.boxShadow = '0 0 30px rgba(254, 202, 87, 0.8)';
                    createDataFlow(level);
                    
                    setTimeout(() => {
                        level.style.transform = '';
                        level.style.boxShadow = '';
                    }, 800);
                }, index * 600);
            });

            // 显示动画说明
            setTimeout(() => {
                alert('💫 这就是数据在存储层次间的流动！CPU首先访问寄存器，然后是Cache，接着是主存，最后是辅存。越靠近CPU的存储器速度越快但容量越小。');
            }, 3000);
        }

        // 添加页面加载动画
        window.addEventListener('load', function() {
            // 延迟显示提示
            setTimeout(() => {
                const hint = document.createElement('div');
                hint.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(102, 126, 234, 0.9);
                    color: white;
                    padding: 15px 20px;
                    border-radius: 10px;
                    font-size: 14px;
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                    backdrop-filter: blur(10px);
                `;
                hint.innerHTML = '💡 点击存储层级查看详细信息<br>🎮 完成测试题检验学习效果';
                document.body.appendChild(hint);

                setTimeout(() => {
                    hint.style.animation = 'fadeInDown 0.5s ease-out reverse';
                    setTimeout(() => hint.remove(), 500);
                }, 5000);
            }, 2000);
        });
    </script>
</body>
</html>
