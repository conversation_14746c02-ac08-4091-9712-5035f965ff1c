<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一过程 (UP) 交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f4f8;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        .container {
            width: 100%;
            max-width: 900px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }

        h1, h2 {
            color: #0056b3;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        #question-container p {
            font-size: 1.1em;
            line-height: 1.6;
            text-align: left;
            margin-bottom: 25px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .option {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .option:hover:not(:disabled) {
            background-color: #e9ecef;
            border-color: #007bff;
        }

        .option:disabled {
            cursor: not-allowed;
            opacity: 0.7;
        }

        .option.correct {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
            font-weight: bold;
        }

        .option.incorrect {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        #feedback {
            font-size: 1.2em;
            font-weight: bold;
            margin-top: 10px;
            height: 30px;
        }

        #visualization-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        canvas {
            width: 100%;
            height: auto;
            display: block;
        }

        #explanation-container {
            text-align: left;
            margin-top: 30px;
            padding: 20px;
            background-color: #e9f5ff;
            border-radius: 8px;
            border-left: 5px solid #007bff;
            display: none; /* Initially hidden */
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>统一过程 (UP) 交互式学习</h1>

        <div id="question-container">
            <p><strong>题目：</strong>统一过程 (UP) 定义了初始阶段、精化阶段、构建阶段、移交阶段和产生阶段，每个阶段以达到某个里程碑时结束，其中 <strong>( &nbsp; &nbsp; )</strong> 的里程碑是生命周期架构。</p>
            <div class="options">
                <button class="option" data-option="A">A. 初始阶段</button>
                <button class="option" data-option="B">B. 精化阶段</button>
                <button class="option" data-option="C">C. 构建阶段</button>
                <button class="option" data-option="D">D. 移交阶段</button>
            </div>
            <div id="feedback"></div>
        </div>

        <div id="visualization-container">
            <canvas id="up-canvas" width="800" height="250"></canvas>
        </div>

        <div id="explanation-container">
            <h2>知识点解析</h2>
            <p><strong>统一过程 (Unified Process, UP)</strong> 是一个迭代式软件开发过程框架。它将软件开发生命周期划分为四个主要阶段：</p>
            <ul>
                <li><strong>初始阶段 (Inception):</strong> 核心任务是确定项目的范围和业务目标。它的里程碑是<strong>生命周期目标</strong>，意味着所有参与者对项目目标达成共识。</li>
                <li><strong>精化阶段 (Elaboration):</strong> 核心任务是分析问题领域、建立软件架构基础。它的里程碑是<strong>生命周期架构</strong>，意味着有了一个稳定的、可执行的架构基础。<strong>这是本题的答案。</strong></li>
                <li><strong>构建阶段 (Construction):</strong> 核心任务是开发剩余的功能，并将其集成为一个完整的产品。它的里程碑是<strong>初始运作功能</strong>，意味着第一个可交付给用户的测试版本完成了。</li>
                <li><strong>移交阶段 (Transition):</strong> 核心任务是将软件产品交付给最终用户。它的里程碑是<strong>产品发布</strong>，意味着产品已经准备好，可以正式使用了。</li>
            </ul>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('up-canvas');
        const ctx = canvas.getContext('2d');
        const options = document.querySelectorAll('.option');
        const feedbackEl = document.getElementById('feedback');
        const explanationContainer = document.getElementById('explanation-container');
        const correctAnswer = 'B';

        const phases = [
            { name: '初始阶段', color: '#3498db', milestone: '生命周期目标' },
            { name: '精化阶段', color: '#2ecc71', milestone: '生命周期架构' },
            { name: '构建阶段', color: '#f1c40f', milestone: '初始运作功能' },
            { name: '移交阶段', color: '#e74c3c', milestone: '产品发布' }
        ];

        let animationState = {
            progress: 0,
            animating: false,
            targetPhase: -1
        };

        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            const boxWidth = 180;
            const boxHeight = 80;
            const gap = 20;
            const startX = (canvas.width - (boxWidth * 4 + gap * 3)) / 2;
            const y = 50;

            phases.forEach((phase, index) => {
                const x = startX + index * (boxWidth + gap);

                // Draw phase box
                ctx.fillStyle = phase.color;
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.globalAlpha = 0.8;
                ctx.fillRect(x, y, boxWidth, boxHeight);
                ctx.strokeRect(x, y, boxWidth, boxHeight);
                
                // Draw phase name
                ctx.fillStyle = '#fff';
                ctx.globalAlpha = 1;
                ctx.font = 'bold 22px sans-serif';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(phase.name, x + boxWidth / 2, y + boxHeight / 2);
            });
            
            // Animation logic
            if (animationState.animating) {
                const currentPhase = Math.floor(animationState.progress);
                if (currentPhase < phases.length) {
                    const phaseProgress = animationState.progress - currentPhase;
                    
                    const x = startX + currentPhase * (boxWidth + gap);
                    const milestone = phases[currentPhase].milestone;
                    
                    // Highlight box
                    if(currentPhase === 1) { // Elaboration phase
                        ctx.strokeStyle = '#ffdd57';
                        ctx.lineWidth = 5;
                        ctx.shadowColor = '#ffdd57';
                        ctx.shadowBlur = 15;
                        ctx.strokeRect(x, y, boxWidth, boxHeight);
                        ctx.shadowColor = 'transparent';
                        ctx.shadowBlur = 0;
                    }

                    // Animate milestone text
                    ctx.globalAlpha = Math.min(1, phaseProgress * 2);
                    ctx.fillStyle = '#000';
                    ctx.font = '18px sans-serif';
                    ctx.fillText(milestone, x + boxWidth / 2, y + boxHeight + 40);
                    ctx.globalAlpha = 1;

                    // Draw connecting arrow to milestone
                    ctx.beginPath();
                    ctx.moveTo(x + boxWidth / 2, y + boxHeight);
                    const arrowY = y + boxHeight + 20;
                    ctx.lineTo(x + boxWidth / 2, arrowY - 5);
                    ctx.strokeStyle = '#555';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    // Arrowhead
                    ctx.beginPath();
                    ctx.moveTo(x + boxWidth / 2, arrowY);
                    ctx.lineTo(x + boxWidth / 2 - 5, arrowY - 5);
                    ctx.lineTo(x + boxWidth / 2 + 5, arrowY - 5);
                    ctx.closePath();
                    ctx.fillStyle = '#555';
                    ctx.fill();
                }

                animationState.progress += 0.02; // Animation speed
                if (animationState.progress >= phases.length) {
                    animationState.animating = false;
                }
            }
             if(animationState.animating) requestAnimationFrame(draw);
        }

        function handleOptionClick(e) {
            const selectedOption = e.target.dataset.option;
            
            options.forEach(opt => {
                opt.disabled = true;
                if (opt.dataset.option === correctAnswer) {
                    opt.classList.add('correct');
                } else if (opt.dataset.option === selectedOption) {
                    opt.classList.add('incorrect');
                }
            });

            if (selectedOption === correctAnswer) {
                feedbackEl.textContent = '回答正确！🎉';
                feedbackEl.style.color = '#155724';
                startAnimation();
            } else {
                feedbackEl.textContent = '回答错误，再想想哦。正确答案是 B。';
                feedbackEl.style.color = '#721c24';
                startAnimation();
            }
        }
        
        function startAnimation() {
            explanationContainer.style.display = 'block';
            animationState.animating = true;
            animationState.progress = 0;
            requestAnimationFrame(draw);
        }

        options.forEach(button => {
            button.addEventListener('click', handleOptionClick);
        });

        // Initial draw
        draw();
    </script>

</body>
</html> 