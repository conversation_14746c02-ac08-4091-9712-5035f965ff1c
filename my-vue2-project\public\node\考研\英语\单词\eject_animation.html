<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Eject</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Teko:wght@400;700&family=Roboto:wght@300;400&display=swap');

        :root {
            --primary-color: #f44336; /* Fiery Red */
            --secondary-color: #d32f2f;
            --glow-color: #ff5722;
            --light-bg: #4e2c2c;
            --panel-bg: #2d1a1a;
            --text-color: #ffcdd2;
            --canvas-bg: #1a1111;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #110b0b;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border: 1px solid var(--primary-color);
            border-radius: 20px;
            box-shadow: 0 0 40px rgba(244, 67, 54, 0.3);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-family: 'Teko', sans-serif;
            font-size: 4.5em;
            color: var(--primary-color);
            margin: 0;
            text-shadow: 0 0 10px var(--glow-color);
        }

        .word-panel .pronunciation {
            font-family: 'Teko', sans-serif;
            font-size: 2em;
            color: var(--glow-color);
            margin-bottom: 20px;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: rgba(0,0,0,0.2);
            border-left: 3px solid var(--primary-color);
            border-radius: 10px;
        }

        .morpheme-btn {
            font-family: 'Teko', sans-serif;
            padding: 8px 20px;
            border: 2px solid var(--glow-color);
            border-radius: 5px;
            background-color: transparent;
            color: var(--glow-color);
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 0 15px var(--glow-color);
        }

        .animation-panel {
            flex: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            background: var(--canvas-bg);
            overflow: hidden;
        }

        #animation-canvas {
            width: 100%;
            height: 100%;
        }
        
        .control-button {
            font-family: 'Teko', sans-serif;
            position: absolute;
            bottom: 20px;
            padding: 15px 40px;
            font-size: 1.5em;
            color: #fff;
            background: var(--primary-color);
            border: 2px solid var(--glow-color);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            text-shadow: 0 0 5px black;
            z-index: 10;
        }
        .control-button:hover { 
            background-color: var(--secondary-color);
            box-shadow: 0 0 20px var(--glow-color);
        }
        .control-button.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>eject</h1>
            <p class="pronunciation">[iˈdʒekt]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 喷射, 驱逐, 弹出</p>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析 (GSAP 动画)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="e-game">e- (out, 向外)</button>
                    <button class="morpheme-btn" data-activity="ject-game">-ject- (to throw, 扔)</button>
                </div>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动 (GSAP 动画)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示：火山喷发</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="control-btn" class="control-button hidden">Eject!</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const controlBtn = document.getElementById('control-btn');
        let currentTicker = null;
        let elements = [];

        const panel = canvas.parentElement;
        canvas.width = panel.clientWidth;
        canvas.height = panel.clientHeight;

        function clearCanvas() {
            ctx.fillStyle = 'rgba(26, 17, 17, 0.5)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
        
        function stopCurrentAnimation() {
            if (currentTicker) gsap.ticker.remove(currentTicker);
            gsap.killTweensOf(elements);
            elements = [];
        }

        class Particle {
            constructor({x, y, color, size, alpha=1}) {
                this.x = x; this.y = y; this.color = color; this.size = size; this.alpha=alpha;
            }
            draw() {
                ctx.fillStyle = this.color;
                ctx.globalAlpha = this.alpha;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;
            }
        }

        // --- Morpheme Games ---
        function initEGame() {
            stopCurrentAnimation();
            const container = {x: canvas.width/2 - 100, y: canvas.height/2 - 50, w: 200, h: 100};
            for(let i=0; i<100; i++) {
                elements.push(new Particle({
                    x: container.x + Math.random() * container.w,
                    y: container.y + Math.random() * container.h,
                    color: '#ffcdd2',
                    size: 2
                }));
            }
            
            function draw() {
                clearCanvas();
                ctx.strokeStyle = '#f44336';
                ctx.strokeRect(container.x, container.y, container.w, container.h);
                elements.forEach(p => p.draw());
            }
            gsap.ticker.add(draw); currentTicker = draw;

            controlBtn.textContent = '飞出 (e-)';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                gsap.to(elements, {
                    x: `random(${container.x - 100}, ${container.x + container.w + 100})`,
                    y: `random(${container.y - 100}, ${container.y + container.h + 100})`,
                    alpha: 0,
                    duration: 1,
                    stagger: 0.01,
                    ease: 'power2.out'
                });
            };
        }

        function initJectGame() {
             stopCurrentAnimation();
             let catapult = { angle: -Math.PI/2 };
             let rock = new Particle({x: 100, y: canvas.height-100, color:'#ffcdd2', size: 10});
             elements.push(rock);

             function draw() {
                clearCanvas();
                // Draw catapult arm
                ctx.strokeStyle = '#d32f2f'; ctx.lineWidth = 10;
                ctx.beginPath();
                ctx.moveTo(100, canvas.height - 50);
                let endX = 100 + Math.cos(catapult.angle) * 80;
                let endY = canvas.height - 50 + Math.sin(catapult.angle) * 80;
                ctx.lineTo(endX, endY);
                ctx.stroke();
                // set rock position to end of arm if not thrown
                if (!gsap.isTweening(rock)) {
                   rock.x = endX; rock.y = endY;
                }
                elements.forEach(p=>p.draw());
             }
             gsap.ticker.add(draw); currentTicker = draw;

             controlBtn.textContent = '投掷 (ject-)';
             controlBtn.classList.remove('hidden');
             controlBtn.onclick = () => {
                const tl = gsap.timeline();
                tl.to(catapult, { angle: -Math.PI * 0.8, duration: 0.2, ease: 'power2.in' })
                  .to(rock, {
                      x: canvas.width - 100,
                      y: 100,
                      duration: 1,
                      ease: 'power1.out'
                  }, ">-0.1");
             };
        }
        
        // --- Full Animation ---
        function initFullAnimation() {
            stopCurrentAnimation();
            const volcano = {
                x: canvas.width/2,
                y: canvas.height,
                w: 200
            };
            
            function draw() {
                clearCanvas();
                // Draw Volcano
                ctx.fillStyle = '#4e342e';
                ctx.beginPath();
                ctx.moveTo(volcano.x - volcano.w/2, volcano.y);
                ctx.lineTo(volcano.x, volcano.y - 150);
                ctx.lineTo(volcano.x + volcano.w/2, volcano.y);
                ctx.closePath();
                ctx.fill();
                // Draw particles
                elements.forEach(p => p.draw());
            }
            gsap.ticker.add(draw); currentTicker = draw;
            
            controlBtn.textContent = 'Eject!';
            controlBtn.classList.remove('hidden');
            controlBtn.onclick = () => {
                stopCurrentAnimation(); // Clear old particles
                gsap.ticker.add(draw); currentTicker = draw; // re-add ticker
                
                for(let i=0; i<200; i++) {
                   const particle = new Particle({
                       x: volcano.x + (Math.random()-0.5) * 20,
                       y: volcano.y - 150,
                       color: `hsl(${Math.random()*20 + 10}, 100%, 60%)`,
                       size: Math.random()*4 + 1
                   });
                   elements.push(particle);
                   gsap.to(particle, {
                       x: `+=${(Math.random()-0.5)*400}`,
                       y: `-=${Math.random()*200 + 100}`,
                       alpha: 0,
                       size: 0,
                       duration: 2,
                       ease: 'power2.out',
                       delay: Math.random()*0.5
                   });
                }
            };
        }

        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                const activity = btn.dataset.activity;
                if (activity === 'e-game') initEGame();
                else if (activity === 'ject-game') initJectGame();
                else if (activity === 'full-animation') initFullAnimation();
            });
        });

        initFullAnimation();
    });
    </script>
</body>
</html> 