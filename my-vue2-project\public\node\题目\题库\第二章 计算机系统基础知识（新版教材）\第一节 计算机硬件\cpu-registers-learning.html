<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPU寄存器学习 - 程序计数器的奥秘</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .cpu-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            position: relative;
        }

        .register {
            width: 120px;
            height: 80px;
            border: 3px solid #667eea;
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 20px;
            background: white;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .register:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .register.active {
            border-color: #ff6b6b;
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            transform: scale(1.1);
        }

        .register-name {
            font-weight: bold;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .register-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }

        .register.active .register-value {
            color: white;
        }

        .memory-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin: 30px 0;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .memory-cell {
            width: 100px;
            height: 60px;
            border: 2px solid #ddd;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .memory-cell.current {
            border-color: #ff6b6b;
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            transform: scale(1.05);
        }

        .memory-address {
            font-size: 0.8rem;
            color: #666;
        }

        .memory-cell.current .memory-address {
            color: white;
        }

        .memory-instruction {
            font-size: 0.9rem;
            font-weight: bold;
            margin-top: 2px;
        }

        .control-panel {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .quiz-container {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.3);
        }

        .quiz-option.wrong {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.3);
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 1s infinite;
        }

        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 15px solid #ff6b6b;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .memory-cell.current .arrow {
            opacity: 1;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }

        .register-explanation {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .register-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .register-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .register-card.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-5px) scale(1.02);
        }

        .register-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .register-card h3 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .register-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .register-card.active p {
            color: rgba(255, 255, 255, 0.9);
        }

        .register-detail {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            text-align: left;
            font-size: 0.9rem;
            line-height: 1.6;
            opacity: 0;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .register-card.active .register-detail {
            opacity: 1;
            max-height: 200px;
        }

        .simulator-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
        }

        .simulator-controls {
            text-align: center;
            margin-bottom: 30px;
        }

        .execution-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }

        .flow-step {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            min-width: 150px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            opacity: 0.5;
        }

        .flow-step.active {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            transform: scale(1.1);
            opacity: 1;
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
        }

        .step-number {
            width: 30px;
            height: 30px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: bold;
        }

        .flow-step.active .step-number {
            background: white;
            color: #ff6b6b;
        }

        .step-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .step-description {
            font-size: 0.9rem;
            color: #666;
        }

        .flow-step.active .step-description {
            color: rgba(255, 255, 255, 0.9);
        }

        .flow-arrow {
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
        }

        .simulation-status {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            font-size: 1.1rem;
            margin-top: 20px;
        }

        .summary-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .summary-point {
            display: flex;
            align-items: flex-start;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .point-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            margin-top: 5px;
        }

        .point-text {
            flex: 1;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .execution-flow {
                flex-direction: column;
            }

            .flow-arrow {
                transform: rotate(90deg);
            }

            .register-explanation {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🖥️ CPU寄存器学习之旅</h1>
        
        <div class="section">
            <h2 class="section-title">什么是CPU寄存器？</h2>
            <div class="explanation">
                CPU寄存器就像是CPU的"小抽屉"，用来临时存放数据和指令。想象一下，如果CPU是一个厨师，那么寄存器就是厨师手边的调料盒，可以快速取用各种"调料"（数据）来完成"烹饪"（计算）。
            </div>
            
            <div class="cpu-container">
                <div class="register" id="ir-register">
                    <div class="register-name">IR 指令寄存器</div>
                    <div class="register-value">ADD A,B</div>
                </div>
                <div class="register" id="pc-register">
                    <div class="register-name">PC 程序计数器</div>
                    <div class="register-value" id="pc-value">1000</div>
                </div>
                <div class="register" id="ar-register">
                    <div class="register-name">AR 地址寄存器</div>
                    <div class="register-value">1000</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">程序计数器（PC）的神奇之处</h2>
            <div class="explanation">
                程序计数器（PC）是CPU中最重要的寄存器之一！它就像一个"导航仪"，始终指向下一条要执行的指令地址。每执行完一条指令，PC就会自动加1，指向下一条指令。这样程序就能按顺序执行了！
            </div>
            
            <div class="memory-container" id="memory-container">
                <!-- 内存单元将通过JavaScript动态生成 -->
            </div>
            
            <div class="control-panel">
                <button class="btn" onclick="startExecution()">▶️ 开始执行</button>
                <button class="btn" onclick="stepExecution()">⏭️ 单步执行</button>
                <button class="btn" onclick="resetExecution()">🔄 重置</button>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">互动小游戏：猜猜PC的值</h2>
            <div class="quiz-container">
                <div class="quiz-question" id="quiz-question">
                    当前PC值为1002，执行完当前指令后，PC的值应该是多少？
                </div>
                <div class="quiz-options" id="quiz-options">
                    <div class="quiz-option" onclick="checkAnswer(this, false)">A. 1002</div>
                    <div class="quiz-option" onclick="checkAnswer(this, true)">B. 1003</div>
                    <div class="quiz-option" onclick="checkAnswer(this, false)">C. 1001</div>
                    <div class="quiz-option" onclick="checkAnswer(this, false)">D. 1004</div>
                </div>
                <div id="quiz-result" style="text-align: center; margin-top: 20px; font-size: 1.2rem;"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">深入理解：各个寄存器的作用</h2>

            <div class="register-explanation">
                <div class="register-card" onclick="showRegisterDetail('PC')">
                    <div class="register-icon">🎯</div>
                    <h3>程序计数器 (PC)</h3>
                    <p>CPU的"导航仪"，指向下一条指令的地址</p>
                    <div class="register-detail" id="PC-detail">
                        <strong>核心作用：</strong>确保程序指令能够连续执行<br>
                        <strong>工作原理：</strong>每执行完一条指令，PC自动加1<br>
                        <strong>重要性：</strong>没有PC，CPU就不知道下一步该做什么！
                    </div>
                </div>

                <div class="register-card" onclick="showRegisterDetail('IR')">
                    <div class="register-icon">📋</div>
                    <h3>指令寄存器 (IR)</h3>
                    <p>存储当前正在执行的指令</p>
                    <div class="register-detail" id="IR-detail">
                        <strong>核心作用：</strong>保存当前正在执行的指令<br>
                        <strong>工作原理：</strong>从内存取指令到IR，然后译码执行<br>
                        <strong>重要性：</strong>CPU执行指令的"工作台"
                    </div>
                </div>

                <div class="register-card" onclick="showRegisterDetail('AR')">
                    <div class="register-icon">📍</div>
                    <h3>地址寄存器 (AR)</h3>
                    <p>保存当前访问的内存地址</p>
                    <div class="register-detail" id="AR-detail">
                        <strong>核心作用：</strong>保存CPU要访问的内存单元地址<br>
                        <strong>工作原理：</strong>在内存读写操作期间保持地址信息<br>
                        <strong>重要性：</strong>确保内存访问的准确性
                    </div>
                </div>

                <div class="register-card" onclick="showRegisterDetail('ID')">
                    <div class="register-icon">🔍</div>
                    <h3>指令译码器 (ID)</h3>
                    <p>解析指令的操作码，确定要执行的操作</p>
                    <div class="register-detail" id="ID-detail">
                        <strong>核心作用：</strong>识别指令要求的具体操作<br>
                        <strong>工作原理：</strong>对操作码进行测试和译码<br>
                        <strong>重要性：</strong>CPU理解指令含义的"翻译官"
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 终极挑战：CPU执行流程模拟器</h2>
            <div class="simulator-container">
                <div class="simulator-controls">
                    <button class="btn" onclick="startSimulation()">🚀 开始模拟</button>
                    <button class="btn" onclick="pauseSimulation()">⏸️ 暂停</button>
                    <button class="btn" onclick="resetSimulation()">🔄 重置</button>
                </div>

                <div class="execution-flow" id="execution-flow">
                    <div class="flow-step" id="step-fetch">
                        <div class="step-number">1</div>
                        <div class="step-title">取指令</div>
                        <div class="step-description">根据PC从内存取出指令</div>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-step" id="step-decode">
                        <div class="step-number">2</div>
                        <div class="step-title">译码</div>
                        <div class="step-description">ID解析指令操作码</div>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-step" id="step-execute">
                        <div class="step-number">3</div>
                        <div class="step-title">执行</div>
                        <div class="step-description">执行具体操作</div>
                    </div>
                    <div class="flow-arrow">→</div>
                    <div class="flow-step" id="step-update">
                        <div class="step-number">4</div>
                        <div class="step-title">更新PC</div>
                        <div class="step-description">PC自动加1</div>
                    </div>
                </div>

                <div class="simulation-status" id="simulation-status">
                    点击"开始模拟"观看CPU执行指令的完整流程！
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📚 知识总结</h2>
            <div class="summary-container">
                <div class="summary-point">
                    <div class="point-icon">✅</div>
                    <div class="point-text">
                        <strong>程序计数器（PC）是正确答案！</strong><br>
                        PC负责指向下一条要执行的指令地址，每执行完一条指令就自动加1，确保程序按顺序执行。
                    </div>
                </div>

                <div class="summary-point">
                    <div class="point-icon">🔄</div>
                    <div class="point-text">
                        <strong>CPU执行指令的循环过程：</strong><br>
                        取指令 → 译码 → 执行 → 更新PC → 取下一条指令...
                    </div>
                </div>

                <div class="summary-point">
                    <div class="point-icon">🎯</div>
                    <div class="point-text">
                        <strong>记忆要点：</strong><br>
                        PC = 程序计数器 = 指令地址的"导航仪" = 自动加1的神奇寄存器
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPC = 1000;
        let isExecuting = false;
        let executionStep = 0;
        const totalSteps = 6;
        
        const instructions = [
            { address: 1000, instruction: "LOAD A" },
            { address: 1001, instruction: "ADD B" },
            { address: 1002, instruction: "STORE C" },
            { address: 1003, instruction: "JUMP 1005" },
            { address: 1004, instruction: "NOP" },
            { address: 1005, instruction: "HALT" }
        ];

        // 初始化内存显示
        function initMemory() {
            const container = document.getElementById('memory-container');
            container.innerHTML = '';
            
            instructions.forEach((inst, index) => {
                const cell = document.createElement('div');
                cell.className = 'memory-cell';
                cell.id = `memory-${inst.address}`;
                
                const arrow = document.createElement('div');
                arrow.className = 'arrow';
                cell.appendChild(arrow);
                
                const address = document.createElement('div');
                address.className = 'memory-address';
                address.textContent = inst.address;
                cell.appendChild(address);
                
                const instruction = document.createElement('div');
                instruction.className = 'memory-instruction';
                instruction.textContent = inst.instruction;
                cell.appendChild(instruction);
                
                cell.onclick = () => highlightMemoryCell(inst.address);
                container.appendChild(cell);
            });
            
            highlightMemoryCell(currentPC);
        }

        function highlightMemoryCell(address) {
            // 移除所有高亮
            document.querySelectorAll('.memory-cell').forEach(cell => {
                cell.classList.remove('current');
            });
            
            // 高亮当前地址
            const currentCell = document.getElementById(`memory-${address}`);
            if (currentCell) {
                currentCell.classList.add('current');
            }
            
            // 更新PC显示
            document.getElementById('pc-value').textContent = address;
            
            // 高亮PC寄存器
            const pcRegister = document.getElementById('pc-register');
            pcRegister.classList.add('active', 'pulse');
            setTimeout(() => {
                pcRegister.classList.remove('pulse');
            }, 1000);
        }

        function startExecution() {
            if (isExecuting) return;
            
            isExecuting = true;
            executionStep = 0;
            currentPC = 1000;
            
            const interval = setInterval(() => {
                if (executionStep >= totalSteps) {
                    clearInterval(interval);
                    isExecuting = false;
                    return;
                }
                
                stepExecution();
            }, 1500);
        }

        function stepExecution() {
            if (executionStep >= totalSteps) return;
            
            const instruction = instructions[executionStep];
            
            // 高亮当前指令
            highlightMemoryCell(instruction.address);
            
            // 更新IR寄存器
            const irRegister = document.getElementById('ir-register');
            irRegister.querySelector('.register-value').textContent = instruction.instruction;
            irRegister.classList.add('active');
            
            setTimeout(() => {
                irRegister.classList.remove('active');
                
                // PC自动加1（除非是跳转指令）
                if (instruction.instruction.startsWith('JUMP')) {
                    currentPC = 1005;
                    executionStep = 5; // 跳转到HALT指令
                } else {
                    currentPC++;
                    executionStep++;
                }
                
                // 更新进度条
                const progress = (executionStep / totalSteps) * 100;
                document.getElementById('progress-fill').style.width = progress + '%';
                
                if (executionStep < totalSteps) {
                    highlightMemoryCell(currentPC);
                }
            }, 800);
        }

        function resetExecution() {
            isExecuting = false;
            executionStep = 0;
            currentPC = 1000;
            
            // 重置所有寄存器
            document.querySelectorAll('.register').forEach(reg => {
                reg.classList.remove('active');
            });
            
            // 重置IR
            document.getElementById('ir-register').querySelector('.register-value').textContent = 'LOAD A';
            
            // 重置进度条
            document.getElementById('progress-fill').style.width = '0%';
            
            highlightMemoryCell(currentPC);
        }

        function checkAnswer(option, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const result = document.getElementById('quiz-result');
            
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === option) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (opt.textContent.includes('1003')) {
                    opt.classList.add('correct');
                }
            });
            
            if (isCorrect) {
                result.innerHTML = '🎉 恭喜答对了！PC确实会自动加1指向下一条指令！';
                result.style.color = '#4CAF50';
            } else {
                result.innerHTML = '❌ 答错了！记住：PC会自动加1，从1002变成1003';
                result.style.color = '#f44336';
            }
            
            // 3秒后重置题目
            setTimeout(() => {
                options.forEach(opt => {
                    opt.style.pointerEvents = 'auto';
                    opt.classList.remove('correct', 'wrong');
                });
                result.innerHTML = '';
                
                // 生成新题目
                const newPC = 1000 + Math.floor(Math.random() * 5);
                document.getElementById('quiz-question').textContent = 
                    `当前PC值为${newPC}，执行完当前指令后，PC的值应该是多少？`;
                
                const correctAnswer = newPC + 1;
                const wrongAnswers = [newPC, newPC - 1, newPC + 2];
                
                const allAnswers = [correctAnswer, ...wrongAnswers];
                allAnswers.sort(() => Math.random() - 0.5);
                
                options.forEach((opt, index) => {
                    const answer = allAnswers[index];
                    opt.textContent = `${String.fromCharCode(65 + index)}. ${answer}`;
                    opt.onclick = () => checkAnswer(opt, answer === correctAnswer);
                });
            }, 3000);
        }

        // 寄存器详情显示
        function showRegisterDetail(registerType) {
            // 移除所有活动状态
            document.querySelectorAll('.register-card').forEach(card => {
                card.classList.remove('active');
            });

            // 激活当前卡片
            event.currentTarget.classList.add('active');

            // 添加一些动画效果
            setTimeout(() => {
                const detail = document.getElementById(`${registerType}-detail`);
                if (detail) {
                    detail.style.animation = 'fadeInUp 0.5s ease-out';
                }
            }, 100);
        }

        // 模拟器相关变量
        let simulationRunning = false;
        let simulationStep = 0;
        let simulationInterval;

        function startSimulation() {
            if (simulationRunning) return;

            simulationRunning = true;
            simulationStep = 0;

            // 重置所有步骤
            document.querySelectorAll('.flow-step').forEach(step => {
                step.classList.remove('active');
            });

            simulationInterval = setInterval(() => {
                if (simulationStep >= 4) {
                    simulationStep = 0;
                    // 重置所有步骤
                    document.querySelectorAll('.flow-step').forEach(step => {
                        step.classList.remove('active');
                    });
                }

                executeSimulationStep(simulationStep);
                simulationStep++;
            }, 2000);
        }

        function pauseSimulation() {
            simulationRunning = false;
            if (simulationInterval) {
                clearInterval(simulationInterval);
            }
        }

        function resetSimulation() {
            pauseSimulation();
            simulationStep = 0;

            // 重置所有步骤
            document.querySelectorAll('.flow-step').forEach(step => {
                step.classList.remove('active');
            });

            document.getElementById('simulation-status').textContent =
                '点击"开始模拟"观看CPU执行指令的完整流程！';
        }

        function executeSimulationStep(step) {
            // 移除之前的活动状态
            document.querySelectorAll('.flow-step').forEach(s => {
                s.classList.remove('active');
            });

            const steps = ['step-fetch', 'step-decode', 'step-execute', 'step-update'];
            const stepNames = ['取指令', '译码', '执行', '更新PC'];
            const stepDescriptions = [
                '根据PC值从内存中取出指令，存入IR寄存器',
                '指令译码器(ID)解析指令的操作码，确定要执行的操作',
                'CPU执行具体的操作（如加法、存储等）',
                '程序计数器(PC)自动加1，指向下一条指令地址'
            ];

            if (step < steps.length) {
                const currentStep = document.getElementById(steps[step]);
                currentStep.classList.add('active');

                document.getElementById('simulation-status').innerHTML =
                    `<strong>正在执行：${stepNames[step]}</strong><br>${stepDescriptions[step]}`;

                // 添加一些特殊效果
                if (step === 3) { // 更新PC步骤
                    setTimeout(() => {
                        const pcValue = document.getElementById('pc-value');
                        const currentValue = parseInt(pcValue.textContent);
                        pcValue.textContent = currentValue + 1;

                        // 高亮PC寄存器
                        const pcRegister = document.getElementById('pc-register');
                        pcRegister.classList.add('pulse');
                        setTimeout(() => {
                            pcRegister.classList.remove('pulse');
                        }, 1000);
                    }, 500);
                }
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            initMemory();

            // 添加寄存器点击事件
            document.getElementById('pc-register').onclick = () => {
                showTooltip('程序计数器（PC）：存储下一条要执行指令的地址，每执行完一条指令就自动加1！这就是为什么程序能按顺序执行的关键！');
            };

            document.getElementById('ir-register').onclick = () => {
                showTooltip('指令寄存器（IR）：存储当前正在执行的指令内容。就像CPU的"工作台"，放着正在处理的任务。');
            };

            document.getElementById('ar-register').onclick = () => {
                showTooltip('地址寄存器（AR）：存储当前访问的内存地址。确保CPU能准确找到需要的数据位置。');
            };
        };

        // 显示提示信息
        function showTooltip(message) {
            // 创建提示框
            const tooltip = document.createElement('div');
            tooltip.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 20px;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                z-index: 1000;
                max-width: 400px;
                text-align: center;
                font-size: 1.1rem;
                line-height: 1.6;
                animation: fadeInUp 0.3s ease-out;
            `;
            tooltip.textContent = message;

            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '知道了';
            closeBtn.style.cssText = `
                background: white;
                color: #667eea;
                border: none;
                padding: 8px 20px;
                border-radius: 20px;
                margin-top: 15px;
                cursor: pointer;
                font-weight: bold;
            `;
            closeBtn.onclick = () => document.body.removeChild(tooltip);

            tooltip.appendChild(document.createElement('br'));
            tooltip.appendChild(closeBtn);
            document.body.appendChild(tooltip);

            // 3秒后自动关闭
            setTimeout(() => {
                if (document.body.contains(tooltip)) {
                    document.body.removeChild(tooltip);
                }
            }, 5000);
        }
    </script>
</body>
</html>
