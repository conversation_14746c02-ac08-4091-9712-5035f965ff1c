<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>架构策略选择 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .scenario-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .scenario-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .scenario-highlight {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.2rem;
            color: #333;
            font-weight: bold;
        }

        .demo-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .demo-title {
            font-size: 1.8rem;
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        #strategyCanvas {
            border: 2px solid #eee;
            border-radius: 15px;
            background: #fafafa;
            cursor: pointer;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .btn.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .question-text {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            font-size: 1.1rem;
            border: none;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .option:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.2);
        }

        .option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            animation: shake 0.5s ease-in-out;
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            animation: fadeIn 1s ease-out;
            display: none;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.6;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .strategy-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .strategy-item {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
        }

        .strategy-item h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .strategy-item p {
            color: #666;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .strategy-item .use-case {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
            margin-top: 10px;
        }

        .highlight {
            background: rgba(255, 255, 0, 0.3);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">架构策略选择</h1>
            <p class="subtitle">理解可用性架构策略的应用</p>
        </div>

        <div class="scenario-card">
            <h2 class="scenario-title">🎯 核心场景分析</h2>
            <div class="scenario-highlight">
                "系统主站断电后，能够在2分钟内自动切换到备用站点，并恢复正常运行"
            </div>
            <p style="text-align: center; color: #666; font-size: 1.1rem; margin-top: 20px;">
                这个场景需要什么样的架构策略来实现？
            </p>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">🏗️ 架构策略可视化演示</h2>
            <div class="canvas-container">
                <canvas id="strategyCanvas" width="1000" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showStrategy('active-redundancy')">🔄 主动冗余</button>
                <button class="btn" onclick="showStrategy('information-hiding')">🔒 信息隐藏</button>
                <button class="btn" onclick="showStrategy('abstract-interface')">📋 抽象接口</button>
                <button class="btn" onclick="showStrategy('record-replay')">📹 记录/回放</button>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                针对"系统主站断电后，能够在2分钟内自动切换到备用站点"这一可用性需求，<br>
                应该采用（ ）架构策略来实现？
            </div>
            
            <div class="options">
                <button class="option" onclick="selectAnswer(this, 'A')">
                    A. 主动冗余
                </button>
                <button class="option" onclick="selectAnswer(this, 'B')">
                    B. 信息隐藏
                </button>
                <button class="option" onclick="selectAnswer(this, 'C')">
                    C. 抽象接口
                </button>
                <button class="option" onclick="selectAnswer(this, 'D')">
                    D. 记录/回放
                </button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3>📚 详细解析</h3>
            <div id="explanationContent"></div>
        </div>

        <div class="demo-section">
            <h2 class="demo-title">📊 架构策略对比分析</h2>
            <div class="strategy-comparison">
                <div class="strategy-item">
                    <h4>🔄 主动冗余 (Active Redundancy)</h4>
                    <p><strong>核心思想：</strong>多个相同组件同时运行，提供相同服务</p>
                    <p><strong>工作方式：</strong>主备系统同时处理请求，故障时无缝切换</p>
                    <p><strong>优势：</strong>零停机时间，快速故障恢复</p>
                    <div class="use-case">适用于：高可用性系统</div>
                </div>
                
                <div class="strategy-item">
                    <h4>🔒 信息隐藏 (Information Hiding)</h4>
                    <p><strong>核心思想：</strong>隐藏模块内部实现细节</p>
                    <p><strong>工作方式：</strong>通过封装减少模块间依赖</p>
                    <p><strong>优势：</strong>提高可修改性，降低耦合度</p>
                    <div class="use-case">适用于：模块化设计</div>
                </div>
                
                <div class="strategy-item">
                    <h4>📋 抽象接口 (Abstract Interface)</h4>
                    <p><strong>核心思想：</strong>定义统一的接口规范</p>
                    <p><strong>工作方式：</strong>通过接口分离实现和调用</p>
                    <p><strong>优势：</strong>提高可扩展性和可替换性</p>
                    <div class="use-case">适用于：组件解耦</div>
                </div>
                
                <div class="strategy-item">
                    <h4>📹 记录/回放 (Record/Replay)</h4>
                    <p><strong>核心思想：</strong>记录系统状态和操作序列</p>
                    <p><strong>工作方式：</strong>故障后重放操作恢复状态</p>
                    <p><strong>优势：</strong>精确的故障恢复</p>
                    <div class="use-case">适用于：事务处理系统</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('strategyCanvas');
        const ctx = canvas.getContext('2d');

        function showStrategy(type) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 移除所有按钮的active状态
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            
            switch(type) {
                case 'active-redundancy':
                    drawActiveRedundancy();
                    document.querySelectorAll('.btn')[0].classList.add('active');
                    break;
                case 'information-hiding':
                    drawInformationHiding();
                    document.querySelectorAll('.btn')[1].classList.add('active');
                    break;
                case 'abstract-interface':
                    drawAbstractInterface();
                    document.querySelectorAll('.btn')[2].classList.add('active');
                    break;
                case 'record-replay':
                    drawRecordReplay();
                    document.querySelectorAll('.btn')[3].classList.add('active');
                    break;
            }
        }

        function drawActiveRedundancy() {
            // 主服务器
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(150, 150, 120, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主服务器', 210, 190);
            ctx.fillText('(运行中)', 210, 210);
            
            // 备用服务器
            ctx.fillStyle = '#3498db';
            ctx.fillRect(350, 150, 120, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('备用服务器', 410, 190);
            ctx.fillText('(同步运行)', 410, 210);
            
            // 负载均衡器
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(550, 150, 120, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('负载均衡器', 610, 190);
            ctx.fillText('(分发请求)', 610, 210);
            
            // 用户
            ctx.fillStyle = '#9b59b6';
            ctx.fillRect(750, 150, 120, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('用户', 810, 200);
            
            // 连接线
            drawArrow(270, 200, 350, 200, '#27ae60');
            drawArrow(470, 200, 550, 200, '#3498db');
            drawArrow(670, 200, 750, 200, '#f39c12');
            
            // 故障模拟
            setTimeout(() => {
                // 主服务器故障
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(150, 150, 120, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('主服务器', 210, 190);
                ctx.fillText('(故障)', 210, 210);
                
                // 流量切换到备用
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 4;
                ctx.setLineDash([10, 5]);
                ctx.beginPath();
                ctx.moveTo(270, 200);
                ctx.lineTo(350, 200);
                ctx.stroke();
                ctx.setLineDash([]);
            }, 2000);
            
            // 标题和说明
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主动冗余：多个组件同时运行', 500, 50);
            
            ctx.font = '16px Arial';
            ctx.fillText('✅ 适合可用性需求：快速故障切换，零停机时间', 500, 350);
        }

        function drawInformationHiding() {
            // 模块A
            ctx.fillStyle = '#3498db';
            ctx.fillRect(150, 150, 150, 120);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('模块A', 225, 190);
            ctx.fillText('(内部实现隐藏)', 225, 210);
            
            // 模块B
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(400, 150, 150, 120);
            ctx.fillStyle = 'white';
            ctx.fillText('模块B', 475, 190);
            ctx.fillText('(内部实现隐藏)', 475, 210);
            
            // 公共接口
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(300, 300, 100, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('公共接口', 350, 335);
            
            // 连接线
            drawArrow(225, 270, 325, 300, '#3498db');
            drawArrow(475, 270, 375, 300, '#27ae60');
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('信息隐藏：封装内部实现', 500, 50);
            
            ctx.font = '16px Arial';
            ctx.fillText('❌ 不适合可用性需求：主要用于提高可修改性', 500, 420);
        }

        function drawAbstractInterface() {
            // 接口层
            ctx.fillStyle = '#9b59b6';
            ctx.fillRect(300, 100, 200, 60);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('抽象接口层', 400, 135);
            
            // 实现A
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(150, 250, 120, 80);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('实现A', 210, 295);
            
            // 实现B
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(350, 250, 120, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('实现B', 410, 295);
            
            // 实现C
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(550, 250, 120, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('实现C', 610, 295);
            
            // 连接线
            drawArrow(350, 160, 210, 250, '#9b59b6');
            drawArrow(400, 160, 410, 250, '#9b59b6');
            drawArrow(450, 160, 610, 250, '#9b59b6');
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('抽象接口：统一访问方式', 500, 50);
            
            ctx.font = '16px Arial';
            ctx.fillText('❌ 不适合可用性需求：主要用于组件解耦', 500, 420);
        }

        function drawRecordReplay() {
            // 系统状态记录
            ctx.fillStyle = '#3498db';
            ctx.fillRect(150, 150, 150, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('系统运行', 225, 190);
            ctx.fillText('(记录状态)', 225, 210);
            
            // 记录存储
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(400, 150, 150, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('操作记录', 475, 190);
            ctx.fillText('(状态快照)', 475, 210);
            
            // 故障恢复
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(650, 150, 150, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('故障恢复', 725, 190);
            ctx.fillText('(回放操作)', 725, 210);
            
            // 流程箭头
            drawArrow(300, 200, 400, 200, '#3498db');
            drawArrow(550, 200, 650, 200, '#f39c12');
            
            // 时间线
            ctx.strokeStyle = '#7f8c8d';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(100, 350);
            ctx.lineTo(850, 350);
            ctx.stroke();
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('正常运行', 225, 370);
            ctx.fillText('故障发生', 475, 370);
            ctx.fillText('状态恢复', 725, 370);
            
            ctx.font = 'bold 24px Arial';
            ctx.fillText('记录/回放：状态恢复机制', 500, 50);
            
            ctx.font = '16px Arial';
            ctx.fillText('❌ 不适合可用性需求：恢复时间较长，无法满足2分钟要求', 500, 420);
        }

        function drawArrow(fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI/6), toY - 15 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI/6), toY - 15 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function selectAnswer(element, answer) {
            // 移除所有选项的状态
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });
            
            const explanationDiv = document.getElementById('explanation');
            const explanationContent = document.getElementById('explanationContent');
            
            if (answer === 'A') {
                element.classList.add('correct');
                explanationContent.innerHTML = `
                    <p><strong>🎉 恭喜答对！主动冗余是最佳选择！</strong></p>
                    <p><strong>为什么选择主动冗余？</strong></p>
                    <p>• <span class="highlight">快速切换</span>：备用系统已经在运行，可以立即接管服务</p>
                    <p>• <span class="highlight">零停机</span>：用户感受不到服务中断</p>
                    <p>• <span class="highlight">满足时间要求</span>：能够在2分钟内完成切换</p>
                    <p>• <span class="highlight">高可用性</span>：专门为故障恢复设计的架构策略</p>
                    <p><strong>💡 关键理解：</strong>主动冗余通过多个组件同时运行来确保服务的连续性，是实现高可用性的核心策略！</p>
                    <p><strong>🔧 相关策略：</strong>心跳检测、Ping/Echo、被动冗余、选举机制等都是可用性架构策略家族的成员。</p>
                `;
            } else {
                element.classList.add('wrong');
                let wrongExplanation = '';
                switch(answer) {
                    case 'B':
                        wrongExplanation = '❌ 信息隐藏主要用于提高可修改性，通过封装隐藏实现细节，与故障恢复无关。';
                        break;
                    case 'C':
                        wrongExplanation = '❌ 抽象接口主要用于组件解耦和提高可扩展性，不能解决故障切换问题。';
                        break;
                    case 'D':
                        wrongExplanation = '❌ 记录/回放虽然能恢复状态，但恢复时间较长，无法满足2分钟的快速切换要求。';
                        break;
                }
                explanationContent.innerHTML = `
                    <p>${wrongExplanation}</p>
                    <p><strong>正确答案是A：主动冗余</strong></p>
                    <p><strong>核心原因：</strong></p>
                    <p>• 题目要求<span class="highlight">"2分钟内自动切换"</span>，这是典型的可用性需求</p>
                    <p>• 主动冗余让备用系统<span class="highlight">同时运行</span>，故障时可以立即切换</p>
                    <p>• 其他选项都不是针对可用性设计的架构策略</p>
                `;
            }
            
            explanationDiv.style.display = 'block';
        }

        // 初始化显示主动冗余
        setTimeout(() => {
            showStrategy('active-redundancy');
        }, 1000);
    </script>
</body>
</html>
