<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画：Conclude</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f2f5;
            margin: 0;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .header {
            padding: 20px 30px;
            background-color: #4a90e2;
            color: white;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 600;
        }
        .header p {
            margin: 5px 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .main-content {
            display: flex;
            flex-direction: row;
        }
        .canvas-container {
            flex-basis: 50%;
            position: relative;
            border-right: 1px solid #e8e8e8;
        }
        canvas {
            display: block;
            width: 100%;
            height: auto;
            aspect-ratio: 1 / 1;
        }
        .controls {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 10px 20px;
            font-size: 1em;
            color: #fff;
            background-color: #4a90e2;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .btn:hover {
            background-color: #357abd;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background-color: #a0c7e8;
            cursor: not-allowed;
        }
        .explanation {
            flex-basis: 50%;
            padding: 25px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .explanation h2 {
            color: #4a90e2;
            margin-top: 0;
        }
        .story {
            font-size: 1.1em;
            line-height: 1.8;
            color: #555;
        }
        .story-step {
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
            margin-bottom: 1em;
            display: none;
        }
        .story-step.active {
            opacity: 1;
            display: block;
        }
        .story-step strong {
            color: #d9534f;
            font-weight: 600;
        }
    </style>
</head>
<body>

<div class="container">
    <header>
        <h1>conclude</h1>
        <p>con (一起) + clude (关闭) → 下结论；结束</p>
    </header>
    <div class="main-content">
        <div class="canvas-container">
            <canvas id="word-canvas" width="400" height="400"></canvas>
            <div class="controls">
                <button id="start-btn" class="btn">开始动画</button>
                <button id="reset-btn" class="btn">重置</button>
            </div>
        </div>
        <div class="explanation">
            <h2>故事讲解</h2>
            <div class="story">
                <div id="step1" class="story-step active">
                    <p>想象一下，你是一名侦探，正在调查一个复杂的案件。桌子上散落着各种线索：一份文件，一张照片，一个指纹...</p>
                </div>
                <div id="step2" class="story-step">
                    <p>你把所有的线索都聚集在<strong>"一起" (con-)</strong>，仔细审视，寻找它们之间的联系。</p>
                </div>
                <div id="step3" class="story-step">
                    <p>经过缜密的分析，你终于得出了结论，将案件档案<strong>"关闭" (-clude)</strong>，成功破案。</p>
                </div>
                <div id="step4" class="story-step">
                    <p>所以, <strong>conclude</strong> 就是把所有信息汇总，得出一个最终的决定或结果，也就是<strong>"下结论"</strong>或<strong>"结束"</strong>。</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    const canvas = document.getElementById('word-canvas');
    const ctx = canvas.getContext('2d');
    const startBtn = document.getElementById('start-btn');
    const resetBtn = document.getElementById('reset-btn');
    const storySteps = [
        document.getElementById('step1'),
        document.getElementById('step2'),
        document.getElementById('step3'),
        document.getElementById('step4'),
    ];

    let animationId;
    let clues = [];
    let animationState = 'initial'; // initial, gathering, closing, finished

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;

    class Clue {
        constructor(icon, startX, startY, targetX, targetY) {
            this.icon = icon;
            this.x = startX;
            this.y = startY;
            this.startX = startX;
            this.startY = startY;
            this.targetX = targetX;
            this.targetY = targetY;
            this.size = 40;
            this.alpha = 1;
        }

        draw() {
            ctx.save();
            ctx.globalAlpha = this.alpha;
            ctx.font = `${this.size}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(this.icon, this.x, this.y);
            ctx.restore();
        }

        update() {
            const dx = this.targetX - this.x;
            const dy = this.targetY - this.y;
            const dist = Math.sqrt(dx * dx + dy * dy);
            if (dist > 1) {
                this.x += dx * 0.05;
                this.y += dy * 0.05;
            } else {
                this.x = this.targetX;
                this.y = this.targetY;
            }
        }
        
        isAtTarget() {
            return Math.abs(this.x - this.targetX) < 1 && Math.abs(this.y - this.targetY) < 1;
        }
    }

    function init() {
        cancelAnimationFrame(animationId);
        animationState = 'initial';
        startBtn.disabled = false;
        
        clues = [
            new Clue('📄', 50, 50, centerX - 50, centerY),
            new Clue('🖼️', canvas.width - 50, 50, centerX, centerY - 50),
            new Clue('👆', 50, canvas.height - 50, centerX, centerY + 50),
            new Clue('🔍', canvas.width - 50, canvas.height - 50, centerX + 50, centerY),
        ];

        setActiveStep(1);
        draw();
    }
    
    function setActiveStep(stepNumber) {
        storySteps.forEach((step, index) => {
            if (index + 1 === stepNumber) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
    }

    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        clues.forEach(clue => clue.draw());

        if (animationState === 'gathering') {
            ctx.save();
            ctx.fillStyle = '#4a90e2';
            ctx.font = 'bold 30px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('con- (一起)', centerX, 30);
            ctx.restore();
        }
        
        if (animationState === 'closing' || animationState === 'finished') {
             // Draw file folder
            ctx.fillStyle = '#f0e68c'; // Khaki
            ctx.strokeStyle = '#deb887'; // Burlywood
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.rect(centerX - 80, centerY - 60, 160, 120);
            ctx.fill();
            ctx.stroke();
             // Tab
            ctx.beginPath();
            ctx.rect(centerX - 70, centerY - 70, 40, 10);
            ctx.fill();
            ctx.stroke();

            if (animationState === 'finished') {
                ctx.save();
                ctx.fillStyle = '#d9534f';
                ctx.font = 'bold 30px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('-clude (关闭)', centerX, canvas.height - 30);
                
                ctx.strokeStyle = 'red';
                ctx.lineWidth = 5;
                ctx.font = 'bold 40px "Times New Roman"';
                ctx.fillStyle = 'red';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.translate(centerX, centerY);
                ctx.rotate(-0.2);
                ctx.strokeText("CASE CLOSED", 0, 0);
                ctx.fillText("CASE CLOSED", 0, 0);

                ctx.restore();
            }
        }
    }

    function animate() {
        animationId = requestAnimationFrame(animate);
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        if (animationState === 'gathering') {
            clues.forEach(clue => clue.update());
            const allAtTarget = clues.every(clue => clue.isAtTarget());
            if (allAtTarget) {
                animationState = 'closing';
                setActiveStep(3);
                setTimeout(() => {
                    animationState = 'finished';
                    startBtn.disabled = true;
                    setActiveStep(4);
                }, 1000); // Wait a bit before showing "closed"
            }
        }
        
        draw();
    }
    
    startBtn.addEventListener('click', () => {
        if (animationState === 'initial') {
            animationState = 'gathering';
            setActiveStep(2);
            startBtn.disabled = true;
            animate();
        }
    });

    resetBtn.addEventListener('click', () => {
        init();
    });

    window.onload = init;
</script>

</body>
</html> 