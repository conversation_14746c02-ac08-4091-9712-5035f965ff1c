<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式三大分类 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            font-weight: 300;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
        }

        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border-radius: 15px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            cursor: pointer;
        }

        .pattern-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .pattern-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(30px);
        }

        .pattern-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .pattern-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            font-weight: bold;
        }

        .creation { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .structure { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .behavior { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }

        .pattern-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .pattern-desc {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .pattern-examples {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            font-size: 0.9rem;
            color: #555;
        }

        .controls {
            text-align: center;
            margin: 40px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        .answer-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            backdrop-filter: blur(10px);
        }

        .correct-answer {
            color: #28a745;
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .bounce {
            animation: bounce 1s ease-in-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">设计模式三大分类</h1>
            <p class="subtitle">通过动画和交互学习设计模式的核心概念</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                按照设计模式的目的进行划分，现有的设计模式可以分为三类。其中<span class="highlight">创建型模式</span>通过采用抽象类所定义的接口，封装了系统中对象如何创建、组合等信息，其代表有（Singleton）模式等；<span class="highlight">结构型模式</span>主要用于如何组合已有的类和对象以获得更大的结构，其代表有 Adapter 模式等；<span class="highlight">（请作答此空）</span>模式主要用于对象之间的职责及其提供服务的分配方式，其代表有（Visitor）模式等。
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="gameCanvas" width="800" height="400"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="startAnimation()">开始动画演示</button>
            <button class="btn" onclick="showAnswer()">显示答案</button>
            <button class="btn" onclick="resetGame()">重新开始</button>
        </div>

        <div class="pattern-cards">
            <div class="pattern-card creation">
                <div class="pattern-icon creation">创</div>
                <div class="pattern-title">创建型模式</div>
                <div class="pattern-desc">负责对象的创建过程，封装对象的实例化细节</div>
                <div class="pattern-examples">
                    <strong>代表模式：</strong>Singleton（单例）、Factory（工厂）、Builder（建造者）
                </div>
            </div>

            <div class="pattern-card structure">
                <div class="pattern-icon structure">构</div>
                <div class="pattern-title">结构型模式</div>
                <div class="pattern-desc">关注类和对象的组合，形成更大的结构</div>
                <div class="pattern-examples">
                    <strong>代表模式：</strong>Adapter（适配器）、Decorator（装饰者）、Facade（外观）
                </div>
            </div>

            <div class="pattern-card behavior">
                <div class="pattern-icon behavior">行</div>
                <div class="pattern-title">行为型模式</div>
                <div class="pattern-desc">定义对象间的职责分配和通信方式</div>
                <div class="pattern-examples">
                    <strong>代表模式：</strong>Visitor（访问者）、Observer（观察者）、Strategy（策略）
                </div>
            </div>
        </div>

        <div class="answer-section" id="answerSection" style="display: none;">
            <div class="correct-answer">✓ 正确答案：A. 行为型</div>
            <p><strong>解析：</strong>设计模式按目的分为三类：</p>
            <ul style="margin: 15px 0; padding-left: 20px;">
                <li><strong>创建型模式：</strong>封装对象创建过程（如 Singleton）</li>
                <li><strong>结构型模式：</strong>组合类和对象形成更大结构（如 Adapter）</li>
                <li><strong>行为型模式：</strong>定义对象间职责分配和服务方式（如 Visitor）</li>
            </ul>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationFrame;
        let currentStep = 0;
        let particles = [];
        
        // 初始化
        function init() {
            drawBackground();
            drawInitialState();
        }
        
        function drawBackground() {
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
        
        function drawInitialState() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawBackground();
            
            // 绘制三个模式类型的占位符
            const patterns = [
                { name: '创建型', x: 150, y: 200, color: '#ff9a9e' },
                { name: '结构型', x: 400, y: 200, color: '#a8edea' },
                { name: '???', x: 650, y: 200, color: '#ffecd2' }
            ];
            
            patterns.forEach((pattern, index) => {
                drawPatternBox(pattern.x, pattern.y, pattern.name, pattern.color, index === 2);
            });
            
            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('设计模式三大分类', canvas.width / 2, 50);
        }
        
        function drawPatternBox(x, y, name, color, isQuestion = false) {
            const width = 120;
            const height = 80;
            
            // 绘制阴影
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(x - width/2 + 3, y - height/2 + 3, width, height);
            
            // 绘制主体
            const gradient = ctx.createLinearGradient(x - width/2, y - height/2, x + width/2, y + height/2);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, adjustColor(color, -20));
            ctx.fillStyle = gradient;
            ctx.fillRect(x - width/2, y - height/2, width, height);
            
            // 绘制边框
            ctx.strokeStyle = isQuestion ? '#ff6b6b' : 'rgba(255, 255, 255, 0.5)';
            ctx.lineWidth = isQuestion ? 3 : 2;
            ctx.strokeRect(x - width/2, y - height/2, width, height);
            
            // 绘制文字
            ctx.fillStyle = isQuestion ? '#ff6b6b' : '#333';
            ctx.font = isQuestion ? 'bold 18px Arial' : 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y + 5);
            
            if (isQuestion) {
                // 添加问号动画效果
                ctx.fillStyle = '#ff6b6b';
                ctx.font = 'bold 24px Arial';
                ctx.fillText('?', x, y + 30);
            }
        }
        
        function adjustColor(color, amount) {
            const hex = color.replace('#', '');
            const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + amount));
            const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + amount));
            const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + amount));
            return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
        }
        
        function startAnimation() {
            currentStep = 0;
            animateStep();
        }
        
        function animateStep() {
            if (currentStep >= 4) return;
            
            switch(currentStep) {
                case 0:
                    animateCreationPattern();
                    break;
                case 1:
                    animateStructurePattern();
                    break;
                case 2:
                    animateBehaviorPattern();
                    break;
                case 3:
                    showFinalAnswer();
                    break;
            }
            
            currentStep++;
            setTimeout(() => {
                if (currentStep < 4) animateStep();
            }, 2000);
        }
        
        function animateCreationPattern() {
            // 创建粒子效果
            createParticles(150, 200, '#ff9a9e');
            
            // 重绘并高亮创建型模式
            drawInitialState();
            drawHighlight(150, 200, '#ff9a9e');
            
            // 显示说明文字
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('负责对象的创建过程', 150, 300);
            ctx.fillText('如：Singleton模式', 150, 320);
        }
        
        function animateStructurePattern() {
            createParticles(400, 200, '#a8edea');
            
            drawInitialState();
            drawHighlight(400, 200, '#a8edea');
            
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('组合类和对象形成结构', 400, 300);
            ctx.fillText('如：Adapter模式', 400, 320);
        }
        
        function animateBehaviorPattern() {
            createParticles(650, 200, '#ffecd2');
            
            drawInitialState();
            
            // 将问号替换为"行为型"
            drawPatternBox(650, 200, '行为型', '#ffecd2', false);
            drawHighlight(650, 200, '#ffecd2');
            
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('定义对象间职责分配', 650, 300);
            ctx.fillText('如：Visitor模式', 650, 320);
        }
        
        function drawHighlight(x, y, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 4;
            ctx.setLineDash([5, 5]);
            ctx.strokeRect(x - 70, y - 50, 140, 100);
            ctx.setLineDash([]);
        }
        
        function createParticles(x, y, color) {
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: x + (Math.random() - 0.5) * 100,
                    y: y + (Math.random() - 0.5) * 100,
                    vx: (Math.random() - 0.5) * 4,
                    vy: (Math.random() - 0.5) * 4,
                    color: color,
                    life: 1.0
                });
            }
            animateParticles();
        }
        
        function animateParticles() {
            if (particles.length === 0) return;
            
            ctx.save();
            particles.forEach((particle, index) => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life -= 0.02;
                
                if (particle.life <= 0) {
                    particles.splice(index, 1);
                    return;
                }
                
                ctx.globalAlpha = particle.life;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                ctx.fill();
            });
            ctx.restore();
            
            if (particles.length > 0) {
                requestAnimationFrame(animateParticles);
            }
        }
        
        function showFinalAnswer() {
            drawInitialState();
            
            // 绘制所有三个模式
            drawPatternBox(150, 200, '创建型', '#ff9a9e');
            drawPatternBox(400, 200, '结构型', '#a8edea');
            drawPatternBox(650, 200, '行为型', '#ffecd2');
            
            // 绘制连接线
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(220, 200);
            ctx.lineTo(330, 200);
            ctx.moveTo(470, 200);
            ctx.lineTo(580, 200);
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 显示答案
            ctx.fillStyle = '#28a745';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('答案：A. 行为型', canvas.width / 2, 350);
        }
        
        function showAnswer() {
            document.getElementById('answerSection').style.display = 'block';
            document.getElementById('answerSection').scrollIntoView({ behavior: 'smooth' });
            
            // 动画显示卡片
            const cards = document.querySelectorAll('.pattern-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                    card.classList.add('bounce');
                }, index * 200);
            });
        }
        
        function resetGame() {
            currentStep = 0;
            particles = [];
            document.getElementById('answerSection').style.display = 'none';
            
            // 重置卡片动画
            const cards = document.querySelectorAll('.pattern-card');
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.classList.remove('bounce');
            });
            
            init();
        }
        
        // 初始化画布
        init();
        
        // 添加点击交互
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 检查是否点击了问号区域
            if (x > 580 && x < 720 && y > 150 && y < 250) {
                animateBehaviorPattern();
            }
        });
    </script>
</body>
</html>
