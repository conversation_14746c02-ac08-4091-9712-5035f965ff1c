<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Evolve</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #3498db; /* A blue color for "evolve" */
            --secondary-color: #2c3e50;
            --light-bg: #f8f9fa;
            --panel-bg: #ffffff;
            --text-color: #333;
            --text-muted: #7f8c8d;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--secondary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--text-muted);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #555;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            background-color: #000;
        }

        #animation-canvas {
            border-radius: 15px;
        }

        .control-button {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
            z-index: 10;
        }
        .control-button:hover {
            background-color: #2980b9;
            transform: translateX(-50%) translateY(-2px);
        }
        .control-button:active { 
            transform: translateX(-50%) translateY(1px); 
        }

    </style>
</head>
<body>

    <div class="container">
        <div class="word-panel">
            <h1>evolve</h1>
            <p class="pronunciation">[ɪˈvɒlv]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 发展，进化；逐步形成</p>
                <div class="example">
                    <p><strong>例句：</strong> The company has evolved into a major chemical manufacturer.</p>
                    <p><strong>翻译：</strong> 这家公司已发展成为一个主要的化学品制造商。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>词源解析</h3>
                <p><strong>e- (前缀):</strong> 表示"out" (出，向外)。</p>
                <p><strong>volv (词根):</strong> 来自拉丁语 <i>volvere</i>，表示"to roll" (滚动)。</p>
                <p class="insight"><strong>字面义:</strong> "to roll out" (滚出来) → 引申为展开、发展、进化。</p>
            </div>
            
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="evolve-btn" class="control-button">开始演变</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const panel = document.querySelector('.animation-panel');
        const button = document.getElementById('evolve-btn');

        let animationFrameId;
        let particles = [];
        let hue = 0;

        function resizeCanvas() {
            canvas.width = panel.clientWidth * 0.9;
            canvas.height = panel.clientHeight * 0.8;
        }

        window.addEventListener('resize', () => {
            resizeCanvas();
            // If animation is running, restart it to adapt to new size
            if (animationFrameId) {
                startAnimation();
            }
        });

        class Particle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.size = 1;
                this.maxSize = Math.random() * 20 + 5;
                this.speedX = Math.random() * 4 - 2;
                this.speedY = Math.random() * 4 - 2;
                this.color = `hsl(${hue}, 100%, 50%)`;
                this.life = 0;
                this.maxLife = 100; // a lifetime for the particle
                this.history = [];
            }
            update() {
                this.x += this.speedX;
                this.y += this.speedY;
                
                // Evolve size
                if (this.size < this.maxSize) {
                    this.size += 0.1;
                }

                // Evolve by creating trails
                this.history.push({x: this.x, y: this.y});
                if(this.history.length > 20) {
                    this.history.shift();
                }

                // Friction
                this.speedX *= 0.98;
                this.speedY *= 0.98;

                this.life++;
            }
            draw() {
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();

                // Draw trail
                for (let i = 0; i < this.history.length; i++) {
                    let pos = this.history[i];
                    ctx.beginPath();
                    ctx.arc(pos.x, pos.y, i / this.history.length * this.size, 0, Math.PI * 2);
                    ctx.fillStyle = `hsla(${hue}, 100%, 50%, ${i / this.history.length * 0.5})`;
                    ctx.fill();
                }
            }
            isDead() {
                return this.life > this.maxLife;
            }
        }
        
        function handleParticles() {
            for (let i = particles.length - 1; i >= 0; i--) {
                particles[i].update();
                particles[i].draw();

                // Evolve by splitting
                if (Math.random() > 0.99 && particles.length < 200) {
                    const newParticle = new Particle(particles[i].x, particles[i].y);
                    newParticle.size = particles[i].size / 2;
                    particles[i].size /= 2;
                    newParticle.speedX = -particles[i].speedX;
                    newParticle.speedY = -particles[i].speedY;
                    particles.push(newParticle);
                }
                
                if (particles[i].isDead() || particles[i].x < 0 || particles[i].x > canvas.width || particles[i].y < 0 || particles[i].y > canvas.height) {
                   particles.splice(i, 1);
                }
            }
        }

        function animate() {
            ctx.fillStyle = 'rgba(0,0,0,0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            if (particles.length < 100) { // Limit total particles
                particles.push(new Particle(canvas.width / 2, canvas.height / 2));
            }
            
            handleParticles();
            
            hue += 0.5;
            animationFrameId = requestAnimationFrame(animate);
        }

        function startAnimation() {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            particles = [];
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            animate();
            button.textContent = '停止演变';
        }

        function stopAnimation() {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
                button.textContent = '重新开始';
                 setTimeout(() => {
                    ctx.fillStyle = 'rgba(0,0,0,0.8)';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    ctx.fillStyle = 'white';
                    ctx.font = '30px Roboto';
                    ctx.textAlign = 'center';
                    ctx.fillText('演变已暂停', canvas.width / 2, canvas.height / 2);
                 }, 100);
            }
        }

        button.addEventListener('click', () => {
            if (animationFrameId) {
                stopAnimation();
            } else {
                startAnimation();
            }
        });

        // Initial setup
        resizeCanvas();
        ctx.fillStyle = 'black';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = 'white';
        ctx.font = '24px Roboto';
        ctx.textAlign = 'center';
        ctx.fillText('点击 "开始演变" 查看动画', canvas.width / 2, canvas.height / 2);

    </script>
</body>
</html> 