<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象分析与设计模型 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transform: translateY(50px);
            opacity: 0;
            animation: slideInUp 1s ease-out 0.6s forwards;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .question-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 30px;
            text-align: center;
        }

        .blank {
            display: inline-block;
            min-width: 120px;
            height: 40px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            margin: 0 5px;
            position: relative;
            background: rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .blank:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: scale(1.05);
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .option {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 25px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .option:hover::before {
            left: 100%;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            background: linear-gradient(45deg, #f0f2f5, #e8ecf0);
        }

        .explanation {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .model-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }

        .model-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .model-card:hover {
            transform: translateY(-10px);
        }

        .model-card h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .model-card ul {
            list-style: none;
            padding-left: 0;
        }

        .model-card li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 20px;
        }

        .model-card li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #667eea;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .correct {
            background: linear-gradient(135deg, #4CAF50, #45a049) !important;
            animation: pulse 0.6s ease-in-out;
        }

        .wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f) !important;
            animation: shake 0.6s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-element {
            position: absolute;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 1s ease;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 面向对象分析与设计模型</h1>
            <p>通过互动游戏学习软件工程核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-title">📝 知识挑战题</div>
            <div class="question-content">
                面向对象的分析模型主要由<span class="blank" id="blank1" data-answer="B">（请作答此空）</span>、用例与用例图、领域概念模型构成；<br>
                设计模型则包含以包图表示的软件体系结构图、以交互图表示的<span class="blank" id="blank2" data-answer="用例实现图">（　）</span>、<br>
                完整精确的类图、针对复杂对象的状态图和描述流程化处理过程的<span class="blank" id="blank3" data-answer="活动图">（　）</span>等。
            </div>

            <div class="options">
                <button class="option" onclick="selectAnswer('A', '业务活动图')">A. 业务活动图</button>
                <button class="option" onclick="selectAnswer('B', '顶层架构图')">B. 顶层架构图</button>
                <button class="option" onclick="selectAnswer('C', '数据流模型')">C. 数据流模型</button>
                <button class="option" onclick="selectAnswer('D', '实体联系图')">D. 实体联系图</button>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="gameCanvas"></canvas>
        </div>

        <div class="model-section">
            <div class="model-card">
                <h4>🔍 分析模型组成</h4>
                <ul>
                    <li>顶层架构图 - 系统整体结构</li>
                    <li>用例与用例图 - 功能需求</li>
                    <li>领域概念模型 - 业务概念</li>
                </ul>
            </div>
            <div class="model-card">
                <h4>🎨 设计模型组成</h4>
                <ul>
                    <li>软件体系结构图 - 包图表示</li>
                    <li>用例实现图 - 交互图表示</li>
                    <li>完整精确的类图</li>
                    <li>状态图 - 复杂对象</li>
                    <li>活动图 - 流程化处理</li>
                </ul>
            </div>
        </div>

        <div class="explanation">
            <h3>💡 知识点详解</h3>
            <p><strong>分析阶段</strong>：主要关注"做什么"的问题，通过顶层架构图了解系统整体，用例图描述功能需求，领域概念模型建立业务理解。</p>
            <p><strong>设计阶段</strong>：主要关注"怎么做"的问题，将分析结果转化为具体的设计方案，包括架构设计、交互设计、类设计等。</p>
            <p><strong>正确答案</strong>：第一个空应该填"顶层架构图"，因为它是分析模型的重要组成部分，用于描述系统的整体架构和主要组件。</p>
        </div>
    </div>

    <script>
        let currentProgress = 0;
        let selectedAnswer = null;
        let gameState = 'waiting';

        // Canvas 动画系统
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画元素
        let particles = [];
        let models = [];

        // 粒子系统
        class Particle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 2;
                this.vy = (Math.random() - 0.5) * 2;
                this.life = 1;
                this.decay = 0.02;
                this.size = Math.random() * 4 + 2;
                this.color = `hsl(${Math.random() * 60 + 200}, 70%, 60%)`;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.size *= 0.99;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 模型组件
        class ModelComponent {
            constructor(x, y, text, type) {
                this.x = x;
                this.y = y;
                this.text = text;
                this.type = type;
                this.scale = 1;
                this.targetScale = 1;
                this.rotation = 0;
                this.alpha = 1;
            }

            update() {
                this.scale += (this.targetScale - this.scale) * 0.1;
                this.rotation += 0.01;
            }

            draw() {
                ctx.save();
                ctx.translate(this.x, this.y);
                ctx.rotate(this.rotation);
                ctx.scale(this.scale, this.scale);
                ctx.globalAlpha = this.alpha;

                // 绘制组件背景
                const gradient = ctx.createLinearGradient(-50, -25, 50, 25);
                if (this.type === 'analysis') {
                    gradient.addColorStop(0, '#667eea');
                    gradient.addColorStop(1, '#764ba2');
                } else {
                    gradient.addColorStop(0, '#4CAF50');
                    gradient.addColorStop(1, '#45a049');
                }

                ctx.fillStyle = gradient;
                ctx.fillRect(-50, -25, 100, 50);

                // 绘制文字
                ctx.fillStyle = 'white';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(this.text, 0, 0);

                ctx.restore();
            }

            hover() {
                this.targetScale = 1.2;
            }

            normal() {
                this.targetScale = 1;
            }
        }

        // 初始化模型组件
        function initModels() {
            models = [
                new ModelComponent(150, 100, '顶层架构图', 'analysis'),
                new ModelComponent(300, 100, '用例图', 'analysis'),
                new ModelComponent(450, 100, '领域概念模型', 'analysis'),
                new ModelComponent(150, 250, '体系结构图', 'design'),
                new ModelComponent(300, 250, '用例实现图', 'design'),
                new ModelComponent(450, 250, '类图', 'design'),
                new ModelComponent(600, 250, '状态图', 'design'),
                new ModelComponent(750, 250, '活动图', 'design')
            ];
        }

        // 动画循环
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            drawGrid();

            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });

            // 更新和绘制模型组件
            models.forEach(model => {
                model.update();
                model.draw();
            });

            // 绘制连接线
            drawConnections();

            requestAnimationFrame(animate);
        }

        function drawGrid() {
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.1)';
            ctx.lineWidth = 1;
            
            for (let x = 0; x < canvas.width; x += 50) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }
            
            for (let y = 0; y < canvas.height; y += 50) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }

        function drawConnections() {
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.3)';
            ctx.lineWidth = 2;
            
            // 分析模型之间的连接
            for (let i = 0; i < 3; i++) {
                for (let j = i + 1; j < 3; j++) {
                    ctx.beginPath();
                    ctx.moveTo(models[i].x, models[i].y);
                    ctx.lineTo(models[j].x, models[j].y);
                    ctx.stroke();
                }
            }
            
            // 设计模型之间的连接
            for (let i = 3; i < models.length; i++) {
                for (let j = i + 1; j < models.length; j++) {
                    ctx.beginPath();
                    ctx.moveTo(models[i].x, models[i].y);
                    ctx.lineTo(models[j].x, models[j].y);
                    ctx.stroke();
                }
            }
        }

        // 鼠标交互
        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            models.forEach(model => {
                const distance = Math.sqrt((mouseX - model.x) ** 2 + (mouseY - model.y) ** 2);
                if (distance < 60) {
                    model.hover();
                    // 创建粒子效果
                    if (Math.random() < 0.3) {
                        particles.push(new Particle(mouseX, mouseY));
                    }
                } else {
                    model.normal();
                }
            });
        });

        // 答案选择功能
        function selectAnswer(option, text) {
            selectedAnswer = option;
            
            // 重置所有选项样式
            document.querySelectorAll('.option').forEach(btn => {
                btn.classList.remove('correct', 'wrong');
            });

            // 设置选中的选项样式
            event.target.classList.add(option === 'B' ? 'correct' : 'wrong');

            // 更新进度
            if (option === 'B') {
                updateProgress(100);
                showSuccess();
                fillBlank('blank1', '顶层架构图');
                setTimeout(() => {
                    fillBlank('blank2', '用例实现图');
                }, 1000);
                setTimeout(() => {
                    fillBlank('blank3', '活动图');
                }, 2000);
            } else {
                showError();
            }
        }

        function fillBlank(blankId, text) {
            const blank = document.getElementById(blankId);
            blank.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
            blank.style.color = 'white';
            blank.style.border = 'none';
            blank.textContent = text;
            blank.style.animation = 'pulse 0.6s ease-in-out';
        }

        function updateProgress(percent) {
            currentProgress = percent;
            document.getElementById('progressFill').style.width = percent + '%';
        }

        function showSuccess() {
            // 创建成功粒子效果
            for (let i = 0; i < 50; i++) {
                particles.push(new Particle(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height
                ));
            }
            
            // 模型组件庆祝动画
            models.forEach((model, index) => {
                setTimeout(() => {
                    model.targetScale = 1.5;
                    setTimeout(() => model.targetScale = 1, 500);
                }, index * 100);
            });
        }

        function showError() {
            // 创建错误提示
            canvas.style.animation = 'shake 0.6s ease-in-out';
            setTimeout(() => {
                canvas.style.animation = '';
            }, 600);
        }

        // 初始化
        initModels();
        animate();
        updateProgress(0);

        // 添加浮动元素
        function createFloatingElements() {
            const symbols = ['📊', '🎯', '⚡', '🔧', '📋', '🎨'];
            symbols.forEach((symbol, index) => {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.textContent = symbol;
                element.style.left = Math.random() * window.innerWidth + 'px';
                element.style.top = Math.random() * window.innerHeight + 'px';
                element.style.fontSize = '2rem';
                element.style.animationDelay = index * 0.5 + 's';
                document.body.appendChild(element);
            });
        }

        createFloatingElements();
    </script>
</body>
</html>
