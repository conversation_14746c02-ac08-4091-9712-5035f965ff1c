<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库优化学习 - 交互式教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: slideUp 0.8s ease-out forwards;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .interactive-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .game-score {
            text-align: center;
            font-size: 1.5rem;
            color: #667eea;
            margin: 20px 0;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 1s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .animated-element {
            animation: pulse 2s infinite;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .section-nav {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 40px 0;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255,255,255,0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: white;
            transform: scale(1.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 数据库优化大师</h1>
            <p>通过有趣的动画和互动游戏，轻松掌握数据库优化的核心概念！</p>
            <div class="progress-bar">
                <div class="progress-fill" id="overallProgress"></div>
            </div>
        </div>

        <div class="section-nav" id="sectionNav">
            <div class="nav-dot active" data-section="0"></div>
            <div class="nav-dot" data-section="1"></div>
            <div class="nav-dot" data-section="2"></div>
            <div class="nav-dot" data-section="3"></div>
            <div class="nav-dot" data-section="4"></div>
        </div>

        <!-- 第一部分：数据范围限制 -->
        <div class="learning-section" id="section0">
            <h2 class="section-title">📊 数据范围限制 - 防止数据库"爆炸"</h2>
            
            <div class="explanation">
                <strong>核心概念：</strong> 就像在图书馆找书一样，如果不告诉管理员具体要找什么类型的书，他就得把整个图书馆翻一遍！数据库也是如此，没有限制条件的查询会让数据库"累死"。
            </div>

            <div class="canvas-container">
                <canvas id="dataRangeCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation">
                <strong>实际应用：</strong> 当用户查询订单历史时，我们限制在一个月内，而不是查询所有历史订单。这样既保护了数据库，又提升了用户体验。
            </div>

            <button class="interactive-button" onclick="startDataRangeDemo()">🎮 开始互动演示</button>
            <button class="interactive-button" onclick="playDataRangeGame()">🎯 挑战小游戏</button>
            
            <div class="game-score" id="dataRangeScore">得分: 0</div>
        </div>

        <!-- 第二部分：读写分离 -->
        <div class="learning-section" id="section1" style="display: none;">
            <h2 class="section-title">⚖️ 读写分离 - 数据库的"分工合作"</h2>

            <div class="explanation">
                <strong>核心概念：</strong> 想象一个餐厅，如果只有一个厨师既要做菜又要上菜，效率会很低。读写分离就是让"主厨师"专门做菜（写数据），"服务员"专门上菜（读数据）。
            </div>

            <div class="canvas-container">
                <canvas id="readWriteCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation">
                <strong>工作原理：</strong> 主库（Master）负责所有的写操作，从库（Slave）负责读操作。数据从主库同步到从库，实现负载均衡。
            </div>

            <button class="interactive-button" onclick="startReadWriteDemo()">🎮 观看分离演示</button>
            <button class="interactive-button" onclick="playReadWriteGame()">🎯 模拟餐厅游戏</button>

            <div class="game-score" id="readWriteScore">效率指数: 0%</div>
        </div>

        <!-- 第三部分：缓存优化 -->
        <div class="learning-section" id="section2" style="display: none;">
            <h2 class="section-title">🚀 缓存优化 - 数据库的"记忆助手"</h2>

            <div class="explanation">
                <strong>核心概念：</strong> 就像你把常用的东西放在桌子上，而不是每次都去柜子里找一样。缓存把经常访问的数据放在"快速通道"里，大大提升访问速度。
            </div>

            <div class="canvas-container">
                <canvas id="cacheCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation">
                <strong>缓存类型：</strong><br>
                • <strong>MySQL缓存：</strong> 数据库自带的查询结果缓存<br>
                • <strong>应用级缓存：</strong> 如Redis、Memcached，适合重量级、更新少的数据
            </div>

            <button class="interactive-button" onclick="startCacheDemo()">🎮 缓存魔法演示</button>
            <button class="interactive-button" onclick="playCacheGame()">🎯 缓存管理游戏</button>

            <div class="game-score" id="cacheScore">缓存命中率: 0%</div>
        </div>

        <!-- 第四部分：分库分表 -->
        <div class="learning-section" id="section3" style="display: none;">
            <h2 class="section-title">🏗️ 分库分表 - 数据的"搬家计划"</h2>

            <div class="explanation">
                <strong>核心概念：</strong> 当一个房子住不下时，我们需要搬到更大的房子，或者分成几个房子住。分库分表就是给数据"搬家"，让它们住得更舒服。
            </div>

            <div class="canvas-container">
                <canvas id="partitionCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation">
                <strong>分表类型：</strong><br>
                • <strong>垂直分表：</strong> 按字段分，就像把衣服和书分开放<br>
                • <strong>水平分表：</strong> 按数据量分，就像把书按A-M和N-Z分开放
            </div>

            <button class="interactive-button" onclick="startPartitionDemo()">🎮 分表搬家演示</button>
            <button class="interactive-button" onclick="playPartitionGame()">🎯 数据搬家游戏</button>

            <div class="game-score" id="partitionScore">分表效率: 0%</div>
        </div>

        <!-- 第五部分：总结 -->
        <div class="learning-section" id="section4" style="display: none;">
            <h2 class="section-title">🎓 恭喜！你已经掌握了数据库优化</h2>

            <div class="explanation">
                <strong>知识回顾：</strong><br>
                1. <strong>数据范围限制</strong> - 防止数据库"累死"<br>
                2. <strong>读写分离</strong> - 让数据库"分工合作"<br>
                3. <strong>缓存优化</strong> - 给数据库配个"记忆助手"<br>
                4. <strong>分库分表</strong> - 给数据找个"更大的家"
            </div>

            <div class="canvas-container">
                <canvas id="summaryCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation">
                <strong>实际应用建议：</strong><br>
                • 小项目：重点关注查询优化和缓存<br>
                • 中型项目：考虑读写分离<br>
                • 大型项目：全面使用分库分表策略
            </div>

            <button class="interactive-button" onclick="showFinalScore()">🏆 查看最终成绩</button>
            <button class="interactive-button" onclick="restartLearning()">🔄 重新学习</button>

            <div class="game-score" id="finalScore">总体掌握度: 计算中...</div>
        </div>

        <div class="tooltip" id="tooltip"></div>
    </div>

    <script>
        // 全局变量
        let currentSection = 0;
        let gameScores = {
            dataRange: 0,
            readWrite: 0,
            cache: 0,
            partition: 0
        };
        let animationFrames = {};

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCanvases();
            setupNavigation();
            startIntroAnimation();
        });

        // 导航设置
        function setupNavigation() {
            const navDots = document.querySelectorAll('.nav-dot');
            navDots.forEach((dot, index) => {
                dot.addEventListener('click', () => showSection(index));
            });
        }

        function showSection(index) {
            // 隐藏所有部分
            document.querySelectorAll('.learning-section').forEach(section => {
                section.style.display = 'none';
            });
            
            // 显示目标部分
            document.getElementById(`section${index}`).style.display = 'block';
            
            // 更新导航
            document.querySelectorAll('.nav-dot').forEach((dot, i) => {
                dot.classList.toggle('active', i === index);
            });
            
            currentSection = index;
            updateProgress();
        }

        function updateProgress() {
            const progress = ((currentSection + 1) / 5) * 100;
            document.getElementById('overallProgress').style.width = progress + '%';
        }

        // 初始化画布
        function initializeCanvases() {
            initDataRangeCanvas();
            initReadWriteCanvas();
            initCacheCanvas();
            initPartitionCanvas();
            initSummaryCanvas();
        }

        // 数据范围限制画布
        function initDataRangeCanvas() {
            const canvas = document.getElementById('dataRangeCanvas');
            const ctx = canvas.getContext('2d');
            
            // 绘制初始状态
            drawDataRangeScene(ctx, 'initial');
        }

        function drawDataRangeScene(ctx, state) {
            ctx.clearRect(0, 0, 800, 400);
            
            // 背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 400);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 400);
            
            if (state === 'initial') {
                // 绘制数据库图标
                drawDatabase(ctx, 150, 200, '数据库', '#667eea');
                
                // 绘制查询示例
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.fillText('❌ 错误查询: SELECT * FROM orders', 300, 100);
                ctx.fillText('✅ 正确查询: SELECT * FROM orders WHERE date >= "2024-06-01"', 300, 140);
                
                // 绘制数据量对比
                drawDataComparison(ctx);
            }
        }

        function drawDatabase(ctx, x, y, label, color) {
            // 数据库圆柱体
            ctx.fillStyle = color;
            ctx.fillRect(x - 40, y - 60, 80, 80);
            
            // 顶部椭圆
            ctx.beginPath();
            ctx.ellipse(x, y - 60, 40, 15, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // 底部椭圆
            ctx.beginPath();
            ctx.ellipse(x, y + 20, 40, 15, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 50);
        }

        function drawDataComparison(ctx) {
            // 无限制查询 - 大量数据
            ctx.fillStyle = '#ff6b6b';
            for (let i = 0; i < 50; i++) {
                const x = 300 + (i % 10) * 15;
                const y = 180 + Math.floor(i / 10) * 15;
                ctx.fillRect(x, y, 10, 10);
            }
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('无限制查询：返回大量数据', 350, 320);
            
            // 有限制查询 - 少量数据
            ctx.fillStyle = '#51cf66';
            for (let i = 0; i < 10; i++) {
                const x = 500 + (i % 5) * 15;
                const y = 200 + Math.floor(i / 5) * 15;
                ctx.fillRect(x, y, 10, 10);
            }
            
            ctx.fillText('限制查询：返回相关数据', 520, 320);
        }

        // 数据范围演示
        function startDataRangeDemo() {
            const canvas = document.getElementById('dataRangeCanvas');
            const ctx = canvas.getContext('2d');
            
            let step = 0;
            const maxSteps = 100;
            
            function animate() {
                ctx.clearRect(0, 0, 800, 400);
                
                // 背景
                const gradient = ctx.createLinearGradient(0, 0, 800, 400);
                gradient.addColorStop(0, '#f8f9fa');
                gradient.addColorStop(1, '#e9ecef');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 800, 400);
                
                // 动画演示查询过程
                drawAnimatedQuery(ctx, step);
                
                step++;
                if (step < maxSteps) {
                    animationFrames.dataRange = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function drawAnimatedQuery(ctx, step) {
            // 数据库
            drawDatabase(ctx, 150, 200, '订单数据库', '#667eea');
            
            // 查询动画
            const progress = step / 100;
            
            if (step < 50) {
                // 显示错误查询
                ctx.fillStyle = '#ff6b6b';
                ctx.font = '16px Arial';
                ctx.fillText('查询所有订单...', 300, 100);
                
                // 数据爆炸效果
                for (let i = 0; i < step; i++) {
                    const angle = (i / 50) * Math.PI * 2;
                    const radius = 50 + i * 2;
                    const x = 150 + Math.cos(angle) * radius;
                    const y = 200 + Math.sin(angle) * radius;
                    
                    ctx.fillStyle = `rgba(255, 107, 107, ${1 - i / 50})`;
                    ctx.fillRect(x - 2, y - 2, 4, 4);
                }
            } else {
                // 显示正确查询
                ctx.fillStyle = '#51cf66';
                ctx.font = '16px Arial';
                ctx.fillText('查询最近一个月订单 ✓', 300, 100);
                
                // 有序数据流
                const dataCount = Math.min(10, step - 50);
                for (let i = 0; i < dataCount; i++) {
                    const x = 250 + i * 30;
                    const y = 200;
                    
                    ctx.fillStyle = '#51cf66';
                    ctx.fillRect(x, y, 20, 20);
                    
                    ctx.fillStyle = '#333';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('订单', x + 10, y + 13);
                }
            }
        }

        // 数据范围游戏
        function playDataRangeGame() {
            let score = 0;
            const questions = [
                {
                    question: "用户要查看订单历史，最好的查询方式是？",
                    options: ["查询所有订单", "查询最近30天订单", "查询最近1年订单"],
                    correct: 1,
                    explanation: "限制在30天内既满足用户需求，又保护数据库性能"
                },
                {
                    question: "为什么要限制数据查询范围？",
                    options: ["节省存储空间", "提高查询速度", "增加安全性"],
                    correct: 1,
                    explanation: "限制范围可以大大提高查询速度，减少数据库负担"
                }
            ];
            
            let currentQ = 0;
            
            function showQuestion() {
                if (currentQ >= questions.length) {
                    alert(`游戏结束！总分：${score}/${questions.length}`);
                    document.getElementById('dataRangeScore').textContent = `得分: ${score}`;
                    gameScores.dataRange = score;
                    return;
                }
                
                const q = questions[currentQ];
                const answer = prompt(`${q.question}\n\n${q.options.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}\n\n请输入选项编号 (1-3):`);
                
                if (answer && parseInt(answer) - 1 === q.correct) {
                    score++;
                    alert(`正确！✓\n\n${q.explanation}`);
                } else {
                    alert(`错误！✗\n\n正确答案：${q.options[q.correct]}\n${q.explanation}`);
                }
                
                currentQ++;
                setTimeout(showQuestion, 1000);
            }
            
            showQuestion();
        }

        // 读写分离画布
        function initReadWriteCanvas() {
            const canvas = document.getElementById('readWriteCanvas');
            const ctx = canvas.getContext('2d');
            
            drawReadWriteScene(ctx, 'initial');
        }

        function drawReadWriteScene(ctx, state) {
            ctx.clearRect(0, 0, 800, 400);
            
            // 背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 400);
            gradient.addColorStop(0, '#f0f8ff');
            gradient.addColorStop(1, '#e6f3ff');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 400);
            
            if (state === 'initial') {
                // 主数据库
                drawDatabase(ctx, 200, 150, '主库 (写)', '#ff6b6b');
                
                // 从数据库
                drawDatabase(ctx, 400, 250, '从库1 (读)', '#51cf66');
                drawDatabase(ctx, 600, 250, '从库2 (读)', '#51cf66');
                
                // 连接线
                drawArrow(ctx, 240, 150, 360, 230, '#333');
                drawArrow(ctx, 240, 170, 560, 230, '#333');
                
                // 标签
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据同步', 400, 200);
                
                // 用户请求
                drawUser(ctx, 100, 300, '写请求');
                drawUser(ctx, 500, 350, '读请求');
                
                drawArrow(ctx, 140, 300, 160, 180, '#ff6b6b');
                drawArrow(ctx, 460, 350, 440, 280, '#51cf66');
            }
        }

        function drawUser(ctx, x, y, label) {
            // 用户图标
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(x, y - 20, 15, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillRect(x - 10, y - 5, 20, 25);
            
            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 35);
        }

        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI / 6), toY - 10 * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI / 6), toY - 10 * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }

        // 读写分离演示
        function startReadWriteDemo() {
            const canvas = document.getElementById('readWriteCanvas');
            const ctx = canvas.getContext('2d');
            
            let step = 0;
            const maxSteps = 200;
            
            function animate() {
                ctx.clearRect(0, 0, 800, 400);
                
                // 背景
                const gradient = ctx.createLinearGradient(0, 0, 800, 400);
                gradient.addColorStop(0, '#f0f8ff');
                gradient.addColorStop(1, '#e6f3ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 800, 400);
                
                drawAnimatedReadWrite(ctx, step);
                
                step++;
                if (step < maxSteps) {
                    animationFrames.readWrite = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function drawAnimatedReadWrite(ctx, step) {
            // 静态元素
            drawDatabase(ctx, 200, 150, '主库', '#ff6b6b');
            drawDatabase(ctx, 400, 250, '从库1', '#51cf66');
            drawDatabase(ctx, 600, 250, '从库2', '#51cf66');
            
            // 动画效果
            const cycle = step % 100;
            
            if (cycle < 30) {
                // 写操作动画
                drawUser(ctx, 100, 300, '写数据');
                
                // 数据流动到主库
                const progress = cycle / 30;
                const x = 100 + (100 * progress);
                const y = 300 - (120 * progress);
                
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(x, y, 5, 0, 2 * Math.PI);
                ctx.fill();
                
            } else if (cycle < 60) {
                // 数据同步动画
                const progress = (cycle - 30) / 30;
                
                // 同步到从库1
                const x1 = 200 + (200 * progress);
                const y1 = 150 + (100 * progress);
                
                ctx.fillStyle = '#ffd43b';
                ctx.beginPath();
                ctx.arc(x1, y1, 4, 0, 2 * Math.PI);
                ctx.fill();
                
                // 同步到从库2
                const x2 = 200 + (400 * progress);
                const y2 = 150 + (100 * progress);
                
                ctx.beginPath();
                ctx.arc(x2, y2, 4, 0, 2 * Math.PI);
                ctx.fill();
                
            } else {
                // 读操作动画
                drawUser(ctx, 500, 350, '读数据');
                
                const progress = (cycle - 60) / 40;
                const x = 500 - (100 * progress);
                const y = 350 - (100 * progress);
                
                ctx.fillStyle = '#51cf66';
                ctx.beginPath();
                ctx.arc(x, y, 5, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // 性能指标
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText(`处理效率: ${Math.min(100, Math.floor(step / 2))}%`, 50, 50);
        }

        // 读写分离游戏
        function playReadWriteGame() {
            alert('🎮 餐厅模拟游戏开始！\n\n你是餐厅经理，需要合理分配厨师和服务员的工作。');
            
            let efficiency = 0;
            let round = 1;
            const maxRounds = 5;
            
            function playRound() {
                if (round > maxRounds) {
                    const avgEfficiency = Math.floor(efficiency / maxRounds);
                    alert(`游戏结束！\n平均效率: ${avgEfficiency}%\n\n${avgEfficiency > 80 ? '优秀！你是出色的数据库架构师！' : '继续努力，多练习读写分离的概念！'}`);
                    document.getElementById('readWriteScore').textContent = `效率指数: ${avgEfficiency}%`;
                    gameScores.readWrite = avgEfficiency;
                    return;
                }
                
                const scenarios = [
                    {
                        situation: "高峰期：100个读请求，10个写请求",
                        options: ["全部给主厨师处理", "读请求给服务员，写请求给主厨师", "随机分配"],
                        correct: 1,
                        efficiency: [20, 95, 40]
                    },
                    {
                        situation: "数据更新：需要修改菜单信息",
                        options: ["在从库修改", "在主库修改然后同步", "同时在所有库修改"],
                        correct: 1,
                        efficiency: [10, 90, 30]
                    }
                ];
                
                const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
                const choice = prompt(`第${round}轮\n\n情况：${scenario.situation}\n\n${scenario.options.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}\n\n请选择 (1-3):`);
                
                if (choice && parseInt(choice) >= 1 && parseInt(choice) <= 3) {
                    const choiceIndex = parseInt(choice) - 1;
                    const roundEfficiency = scenario.efficiency[choiceIndex];
                    efficiency += roundEfficiency;
                    
                    if (choiceIndex === scenario.correct) {
                        alert(`✅ 正确！效率: ${roundEfficiency}%`);
                    } else {
                        alert(`❌ 不是最佳选择。效率: ${roundEfficiency}%\n最佳答案是: ${scenario.options[scenario.correct]}`);
                    }
                } else {
                    alert('无效选择，本轮效率为0%');
                }
                
                round++;
                setTimeout(playRound, 1500);
            }
            
            playRound();
        }

        // 缓存画布初始化
        function initCacheCanvas() {
            const canvas = document.getElementById('cacheCanvas');
            const ctx = canvas.getContext('2d');

            drawCacheScene(ctx, 'initial');
        }

        function drawCacheScene(ctx, state) {
            ctx.clearRect(0, 0, 800, 400);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 400);
            gradient.addColorStop(0, '#fff5f5');
            gradient.addColorStop(1, '#ffe0e0');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 400);

            if (state === 'initial') {
                // 用户
                drawUser(ctx, 100, 200, '用户请求');

                // 缓存层
                drawCache(ctx, 300, 150, '缓存', '#ffd43b');

                // 数据库
                drawDatabase(ctx, 600, 250, '数据库', '#667eea');

                // 箭头和标签
                drawArrow(ctx, 140, 200, 260, 170, '#51cf66');
                drawArrow(ctx, 340, 170, 560, 230, '#ff6b6b');

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText('快速响应', 200, 160);
                ctx.fillText('慢速查询', 450, 190);

                // 性能对比
                ctx.fillText('缓存命中: 1ms', 50, 350);
                ctx.fillText('数据库查询: 100ms', 450, 350);
            }
        }

        function drawCache(ctx, x, y, label, color) {
            // 缓存图标 - 闪电形状
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(x - 20, y - 30);
            ctx.lineTo(x + 10, y - 10);
            ctx.lineTo(x - 5, y - 10);
            ctx.lineTo(x + 20, y + 30);
            ctx.lineTo(x - 10, y + 10);
            ctx.lineTo(x + 5, y + 10);
            ctx.closePath();
            ctx.fill();

            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 50);
        }

        // 缓存演示
        function startCacheDemo() {
            const canvas = document.getElementById('cacheCanvas');
            const ctx = canvas.getContext('2d');

            let step = 0;
            const maxSteps = 150;

            function animate() {
                ctx.clearRect(0, 0, 800, 400);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, 800, 400);
                gradient.addColorStop(0, '#fff5f5');
                gradient.addColorStop(1, '#ffe0e0');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 800, 400);

                drawAnimatedCache(ctx, step);

                step++;
                if (step < maxSteps) {
                    animationFrames.cache = requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function drawAnimatedCache(ctx, step) {
            // 静态元素
            drawUser(ctx, 100, 200, '用户');
            drawCache(ctx, 300, 150, '缓存', '#ffd43b');
            drawDatabase(ctx, 600, 250, '数据库', '#667eea');

            const cycle = step % 50;

            if (cycle < 20) {
                // 第一次请求 - 缓存未命中
                const progress = cycle / 20;

                // 请求到缓存
                let x = 100 + (200 * Math.min(progress * 2, 1));
                let y = 200 - (50 * Math.min(progress * 2, 1));

                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(x, y, 5, 0, 2 * Math.PI);
                ctx.fill();

                if (progress > 0.5) {
                    // 缓存未命中，转向数据库
                    x = 300 + (300 * (progress - 0.5) * 2);
                    y = 150 + (100 * (progress - 0.5) * 2);

                    ctx.fillStyle = '#ff9999';
                    ctx.beginPath();
                    ctx.arc(x, y, 4, 0, 2 * Math.PI);
                    ctx.fill();
                }

                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText('缓存未命中 - 查询数据库', 200, 50);

            } else if (cycle < 30) {
                // 数据返回并缓存
                const progress = (cycle - 20) / 10;
                const x = 600 - (300 * progress);
                const y = 250 - (100 * progress);

                ctx.fillStyle = '#51cf66';
                ctx.beginPath();
                ctx.arc(x, y, 5, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText('数据存入缓存', 200, 50);

            } else {
                // 第二次请求 - 缓存命中
                const progress = (cycle - 30) / 20;
                const x = 100 + (200 * progress);
                const y = 200 - (50 * progress);

                ctx.fillStyle = '#51cf66';
                ctx.beginPath();
                ctx.arc(x, y, 6, 0, 2 * Math.PI);
                ctx.fill();

                // 闪光效果
                ctx.fillStyle = `rgba(255, 212, 59, ${0.5 + 0.5 * Math.sin(step * 0.3)})`;
                ctx.beginPath();
                ctx.arc(300, 150, 30, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText('缓存命中 - 快速响应！', 200, 50);
            }

            // 性能统计
            const hitRate = Math.min(100, Math.floor(step / 1.5));
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText(`缓存命中率: ${hitRate}%`, 50, 350);
        }

        // 缓存游戏
        function playCacheGame() {
            alert('🎮 缓存管理游戏开始！\n\n你是缓存管理员，需要决定哪些数据应该缓存。');

            let hitRate = 0;
            let round = 1;
            const maxRounds = 5;

            function playRound() {
                if (round > maxRounds) {
                    const avgHitRate = Math.floor(hitRate / maxRounds);
                    alert(`游戏结束！\n平均命中率: ${avgHitRate}%\n\n${avgHitRate > 70 ? '优秀！你是缓存优化专家！' : '继续学习缓存策略！'}`);
                    document.getElementById('cacheScore').textContent = `缓存命中率: ${avgHitRate}%`;
                    gameScores.cache = avgHitRate;
                    return;
                }

                const scenarios = [
                    {
                        situation: "用户个人资料信息（很少修改）",
                        options: ["不缓存", "缓存1小时", "缓存24小时"],
                        correct: 2,
                        hitRate: [0, 70, 95]
                    },
                    {
                        situation: "实时股票价格（频繁变化）",
                        options: ["缓存1分钟", "缓存1小时", "不缓存"],
                        correct: 2,
                        hitRate: [30, 10, 85]
                    },
                    {
                        situation: "商品分类列表（偶尔更新）",
                        options: ["缓存30分钟", "不缓存", "缓存7天"],
                        correct: 0,
                        hitRate: [90, 0, 95]
                    }
                ];

                const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
                const choice = prompt(`第${round}轮 - 缓存策略选择\n\n数据类型：${scenario.situation}\n\n${scenario.options.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}\n\n请选择最佳缓存策略 (1-3):`);

                if (choice && parseInt(choice) >= 1 && parseInt(choice) <= 3) {
                    const choiceIndex = parseInt(choice) - 1;
                    const roundHitRate = scenario.hitRate[choiceIndex];
                    hitRate += roundHitRate;

                    if (choiceIndex === scenario.correct) {
                        alert(`✅ 最佳选择！命中率: ${roundHitRate}%`);
                    } else {
                        alert(`⚠️ 可以优化。命中率: ${roundHitRate}%\n最佳策略: ${scenario.options[scenario.correct]}`);
                    }
                } else {
                    alert('无效选择，本轮命中率为0%');
                }

                round++;
                setTimeout(playRound, 1500);
            }

            playRound();
        }

        // 分库分表画布初始化
        function initPartitionCanvas() {
            const canvas = document.getElementById('partitionCanvas');
            const ctx = canvas.getContext('2d');

            drawPartitionScene(ctx, 'initial');
        }

        function drawPartitionScene(ctx, state) {
            ctx.clearRect(0, 0, 800, 400);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 400);
            gradient.addColorStop(0, '#f0fff0');
            gradient.addColorStop(1, '#e0ffe0');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 400);

            if (state === 'initial') {
                // 原始大表
                drawTable(ctx, 150, 100, '用户表', '#ff6b6b', true);

                // 箭头
                drawArrow(ctx, 250, 150, 350, 150, '#333');

                // 分表后
                drawTable(ctx, 450, 80, '用户表1', '#51cf66', false);
                drawTable(ctx, 450, 160, '用户表2', '#51cf66', false);
                drawTable(ctx, 450, 240, '用户表3', '#51cf66', false);

                // 标签
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('水平分表', 300, 130);
                ctx.fillText('(按用户ID范围)', 300, 145);

                // 垂直分表示例
                drawTable(ctx, 150, 280, '订单表', '#ffd43b', true);
                drawArrow(ctx, 250, 320, 350, 320, '#333');

                drawTable(ctx, 450, 300, '订单基础', '#74c0fc', false);
                drawTable(ctx, 600, 300, '订单详情', '#74c0fc', false);

                ctx.fillText('垂直分表', 300, 300);
                ctx.fillText('(按字段分离)', 300, 315);
            }
        }

        function drawTable(ctx, x, y, label, color, isBig) {
            const width = isBig ? 80 : 60;
            const height = isBig ? 60 : 40;

            // 表格
            ctx.fillStyle = color;
            ctx.fillRect(x - width/2, y - height/2, width, height);

            // 边框
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - width/2, y - height/2, width, height);

            // 表格线
            if (isBig) {
                for (let i = 1; i < 4; i++) {
                    ctx.beginPath();
                    ctx.moveTo(x - width/2, y - height/2 + i * height/4);
                    ctx.lineTo(x + width/2, y - height/2 + i * height/4);
                    ctx.stroke();
                }
            } else {
                ctx.beginPath();
                ctx.moveTo(x - width/2, y - height/2 + height/3);
                ctx.lineTo(x + width/2, y - height/2 + height/3);
                ctx.stroke();
            }

            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + height/2 + 15);
        }

        // 分库分表演示
        function startPartitionDemo() {
            const canvas = document.getElementById('partitionCanvas');
            const ctx = canvas.getContext('2d');

            let step = 0;
            const maxSteps = 200;

            function animate() {
                ctx.clearRect(0, 0, 800, 400);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, 800, 400);
                gradient.addColorStop(0, '#f0fff0');
                gradient.addColorStop(1, '#e0ffe0');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, 800, 400);

                drawAnimatedPartition(ctx, step);

                step++;
                if (step < maxSteps) {
                    animationFrames.partition = requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function drawAnimatedPartition(ctx, step) {
            const phase = Math.floor(step / 50);

            if (phase === 0) {
                // 显示数据增长问题
                drawTable(ctx, 400, 200, '用户表', '#ff6b6b', true);

                // 数据增长动画
                const dataCount = Math.min(20, step);
                for (let i = 0; i < dataCount; i++) {
                    const angle = (i / 20) * Math.PI * 2;
                    const radius = 50 + (step % 50);
                    const x = 400 + Math.cos(angle) * radius;
                    const y = 200 + Math.sin(angle) * radius;

                    ctx.fillStyle = `rgba(255, 107, 107, ${1 - (step % 50) / 50})`;
                    ctx.fillRect(x - 2, y - 2, 4, 4);
                }

                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据量暴增！性能下降...', 400, 100);

            } else if (phase === 1) {
                // 分表过程
                const progress = (step - 50) / 50;

                // 原表逐渐缩小
                const scale = 1 - progress * 0.3;
                ctx.save();
                ctx.translate(200, 200);
                ctx.scale(scale, scale);
                drawTable(ctx, 0, 0, '用户表', '#ff6b6b', true);
                ctx.restore();

                // 新表逐渐出现
                if (progress > 0.3) {
                    const alpha = (progress - 0.3) / 0.7;
                    ctx.globalAlpha = alpha;
                    drawTable(ctx, 500, 120, '用户表1', '#51cf66', false);
                    drawTable(ctx, 500, 200, '用户表2', '#51cf66', false);
                    drawTable(ctx, 500, 280, '用户表3', '#51cf66', false);
                    ctx.globalAlpha = 1;
                }

                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('正在分表...', 400, 50);

            } else {
                // 分表完成，显示性能提升
                drawTable(ctx, 200, 200, '原表', '#cccccc', true);
                drawTable(ctx, 500, 120, '用户表1', '#51cf66', false);
                drawTable(ctx, 500, 200, '用户表2', '#51cf66', false);
                drawTable(ctx, 500, 280, '用户表3', '#51cf66', false);

                // 性能指标动画
                const performance = Math.min(100, (step - 100) * 2);
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`查询性能提升: ${performance}%`, 400, 50);

                // 数据流动
                if (step % 20 < 10) {
                    ctx.fillStyle = '#51cf66';
                    ctx.beginPath();
                    ctx.arc(450, 120, 5, 0, 2 * Math.PI);
                    ctx.fill();
                }
            }
        }

        // 分库分表游戏
        function playPartitionGame() {
            alert('🎮 数据搬家游戏开始！\n\n你是数据架构师，需要为不同场景选择最佳的分表策略。');

            let efficiency = 0;
            let round = 1;
            const maxRounds = 4;

            function playRound() {
                if (round > maxRounds) {
                    const avgEfficiency = Math.floor(efficiency / maxRounds);
                    alert(`游戏结束！\n平均效率: ${avgEfficiency}%\n\n${avgEfficiency > 75 ? '优秀！你是分表专家！' : '继续学习分表策略！'}`);
                    document.getElementById('partitionScore').textContent = `分表效率: ${avgEfficiency}%`;
                    gameScores.partition = avgEfficiency;
                    return;
                }

                const scenarios = [
                    {
                        situation: "用户表有1000万条记录，查询慢",
                        options: ["垂直分表", "水平分表", "不分表"],
                        correct: 1,
                        efficiency: [30, 90, 10]
                    },
                    {
                        situation: "订单表字段太多，有些字段很少用",
                        options: ["水平分表", "垂直分表", "加索引"],
                        correct: 1,
                        efficiency: [40, 85, 60]
                    },
                    {
                        situation: "日志表按时间查询，数据量巨大",
                        options: ["按时间水平分表", "垂直分表", "删除旧数据"],
                        correct: 0,
                        efficiency: [95, 20, 50]
                    }
                ];

                const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
                const choice = prompt(`第${round}轮 - 分表策略\n\n场景：${scenario.situation}\n\n${scenario.options.map((opt, i) => `${i + 1}. ${opt}`).join('\n')}\n\n请选择最佳方案 (1-3):`);

                if (choice && parseInt(choice) >= 1 && parseInt(choice) <= 3) {
                    const choiceIndex = parseInt(choice) - 1;
                    const roundEfficiency = scenario.efficiency[choiceIndex];
                    efficiency += roundEfficiency;

                    if (choiceIndex === scenario.correct) {
                        alert(`✅ 完美方案！效率提升: ${roundEfficiency}%`);
                    } else {
                        alert(`⚠️ 可以更好。效率提升: ${roundEfficiency}%\n最佳方案: ${scenario.options[scenario.correct]}`);
                    }
                } else {
                    alert('无效选择，本轮效率为0%');
                }

                round++;
                setTimeout(playRound, 1500);
            }

            playRound();
        }

        // 总结画布初始化
        function initSummaryCanvas() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');

            drawSummaryScene(ctx);
        }

        function drawSummaryScene(ctx) {
            ctx.clearRect(0, 0, 800, 400);

            // 彩虹背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 400);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(0.25, '#764ba2');
            gradient.addColorStop(0.5, '#f093fb');
            gradient.addColorStop(0.75, '#f5576c');
            gradient.addColorStop(1, '#4facfe');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 400);

            // 半透明覆盖
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(0, 0, 800, 400);

            // 四个优化技术图标
            const techniques = [
                { x: 150, y: 150, label: '数据范围\n限制', icon: '🎯' },
                { x: 350, y: 150, label: '读写\n分离', icon: '⚖️' },
                { x: 550, y: 150, label: '缓存\n优化', icon: '🚀' },
                { x: 650, y: 150, label: '分库\n分表', icon: '🏗️' }
            ];

            techniques.forEach(tech => {
                // 图标背景圆
                ctx.fillStyle = 'rgba(102, 126, 234, 0.1)';
                ctx.beginPath();
                ctx.arc(tech.x, tech.y, 40, 0, 2 * Math.PI);
                ctx.fill();

                // 图标
                ctx.font = '30px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(tech.icon, tech.x, tech.y + 10);

                // 标签
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                const lines = tech.label.split('\n');
                lines.forEach((line, i) => {
                    ctx.fillText(line, tech.x, tech.y + 60 + i * 15);
                });
            });

            // 连接线
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(190, 150);
            ctx.lineTo(310, 150);
            ctx.moveTo(390, 150);
            ctx.lineTo(510, 150);
            ctx.moveTo(590, 150);
            ctx.lineTo(610, 150);
            ctx.stroke();
            ctx.setLineDash([]);

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🎓 数据库优化完整体系', 400, 50);

            // 底部提示
            ctx.font = '16px Arial';
            ctx.fillText('恭喜！你已经掌握了数据库优化的核心技术！', 400, 350);
        }

        // 显示最终成绩
        function showFinalScore() {
            const totalScore = Object.values(gameScores).reduce((sum, score) => sum + score, 0);
            const avgScore = Math.floor(totalScore / 4);

            let level = '';
            if (avgScore >= 90) level = '🏆 数据库优化大师';
            else if (avgScore >= 75) level = '🥇 高级数据库工程师';
            else if (avgScore >= 60) level = '🥈 中级数据库开发者';
            else level = '🥉 数据库学习者';

            alert(`🎉 学习完成！\n\n最终成绩单：\n• 数据范围限制: ${gameScores.dataRange}分\n• 读写分离: ${gameScores.readWrite}分\n• 缓存优化: ${gameScores.cache}分\n• 分库分表: ${gameScores.partition}分\n\n平均分: ${avgScore}分\n等级: ${level}`);

            document.getElementById('finalScore').textContent = `总体掌握度: ${avgScore}分 - ${level}`;
        }

        // 重新开始学习
        function restartLearning() {
            gameScores = { dataRange: 0, readWrite: 0, cache: 0, partition: 0 };
            showSection(0);

            // 重置所有分数显示
            document.getElementById('dataRangeScore').textContent = '得分: 0';
            document.getElementById('readWriteScore').textContent = '效率指数: 0%';
            document.getElementById('cacheScore').textContent = '缓存命中率: 0%';
            document.getElementById('partitionScore').textContent = '分表效率: 0%';
            document.getElementById('finalScore').textContent = '总体掌握度: 计算中...';

            alert('🔄 重新开始学习之旅！加油！');
        }

        // 开场动画
        function startIntroAnimation() {
            const sections = document.querySelectorAll('.learning-section');
            sections.forEach((section, index) => {
                section.style.animationDelay = `${index * 0.2}s`;
            });
        }

        // 工具提示
        function showTooltip(event, text) {
            const tooltip = document.getElementById('tooltip');
            tooltip.textContent = text;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 30 + 'px';
            tooltip.style.opacity = '1';
        }

        function hideTooltip() {
            document.getElementById('tooltip').style.opacity = '0';
        }

        // 添加鼠标悬停效果
        document.querySelectorAll('canvas').forEach(canvas => {
            canvas.addEventListener('mouseenter', (e) => showTooltip(e, '点击开始互动演示'));
            canvas.addEventListener('mouseleave', hideTooltip);
        });

        // 清理动画帧
        window.addEventListener('beforeunload', () => {
            Object.values(animationFrames).forEach(frame => {
                if (frame) cancelAnimationFrame(frame);
            });
        });
    </script>
</body>
</html>
