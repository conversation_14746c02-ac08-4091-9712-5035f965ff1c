<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库视图 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .interactive-demo {
            background: linear-gradient(135deg, #667eea20, #764ba220);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .game-area {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
            border: 2px dashed #667eea;
        }

        .score {
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .floating-element {
            position: absolute;
            pointer-events: none;
            font-size: 2rem;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 5px 10px;
            border-radius: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 数据库视图学习之旅</h1>
            <p>通过动画和交互，轻松掌握数据库视图的概念与应用</p>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是数据库视图？</h2>
            <div class="canvas-container">
                <canvas id="viewIntroCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">数据库视图</span>是一个虚拟表，它不存储实际数据，而是基于一个或多个基本表的查询结果。想象一下，视图就像是一个<span class="highlight">智能窗口</span>，透过它你可以看到经过筛选和整理的数据。</p>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animateViewConcept()">🎬 观看视图概念动画</button>
                <button class="demo-button" onclick="showViewExample()">📊 查看实例演示</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚡ 视图的性能问题</h2>
            <div class="canvas-container">
                <canvas id="performanceCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">性能影响</span>：数据库必须把视图的查询转化成对基本表的查询。如果这个视图是由一个复杂的多表查询所定义，那么，即使是视图的一个简单查询，数据库也把它变成一个复杂的结合体，需要花费一定的时间。</p>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animatePerformanceIssue()">🐌 演示性能问题</button>
                <button class="demo-button" onclick="showQueryTransformation()">🔄 查询转换过程</button>
            </div>
            <div class="game-area">
                <div class="score">性能挑战游戏 - 得分: <span id="performanceScore">0</span></div>
                <p>点击下方按钮，选择最优的查询方式！</p>
                <button class="demo-button" onclick="performanceGame('simple')">简单查询</button>
                <button class="demo-button" onclick="performanceGame('complex')">复杂视图查询</button>
                <button class="demo-button" onclick="performanceGame('direct')">直接表查询</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🚫 视图的修改限制</h2>
            <div class="canvas-container">
                <canvas id="restrictionCanvas" width="800" height="500"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">修改限制</span>：当用户试图修改视图的某些行时，数据库必须把它转化为对基本表的某些行的修改。对于复杂视图，可能是不可修改的。</p>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="showModificationRestrictions()">📋 查看限制类型</button>
                <button class="demo-button" onclick="animateRestrictionDemo()">🎭 交互演示</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 知识检测游戏</h2>
            <div class="game-area">
                <div class="score">总得分: <span id="totalScore">0</span></div>
                <canvas id="gameCanvas" width="800" height="400"></canvas>
                <div style="margin-top: 20px;">
                    <button class="demo-button" onclick="startQuizGame()">🎯 开始知识问答</button>
                    <button class="demo-button" onclick="resetGame()">🔄 重新开始</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationId;
        let score = 0;
        let performanceScore = 0;
        let totalScore = 0;
        let currentQuestion = 0;
        let gameQuestions = [
            {
                question: "视图是什么？",
                options: ["实际存储数据的表", "虚拟表，不存储数据", "索引文件", "备份表"],
                correct: 1,
                explanation: "视图是虚拟表，它不存储实际数据，而是基于查询结果动态生成。"
            },
            {
                question: "复杂视图查询会导致什么问题？",
                options: ["数据丢失", "性能下降", "数据重复", "权限错误"],
                correct: 1,
                explanation: "复杂视图查询需要数据库进行复杂的查询转换，会影响性能。"
            },
            {
                question: "以下哪种视图通常不可修改？",
                options: ["简单单表视图", "有GROUP BY的视图", "只读视图", "临时视图"],
                correct: 1,
                explanation: "包含GROUP BY子句的视图通常不可修改，因为无法确定修改应该影响哪些原始行。"
            }
        ];

        // 初始化画布
        function initCanvas() {
            const canvas = document.getElementById('viewIntroCanvas');
            const ctx = canvas.getContext('2d');

            // 绘制初始状态
            drawViewIntro(ctx, canvas.width, canvas.height);

            // 初始化其他画布
            initPerformanceCanvas();
            initRestrictionCanvas();
            initGameCanvas();
        }

        // 初始化性能画布
        function initPerformanceCanvas() {
            const canvas = document.getElementById('performanceCanvas');
            const ctx = canvas.getContext('2d');
            drawPerformanceIntro(ctx, canvas.width, canvas.height);
        }

        // 初始化限制画布
        function initRestrictionCanvas() {
            const canvas = document.getElementById('restrictionCanvas');
            const ctx = canvas.getContext('2d');
            drawRestrictionIntro(ctx, canvas.width, canvas.height);
        }

        // 初始化游戏画布
        function initGameCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            drawGameIntro(ctx, canvas.width, canvas.height);
        }

        // 绘制视图介绍动画
        function drawViewIntro(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);
            
            // 绘制基本表
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(50, 150, 200, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('基本表', 150, 200);
            ctx.fillText('(实际数据)', 150, 220);
            
            // 绘制视图
            ctx.fillStyle = '#2196F3';
            ctx.fillRect(550, 150, 200, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('视图', 650, 200);
            ctx.fillText('(虚拟表)', 650, 220);
            
            // 绘制箭头
            ctx.strokeStyle = '#FF9800';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(270, 200);
            ctx.lineTo(530, 200);
            ctx.stroke();
            
            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(520, 190);
            ctx.lineTo(530, 200);
            ctx.lineTo(520, 210);
            ctx.stroke();
            
            // 添加说明文字
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('查询转换', 400, 180);
        }

        // 视图概念动画
        function animateViewConcept() {
            const canvas = document.getElementById('viewIntroCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#f8f9fa');
                gradient.addColorStop(1, '#e9ecef');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 动画效果
                const pulse = Math.sin(frame * 0.1) * 0.1 + 1;
                
                // 基本表（带脉冲效果）
                ctx.save();
                ctx.translate(150, 200);
                ctx.scale(pulse, pulse);
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(-100, -50, 200, 100);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('基本表', 0, -10);
                ctx.fillText('(实际数据)', 0, 10);
                ctx.restore();
                
                // 数据流动效果
                const flowOffset = (frame * 2) % 280;
                for (let i = 0; i < 5; i++) {
                    const x = 270 + flowOffset + i * 56;
                    if (x < 530) {
                        ctx.fillStyle = `hsl(${(frame + i * 20) % 360}, 70%, 60%)`;
                        ctx.beginPath();
                        ctx.arc(x, 200, 5, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }
                
                // 视图（带脉冲效果）
                ctx.save();
                ctx.translate(650, 200);
                ctx.scale(pulse, pulse);
                ctx.fillStyle = '#2196F3';
                ctx.fillRect(-100, -50, 200, 100);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('视图', 0, -10);
                ctx.fillText('(虚拟表)', 0, 10);
                ctx.restore();
                
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        // 显示视图示例
        function showViewExample() {
            const canvas = document.getElementById('viewIntroCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景
            ctx.fillStyle = '#f0f8ff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 示例表格
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('学生表 → 优秀学生视图', canvas.width/2, 50);
            
            // 原始表
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(50, 100, 300, 200);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('学生表', 200, 130);
            ctx.fillText('姓名 | 年龄 | 成绩', 200, 160);
            ctx.fillText('张三 | 20 | 95', 200, 180);
            ctx.fillText('李四 | 21 | 78', 200, 200);
            ctx.fillText('王五 | 19 | 92', 200, 220);
            ctx.fillText('赵六 | 22 | 65', 200, 240);
            
            // 箭头
            ctx.strokeStyle = '#FF9800';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(370, 200);
            ctx.lineTo(430, 200);
            ctx.stroke();
            
            // 视图
            ctx.fillStyle = '#2196F3';
            ctx.fillRect(450, 100, 300, 150);
            ctx.fillStyle = 'white';
            ctx.fillText('优秀学生视图', 600, 130);
            ctx.fillText('(成绩≥90)', 600, 150);
            ctx.fillText('姓名 | 成绩', 600, 180);
            ctx.fillText('张三 | 95', 600, 200);
            ctx.fillText('王五 | 92', 600, 220);
        }

        // 绘制性能介绍
        function drawPerformanceIntro(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#fff3e0');
            gradient.addColorStop(1, '#ffe0b2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);

            // 绘制性能对比图
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(100, 250, 150, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('直接查询', 175, 295);
            ctx.fillText('⚡ 快速', 175, 315);

            ctx.fillStyle = '#FF5722';
            ctx.fillRect(550, 200, 150, 150);
            ctx.fillStyle = 'white';
            ctx.fillText('复杂视图查询', 625, 270);
            ctx.fillText('🐌 较慢', 625, 290);
            ctx.fillText('需要转换', 625, 310);

            // 时间轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(300, 300);
            ctx.lineTo(500, 300);
            ctx.stroke();

            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.fillText('处理时间', 400, 320);
        }

        // 性能问题动画
        function animatePerformanceIssue() {
            const canvas = document.getElementById('performanceCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#fff3e0');
                gradient.addColorStop(1, '#ffe0b2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 查询流程动画
                const progress = (frame % 120) / 120;

                // 用户查询
                ctx.fillStyle = '#2196F3';
                ctx.fillRect(50, 50, 120, 60);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('用户查询', 110, 85);

                // 视图处理（带旋转效果）
                ctx.save();
                ctx.translate(300, 80);
                ctx.rotate(frame * 0.1);
                ctx.fillStyle = '#FF9800';
                ctx.fillRect(-60, -30, 120, 60);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('视图处理', 0, 5);
                ctx.restore();

                // 基本表查询
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(550, 50, 120, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('基本表查询', 610, 85);

                // 处理时间指示器
                const barHeight = Math.sin(frame * 0.2) * 50 + 100;
                ctx.fillStyle = `hsl(${120 - barHeight}, 70%, 50%)`;
                ctx.fillRect(350, 350 - barHeight, 100, barHeight);
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText('处理时间', 400, 370);

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 查询转换过程演示
        function showQueryTransformation() {
            const canvas = document.getElementById('performanceCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            ctx.fillStyle = '#f5f5f5';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('查询转换过程演示', canvas.width/2, 30);

            // 原始查询
            ctx.fillStyle = '#E3F2FD';
            ctx.fillRect(50, 60, 300, 80);
            ctx.fillStyle = '#1976D2';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('SELECT * FROM 学生视图', 70, 90);
            ctx.fillText('WHERE 成绩 > 90', 70, 110);

            // 箭头
            ctx.strokeStyle = '#FF5722';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(370, 100);
            ctx.lineTo(430, 100);
            ctx.stroke();

            // 转换后查询
            ctx.fillStyle = '#FFF3E0';
            ctx.fillRect(450, 60, 300, 120);
            ctx.fillStyle = '#F57C00';
            ctx.fillText('SELECT 学生.姓名, 学生.成绩', 470, 90);
            ctx.fillText('FROM 学生表 学生', 470, 110);
            ctx.fillText('JOIN 课程表 课程 ON ...', 470, 130);
            ctx.fillText('WHERE 学生.成绩 > 90', 470, 150);

            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('简单查询', 200, 160);
            ctx.fillText('复杂转换查询', 600, 200);
        }

        // 性能游戏
        function performanceGame(choice) {
            let points = 0;
            let message = '';

            switch(choice) {
                case 'simple':
                    points = 5;
                    message = '正确！简单查询性能最好！';
                    break;
                case 'complex':
                    points = -2;
                    message = '复杂视图查询性能较差！';
                    break;
                case 'direct':
                    points = 10;
                    message = '优秀！直接表查询是最优选择！';
                    break;
            }

            performanceScore += points;
            totalScore += points;

            document.getElementById('performanceScore').textContent = performanceScore;
            document.getElementById('totalScore').textContent = totalScore;

            // 显示反馈动画
            showFeedback(message, points > 0);
        }

        // 绘制限制介绍
        function drawRestrictionIntro(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#ffebee');
            gradient.addColorStop(1, '#ffcdd2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('视图修改限制类型', width/2, 30);

            // 限制类型列表
            const restrictions = [
                '1. 有UNIQUE等集合操作符的视图',
                '2. 有GROUP BY子句的视图',
                '3. 有聚合函数(AVG/SUM/MAX)的视图',
                '4. 使用DISTINCT关键字的视图',
                '5. 连接表的视图(部分例外)'
            ];

            ctx.fillStyle = '#d32f2f';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';

            restrictions.forEach((restriction, index) => {
                const y = 80 + index * 40;

                // 绘制禁止图标
                ctx.fillStyle = '#f44336';
                ctx.beginPath();
                ctx.arc(80, y, 15, 0, Math.PI * 2);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('✗', 80, y + 7);

                // 绘制文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(restriction, 120, y + 5);
            });
        }

        // 显示修改限制
        function showModificationRestrictions() {
            const canvas = document.getElementById('restrictionCanvas');
            const ctx = canvas.getContext('2d');

            drawRestrictionIntro(ctx, canvas.width, canvas.height);

            // 添加动画效果
            let frame = 0;
            function animate() {
                // 添加闪烁效果
                const alpha = Math.sin(frame * 0.1) * 0.3 + 0.7;
                ctx.globalAlpha = alpha;

                // 重绘禁止图标
                for (let i = 0; i < 5; i++) {
                    const y = 80 + i * 40;
                    ctx.fillStyle = '#f44336';
                    ctx.beginPath();
                    ctx.arc(80, y, 15, 0, Math.PI * 2);
                    ctx.fill();

                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 20px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('✗', 80, y + 7);
                }

                ctx.globalAlpha = 1;
                frame++;

                if (frame < 100) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 限制演示动画
        function animateRestrictionDemo() {
            const canvas = document.getElementById('restrictionCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                ctx.fillStyle = '#f5f5f5';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 演示场景
                const scene = Math.floor(frame / 60) % 3;

                switch(scene) {
                    case 0:
                        // 简单视图 - 可修改
                        ctx.fillStyle = '#4CAF50';
                        ctx.fillRect(100, 100, 200, 150);
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('简单视图', 200, 130);
                        ctx.fillText('✓ 可修改', 200, 200);

                        ctx.fillStyle = '#333';
                        ctx.font = '14px Arial';
                        ctx.fillText('SELECT * FROM 学生', 200, 160);
                        break;

                    case 1:
                        // GROUP BY视图 - 不可修改
                        ctx.fillStyle = '#f44336';
                        ctx.fillRect(100, 100, 200, 150);
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('GROUP BY视图', 200, 130);
                        ctx.fillText('✗ 不可修改', 200, 200);

                        ctx.fillStyle = '#333';
                        ctx.font = '12px Arial';
                        ctx.fillText('SELECT 班级, COUNT(*)', 200, 160);
                        ctx.fillText('FROM 学生 GROUP BY 班级', 200, 175);
                        break;

                    case 2:
                        // 聚合函数视图 - 不可修改
                        ctx.fillStyle = '#FF9800';
                        ctx.fillRect(100, 100, 200, 150);
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('聚合函数视图', 200, 130);
                        ctx.fillText('✗ 不可修改', 200, 200);

                        ctx.fillStyle = '#333';
                        ctx.font = '12px Arial';
                        ctx.fillText('SELECT AVG(成绩)', 200, 160);
                        ctx.fillText('FROM 学生', 200, 175);
                        break;
                }

                // 进度指示器
                ctx.fillStyle = '#2196F3';
                for (let i = 0; i < 3; i++) {
                    ctx.beginPath();
                    ctx.arc(350 + i * 30, 300, scene === i ? 8 : 5, 0, Math.PI * 2);
                    ctx.fill();
                }

                frame++;
                if (frame < 180) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 绘制游戏介绍
        function drawGameIntro(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            gradient.addColorStop(0, '#e8f5e8');
            gradient.addColorStop(1, '#c8e6c9');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, width, height);

            // 游戏说明
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 数据库视图知识问答', width/2, 50);

            ctx.font = '16px Arial';
            ctx.fillText('测试你对数据库视图的理解程度', width/2, 80);

            // 绘制问号图标
            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(width/2, height/2, 80, 0, Math.PI * 2);
            ctx.fill();

            ctx.fillStyle = 'white';
            ctx.font = 'bold 60px Arial';
            ctx.fillText('?', width/2, height/2 + 20);

            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.fillText('点击"开始知识问答"按钮开始游戏', width/2, height - 50);
        }

        // 开始问答游戏
        function startQuizGame() {
            currentQuestion = 0;
            showQuestion();
        }

        // 显示问题
        function showQuestion() {
            if (currentQuestion >= gameQuestions.length) {
                showGameResult();
                return;
            }

            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            const question = gameQuestions[currentQuestion];

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#e3f2fd');
            gradient.addColorStop(1, '#bbdefb');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 问题标题
            ctx.fillStyle = '#1976d2';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`问题 ${currentQuestion + 1}/${gameQuestions.length}`, canvas.width/2, 40);

            // 问题内容
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText(question.question, canvas.width/2, 80);

            // 选项
            question.options.forEach((option, index) => {
                const y = 120 + index * 50;
                const x = 100;

                // 选项背景
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(x, y - 20, 600, 35);
                ctx.strokeStyle = '#2196f3';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y - 20, 600, 35);

                // 选项文字
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`${String.fromCharCode(65 + index)}. ${option}`, x + 10, y);

                // 添加点击区域
                canvas.onclick = function(e) {
                    const rect = canvas.getBoundingClientRect();
                    const clickX = e.clientX - rect.left;
                    const clickY = e.clientY - rect.top;

                    if (clickX >= x && clickX <= x + 600 && clickY >= y - 20 && clickY <= y + 15) {
                        answerQuestion(index);
                    }
                };
            });
        }

        // 回答问题
        function answerQuestion(selectedIndex) {
            const question = gameQuestions[currentQuestion];
            const isCorrect = selectedIndex === question.correct;

            if (isCorrect) {
                totalScore += 20;
                showFeedback('正确！+20分', true);
            } else {
                showFeedback(`错误！正确答案是：${question.options[question.correct]}`, false);
            }

            document.getElementById('totalScore').textContent = totalScore;

            // 显示解释
            setTimeout(() => {
                showExplanation(question.explanation);
            }, 1500);

            setTimeout(() => {
                currentQuestion++;
                showQuestion();
            }, 4000);
        }

        // 显示解释
        function showExplanation(explanation) {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            // 解释背景
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(50, 300, 700, 80);
            ctx.strokeStyle = '#4caf50';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 300, 700, 80);

            // 解释文字
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('💡 解释：' + explanation, canvas.width/2, 345);
        }

        // 显示游戏结果
        function showGameResult() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#e8f5e8');
            gradient.addColorStop(1, '#c8e6c9');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 结果标题
            ctx.fillStyle = '#4caf50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🎉 游戏完成！', canvas.width/2, 100);

            // 得分
            ctx.fillStyle = '#333';
            ctx.font = '20px Arial';
            ctx.fillText(`最终得分：${totalScore}分`, canvas.width/2, 150);

            // 评价
            let evaluation = '';
            if (totalScore >= 50) {
                evaluation = '优秀！你已经很好地掌握了数据库视图的知识！';
            } else if (totalScore >= 30) {
                evaluation = '良好！继续学习，你会更棒的！';
            } else {
                evaluation = '需要加强！建议重新学习相关内容。';
            }

            ctx.font = '16px Arial';
            ctx.fillText(evaluation, canvas.width/2, 200);

            // 重置按钮提示
            ctx.font = '14px Arial';
            ctx.fillStyle = '#666';
            ctx.fillText('点击"重新开始"按钮可以再次挑战', canvas.width/2, 300);
        }

        // 重置游戏
        function resetGame() {
            currentQuestion = 0;
            totalScore = 0;
            performanceScore = 0;
            document.getElementById('totalScore').textContent = '0';
            document.getElementById('performanceScore').textContent = '0';

            // 重新初始化所有画布
            initCanvas();

            showFeedback('游戏已重置！', true);
        }

        // 显示反馈
        function showFeedback(message, isPositive) {
            const feedback = document.createElement('div');
            feedback.style.position = 'fixed';
            feedback.style.top = '50%';
            feedback.style.left = '50%';
            feedback.style.transform = 'translate(-50%, -50%)';
            feedback.style.background = isPositive ? '#4caf50' : '#f44336';
            feedback.style.color = 'white';
            feedback.style.padding = '20px 40px';
            feedback.style.borderRadius = '10px';
            feedback.style.fontSize = '18px';
            feedback.style.fontWeight = 'bold';
            feedback.style.zIndex = '10000';
            feedback.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';
            feedback.style.animation = 'pulse 0.5s ease-in-out';
            feedback.textContent = message;

            document.body.appendChild(feedback);

            setTimeout(() => {
                document.body.removeChild(feedback);
            }, 2000);
        }

        // 页面加载时初始化
        window.addEventListener('load', function() {
            initCanvas();

            // 添加浮动元素
            createFloatingElements();
        });

        // 创建浮动装饰元素
        function createFloatingElements() {
            const symbols = ['📊', '🗄️', '📈', '💾', '🔍'];
            
            symbols.forEach((symbol, index) => {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.textContent = symbol;
                element.style.left = Math.random() * window.innerWidth + 'px';
                element.style.top = Math.random() * window.innerHeight + 'px';
                element.style.animationDelay = index * 0.5 + 's';
                document.body.appendChild(element);
                
                // 定期更新位置
                setInterval(() => {
                    element.style.left = Math.random() * window.innerWidth + 'px';
                    element.style.top = Math.random() * window.innerHeight + 'px';
                }, 5000 + Math.random() * 3000);
            });
        }

        // 添加点击效果
        document.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            ripple.style.position = 'fixed';
            ripple.style.left = e.clientX + 'px';
            ripple.style.top = e.clientY + 'px';
            ripple.style.width = '20px';
            ripple.style.height = '20px';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(102, 126, 234, 0.6)';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.animation = 'pulse 0.6s ease-out';
            ripple.style.pointerEvents = 'none';
            ripple.style.zIndex = '9999';
            
            document.body.appendChild(ripple);
            
            setTimeout(() => {
                document.body.removeChild(ripple);
            }, 600);
        });
    </script>
</body>
</html>
