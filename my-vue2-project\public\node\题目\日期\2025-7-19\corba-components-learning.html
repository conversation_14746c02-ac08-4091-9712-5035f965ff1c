<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORBA构件标准学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .components-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .components-demo {
            text-align: center;
            margin: 30px 0;
        }

        #componentsCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .component-controls {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 25px 0;
        }

        .component-btn {
            padding: 15px 10px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .entity-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .process-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .session-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .service-btn {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }

        .component-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .component-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .component-comparison {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .comparison-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .comparison-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .comparison-card.highlight {
            border-color: #00b894;
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }

        .comparison-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .comparison-card p {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-session {
            color: #00a085;
            font-weight: bold;
            background: rgba(0,184,148,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-component {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            animation: floatComponent 15s infinite ease-in-out;
        }

        .comp1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .comp2 {
            top: 70%;
            right: 15%;
            animation-delay: 5s;
        }

        .comp3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatComponent {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-25px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .component-controls {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .component-comparison {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-component comp1"></div>
        <div class="floating-component comp2"></div>
        <div class="floating-component comp3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🏗️ CORBA构件标准学习</h1>
            <p>深度理解OMG定义的四种构件标准及其状态管理方式</p>
        </div>

        <div class="main-grid">
            <div class="components-section">
                <h2 class="section-title">🔧 CORBA构件演示</h2>
                
                <div class="components-demo">
                    <canvas id="componentsCanvas" width="700" height="400"></canvas>
                </div>

                <div class="component-controls">
                    <button class="component-btn entity-btn" onclick="demonstrateComponent('entity')">
                        实体构件<br><small>Entity</small>
                    </button>
                    <button class="component-btn process-btn" onclick="demonstrateComponent('process')">
                        加工构件<br><small>Process</small>
                    </button>
                    <button class="component-btn session-btn" onclick="demonstrateComponent('session')">
                        会话构件<br><small>Session</small>
                    </button>
                    <button class="component-btn service-btn" onclick="demonstrateComponent('service')">
                        服务构件<br><small>Service</small>
                    </button>
                </div>

                <div class="component-comparison">
                    <div class="comparison-card">
                        <h3>🏛️ 实体构件 (Entity)</h3>
                        <p>• 长期持久化<br>• 事务性行为<br>• 容器管理持久化<br>• 有客户端主键</p>
                    </div>
                    <div class="comparison-card">
                        <h3>⚙️ 加工构件 (Process)</h3>
                        <p>• 需要持久化<br>• 容器管理持久化<br>• 无客户端主键<br>• 业务流程处理</p>
                    </div>
                    <div class="comparison-card highlight">
                        <h3>💬 会话构件 (Session)</h3>
                        <p>• 自身管理状态<br>• 不需要持久化<br>• 临时性会话<br>• 构件自维护状态</p>
                    </div>
                    <div class="comparison-card">
                        <h3>🌐 服务构件 (Service)</h3>
                        <p>• 无状态构件<br>• 不需要持久化<br>• 纯功能服务<br>• 无状态信息</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 对象管理组织（OMG）基于CORBA基础设施定义了4种构件标准。其中（　　）的状态信息是由构件自身而不是由容器维护。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. 实体构件
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 加工构件
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. 服务构件
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        D. 会话构件
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：D. 会话构件</strong></p>
                    <p>OMG基于CORBA基础设施定义的四种构件标准：</p>
                    <ul>
                        <li><strong>实体构件 (Entity)</strong>：
                            <br>• 需要长期持久化，主要用于事务性行为
                            <br>• <strong>由容器管理其持久化</strong>
                            <br>• 有客户端可访问的主键</li>
                        <li><strong>加工构件 (Process)</strong>：
                            <br>• 同样需要持久化处理
                            <br>• <strong>由容器管理其持久化</strong>
                            <br>• 没有客户端可访问的主键</li>
                        <li><span class="highlight-session">会话构件 (Session)</span>：
                            <br>• <strong>不需要容器管理其持久化</strong>
                            <br>• <strong>状态信息必须由构件自己管理</strong>
                            <br>• 用于临时性的会话处理</li>
                        <li><strong>服务构件 (Service)</strong>：
                            <br>• 是无状态的构件
                            <br>• 不需要状态管理
                            <br>• 提供纯功能性服务</li>
                    </ul>
                    <p><strong>关键理解</strong>：会话构件是唯一一种需要自身管理状态信息而不依赖容器的构件类型。</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了CORBA构件的状态管理特性！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('componentsCanvas');
        const ctx = canvas.getContext('2d');
        let currentComponent = 'session';
        let animationId = null;

        // 演示不同构件类型
        function demonstrateComponent(componentType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            currentComponent = componentType;
            
            // 更新按钮状态
            document.querySelectorAll('.component-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${componentType}-btn`).classList.add('active');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(componentType) {
                case 'entity':
                    drawEntityComponent();
                    break;
                case 'process':
                    drawProcessComponent();
                    break;
                case 'session':
                    drawSessionComponent();
                    break;
                case 'service':
                    drawServiceComponent();
                    break;
            }
        }

        // 绘制实体构件
        function drawEntityComponent() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('实体构件 (Entity)', 350, 40);

            // 构件本体
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(250, 100, 200, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 100, 200, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('实体构件', 350, 140);
            ctx.font = '12px Arial';
            ctx.fillText('事务性行为', 350, 160);
            ctx.fillText('有主键', 350, 180);

            // 容器
            ctx.fillStyle = 'rgba(116, 185, 255, 0.3)';
            ctx.fillRect(200, 80, 300, 140);
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 3;
            ctx.setLineDash([10, 5]);
            ctx.strokeRect(200, 80, 300, 140);
            ctx.setLineDash([]);

            ctx.fillStyle = '#0984e3';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('容器管理', 350, 70);

            // 持久化存储
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(300, 250, 100, 60);
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 2;
            ctx.strokeRect(300, 250, 100, 60);
            
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('持久化存储', 350, 285);

            // 连接线
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(350, 200);
            ctx.lineTo(350, 250);
            ctx.stroke();

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 长期持久化', 50, 340);
            ctx.fillText('• 容器管理状态', 200, 340);
            ctx.fillText('• 事务性行为', 350, 340);
            ctx.fillText('• 有客户端主键', 500, 340);
        }

        // 绘制加工构件
        function drawProcessComponent() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('加工构件 (Process)', 350, 40);

            // 构件本体
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(250, 100, 200, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 100, 200, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('加工构件', 350, 140);
            ctx.font = '12px Arial';
            ctx.fillText('业务流程', 350, 160);
            ctx.fillText('无主键', 350, 180);

            // 容器
            ctx.fillStyle = 'rgba(253, 121, 168, 0.3)';
            ctx.fillRect(200, 80, 300, 140);
            ctx.strokeStyle = '#e84393';
            ctx.lineWidth = 3;
            ctx.setLineDash([10, 5]);
            ctx.strokeRect(200, 80, 300, 140);
            ctx.setLineDash([]);

            ctx.fillStyle = '#e84393';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('容器管理', 350, 70);

            // 持久化存储
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(300, 250, 100, 60);
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 2;
            ctx.strokeRect(300, 250, 100, 60);
            
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('持久化存储', 350, 285);

            // 连接线
            ctx.strokeStyle = '#e84393';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(350, 200);
            ctx.lineTo(350, 250);
            ctx.stroke();

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 需要持久化', 50, 340);
            ctx.fillText('• 容器管理状态', 200, 340);
            ctx.fillText('• 业务流程处理', 350, 340);
            ctx.fillText('• 无客户端主键', 500, 340);
        }

        // 绘制会话构件
        function drawSessionComponent() {
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('会话构件 (Session)', 350, 40);

            // 构件本体
            ctx.fillStyle = '#00b894';
            ctx.fillRect(250, 100, 200, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 100, 200, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('会话构件', 350, 130);
            ctx.font = '12px Arial';
            ctx.fillText('自管理状态', 350, 150);
            ctx.fillText('临时性会话', 350, 170);
            ctx.fillText('不需持久化', 350, 190);

            // 内部状态管理
            ctx.fillStyle = '#00a085';
            ctx.fillRect(270, 120, 60, 30);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(270, 120, 60, 30);
            
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.fillText('状态管理', 300, 140);

            ctx.fillStyle = '#00a085';
            ctx.fillRect(370, 120, 60, 30);
            ctx.strokeRect(370, 120, 60, 30);
            ctx.fillText('会话数据', 400, 140);

            // 容器（虚线表示不管理状态）
            ctx.fillStyle = 'rgba(0, 184, 148, 0.1)';
            ctx.fillRect(200, 80, 300, 140);
            ctx.strokeStyle = '#00b894';
            ctx.lineWidth = 2;
            ctx.setLineDash([15, 10]);
            ctx.strokeRect(200, 80, 300, 140);
            ctx.setLineDash([]);

            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('容器（不管理状态）', 350, 70);

            // 自管理标识
            ctx.fillStyle = '#e17055';
            ctx.fillRect(520, 140, 120, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(520, 140, 120, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('构件自身管理', 580, 165);

            // 连接线
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(450, 150);
            ctx.lineTo(520, 160);
            ctx.stroke();

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 自身管理状态', 50, 340);
            ctx.fillText('• 不需要持久化', 200, 340);
            ctx.fillText('• 临时性会话', 350, 340);
            ctx.fillText('• 构件自维护', 500, 340);
        }

        // 绘制服务构件
        function drawServiceComponent() {
            ctx.fillStyle = '#fdcb6e';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('服务构件 (Service)', 350, 40);

            // 构件本体
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(250, 100, 200, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 100, 200, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('服务构件', 350, 140);
            ctx.font = '12px Arial';
            ctx.fillText('无状态', 350, 160);
            ctx.fillText('纯功能服务', 350, 180);

            // 无状态标识
            ctx.fillStyle = '#e17055';
            ctx.fillRect(300, 250, 100, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(300, 250, 100, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('无状态', 350, 275);

            // 功能服务
            const services = ['服务A', '服务B', '服务C'];
            services.forEach((service, index) => {
                const x = 150 + index * 150;
                const y = 320;
                
                ctx.fillStyle = '#fdcb6e';
                ctx.fillRect(x, y, 80, 30);
                ctx.strokeStyle = '#e17055';
                ctx.lineWidth = 1;
                ctx.strokeRect(x, y, 80, 30);
                
                ctx.fillStyle = '#2d3436';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(service, x + 40, y + 20);
            });

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 无状态构件', 50, 380);
            ctx.fillText('• 不需要持久化', 200, 380);
            ctx.fillText('• 纯功能服务', 350, 380);
            ctx.fillText('• 无状态信息', 500, 380);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('D. 会话构件')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画
                    demonstrateComponent('session');
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateComponent('session');
            
            // 自动演示序列
            setTimeout(() => demonstrateComponent('entity'), 4000);
            setTimeout(() => demonstrateComponent('process'), 8000);
            setTimeout(() => demonstrateComponent('service'), 12000);
            setTimeout(() => demonstrateComponent('session'), 16000);
        };
    </script>
</body>
</html>
