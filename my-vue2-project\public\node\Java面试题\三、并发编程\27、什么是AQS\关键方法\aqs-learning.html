<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AQS 同步器学习 - 交互式教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .method-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .method-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .method-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .method-card:hover::before {
            left: 100%;
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }

        .method-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .method-desc {
            font-size: 0.95rem;
            opacity: 0.9;
            line-height: 1.5;
        }

        .interactive-demo {
            background: #f8fafc;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .demo-button:active {
            transform: translateY(0);
        }

        .explanation {
            background: #e6fffa;
            border-left: 4px solid #38b2ac;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 12px 12px 0;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #2d3748;
        }

        .game-score {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px 25px;
            border-radius: 50px;
            font-weight: 600;
            color: #2d3748;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="game-score">
        学习进度: <span id="score">0</span>/100
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">AQS 同步器</h1>
            <p class="subtitle">AbstractQueuedSynchronizer 交互式学习之旅</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是 AQS？</h2>
            <div class="explanation">
                AQS（AbstractQueuedSynchronizer）是Java并发包的核心基础框架，它为实现依赖于先进先出（FIFO）等待队列的阻塞锁和相关同步器提供了一个框架。
            </div>
            <div class="canvas-container">
                <canvas id="aqsOverview" width="800" height="300"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animateAQSOverview()">🎬 播放AQS概览动画</button>
                <button class="demo-button" onclick="resetAQSOverview()">🔄 重置动画</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 模板方法 - 使用者调用</h2>
            <div class="explanation">
                模板方法是AQS提供给使用者的接口，定义了获取和释放锁的标准流程。这些方法内部会调用钩子方法来实现具体的同步逻辑。
            </div>
            <div class="method-grid">
                <div class="method-card" onclick="demonstrateMethod('acquire')">
                    <div class="method-name">acquire(int arg)</div>
                    <div class="method-desc">获取锁（独占模式）- 如果获取失败，线程会被加入等待队列并阻塞</div>
                </div>
                <div class="method-card" onclick="demonstrateMethod('release')">
                    <div class="method-name">release(int arg)</div>
                    <div class="method-desc">释放锁（独占模式）- 释放锁并唤醒等待队列中的下一个线程</div>
                </div>
                <div class="method-card" onclick="demonstrateMethod('acquireShared')">
                    <div class="method-name">acquireShared(int arg)</div>
                    <div class="method-desc">获取锁（共享模式）- 多个线程可以同时获取共享锁</div>
                </div>
                <div class="method-card" onclick="demonstrateMethod('releaseShared')">
                    <div class="method-name">releaseShared(int arg)</div>
                    <div class="method-desc">释放锁（共享模式）- 释放共享锁，可能唤醒多个等待线程</div>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="templateMethods" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="startTemplateDemo()">🎯 开始模板方法演示</button>
                <button class="demo-button" onclick="resetTemplateDemo()">🔄 重置演示</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔧 钩子方法 - 子类实现</h2>
            <div class="explanation">
                钩子方法是需要子类实现的抽象方法，定义了具体的同步逻辑。AQS的模板方法会调用这些钩子方法来实现不同的同步器。
            </div>
            <div class="method-grid">
                <div class="method-card" onclick="demonstrateHook('tryAcquire')">
                    <div class="method-name">tryAcquire(int arg)</div>
                    <div class="method-desc">尝试获取锁（独占模式）- 返回true表示获取成功，false表示失败</div>
                </div>
                <div class="method-card" onclick="demonstrateHook('tryRelease')">
                    <div class="method-name">tryRelease(int arg)</div>
                    <div class="method-desc">尝试释放锁（独占模式）- 返回true表示完全释放，false表示部分释放</div>
                </div>
                <div class="method-card" onclick="demonstrateHook('tryAcquireShared')">
                    <div class="method-name">tryAcquireShared(int arg)</div>
                    <div class="method-desc">尝试获取锁（共享模式）- 返回负数失败，0成功但无剩余，正数成功且有剩余</div>
                </div>
                <div class="method-card" onclick="demonstrateHook('tryReleaseShared')">
                    <div class="method-name">tryReleaseShared(int arg)</div>
                    <div class="method-desc">尝试释放锁（共享模式）- 返回true表示释放后可能有等待线程需要唤醒</div>
                </div>
                <div class="method-card" onclick="demonstrateHook('isHeldExclusively')">
                    <div class="method-name">isHeldExclusively()</div>
                    <div class="method-desc">判断当前线程是否独占锁 - 用于条件变量的实现</div>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="hookMethods" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="startHookDemo()">🔧 开始钩子方法演示</button>
                <button class="demo-button" onclick="resetHookDemo()">🔄 重置演示</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎲 互动游戏：AQS 同步器挑战</h2>
            <div class="explanation">
                通过游戏来加深对AQS工作原理的理解！选择正确的方法来处理不同的同步场景。
            </div>
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="500"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="startGame()">🎮 开始挑战游戏</button>
                <button class="demo-button" onclick="nextChallenge()">➡️ 下一个挑战</button>
                <button class="demo-button" onclick="resetGame()">🔄 重新开始</button>
            </div>
        </div>
    </div>

    <script>
        let score = 0;
        let animationId;
        
        // 更新学习进度
        function updateProgress(points) {
            score = Math.min(score + points, 100);
            document.getElementById('score').textContent = score;
            document.getElementById('progressFill').style.width = score + '%';
            
            if (score >= 100) {
                setTimeout(() => {
                    alert('🎉 恭喜！您已完成AQS学习！');
                }, 500);
            }
        }

        // AQS概览动画
        function animateAQSOverview() {
            const canvas = document.getElementById('aqsOverview');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景渐变
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#f0f9ff');
                gradient.addColorStop(1, '#e0e7ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // AQS核心框
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                
                // 主框架
                ctx.fillStyle = '#3b82f6';
                ctx.fillRect(centerX - 100, centerY - 40, 200, 80);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('AQS 核心', centerX, centerY);
                
                // 动态等待队列
                const queueY = centerY + 80;
                for (let i = 0; i < 5; i++) {
                    const x = centerX - 200 + i * 80;
                    const offset = Math.sin(frame * 0.1 + i * 0.5) * 5;
                    
                    ctx.fillStyle = i === 0 ? '#ef4444' : '#6b7280';
                    ctx.fillRect(x, queueY + offset, 60, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(`线程${i + 1}`, x + 30, queueY + offset + 25);
                }
                
                // 连接线动画
                ctx.strokeStyle = '#3b82f6';
                ctx.lineWidth = 3;
                ctx.setLineDash([5, 5]);
                ctx.lineDashOffset = -frame * 0.5;
                
                ctx.beginPath();
                ctx.moveTo(centerX, centerY + 40);
                ctx.lineTo(centerX, queueY);
                ctx.stroke();
                
                // 状态指示器
                ctx.fillStyle = '#10b981';
                ctx.beginPath();
                ctx.arc(centerX + 120, centerY - 60, 8 + Math.sin(frame * 0.2) * 3, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = '#1f2937';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('同步状态', centerX + 135, centerY - 55);
                
                frame++;
                animationId = requestAnimationFrame(draw);
            }
            
            draw();
            updateProgress(10);
        }
        
        function resetAQSOverview() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            const canvas = document.getElementById('aqsOverview');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制静态初始状态
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f0f9ff');
            gradient.addColorStop(1, '#e0e7ff');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = '#6b7280';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击"播放AQS概览动画"开始学习', canvas.width / 2, canvas.height / 2);
        }
        
        // 模板方法演示
        let templateAnimationId;
        let currentMethod = '';

        function demonstrateMethod(methodName) {
            currentMethod = methodName;
            const canvas = document.getElementById('templateMethods');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#fef3c7');
            gradient.addColorStop(1, '#fde68a');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 方法说明
            ctx.fillStyle = '#92400e';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`${methodName} 方法演示`, canvas.width / 2, 50);

            // 根据不同方法显示不同内容
            switch(methodName) {
                case 'acquire':
                    drawAcquireDemo(ctx);
                    break;
                case 'release':
                    drawReleaseDemo(ctx);
                    break;
                case 'acquireShared':
                    drawAcquireSharedDemo(ctx);
                    break;
                case 'releaseShared':
                    drawReleaseSharedDemo(ctx);
                    break;
            }

            updateProgress(5);
        }

        function drawAcquireDemo(ctx) {
            // 线程尝试获取锁
            ctx.fillStyle = '#3b82f6';
            ctx.fillRect(100, 150, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('线程A', 140, 185);

            // 箭头
            ctx.strokeStyle = '#ef4444';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(180, 180);
            ctx.lineTo(250, 180);
            ctx.stroke();

            // 锁状态
            ctx.fillStyle = '#ef4444';
            ctx.fillRect(300, 150, 100, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('锁已被占用', 350, 185);

            // 等待队列
            ctx.fillStyle = '#6b7280';
            ctx.fillRect(500, 150, 60, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('等待', 530, 185);
            ctx.fillText('队列', 530, 200);
        }

        function drawReleaseDemo(ctx) {
            // 释放锁的线程
            ctx.fillStyle = '#10b981';
            ctx.fillRect(100, 150, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('线程A', 140, 185);

            // 释放箭头
            ctx.strokeStyle = '#10b981';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(180, 180);
            ctx.lineTo(250, 180);
            ctx.stroke();

            // 锁变为可用
            ctx.fillStyle = '#10b981';
            ctx.fillRect(300, 150, 100, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('锁可用', 350, 185);

            // 唤醒等待线程
            ctx.strokeStyle = '#f59e0b';
            ctx.beginPath();
            ctx.moveTo(400, 180);
            ctx.lineTo(470, 180);
            ctx.stroke();

            ctx.fillStyle = '#f59e0b';
            ctx.fillRect(500, 150, 60, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('唤醒', 530, 185);
        }

        function drawAcquireSharedDemo(ctx) {
            // 多个线程共享锁
            for (let i = 0; i < 3; i++) {
                ctx.fillStyle = '#8b5cf6';
                ctx.fillRect(100 + i * 90, 150, 70, 60);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`线程${i + 1}`, 135 + i * 90, 185);
            }

            // 共享锁
            ctx.fillStyle = '#8b5cf6';
            ctx.fillRect(450, 150, 120, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('共享锁', 510, 185);
            ctx.fillText('(可多个持有)', 510, 200);
        }

        function drawReleaseSharedDemo(ctx) {
            // 释放共享锁
            ctx.fillStyle = '#06b6d4';
            ctx.fillRect(100, 150, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('线程A', 140, 185);

            // 释放箭头
            ctx.strokeStyle = '#06b6d4';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(180, 180);
            ctx.lineTo(250, 180);
            ctx.stroke();

            // 共享锁状态更新
            ctx.fillStyle = '#06b6d4';
            ctx.fillRect(300, 150, 120, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('共享锁', 360, 180);
            ctx.fillText('计数减1', 360, 195);

            // 可能唤醒等待线程
            ctx.fillStyle = '#f59e0b';
            ctx.fillRect(500, 150, 80, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('可能唤醒', 540, 180);
            ctx.fillText('等待线程', 540, 195);
        }

        function startTemplateDemo() {
            if (!currentMethod) {
                alert('请先点击一个模板方法卡片！');
                return;
            }

            const canvas = document.getElementById('templateMethods');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                // 添加闪烁效果
                if (frame % 60 < 30) {
                    ctx.shadowColor = '#fbbf24';
                    ctx.shadowBlur = 20;
                } else {
                    ctx.shadowBlur = 0;
                }

                demonstrateMethod(currentMethod);
                frame++;
                templateAnimationId = requestAnimationFrame(animate);
            }

            animate();
            updateProgress(10);
        }

        function resetTemplateDemo() {
            if (templateAnimationId) {
                cancelAnimationFrame(templateAnimationId);
            }
            const canvas = document.getElementById('templateMethods');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.shadowBlur = 0;

            // 显示提示
            ctx.fillStyle = '#6b7280';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击上方的方法卡片开始学习', canvas.width / 2, canvas.height / 2);
        }

        // 钩子方法演示
        let hookAnimationId;
        let currentHook = '';

        function demonstrateHook(hookName) {
            currentHook = hookName;
            const canvas = document.getElementById('hookMethods');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#ddd6fe');
            gradient.addColorStop(1, '#c4b5fd');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 方法说明
            ctx.fillStyle = '#5b21b6';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`${hookName} 钩子方法`, canvas.width / 2, 50);

            // 根据不同钩子显示不同内容
            switch(hookName) {
                case 'tryAcquire':
                    drawTryAcquireDemo(ctx);
                    break;
                case 'tryRelease':
                    drawTryReleaseDemo(ctx);
                    break;
                case 'tryAcquireShared':
                    drawTryAcquireSharedDemo(ctx);
                    break;
                case 'tryReleaseShared':
                    drawTryReleaseSharedDemo(ctx);
                    break;
                case 'isHeldExclusively':
                    drawIsHeldExclusivelyDemo(ctx);
                    break;
            }

            updateProgress(5);
        }

        function drawTryAcquireDemo(ctx) {
            // 子类实现
            ctx.fillStyle = '#7c3aed';
            ctx.fillRect(100, 120, 150, 80);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('子类实现', 175, 155);
            ctx.fillText('tryAcquire', 175, 175);

            // 判断逻辑
            ctx.fillStyle = '#f59e0b';
            ctx.fillRect(300, 120, 120, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('检查状态', 360, 155);
            ctx.fillText('CAS操作', 360, 175);

            // 返回结果
            ctx.fillStyle = '#10b981';
            ctx.fillRect(480, 100, 80, 50);
            ctx.fillStyle = 'white';
            ctx.fillText('true', 520, 130);

            ctx.fillStyle = '#ef4444';
            ctx.fillRect(480, 170, 80, 50);
            ctx.fillStyle = 'white';
            ctx.fillText('false', 520, 200);

            // 箭头
            ctx.strokeStyle = '#374151';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(250, 160);
            ctx.lineTo(300, 160);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(420, 140);
            ctx.lineTo(480, 125);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(420, 180);
            ctx.lineTo(480, 195);
            ctx.stroke();
        }

        function drawTryReleaseDemo(ctx) {
            // 释放逻辑
            ctx.fillStyle = '#059669';
            ctx.fillRect(150, 120, 120, 80);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('释放资源', 210, 155);
            ctx.fillText('更新状态', 210, 175);

            // 判断是否完全释放
            ctx.fillStyle = '#dc2626';
            ctx.fillRect(320, 120, 120, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('是否完全', 380, 155);
            ctx.fillText('释放？', 380, 175);

            // 返回结果
            ctx.fillStyle = '#10b981';
            ctx.fillRect(500, 120, 80, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('true/false', 540, 165);
        }

        function drawTryAcquireSharedDemo(ctx) {
            // 共享获取逻辑
            ctx.fillStyle = '#0891b2';
            ctx.fillRect(100, 120, 140, 80);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('检查共享资源', 170, 155);
            ctx.fillText('剩余数量', 170, 175);

            // 返回值说明
            ctx.fillStyle = '#ef4444';
            ctx.fillRect(280, 80, 100, 40);
            ctx.fillStyle = 'white';
            ctx.fillText('< 0: 失败', 330, 105);

            ctx.fillStyle = '#f59e0b';
            ctx.fillRect(280, 130, 100, 40);
            ctx.fillStyle = 'white';
            ctx.fillText('= 0: 成功', 330, 155);
            ctx.fillText('无剩余', 330, 165);

            ctx.fillStyle = '#10b981';
            ctx.fillRect(280, 180, 100, 40);
            ctx.fillStyle = 'white';
            ctx.fillText('> 0: 成功', 330, 205);
            ctx.fillText('有剩余', 330, 215);
        }

        function drawTryReleaseSharedDemo(ctx) {
            // 共享释放
            ctx.fillStyle = '#7c2d12';
            ctx.fillRect(150, 120, 120, 80);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('释放共享', 210, 155);
            ctx.fillText('资源', 210, 175);

            // 是否需要唤醒
            ctx.fillStyle = '#b45309';
            ctx.fillRect(320, 120, 120, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('是否需要', 380, 155);
            ctx.fillText('唤醒等待者', 380, 175);

            // 返回结果
            ctx.fillStyle = '#10b981';
            ctx.fillRect(500, 120, 80, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('true/false', 540, 165);
        }

        function drawIsHeldExclusivelyDemo(ctx) {
            // 当前线程
            ctx.fillStyle = '#1e40af';
            ctx.fillRect(150, 120, 100, 80);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('当前线程', 200, 165);

            // 检查逻辑
            ctx.fillStyle = '#7c2d12';
            ctx.fillRect(300, 120, 120, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('是否独占', 360, 155);
            ctx.fillText('持有锁？', 360, 175);

            // 结果
            ctx.fillStyle = '#059669';
            ctx.fillRect(480, 120, 80, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('true/false', 520, 165);
        }

        function startHookDemo() {
            if (!currentHook) {
                alert('请先点击一个钩子方法卡片！');
                return;
            }

            const canvas = document.getElementById('hookMethods');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                // 添加脉冲效果
                if (frame % 40 < 20) {
                    ctx.shadowColor = '#8b5cf6';
                    ctx.shadowBlur = 15;
                } else {
                    ctx.shadowBlur = 0;
                }

                demonstrateHook(currentHook);
                frame++;
                hookAnimationId = requestAnimationFrame(animate);
            }

            animate();
            updateProgress(10);
        }

        function resetHookDemo() {
            if (hookAnimationId) {
                cancelAnimationFrame(hookAnimationId);
            }
            const canvas = document.getElementById('hookMethods');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.shadowBlur = 0;

            // 显示提示
            ctx.fillStyle = '#6b7280';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击上方的钩子方法卡片开始学习', canvas.width / 2, canvas.height / 2);
        }

        // 游戏功能
        let gameState = {
            currentChallenge: 0,
            gameScore: 0,
            challenges: [
                {
                    question: "线程想要获取独占锁，应该调用哪个方法？",
                    options: ["acquire()", "acquireShared()", "tryAcquire()", "release()"],
                    correct: 0,
                    explanation: "acquire()是获取独占锁的模板方法，会调用tryAcquire()钩子方法"
                },
                {
                    question: "子类需要实现哪个方法来定义独占锁的获取逻辑？",
                    options: ["acquire()", "tryAcquire()", "release()", "tryRelease()"],
                    correct: 1,
                    explanation: "tryAcquire()是钩子方法，需要子类实现具体的获取逻辑"
                },
                {
                    question: "共享锁的tryAcquireShared()返回负数表示什么？",
                    options: ["获取成功", "获取失败", "有剩余资源", "需要等待"],
                    correct: 1,
                    explanation: "返回负数表示获取失败，0表示成功但无剩余，正数表示成功且有剩余"
                },
                {
                    question: "isHeldExclusively()方法主要用于什么？",
                    options: ["获取锁", "释放锁", "条件变量", "共享锁"],
                    correct: 2,
                    explanation: "isHeldExclusively()主要用于条件变量的实现，判断当前线程是否独占锁"
                }
            ]
        };

        function startGame() {
            gameState.currentChallenge = 0;
            gameState.gameScore = 0;
            drawChallenge();
            updateProgress(5);
        }

        function drawChallenge() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            const challenge = gameState.challenges[gameState.currentChallenge];

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#fef7ff');
            gradient.addColorStop(1, '#f3e8ff');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 挑战标题
            ctx.fillStyle = '#581c87';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`挑战 ${gameState.currentChallenge + 1}/${gameState.challenges.length}`, canvas.width / 2, 50);

            // 问题
            ctx.fillStyle = '#374151';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            const words = challenge.question.split('');
            let line = '';
            let y = 100;

            for (let i = 0; i < words.length; i++) {
                const testLine = line + words[i];
                const metrics = ctx.measureText(testLine);
                if (metrics.width > 600 && i > 0) {
                    ctx.fillText(line, canvas.width / 2, y);
                    line = words[i];
                    y += 30;
                } else {
                    line = testLine;
                }
            }
            ctx.fillText(line, canvas.width / 2, y);

            // 选项按钮
            for (let i = 0; i < challenge.options.length; i++) {
                const x = 150 + (i % 2) * 300;
                const y = 200 + Math.floor(i / 2) * 80;

                // 按钮背景
                ctx.fillStyle = '#8b5cf6';
                ctx.fillRect(x, y, 250, 60);

                // 按钮文字
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(challenge.options[i], x + 125, y + 38);

                // 选项标号
                ctx.fillStyle = '#fbbf24';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(String.fromCharCode(65 + i), x + 20, y + 35);
            }

            // 游戏分数
            ctx.fillStyle = '#059669';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`游戏得分: ${gameState.gameScore}`, 50, 450);
        }

        function nextChallenge() {
            if (gameState.currentChallenge < gameState.challenges.length - 1) {
                gameState.currentChallenge++;
                drawChallenge();
            } else {
                // 游戏结束
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 结束背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#ecfdf5');
                gradient.addColorStop(1, '#d1fae5');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 恭喜信息
                ctx.fillStyle = '#065f46';
                ctx.font = 'bold 36px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 恭喜完成所有挑战！', canvas.width / 2, 200);

                ctx.font = '24px Arial';
                ctx.fillText(`最终得分: ${gameState.gameScore}/${gameState.challenges.length * 10}`, canvas.width / 2, 250);

                ctx.font = '18px Arial';
                ctx.fillStyle = '#374151';
                ctx.fillText('您已经掌握了AQS的核心概念！', canvas.width / 2, 300);

                updateProgress(20);
            }
        }

        function resetGame() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 初始提示
            ctx.fillStyle = '#6b7280';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 点击"开始挑战游戏"来测试你的知识！', canvas.width / 2, canvas.height / 2);

            gameState.currentChallenge = 0;
            gameState.gameScore = 0;
        }

        // 添加点击事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            resetAQSOverview();
            resetTemplateDemo();
            resetHookDemo();
            resetGame();

            // 游戏画布点击事件
            const gameCanvas = document.getElementById('gameCanvas');
            gameCanvas.addEventListener('click', function(event) {
                if (gameState.currentChallenge >= gameState.challenges.length) return;

                const rect = gameCanvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                // 检查点击的选项
                for (let i = 0; i < 4; i++) {
                    const optionX = 150 + (i % 2) * 300;
                    const optionY = 200 + Math.floor(i / 2) * 80;

                    if (x >= optionX && x <= optionX + 250 && y >= optionY && y <= optionY + 60) {
                        const challenge = gameState.challenges[gameState.currentChallenge];
                        const ctx = gameCanvas.getContext('2d');

                        if (i === challenge.correct) {
                            // 正确答案
                            ctx.fillStyle = '#10b981';
                            ctx.fillRect(optionX, optionY, 250, 60);
                            ctx.fillStyle = 'white';
                            ctx.font = '16px Arial';
                            ctx.textAlign = 'center';
                            ctx.fillText('✓ 正确！', optionX + 125, optionY + 38);

                            gameState.gameScore += 10;
                            updateProgress(5);
                        } else {
                            // 错误答案
                            ctx.fillStyle = '#ef4444';
                            ctx.fillRect(optionX, optionY, 250, 60);
                            ctx.fillStyle = 'white';
                            ctx.font = '16px Arial';
                            ctx.textAlign = 'center';
                            ctx.fillText('✗ 错误', optionX + 125, optionY + 38);
                        }

                        // 显示解释
                        setTimeout(() => {
                            ctx.fillStyle = 'rgba(0,0,0,0.8)';
                            ctx.fillRect(50, 350, 700, 100);
                            ctx.fillStyle = 'white';
                            ctx.font = '16px Arial';
                            ctx.textAlign = 'center';
                            ctx.fillText('解释: ' + challenge.explanation, canvas.width / 2, 400);
                            ctx.fillText('点击"下一个挑战"继续', canvas.width / 2, 430);
                        }, 1000);

                        break;
                    }
                }
            });
        });
    </script>
</body>
</html>
