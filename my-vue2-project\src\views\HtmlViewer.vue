<template>
  <div class="html-viewer">
    <iframe :src="fileUrl" frameborder="0" width="100%" height="100%"></iframe>
  </div>
</template>

<script>
export default {
  name: 'HtmlViewer',
  props: {
    filePath: {
      type: String,
      required: true
    }
  },
  computed: {
    fileUrl() {
      // We need to make sure the path is properly encoded
      return `${process.env.BASE_URL}${this.filePath}`;
    }
  }
}
</script>

<style scoped>
.html-viewer {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

iframe {
  border: none;
}
</style> 