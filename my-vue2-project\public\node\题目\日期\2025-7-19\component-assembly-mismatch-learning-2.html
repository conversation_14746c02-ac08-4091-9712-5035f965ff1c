<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构件组装架构失配学习 - 连接子失配专题</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .mismatch-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .mismatch-demo {
            text-align: center;
            margin: 30px 0;
        }

        #mismatchCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .mismatch-controls {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 25px 0;
        }

        .mismatch-btn {
            padding: 20px 15px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .component-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .connector-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .mismatch-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .mismatch-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .comparison-section {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .comparison-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .comparison-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .comparison-card.component {
            border-color: #fd79a8;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .comparison-card.connector {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .comparison-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .comparison-card p {
            font-size: 1rem;
            line-height: 1.6;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-component {
            color: #e84393;
            font-weight: bold;
            background: rgba(253,121,168,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .highlight-connector {
            color: #0984e3;
            font-weight: bold;
            background: rgba(116,185,255,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-connector {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: floatConnector 16s infinite ease-in-out;
        }

        .conn1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .conn2 {
            top: 70%;
            right: 15%;
            animation-delay: 5s;
        }

        .conn3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatConnector {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-25px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .mismatch-controls {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-connector conn1"></div>
        <div class="floating-connector conn2"></div>
        <div class="floating-connector conn3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🔗 构件组装架构失配专题</h1>
            <p>深度理解连接子失配的交互协议与数据格式特征</p>
        </div>

        <div class="main-grid">
            <div class="mismatch-section">
                <h2 class="section-title">⚠️ 架构失配类型对比</h2>
                
                <div class="mismatch-demo">
                    <canvas id="mismatchCanvas" width="700" height="400"></canvas>
                </div>

                <div class="mismatch-controls">
                    <button class="mismatch-btn component-btn" onclick="demonstrateMismatch('component')">
                        构件失配<br><small>基础设施、控制模型、数据模型</small>
                    </button>
                    <button class="mismatch-btn connector-btn" onclick="demonstrateMismatch('connector')">
                        连接子失配<br><small>交互协议、数据格式</small>
                    </button>
                </div>

                <div class="comparison-section">
                    <div class="comparison-card component">
                        <h3>🧩 构件失配 (Component Mismatch) - 第一空答案</h3>
                        <p><strong>失配原因</strong>：系统对构件内部特性的假设存在冲突<br>
                        • <strong>基础设施假设</strong>：运行环境、平台依赖<br>
                        • <strong>控制模型假设</strong>：执行流程、生命周期<br>
                        • <strong>数据模型假设</strong>：数据结构、类型系统<br>
                        • <strong>影响范围</strong>：构件内部实现和行为</p>
                    </div>
                    <div class="comparison-card connector">
                        <h3>🔗 连接子失配 (Connector Mismatch) - 第二空答案</h3>
                        <p><strong>失配原因</strong>：系统对构件连接方式的假设存在冲突<br>
                        • <strong>交互协议假设</strong>：通信方式、调用约定<br>
                        • <strong>数据格式假设</strong>：接口参数、消息格式<br>
                        • <strong>连接时机假设</strong>：同步/异步、时序要求<br>
                        • <strong>影响范围</strong>：构件间的连接和通信</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 在构件组装过程中需要检测并解决架构失配问题。其中（构件）失配主要包括由于系统对构件基础设施、控制模型和数据模型的假设存在冲突引起的失配。<strong>（请作答此空）失配包括由于系统对构件交互协议、构件连接时数据格式的假设存在冲突引起的失配</strong>。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. 构件
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 模型
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. 协议
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        D. 连接子
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：D. 连接子</strong></p>
                    <p>根据题目描述的两种失配类型：</p>
                    <ul>
                        <li><strong>第一空</strong>：<span class="highlight-component">构件失配</span>
                            <br>• 失配原因：系统对构件内部特性的假设存在冲突
                            <br>• 包括：基础设施、控制模型、数据模型假设冲突
                            <br>• 影响：构件内部实现和行为</li>
                        <li><strong>第二空</strong>：<span class="highlight-connector">连接子失配</span>
                            <br>• 失配原因：系统对构件连接方式的假设存在冲突
                            <br>• 包括：交互协议、数据格式假设冲突
                            <br>• 影响：构件间的连接和通信</li>
                    </ul>
                    <p><strong>连接子失配的关键特征</strong>：</p>
                    <ul>
                        <li><strong>交互协议失配</strong>：
                            <br>• 通信方式：HTTP vs TCP、RPC vs REST
                            <br>• 调用约定：同步 vs 异步、阻塞 vs 非阻塞
                            <br>• 消息传递：点对点 vs 发布订阅</li>
                        <li><strong>数据格式失配</strong>：
                            <br>• 接口参数：参数类型、数量、顺序不匹配
                            <br>• 消息格式：XML vs JSON vs 二进制
                            <br>• 序列化方式：Java序列化 vs Protocol Buffers</li>
                        <li><strong>连接时机失配</strong>：
                            <br>• 时序要求：严格顺序 vs 并发执行
                            <br>• 事件处理：事件驱动 vs 轮询机制
                            <br>• 连接生命周期：长连接 vs 短连接</li>
                    </ul>
                    <p><strong>为什么不是其他选项</strong>：</p>
                    <ul>
                        <li><strong>A. 构件</strong>：构件失配是第一空的答案</li>
                        <li><strong>B. 模型</strong>：模型只是构件失配的一个方面</li>
                        <li><strong>C. 协议</strong>：协议只是连接子失配的一个方面</li>
                    </ul>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了连接子失配的特征！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('mismatchCanvas');
        const ctx = canvas.getContext('2d');
        let currentMismatch = 'connector';
        let animationId = null;

        // 演示不同失配类型
        function demonstrateMismatch(mismatchType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            currentMismatch = mismatchType;

            // 更新按钮状态
            document.querySelectorAll('.mismatch-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${mismatchType}-btn`).classList.add('active');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            switch(mismatchType) {
                case 'component':
                    drawComponentMismatch();
                    break;
                case 'connector':
                    drawConnectorMismatch();
                    break;
            }
        }

        // 绘制构件失配
        function drawComponentMismatch() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('构件失配 - 构件内部假设冲突', 350, 40);

            // 第一空标识
            ctx.fillStyle = '#e17055';
            ctx.fillRect(250, 60, 200, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 60, 200, 40);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('🎯 第一空答案', 350, 85);

            // 系统期望
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(50, 120, 150, 120);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 120, 150, 120);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('系统期望', 125, 150);
            ctx.font = '12px Arial';
            ctx.fillText('• Windows平台', 125, 175);
            ctx.fillText('• 单线程模型', 125, 195);
            ctx.fillText('• XML数据格式', 125, 215);

            // 构件实际
            ctx.fillStyle = '#e84393';
            ctx.fillRect(500, 120, 150, 120);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(500, 120, 150, 120);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('构件实际', 575, 150);
            ctx.font = '12px Arial';
            ctx.fillText('• Linux平台', 575, 175);
            ctx.fillText('• 多线程模型', 575, 195);
            ctx.fillText('• JSON数据格式', 575, 215);

            // 冲突标识
            ctx.fillStyle = '#e17055';
            ctx.fillRect(250, 140, 200, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 140, 200, 80);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('⚠️ 构件失配', 350, 170);
            ctx.font = '14px Arial';
            ctx.fillText('基础设施冲突', 350, 195);
            ctx.fillText('控制模型冲突', 350, 215);

            // 连接线
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(200, 180);
            ctx.lineTo(250, 180);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(450, 180);
            ctx.lineTo(500, 180);
            ctx.stroke();

            // 失配类型说明
            const mismatchTypes = [
                {x: 50, y: 280, text: '基础设施失配', desc: '平台、环境'},
                {x: 200, y: 280, text: '控制模型失配', desc: '执行流程'},
                {x: 350, y: 280, text: '数据模型失配', desc: '数据结构'},
                {x: 500, y: 280, text: '生命周期失配', desc: '状态管理'}
            ];

            mismatchTypes.forEach(type => {
                ctx.fillStyle = '#fd79a8';
                ctx.fillRect(type.x, type.y, 120, 60);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(type.x, type.y, 120, 60);

                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(type.text, type.x + 60, type.y + 25);
                ctx.font = '10px Arial';
                ctx.fillText(type.desc, type.x + 60, type.y + 45);
            });

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('构件失配：系统对构件基础设施、控制模型、数据模型的假设冲突', 350, 380);
        }

        // 绘制连接子失配（重点）
        function drawConnectorMismatch() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('连接子失配 - 构件连接假设冲突', 350, 40);

            // 第二空标识（突出显示）
            ctx.fillStyle = '#00b894';
            ctx.fillRect(250, 60, 200, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 60, 200, 40);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('🎯 第二空答案', 350, 85);

            // 构件A
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(50, 140, 120, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 140, 120, 80);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('构件A', 110, 170);
            ctx.font = '12px Arial';
            ctx.fillText('HTTP协议', 110, 190);
            ctx.fillText('XML格式', 110, 205);

            // 构件B
            ctx.fillStyle = '#0984e3';
            ctx.fillRect(530, 140, 120, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(530, 140, 120, 80);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('构件B', 590, 170);
            ctx.font = '12px Arial';
            ctx.fillText('TCP协议', 590, 190);
            ctx.fillText('JSON格式', 590, 205);

            // 连接子失配（突出显示）
            ctx.fillStyle = '#e17055';
            ctx.fillRect(250, 140, 200, 80);
            ctx.strokeStyle = '#00b894';
            ctx.lineWidth = 4;
            ctx.strokeRect(250, 140, 200, 80);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('⚠️ 连接子失配', 350, 170);
            ctx.font = '14px Arial';
            ctx.fillText('协议不匹配', 350, 190);
            ctx.fillText('格式不兼容', 350, 205);

            // 连接线（断开，突出显示）
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 8;
            ctx.setLineDash([15, 15]);
            ctx.beginPath();
            ctx.moveTo(170, 180);
            ctx.lineTo(250, 180);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(450, 180);
            ctx.lineTo(530, 180);
            ctx.stroke();
            ctx.setLineDash([]);

            // 失配类型说明（重点突出）
            const connectorMismatchTypes = [
                {x: 50, y: 280, text: '交互协议失配', desc: 'HTTP vs TCP', color: '#00b894'},
                {x: 200, y: 280, text: '数据格式失配', desc: 'XML vs JSON', color: '#00b894'},
                {x: 350, y: 280, text: '时序失配', desc: '同步 vs 异步', color: '#74b9ff'},
                {x: 500, y: 280, text: '接口失配', desc: '参数不匹配', color: '#74b9ff'}
            ];

            connectorMismatchTypes.forEach(type => {
                ctx.fillStyle = type.color;
                ctx.fillRect(type.x, type.y, 120, 60);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(type.x, type.y, 120, 60);

                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(type.text, type.x + 60, type.y + 25);
                ctx.font = '10px Arial';
                ctx.fillText(type.desc, type.x + 60, type.y + 45);
            });

            // 说明（突出显示）
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('连接子失配：系统对构件交互协议、连接时数据格式的假设冲突', 350, 380);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('D. 连接子')) {
                    option.classList.add('correct');
                }
            });

            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画，重点展示连接子失配
                    demonstrateMismatch('connector');
                    setTimeout(() => demonstrateMismatch('component'), 3000);
                    setTimeout(() => demonstrateMismatch('connector'), 6000);
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateMismatch('connector');

            // 自动演示序列，重点展示连接子失配
            setTimeout(() => demonstrateMismatch('component'), 4000);
            setTimeout(() => demonstrateMismatch('connector'), 8000);
            setTimeout(() => demonstrateMismatch('connector'), 12000);
        };
    </script>
</body>
</html>
