<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件系统索引节点 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: auto;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            color: #4a5568;
            margin-bottom: 10px;
            animation: fadeInDown 1s ease-out;
        }

        .header p {
            font-size: 1.2em;
            color: #718096;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .knowledge-panel, .demo-panel {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            animation: slideInLeft 1s ease-out;
        }

        .demo-panel {
            animation: slideInRight 1s ease-out;
        }

        .section-title {
            font-size: 1.8em;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            position: relative;
            margin: 20px 0;
            text-align: center;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: #f7fafc;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            margin: 20px 0;
            text-align: center;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .knowledge-item {
            background: #f8f9fa;
            padding: 20px;
            margin: 15px 0;
            border-radius: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .knowledge-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .knowledge-item h3 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .knowledge-item p {
            color: #4a5568;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .quiz-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .answer-feedback {
            margin-top: 15px;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            text-align: center;
            opacity: 0;
            transition: all 0.5s ease;
        }

        .answer-feedback.show {
            opacity: 1;
        }

        .answer-feedback.correct {
            background: #c6f6d5;
            color: #22543d;
            border: 2px solid #68d391;
        }

        .answer-feedback.incorrect {
            background: #fed7d7;
            color: #742a2a;
            border: 2px solid #fc8181;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #48bb78;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ 文件系统索引节点学习</h1>
            <p>通过动画和交互理解文件系统的索引节点结构</p>
        </div>

        <div class="content-grid">
            <div class="knowledge-panel">
                <h2 class="section-title">📚 基础知识</h2>
                
                <div class="knowledge-item">
                    <h3>🎯 什么是索引节点？</h3>
                    <p>索引节点（inode）是文件系统中存储文件元数据的数据结构，包含文件的<span class="highlight">地址信息</span>，用于定位文件在磁盘上的实际位置。</p>
                </div>

                <div class="knowledge-item">
                    <h3>🔢 地址项结构</h3>
                    <p>本题中有8个地址项 iaddr[0]～iaddr[7]：</p>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li><span class="highlight">iaddr[0]～iaddr[4]</span>：直接地址索引（5个）</li>
                        <li><span class="highlight">iaddr[5]～iaddr[6]</span>：一级间接地址索引（2个）</li>
                        <li><span class="highlight">iaddr[7]</span>：二级间接地址索引（1个）</li>
                    </ul>
                </div>

                <div class="knowledge-item">
                    <h3>📊 容量计算</h3>
                    <p>磁盘块大小：<span class="highlight">1KB = 1024字节</span><br>
                    地址项大小：<span class="highlight">4字节</span><br>
                    每个索引块可存放：<span class="highlight">1024 ÷ 4 = 256个地址</span></p>
                </div>
            </div>

            <div class="demo-panel">
                <h2 class="section-title">🎮 交互演示</h2>
                
                <div class="step-indicator">
                    <div class="step active" id="step1">1</div>
                    <div class="step" id="step2">2</div>
                    <div class="step" id="step3">3</div>
                    <div class="step" id="step4">4</div>
                </div>

                <div class="canvas-container">
                    <canvas id="demoCanvas" width="600" height="400"></canvas>
                </div>

                <div class="controls">
                    <button class="btn" onclick="startDemo()">🚀 开始演示</button>
                    <button class="btn" onclick="showLogicalBlock5()">📍 查找逻辑块5</button>
                    <button class="btn" onclick="showLogicalBlock261()">📍 查找逻辑块261</button>
                    <button class="btn" onclick="resetDemo()">🔄 重置</button>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title">🧠 练习题目</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 15px; margin: 20px 0;">
                <h3>题目：</h3>
                <p>若用户访问文件File1中逻辑块号为5和261的信息，则对应的物理块号分别为（ ）；101号物理块存放的是（ ）。</p>
            </div>
            
            <div style="margin: 20px 0;">
                <h4>逻辑块5和261对应的物理块号：</h4>
                <button class="btn" onclick="checkAnswer('A')">A. 89和90</button>
                <button class="btn" onclick="checkAnswer('B')">B. 89和136</button>
                <button class="btn" onclick="checkAnswer('C')">C. 58和187</button>
                <button class="btn" onclick="checkAnswer('D')">D. 90和136</button>
            </div>

            <div class="answer-feedback" id="feedback"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('demoCanvas');
        const ctx = canvas.getContext('2d');
        let currentStep = 1;
        let animationId;

        // 初始化画布
        function initCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawInodeStructure();
        }

        // 绘制索引节点结构
        function drawInodeStructure() {
            // 绘制索引节点表
            ctx.fillStyle = '#e2e8f0';
            ctx.fillRect(50, 50, 120, 300);
            ctx.strokeStyle = '#4a5568';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 50, 120, 300);

            // 绘制地址项
            const addresses = [50, 67, 68, 78, 89, 90, 91, 101];
            for (let i = 0; i < 8; i++) {
                const y = 70 + i * 35;
                
                // 地址项框
                ctx.fillStyle = i < 5 ? '#c6f6d5' : (i < 7 ? '#fed7d7' : '#fbb6ce');
                ctx.fillRect(60, y, 100, 25);
                ctx.strokeRect(60, y, 100, 25);
                
                // 地址项标签
                ctx.fillStyle = '#2d3748';
                ctx.font = '12px Arial';
                ctx.fillText(`iaddr[${i}]`, 15, y + 17);
                
                // 地址值
                ctx.fillStyle = '#1a202c';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(addresses[i].toString(), 100, y + 17);
            }

            // 添加标题
            ctx.fillStyle = '#2d3748';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('索引节点', 80, 40);
        }

        // 绘制箭头
        function drawArrow(fromX, fromY, toX, toY, color = '#667eea') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI / 6), toY - 10 * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI / 6), toY - 10 * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }

        // 绘制数据块
        function drawDataBlock(x, y, blockNum, highlight = false) {
            ctx.fillStyle = highlight ? '#ffd700' : '#f7fafc';
            ctx.fillRect(x, y, 60, 30);
            ctx.strokeStyle = highlight ? '#ff6b35' : '#4a5568';
            ctx.lineWidth = highlight ? 3 : 1;
            ctx.strokeRect(x, y, 60, 30);
            
            ctx.fillStyle = '#2d3748';
            ctx.font = 'bold 12px Arial';
            ctx.fillText(blockNum.toString(), x + 20, y + 20);
        }

        // 开始演示
        function startDemo() {
            currentStep = 1;
            updateStepIndicator();
            initCanvas();
            
            setTimeout(() => {
                showDirectAddressing();
            }, 500);
        }

        // 显示直接地址索引
        function showDirectAddressing() {
            currentStep = 2;
            updateStepIndicator();
            
            // 绘制直接地址的数据块
            const directBlocks = [50, 67, 68, 78, 89];
            for (let i = 0; i < 5; i++) {
                const x = 250 + (i % 3) * 80;
                const y = 80 + Math.floor(i / 3) * 50;
                drawDataBlock(x, y, directBlocks[i]);
                drawArrow(170, 87 + i * 35, x, y + 15);
            }
            
            // 添加说明文字
            ctx.fillStyle = '#2d3748';
            ctx.font = '14px Arial';
            ctx.fillText('直接地址索引', 250, 60);
            ctx.fillText('逻辑块 0-4', 250, 200);
        }

        // 显示逻辑块5的查找过程
        function showLogicalBlock5() {
            currentStep = 3;
            updateStepIndicator();
            initCanvas();
            showDirectAddressing();
            
            // 高亮iaddr[5]
            ctx.fillStyle = '#ff6b35';
            ctx.fillRect(60, 245, 100, 25);
            ctx.strokeStyle = '#ff6b35';
            ctx.lineWidth = 3;
            ctx.strokeRect(60, 245, 100, 25);
            
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('90', 100, 262);
            
            // 绘制一级间接索引表
            ctx.fillStyle = '#fed7d7';
            ctx.fillRect(450, 200, 80, 120);
            ctx.strokeRect(450, 200, 80, 120);
            
            // 索引表内容
            const indexTable = [58, 59, '...', 136];
            for (let i = 0; i < 4; i++) {
                const y = 220 + i * 25;
                ctx.fillStyle = '#2d3748';
                ctx.font = '12px Arial';
                ctx.fillText(indexTable[i].toString(), 470, y);
            }
            
            // 绘制箭头到索引表
            drawArrow(170, 257, 450, 250, '#ff6b35');
            
            // 高亮逻辑块5对应的物理块58
            drawDataBlock(550, 220, 58, true);
            drawArrow(530, 235, 550, 235, '#ffd700');
            
            // 添加说明
            ctx.fillStyle = '#ff6b35';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('一级间接索引表', 430, 190);
            ctx.fillText('90号物理块', 430, 340);
            
            ctx.fillStyle = '#ffd700';
            ctx.fillText('逻辑块5 → 物理块58', 450, 360);
        }

        // 显示逻辑块261的查找过程
        function showLogicalBlock261() {
            currentStep = 4;
            updateStepIndicator();
            initCanvas();
            
            // 高亮iaddr[6]
            ctx.fillStyle = '#ff6b35';
            ctx.fillRect(60, 280, 100, 25);
            ctx.strokeStyle = '#ff6b35';
            ctx.lineWidth = 3;
            ctx.strokeRect(60, 280, 100, 25);
            
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('91', 100, 297);
            
            // 绘制第二个一级间接索引表
            ctx.fillStyle = '#fed7d7';
            ctx.fillRect(250, 280, 80, 100);
            ctx.strokeRect(250, 280, 80, 100);
            
            // 索引表内容
            const indexTable2 = [187, 193, '...', 129];
            for (let i = 0; i < 4; i++) {
                const y = 300 + i * 20;
                ctx.fillStyle = '#2d3748';
                ctx.font = '12px Arial';
                ctx.fillText(indexTable2[i].toString(), 270, y);
            }
            
            // 绘制箭头
            drawArrow(170, 292, 250, 320, '#ff6b35');
            
            // 高亮逻辑块261对应的物理块187
            drawDataBlock(400, 300, 187, true);
            drawArrow(330, 305, 400, 315, '#ffd700');
            
            // 添加说明
            ctx.fillStyle = '#ff6b35';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('第二个一级间接索引表', 200, 270);
            ctx.fillText('91号物理块', 200, 390);
            
            ctx.fillStyle = '#ffd700';
            ctx.fillText('逻辑块261 → 物理块187', 350, 350);
            
            // 显示逻辑块范围
            ctx.fillStyle = '#4a5568';
            ctx.font = '12px Arial';
            ctx.fillText('逻辑块 261-516', 200, 405);
        }

        // 重置演示
        function resetDemo() {
            currentStep = 1;
            updateStepIndicator();
            initCanvas();
        }

        // 更新步骤指示器
        function updateStepIndicator() {
            for (let i = 1; i <= 4; i++) {
                const step = document.getElementById(`step${i}`);
                step.className = 'step';
                if (i < currentStep) {
                    step.className += ' completed';
                } else if (i === currentStep) {
                    step.className += ' active';
                }
            }
        }

        // 检查答案
        function checkAnswer(answer) {
            const feedback = document.getElementById('feedback');
            feedback.className = 'answer-feedback show';
            
            if (answer === 'C') {
                feedback.className += ' correct';
                feedback.innerHTML = `
                    <h3>🎉 答案正确！</h3>
                    <p><strong>解析：</strong></p>
                    <p>• 逻辑块5：通过iaddr[5]=90 → 一级间接索引表 → 物理块58</p>
                    <p>• 逻辑块261：通过iaddr[6]=91 → 一级间接索引表 → 物理块187</p>
                    <p>• 101号物理块：存放二级间接地址索引表（iaddr[7]指向）</p>
                `;
            } else {
                feedback.className += ' incorrect';
                feedback.innerHTML = `
                    <h3>❌ 答案错误</h3>
                    <p><strong>正确答案是C：58和187</strong></p>
                    <p>请重新观看演示，理解索引结构的工作原理</p>
                `;
            }
        }

        // 初始化
        window.onload = function() {
            initCanvas();
        };
    </script>
</body>
</html>
