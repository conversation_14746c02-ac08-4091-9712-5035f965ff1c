<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP地址与子网掩码学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2em;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        .question-title {
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
        }

        .ip-display {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .ip-part {
            background: white;
            color: #333;
            padding: 15px 20px;
            border-radius: 10px;
            font-size: 1.5em;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .ip-part:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        .binary-display {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #e9ecef;
        }

        .binary-row {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 15px 0;
            flex-wrap: wrap;
            gap: 10px;
        }

        .binary-bit {
            width: 40px;
            height: 40px;
            background: #6c757d;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .binary-bit.network {
            background: #28a745;
        }

        .binary-bit.subnet {
            background: #ffc107;
            color: #333;
        }

        .binary-bit.host {
            background: #dc3545;
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        canvas {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .explanation {
            background: #e3f2fd;
            border-left: 5px solid #2196f3;
            padding: 25px;
            margin: 30px 0;
            border-radius: 10px;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .step {
            background: #f1f8e9;
            border-left: 5px solid #4caf50;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            opacity: 0;
            transform: translateX(-50px);
            transition: all 0.5s ease;
        }

        .step.show {
            opacity: 1;
            transform: translateX(0);
        }

        .answer-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.2em;
        }

        .option:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-3px);
        }

        .option.correct {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .option.wrong {
            background: #ffebee;
            border-color: #f44336;
            color: #c62828;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .highlight {
            animation: pulse 1s infinite;
        }

        .result-display {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            font-size: 1.3em;
            display: none;
        }

        .result-display.show {
            display: block;
            animation: fadeInUp 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 IP地址与子网掩码学习</h1>
            <p>让我们用动画和交互来理解网络基础知识</p>
        </div>

        <div class="section">
            <div class="question-box">
                <div class="question-title">📝 题目</div>
                <p style="font-size: 1.2em; line-height: 1.6;">
                    给定IP地址为 <strong>************20</strong>，子网掩码是 <strong>*************</strong><br>
                    那么主机号是（请作答此空），该子网的广播地址是（ ）。
                </p>
                
                <div class="ip-display">
                    <div class="ip-part" id="ip1">140</div>
                    <div class="ip-part">.</div>
                    <div class="ip-part" id="ip2">252</div>
                    <div class="ip-part">.</div>
                    <div class="ip-part" id="ip3">12</div>
                    <div class="ip-part">.</div>
                    <div class="ip-part" id="ip4">120</div>
                </div>
                
                <div class="ip-display">
                    <div style="color: white; font-size: 1.2em;">子网掩码：</div>
                    <div class="ip-part" id="mask1">255</div>
                    <div class="ip-part">.</div>
                    <div class="ip-part" id="mask2">255</div>
                    <div class="ip-part">.</div>
                    <div class="ip-part" id="mask3">255</div>
                    <div class="ip-part">.</div>
                    <div class="ip-part" id="mask4">0</div>
                </div>
            </div>

            <div class="controls">
                <button class="btn" onclick="startLearning()">🚀 开始学习</button>
                <button class="btn" onclick="showBinary()">🔢 显示二进制</button>
                <button class="btn" onclick="analyzeNetwork()">🔍 分析网络</button>
                <button class="btn" onclick="showAnswer()">✅ 查看答案</button>
            </div>

            <div class="canvas-container">
                <canvas id="networkCanvas" width="800" height="400"></canvas>
            </div>

            <div id="binarySection" style="display: none;">
                <h3 style="text-align: center; margin-bottom: 30px; color: #333;">🔢 二进制表示</h3>
                <div class="binary-display">
                    <div style="text-align: center; margin-bottom: 20px; font-weight: bold;">IP地址: ************20</div>
                    <div class="binary-row" id="ipBinary"></div>
                </div>
                <div class="binary-display">
                    <div style="text-align: center; margin-bottom: 20px; font-weight: bold;">子网掩码: *************</div>
                    <div class="binary-row" id="maskBinary"></div>
                </div>
            </div>

            <div id="explanationSection" style="display: none;">
                <h3 style="text-align: center; margin-bottom: 30px; color: #333;">📚 知识解析</h3>
                
                <div class="step" id="step1">
                    <h4>🎯 第一步：识别IP地址类型</h4>
                    <p>************20 是一个B类地址，因为第一个字节140在128-191范围内。</p>
                    <p>B类地址的默认网络位是16位（前两个字节），主机位是16位（后两个字节）。</p>
                </div>

                <div class="step" id="step2">
                    <h4>🔍 第二步：分析子网掩码</h4>
                    <p>子网掩码*************表示前24位是网络位，后8位是主机位。</p>
                    <p>这意味着原来的B类地址被进一步划分了子网。</p>
                </div>

                <div class="step" id="step3">
                    <h4>⚡ 第三步：计算主机号</h4>
                    <p>主机号 = IP地址 AND (NOT 子网掩码)</p>
                    <p>************20 AND ********* = *********</p>
                </div>

                <div class="step" id="step4">
                    <h4>📡 第四步：计算广播地址</h4>
                    <p>广播地址 = 网络地址 OR 主机位全为1</p>
                    <p>网络地址：************</p>
                    <p>广播地址：**************</p>
                </div>
            </div>

            <div class="answer-options" id="answerOptions" style="display: none;">
                <h3 style="grid-column: 1/-1; text-align: center; margin-bottom: 20px; color: #333;">选择正确答案</h3>
                <div class="option" onclick="selectAnswer(this, false)">A. *********</div>
                <div class="option" onclick="selectAnswer(this, false)">B. **********</div>
                <div class="option" onclick="selectAnswer(this, false)">C. ********</div>
                <div class="option" onclick="selectAnswer(this, false)">D. ************</div>
            </div>

            <div class="result-display" id="resultDisplay">
                <h3>🎉 答案解析</h3>
                <p><strong>正确答案：A. *********</strong></p>
                <p>主机号是IP地址中用于标识具体主机的部分。</p>
                <p>广播地址是：<strong>**************</strong></p>
            </div>
        </div>
    </div>

    <script>
        let canvas, ctx;
        let animationStep = 0;
        let isAnimating = false;

        window.onload = function() {
            canvas = document.getElementById('networkCanvas');
            ctx = canvas.getContext('2d');
            drawInitialNetwork();
        };

        function drawInitialNetwork() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制网络图标
            ctx.fillStyle = '#667eea';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🌐 网络结构图', canvas.width/2, 40);
            
            // 绘制路由器
            drawRouter(400, 150);
            
            // 绘制子网
            drawSubnet(200, 250, '************/24');
            drawSubnet(600, 250, '其他子网');
            
            // 绘制主机
            drawHost(150, 320, '************20');
            drawHost(250, 320, '************');
        }

        function drawRouter(x, y) {
            ctx.fillStyle = '#28a745';
            ctx.fillRect(x-30, y-15, 60, 30);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('路由器', x, y+5);
        }

        function drawSubnet(x, y, label) {
            ctx.strokeStyle = '#ffc107';
            ctx.lineWidth = 2;
            ctx.strokeRect(x-80, y-20, 160, 40);
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y+5);
        }

        function drawHost(x, y, ip) {
            ctx.fillStyle = '#dc3545';
            ctx.fillRect(x-25, y-10, 50, 20);
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(ip, x, y+5);
        }

        function startLearning() {
            const steps = document.querySelectorAll('.step');
            let delay = 0;
            
            document.getElementById('explanationSection').style.display = 'block';
            
            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.classList.add('show');
                    animateNetworkStep(index);
                }, delay);
                delay += 1000;
            });
        }

        function animateNetworkStep(step) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawInitialNetwork();
            
            switch(step) {
                case 0:
                    // 高亮IP地址
                    highlightIPParts();
                    break;
                case 1:
                    // 显示子网掩码分析
                    showSubnetMaskAnalysis();
                    break;
                case 2:
                    // 计算主机号
                    calculateHostNumber();
                    break;
                case 3:
                    // 显示广播地址
                    showBroadcastAddress();
                    break;
            }
        }

        function highlightIPParts() {
            document.getElementById('ip1').classList.add('highlight');
            document.getElementById('ip2').classList.add('highlight');
            setTimeout(() => {
                document.getElementById('ip1').classList.remove('highlight');
                document.getElementById('ip2').classList.remove('highlight');
                document.getElementById('ip3').classList.add('highlight');
                document.getElementById('ip4').classList.add('highlight');
            }, 1000);
        }

        function showBinary() {
            document.getElementById('binarySection').style.display = 'block';
            
            // IP地址二进制
            const ipBinary = [
                '10001100', '11111100', '00001100', '01111000'
            ];
            
            // 子网掩码二进制
            const maskBinary = [
                '11111111', '11111111', '11111111', '00000000'
            ];
            
            displayBinary('ipBinary', ipBinary, ['network', 'network', 'subnet', 'host']);
            displayBinary('maskBinary', maskBinary, ['network', 'network', 'network', 'host']);
        }

        function displayBinary(containerId, binaryArray, types) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            
            binaryArray.forEach((binary, byteIndex) => {
                for(let i = 0; i < 8; i++) {
                    const bit = document.createElement('div');
                    bit.className = `binary-bit ${types[byteIndex]}`;
                    bit.textContent = binary[i];
                    container.appendChild(bit);
                    
                    setTimeout(() => {
                        bit.style.transform = 'scale(1.1)';
                        setTimeout(() => {
                            bit.style.transform = 'scale(1)';
                        }, 200);
                    }, (byteIndex * 8 + i) * 50);
                }
                
                if(byteIndex < binaryArray.length - 1) {
                    const dot = document.createElement('div');
                    dot.style.margin = '0 10px';
                    dot.style.fontSize = '20px';
                    dot.textContent = '.';
                    container.appendChild(dot);
                }
            });
        }

        function analyzeNetwork() {
            showBinary();
            setTimeout(() => {
                startLearning();
            }, 1000);
        }

        function showAnswer() {
            document.getElementById('answerOptions').style.display = 'grid';
            
            // 自动显示正确答案
            setTimeout(() => {
                const correctOption = document.querySelector('.option');
                correctOption.classList.add('correct');
                document.getElementById('resultDisplay').classList.add('show');
            }, 2000);
        }

        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if(opt === element) {
                    if(opt.textContent.includes('A. *********')) {
                        opt.classList.add('correct');
                    } else {
                        opt.classList.add('wrong');
                    }
                }
            });
            
            setTimeout(() => {
                document.getElementById('resultDisplay').classList.add('show');
            }, 1000);
        }

        function showSubnetMaskAnalysis() {
            ctx.fillStyle = '#ffc107';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('子网掩码分析:', 50, 100);
            ctx.fillText('************* = 24位网络位', 50, 120);
        }

        function calculateHostNumber() {
            ctx.fillStyle = '#dc3545';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('主机号计算:', 450, 100);
            ctx.fillText('120 = 01111000 (二进制)', 450, 120);
            ctx.fillText('主机号 = *********', 450, 140);
        }

        function showBroadcastAddress() {
            ctx.fillStyle = '#28a745';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('广播地址: **************', canvas.width/2, 380);
        }

        // 添加交互效果
        document.querySelectorAll('.ip-part').forEach(part => {
            part.addEventListener('click', function() {
                this.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 300);
            });
        });
    </script>
</body>
</html>
