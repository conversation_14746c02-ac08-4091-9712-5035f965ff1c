<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：trans-（穿越、转换）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #2c3e50, #34495e);
        }

        #portalCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .laboratory-chambers {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .portal-chamber {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .portal-chamber::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 73, 94, 0.1), transparent);
            transition: left 0.6s;
        }

        .portal-chamber:hover::before {
            left: 100%;
        }

        .portal-chamber:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .portal-chamber.activated {
            opacity: 1;
            transform: translateY(0);
        }

        .transformation-portal {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #ecf0f1, #bdc3c7);
            border-radius: 10px;
            position: relative;
        }

        .origin-state {
            background: #3498db;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .origin-state::after {
            content: '原始';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #2980b9;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .portal-gate {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: radial-gradient(circle, #9b59b6, #8e44ad, #663399);
            position: relative;
            margin: 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: portalSpin 3s linear infinite;
            box-shadow: 0 0 20px rgba(155, 89, 182, 0.6);
        }

        .portal-gate::before {
            content: '🌀';
            font-size: 2rem;
            animation: portalSpin 2s linear infinite reverse;
        }

        .portal-gate::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            border: 3px dashed rgba(155, 89, 182, 0.4);
            border-radius: 50%;
            animation: portalSpin 4s linear infinite;
        }

        .transformed-state {
            background: #e74c3c;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .transformed-state::after {
            content: '转换';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #c0392b;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .prefix-highlight {
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .portal-explanation {
            background: rgba(155, 89, 182, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .experiment-log {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #f39c12;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #f39c12;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .portal-status {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #95a5a6;
            transition: all 0.3s ease;
        }

        .portal-status.charging {
            background: #f39c12;
            animation: energyPulse 1.5s infinite;
        }

        .portal-status.active {
            background: #e74c3c;
            box-shadow: 0 0 10px #e74c3c;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes portalSpin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes energyPulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.3);
            }
        }

        @keyframes dimensionalRift {
            0%, 100% {
                opacity: 0.3;
                transform: scaleX(1);
            }
            50% {
                opacity: 0.8;
                transform: scaleX(1.5);
            }
        }

        @keyframes particleFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        .interactive-hint {
            text-align: center;
            color: #9b59b6;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .dimensional-particles {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #9b59b6;
            border-radius: 50%;
            pointer-events: none;
            animation: particleFloat 3s infinite;
        }

        .portal-sequence {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 15px;
        }

        .sequence-gate {
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background: radial-gradient(circle, #bdc3c7, #95a5a6);
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(155, 89, 182, 0.3);
        }

        .sequence-gate.active {
            background: radial-gradient(circle, #9b59b6, #8e44ad);
            transform: scale(1.3);
            box-shadow: 0 4px 15px rgba(155, 89, 182, 0.6);
            animation: portalSpin 2s linear infinite;
        }

        .sequence-gate.completed {
            background: radial-gradient(circle, #e74c3c, #c0392b);
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .sequence-gate::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #9b59b6, transparent);
            transform: translateY(-50%);
        }

        .sequence-gate:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>穿越前缀：trans-</h1>
            <p>在时空传送门实验室中学会"跨越转换"的奥秘</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🌀 时空传送门实验室的故事</h2>
                <p>在一个神秘的科学实验室里，科学家们发明了神奇的时空传送门技术。这些传送门拥有"trans-"能量核心，能够让任何事物穿越空间、跨越界限或发生根本性转换。当普通的词汇通过这些传送门时，就会获得"穿越"、"转换"、"跨越"的神奇能力，从一种状态完全转变为另一种状态！</p>
            </div>

            <div class="canvas-container">
                <canvas id="portalCanvas"></canvas>
                <div class="portal-sequence" id="portalSequence">
                    <div class="sequence-gate"></div>
                    <div class="sequence-gate"></div>
                    <div class="sequence-gate"></div>
                    <div class="sequence-gate"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择时空传送门实验室的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"时空传送门实验室"的比喻，是因为"trans-"前缀的核心含义就是"穿越"、"转换"、"跨越"，这与传送门让物体从一个地方瞬间转移到另一个地方的功能完美契合。传送门的视觉效果帮助学生理解"跨越界限"、"状态转换"的概念，而科学实验室的设定强调了这种转换的神奇和精确性。通过传送门的穿越过程，让抽象的"转换"概念变得生动有趣。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="activatePortals()">激活传送门</button>
                <button class="btn" onclick="showChambers()">显示实验室</button>
                <button class="btn" onclick="resetLaboratory()">重置实验室</button>
            </div>

            <div class="interactive-hint">
                🔬 点击"激活传送门"观看词汇穿越过程，点击实验室查看实验日志
            </div>
        </div>

        <div class="laboratory-chambers" id="laboratoryChambers">
            <div class="portal-chamber">
                <div class="portal-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Port → Transport</h3>
                <div class="transformation-portal">
                    <div class="origin-state">port</div>
                    <div class="portal-gate"></div>
                    <div class="transformed-state"><span class="prefix-highlight">trans</span>port</div>
                </div>
                <div class="portal-explanation">
                    携带 → <span class="prefix-highlight">跨越</span>携带
                </div>
                <div class="experiment-log">
                    <strong>实验日志：</strong><br>
                    <strong>原始：</strong>The port is busy. (港口很繁忙。)<br>
                    <strong>转换：</strong>We need to transport goods. (我们需要运输货物。)<br>
                    <strong>解析：</strong>"port"表示携带、港口，加上"trans-"变成"transport"，表示运输、运送。从静态的携带转换为跨越空间的运输。
                </div>
            </div>

            <div class="portal-chamber">
                <div class="portal-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Form → Transform</h3>
                <div class="transformation-portal">
                    <div class="origin-state">form</div>
                    <div class="portal-gate"></div>
                    <div class="transformed-state"><span class="prefix-highlight">trans</span>form</div>
                </div>
                <div class="portal-explanation">
                    形状 → <span class="prefix-highlight">转换</span>形状
                </div>
                <div class="experiment-log">
                    <strong>实验日志：</strong><br>
                    <strong>原始：</strong>The form is complete. (表格完成了。)<br>
                    <strong>转换：</strong>The robot can transform. (机器人可以变形。)<br>
                    <strong>解析：</strong>"form"表示形状、形式，加上"trans-"变成"transform"，表示变形、转换。从固定形状转换为可变形状。
                </div>
            </div>

            <div class="portal-chamber">
                <div class="portal-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Late → Translate</h3>
                <div class="transformation-portal">
                    <div class="origin-state">late</div>
                    <div class="portal-gate"></div>
                    <div class="transformed-state"><span class="prefix-highlight">trans</span>late</div>
                </div>
                <div class="portal-explanation">
                    携带 → <span class="prefix-highlight">跨越</span>语言
                </div>
                <div class="experiment-log">
                    <strong>实验日志：</strong><br>
                    <strong>原始：</strong>Don't be late. (不要迟到。)<br>
                    <strong>转换：</strong>Please translate this text. (请翻译这段文字。)<br>
                    <strong>解析：</strong>"late"词根表示携带，加上"trans-"变成"translate"，表示翻译。从一种语言跨越转换到另一种语言。
                </div>
            </div>

            <div class="portal-chamber">
                <div class="portal-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Parent → Transparent</h3>
                <div class="transformation-portal">
                    <div class="origin-state">parent</div>
                    <div class="portal-gate"></div>
                    <div class="transformed-state"><span class="prefix-highlight">trans</span>parent</div>
                </div>
                <div class="portal-explanation">
                    显现 → <span class="prefix-highlight">穿透</span>显现
                </div>
                <div class="experiment-log">
                    <strong>实验日志：</strong><br>
                    <strong>原始：</strong>Ask your parent. (问你的父母。)<br>
                    <strong>转换：</strong>The glass is transparent. (玻璃是透明的。)<br>
                    <strong>解析：</strong>"parent"词根表示显现，加上"trans-"变成"transparent"，表示透明的。光线可以穿越通过，完全显现。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"trans-"前缀表示穿越、跨越、转换、改变状态的含义。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"trans-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"trans-"后的词根基本含义</li>
                <li><strong>应用转换概念：</strong>在词根意思前加上"跨越"、"转换"、"穿越"</li>
                <li><strong>状态调整：</strong>强调从一种状态转变为另一种状态</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象时空传送门的穿越过程，"trans-"就像传送门，让词汇从一种状态跨越到另一种状态！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('portalCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentPortal = 0;
        let energyParticles = [];
        let dimensionalRifts = [];
        let portalRotation = 0;
        
        const portals = [
            { from: 'port', to: 'transport', x: 150, y: 200 },
            { from: 'form', to: 'transform', x: 350, y: 300 },
            { from: 'late', to: 'translate', x: 550, y: 150 },
            { from: 'parent', to: 'transparent', x: 750, y: 250 }
        ];

        class EnergyParticle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 3;
                this.vy = (Math.random() - 0.5) * 3;
                this.life = 1;
                this.decay = 0.02;
                this.size = Math.random() * 4 + 2;
                this.color = `hsl(${280 + Math.random() * 40}, 70%, 60%)`;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.size *= 0.99;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        class DimensionalRift {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.width = 0;
                this.maxWidth = 100;
                this.height = 4;
                this.expanding = true;
                this.life = 1;
            }

            update() {
                if (this.expanding) {
                    this.width += 3;
                    if (this.width >= this.maxWidth) {
                        this.expanding = false;
                    }
                } else {
                    this.life -= 0.02;
                }
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                const gradient = ctx.createLinearGradient(this.x - this.width/2, this.y, this.x + this.width/2, this.y);
                gradient.addColorStop(0, 'transparent');
                gradient.addColorStop(0.5, '#9b59b6');
                gradient.addColorStop(1, 'transparent');
                ctx.fillStyle = gradient;
                ctx.fillRect(this.x - this.width/2, this.y - this.height/2, this.width, this.height);
                ctx.restore();
            }
        }

        function drawMainPortal() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 主传送门
            ctx.save();
            ctx.translate(centerX, centerY);
            ctx.rotate(portalRotation);
            
            // 外环
            const outerGradient = ctx.createRadialGradient(0, 0, 40, 0, 0, 80);
            outerGradient.addColorStop(0, 'transparent');
            outerGradient.addColorStop(0.7, 'rgba(155, 89, 182, 0.3)');
            outerGradient.addColorStop(1, 'rgba(155, 89, 182, 0.8)');
            ctx.fillStyle = outerGradient;
            ctx.beginPath();
            ctx.arc(0, 0, 80, 0, Math.PI * 2);
            ctx.fill();
            
            // 内环
            const innerGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 50);
            innerGradient.addColorStop(0, 'rgba(142, 68, 173, 0.8)');
            innerGradient.addColorStop(0.5, 'rgba(155, 89, 182, 0.6)');
            innerGradient.addColorStop(1, 'transparent');
            ctx.fillStyle = innerGradient;
            ctx.beginPath();
            ctx.arc(0, 0, 50, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
            
            if (animationState === 'activating') {
                portalRotation += 0.05;
            }
        }

        function drawWordTransformation() {
            if (currentPortal < portals.length && animationState === 'activating') {
                const portal = portals[currentPortal];
                const centerX = canvas.width / 2;
                
                // 原始词汇（左侧）
                ctx.fillStyle = '#3498db';
                ctx.fillRect(centerX - 250, portal.y, 100, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(portal.from, centerX - 200, portal.y + 25);
                
                // 传送光束
                ctx.strokeStyle = '#9b59b6';
                ctx.lineWidth = 4;
                ctx.setLineDash([10, 5]);
                ctx.beginPath();
                ctx.moveTo(centerX - 130, portal.y + 20);
                ctx.lineTo(centerX + 130, portal.y + 20);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // 转换后词汇（右侧）
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(centerX + 150, portal.y, 120, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                
                // 高亮trans-前缀
                ctx.fillStyle = '#f39c12';
                ctx.fillText('trans', centerX + 185, portal.y + 25);
                ctx.fillStyle = 'white';
                ctx.fillText(portal.from, centerX + 225, portal.y + 25);
            }
        }

        function createEnergyEffect(x, y) {
            for (let i = 0; i < 12; i++) {
                energyParticles.push(new EnergyParticle(x, y));
            }
        }

        function createDimensionalRift(x, y) {
            dimensionalRifts.push(new DimensionalRift(x, y));
        }

        function updateEffects() {
            // 更新能量粒子
            energyParticles = energyParticles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
            
            // 更新次元裂缝
            dimensionalRifts = dimensionalRifts.filter(rift => {
                rift.update();
                rift.draw();
                return rift.life > 0;
            });
        }

        function updatePortalSequence() {
            const gates = document.querySelectorAll('.sequence-gate');
            gates.forEach((gate, index) => {
                gate.classList.remove('active', 'completed');
                if (index < currentPortal) {
                    gate.classList.add('completed');
                } else if (index === currentPortal && animationState === 'activating') {
                    gate.classList.add('active');
                }
            });
        }

        function updateChamberStatus() {
            const chambers = document.querySelectorAll('.portal-chamber');
            const statuses = document.querySelectorAll('.portal-status');
            
            chambers.forEach((chamber, index) => {
                const status = statuses[index];
                if (index < currentPortal) {
                    status.classList.remove('charging');
                    status.classList.add('active');
                } else if (index === currentPortal && animationState === 'activating') {
                    status.classList.add('charging');
                    status.classList.remove('active');
                } else {
                    status.classList.remove('charging', 'active');
                }
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const bgGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            bgGradient.addColorStop(0, '#2c3e50');
            bgGradient.addColorStop(1, '#34495e');
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制主传送门
            drawMainPortal();
            
            // 绘制词汇转换过程
            drawWordTransformation();
            
            // 更新特效
            updateEffects();
            
            // 更新界面状态
            updatePortalSequence();
            updateChamberStatus();
        }

        function animate() {
            drawScene();
            
            if (animationState === 'activating' && currentPortal < portals.length) {
                // 创建能量特效
                if (Math.random() < 0.15) {
                    createEnergyEffect(canvas.width / 2 + (Math.random() - 0.5) * 160, 
                                     canvas.height / 2 + (Math.random() - 0.5) * 120);
                }
                
                // 创建次元裂缝
                if (Math.random() < 0.08) {
                    createDimensionalRift(canvas.width / 2, canvas.height / 2 + (Math.random() - 0.5) * 200);
                }
                
                // 自动切换到下一个传送门
                setTimeout(() => {
                    currentPortal++;
                    if (currentPortal >= portals.length) {
                        animationState = 'completed';
                    }
                }, 3000);
            }
            
            requestAnimationFrame(animate);
        }

        function activatePortals() {
            animationState = 'activating';
            currentPortal = 0;
            portalRotation = 0;
            energyParticles = [];
            dimensionalRifts = [];
        }

        function showChambers() {
            const chambers = document.querySelectorAll('.portal-chamber');
            chambers.forEach((chamber, index) => {
                setTimeout(() => {
                    chamber.classList.add('activated');
                }, index * 400);
            });
        }

        function resetLaboratory() {
            animationState = 'idle';
            currentPortal = 0;
            portalRotation = 0;
            energyParticles = [];
            dimensionalRifts = [];
            
            const chambers = document.querySelectorAll('.portal-chamber');
            chambers.forEach(chamber => chamber.classList.remove('activated'));
            
            const statuses = document.querySelectorAll('.portal-status');
            statuses.forEach(status => {
                status.classList.remove('charging', 'active');
            });
        }

        // 初始化
        animate();

        // 点击实验室的交互
        document.querySelectorAll('.portal-chamber').forEach(chamber => {
            chamber.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
