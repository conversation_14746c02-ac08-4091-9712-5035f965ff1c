<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件系统工具知识学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f7f6;
            color: #333;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
            box-sizing: border-box;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .question-section {
            margin-bottom: 25px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        .question-text {
            font-size: 1.3em;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .options {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .option-item {
            background-color: #e8f5e9;
            border: 1px solid #a5d6a7;
            border-radius: 8px;
            padding: 12px 18px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1em;
            display: flex;
            align-items: center;
        }
        .option-item:hover {
            background-color: #dcedc8;
            border-color: #8bc34a;
            transform: translateY(-2px);
        }
        .option-item.selected {
            background-color: #c8e6c9;
            border-color: #4caf50;
            box-shadow: 0 0 0 2px #4caf50;
        }
        .option-item.correct {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
            font-weight: bold;
        }
        .option-item.incorrect {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
            text-decoration: line-through;
        }
        .option-prefix {
            font-weight: bold;
            margin-right: 10px;
            color: #4CAF50;
        }
        .answer-explanation {
            margin-top: 25px;
            padding: 20px;
            background-color: #e0f7fa;
            border-left: 5px solid #00bcd4;
            border-radius: 8px;
            line-height: 1.7;
            font-size: 1.1em;
            display: none; /* 初始隐藏 */
        }
        .answer-explanation h3 {
            color: #00838f;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .answer-explanation p {
            margin-bottom: 10px;
        }
        .highlight {
            font-weight: bold;
            color: #e67e22;
        }
        .highlight-blue {
            font-weight: bold;
            color: #3498db;
        }
        .interactive-section {
            margin-top: 30px;
            padding: 25px;
            background-color: #f0f4f8;
            border-radius: 10px;
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.05);
        }
        .interactive-section h3 {
            color: #34495e;
            margin-bottom: 20px;
            text-align: center;
        }
        .animation-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        .animation-box {
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            width: 250px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease-in-out;
            cursor: pointer;
        }
        .animation-box:hover {
            transform: translateY(-5px);
        }
        .animation-box h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .animation-box p {
            font-size: 0.95em;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 1em;
            transition: background-color 0.3s ease;
            margin-top: 15px;
            cursor: pointer;
            border: none;
        }
        .button:hover {
            background-color: #2980b9;
        }
        #canvas-section {
            margin-top: 30px;
            text-align: center;
            background-color: #fdfdfd;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        #canvas-section h3 {
            color: #2980b9;
            margin-bottom: 20px;
        }
        #versionControlCanvas {
            border: 1px solid #ccc;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
        }
        .game-section {
            margin-top: 30px;
            padding: 25px;
            background-color: #fff3e0;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            text-align: center;
        }
        .game-section h3 {
            color: #e67e22;
            margin-bottom: 20px;
        }
        .game-area {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            flex-wrap: wrap;
            gap: 20px;
        }
        .category-drop-target {
            border: 2px dashed #95a5a6;
            background-color: #f5f5f5;
            padding: 20px;
            min-width: 200px;
            min-height: 100px;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #7f8c8d;
            transition: all 0.3s ease;
        }
        .category-drop-target.hover {
            border-color: #3498db;
            background-color: #eaf3f8;
        }
        .draggable-tool {
            background-color: #f1c40f;
            color: #fff;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 5px;
            cursor: grab;
            display: inline-block;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: transform 0.2s ease;
        }
        .draggable-tool.dragging {
            opacity: 0.7;
            transform: scale(1.05);
        }
        .feedback {
            margin-top: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        .feedback.correct {
            color: #27ae60;
        }
        .feedback.incorrect {
            color: #c0392b;
        }
        .reset-button {
            background-color: #e74c3c;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件系统工具知识学习</h1>

        <div class="question-section">
            <p class="question-text">在软件系统工具中，版本控制工具属于（），软件评价工具属于（）。</p>
            <ul class="options" id="options-list">
                <li class="option-item" data-value="A">
                    <span class="option-prefix">A.</span> 软件开发工具
                </li>
                <li class="option-item" data-value="B">
                    <span class="option-prefix">B.</span> 软件维护工具
                </li>
                <li class="option-item" data-value="C">
                    <span class="option-prefix">C.</span> 编码与排错工具
                </li>
                <li class="option-item" data-value="D">
                    <span class="option-prefix">D.</span> 软件管理和软件支持工具
                </li>
            </ul>
        </div>

        <div class="answer-explanation" id="answer-explanation">
            <h3>解析</h3>
            <p>本题考查软件开发过程管理和工具基础知识。</p>
            <p><span class="highlight">版本控制工具</span>属于 <span class="highlight-blue">软件维护工具</span>，其主要目的是跟踪和管理软件代码的变化，这在软件的整个生命周期（包括维护阶段）都至关重要，它帮助团队协作，回溯历史版本，解决冲突。</p>
            <p><span class="highlight">软件评价工具</span>属于 <span class="highlight-blue">软件管理与软件支持工具</span>。这类工具用于评估软件的质量、性能、安全性等方面，为软件的管理决策和支持服务提供数据和依据。例如，测试工具、性能分析工具等。</p>
            <button class="button" id="show-interactive-btn">进入互动学习</button>
        </div>
    </div>

    <div class="container interactive-section" style="display:none;" id="interactive-content">
        <h3>互动演示：理解工具分类</h3>
        <div class="animation-container">
            <div class="animation-box" id="version-control-anim">
                <h4>版本控制工具 (Git/SVN)</h4>
                <p>跟踪代码历史，协作开发，回溯版本。</p>
                <button class="button animate-btn" data-target="versionControlCanvas" data-animation="versionControl">演示版本控制</button>
            </div>
            <div class="animation-box" id="software-evaluation-anim">
                <h4>软件评价工具 (JUnit/SonarQube)</h4>
                <p>评估软件质量、性能、安全性，提供报告。</p>
                <button class="button animate-btn" data-target="evaluationCanvas" data-animation="softwareEvaluation">演示软件评价</button>
            </div>
        </div>

        <div id="canvas-section">
            <h3>Canvas 演示区</h3>
            <canvas id="mainCanvas" width="800" height="400" style="background-color: #eef;"></canvas>
            <p id="canvas-status" style="color: #555; margin-top: 10px;">点击上方按钮开始演示。</p>
        </div>
    </div>

    <div class="container game-section" style="display:none;" id="game-content">
        <h3>小游戏：工具分类挑战！</h3>
        <p>将左侧的工具拖拽到右侧对应的分类中。</p>
        <div class="game-area">
            <div class="tools-to-drag">
                <div class="draggable-tool" draggable="true" data-category="维护工具">Git</div>
                <div class="draggable-tool" draggable="true" data-category="管理与支持工具">Jira</div>
                <div class="draggable-tool" draggable="true" data-category="维护工具">SVN</div>
                <div class="draggable-tool" draggable="true" data-category="开发工具">IDE (如VS Code)</div>
                <div class="draggable-tool" draggable="true" data-category="管理与支持工具">JUnit</div>
                <div class="draggable-tool" draggable="true" data-category="管理与支持工具">Bugzilla</div>
                <div class="draggable-tool" draggable="true" data-category="开发工具">编译器</div>
                <div class="draggable-tool" draggable="true" data-category="维护工具">Jenkins (CI/CD)</div>
            </div>
            <div class="drop-targets">
                <div class="category-drop-target" data-accept="维护工具">
                    软件维护工具
                </div>
                <div class="category-drop-target" data-accept="管理与支持工具">
                    软件管理和软件支持工具
                </div>
                <div class="category-drop-target" data-accept="开发工具">
                    软件开发工具
                </div>
            </div>
        </div>
        <div class="feedback" id="game-feedback"></div>
        <button class="button reset-button" id="reset-game-btn" style="display:none;">重置游戏</button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const optionsList = document.getElementById('options-list');
            const answerExplanation = document.getElementById('answer-explanation');
            const showInteractiveBtn = document.getElementById('show-interactive-btn');
            const interactiveContent = document.getElementById('interactive-content');
            const gameContent = document.getElementById('game-content');
            const animateButtons = document.querySelectorAll('.animate-btn');
            const mainCanvas = document.getElementById('mainCanvas');
            const ctx = mainCanvas.getContext('2d');
            const canvasStatus = document.getElementById('canvas-status');

            // 题目选项点击逻辑
            optionsList.addEventListener('click', (event) => {
                const selectedOption = event.target.closest('.option-item');
                if (selectedOption) {
                    // 移除之前选中的样式
                    optionsList.querySelectorAll('.option-item').forEach(item => {
                        item.classList.remove('selected', 'correct', 'incorrect');
                    });

                    selectedOption.classList.add('selected');

                    // 显示解析
                    answerExplanation.style.display = 'block';

                    // 判断对错并添加样式
                    if (selectedOption.dataset.value === 'B') {
                        selectedOption.classList.add('correct');
                        alert('恭喜你，回答正确！现在我们来深入学习一下。');
                    } else {
                        selectedOption.classList.add('incorrect');
                        // 找到正确答案并标记
                        optionsList.querySelector('[data-value="B"]').classList.add('correct');
                        alert('很遗憾，回答错误。正确答案是B。让我们看看详细解析。');
                    }

                    // 滚动到解析区域
                    answerExplanation.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });

            // 显示互动学习按钮点击逻辑
            showInteractiveBtn.addEventListener('click', () => {
                interactiveContent.style.display = 'block';
                gameContent.style.display = 'block'; // 也显示游戏区
                interactiveContent.scrollIntoView({ behavior: 'smooth', block: 'start' });
            });

            // Canvas 动画逻辑
            const drawBranch = (startX, startY, endX, endY, color, text) => {
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.stroke();
                ctx.fillStyle = color;
                ctx.font = '14px Arial';
                ctx.fillText(text, (startX + endX) / 2 + 5, (startY + endY) / 2 - 5);
            };

            const drawCommit = (x, y, radius, color, text) => {
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, Math.PI * 2);
                ctx.fillStyle = color;
                ctx.fill();
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 1;
                ctx.stroke();
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(text, x, y);
            };

            const animateVersionControl = () => {
                ctx.clearRect(0, 0, mainCanvas.width, mainCanvas.height);
                canvasStatus.textContent = '版本控制演示：主分支与新功能分支的开发与合并。';

                let x = 100, y = 50;
                const commitRadius = 15;
                const step = 80;

                // 主分支
                drawCommit(x, y, commitRadius, 'green', '初始');
                drawCommit(x + step, y, commitRadius, 'green', '提交1');
                drawBranch(x + commitRadius, y, x + step - commitRadius, y, 'green', '主分支');

                // 新功能分支
                drawCommit(x + step, y, commitRadius, 'green', '提交1'); // 共享提交点
                drawCommit(x + step * 1.5, y + step, commitRadius, 'blue', '特性1');
                drawCommit(x + step * 2.5, y + step, commitRadius, 'blue', '特性2');
                drawBranch(x + step + commitRadius, y, x + step * 1.5 - commitRadius, y + step, 'gray', '分支');
                drawBranch(x + step * 1.5 + commitRadius, y + step, x + step * 2.5 - commitRadius, y + step, 'blue', '新功能');

                // 合并到主分支
                drawCommit(x + step * 3.5, y, commitRadius, 'purple', '合并');
                drawBranch(x + step + commitRadius, y, x + step * 3.5 - commitRadius, y, 'green', '主分支'); // 继续主分支
                drawBranch(x + step * 2.5 + commitRadius, y + step, x + step * 3.5 - commitRadius, y, 'red', '合并新功能');

                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('Git/SVN等工具帮助我们管理代码的不同版本，就像记录每一次修改并能随时查看或回到过去的某个状态。', 50, mainCanvas.height - 30);
                ctx.fillText('它允许多人同时开发，并通过分支和合并将各自的工作整合起来，确保代码协同和项目的顺利进行。', 50, mainCanvas.height - 10);
            };

            const animateSoftwareEvaluation = () => {
                ctx.clearRect(0, 0, mainCanvas.width, mainCanvas.height);
                canvasStatus.textContent = '软件评价演示：从需求到报告的质量评估过程。';

                // 绘制流程图
                let y = 80;
                let xStart = 100;
                let boxWidth = 120;
                let boxHeight = 50;
                let arrowOffset = 20;

                const drawBox = (x, y, text, color) => {
                    ctx.fillStyle = color;
                    ctx.fillRect(x, y, boxWidth, boxHeight);
                    ctx.strokeStyle = '#333';
                    ctx.strokeRect(x, y, boxWidth, boxHeight);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(text, x + boxWidth / 2, y + boxHeight / 2);
                };

                const drawArrow = (fromX, fromY, toX, toY) => {
                    ctx.beginPath();
                    ctx.moveTo(fromX, fromY);
                    ctx.lineTo(toX, toY);
                    ctx.strokeStyle = '#555';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    // Arrowhead
                    ctx.beginPath();
                    ctx.moveTo(toX, toY);
                    ctx.lineTo(toX - 8, toY - 5);
                    ctx.lineTo(toX - 8, toY + 5);
                    ctx.closePath();
                    ctx.fillStyle = '#555';
                    ctx.fill();
                };

                drawBox(xStart, y, '需求分析', '#3498db');
                drawArrow(xStart + boxWidth, y + boxHeight / 2, xStart + boxWidth + arrowOffset, y + boxHeight / 2);

                drawBox(xStart + boxWidth + arrowOffset * 2, y, '设计与编码', '#2ecc71');
                drawArrow(xStart + boxWidth * 2 + arrowOffset * 2, y + boxHeight / 2, xStart + boxWidth * 2 + arrowOffset * 3, y + boxHeight / 2);

                drawBox(xStart + boxWidth * 2 + arrowOffset * 4, y, '测试与评估', '#e67e22');
                drawArrow(xStart + boxWidth * 3 + arrowOffset * 4, y + boxHeight / 2, xStart + boxWidth * 3 + arrowOffset * 5, y + boxHeight / 2);

                drawBox(xStart + boxWidth * 3 + arrowOffset * 6, y, '质量报告', '#9b59b6');

                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('软件评价工具帮助我们检查软件是否达到了预期的质量标准。', 50, mainCanvas.height - 50);
                ctx.fillText('这包括功能测试、性能测试、安全测试等，最终生成报告指导改进。', 50, mainCanvas.height - 30);
            };

            animateButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const animationType = button.dataset.animation;
                    if (animationType === 'versionControl') {
                        animateVersionControl();
                    } else if (animationType === 'softwareEvaluation') {
                        animateSoftwareEvaluation();
                    }
                });
            });

            // 游戏逻辑 (拖拽分类)
            const draggables = document.querySelectorAll('.draggable-tool');
            const dropTargets = document.querySelectorAll('.category-drop-target');
            const gameFeedback = document.getElementById('game-feedback');
            const resetGameBtn = document.getElementById('reset-game-btn');
            let draggedItem = null;
            let correctDrops = 0;
            const totalDraggables = draggables.length;

            draggables.forEach(draggable => {
                draggable.addEventListener('dragstart', (e) => {
                    draggedItem = draggable;
                    setTimeout(() => draggable.classList.add('dragging'), 0);
                });

                draggable.addEventListener('dragend', () => {
                    draggedItem.classList.remove('dragging');
                    draggedItem = null;
                });
            });

            dropTargets.forEach(target => {
                target.addEventListener('dragover', (e) => {
                    e.preventDefault(); // 允许放置
                    target.classList.add('hover');
                });

                target.addEventListener('dragleave', () => {
                    target.classList.remove('hover');
                });

                target.addEventListener('drop', (e) => {
                    e.preventDefault();
                    target.classList.remove('hover');

                    if (draggedItem && target.dataset.accept === draggedItem.dataset.category) {
                        target.appendChild(draggedItem);
                        draggedItem.style.backgroundColor = '#27ae60'; // 正确放置变为绿色
                        draggedItem.draggable = false; // 放置后不可再拖拽
                        gameFeedback.textContent = '太棒了！放置正确！';
                        gameFeedback.classList.remove('incorrect');
                        gameFeedback.classList.add('correct');
                        correctDrops++;
                        if (correctDrops === totalDraggables) {
                            gameFeedback.textContent = '恭喜你！所有工具都分类正确！你真棒！';
                            resetGameBtn.style.display = 'block';
                        }
                    } else {
                        gameFeedback.textContent = '不对哦，再想想看！';
                        gameFeedback.classList.remove('correct');
                        gameFeedback.classList.add('incorrect');
                    }
                });
            });

            resetGameBtn.addEventListener('click', () => {
                const toolsContainer = document.querySelector('.tools-to-drag');
                draggables.forEach(tool => {
                    toolsContainer.appendChild(tool); // 将工具放回初始位置
                    tool.style.backgroundColor = '#f1c40f'; // 恢复初始颜色
                    tool.draggable = true; // 恢复可拖拽
                });
                gameFeedback.textContent = '';
                gameFeedback.classList.remove('correct', 'incorrect');
                resetGameBtn.style.display = 'none';
                correctDrops = 0;
            });
        });
    </script>
</body>
</html> 