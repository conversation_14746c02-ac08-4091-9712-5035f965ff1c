<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>索引是什么 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-50px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #555;
            text-align: center;
            margin: 20px 0;
        }

        .interactive-btn {
            display: block;
            margin: 20px auto;
            padding: 15px 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .game-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .score {
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 20px;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">索引是什么？</h1>

        <div class="section">
            <h2 class="section-title">📚 什么是索引？</h2>
            <div class="canvas-container">
                <canvas id="indexCanvas" width="600" height="300"></canvas>
            </div>
            <p class="explanation">
                索引就像书本的目录，帮助我们快速找到想要的内容！<br>
                点击上方画布，看看索引是如何工作的
            </p>
            <button class="interactive-btn" onclick="startIndexDemo()">开始演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">💾 索引存储在磁盘中</h2>
            <div class="canvas-container">
                <canvas id="storageCanvas" width="600" height="300"></canvas>
            </div>
            <div class="highlight">
                <p class="explanation">
                    索引像一个特殊的文件夹，存储在电脑的硬盘里<br>
                    它记录着数据的位置信息，让查找变得超级快！
                </p>
            </div>
            <button class="interactive-btn" onclick="startStorageDemo()">查看存储演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">🚀 提高查询效率</h2>
            <div class="canvas-container">
                <canvas id="efficiencyCanvas" width="600" height="300"></canvas>
            </div>
            <div class="game-area">
                <div class="score">得分: <span id="score">0</span></div>
                <p class="explanation">
                    体验查找游戏：有索引 vs 没有索引的速度对比
                </p>
                <button class="interactive-btn" onclick="startSpeedGame()">开始速度挑战</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚡ 索引的优缺点</h2>
            <div class="canvas-container">
                <canvas id="prosConsCanvas" width="600" height="400"></canvas>
            </div>
            <div class="highlight">
                <p class="explanation">
                    <strong>优点：</strong>查询速度快如闪电⚡<br>
                    <strong>缺点：</strong>需要额外存储空间，更新时需要维护
                </p>
            </div>
            <button class="interactive-btn" onclick="startProsConsDemo()">查看详细对比</button>
        </div>
    </div>

    <script>
        let score = 0;
        let gameActive = false;

        // 索引演示动画
        function startIndexDemo() {
            const canvas = document.getElementById('indexCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制书本
            drawBook(ctx, 100, 100);
            
            // 绘制目录页面
            setTimeout(() => {
                drawIndex(ctx, 350, 50);
                animateSearch(ctx);
            }, 1000);
        }

        function drawBook(ctx, x, y) {
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(x, y, 120, 150);
            
            ctx.fillStyle = '#FFF';
            ctx.font = '16px Arial';
            ctx.fillText('数据库', x + 30, y + 80);
            
            // 书页动画
            let pageOffset = 0;
            const animatePages = () => {
                ctx.clearRect(x + 120, y, 20, 150);
                ctx.fillStyle = '#F0F0F0';
                for(let i = 0; i < 5; i++) {
                    ctx.fillRect(x + 120 + i * 2 + pageOffset, y + i * 2, 15, 150 - i * 4);
                }
                pageOffset = (pageOffset + 0.5) % 10;
                requestAnimationFrame(animatePages);
            };
            animatePages();
        }

        function drawIndex(ctx, x, y) {
            ctx.fillStyle = '#E6F3FF';
            ctx.fillRect(x, y, 200, 200);

            ctx.strokeStyle = '#4A90E2';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 200, 200);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('索引目录', x + 70, y + 30);

            const items = ['用户A → 第1页', '用户B → 第5页', '用户C → 第8页'];
            items.forEach((item, i) => {
                ctx.fillText(item, x + 20, y + 70 + i * 30);
            });
        }

        function animateSearch(ctx) {
            let step = 0;
            const animate = () => {
                if (step < 100) {
                    // 绘制搜索光标
                    ctx.fillStyle = '#FF6B6B';
                    ctx.beginPath();
                    ctx.arc(350 + step * 2, 120, 5, 0, 2 * Math.PI);
                    ctx.fill();

                    // 绘制连接线
                    if (step > 50) {
                        ctx.strokeStyle = '#FF6B6B';
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(450, 120);
                        ctx.lineTo(100 + (step - 50) * 2, 175);
                        ctx.stroke();
                    }

                    step++;
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 存储演示
        function startStorageDemo() {
            const canvas = document.getElementById('storageCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制硬盘
            drawHardDisk(ctx, 50, 100);

            // 绘制索引文件
            setTimeout(() => {
                drawIndexFile(ctx, 250, 80);
                animateDataFlow(ctx);
            }, 1000);
        }

        function drawHardDisk(ctx, x, y) {
            // 硬盘外壳
            ctx.fillStyle = '#2C3E50';
            ctx.fillRect(x, y, 150, 100);

            // 硬盘标签
            ctx.fillStyle = '#ECF0F1';
            ctx.fillRect(x + 10, y + 10, 130, 20);
            ctx.fillStyle = '#2C3E50';
            ctx.font = '12px Arial';
            ctx.fillText('硬盘存储', x + 50, y + 25);

            // 旋转的磁盘动画
            let rotation = 0;
            const animateDisk = () => {
                ctx.save();
                ctx.translate(x + 75, y + 65);
                ctx.rotate(rotation);

                ctx.fillStyle = '#34495E';
                ctx.beginPath();
                ctx.arc(0, 0, 25, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = '#ECF0F1';
                ctx.beginPath();
                ctx.arc(0, 0, 5, 0, 2 * Math.PI);
                ctx.fill();

                ctx.restore();
                rotation += 0.1;
                requestAnimationFrame(animateDisk);
            };
            animateDisk();
        }

        function drawIndexFile(ctx, x, y) {
            ctx.fillStyle = '#F39C12';
            ctx.fillRect(x, y, 120, 140);

            ctx.fillStyle = '#FFF';
            ctx.font = '14px Arial';
            ctx.fillText('索引文件', x + 30, y + 30);

            // 绘制索引条目
            const entries = ['A-D', 'E-H', 'I-L', 'M-P', 'Q-T', 'U-Z'];
            entries.forEach((entry, i) => {
                ctx.fillStyle = i % 2 === 0 ? '#E8F4FD' : '#D4EDDA';
                ctx.fillRect(x + 10, y + 50 + i * 15, 100, 12);
                ctx.fillStyle = '#333';
                ctx.font = '10px Arial';
                ctx.fillText(entry, x + 15, y + 60 + i * 15);
            });
        }

        function animateDataFlow(ctx) {
            let particles = [];
            for (let i = 0; i < 10; i++) {
                particles.push({
                    x: 200,
                    y: 150 + Math.random() * 20,
                    vx: 2 + Math.random() * 2,
                    vy: (Math.random() - 0.5) * 2,
                    life: 100
                });
            }

            const animateParticles = () => {
                particles.forEach((particle, index) => {
                    if (particle.life > 0) {
                        ctx.fillStyle = `rgba(52, 152, 219, ${particle.life / 100})`;
                        ctx.beginPath();
                        ctx.arc(particle.x, particle.y, 3, 0, 2 * Math.PI);
                        ctx.fill();

                        particle.x += particle.vx;
                        particle.y += particle.vy;
                        particle.life--;
                    }
                });

                if (particles.some(p => p.life > 0)) {
                    requestAnimationFrame(animateParticles);
                }
            };
            animateParticles();
        }

        // 速度挑战游戏
        function startSpeedGame() {
            const canvas = document.getElementById('efficiencyCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            gameActive = true;

            // 绘制两个搜索区域
            drawSearchArea(ctx, 50, 50, '无索引搜索', '#E74C3C');
            drawSearchArea(ctx, 350, 50, '有索引搜索', '#27AE60');

            // 开始搜索动画
            simulateSearch(ctx, 50, 100, false); // 无索引
            simulateSearch(ctx, 350, 100, true);  // 有索引
        }

        function drawSearchArea(ctx, x, y, title, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 200, 150);

            ctx.fillStyle = '#FFF';
            ctx.font = '16px Arial';
            ctx.fillText(title, x + 20, y + 30);

            // 绘制数据块
            for (let i = 0; i < 20; i++) {
                const blockX = x + 20 + (i % 5) * 30;
                const blockY = y + 50 + Math.floor(i / 5) * 20;
                ctx.fillStyle = '#ECF0F1';
                ctx.fillRect(blockX, blockY, 25, 15);
                ctx.fillStyle = '#2C3E50';
                ctx.font = '10px Arial';
                ctx.fillText(i + 1, blockX + 8, blockY + 12);
            }
        }

        function simulateSearch(ctx, startX, startY, hasIndex) {
            let currentBlock = 0;
            const totalBlocks = 20;
            const targetBlock = 15; // 要找的数据
            const speed = hasIndex ? 200 : 50; // 有索引的搜索更快

            const searchAnimation = () => {
                if (!gameActive) return;

                // 清除之前的高亮
                if (currentBlock > 0) {
                    const prevX = startX + 20 + ((currentBlock - 1) % 5) * 30;
                    const prevY = startY + 50 + Math.floor((currentBlock - 1) / 5) * 20;
                    ctx.fillStyle = '#ECF0F1';
                    ctx.fillRect(prevX, prevY, 25, 15);
                    ctx.fillStyle = '#2C3E50';
                    ctx.font = '10px Arial';
                    ctx.fillText(currentBlock, prevX + 8, prevY + 12);
                }

                if (currentBlock < totalBlocks) {
                    // 高亮当前搜索的块
                    const blockX = startX + 20 + (currentBlock % 5) * 30;
                    const blockY = startY + 50 + Math.floor(currentBlock / 5) * 20;
                    ctx.fillStyle = '#F1C40F';
                    ctx.fillRect(blockX, blockY, 25, 15);
                    ctx.fillStyle = '#2C3E50';
                    ctx.font = '10px Arial';
                    ctx.fillText(currentBlock + 1, blockX + 8, blockY + 12);

                    if (hasIndex && currentBlock === 0) {
                        // 有索引时直接跳到目标
                        currentBlock = targetBlock - 1;
                    }

                    if (currentBlock + 1 === targetBlock) {
                        // 找到目标
                        ctx.fillStyle = '#27AE60';
                        ctx.fillRect(blockX, blockY, 25, 15);
                        ctx.fillStyle = '#FFF';
                        ctx.fillText('找到!', blockX + 2, blockY + 12);

                        if (hasIndex) {
                            score += 10;
                        } else {
                            score += 5;
                        }
                        document.getElementById('score').textContent = score;
                        return;
                    }

                    currentBlock++;
                    setTimeout(searchAnimation, speed);
                }
            };

            searchAnimation();
        }

        // 优缺点演示
        function startProsConsDemo() {
            const canvas = document.getElementById('prosConsCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制优点区域
            drawProsSection(ctx, 50, 50);

            // 绘制缺点区域
            drawConsSection(ctx, 350, 50);

            // 添加动画效果
            animateProsConsComparison(ctx);
        }

        function drawProsSection(ctx, x, y) {
            ctx.fillStyle = '#D5EDDA';
            ctx.fillRect(x, y, 250, 300);

            ctx.fillStyle = '#155724';
            ctx.font = '20px Arial';
            ctx.fillText('✅ 优点', x + 80, y + 40);

            const pros = [
                '🚀 查询速度快',
                '⚡ 响应时间短',
                '📈 提高性能',
                '🎯 精确定位',
                '💡 减少扫描'
            ];

            pros.forEach((pro, i) => {
                ctx.fillStyle = '#28A745';
                ctx.font = '16px Arial';
                ctx.fillText(pro, x + 20, y + 80 + i * 40);
            });
        }

        function drawConsSection(ctx, x, y) {
            ctx.fillStyle = '#F8D7DA';
            ctx.fillRect(x, y, 250, 300);

            ctx.fillStyle = '#721C24';
            ctx.font = '20px Arial';
            ctx.fillText('❌ 缺点', x + 80, y + 40);

            const cons = [
                '💾 占用存储空间',
                '🔄 需要维护更新',
                '⏱️ 插入删除变慢',
                '🏗️ 创建需要时间',
                '📊 额外的开销'
            ];

            cons.forEach((con, i) => {
                ctx.fillStyle = '#DC3545';
                ctx.font = '16px Arial';
                ctx.fillText(con, x + 20, y + 80 + i * 40);
            });
        }

        function animateProsConsComparison(ctx) {
            let scale = 1;
            let growing = true;

            const animate = () => {
                // 清除天平区域
                ctx.clearRect(250, 360, 100, 40);

                // 绘制天平
                ctx.strokeStyle = '#6C757D';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(280, 380);
                ctx.lineTo(320, 380);
                ctx.moveTo(300, 380);
                ctx.lineTo(300, 390);
                ctx.stroke();

                // 天平左右摆动
                const leftY = 375 + Math.sin(Date.now() * 0.005) * 5;
                const rightY = 385 - Math.sin(Date.now() * 0.005) * 5;

                ctx.beginPath();
                ctx.moveTo(285, leftY);
                ctx.lineTo(295, leftY);
                ctx.moveTo(305, rightY);
                ctx.lineTo(315, rightY);
                ctx.stroke();

                if (growing) {
                    scale += 0.01;
                    if (scale > 1.1) growing = false;
                } else {
                    scale -= 0.01;
                    if (scale < 0.9) growing = true;
                }

                requestAnimationFrame(animate);
            };
            animate();
        }

        // 初始化页面
        window.onload = function() {
            // 自动开始第一个演示
            setTimeout(startIndexDemo, 1000);
        };
    </script>
</body>
</html>
