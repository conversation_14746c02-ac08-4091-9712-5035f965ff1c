[{"name": "Java面试题", "path": "node/Java面试题", "icon": "el-icon-folder", "files": [], "children": [{"name": "三、并发编程", "path": "node/Java面试题/三、并发编程", "icon": "el-icon-folder", "files": [], "children": [{"name": "27、什么是AQS", "path": "node/Java面试题/三、并发编程/27、什么是AQS", "icon": "el-icon-folder", "files": [{"name": "aqs-learning", "path": "node/Java面试题/三、并发编程/27、什么是AQS/aqs-learning.html"}], "children": [{"name": "关键方法", "path": "node/Java面试题/三、并发编程/27、什么是AQS/关键方法", "icon": "el-icon-folder", "files": [{"name": "aqs-learning", "path": "node/Java面试题/三、并发编程/27、什么是AQS/关键方法/aqs-learning.html"}]}, {"name": "核心原理", "path": "node/Java面试题/三、并发编程/27、什么是AQS/核心原理", "icon": "el-icon-folder", "files": [{"name": "aqs-learning", "path": "node/Java面试题/三、并发编程/27、什么是AQS/核心原理/aqs-learning.html"}]}]}]}, {"name": "九、Linux", "path": "node/Java面试题/九、Linux", "icon": "el-icon-folder", "files": [], "children": [{"name": "10.一台 Linux 系统初始化环境后需要做一些什么安全工作？", "path": "node/Java面试题/九、Linux/10.一台 Linux 系统初始化环境后需要做一些什么安全工作？", "icon": "el-icon-folder", "files": [{"name": "linux-security-tutorial", "path": "node/Java面试题/九、Linux/10.一台 Linux 系统初始化环境后需要做一些什么安全工作？/linux-security-tutorial.html"}]}, {"name": "11.什么叫 CC 攻击？什么叫 DDOS 攻击？", "path": "node/Java面试题/九、Linux/11.什么叫 CC 攻击？什么叫 DDOS 攻击？", "icon": "el-icon-folder", "files": [{"name": "network-security-education", "path": "node/Java面试题/九、Linux/11.什么叫 CC 攻击？什么叫 DDOS 攻击？/network-security-education.html"}]}, {"name": "12.请问当用户反馈网站访问慢，你会如何处理？", "path": "node/Java面试题/九、Linux/12.请问当用户反馈网站访问慢，你会如何处理？", "icon": "el-icon-folder", "files": [{"name": "website-performance-tutorial", "path": "node/Java面试题/九、Linux/12.请问当用户反馈网站访问慢，你会如何处理？/website-performance-tutorial.html"}, {"name": "网站访问慢原因学习", "path": "node/Java面试题/九、Linux/12.请问当用户反馈网站访问慢，你会如何处理？/网站访问慢原因学习.html"}]}]}, {"name": "二、集合", "path": "node/Java面试题/二、集合", "icon": "el-icon-folder", "files": [], "children": [{"name": "1、 什么是集合 ？", "path": "node/Java面试题/二、集合/1、 什么是集合 ？", "icon": "el-icon-folder", "files": [{"name": "集合概念学习_动画版", "path": "node/Java面试题/二、集合/1、 什么是集合 ？/集合概念学习_动画版.html"}], "children": [{"name": "1.集合的特点", "path": "node/Java面试题/二、集合/1、 什么是集合 ？/1.集合的特点", "icon": "el-icon-folder", "files": [{"name": "集合特点学习_互动版", "path": "node/Java面试题/二、集合/1、 什么是集合 ？/1.集合的特点/集合特点学习_互动版.html"}]}, {"name": "2.集合和数组的区别", "path": "node/Java面试题/二、集合/1、 什么是集合 ？/2.集合和数组的区别", "icon": "el-icon-folder", "files": [{"name": "集合与数组区别_深度学习版", "path": "node/Java面试题/二、集合/1、 什么是集合 ？/2.集合和数组的区别/集合与数组区别_深度学习版.html"}]}, {"name": "3.使用集合框架的好处", "path": "node/Java面试题/二、集合/1、 什么是集合 ？/3.使用集合框架的好处", "icon": "el-icon-folder", "files": [{"name": "集合框架优势_互动学习版", "path": "node/Java面试题/二、集合/1、 什么是集合 ？/3.使用集合框架的好处/集合框架优势_互动学习版.html"}]}]}, {"name": "2、常用的集合类有哪些？", "path": "node/Java面试题/二、集合/2、常用的集合类有哪些？", "icon": "el-icon-folder", "files": [{"name": "java-collections-learning", "path": "node/Java面试题/二、集合/2、常用的集合类有哪些？/java-collections-learning.html"}]}, {"name": "3、List，Set，Map三者的区别？List、Set、Map 是否继 承自 Collection 接口？List、Map、Set 三个接口存取 元素时，各有什么特点？", "path": "node/Java面试题/二、集合/3、List，Set，Map三者的区别？List、Set、Map 是否继 承自 Collection 接口？List、Map、Set 三个接口存取 元素时，各有什么特点？", "icon": "el-icon-folder", "files": [{"name": "java-collections-interactive", "path": "node/Java面试题/二、集合/3、List，Set，Map三者的区别？List、Set、Map 是否继 承自 Collection 接口？List、Map、Set 三个接口存取 元素时，各有什么特点？/java-collections-interactive.html"}]}, {"name": "4、集合框架底层数据结构", "path": "node/Java面试题/二、集合/4、集合框架底层数据结构", "icon": "el-icon-folder", "files": [{"name": "java-collections-interactive", "path": "node/Java面试题/二、集合/4、集合框架底层数据结构/java-collections-interactive.html"}]}, {"name": "5、哪些集合类是线程安全的？", "path": "node/Java面试题/二、集合/5、哪些集合类是线程安全的？", "icon": "el-icon-folder", "files": [{"name": "java-collections-learning", "path": "node/Java面试题/二、集合/5、哪些集合类是线程安全的？/java-collections-learning.html"}]}, {"name": "6、Java集合的快速失败机制 “fail-fast”？", "path": "node/Java面试题/二、集合/6、Java集合的快速失败机制 “fail-fast”？", "icon": "el-icon-folder", "files": []}]}, {"name": "八、Mysql", "path": "node/Java面试题/八、Mysql", "icon": "el-icon-folder", "files": [], "children": [{"name": "1. 什么是MySQL", "path": "node/Java面试题/八、Mysql/1. 什么是MySQL", "icon": "el-icon-folder", "files": [{"name": "mysql-learning", "path": "node/Java面试题/八、Mysql/1. 什么是MySQL/mysql-learning.html"}]}, {"name": "10. 索引算法有哪些", "path": "node/Java面试题/八、Mysql/10. 索引算法有哪些", "icon": "el-icon-folder", "files": [{"name": "database-index-learning", "path": "node/Java面试题/八、Mysql/10. 索引算法有哪些/database-index-learning.html"}]}, {"name": "11.索引设计的原则", "path": "node/Java面试题/八、Mysql/11.索引设计的原则", "icon": "el-icon-folder", "files": [{"name": "database-index-learning", "path": "node/Java面试题/八、Mysql/11.索引设计的原则/database-index-learning.html"}]}, {"name": "12.索引虽好，但也不是无限制的使用，好符合一下几个原则", "path": "node/Java面试题/八、Mysql/12.索引虽好，但也不是无限制的使用，好符合一下几个原则", "icon": "el-icon-folder", "files": [{"name": "mysql-index-interactive-learning", "path": "node/Java面试题/八、Mysql/12.索引虽好，但也不是无限制的使用，好符合一下几个原则/mysql-index-interactive-learning.html"}]}, {"name": "13.创建索引时需要注意什么", "path": "node/Java面试题/八、Mysql/13.创建索引时需要注意什么", "icon": "el-icon-folder", "files": [{"name": "mysql-index-learning", "path": "node/Java面试题/八、Mysql/13.创建索引时需要注意什么/mysql-index-learning.html"}]}, {"name": "2.数据库三大范式是什么", "path": "node/Java面试题/八、Mysql/2.数据库三大范式是什么", "icon": "el-icon-folder", "files": [{"name": "database-normalization-learning", "path": "node/Java面试题/八、Mysql/2.数据库三大范式是什么/database-normalization-learning.html"}]}, {"name": "22.什么是脏读？幻读？不可重复读？", "path": "node/Java面试题/八、Mysql/22.什么是脏读？幻读？不可重复读？", "icon": "el-icon-folder", "files": [{"name": "database-transaction-isolation", "path": "node/Java面试题/八、Mysql/22.什么是脏读？幻读？不可重复读？/database-transaction-isolation.html"}]}, {"name": "29. 为什么要使用视图？什么是视图", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图", "icon": "el-icon-folder", "files": [{"name": "mysql-view-learning", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图/mysql-view-learning.html"}], "children": [{"name": "1.视图有哪些特点", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图/1.视图有哪些特点", "icon": "el-icon-folder", "files": [{"name": "database-view-learning", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图/1.视图有哪些特点/database-view-learning.html"}]}, {"name": "2.视图的使用场景有哪些", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图/2.视图的使用场景有哪些", "icon": "el-icon-folder", "files": [{"name": "database-view-learning", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图/2.视图的使用场景有哪些/database-view-learning.html"}]}, {"name": "3.视图的优点", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图/3.视图的优点", "icon": "el-icon-folder", "files": [{"name": "database-views-learning", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图/3.视图的优点/database-views-learning.html"}]}, {"name": "4.视图的缺点", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图/4.视图的缺点", "icon": "el-icon-folder", "files": [{"name": "database-view-learning", "path": "node/Java面试题/八、Mysql/29. 为什么要使用视图？什么是视图/4.视图的缺点/database-view-learning.html"}]}]}, {"name": "3.MySQL的binlog有有几种录入格式？分别有什么区别", "path": "node/Java面试题/八、Mysql/3.MySQL的binlog有有几种录入格式？分别有什么区别", "icon": "el-icon-folder", "files": [{"name": "mysql-binlog-learning", "path": "node/Java面试题/八、Mysql/3.MySQL的binlog有有几种录入格式？分别有什么区别/mysql-binlog-learning.html"}]}, {"name": "4. MySQL存储引擎MyISAM与InnoDB区别", "path": "node/Java面试题/八、Mysql/4. MySQL存储引擎MyISAM与InnoDB区别", "icon": "el-icon-folder", "files": [{"name": "mysql-storage-engine-learning", "path": "node/Java面试题/八、Mysql/4. MySQL存储引擎MyISAM与InnoDB区别/mysql-storage-engine-learning.html"}]}, {"name": "45.大表怎么优化？", "path": "node/Java面试题/八、Mysql/45.大表怎么优化？", "icon": "el-icon-folder", "files": [{"name": "database-optimization-learning", "path": "node/Java面试题/八、Mysql/45.大表怎么优化？/database-optimization-learning.html"}]}, {"name": "48. 读写分离有哪些解决方案？", "path": "node/Java面试题/八、Mysql/48. 读写分离有哪些解决方案？", "icon": "el-icon-folder", "files": [{"name": "mysql-read-write-separation", "path": "node/Java面试题/八、Mysql/48. 读写分离有哪些解决方案？/mysql-read-write-separation.html"}]}, {"name": "5. MyISAM索引与InnoDB索引的区别？", "path": "node/Java面试题/八、Mysql/5. MyISAM索引与InnoDB索引的区别？", "icon": "el-icon-folder", "files": [{"name": "database-index-learning", "path": "node/Java面试题/八、Mysql/5. MyISAM索引与InnoDB索引的区别？/database-index-learning.html"}]}, {"name": "6. 存储引擎选择", "path": "node/Java面试题/八、Mysql/6. 存储引擎选择", "icon": "el-icon-folder", "files": [{"name": "mysql-storage-engines", "path": "node/Java面试题/八、Mysql/6. 存储引擎选择/mysql-storage-engines.html"}]}, {"name": "8. 索引有哪几种类型？", "path": "node/Java面试题/八、Mysql/8. 索引有哪几种类型？", "icon": "el-icon-folder", "files": [{"name": "database-index-learning", "path": "node/Java面试题/八、Mysql/8. 索引有哪几种类型？/database-index-learning.html"}]}, {"name": "9.索引的基本原理", "path": "node/Java面试题/八、Mysql/9.索引的基本原理", "icon": "el-icon-folder", "files": []}]}, {"name": "初级", "path": "node/Java面试题/初级", "icon": "el-icon-folder", "files": [{"name": "automatic-memory-management", "path": "node/Java面试题/初级/automatic-memory-management.html"}, {"name": "index", "path": "node/Java面试题/初级/index.html"}, {"name": "index2", "path": "node/Java面试题/初级/index2.html"}, {"name": "index3", "path": "node/Java面试题/初级/index3.html"}, {"name": "index4", "path": "node/Java面试题/初级/index4.html"}, {"name": "index5", "path": "node/Java面试题/初级/index5.html"}, {"name": "index6", "path": "node/Java面试题/初级/index6.html"}, {"name": "java8_features", "path": "node/Java面试题/初级/java8_features.html"}, {"name": "java_access_modifiers", "path": "node/Java面试题/初级/java_access_modifiers.html"}, {"name": "java_concepts", "path": "node/Java面试题/初级/java_concepts.html"}, {"name": "java_concepts_explained", "path": "node/Java面试题/初级/java_concepts_explained.html"}, {"name": "java_concepts_interactive", "path": "node/Java面试题/初级/java_concepts_interactive.html"}, {"name": "java_equals_vs_double_equals_explainer", "path": "node/Java面试题/初级/java_equals_vs_double_equals_explainer.html"}, {"name": "java_equals_vs_operator", "path": "node/Java面试题/初级/java_equals_vs_operator.html"}, {"name": "java_features_interactive", "path": "node/Java面试题/初级/java_features_interactive.html"}, {"name": "java_interview_interactive_bit_shift_float_casting", "path": "node/Java面试题/初级/java_interview_interactive_bit_shift_float_casting.html"}, {"name": "java_learning_interactive", "path": "node/Java面试题/初级/java_learning_interactive.html"}, {"name": "java_loop_control_statements", "path": "node/Java面试题/初级/java_loop_control_statements.html"}, {"name": "java_oop_visualization", "path": "node/Java面试题/初级/java_oop_visualization.html"}, {"name": "java_platform_independence", "path": "node/Java面试题/初级/java_platform_independence.html"}, {"name": "multithread_explanation", "path": "node/Java面试题/初级/multithread_explanation.html"}, {"name": "oop_explanation", "path": "node/Java面试题/初级/oop_explanation.html"}, {"name": "overload_override_interactive", "path": "node/Java面试题/初级/overload_override_interactive.html"}, {"name": "reflection_and_copy", "path": "node/Java面试题/初级/reflection_and_copy.html"}, {"name": "string-comparison", "path": "node/Java面试题/初级/string-comparison.html"}, {"name": "strongly_typed_language_demo", "path": "node/Java面试题/初级/strongly_typed_language_demo.html"}, {"name": "抽象类与接口的区别", "path": "node/Java面试题/初级/抽象类与接口的区别.html"}, {"name": "编程语言对比", "path": "node/Java面试题/初级/编程语言对比.html"}, {"name": "逻辑运算符演示", "path": "node/Java面试题/初级/逻辑运算符演示.html"}]}, {"name": "十、Redis", "path": "node/Java面试题/十、Redis", "icon": "el-icon-folder", "files": [], "children": [{"name": "26.缓存穿透", "path": "node/Java面试题/十、Redis/26.缓存穿透", "icon": "el-icon-folder", "files": [{"name": "cache-penetration-tutorial", "path": "node/Java面试题/十、Redis/26.缓存穿透/cache-penetration-tutorial.html"}]}, {"name": "27.缓存击穿", "path": "node/Java面试题/十、Redis/27.缓存击穿", "icon": "el-icon-folder", "files": [{"name": "cache-breakdown-tutorial", "path": "node/Java面试题/十、Redis/27.缓存击穿/cache-breakdown-tutorial.html"}]}, {"name": "4.为什么要用 Redis 而不用 map", "path": "node/Java面试题/十、Redis/4.为什么要用 Redis 而不用 map", "icon": "el-icon-folder", "files": [], "children": [{"name": "guava 做缓存", "path": "node/Java面试题/十、Redis/4.为什么要用 Redis 而不用 map/guava 做缓存", "icon": "el-icon-folder", "files": [{"name": "cache-learning", "path": "node/Java面试题/十、Redis/4.为什么要用 Redis 而不用 map/guava 做缓存/cache-learning.html"}]}]}]}, {"name": "十三、Spring Cloud", "path": "node/Java面试题/十三、Spring Cloud", "icon": "el-icon-folder", "files": []}, {"name": "十六、二十三种设计模式", "path": "node/Java面试题/十六、二十三种设计模式", "icon": "el-icon-folder", "files": []}, {"name": "四、Spinrg", "path": "node/Java面试题/四、Spinrg", "icon": "el-icon-folder", "files": [], "children": [{"name": "10、 @Autowired和@Resource之间的区别？", "path": "node/Java面试题/四、Spinrg/10、 @Autowired和@Resource之间的区别？", "icon": "el-icon-folder", "files": [{"name": "spring-annotations-learning", "path": "node/Java面试题/四、Spinrg/10、 @Autowired和@Resource之间的区别？/spring-annotations-learning.html"}]}, {"name": "11、@Qualifier 注解有什么作用", "path": "node/Java面试题/四、Spinrg/11、@Qualifier 注解有什么作用", "icon": "el-icon-folder", "files": [{"name": "spring-qualifier-tutorial", "path": "node/Java面试题/四、Spinrg/11、@Qualifier 注解有什么作用/spring-qualifier-tutorial.html"}]}]}, {"name": "索引相关", "path": "node/Java面试题/索引相关", "icon": "el-icon-folder", "files": [], "children": [{"name": "1.索引是什么？", "path": "node/Java面试题/索引相关/1.索引是什么？", "icon": "el-icon-folder", "files": [{"name": "索引学习页面", "path": "node/Java面试题/索引相关/1.索引是什么？/索引学习页面.html"}]}, {"name": "2.索引类型", "path": "node/Java面试题/索引相关/2.索引类型", "icon": "el-icon-folder", "files": [{"name": "database-index-learning", "path": "node/Java面试题/索引相关/2.索引类型/database-index-learning.html"}]}]}]}, {"name": "我的架构", "path": "node/我的架构", "icon": "el-icon-folder", "files": [], "children": [{"name": "硬件", "path": "node/我的架构/硬件", "icon": "el-icon-folder", "files": [], "children": [{"name": "嵌入式", "path": "node/我的架构/硬件/嵌入式", "icon": "el-icon-folder", "files": [{"name": "嵌入式处理器MMU学习", "path": "node/我的架构/硬件/嵌入式/嵌入式处理器MMU学习.html"}], "children": [{"name": "内存", "path": "node/我的架构/硬件/嵌入式/内存", "icon": "el-icon-folder", "files": [{"name": "缓冲区管理学习", "path": "node/我的架构/硬件/嵌入式/内存/缓冲区管理学习.html"}]}, {"name": "磁盘", "path": "node/我的架构/硬件/嵌入式/磁盘", "icon": "el-icon-folder", "files": [], "children": [{"name": "1.存储", "path": "node/我的架构/硬件/嵌入式/磁盘/1.存储", "icon": "el-icon-folder", "files": [{"name": "storage-hierarchy-learning", "path": "node/我的架构/硬件/嵌入式/磁盘/1.存储/storage-hierarchy-learning.html"}, {"name": "存储器存取方式教学", "path": "node/我的架构/硬件/嵌入式/磁盘/1.存储/存储器存取方式教学.html"}, {"name": "存储器寻址方式学习", "path": "node/我的架构/硬件/嵌入式/磁盘/1.存储/存储器寻址方式学习.html"}, {"name": "存储器访问方式学习", "path": "node/我的架构/硬件/嵌入式/磁盘/1.存储/存储器访问方式学习.html"}, {"name": "磁盘存储原理教学", "path": "node/我的架构/硬件/嵌入式/磁盘/1.存储/磁盘存储原理教学.html"}]}, {"name": "2.计算", "path": "node/我的架构/硬件/嵌入式/磁盘/2.计算", "icon": "el-icon-folder", "files": [{"name": "磁盘IO学习游戏", "path": "node/我的架构/硬件/嵌入式/磁盘/2.计算/磁盘IO学习游戏.html"}, {"name": "磁盘IO调度动画教学", "path": "node/我的架构/硬件/嵌入式/磁盘/2.计算/磁盘IO调度动画教学.html"}]}]}]}]}, {"name": "软件", "path": "node/我的架构/软件", "icon": "el-icon-folder", "files": [], "children": [{"name": "架构", "path": "node/我的架构/软件/架构", "icon": "el-icon-s-platform", "files": [], "children": [{"name": "方法", "path": "node/我的架构/软件/架构/方法", "icon": "el-icon-folder", "files": [{"name": "6.软件方法学学习", "path": "node/我的架构/软件/架构/方法/6.软件方法学学习.html"}, {"name": "7.软件方法学学习", "path": "node/我的架构/软件/架构/方法/7.软件方法学学习.html"}, {"name": "8.软件方法学学习", "path": "node/我的架构/软件/架构/方法/8.软件方法学学习.html"}]}, {"name": "标准", "path": "node/我的架构/软件/架构/标准", "icon": "el-icon-folder", "files": [{"name": "3.architecture_standard_learning", "path": "node/我的架构/软件/架构/标准/3.architecture_standard_learning.html"}, {"name": "4.软件架构标准学习", "path": "node/我的架构/软件/架构/标准/4.软件架构标准学习.html"}, {"name": "5.软件架构标准学习", "path": "node/我的架构/软件/架构/标准/5.软件架构标准学习.html"}]}, {"name": "系统", "path": "node/我的架构/软件/架构/系统", "icon": "el-icon-folder", "files": [], "children": [{"name": "企业", "path": "node/我的架构/软件/架构/系统/企业", "icon": "el-icon-folder", "files": [], "children": [{"name": "应用", "path": "node/我的架构/软件/架构/系统/企业/应用", "icon": "el-icon-folder", "files": [{"name": "1.enterprise-integration-learning", "path": "node/我的架构/软件/架构/系统/企业/应用/1.enterprise-integration-learning.html"}, {"name": "2.企业应用集成学习", "path": "node/我的架构/软件/架构/系统/企业/应用/2.企业应用集成学习.html"}]}, {"name": "流水线", "path": "node/我的架构/软件/架构/系统/企业/流水线", "icon": "el-icon-folder", "files": [{"name": "流水线吞吐率教学", "path": "node/我的架构/软件/架构/系统/企业/流水线/流水线吞吐率教学.html"}, {"name": "流水线指令执行教学", "path": "node/我的架构/软件/架构/系统/企业/流水线/流水线指令执行教学.html"}]}]}, {"name": "命令", "path": "node/我的架构/软件/架构/系统/命令", "icon": "el-icon-folder", "files": [{"name": "system-monitoring-interactive-learning", "path": "node/我的架构/软件/架构/系统/命令/system-monitoring-interactive-learning.html"}, {"name": "system-monitoring-learning", "path": "node/我的架构/软件/架构/系统/命令/system-monitoring-learning.html"}]}]}]}, {"name": "计算", "path": "node/我的架构/软件/计算", "icon": "el-icon-folder", "files": [], "children": [{"name": "crc", "path": "node/我的架构/软件/计算/crc", "icon": "el-icon-folder", "files": [{"name": "crc-learning", "path": "node/我的架构/软件/计算/crc/crc-learning.html"}]}, {"name": "性价比", "path": "node/我的架构/软件/计算/性价比", "icon": "el-icon-folder", "files": [{"name": "博弈论教学", "path": "node/我的架构/软件/计算/性价比/博弈论教学.html"}]}]}]}]}, {"name": "考研", "path": "node/考研", "icon": "el-icon-reading", "files": [], "children": [{"name": "408", "path": "node/考研/408", "icon": "el-icon-monitor", "files": []}, {"name": "数学", "path": "node/考研/数学", "icon": "el-icon-data-line", "files": [], "children": [{"name": "小学", "path": "node/考研/数学/小学", "icon": "el-icon-school", "files": [], "children": [{"name": "除法", "path": "node/考研/数学/小学/除法", "icon": "el-icon-folder", "files": [{"name": "三年级数学除法互动教学", "path": "node/考研/数学/小学/除法/三年级数学除法互动教学.html"}, {"name": "竖式除法专项教学", "path": "node/考研/数学/小学/除法/竖式除法专项教学.html"}]}]}, {"name": "高中", "path": "node/考研/数学/高中", "icon": "el-icon-folder", "files": [], "children": [{"name": "1.集合", "path": "node/考研/数学/高中/1.集合", "icon": "el-icon-folder", "files": [], "children": [{"name": "1.集合与集合的表示方法", "path": "node/考研/数学/高中/1.集合/1.集合与集合的表示方法", "icon": "el-icon-folder", "files": [{"name": "集合学习互动页面", "path": "node/考研/数学/高中/1.集合/1.集合与集合的表示方法/集合学习互动页面.html"}]}, {"name": "2.集合之间的关系", "path": "node/考研/数学/高中/1.集合/2.集合之间的关系", "icon": "el-icon-folder", "files": [{"name": "集合关系学习页面", "path": "node/考研/数学/高中/1.集合/2.集合之间的关系/集合关系学习页面.html"}]}, {"name": "3.集合的运算", "path": "node/考研/数学/高中/1.集合/3.集合的运算", "icon": "el-icon-folder", "files": [{"name": "集合运算学习页面", "path": "node/考研/数学/高中/1.集合/3.集合的运算/集合运算学习页面.html"}]}]}, {"name": "2.函数", "path": "node/考研/数学/高中/2.函数", "icon": "el-icon-folder", "files": [], "children": [{"name": "01.函数", "path": "node/考研/数学/高中/2.函数/01.函数", "icon": "el-icon-folder", "files": [{"name": "函数学习交互页面", "path": "node/考研/数学/高中/2.函数/01.函数/函数学习交互页面.html"}]}, {"name": "02.函数的表示方法", "path": "node/考研/数学/高中/2.函数/02.函数的表示方法", "icon": "el-icon-folder", "files": [{"name": "函数表示方法教学", "path": "node/考研/数学/高中/2.函数/02.函数的表示方法/函数表示方法教学.html"}]}, {"name": "03.函数的单调性", "path": "node/考研/数学/高中/2.函数/03.函数的单调性", "icon": "el-icon-folder", "files": [{"name": "函数单调性教学", "path": "node/考研/数学/高中/2.函数/03.函数的单调性/函数单调性教学.html"}]}]}]}]}, {"name": "英语", "path": "node/考研/英语", "icon": "el-icon-folder", "files": [], "children": [{"name": "单词", "path": "node/考研/英语/单词", "icon": "el-icon-document", "files": [{"name": "abstract_animation", "path": "node/考研/英语/单词/abstract_animation.html"}, {"name": "accommodate_animation", "path": "node/考研/英语/单词/accommodate_animation.html"}, {"name": "acquire_animation", "path": "node/考研/英语/单词/acquire_animation.html"}, {"name": "adapt_animation", "path": "node/考研/英语/单词/adapt_animation.html"}, {"name": "adhere_animation", "path": "node/考研/英语/单词/adhere_animation.html"}, {"name": "advocate_animation", "path": "node/考研/英语/单词/advocate_animation.html"}, {"name": "ameliorate_animation", "path": "node/考研/英语/单词/ameliorate_animation.html"}, {"name": "append_animation", "path": "node/考研/英语/单词/append_animation.html"}, {"name": "assemble_animation", "path": "node/考研/英语/单词/assemble_animation.html"}, {"name": "assess_animation", "path": "node/考研/英语/单词/assess_animation.html"}, {"name": "attribute_animation", "path": "node/考研/英语/单词/attribute_animation.html"}, {"name": "benevolent_animation", "path": "node/考研/英语/单词/benevolent_animation.html"}, {"name": "circumstance_animation", "path": "node/考研/英语/单词/circumstance_animation.html"}, {"name": "circumvent_animation", "path": "node/考研/英语/单词/circumvent_animation.html"}, {"name": "coincidence_animation", "path": "node/考研/英语/单词/coincidence_animation.html"}, {"name": "component_animation", "path": "node/考研/英语/单词/component_animation.html"}, {"name": "comprehend_animation", "path": "node/考研/英语/单词/comprehend_animation.html"}, {"name": "compress_animation", "path": "node/考研/英语/单词/compress_animation.html"}, {"name": "conclude_animation", "path": "node/考研/英语/单词/conclude_animation.html"}, {"name": "construct_animation", "path": "node/考研/英语/单词/construct_animation.html"}, {"name": "contradict_animation", "path": "node/考研/英语/单词/contradict_animation.html"}, {"name": "contribute_animation", "path": "node/考研/英语/单词/contribute_animation.html"}, {"name": "convene_animation", "path": "node/考研/英语/单词/convene_animation.html"}, {"name": "convert_animation", "path": "node/考研/英语/单词/convert_animation.html"}, {"name": "decompose_animation", "path": "node/考研/英语/单词/decompose_animation.html"}, {"name": "deduce_animation", "path": "node/考研/英语/单词/deduce_animation.html"}, {"name": "demote_animation", "path": "node/考研/英语/单词/demote_animation.html"}, {"name": "describe_animation", "path": "node/考研/英语/单词/describe_animation.html"}, {"name": "disperse_animation", "path": "node/考研/英语/单词/disperse_animation.html"}, {"name": "disrupt_animation", "path": "node/考研/英语/单词/disrupt_animation.html"}, {"name": "distribute_animation", "path": "node/考研/英语/单词/distribute_animation.html"}, {"name": "eject_animation", "path": "node/考研/英语/单词/eject_animation.html"}, {"name": "emerge_animation", "path": "node/考研/英语/单词/emerge_animation.html"}, {"name": "evolve_animation", "path": "node/考研/英语/单词/evolve_animation.html"}, {"name": "expand_animation", "path": "node/考研/英语/单词/expand_animation.html"}, {"name": "expel_animation", "path": "node/考研/英语/单词/expel_animation.html"}, {"name": "export_animation", "path": "node/考研/英语/单词/export_animation.html"}, {"name": "extract_animation", "path": "node/考研/英语/单词/extract_animation.html"}, {"name": "generate_animation", "path": "node/考研/英语/单词/generate_animation.html"}, {"name": "illuminate_animation", "path": "node/考研/英语/单词/illuminate_animation.html"}, {"name": "imminent_animation", "path": "node/考研/英语/单词/imminent_animation.html"}, {"name": "import_animation", "path": "node/考研/英语/单词/import_animation.html"}, {"name": "induce_animation", "path": "node/考研/英语/单词/induce_animation.html"}, {"name": "inhibit_animation", "path": "node/考研/英语/单词/inhibit_animation.html"}, {"name": "innovate_animation", "path": "node/考研/英语/单词/innovate_animation.html"}, {"name": "insert_animation", "path": "node/考研/英语/单词/insert_animation.html"}, {"name": "inspect_animation", "path": "node/考研/英语/单词/inspect_animation.html"}, {"name": "integrate_animation", "path": "node/考研/英语/单词/integrate_animation.html"}, {"name": "interrupt_animation", "path": "node/考研/英语/单词/interrupt_animation.html"}, {"name": "intervene_animation", "path": "node/考研/英语/单词/intervene_animation.html"}, {"name": "migrate_animation", "path": "node/考研/英语/单词/migrate_animation.html"}, {"name": "perceive_animation", "path": "node/考研/英语/单词/perceive_animation.html"}, {"name": "perspective_animation", "path": "node/考研/英语/单词/perspective_animation.html"}, {"name": "preclude_animation", "path": "node/考研/英语/单词/preclude_animation.html"}, {"name": "predict_animation", "path": "node/考研/英语/单词/predict_animation.html"}, {"name": "profligate_aniation", "path": "node/考研/英语/单词/profligate_aniation.html"}, {"name": "progress_animation", "path": "node/考研/英语/单词/progress_animation.html"}, {"name": "propose_animation", "path": "node/考研/英语/单词/propose_animation.html"}, {"name": "prospect_animation", "path": "node/考研/英语/单词/prospect_animation.html"}, {"name": "provoke_animation", "path": "node/考研/英语/单词/provoke_animation.html"}, {"name": "reflect_animation", "path": "node/考研/英语/单词/reflect_animation.html"}, {"name": "retract_animation", "path": "node/考研/英语/单词/retract_animation.html"}, {"name": "retrospect_animation", "path": "node/考研/英语/单词/retrospect_animation.html"}, {"name": "reveal_animation", "path": "node/考研/英语/单词/reveal_animation.html"}, {"name": "review_animation", "path": "node/考研/英语/单词/review_animation.html"}, {"name": "submit_animation", "path": "node/考研/英语/单词/submit_animation.html"}, {"name": "subscribe_animation", "path": "node/考研/英语/单词/subscribe_animation.html"}, {"name": "subvert_animation", "path": "node/考研/英语/单词/subvert_animation.html"}, {"name": "sustain_animation", "path": "node/考研/英语/单词/sustain_animation.html"}, {"name": "transcend_animation", "path": "node/考研/英语/单词/transcend_animation.html"}, {"name": "transform_animation", "path": "node/考研/英语/单词/transform_animation.html"}, {"name": "transmit_animation", "path": "node/考研/英语/单词/transmit_animation.html"}, {"name": "transport_animation", "path": "node/考研/英语/单词/transport_animation.html"}]}, {"name": "句子", "path": "node/考研/英语/句子", "icon": "el-icon-folder", "files": [{"name": "exam_sentence_animation", "path": "node/考研/英语/句子/exam_sentence_animation.html"}, {"name": "exam_sentence_animation_2", "path": "node/考研/英语/句子/exam_sentence_animation_2.html"}, {"name": "sentence_animation", "path": "node/考研/英语/句子/sentence_animation.html"}, {"name": "word_animation", "path": "node/考研/英语/句子/word_animation.html"}]}, {"name": "手机单词", "path": "node/考研/英语/手机单词", "icon": "el-icon-folder", "files": [{"name": "transform_animation", "path": "node/考研/英语/手机单词/transform_animation.html"}]}, {"name": "词缀", "path": "node/考研/英语/词缀", "icon": "el-icon-folder", "files": [{"name": "act_story_animation", "path": "node/考研/英语/词缀/act_story_animation.html"}, {"name": "aero_word_roots", "path": "node/考研/英语/词缀/aero_word_roots.html"}, {"name": "inter-prefix-story", "path": "node/考研/英语/词缀/inter-prefix-story.html"}, {"name": "over-prefix-story", "path": "node/考研/英语/词缀/over-prefix-story.html"}, {"name": "pre-prefix-story", "path": "node/考研/英语/词缀/pre-prefix-story.html"}, {"name": "prefix-anti-learning", "path": "node/考研/英语/词缀/prefix-anti-learning.html"}, {"name": "prefix-auto-learning", "path": "node/考研/英语/词缀/prefix-auto-learning.html"}, {"name": "prefix-inter-learning", "path": "node/考研/英语/词缀/prefix-inter-learning.html"}, {"name": "prefix-micro-learning", "path": "node/考研/英语/词缀/prefix-micro-learning.html"}, {"name": "prefix-multi-learning", "path": "node/考研/英语/词缀/prefix-multi-learning.html"}, {"name": "prefix-over-learning", "path": "node/考研/英语/词缀/prefix-over-learning.html"}, {"name": "prefix-pre-learning", "path": "node/考研/英语/词缀/prefix-pre-learning.html"}, {"name": "prefix-sub-learning", "path": "node/考研/英语/词缀/prefix-sub-learning.html"}, {"name": "prefix-trans-learning", "path": "node/考研/英语/词缀/prefix-trans-learning.html"}, {"name": "prefix-un-learning", "path": "node/考研/英语/词缀/prefix-un-learning.html"}, {"name": "sub-prefix-story", "path": "node/考研/英语/词缀/sub-prefix-story.html"}, {"name": "suffix-able-learning", "path": "node/考研/英语/词缀/suffix-able-learning.html"}, {"name": "suffix-ology-learning", "path": "node/考研/英语/词缀/suffix-ology-learning.html"}, {"name": "suffix-tion-learning", "path": "node/考研/英语/词缀/suffix-tion-learning.html"}, {"name": "trans-prefix-story", "path": "node/考研/英语/词缀/trans-prefix-story.html"}]}]}]}, {"name": "题目", "path": "node/题目", "icon": "el-icon-edit-outline", "files": [], "children": [{"name": "日期", "path": "node/题目/日期", "icon": "el-icon-folder", "files": [], "children": [{"name": "2025-7-1", "path": "node/题目/日期/2025-7-1", "icon": "el-icon-folder", "files": [{"name": "crc_demo", "path": "node/题目/日期/2025-7-1/crc_demo.html"}, {"name": "database_normalization_game", "path": "node/题目/日期/2025-7-1/database_normalization_game.html"}, {"name": "database_normalization_tutorial", "path": "node/题目/日期/2025-7-1/database_normalization_tutorial.html"}, {"name": "index", "path": "node/题目/日期/2025-7-1/index.html"}, {"name": "index10", "path": "node/题目/日期/2025-7-1/index10.html"}, {"name": "index11", "path": "node/题目/日期/2025-7-1/index11.html"}, {"name": "index12", "path": "node/题目/日期/2025-7-1/index12.html"}, {"name": "index13", "path": "node/题目/日期/2025-7-1/index13.html"}, {"name": "index14", "path": "node/题目/日期/2025-7-1/index14.html"}, {"name": "index2", "path": "node/题目/日期/2025-7-1/index2.html"}, {"name": "index3", "path": "node/题目/日期/2025-7-1/index3.html"}, {"name": "index4", "path": "node/题目/日期/2025-7-1/index4.html"}, {"name": "index5", "path": "node/题目/日期/2025-7-1/index5.html"}, {"name": "index6", "path": "node/题目/日期/2025-7-1/index6.html"}, {"name": "index7", "path": "node/题目/日期/2025-7-1/index7.html"}, {"name": "index8", "path": "node/题目/日期/2025-7-1/index8.html"}, {"name": "index9", "path": "node/题目/日期/2025-7-1/index9.html"}, {"name": "process_sync_demo", "path": "node/题目/日期/2025-7-1/process_sync_demo.html"}]}, {"name": "2025-7-13", "path": "node/题目/日期/2025-7-13", "icon": "el-icon-folder", "files": [{"name": "design_pattern_quiz", "path": "node/题目/日期/2025-7-13/design_pattern_quiz.html"}, {"name": "index", "path": "node/题目/日期/2025-7-13/index.html"}, {"name": "系统工程题目解析", "path": "node/题目/日期/2025-7-13/系统工程题目解析.html"}]}, {"name": "2025-7-14", "path": "node/题目/日期/2025-7-14", "icon": "el-icon-folder", "files": [{"name": "coupling_explanation", "path": "node/题目/日期/2025-7-14/coupling_explanation.html"}, {"name": "index", "path": "node/题目/日期/2025-7-14/index.html"}, {"name": "index2", "path": "node/题目/日期/2025-7-14/index2.html"}]}, {"name": "2025-7-16", "path": "node/题目/日期/2025-7-16", "icon": "el-icon-folder", "files": [{"name": "atam_utility_tree_interactive", "path": "node/题目/日期/2025-7-16/atam_utility_tree_interactive.html"}, {"name": "atam_utility_tree_quiz_game", "path": "node/题目/日期/2025-7-16/atam_utility_tree_quiz_game.html"}, {"name": "cache_memory_address_translation", "path": "node/题目/日期/2025-7-16/cache_memory_address_translation.html"}, {"name": "finance_system_compare", "path": "node/题目/日期/2025-7-16/finance_system_compare.html"}, {"name": "finance_system_compare2", "path": "node/题目/日期/2025-7-16/finance_system_compare2.html"}, {"name": "memory_addressing_modes_game", "path": "node/题目/日期/2025-7-16/memory_addressing_modes_game.html"}, {"name": "network_layer_diagnose", "path": "node/题目/日期/2025-7-16/network_layer_diagnose.html"}, {"name": "utility_tree_structure_game", "path": "node/题目/日期/2025-7-16/utility_tree_structure_game.html"}]}, {"name": "2025-7-17", "path": "node/题目/日期/2025-7-17", "icon": "el-icon-folder", "files": [{"name": "builder_pattern_demo", "path": "node/题目/日期/2025-7-17/builder_pattern_demo.html"}, {"name": "com-object-reuse-learning", "path": "node/题目/日期/2025-7-17/com-object-reuse-learning.html"}, {"name": "creator_pattern_demo", "path": "node/题目/日期/2025-7-17/creator_pattern_demo.html"}, {"name": "database-design-learning", "path": "node/题目/日期/2025-7-17/database-design-learning.html"}, {"name": "design-patterns-learning", "path": "node/题目/日期/2025-7-17/design-patterns-learning.html"}, {"name": "design-patterns-quiz-interactive", "path": "node/题目/日期/2025-7-17/design-patterns-quiz-interactive.html"}, {"name": "design_constraints_demo", "path": "node/题目/日期/2025-7-17/design_constraints_demo.html"}, {"name": "different_representations_demo", "path": "node/题目/日期/2025-7-17/different_representations_demo.html"}, {"name": "doc_sync_game", "path": "node/题目/日期/2025-7-17/doc_sync_game.html"}, {"name": "functional_requirements_demo", "path": "node/题目/日期/2025-7-17/functional_requirements_demo.html"}, {"name": "game-theory-learning", "path": "node/题目/日期/2025-7-17/game-theory-learning.html"}, {"name": "requirements-baseline-learning", "path": "node/题目/日期/2025-7-17/requirements-baseline-learning.html"}, {"name": "rup-learning-game", "path": "node/题目/日期/2025-7-17/rup-learning-game.html"}, {"name": "rup-learning", "path": "node/题目/日期/2025-7-17/rup-learning.html"}, {"name": "scm-tools-learning", "path": "node/题目/日期/2025-7-17/scm-tools-learning.html"}, {"name": "software_design_explanation", "path": "node/题目/日期/2025-7-17/software_design_explanation.html"}, {"name": "uml-learning-game", "path": "node/题目/日期/2025-7-17/uml-learning-game.html"}, {"name": "uml-sequence-diagram-learning", "path": "node/题目/日期/2025-7-17/uml-sequence-diagram-learning.html"}, {"name": "uml-sequence-diagram-learning2", "path": "node/题目/日期/2025-7-17/uml-sequence-diagram-learning2.html"}, {"name": "user_requirements_game", "path": "node/题目/日期/2025-7-17/user_requirements_game.html"}, {"name": "waiter_builder_demo", "path": "node/题目/日期/2025-7-17/waiter_builder_demo.html"}, {"name": "数据库设计阶段学习", "path": "node/题目/日期/2025-7-17/数据库设计阶段学习.html"}, {"name": "系统可靠性学习", "path": "node/题目/日期/2025-7-17/系统可靠性学习.html"}, {"name": "系统性能优化学习", "path": "node/题目/日期/2025-7-17/系统性能优化学习.html"}, {"name": "系统性能调整学习", "path": "node/题目/日期/2025-7-17/系统性能调整学习.html"}, {"name": "软件可靠性学习", "path": "node/题目/日期/2025-7-17/软件可靠性学习.html"}, {"name": "软件开发方法互动学习-新版", "path": "node/题目/日期/2025-7-17/软件开发方法互动学习-新版.html"}, {"name": "软件开发方法学习", "path": "node/题目/日期/2025-7-17/软件开发方法学习.html"}, {"name": "软件架构风格学习", "path": "node/题目/日期/2025-7-17/软件架构风格学习.html"}, {"name": "软件质量属性学习", "path": "node/题目/日期/2025-7-17/软件质量属性学习.html"}, {"name": "软件质量属性学习2", "path": "node/题目/日期/2025-7-17/软件质量属性学习2.html"}]}, {"name": "2025-7-18", "path": "node/题目/日期/2025-7-18", "icon": "el-icon-folder", "files": [{"name": "ejb-learning", "path": "node/题目/日期/2025-7-18/ejb-learning.html"}, {"name": "ejb-question2", "path": "node/题目/日期/2025-7-18/ejb-question2.html"}, {"name": "ejb-question3", "path": "node/题目/日期/2025-7-18/ejb-question3.html"}, {"name": "microkernel-os-learning", "path": "node/题目/日期/2025-7-18/microkernel-os-learning.html"}, {"name": "microkernel-os-learning2", "path": "node/题目/日期/2025-7-18/microkernel-os-learning2.html"}, {"name": "multiprocessor-learning", "path": "node/题目/日期/2025-7-18/multiprocessor-learning.html"}, {"name": "process-scheduling-game", "path": "node/题目/日期/2025-7-18/process-scheduling-game.html"}, {"name": "workflow-modeling", "path": "node/题目/日期/2025-7-18/workflow-modeling.html"}, {"name": "多机系统耦合学习", "path": "node/题目/日期/2025-7-18/多机系统耦合学习.html"}, {"name": "操作系统死锁教学", "path": "node/题目/日期/2025-7-18/操作系统死锁教学.html"}, {"name": "软件开发方法学习", "path": "node/题目/日期/2025-7-18/软件开发方法学习.html"}, {"name": "软件开发方法学习2", "path": "node/题目/日期/2025-7-18/软件开发方法学习2.html"}, {"name": "软件架构学习", "path": "node/题目/日期/2025-7-18/软件架构学习.html"}, {"name": "软件架构学习2", "path": "node/题目/日期/2025-7-18/软件架构学习2.html"}, {"name": "软件架构学习3", "path": "node/题目/日期/2025-7-18/软件架构学习3.html"}, {"name": "软件需求分析学习", "path": "node/题目/日期/2025-7-18/软件需求分析学习.html"}, {"name": "软件需求分析学习2", "path": "node/题目/日期/2025-7-18/软件需求分析学习2.html"}, {"name": "软件需求分析学习3", "path": "node/题目/日期/2025-7-18/软件需求分析学习3.html"}]}, {"name": "2025-7-19", "path": "node/题目/日期/2025-7-19", "icon": "el-icon-folder", "files": [{"name": "component-assembly-learning", "path": "node/题目/日期/2025-7-19/component-assembly-learning.html"}, {"name": "component-assembly-mismatch-learning-2", "path": "node/题目/日期/2025-7-19/component-assembly-mismatch-learning-2.html"}, {"name": "component-assembly-mismatch-learning", "path": "node/题目/日期/2025-7-19/component-assembly-mismatch-learning.html"}, {"name": "component-learning", "path": "node/题目/日期/2025-7-19/component-learning.html"}, {"name": "corba-components-learning", "path": "node/题目/日期/2025-7-19/corba-components-learning.html"}, {"name": "data-asset-features-learning", "path": "node/题目/日期/2025-7-19/data-asset-features-learning.html"}, {"name": "development-models-learning-2", "path": "node/题目/日期/2025-7-19/development-models-learning-2.html"}, {"name": "development-models-learning-3", "path": "node/题目/日期/2025-7-19/development-models-learning-3.html"}, {"name": "development-models-learning-4", "path": "node/题目/日期/2025-7-19/development-models-learning-4.html"}, {"name": "development-models-learning", "path": "node/题目/日期/2025-7-19/development-models-learning.html"}, {"name": "flash-memory-learning", "path": "node/题目/日期/2025-7-19/flash-memory-learning.html"}, {"name": "javaee-integration-learning", "path": "node/题目/日期/2025-7-19/javaee-integration-learning.html"}, {"name": "omg-idl-learning-2", "path": "node/题目/日期/2025-7-19/omg-idl-learning-2.html"}, {"name": "omg-idl-learning", "path": "node/题目/日期/2025-7-19/omg-idl-learning.html"}, {"name": "software-reuse-learning", "path": "node/题目/日期/2025-7-19/software-reuse-learning.html"}, {"name": "supply-chain-learning-2", "path": "node/题目/日期/2025-7-19/supply-chain-learning-2.html"}, {"name": "supply-chain-learning", "path": "node/题目/日期/2025-7-19/supply-chain-learning.html"}]}, {"name": "2025-7-2", "path": "node/题目/日期/2025-7-2", "icon": "el-icon-folder", "files": [{"name": "absd_interactive_explanation", "path": "node/题目/日期/2025-7-2/absd_interactive_explanation.html"}, {"name": "absd_interactive_explanation_v2", "path": "node/题目/日期/2025-7-2/absd_interactive_explanation_v2.html"}, {"name": "absd_interactive_explanation_v3", "path": "node/题目/日期/2025-7-2/absd_interactive_explanation_v3.html"}, {"name": "absd_interactive_explanation_v4", "path": "node/题目/日期/2025-7-2/absd_interactive_explanation_v4.html"}, {"name": "absd_interactive_explanation_v5", "path": "node/题目/日期/2025-7-2/absd_interactive_explanation_v5.html"}, {"name": "database_dump_interactive", "path": "node/题目/日期/2025-7-2/database_dump_interactive.html"}, {"name": "disk_io_explanation", "path": "node/题目/日期/2025-7-2/disk_io_explanation.html"}, {"name": "dssa_interactive_explanation", "path": "node/题目/日期/2025-7-2/dssa_interactive_explanation.html"}, {"name": "microprogram_explanation", "path": "node/题目/日期/2025-7-2/microprogram_explanation.html"}, {"name": "omt_explanation", "path": "node/题目/日期/2025-7-2/omt_explanation.html"}, {"name": "process_synchronization_interactive_tutorial", "path": "node/题目/日期/2025-7-2/process_synchronization_interactive_tutorial.html"}, {"name": "process_sync_explanation", "path": "node/题目/日期/2025-7-2/process_sync_explanation.html"}, {"name": "snmpv3_security_interactive", "path": "node/题目/日期/2025-7-2/snmpv3_security_interactive.html"}, {"name": "software_architecture_evaluation_interactive", "path": "node/题目/日期/2025-7-2/software_architecture_evaluation_interactive.html"}, {"name": "software_architecture_evaluation_interactive_v2", "path": "node/题目/日期/2025-7-2/software_architecture_evaluation_interactive_v2.html"}, {"name": "software_model_explanation", "path": "node/题目/日期/2025-7-2/software_model_explanation.html"}, {"name": "software_tools_explanation_interactive", "path": "node/题目/日期/2025-7-2/software_tools_explanation_interactive.html"}, {"name": "trap_interrupt_explanation", "path": "node/题目/日期/2025-7-2/trap_interrupt_explanation.html"}, {"name": "up_interactive_explanation", "path": "node/题目/日期/2025-7-2/up_interactive_explanation.html"}]}, {"name": "2025-7-20", "path": "node/题目/日期/2025-7-20", "icon": "el-icon-folder", "files": [{"name": "absd-architecture-learning", "path": "node/题目/日期/2025-7-20/absd-architecture-learning.html"}, {"name": "data-storage-learning", "path": "node/题目/日期/2025-7-20/data-storage-learning.html"}, {"name": "design-patterns-bridge", "path": "node/题目/日期/2025-7-20/design-patterns-bridge.html"}, {"name": "design-patterns-interactive", "path": "node/题目/日期/2025-7-20/design-patterns-interactive.html"}, {"name": "design-patterns-learning", "path": "node/题目/日期/2025-7-20/design-patterns-learning.html"}, {"name": "design-patterns-learning2", "path": "node/题目/日期/2025-7-20/design-patterns-learning2.html"}, {"name": "design-patterns-learning3", "path": "node/题目/日期/2025-7-20/design-patterns-learning3.html"}, {"name": "digital-signature-learning", "path": "node/题目/日期/2025-7-20/digital-signature-learning.html"}, {"name": "eai-learning", "path": "node/题目/日期/2025-7-20/eai-learning.html"}, {"name": "eai-learning2", "path": "node/题目/日期/2025-7-20/eai-learning2.html"}, {"name": "IDE架构风格学习", "path": "node/题目/日期/2025-7-20/IDE架构风格学习.html"}, {"name": "oo-analysis-design-learning", "path": "node/题目/日期/2025-7-20/oo-analysis-design-learning.html"}, {"name": "operation-concept-learning", "path": "node/题目/日期/2025-7-20/operation-concept-learning.html"}, {"name": "package-diagram-learning", "path": "node/题目/日期/2025-7-20/package-diagram-learning.html"}, {"name": "service-oriented-learning", "path": "node/题目/日期/2025-7-20/service-oriented-learning.html"}, {"name": "service-oriented-learning2", "path": "node/题目/日期/2025-7-20/service-oriented-learning2.html"}, {"name": "software-architecture-learning", "path": "node/题目/日期/2025-7-20/software-architecture-learning.html"}, {"name": "state-diagram-learning", "path": "node/题目/日期/2025-7-20/state-diagram-learning.html"}, {"name": "sysml-complete-learning", "path": "node/题目/日期/2025-7-20/sysml-complete-learning.html"}, {"name": "sysml-vs-uml-interactive", "path": "node/题目/日期/2025-7-20/sysml-vs-uml-interactive.html"}, {"name": "system-testing-learning", "path": "node/题目/日期/2025-7-20/system-testing-learning.html"}, {"name": "UML类图关联学习", "path": "node/题目/日期/2025-7-20/UML类图关联学习.html"}, {"name": "可测试性质量属性学习", "path": "node/题目/日期/2025-7-20/可测试性质量属性学习.html"}, {"name": "构件组装失配问题教学", "path": "node/题目/日期/2025-7-20/构件组装失配问题教学.html"}, {"name": "架构概念学习游戏", "path": "node/题目/日期/2025-7-20/架构概念学习游戏.html"}, {"name": "架构策略选择学习", "path": "node/题目/日期/2025-7-20/架构策略选择学习.html"}, {"name": "软件架构标准学习", "path": "node/题目/日期/2025-7-20/软件架构标准学习.html"}, {"name": "软件架构风格学习", "path": "node/题目/日期/2025-7-20/软件架构风格学习.html"}, {"name": "软件架构风格学习2", "path": "node/题目/日期/2025-7-20/软件架构风格学习2.html"}, {"name": "软件质量属性学习", "path": "node/题目/日期/2025-7-20/软件质量属性学习.html"}, {"name": "软件质量属性学习3", "path": "node/题目/日期/2025-7-20/软件质量属性学习3.html"}, {"name": "面向对象类分类学习", "path": "node/题目/日期/2025-7-20/面向对象类分类学习.html"}, {"name": "面向对象类分类教学", "path": "node/题目/日期/2025-7-20/面向对象类分类教学.html"}]}, {"name": "2025-7-21", "path": "node/题目/日期/2025-7-21", "icon": "el-icon-folder", "files": [{"name": "architecture-assessment-interactive", "path": "node/题目/日期/2025-7-21/architecture-assessment-interactive.html"}, {"name": "architecture-assessment-learning", "path": "node/题目/日期/2025-7-21/architecture-assessment-learning.html"}, {"name": "bridge-pattern-learning", "path": "node/题目/日期/2025-7-21/bridge-pattern-learning.html"}, {"name": "component-based-development", "path": "node/题目/日期/2025-7-21/component-based-development.html"}, {"name": "design-patterns-bridge", "path": "node/题目/日期/2025-7-21/design-patterns-bridge.html"}, {"name": "design-patterns-learning", "path": "node/题目/日期/2025-7-21/design-patterns-learning.html"}, {"name": "design-patterns-story", "path": "node/题目/日期/2025-7-21/design-patterns-story.html"}, {"name": "RFID学习", "path": "node/题目/日期/2025-7-21/RFID学习.html"}, {"name": "software-quality-interactive", "path": "node/题目/日期/2025-7-21/software-quality-interactive.html"}, {"name": "构件开发模型学习", "path": "node/题目/日期/2025-7-21/构件开发模型学习.html"}, {"name": "软件架构风格学习", "path": "node/题目/日期/2025-7-21/软件架构风格学习.html"}, {"name": "面向对象分析设计学习", "path": "node/题目/日期/2025-7-21/面向对象分析设计学习.html"}]}, {"name": "2025-7-3", "path": "node/题目/日期/2025-7-3", "icon": "el-icon-folder", "files": [{"name": "crc_calculator", "path": "node/题目/日期/2025-7-3/crc_calculator.html"}, {"name": "embedded_database_quiz", "path": "node/题目/日期/2025-7-3/embedded_database_quiz.html"}, {"name": "embedded_systems", "path": "node/题目/日期/2025-7-3/embedded_systems.html"}, {"name": "embedded_systems_learning", "path": "node/题目/日期/2025-7-3/embedded_systems_learning.html"}, {"name": "event_driven_architecture", "path": "node/题目/日期/2025-7-3/event_driven_architecture.html"}, {"name": "file_system_index", "path": "node/题目/日期/2025-7-3/file_system_index.html"}, {"name": "index", "path": "node/题目/日期/2025-7-3/index.html"}, {"name": "index2", "path": "node/题目/日期/2025-7-3/index2.html"}, {"name": "index3", "path": "node/题目/日期/2025-7-3/index3.html"}, {"name": "index4", "path": "node/题目/日期/2025-7-3/index4.html"}, {"name": "kernel_architecture_explanation", "path": "node/题目/日期/2025-7-3/kernel_architecture_explanation.html"}, {"name": "windows_architecture_tutorial", "path": "node/题目/日期/2025-7-3/windows_architecture_tutorial.html"}, {"name": "文件系统索引节点演示", "path": "node/题目/日期/2025-7-3/文件系统索引节点演示.html"}, {"name": "构件分类方法", "path": "node/题目/日期/2025-7-3/构件分类方法.html"}, {"name": "构件分类方法学习", "path": "node/题目/日期/2025-7-3/构件分类方法学习.html"}, {"name": "航班预订系统演示", "path": "node/题目/日期/2025-7-3/航班预订系统演示.html"}, {"name": "软件构件分类方法", "path": "node/题目/日期/2025-7-3/软件构件分类方法.html"}, {"name": "面向对象分析教学", "path": "node/题目/日期/2025-7-3/面向对象分析教学.html"}, {"name": "面向对象分析教学2", "path": "node/题目/日期/2025-7-3/面向对象分析教学2.html"}, {"name": "面向对象分析教学_2025-7-3", "path": "node/题目/日期/2025-7-3/面向对象分析教学_2025-7-3.html"}]}, {"name": "2025-7-6", "path": "node/题目/日期/2025-7-6", "icon": "el-icon-folder", "files": [{"name": "2025-7-6_computer_memory_explainer", "path": "node/题目/日期/2025-7-6/2025-7-6_computer_memory_explainer.html"}, {"name": "index", "path": "node/题目/日期/2025-7-6/index.html"}, {"name": "index2", "path": "node/题目/日期/2025-7-6/index2.html"}, {"name": "index3", "path": "node/题目/日期/2025-7-6/index3.html"}, {"name": "index4", "path": "node/题目/日期/2025-7-6/index4.html"}, {"name": "index7", "path": "node/题目/日期/2025-7-6/index7.html"}, {"name": "index8", "path": "node/题目/日期/2025-7-6/index8.html"}, {"name": "index9", "path": "node/题目/日期/2025-7-6/index9.html"}, {"name": "中断笔记", "path": "node/题目/日期/2025-7-6/中断笔记.html"}, {"name": "任务执行时间笔记", "path": "node/题目/日期/2025-7-6/任务执行时间笔记.html"}, {"name": "倒数笔记", "path": "node/题目/日期/2025-7-6/倒数笔记.html"}, {"name": "分解笔记", "path": "node/题目/日期/2025-7-6/分解笔记.html"}, {"name": "后进先出笔记-历史", "path": "node/题目/日期/2025-7-6/后进先出笔记-历史.html"}, {"name": "命令模式笔记", "path": "node/题目/日期/2025-7-6/命令模式笔记.html"}, {"name": "构建笔记", "path": "node/题目/日期/2025-7-6/构建笔记.html"}, {"name": "状态模式", "path": "node/题目/日期/2025-7-6/状态模式.html"}, {"name": "知识前驱图笔记", "path": "node/题目/日期/2025-7-6/知识前驱图笔记.html"}, {"name": "知识吸收笔记", "path": "node/题目/日期/2025-7-6/知识吸收笔记.html"}, {"name": "笔记系统外观模式", "path": "node/题目/日期/2025-7-6/笔记系统外观模式.html"}, {"name": "策略模式笔记", "path": "node/题目/日期/2025-7-6/策略模式笔记.html"}, {"name": "聚合笔记笔记", "path": "node/题目/日期/2025-7-6/聚合笔记笔记.html"}]}, {"name": "2025-7-7", "path": "node/题目/日期/2025-7-7", "icon": "el-icon-folder", "files": [{"name": "ABSD方法 - 架构演化学习游戏", "path": "node/题目/日期/2025-7-7/ABSD方法 - 架构演化学习游戏.html"}, {"name": "compiler_architecture_explainer", "path": "node/题目/日期/2025-7-7/compiler_architecture_explainer.html"}, {"name": "database_design_quiz", "path": "node/题目/日期/2025-7-7/database_design_quiz.html"}, {"name": "e_commerce_explainer", "path": "node/题目/日期/2025-7-7/e_commerce_explainer.html"}, {"name": "functional_vs_nonfunctional_requirements", "path": "node/题目/日期/2025-7-7/functional_vs_nonfunctional_requirements.html"}, {"name": "index", "path": "node/题目/日期/2025-7-7/index.html"}, {"name": "index2", "path": "node/题目/日期/2025-7-7/index2.html"}, {"name": "index3", "path": "node/题目/日期/2025-7-7/index3.html"}, {"name": "index4", "path": "node/题目/日期/2025-7-7/index4.html"}, {"name": "index5", "path": "node/题目/日期/2025-7-7/index5.html"}, {"name": "index6", "path": "node/题目/日期/2025-7-7/index6.html"}, {"name": "index7", "path": "node/题目/日期/2025-7-7/index7.html"}, {"name": "index8", "path": "node/题目/日期/2025-7-7/index8.html"}, {"name": "software_architecture_explanation", "path": "node/题目/日期/2025-7-7/software_architecture_explanation.html"}, {"name": "system_analysis_lesson", "path": "node/题目/日期/2025-7-7/system_analysis_lesson.html"}, {"name": "数据共享", "path": "node/题目/日期/2025-7-7/数据共享.html"}, {"name": "架构复审", "path": "node/题目/日期/2025-7-7/架构复审.html"}, {"name": "架构风格", "path": "node/题目/日期/2025-7-7/架构风格.html"}, {"name": "隐式调用", "path": "node/题目/日期/2025-7-7/隐式调用.html"}, {"name": "顺序批处理", "path": "node/题目/日期/2025-7-7/顺序批处理.html"}]}, {"name": "2025-7-8", "path": "node/题目/日期/2025-7-8", "icon": "el-icon-folder", "files": [{"name": "index", "path": "node/题目/日期/2025-7-8/index.html"}, {"name": "index2", "path": "node/题目/日期/2025-7-8/index2.html"}, {"name": "index3", "path": "node/题目/日期/2025-7-8/index3.html"}]}, {"name": "2025-7-9", "path": "node/题目/日期/2025-7-9", "icon": "el-icon-folder", "files": [{"name": "design_patterns_explained", "path": "node/题目/日期/2025-7-9/design_patterns_explained.html"}, {"name": "design_patterns_game", "path": "node/题目/日期/2025-7-9/design_patterns_game.html"}, {"name": "design_patterns_quiz", "path": "node/题目/日期/2025-7-9/design_patterns_quiz.html"}, {"name": "index", "path": "node/题目/日期/2025-7-9/index.html"}, {"name": "index2", "path": "node/题目/日期/2025-7-9/index2.html"}, {"name": "index3", "path": "node/题目/日期/2025-7-9/index3.html"}]}]}, {"name": "题库", "path": "node/题目/题库", "icon": "el-icon-receiving", "files": [], "children": [{"name": "第一章 绪论（新版教材）", "path": "node/题目/题库/第一章 绪论（新版教材）", "icon": "el-icon-folder", "files": [], "children": [{"name": "第一节 系统架构概述", "path": "node/题目/题库/第一章 绪论（新版教材）/第一节 系统架构概述", "icon": "el-icon-folder", "files": [{"name": "1.enterprise-integration-learning", "path": "node/题目/题库/第一章 绪论（新版教材）/第一节 系统架构概述/1.enterprise-integration-learning.html"}, {"name": "2.企业应用集成学习", "path": "node/题目/题库/第一章 绪论（新版教材）/第一节 系统架构概述/2.企业应用集成学习.html"}, {"name": "3.architecture_standard_learning", "path": "node/题目/题库/第一章 绪论（新版教材）/第一节 系统架构概述/3.architecture_standard_learning.html"}, {"name": "4.软件架构标准学习", "path": "node/题目/题库/第一章 绪论（新版教材）/第一节 系统架构概述/4.软件架构标准学习.html"}, {"name": "5.软件架构标准学习", "path": "node/题目/题库/第一章 绪论（新版教材）/第一节 系统架构概述/5.软件架构标准学习.html"}, {"name": "6.软件方法学学习", "path": "node/题目/题库/第一章 绪论（新版教材）/第一节 系统架构概述/6.软件方法学学习.html"}, {"name": "7.软件方法学学习", "path": "node/题目/题库/第一章 绪论（新版教材）/第一节 系统架构概述/7.软件方法学学习.html"}, {"name": "8.软件方法学学习", "path": "node/题目/题库/第一章 绪论（新版教材）/第一节 系统架构概述/8.软件方法学学习.html"}]}]}, {"name": "第三章 信息系统基础知识（新版教材）", "path": "node/题目/题库/第三章 信息系统基础知识（新版教材）", "icon": "el-icon-folder", "files": []}, {"name": "第二章 计算机系统基础知识（新版教材）", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）", "icon": "el-icon-folder", "files": [], "children": [{"name": "第一节 计算机硬件", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件", "icon": "el-icon-folder", "files": [{"name": "ai-chip-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/ai-chip-learning.html"}, {"name": "armstrong-axioms-game", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/armstrong-axioms-game.html"}, {"name": "cache-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/cache-learning.html"}, {"name": "cache-learning2", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/cache-learning2.html"}, {"name": "cache-memory-hierarchy", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/cache-memory-hierarchy.html"}, {"name": "cisc-risc-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/cisc-risc-learning.html"}, {"name": "cpu-data-bus-tutorial", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/cpu-data-bus-tutorial.html"}, {"name": "cpu-frequency-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/cpu-frequency-learning.html"}, {"name": "cpu-registers-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/cpu-registers-learning.html"}, {"name": "database-three-level-architecture", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/database-three-level-architecture.html"}, {"name": "disk-io-scheduler", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/disk-io-scheduler.html"}, {"name": "dsp-architecture-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/dsp-architecture-learning.html"}, {"name": "embedded_processor_mmu_learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/embedded_processor_mmu_learning.html"}, {"name": "flash_memory_learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/flash_memory_learning.html"}, {"name": "instruction-pipeline-tutorial", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/instruction-pipeline-tutorial.html"}, {"name": "instruction-pipeline-tutorial2", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/instruction-pipeline-tutorial2.html"}, {"name": "interrupt_learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/interrupt_learning.html"}, {"name": "memory-address-calculator", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/memory-address-calculator.html"}, {"name": "memory-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/memory-learning.html"}, {"name": "middleware-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/middleware-learning.html"}, {"name": "multicore-processor-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/multicore-processor-learning.html"}, {"name": "page-replacement-tutorial", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/page-replacement-tutorial.html"}, {"name": "pipeline-interactive-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/pipeline-interactive-learning.html"}, {"name": "pipeline-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/pipeline-learning.html"}, {"name": "pipeline_speedup_learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/pipeline_speedup_learning.html"}, {"name": "pipeline_tutorial", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/pipeline_tutorial.html"}, {"name": "raid6-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/raid6-learning.html"}, {"name": "risc-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/risc-learning.html"}, {"name": "risc-learning2", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/risc-learning2.html"}, {"name": "software-reliability-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/software-reliability-learning.html"}, {"name": "ssd_learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/ssd_learning.html"}, {"name": "storage-speed-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/storage-speed-learning.html"}, {"name": "串行总线学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/串行总线学习.html"}, {"name": "事务服务器系统学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/事务服务器系统学习.html"}, {"name": "博弈论教学", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/博弈论教学.html"}, {"name": "双缓冲教学演示", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/双缓冲教学演示.html"}, {"name": "存储器存取方式教学", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/存储器存取方式教学.html"}, {"name": "存储器寻址方式学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/存储器寻址方式学习.html"}, {"name": "存储器访问方式学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/存储器访问方式学习.html"}, {"name": "嵌入式处理器MMU学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/嵌入式处理器MMU学习.html"}, {"name": "嵌入式处理器学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/嵌入式处理器学习.html"}, {"name": "嵌入式操作系统学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/嵌入式操作系统学习.html"}, {"name": "总线知识学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/总线知识学习.html"}, {"name": "指令周期学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/指令周期学习.html"}, {"name": "流水线吞吐率教学", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/流水线吞吐率教学.html"}, {"name": "流水线指令执行教学", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/流水线指令执行教学.html"}, {"name": "消息中间件互动学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/消息中间件互动学习.html"}, {"name": "磁盘IO学习游戏", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/磁盘IO学习游戏.html"}, {"name": "磁盘IO调度动画教学", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/磁盘IO调度动画教学.html"}, {"name": "磁盘存储原理教学", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/磁盘存储原理教学.html"}, {"name": "系统集成教学页面", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/系统集成教学页面.html"}, {"name": "缓冲区管理学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/缓冲区管理学习.html"}, {"name": "网络安全协议学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/网络安全协议学习.html"}, {"name": "页面地址转换学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机硬件/页面地址转换学习.html"}]}, {"name": "第一节 计算机系统概述", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机系统概述", "icon": "el-icon-folder", "files": [{"name": "crc-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机系统概述/crc-learning.html"}, {"name": "storage-hierarchy-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机系统概述/storage-hierarchy-learning.html"}, {"name": "system-monitoring-interactive-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机系统概述/system-monitoring-interactive-learning.html"}, {"name": "system-monitoring-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第一节 计算机系统概述/system-monitoring-learning.html"}]}, {"name": "第三节 嵌入式系统及软件", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第三节 嵌入式系统及软件", "icon": "el-icon-folder", "files": [{"name": "bsp-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第三节 嵌入式系统及软件/bsp-learning.html"}, {"name": "embedded-software-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第三节 嵌入式系统及软件/embedded-software-learning.html"}, {"name": "ndb-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第三节 嵌入式系统及软件/ndb-learning.html"}, {"name": "嵌入式操作系统学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第三节 嵌入式系统及软件/嵌入式操作系统学习.html"}, {"name": "硬件抽象层学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第三节 嵌入式系统及软件/硬件抽象层学习.html"}, {"name": "网络流量控制学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第三节 嵌入式系统及软件/网络流量控制学习.html"}]}, {"name": "第二节 计算机软件", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件", "icon": "el-icon-folder", "files": [{"name": "bitmap-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/bitmap-learning.html"}, {"name": "bitmap_learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/bitmap_learning.html"}, {"name": "database-security-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/database-security-learning.html"}, {"name": "process-sync-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/process-sync-learning.html"}, {"name": "process_synchronization", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/process_synchronization.html"}, {"name": "process_sync_tutorial", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/process_sync_tutorial.html"}, {"name": "pv-operation-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/pv-operation-learning.html"}, {"name": "pv-operation-learning2", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/pv-operation-learning2.html"}, {"name": "pv-operation-learning4", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/pv-operation-learning4.html"}, {"name": "pv-operation-tutorial", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/pv-operation-tutorial.html"}, {"name": "pv_operation_learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/pv_operation_learning.html"}, {"name": "rtos-learning", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/rtos-learning.html"}, {"name": "前趋图学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/前趋图学习.html"}, {"name": "前趋图学习2", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/前趋图学习2.html"}, {"name": "数据资产特性学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/数据资产特性学习.html"}, {"name": "文件系统索引节点学习", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/文件系统索引节点学习.html"}, {"name": "文件系统索引节点学习2", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/文件系统索引节点学习2.html"}, {"name": "文件系统索引节点学习3", "path": "node/题目/题库/第二章 计算机系统基础知识（新版教材）/第二节 计算机软件/文件系统索引节点学习3.html"}]}]}, {"name": "第四章 信息安全技术基础知识（新版教材）", "path": "node/题目/题库/第四章 信息安全技术基础知识（新版教材）", "icon": "el-icon-folder", "files": []}]}]}]