<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PV操作 - 进程同步学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8em;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .process-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .process-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .process-card:hover {
            transform: scale(1.05);
        }

        .semaphore-display {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .semaphore {
            background: white;
            border: 3px solid #667eea;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            min-width: 100px;
            transition: all 0.3s ease;
        }

        .semaphore.active {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .answer-section {
            background: #e8f5e8;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #4caf50;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #4caf50;
            color: white;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 1s infinite;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 PV操作 - 进程同步控制</h1>
            <p>零基础学习进程同步的PV操作原理</p>
        </div>

        <!-- 题目展示 -->
        <div class="section fade-in">
            <h2 class="section-title">📝 题目</h2>
            <div class="explanation">
                <h3>题目描述：</h3>
                <p>进程P1、P2、P3、P4、P5的前趋图如图所示。</p>
                <p>若用PV操作控制进程并发执行的过程，则需要设置4个信号量S1、S2、S3和S4，且信号量初值都等于零。</p>
                <p>图中a和b应分别填写（ ），c和d应分别填写（ ），e和f应分别填写（ ）。</p>
                <br>
                <h3>选项：</h3>
                <div style="background: #f0f8ff; padding: 15px; border-radius: 10px; margin: 10px 0;">
                    <p><strong>A.</strong> P(S1)、P(S2)和V(S3)、V(S4)</p>
                    <p><strong>B.</strong> P(S1)、P(S2)和P(S3)、P(S4)</p>
                    <p><strong>C.</strong> V(S1)、V(S2)和P(S3)、P(S4)</p>
                    <p><strong>D.</strong> V(S1)、V(S2)和V(S3)、V(S4)</p>
                </div>
                <div style="background: #fff3cd; padding: 15px; border-radius: 10px; border-left: 4px solid #ffc107;">
                    <p><strong>🎯 你的任务：</strong>根据前趋图，确定每个位置应该填写什么PV操作</p>
                </div>
            </div>
        </div>

        <!-- 基础概念介绍 -->
        <div class="section fade-in">
            <h2 class="section-title">📚 基础概念</h2>
            <div class="explanation">
                <h3>什么是PV操作？</h3>
                <p><strong>P操作（Wait）</strong>：等待信号量，如果信号量>0则减1并继续执行，否则阻塞等待</p>
                <p><strong>V操作（Signal）</strong>：释放信号量，将信号量加1，唤醒等待的进程</p>
                <br>
                <h3>信号量的作用：</h3>
                <p>🔒 控制进程的执行顺序，确保前趋关系得到满足</p>
                <p>🚦 就像交通信号灯，控制进程的"通行"</p>
            </div>
        </div>

        <!-- 题目图示 -->
        <div class="section fade-in">
            <h2 class="section-title">🖼️ 题目图示</h2>
            <div class="canvas-container">
                <canvas id="questionCanvas" width="700" height="350"></canvas>
            </div>
            <div class="explanation">
                <p><strong>图中标注说明：</strong></p>
                <p>• <span style="color: #ff6b6b; font-weight: bold;">a, b</span>：P1和P2结束时的操作</p>
                <p>• <span style="color: #45b7d1; font-weight: bold;">c, d</span>：P3开始和结束时的操作</p>
                <p>• <span style="color: #96ceb4; font-weight: bold;">e, f</span>：P4和P5开始时的操作</p>
            </div>
        </div>

        <!-- 前趋图展示 -->
        <div class="section fade-in">
            <h2 class="section-title">🗺️ 前趋图分析</h2>
            <div class="canvas-container">
                <canvas id="precedenceCanvas" width="600" height="300"></canvas>
            </div>
            <div class="explanation">
                <p><strong>前趋关系：</strong></p>
                <p>• P1 和 P2 可以并发执行（没有依赖关系）</p>
                <p>• P3 必须等待 P1 和 P2 都完成后才能执行</p>
                <p>• P4 和 P5 必须等待 P3 完成后才能执行</p>
            </div>
        </div>

        <!-- 信号量设置 -->
        <div class="section fade-in">
            <h2 class="section-title">🚦 信号量设置</h2>
            <div class="semaphore-display">
                <div class="semaphore" id="s1">
                    <h3>S1</h3>
                    <p>初值: 0</p>
                    <p>P1→P3</p>
                </div>
                <div class="semaphore" id="s2">
                    <h3>S2</h3>
                    <p>初值: 0</p>
                    <p>P2→P3</p>
                </div>
                <div class="semaphore" id="s3">
                    <h3>S3</h3>
                    <p>初值: 0</p>
                    <p>P3→P4</p>
                </div>
                <div class="semaphore" id="s4">
                    <h3>S4</h3>
                    <p>初值: 0</p>
                    <p>P3→P5</p>
                </div>
            </div>
        </div>

        <!-- PV操作演示 -->
        <div class="section fade-in">
            <h2 class="section-title">🎬 PV操作演示</h2>
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
                <div class="step" id="step5">5</div>
            </div>
            <div class="canvas-container">
                <canvas id="pvCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="startDemo()">🎬 开始演示</button>
                <button class="btn" onclick="nextStep()">➡️ 下一步</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>
        </div>

        <!-- 答案解析 -->
        <div class="section fade-in">
            <h2 class="section-title">✅ 答案解析</h2>
            <div class="answer-section">
                <h3>正确答案：A</h3>
                <div class="process-info">
                    <div class="process-card">
                        <h4>a, b 位置</h4>
                        <p>P1结束: V(S1)</p>
                        <p>P2结束: V(S2)</p>
                        <p>释放信号，通知P3</p>
                    </div>
                    <div class="process-card">
                        <h4>c, d 位置</h4>
                        <p>P3开始: P(S1), P(S2)</p>
                        <p>P3结束: V(S3), V(S4)</p>
                        <p>等待P1,P2，通知P4,P5</p>
                    </div>
                    <div class="process-card">
                        <h4>e, f 位置</h4>
                        <p>P4开始: P(S3)</p>
                        <p>P5开始: P(S4)</p>
                        <p>等待P3完成</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 思路总结 -->
        <div class="section fade-in">
            <h2 class="section-title">💡 解题思路</h2>
            <div class="explanation">
                <h3>🎯 关键思路：</h3>
                <ol>
                    <li><strong>识别前趋关系</strong>：找出哪些进程需要等待其他进程</li>
                    <li><strong>设置信号量</strong>：每个依赖关系对应一个信号量</li>
                    <li><strong>V操作位置</strong>：在被依赖的进程结束时执行</li>
                    <li><strong>P操作位置</strong>：在需要等待的进程开始时执行</li>
                </ol>
                <br>
                <h3>🔍 记忆口诀：</h3>
                <p><strong>"先V后P，发送接收"</strong></p>
                <p>• V操作：发送信号（进程完成时）</p>
                <p>• P操作：接收信号（进程开始前）</p>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let demoRunning = false;

        // 进程状态
        const processes = {
            P1: { x: 100, y: 100, status: 'ready', color: '#ff6b6b' },
            P2: { x: 100, y: 200, status: 'ready', color: '#4ecdc4' },
            P3: { x: 300, y: 150, status: 'waiting', color: '#45b7d1' },
            P4: { x: 500, y: 100, status: 'waiting', color: '#96ceb4' },
            P5: { x: 500, y: 200, status: 'waiting', color: '#feca57' }
        };

        // 信号量状态
        const semaphores = {
            S1: { value: 0, active: false },
            S2: { value: 0, active: false },
            S3: { value: 0, active: false },
            S4: { value: 0, active: false }
        };

        // 绘制题目图示
        function drawQuestionDiagram() {
            const canvas = document.getElementById('questionCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制进程框
            const processes = [
                { name: 'P1', x: 50, y: 50, width: 80, height: 60 },
                { name: 'P2', x: 50, y: 150, width: 80, height: 60 },
                { name: 'P3', x: 250, y: 100, width: 120, height: 100 },
                { name: 'P4', x: 450, y: 50, width: 80, height: 60 },
                { name: 'P5', x: 450, y: 150, width: 80, height: 60 }
            ];

            // 绘制进程框
            processes.forEach(p => {
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(p.x, p.y, p.width, p.height);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(p.x, p.y, p.width, p.height);

                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(p.name, p.x + p.width/2, p.y + p.height/2 + 5);
            });

            // 绘制PV操作标注
            ctx.font = 'bold 14px Arial';
            ctx.fillStyle = '#ff6b6b';
            ctx.fillText('a', 90, 125);  // P1结束
            ctx.fillText('b', 90, 225);  // P2结束

            ctx.fillStyle = '#45b7d1';
            ctx.fillText('c', 250, 85);  // P3开始
            ctx.fillText('d', 250, 220); // P3结束

            ctx.fillStyle = '#96ceb4';
            ctx.fillText('e', 450, 125); // P4开始
            ctx.fillText('f', 450, 225); // P5开始

            // 绘制箭头和连接线
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;

            // P1 -> P3
            drawArrow(ctx, 130, 80, 250, 120);
            // P2 -> P3
            drawArrow(ctx, 130, 180, 250, 160);
            // P3 -> P4
            drawArrow(ctx, 370, 130, 450, 80);
            // P3 -> P5
            drawArrow(ctx, 370, 170, 450, 180);
        }

        // 绘制前趋图
        function drawPrecedenceGraph() {
            const canvas = document.getElementById('precedenceCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制进程节点
            const nodes = [
                { name: 'P1', x: 100, y: 100 },
                { name: 'P2', x: 100, y: 200 },
                { name: 'P3', x: 300, y: 150 },
                { name: 'P4', x: 500, y: 100 },
                { name: 'P5', x: 500, y: 200 }
            ];

            // 绘制箭头
            const arrows = [
                { from: nodes[0], to: nodes[2] }, // P1 -> P3
                { from: nodes[1], to: nodes[2] }, // P2 -> P3
                { from: nodes[2], to: nodes[3] }, // P3 -> P4
                { from: nodes[2], to: nodes[4] }  // P3 -> P5
            ];

            // 绘制箭头
            arrows.forEach(arrow => {
                drawArrow(ctx, arrow.from.x + 30, arrow.from.y, arrow.to.x - 30, arrow.to.y);
            });

            // 绘制节点
            nodes.forEach(node => {
                drawProcess(ctx, node.x, node.y, node.name, '#667eea');
            });
        }

        function drawProcess(ctx, x, y, name, color) {
            ctx.beginPath();
            ctx.arc(x, y, 25, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y + 5);
        }

        function drawArrow(ctx, fromX, fromY, toX, toY) {
            const headlen = 10;
            const dx = toX - fromX;
            const dy = toY - fromY;
            const angle = Math.atan2(dy, dx);

            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // 绘制PV操作演示
        function drawPVDemo() {
            const canvas = document.getElementById('pvCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制进程
            Object.keys(processes).forEach(name => {
                const p = processes[name];
                let color = p.color;
                if (p.status === 'running') color = '#ff9f43';
                if (p.status === 'completed') color = '#10ac84';
                if (p.status === 'waiting') color = '#ddd';

                drawProcess(ctx, p.x, p.y, name, color);

                // 绘制状态文字
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(p.status, p.x, p.y + 45);
            });

            // 绘制PV操作标注
            drawPVOperations(ctx);
        }

        function drawPVOperations(ctx) {
            ctx.fillStyle = '#333';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';

            // 根据当前步骤显示不同的PV操作
            if (currentStep >= 1) {
                ctx.fillText('V(S1)', 130, 85);
                ctx.fillText('V(S2)', 130, 185);
            }
            if (currentStep >= 2) {
                ctx.fillText('P(S1), P(S2)', 300, 120);
            }
            if (currentStep >= 3) {
                ctx.fillText('V(S3), V(S4)', 300, 180);
            }
            if (currentStep >= 4) {
                ctx.fillText('P(S3)', 500, 85);
                ctx.fillText('P(S4)', 500, 185);
            }
        }

        function startDemo() {
            demoRunning = true;
            currentStep = 0;
            resetProcesses();
            nextStep();
        }

        function nextStep() {
            if (!demoRunning) return;

            currentStep++;
            updateStepIndicator();

            switch(currentStep) {
                case 1:
                    // P1, P2 开始执行
                    processes.P1.status = 'running';
                    processes.P2.status = 'running';
                    setTimeout(() => {
                        processes.P1.status = 'completed';
                        processes.P2.status = 'completed';
                        semaphores.S1.value = 1;
                        semaphores.S2.value = 1;
                        updateSemaphoreDisplay();
                        drawPVDemo();
                    }, 1000);
                    break;
                case 2:
                    // P3 开始执行
                    processes.P3.status = 'running';
                    semaphores.S1.value = 0;
                    semaphores.S2.value = 0;
                    updateSemaphoreDisplay();
                    setTimeout(() => {
                        processes.P3.status = 'completed';
                        semaphores.S3.value = 1;
                        semaphores.S4.value = 1;
                        updateSemaphoreDisplay();
                        drawPVDemo();
                    }, 1000);
                    break;
                case 3:
                    // P4, P5 开始执行
                    processes.P4.status = 'running';
                    processes.P5.status = 'running';
                    semaphores.S3.value = 0;
                    semaphores.S4.value = 0;
                    updateSemaphoreDisplay();
                    setTimeout(() => {
                        processes.P4.status = 'completed';
                        processes.P5.status = 'completed';
                        drawPVDemo();
                    }, 1000);
                    break;
            }

            drawPVDemo();
        }

        function resetDemo() {
            demoRunning = false;
            currentStep = 0;
            resetProcesses();
            resetSemaphores();
            updateStepIndicator();
            updateSemaphoreDisplay();
            drawPVDemo();
        }

        function resetProcesses() {
            processes.P1.status = 'ready';
            processes.P2.status = 'ready';
            processes.P3.status = 'waiting';
            processes.P4.status = 'waiting';
            processes.P5.status = 'waiting';
        }

        function resetSemaphores() {
            Object.keys(semaphores).forEach(key => {
                semaphores[key].value = 0;
                semaphores[key].active = false;
            });
        }

        function updateStepIndicator() {
            for (let i = 1; i <= 5; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active', 'completed');
                if (i === currentStep) {
                    step.classList.add('active');
                } else if (i < currentStep) {
                    step.classList.add('completed');
                }
            }
        }

        function updateSemaphoreDisplay() {
            Object.keys(semaphores).forEach(key => {
                const element = document.getElementById(key.toLowerCase());
                const sem = semaphores[key];
                element.querySelector('p').textContent = `值: ${sem.value}`;

                if (sem.active) {
                    element.classList.add('active');
                } else {
                    element.classList.remove('active');
                }
            });
        }

        // 初始化
        window.onload = function() {
            drawQuestionDiagram();
            drawPrecedenceGraph();
            drawPVDemo();
            updateSemaphoreDisplay();

            // 添加淡入动画
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '0';
                    section.style.transform = 'translateY(20px)';
                    section.style.transition = 'all 0.5s ease';
                    setTimeout(() => {
                        section.style.opacity = '1';
                        section.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        };
    </script>
</body>
</html>
