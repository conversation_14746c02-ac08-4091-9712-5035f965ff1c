<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cache-主存地址转换探索</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3.2rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .quiz-section {
            background: white;
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 50px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .quiz-title {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            margin-bottom: 40px;
            color: #444;
            text-align: center;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 10px;
            border-radius: 8px;
            font-weight: 600;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .option-card {
            background: #f8f9ff;
            border: 3px solid #e1e5f2;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            font-size: 1.2rem;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }

        .option-card:hover::before {
            left: 100%;
        }

        .option-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.25);
            border-color: #667eea;
        }

        .option-card.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: scale(1.02);
        }

        .option-card.correct {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
            animation: correctPulse 0.8s ease-out;
        }

        .option-card.incorrect {
            background: #f44336;
            color: white;
            border-color: #f44336;
            animation: shake 0.6s ease-out;
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.08); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }

        .feedback {
            text-align: center;
            padding: 30px;
            border-radius: 20px;
            margin-top: 25px;
            font-size: 1.2rem;
            font-weight: 500;
            line-height: 1.7;
            display: none;
        }

        .feedback.correct {
            background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
            color: #2e7d32;
            border: 3px solid #4CAF50;
        }

        .feedback.incorrect {
            background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
            color: #c62828;
            border: 3px solid #f44336;
        }

        .interactive-section {
            background: white;
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 50px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .section-title {
            font-size: 2.2rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .section-subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 50px;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .canvas-container {
            background: #f8f9ff;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
            border: 3px solid #e1e5f2;
        }

        #cacheCanvas {
            border-radius: 15px;
            background: white;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 40px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .btn.special {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .demo-status {
            text-align: center;
            margin: 30px 0;
            font-size: 1.3rem;
            color: #667eea;
            font-weight: 500;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9ff;
            border-radius: 15px;
            padding: 25px;
            border: 2px solid #e1e5f2;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 2.2rem;
            }
            
            .quiz-section, .interactive-section {
                padding: 30px;
            }
            
            .options-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>⚡ Cache-主存地址转换探索</h1>
            <p>深入理解Cache与主存之间的地址转换机制，掌握硬件加速的核心原理</p>
        </div>

        <!-- 题目测试区 -->
        <div class="quiz-section">
            <div class="quiz-title">🎯 知识检测</div>
            <div class="question-text">
                在Cache-主存层次结构中，主存单元到Cache单元的地址转换由<span class="highlight">（____）</span>完成。
            </div>
            <div class="options-grid">
                <div class="option-card" data-option="A">A. 硬件</div>
                <div class="option-card" data-option="B">B. 寻址方式</div>
                <div class="option-card" data-option="C">C. 软件和少量的辅助硬件</div>
                <div class="option-card" data-option="D">D. 微程序</div>
            </div>
            <div id="quiz-feedback" class="feedback"></div>
        </div>

        <!-- 互动演示区 -->
        <div class="interactive-section">
            <div class="section-title">🎮 Cache地址转换体验馆</div>
            <div class="section-subtitle">通过互动动画深入理解Cache-主存地址转换的硬件实现原理</div>

            <div class="canvas-container">
                <canvas id="cacheCanvas" width="1200" height="600"></canvas>
            </div>

            <div class="controls">
                <button class="btn" id="hardware-btn">⚡ 硬件转换演示</button>
                <button class="btn secondary" id="software-btn">💻 软件转换对比</button>
                <button class="btn special" id="speed-btn">🏃 速度对比测试</button>
                <button class="btn" id="reset-btn">🔄 重置演示</button>
            </div>

            <div class="demo-status" id="demo-status">
                选择一个演示模式，观看Cache地址转换的工作原理！硬件转换为什么这么快？
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            currentDemo: null,
            animationId: null,
            step: 0,
            isAnimating: false
        };

        // Canvas相关
        const canvas = document.getElementById('cacheCanvas');
        const ctx = canvas.getContext('2d');

        // 题目测试逻辑
        const quizOptions = document.querySelectorAll('.option-card');
        const quizFeedback = document.getElementById('quiz-feedback');
        const correctAnswer = 'A';

        // 按钮元素
        const buttons = {
            hardware: document.getElementById('hardware-btn'),
            software: document.getElementById('software-btn'),
            speed: document.getElementById('speed-btn'),
            reset: document.getElementById('reset-btn')
        };

        const demoStatus = document.getElementById('demo-status');

        // 初始化Canvas
        function initCanvas() {
            ctx.fillStyle = '#f8f9ff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制初始界面
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击按钮开始Cache地址转换演示', canvas.width/2, canvas.height/2);
            
            ctx.font = '18px Arial';
            ctx.fillStyle = '#666';
            ctx.fillText('硬件转换 vs 软件转换 - 速度对比', canvas.width/2, canvas.height/2 + 40);
        }

        // 显示反馈
        function showFeedback(message, type) {
            quizFeedback.textContent = message;
            quizFeedback.className = `feedback ${type}`;
            quizFeedback.style.display = 'block';
        }

        // 硬件转换演示
        function hardwareDemo() {
            if (gameState.isAnimating) return;
            
            gameState.currentDemo = 'hardware';
            gameState.isAnimating = true;
            gameState.step = 0;
            
            buttons.hardware.classList.add('active');
            buttons.software.classList.remove('active');
            buttons.speed.classList.remove('active');
            
            demoStatus.textContent = '⚡ 硬件转换演示：观察专用电路如何瞬间完成地址转换';
            
            animateHardware();
        }

        // 软件转换演示
        function softwareDemo() {
            if (gameState.isAnimating) return;
            
            gameState.currentDemo = 'software';
            gameState.isAnimating = true;
            gameState.step = 0;
            
            buttons.software.classList.add('active');
            buttons.hardware.classList.remove('active');
            buttons.speed.classList.remove('active');
            
            demoStatus.textContent = '💻 软件转换演示：看看程序执行需要多少步骤';
            
            animateSoftware();
        }

        // 速度对比演示
        function speedDemo() {
            if (gameState.isAnimating) return;
            
            gameState.currentDemo = 'speed';
            gameState.isAnimating = true;
            gameState.step = 0;
            
            buttons.speed.classList.add('active');
            buttons.hardware.classList.remove('active');
            buttons.software.classList.remove('active');
            
            demoStatus.textContent = '🏃 速度对比：硬件 vs 软件，谁更快？';
            
            animateSpeedComparison();
        }

        // 硬件转换动画
        function animateHardware() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景
            ctx.fillStyle = '#f0f2ff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 主存地址
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(50, 100, 200, 80);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主存地址', 150, 130);
            ctx.fillText('0x1A2B3C4D', 150, 150);
            
            // 硬件转换器
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(400, 50, 300, 180);
            ctx.fillStyle = 'white';
            ctx.fillText('硬件地址转换器', 550, 90);
            ctx.fillText('⚡ 并行比较器', 550, 120);
            ctx.fillText('🔧 解码器', 550, 140);
            ctx.fillText('🎯 选择器', 550, 160);
            ctx.fillText('1-2 时钟周期', 550, 190);
            
            // Cache地址
            ctx.fillStyle = '#667eea';
            ctx.fillRect(850, 100, 200, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('Cache地址', 950, 130);
            ctx.fillText('Set:5 Way:2', 950, 150);
            
            // 动画箭头
            const progress = (gameState.step % 60) / 60;
            
            // 第一个箭头
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(250, 140);
            ctx.lineTo(250 + 150 * progress, 140);
            ctx.stroke();
            
            if (progress > 0.5) {
                // 第二个箭头
                const progress2 = (progress - 0.5) * 2;
                ctx.strokeStyle = '#667eea';
                ctx.beginPath();
                ctx.moveTo(700, 140);
                ctx.lineTo(700 + 150 * progress2, 140);
                ctx.stroke();
            }
            
            // 时间显示
            ctx.fillStyle = '#4CAF50';
            ctx.font = 'bold 20px Arial';
            ctx.fillText(`时间: ${Math.min(gameState.step * 0.1, 2).toFixed(1)} 时钟周期`, 600, 300);
            
            gameState.step++;
            
            if (gameState.step < 120) {
                gameState.animationId = requestAnimationFrame(animateHardware);
            } else {
                gameState.isAnimating = false;
                demoStatus.textContent = '✅ 硬件转换完成！仅用2个时钟周期就完成了地址转换';
            }
        }

        // 软件转换动画
        function animateSoftware() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景
            ctx.fillStyle = '#fff5f5';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 主存地址
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(50, 100, 200, 80);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主存地址', 150, 130);
            ctx.fillText('0x1A2B3C4D', 150, 150);
            
            // 软件处理步骤
            const steps = [
                '1. 读取地址',
                '2. 解析标记',
                '3. 计算索引',
                '4. 查找表格',
                '5. 比较标记',
                '6. 判断命中',
                '7. 返回结果'
            ];
            
            const currentStep = Math.floor(gameState.step / 20) % steps.length;
            
            ctx.fillStyle = '#ff9800';
            ctx.fillRect(350, 50, 400, 200);
            ctx.fillStyle = 'white';
            ctx.fillText('软件地址转换', 550, 80);
            
            // 显示当前步骤
            for (let i = 0; i < steps.length; i++) {
                if (i <= currentStep) {
                    ctx.fillStyle = i === currentStep ? '#4CAF50' : '#666';
                } else {
                    ctx.fillStyle = '#ccc';
                }
                ctx.font = '14px Arial';
                ctx.fillText(steps[i], 550, 110 + i * 20);
            }
            
            // Cache地址
            if (currentStep >= steps.length - 1) {
                ctx.fillStyle = '#667eea';
                ctx.fillRect(850, 100, 200, 80);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('Cache地址', 950, 130);
                ctx.fillText('Set:5 Way:2', 950, 150);
            }
            
            // 时间显示
            ctx.fillStyle = '#ff9800';
            ctx.font = 'bold 20px Arial';
            ctx.fillText(`时间: ${Math.min(gameState.step * 0.5, 100).toFixed(0)} 时钟周期`, 600, 300);
            
            gameState.step++;
            
            if (gameState.step < 200) {
                gameState.animationId = requestAnimationFrame(animateSoftware);
            } else {
                gameState.isAnimating = false;
                demoStatus.textContent = '⏰ 软件转换完成！用了100个时钟周期，比硬件慢50倍！';
            }
        }

        // 速度对比动画
        function animateSpeedComparison() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景
            ctx.fillStyle = '#f0f8ff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('速度对比竞赛', canvas.width/2, 50);
            
            // 硬件跑道
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(100, 150, 1000, 60);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('硬件转换', 150, 185);
            
            // 软件跑道
            ctx.fillStyle = '#ff9800';
            ctx.fillRect(100, 250, 1000, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('软件转换', 150, 285);
            
            // 进度
            const hardwareProgress = Math.min(gameState.step * 8, 1000);
            const softwareProgress = Math.min(gameState.step * 0.16, 1000);
            
            // 硬件进度条
            ctx.fillStyle = '#2E7D32';
            ctx.fillRect(100, 150, hardwareProgress, 60);
            
            // 软件进度条
            ctx.fillStyle = '#F57C00';
            ctx.fillRect(100, 250, softwareProgress, 60);
            
            // 时间显示
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Arial';
            ctx.fillText(`硬件: ${Math.min(gameState.step * 0.02, 2).toFixed(1)} 周期`, 200, 400);
            ctx.fillText(`软件: ${Math.min(gameState.step * 1, 125).toFixed(0)} 周期`, 200, 430);
            
            // 胜负判定
            if (hardwareProgress >= 1000) {
                ctx.fillStyle = '#4CAF50';
                ctx.font = 'bold 32px Arial';
                ctx.fillText('🏆 硬件获胜！', canvas.width/2, 500);
            }
            
            gameState.step++;
            
            if (gameState.step < 125) {
                gameState.animationId = requestAnimationFrame(animateSpeedComparison);
            } else {
                gameState.isAnimating = false;
                demoStatus.textContent = '🏆 比赛结束！硬件转换以压倒性优势获胜，这就是为什么Cache必须用硬件！';
            }
        }

        // 重置演示
        function resetDemo() {
            if (gameState.animationId) {
                cancelAnimationFrame(gameState.animationId);
            }
            
            gameState.currentDemo = null;
            gameState.isAnimating = false;
            gameState.step = 0;
            
            // 重置按钮状态
            Object.values(buttons).forEach(btn => btn.classList.remove('active'));
            
            demoStatus.textContent = '选择一个演示模式，观看Cache地址转换的工作原理！硬件转换为什么这么快？';
            
            initCanvas();
        }

        // 题目测试事件
        quizOptions.forEach(option => {
            option.addEventListener('click', () => {
                // 清除之前的选择
                quizOptions.forEach(opt => {
                    opt.classList.remove('selected', 'correct', 'incorrect');
                });

                // 标记当前选择
                option.classList.add('selected');
                
                const selectedOption = option.dataset.option;
                
                setTimeout(() => {
                    if (selectedOption === correctAnswer) {
                        option.classList.add('correct');
                        showFeedback('🎉 正确！Cache-主存地址转换确实由硬件完成。为了保证Cache的高速访问特性，地址转换必须在1-2个时钟周期内完成，只有专用硬件电路才能达到这样的速度要求。现在让我们通过动画来深入理解这个过程！', 'correct');
                    } else {
                        option.classList.add('incorrect');
                        // 显示正确答案
                        quizOptions.forEach(opt => {
                            if (opt.dataset.option === correctAnswer) {
                                opt.classList.add('correct');
                            }
                        });
                        let explanation = '💡 不对哦！正确答案是A-硬件。';
                        if (selectedOption === 'B') {
                            explanation += '寻址方式是访问数据的方法，不是转换的执行者。';
                        } else if (selectedOption === 'C') {
                            explanation += '软件转换太慢，会严重影响Cache性能。';
                        } else if (selectedOption === 'D') {
                            explanation += '微程序主要用于指令执行控制，不负责地址转换。';
                        }
                        explanation += '试试下面的动画演示，看看硬件转换有多快！';
                        showFeedback(explanation, 'incorrect');
                    }
                }, 300);
            });
        });

        // 按钮事件监听
        buttons.hardware.addEventListener('click', hardwareDemo);
        buttons.software.addEventListener('click', softwareDemo);
        buttons.speed.addEventListener('click', speedDemo);
        buttons.reset.addEventListener('click', resetDemo);

        // Canvas点击事件
        canvas.addEventListener('click', () => {
            if (!gameState.isAnimating) {
                hardwareDemo();
            }
        });

        // 初始化
        initCanvas();
    </script>
</body>
</html>