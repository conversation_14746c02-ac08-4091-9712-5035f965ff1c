<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能需求与非功能需求演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f4f7f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        .explanation {
            background: #e9f7fd;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .demo-area {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
        .shop-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .product-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .product-card {
            width: 200px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: white;
            transition: transform 0.2s;
        }
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .product-image {
            width: 100%;
            height: 150px;
            background-color: #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }
        .product-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .product-price {
            color: #e74c3c;
            font-weight: bold;
        }
        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .cart {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background-color: white;
        }
        .cart-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .cart-total {
            font-weight: bold;
            text-align: right;
            margin-top: 10px;
        }
        .requirements-box {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        .requirement {
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .functional {
            background-color: #d4edda;
            border-left: 5px solid #28a745;
        }
        .non-functional {
            background-color: #f8d7da;
            border-left: 5px solid #dc3545;
        }
        .control-panel {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .control-group {
            margin-bottom: 15px;
        }
        .control-label {
            font-weight: bold;
            margin-bottom: 5px;
            display: block;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            display: none;
        }
        .message.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .message.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .message.info {
            background-color: #e9f7fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .tab-container {
            margin-top: 20px;
        }
        .tab-buttons {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        .tab-button {
            padding: 10px 20px;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
        }
        .tab-button.active {
            background-color: white;
            font-weight: bold;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 5px;
            background-color: white;
        }
        .tab-content.active {
            display: block;
        }
        .highlight {
            background-color: yellow;
            padding: 2px;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>功能需求与非功能需求演示</h1>
        
        <div class="explanation">
            <h2>简单解释</h2>
            <p><strong>功能需求</strong>：描述系统<strong>应该做什么</strong>，即系统的功能和行为。</p>
            <p><strong>非功能需求</strong>：描述系统<strong>应该如何做</strong>，即系统的品质特性和约束条件。</p>
        </div>
        
        <div class="explanation">
            <h3>通过类比理解</h3>
            <p>想象你在描述一辆汽车：</p>
            <ul>
                <li><strong>功能需求</strong>就像是说："这辆车能载人、能开动、能刹车、有空调"</li>
                <li><strong>非功能需求</strong>就像是说："这辆车时速能达到200公里、油耗是6升/100公里、安全气囊能在0.03秒内弹出"</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>电子商城演示</h2>
        <p>下面是一个简单的电子商城演示，通过这个演示，我们可以直观地理解功能需求和非功能需求的区别。</p>
        
        <div class="control-panel">
            <h3>需求控制面板</h3>
            <p>通过切换下面的开关，你可以体验不同需求的效果：</p>
            
            <div class="control-group">
                <span class="control-label">功能需求：添加商品到购物车</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="toggle-add-to-cart" checked>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="control-group">
                <span class="control-label">功能需求：从购物车移除商品</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="toggle-remove-from-cart" checked>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="control-group">
                <span class="control-label">非功能需求：系统响应时间不超过1秒</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="toggle-response-time" checked>
                    <span class="slider"></span>
                </label>
            </div>
            
            <div class="control-group">
                <span class="control-label">非功能需求：购物车商品数量不能超过5个</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="toggle-cart-limit" checked>
                    <span class="slider"></span>
                </label>
            </div>
        </div>
        
        <div class="demo-area">
            <div class="shop-container">
                <h3>商品列表</h3>
                <div class="product-list">
                    <div class="product-card">
                        <div class="product-image">手机图片</div>
                        <div class="product-title">智能手机</div>
                        <div class="product-price">¥3999</div>
                        <button class="btn add-to-cart" data-id="1" data-name="智能手机" data-price="3999">加入购物车</button>
                    </div>
                    <div class="product-card">
                        <div class="product-image">笔记本图片</div>
                        <div class="product-title">笔记本电脑</div>
                        <div class="product-price">¥6999</div>
                        <button class="btn add-to-cart" data-id="2" data-name="笔记本电脑" data-price="6999">加入购物车</button>
                    </div>
                    <div class="product-card">
                        <div class="product-image">耳机图片</div>
                        <div class="product-title">无线耳机</div>
                        <div class="product-price">¥999</div>
                        <button class="btn add-to-cart" data-id="3" data-name="无线耳机" data-price="999">加入购物车</button>
                    </div>
                    <div class="product-card">
                        <div class="product-image">平板图片</div>
                        <div class="product-title">平板电脑</div>
                        <div class="product-price">¥4999</div>
                        <button class="btn add-to-cart" data-id="4" data-name="平板电脑" data-price="4999">加入购物车</button>
                    </div>
                    <div class="product-card">
                        <div class="product-image">手表图片</div>
                        <div class="product-title">智能手表</div>
                        <div class="product-price">¥1999</div>
                        <button class="btn add-to-cart" data-id="5" data-name="智能手表" data-price="1999">加入购物车</button>
                    </div>
                    <div class="product-card">
                        <div class="product-image">相机图片</div>
                        <div class="product-title">数码相机</div>
                        <div class="product-price">¥5999</div>
                        <button class="btn add-to-cart" data-id="6" data-name="数码相机" data-price="5999">加入购物车</button>
                    </div>
                </div>
                
                <div class="message" id="system-message"></div>
                
                <div class="cart">
                    <h3>购物车</h3>
                    <div id="cart-items">
                        <div class="cart-item">
                            <span>购物车是空的</span>
                        </div>
                    </div>
                    <div class="cart-total">
                        总计: ¥<span id="cart-total">0</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="requirements-box">
            <h3>需求分析</h3>
            <div class="tab-container">
                <div class="tab-buttons">
                    <div class="tab-button active" data-tab="tab-functional">功能需求</div>
                    <div class="tab-button" data-tab="tab-non-functional">非功能需求</div>
                    <div class="tab-button" data-tab="tab-password">密码策略示例</div>
                </div>
                
                <div class="tab-content active" id="tab-functional">
                    <h4>功能需求示例</h4>
                    <div class="requirement functional">
                        <strong>FR1:</strong> 系统应该允许用户浏览商品列表
                    </div>
                    <div class="requirement functional">
                        <strong>FR2:</strong> 系统应该允许用户将商品添加到购物车
                    </div>
                    <div class="requirement functional">
                        <strong>FR3:</strong> 系统应该允许用户从购物车中移除商品
                    </div>
                    <div class="requirement functional">
                        <strong>FR4:</strong> 系统应该计算购物车中商品的总价
                    </div>
                    <p>
                        功能需求描述了系统<strong>应该做什么</strong>，即系统应该提供的功能或服务。它们定义了系统的行为，用户可以使用系统做什么。
                    </p>
                </div>
                
                <div class="tab-content" id="tab-non-functional">
                    <h4>非功能需求示例</h4>
                    <div class="requirement non-functional">
                        <strong>NFR1:</strong> 系统响应时间不应超过1秒
                    </div>
                    <div class="requirement non-functional">
                        <strong>NFR2:</strong> 购物车中的商品数量不能超过5个
                    </div>
                    <div class="requirement non-functional">
                        <strong>NFR3:</strong> 系统应该能够同时处理1000个用户的请求
                    </div>
                    <div class="requirement non-functional">
                        <strong>NFR4:</strong> 系统应该使用SSL加密所有传输的数据
                    </div>
                    <p>
                        非功能需求描述了系统<strong>应该如何做</strong>，即系统应该具有的品质属性或约束条件。它们定义了系统的特性和约束，而不是具体的功能。
                    </p>
                </div>
                
                <div class="tab-content" id="tab-password">
                    <h4>密码策略需求分析</h4>
                    <p>
                        需求："<span class="highlight">登录用户的密码30天需要修改一次，而且不能使用最近5次的密码，以提高系统的安全性</span>"
                    </p>
                    
                    <h5>相关的功能需求：</h5>
                    <div class="requirement functional">
                        <strong>FR1:</strong> 系统应该允许用户登录
                    </div>
                    <div class="requirement functional">
                        <strong>FR2:</strong> 系统应该允许用户修改密码
                    </div>
                    <div class="requirement functional">
                        <strong>FR3:</strong> 系统应该能够存储用户的历史密码记录
                    </div>
                    
                    <h5>相关的非功能需求：</h5>
                    <div class="requirement non-functional">
                        <strong>NFR1:</strong> 用户密码必须每30天更改一次（安全性）
                    </div>
                    <div class="requirement non-functional">
                        <strong>NFR2:</strong> 新密码不能与最近5次使用的密码相同（安全性）
                    </div>
                    
                    <p>
                        这个密码策略是非功能需求，因为：
                    </p>
                    <ol>
                        <li>它不是描述系统<strong>应该做什么</strong>（功能），而是描述系统<strong>应该如何做</strong>（质量属性）</li>
                        <li>它关注的是系统的<strong>安全性</strong>这一品质属性，而不是具体的功能</li>
                        <li>它是对<strong>已有功能</strong>（用户登录和密码修改）的约束和要求，而不是新功能</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 购物车数据
        let cart = [];
        let totalPrice = 0;
        
        // 控制开关
        const toggleAddToCart = document.getElementById('toggle-add-to-cart');
        const toggleRemoveFromCart = document.getElementById('toggle-remove-from-cart');
        const toggleResponseTime = document.getElementById('toggle-response-time');
        const toggleCartLimit = document.getElementById('toggle-cart-limit');
        
        // 消息显示
        const systemMessage = document.getElementById('system-message');
        
        // 添加商品到购物车
        document.querySelectorAll('.add-to-cart').forEach(button => {
            button.addEventListener('click', function() {
                // 检查功能需求：添加商品到购物车
                if (!toggleAddToCart.checked) {
                    showMessage('错误：功能需求"添加商品到购物车"已禁用！', 'error');
                    return;
                }
                
                const productId = this.dataset.id;
                const productName = this.dataset.name;
                const productPrice = parseInt(this.dataset.price);
                
                // 检查非功能需求：购物车商品数量限制
                if (toggleCartLimit.checked && cart.length >= 5) {
                    showMessage('错误：非功能需求限制 - 购物车商品数量不能超过5个！', 'error');
                    return;
                }
                
                // 检查非功能需求：系统响应时间
                if (toggleResponseTime.checked) {
                    showMessage('处理中...', 'info');
                    setTimeout(() => {
                        addToCartLogic(productId, productName, productPrice);
                    }, 500); // 模拟0.5秒响应时间
                } else {
                    // 模拟慢响应
                    showMessage('处理中...（响应较慢）', 'info');
                    setTimeout(() => {
                        addToCartLogic(productId, productName, productPrice);
                    }, 2000); // 模拟2秒响应时间
                }
            });
        });
        
        // 添加到购物车的逻辑
        function addToCartLogic(productId, productName, productPrice) {
            // 检查商品是否已在购物车中
            const existingItem = cart.find(item => item.id === productId);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: productId,
                    name: productName,
                    price: productPrice,
                    quantity: 1
                });
            }
            
            updateCart();
            showMessage(`成功添加 ${productName} 到购物车！`, 'success');
        }
        
        // 更新购物车显示
        function updateCart() {
            const cartItemsElement = document.getElementById('cart-items');
            const cartTotalElement = document.getElementById('cart-total');
            
            // 清空购物车显示
            cartItemsElement.innerHTML = '';
            
            // 如果购物车为空
            if (cart.length === 0) {
                cartItemsElement.innerHTML = '<div class="cart-item"><span>购物车是空的</span></div>';
                cartTotalElement.textContent = '0';
                return;
            }
            
            // 计算总价
            totalPrice = 0;
            
            // 添加购物车项目
            cart.forEach(item => {
                const itemTotal = item.price * item.quantity;
                totalPrice += itemTotal;
                
                const cartItemElement = document.createElement('div');
                cartItemElement.className = 'cart-item';
                cartItemElement.innerHTML = `
                    <span>${item.name} x ${item.quantity}</span>
                    <span>¥${itemTotal}</span>
                    <button class="btn remove-from-cart" data-id="${item.id}">移除</button>
                `;
                cartItemsElement.appendChild(cartItemElement);
            });
            
            // 更新总价
            cartTotalElement.textContent = totalPrice;
            
            // 添加移除商品事件
            document.querySelectorAll('.remove-from-cart').forEach(button => {
                button.addEventListener('click', function() {
                    // 检查功能需求：从购物车移除商品
                    if (!toggleRemoveFromCart.checked) {
                        showMessage('错误：功能需求"从购物车移除商品"已禁用！', 'error');
                        return;
                    }
                    
                    const productId = this.dataset.id;
                    
                    // 检查非功能需求：系统响应时间
                    if (toggleResponseTime.checked) {
                        showMessage('处理中...', 'info');
                        setTimeout(() => {
                            removeFromCart(productId);
                        }, 500); // 模拟0.5秒响应时间
                    } else {
                        // 模拟慢响应
                        showMessage('处理中...（响应较慢）', 'info');
                        setTimeout(() => {
                            removeFromCart(productId);
                        }, 2000); // 模拟2秒响应时间
                    }
                });
            });
        }
        
        // 从购物车移除商品
        function removeFromCart(productId) {
            const index = cart.findIndex(item => item.id === productId);
            
            if (index !== -1) {
                const removedItem = cart[index];
                
                if (removedItem.quantity > 1) {
                    removedItem.quantity -= 1;
                    showMessage(`已从购物车移除一个 ${removedItem.name}！`, 'success');
                } else {
                    cart.splice(index, 1);
                    showMessage(`已从购物车移除 ${removedItem.name}！`, 'success');
                }
                
                updateCart();
            }
        }
        
        // 显示系统消息
        function showMessage(message, type) {
            systemMessage.textContent = message;
            systemMessage.className = `message ${type}`;
            systemMessage.style.display = 'block';
            
            // 3秒后自动隐藏消息
            setTimeout(() => {
                systemMessage.style.display = 'none';
            }, 3000);
        }
        
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有标签页的活动状态
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // 设置当前标签页为活动状态
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });
    </script>

</body>
</html> 