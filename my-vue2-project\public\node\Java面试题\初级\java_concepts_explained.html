<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java 核心概念趣味学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .container {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            padding: 30px;
            box-sizing: border-box;
        }

        h1, h2 {
            text-align: center;
            color: #1a2a4c;
        }
        
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        h2 {
            font-size: 2em;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-top: 40px;
        }

        p, .explanation {
            font-size: 1.1em;
            line-height: 1.8;
            color: #555;
            text-align: center;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
            padding: 10px;
        }

        canvas {
            background-color: #fff;
            border-radius: 8px;
        }

        .controls, .platform-controls {
            text-align: center;
            margin-top: 15px;
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin: 5px;
        }

        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }
        
        button.active {
            background-color: #28a745;
        }

        .explanation-box {
            background-color: #e9f5ff;
            border-left: 5px solid #007bff;
            padding: 20px;
            margin: 20px auto;
            border-radius: 5px;
            max-width: 80%;
        }

        .explanation-box p {
            margin: 0;
            text-align: left;
            font-size: 1.05em;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Java 核心概念趣味学习</h1>
        
        <!-- Section 1: Write Once, Run Anywhere -->
        <section id="wora">
            <h2>拓展一：一次编写，随处运行 (WORA)</h2>
            <p>这是 Java 最经典的口号。它意味着你的 Java 代码只需编写一次，就可以在任何支持 Java 的设备上运行，无需为每个平台单独修改。这是如何做到的呢？让我们通过一个动画来理解！</p>
            <div class="canvas-container">
                <canvas id="woraCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button id="playWoraAnimation">观看动画</button>
            </div>
            <div id="wora-explanation" class="explanation-box" style="display: none;">
                <p><strong>核心揭秘：</strong>这一切都归功于 <strong>JVM (Java 虚拟机)</strong>。Java 代码首先被"编译"成一种叫做"字节码 (.class 文件)"的中间语言。JVM 就像一个翻译官，安装在各种操作系统上（Windows, macOS, Linux）。它能读懂并执行这个统一的字节码文件，从而实现了跨平台运行。不过，现在有了 Docker 等技术，跨平台也变得更加容易，而 Java 强大的"生态系统"才是它更核心的优势。</p>
            </div>
        </section>

        <!-- Section 2: Java SE vs EE vs ME -->
        <section id="editions">
            <h2>拓展二：Java 的三个版本 (SE, EE, ME)</h2>
            <p>Java 根据不同的用途，分成了三个主要版本。把它们想象成不同配置的工具箱，用于完成不同的任务。点击下方按钮，看看它们的关系和用途吧！</p>
             <div class="canvas-container">
                <canvas id="editionsCanvas" width="800" height="350"></canvas>
            </div>
            <div class="platform-controls">
                <button id="showSE">Java SE (标准版)</button>
                <button id="showEE">Java EE (企业版)</button>
                <button id="showME">Java ME (微型版)</button>
            </div>
            <div id="editions-explanation" class="explanation-box">
                <p>请点击上面的按钮来查看对应版本的解释。</p>
            </div>
        </section>
    </div>

    <script>
    window.onload = function() {
        // --- WORA Animation Logic ---
        const woraCanvas = document.getElementById('woraCanvas');
        const woraCtx = woraCanvas.getContext('2d');
        const playWoraBtn = document.getElementById('playWoraAnimation');
        const woraExplanation = document.getElementById('wora-explanation');
        let woraAnimationState = 0; // 0: idle, 1: compiling, 2: distributing, 3: running

        function drawWoraIdle() {
            woraCtx.clearRect(0, 0, woraCanvas.width, woraCanvas.height);
            // Draw initial Java file
            drawFile(woraCtx, 50, 120, '.java');
            // Draw compiler
            drawBox(woraCtx, 200, 110, 80, 60, 'Compiler', '#ffc107');
            // Draw platforms
            drawPlatform(woraCtx, 450, 50, 'Windows');
            drawPlatform(woraCtx, 450, 130, 'macOS');
            drawPlatform(woraCtx, 450, 210, 'Linux');
        }

        let progress = 0;
        function animateWora() {
            woraCtx.clearRect(0, 0, woraCanvas.width, woraCanvas.height);
            
            // Static elements
            drawFile(woraCtx, 50, 120, '.java');
            drawBox(woraCtx, 200, 110, 80, 60, 'Compiler', '#ffc107');
            const win = drawPlatform(woraCtx, 450, 50, 'Windows');
            const mac = drawPlatform(woraCtx, 450, 130, 'macOS');
            const lin = drawPlatform(woraCtx, 450, 210, 'Linux');

            // Animation logic
            if (woraAnimationState > 0) { // Compiling
                progress += 2;
                const currentX = 50 + progress;
                drawArrow(woraCtx, 120, 145, 200, 145);
                if (currentX < 240) {
                   drawFile(woraCtx, currentX, 120, '.java');
                } else {
                   woraAnimationState = 2; // Move to distribution
                   progress = 0;
                }
            }
            
            if (woraAnimationState >= 2) { // Distributing
                const bytecodeX = 330;
                drawFile(woraCtx, bytecodeX, 120, '.class', '#28a745');
                drawArrow(woraCtx, 280, 145, 330, 145);

                progress += 2;
                const arrowEndX = bytecodeX + 50;

                // To Windows
                drawArrow(woraCtx, bytecodeX + 50, 145, Math.min(arrowEndX + progress, win.x), win.y + win.h / 2, true);
                // To macOS
                drawArrow(woraCtx, bytecodeX + 50, 145, Math.min(arrowEndX + progress, mac.x), mac.y + mac.h / 2, true);
                // To Linux
                drawArrow(woraCtx, bytecodeX + 50, 145, Math.min(arrowEndX + progress, lin.x), lin.y + lin.h / 2, true);

                if (arrowEndX + progress >= mac.x) {
                    woraAnimationState = 3;
                    progress = 0;
                }
            }
            
            if (woraAnimationState === 3) { // Running
                const bytecodeX = 330;
                drawFile(woraCtx, bytecodeX, 120, '.class', '#28a745');
                drawArrow(woraCtx, 280, 145, 330, 145);
                drawArrow(woraCtx, bytecodeX + 50, 145, win.x, win.y + win.h / 2);
                drawArrow(woraCtx, bytecodeX + 50, 145, mac.x, mac.y + mac.h / 2);
                drawArrow(woraCtx, bytecodeX + 50, 145, lin.x, lin.y + lin.h / 2);
                
                // Show running state
                runAppInPlatform(woraCtx, win);
                runAppInPlatform(woraCtx, mac);
                runAppInPlatform(woraCtx, lin);

                // Stop animation
                playWoraBtn.disabled = false;
                playWoraBtn.textContent = '重新播放';
                woraExplanation.style.display = 'block';
                return; 
            }

            requestAnimationFrame(animateWora);
        }

        playWoraBtn.addEventListener('click', () => {
            playWoraBtn.disabled = true;
            woraAnimationState = 1;
            progress = 0;
            woraExplanation.style.display = 'none';
            animateWora();
        });

        // --- Editions Visualization Logic ---
        const editionsCanvas = document.getElementById('editionsCanvas');
        const editionsCtx = editionsCanvas.getContext('2d');
        const editionsExplanation = document.getElementById('editions-explanation');
        const editionButtons = document.querySelectorAll('.platform-controls button');

        let activeEdition = 'SE'; // SE, EE, ME
        
        const editionDetails = {
            SE: {
                color: '#007bff',
                text: `<strong>Java SE (标准版):</strong> 这是Java的基础和核心，就像是工具箱里的基础套装(锤子、螺丝刀)。它包含了运行Java程序所需的一切，主要用于开发桌面应用程序或简单的服务器程序。`
            },
            EE: {
                color: '#dc3545',
                text: `<strong>Java EE (企业版):</strong> 这是Java的升级版和豪华版，在SE的基础上增加了很多"重型装备"(如服务器、数据库连接工具等)。它专门用于构建大型、复杂、安全的企业级应用，比如银行系统、电商网站后台。`
            },
            ME: {
                color: '#ffc107',
                text: `<strong>Java ME (微型版):</strong> 这是Java的迷你版，专为计算能力有限的小型设备设计，比如以前的功能手机、机顶盒等。现在已经用得很少了，我们只需要知道它存在过就行。`
            }
        };

        function drawEditions() {
            editionsCtx.clearRect(0, 0, editionsCanvas.width, editionsCanvas.height);
            const centerX = editionsCanvas.width / 2;
            
            // Draw SE as the base
            drawBigBox(editionsCtx, centerX - 150, 150, 300, 100, 'Java SE', '核心库, JVM', '#007bff');

            if (activeEdition === 'EE') {
                drawBigBox(editionsCtx, centerX - 180, 50, 360, 80, 'Java EE', '企业级功能 (Servlet, JSP...)', '#dc3545', true);
                 drawRelationshipLine(editionsCtx, centerX, 50, centerX, 150, "建立在 SE 之上");
            } else if (activeEdition === 'ME') {
                drawBigBox(editionsCtx, centerX + 200, 180, 150, 70, 'Java ME', '嵌入式设备', '#ffc107');
            }
        }
        
        function updateEditionsView(edition) {
            activeEdition = edition;
            
            editionButtons.forEach(btn => btn.classList.remove('active'));
            document.getElementById(`show${edition}`).classList.add('active');

            editionsExplanation.innerHTML = `<p>${editionDetails[edition].text}</p>`;
            editionsExplanation.style.borderColor = editionDetails[edition].color;

            drawEditions();
        }

        document.getElementById('showSE').addEventListener('click', () => updateEditionsView('SE'));
        document.getElementById('showEE').addEventListener('click', () => updateEditionsView('EE'));
        document.getElementById('showME').addEventListener('click', () => updateEditionsView('ME'));
        
        // --- Common Drawing Functions ---
        function drawFile(ctx, x, y, text, color = '#007bff') {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 70, 40);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + 35, y + 25);
        }

        function drawBox(ctx, x, y, w, h, text, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, w, h);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + w / 2, y + h / 2 + 5);
        }
        
        function drawBigBox(ctx, x, y, w, h, title, subtitle, color, dashed = false) {
             ctx.save();
             ctx.fillStyle = color;
             ctx.strokeStyle = color;
             ctx.lineWidth = 3;
             if (dashed) {
                 ctx.setLineDash([10, 5]);
             }
             ctx.globalAlpha = 0.1;
             ctx.fillRect(x, y, w, h);
             ctx.globalAlpha = 1;
             ctx.strokeRect(x,y,w,h);
             
             ctx.fillStyle = '#333';
             ctx.font = 'bold 20px sans-serif';
             ctx.textAlign = 'center';
             ctx.fillText(title, x + w/2, y + 40);
             
             ctx.font = '14px sans-serif';
             ctx.fillText(subtitle, x + w/2, y + 70);
             ctx.restore();
        }

        function drawPlatform(ctx, x, y, text) {
            const w = 200, h = 60;
            ctx.fillStyle = '#6c757d';
            ctx.fillRect(x, y, w, h);
            
            // JVM Box
            const jvmW = 60, jvmH = 40;
            ctx.fillStyle = '#17a2b8';
            ctx.fillRect(x + 10, y + 10, jvmW, jvmH);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px sans-serif';
            ctx.fillText('JVM', x + 10 + jvmW/2, y + 35);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + w/2 + 20, y + h/2 + 5);
            return { x, y, w, h, jvmX: x + 10, jvmY: y + 10, jvmW, jvmH };
        }
        
        function runAppInPlatform(ctx, platform) {
            ctx.fillStyle = '#28a745';
            ctx.beginPath();
            ctx.arc(platform.jvmX + platform.jvmW / 2, platform.jvmY + platform.jvmH / 2, 10, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px sans-serif';
            ctx.fillText('App', platform.jvmX + platform.jvmW / 2, platform.jvmY + platform.jvmH / 2 + 4);
        }

        function drawArrow(ctx, fromX, fromY, toX, toY, isAnimating = false) {
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            if (!isAnimating || (isAnimating && toX > fromX + 20)) {
                ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
                ctx.moveTo(toX, toY);
                ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            }
            ctx.stroke();
        }
        
        function drawRelationshipLine(ctx, fromX, fromY, toX, toY, text) {
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.strokeStyle = '#555';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.stroke();
            ctx.setLineDash([]); // Reset
            
            ctx.fillStyle = '#333';
            ctx.font = 'italic 14px sans-serif';
            ctx.save();
            ctx.translate((fromX + toX)/2, (fromY + toY)/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText(text, 10, 5);
            ctx.restore();
        }

        // Initial draws
        drawWoraIdle();
        updateEditionsView('SE');
    };
    </script>
</body>
</html> 