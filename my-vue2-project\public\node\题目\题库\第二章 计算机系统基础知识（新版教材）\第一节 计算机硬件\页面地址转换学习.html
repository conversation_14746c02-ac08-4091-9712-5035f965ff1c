<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面地址转换 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.5em;
            color: white;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2em;
            color: rgba(255,255,255,0.9);
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8em;
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .concept-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.1em;
            line-height: 1.6;
            animation: pulse 2s infinite;
        }

        .demo-area {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .address-box {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            min-width: 200px;
            transition: all 0.3s ease;
        }

        .address-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .address-label {
            font-size: 0.9em;
            color: #718096;
            margin-bottom: 10px;
        }

        .address-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2d3748;
            font-family: 'Courier New', monospace;
        }

        .arrow {
            font-size: 2em;
            color: #667eea;
            animation: bounce 2s infinite;
        }

        .step-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .step {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .step:hover {
            transform: scale(1.05);
        }

        .step-number {
            background: white;
            color: #667eea;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 15px;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #animationCanvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .highlight {
            background: #ffd700 !important;
            animation: glow 1s ease-in-out infinite alternate;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        @keyframes glow {
            from { box-shadow: 0 0 10px #ffd700; }
            to { box-shadow: 0 0 20px #ffd700, 0 0 30px #ffd700; }
        }

        .page-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .page-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .page-table th, .page-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
        }

        .page-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }

        .page-table tr:hover {
            background: #f7fafc;
        }

        .question-box {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .question-box h3 {
            color: #744210;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .question-box p {
            color: #744210;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: bold;
        }

        .option:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #48bb78;
            background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
            color: #22543d;
        }

        .option.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .answer-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #e53e3e;
        }

        .answer-info p {
            margin: 5px 0;
            color: #2d3748;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🧠 页面地址转换学习</h1>
            <p class="subtitle">零基础也能轻松掌握的操作系统概念</p>
        </div>

        <div class="section">
            <h2 class="section-title">� 原题目</h2>
            <div class="question-box">
                <h3>题目描述</h3>
                <p><strong>设计算机系统的页面大小为4K，进程P的页面变换表如下表所示。若P要访问的逻辑地址为十六进制3C20H，那么该逻辑地址经过地址变换后，其物理地址应为（ ）。</strong></p>

                <div class="options">
                    <div class="option" data-answer="A">A. 2048H</div>
                    <div class="option" data-answer="B">B. 3C20H</div>
                    <div class="option" data-answer="C">C. 5C20H</div>
                    <div class="option correct" data-answer="D">D. 6C20H</div>
                </div>

                <div class="answer-info">
                    <p><strong>正确答案：D</strong></p>
                    <p><strong>你的答案：B</strong> ❌</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">�📚 基础概念</h2>
            <div class="concept-box">
                <strong>什么是页面地址转换？</strong><br>
                就像邮递员送信一样，计算机需要把"逻辑地址"（你写的地址）转换成"物理地址"（实际的位置）
            </div>
            
            <div class="step-container">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3>页面大小</h3>
                    <p>4K = 4096字节 = 2¹²字节<br>用16进制表示就是1000H</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h3>地址分解</h3>
                    <p>逻辑地址 = 页号 + 页内偏移<br>物理地址 = 块号 + 页内偏移</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h3>转换规则</h3>
                    <p>页内偏移不变<br>只需要查表把页号换成块号</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📋 页面变换表</h2>
            <div class="page-table">
                <table>
                    <thead>
                        <tr>
                            <th>页号</th>
                            <th>物理块号</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr id="page0">
                            <td>0</td>
                            <td>2</td>
                            <td>有效</td>
                        </tr>
                        <tr id="page1">
                            <td>1</td>
                            <td>4</td>
                            <td>有效</td>
                        </tr>
                        <tr id="page2">
                            <td>2</td>
                            <td>1</td>
                            <td>有效</td>
                        </tr>
                        <tr id="page3">
                            <td>3</td>
                            <td>6</td>
                            <td>有效</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 题目演示</h2>
            <div class="demo-area">
                <div class="address-box" id="logicalAddr">
                    <div class="address-label">逻辑地址</div>
                    <div class="address-value">3C20H</div>
                </div>
                <div class="arrow">→</div>
                <div class="address-box" id="physicalAddr">
                    <div class="address-label">物理地址</div>
                    <div class="address-value">?</div>
                </div>
            </div>
            
            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>
            
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="startAnimation()">🎬 开始动画演示</button>
                <button class="interactive-btn" onclick="stepByStep()">📝 分步解析</button>
                <button class="interactive-btn" onclick="reset()">🔄 重新开始</button>
            </div>
        </div>

        <div class="section" id="explanation" style="display: none;">
            <h2 class="section-title">💡 详细解析</h2>
            <div id="stepExplanation"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let isAnimating = false;

        function drawBackground() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('地址转换动画演示', canvas.width / 2, 30);
        }

        function drawAddressBreakdown() {
            drawBackground();
            
            // 绘制逻辑地址分解
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('逻辑地址 3C20H 的分解：', 50, 80);
            
            // 绘制地址框
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 100, 100, 50);
            ctx.strokeRect(150, 100, 100, 50);
            
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('页号', 100, 125);
            ctx.fillText('页内偏移', 200, 125);
            
            ctx.fillStyle = '#e53e3e';
            ctx.font = 'bold 18px Courier New';
            ctx.fillText('3', 100, 145);
            ctx.fillText('C20', 200, 145);
            
            // 说明文字
            ctx.fillStyle = '#718096';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('因为页面大小是4K=1000H', 50, 180);
            ctx.fillText('所以最高位是页号，后3位是页内偏移', 50, 200);
        }

        function drawTableLookup() {
            drawAddressBreakdown();
            
            // 绘制查表过程
            ctx.fillStyle = '#38a169';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('查表：页号3 → 物理块号6', 50, 250);
            
            // 绘制箭头
            ctx.strokeStyle = '#38a169';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(300, 240);
            ctx.lineTo(350, 240);
            ctx.lineTo(340, 230);
            ctx.moveTo(350, 240);
            ctx.lineTo(340, 250);
            ctx.stroke();
            
            // 高亮页表中的行
            document.getElementById('page3').classList.add('highlight');
        }

        function drawFinalResult() {
            drawTableLookup();
            
            // 绘制最终结果
            ctx.fillStyle = '#d69e2e';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('物理地址：6C20H', 50, 320);
            
            // 绘制结果框
            ctx.strokeStyle = '#d69e2e';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 340, 100, 50);
            ctx.strokeRect(150, 340, 100, 50);
            
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('物理块号', 100, 365);
            ctx.fillText('页内偏移', 200, 365);
            
            ctx.fillStyle = '#d69e2e';
            ctx.font = 'bold 18px Courier New';
            ctx.fillText('6', 100, 385);
            ctx.fillText('C20', 200, 385);
            
            // 更新物理地址显示
            document.getElementById('physicalAddr').querySelector('.address-value').textContent = '6C20H';
            document.getElementById('physicalAddr').classList.add('highlight');
        }

        function startAnimation() {
            if (isAnimating) return;
            isAnimating = true;
            animationStep = 0;
            reset();
            
            setTimeout(() => {
                drawAddressBreakdown();
                setTimeout(() => {
                    drawTableLookup();
                    setTimeout(() => {
                        drawFinalResult();
                        isAnimating = false;
                    }, 2000);
                }, 2000);
            }, 1000);
        }

        function stepByStep() {
            const explanation = document.getElementById('explanation');
            const stepExplanation = document.getElementById('stepExplanation');
            
            explanation.style.display = 'block';
            stepExplanation.innerHTML = `
                <div class="step-container">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3>分解逻辑地址</h3>
                        <p><strong>3C20H</strong><br>
                        页号：<span style="color: #e53e3e;">3</span><br>
                        页内偏移：<span style="color: #e53e3e;">C20</span></p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3>查找页表</h3>
                        <p>页号3对应的物理块号是<span style="color: #38a169;">6</span></p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3>组合物理地址</h3>
                        <p>物理块号：<span style="color: #d69e2e;">6</span><br>
                        页内偏移：<span style="color: #d69e2e;">C20</span><br>
                        结果：<strong>6C20H</strong></p>
                    </div>
                </div>
                <div class="concept-box">
                    <strong>关键理解：</strong><br>
                    页内偏移永远不变！只有页号会被替换成物理块号。<br>
                    这就像换房子，房间内的布局不变，只是楼栋号码变了。
                </div>
            `;
            
            drawFinalResult();
        }

        function reset() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawBackground();
            document.getElementById('physicalAddr').querySelector('.address-value').textContent = '?';
            document.getElementById('physicalAddr').classList.remove('highlight');
            document.getElementById('page3').classList.remove('highlight');
            document.getElementById('explanation').style.display = 'none';
            isAnimating = false;
        }

        // 初始化
        drawBackground();
        
        // 添加交互效果
        document.querySelectorAll('.step').forEach(step => {
            step.addEventListener('click', function() {
                this.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    this.style.transform = 'scale(1.05)';
                }, 200);
            });
        });

        // 选项点击交互
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                // 移除所有选项的选中状态
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('selected');
                });

                // 添加选中状态
                this.classList.add('selected');

                // 显示解释
                const answer = this.getAttribute('data-answer');
                showExplanation(answer);
            });
        });

        function showExplanation(selectedAnswer) {
            const explanationDiv = document.getElementById('explanation');
            const stepExplanation = document.getElementById('stepExplanation');

            let explanationText = '';

            if (selectedAnswer === 'D') {
                explanationText = `
                    <div class="concept-box" style="background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%); color: #22543d;">
                        <strong>🎉 恭喜你选对了！</strong><br>
                        答案D（6C20H）是正确的。让我们看看为什么：
                    </div>
                `;
            } else {
                const wrongAnswers = {
                    'A': '2048H - 这个答案完全错误，没有按照正确的地址转换规则',
                    'B': '3C20H - 这是原来的逻辑地址，没有进行任何转换',
                    'C': '5C20H - 页号3对应的不是物理块号5，而是6'
                };

                explanationText = `
                    <div class="concept-box" style="background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%); color: #742a2a;">
                        <strong>❌ 这个答案不对哦！</strong><br>
                        ${wrongAnswers[selectedAnswer]}<br>
                        让我们一起学习正确的解法：
                    </div>
                `;
            }

            explanationText += `
                <div class="step-container">
                    <div class="step">
                        <div class="step-number">1</div>
                        <h3>理解题目条件</h3>
                        <p>页面大小：4K = 4096 = 2¹² = 1000H<br>
                        逻辑地址：3C20H<br>
                        需要查页表进行转换</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <h3>分解逻辑地址</h3>
                        <p>因为页面大小是1000H（4位16进制）<br>
                        所以：页号 = 3，页内偏移 = C20H</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <h3>查表转换</h3>
                        <p>查页表：页号3 → 物理块号6<br>
                        页内偏移保持不变：C20H</p>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <h3>得出答案</h3>
                        <p>物理地址 = 物理块号 + 页内偏移<br>
                        = 6 + C20H = <strong>6C20H</strong></p>
                    </div>
                </div>
            `;

            stepExplanation.innerHTML = explanationText;
            explanationDiv.style.display = 'block';

            // 滚动到解释部分
            explanationDiv.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
