<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库索引学习 - 交互式教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: center;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .code-example {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #333;
        }

        .game-score {
            text-align: center;
            font-size: 1.2rem;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ 数据库索引学习之旅</h1>
            <p>通过动画和交互，轻松掌握数据库索引的核心概念</p>
        </div>

        <div class="section">
            <h2 class="section-title">🔑 主键索引 (Primary Key Index)</h2>
            <div class="canvas-container">
                <canvas id="primaryCanvas" width="600" height="300"></canvas>
            </div>
            <div class="description">
                <span class="highlight">主键索引</span>是数据表中最重要的索引类型：
                <br>• 数据列不允许重复
                <br>• 不允许为NULL值
                <br>• 一个表只能有一个主键
            </div>
            <button class="interactive-btn" onclick="animatePrimaryKey()">🎮 演示主键索引</button>
            <div class="game-score" id="primaryScore">点击按钮开始学习！</div>
        </div>

        <div class="section">
            <h2 class="section-title">⭐ 唯一索引 (Unique Index)</h2>
            <div class="canvas-container">
                <canvas id="uniqueCanvas" width="600" height="300"></canvas>
            </div>
            <div class="description">
                <span class="highlight">唯一索引</span>保证数据的唯一性：
                <br>• 数据列不允许重复
                <br>• 允许为NULL值
                <br>• 一个表允许多个唯一索引
            </div>
            <div class="code-example">
                ALTER TABLE table_name ADD UNIQUE(column);<br>
                ALTER TABLE table_name ADD UNIQUE(column1,column2);
            </div>
            <button class="interactive-btn" onclick="animateUniqueIndex()">🎮 演示唯一索引</button>
            <div class="game-score" id="uniqueScore">准备好了吗？</div>
        </div>

        <div class="section">
            <h2 class="section-title">📋 普通索引 (Normal Index)</h2>
            <div class="canvas-container">
                <canvas id="normalCanvas" width="600" height="300"></canvas>
            </div>
            <div class="description">
                <span class="highlight">普通索引</span>是最基础的索引类型：
                <br>• 没有唯一性限制
                <br>• 允许为NULL值
                <br>• 可以有重复值
            </div>
            <div class="code-example">
                ALTER TABLE table_name ADD INDEX index_name(column);<br>
                ALTER TABLE table_name ADD INDEX index_name(column1,column2,column3);
            </div>
            <button class="interactive-btn" onclick="animateNormalIndex()">🎮 演示普通索引</button>
            <div class="game-score" id="normalScore">来试试看！</div>
        </div>

        <div class="section">
            <h2 class="section-title">🔍 全文索引 (Full-text Index)</h2>
            <div class="canvas-container">
                <canvas id="fulltextCanvas" width="600" height="300"></canvas>
            </div>
            <div class="description">
                <span class="highlight">全文索引</span>是搜索引擎的核心技术：
                <br>• 用于文本搜索
                <br>• 支持关键词匹配
                <br>• 提高搜索效率
            </div>
            <div class="code-example">
                ALTER TABLE table_name ADD FULLTEXT(column);
            </div>
            <button class="interactive-btn" onclick="animateFulltextIndex()">🎮 演示全文索引</button>
            <div class="game-score" id="fulltextScore">最后一个挑战！</div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationId;
        let gameScores = {
            primary: 0,
            unique: 0,
            normal: 0,
            fulltext: 0
        };

        // 主键索引动画
        function animatePrimaryKey() {
            const canvas = document.getElementById('primaryCanvas');
            const ctx = canvas.getContext('2d');
            const scoreElement = document.getElementById('primaryScore');
            
            let step = 0;
            const maxSteps = 100;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制表格结构
                drawTable(ctx, 50, 50, 500, 200);
                
                // 绘制主键列
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(70, 80, 100, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText('ID (主键)', 85, 100);
                
                // 动画效果：数据插入演示
                const progress = step / maxSteps;
                const numRows = Math.floor(progress * 5);
                
                for (let i = 0; i < numRows; i++) {
                    const y = 120 + i * 25;
                    
                    // 主键值
                    ctx.fillStyle = '#ff6b6b';
                    ctx.fillRect(70, y, 100, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(`${i + 1}`, 110, y + 15);
                    
                    // 其他列
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(180, y, 100, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(`数据${i + 1}`, 210, y + 15);
                }
                
                // 显示规则
                ctx.fillStyle = '#2d3436';
                ctx.font = '16px Arial';
                ctx.fillText('✓ 唯一性：每个ID都不同', 320, 120);
                ctx.fillText('✓ 非空：不能为NULL', 320, 150);
                ctx.fillText('✓ 唯一主键：一个表只有一个', 320, 180);
                
                step++;
                if (step <= maxSteps) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    gameScores.primary += 10;
                    scoreElement.textContent = `🎉 完成！得分: ${gameScores.primary}`;
                }
            }
            
            animate();
        }

        // 绘制表格的通用函数
        function drawTable(ctx, x, y, width, height) {
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);

            // 绘制表头
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(x, y, width, 30);
            ctx.strokeRect(x, y, width, 30);
        }

        // 唯一索引动画
        function animateUniqueIndex() {
            const canvas = document.getElementById('uniqueCanvas');
            const ctx = canvas.getContext('2d');
            const scoreElement = document.getElementById('uniqueScore');

            let step = 0;
            const maxSteps = 120;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制表格结构
                drawTable(ctx, 50, 50, 500, 200);

                // 绘制列头
                ctx.fillStyle = '#00b894';
                ctx.fillRect(70, 80, 80, 30);
                ctx.fillRect(160, 80, 80, 30);
                ctx.fillRect(250, 80, 80, 30);

                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText('ID', 105, 100);
                ctx.fillText('邮箱(唯一)', 170, 100);
                ctx.fillText('姓名', 280, 100);

                // 动画效果：展示唯一索引特性
                const progress = step / maxSteps;
                const emails = ['<EMAIL>', '<EMAIL>', null, '<EMAIL>', '<EMAIL>'];
                const names = ['张三', '李四', '王五', '赵六', '钱七'];

                for (let i = 0; i < Math.min(Math.floor(progress * 5), 4); i++) {
                    const y = 120 + i * 25;

                    // ID列
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(70, y, 80, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(`${i + 1}`, 105, y + 15);

                    // 邮箱列（唯一索引）
                    const isConflict = i === 4; // 最后一个是重复邮箱
                    ctx.fillStyle = isConflict ? '#ff7675' : '#00b894';
                    ctx.fillRect(160, y, 80, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(emails[i] || 'NULL', 170, y + 15);

                    // 姓名列
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(250, y, 80, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(names[i], 280, y + 15);

                    if (isConflict && progress > 0.8) {
                        ctx.fillStyle = '#ff7675';
                        ctx.font = '14px Arial';
                        ctx.fillText('❌ 重复邮箱被拒绝！', 350, y + 15);
                    }
                }

                // 显示规则
                ctx.fillStyle = '#2d3436';
                ctx.font = '16px Arial';
                ctx.fillText('✓ 唯一性：邮箱不能重复', 350, 120);
                ctx.fillText('✓ 允许NULL值', 350, 145);
                ctx.fillText('✓ 可以有多个唯一索引', 350, 170);

                step++;
                if (step <= maxSteps) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    gameScores.unique += 15;
                    scoreElement.textContent = `🎉 完成！得分: ${gameScores.unique}`;
                }
            }

            animate();
        }

        // 普通索引动画
        function animateNormalIndex() {
            const canvas = document.getElementById('normalCanvas');
            const ctx = canvas.getContext('2d');
            const scoreElement = document.getElementById('normalScore');

            let step = 0;
            const maxSteps = 150;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制表格结构
                drawTable(ctx, 50, 50, 500, 200);

                // 绘制列头
                ctx.fillStyle = '#6c5ce7';
                ctx.fillRect(70, 80, 80, 30);
                ctx.fillRect(160, 80, 80, 30);
                ctx.fillRect(250, 80, 80, 30);

                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText('ID', 105, 100);
                ctx.fillText('年龄(索引)', 190, 100);
                ctx.fillText('姓名', 280, 100);

                // 动画效果：展示普通索引特性
                const progress = step / maxSteps;
                const ages = [25, 30, 25, null, 30]; // 允许重复和NULL
                const names = ['张三', '李四', '王五', '赵六', '钱七'];

                for (let i = 0; i < Math.floor(progress * 5); i++) {
                    const y = 120 + i * 25;

                    // ID列
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(70, y, 80, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(`${i + 1}`, 105, y + 15);

                    // 年龄列（普通索引）
                    ctx.fillStyle = '#6c5ce7';
                    ctx.fillRect(160, y, 80, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(ages[i] !== null ? ages[i].toString() : 'NULL', 190, y + 15);

                    // 姓名列
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(250, y, 80, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(names[i], 280, y + 15);
                }

                // 显示规则
                ctx.fillStyle = '#2d3436';
                ctx.font = '16px Arial';
                ctx.fillText('✓ 允许重复值', 350, 120);
                ctx.fillText('✓ 允许NULL值', 350, 145);
                ctx.fillText('✓ 提高查询速度', 350, 170);

                // 搜索演示
                if (progress > 0.7) {
                    ctx.fillStyle = '#fd79a8';
                    ctx.font = '14px Arial';
                    ctx.fillText('🔍 搜索年龄=25的记录', 350, 200);

                    // 高亮匹配的行
                    for (let i = 0; i < 5; i++) {
                        if (ages[i] === 25) {
                            const y = 120 + i * 25;
                            ctx.strokeStyle = '#fd79a8';
                            ctx.lineWidth = 3;
                            ctx.strokeRect(160, y, 80, 20);
                        }
                    }
                }

                step++;
                if (step <= maxSteps) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    gameScores.normal += 20;
                    scoreElement.textContent = `🎉 完成！得分: ${gameScores.normal}`;
                }
            }

            animate();
        }

        // 全文索引动画
        function animateFulltextIndex() {
            const canvas = document.getElementById('fulltextCanvas');
            const ctx = canvas.getContext('2d');
            const scoreElement = document.getElementById('fulltextScore');

            let step = 0;
            const maxSteps = 180;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制表格结构
                drawTable(ctx, 50, 50, 500, 200);

                // 绘制列头
                ctx.fillStyle = '#e17055';
                ctx.fillRect(70, 80, 80, 30);
                ctx.fillRect(160, 80, 200, 30);

                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText('ID', 105, 100);
                ctx.fillText('文章内容(全文索引)', 220, 100);

                // 动画效果：展示全文索引特性
                const progress = step / maxSteps;
                const articles = [
                    '学习数据库索引技术',
                    '深入理解MySQL优化',
                    '数据库性能调优指南',
                    '索引设计最佳实践'
                ];

                for (let i = 0; i < Math.floor(progress * 4); i++) {
                    const y = 120 + i * 25;

                    // ID列
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(70, y, 80, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(`${i + 1}`, 105, y + 15);

                    // 文章内容列（全文索引）
                    ctx.fillStyle = '#e17055';
                    ctx.fillRect(160, y, 200, 20);
                    ctx.fillStyle = 'white';
                    ctx.font = '11px Arial';
                    ctx.fillText(articles[i], 170, y + 15);
                }

                // 搜索演示
                if (progress > 0.6) {
                    ctx.fillStyle = '#2d3436';
                    ctx.font = '16px Arial';
                    ctx.fillText('🔍 搜索关键词: "索引"', 380, 120);

                    // 高亮包含关键词的行
                    for (let i = 0; i < 4; i++) {
                        if (articles[i].includes('索引')) {
                            const y = 120 + i * 25;
                            ctx.strokeStyle = '#fdcb6e';
                            ctx.lineWidth = 3;
                            ctx.strokeRect(160, y, 200, 20);

                            // 高亮关键词
                            const text = articles[i];
                            const keywordIndex = text.indexOf('索引');
                            if (keywordIndex !== -1) {
                                ctx.fillStyle = '#fdcb6e';
                                ctx.fillRect(170 + keywordIndex * 8, y + 2, 16, 16);
                            }
                        }
                    }
                }

                // 显示规则
                ctx.fillStyle = '#2d3436';
                ctx.font = '16px Arial';
                ctx.fillText('✓ 支持文本搜索', 380, 150);
                ctx.fillText('✓ 关键词匹配', 380, 175);
                ctx.fillText('✓ 搜索引擎核心', 380, 200);

                step++;
                if (step <= maxSteps) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    gameScores.fulltext += 25;
                    scoreElement.textContent = `🎉 完成！得分: ${gameScores.fulltext}`;
                    showFinalScore();
                }
            }

            animate();
        }

        // 显示最终得分
        function showFinalScore() {
            const totalScore = gameScores.primary + gameScores.unique + gameScores.normal + gameScores.fulltext;
            setTimeout(() => {
                alert(`🎉 恭喜完成所有学习！\n\n总得分: ${totalScore}\n\n你已经掌握了数据库索引的核心概念！`);
            }, 1000);
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 为每个canvas添加点击事件
            document.getElementById('primaryCanvas').addEventListener('click', animatePrimaryKey);
            document.getElementById('uniqueCanvas').addEventListener('click', animateUniqueIndex);
            document.getElementById('normalCanvas').addEventListener('click', animateNormalIndex);
            document.getElementById('fulltextCanvas').addEventListener('click', animateFulltextIndex);

            // 初始化canvas
            initializeCanvases();
        });

        // 初始化所有canvas
        function initializeCanvases() {
            const canvases = ['primaryCanvas', 'uniqueCanvas', 'normalCanvas', 'fulltextCanvas'];
            const titles = ['主键索引', '唯一索引', '普通索引', '全文索引'];

            canvases.forEach((canvasId, index) => {
                const canvas = document.getElementById(canvasId);
                const ctx = canvas.getContext('2d');

                // 绘制初始状态
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawTable(ctx, 50, 50, 500, 200);

                // 绘制标题
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(titles[index], canvas.width / 2, canvas.height / 2);

                ctx.fillStyle = '#999';
                ctx.font = '16px Arial';
                ctx.fillText('点击开始学习', canvas.width / 2, canvas.height / 2 + 30);

                ctx.textAlign = 'left'; // 重置对齐方式
            });
        }
    </script>
</body>
</html>
