<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式互动学习 - 图像处理软件案例</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .scenario-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .scenario-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
        }

        .requirements {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .requirement-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .requirement-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(240, 147, 251, 0.4);
        }

        .requirement-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .requirement-card:hover::before {
            left: 100%;
        }

        .requirement-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .requirement-text {
            font-size: 1rem;
            line-height: 1.6;
        }

        .patterns-section {
            margin-top: 50px;
        }

        .patterns-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .pattern-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .pattern-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .pattern-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            font-weight: bold;
        }

        .command-pattern .pattern-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .state-pattern .pattern-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .strategy-pattern .pattern-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .pattern-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .pattern-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }

        #gameCanvas {
            border: 2px solid #eee;
            border-radius: 10px;
            cursor: pointer;
        }

        .controls {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(240, 147, 251, 0.4);
        }

        .explanation {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            backdrop-filter: blur(10px);
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.8;
            margin-bottom: 15px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .hidden {
            display: none;
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: 600;
        }

        .quiz-section {
            margin-top: 40px;
        }

        .quiz-question h3 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.3rem;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
            margin-bottom: 25px;
        }

        .quiz-option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .quiz-option:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateX(5px);
        }

        .quiz-option.selected {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .quiz-option.correct {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        .quiz-option.incorrect {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        .quiz-explanation {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .quiz-explanation h4 {
            color: #2e7d32;
            margin-bottom: 10px;
        }

        .quiz-explanation p {
            color: #1b5e20;
            line-height: 1.6;
        }

        .quiz-controls {
            text-align: center;
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .score-display {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
        }

        .score-display h3 {
            margin-bottom: 10px;
            font-size: 1.5rem;
        }

        .floating-hint {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 20px;
            border-radius: 15px;
            z-index: 1000;
            text-align: center;
            animation: fadeInScale 0.5s ease-out;
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 设计模式互动学习</h1>
            <p>通过图像处理软件案例，轻松掌握三大核心设计模式</p>
        </div>

        <div class="scenario-card">
            <h2 class="scenario-title">📋 软件需求分析</h2>
            <div class="requirements">
                <div class="requirement-card" onclick="highlightPattern('command')">
                    <div class="requirement-number">需求 1</div>
                    <div class="requirement-text">
                        图像处理软件需要记录用户在处理照片时所有动作，并能够支持用户动作的<span class="highlight">撤销与重做</span>等行为。
                    </div>
                </div>
                <div class="requirement-card" onclick="highlightPattern('state')">
                    <div class="requirement-number">需求 2</div>
                    <div class="requirement-text">
                        图像处理软件需要根据当前正在处理的照片的不同特征选择合适的处理操作，处理操作与照片特征之间具有<span class="highlight">较为复杂的逻辑关系</span>。
                    </div>
                </div>
                <div class="requirement-card" onclick="highlightPattern('strategy')">
                    <div class="requirement-number">需求 3</div>
                    <div class="requirement-text">
                        图像处理软件需要封装各种图像处理算法，用户能够根据需要<span class="highlight">灵活选择合适的处理算法</span>；软件还要支持高级用户根据一定的规则添加自定义处理算法。
                    </div>
                </div>
            </div>
        </div>

        <div class="patterns-section">
            <h2 class="scenario-title">🎯 设计模式解决方案</h2>
            <div class="patterns-grid">
                <div class="pattern-card command-pattern" id="commandCard">
                    <div class="pattern-icon">⚡</div>
                    <h3 class="pattern-title">命令模式</h3>
                    <p class="pattern-description">
                        将请求封装为对象，支持撤销、重做、排队等操作。完美解决需求1的撤销重做功能。
                    </p>
                    <button class="demo-button" onclick="showDemo('command')">体验演示</button>
                </div>
                <div class="pattern-card state-pattern" id="stateCard">
                    <div class="pattern-icon">🔄</div>
                    <h3 class="pattern-title">状态模式</h3>
                    <p class="pattern-description">
                        根据对象状态改变行为，将复杂的条件逻辑分离到独立的状态类中。解决需求2的复杂逻辑关系。
                    </p>
                    <button class="demo-button" onclick="showDemo('state')">体验演示</button>
                </div>
                <div class="pattern-card strategy-pattern" id="strategyCard">
                    <div class="pattern-icon">🎲</div>
                    <h3 class="pattern-title">策略模式</h3>
                    <p class="pattern-description">
                        定义算法族，封装并可相互替换。让算法独立于使用它的客户变化。解决需求3的算法选择。
                    </p>
                    <button class="demo-button" onclick="showDemo('strategy')">体验演示</button>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <h3>🎮 互动演示区</h3>
            <canvas id="gameCanvas" width="800" height="400"></canvas>
            <div class="controls">
                <button class="control-btn" onclick="resetDemo()">重置演示</button>
                <button class="control-btn" onclick="nextStep()">下一步</button>
                <button class="control-btn" onclick="autoPlay()">自动播放</button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3>💡 知识点解析</h3>
            <p>点击上方的需求卡片或设计模式卡片，开始您的学习之旅！</p>
        </div>

        <div class="quiz-section" id="quizSection" style="display: none;">
            <div class="scenario-card">
                <h2 class="scenario-title">🎯 知识测验</h2>
                <div id="quizContent">
                    <div class="quiz-question">
                        <h3>根据题目描述，为了支持灵活的撤销与重做等行为，应该采用哪种设计模式？</h3>
                        <div class="quiz-options">
                            <div class="quiz-option" onclick="selectOption(this, false)">A. 工厂模式</div>
                            <div class="quiz-option" onclick="selectOption(this, false)">B. 责任链模式</div>
                            <div class="quiz-option" onclick="selectOption(this, false)">C. 中介者模式</div>
                            <div class="quiz-option" onclick="selectOption(this, true)">D. 命令模式</div>
                        </div>
                        <div class="quiz-explanation" style="display: none;">
                            <h4>✅ 正确答案：D. 命令模式</h4>
                            <p>命令模式将请求封装为对象，可以对请求排队、记录请求日志，以及支持可撤销的操作。这正是实现撤销重做功能的最佳选择。</p>
                        </div>
                    </div>
                </div>
                <div class="quiz-controls">
                    <button class="demo-button" onclick="showQuiz()">开始测验</button>
                    <button class="demo-button" onclick="nextQuestion()" style="display: none;" id="nextBtn">下一题</button>
                    <button class="demo-button" onclick="showResults()" style="display: none;" id="resultsBtn">查看结果</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let currentDemo = null;
        let animationFrame = null;
        let demoStep = 0;
        let isAutoPlaying = false;

        // 动画状态
        let commandHistory = [];
        let currentState = 'normal';
        let currentStrategy = 'blur';
        let particles = [];

        // 初始化
        function init() {
            drawBackground();
            createParticles();
            animate();
        }

        function drawBackground() {
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        function createParticles() {
            particles = [];
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    radius: Math.random() * 3 + 1,
                    opacity: Math.random() * 0.5 + 0.2
                });
            }
        }

        function updateParticles() {
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            });
        }

        function drawParticles() {
            particles.forEach(particle => {
                ctx.save();
                ctx.globalAlpha = particle.opacity;
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            });
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawBackground();
            updateParticles();
            drawParticles();
            
            if (currentDemo) {
                drawCurrentDemo();
            }
            
            animationFrame = requestAnimationFrame(animate);
        }

        function highlightPattern(pattern) {
            // 移除所有高亮
            document.querySelectorAll('.pattern-card').forEach(card => {
                card.classList.remove('pulse');
            });
            
            // 添加对应高亮
            const cardId = pattern + 'Card';
            document.getElementById(cardId).classList.add('pulse');
            
            // 更新解释
            updateExplanation(pattern);
            
            setTimeout(() => {
                document.getElementById(cardId).classList.remove('pulse');
            }, 3000);
        }

        function updateExplanation(pattern) {
            const explanationDiv = document.getElementById('explanation');
            let content = '';
            
            switch(pattern) {
                case 'command':
                    content = `
                        <h3>💡 命令模式详解</h3>
                        <p><strong>核心思想：</strong>将"请求"封装成对象，使得可以用不同的请求对客户进行参数化。</p>
                        <p><strong>解决问题：</strong>需求1要求支持撤销和重做功能，命令模式可以将每个操作封装成命令对象，存储在历史记录中。</p>
                        <p><strong>实际应用：</strong>图像处理中的每个操作（如调整亮度、对比度）都是一个命令，可以轻松实现撤销重做。</p>
                        <p><strong>优势：</strong>解耦请求发送者和接收者，支持撤销操作，可以组合命令实现宏操作。</p>
                    `;
                    break;
                case 'state':
                    content = `
                        <h3>💡 状态模式详解</h3>
                        <p><strong>核心思想：</strong>允许对象在内部状态改变时改变它的行为，对象看起来好像修改了它的类。</p>
                        <p><strong>解决问题：</strong>需求2中照片特征与处理操作的复杂逻辑关系，状态模式将每种状态的行为封装到独立的类中。</p>
                        <p><strong>实际应用：</strong>根据照片类型（人像、风景、夜景）自动选择不同的处理策略和参数。</p>
                        <p><strong>优势：</strong>消除庞大的条件分支语句，每个状态都有自己的类，易于维护和扩展。</p>
                    `;
                    break;
                case 'strategy':
                    content = `
                        <h3>💡 策略模式详解</h3>
                        <p><strong>核心思想：</strong>定义一系列算法，把它们封装起来，并且使它们可相互替换。</p>
                        <p><strong>解决问题：</strong>需求3要求灵活选择和替换图像处理算法，策略模式让算法独立于使用它的客户变化。</p>
                        <p><strong>实际应用：</strong>不同的滤镜算法（模糊、锐化、美颜）可以作为不同的策略，用户可以自由选择和切换。</p>
                        <p><strong>优势：</strong>算法可以自由切换，易于扩展新算法，避免使用多重条件判断。</p>
                    `;
                    break;
            }
            
            explanationDiv.innerHTML = content;
        }

        function showDemo(pattern) {
            currentDemo = pattern;
            demoStep = 0;
            
            // 更新解释
            updateExplanation(pattern);
            
            // 开始演示动画
            drawCurrentDemo();
        }

        function drawCurrentDemo() {
            switch(currentDemo) {
                case 'command':
                    drawCommandDemo();
                    break;
                case 'state':
                    drawStateDemo();
                    break;
                case 'strategy':
                    drawStrategyDemo();
                    break;
            }
        }

        function drawCommandDemo() {
            // 绘制命令历史栈
            const stackX = 50;
            const stackY = 50;
            const commandWidth = 100;
            const commandHeight = 40;
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText('命令历史栈', stackX, stackY - 10);
            
            // 绘制命令
            const commands = ['调整亮度', '添加滤镜', '裁剪图片'];
            commands.forEach((cmd, index) => {
                const y = stackY + index * (commandHeight + 10);
                
                // 命令框
                ctx.fillStyle = index < demoStep ? '#667eea' : '#ddd';
                ctx.fillRect(stackX, y, commandWidth, commandHeight);
                
                // 命令文字
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(cmd, stackX + 10, y + 25);
            });
            
            // 绘制撤销重做按钮
            const buttonY = stackY + commands.length * (commandHeight + 10) + 20;
            
            // 撤销按钮
            ctx.fillStyle = '#f5576c';
            ctx.fillRect(stackX, buttonY, 45, 30);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('撤销', stackX + 8, buttonY + 20);
            
            // 重做按钮
            ctx.fillStyle = '#4facfe';
            ctx.fillRect(stackX + 55, buttonY, 45, 30);
            ctx.fillStyle = 'white';
            ctx.fillText('重做', stackX + 63, buttonY + 20);
            
            // 绘制图片预览
            drawImagePreview(300, 100, demoStep);
        }

        function drawStateDemo() {
            // 绘制状态转换图
            const states = [
                { name: '普通照片', x: 150, y: 100, color: '#667eea' },
                { name: '人像照片', x: 350, y: 100, color: '#f093fb' },
                { name: '风景照片', x: 550, y: 100, color: '#4facfe' },
                { name: '夜景照片', x: 350, y: 250, color: '#f5576c' }
            ];
            
            states.forEach((state, index) => {
                // 状态圆圈
                ctx.fillStyle = index === demoStep % states.length ? state.color : '#ddd';
                ctx.beginPath();
                ctx.arc(state.x, state.y, 40, 0, Math.PI * 2);
                ctx.fill();
                
                // 状态文字
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(state.name, state.x, state.y + 5);
            });
            
            // 绘制箭头连接
            drawArrow(190, 100, 310, 100);
            drawArrow(390, 100, 510, 100);
            drawArrow(350, 140, 350, 210);
            
            ctx.textAlign = 'left';
        }

        function drawStrategyDemo() {
            // 绘制策略选择器
            const strategies = ['模糊', '锐化', '美颜', '复古'];
            const strategyY = 80;
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText('选择处理策略:', 50, strategyY - 20);
            
            strategies.forEach((strategy, index) => {
                const x = 50 + index * 120;
                const isSelected = index === demoStep % strategies.length;
                
                // 策略按钮
                ctx.fillStyle = isSelected ? '#667eea' : '#ddd';
                ctx.fillRect(x, strategyY, 100, 40);
                
                // 策略文字
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText(strategy, x + 25, strategyY + 25);
            });
            
            // 绘制处理效果
            const effectY = 200;
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('处理效果预览:', 50, effectY - 10);
            
            // 原图
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(50, effectY, 150, 100);
            ctx.fillStyle = '#333';
            ctx.fillText('原图', 110, effectY + 55);
            
            // 箭头
            drawArrow(220, effectY + 50, 280, effectY + 50);
            
            // 处理后
            const currentStrategy = strategies[demoStep % strategies.length];
            ctx.fillStyle = getStrategyColor(currentStrategy);
            ctx.fillRect(300, effectY, 150, 100);
            ctx.fillStyle = 'white';
            ctx.fillText(currentStrategy + '效果', 350, effectY + 55);
        }

        function getStrategyColor(strategy) {
            const colors = {
                '模糊': '#667eea',
                '锐化': '#f093fb',
                '美颜': '#4facfe',
                '复古': '#f5576c'
            };
            return colors[strategy] || '#ddd';
        }

        function drawImagePreview(x, y, step) {
            // 绘制图片框
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(x, y, 200, 150);
            ctx.strokeStyle = '#ddd';
            ctx.strokeRect(x, y, 200, 150);
            
            // 根据步骤绘制不同效果
            const effects = ['原图', '调亮', '加滤镜', '已裁剪'];
            const colors = ['#f8f9fa', '#fff3cd', '#d1ecf1', '#d4edda'];
            
            if (step < effects.length) {
                ctx.fillStyle = colors[step];
                ctx.fillRect(x + 10, y + 10, 180, 130);
                
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(effects[step], x + 100, y + 80);
            }
            
            ctx.textAlign = 'left';
        }

        function drawArrow(fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI / 6), toY - 10 * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI / 6), toY - 10 * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        function nextStep() {
            if (currentDemo) {
                demoStep++;
                drawCurrentDemo();
            }
        }

        function resetDemo() {
            demoStep = 0;
            if (currentDemo) {
                drawCurrentDemo();
            }
        }

        function autoPlay() {
            if (isAutoPlaying) {
                isAutoPlaying = false;
                document.querySelector('.control-btn:nth-child(3)').textContent = '自动播放';
                return;
            }

            if (!currentDemo) {
                showDemo('command');
            }

            isAutoPlaying = true;
            document.querySelector('.control-btn:nth-child(3)').textContent = '停止播放';

            const autoInterval = setInterval(() => {
                if (!isAutoPlaying) {
                    clearInterval(autoInterval);
                    return;
                }

                nextStep();

                if (demoStep > 10) {
                    demoStep = 0;
                }
            }, 1500);
        }

        // 添加点击画布交互
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检测点击区域并响应
            handleCanvasClick(x, y);
        });

        function handleCanvasClick(x, y) {
            if (!currentDemo) return;

            switch(currentDemo) {
                case 'command':
                    // 检测撤销重做按钮点击
                    if (x >= 50 && x <= 95 && y >= 170 && y <= 200) {
                        // 撤销按钮
                        if (demoStep > 0) demoStep--;
                        drawCurrentDemo();
                        updateCommandExplanation('undo');
                    } else if (x >= 105 && x <= 150 && y >= 170 && y <= 200) {
                        // 重做按钮
                        if (demoStep < 3) demoStep++;
                        drawCurrentDemo();
                        updateCommandExplanation('redo');
                    }
                    break;
                case 'state':
                    // 检测状态圆圈点击
                    const states = [
                        { x: 150, y: 100 }, { x: 350, y: 100 },
                        { x: 550, y: 100 }, { x: 350, y: 250 }
                    ];
                    states.forEach((state, index) => {
                        const distance = Math.sqrt((x - state.x) ** 2 + (y - state.y) ** 2);
                        if (distance <= 40) {
                            demoStep = index;
                            drawCurrentDemo();
                            updateStateExplanation(index);
                        }
                    });
                    break;
                case 'strategy':
                    // 检测策略按钮点击
                    for (let i = 0; i < 4; i++) {
                        const btnX = 50 + i * 120;
                        if (x >= btnX && x <= btnX + 100 && y >= 80 && y <= 120) {
                            demoStep = i;
                            drawCurrentDemo();
                            updateStrategyExplanation(i);
                        }
                    }
                    break;
            }
        }

        function updateCommandExplanation(action) {
            const explanationDiv = document.getElementById('explanation');
            let actionText = action === 'undo' ? '撤销' : '重做';
            explanationDiv.innerHTML += `
                <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <strong>🎯 ${actionText}操作演示</strong><br>
                    当前步骤: ${demoStep}/3 - 命令模式通过维护命令历史栈，轻松实现${actionText}功能！
                </div>
            `;
        }

        function updateStateExplanation(stateIndex) {
            const states = ['普通照片', '人像照片', '风景照片', '夜景照片'];
            const behaviors = [
                '基础处理：调整亮度、对比度',
                '人像优化：美颜、磨皮、大眼',
                '风景增强：饱和度、清晰度、HDR',
                '夜景处理：降噪、提亮、星空模式'
            ];

            const explanationDiv = document.getElementById('explanation');
            explanationDiv.innerHTML += `
                <div style="background: #fce4ec; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <strong>🔄 状态切换演示</strong><br>
                    当前状态: ${states[stateIndex]}<br>
                    对应行为: ${behaviors[stateIndex]}
                </div>
            `;
        }

        function updateStrategyExplanation(strategyIndex) {
            const strategies = ['模糊', '锐化', '美颜', '复古'];
            const descriptions = [
                '高斯模糊算法，营造柔和效果',
                'USM锐化算法，增强图像细节',
                '智能美颜算法，自然美化人像',
                '复古滤镜算法，营造怀旧氛围'
            ];

            const explanationDiv = document.getElementById('explanation');
            explanationDiv.innerHTML += `
                <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin-top: 15px;">
                    <strong>🎲 策略切换演示</strong><br>
                    当前策略: ${strategies[strategyIndex]}<br>
                    算法说明: ${descriptions[strategyIndex]}
                </div>
            `;
        }

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                    nextStep();
                    break;
                case 'ArrowLeft':
                    if (demoStep > 0) {
                        demoStep--;
                        if (currentDemo) drawCurrentDemo();
                    }
                    break;
                case ' ':
                    e.preventDefault();
                    autoPlay();
                    break;
                case 'r':
                    resetDemo();
                    break;
                case '1':
                    showDemo('command');
                    break;
                case '2':
                    showDemo('state');
                    break;
                case '3':
                    showDemo('strategy');
                    break;
            }
        });

        // 添加提示信息
        function showKeyboardHints() {
            const hintsDiv = document.createElement('div');
            hintsDiv.innerHTML = `
                <div style="position: fixed; bottom: 20px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 15px; border-radius: 10px; font-size: 12px; z-index: 1000;">
                    <strong>⌨️ 快捷键提示</strong><br>
                    ← → : 上一步/下一步<br>
                    空格 : 自动播放<br>
                    R : 重置<br>
                    1,2,3 : 切换演示
                </div>
            `;
            document.body.appendChild(hintsDiv);

            setTimeout(() => {
                hintsDiv.style.opacity = '0';
                hintsDiv.style.transition = 'opacity 1s';
                setTimeout(() => hintsDiv.remove(), 1000);
            }, 5000);
        }

        // 初始化
        init();

        // 测验相关变量
        let currentQuestionIndex = 0;
        let score = 0;
        let quizStarted = false;

        const quizQuestions = [
            {
                question: "根据题目描述，为了支持灵活的撤销与重做等行为，应该采用哪种设计模式？",
                options: ["A. 工厂模式", "B. 责任链模式", "C. 中介者模式", "D. 命令模式"],
                correct: 3,
                explanation: "命令模式将请求封装为对象，可以对请求排队、记录请求日志，以及支持可撤销的操作。这正是实现撤销重做功能的最佳选择。"
            },
            {
                question: "为了封装图像操作与照片特征之间的复杂逻辑关系，应该采用哪种设计模式？",
                options: ["A. 状态模式", "B. 观察者模式", "C. 装饰器模式", "D. 适配器模式"],
                correct: 0,
                explanation: "状态模式将每一个条件分支放入一个独立的类中，可以根据对象自身的情况将对象的状态作为一个对象，这一对象可以不依赖于其他对象而独立变化。"
            },
            {
                question: "为了实现图像处理算法的灵活选择与替换，应该采用哪种设计模式？",
                options: ["A. 单例模式", "B. 策略模式", "C. 建造者模式", "D. 代理模式"],
                correct: 1,
                explanation: "策略模式定义一系列的算法，把它们封装起来，并且使它们可相互替换，使得算法可独立于使用它的客户而变化。"
            }
        ];

        function showQuiz() {
            document.getElementById('quizSection').style.display = 'block';
            quizStarted = true;
            currentQuestionIndex = 0;
            score = 0;
            displayQuestion();

            // 滚动到测验区域
            document.getElementById('quizSection').scrollIntoView({ behavior: 'smooth' });
        }

        function displayQuestion() {
            const question = quizQuestions[currentQuestionIndex];
            const quizContent = document.getElementById('quizContent');

            quizContent.innerHTML = `
                <div class="quiz-question">
                    <h3>题目 ${currentQuestionIndex + 1}/${quizQuestions.length}: ${question.question}</h3>
                    <div class="quiz-options">
                        ${question.options.map((option, index) =>
                            `<div class="quiz-option" onclick="selectOption(this, ${index === question.correct}, ${index})">${option}</div>`
                        ).join('')}
                    </div>
                    <div class="quiz-explanation" style="display: none;">
                        <h4>✅ 正确答案：${question.options[question.correct]}</h4>
                        <p>${question.explanation}</p>
                    </div>
                </div>
            `;

            // 更新按钮状态
            document.getElementById('nextBtn').style.display = 'none';
            document.getElementById('resultsBtn').style.display = 'none';
        }

        function selectOption(element, isCorrect, optionIndex) {
            // 禁用所有选项
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'incorrect');
                } else if (option.textContent.includes(quizQuestions[currentQuestionIndex].options[quizQuestions[currentQuestionIndex].correct])) {
                    option.classList.add('correct');
                }
            });

            // 显示解释
            document.querySelector('.quiz-explanation').style.display = 'block';

            // 更新分数
            if (isCorrect) {
                score++;
                showFloatingHint('🎉 回答正确！', '#4caf50');
            } else {
                showFloatingHint('❌ 回答错误，查看正确答案', '#f44336');
            }

            // 显示下一题或结果按钮
            setTimeout(() => {
                if (currentQuestionIndex < quizQuestions.length - 1) {
                    document.getElementById('nextBtn').style.display = 'inline-block';
                } else {
                    document.getElementById('resultsBtn').style.display = 'inline-block';
                }
            }, 1500);
        }

        function nextQuestion() {
            currentQuestionIndex++;
            displayQuestion();
        }

        function showResults() {
            const percentage = Math.round((score / quizQuestions.length) * 100);
            let message = '';
            let color = '';

            if (percentage >= 80) {
                message = '🏆 优秀！您已经很好地掌握了设计模式的核心概念！';
                color = '#4caf50';
            } else if (percentage >= 60) {
                message = '👍 良好！继续学习，您会掌握得更好！';
                color = '#ff9800';
            } else {
                message = '💪 加油！建议重新学习相关内容，多做练习！';
                color = '#f44336';
            }

            const quizContent = document.getElementById('quizContent');
            quizContent.innerHTML = `
                <div class="score-display">
                    <h3>测验完成！</h3>
                    <p style="font-size: 2rem; margin: 20px 0;">${score}/${quizQuestions.length}</p>
                    <p style="font-size: 1.5rem; margin-bottom: 20px;">正确率: ${percentage}%</p>
                    <p style="color: ${color}; font-weight: bold;">${message}</p>
                </div>
                <div style="text-align: center; margin-top: 30px;">
                    <button class="demo-button" onclick="restartQuiz()">重新测验</button>
                    <button class="demo-button" onclick="backToLearning()">返回学习</button>
                </div>
            `;

            // 隐藏控制按钮
            document.getElementById('nextBtn').style.display = 'none';
            document.getElementById('resultsBtn').style.display = 'none';
        }

        function restartQuiz() {
            currentQuestionIndex = 0;
            score = 0;
            displayQuestion();
        }

        function backToLearning() {
            document.getElementById('quizSection').style.display = 'none';
            document.querySelector('.header').scrollIntoView({ behavior: 'smooth' });
        }

        function showFloatingHint(text, color) {
            const hint = document.createElement('div');
            hint.className = 'floating-hint';
            hint.style.background = color;
            hint.textContent = text;
            document.body.appendChild(hint);

            setTimeout(() => {
                hint.style.opacity = '0';
                hint.style.transition = 'opacity 0.5s';
                setTimeout(() => hint.remove(), 500);
            }, 2000);
        }

        // 显示快捷键提示
        setTimeout(showKeyboardHints, 2000);

        // 添加测验入口按钮
        setTimeout(() => {
            const quizButton = document.createElement('button');
            quizButton.className = 'demo-button';
            quizButton.textContent = '🎯 开始知识测验';
            quizButton.style.position = 'fixed';
            quizButton.style.bottom = '20px';
            quizButton.style.left = '20px';
            quizButton.style.zIndex = '1000';
            quizButton.onclick = showQuiz;
            document.body.appendChild(quizButton);
        }, 3000);
    </script>
</body>
</html>
