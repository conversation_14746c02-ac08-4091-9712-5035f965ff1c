<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向服务开发方法学习 - 三层抽象动画</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .story-canvas {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            overflow: hidden;
            position: relative;
        }

        canvas {
            display: block;
            width: 100%;
            height: 600px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .btn.active {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .layer-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            transform: translateX(-100px);
            opacity: 0;
            transition: all 0.6s ease;
        }

        .layer-card.show {
            transform: translateX(0);
            opacity: 1;
        }

        .layer-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .layer-number {
            background: rgba(255,255,255,0.2);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .layer-description {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .layer-example {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            font-style: italic;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .interactive-area {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            text-align: center;
        }

        .quiz-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
            margin: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #74b9ff, #0984e3);
            width: 0%;
            transition: width 0.5s ease;
        }

        .vocabulary-section {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin: 20px 0;
        }

        .vocab-word {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .vocab-word:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>面向服务开发方法学习</h1>
            <p>通过动画故事理解SO的三层抽象结构</p>
        </div>

        <div class="story-canvas">
            <canvas id="storyCanvas" width="1000" height="600"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="showLayer(0)">显示操作层</button>
            <button class="btn" onclick="showLayer(1)">显示服务层</button>
            <button class="btn" onclick="showLayer(2)">显示业务流程层</button>
            <button class="btn" onclick="showAllLayers()">显示完整架构</button>
            <button class="btn" onclick="startAnimation()">开始动画演示</button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="explanation">
            <h2 style="color: #2d3436; margin-bottom: 30px;">SO开发方法的三层抽象结构</h2>
            <div id="layerCards"></div>
        </div>

        <div class="vocabulary-section">
            <h3 style="margin-bottom: 20px;">核心词汇学习</h3>
            <p style="margin-bottom: 20px;">点击词汇查看详细解释：</p>
            <div id="vocabularyArea"></div>
        </div>

        <div class="interactive-area">
            <h3 style="color: #2d3436; margin-bottom: 20px;">知识测试</h3>
            <div id="quizArea"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        let currentLayer = -1;
        let animationFrame = 0;
        let isAnimating = false;

        // 三层抽象数据
        const layers = [
            {
                name: "操作层 (Operation)",
                level: "最低层",
                description: "代表单个逻辑单元的事物，包含特定的结构化接口，并且返回结构化的响应",
                example: "用户登录操作、数据查询操作、文件上传操作",
                color: "#e17055",
                y: 450
            },
            {
                name: "服务层 (Service)", 
                level: "第二层",
                description: "代表操作的逻辑分组，将相关的操作组织在一起",
                example: "用户管理服务、订单处理服务、支付服务",
                color: "#74b9ff",
                y: 300
            },
            {
                name: "业务流程层 (Business Process)",
                level: "最高层", 
                description: "为了实现特定业务目标而执行的一组长期运行的动作或活动",
                example: "完整的电商购买流程、客户服务流程、供应链管理流程",
                color: "#00b894",
                y: 150
            }
        ];

        // 词汇数据
        const vocabulary = [
            {
                word: "Service-Oriented",
                chinese: "面向服务",
                meaning: "一种软件设计方法，将功能组织为可重用的服务",
                usage: "Service-Oriented Architecture (SOA) - 面向服务架构"
            },
            {
                word: "Decoupling", 
                chinese: "解耦",
                meaning: "将系统组件之间的依赖关系分离，提高灵活性",
                usage: "Interface decoupling - 接口解耦"
            },
            {
                word: "Interface",
                chinese: "接口",
                meaning: "定义组件间交互方式的契约",
                usage: "Structured interface - 结构化接口"
            },
            {
                word: "Operation",
                chinese: "操作",
                meaning: "执行特定功能的最小逻辑单元",
                usage: "CRUD operations - 增删改查操作"
            },
            {
                word: "Orchestration",
                chinese: "编排",
                meaning: "协调多个服务按特定顺序执行",
                usage: "Service orchestration - 服务编排"
            }
        ];

        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('面向服务开发方法 - 三层抽象架构', canvas.width / 2, 40);

            if (currentLayer === -1) {
                drawAllLayers();
            } else {
                drawSingleLayer(currentLayer);
            }

            if (isAnimating) {
                drawDataFlow();
            }

            animationFrame++;
        }

        function drawAllLayers() {
            layers.forEach((layer, index) => {
                drawLayerBox(layer, index, true);
            });
            
            // 绘制层间连接线
            ctx.strokeStyle = '#74b9ff';
            ctx.lineWidth = 3;
            ctx.setLineDash([10, 5]);
            
            for (let i = 0; i < layers.length - 1; i++) {
                ctx.beginPath();
                ctx.moveTo(canvas.width / 2, layers[i].y - 30);
                ctx.lineTo(canvas.width / 2, layers[i + 1].y + 30);
                ctx.stroke();
                
                // 箭头
                drawArrow(canvas.width / 2, layers[i + 1].y + 30, 0);
            }
            
            ctx.setLineDash([]);
        }

        function drawSingleLayer(layerIndex) {
            const layer = layers[layerIndex];
            drawLayerBox(layer, layerIndex, false);
            
            // 绘制详细说明
            ctx.fillStyle = '#636e72';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            
            const lines = wrapText(layer.description, 600);
            lines.forEach((line, index) => {
                ctx.fillText(line, canvas.width / 2, layer.y + 80 + index * 25);
            });
        }

        function drawLayerBox(layer, index, isOverview) {
            const boxWidth = isOverview ? 300 : 400;
            const boxHeight = isOverview ? 60 : 80;
            const x = canvas.width / 2 - boxWidth / 2;
            const y = layer.y - boxHeight / 2;
            
            // 动画效果
            const pulse = isAnimating ? Math.sin(animationFrame * 0.1 + index) * 5 + 5 : 0;
            
            // 绘制层级框
            ctx.fillStyle = layer.color;
            ctx.shadowColor = 'rgba(0,0,0,0.2)';
            ctx.shadowBlur = 10 + pulse;
            ctx.shadowOffsetY = 5;
            ctx.fillRect(x, y, boxWidth, boxHeight);
            ctx.shadowBlur = 0;
            ctx.shadowOffsetY = 0;
            
            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = isOverview ? 'bold 16px Microsoft YaHei' : 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(layer.name, canvas.width / 2, layer.y - 10);
            
            ctx.font = isOverview ? '12px Microsoft YaHei' : '14px Microsoft YaHei';
            ctx.fillText(layer.level, canvas.width / 2, layer.y + 10);
        }

        function drawDataFlow() {
            // 绘制数据流动动画
            const flowY = 500;
            const flowSpeed = animationFrame * 3;
            
            for (let i = 0; i < 5; i++) {
                const x = (flowSpeed + i * 100) % (canvas.width + 50);
                
                ctx.fillStyle = '#fd79a8';
                ctx.beginPath();
                ctx.arc(x, flowY, 8, 0, 2 * Math.PI);
                ctx.fill();
                
                // 数据包标签
                ctx.fillStyle = '#2d3436';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('数据', x, flowY - 15);
            }
        }

        function drawArrow(x, y, rotation) {
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(rotation);
            
            ctx.fillStyle = '#74b9ff';
            ctx.beginPath();
            ctx.moveTo(0, -10);
            ctx.lineTo(-8, 5);
            ctx.lineTo(8, 5);
            ctx.closePath();
            ctx.fill();
            
            ctx.restore();
        }

        function wrapText(text, maxWidth) {
            const words = text.split('');
            const lines = [];
            let currentLine = '';
            
            for (let i = 0; i < words.length; i++) {
                const testLine = currentLine + words[i];
                const metrics = ctx.measureText(testLine);
                
                if (metrics.width > maxWidth && currentLine !== '') {
                    lines.push(currentLine);
                    currentLine = words[i];
                } else {
                    currentLine = testLine;
                }
            }
            lines.push(currentLine);
            return lines;
        }

        function showLayer(index) {
            currentLayer = index;
            updateLayerCards(index);
            updateProgress((index + 1) / 4 * 100);
            
            // 更新按钮状态
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.btn')[index].classList.add('active');
        }

        function showAllLayers() {
            currentLayer = -1;
            updateLayerCards(-1);
            updateProgress(100);
            
            // 更新按钮状态
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.btn')[3].classList.add('active');
        }

        function startAnimation() {
            isAnimating = !isAnimating;
            const btn = document.querySelectorAll('.btn')[4];
            btn.textContent = isAnimating ? '停止动画' : '开始动画演示';
        }

        function updateLayerCards(selectedLayer) {
            const container = document.getElementById('layerCards');
            container.innerHTML = '';
            
            const layersToShow = selectedLayer === -1 ? layers : [layers[selectedLayer]];
            
            layersToShow.forEach((layer, index) => {
                setTimeout(() => {
                    const card = document.createElement('div');
                    card.className = 'layer-card';
                    card.innerHTML = `
                        <div class="layer-title">
                            <div class="layer-number">${selectedLayer === -1 ? index + 1 : selectedLayer + 1}</div>
                            ${layer.name}
                        </div>
                        <div class="layer-description">${layer.description}</div>
                        <div class="layer-example">
                            <strong>示例：</strong>${layer.example}
                        </div>
                    `;
                    container.appendChild(card);
                    
                    setTimeout(() => {
                        card.classList.add('show');
                    }, 100);
                }, index * 300);
            });
        }

        function updateProgress(percentage) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = percentage + '%';
        }

        function createVocabulary() {
            const vocabArea = document.getElementById('vocabularyArea');
            
            vocabulary.forEach(vocab => {
                const vocabElement = document.createElement('div');
                vocabElement.className = 'vocab-word';
                vocabElement.textContent = vocab.word;
                vocabElement.onclick = () => showVocabDetail(vocab);
                vocabArea.appendChild(vocabElement);
            });
        }

        function showVocabDetail(vocab) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); display: flex; align-items: center;
                justify-content: center; z-index: 1000;
            `;
            
            modal.innerHTML = `
                <div style="background: white; padding: 40px; border-radius: 20px; max-width: 500px; text-align: center;">
                    <h3 style="color: #2d3436; margin-bottom: 20px;">${vocab.word}</h3>
                    <p style="color: #74b9ff; font-size: 1.2rem; margin-bottom: 15px;">${vocab.chinese}</p>
                    <p style="color: #636e72; margin-bottom: 20px;">${vocab.meaning}</p>
                    <p style="color: #00b894; font-style: italic; margin-bottom: 30px;">${vocab.usage}</p>
                    <button onclick="this.parentElement.parentElement.remove()" 
                            style="padding: 10px 20px; border: none; border-radius: 25px; 
                                   background: #74b9ff; color: white; cursor: pointer;">关闭</button>
                </div>
            `;
            
            document.body.appendChild(modal);
        }

        function createQuiz() {
            const quizArea = document.getElementById('quizArea');
            const questions = [
                {
                    question: "SO开发方法的最低层是什么？",
                    options: ["操作", "服务", "业务流程", "接口"],
                    correct: 0,
                    explanation: "操作(Operation)是最低层，代表单个逻辑单元的事物"
                },
                {
                    question: "面向服务开发方法将什么进行解耦？",
                    options: ["类和对象", "接口的定义与实现", "服务和流程", "数据和方法"],
                    correct: 1,
                    explanation: "SO方法将接口的定义与实现进行解耦"
                },
                {
                    question: "最高层的业务流程主要用于？",
                    options: ["数据存储", "接口定义", "实现特定业务目标", "系统监控"],
                    correct: 2,
                    explanation: "业务流程是为了实现特定业务目标而执行的一组长期运行的动作或活动"
                }
            ];

            let currentQuestion = 0;

            function showQuestion() {
                if (currentQuestion < questions.length) {
                    const q = questions[currentQuestion];
                    quizArea.innerHTML = `
                        <h4 style="margin-bottom: 20px;">${q.question}</h4>
                        <div style="margin-bottom: 20px;">
                            ${q.options.map((option, index) => 
                                `<button class="btn quiz-btn" onclick="checkAnswer(${index}, ${q.correct}, '${q.explanation}')">${option}</button>`
                            ).join('')}
                        </div>
                        <p style="color: #636e72;">问题 ${currentQuestion + 1} / ${questions.length}</p>
                    `;
                }
            }

            window.checkAnswer = function(selected, correct, explanation) {
                const resultColor = selected === correct ? 'green' : 'red';
                const resultText = selected === correct ? '✓ 正确！' : '✗ 错误';
                
                quizArea.innerHTML += `
                    <div style="color: ${resultColor}; margin-top: 20px; padding: 15px; 
                                background: ${selected === correct ? '#d4edda' : '#f8d7da'}; 
                                border-radius: 10px;">
                        <p style="font-weight: bold;">${resultText}</p>
                        <p style="margin-top: 10px;">${explanation}</p>
                    </div>
                `;
                
                setTimeout(() => {
                    currentQuestion++;
                    if (currentQuestion < questions.length) {
                        showQuestion();
                    } else {
                        quizArea.innerHTML += '<p style="margin-top: 30px; font-size: 1.2rem; color: #00b894;">🎉 测试完成！</p>';
                    }
                }, 3000);
            };

            showQuestion();
        }

        // 初始化
        function init() {
            showAllLayers();
            createVocabulary();
            setTimeout(createQuiz, 2000);
            
            // 开始动画循环
            function animate() {
                drawScene();
                requestAnimationFrame(animate);
            }
            animate();
        }

        init();
    </script>
</body>
</html>
