<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集合学习 - 零基础互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: center;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #667eea;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .concept-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .concept-desc {
            color: #666;
            line-height: 1.6;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .nav-item {
            display: block;
            color: #333;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            margin: 5px 0;
        }

        .nav-item:hover {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="#concept" class="nav-item">集合概念</a>
        <a href="#representation" class="nav-item">表示方法</a>
        <a href="#properties" class="nav-item">集合性质</a>
        <a href="#description" class="nav-item">描述法</a>
        <a href="#uniqueness" class="nav-item">互异性</a>
        <a href="#equations" class="nav-item">方程解集</a>
        <a href="#practice" class="nav-item">实践练习</a>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">集合学习</h1>
            <p class="subtitle">零基础互动教学 - 从概念到应用</p>
        </div>

        <!-- 1. 集合的概念 -->
        <div class="section" id="concept">
            <h2 class="section-title">1. 集合的概念</h2>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="600" height="400"></canvas>
            </div>
            <p class="explanation">
                集合是数学中最基本的概念之一。想象一个装满不同物品的盒子，这个盒子就是集合，里面的物品就是元素。
                点击画布看看集合是如何形成的！
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="animateConcept()">演示集合概念</button>
                <button class="interactive-btn" onclick="resetConcept()">重新开始</button>
            </div>
        </div>

        <!-- 2. 集合的性质及表示 -->
        <div class="section" id="representation">
            <h2 class="section-title">2. 集合的性质及表示</h2>
            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">列举法</div>
                    <div class="concept-desc">
                        用花括号{}把集合中的元素一一列举出来<br>
                        例如：A = {1, 2, 3, 4, 5}
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">描述法</div>
                    <div class="concept-desc">
                        用集合中元素的共同特征来表示集合<br>
                        例如：B = {x | x > 0, x ∈ N}
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">图示法</div>
                    <div class="concept-desc">
                        用韦恩图等图形来表示集合<br>
                        直观地显示集合之间的关系
                    </div>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="representationCanvas" width="600" height="400"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="showListMethod()">列举法演示</button>
                <button class="interactive-btn" onclick="showDescMethod()">描述法演示</button>
                <button class="interactive-btn" onclick="showVennDiagram()">韦恩图演示</button>
            </div>
        </div>

        <!-- 3. 集合的描述法 -->
        <div class="section" id="description">
            <h2 class="section-title">3. 集合的描述法</h2>
            <div class="canvas-container">
                <canvas id="descriptionCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                描述法是用集合中元素的共同特征来表示集合的方法。格式：{x | P(x)}，读作"满足条件P(x)的x的集合"
            </p>
            <div style="text-align: center; margin: 20px 0;">
                <button class="interactive-btn" onclick="animateDescriptionBasic()">基础格式演示</button>
                <button class="interactive-btn" onclick="animateNumberSet()">数字集合演示</button>
                <button class="interactive-btn" onclick="animateEquationSet()">方程解集演示</button>
                <button class="interactive-btn" onclick="animateGeometrySet()">几何图形演示</button>
                <button class="interactive-btn" onclick="startInteractiveFilter()">🎮 互动筛选器</button>
                <button class="interactive-btn" onclick="resetDescriptionCanvas()">重置画布</button>
            </div>

            <!-- 互动控制面板 -->
            <div id="interactivePanel" style="display: none; background: rgba(255,255,255,0.9); border-radius: 15px; padding: 20px; margin: 20px 0; text-align: center;">
                <h3 style="color: #333; margin-bottom: 15px;">🎯 互动筛选器 - 体验描述法</h3>
                <div style="margin: 15px 0;">
                    <label style="color: #333; font-weight: bold;">选择条件类型：</label>
                    <select id="conditionType" onchange="updateCondition()" style="margin: 0 10px; padding: 5px 10px; border-radius: 5px; border: 2px solid #667eea;">
                        <option value="greater">大于某数 (x > n)</option>
                        <option value="less">小于某数 (x < n)</option>
                        <option value="range">范围 (a < x < b)</option>
                        <option value="even">偶数 (x = 2k)</option>
                        <option value="odd">奇数 (x = 2k+1)</option>
                        <option value="square">完全平方数</option>
                    </select>
                </div>
                <div id="parameterControls" style="margin: 15px 0;">
                    <label style="color: #333;">参数值：</label>
                    <input type="range" id="paramSlider" min="0" max="10" value="3" oninput="updateFilter()" style="margin: 0 10px;">
                    <span id="paramValue" style="color: #667eea; font-weight: bold;">3</span>
                </div>
                <div style="margin: 15px 0;">
                    <button class="interactive-btn" onclick="startAnimation()" style="background: linear-gradient(45deg, #44ff44, #00cc88);">🚀 开始筛选动画</button>
                    <button class="interactive-btn" onclick="hideInteractivePanel()">关闭面板</button>
                </div>
            </div>
            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">基本格式</div>
                    <div class="concept-desc">
                        {x | P(x)}<br>
                        x：代表集合中的元素<br>
                        |：读作"使得"或"满足"<br>
                        P(x)：元素x必须满足的条件
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">常见条件</div>
                    <div class="concept-desc">
                        不等式：x > 0, x ≤ 5<br>
                        方程：x² = 4<br>
                        范围：x ∈ N, x ∈ R<br>
                        复合条件：x > 0 且 x < 10
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">实际应用</div>
                    <div class="concept-desc">
                        正整数集：{x | x > 0, x ∈ Z}<br>
                        单位圆：{(x,y) | x² + y² = 1}<br>
                        偶数集：{x | x = 2k, k ∈ Z}
                    </div>
                </div>
            </div>
        </div>

        <!-- 4. 元素的互异性 -->
        <div class="section" id="uniqueness">
            <h2 class="section-title">4. 元素的互异性</h2>
            <div class="canvas-container">
                <canvas id="uniquenessCanvas" width="600" height="400"></canvas>
            </div>
            <p class="explanation">
                集合中的元素必须是不同的，相同的元素只能出现一次。这就是集合的互异性。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="demonstrateUniqueness()">演示互异性</button>
                <button class="interactive-btn" onclick="showDuplicateExample()">重复元素示例</button>
            </div>
        </div>

        <!-- 5. 含参方程的解集 -->
        <div class="section" id="equations">
            <h2 class="section-title">5. 含参方程的解集</h2>
            <div class="canvas-container">
                <canvas id="equationsCanvas" width="600" height="400"></canvas>
            </div>
            <p class="explanation">
                当方程中含有参数时，解集的元素个数可能会发生变化。我们需要考虑互异性的要求。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="solveParametricEquation()">求解含参方程</button>
                <button class="interactive-btn" onclick="analyzeUniqueness()">分析互异性</button>
            </div>
        </div>

        <!-- 6. 二次型方程解集个数问题 -->
        <div class="section" id="practice">
            <h2 class="section-title">6. 二次型方程解集个数问题</h2>
            <div class="canvas-container">
                <canvas id="practiceCanvas" width="600" height="400"></canvas>
            </div>
            <p class="explanation">
                二次方程的解集个数取决于判别式Δ的值。让我们通过动画来理解这个概念。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="analyzeQuadratic()">分析二次方程</button>
                <button class="interactive-btn" onclick="showDiscriminant()">判别式演示</button>
                <button class="interactive-btn" onclick="interactiveQuiz()">互动练习</button>
            </div>
        </div>
    </div>

    <script>
        // 获取所有画布
        const conceptCanvas = document.getElementById('conceptCanvas');
        const conceptCtx = conceptCanvas.getContext('2d');
        
        const representationCanvas = document.getElementById('representationCanvas');
        const representationCtx = representationCanvas.getContext('2d');
        
        const descriptionCanvas = document.getElementById('descriptionCanvas');
        const descriptionCtx = descriptionCanvas.getContext('2d');
        
        const uniquenessCanvas = document.getElementById('uniquenessCanvas');
        const uniquenessCtx = uniquenessCanvas.getContext('2d');
        
        const equationsCanvas = document.getElementById('equationsCanvas');
        const equationsCtx = equationsCanvas.getContext('2d');
        
        const practiceCanvas = document.getElementById('practiceCanvas');
        const practiceCtx = practiceCanvas.getContext('2d');

        // 动画变量
        let animationFrame;
        let currentStep = 0;

        // 1. 集合概念动画
        function animateConcept() {
            conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);
            
            // 绘制集合圆圈
            conceptCtx.beginPath();
            conceptCtx.arc(300, 200, 150, 0, 2 * Math.PI);
            conceptCtx.strokeStyle = '#667eea';
            conceptCtx.lineWidth = 3;
            conceptCtx.stroke();
            
            // 添加标签
            conceptCtx.font = '20px Microsoft YaHei';
            conceptCtx.fillStyle = '#333';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('集合 A', 300, 50);
            
            // 动画添加元素
            const elements = ['1', '2', '3', '4', '5'];
            const positions = [
                {x: 250, y: 150},
                {x: 350, y: 150},
                {x: 220, y: 220},
                {x: 380, y: 220},
                {x: 300, y: 250}
            ];
            
            elements.forEach((element, index) => {
                setTimeout(() => {
                    conceptCtx.beginPath();
                    conceptCtx.arc(positions[index].x, positions[index].y, 20, 0, 2 * Math.PI);
                    conceptCtx.fillStyle = '#764ba2';
                    conceptCtx.fill();
                    
                    conceptCtx.fillStyle = 'white';
                    conceptCtx.font = '16px Microsoft YaHei';
                    conceptCtx.textAlign = 'center';
                    conceptCtx.fillText(element, positions[index].x, positions[index].y + 5);
                }, index * 500);
            });
            
            // 添加说明文字
            setTimeout(() => {
                conceptCtx.fillStyle = '#333';
                conceptCtx.font = '16px Microsoft YaHei';
                conceptCtx.textAlign = 'center';
                conceptCtx.fillText('A = {1, 2, 3, 4, 5}', 300, 350);
            }, 2500);
        }

        function resetConcept() {
            conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);
            conceptCtx.fillStyle = '#999';
            conceptCtx.font = '18px Microsoft YaHei';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('点击"演示集合概念"开始学习', 300, 200);
        }

        // 2. 表示方法演示
        function showListMethod() {
            representationCtx.clearRect(0, 0, representationCanvas.width, representationCanvas.height);
            
            representationCtx.fillStyle = '#333';
            representationCtx.font = '24px Microsoft YaHei';
            representationCtx.textAlign = 'center';
            representationCtx.fillText('列举法', 300, 50);
            
            representationCtx.font = '18px Microsoft YaHei';
            representationCtx.fillText('A = {1, 2, 3, 4, 5}', 300, 120);
            representationCtx.fillText('B = {红, 黄, 蓝, 绿}', 300, 160);
            representationCtx.fillText('C = {a, b, c, d, e}', 300, 200);
            
            // 绘制花括号强调
            representationCtx.strokeStyle = '#667eea';
            representationCtx.lineWidth = 3;
            representationCtx.beginPath();
            representationCtx.arc(180, 120, 10, -Math.PI/2, Math.PI/2);
            representationCtx.arc(420, 120, 10, Math.PI/2, 3*Math.PI/2);
            representationCtx.stroke();
        }

        function showDescMethod() {
            representationCtx.clearRect(0, 0, representationCanvas.width, representationCanvas.height);
            
            representationCtx.fillStyle = '#333';
            representationCtx.font = '24px Microsoft YaHei';
            representationCtx.textAlign = 'center';
            representationCtx.fillText('描述法', 300, 50);
            
            representationCtx.font = '18px Microsoft YaHei';
            representationCtx.fillText('A = {x | x > 0, x ∈ N}', 300, 120);
            representationCtx.fillText('B = {x | x² - 5x + 6 = 0}', 300, 160);
            representationCtx.fillText('C = {(x,y) | x² + y² = 1}', 300, 200);
            
            representationCtx.font = '14px Microsoft YaHei';
            representationCtx.fillStyle = '#666';
            representationCtx.fillText('读作：满足条件的x的集合', 300, 250);
        }

        function showVennDiagram() {
            representationCtx.clearRect(0, 0, representationCanvas.width, representationCanvas.height);
            
            representationCtx.fillStyle = '#333';
            representationCtx.font = '24px Microsoft YaHei';
            representationCtx.textAlign = 'center';
            representationCtx.fillText('韦恩图', 300, 50);
            
            // 绘制韦恩图
            representationCtx.beginPath();
            representationCtx.arc(250, 200, 80, 0, 2 * Math.PI);
            representationCtx.fillStyle = 'rgba(102, 126, 234, 0.3)';
            representationCtx.fill();
            representationCtx.strokeStyle = '#667eea';
            representationCtx.lineWidth = 2;
            representationCtx.stroke();
            
            representationCtx.beginPath();
            representationCtx.arc(350, 200, 80, 0, 2 * Math.PI);
            representationCtx.fillStyle = 'rgba(118, 75, 162, 0.3)';
            representationCtx.fill();
            representationCtx.strokeStyle = '#764ba2';
            representationCtx.stroke();
            
            representationCtx.fillStyle = '#333';
            representationCtx.font = '16px Microsoft YaHei';
            representationCtx.fillText('A', 220, 150);
            representationCtx.fillText('B', 380, 150);
        }

        // 3. 描述法动画 - 增强版
        function animateDescriptionBasic() {
            descriptionCtx.clearRect(0, 0, descriptionCanvas.width, descriptionCanvas.height);

            // 标题
            descriptionCtx.fillStyle = '#333';
            descriptionCtx.font = 'bold 24px Microsoft YaHei';
            descriptionCtx.textAlign = 'center';
            descriptionCtx.fillText('描述法基本格式', 400, 40);

            // 分步骤动画展示格式
            const formatSteps = [
                { text: '{', x: 200, y: 150, color: '#ff6b6b', delay: 0 },
                { text: 'x', x: 250, y: 150, color: '#4ecdc4', delay: 500 },
                { text: '|', x: 300, y: 150, color: '#45b7d1', delay: 1000 },
                { text: 'P(x)', x: 380, y: 150, color: '#96ceb4', delay: 1500 },
                { text: '}', x: 450, y: 150, color: '#ff6b6b', delay: 2000 }
            ];

            formatSteps.forEach(step => {
                setTimeout(() => {
                    descriptionCtx.fillStyle = step.color;
                    descriptionCtx.font = 'bold 36px Microsoft YaHei';
                    descriptionCtx.textAlign = 'center';
                    descriptionCtx.fillText(step.text, step.x, step.y);

                    // 添加解释
                    descriptionCtx.fillStyle = '#666';
                    descriptionCtx.font = '16px Microsoft YaHei';
                    if (step.text === '{' || step.text === '}') {
                        descriptionCtx.fillText('集合符号', step.x, step.y + 40);
                    } else if (step.text === 'x') {
                        descriptionCtx.fillText('元素变量', step.x, step.y + 40);
                    } else if (step.text === '|') {
                        descriptionCtx.fillText('使得/满足', step.x, step.y + 40);
                    } else if (step.text === 'P(x)') {
                        descriptionCtx.fillText('条件/性质', step.x, step.y + 40);
                    }
                }, step.delay);
            });

            // 最后显示完整解释
            setTimeout(() => {
                descriptionCtx.fillStyle = '#333';
                descriptionCtx.font = '18px Microsoft YaHei';
                descriptionCtx.textAlign = 'center';
                descriptionCtx.fillText('读作："满足条件P(x)的所有x组成的集合"', 400, 250);

                // 绘制箭头指向
                drawArrow(descriptionCtx, 300, 200, 300, 230, '#667eea');
            }, 2500);
        }

        function animateNumberSet() {
            descriptionCtx.clearRect(0, 0, descriptionCanvas.width, descriptionCanvas.height);

            descriptionCtx.fillStyle = '#333';
            descriptionCtx.font = 'bold 24px Microsoft YaHei';
            descriptionCtx.textAlign = 'center';
            descriptionCtx.fillText('数字集合演示', 400, 40);

            // 演示 {x | x > 0, x ∈ N}
            setTimeout(() => {
                descriptionCtx.fillStyle = '#667eea';
                descriptionCtx.font = '20px Microsoft YaHei';
                descriptionCtx.fillText('{x | x > 0, x ∈ N}', 400, 100);
            }, 500);

            // 绘制数轴
            setTimeout(() => {
                drawNumberLine(descriptionCtx, 150, 200, 500, [-2, -1, 0, 1, 2, 3, 4, 5, 6]);
            }, 1000);

            // 高亮满足条件的数字
            setTimeout(() => {
                const validNumbers = [1, 2, 3, 4, 5, 6];
                validNumbers.forEach((num, index) => {
                    setTimeout(() => {
                        const x = 150 + (num + 2) * 50;

                        // 绘制高亮圆圈
                        descriptionCtx.beginPath();
                        descriptionCtx.arc(x, 200, 15, 0, 2 * Math.PI);
                        descriptionCtx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                        descriptionCtx.fill();
                        descriptionCtx.strokeStyle = '#667eea';
                        descriptionCtx.lineWidth = 3;
                        descriptionCtx.stroke();

                        // 添加跳跃动画
                        animateJump(x, 200, num.toString());
                    }, index * 300);
                });
            }, 2000);

            // 显示结果
            setTimeout(() => {
                descriptionCtx.fillStyle = '#44ff44';
                descriptionCtx.font = '18px Microsoft YaHei';
                descriptionCtx.textAlign = 'center';
                descriptionCtx.fillText('结果：{1, 2, 3, 4, 5, 6, ...}', 400, 300);
                descriptionCtx.fillText('(所有正整数)', 400, 330);
            }, 4000);
        }

        function animateEquationSet() {
            descriptionCtx.clearRect(0, 0, descriptionCanvas.width, descriptionCanvas.height);

            descriptionCtx.fillStyle = '#333';
            descriptionCtx.font = 'bold 24px Microsoft YaHei';
            descriptionCtx.textAlign = 'center';
            descriptionCtx.fillText('方程解集演示', 400, 40);

            // 演示 {x | x² - 5x + 6 = 0}
            const steps = [
                { text: '{x | x² - 5x + 6 = 0}', y: 100, color: '#667eea', delay: 500 },
                { text: '分解因式：(x - 2)(x - 3) = 0', y: 150, color: '#333', delay: 1500 },
                { text: '解得：x = 2 或 x = 3', y: 200, color: '#ff6b6b', delay: 2500 },
                { text: '所以集合为：{2, 3}', y: 250, color: '#44ff44', delay: 3500 }
            ];

            steps.forEach(step => {
                setTimeout(() => {
                    descriptionCtx.fillStyle = step.color;
                    descriptionCtx.font = '18px Microsoft YaHei';
                    descriptionCtx.textAlign = 'center';
                    descriptionCtx.fillText(step.text, 400, step.y);
                }, step.delay);
            });

            // 绘制抛物线
            setTimeout(() => {
                drawParabola(descriptionCtx, 100, 320, 600, 100);

                // 标记解点
                setTimeout(() => {
                    markSolutionPoints(descriptionCtx, [2, 3], 100, 320, 600, 100);
                }, 1000);
            }, 4000);
        }

        function animateGeometrySet() {
            descriptionCtx.clearRect(0, 0, descriptionCanvas.width, descriptionCanvas.height);

            descriptionCtx.fillStyle = '#333';
            descriptionCtx.font = 'bold 24px Microsoft YaHei';
            descriptionCtx.textAlign = 'center';
            descriptionCtx.fillText('几何图形演示', 400, 40);

            setTimeout(() => {
                descriptionCtx.fillStyle = '#667eea';
                descriptionCtx.font = '18px Microsoft YaHei';
                descriptionCtx.fillText('{(x,y) | x² + y² = 1}', 400, 80);
                descriptionCtx.fillText('单位圆上所有点的集合', 400, 110);
            }, 500);

            // 绘制坐标系
            setTimeout(() => {
                drawCoordinateSystem(descriptionCtx, 400, 250, 150);
            }, 1000);

            // 动画绘制单位圆
            setTimeout(() => {
                animateCircle(descriptionCtx, 400, 250, 100);
            }, 2000);

            // 显示一些点的坐标
            setTimeout(() => {
                const points = [
                    { x: 400 + 100, y: 250, label: '(1,0)' },
                    { x: 400, y: 250 - 100, label: '(0,1)' },
                    { x: 400 - 100, y: 250, label: '(-1,0)' },
                    { x: 400, y: 250 + 100, label: '(0,-1)' }
                ];

                points.forEach((point, index) => {
                    setTimeout(() => {
                        descriptionCtx.beginPath();
                        descriptionCtx.arc(point.x, point.y, 5, 0, 2 * Math.PI);
                        descriptionCtx.fillStyle = '#ff6b6b';
                        descriptionCtx.fill();

                        descriptionCtx.fillStyle = '#333';
                        descriptionCtx.font = '12px Microsoft YaHei';
                        descriptionCtx.textAlign = 'center';
                        descriptionCtx.fillText(point.label, point.x, point.y - 15);
                    }, index * 500);
                });
            }, 4000);
        }

        function resetDescriptionCanvas() {
            descriptionCtx.clearRect(0, 0, descriptionCanvas.width, descriptionCanvas.height);
            descriptionCtx.fillStyle = '#999';
            descriptionCtx.font = '18px Microsoft YaHei';
            descriptionCtx.textAlign = 'center';
            descriptionCtx.fillText('选择上方按钮开始学习描述法', 400, 250);
        }

        // 🎮 互动筛选器功能
        let currentCondition = 'greater';
        let currentParam = 3;
        let allNumbers = [-3, -2, -1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        let filteredNumbers = [];

        function startInteractiveFilter() {
            document.getElementById('interactivePanel').style.display = 'block';
            resetDescriptionCanvas();

            descriptionCtx.fillStyle = '#333';
            descriptionCtx.font = 'bold 24px Microsoft YaHei';
            descriptionCtx.textAlign = 'center';
            descriptionCtx.fillText('🎮 互动筛选器', 400, 50);

            descriptionCtx.fillStyle = '#666';
            descriptionCtx.font = '16px Microsoft YaHei';
            descriptionCtx.fillText('使用下方控制面板来体验描述法的筛选过程', 400, 80);

            // 绘制所有数字
            drawAllNumbers();
            updateFilter();
        }

        function hideInteractivePanel() {
            document.getElementById('interactivePanel').style.display = 'none';
        }

        function updateCondition() {
            currentCondition = document.getElementById('conditionType').value;
            updateParameterControls();
            updateFilter();
        }

        function updateParameterControls() {
            const paramControls = document.getElementById('parameterControls');
            const slider = document.getElementById('paramSlider');

            if (currentCondition === 'even' || currentCondition === 'odd' || currentCondition === 'square') {
                paramControls.style.display = 'none';
            } else {
                paramControls.style.display = 'block';
                if (currentCondition === 'range') {
                    slider.min = '1';
                    slider.max = '8';
                    slider.value = '5';
                } else {
                    slider.min = '0';
                    slider.max = '10';
                    slider.value = '3';
                }
            }
            updateFilter();
        }

        function updateFilter() {
            const slider = document.getElementById('paramSlider');
            const paramValue = document.getElementById('paramValue');

            if (slider.style.display !== 'none') {
                currentParam = parseInt(slider.value);
                paramValue.textContent = currentParam;
            }

            // 计算满足条件的数字
            filteredNumbers = allNumbers.filter(num => {
                switch (currentCondition) {
                    case 'greater': return num > currentParam;
                    case 'less': return num < currentParam;
                    case 'range': return num > 0 && num < currentParam;
                    case 'even': return num % 2 === 0;
                    case 'odd': return num % 2 === 1;
                    case 'square': return Math.sqrt(Math.abs(num)) % 1 === 0 && num >= 0;
                    default: return false;
                }
            });

            // 更新显示
            if (document.getElementById('interactivePanel').style.display !== 'none') {
                drawFilterPreview();
            }
        }

        function drawAllNumbers() {
            const startX = 50;
            const startY = 150;
            const spacing = 50;

            // 绘制数轴
            descriptionCtx.strokeStyle = '#ccc';
            descriptionCtx.lineWidth = 2;
            descriptionCtx.beginPath();
            descriptionCtx.moveTo(startX - 20, startY);
            descriptionCtx.lineTo(startX + allNumbers.length * spacing + 20, startY);
            descriptionCtx.stroke();

            // 绘制所有数字
            allNumbers.forEach((num, index) => {
                const x = startX + index * spacing;

                // 绘制数字圆圈
                descriptionCtx.beginPath();
                descriptionCtx.arc(x, startY, 20, 0, 2 * Math.PI);
                descriptionCtx.fillStyle = '#f0f0f0';
                descriptionCtx.fill();
                descriptionCtx.strokeStyle = '#ccc';
                descriptionCtx.lineWidth = 2;
                descriptionCtx.stroke();

                // 绘制数字
                descriptionCtx.fillStyle = '#333';
                descriptionCtx.font = '14px Microsoft YaHei';
                descriptionCtx.textAlign = 'center';
                descriptionCtx.fillText(num.toString(), x, startY + 5);
            });
        }

        function drawFilterPreview() {
            // 清除之前的高亮
            drawAllNumbers();

            const startX = 50;
            const startY = 150;
            const spacing = 50;

            // 显示当前条件
            descriptionCtx.fillStyle = '#667eea';
            descriptionCtx.font = 'bold 18px Microsoft YaHei';
            descriptionCtx.textAlign = 'center';

            let conditionText = '';
            switch (currentCondition) {
                case 'greater': conditionText = `{x | x > ${currentParam}}`; break;
                case 'less': conditionText = `{x | x < ${currentParam}}`; break;
                case 'range': conditionText = `{x | 0 < x < ${currentParam}}`; break;
                case 'even': conditionText = '{x | x = 2k, k ∈ Z}'; break;
                case 'odd': conditionText = '{x | x = 2k+1, k ∈ Z}'; break;
                case 'square': conditionText = '{x | x = n², n ∈ N}'; break;
            }
            descriptionCtx.fillText(conditionText, 400, 120);

            // 高亮满足条件的数字
            filteredNumbers.forEach(num => {
                const index = allNumbers.indexOf(num);
                if (index !== -1) {
                    const x = startX + index * spacing;

                    // 绘制高亮圆圈
                    descriptionCtx.beginPath();
                    descriptionCtx.arc(x, startY, 22, 0, 2 * Math.PI);
                    descriptionCtx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                    descriptionCtx.fill();
                    descriptionCtx.strokeStyle = '#667eea';
                    descriptionCtx.lineWidth = 3;
                    descriptionCtx.stroke();

                    // 重新绘制数字
                    descriptionCtx.fillStyle = '#667eea';
                    descriptionCtx.font = 'bold 14px Microsoft YaHei';
                    descriptionCtx.textAlign = 'center';
                    descriptionCtx.fillText(num.toString(), x, startY + 5);
                }
            });

            // 显示结果集合
            descriptionCtx.fillStyle = '#44ff44';
            descriptionCtx.font = '16px Microsoft YaHei';
            descriptionCtx.textAlign = 'center';
            const resultText = filteredNumbers.length > 0 ?
                `结果：{${filteredNumbers.join(', ')}}` :
                '结果：∅ (空集)';
            descriptionCtx.fillText(resultText, 400, 220);
        }

        function startAnimation() {
            resetDescriptionCanvas();

            descriptionCtx.fillStyle = '#333';
            descriptionCtx.font = 'bold 24px Microsoft YaHei';
            descriptionCtx.textAlign = 'center';
            descriptionCtx.fillText('🎬 动画筛选过程', 400, 50);

            // 显示条件
            let conditionText = '';
            switch (currentCondition) {
                case 'greater': conditionText = `{x | x > ${currentParam}}`; break;
                case 'less': conditionText = `{x | x < ${currentParam}}`; break;
                case 'range': conditionText = `{x | 0 < x < ${currentParam}}`; break;
                case 'even': conditionText = '{x | x = 2k, k ∈ Z}'; break;
                case 'odd': conditionText = '{x | x = 2k+1, k ∈ Z}'; break;
                case 'square': conditionText = '{x | x = n², n ∈ N}'; break;
            }

            descriptionCtx.fillStyle = '#667eea';
            descriptionCtx.font = 'bold 20px Microsoft YaHei';
            descriptionCtx.fillText(conditionText, 400, 90);

            // 绘制初始数字
            setTimeout(() => {
                drawAllNumbers();

                descriptionCtx.fillStyle = '#666';
                descriptionCtx.font = '16px Microsoft YaHei';
                descriptionCtx.textAlign = 'center';
                descriptionCtx.fillText('正在检查每个数字是否满足条件...', 400, 200);
            }, 500);

            // 逐个检查数字
            allNumbers.forEach((num, index) => {
                setTimeout(() => {
                    const startX = 50;
                    const startY = 150;
                    const spacing = 50;
                    const x = startX + index * spacing;

                    // 高亮当前检查的数字
                    descriptionCtx.beginPath();
                    descriptionCtx.arc(x, startY, 25, 0, 2 * Math.PI);
                    descriptionCtx.strokeStyle = '#ffaa00';
                    descriptionCtx.lineWidth = 4;
                    descriptionCtx.stroke();

                    // 检查是否满足条件
                    const satisfies = filteredNumbers.includes(num);

                    setTimeout(() => {
                        if (satisfies) {
                            // 满足条件 - 绿色高亮
                            descriptionCtx.beginPath();
                            descriptionCtx.arc(x, startY, 22, 0, 2 * Math.PI);
                            descriptionCtx.fillStyle = 'rgba(68, 255, 68, 0.4)';
                            descriptionCtx.fill();
                            descriptionCtx.strokeStyle = '#44ff44';
                            descriptionCtx.lineWidth = 3;
                            descriptionCtx.stroke();

                            // 添加跳跃动画
                            animateNumberJump(x, startY, num, '#44ff44');
                        } else {
                            // 不满足条件 - 灰色
                            descriptionCtx.beginPath();
                            descriptionCtx.arc(x, startY, 20, 0, 2 * Math.PI);
                            descriptionCtx.fillStyle = '#f0f0f0';
                            descriptionCtx.fill();
                            descriptionCtx.strokeStyle = '#ccc';
                            descriptionCtx.lineWidth = 2;
                            descriptionCtx.stroke();

                            descriptionCtx.fillStyle = '#999';
                            descriptionCtx.font = '14px Microsoft YaHei';
                            descriptionCtx.textAlign = 'center';
                            descriptionCtx.fillText(num.toString(), x, startY + 5);
                        }
                    }, 300);

                }, (index + 1) * 600);
            });

            // 显示最终结果
            setTimeout(() => {
                descriptionCtx.fillStyle = '#333';
                descriptionCtx.font = 'bold 18px Microsoft YaHei';
                descriptionCtx.textAlign = 'center';
                descriptionCtx.fillText('筛选完成！', 400, 250);

                const resultText = filteredNumbers.length > 0 ?
                    `最终集合：{${filteredNumbers.join(', ')}}` :
                    '最终集合：∅ (空集)';

                descriptionCtx.fillStyle = '#667eea';
                descriptionCtx.font = 'bold 16px Microsoft YaHei';
                descriptionCtx.fillText(resultText, 400, 280);

                // 添加庆祝动画
                if (filteredNumbers.length > 0) {
                    setTimeout(() => {
                        drawCelebrationEffect();
                    }, 500);
                }
            }, (allNumbers.length + 1) * 600 + 1000);
        }

        function animateNumberJump(x, y, number, color) {
            let jumpHeight = 0;
            let direction = 1;
            const maxJump = 30;

            const jumpInterval = setInterval(() => {
                // 清除该区域
                descriptionCtx.clearRect(x - 30, y - 50, 60, 80);

                // 重新绘制背景数字圆圈
                descriptionCtx.beginPath();
                descriptionCtx.arc(x, y, 22, 0, 2 * Math.PI);
                descriptionCtx.fillStyle = 'rgba(68, 255, 68, 0.4)';
                descriptionCtx.fill();
                descriptionCtx.strokeStyle = color;
                descriptionCtx.lineWidth = 3;
                descriptionCtx.stroke();

                // 绘制跳跃的数字
                descriptionCtx.fillStyle = color;
                descriptionCtx.font = 'bold 14px Microsoft YaHei';
                descriptionCtx.textAlign = 'center';
                descriptionCtx.fillText(number.toString(), x, y - jumpHeight + 5);

                jumpHeight += direction * 3;
                if (jumpHeight >= maxJump || jumpHeight <= 0) {
                    direction *= -1;
                }

                if (jumpHeight <= 0 && direction === 1) {
                    clearInterval(jumpInterval);
                    // 最终绘制
                    descriptionCtx.fillText(number.toString(), x, y + 5);
                }
            }, 50);
        }

        function drawCelebrationEffect() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];

            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const x = 200 + Math.random() * 400;
                    const y = 100 + Math.random() * 200;
                    const color = colors[Math.floor(Math.random() * colors.length)];

                    descriptionCtx.beginPath();
                    descriptionCtx.arc(x, y, 5, 0, 2 * Math.PI);
                    descriptionCtx.fillStyle = color;
                    descriptionCtx.fill();

                    // 粒子消失动画
                    setTimeout(() => {
                        descriptionCtx.clearRect(x - 6, y - 6, 12, 12);
                    }, 1000);
                }, i * 100);
            }
        }

        // 辅助绘图函数
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function drawNumberLine(ctx, startX, y, width, numbers) {
            // 绘制数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, y);
            ctx.lineTo(startX + width, y);
            ctx.stroke();

            // 绘制刻度和数字
            numbers.forEach((num, index) => {
                const x = startX + (index * width / (numbers.length - 1));

                // 刻度线
                ctx.beginPath();
                ctx.moveTo(x, y - 10);
                ctx.lineTo(x, y + 10);
                ctx.stroke();

                // 数字
                ctx.fillStyle = '#333';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(num.toString(), x, y + 30);
            });
        }

        function animateJump(x, y, text) {
            let jumpHeight = 0;
            let direction = 1;
            const maxHeight = 30;

            const jumpInterval = setInterval(() => {
                descriptionCtx.clearRect(x - 20, y - maxHeight - 20, 40, maxHeight + 40);

                jumpHeight += direction * 3;
                if (jumpHeight >= maxHeight || jumpHeight <= 0) {
                    direction *= -1;
                }

                if (jumpHeight <= 0 && direction === 1) {
                    clearInterval(jumpInterval);
                    jumpHeight = 0;
                }

                descriptionCtx.fillStyle = '#ff6b6b';
                descriptionCtx.font = 'bold 16px Microsoft YaHei';
                descriptionCtx.textAlign = 'center';
                descriptionCtx.fillText(text, x, y - jumpHeight - 5);
            }, 50);
        }

        function drawParabola(ctx, startX, startY, width, height) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let i = 0; i <= width; i += 2) {
                const x = startX + i;
                const normalizedX = (i / width) * 6 - 3; // -3 to 3
                const y = startY - (normalizedX * normalizedX - 5 * normalizedX + 6) * height / 10;

                if (i === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            ctx.stroke();
        }

        function markSolutionPoints(ctx, solutions, startX, startY, width, height) {
            solutions.forEach(sol => {
                const x = startX + ((sol + 3) / 6) * width;
                const y = startY;

                ctx.beginPath();
                ctx.arc(x, y, 8, 0, 2 * Math.PI);
                ctx.fillStyle = '#ff6b6b';
                ctx.fill();

                ctx.fillStyle = '#333';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`x=${sol}`, x, y + 25);
            });
        }

        function drawCoordinateSystem(ctx, centerX, centerY, size) {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;

            // X轴
            ctx.beginPath();
            ctx.moveTo(centerX - size, centerY);
            ctx.lineTo(centerX + size, centerY);
            ctx.stroke();

            // Y轴
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - size);
            ctx.lineTo(centerX, centerY + size);
            ctx.stroke();

            // 原点
            ctx.fillStyle = '#333';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('O', centerX - 15, centerY + 15);
        }

        function animateCircle(ctx, centerX, centerY, radius) {
            let angle = 0;
            const step = Math.PI / 30;

            const interval = setInterval(() => {
                if (angle <= 2 * Math.PI) {
                    const x = centerX + radius * Math.cos(angle);
                    const y = centerY + radius * Math.sin(angle);

                    ctx.beginPath();
                    ctx.arc(x, y, 2, 0, 2 * Math.PI);
                    ctx.fillStyle = '#667eea';
                    ctx.fill();

                    angle += step;
                } else {
                    clearInterval(interval);
                }
            }, 50);
        }

        // 4. 互异性演示
        function demonstrateUniqueness() {
            uniquenessCtx.clearRect(0, 0, uniquenessCanvas.width, uniquenessCanvas.height);
            
            uniquenessCtx.fillStyle = '#333';
            uniquenessCtx.font = '20px Microsoft YaHei';
            uniquenessCtx.textAlign = 'center';
            uniquenessCtx.fillText('元素的互异性', 300, 50);
            
            // 错误示例
            uniquenessCtx.fillStyle = '#ff4444';
            uniquenessCtx.font = '18px Microsoft YaHei';
            uniquenessCtx.fillText('❌ {1, 2, 2, 3} 不是集合', 300, 120);
            
            // 正确示例
            uniquenessCtx.fillStyle = '#44ff44';
            uniquenessCtx.fillText('✓ {1, 2, 3} 是集合', 300, 180);
            
            uniquenessCtx.fillStyle = '#666';
            uniquenessCtx.font = '16px Microsoft YaHei';
            uniquenessCtx.fillText('集合中的每个元素都是唯一的', 300, 250);
        }

        function showDuplicateExample() {
            uniquenessCtx.clearRect(0, 0, uniquenessCanvas.width, uniquenessCanvas.height);
            
            // 动画演示重复元素被移除
            const elements = [1, 2, 2, 3, 3, 3, 4];
            const finalElements = [1, 2, 3, 4];
            
            uniquenessCtx.fillStyle = '#333';
            uniquenessCtx.font = '18px Microsoft YaHei';
            uniquenessCtx.textAlign = 'center';
            uniquenessCtx.fillText('原始：[1, 2, 2, 3, 3, 3, 4]', 300, 100);
            
            setTimeout(() => {
                uniquenessCtx.fillStyle = '#667eea';
                uniquenessCtx.fillText('去重后：{1, 2, 3, 4}', 300, 200);
            }, 1500);
        }

        // 5. 含参方程演示
        function solveParametricEquation() {
            equationsCtx.clearRect(0, 0, equationsCanvas.width, equationsCanvas.height);
            
            equationsCtx.fillStyle = '#333';
            equationsCtx.font = '20px Microsoft YaHei';
            equationsCtx.textAlign = 'center';
            equationsCtx.fillText('含参方程：x² - (a+1)x + a = 0', 300, 80);
            
            equationsCtx.font = '16px Microsoft YaHei';
            equationsCtx.fillText('解得：x₁ = 1, x₂ = a', 300, 140);
            
            equationsCtx.fillText('当 a ≠ 1 时，解集为 {1, a}', 300, 180);
            equationsCtx.fillText('当 a = 1 时，解集为 {1}', 300, 220);
            
            equationsCtx.fillStyle = '#667eea';
            equationsCtx.fillText('互异性要求：a ≠ 1', 300, 280);
        }

        function analyzeUniqueness() {
            equationsCtx.clearRect(0, 0, equationsCanvas.width, equationsCanvas.height);
            
            equationsCtx.fillStyle = '#333';
            equationsCtx.font = '18px Microsoft YaHei';
            equationsCtx.textAlign = 'center';
            
            const analysis = [
                '分析互异性：',
                '若 x₁ = x₂，则 1 = a',
                '所以当 a = 1 时，两根相等',
                '此时解集只有一个元素 {1}',
                '当 a ≠ 1 时，解集有两个元素 {1, a}'
            ];
            
            analysis.forEach((text, index) => {
                setTimeout(() => {
                    equationsCtx.fillText(text, 300, 80 + index * 40);
                }, index * 800);
            });
        }

        // 6. 二次方程分析
        function analyzeQuadratic() {
            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);
            
            practiceCtx.fillStyle = '#333';
            practiceCtx.font = '20px Microsoft YaHei';
            practiceCtx.textAlign = 'center';
            practiceCtx.fillText('二次方程：ax² + bx + c = 0', 300, 60);
            
            practiceCtx.font = '16px Microsoft YaHei';
            practiceCtx.fillText('判别式：Δ = b² - 4ac', 300, 120);
            
            practiceCtx.fillStyle = '#44ff44';
            practiceCtx.fillText('Δ > 0：两个不等实根', 300, 170);
            
            practiceCtx.fillStyle = '#ffaa00';
            practiceCtx.fillText('Δ = 0：一个重根', 300, 210);
            
            practiceCtx.fillStyle = '#ff4444';
            practiceCtx.fillText('Δ < 0：无实根', 300, 250);
        }

        function showDiscriminant() {
            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);
            
            // 绘制抛物线示意图
            practiceCtx.strokeStyle = '#667eea';
            practiceCtx.lineWidth = 3;
            
            // Δ > 0 的情况
            practiceCtx.beginPath();
            for (let x = 50; x <= 150; x++) {
                const y = 0.01 * (x - 100) * (x - 100) + 150;
                if (x === 50) practiceCtx.moveTo(x, y);
                else practiceCtx.lineTo(x, y);
            }
            practiceCtx.stroke();
            
            practiceCtx.fillStyle = '#333';
            practiceCtx.font = '14px Microsoft YaHei';
            practiceCtx.textAlign = 'center';
            practiceCtx.fillText('Δ > 0', 100, 300);
            practiceCtx.fillText('两个交点', 100, 320);
        }

        function interactiveQuiz() {
            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);
            
            practiceCtx.fillStyle = '#333';
            practiceCtx.font = '18px Microsoft YaHei';
            practiceCtx.textAlign = 'center';
            practiceCtx.fillText('互动练习', 300, 60);
            
            practiceCtx.font = '16px Microsoft YaHei';
            practiceCtx.fillText('方程 x² - 4x + k = 0 的解集只有一个元素', 300, 120);
            practiceCtx.fillText('求 k 的值', 300, 160);
            
            practiceCtx.fillStyle = '#667eea';
            practiceCtx.fillText('提示：考虑判别式 Δ = 0', 300, 220);
            
            setTimeout(() => {
                practiceCtx.fillStyle = '#44ff44';
                practiceCtx.fillText('答案：k = 4', 300, 280);
            }, 3000);
        }

        // 初始化画布
        function initCanvases() {
            resetConcept();
            
            representationCtx.fillStyle = '#999';
            representationCtx.font = '18px Microsoft YaHei';
            representationCtx.textAlign = 'center';
            representationCtx.fillText('点击按钮查看不同的表示方法', 300, 200);
            
            resetDescriptionCanvas();
            
            uniquenessCtx.fillStyle = '#999';
            uniquenessCtx.font = '18px Microsoft YaHei';
            uniquenessCtx.textAlign = 'center';
            uniquenessCtx.fillText('点击按钮了解互异性', 300, 200);
            
            equationsCtx.fillStyle = '#999';
            equationsCtx.font = '18px Microsoft YaHei';
            equationsCtx.textAlign = 'center';
            equationsCtx.fillText('点击按钮学习含参方程', 300, 200);
            
            practiceCtx.fillStyle = '#999';
            practiceCtx.font = '18px Microsoft YaHei';
            practiceCtx.textAlign = 'center';
            practiceCtx.fillText('点击按钮开始练习', 300, 200);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', initCanvases);

        // 平滑滚动到指定部分
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({ behavior: 'smooth' });
            });
        });
    </script>
</body>
</html>
