<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UML的四种事物 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
        }

        .subtitle {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .game-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out 1s forwards;
        }

        .question-section {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 40px;
            border-left: 5px solid #667eea;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 20px;
        }

        .blank {
            display: inline-block;
            min-width: 120px;
            height: 40px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            margin: 0 5px;
            position: relative;
            vertical-align: middle;
            background: white;
            transition: all 0.3s ease;
        }

        .blank.filled {
            background: #667eea;
            color: white;
            border: 2px solid #667eea;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .things-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .thing-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(20px);
            opacity: 0;
            animation: cardSlideIn 0.8s ease-out forwards;
        }

        .thing-card:nth-child(1) { animation-delay: 1.2s; }
        .thing-card:nth-child(2) { animation-delay: 1.4s; }
        .thing-card:nth-child(3) { animation-delay: 1.6s; }
        .thing-card:nth-child(4) { animation-delay: 1.8s; }

        .thing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .thing-card.selected {
            background: #667eea;
            color: white;
            transform: scale(1.05);
        }

        .thing-card.correct {
            background: #4CAF50;
            color: white;
            animation: bounce 0.6s ease;
        }

        .thing-card.wrong {
            background: #f44336;
            color: white;
            animation: shake 0.6s ease;
        }

        .thing-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .thing-name {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .thing-desc {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        #animationCanvas {
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            background: white;
        }

        .explanation {
            background: #e8f2ff;
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .explanation h3 {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .explanation-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .explanation-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .explanation-item h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .explanation-item p {
            color: #666;
            line-height: 1.6;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes cardSlideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .reset-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 20px auto;
            display: block;
            transition: all 0.3s ease;
        }

        .reset-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎯 UML的四种事物</h1>
        <p class="subtitle">通过互动游戏学习UML建模的基础概念</p>

        <div class="game-area">
            <div class="question-section">
                <div class="question-text">
                    UML的事物是对模型中最具有代表性的成分的抽象，<span class="blank" id="blank1"></span>是模型的静态部分，描述概念或物理元素；<span class="blank" id="blank2"></span>用来描述、说明和标注模型的任何元素。
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>

            <div class="things-container">
                <div class="thing-card" data-type="structural">
                    <span class="thing-icon">🏗️</span>
                    <div class="thing-name">结构事物</div>
                    <div class="thing-desc">模型的静态部分，描述概念或物理元素</div>
                </div>
                
                <div class="thing-card" data-type="behavioral">
                    <span class="thing-icon">⚡</span>
                    <div class="thing-name">行为事物</div>
                    <div class="thing-desc">模型的动态部分，描述跨越时间和空间的行为</div>
                </div>
                
                <div class="thing-card" data-type="grouping">
                    <span class="thing-icon">📦</span>
                    <div class="thing-name">分组事物</div>
                    <div class="thing-desc">模型的组织部分，分解成的盒子</div>
                </div>
                
                <div class="thing-card" data-type="annotation">
                    <span class="thing-icon">📝</span>
                    <div class="thing-name">注释事物</div>
                    <div class="thing-desc">模型的解释部分，用来描述和标注</div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>

            <button class="reset-btn" onclick="resetGame()">🔄 重新开始</button>
        </div>

        <div class="explanation" id="explanation">
            <h3>🎓 知识详解</h3>
            <div class="explanation-content">
                <div class="explanation-item">
                    <h4>🏗️ 结构事物</h4>
                    <p>UML模型中的"名词"，是静态部分。包括类、接口、协作、用例、活动类、组件和节点等。它们描述了系统的概念或物理元素。</p>
                </div>
                <div class="explanation-item">
                    <h4>⚡ 行为事物</h4>
                    <p>UML模型中的"动词"，是动态部分。包括交互、状态机和活动等。它们描述了跨越时间和空间的行为。</p>
                </div>
                <div class="explanation-item">
                    <h4>📦 分组事物</h4>
                    <p>UML模型的组织部分，主要是包(Package)。它们是一些由模型分解成的"盒子"，用于组织模型元素。</p>
                </div>
                <div class="explanation-item">
                    <h4>📝 注释事物</h4>
                    <p>UML模型的解释部分，主要是注解(Note)。用来描述、说明和标注模型的任何元素，提供额外的信息。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let selectedCards = [];
        const correctAnswers = ['structural', 'annotation'];

        // 获取画布和上下文
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');

        // 动画变量
        let animationFrame = 0;
        let particles = [];

        // 初始化游戏
        function initGame() {
            setupEventListeners();
            startAnimation();
            showExplanation();
        }

        // 设置事件监听器
        function setupEventListeners() {
            const cards = document.querySelectorAll('.thing-card');
            cards.forEach(card => {
                card.addEventListener('click', handleCardClick);
            });
        }

        // 处理卡片点击
        function handleCardClick(event) {
            const card = event.currentTarget;
            const type = card.dataset.type;

            if (currentStep >= 2) return; // 游戏已完成

            // 移除之前的状态
            card.classList.remove('correct', 'wrong');

            // 检查答案
            if (type === correctAnswers[currentStep]) {
                card.classList.add('correct');
                fillBlank(currentStep, card.querySelector('.thing-name').textContent);
                selectedCards.push(card);
                currentStep++;
                updateProgress();

                // 创建成功粒子效果
                createParticles(card.getBoundingClientRect());

                if (currentStep === 2) {
                    setTimeout(() => {
                        showCompletionAnimation();
                    }, 1000);
                }
            } else {
                card.classList.add('wrong');
                setTimeout(() => {
                    card.classList.remove('wrong');
                }, 600);
            }
        }

        // 填充空白
        function fillBlank(step, text) {
            const blank = document.getElementById(`blank${step + 1}`);
            blank.textContent = text;
            blank.classList.add('filled');
        }

        // 更新进度条
        function updateProgress() {
            const progressFill = document.getElementById('progressFill');
            const progress = (currentStep / 2) * 100;
            progressFill.style.width = progress + '%';
        }

        // 创建粒子效果
        function createParticles(rect) {
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            for (let i = 0; i < 15; i++) {
                particles.push({
                    x: centerX,
                    y: centerY,
                    vx: (Math.random() - 0.5) * 10,
                    vy: (Math.random() - 0.5) * 10,
                    life: 60,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`
                });
            }
        }

        // 开始动画
        function startAnimation() {
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制背景
                drawBackground();

                // 绘制UML图示
                drawUMLDiagram();

                // 绘制粒子
                drawParticles();

                animationFrame++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 绘制背景
        function drawBackground() {
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9ff');
            gradient.addColorStop(1, '#e8f2ff');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制UML图示
        function drawUMLDiagram() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = 80;

            // 绘制中心圆
            ctx.beginPath();
            ctx.arc(centerX, centerY, 60, 0, Math.PI * 2);
            ctx.fillStyle = '#667eea';
            ctx.fill();
            ctx.strokeStyle = '#5a6fd8';
            ctx.lineWidth = 3;
            ctx.stroke();

            // 绘制中心文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('UML', centerX, centerY - 5);
            ctx.fillText('事物', centerX, centerY + 15);

            // 绘制四个分支
            const branches = [
                { angle: 0, icon: '🏗️', name: '结构', color: '#4CAF50' },
                { angle: Math.PI / 2, icon: '⚡', name: '行为', color: '#FF9800' },
                { angle: Math.PI, icon: '📦', name: '分组', color: '#9C27B0' },
                { angle: Math.PI * 1.5, icon: '📝', name: '注释', color: '#F44336' }
            ];

            branches.forEach((branch, index) => {
                const x = centerX + Math.cos(branch.angle) * 150;
                const y = centerY + Math.sin(branch.angle) * 150;

                // 绘制连接线
                ctx.beginPath();
                ctx.moveTo(centerX + Math.cos(branch.angle) * 60, centerY + Math.sin(branch.angle) * 60);
                ctx.lineTo(x - Math.cos(branch.angle) * 40, y - Math.sin(branch.angle) * 40);
                ctx.strokeStyle = branch.color;
                ctx.lineWidth = 3;
                ctx.stroke();

                // 绘制分支圆
                ctx.beginPath();
                ctx.arc(x, y, 40, 0, Math.PI * 2);
                ctx.fillStyle = branch.color;
                ctx.fill();
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();

                // 绘制图标和文字
                ctx.fillStyle = 'white';
                ctx.font = '20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(branch.icon, x, y - 5);
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(branch.name, x, y + 15);

                // 添加脉动效果
                if (currentStep < 2 && index < 2) {
                    const pulse = Math.sin(animationFrame * 0.1) * 5;
                    ctx.beginPath();
                    ctx.arc(x, y, 40 + pulse, 0, Math.PI * 2);
                    ctx.strokeStyle = branch.color;
                    ctx.lineWidth = 2;
                    ctx.globalAlpha = 0.5;
                    ctx.stroke();
                    ctx.globalAlpha = 1;
                }
            });
        }

        // 绘制粒子
        function drawParticles() {
            particles = particles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;

                ctx.globalAlpha = particle.life / 60;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x - canvas.getBoundingClientRect().left,
                       particle.y - canvas.getBoundingClientRect().top, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;

                return particle.life > 0;
            });
        }

        // 显示完成动画
        function showCompletionAnimation() {
            // 创建庆祝粒子
            for (let i = 0; i < 50; i++) {
                particles.push({
                    x: canvas.width / 2,
                    y: canvas.height / 2,
                    vx: (Math.random() - 0.5) * 20,
                    vy: (Math.random() - 0.5) * 20,
                    life: 120,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`
                });
            }

            // 显示完成消息
            setTimeout(() => {
                alert('🎉 恭喜！您已经掌握了UML的四种事物！\n\n正确答案：\n第一空：结构事物\n第二空：注释事物');
            }, 500);
        }

        // 显示详细解释
        function showExplanation() {
            const explanation = document.getElementById('explanation');
            setTimeout(() => {
                explanation.classList.add('show');
            }, 2000);
        }

        // 重置游戏
        function resetGame() {
            currentStep = 0;
            selectedCards = [];

            // 重置空白
            document.querySelectorAll('.blank').forEach(blank => {
                blank.textContent = '';
                blank.classList.remove('filled');
            });

            // 重置卡片
            document.querySelectorAll('.thing-card').forEach(card => {
                card.classList.remove('selected', 'correct', 'wrong');
            });

            // 重置进度条
            document.getElementById('progressFill').style.width = '0%';

            // 清除粒子
            particles = [];
        }

        // 初始化游戏
        window.addEventListener('load', initGame);
    </script>
</body>
</html>
