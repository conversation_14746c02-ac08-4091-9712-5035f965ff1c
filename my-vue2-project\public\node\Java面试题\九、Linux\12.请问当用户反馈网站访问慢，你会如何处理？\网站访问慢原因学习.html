<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站访问慢的原因 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.6s ease;
        }

        .section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            animation: pulse 2s infinite;
        }

        .bandwidth .icon { background: #ff6b6b; }
        .server .icon { background: #4ecdc4; }
        .database .icon { background: #45b7d1; }
        .code .icon { background: #96ceb4; }

        .demo-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: white;
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .game-score {
            text-align: center;
            font-size: 1.5rem;
            color: #333;
            margin: 20px 0;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .floating-element {
            position: absolute;
            animation: bounce 2s infinite;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.9rem;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .interactive-element {
            transition: all 0.3s ease;
        }

        .interactive-element:hover {
            transform: scale(1.05);
            filter: brightness(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 网站访问慢的原因</h1>
            <p>通过有趣的动画和交互游戏，轻松掌握网站性能优化知识</p>
        </div>

        <!-- 第一部分：带宽问题 -->
        <div class="section bandwidth">
            <div class="section-title">
                <div class="icon">🌊</div>
                <h2>1. 服务器出口带宽不够用</h2>
            </div>
            
            <div class="explanation">
                <strong>什么是带宽？</strong><br>
                带宽就像水管的粗细，决定了数据传输的速度。带宽越小，同时访问的用户越多，每个人分到的"水流"就越少。
            </div>

            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="bandwidthCanvas" width="600" height="300"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="addUser()">添加用户</button>
                    <button class="btn" onclick="removeUser()">减少用户</button>
                    <button class="btn" onclick="increaseBandwidth()">增加带宽</button>
                    <button class="btn" onclick="resetBandwidth()">重置演示</button>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="bandwidthProgress"></div>
                </div>
                <div class="game-score">
                    当前用户数: <span id="userCount">1</span> | 
                    带宽利用率: <span id="bandwidthUsage">20%</span>
                </div>
            </div>
        </div>

        <!-- 第二部分：服务器负载 -->
        <div class="section server">
            <div class="section-title">
                <div class="icon">🖥️</div>
                <h2>2. 服务器负载过大</h2>
            </div>
            
            <div class="explanation">
                <strong>服务器就像一个忙碌的厨师</strong><br>
                当订单（用户请求）太多时，厨师就会忙不过来，导致每道菜（网页）的制作时间变长。
            </div>

            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="serverCanvas" width="600" height="300"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="addRequest()">增加请求</button>
                    <button class="btn" onclick="addCPU()">升级CPU</button>
                    <button class="btn" onclick="addMemory()">增加内存</button>
                    <button class="btn" onclick="resetServer()">重置服务器</button>
                </div>
                <div class="game-score">
                    CPU使用率: <span id="cpuUsage">30%</span> | 
                    内存使用率: <span id="memoryUsage">25%</span> | 
                    处理速度: <span id="processSpeed">正常</span>
                </div>
            </div>
        </div>

        <!-- 第三部分：数据库瓶颈 -->
        <div class="section database">
            <div class="section-title">
                <div class="icon">🗄️</div>
                <h2>3. 数据库瓶颈</h2>
            </div>

            <div class="explanation">
                <strong>数据库就像图书馆</strong><br>
                如果查找书籍的方法不对（慢查询），或者图书管理员太少，就会让读者等很久才能找到想要的书。
            </div>

            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="databaseCanvas" width="600" height="300"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="addQuery()">增加查询</button>
                    <button class="btn" onclick="optimizeSQL()">优化SQL</button>
                    <button class="btn" onclick="addCache()">添加缓存</button>
                    <button class="btn" onclick="addSlave()">添加从库</button>
                    <button class="btn" onclick="resetDatabase()">重置数据库</button>
                </div>
                <div class="game-score">
                    查询数量: <span id="queryCount">3</span> |
                    查询速度: <span id="querySpeed">正常</span> |
                    缓存命中率: <span id="cacheHit">0%</span>
                </div>
            </div>
        </div>

        <!-- 第四部分：代码优化 -->
        <div class="section code">
            <div class="section-title">
                <div class="icon">💻</div>
                <h2>4. 网站开发代码没有优化好</h2>
            </div>

            <div class="explanation">
                <strong>代码就像做菜的步骤</strong><br>
                如果步骤繁琐、重复，或者用了低效的方法，就会让整个过程变得很慢。好的代码就像高效的菜谱。
            </div>

            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="codeCanvas" width="600" height="300"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="addCodeComplexity()">增加复杂度</button>
                    <button class="btn" onclick="optimizeCode()">优化代码</button>
                    <button class="btn" onclick="addCompression()">启用压缩</button>
                    <button class="btn" onclick="addCDN()">使用CDN</button>
                    <button class="btn" onclick="resetCode()">重置代码</button>
                </div>
                <div class="game-score">
                    代码复杂度: <span id="codeComplexity">中等</span> |
                    执行时间: <span id="executionTime">500ms</span> |
                    优化程度: <span id="optimizationLevel">未优化</span>
                </div>
            </div>
        </div>

        <!-- 综合测试游戏 -->
        <div class="section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div class="section-title" style="color: white;">
                <div class="icon" style="background: rgba(255,255,255,0.2);">🎮</div>
                <h2>综合优化挑战</h2>
            </div>

            <div class="explanation" style="background: rgba(255,255,255,0.1); border-left-color: white; color: white;">
                <strong>现在来测试你的学习成果！</strong><br>
                你是一名网站优化专家，需要诊断并解决网站访问慢的问题。点击下面的按钮开始挑战！
            </div>

            <div class="demo-area" style="background: rgba(255,255,255,0.1);">
                <div class="canvas-container">
                    <canvas id="gameCanvas" width="600" height="300"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="startChallenge()" style="background: #ff6b6b;">开始挑战</button>
                    <button class="btn" onclick="diagnose('bandwidth')" id="diagnoseBandwidth" disabled>检查带宽</button>
                    <button class="btn" onclick="diagnose('server')" id="diagnoseServer" disabled>检查服务器</button>
                    <button class="btn" onclick="diagnose('database')" id="diagnoseDatabase" disabled>检查数据库</button>
                    <button class="btn" onclick="diagnose('code')" id="diagnoseCode" disabled>检查代码</button>
                </div>
                <div class="game-score" style="color: white;">
                    网站响应时间: <span id="responseTime">5000ms</span> |
                    用户满意度: <span id="userSatisfaction">😞 20%</span> |
                    得分: <span id="gameScore">0</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let users = 1;
        let bandwidth = 100;
        let requests = 5;
        let cpuPower = 100;
        let memory = 100;
        let queries = 3;
        let sqlOptimized = false;
        let hasCache = false;
        let hasSlaveDB = false;
        let codeComplexity = 50;
        let isCompressed = false;
        let hasCDN = false;
        let gameActive = false;
        let gameScore = 0;
        let problemType = '';
        let animationId;

        // 页面滚动动画
        function checkScroll() {
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top < window.innerHeight * 0.8) {
                    section.classList.add('visible');
                }
            });
        }

        window.addEventListener('scroll', checkScroll);
        window.addEventListener('load', checkScroll);

        // 带宽演示动画
        function drawBandwidthDemo() {
            const canvas = document.getElementById('bandwidthCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制服务器
            ctx.fillStyle = '#4ecdc4';
            ctx.fillRect(50, 100, 80, 100);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('服务器', 90, 155);
            
            // 绘制带宽管道
            const pipeWidth = Math.max(20, bandwidth / 5);
            ctx.fillStyle = '#667eea';
            ctx.fillRect(150, 140, 300, pipeWidth);
            
            // 绘制用户
            for (let i = 0; i < users; i++) {
                const x = 480 + (i % 3) * 40;
                const y = 80 + Math.floor(i / 3) * 60;
                
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(x, y, 15, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('👤', x, y + 4);
                
                // 绘制数据流
                const speed = Math.max(1, bandwidth / users / 10);
                const flowX = 150 + (Date.now() / (100 / speed)) % 300;
                ctx.fillStyle = '#ffd93d';
                ctx.beginPath();
                ctx.arc(flowX, 150 + pipeWidth/2, 3, 0, 2 * Math.PI);
                ctx.fill();
            }
            
            // 更新进度条和数据
            const usage = Math.min(100, (users * 20));
            document.getElementById('bandwidthProgress').style.width = usage + '%';
            document.getElementById('userCount').textContent = users;
            document.getElementById('bandwidthUsage').textContent = usage + '%';
            
            requestAnimationFrame(drawBandwidthDemo);
        }

        // 服务器负载演示
        function drawServerDemo() {
            const canvas = document.getElementById('serverCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制服务器机箱
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(250, 50, 100, 200);
            
            // CPU使用率显示
            const cpuUsage = Math.min(100, (requests * 100) / cpuPower);
            ctx.fillStyle = cpuUsage > 80 ? '#e74c3c' : cpuUsage > 60 ? '#f39c12' : '#27ae60';
            ctx.fillRect(260, 60, 80, cpuUsage * 0.8);
            
            // 内存使用率显示
            const memUsage = Math.min(100, (requests * 80) / memory);
            ctx.fillStyle = memUsage > 80 ? '#e74c3c' : memUsage > 60 ? '#f39c12' : '#3498db';
            ctx.fillRect(260, 160, 80, memUsage * 0.8);
            
            // 绘制请求队列
            for (let i = 0; i < Math.min(requests, 10); i++) {
                ctx.fillStyle = '#e67e22';
                ctx.fillRect(100 + i * 25, 100 + Math.sin(Date.now() / 200 + i) * 10, 20, 20);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('📄', 110 + i * 25, 115);
            }
            
            // 更新状态显示
            document.getElementById('cpuUsage').textContent = Math.round(cpuUsage) + '%';
            document.getElementById('memoryUsage').textContent = Math.round(memUsage) + '%';
            
            const speed = cpuUsage > 90 ? '很慢' : cpuUsage > 70 ? '较慢' : cpuUsage > 50 ? '正常' : '很快';
            document.getElementById('processSpeed').textContent = speed;
            
            requestAnimationFrame(drawServerDemo);
        }

        // 带宽控制函数
        function addUser() {
            if (users < 15) users++;
        }

        function removeUser() {
            if (users > 1) users--;
        }

        function increaseBandwidth() {
            bandwidth = Math.min(500, bandwidth + 50);
        }

        function resetBandwidth() {
            users = 1;
            bandwidth = 100;
        }

        // 服务器控制函数
        function addRequest() {
            requests = Math.min(20, requests + 2);
        }

        function addCPU() {
            cpuPower = Math.min(300, cpuPower + 50);
        }

        function addMemory() {
            memory = Math.min(300, memory + 50);
        }

        function resetServer() {
            requests = 5;
            cpuPower = 100;
            memory = 100;
        }

        // 数据库控制函数
        function addQuery() {
            queries = Math.min(15, queries + 2);
        }

        function optimizeSQL() {
            sqlOptimized = true;
        }

        function addCache() {
            hasCache = true;
        }

        function addSlave() {
            hasSlaveDB = true;
        }

        function resetDatabase() {
            queries = 3;
            sqlOptimized = false;
            hasCache = false;
            hasSlaveDB = false;
        }

        // 代码优化控制函数
        function addCodeComplexity() {
            codeComplexity = Math.min(100, codeComplexity + 20);
        }

        function optimizeCode() {
            codeComplexity = Math.max(20, codeComplexity - 30);
        }

        function addCompression() {
            isCompressed = true;
        }

        function addCDN() {
            hasCDN = true;
        }

        function resetCode() {
            codeComplexity = 50;
            isCompressed = false;
            hasCDN = false;
        }

        // 游戏控制函数
        function startChallenge() {
            gameActive = true;
            gameScore = 0;

            // 随机生成问题
            const problems = ['bandwidth', 'server', 'database', 'code'];
            problemType = problems[Math.floor(Math.random() * problems.length)];

            // 启用诊断按钮
            document.getElementById('diagnoseBandwidth').disabled = false;
            document.getElementById('diagnoseServer').disabled = false;
            document.getElementById('diagnoseDatabase').disabled = false;
            document.getElementById('diagnoseCode').disabled = false;
        }

        function diagnose(type) {
            if (!gameActive) return;

            if (type === problemType) {
                gameScore += 100;
                alert('🎉 恭喜！你找到了正确的问题原因！\n\n' + getExplanation(type));

                // 开始新一轮
                setTimeout(() => {
                    const problems = ['bandwidth', 'server', 'database', 'code'];
                    problemType = problems[Math.floor(Math.random() * problems.length)];
                }, 2000);
            } else {
                gameScore = Math.max(0, gameScore - 20);
                alert('❌ 不对哦，再试试看！\n\n提示：' + getHint(problemType));
            }
        }

        function getExplanation(type) {
            const explanations = {
                'bandwidth': '带宽不足会导致数据传输缓慢，特别是在用户并发量大的时候。解决方案：升级带宽、使用CDN、优化资源大小。',
                'server': '服务器负载过高会导致响应变慢。解决方案：升级硬件、优化代码、使用负载均衡。',
                'database': '数据库查询慢会拖累整个网站。解决方案：优化SQL语句、添加索引、使用缓存、读写分离。',
                'code': '代码效率低下会增加处理时间。解决方案：代码重构、启用压缩、使用CDN、减少HTTP请求。'
            };
            return explanations[type];
        }

        function getHint(type) {
            const hints = {
                'bandwidth': '想想数据传输的"管道"是否够粗...',
                'server': '服务器是否太忙了，处理不过来？',
                'database': '数据查询是否需要很长时间？',
                'code': '代码执行效率如何？资源是否优化？'
            };
            return hints[type];
        }

        function calculateResponseTime() {
            let baseTime = 1000;

            // 根据不同问题类型计算响应时间
            switch(problemType) {
                case 'bandwidth':
                    baseTime += users * 500;
                    break;
                case 'server':
                    baseTime += requests * 200;
                    break;
                case 'database':
                    baseTime += queries * 300;
                    break;
                case 'code':
                    baseTime += codeComplexity * 50;
                    break;
            }

            return Math.max(100, baseTime);
        }

        // 数据库演示动画
        function drawDatabaseDemo() {
            const canvas = document.getElementById('databaseCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制主数据库
            ctx.fillStyle = '#3498db';
            ctx.fillRect(100, 100, 80, 100);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主数据库', 140, 155);

            // 绘制从数据库（如果有）
            if (hasSlaveDB) {
                ctx.fillStyle = '#2ecc71';
                ctx.fillRect(200, 120, 60, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('从库', 230, 165);
            }

            // 绘制缓存（如果有）
            if (hasCache) {
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(300, 80, 60, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('缓存', 330, 115);
            }

            // 绘制查询请求
            for (let i = 0; i < queries; i++) {
                const x = 400 + (i % 4) * 30;
                const y = 100 + Math.floor(i / 4) * 40;
                const speed = sqlOptimized ? 2 : 0.5;

                ctx.fillStyle = sqlOptimized ? '#27ae60' : '#e67e22';
                ctx.beginPath();
                ctx.arc(x + Math.sin(Date.now() / 1000 + i) * 10, y, 8, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.fillText('Q', x + Math.sin(Date.now() / 1000 + i) * 10, y + 3);
            }

            // 更新状态
            const querySpeed = sqlOptimized ? '很快' : hasCache ? '较快' : '慢';
            const cacheHitRate = hasCache ? Math.min(80, queries * 10) : 0;

            document.getElementById('queryCount').textContent = queries;
            document.getElementById('querySpeed').textContent = querySpeed;
            document.getElementById('cacheHit').textContent = cacheHitRate + '%';

            requestAnimationFrame(drawDatabaseDemo);
        }

        // 代码优化演示
        function drawCodeDemo() {
            const canvas = document.getElementById('codeCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制代码块
            const complexity = codeComplexity;
            const blockCount = Math.ceil(complexity / 10);

            for (let i = 0; i < blockCount; i++) {
                const x = 50 + (i % 8) * 60;
                const y = 50 + Math.floor(i / 8) * 40;

                ctx.fillStyle = complexity > 70 ? '#e74c3c' : complexity > 40 ? '#f39c12' : '#27ae60';
                ctx.fillRect(x, y, 50, 30);

                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('{ }', x + 25, y + 20);
            }

            // 绘制CDN节点
            if (hasCDN) {
                for (let i = 0; i < 3; i++) {
                    ctx.fillStyle = '#9b59b6';
                    ctx.beginPath();
                    ctx.arc(500 + i * 30, 100, 15, 0, 2 * Math.PI);
                    ctx.fill();

                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('CDN', 500 + i * 30, 105);
                }
            }

            // 绘制压缩效果
            if (isCompressed) {
                ctx.fillStyle = 'rgba(46, 204, 113, 0.3)';
                ctx.fillRect(40, 40, 520, 160);
                ctx.fillStyle = '#2ecc71';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('已压缩 -60%', 300, 120);
            }

            // 更新状态
            const complexityText = complexity > 70 ? '高' : complexity > 40 ? '中等' : '低';
            const executionTime = Math.max(50, complexity * 10 - (isCompressed ? 300 : 0) - (hasCDN ? 200 : 0));
            const optimizationText = (isCompressed && hasCDN) ? '高度优化' :
                                   (isCompressed || hasCDN) ? '部分优化' : '未优化';

            document.getElementById('codeComplexity').textContent = complexityText;
            document.getElementById('executionTime').textContent = executionTime + 'ms';
            document.getElementById('optimizationLevel').textContent = optimizationText;

            requestAnimationFrame(drawCodeDemo);
        }

        // 综合游戏演示
        function drawGameDemo() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (!gameActive) {
                ctx.fillStyle = '#34495e';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('点击"开始挑战"来测试你的知识！', 300, 150);
                return;
            }

            // 绘制网站状态
            const responseTime = calculateResponseTime();
            const satisfaction = Math.max(0, 100 - responseTime / 50);

            // 绘制网站图标
            ctx.fillStyle = satisfaction > 70 ? '#27ae60' : satisfaction > 40 ? '#f39c12' : '#e74c3c';
            ctx.fillRect(250, 100, 100, 80);

            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('网站', 300, 145);

            // 绘制用户反馈
            const emoji = satisfaction > 70 ? '😊' : satisfaction > 40 ? '😐' : '😞';
            ctx.font = '30px Arial';
            ctx.fillText(emoji, 400, 150);

            // 绘制问题指示器
            if (problemType) {
                ctx.fillStyle = '#e74c3c';
                ctx.font = '14px Arial';
                ctx.fillText(`问题类型: ${problemType}`, 300, 50);
            }

            // 更新游戏状态
            document.getElementById('responseTime').textContent = responseTime + 'ms';
            document.getElementById('userSatisfaction').textContent = emoji + ' ' + Math.round(satisfaction) + '%';
            document.getElementById('gameScore').textContent = gameScore;

            requestAnimationFrame(drawGameDemo);
        }

        // 启动动画
        window.addEventListener('load', () => {
            drawBandwidthDemo();
            drawServerDemo();
            drawDatabaseDemo();
            drawCodeDemo();
            drawGameDemo();
        });
    </script>
</body>
</html>
