<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL索引优化 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .lesson-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transform: translateY(50px);
            opacity: 0;
            animation: slideUp 0.8s ease-out forwards;
        }

        .lesson-card:nth-child(2) { animation-delay: 0.2s; }
        .lesson-card:nth-child(3) { animation-delay: 0.4s; }
        .lesson-card:nth-child(4) { animation-delay: 0.6s; }

        .lesson-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .lesson-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: white;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .interactive-btn:active {
            transform: translateY(-1px);
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.6;
            font-size: 1.1rem;
        }

        .game-score {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-weight: bold;
            color: #333;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 1s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            animation: pulse 2s infinite;
        }

        .floating-tip {
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.9rem;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .floating-tip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: #333;
        }
    </style>
</head>
<body>
    <div class="game-score">
        <div>学习进度: <span id="score">0</span>/100</div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🎯 MySQL索引优化大师</h1>
            <p>通过有趣的动画和交互游戏，轻松掌握数据库索引优化的核心原理</p>
        </div>

        <!-- 第一课：非空字段 -->
        <div class="lesson-card">
            <h2 class="lesson-title">📋 第一课：非空字段的重要性</h2>
            
            <div class="explanation">
                <h3>🤔 为什么要避免NULL值？</h3>
                <p>在MySQL中，NULL值就像是数据库中的"未知数"，它们会让查询变得复杂和缓慢。想象一下，如果你在寻找一本书，但书架上有很多空位置，你就需要花更多时间来确认哪些位置是真的没有书。</p>
            </div>

            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="nullCanvas" width="600" height="300"></canvas>
                </div>
                <div style="text-align: center;">
                    <button class="interactive-btn" onclick="demonstrateNull()">🎮 演示NULL值的影响</button>
                    <button class="interactive-btn" onclick="demonstrateNotNull()">✨ 演示NOT NULL的优势</button>
                </div>
            </div>

            <div class="explanation">
                <h3>💡 最佳实践</h3>
                <p>• 使用NOT NULL约束<br>• 用0代替数字字段的NULL<br>• 用空字符串''代替文本字段的NULL<br>• 用特殊值代替日期字段的NULL</p>
            </div>
        </div>

        <!-- 第二课：离散度 -->
        <div class="lesson-card">
            <h2 class="lesson-title">📊 第二课：字段离散度的奥秘</h2>

            <div class="explanation">
                <h3>🎯 什么是离散度？</h3>
                <p>离散度就像是数据的"独特性"。想象你在一个班级里点名，如果大家的名字都不一样，点名就很快；但如果很多人都叫"小明"，你就需要加上姓氏才能区分，这就慢了。</p>
            </div>

            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="discreteCanvas" width="600" height="350"></canvas>
                </div>
                <div style="text-align: center;">
                    <button class="interactive-btn" onclick="showLowDiscrete()">😴 低离散度演示</button>
                    <button class="interactive-btn" onclick="showHighDiscrete()">🚀 高离散度演示</button>
                    <button class="interactive-btn" onclick="compareSearch()">⚡ 搜索效率对比</button>
                </div>
            </div>

            <div class="explanation">
                <h3>🔍 如何测量离散度？</h3>
                <p>使用SQL查询：<code>SELECT COUNT(DISTINCT column_name) FROM table_name;</code><br>
                返回值越大，说明字段的唯一值越多，离散度越高，索引效果越好！</p>
            </div>
        </div>

        <!-- 第三课：索引字段大小 -->
        <div class="lesson-card">
            <h2 class="lesson-title">📏 第三课：索引字段越小越好</h2>

            <div class="explanation">
                <h3>📚 为什么字段越小越好？</h3>
                <p>数据库存储就像图书馆的书架，每一页（Page）能存储的数据是固定的。如果每本书都很薄，一个书架就能放更多书；如果书很厚，书架就放不了几本。索引字段越小，一页能存储的索引项越多，查询效率就越高！</p>
            </div>

            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="sizeCanvas" width="600" height="350"></canvas>
                </div>
                <div style="text-align: center;">
                    <button class="interactive-btn" onclick="showLargeFields()">🐘 大字段演示</button>
                    <button class="interactive-btn" onclick="showSmallFields()">🐭 小字段演示</button>
                    <button class="interactive-btn" onclick="compareIO()">💾 IO效率对比</button>
                </div>
            </div>

            <div class="explanation">
                <h3>⚡ 优化建议</h3>
                <p>• 优先使用整数类型而非字符串<br>• VARCHAR长度设置合理，不要过大<br>• 避免在TEXT、BLOB字段上建索引<br>• 考虑使用前缀索引</p>
            </div>
        </div>

        <!-- 综合游戏 -->
        <div class="lesson-card">
            <h2 class="lesson-title">🎮 综合挑战：索引优化大师</h2>

            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="gameCanvas" width="600" height="400"></canvas>
                </div>
                <div style="text-align: center;">
                    <button class="interactive-btn" onclick="startGame()">🚀 开始挑战</button>
                    <button class="interactive-btn" onclick="resetGame()">🔄 重新开始</button>
                </div>
                <div id="gameInstructions" style="text-align: center; margin-top: 20px; color: #666;">
                    点击"开始挑战"来测试你的索引优化知识！
                </div>
            </div>
        </div>
    </div>

    <div class="floating-tip" id="floatingTip"></div>

    <script>
        let currentScore = 0;
        let gameState = {
            level: 1,
            questions: [
                {
                    question: "哪个字段更适合做索引？",
                    options: ["性别(男/女)", "用户ID(唯一)"],
                    correct: 1,
                    explanation: "用户ID具有更高的离散度，索引效果更好！"
                },
                {
                    question: "以下哪种做法更好？",
                    options: ["允许NULL值", "使用NOT NULL"],
                    correct: 1,
                    explanation: "NOT NULL可以避免复杂的查询优化问题！"
                },
                {
                    question: "哪种数据类型更适合做索引？",
                    options: ["VARCHAR(500)", "INT"],
                    correct: 1,
                    explanation: "INT类型更小，一页能存储更多索引项！"
                }
            ],
            currentQuestion: 0
        };

        // 更新进度
        function updateProgress(points) {
            currentScore = Math.min(currentScore + points, 100);
            document.getElementById('score').textContent = currentScore;
            document.getElementById('progressFill').style.width = currentScore + '%';

            if (currentScore >= 100) {
                setTimeout(() => {
                    alert('🎉 恭喜！你已经成为MySQL索引优化大师！');
                }, 500);
            }
        }

        // NULL值演示
        function demonstrateNull() {
            const canvas = document.getElementById('nullCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('含有NULL值的查询过程', canvas.width/2, 30);

            // 绘制数据行
            const rows = [
                {id: 1, name: 'Alice', age: 25},
                {id: 2, name: null, age: 30},
                {id: 3, name: 'Bob', age: null},
                {id: 4, name: 'Carol', age: 28}
            ];

            let y = 60;
            rows.forEach((row, index) => {
                // 绘制行背景
                ctx.fillStyle = row.name === null || row.age === null ? '#ffebee' : '#e8f5e8';
                ctx.fillRect(50, y, 500, 40);

                // 绘制边框
                ctx.strokeStyle = '#ddd';
                ctx.strokeRect(50, y, 500, 40);

                // 绘制数据
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`ID: ${row.id}`, 70, y + 25);
                ctx.fillText(`Name: ${row.name || 'NULL'}`, 200, y + 25);
                ctx.fillText(`Age: ${row.age || 'NULL'}`, 350, y + 25);

                // 标记NULL值
                if (row.name === null || row.age === null) {
                    ctx.fillStyle = '#f44336';
                    ctx.fillText('⚠️ 需要特殊处理', 450, y + 25);
                }

                y += 50;
            });

            // 绘制说明
            ctx.fillStyle = '#666';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('红色行包含NULL值，查询时需要额外的处理逻辑', canvas.width/2, 280);

            updateProgress(10);
        }

        function demonstrateNotNull() {
            const canvas = document.getElementById('nullCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('NOT NULL约束的查询过程', canvas.width/2, 30);

            // 绘制数据行
            const rows = [
                {id: 1, name: 'Alice', age: 25},
                {id: 2, name: 'Unknown', age: 0},
                {id: 3, name: 'Bob', age: 35},
                {id: 4, name: 'Carol', age: 28}
            ];

            let y = 60;
            rows.forEach((row, index) => {
                // 绘制行背景
                ctx.fillStyle = '#e8f5e8';
                ctx.fillRect(50, y, 500, 40);

                // 绘制边框
                ctx.strokeStyle = '#4caf50';
                ctx.strokeRect(50, y, 500, 40);

                // 绘制数据
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`ID: ${row.id}`, 70, y + 25);
                ctx.fillText(`Name: ${row.name}`, 200, y + 25);
                ctx.fillText(`Age: ${row.age}`, 350, y + 25);

                // 标记优化
                ctx.fillStyle = '#4caf50';
                ctx.fillText('✅ 查询高效', 450, y + 25);

                y += 50;
            });

            // 绘制说明
            ctx.fillStyle = '#666';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('所有字段都有明确值，查询优化器可以高效处理', canvas.width/2, 280);

            updateProgress(10);
        }

        // 离散度演示
        function showLowDiscrete() {
            const canvas = document.getElementById('discreteCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('低离散度字段：性别', canvas.width/2, 30);

            // 绘制数据分布
            const data = ['男', '女', '男', '男', '女', '男', '女', '男'];
            let x = 50;
            data.forEach((value, index) => {
                ctx.fillStyle = value === '男' ? '#2196f3' : '#e91e63';
                ctx.fillRect(x, 60, 60, 40);

                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText(value, x + 30, 85);

                x += 70;
            });

            // 绘制索引效果
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('索引分组结果：', 100, 140);

            // 男性组
            ctx.fillStyle = '#2196f3';
            ctx.fillRect(50, 160, 200, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('男性组 (5人)', 150, 195);

            // 女性组
            ctx.fillStyle = '#e91e63';
            ctx.fillRect(300, 160, 200, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('女性组 (3人)', 400, 195);

            ctx.fillStyle = '#f44336';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('⚠️ 每组数据太多，查询时仍需扫描大量记录', canvas.width/2, 250);

            updateProgress(10);
        }

        function showHighDiscrete() {
            const canvas = document.getElementById('discreteCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('高离散度字段：用户ID', canvas.width/2, 30);

            // 绘制数据分布
            const data = [1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008];
            let x = 30;
            data.forEach((value, index) => {
                const colors = ['#f44336', '#2196f3', '#4caf50', '#ff9800', '#9c27b0', '#00bcd4', '#795548', '#607d8b'];
                ctx.fillStyle = colors[index];
                ctx.fillRect(x, 60, 65, 40);

                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(value.toString(), x + 32, 85);

                x += 70;
            });

            // 绘制索引效果
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('索引分组结果：', 100, 140);

            // 每个ID一组
            x = 30;
            data.forEach((value, index) => {
                const colors = ['#f44336', '#2196f3', '#4caf50', '#ff9800', '#9c27b0', '#00bcd4', '#795548', '#607d8b'];
                ctx.fillStyle = colors[index];
                ctx.fillRect(x, 160, 65, 40);

                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.fillText('1人', x + 32, 185);

                x += 70;
            });

            ctx.fillStyle = '#4caf50';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 每组只有1条记录，查询极其高效！', canvas.width/2, 250);

            updateProgress(10);
        }

        function compareSearch() {
            const canvas = document.getElementById('discreteCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('搜索效率对比', canvas.width/2, 30);

            // 低离散度搜索
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('查找性别="男"的用户：', 50, 70);

            // 绘制搜索过程
            for (let i = 0; i < 5; i++) {
                ctx.fillStyle = '#ffcdd2';
                ctx.fillRect(50 + i * 100, 90, 80, 30);
                ctx.fillStyle = '#333';
                ctx.fillText(`扫描${i+1}`, 70 + i * 100, 110);
            }
            ctx.fillStyle = '#f44336';
            ctx.fillText('需要扫描5条记录', 50, 140);

            // 高离散度搜索
            ctx.fillStyle = '#333';
            ctx.fillText('查找ID=1003的用户：', 50, 180);

            ctx.fillStyle = '#c8e6c9';
            ctx.fillRect(50, 200, 80, 30);
            ctx.fillStyle = '#333';
            ctx.fillText('直接定位', 70, 220);

            ctx.fillStyle = '#4caf50';
            ctx.fillText('只需扫描1条记录', 50, 250);

            updateProgress(15);
        }

        // 字段大小演示
        function showLargeFields() {
            const canvas = document.getElementById('sizeCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('大字段索引：VARCHAR(500)', canvas.width/2, 30);

            // 绘制页面
            ctx.fillStyle = '#e3f2fd';
            ctx.fillRect(50, 60, 500, 200);
            ctx.strokeStyle = '#1976d2';
            ctx.strokeRect(50, 60, 500, 200);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('数据页 (16KB)', 300, 80);

            // 绘制大字段记录
            const records = [
                'user_description_very_long_text_field_1...',
                'user_description_very_long_text_field_2...',
                'user_description_very_long_text_field_3...'
            ];

            let y = 100;
            records.forEach((record, index) => {
                ctx.fillStyle = '#ffcdd2';
                ctx.fillRect(70, y, 460, 40);
                ctx.strokeStyle = '#f44336';
                ctx.strokeRect(70, y, 460, 40);

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(record, 80, y + 25);

                y += 50;
            });

            ctx.fillStyle = '#f44336';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('⚠️ 一页只能存储3条记录', 300, 280);
            ctx.fillText('需要更多IO操作来查找数据', 300, 300);

            updateProgress(10);
        }

        function showSmallFields() {
            const canvas = document.getElementById('sizeCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('小字段索引：INT', canvas.width/2, 30);

            // 绘制页面
            ctx.fillStyle = '#e8f5e8';
            ctx.fillRect(50, 60, 500, 200);
            ctx.strokeStyle = '#4caf50';
            ctx.strokeRect(50, 60, 500, 200);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('数据页 (16KB)', 300, 80);

            // 绘制小字段记录
            let x = 70, y = 100;
            for (let i = 1; i <= 12; i++) {
                ctx.fillStyle = '#c8e6c9';
                ctx.fillRect(x, y, 80, 25);
                ctx.strokeStyle = '#4caf50';
                ctx.strokeRect(x, y, 80, 25);

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`ID: ${1000 + i}`, x + 40, y + 17);

                x += 90;
                if (i % 4 === 0) {
                    x = 70;
                    y += 35;
                }
            }

            ctx.fillStyle = '#4caf50';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 一页可以存储12条记录', 300, 280);
            ctx.fillText('减少IO操作，查询更快！', 300, 300);

            updateProgress(10);
        }

        function compareIO() {
            const canvas = document.getElementById('sizeCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('IO效率对比', canvas.width/2, 30);

            // 大字段IO
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('大字段查询：需要4次IO', 50, 70);

            for (let i = 0; i < 4; i++) {
                ctx.fillStyle = '#ffcdd2';
                ctx.fillRect(50 + i * 120, 90, 100, 40);
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText(`IO ${i+1}`, 100 + i * 120, 115);
            }

            // 小字段IO
            ctx.fillStyle = '#333';
            ctx.textAlign = 'left';
            ctx.fillText('小字段查询：只需1次IO', 50, 170);

            ctx.fillStyle = '#c8e6c9';
            ctx.fillRect(50, 190, 100, 40);
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('IO 1', 100, 215);

            // 绘制时间对比
            ctx.fillStyle = '#f44336';
            ctx.textAlign = 'left';
            ctx.fillText('耗时：40ms', 50, 260);

            ctx.fillStyle = '#4caf50';
            ctx.fillText('耗时：10ms', 50, 290);

            updateProgress(15);
        }

        // 游戏功能
        function startGame() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            if (gameState.currentQuestion >= gameState.questions.length) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#4caf50';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 恭喜通关！', canvas.width/2, canvas.height/2);
                ctx.font = '16px Arial';
                ctx.fillText('你已经掌握了MySQL索引优化的核心知识！', canvas.width/2, canvas.height/2 + 40);
                updateProgress(30);
                return;
            }

            const question = gameState.questions[gameState.currentQuestion];

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制问题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`第${gameState.currentQuestion + 1}题：${question.question}`, canvas.width/2, 50);

            // 绘制选项
            question.options.forEach((option, index) => {
                const y = 120 + index * 80;

                // 选项背景
                ctx.fillStyle = '#e3f2fd';
                ctx.fillRect(100, y, 400, 60);
                ctx.strokeStyle = '#1976d2';
                ctx.strokeRect(100, y, 400, 60);

                // 选项文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.fillText(`${String.fromCharCode(65 + index)}. ${option}`, 120, y + 35);

                // 添加点击事件
                canvas.onclick = function(event) {
                    const rect = canvas.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    question.options.forEach((opt, idx) => {
                        const optY = 120 + idx * 80;
                        if (x >= 100 && x <= 500 && y >= optY && y <= optY + 60) {
                            selectAnswer(idx);
                        }
                    });
                };
            });

            document.getElementById('gameInstructions').textContent = '点击选择你认为正确的答案';
        }

        function selectAnswer(selectedIndex) {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            const question = gameState.questions[gameState.currentQuestion];

            // 显示答案结果
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const isCorrect = selectedIndex === question.correct;

            ctx.fillStyle = isCorrect ? '#4caf50' : '#f44336';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(isCorrect ? '✅ 正确！' : '❌ 错误！', canvas.width/2, 100);

            // 显示解释
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText(question.explanation, canvas.width/2, 150);

            // 显示正确答案
            ctx.font = '14px Arial';
            ctx.fillText(`正确答案：${question.options[question.correct]}`, canvas.width/2, 200);

            if (isCorrect) {
                updateProgress(20);
            }

            // 下一题按钮
            ctx.fillStyle = '#2196f3';
            ctx.fillRect(250, 250, 100, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('下一题', 300, 275);

            canvas.onclick = function(event) {
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                if (x >= 250 && x <= 350 && y >= 250 && y <= 290) {
                    gameState.currentQuestion++;
                    startGame();
                }
            };
        }

        function resetGame() {
            gameState.currentQuestion = 0;
            document.getElementById('gameInstructions').textContent = '点击"开始挑战"来测试你的索引优化知识！';
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#666';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('准备好开始挑战了吗？', canvas.width/2, canvas.height/2);
        }

        // 初始化
        window.onload = function() {
            resetGame();

            // 添加悬浮提示
            const canvases = document.querySelectorAll('canvas');
            canvases.forEach(canvas => {
                canvas.addEventListener('mousemove', function(e) {
                    const tip = document.getElementById('floatingTip');
                    tip.style.left = e.pageX + 10 + 'px';
                    tip.style.top = e.pageY - 30 + 'px';
                    tip.textContent = '点击进行交互演示';
                    tip.style.opacity = '1';
                });

                canvas.addEventListener('mouseleave', function() {
                    document.getElementById('floatingTip').style.opacity = '0';
                });
            });
        };
    </script>
</body>
</html>
