<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP地址与子网掩码 - 零基础互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .learning-card {
            background: white;
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
            position: relative;
            overflow: hidden;
        }

        .learning-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .section-title {
            font-size: 2.2em;
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .question-display {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin: 30px 0;
            text-align: center;
            font-size: 1.3em;
            line-height: 1.8;
            box-shadow: 0 10px 25px rgba(240, 147, 251, 0.3);
        }

        .ip-visualization {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .ip-segment {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 25px;
            border-radius: 20px;
            font-size: 1.8em;
            font-weight: bold;
            min-width: 100px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .ip-segment:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .ip-segment::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .ip-segment:hover::before {
            left: 100%;
        }

        .separator {
            font-size: 2.5em;
            color: #4a5568;
            font-weight: bold;
            animation: bounce 2s infinite;
        }

        .canvas-area {
            text-align: center;
            margin: 40px 0;
            background: #f8fafc;
            border-radius: 20px;
            padding: 30px;
            border: 3px dashed #e2e8f0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
            max-width: 100%;
            height: auto;
        }

        .control-panel {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin: 30px 0;
        }

        .magic-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 30px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .magic-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }

        .magic-button:active {
            transform: translateY(-2px);
        }

        .binary-showcase {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            display: none;
        }

        .binary-row {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
            gap: 8px;
        }

        .binary-bit {
            width: 45px;
            height: 45px;
            background: #e2e8f0;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }

        .binary-bit.network-bit {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            transform: scale(1.1);
        }

        .binary-bit.host-bit {
            background: linear-gradient(135deg, #ed8936, #dd6b20);
            color: white;
            transform: scale(1.1);
        }

        .binary-bit:hover {
            transform: scale(1.2) rotate(5deg);
            z-index: 10;
        }

        .progress-tracker {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .progress-step {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2em;
            transition: all 0.4s ease;
            position: relative;
        }

        .progress-step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: scale(1.3);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .progress-step.completed {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            transform: scale(1.1);
        }

        .answer-reveal {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 25px;
            padding: 40px;
            margin-top: 40px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(252, 182, 159, 0.3);
        }

        .answer-box {
            background: white;
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
            font-size: 1.5em;
            font-weight: bold;
            color: #4a5568;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .answer-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.15);
        }

        .explanation-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            line-height: 1.8;
            font-size: 1.1em;
            display: none;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.5); }
            50% { box-shadow: 0 0 40px rgba(102, 126, 234, 0.8); }
        }

        .glowing {
            animation: glow 2s infinite;
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2em; }
            .ip-segment { font-size: 1.4em; padding: 15px 20px; }
            .magic-button { padding: 15px 25px; font-size: 1em; }
            canvas { width: 100%; height: 300px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 IP地址与子网掩码</h1>
            <p>零基础互动学习 - 通过动画轻松掌握网络知识</p>
            <p style="font-size: 1em; opacity: 0.8;">🎯 让复杂的网络概念变得简单有趣</p>
        </div>

        <!-- 题目展示 -->
        <div class="learning-card">
            <h2 class="section-title">📋 考试题目</h2>
            <div class="question-display">
                <strong>题目：</strong>给定IP地址为<strong>**************</strong>，子网掩码是<strong>*************</strong>，<br><br>
                那么主机号是（ ），该子网的广播地址是（ ）。<br><br>
                <em>选择题答案：A. ***************  B. **************  C. **************  D. ***************</em>
            </div>
        </div>

        <!-- 学习进度 -->
        <div class="learning-card">
            <h2 class="section-title">🎯 学习进度</h2>
            <div class="progress-tracker">
                <div class="progress-step active" id="step1">1</div>
                <div class="progress-step" id="step2">2</div>
                <div class="progress-step" id="step3">3</div>
                <div class="progress-step" id="step4">4</div>
                <div class="progress-step" id="step5">5</div>
                <div class="progress-step" id="step6">6</div>
            </div>
            <div style="text-align: center; margin-top: 20px; color: #666;">
                <span id="stepDescription">第1步：理解IP地址结构</span>
            </div>
        </div>

        <!-- IP地址可视化 -->
        <div class="learning-card">
            <h2 class="section-title">🔍 IP地址分解</h2>
            <div class="ip-visualization">
                <div class="ip-segment" id="segment1" onclick="explainSegment(1)">140</div>
                <div class="separator">.</div>
                <div class="ip-segment" id="segment2" onclick="explainSegment(2)">252</div>
                <div class="separator">.</div>
                <div class="ip-segment" id="segment3" onclick="explainSegment(3)">12</div>
                <div class="separator">.</div>
                <div class="ip-segment" id="segment4" onclick="explainSegment(4)">120</div>
            </div>
            
            <div class="canvas-area">
                <canvas id="mainCanvas" width="1000" height="500"></canvas>
            </div>
            
            <div class="control-panel">
                <button class="magic-button" onclick="startLearningJourney()">🚀 开始学习之旅</button>
                <button class="magic-button" onclick="showBinaryMagic()">✨ 二进制魔法</button>
                <button class="magic-button" onclick="analyzeNetwork()">🔬 网络分析</button>
                <button class="magic-button" onclick="calculateBroadcast()">📡 计算广播</button>
                <button class="magic-button" onclick="showFinalAnswer()">🎉 揭晓答案</button>
            </div>
        </div>

        <!-- 二进制展示 -->
        <div class="learning-card binary-showcase" id="binarySection">
            <h2 class="section-title">💻 二进制世界</h2>
            <div style="text-align: center; margin-bottom: 20px; font-size: 1.2em; color: #666;">
                点击每个二进制位来了解它的含义！
            </div>
            <div class="binary-row" id="ipBinaryRow"></div>
            <div style="text-align: center; margin: 20px 0; font-size: 1.1em; color: #666;">子网掩码</div>
            <div class="binary-row" id="maskBinaryRow"></div>
        </div>

        <!-- 详细解释 -->
        <div class="explanation-panel" id="explanationPanel">
            <h3>🎓 知识点解析</h3>
            <div id="explanationContent"></div>
        </div>

        <!-- 答案揭晓 -->
        <div class="answer-reveal">
            <h2 class="section-title">✅ 最终答案</h2>
            <div class="answer-box">
                <strong>主机号：</strong><span id="hostResult">等待计算...</span>
            </div>
            <div class="answer-box">
                <strong>广播地址：</strong><span id="broadcastResult">等待计算...</span>
            </div>
            <div class="answer-box" style="background: linear-gradient(135deg, #48bb78, #38a169); color: white;">
                <strong>正确答案是：</strong><span id="correctAnswer">B. **************</span>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 1;
        let animationInProgress = false;
        const canvas = document.getElementById('mainCanvas');
        const ctx = canvas.getContext('2d');

        // 步骤描述
        const stepDescriptions = [
            "第1步：理解IP地址结构",
            "第2步：分析子网掩码",
            "第3步：识别网络部分",
            "第4步：识别主机部分", 
            "第5步：计算广播地址",
            "第6步：总结答案"
        ];

        // 更新学习进度
        function updateProgress(step) {
            for (let i = 1; i <= 6; i++) {
                const stepEl = document.getElementById(`step${i}`);
                stepEl.classList.remove('active', 'completed');
                if (i < step) {
                    stepEl.classList.add('completed');
                } else if (i === step) {
                    stepEl.classList.add('active');
                }
            }
            currentStep = step;
            document.getElementById('stepDescription').textContent = stepDescriptions[step - 1];
        }

        // 开始学习之旅
        function startLearningJourney() {
            if (animationInProgress) return;
            animationInProgress = true;
            
            updateProgress(1);
            clearCanvas();
            
            // 第1步：绘制IP地址结构
            drawIPStructure();
            
            setTimeout(() => {
                updateProgress(2);
                drawSubnetMask();
            }, 2000);
            
            setTimeout(() => {
                updateProgress(3);
                highlightNetworkPortion();
            }, 4000);
            
            setTimeout(() => {
                updateProgress(4);
                highlightHostPortion();
            }, 6000);
            
            setTimeout(() => {
                updateProgress(5);
                showBroadcastCalculation();
            }, 8000);
            
            setTimeout(() => {
                updateProgress(6);
                showFinalSummary();
                animationInProgress = false;
            }, 10000);
        }

        // 清空画布
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制IP地址结构
        function drawIPStructure() {
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            
            // 标题
            ctx.fillStyle = '#667eea';
            ctx.fillText('IP地址结构分析', canvas.width/2, 40);
            
            // 绘制IP地址段
            const segments = [140, 252, 12, 120];
            const startX = 200;
            const y = 80;
            const width = 120;
            const height = 80;
            
            segments.forEach((segment, index) => {
                const x = startX + index * 140;
                
                // 绘制渐变背景
                const gradient = ctx.createLinearGradient(x, y, x + width, y + height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                
                ctx.fillStyle = gradient;
                ctx.fillRect(x, y, width, height);
                
                // 绘制边框
                ctx.strokeStyle = '#4a5568';
                ctx.lineWidth = 3;
                ctx.strokeRect(x, y, width, height);
                
                // 绘制数字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Microsoft YaHei';
                ctx.fillText(segment.toString(), x + width/2, y + height/2 + 10);
                
                // 绘制标签
                ctx.fillStyle = '#4a5568';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText(`字节${index + 1}`, x + width/2, y + height + 25);
                
                // 绘制点分隔符
                if (index < 3) {
                    ctx.fillStyle = '#4a5568';
                    ctx.font = 'bold 36px Microsoft YaHei';
                    ctx.fillText('•', x + width + 70, y + height/2 + 10);
                }
            });
        }

        // 绘制子网掩码
        function drawSubnetMask() {
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('子网掩码：*************', canvas.width/2, 220);
            
            const maskSegments = [255, 255, 255, 0];
            const startX = 200;
            const y = 250;
            const width = 120;
            const height = 80;
            
            maskSegments.forEach((segment, index) => {
                const x = startX + index * 140;
                
                // 不同颜色表示网络位和主机位
                if (segment === 255) {
                    ctx.fillStyle = '#48bb78'; // 绿色表示网络位
                } else {
                    ctx.fillStyle = '#ed8936'; // 橙色表示主机位
                }
                
                ctx.fillRect(x, y, width, height);
                
                // 边框
                ctx.strokeStyle = '#4a5568';
                ctx.lineWidth = 3;
                ctx.strokeRect(x, y, width, height);
                
                // 数字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Microsoft YaHei';
                ctx.fillText(segment.toString(), x + width/2, y + height/2 + 10);
                
                // 标签
                ctx.fillStyle = '#4a5568';
                ctx.font = '16px Microsoft YaHei';
                if (segment === 255) {
                    ctx.fillText('网络位', x + width/2, y + height + 25);
                } else {
                    ctx.fillText('主机位', x + width/2, y + height + 25);
                }
            });
        }

        // 高亮网络部分
        function highlightNetworkPortion() {
            // 绘制网络部分说明
            ctx.fillStyle = '#48bb78';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillText('网络部分：140.252.12', canvas.width/2, 400);
            
            // 绘制箭头和说明
            ctx.strokeStyle = '#48bb78';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(200, 380);
            ctx.lineTo(620, 380);
            ctx.stroke();
            
            // 箭头
            ctx.beginPath();
            ctx.moveTo(610, 375);
            ctx.lineTo(620, 380);
            ctx.lineTo(610, 385);
            ctx.stroke();
        }

        // 高亮主机部分
        function highlightHostPortion() {
            ctx.fillStyle = '#ed8936';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillText('主机部分：120', canvas.width/2, 430);
            
            // 更新答案
            document.getElementById('hostResult').textContent = '120';
            document.getElementById('hostResult').style.color = '#ed8936';
        }

        // 显示广播地址计算
        function showBroadcastCalculation() {
            ctx.fillStyle = '#e53e3e';
            ctx.font = 'bold 22px Microsoft YaHei';
            ctx.fillText('广播地址计算：网络部分 + 主机位全1', canvas.width/2, 460);
            ctx.fillText('**************', canvas.width/2, 485);
            
            // 更新答案
            document.getElementById('broadcastResult').textContent = '**************';
            document.getElementById('broadcastResult').style.color = '#e53e3e';
        }

        // 显示最终总结
        function showFinalSummary() {
            const explanationPanel = document.getElementById('explanationPanel');
            explanationPanel.style.display = 'block';
            explanationPanel.innerHTML = `
                <h3>🎓 完整解题思路</h3>
                <div style="line-height: 2; font-size: 1.1em;">
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <p><strong>1. 识别IP地址类型：</strong></p>
                        <p>   ************** → 第一个字节140在128-191之间 → B类地址</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <p><strong>2. 分析子网掩码：</strong></p>
                        <p>   ************* → 前24位为1，后8位为0</p>
                        <p>   表示：前3个字节是网络部分，最后1个字节是主机部分</p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <p><strong>3. 确定主机号：</strong></p>
                        <p>   主机部分 = 最后一个字节 = <span style="color: #ffd700; font-weight: bold;">120</span></p>
                    </div>
                    <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 10px 0;">
                        <p><strong>4. 计算广播地址：</strong></p>
                        <p>   网络部分保持不变：140.252.12</p>
                        <p>   主机部分全部置1：255</p>
                        <p>   广播地址 = <span style="color: #ffd700; font-weight: bold;">**************</span></p>
                    </div>
                    <div style="background: rgba(72, 187, 120, 0.3); padding: 15px; border-radius: 10px; margin: 10px 0; border: 2px solid #48bb78;">
                        <p><strong>5. 最终答案：</strong></p>
                        <p>   ✅ 主机号 = 120</p>
                        <p>   ✅ 广播地址 = **************</p>
                        <p>   ✅ 选择题答案：<span style="color: #ffd700; font-weight: bold; font-size: 1.2em;">B</span></p>
                    </div>
                </div>
            `;

            // 添加打字机效果
            animateTyping(explanationPanel);
        }

        // 打字机动画效果
        function animateTyping(element) {
            const text = element.innerHTML;
            element.innerHTML = '';
            element.style.display = 'block';

            let i = 0;
            const speed = 20; // 打字速度

            function typeWriter() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, speed);
                }
            }

            typeWriter();
        }

        // 添加粒子效果
        function createParticleEffect(x, y, color) {
            const particles = [];
            const particleCount = 15;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: fixed;
                    left: ${x}px;
                    top: ${y}px;
                    width: 6px;
                    height: 6px;
                    background: ${color};
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 1000;
                `;

                document.body.appendChild(particle);
                particles.push(particle);

                // 随机方向和速度
                const angle = (Math.PI * 2 * i) / particleCount;
                const velocity = 2 + Math.random() * 3;
                const vx = Math.cos(angle) * velocity;
                const vy = Math.sin(angle) * velocity;

                // 动画
                let opacity = 1;
                let currentX = x;
                let currentY = y;

                const animate = () => {
                    currentX += vx;
                    currentY += vy;
                    opacity -= 0.02;

                    particle.style.left = currentX + 'px';
                    particle.style.top = currentY + 'px';
                    particle.style.opacity = opacity;

                    if (opacity > 0) {
                        requestAnimationFrame(animate);
                    } else {
                        document.body.removeChild(particle);
                    }
                };

                requestAnimationFrame(animate);
            }
        }

        // 增强的网络分析
        function analyzeNetwork() {
            const segment4 = document.getElementById('segment4');
            segment4.classList.add('glowing');

            // 添加粒子效果
            const rect = segment4.getBoundingClientRect();
            createParticleEffect(
                rect.left + rect.width / 2,
                rect.top + rect.height / 2,
                '#ed8936'
            );

            setTimeout(() => {
                segment4.classList.remove('glowing');
                createKnowledgeCard(
                    "🔍 网络分析完成！",
                    `<strong>网络详细信息：</strong><br><br>
                     🌐 <strong>网络类型：</strong>B类地址<br>
                     📍 <strong>网络部分：</strong>140.252.12<br>
                     💻 <strong>主机部分：</strong>120<br>
                     📊 <strong>可用主机数：</strong>254个<br>
                     🏠 <strong>网络地址：</strong>************<br>
                     📡 <strong>广播地址：</strong>**************<br><br>
                     <em>这个子网可以容纳254台设备！</em>`,
                    "#48bb78"
                );
            }, 2000);
        }

        // 显示二进制魔法
        function showBinaryMagic() {
            const binarySection = document.getElementById('binarySection');
            binarySection.style.display = 'block';
            
            // IP地址二进制
            const ipSegments = [140, 252, 12, 120];
            const ipBinaryRow = document.getElementById('ipBinaryRow');
            ipBinaryRow.innerHTML = '<strong style="margin-right: 20px;">IP地址二进制：</strong>';
            
            ipSegments.forEach((segment, segIndex) => {
                const binary = segment.toString(2).padStart(8, '0');
                for (let i = 0; i < 8; i++) {
                    const bit = document.createElement('div');
                    bit.className = 'binary-bit';
                    if (segIndex < 3) {
                        bit.classList.add('network-bit');
                        bit.title = '网络位';
                    } else {
                        bit.classList.add('host-bit');
                        bit.title = '主机位';
                    }
                    bit.textContent = binary[i];
                    bit.onclick = () => explainBit(segIndex, i, binary[i]);
                    ipBinaryRow.appendChild(bit);
                }
                
                if (segIndex < 3) {
                    const separator = document.createElement('span');
                    separator.textContent = '.';
                    separator.style.margin = '0 15px';
                    separator.style.fontSize = '1.5em';
                    ipBinaryRow.appendChild(separator);
                }
            });
            
            // 子网掩码二进制
            const maskSegments = [255, 255, 255, 0];
            const maskBinaryRow = document.getElementById('maskBinaryRow');
            maskBinaryRow.innerHTML = '<strong style="margin-right: 20px;">掩码二进制：</strong>';
            
            maskSegments.forEach((segment, segIndex) => {
                const binary = segment.toString(2).padStart(8, '0');
                for (let i = 0; i < 8; i++) {
                    const bit = document.createElement('div');
                    bit.className = 'binary-bit';
                    if (segment === 255) {
                        bit.classList.add('network-bit');
                        bit.title = '网络掩码位';
                    } else {
                        bit.classList.add('host-bit');
                        bit.title = '主机掩码位';
                    }
                    bit.textContent = binary[i];
                    maskBinaryRow.appendChild(bit);
                }
                
                if (segIndex < 3) {
                    const separator = document.createElement('span');
                    separator.textContent = '.';
                    separator.style.margin = '0 15px';
                    separator.style.fontSize = '1.5em';
                    maskBinaryRow.appendChild(separator);
                }
            });
        }

        // 解释二进制位
        function explainBit(segIndex, bitIndex, bitValue) {
            const position = segIndex * 8 + bitIndex + 1;
            let explanation = `第${position}位：${bitValue}\n\n`;

            if (segIndex < 3) {
                explanation += "这是网络位，用于标识网络";
            } else {
                explanation += "这是主机位，用于标识主机";
            }

            alert(explanation);
        }

        // 计算广播地址
        function calculateBroadcast() {
            alert('📡 广播地址计算步骤：\n\n' +
                  '1. 保持网络部分不变：140.252.12\n' +
                  '2. 将主机部分全部设为1：11111111\n' +
                  '3. 转换为十进制：255\n' +
                  '4. 最终广播地址：**************\n\n' +
                  '✅ 这就是答案B！');
        }

        // 显示最终答案
        function showFinalAnswer() {
            document.getElementById('hostResult').textContent = '120';
            document.getElementById('broadcastResult').textContent = '**************';
            document.getElementById('correctAnswer').textContent = 'B. **************';
            
            // 滚动到答案区域
            document.querySelector('.answer-reveal').scrollIntoView({ 
                behavior: 'smooth' 
            });
            
            // 添加闪烁效果
            document.querySelector('.answer-reveal').style.animation = 'glow 1s ease-in-out 3';
        }

        // 添加知识点卡片动画
        function createKnowledgeCard(title, content, color) {
            const card = document.createElement('div');
            card.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) scale(0);
                background: linear-gradient(135deg, ${color}, #764ba2);
                color: white;
                padding: 30px;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                z-index: 1000;
                max-width: 500px;
                text-align: center;
                transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            `;

            card.innerHTML = `
                <h3 style="margin-bottom: 15px; font-size: 1.5em;">${title}</h3>
                <p style="line-height: 1.6; margin-bottom: 20px;">${content}</p>
                <button onclick="closeKnowledgeCard(this)" style="
                    background: rgba(255,255,255,0.2);
                    border: 2px solid white;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-weight: bold;
                ">我明白了！</button>
            `;

            document.body.appendChild(card);

            // 动画显示
            setTimeout(() => {
                card.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 100);

            return card;
        }

        function closeKnowledgeCard(button) {
            const card = button.parentElement;
            card.style.transform = 'translate(-50%, -50%) scale(0)';
            setTimeout(() => {
                document.body.removeChild(card);
            }, 300);
        }

        // 增强的段落解释
        function explainSegment(segmentIndex) {
            const segments = [140, 252, 12, 120];
            const titles = [
                "🌐 网络标识 - 第1字节",
                "🌐 网络标识 - 第2字节",
                "🏠 子网标识 - 第3字节",
                "💻 主机标识 - 第4字节"
            ];
            const explanations = [
                `数值140表示这是B类地址的开始。<br><br>
                 <strong>知识点：</strong><br>
                 • A类：1-126<br>
                 • B类：128-191<br>
                 • C类：192-223<br><br>
                 <strong>二进制：</strong>${segments[0].toString(2).padStart(8, '0')}`,

                `第2字节252继续标识网络部分。<br><br>
                 在B类地址中，前两个字节都用于网络标识。<br><br>
                 <strong>二进制：</strong>${segments[1].toString(2).padStart(8, '0')}`,

                `第3字节12是子网号。<br><br>
                 <strong>重要：</strong>原本B类地址只用前2字节做网络标识，<br>
                 但这里的子网掩码*************把第3字节也用作网络部分！<br><br>
                 <strong>二进制：</strong>${segments[2].toString(2).padStart(8, '0')}`,

                `第4字节120就是我们要找的主机号！<br><br>
                 <strong>这就是答案的第一部分！</strong><br><br>
                 在这个子网中，主机号可以是1-254<br>
                 （0是网络地址，255是广播地址）<br><br>
                 <strong>二进制：</strong>${segments[3].toString(2).padStart(8, '0')}`
            ];

            createKnowledgeCard(titles[segmentIndex - 1], explanations[segmentIndex - 1], '#667eea');
        }

        // 添加趣味小测试
        function addQuizMode() {
            const quizButton = document.createElement('button');
            quizButton.className = 'magic-button';
            quizButton.innerHTML = '🧠 趣味小测试';
            quizButton.onclick = startQuiz;

            document.querySelector('.control-panel').appendChild(quizButton);
        }

        function startQuiz() {
            const questions = [
                {
                    q: "**************是什么类型的IP地址？",
                    options: ["A类", "B类", "C类", "D类"],
                    correct: 1,
                    explanation: "第一字节140在128-191范围内，所以是B类地址！"
                },
                {
                    q: "子网掩码*************表示什么？",
                    options: ["前8位是网络位", "前16位是网络位", "前24位是网络位", "前32位是网络位"],
                    correct: 2,
                    explanation: "************* = 11111111.11111111.11111111.00000000，前24位是1，所以前24位是网络位！"
                },
                {
                    q: "在这个例子中，主机号是多少？",
                    options: ["140", "252", "12", "120"],
                    correct: 3,
                    explanation: "最后一个字节120就是主机号！"
                }
            ];

            let currentQ = 0;
            let score = 0;

            function showQuestion() {
                if (currentQ >= questions.length) {
                    alert(`🎉 测试完成！\n得分：${score}/${questions.length}\n\n${score === questions.length ? '完美！你已经完全掌握了！' : '继续加油，多练习几次就能完全掌握了！'}`);
                    return;
                }

                const q = questions[currentQ];
                let optionsText = '';
                q.options.forEach((option, index) => {
                    optionsText += `${index + 1}. ${option}\n`;
                });

                const answer = prompt(`🧠 第${currentQ + 1}题：\n\n${q.q}\n\n${optionsText}\n请输入选项编号（1-4）：`);

                if (answer && parseInt(answer) - 1 === q.correct) {
                    score++;
                    alert(`✅ 正确！\n\n${q.explanation}`);
                } else {
                    alert(`❌ 不对哦！\n\n正确答案是：${q.options[q.correct]}\n\n${q.explanation}`);
                }

                currentQ++;
                setTimeout(showQuestion, 1000);
            }

            showQuestion();
        }

        // 初始化
        window.onload = function() {
            drawIPStructure();
            addQuizMode();

            // 添加欢迎提示
            setTimeout(() => {
                createKnowledgeCard(
                    "🎉 欢迎来到IP地址学习世界！",
                    "我将通过生动的动画和交互，帮你轻松掌握IP地址和子网掩码的知识！<br><br>" +
                    "🚀 点击'开始学习之旅'开始系统学习<br>" +
                    "🔍 点击IP地址各部分了解详情<br>" +
                    "🧠 完成趣味小测试检验学习效果",
                    "#667eea"
                );
            }, 1500);
        };
    </script>
</body>
</html>
