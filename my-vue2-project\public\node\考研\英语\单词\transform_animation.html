<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Transform</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #8E44AD; /* 紫色主题，代表神秘与变化 */
            --secondary-color: #9B59B6;
            --accent-color: #F1C40F; /* 金色点缀，代表新生 */
            --dark-bg: #2C3E50;
            --light-bg: #f8f9fa;
            --panel-bg: #ffffff;
            --text-color: #333;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #ECF0F1;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--primary-color);
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: #546e7a;
            margin-bottom: 20px;
        }

        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--secondary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: #555; }
        .breakdown-section { margin-top: 25px; padding: 20px; background-color: #dfe6e9; border-radius: 10px; }
        .breakdown-section h3 { margin-top: 0; color: var(--secondary-color); font-size: 1.3em; margin-bottom: 15px; }
        .morpheme-btn { padding: 8px 15px; border: 2px solid var(--primary-color); border-radius: 20px; background-color: transparent; color: var(--primary-color); font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; }
        .morpheme-btn:hover, .morpheme-btn.active { background-color: var(--primary-color); color: white; transform: translateY(-2px); box-shadow: 0 0 10px var(--primary-color);}

        .animation-panel {
            flex: 2;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            background: var(--dark-bg);
        }

        .activity-title { font-size: 1.8em; color: var(--light-bg); margin-bottom: 20px; text-align: center; }
        .activity-wrapper { display: none; width: 100%; height: 100%; flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        .game-container { width: 90%; max-width: 600px; height: 400px; border: 2px dashed #546e7a; border-radius: 15px; position: relative; overflow: hidden; display: flex; align-items: center; justify-content: center; background: #34495E; }
        .control-button { margin-top: 30px; padding: 15px 30px; font-size: 1.2em; color: var(--dark-bg); background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; box-shadow: 0 4px 15px rgba(142, 68, 173, 0.4); }

        /* 词根 form (形状) */
        #form-shape {
            width: 100px;
            height: 100px;
            background-color: var(--accent-color);
            transition: all 1s ease-in-out;
        }

        /* 前缀 trans- (变化) */
        #trans-bridge {
            width: 0%;
            height: 10px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            position: absolute;
            top: 50%;
            left: 0;
            transition: width 2s ease-in-out;
        }
        #trans-object {
             width: 40px;
             height: 40px;
             background-color: white;
             border-radius: 50%;
             position: absolute;
             top: calc(50% - 20px);
             left: -40px;
             transition: left 2s ease-in-out;
        }

        /* 完整动画: 蜕变 */
        #caterpillar {
            position: absolute;
            bottom: 20%;
            left: 10%;
            width: 120px;
            height: 30px;
            display: flex;
            transition: all 2s ease;
        }
        .segment { width: 30px; height: 30px; background-color: #2ECC71; border-radius: 50%; margin-right: -10px; }
        
        #chrysalis {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 80px;
            background-color: #795548;
            border-radius: 40px 40px 20px 20px;
            opacity: 0;
            transition: opacity 1s ease, transform 1s ease;
        }
        
        #butterfly {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            opacity: 0;
            transition: all 1.5s ease;
            width: 150px;
            height: 120px;
        }
        .wing { position: absolute; width: 70px; height: 120px; background-color: var(--accent-color); border-radius: 70px 0 70px 0; }
        #left-wing { top: 0; left: 0; transform-origin: top right; }
        #right-wing { top: 0; right: 0; transform-origin: top left; transform: scaleX(-1); }

        .metamorphosis-run #caterpillar { left: 45%; bottom: 60%; transform: rotate(-90deg); opacity: 0;}
        .metamorphosis-run #chrysalis { opacity: 1; transform: translate(-50%, -50%) rotate(360deg); }
        .metamorphosis-run #butterfly { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        .metamorphosis-run #left-wing { animation: flap 1s infinite alternate ease-in-out; }
        .metamorphosis-run #right-wing { animation: flap 1s infinite alternate ease-in-out; }

        @keyframes flap {
            from { transform: scaleX(1) rotate(0deg); }
            to { transform: scaleX(1) rotate(20deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>transform</h1>
            <p class="pronunciation">[trænsˈfɔːrm]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 改变, 变换, 使...变形</p>
                <p><strong>故事：</strong>想象一只毛毛虫 (a caterpillar's form)，它经历了一个"跨越"(trans-)生命阶段的过程，最终变成了一只美丽的蝴蝶。这个过程就是形态(form)的彻底改变(trans-)。</p>
                <div class="example">
                    <p><strong>例句：</strong> The caterpillar will transform into a butterfly.</p>
                    <p><strong>翻译：</strong>毛毛虫将蜕变成一只蝴蝶。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>交互式词缀解析</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="form-game">form (形状)</button>
                    <button class="morpheme-btn" data-activity="trans-game">trans- (变化)</button>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>完整单词活动</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画: 蜕变</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p style="color: white;">点击左侧按钮，开始"蜕变"之旅！</p></div>
            
            <div id="form-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="form-shape"></div>
                </div>
                <button class="control-button" id="form-btn">改变形状 (form)</button>
            </div>
            
            <div id="trans-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="trans-bridge"></div>
                    <div id="trans-object"></div>
                </div>
                <button class="control-button" id="trans-btn">跨越 (trans-)</button>
            </div>

            <div id="full-animation" class="activity-wrapper">
                <div class="game-container" id="metamorphosis-container">
                    <div id="caterpillar">
                        <div class="segment"></div><div class="segment"></div><div class="segment"></div><div class="segment"></div>
                    </div>
                    <div id="chrysalis"></div>
                    <div id="butterfly">
                        <div id="left-wing" class="wing"></div>
                        <div id="right-wing" class="wing"></div>
                    </div>
                </div>
                <button class="control-button" id="metamorphosis-btn">开始蜕变</button>
            </div>

        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                const titleText = btn.dataset.activity === 'full-animation' ? '动画: 蜕变' : `词缀: ${btn.textContent}`;
                activityTitle.textContent = titleText;
                activityWrappers.forEach(w => w.classList.remove('active'));
                document.getElementById(btn.dataset.activity)?.classList.add('active');
            });
        });

        // form game
        document.getElementById('form-btn').addEventListener('click', () => {
            const shape = document.getElementById('form-shape');
            const isCircle = shape.style.borderRadius === '50%';
            shape.style.borderRadius = isCircle ? '0' : '50%';
            shape.style.transform = isCircle ? 'rotate(0deg)' : 'rotate(360deg)';
            shape.style.backgroundColor = isCircle ? '#F1C40F' : '#3498DB';
        });

        // trans- game
        document.getElementById('trans-btn').addEventListener('click', (e) => {
            const bridge = document.getElementById('trans-bridge');
            const obj = document.getElementById('trans-object');
            e.target.disabled = true;

            bridge.style.width = '100%';
            obj.style.left = 'calc(100% - 40px)';

            setTimeout(() => {
                bridge.style.width = '0%';
                obj.style.left = '-40px';
                e.target.disabled = false;
            }, 2500);
        });

        // Metamorphosis animation
        const metamorphosisBtn = document.getElementById('metamorphosis-btn');
        const container = document.getElementById('metamorphosis-container');
        metamorphosisBtn.addEventListener('click', () => {
            const isRunning = container.classList.contains('metamorphosis-run');
            if (isRunning) {
                container.classList.remove('metamorphosis-run');
                metamorphosisBtn.textContent = '开始蜕变';
                // Reset butterfly wing animation to prevent weird state
                const leftWing = document.getElementById('left-wing');
                const rightWing = document.getElementById('right-wing');
                leftWing.style.animation = 'none';
                rightWing.style.animation = 'none';
                //offsetHeight triggers reflow, which is needed to restart css animation
                void leftWing.offsetHeight;
                void rightWing.offsetHeight;
                 leftWing.style.animation = '';
                rightWing.style.animation = '';


            } else {
                container.classList.add('metamorphosis-run');
                metamorphosisBtn.textContent = '重置';
            }
        });
    });
    </script>
</body>
</html> 