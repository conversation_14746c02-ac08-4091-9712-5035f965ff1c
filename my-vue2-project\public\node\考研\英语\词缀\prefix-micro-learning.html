<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：micro-（微小、极小）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: radial-gradient(ellipse at center, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
        }

        #microscopeCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .laboratory-stations {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .microscope-station {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .microscope-station::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.6s;
        }

        .microscope-station:hover::before {
            left: 100%;
        }

        .microscope-station:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .microscope-station.focused {
            opacity: 1;
            transform: translateY(0);
        }

        .magnification-process {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            position: relative;
        }

        .macro-view {
            background: #28a745;
            color: white;
            padding: 20px 30px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 1.3rem;
            position: relative;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .macro-view::after {
            content: '宏观';
            position: absolute;
            top: -15px;
            right: -15px;
            background: #218838;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .microscope-lens {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: radial-gradient(circle, #ffffff, #e3f2fd, #2196f3);
            position: relative;
            margin: 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: magnify 3s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(33, 150, 243, 0.4);
            border: 3px solid #1976d2;
        }

        .microscope-lens::before {
            content: '🔬';
            font-size: 2rem;
            animation: magnify 2s ease-in-out infinite reverse;
        }

        .microscope-lens::after {
            content: '';
            position: absolute;
            width: 100px;
            height: 100px;
            border: 2px dashed rgba(33, 150, 243, 0.3);
            border-radius: 50%;
            animation: magnify 4s ease-in-out infinite;
        }

        .micro-view {
            background: #dc3545;
            color: white;
            padding: 15px 20px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 1rem;
            position: relative;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            animation: microPulse 2s ease-in-out infinite;
        }

        .micro-view::after {
            content: '微观';
            position: absolute;
            top: -12px;
            right: -12px;
            background: #c82333;
            color: white;
            padding: 1px 6px;
            border-radius: 8px;
            font-size: 0.6rem;
        }

        .prefix-highlight {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .magnification-explanation {
            background: rgba(33, 150, 243, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .research-notes {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #ffc107;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #2196f3, #1976d2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #ffc107;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .focus-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #6c757d;
            transition: all 0.3s ease;
        }

        .focus-indicator.focusing {
            background: #ffc107;
            animation: focus 1.5s infinite;
        }

        .focus-indicator.magnified {
            background: #dc3545;
            box-shadow: 0 0 10px #dc3545;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes magnify {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        @keyframes microPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
            }
            50% {
                transform: scale(0.9);
                box-shadow: 0 0 20px rgba(220, 53, 69, 0.8);
            }
        }

        @keyframes focus {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.3);
            }
        }

        @keyframes cellFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-15px) rotate(180deg);
                opacity: 1;
            }
        }

        @keyframes moleculeVibrate {
            0%, 100% {
                transform: translateX(0px);
            }
            25% {
                transform: translateX(2px);
            }
            75% {
                transform: translateX(-2px);
            }
        }

        .interactive-hint {
            text-align: center;
            color: #2196f3;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .micro-particles {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #2196f3;
            border-radius: 50%;
            pointer-events: none;
            animation: cellFloat 4s infinite;
        }

        .magnification-scale {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .scale-level {
            width: 40px;
            height: 6px;
            background: #dee2e6;
            border-radius: 3px;
            transition: all 0.3s ease;
            position: relative;
        }

        .scale-level.active {
            background: linear-gradient(90deg, #28a745, #ffc107);
            animation: magnify 1s infinite;
        }

        .scale-level.micro {
            background: linear-gradient(90deg, #ffc107, #dc3545);
            animation: microPulse 0.8s infinite;
        }

        .scale-level::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 15px;
            height: 1px;
            background: #bdc3c7;
            transform: translateY(-50%);
        }

        .scale-level:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>微观前缀：micro-</h1>
            <p>在微观世界探索实验室中学会"极小缩放"的奥秘</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🔬 微观世界探索实验室的故事</h2>
                <p>在一座先进的科学研究院里，有一个神奇的微观世界探索实验室。这里配备了最先进的"micro-"超级显微镜，能够将任何普通大小的事物放大到微观层面进行观察。当普通的词汇通过这台显微镜时，就会从宏观尺度缩小到微观尺度，获得"微小"、"极小"、"精细"的神奇特性！</p>
            </div>

            <div class="canvas-container">
                <canvas id="microscopeCanvas"></canvas>
                <div class="magnification-scale" id="magnificationScale">
                    <div class="scale-level"></div>
                    <div class="scale-level"></div>
                    <div class="scale-level"></div>
                    <div class="scale-level"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择微观世界探索实验室的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"微观世界探索实验室"的比喻，是因为"micro-"前缀的核心含义就是"微小"、"极小"，这与显微镜将事物放大到微观层面观察的功能完美契合。显微镜的视觉效果帮助学生理解"从大到小"的尺度转换概念，而科学实验室的设定强调了精确观察和细致研究的科学精神。通过显微镜的放大过程，让抽象的"微小"概念变得生动有趣。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="startMagnification()">启动显微镜</button>
                <button class="btn" onclick="showStations()">显示实验站</button>
                <button class="btn" onclick="resetLaboratory()">重置实验室</button>
            </div>

            <div class="interactive-hint">
                🔍 点击"启动显微镜"观看词汇微观化过程，点击实验站查看研究笔记
            </div>
        </div>

        <div class="laboratory-stations" id="laboratoryStations">
            <div class="microscope-station">
                <div class="focus-indicator"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Phone → Microphone</h3>
                <div class="magnification-process">
                    <div class="macro-view">phone</div>
                    <div class="microscope-lens"></div>
                    <div class="micro-view"><span class="prefix-highlight">micro</span>phone</div>
                </div>
                <div class="magnification-explanation">
                    声音 → <span class="prefix-highlight">微小</span>声音
                </div>
                <div class="research-notes">
                    <strong>研究笔记：</strong><br>
                    <strong>宏观：</strong>Answer the phone call. (接电话。)<br>
                    <strong>微观：</strong>Speak into the microphone. (对着麦克风说话。)<br>
                    <strong>解析：</strong>"phone"表示声音，加上"micro-"变成"microphone"，表示麦克风、话筒。从一般的声音缩小到捕捉微小声音的精密设备。
                </div>
            </div>

            <div class="microscope-station">
                <div class="focus-indicator"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Wave → Microwave</h3>
                <div class="magnification-process">
                    <div class="macro-view">wave</div>
                    <div class="microscope-lens"></div>
                    <div class="micro-view"><span class="prefix-highlight">micro</span>wave</div>
                </div>
                <div class="magnification-explanation">
                    波 → <span class="prefix-highlight">微</span>波
                </div>
                <div class="research-notes">
                    <strong>研究笔记：</strong><br>
                    <strong>宏观：</strong>The ocean wave is huge. (海浪很大。)<br>
                    <strong>微观：</strong>Heat food in the microwave. (用微波炉加热食物。)<br>
                    <strong>解析：</strong>"wave"表示波，加上"micro-"变成"microwave"，表示微波。从肉眼可见的大波缩小到极短的电磁波。
                </div>
            </div>

            <div class="microscope-station">
                <div class="focus-indicator"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Scope → Microscope</h3>
                <div class="magnification-process">
                    <div class="macro-view">scope</div>
                    <div class="microscope-lens"></div>
                    <div class="micro-view"><span class="prefix-highlight">micro</span>scope</div>
                </div>
                <div class="magnification-explanation">
                    观察 → <span class="prefix-highlight">微观</span>观察
                </div>
                <div class="research-notes">
                    <strong>研究笔记：</strong><br>
                    <strong>宏观：</strong>The scope of work is large. (工作范围很大。)<br>
                    <strong>微观：</strong>Look through the microscope. (通过显微镜观察。)<br>
                    <strong>解析：</strong>"scope"表示观察、范围，加上"micro-"变成"microscope"，表示显微镜。从宏观观察缩小到微观精密观察。
                </div>
            </div>

            <div class="microscope-station">
                <div class="focus-indicator"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Chip → Microchip</h3>
                <div class="magnification-process">
                    <div class="macro-view">chip</div>
                    <div class="microscope-lens"></div>
                    <div class="micro-view"><span class="prefix-highlight">micro</span>chip</div>
                </div>
                <div class="magnification-explanation">
                    芯片 → <span class="prefix-highlight">微型</span>芯片
                </div>
                <div class="research-notes">
                    <strong>研究笔记：</strong><br>
                    <strong>宏观：</strong>Eat a potato chip. (吃薯片。)<br>
                    <strong>微观：</strong>The microchip is very small. (微芯片非常小。)<br>
                    <strong>解析：</strong>"chip"表示芯片、碎片，加上"micro-"变成"microchip"，表示微芯片。从普通大小的芯片缩小到极其微小的电子芯片。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"micro-"前缀表示微小、极小、精细的含义。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"micro-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"micro-"后的词根基本含义</li>
                <li><strong>应用微小概念：</strong>在词根意思前加上"微"、"微小"、"微型"</li>
                <li><strong>尺度调整：</strong>强调从宏观尺度缩小到微观尺度</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象微观世界探索实验室的显微镜，"micro-"就像显微镜，让事物变得微小精细！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('microscopeCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentMagnification = 0;
        let microParticles = [];
        let cells = [];
        let magnificationLevel = 1;
        
        const magnifications = [
            { macro: 'phone', micro: 'microphone', x: 150, y: 200 },
            { macro: 'wave', micro: 'microwave', x: 350, y: 300 },
            { macro: 'scope', micro: 'microscope', x: 550, y: 150 },
            { macro: 'chip', micro: 'microchip', x: 750, y: 250 }
        ];

        class MicroParticle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 1;
                this.vy = (Math.random() - 0.5) * 1;
                this.size = Math.random() * 2 + 1;
                this.life = 1;
                this.decay = 0.01;
                this.color = `hsl(${200 + Math.random() * 60}, 70%, 60%)`;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.size *= 0.995;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        class Cell {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.size = Math.random() * 8 + 4;
                this.originalSize = this.size;
                this.pulsePhase = Math.random() * Math.PI * 2;
                this.floatPhase = Math.random() * Math.PI * 2;
                this.color = `hsl(${Math.random() * 60 + 180}, 60%, 70%)`;
            }

            update() {
                this.pulsePhase += 0.03;
                this.floatPhase += 0.02;
                this.size = this.originalSize + Math.sin(this.pulsePhase) * 2;
                this.y += Math.sin(this.floatPhase) * 0.5;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = 0.7;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                
                // 细胞核
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size * 0.3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        function initMicroParticles() {
            microParticles = [];
            for (let i = 0; i < 30; i++) {
                microParticles.push(new MicroParticle(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height
                ));
            }
        }

        function initCells() {
            cells = [];
            for (let i = 0; i < 8; i++) {
                cells.push(new Cell(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height
                ));
            }
        }

        function drawMicroscope() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 显微镜镜头
            ctx.save();
            ctx.translate(centerX, centerY);
            
            // 外镜框
            ctx.strokeStyle = '#1976d2';
            ctx.lineWidth = 6;
            ctx.beginPath();
            ctx.arc(0, 0, 80, 0, Math.PI * 2);
            ctx.stroke();
            
            // 镜头
            const lensGradient = ctx.createRadialGradient(0, 0, 20, 0, 0, 70);
            lensGradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
            lensGradient.addColorStop(0.3, 'rgba(227, 242, 253, 0.7)');
            lensGradient.addColorStop(0.7, 'rgba(33, 150, 243, 0.5)');
            lensGradient.addColorStop(1, 'rgba(25, 118, 210, 0.8)');
            
            ctx.fillStyle = lensGradient;
            ctx.beginPath();
            ctx.arc(0, 0, 70, 0, Math.PI * 2);
            ctx.fill();
            
            // 放大倍数指示
            if (animationState === 'magnifying') {
                ctx.fillStyle = '#1976d2';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(`${magnificationLevel}00×`, 0, 0);
                magnificationLevel = Math.min(magnificationLevel + 0.1, 10);
            }
            
            ctx.restore();
        }

        function drawWordMagnification() {
            if (currentMagnification < magnifications.length && animationState === 'magnifying') {
                const mag = magnifications[currentMagnification];
                const centerX = canvas.width / 2;
                
                // 宏观词汇（左侧，大）
                ctx.fillStyle = '#28a745';
                ctx.fillRect(centerX - 280, mag.y, 120, 50);
                ctx.fillStyle = 'white';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(mag.macro, centerX - 220, mag.y + 30);
                
                // 放大光束
                ctx.strokeStyle = '#2196f3';
                ctx.lineWidth = 3;
                ctx.setLineDash([6, 3]);
                ctx.beginPath();
                ctx.moveTo(centerX - 140, mag.y + 25);
                ctx.lineTo(centerX + 140, mag.y + 25);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // 微观词汇（右侧，小）
                ctx.fillStyle = '#dc3545';
                ctx.fillRect(centerX + 160, mag.y + 10, 100, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                
                // 高亮micro-前缀
                ctx.fillStyle = '#ffc107';
                ctx.fillText('micro', centerX + 185, mag.y + 27);
                ctx.fillStyle = 'white';
                ctx.fillText(mag.macro, centerX + 225, mag.y + 27);
            }
        }

        function createMicroEffect(x, y) {
            for (let i = 0; i < 8; i++) {
                microParticles.push(new MicroParticle(x, y));
            }
        }

        function updateParticles() {
            microParticles = microParticles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
        }

        function updateCells() {
            cells.forEach(cell => {
                cell.update();
                cell.draw();
            });
        }

        function updateMagnificationScale() {
            const levels = document.querySelectorAll('.scale-level');
            levels.forEach((level, index) => {
                level.classList.remove('active', 'micro');
                if (index < currentMagnification) {
                    level.classList.add('micro');
                } else if (index === currentMagnification && animationState === 'magnifying') {
                    level.classList.add('active');
                }
            });
        }

        function updateStationStatus() {
            const stations = document.querySelectorAll('.microscope-station');
            const indicators = document.querySelectorAll('.focus-indicator');
            
            stations.forEach((station, index) => {
                const indicator = indicators[index];
                if (index < currentMagnification) {
                    indicator.classList.remove('focusing');
                    indicator.classList.add('magnified');
                } else if (index === currentMagnification && animationState === 'magnifying') {
                    indicator.classList.add('focusing');
                    indicator.classList.remove('magnified');
                } else {
                    indicator.classList.remove('focusing', 'magnified');
                }
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制实验室背景
            const bgGradient = ctx.createRadialGradient(canvas.width/2, canvas.height/2, 0, canvas.width/2, canvas.height/2, Math.max(canvas.width, canvas.height)/2);
            bgGradient.addColorStop(0, '#f8f9fa');
            bgGradient.addColorStop(0.5, '#e9ecef');
            bgGradient.addColorStop(1, '#dee2e6');
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制微观细胞
            updateCells();
            
            // 绘制显微镜
            drawMicroscope();
            
            // 绘制词汇放大过程
            drawWordMagnification();
            
            // 更新微粒效果
            updateParticles();
            
            // 更新界面状态
            updateMagnificationScale();
            updateStationStatus();
        }

        function animate() {
            drawScene();
            
            if (animationState === 'magnifying' && currentMagnification < magnifications.length) {
                // 创建微观效果
                if (Math.random() < 0.1) {
                    createMicroEffect(canvas.width / 2 + (Math.random() - 0.5) * 160, 
                                    canvas.height / 2 + (Math.random() - 0.5) * 120);
                }
                
                // 自动切换到下一个放大
                setTimeout(() => {
                    currentMagnification++;
                    magnificationLevel = 1;
                    if (currentMagnification >= magnifications.length) {
                        animationState = 'completed';
                    }
                }, 3000);
            }
            
            requestAnimationFrame(animate);
        }

        function startMagnification() {
            animationState = 'magnifying';
            currentMagnification = 0;
            magnificationLevel = 1;
            microParticles = [];
        }

        function showStations() {
            const stations = document.querySelectorAll('.microscope-station');
            stations.forEach((station, index) => {
                setTimeout(() => {
                    station.classList.add('focused');
                }, index * 400);
            });
        }

        function resetLaboratory() {
            animationState = 'idle';
            currentMagnification = 0;
            magnificationLevel = 1;
            microParticles = [];
            
            const stations = document.querySelectorAll('.microscope-station');
            stations.forEach(station => station.classList.remove('focused'));
            
            const indicators = document.querySelectorAll('.focus-indicator');
            indicators.forEach(indicator => {
                indicator.classList.remove('focusing', 'magnified');
            });
        }

        // 初始化
        initMicroParticles();
        initCells();
        animate();

        // 点击实验站的交互
        document.querySelectorAll('.microscope-station').forEach(station => {
            station.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
