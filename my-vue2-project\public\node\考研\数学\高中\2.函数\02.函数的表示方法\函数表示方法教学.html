<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函数的表示方法 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .nav-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .nav-item {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .nav-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            background: rgba(255,255,255,1);
        }

        .nav-item h3 {
            color: #333;
            font-size: 1.4rem;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .nav-item p {
            color: #666;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .content-section {
            display: none;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: slideInRight 0.6s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            color: #333;
            font-size: 2rem;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 300;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #fafafa;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .explanation {
            background: #f8f9ff;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            line-height: 1.8;
            color: #333;
        }

        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .back-btn:hover {
            background: white;
            transform: translateY(-2px);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .interactive-demo {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .input-group label {
            font-weight: 500;
            color: #333;
            min-width: 60px;
        }

        .input-group input {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .result-display {
            background: #f0f8ff;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-size: 1.1rem;
            text-align: center;
            color: #333;
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="showMenu()" style="display: none;">← 返回目录</button>
    
    <div class="container">
        <div class="header">
            <h1>函数的表示方法</h1>
            <p>零基础交互式学习 - 用动画理解数学</p>
        </div>

        <!-- 主菜单 -->
        <div id="main-menu" class="nav-menu">
            <div class="nav-item" onclick="showSection('method1')">
                <h3>01. 函数的表示方法</h3>
                <p>学习函数的三种基本表示方法：解析式、图像法、列表法。通过动画演示每种方法的特点和应用场景。</p>
            </div>
            
            <div class="nav-item" onclick="showSection('method2')">
                <h3>02. 分段函数</h3>
                <p>理解分段函数的概念，学会如何定义和表示在不同区间有不同规则的函数。</p>
            </div>
            
            <div class="nav-item" onclick="showSection('method3')">
                <h3>03. 分段函数求值</h3>
                <p>掌握分段函数的求值方法，学会根据自变量的取值范围选择对应的函数表达式。</p>
            </div>
            
            <div class="nav-item" onclick="showSection('method4')">
                <h3>04. 分段函数的值域</h3>
                <p>学习如何求分段函数的值域，理解各段函数值域的合并方法。</p>
            </div>
            
            <div class="nav-item" onclick="showSection('method5')">
                <h3>05. 具体函数图象的平移</h3>
                <p>通过动画演示具体函数图象的上下左右平移规律，掌握平移变换的规则。</p>
            </div>
            
            <div class="nav-item" onclick="showSection('method6')">
                <h3>06. 抽象函数图象的平移</h3>
                <p>学习抽象函数图象的平移方法，理解函数变换的一般规律。</p>
            </div>
            
            <div class="nav-item" onclick="showSection('method7')">
                <h3>07. 函数图象关于x轴对称</h3>
                <p>探索函数图象的对称变换，学习如何通过改变函数表达式实现图象对称。</p>
            </div>
        </div>

        <!-- 内容区域 -->
        <!-- 01. 函数的表示方法 -->
        <div id="method1" class="content-section">
            <h2 class="section-title">函数的表示方法</h2>
            <div class="explanation">
                <h4>函数有三种主要表示方法：</h4>
                <p><strong>1. 解析式法：</strong>用数学表达式表示函数关系，如 f(x) = 2x + 1</p>
                <p><strong>2. 图像法：</strong>用坐标平面上的图形表示函数关系</p>
                <p><strong>3. 列表法：</strong>用表格形式列出自变量和因变量的对应关系</p>
            </div>

            <div class="interactive-demo">
                <h4>交互演示：三种表示方法</h4>
                <div class="controls">
                    <button class="btn" onclick="showAnalytic()">解析式法</button>
                    <button class="btn" onclick="showGraph()">图像法</button>
                    <button class="btn" onclick="showTable()">列表法</button>
                </div>
                <div class="canvas-container">
                    <canvas id="methodCanvas" width="600" height="400"></canvas>
                </div>
                <div id="methodResult" class="result-display">点击按钮查看不同的函数表示方法</div>
            </div>
        </div>

        <!-- 02. 分段函数 -->
        <div id="method2" class="content-section">
            <h2 class="section-title">分段函数</h2>
            <div class="explanation">
                <h4>什么是分段函数？</h4>
                <p>分段函数是在定义域的不同区间上有不同对应规则的函数。</p>
                <p>例如：f(x) = { x+1 (x≤0), x² (x>0) }</p>
            </div>

            <div class="interactive-demo">
                <h4>交互演示：构建分段函数</h4>
                <div class="input-group">
                    <label>区间1:</label>
                    <input type="text" id="segment1" placeholder="x ≤ 0" readonly>
                    <label>规则1:</label>
                    <input type="text" id="rule1" placeholder="x + 1" value="x + 1">
                </div>
                <div class="input-group">
                    <label>区间2:</label>
                    <input type="text" id="segment2" placeholder="x > 0" readonly>
                    <label>规则2:</label>
                    <input type="text" id="rule2" placeholder="x²" value="x*x">
                </div>
                <div class="controls">
                    <button class="btn" onclick="drawPiecewise()">绘制分段函数</button>
                    <button class="btn" onclick="animatePiecewise()">动画演示</button>
                </div>
                <div class="canvas-container">
                    <canvas id="piecewiseCanvas" width="600" height="400"></canvas>
                </div>
            </div>
        </div>

        <!-- 03. 分段函数求值 -->
        <div id="method3" class="content-section">
            <h2 class="section-title">分段函数求值</h2>
            <div class="explanation">
                <h4>如何计算分段函数的值？</h4>
                <p><strong>步骤1：</strong>确定自变量x属于哪个区间</p>
                <p><strong>步骤2：</strong>选择对应区间的函数表达式</p>
                <p><strong>步骤3：</strong>将x值代入表达式计算结果</p>
            </div>

            <div class="interactive-demo">
                <h4>交互练习：分段函数求值</h4>
                <div class="input-group">
                    <label>输入x值:</label>
                    <input type="number" id="xValue" placeholder="请输入x值" step="0.1">
                    <button class="btn" onclick="calculatePiecewise()">计算f(x)</button>
                </div>
                <div id="calculationSteps" class="result-display">请输入x值进行计算</div>
                <div class="canvas-container">
                    <canvas id="evaluationCanvas" width="600" height="400"></canvas>
                </div>
            </div>
        </div>

        <!-- 04. 分段函数的值域 -->
        <div id="method4" class="content-section">
            <h2 class="section-title">分段函数的值域</h2>
            <div class="explanation">
                <h4>如何求分段函数的值域？</h4>
                <p><strong>方法：</strong>分别求出各段函数在对应区间上的值域，然后求并集</p>
                <p>例如：f(x) = { x+1 (x≤0), x² (x>0) }</p>
                <p>区间1值域：(-∞, 1]，区间2值域：(0, +∞)</p>
                <p>总值域：(-∞, 1] ∪ (0, +∞)</p>
            </div>

            <div class="interactive-demo">
                <h4>可视化演示：值域分析</h4>
                <div class="controls">
                    <button class="btn" onclick="showRange1()">显示区间1值域</button>
                    <button class="btn" onclick="showRange2()">显示区间2值域</button>
                    <button class="btn" onclick="showTotalRange()">显示总值域</button>
                    <button class="btn" onclick="animateRange()">动画演示</button>
                </div>
                <div class="canvas-container">
                    <canvas id="rangeCanvas" width="600" height="400"></canvas>
                </div>
                <div id="rangeResult" class="result-display">点击按钮查看值域分析</div>
            </div>
        </div>

        <!-- 05. 具体函数图象的平移 -->
        <div id="method5" class="content-section">
            <h2 class="section-title">具体函数图象的平移</h2>
            <div class="explanation">
                <h4>函数图象平移规律：</h4>
                <p><strong>左右平移：</strong>f(x+a) 向左移a个单位，f(x-a) 向右移a个单位</p>
                <p><strong>上下平移：</strong>f(x)+b 向上移b个单位，f(x)-b 向下移b个单位</p>
                <p><strong>记忆口诀：</strong>"左加右减，上加下减"</p>
            </div>

            <div class="interactive-demo">
                <h4>交互演示：函数平移</h4>
                <div class="input-group">
                    <label>水平平移:</label>
                    <input type="range" id="hShift" min="-5" max="5" value="0" step="0.5">
                    <span id="hValue">0</span>
                </div>
                <div class="input-group">
                    <label>垂直平移:</label>
                    <input type="range" id="vShift" min="-5" max="5" value="0" step="0.5">
                    <span id="vValue">0</span>
                </div>
                <div class="controls">
                    <button class="btn" onclick="resetTransform()">重置</button>
                    <button class="btn" onclick="animateTransform()">动画演示</button>
                </div>
                <div class="canvas-container">
                    <canvas id="transformCanvas" width="600" height="400"></canvas>
                </div>
                <div id="transformFormula" class="result-display">当前函数：f(x) = x²</div>
            </div>
        </div>

        <!-- 06. 抽象函数图象的平移 -->
        <div id="method6" class="content-section">
            <h2 class="section-title">抽象函数图象的平移</h2>
            <div class="explanation">
                <h4>抽象函数平移的理解：</h4>
                <p>对于抽象函数f(x)，平移规律同样适用：</p>
                <p><strong>f(x+a)：</strong>图象向左平移a个单位</p>
                <p><strong>f(x-a)：</strong>图象向右平移a个单位</p>
                <p><strong>f(x)+b：</strong>图象向上平移b个单位</p>
                <p><strong>f(x)-b：</strong>图象向下平移b个单位</p>
            </div>

            <div class="interactive-demo">
                <h4>抽象函数平移演示</h4>
                <div class="controls">
                    <button class="btn" onclick="showAbstractOriginal()">原函数f(x)</button>
                    <button class="btn" onclick="showAbstractLeft()">f(x+2)</button>
                    <button class="btn" onclick="showAbstractRight()">f(x-2)</button>
                    <button class="btn" onclick="showAbstractUp()">f(x)+3</button>
                    <button class="btn" onclick="showAbstractDown()">f(x)-3</button>
                </div>
                <div class="canvas-container">
                    <canvas id="abstractCanvas" width="600" height="400"></canvas>
                </div>
                <div id="abstractResult" class="result-display">点击按钮查看不同的平移效果</div>
            </div>
        </div>

        <!-- 07. 函数图象关于x轴对称 -->
        <div id="method7" class="content-section">
            <h2 class="section-title">函数图象关于x轴对称</h2>
            <div class="explanation">
                <h4>图象对称变换：</h4>
                <p><strong>关于x轴对称：</strong>y = f(x) → y = -f(x)</p>
                <p><strong>关于y轴对称：</strong>y = f(x) → y = f(-x)</p>
                <p><strong>关于原点对称：</strong>y = f(x) → y = -f(-x)</p>
                <p>对称变换改变了函数图象的位置，但保持了图象的形状。</p>
            </div>

            <div class="interactive-demo">
                <h4>对称变换演示</h4>
                <div class="controls">
                    <button class="btn" onclick="showOriginalSymmetry()">原函数</button>
                    <button class="btn" onclick="showXAxisSymmetry()">关于x轴对称</button>
                    <button class="btn" onclick="showYAxisSymmetry()">关于y轴对称</button>
                    <button class="btn" onclick="showOriginSymmetry()">关于原点对称</button>
                    <button class="btn" onclick="animateSymmetry()">动画演示</button>
                </div>
                <div class="canvas-container">
                    <canvas id="symmetryCanvas" width="600" height="400"></canvas>
                </div>
                <div id="symmetryResult" class="result-display">点击按钮查看不同的对称变换</div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = '';
        let animationId = null;

        // 显示指定章节
        function showSection(sectionId) {
            // 隐藏主菜单
            document.getElementById('main-menu').style.display = 'none';
            document.querySelector('.back-btn').style.display = 'block';

            // 隐藏所有内容区域
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 显示指定区域
            document.getElementById(sectionId).classList.add('active');
            currentSection = sectionId;

            // 初始化对应的画布
            initializeCanvas(sectionId);
        }

        // 返回主菜单
        function showMenu() {
            document.getElementById('main-menu').style.display = 'grid';
            document.querySelector('.back-btn').style.display = 'none';

            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 停止所有动画
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
        }

        // 初始化画布
        function initializeCanvas(sectionId) {
            switch(sectionId) {
                case 'method1':
                    initMethodCanvas();
                    break;
                case 'method2':
                    initPiecewiseCanvas();
                    break;
                case 'method3':
                    initEvaluationCanvas();
                    break;
                case 'method4':
                    initRangeCanvas();
                    break;
                case 'method5':
                    initTransformCanvas();
                    setupTransformControls();
                    break;
                case 'method6':
                    initAbstractCanvas();
                    break;
                case 'method7':
                    initSymmetryCanvas();
                    break;
            }
        }

        // 绘制坐标系
        function drawCoordinateSystem(ctx, width, height, scale = 20) {
            const centerX = width / 2;
            const centerY = height / 2;

            ctx.clearRect(0, 0, width, height);
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 1;

            // 绘制网格
            for (let i = 0; i <= width; i += scale) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, height);
                ctx.stroke();
            }

            for (let i = 0; i <= height; i += scale) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(width, i);
                ctx.stroke();
            }

            // 绘制坐标轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;

            // x轴
            ctx.beginPath();
            ctx.moveTo(0, centerY);
            ctx.lineTo(width, centerY);
            ctx.stroke();

            // y轴
            ctx.beginPath();
            ctx.moveTo(centerX, 0);
            ctx.lineTo(centerX, height);
            ctx.stroke();

            // 绘制箭头
            ctx.fillStyle = '#333';
            // x轴箭头
            ctx.beginPath();
            ctx.moveTo(width - 10, centerY - 5);
            ctx.lineTo(width, centerY);
            ctx.lineTo(width - 10, centerY + 5);
            ctx.fill();

            // y轴箭头
            ctx.beginPath();
            ctx.moveTo(centerX - 5, 10);
            ctx.lineTo(centerX, 0);
            ctx.lineTo(centerX + 5, 10);
            ctx.fill();

            // 标记原点
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.fillText('O', centerX + 5, centerY + 15);
            ctx.fillText('x', width - 15, centerY + 15);
            ctx.fillText('y', centerX + 5, 15);
        }

        // 01. 函数表示方法相关函数
        function initMethodCanvas() {
            const canvas = document.getElementById('methodCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);
        }

        function showAnalytic() {
            const canvas = document.getElementById('methodCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            // 显示解析式
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = 2x + 1', canvas.width/2, 50);

            // 绘制直线
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            for (let x = -10; x <= 10; x += 0.1) {
                const y = 2 * x + 1;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === -10) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();

            document.getElementById('methodResult').innerHTML =
                '<strong>解析式法：</strong>用数学表达式 f(x) = 2x + 1 表示函数关系';
        }

        function showGraph() {
            const canvas = document.getElementById('methodCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            // 绘制抛物线
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            for (let x = -8; x <= 8; x += 0.1) {
                const y = 0.1 * x * x - 2;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === -8) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();

            // 标题
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('图像法表示', canvas.width/2, 50);

            document.getElementById('methodResult').innerHTML =
                '<strong>图像法：</strong>用坐标平面上的曲线图形表示函数关系';
        }

        function showTable() {
            const canvas = document.getElementById('methodCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制表格
            ctx.fillStyle = '#2ecc71';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('列表法表示', canvas.width/2, 40);

            // 表格数据
            const tableData = [
                ['x', '-2', '-1', '0', '1', '2', '3'],
                ['f(x)', '-3', '-1', '1', '3', '5', '7']
            ];

            const startX = 100;
            const startY = 80;
            const cellWidth = 70;
            const cellHeight = 40;

            ctx.strokeStyle = '#2ecc71';
            ctx.lineWidth = 2;
            ctx.fillStyle = '#2ecc71';
            ctx.font = '16px Arial';

            for (let row = 0; row < tableData.length; row++) {
                for (let col = 0; col < tableData[row].length; col++) {
                    const x = startX + col * cellWidth;
                    const y = startY + row * cellHeight;

                    // 绘制单元格边框
                    ctx.strokeRect(x, y, cellWidth, cellHeight);

                    // 填充文字
                    ctx.fillText(tableData[row][col], x + cellWidth/2, y + cellHeight/2 + 5);
                }
            }

            document.getElementById('methodResult').innerHTML =
                '<strong>列表法：</strong>用表格形式列出自变量x和因变量f(x)的对应关系';
        }

        // 02. 分段函数相关函数
        function initPiecewiseCanvas() {
            const canvas = document.getElementById('piecewiseCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);
            drawPiecewise();
        }

        function drawPiecewise() {
            const canvas = document.getElementById('piecewiseCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            // 绘制第一段：x ≤ 0, f(x) = x + 1
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = -10; x <= 0; x += 0.1) {
                const y = x + 1;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === -10) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();

            // 绘制第二段：x > 0, f(x) = x²
            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = 0.1; x <= 5; x += 0.1) {
                const y = x * x;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === 0.1) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();

            // 标记分界点
            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.arc(centerX, centerY - scale, 5, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = '#3498db';
            ctx.beginPath();
            ctx.arc(centerX + 0.1 * scale, centerY - 0.01 * scale, 3, 0, 2 * Math.PI);
            ctx.fill();

            // 添加函数表达式
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('f(x) = x + 1 (x ≤ 0)', 20, 30);
            ctx.fillText('f(x) = x² (x > 0)', 20, 50);
        }

        function animatePiecewise() {
            let step = 0;
            const maxSteps = 100;

            function animate() {
                const canvas = document.getElementById('piecewiseCanvas');
                const ctx = canvas.getContext('2d');
                drawCoordinateSystem(ctx, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const scale = 20;
                const progress = step / maxSteps;

                // 动画绘制第一段
                if (progress <= 0.5) {
                    const endX = -10 + (10 * progress * 2);
                    ctx.strokeStyle = '#e74c3c';
                    ctx.lineWidth = 3;
                    ctx.beginPath();

                    for (let x = -10; x <= endX; x += 0.1) {
                        const y = x + 1;
                        const pixelX = centerX + x * scale;
                        const pixelY = centerY - y * scale;

                        if (x === -10) {
                            ctx.moveTo(pixelX, pixelY);
                        } else {
                            ctx.lineTo(pixelX, pixelY);
                        }
                    }
                    ctx.stroke();
                } else {
                    // 绘制完整的第一段
                    ctx.strokeStyle = '#e74c3c';
                    ctx.lineWidth = 3;
                    ctx.beginPath();

                    for (let x = -10; x <= 0; x += 0.1) {
                        const y = x + 1;
                        const pixelX = centerX + x * scale;
                        const pixelY = centerY - y * scale;

                        if (x === -10) {
                            ctx.moveTo(pixelX, pixelY);
                        } else {
                            ctx.lineTo(pixelX, pixelY);
                        }
                    }
                    ctx.stroke();

                    // 动画绘制第二段
                    const endX2 = 0.1 + (4.9 * (progress - 0.5) * 2);
                    ctx.strokeStyle = '#3498db';
                    ctx.lineWidth = 3;
                    ctx.beginPath();

                    for (let x = 0.1; x <= endX2; x += 0.1) {
                        const y = x * x;
                        const pixelX = centerX + x * scale;
                        const pixelY = centerY - y * scale;

                        if (x === 0.1) {
                            ctx.moveTo(pixelX, pixelY);
                        } else {
                            ctx.lineTo(pixelX, pixelY);
                        }
                    }
                    ctx.stroke();
                }

                step++;
                if (step <= maxSteps) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    drawPiecewise();
                }
            }

            animate();
        }

        // 03. 分段函数求值相关函数
        function initEvaluationCanvas() {
            const canvas = document.getElementById('evaluationCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);
            drawPiecewiseForEvaluation();
        }

        function drawPiecewiseForEvaluation() {
            const canvas = document.getElementById('evaluationCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            // 绘制分段函数
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = -10; x <= 0; x += 0.1) {
                const y = x + 1;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === -10) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();

            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = 0.1; x <= 5; x += 0.1) {
                const y = x * x;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === 0.1) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();
        }

        function calculatePiecewise() {
            const xValue = parseFloat(document.getElementById('xValue').value);

            if (isNaN(xValue)) {
                document.getElementById('calculationSteps').innerHTML = '请输入有效的数值';
                return;
            }

            let result, steps, color;

            if (xValue <= 0) {
                result = xValue + 1;
                steps = `
                    <strong>计算步骤：</strong><br>
                    1. 判断：x = ${xValue} ≤ 0，使用第一段函数<br>
                    2. 选择：f(x) = x + 1<br>
                    3. 计算：f(${xValue}) = ${xValue} + 1 = ${result}
                `;
                color = '#e74c3c';
            } else {
                result = xValue * xValue;
                steps = `
                    <strong>计算步骤：</strong><br>
                    1. 判断：x = ${xValue} > 0，使用第二段函数<br>
                    2. 选择：f(x) = x²<br>
                    3. 计算：f(${xValue}) = ${xValue}² = ${result}
                `;
                color = '#3498db';
            }

            document.getElementById('calculationSteps').innerHTML = steps;

            // 在图上标记点
            const canvas = document.getElementById('evaluationCanvas');
            const ctx = canvas.getContext('2d');
            drawPiecewiseForEvaluation();

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            const pixelX = centerX + xValue * scale;
            const pixelY = centerY - result * scale;

            // 绘制垂直线
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(pixelX, centerY);
            ctx.lineTo(pixelX, pixelY);
            ctx.stroke();

            // 绘制水平线
            ctx.beginPath();
            ctx.moveTo(centerX, pixelY);
            ctx.lineTo(pixelX, pixelY);
            ctx.stroke();

            ctx.setLineDash([]);

            // 标记点
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(pixelX, pixelY, 6, 0, 2 * Math.PI);
            ctx.fill();

            // 标记坐标
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText(`(${xValue}, ${result})`, pixelX + 10, pixelY - 10);
        }

        // 04. 分段函数值域相关函数
        function initRangeCanvas() {
            const canvas = document.getElementById('rangeCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);
            drawPiecewiseForRange();
        }

        function drawPiecewiseForRange() {
            const canvas = document.getElementById('rangeCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            // 绘制分段函数
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = -10; x <= 0; x += 0.1) {
                const y = x + 1;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === -10) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();

            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = 0.1; x <= 5; x += 0.1) {
                const y = x * x;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === 0.1) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();
        }

        function showRange1() {
            const canvas = document.getElementById('rangeCanvas');
            const ctx = canvas.getContext('2d');
            drawPiecewiseForRange();

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            // 高亮第一段的值域
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 5;
            ctx.beginPath();
            ctx.moveTo(10, centerY - (-9) * scale); // y = -9
            ctx.lineTo(10, centerY - 1 * scale); // y = 1
            ctx.stroke();

            // 添加箭头表示延伸到负无穷
            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.moveTo(5, centerY - (-9) * scale - 10);
            ctx.lineTo(10, centerY - (-9) * scale);
            ctx.lineTo(15, centerY - (-9) * scale - 10);
            ctx.fill();

            document.getElementById('rangeResult').innerHTML =
                '<strong>区间1值域：</strong>当 x ≤ 0 时，f(x) = x + 1，值域为 (-∞, 1]';
        }

        function showRange2() {
            const canvas = document.getElementById('rangeCanvas');
            const ctx = canvas.getContext('2d');
            drawPiecewiseForRange();

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            // 高亮第二段的值域
            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 5;
            ctx.beginPath();
            ctx.moveTo(30, centerY - 0.01 * scale); // y = 0.01
            ctx.lineTo(30, centerY - 25 * scale); // y = 25
            ctx.stroke();

            // 添加箭头表示延伸到正无穷
            ctx.fillStyle = '#3498db';
            ctx.beginPath();
            ctx.moveTo(25, centerY - 25 * scale + 10);
            ctx.lineTo(30, centerY - 25 * scale);
            ctx.lineTo(35, centerY - 25 * scale + 10);
            ctx.fill();

            document.getElementById('rangeResult').innerHTML =
                '<strong>区间2值域：</strong>当 x > 0 时，f(x) = x²，值域为 (0, +∞)';
        }

        function showTotalRange() {
            const canvas = document.getElementById('rangeCanvas');
            const ctx = canvas.getContext('2d');
            drawPiecewiseForRange();

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            // 显示总值域
            ctx.strokeStyle = '#9b59b6';
            ctx.lineWidth = 5;

            // 第一部分：(-∞, 1]
            ctx.beginPath();
            ctx.moveTo(10, centerY - (-9) * scale);
            ctx.lineTo(10, centerY - 1 * scale);
            ctx.stroke();

            // 第二部分：(0, +∞)
            ctx.beginPath();
            ctx.moveTo(30, centerY - 0.01 * scale);
            ctx.lineTo(30, centerY - 25 * scale);
            ctx.stroke();

            document.getElementById('rangeResult').innerHTML =
                '<strong>总值域：</strong>(-∞, 1] ∪ (0, +∞)，注意：0和1之间的值不在值域内';
        }

        function animateRange() {
            let step = 0;
            const steps = ['range1', 'range2', 'total'];

            function animate() {
                switch(steps[step % 3]) {
                    case 'range1':
                        showRange1();
                        break;
                    case 'range2':
                        showRange2();
                        break;
                    case 'total':
                        showTotalRange();
                        break;
                }

                step++;
                if (step < 9) { // 每个状态显示3次
                    setTimeout(() => animationId = requestAnimationFrame(animate), 2000);
                }
            }

            animate();
        }

        // 05. 函数平移相关函数
        function initTransformCanvas() {
            const canvas = document.getElementById('transformCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);
            drawParabola(0, 0);
        }

        function setupTransformControls() {
            const hShift = document.getElementById('hShift');
            const vShift = document.getElementById('vShift');
            const hValue = document.getElementById('hValue');
            const vValue = document.getElementById('vValue');

            hShift.addEventListener('input', function() {
                hValue.textContent = this.value;
                updateTransform();
            });

            vShift.addEventListener('input', function() {
                vValue.textContent = this.value;
                updateTransform();
            });
        }

        function drawParabola(hShift, vShift) {
            const canvas = document.getElementById('transformCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const scale = 20;

            // 绘制原函数（淡色）
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = -8; x <= 8; x += 0.1) {
                const y = 0.1 * x * x;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === -8) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();

            // 绘制变换后的函数
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = -8; x <= 8; x += 0.1) {
                const y = 0.1 * (x - hShift) * (x - hShift) + vShift;
                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === -8) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();
        }

        function updateTransform() {
            const hShift = parseFloat(document.getElementById('hShift').value);
            const vShift = parseFloat(document.getElementById('vShift').value);

            drawParabola(hShift, vShift);

            let formula = 'f(x) = ';
            if (hShift === 0 && vShift === 0) {
                formula += 'x²';
            } else {
                formula += '(x';
                if (hShift > 0) formula += ` - ${hShift}`;
                else if (hShift < 0) formula += ` + ${Math.abs(hShift)}`;
                formula += ')²';
                if (vShift > 0) formula += ` + ${vShift}`;
                else if (vShift < 0) formula += ` - ${Math.abs(vShift)}`;
            }

            document.getElementById('transformFormula').innerHTML = `当前函数：${formula}`;
        }

        function resetTransform() {
            document.getElementById('hShift').value = 0;
            document.getElementById('vShift').value = 0;
            document.getElementById('hValue').textContent = '0';
            document.getElementById('vValue').textContent = '0';
            updateTransform();
        }

        function animateTransform() {
            let step = 0;
            const maxSteps = 200;

            function animate() {
                const progress = step / maxSteps;
                const hShift = 3 * Math.sin(progress * 4 * Math.PI);
                const vShift = 2 * Math.cos(progress * 4 * Math.PI);

                document.getElementById('hShift').value = hShift.toFixed(1);
                document.getElementById('vShift').value = vShift.toFixed(1);
                document.getElementById('hValue').textContent = hShift.toFixed(1);
                document.getElementById('vValue').textContent = vShift.toFixed(1);

                drawParabola(hShift, vShift);

                let formula = 'f(x) = ';
                if (Math.abs(hShift) < 0.1 && Math.abs(vShift) < 0.1) {
                    formula += 'x²';
                } else {
                    formula += '(x';
                    if (hShift > 0.1) formula += ` - ${hShift.toFixed(1)}`;
                    else if (hShift < -0.1) formula += ` + ${Math.abs(hShift).toFixed(1)}`;
                    formula += ')²';
                    if (vShift > 0.1) formula += ` + ${vShift.toFixed(1)}`;
                    else if (vShift < -0.1) formula += ` - ${Math.abs(vShift).toFixed(1)}`;
                }

                document.getElementById('transformFormula').innerHTML = `当前函数：${formula}`;

                step++;
                if (step <= maxSteps) {
                    animationId = requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 06. 抽象函数平移相关函数
        function initAbstractCanvas() {
            const canvas = document.getElementById('abstractCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);
            showAbstractOriginal();
        }

        function drawAbstractFunction(ctx, hShift = 0, vShift = 0, color = '#667eea') {
            const centerX = ctx.canvas.width / 2;
            const centerY = ctx.canvas.height / 2;
            const scale = 20;

            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();

            // 绘制一个抽象的波浪形函数
            for (let x = -10; x <= 10; x += 0.1) {
                const y = Math.sin(x * 0.5) * 2 + Math.cos(x * 0.3) * 1.5 + vShift;
                const pixelX = centerX + (x + hShift) * scale;
                const pixelY = centerY - y * scale;

                if (x === -10) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();
        }

        function showAbstractOriginal() {
            const canvas = document.getElementById('abstractCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);
            drawAbstractFunction(ctx, 0, 0, '#667eea');

            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x)', canvas.width/2, 40);

            document.getElementById('abstractResult').innerHTML =
                '<strong>原函数：</strong>f(x) - 这是我们的基础抽象函数';
        }

        function showAbstractLeft() {
            const canvas = document.getElementById('abstractCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            // 显示原函数（淡色）
            drawAbstractFunction(ctx, 0, 0, '#ddd');
            // 显示平移后的函数
            drawAbstractFunction(ctx, -2, 0, '#e74c3c');

            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x+2)', canvas.width/2, 40);

            document.getElementById('abstractResult').innerHTML =
                '<strong>左移：</strong>f(x+2) - 图象向左平移2个单位';
        }

        function showAbstractRight() {
            const canvas = document.getElementById('abstractCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            drawAbstractFunction(ctx, 0, 0, '#ddd');
            drawAbstractFunction(ctx, 2, 0, '#2ecc71');

            ctx.fillStyle = '#2ecc71';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x-2)', canvas.width/2, 40);

            document.getElementById('abstractResult').innerHTML =
                '<strong>右移：</strong>f(x-2) - 图象向右平移2个单位';
        }

        function showAbstractUp() {
            const canvas = document.getElementById('abstractCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            drawAbstractFunction(ctx, 0, 0, '#ddd');
            drawAbstractFunction(ctx, 0, 3, '#f39c12');

            ctx.fillStyle = '#f39c12';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x)+3', canvas.width/2, 40);

            document.getElementById('abstractResult').innerHTML =
                '<strong>上移：</strong>f(x)+3 - 图象向上平移3个单位';
        }

        function showAbstractDown() {
            const canvas = document.getElementById('abstractCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            drawAbstractFunction(ctx, 0, 0, '#ddd');
            drawAbstractFunction(ctx, 0, -3, '#9b59b6');

            ctx.fillStyle = '#9b59b6';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x)-3', canvas.width/2, 40);

            document.getElementById('abstractResult').innerHTML =
                '<strong>下移：</strong>f(x)-3 - 图象向下平移3个单位';
        }

        // 07. 对称变换相关函数
        function initSymmetryCanvas() {
            const canvas = document.getElementById('symmetryCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);
            showOriginalSymmetry();
        }

        function drawSymmetryFunction(ctx, type = 'original', color = '#667eea') {
            const centerX = ctx.canvas.width / 2;
            const centerY = ctx.canvas.height / 2;
            const scale = 20;

            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = -6; x <= 6; x += 0.1) {
                let y;
                switch(type) {
                    case 'original':
                        y = 0.1 * x * x * x - x; // 原函数
                        break;
                    case 'xAxis':
                        y = -(0.1 * x * x * x - x); // 关于x轴对称
                        break;
                    case 'yAxis':
                        y = 0.1 * (-x) * (-x) * (-x) - (-x); // 关于y轴对称
                        break;
                    case 'origin':
                        y = -(0.1 * (-x) * (-x) * (-x) - (-x)); // 关于原点对称
                        break;
                }

                const pixelX = centerX + x * scale;
                const pixelY = centerY - y * scale;

                if (x === -6) {
                    ctx.moveTo(pixelX, pixelY);
                } else {
                    ctx.lineTo(pixelX, pixelY);
                }
            }
            ctx.stroke();
        }

        function showOriginalSymmetry() {
            const canvas = document.getElementById('symmetryCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);
            drawSymmetryFunction(ctx, 'original', '#667eea');

            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('f(x) = 0.1x³ - x', canvas.width/2, 40);

            document.getElementById('symmetryResult').innerHTML =
                '<strong>原函数：</strong>f(x) = 0.1x³ - x';
        }

        function showXAxisSymmetry() {
            const canvas = document.getElementById('symmetryCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            drawSymmetryFunction(ctx, 'original', '#ddd');
            drawSymmetryFunction(ctx, 'xAxis', '#e74c3c');

            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('y = -f(x)', canvas.width/2, 40);

            document.getElementById('symmetryResult').innerHTML =
                '<strong>关于x轴对称：</strong>y = -f(x) = -(0.1x³ - x) = -0.1x³ + x';
        }

        function showYAxisSymmetry() {
            const canvas = document.getElementById('symmetryCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            drawSymmetryFunction(ctx, 'original', '#ddd');
            drawSymmetryFunction(ctx, 'yAxis', '#2ecc71');

            ctx.fillStyle = '#2ecc71';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('y = f(-x)', canvas.width/2, 40);

            document.getElementById('symmetryResult').innerHTML =
                '<strong>关于y轴对称：</strong>y = f(-x) = 0.1(-x)³ - (-x) = -0.1x³ + x';
        }

        function showOriginSymmetry() {
            const canvas = document.getElementById('symmetryCanvas');
            const ctx = canvas.getContext('2d');
            drawCoordinateSystem(ctx, canvas.width, canvas.height);

            drawSymmetryFunction(ctx, 'original', '#ddd');
            drawSymmetryFunction(ctx, 'origin', '#9b59b6');

            ctx.fillStyle = '#9b59b6';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('y = -f(-x)', canvas.width/2, 40);

            document.getElementById('symmetryResult').innerHTML =
                '<strong>关于原点对称：</strong>y = -f(-x) = -(0.1(-x)³ - (-x)) = 0.1x³ - x';
        }

        function animateSymmetry() {
            const functions = [
                showOriginalSymmetry,
                showXAxisSymmetry,
                showYAxisSymmetry,
                showOriginSymmetry
            ];

            let step = 0;

            function animate() {
                functions[step % 4]();
                step++;

                if (step < 12) { // 每个状态显示3次
                    setTimeout(() => animationId = requestAnimationFrame(animate), 2000);
                }
            }

            animate();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有导航项添加动画延迟
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>

</body>
</html>
