<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java集合类互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .demo-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 30px;
            margin: 30px 0;
        }

        .explanation {
            flex: 1;
            padding: 20px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .canvas-container {
            flex: 1;
            text-align: center;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            margin-top: 20px;
            text-align: center;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            color: #2d3436;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin: 5px;
            animation: pulse 2s infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        .comparison-table tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .game-score {
            text-align: center;
            font-size: 1.2rem;
            color: #333;
            margin: 20px 0;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Java集合类互动学习</h1>
            <p>通过动画和游戏，轻松掌握Java集合的核心概念</p>
        </div>

        <div class="section">
            <h2 class="section-title">ArrayList vs Vector 对比演示</h2>
            <div class="demo-container">
                <div class="explanation">
                    <h3>🚀 ArrayList（推荐使用）</h3>
                    <p><span class="highlight">非线程安全</span> - 速度更快</p>
                    <p><span class="highlight">动态数组</span> - 可自动扩容</p>
                    <p><span class="highlight">现代首选</span> - 特别适合Web应用</p>
                    
                    <h3>🔒 Vector（不推荐）</h3>
                    <p><span class="highlight">线程安全</span> - 有同步机制</p>
                    <p><span class="highlight">效率较低</span> - 因为同步开销</p>
                    <p><span class="highlight">已过时</span> - 现在很少使用</p>
                </div>
                <div class="canvas-container">
                    <canvas id="arrayListCanvas" width="400" height="300"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="addToArrayList()">添加元素</button>
                        <button class="btn" onclick="removeFromArrayList()">删除元素</button>
                        <button class="btn" onclick="resetArrayList()">重置</button>
                    </div>
                    <div class="game-score" id="arrayListScore">ArrayList 操作次数: 0</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Stack 堆栈演示 - 先进后出</h2>
            <div class="demo-container">
                <div class="explanation">
                    <h3>📚 Stack 堆栈特点</h3>
                    <p><span class="highlight">LIFO原则</span> - Last In, First Out</p>
                    <p><span class="highlight">先进后出</span> - 像叠盘子一样</p>
                    <p><span class="highlight">主要操作</span> - push(入栈) 和 pop(出栈)</p>
                    <p><span class="highlight">应用场景</span> - 函数调用、撤销操作等</p>
                </div>
                <div class="canvas-container">
                    <canvas id="stackCanvas" width="400" height="300"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="pushToStack()">Push 入栈</button>
                        <button class="btn" onclick="popFromStack()">Pop 出栈</button>
                        <button class="btn" onclick="resetStack()">重置</button>
                    </div>
                    <div class="game-score" id="stackScore">Stack 操作次数: 0</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">HashMap vs Hashtable 对比演示</h2>
            <div class="demo-container">
                <div class="explanation">
                    <h3>⚡ HashMap（推荐使用）</h3>
                    <p><span class="highlight">非线程安全</span> - 性能更好</p>
                    <p><span class="highlight">允许null值</span> - key和value都可以为null</p>
                    <p><span class="highlight">现代首选</span> - Web应用的最佳选择</p>

                    <h3>🔐 Hashtable（不推荐）</h3>
                    <p><span class="highlight">线程安全</span> - 有同步机制</p>
                    <p><span class="highlight">不允许null</span> - key和value都不能为null</p>
                    <p><span class="highlight">效率较低</span> - 因为同步开销</p>
                </div>
                <div class="canvas-container">
                    <canvas id="hashMapCanvas" width="400" height="300"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="addToHashMap()">添加键值对</button>
                        <button class="btn" onclick="removeFromHashMap()">删除键值对</button>
                        <button class="btn" onclick="resetHashMap()">重置</button>
                    </div>
                    <div class="game-score" id="hashMapScore">HashMap 操作次数: 0</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">Enumeration 枚举迭代器演示</h2>
            <div class="demo-container">
                <div class="explanation">
                    <h3>🔄 Enumeration 特点</h3>
                    <p><span class="highlight">迭代器接口</span> - 用于遍历集合</p>
                    <p><span class="highlight">只读访问</span> - 只能读取，不能修改</p>
                    <p><span class="highlight">两个方法</span> - hasMoreElements() 和 nextElement()</p>
                    <p><span class="highlight">传统方式</span> - 现在更多使用Iterator</p>
                </div>
                <div class="canvas-container">
                    <canvas id="enumerationCanvas" width="400" height="300"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="startEnumeration()">开始遍历</button>
                        <button class="btn" onclick="nextElement()">下一个元素</button>
                        <button class="btn" onclick="resetEnumeration()">重置</button>
                    </div>
                    <div class="game-score" id="enumerationScore">遍历进度: 0/5</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">性能对比总结</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>集合类型</th>
                        <th>线程安全</th>
                        <th>性能</th>
                        <th>推荐程度</th>
                        <th>适用场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>ArrayList</strong></td>
                        <td>❌ 非线程安全</td>
                        <td>⚡ 高性能</td>
                        <td>🌟🌟🌟🌟🌟 强烈推荐</td>
                        <td>Web前台页面、单线程环境</td>
                    </tr>
                    <tr>
                        <td><strong>Vector</strong></td>
                        <td>✅ 线程安全</td>
                        <td>🐌 性能较低</td>
                        <td>⭐ 不推荐</td>
                        <td>多线程环境（但有更好选择）</td>
                    </tr>
                    <tr>
                        <td><strong>HashMap</strong></td>
                        <td>❌ 非线程安全</td>
                        <td>⚡ 高性能</td>
                        <td>🌟🌟🌟🌟🌟 强烈推荐</td>
                        <td>Web应用、键值对存储</td>
                    </tr>
                    <tr>
                        <td><strong>Hashtable</strong></td>
                        <td>✅ 线程安全</td>
                        <td>🐌 性能较低</td>
                        <td>⭐ 不推荐</td>
                        <td>多线程环境（但有更好选择）</td>
                    </tr>
                    <tr>
                        <td><strong>Stack</strong></td>
                        <td>✅ 线程安全</td>
                        <td>🔄 中等</td>
                        <td>🌟🌟🌟 适度推荐</td>
                        <td>LIFO操作、函数调用栈</td>
                    </tr>
                </tbody>
            </table>

            <div class="explanation" style="margin-top: 30px;">
                <h3>💡 核心要点总结</h3>
                <p><span class="highlight">Web应用优先考虑性能</span> - 前台页面响应速度最重要</p>
                <p><span class="highlight">ArrayList和HashMap是首选</span> - 非线程安全但性能优秀</p>
                <p><span class="highlight">Vector和Hashtable已过时</span> - 虽然线程安全但效率低</p>
                <p><span class="highlight">现代替代方案</span> - 需要线程安全时使用ConcurrentHashMap等</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let arrayListData = [];
        let stackData = [];
        let hashMapData = new Map();
        let enumerationData = ['Java', 'Python', 'JavaScript', 'C++', 'Go'];
        let enumerationIndex = 0;

        let arrayListCount = 0;
        let stackCount = 0;
        let hashMapCount = 0;

        // 颜色配置
        const colors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#00b894',
            warning: '#fdcb6e',
            danger: '#e17055',
            light: '#ddd',
            white: '#fff'
        };

        // ArrayList 相关函数
        function addToArrayList() {
            const value = Math.floor(Math.random() * 100) + 1;
            arrayListData.push(value);
            arrayListCount++;
            document.getElementById('arrayListScore').textContent = `ArrayList 操作次数: ${arrayListCount}`;
            drawArrayList();

            // 添加动画效果
            const canvas = document.getElementById('arrayListCanvas');
            canvas.style.transform = 'scale(1.05)';
            setTimeout(() => {
                canvas.style.transform = 'scale(1)';
            }, 200);
        }

        function removeFromArrayList() {
            if (arrayListData.length > 0) {
                arrayListData.pop();
                arrayListCount++;
                document.getElementById('arrayListScore').textContent = `ArrayList 操作次数: ${arrayListCount}`;
                drawArrayList();
            }
        }

        function resetArrayList() {
            arrayListData = [];
            arrayListCount = 0;
            document.getElementById('arrayListScore').textContent = `ArrayList 操作次数: ${arrayListCount}`;
            drawArrayList();
        }

        function drawArrayList() {
            const canvas = document.getElementById('arrayListCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = colors.primary;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('ArrayList 动态数组演示', canvas.width / 2, 30);

            // 绘制数组元素
            const boxWidth = 40;
            const boxHeight = 40;
            const startX = 50;
            const startY = 80;
            const maxPerRow = 7;

            arrayListData.forEach((value, index) => {
                const row = Math.floor(index / maxPerRow);
                const col = index % maxPerRow;
                const x = startX + col * (boxWidth + 10);
                const y = startY + row * (boxHeight + 15);

                // 绘制方框
                ctx.fillStyle = colors.primary;
                ctx.fillRect(x, y, boxWidth, boxHeight);

                // 绘制边框
                ctx.strokeStyle = colors.secondary;
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, boxWidth, boxHeight);

                // 绘制数值
                ctx.fillStyle = colors.white;
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(value, x + boxWidth / 2, y + boxHeight / 2 + 5);

                // 绘制索引
                ctx.fillStyle = colors.secondary;
                ctx.font = '12px Arial';
                ctx.fillText(index, x + boxWidth / 2, y - 5);
            });

            // 绘制说明
            ctx.fillStyle = colors.secondary;
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`长度: ${arrayListData.length}`, 20, canvas.height - 40);
            ctx.fillText('特点: 快速访问、动态扩容', 20, canvas.height - 20);
        }

        // Stack 相关函数
        function pushToStack() {
            const value = Math.floor(Math.random() * 100) + 1;
            stackData.push(value);
            stackCount++;
            document.getElementById('stackScore').textContent = `Stack 操作次数: ${stackCount}`;
            drawStack();

            // 添加动画效果
            const canvas = document.getElementById('stackCanvas');
            canvas.style.transform = 'scale(1.05)';
            setTimeout(() => {
                canvas.style.transform = 'scale(1)';
            }, 200);
        }

        function popFromStack() {
            if (stackData.length > 0) {
                stackData.pop();
                stackCount++;
                document.getElementById('stackScore').textContent = `Stack 操作次数: ${stackCount}`;
                drawStack();
            }
        }

        function resetStack() {
            stackData = [];
            stackCount = 0;
            document.getElementById('stackScore').textContent = `Stack 操作次数: ${stackCount}`;
            drawStack();
        }

        function drawStack() {
            const canvas = document.getElementById('stackCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = colors.primary;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Stack 堆栈演示 (LIFO)', canvas.width / 2, 30);

            // 绘制栈底
            const stackX = canvas.width / 2 - 50;
            const stackBottom = canvas.height - 40;
            const boxWidth = 100;
            const boxHeight = 30;

            ctx.fillStyle = colors.light;
            ctx.fillRect(stackX - 10, stackBottom, boxWidth + 20, 10);
            ctx.fillStyle = colors.secondary;
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('栈底', stackX + boxWidth / 2, stackBottom + 25);

            // 绘制栈元素
            stackData.forEach((value, index) => {
                const y = stackBottom - (index + 1) * (boxHeight + 5);

                // 绘制方框
                ctx.fillStyle = index === stackData.length - 1 ? colors.warning : colors.primary;
                ctx.fillRect(stackX, y, boxWidth, boxHeight);

                // 绘制边框
                ctx.strokeStyle = colors.secondary;
                ctx.lineWidth = 2;
                ctx.strokeRect(stackX, y, boxWidth, boxHeight);

                // 绘制数值
                ctx.fillStyle = colors.white;
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(value, stackX + boxWidth / 2, y + boxHeight / 2 + 5);

                // 标记栈顶
                if (index === stackData.length - 1) {
                    ctx.fillStyle = colors.warning;
                    ctx.font = '12px Arial';
                    ctx.fillText('← 栈顶 (下次出栈)', stackX + boxWidth + 15, y + boxHeight / 2 + 5);
                }
            });

            // 绘制箭头指示
            if (stackData.length > 0) {
                const arrowY = stackBottom - stackData.length * (boxHeight + 5) - 20;
                ctx.fillStyle = colors.success;
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Push ↓', stackX - 30, arrowY);
                ctx.fillText('Pop ↑', stackX + boxWidth + 30, arrowY);
            }

            // 绘制说明
            ctx.fillStyle = colors.secondary;
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`栈大小: ${stackData.length}`, 20, 60);
            ctx.fillText('原则: 先进后出 (LIFO)', 20, 80);
        }

        // HashMap 相关函数
        function addToHashMap() {
            const keys = ['name', 'age', 'city', 'job', 'hobby', 'language', 'skill'];
            const values = ['张三', '25', '北京', '程序员', '编程', 'Java', 'Spring'];

            if (hashMapData.size < keys.length) {
                const key = keys[hashMapData.size];
                const value = values[hashMapData.size];
                hashMapData.set(key, value);
                hashMapCount++;
                document.getElementById('hashMapScore').textContent = `HashMap 操作次数: ${hashMapCount}`;
                drawHashMap();

                // 添加动画效果
                const canvas = document.getElementById('hashMapCanvas');
                canvas.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    canvas.style.transform = 'scale(1)';
                }, 200);
            }
        }

        function removeFromHashMap() {
            if (hashMapData.size > 0) {
                const keys = Array.from(hashMapData.keys());
                const lastKey = keys[keys.length - 1];
                hashMapData.delete(lastKey);
                hashMapCount++;
                document.getElementById('hashMapScore').textContent = `HashMap 操作次数: ${hashMapCount}`;
                drawHashMap();
            }
        }

        function resetHashMap() {
            hashMapData.clear();
            hashMapCount = 0;
            document.getElementById('hashMapScore').textContent = `HashMap 操作次数: ${hashMapCount}`;
            drawHashMap();
        }

        function drawHashMap() {
            const canvas = document.getElementById('hashMapCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = colors.primary;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('HashMap 键值对演示', canvas.width / 2, 30);

            // 绘制键值对
            const startY = 60;
            const lineHeight = 35;
            let index = 0;

            hashMapData.forEach((value, key) => {
                const y = startY + index * lineHeight;

                // 绘制键的方框
                ctx.fillStyle = colors.primary;
                ctx.fillRect(50, y, 80, 25);
                ctx.strokeStyle = colors.secondary;
                ctx.lineWidth = 2;
                ctx.strokeRect(50, y, 80, 25);

                // 绘制键的文本
                ctx.fillStyle = colors.white;
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(key, 90, y + 17);

                // 绘制箭头
                ctx.fillStyle = colors.secondary;
                ctx.font = '16px Arial';
                ctx.fillText('→', 140, y + 17);

                // 绘制值的方框
                ctx.fillStyle = colors.success;
                ctx.fillRect(160, y, 100, 25);
                ctx.strokeStyle = colors.secondary;
                ctx.strokeRect(160, y, 100, 25);

                // 绘制值的文本
                ctx.fillStyle = colors.white;
                ctx.font = 'bold 12px Arial';
                ctx.fillText(value, 210, y + 17);

                index++;
            });

            // 绘制说明
            ctx.fillStyle = colors.secondary;
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`键值对数量: ${hashMapData.size}`, 20, canvas.height - 40);
            ctx.fillText('特点: 快速查找、键值映射', 20, canvas.height - 20);
        }

        // Enumeration 相关函数
        function startEnumeration() {
            enumerationIndex = 0;
            document.getElementById('enumerationScore').textContent = `遍历进度: ${enumerationIndex}/${enumerationData.length}`;
            drawEnumeration();
        }

        function nextElement() {
            if (enumerationIndex < enumerationData.length) {
                enumerationIndex++;
                document.getElementById('enumerationScore').textContent = `遍历进度: ${enumerationIndex}/${enumerationData.length}`;
                drawEnumeration();

                // 添加动画效果
                const canvas = document.getElementById('enumerationCanvas');
                canvas.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    canvas.style.transform = 'scale(1)';
                }, 200);
            }
        }

        function resetEnumeration() {
            enumerationIndex = 0;
            document.getElementById('enumerationScore').textContent = `遍历进度: ${enumerationIndex}/${enumerationData.length}`;
            drawEnumeration();
        }

        function drawEnumeration() {
            const canvas = document.getElementById('enumerationCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = colors.primary;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Enumeration 枚举遍历演示', canvas.width / 2, 30);

            // 绘制集合元素
            const boxWidth = 60;
            const boxHeight = 40;
            const startX = 50;
            const startY = 80;

            enumerationData.forEach((value, index) => {
                const x = startX + index * (boxWidth + 15);
                const y = startY;

                // 根据遍历状态设置颜色
                let fillColor = colors.light;
                let textColor = '#666';

                if (index < enumerationIndex) {
                    fillColor = colors.success; // 已遍历
                    textColor = colors.white;
                } else if (index === enumerationIndex && enumerationIndex < enumerationData.length) {
                    fillColor = colors.warning; // 当前位置
                    textColor = colors.white;
                }

                // 绘制方框
                ctx.fillStyle = fillColor;
                ctx.fillRect(x, y, boxWidth, boxHeight);

                // 绘制边框
                ctx.strokeStyle = colors.secondary;
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, boxWidth, boxHeight);

                // 绘制文本
                ctx.fillStyle = textColor;
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(value, x + boxWidth / 2, y + boxHeight / 2 + 4);

                // 绘制索引
                ctx.fillStyle = colors.secondary;
                ctx.font = '10px Arial';
                ctx.fillText(index, x + boxWidth / 2, y - 5);
            });

            // 绘制当前指针
            if (enumerationIndex < enumerationData.length) {
                const pointerX = startX + enumerationIndex * (boxWidth + 15) + boxWidth / 2;
                const pointerY = startY + boxHeight + 20;

                ctx.fillStyle = colors.danger;
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('↑', pointerX, pointerY);
                ctx.font = '12px Arial';
                ctx.fillText('当前位置', pointerX, pointerY + 20);
            }

            // 绘制方法说明
            ctx.fillStyle = colors.secondary;
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';

            const hasMore = enumerationIndex < enumerationData.length;
            ctx.fillText(`hasMoreElements(): ${hasMore}`, 20, canvas.height - 60);

            if (enumerationIndex > 0 && enumerationIndex <= enumerationData.length) {
                const currentElement = enumerationData[enumerationIndex - 1];
                ctx.fillText(`nextElement(): "${currentElement}"`, 20, canvas.height - 40);
            }

            ctx.fillText('特点: 只读遍历、传统迭代器', 20, canvas.height - 20);
        }

        // 初始化画布
        window.onload = function() {
            drawArrayList();
            drawStack();
            drawHashMap();
            drawEnumeration();
        };
    </script>
</body>
</html>
