<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Reveal</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            background-color: #f0f2f5;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 900px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .story-explanation {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            width: 100%;
            border-left: 5px solid #d9534f;
        }

        .story-explanation p {
            margin: 0;
            line-height: 1.6;
            color: #555;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-radius: 8px;
            overflow: hidden;
        }

        canvas {
            display: block;
            width: 100%;
            height: auto;
            background-color: #2c3e50;
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        button {
            padding: 10px 20px;
            font-size: 1em;
            color: #fff;
            background-color: #d9534f;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        button:hover {
            background-color: #c9302c;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }

        #explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 8px;
            width: 100%;
            text-align: center;
            font-size: 1.2em;
            color: #333;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.5s;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>
        reveal
        <span style="font-size: 0.5em; color: #555;">(re- + veal)</span>
    </h1>

    <div class="story-explanation">
        <p><strong>故事背景：</strong>在一个盛大的颁奖典礼上，舞台中央的聚光灯下，一个巨大的幕布遮住了一个神秘的奖品。这个词的故事，就是关于"揭开"这层面纱。</p>
    </div>

    <div class="canvas-container">
        <canvas id="wordAnimation" width="800" height="450"></canvas>
    </div>

    <div class="controls">
        <button id="playBtn">播放完整动画</button>
        <button id="reBtn">第一幕: re- (向后)</button>
        <button id="vealBtn">第二幕: veal (面纱)</button>
        <button id="revealBtn">第三幕: reveal (揭示)</button>
        <button id="resetBtn">重置</button>
    </div>

    <div id="explanation">
        <p>点击按钮，开始探索 "reveal" 的含义吧！</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordAnimation');
    const ctx = canvas.getContext('2d');
    const explanationDiv = document.getElementById('explanation');

    const playBtn = document.getElementById('playBtn');
    const reBtn = document.getElementById('reBtn');
    const vealBtn = document.getElementById('vealBtn');
    const revealBtn = document.getElementById('revealBtn');
    const resetBtn = document.getElementById('resetBtn');

    let animationFrameId;

    const colors = {
        background: '#2c3e50',
        text: '#ffffff',
        curtain: '#c0392b',
        trophy: '#f1c40f',
        arrow: '#e74c3c'
    };
    const fonts = {
        title: 'bold 36px Arial',
        text: '24px Arial',
        chinese: '20px "Microsoft YaHei", sans-serif'
    };

    function drawStage(curtainWidth) {
        ctx.fillStyle = colors.background;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // stage floor
        ctx.fillStyle = '#34495e';
        ctx.fillRect(0, 350, canvas.width, 100);

        // trophy (hidden)
        ctx.fillStyle = colors.trophy;
        ctx.beginPath();
        ctx.moveTo(380, 350);
        ctx.lineTo(370, 320);
        ctx.arc(375, 320, 5, 0, Math.PI * 2);
        ctx.moveTo(420, 350);
        ctx.lineTo(430, 320);
        ctx.arc(425, 320, 5, 0, Math.PI * 2);
        ctx.fillRect(375, 280, 50, 40);
        ctx.fillRect(360, 260, 80, 20);
        ctx.fill();

        // curtain
        ctx.fillStyle = colors.curtain;
        const halfW = canvas.width / 2;
        ctx.fillRect(0, 0, halfW - curtainWidth, 360);
        ctx.fillRect(halfW + curtainWidth, 0, halfW - curtainWidth, 360);
    }

    function drawInitialState() {
        drawStage(0);
        ctx.fillStyle = colors.text;
        ctx.font = fonts.title;
        ctx.textAlign = 'center';
        ctx.fillText('reveal', canvas.width / 2, 50);
        explanationDiv.innerHTML = '<p>点击按钮，开始探索 "reveal" 的含义吧！</p>';
    }
    
    function resetAnimation() {
        cancelAnimationFrame(animationFrameId);
        drawInitialState();
    }

    // 第一幕: re-
    function animateRe() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>re- (向后)</strong>: 这个前缀意味着"向后"或"离开"。动画将展示幕布即将被拉开的方向。</p>';
        
        let progress = 0;
        
        function drawArrows(p) {
            ctx.strokeStyle = colors.arrow;
            ctx.fillStyle = colors.arrow;
            ctx.lineWidth = 5;
            const y = 180;
            const start1 = 380, end1 = 380 - p * 100;
            const start2 = 420, end2 = 420 + p * 100;

            // left arrow
            ctx.beginPath();
            ctx.moveTo(start1, y);
            ctx.lineTo(end1, y);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(end1, y - 10);
            ctx.lineTo(end1 - 20, y);
            ctx.lineTo(end1, y + 10);
            ctx.closePath();
            ctx.fill();

            // right arrow
            ctx.beginPath();
            ctx.moveTo(start2, y);
            ctx.lineTo(end2, y);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(end2, y - 10);
            ctx.lineTo(end2 + 20, y);
            ctx.lineTo(end2, y + 10);
            ctx.closePath();
            ctx.fill();
        }

        function animate() {
            if (progress >= 1) {
                 ctx.fillStyle = colors.arrow;
                 ctx.font = 'bold 30px Arial';
                 ctx.fillText('re- (向后)', 400, 100);
                 return;
            }
            progress += 0.02;
            
            drawStage(0);
            drawArrows(progress);
            
            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }
    
    // 第二幕: veal
    function animateVeal() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>veal (面纱)</strong>: 词根来自拉丁语 "velum"，意思是"面纱"或"遮盖物"，就像舞台上的这块幕布。</p>';
        
        let alpha = 0;

        function animate() {
            if (alpha >= 1) {
                 ctx.fillStyle = colors.text;
                 ctx.font = 'bold 30px Arial';
                 ctx.fillText('veal (面纱)', 400, 100);
                 return;
            }
            alpha += 0.02;

            drawStage(0);
            ctx.fillStyle = `rgba(255, 255, 255, ${alpha * 0.3})`;
            ctx.fillRect(0, 0, canvas.width, 360); // Highlight effect
            
            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }
    
    // 第三幕: reveal
    function animateReveal() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>reveal (揭示)</strong>: 将面纱(veal)向后(re)拉开，就是"<strong>揭示</strong>"真相的时刻！</p>';

        let progress = 0;

        function animate() {
             if (progress >= 1) {
                ctx.fillStyle = colors.text;
                ctx.font = 'bold 35px Arial';
                ctx.fillText('reveal = re + veal', 400, 60);
                ctx.font = 'bold 30px "Microsoft YaHei"';
                ctx.fillText('向后 + 面纱 = 揭示', 400, 110);
                return;
            }
            progress += 0.01;

            const curtainWidth = progress * (canvas.width / 2);
            drawStage(curtainWidth);
            
            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }
    
    async function playFullAnimation() {
        resetAnimation();
        animateRe();
        await new Promise(r => setTimeout(r, 2500));
        if (animationFrameId === 0) return;
        animateVeal();
        await new Promise(r => setTimeout(r, 2500));
        if (animationFrameId === 0) return;
        animateReveal();
    }

    // Event Listeners
    playBtn.addEventListener('click', playFullAnimation);
    reBtn.addEventListener('click', animateRe);
    vealBtn.addEventListener('click', animateVeal);
    revealBtn.addEventListener('click', animateReveal);
    resetBtn.addEventListener('click', () => {
        animationFrameId = 0;
        resetAnimation();
    });

    // Initial draw
    window.addEventListener('load', drawInitialState);

</script>

</body>
</html> 