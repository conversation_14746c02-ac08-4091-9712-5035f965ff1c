<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL Binlog 格式学习 - 交互式教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .learning-section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 50px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 200;
        }

        .intro-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #34495e;
            margin-bottom: 40px;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .format-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .format-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 20px;
            padding: 30px;
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .format-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .format-card.statement {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .format-card.row {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .format-card.mixed {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .format-card h3 {
            font-size: 1.8rem;
            margin-bottom: 15px;
            font-weight: 300;
        }

        .format-card p {
            font-size: 1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .canvas-container {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 12px;
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 30px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }

        .game-info {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            color: white;
            text-align: center;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .animated-icon {
            display: inline-block;
            animation: pulse 2s infinite;
        }

        .comparison-table {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-weight: 500;
            text-align: center;
        }

        .comparison-table td {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #eee;
            transition: background 0.3s ease;
        }

        .comparison-table tr:hover td {
            background: rgba(102, 126, 234, 0.05);
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            color: #2d3436;
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ MySQL Binlog 格式学习</h1>
            <p>通过动画和交互了解三种 Binlog 格式的特点</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">什么是 MySQL Binlog？</h2>
            <p class="intro-text">
                MySQL Binlog（二进制日志）是 MySQL 数据库中用于记录所有修改数据的操作的日志文件。
                它主要用于数据复制和数据恢复。Binlog 有三种不同的格式，每种都有其独特的特点和适用场景。
            </p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">三种 Binlog 格式</h2>
            
            <div class="format-cards">
                <div class="format-card statement" onclick="showFormat('statement')">
                    <h3>📝 Statement 格式</h3>
                    <p>记录执行的 SQL 语句本身，节省空间但需要保存上下文信息</p>
                </div>
                
                <div class="format-card row" onclick="showFormat('row')">
                    <h3>📊 Row 格式</h3>
                    <p>记录每一行数据的具体变化，信息完整但占用空间较大</p>
                </div>
                
                <div class="format-card mixed" onclick="showFormat('mixed')">
                    <h3>🔄 Mixed 格式</h3>
                    <p>智能选择，普通操作用 Statement，复杂操作用 Row</p>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="1000" height="400"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始动画演示</button>
                <button class="btn" onclick="startGame()">🎮 开始互动游戏</button>
                <button class="btn" onclick="resetDemo()">🔄 重置演示</button>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="game-info" id="gameInfo">
                点击上方的格式卡片或按钮开始学习！
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">格式对比表</h2>
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>特性</th>
                            <th>Statement</th>
                            <th>Row</th>
                            <th>Mixed</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>日志大小</strong></td>
                            <td><span class="highlight">较小</span></td>
                            <td>较大</td>
                            <td>中等</td>
                        </tr>
                        <tr>
                            <td><strong>性能影响</strong></td>
                            <td><span class="highlight">较低</span></td>
                            <td>较高</td>
                            <td>中等</td>
                        </tr>
                        <tr>
                            <td><strong>复制安全性</strong></td>
                            <td>可能不安全</td>
                            <td><span class="highlight">安全</span></td>
                            <td><span class="highlight">安全</span></td>
                        </tr>
                        <tr>
                            <td><strong>函数支持</strong></td>
                            <td>有限制</td>
                            <td><span class="highlight">完全支持</span></td>
                            <td><span class="highlight">智能处理</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let currentDemo = 'intro';
        let animationFrame = 0;
        let gameState = 'waiting';
        let particles = [];
        let score = 0;

        // 设置画布尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 粒子系统
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = (Math.random() - 0.5) * 4;
                this.color = color;
                this.life = 1;
                this.decay = 0.02;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.vy += 0.1; // 重力
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 创建粒子效果
        function createParticles(x, y, color, count = 10) {
            for (let i = 0; i < count; i++) {
                particles.push(new Particle(x, y, color));
            }
        }

        // 绘制文本
        function drawText(text, x, y, size = 16, color = '#2c3e50', align = 'center') {
            ctx.font = `${size}px SF Pro Display, sans-serif`;
            ctx.fillStyle = color;
            ctx.textAlign = align;
            ctx.fillText(text, x, y);
        }

        // 绘制圆角矩形
        function drawRoundRect(x, y, width, height, radius, color) {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, radius);
            ctx.fill();
        }

        // 绘制数据库图标
        function drawDatabase(x, y, size, color) {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.ellipse(x, y - size/4, size/2, size/8, 0, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillRect(x - size/2, y - size/4, size, size/2);
            
            ctx.beginPath();
            ctx.ellipse(x, y + size/4, size/2, size/8, 0, 0, Math.PI * 2);
            ctx.fill();
        }

        // 绘制 SQL 语句
        function drawSQL(x, y, text, highlight = false) {
            const bgColor = highlight ? '#667eea' : '#f8f9fa';
            const textColor = highlight ? 'white' : '#2c3e50';
            
            drawRoundRect(x - 100, y - 15, 200, 30, 8, bgColor);
            drawText(text, x, y + 5, 12, textColor);
        }

        // 绘制数据行
        function drawDataRow(x, y, data, highlight = false) {
            const bgColor = highlight ? '#f093fb' : '#e9ecef';
            const textColor = highlight ? 'white' : '#2c3e50';
            
            drawRoundRect(x - 80, y - 12, 160, 24, 6, bgColor);
            drawText(data, x, y + 4, 10, textColor);
        }

        // Statement 格式演示
        function drawStatementDemo() {
            const centerX = canvas.width / (2 * window.devicePixelRatio);
            const centerY = canvas.height / (2 * window.devicePixelRatio);
            
            // 标题
            drawText('Statement 格式演示', centerX, 40, 24, '#667eea');
            
            // 数据库
            drawDatabase(100, centerY, 60, '#667eea');
            drawText('MySQL', 100, centerY + 50, 14);
            
            // SQL 语句
            const sqlY = centerY - 80;
            drawSQL(centerX, sqlY, 'UPDATE users SET age=25 WHERE id=1', true);
            
            // Binlog
            drawRoundRect(centerX + 200, centerY - 40, 180, 80, 12, '#f8f9fa');
            drawText('Binlog (Statement)', centerX + 290, centerY - 20, 14, '#667eea');
            drawText('记录: SQL语句', centerX + 290, centerY, 12);
            drawText('+ 上下文信息', centerX + 290, centerY + 15, 12);
            
            // 箭头动画
            const arrowX = 200 + Math.sin(animationFrame * 0.1) * 10;
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(arrowX, centerY);
            ctx.lineTo(arrowX + 40, centerY);
            ctx.stroke();
            
            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(arrowX + 35, centerY - 5);
            ctx.lineTo(arrowX + 40, centerY);
            ctx.lineTo(arrowX + 35, centerY + 5);
            ctx.stroke();
            
            // 优缺点
            drawText('优点: 日志量小，性能好', centerX, centerY + 100, 14, '#27ae60');
            drawText('缺点: 函数复制可能不安全', centerX, centerY + 120, 14, '#e74c3c');
        }

        // Row 格式演示
        function drawRowDemo() {
            const centerX = canvas.width / (2 * window.devicePixelRatio);
            const centerY = canvas.height / (2 * window.devicePixelRatio);
            
            drawText('Row 格式演示', centerX, 40, 24, '#f093fb');
            
            drawDatabase(100, centerY, 60, '#f093fb');
            drawText('MySQL', 100, centerY + 50, 14);
            
            // 数据变化
            drawDataRow(centerX - 50, centerY - 60, 'Before: id=1, age=24', false);
            drawDataRow(centerX - 50, centerY - 30, 'After: id=1, age=25', true);
            
            // Binlog
            drawRoundRect(centerX + 150, centerY - 50, 200, 100, 12, '#f8f9fa');
            drawText('Binlog (Row)', centerX + 250, centerY - 30, 14, '#f093fb');
            drawText('记录: 行变化', centerX + 250, centerY - 10, 12);
            drawText('Before Image', centerX + 250, centerY + 5, 10);
            drawText('After Image', centerX + 250, centerY + 20, 10);
            
            drawText('优点: 复制安全，支持所有函数', centerX, centerY + 100, 14, '#27ae60');
            drawText('缺点: 日志量大，性能影响较大', centerX, centerY + 120, 14, '#e74c3c');
        }

        // Mixed 格式演示
        function drawMixedDemo() {
            const centerX = canvas.width / (2 * window.devicePixelRatio);
            const centerY = canvas.height / (2 * window.devicePixelRatio);
            
            drawText('Mixed 格式演示', centerX, 40, 24, '#4facfe');
            
            drawDatabase(100, centerY, 60, '#4facfe');
            drawText('MySQL', 100, centerY + 50, 14);
            
            // 智能选择
            drawRoundRect(centerX - 100, centerY - 80, 200, 40, 8, '#e9ecef');
            drawText('普通 SQL → Statement', centerX, centerY - 55, 12);
            
            drawRoundRect(centerX - 100, centerY - 30, 200, 40, 8, '#e9ecef');
            drawText('复杂函数 → Row', centerX, centerY - 5, 12);
            
            // Binlog
            drawRoundRect(centerX + 150, centerY - 40, 180, 80, 12, '#f8f9fa');
            drawText('Binlog (Mixed)', centerX + 240, centerY - 20, 14, '#4facfe');
            drawText('智能选择格式', centerX + 240, centerY, 12);
            
            drawText('优点: 平衡性能和安全性', centerX, centerY + 100, 14, '#27ae60');
            drawText('推荐: 大多数场景的最佳选择', centerX, centerY + 120, 14, '#3498db');
        }

        // 游戏模式
        function drawGame() {
            const centerX = canvas.width / (2 * window.devicePixelRatio);
            const centerY = canvas.height / (2 * window.devicePixelRatio);
            
            drawText('Binlog 格式选择游戏', centerX, 40, 20, '#667eea');
            drawText(`得分: ${score}`, centerX, 70, 16, '#2c3e50');
            
            // 场景描述
            const scenarios = [
                { text: '大量 UPDATE 操作', answer: 'statement', x: centerX - 150, y: centerY - 50 },
                { text: '使用 NOW() 函数', answer: 'row', x: centerX, y: centerY - 50 },
                { text: '一般业务场景', answer: 'mixed', x: centerX + 150, y: centerY - 50 }
            ];
            
            scenarios.forEach((scenario, index) => {
                const highlight = Math.floor(animationFrame / 60) % 3 === index;
                const color = highlight ? '#667eea' : '#e9ecef';
                const textColor = highlight ? 'white' : '#2c3e50';
                
                drawRoundRect(scenario.x - 60, scenario.y - 20, 120, 40, 8, color);
                drawText(scenario.text, scenario.x, scenario.y, 12, textColor);
            });
            
            // 选项
            const options = [
                { text: 'Statement', color: '#667eea', x: centerX - 100, y: centerY + 50 },
                { text: 'Row', color: '#f093fb', x: centerX, y: centerY + 50 },
                { text: 'Mixed', color: '#4facfe', x: centerX + 100, y: centerY + 50 }
            ];
            
            options.forEach(option => {
                drawRoundRect(option.x - 40, option.y - 15, 80, 30, 8, option.color);
                drawText(option.text, option.x, option.y + 5, 12, 'white');
            });
            
            drawText('点击正确的格式选择！', centerX, centerY + 100, 14, '#2c3e50');
        }

        // 主渲染循环
        function render() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
            
            switch(currentDemo) {
                case 'statement':
                    drawStatementDemo();
                    break;
                case 'row':
                    drawRowDemo();
                    break;
                case 'mixed':
                    drawMixedDemo();
                    break;
                case 'game':
                    drawGame();
                    break;
                default:
                    const centerX = canvas.width / (2 * window.devicePixelRatio);
                    const centerY = canvas.height / (2 * window.devicePixelRatio);
                    drawText('选择一个格式开始学习', centerX, centerY, 20, '#667eea');
                    drawText('或点击按钮开始动画演示', centerX, centerY + 30, 16, '#7f8c8d');
            }
            
            animationFrame++;
            requestAnimationFrame(render);
        }

        // 显示特定格式
        function showFormat(format) {
            currentDemo = format;
            updateProgress(33);
            updateGameInfo(`正在学习 ${format.toUpperCase()} 格式...`);
            createParticles(canvas.width / (4 * window.devicePixelRatio), canvas.height / (4 * window.devicePixelRatio), '#667eea', 15);
        }

        // 开始动画演示
        function startAnimation() {
            let step = 0;
            const formats = ['statement', 'row', 'mixed'];
            
            const animate = () => {
                if (step < formats.length) {
                    showFormat(formats[step]);
                    step++;
                    setTimeout(animate, 3000);
                } else {
                    updateProgress(100);
                    updateGameInfo('动画演示完成！你已经了解了所有三种格式。');
                }
            };
            
            updateGameInfo('开始自动演示...');
            animate();
        }

        // 开始游戏
        function startGame() {
            currentDemo = 'game';
            gameState = 'playing';
            score = 0;
            updateProgress(50);
            updateGameInfo('游戏开始！根据场景选择合适的 Binlog 格式。');
        }

        // 重置演示
        function resetDemo() {
            currentDemo = 'intro';
            gameState = 'waiting';
            score = 0;
            particles = [];
            updateProgress(0);
            updateGameInfo('演示已重置，选择一个选项开始学习！');
        }

        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        // 更新游戏信息
        function updateGameInfo(text) {
            document.getElementById('gameInfo').textContent = text;
        }

        // 画布点击事件
        canvas.addEventListener('click', (e) => {
            if (currentDemo === 'game' && gameState === 'playing') {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // 检查点击的选项
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const options = [
                    { name: 'statement', x: centerX - 100, y: centerY + 50 },
                    { name: 'row', x: centerX, y: centerY + 50 },
                    { name: 'mixed', x: centerX + 100, y: centerY + 50 }
                ];
                
                options.forEach(option => {
                    const distance = Math.sqrt((x - option.x) ** 2 + (y - option.y) ** 2);
                    if (distance < 40) {
                        score += 10;
                        createParticles(option.x, option.y, '#27ae60', 20);
                        updateGameInfo(`正确！得分: ${score}。继续选择下一个场景。`);
                        updateProgress(Math.min(100, 50 + score));
                    }
                });
            }
        });

        // 开始渲染
        render();

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            updateGameInfo('欢迎来到 MySQL Binlog 学习中心！选择一个选项开始你的学习之旅。');
            
            // 为格式卡片添加动画延迟
            const cards = document.querySelectorAll('.format-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.2}s`;
                card.style.animation = 'fadeInUp 0.8s ease-out forwards';
            });
        });
    </script>
</body>
</html>
