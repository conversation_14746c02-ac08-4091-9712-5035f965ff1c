<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件开发方法互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            margin-bottom: 30px;
        }

        .game-board {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-text {
            font-size: 1.3rem;
            color: #333;
            line-height: 1.6;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option-card {
            background: white;
            border: 3px solid #e0e6ff;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .option-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .option-card.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            animation: correctPulse 0.6s ease-in-out;
        }

        .option-card.wrong {
            border-color: #f44336;
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            animation: wrongShake 0.6s ease-in-out;
        }

        .option-label {
            font-weight: bold;
            font-size: 1.1rem;
            margin-bottom: 10px;
        }

        .option-text {
            font-size: 1rem;
            line-height: 1.4;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #gameCanvas {
            border: 2px solid #667eea;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .explanation-section {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        .explanation-title {
            color: #667eea;
            font-size: 1.5rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .method-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .method-card:hover {
            transform: translateX(10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .method-name {
            font-weight: bold;
            color: #667eea;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .method-description {
            color: #666;
            line-height: 1.5;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .check-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto;
        }

        .check-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .check-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .score-display {
            text-align: center;
            margin: 20px 0;
            font-size: 1.2rem;
            color: #667eea;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e6ff;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 软件开发方法学习游戏</h1>
            <p class="subtitle">通过互动游戏掌握敏捷开发方法的核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="score-display" id="scoreDisplay">准备开始学习！</div>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-text">
                    <strong>题目：</strong>（ ） 适用于程序开发人员在地域上分布很广的开发团队，（ ） 中，程序开发人员分成首席程序员和"类"程序员。
                </div>

                <div class="canvas-container">
                    <canvas id="gameCanvas" width="800" height="300"></canvas>
                </div>

                <div class="options-container">
                    <div class="option-card" data-option="A">
                        <div class="option-label">A. 水晶系列（Crystal）开发方法</div>
                        <div class="option-text">强调以人为中心，用最少纪律约束达到平衡</div>
                    </div>
                    <div class="option-card" data-option="B">
                        <div class="option-label">B. 开放式源码（Open Source）开发方法</div>
                        <div class="option-text">程序开发人员在地域上分布很广</div>
                    </div>
                    <div class="option-card" data-option="C">
                        <div class="option-label">C. SCRUM开发方法</div>
                        <div class="option-text">强调明确定义的可重复方法过程</div>
                    </div>
                    <div class="option-card" data-option="D">
                        <div class="option-label">D. 功用驱动开发方法（FDD）</div>
                        <div class="option-text">分成首席程序员和"类"程序员</div>
                    </div>
                </div>

                <button class="check-btn" id="checkBtn" disabled>检查答案</button>
            </div>

            <div class="explanation-section" id="explanationSection">
                <h3 class="explanation-title">📚 知识点详解</h3>
                
                <div class="method-card">
                    <div class="method-name">🌐 开放式源码（Open Source）开发方法</div>
                    <div class="method-description">
                        <span class="highlight">地域分布广泛</span>是其突出特点。开发人员可以在世界各地协作，通过互联网进行代码共享和错误修复。任何人发现错误都可以提交"补丁"文件。
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-name">👑 功用驱动开发方法（FDD）</div>
                    <div class="method-description">
                        将开发人员分为两类：<span class="highlight">首席程序员</span>（最富经验的协调者、设计者）和<span class="highlight">"类"程序员</span>（主要负责源码编写）。迭代周期通常为两周。
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-name">💎 水晶系列（Crystal）开发方法</div>
                    <div class="method-description">
                        由Alistair Cockburn提出，<span class="highlight">以人为中心</span>，探索用最少纪律约束而仍能成功的方法，在产出效率与易于运作上达到平衡。
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-name">🏃 SCRUM开发方法</div>
                    <div class="method-description">
                        强调<span class="highlight">明确定义的可重复方法过程</span>只适用于明确定义的环境、人员和问题。注重团队协作和迭代开发。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let selectedOption = null;
        let gameCompleted = false;
        let score = 0;

        // Canvas 动画
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 动画元素
        let particles = [];
        let animationFrame = 0;

        // 初始化粒子系统
        function initParticles() {
            particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: Math.random() * 3 + 1,
                    color: `hsl(${Math.random() * 60 + 220}, 70%, 70%)`,
                    alpha: Math.random() * 0.5 + 0.3
                });
            }
        }

        // 绘制开发方法图标
        function drawMethodIcons() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景粒子
            particles.forEach(particle => {
                ctx.save();
                ctx.globalAlpha = particle.alpha;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                
                // 更新粒子位置
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                // 边界检测
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            });

            // 绘制开发方法图标
            const methods = [
                { name: 'Open Source', x: 150, y: 150, icon: '🌐', color: '#4CAF50' },
                { name: 'FDD', x: 350, y: 150, icon: '👑', color: '#FF9800' },
                { name: 'Crystal', x: 550, y: 150, icon: '💎', color: '#9C27B0' },
                { name: 'SCRUM', x: 650, y: 150, icon: '🏃', color: '#2196F3' }
            ];

            methods.forEach((method, index) => {
                const pulse = Math.sin(animationFrame * 0.05 + index) * 0.1 + 1;
                
                // 绘制圆形背景
                ctx.save();
                ctx.fillStyle = method.color + '20';
                ctx.beginPath();
                ctx.arc(method.x, method.y, 40 * pulse, 0, Math.PI * 2);
                ctx.fill();
                
                // 绘制图标
                ctx.font = `${30 * pulse}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(method.icon, method.x, method.y - 5);
                
                // 绘制名称
                ctx.font = '14px Microsoft YaHei';
                ctx.fillStyle = method.color;
                ctx.fillText(method.name, method.x, method.y + 35);
                ctx.restore();
            });

            animationFrame++;
        }

        // 选项卡点击事件
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('click', function() {
                if (gameCompleted) return;
                
                // 移除之前的选择
                document.querySelectorAll('.option-card').forEach(c => c.classList.remove('selected'));
                
                // 添加当前选择
                this.classList.add('selected');
                selectedOption = this.dataset.option;
                
                // 启用检查按钮
                document.getElementById('checkBtn').disabled = false;
                
                // 更新进度
                updateProgress(25);
            });
        });

        // 检查答案
        document.getElementById('checkBtn').addEventListener('click', function() {
            if (!selectedOption || gameCompleted) return;
            
            gameCompleted = true;
            this.disabled = true;
            
            // 显示正确答案
            document.querySelectorAll('.option-card').forEach(card => {
                const option = card.dataset.option;
                if (option === 'B') {
                    card.classList.add('correct');
                } else if (option === selectedOption && option !== 'B') {
                    card.classList.add('wrong');
                }
            });
            
            // 计算分数
            if (selectedOption === 'B') {
                score = 100;
                updateScore('🎉 恭喜答对了！得分：100分');
                updateProgress(100);
                setTimeout(() => {
                    showCelebration();
                }, 500);
            } else {
                score = 0;
                updateScore('😔 答错了，正确答案是B。继续学习吧！');
                updateProgress(50);
            }
            
            // 显示解释
            setTimeout(() => {
                document.getElementById('explanationSection').style.display = 'block';
            }, 1000);
        });

        // 更新分数显示
        function updateScore(message) {
            document.getElementById('scoreDisplay').textContent = message;
        }

        // 更新进度条
        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // 庆祝动画
        function showCelebration() {
            // 创建庆祝粒子
            for (let i = 0; i < 30; i++) {
                particles.push({
                    x: canvas.width / 2,
                    y: canvas.height / 2,
                    vx: (Math.random() - 0.5) * 10,
                    vy: (Math.random() - 0.5) * 10,
                    size: Math.random() * 5 + 3,
                    color: `hsl(${Math.random() * 60 + 45}, 100%, 60%)`,
                    alpha: 1,
                    life: 60
                });
            }
        }

        // 启动动画循环
        function animate() {
            drawMethodIcons();
            requestAnimationFrame(animate);
        }

        // 初始化游戏
        initParticles();
        animate();
        updateScore('选择一个答案开始游戏！');
    </script>
</body>
</html>
