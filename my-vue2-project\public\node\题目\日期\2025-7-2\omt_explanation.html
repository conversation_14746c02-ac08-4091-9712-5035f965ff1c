<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OMT方法交互式解释</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        #app-container {
            width: 100%;
            max-width: 900px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            padding: 30px;
            box-sizing: border-box;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 0.8s ease-out forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1, h2 {
            text-align: center;
            color: #1a2a4c;
        }

        #question-box {
            background-color: #e6f7ff;
            border-left: 5px solid #1890ff;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 6px;
        }

        #question-box p {
            margin: 0;
            font-size: 1.1em;
            line-height: 1.6;
        }
        
        #options {
            margin-top: 15px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .option {
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .option.correct {
            background-color: #f6ffed;
            border-color: #52c41a;
        }

        .option.wrong {
            background-color: #fff1f0;
            border-color: #f5222d;
        }


        #interactive-area {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            align-items: flex-start;
        }

        #controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
            flex-shrink: 0;
        }

        .control-btn {
            padding: 12px 20px;
            font-size: 1em;
            cursor: pointer;
            border: none;
            border-radius: 6px;
            background-color: #1890ff;
            color: white;
            transition: background-color 0.3s, transform 0.2s;
            min-width: 150px;
        }

        .control-btn:hover {
            background-color: #40a9ff;
            transform: translateY(-2px);
        }

        .control-btn.active {
            background-color: #096dd9;
        }

        #canvas-container {
            flex-grow: 1;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            position: relative;
            background-color: #fafafa;
        }

        canvas {
            display: block;
            width: 100%;
            height: auto;
        }

        #explanation-text {
            position: absolute;
            top: 15px;
            left: 15px;
            right: 15px;
            font-size: 0.95em;
            color: #555;
            background-color: rgba(255, 255, 255, 0.85);
            padding: 10px;
            border-radius: 6px;
            line-height: 1.5;
        }

        #summary {
            margin-top: 30px;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 6px;
        }
        
        @media (max-width: 768px) {
            #interactive-area {
                flex-direction: column;
            }
            #controls {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>

    <div id="app-container">
        <h1>OMT方法：软件开发的三个视角</h1>

        <div id="question-box">
            <p><strong>题目：</strong>有一种开发方法，它使用<strong>建模</strong>的思想，通过建立<strong>对象模型</strong>、<strong>动态模型</strong>和<strong>功能模型</strong>来描述一个实际的应用。请问这是哪种方法？</p>
            <div id="options">
                <div class="option" data-answer="A">A) 面向对象方法</div>
                <div class="option" data-answer="B">B) OMT方法</div>
                <div class="option" data-answer="C">C) 结构化方法</div>
                <div class="option" data-answer="D">D) Booch方法</div>
            </div>
        </div>

        <h2>交互式动画演示</h2>
        <p style="text-align: center; margin-bottom: 20px; color: #555;">把一个复杂的"网上商店"系统作为例子，看看OMT方法如何分析它。请点击下方按钮查看不同模型：</p>
        
        <div id="interactive-area">
            <div id="controls">
                <button class="control-btn active" data-model="object">对象模型</button>
                <button class="control-btn" data-model="dynamic">动态模型</button>
                <button class="control-btn" data-model="functional">功能模型</button>
            </div>
            <div id="canvas-container">
                <canvas id="omt-canvas" width="600" height="400"></canvas>
                <div id="explanation-text"></div>
            </div>
        </div>

        <div id="summary">
            <h3>知识点总结</h3>
            <p><strong>OMT (对象建模技术)</strong> 是一种经典的面向对象分析方法。它的核心就是用三个相互独立又互相关联的模型来全面地描述系统：</p>
            <ul>
                <li><strong>对象模型 (Object Model):</strong> 回答"系统由什么组成？"，关注静态结构。用<strong>对象图</strong>实现。</li>
                <li><strong>动态模型 (Dynamic Model):</strong> 回答"系统中的对象如何随时间变化？"，关注事件与状态。用<strong>状态图</strong>实现。</li>
                <li><strong>功能模型 (Functional Model):</strong> 回答"数据如何处理和流动？"，关注系统功能。用<strong>数据流图(DFD)</strong>实现。</li>
            </ul>
            <p>所以，原题中提到的"功能模型主要用( )实现"，答案就是<strong>数据流图 (DFD)</strong>。</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('omt-canvas');
        const ctx = canvas.getContext('2d');
        const explanationDiv = document.getElementById('explanation-text');
        const buttons = document.querySelectorAll('.control-btn');
        let currentModel = 'object';

        // --- 绘图相关的辅助函数 ---
        function drawBox(x, y, width, height, text, color = '#1890ff') {
            ctx.strokeStyle = color;
            ctx.fillStyle = '#e6f7ff';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.rect(x, y, width, height);
            ctx.stroke();
            ctx.fill();

            ctx.fillStyle = '#1a2a4c';
            ctx.font = '16px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + width / 2, y + height / 2);
        }

        function drawState(x, y, width, height, text) {
            ctx.strokeStyle = '#faad14';
            ctx.fillStyle = '#fffbe6';
            ctx.lineWidth = 2;

            ctx.beginPath();
            ctx.roundRect(x, y, width, height, 15);
            ctx.stroke();
            ctx.fill();

            ctx.fillStyle = '#613400';
            ctx.font = '16px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + width / 2, y + height / 2);
        }
        
        function drawProcess(x, y, radius, text) {
            ctx.strokeStyle = '#52c41a';
            ctx.fillStyle = '#f6ffed';
            ctx.lineWidth = 2;
            
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.stroke();
            ctx.fill();

            ctx.fillStyle = '#135200';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
        }

        function drawArrow(fromX, fromY, toX, toY, text = '') {
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            
            ctx.strokeStyle = '#595959';
            ctx.lineWidth = 1.5;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = '#595959';
            ctx.fill();

            if (text) {
                ctx.save();
                ctx.translate((fromX + toX) / 2, (fromY + toY) / 2);
                ctx.rotate(angle);
                if (Math.abs(angle) > Math.PI/2) {
                   ctx.rotate(Math.PI);
                }
                ctx.fillStyle = '#262626';
                ctx.font = '14px sans-serif';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'bottom';
                ctx.fillText(text, 0, -5);
                ctx.restore();
            }
        }

        // --- 各个模型的绘制函数 ---

        function drawObjectModel() {
            explanationDiv.innerHTML = '<strong>对象模型:</strong> 关注系统由哪些"东西"组成，以及它们之间的关系。这里我们看到"顾客"、"订单"和"商品"是核心对象。';
            
            const customer = { x: 50, y: 150, w: 100, h: 50, text: "顾客" };
            const order = { x: 250, y: 150, w: 100, h: 50, text: "订单" };
            const product = { x: 450, y: 150, w: 100, h: 50, text: "商品" };

            drawBox(customer.x, customer.y, customer.w, customer.h, customer.text);
            drawBox(order.x, order.y, order.w, order.h, order.text);
            drawBox(product.x, product.y, product.w, product.h, product.text);

            drawArrow(customer.x + customer.w, customer.y + customer.h / 2, order.x, order.y + order.h / 2, "下达");
            drawArrow(order.x + order.w, order.y + order.h / 2, product.x, product.y + product.h / 2, "包含");
        }

        function drawDynamicModel() {
            explanationDiv.innerHTML = '<strong>动态模型:</strong> 关注对象的状态如何随时间变化。以"订单"为例，它会经历从"待支付"到"已完成"等不同状态。';
            
            const state1 = { x: 50, y: 150, w: 120, h: 60, text: "待支付" };
            const state2 = { x: 240, y: 150, w: 120, h: 60, text: "已支付" };
            const state3 = { x: 430, y: 150, w: 120, h: 60, text: "已完成" };

            drawState(state1.x, state1.y, state1.w, state1.h, state1.text);
            drawState(state2.x, state2.y, state2.w, state2.h, state2.text);
            drawState(state3.x, state3.y, state3.w, state3.h, state3.text);

            drawArrow(state1.x + state1.w, state1.y + state1.h / 2, state2.x, state2.y + state2.h / 2, "付款成功");
            drawArrow(state2.x + state2.w, state2.y + state2.h / 2, state3.x, state3.y + state3.h / 2, "发货签收");
        }
        
        function drawFunctionalModel() {
            explanationDiv.innerHTML = '<strong>功能模型:</strong> 关注数据如何在系统中流动和处理。这个模型展示了"处理付款"这个功能的数据输入和输出。它常用数据流图(DFD)表示。';

            const customer = { x: 80, y: 200, w: 100, h: 50, text: "顾客" };
            const process = { x: 300, y: 200, r: 70, text: "处理付款" };
            const orderDB = { x: 520, y: 200, w: 100, h: 50, text: "订单数据" };

            drawBox(customer.x-customer.w/2, customer.y-customer.h/2, customer.w, customer.h, customer.text, '#722ed1'); // Entity
            drawProcess(process.x, process.y, process.r, process.text);
            
            // Data store
            ctx.strokeStyle = '#08979c';
            ctx.fillStyle = '#e6fffb';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(orderDB.x, orderDB.y - orderDB.h/2);
            ctx.lineTo(orderDB.x + orderDB.w, orderDB.y - orderDB.h/2);
            ctx.moveTo(orderDB.x, orderDB.y + orderDB.h/2);
            ctx.lineTo(orderDB.x + orderDB.w, orderDB.y + orderDB.h/2);
            ctx.rect(orderDB.x, orderDB.y - orderDB.h/2, orderDB.w, orderDB.h);
            ctx.stroke();
            ctx.fill();
            ctx.fillStyle = '#00474f';
            ctx.fillText("订单数据", orderDB.x+orderDB.w/2, orderDB.y);


            drawArrow(customer.x + customer.w/2, customer.y, process.x - process.r, process.y, "支付信息");
            drawArrow(process.x + process.r, process.y, orderDB.x, orderDB.y, "更新订单状态");
            drawArrow(process.x, process.y - process.r, customer.x, customer.y - customer.h, "支付回执");
        }

        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            switch (currentModel) {
                case 'object':
                    drawObjectModel();
                    break;
                case 'dynamic':
                    drawDynamicModel();
                    break;
                case 'functional':
                    drawFunctionalModel();
                    break;
            }
        }

        // --- 事件监听 ---
        buttons.forEach(btn => {
            btn.addEventListener('click', () => {
                currentModel = btn.dataset.model;
                buttons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                draw();
            });
        });

        const options = document.querySelectorAll('.option');
        options.forEach(option => {
            option.addEventListener('click', (e) => {
                // Clear previous states
                options.forEach(o => {
                    o.classList.remove('correct', 'wrong');
                });

                const selectedAnswer = e.currentTarget.dataset.answer;
                if (selectedAnswer === 'B') {
                    e.currentTarget.classList.add('correct');
                } else {
                    e.currentTarget.classList.add('wrong');
                    // Highlight the correct answer
                    document.querySelector('.option[data-answer="B"]').classList.add('correct');
                }
            });
        });


        // --- 初始化 ---
        draw();

    </script>
</body>
</html> 