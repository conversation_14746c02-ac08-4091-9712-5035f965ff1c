<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件需求分析 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .game-board {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            text-align: center;
        }

        .pyramid-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        .pyramid {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .pyramid-level {
            padding: 20px 40px;
            border-radius: 15px;
            color: white;
            font-weight: bold;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-width: 200px;
        }

        .business-req {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            width: 300px;
        }

        .user-req {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            width: 400px;
        }

        .func-req {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            width: 500px;
        }

        .pyramid-level:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        #gameCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option-card {
            background: white;
            border: 3px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .option-card.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .option-card.wrong {
            border-color: #f44336;
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            color: white;
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #2196F3;
            display: none;
            animation: slideInLeft 0.5s ease-out;
        }

        .explanation.show {
            display: block;
        }

        .explanation h3 {
            color: #1976D2;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            line-height: 1.6;
            color: #333;
            margin-bottom: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .score-display {
            text-align: center;
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }

        .next-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: none;
        }

        .next-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .next-button.show {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 软件需求分析学习</h1>
            <p>通过互动游戏学习业务需求、用户需求和功能需求的区别</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-title">
                    📚 知识点：软件需求的三个层次
                </div>
                
                <div class="pyramid-container">
                    <div class="pyramid">
                        <div class="pyramid-level business-req" onclick="showRequirementInfo('business')">
                            <strong>业务需求</strong><br>
                            <small>组织的高层次目标</small>
                        </div>
                        <div class="pyramid-level user-req" onclick="showRequirementInfo('user')">
                            <strong>用户需求</strong><br>
                            <small>用户必须完成的任务</small>
                        </div>
                        <div class="pyramid-level func-req" onclick="showRequirementInfo('functional')">
                            <strong>功能需求</strong><br>
                            <small>开发人员必须实现的功能</small>
                        </div>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="gameCanvas" width="800" height="400"></canvas>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="score-display" id="scoreDisplay">得分: 0</div>
            </div>

            <div id="questionContainer">
                <!-- 题目将通过JavaScript动态生成 -->
            </div>

            <div class="explanation" id="explanation">
                <!-- 解释内容将通过JavaScript动态生成 -->
            </div>

            <button class="next-button" id="nextButton" onclick="nextQuestion()">下一题</button>
        </div>
    </div>

    <script>
        // 游戏状态
        let currentQuestion = 0;
        let score = 0;
        let totalQuestions = 3;
        let canvas, ctx;
        let particles = [];

        // 题目数据
        const questions = [
            {
                question: "找出文档中的拼写错误并提供一个替换项列表来供选择替换拼错的词",
                options: ["业务需求", "用户需求", "功能需求", "性能需求"],
                correct: 1,
                explanation: "这描述了用户使用产品必须要完成的任务，属于用户需求。用户需求说明用户要做什么，而不是系统如何实现。"
            },
            {
                question: "显示提供替换词的对话框以及实现整个文档范围的替换",
                options: ["业务需求", "用户需求", "功能需求", "性能需求"],
                correct: 2,
                explanation: "这定义了开发人员必须实现的具体软件功能，如显示对话框、实现替换等技术细节，属于功能需求。"
            },
            {
                question: "用户能有效地纠正文档中的拼写错误",
                options: ["业务需求", "用户需求", "功能需求", "性能需求"],
                correct: 0,
                explanation: "这反映了组织对系统的高层次目标要求，是产品要达到的商业价值，属于业务需求。"
            }
        ];

        // 初始化
        window.onload = function() {
            canvas = document.getElementById('gameCanvas');
            ctx = canvas.getContext('2d');
            initParticles();
            animateCanvas();
            showQuestion();
        };

        // 粒子系统
        function initParticles() {
            for(let i = 0; i < 50; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: Math.random() * 3 + 1,
                    color: `hsl(${Math.random() * 60 + 200}, 70%, 70%)`
                });
            }
        }

        function animateCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 更新和绘制粒子
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;

                if(particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if(particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = particle.color;
                ctx.fill();
            });

            // 绘制需求层次图
            drawRequirementDiagram();

            requestAnimationFrame(animateCanvas);
        }

        function drawRequirementDiagram() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制三个层次的圆圈
            const levels = [
                {name: '业务需求', radius: 60, color: '#ff9a9e'},
                {name: '用户需求', radius: 90, color: '#a8edea'},
                {name: '功能需求', radius: 120, color: '#ffecd2'}
            ];

            levels.forEach((level, index) => {
                ctx.beginPath();
                ctx.arc(centerX, centerY, level.radius, 0, Math.PI * 2);
                ctx.strokeStyle = level.color;
                ctx.lineWidth = 3;
                ctx.stroke();

                ctx.fillStyle = level.color;
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(level.name, centerX, centerY - level.radius - 10);
            });
        }

        // 显示题目
        function showQuestion() {
            if(currentQuestion >= questions.length) {
                showFinalScore();
                return;
            }

            const question = questions[currentQuestion];
            const container = document.getElementById('questionContainer');
            
            container.innerHTML = `
                <div class="question-title">
                    第 ${currentQuestion + 1} 题：${question.question}
                </div>
                <div class="options-container" id="optionsContainer">
                    ${question.options.map((option, index) => `
                        <div class="option-card" onclick="selectOption(${index})">
                            ${String.fromCharCode(65 + index)}. ${option}
                        </div>
                    `).join('')}
                </div>
            `;

            updateProgress();
        }

        // 选择选项
        function selectOption(selectedIndex) {
            const question = questions[currentQuestion];
            const options = document.querySelectorAll('.option-card');
            
            options.forEach((option, index) => {
                option.onclick = null; // 禁用点击
                if(index === question.correct) {
                    option.classList.add('correct');
                } else if(index === selectedIndex && index !== question.correct) {
                    option.classList.add('wrong');
                }
            });

            if(selectedIndex === question.correct) {
                score += 10;
                showCelebration();
            }

            updateScore();
            showExplanation();
            document.getElementById('nextButton').classList.add('show');
        }

        // 显示解释
        function showExplanation() {
            const question = questions[currentQuestion];
            const explanation = document.getElementById('explanation');
            
            explanation.innerHTML = `
                <h3>💡 详细解释</h3>
                <p><strong>正确答案：</strong>${question.options[question.correct]}</p>
                <p>${question.explanation}</p>
                <p><strong>记忆要点：</strong></p>
                <ul>
                    <li><strong>业务需求</strong>：回答"为什么要做"，体现商业价值</li>
                    <li><strong>用户需求</strong>：回答"用户要做什么"，描述用户任务</li>
                    <li><strong>功能需求</strong>：回答"系统如何实现"，定义具体功能</li>
                </ul>
            `;
            
            explanation.classList.add('show');
        }

        // 下一题
        function nextQuestion() {
            currentQuestion++;
            document.getElementById('explanation').classList.remove('show');
            document.getElementById('nextButton').classList.remove('show');
            showQuestion();
        }

        // 更新进度
        function updateProgress() {
            const progress = (currentQuestion / questions.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 更新分数
        function updateScore() {
            document.getElementById('scoreDisplay').textContent = `得分: ${score}`;
        }

        // 显示庆祝动画
        function showCelebration() {
            for(let i = 0; i < 20; i++) {
                particles.push({
                    x: canvas.width / 2,
                    y: canvas.height / 2,
                    vx: (Math.random() - 0.5) * 10,
                    vy: (Math.random() - 0.5) * 10,
                    size: Math.random() * 5 + 3,
                    color: '#FFD700'
                });
            }
        }

        // 显示需求信息
        function showRequirementInfo(type) {
            const info = {
                business: "业务需求：反映组织对系统的高层次目标，回答'为什么要做这个系统'",
                user: "用户需求：描述用户使用产品必须完成的任务，回答'用户要做什么'",
                functional: "功能需求：定义开发人员必须实现的软件功能，回答'系统如何实现'"
            };
            
            alert(info[type]);
        }

        // 显示最终分数
        function showFinalScore() {
            const container = document.getElementById('questionContainer');
            const percentage = (score / (questions.length * 10)) * 100;
            
            container.innerHTML = `
                <div class="question-title">
                    🎉 学习完成！
                </div>
                <div style="text-align: center; padding: 40px;">
                    <h2>最终得分: ${score}/${questions.length * 10}</h2>
                    <h3>正确率: ${percentage.toFixed(1)}%</h3>
                    <p style="margin-top: 20px; font-size: 1.1rem;">
                        ${percentage >= 80 ? '🌟 优秀！您已经很好地掌握了软件需求分析的知识！' : 
                          percentage >= 60 ? '👍 不错！建议再复习一下相关概念。' : 
                          '💪 继续努力！多练习几遍会更好。'}
                    </p>
                    <button onclick="location.reload()" style="margin-top: 20px; padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        重新开始
                    </button>
                </div>
            `;
        }
    </script>
</body>
</html>
