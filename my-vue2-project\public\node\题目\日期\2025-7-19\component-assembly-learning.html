<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构件组装三层次学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .assembly-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .assembly-demo {
            text-align: center;
            margin: 30px 0;
        }

        #assemblyCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .level-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 25px 0;
        }

        .level-btn {
            padding: 20px 15px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .customization-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .integration-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .extension-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .level-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .level-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .level-explanation {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .explanation-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .explanation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .explanation-card.customization {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .explanation-card.integration {
            border-color: #fd79a8;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .explanation-card.extension {
            border-color: #00b894;
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }

        .explanation-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .explanation-card p {
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-correct {
            color: #00a085;
            font-weight: bold;
            background: rgba(0,184,148,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-component {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            animation: floatComponent 15s infinite ease-in-out;
        }

        .comp1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .comp2 {
            top: 70%;
            right: 15%;
            animation-delay: 5s;
        }

        .comp3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatComponent {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-25px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .level-controls {
                grid-template-columns: 1fr;
            }
            
            .level-explanation {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-component comp1"></div>
        <div class="floating-component comp2"></div>
        <div class="floating-component comp3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🧩 构件组装三层次学习</h1>
            <p>深度理解构件组装的定制、集成、扩展三个层次</p>
        </div>

        <div class="main-grid">
            <div class="assembly-section">
                <h2 class="section-title">🔧 构件组装演示</h2>
                
                <div class="assembly-demo">
                    <canvas id="assemblyCanvas" width="700" height="400"></canvas>
                </div>

                <div class="level-controls">
                    <button class="level-btn customization-btn" onclick="demonstrateLevel('customization')">
                        定制层次<br><small>Customization</small>
                    </button>
                    <button class="level-btn integration-btn" onclick="demonstrateLevel('integration')">
                        集成层次<br><small>Integration</small>
                    </button>
                    <button class="level-btn extension-btn" onclick="demonstrateLevel('extension')">
                        扩展层次<br><small>Extension</small>
                    </button>
                </div>

                <div class="level-explanation">
                    <div class="explanation-card customization">
                        <h3>🎨 定制 (Customization)</h3>
                        <p>• 配置构件参数<br>• 调整构件属性<br>• 适应特定需求<br>• 不改变构件结构</p>
                    </div>
                    <div class="explanation-card integration">
                        <h3>🔗 集成 (Integration)</h3>
                        <p>• 连接多个构件<br>• 建立构件间通信<br>• 协调构件协作<br>• 形成完整系统</p>
                    </div>
                    <div class="explanation-card extension">
                        <h3>🚀 扩展 (Extension)</h3>
                        <p>• 增加新功能<br>• 扩展构件能力<br>• 添加新接口<br>• 增强系统功能</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 构件组装成软件系统的过程可以分为三个不同的层次：（　　）。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. 初始化、互连和集成
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 连接、集成和演化
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        C. 定制、集成和扩展
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        D. 集成、扩展和演化
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：C. 定制、集成和扩展</strong></p>
                    <p>构件组装成软件系统的三个层次：</p>
                    <ul>
                        <li><span class="highlight-correct">定制 (Customization)</span>：
                            <br>• 配置构件的参数和属性
                            <br>• 使构件适应特定的应用需求
                            <br>• 不改变构件的基本结构和接口</li>
                        <li><span class="highlight-correct">集成 (Integration)</span>：
                            <br>• 将多个构件连接组合在一起
                            <br>• 建立构件间的通信和协作机制
                            <br>• 协调构件间的数据流和控制流</li>
                        <li><span class="highlight-correct">扩展 (Extension)</span>：
                            <br>• 在现有构件基础上增加新功能
                            <br>• 扩展构件的能力和接口
                            <br>• 增强整个系统的功能</li>
                    </ul>
                    <p><strong>层次递进关系</strong>：定制→集成→扩展，每个层次都建立在前一层次的基础上，逐步构建完整的软件系统。</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了构件组装的三个层次！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('assemblyCanvas');
        const ctx = canvas.getContext('2d');
        let currentLevel = 'customization';
        let animationId = null;

        // 演示不同组装层次
        function demonstrateLevel(levelType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            currentLevel = levelType;
            
            // 更新按钮状态
            document.querySelectorAll('.level-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${levelType}-btn`).classList.add('active');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(levelType) {
                case 'customization':
                    drawCustomizationLevel();
                    break;
                case 'integration':
                    drawIntegrationLevel();
                    break;
                case 'extension':
                    drawExtensionLevel();
                    break;
            }
        }

        // 绘制定制层次
        function drawCustomizationLevel() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('定制层次 - Customization', 350, 40);

            // 绘制原始构件
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(200, 100, 120, 80);
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 3;
            ctx.strokeRect(200, 100, 120, 80);
            
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('原始构件', 260, 145);

            // 配置参数
            const params = ['参数A', '参数B', '参数C'];
            params.forEach((param, index) => {
                const x = 380;
                const y = 110 + index * 25;
                
                ctx.fillStyle = '#74b9ff';
                ctx.fillRect(x, y, 80, 20);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 1;
                ctx.strokeRect(x, y, 80, 20);
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(param, x + 40, y + 14);
            });

            // 箭头
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(320, 140);
            ctx.lineTo(380, 140);
            ctx.stroke();
            
            // 箭头头部
            ctx.fillStyle = '#0984e3';
            ctx.beginPath();
            ctx.moveTo(380, 140);
            ctx.lineTo(370, 135);
            ctx.lineTo(370, 145);
            ctx.closePath();
            ctx.fill();

            // 定制后的构件
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(200, 250, 120, 80);
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 3;
            ctx.strokeRect(200, 250, 120, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('定制构件', 260, 295);

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 配置构件参数', 480, 120);
            ctx.fillText('• 调整构件属性', 480, 140);
            ctx.fillText('• 适应特定需求', 480, 160);
            ctx.fillText('• 不改变构件结构', 480, 180);
        }

        // 绘制集成层次
        function drawIntegrationLevel() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('集成层次 - Integration', 350, 40);

            // 绘制多个构件
            const components = [
                {x: 100, y: 100, name: '构件A', color: '#74b9ff'},
                {x: 300, y: 100, name: '构件B', color: '#00b894'},
                {x: 500, y: 100, name: '构件C', color: '#fdcb6e'},
                {x: 200, y: 250, name: '构件D', color: '#e17055'}
            ];

            components.forEach(comp => {
                ctx.fillStyle = comp.color;
                ctx.fillRect(comp.x, comp.y, 100, 60);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(comp.x, comp.y, 100, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(comp.name, comp.x + 50, comp.y + 35);
            });

            // 连接线
            const connections = [
                {from: {x: 200, y: 130}, to: {x: 300, y: 130}},
                {from: {x: 400, y: 130}, to: {x: 500, y: 130}},
                {from: {x: 150, y: 160}, to: {x: 200, y: 250}},
                {from: {x: 350, y: 160}, to: {x: 300, y: 250}}
            ];

            connections.forEach(conn => {
                ctx.strokeStyle = '#fd79a8';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(conn.from.x, conn.from.y);
                ctx.lineTo(conn.to.x, conn.to.y);
                ctx.stroke();
            });

            // 集成后的系统
            ctx.fillStyle = 'rgba(253, 121, 168, 0.2)';
            ctx.fillRect(80, 80, 540, 250);
            ctx.strokeStyle = '#fd79a8';
            ctx.lineWidth = 4;
            ctx.setLineDash([10, 5]);
            ctx.strokeRect(80, 80, 540, 250);
            ctx.setLineDash([]);

            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('集成系统', 350, 360);

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 连接多个构件', 50, 380);
            ctx.fillText('• 建立构件间通信', 200, 380);
            ctx.fillText('• 协调构件协作', 380, 380);
            ctx.fillText('• 形成完整系统', 520, 380);
        }

        // 绘制扩展层次
        function drawExtensionLevel() {
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('扩展层次 - Extension', 350, 40);

            // 原有系统
            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(150, 100, 200, 120);
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 3;
            ctx.strokeRect(150, 100, 200, 120);
            
            ctx.fillStyle = '#495057';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('原有系统', 250, 165);

            // 扩展模块
            const extensions = [
                {x: 400, y: 80, name: '扩展A', color: '#00b894'},
                {x: 400, y: 140, name: '扩展B', color: '#00a085'},
                {x: 400, y: 200, name: '扩展C', color: '#00b894'}
            ];

            extensions.forEach(ext => {
                ctx.fillStyle = ext.color;
                ctx.fillRect(ext.x, ext.y, 80, 40);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(ext.x, ext.y, 80, 40);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(ext.name, ext.x + 40, ext.y + 25);
            });

            // 连接线
            extensions.forEach(ext => {
                ctx.strokeStyle = '#00b894';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(350, 160);
                ctx.lineTo(ext.x, ext.y + 20);
                ctx.stroke();
            });

            // 扩展后的系统
            ctx.fillStyle = 'rgba(0, 184, 148, 0.2)';
            ctx.fillRect(130, 70, 370, 180);
            ctx.strokeStyle = '#00b894';
            ctx.lineWidth = 4;
            ctx.setLineDash([10, 5]);
            ctx.strokeRect(130, 70, 370, 180);
            ctx.setLineDash([]);

            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('扩展系统', 315, 280);

            // 新功能标注
            ctx.fillStyle = '#e17055';
            ctx.fillRect(520, 120, 100, 30);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(520, 120, 100, 30);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('新增功能', 570, 140);

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 增加新功能', 100, 320);
            ctx.fillText('• 扩展构件能力', 250, 320);
            ctx.fillText('• 添加新接口', 400, 320);
            ctx.fillText('• 增强系统功能', 550, 320);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('C. 定制、集成和扩展')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画序列
                    demonstrateLevel('customization');
                    setTimeout(() => demonstrateLevel('integration'), 2000);
                    setTimeout(() => demonstrateLevel('extension'), 4000);
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateLevel('customization');
            
            // 自动演示序列
            setTimeout(() => demonstrateLevel('integration'), 3000);
            setTimeout(() => demonstrateLevel('extension'), 6000);
            setTimeout(() => demonstrateLevel('customization'), 9000);
        };
    </script>
</body>
</html>
