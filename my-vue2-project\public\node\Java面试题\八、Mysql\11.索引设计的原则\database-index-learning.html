<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库索引优化 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 100%;
            padding: 20px;
            margin: 0 auto;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px 20px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideUp 0.8s ease forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 40px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        h2 {
            color: #34495e;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .demo-canvas {
            width: 100%;
            height: 300px;
            border: 2px solid #ecf0f1;
            border-radius: 15px;
            margin: 20px 0;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .demo-canvas:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .game-score {
            text-align: center;
            font-size: 1.5rem;
            color: #2c3e50;
            margin: 20px 0;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #ecf0f1;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00b894, #00cec9);
            width: 0%;
            transition: width 1s ease;
        }

        @media (max-width: 768px) {
            h1 { font-size: 2rem; }
            h2 { font-size: 1.5rem; }
            .container { padding: 10px; }
            .section { padding: 20px 15px; margin: 20px 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 数据库索引优化大师</h1>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressBar"></div>
        </div>
        
        <div class="game-score">
            学习进度: <span id="score">0</span>/4 ⭐
        </div>

        <!-- 第一部分：WHERE子句索引 -->
        <div class="section">
            <h2>📍 规则一：WHERE子句中的列需要索引</h2>
            <div class="explanation">
                想象一下图书馆📚！如果你要找关于"数据库"的书，没有目录的话，你需要一本本翻找。
                但有了按主题分类的目录（索引），你就能快速定位到相关书籍！
            </div>
            <canvas class="demo-canvas" id="whereCanvas"></canvas>
            <button class="interactive-btn" onclick="startWhereDemo()">🎮 开始WHERE查询游戏</button>
        </div>

        <!-- 第二部分：基数与索引效果 -->
        <div class="section">
            <h2>🎲 规则二：基数小的列索引效果差</h2>
            <div class="explanation">
                基数就是不同值的数量！比如性别只有"男/女"两个值（基数=2），
                而身份证号每个人都不同（基数很大）。在基数小的列建索引就像用颜色分类书籍，
                只有红蓝两种颜色，分类效果不好！
            </div>
            <canvas class="demo-canvas" id="cardinalityCanvas"></canvas>
            <button class="interactive-btn" onclick="startCardinalityDemo()">🎯 体验基数分类游戏</button>
        </div>

        <!-- 第三部分：短索引优化 -->
        <div class="section">
            <h2>✂️ 规则三：使用短索引节省空间</h2>
            <div class="explanation">
                对长字符串建索引就像给每本书写完整的摘要作为目录，太占地方了！
                我们只需要记录前几个字符（前缀）就够了，就像书名的前几个字一样。
                这样既能快速查找，又节省存储空间！
            </div>
            <canvas class="demo-canvas" id="shortIndexCanvas"></canvas>
            <button class="interactive-btn" onclick="startShortIndexDemo()">📏 玩转短索引游戏</button>
        </div>

        <!-- 第四部分：避免过度索引 -->
        <div class="section">
            <h2>⚖️ 规则四：不要过度索引</h2>
            <div class="explanation">
                索引就像书的目录，目录太多反而会让书变厚，翻页变慢！
                每次更新数据时，所有相关索引都要更新，就像修改书内容时要同步更新所有目录。
                所以只保留真正需要的索引，让数据库保持轻盈高效！
            </div>
            <canvas class="demo-canvas" id="overIndexCanvas"></canvas>
            <button class="interactive-btn" onclick="startOverIndexDemo()">⚡ 索引平衡挑战</button>
        </div>

        <!-- 总结游戏 -->
        <div class="section">
            <h2>🏆 索引大师挑战</h2>
            <div class="explanation">
                恭喜你学完了所有索引优化规则！现在来测试一下你的掌握程度吧！
                点击下面的按钮开始最终挑战，成为真正的索引优化大师！
            </div>
            <canvas class="demo-canvas" id="finalGameCanvas"></canvas>
            <button class="interactive-btn" onclick="startFinalChallenge()">🎊 开始最终挑战</button>
        </div>
    </div>

    <script>
        let currentScore = 0;
        let gameStates = {
            where: false,
            cardinality: false,
            shortIndex: false,
            overIndex: false
        };

        // 更新进度
        function updateProgress() {
            const progress = (currentScore / 4) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('score').textContent = currentScore;
        }

        // WHERE子句演示
        function startWhereDemo() {
            const canvas = document.getElementById('whereCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let animationFrame = 0;
            let books = [];
            let searchTarget = '数据库';
            let foundBooks = [];

            // 初始化书籍
            for (let i = 0; i < 20; i++) {
                books.push({
                    x: Math.random() * (canvas.width - 60),
                    y: Math.random() * (canvas.height - 40),
                    title: Math.random() > 0.7 ? '数据库' : ['Java', 'Python', 'HTML', 'CSS'][Math.floor(Math.random() * 4)],
                    checked: false,
                    isTarget: Math.random() > 0.7
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制搜索框
                ctx.fillStyle = '#3498db';
                ctx.fillRect(10, 10, canvas.width - 20, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText('🔍 搜索: ' + searchTarget, 20, 35);

                // 绘制书籍
                books.forEach((book, index) => {
                    if (book.checked) {
                        ctx.fillStyle = book.title === searchTarget ? '#2ecc71' : '#e74c3c';
                    } else {
                        ctx.fillStyle = '#95a5a6';
                    }

                    ctx.fillRect(book.x, book.y, 60, 30);
                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.fillText(book.title, book.x + 5, book.y + 20);

                    // 动画检查过程
                    if (animationFrame > index * 10 && !book.checked) {
                        book.checked = true;
                        if (book.title === searchTarget) {
                            foundBooks.push(book);
                        }
                    }
                });

                // 显示结果
                if (foundBooks.length > 0) {
                    ctx.fillStyle = '#2ecc71';
                    ctx.font = '20px Arial';
                    ctx.fillText(`找到 ${foundBooks.length} 本相关书籍！`, 10, canvas.height - 20);
                }

                animationFrame++;

                if (animationFrame < books.length * 10 + 60) {
                    requestAnimationFrame(animate);
                } else if (!gameStates.where) {
                    gameStates.where = true;
                    currentScore++;
                    updateProgress();
                }
            }

            animate();
        }

        // 基数演示
        function startCardinalityDemo() {
            const canvas = document.getElementById('cardinalityCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let people = [];
            let animationFrame = 0;

            // 创建人员数据
            for (let i = 0; i < 30; i++) {
                people.push({
                    x: Math.random() * (canvas.width - 40),
                    y: Math.random() * (canvas.height - 40),
                    gender: Math.random() > 0.5 ? '男' : '女',
                    id: 'ID' + String(i).padStart(3, '0'),
                    color: Math.random() > 0.5 ? '#3498db' : '#e74c3c',
                    sorted: false
                });
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = '16px Arial';
                ctx.fillText('👥 按性别分类 (基数=2)', 10, 25);
                ctx.fillText('🆔 按身份证分类 (基数=30)', 10, canvas.height - 10);

                // 绘制人员
                people.forEach((person, index) => {
                    // 性别分类动画
                    if (animationFrame > index * 5) {
                        let targetX = person.gender === '男' ? 50 : 150;
                        person.x += (targetX - person.x) * 0.1;
                        person.y += (50 - person.y) * 0.1;
                    }

                    ctx.fillStyle = person.gender === '男' ? '#3498db' : '#e74c3c';
                    ctx.fillRect(person.x, person.y, 30, 30);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(person.gender, person.x + 5, person.y + 20);

                    // 身份证分类（每个人独立位置）
                    let idX = 20 + (index % 10) * 35;
                    let idY = canvas.height - 80 + Math.floor(index / 10) * 25;

                    ctx.fillStyle = '#95a5a6';
                    ctx.fillRect(idX, idY, 30, 20);
                    ctx.fillStyle = 'white';
                    ctx.font = '8px Arial';
                    ctx.fillText(person.id, idX + 2, idY + 15);
                });

                // 显示效果对比
                if (animationFrame > 200) {
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = '14px Arial';
                    ctx.fillText('性别分类：只有2组，查找效率低', 250, 80);
                    ctx.fillStyle = '#2ecc71';
                    ctx.fillText('身份证分类：30组，查找效率高', 250, canvas.height - 50);
                }

                animationFrame++;

                if (animationFrame < 300) {
                    requestAnimationFrame(animate);
                } else if (!gameStates.cardinality) {
                    gameStates.cardinality = true;
                    currentScore++;
                    updateProgress();
                }
            }

            animate();
        }

        // 短索引演示
        function startShortIndexDemo() {
            const canvas = document.getElementById('shortIndexCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let books = [
                { title: '数据库系统概论第五版', shortIndex: '数据库', space: 100 },
                { title: '计算机网络原理与应用', shortIndex: '计算机', space: 120 },
                { title: '操作系统设计与实现', shortIndex: '操作系', space: 110 },
                { title: '软件工程理论与实践', shortIndex: '软件工', space: 105 }
            ];

            let animationFrame = 0;
            let showComparison = false;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = '18px Arial';
                ctx.fillText('📚 完整索引 vs 短索引对比', 10, 30);

                books.forEach((book, index) => {
                    let y = 60 + index * 60;

                    // 完整索引
                    ctx.fillStyle = '#e74c3c';
                    let fullWidth = book.title.length * 8;
                    ctx.fillRect(20, y, fullWidth, 25);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(book.title, 25, y + 18);

                    // 短索引
                    if (animationFrame > index * 30) {
                        ctx.fillStyle = '#2ecc71';
                        let shortWidth = book.shortIndex.length * 8;
                        ctx.fillRect(20, y + 30, shortWidth, 25);
                        ctx.fillStyle = 'white';
                        ctx.fillText(book.shortIndex, 25, y + 48);

                        // 显示空间节省
                        ctx.fillStyle = '#3498db';
                        ctx.font = '10px Arial';
                        let saved = Math.round((1 - shortWidth / fullWidth) * 100);
                        ctx.fillText(`节省${saved}%空间`, shortWidth + 30, y + 48);
                    }
                });

                // 总结
                if (animationFrame > 150) {
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '16px Arial';
                    ctx.fillText('💡 短索引既能快速查找，又节省存储空间！', 20, canvas.height - 20);
                }

                animationFrame++;

                if (animationFrame < 200) {
                    requestAnimationFrame(animate);
                } else if (!gameStates.shortIndex) {
                    gameStates.shortIndex = true;
                    currentScore++;
                    updateProgress();
                }
            }

            animate();
        }

        // 过度索引演示
        function startOverIndexDemo() {
            const canvas = document.getElementById('overIndexCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let database = {
                size: 100,
                indexes: [],
                updateTime: 0
            };

            let animationFrame = 0;
            let addingIndexes = true;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制数据库
                ctx.fillStyle = '#3498db';
                ctx.fillRect(50, 50, database.size, 80);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText('数据库', 70, 95);

                // 添加索引动画
                if (addingIndexes && animationFrame % 30 === 0 && database.indexes.length < 10) {
                    database.indexes.push({
                        x: 200 + database.indexes.length * 40,
                        y: 60,
                        width: 30,
                        height: 60,
                        color: `hsl(${database.indexes.length * 36}, 70%, 60%)`
                    });
                    database.size += 10;
                    database.updateTime += 20;
                }

                // 绘制索引
                database.indexes.forEach((index, i) => {
                    ctx.fillStyle = index.color;
                    ctx.fillRect(index.x, index.y, index.width, index.height);
                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.fillText(`索引${i+1}`, index.x + 2, index.y + 35);
                });

                // 显示性能影响
                ctx.fillStyle = '#2c3e50';
                ctx.font = '16px Arial';
                ctx.fillText(`数据库大小: ${database.size}MB`, 50, 160);
                ctx.fillText(`更新耗时: ${database.updateTime}ms`, 50, 180);

                // 性能警告
                if (database.indexes.length > 5) {
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = '18px Arial';
                    ctx.fillText('⚠️ 索引过多，性能下降！', 50, 220);
                }

                if (database.indexes.length >= 10) {
                    addingIndexes = false;
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = '20px Arial';
                    ctx.fillText('💥 系统过载！', 50, 250);
                }

                animationFrame++;

                if (animationFrame < 400) {
                    requestAnimationFrame(animate);
                } else if (!gameStates.overIndex) {
                    gameStates.overIndex = true;
                    currentScore++;
                    updateProgress();
                }
            }

            animate();
        }

        // 最终挑战
        function startFinalChallenge() {
            const canvas = document.getElementById('finalGameCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let questions = [
                { text: '在WHERE子句中的列需要索引', correct: true },
                { text: '性别列(男/女)适合建索引', correct: false },
                { text: '长字符串应该使用短索引', correct: true },
                { text: '索引越多越好', correct: false }
            ];

            let currentQuestion = 0;
            let score = 0;
            let gameComplete = false;

            function drawQuestion() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                if (gameComplete) {
                    ctx.fillStyle = '#2ecc71';
                    ctx.font = '24px Arial';
                    ctx.fillText('🎉 恭喜！你已经是索引大师了！', 20, canvas.height / 2);
                    ctx.font = '18px Arial';
                    ctx.fillText(`最终得分: ${score}/${questions.length}`, 20, canvas.height / 2 + 40);
                    return;
                }

                let question = questions[currentQuestion];

                // 绘制问题
                ctx.fillStyle = '#2c3e50';
                ctx.font = '18px Arial';
                ctx.fillText(`问题 ${currentQuestion + 1}/${questions.length}:`, 20, 40);
                ctx.font = '16px Arial';
                ctx.fillText(question.text, 20, 80);

                // 绘制选项按钮
                ctx.fillStyle = '#2ecc71';
                ctx.fillRect(20, 120, 100, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText('✓ 正确', 35, 145);

                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(140, 120, 100, 40);
                ctx.fillStyle = 'white';
                ctx.fillText('✗ 错误', 155, 145);

                ctx.fillStyle = '#3498db';
                ctx.font = '14px Arial';
                ctx.fillText(`当前得分: ${score}`, 20, canvas.height - 20);
            }

            canvas.onclick = function(e) {
                if (gameComplete) return;

                let rect = canvas.getBoundingClientRect();
                let x = e.clientX - rect.left;
                let y = e.clientY - rect.top;

                let question = questions[currentQuestion];

                // 检查点击位置
                if (y >= 120 && y <= 160) {
                    let answer;
                    if (x >= 20 && x <= 120) {
                        answer = true; // 点击正确
                    } else if (x >= 140 && x <= 240) {
                        answer = false; // 点击错误
                    } else {
                        return;
                    }

                    if (answer === question.correct) {
                        score++;
                    }

                    currentQuestion++;

                    if (currentQuestion >= questions.length) {
                        gameComplete = true;
                    }

                    drawQuestion();
                }
            };

            drawQuestion();
        }

        // 初始化
        updateProgress();
    </script>
</body>
</html>
