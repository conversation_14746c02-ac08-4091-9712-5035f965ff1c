# 移动端功能说明

## 新增的移动端支持功能

### 1. 响应式布局
- 自动检测设备类型（桌面/移动端）
- 移动端采用全屏布局，隐藏桌面端侧边栏
- 添加移动端专用的顶部导航栏

### 2. 侧边栏优化
- 移动端侧边栏改为抽屉式设计
- 支持手势滑动打开/关闭侧边栏
- 添加遮罩层和关闭按钮
- 选择菜单项后自动关闭侧边栏

### 3. 触摸手势支持
- **左右滑动切换文件**：在阅读器页面左右滑动可切换上一个/下一个文件
- **边缘滑动打开侧边栏**：从左边缘向右滑动打开侧边栏
- **滑动关闭侧边栏**：向左滑动关闭侧边栏

### 4. 移动端底部导航栏
- 固定在底部的导航控制栏
- 包含上一个、播放/暂停、下一个按钮
- 支持安全区域适配（iPhone等设备）
- 触摸反馈优化

### 5. 沉浸式阅读模式
- 移动端专用的全屏阅读模式
- 隐藏所有UI元素，专注阅读体验
- 浮动控制按钮提供基本操作
- 一键切换沉浸/正常模式

### 6. 移动端UI优化
- 调整按钮大小适合触摸操作
- 优化字体大小和间距
- 简化复杂控件（如画中画功能在移动端隐藏）
- 对话框自适应移动端屏幕

### 7. 性能优化
- 禁用点击高亮效果
- 优化滚动性能
- 减少不必要的动画
- 触摸事件优化

## 使用方法

### 基本操作
1. **打开侧边栏**：点击左上角菜单按钮或从左边缘向右滑动
2. **切换文件**：在阅读器中左右滑动或使用底部导航按钮
3. **轮播控制**：使用底部导航栏的播放/暂停按钮
4. **沉浸阅读**：点击"沉浸"按钮进入全屏阅读模式

### 手势操作
- **从左边缘向右滑动**：打开侧边栏（仅在文件列表页面）
- **向左滑动**：关闭侧边栏
- **在阅读器中左右滑动**：切换文件
- **点击遮罩层**：关闭侧边栏

### 沉浸式阅读模式
- 点击"沉浸"按钮进入全屏阅读
- 使用右上角浮动按钮进行操作
- 点击关闭按钮退出沉浸模式

## 兼容性
- 支持所有现代移动浏览器
- 针对iOS Safari和Android Chrome优化
- 支持PWA安装和使用
- 适配各种屏幕尺寸

## 技术特性
- CSS媒体查询实现响应式设计
- Vue.js响应式数据绑定
- 触摸事件处理
- CSS3动画和过渡效果
- 现代CSS特性（如safe-area-inset）
