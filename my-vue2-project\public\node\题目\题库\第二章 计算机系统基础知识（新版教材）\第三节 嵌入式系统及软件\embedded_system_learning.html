<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式实时系统学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 20px;
            letter-spacing: -1px;
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease-out;
        }

        .section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .section h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 30px;
            font-weight: 600;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            position: relative;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        canvas {
            display: block;
            width: 100%;
            background: #f8f9fa;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .btn.success {
            background: linear-gradient(135deg, #00b894, #00a085);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            font-size: 16px;
            line-height: 1.6;
            color: #2c3e50;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        }

        .task-timeline {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 20px 0;
        }

        .task-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .task-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .task-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .nav-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            border-bottom: 2px solid #e9ecef;
        }

        .nav-tab {
            padding: 12px 20px;
            background: none;
            border: none;
            border-bottom: 3px solid transparent;
            color: #6c757d;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .section {
                padding: 25px;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>嵌入式实时系统</h1>
            <p>交互式学习 · 动画演示 · 深度理解</p>
        </div>

        <div class="section">
            <h2>📋 题目概览</h2>
            <div class="explanation">
                <p>本题考查的是<span class="highlight">嵌入式实时系统</span>的设计与分析。系统需要处理多个不同周期的任务：</p>
                <div class="task-timeline">
                    <div class="task-item">
                        <div class="task-color" style="background: #e74c3c;"></div>
                        <div><strong>5ms任务</strong> - 双口存储器数据采集 (1024KB, 处理时间1ms)</div>
                    </div>
                    <div class="task-item">
                        <div class="task-color" style="background: #f39c12;"></div>
                        <div><strong>20ms任务</strong> - 422接口数据处理 (64B输入, 16B输出, 处理时间4ms)</div>
                    </div>
                    <div class="task-item">
                        <div class="task-color" style="background: #27ae60;"></div>
                        <div><strong>60ms任务</strong> - 多接口综合处理 (6B+28位输入, 处理时间2ms)</div>
                    </div>
                    <div class="task-item">
                        <div class="task-color" style="background: #3498db;"></div>
                        <div><strong>1s任务</strong> - 系统监控与状态记录 (处理时间5ms)</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 知识点学习</h2>
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="switchTab('concept')">基本概念</button>
                <button class="nav-tab" onclick="switchTab('rtos')">实时操作系统</button>
                <button class="nav-tab" onclick="switchTab('scheduling')">任务调度</button>
                <button class="nav-tab" onclick="switchTab('io')">I/O处理</button>
            </div>

            <div id="concept" class="tab-content active">
                <div class="canvas-container">
                    <canvas id="conceptCanvas" width="800" height="400"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="animateConcept()">🎬 播放动画</button>
                    <button class="btn secondary" onclick="resetConcept()">🔄 重置</button>
                </div>
                <div class="explanation">
                    <h3>嵌入式实时系统基本概念</h3>
                    <p><strong>实时性</strong>：系统必须在规定的时间内完成任务，超时即为失败。</p>
                    <p><strong>确定性</strong>：系统行为可预测，响应时间有上界。</p>
                    <p><strong>可靠性</strong>：系统必须稳定运行，不能出现故障。</p>
                </div>
            </div>

            <div id="rtos" class="tab-content">
                <div class="canvas-container">
                    <canvas id="rtosCanvas" width="800" height="400"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="animateRTOS()">🎬 RTOS优势演示</button>
                    <button class="btn secondary" onclick="compareBareMetalRTOS()">⚖️ 裸机 vs RTOS</button>
                </div>
                <div class="explanation">
                    <h3>为什么选择RTOS？</h3>
                    <p>✅ <strong>任务调度</strong>：自动管理多任务执行顺序</p>
                    <p>✅ <strong>资源管理</strong>：提供互斥、同步机制</p>
                    <p>✅ <strong>可靠性</strong>：成熟的商业产品，经过验证</p>
                    <p>✅ <strong>开发效率</strong>：丰富的API和工具支持</p>
                </div>
            </div>

            <div id="scheduling" class="tab-content">
                <div class="canvas-container">
                    <canvas id="schedulingCanvas" width="800" height="400"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="animateScheduling()">🎬 调度演示</button>
                    <button class="btn secondary" onclick="showPriorityAssignment()">📊 优先级分配</button>
                    <button class="btn success" onclick="analyzeSchedulability()">🔍 可调度性分析</button>
                </div>
                <div class="explanation">
                    <h3>任务调度策略</h3>
                    <p><strong>Rate Monotonic (RM)</strong>：周期越短，优先级越高</p>
                    <p>本题优先级：5ms > 20ms > 60ms > 1s</p>
                    <p><strong>可调度性条件</strong>：所有任务的CPU利用率之和 ≤ 调度上界</p>
                </div>
            </div>

            <div id="io" class="tab-content">
                <div class="canvas-container">
                    <canvas id="ioCanvas" width="800" height="400"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="animateIO()">🎬 I/O方式对比</button>
                    <button class="btn secondary" onclick="showInterruptFlow()">⚡ 中断流程</button>
                    <button class="btn success" onclick="showPollingFlow()">🔄 查询流程</button>
                </div>
                <div class="explanation">
                    <h3>I/O处理方式选择</h3>
                    <p><strong>中断方式</strong>：适用于422串口 - 数据到达时自动处理，CPU效率高</p>
                    <p><strong>查询方式</strong>：适用于双口存储器、离散量 - 直接内存访问，简单可靠</p>
                    <p><strong>定时查询</strong>：适用于A/D、D/A - 考虑转换时间，避免阻塞</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🧠 解题思路</h2>
            <div class="canvas-container">
                <canvas id="solutionCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showProblem1()">📝 问题1解析</button>
                <button class="btn" onclick="showProblem2()">📊 问题2解析</button>
                <button class="btn" onclick="showProblem3()">⚙️ 问题3解析</button>
                <button class="btn success" onclick="showCompleteTimeline()">🕒 完整时序图</button>
            </div>
            <div class="explanation">
                <div id="solutionText">
                    <h3>解题步骤</h3>
                    <p>1. <strong>理解需求</strong>：分析各任务的周期、处理时间和数据量</p>
                    <p>2. <strong>选择方案</strong>：比较裸机开发与RTOS的优缺点</p>
                    <p>3. <strong>性能评估</strong>：计算CPU利用率，验证可调度性</p>
                    <p>4. <strong>设计实现</strong>：确定优先级和I/O处理方式</p>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentAnimation = null;
        let animationSpeed = 1;

        // 工具函数
        function getCanvas(id) {
            const canvas = document.getElementById(id);
            const ctx = canvas.getContext('2d');
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
            return { canvas, ctx, width: rect.width, height: rect.height };
        }

        function clearCanvas(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);
        }

        function drawRoundedRect(ctx, x, y, width, height, radius, fillColor, strokeColor = null) {
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, radius);
            if (fillColor) {
                ctx.fillStyle = fillColor;
                ctx.fill();
            }
            if (strokeColor) {
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        }

        function drawText(ctx, text, x, y, font = '16px Arial', color = '#2c3e50', align = 'left') {
            ctx.font = font;
            ctx.fillStyle = color;
            ctx.textAlign = align;
            ctx.fillText(text, x, y);
        }

        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
        }

        // 基本概念动画
        function animateConcept() {
            const { ctx, width, height } = getCanvas('conceptCanvas');
            clearCanvas(ctx, width, height);
            
            let frame = 0;
            const maxFrames = 180;
            
            function animate() {
                clearCanvas(ctx, width, height);
                
                // 绘制实时系统核心概念
                const centerX = width / 2;
                const centerY = height / 2;
                const radius = Math.min(width, height) * 0.15;
                
                // 中心圆 - 实时系统
                const pulse = 1 + 0.1 * Math.sin(frame * 0.1);
                drawRoundedRect(ctx, centerX - radius * pulse, centerY - radius * pulse, 
                               radius * 2 * pulse, radius * 2 * pulse, radius * pulse, 
                               '#667eea', '#764ba2');
                drawText(ctx, '实时系统', centerX, centerY + 5, '18px Arial', 'white', 'center');
                
                // 三个关键特性
                const concepts = [
                    { name: '实时性', color: '#e74c3c', angle: 0 },
                    { name: '确定性', color: '#f39c12', angle: 2 * Math.PI / 3 },
                    { name: '可靠性', color: '#27ae60', angle: 4 * Math.PI / 3 }
                ];
                
                concepts.forEach((concept, index) => {
                    const delay = index * 20;
                    if (frame > delay) {
                        const progress = Math.min((frame - delay) / 60, 1);
                        const distance = 120 * progress;
                        const x = centerX + Math.cos(concept.angle) * distance;
                        const y = centerY + Math.sin(concept.angle) * distance;
                        
                        drawRoundedRect(ctx, x - 40, y - 20, 80, 40, 20, concept.color);
                        drawText(ctx, concept.name, x, y + 5, '14px Arial', 'white', 'center');
                        
                        // 连接线
                        ctx.beginPath();
                        ctx.moveTo(centerX + Math.cos(concept.angle) * radius, 
                                  centerY + Math.sin(concept.angle) * radius);
                        ctx.lineTo(x, y);
                        ctx.strokeStyle = concept.color;
                        ctx.lineWidth = 2;
                        ctx.stroke();
                    }
                });
                
                frame++;
                if (frame < maxFrames) {
                    requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function resetConcept() {
            const { ctx, width, height } = getCanvas('conceptCanvas');
            clearCanvas(ctx, width, height);
            
            // 绘制静态状态
            const centerX = width / 2;
            const centerY = height / 2;
            
            drawText(ctx, '点击"播放动画"开始学习', centerX, centerY, '20px Arial', '#6c757d', 'center');
        }

        // RTOS动画
        function animateRTOS() {
            const { ctx, width, height } = getCanvas('rtosCanvas');
            clearCanvas(ctx, width, height);
            
            let frame = 0;
            const maxFrames = 240;
            
            function animate() {
                clearCanvas(ctx, width, height);
                
                // 绘制RTOS架构
                const layers = [
                    { name: '应用层', color: '#3498db', y: 50 },
                    { name: 'RTOS内核', color: '#e74c3c', y: 150 },
                    { name: '硬件抽象层', color: '#f39c12', y: 250 },
                    { name: '硬件层', color: '#95a5a6', y: 350 }
                ];
                
                layers.forEach((layer, index) => {
                    const delay = index * 30;
                    if (frame > delay) {
                        const progress = Math.min((frame - delay) / 60, 1);
                        const layerWidth = width * 0.8 * progress;
                        const x = (width - layerWidth) / 2;
                        
                        drawRoundedRect(ctx, x, layer.y - 25, layerWidth, 50, 10, layer.color);
                        drawText(ctx, layer.name, width / 2, layer.y + 5, '16px Arial', 'white', 'center');
                    }
                });
                
                // 绘制数据流
                if (frame > 120) {
                    const flowProgress = (frame - 120) / 60;
                    const arrowY = 80 + (270 * Math.min(flowProgress, 1));
                    
                    ctx.beginPath();
                    ctx.moveTo(width / 2, 80);
                    ctx.lineTo(width / 2, arrowY);
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    
                    // 箭头
                    if (flowProgress >= 1) {
                        ctx.beginPath();
                        ctx.moveTo(width / 2, arrowY);
                        ctx.lineTo(width / 2 - 10, arrowY - 10);
                        ctx.lineTo(width / 2 + 10, arrowY - 10);
                        ctx.closePath();
                        ctx.fillStyle = '#667eea';
                        ctx.fill();
                    }
                }
                
                frame++;
                if (frame < maxFrames) {
                    requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function compareBareMetalRTOS() {
            const { ctx, width, height } = getCanvas('rtosCanvas');
            clearCanvas(ctx, width, height);
            
            // 左侧：裸机开发
            drawRoundedRect(ctx, 20, 50, width/2 - 40, height - 100, 15, '#ffebee', '#e74c3c');
            drawText(ctx, '裸机开发', 30, 80, '18px Arial', '#e74c3c');
            
            const bareMetalPoints = [
                '❌ 手动任务调度',
                '❌ 复杂的资源管理',
                '❌ 开发周期长',
                '✅ 资源占用少',
                '✅ 实时性可控'
            ];
            
            bareMetalPoints.forEach((point, index) => {
                drawText(ctx, point, 30, 110 + index * 30, '14px Arial', '#2c3e50');
            });
            
            // 右侧：RTOS
            drawRoundedRect(ctx, width/2 + 20, 50, width/2 - 40, height - 100, 15, '#e8f5e8', '#27ae60');
            drawText(ctx, 'RTOS开发', width/2 + 30, 80, '18px Arial', '#27ae60');
            
            const rtosPoints = [
                '✅ 自动任务调度',
                '✅ 丰富的同步机制',
                '✅ 开发效率高',
                '✅ 成熟可靠',
                '⚠️ 资源开销'
            ];
            
            rtosPoints.forEach((point, index) => {
                drawText(ctx, point, width/2 + 30, 110 + index * 30, '14px Arial', '#2c3e50');
            });
        }

        // 任务调度动画
        function animateScheduling() {
            const { ctx, width, height } = getCanvas('schedulingCanvas');
            clearCanvas(ctx, width, height);
            
            let frame = 0;
            const maxFrames = 300;
            const timeScale = width / 100; // 100ms时间轴
            
            const tasks = [
                { name: '5ms任务', period: 5, execution: 1, color: '#e74c3c', priority: 1 },
                { name: '20ms任务', period: 20, execution: 4, color: '#f39c12', priority: 2 },
                { name: '60ms任务', period: 60, execution: 2, color: '#27ae60', priority: 3 },
                { name: '1s任务', period: 1000, execution: 5, color: '#3498db', priority: 4 }
            ];
            
            function animate() {
                clearCanvas(ctx, width, height);
                
                // 绘制时间轴
                ctx.beginPath();
                ctx.moveTo(50, height - 50);
                ctx.lineTo(width - 50, height - 50);
                ctx.strokeStyle = '#2c3e50';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // 时间刻度
                for (let t = 0; t <= 100; t += 10) {
                    const x = 50 + t * timeScale;
                    ctx.beginPath();
                    ctx.moveTo(x, height - 55);
                    ctx.lineTo(x, height - 45);
                    ctx.stroke();
                    drawText(ctx, t + 'ms', x, height - 30, '12px Arial', '#2c3e50', 'center');
                }
                
                // 绘制任务执行
                const currentTime = (frame / maxFrames) * 100;
                
                tasks.forEach((task, taskIndex) => {
                    const y = 80 + taskIndex * 60;
                    
                    // 任务名称
                    drawText(ctx, task.name, 10, y + 20, '14px Arial', task.color);
                    
                    // 绘制任务实例
                    for (let t = 0; t < currentTime; t += task.period) {
                        const startX = 50 + t * timeScale;
                        const endX = 50 + (t + task.execution) * timeScale;
                        
                        if (startX < width - 50) {
                            drawRoundedRect(ctx, startX, y, 
                                          Math.min(endX - startX, width - 50 - startX), 30, 
                                          5, task.color);
                            
                            // 优先级标识
                            drawText(ctx, 'P' + task.priority, startX + 5, y + 20, 
                                   '12px Arial', 'white');
                        }
                    }
                });
                
                // 当前时间线
                const currentX = 50 + currentTime * timeScale;
                ctx.beginPath();
                ctx.moveTo(currentX, 50);
                ctx.lineTo(currentX, height - 50);
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.stroke();
                
                frame++;
                if (frame < maxFrames) {
                    requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function showPriorityAssignment() {
            const { ctx, width, height } = getCanvas('schedulingCanvas');
            clearCanvas(ctx, width, height);
            
            drawText(ctx, 'Rate Monotonic 优先级分配', width/2, 40, '20px Arial', '#2c3e50', 'center');
            
            const tasks = [
                { name: '5ms任务', period: 5, priority: 1, color: '#e74c3c' },
                { name: '20ms任务', period: 20, priority: 2, color: '#f39c12' },
                { name: '60ms任务', period: 60, priority: 3, color: '#27ae60' },
                { name: '1s任务', period: 1000, priority: 4, color: '#3498db' }
            ];
            
            tasks.forEach((task, index) => {
                const y = 80 + index * 60;
                const barWidth = 200 - (task.priority - 1) * 40;
                
                drawRoundedRect(ctx, 100, y, barWidth, 40, 10, task.color);
                drawText(ctx, task.name, 110, y + 25, '16px Arial', 'white');
                drawText(ctx, `周期: ${task.period}ms`, 320, y + 15, '14px Arial', '#2c3e50');
                drawText(ctx, `优先级: ${task.priority}`, 320, y + 35, '14px Arial', '#2c3e50');
            });
            
            drawText(ctx, '规则：周期越短，优先级越高', width/2, height - 30, '16px Arial', '#667eea', 'center');
        }

        function analyzeSchedulability() {
            const { ctx, width, height } = getCanvas('schedulingCanvas');
            clearCanvas(ctx, width, height);
            
            drawText(ctx, '可调度性分析', width/2, 40, '20px Arial', '#2c3e50', 'center');
            
            // CPU利用率计算
            const tasks = [
                { name: '5ms任务', period: 5, execution: 1 },
                { name: '20ms任务', period: 20, execution: 4 },
                { name: '60ms任务', period: 60, execution: 2 },
                { name: '1s任务', period: 1000, execution: 5 }
            ];
            
            let totalUtilization = 0;
            
            tasks.forEach((task, index) => {
                const utilization = task.execution / task.period;
                totalUtilization += utilization;
                
                const y = 80 + index * 50;
                drawText(ctx, `${task.name}: ${task.execution}ms / ${task.period}ms = ${(utilization * 100).toFixed(1)}%`, 
                        50, y, '16px Arial', '#2c3e50');
            });
            
            const y = 80 + tasks.length * 50 + 20;
            drawText(ctx, `总CPU利用率: ${(totalUtilization * 100).toFixed(1)}%`, 50, y, '18px Arial', '#e74c3c');
            
            // RM调度上界 (n=4): 4*(2^(1/4) - 1) ≈ 75.7%
            const rmBound = 4 * (Math.pow(2, 1/4) - 1);
            drawText(ctx, `RM调度上界: ${(rmBound * 100).toFixed(1)}%`, 50, y + 30, '18px Arial', '#f39c12');
            
            const isSchedulable = totalUtilization <= rmBound;
            const resultColor = isSchedulable ? '#27ae60' : '#e74c3c';
            const resultText = isSchedulable ? '✅ 可调度' : '❌ 不可调度';
            
            drawText(ctx, resultText, 50, y + 60, '20px Arial', resultColor);
        }

        // I/O处理动画
        function animateIO() {
            const { ctx, width, height } = getCanvas('ioCanvas');
            clearCanvas(ctx, width, height);
            
            let frame = 0;
            const maxFrames = 200;
            
            function animate() {
                clearCanvas(ctx, width, height);
                
                // 绘制CPU
                drawRoundedRect(ctx, width/2 - 50, 50, 100, 60, 15, '#667eea');
                drawText(ctx, 'CPU', width/2, 85, '16px Arial', 'white', 'center');
                
                // 绘制不同I/O设备
                const devices = [
                    { name: '422串口', x: 100, y: 200, method: '中断', color: '#e74c3c' },
                    { name: '双口存储器', x: 300, y: 200, method: '查询', color: '#f39c12' },
                    { name: 'A/D转换器', x: 500, y: 200, method: '定时查询', color: '#27ae60' },
                    { name: '离散量接口', x: 700, y: 200, method: '查询', color: '#3498db' }
                ];
                
                devices.forEach((device, index) => {
                    const delay = index * 30;
                    if (frame > delay) {
                        // 设备框
                        drawRoundedRect(ctx, device.x - 60, device.y - 30, 120, 60, 10, device.color);
                        drawText(ctx, device.name, device.x, device.y - 5, '14px Arial', 'white', 'center');
                        drawText(ctx, device.method, device.x, device.y + 15, '12px Arial', 'white', 'center');
                        
                        // 连接线
                        ctx.beginPath();
                        ctx.moveTo(width/2, 110);
                        ctx.lineTo(device.x, device.y - 30);
                        ctx.strokeStyle = device.color;
                        ctx.lineWidth = 2;
                        ctx.stroke();
                        
                        // 数据流动画
                        if (frame > delay + 60) {
                            const flowFrame = (frame - delay - 60) % 60;
                            const progress = flowFrame / 60;
                            const flowX = width/2 + (device.x - width/2) * progress;
                            const flowY = 110 + (device.y - 30 - 110) * progress;
                            
                            ctx.beginPath();
                            ctx.arc(flowX, flowY, 5, 0, 2 * Math.PI);
                            ctx.fillStyle = device.color;
                            ctx.fill();
                        }
                    }
                });
                
                frame++;
                if (frame < maxFrames) {
                    requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function showInterruptFlow() {
            const { ctx, width, height } = getCanvas('ioCanvas');
            clearCanvas(ctx, width, height);
            
            drawText(ctx, '中断处理流程', width/2, 30, '20px Arial', '#2c3e50', 'center');
            
            const steps = [
                '1. 设备产生中断信号',
                '2. CPU保存当前状态',
                '3. 跳转到中断服务程序',
                '4. 处理I/O数据',
                '5. 恢复CPU状态',
                '6. 返回原程序执行'
            ];
            
            steps.forEach((step, index) => {
                const y = 70 + index * 50;
                const color = index % 2 === 0 ? '#e74c3c' : '#f39c12';
                
                drawRoundedRect(ctx, 50, y - 20, width - 100, 35, 10, color);
                drawText(ctx, step, 70, y, '16px Arial', 'white');
            });
            
            drawText(ctx, '优点：CPU效率高，响应及时', width/2, height - 30, '16px Arial', '#27ae60', 'center');
        }

        function showPollingFlow() {
            const { ctx, width, height } = getCanvas('ioCanvas');
            clearCanvas(ctx, width, height);
            
            drawText(ctx, '查询处理流程', width/2, 30, '20px Arial', '#2c3e50', 'center');
            
            const steps = [
                '1. CPU主动检查设备状态',
                '2. 判断是否有数据就绪',
                '3. 如果就绪，读取数据',
                '4. 如果未就绪，继续查询',
                '5. 处理读取的数据',
                '6. 继续下一轮查询'
            ];
            
            steps.forEach((step, index) => {
                const y = 70 + index * 50;
                const color = index % 2 === 0 ? '#3498db' : '#27ae60';
                
                drawRoundedRect(ctx, 50, y - 20, width - 100, 35, 10, color);
                drawText(ctx, step, 70, y, '16px Arial', 'white');
            });
            
            drawText(ctx, '优点：简单可靠，适合高速设备', width/2, height - 30, '16px Arial', '#667eea', 'center');
        }

        // 解题思路动画
        function showProblem1() {
            updateSolutionText('问题1：RTOS选择理由', [
                '✅ 任务调度：自动管理多周期任务的执行顺序',
                '✅ 资源管理：提供互斥、同步等通信机制',
                '✅ 可靠性：商业化产品，经过充分验证',
                '✅ 开发效率：丰富的API接口，缩短开发周期',
                '',
                '选择RTOS需考虑的性能指标：',
                '• 任务切换时间 < 系统要求',
                '• 中断响应时间 < 实时要求',
                '• 内核占用空间合理',
                '• API接口丰富，支持配置裁剪'
            ]);
            updateProgress(25);
        }

        function showProblem2() {
            updateSolutionText('问题2：时间性能评估', [
                '评估因素：',
                '1. 系统开销 ≤ 总运行时间的20%',
                '2. 所有任务在期限内完成（可调度性）',
                '3. 任务切换和中断响应时间',
                '',
                '本题时序关系：',
                '• 最小时间节拍：5ms',
                '• 最大工作周期：1s',
                '• 5ms任务优先级最高，首先执行',
                '• 按周期到达时间启动相应任务',
                '• 确保1s内所有任务按序完成'
            ]);
            updateProgress(50);
        }

        function showProblem3() {
            updateSolutionText('问题3：系统设计方案', [
                '优先级分配策略：',
                '• Rate Monotonic：周期越短优先级越高',
                '• 5ms > 20ms > 60ms > 1s',
                '',
                'I/O处理方式选择：',
                '• 422串口 → 中断方式（数据到达驱动）',
                '• 双口存储器 → 查询方式（直接内存访问）',
                '• 离散量接口 → 查询方式（简单可靠）',
                '• A/D、D/A → 定时查询（考虑转换时间）'
            ]);
            updateProgress(75);
        }

        function showCompleteTimeline() {
            const { ctx, width, height } = getCanvas('solutionCanvas');
            clearCanvas(ctx, width, height);
            
            drawText(ctx, '完整系统时序图', width/2, 30, '20px Arial', '#2c3e50', 'center');
            
            // 绘制时间轴 (0-100ms)
            const timeScale = (width - 100) / 100;
            
            ctx.beginPath();
            ctx.moveTo(50, height - 50);
            ctx.lineTo(width - 50, height - 50);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 时间刻度
            for (let t = 0; t <= 100; t += 20) {
                const x = 50 + t * timeScale;
                ctx.beginPath();
                ctx.moveTo(x, height - 55);
                ctx.lineTo(x, height - 45);
                ctx.stroke();
                drawText(ctx, t + 'ms', x, height - 30, '12px Arial', '#2c3e50', 'center');
            }
            
            // 绘制任务执行时序
            const tasks = [
                { name: '5ms任务', periods: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95], 
                  duration: 1, color: '#e74c3c', y: 80 },
                { name: '20ms任务', periods: [0, 20, 40, 60, 80], duration: 4, color: '#f39c12', y: 140 },
                { name: '60ms任务', periods: [0, 60], duration: 2, color: '#27ae60', y: 200 },
                { name: '1s任务', periods: [0], duration: 5, color: '#3498db', y: 260 }
            ];
            
            tasks.forEach(task => {
                // 任务名称
                drawText(ctx, task.name, 10, task.y + 20, '14px Arial', task.color);
                
                // 绘制任务实例
                task.periods.forEach(period => {
                    if (period <= 100) {
                        const startX = 50 + period * timeScale;
                        const endX = 50 + (period + task.duration) * timeScale;
                        
                        drawRoundedRect(ctx, startX, task.y, endX - startX, 30, 5, task.color);
                        drawText(ctx, task.duration + 'ms', startX + 5, task.y + 20, '10px Arial', 'white');
                    }
                });
            });
            
            updateProgress(100);
        }

        function updateSolutionText(title, content) {
            const solutionText = document.getElementById('solutionText');
            solutionText.innerHTML = `<h3>${title}</h3>` + 
                content.map(line => `<p>${line}</p>`).join('');
        }

        function updateProgress(percentage) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = percentage + '%';
        }

        // 页面滚动动画
        function handleScroll() {
            const sections = document.querySelectorAll('.section');
            const windowHeight = window.innerHeight;
            
            sections.forEach(section => {
                const sectionTop = section.getBoundingClientRect().top;
                const sectionVisible = sectionTop < windowHeight * 0.8;
                
                if (sectionVisible) {
                    section.classList.add('visible');
                }
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化画布
            resetConcept();
            
            // 滚动监听
            window.addEventListener('scroll', handleScroll);
            handleScroll(); // 初始检查
            
            // 自动播放概念动画
            setTimeout(animateConcept, 1000);
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            // 重新初始化当前显示的画布
            const activeTab = document.querySelector('.tab-content.active');
            if (activeTab) {
                const canvas = activeTab.querySelector('canvas');
                if (canvas) {
                    // 触发重绘
                    const event = new Event('resize');
                    canvas.dispatchEvent(event);
                }
            }
        });
    </script>
</body>
</html>
