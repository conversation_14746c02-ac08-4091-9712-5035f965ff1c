<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机流水线学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 30px;
            color: #2c3e50;
            text-align: center;
        }

        .pipeline-container {
            position: relative;
            height: 400px;
            margin: 40px 0;
            border-radius: 16px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            overflow: hidden;
        }

        .stage {
            position: absolute;
            width: 180px;
            height: 80px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stage:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .stage-1 { background: linear-gradient(135deg, #ff6b6b, #ee5a52); top: 50px; left: 50px; }
        .stage-2 { background: linear-gradient(135deg, #4ecdc4, #44a08d); top: 50px; left: 250px; }
        .stage-3 { background: linear-gradient(135deg, #45b7d1, #96c93d); top: 50px; left: 450px; }
        .stage-4 { background: linear-gradient(135deg, #f9ca24, #f0932b); top: 50px; left: 650px; }
        .stage-5 { background: linear-gradient(135deg, #6c5ce7, #a29bfe); top: 50px; left: 850px; }

        .instruction {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-weight: 500;
        }

        .formula-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 16px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .quiz-section {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            padding: 40px;
            border-radius: 24px;
            margin-top: 40px;
        }

        .quiz-option {
            background: rgba(255,255,255,0.2);
            padding: 15px 25px;
            margin: 10px 0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(10px);
        }

        .quiz-option.selected {
            border-color: white;
            background: rgba(255,255,255,0.4);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .highlight {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 计算机流水线学习</h1>
            <p class="subtitle">通过动画和交互理解流水线的工作原理</p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 什么是流水线？</h2>
            <p style="font-size: 1.1rem; line-height: 1.8; text-align: center; color: #555;">
                想象一下汽车装配线：每个工人负责一个步骤，多辆汽车可以同时在不同阶段进行装配。<br>
                计算机流水线也是如此，将指令执行分成多个阶段，提高处理效率！
            </p>
        </div>

        <div class="section">
            <h2 class="section-title">🔧 5级流水线结构</h2>
            <div class="pipeline-container" id="pipelineContainer">
                <div class="stage stage-1" data-stage="1">
                    <div>取指令<br>2Δt</div>
                </div>
                <div class="stage stage-2" data-stage="2">
                    <div>分析指令<br>1Δt</div>
                </div>
                <div class="stage stage-3" data-stage="3">
                    <div>取操作数<br>3Δt</div>
                </div>
                <div class="stage stage-4" data-stage="4">
                    <div>运算<br>1Δt</div>
                </div>
                <div class="stage stage-5" data-stage="5">
                    <div>写回结果<br>2Δt</div>
                </div>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn btn-secondary" onclick="resetAnimation()">🔄 重置</button>
                <button class="btn btn-primary" onclick="addInstruction()">➕ 添加指令</button>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value" id="throughput">1/3Δt</div>
                    <div class="stat-label">最大吞吐率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="speedup">2.5</div>
                    <div class="stat-label">加速比 (10条指令)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="cycle">3Δt</div>
                    <div class="stat-label">流水线周期</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="instructions">0</div>
                    <div class="stat-label">当前指令数</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📐 关键公式</h2>
            <div class="formula-box">
                <div>🔹 流水线执行周期 = max(各阶段执行时间) = 3Δt</div>
                <div>🔹 最大吞吐率 = 1 / 流水线执行周期 = 1/3Δt</div>
                <div>🔹 流水线执行时间 = 首条指令时间 + (n-1) × 周期</div>
                <div>🔹 加速比 = 顺序执行时间 / 流水线执行时间</div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 style="text-align: center; margin-bottom: 30px;">🎯 测试题目</h2>
            <p style="font-size: 1.1rem; margin-bottom: 20px;">
                某计算机系统采用5级流水线结构，该流水线的最大吞吐率为：
            </p>
            <div class="quiz-option" onclick="selectOption(this, 'A')">A. 1/9Δt</div>
            <div class="quiz-option" onclick="selectOption(this, 'B')">B. 1/3Δt ✓</div>
            <div class="quiz-option" onclick="selectOption(this, 'C')">C. 1/2Δt</div>
            <div class="quiz-option" onclick="selectOption(this, 'D')">D. 1/1Δt</div>
            <div id="explanation" style="margin-top: 20px; padding: 20px; background: rgba(255,255,255,0.2); border-radius: 12px; display: none;">
                <strong>解析：</strong>流水线的最大吞吐率取决于最慢的阶段。在这个例子中，"取操作数"阶段需要3Δt，是最慢的，所以流水线周期为3Δt，最大吞吐率为1/3Δt。
            </div>
        </div>
    </div>

    <script>
        let instructionCount = 0;
        let animationRunning = false;
        const instructions = [];

        function startAnimation() {
            if (animationRunning) return;
            animationRunning = true;
            
            // 添加第一条指令开始演示
            if (instructions.length === 0) {
                addInstruction();
            }
            
            animateInstructions();
        }

        function addInstruction() {
            instructionCount++;
            const instruction = {
                id: instructionCount,
                stage: 0,
                element: createInstructionElement(instructionCount)
            };
            instructions.push(instruction);
            updateStats();
        }

        function createInstructionElement(id) {
            const element = document.createElement('div');
            element.className = 'instruction';
            element.textContent = id;
            element.style.left = '20px';
            element.style.top = '200px';
            document.getElementById('pipelineContainer').appendChild(element);
            return element;
        }

        function animateInstructions() {
            if (!animationRunning) return;
            
            // 移动所有指令到下一阶段
            instructions.forEach((instruction, index) => {
                if (instruction.stage < 5) {
                    instruction.stage++;
                    moveInstructionToStage(instruction, instruction.stage);
                }
            });
            
            // 移除完成的指令
            for (let i = instructions.length - 1; i >= 0; i--) {
                if (instructions[i].stage > 5) {
                    instructions[i].element.remove();
                    instructions.splice(i, 1);
                }
            }
            
            // 高亮当前最慢阶段
            highlightSlowestStage();
            
            // 继续动画
            if (instructions.length > 0 || animationRunning) {
                setTimeout(animateInstructions, 1500);
            } else {
                animationRunning = false;
            }
        }

        function moveInstructionToStage(instruction, stage) {
            const positions = [
                { x: 20, y: 200 },   // 起始位置
                { x: 130, y: 80 },   // 阶段1
                { x: 330, y: 80 },   // 阶段2
                { x: 530, y: 80 },   // 阶段3
                { x: 730, y: 80 },   // 阶段4
                { x: 930, y: 80 },   // 阶段5
                { x: 1100, y: 200 }  // 完成位置
            ];
            
            if (stage <= 5) {
                instruction.element.style.left = positions[stage].x + 'px';
                instruction.element.style.top = positions[stage].y + 'px';
                
                // 添加脉冲动画
                instruction.element.classList.add('highlight');
                setTimeout(() => {
                    instruction.element.classList.remove('highlight');
                }, 500);
            }
        }

        function highlightSlowestStage() {
            // 移除之前的高亮
            document.querySelectorAll('.stage').forEach(stage => {
                stage.classList.remove('highlight');
            });
            
            // 高亮最慢阶段（阶段3，取操作数）
            document.querySelector('.stage-3').classList.add('highlight');
        }

        function resetAnimation() {
            animationRunning = false;
            instructions.forEach(instruction => {
                instruction.element.remove();
            });
            instructions.length = 0;
            instructionCount = 0;
            updateStats();
            
            // 移除高亮
            document.querySelectorAll('.stage').forEach(stage => {
                stage.classList.remove('highlight');
            });
        }

        function updateStats() {
            document.getElementById('instructions').textContent = instructionCount;
            
            // 计算加速比（以10条指令为例）
            const n = 10;
            const sequentialTime = n * 9; // 9Δt per instruction
            const pipelineTime = 9 + (n - 1) * 3; // 9Δt + 9*3Δt = 36Δt
            const speedup = (sequentialTime / pipelineTime).toFixed(1);
            document.getElementById('speedup').textContent = speedup;
        }

        function selectOption(element, option) {
            // 移除之前的选择
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // 选择当前选项
            element.classList.add('selected');
            
            // 显示解析
            const explanation = document.getElementById('explanation');
            explanation.style.display = 'block';
            
            if (option === 'B') {
                explanation.innerHTML = '<strong>🎉 正确！</strong>流水线的最大吞吐率取决于最慢的阶段。在这个例子中，"取操作数"阶段需要3Δt，是最慢的，所以流水线周期为3Δt，最大吞吐率为1/3Δt。';
                explanation.style.background = 'rgba(76, 175, 80, 0.3)';
            } else {
                explanation.innerHTML = '<strong>❌ 不正确</strong>请重新思考：流水线的吞吐率由最慢的阶段决定。哪个阶段用时最长？';
                explanation.style.background = 'rgba(244, 67, 54, 0.3)';
            }
        }

        // 添加阶段点击事件
        document.querySelectorAll('.stage').forEach(stage => {
            stage.addEventListener('click', function() {
                const stageNum = this.dataset.stage;
                const times = ['2Δt', '1Δt', '3Δt', '1Δt', '2Δt'];
                const descriptions = [
                    '从内存中获取指令',
                    '解码指令，确定操作类型',
                    '获取操作所需的数据（最慢阶段）',
                    '执行算术或逻辑运算',
                    '将结果写回寄存器或内存'
                ];
                
                alert(`阶段${stageNum}：${descriptions[stageNum-1]}\n执行时间：${times[stageNum-1]}`);
            });
        });

        // 初始化
        updateStats();
    </script>
</body>
</html>
