<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPU数据总线宽度 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .explanation {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .quiz-container {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 30px;
            border-radius: 20px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            padding: 15px;
            background: white;
            border: 2px solid transparent;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title floating">CPU数据总线宽度</h1>
            <p class="subtitle">通过动画和交互学习计算机核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">什么是数据总线？</h2>
            <div class="explanation">
                <p><strong>数据总线</strong>就像是计算机内部的"高速公路"，负责在CPU和其他部件（如内存、硬盘）之间传输数据。</p>
            </div>
            <div class="canvas-container">
                <canvas id="busCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="startBusAnimation()">开始数据传输动画</button>
                <button class="btn btn-secondary" onclick="resetBusAnimation()">重置动画</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">数据总线宽度对比</h2>
            <div class="explanation">
                <p><strong>数据总线宽度</strong>指的是同时能传输多少位（bit）的数据。就像道路的车道数量一样，车道越多，同时通过的车辆就越多！</p>
            </div>
            <div class="canvas-container">
                <canvas id="widthCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="show8Bit()">8位总线演示</button>
                <button class="btn btn-secondary" onclick="show16Bit()">16位总线演示</button>
                <button class="btn btn-success" onclick="show32Bit()">32位总线演示</button>
                <button class="btn btn-primary" onclick="compareAll()">对比所有宽度</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">运算速度游戏</h2>
            <div class="explanation">
                <p>来玩个游戏！看看不同总线宽度如何影响数据传输速度。点击按钮传输数据包，观察哪个更快！</p>
            </div>
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="startRace()">开始传输竞赛</button>
                <button class="btn btn-secondary" onclick="resetRace()">重置竞赛</button>
            </div>
            <div id="raceResult" style="text-align: center; font-size: 1.2rem; margin-top: 20px; font-weight: bold;"></div>
        </div>

        <div class="section">
            <h2 class="section-title">知识要点总结</h2>
            <div class="explanation">
                <h3>🎯 核心概念：</h3>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li><strong>数据总线宽度</strong> = 同时传输的二进制位数</li>
                    <li><strong>宽度越大</strong> = 单位时间传输数据越多</li>
                    <li><strong>传输数据越多</strong> = 系统运算速度越快</li>
                </ul>

                <h3>❌ 数据总线宽度不影响：</h3>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>内存容量大小（由地址总线决定）</li>
                    <li>指令系统的指令数量（由CPU架构决定）</li>
                    <li>寄存器的宽度（由CPU设计决定）</li>
                </ul>
            </div>
        </div>

        <div class="quiz-container">
            <h2 class="section-title">互动测试</h2>
            <div class="quiz-question" id="quizQuestion">
                CPU中的数据总线宽度会影响（）？
            </div>
            <div class="quiz-options" id="quizOptions">
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 内存容量的大小</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">B. 系统的运算速度</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">C. 指令系统的指令数量</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 寄存器的宽度</div>
            </div>
            <div id="quizExplanation" style="display: none; margin-top: 20px; padding: 20px; background: rgba(255,255,255,0.8); border-radius: 10px;">
                <h4>✅ 正确答案：B. 系统的运算速度</h4>
                <p><strong>解析：</strong>CPU与其他部件交换数据时，用数据总线传输数据。数据总线宽度指同时传送的二进制位数，内存容量、指令系统中的指令数量和寄存器的位数与数据总线的宽度无关。数据总线宽度越大，单位时间内能进出CPU的数据就越多，系统的运算速度越快。</p>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 5;

        // 更新进度条
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 数据总线动画
        function startBusAnimation() {
            const canvas = document.getElementById('busCanvas');
            const ctx = canvas.getContext('2d');
            let animationId;
            let dataPackets = [];

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制CPU
            function drawCPU() {
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 150, 100, 100);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('CPU', 100, 205);
            }

            // 绘制内存
            function drawMemory() {
                ctx.fillStyle = '#764ba2';
                ctx.fillRect(650, 150, 100, 100);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('内存', 700, 205);
            }

            // 绘制数据总线
            function drawBus() {
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 8;
                ctx.beginPath();
                ctx.moveTo(150, 200);
                ctx.lineTo(650, 200);
                ctx.stroke();

                // 总线标签
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据总线', 400, 190);
            }

            // 数据包类
            class DataPacket {
                constructor() {
                    this.x = 150;
                    this.y = 200;
                    this.speed = 2;
                    this.color = `hsl(${Math.random() * 360}, 70%, 60%)`;
                }

                update() {
                    this.x += this.speed;
                }

                draw() {
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, 8, 0, Math.PI * 2);
                    ctx.fill();

                    // 数据标识
                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('01', this.x, this.y + 3);
                }
            }

            // 动画循环
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                drawCPU();
                drawMemory();
                drawBus();

                // 每30帧添加一个数据包
                if (Math.random() < 0.03) {
                    dataPackets.push(new DataPacket());
                }

                // 更新和绘制数据包
                dataPackets = dataPackets.filter(packet => {
                    packet.update();
                    packet.draw();
                    return packet.x < 650;
                });

                animationId = requestAnimationFrame(animate);
            }

            animate();
            currentStep = Math.max(currentStep, 1);
            updateProgress();

            // 5秒后停止动画
            setTimeout(() => {
                cancelAnimationFrame(animationId);
            }, 5000);
        }

        function resetBusAnimation() {
            const canvas = document.getElementById('busCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 数据总线宽度对比动画
        function drawBusWidth(canvas, bits, yOffset, label) {
            const ctx = canvas.getContext('2d');
            const startX = 100;
            const endX = 700;
            const lineSpacing = 15;

            // 绘制标签
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`${bits}位总线 (${label})`, startX, yOffset - 20);

            // 绘制多条线表示总线宽度
            for (let i = 0; i < bits / 8; i++) {
                ctx.strokeStyle = i < bits / 8 ? '#667eea' : '#ccc';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(startX, yOffset + i * lineSpacing);
                ctx.lineTo(endX, yOffset + i * lineSpacing);
                ctx.stroke();
            }

            // 绘制数据流动动画
            const time = Date.now() * 0.005;
            for (let i = 0; i < bits / 8; i++) {
                const x = startX + ((time + i * 0.5) % 1) * (endX - startX);
                ctx.fillStyle = `hsl(${200 + i * 30}, 70%, 60%)`;
                ctx.beginPath();
                ctx.arc(x, yOffset + i * lineSpacing, 6, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function show8Bit() {
            const canvas = document.getElementById('widthCanvas');
            const ctx = canvas.getContext('2d');

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawBusWidth(canvas, 8, 100, '较慢');
                requestAnimationFrame(animate);
            }
            animate();

            currentStep = Math.max(currentStep, 2);
            updateProgress();
        }

        function show16Bit() {
            const canvas = document.getElementById('widthCanvas');
            const ctx = canvas.getContext('2d');

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawBusWidth(canvas, 16, 100, '中等');
                requestAnimationFrame(animate);
            }
            animate();

            currentStep = Math.max(currentStep, 2);
            updateProgress();
        }

        function show32Bit() {
            const canvas = document.getElementById('widthCanvas');
            const ctx = canvas.getContext('2d');

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawBusWidth(canvas, 32, 100, '较快');
                requestAnimationFrame(animate);
            }
            animate();

            currentStep = Math.max(currentStep, 2);
            updateProgress();
        }

        function compareAll() {
            const canvas = document.getElementById('widthCanvas');
            const ctx = canvas.getContext('2d');

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawBusWidth(canvas, 8, 80, '较慢');
                drawBusWidth(canvas, 16, 200, '中等');
                drawBusWidth(canvas, 32, 350, '较快');
                requestAnimationFrame(animate);
            }
            animate();

            currentStep = Math.max(currentStep, 2);
            updateProgress();
        }

        // 传输竞赛游戏
        let raceAnimationId;
        let raceData = {
            bus8: { progress: 0, speed: 1, packets: 1 },
            bus16: { progress: 0, speed: 2, packets: 2 },
            bus32: { progress: 0, speed: 4, packets: 4 }
        };

        function startRace() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            // 重置竞赛数据
            raceData.bus8.progress = 0;
            raceData.bus16.progress = 0;
            raceData.bus32.progress = 0;

            document.getElementById('raceResult').innerHTML = '';

            function drawRace() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                const lanes = [
                    { name: '8位总线', data: raceData.bus8, y: 80, color: '#ff6b6b' },
                    { name: '16位总线', data: raceData.bus16, y: 180, color: '#4ecdc4' },
                    { name: '32位总线', data: raceData.bus32, y: 280, color: '#45b7d1' }
                ];

                lanes.forEach(lane => {
                    // 绘制跑道
                    ctx.strokeStyle = '#ddd';
                    ctx.lineWidth = 40;
                    ctx.beginPath();
                    ctx.moveTo(50, lane.y);
                    ctx.lineTo(750, lane.y);
                    ctx.stroke();

                    // 绘制标签
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'left';
                    ctx.fillText(lane.name, 10, lane.y + 5);

                    // 绘制数据包
                    for (let i = 0; i < lane.data.packets; i++) {
                        const x = 50 + lane.data.progress + i * 20;
                        if (x < 750) {
                            ctx.fillStyle = lane.color;
                            ctx.beginPath();
                            ctx.arc(x, lane.y - 10 + i * 10, 8, 0, Math.PI * 2);
                            ctx.fill();
                        }
                    }

                    // 更新进度
                    lane.data.progress += lane.data.speed;
                });

                // 检查获胜者
                const winner = lanes.find(lane => lane.data.progress >= 700);
                if (winner) {
                    document.getElementById('raceResult').innerHTML =
                        `🏆 ${winner.name}获胜！数据传输速度最快！`;
                    currentStep = Math.max(currentStep, 3);
                    updateProgress();
                    return;
                }

                raceAnimationId = requestAnimationFrame(drawRace);
            }

            drawRace();
        }

        function resetRace() {
            if (raceAnimationId) {
                cancelAnimationFrame(raceAnimationId);
            }
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('raceResult').innerHTML = '';
        }

        // 测试功能
        function selectAnswer(element, isCorrect) {
            // 移除所有选项的样式
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.classList.remove('correct', 'wrong');
                option.style.pointerEvents = 'none';
            });

            // 标记正确和错误答案
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('quizExplanation').style.display = 'block';
                currentStep = Math.max(currentStep, 4);
                updateProgress();

                // 添加庆祝动画
                element.style.animation = 'pulse 0.5s ease-in-out 3';

                setTimeout(() => {
                    alert('🎉 恭喜你答对了！你已经掌握了CPU数据总线宽度的核心概念！');
                    currentStep = 5;
                    updateProgress();
                }, 1000);
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                options.forEach(option => {
                    if (option.onclick.toString().includes('true')) {
                        option.classList.add('correct');
                    }
                });
                document.getElementById('quizExplanation').style.display = 'block';

                setTimeout(() => {
                    alert('❌ 答案不正确，请仔细阅读解析，理解数据总线宽度如何影响系统运算速度。');
                }, 500);
            }
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 添加一些初始动画效果
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                section.style.animationDelay = `${index * 0.2}s`;
            });

            // 初始化进度条
            updateProgress();

            // 添加鼠标悬停效果
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.05)';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            console.log('🎓 CPU数据总线宽度学习页面已加载完成！');
            console.log('💡 提示：尝试点击各种按钮来体验交互式学习！');
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case '1':
                    startBusAnimation();
                    break;
                case '2':
                    compareAll();
                    break;
                case '3':
                    startRace();
                    break;
                case 'r':
                case 'R':
                    resetBusAnimation();
                    resetRace();
                    break;
            }
        });
    </script>
</body>
</html>
