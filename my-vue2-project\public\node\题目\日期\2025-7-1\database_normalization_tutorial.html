<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库范式动画教程</title>
    <style>
        /* CSS 样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }

        h1, h2 {
            text-align: center;
            color: #1e3a8a;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .paradigm-card {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 25px;
            transition: transform 0.3s ease;
        }

        .paradigm-card:hover {
            transform: translateY(-5px);
        }

        .paradigm-card h2 {
            margin-top: 0;
            font-size: 1.8em;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .explanation {
            background-color: #eef2ff;
            border-left: 4px solid #4338ca;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }

        .demo {
            margin-top: 20px;
        }

        .demo h3 {
            color: #1d4ed8;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        th, td {
            border: 1px solid #cbd5e1;
            padding: 12px;
            text-align: center;
            transition: background-color 0.3s;
        }

        th {
            background-color: #dbeafe;
        }

        .pk {
            background-color: #fef08a;
            font-weight: bold;
        }

        .demo-tables {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            min-height: 200px; /* 保证容器高度，防止塌陷 */
        }
        
        .after-state {
            display: none; /* 默认隐藏 */
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .table-container {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .table-container.hidden {
            opacity: 0;
            transform: translateY(20px);
            position: absolute; /* 动画时脱离文档流 */
            pointer-events: none; /* 防止交互 */
        }
        
        .btn-container {
            text-align: center;
            margin-top: 20px;
        }

        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s, box-shadow 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        button:hover {
            background-color: #1d4ed8;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .note {
            font-size: 0.9em;
            color: #64748b;
            margin-top: 15px;
        }

    </style>
</head>
<body>

    <h1>数据库范式 交互式动画教程</h1>

    <div class="container">
        <!-- 第一范式 (1NF) -->
        <div class="paradigm-card" id="nf1">
            <h2>第一范式 (1NF): 属性的原子性</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 确保数据表中的每一列（属性）都是不可再分的原子值。简单说，就是单元格里不能再包含多个值。
            </p>
            <div class="demo">
                <div class="demo-tables">
                    <div class="before-state table-container">
                        <h3>优化前 (不满足1NF)</h3>
                        <table>
                            <thead>
                                <tr><th>订单ID</th><th>客户名</th><th style="background-color:#fecaca;">产品 (非原子)</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>101</td><td>张三</td><td>手机, 充电器</td></tr>
                                <tr><td>102</td><td>李四</td><td>电脑</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-container hidden">
                         <h3>优化后 (满足1NF)</h3>
                        <table>
                             <thead>
                                <tr><th>订单ID</th><th>客户名</th><th>产品</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>101</td><td>张三</td><td>手机</td></tr>
                                <tr><td>101</td><td>张三</td><td>充电器</td></tr>
                                <tr><td>102</td><td>李四</td><td>电脑</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf1">开始转换</button>
                </div>
            </div>
        </div>
        
        <!-- 第二范式 (2NF) -->
        <div class="paradigm-card" id="nf2">
            <h2>第二范式 (2NF): 消除部分依赖</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 在满足1NF的基础上，非主键列必须完全依赖于整个主键，而不是主键的一部分。这通常针对联合主键的表。
            </p>
            <div class="demo">
                 <div class="demo-tables">
                    <div class="before-state table-container">
                        <h3>优化前 (不满足2NF)</h3>
                        <p class="note">主键是 (学生ID, 课程ID)。但'学生姓名'只依赖于'学生ID'，这就是部分依赖。</p>
                        <table>
                            <thead>
                                <tr><th class="pk">学生ID</th><th class="pk">课程ID</th><th>课程名称</th><th>分数</th><th style="background-color:#fecaca;">学生姓名</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>1</td><td>C101</td><td>数学</td><td>90</td><td>张三</td></tr>
                                <tr><td>1</td><td>C102</td><td>物理</td><td>85</td><td>张三</td></tr>
                                <tr><td>2</td><td>C101</td><td>数学</td><td>92</td><td>李四</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-container hidden">
                        <h3>优化后 (满足2NF)</h3>
                        <p class="note">将'学生姓名'拆分到新表，消除了部分依赖。</p>
                        <div class="table-group">
                             <table>
                                <caption>成绩表</caption>
                                <thead>
                                    <tr><th class="pk">学生ID</th><th class="pk">课程ID</th><th>分数</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>1</td><td>C101</td><td>90</td></tr>
                                    <tr><td>1</td><td>C102</td><td>85</td></tr>
                                    <tr><td>2</td><td>C101</td><td>92</td></tr>
                                </tbody>
                            </table>
                             <table>
                                <caption>学生表</caption>
                                <thead>
                                    <tr><th class="pk">学生ID</th><th>学生姓名</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>1</td><td>张三</td></tr>
                                    <tr><td>2</td><td>李四</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf2">开始转换</button>
                </div>
            </div>
        </div>

        <!-- 第三范式 (3NF) -->
        <div class="paradigm-card" id="nf3">
            <h2>第三范式 (3NF): 消除传递依赖</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 在满足2NF的基础上，任何非主键列都不能依赖于其他非主键列。也就是说，不能存在 A -> B -> C 的关系 (A是主键, B,C是非主键)。
            </p>
            <div class="demo">
                 <div class="demo-tables">
                    <div class="before-state table-container">
                         <h3>优化前 (不满足3NF)</h3>
                         <p class="note">主键是'学生ID'。'系主任'依赖于'所在系'，而'所在系'依赖于'学生ID'。这就是传递依赖: 学生ID -> 所在系 -> 系主任。</p>
                        <table>
                            <thead>
                                <tr><th class="pk">学生ID</th><th>学生姓名</th><th>所在系</th><th style="background-color:#fecaca;">系主任</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>1</td><td>张三</td><td>计算机系</td><td>王教授</td></tr>
                                <tr><td>2</td><td>李四</td><td>物理系</td><td>刘教授</td></tr>
                                <tr><td>3</td><td>王五</td><td>计算机系</td><td>王教授</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-container hidden">
                        <h3>优化后 (满足3NF)</h3>
                        <p class="note">将'系'信息独立成表，消除了传递依赖。</p>
                        <div class="table-group">
                             <table>
                                <caption>学生表</caption>
                                <thead>
                                    <tr><th class="pk">学生ID</th><th>学生姓名</th><th>所在系</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>1</td><td>张三</td><td>计算机系</td></tr>
                                    <tr><td>2</td><td>李四</td><td>物理系</td></tr>
                                    <tr><td>3</td><td>王五</td><td>计算机系</td></tr>
                                </tbody>
                            </table>
                             <table>
                                <caption>院系表</caption>
                                <thead>
                                    <tr><th class="pk">所在系</th><th>系主任</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>计算机系</td><td>王教授</td></tr>
                                    <tr><td>物理系</td><td>刘教授</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf3">开始转换</button>
                </div>
            </div>
        </div>

         <!-- BCNF -->
        <div class="paradigm-card" id="bcnf">
            <h2>巴斯-科德范式 (BCNF)</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 在3NF基础上，要求每个决定因素（能决定其他属性的属性或属性组）都必须包含候选键。这是更严格的3NF。
            </p>
             <div class="demo">
                 <div class="demo-tables">
                    <div class="before-state table-container">
                         <h3>优化前 (满足3NF但不满足BCNF)</h3>
                         <p class="note">假设一个老师只教一门课，一门课可由多个老师教。候选键是(老师, 学生)。但存在'老师 -> 课程'的依赖，而'老师'不是候选键，只是候选键的一部分，所以不满足BCNF。</p>
                        <table>
                            <thead>
                                <tr><th class="pk">老师</th><th class="pk">学生</th><th style="background-color:#fecaca;">课程</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>王教授</td><td>学生A</td><td>物理</td></tr>
                                <tr><td>王教授</td><td>学生B</td><td>物理</td></tr>
                                <tr><td>李教授</td><td>学生C</td><td>化学</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-container hidden">
                        <h3>优化后 (满足BCNF)</h3>
                        <p class="note">分解成两个表，让每个决定因素都包含候选键。</p>
                        <div class="table-group">
                             <table>
                                <caption>师生关系表</caption>
                                <thead>
                                    <tr><th class="pk">老师</th><th class="pk">学生</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>王教授</td><td>学生A</td></tr>
                                    <tr><td>王教授</td><td>学生B</td></tr>
                                    <tr><td>李教授</td><td>学生C</td></tr>
                                </tbody>
                            </table>
                             <table>
                                <caption>教师课程表</caption>
                                <thead>
                                    <tr><th class="pk">老师</th><th>课程</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>王教授</td><td>物理</td></tr>
                                    <tr><td>李教授</td><td>化学</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="bcnf">开始转换</button>
                </div>
            </div>
        </div>

        <!-- 第四范式 (4NF) -->
        <div class="paradigm-card" id="nf4">
            <h2>第四范式 (4NF): 消除多值依赖</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 消除表中的多值依赖。简单说，如果一个表中的某一行，可以对应【多个独立的】属性集合，那就需要拆分。比如，一个老师可以教多种课程，同时可以负责多个项目，但"课程"和"项目"之间没有直接关系，它们都只和老师有关。
            </p>
            <div class="demo">
                <div class="demo-tables">
                    <div class="before-state table-container">
                        <h3>优化前 (不满足4NF)</h3>
                        <p class="note">为了记录王教授教'物理'和'化学'，同时负责'项目A'和'项目B'，我们被迫创建了 2x2=4 条记录来表示所有组合，这导致了严重的数据冗余。</p>
                        <table>
                            <thead>
                                <tr><th class="pk">老师</th><th style="background-color:#fecaca;">所教课程</th><th style="background-color:#fecaca;">负责项目</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>王教授</td><td>物理</td><td>项目A</td></tr>
                                <tr><td>王教授</td><td>物理</td><td>项目B</td></tr>
                                <tr><td>王教授</td><td>化学</td><td>项目A</td></tr>
                                <tr><td>王教授</td><td>化学</td><td>项目B</td></tr>
                                <tr><td>李教授</td><td>数学</td><td>项目C</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-container hidden">
                        <h3>优化后 (满足4NF)</h3>
                        <p class="note">我们将"课程"和"项目"这两个相互独立的多值属性拆分成两个表，彻底消除了多值依赖和数据冗余。</p>
                        <div class="table-group">
                             <table>
                                <caption>教师课程表</caption>
                                <thead>
                                    <tr><th class="pk">老师</th><th>所教课程</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>王教授</td><td>物理</td></tr>
                                    <tr><td>王教授</td><td>化学</td></tr>
                                    <tr><td>李教授</td><td>数学</td></tr>
                                </tbody>
                            </table>
                             <table>
                                <caption>教师项目表</caption>
                                <thead>
                                    <tr><th class="pk">老师</th><th>负责项目</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>王教授</td><td>项目A</td></tr>
                                    <tr><td>王教授</td><td>项目B</td></tr>
                                    <tr><td>李教授</td><td>项目C</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf4">开始转换</button>
                </div>
            </div>
        </div>

        <!-- 2NF vs 3NF 对比 -->
        <div class="paradigm-card" id="nf2-vs-nf3">
            <h2>2NF vs 3NF: 一步步看区别</h2>
            <p class="explanation">
                <strong>关键区别：</strong>2NF 解决的是【非主键】对【部分主键】的依赖（通常发生在联合主键的表中）。而 3NF 解决的是【非主键】对【另一个非主键】的依赖。
                <br>让我们通过一个例子，一步步将一个混乱的表规范到 3NF，来感受它们的区别。
            </p>
            <div class="demo">
                <div class="demo-tables">
                    <!-- 步骤 0: 原始状态 (不满足2NF和3NF) -->
                    <div class="step-0 table-container">
                        <h3>原始表 (不满足 2NF & 3NF)</h3>
                        <p class="note">主键是 (学生ID, 活动ID)。<br>
                        <span style="color:#c00;"><b>2NF问题:</b> '学生姓名' 和 '所在系' 只依赖于 '学生ID'，这是部分依赖。</span><br>
                        <span style="color:#e67e22;"><b>3NF问题:</b> '系主任' 依赖于 '所在系'，而'所在系'又依赖于'学生ID'，这是传递依赖。</span>
                        </p>
                        <table>
                            <thead>
                                <tr><th class="pk">学生ID</th><th class="pk">活动ID</th><th style="background-color:#fecaca;">学生姓名</th><th style="background-color:#fecaca;">所在系</th><th style="background-color:#ffe4e1;">系主任</th><th>活动名称</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>1</td><td>A1</td><td>张三</td><td>计算机系</td><td>王教授</td><td>迎新晚会</td></tr>
                                <tr><td>1</td><td>A2</td><td>张三</td><td>计算机系</td><td>王教授</td><td>编程大赛</td></tr>
                                <tr><td>2</td><td>A1</td><td>李四</td><td>物理系</td><td>刘教授</td><td>迎新晚会</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 步骤 1: 优化到 2NF -->
                    <div class="step-1 table-container hidden">
                        <h3>第一步: 优化到 2NF (已消除部分依赖)</h3>
                        <p class="note">我们将只依赖'学生ID'的列（学生姓名, 所在系, 系主任）和只依赖'活动ID'的列（活动名称）拆分出去，形成了三个表。现在每个表都不存在部分依赖了。<br>
                        <span style="color:#e67e22;"><b>新问题:</b> 看新的"学生信息表"，'系主任'依赖于'所在系'，传递依赖问题依然存在。</span>
                        </p>
                        <div class="table-group">
                            <table><caption>报名表</caption><thead><tr><th class="pk">学生ID</th><th class="pk">活动ID</th></tr></thead><tbody><tr><td>1</td><td>A1</td></tr><tr><td>1</td><td>A2</td></tr><tr><td>2</td><td>A1</td></tr></tbody></table>
                            <table><caption>活动表</caption><thead><tr><th class="pk">活动ID</th><th>活动名称</th></tr></thead><tbody><tr><td>A1</td><td>迎新晚会</td></tr><tr><td>A2</td><td>编程大赛</td></tr></tbody></table>
                            <table style="border: 2px solid #e67e22;"><caption>学生信息表 (不满足3NF)</caption><thead><tr><th class="pk">学生ID</th><th>学生姓名</th><th>所在系</th><th style="background-color:#ffe4e1;">系主任</th></tr></thead><tbody><tr><td>1</td><td>张三</td><td>计算机系</td><td>王教授</td></tr><tr><td>2</td><td>李四</td><td>物理系</td><td>刘教授</td></tr></tbody></table>
                        </div>
                    </div>

                    <!-- 步骤 2: 优化到 3NF -->
                    <div class="step-2 table-container hidden">
                        <h3>第二步: 优化到 3NF (已消除传递依赖)</h3>
                        <p class="note">我们继续将"学生信息表"拆分，把传递依赖的'系主任'也独立出去。现在所有的表都满足3NF了！数据冗余更少，结构更清晰。</p>
                        <div class="table-group">
                             <table><caption>报名表</caption><thead><tr><th class="pk">学生ID</th><th class="pk">活动ID</th></tr></thead><tbody><tr><td>1</td><td>A1</td></tr><tr><td>1</td><td>A2</td></tr><tr><td>2</td><td>A1</td></tr></tbody></table>
                             <table><caption>活动表</caption><thead><tr><th class="pk">活动ID</th><th>活动名称</th></tr></thead><tbody><tr><td>A1</td><td>迎新晚会</td></tr><tr><td>A2</td><td>编程大赛</td></tr></tbody></table>
                             <table><caption>学生表</caption><thead><tr><th class="pk">学生ID</th><th>学生姓名</th><th>所在系</th></tr></thead><tbody><tr><td>1</td><td>张三</td><td>计算机系</td></tr><tr><td>2</td><td>李四</td><td>物理系</td></tr></tbody></table>
                             <table><caption>院系表</caption><thead><tr><th class="pk">所在系</th><th>系主任</th></tr></thead><tbody><tr><td>计算机系</td><td>王教授</td></tr><tr><td>物理系</td><td>刘教授</td></tr></tbody></table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf2-vs-nf3" data-step="0">第一步: 优化到 2NF</button>
                </div>
            </div>
        </div>

    </div>

    <script>
        // JavaScript 逻辑
        document.querySelectorAll('button[data-target]').forEach(button => {
            button.addEventListener('click', () => {
                const targetId = button.getAttribute('data-target');
                
                // 处理新的多步骤卡片
                if (targetId === 'nf2-vs-nf3') {
                    const card = document.getElementById(targetId);
                    let currentStep = parseInt(button.getAttribute('data-step'), 10);
                    
                    const step0 = card.querySelector('.step-0');
                    const step1 = card.querySelector('.step-1');
                    const step2 = card.querySelector('.step-2');

                    // 隐藏所有步骤
                    [step0, step1, step2].forEach(s => s.classList.add('hidden'));

                    if (currentStep === 0) {
                        // 从 0 到 1
                        step1.classList.remove('hidden');
                        button.setAttribute('data-step', 1);
                        button.textContent = '第二步: 优化到 3NF';
                    } else if (currentStep === 1) {
                        // 从 1 到 2
                        step2.classList.remove('hidden');
                        button.setAttribute('data-step', 2);
                        button.textContent = '返回重置';
                    } else {
                        // 从 2 回到 0
                        step0.classList.remove('hidden');
                        button.setAttribute('data-step', 0);
                        button.textContent = '第一步: 优化到 2NF';
                    }
                    
                    // 为了平滑的淡入效果，我们需要用和之前类似的延时逻辑
                    // 简单的实现是直接切换，高级的实现需要更复杂的 setTimeout 链
                    // 这里为了代码清晰，我们采用直接切换，上面的 hidden 添加已经确保了元素先被隐藏
                    // 下面的 remove 会触发 transition
                    // (实际上，浏览器可能会将 add/remove 合并，导致无动画。一个简单的强制重绘 hack)
                    void card.offsetWidth; // 强制浏览器重绘

                    if (currentStep === 0) step1.classList.remove('hidden');
                    if (currentStep === 1) step2.classList.remove('hidden');
                    if (currentStep === 2) step0.classList.remove('hidden');
                    
                    //  上面的逻辑已经包含了remove.hidden，为了动画效果，重新组织一下
                    const steps = [step0, step1, step2];
                    const currentVisible = steps[currentStep];
                    let nextStepIndex;

                    if (currentStep === 0) nextStepIndex = 1;
                    else if (currentStep === 1) nextStepIndex = 2;
                    else nextStepIndex = 0;

                    const nextVisible = steps[nextStepIndex];

                    currentVisible.classList.add('hidden');
                    setTimeout(() => {
                        currentVisible.style.display = 'none';
                        nextVisible.style.display = 'grid'; // or 'block' etc.
                        
                        setTimeout(() => {
                            nextVisible.classList.remove('hidden');
                        }, 20); // 短暂延时确保 display 生效

                        button.setAttribute('data-step', nextStepIndex);
                        if (nextStepIndex === 0) button.textContent = '第一步: 优化到 2NF';
                        if (nextStepIndex === 1) button.textContent = '第二步: 优化到 3NF';
                        if (nextStepIndex === 2) button.textContent = '返回重置';

                    }, 500); // 等待淡出动画

                    return; // 结束这个按钮的处理
                }

                // --- 原有的其他按钮的逻辑 ---
                const card = document.getElementById(targetId);
                const beforeState = card.querySelector('.before-state');
                const afterState = card.querySelector('.after-state');
                const btnText = button.textContent;

                if (btnText === '开始转换') {
                    // 切换到优化后
                    beforeState.classList.add('hidden');
                    // 等待 'before' 动画完成
                    setTimeout(() => {
                        beforeState.style.display = 'none';
                        afterState.style.display = 'grid'; // 或 'block'
                         // 触发 'after' 淡入
                        setTimeout(() => {
                            afterState.classList.remove('hidden');
                        }, 50);
                    }, 500);
                    button.textContent = '返回';
                } else {
                    // 切换回优化前
                    afterState.classList.add('hidden');
                     // 等待 'after' 动画完成
                    setTimeout(() => {
                        afterState.style.display = 'none';
                        beforeState.style.display = 'block';
                        // 触发 'before' 淡入
                         setTimeout(() => {
                            beforeState.classList.remove('hidden');
                        }, 50);
                    }, 500);
                    button.textContent = '开始转换';
                }
            });
        });
    </script>

</body>
</html> 