<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SNMPv3 安全威胁交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f4f8;
            color: #333;
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            padding: 20px 40px;
            box-sizing: border-box;
        }
        h1, h2 {
            color: #1e3a8a;
            text-align: center;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        .question-box {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
            margin-top: 15px;
        }
        .option {
            padding: 12px 15px;
            border: 1px solid #cbd5e1;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            background-color: #fff;
        }
        .option:hover {
            background-color: #eef2ff;
            border-color: #4f46e5;
        }
        .option.correct {
            background-color: #dcfce7;
            border-color: #22c55e;
            font-weight: bold;
        }
        .option.incorrect {
            background-color: #fee2e2;
            border-color: #ef4444;
        }
        #interactive-area {
            text-align: center;
        }
        #simulationCanvas {
            background: #f1f5f9;
            border-radius: 8px;
            border: 1px solid #cbd5e1;
            margin-bottom: 15px;
        }
        .controls {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .control-btn {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.2s;
        }
        .control-btn:hover {
            background-color: #2563eb;
        }
        .control-btn.active {
            background-color: #1e3a8a;
            font-weight: bold;
        }
        #animation-explanation {
            min-height: 40px;
            padding: 10px;
            background-color: #f8fafc;
            border-radius: 6px;
            color: #1d4ed8;
            font-weight: 500;
            border: 1px dashed #93c5fd;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .explanation-box {
            margin-top: 20px;
            padding: 20px;
            background-color: #fefce8;
            border: 1px solid #fde047;
            border-radius: 8px;
        }
        .explanation-box ul {
            padding-left: 20px;
        }
        .explanation-box li {
            margin-bottom: 10px;
        }
        .explanation-box strong {
            color: #854d0e;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>SNMPv3 安全威胁交互式学习</h1>

    <div class="question-box">
        <h2>题目</h2>
        <p>在网络管理中要防止各种安全威胁。在SNMPv3中，标准"无法预防"的安全威胁是？</p>
        <div class="options">
            <div class="option" data-option="A">A. 篡改管理信息：通过改变传输中的SNMP报文实施未经授权的管理操作</div>
            <div class="option" data-option="B">B. 通信分析：第三方分析管理实体之间的通信规律，从而获取管理信息</div>
            <div class="option" data-option="C">C. 假冒合法用户：未经授权的用户冒充授权用户，企图实施管理操作</div>
            <div class="option" data-option="D">D. 消息泄露：SNMP引擎之间交换的信息被第三方偷听</div>
        </div>
    </div>

    <div id="interactive-area">
        <h2>动画演示</h2>
        <canvas id="simulationCanvas" width="800" height="350"></canvas>
        <div class="controls">
            <button class="control-btn active" data-scene="normal">正常通信</button>
            <button class="control-btn" data-scene="disclosure">演示: 消息泄露</button>
            <button class="control-btn" data-scene="modification">演示: 信息篡改</button>
            <button class="control-btn" data-scene="masquerade">演示: 用户假冒</button>
            <button class="control-btn" data-scene="analysis">演示: 通信分析</button>
        </div>
        <div id="animation-explanation">点击上方按钮，开始观看动画演示</div>
    </div>

    <div class="explanation-box">
        <h2>知识点总结</h2>
        <p>SNMPv3通过其"基于用户的安全模型（USM）"提供了强大的安全功能，主要防范以下威胁：</p>
        <ul>
            <li><strong>消息泄露 (Disclosure)</strong>: 防止窃听者看懂消息内容。SNMPv3使用加密（如DES、AES）来实现<strong>机密性</strong>。</li>
            <li><strong>信息篡改 (Modification)</strong>: 防止消息在传输中被修改。SNMPv3使用认证算法（如MD5、SHA）来确保<strong>完整性</strong>。</li>
            <li><strong>用户假冒 (Masquerade)</strong>: 防止无授权的来源冒充合法用户。SNMPv3通过严格的用户名和密钥来做<strong>身份认证</strong>。</li>
        </ul>
        <p>然而，SNMPv3的安全模型<strong>并未强制要求</strong>防范以下两种威胁：</p>
        <ul>
            <li><strong>拒绝服务 (Denial of Service)</strong>: 这种攻击形式多样，通常认为应由更广泛的网络管理策略来处理，而不是仅靠SNMP协议本身。</li>
            <li><strong>通信分析 (Traffic Analysis)</strong>: 即使消息内容被加密，攻击者仍然可以观察通信的"元数据"，比如谁在和谁通信、通信的频率、数据包的大小等。从这些规律中，攻击者可能推断出网络的运行状态（例如"网络在午夜12点有大量备份流量"）。隐藏这些通信模式非常困难且代价高昂，因此SNMPv3标准不要求防范它。<strong>这就是题目的正确答案。</strong></li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('simulationCanvas');
    const ctx = canvas.getContext('2d');
    const explanation = document.getElementById('animation-explanation');

    let scene = 'normal'; // current animation scene
    let frame = 0;

    const managerPos = { x: 100, y: canvas.height / 2 };
    const agentPos = { x: canvas.width - 100, y: canvas.height / 2 };
    const attackerPos = { x: canvas.width / 2, y: canvas.height - 50 };
    
    const packet = {
        x: managerPos.x,
        y: managerPos.y,
        size: 15,
        color: '#3b82f6',
        progress: 0,
        direction: 1, // 1: manager to agent, -1: agent to manager
        locked: false,
        tampered: false,
        valid: true,
        text: ''
    };

    const explanations = {
        normal: '正常通信：管理站(左)和代理设备(右)之间安全地交换信息。',
        disclosure: '【消息泄露】攻击者(下)尝试偷听，但SNMPv3加密了报文(带锁)，他什么也看不懂。',
        modification: '【信息篡改】攻击者尝试修改报文(变色)，但SNMPv3的完整性校验使其在接收端失效(红叉)。',
        masquerade: '【用户假冒】攻击者尝试发送伪造命令，但因没有正确凭证，被代理设备拒绝。',
        analysis: '【通信分析】攻击者不看内容，只观察通信规律，推断网络行为。SNMPv3无法阻止这种分析。'
    };

    function drawComputer(x, y, label) {
        ctx.fillStyle = '#4b5563';
        ctx.fillRect(x - 30, y - 20, 60, 40);
        ctx.fillStyle = '#e5e7eb';
        ctx.fillRect(x - 28, y - 18, 56, 30);
        ctx.fillStyle = '#4b5563';
        ctx.fillRect(x - 35, y + 20, 70, 5);
        ctx.fillRect(x - 5, y + 25, 10, 5);
        ctx.fillStyle = '#111827';
        ctx.font = '14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(label, x, y + 50);
    }

    function drawRouter(x, y, label) {
        ctx.fillStyle = '#4b5563';
        ctx.fillRect(x - 30, y - 15, 60, 30);
        ctx.beginPath();
        ctx.moveTo(x-15, y - 15);
        ctx.lineTo(x-20, y-25);
        ctx.lineTo(x+20, y-25);
        ctx.lineTo(x+15, y-15);
        ctx.closePath();
        ctx.fill();
        ctx.fillStyle = '#111827';
        ctx.font = '14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(label, x, y + 40);
    }
    
    function drawAttacker(x, y) {
        ctx.fillStyle = 'red';
        ctx.beginPath();
        ctx.arc(x, y - 10, 10, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillRect(x - 10, y, 20, 20);
        ctx.fillStyle = '#111827';
        ctx.font = '14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('攻击者', x, y + 40);
    }

    function drawPacket() {
        ctx.fillStyle = packet.color;
        ctx.fillRect(packet.x - packet.size, packet.y - packet.size / 2, packet.size * 2, packet.size);
        if (packet.locked) {
            ctx.fillStyle = 'gold';
            ctx.fillRect(packet.x-2, packet.y - 12, 4, 6);
            ctx.beginPath();
            ctx.arc(packet.x, packet.y - 12, 4, Math.PI, 0);
            ctx.stroke();
        }
        if (!packet.valid) {
            ctx.strokeStyle = 'red';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(packet.x - 10, packet.y - 10);
            ctx.lineTo(packet.x + 10, packet.y + 10);
            ctx.moveTo(packet.x + 10, packet.y - 10);
            ctx.lineTo(packet.x - 10, packet.y + 10);
            ctx.stroke();
            ctx.lineWidth = 1;
        }
        if (packet.text) {
             ctx.fillStyle = '#1e3a8a';
             ctx.font = '12px sans-serif';
             ctx.textAlign = 'center';
             ctx.fillText(packet.text, packet.x, packet.y + packet.size + 2);
        }
    }

    function drawAnalysisLines() {
        ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
        ctx.lineWidth = 1;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(attackerPos.x, attackerPos.y);
        ctx.lineTo(managerPos.x, managerPos.y);
        ctx.moveTo(attackerPos.x, attackerPos.y);
        ctx.lineTo(agentPos.x, agentPos.y);
        ctx.stroke();
        ctx.setLineDash([]);
        
        ctx.fillStyle = 'red';
        ctx.font = '14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('观察频率和时间...', attackerPos.x, attackerPos.y - 30);
    }
    
    function resetPacket() {
        packet.progress = 0;
        packet.direction = 1;
        packet.locked = false;
        packet.tampered = false;
        packet.valid = true;
        packet.color = '#3b82f6';
        packet.text = '';
    }

    function animate() {
        frame++;
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        drawComputer(managerPos.x, managerPos.y, '管理站 (Manager)');
        drawRouter(agentPos.x, agentPos.y, '代理设备 (Agent)');

        if (scene !== 'normal' && scene !== 'analysis') {
            drawAttacker(attackerPos.x, attackerPos.y);
        }
        if (scene === 'analysis') {
             drawAttacker(attackerPos.x, attackerPos.y);
             drawAnalysisLines();
        }

        // Packet animation
        packet.progress = (frame % 200) / 200;
        if (frame % 200 === 199) {
            packet.direction *= -1;
            // Reset states for next loop if needed
            if(scene === 'modification') packet.valid = true;
            if(scene === 'masquerade') packet.color = '#3b82f6';
        }
        
        let startPos = packet.direction === 1 ? managerPos : agentPos;
        let endPos = packet.direction === 1 ? agentPos : managerPos;
        packet.x = startPos.x + (endPos.x - startPos.x) * packet.progress;

        // Scene specific logic
        switch (scene) {
            case 'disclosure':
                packet.locked = true;
                if (packet.progress > 0.4 && packet.progress < 0.6) {
                    ctx.beginPath();
                    ctx.moveTo(packet.x, packet.y);
                    ctx.lineTo(attackerPos.x, attackerPos.y);
                    ctx.strokeStyle = 'rgba(255,0,0,0.5)';
                    ctx.stroke();
                    packet.text = "???";
                } else {
                    packet.text = '';
                }
                break;
            case 'modification':
                 if (packet.progress > 0.4 && packet.progress < 0.6) {
                    packet.color = '#f59e0b'; // Tampered color
                 } else {
                    packet.color = '#3b82f6';
                 }
                 if (packet.progress > 0.95) {
                    packet.valid = false;
                 }
                break;
            case 'masquerade':
                // Attacker sends the packet
                 startPos = attackerPos;
                 endPos = agentPos;
                 packet.x = startPos.x + (endPos.x - startPos.x) * packet.progress;
                 packet.y = startPos.y + (endPos.y - startPos.y) * packet.progress;
                 packet.color = '#ef4444'; // Fake packet color
                 if(packet.progress > 0.95) {
                    packet.valid = false;
                 }
                break;
        }

        drawPacket();
        requestAnimationFrame(animate);
    }

    // Controls
    const controlBtns = document.querySelectorAll('.control-btn');
    controlBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            scene = btn.dataset.scene;
            frame = 0;
            resetPacket();
            explanation.textContent = explanations[scene];
            controlBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
        });
    });

    // Options
    const options = document.querySelectorAll('.option');
    options.forEach(option => {
        option.addEventListener('click', () => {
            options.forEach(opt => {
                opt.classList.remove('correct', 'incorrect');
            });
            const selected = option.dataset.option;
            if (selected === 'B') {
                option.classList.add('correct');
            } else {
                option.classList.add('incorrect');
            }
            // Trigger corresponding animation
            const sceneMap = {A: 'modification', B: 'analysis', C: 'masquerade', D: 'disclosure'};
            document.querySelector(`.control-btn[data-scene="${sceneMap[selected]}"]`).click();
        });
    });

    // Initial state
    explanation.textContent = explanations.normal;
    animate();
});
</script>

</body>
</html> 