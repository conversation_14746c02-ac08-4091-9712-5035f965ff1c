<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机软件著作权学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .question-box h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d4aa;
            background: rgba(0, 212, 170, 0.3);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.3);
            animation: shake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        canvas {
            width: 100%;
            height: 500px;
            border-radius: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            line-height: 1.6;
        }

        .copyright-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .copyright-card {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .copyright-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(108, 92, 231, 0.3);
        }

        .copyright-card.protection {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .copyright-card.ownership {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .copyright-card.rights {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .copyright-card.infringement {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .copyright-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .copyright-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 3px 8px;
            border-radius: 5px;
            color: #2d3436;
            font-weight: bold;
        }

        .step {
            background: rgba(116, 185, 255, 0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateX(5px);
        }

        .timeline {
            position: relative;
            margin: 30px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            transform: translateX(-50%);
        }

        .timeline-item {
            position: relative;
            margin: 40px 0;
            width: 45%;
        }

        .timeline-item:nth-child(odd) {
            left: 0;
            text-align: right;
        }

        .timeline-item:nth-child(even) {
            left: 55%;
            text-align: left;
        }

        .timeline-content {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: relative;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            top: 20px;
            width: 20px;
            height: 20px;
            background: #667eea;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 0 0 4px #667eea;
        }

        .timeline-item:nth-child(odd)::before {
            right: -60px;
        }

        .timeline-item:nth-child(even)::before {
            left: -60px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #01a3a4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .copyright-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .concept-box {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
        }

        .concept-box h4 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .protection-timeline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .protection-step {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            flex: 1;
            margin: 10px;
            min-width: 150px;
            position: relative;
        }

        .protection-step::after {
            content: '→';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            color: #74b9ff;
        }

        .protection-step:last-child::after {
            display: none;
        }

        .law-box {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #fff;
        }

        .law-box h4 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚖️ 计算机软件著作权学习</h1>
            <p>探索软件知识产权的法律保护机制</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2>📝 考试题目</h2>
                <p><strong>以下关于计算机软件著作权的叙述中，正确的是（ ）。</strong></p>
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 软件著作权自软件开发完成之日生效
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 非法进行拷贝、发布或更改软件的人被称为软件盗版者
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 开发者在单位或组织中任职期间所开发软件的著作权应归个人所有
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 用户购买了具有版权的软件，则具有对该软件的使用权和复制权
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 什么是软件著作权？</h2>
            <div class="explanation">
                <p><span class="highlight">软件著作权</span>是知识产权的重要组成部分，采用<strong>"自动保护"原则</strong>，无须经过个别确认。就像你写了一首诗，从完成的那一刻起就自动拥有版权！</p>
                <p>🔑 <strong>核心特点：</strong>软件著作权自软件开发完成之日起产生，不需要申请或登记。</p>
            </div>
            
            <div class="concept-box">
                <h4>📜 著作权人享有的权利</h4>
                <p>• <strong>发表权：</strong>决定软件是否公开发表</p>
                <p>• <strong>开发者身份权：</strong>表明开发者身份的权利</p>
                <p>• <strong>使用权：</strong>自己使用软件的权利</p>
                <p>• <strong>使用许可权：</strong>许可他人使用软件的权利</p>
                <p>• <strong>获得报酬权：</strong>因许可使用而获得报酬的权利</p>
            </div>
        </div>

        <div class="section">
            <h2>🔧 软件著作权核心概念</h2>
            <div class="copyright-grid">
                <div class="copyright-card protection" onclick="showCopyrightDemo('protection')">
                    <span class="icon">🛡️</span>
                    <h3>自动保护</h3>
                    <p>开发完成即生效</p>
                    <p>无需申请或登记</p>
                </div>
                <div class="copyright-card ownership" onclick="showCopyrightDemo('ownership')">
                    <span class="icon">👤</span>
                    <h3>归属原则</h3>
                    <p>个人开发归个人</p>
                    <p>职务开发归单位</p>
                </div>
                <div class="copyright-card rights" onclick="showCopyrightDemo('rights')">
                    <span class="icon">📋</span>
                    <h3>使用权限</h3>
                    <p>购买获得使用权</p>
                    <p>不包括复制权</p>
                </div>
                <div class="copyright-card infringement" onclick="showCopyrightDemo('infringement')">
                    <span class="icon">⚠️</span>
                    <h3>侵权行为</h3>
                    <p>未经许可复制</p>
                    <p>承担法律责任</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 软件著作权保护机制演示</h2>
            <div class="canvas-container">
                <canvas id="copyrightCanvas"></canvas>
                <div class="copyright-indicator" id="copyrightIndicator">
                    🎯 点击按钮开始演示
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startProtectionDemo()">🛡️ 自动保护演示</button>
                <button class="btn" onclick="showOwnershipDemo()">👤 归属权演示</button>
                <button class="btn" onclick="showInfringementDemo()">⚠️ 侵权行为演示</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2>🔍 详细解析每个选项</h2>

            <div class="step">
                <h3>选项A：软件著作权自软件开发完成之日生效 ✅</h3>
                <p><strong>正确答案！</strong>这正是软件著作权的<span class="highlight">"自动保护"原则</span></p>
                <ul>
                    <li>🛡️ <strong>自动保护：</strong>著作权的取得无须经过个别确认</li>
                    <li>📅 <strong>生效时间：</strong>软件开发完成之日起产生</li>
                    <li>❌ <strong>无需申请：</strong>不需要申请、登记或其他手续</li>
                    <li>⚖️ <strong>法律依据：</strong>这是著作权法的基本原则</li>
                </ul>
                <div class="concept-box">
                    <h4>🔑 关键理解</h4>
                    <p>著作权是知识产权中的例外，因为它采用"自动保护"原则，这与专利权、商标权需要申请注册不同。</p>
                </div>
            </div>

            <div class="step">
                <h3>选项B：非法进行拷贝、发布或更改软件的人被称为软件盗版者 ❌</h3>
                <p><strong>错误原因：</strong>这个说法过于<span class="highlight">宽泛和不准确</span></p>
                <ul>
                    <li>⚠️ <strong>概念混淆：</strong>"软件盗版者"不是严格的法律术语</li>
                    <li>📋 <strong>正确表述：</strong>应该说是"软件侵权者"或"违法者"</li>
                    <li>🔍 <strong>行为定性：</strong>这些行为属于侵犯著作权的行为</li>
                    <li>⚖️ <strong>法律后果：</strong>承担民事、行政和刑事责任</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项C：开发者在单位或组织中任职期间所开发软件的著作权应归个人所有 ❌</h3>
                <p><strong>错误原因：</strong><span class="highlight">职务作品</span>的著作权归属有特殊规定</p>
                <ul>
                    <li>🏢 <strong>职务作品：</strong>在单位任职期间开发的软件通常属于职务作品</li>
                    <li>👤 <strong>归属原则：</strong>职务作品的著作权一般归单位所有</li>
                    <li>📋 <strong>合同约定：</strong>具体归属可能由劳动合同或开发合同约定</li>
                    <li>⚖️ <strong>法律依据：</strong>著作权法对职务作品有明确规定</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项D：用户购买了具有版权的软件，则具有对该软件的使用权和复制权 ❌</h3>
                <p><strong>错误原因：</strong>购买软件只获得<span class="highlight">使用权</span>，不包括<span class="highlight">复制权</span></p>
                <ul>
                    <li>✅ <strong>使用权：</strong>购买软件后可以正常使用软件功能</li>
                    <li>❌ <strong>复制权：</strong>复制权仍然属于著作权人，用户不能随意复制</li>
                    <li>📋 <strong>许可范围：</strong>用户只获得软件使用许可，不是著作权转让</li>
                    <li>⚠️ <strong>违法后果：</strong>未经许可复制软件属于侵权行为</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📅 软件著作权保护期限</h2>

            <div class="protection-timeline">
                <div class="protection-step">
                    <h4>👤 自然人</h4>
                    <p>终生 + 死后50年</p>
                    <p>截止12月31日</p>
                </div>
                <div class="protection-step">
                    <h4>👥 合作开发</h4>
                    <p>最后死亡者</p>
                    <p>死后50年</p>
                </div>
                <div class="protection-step">
                    <h4>🏢 法人组织</h4>
                    <p>首次发表后50年</p>
                    <p>未发表不保护</p>
                </div>
            </div>

            <div class="law-box">
                <h4>⚖️ 法律条文要点</h4>
                <p><strong>自然人软件著作权：</strong>保护期为自然人终生及其死亡后50年，截止于自然人死亡后第50年的12月31日。</p>
                <p><strong>法人软件著作权：</strong>保护期为50年，截止于软件首次发表后第50年的12月31日，但软件自开发完成之日起50年内未发表的不予保护。</p>
            </div>
        </div>

        <div class="section">
            <h2>⚠️ 软件侵权行为及责任</h2>

            <div class="concept-box">
                <h4>🚫 主要侵权行为</h4>
                <p>• <strong>未经许可复制：</strong>擅自复制他人软件</p>
                <p>• <strong>未经许可发行：</strong>擅自销售、传播他人软件</p>
                <p>• <strong>未经许可修改：</strong>擅自修改、翻译他人软件</p>
                <p>• <strong>未经许可出租：</strong>擅自出租他人软件</p>
            </div>

            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h4>⚖️ 民事责任</h4>
                        <p>停止侵害、消除影响、赔礼道歉、赔偿损失</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h4>🏛️ 行政责任</h4>
                        <p>没收违法所得、罚款、没收销毁侵权复制品</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <h4>🔒 刑事责任</h4>
                        <p>情节严重的，依法追究刑事责任</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 记忆技巧和考试要点</h2>

            <div class="explanation">
                <h3>🧠 记忆口诀</h3>
                <p style="font-size: 1.3rem; text-align: center; font-weight: bold; margin: 20px 0;">
                    "软件著作权，开发完成就生效，<br>
                    自动保护不申请，个人职务分归属"
                </p>

                <h3>🔑 关键词记忆法</h3>
                <p><strong>自动保护：</strong>"开发完成"、"自动生效"、"无需申请"</p>
                <p><strong>归属原则：</strong>"个人开发归个人"、"职务开发归单位"</p>
                <p><strong>使用权限：</strong>"购买获得使用权"、"不包括复制权"</p>
                <p><strong>侵权行为：</strong>"未经许可"、"复制发布"、"法律责任"</p>

                <h3>🎯 考试技巧</h3>
                <ul>
                    <li>看到"自软件开发完成之日生效" → <span class="highlight">正确</span></li>
                    <li>看到"需要申请"、"需要登记" → <span class="highlight">错误</span></li>
                    <li>看到"职务开发归个人" → <span class="highlight">错误</span></li>
                    <li>看到"购买软件获得复制权" → <span class="highlight">错误</span></li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎉 学习总结</h2>
            <div class="explanation">
                <h3>📚 核心知识点</h3>
                <ul>
                    <li><span class="highlight">自动保护原则</span>：软件著作权自开发完成之日起产生</li>
                    <li><span class="highlight">归属原则</span>：个人开发归个人，职务开发归单位</li>
                    <li><span class="highlight">权利内容</span>：发表权、身份权、使用权、许可权、报酬权</li>
                    <li><span class="highlight">侵权责任</span>：承担民事、行政、刑事三重责任</li>
                </ul>

                <h3>⚡ 实际应用</h3>
                <ul>
                    <li>软件开发公司的知识产权保护策略</li>
                    <li>程序员个人作品的版权归属问题</li>
                    <li>软件采购中的使用权限理解</li>
                    <li>软件盗版的法律风险防范</li>
                </ul>
            </div>

            <div class="controls">
                <button class="btn" onclick="reviewQuestion()">🔄 重新答题</button>
                <button class="btn" onclick="showSummary()">📋 显示总结</button>
            </div>
        </div>
    </div>

    <script>
        // Canvas相关变量
        const canvas = document.getElementById('copyrightCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;
        let currentDemo = 'none';

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 题目交互逻辑
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n解释：软件著作权采用"自动保护"原则，自软件开发完成之日起产生，无需申请或登记。这是著作权的重要特点。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    progressFill.style.width = '25%';
                    setTimeout(() => {
                        let hint = '';
                        switch(answer) {
                            case 'B':
                                hint = '这个说法过于宽泛，软件盗版有特定的法律定义，不是所有非法行为都叫盗版。';
                                break;
                            case 'C':
                                hint = '职务作品的著作权通常归单位所有，不是个人所有。';
                                break;
                            case 'D':
                                hint = '购买软件只获得使用权，不包括复制权，复制权仍属于著作权人。';
                                break;
                        }
                        alert('❌ 答案不正确！\n\n提示：' + hint + '\n\n记住：软件著作权的核心是"自动保护"原则！');
                    }, 500);
                }
            });
        });

        // 绘制开发者
        function drawDeveloper(x, y, label, active = false) {
            ctx.save();

            // 开发者头像
            ctx.fillStyle = active ? '#667eea' : '#bdc3c7';
            ctx.beginPath();
            ctx.arc(x, y - 10, 20, 0, Math.PI * 2);
            ctx.fill();

            // 身体
            ctx.fillRect(x - 15, y + 10, 30, 40);

            // 标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 70);

            ctx.restore();
        }

        // 绘制软件
        function drawSoftware(x, y, label, protected = false) {
            ctx.save();

            // 软件图标
            ctx.fillStyle = protected ? '#00d4aa' : '#74b9ff';
            ctx.fillRect(x - 25, y - 20, 50, 40);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 25, y - 20, 50, 40);

            // 保护盾牌
            if (protected) {
                ctx.fillStyle = '#00d4aa';
                ctx.font = '20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('🛡️', x + 30, y - 10);
            }

            // 标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 50);

            ctx.restore();
        }

        // 绘制时间线
        function drawTimeline(x, y, width, progress) {
            ctx.save();

            // 时间线背景
            ctx.strokeStyle = '#bdc3c7';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + width, y);
            ctx.stroke();

            // 进度
            ctx.strokeStyle = '#00d4aa';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + width * progress, y);
            ctx.stroke();

            // 关键点
            ctx.fillStyle = '#00d4aa';
            ctx.beginPath();
            ctx.arc(x + width * progress, y, 8, 0, Math.PI * 2);
            ctx.fill();

            ctx.restore();
        }

        // 自动保护演示
        function startProtectionDemo() {
            currentDemo = 'protection';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const copyrightIndicator = document.getElementById('copyrightIndicator');
            copyrightIndicator.textContent = '🛡️ 软件著作权自动保护机制';

            animateProtection();
        }

        function animateProtection() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            const step = Math.floor(animationStep / 60);
            const progress = (animationStep % 60) / 60;

            // 绘制开发者
            drawDeveloper(centerX - 150, centerY, '开发者', true);

            // 绘制开发过程
            if (step >= 0) {
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('正在开发软件...', centerX, centerY - 100);

                // 开发进度条
                drawTimeline(centerX - 100, centerY - 50, 200, Math.min(progress + step * 0.3, 1));
            }

            // 软件完成
            if (step >= 1) {
                drawSoftware(centerX, centerY, '软件', false);
            }

            // 自动获得保护
            if (step >= 2) {
                drawSoftware(centerX, centerY, '软件', true);

                // 保护效果
                ctx.strokeStyle = '#00d4aa';
                ctx.lineWidth = 3;
                ctx.globalAlpha = 0.6;
                for (let i = 1; i <= 3; i++) {
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, i * 20 + progress * 10, 0, Math.PI * 2);
                    ctx.stroke();
                }
                ctx.globalAlpha = 1;
            }

            // 完成说明
            if (step >= 3) {
                ctx.fillStyle = '#00d4aa';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('✅ 软件开发完成，自动获得著作权保护！', centerX, centerY + 120);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateProtection);
        }

        // 归属权演示
        function showOwnershipDemo() {
            currentDemo = 'ownership';
            if (animationId) cancelAnimationFrame(animationId);

            const copyrightIndicator = document.getElementById('copyrightIndicator');
            copyrightIndicator.textContent = '👤 软件著作权归属原则';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 个人开发情况
            drawDeveloper(centerX - 150, centerY - 80, '个人开发者', true);
            drawSoftware(centerX - 150, centerY + 40, '个人软件', true);

            // 连接线
            ctx.strokeStyle = '#00d4aa';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX - 150, centerY - 40);
            ctx.lineTo(centerX - 150, centerY + 20);
            ctx.stroke();

            // 职务开发情况
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(centerX + 100, centerY - 100, 100, 80);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(centerX + 100, centerY - 100, 100, 80);

            ctx.fillStyle = '#fff';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('公司', centerX + 150, centerY - 55);

            drawDeveloper(centerX + 150, centerY - 20, '员工开发者', true);
            drawSoftware(centerX + 150, centerY + 60, '职务软件', true);

            // 归属箭头
            ctx.strokeStyle = '#fd79a8';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX + 150, centerY + 20);
            ctx.lineTo(centerX + 150, centerY - 20);
            ctx.stroke();

            // 说明文字
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('个人开发 → 个人所有', centerX - 150, centerY + 100);
            ctx.fillText('职务开发 → 单位所有', centerX + 150, centerY + 120);
        }

        // 侵权行为演示
        function showInfringementDemo() {
            currentDemo = 'infringement';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const copyrightIndicator = document.getElementById('copyrightIndicator');
            copyrightIndicator.textContent = '⚠️ 软件侵权行为及后果';

            animateInfringement();
        }

        function animateInfringement() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            const step = Math.floor(animationStep / 60);

            // 原始软件和开发者
            drawDeveloper(centerX - 200, centerY - 50, '著作权人', true);
            drawSoftware(centerX - 200, centerY + 50, '原版软件', true);

            // 侵权者
            if (step >= 0) {
                drawDeveloper(centerX + 50, centerY - 50, '侵权者', false);
            }

            // 非法复制
            if (step >= 1) {
                ctx.strokeStyle = '#ff4757';
                ctx.lineWidth = 3;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(centerX - 175, centerY + 50);
                ctx.lineTo(centerX + 75, centerY + 50);
                ctx.stroke();
                ctx.setLineDash([]);

                ctx.fillStyle = '#ff4757';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('非法复制', centerX - 50, centerY + 40);
            }

            // 盗版软件
            if (step >= 2) {
                ctx.fillStyle = '#ff4757';
                ctx.fillRect(centerX + 25, centerY + 30, 50, 40);
                ctx.strokeStyle = '#2c3e50';
                ctx.lineWidth = 2;
                ctx.strokeRect(centerX + 25, centerY + 30, 50, 40);

                // 警告标志
                ctx.fillStyle = '#ff4757';
                ctx.font = '20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('⚠️', centerX + 80, centerY + 40);

                ctx.fillStyle = '#2c3e50';
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText('盗版软件', centerX + 50, centerY + 90);
            }

            // 法律后果
            if (step >= 3) {
                ctx.fillStyle = '#ff4757';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('⚖️ 承担民事、行政、刑事责任！', centerX, centerY + 140);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateInfringement);
        }

        // 概念演示
        function showCopyrightDemo(concept) {
            currentDemo = concept;
            if (animationId) cancelAnimationFrame(animationId);

            const copyrightIndicator = document.getElementById('copyrightIndicator');
            const descriptions = {
                'protection': '🛡️ 自动保护：软件开发完成即获得著作权',
                'ownership': '👤 归属原则：个人开发归个人，职务开发归单位',
                'rights': '📋 使用权限：购买软件只获得使用权，不包括复制权',
                'infringement': '⚠️ 侵权行为：未经许可复制、发布软件属于侵权'
            };
            copyrightIndicator.textContent = descriptions[concept];

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(descriptions[concept], centerX, centerY);
        }

        // 重置演示
        function resetDemo() {
            if (animationId) cancelAnimationFrame(animationId);
            currentDemo = 'none';
            animationStep = 0;

            const copyrightIndicator = document.getElementById('copyrightIndicator');
            copyrightIndicator.textContent = '🎯 点击按钮开始演示';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('选择上方按钮查看软件著作权演示', centerX, centerY);
        }

        // 重新答题功能
        function reviewQuestion() {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            document.getElementById('progressFill').style.width = '0%';

            document.querySelector('.question-box').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            setTimeout(() => {
                document.querySelector('.question-box').classList.add('pulse');
                setTimeout(() => {
                    document.querySelector('.question-box').classList.remove('pulse');
                }, 2000);
            }, 500);
        }

        // 显示总结
        function showSummary() {
            const summary = `
🎯 计算机软件著作权学习总结

✅ 正确答案：A - 软件著作权自软件开发完成之日生效

📚 核心概念：
• 软件著作权是知识产权的重要组成部分
• 采用"自动保护"原则，无须经过个别确认
• 自软件开发完成之日起产生，不需要申请或登记

🛡️ 自动保护原则：
• 开发完成即生效 ✅
• 无需申请登记 ✅
• 无需个别确认 ✅
• 这是著作权的重要特点

👤 著作权归属原则：
• 个人开发 → 个人所有
• 职务开发 → 单位所有
• 合作开发 → 共同所有
• 委托开发 → 合同约定

📋 著作权人享有的权利：
• 发表权：决定软件是否公开发表
• 开发者身份权：表明开发者身份
• 使用权：自己使用软件
• 使用许可权：许可他人使用
• 获得报酬权：因许可使用获得报酬

📅 保护期限：
• 自然人：终生 + 死后50年
• 合作开发：最后死亡者死后50年
• 法人组织：首次发表后50年

⚠️ 侵权行为及责任：
• 主要侵权行为：未经许可复制、发行、修改、出租
• 法律责任：民事责任 + 行政责任 + 刑事责任
• 购买软件只获得使用权，不包括复制权

🧠 记忆技巧：
• "软件著作权，开发完成就生效"
• "自动保护不申请，个人职务分归属"
• 关键词：自动保护、开发完成、无需申请

⚡ 考试要点：
• 看到"自软件开发完成之日生效" → 正确 ✅
• 看到"需要申请"、"需要登记" → 错误 ❌
• 看到"职务开发归个人" → 错误 ❌
• 看到"购买软件获得复制权" → 错误 ❌

🔑 关键理解：
软件著作权的核心是"自动保护"原则，这与专利权、
商标权需要申请注册完全不同。软件一旦开发完成，
就自动获得法律保护，这是著作权法的基本特点。

🎉 恭喜掌握软件著作权知识！
            `;

            alert(summary);
        }

        // 添加CSS动画类
        const style = document.createElement('style');
        style.textContent = `
            .pulse {
                animation: pulse 1s ease-in-out 3;
            }

            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.02);
                    box-shadow: 0 25px 50px rgba(255, 107, 107, 0.4);
                }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的欢迎提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                welcome.innerHTML = `
                    <h3>🌟 欢迎来到软件著作权学习世界！</h3>
                    <p>让我们一起探索软件知识产权的法律保护</p>
                    <button onclick="this.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 15px;
                        margin-top: 15px;
                        cursor: pointer;
                    ">开始学习 🚀</button>
                `;
                document.body.appendChild(welcome);
            }, 1000);
        });

        // 初始化
        resetDemo();
    </script>
</body>
</html>
