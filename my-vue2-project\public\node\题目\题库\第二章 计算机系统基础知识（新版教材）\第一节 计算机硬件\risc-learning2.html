<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RISC计算机原理 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: center;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .feature-card.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .feature-card.incorrect {
            border-color: #f44336;
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .feature-desc {
            font-size: 0.95rem;
            opacity: 0.8;
            line-height: 1.5;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 500;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .option.selected {
            background: rgba(255,255,255,0.4);
            border-color: white;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            font-weight: 500;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .result.show {
            opacity: 1;
        }

        .result.correct {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
        }

        .result.incorrect {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #f44336;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .thinking-bubble {
            position: relative;
            background: #f0f8ff;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .thinking-bubble::before {
            content: "💡";
            position: absolute;
            left: -15px;
            top: 15px;
            font-size: 1.5rem;
            background: white;
            border-radius: 50%;
            padding: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">RISC 计算机原理</h1>
            <p class="subtitle">精简指令系统计算机 - 交互式学习体验</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是RISC？</h2>
            <div class="canvas-container">
                <canvas id="riscCanvas" width="600" height="300"></canvas>
            </div>
            <div class="explanation">
                RISC（Reduced Instruction Set Computer）是精简指令系统计算机<br>
                点击上方动画了解RISC的核心理念
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">RISC的四大特点</h2>
            <div class="feature-grid" id="featureGrid">
                <div class="feature-card" data-feature="fixed">
                    <span class="feature-icon">📏</span>
                    <div class="feature-title">指令长度固定</div>
                    <div class="feature-desc">所有指令都是相同长度，通常32位，便于快速解码</div>
                </div>
                <div class="feature-card" data-feature="simple">
                    <span class="feature-icon">🎯</span>
                    <div class="feature-title">寻址方式简单</div>
                    <div class="feature-desc">寻址方式少且简单，不追求复杂功能</div>
                </div>
                <div class="feature-card" data-feature="registers">
                    <span class="feature-icon">🗃️</span>
                    <div class="feature-title">大量寄存器</div>
                    <div class="feature-desc">增加寄存器数目，减少内存访问次数</div>
                </div>
                <div class="feature-card" data-feature="hardwired">
                    <span class="feature-icon">⚡</span>
                    <div class="feature-title">硬布线控制</div>
                    <div class="feature-desc">用硬件电路实现指令解码，速度更快</div>
                </div>
            </div>
        </div>

        <div class="section quiz-section">
            <h2 class="section-title">现在来做题吧！</h2>
            <div class="quiz-question">
                RISC（精简指令系统计算机）的特点不包括：
            </div>
            <div class="options" id="quizOptions">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 指令长度固定，指令种类尽量少
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 寻址方式尽量丰富，指令功能尽可能强
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 增加寄存器数目，以减少访存次数
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 用硬布线电路实现指令解码，以尽快完成指令译码
                </div>
            </div>
            <div class="result" id="quizResult"></div>
        </div>

        <div class="section">
            <h2 class="section-title">解题思路</h2>
            <div class="thinking-bubble">
                <h3>🤔 如何分析这道题？</h3>
                <p>1. 首先理解RISC的核心理念：<strong>简化</strong></p>
                <p>2. 逐一分析每个选项是否符合"简化"原则</p>
                <p>3. 找出与RISC理念<strong>相反</strong>的选项</p>
            </div>
            <div class="canvas-container">
                <canvas id="analysisCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                点击上方动画查看详细的解题分析过程
            </div>
        </div>
    </div>

    <script>
        // RISC概念动画
        const riscCanvas = document.getElementById('riscCanvas');
        const riscCtx = riscCanvas.getContext('2d');
        let riscAnimationFrame = 0;
        let isRiscAnimating = false;

        function drawRiscAnimation() {
            riscCtx.clearRect(0, 0, riscCanvas.width, riscCanvas.height);

            // 背景
            const gradient = riscCtx.createLinearGradient(0, 0, riscCanvas.width, riscCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            riscCtx.fillStyle = gradient;
            riscCtx.fillRect(0, 0, riscCanvas.width, riscCanvas.height);

            // RISC标题
            riscCtx.fillStyle = '#333';
            riscCtx.font = 'bold 24px Arial';
            riscCtx.textAlign = 'center';
            riscCtx.fillText('RISC = 简化 + 高效', riscCanvas.width/2, 40);

            // 动画进度
            const progress = (riscAnimationFrame % 300) / 300;

            // 绘制简化的指令
            const instructionY = 100;
            const instructionWidth = 80;
            const instructionHeight = 30;

            for (let i = 0; i < 4; i++) {
                const x = 100 + i * 120;
                const alpha = Math.sin(progress * Math.PI * 2 + i * 0.5) * 0.3 + 0.7;

                riscCtx.fillStyle = `rgba(102, 126, 234, ${alpha})`;
                riscCtx.fillRect(x, instructionY, instructionWidth, instructionHeight);

                riscCtx.fillStyle = 'white';
                riscCtx.font = '12px Arial';
                riscCtx.textAlign = 'center';
                riscCtx.fillText('指令', x + instructionWidth/2, instructionY + 20);
            }

            // 绘制特点
            const features = ['固定长度', '简单寻址', '多寄存器', '硬件控制'];
            for (let i = 0; i < features.length; i++) {
                const x = 100 + i * 120;
                const y = 180;
                const bounce = Math.sin(progress * Math.PI * 4 + i * 0.8) * 10;

                riscCtx.fillStyle = '#28a745';
                riscCtx.fillRect(x, y + bounce, instructionWidth, 20);

                riscCtx.fillStyle = 'white';
                riscCtx.font = '10px Arial';
                riscCtx.textAlign = 'center';
                riscCtx.fillText(features[i], x + instructionWidth/2, y + bounce + 14);
            }

            // 效率箭头
            const arrowY = 240;
            const arrowProgress = progress * riscCanvas.width;

            riscCtx.strokeStyle = '#dc3545';
            riscCtx.lineWidth = 3;
            riscCtx.beginPath();
            riscCtx.moveTo(50, arrowY);
            riscCtx.lineTo(Math.min(arrowProgress, riscCanvas.width - 50), arrowY);
            riscCtx.stroke();

            if (arrowProgress > riscCanvas.width - 100) {
                riscCtx.fillStyle = '#dc3545';
                riscCtx.font = 'bold 16px Arial';
                riscCtx.textAlign = 'center';
                riscCtx.fillText('高效执行！', riscCanvas.width/2, arrowY + 30);
            }

            if (isRiscAnimating) {
                riscAnimationFrame++;
                requestAnimationFrame(drawRiscAnimation);
            }
        }

        riscCanvas.addEventListener('click', () => {
            if (!isRiscAnimating) {
                isRiscAnimating = true;
                riscAnimationFrame = 0;
                drawRiscAnimation();
                setTimeout(() => {
                    isRiscAnimating = false;
                }, 6000);
            }
        });

        // 初始绘制
        drawRiscAnimation();

        // 特点卡片交互
        const featureCards = document.querySelectorAll('.feature-card');
        featureCards.forEach(card => {
            card.addEventListener('click', () => {
                card.classList.add('pulse');
                setTimeout(() => {
                    card.classList.remove('pulse');
                }, 1000);
            });
        });

        // 题目交互
        const quizOptions = document.querySelectorAll('.option');
        const quizResult = document.getElementById('quizResult');
        let selectedAnswer = null;

        quizOptions.forEach(option => {
            option.addEventListener('click', () => {
                // 清除之前的选择
                quizOptions.forEach(opt => opt.classList.remove('selected'));

                // 标记当前选择
                option.classList.add('selected');
                selectedAnswer = option.dataset.answer;

                // 显示结果
                setTimeout(() => {
                    showQuizResult(selectedAnswer);
                }, 500);
            });
        });

        function showQuizResult(answer) {
            quizResult.classList.remove('show', 'correct', 'incorrect');

            if (answer === 'B') {
                quizResult.className = 'result show correct';
                quizResult.innerHTML = `
                    <h3>🎉 恭喜你答对了！</h3>
                    <p><strong>正确答案是B</strong></p>
                    <p>RISC追求简化，所以寻址方式应该<strong>简单</strong>，而不是丰富复杂</p>
                `;
            } else {
                quizResult.className = 'result show incorrect';
                quizResult.innerHTML = `
                    <h3>😅 再想想看</h3>
                    <p><strong>正确答案是B</strong></p>
                    <p>提示：RISC的核心是"简化"，哪个选项与此相反？</p>
                `;
            }
        }

        // 分析动画
        const analysisCanvas = document.getElementById('analysisCanvas');
        const analysisCtx = analysisCanvas.getContext('2d');
        let analysisFrame = 0;
        let isAnalysisAnimating = false;

        function drawAnalysisAnimation() {
            analysisCtx.clearRect(0, 0, analysisCanvas.width, analysisCanvas.height);

            // 背景
            const gradient = analysisCtx.createLinearGradient(0, 0, analysisCanvas.width, analysisCanvas.height);
            gradient.addColorStop(0, '#fff5f5');
            gradient.addColorStop(1, '#f0f8ff');
            analysisCtx.fillStyle = gradient;
            analysisCtx.fillRect(0, 0, analysisCanvas.width, analysisCanvas.height);

            const progress = (analysisFrame % 600) / 600;
            const step = Math.floor(progress * 5);

            // 标题
            analysisCtx.fillStyle = '#333';
            analysisCtx.font = 'bold 20px Arial';
            analysisCtx.textAlign = 'center';
            analysisCtx.fillText('解题分析过程', analysisCanvas.width/2, 30);

            // 选项分析
            const options = [
                { text: 'A. 指令长度固定', correct: true, reason: '✓ 符合RISC简化原则' },
                { text: 'B. 寻址方式丰富', correct: false, reason: '✗ 与RISC简化相反！' },
                { text: 'C. 增加寄存器数目', correct: true, reason: '✓ 符合RISC原则' },
                { text: 'D. 硬布线控制', correct: true, reason: '✓ 符合RISC原则' }
            ];

            for (let i = 0; i < options.length; i++) {
                if (step > i) {
                    const y = 80 + i * 70;
                    const option = options[i];

                    // 选项框
                    if (option.correct) {
                        analysisCtx.fillStyle = step === 4 && i === 1 ? '#ffebee' : '#e8f5e8';
                        analysisCtx.strokeStyle = step === 4 && i === 1 ? '#f44336' : '#4caf50';
                    } else {
                        analysisCtx.fillStyle = '#ffebee';
                        analysisCtx.strokeStyle = '#f44336';
                    }

                    analysisCtx.lineWidth = step === 4 && i === 1 ? 4 : 2;
                    analysisCtx.fillRect(50, y, 700, 50);
                    analysisCtx.strokeRect(50, y, 700, 50);

                    // 选项文字
                    analysisCtx.fillStyle = '#333';
                    analysisCtx.font = '16px Arial';
                    analysisCtx.textAlign = 'left';
                    analysisCtx.fillText(option.text, 70, y + 25);

                    // 分析结果
                    if (step > 3 || (step === 3 && i < 3)) {
                        analysisCtx.fillStyle = option.correct ? '#4caf50' : '#f44336';
                        analysisCtx.font = 'bold 14px Arial';
                        analysisCtx.textAlign = 'right';
                        analysisCtx.fillText(option.reason, 730, y + 35);
                    }
                }
            }

            // 结论
            if (step >= 4) {
                analysisCtx.fillStyle = '#ff4444';
                analysisCtx.font = 'bold 18px Arial';
                analysisCtx.textAlign = 'center';
                analysisCtx.fillText('答案：B选项与RISC简化原则相反！', analysisCanvas.width/2, 370);
            }

            if (isAnalysisAnimating) {
                analysisFrame++;
                requestAnimationFrame(drawAnalysisAnimation);
            }
        }

        analysisCanvas.addEventListener('click', () => {
            if (!isAnalysisAnimating) {
                isAnalysisAnimating = true;
                analysisFrame = 0;
                drawAnalysisAnimation();
                setTimeout(() => {
                    isAnalysisAnimating = false;
                }, 12000);
            }
        });

        // 初始绘制分析画布
        drawAnalysisAnimation();
    </script>
</body>
</html>
