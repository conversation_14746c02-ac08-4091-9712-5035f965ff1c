<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件可靠性互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            background: #f8f9ff;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .interactive-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .interactive-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .quiz-container {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 30px;
            border-radius: 20px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quiz-option:hover {
            background: #f0f0f0;
            transform: translateX(10px);
        }

        .quiz-option.selected {
            border-color: #667eea;
            background: #e8f0fe;
        }

        .quiz-option.correct {
            border-color: #4caf50;
            background: #e8f5e8;
        }

        .quiz-option.incorrect {
            border-color: #f44336;
            background: #ffebee;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 软件可靠性学习之旅</h1>
            <p>通过互动动画和游戏，轻松掌握软件可靠性的核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是软件可靠性？</h2>
            <div class="canvas-container">
                <canvas id="reliabilityCanvas" width="600" height="300"></canvas>
            </div>
            <div class="explanation">
                <strong>软件可靠性</strong>是指在特定环境和特定时间内，计算机程序无故障运行的概率。
                就像一辆汽车的可靠性一样，我们希望软件能够稳定、持续地为我们服务。
            </div>
            <button class="interactive-button" onclick="startReliabilityDemo()">🚀 开始可靠性演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">⚠️ 故障与错误的区别</h2>
            <div class="canvas-container">
                <canvas id="faultCanvas" width="600" height="300"></canvas>
            </div>
            <div class="explanation">
                <strong>故障</strong>是指软件行为与需求的不符，故障有等级之分。
                想象一下：如果你点击"保存"按钮，但文件没有保存，这就是一个故障。
                故障可能是轻微的（界面显示问题）或严重的（数据丢失）。
            </div>
            <button class="interactive-button" onclick="startFaultDemo()">🔍 探索故障类型</button>
        </div>

        <div class="section">
            <h2 class="section-title">📊 可靠性的测量与估算</h2>
            <div class="canvas-container">
                <canvas id="measureCanvas" width="600" height="300"></canvas>
            </div>
            <div class="explanation">
                软件可靠性<strong>可以</strong>通过历史数据和开发数据直接测量和估算出来。
                就像医生通过检查和化验来诊断病情一样，我们可以通过各种数据来评估软件的可靠性。
            </div>
            <button class="interactive-button" onclick="startMeasureDemo()">📈 查看测量过程</button>
        </div>
    </div>

    <script>
        let currentProgress = 0;
        const totalSections = 3;

        function updateProgress() {
            currentProgress++;
            const percentage = (currentProgress / totalSections) * 100;
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // 可靠性演示动画
        function startReliabilityDemo() {
            const canvas = document.getElementById('reliabilityCanvas');
            const ctx = canvas.getContext('2d');
            let animationFrame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制时间轴
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(50, 250);
                ctx.lineTo(550, 250);
                ctx.stroke();
                
                // 绘制时间标记
                ctx.fillStyle = '#666';
                ctx.font = '14px Arial';
                for(let i = 0; i <= 5; i++) {
                    const x = 50 + i * 100;
                    ctx.fillText(`${i}h`, x - 10, 270);
                    ctx.beginPath();
                    ctx.moveTo(x, 245);
                    ctx.lineTo(x, 255);
                    ctx.stroke();
                }
                
                // 绘制软件运行状态
                const time = animationFrame / 60;
                for(let i = 0; i < time * 100 && i < 500; i++) {
                    const x = 50 + i;
                    const isRunning = Math.sin(i * 0.1) > -0.8; // 大部分时间运行正常
                    
                    ctx.fillStyle = isRunning ? '#4caf50' : '#f44336';
                    ctx.fillRect(x, 230, 1, 20);
                }
                
                // 绘制可靠性指标
                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.fillText('软件运行状态', 250, 30);
                
                ctx.fillStyle = '#4caf50';
                ctx.fillRect(50, 50, 20, 20);
                ctx.fillStyle = '#333';
                ctx.fillText('正常运行', 80, 65);
                
                ctx.fillStyle = '#f44336';
                ctx.fillRect(200, 50, 20, 20);
                ctx.fillStyle = '#333';
                ctx.fillText('故障状态', 230, 65);
                
                // 计算可靠性百分比
                const reliability = Math.max(0, 95 - Math.sin(time) * 5);
                ctx.fillStyle = '#667eea';
                ctx.font = '24px Arial';
                ctx.fillText(`当前可靠性: ${reliability.toFixed(1)}%`, 350, 65);
                
                animationFrame++;
                if(animationFrame < 300) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress();
                }
            }
            
            animate();
        }

        // 故障演示动画
        function startFaultDemo() {
            const canvas = document.getElementById('faultCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;
            
            const faults = [
                { name: '轻微故障', color: '#ffeb3b', severity: 1, x: 100, y: 200 },
                { name: '中等故障', color: '#ff9800', severity: 2, x: 300, y: 150 },
                { name: '严重故障', color: '#f44336', severity: 3, x: 500, y: 100 }
            ];
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.fillStyle = '#333';
                ctx.font = '20px Arial';
                ctx.fillText('故障等级分类', 220, 30);
                
                // 绘制故障
                faults.forEach((fault, index) => {
                    if(step > index * 60) {
                        const scale = Math.sin((step - index * 60) * 0.1) * 0.2 + 1;
                        const radius = fault.severity * 15 * scale;
                        
                        // 绘制故障圆圈
                        ctx.fillStyle = fault.color;
                        ctx.beginPath();
                        ctx.arc(fault.x, fault.y, radius, 0, Math.PI * 2);
                        ctx.fill();
                        
                        // 绘制故障名称
                        ctx.fillStyle = '#333';
                        ctx.font = '14px Arial';
                        ctx.fillText(fault.name, fault.x - 30, fault.y + radius + 20);
                        
                        // 绘制严重程度
                        ctx.fillText(`严重程度: ${fault.severity}`, fault.x - 40, fault.y + radius + 40);
                    }
                });
                
                step++;
                if(step < 240) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress();
                }
            }
            
            animate();
        }

        // 测量演示动画
        function startMeasureDemo() {
            const canvas = document.getElementById('measureCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制数据收集过程
                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.fillText('软件可靠性数据收集与分析', 180, 30);
                
                // 绘制数据源
                const dataSources = [
                    { name: '历史数据', x: 100, y: 80, color: '#2196f3' },
                    { name: '开发数据', x: 300, y: 80, color: '#4caf50' },
                    { name: '测试数据', x: 500, y: 80, color: '#ff9800' }
                ];
                
                dataSources.forEach((source, index) => {
                    if(frame > index * 30) {
                        // 绘制数据源圆圈
                        ctx.fillStyle = source.color;
                        ctx.beginPath();
                        ctx.arc(source.x, source.y, 25, 0, Math.PI * 2);
                        ctx.fill();
                        
                        // 绘制数据源名称
                        ctx.fillStyle = '#333';
                        ctx.font = '12px Arial';
                        ctx.fillText(source.name, source.x - 20, source.y + 45);
                        
                        // 绘制数据流动画
                        if(frame > index * 30 + 30) {
                            const progress = Math.min(1, (frame - index * 30 - 30) / 60);
                            const endY = 200;
                            const currentY = source.y + (endY - source.y) * progress;
                            
                            ctx.strokeStyle = source.color;
                            ctx.lineWidth = 3;
                            ctx.beginPath();
                            ctx.moveTo(source.x, source.y + 25);
                            ctx.lineTo(source.x, currentY);
                            ctx.stroke();
                            
                            // 绘制数据点
                            ctx.fillStyle = source.color;
                            ctx.beginPath();
                            ctx.arc(source.x, currentY, 5, 0, Math.PI * 2);
                            ctx.fill();
                        }
                    }
                });
                
                // 绘制分析结果
                if(frame > 150) {
                    ctx.fillStyle = '#667eea';
                    ctx.fillRect(200, 200, 200, 60);
                    
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.fillText('可靠性分析结果', 230, 225);
                    ctx.fillText('可靠性: 96.5%', 240, 245);
                }
                
                frame++;
                if(frame < 200) {
                    requestAnimationFrame(animate);
                } else {
                    updateProgress();
                    showQuiz();
                }
            }
            
            animate();
        }

        // 显示测验
        function showQuiz() {
            const quizHTML = `
                <div class="section quiz-container">
                    <h2 class="section-title">🎓 知识测验</h2>
                    <div class="quiz-question">
                        下列关于软件可靠性的叙述，不正确的是____。
                    </div>
                    <div class="quiz-options">
                        <div class="quiz-option" onclick="selectOption(this, false)">
                            A. 由于影响软件可靠性的因素很复杂，软件可靠性不能通过历史数据和开发数据直接测量和估算出来
                        </div>
                        <div class="quiz-option" onclick="selectOption(this, true)">
                            B. 软件可靠性是指在特定环境和特定时间内，计算机程序无故障运行的概率
                        </div>
                        <div class="quiz-option" onclick="selectOption(this, true)">
                            C. 在软件可靠性的讨论中，故障指软件行为与需求的不符，故障有等级之分
                        </div>
                        <div class="quiz-option" onclick="selectOption(this, true)">
                            D. 排除一个故障可能会引入其他的错误，而这些错误会导致其他的故障
                        </div>
                    </div>
                    <div class="explanation">
                        <strong>正确答案：A</strong><br>
                        软件可靠性<strong>可以</strong>通过历史数据和开发数据直接测量和估算出来。
                        通过收集软件运行过程中的各种数据，我们能够建立数学模型来预测和评估软件的可靠性水平。
                    </div>
                </div>
            `;
            
            document.querySelector('.container').insertAdjacentHTML('beforeend', quizHTML);
        }

        function selectOption(element, isCorrect) {
            // 移除所有选项的选中状态
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('selected', 'correct', 'incorrect');
            });
            
            // 标记选中的选项
            element.classList.add('selected');
            
            // 显示正确答案
            setTimeout(() => {
                document.querySelectorAll('.quiz-option').forEach((option, index) => {
                    if(index === 0) { // A选项是正确答案
                        option.classList.add('correct');
                    } else if(option.classList.contains('selected') && !isCorrect) {
                        option.classList.add('incorrect');
                    }
                });
            }, 1000);
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 添加一些初始动画效果
            setTimeout(() => {
                document.querySelector('.header h1').classList.add('pulse');
            }, 2000);
        });
    </script>
</body>
</html>
