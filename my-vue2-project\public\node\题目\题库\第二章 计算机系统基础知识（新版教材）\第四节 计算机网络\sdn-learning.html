<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDN网络架构 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInUp 1s ease-out 0.3s both;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #28a745;
            background: #d4edda;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #f8d7da;
            animation: shake 0.6s ease-in-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInUp 1s ease-out 0.6s both;
        }

        #networkCanvas {
            width: 100%;
            height: 500px;
            border-radius: 15px;
            background: linear-gradient(45deg, #f0f2f5, #e9ecef);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInUp 1s ease-out 0.9s both;
        }

        .layer-info {
            margin-bottom: 30px;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .layer-info:hover {
            transform: translateX(10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .layer-info.app { 
            background: #e3f2fd; 
            border-color: #2196f3; 
        }
        .layer-info.control { 
            background: #f3e5f5; 
            border-color: #9c27b0; 
        }
        .layer-info.data { 
            background: #e8f5e8; 
            border-color: #4caf50; 
        }

        .layer-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🌐 SDN网络架构学习</h1>
            <p class="subtitle">通过动画和交互，轻松理解软件定义网络的三层架构</p>
        </div>

        <div class="question-card">
            <h2 class="question-title">📝 题目：SDN(Software Defined Network)的网络架构中不包含（ ）？</h2>
            <div class="options" id="options">
                <div class="option" data-answer="A">A. 逻辑层</div>
                <div class="option" data-answer="B">B. 控制层</div>
                <div class="option" data-answer="C">C. 转发层</div>
                <div class="option" data-answer="D">D. 应用层</div>
            </div>
        </div>

        <div class="canvas-container">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">🎯 SDN三层架构动画演示</h3>
            <canvas id="networkCanvas"></canvas>
            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重新开始</button>
            </div>
        </div>

        <div class="explanation">
            <h3 style="text-align: center; margin-bottom: 30px; color: #333;">📚 知识详解</h3>
            
            <div class="layer-info app" onclick="highlightLayer('app')">
                <div class="layer-title">🎯 应用层 (Application Layer)</div>
                <p>就像手机上的各种APP一样，这一层包含了各种网络应用和业务。比如视频会议、在线游戏、网页浏览等。它们告诉网络："我需要什么样的服务！"</p>
            </div>

            <div class="layer-info control" onclick="highlightLayer('control')">
                <div class="layer-title">🧠 控制层 (Control Layer)</div>
                <p>这是SDN的"大脑"！就像交通指挥中心一样，它负责制定规则、管理网络拓扑、决定数据该怎么走。所有的智能决策都在这里完成。</p>
            </div>

            <div class="layer-info data" onclick="highlightLayer('data')">
                <div class="layer-title">🚛 数据转发层 (Data Forwarding Layer)</div>
                <p>这是真正干活的"搬运工"！就像快递员一样，负责按照控制层的指令，把数据包从一个地方搬到另一个地方。</p>
            </div>

            <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 10px; border-left: 5px solid #ffc107;">
                <h4>💡 解题思路：</h4>
                <p>SDN只有<strong>三层架构</strong>：应用层、控制层、数据转发层。题目中的"逻辑层"并不存在于SDN架构中，所以答案是A。记住：SDN = 应用层 + 控制层 + 数据转发层！</p>
            </div>
        </div>
    </div>

    <script>
        // 创建浮动粒子效果
        function createParticles() {
            const container = document.getElementById('particles');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 10 + 5 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(particle);
            }
        }

        // Canvas动画
        const canvas = document.getElementById('networkCanvas');
        const ctx = canvas.getContext('2d');
        
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        let animationId;
        let animationStep = 0;
        let highlightedLayer = null;

        // 绘制SDN架构
        function drawSDNArchitecture() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const layerHeight = canvas.height / 4;
            const layerWidth = canvas.width * 0.8;
            const startX = canvas.width * 0.1;
            
            // 应用层
            drawLayer(startX, layerHeight * 0.5, layerWidth, layerHeight * 0.6, 
                     '#2196f3', '应用层', '🎯', highlightedLayer === 'app');
            
            // 控制层  
            drawLayer(startX, layerHeight * 1.5, layerWidth, layerHeight * 0.6,
                     '#9c27b0', '控制层', '🧠', highlightedLayer === 'control');
            
            // 数据转发层
            drawLayer(startX, layerHeight * 2.5, layerWidth, layerHeight * 0.6,
                     '#4caf50', '数据转发层', '🚛', highlightedLayer === 'data');
            
            // 绘制连接线和数据流
            if (animationStep > 0) {
                drawConnections();
            }
            
            if (animationStep > 1) {
                drawDataFlow();
            }
        }

        function drawLayer(x, y, width, height, color, title, icon, highlight) {
            // 绘制层背景
            ctx.fillStyle = highlight ? color : color + '40';
            ctx.strokeStyle = color;
            ctx.lineWidth = highlight ? 4 : 2;
            
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, 15);
            ctx.fill();
            ctx.stroke();
            
            // 绘制标题
            ctx.fillStyle = highlight ? '#fff' : color;
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(icon + ' ' + title, x + width/2, y + height/2 + 8);
        }

        function drawConnections() {
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 3;
            ctx.setLineDash([10, 5]);
            
            const centerX = canvas.width / 2;
            const layerHeight = canvas.height / 4;
            
            // 应用层到控制层
            ctx.beginPath();
            ctx.moveTo(centerX, layerHeight * 1.1);
            ctx.lineTo(centerX, layerHeight * 1.5);
            ctx.stroke();
            
            // 控制层到数据转发层
            ctx.beginPath();
            ctx.moveTo(centerX, layerHeight * 2.1);
            ctx.lineTo(centerX, layerHeight * 2.5);
            ctx.stroke();
            
            ctx.setLineDash([]);
        }

        function drawDataFlow() {
            const time = Date.now() * 0.005;
            const centerX = canvas.width / 2;
            const layerHeight = canvas.height / 4;
            
            // 绘制流动的数据包
            for (let i = 0; i < 3; i++) {
                const offset = (time + i * 2) % 6;
                let y;
                
                if (offset < 2) {
                    y = layerHeight * 1.1 + (layerHeight * 0.4) * (offset / 2);
                } else if (offset < 4) {
                    y = layerHeight * 1.5 + (layerHeight * 1) * ((offset - 2) / 2);
                } else {
                    continue;
                }
                
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(centerX + Math.sin(time + i) * 20, y, 8, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function startAnimation() {
            animationStep = 0;
            animate();
        }

        function animate() {
            drawSDNArchitecture();
            
            setTimeout(() => {
                if (animationStep < 2) {
                    animationStep++;
                    animate();
                } else {
                    // 持续绘制数据流动画
                    function loop() {
                        drawSDNArchitecture();
                        animationId = requestAnimationFrame(loop);
                    }
                    loop();
                }
            }, 1000);
        }

        function resetAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animationStep = 0;
            highlightedLayer = null;
            drawSDNArchitecture();
        }

        function highlightLayer(layer) {
            highlightedLayer = layer;
            drawSDNArchitecture();
            
            setTimeout(() => {
                highlightedLayer = null;
                drawSDNArchitecture();
            }, 2000);
        }

        // 题目交互
        document.getElementById('options').addEventListener('click', function(e) {
            if (e.target.classList.contains('option')) {
                const options = document.querySelectorAll('.option');
                options.forEach(opt => {
                    opt.style.pointerEvents = 'none';
                    if (opt.dataset.answer === 'A') {
                        opt.classList.add('correct');
                    } else if (opt === e.target && opt.dataset.answer !== 'A') {
                        opt.classList.add('wrong');
                    }
                });
                
                setTimeout(() => {
                    alert(e.target.dataset.answer === 'A' ? 
                          '🎉 恭喜答对了！SDN确实不包含逻辑层。' : 
                          '❌ 答错了，正确答案是A。SDN只有应用层、控制层和数据转发层三层架构。');
                }, 1000);
            }
        });

        // 初始化
        createParticles();
        drawSDNArchitecture();
        
        // 自动开始演示
        setTimeout(startAnimation, 2000);
    </script>
</body>
</html>
