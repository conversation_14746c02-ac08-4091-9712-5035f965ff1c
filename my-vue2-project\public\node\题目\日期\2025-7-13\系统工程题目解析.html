<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统工程题目解析与互动演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 20px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
            font-weight: 600;
        }
        .section {
            margin-bottom: 40px;
            padding: 20px;
            background-color: #e9f5f5;
            border-left: 6px solid #2ecc71;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .question-box {
            background-color: #ffffff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .question-title {
            font-size: 22px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 15px;
            text-align: center;
        }
        .question-text {
            font-size: 18px;
            margin-bottom: 20px;
            color: #555;
        }
        .options-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .option-button {
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            position: relative;
            padding-left: 45px;
        }
        .option-button:hover {
            background-color: #e0e0e0;
            border-color: #a0a0a0;
        }
        .option-button.selected {
            background-color: #d1ecf1;
            border-color: #007bff;
            color: #007bff;
            font-weight: bold;
        }
        .option-button.correct {
            background-color: #d4edda;
            border-color: #28a745;
            color: #28a745;
            font-weight: bold;
        }
        .option-button.incorrect {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #dc3545;
            font-weight: bold;
        }
        .option-label {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-weight: bold;
        }
        .explanation {
            margin-top: 25px;
            padding: 20px;
            background-color: #fff3cd;
            border-left: 6px solid #ffc107;
            border-radius: 8px;
            font-size: 16px;
            color: #6a4a00;
            display: none; /* 初始隐藏 */
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .explanation p {
            margin-bottom: 10px;
        }
        .explanation strong {
            color: #856404;
        }

        .knowledge-section {
            margin-top: 40px;
            padding: 25px;
            background-color: #f0f8ff;
            border-left: 6px solid #6cb6ff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .knowledge-section h2 {
            color: #0056b3;
            margin-bottom: 20px;
        }
        .knowledge-section p {
            margin-bottom: 15px;
            color: #444;
        }
        .knowledge-section ul {
            list-style: disc;
            margin-left: 25px;
            margin-bottom: 15px;
        }
        .knowledge-section ul li {
            margin-bottom: 8px;
        }
        .canvas-container {
            text-align: center;
            margin-top: 30px;
            background-color: #fdfdfd;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
        }
        canvas {
            border: 1px solid #dcdcdc;
            background-color: #ffffff;
            border-radius: 6px;
            cursor: grab;
            transition: border-color 0.3s ease;
        }
        canvas:active {
            cursor: grabbing;
        }
        .canvas-controls {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .canvas-controls button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        .canvas-controls button:hover {
            background-color: #0056b3;
        }
        .canvas-controls button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .game-section {
            margin-top: 40px;
            padding: 25px;
            background-color: #e8f8f8;
            border-left: 6px solid #1abc9c;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            text-align: center;
        }
        .game-section h2 {
            color: #0b7f6c;
            margin-bottom: 20px;
        }
        .game-controls button {
            background-color: #1abc9c;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 18px;
            transition: background-color 0.3s ease;
            margin: 10px;
        }
        .game-controls button:hover {
            background-color: #16a085;
        }
        .game-info {
            margin-top: 20px;
            font-size: 1.1em;
            color: #34495e;
        }
        .game-canvas {
            border: 2px solid #1abc9c;
            background-color: #ffffff;
            margin-top: 20px;
            border-radius: 8px;
        }

        .footer-note {
            margin-top: 50px;
            text-align: center;
            font-size: 14px;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>系统工程题目解析与互动学习</h1>

        <div class="section question-box">
            <div class="question-title">问题1 [单选题]</div>
            <div class="question-text">系统工程利用计算机作为工具，对系统的结构、元素、(请作答此空)和反馈等进行分析，以达到最优()、最优设计、最优管理和最优控制的目的。霍尔(A.D. Hall) 于1969年提出了系统方法的三维结构体系，通常称为霍尔三维结构，这是系统工程方法论的基础。霍尔三维结构以时间维、()维、知识维组成的立体结构概念性地表示出系统工程的各阶段、各步骤以及所涉及的知识范围。其中时间维是系统的工作进程，对于一个具体的工程项目，可以分为7个阶段，在()阶段会做出研制方案及生产计划。</div>
            
            <div class="options-container">
                <button class="option-button" data-option="A"><span class="option-label">A</span> 知识</button>
                <button class="option-button" data-option="B"><span class="option-label">B</span> 需求</button>
                <button class="option-button" data-option="C"><span class="option-label">C</span> 文档</button>
                <button class="option-button" data-option="D"><span class="option-label">D</span> 信息</button>
            </div>
            <div class="explanation" id="questionExplanation">
                <p><strong>正确答案: D</strong></p>
                <p>解析: 题目考查的是霍尔三维结构中的维度之一。根据系统工程的定义和霍尔三维结构的组成，除了“结构”、“元素”和“反馈”之外，另一个核心分析对象是“信息”。信息在系统运作和决策中扮演关键角色。</p>
                <p>霍尔三维结构包括：</p>
                <ul>
                    <li>时间维：系统的工作进程，即系统生命周期的各个阶段。</li>
                    <li>逻辑维（或称功能维/方法维）：在系统工程中，这指的是解决问题和进行系统开发所采用的逻辑步骤或方法，如问题定义、系统设计、系统实现等。题目中的“（）维”指的是这个。</li>
                    <li>知识维：系统工程涉及的专业知识领域，如物理、经济、管理等。</li>
                </ul>
                <p>至于“7个阶段，在（）阶段会做出研制方案及生产计划”，这对应的是系统工程生命周期中的 **C阶段（最终设计与制造阶段）**。在这个阶段，会完成详细设计，并制定制造和生产计划。</p>
            </div>
            <button id="showExplanation" class="option-button" style="margin-top: 20px; background-color: #28a745; color: white; border-color: #28a745; padding-left: 20px; display: none;">显示解析</button>
            <button id="resetOptions" class="option-button" style="margin-top: 10px; background-color: #6c757d; color: white; border-color: #6c757d; padding-left: 20px; display: none;">重置</button>
        </div>

        <div class="section knowledge-section">
            <h2>深入理解系统工程</h2>
            <p>系统工程是一门综合性学科，旨在以整体的视角设计和管理复杂的系统。它不仅仅是关于技术，还涉及到经济、管理和社会等多个方面。</p>

            <h3>A.D. Hall 三维结构：系统工程的基石</h3>
            <p>A.D. Hall于1969年提出的三维结构概念性地描绘了系统工程的范畴，帮助我们理解系统工程的复杂性。它包括三个核心维度：</p>
            <ul>
                <li><strong>时间维 (Time Dimension)</strong>：表示系统生命周期的演进。从最初的构想、定义、设计、开发、生产、部署，到运行维护，直至最终的报废。这是一个动态的过程，强调了系统从诞生到消亡的全生命周期管理。</li>
                <li><strong>逻辑维 (Logical/Functional Dimension)</strong>：也称为功能维或方法维，指的是在系统开发过程中所采用的逻辑步骤或过程。这包括问题识别、需求分析、概念形成、系统合成、系统分析、开发、工程、测试、部署等一系列有序的活动。它关注的是“如何做”系统工程。</li>
                <li><strong>知识维 (Knowledge Dimension)</strong>：表示系统工程所跨越的知识领域和专业学科。系统工程并非单一学科，它融合了数学、物理、经济学、社会学、心理学、管理学、计算机科学等众多领域的知识，以解决复杂问题。</li>
            </ul>
            <p>这三个维度相互关联，构成了一个立体的框架，指导系统工程师在时间、方法和知识的交织中进行系统开发和管理。</p>

            <div class="canvas-container">
                <h3>霍尔三维结构互动演示 (拖动旋转，点击维度查看解释)</h3>
                <canvas id="hall3DCanvas" width="600" height="400"></canvas>
                <div class="canvas-controls">
                    <button id="toggleRotation">开始/暂停旋转</button>
                    <button id="resetView">重置视角</button>
                    <button id="explainTime">解释时间维</button>
                    <button id="explainLogical">解释逻辑维</button>
                    <button id="explainKnowledge">解释知识维</button>
                </div>
            </div>

            <h3>系统分析的四个核心要素</h3>
            <p>系统工程在分析系统时，主要关注以下四个核心要素：</p>
            <ul>
                <li><strong>结构 (Structure)</strong>：系统的组成部分及其相互关系，如同一个建筑的蓝图。</li>
                <li><strong>元素 (Elements)</strong>：构成系统的基本组件，可以是硬件、软件、人员等。</li>
                <li><strong>信息 (Information)</strong>：在系统内部以及系统与外部环境之间流动的数据和知识。它是系统运行的“血液”。</li>
                <li><strong>反馈 (Feedback)</strong>：系统对其自身或环境的响应，用于调整和优化系统行为，保持系统稳定和适应性。</li>
            </ul>
            <p>这些要素的协同作用，决定了系统的整体性能和行为。</p>

            <h3>系统工程项目实施的七个阶段 (游戏化演示)</h3>
            <p>虽然A.D. Hall没有明确定义七个阶段，但系统工程项目通常会经历一系列生命周期阶段，我将以NASA的生命周期阶段为例，通过互动方式为你展示，并突出题目中提到的“研制方案及生产计划”所在的关键阶段。</p>
            <div class="game-section">
                <h2>系统工程生命周期挑战！</h2>
                <p>点击每个阶段的按钮，了解其目标和任务，看看你能否找到“研制方案及生产计划”的阶段！</p>
                <div class="game-controls">
                    <button id="stageBtn0">阶段 1: 概念研究</button>
                    <button id="stageBtn1">阶段 2: 概念与技术开发</button>
                    <button id="stageBtn2">阶段 3: 初步设计与技术完成</button>
                    <button id="stageBtn3">阶段 4: 最终设计与制造</button>
                    <button id="stageBtn4">阶段 5: 集成与部署</button>
                    <button id="stageBtn5">阶段 6: 运行与维护</button>
                    <button id="stageBtn6">阶段 7: 报废</button>
                </div>
                <div class="game-info" id="stageInfo">点击上方按钮开始探索...</div>
                <canvas id="lifecycleCanvas" class="game-canvas" width="800" height="200"></canvas>
            </div>
        </div>
    </div>

    <div class="footer-note">
        <p>此页面为教育目的创建，旨在帮助您理解系统工程的核心概念。内容基于公开资料整理，可能与某些特定教材的表述略有差异。</p>
    </div>

    <script>
        // 题目交互逻辑
        const optionButtons = document.querySelectorAll('.option-button[data-option]');
        const questionExplanation = document.getElementById('questionExplanation');
        const showExplanationButton = document.getElementById('showExplanation');
        const resetOptionsButton = document.getElementById('resetOptions');
        const correctAnswer = 'D';

        optionButtons.forEach(button => {
            button.addEventListener('click', () => {
                // 如果已经有选中的答案，不允许再次选择
                if (document.querySelector('.option-button.selected, .option-button.correct, .option-button.incorrect')) {
                    return;
                }

                const selectedOption = button.dataset.option;
                button.classList.add('selected');

                if (selectedOption === correctAnswer) {
                    button.classList.add('correct');
                    questionExplanation.innerHTML = `<p><strong>回答正确！</strong></p><p>解析: 题目考查的是霍尔三维结构中的维度之一。根据系统工程的定义和霍尔三维结构的组成，除了“结构”、“元素”和“反馈”之外，另一个核心分析对象是“信息”。信息在系统运作和决策中扮演关键角色。</p><p>霍尔三维结构包括：</p><ul><li><strong>时间维</strong>：系统的工作进程，即系统生命周期的各个阶段。</li><li><strong>逻辑维（或称功能维/方法维）</strong>：在系统工程中，这指的是解决问题和进行系统开发所采用的逻辑步骤或方法，如问题定义、系统设计、系统实现等。题目中的“（）维”指的是这个。</li><li><strong>知识维</strong>：系统工程涉及的专业知识领域，如物理、经济、管理等。</li></ul><p>至于“7个阶段，在（）阶段会做出研制方案及生产计划”，这对应的是系统工程生命周期中的 <strong>C阶段（最终设计与制造阶段）</strong>。在这个阶段，会完成详细设计，并制定制造和生产计划。</p>`;
                } else {
                    button.classList.add('incorrect');
                    document.querySelector(`.option-button[data-option="${correctAnswer}"]`).classList.add('correct');
                    questionExplanation.innerHTML = `<p><strong>回答错误。</strong></p><p><strong>正确答案: D</strong></p><p>解析: 题目考查的是霍尔三维结构中的维度之一。根据系统工程的定义和霍尔三维结构的组成，除了“结构”、“元素”和“反馈”之外，另一个核心分析对象是“信息”。信息在系统运作和决策中扮演关键角色。</p><p>霍尔三维结构包括：</p><ul><li><strong>时间维</strong>：系统的工作进程，即系统生命周期的各个阶段。</li><li><strong>逻辑维（或称功能维/方法维）</strong>：在系统工程中，这指的是解决问题和进行系统开发所采用的逻辑步骤或方法，如问题定义、系统设计、系统实现等。题目中的“（）维”指的是这个。</li><li><strong>知识维</strong>：系统工程涉及的专业知识领域，如物理、经济、管理等。</li></ul><p>至于“7个阶段，在（）阶段会做出研制方案及生产计划”，这对应的是系统工程生命周期中的 <strong>C阶段（最终设计与制造阶段）</strong>。在这个阶段，会完成详细设计，并制定制造和生产计划。</p>`;
                }
                questionExplanation.style.display = 'block';
                showExplanationButton.style.display = 'none'; // 答案揭晓后隐藏“显示解析”
                resetOptionsButton.style.display = 'block';
            });
        });

        showExplanationButton.addEventListener('click', () => {
            questionExplanation.style.display = 'block';
            showExplanationButton.style.display = 'none';
            resetOptionsButton.style.display = 'block';
            // 确保没有选中任何答案时，不显示正确/错误颜色
            optionButtons.forEach(button => {
                button.classList.remove('selected', 'correct', 'incorrect');
            });
            document.querySelector(`.option-button[data-option="${correctAnswer}"]`).classList.add('correct');
        });

        resetOptionsButton.addEventListener('click', () => {
            optionButtons.forEach(button => {
                button.classList.remove('selected', 'correct', 'incorrect');
            });
            questionExplanation.style.display = 'none';
            showExplanationButton.style.display = 'block';
            resetOptionsButton.style.display = 'none';
        });

        // 初始状态，如果用户直接点了解析，也显示解析按钮
        if (!document.querySelector('.option-button.selected, .option-button.correct, .option-button.incorrect')) {
            showExplanationButton.style.display = 'block';
        }

        // 霍尔三维结构Canvas演示
        const hallCanvas = document.getElementById('hall3DCanvas');
        const hallCtx = hallCanvas.getContext('2d');

        let angleX = Math.PI / 6;
        let angleY = Math.PI / 6;
        let isDragging = false;
        let lastMouseX, lastMouseY;
        let autoRotate = true;
        let animationFrameId;

        const dimensions = [
            { name: '时间维', color: '#ff6347', x: 150, y: 0, z: 0, info: '时间维表示系统生命周期的演进，从概念到报废。' },
            { name: '逻辑维', color: '#4682b4', x: 0, y: 150, z: 0, info: '逻辑维（或称功能维）指系统开发过程中采用的逻辑步骤或方法，如问题定义、系统设计。' },
            { name: '知识维', color: '#32cd32', x: 0, y: 0, z: 150, info: '知识维表示系统工程所跨越的知识领域和专业学科，融合了多领域知识。' }
        ];

        function rotatePoint(x, y, z) {
            let rotatedX, rotatedY, rotatedZ;

            // Rotate around Y-axis
            rotatedX = x * Math.cos(angleY) - z * Math.sin(angleY);
            rotatedZ = x * Math.sin(angleY) + z * Math.cos(angleY);
            x = rotatedX;
            z = rotatedZ;

            // Rotate around X-axis
            rotatedY = y * Math.cos(angleX) - z * Math.sin(angleX);
            rotatedZ = y * Math.sin(angleX) + z * Math.cos(angleX);
            y = rotatedY;
            z = rotatedZ;

            return { x, y, z };
        }

        function projectPoint(p) {
            const focalLength = 400; // 模拟透视效果的焦距
            const scale = focalLength / (focalLength + p.z);
            const centerX = hallCanvas.width / 2;
            const centerY = hallCanvas.height / 2;
            return {
                x: p.x * scale + centerX,
                y: p.y * scale + centerY
            };
        }

        function drawHall3D() {
            hallCtx.clearRect(0, 0, hallCanvas.width, hallCanvas.height);

            const origin = rotatePoint(0, 0, 0);
            const pOrigin = projectPoint(origin);

            // Draw axes
            hallCtx.lineWidth = 2;
            const axisLength = 150;

            // Time Dimension (Red)
            let p1 = rotatePoint(axisLength, 0, 0);
            let pp1 = projectPoint(p1);
            hallCtx.strokeStyle = dimensions[0].color;
            hallCtx.beginPath();
            hallCtx.moveTo(pOrigin.x, pOrigin.y);
            hallCtx.lineTo(pp1.x, pp1.y);
            hallCtx.stroke();
            hallCtx.fillStyle = dimensions[0].color;
            hallCtx.font = '16px Arial';
            hallCtx.fillText(dimensions[0].name, pp1.x + 10, pp1.y);

            // Logical Dimension (Blue)
            p1 = rotatePoint(0, axisLength, 0);
            pp1 = projectPoint(p1);
            hallCtx.strokeStyle = dimensions[1].color;
            hallCtx.beginPath();
            hallCtx.moveTo(pOrigin.x, pOrigin.y);
            hallCtx.lineTo(pp1.x, pp1.y);
            hallCtx.stroke();
            hallCtx.fillStyle = dimensions[1].color;
            hallCtx.font = '16px Arial';
            hallCtx.fillText(dimensions[1].name, pp1.x, pp1.y - 10);

            // Knowledge Dimension (Green)
            p1 = rotatePoint(0, 0, axisLength);
            pp1 = projectPoint(p1);
            hallCtx.strokeStyle = dimensions[2].color;
            hallCtx.beginPath();
            hallCtx.moveTo(pOrigin.x, pOrigin.y);
            hallCtx.lineTo(pp1.x, pp1.y);
            hallCtx.stroke();
            hallCtx.fillStyle = dimensions[2].color;
            hallCtx.font = '16px Arial';
            hallCtx.fillText(dimensions[2].name, pp1.x - 10, pp1.y + 20);

            // Draw a cube to represent the 3D space
            const size = 100;
            const vertices = [
                { x: size, y: size, z: size }, { x: -size, y: size, z: size },
                { x: size, y: -size, z: size }, { x: -size, y: -size, z: size },
                { x: size, y: size, z: -size }, { x: -size, y: size, z: -size },
                { x: size, y: -size, z: -size }, { x: -size, y: -size, z: -size }
            ];

            const edges = [
                [0, 1], [0, 2], [0, 4],
                [1, 3], [1, 5],
                [2, 3], [2, 6],
                [3, 7],
                [4, 5], [4, 6],
                [5, 7],
                [6, 7]
            ];

            const rotatedVertices = vertices.map(v => rotatePoint(v.x, v.y, v.z));
            const projectedVertices = rotatedVertices.map(projectPoint);

            hallCtx.strokeStyle = '#a0a0a0';
            hallCtx.lineWidth = 1;
            edges.forEach(edge => {
                const p1 = projectedVertices[edge[0]];
                const p2 = projectedVertices[edge[1]];
                hallCtx.beginPath();
                hallCtx.moveTo(p1.x, p1.y);
                hallCtx.lineTo(p2.x, p2.y);
                hallCtx.stroke();
            });
        }

        function animateHall3D() {
            if (autoRotate) {
                angleY += 0.005; // 自动绕Y轴旋转
                angleX += 0.001; // 自动绕X轴微小旋转
            }
            drawHall3D();
            animationFrameId = requestAnimationFrame(animateHall3D);
        }

        function stopAutoRotation() {
            autoRotate = false;
            document.getElementById('toggleRotation').textContent = '开始旋转';
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
        }

        function startAutoRotation() {
            autoRotate = true;
            document.getElementById('toggleRotation').textContent = '暂停旋转';
            if (!animationFrameId) {
                animateHall3D();
            }
        }

        hallCanvas.addEventListener('mousedown', (e) => {
            stopAutoRotation(); // 用户拖动时暂停自动旋转
            isDragging = true;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        });

        hallCanvas.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = e.clientX - lastMouseX;
                const deltaY = e.clientY - lastMouseY;
                angleY += deltaX * 0.01;
                angleX -= deltaY * 0.01;
                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
                drawHall3D();
            }
        });

        hallCanvas.addEventListener('mouseup', () => {
            isDragging = false;
        });

        hallCanvas.addEventListener('mouseout', () => {
            isDragging = false;
        });

        // Touch events for mobile
        hallCanvas.addEventListener('touchstart', (e) => {
            stopAutoRotation();
            isDragging = true;
            lastMouseX = e.touches[0].clientX;
            lastMouseY = e.touches[0].clientY;
            e.preventDefault(); // Prevent scrolling
        });

        hallCanvas.addEventListener('touchmove', (e) => {
            if (isDragging) {
                const deltaX = e.touches[0].clientX - lastMouseX;
                const deltaY = e.touches[0].clientY - lastMouseY;
                angleY += deltaX * 0.01;
                angleX -= deltaY * 0.01;
                lastMouseX = e.touches[0].clientX;
                lastMouseY = e.touches[0].clientY;
                drawHall3D();
                e.preventDefault();
            }
        });

        hallCanvas.addEventListener('touchend', () => {
            isDragging = false;
        });


        // 霍尔三维结构控制按钮
        document.getElementById('toggleRotation').addEventListener('click', () => {
            if (autoRotate) {
                stopAutoRotation();
            } else {
                startAutoRotation();
            }
        });

        document.getElementById('resetView').addEventListener('click', () => {
            angleX = Math.PI / 6;
            angleY = Math.PI / 6;
            drawHall3D();
            if (!autoRotate) { // 如果之前是暂停的，重置后仍然保持暂停
                stopAutoRotation();
            } else { // 否则重新开始旋转
                startAutoRotation();
            }
        });

        function showDimensionInfo(dimName, info) {
            alert(`维度：${dimName}

解释：${info}`);
        }

        document.getElementById('explainTime').addEventListener('click', () => {
            showDimensionInfo(dimensions[0].name, dimensions[0].info);
        });
        document.getElementById('explainLogical').addEventListener('click', () => {
            showDimensionInfo(dimensions[1].name, dimensions[1].info);
        });
        document.getElementById('explainKnowledge').addEventListener('click', () => {
            showDimensionInfo(dimensions[2].name, dimensions[2].info);
        });


        // 初始化Hall 3D Canvas
        animateHall3D();


        // 系统工程生命周期游戏化演示
        const lifecycleCanvas = document.getElementById('lifecycleCanvas');
        const lcCtx = lifecycleCanvas.getContext('2d');
        const stageInfo = document.getElementById('stageInfo');

        const stages = [
            { name: '概念研究 (Pre-Phase A)', color: '#FFD700', description: '探索新的想法和潜在的解决方案，明确任务目标。' },
            { name: '概念与技术开发 (Phase A)', color: '#FFA500', description: '从多种方案中选择一个系统概念，并制定系统级需求和初步架构。' },
            { name: '初步设计与技术完成 (Phase B)', color: '#FF8C00', description: '确立详细的初步设计，解决所有技术难题，确保可实施性。' },
            { name: '最终设计与制造 (Phase C)', color: '#FF4500', description: '完成最终的详细设计，并制定研制方案和生产计划。' },
            { name: '系统组装、集成、测试与发射 (Phase D)', color: '#FF0000', description: '将各部分组装、集成，进行全面测试，并部署系统。' },
            { name: '运行与维护 (Phase E)', color: '#B22222', description: '系统投入使用后的日常运行、性能监控和维护。' },
            { name: '报废 (Phase F)', color: '#8B0000', description: '系统生命周期结束，进行退役和安全处置。' }
        ];

        let currentStageIndex = -1;
        const totalStages = stages.length;
        const stageWidth = lifecycleCanvas.width / totalStages;

        function drawLifecycle() {
            lcCtx.clearRect(0, 0, lifecycleCanvas.width, lifecycleCanvas.height);
            const startY = lifecycleCanvas.height / 2;
            const arrowHeight = 20;

            for (let i = 0; i < totalStages; i++) {
                const x = i * stageWidth + stageWidth / 2;
                const y = startY;

                // Draw line
                if (i > 0) {
                    lcCtx.strokeStyle = '#ccc';
                    lcCtx.lineWidth = 2;
                    lcCtx.beginPath();
                    lcCtx.moveTo((i - 1) * stageWidth + stageWidth / 2, y);
                    lcCtx.lineTo(x, y);
                    lcCtx.stroke();
                }

                // Draw stage circle
                lcCtx.beginPath();
                lcCtx.arc(x, y, 15, 0, Math.PI * 2);
                lcCtx.fillStyle = stages[i].color;
                lcCtx.fill();
                lcCtx.strokeStyle = '#333';
                lcCtx.lineWidth = 1;
                lcCtx.stroke();

                // Draw stage name
                lcCtx.fillStyle = '#333';
                lcCtx.font = '14px Arial';
                lcCtx.textAlign = 'center';
                lcCtx.fillText(stages[i].name, x, y - 25);

                // Highlight current stage
                if (i === currentStageIndex) {
                    lcCtx.beginPath();
                    lcCtx.arc(x, y, 18, 0, Math.PI * 2);
                    lcCtx.strokeStyle = '#007bff';
                    lcCtx.lineWidth = 3;
                    lcCtx.stroke();

                    // Animate a pulsing effect
                    const pulseRadius = 20 + Math.sin(performance.now() / 100) * 5;
                    lcCtx.beginPath();
                    lcCtx.arc(x, y, pulseRadius, 0, Math.PI * 2);
                    lcCtx.strokeStyle = 'rgba(0, 123, 255, ' + (0.5 + Math.sin(performance.now() / 100) * 0.2) + ')';
                    lcCtx.lineWidth = 4;
                    lcCtx.stroke();
                }

                // Draw arrow
                if (i < totalStages - 1) {
                    const arrowX = x + stageWidth / 2 - 10;
                    const arrowY = y;
                    lcCtx.fillStyle = '#333';
                    lcCtx.beginPath();
                    lcCtx.moveTo(arrowX - 5, arrowY - 5);
                    lcCtx.lineTo(arrowX, arrowY);
                    lcCtx.lineTo(arrowX - 5, arrowY + 5);
                    lcCtx.fill();
                }
            }
            requestAnimationFrame(drawLifecycle); // 持续动画
        }

        document.querySelectorAll('.game-controls button').forEach((button, index) => {
            button.addEventListener('click', () => {
                currentStageIndex = index;
                stageInfo.textContent = `当前阶段：${stages[index].name} - ${stages[index].description}`;
                if (index === 3) { // 阶段4: 最终设计与制造
                    stageInfo.textContent += '  🎉恭喜你，这就是做出研制方案及生产计划的关键阶段！';
                    stageInfo.style.color = '#28a745';
                } else {
                    stageInfo.style.color = '#34495e';
                }
                drawLifecycle(); // Redraw immediately on click
            });
        });

        // Initial draw for lifecycle canvas
        drawLifecycle();

    </script>
</body>
</html> 