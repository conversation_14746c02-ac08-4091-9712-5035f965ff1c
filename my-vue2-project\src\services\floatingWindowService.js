/**
 * 悬浮窗管理服务
 * 处理悬浮窗的创建、关闭、位置管理等功能
 */

class FloatingWindowService {
  constructor() {
    this.isElectron = window.require !== undefined
    this.ipcRenderer = null
    this.floatingWindows = new Map() // 存储悬浮窗信息
    this.eventListeners = new Map() // 存储事件监听器
    
    if (this.isElectron) {
      this.ipcRenderer = window.require('electron').ipcRenderer
      this.setupEventListeners()
    }
  }
  
  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    if (!this.ipcRenderer) return
    
    // 监听悬浮窗创建事件
    this.ipcRenderer.on('floating-window-created', (event, windowId) => {
      this.onFloatingWindowCreated(windowId)
    })
    
    // 监听悬浮窗关闭事件
    this.ipcRenderer.on('floating-window-closed', (event, windowId) => {
      this.onFloatingWindowClosed(windowId)
    })
  }
  
  /**
   * 创建悬浮窗
   * @param {Object} options - 悬浮窗配置选项
   * @returns {Promise<Object>} 创建结果
   */
  async createFloatingWindow(options = {}) {
    if (!this.isElectron) {
      console.warn('悬浮窗功能仅在Electron环境中可用')
      return { success: false, error: '不支持的环境' }
    }
    
    try {
      // 获取保存的窗口位置
      const savedPosition = this.getSavedWindowPosition()
      const windowOptions = {
        width: 300,
        height: 400,
        x: 100,
        y: 100,
        ...savedPosition,
        ...options
      }
      
      const result = await this.ipcRenderer.invoke('create-floating-window', windowOptions)
      
      if (result.success) {
        this.floatingWindows.set(result.windowId, {
          id: result.windowId,
          options: windowOptions,
          createdAt: new Date()
        })
        
        this.emit('window-created', result.windowId)
      }
      
      return result
    } catch (error) {
      console.error('创建悬浮窗失败:', error)
      return { success: false, error: error.message }
    }
  }
  
  /**
   * 关闭悬浮窗
   * @param {string} windowId - 窗口ID
   * @returns {Promise<Object>} 关闭结果
   */
  async closeFloatingWindow(windowId) {
    if (!this.isElectron) {
      return { success: false, error: '不支持的环境' }
    }
    
    try {
      const result = await this.ipcRenderer.invoke('close-floating-window', windowId)
      
      if (result.success) {
        this.floatingWindows.delete(windowId)
        this.emit('window-closed', windowId)
      }
      
      return result
    } catch (error) {
      console.error('关闭悬浮窗失败:', error)
      return { success: false, error: error.message }
    }
  }
  
  /**
   * 关闭所有悬浮窗
   * @returns {Promise<Object>} 关闭结果
   */
  async closeAllFloatingWindows() {
    if (!this.isElectron) {
      return { success: false, error: '不支持的环境' }
    }
    
    try {
      const result = await this.ipcRenderer.invoke('close-all-floating-windows')
      
      if (result.success) {
        this.floatingWindows.clear()
        this.emit('all-windows-closed')
      }
      
      return result
    } catch (error) {
      console.error('关闭所有悬浮窗失败:', error)
      return { success: false, error: error.message }
    }
  }
  
  /**
   * 获取悬浮窗列表
   * @returns {Promise<Object>} 窗口列表
   */
  async getFloatingWindows() {
    if (!this.isElectron) {
      return { success: false, error: '不支持的环境' }
    }
    
    try {
      const result = await this.ipcRenderer.invoke('get-floating-windows')
      return result
    } catch (error) {
      console.error('获取悬浮窗列表失败:', error)
      return { success: false, error: error.message }
    }
  }
  
  /**
   * 保存窗口位置
   * @param {Object} position - 位置信息 {x, y, width, height}
   */
  saveWindowPosition(position) {
    try {
      localStorage.setItem('floating-window-position', JSON.stringify(position))
    } catch (error) {
      console.error('保存窗口位置失败:', error)
    }
  }
  
  /**
   * 获取保存的窗口位置
   * @returns {Object|null} 位置信息
   */
  getSavedWindowPosition() {
    try {
      const saved = localStorage.getItem('floating-window-position')
      return saved ? JSON.parse(saved) : null
    } catch (error) {
      console.error('获取保存的窗口位置失败:', error)
      return null
    }
  }
  
  /**
   * 获取当前活动的悬浮窗数量
   * @returns {number} 窗口数量
   */
  getActiveWindowCount() {
    return this.floatingWindows.size
  }
  
  /**
   * 检查是否有活动的悬浮窗
   * @returns {boolean} 是否有活动窗口
   */
  hasActiveWindows() {
    return this.floatingWindows.size > 0
  }
  
  /**
   * 获取悬浮窗信息
   * @param {string} windowId - 窗口ID
   * @returns {Object|null} 窗口信息
   */
  getWindowInfo(windowId) {
    return this.floatingWindows.get(windowId) || null
  }
  
  /**
   * 悬浮窗创建事件处理
   * @param {string} windowId - 窗口ID
   */
  onFloatingWindowCreated(windowId) {
    console.log('悬浮窗已创建:', windowId)
    this.emit('window-created', windowId)
  }
  
  /**
   * 悬浮窗关闭事件处理
   * @param {string} windowId - 窗口ID
   */
  onFloatingWindowClosed(windowId) {
    console.log('悬浮窗已关闭:', windowId)
    this.floatingWindows.delete(windowId)
    this.emit('window-closed', windowId)
  }
  
  /**
   * 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }
  
  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }
  
  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {...any} args - 事件参数
   */
  emit(event, ...args) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(...args)
        } catch (error) {
          console.error('事件回调执行失败:', error)
        }
      })
    }
  }
  
  /**
   * 清理资源
   */
  destroy() {
    if (this.ipcRenderer) {
      this.ipcRenderer.removeAllListeners('floating-window-created')
      this.ipcRenderer.removeAllListeners('floating-window-closed')
    }
    
    this.eventListeners.clear()
    this.floatingWindows.clear()
  }
}

// 创建单例实例
const floatingWindowService = new FloatingWindowService()

export default floatingWindowService
