<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络系统生命周期 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .option:hover {
            border-color: #667eea;
            background: #f0f4ff;
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #4caf50;
            background: #e8f5e8;
            animation: pulse 0.6s ease-in-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .stage-info {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .stage-title {
            font-size: 1.3rem;
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .stage-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }

        .controls {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .hidden {
            display: none;
        }

        .explanation {
            background: #f0f4ff;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin-top: 20px;
            border-radius: 0 10px 10px 0;
            animation: slideInLeft 0.5s ease-out;
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🌐 网络系统生命周期</h1>
            <p class="subtitle">通过动画和交互学习网络系统的5个发展阶段</p>
        </div>

        <div class="question-card">
            <h2 class="question-title">📝 题目练习</h2>
            <p style="margin-bottom: 20px; font-size: 1.1rem; line-height: 1.6;">
                网络系统生命周期可以划分为5个阶段，实施这5个阶段的合理顺序是（ ）。
            </p>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 需求规范、通信规范、逻辑网络设计、物理网络设计、实施阶段
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 需求规范、逻辑网络设计、通信规范、物理网络设计、实施阶段
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 通信规范、物理网络设计、需求规范、逻辑网络设计、实施阶段
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 通信规范、需求规范、逻辑网络设计、物理网络设计、实施阶段
                </div>
            </div>

            <div id="explanation" class="explanation hidden">
                <h3>💡 正确答案：A</h3>
                <p>网络系统生命周期必须按照逻辑顺序进行，就像盖房子一样，必须先打地基再建墙！</p>
            </div>
        </div>

        <div class="canvas-container">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">🎬 动画演示：网络系统生命周期</h2>
            <canvas id="lifecycleCanvas" width="1000" height="400" style="border: 2px solid #e0e0e0; border-radius: 10px; display: block; margin: 0 auto;"></canvas>
            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重新开始</button>
                <button class="btn" onclick="nextStage()">➡️ 下一阶段</button>
            </div>
        </div>

        <div id="stageDetails">
            <!-- 阶段详情将通过JavaScript动态生成 -->
        </div>

        <div class="stage-info">
            <div class="stage-title">
                <div class="stage-number">💡</div>
                学习要点总结
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="background: #fff3e0; padding: 20px; border-radius: 10px; border-left: 4px solid #ff9800;">
                    <h4>🎯 记忆口诀</h4>
                    <p style="font-size: 1.1rem; margin-top: 10px;">
                        <strong>"需通逻物实"</strong><br>
                        需求→通信→逻辑→物理→实施
                    </p>
                </div>
                <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; border-left: 4px solid #4caf50;">
                    <h4>🏗️ 类比记忆</h4>
                    <p style="margin-top: 10px;">
                        就像盖房子：<br>
                        1️⃣ 确定需求（要几室几厅）<br>
                        2️⃣ 制定标准（建筑规范）<br>
                        3️⃣ 设计图纸（平面图）<br>
                        4️⃣ 选择材料（具体建材）<br>
                        5️⃣ 开工建设（实际施工）
                    </p>
                </div>
            </div>
        </div>

        <div class="stage-info">
            <div class="stage-title">
                <div class="stage-number">❓</div>
                常见错误分析
            </div>
            <div style="background: #ffebee; padding: 20px; border-radius: 10px; margin-top: 15px;">
                <h4 style="color: #d32f2f; margin-bottom: 15px;">为什么其他选项是错误的？</h4>
                <div style="margin-bottom: 10px;">
                    <strong>选项B错误：</strong> 逻辑设计在通信规范之前是不合理的，因为不知道通信标准就无法设计逻辑结构。
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>选项C错误：</strong> 没有需求分析就制定通信规范，就像不知道要建什么就开始买材料。
                </div>
                <div>
                    <strong>选项D错误：</strong> 通信规范应该在需求规范之后，因为通信方式要根据需求来确定。
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('lifecycleCanvas');
        const ctx = canvas.getContext('2d');
        let currentStage = 0;
        let animationId;
        let isAnimating = false;

        const stages = [
            {
                name: "需求规范",
                icon: "📋",
                description: "明确网络系统的功能需求和性能要求",
                details: "就像装修房子前要列清单：需要几个房间？什么功能？预算多少？网络也是一样，要先搞清楚需要什么功能。",
                color: "#ff6b6b",
                x: 100
            },
            {
                name: "通信规范", 
                icon: "📡",
                description: "制定网络通信的标准和协议",
                details: "确定大家怎么'说话'：用什么语言（协议）？怎么传递信息？就像约定用中文还是英文交流。",
                color: "#4ecdc4",
                x: 250
            },
            {
                name: "逻辑网络设计",
                icon: "🧠", 
                description: "设计网络的逻辑结构和拓扑",
                details: "画出网络的'思维导图'：哪些设备连哪些？数据怎么流动？就像设计交通路线图。",
                color: "#45b7d1",
                x: 400
            },
            {
                name: "物理网络设计",
                icon: "🔧",
                description: "确定具体的硬件设备和物理连接",
                details: "选择具体的设备：用什么路由器？多长的网线？放在哪里？就像买具体的建材。",
                color: "#96ceb4", 
                x: 550
            },
            {
                name: "实施阶段",
                icon: "🚀",
                description: "安装配置设备，部署网络系统",
                details: "开始动手搭建：安装设备、连接网线、配置参数，让网络真正运行起来！",
                color: "#feca57",
                x: 700
            }
        ];

        function drawStage(stage, index, isActive = false, isCompleted = false) {
            const y = 200;
            const radius = isActive ? 50 : 40;

            // 绘制连接线
            if (index > 0) {
                ctx.strokeStyle = isCompleted ? '#4caf50' : '#e0e0e0';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(stages[index-1].x + 40, y);
                ctx.lineTo(stage.x - 40, y);
                ctx.stroke();

                // 绘制箭头
                if (isCompleted || isActive) {
                    drawArrow(stages[index-1].x + 40, y, stage.x - 40, y);
                }
            }

            // 绘制圆圈背景光晕效果
            if (isActive) {
                ctx.shadowColor = stage.color;
                ctx.shadowBlur = 20;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;
            }

            // 绘制圆圈
            ctx.fillStyle = isCompleted ? '#4caf50' : isActive ? stage.color : '#f0f0f0';
            ctx.strokeStyle = isActive ? stage.color : '#ddd';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(stage.x, y, radius, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();

            // 重置阴影
            ctx.shadowBlur = 0;

            // 绘制图标
            ctx.font = `${radius * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillStyle = isCompleted ? 'white' : isActive ? 'white' : '#999';
            ctx.fillText(stage.icon, stage.x, y + 8);

            // 绘制标题
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillStyle = isActive ? stage.color : '#333';
            ctx.fillText(stage.name, stage.x, y + radius + 25);

            // 绘制序号
            ctx.font = 'bold 16px Arial';
            ctx.fillStyle = isCompleted ? '#4caf50' : isActive ? stage.color : '#999';
            const numberY = y - radius - 15;
            ctx.fillText((index + 1).toString(), stage.x, numberY);

            // 绘制进度描述
            if (isActive) {
                ctx.font = '12px Microsoft YaHei';
                ctx.fillStyle = '#666';
                ctx.fillText('当前阶段', stage.x, y + radius + 45);
            } else if (isCompleted) {
                ctx.font = '12px Microsoft YaHei';
                ctx.fillStyle = '#4caf50';
                ctx.fillText('已完成', stage.x, y + radius + 45);
            }
        }

        function drawArrow(fromX, fromY, toX, toY) {
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            
            ctx.strokeStyle = '#4caf50';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawAllStages() {
            clearCanvas();
            stages.forEach((stage, index) => {
                const isActive = index === currentStage;
                const isCompleted = index < currentStage;
                drawStage(stage, index, isActive, isCompleted);
            });
        }

        function updateStageDetails() {
            const stageDetailsDiv = document.getElementById('stageDetails');
            if (currentStage < stages.length) {
                const stage = stages[currentStage];
                stageDetailsDiv.innerHTML = `
                    <div class="stage-info">
                        <div class="stage-title">
                            <div class="stage-number">${currentStage + 1}</div>
                            ${stage.icon} ${stage.name}
                        </div>
                        <p style="margin-bottom: 15px; font-size: 1.1rem; color: #555;">${stage.description}</p>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid ${stage.color};">
                            <strong>💡 通俗解释：</strong>${stage.details}
                        </div>
                    </div>
                `;
            }
        }

        function startAnimation() {
            if (isAnimating) return;
            isAnimating = true;
            currentStage = 0;
            animateStages();
        }

        function animateStages() {
            if (currentStage >= stages.length) {
                isAnimating = false;
                return;
            }
            
            drawAllStages();
            updateStageDetails();
            
            setTimeout(() => {
                currentStage++;
                animateStages();
            }, 2000);
        }

        function nextStage() {
            if (currentStage < stages.length - 1) {
                currentStage++;
                drawAllStages();
                updateStageDetails();
            }
        }

        function resetAnimation() {
            isAnimating = false;
            currentStage = 0;
            drawAllStages();
            updateStageDetails();
        }

        // Canvas点击交互
        canvas.addEventListener('click', function(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // 检查点击的是哪个阶段
            stages.forEach((stage, index) => {
                const distance = Math.sqrt((x - stage.x) ** 2 + (y - 200) ** 2);
                if (distance <= 50) {
                    currentStage = index;
                    drawAllStages();
                    updateStageDetails();

                    // 添加点击反馈动画
                    animateClickFeedback(stage.x, 200);
                }
            });
        });

        function animateClickFeedback(x, y) {
            let radius = 0;
            const maxRadius = 30;
            const animate = () => {
                drawAllStages();

                // 绘制点击波纹效果
                ctx.strokeStyle = 'rgba(102, 126, 234, 0.6)';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, 2 * Math.PI);
                ctx.stroke();

                radius += 2;
                if (radius < maxRadius) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 题目交互
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const explanation = document.getElementById('explanation');

                // 禁用其他选项
                document.querySelectorAll('.option').forEach(opt => {
                    opt.style.pointerEvents = 'none';
                });

                if (answer === 'A') {
                    this.classList.add('correct');
                    explanation.classList.remove('hidden');

                    // 添加成功音效（视觉反馈）
                    this.style.transform = 'scale(1.05)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 200);

                    // 添加成功动画
                    setTimeout(() => {
                        startAnimation();
                    }, 1500);
                } else {
                    this.style.background = '#ffebee';
                    this.style.borderColor = '#f44336';
                    this.style.transform = 'translateX(-5px)';

                    setTimeout(() => {
                        this.style.transform = 'translateX(5px)';
                    }, 100);

                    setTimeout(() => {
                        this.style.transform = 'translateX(0)';
                        this.style.background = '#f8f9fa';
                        this.style.borderColor = '#e0e0e0';

                        // 重新启用选项
                        document.querySelectorAll('.option').forEach(opt => {
                            opt.style.pointerEvents = 'auto';
                        });
                    }, 1000);
                }
            });
        });

        // 键盘快捷键
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case 'ArrowRight':
                    nextStage();
                    break;
                case 'ArrowLeft':
                    if (currentStage > 0) {
                        currentStage--;
                        drawAllStages();
                        updateStageDetails();
                    }
                    break;
                case ' ':
                    event.preventDefault();
                    startAnimation();
                    break;
                case 'r':
                    resetAnimation();
                    break;
            }
        });

        // 添加键盘提示
        const keyboardHint = document.createElement('div');
        keyboardHint.innerHTML = `
            <div style="position: fixed; bottom: 20px; right: 20px; background: rgba(0,0,0,0.8); color: white; padding: 15px; border-radius: 10px; font-size: 12px;">
                <div>⌨️ 键盘快捷键：</div>
                <div>← → 切换阶段</div>
                <div>空格 开始动画</div>
                <div>R 重置</div>
            </div>
        `;
        document.body.appendChild(keyboardHint);

        // 初始化
        drawAllStages();
        updateStageDetails();
    </script>
</body>
</html>
