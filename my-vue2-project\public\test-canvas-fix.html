<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Arc 修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
        }
        canvas {
            border: 1px solid #333;
            background: #000;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #2980b9;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #2c3e50;
            border-radius: 5px;
        }
        .error {
            background: #e74c3c;
        }
        .success {
            background: #27ae60;
        }
    </style>
</head>
<body>
    <h1>Canvas Arc 负半径修复测试</h1>
    
    <div class="controls">
        <button onclick="testOriginalCode()">测试原始代码（可能出错）</button>
        <button onclick="testFixedCode()">测试修复后代码</button>
        <button onclick="clearCanvas()">清空画布</button>
    </div>
    
    <div id="status" class="status">点击按钮开始测试...</div>
    
    <canvas id="testCanvas" width="800" height="400"></canvas>

    <script>
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        const statusDiv = document.getElementById('status');
        let animationFrame = 0;
        let animationId = null;

        // 辅助函数：确保半径为正数
        function ensurePositiveRadius(radius, minRadius = 0.1) {
            return Math.max(minRadius, radius);
        }

        function updateStatus(message, isError = false, isSuccess = false) {
            statusDiv.textContent = message;
            statusDiv.className = 'status';
            if (isError) statusDiv.className += ' error';
            if (isSuccess) statusDiv.className += ' success';
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            updateStatus('画布已清空');
        }

        function testOriginalCode() {
            clearCanvas();
            updateStatus('测试原始代码...');
            
            try {
                // 模拟原始代码中可能产生负半径的情况
                for (let i = 0; i < 150; i++) {
                    const x = (i * 137.5) % canvas.width;
                    const y = (i * 73.3) % canvas.height;
                    // 这里可能产生负值
                    const size = Math.sin(animationFrame * 0.01 + i) * 1 + 0.5;
                    const alpha = Math.sin(animationFrame * 0.02 + i) * 0.5 + 0.5;

                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                    ctx.beginPath();
                    ctx.arc(x, y, size, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                // 测试飞船尾迹（另一个可能出错的地方）
                for (let i = 0; i < 10; i++) {
                    const trailX = 400 - i * 15;
                    const trailY = 200;
                    const radius = 3 - i * 0.5; // 当i > 6时会变成负数
                    
                    ctx.fillStyle = `rgba(0, 212, 255, 0.6)`;
                    ctx.beginPath();
                    ctx.arc(trailX, trailY, radius, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                ctx.globalAlpha = 1;
                updateStatus('原始代码测试完成 - 如果看到错误，说明存在负半径问题', false, true);
                
            } catch (error) {
                updateStatus(`原始代码出错: ${error.message}`, true);
                console.error('原始代码错误:', error);
            }
        }

        function testFixedCode() {
            clearCanvas();
            updateStatus('测试修复后代码...');
            
            try {
                // 使用修复后的代码
                for (let i = 0; i < 150; i++) {
                    const x = (i * 137.5) % canvas.width;
                    const y = (i * 73.3) % canvas.height;
                    // 使用 ensurePositiveRadius 确保半径为正数
                    const size = ensurePositiveRadius(Math.sin(animationFrame * 0.01 + i) * 1 + 0.5);
                    const alpha = Math.sin(animationFrame * 0.02 + i) * 0.5 + 0.5;

                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                    ctx.beginPath();
                    ctx.arc(x, y, size, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                // 测试修复后的飞船尾迹
                for (let i = 0; i < 10; i++) {
                    const trailX = 400 - i * 15;
                    const trailY = 200;
                    const radius = ensurePositiveRadius(3 - i * 0.5, 0.5);
                    
                    ctx.fillStyle = `rgba(0, 212, 255, 0.6)`;
                    ctx.beginPath();
                    ctx.arc(trailX, trailY, radius, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                ctx.globalAlpha = 1;
                updateStatus('修复后代码测试完成 - 应该没有错误！', false, true);
                
            } catch (error) {
                updateStatus(`修复后代码仍有错误: ${error.message}`, true);
                console.error('修复后代码错误:', error);
            }
        }

        // 增加动画帧计数来模拟动画效果
        function incrementFrame() {
            animationFrame++;
            animationId = requestAnimationFrame(incrementFrame);
        }
        
        // 开始动画循环
        incrementFrame();
    </script>
</body>
</html>
