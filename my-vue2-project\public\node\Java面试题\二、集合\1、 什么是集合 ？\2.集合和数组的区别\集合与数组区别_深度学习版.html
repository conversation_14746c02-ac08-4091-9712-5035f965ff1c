<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集合与数组的区别 - 深度学习版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1500px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            animation: fadeInDown 1.5s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 4rem;
            margin-bottom: 30px;
            text-shadow: 4px 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #ffeaa7, #fd79a8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 1.4rem;
            margin-bottom: 40px;
            font-weight: 300;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            padding: 60px;
            margin-bottom: 60px;
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.3);
            opacity: 0;
            transform: translateY(80px);
            animation: slideInUp 1.2s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }
        .section:nth-child(6) { animation-delay: 1.0s; }
        .section:nth-child(7) { animation-delay: 1.2s; }

        .section-title {
            font-size: 3rem;
            color: #2d3436;
            margin-bottom: 50px;
            text-align: center;
            position: relative;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 50px 0;
            position: relative;
        }

        canvas {
            border-radius: 25px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: all 0.4s ease;
        }

        canvas:hover {
            transform: scale(1.02) translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .text-content {
            font-size: 1.3rem;
            line-height: 2.2;
            color: #2d3436;
            margin: 40px 0;
            text-align: center;
        }

        .highlight {
            background: linear-gradient(120deg, #667eea 0%, #764ba2 100%);
            padding: 6px 15px;
            border-radius: 12px;
            font-weight: bold;
            color: white;
            display: inline-block;
            margin: 0 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transform: translateY(-2px);
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 35px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.4s ease;
            margin: 20px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .interactive-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }

        .interactive-btn:hover::before {
            left: 100%;
        }

        .interactive-btn:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin: 40px 0;
        }

        .comparison-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 40px;
            border-radius: 25px;
            color: white;
            text-align: center;
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
            transition: all 0.4s ease;
        }

        .comparison-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .comparison-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .progress-container {
            background: rgba(255,255,255,0.3);
            border-radius: 20px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: rgba(255,255,255,0.3);
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 1s ease;
            border-radius: 5px;
        }

        .game-area {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 25px;
            padding: 40px;
            margin: 40px 0;
            border: 3px dashed #667eea;
            min-height: 250px;
            position: relative;
        }

        .data-item {
            display: inline-block;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            font-weight: bold;
        }

        .basic-type {
            background: linear-gradient(45deg, #fd79a8, #e84393);
            color: white;
        }

        .reference-type {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            color: white;
        }

        .data-item:hover {
            transform: scale(1.1) rotate(3deg);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-60px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-15px); }
            60% { transform: translateY(-8px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.08); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-25px); }
        }

        .floating {
            animation: float 4s ease-in-out infinite;
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚖️ 集合与数组的区别</h1>
            <p class="subtitle">深度解析两种数据容器的核心差异</p>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <p style="color: white; margin: 0; font-size: 1.1rem;">学习进度</p>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 核心区别概览</h2>
            <div class="canvas-container">
                <canvas id="overviewCanvas" width="900" height="500"></canvas>
            </div>
            <div class="text-content">
                <p>集合和数组是两种重要的数据容器，它们有着本质的区别：</p>
                <div class="comparison-grid">
                    <div class="comparison-card">
                        <h3>📊 数组</h3>
                        <p>固定长度的传统容器</p>
                    </div>
                    <div class="comparison-card">
                        <h3>📦 集合</h3>
                        <p>可变长度的现代容器</p>
                    </div>
                </div>
                <button class="interactive-btn" onclick="animateOverview()">🎬 播放概览动画</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📏 区别一：长度特性</h2>
            <div class="canvas-container">
                <canvas id="lengthCanvas" width="800" height="450"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">数组是固定长度的；集合是可变长度的。</span></p>
                <p>数组就像固定大小的停车场，而集合像可以无限扩展的魔法空间！</p>
                <button class="interactive-btn" onclick="animateLength()">📏 观看长度对比</button>
                <button class="interactive-btn" onclick="playLengthGame()">🎮 长度体验游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎭 区别二：数据类型支持</h2>
            <div class="canvas-container">
                <canvas id="dataTypeCanvas" width="800" height="500"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">数组可以存储基本数据类型，也可以存储引用数据类型；集合只能存储引用数据类型。</span></p>
                <p>数组是全能选手，集合是专业选手！</p>
                <button class="interactive-btn" onclick="animateDataType()">🎭 查看类型支持</button>
                <button class="interactive-btn" onclick="playDataTypeGame()">🎮 类型分类游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🌈 区别三：元素类型一致性</h2>
            <div class="canvas-container">
                <canvas id="consistencyCanvas" width="800" height="450"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">数组存储的元素必须是同一个数据类型；集合存储的对象可以是不同数据类型。</span></p>
                <p>数组像严格的军队，集合像多元的社区！</p>
                <button class="interactive-btn" onclick="animateConsistency()">🌈 观看一致性对比</button>
                <button class="interactive-btn" onclick="playConsistencyGame()">🎮 一致性挑战</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🏗️ 数据结构深度解析</h2>
            <div class="canvas-container">
                <canvas id="structureCanvas" width="900" height="400"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">数据结构：就是容器中存储数据的方式。</span></p>
                <p>对于集合容器，有很多种。因为每一个容器的自身特点不同，其实原理在于每个容器的内部数据结构不同。</p>
                <button class="interactive-btn" onclick="animateStructure()">🏗️ 探索数据结构</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🌳 集合体系架构</h2>
            <div class="canvas-container">
                <canvas id="hierarchyCanvas" width="900" height="500"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">集合容器在不断向上抽取过程中，出现了集合体系。</span></p>
                <p><span class="highlight">在使用一个体系的原则：参阅顶层内容。建立底层对象。</span></p>
                <p>就像一棵知识树，根部是具体实现，顶部是抽象接口！</p>
                <button class="interactive-btn" onclick="animateHierarchy()">🌳 查看体系结构</button>
                <button class="interactive-btn" onclick="playHierarchyGame()">🎮 体系构建游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎓 学习总结</h2>
            <div class="canvas-container">
                <canvas id="summaryCanvas" width="800" height="350"></canvas>
            </div>
            <div class="text-content">
                <p>🎉 恭喜！你已经掌握了集合与数组的核心区别：</p>
                <div class="comparison-grid">
                    <div class="comparison-card">
                        <h3>📏 长度特性</h3>
                        <p>数组固定 vs 集合可变</p>
                    </div>
                    <div class="comparison-card">
                        <h3>🎭 类型支持</h3>
                        <p>数组全能 vs 集合专业</p>
                    </div>
                    <div class="comparison-card">
                        <h3>🌈 元素一致性</h3>
                        <p>数组统一 vs 集合多样</p>
                    </div>
                    <div class="comparison-card">
                        <h3>🏗️ 内部结构</h3>
                        <p>不同结构决定特性</p>
                    </div>
                </div>
                <button class="interactive-btn" onclick="animateSummary()">🎊 播放总结动画</button>
                <button class="interactive-btn" onclick="playAllAnimations()">🎬 播放全部动画</button>
            </div>
        </div>
    </div>

    <script>
        let currentProgress = 0;
        
        function updateProgress(progress) {
            currentProgress = Math.min(100, currentProgress + progress);
            document.getElementById('progressFill').style.width = currentProgress + '%';
        }

        // 概览动画
        function animateOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景渐变
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(0.5, '#764ba2');
                gradient.addColorStop(1, '#f093fb');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                
                // 主标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 36px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数组 VS 集合', centerX, 80);
                
                // 数组部分（左侧）
                if (frame > 30) {
                    const progress1 = Math.min(1, (frame - 30) / 80);
                    ctx.save();
                    ctx.globalAlpha = progress1;
                    
                    // 数组容器
                    ctx.fillStyle = 'rgba(253, 121, 168, 0.9)';
                    ctx.fillRect(100, 150, 300, 200);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(100, 150, 300, 200);
                    
                    // 数组标题
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 24px Arial';
                    ctx.fillText('📊 数组', 250, 130);
                    
                    // 数组特点
                    ctx.font = '16px Arial';
                    ctx.fillText('✓ 固定长度', 250, 180);
                    ctx.fillText('✓ 存储基本+引用类型', 250, 210);
                    ctx.fillText('✓ 同一数据类型', 250, 240);
                    
                    // 数组元素
                    for (let i = 0; i < 5; i++) {
                        ctx.fillStyle = 'white';
                        ctx.fillRect(120 + i * 50, 280, 40, 40);
                        ctx.strokeStyle = '#fd79a8';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(120 + i * 50, 280, 40, 40);
                        ctx.fillStyle = '#fd79a8';
                        ctx.font = '14px Arial';
                        ctx.fillText(i + 1, 140 + i * 50, 305);
                    }
                    
                    ctx.restore();
                }
                
                // 集合部分（右侧）
                if (frame > 60) {
                    const progress2 = Math.min(1, (frame - 60) / 80);
                    ctx.save();
                    ctx.globalAlpha = progress2;
                    
                    // 集合容器（可变大小）
                    const collectionSize = 3 + Math.floor(Math.sin(frame * 0.05) * 2 + 2);
                    const containerWidth = 200 + collectionSize * 20;
                    
                    ctx.fillStyle = 'rgba(116, 185, 255, 0.9)';
                    ctx.fillRect(500, 150, containerWidth, 200);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(500, 150, containerWidth, 200);
                    
                    // 集合标题
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 24px Arial';
                    ctx.fillText('📦 集合', 500 + containerWidth/2, 130);
                    
                    // 集合特点
                    ctx.font = '16px Arial';
                    ctx.fillText('✓ 可变长度', 500 + containerWidth/2, 180);
                    ctx.fillText('✓ 只存储引用类型', 500 + containerWidth/2, 210);
                    ctx.fillText('✓ 不同数据类型', 500 + containerWidth/2, 240);
                    
                    // 集合元素（不同类型）
                    const elementTypes = ['📚', '🚗', '👨‍🎓', '📱', '🍎'];
                    for (let i = 0; i < collectionSize; i++) {
                        ctx.fillStyle = 'white';
                        ctx.fillRect(520 + i * 45, 280, 35, 40);
                        ctx.strokeStyle = '#74b9ff';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(520 + i * 45, 280, 35, 40);
                        ctx.font = '20px Arial';
                        ctx.fillText(elementTypes[i % elementTypes.length], 537.5 + i * 45, 305);
                    }
                    
                    ctx.restore();
                }
                
                frame++;
                if (frame < 250) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }
            
            draw();
        }

        // 长度特性动画
        function animateLength() {
            const canvas = document.getElementById('lengthCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            let arrayElements = 5;
            let collectionElements = 1;
            let growing = true;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#fd79a8');
                gradient.addColorStop(0.5, '#fdcb6e');
                gradient.addColorStop(1, '#74b9ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('📏 长度特性对比', canvas.width / 2, 50);

                // 数组部分（固定长度）
                ctx.fillStyle = 'rgba(253, 121, 168, 0.9)';
                ctx.fillRect(50, 120, 300, 100);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 3;
                ctx.strokeRect(50, 120, 300, 100);

                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Arial';
                ctx.fillText('📊 数组 (固定长度)', 200, 100);

                // 数组元素（始终5个）
                for (let i = 0; i < arrayElements; i++) {
                    ctx.fillStyle = 'white';
                    ctx.fillRect(70 + i * 50, 140, 40, 60);
                    ctx.strokeStyle = '#fd79a8';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(70 + i * 50, 140, 40, 60);
                    ctx.fillStyle = '#fd79a8';
                    ctx.font = '16px Arial';
                    ctx.fillText(i + 1, 90 + i * 50, 175);
                }

                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText(`长度: ${arrayElements} (不可变)`, 200, 250);

                // 集合部分（可变长度）
                if (frame % 40 === 0) {
                    if (growing) {
                        collectionElements++;
                        if (collectionElements >= 8) growing = false;
                    } else {
                        collectionElements--;
                        if (collectionElements <= 1) growing = true;
                    }
                }

                const collectionWidth = 100 + collectionElements * 45;
                ctx.fillStyle = 'rgba(116, 185, 255, 0.9)';
                ctx.fillRect(450, 120, collectionWidth, 100);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 3;
                ctx.strokeRect(450, 120, collectionWidth, 100);

                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Arial';
                ctx.fillText('📦 集合 (可变长度)', 450 + collectionWidth/2, 100);

                // 集合元素（动态数量）
                for (let i = 0; i < collectionElements; i++) {
                    ctx.fillStyle = 'white';
                    ctx.fillRect(470 + i * 40, 140, 35, 60);
                    ctx.strokeStyle = '#74b9ff';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(470 + i * 40, 140, 35, 60);
                    ctx.fillStyle = '#74b9ff';
                    ctx.font = '14px Arial';
                    ctx.fillText(i + 1, 487.5 + i * 40, 175);
                }

                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText(`长度: ${collectionElements} (可变)`, 450 + collectionWidth/2, 250);

                // 动态指示
                if (growing) {
                    ctx.fillStyle = '#00b894';
                    ctx.font = '18px Arial';
                    ctx.fillText('↗️ 增长中', 450 + collectionWidth/2, 280);
                } else {
                    ctx.fillStyle = '#e17055';
                    ctx.font = '18px Arial';
                    ctx.fillText('↘️ 缩小中', 450 + collectionWidth/2, 280);
                }

                // 对比箭头
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(370, 170);
                ctx.lineTo(430, 170);
                ctx.stroke();

                ctx.fillStyle = 'white';
                ctx.font = '20px Arial';
                ctx.fillText('VS', 400, 175);

                frame++;
                if (frame < 400) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 数据类型支持动画
        function animateDataType() {
            const canvas = document.getElementById('dataTypeCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            const basicTypes = [
                { name: 'int', value: '42', color: '#fd79a8' },
                { name: 'double', value: '3.14', color: '#fdcb6e' },
                { name: 'boolean', value: 'true', color: '#e17055' },
                { name: 'char', value: "'A'", color: '#a29bfe' }
            ];

            const referenceTypes = [
                { name: 'String', value: '"Hello"', color: '#74b9ff' },
                { name: 'Student', value: '👨‍🎓', color: '#00b894' },
                { name: 'Car', value: '🚗', color: '#6c5ce7' },
                { name: 'Book', value: '📚', color: '#fd79a8' }
            ];

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#a29bfe');
                gradient.addColorStop(1, '#6c5ce7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎭 数据类型支持对比', canvas.width / 2, 50);

                // 数组部分
                if (frame > 30) {
                    const progress1 = Math.min(1, (frame - 30) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress1;

                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.fillRect(50, 100, 300, 350);
                    ctx.strokeStyle = '#fd79a8';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(50, 100, 300, 350);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 22px Arial';
                    ctx.fillText('📊 数组', 200, 130);
                    ctx.font = '16px Arial';
                    ctx.fillText('支持基本类型 + 引用类型', 200, 155);

                    // 基本类型区域
                    ctx.fillStyle = '#fd79a8';
                    ctx.fillRect(70, 180, 260, 100);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText('基本数据类型', 200, 200);

                    basicTypes.forEach((type, i) => {
                        const x = 90 + (i % 2) * 120;
                        const y = 220 + Math.floor(i / 2) * 30;
                        ctx.fillStyle = type.color;
                        ctx.fillRect(x, y, 100, 25);
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.fillText(`${type.name}: ${type.value}`, x + 50, y + 17);
                    });

                    // 引用类型区域
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(70, 300, 260, 120);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText('引用数据类型', 200, 320);

                    referenceTypes.forEach((type, i) => {
                        const x = 90 + (i % 2) * 120;
                        const y = 340 + Math.floor(i / 2) * 30;
                        ctx.fillStyle = type.color;
                        ctx.fillRect(x, y, 100, 25);
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.fillText(`${type.name}`, x + 50, y + 17);
                    });

                    ctx.restore();
                }

                // 集合部分
                if (frame > 90) {
                    const progress2 = Math.min(1, (frame - 90) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress2;

                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.fillRect(450, 100, 300, 350);
                    ctx.strokeStyle = '#74b9ff';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(450, 100, 300, 350);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 22px Arial';
                    ctx.fillText('📦 集合', 600, 130);
                    ctx.font = '16px Arial';
                    ctx.fillText('只支持引用类型', 600, 155);

                    // 禁止基本类型
                    ctx.fillStyle = '#ddd';
                    ctx.fillRect(470, 180, 260, 100);
                    ctx.strokeStyle = '#e17055';
                    ctx.lineWidth = 3;
                    ctx.setLineDash([10, 5]);
                    ctx.strokeRect(470, 180, 260, 100);

                    ctx.fillStyle = '#e17055';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText('❌ 不支持基本类型', 600, 200);

                    // 大红叉
                    ctx.strokeStyle = '#e17055';
                    ctx.lineWidth = 5;
                    ctx.beginPath();
                    ctx.moveTo(480, 220);
                    ctx.lineTo(740, 260);
                    ctx.moveTo(740, 220);
                    ctx.lineTo(480, 260);
                    ctx.stroke();

                    // 支持引用类型
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(470, 300, 260, 120);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText('✅ 支持引用类型', 600, 320);

                    referenceTypes.forEach((type, i) => {
                        const x = 490 + (i % 2) * 120;
                        const y = 340 + Math.floor(i / 2) * 30;
                        ctx.fillStyle = type.color;
                        ctx.fillRect(x, y, 100, 25);
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.fillText(`${type.name}`, x + 50, y + 17);
                    });

                    ctx.restore();
                }

                frame++;
                if (frame < 250) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 长度体验游戏
        function playLengthGame() {
            const gameArea = document.createElement('div');
            gameArea.className = 'game-area';
            gameArea.innerHTML = `
                <h3 style="text-align: center; color: #2d3436; margin-bottom: 20px;">🎮 长度特性体验游戏</h3>
                <p style="text-align: center; color: #636e72; margin-bottom: 20px;">体验数组固定长度 vs 集合可变长度的区别！</p>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div style="text-align: center;">
                        <h4 style="color: #fd79a8; margin-bottom: 15px;">📊 数组 (固定长度)</h4>
                        <div id="arrayContainer" style="border: 3px solid #fd79a8; border-radius: 15px; padding: 20px; min-height: 100px; background: rgba(253, 121, 168, 0.1);">
                            <div style="display: flex; gap: 10px; justify-content: center;">
                                <div class="data-item basic-type">1</div>
                                <div class="data-item basic-type">2</div>
                                <div class="data-item basic-type">3</div>
                                <div class="data-item basic-type">4</div>
                                <div class="data-item basic-type">5</div>
                            </div>
                        </div>
                        <p style="margin-top: 10px; color: #fd79a8; font-weight: bold;">长度: 5 (不可变)</p>
                        <button class="interactive-btn" onclick="tryAddToArray()" style="margin: 10px; padding: 10px 20px; font-size: 14px;">尝试添加元素</button>
                    </div>

                    <div style="text-align: center;">
                        <h4 style="color: #74b9ff; margin-bottom: 15px;">📦 集合 (可变长度)</h4>
                        <div id="collectionContainer" style="border: 3px solid #74b9ff; border-radius: 15px; padding: 20px; min-height: 100px; background: rgba(116, 185, 255, 0.1); display: flex; flex-wrap: wrap; justify-content: center; gap: 10px;">
                            <div class="data-item reference-type">📚</div>
                        </div>
                        <p id="collectionSize" style="margin-top: 10px; color: #74b9ff; font-weight: bold;">长度: 1 (可变)</p>
                        <button class="interactive-btn" onclick="addToCollection()" style="margin: 5px; padding: 10px 20px; font-size: 14px;">添加元素</button>
                        <button class="interactive-btn" onclick="removeFromCollection()" style="margin: 5px; padding: 10px 20px; font-size: 14px;">删除元素</button>
                    </div>
                </div>
            `;

            const currentSection = document.querySelector('.section:nth-child(3)');
            currentSection.appendChild(gameArea);

            let collectionCount = 1;
            const collectionItems = ['📚', '🚗', '👨‍🎓', '📱', '🍎', '🏠', '⚽', '🎵'];

            window.tryAddToArray = function() {
                alert('❌ 数组长度固定，无法添加新元素！\n这就是数组的特点：一旦创建，长度就不能改变。');
            };

            window.addToCollection = function() {
                if (collectionCount < collectionItems.length) {
                    const newItem = document.createElement('div');
                    newItem.className = 'data-item reference-type';
                    newItem.textContent = collectionItems[collectionCount];
                    newItem.style.animation = 'bounce 0.5s ease';
                    document.getElementById('collectionContainer').appendChild(newItem);
                    collectionCount++;
                    document.getElementById('collectionSize').textContent = `长度: ${collectionCount} (可变)`;
                } else {
                    alert('🎉 集合已经包含所有演示元素！');
                }
            };

            window.removeFromCollection = function() {
                const container = document.getElementById('collectionContainer');
                if (collectionCount > 1) {
                    container.removeChild(container.lastChild);
                    collectionCount--;
                    document.getElementById('collectionSize').textContent = `长度: ${collectionCount} (可变)`;
                } else {
                    alert('⚠️ 集合至少要保留一个元素！');
                }
            };
        }

        // 一致性动画
        function animateConsistency() {
            const canvas = document.getElementById('consistencyCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#ffeaa7');
                gradient.addColorStop(1, '#fab1a0');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2d3436';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🌈 元素类型一致性对比', canvas.width / 2, 50);

                // 数组部分（同一类型）
                if (frame > 30) {
                    const progress1 = Math.min(1, (frame - 30) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress1;

                    ctx.fillStyle = 'rgba(253, 121, 168, 0.9)';
                    ctx.fillRect(50, 120, 300, 200);
                    ctx.strokeStyle = '#2d3436';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(50, 120, 300, 200);

                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 20px Arial';
                    ctx.fillText('📊 数组 (同一类型)', 200, 110);

                    // 相同类型的元素
                    for (let i = 0; i < 5; i++) {
                        ctx.fillStyle = '#fd79a8';
                        ctx.fillRect(70 + i * 50, 150, 40, 40);
                        ctx.fillStyle = 'white';
                        ctx.font = '16px Arial';
                        ctx.fillText('int', 90 + i * 50, 175);
                    }

                    ctx.fillStyle = '#2d3436';
                    ctx.font = '16px Arial';
                    ctx.fillText('所有元素都是 int 类型', 200, 220);
                    ctx.fillText('✅ 类型统一', 200, 250);

                    // 统一性指示线
                    ctx.strokeStyle = '#00b894';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(70, 200);
                    ctx.lineTo(310, 200);
                    ctx.stroke();

                    ctx.restore();
                }

                // 集合部分（不同类型）
                if (frame > 90) {
                    const progress2 = Math.min(1, (frame - 90) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress2;

                    ctx.fillStyle = 'rgba(116, 185, 255, 0.9)';
                    ctx.fillRect(450, 120, 300, 200);
                    ctx.strokeStyle = '#2d3436';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(450, 120, 300, 200);

                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 20px Arial';
                    ctx.fillText('📦 集合 (不同类型)', 600, 110);

                    // 不同类型的元素
                    const types = ['📚', '🚗', '👨‍🎓', '📱', '🍎'];
                    const colors = ['#e17055', '#00b894', '#6c5ce7', '#fdcb6e', '#fd79a8'];

                    for (let i = 0; i < 5; i++) {
                        ctx.fillStyle = colors[i];
                        ctx.fillRect(470 + i * 50, 150, 40, 40);
                        ctx.font = '20px Arial';
                        ctx.fillText(types[i], 490 + i * 50, 175);
                    }

                    ctx.fillStyle = '#2d3436';
                    ctx.font = '16px Arial';
                    ctx.fillText('不同类型的对象', 600, 220);
                    ctx.fillText('✅ 类型多样', 600, 250);

                    // 多样性指示线（彩虹色）
                    for (let i = 0; i < 5; i++) {
                        ctx.strokeStyle = colors[i];
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(470 + i * 50, 200);
                        ctx.lineTo(510 + i * 50, 200);
                        ctx.stroke();
                    }

                    ctx.restore();
                }

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 数据结构动画
        function animateStructure() {
            const canvas = document.getElementById('structureCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            const structures = [
                { name: 'ArrayList', desc: '动态数组', color: '#fd79a8', x: 150, y: 200 },
                { name: 'LinkedList', desc: '链表结构', color: '#74b9ff', x: 350, y: 200 },
                { name: 'HashSet', desc: '哈希表', color: '#00b894', x: 550, y: 200 },
                { name: 'TreeSet', desc: '红黑树', color: '#6c5ce7', x: 750, y: 200 }
            ];

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#a29bfe');
                gradient.addColorStop(1, '#6c5ce7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🏗️ 不同的数据结构', canvas.width / 2, 50);

                ctx.font = '18px Arial';
                ctx.fillText('数据结构：就是容器中存储数据的方式', canvas.width / 2, 80);

                // 绘制各种数据结构
                structures.forEach((struct, index) => {
                    const delay = index * 40;
                    const progress = Math.max(0, Math.min(1, (frame - delay) / 80));

                    if (progress > 0) {
                        ctx.save();
                        ctx.translate(struct.x, struct.y);
                        ctx.scale(progress, progress);

                        // 结构容器
                        ctx.fillStyle = struct.color;
                        ctx.fillRect(-60, -50, 120, 100);
                        ctx.strokeStyle = 'white';
                        ctx.lineWidth = 3;
                        ctx.strokeRect(-60, -50, 120, 100);

                        // 结构名称
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(struct.name, 0, -20);

                        // 结构描述
                        ctx.font = '12px Arial';
                        ctx.fillText(struct.desc, 0, 0);

                        // 数据元素可视化
                        if (struct.name === 'ArrayList') {
                            // 数组形式
                            for (let i = 0; i < 3; i++) {
                                ctx.fillStyle = 'white';
                                ctx.fillRect(-40 + i * 25, 15, 20, 20);
                                ctx.strokeStyle = struct.color;
                                ctx.lineWidth = 1;
                                ctx.strokeRect(-40 + i * 25, 15, 20, 20);
                            }
                        } else if (struct.name === 'LinkedList') {
                            // 链表形式
                            for (let i = 0; i < 3; i++) {
                                ctx.fillStyle = 'white';
                                ctx.beginPath();
                                ctx.arc(-30 + i * 30, 25, 8, 0, Math.PI * 2);
                                ctx.fill();
                                if (i < 2) {
                                    ctx.strokeStyle = 'white';
                                    ctx.lineWidth = 2;
                                    ctx.beginPath();
                                    ctx.moveTo(-22 + i * 30, 25);
                                    ctx.lineTo(-8 + i * 30, 25);
                                    ctx.stroke();
                                }
                            }
                        } else if (struct.name === 'HashSet') {
                            // 散列形式
                            const positions = [[-20, 15], [0, 25], [20, 10]];
                            positions.forEach(pos => {
                                ctx.fillStyle = 'white';
                                ctx.beginPath();
                                ctx.arc(pos[0], pos[1], 6, 0, Math.PI * 2);
                                ctx.fill();
                            });
                        } else if (struct.name === 'TreeSet') {
                            // 树形结构
                            ctx.strokeStyle = 'white';
                            ctx.lineWidth = 2;
                            ctx.beginPath();
                            ctx.moveTo(0, 15);
                            ctx.lineTo(-20, 35);
                            ctx.moveTo(0, 15);
                            ctx.lineTo(20, 35);
                            ctx.stroke();

                            ctx.fillStyle = 'white';
                            ctx.beginPath();
                            ctx.arc(0, 15, 5, 0, Math.PI * 2);
                            ctx.fill();
                            ctx.beginPath();
                            ctx.arc(-20, 35, 5, 0, Math.PI * 2);
                            ctx.fill();
                            ctx.beginPath();
                            ctx.arc(20, 35, 5, 0, Math.PI * 2);
                            ctx.fill();
                        }

                        ctx.restore();
                    }
                });

                // 说明文字
                if (frame > 160) {
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('每个容器的特点不同，原理在于内部数据结构不同', canvas.width / 2, 350);
                }

                frame++;
                if (frame < 250) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 数据类型分类游戏
        function playDataTypeGame() {
            const gameArea = document.createElement('div');
            gameArea.className = 'game-area';
            gameArea.innerHTML = `
                <h3 style="text-align: center; color: #2d3436; margin-bottom: 20px;">🎮 数据类型分类游戏</h3>
                <p style="text-align: center; color: #636e72; margin-bottom: 20px;">将数据类型拖拽到正确的容器中！</p>

                <div style="margin-bottom: 20px; text-align: center;">
                    <h4>待分类的数据类型：</h4>
                    <div id="dataTypePool">
                        <div class="data-item basic-type" draggable="true" data-type="basic">int: 42</div>
                        <div class="data-item reference-type" draggable="true" data-type="reference">String: "Hello"</div>
                        <div class="data-item basic-type" draggable="true" data-type="basic">double: 3.14</div>
                        <div class="data-item reference-type" draggable="true" data-type="reference">Student: 👨‍🎓</div>
                        <div class="data-item basic-type" draggable="true" data-type="basic">boolean: true</div>
                        <div class="data-item reference-type" draggable="true" data-type="reference">Car: 🚗</div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <div style="text-align: center;">
                        <h4 style="color: #fd79a8;">📊 数组容器</h4>
                        <div id="arrayTypeContainer" style="border: 3px dashed #fd79a8; border-radius: 15px; padding: 20px; min-height: 120px; background: rgba(253, 121, 168, 0.1);">
                            <p style="color: #fd79a8;">可以存储基本类型和引用类型</p>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <h4 style="color: #74b9ff;">📦 集合容器</h4>
                        <div id="collectionTypeContainer" style="border: 3px dashed #74b9ff; border-radius: 15px; padding: 20px; min-height: 120px; background: rgba(116, 185, 255, 0.1);">
                            <p style="color: #74b9ff;">只能存储引用类型</p>
                        </div>
                    </div>
                </div>

                <p id="typeGameScore" style="text-align: center; margin-top: 15px; font-weight: bold; color: #00b894;">正确分类: 0/6</p>
            `;

            const currentSection = document.querySelector('.section:nth-child(4)');
            currentSection.appendChild(gameArea);

            let correctCount = 0;

            // 添加拖拽功能
            document.querySelectorAll('#dataTypePool .data-item').forEach(item => {
                item.addEventListener('dragstart', (e) => {
                    e.dataTransfer.setData('text/plain', JSON.stringify({
                        type: e.target.dataset.type,
                        content: e.target.textContent,
                        className: e.target.className
                    }));
                    e.target.style.opacity = '0.5';
                });

                item.addEventListener('dragend', (e) => {
                    e.target.style.opacity = '1';
                });
            });

            // 数组容器拖拽
            const arrayContainer = document.getElementById('arrayTypeContainer');
            arrayContainer.addEventListener('dragover', (e) => {
                e.preventDefault();
                arrayContainer.style.background = 'rgba(253, 121, 168, 0.3)';
            });

            arrayContainer.addEventListener('dragleave', () => {
                arrayContainer.style.background = 'rgba(253, 121, 168, 0.1)';
            });

            arrayContainer.addEventListener('drop', (e) => {
                e.preventDefault();
                const data = JSON.parse(e.dataTransfer.getData('text/plain'));

                // 数组可以接受任何类型
                const droppedItem = document.createElement('div');
                droppedItem.className = data.className;
                droppedItem.textContent = data.content;
                droppedItem.style.animation = 'bounce 0.5s ease';
                arrayContainer.appendChild(droppedItem);

                correctCount++;
                document.getElementById('typeGameScore').textContent = `正确分类: ${correctCount}/6`;
                arrayContainer.style.background = 'rgba(253, 121, 168, 0.1)';

                // 移除原元素
                const originalItem = Array.from(document.querySelectorAll('#dataTypePool .data-item')).find(item =>
                    item.textContent === data.content
                );
                if (originalItem) originalItem.remove();

                if (correctCount === 6) {
                    setTimeout(() => {
                        alert('🎉 恭喜！你已经理解了数组和集合的数据类型支持差异！');
                        updateProgress(10);
                    }, 500);
                }
            });

            // 集合容器拖拽
            const collectionContainer = document.getElementById('collectionTypeContainer');
            collectionContainer.addEventListener('dragover', (e) => {
                e.preventDefault();
                collectionContainer.style.background = 'rgba(116, 185, 255, 0.3)';
            });

            collectionContainer.addEventListener('dragleave', () => {
                collectionContainer.style.background = 'rgba(116, 185, 255, 0.1)';
            });

            collectionContainer.addEventListener('drop', (e) => {
                e.preventDefault();
                const data = JSON.parse(e.dataTransfer.getData('text/plain'));

                if (data.type === 'reference') {
                    // 集合只接受引用类型
                    const droppedItem = document.createElement('div');
                    droppedItem.className = data.className;
                    droppedItem.textContent = data.content;
                    droppedItem.style.animation = 'bounce 0.5s ease';
                    collectionContainer.appendChild(droppedItem);

                    correctCount++;
                    document.getElementById('typeGameScore').textContent = `正确分类: ${correctCount}/6`;

                    // 移除原元素
                    const originalItem = Array.from(document.querySelectorAll('#dataTypePool .data-item')).find(item =>
                        item.textContent === data.content
                    );
                    if (originalItem) originalItem.remove();
                } else {
                    // 基本类型不能放入集合
                    alert('❌ 集合不能存储基本数据类型！\n只能存储引用数据类型（对象）。');
                }

                collectionContainer.style.background = 'rgba(116, 185, 255, 0.1)';

                if (correctCount === 6) {
                    setTimeout(() => {
                        alert('🎉 恭喜！你已经理解了数组和集合的数据类型支持差异！');
                        updateProgress(10);
                    }, 500);
                }
            });
        }

        // 集合体系架构动画
        function animateHierarchy() {
            const canvas = document.getElementById('hierarchyCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🌳 集合体系架构', canvas.width / 2, 50);

                // 顶层接口
                if (frame > 30) {
                    const progress1 = Math.min(1, (frame - 30) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress1;

                    ctx.fillStyle = '#fd79a8';
                    ctx.fillRect(350, 100, 200, 60);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(350, 100, 200, 60);

                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 18px Arial';
                    ctx.fillText('Collection 接口', 450, 135);
                    ctx.font = '12px Arial';
                    ctx.fillText('(顶层抽象)', 450, 150);

                    ctx.restore();
                }

                // 中层接口
                if (frame > 90) {
                    const progress2 = Math.min(1, (frame - 90) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress2;

                    // List接口
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(150, 220, 150, 50);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(150, 220, 150, 50);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText('List 接口', 225, 250);

                    // Set接口
                    ctx.fillStyle = '#00b894';
                    ctx.fillRect(600, 220, 150, 50);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(600, 220, 150, 50);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText('Set 接口', 675, 250);

                    // 连接线
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(450, 160);
                    ctx.lineTo(225, 220);
                    ctx.moveTo(450, 160);
                    ctx.lineTo(675, 220);
                    ctx.stroke();

                    ctx.restore();
                }

                // 底层实现类
                if (frame > 150) {
                    const progress3 = Math.min(1, (frame - 150) / 60);
                    ctx.save();
                    ctx.globalAlpha = progress3;

                    // ArrayList
                    ctx.fillStyle = '#fdcb6e';
                    ctx.fillRect(50, 340, 120, 40);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(50, 340, 120, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.fillText('ArrayList', 110, 365);

                    // LinkedList
                    ctx.fillStyle = '#e17055';
                    ctx.fillRect(230, 340, 120, 40);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(230, 340, 120, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.fillText('LinkedList', 290, 365);

                    // HashSet
                    ctx.fillStyle = '#a29bfe';
                    ctx.fillRect(530, 340, 120, 40);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(530, 340, 120, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.fillText('HashSet', 590, 365);

                    // TreeSet
                    ctx.fillStyle = '#6c5ce7';
                    ctx.fillRect(710, 340, 120, 40);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(710, 340, 120, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.fillText('TreeSet', 770, 365);

                    // 连接线
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(225, 270);
                    ctx.lineTo(110, 340);
                    ctx.moveTo(225, 270);
                    ctx.lineTo(290, 340);
                    ctx.moveTo(675, 270);
                    ctx.lineTo(590, 340);
                    ctx.moveTo(675, 270);
                    ctx.lineTo(770, 340);
                    ctx.stroke();

                    ctx.restore();
                }

                // 使用原则
                if (frame > 210) {
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('使用原则：参阅顶层内容，建立底层对象', canvas.width / 2, 430);

                    // 箭头指示
                    ctx.strokeStyle = '#ffeaa7';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(450, 160);
                    ctx.lineTo(450, 100);
                    ctx.stroke();

                    ctx.fillStyle = '#ffeaa7';
                    ctx.font = '14px Arial';
                    ctx.fillText('参阅', 470, 130);

                    ctx.strokeStyle = '#ffeaa7';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(450, 270);
                    ctx.lineTo(450, 340);
                    ctx.stroke();

                    ctx.fillStyle = '#ffeaa7';
                    ctx.font = '14px Arial';
                    ctx.fillText('建立', 470, 310);
                }

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 总结动画
        function animateSummary() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createRadialGradient(400, 175, 0, 400, 175, 300);
                gradient.addColorStop(0, '#fd79a8');
                gradient.addColorStop(0.5, '#74b9ff');
                gradient.addColorStop(1, '#6c5ce7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;

                // 庆祝烟花效果
                for (let i = 0; i < 20; i++) {
                    const angle = (frame * 0.03 + i * 0.3) % (Math.PI * 2);
                    const radius = 60 + Math.sin(frame * 0.02 + i) * 30;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;

                    ctx.fillStyle = `hsl(${(frame * 2 + i * 20) % 360}, 80%, 70%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, 3 + Math.sin(frame * 0.1 + i) * 2, 0, Math.PI * 2);
                    ctx.fill();

                    // 闪烁效果
                    if (Math.sin(frame * 0.2 + i) > 0.7) {
                        ctx.fillStyle = 'white';
                        ctx.beginPath();
                        ctx.arc(x, y, 1, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }

                // 中心成就徽章
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.rotate(frame * 0.01);

                // 徽章外圈
                ctx.fillStyle = 'gold';
                ctx.beginPath();
                ctx.arc(0, 0, 40, 0, Math.PI * 2);
                ctx.fill();

                // 徽章内圈
                ctx.fillStyle = '#fff';
                ctx.beginPath();
                ctx.arc(0, 0, 30, 0, Math.PI * 2);
                ctx.fill();

                // 徽章图标
                ctx.fillStyle = '#fd79a8';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎓', 0, 8);

                ctx.restore();

                // 成就文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 学习完成！', centerX, centerY - 80);

                ctx.font = '16px Arial';
                ctx.fillText('你已经掌握了集合与数组的核心区别', centerX, centerY + 80);

                // 知识点总结
                if (frame > 60) {
                    const summaryPoints = [
                        '📏 长度：数组固定 vs 集合可变',
                        '🎭 类型：数组全能 vs 集合专业',
                        '🌈 一致性：数组统一 vs 集合多样',
                        '🏗️ 结构：不同结构决定特性'
                    ];

                    ctx.fillStyle = 'rgba(255,255,255,0.9)';
                    ctx.fillRect(centerX - 200, centerY + 100, 400, 120);
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(centerX - 200, centerY + 100, 400, 120);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = '12px Arial';
                    summaryPoints.forEach((point, i) => {
                        ctx.fillText(point, centerX, centerY + 125 + i * 20);
                    });
                }

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(100);
                }
            }

            draw();
        }

        // 播放全部动画
        async function playAllAnimations() {
            const animations = [
                animateOverview,
                animateLength,
                animateDataType,
                animateConsistency,
                animateStructure,
                animateHierarchy,
                animateSummary
            ];

            for (let i = 0; i < animations.length; i++) {
                animations[i]();
                await new Promise(resolve => setTimeout(resolve, 4000));
            }
        }

        // 页面加载完成后自动播放概览动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                animateOverview();
            }, 1000);
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case '1':
                    animateOverview();
                    break;
                case '2':
                    animateLength();
                    break;
                case '3':
                    animateDataType();
                    break;
                case '4':
                    animateConsistency();
                    break;
                case '5':
                    animateStructure();
                    break;
                case '6':
                    animateHierarchy();
                    break;
                case '7':
                    animateSummary();
                    break;
                case ' ':
                    e.preventDefault();
                    playAllAnimations();
                    break;
            }
        });

        // 添加提示信息
        const hint = document.createElement('div');
        hint.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 1000;
            max-width: 220px;
        `;
        hint.innerHTML = '💡 提示：按数字键1-7播放对应动画<br>空格键播放全部动画<br>参与互动游戏加深理解！';
        document.body.appendChild(hint);

        // 4秒后隐藏提示
        setTimeout(() => {
            hint.style.opacity = '0';
            hint.style.transition = 'opacity 1s';
        }, 5000);

        // 鼠标悬停效果
        document.querySelectorAll('.section').forEach(section => {
            section.addEventListener('mouseenter', () => {
                section.style.transform = 'translateY(-10px)';
                section.style.boxShadow = '0 35px 70px rgba(0,0,0,0.2)';
            });

            section.addEventListener('mouseleave', () => {
                section.style.transform = 'translateY(0)';
                section.style.boxShadow = '0 30px 60px rgba(0,0,0,0.15)';
            });
        });
    </script>
</body>
</html>
