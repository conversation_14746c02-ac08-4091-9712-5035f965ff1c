<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统分析课程 - 什么是错误的？</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
        }

        h1 {
            color: #0056b3;
            text-align: center;
            margin-bottom: 25px;
            font-size: 1.8em;
            border-bottom: 2px solid #eee;
            padding-bottom: 15px;
        }

        h2 {
            color: #007bff;
            font-size: 1.4em;
            margin-top: 20px;
            margin-bottom: 15px;
        }

        .question-section, .options-section, .explanation-section, .animation-section {
            margin-bottom: 25px;
        }

        .question-text {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 20px;
            color: #555;
        }

        .option-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
            cursor: pointer;
            transition: background-color 0.3s, border-color 0.3s;
            position: relative;
        }

        .option-item:hover {
            background-color: #eef7ff;
            border-color: #a7d9ff;
        }

        .option-item.selected {
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }

        .option-item.correct {
            background-color: #d4edda;
            border-color: #28a745;
            animation: pulse-green 1s infinite alternate;
        }

        .option-item.incorrect {
            background-color: #f8d7da;
            border-color: #dc3545;
            animation: shake 0.5s;
        }

        .option-prefix {
            font-weight: bold;
            margin-right: 10px;
            color: #007bff;
            font-size: 1.1em;
            min-width: 25px;
        }

        .feedback-message {
            margin-top: 15px;
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            display: none; /* Hidden by default */
        }

        .feedback-message.correct {
            background-color: #28a745;
            color: white;
        }

        .feedback-message.incorrect {
            background-color: #dc3545;
            color: white;
        }

        .explanation-text {
            background-color: #e9f7ef;
            border-left: 5px solid #28a745;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 1.1em;
        }

        .explanation-text p {
            margin-bottom: 10px;
        }

        .highlight {
            color: #0056b3;
            font-weight: bold;
        }

        canvas {
            display: block;
            border: 1px solid #ccc;
            background-color: #fff;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        }

        .animation-controls {
            text-align: center;
            margin-top: 20px;
        }

        .animation-controls button {
            padding: 10px 20px;
            font-size: 1em;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin: 0 10px;
        }

        .animation-controls button:hover {
            background-color: #0056b3;
        }

        @keyframes shake {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
            100% { transform: translateX(0); }
        }

        @keyframes pulse-green {
            0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4); }
            100% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>系统分析课程：在对现有系统进行分析时，( ) 方法是错误的。</h1>

        <div class="question-section">
            <div class="question-text">
                <p>在对现有系统进行分析时，以下哪种方法是**错误**的？</p>
            </div>
        </div>

        <div class="options-section" id="options-container">
            <div class="option-item" data-option="A">
                <span class="option-prefix">A</span>
                <span>多与用户沟通，了解他们对现有系统的认识和评价</span>
            </div>
            <div class="option-item" data-option="B">
                <span class="option-prefix">B</span>
                <span>了解现有系统的组织结构，输入/输出、资源利用情况和数据处理过程</span>
            </div>
            <div class="option-item" data-option="C">
                <span class="option-prefix">C</span>
                <span>理解现有系统"做什么"的基础上，抽取其"怎么做"的本质</span>
            </div>
            <div class="option-item" data-option="D">
                <span class="option-prefix">D</span>
                <span>从对现有系统的物理模型出发，通过研究、分析建立起其较高层次的逻辑模型描述</span>
            </div>
        </div>

        <div class="feedback-message" id="feedback"></div>

        <div class="explanation-section" style="display: none;">
            <h2>题目知识解释</h2>
            <div class="explanation-text">
                <p>在系统分析过程中，核心任务是解决"<span class="highlight">做什么</span>"的问题，即明确系统应该达成的目标和功能（<span class="highlight">分析问题域</span>）。</p>
                <p>我们不应该过早地关注"<span class="highlight">怎么做</span>"，即具体的实现细节和解决方案。</p>
                <p>让我们逐一分析选项：</p>
                <ul>
                    <li>**A选项：多与用户沟通，了解他们对现有系统的认识和评价。** 这是非常正确且关键的。用户是系统的实际使用者，他们的反馈能帮助我们了解系统当前的痛点和需求，属于"做什么"的范畴。</li>
                    <li>**B选项：了解现有系统的组织结构，输入/输出、资源利用情况和数据处理过程。** 这也是正确的方法。理解现有系统的运作方式和限制，是分析新系统需求的基础，属于对"做什么"的深入了解。</li>
                    <li>**C选项：在理解现有系统"做什么"的基础上，抽取其"怎么做"的本质。** 这个选项是**错误**的。系统分析阶段的目的是搞清楚"做什么"，而不是去深入探讨"怎么做"的本质。过早地关注实现细节（"怎么做"）会导致分析师陷入具体技术方案的泥潭，而忽略了真正的业务需求。这违背了系统分析的原则。</li>
                    <li>**D选项：从对现有系统的物理模型出发，通过研究、分析建立起其较高层次的逻辑模型描述。** 这是正确的方法。物理模型是系统实际的实现形式（包含"怎么做"的细节），通过对其分析，抽象出更高层次、更关注功能和业务的逻辑模型（接近"做什么"），是系统分析的重要步骤。</li>
                </ul>
                <p>因此，在系统分析阶段，我们的焦点是"做什么"，而不是"怎么做"。</p>
            </div>
        </div>

        <div class="animation-section" style="display: none;">
            <h2>概念动画演示</h2>
            <canvas id="analysisCanvas" width="800" height="400"></canvas>
            <div class="animation-controls">
                <button id="startAnimation">开始演示</button>
                <button id="resetAnimation">重置</button>
            </div>
        </div>
    </div>

    <script>
        const optionsContainer = document.getElementById('options-container');
        const feedbackMessage = document.getElementById('feedback');
        const explanationSection = document.querySelector('.explanation-section');
        const animationSection = document.querySelector('.animation-section');
        const canvas = document.getElementById('analysisCanvas');
        const ctx = canvas.getContext('2d');
        const startAnimationBtn = document.getElementById('startAnimation');
        const resetAnimationBtn = document.getElementById('resetAnimation');

        let selectedOption = null;
        let animationFrameId;
        let animationProgress = 0;
        let animationState = 'initial'; // 'initial', 'focus_what', 'avoid_how', 'conclusion'

        const correctOption = 'C';

        // Event Listeners for options
        optionsContainer.addEventListener('click', (event) => {
            const optionItem = event.target.closest('.option-item');
            if (!optionItem || selectedOption) return; // Only allow one selection

            selectedOption = optionItem.dataset.option;

            // Remove previous selections
            document.querySelectorAll('.option-item').forEach(item => {
                item.classList.remove('selected', 'correct', 'incorrect');
            });

            optionItem.classList.add('selected');

            if (selectedOption === correctOption) {
                feedbackMessage.textContent = '恭喜你，选择正确！';
                feedbackMessage.classList.remove('incorrect');
                feedbackMessage.classList.add('correct');
                optionItem.classList.add('correct');
            } else {
                feedbackMessage.textContent = '很遗憾，选择错误。正确答案是 C。';
                feedbackMessage.classList.remove('correct');
                feedbackMessage.classList.add('incorrect');
                optionItem.classList.add('incorrect');
                // Highlight correct answer after user makes a wrong choice
                document.querySelector(`.option-item[data-option="${correctOption}"]`).classList.add('correct');
            }
            feedbackMessage.style.display = 'block';

            // Show explanation and animation sections
            explanationSection.style.display = 'block';
            animationSection.style.display = 'block';
            drawInitialCanvas(); // Draw initial state of canvas
        });

        // Canvas Drawing Functions
        function drawInitialCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear canvas

            // Draw "What" box
            ctx.fillStyle = '#ADD8E6'; // Light blue
            ctx.fillRect(100, 100, 200, 150);
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.strokeRect(100, 100, 200, 150);
            ctx.fillStyle = '#333';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('做什么 (问题域)', 200, 175);
            ctx.font = '16px Arial';
            ctx.fillText('目标 & 功能', 200, 205);


            // Draw "How" box
            ctx.fillStyle = '#FFDAB9'; // Peach
            ctx.fillRect(500, 100, 200, 150);
            ctx.strokeStyle = '#FFA500';
            ctx.lineWidth = 2;
            ctx.strokeRect(500, 100, 200, 150);
            ctx.fillStyle = '#333';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('怎么做 (解决方案)', 600, 175);
            ctx.font = '16px Arial';
            ctx.fillText('技术实现 & 细节', 600, 205);


            // Draw initial arrow (no direction yet)
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(350, 175);
            ctx.lineTo(450, 175);
            ctx.stroke();
            ctx.fillStyle = '#666';
            ctx.font = '18px Arial';
            ctx.fillText('系统分析', 400, 280);
        }

        function animateCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear canvas for redraw

            // Redraw static elements
            drawInitialCanvas();

            if (animationState === 'focus_what') {
                const arrowLength = 100 + animationProgress * 50; // Grow arrow
                const arrowX = 300;
                const arrowY = 175;

                ctx.strokeStyle = '#28a745'; // Green for focus
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(arrowX, arrowY);
                ctx.lineTo(arrowX + arrowLength, arrowY);
                ctx.stroke();

                // Arrowhead
                ctx.beginPath();
                ctx.lineTo(arrowX + arrowLength, arrowY);
                ctx.lineTo(arrowX + arrowLength - 10, arrowY - 5);
                ctx.lineTo(arrowX + arrowLength - 10, arrowY + 5);
                ctx.closePath();
                ctx.fillStyle = '#28a745';
                ctx.fill();

                // Highlight "What" box
                ctx.strokeStyle = '#28a745';
                ctx.lineWidth = 5;
                ctx.strokeRect(100, 100, 200, 150);

                ctx.fillStyle = '#0056b3';
                ctx.font = '20px Arial';
                ctx.fillText('聚焦', 200, 80 - animationProgress * 5); // Text above "What"
                ctx.fillText('主要分析点', 200, 265 + animationProgress * 5);
            } else if (animationState === 'avoid_how') {
                // Focus "What"
                ctx.strokeStyle = '#28a745';
                ctx.lineWidth = 5;
                ctx.strokeRect(100, 100, 200, 150);
                ctx.fillStyle = '#0056b3';
                ctx.font = '20px Arial';
                ctx.fillText('聚焦', 200, 75);
                ctx.fillText('主要分析点', 200, 270);

                // Draw "X" over "How"
                ctx.strokeStyle = '#dc3545'; // Red for incorrect/avoid
                ctx.lineWidth = 8;
                const howX = 500;
                const howY = 100;
                const howWidth = 200;
                const howHeight = 150;

                ctx.beginPath();
                ctx.moveTo(howX, howY);
                ctx.lineTo(howX + howWidth * animationProgress, howY + howHeight * animationProgress);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(howX + howWidth, howY);
                ctx.lineTo(howX + howWidth - howWidth * animationProgress, howY + howHeight * animationProgress);
                ctx.stroke();

                ctx.fillStyle = '#dc3545';
                ctx.font = '20px Arial';
                ctx.fillText('避免', 600, 80 - animationProgress * 5); // Text above "How"
                ctx.fillText('分析陷阱', 600, 265 + animationProgress * 5);

            } else if (animationState === 'conclusion') {
                 // Final state: "What" highlighted, "How" crossed out
                ctx.strokeStyle = '#28a745';
                ctx.lineWidth = 5;
                ctx.strokeRect(100, 100, 200, 150);
                ctx.fillStyle = '#0056b3';
                ctx.font = '20px Arial';
                ctx.fillText('聚焦', 200, 75);
                ctx.fillText('主要分析点', 200, 270);

                ctx.strokeStyle = '#dc3545';
                ctx.lineWidth = 8;
                ctx.beginPath();
                ctx.moveTo(500, 100);
                ctx.lineTo(700, 250);
                ctx.stroke();
                ctx.beginPath();
                ctx.moveTo(700, 100);
                ctx.lineTo(500, 250);
                ctx.stroke();
                ctx.fillStyle = '#dc3545';
                ctx.font = '20px Arial';
                ctx.fillText('避免', 600, 75);
                ctx.fillText('分析陷阱', 600, 270);

                ctx.fillStyle = '#0056b3';
                ctx.font = '28px Arial';
                ctx.fillText('系统分析 = 聚焦"做什么"', canvas.width / 2, 350);
            }
        }

        function animate() {
            if (animationState === 'focus_what') {
                animationProgress += 0.02;
                if (animationProgress >= 1) {
                    animationProgress = 0;
                    animationState = 'avoid_how';
                    setTimeout(nextStep, 1000); // Pause before next step
                    return;
                }
            } else if (animationState === 'avoid_how') {
                animationProgress += 0.02;
                if (animationProgress >= 1) {
                    animationProgress = 1; // Ensure it reaches full cross
                    animationState = 'conclusion';
                    setTimeout(nextStep, 1000); // Pause before conclusion
                    return;
                }
            } else if (animationState === 'conclusion') {
                // Stay in conclusion state
            }

            animateCanvas();
            if (animationState !== 'conclusion') {
                animationFrameId = requestAnimationFrame(animate);
            }
        }

        function nextStep() {
            if (animationState === 'initial') {
                animationState = 'focus_what';
                startAnimationBtn.textContent = '下一步 (聚焦做什么)';
                animationFrameId = requestAnimationFrame(animate);
            } else if (animationState === 'focus_what') {
                animationState = 'avoid_how';
                startAnimationBtn.textContent = '下一步 (避免怎么做)';
                animationFrameId = requestAnimationFrame(animate);
            } else if (animationState === 'avoid_how') {
                animationState = 'conclusion';
                startAnimationBtn.textContent = '演示结束';
                startAnimationBtn.disabled = true; // Disable button after conclusion
                animateCanvas(); // Draw final state
            }
        }

        startAnimationBtn.addEventListener('click', () => {
            if (animationState === 'conclusion') { // If already concluded, reset first
                resetAnimation();
            }
            nextStep();
        });

        resetAnimationBtn.addEventListener('click', resetAnimation);

        function resetAnimation() {
            cancelAnimationFrame(animationFrameId);
            animationProgress = 0;
            animationState = 'initial';
            startAnimationBtn.textContent = '开始演示';
            startAnimationBtn.disabled = false;
            drawInitialCanvas();
        }

        // Initial draw on load
        drawInitialCanvas();
    </script>
</body>
</html> 