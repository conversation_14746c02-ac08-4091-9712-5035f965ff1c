<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Waiter与Builder的关系</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f4f7f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .demo-area {
            display: flex;
            flex-direction: column;
            margin: 30px 0;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
        }
        .demo-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .demo-box {
            width: 48%;
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .code-block {
            background: #f8f9fa;
            border-left: 5px solid #2ecc71;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre;
            overflow-x: auto;
            font-size: 14px;
        }
        .explanation {
            background: #e9f7fd;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .highlight {
            background-color: #fffacd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .btn-group {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .animation-area {
            height: 200px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
            background-color: #f9f9f9;
        }
        .actor {
            position: absolute;
            transition: all 0.5s ease;
        }
        #waiter {
            left: 50px;
            top: 50px;
            font-size: 40px;
        }
        #builder {
            right: 50px;
            top: 50px;
            font-size: 40px;
        }
        .speech-bubble {
            position: absolute;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 10px;
            max-width: 150px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        #waiter-speech {
            left: 100px;
            top: 20px;
        }
        #builder-speech {
            right: 100px;
            top: 20px;
        }
        .pizza {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 60px;
            opacity: 0;
            transition: opacity 0.5s;
        }
        .step-indicator {
            text-align: center;
            font-weight: bold;
            margin-top: 10px;
            color: #2c3e50;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>Waiter构造Builder接口对象的详细解释</h1>
        
        <div class="explanation">
            <p>在生成器模式中，<strong>Waiter（指导者）</strong>不是直接创建复杂对象，而是<strong>指挥</strong>一个<strong>Builder（生成器）</strong>来一步步构建对象。</p>
            <p>关键点是：<span class="highlight">Waiter持有一个Builder接口的引用，而不关心具体是哪种Builder的实现</span>。这样，同一个Waiter可以使用不同的Builder来创建不同风格的对象。</p>
        </div>

        <h2>核心概念图解</h2>
        
        <div class="demo-area">
            <div class="demo-row">
                <div class="demo-box">
                    <h3>Waiter与Builder的关系</h3>
                    <pre>
Waiter(服务员)
    │
    │ 持有
    ▼
PizzaBuilder(接口)
    ▲
    │ 实现
    │
┌───┴───┐
│       │
ItalianBuilder  AmericanBuilder
                    </pre>
                    <p>Waiter类中有一个PizzaBuilder类型的成员变量，这个变量可以指向任何实现了PizzaBuilder接口的对象。</p>
                </div>
                <div class="demo-box">
                    <h3>代码中的体现</h3>
                    <div class="code-block">
class Waiter {
    // 这里持有的是接口类型，而不是具体实现
    private builder: PizzaBuilder;
    
    // 通过这个方法设置具体的Builder
    setBuilder(builder: PizzaBuilder) {
        this.builder = builder;
    }
    
    // 指导Builder完成构建过程
    constructPizza() {
        this.builder.prepareDough();
        this.builder.addSauce();
        this.builder.addToppings();
        this.builder.bake();
    }
}</div>
                </div>
            </div>
            
            <h3>交互演示</h3>
            <p>下面的演示展示了Waiter如何与不同的Builder交互。点击按钮查看过程：</p>
            
            <div class="btn-group">
                <button id="btn-italian">使用意大利风格Builder</button>
                <button id="btn-american">使用美式风格Builder</button>
                <button id="btn-reset">重置演示</button>
            </div>
            
            <div class="animation-area">
                <div id="waiter" class="actor">👨‍🍳</div>
                <div id="builder" class="actor">👨‍🔧</div>
                <div id="waiter-speech" class="speech-bubble"></div>
                <div id="builder-speech" class="speech-bubble"></div>
                <div id="pizza" class="pizza"></div>
            </div>
            <div id="step-text" class="step-indicator">选择一个Builder开始演示</div>
        </div>

        <h2>详细解释</h2>
        
        <div class="explanation">
            <h3>为什么说"Waiter构造了一个Builder接口对象"？</h3>
            <p>这句话其实不太准确。更准确的说法是：</p>
            <ol>
                <li><strong>Waiter持有一个Builder接口类型的引用</strong>：Waiter类中有一个Builder类型的成员变量</li>
                <li><strong>Waiter不创建Builder</strong>：Builder对象通常由客户端创建，然后通过setBuilder方法传给Waiter</li>
                <li><strong>Waiter指导Builder工作</strong>：Waiter调用Builder接口的方法，按特定顺序构建对象</li>
            </ol>
        </div>
        
        <div class="code-block">
// 完整代码示例

// 1. 定义Builder接口
interface PizzaBuilder {
    prepareDough(): void;
    addSauce(): void;
    addToppings(): void;
    bake(): void;
    getPizza(): Pizza;  // 获取最终产品
}

// 2. 产品类
class Pizza {
    private parts: string[] = [];
    
    add(part: string) {
        this.parts.push(part);
    }
    
    showParts() {
        return this.parts.join(', ');
    }
}

// 3. 具体Builder实现
class ItalianPizzaBuilder implements PizzaBuilder {
    private pizza = new Pizza();
    
    prepareDough() { 
        this.pizza.add("薄脆面团");
    }
    
    addSauce() { 
        this.pizza.add("番茄酱");
    }
    
    addToppings() { 
        this.pizza.add("马苏里拉奶酪");
        this.pizza.add("罗勒叶");
    }
    
    bake() { 
        this.pizza.add("石窑烘烤");
    }
    
    getPizza(): Pizza {
        return this.pizza;
    }
}

class AmericanPizzaBuilder implements PizzaBuilder {
    private pizza = new Pizza();
    
    prepareDough() { 
        this.pizza.add("厚实松软面团");
    }
    
    addSauce() { 
        this.pizza.add("番茄酱和大蒜");
    }
    
    addToppings() { 
        this.pizza.add("双倍奶酪");
        this.pizza.add("香肠");
        this.pizza.add("青椒");
    }
    
    bake() { 
        this.pizza.add("烤箱慢烤");
    }
    
    getPizza(): Pizza {
        return this.pizza;
    }
}

// 4. 指导者类
class Waiter {
    private builder: PizzaBuilder;
    
    setBuilder(builder: PizzaBuilder) {
        this.builder = builder;
    }
    
    constructPizza() {
        this.builder.prepareDough();
        this.builder.addSauce();
        this.builder.addToppings();
        this.builder.bake();
    }
    
    getPizza(): Pizza {
        return this.builder.getPizza();
    }
}

// 5. 客户端代码
const waiter = new Waiter();
const italianBuilder = new ItalianPizzaBuilder();

// 设置使用哪种Builder
waiter.setBuilder(italianBuilder);

// 指导Builder完成构建过程
waiter.constructPizza();

// 获取最终产品
const pizza = waiter.getPizza();
console.log(pizza.showParts());
// 输出: 薄脆面团, 番茄酱, 马苏里拉奶酪, 罗勒叶, 石窑烘烤</div>

        <div class="explanation">
            <h3>总结：Waiter和Builder的关系</h3>
            <ol>
                <li><strong>依赖倒置原则</strong>：Waiter依赖抽象接口(PizzaBuilder)，而不是具体实现</li>
                <li><strong>职责分离</strong>：Waiter负责构建流程，Builder负责具体实现</li>
                <li><strong>灵活性</strong>：可以轻松切换不同的Builder，而Waiter的代码不需要改变</li>
                <li><strong>可扩展性</strong>：添加新的Builder实现不会影响现有代码</li>
            </ol>
            <p>这就是为什么生成器模式在创建复杂对象时如此有用 - 它将"如何构建"和"构建什么"分离开来。</p>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const waiterElement = document.getElementById('waiter');
        const builderElement = document.getElementById('builder');
        const waiterSpeech = document.getElementById('waiter-speech');
        const builderSpeech = document.getElementById('builder-speech');
        const pizzaElement = document.getElementById('pizza');
        const stepText = document.getElementById('step-text');
        
        const btnItalian = document.getElementById('btn-italian');
        const btnAmerican = document.getElementById('btn-american');
        const btnReset = document.getElementById('btn-reset');
        
        let currentBuilder = '';
        let animationStep = 0;
        let animationTimer = null;
        
        // 设置Builder并开始演示
        btnItalian.addEventListener('click', () => startDemo('italian'));
        btnAmerican.addEventListener('click', () => startDemo('american'));
        btnReset.addEventListener('click', resetDemo);
        
        function startDemo(builderType) {
            resetDemo();
            currentBuilder = builderType;
            
            // 禁用按钮
            btnItalian.disabled = true;
            btnAmerican.disabled = true;
            
            // 更改Builder的表情
            builderElement.innerText = builderType === 'italian' ? '🧑‍🍳' : '👨‍🍳';
            
            // 开始动画
            nextStep();
        }
        
        function resetDemo() {
            // 清除定时器
            if (animationTimer) clearTimeout(animationTimer);
            
            // 重置状态
            animationStep = 0;
            currentBuilder = '';
            
            // 重置UI
            waiterElement.style.left = '50px';
            waiterElement.style.top = '50px';
            builderElement.style.right = '50px';
            builderElement.style.top = '50px';
            builderElement.innerText = '👨‍🔧';
            
            waiterSpeech.style.opacity = '0';
            builderSpeech.style.opacity = '0';
            pizzaElement.style.opacity = '0';
            pizzaElement.innerText = '';
            
            stepText.innerText = '选择一个Builder开始演示';
            
            // 启用按钮
            btnItalian.disabled = false;
            btnAmerican.disabled = false;
        }
        
        function nextStep() {
            animationStep++;
            
            switch(animationStep) {
                case 1:
                    // Waiter选择Builder
                    waiterSpeech.innerText = `我需要一个${currentBuilder === 'italian' ? '意大利' : '美式'}风格的披萨生成器`;
                    waiterSpeech.style.opacity = '1';
                    stepText.innerText = '1. Waiter选择了一个具体的Builder实现';
                    animationTimer = setTimeout(nextStep, 2000);
                    break;
                    
                case 2:
                    // Waiter指示准备面团
                    waiterSpeech.innerText = '请准备面团';
                    builderSpeech.innerText = currentBuilder === 'italian' ? '准备薄脆面团' : '准备厚实松软面团';
                    builderSpeech.style.opacity = '1';
                    stepText.innerText = '2. Waiter调用Builder的prepareDough()方法';
                    animationTimer = setTimeout(nextStep, 2000);
                    break;
                    
                case 3:
                    // Waiter指示添加酱料
                    waiterSpeech.innerText = '请添加酱料';
                    builderSpeech.innerText = currentBuilder === 'italian' ? '添加传统番茄酱' : '添加番茄酱和大蒜';
                    stepText.innerText = '3. Waiter调用Builder的addSauce()方法';
                    animationTimer = setTimeout(nextStep, 2000);
                    break;
                    
                case 4:
                    // Waiter指示添加配料
                    waiterSpeech.innerText = '请添加配料';
                    builderSpeech.innerText = currentBuilder === 'italian' ? '添加马苏里拉奶酪和罗勒叶' : '添加双倍奶酪、香肠和青椒';
                    stepText.innerText = '4. Waiter调用Builder的addToppings()方法';
                    animationTimer = setTimeout(nextStep, 2000);
                    break;
                    
                case 5:
                    // Waiter指示烘烤
                    waiterSpeech.innerText = '请烘烤披萨';
                    builderSpeech.innerText = currentBuilder === 'italian' ? '在石窑中高温快速烘烤' : '在烤箱中中温烘烤较长时间';
                    stepText.innerText = '5. Waiter调用Builder的bake()方法';
                    animationTimer = setTimeout(nextStep, 2000);
                    break;
                    
                case 6:
                    // 展示最终产品
                    waiterSpeech.style.opacity = '0';
                    builderSpeech.style.opacity = '0';
                    pizzaElement.innerText = '🍕';
                    pizzaElement.style.opacity = '1';
                    stepText.innerText = `6. 完成！Builder创建了一个${currentBuilder === 'italian' ? '意大利' : '美式'}风格的披萨`;
                    
                    // 启用重置按钮
                    btnReset.disabled = false;
                    break;
            }
        }
    </script>

</body>
</html> 