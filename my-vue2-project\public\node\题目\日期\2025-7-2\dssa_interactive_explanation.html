<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DSSA - 垂直域与水平域交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f4f8;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
        }
        h1, h2 {
            color: #1e3a8a;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .question-box {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: left;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .question-box p {
            margin: 10px 0;
        }
        .question-box .options {
            padding-left: 20px;
        }
        .correct-answer {
            font-weight: bold;
            color: #16a34a;
        }
        canvas {
            background-color: #ffffff;
            border: 1px solid #cbd5e1;
            border-radius: 8px;
            cursor: pointer;
            margin-bottom: 15px;
        }
        .explanation-text {
            min-height: 50px;
            padding: 15px;
            background-color: #f1f5f9;
            border-radius: 8px;
            color: #1e293b;
            font-size: 1.1em;
            font-weight: 500;
            line-height: 1.5;
            transition: background-color 0.3s ease;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>特定领域软件架构 (DSSA)</h1>
    
    <div class="question-box">
        <p><strong>题目：</strong>特定领域软件架构(Domain Specific Software Architecture, DSSA)是指特定应用领域中为一组应用提供组织结构参考的标准软件架构。从功能覆盖的范围角度，( )定义了一个特定的系统族，包含整个系统族内的多个系统，可作为该领域系统的可行解决方案的一个通用软件架构。</p>
        <div class="options">
            <p>A. 垂直域 <span class="correct-answer">(正确答案)</span></p>
            <p>B. 水平域</p>
            <p>C. 功能域</p>
            <p>D. 属性域</p>
        </div>
    </div>

    <h2>交互式演示：垂直域 vs 水平域</h2>
    <canvas id="dssa-canvas" width="800" height="400"></canvas>
    <div id="explanation-text" class="explanation-text">
        <p>将鼠标悬停在下方的彩色模块上，查看它们的区别。</p>
    </div>

</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('dssa-canvas');
    const ctx = canvas.getContext('2d');
    const explanationDiv = document.getElementById('explanation-text');

    // 适配高DPI屏幕
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);
    const canvasWidth = canvas.width / dpr;
    const canvasHeight = canvas.height / dpr;

    let mouseX = 0;
    let mouseY = 0;
    let hoveredItem = null;

    const colors = {
        bg: '#ffffff',
        text: '#334155',
        domainBg: '#e2e8f0',
        vertical: 'rgba(59, 130, 246, 0.7)',
        verticalHover: 'rgba(37, 99, 235, 1)',
        horizontal: 'rgba(234, 179, 8, 0.7)',
        horizontalHover: 'rgba(202, 138, 4, 1)',
    };

    const domains = [
        { name: '金融行业', x: 50, y: 80 },
        { name: '医疗行业', x: 300, y: 80 },
        { name: '零售行业', x: 550, y: 80 },
    ];
    const domainWidth = 200;
    const domainHeight = 280;

    const horizontalComponent = {
        id: 'horizontal',
        x: domains[0].x,
        y: 280,
        width: domains[2].x + domainWidth - domains[0].x,
        height: 60,
        text: '水平域：跨行业的通用功能',
        explanation: '<strong>水平域</strong>指的是那些不特定于某个行业，但在许多行业中都通用的功能模块。比如用户认证、日志记录、数据访问等，它们是构建任何大型应用的基础。'
    };

    const verticalComponent = {
        id: 'vertical',
        x: domains[0].x + 20,
        y: domains[0].y + 50,
        width: domainWidth - 40,
        height: 120,
        text: '垂直域：特定行业的核心功能',
        explanation: '<strong>垂直域</strong>深入某个特定行业，提供该行业独有的、专门化的功能。比如金融软件中的"利息计算"、"交易撮合"功能。这些功能是该行业的命脉，但在其他行业中几乎无用。'
    };

    function draw() {
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // Draw domains
        ctx.font = 'bold 16px sans-serif';
        ctx.textAlign = 'center';
        domains.forEach(domain => {
            ctx.fillStyle = colors.domainBg;
            ctx.fillRect(domain.x, domain.y, domainWidth, domainHeight);
            ctx.fillStyle = colors.text;
            ctx.fillText(domain.name, domain.x + domainWidth / 2, domain.y + 30);
        });

        // Draw Horizontal Component
        ctx.fillStyle = hoveredItem === horizontalComponent.id ? colors.horizontalHover : colors.horizontal;
        ctx.fillRect(horizontalComponent.x, horizontalComponent.y, horizontalComponent.width, horizontalComponent.height);
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '15px sans-serif';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(horizontalComponent.text, horizontalComponent.x + horizontalComponent.width / 2, horizontalComponent.y + horizontalComponent.height / 2);

        // Draw Vertical Component
        ctx.fillStyle = hoveredItem === verticalComponent.id ? colors.verticalHover : colors.vertical;
        ctx.fillRect(verticalComponent.x, verticalComponent.y, verticalComponent.width, verticalComponent.height);
        ctx.fillStyle = '#FFFFFF';
        ctx.fillText(verticalComponent.text, verticalComponent.x + verticalComponent.width / 2, verticalComponent.y + verticalComponent.height / 2);

        requestAnimationFrame(draw);
    }

    function checkHover() {
        let currentHover = null;
        if (mouseX > verticalComponent.x && mouseX < verticalComponent.x + verticalComponent.width &&
            mouseY > verticalComponent.y && mouseY < verticalComponent.y + verticalComponent.height) {
            currentHover = verticalComponent.id;
        } else if (mouseX > horizontalComponent.x && mouseX < horizontalComponent.x + horizontalComponent.width &&
                   mouseY > horizontalComponent.y && mouseY < horizontalComponent.y + horizontalComponent.height) {
            currentHover = horizontalComponent.id;
        }

        if (currentHover !== hoveredItem) {
            hoveredItem = currentHover;
            updateExplanation();
        }
    }

    function updateExplanation() {
        if (hoveredItem === 'vertical') {
            explanationDiv.innerHTML = verticalComponent.explanation;
            explanationDiv.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
        } else if (hoveredItem === 'horizontal') {
            explanationDiv.innerHTML = horizontalComponent.explanation;
            explanationDiv.style.backgroundColor = 'rgba(234, 179, 8, 0.1)';
        } else {
            explanationDiv.innerHTML = '<p>将鼠标悬停在上方彩色的模块上，查看它们的区别。</p>';
            explanationDiv.style.backgroundColor = '#f1f5f9';
        }
    }

    canvas.addEventListener('mousemove', (e) => {
        const rect = canvas.getBoundingClientRect();
        mouseX = e.clientX - rect.left;
        mouseY = e.clientY - rect.top;
        checkHover();
    });
    
    canvas.addEventListener('mouseleave', () => {
        hoveredItem = null;
        updateExplanation();
    });

    draw();
});
</script>

</body>
</html> 