<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java概念可视化</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .tab-container {
            display: flex;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background-color: #e0e0e0;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            flex: 1;
            transition: background-color 0.3s;
        }
        
        .tab.active {
            background-color: #3498db;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        canvas {
            border: 1px solid #ddd;
            margin: 20px auto;
            display: block;
        }
        
        .explanation {
            background-color: #e8f4fc;
            border-left: 4px solid #3498db;
            padding: 10px 15px;
            margin: 15px 0;
        }
        
        .button {
            background-color: #3498db;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .button:hover {
            background-color: #2980b9;
        }
        
        .demo-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .demo-box {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            width: 45%;
            min-width: 300px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>Java 核心概念可视化学习</h1>
    
    <div class="container">
        <div class="tab-container">
            <button class="tab active" onclick="showTab('error-exception')">Error 和 Exception 的区别</button>
            <button class="tab" onclick="showTab('final-keyword')">final 关键字的用法</button>
        </div>
        
        <div id="error-exception" class="tab-content active">
            <h2>Error 和 Exception 的区别</h2>
            
            <div class="explanation">
                <p><strong>Error</strong>：程序无法处理的严重问题，会导致程序崩溃，JVM停止运行。</p>
                <p><strong>Exception</strong>：程序可以处理的问题，分为编译时异常和运行时异常。</p>
            </div>
            
            <div class="demo-container">
                <div class="demo-box">
                    <h3>交互演示</h3>
                    <button class="button" onclick="simulateError()">模拟 Error</button>
                    <button class="button" onclick="simulateException()">模拟 Exception</button>
                    <button class="button" onclick="simulateHandledException()">模拟处理异常</button>
                </div>
                <div class="demo-box">
                    <h3>结果</h3>
                    <div id="error-exception-result" style="min-height: 100px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                        点击按钮查看效果
                    </div>
                </div>
            </div>
            
            <canvas id="errorExceptionCanvas" width="800" height="300"></canvas>
        </div>
        
        <div id="final-keyword" class="tab-content">
            <h2>final 关键字的用法</h2>
            
            <div class="explanation">
                <p><strong>修饰类</strong>：类不能被继承</p>
                <p><strong>修饰方法</strong>：方法不能被重写</p>
                <p><strong>修饰变量</strong>：变量只能被赋值一次，成为常量</p>
            </div>
            
            <div class="demo-container">
                <div class="demo-box">
                    <h3>交互演示</h3>
                    <button class="button" onclick="showFinalClass()">final修饰类</button>
                    <button class="button" onclick="showFinalMethod()">final修饰方法</button>
                    <button class="button" onclick="showFinalVariable()">final修饰变量</button>
                </div>
                <div class="demo-box">
                    <h3>结果</h3>
                    <div id="final-result" style="min-height: 100px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                        点击按钮查看效果
                    </div>
                </div>
            </div>
            
            <canvas id="finalKeywordCanvas" width="800" height="300"></canvas>
        </div>
    </div>

    <script>
        // 切换标签页
        function showTab(tabId) {
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            document.querySelector(`button[onclick="showTab('${tabId}')"]`).classList.add('active');
            document.getElementById(tabId).classList.add('active');
            
            if (tabId === 'error-exception') {
                initErrorExceptionCanvas();
            } else if (tabId === 'final-keyword') {
                initFinalKeywordCanvas();
            }
        }
        
        // Error 和 Exception 演示
        function simulateError() {
            const resultDiv = document.getElementById('error-exception-result');
            resultDiv.innerHTML = '<span style="color: red;">✖ Error 发生!</span><br>程序崩溃，JVM 停止运行。<br>示例：OutOfMemoryError - 内存溢出错误';
            
            // 在 Canvas 上动画显示 Error
            const canvas = document.getElementById('errorExceptionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            drawProgram(ctx, 150, 150);
            animateError(ctx);
        }
        
        function simulateException() {
            const resultDiv = document.getElementById('error-exception-result');
            resultDiv.innerHTML = '<span style="color: orange;">⚠ Exception 发生!</span><br>程序可以继续运行，但需要处理异常。<br>示例：NullPointerException - 空指针异常';
            
            // 在 Canvas 上动画显示 Exception
            const canvas = document.getElementById('errorExceptionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            drawProgram(ctx, 150, 150);
            animateException(ctx, false);
        }
        
        function simulateHandledException() {
            const resultDiv = document.getElementById('error-exception-result');
            resultDiv.innerHTML = '<span style="color: green;">✓ Exception 已处理!</span><br>程序通过 try-catch 捕获异常并处理，继续正常运行。';
            
            // 在 Canvas 上动画显示处理 Exception
            const canvas = document.getElementById('errorExceptionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            drawProgram(ctx, 150, 150);
            animateException(ctx, true);
        }
        
        // final 关键字演示
        function showFinalClass() {
            const resultDiv = document.getElementById('final-result');
            resultDiv.innerHTML = '<span style="color: blue;">final 修饰类</span><br>' +
                                 'final class FinalClass { ... }<br>' +
                                 '// 下面代码会导致编译错误<br>' +
                                 '<span style="color: red;">class Child extends FinalClass { ... } // 错误！不能继承 final 类</span>';
            
            drawFinalClass();
        }
        
        function showFinalMethod() {
            const resultDiv = document.getElementById('final-result');
            resultDiv.innerHTML = '<span style="color: blue;">final 修饰方法</span><br>' +
                                 'class Parent { <br>' +
                                 '&nbsp;&nbsp;final void finalMethod() { ... } <br>' +
                                 '} <br>' +
                                 'class Child extends Parent { <br>' +
                                 '&nbsp;&nbsp;<span style="color: red;">void finalMethod() { ... } // 错误！不能重写 final 方法</span> <br>' +
                                 '}';
            
            drawFinalMethod();
        }
        
        function showFinalVariable() {
            const resultDiv = document.getElementById('final-result');
            resultDiv.innerHTML = '<span style="color: blue;">final 修饰变量</span><br>' +
                                 'final int MAX_VALUE = 100; <br>' +
                                 '<span style="color: red;">MAX_VALUE = 200; // 错误！final 变量不能被修改</span><br><br>' +
                                 '// 对于引用类型：<br>' +
                                 'final Person person = new Person(); <br>' +
                                 '<span style="color: red;">person = new Person(); // 错误！引用不能改变</span><br>' +
                                 'person.name = "张三"; // 正确，可以修改对象的属性';
            
            drawFinalVariable();
        }
        
        // Canvas 动画和绘图函数
        function initErrorExceptionCanvas() {
            const canvas = document.getElementById('errorExceptionCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制初始状态
            drawProgram(ctx, 150, 150);
            
            // 绘制说明
            ctx.font = '16px Arial';
            ctx.fillStyle = 'black';
            ctx.fillText('点击上方按钮查看动画演示', 300, 150);
        }
        
        function initFinalKeywordCanvas() {
            const canvas = document.getElementById('finalKeywordCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制初始状态
            ctx.font = '16px Arial';
            ctx.fillStyle = 'black';
            ctx.fillText('点击上方按钮查看 final 关键字的不同用法', 250, 150);
        }
        
        function drawProgram(ctx, x, y) {
            ctx.fillStyle = '#3498db';
            ctx.fillRect(x - 50, y - 50, 100, 100);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('程序', x - 16, y + 5);
            
            // 绘制 JVM
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(x - 100, y + 100, 200, 50);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('JVM', x - 12, y + 130);
        }
        
        function animateError(ctx) {
            let explosionRadius = 0;
            const maxRadius = 100;
            const x = 150, y = 150;
            
            function drawExplosion() {
                // 清除之前的爆炸效果
                ctx.clearRect(x - maxRadius - 10, y - maxRadius - 10, maxRadius * 2 + 20, maxRadius * 2 + 20);
                
                // 重绘程序
                drawProgram(ctx, x, y);
                
                // 绘制爆炸效果
                const gradient = ctx.createRadialGradient(x, y, 0, x, y, explosionRadius);
                gradient.addColorStop(0, 'rgba(255, 0, 0, 0.8)');
                gradient.addColorStop(1, 'rgba(255, 0, 0, 0)');
                
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(x, y, explosionRadius, 0, Math.PI * 2);
                ctx.fill();
                
                explosionRadius += 2;
                
                // 添加文字说明
                ctx.font = '18px Arial';
                ctx.fillStyle = 'red';
                ctx.fillText('Error: 程序崩溃，JVM停止运行', 300, 120);
                
                // 画一个 X 在 JVM 上
                if (explosionRadius > 70) {
                    ctx.strokeStyle = 'red';
                    ctx.lineWidth = 4;
                    ctx.beginPath();
                    ctx.moveTo(x - 80, y + 100);
                    ctx.lineTo(x + 80, y + 150);
                    ctx.stroke();
                    
                    ctx.beginPath();
                    ctx.moveTo(x + 80, y + 100);
                    ctx.lineTo(x - 80, y + 150);
                    ctx.stroke();
                }
                
                if (explosionRadius < maxRadius) {
                    requestAnimationFrame(drawExplosion);
                }
            }
            
            drawExplosion();
        }
        
        function animateException(ctx, handled) {
            let waveY = 0;
            const waveHeight = 20;
            const waveDuration = 50;
            const x = 150, y = 150;
            
            function drawWave() {
                // 清除之前的波浪效果
                ctx.clearRect(x - 60, y - 60, 120, 220);
                
                // 重绘程序
                drawProgram(ctx, x, y);
                
                // 绘制波浪效果（代表异常）
                ctx.strokeStyle = handled ? 'orange' : 'red';
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                // 波浪线从程序向下传播
                for (let i = 0; i < 3; i++) {
                    const waveOffset = waveY + i * 20;
                    if (waveOffset > 0 && waveOffset < 100) {
                        ctx.beginPath();
                        for (let j = -50; j <= 50; j += 5) {
                            const xPos = x + j;
                            const yPos = y + waveOffset + Math.sin(j / 10) * (waveHeight - waveOffset / 5);
                            
                            if (j === -50) {
                                ctx.moveTo(xPos, yPos);
                            } else {
                                ctx.lineTo(xPos, yPos);
                            }
                        }
                        ctx.stroke();
                    }
                }
                
                // 添加文字说明
                ctx.font = '18px Arial';
                ctx.fillStyle = handled ? 'green' : 'orange';
                const text = handled ? 'Exception: 已捕获并处理' : 'Exception: 未处理的异常';
                ctx.fillText(text, 300, 120);
                
                // 如果异常被处理，绘制一个防护罩
                if (handled) {
                    ctx.strokeStyle = 'green';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.arc(x, y, 60, 0, Math.PI * 2);
                    ctx.stroke();
                    
                    // 绘制 try-catch 标签
                    ctx.fillStyle = 'green';
                    ctx.fillText('try-catch', x - 35, y - 65);
                }
                
                waveY += 2;
                
                if (waveY < waveDuration) {
                    requestAnimationFrame(drawWave);
                } else if (!handled) {
                    // 如果未处理，显示程序受到影响但没有崩溃
                    ctx.fillStyle = 'rgba(255, 165, 0, 0.3)';
                    ctx.fillRect(x - 50, y - 50, 100, 100);
                    ctx.fillStyle = 'orange';
                    ctx.fillText('程序异常但继续运行', 300, 150);
                } else {
                    // 如果已处理，显示程序正常运行
                    ctx.fillStyle = 'green';
                    ctx.fillText('程序正常运行', 300, 150);
                }
            }
            
            drawWave();
        }
        
        function drawFinalClass() {
            const canvas = document.getElementById('finalKeywordCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制 final 类
            ctx.fillStyle = '#3498db';
            ctx.fillRect(200, 50, 150, 80);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('final class FinalClass', 210, 90);
            
            // 尝试继承的类
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(200, 200, 150, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('class Child', 235, 240);
            
            // 绘制继承箭头
            ctx.strokeStyle = 'red';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(275, 200);
            ctx.lineTo(275, 130);
            ctx.stroke();
            
            // 绘制禁止标志
            ctx.beginPath();
            ctx.arc(275, 165, 15, 0, Math.PI * 2);
            ctx.strokeStyle = 'red';
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(260, 165);
            ctx.lineTo(290, 165);
            ctx.stroke();
            
            // 绘制说明
            ctx.fillStyle = 'black';
            ctx.fillText('final 修饰的类不能被继承', 400, 165);
        }
        
        function drawFinalMethod() {
            const canvas = document.getElementById('finalKeywordCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制父类
            ctx.fillStyle = '#3498db';
            ctx.fillRect(200, 50, 150, 100);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('class Parent', 235, 80);
            ctx.fillText('final void', 235, 100);
            ctx.fillText('finalMethod()', 235, 120);
            
            // 绘制子类
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(200, 200, 150, 100);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('class Child', 235, 230);
            ctx.fillStyle = 'red';
            ctx.fillText('void finalMethod()', 212, 250);
            ctx.fillStyle = 'white';
            ctx.fillText('// 错误!', 235, 270);
            
            // 绘制继承箭头
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(275, 200);
            ctx.lineTo(275, 150);
            ctx.stroke();
            
            // 绘制禁止标志在方法上
            ctx.beginPath();
            ctx.arc(250, 250, 15, 0, Math.PI * 2);
            ctx.strokeStyle = 'red';
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(235, 250);
            ctx.lineTo(265, 250);
            ctx.stroke();
            
            // 绘制说明
            ctx.fillStyle = 'black';
            ctx.fillText('final 修饰的方法不能被重写', 400, 165);
        }
        
        function drawFinalVariable() {
            const canvas = document.getElementById('finalKeywordCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制原始类型变量示例
            ctx.fillStyle = '#3498db';
            ctx.fillRect(150, 50, 180, 60);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('final int MAX_VALUE = 100', 160, 85);
            
            // 绘制尝试修改
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(150, 130, 180, 60);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('MAX_VALUE = 200', 180, 165);
            
            // 绘制禁止标志
            ctx.beginPath();
            ctx.arc(240, 165, 15, 0, Math.PI * 2);
            ctx.strokeStyle = 'red';
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(225, 165);
            ctx.lineTo(255, 165);
            ctx.stroke();
            
            // 绘制引用类型示例
            ctx.fillStyle = '#9b59b6';
            ctx.fillRect(450, 50, 250, 60);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('final Person person = new Person()', 460, 85);
            
            // 绘制尝试修改引用
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(450, 130, 250, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('person = new Person()', 480, 165);
            
            // 绘制禁止标志
            ctx.beginPath();
            ctx.arc(575, 165, 15, 0, Math.PI * 2);
            ctx.strokeStyle = 'red';
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(560, 165);
            ctx.lineTo(590, 165);
            ctx.stroke();
            
            // 绘制修改对象属性（允许）
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(450, 210, 250, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('person.name = "张三"', 490, 245);
            
            // 绘制勾号
            ctx.strokeStyle = 'green';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(560, 245);
            ctx.lineTo(570, 255);
            ctx.lineTo(590, 235);
            ctx.stroke();
            
            // 绘制说明
            ctx.fillStyle = 'black';
            ctx.font = '14px Arial';
            ctx.fillText('final 变量只能被赋值一次', 150, 210);
            ctx.fillText('final 引用变量不能指向新对象', 450, 290);
            ctx.fillText('但可以修改对象的属性', 450, 310);
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            initErrorExceptionCanvas();
        };
    </script>
</body>
</html> 