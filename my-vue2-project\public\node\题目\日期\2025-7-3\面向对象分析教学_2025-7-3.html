<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象分析教学</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: column;
            gap: 25px;
        }
        h1, h2 {
            color: #007bff;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 1.8em;
            color: #28a745;
        }
        .question-section {
            background-color: #e9f7ef;
            padding: 20px;
            border-radius: 6px;
            border-left: 5px solid #28a745;
        }
        .question-text {
            font-size: 1.3em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .options label {
            display: block;
            margin-bottom: 12px;
            font-size: 1.1em;
            cursor: pointer;
            padding: 10px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .options label:hover {
            background-color: #d8edf1;
        }
        .options input[type="radio"] {
            margin-right: 10px;
        }
        .explanation-section {
            background-color: #f0f8ff;
            padding: 20px;
            border-radius: 6px;
            border-left: 5px solid #007bff;
        }
        .explanation-content {
            font-size: 1.1em;
        }
        .quiz-result {
            margin-top: 20px;
            font-weight: bold;
            font-size: 1.2em;
        }
        .correct {
            color: #28a745;
        }
        .incorrect {
            color: #dc3545;
        }
        canvas {
            border: 1px solid #ddd;
            background-color: #fff;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        .controls button {
            background-color: #007bff;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin: 0 10px;
        }
        .controls button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        .controls button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .step-info {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 1.1em;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>面向对象分析教学</h1>

        <div class="question-section">
            <h2>问题14：单选题</h2>
            <p class="question-text">在面向对象分析中，利用 ( ) 表示需求，并从中提炼出 ( )，以上两者形成 ( )，之后再进行后续的开发工作。</p>
            <form id="quizForm">
                <div class="options">
                    <label>
                        <input type="radio" name="answer" value="A"> A. 领域模型
                    </label>
                    <label>
                        <input type="radio" name="answer" value="B"> B. 包图
                    </label>
                    <label>
                        <input type="radio" name="answer" value="C"> C. 体系结构图
                    </label>
                    <label>
                        <input type="radio" name="answer" value="D"> D. 类图
                    </label>
                </div>
                <button type="submit">提交答案</button>
            </form>
            <div id="quizResult" class="quiz-result"></div>
        </div>

        <div class="explanation-section">
            <h2>知识点讲解与互动演示</h2>
            <div class="explanation-content" id="explanationText">
                <p>欢迎来到面向对象分析（OOA）的学习！OOA是软件开发生命周期中的一个重要阶段，它侧重于识别和定义系统的对象，以及它们如何相互关联，以满足用户需求。我们将通过交互式动画来逐步理解这些概念。</p>
                <p>点击"开始学习"按钮，让我们一起探索面向对象分析的奥秘吧！</p>
            </div>
            <canvas id="ooadCanvas" width="900" height="600"></canvas>
            <div class="controls">
                <button id="prevBtn" disabled>上一步</button>
                <button id="nextBtn">开始学习</button>
            </div>
            <div class="step-info" id="stepInfo"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('ooadCanvas');
        const ctx = canvas.getContext('2d');
        const explanationTextDiv = document.getElementById('explanationText');
        const stepInfoDiv = document.getElementById('stepInfo');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const quizForm = document.getElementById('quizForm');
        const quizResultDiv = document.getElementById('quizResult');

        let currentStep = -1; // -1 for initial state, 0 for first actual step

        const steps = [
            {
                name: "OOA介绍",
                explanation: `欢迎来到面向对象分析（OOA）的学习！OOA是软件开发生命周期中的一个重要阶段，它侧重于识别和定义系统的对象，以及它们如何相互关联，以满足用户需求。我们将通过交互式动画来逐步理解这些概念。<br><br>在软件开发中，我们首先需要理解用户"想要什么"。这就是需求分析的阶段。`,
                draw: () => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    drawText("面向对象分析 (OOA)", canvas.width / 2, 100, 36, "#007bff");
                    drawText("理解用户需求 -> 识别对象 -> 构建模型", canvas.width / 2, 180, 24, "#555");
                    drawFlowArrow(canvas.width / 2, 250, canvas.width / 2, 350, "开始");
                    drawRectangle(canvas.width / 2 - 100, 350, 200, 80, "#28a745", "需求分析", "#fff");
                }
            },
            {
                name: "用例与用例图",
                explanation: `在面向对象分析中，我们首先利用<strong>用例（Use Case）</strong>和<strong>用例图（Use Case Diagram）</strong>来表示系统的功能需求。<br><br>用例描述了系统外部参与者（如用户）与系统之间的一系列交互，以完成一个有意义的目标。用例图则可视化了这些用例以及参与者与用例之间的关系。`,
                draw: () => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    drawText("用例与用例图", canvas.width / 2, 50, 30, "#007bff");

                    // 参与者 (Actor)
                    drawActor(150, 200, "用户");
                    // 用例 (Use Case)
                    drawUseCase(400, 200, "购买商品");
                    drawUseCase(400, 350, "查看订单");
                    // 关系 (Association)
                    drawLine(180, 200, 300, 200, "关联");
                    drawLine(180, 200, 300, 350, "关联");

                    drawText("一个用户可以执行多个用例", 550, 275, 18, "#555");
                    drawText("点击用例或参与者查看详情", canvas.width / 2, canvas.height - 50, 16, "#777");

                    canvas.onclick = (event) => {
                        const rect = canvas.getBoundingClientRect();
                        const x = event.clientX - rect.left;
                        const y = event.clientY - rect.top;

                        if (isPointInRect(x, y, 130, 160, 40, 80)) { // Actor area
                            alert("参与者：系统外部与系统交互的人或事物。");
                        } else if (isPointInEllipse(x, y, 400, 200, 100, 40)) { // Buy product use case
                            alert("用例：系统提供的一个功能单元，描述了参与者与系统之间的交互。");
                        } else if (isPointInEllipse(x, y, 400, 350, 100, 40)) { // View order use case
                            alert("用例：系统提供的一个功能单元，描述了参与者与系统之间的交互。");
                        }
                    };
                }
            },
            {
                name: "从用例中提炼领域模型",
                explanation: `从用例和用例图中，我们提炼出<strong>领域模型（Domain Model）</strong>。<br><br>领域模型是描述问题领域中概念、它们之间的关系以及它们的属性的可视化表示。它不关注软件实现细节，而是关注业务领域的"事物"。`,
                draw: () => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    drawText("从用例到领域模型", canvas.width / 2, 50, 30, "#007bff");

                    // 模拟用例输入
                    drawRectangle(150, 150, 200, 80, "#28a745", "用例: 购买商品", "#fff");
                    drawRectangle(150, 280, 200, 80, "#28a745", "用例: 查看订单", "#fff");
                    drawFlowArrow(250, 240, 250, 270, "分析");

                    // 动画效果：从用例中提取名词
                    const nouns = ["商品", "订单", "用户", "购物车"];
                    nouns.forEach((noun, index) => {
                        setTimeout(() => {
                            drawText(noun, 450, 150 + index * 50, 20, "#e67e22");
                            if (index === nouns.length - 1) {
                                setTimeout(() => {
                                    drawFlowArrow(550, 250, 650, 250, "提炼");
                                    // 领域模型
                                    drawDomainClass(750, 150, "商品", ["名称", "价格"]);
                                    drawDomainClass(750, 280, "订单", ["日期", "总价"]);
                                    drawDomainClass(750, 410, "用户", ["用户名", "地址"]);
                                }, 1000);
                            }
                        }, index * 500);
                    });

                    drawText("领域模型只包含业务概念和它们的关系", canvas.width / 2, canvas.height - 50, 16, "#777");

                    canvas.onclick = (event) => {
                         const rect = canvas.getBoundingClientRect();
                        const x = event.clientX - rect.left;
                        const y = event.clientY - rect.top;
                        if (isPointInRect(x,y, 700, 120, 100, 80)) { //商品
                            alert("商品：领域中的一个重要概念，代表可供销售的物品。");
                        } else if (isPointInRect(x,y, 700, 250, 100, 80)) { //订单
                            alert("订单：领域中的一个重要概念，代表用户购买行为的记录。");
                        } else if (isPointInRect(x,y, 700, 380, 100, 80)) { //用户
                            alert("用户：领域中的一个重要概念，代表与系统交互的个人。");
                        }
                    };
                }
            },
            {
                name: "从领域模型到类图",
                explanation: `从领域模型和用例图中，我们最终形成<strong>类图（Class Diagram）</strong>。<br><br>类图是面向对象建模中最常用的图之一，它详细描述了系统中类的静态结构，包括类的属性（数据）、操作（行为），以及类之间的关系（如关联、继承、聚合、组合）。类图是设计阶段的基础，直接指导后续的代码实现。`,
                draw: () => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    drawText("从领域模型到类图", canvas.width / 2, 50, 30, "#007bff");

                    // 领域模型作为输入
                    drawDomainClass(200, 150, "商品", ["名称", "价格"]);
                    drawDomainClass(200, 300, "订单", ["日期", "总价"]);
                    drawDomainClass(200, 450, "用户", ["用户名", "地址"]);

                    drawFlowArrow(350, 300, 450, 300, "细化");

                    // 动画效果：增加操作和关系
                    setTimeout(() => {
                        // 类图
                        drawClass(650, 100, "Product", ["name: String", "price: double"], ["calcTax()", "updateStock()"]);
                        drawClass(650, 280, "Order", ["orderDate: Date", "totalAmount: double"], ["calculateTotal()", "placeOrder()"]);
                        drawClass(650, 460, "User", ["username: String", "address: String"], ["login()", "register()"]);

                        // 关系
                        drawLine(700, 220, 700, 270, "1..*", "0..1"); // Product *--1 OrderLineItem (implicit)
                        drawLine(700, 400, 700, 450, "1", "1..*"); // User 1--* Order

                        drawText("类图包含属性、操作和多种关系", canvas.width / 2, canvas.height - 50, 16, "#777");

                        canvas.onclick = (event) => {
                            const rect = canvas.getBoundingClientRect();
                            const x = event.clientX - rect.left;
                            const y = event.clientY - rect.top;
                            if (isPointInRect(x,y, 600, 100, 200, 100)) { // Product class
                                alert("Product类：表示商品，包含名称、价格等属性，以及计算税费、更新库存等操作。");
                            } else if (isPointInRect(x,y, 600, 280, 200, 100)) { // Order class
                                alert("Order类：表示订单，包含日期、总价等属性，以及计算总价、下订单等操作。");
                            } else if (isPointInRect(x,y, 600, 460, 200, 100)) { // User class
                                alert("User类：表示用户，包含用户名、地址等属性，以及登录、注册等操作。");
                            }
                        };
                    }, 1000);
                }
            },
            {
                name: "包图与体系结构图",
                explanation: `最后，用<strong>包图（Package Diagram）</strong>和<strong>类图</strong>可以进一步形成<strong>体系结构图（Architecture Diagram）</strong>。<br><br>包图用于组织和管理大型系统中的模型元素（如类），将相关的类分组到逻辑包中。体系结构图则从更高层次展示系统的主要组件、它们之间的关系以及它们如何协同工作，帮助我们理解系统的整体结构。`,
                draw: () => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    drawText("包图与体系结构图", canvas.width / 2, 50, 30, "#007bff");

                    // 模拟类图的输入
                    drawText("输入：大量类图", 200, 150, 20, "#555");
                    drawClass(150, 200, "User", [], []);
                    drawClass(250, 250, "Product", [], []);
                    drawClass(150, 300, "Order", [], []);
                    drawClass(250, 350, "Payment", [], []);

                    drawFlowArrow(350, 280, 450, 280, "分组/抽象");

                    // 包图
                    drawPackage(550, 100, "用户管理", ["User", "AuthService"]);
                    drawPackage(550, 300, "商品目录", ["Product", "Category"]);
                    drawPackage(550, 500, "交易处理", ["Order", "Payment"]);

                    // 体系结构图 (简化)
                    drawFlowArrow(700, 280, 800, 280, "汇总");
                    drawRectangle(850, 200, 150, 80, "#6c757d", "用户模块", "#fff");
                    drawRectangle(850, 300, 150, 80, "#6c757d", "商品模块", "#fff");
                    drawRectangle(850, 400, 150, 80, "#6c757d", "订单模块", "#fff");

                    drawText("体系结构图是系统的高层视图", canvas.width / 2, canvas.height - 50, 16, "#777");

                    canvas.onclick = (event) => {
                        const rect = canvas.getBoundingClientRect();
                        const x = event.clientX - rect.left;
                        const y = event.clientY - rect.top;
                        if (isPointInRect(x,y, 550, 100, 150, 100)) { // User Management Package
                            alert("用户管理包：包含用户相关的类和功能。");
                        } else if (isPointInRect(x,y, 550, 300, 150, 100)) { // Product Catalog Package
                            alert("商品目录包：包含商品相关的类和功能。");
                        } else if (isPointInRect(x,y, 550, 500, 150, 100)) { // Transaction Processing Package
                            alert("交易处理包：包含订单和支付相关的类和功能。");
                        }
                    };
                }
            },
            {
                name: "总结与答案揭晓",
                explanation: `通过前面的学习，我们了解了面向对象分析的整个流程。<br><br>现在，让我们回顾一下问题：<br>"在面向对象分析中，利用 <strong>用例与用例图</strong> 表示需求，并从中提炼出 <strong>领域模型</strong>，以上两者最终形成 <strong>类图</strong>，之后再进行后续的开发工作。"<br><br>因此，本题的正确答案是：<strong>D. 类图</strong>。<br><br>类图是连接分析阶段（理解需求和领域）与设计阶段（构建系统结构）的关键桥梁。希望这个互动演示能帮助您更好地理解面向对象分析！`,
                draw: () => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    drawText("知识点回顾", canvas.width / 2, 100, 36, "#007bff");

                    drawRectangle(100, 200, 200, 80, "#28a745", "需求 (用例图)", "#fff");
                    drawFlowArrow(200, 280, 200, 330, "提炼");
                    drawRectangle(100, 350, 200, 80, "#e67e22", "业务概念 (领域模型)", "#fff");

                    drawFlowArrow(320, 280, 400, 280, "结合");
                    drawFlowArrow(320, 380, 400, 380, "结合");

                    drawRectangle(450, 300, 200, 80, "#007bff", "系统结构 (类图)", "#fff");

                    drawText("需求 -> 领域模型 -> 类图", canvas.width / 2, canvas.height - 50, 20, "#555");
                }
            }
        ];

        // Drawing Utility Functions
        function drawText(text, x, y, fontSize = 20, color = '#333', align = 'center') {
            ctx.font = `${fontSize}px Arial`;
            ctx.fillStyle = color;
            ctx.textAlign = align;
            ctx.fillText(text, x, y);
        }

        function drawRectangle(x, y, width, height, fillColor, text, textColor = '#fff') {
            ctx.fillStyle = fillColor;
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = '#333';
            ctx.strokeRect(x, y, width, height);
            drawText(text, x + width / 2, y + height / 2 + fontSize / 3, fontSize = 18, textColor);
        }

        function drawFlowArrow(x1, y1, x2, y2, text = "", color = "#000") {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();

            // Arrowhead
            const headlen = 10;
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.lineTo(x2 - headlen * Math.cos(angle - Math.PI / 6), y2 - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - headlen * Math.cos(angle + Math.PI / 6), y2 - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();

            if (text) {
                const midX = (x1 + x2) / 2;
                const midY = (y1 + y2) / 2;
                drawText(text, midX, midY - 10, 14, color);
            }
        }

        function drawActor(x, y, name) {
            // Head
            ctx.beginPath();
            ctx.arc(x, y - 30, 20, 0, Math.PI * 2, true);
            ctx.fillStyle = "#3498db";
            ctx.fill();
            ctx.strokeStyle = "#2980b9";
            ctx.stroke();

            // Body
            ctx.beginPath();
            ctx.moveTo(x, y - 10);
            ctx.lineTo(x, y + 40);
            ctx.stroke();

            // Arms
            ctx.beginPath();
            ctx.moveTo(x - 30, y);
            ctx.lineTo(x + 30, y);
            ctx.stroke();

            // Legs
            ctx.beginPath();
            ctx.moveTo(x, y + 40);
            ctx.lineTo(x - 20, y + 70);
            ctx.moveTo(x, y + 40);
            ctx.lineTo(x + 20, y + 70);
            ctx.stroke();

            drawText(name, x, y + 85, 16, "#333");
        }

        function drawUseCase(x, y, name) {
            // Ellipse
            ctx.beginPath();
            ctx.ellipse(x, y, 100, 40, 0, 0, Math.PI * 2);
            ctx.fillStyle = "#f39c12";
            ctx.fill();
            ctx.strokeStyle = "#d35400";
            ctx.stroke();
            drawText(name, x, y + 5, 18, "#fff");
        }

        function drawDomainClass(x, y, name, attributes) {
            const width = 150;
            const height = 30 + attributes.length * 20;
            ctx.fillStyle = "#ecf0f1";
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = "#7f8c8d";
            ctx.strokeRect(x, y, width, height);

            drawText(name, x + width / 2, y + 20, 18, "#2c3e50");
            ctx.beginPath();
            ctx.moveTo(x, y + 30);
            ctx.lineTo(x + width, y + 30);
            ctx.stroke();

            attributes.forEach((attr, index) => {
                drawText(attr, x + 10, y + 50 + index * 20, 14, "#555", 'left');
            });
        }

        function drawClass(x, y, name, attributes, operations) {
            const width = 200;
            const attrHeight = attributes.length * 20;
            const opHeight = operations.length * 20;
            const totalHeight = 30 + attrHeight + 10 + opHeight;

            ctx.fillStyle = "#e0f2f7";
            ctx.fillRect(x, y, width, totalHeight);
            ctx.strokeStyle = "#007bff";
            ctx.strokeRect(x, y, width, totalHeight);

            drawText(name, x + width / 2, y + 20, 20, "#007bff");
            ctx.beginPath();
            ctx.moveTo(x, y + 30);
            ctx.lineTo(x + width, y + 30);
            ctx.stroke();

            attributes.forEach((attr, index) => {
                drawText(`- ${attr}`, x + 10, y + 50 + index * 20, 14, "#333", 'left');
            });
            ctx.beginPath();
            ctx.moveTo(x, y + 30 + attrHeight + 5);
            ctx.lineTo(x + width, y + 30 + attrHeight + 5);
            ctx.stroke();

            operations.forEach((op, index) => {
                drawText(`+ ${op}`, x + 10, y + 30 + attrHeight + 25 + index * 20, 14, "#333", 'left');
            });
        }

        function drawPackage(x, y, name, contents) {
            const width = 150;
            const height = 100;
            ctx.fillStyle = "#d4edda";
            ctx.fillRect(x, y + 20, width, height - 20); // Main box
            ctx.strokeStyle = "#28a745";
            ctx.strokeRect(x, y + 20, width, height - 20);

            // Tab
            ctx.fillStyle = "#d4edda";
            ctx.fillRect(x, y, width / 2, 20);
            ctx.strokeStyle = "#28a745";
            ctx.strokeRect(x, y, width / 2, 20);

            drawText(name, x + width / 4, y + 15, 14, "#28a745");

            contents.forEach((item, index) => {
                drawText(item, x + width / 2, y + 45 + index * 20, 12, "#555");
            });
        }

        // Helper for click detection
        function isPointInRect(px, py, rx, ry, rw, rh) {
            return px >= rx && px <= rx + rw && py >= ry && py <= ry + rh;
        }

        function isPointInEllipse(px, py, ex, ey, rx, ry) {
            // Check if point (px, py) is inside ellipse with center (ex, ey) and radii rx, ry
            // ( (px - ex)^2 / rx^2 ) + ( (py - ey)^2 / ry^2 ) <= 1
            return (Math.pow(px - ex, 2) / Math.pow(rx, 2)) + (Math.pow(py - ey, 2) / Math.pow(ry, 2)) <= 1;
        }


        // Navigation Logic
        function updateUI() {
            explanationTextDiv.innerHTML = steps[currentStep].explanation;
            stepInfoDiv.textContent = `当前步骤：${currentStep + 1} / ${steps.length} - ${steps[currentStep].name}`;
            steps[currentStep].draw();

            prevBtn.disabled = currentStep === 0;
            nextBtn.disabled = currentStep === steps.length - 1;
            nextBtn.textContent = currentStep === steps.length - 1 ? "已完成" : "下一步";
        }

        function nextStep() {
            if (currentStep < steps.length - 1) {
                currentStep++;
                updateUI();
            }
        }

        function prevStep() {
            if (currentStep > 0) {
                currentStep--;
                updateUI();
            }
        }

        nextBtn.addEventListener('click', () => {
            if (currentStep === -1) { // First click is "Start Learning"
                currentStep = 0;
            } else {
                nextStep();
            }
            updateUI();
        });
        prevBtn.addEventListener('click', prevStep);

        // Quiz Logic
        quizForm.addEventListener('submit', (event) => {
            event.preventDefault();
            const selectedOption = document.querySelector('input[name="answer"]:checked');
            if (selectedOption) {
                const userAnswer = selectedOption.value;
                const correctAnswer = 'D'; // Correct answer for the given question

                if (userAnswer === correctAnswer) {
                    quizResultDiv.innerHTML = '<span class="correct">恭喜你，回答正确！</span>';
                } else {
                    quizResultDiv.innerHTML = `<span class="incorrect">很抱歉，回答错误。正确答案是：${correctAnswer}</span>`;
                }
                quizResultDiv.innerHTML += '<br>请继续学习下面的知识点讲解，答案会在最后揭晓哦！';
            } else {
                quizResultDiv.innerHTML = '<span class="incorrect">请选择一个答案。</span>';
            }
        });

        // Initial setup
        // Draw initial intro on canvas when page loads, but without activating step 0
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        drawText('点击下方 "开始学习" 按钮', canvas.width / 2, canvas.height / 2 - 20, 24, "#555");
        drawText("开始面向对象分析的交互式教学", canvas.width / 2, canvas.height / 2 + 20, 20, "#555");
        explanationTextDiv.innerHTML = steps[0].explanation;
        stepInfoDiv.textContent = `当前步骤：未开始`;
    </script>
</body>
</html> 