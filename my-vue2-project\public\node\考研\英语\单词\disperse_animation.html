<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Disperse</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #26a69a; /* 青色，代表自然与散播 */
            --secondary-color: #00897b;
            --accent-color: #ffd54f; /* 淡黄色，如花粉 */
            --light-bg: #e0f2f1;
            --panel-bg: #ffffff;
            --text-color: #004d40;
            --text-muted: #7f8c8d;
            --canvas-bg: #111;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #b2dfdb;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--primary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #00695c;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .breakdown-section .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            margin: 5px;
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .video-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 10px;
        }

        .video-placeholder {
            width: 100%;
            height: 150px;
            background-color: #e0e0e0;
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #757575;
            text-align: center;
            margin-bottom: 15px;
        }

        .video-link-btn {
            display: block;
            width: 100%;
            text-align: center;
            padding: 10px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .video-link-btn:hover {
            background-color: var(--secondary-color);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            background: #fafafa;
        }

        .activity-title {
            font-size: 1.8em;
            color: var(--primary-color);
            margin-bottom: 15px;
            text-align: center;
        }

        .activity-wrapper {
            display: none;
            width: 100%;
            height: calc(100% - 100px);
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .activity-wrapper.active {
            display: flex;
        }

        .game-container {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px;
            background: linear-gradient(to bottom, #80deea, #4dd0e1);
            overflow: hidden;
        }

        #dispersal-canvas {
            cursor: pointer;
        }

        .control-button {
            margin-top: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>disperse</h1>
            <p class="pronunciation">[dɪˈspɜːs]</p>
            <div class="details">
                <p><strong>词性：</strong> v. （使）分散，散开；传播</p>
                <p><strong>含义：</strong> 分散或传播至一个广阔的区域。</p>
                <div class="example">
                    <p><strong>例句1:</strong> The police used tear gas to disperse the crowd.</p>
                    <p><strong>翻译1:</strong> 警察使用催泪瓦斯来驱散人群。</p>
                    <p><strong>例句2:</strong> The wind dispersed the dandelion seeds.</p>
                    <p><strong>翻译2:</strong> 风吹散了蒲公英的种子。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>单词动画</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="dispersal-game">互动: 蒲公英的飘散</button>
                </div>
            </div>
            <div class="video-section">
                <h3>相关视频</h3>
                <div class="video-placeholder">您可以将从其他网站获得的视频嵌入代码(iframe)粘贴到此处进行学习。</div>
                <a href="https://www.douyin.com/search/单词" target="_blank" class="video-link-btn">去抖音搜索"单词"</a>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p>点击左侧按钮，体验"分散"之美。</p></div>
            
            <div id="dispersal-game" class="activity-wrapper">
                <div class="game-container"><canvas id="dispersal-canvas"></canvas></div>
                <button class="control-button" id="disperse-btn">吹散</button>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');
        let currentCleanup = null;

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                if(currentCleanup) currentCleanup();
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                const targetId = btn.dataset.activity;
                document.getElementById(targetId)?.classList.add('active');
                
                if (targetId === 'dispersal-game') currentCleanup = setupDispersalGame();
                else currentCleanup = null;
            });
        });

        function setupDispersalGame() {
            const canvas = document.getElementById('dispersal-canvas');
            const btn = document.getElementById('disperse-btn');
            if (!canvas) return null;

            const ctx = canvas.getContext('2d');
            let animationId;
            let particles = [];
            let wind = { x: 0.5, y: -0.2 };
            
            function resizeCanvas() {
                const container = canvas.parentElement;
                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;
            }

            class Particle { // Dandelion seed
                constructor(x, y) {
                    this.x = x; this.y = y;
                    this.vx = (Math.random() - 0.5) * 2;
                    this.vy = (Math.random() - 2);
                    this.radius = Math.random() * 2 + 1;
                    this.color = `rgba(255, 248, 225, ${Math.random() * 0.5 + 0.5})`; // Light yellow
                    this.life = 100 + Math.random() * 100;
                }
                update() {
                    this.life--;
                    this.vx += wind.x / 10;
                    this.vy += wind.y / 10;
                    this.x += this.vx;
                    this.y += this.vy;
                }
                draw() {
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            function drawDandelion() {
                const x = canvas.width / 2;
                const y = canvas.height - 50;
                // Stem
                ctx.strokeStyle = '#4caf50';
                ctx.lineWidth = 5;
                ctx.beginPath();
                ctx.moveTo(x, canvas.height);
                ctx.lineTo(x, y);
                ctx.stroke();
                // Head
                ctx.fillStyle = '#fffde7';
                ctx.beginPath();
                ctx.arc(x, y, 30, 0, Math.PI * 2);
                ctx.fill();
            }

            function init() {
                resizeCanvas();
                particles = [];
                drawDandelion();
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawDandelion();
                
                particles.forEach((p, index) => {
                    p.update();
                    p.draw();
                    if (p.life <= 0 || p.y < 0 || p.x < 0 || p.x > canvas.width) {
                        particles.splice(index, 1);
                    }
                });
                animationId = requestAnimationFrame(animate);
            }

            btn.onclick = () => {
                const startX = canvas.width / 2;
                const startY = canvas.height - 80;
                for (let i = 0; i < 100; i++) {
                    particles.push(new Particle(startX, startY));
                }
            };

            init();
            animate();
            
            const cleanup = () => {
                if (animationId) cancelAnimationFrame(animationId);
            };
            return cleanup;
        }
        
        // Activate default if any
        if(document.querySelector('.morpheme-btn.active')) {
            document.querySelector('.morpheme-btn.active').click();
        }
    });
    </script>
</body>
</html> 