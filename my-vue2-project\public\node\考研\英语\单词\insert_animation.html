<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Insert</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            background: #f0f2f5;
            color: #333;
        }
        #canvas-container {
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            background: #fff;
        }
        canvas {
            display: block;
        }
        #info-panel {
            margin-top: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 800px;
            text-align: center;
        }
        h1 {
            color: #007bff;
        }
        p {
            font-size: 1.1em;
            line-height: 1.6;
        }
        #controls {
            margin-top: 15px;
        }
        button {
            padding: 10px 20px;
            font-size: 1em;
            color: #fff;
            background-color: #007bff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin: 0 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>单词故事：Insert (插入)</h1>
    <div id="canvas-container">
        <canvas id="wordCanvas" width="800" height="400"></canvas>
    </div>
    <div id="info-panel">
        <p id="explanation">大家好！今天我们学习的单词是 "insert"。它由前缀 "in-" 和词根 "-sert" 构成。让我们通过一个动画故事来理解它吧！</p>
    </div>
    <div id="controls">
        <button id="playBtn">播放动画</button>
        <button id="resetBtn">重置</button>
    </div>

    <script>
        const canvas = document.getElementById('wordCanvas');
        const ctx = canvas.getContext('2d');
        const playBtn = document.getElementById('playBtn');
        const resetBtn = document.getElementById('resetBtn');
        const explanation = document.getElementById('explanation');

        let animationFrameId;
        let scene = 0; // 0: initial, 1: word, 2: split, 3: 'in' demo, 4: 'sert' demo, 5: combine, 6: interactive, 7: final
        let progress = 0;
        
        // Interactive part variables
        let draggable = { x: 100, y: 200, width: 50, height: 80, color: '#28a745', isDragging: false };
        let dropZone = { x: 600, y: 150, width: 100, height: 120, color: '#ffc107' };
        let isDropped = false;

        function drawInitialState() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawText("Insert", canvas.width / 2, canvas.height / 2, 80, 'black');
            explanation.textContent = '故事开始：单词 "insert"。意思是"插入"。';
        }

        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            switch (scene) {
                case 1: // Show word
                    drawText("Insert", canvas.width / 2, canvas.height / 2, 80, `rgba(0, 0, 0, ${progress})`);
                    break;
                case 2: // Split word
                    let offset = 80 * progress;
                    drawText("in", canvas.width / 2 - 60 - offset, canvas.height / 2, 80, 'black');
                    drawText("sert", canvas.width / 2 + 80 + offset, canvas.height / 2, 80, 'black');
                    break;
                case 3: // 'in' animation
                    let box = { x: 450, y: 150, w: 200, h: 150 };
                    let block = { x: 150 + 200 * progress, y: 190, w: 50, h: 50 };
                    drawText("in- (前缀) = '进入'", canvas.width / 2, 80, 40, '#007bff');
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(box.x, box.y, box.w, box.h);
                    ctx.fillStyle = '#28a745';
                    ctx.fillRect(block.x, block.y, block.w, block.h);
                    break;
                case 4: // 'sert' animation
                    drawText("sert (词根) = '放置，加入'", canvas.width / 2, 80, 40, '#dc3545');
                    for (let i = 0; i < 3; i++) {
                        let y = 180;
                        let startX = 250;
                        let spacing = 100;
                        let moveDist = 50 * (1 - progress);
                        ctx.fillStyle = `hsl(${i * 60}, 70%, 60%)`;
                        ctx.fillRect(startX + i * spacing - moveDist, y, 80, 80);
                    }
                    break;
                case 5: // Combine animation
                    let finalBox = { x: 500, y: 150, w: 150, h: 150 };
                    let finalBlock = { x: 200 + 320 * progress, y: 185, w: 80, h: 80 };
                     drawText("in + sert = insert (放入)", canvas.width / 2, 80, 40, '#17a2b8');
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(finalBox.x, finalBox.y, finalBox.w, finalBox.h);
                     // Draw arrow on box
                    ctx.fillStyle = '#333';
                    ctx.beginPath();
                    ctx.moveTo(finalBox.x + 50, finalBox.y - 40);
                    ctx.lineTo(finalBox.x + 100, finalBox.y - 40);
                    ctx.lineTo(finalBox.x + 75, finalBox.y - 10);
                    ctx.closePath();
                    ctx.fill();

                    ctx.fillStyle = '#28a745';
                    ctx.fillRect(finalBlock.x, finalBlock.y, finalBlock.w, finalBlock.h);
                    break;
                case 6: // Interactive
                    drawText("互动环节：请把左侧的卡片插入右侧的插槽中", canvas.width/2, 50, 25, '#6c757d');
                    // Draw Drop Zone (Slot Machine)
                    ctx.fillStyle = dropZone.color;
                    ctx.fillRect(dropZone.x, dropZone.y, dropZone.width, dropZone.height);
                    ctx.fillStyle = '#333';
                    ctx.fillRect(dropZone.x - 10, dropZone.y-10, dropZone.width + 20, dropZone.height + 20);
                    ctx.fillStyle = dropZone.color;
                    ctx.fillRect(dropZone.x, dropZone.y, dropZone.width, dropZone.height);
                    ctx.fillStyle = '#fff';
                    ctx.fillRect(dropZone.x + 10, dropZone.y, dropZone.width-20, 15);
                     ctx.font = 'bold 20px sans-serif';
                    ctx.fillStyle = '#333';
                    ctx.fillText('INSERT CARD', dropZone.x + 2, dropZone.y - 20);


                    // Draw Draggable
                    if (!isDropped) {
                        ctx.fillStyle = draggable.color;
                        ctx.fillRect(draggable.x, draggable.y, draggable.width, draggable.height);
                        ctx.fillStyle = '#fff';
                        ctx.font = '12px sans-serif';
                        ctx.fillText('CARD', draggable.x + 10, draggable.y + 45);
                    }
                     if(isDropped) {
                        drawText("太棒了！你学会了 'insert'！", canvas.width / 2, canvas.height - 30, 30, 'green');
                    }
                    break;
            }
        }
        
        function drawText(text, x, y, size, color) {
            ctx.font = `bold ${size}px Arial`;
            ctx.fillStyle = color;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
        }

        function animate() {
            progress += 0.01;
            if (progress > 1) {
                progress = 0;
                scene++;
                updateExplanation();
            }

            if (scene > 6) {
                cancelAnimationFrame(animationFrameId);
                playBtn.disabled = true;
                return;
            }
            
            drawScene();
            animationFrameId = requestAnimationFrame(animate);
        }
        
        function updateExplanation() {
            switch (scene) {
                case 1: explanation.textContent = "首先，我们看到了完整的单词 'insert'。"; break;
                case 2: explanation.textContent = "然后，它分解为前缀 'in-' 和词根 'sert'。"; break;
                case 3: explanation.textContent = "前缀 'in-' 的意思是 '进入' 或 '向内'。就像这个方块进入了容器一样。"; break;
                case 4: explanation.textContent = "词根 'sert' 源自拉丁语，意思是 '放置' 或 '加入'。看，这些方块被放置在了一起。"; break;
                case 5: explanation.textContent = "把它们合起来：'in-'(进入) + 'sert'(放置) = 'insert' (插入/放入)。"; break;
                case 6: explanation.textContent = "现在轮到你了！请用鼠标将左边的绿色卡片拖动到右边的插槽中，完成 'insert' 动作。"; playBtn.disabled = true; break;
            }
        }

        function reset() {
            cancelAnimationFrame(animationFrameId);
            scene = 0;
            progress = 0;
            isDropped = false;
            draggable.x = 100;
            draggable.y = 200;
            drawInitialState();
            playBtn.disabled = false;
            explanation.textContent = "大家好！今天我们学习的单词是 'insert'。它由前缀 'in-' 和词根 '-sert' 构成。让我们通过一个动画故事来理解它吧！";
        }

        playBtn.addEventListener('click', () => {
            if (scene === 0) {
                scene = 1;
                progress = 0;
                updateExplanation();
                animate();
                playBtn.disabled = true;
            }
        });

        resetBtn.addEventListener('click', reset);

        // --- Interactivity Logic ---
        let offsetX, offsetY;

        canvas.addEventListener('mousedown', (e) => {
            if (scene !== 6 || isDropped) return;
            const mousePos = getMousePos(e);
            if (isInside(mousePos, draggable)) {
                draggable.isDragging = true;
                offsetX = mousePos.x - draggable.x;
                offsetY = mousePos.y - draggable.y;
            }
        });

        canvas.addEventListener('mousemove', (e) => {
            if (scene !== 6 || !draggable.isDragging) return;
            const mousePos = getMousePos(e);
            draggable.x = mousePos.x - offsetX;
            draggable.y = mousePos.y - offsetY;
            drawScene(); // Redraw while dragging
        });

        canvas.addEventListener('mouseup', (e) => {
            if (scene !== 6 || !draggable.isDragging) return;
            draggable.isDragging = false;
            const mousePos = getMousePos(e);
            if (isInside(mousePos, dropZone)) {
                isDropped = true;
                draggable.x = dropZone.x + (dropZone.width - draggable.width)/2;
                draggable.y = dropZone.y + 10;
                explanation.textContent = "非常棒！你成功地 'insert' 了卡片。这就是 '插入' 的意思。例如：'Please insert your credit card.' (请插入您的信用卡)";
                drawScene();
            } else {
                 // Snap back
                draggable.x = 100;
                draggable.y = 200;
                drawScene();
            }
        });

        function getMousePos(evt) {
            const rect = canvas.getBoundingClientRect();
            return {
                x: evt.clientX - rect.left,
                y: evt.clientY - rect.top
            };
        }

        function isInside(pos, rect) {
            return pos.x > rect.x && pos.x < rect.x + rect.width && pos.y < rect.y + rect.height && pos.y > rect.y;
        }

        // Initial draw
        window.onload = () => {
            drawInitialState();
        };
    </script>
</body>
</html> 