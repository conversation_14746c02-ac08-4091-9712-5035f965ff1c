<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象单元测试详解</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .question-section {
            margin-bottom: 25px;
        }
        .question-text {
            font-size: 1.25em;
            margin-bottom: 20px;
            background-color: #e8f5e9;
            padding: 15px;
            border-left: 5px solid #4CAF50;
            border-radius: 5px;
        }
        .options-list {
            list-style: none;
            padding: 0;
        }
        .options-list li {
            margin-bottom: 10px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, border-color 0.3s ease;
            display: flex;
            align-items: center;
        }
        .options-list li:hover {
            background-color: #eef;
            border-color: #a0a0a0;
        }
        .options-list li.selected {
            background-color: #d1ecf1;
            border-color: #007bff;
            font-weight: bold;
        }
        .options-list li.correct {
            background-color: #d4edda;
            border-color: #28a745;
            font-weight: bold;
        }
        .options-list li.incorrect {
            background-color: #f8d7da;
            border-color: #dc3545;
        }
        .option-label {
            display: inline-block;
            width: 25px;
            height: 25px;
            line-height: 25px;
            text-align: center;
            border: 1px solid #aaa;
            border-radius: 50%;
            margin-right: 15px;
            background-color: #f0f0f0;
            color: #555;
        }
        .options-list li.selected .option-label {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .options-list li.correct .option-label {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }
        .options-list li.incorrect .option-label {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        .submit-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1em;
            transition: background-color 0.3s ease;
            margin-top: 20px;
        }
        .submit-btn:hover {
            background-color: #0056b3;
        }
        .result-message {
            margin-top: 15px;
            font-size: 1.1em;
            font-weight: bold;
            text-align: center;
        }
        .correct-answer-display {
            margin-top: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-left: 5px solid #6c757d;
            border-radius: 5px;
        }
        .explanation-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px dashed #ccc;
        }
        .explanation-point {
            margin-bottom: 20px;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #eee;
            position: relative;
        }
        .explanation-point h3 {
            color: #28a745;
            margin-top: 0;
            margin-bottom: 10px;
        }
        .explanation-point p {
            margin-bottom: 10px;
        }
        .explanation-point button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin-top: 10px;
        }
        .explanation-point button:hover {
            background-color: #5a6268;
        }
        .canvas-area {
            margin-top: 20px;
            text-align: center;
        }
        canvas {
            border: 2px solid #a0a0a0;
            background-color: #fdfdfd;
            display: block; /* Centers the canvas */
            margin: 0 auto;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>面向对象单元测试题目解析</h1>

        <div class="question-section">
            <h2>题目</h2>
            <p class="question-text">面向对象系统的单元测试包括方法层次的测试、类层次的测试和类树层次的测试。在常见的测试技术中，(<span id="placeholder1"></span>) 属于方法层次的测试，(<span id="placeholder2"></span>) 属于类层次的测试。</p>

            <ul class="options-list" id="options">
                <li data-value="A">
                    <span class="option-label">A</span>等价类划分测试和多态消息测试
                </li>
                <li data-value="B">
                    <span class="option-label">B</span>不变式边界测试和递归函数测试
                </li>
                <li data-value="C">
                    <span class="option-label">C</span>组合功能测试和非模态类测试
                </li>
                <li data-value="D">
                    <span class="option-label">D</span>不变式边界测试和模态类测试
                </li>
            </ul>
            <button class="submit-btn" id="submitAnswer">提交答案</button>
            <div class="result-message" id="resultMessage"></div>
            <div class="correct-answer-display" id="correctAnswerDisplay" style="display: none;">
                正确答案：<span id="correctAnswer">A</span>
            </div>
        </div>

        <div class="explanation-section">
            <h2>知识点解析与交互演示</h2>
            <p>通过下面的内容，我们将深入理解面向对象单元测试的各个层次。</p>

            <div class="explanation-point">
                <h3>1. 方法层次的测试 (Method-level testing)</h3>
                <p>方法层次的测试类似于传统软件中对单个函数的测试。它关注的是对象内部的单个方法是否按照预期工作。</p>
                <p>常见的测试技术包括：<strong>等价类划分测试</strong>、<strong>组合功能测试</strong>、<strong>递归函数测试</strong>和<strong>多态消息测试</strong>。</p>
                <button onclick="startMethodLevelDemo()">开始演示方法层次测试</button>
                <div class="canvas-area">
                    <canvas id="methodCanvas" width="600" height="200"></canvas>
                </div>
            </div>

            <div class="explanation-point">
                <h3>2. 类层次的测试 (Class-level testing)</h3>
                <p>类层次的测试关注的是单个类作为一个整体是否正确。这包括类的属性、方法以及它们之间的相互作用。</p>
                <p>主要包括：<strong>不变式边界测试</strong>、<strong>模态类测试</strong>和<strong>非模态类测试</strong>。</p>
                <button onclick="startClassLevelDemo()">开始演示类层次测试</button>
                <div class="canvas-area">
                    <canvas id="classCanvas" width="600" height="250"></canvas>
                </div>
            </div>

            <div class="explanation-point">
                <h3>3. 类树层次的测试 (Class-tree level testing)</h3>
                <p>类树层次的测试涉及继承、多态等面向对象特性。它关注的是类之间，特别是父类和子类之间的交互是否正确。</p>
                <p>主要包括：<strong>多态服务测试</strong>和<strong>展平测试</strong>。</p>
                <button onclick="startClassTreeLevelDemo()">开始演示类树层次测试</button>
                <div class="canvas-area">
                    <canvas id="treeCanvas" width="600" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        const optionsList = document.getElementById('options');
        const submitButton = document.getElementById('submitAnswer');
        const resultMessage = document.getElementById('resultMessage');
        const correctAnswerDisplay = document.getElementById('correctAnswerDisplay');
        const correctAnswerSpan = document.getElementById('correctAnswer');
        const placeholder1 = document.getElementById('placeholder1');
        const placeholder2 = document.getElementById('placeholder2');

        const questionInfo = {
            correctOption: 'A',
            placeholder1Text: '等价类划分测试',
            placeholder2Text: '多态消息测试'
        };

        /*
        关于题目和答案的分析：
        原始题目：面向对象系统的单元测试包括方法层次的测试、类层次的测试和类树层次的测试。在常见的测试技术中，() 属于方法层次的测试，() 属于类层次的测试。
        正确答案：A (等价类划分测试和多态消息测试)

        题目解析中给出的定义：
        (1) 方法层次的测试：等价类划分测试、组合功能测试、递归函数测试和多态消息测试等。
        (2) 类层次的测试：不变式边界测试、模态类测试和非模态类测试。
        (3) 类树层次的测试：多态服务测试和展平测试。

        从解析(1)可知，"等价类划分测试" 属于方法层次的测试。
        从解析(1)可知，"多态消息测试" 也属于方法层次的测试。

        这意味着，选项 A 提供了两个方法层次的测试。然而，题目要求第一个空是“方法层次的测试”，第二个空是“类层次的测试”。
        这与选项 A 和题目解析之间存在一个小的矛盾点。

        可能的原因：
        1. 题目本身在表述上略有歧义，或者选项 A 是在多选题中“最合适”的答案，即使它不完全符合两个空分别对应“方法层次”和“类层次”的严格要求。
        2. "多态消息测试" 在某些广义的语境下，可能也与类之间交互（尤其涉及到多态性时）相关联，但在本题提供的解析中，它被明确归为“方法层次”。

        为了提供最符合题目要求和其提供的正确答案的演示：
        - 我会按照提供的正确答案 A，将第一个空填充为“等价类划分测试”，第二个空填充为“多态消息测试”。
        - 但在下方对知识点的具体解释部分，我会严格按照题目解析中对“方法层次”、“类层次”和“类树层次”的定义进行说明，并指出“多态消息测试”在解析中是被归类为方法层次的。
        */

        let selectedOption = null;

        optionsList.addEventListener('click', (event) => {
            const target = event.target.closest('li');
            if (target && !target.classList.contains('correct') && !target.classList.contains('incorrect')) {
                if (selectedOption) {
                    selectedOption.classList.remove('selected');
                }
                selectedOption = target;
                selectedOption.classList.add('selected');
            }
        });

        submitButton.addEventListener('click', () => {
            if (!selectedOption) {
                resultMessage.textContent = '请选择一个答案！';
                resultMessage.style.color = '#dc3545';
                return;
            }

            // Reset previous states
            Array.from(optionsList.children).forEach(li => {
                li.classList.remove('selected', 'correct', 'incorrect');
            });

            if (selectedOption.dataset.value === questionInfo.correctOption) {
                selectedOption.classList.add('correct');
                resultMessage.textContent = '恭喜你，回答正确！';
                resultMessage.style.color = '#28a745';
                placeholder1.textContent = questionInfo.placeholder1Text;
                placeholder2.textContent = questionInfo.placeholder2Text;
                correctAnswerDisplay.style.display = 'block';
            } else {
                selectedOption.classList.add('incorrect');
                resultMessage.textContent = '很遗憾，回答错误。';
                resultMessage.style.color = '#dc3545';
                correctAnswerDisplay.style.display = 'block';
                // Show the correct one
                document.querySelector(`li[data-value="${questionInfo.correctOption}"]`).classList.add('correct');
                placeholder1.textContent = questionInfo.placeholder1Text;
                placeholder2.textContent = questionInfo.placeholder2Text;
            }
        });

        // --- Canvas Animations (Placeholder Functions) ---
        const methodCanvas = document.getElementById('methodCanvas');
        const methodCtx = methodCanvas.getContext('2d');

        function startMethodLevelDemo() {
            methodCtx.clearRect(0, 0, methodCanvas.width, methodCanvas.height);
            methodCtx.font = '16px Arial';
            methodCtx.textAlign = 'center';
            methodCtx.textBaseline = 'middle';

            let step = 0;
            const maxSteps = 4; // Intro, Equivalence Partitioning, Polymorphic Message, End

            function drawMethodDemo() {
                methodCtx.clearRect(0, 0, methodCanvas.width, methodCanvas.height);
                let text = '';
                let boxColor = '#ADD8E6'; // Light blue
                let textColor = '#000';

                // Simple function box
                methodCtx.fillStyle = '#ADD8E6';
                methodCtx.fillRect(methodCanvas.width / 2 - 50, 20, 100, 50);
                methodCtx.strokeRect(methodCanvas.width / 2 - 50, 20, 100, 50);
                methodCtx.fillStyle = '#333';
                methodCtx.fillText('方法 (Function)', methodCanvas.width / 2, 45);

                switch (step) {
                    case 0:
                        text = '方法层次测试：针对单个函数/方法';
                        break;
                    case 1:
                        text = '等价类划分：输入划分为有效/无效等价类';
                        methodCtx.fillStyle = '#f0e68c'; // Khaki
                        methodCtx.fillRect(methodCanvas.width / 2 - 120, 90, 240, 40);
                        methodCtx.strokeRect(methodCanvas.width / 2 - 120, 90, 240, 40);
                        methodCtx.fillStyle = '#333';
                        methodCtx.fillText('输入: [1..100] -> 有效/无效', methodCanvas.width / 2, 110);
                        break;
                    case 2:
                        text = '多态消息：不同对象响应相同消息';
                        // Simulate two objects receiving a message
                        methodCtx.fillStyle = '#dda0dd'; // Plum
                        methodCtx.fillRect(methodCanvas.width / 2 - 180, 90, 100, 40);
                        methodCtx.strokeRect(methodCanvas.width / 2 - 180, 90, 100, 40);
                        methodCtx.fillStyle = '#333';
                        methodCtx.fillText('对象A', methodCanvas.width / 2 - 130, 110);

                        methodCtx.fillStyle = '#dda0dd';
                        methodCtx.fillRect(methodCanvas.width / 2 + 80, 90, 100, 40);
                        methodCtx.strokeRect(methodCanvas.width / 2 + 80, 90, 100, 40);
                        methodCtx.fillStyle = '#333';
                        methodCtx.fillText('对象B', methodCanvas.width / 2 + 130, 110);

                        methodCtx.beginPath();
                        methodCtx.moveTo(methodCanvas.width / 2 - 50, 70);
                        methodCtx.lineTo(methodCanvas.width / 2 - 130, 90);
                        methodCtx.strokeStyle = 'green';
                        methodCtx.stroke();
                        methodCtx.fillText('消息()', methodCanvas.width / 2 - 90, 80);

                        methodCtx.beginPath();
                        methodCtx.moveTo(methodCanvas.width / 2 + 50, 70);
                        methodCtx.lineTo(methodCanvas.width / 2 + 130, 90);
                        methodCtx.strokeStyle = 'green';
                        methodCtx.stroke();
                        methodCtx.fillText('消息()', methodCanvas.width / 2 + 90, 80);
                        break;
                    case 3:
                        text = '方法层次测试演示结束。';
                        break;
                }
                methodCtx.fillStyle = textColor;
                methodCtx.fillText(text, methodCanvas.width / 2, methodCanvas.height - 30);
            }

            function animateMethodDemo() {
                if (step < maxSteps) {
                    drawMethodDemo();
                    step++;
                    setTimeout(animateMethodDemo, 2000); // Advance every 2 seconds
                }
            }
            animateMethodDemo();
        }

        const classCanvas = document.getElementById('classCanvas');
        const classCtx = classCanvas.getContext('2d');

        function startClassLevelDemo() {
            classCtx.clearRect(0, 0, classCanvas.width, classCanvas.height);
            classCtx.font = '16px Arial';
            classCtx.textAlign = 'center';
            classCtx.textBaseline = 'middle';

            let step = 0;
            const maxSteps = 4; // Intro, Invariant Boundary, Modal/Non-Modal, End

            function drawClassDemo() {
                classCtx.clearRect(0, 0, classCanvas.width, classCanvas.height);
                let text = '';
                let boxColor = '#FFB6C1'; // Light pink for Class

                // Class box
                classCtx.fillStyle = boxColor;
                classCtx.fillRect(methodCanvas.width / 2 - 70, 20, 140, 60);
                classCtx.strokeRect(methodCanvas.width / 2 - 70, 20, 140, 60);
                classCtx.fillStyle = '#333';
                classCtx.fillText('类 (Class)', methodCanvas.width / 2, 35);
                classCtx.fillText('属性, 方法', methodCanvas.width / 2, 60);


                switch (step) {
                    case 0:
                        text = '类层次测试：关注类整体及其成员';
                        break;
                    case 1:
                        text = '不变式边界测试：确保类在操作前后状态不变';
                        classCtx.fillStyle = '#90ee90'; // Light green
                        classCtx.fillRect(methodCanvas.width / 2 - 150, 100, 300, 50);
                        classCtx.strokeRect(methodCanvas.width / 2 - 150, 100, 300, 50);
                        classCtx.fillStyle = '#333';
                        classCtx.fillText('测试: obj.method() -> obj状态是否保持有效', methodCanvas.width / 2, 125);
                        break;
                    case 2:
                        text = '模态/非模态类：测试类在不同状态下的行为';
                        // Simple state transition
                        classCtx.fillStyle = '#add8e6';
                        classCtx.fillRect(methodCanvas.width / 2 - 150, 100, 100, 40);
                        classCtx.strokeRect(methodCanvas.width / 2 - 150, 100, 100, 40);
                        classCtx.fillStyle = '#333';
                        classCtx.fillText('状态 A', methodCanvas.width / 2 - 100, 120);

                        classCtx.fillStyle = '#add8e6';
                        classCtx.fillRect(methodCanvas.width / 2 + 50, 100, 100, 40);
                        classCtx.strokeRect(methodCanvas.width / 2 + 50, 100, 100, 40);
                        classCtx.fillStyle = '#333';
                        classCtx.fillText('状态 B', methodCanvas.width / 2 + 100, 120);

                        classCtx.beginPath();
                        classCtx.moveTo(methodCanvas.width / 2 - 50, 120);
                        classCtx.lineTo(methodCanvas.width / 2 + 50, 120);
                        classCtx.strokeStyle = 'blue';
                        classCtx.stroke();
                        classCtx.fillText('操作', methodCanvas.width / 2, 110);
                        classCtx.arc(methodCanvas.width / 2 + 50, 120, 5, 0, Math.PI * 2);
                        classCtx.fill();
                        break;
                    case 3:
                        text = '类层次测试演示结束。';
                        break;
                }
                classCtx.fillStyle = '#000';
                classCtx.fillText(text, classCanvas.width / 2, classCanvas.height - 30);
            }

            function animateClassDemo() {
                if (step < maxSteps) {
                    drawClassDemo();
                    step++;
                    setTimeout(animateClassDemo, 2000); // Advance every 2 seconds
                }
            }
            animateClassDemo();
        }

        const treeCanvas = document.getElementById('treeCanvas');
        const treeCtx = treeCanvas.getContext('2d');

        function startClassTreeLevelDemo() {
            treeCtx.clearRect(0, 0, treeCanvas.width, treeCanvas.height);
            treeCtx.font = '16px Arial';
            treeCtx.textAlign = 'center';
            treeCtx.textBaseline = 'middle';

            let step = 0;
            const maxSteps = 4; // Intro, Polymorphic Service, Flat Testing, End

            function drawTreeDemo() {
                treeCtx.clearRect(0, 0, treeCanvas.width, treeCtx.height);
                let text = '';
                let boxColor = '#DDA0DD'; // Plum for Inheritance

                // Simple inheritance hierarchy
                // Base Class
                treeCtx.fillStyle = boxColor;
                treeCtx.fillRect(treeCanvas.width / 2 - 50, 20, 100, 50);
                treeCtx.strokeRect(treeCanvas.width / 2 - 50, 20, 100, 50);
                treeCtx.fillStyle = '#333';
                treeCtx.fillText('基类', treeCanvas.width / 2, 45);

                // Arrows
                treeCtx.beginPath();
                treeCtx.moveTo(treeCanvas.width / 2, 70);
                treeCtx.lineTo(treeCanvas.width / 2, 100);
                treeCtx.strokeStyle = '#555';
                treeCtx.stroke();
                treeCtx.beginPath();
                treeCtx.moveTo(treeCanvas.width / 2, 100);
                treeCtx.lineTo(treeCanvas.width / 2 - 60, 130);
                treeCtx.lineTo(treeCanvas.width / 2 + 60, 130);
                treeCtx.closePath();
                treeCtx.fillStyle = '#555'; // Triangle for inheritance
                treeCtx.fill();

                // Derived Classes
                treeCtx.fillStyle = boxColor;
                treeCtx.fillRect(treeCanvas.width / 2 - 120, 150, 100, 50);
                treeCtx.strokeRect(treeCanvas.width / 2 - 120, 150, 100, 50);
                treeCtx.fillStyle = '#333';
                treeCtx.fillText('子类1', treeCanvas.width / 2 - 70, 175);

                treeCtx.fillStyle = boxColor;
                treeCtx.fillRect(treeCanvas.width / 2 + 20, 150, 100, 50);
                treeCtx.strokeRect(treeCanvas.width / 2 + 20, 150, 100, 50);
                treeCtx.fillStyle = '#333';
                treeCtx.fillText('子类2', treeCanvas.width / 2 + 70, 175);


                switch (step) {
                    case 0:
                        text = '类树层次测试：关注继承、多态等特性';
                        break;
                    case 1:
                        text = '多态服务测试：测试通过父类引用调用子类方法';
                        treeCtx.fillStyle = '#ffff00'; // Yellow
                        treeCtx.fillRect(treeCanvas.width / 2 - 180, 220, 360, 40);
                        treeCtx.strokeRect(treeCanvas.width / 2 - 180, 220, 360, 40);
                        treeCtx.fillStyle = '#333';
                        treeCtx.fillText('测试: BaseObj.method() -> 实际执行子类方法', treeCanvas.width / 2, 240);
                        break;
                    case 2:
                        text = '展平测试：将继承层次结构展平为单个类进行测试';
                        treeCtx.fillStyle = '#c0c0c0'; // Silver
                        treeCtx.fillRect(treeCanvas.width / 2 - 100, 220, 200, 40);
                        treeCtx.strokeRect(treeCanvas.width / 2 - 100, 220, 200, 40);
                        treeCtx.fillStyle = '#333';
                        treeCtx.fillText('将所有方法/属性汇总到一起测试', treeCanvas.width / 2, 240);
                        break;
                    case 3:
                        text = '类树层次测试演示结束。';
                        break;
                }
                treeCtx.fillStyle = '#000';
                treeCtx.fillText(text, treeCanvas.width / 2, treeCanvas.height - 30);
            }

            function animateClassTreeDemo() {
                if (step < maxSteps) {
                    drawTreeDemo();
                    step++;
                    setTimeout(animateClassTreeDemo, 2000); // Advance every 2 seconds
                }
            }
            animateClassTreeDemo();
        }

        // Initialize question placeholders
        placeholder1.textContent = '()';
        placeholder2.textContent = '()';

    </script>
</body>
</html> 