<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perspective - 词缀故事动画</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            min-height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }

        .word-panel {
            flex: 1;
            padding: 30px;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-right: 3px solid #6c757d;
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 2.8em;
            color: #2c3e50;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .pronunciation {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
            font-style: italic;
        }

        .details {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .example {
            background: #e8f4fd;
            padding: 15px;
            border-left: 4px solid #3498db;
            margin-top: 15px;
            border-radius: 0 8px 8px 0;
        }

        .breakdown-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .breakdown-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }

        .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            background: linear-gradient(145deg, #6c5ce7, #5a4fcf);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .morpheme-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
            background: linear-gradient(145deg, #5a4fcf, #4834d4);
        }

        .insight {
            margin-top: 15px;
            font-style: italic;
            color: #7f8c8d;
            text-align: center;
        }

        .animation-panel {
            flex: 1;
            padding: 30px;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .activity-title {
            color: white;
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
        }

        .activity-wrapper {
            display: none;
            width: 100%;
            height: 100%;
            text-align: center;
        }

        .activity-wrapper.active {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .activity-wrapper p {
            color: white;
            font-size: 1.2em;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .game-container {
            width: 100%;
            height: 400px;
            position: relative;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .control-button {
            background: linear-gradient(145deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            margin: 5px;
        }

        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        }

        .perspective-canvas {
            width: 100%;
            height: 100%;
            border-radius: 15px;
        }

        .story-text {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            line-height: 1.4;
        }

        .viewpoint-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(255,255,255,0.9);
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>perspective</h1>
            <div class="pronunciation">/pərˈspektɪv/</div>
            
            <div class="details">
                <h3>📖 基本含义</h3>
                <p><strong>名词：</strong>观点，视角，透视法</p>
                <p><strong>形容词：</strong>透视的，有洞察力的</p>
                
                <div class="example">
                    <strong>考研例句：</strong><br>
                    "From a historical perspective, this event marked a turning point."<br>
                    <em>从历史的角度来看，这个事件标志着一个转折点。</em>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>🔍 词缀拆解</h3>
                <p><strong>per-</strong> (通过) + <strong>spect</strong> (看) + <strong>-ive</strong> (形容词后缀)</p>
                <p>💡 <strong>记忆口诀：</strong>"通过看"得到的就是"观点"</p>
            </div>

            <div class="breakdown-section">
                <h3>🎭 互动故事体验</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="per-story">per- 故事</button>
                    <button class="morpheme-btn" data-activity="spect-story">spect 故事</button>
                    <button class="morpheme-btn" data-activity="ive-story">-ive 故事</button>
                </div>
                <p class="insight">点击词缀，体验生动的故事动画！</p>
            </div>
            
            <div class="breakdown-section">
                <h3>🎨 完整单词动画</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">观点转换</button>
                    <button class="morpheme-btn" data-activity="perspective-demo">透视演示</button>
                </div>
            </div>
        </div>

        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">选择一个故事开始学习</h2>
            
            <!-- per- 故事 -->
            <div id="per-story" class="activity-wrapper">
                <p>🚶‍♂️ 前缀 "per-" 的故事：穿越之旅</p>
                <div class="game-container">
                    <canvas id="per-canvas" class="perspective-canvas"></canvas>
                    <div class="story-text" id="per-text">点击开始，看小人如何"穿越"障碍！</div>
                </div>
                <button class="control-button" id="per-btn">开始穿越</button>
            </div>

            <!-- spect 故事 -->
            <div id="spect-story" class="activity-wrapper">
                <p>👁️ 词根 "spect" 的故事：观察者的眼睛</p>
                <div class="game-container">
                    <canvas id="spect-canvas" class="perspective-canvas"></canvas>
                    <div class="story-text" id="spect-text">点击开始，体验"观看"的力量！</div>
                </div>
                <button class="control-button" id="spect-btn">开始观察</button>
            </div>

            <!-- -ive 故事 -->
            <div id="ive-story" class="activity-wrapper">
                <p>⚡ 后缀 "-ive" 的故事：魔法变身</p>
                <div class="game-container">
                    <canvas id="ive-canvas" class="perspective-canvas"></canvas>
                    <div class="story-text" id="ive-text">点击开始，看词根如何变成形容词！</div>
                </div>
                <button class="control-button" id="ive-btn">开始变身</button>
            </div>

            <!-- 完整动画 -->
            <div id="full-animation" class="activity-wrapper">
                <p>🌟 完整故事：perspective 的诞生</p>
                <div class="game-container">
                    <canvas id="full-canvas" class="perspective-canvas"></canvas>
                    <div class="story-text" id="full-text">三个词缀将合体，形成完整的"观点"！</div>
                    <div class="viewpoint-indicator" id="viewpoint">观点 1</div>
                </div>
                <button class="control-button" id="full-btn">开始合体</button>
            </div>

            <!-- 透视演示 -->
            <div id="perspective-demo" class="activity-wrapper">
                <p>🎨 透视法演示：不同角度看世界</p>
                <div class="game-container">
                    <canvas id="demo-canvas" class="perspective-canvas"></canvas>
                    <div class="story-text" id="demo-text">点击切换视角，体验perspective的真正含义！</div>
                </div>
                <button class="control-button" id="demo-btn">切换视角</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentActivity = null;
        let animationFrameId = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            setupEventListeners();
        });

        function setupEventListeners() {
            // 词缀按钮事件
            document.querySelectorAll('.morpheme-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const activity = e.target.dataset.activity;
                    showActivity(activity);
                });
            });
        }

        function showActivity(activityName) {
            // 隐藏所有活动
            document.querySelectorAll('.activity-wrapper').forEach(wrapper => {
                wrapper.classList.remove('active');
            });

            // 显示选中的活动
            const targetWrapper = document.getElementById(activityName);
            if (targetWrapper) {
                targetWrapper.classList.add('active');
                currentActivity = activityName;
                
                // 停止之前的动画
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                }

                // 初始化对应的动画
                initActivity(activityName);
            }
        }

        function initActivity(activityName) {
            switch(activityName) {
                case 'per-story':
                    initPerStory();
                    break;
                case 'spect-story':
                    initSpectStory();
                    break;
                case 'ive-story':
                    initIveStory();
                    break;
                case 'full-animation':
                    initFullAnimation();
                    break;
                case 'perspective-demo':
                    initPerspectiveDemo();
                    break;
            }
        }

        // per- 故事动画
        function initPerStory() {
            const canvas = document.getElementById('per-canvas');
            const ctx = canvas.getContext('2d');
            const textDiv = document.getElementById('per-text');
            const btn = document.getElementById('per-btn');
            
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let character = { x: 50, y: canvas.height - 100, size: 30 };
            let obstacles = [
                { x: 200, y: canvas.height - 150, width: 20, height: 100 },
                { x: 350, y: canvas.height - 120, width: 20, height: 70 },
                { x: 500, y: canvas.height - 180, width: 20, height: 130 }
            ];
            let isMoving = false;
            let currentObstacle = 0;

            function drawScene() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制地面
                ctx.fillStyle = '#8B4513';
                ctx.fillRect(0, canvas.height - 50, canvas.width, 50);
                
                // 绘制障碍物
                ctx.fillStyle = '#654321';
                obstacles.forEach(obstacle => {
                    ctx.fillRect(obstacle.x, obstacle.y, obstacle.width, obstacle.height);
                });
                
                // 绘制角色
                ctx.fillStyle = '#FF6B6B';
                ctx.beginPath();
                ctx.arc(character.x, character.y, character.size/2, 0, Math.PI * 2);
                ctx.fill();
                
                // 绘制眼睛
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(character.x - 8, character.y - 5, 5, 0, Math.PI * 2);
                ctx.arc(character.x + 8, character.y - 5, 5, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = 'black';
                ctx.beginPath();
                ctx.arc(character.x - 8, character.y - 5, 2, 0, Math.PI * 2);
                ctx.arc(character.x + 8, character.y - 5, 2, 0, Math.PI * 2);
                ctx.fill();

                // 绘制 "per-" 文字
                ctx.fillStyle = '#4ECDC4';
                ctx.font = 'bold 24px Arial';
                ctx.fillText('per-', character.x - 20, character.y - 40);
            }

            function animate() {
                if (!isMoving) return;

                if (currentObstacle < obstacles.length) {
                    const target = obstacles[currentObstacle];
                    
                    if (character.x < target.x - 30) {
                        character.x += 2;
                        textDiv.textContent = `小人正在向障碍物${currentObstacle + 1}前进...`;
                    } else {
                        // 穿越障碍物
                        character.x += 3;
                        character.y = target.y - 20; // 跳过障碍物
                        textDiv.textContent = `太棒了！小人"穿越(per-)"了障碍物${currentObstacle + 1}！`;
                        
                        if (character.x > target.x + target.width + 30) {
                            character.y = canvas.height - 100; // 落地
                            currentObstacle++;
                        }
                    }
                } else {
                    if (character.x < canvas.width - 50) {
                        character.x += 2;
                        textDiv.textContent = '小人成功穿越了所有障碍！这就是"per-"(通过)的含义！';
                    } else {
                        isMoving = false;
                        btn.textContent = '重新开始';
                        textDiv.textContent = '🎉 完成！"per-"表示"通过、穿越"，就像小人穿越障碍一样！';
                        return;
                    }
                }

                drawScene();
                animationFrameId = requestAnimationFrame(animate);
            }

            btn.onclick = () => {
                if (!isMoving) {
                    character.x = 50;
                    character.y = canvas.height - 100;
                    currentObstacle = 0;
                    isMoving = true;
                    btn.textContent = '穿越中...';
                    animate();
                }
            };

            drawScene();
        }

        // spect 故事动画
        function initSpectStory() {
            const canvas = document.getElementById('spect-canvas');
            const ctx = canvas.getContext('2d');
            const textDiv = document.getElementById('spect-text');
            const btn = document.getElementById('spect-btn');
            
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let eye = { x: canvas.width/2, y: canvas.height/2, size: 60 };
            let objects = [
                { x: 100, y: 100, type: '🌟', discovered: false },
                { x: 300, y: 150, type: '🎯', discovered: false },
                { x: 500, y: 200, type: '🔍', discovered: false },
                { x: 200, y: 300, type: '💎', discovered: false }
            ];
            let isWatching = false;
            let currentTarget = 0;
            let rayAngle = 0;

            function drawScene() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景
                ctx.fillStyle = '#2C3E50';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 绘制物体
                objects.forEach((obj, index) => {
                    ctx.font = '30px Arial';
                    if (obj.discovered) {
                        ctx.fillStyle = '#F39C12';
                        ctx.fillText('✨', obj.x - 15, obj.y - 15);
                    }
                    ctx.fillStyle = obj.discovered ? '#E74C3C' : '#7F8C8D';
                    ctx.fillText(obj.type, obj.x, obj.y);
                });
                
                // 绘制眼睛
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(eye.x, eye.y, eye.size, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = '#3498DB';
                ctx.beginPath();
                ctx.arc(eye.x, eye.y, eye.size * 0.6, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = 'black';
                ctx.beginPath();
                ctx.arc(eye.x, eye.y, eye.size * 0.3, 0, Math.PI * 2);
                ctx.fill();

                // 绘制观察射线
                if (isWatching && currentTarget < objects.length) {
                    const target = objects[currentTarget];
                    ctx.strokeStyle = '#F1C40F';
                    ctx.lineWidth = 3;
                    ctx.setLineDash([5, 5]);
                    ctx.beginPath();
                    ctx.moveTo(eye.x, eye.y);
                    ctx.lineTo(target.x, target.y);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }

                // 绘制 "spect" 文字
                ctx.fillStyle = '#E74C3C';
                ctx.font = 'bold 28px Arial';
                ctx.fillText('spect', eye.x - 35, eye.y + 100);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText('(看)', eye.x - 15, eye.y + 120);
            }

            function animate() {
                if (!isWatching) return;

                if (currentTarget < objects.length) {
                    const target = objects[currentTarget];
                    
                    // 模拟观察过程
                    rayAngle += 0.1;
                    
                    if (rayAngle > Math.PI) {
                        target.discovered = true;
                        textDiv.textContent = `发现了 ${target.type}！"spect"让我们能够观察和发现！`;
                        currentTarget++;
                        rayAngle = 0;
                        
                        setTimeout(() => {
                            if (currentTarget < objects.length) {
                                textDiv.textContent = `继续观察下一个目标...`;
                            }
                        }, 1000);
                    }
                } else {
                    isWatching = false;
                    btn.textContent = '重新观察';
                    textDiv.textContent = '🎉 太棒了！通过"观察(spect)"，我们发现了所有隐藏的宝物！';
                    return;
                }

                drawScene();
                animationFrameId = requestAnimationFrame(animate);
            }

            btn.onclick = () => {
                if (!isWatching) {
                    objects.forEach(obj => obj.discovered = false);
                    currentTarget = 0;
                    rayAngle = 0;
                    isWatching = true;
                    btn.textContent = '观察中...';
                    textDiv.textContent = '眼睛开始观察，寻找隐藏的宝物...';
                    animate();
                }
            };

            drawScene();
        }

        // -ive 故事动画
        function initIveStory() {
            const canvas = document.getElementById('ive-canvas');
            const ctx = canvas.getContext('2d');
            const textDiv = document.getElementById('ive-text');
            const btn = document.getElementById('ive-btn');
            
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let word = { x: canvas.width/2 - 100, y: canvas.height/2, text: 'perspect', color: '#3498DB' };
            let suffix = { x: canvas.width - 100, y: canvas.height/2, text: '-ive', color: '#E74C3C' };
            let isTransforming = false;
            let particles = [];
            let transformProgress = 0;

            function createParticles() {
                for (let i = 0; i < 20; i++) {
                    particles.push({
                        x: suffix.x + Math.random() * 60,
                        y: suffix.y + Math.random() * 40,
                        vx: (Math.random() - 0.5) * 4,
                        vy: (Math.random() - 0.5) * 4,
                        life: 1,
                        color: `hsl(${Math.random() * 60 + 300}, 70%, 60%)`
                    });
                }
            }

            function drawScene() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景魔法圈
                if (isTransforming) {
                    ctx.strokeStyle = '#9B59B6';
                    ctx.lineWidth = 3;
                    ctx.setLineDash([10, 5]);
                    ctx.beginPath();
                    ctx.arc(canvas.width/2, canvas.height/2, 150 + Math.sin(transformProgress * 0.1) * 20, 0, Math.PI * 2);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }
                
                // 绘制词根
                ctx.fillStyle = word.color;
                ctx.font = 'bold 36px Arial';
                ctx.fillText(word.text, word.x, word.y);
                
                // 绘制后缀
                if (!isTransforming || transformProgress < 100) {
                    ctx.fillStyle = suffix.color;
                    ctx.font = 'bold 36px Arial';
                    ctx.fillText(suffix.text, suffix.x - transformProgress * 2, suffix.y);
                }
                
                // 绘制粒子效果
                particles.forEach(particle => {
                    ctx.fillStyle = particle.color;
                    ctx.globalAlpha = particle.life;
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.globalAlpha = 1;
                });
                
                // 绘制变换后的完整单词
                if (isTransforming && transformProgress > 50) {
                    ctx.fillStyle = '#27AE60';
                    ctx.font = 'bold 42px Arial';
                    const alpha = (transformProgress - 50) / 50;
                    ctx.globalAlpha = alpha;
                    ctx.fillText('perspective', canvas.width/2 - 120, canvas.height/2 + 80);
                    ctx.globalAlpha = 1;
                    
                    // 绘制含义
                    ctx.fillStyle = '#F39C12';
                    ctx.font = '20px Arial';
                    ctx.fillText('(形容词：有观点的)', canvas.width/2 - 80, canvas.height/2 + 110);
                }
            }

            function animate() {
                if (!isTransforming) return;

                transformProgress += 1;
                
                // 更新粒子
                particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    particle.life -= 0.02;
                });
                
                particles = particles.filter(p => p.life > 0);
                
                if (transformProgress < 30) {
                    textDiv.textContent = '魔法后缀"-ive"正在靠近词根...';
                } else if (transformProgress < 60) {
                    textDiv.textContent = '✨ 神奇的变化开始了！"-ive"让词根变成形容词！';
                    if (particles.length < 10) createParticles();
                } else if (transformProgress < 100) {
                    textDiv.textContent = '🌟 变身完成！"perspective"诞生了！';
                } else {
                    isTransforming = false;
                    btn.textContent = '重新变身';
                    textDiv.textContent = '🎉 完美！"-ive"后缀让词根变成了形容词，表示"具有...性质的"！';
                    return;
                }

                drawScene();
                animationFrameId = requestAnimationFrame(animate);
            }

            btn.onclick = () => {
                if (!isTransforming) {
                    transformProgress = 0;
                    particles = [];
                    isTransforming = true;
                    btn.textContent = '变身中...';
                    animate();
                }
            };

            drawScene();
        }

        // 完整动画
        function initFullAnimation() {
            const canvas = document.getElementById('full-canvas');
            const ctx = canvas.getContext('2d');
            const textDiv = document.getElementById('full-text');
            const btn = document.getElementById('full-btn');
            
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let parts = [
                { text: 'per-', x: 50, y: canvas.height/2, targetX: canvas.width/2 - 80, color: '#E74C3C' },
                { text: 'spect', x: canvas.width/2 - 40, y: 100, targetX: canvas.width/2 - 20, targetY: canvas.height/2, color: '#3498DB' },
                { text: '-ive', x: canvas.width - 100, y: canvas.height/2, targetX: canvas.width/2 + 40, color: '#27AE60' }
            ];
            let isAnimating = false;
            let phase = 0;
            let progress = 0;

            function drawScene() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制连接线
                if (phase > 0) {
                    ctx.strokeStyle = '#F39C12';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);
                    parts.forEach((part, i) => {
                        if (i < parts.length - 1) {
                            ctx.beginPath();
                            ctx.moveTo(part.x, part.y);
                            ctx.lineTo(parts[i+1].x, parts[i+1].y);
                            ctx.stroke();
                        }
                    });
                    ctx.setLineDash([]);
                }
                
                // 绘制每个部分
                parts.forEach(part => {
                    ctx.fillStyle = part.color;
                    ctx.font = 'bold 36px Arial';
                    ctx.fillText(part.text, part.x, part.y);
                });
            }

            function animate() {
                if (!isAnimating) return;

                progress += 1;
                
                if (progress < 100) {
                    parts[0].x += (parts[0].targetX - parts[0].x) * 0.1;
                    parts[1].x += (parts[1].targetX - parts[1].x) * 0.1;
                    parts[1].y += (parts[1].targetY - parts[1].y) * 0.1;
                    parts[2].x += (parts[2].targetX - parts[2].x) * 0.1;
                } else {
                    isAnimating = false;
                    btn.textContent = '重新开始';
                    textDiv.textContent = '🎉 完成！"perspective"由"per-"、"spect"和"-ive"三个部分组成！';
                    return;
                }

                drawScene();
                animationFrameId = requestAnimationFrame(animate);
            }

            btn.onclick = () => {
                if (!isAnimating) {
                    progress = 0;
                    isAnimating = true;
                    btn.textContent = '动画中...';
                    animate();
                }
            };

            drawScene();
        }
    </script>
</body>
</html> 
