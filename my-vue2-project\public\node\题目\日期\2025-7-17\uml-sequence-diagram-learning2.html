<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UML顺序图互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .canvas-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        canvas {
            border-radius: 10px;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #28a745;
            background: #d4edda;
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #f8d7da;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            border-left: 5px solid #2196f3;
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        .explanation.show {
            display: block;
        }

        .knowledge-point {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 3px;
            transition: width 0.5s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 UML顺序图学习</h1>
            <p class="subtitle">通过动画和交互理解UML顺序图的核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <h2 class="question-title">
                【软考达人-回忆版】在UML2.0 (Unified Modeling Language)中，顺序图用来描述对象之间的交互，其中循环、选择等复杂交互使用（ ）表示，对象之间的消息类型包括（ ）。
            </h2>

            <div class="canvas-container">
                <canvas id="sequenceCanvas" width="800" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="showCombinedFragments()">🔄 演示组合片段</button>
                <button class="btn" onclick="showMessageTypes()">📨 演示消息类型</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置动画</button>
            </div>

            <div class="options">
                <div class="option" onclick="selectOption(this, false)">
                    <strong>A.</strong> 同步消息、异步消息、返回消息、动态消息、静态消息
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>B.</strong> 同步消息、异步消息、动态消息、参与者创建消息、参与者销毁消息
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>C.</strong> 同步消息、异步消息、静态消息、参与者创建消息、参与者销毁消息
                </div>
                <div class="option" onclick="selectOption(this, true)">
                    <strong>D.</strong> 同步消息、异步消息、返回消息、参与者创建消息、参与者销毁消息
                </div>
            </div>

            <div class="explanation" id="explanation">
                <h3>🎓 知识点详解</h3>
                
                <div class="knowledge-point">
                    <h4>🔧 组合片段 (Combined Fragments)</h4>
                    <p>在UML顺序图中，复杂交互（如循环、选择、并行等）使用<strong>组合片段</strong>来表示。组合片段是一个矩形框，左上角标有操作符：</p>
                    <ul style="margin: 10px 0 0 20px;">
                        <li><strong>loop</strong> - 循环</li>
                        <li><strong>alt</strong> - 选择（if-else）</li>
                        <li><strong>opt</strong> - 可选（if）</li>
                        <li><strong>par</strong> - 并行</li>
                    </ul>
                </div>

                <div class="knowledge-point">
                    <h4>📨 消息类型</h4>
                    <p>UML顺序图中的消息类型包括：</p>
                    <ul style="margin: 10px 0 0 20px;">
                        <li><strong>同步消息</strong> - 实线箭头，发送者等待接收者处理完成</li>
                        <li><strong>异步消息</strong> - 开放箭头，发送者不等待</li>
                        <li><strong>返回消息</strong> - 虚线箭头，表示方法调用的返回</li>
                        <li><strong>参与者创建消息</strong> - 指向参与者头部的消息</li>
                        <li><strong>参与者销毁消息</strong> - 以X结尾的消息</li>
                    </ul>
                </div>

                <div class="knowledge-point">
                    <h4>✅ 正确答案解析</h4>
                    <p><strong>答案D</strong>是正确的，因为它准确列出了UML顺序图中的标准消息类型。选项A、B、C中提到的"动态消息"和"静态消息"不是UML标准术语。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('sequenceCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;

        // 设置画布尺寸
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth - 60;
            canvas.height = 500;
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // 绘制参与者
        function drawParticipant(x, y, name, isActive = false) {
            ctx.save();
            
            // 头部矩形
            ctx.fillStyle = isActive ? '#667eea' : '#f8f9fa';
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.fillRect(x - 40, y, 80, 40);
            ctx.strokeRect(x - 40, y, 80, 40);
            
            // 文字
            ctx.fillStyle = isActive ? 'white' : '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y + 25);
            
            // 生命线
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(x, y + 40);
            ctx.lineTo(x, y + 400);
            ctx.stroke();
            ctx.setLineDash([]);
            
            ctx.restore();
        }

        // 绘制消息
        function drawMessage(x1, y, x2, type, label, animated = false) {
            ctx.save();
            
            const progress = animated ? Math.min(animationStep / 30, 1) : 1;
            const currentX = x1 + (x2 - x1) * progress;
            
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            
            if (type === 'sync') {
                // 同步消息 - 实线箭头
                ctx.beginPath();
                ctx.moveTo(x1, y);
                ctx.lineTo(currentX, y);
                ctx.stroke();
                
                if (progress === 1) {
                    // 箭头
                    ctx.beginPath();
                    ctx.moveTo(x2, y);
                    ctx.lineTo(x2 - 10, y - 5);
                    ctx.moveTo(x2, y);
                    ctx.lineTo(x2 - 10, y + 5);
                    ctx.stroke();
                }
            } else if (type === 'async') {
                // 异步消息 - 开放箭头
                ctx.beginPath();
                ctx.moveTo(x1, y);
                ctx.lineTo(currentX, y);
                ctx.stroke();
                
                if (progress === 1) {
                    ctx.beginPath();
                    ctx.moveTo(x2 - 10, y - 5);
                    ctx.lineTo(x2, y);
                    ctx.lineTo(x2 - 10, y + 5);
                    ctx.stroke();
                }
            } else if (type === 'return') {
                // 返回消息 - 虚线箭头
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(x1, y);
                ctx.lineTo(currentX, y);
                ctx.stroke();
                ctx.setLineDash([]);
                
                if (progress === 1) {
                    ctx.beginPath();
                    ctx.moveTo(x2, y);
                    ctx.lineTo(x2 - 10, y - 5);
                    ctx.moveTo(x2, y);
                    ctx.lineTo(x2 - 10, y + 5);
                    ctx.stroke();
                }
            }
            
            // 标签
            if (progress > 0.5) {
                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(label, (x1 + x2) / 2, y - 8);
            }
            
            ctx.restore();
        }

        // 绘制组合片段
        function drawCombinedFragment(x, y, width, height, operator, condition) {
            ctx.save();
            
            // 外框
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            
            // 操作符标签
            ctx.fillStyle = '#667eea';
            ctx.fillRect(x, y, 60, 25);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(operator, x + 30, y + 17);
            
            // 条件
            if (condition) {
                ctx.fillStyle = '#333';
                ctx.font = '11px Microsoft YaHei';
                ctx.textAlign = 'left';
                ctx.fillText(condition, x + 70, y + 17);
            }
            
            ctx.restore();
        }

        // 显示组合片段演示
        function showCombinedFragments() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const participants = [
                { x: 150, name: '用户' },
                { x: 350, name: '系统' },
                { x: 550, name: '数据库' }
            ];
            
            participants.forEach(p => drawParticipant(p.x, 50, p.name));
            
            // 绘制loop组合片段
            drawCombinedFragment(100, 120, 500, 150, 'loop', '[i < 10]');
            
            // 绘制alt组合片段
            drawCombinedFragment(100, 300, 500, 120, 'alt', '[条件为真]');
            
            // 分隔线
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 1;
            ctx.setLineDash([3, 3]);
            ctx.beginPath();
            ctx.moveTo(100, 360);
            ctx.lineTo(600, 360);
            ctx.stroke();
            ctx.setLineDash([]);
            
            ctx.fillStyle = '#666';
            ctx.font = '11px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('[else]', 110, 375);
            
            updateProgress(33);
        }

        // 显示消息类型演示
        function showMessageTypes() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const participants = [
                { x: 120, name: '客户端' },
                { x: 320, name: '服务器' },
                { x: 520, name: '数据库' },
                { x: 720, name: '新对象' }
            ];
            
            participants.forEach((p, i) => {
                if (i < 3) {
                    drawParticipant(p.x, 50, p.name);
                }
            });
            
            let y = 120;
            
            // 同步消息
            drawMessage(120, y, 320, 'sync', '同步调用');
            y += 40;
            
            // 异步消息
            drawMessage(120, y, 320, 'async', '异步调用');
            y += 40;
            
            // 返回消息
            drawMessage(320, y, 120, 'return', '返回结果');
            y += 40;
            
            // 参与者创建消息
            drawParticipant(720, 50, '新对象');
            drawMessage(320, y, 720, 'sync', '<<create>>');
            y += 40;
            
            // 参与者销毁消息
            drawMessage(320, y, 720, 'sync', '<<destroy>>');
            
            // 销毁标记
            ctx.strokeStyle = '#dc3545';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(710, y + 20);
            ctx.lineTo(730, y + 40);
            ctx.moveTo(730, y + 20);
            ctx.lineTo(710, y + 40);
            ctx.stroke();
            
            updateProgress(66);
        }

        // 重置动画
        function resetAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            animationStep = 0;
            
            const participants = [
                { x: 200, name: '对象A' },
                { x: 400, name: '对象B' },
                { x: 600, name: '对象C' }
            ];
            
            participants.forEach(p => drawParticipant(p.x, 50, p.name));
            
            ctx.fillStyle = '#666';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('点击按钮开始学习UML顺序图！', canvas.width / 2, 300);
            
            updateProgress(0);
        }

        // 选择选项
        function selectOption(element, isCorrect) {
            const options = document.querySelectorAll('.option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt !== element) {
                    opt.style.opacity = '0.5';
                }
            });
            
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('explanation').classList.add('show');
                updateProgress(100);
                
                // 显示庆祝动画
                setTimeout(() => {
                    showCelebration();
                }, 500);
            } else {
                element.classList.add('wrong');
                setTimeout(() => {
                    options.forEach(opt => {
                        opt.style.pointerEvents = 'auto';
                        opt.style.opacity = '1';
                    });
                    element.classList.remove('wrong');
                }, 1000);
            }
        }

        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        // 庆祝动画
        function showCelebration() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'];
            
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    createConfetti(colors[Math.floor(Math.random() * colors.length)]);
                }, i * 100);
            }
        }

        function createConfetti(color) {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.left = Math.random() * window.innerWidth + 'px';
            confetti.style.top = '-10px';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = color;
            confetti.style.borderRadius = '50%';
            confetti.style.pointerEvents = 'none';
            confetti.style.zIndex = '9999';
            confetti.style.transition = 'all 3s ease-out';
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                confetti.style.top = window.innerHeight + 'px';
                confetti.style.transform = 'rotate(720deg)';
                confetti.style.opacity = '0';
            }, 100);
            
            setTimeout(() => {
                document.body.removeChild(confetti);
            }, 3000);
        }

        // 初始化
        resetAnimation();
    </script>
</body>
</html>
