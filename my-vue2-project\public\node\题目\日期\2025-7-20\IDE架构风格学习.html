<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IDE架构风格选择 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .scenario-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .scenario-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .scenario-text {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #555;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            background: -webkit-linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-item {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-weight: bold;
            animation: bounceIn 0.8s ease-out;
        }

        .architecture-demo {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .demo-title {
            font-size: 1.8rem;
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        #architectureCanvas {
            border: 2px solid #eee;
            border-radius: 15px;
            background: #fafafa;
            cursor: pointer;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.5s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
            font-size: 1.1rem;
            border: none;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.2);
        }

        .option.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            animation: shake 0.5s ease-in-out;
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            animation: fadeIn 1s ease-out;
            display: none;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.6;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .highlight {
            background: rgba(255, 255, 0, 0.3);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:hover {
            background: #f8f9fa;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">IDE架构风格选择</h1>
            <p class="subtitle">理解集成开发环境的最佳架构设计</p>
        </div>

        <div class="scenario-card">
            <h2 class="scenario-title">📋 项目场景</h2>
            <div class="scenario-text">
                某公司为其研发的硬件产品设计实现了一种特定的编程语言，为了方便开发者进行软件开发，公司拟开发一套针对该编程语言的集成开发环境，包括代码编辑、语法高亮、代码编译、运行调试等功能。
            </div>
            
            <div class="features-grid">
                <div class="feature-item">📝 代码编辑</div>
                <div class="feature-item">🎨 语法高亮</div>
                <div class="feature-item">⚙️ 代码编译</div>
                <div class="feature-item">🐛 运行调试</div>
            </div>
        </div>

        <div class="architecture-demo">
            <h2 class="demo-title">🏗️ 架构风格对比演示</h2>
            <div class="canvas-container">
                <canvas id="architectureCanvas" width="1000" height="600"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showArchitecture('repository')">🗄️ 数据仓储</button>
                <button class="btn" onclick="showArchitecture('pipeline')">🔄 管道-过滤器</button>
                <button class="btn" onclick="showArchitecture('main-sub')">📋 主程序-子程序</button>
                <button class="btn" onclick="showArchitecture('interpreter')">🔍 解释器</button>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                针对上述描述，该集成开发环境应采用（ ）架构风格最为合适。
            </div>
            
            <div class="options">
                <button class="option" onclick="selectAnswer(this, 'A')">
                    A. 管道—过滤器
                </button>
                <button class="option" onclick="selectAnswer(this, 'B')">
                    B. 数据仓储
                </button>
                <button class="option" onclick="selectAnswer(this, 'C')">
                    C. 主程序—子程序
                </button>
                <button class="option" onclick="selectAnswer(this, 'D')">
                    D. 解释器
                </button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3>📚 详细解析</h3>
            <div id="explanationContent"></div>
        </div>

        <div class="architecture-demo">
            <h2 class="demo-title">📊 架构风格特点对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>架构风格</th>
                        <th>核心特点</th>
                        <th>适用场景</th>
                        <th>IDE适用性</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>数据仓储</strong></td>
                        <td>以数据为中心，多个组件共享数据</td>
                        <td>需要多个工具操作同一数据集</td>
                        <td>✅ 最适合</td>
                    </tr>
                    <tr>
                        <td><strong>管道-过滤器</strong></td>
                        <td>数据流式处理，单向传递</td>
                        <td>数据转换、流处理</td>
                        <td>❌ 不适合</td>
                    </tr>
                    <tr>
                        <td><strong>主程序-子程序</strong></td>
                        <td>层次化调用结构</td>
                        <td>简单的功能分解</td>
                        <td>❌ 过于简单</td>
                    </tr>
                    <tr>
                        <td><strong>解释器</strong></td>
                        <td>解释执行特定语言</td>
                        <td>语言处理器、脚本引擎</td>
                        <td>❌ 功能单一</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');

        function showArchitecture(type) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(type) {
                case 'repository':
                    drawRepositoryArchitecture();
                    break;
                case 'pipeline':
                    drawPipelineArchitecture();
                    break;
                case 'main-sub':
                    drawMainSubArchitecture();
                    break;
                case 'interpreter':
                    drawInterpreterArchitecture();
                    break;
            }
        }

        function drawRepositoryArchitecture() {
            // 中央数据仓储
            ctx.fillStyle = '#3498db';
            ctx.fillRect(400, 250, 200, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('代码仓储', 500, 290);
            ctx.fillText('(源代码、AST、符号表)', 500, 310);
            
            // IDE组件
            const components = [
                {x: 150, y: 100, name: '代码编辑器', color: '#e74c3c'},
                {x: 650, y: 100, name: '语法高亮', color: '#f39c12'},
                {x: 150, y: 400, name: '编译器', color: '#27ae60'},
                {x: 650, y: 400, name: '调试器', color: '#9b59b6'}
            ];
            
            components.forEach((comp, index) => {
                setTimeout(() => {
                    ctx.fillStyle = comp.color;
                    ctx.fillRect(comp.x, comp.y, 120, 80);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(comp.name, comp.x + 60, comp.y + 45);
                    
                    // 双向箭头到仓储
                    drawBidirectionalArrow(comp.x + 60, comp.y + 40, 500, 300);
                }, index * 300);
            });
            
            // 标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('数据仓储架构 - IDE的最佳选择', 500, 50);
        }

        function drawPipelineArchitecture() {
            const stages = ['源代码', '词法分析', '语法分析', '代码生成', '可执行文件'];
            stages.forEach((stage, index) => {
                setTimeout(() => {
                    const x = 50 + index * 180;
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillRect(x, 250, 160, 80);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(stage, x + 80, 295);
                    
                    if (index < stages.length - 1) {
                        drawArrow(x + 160, 290, x + 180, 290);
                    }
                }, index * 400);
            });
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('管道-过滤器架构 - 适合编译流程', 500, 50);
        }

        function drawMainSubArchitecture() {
            // 主程序
            ctx.fillStyle = '#3498db';
            ctx.fillRect(400, 100, 200, 80);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主程序 (IDE)', 500, 145);
            
            // 子程序
            const subs = [
                {x: 200, y: 300, name: '编辑模块'},
                {x: 400, y: 300, name: '编译模块'},
                {x: 600, y: 300, name: '调试模块'}
            ];
            
            subs.forEach((sub, index) => {
                setTimeout(() => {
                    ctx.fillStyle = '#27ae60';
                    ctx.fillRect(sub.x, sub.y, 120, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(sub.name, sub.x + 60, sub.y + 35);
                    
                    drawArrow(500, 180, sub.x + 60, sub.y);
                }, index * 300);
            });
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主程序-子程序架构 - 过于简单', 500, 50);
        }

        function drawInterpreterArchitecture() {
            // 解释器核心
            ctx.fillStyle = '#9b59b6';
            ctx.fillRect(350, 250, 300, 100);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('语言解释器', 500, 290);
            ctx.fillText('(解析和执行代码)', 500, 310);
            
            // 输入输出
            ctx.fillStyle = '#f39c12';
            ctx.fillRect(50, 275, 150, 50);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('源代码输入', 125, 305);
            
            ctx.fillStyle = '#27ae60';
            ctx.fillRect(800, 275, 150, 50);
            ctx.fillStyle = 'white';
            ctx.fillText('执行结果', 875, 305);
            
            drawArrow(200, 300, 350, 300);
            drawArrow(650, 300, 800, 300);
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('解释器架构 - 功能单一', 500, 50);
        }

        function drawArrow(fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#7f8c8d';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function drawBidirectionalArrow(fromX, fromY, toX, toY) {
            drawArrow(fromX, fromY, toX, toY);
            drawArrow(toX, toY, fromX, fromY);
        }

        function selectAnswer(element, answer) {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });
            
            const explanationDiv = document.getElementById('explanation');
            const explanationContent = document.getElementById('explanationContent');
            
            if (answer === 'B') {
                element.classList.add('correct');
                explanationContent.innerHTML = `
                    <p><strong>🎉 恭喜答对！数据仓储架构是IDE的最佳选择！</strong></p>
                    <p><strong>为什么选择数据仓储架构？</strong></p>
                    <p>• <span class="highlight">以代码为中心</span>：所有IDE功能都围绕源代码进行操作</p>
                    <p>• <span class="highlight">共享数据模型</span>：编辑器、编译器、调试器都需要访问相同的代码数据</p>
                    <p>• <span class="highlight">多工具协作</span>：不同工具可以同时对代码进行不同类型的处理</p>
                    <p>• <span class="highlight">数据一致性</span>：确保所有组件看到的是同一份代码</p>
                    <p><strong>💡 关键理解：</strong>IDE的核心是对源代码的多种操作，数据仓储架构最适合这种"多工具共享数据"的场景！</p>
                `;
            } else {
                element.classList.add('wrong');
                let wrongExplanation = '';
                switch(answer) {
                    case 'A':
                        wrongExplanation = '❌ 管道-过滤器适合数据流处理，但IDE需要多个工具同时访问代码，不是单向流处理。';
                        break;
                    case 'C':
                        wrongExplanation = '❌ 主程序-子程序过于简单，无法支持IDE复杂的多工具协作需求。';
                        break;
                    case 'D':
                        wrongExplanation = '❌ 解释器只是IDE的一个组件，不能作为整个IDE的架构风格。';
                        break;
                }
                explanationContent.innerHTML = `
                    <p>${wrongExplanation}</p>
                    <p><strong>正确答案是B：数据仓储架构</strong></p>
                    <p><strong>核心原因：</strong></p>
                    <p>• IDE的所有功能都以<span class="highlight">源代码为中心</span></p>
                    <p>• 需要多个工具<span class="highlight">同时访问和操作</span>同一份代码</p>
                    <p>• 数据仓储架构提供了<span class="highlight">共享的数据存储</span>和访问机制</p>
                `;
            }
            
            explanationDiv.style.display = 'block';
        }

        // 初始化显示数据仓储架构
        setTimeout(() => {
            showArchitecture('repository');
        }, 1000);
    </script>
</body>
</html>
