<template>
  <div class="floating-window" :class="{ 'dark-mode': darkMode }">
    <!-- 圆形主体 -->
    <div class="circle-container" @click="openNotes">
      <!-- 中心图标 -->
      <div class="center-icon">
        <i class="el-icon-edit-outline"></i>
      </div>

      <!-- 关闭按钮 -->
      <div class="close-btn" @click.stop="closeWindow">
        <i class="el-icon-close"></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FloatingWindow',
  
  data() {
    return {
      darkMode: false,
      windowId: null
    }
  },
  
  mounted() {
    this.initFloatingWindow()
  },

  beforeDestroy() {
    this.cleanup()
  },
  
  methods: {
    // 初始化悬浮窗
    initFloatingWindow() {
      // 检测是否在Electron环境中
      if (window.require) {
        const { ipcRenderer } = window.require('electron')
        
        // 监听窗口信息
        ipcRenderer.on('floating-window-info', (event, info) => {
          this.windowId = info.id
        })
        
        // 监听窗口移动
        ipcRenderer.on('floating-window-moved', (event, position) => {
          console.log('Window moved:', position)
        })
        
        // 监听窗口大小变化
        ipcRenderer.on('floating-window-resized', (event, size) => {
          console.log('Window resized:', size)
        })
      }
    },
    
    // 清理资源
    cleanup() {
      if (window.require) {
        const { ipcRenderer } = window.require('electron')
        ipcRenderer.removeAllListeners('floating-window-info')
        ipcRenderer.removeAllListeners('floating-window-moved')
        ipcRenderer.removeAllListeners('floating-window-resized')
      }
    },
    
    // 窗口控制方法
    closeWindow() {
      if (window.require) {
        const { ipcRenderer } = window.require('electron')
        ipcRenderer.send('close-floating-window')
      }
    },
    
    // 打开笔记功能
    openNotes() {
      // 通知主窗口打开笔记页面
      if (window.require) {
        const { ipcRenderer } = window.require('electron')
        ipcRenderer.send('open-notes-page')
      }
    },

  }
}
</script>

<style scoped>
.floating-window {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  user-select: none;
  position: relative;
}

/* 圆形容器 */
.circle-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  position: relative;
  -webkit-app-region: drag;
}

.floating-window.dark-mode .circle-container {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.circle-container:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* 中心图标 */
.center-icon {
  font-size: 24px;
  color: white;
  transition: all 0.3s ease;
}

.center-icon i {
  font-size: 24px;
  color: white;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  -webkit-app-region: no-drag;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.close-btn i {
  font-size: 10px;
  color: #666;
}
</style>
