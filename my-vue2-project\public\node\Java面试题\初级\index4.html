<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java 核心概念解析</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 960px;
            margin: 20px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }
        .question-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .question-section h3 {
            color: #28a745;
            margin-top: 0;
            font-size: 1.8em;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .explanation {
            background-color: #e9f7ef;
            padding: 15px;
            border-left: 5px solid #28a745;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .interactive-area {
            background-color: #fff;
            padding: 20px;
            border: 1px dashed #007bff;
            border-radius: 8px;
            min-height: 200px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
            margin-top: 20px;
        }
        canvas {
            border: 1px solid #ddd;
            background-color: #f8f8f8;
            border-radius: 5px;
            margin-top: 20px;
            max-width: 100%;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin-top: 10px;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background-color: #0056b3;
        }
        .code-block {
            background-color: #eee;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', Courier, monospace;
            white-space: pre-wrap;
            word-break: break-all;
            margin-top: 15px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Java 核心概念解析</h1>

        <div class="question-section">
            <h3>20、Java语言采用何种编码方案？有何特点？</h3>
            <div class="explanation">
                <p>Java语言采用<strong>Unicode编码标准</strong>。Unicode（标准码）为每个字符制定了一个唯一的数值，因此在任何语言、平台、程序中都可以放心地使用。</p>
            </div>
            <div class="interactive-area" id="unicode-demo">
                <p>点击下方按钮，开始Unicode编码演示：</p>
                <button onclick="startUnicodeDemo()">开始演示</button>
                <canvas id="unicodeCanvas" width="600" height="200"></canvas>
            </div>
        </div>

        <div class="question-section">
            <h3>21、 final、finally、finalize 区别</h3>
            
            <h4><strong>final</strong></h4>
            <div class="explanation">
                <p><code>final</code> 可以修饰类、变量、方法：</p>
                <ul>
                    <li>修饰类表示该类不能被继承。</li>
                    <li>修饰方法表示该方法不能被重写。</li>
                    <li>修饰变量表示该变量是一个常量，不能被重新赋值。</li>
                </ul>
            </div>
            <div class="interactive-area" id="final-demo">
                <p>点击按钮演示 <code>final</code> 关键字的用法：</p>
                <button onclick="startFinalDemo()">演示 final</button>
                <canvas id="finalCanvas" width="600" height="300"></canvas>
            </div>

            <h4><strong>finally</strong></h4>
            <div class="explanation">
                <p><code>finally</code> 一般作用在 <code>try-catch</code> 代码块中，在处理异常时，通常我们将一定要执行的代码放在 <code>finally</code> 代码块中，表示不管是否出现异常，该代码块都会执行，一般用来存放一些关闭资源的代码。</p>
            </div>
            <div class="interactive-area" id="finally-demo">
                <p>点击按钮演示 <code>finally</code> 代码块的执行时机：</p>
                <button onclick="startFinallyDemo()">演示 finally</button>
                <canvas id="finallyCanvas" width="600" height="250"></canvas>
            </div>

            <h4><strong>finalize</strong></h4>
            <div class="explanation">
                <p><code>finalize</code> 是一个方法，属于 <code>Object</code> 类的一个方法，而 <code>Object</code> 类是所有类的父类。该方法一般由垃圾回收器来调用，当我们调用 <code>System.gc()</code> 方法的时候，由垃圾回收器调用 <code>finalize()</code>，回收垃圾，是一个对象是否可回收的最后判断。</p>
            </div>
            <div class="interactive-area" id="finalize-demo">
                <p>点击按钮演示 <code>finalize</code> 方法的调用过程：</p>
                <button onclick="startFinalizeDemo()">演示 finalize</button>
                <canvas id="finalizeCanvas" width="600" height="250"></canvas>
            </div>
        </div>
    </div>

    <script>
        // JavaScript 交互逻辑将在这里添加

        // 20. Unicode Demo
        function startUnicodeDemo() {
            const canvas = document.getElementById('unicodeCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const chars = ['A', '你', '😊'];
            const hexCodes = ['U+0041', 'U+4F60', 'U+1F60A'];

            let step = 0;
            const animateUnicode = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                if (step < chars.length) {
                    const char = chars[step];
                    const hex = hexCodes[step];

                    ctx.fillStyle = '#007bff';
                    ctx.fillText('字符: ' + char, canvas.width / 2, 50);
                    
                    ctx.fillStyle = '#28a745';
                    ctx.fillText('Unicode 值: ' + hex, canvas.width / 2, 100);

                    ctx.fillStyle = '#333';
                    ctx.fillText('每一个字符都有一个唯一的数值', canvas.width / 2, 150);
                    step++;
                    setTimeout(animateUnicode, 1500); // 1.5秒后显示下一个
                } else {
                    ctx.fillStyle = '#333';
                    ctx.fillText('Unicode 让全球字符通用！', canvas.width / 2, 100);
                    step = 0; // 重置以便再次演示
                }
            };
            animateUnicode();
        }

        // 21. final Demo
        function startFinalDemo() {
            const canvas = document.getElementById('finalCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let y = 50;
            const drawText = (text, color = '#333') => {
                ctx.fillStyle = color;
                ctx.fillText(text, canvas.width / 2, y);
                y += 40;
            };

            y = 50;
            drawText('演示 final 关键字', '#007bff');
            drawText('--------------------');
            
            // 演示 final 变量
            drawText('1. final 变量:', '#28a745');
            drawText('final int MAX_VALUE = 100;', '#555');
            drawText('MAX_VALUE = 200; // 错误！不能重新赋值', 'red');
            
            // 演示 final 类 (概念性)
            y += 20;
            drawText('2. final 类:', '#28a745');
            drawText('final class MyClass { ... }', '#555');
            drawText('class SubClass extends MyClass { ... } // 错误！final 类不能被继承', 'red');

            // 演示 final 方法 (概念性)
            y += 20;
            drawText('3. final 方法:', '#28a745');
            drawText('class Parent { final void doSomething() { ... } }', '#555');
            drawText('class Child extends Parent { void doSomething() { ... } } // 错误！final 方法不能被重写', 'red');
            drawText('--------------------');
            drawText('final 意味着"最终的、不可改变的"');
        }

        // 21. finally Demo
        function startFinallyDemo() {
            const canvas = document.getElementById('finallyCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const blockWidth = 150;
            const blockHeight = 50;
            const startX = (canvas.width - blockWidth) / 2;
            let currentY = 50;

            const drawBlock = (text, color) => {
                ctx.fillStyle = color;
                ctx.fillRect(startX, currentY, blockWidth, blockHeight);
                ctx.strokeStyle = '#333';
                ctx.strokeRect(startX, currentY, blockWidth, blockHeight);
                ctx.fillStyle = 'white';
                ctx.fillText(text, startX + blockWidth / 2, currentY + blockHeight / 2);
                currentY += blockHeight + 20;
            };

            const drawArrow = (fromY, toY, color = '#333') => {
                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(startX + blockWidth / 2, fromY + blockHeight);
                ctx.lineTo(startX + blockWidth / 2, toY);
                ctx.lineTo(startX + blockWidth / 2 - 5, toY - 10);
                ctx.moveTo(startX + blockWidth / 2, toY);
                ctx.lineTo(startX + blockWidth / 2 + 5, toY - 10);
                ctx.stroke();
                ctx.lineWidth = 1;
            };
            
            // 场景 1: 无异常
            let y1 = 30;
            ctx.fillStyle = '#007bff';
            ctx.fillText('场景 1: 没有异常', canvas.width / 4, y1);
            ctx.beginPath();
            ctx.moveTo(canvas.width / 4, y1 + 10);
            ctx.lineTo(canvas.width / 4 + 70, y1 + 10);
            ctx.stroke();

            currentY = 60;
            drawBlock('try', '#4CAF50');
            drawBlock('finally', '#FFC107');
            drawArrow(60, 60 + blockHeight + 20); // try -> finally

            // 场景 2: 发生异常
            let y2 = 30;
            ctx.fillStyle = '#007bff';
            ctx.fillText('场景 2: 发生异常', canvas.width * 3 / 4, y2);
            ctx.beginPath();
            ctx.moveTo(canvas.width * 3 / 4, y2 + 10);
            ctx.lineTo(canvas.width * 3 / 4 + 70, y2 + 10);
            ctx.stroke();

            currentY = 60;
            ctx.save(); // 保存当前状态
            ctx.translate(canvas.width / 2, 0); // 移动到右侧
            drawBlock('try (发生异常)', '#f44336');
            drawBlock('catch', '#2196F3');
            drawBlock('finally', '#FFC107');
            drawArrow(60, 60 + blockHeight + 20, '#f44336'); // try -> catch
            drawArrow(60 + blockHeight + 20, 60 + 2 * (blockHeight + 20), '#2196F3'); // catch -> finally
            ctx.restore(); // 恢复之前保存的状态
            
            ctx.fillStyle = '#333';
            ctx.fillText('无论是否发生异常，finally 总是会执行', canvas.width / 2, canvas.height - 20);
        }

        // 21. finalize Demo
        function startFinalizeDemo() {
            const canvas = document.getElementById('finalizeCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            let objectX = canvas.width / 2;
            let objectY = 50;
            let gcX = canvas.width / 2;
            let gcY = 150;

            const drawObject = (text, color) => {
                ctx.fillStyle = color;
                ctx.fillRect(objectX - 50, objectY, 100, 50);
                ctx.strokeStyle = '#333';
                ctx.strokeRect(objectX - 50, objectY, 100, 50);
                ctx.fillStyle = 'white';
                ctx.fillText(text, objectX, objectY + 25);
            };

            const drawGC = (text, color) => {
                ctx.fillStyle = color;
                ctx.fillRect(gcX - 70, gcY, 140, 50);
                ctx.strokeStyle = '#333';
                ctx.strokeRect(gcX - 70, gcY, 140, 50);
                ctx.fillStyle = 'white';
                ctx.fillText(text, gcX, gcY + 25);
            };

            let step = 0;
            const animateFinalize = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                if (step === 0) {
                    drawObject('Java 对象', '#007bff');
                    ctx.fillStyle = '#333';
                    ctx.fillText('1. 创建了一个 Java 对象', objectX, objectY + 80);
                    step = 1;
                    setTimeout(animateFinalize, 2000);
                } else if (step === 1) {
                    drawObject('Java 对象 (无引用)', 'gray');
                    ctx.fillStyle = '#333';
                    ctx.fillText('2. 对象不再被引用，成为垃圾', objectX, objectY + 80);
                    ctx.fillText('System.gc() 被调用 (可选)', objectX, objectY + 110);
                    drawGC('垃圾回收器 (GC)', '#28a745');
                    step = 2;
                    setTimeout(animateFinalize, 2000);
                } else if (step === 2) {
                    drawGC('垃圾回收器 (GC)', '#28a745');
                    ctx.fillStyle = '#f44336';
                    ctx.fillText('3. GC 判断对象可回收', objectX, objectY + 80);
                    ctx.fillText('如果重写了 finalize() 方法', objectX, objectY + 110);
                    ctx.fillText('GC 调用对象的 finalize() 方法', objectX, gcY + 80);
                    ctx.fillStyle = '#333';
                    ctx.fillText('进行资源清理 (例如关闭文件)', objectX, gcY + 110);
                    step = 3;
                    setTimeout(animateFinalize, 3000);
                } else if (step === 3) {
                    ctx.fillStyle = '#007bff';
                    ctx.fillText('对象被彻底回收', canvas.width / 2, canvas.height / 2);
                    step = 0; // 重置以便再次演示
                }
            };
            animateFinalize();
        }
    </script>
</body>
</html> 