<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式软件设计 - 可移植性学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-text {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .option.selected {
            border-color: #4CAF50;
            background: #e8f5e8;
        }

        .option.correct {
            border-color: #4CAF50;
            background: #d4edda;
            animation: correctPulse 0.6s ease-in-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: #f8d7da;
            animation: wrongShake 0.6s ease-in-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.6s forwards;
        }

        #animationCanvas {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            background: #f8f9fa;
        }

        .explanation-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.9s forwards;
        }

        .concept-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .concept-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 20px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-circle" style="width: 60px; height: 60px; top: 10%; left: 10%; animation-delay: 0s;"></div>
        <div class="floating-circle" style="width: 80px; height: 80px; top: 20%; right: 10%; animation-delay: 2s;"></div>
        <div class="floating-circle" style="width: 40px; height: 40px; bottom: 20%; left: 20%; animation-delay: 4s;"></div>
        <div class="floating-circle" style="width: 70px; height: 70px; bottom: 10%; right: 20%; animation-delay: 1s;"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">🔧 嵌入式软件设计</h1>
            <p class="subtitle">通过动画学习可移植性概念</p>
        </div>

        <div class="question-card">
            <h2 class="question-text">
                嵌入式软件设计需要考虑（ ）以保障软件良好的可移植性。
            </h2>
            <div class="options-container">
                <div class="option" data-option="A">
                    <h3>A. 先进性</h3>
                    <p>使用最新技术</p>
                </div>
                <div class="option" data-option="B">
                    <h3>B. 易用性</h3>
                    <p>用户友好界面</p>
                </div>
                <div class="option" data-option="C">
                    <h3>C. 硬件无关性</h3>
                    <p>独立于硬件平台</p>
                </div>
                <div class="option" data-option="D">
                    <h3>D. 可靠性</h3>
                    <p>系统稳定运行</p>
                </div>
            </div>
            <div style="text-align: center;">
                <button class="btn" onclick="showAnswer()">查看答案解析</button>
                <button class="btn" onclick="startAnimation()">开始动画演示</button>
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="animationCanvas"></canvas>
        </div>

        <div class="explanation-card">
            <h2 class="concept-title">💡 知识点详解</h2>
            <div class="concept-content">
                <p><strong>什么是可移植性？</strong></p>
                <p>可移植性是指软件能够在不同的硬件平台、操作系统或环境中运行的能力。对于嵌入式软件来说，这意味着同一套代码可以在不同的微控制器、处理器或开发板上运行。</p>
                
                <p><strong>为什么选择"硬件无关性"？</strong></p>
                <p>• <span class="highlight">硬件无关性</span>是实现可移植性的核心原则</p>
                <p>• 通过抽象硬件接口，软件可以独立于具体硬件实现</p>
                <p>• 降低了软件与硬件的耦合度，提高了代码复用性</p>
                
                <p><strong>其他选项为什么不对？</strong></p>
                <p>• 先进性：与可移植性没有直接关系</p>
                <p>• 易用性：主要关注用户体验，不是可移植性的关键</p>
                <p>• 可靠性：虽然重要，但不是保障可移植性的主要因素</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        let animationRunning = false;
        let animationFrame = 0;

        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 显示答案
        function showAnswer() {
            document.querySelectorAll('.option').forEach(option => {
                const optionLetter = option.dataset.option;
                if (optionLetter === 'C') {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected')) {
                    option.classList.add('wrong');
                }
            });
        }

        // 开始动画演示
        function startAnimation() {
            if (animationRunning) return;
            animationRunning = true;
            animationFrame = 0;
            animate();
        }

        function animate() {
            if (!animationRunning) return;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 动画阶段
            if (animationFrame < 120) {
                // 阶段1：展示软件代码
                drawSoftwareCode(centerX - 200, centerY - 100);
                drawText("嵌入式软件", centerX - 200, centerY - 130, "20px", "#333");
            }
            
            if (animationFrame > 60 && animationFrame < 240) {
                // 阶段2：展示不同硬件平台
                drawHardwarePlatform(centerX + 100, centerY - 80, "ARM", "#ff6b6b");
                drawHardwarePlatform(centerX + 100, centerY, "x86", "#4ecdc4");
                drawHardwarePlatform(centerX + 100, centerY + 80, "RISC-V", "#45b7d1");
                drawText("不同硬件平台", centerX + 100, centerY - 110, "16px", "#666");
            }
            
            if (animationFrame > 120 && animationFrame < 360) {
                // 阶段3：展示连接线（可移植性）
                const progress = Math.min((animationFrame - 120) / 120, 1);
                drawPortabilityLines(centerX - 150, centerY, centerX + 50, centerY - 80, progress);
                drawPortabilityLines(centerX - 150, centerY, centerX + 50, centerY, progress);
                drawPortabilityLines(centerX - 150, centerY, centerX + 50, centerY + 80, progress);
                
                if (progress > 0.5) {
                    drawText("硬件无关性", centerX - 50, centerY - 150, "18px", "#28a745");
                    drawText("实现可移植性", centerX - 50, centerY - 125, "14px", "#666");
                }
            }
            
            if (animationFrame > 240) {
                // 阶段4：展示成功的可移植性
                drawSuccessIndicator(centerX, centerY + 120);
            }
            
            animationFrame++;
            
            if (animationFrame < 400) {
                requestAnimationFrame(animate);
            } else {
                animationRunning = false;
            }
        }
        
        function drawSoftwareCode(x, y) {
            // 绘制代码框
            ctx.fillStyle = "#2c3e50";
            ctx.fillRect(x, y, 150, 100);
            
            // 绘制代码行
            ctx.fillStyle = "#ecf0f1";
            ctx.font = "12px monospace";
            ctx.fillText("int main() {", x + 10, y + 20);
            ctx.fillText("  // 业务逻辑", x + 10, y + 40);
            ctx.fillText("  return 0;", x + 10, y + 60);
            ctx.fillText("}", x + 10, y + 80);
        }
        
        function drawHardwarePlatform(x, y, name, color) {
            // 绘制硬件平台
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 80, 50);
            
            // 绘制芯片图标
            ctx.fillStyle = "white";
            ctx.fillRect(x + 10, y + 10, 60, 30);
            ctx.fillStyle = color;
            ctx.font = "14px Arial";
            ctx.textAlign = "center";
            ctx.fillText(name, x + 40, y + 28);
            ctx.textAlign = "left";
        }
        
        function drawPortabilityLines(x1, y1, x2, y2, progress) {
            const endX = x1 + (x2 - x1) * progress;
            const endY = y1 + (y2 - y1) * progress;
            
            ctx.strokeStyle = "#28a745";
            ctx.lineWidth = 3;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(endX, endY);
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 绘制箭头
            if (progress > 0.8) {
                const angle = Math.atan2(y2 - y1, x2 - x1);
                ctx.save();
                ctx.translate(endX, endY);
                ctx.rotate(angle);
                ctx.fillStyle = "#28a745";
                ctx.beginPath();
                ctx.moveTo(0, 0);
                ctx.lineTo(-10, -5);
                ctx.lineTo(-10, 5);
                ctx.closePath();
                ctx.fill();
                ctx.restore();
            }
        }
        
        function drawSuccessIndicator(x, y) {
            // 绘制成功图标
            ctx.fillStyle = "#28a745";
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制对勾
            ctx.strokeStyle = "white";
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(x - 10, y);
            ctx.lineTo(x - 5, y + 8);
            ctx.lineTo(x + 10, y - 8);
            ctx.stroke();
            
            drawText("可移植性实现！", x, y + 50, "16px", "#28a745");
        }
        
        function drawText(text, x, y, font, color) {
            ctx.font = font + " Arial";
            ctx.fillStyle = color;
            ctx.textAlign = "center";
            ctx.fillText(text, x, y);
            ctx.textAlign = "left";
        }
    </script>
</body>
</html>
