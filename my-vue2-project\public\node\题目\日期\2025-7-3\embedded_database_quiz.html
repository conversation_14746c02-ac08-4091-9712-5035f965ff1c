<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式数据库知识学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-section, .explanation-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 25px;
            background-color: #fdfdfd;
        }
        .question {
            font-size: 1.2em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .options label {
            display: block;
            margin-bottom: 15px;
            cursor: pointer;
            font-size: 1.1em;
            padding: 10px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .options label:hover {
            background-color: #f0f0f0;
        }
        .options input[type="radio"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        .submit-btn {
            display: block;
            width: 150px;
            padding: 12px 20px;
            margin: 25px auto 0;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1.1em;
            transition: background-color 0.3s ease;
        }
        .submit-btn:hover {
            background-color: #45a049;
        }
        .result {
            text-align: center;
            margin-top: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        .explanation-content p {
            margin-bottom: 10px;
        }
        .explanation-content ul {
            list-style-type: disc;
            padding-left: 25px;
        }
        .explanation-content li {
            margin-bottom: 8px;
        }
        .highlight {
            color: #007BFF;
            font-weight: bold;
        }
        .correct-answer {
            color: #28a745;
        }
        .wrong-answer {
            color: #dc3545;
        }

        .canvas-section {
            margin-top: 40px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 25px;
            background-color: #fdfdfd;
            text-align: center;
        }
        canvas {
            border: 1px solid #ddd;
            background-color: #fff;
            display: block;
            margin: 20px auto;
            border-radius: 5px;
        }
        .controls {
            margin-top: 20px;
        }
        .controls button {
            padding: 10px 18px;
            margin: 0 10px;
            background-color: #007BFF;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>嵌入式数据库知识学习</h1>

        <div class="question-section">
            <h2>题目</h2>
            <div class="question">
                <p>与传统数据库相比，嵌入式数据库的主要特点不包括（）；基于文件的嵌入式数据库系统典型产品是（）。</p>
            </div>
            <div class="options">
                <label>
                    <input type="radio" name="answer" value="A"> A. SQLite
                </label>
                <label>
                    <input type="radio" name="answer" value="B"> B. Oracle
                </label>
                <label>
                    <input type="radio" name="answer" value="C"> C. eXtremeDB
                </label>
                <label>
                    <input type="radio" name="answer" value="D"> D. Firebird
                </label>
            </div>
            <button class="submit-btn" onclick="checkAnswer()">提交答案</button>
            <div class="result" id="quiz-result"></div>
        </div>

        <div class="explanation-section">
            <h2>解析</h2>
            <div class="explanation-content">
                <p>与传统数据库相比，嵌入式数据库系统有以下几个主要特点：</p>
                <ul>
                    <li><span class="highlight">嵌入式：</span> 嵌入性是嵌入式数据库的基本特性。嵌入式数据库不仅可以嵌入到其他的软件当中，也可以嵌入到硬件设备当中。</li>
                    <li><span class="highlight">实时性：</span> 实时性和嵌入性是分不开的。只有具有了实时性的数据库才能够第一时间得到系统的资源，对系统的请求在第一时间做出响应。但是，并不是具有嵌入性就一定具有实时性。要想嵌入式数据库具有很好的实时性，必须做很多额外的工作。</li>
                    <li><span class="highlight">移动性：</span> 移动性是目前在国内提得比较多的一个说法，这和目前国内移动设备的大规模应用有关。可以这么说，具有嵌入性的数据库一定具有比较好的移动性，但是具有比较好的移动性的数据库，不一定具有嵌入性。</li>
                    <li><span class="highlight">伸缩性：</span> 伸缩性在嵌入式场合显得尤为重要。首先嵌入式场合硬件和软件的平台都是千差万别，基本都是客户根据需要自己选择的结果。</li>
                </ul>
                <p>嵌入式数据库中，<span class="highlight">SQLite</span> 和 <span class="highlight">Berkeley DB</span> 是文件型数据库，<span class="highlight">Firebird</span> 是网络型数据库，<span class="highlight">eXtremeDB</span> 则是内存型数据库。<span class="highlight">Oracle</span> 是大型数据库，不是嵌入式数据库。</p>
                <p>（官方教材第二版P565）</p>
            </div>
        </div>

        <div class="canvas-section">
            <h2>知识点动画演示</h2>
            <p>点击下方按钮，观看嵌入式数据库主要特点的动画演示和不同数据库类型的说明。</p>
            <canvas id="myCanvas" width="800" height="400"></canvas>
            <div class="controls">
                <button onclick="startAnimation('embedded')">嵌入性</button>
                <button onclick="startAnimation('realtime')">实时性</button>
                <button onclick="startAnimation('portability')">移动性</button>
                <button onclick="startAnimation('scalability')">伸缩性</button>
                <button onclick="startAnimation('types')">数据库类型</button>
                <button onclick="resetCanvas()">重置</button>
            </div>
        </div>
    </div>

    <script>
        const correctAnswer = "B"; // 根据题目解析，Oracle不是嵌入式数据库，所以选择B是正确的答案。

        function checkAnswer() {
            const selectedOption = document.querySelector('input[name="answer"]:checked');
            const resultDiv = document.getElementById('quiz-result');

            if (selectedOption) {
                if (selectedOption.value === correctAnswer) {
                    resultDiv.textContent = "回答正确！Oracle不是嵌入式数据库。";
                    resultDiv.className = "result correct-answer";
                } else {
                    resultDiv.textContent = `回答错误。正确答案是 B (Oracle)。因为Oracle是大型数据库，不是嵌入式数据库。`;
                    resultDiv.className = "result wrong-answer";
                }
            } else {
                resultDiv.textContent = "请选择一个答案。";
                resultDiv.className = "result";
            }
        }

        const canvas = document.getElementById('myCanvas');
        const ctx = canvas.getContext('2d');
        let animationFrameId;

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawText(text, x, y, color = '#333', fontSize = 18) {
            ctx.fillStyle = color;
            ctx.font = `${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        function drawRectangle(x, y, width, height, color, text = '', textColor = 'white') {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.strokeRect(x, y, width, height);
            if (text) {
                drawText(text, x + width / 2, y + height / 2 + 6, textColor);
            }
        }

        function drawCircle(x, y, radius, color, text = '', textColor = 'white') {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.stroke();
            if (text) {
                drawText(text, x, y + 6, textColor);
            }
        }

        function drawArrow(fromX, fromY, toX, toY, color = '#333') {
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // Arrowhead
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fill();
        }

        let animationProgress = 0;
        let animationType = '';
        let startTime = null;

        function animate(timestamp) {
            if (!startTime) startTime = timestamp;
            const elapsed = timestamp - startTime;
            animationProgress = elapsed / 2000; // 2 seconds for one cycle

            clearCanvas();

            if (animationType === 'embedded') {
                animateEmbedded(animationProgress);
            } else if (animationType === 'realtime') {
                animateRealtime(animationProgress);
            } else if (animationType === 'portability') {
                animatePortability(animationProgress);
            } else if (animationType === 'scalability') {
                animateScalability(animationProgress);
            } else if (animationType === 'types') {
                drawDatabaseTypes();
            }

            if (animationProgress < 1 || animationType === 'types') { // Keep animating 'types' until reset
                 animationFrameId = requestAnimationFrame(animate);
            } else {
                // For looping animations, reset and restart
                startTime = null;
                animationProgress = 0;
                animationFrameId = requestAnimationFrame(animate);
            }
        }

        function startAnimation(type) {
            cancelAnimationFrame(animationFrameId);
            animationType = type;
            startTime = null;
            animationProgress = 0;
            if (type === 'types') { // For 'types', draw immediately and keep static
                clearCanvas();
                drawDatabaseTypes();
            }
            animationFrameId = requestAnimationFrame(animate);
        }

        function resetCanvas() {
            cancelAnimationFrame(animationFrameId);
            clearCanvas();
            animationType = '';
            drawText("点击上方按钮开始演示", canvas.width / 2, canvas.height / 2, '#888', 24);
        }

        function animateEmbedded(progress) {
            const dbX = canvas.width / 2 - 50;
            const dbY = canvas.height / 2 - 30;
            drawRectangle(dbX, dbY, 100, 60, '#6A1B9A', '数据库', 'white'); // Database

            const appX = canvas.width / 2 - 150;
            const appY = canvas.height / 2 - 120;
            drawRectangle(appX, appY, 300, 100, '#42A5F5', '应用程序', 'white'); // Application

            const deviceX = canvas.width / 2 - 150;
            const deviceY = canvas.height / 2 + 50;
            drawRectangle(deviceX, deviceY, 300, 100, '#FF7043', '硬件设备', 'white'); // Hardware Device

            ctx.save();
            ctx.beginPath();
            ctx.rect(appX, appY, 300, 100);
            ctx.clip();

            let embeddedX = dbX;
            let embeddedY = dbY;

            if (progress < 0.5) { // Moving towards application
                embeddedX = dbX + (appX - dbX) * progress * 2;
                embeddedY = dbY + (appY - dbY) * progress * 2;
                drawRectangle(embeddedX, embeddedY, 100, 60, '#6A1B9A', '嵌入式数据库', 'white');
                drawArrow(dbX + 50, dbY + 60, embeddedX + 50, embeddedY);
                drawText("嵌入到应用程序中", canvas.width / 2, 30, '#333');
            } else { // Moving towards device
                embeddedX = appX + (deviceX - appX) * (progress - 0.5) * 2;
                embeddedY = appY + (deviceY - appY) * (progress - 0.5) * 2;
                drawRectangle(embeddedX, embeddedY, 100, 60, '#6A1B9A', '嵌入式数据库', 'white');
                drawArrow(appX + 150, appY + 100, embeddedX + 50, embeddedY);
                drawText("嵌入到硬件设备中", canvas.width / 2, 30, '#333');
            }
            ctx.restore();

             // Loop animation
            if (progress >= 1) {
                startTime = null; // Reset startTime to loop
            }
        }

        function animateRealtime(progress) {
            const clientX = canvas.width / 4;
            const clientY = canvas.height / 2;
            const dbX = canvas.width * 3 / 4;
            const dbY = canvas.height / 2;

            drawCircle(clientX, clientY, 40, '#4CAF50', '请求方', 'white');
            drawRectangle(dbX - 50, dbY - 30, 100, 60, '#FFC107', '数据库', 'black');

            let arrowEndX = clientX + (dbX - clientX) * progress;
            let arrowEndY = clientY;

            drawArrow(clientX + 40, clientY, arrowEndX - 5, arrowEndY);
            drawText("发送请求", clientX + (dbX - clientX) / 2, clientY - 30, '#333');

            if (progress > 0.5) {
                let responseStartX = dbX - (dbX - clientX) * (progress - 0.5) * 2;
                let responseEndX = clientX;
                drawArrow(dbX - 40, dbY, responseStartX + 5, responseEndY);
                drawText("快速响应", clientX + (dbX - clientX) / 2, clientY + 30, '#333');
            }

            const responseTime = Math.max(0, 2000 - Math.floor(progress * 2000)); // Simulate quick response
            drawText(`响应时间: ${responseTime}ms`, canvas.width / 2, 30, '#333', 20);

             // Loop animation
            if (progress >= 1) {
                startTime = null; // Reset startTime to loop
            }
        }

        function animatePortability(progress) {
            const dbSize = 60;
            const startX = canvas.width / 8;
            const endX = canvas.width * 7 / 8 - dbSize;
            const yPos = canvas.height / 2 - dbSize / 2;

            const currentX = startX + (endX - startX) * progress;

            drawRectangle(currentX, yPos, dbSize, dbSize, '#FF9800', '数据库', 'white');

            drawRectangle(startX - 50, yPos + 80, 160, 50, '#9C27B0', '手机应用', 'white');
            drawRectangle(canvas.width / 2 - 80, yPos + 80, 160, 50, '#2196F3', '桌面应用', 'white');
            drawRectangle(endX - 50, yPos + 80, 160, 50, '#E91E63', '服务器', 'white');

            drawText("数据库轻松在不同平台间移动", canvas.width / 2, 30, '#333', 20);

             // Loop animation
            if (progress >= 1) {
                startTime = null; // Reset startTime to loop
            }
        }

        function animateScalability(progress) {
            const baseSize = 30;
            const maxSize = 100;
            const currentSize = baseSize + (maxSize - baseSize) * progress;

            const circleX = canvas.width / 2;
            const circleY = canvas.height / 2;

            drawCircle(circleX, circleY, currentSize, '#795548', '数据库', 'white');

            drawText("数据库根据需求扩展或缩小", canvas.width / 2, 30, '#333', 20);
            drawText(`当前规模: ${Math.floor(currentSize)}`, canvas.width / 2, canvas.height - 30, '#333', 16);

             // Loop animation
            if (progress >= 1) {
                startTime = null; // Reset startTime to loop
            }
        }

        function drawDatabaseTypes() {
            const startY = 100;
            const gapY = 80;
            const boxWidth = 180;
            const boxHeight = 50;

            drawText("不同类型的嵌入式数据库及其特点", canvas.width / 2, 50, '#333', 22);

            // SQLite (File-based)
            drawRectangle(50, startY, boxWidth, boxHeight, '#8BC34A', 'SQLite', 'white');
            drawText("文件型数据库", 50 + boxWidth / 2, startY + boxHeight + 20, '#555', 16);
            drawRectangle(50, startY + gapY * 0.5, boxWidth * 0.3, boxHeight * 0.6, '#CDDC39', '', 'white'); // File icon
            drawText("存储在文件中", 50 + boxWidth / 2, startY + boxHeight + 40, '#555', 14);


            // Berkeley DB (File-based, example)
            drawRectangle(canvas.width - 50 - boxWidth, startY, boxWidth, boxHeight, '#4CAF50', 'Berkeley DB', 'white');
            drawText("文件型数据库", canvas.width - 50 - boxWidth / 2, startY + boxHeight + 20, '#555', 16);
            drawRectangle(canvas.width - 50 - boxWidth * 0.3, startY + gapY * 0.5, boxWidth * 0.3, boxHeight * 0.6, '#CDDC39', '', 'white'); // File icon
            drawText("存储在文件中", canvas.width - 50 - boxWidth / 2, startY + boxHeight + 40, '#555', 14);

            // Firebird (Network Database)
            drawRectangle(50, startY + gapY * 2, boxWidth, boxHeight, '#FFEB3B', 'Firebird', 'black');
            drawText("网络型数据库", 50 + boxWidth / 2, startY + gapY * 2 + boxHeight + 20, '#555', 16);
            drawCircle(50 + boxWidth / 2 - 30, startY + gapY * 2 + boxHeight + 40, 8, '#795548');
            drawCircle(50 + boxWidth / 2 + 30, startY + gapY * 2 + boxHeight + 40, 8, '#795548');
            drawArrow(50 + boxWidth / 2 - 20, startY + gapY * 2 + boxHeight + 40, 50 + boxWidth / 2 + 20, startY + gapY * 2 + boxHeight + 40, '#795548');
            drawText("通过网络访问", 50 + boxWidth / 2, startY + gapY * 2 + boxHeight + 60, '#555', 14);


            // eXtremeDB (In-memory)
            drawRectangle(canvas.width - 50 - boxWidth, startY + gapY * 2, boxWidth, boxHeight, '#FFC107', 'eXtremeDB', 'black');
            drawText("内存型数据库", canvas.width - 50 - boxWidth / 2, startY + gapY * 2 + boxHeight + 20, '#555', 16);
            drawCircle(canvas.width - 50 - boxWidth / 2, startY + gapY * 2 + boxHeight + 40, 15, '#F44336'); // RAM icon
            drawText("数据存储在内存中", canvas.width - 50 - boxWidth / 2, startY + gapY * 2 + boxHeight + 60, '#555', 14);

            // Oracle (Large Database - Not Embedded)
            drawRectangle(canvas.width / 2 - boxWidth / 2, startY + gapY * 3.5, boxWidth, boxHeight, '#F44336', 'Oracle', 'white');
            drawText("大型数据库 (非嵌入式)", canvas.width / 2, startY + gapY * 3.5 + boxHeight + 20, '#555', 16);
            drawText("通常用于大型企业系统", canvas.width / 2, startY + gapY * 3.5 + boxHeight + 40, '#555', 14);
        }

        // Initial canvas text
        drawText("点击上方按钮开始演示", canvas.width / 2, canvas.height / 2, '#888', 24);
    </script>
</body>
</html> 