<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络数据库系统(NDB)学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(10px);
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
            animation: shake 0.6s ease-in-out;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.8s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">网络数据库系统 (NDB)</h1>
            <p class="subtitle">通过动画和交互学习数据库系统概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🌐 什么是网络数据库系统？</h2>
            <div class="canvas-container">
                <canvas id="ndbCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <strong>网络数据库系统(NDB)</strong> 是基于4G/5G移动通信技术的数据库系统。
                想象一下：你的手机就像一个小助手，它通过网络连接到远程的大型数据库服务器。
                点击上方画布查看动画演示！
            </div>
            <button class="btn" onclick="startNDBAnimation()">🎬 播放NDB架构动画</button>
        </div>

        <div class="section">
            <h2 class="section-title">🏗️ NDB的三大组成部分</h2>
            <div class="canvas-container">
                <canvas id="componentsCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                NDB由三个核心部分组成：
                <br>1. <strong>客户端</strong> - 你的设备（手机、平板等）
                <br>2. <strong>通信协议</strong> - 数据传输的"语言"
                <br>3. <strong>远程服务器</strong> - 存储数据的"大脑"
            </div>
            <button class="btn" onclick="startComponentsAnimation()">🔧 演示组件交互</button>
        </div>

        <div class="section">
            <h2 class="section-title">🆚 NDB vs 文件数据库</h2>
            <div class="canvas-container">
                <canvas id="comparisonCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <strong>关键区别：</strong>
                <br>• <strong>NDB</strong>：数据存储在远程服务器，通过网络访问
                <br>• <strong>文件数据库</strong>：数据以文件形式存储在本地磁盘
                <br>题目中的D选项描述的是文件数据库，不是NDB！
            </div>
            <button class="btn" onclick="startComparisonAnimation()">📊 对比两种数据库</button>
        </div>

        <div class="section quiz-section">
            <h2 class="section-title">🎯 原题解析</h2>
            <div class="explanation" style="background: rgba(255,255,255,0.2); color: white;">
                <strong>题目：</strong>以下有关NDB的叙述中，不正确的是（ ）
            </div>

            <div class="option" onclick="selectOption(this, false)">
                <strong>A.</strong> NDB主要由客户端、通信协议和远程服务器等三部分组成
                <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.8;">
                    ✅ 正确！这是NDB的标准架构
                </div>
            </div>

            <div class="option" onclick="selectOption(this, false)">
                <strong>B.</strong> NDB的客户端主要负责提供接口给嵌入式程序，通信协议负责规范客户端与远程服务器之间的通信，远程服务器负责维护服务器上的数据库数据
                <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.8;">
                    ✅ 正确！准确描述了各组件的职责
                </div>
            </div>

            <div class="option" onclick="selectOption(this, false)">
                <strong>C.</strong> NDB具有客户端小、无需支持可剪裁性、代码可重用等特点
                <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.8;">
                    ✅ 正确！这些都是NDB的优势特点
                </div>
            </div>

            <div class="option" onclick="selectOption(this, true)">
                <strong>D.</strong> NDB是以文件方式存储数据库数据。即数据按照一定格式储存在磁盘中，使用时由应用程序通过相应的驱动程序甚至直接对数据文件进行读写
                <div style="margin-top: 10px; font-size: 0.9rem; opacity: 0.8;">
                    ❌ 错误！这是在描述<strong>文件数据库</strong>，不是NDB
                </div>
            </div>

            <div id="resultMessage" style="margin-top: 20px; padding: 20px; border-radius: 10px; display: none;"></div>
            <button class="btn" onclick="showThinkingProcess()">🧠 查看解题思路</button>
        </div>

        <div class="section" id="thinkingSection" style="display: none;">
            <h2 class="section-title">🤔 解题思路分析</h2>
            <div class="canvas-container">
                <canvas id="thinkingCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <strong>解题步骤：</strong>
                <br>1. <strong>理解题目</strong>：找出"不正确"的选项
                <br>2. <strong>分析概念</strong>：区分NDB和文件数据库
                <br>3. <strong>逐项检查</strong>：验证每个选项的正确性
                <br>4. <strong>识别陷阱</strong>：D选项是典型的"张冠李戴"
            </div>
            <button class="btn" onclick="startThinkingAnimation()">🎭 演示解题过程</button>
        </div>
    </div>

    <script>
        let currentStep = 0;
        const totalSteps = 4;

        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 15; i++) {
                const circle = document.createElement('div');
                circle.className = 'floating-circle';
                circle.style.width = Math.random() * 60 + 20 + 'px';
                circle.style.height = circle.style.width;
                circle.style.left = Math.random() * 100 + '%';
                circle.style.top = Math.random() * 100 + '%';
                circle.style.animationDelay = Math.random() * 6 + 's';
                circle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(circle);
            }
        }

        // 更新进度条
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // NDB架构动画
        function startNDBAnimation() {
            const canvas = document.getElementById('ndbCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#e3f2fd');
                gradient.addColorStop(1, '#bbdefb');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制5G信号塔
                ctx.fillStyle = '#2196F3';
                ctx.fillRect(380, 100, 40, 200);
                
                // 信号波纹动画
                for (let i = 0; i < 3; i++) {
                    ctx.strokeStyle = `rgba(33, 150, 243, ${0.8 - frame * 0.01 - i * 0.2})`;
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(400, 150, 50 + frame * 2 + i * 30, 0, Math.PI * 2);
                    ctx.stroke();
                }

                // 绘制客户端设备
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(100, 250, 80, 120);
                ctx.fillStyle = '#81C784';
                ctx.fillRect(110, 260, 60, 80);
                
                // 绘制远程服务器
                ctx.fillStyle = '#FF9800';
                ctx.fillRect(600, 200, 120, 150);
                ctx.fillStyle = '#FFB74D';
                ctx.fillRect(610, 210, 100, 130);

                // 数据传输动画
                const dataX = 200 + Math.sin(frame * 0.1) * 200;
                ctx.fillStyle = '#E91E63';
                ctx.beginPath();
                ctx.arc(dataX, 200, 8, 0, Math.PI * 2);
                ctx.fill();

                // 添加文字标签
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.fillText('客户端', 110, 390);
                ctx.fillText('5G网络', 370, 320);
                ctx.fillText('远程服务器', 620, 370);

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                } else {
                    currentStep = Math.max(currentStep, 1);
                    updateProgress();
                }
            }
            animate();
        }

        // 组件交互动画
        function startComponentsAnimation() {
            const canvas = document.getElementById('componentsCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                ctx.fillStyle = '#f5f5f5';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 客户端
                ctx.fillStyle = '#4CAF50';
                ctx.beginPath();
                ctx.arc(150, 200, 60, 0, Math.PI * 2);
                ctx.fill();

                // 服务器
                ctx.fillStyle = '#FF9800';
                ctx.beginPath();
                ctx.arc(650, 200, 60, 0, Math.PI * 2);
                ctx.fill();

                // 通信协议动画线条
                const progress = (Math.sin(frame * 0.1) + 1) / 2;
                ctx.strokeStyle = '#2196F3';
                ctx.lineWidth = 8;
                ctx.beginPath();
                ctx.moveTo(210, 200);
                ctx.lineTo(210 + (380 * progress), 200);
                ctx.stroke();

                // 数据包动画
                for (let i = 0; i < 5; i++) {
                    const x = 220 + i * 80 + Math.sin(frame * 0.05 + i) * 20;
                    const y = 200 + Math.cos(frame * 0.05 + i) * 10;
                    ctx.fillStyle = '#E91E63';
                    ctx.fillRect(x, y - 5, 15, 10);
                }

                // 标签
                ctx.fillStyle = '#333';
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.fillText('客户端', 120, 280);
                ctx.fillText('通信协议', 370, 180);
                ctx.fillText('远程服务器', 610, 280);

                frame++;
                if (frame < 150) {
                    requestAnimationFrame(animate);
                } else {
                    currentStep = Math.max(currentStep, 2);
                    updateProgress();
                }
            }
            animate();
        }

        // 对比动画
        function startComparisonAnimation() {
            const canvas = document.getElementById('comparisonCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景分割
                ctx.fillStyle = '#e8f5e8';
                ctx.fillRect(0, 0, canvas.width / 2, canvas.height);
                ctx.fillStyle = '#fff3e0';
                ctx.fillRect(canvas.width / 2, 0, canvas.width / 2, canvas.height);

                // 分割线
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.setLineDash([10, 5]);
                ctx.beginPath();
                ctx.moveTo(canvas.width / 2, 0);
                ctx.lineTo(canvas.width / 2, canvas.height);
                ctx.stroke();
                ctx.setLineDash([]);

                // NDB侧 - 网络架构
                ctx.fillStyle = '#4CAF50';
                ctx.beginPath();
                ctx.arc(100, 150, 30, 0, Math.PI * 2);
                ctx.fill();

                ctx.fillStyle = '#2196F3';
                ctx.fillRect(180, 120, 20, 60);

                ctx.fillStyle = '#FF9800';
                ctx.fillRect(280, 100, 60, 100);

                // 网络连接线
                const pulse = Math.sin(frame * 0.2) * 0.5 + 0.5;
                ctx.strokeStyle = `rgba(33, 150, 243, ${pulse})`;
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(130, 150);
                ctx.lineTo(280, 150);
                ctx.stroke();

                // 文件数据库侧
                ctx.fillStyle = '#795548';
                ctx.fillRect(450, 120, 80, 60);
                ctx.fillStyle = '#8D6E63';
                ctx.fillRect(460, 130, 60, 40);

                // 文件图标
                for (let i = 0; i < 3; i++) {
                    ctx.fillStyle = '#FFC107';
                    ctx.fillRect(580 + i * 25, 140 + i * 10, 20, 25);
                }

                // 标签
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.fillText('NDB (网络数据库)', 120, 50);
                ctx.fillText('文件数据库', 520, 50);

                ctx.font = '12px Microsoft YaHei';
                ctx.fillText('客户端', 80, 200);
                ctx.fillText('网络', 175, 200);
                ctx.fillText('服务器', 290, 220);
                ctx.fillText('本地存储', 470, 200);
                ctx.fillText('文件', 590, 200);

                frame++;
                if (frame < 120) {
                    requestAnimationFrame(animate);
                } else {
                    currentStep = Math.max(currentStep, 3);
                    updateProgress();
                }
            }
            animate();
        }

        // 选择选项
        function selectOption(element, isCorrect) {
            // 移除所有选项的状态
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 添加对应状态
            if (isCorrect) {
                element.classList.add('correct');
                showResult('🎉 恭喜答对了！D选项描述的是文件数据库，不是NDB。', 'success');
            } else {
                element.classList.add('wrong');
                showResult('❌ 再想想，题目问的是"不正确"的选项哦！', 'error');
            }
        }

        // 显示结果
        function showResult(message, type) {
            const resultDiv = document.getElementById('resultMessage');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = message;
            resultDiv.style.background = type === 'success' ?
                'rgba(76, 175, 80, 0.8)' : 'rgba(244, 67, 54, 0.8)';
        }

        // 显示解题思路
        function showThinkingProcess() {
            document.getElementById('thinkingSection').style.display = 'block';
            document.getElementById('thinkingSection').scrollIntoView({ behavior: 'smooth' });
        }

        // 解题思路动画
        function startThinkingAnimation() {
            const canvas = document.getElementById('thinkingCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            let step = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#f3e5f5');
                gradient.addColorStop(1, '#e1bee7');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 步骤指示器
                const steps = ['理解题目', '分析概念', '逐项检查', '识别陷阱'];
                const currentStep = Math.floor(frame / 60) % 4;

                for (let i = 0; i < 4; i++) {
                    const x = 100 + i * 150;
                    const y = 100;

                    // 步骤圆圈
                    ctx.fillStyle = i <= currentStep ? '#9C27B0' : '#E0E0E0';
                    ctx.beginPath();
                    ctx.arc(x, y, 30, 0, Math.PI * 2);
                    ctx.fill();

                    // 步骤数字
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 20px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(i + 1, x, y + 7);

                    // 步骤标题
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(steps[i], x, y + 60);

                    // 连接线
                    if (i < 3) {
                        ctx.strokeStyle = i < currentStep ? '#9C27B0' : '#E0E0E0';
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(x + 30, y);
                        ctx.lineTo(x + 120, y);
                        ctx.stroke();
                    }
                }

                // 详细说明区域
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.fillRect(50, 200, 700, 150);
                ctx.strokeStyle = '#9C27B0';
                ctx.lineWidth = 2;
                ctx.strokeRect(50, 200, 700, 150);

                // 当前步骤说明
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'left';

                const explanations = [
                    '题目问的是"不正确"的选项，要找出错误的描述',
                    'NDB是网络数据库，文件数据库是本地存储，两者不同',
                    'A、B、C选项都正确描述了NDB的特点和组成',
                    'D选项描述的是文件数据库特征，属于"张冠李戴"'
                ];

                const lines = explanations[currentStep].match(/.{1,30}/g) || [explanations[currentStep]];
                lines.forEach((line, index) => {
                    ctx.fillText(line, 70, 240 + index * 25);
                });

                // 闪烁效果
                if (frame % 30 < 15) {
                    ctx.fillStyle = 'rgba(156, 39, 176, 0.3)';
                    ctx.fillRect(50, 200, 700, 150);
                }

                frame++;
                if (frame < 240) {
                    requestAnimationFrame(animate);
                } else {
                    currentStep = Math.max(currentStep, 4);
                    updateProgress();
                }
            }
            animate();
        }

        // 初始化
        window.onload = function() {
            createFloatingElements();
            updateProgress();
        };
    </script>
</body>
</html>
