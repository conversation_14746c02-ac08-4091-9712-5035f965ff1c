<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PV操作与进程同步 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .content-section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8em;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
            transition: box-shadow 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
        }

        .explanation {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .process-box {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .process-waiting {
            background: #ffebee;
            color: #c62828;
            border: 2px solid #c62828;
        }

        .process-running {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #2e7d32;
            animation: pulse 1s infinite;
        }

        .process-finished {
            background: #e3f2fd;
            color: #1565c0;
            border: 2px solid #1565c0;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .semaphore-display {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .semaphore {
            background: white;
            border: 3px solid #667eea;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            min-width: 100px;
            transition: all 0.3s ease;
        }

        .semaphore.active {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .semaphore-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
        }

        .quiz-option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(10px);
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4caf50;
        }

        .quiz-option.wrong {
            background: rgba(244, 67, 54, 0.3);
            border-color: #f44336;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #4caf50;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 PV操作与进程同步</h1>
            <p>通过动画和交互学习操作系统中的进程同步机制</p>
        </div>

        <!-- 基础概念介绍 -->
        <div class="content-section">
            <h2 class="section-title">📚 基础概念</h2>
            <div class="explanation">
                <h3>什么是PV操作？</h3>
                <p><strong>P操作（Wait）</strong>：等待信号量，如果信号量>0则减1并继续执行，否则阻塞等待</p>
                <p><strong>V操作（Signal）</strong>：释放信号量，将信号量加1，唤醒等待的进程</p>
                <br>
                <h3>信号量的作用</h3>
                <p>信号量是一个整数变量，用于控制多个进程对共享资源的访问，实现进程间的同步与互斥。</p>
            </div>
        </div>

        <!-- 进程前趋图可视化 -->
        <div class="content-section">
            <h2 class="section-title">🔗 进程前趋图</h2>
            <div class="canvas-container">
                <canvas id="precedenceCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p>前趋图显示了进程之间的依赖关系：</p>
                <ul>
                    <li>P3必须等待P1和P2完成后才能执行</li>
                    <li>P4和P5必须等待P3完成后才能执行</li>
                    <li>箭头表示执行顺序的依赖关系</li>
                </ul>
            </div>
        </div>

        <!-- 信号量状态显示 -->
        <div class="content-section">
            <h2 class="section-title">🚦 信号量状态</h2>
            <div class="semaphore-display">
                <div class="semaphore" id="s1">
                    <div class="semaphore-value">0</div>
                    <div>S1</div>
                </div>
                <div class="semaphore" id="s2">
                    <div class="semaphore-value">0</div>
                    <div>S2</div>
                </div>
                <div class="semaphore" id="s3">
                    <div class="semaphore-value">0</div>
                    <div>S3</div>
                </div>
                <div class="semaphore" id="s4">
                    <div class="semaphore-value">0</div>
                    <div>S4</div>
                </div>
            </div>
        </div>

        <!-- 执行步骤演示 -->
        <div class="content-section">
            <h2 class="section-title">▶️ 执行步骤演示</h2>
            <div class="step-indicator">
                <div class="step" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
                <div class="step" id="step5">5</div>
            </div>
            <div class="canvas-container">
                <canvas id="executionCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn btn-secondary" onclick="resetAnimation()">🔄 重置</button>
                <button class="btn btn-secondary" onclick="nextStep()">➡️ 下一步</button>
            </div>
            <div id="stepExplanation" class="explanation">
                <h3>点击"开始演示"查看PV操作的执行过程</h3>
            </div>
        </div>

        <!-- 题目解析 -->
        <div class="quiz-section">
            <h2 class="section-title" style="color: white;">🎯 题目解析</h2>
            <div class="explanation" style="background: rgba(255,255,255,0.1); border-left-color: white;">
                <h3 style="color: white;">解题思路：</h3>
                <p>1. <strong>a和b位置</strong>：P1和P2执行完成后，需要通知P3可以开始执行</p>
                <p>2. <strong>c和d位置</strong>：P3开始执行前，需要等待P1和P2的完成信号</p>
                <p>3. <strong>e和f位置</strong>：P4和P5开始执行前，需要等待P3的完成信号</p>
            </div>
            
            <h3>问题1：a和b应分别填写什么？</h3>
            <div class="quiz-option" onclick="selectOption(this, false)">A. P(S1)和P(S2)</div>
            <div class="quiz-option" onclick="selectOption(this, false)">B. P(S1)和V(S2)</div>
            <div class="quiz-option" onclick="selectOption(this, true)">C. V(S1)和V(S2) ✓</div>
            <div class="quiz-option" onclick="selectOption(this, false)">D. V(S1)和P(S2)</div>
            
            <div id="answerExplanation" style="display: none; margin-top: 20px; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                <h4>正确答案解析：</h4>
                <p>当P1和P2执行完毕时，需要使用V操作来增加信号量的值，通知等待的P3进程。因此a处应填V(S1)，b处应填V(S2)。</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 0;
        let animationRunning = false;
        let semaphoreValues = {s1: 0, s2: 0, s3: 0, s4: 0};
        let processStates = {P1: 'waiting', P2: 'waiting', P3: 'waiting', P4: 'waiting', P5: 'waiting'};

        // 绘制前趋图
        function drawPrecedenceGraph() {
            const canvas = document.getElementById('precedenceCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 进程位置
            const processes = {
                P1: {x: 150, y: 100},
                P2: {x: 150, y: 250},
                P3: {x: 400, y: 175},
                P4: {x: 650, y: 100},
                P5: {x: 650, y: 250}
            };
            
            // 绘制箭头
            drawArrow(ctx, processes.P1.x + 30, processes.P1.y, processes.P3.x - 30, processes.P3.y - 20);
            drawArrow(ctx, processes.P2.x + 30, processes.P2.y, processes.P3.x - 30, processes.P3.y + 20);
            drawArrow(ctx, processes.P3.x + 30, processes.P3.y, processes.P4.x - 30, processes.P4.y + 20);
            drawArrow(ctx, processes.P3.x + 30, processes.P3.y, processes.P5.x - 30, processes.P5.y - 20);
            
            // 绘制进程圆圈
            Object.keys(processes).forEach(processName => {
                const pos = processes[processName];
                const state = processStates[processName];
                
                // 设置颜色
                if (state === 'running') {
                    ctx.fillStyle = '#4caf50';
                    ctx.strokeStyle = '#2e7d32';
                } else if (state === 'finished') {
                    ctx.fillStyle = '#2196f3';
                    ctx.strokeStyle = '#1565c0';
                } else {
                    ctx.fillStyle = '#f5f5f5';
                    ctx.strokeStyle = '#666';
                }
                
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(pos.x, pos.y, 30, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();
                
                // 绘制进程名称
                ctx.fillStyle = state === 'waiting' ? '#333' : 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(processName, pos.x, pos.y + 5);
            });
        }
        
        // 绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY) {
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }
        
        // 更新信号量显示
        function updateSemaphoreDisplay() {
            Object.keys(semaphoreValues).forEach(semId => {
                const element = document.getElementById(semId);
                const valueElement = element.querySelector('.semaphore-value');
                valueElement.textContent = semaphoreValues[semId];
                
                // 添加动画效果
                element.classList.remove('active');
                setTimeout(() => element.classList.add('active'), 100);
                setTimeout(() => element.classList.remove('active'), 600);
            });
        }
        
        // 开始动画演示
        function startAnimation() {
            if (animationRunning) return;
            
            animationRunning = true;
            currentStep = 0;
            resetStates();
            
            const steps = [
                () => executeStep1(),
                () => executeStep2(),
                () => executeStep3(),
                () => executeStep4(),
                () => executeStep5()
            ];
            
            function runNextStep() {
                if (currentStep < steps.length) {
                    updateStepIndicator();
                    steps[currentStep]();
                    currentStep++;
                    setTimeout(runNextStep, 2000);
                } else {
                    animationRunning = false;
                }
            }
            
            runNextStep();
        }
        
        // 执行步骤
        function executeStep1() {
            processStates.P1 = 'running';
            processStates.P2 = 'running';
            drawPrecedenceGraph();
            updateStepExplanation('步骤1：P1和P2开始并发执行');
        }
        
        function executeStep2() {
            processStates.P1 = 'finished';
            processStates.P2 = 'finished';
            semaphoreValues.s1 = 1; // V(S1)
            semaphoreValues.s2 = 1; // V(S2)
            drawPrecedenceGraph();
            updateSemaphoreDisplay();
            updateStepExplanation('步骤2：P1执行V(S1)，P2执行V(S2)，通知P3可以开始');
        }
        
        function executeStep3() {
            processStates.P3 = 'running';
            semaphoreValues.s1 = 0; // P(S1)
            semaphoreValues.s2 = 0; // P(S2)
            drawPrecedenceGraph();
            updateSemaphoreDisplay();
            updateStepExplanation('步骤3：P3执行P(S1)和P(S2)，获得执行权限并开始运行');
        }
        
        function executeStep4() {
            processStates.P3 = 'finished';
            semaphoreValues.s3 = 1; // V(S3)
            semaphoreValues.s4 = 1; // V(S4)
            drawPrecedenceGraph();
            updateSemaphoreDisplay();
            updateStepExplanation('步骤4：P3执行V(S3)和V(S4)，通知P4和P5可以开始');
        }
        
        function executeStep5() {
            processStates.P4 = 'running';
            processStates.P5 = 'running';
            semaphoreValues.s3 = 0; // P(S3)
            semaphoreValues.s4 = 0; // P(S4)
            drawPrecedenceGraph();
            updateSemaphoreDisplay();
            updateStepExplanation('步骤5：P4执行P(S3)，P5执行P(S4)，开始并发执行');
            
            setTimeout(() => {
                processStates.P4 = 'finished';
                processStates.P5 = 'finished';
                drawPrecedenceGraph();
                updateStepExplanation('所有进程执行完毕！');
            }, 1000);
        }
        
        // 更新步骤指示器
        function updateStepIndicator() {
            for (let i = 1; i <= 5; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active', 'completed');
                
                if (i === currentStep + 1) {
                    step.classList.add('active');
                } else if (i <= currentStep) {
                    step.classList.add('completed');
                }
            }
        }
        
        // 更新步骤说明
        function updateStepExplanation(text) {
            document.getElementById('stepExplanation').innerHTML = `<h3>${text}</h3>`;
        }
        
        // 重置动画
        function resetAnimation() {
            animationRunning = false;
            currentStep = 0;
            resetStates();
            drawPrecedenceGraph();
            updateSemaphoreDisplay();
            updateStepIndicator();
            updateStepExplanation('点击"开始演示"查看PV操作的执行过程');
        }
        
        // 重置状态
        function resetStates() {
            processStates = {P1: 'waiting', P2: 'waiting', P3: 'waiting', P4: 'waiting', P5: 'waiting'};
            semaphoreValues = {s1: 0, s2: 0, s3: 0, s4: 0};
        }
        
        // 下一步
        function nextStep() {
            if (!animationRunning && currentStep < 5) {
                updateStepIndicator();
                switch(currentStep) {
                    case 0: executeStep1(); break;
                    case 1: executeStep2(); break;
                    case 2: executeStep3(); break;
                    case 3: executeStep4(); break;
                    case 4: executeStep5(); break;
                }
                currentStep++;
            }
        }
        
        // 选择答案
        function selectOption(element, isCorrect) {
            // 移除所有选项的样式
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });
            
            // 添加相应样式
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('answerExplanation').style.display = 'block';
            } else {
                element.classList.add('wrong');
                // 高亮正确答案
                document.querySelectorAll('.quiz-option')[2].classList.add('correct');
                document.getElementById('answerExplanation').style.display = 'block';
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            drawPrecedenceGraph();
            updateSemaphoreDisplay();
        });
    </script>
</body>
</html>
