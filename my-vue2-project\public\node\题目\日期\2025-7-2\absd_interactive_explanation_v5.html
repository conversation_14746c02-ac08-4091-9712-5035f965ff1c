<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式理解：基于架构的软件设计 (ABSD)</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', 'Helvetica Neue', sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            overflow: hidden;
        }
        .container {
            width: 95%;
            max-width: 1000px;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .header {
            background-color: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 1.8em;
        }
        .main-content {
            display: flex;
            flex-direction: row;
            height: 600px; /* Fixed height for layout */
        }
        .question-panel {
            flex-basis: 35%;
            padding: 20px;
            border-right: 1px solid #e0e0e0;
            overflow-y: auto;
            background: #fafafa;
        }
        .question-panel h2 {
            color: #4a90e2;
            border-bottom: 2px solid #4a90e2;
            padding-bottom: 10px;
        }
        .question-panel p {
            line-height: 1.8;
        }
        .question-panel .correct-answer {
            background-color: #e8f5e9;
            border-left: 5px solid #4CAF50;
            padding: 15px;
            margin-top: 20px;
            font-weight: bold;
        }
        .interactive-panel {
            flex-basis: 65%;
            display: flex;
            flex-direction: column;
            padding: 0;
        }
        #absdCanvas {
            width: 100%;
            height: 100%;
            background: #fff;
        }
        .controls {
            padding: 15px;
            text-align: center;
            border-top: 1px solid #e0e0e0;
            background: #fdfdfd;
        }
        button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1em;
            border-radius: 25px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin: 0 10px;
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }
        button:hover {
            background-color: #357ABD;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .explanation-text {
            padding: 15px 20px;
            background: #e7f3ff;
            text-align: center;
            font-size: 0.95em;
            color: #1c5a9b;
        }
        .explanation-text p {
            margin: 0;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h1>交互式理解：基于架构的软件设计 (ABSD)</h1>
    </div>
    <div class="main-content">
        <div class="question-panel">
            <h2>原题回顾</h2>
            <p>
                某公司采用基于架构的软件设计 (Architecture-Based Software Design, ABSD) 方法进行软件设计与开发。ABSD方法有三个基础，分别是<strong>对系统进行功能分解</strong>、采用 ( <strong>架构风格</strong> ) 实现质量属性与商业需求、<strong>采用软件模板设计软件结构</strong>。ABSD方法主要包括架构需求等6个主要活动，其中 ( <strong>架构复审</strong> ) 活动的目标是标识潜在的风险，及早发现架构设计中的缺陷和错误；( <strong>架构演化</strong> ) 活动针对用户的需求变化，修改应用架构，满足新的需求。
            </p>
            <div class="correct-answer">
                解析：题目中提到的"实现质量属性与商业需求"，其依据是"架构风格"。而架构文档化的主要输出是"架构规格说明书"和"架构质量说明书"。因此，在文档层面上，是通过<strong>架构质量说明书</strong>来描述质量属性的。
            </div>
        </div>
        <div class="interactive-panel">
            <div class="explanation-text" id="explanationText">
                <p>点击"开始演示"来学习ABSD的核心概念。</p>
            </div>
            <canvas id="absdCanvas"></canvas>
            <div class="controls">
                <button id="prevBtn" disabled>上一步</button>
                <button id="nextBtn">开始演示</button>
            </div>
        </div>
    </div>
</div>

<script>
    const canvas = document.getElementById('absdCanvas');
    const ctx = canvas.getContext('2d');
    const explanationText = document.getElementById('explanationText').querySelector('p');
    const nextBtn = document.getElementById('nextBtn');
    const prevBtn = document.getElementById('prevBtn');

    let parent = canvas.parentElement;
    canvas.width = parent.clientWidth;
    canvas.height = parent.clientHeight;

    window.addEventListener('resize', () => {
        canvas.width = parent.clientWidth;
        canvas.height = parent.clientHeight;
        draw();
    });

    let currentStep = 0;
    const totalSteps = 6;

    const lerp = (a, b, t) => a + (b - a) * t;

    class AnimatedText {
        constructor(text, x, y, targetAlpha = 1, size = 16, color = '#333') {
            this.text = text;
            this.x = x;
            this.y = y;
            this.alpha = 0;
            this.targetAlpha = targetAlpha;
            this.size = size;
            this.color = color;
        }
        update() {
            this.alpha = lerp(this.alpha, this.targetAlpha, 0.05);
        }
        draw(context) {
            context.save();
            context.globalAlpha = this.alpha;
            context.fillStyle = this.color;
            context.font = `${this.size}px 'Microsoft YaHei'`;
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillText(this.text, this.x, this.y);
            context.restore();
        }
    }

    class AnimatedBox extends AnimatedText {
        constructor(text, x, y, width, height, color, textColor = '#fff') {
            super(text, x, y, 1, 16, textColor);
            this.width = width;
            this.height = height;
            this.boxColor = color;
            this.scale = 0;
            this.targetScale = 1;
        }
        update() {
            super.update();
            this.scale = lerp(this.scale, this.targetScale, 0.07);
        }
        draw(context) {
            context.save();
            context.translate(this.x, this.y);
            context.scale(this.scale, this.scale);
            
            context.fillStyle = this.boxColor;
            context.globalAlpha = this.alpha;
            context.beginPath();
            context.roundRect(-this.width / 2, -this.height / 2, this.width, this.height, 10);
            context.fill();
            
            context.fillStyle = this.color;
            context.font = `${this.size}px 'Microsoft YaHei'`;
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            context.fillText(this.text, 0, 0);
            
            context.restore();
        }
    }

    let elements = [];
    
    function setupStep(step) {
        elements = [];
        let w = canvas.width;
        let h = canvas.height;

        switch (step) {
            case 0:
                explanationText.textContent = 'ABSD(基于架构的软件设计)是一种重要的方法。我们先从它的三个基础开始。';
                elements.push(new AnimatedText('ABSD: 基于架构的软件设计', w / 2, h / 2, 1, 28, '#4a90e2'));
                break;
            case 1:
                explanationText.textContent = 'ABSD有三大基础，它们是架构设计的"地基"。';
                elements.push(new AnimatedBox('功能分解', w / 2 - 180, h / 2, 140, 80, '#f39c12'));
                elements.push(new AnimatedBox('架构风格', w / 2, h / 2, 140, 80, '#3498db'));
                elements.push(new AnimatedBox('软件模板', w / 2 + 180, h / 2, 140, 80, '#9b59b6'));
                elements.push(new AnimatedText('-> 实现质量与业务需求', w / 2, h/2 + 70, 1, 14, '#2c3e50'));
                break;
            case 2:
                explanationText.textContent = '在此基础上，ABSD包含6个主要活动，构成一个循环的生命周期。';
                const radius = Math.min(w, h) / 3.5;
                const activities = ['架构需求', '架构设计', '架构文档化', '架构复审', '架构实现', '架构演化'];
                activities.forEach((act, i) => {
                    const angle = (i / activities.length) * 2 * Math.PI - Math.PI / 2;
                    const x = w / 2 + radius * Math.cos(angle);
                    const y = h / 2 + radius * Math.sin(angle);
                    elements.push(new AnimatedBox(act, x, y, 110, 50, '#1abc9c'));
                });
                break;
            case 3:
                explanationText.textContent = '【架构复审】：就像给设计稿"体检"，目标是尽早发现潜在的风险和错误。';
                // Highlight '架构复审'
                const radius3 = Math.min(w, h) / 3.5;
                const activities3 = ['架构需求', '架构设计', '架构文档化', '架构复审', '架构实现', '架构演化'];
                activities3.forEach((act, i) => {
                    const angle = (i / activities3.length) * 2 * Math.PI - Math.PI / 2;
                    const x = w / 2 + radius3 * Math.cos(angle);
                    const y = h / 2 + radius3 * Math.sin(angle);
                    const color = act === '架构复审' ? '#e74c3c' : '#bdc3c7';
                    const box = new AnimatedBox(act, x, y, 110, 50, color);
                    elements.push(box);
                    if(act === '架构复审') {
                        elements.push(new AnimatedText('🔍 发现风险、缺陷', box.x, box.y + 50, 1, 14, '#c0392b'));
                    }
                });
                break;
            case 4:
                explanationText.textContent = '【架构演化】：软件不是一成不变的，需要根据用户的新需求进行修改和升级。';
                 const radius4 = Math.min(w, h) / 3.5;
                const activities4 = ['架构需求', '架构设计', '架构文档化', '架构复审', '架构实现', '架构演化'];
                activities4.forEach((act, i) => {
                    const angle = (i / activities4.length) * 2 * Math.PI - Math.PI / 2;
                    const x = w / 2 + radius4 * Math.cos(angle);
                    const y = h / 2 + radius4 * Math.sin(angle);
                    const color = act === '架构演化' ? '#e67e22' : '#bdc3c7';
                     const box = new AnimatedBox(act, x, y, 110, 50, color);
                    elements.push(box);
                     if(act === '架构演化') {
                        elements.push(new AnimatedText('🔧 响应需求变化', box.x, box.y + 50, 1, 14, '#d35400'));
                    }
                });
                break;
            case 5:
                explanationText.textContent = '【架构文档化】：这是关键一步！它将设计思想固化为文档，主要产出两种说明书。';
                const radius5 = Math.min(w, h) / 3.5;
                const activities5 = ['架构需求', '架构设计', '架构文档化', '架构复审', '架构实现', '架构演化'];
                 activities5.forEach((act, i) => {
                    const angle = (i / activities5.length) * 2 * Math.PI - Math.PI / 2;
                    const x = w / 2 + radius5 * Math.cos(angle);
                    const y = h / 2 + radius5 * Math.sin(angle);
                    const color = act === '架构文档化' ? '#2980b9' : '#bdc3c7';
                    elements.push(new AnimatedBox(act, x, y, 110, 50, color));
                });
                 // Find the documentation box
                const docBox = elements.find(e => e.text === '架构文档化');
                if(docBox) {
                    elements.push(new AnimatedBox('架构规格说明书', docBox.x - 100, docBox.y + 80, 150, 60, '#34495e'));
                    elements.push(new AnimatedBox('架构质量说明书', docBox.x + 100, docBox.y + 80, 150, 60, '#4CAF50'));
                    elements.push(new AnimatedText('↑ 这份文档描述了"质量属性"，回应了题目的问题', docBox.x + 100, docBox.y + 125, 1, 14, '#16a085'));
                }
                break;
            case 6:
                explanationText.textContent = '总结：ABSD通过一系列活动，将抽象的"质量需求"落实到具体的"质量说明书"中。';
                 elements.push(new AnimatedText('恭喜你！已完成ABSD核心概念学习', w / 2, h / 2, 1, 22, '#4a90e2'));
                break;

        }
        animate();
    }

    function updateButtons() {
        if (currentStep === 0) {
            prevBtn.disabled = true;
            nextBtn.textContent = '开始演示';
        } else {
            prevBtn.disabled = false;
            nextBtn.textContent = '下一步';
        }

        if (currentStep === totalSteps) {
            nextBtn.textContent = '重新开始';
        }
    }

    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        elements.forEach(el => el.draw(ctx));
    }

    function animate() {
        elements.forEach(el => el.update());
        draw();
        
        // continue animation if not stable
        let isStable = elements.every(el => Math.abs(el.alpha - el.targetAlpha) < 0.01 && (el.scale === undefined || Math.abs(el.scale - el.targetScale) < 0.01));

        if (!isStable) {
            requestAnimationFrame(animate);
        }
    }
    
    nextBtn.addEventListener('click', () => {
        if (currentStep === totalSteps) {
            currentStep = 0;
        } else {
            currentStep++;
        }
        setupStep(currentStep);
        updateButtons();
    });

    prevBtn.addEventListener('click', () => {
        if (currentStep > 0) {
            currentStep--;
            setupStep(currentStep);
            updateButtons();
        }
    });
    
    // Initial call
    updateButtons();
    setupStep(0);

</script>
</body>
</html> 