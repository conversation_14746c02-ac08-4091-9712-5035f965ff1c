<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OMG IDL学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .idl-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .idl-demo {
            text-align: center;
            margin: 30px 0;
        }

        #idlCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .element-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 25px 0;
        }

        .element-btn {
            padding: 15px 10px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .module-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .interface-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .type-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .constant-btn {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }

        .exception-btn {
            background: linear-gradient(45deg, #a29bfe, #6c5ce7);
        }

        .value-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .element-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .element-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .element-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .element-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .element-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .element-card.core {
            border-color: #fd79a8;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .element-card.mapping {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .element-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .element-card p {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-interface {
            color: #e84393;
            font-weight: bold;
            background: rgba(253,121,168,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .highlight-module {
            color: #0984e3;
            font-weight: bold;
            background: rgba(116,185,255,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-idl {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            animation: floatIDL 16s infinite ease-in-out;
        }

        .idl1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .idl2 {
            top: 70%;
            right: 15%;
            animation-delay: 5s;
        }

        .idl3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatIDL {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-25px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .element-controls {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .element-cards {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-idl idl1"></div>
        <div class="floating-idl idl2"></div>
        <div class="floating-idl idl3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>🔧 OMG IDL学习</h1>
            <p>深度理解接口定义语言的六种元素及其核心内容</p>
        </div>

        <div class="main-grid">
            <div class="idl-section">
                <h2 class="section-title">📄 IDL文件元素演示</h2>
                
                <div class="idl-demo">
                    <canvas id="idlCanvas" width="700" height="350"></canvas>
                </div>

                <div class="element-controls">
                    <button class="element-btn module-btn" onclick="demonstrateElement('module')">
                        模块定义<br><small>Module</small>
                    </button>
                    <button class="element-btn interface-btn" onclick="demonstrateElement('interface')">
                        接口描述<br><small>Interface</small>
                    </button>
                    <button class="element-btn type-btn" onclick="demonstrateElement('type')">
                        类型定义<br><small>Type</small>
                    </button>
                    <button class="element-btn constant-btn" onclick="demonstrateElement('constant')">
                        常量定义<br><small>Constant</small>
                    </button>
                    <button class="element-btn exception-btn" onclick="demonstrateElement('exception')">
                        异常<br><small>Exception</small>
                    </button>
                    <button class="element-btn value-btn" onclick="demonstrateElement('value')">
                        值类型<br><small>Value Type</small>
                    </button>
                </div>

                <div class="element-cards">
                    <div class="element-card core">
                        <h3>🎯 接口描述 (Interface)</h3>
                        <p><strong>IDL文件最核心的内容</strong><br>定义对象的操作和属性<br>描述服务的接口契约</p>
                    </div>
                    <div class="element-card mapping">
                        <h3>📦 模块定义 (Module)</h3>
                        <p><strong>映射为包/命名空间</strong><br>Java中的Package<br>C++中的Namespace</p>
                    </div>
                    <div class="element-card">
                        <h3>🏷️ 类型定义 (Type)</h3>
                        <p>定义数据类型<br>结构体、联合体等<br>支持复杂数据结构</p>
                    </div>
                    <div class="element-card">
                        <h3>📊 常量定义 (Constant)</h3>
                        <p>定义常量值<br>编译时确定<br>提供配置参数</p>
                    </div>
                    <div class="element-card">
                        <h3>⚠️ 异常 (Exception)</h3>
                        <p>定义异常类型<br>错误处理机制<br>异常传播规则</p>
                    </div>
                    <div class="element-card">
                        <h3>💎 值类型 (Value Type)</h3>
                        <p>值语义对象<br>可序列化传输<br>支持继承关系</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 OMG接口定义语言IDL文件包含了六种不同的元素，（　　）是一个IDL文件最核心的内容，<strong>（请作答此空）将映射为Java语言中的包(Package)或C++语言中的命名空间(Namespace)</strong>。
                </div>

                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        A. 模块定义
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 消息结构
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. 接口描述
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        D. 值类型
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：A. 模块定义</strong></p>
                    <p>根据题目的两个空格：</p>
                    <ul>
                        <li><strong>第一空</strong>：<span class="highlight-interface">接口描述 (Interface)</span> - IDL文件最核心的内容</li>
                        <li><strong>第二空</strong>：<span class="highlight-module">模块定义 (Module)</span> - 映射为Java的Package或C++的Namespace</li>
                    </ul>
                    <p>OMG接口定义语言IDL文件包含六种元素：</p>
                    <ul>
                        <li><span class="highlight-module">模块定义 (Module)</span>：
                            <br>• <strong>映射为Java的Package或C++的Namespace</strong>
                            <br>• 提供命名空间功能
                            <br>• 组织和管理IDL定义
                            <br>• 避免命名冲突</li>
                        <li><strong>类型定义 (Type)</strong>：定义数据类型和结构</li>
                        <li><strong>常量定义 (Constant)</strong>：定义编译时常量</li>
                        <li><strong>异常 (Exception)</strong>：定义异常类型和处理</li>
                        <li><span class="highlight-interface">接口描述 (Interface)</span>：IDL文件最核心的内容</li>
                        <li><strong>值类型 (Value Type)</strong>：值语义对象定义</li>
                    </ul>
                    <p><strong>关键理解</strong>：题目问的是第二空，即映射为包/命名空间的是模块定义。</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了模块定义的映射机制！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('idlCanvas');
        const ctx = canvas.getContext('2d');
        let currentElement = 'interface';
        let animationId = null;

        // 演示不同IDL元素
        function demonstrateElement(elementType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            currentElement = elementType;
            
            // 更新按钮状态
            document.querySelectorAll('.element-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${elementType}-btn`).classList.add('active');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(elementType) {
                case 'module':
                    drawModuleElement();
                    break;
                case 'interface':
                    drawInterfaceElement();
                    break;
                case 'type':
                    drawTypeElement();
                    break;
                case 'constant':
                    drawConstantElement();
                    break;
                case 'exception':
                    drawExceptionElement();
                    break;
                case 'value':
                    drawValueElement();
                    break;
            }
        }

        // 绘制模块定义
        function drawModuleElement() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('模块定义 - 命名空间映射', 350, 40);

            // IDL模块
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(100, 80, 200, 120);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(100, 80, 200, 120);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('IDL Module', 200, 110);
            ctx.font = '12px Arial';
            ctx.fillText('module MyModule {', 200, 130);
            ctx.fillText('  // 接口定义', 200, 150);
            ctx.fillText('  // 类型定义', 200, 170);
            ctx.fillText('};', 200, 190);

            // 映射箭头
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(300, 140);
            ctx.lineTo(380, 140);
            ctx.stroke();
            
            // 箭头头部
            ctx.fillStyle = '#0984e3';
            ctx.beginPath();
            ctx.moveTo(380, 140);
            ctx.lineTo(370, 135);
            ctx.lineTo(370, 145);
            ctx.closePath();
            ctx.fill();

            // Java Package
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(400, 100, 120, 80);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 2;
            ctx.strokeRect(400, 100, 120, 80);
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('Java Package', 460, 125);
            ctx.font = '10px Arial';
            ctx.fillText('package MyModule;', 460, 145);
            ctx.fillText('public class...', 460, 165);

            // C++ Namespace
            ctx.fillStyle = '#a29bfe';
            ctx.fillRect(540, 100, 120, 80);
            ctx.strokeStyle = '#6c5ce7';
            ctx.lineWidth = 2;
            ctx.strokeRect(540, 100, 120, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('C++ Namespace', 600, 125);
            ctx.font = '10px Arial';
            ctx.fillText('namespace MyModule {', 600, 145);
            ctx.fillText('  class...', 600, 165);

            // 连接线
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(380, 120);
            ctx.lineTo(400, 120);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(380, 160);
            ctx.lineTo(540, 140);
            ctx.stroke();

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('模块定义映射为不同语言的命名空间机制', 350, 250);
        }

        // 绘制接口描述（核心）
        function drawInterfaceElement() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('接口描述 - IDL文件核心', 350, 40);

            // 核心标识
            ctx.fillStyle = '#e17055';
            ctx.fillRect(250, 60, 200, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 60, 200, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('🎯 最核心内容', 350, 85);

            // 接口定义
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(200, 120, 300, 150);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(200, 120, 300, 150);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('Interface Definition', 350, 150);
            
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('interface Calculator {', 220, 170);
            ctx.fillText('  long add(in long a, in long b);', 220, 190);
            ctx.fillText('  long subtract(in long a, in long b);', 220, 210);
            ctx.fillText('  double divide(in double a, in double b)', 220, 230);
            ctx.fillText('    raises(DivisionByZero);', 220, 250);
            ctx.fillText('};', 220, 270);

            // 接口组成部分
            const parts = [
                {x: 50, y: 120, name: '操作定义', desc: 'Operations'},
                {x: 550, y: 120, name: '属性定义', desc: 'Attributes'},
                {x: 50, y: 200, name: '异常声明', desc: 'Exceptions'},
                {x: 550, y: 200, name: '继承关系', desc: 'Inheritance'}
            ];

            parts.forEach(part => {
                ctx.fillStyle = '#e84393';
                ctx.fillRect(part.x, part.y, 100, 60);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(part.x, part.y, 100, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(part.name, part.x + 50, part.y + 25);
                ctx.font = '10px Arial';
                ctx.fillText(part.desc, part.x + 50, part.y + 45);
                
                // 连接线
                ctx.strokeStyle = '#fd79a8';
                ctx.lineWidth = 2;
                ctx.beginPath();
                if (part.x < 300) {
                    ctx.moveTo(part.x + 100, part.y + 30);
                    ctx.lineTo(200, 195);
                } else {
                    ctx.moveTo(part.x, part.y + 30);
                    ctx.lineTo(500, 195);
                }
                ctx.stroke();
            });

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('接口描述定义了对象的完整契约', 350, 310);
        }

        // 绘制类型定义
        function drawTypeElement() {
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('类型定义 - 数据结构', 350, 40);

            // 基本类型
            ctx.fillStyle = '#00b894';
            ctx.fillRect(50, 80, 150, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 80, 150, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('基本类型', 125, 105);
            ctx.font = '11px Arial';
            ctx.fillText('boolean', 125, 125);
            ctx.fillText('char, string', 125, 140);
            ctx.fillText('short, long', 125, 155);
            ctx.fillText('float, double', 125, 170);

            // 复合类型
            ctx.fillStyle = '#00b894';
            ctx.fillRect(250, 80, 200, 100);
            ctx.strokeRect(250, 80, 200, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('复合类型', 350, 105);
            ctx.font = '11px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('struct Person {', 270, 125);
            ctx.fillText('  string name;', 270, 140);
            ctx.fillText('  long age;', 270, 155);
            ctx.fillText('};', 270, 170);

            // 集合类型
            ctx.fillStyle = '#00b894';
            ctx.fillRect(500, 80, 150, 100);
            ctx.strokeRect(500, 80, 150, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('集合类型', 575, 105);
            ctx.font = '11px Arial';
            ctx.fillText('sequence<T>', 575, 125);
            ctx.fillText('array[size]', 575, 140);
            ctx.fillText('union', 575, 155);
            ctx.fillText('enum', 575, 170);

            // 类型映射
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(200, 220, 300, 80);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 2;
            ctx.strokeRect(200, 220, 300, 80);
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('类型映射到目标语言', 350, 245);
            ctx.font = '12px Arial';
            ctx.fillText('IDL → Java/C++/Python等', 350, 270);
            ctx.fillText('保持类型安全和一致性', 350, 290);
        }

        // 绘制常量定义
        function drawConstantElement() {
            ctx.fillStyle = '#fdcb6e';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('常量定义 - 编译时常量', 350, 40);

            // 常量示例
            const constants = [
                {name: 'const long MAX_SIZE = 1024;', x: 100, y: 100},
                {name: 'const string VERSION = "1.0";', x: 100, y: 140},
                {name: 'const boolean DEBUG = TRUE;', x: 100, y: 180},
                {name: 'const double PI = 3.14159;', x: 100, y: 220}
            ];

            constants.forEach(constant => {
                ctx.fillStyle = '#fdcb6e';
                ctx.fillRect(constant.x, constant.y, 300, 30);
                ctx.strokeStyle = '#e17055';
                ctx.lineWidth = 1;
                ctx.strokeRect(constant.x, constant.y, 300, 30);
                
                ctx.fillStyle = '#2d3436';
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(constant.name, constant.x + 10, constant.y + 20);
            });

            // 特点说明
            ctx.fillStyle = '#e17055';
            ctx.fillRect(450, 100, 200, 150);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(450, 100, 200, 150);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('常量特点', 550, 125);
            ctx.font = '12px Arial';
            ctx.fillText('• 编译时确定', 550, 150);
            ctx.fillText('• 类型安全', 550, 170);
            ctx.fillText('• 全局可见', 550, 190);
            ctx.fillText('• 配置参数', 550, 210);
            ctx.fillText('• 版本控制', 550, 230);

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('常量定义提供编译时配置和参数', 350, 290);
        }

        // 绘制异常
        function drawExceptionElement() {
            ctx.fillStyle = '#a29bfe';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('异常 - 错误处理机制', 350, 40);

            // 异常定义
            ctx.fillStyle = '#a29bfe';
            ctx.fillRect(150, 80, 400, 120);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(150, 80, 400, 120);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('Exception Definition', 350, 110);
            
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('exception DivisionByZero {', 170, 130);
            ctx.fillText('  string reason;', 170, 150);
            ctx.fillText('  long errorCode;', 170, 170);
            ctx.fillText('};', 170, 190);

            // 异常使用
            ctx.fillStyle = '#6c5ce7';
            ctx.fillRect(50, 220, 250, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 220, 250, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('异常声明', 175, 245);
            ctx.font = '10px Arial';
            ctx.fillText('double divide(...) raises(DivisionByZero);', 175, 270);
            ctx.fillText('操作可能抛出的异常', 175, 290);

            // 异常处理
            ctx.fillStyle = '#6c5ce7';
            ctx.fillRect(350, 220, 250, 80);
            ctx.strokeRect(350, 220, 250, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('异常处理', 475, 245);
            ctx.font = '10px Arial';
            ctx.fillText('try { ... } catch(DivisionByZero ex)', 475, 270);
            ctx.fillText('客户端异常捕获', 475, 290);

            // 连接线
            ctx.strokeStyle = '#a29bfe';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(350, 200);
            ctx.lineTo(175, 220);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(350, 200);
            ctx.lineTo(475, 220);
            ctx.stroke();
        }

        // 绘制值类型
        function drawValueElement() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('值类型 - 值语义对象', 350, 40);

            // 值类型定义
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(200, 80, 300, 120);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(200, 80, 300, 120);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('Value Type Definition', 350, 110);
            
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('valuetype Address {', 220, 130);
            ctx.fillText('  public string street;', 220, 150);
            ctx.fillText('  public string city;', 220, 170);
            ctx.fillText('  public long zipCode;', 220, 190);
            ctx.fillText('};', 220, 210);

            // 值类型特性
            const features = [
                {x: 50, y: 230, name: '值语义', desc: '按值传递'},
                {x: 200, y: 230, name: '可序列化', desc: '网络传输'},
                {x: 350, y: 230, name: '支持继承', desc: '面向对象'},
                {x: 500, y: 230, name: '状态封装', desc: '数据+行为'}
            ];

            features.forEach(feature => {
                ctx.fillStyle = '#e84393';
                ctx.fillRect(feature.x, feature.y, 120, 60);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(feature.x, feature.y, 120, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(feature.name, feature.x + 60, feature.y + 25);
                ctx.font = '10px Arial';
                ctx.fillText(feature.desc, feature.x + 60, feature.y + 45);
            });

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('值类型支持复杂数据的值语义传递', 350, 320);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('C. 接口描述')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画
                    demonstrateElement('interface');
                    setTimeout(() => demonstrateElement('module'), 3000);
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateElement('interface');
            
            // 自动演示序列
            setTimeout(() => demonstrateElement('module'), 4000);
            setTimeout(() => demonstrateElement('type'), 8000);
            setTimeout(() => demonstrateElement('interface'), 12000);
        };
    </script>
</body>
</html>
