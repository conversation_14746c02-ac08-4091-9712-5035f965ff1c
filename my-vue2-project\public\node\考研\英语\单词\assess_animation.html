<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Assess</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f7ff;
            flex-direction: column;
        }
        #canvas-container {
            border: 2px solid #007bff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        canvas {
            display: block;
            background-color: #fff;
            border-radius: 8px;
        }
        #controls {
            text-align: center;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
        }
        #explanation {
            margin-top: 15px;
            font-size: 18px;
            color: #333;
            text-align: center;
            padding: 10px;
            max-width: 800px;
        }
        h1 {
            color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>单词: Assess (评估, 评定)</h1>
    <div id="canvas-container">
        <canvas id="word-canvas" width="800" height="400"></canvas>
    </div>
    <div id="controls">
        <button id="prefix-btn">1. 词缀 'as-'</button>
        <button id="root-btn">2. 词根 'sess'</button>
        <button id="combine-btn">3. 组合 'assess'</button>
        <button id="reset-btn">重置</button>
    </div>
    <div id="explanation">
        <strong>故事：</strong>想象一位顾问走向(as-)法官席，并坐下(sess)帮助评估案件。这就是"assess"的来源。
    </div>

    <script>
        const canvas = document.getElementById('word-canvas');
        const ctx = canvas.getContext('2d');
        const explanationDiv = document.getElementById('explanation');

        const prefixBtn = document.getElementById('prefix-btn');
        const rootBtn = document.getElementById('root-btn');
        const combineBtn = document.getElementById('combine-btn');
        const resetBtn = document.getElementById('reset-btn');

        let character = { x: 50, y: 250, width: 40, height: 80 };
        let desk = { x: 600, y: 250, width: 150, height: 100 };
        let chair = { x: 540, y: 280, width: 50, height: 70 };
        let animationFrameId;

        function drawCharacter(isSitting = false) {
            ctx.fillStyle = '#007bff';
            // head
            ctx.beginPath();
            ctx.arc(character.x + character.width / 2, character.y - 10, 15, 0, Math.PI * 2);
            ctx.fill();
            if (isSitting) {
                // sitting body
                ctx.fillRect(character.x, character.y, character.width, character.height / 2);
                 // legs
                ctx.fillRect(character.x, character.y + character.height / 2, 10, 40);
                ctx.fillRect(character.x + character.width - 20, character.y + character.height / 2, 10, 40);
            } else {
                 // standing body
                ctx.fillRect(character.x, character.y, character.width, character.height);
            }
        }

        function drawScenery() {
            // Desk
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(desk.x, desk.y, desk.width, desk.height);
            // Chair
            ctx.fillStyle = '#A0522D';
            ctx.fillRect(chair.x, chair.y, chair.width, chair.height);
             // Floor
            ctx.fillStyle = '#d2b48c';
            ctx.fillRect(0, 350, canvas.width, 50);
        }

        function drawText(text, x, y, size = 30) {
            ctx.fillStyle = 'black';
            ctx.font = `bold ${size}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function resetState() {
            cancelAnimationFrame(animationFrameId);
            clearCanvas();
            character.x = 50;
            character.y = 250;
            drawScenery();
            drawCharacter();
            drawText('Assess: 评估, 评定', canvas.width / 2, 50, 40);
            explanationDiv.innerHTML = '<strong>故事：</strong>想象一位顾问走向(as-)法官席，并坐下(sess)帮助评估案件。这就是"assess"的来源。';
            enableButtons(true);
        }
        
        function enableButtons(enable) {
            prefixBtn.disabled = !enable;
            rootBtn.disabled = !enable;
            combineBtn.disabled = !enable;
        }

        function animatePrefix() {
            cancelAnimationFrame(animationFrameId);
            enableButtons(false);
            explanationDiv.innerHTML = "<strong>词缀 'as-' (ad-):</strong> 意为 'to', 'toward' (去，朝向)。人物正在走向座位。";
            let targetX = chair.x - character.width;
            
            function move() {
                clearCanvas();
                drawScenery();
                drawCharacter();
                drawText("as- (ad-): to, toward", canvas.width / 2, 50);

                if (character.x < targetX) {
                    character.x += 3;
                    animationFrameId = requestAnimationFrame(move);
                } else {
                    character.x = targetX;
                    clearCanvas();
                    drawScenery();
                    drawCharacter();
                    drawText("as- (ad-): to, toward", canvas.width / 2, 50);
                    enableButtons(true);
                }
            }
            move();
        }

        function animateRoot() {
            cancelAnimationFrame(animationFrameId);
            enableButtons(false);
            explanationDiv.innerHTML = "<strong>词根 'sess':</strong> 意为 'sit' (坐)。人物坐了下来，准备开始工作。";
            
            // Ensure character is at the chair
            character.x = chair.x - character.width;
            
            let startY = character.y;
            let targetY = chair.y - character.height / 2 + 10;
            let startHeight = character.height;
            let targetHeight = character.height / 2;

            function sit() {
                clearCanvas();
                drawScenery();
                drawText("sess: sit", canvas.width / 2, 50);
                
                // Animate sitting down
                character.y += (targetY - startY) * 0.1;
                character.height += (targetHeight - startHeight) * 0.1;

                drawCharacter(true);
                
                if (Math.abs(character.y - targetY) > 1) {
                     animationFrameId = requestAnimationFrame(sit);
                } else {
                    character.y = targetY;
                    character.height = targetHeight;
                    clearCanvas();
                    drawScenery();
                    drawCharacter(true);
                    drawText("sess: sit", canvas.width / 2, 50);
                    enableButtons(true);
                }
            }
            
            // first make sure character is standing at the right spot
            clearCanvas();
            drawScenery();
            drawCharacter(); // standing
            
            // then animate sitting
            setTimeout(() => {
                 character.y = chair.y - 20; // adjust start pos for better sit anim
                 sit();
            }, 200);
        }

        function animateCombine() {
            cancelAnimationFrame(animationFrameId);
            enableButtons(false);
            explanationDiv.innerHTML = "<strong>Assess = as- + sess:</strong> 坐在旁边进行评估。顾问开始查看文件，进行评估。";
            
            // Ensure character is sitting
            character.x = chair.x - character.width;
            character.y = chair.y - character.height / 2 + 10;
            character.height = character.height / 2;
            
            let document = { x: desk.x + 20, y: desk.y - 30, width: 60, height: 40 };
            let hand = { x: character.x + character.width, y: character.y + 20 };
            let angle = 0;

            function assessAction() {
                clearCanvas();
                drawScenery();
                drawCharacter(true);
                drawText("assess: 评估, 评定", canvas.width / 2, 50, 40);
                
                // Draw document
                ctx.fillStyle = '#fff';
                ctx.fillRect(document.x, document.y, document.width, document.height);
                ctx.strokeStyle = '#000';
                ctx.strokeRect(document.x, document.y, document.width, document.height);
                for(let i = 0; i < 4; i++) {
                    ctx.beginPath();
                    ctx.moveTo(document.x + 5, document.y + 10 + i*5);
                    ctx.lineTo(document.x + document.width - 5, document.y + 10 + i*5);
                    ctx.stroke();
                }

                // Animate hand pointing
                ctx.fillStyle = '#007bff';
                ctx.beginPath();
                hand.x = character.x + character.width + Math.sin(angle) * 10;
                ctx.arc(hand.x, hand.y, 8, 0, Math.PI * 2);
                ctx.fill();

                angle += 0.1;
                
                animationFrameId = requestAnimationFrame(assessAction);
            }
            assessAction();
            
            // We don't re-enable buttons here to show the final state
        }
        
        prefixBtn.addEventListener('click', animatePrefix);
        rootBtn.addEventListener('click', animateRoot);
        combineBtn.addEventListener('click', animateCombine);
        resetBtn.addEventListener('click', resetState);

        window.onload = resetState;
    </script>
</body>
</html> 