<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EJB 实体型构件 - 数据持久化学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .game-board {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 40px;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 16px;
            border-left: 4px solid #667eea;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            background: white;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .option-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .option-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .option-card.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            transform: scale(1.05);
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            animation: shake 0.5s ease-in-out;
        }

        .option-letter {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 12px;
        }

        .option-text {
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease-out;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .explanation h3 {
            color: #2e7d32;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .data-flow {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .flow-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            text-align: center;
        }

        .flow-card:hover {
            transform: translateY(-5px);
        }

        .flow-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes dataFlow {
            0% { transform: translateX(-100px); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100px); opacity: 0; }
        }

        .data-particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            animation: dataFlow 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">💾 EJB 实体型构件</h1>
            <p class="subtitle">探索数据持久化的奥秘 - O/R映射与数据库同步</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-text">
                    <strong>题目：</strong>EJB是企业级Java构件，用于开发和部署多层结构的、分布式的、面向对象的Java应用系统。其中，<span style="background: #d1ecf1; padding: 2px 8px; border-radius: 4px;">（会话型构件）</span>负责完成服务端与客户端的交互；<span style="background: #fff3cd; padding: 2px 8px; border-radius: 4px;">（请作答此空）</span>用于数据持久化来简化数据库开发工作；<span style="background: #f8d7da; padding: 2px 8px; border-radius: 4px;">（消息驱动构件）</span>主要用来处理并发和异步访问操作。
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="700" height="400"></canvas>
            </div>

            <div class="options-grid">
                <div class="option-card" data-answer="A">
                    <div class="option-letter">A</div>
                    <div class="option-text">会话型构件</div>
                </div>
                <div class="option-card" data-answer="B">
                    <div class="option-letter">B</div>
                    <div class="option-text">实体型构件</div>
                </div>
                <div class="option-card" data-answer="C">
                    <div class="option-letter">C</div>
                    <div class="option-text">COM构件</div>
                </div>
                <div class="option-card" data-answer="D">
                    <div class="option-letter">D</div>
                    <div class="option-text">消息驱动构件</div>
                </div>
            </div>

            <div class="interactive-demo">
                <h4>🎮 互动演示：实体Bean的数据操作</h4>
                <button class="demo-button" onclick="demoCreate()">创建实体</button>
                <button class="demo-button" onclick="demoUpdate()">更新数据</button>
                <button class="demo-button" onclick="demoDelete()">删除实体</button>
                <button class="demo-button" onclick="demoSync()">同步演示</button>
            </div>

            <div class="explanation" id="explanation">
                <h3>🎉 正确答案：B - 实体型构件</h3>
                <p><strong>解析：</strong>实体Bean是EJB中专门负责数据持久化的组件，它实现了对象关系映射(O/R Mapping)。</p>
                
                <div class="data-flow">
                    <div class="flow-card">
                        <div class="flow-icon">🏗️</div>
                        <h4>创建实体Bean</h4>
                        <p>创建一个实体Bean对象相当于在数据库中新建一条记录</p>
                    </div>
                    <div class="flow-card">
                        <div class="flow-icon">🔄</div>
                        <h4>自动同步</h4>
                        <p>修改实体Bean时，容器会自动将实体Bean的状态和数据库同步</p>
                    </div>
                    <div class="flow-card">
                        <div class="flow-icon">🗑️</div>
                        <h4>删除操作</h4>
                        <p>删除一个实体Bean会同时从数据库中删除对应记录</p>
                    </div>
                </div>

                <p><strong>核心特点：</strong></p>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    <li>实现O/R映射，将数据库表记录映射为内存中的实体对象</li>
                    <li>提供数据持久化功能，简化数据库开发工作</li>
                    <li>容器自动管理实体Bean与数据库的同步</li>
                    <li>支持事务管理，确保数据一致性</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const options = document.querySelectorAll('.option-card');
        const explanation = document.getElementById('explanation');

        let animationFrame;
        let particles = [];
        let dataFlowAnimation = false;
        let entities = [];

        // 初始化画布
        function initCanvas() {
            drawDataPersistenceDemo();
            animate();
        }

        function drawDataPersistenceDemo() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制应用层
            drawComponent(50, 50, 150, 80, '应用程序', '#ff9a9e', '💻');
            
            // 绘制实体Bean层
            drawComponent(275, 50, 150, 80, '实体Bean\n(O/R映射)', '#4CAF50', '💾');
            
            // 绘制数据库
            drawComponent(500, 50, 150, 80, '数据库', '#2196F3', '🗄️');

            // 绘制内存中的对象
            drawMemoryObjects();
            
            // 绘制数据流箭头
            drawDataFlow();
            
            // 绘制说明文字
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('对象操作', 200, 40);
            ctx.fillText('数据同步', 400, 40);
            
            // 绘制实体Bean的特性
            drawEntityFeatures();
        }

        function drawComponent(x, y, width, height, text, color, icon) {
            // 绘制圆角矩形
            const gradient = ctx.createLinearGradient(x, y, x, y + height);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, adjustBrightness(color, -20));
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, 12);
            ctx.fill();
            
            // 绘制边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制图标
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(icon, x + width/2, y + 35);
            
            // 绘制文字
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 14px Arial';
            const lines = text.split('\n');
            lines.forEach((line, index) => {
                ctx.fillText(line, x + width/2, y + 55 + index * 18);
            });
        }

        function drawMemoryObjects() {
            // 绘制内存中的对象表示
            const objects = [
                {x: 280, y: 160, name: 'User对象', color: '#81C784'},
                {x: 350, y: 160, name: 'Order对象', color: '#64B5F6'},
                {x: 420, y: 160, name: 'Product对象', color: '#FFB74D'}
            ];
            
            objects.forEach(obj => {
                ctx.fillStyle = obj.color;
                ctx.beginPath();
                ctx.roundRect(obj.x, obj.y, 60, 40, 8);
                ctx.fill();
                
                ctx.fillStyle = '#fff';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(obj.name, obj.x + 30, obj.y + 25);
            });
        }

        function drawDataFlow() {
            // 绘制双向箭头表示数据同步
            drawBidirectionalArrow(200, 90, 275, 90, '#4CAF50');
            drawBidirectionalArrow(425, 90, 500, 90, '#2196F3');
            
            // 绘制同步指示器
            if (dataFlowAnimation) {
                drawSyncIndicator();
            }
        }

        function drawBidirectionalArrow(x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 右箭头
            const angle1 = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 12 * Math.cos(angle1 - Math.PI/6), y2 - 12 * Math.sin(angle1 - Math.PI/6));
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 12 * Math.cos(angle1 + Math.PI/6), y2 - 12 * Math.sin(angle1 + Math.PI/6));
            ctx.stroke();
            
            // 左箭头
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x1 + 12 * Math.cos(angle1 - Math.PI/6), y1 + 12 * Math.sin(angle1 - Math.PI/6));
            ctx.moveTo(x1, y1);
            ctx.lineTo(x1 + 12 * Math.cos(angle1 + Math.PI/6), y1 + 12 * Math.sin(angle1 + Math.PI/6));
            ctx.stroke();
        }

        function drawEntityFeatures() {
            // 绘制实体Bean的核心特性
            const features = [
                '✓ O/R映射',
                '✓ 自动同步',
                '✓ 事务管理',
                '✓ 持久化'
            ];
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            features.forEach((feature, index) => {
                ctx.fillText(feature, 280, 250 + index * 20);
            });
        }

        function drawSyncIndicator() {
            const time = Date.now() * 0.005;
            const x = 350 + Math.sin(time) * 50;
            
            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(x, 90, 6, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#fff';
            ctx.font = '8px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('同步', x, 94);
        }

        function createParticles(x, y, color) {
            for (let i = 0; i < 15; i++) {
                particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 8,
                    vy: (Math.random() - 0.5) * 8,
                    life: 1,
                    color: color
                });
            }
        }

        function updateParticles() {
            particles = particles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life -= 0.02;
                particle.vy += 0.1;
                
                if (particle.life > 0) {
                    ctx.save();
                    ctx.globalAlpha = particle.life;
                    ctx.fillStyle = particle.color;
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                    return true;
                }
                return false;
            });
        }

        function animate() {
            drawDataPersistenceDemo();
            updateParticles();
            animationFrame = requestAnimationFrame(animate);
        }

        function adjustBrightness(color, amount) {
            return color; // 简化处理
        }

        // 选项点击事件
        options.forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                
                options.forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'B') {
                    this.classList.add('correct');
                    explanation.classList.add('show');
                    createParticles(canvas.width/2, canvas.height/2, '#4CAF50');
                    dataFlowAnimation = true;
                } else {
                    this.classList.add('wrong');
                    createParticles(canvas.width/2, canvas.height/2, '#f44336');
                }
            });
        });

        // 演示函数
        function demoCreate() {
            createParticles(350, 200, '#4CAF50');
            showTooltip('创建实体Bean对象 → 数据库新增记录');
        }

        function demoUpdate() {
            dataFlowAnimation = true;
            createParticles(350, 90, '#2196F3');
            showTooltip('修改实体Bean → 自动同步到数据库');
            setTimeout(() => dataFlowAnimation = false, 3000);
        }

        function demoDelete() {
            createParticles(350, 200, '#f44336');
            showTooltip('删除实体Bean → 同时删除数据库记录');
        }

        function demoSync() {
            dataFlowAnimation = true;
            createParticles(350, 90, '#FF9800');
            showTooltip('容器自动管理实体Bean与数据库的同步');
            setTimeout(() => dataFlowAnimation = false, 4000);
        }

        function showTooltip(text) {
            const tooltip = document.createElement('div');
            tooltip.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeInOut 3s ease-in-out;
            `;
            tooltip.textContent = text;
            document.body.appendChild(tooltip);
            
            setTimeout(() => {
                if (document.body.contains(tooltip)) {
                    document.body.removeChild(tooltip);
                }
            }, 3000);
        }

        // Canvas点击交互
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            if (x >= 275 && x <= 425 && y >= 50 && y <= 130) {
                demoSync();
            } else if (x >= 280 && x <= 420 && y >= 160 && y <= 200) {
                demoCreate();
            }
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            }
        `;
        document.head.appendChild(style);

        // 初始化
        initCanvas();
    </script>
</body>
</html>
