<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARP病毒防护学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            border-radius: 2px;
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
            position: relative;
        }

        #networkCanvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: #f8f9fa;
        }

        .control-panel {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        .btn.danger {
            background: linear-gradient(135deg, #ff4757, #ff3838);
        }

        .btn.success {
            background: linear-gradient(135deg, #2ed573, #1e90ff);
        }

        .command-demo {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            font-family: 'Courier New', monospace;
            position: relative;
            overflow: hidden;
        }

        .command-demo::before {
            content: '💻';
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 1.5rem;
        }

        .command-line {
            background: #34495e;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
            animation: typewriter 2s steps(40) infinite;
        }

        .command-explanation {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .cmd-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .cmd-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0,0,0,0.2);
        }

        .cmd-card.correct {
            background: linear-gradient(135deg, #2ed573, #17a2b8);
            animation: pulse 1s ease-in-out infinite;
        }

        .cmd-card.wrong {
            background: linear-gradient(135deg, #ff4757, #ff3838);
            animation: shake 0.6s ease-in-out;
        }

        .question-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.6;
            margin-bottom: 30px;
            text-align: center;
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 15px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: rgba(255,255,255,0.9);
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            font-family: 'Courier New', monospace;
        }

        .option:hover {
            background: white;
            transform: scale(1.02);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .option.correct {
            background: #d4edda;
            border-color: #28a745;
            animation: correctPulse 0.8s ease-in-out;
        }

        .option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
            animation: wrongShake 0.6s ease-in-out;
        }

        .explanation {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            animation: blink 2s infinite;
        }

        .status-safe {
            background: #d4edda;
            color: #155724;
        }

        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            20%, 60% { transform: translateX(-10px); }
            40%, 80% { transform: translateX(10px); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }

        @keyframes typewriter {
            from { border-right: 2px solid #3498db; }
            to { border-right: 2px solid transparent; }
        }

        .network-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .info-card {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .info-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2d3436;
        }

        .info-content {
            color: #636e72;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🛡️ ARP病毒防护学习</h1>
            <p class="subtitle">通过动画理解网络安全与ARP绑定命令</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🌐 什么是ARP协议？</h2>
            <div class="network-info">
                <div class="info-card">
                    <div class="info-title">🏠 IP地址</div>
                    <div class="info-content">就像你家的门牌号<br>***********</div>
                </div>
                <div class="info-card">
                    <div class="info-title">🆔 MAC地址</div>
                    <div class="info-content">就像你的身份证号<br>00-22-aa-00-22-aa</div>
                </div>
                <div class="info-card">
                    <div class="info-title">📋 ARP表</div>
                    <div class="info-content">记录IP和MAC对应关系的表格</div>
                </div>
            </div>
            
            <div class="canvas-container">
                <div class="status-indicator" id="networkStatus">🔒 网络安全</div>
                <canvas id="networkCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="control-panel">
                <button class="btn" onclick="showNormalNetwork()">🌟 正常网络</button>
                <button class="btn danger" onclick="showARPAttack()">⚠️ ARP攻击</button>
                <button class="btn success" onclick="showARPBinding()">🛡️ ARP绑定</button>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">💻 ARP命令详解</h2>
            <div class="command-demo">
                <h3 style="color: #3498db; margin-bottom: 20px;">Windows ARP命令参数</h3>
                <div class="command-explanation">
                    <div class="cmd-card" onclick="highlightCommand('a')">
                        <h4>arp -a</h4>
                        <p>查看ARP表</p>
                    </div>
                    <div class="cmd-card" onclick="highlightCommand('d')">
                        <h4>arp -d</h4>
                        <p>删除ARP条目</p>
                    </div>
                    <div class="cmd-card" onclick="highlightCommand('s')">
                        <h4>arp -s</h4>
                        <p>静态绑定ARP</p>
                    </div>
                    <div class="cmd-card" onclick="highlightCommand('r')">
                        <h4>arp -r</h4>
                        <p>无效参数</p>
                    </div>
                </div>
                <div class="command-line" id="commandDemo">
                    C:\> arp -s ************** 00-22-aa-00-22-aa
                </div>
            </div>
        </div>

        <div class="question-section">
            <h2 class="section-title">📝 现在来答题吧！</h2>
            <div class="question-text">
                某计算机遭到ARP病毒的攻击，为彻底解决故障，可将网关IP地址与其MAC绑定，正确的命令是（ ）。
            </div>
            
            <div class="options">
                <div class="option" onclick="selectOption(this, false, 'a')">
                    <strong>A.</strong> arp -a ************** 00-22-aa-00-22-aa
                </div>
                <div class="option" onclick="selectOption(this, false, 'd')">
                    <strong>B.</strong> arp -d ************** 00-22-aa-00-22-aa
                </div>
                <div class="option" onclick="selectOption(this, false, 'r')">
                    <strong>C.</strong> arp -r ************** 00-22-aa-00-22-aa
                </div>
                <div class="option" onclick="selectOption(this, true, 's')">
                    <strong>D.</strong> arp -s ************** 00-22-aa-00-22-aa
                </div>
            </div>

            <div class="explanation" id="explanation">
                <h3>💡 答案解析</h3>
                <p><strong>正确答案：D (arp -s)</strong></p>
                <div style="margin: 20px 0;">
                    <h4>🎯 解题思路：</h4>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li><strong>-a</strong>：只是查看ARP表，不能解决问题</li>
                        <li><strong>-d</strong>：删除ARP条目，会让攻击更严重</li>
                        <li><strong>-r</strong>：这个参数根本不存在</li>
                        <li><strong>-s</strong>：静态绑定IP和MAC，防止被篡改 ✅</li>
                    </ul>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 15px 0;">
                        <strong>🔑 关键理解：</strong><br>
                        ARP病毒会篡改ARP表，让你的数据发送到错误的地方。<br>
                        使用 <code>arp -s</code> 可以强制绑定正确的IP-MAC对应关系，病毒就无法篡改了！
                    </div>
                </div>
                <button class="btn" onclick="resetQuiz()">🔄 重新练习</button>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('networkCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let networkState = 'normal';

        // 网络设备位置
        const devices = {
            computer: { x: 100, y: 200, label: '你的电脑' },
            gateway: { x: 400, y: 200, label: '网关' },
            attacker: { x: 700, y: 200, label: '攻击者' },
            server: { x: 400, y: 100, label: '服务器' }
        };

        function initCanvas() {
            drawNetwork();
        }

        function drawNetwork() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制连接线
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 3;
            ctx.setLineDash([]);
            
            // 电脑到网关
            drawLine(devices.computer, devices.gateway);
            // 网关到服务器
            drawLine(devices.gateway, devices.server);
            // 攻击者到网关
            if (networkState === 'attack') {
                ctx.strokeStyle = '#ff4757';
                ctx.setLineDash([10, 5]);
            }
            drawLine(devices.attacker, devices.gateway);
            
            // 绘制设备
            drawDevice(devices.computer, '💻', '#3498db');
            drawDevice(devices.gateway, '🌐', '#2ecc71');
            drawDevice(devices.server, '🖥️', '#9b59b6');
            
            if (networkState === 'attack') {
                drawDevice(devices.attacker, '🦠', '#ff4757');
                drawAttackAnimation();
            } else {
                drawDevice(devices.attacker, '😴', '#95a5a6');
            }
            
            // 绘制数据包
            if (networkState === 'normal') {
                drawDataPacket(devices.computer, devices.gateway, '#3498db', '正常数据');
            } else if (networkState === 'attack') {
                drawDataPacket(devices.computer, devices.attacker, '#ff4757', '被劫持数据');
            } else if (networkState === 'protected') {
                drawDataPacket(devices.computer, devices.gateway, '#2ecc71', '受保护数据');
                drawShield(devices.gateway);
            }
        }

        function drawLine(from, to) {
            ctx.beginPath();
            ctx.moveTo(from.x, from.y);
            ctx.lineTo(to.x, to.y);
            ctx.stroke();
        }

        function drawDevice(device, icon, color) {
            // 绘制设备圆圈
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(device.x, device.y, 30, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制图标
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(icon, device.x, device.y + 8);
            
            // 绘制标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText(device.label, device.x, device.y + 50);
        }

        function drawDataPacket(from, to, color, label) {
            const progress = (Date.now() % 2000) / 2000;
            const x = from.x + (to.x - from.x) * progress;
            const y = from.y + (to.y - from.y) * progress;
            
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 8, 0, Math.PI * 2);
            ctx.fill();
            
            // 数据包标签
            ctx.fillStyle = color;
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y - 15);
        }

        function drawAttackAnimation() {
            const time = Date.now() * 0.005;
            const intensity = Math.sin(time) * 0.5 + 0.5;
            
            // 攻击波纹
            ctx.strokeStyle = `rgba(255, 71, 87, ${intensity})`;
            ctx.lineWidth = 3;
            ctx.setLineDash([]);
            
            for (let i = 1; i <= 3; i++) {
                ctx.beginPath();
                ctx.arc(devices.attacker.x, devices.attacker.y, 30 + i * 20 * intensity, 0, Math.PI * 2);
                ctx.stroke();
            }
        }

        function drawShield(device) {
            ctx.fillStyle = '#2ecc71';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🛡️', device.x + 40, device.y - 20);
        }

        function showNormalNetwork() {
            networkState = 'normal';
            document.getElementById('networkStatus').textContent = '🔒 网络安全';
            document.getElementById('networkStatus').className = 'status-indicator status-safe';
            animate();
        }

        function showARPAttack() {
            networkState = 'attack';
            document.getElementById('networkStatus').textContent = '⚠️ 遭受攻击';
            document.getElementById('networkStatus').className = 'status-indicator status-danger';
            animate();
        }

        function showARPBinding() {
            networkState = 'protected';
            document.getElementById('networkStatus').textContent = '🛡️ ARP已绑定';
            document.getElementById('networkStatus').className = 'status-indicator status-safe';
            animate();
        }

        function animate() {
            drawNetwork();
            animationId = requestAnimationFrame(animate);
        }

        function highlightCommand(type) {
            const commands = {
                'a': 'arp -a  # 查看当前ARP表中的所有条目',
                'd': 'arp -d **************  # 删除指定IP的ARP条目',
                's': 'arp -s ************** 00-22-aa-00-22-aa  # 静态绑定IP和MAC地址',
                'r': 'arp -r  # 错误！此参数不存在'
            };
            
            document.getElementById('commandDemo').textContent = commands[type];
            
            // 重置所有卡片
            document.querySelectorAll('.cmd-card').forEach(card => {
                card.classList.remove('correct');
            });
            
            // 高亮选中的卡片
            event.target.classList.add('correct');
        }

        function selectOption(element, isCorrect, cmdType) {
            // 禁用所有选项
            document.querySelectorAll('.option').forEach(opt => {
                opt.style.pointerEvents = 'none';
            });
            
            if (isCorrect) {
                element.classList.add('correct');
                // 同时高亮对应的命令卡片
                highlightCommand(cmdType);
                setTimeout(() => {
                    document.getElementById('explanation').style.display = 'block';
                }, 1000);
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                setTimeout(() => {
                    document.querySelectorAll('.option')[3].classList.add('correct');
                    highlightCommand('s');
                    setTimeout(() => {
                        document.getElementById('explanation').style.display = 'block';
                    }, 1000);
                }, 1000);
            }
        }

        function resetQuiz() {
            // 重置所有选项
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
                opt.style.pointerEvents = 'auto';
            });
            
            // 重置命令卡片
            document.querySelectorAll('.cmd-card').forEach(card => {
                card.classList.remove('correct');
            });
            
            // 隐藏解析
            document.getElementById('explanation').style.display = 'none';
            
            // 重置命令演示
            document.getElementById('commandDemo').textContent = 'C:\\> arp -s ************** 00-22-aa-00-22-aa';
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initCanvas();
            showNormalNetwork();
        });
    </script>
</body>
</html>
