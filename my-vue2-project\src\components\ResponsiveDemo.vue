<template>
  <div class="responsive-demo">
    <div class="header">
      <h1>窗口自适应演示</h1>
      <div class="header-actions">
        <el-button @click="toggleWindowInfo">
          {{ showInfo ? '隐藏' : '显示' }}窗口信息
        </el-button>
        <el-button @click="toggleDarkMode">
          {{ darkMode ? '浅色' : '深色' }}模式
        </el-button>
      </div>
    </div>
    
    <div class="content-container">
      <!-- 窗口信息卡片 -->
      <div v-if="showInfo" class="card window-info-card">
        <h3 class="card-title">窗口信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>设备类型:</label>
            <span :class="`device-badge device-${deviceType}`">{{ deviceTypeText }}</span>
          </div>
          <div class="info-item">
            <label>窗口大小:</label>
            <span>{{ windowInfo.width }} × {{ windowInfo.height }}</span>
          </div>
          <div class="info-item">
            <label>最大化状态:</label>
            <span :class="windowInfo.isMaximized ? 'status-active' : 'status-inactive'">
              {{ windowInfo.isMaximized ? '已最大化' : '未最大化' }}
            </span>
          </div>
          <div class="info-item">
            <label>全屏状态:</label>
            <span :class="windowInfo.isFullScreen ? 'status-active' : 'status-inactive'">
              {{ windowInfo.isFullScreen ? '全屏模式' : '窗口模式' }}
            </span>
          </div>
          <div class="info-item">
            <label>运行环境:</label>
            <span>{{ isElectron ? 'Electron桌面应用' : 'Web浏览器' }}</span>
          </div>
        </div>
      </div>
      
      <!-- 响应式网格演示 -->
      <div class="card">
        <h3 class="card-title">响应式网格布局</h3>
        <div class="responsive-grid">
          <div v-for="i in 12" :key="i" class="grid-item">
            <div class="grid-content">
              <h4>卡片 {{ i }}</h4>
              <p>这是一个响应式卡片，会根据窗口大小自动调整布局。</p>
              <small>当前列数: {{ cardColumns }}</small>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 自适应功能演示 */
      <div class="card">
        <h3 class="card-title">自适应功能</h3>
        <div class="feature-list">
          <div class="feature-item">
            <i class="el-icon-monitor"></i>
            <div>
              <h4>窗口大小自适应</h4>
              <p>根据窗口大小自动调整布局和字体大小</p>
            </div>
          </div>
          <div class="feature-item">
            <i class="el-icon-mobile-phone"></i>
            <div>
              <h4>设备类型检测</h4>
              <p>自动识别移动设备、平板和桌面设备</p>
            </div>
          </div>
          <div class="feature-item">
            <i class="el-icon-full-screen"></i>
            <div>
              <h4>全屏模式支持</h4>
              <p>支持全屏模式和最大化状态检测</p>
            </div>
          </div>
          <div class="feature-item">
            <i class="el-icon-refresh"></i>
            <div>
              <h4>实时响应</h4>
              <p>窗口大小变化时实时更新布局</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import windowAdaptive from '../mixins/windowAdaptive'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'ResponsiveDemo',
  mixins: [windowAdaptive],
  
  data() {
    return {
      showInfo: true
    }
  },
  
  computed: {
    ...mapState(['darkMode']),
    
    deviceTypeText() {
      const typeMap = {
        'mobile': '移动设备',
        'tablet': '平板设备',
        'desktop': '桌面设备',
        'large-desktop': '大屏桌面'
      }
      return typeMap[this.deviceType] || '未知设备'
    },
    
    cardColumns() {
      return getComputedStyle(document.documentElement)
        .getPropertyValue('--card-columns').trim()
    }
  },
  
  methods: {
    ...mapMutations(['setDarkMode']),
    
    toggleWindowInfo() {
      this.showInfo = !this.showInfo
    },
    
    toggleDarkMode() {
      this.setDarkMode(!this.darkMode)
    },
    
    // 重写窗口自适应回调
    onWindowResize(size) {
      console.log('ResponsiveDemo - Window resized:', size)
      // 可以在这里添加组件特定的窗口大小变化处理逻辑
    }
  }
}
</script>

<style scoped>
.responsive-demo {
  width: 100%;
  min-height: 100vh;
}

.window-info-card {
  background: linear-gradient(135deg, var(--primary-color-translucent), var(--primary-color-light-translucent));
  border-left: 4px solid var(--primary-color);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item label {
  font-weight: 600;
  color: var(--text-dark);
  font-size: 0.9em;
}

.info-item span {
  font-size: 1.1em;
  padding: 5px 10px;
  border-radius: 4px;
  background: var(--hover-bg-color);
}

.device-badge {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.device-mobile { color: #e74c3c; }
.device-tablet { color: #f39c12; }
.device-desktop { color: #27ae60; }
.device-large-desktop { color: #8e44ad; }

.status-active {
  color: var(--primary-color) !important;
  background: var(--primary-color-translucent) !important;
}

.status-inactive {
  color: var(--text-light) !important;
}

.grid-item {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s ease;
}

.grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color);
}

.grid-content {
  padding: 20px;
}

.grid-content h4 {
  margin: 0 0 10px 0;
  color: var(--text-color);
}

.grid-content p {
  margin: 0 0 10px 0;
  color: var(--text-light);
  line-height: 1.5;
}

.grid-content small {
  color: var(--primary-color);
  font-weight: 600;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: var(--hover-bg-color);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: var(--primary-color-translucent);
}

.feature-item i {
  font-size: 24px;
  color: var(--primary-color);
  margin-top: 5px;
}

.feature-item h4 {
  margin: 0 0 8px 0;
  color: var(--text-color);
}

.feature-item p {
  margin: 0;
  color: var(--text-light);
  line-height: 1.4;
}

/* 响应式调整 */
.device-mobile .info-grid {
  grid-template-columns: 1fr;
}

.device-mobile .feature-list {
  grid-template-columns: 1fr;
}

.device-mobile .feature-item {
  padding: 15px;
}

.device-tablet .info-grid {
  grid-template-columns: repeat(2, 1fr);
}
</style>
