<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作系统PV操作交互式学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f8ff;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            font-size: 16px;
        }
        h1 {
            color: #2c3e50;
            text-shadow: 1px 1px 2px #bdc3c7;
        }
        #container {
            display: flex;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 95%;
            max-width: 1200px;
        }
        #controls-panel {
            width: 300px;
            padding: 20px;
            background-color: #ecf0f1;
            border-right: 1px solid #dcdcdc;
            display: flex;
            flex-direction: column;
        }
        #controls-panel h2, #controls-panel h3 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        #canvas-container {
            flex-grow: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        canvas {
            border: 1px solid #bdc3c7;
            background-color: #fff;
            border-radius: 8px;
        }
        .control-group {
            margin-bottom: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s, transform 0.1s;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:active {
            transform: scale(0.98);
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        #explanation, #log-container {
            background: #fdfefe;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            height: 150px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }
        #log-container {
             height: 200px;
        }
        #log-container p {
            margin: 0 0 5px 0;
            padding: 3px 5px;
            border-radius: 3px;
        }
        .log-info { color: #2c3e50; }
        .log-wait { color: #e67e22; background-color: #fdf3e7; }
        .log-signal { color: #27ae60; background-color: #eaf8f0; }
        .log-run { color: #8e44ad; }
        .log-done { color: #2980b9; }
        .log-error { color: #c0392b; font-weight: bold; }

        .tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.75);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            display: none; /* Hidden by default */
            pointer-events: none; /* The tooltip should not be interactive */
        }
    </style>
</head>
<body>
    <h1>操作系统PV操作 - 交互式学习</h1>
    <div id="container">
        <div id="controls-panel">
            <div class="control-group">
                <h2>知识简介</h2>
                <div id="explanation">
                    <p>你好！我们来学习控制多个任务（叫"进程"）按顺序工作的"PV操作"。</p>
                    <p>想象有多个工人，有些工作必须等其他工作完成后才能开始。</p>
                    <ul>
                        <li><strong>V操作 (Signal)</strong>: 一个工人完成自己的任务后，发出一个"信号"，告诉下一个工人可以开始了。</li>
                        <li><strong>P操作 (Wait)</strong>: 工人在开始自己的任务前，需要"等待"一个信号。如果信号没到，就只能等着。</li>
                    </ul>
                    <p>我们用"信号量"来记录信号的个数，初始值通常是0。</p>
                </div>
            </div>

            <div class="control-group">
                <h3>交互操作</h3>
                <p>点击下方按钮，一步步观察进程如何协作。</p>
                <button id="next-step-btn">开始执行 (P1)</button>
                <button id="reset-btn">重新开始</button>
            </div>

            <div class="control-group">
                <h3>执行日志</h3>
                <div id="log-container"></div>
            </div>
        </div>
        <div id="canvas-container">
             <h3>流程图与状态</h3>
             <canvas id="sync-canvas" width="800" height="600"></canvas>
        </div>
    </div>
    <div id="tooltip" class="tooltip"></div>

    <script>
        const canvas = document.getElementById('sync-canvas');
        const ctx = canvas.getContext('2d');
        const nextStepBtn = document.getElementById('next-step-btn');
        const resetBtn = document.getElementById('reset-btn');
        const logContainer = document.getElementById('log-container');
        const tooltip = document.getElementById('tooltip');

        const colors = {
            process: {
                ready: '#3498db',
                running: '#e67e22',
                waiting: '#f1c40f',
                done: '#2ecc71',
                text: '#ffffff'
            },
            semaphore: {
                box: '#95a5a6',
                text: '#ffffff',
                highlight: '#e74c3c'
            },
            arrow: '#34495e'
        };

        let state;

        function getInitialState() {
            return {
                step: 0,
                processes: {
                    p1: { name: 'P1', x: 150, y: 300, w: 80, h: 50, status: 'ready' },
                    p2: { name: 'P2', x: 350, y: 150, w: 80, h: 50, status: 'ready' },
                    p3: { name: 'P3', x: 350, y: 450, w: 80, h: 50, status: 'ready' },
                    p4: { name: 'P4', x: 550, y: 300, w: 80, h: 50, status: 'ready' }
                },
                semaphores: {
                    s1: { name: 'S1', value: 0, x: 250, y: 200 },
                    s2: { name: 'S2', value: 0, x: 250, y: 400 },
                    s3: { name: 'S3', value: 0, x: 450, y: 200 },
                    s4: { name: 'S4', value: 0, x: 450, y: 400 }
                },
                operations: {
                    a: { text: "V(S1)\nV(S2)", x: 150, y: 360, w: 80, h: 40, show: false}, // After P1
                    b: { text: "P(S1)", x: 350, y: 100, w: 80, h: 30, show: false },      // Before P2
                    c: { text: "V(S3)", x: 350, y: 210, w: 80, h: 30, show: false },      // After P2
                    d: { text: "P(S2)", x: 350, y: 410, w: 80, h: 30, show: false },      // Before P3
                    e: { text: "V(S4)", x: 350, y: 510, w: 80, h: 30, show: false },      // After P3
                    f: { text: "P(S3)\nP(S4)", x: 550, y: 240, w: 80, h: 40, show: false }, // Before P4
                },
                log: [],
                done: false,
                currentAnimation: null
            };
        }

        function drawProcess(p) {
            ctx.fillStyle = colors.process[p.status];
            ctx.fillRect(p.x, p.y, p.w, p.h);
            ctx.fillStyle = colors.process.text;
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(p.name, p.x + p.w / 2, p.y + p.h / 2);
            ctx.font = '12px sans-serif';
            ctx.fillText(`(${p.status})`, p.x + p.w / 2, p.y + p.h / 2 + 15);
        }

        function drawSemaphore(s) {
            ctx.fillStyle = colors.semaphore.box;
            ctx.fillRect(s.x, s.y, 40, 40);
            ctx.fillStyle = colors.semaphore.text;
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(s.value, s.x + 20, s.y + 20);
            ctx.fillStyle = '#333';
            ctx.fillText(s.name, s.x + 20, s.y - 15);
        }
        
        function drawArrow(from, to) {
            ctx.beginPath();
            ctx.moveTo(from.x + from.w, from.y + from.h / 2);
            const midX = (from.x + from.w + to.x) / 2;
            const midY = (from.y + from.h/2 + to.y + to.h/2) / 2;
            
            let cpx1 = midX;
            let cpy1 = from.y + from.h / 2;
            let cpx2 = midX;
            let cpy2 = to.y + to.h / 2;
            
            ctx.quadraticCurveTo(cpx1, cpy1, midX, midY);
            ctx.quadraticCurveTo(cpx2, cpy2, to.x, to.y + to.h / 2);

            ctx.strokeStyle = colors.arrow;
            ctx.lineWidth = 2;
            ctx.stroke();

            // Draw arrowhead
            const angle = Math.atan2((to.y + to.h/2) - midY, to.x - midX);
            ctx.save();
            ctx.translate(to.x, to.y + to.h / 2);
            ctx.rotate(angle);
            ctx.beginPath();
            ctx.moveTo(-10, -5);
            ctx.lineTo(0, 0);
            ctx.lineTo(-10, 5);
            ctx.fillStyle = colors.arrow;
            ctx.fill();
            ctx.restore();
        }

        function drawOperation(op) {
            if (!op.show) return;
            ctx.fillStyle = 'rgba(236, 240, 241, 0.8)';
            ctx.strokeStyle = '#7f8c8d';
            ctx.lineWidth = 1;
            ctx.fillRect(op.x, op.y, op.w, op.h);
            ctx.strokeRect(op.x, op.y, op.w, op.h);
            
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            const lines = op.text.split('\n');
            if (lines.length > 1) {
                ctx.fillText(lines[0], op.x + op.w/2, op.y + op.h/2 - 8);
                ctx.fillText(lines[1], op.x + op.w/2, op.y + op.h/2 + 8);
            } else {
                ctx.fillText(op.text, op.x + op.w / 2, op.y + op.h / 2);
            }
        }

        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw precedence graph arrows
            const { p1, p2, p3, p4 } = state.processes;
            drawArrow(p1, p2);
            drawArrow(p1, p3);
            drawArrow(p2, p4);
            drawArrow(p3, p4);

            for (const key in state.processes) {
                drawProcess(state.processes[key]);
            }
            for (const key in state.semaphores) {
                drawSemaphore(state.semaphores[key]);
            }
            for (const key in state.operations) {
                drawOperation(state.operations[key]);
            }
        }
        
        function addLog(message, type) {
            state.log.unshift({ message, type });
            const p = document.createElement('p');
            p.textContent = message;
            p.className = `log-${type}`;
            logContainer.insertBefore(p, logContainer.firstChild);
        }

        function animate(type, fromObj, toObj, onComplete) {
            if (state.currentAnimation) {
                console.warn("Animation already in progress");
                return;
            }

            let start = null;
            const duration = 1000;
            const from = { x: fromObj.x + fromObj.w/2, y: fromObj.y + fromObj.h/2 };
            const to = { x: toObj.x + 20, y: toObj.y + 20 };

            function animationStep(timestamp) {
                if (!start) start = timestamp;
                const progress = Math.min((timestamp - start) / duration, 1);
                
                const currentX = from.x + (to.x - from.x) * progress;
                const currentY = from.y + (to.y - from.y) * progress;

                draw(); // Redraw background

                // Draw the animated "signal"
                ctx.beginPath();
                ctx.arc(currentX, currentY, 8, 0, 2 * Math.PI);
                ctx.fillStyle = type === 'P' ? colors.process.waiting : colors.process.done;
                ctx.fill();
                ctx.font = 'bold 14px sans-serif';
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(type, currentX, currentY);

                if (progress < 1) {
                    state.currentAnimation = requestAnimationFrame(animationStep);
                } else {
                    state.currentAnimation = null;
                    if (onComplete) {
                        onComplete();
                    }
                    draw();
                }
            }
            state.currentAnimation = requestAnimationFrame(animationStep);
        }

        const steps = [
            // Step 0: Initial state
            {
                action: () => {
                    addLog("开始执行。P1是第一个进程，可以直接运行。", "info");
                    state.processes.p1.status = 'running';
                    nextStepBtn.textContent = '执行P1完毕 (执行V操作)';
                },
                canProceed: () => true
            },
            // Step 1: P1 finishes, does V(S1), V(S2)
            {
                action: () => {
                    addLog("P1完成，发出两个信号：V(S1)和V(S2)。", "signal");
                    state.operations.a.show = true;
                    animate('V', state.processes.p1, state.semaphores.s1, () => {
                        state.semaphores.s1.value++;
                        animate('V', state.processes.p1, state.semaphores.s2, () => {
                            state.semaphores.s2.value++;
                            state.processes.p1.status = 'done';
                            addLog("信号量S1和S2的值都增加了1。", "info");
                            addLog("P2和P3现在可以尝试执行了。", "info");
                            nextStepBtn.textContent = '执行P2 (执行P操作)';
                        });
                    });
                },
                canProceed: () => true
            },
            // Step 2: P2 tries to run, does P(S1)
            {
                action: () => {
                    addLog("P2开始执行，首先需要等待P1的信号，执行P(S1)。", "wait");
                    state.operations.b.show = true;
                     animate('P', state.processes.p2, state.semaphores.s1, () => {
                        if (state.semaphores.s1.value > 0) {
                            state.semaphores.s1.value--;
                            state.processes.p2.status = 'running';
                            addLog("S1 > 0，P(S1)成功，P2开始运行。", "run");
                            nextStepBtn.textContent = '执行P3 (执行P操作)';
                        } else {
                            // This case shouldn't happen in this flow
                             addLog("S1 = 0，P(S1)失败，P2需要等待。", "error");
                        }
                    });
                },
                canProceed: () => state.processes.p1.status === 'done'
            },
            // Step 3: P3 tries to run, does P(S2)
            {
                action: () => {
                    addLog("P3开始执行，等待P1的信号，执行P(S2)。", "wait");
                    state.operations.d.show = true;
                     animate('P', state.processes.p3, state.semaphores.s2, () => {
                        if (state.semaphores.s2.value > 0) {
                            state.semaphores.s2.value--;
                            state.processes.p3.status = 'running';
                            addLog("S2 > 0，P(S2)成功，P3开始运行。", "run");
                             addLog("P2和P3现在可以并行运行。", "info");
                            nextStepBtn.textContent = '执行P2完毕 (执行V操作)';
                        } else {
                             addLog("S2 = 0，P(S2)失败，P3需要等待。", "error");
                        }
                    });
                },
                canProceed: () => state.processes.p1.status === 'done'
            },
             // Step 4: P2 finishes, does V(S3)
            {
                action: () => {
                    addLog("P2运行结束，发出信号V(S3)通知P4。", "signal");
                    state.operations.c.show = true;
                    animate('V', state.processes.p2, state.semaphores.s3, () => {
                        state.semaphores.s3.value++;
                        state.processes.p2.status = 'done';
                        addLog("信号量S3的值增加了1。", "info");
                        nextStepBtn.textContent = '执行P3完毕 (执行V操作)';
                    });
                },
                canProceed: () => state.processes.p2.status === 'running'
            },
            // Step 5: P3 finishes, does V(S4)
            {
                action: () => {
                    addLog("P3运行结束，发出信号V(S4)通知P4。", "signal");
                    state.operations.e.show = true;
                    animate('V', state.processes.p3, state.semaphores.s4, () => {
                        state.semaphores.s4.value++;
                        state.processes.p3.status = 'done';
                        addLog("信号量S4的值增加了1。", "info");
                        addLog("现在P4可以尝试执行了。", "info");
                        nextStepBtn.textContent = '执行P4 (执行P操作)';
                    });
                },
                canProceed: () => state.processes.p3.status === 'running'
            },
            // Step 6: P4 tries to run, does P(S3) and P(S4)
            {
                action: () => {
                    addLog("P4开始，它需要P2和P3都完成。因此执行P(S3)和P(S4)。", "wait");
                    state.operations.f.show = true;
                    animate('P', state.processes.p4, state.semaphores.s3, () => {
                        if (state.semaphores.s3.value > 0) {
                            state.semaphores.s3.value--;
                            addLog("P(S3)成功！等待P(S4)...", "run");
                            animate('P', state.processes.p4, state.semaphores.s4, () => {
                                if(state.semaphores.s4.value > 0) {
                                    state.semaphores.s4.value--;
                                    state.processes.p4.status = 'running';
                                    addLog("P(S4)成功！P4开始运行。", "run");
                                    nextStepBtn.textContent = '执行P4完毕';
                                } else {
                                     addLog("S4=0, P4等待P3完成", "error");
                                     state.semaphores.s3.value++; // Rollback
                                }
                            });
                        } else {
                            addLog("S3=0, P4等待P2完成", "error");
                        }
                    });
                },
                canProceed: () => state.processes.p2.status === 'done' && state.processes.p3.status === 'done'
            },
            // Step 7: P4 finishes
            {
                action: () => {
                    addLog("P4运行结束。", "done");
                    state.processes.p4.status = 'done';
                    addLog("所有进程都已成功执行！", "info");
                    nextStepBtn.textContent = '已全部完成';
                    nextStepBtn.disabled = true;
                    state.done = true;
                },
                canProceed: () => state.processes.p4.status === 'running'
            }
        ];

        function nextStep() {
            if (state.done || state.currentAnimation) return;

            const currentStep = steps[state.step];
            if (currentStep && currentStep.canProceed()) {
                currentStep.action();
                state.step++;
            } else {
                 addLog("条件不满足，无法继续！", "error");
            }
            draw();
        }

        function reset() {
            if(state && state.currentAnimation) {
                cancelAnimationFrame(state.currentAnimation);
            }
            state = getInitialState();
            logContainer.innerHTML = '';
            nextStepBtn.textContent = '开始执行 (P1)';
            nextStepBtn.disabled = false;
            draw();
        }

        nextStepBtn.addEventListener('click', nextStep);
        resetBtn.addEventListener('click', reset);

        function showTooltip(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            let hoveredItem = null;
            for (const key in state.processes) {
                const p = state.processes[key];
                if (x >= p.x && x <= p.x + p.w && y >= p.y && y <= p.y + p.h) {
                    hoveredItem = `进程 ${p.name}: 当前状态是 '${p.status}'`;
                }
            }
             for (const key in state.semaphores) {
                const s = state.semaphores[key];
                if (x >= s.x && x <= s.x + 40 && y >= s.y && y <= s.y + 40) {
                    hoveredItem = `信号量 ${s.name}: 当前值为 ${s.value}`;
                }
            }
            
            if (hoveredItem) {
                tooltip.style.display = 'block';
                tooltip.style.left = `${e.clientX + 10}px`;
                tooltip.style.top = `${e.clientY + 10}px`;
                tooltip.textContent = hoveredItem;
            } else {
                tooltip.style.display = 'none';
            }
        }
        
        canvas.addEventListener('mousemove', showTooltip);
        canvas.addEventListener('mouseout', () => tooltip.style.display = 'none');

        // Initial setup
        reset();
    </script>
</body>
</html> 