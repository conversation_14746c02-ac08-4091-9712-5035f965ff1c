<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库设计需求分析 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .option.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .option.correct {
            border-color: #28a745;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .option.wrong {
            border-color: #dc3545;
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .phase-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .phase-card:hover {
            transform: scale(1.02);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .phase-card.active {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .floating-element {
            position: absolute;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 数据库设计需求分析</h1>
            <p class="subtitle">通过互动游戏学习数据库设计的四个阶段</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>在数据库设计的需求分析阶段应当形成（ ），这些文档可以作为（ ）阶段的设计依据。
            </div>
            
            <div class="options" id="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 程序文档、数据字典和数据流图
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 需求说明文档、程序文档和数据流图
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 需求说明文档、数据字典和数据流图
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 需求说明文档、数据字典和程序文档
                </div>
            </div>
            
            <button class="btn" id="checkAnswer">检查答案</button>
            <button class="btn" id="startGame">开始互动学习</button>
        </div>

        <div class="canvas-container">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">🎮 数据库设计四阶段互动演示</h3>
            <canvas id="gameCanvas"></canvas>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn" id="resetGame">重新开始</button>
                <button class="btn" id="nextPhase">下一阶段</button>
            </div>
        </div>

        <div class="explanation">
            <h3 style="color: #333; margin-bottom: 30px;">📚 知识详解</h3>
            
            <div class="phase-card" data-phase="1">
                <h4>🔍 第一阶段：需求分析</h4>
                <p>形成三个重要文档：需求说明文档、数据字典、数据流图</p>
            </div>
            
            <div class="phase-card" data-phase="2">
                <h4>🎯 第二阶段：概念结构设计</h4>
                <p>基于需求分析的文档，设计E-R图等概念模型</p>
            </div>
            
            <div class="phase-card" data-phase="3">
                <h4>🏗️ 第三阶段：逻辑结构设计</h4>
                <p>将概念模型转换为具体的数据库逻辑结构</p>
            </div>
            
            <div class="phase-card" data-phase="4">
                <h4>⚙️ 第四阶段：物理结构设计</h4>
                <p>确定数据的存储结构和存取方法</p>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let currentPhase = 1;
        let gameStarted = false;
        let animationId;
        
        // Canvas 设置
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置 Canvas 尺寸
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth - 60;
            canvas.height = 400;
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // 游戏对象
        const phases = [
            {
                name: "需求分析",
                color: "#667eea",
                documents: ["需求说明文档", "数据字典", "数据流图"],
                x: 100,
                y: 200
            },
            {
                name: "概念结构设计",
                color: "#28a745",
                documents: ["E-R图", "概念模型"],
                x: 300,
                y: 200
            },
            {
                name: "逻辑结构设计",
                color: "#fd7e14",
                documents: ["关系模式", "数据表结构"],
                x: 500,
                y: 200
            },
            {
                name: "物理结构设计",
                color: "#dc3545",
                documents: ["存储结构", "索引设计"],
                x: 700,
                y: 200
            }
        ];
        
        let particles = [];
        let time = 0;
        
        // 粒子类
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 2;
                this.vy = (Math.random() - 0.5) * 2;
                this.color = color;
                this.life = 1;
                this.decay = 0.02;
            }
            
            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
            }
            
            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }
        
        // 绘制阶段
        function drawPhase(phase, index, isActive = false) {
            const scale = isActive ? 1.2 : 1;
            const alpha = isActive ? 1 : 0.6;
            
            ctx.save();
            ctx.globalAlpha = alpha;
            
            // 绘制圆形背景
            ctx.fillStyle = phase.color;
            ctx.beginPath();
            ctx.arc(phase.x, phase.y, 50 * scale, 0, Math.PI * 2);
            ctx.fill();
            
            // 绘制阶段名称
            ctx.fillStyle = "white";
            ctx.font = "bold 14px Microsoft YaHei";
            ctx.textAlign = "center";
            ctx.fillText(phase.name, phase.x, phase.y - 10);
            ctx.fillText(`第${index + 1}阶段`, phase.x, phase.y + 10);
            
            // 绘制文档列表
            if (isActive) {
                ctx.fillStyle = "#333";
                ctx.font = "12px Microsoft YaHei";
                phase.documents.forEach((doc, i) => {
                    ctx.fillText(doc, phase.x, phase.y + 80 + i * 20);
                });
            }
            
            ctx.restore();
        }
        
        // 绘制连接线
        function drawConnections() {
            ctx.strokeStyle = "#ddd";
            ctx.lineWidth = 3;
            ctx.setLineDash([5, 5]);
            
            for (let i = 0; i < phases.length - 1; i++) {
                const current = phases[i];
                const next = phases[i + 1];
                
                ctx.beginPath();
                ctx.moveTo(current.x + 50, current.y);
                ctx.lineTo(next.x - 50, next.y);
                ctx.stroke();
                
                // 绘制箭头
                const angle = Math.atan2(next.y - current.y, next.x - current.x);
                const arrowX = next.x - 50;
                const arrowY = next.y;
                
                ctx.save();
                ctx.translate(arrowX, arrowY);
                ctx.rotate(angle);
                ctx.fillStyle = "#ddd";
                ctx.beginPath();
                ctx.moveTo(0, 0);
                ctx.lineTo(-15, -8);
                ctx.lineTo(-15, 8);
                ctx.closePath();
                ctx.fill();
                ctx.restore();
            }
            
            ctx.setLineDash([]);
        }
        
        // 主绘制函数
        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, "#f8f9fa");
            gradient.addColorStop(1, "#e9ecef");
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制连接线
            drawConnections();
            
            // 绘制阶段
            phases.forEach((phase, index) => {
                drawPhase(phase, index, index + 1 === currentPhase);
            });
            
            // 绘制粒子效果
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
            
            // 绘制进度指示器
            ctx.fillStyle = "#333";
            ctx.font = "16px Microsoft YaHei";
            ctx.textAlign = "center";
            ctx.fillText(`当前阶段: ${phases[currentPhase - 1].name}`, canvas.width / 2, 50);
            
            time += 0.02;
        }
        
        // 动画循环
        function animate() {
            draw();
            animationId = requestAnimationFrame(animate);
        }
        
        // 添加粒子效果
        function addParticles(x, y, color) {
            for (let i = 0; i < 10; i++) {
                particles.push(new Particle(x, y, color));
            }
        }
        
        // 事件处理
        document.getElementById('checkAnswer').addEventListener('click', function() {
            const options = document.querySelectorAll('.option');
            const correctAnswer = 'C';
            
            options.forEach(option => {
                const answer = option.dataset.answer;
                if (answer === correctAnswer) {
                    option.classList.add('correct');
                } else {
                    option.classList.add('wrong');
                }
            });
            
            // 更新进度
            document.getElementById('progressFill').style.width = '25%';
        });
        
        document.getElementById('startGame').addEventListener('click', function() {
            gameStarted = true;
            currentPhase = 1;
            animate();
            document.getElementById('progressFill').style.width = '50%';
        });
        
        document.getElementById('nextPhase').addEventListener('click', function() {
            if (currentPhase < phases.length) {
                const currentPhaseObj = phases[currentPhase - 1];
                addParticles(currentPhaseObj.x, currentPhaseObj.y, currentPhaseObj.color);
                currentPhase++;
                
                // 更新进度
                const progress = 50 + (currentPhase - 1) * 12.5;
                document.getElementById('progressFill').style.width = progress + '%';
            }
        });
        
        document.getElementById('resetGame').addEventListener('click', function() {
            currentPhase = 1;
            particles = [];
            document.getElementById('progressFill').style.width = '50%';
        });
        
        // 阶段卡片点击事件
        document.querySelectorAll('.phase-card').forEach((card, index) => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.phase-card').forEach(c => c.classList.remove('active'));
                this.classList.add('active');
                currentPhase = index + 1;
                
                if (gameStarted) {
                    const phaseObj = phases[index];
                    addParticles(phaseObj.x, phaseObj.y, phaseObj.color);
                }
            });
        });
        
        // Canvas 点击事件
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            phases.forEach((phase, index) => {
                const distance = Math.sqrt((x - phase.x) ** 2 + (y - phase.y) ** 2);
                if (distance < 50) {
                    currentPhase = index + 1;
                    addParticles(phase.x, phase.y, phase.color);
                    
                    // 激活对应的阶段卡片
                    document.querySelectorAll('.phase-card').forEach(c => c.classList.remove('active'));
                    document.querySelectorAll('.phase-card')[index].classList.add('active');
                }
            });
        });
        
        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
            });
        });
        
        // 初始化
        draw();
    </script>
</body>
</html>
