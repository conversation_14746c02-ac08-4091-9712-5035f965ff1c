<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集合的运算 - 零基础互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: center;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .special-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }

        .success-btn {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #667eea;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .concept-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .concept-desc {
            color: #666;
            line-height: 1.6;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
            max-height: 80vh;
            overflow-y: auto;
        }

        .nav-item {
            display: block;
            color: #333;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            margin: 3px 0;
            font-size: 0.85rem;
        }

        .nav-item:hover {
            background: #667eea;
            color: white;
        }

        .control-panel {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }

        .control-panel.active {
            display: block;
            animation: slideIn 0.5s ease-out;
        }

        .slider-container {
            margin: 15px 0;
        }

        .slider {
            width: 200px;
            margin: 0 10px;
        }

        .formula-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .operation-symbol {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 0 20px;
        }

        .venn-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
            50% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .glow {
            animation: glow 2s infinite;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="#intersection" class="nav-item">交集概念</a>
        <a href="#intersection-problems" class="nav-item">交集问题</a>
        <a href="#intersection-params" class="nav-item">交集求参数</a>
        <a href="#union" class="nav-item">并集概念</a>
        <a href="#union-problems" class="nav-item">并集问题</a>
        <a href="#complement" class="nav-item">补集概念</a>
        <a href="#mixed-operations" class="nav-item">混合运算</a>
        <a href="#venn-diagrams" class="nav-item">韦恩图运算</a>
        <a href="#parameter-solving" class="nav-item">参数求解</a>
        <a href="#element-counting" class="nav-item">元素计数</a>
        <a href="#error-analysis" class="nav-item">易错点分析</a>
        <a href="#comprehensive" class="nav-item">综合练习</a>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">集合的运算</h1>
            <p class="subtitle">零基础互动教学 - 从基础运算到综合应用</p>
        </div>

        <!-- 1. 交集的概念 -->
        <div class="section" id="intersection">
            <h2 class="section-title">1. 交集的概念</h2>
            <div class="canvas-container">
                <canvas id="intersectionCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                交集是两个集合共同拥有的元素组成的集合。记作A∩B，读作"A交B"。
                <span class="highlight">A∩B = {x | x∈A 且 x∈B}</span>
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="demonstrateIntersection()">演示交集概念</button>
                <button class="interactive-btn" onclick="animateIntersectionProcess()">动画过程</button>
                <button class="interactive-btn special-btn" onclick="interactiveIntersection()">🎮 互动交集</button>
                <button class="interactive-btn" onclick="resetCanvas('intersection')">重置</button>
            </div>

            <div class="control-panel" id="intersectionPanel">
                <h3>🎯 互动交集演示</h3>
                <p>拖拽元素体验交集运算</p>
                <div class="venn-container">
                    <div>
                        <label>集合A元素：</label>
                        <input type="text" id="setAElements" value="1,2,3,4" placeholder="用逗号分隔">
                    </div>
                    <span class="operation-symbol">∩</span>
                    <div>
                        <label>集合B元素：</label>
                        <input type="text" id="setBElements" value="3,4,5,6" placeholder="用逗号分隔">
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <button class="interactive-btn success-btn" onclick="calculateIntersection()">计算交集</button>
                    <button class="interactive-btn" onclick="showIntersectionSteps()">显示步骤</button>
                </div>
                <div class="formula-display" id="intersectionResult">
                    A ∩ B = ?
                </div>
            </div>

            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">交集定义</div>
                    <div class="concept-desc">
                        A∩B = {x | x∈A 且 x∈B}<br>
                        即：<span class="highlight">既属于A又属于B的元素</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">交集性质</div>
                    <div class="concept-desc">
                        • A∩B = B∩A (交换律)<br>
                        • A∩A = A (幂等律)<br>
                        • A∩∅ = ∅ (空集性质)<br>
                        • <span class="highlight">A∩B ⊆ A, A∩B ⊆ B</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">韦恩图表示</div>
                    <div class="concept-desc">
                        在韦恩图中，交集表示为<br>
                        两个圆的<span class="highlight">重叠部分</span><br>
                        直观显示共同元素
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. 数集和点集的交集问题 -->
        <div class="section" id="intersection-problems">
            <h2 class="section-title">2. 数集和点集的交集问题</h2>
            <div class="canvas-container">
                <canvas id="intersectionProblemsCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                数集交集在数轴上表现为区间的重叠，点集交集涉及坐标平面上的点。理解不同类型集合的交集是关键。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="showNumberSetIntersection()">数集交集</button>
                <button class="interactive-btn" onclick="showPointSetIntersection()">点集交集</button>
                <button class="interactive-btn special-btn" onclick="interactiveSetTypes()">🎮 集合类型</button>
                <button class="interactive-btn" onclick="resetCanvas('intersection-problems')">重置</button>
            </div>

            <div class="control-panel" id="setTypesPanel">
                <h3>🎯 集合类型探索</h3>
                <div class="slider-container">
                    <label>数集A范围：[</label>
                    <input type="range" class="slider" id="numSetA1" min="-5" max="5" value="-2" oninput="updateNumberSets()">
                    <span id="numSetA1Value">-2</span>
                    <label>, </label>
                    <input type="range" class="slider" id="numSetA2" min="-5" max="5" value="3" oninput="updateNumberSets()">
                    <span id="numSetA2Value">3</span>
                    <label>]</label>
                </div>
                <div class="slider-container">
                    <label>数集B范围：[</label>
                    <input type="range" class="slider" id="numSetB1" min="-5" max="5" value="1" oninput="updateNumberSets()">
                    <span id="numSetB1Value">1</span>
                    <label>, </label>
                    <input type="range" class="slider" id="numSetB2" min="-5" max="5" value="5" oninput="updateNumberSets()">
                    <span id="numSetB2Value">5</span>
                    <label>]</label>
                </div>
                <div class="formula-display" id="numberSetResult">
                    A ∩ B = ?
                </div>
            </div>
        </div>

        <!-- 3. 已知交集结果求参数值 -->
        <div class="section" id="intersection-params">
            <h2 class="section-title">3. 已知交集结果求参数值</h2>
            <div class="canvas-container">
                <canvas id="intersectionParamsCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                当集合中含有参数时，根据交集的结果可以确定参数的值。这是集合运算的重要应用。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="solveIntersectionParameter()">求解参数</button>
                <button class="interactive-btn" onclick="visualizeParameterSolution()">可视化过程</button>
                <button class="interactive-btn special-btn" onclick="parameterPractice()">🎮 参数练习</button>
                <button class="interactive-btn" onclick="resetCanvas('intersection-params')">重置</button>
            </div>

            <div class="control-panel" id="parameterPanel">
                <h3>🎯 参数求解器</h3>
                <div class="formula-display">
                    设 A = {x | x² + ax + 1 = 0}, B = {1, 2}
                </div>
                <div class="slider-container">
                    <label>参数 a 的值：</label>
                    <input type="range" class="slider" id="paramA" min="-5" max="5" step="0.1" value="0" oninput="updateParameter()">
                    <span id="paramAValue">0</span>
                </div>
                <div class="formula-display" id="parameterEquation">
                    x² + 0x + 1 = 0
                </div>
                <div class="formula-display" id="parameterIntersection">
                    A ∩ B = ?
                </div>
                <button class="interactive-btn success-btn" onclick="checkParameterCondition()">检查条件</button>
            </div>
        </div>

        <!-- 4. 并集的概念 -->
        <div class="section" id="union">
            <h2 class="section-title">4. 并集的概念</h2>
            <div class="canvas-container">
                <canvas id="unionCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                并集是两个集合所有元素组成的集合。记作A∪B，读作"A并B"。
                <span class="highlight">A∪B = {x | x∈A 或 x∈B}</span>
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="demonstrateUnion()">演示并集概念</button>
                <button class="interactive-btn" onclick="animateUnionProcess()">动画过程</button>
                <button class="interactive-btn special-btn" onclick="interactiveUnion()">🎮 互动并集</button>
                <button class="interactive-btn" onclick="resetCanvas('union')">重置</button>
            </div>

            <div class="control-panel" id="unionPanel">
                <h3>🎯 互动并集演示</h3>
                <div class="venn-container">
                    <div>
                        <label>集合A元素：</label>
                        <input type="text" id="unionSetA" value="1,2,3" placeholder="用逗号分隔">
                    </div>
                    <span class="operation-symbol">∪</span>
                    <div>
                        <label>集合B元素：</label>
                        <input type="text" id="unionSetB" value="3,4,5" placeholder="用逗号分隔">
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <button class="interactive-btn success-btn" onclick="calculateUnion()">计算并集</button>
                    <button class="interactive-btn" onclick="showUnionSteps()">显示步骤</button>
                </div>
                <div class="formula-display" id="unionResult">
                    A ∪ B = ?
                </div>
            </div>

            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">并集定义</div>
                    <div class="concept-desc">
                        A∪B = {x | x∈A 或 x∈B}<br>
                        即：<span class="highlight">属于A或属于B的所有元素</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">并集性质</div>
                    <div class="concept-desc">
                        • A∪B = B∪A (交换律)<br>
                        • A∪A = A (幂等律)<br>
                        • A∪∅ = A (空集性质)<br>
                        • <span class="highlight">A ⊆ A∪B, B ⊆ A∪B</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">韦恩图表示</div>
                    <div class="concept-desc">
                        在韦恩图中，并集表示为<br>
                        两个圆的<span class="highlight">全部区域</span><br>
                        包含所有元素
                    </div>
                </div>
            </div>
        </div>

        <!-- 5. 已知并集结果求参数值 -->
        <div class="section" id="union-problems">
            <h2 class="section-title">5. 已知并集结果求参数值</h2>
            <div class="canvas-container">
                <canvas id="unionProblemsCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                根据并集的结果确定参数值，需要分析元素的包含关系和集合的结构。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="solveUnionParameter()">求解参数</button>
                <button class="interactive-btn" onclick="visualizeUnionSolution()">可视化过程</button>
                <button class="interactive-btn special-btn" onclick="unionParameterPractice()">🎮 并集练习</button>
                <button class="interactive-btn" onclick="resetCanvas('union-problems')">重置</button>
            </div>
        </div>

        <!-- 6. 补集的概念 -->
        <div class="section" id="complement">
            <h2 class="section-title">6. 补集的概念</h2>
            <div class="canvas-container">
                <canvas id="complementCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                补集是全集中不属于某集合的所有元素组成的集合。记作∁ᵤA或Aᶜ，读作"A的补集"。
                <span class="highlight">∁ᵤA = {x | x∈U 且 x∉A}</span>
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="demonstrateComplement()">演示补集概念</button>
                <button class="interactive-btn" onclick="animateComplementProcess()">动画过程</button>
                <button class="interactive-btn special-btn" onclick="interactiveComplement()">🎮 互动补集</button>
                <button class="interactive-btn" onclick="resetCanvas('complement')">重置</button>
            </div>

            <div class="control-panel" id="complementPanel">
                <h3>🎯 互动补集演示</h3>
                <div class="venn-container">
                    <div>
                        <label>全集U元素：</label>
                        <input type="text" id="universalSet" value="1,2,3,4,5,6,7,8" placeholder="用逗号分隔">
                    </div>
                </div>
                <div class="venn-container">
                    <div>
                        <label>集合A元素：</label>
                        <input type="text" id="complementSetA" value="2,4,6" placeholder="用逗号分隔">
                    </div>
                    <span class="operation-symbol">∁</span>
                    <div>
                        <label>A的补集：</label>
                        <span id="complementDisplay">?</span>
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <button class="interactive-btn success-btn" onclick="calculateComplement()">计算补集</button>
                    <button class="interactive-btn" onclick="showComplementSteps()">显示步骤</button>
                </div>
                <div class="formula-display" id="complementResult">
                    ∁ᵤA = ?
                </div>
            </div>

            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">补集定义</div>
                    <div class="concept-desc">
                        ∁ᵤA = {x | x∈U 且 x∉A}<br>
                        即：<span class="highlight">全集中不属于A的元素</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">补集性质</div>
                    <div class="concept-desc">
                        • A∪∁ᵤA = U<br>
                        • A∩∁ᵤA = ∅<br>
                        • ∁ᵤ(∁ᵤA) = A<br>
                        • <span class="highlight">∁ᵤU = ∅, ∁ᵤ∅ = U</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">德摩根定律</div>
                    <div class="concept-desc">
                        • ∁ᵤ(A∪B) = ∁ᵤA∩∁ᵤB<br>
                        • ∁ᵤ(A∩B) = ∁ᵤA∪∁ᵤB<br>
                        <span class="highlight">补集运算的重要规律</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 7. 集合的混合运算 -->
        <div class="section" id="mixed-operations">
            <h2 class="section-title">7. 集合的混合运算</h2>
            <div class="canvas-container">
                <canvas id="mixedOperationsCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                集合的混合运算涉及交集、并集、补集的组合使用。需要注意运算顺序和括号的使用。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="demonstrateMixedOperations()">演示混合运算</button>
                <button class="interactive-btn" onclick="showOperationPriority()">运算优先级</button>
                <button class="interactive-btn special-btn" onclick="interactiveMixedOps()">🎮 混合运算</button>
                <button class="interactive-btn" onclick="resetCanvas('mixed-operations')">重置</button>
            </div>

            <div class="control-panel" id="mixedOpsPanel">
                <h3>🎯 混合运算计算器</h3>
                <div class="formula-display">
                    计算：(A∪B)∩∁ᵤC
                </div>
                <div class="venn-container">
                    <div>
                        <label>A = </label>
                        <input type="text" id="mixedSetA" value="1,2,3" placeholder="用逗号分隔">
                    </div>
                    <div>
                        <label>B = </label>
                        <input type="text" id="mixedSetB" value="3,4,5" placeholder="用逗号分隔">
                    </div>
                    <div>
                        <label>C = </label>
                        <input type="text" id="mixedSetC" value="2,4,6" placeholder="用逗号分隔">
                    </div>
                </div>
                <div class="venn-container">
                    <div>
                        <label>全集U = </label>
                        <input type="text" id="mixedUniversal" value="1,2,3,4,5,6,7,8" placeholder="用逗号分隔">
                    </div>
                </div>
                <div style="margin: 20px 0;">
                    <button class="interactive-btn success-btn" onclick="calculateMixedOperation()">逐步计算</button>
                    <button class="interactive-btn" onclick="showMixedSteps()">显示步骤</button>
                </div>
                <div class="formula-display" id="mixedResult">
                    结果 = ?
                </div>
            </div>
        </div>

        <!-- 8. 用韦恩图表示集合混合运算 -->
        <div class="section" id="venn-diagrams">
            <h2 class="section-title">8. 用韦恩图表示集合混合运算</h2>
            <div class="canvas-container">
                <canvas id="vennDiagramsCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                韦恩图是表示集合运算的直观工具，通过图形可以清楚地看到各种运算的结果。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="drawVennDiagrams()">绘制韦恩图</button>
                <button class="interactive-btn" onclick="animateVennOperations()">动画运算</button>
                <button class="interactive-btn special-btn" onclick="interactiveVenn()">🎮 韦恩图工具</button>
                <button class="interactive-btn" onclick="resetCanvas('venn-diagrams')">重置</button>
            </div>

            <div class="control-panel" id="vennPanel">
                <h3>🎯 韦恩图运算工具</h3>
                <div style="margin: 15px 0;">
                    <label>选择运算类型：</label>
                    <select id="vennOperation" onchange="updateVennOperation()">
                        <option value="intersection">A ∩ B (交集)</option>
                        <option value="union">A ∪ B (并集)</option>
                        <option value="complement-a">∁ᵤA (A的补集)</option>
                        <option value="complement-b">∁ᵤB (B的补集)</option>
                        <option value="difference">A - B (差集)</option>
                        <option value="symmetric">A ⊕ B (对称差)</option>
                    </select>
                </div>
                <div class="formula-display" id="vennOperationDesc">
                    选择运算查看韦恩图表示
                </div>
                <button class="interactive-btn success-btn" onclick="highlightVennResult()">高亮结果</button>
            </div>
        </div>

        <!-- 9. 由混合运算求参数值 -->
        <div class="section" id="parameter-solving">
            <h2 class="section-title">9. 由混合运算求参数值</h2>
            <div class="canvas-container">
                <canvas id="parameterSolvingCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                通过混合运算的结果来确定参数值，需要综合运用各种集合运算的性质。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="solveMixedParameter()">求解参数</button>
                <button class="interactive-btn" onclick="visualizeMixedSolution()">可视化过程</button>
                <button class="interactive-btn special-btn" onclick="mixedParameterPractice()">🎮 参数挑战</button>
                <button class="interactive-btn" onclick="resetCanvas('parameter-solving')">重置</button>
            </div>
        </div>

        <!-- 10. 集合中元素个数的计算 -->
        <div class="section" id="element-counting">
            <h2 class="section-title">10. 集合中元素个数的计算</h2>
            <div class="canvas-container">
                <canvas id="elementCountingCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                集合元素个数的计算涉及容斥原理。对于有限集合A和B：
                <span class="highlight">|A∪B| = |A| + |B| - |A∩B|</span>
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="demonstrateElementCounting()">演示计数公式</button>
                <button class="interactive-btn" onclick="showInclusionExclusion()">容斥原理</button>
                <button class="interactive-btn special-btn" onclick="interactiveElementCount()">🎮 元素计数</button>
                <button class="interactive-btn" onclick="resetCanvas('element-counting')">重置</button>
            </div>

            <div class="control-panel" id="elementCountPanel">
                <h3>🎯 元素计数器</h3>
                <div class="slider-container">
                    <label>|A| = </label>
                    <input type="range" class="slider" id="countA" min="1" max="20" value="8" oninput="updateElementCount()">
                    <span id="countAValue">8</span>
                </div>
                <div class="slider-container">
                    <label>|B| = </label>
                    <input type="range" class="slider" id="countB" min="1" max="20" value="6" oninput="updateElementCount()">
                    <span id="countBValue">6</span>
                </div>
                <div class="slider-container">
                    <label>|A∩B| = </label>
                    <input type="range" class="slider" id="countIntersection" min="0" max="10" value="3" oninput="updateElementCount()">
                    <span id="countIntersectionValue">3</span>
                </div>
                <div class="formula-display" id="countingFormula">
                    |A∪B| = |A| + |B| - |A∩B| = 8 + 6 - 3 = 11
                </div>
                <button class="interactive-btn success-btn" onclick="visualizeElementCount()">可视化计数</button>
            </div>

            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">容斥原理</div>
                    <div class="concept-desc">
                        |A∪B| = |A| + |B| - |A∩B|<br>
                        避免重复计算<span class="highlight">交集元素</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">三集合公式</div>
                    <div class="concept-desc">
                        |A∪B∪C| = |A| + |B| + |C|<br>
                        - |A∩B| - |A∩C| - |B∩C|<br>
                        + <span class="highlight">|A∩B∩C|</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">实际应用</div>
                    <div class="concept-desc">
                        • 统计学调查<br>
                        • 概率计算<br>
                        • <span class="highlight">数据分析</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 11. 点集运算易错点辨析 -->
        <div class="section" id="error-analysis">
            <h2 class="section-title">11. 点集运算易错点辨析</h2>
            <div class="canvas-container">
                <canvas id="errorAnalysisCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                集合运算中常见的错误包括：混淆运算符号、忽略空集情况、参数范围错误等。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="showCommonErrors()">常见错误</button>
                <button class="interactive-btn" onclick="demonstrateCorrections()">正确方法</button>
                <button class="interactive-btn special-btn" onclick="errorQuiz()">🎮 错误识别</button>
                <button class="interactive-btn" onclick="resetCanvas('error-analysis')">重置</button>
            </div>

            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">符号混淆</div>
                    <div class="concept-desc">
                        ❌ A∩B = A∪B<br>
                        ✅ 交集≠并集<br>
                        <span class="highlight">注意运算符号的区别</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">空集遗漏</div>
                    <div class="concept-desc">
                        ❌ 忽略A∩B = ∅的情况<br>
                        ✅ 考虑所有可能性<br>
                        <span class="highlight">空集也是重要结果</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">参数范围</div>
                    <div class="concept-desc">
                        ❌ 参数取值不完整<br>
                        ✅ 分类讨论所有情况<br>
                        <span class="highlight">注意边界条件</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 12. 综合练习 -->
        <div class="section" id="comprehensive">
            <h2 class="section-title">12. 综合练习</h2>
            <div class="canvas-container">
                <canvas id="comprehensiveCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                通过综合练习巩固集合运算的各种知识点，提高解题能力和运算技巧。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="startComprehensiveQuiz()">开始测验</button>
                <button class="interactive-btn" onclick="showQuizSolution()">查看解答</button>
                <button class="interactive-btn special-btn" onclick="adaptiveQuiz()">🎮 智能练习</button>
                <button class="interactive-btn" onclick="resetCanvas('comprehensive')">重置</button>
            </div>

            <div class="control-panel" id="comprehensivePanel">
                <h3>🎯 智能练习系统</h3>
                <div id="currentQuestion" class="formula-display">
                    点击"开始测验"开始练习
                </div>
                <div style="margin: 15px 0;">
                    <input type="text" id="quizAnswer" placeholder="输入你的答案" style="padding: 10px; border-radius: 5px; border: 2px solid #667eea; width: 250px;">
                    <button class="interactive-btn success-btn" onclick="checkQuizAnswer()">提交答案</button>
                </div>
                <div id="quizFeedback" class="formula-display" style="display: none;">
                    反馈信息
                </div>
                <div id="quizScore" class="formula-display">
                    得分：0 / 0 | 正确率：0%
                </div>
                <div style="margin: 15px 0;">
                    <button class="interactive-btn" onclick="nextQuestion()">下一题</button>
                    <button class="interactive-btn" onclick="showHint()">提示</button>
                    <button class="interactive-btn" onclick="resetQuiz()">重新开始</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取所有画布和上下文
        const canvases = {
            intersection: document.getElementById('intersectionCanvas'),
            'intersection-problems': document.getElementById('intersectionProblemsCanvas'),
            'intersection-params': document.getElementById('intersectionParamsCanvas'),
            union: document.getElementById('unionCanvas'),
            'union-problems': document.getElementById('unionProblemsCanvas'),
            complement: document.getElementById('complementCanvas'),
            'mixed-operations': document.getElementById('mixedOperationsCanvas'),
            'venn-diagrams': document.getElementById('vennDiagramsCanvas'),
            'parameter-solving': document.getElementById('parameterSolvingCanvas'),
            'element-counting': document.getElementById('elementCountingCanvas'),
            'error-analysis': document.getElementById('errorAnalysisCanvas'),
            comprehensive: document.getElementById('comprehensiveCanvas')
        };

        const contexts = {};
        Object.keys(canvases).forEach(key => {
            contexts[key] = canvases[key].getContext('2d');
        });

        // 动画变量
        let animationFrames = {};
        let currentAnimations = {};

        // 测验系统变量
        let currentQuestionIndex = 0;
        let quizScore = 0;
        let totalQuestions = 0;
        let correctAnswers = 0;

        // 题目库
        const questions = [
            {
                question: "设A = {1, 2, 3}, B = {2, 3, 4}，求A∩B",
                answer: "{2, 3}",
                hint: "找出既属于A又属于B的元素",
                type: "intersection"
            },
            {
                question: "设A = {1, 2}, B = {3, 4}，求A∪B",
                answer: "{1, 2, 3, 4}",
                hint: "找出属于A或属于B的所有元素",
                type: "union"
            },
            {
                question: "设U = {1, 2, 3, 4, 5}, A = {1, 3, 5}，求∁ᵤA",
                answer: "{2, 4}",
                hint: "找出全集中不属于A的元素",
                type: "complement"
            },
            {
                question: "若|A| = 5, |B| = 3, |A∩B| = 2，求|A∪B|",
                answer: "6",
                hint: "使用容斥原理：|A∪B| = |A| + |B| - |A∩B|",
                type: "counting"
            }
        ];

        // 初始化所有画布
        function initializeCanvases() {
            Object.keys(contexts).forEach(key => {
                resetCanvas(key);
            });
        }

        function resetCanvas(canvasName) {
            const ctx = contexts[canvasName];
            if (!ctx) return;

            ctx.clearRect(0, 0, canvases[canvasName].width, canvases[canvasName].height);

            ctx.fillStyle = '#999';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('点击按钮开始学习', canvases[canvasName].width / 2, canvases[canvasName].height / 2);
        }

        // 1. 交集功能
        function demonstrateIntersection() {
            const ctx = contexts.intersection;
            ctx.clearRect(0, 0, canvases.intersection.width, canvases.intersection.height);

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('交集演示', 400, 40);

            // 绘制韦恩图
            setTimeout(() => {
                drawVennDiagram(ctx, 300, 250, 80, 500, 250, 80, '#ff6b6b', '#667eea', 'A', 'B');

                // 高亮交集部分
                setTimeout(() => {
                    highlightIntersection(ctx, 300, 250, 80, 500, 250, 80);
                }, 1000);

                // 显示元素
                setTimeout(() => {
                    showSetElements(ctx, 300, 250, ['1', '2'], 'A独有');
                    showSetElements(ctx, 500, 250, ['4', '5'], 'B独有');
                    showSetElements(ctx, 400, 250, ['3'], '交集');
                }, 2000);

                // 显示结果
                setTimeout(() => {
                    ctx.fillStyle = '#44ff44';
                    ctx.font = 'bold 18px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('A = {1, 2, 3}, B = {3, 4, 5}', 400, 420);
                    ctx.fillText('A ∩ B = {3}', 400, 450);
                }, 3000);
            }, 500);
        }

        function animateIntersectionProcess() {
            const ctx = contexts.intersection;
            ctx.clearRect(0, 0, canvases.intersection.width, canvases.intersection.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('交集形成过程', 400, 40);

            const elementsA = ['1', '2', '3'];
            const elementsB = ['3', '4', '5'];
            const intersection = ['3'];

            // 显示集合A
            setTimeout(() => {
                ctx.fillStyle = '#ff6b6b';
                ctx.font = '18px Microsoft YaHei';
                ctx.fillText('集合A = {1, 2, 3}', 200, 100);

                elementsA.forEach((elem, index) => {
                    setTimeout(() => {
                        drawElement(ctx, 150 + index * 60, 150, elem, '#ff6b6b');
                    }, index * 300);
                });
            }, 500);

            // 显示集合B
            setTimeout(() => {
                ctx.fillStyle = '#667eea';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('集合B = {3, 4, 5}', 600, 100);

                elementsB.forEach((elem, index) => {
                    setTimeout(() => {
                        drawElement(ctx, 550 + index * 60, 150, elem, '#667eea');
                    }, index * 300);
                });
            }, 2000);

            // 找出交集
            setTimeout(() => {
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('寻找共同元素...', 400, 250);

                setTimeout(() => {
                    drawElement(ctx, 400, 300, '3', '#44ff44');
                    ctx.fillStyle = '#44ff44';
                    ctx.font = 'bold 18px Microsoft YaHei';
                    ctx.fillText('A ∩ B = {3}', 400, 380);
                }, 1000);
            }, 4000);
        }

        function interactiveIntersection() {
            document.getElementById('intersectionPanel').classList.add('active');
            calculateIntersection();
        }

        function calculateIntersection() {
            const setAInput = document.getElementById('setAElements').value;
            const setBInput = document.getElementById('setBElements').value;

            const setA = setAInput.split(',').map(x => x.trim()).filter(x => x);
            const setB = setBInput.split(',').map(x => x.trim()).filter(x => x);

            const intersection = setA.filter(x => setB.includes(x));

            const result = intersection.length > 0 ? `{${intersection.join(', ')}}` : '∅';
            document.getElementById('intersectionResult').textContent = `A ∩ B = ${result}`;

            // 在画布上可视化
            const ctx = contexts.intersection;
            ctx.clearRect(0, 0, canvases.intersection.width, canvases.intersection.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 互动交集计算', 400, 40);

            // 绘制集合
            drawInteractiveSet(ctx, 250, 200, setA, '#ff6b6b', 'A');
            drawInteractiveSet(ctx, 550, 200, setB, '#667eea', 'B');

            // 显示交集
            if (intersection.length > 0) {
                ctx.fillStyle = '#44ff44';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`交集: {${intersection.join(', ')}}`, 400, 400);

                // 高亮交集元素
                intersection.forEach((elem, index) => {
                    drawElement(ctx, 350 + index * 40, 350, elem, '#44ff44');
                });
            } else {
                ctx.fillStyle = '#ff6b6b';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('交集为空集 ∅', 400, 400);
            }
        }

        function showIntersectionSteps() {
            const setAInput = document.getElementById('setAElements').value;
            const setBInput = document.getElementById('setBElements').value;

            const setA = setAInput.split(',').map(x => x.trim()).filter(x => x);
            const setB = setBInput.split(',').map(x => x.trim()).filter(x => x);

            let steps = `计算步骤：\n`;
            steps += `1. 集合A = {${setA.join(', ')}}\n`;
            steps += `2. 集合B = {${setB.join(', ')}}\n`;
            steps += `3. 找出既属于A又属于B的元素：\n`;

            const intersection = [];
            setA.forEach(elem => {
                if (setB.includes(elem)) {
                    intersection.push(elem);
                    steps += `   ${elem} ∈ A 且 ${elem} ∈ B ✓\n`;
                } else {
                    steps += `   ${elem} ∈ A 但 ${elem} ∉ B ✗\n`;
                }
            });

            const result = intersection.length > 0 ? `{${intersection.join(', ')}}` : '∅';
            steps += `4. 因此 A ∩ B = ${result}`;

            alert(steps);
        }

        // 2. 数集和点集交集功能
        function showNumberSetIntersection() {
            const ctx = contexts['intersection-problems'];
            ctx.clearRect(0, 0, canvases['intersection-problems'].width, canvases['intersection-problems'].height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('数集交集演示', 400, 40);

            // 绘制数轴
            drawNumberLine(ctx, 100, 200, 600, [-3, -2, -1, 0, 1, 2, 3, 4, 5]);

            // 区间A: [-1, 3]
            setTimeout(() => {
                drawInterval(ctx, 200, 400, 180, '#ff6b6b', 'A: [-1, 3]');
            }, 500);

            // 区间B: [1, 4]
            setTimeout(() => {
                drawInterval(ctx, 300, 500, 220, '#667eea', 'B: [1, 4]');
            }, 1000);

            // 高亮交集
            setTimeout(() => {
                drawInterval(ctx, 300, 400, 240, '#44ff44', 'A∩B: [1, 3]');

                ctx.fillStyle = '#44ff44';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('A ∩ B = [1, 3]', 400, 320);
            }, 1500);
        }

        function showPointSetIntersection() {
            const ctx = contexts['intersection-problems'];
            ctx.clearRect(0, 0, canvases['intersection-problems'].width, canvases['intersection-problems'].height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('点集交集演示', 400, 40);

            // 绘制坐标系
            drawCoordinateSystem(ctx, 400, 250, 150);

            // 集合A: 圆 x² + y² = 1
            setTimeout(() => {
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(400, 250, 80, 0, 2 * Math.PI);
                ctx.stroke();

                ctx.fillStyle = '#ff6b6b';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('A: x² + y² = 1', 300, 120);
            }, 500);

            // 集合B: 直线 y = x
            setTimeout(() => {
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(250, 400);
                ctx.lineTo(550, 100);
                ctx.stroke();

                ctx.fillStyle = '#667eea';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('B: y = x', 500, 120);
            }, 1000);

            // 标记交点
            setTimeout(() => {
                const intersectionPoints = [
                    {x: 400 + 80/Math.sqrt(2), y: 250 - 80/Math.sqrt(2)},
                    {x: 400 - 80/Math.sqrt(2), y: 250 + 80/Math.sqrt(2)}
                ];

                intersectionPoints.forEach(point => {
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, 6, 0, 2 * Math.PI);
                    ctx.fillStyle = '#44ff44';
                    ctx.fill();
                });

                ctx.fillStyle = '#44ff44';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('A ∩ B = 两个交点', 400, 420);
            }, 1500);
        }

        function interactiveSetTypes() {
            document.getElementById('setTypesPanel').classList.add('active');
            updateNumberSets();
        }

        function updateNumberSets() {
            const a1 = parseInt(document.getElementById('numSetA1').value);
            const a2 = parseInt(document.getElementById('numSetA2').value);
            const b1 = parseInt(document.getElementById('numSetB1').value);
            const b2 = parseInt(document.getElementById('numSetB2').value);

            document.getElementById('numSetA1Value').textContent = a1;
            document.getElementById('numSetA2Value').textContent = a2;
            document.getElementById('numSetB1Value').textContent = b1;
            document.getElementById('numSetB2Value').textContent = b2;

            // 计算交集
            const intersectionStart = Math.max(a1, b1);
            const intersectionEnd = Math.min(a2, b2);

            let result;
            if (intersectionStart <= intersectionEnd) {
                result = `[${intersectionStart}, ${intersectionEnd}]`;
            } else {
                result = '∅ (空集)';
            }

            document.getElementById('numberSetResult').textContent = `A ∩ B = ${result}`;

            // 可视化
            const ctx = contexts['intersection-problems'];
            ctx.clearRect(0, 0, canvases['intersection-problems'].width, canvases['intersection-problems'].height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 数集交集计算器', 400, 40);

            drawNumberLine(ctx, 100, 200, 600, [-5, -4, -3, -2, -1, 0, 1, 2, 3, 4, 5]);

            // 绘制区间A和B
            const scale = 60;
            const offset = 400;

            if (a1 <= a2) {
                drawInterval(ctx, offset + a1 * scale, offset + a2 * scale, 180, '#ff6b6b', `A: [${a1}, ${a2}]`);
            }
            if (b1 <= b2) {
                drawInterval(ctx, offset + b1 * scale, offset + b2 * scale, 220, '#667eea', `B: [${b1}, ${b2}]`);
            }

            // 绘制交集
            if (intersectionStart <= intersectionEnd) {
                drawInterval(ctx, offset + intersectionStart * scale, offset + intersectionEnd * scale, 240, '#44ff44', `A∩B: [${intersectionStart}, ${intersectionEnd}]`);
            }
        }

        // 3. 并集功能
        function demonstrateUnion() {
            const ctx = contexts.union;
            ctx.clearRect(0, 0, canvases.union.width, canvases.union.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('并集演示', 400, 40);

            // 绘制韦恩图
            setTimeout(() => {
                drawVennDiagram(ctx, 300, 250, 80, 500, 250, 80, '#ff6b6b', '#667eea', 'A', 'B');

                // 高亮并集部分
                setTimeout(() => {
                    highlightUnion(ctx, 300, 250, 80, 500, 250, 80);
                }, 1000);

                // 显示元素
                setTimeout(() => {
                    showSetElements(ctx, 250, 220, ['1'], 'A独有');
                    showSetElements(ctx, 550, 220, ['4'], 'B独有');
                    showSetElements(ctx, 400, 250, ['2', '3'], '共同');
                }, 2000);

                // 显示结果
                setTimeout(() => {
                    ctx.fillStyle = '#44ff44';
                    ctx.font = 'bold 18px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('A = {1, 2, 3}, B = {2, 3, 4}', 400, 420);
                    ctx.fillText('A ∪ B = {1, 2, 3, 4}', 400, 450);
                }, 3000);
            }, 500);
        }

        function animateUnionProcess() {
            const ctx = contexts.union;
            ctx.clearRect(0, 0, canvases.union.width, canvases.union.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('并集形成过程', 400, 40);

            const elementsA = ['1', '2', '3'];
            const elementsB = ['2', '3', '4'];
            const union = ['1', '2', '3', '4'];

            // 显示集合A
            setTimeout(() => {
                ctx.fillStyle = '#ff6b6b';
                ctx.font = '18px Microsoft YaHei';
                ctx.fillText('集合A = {1, 2, 3}', 200, 100);

                elementsA.forEach((elem, index) => {
                    setTimeout(() => {
                        drawElement(ctx, 150 + index * 60, 150, elem, '#ff6b6b');
                    }, index * 300);
                });
            }, 500);

            // 显示集合B
            setTimeout(() => {
                ctx.fillStyle = '#667eea';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('集合B = {2, 3, 4}', 600, 100);

                elementsB.forEach((elem, index) => {
                    setTimeout(() => {
                        drawElement(ctx, 550 + index * 60, 150, elem, '#667eea');
                    }, index * 300);
                });
            }, 2000);

            // 合并元素
            setTimeout(() => {
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('合并所有元素（去重）...', 400, 250);

                setTimeout(() => {
                    union.forEach((elem, index) => {
                        drawElement(ctx, 300 + index * 50, 300, elem, '#44ff44');
                    });

                    ctx.fillStyle = '#44ff44';
                    ctx.font = 'bold 18px Microsoft YaHei';
                    ctx.fillText('A ∪ B = {1, 2, 3, 4}', 400, 380);
                }, 1000);
            }, 4000);
        }

        function interactiveUnion() {
            document.getElementById('unionPanel').classList.add('active');
            calculateUnion();
        }

        function calculateUnion() {
            const setAInput = document.getElementById('unionSetA').value;
            const setBInput = document.getElementById('unionSetB').value;

            const setA = setAInput.split(',').map(x => x.trim()).filter(x => x);
            const setB = setBInput.split(',').map(x => x.trim()).filter(x => x);

            const union = [...new Set([...setA, ...setB])];

            const result = union.length > 0 ? `{${union.join(', ')}}` : '∅';
            document.getElementById('unionResult').textContent = `A ∪ B = ${result}`;

            // 在画布上可视化
            const ctx = contexts.union;
            ctx.clearRect(0, 0, canvases.union.width, canvases.union.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 互动并集计算', 400, 40);

            // 绘制集合
            drawInteractiveSet(ctx, 250, 200, setA, '#ff6b6b', 'A');
            drawInteractiveSet(ctx, 550, 200, setB, '#667eea', 'B');

            // 显示并集
            ctx.fillStyle = '#44ff44';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(`并集: {${union.join(', ')}}`, 400, 400);

            // 显示并集元素
            union.forEach((elem, index) => {
                drawElement(ctx, 300 + index * 40, 350, elem, '#44ff44');
            });
        }

        function showUnionSteps() {
            const setAInput = document.getElementById('unionSetA').value;
            const setBInput = document.getElementById('unionSetB').value;

            const setA = setAInput.split(',').map(x => x.trim()).filter(x => x);
            const setB = setBInput.split(',').map(x => x.trim()).filter(x => x);

            let steps = `计算步骤：\n`;
            steps += `1. 集合A = {${setA.join(', ')}}\n`;
            steps += `2. 集合B = {${setB.join(', ')}}\n`;
            steps += `3. 合并所有元素：\n`;

            const allElements = [...setA, ...setB];
            steps += `   所有元素: [${allElements.join(', ')}]\n`;

            const union = [...new Set(allElements)];
            steps += `4. 去除重复元素：\n`;
            steps += `   A ∪ B = {${union.join(', ')}}`;

            alert(steps);
        }

        // 辅助绘图函数
        function drawVennDiagram(ctx, x1, y1, r1, x2, y2, r2, color1, color2, label1, label2) {
            // 绘制圆A
            ctx.beginPath();
            ctx.arc(x1, y1, r1, 0, 2 * Math.PI);
            ctx.strokeStyle = color1;
            ctx.lineWidth = 3;
            ctx.stroke();
            ctx.fillStyle = color1 + '20';
            ctx.fill();

            // 绘制圆B
            ctx.beginPath();
            ctx.arc(x2, y2, r2, 0, 2 * Math.PI);
            ctx.strokeStyle = color2;
            ctx.lineWidth = 3;
            ctx.stroke();
            ctx.fillStyle = color2 + '20';
            ctx.fill();

            // 标签
            ctx.fillStyle = color1;
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label1, x1 - 40, y1 - 60);

            ctx.fillStyle = color2;
            ctx.fillText(label2, x2 + 40, y2 - 60);
        }

        function highlightIntersection(ctx, x1, y1, r1, x2, y2, r2) {
            // 计算交集区域并高亮
            ctx.save();
            ctx.globalCompositeOperation = 'source-over';

            // 创建剪切路径
            ctx.beginPath();
            ctx.arc(x1, y1, r1, 0, 2 * Math.PI);
            ctx.clip();

            // 绘制第二个圆的填充
            ctx.beginPath();
            ctx.arc(x2, y2, r2, 0, 2 * Math.PI);
            ctx.fillStyle = '#44ff4480';
            ctx.fill();

            ctx.restore();
        }

        function highlightUnion(ctx, x1, y1, r1, x2, y2, r2) {
            // 高亮并集区域
            ctx.save();

            // 绘制两个圆的并集
            ctx.beginPath();
            ctx.arc(x1, y1, r1, 0, 2 * Math.PI);
            ctx.arc(x2, y2, r2, 0, 2 * Math.PI);
            ctx.fillStyle = '#44ff4440';
            ctx.fill();

            ctx.restore();
        }

        function drawElement(ctx, x, y, text, color) {
            ctx.beginPath();
            ctx.arc(x, y, 15, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 4);
        }

        function showSetElements(ctx, x, y, elements, label) {
            elements.forEach((elem, index) => {
                const angle = (index * 2 * Math.PI) / elements.length;
                const elemX = x + Math.cos(angle) * 30;
                const elemY = y + Math.sin(angle) * 30;
                drawElement(ctx, elemX, elemY, elem, '#44ff44');
            });

            ctx.fillStyle = '#333';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 60);
        }

        function drawInteractiveSet(ctx, x, y, elements, color, label) {
            // 绘制集合圆圈
            ctx.beginPath();
            ctx.arc(x, y, 80, 0, 2 * Math.PI);
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.stroke();
            ctx.fillStyle = color + '20';
            ctx.fill();

            // 标签
            ctx.fillStyle = color;
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y - 100);

            // 元素
            elements.forEach((elem, index) => {
                const angle = (index * 2 * Math.PI) / Math.max(elements.length, 1);
                const elemX = x + Math.cos(angle) * 50;
                const elemY = y + Math.sin(angle) * 50;
                drawElement(ctx, elemX, elemY, elem, color);
            });
        }

        function drawNumberLine(ctx, startX, y, width, numbers) {
            // 绘制数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, y);
            ctx.lineTo(startX + width, y);
            ctx.stroke();

            // 绘制刻度和数字
            numbers.forEach((num, index) => {
                const x = startX + (index * width) / (numbers.length - 1);

                // 刻度线
                ctx.beginPath();
                ctx.moveTo(x, y - 10);
                ctx.lineTo(x, y + 10);
                ctx.stroke();

                // 数字
                ctx.fillStyle = '#333';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(num.toString(), x, y + 30);
            });
        }

        function drawInterval(ctx, startX, endX, y, color, label) {
            // 绘制区间线段
            ctx.strokeStyle = color;
            ctx.lineWidth = 6;
            ctx.beginPath();
            ctx.moveTo(startX, y);
            ctx.lineTo(endX, y);
            ctx.stroke();

            // 绘制端点
            ctx.beginPath();
            ctx.arc(startX, y, 5, 0, 2 * Math.PI);
            ctx.arc(endX, y, 5, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();

            // 标签
            ctx.fillStyle = color;
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, (startX + endX) / 2, y - 15);
        }

        function drawCoordinateSystem(ctx, centerX, centerY, scale) {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;

            // x轴
            ctx.beginPath();
            ctx.moveTo(centerX - scale, centerY);
            ctx.lineTo(centerX + scale, centerY);
            ctx.stroke();

            // y轴
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - scale);
            ctx.lineTo(centerX, centerY + scale);
            ctx.stroke();

            // 原点
            ctx.fillStyle = '#333';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('O', centerX - 15, centerY + 15);
        }

        // 补集功能
        function demonstrateComplement() {
            const ctx = contexts.complement;
            ctx.clearRect(0, 0, canvases.complement.width, canvases.complement.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('补集演示', 400, 40);

            // 绘制全集
            setTimeout(() => {
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 3;
                ctx.strokeRect(200, 150, 400, 200);

                ctx.fillStyle = '#333';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'left';
                ctx.fillText('全集 U', 210, 140);
            }, 500);

            // 绘制集合A
            setTimeout(() => {
                ctx.beginPath();
                ctx.arc(400, 250, 80, 0, 2 * Math.PI);
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 3;
                ctx.stroke();
                ctx.fillStyle = '#ff6b6b40';
                ctx.fill();

                ctx.fillStyle = '#ff6b6b';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('A', 400, 200);
            }, 1000);

            // 高亮补集
            setTimeout(() => {
                ctx.save();
                ctx.fillStyle = '#44ff4460';
                ctx.fillRect(200, 150, 400, 200);

                // 挖掉A的部分
                ctx.globalCompositeOperation = 'destination-out';
                ctx.beginPath();
                ctx.arc(400, 250, 80, 0, 2 * Math.PI);
                ctx.fill();

                ctx.restore();

                ctx.fillStyle = '#44ff44';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('∁ᵤA (A的补集)', 400, 400);
            }, 1500);
        }

        function interactiveComplement() {
            document.getElementById('complementPanel').classList.add('active');
            calculateComplement();
        }

        function calculateComplement() {
            const universalInput = document.getElementById('universalSet').value;
            const setAInput = document.getElementById('complementSetA').value;

            const universalSet = universalInput.split(',').map(x => x.trim()).filter(x => x);
            const setA = setAInput.split(',').map(x => x.trim()).filter(x => x);

            const complement = universalSet.filter(x => !setA.includes(x));

            const result = complement.length > 0 ? `{${complement.join(', ')}}` : '∅';
            document.getElementById('complementResult').textContent = `∁ᵤA = ${result}`;
            document.getElementById('complementDisplay').textContent = result;

            // 在画布上可视化
            const ctx = contexts.complement;
            ctx.clearRect(0, 0, canvases.complement.width, canvases.complement.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 互动补集计算', 400, 40);

            // 绘制全集矩形
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.strokeRect(150, 100, 500, 300);

            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText(`全集 U = {${universalSet.join(', ')}}`, 160, 90);

            // 绘制集合A
            if (setA.length > 0) {
                ctx.beginPath();
                ctx.arc(400, 250, 100, 0, 2 * Math.PI);
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 3;
                ctx.stroke();
                ctx.fillStyle = '#ff6b6b40';
                ctx.fill();

                ctx.fillStyle = '#ff6b6b';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`A = {${setA.join(', ')}}`, 400, 180);
            }

            // 显示补集元素
            if (complement.length > 0) {
                ctx.fillStyle = '#44ff44';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`∁ᵤA = {${complement.join(', ')}}`, 400, 450);

                // 在补集区域显示元素
                complement.forEach((elem, index) => {
                    const angle = (index * 2 * Math.PI) / complement.length;
                    const radius = 150;
                    const elemX = 400 + Math.cos(angle) * radius;
                    const elemY = 250 + Math.sin(angle) * radius;

                    // 确保元素在矩形内但在圆外
                    if (elemX > 160 && elemX < 640 && elemY > 110 && elemY < 390) {
                        const distFromCenter = Math.sqrt((elemX - 400) ** 2 + (elemY - 250) ** 2);
                        if (distFromCenter > 110) {
                            drawElement(ctx, elemX, elemY, elem, '#44ff44');
                        }
                    }
                });
            }
        }

        function showComplementSteps() {
            const universalInput = document.getElementById('universalSet').value;
            const setAInput = document.getElementById('complementSetA').value;

            const universalSet = universalInput.split(',').map(x => x.trim()).filter(x => x);
            const setA = setAInput.split(',').map(x => x.trim()).filter(x => x);

            let steps = `计算步骤：\n`;
            steps += `1. 全集 U = {${universalSet.join(', ')}}\n`;
            steps += `2. 集合 A = {${setA.join(', ')}}\n`;
            steps += `3. 找出U中不属于A的元素：\n`;

            const complement = [];
            universalSet.forEach(elem => {
                if (!setA.includes(elem)) {
                    complement.push(elem);
                    steps += `   ${elem} ∈ U 且 ${elem} ∉ A ✓\n`;
                } else {
                    steps += `   ${elem} ∈ U 但 ${elem} ∈ A ✗\n`;
                }
            });

            const result = complement.length > 0 ? `{${complement.join(', ')}}` : '∅';
            steps += `4. 因此 ∁ᵤA = ${result}`;

            alert(steps);
        }

        // 测验系统功能
        function startComprehensiveQuiz() {
            document.getElementById('comprehensivePanel').classList.add('active');
            currentQuestionIndex = 0;
            quizScore = 0;
            totalQuestions = 0;
            correctAnswers = 0;
            showCurrentQuestion();
        }

        function showCurrentQuestion() {
            if (currentQuestionIndex < questions.length) {
                const question = questions[currentQuestionIndex];
                document.getElementById('currentQuestion').textContent = question.question;
                document.getElementById('quizAnswer').value = '';
                document.getElementById('quizFeedback').style.display = 'none';
            } else {
                document.getElementById('currentQuestion').textContent = '测验完成！';
                document.getElementById('quizAnswer').style.display = 'none';

                const accuracy = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
                document.getElementById('quizScore').textContent =
                    `最终得分：${correctAnswers} / ${totalQuestions} | 正确率：${accuracy}%`;
            }
            updateQuizScore();
        }

        function checkQuizAnswer() {
            if (currentQuestionIndex >= questions.length) return;

            const userAnswer = document.getElementById('quizAnswer').value.trim();
            const correctAnswer = questions[currentQuestionIndex].answer;
            const feedback = document.getElementById('quizFeedback');

            totalQuestions++;

            if (userAnswer === correctAnswer) {
                correctAnswers++;
                feedback.textContent = '✓ 正确！';
                feedback.style.color = '#44ff44';
            } else {
                feedback.textContent = `✗ 错误。正确答案是：${correctAnswer}`;
                feedback.style.color = '#ff6b6b';
            }

            feedback.style.display = 'block';
            updateQuizScore();

            // 在画布上可视化答案
            visualizeQuizAnswer();
        }

        function visualizeQuizAnswer() {
            const ctx = contexts.comprehensive;
            ctx.clearRect(0, 0, canvases.comprehensive.width, canvases.comprehensive.height);

            const question = questions[currentQuestionIndex];

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('题目可视化', 400, 40);

            // 根据题目类型绘制不同的可视化
            switch (question.type) {
                case 'intersection':
                    drawVennDiagram(ctx, 300, 250, 80, 500, 250, 80, '#ff6b6b', '#667eea', 'A', 'B');
                    highlightIntersection(ctx, 300, 250, 80, 500, 250, 80);
                    break;
                case 'union':
                    drawVennDiagram(ctx, 300, 250, 80, 500, 250, 80, '#ff6b6b', '#667eea', 'A', 'B');
                    highlightUnion(ctx, 300, 250, 80, 500, 250, 80);
                    break;
                case 'complement':
                    ctx.strokeRect(200, 150, 400, 200);
                    ctx.beginPath();
                    ctx.arc(400, 250, 80, 0, 2 * Math.PI);
                    ctx.stroke();
                    break;
                case 'counting':
                    drawElementCountVisualization(ctx);
                    break;
            }
        }

        function drawElementCountVisualization(ctx) {
            // 绘制容斥原理图
            drawVennDiagram(ctx, 300, 250, 80, 500, 250, 80, '#ff6b6b', '#667eea', 'A', 'B');

            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('|A| = 5', 250, 200);
            ctx.fillText('|B| = 3', 550, 200);
            ctx.fillText('|A∩B| = 2', 400, 250);
            ctx.fillText('|A∪B| = 5 + 3 - 2 = 6', 400, 400);
        }

        function nextQuestion() {
            currentQuestionIndex++;
            showCurrentQuestion();
        }

        function showHint() {
            if (currentQuestionIndex < questions.length) {
                const hint = questions[currentQuestionIndex].hint;
                alert(`提示：${hint}`);
            }
        }

        function updateQuizScore() {
            const accuracy = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
            document.getElementById('quizScore').textContent =
                `得分：${correctAnswers} / ${totalQuestions} | 正确率：${accuracy}%`;
        }

        function resetQuiz() {
            currentQuestionIndex = 0;
            quizScore = 0;
            totalQuestions = 0;
            correctAnswers = 0;
            showCurrentQuestion();
        }

        function showQuizSolution() {
            if (currentQuestionIndex < questions.length) {
                const question = questions[currentQuestionIndex];
                alert(`解答：${question.answer}\n提示：${question.hint}`);
            }
        }

        function adaptiveQuiz() {
            alert('🎮 智能练习系统\n\n根据你的答题情况，系统会自动调整题目难度，提供个性化的学习体验！');
            startComprehensiveQuiz();
        }

        // 其他功能的占位函数
        function solveIntersectionParameter() {
            alert('参数求解功能：根据交集结果确定参数值');
        }

        function visualizeParameterSolution() {
            alert('可视化参数求解过程');
        }

        function parameterPractice() {
            alert('🎮 参数练习：交互式参数求解练习');
        }

        function updateParameter() {
            const a = parseFloat(document.getElementById('paramA').value);
            document.getElementById('paramAValue').textContent = a;
            document.getElementById('parameterEquation').textContent = `x² + ${a}x + 1 = 0`;
        }

        function checkParameterCondition() {
            alert('检查参数条件功能');
        }

        function solveUnionParameter() {
            alert('并集参数求解功能');
        }

        function visualizeUnionSolution() {
            alert('并集参数可视化');
        }

        function unionParameterPractice() {
            alert('🎮 并集参数练习');
        }

        function animateComplementProcess() {
            alert('补集动画过程');
        }

        function demonstrateMixedOperations() {
            alert('混合运算演示');
        }

        function showOperationPriority() {
            alert('运算优先级说明');
        }

        function interactiveMixedOps() {
            alert('🎮 混合运算练习');
        }

        function calculateMixedOperation() {
            alert('混合运算计算');
        }

        function showMixedSteps() {
            alert('混合运算步骤');
        }

        function drawVennDiagrams() {
            alert('韦恩图绘制');
        }

        function animateVennOperations() {
            alert('韦恩图运算动画');
        }

        function interactiveVenn() {
            alert('🎮 韦恩图工具');
        }

        function updateVennOperation() {
            alert('更新韦恩图运算');
        }

        function highlightVennResult() {
            alert('高亮韦恩图结果');
        }

        function solveMixedParameter() {
            alert('混合运算参数求解');
        }

        function visualizeMixedSolution() {
            alert('混合运算可视化');
        }

        function mixedParameterPractice() {
            alert('🎮 混合参数挑战');
        }

        function demonstrateElementCounting() {
            alert('元素计数演示');
        }

        function showInclusionExclusion() {
            alert('容斥原理说明');
        }

        function interactiveElementCount() {
            alert('🎮 元素计数练习');
        }

        function updateElementCount() {
            const countA = parseInt(document.getElementById('countA').value);
            const countB = parseInt(document.getElementById('countB').value);
            const countIntersection = parseInt(document.getElementById('countIntersection').value);

            document.getElementById('countAValue').textContent = countA;
            document.getElementById('countBValue').textContent = countB;
            document.getElementById('countIntersectionValue').textContent = countIntersection;

            const countUnion = countA + countB - countIntersection;
            document.getElementById('countingFormula').textContent =
                `|A∪B| = |A| + |B| - |A∩B| = ${countA} + ${countB} - ${countIntersection} = ${countUnion}`;
        }

        function visualizeElementCount() {
            alert('可视化元素计数');
        }

        function showCommonErrors() {
            alert('常见错误展示');
        }

        function demonstrateCorrections() {
            alert('正确方法演示');
        }

        function errorQuiz() {
            alert('🎮 错误识别练习');
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initializeCanvases();

            // 平滑滚动
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // 初始化控制面板
            updateElementCount();
        });
    </script>
</body>
</html>
