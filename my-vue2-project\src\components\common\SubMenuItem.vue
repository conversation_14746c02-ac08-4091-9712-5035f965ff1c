<template>
  <el-submenu v-if="item.children && item.children.length > 0" :index="item.name">
    <template slot="title">
      <i :class="item.icon"></i>
      <span>{{ item.name }}</span>
    </template>
    <sub-menu-item
      v-for="child in item.children"
      :key="child.name"
      :item="child"
    />
  </el-submenu>
  <el-menu-item v-else :index="item.name">
    <i :class="item.icon"></i>
    <span slot="title">{{ item.name }}</span>
  </el-menu-item>
</template>

<script>
export default {
  name: 'SubMenuItem',
  props: {
    item: {
      type: Object,
      required: true
    }
  }
}
</script> 