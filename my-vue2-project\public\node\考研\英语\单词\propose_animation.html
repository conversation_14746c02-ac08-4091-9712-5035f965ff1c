<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Propose</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            background-color: #f0f2f5;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 900px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .story-explanation {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            width: 100%;
            border-left: 5px solid #007bff;
        }

        .story-explanation p {
            margin: 0;
            line-height: 1.6;
            color: #555;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-radius: 8px;
            overflow: hidden;
        }

        canvas {
            display: block;
            width: 100%;
            height: auto;
            background-color: #ffffff;
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        button {
            padding: 10px 20px;
            font-size: 1em;
            color: #fff;
            background-color: #007bff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }

        #explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 8px;
            width: 100%;
            text-align: center;
            font-size: 1.2em;
            color: #333;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.5s;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>
        propose
        <span style="font-size: 0.5em; color: #555;">(pro- + pose)</span>
    </h1>

    <div class="story-explanation">
        <p><strong>故事背景：</strong>想象一位充满抱负的年轻建筑师，他梦想着在被湍急河流分隔的两座城镇之间建立一座桥梁，连接彼此，带来繁荣。这个故事将通过"propose"这个词的词源来展开。</p>
    </div>

    <div class="canvas-container">
        <canvas id="wordAnimation" width="800" height="450"></canvas>
    </div>

    <div class="controls">
        <button id="playBtn">播放完整动画</button>
        <button id="proBtn">第一幕: pro- (向前)</button>
        <button id="poseBtn">第二幕: pose (放置)</button>
        <button id="proposeBtn">第三幕: propose (提出/提议)</button>
        <button id="resetBtn">重置</button>
    </div>

    <div id="explanation">
        <p>点击按钮，开始探索 "propose" 的含义吧！</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordAnimation');
    const ctx = canvas.getContext('2d');
    const explanationDiv = document.getElementById('explanation');

    const playBtn = document.getElementById('playBtn');
    const proBtn = document.getElementById('proBtn');
    const poseBtn = document.getElementById('poseBtn');
    const proposeBtn = document.getElementById('proposeBtn');
    const resetBtn = document.getElementById('resetBtn');

    let animationFrameId;

    // 颜色和字体配置
    const colors = {
        background: '#ffffff',
        text: '#333333',
        primary: '#007bff',
        secondary: '#6c757d',
        accent: '#28a745',
        river: '#a1d2e6'
    };
    const fonts = {
        title: 'bold 36px Arial',
        text: '24px Arial',
        chinese: '20px "Microsoft YaHei", sans-serif'
    };

    // 初始状态
    function drawInitialState() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = colors.background;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制河流
        ctx.fillStyle = colors.river;
        ctx.beginPath();
        ctx.moveTo(0, 250);
        ctx.bezierCurveTo(200, 200, 600, 300, 800, 250);
        ctx.lineTo(800, 450);
        ctx.lineTo(0, 450);
        ctx.closePath();
        ctx.fill();

        // 绘制两岸
        ctx.fillStyle = '#9B7653';
        ctx.fillRect(0, 250, 150, 200);
        ctx.fillRect(650, 250, 150, 200);
        
        ctx.fillStyle = colors.text;
        ctx.font = fonts.title;
        ctx.textAlign = 'center';
        ctx.fillText('propose', canvas.width / 2, 50);
        
        explanationDiv.innerHTML = '<p>点击按钮，开始探索 "propose" 的含义吧！</p>';
    }
    
    // 清除动画
    function resetAnimation() {
        cancelAnimationFrame(animationFrameId);
        drawInitialState();
    }

    // 第一幕: pro-
    function animatePro() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>pro- (向前)</strong>: 我们的建筑师站在河的一边，<strong>向前</strong>望着对岸，展望着连接两地的未来。</p>';
        
        let progress = 0;
        const startX = 100;
        const startY = 220;
        
        function drawMan(x, y) {
            // 简笔画小人
            ctx.strokeStyle = colors.secondary;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(x, y - 20, 10, 0, Math.PI * 2); // head
            ctx.moveTo(x, y - 10);
            ctx.lineTo(x, y + 10); // body
            ctx.lineTo(x - 10, y + 20); // left leg
            ctx.moveTo(x, y + 10);
            ctx.lineTo(x + 10, y + 20); // right leg
            ctx.moveTo(x, y);
            ctx.lineTo(x + 10, y - 5); // right arm
            ctx.stroke();
        }

        function drawArrow(progress) {
            ctx.strokeStyle = colors.primary;
            ctx.fillStyle = colors.primary;
            ctx.lineWidth = 5;
            const endX = startX + 10 + progress * 500;
            const y = startY - 20;

            // line
            ctx.beginPath();
            ctx.moveTo(startX + 15, y);
            ctx.lineTo(endX, y);
            ctx.stroke();
            // head
            ctx.beginPath();
            ctx.moveTo(endX, y - 10);
            ctx.lineTo(endX + 20, y);
            ctx.lineTo(endX, y + 10);
            ctx.closePath();
            ctx.fill();
        }

        function animate() {
            if (progress >= 1) {
                 ctx.fillStyle = colors.primary;
                 ctx.font = 'bold 30px Arial';
                 ctx.fillText('pro- (向前)', 400, 100);
                 return;
            }
            progress += 0.01;
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawInitialState();
            drawMan(startX, startY);
            drawArrow(progress);
            
            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }
    
    // 第二幕: pose
    function animatePose() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>pose (放置)</strong>: 他拿出了精心设计的蓝图，并将其<strong>放置</strong>在工作台上，准备开始伟大的工程。</p>';
        
        let progress = 0;
        const tableX = 300, tableY = 300, tableW = 200, tableH = 100;

        function drawTable() {
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(tableX, tableY, tableW, 10); // table top
            ctx.fillRect(tableX + 10, tableY + 10, 10, tableH); // leg 1
            ctx.fillRect(tableX + tableW - 20, tableY + 10, 10, tableH); // leg 2
        }

        function drawBlueprint(p) {
            const blueprintY = tableY - 50 + (1 - p) * 150;
            ctx.fillStyle = '#e0f7fa';
            ctx.strokeStyle = '#0277bd';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.rect(tableX + 20, blueprintY, 160, 40);
            ctx.fill();
            ctx.stroke();
            // lines on blueprint
            ctx.strokeStyle = '#0288d1';
            ctx.beginPath();
            ctx.moveTo(tableX + 30, blueprintY + 10);
            ctx.lineTo(tableX + 170, blueprintY + 10);
            ctx.moveTo(tableX + 50, blueprintY + 20);
            ctx.lineTo(tableX + 150, blueprintY + 20);
            ctx.moveTo(tableX + 70, blueprintY + 30);
            ctx.lineTo(tableX + 130, blueprintY + 30);
            ctx.stroke();
        }

        function animate() {
            if (progress >= 1) {
                ctx.fillStyle = colors.accent;
                ctx.font = 'bold 30px Arial';
                ctx.fillText('pose (放置)', 400, 100);
                return;
            }
            progress += 0.015;

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawInitialState();
            drawTable();
            drawBlueprint(Math.min(1, progress * 2));

            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }
    
    // 第三幕: propose
    function animatePropose() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>propose (提议)</strong>: 建筑师将他的计划<strong>向前放置</strong>于众人面前，这便是"<strong>提议</strong>"。他提议建造一座连接未来的桥梁。</p>';

        let progress = 0;
        const startX = 100, startY = 220;
        const blueprintX = 200, blueprintY = 180;

        function drawMan(x, y) { /* ... same as animatePro ... */ }
        function drawCrowd() { /* ... draw some people ... */ }
        function drawBridge(p) {
            const bridgeY = 250;
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(150, bridgeY);
            ctx.lineTo(150 + p * 500, bridgeY);
            ctx.stroke();

            // support
            const numSupports = 5;
            for(let i=1; i < numSupports * p; i++) {
                let x = 150 + i * (500 / numSupports);
                ctx.beginPath();
                ctx.moveTo(x, bridgeY);
                ctx.lineTo(x, bridgeY + 30);
                ctx.stroke();
            }
        }


        function animate() {
             if (progress >= 1) {
                ctx.fillStyle = colors.text;
                ctx.font = 'bold 35px Arial';
                ctx.fillText('propose = pro + pose', 400, 60);
                ctx.font = 'bold 30px "Microsoft YaHei"';
                ctx.fillText('向前 + 放置 = 提议', 400, 110);
                return;
            }
            progress += 0.008;

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawInitialState();
            drawBridge(progress);

            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }
    
    async function playFullAnimation() {
        resetAnimation();
        animatePro();
        await new Promise(r => setTimeout(r, 4000));
        if (animationFrameId === 0) return; // check if reset was called
        animatePose();
        await new Promise(r => setTimeout(r, 4000));
        if (animationFrameId === 0) return;
        animatePropose();
    }

    // Event Listeners
    playBtn.addEventListener('click', playFullAnimation);
    proBtn.addEventListener('click', animatePro);
    poseBtn.addEventListener('click', animatePose);
    proposeBtn.addEventListener('click', animatePropose);
    resetBtn.addEventListener('click', () => {
        animationFrameId = 0; // stop chained animation
        resetAnimation();
    });

    // Initial draw
    window.addEventListener('load', drawInitialState);

</script>

</body>
</html> 