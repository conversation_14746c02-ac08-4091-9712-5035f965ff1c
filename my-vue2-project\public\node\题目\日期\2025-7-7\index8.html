<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 互动学习：数据结构与算法探险</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            animation: fadeInDown 1s ease-out;
        }
        
        .header h1 {
            color: white;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 10px;
        }
        
        .header p {
            color: #f0f0f0;
            font-size: 1.2em;
        }
        
        .learning-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
            animation: slideInUp 0.8s ease-out;
        }
        
        .topic-selector {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .topic-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .topic-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .topic-btn.active {
            background: linear-gradient(45deg, #10ac84, #00d2d3);
            transform: scale(1.05);
        }
        
        .topic-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }
        
        .topic-btn:hover::before {
            left: 100%;
        }
        
        .content-area {
            min-height: 400px;
            position: relative;
        }
        
        .topic-content {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }
        
        .topic-content.active {
            display: block;
        }
        
        .interactive-canvas {
            width: 100%;
            height: 400px;
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .interactive-canvas:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .control-btn {
            background: linear-gradient(45deg, #4834d4, #686de0);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .control-btn:active {
            transform: translateY(0);
        }
        
        .explanation {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #e17055;
            animation: slideInLeft 0.6s ease-out;
        }
        
        .explanation h3 {
            color: #2d3436;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .quiz-section {
            background: linear-gradient(135deg, #a8e6cf, #88d8a3);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            animation: bounceIn 0.8s ease-out;
        }
        
        .quiz-question {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2d3436;
        }
        
        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .quiz-option {
            background: white;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-align: center;
        }
        
        .quiz-option:hover {
            background: #f8f9fa;
            border-color: #007bff;
            transform: scale(1.02);
        }
        
        .quiz-option.selected {
            background: #007bff;
            color: white;
            border-color: #0056b3;
        }
        
        .quiz-option.correct {
            background: #28a745;
            color: white;
            animation: pulse 0.6s ease-in-out;
        }
        
        .quiz-option.incorrect {
            background: #dc3545;
            color: white;
            animation: shake 0.6s ease-in-out;
        }
        
        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e9ecef;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            width: 0%;
            transition: width 1s ease-out;
            border-radius: 5px;
        }
        
        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            50% { transform: translateY(-100px) rotate(180deg); }
        }
        
        .score-display {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            font-weight: bold;
            color: #333;
            z-index: 1000;
        }
        
        .achievement {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
            z-index: 1001;
            display: none;
            animation: bounceIn 0.8s ease-out;
        }
        
        .achievement h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2em;
        }
        
        .achievement p {
            color: #666;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="score-display" id="scoreDisplay">
        🏆 得分: <span id="score">0</span>
    </div>
    
    <div class="achievement" id="achievement">
        <h2>🎉 恭喜你！</h2>
        <p id="achievementText">你获得了新成就！</p>
    </div>
    
    <div class="container">
        <div class="header">
            <h1>🌟 数据结构与算法探险之旅</h1>
            <p>通过互动动画学习编程的核心概念</p>
        </div>
        
        <div class="learning-section">
            <div class="topic-selector">
                <button class="topic-btn active" data-topic="array">📊 数组探索</button>
                <button class="topic-btn" data-topic="stack">📚 栈的奥秘</button>
                <button class="topic-btn" data-topic="queue">🚶 队列世界</button>
                <button class="topic-btn" data-topic="tree">🌳 树的王国</button>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="content-area">
                <!-- 数组内容 -->
                <div class="topic-content active" id="array-content">
                    <div class="explanation">
                        <h3>🎯 什么是数组？</h3>
                        <p>数组就像一排有编号的盒子，每个盒子可以存放一个物品。我们可以通过编号（索引）快速找到任何一个盒子里的东西！</p>
                    </div>
                    
                    <canvas class="interactive-canvas" id="arrayCanvas"></canvas>
                    
                    <div class="controls">
                        <button class="control-btn" onclick="addElement()">➕ 添加元素</button>
                        <button class="control-btn" onclick="removeElement()">➖ 删除元素</button>
                        <button class="control-btn" onclick="searchElement()">🔍 查找元素</button>
                        <button class="control-btn" onclick="sortArray()">🔄 排序数组</button>
                    </div>
                    
                    <div class="quiz-section">
                        <div class="quiz-question">🤔 数组的第一个元素的索引是多少？</div>
                        <div class="quiz-options">
                            <div class="quiz-option" onclick="selectAnswer(this, false)">1</div>
                            <div class="quiz-option" onclick="selectAnswer(this, true)">0</div>
                            <div class="quiz-option" onclick="selectAnswer(this, false)">-1</div>
                            <div class="quiz-option" onclick="selectAnswer(this, false)">任意数字</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let score = 0;
        let currentTopic = 'array';
        let arrayData = [5, 3, 8, 1, 9, 2];
        let animationId;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            initializeCanvas();
            updateScore(0);
        });

        // 创建浮动粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.width = particle.style.height = Math.random() * 10 + 5 + 'px';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 主题切换
        document.querySelectorAll('.topic-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const topic = this.dataset.topic;
                switchTopic(topic);
            });
        });

        function switchTopic(topic) {
            // 更新按钮状态
            document.querySelectorAll('.topic-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-topic="${topic}"]`).classList.add('active');

            // 更新内容
            document.querySelectorAll('.topic-content').forEach(content => {
                content.classList.remove('active');
            });

            currentTopic = topic;

            if (topic === 'array') {
                document.getElementById('array-content').classList.add('active');
                initializeCanvas();
            }
            // 其他主题的内容将在后续添加

            updateProgress();
        }

        // 画布初始化和动画
        function initializeCanvas() {
            const canvas = document.getElementById('arrayCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            drawArray(ctx, canvas);
        }

        function drawArray(ctx, canvas) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const boxWidth = 60;
            const boxHeight = 60;
            const spacing = 10;
            const startX = (canvas.width - (arrayData.length * (boxWidth + spacing) - spacing)) / 2;
            const startY = canvas.height / 2 - boxHeight / 2;

            arrayData.forEach((value, index) => {
                const x = startX + index * (boxWidth + spacing);
                const y = startY;

                // 绘制盒子
                ctx.fillStyle = `hsl(${index * 60}, 70%, 60%)`;
                ctx.fillRect(x, y, boxWidth, boxHeight);

                // 绘制边框
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, boxWidth, boxHeight);

                // 绘制数值
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(value, x + boxWidth/2, y + boxHeight/2);

                // 绘制索引
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText(`[${index}]`, x + boxWidth/2, y - 15);
            });
        }

        // 数组操作函数
        function addElement() {
            const newValue = Math.floor(Math.random() * 10) + 1;
            arrayData.push(newValue);
            animateArrayChange();
            updateScore(10);
        }

        function removeElement() {
            if (arrayData.length > 0) {
                arrayData.pop();
                animateArrayChange();
                updateScore(5);
            }
        }

        function searchElement() {
            if (arrayData.length === 0) return;

            const targetIndex = Math.floor(Math.random() * arrayData.length);
            animateSearch(targetIndex);
            updateScore(15);
        }

        function sortArray() {
            arrayData.sort((a, b) => a - b);
            animateArrayChange();
            updateScore(20);
            showAchievement("排序大师", "你成功排序了数组！");
        }

        // 动画函数
        function animateArrayChange() {
            const canvas = document.getElementById('arrayCanvas');
            const ctx = canvas.getContext('2d');

            let scale = 1;
            let growing = false;

            function animate() {
                if (!growing) {
                    scale -= 0.05;
                    if (scale <= 0.8) growing = true;
                } else {
                    scale += 0.05;
                    if (scale >= 1) {
                        scale = 1;
                        drawArray(ctx, canvas);
                        return;
                    }
                }

                ctx.save();
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.translate(canvas.width/2, canvas.height/2);
                ctx.scale(scale, scale);
                ctx.translate(-canvas.width/2, -canvas.height/2);
                drawArray(ctx, canvas);
                ctx.restore();

                requestAnimationFrame(animate);
            }

            animate();
        }

        function animateSearch(targetIndex) {
            const canvas = document.getElementById('arrayCanvas');
            const ctx = canvas.getContext('2d');
            let currentIndex = 0;

            function searchStep() {
                drawArray(ctx, canvas);

                // 高亮当前搜索位置
                const boxWidth = 60;
                const spacing = 10;
                const startX = (canvas.width - (arrayData.length * (boxWidth + spacing) - spacing)) / 2;
                const startY = canvas.height / 2 - boxWidth / 2;
                const x = startX + currentIndex * (boxWidth + spacing);

                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 4;
                ctx.strokeRect(x - 2, startY - 2, boxWidth + 4, boxWidth + 4);

                if (currentIndex === targetIndex) {
                    // 找到目标，闪烁效果
                    ctx.fillStyle = 'rgba(255, 107, 107, 0.3)';
                    ctx.fillRect(x, startY, boxWidth, boxWidth);
                    return;
                }

                currentIndex++;
                if (currentIndex < arrayData.length) {
                    setTimeout(searchStep, 500);
                }
            }

            searchStep();
        }

        // 答题系统
        function selectAnswer(element, isCorrect) {
            const options = element.parentElement.querySelectorAll('.quiz-option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    opt.classList.add(isCorrect ? 'correct' : 'incorrect');
                } else if (opt.onclick.toString().includes('true')) {
                    opt.classList.add('correct');
                }
            });

            if (isCorrect) {
                updateScore(25);
                showAchievement("知识达人", "回答正确！");
            }

            setTimeout(() => {
                options.forEach(opt => {
                    opt.style.pointerEvents = 'auto';
                    opt.classList.remove('correct', 'incorrect', 'selected');
                });
            }, 2000);
        }

        // 分数和成就系统
        function updateScore(points) {
            score += points;
            document.getElementById('score').textContent = score;

            // 更新进度条
            updateProgress();
        }

        function updateProgress() {
            const maxScore = 1000;
            const percentage = Math.min((score / maxScore) * 100, 100);
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        function showAchievement(title, text) {
            const achievement = document.getElementById('achievement');
            document.getElementById('achievementText').textContent = text;
            achievement.style.display = 'block';

            setTimeout(() => {
                achievement.style.display = 'none';
            }, 3000);
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            if (currentTopic === 'array') {
                initializeCanvas();
            }
        });

        // 添加键盘交互
        document.addEventListener('keydown', function(e) {
            if (e.key === 'a' || e.key === 'A') {
                addElement();
            } else if (e.key === 'd' || e.key === 'D') {
                removeElement();
            } else if (e.key === 's' || e.key === 'S') {
                searchElement();
            } else if (e.key === 'r' || e.key === 'R') {
                sortArray();
            }
        });

        // 添加鼠标点击画布交互
        document.getElementById('arrayCanvas').addEventListener('click', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检查点击的是哪个数组元素
            const boxWidth = 60;
            const spacing = 10;
            const startX = (this.width - (arrayData.length * (boxWidth + spacing) - spacing)) / 2;
            const startY = this.height / 2 - boxWidth / 2;

            for (let i = 0; i < arrayData.length; i++) {
                const elementX = startX + i * (boxWidth + spacing);
                if (x >= elementX && x <= elementX + boxWidth &&
                    y >= startY && y <= startY + boxWidth) {
                    // 点击了第i个元素，显示详细信息
                    showElementInfo(i, arrayData[i]);
                    break;
                }
            }
        });

        function showElementInfo(index, value) {
            showAchievement("元素信息", `索引: ${index}, 值: ${value}`);
            updateScore(5);
        }

        // 添加更多互动提示
        function showHints() {
            const hints = [
                "💡 提示：按 A 键添加元素",
                "💡 提示：按 D 键删除元素",
                "💡 提示：按 S 键搜索元素",
                "💡 提示：按 R 键排序数组",
                "💡 提示：点击数组元素查看详情"
            ];

            let hintIndex = 0;
            setInterval(() => {
                if (score < 100) { // 只在新手阶段显示提示
                    showAchievement("学习提示", hints[hintIndex]);
                    hintIndex = (hintIndex + 1) % hints.length;
                }
            }, 10000); // 每10秒显示一个提示
        }

        // 启动提示系统
        setTimeout(showHints, 3000);
    </script>
</body>
</html>
