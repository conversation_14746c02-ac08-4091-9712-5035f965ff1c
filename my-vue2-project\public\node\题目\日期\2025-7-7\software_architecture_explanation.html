<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构视角趣味学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            color: #343a40;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            width: 90%;
            max-width: 1000px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            margin: 30px 0;
            padding: 30px;
        }
        h1, h2, h3 {
            color: #007bff;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-section, .explanation-section, .interactive-section {
            margin-bottom: 40px;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        .question-section p, .explanation-content p {
            line-height: 1.8;
            font-size: 1.1em;
            margin-bottom: 15px;
        }
        .options div {
            background-color: #f0f8ff;
            border: 1px solid #b0e0e6;
            border-radius: 8px;
            padding: 12px 20px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .options div:hover {
            background-color: #e0f2f7;
            border-color: #87ceeb;
            transform: translateY(-2px);
        }
        .options div.correct {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .explanation-nav {
            text-align: center;
            margin-top: 30px;
            margin-bottom: 20px;
        }
        .explanation-nav button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 10px 20px;
            margin: 0 10px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        .explanation-nav button:hover {
            background-color: #0056b3;
        }
        .explanation-content {
            display: none; /* Hide all content sections by default */
        }
        .explanation-content.active {
            display: block; /* Show active content section */
        }
        canvas {
            border: 2px solid #007bff;
            display: block;
            margin: 20px auto;
            background-color: #ffffff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .instruction {
            text-align: center;
            font-size: 1.1em;
            color: #6c757d;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件架构视角趣味学习</h1>

        <div class="question-section">
            <h2>题目回顾</h2>
            <p><strong>问题：</strong> 考虑软件架构时，重要的是从不同的视角 (perspective) 来检查，这促使软件设计师考虑架构的不同属性。例如，展示功能组织的 () 能判断质量特性，展示并发行行为的 () 能判断系统行为特性。选择的特定视角或视图也就是逻辑视图、进程视图、实现视图和 () 。使用 () 来记录设计元素的功能和概念接口，设计元素的功能定义了它本身在系统中的角色，这些角色包括功能、性能等。</p>
            <div class="options">
                <div id="optionA">A. 静态视角</div>
                <div id="optionB">B. 动态视角</div>
                <div id="optionC">C. 多维视角</div>
                <div id="optionD">D. 功能视角</div>
            </div>
            <p><strong>正确答案：</strong> A</p>
        </div>

        <div class="explanation-section">
            <h2>知识点解析与趣味演示</h2>
            <div class="explanation-nav">
                <button id="prevBtn">上一页</button>
                <button id="nextBtn">下一页</button>
            </div>

            <div id="intro" class="explanation-content active">
                <h3>1. 什么是软件架构视角？</h3>
                <p>想象一下，软件就像一座复杂的房子。如果你只从一个角度看，比如只看外观，你可能不知道里面有多少房间，水管怎么走，或者电线藏在哪里。</p>
                <p>软件架构也是一样！我们需要从不同的“视角”来看待它，才能全面理解它的结构、功能和行为。就像我们盖房子需要建筑师、水管工、电工等不同专业的人从不同角度来设计和检查一样。</p>
                <p>每个视角都帮助我们关注软件的某个特定方面，确保它既能满足功能需求，又能跑得快、容易维护、安全等等。</p>
                <p class="instruction">点击“下一页”开始探索第一个视角！</p>
            </div>

            <div id="static-dynamic" class="explanation-content">
                <h3>2. 静态视角 vs 动态视角</h3>
                <p>题目中提到了“静态视角”和“动态视角”，这是理解软件架构很重要的两种基本视角。</p>
                <ul>
                    <li><strong>静态视角：</strong> 就像一张房子的蓝图，它展示的是软件“静止时”的样子，比如有哪些模块、类、组件，它们之间有什么关系。它帮助我们判断软件的“质量特性”，比如结构是否清晰，模块之间耦合度是否合理，这影响到软件的可维护性和可扩展性。</li>
                    <li><strong>动态视角：</strong> 就像一部电影，它展示的是软件“运行起来”的样子，比如数据如何在模块之间流动，不同组件如何协作完成一个任务。它帮助我们判断软件的“系统行为特性”，比如某个操作的执行流程，性能瓶颈可能在哪里。</li>
                </ul>
                <p class="instruction">请观察下面的动画，思考静态与动态的区别。</p>
                <canvas id="staticDynamicCanvas" width="800" height="400"></canvas>
            </div>

            <div id="logical-view" class="explanation-content">
                <h3>3. 逻辑视图 (Logical View)</h3>
                <p>逻辑视图关注的是软件的功能分解，也就是软件提供了哪些服务，以及这些服务是如何通过抽象的组件（比如类、模块、包）来实现的。</p>
                <p>它就像是房子的“功能区划图”：客厅、卧室、厨房、卫生间等等，每个区域有什么功能，它们之间有什么逻辑上的连接。</p>
                <p>这个视图帮助我们理解软件的功能结构和关键抽象，通常会使用类图或组件图来表示。题目中提到的“使用 () 来记录设计元素的功能和概念接口”指的就是逻辑视图。</p>
                <p class="instruction">点击方块，看看不同的功能模块如何协作！</p>
                <canvas id="logicalCanvas" width="800" height="400"></canvas>
            </div>

            <div id="process-view" class="explanation-content">
                <h3>4. 进程视图 (Process View)</h3>
                <p>进程视图关注软件运行时，并发和分布的方面。它描述了软件的进程或线程是如何协同工作的，以及它们之间的通信和同步机制。</p>
                <p>这就像是房子的“人员活动路线图”：谁在厨房做饭，谁在客厅看电视，他们如何共享资源（比如电、水），以及如何避免互相干扰。</p>
                <p>这个视图对于理解系统的并发性、性能、伸缩性以及处理死锁和竞争条件非常重要。</p>
                <p class="instruction">点击开始按钮，观察数据流如何在不同进程中流动！</p>
                <canvas id="processCanvas" width="800" height="400"></canvas>
            </div>

            <div id="implementation-view" class="explanation-content">
                <h3>5. 实现视图 (Implementation View)</h3>
                <p>实现视图关注的是软件的实际代码组织结构，也就是开发人员在编写代码时看到的模块、源文件、库、组件和它们的依赖关系。</p>
                <p>这就像是房子的“施工图纸”和“材料清单”：墙体用什么砖，屋顶用什么瓦，这些材料在哪里，如何组装起来。</p>
                <p>这个视图对于软件的开发、集成、测试和版本控制非常重要，它直接反映了项目的物理结构和开发团队的工作方式。</p>
                <p class="instruction">点击文件图标，模拟代码文件的组织结构和依赖关系！</p>
                <canvas id="implementationCanvas" width="800" height="400"></canvas>
            </div>

            <div id="deployment-view" class="explanation-content">
                <h3>6. 配置视图 / 部署视图 (Deployment View)</h3>
                <p>配置视图（也常被称为部署视图）关注的是软件系统在物理硬件上的部署方式，包括进程和任务如何映射到具体的处理器、服务器等物理节点，以及网络连接。</p>
                <p>这就像是房子的“家具和设备布局图”：冰箱放厨房，电视放客厅，床放卧室，以及这些电器如何连接到电源插座。</p>
                <p>这个视图对于系统的安装、部署、系统管理、性能调优和故障排除非常关键。</p>
                <p class="instruction">点击服务器，模拟组件在不同服务器上的部署和通信！</p>
                <canvas id="deploymentCanvas" width="800" height="400"></canvas>
            </div>

            <div id="summary" class="explanation-content">
                <h3>总结与回顾</h3>
                <p>现在，我们已经学习了软件架构的几个重要视角：</p>
                <ul>
                    <li>**静态视角：** 关注软件的结构，帮助判断质量特性（例如可维护性）。</li>
                    <li>**动态视角：** 关注软件的运行时行为，帮助判断系统行为特性（例如性能）。</li>
                    <li>**逻辑视图：** 描述功能分解和抽象组件（类、模块）。</li>
                    <li>**进程视图：** 描述并发和运行时交互。</li>
                    <li>**实现视图：** 描述代码的物理组织和依赖。</li>
                    <li>**配置视图/部署视图：** 描述系统在硬件上的部署。</li>
                </ul>
                <p>理解这些视角，就能像一位全能的建筑师一样，从宏观到微观，从静止到运行，全面地设计和检查软件这座“大房子”！</p>
                <p class="instruction">希望您通过这次学习，对软件架构有了更深入的理解！</p>
            </div>
        </div>
    </div>

    <script>
        const explanationContents = document.querySelectorAll('.explanation-content');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        let currentContentIndex = 0;

        function showContent(index) {
            explanationContents.forEach((content, i) => {
                content.classList.remove('active');
                if (i === index) {
                    content.classList.add('active');
                }
            });
            prevBtn.disabled = index === 0;
            nextBtn.disabled = index === explanationContents.length - 1;
            // Trigger specific canvas animations when their section becomes active
            if (explanationContents[index].id === 'static-dynamic') {
                drawStaticDynamicAnimation();
            } else if (explanationContents[index].id === 'logical-view') {
                drawLogicalView();
            } else if (explanationContents[index].id === 'process-view') {
                drawProcessView();
            } else if (explanationContents[index].id === 'implementation-view') {
                drawImplementationView();
            } else if (explanationContents[index].id === 'deployment-view') {
                drawDeploymentView();
            }
        }

        prevBtn.addEventListener('click', () => {
            if (currentContentIndex > 0) {
                currentContentIndex--;
                showContent(currentContentIndex);
            }
        });

        nextBtn.addEventListener('click', () => {
            if (currentContentIndex < explanationContents.length - 1) {
                currentContentIndex++;
                showContent(currentContentIndex);
            }
        });

        // Highlight correct option
        document.getElementById('optionA').classList.add('correct');

        // Canvas Animations - Placeholders for now, will be filled in next steps
        function drawStaticDynamicAnimation() {
            const canvas = document.getElementById('staticDynamicCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear canvas

            // Static view: simple blocks representing modules
            ctx.fillStyle = '#add8e6';
            ctx.fillRect(50, 100, 100, 100);
            ctx.fillRect(200, 100, 100, 100);
            ctx.fillRect(125, 250, 100, 100);
            ctx.strokeStyle = '#4682b4';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 100, 100, 100);
            ctx.strokeRect(200, 100, 100, 100);
            ctx.strokeRect(125, 250, 100, 100);
            ctx.fillStyle = '#333';
            ctx.fillText('模块A', 75, 150);
            ctx.fillText('模块B', 225, 150);
            ctx.fillText('模块C', 150, 300);
            ctx.beginPath();
            ctx.moveTo(100, 200);
            ctx.lineTo(150, 250);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(250, 200);
            ctx.lineTo(200, 250);
            ctx.stroke();
            ctx.fillText('静态结构 (模块和关系)', 100, 50);

            // Dynamic view: animated flow
            let flowPos = 0;
            function animateDynamic() {
                ctx.clearRect(400, 0, canvas.width - 400, canvas.height); // Clear dynamic side

                ctx.fillStyle = '#add8e6';
                ctx.fillRect(450, 100, 100, 100);
                ctx.fillRect(600, 100, 100, 100);
                ctx.fillRect(525, 250, 100, 100);
                ctx.strokeStyle = '#4682b4';
                ctx.lineWidth = 2;
                ctx.strokeRect(450, 100, 100, 100);
                ctx.strokeRect(600, 100, 100, 100);
                ctx.strokeRect(525, 250, 100, 100);
                ctx.fillStyle = '#333';
                ctx.fillText('用户请求', 475, 150);
                ctx.fillText('处理模块', 625, 150);
                ctx.fillText('数据库', 550, 300);
                ctx.fillText('动态行为 (数据流)', 500, 50);

                // Animated arrow
                ctx.fillStyle = '#ff6347';
                ctx.beginPath();
                ctx.arc(475 + flowPos, 200, 8, 0, Math.PI * 2);
                ctx.fill();
                ctx.beginPath();
                ctx.arc(625 - flowPos, 200, 8, 0, Math.PI * 2);
                ctx.fill();
                ctx.beginPath();
                ctx.arc(550, 250 + flowPos, 8, 0, Math.PI * 2);
                ctx.fill();

                flowPos = (flowPos + 1) % 100; // Move dot

                requestAnimationFrame(animateDynamic);
            }
            animateDynamic();
        }

        function drawLogicalView() {
            const canvas = document.getElementById('logicalCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const boxes = [
                { x: 100, y: 100, width: 150, height: 80, text: '用户管理模块', color: '#ff9999' },
                { x: 300, y: 100, width: 150, height: 80, text: '订单处理模块', color: '#99ff99' },
                { x: 500, y: 100, width: 150, height: 80, text: '支付模块', color: '#9999ff' },
                { x: 200, y: 250, width: 200, height: 80, text: '数据存储服务', color: '#ffff99' }
            ];

            boxes.forEach(box => {
                ctx.fillStyle = box.color;
                ctx.fillRect(box.x, box.y, box.width, box.height);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(box.x, box.y, box.width, box.height);
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(box.text, box.x + box.width / 2, box.y + box.height / 2);
            });

            // Relations
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(boxes[0].x + boxes[0].width / 2, boxes[0].y + boxes[0].height);
            ctx.lineTo(boxes[3].x + boxes[3].width / 4, boxes[3].y);
            ctx.stroke();
            ctx.fillText('使用', (boxes[0].x + boxes[0].width / 2 + boxes[3].x + boxes[3].width / 4) / 2, (boxes[0].y + boxes[0].height + boxes[3].y) / 2 - 10);

            ctx.beginPath();
            ctx.moveTo(boxes[1].x + boxes[1].width / 2, boxes[1].y + boxes[1].height);
            ctx.lineTo(boxes[3].x + boxes[3].width / 2, boxes[3].y);
            ctx.stroke();
            ctx.fillText('使用', (boxes[1].x + boxes[1].width / 2 + boxes[3].x + boxes[3].width / 2) / 2, (boxes[1].y + boxes[1].height + boxes[3].y) / 2 - 10);

            ctx.beginPath();
            ctx.moveTo(boxes[2].x + boxes[2].width / 2, boxes[2].y + boxes[2].height);
            ctx.lineTo(boxes[3].x + boxes[3].width * 3 / 4, boxes[3].y);
            ctx.stroke();
            ctx.fillText('使用', (boxes[2].x + boxes[2].width / 2 + boxes[3].x + boxes[3].width * 3 / 4) / 2, (boxes[2].y + boxes[2].height + boxes[3].y) / 2 - 10);

            // Interaction
            canvas.onclick = function(event) {
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                boxes.forEach(box => {
                    if (x > box.x && x < box.x + box.width && y > box.y && y < box.y + box.height) {
                        alert(`点击了: ${box.text} \n这是一个逻辑模块，提供特定功能。`);
                    }
                });
            };
        }

        function drawProcessView() {
            const canvas = document.getElementById('processCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const processes = [
                { id: 'P1', x: 100, y: 100, text: '用户接口进程' },
                { id: 'P2', x: 350, y: 100, text: '业务逻辑进程' },
                { id: 'P3', x: 600, y: 100, text: '数据库访问进程' }
            ];

            processes.forEach(p => {
                ctx.fillStyle = '#c3e6cb';
                ctx.fillRect(p.x, p.y, 150, 80);
                ctx.strokeStyle = '#28a745';
                ctx.lineWidth = 2;
                ctx.strokeRect(p.x, p.y, 150, 80);
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(p.text, p.x + 75, p.y + 40);
            });

            // Add buttons for animation
            const startBtn = document.createElement('button');
            startBtn.innerText = '开始数据流动画';
            startBtn.style.position = 'absolute';
            startBtn.style.top = canvas.offsetTop + canvas.height + 10 + 'px';
            startBtn.style.left = canvas.offsetLeft + canvas.width / 2 - 75 + 'px';
            startBtn.style.backgroundColor = '#28a745';
            startBtn.style.color = 'white';
            startBtn.style.border = 'none';
            startBtn.style.borderRadius = '5px';
            startBtn.style.padding = '10px 20px';
            startBtn.style.cursor = 'pointer';
            startBtn.id = 'processStartBtn';
            canvas.parentNode.insertBefore(startBtn, canvas.nextSibling);

            let animationFrameId;
            let dataPos = 0;
            let step = 0;

            function animateProcess() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                processes.forEach(p => {
                    ctx.fillStyle = '#c3e6cb';
                    ctx.fillRect(p.x, p.y, 150, 80);
                    ctx.strokeStyle = '#28a745';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(p.x, p.y, 150, 80);
                    ctx.fillStyle = '#333';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(p.text, p.x + 75, p.y + 40);
                });

                ctx.fillStyle = '#007bff'; // Data flow color

                if (step === 0) { // P1 -> P2
                    ctx.beginPath();
                    ctx.arc(processes[0].x + 150 + dataPos, processes[0].y + 40, 10, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.beginPath();
                    ctx.moveTo(processes[0].x + 150, processes[0].y + 40);
                    ctx.lineTo(processes[1].x, processes[1].y + 40);
                    ctx.strokeStyle = '#007bff';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    if (dataPos >= processes[1].x - (processes[0].x + 150)) {
                        step = 1;
                        dataPos = 0;
                    } else {
                        dataPos += 2;
                    }
                } else if (step === 1) { // P2 -> P3
                    ctx.beginPath();
                    ctx.arc(processes[1].x + 150 + dataPos, processes[1].y + 40, 10, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.beginPath();
                    ctx.moveTo(processes[1].x + 150, processes[1].y + 40);
                    ctx.lineTo(processes[2].x, processes[2].y + 40);
                    ctx.strokeStyle = '#007bff';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    if (dataPos >= processes[2].x - (processes[1].x + 150)) {
                        step = 0; // Loop animation
                        dataPos = 0;
                    } else {
                        dataPos += 2;
                    }
                }
                animationFrameId = requestAnimationFrame(animateProcess);
            }

            startBtn.onclick = function() {
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                    animationFrameId = null;
                    startBtn.innerText = '开始数据流动画';
                    ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear to redraw static elements
                    processes.forEach(p => {
                        ctx.fillStyle = '#c3e6cb';
                        ctx.fillRect(p.x, p.y, 150, 80);
                        ctx.strokeStyle = '#28a745';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(p.x, p.y, 150, 80);
                        ctx.fillStyle = '#333';
                        ctx.font = '16px Arial';
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        ctx.fillText(p.text, p.x + 75, p.y + 40);
                    });
                } else {
                    startBtn.innerText = '停止动画';
                    animateProcess();
                }
            };
            // Clean up button if moving away from this section
            canvas.parentNode.onanimationend = function() {
                const btn = document.getElementById('processStartBtn');
                if (btn && !document.getElementById('process-view').classList.contains('active')) {
                    btn.remove();
                }
            };
        }

        function drawImplementationView() {
            const canvas = document.getElementById('implementationCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const files = [
                { id: 'file1', x: 100, y: 100, text: 'user.js', type: 'js' },
                { id: 'file2', x: 250, y: 100, text: 'order.js', type: 'js' },
                { id: 'file3', x: 400, y: 100, text: 'payment.js', type: 'js' },
                { id: 'file4', x: 250, y: 250, text: 'database.js', type: 'js' }
            ];

            function drawFile(file) {
                ctx.fillStyle = '#f0f0f0';
                ctx.fillRect(file.x, file.y, 120, 60);
                ctx.strokeStyle = '#6c757d';
                ctx.lineWidth = 2;
                ctx.strokeRect(file.x, file.y, 120, 60);
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(file.text, file.x + 60, file.y + 30);
                // Simple icon for file type
                ctx.font = '20px Arial';
                if (file.type === 'js') ctx.fillText('JS', file.x + 10, file.y + 20);
            }

            files.forEach(drawFile);

            // Dependencies
            ctx.strokeStyle = '#87ceeb';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(files[0].x + 60, files[0].y + 60);
            ctx.lineTo(files[3].x + 30, files[3].y);
            ctx.stroke();
            ctx.fillText('依赖', files[0].x + 60 + 20, files[0].y + 60 + 20);

            ctx.beginPath();
            ctx.moveTo(files[1].x + 60, files[1].y + 60);
            ctx.lineTo(files[3].x + 60, files[3].y);
            ctx.stroke();
            ctx.fillText('依赖', files[1].x + 60 + 20, files[1].y + 60 + 20);

            ctx.beginPath();
            ctx.moveTo(files[2].x + 60, files[2].y + 60);
            ctx.lineTo(files[3].x + 90, files[3].y);
            ctx.stroke();
            ctx.fillText('依赖', files[2].x + 60 + 20, files[2].y + 60 + 20);

            canvas.onclick = function(event) {
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                files.forEach(file => {
                    if (x > file.x && x < file.x + 120 && y > file.y && y < file.y + 60) {
                        alert(`点击了文件: ${file.text} \n这是代码库中的一个文件，它们之间存在依赖关系。`);
                    }
                });
            };
        }

        function drawDeploymentView() {
            const canvas = document.getElementById('deploymentCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const servers = [
                { id: 'S1', x: 100, y: 100, text: 'Web服务器', color: '#ffc107' },
                { id: 'S2', x: 400, y: 100, text: '应用服务器', color: '#17a2b8' },
                { id: 'S3', x: 250, y: 250, text: '数据库服务器', color: '#20c997' }
            ];

            function drawServer(server) {
                ctx.fillStyle = server.color;
                ctx.fillRect(server.x, server.y, 180, 100);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(server.x, server.y, 180, 100);
                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(server.text, server.x + 90, server.y + 50);
                ctx.font = '12px Arial';
                ctx.fillText('组件: ' + server.text.replace('服务器', ''), server.x + 90, server.y + 80);
            }

            servers.forEach(drawServer);

            // Network connections
            ctx.strokeStyle = '#6610f2';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(servers[0].x + 180, servers[0].y + 50);
            ctx.lineTo(servers[1].x, servers[1].y + 50);
            ctx.stroke();
            ctx.fillText('HTTP请求', (servers[0].x + 180 + servers[1].x) / 2, servers[0].y + 50 - 10);

            ctx.beginPath();
            ctx.moveTo(servers[1].x + 90, servers[1].y + 100);
            ctx.lineTo(servers[2].x + 90, servers[2].y);
            ctx.stroke();
            ctx.fillText('数据库查询', servers[1].x + 90 + 10, (servers[1].y + 100 + servers[2].y) / 2);

            canvas.onclick = function(event) {
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                servers.forEach(server => {
                    if (x > server.x && x < server.x + 180 && y > server.y && y < server.y + 100) {
                        alert(`点击了: ${server.text} \n这是软件部署的物理节点，组件运行在此服务器上。`);
                    }
                });
            };
        }


        // Initial display
        showContent(currentContentIndex);
    </script>
</body>
</html> 