# Electron 桌面应用空白问题解决方案

## 问题描述
桌面应用启动后显示空白页面，无法正常加载 Vue 应用内容。

## 问题原因
1. **路径问题**：Vue 构建后的 index.html 中使用绝对路径（如 `/js/app.js`），在 Electron 中无法正确加载
2. **环境检测问题**：Electron 无法正确区分开发环境和生产环境
3. **安全策略问题**：Electron 的安全策略阻止了某些资源的加载

## 解决方案

### 1. 配置 Vue 使用相对路径
创建或修改 `vue.config.js` 文件：

```javascript
const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 设置公共路径为相对路径
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  
  // 生产环境下不生成 source map
  productionSourceMap: false,
  
  // 配置 webpack
  configureWebpack: {
    target: process.env.IS_ELECTRON ? 'electron-renderer' : 'web'
  }
})
```

### 2. 更新 Electron 主进程文件
修改 `electron-main.js`：

```javascript
const { app, BrowserWindow } = require('electron')
const path = require('path')
const fs = require('fs')

function createWindow () {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: true
    }
  })

  // 正确检测环境
  const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged
  
  if (isDev) {
    // 开发模式
    mainWindow.loadURL('http://localhost:8080')
    mainWindow.webContents.openDevTools()
  } else {
    // 生产模式
    const indexPath = path.join(__dirname, 'dist', 'index.html')
    mainWindow.loadFile(indexPath)
  }
}
```

### 3. 重新构建和打包

1. **清理旧文件**：
   ```bash
   rm -rf dist
   rm -rf dist-electron
   ```

2. **重新构建**：
   ```bash
   npm run build
   ```

3. **重新打包**：
   ```bash
   npm run electron:pack
   ```

### 4. 验证修复

检查 `dist/index.html` 中的路径应该是相对路径：
- ✓ 正确：`src="js/app.js"`
- ✗ 错误：`src="/js/app.js"`

## 常见问题

### Q: 应用仍然显示空白
A: 检查开发者工具的控制台是否有错误信息，通常是资源加载失败

### Q: 开发模式正常，生产模式空白
A: 确保 `dist` 目录存在且包含正确的文件

### Q: 打包后文件很大
A: 在 `vue.config.js` 中设置 `productionSourceMap: false`

## 测试命令

```bash
# 开发模式测试
npm run electron:serve

# 生产模式测试
npm run build && electron .

# 完整打包测试
npm run electron:pack
```
