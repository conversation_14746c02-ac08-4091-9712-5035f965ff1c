<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件需求分析互动学习</title>
    <style>
        @font-face {
            font-family: 'MiSans';
            src: url('https://web.archive.org/web/20210515000000im_/https://www.xiaomi.com/static/fonts/MiSans-Normal.woff2') format('woff2');
            font-weight: normal;
            font-style: normal;
        }

        body {
            font-family: 'MiSans', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f8f8;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start; /* Align to top */
            min-height: 100vh;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        .container {
            width: 90%;
            max-width: 1200px;
            padding: 2rem;
            background-color: #fff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            margin: 2rem 0;
            position: relative;
            z-index: 10;
            overflow: hidden; /* Ensure content doesn't spill out */
        }

        canvas {
            position: fixed; /* Fixed position to act as background */
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1; /* Behind other content */
            pointer-events: none; /* Allow clicks to pass through */
        }

        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 1.5rem;
        }

        h1 {
            font-size: 2.8rem;
            margin-bottom: 0.5rem;
        }

        h2 {
            font-size: 2rem;
            border-bottom: 2px solid #eee;
            padding-bottom: 0.5rem;
            margin-top: 2rem;
        }

        h3 {
            font-size: 1.5rem;
            margin-top: 1.5rem;
        }

        p {
            font-size: 1.1rem;
            line-height: 1.7;
            margin-bottom: 1rem;
        }

        .section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border-left: 4px solid #4CAF50;
            background-color: #f9f9f9;
            border-radius: 8px;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .subsection {
            margin-top: 1.5rem;
            padding-left: 1rem;
            border-left: 2px dashed #ccc;
        }

        .quiz-container {
            margin-top: 4rem;
            padding: 2rem;
            background-color: #e8f5e9;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
        }

        .question-statement {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            line-height: 1.8;
        }

        .options label {
            display: block;
            background-color: #fff;
            padding: 1rem 1.2rem;
            margin-bottom: 0.8rem;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
        }

        .options label:hover {
            background-color: #e0e0e0;
            transform: translateY(-2px);
        }

        .options input[type="radio"] {
            display: none;
        }

        .options input[type="radio"]:checked + label {
            background-color: #c8e6c9; /* Light green for selected */
            border: 2px solid #4CAF50;
            transform: translateY(0);
        }

        .submit-btn {
            display: block;
            width: 100%;
            padding: 1rem;
            font-size: 1.2rem;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 2rem;
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .submit-btn:hover {
            background-color: #45a049;
            transform: translateY(-2px);
        }

        .feedback {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 8px;
            font-size: 1.1rem;
            text-align: center;
            display: none;
        }

        .feedback.correct {
            background-color: #d4edda;
            color: #155724;
        }

        .feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
        }

        .explanation-details {
            margin-top: 2rem;
            padding: 1.5rem;
            background-color: #f1f8e9;
            border-left: 5px solid #8bc34a;
            border-radius: 8px;
            display: none;
        }

        .explanation-details.visible {
            display: block;
        }

        .explanation-details h3 {
            color: #689f38;
        }

        .key-term {
            font-weight: bold;
            color: #3f51b5; /* Indigo */
        }

        .start-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom right, #e0f2f7, #bbdefb);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 100;
            opacity: 1;
            transition: opacity 1s ease-out;
        }

        .start-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .start-screen h1 {
            font-size: 4rem;
            color: #2c3e50;
            margin-bottom: 2rem;
        }

        .start-button {
            padding: 1.2rem 2.5rem;
            font-size: 1.5rem;
            background-color: #2196f3; /* Blue */
            color: white;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
        }

        .start-button:hover {
            background-color: #1976d2;
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        /* Interactive element for concept explanation - Example */
        .concept-card {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            display: inline-block; /* For horizontal layout */
            margin-right: 1.5rem;
            vertical-align: top;
            width: 250px; /* Fixed width for better alignment */
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            background-color: #e3f2fd; /* Light blue */
        }

        .concept-card.active {
            border-color: #2196f3;
            box-shadow: 0 0 0 3px #90caf9;
            background-color: #e3f2fd;
        }

        .concept-card h3 {
            margin-top: 0;
            color: #2196f3;
        }

        .concept-details {
            margin-top: 1rem;
            font-size: 0.95rem;
            color: #555;
            text-align: left;
            display: none; /* Hidden by default */
        }

        .concept-details.visible {
            display: block;
        }

        .concept-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin-top: 2rem;
        }

        .qfd-game-container {
            display: flex;
            justify-content: center;
            align-items: flex-start;
            gap: 30px;
            flex-wrap: wrap;
            margin-top: 2rem;
        }

        .qfd-category-box {
            border: 2px dashed #9e9e9e;
            border-radius: 10px;
            padding: 1.5rem;
            min-width: 280px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            background-color: #f5f5f5;
            transition: all 0.3s ease;
            position: relative; /* For particle origin */
        }

        .qfd-category-box.highlight {
            border-color: #4CAF50;
            background-color: #e8f5e9;
        }

        .qfd-category-box h3 {
            margin-top: 0;
            color: #616161;
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }
        .qfd-category-box p {
            font-size: 0.9rem;
            text-align: center;
            line-height: 1.4;
            color: #666;
        }

        .qfd-item-pool {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 1.5rem;
            padding: 1rem;
            background-color: #e3f2fd;
            border-radius: 8px;
            box-shadow: inset 0 0 10px rgba(0,0,0,0.05);
        }

        .qfd-item {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 0.8rem 1.2rem;
            cursor: grab;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            min-width: 150px;
            text-align: center;
            position: relative; /* For drag positioning */
        }

        .qfd-item:active {
            cursor: grabbing;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            transform: scale(1.02);
        }
        .qfd-item.correct-placed {
            background-color: #d4edda;
            border-color: #4CAF50;
            cursor: default;
        }
        .qfd-item.incorrect-placed {
            background-color: #f8d7da;
            border-color: #dc3545;
            animation: pulse-red 0.5s infinite alternate;
        }

        @keyframes pulse-red {
            from {
                transform: scale(1);
            }
            to {
                transform: scale(1.02);
                box-shadow: 0 0 0 5px rgba(220, 53, 69, 0.3);
            }
        }

        .qfd-feedback {
            margin-top: 1rem;
            font-size: 1rem;
            text-align: center;
            display: none;
        }

        .qfd-feedback.correct {
            color: #28a745;
        }
        .qfd-feedback.incorrect {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <canvas id="backgroundCanvas"></canvas>

    <div class="start-screen" id="startScreen">
        <h1>软件需求分析互动学习</h1>
        <button class="start-button" id="startButton">开始学习</button>
    </div>

    <div class="container" id="mainContent" style="display: none;">
        <h1>软件需求分析</h1>
        <p>本课程将通过互动方式，带你深入理解软件需求分析的各个层面。</p>

        <section class="section" id="section1">
            <h2>需求层次概览</h2>
            <p>需求是多层次的，从高层目标到具体细节，可以分为：</p>
            <div class="concept-grid">
                <div class="concept-card" data-concept="biz">
                    <h3>业务需求 (Business Requirements)</h3>
                    <div class="concept-details">
                        <p><span class="key-term">定义:</span> 反映企业或客户对系统高层次的目标要求。</p>
                        <p><span class="key-term">来源:</span> 通常来自项目投资人、购买产品的客户、客户单位的管理人员、市场营销部门或产品策划部门等。</p>
                        <p><span class="key-term">目的:</span> 确定项目视图和范围。</p>
                        <p><span class="key-term">示例:</span> 公司需要一个系统来提高财务报表的生成效率，并减少人工错误。</p>
                    </div>
                </div>
                <div class="concept-card" data-concept="user">
                    <h3>用户需求 (User Requirements)</h3>
                    <div class="concept-details">
                        <p><span class="key-term">定义:</span> 描述的是用户的具体目标，或用户要求系统必须能完成的任务。</p>
                        <p><span class="key-term">目的:</span> 描述了用户能使用系统来做些什么。</p>
                        <p><span class="key-term">获取:</span> 通常采取用户访谈和问卷调查等方式，对用户使用的场景（scenarios）进行整理。</p>
                        <p><span class="key-term">示例:</span> 财务人员需要能够通过系统录入日常凭证，并自动计算借贷平衡。</p>
                    </div>
                </div>
                <div class="concept-card" data-concept="system">
                    <h3>系统需求 (System Requirements)</h3>
                    <div class="concept-details">
                        <p><span class="key-term">定义:</span> 从系统的角度来说明软件的需求，包括功能需求、非功能需求和设计约束等。</p>
                        <p><span class="key-term">目的:</span> 指导开发人员具体实现软件。</p>
                        <p><span class="key-term">分类:</span> 功能需求、非功能需求、设计约束。</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="section2">
            <h2>系统需求详解</h2>
            <p>系统需求是指导软件开发的具体蓝图，它包含三个主要部分：</p>
            <div class="concept-grid">
                <div class="concept-card" data-concept="functional">
                    <h3>功能需求 (Functional Requirements)</h3>
                    <div class="concept-details">
                        <p><span class="key-term">定义:</span> 规定了开发人员必须在系统中实现的软件功能，用户利用这些功能来完成任务，满足业务需要。</p>
                        <p><span class="key-term">表现:</span> 通常通过系统特性的描述表现出来，表示系统为用户提供某项功能（服务）。</p>
                        <p><span class="key-term">示例:</span> 系统应支持用户创建、修改、删除和查询凭证。</p>
                        <p><span class="key-term">示例:</span> 系统应能生成资产负债表和利润表。</p>
                    </div>
                </div>
                <div class="concept-card" data-concept="non-functional">
                    <h3>非功能需求 (Non-functional Requirements)</h3>
                    <div class="concept-details">
                        <p><span class="key-term">定义:</span> 系统必须具备的属性或品质，描述了系统的“怎么样”。</p>
                        <p><span class="key-term">分类:</span> 可细分为软件质量属性（例如，可维护性、效率、安全性、可靠性等）和其他非功能需求。</p>
                        <p><span class="key-term">示例:</span> 系统响应时间在3秒以内。</p>
                        <p><span class="key-term">示例:</span> 系统应支持1000个并发用户。</p>
                        <p><span class="key-term">示例:</span> 系统的安全性应符合国家金融安全标准。</p>
                    </div>
                </div>
                <div class="concept-card" data-concept="design-constraints">
                    <h3>设计约束 (Design Constraints)</h3>
                    <div class="concept-details">
                        <p><span class="key-term">定义:</span> 对系统设计或实现过程施加的限制或条件。</p>
                        <p><span class="key-term">示例:</span> 系统必须采用Linux操作系统。</p>
                        <p><span class="key-term">示例:</span> 必须使用Java语言进行开发。</p>
                        <p><span class="key-term">示例:</span> 必须采用国产数据库。</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="section3">
            <h2>质量功能部署 (QFD) 游戏</h2>
            <p>将下面的需求拖拽到正确的类别中，然后点击“检查QFD分类”按钮。</p>
            <div class="qfd-game-container">
                <div class="qfd-category-box" data-category="常规需求">
                    <h3>常规需求</h3>
                    <p>用户认为系统应该做到的功能或性能，实现越多用户会越满意。</p>
                </div>
                <div class="qfd-category-box" data-category="期望需求">
                    <h3>期望需求</h3>
                    <p>用户想当然认为系统应具备的功能或性能，但并不能正确描述自己想要得到的这些功能或性能需求。如果期望需求没有得到实现，会让用户感到不满意。</p>
                </div>
                <div class="qfd-category-box" data-category="意外需求">
                    <h3>意外需求</h3>
                    <p>用户要求范围外的功能或性能，实现这些需求用户会更高兴。</p>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <div class="qfd-item-pool" id="qfdItemPool">
                    <div class="qfd-item" draggable="true" data-type="常规需求">凭证录入功能</div>
                    <div class="qfd-item" draggable="true" data-type="期望需求">系统操作简便易用</div>
                    <div class="qfd-item" draggable="true" data-type="意外需求">自动生成图形化报表</div>
                    <div class="qfd-item" draggable="true" data-type="常规需求">用户管理模块</div>
                    <div class="qfd-item" draggable="true" data-type="期望需求">数据输入校验</div>
                    <div class="qfd-item" draggable="true" data-type="意外需求">AI智能推荐预算方案</div>
                </div>
                <button class="submit-btn" id="qfdCheckButton">检查QFD分类</button>
                <div class="qfd-feedback" id="qfdFeedback"></div>
            </div>
        </section>

        <section class="section quiz-container" id="quizSection">
            <h2>小测验</h2>
            <p class="question-statement">
                某软件公司承担了某金融公司财务管理系统的建设。在需求分析阶段，公司分析人员整理出一些相关的系统需求，其中：
                <ol>
                    <li>“登陆用户的密码，30天需要修改一次，而且不能使用最近5次的密码，以提高系统的安全性”</li>
                    <li>“要求采用国有自主知识产权的数据库”</li>
                    <li>“在凭证录入时对往来款项进行详细的信息录入”</li>
                    <li>“用户在凭证录入界面录入信息后，点击保存按钮后，客户认为一般能在3秒钟内保存到数据库中”</li>
                </ol>
                以上描述分别属于哪种需求？请选择与第一条描述对应的需求类型。
            </p>
            <div class="options">
                <input type="radio" id="optionA" name="question" value="A">
                <label for="optionA">A. 业务需求</label>
                <input type="radio" id="optionB" name="question" value="B">
                <label for="optionB">B. 功能需求</label>
                <input type="radio" id="optionC" name="question" value="C">
                <label for="optionC">C. 设计约束</label>
                <input type="radio" id="optionD" name="question" value="D">
                <label for="optionD">D. 非功能需求</label>
            </div>
            <button class="submit-btn" id="submitQuiz">提交答案</button>
            <div class="feedback" id="quizFeedback"></div>
            <div class="explanation-details" id="quizExplanation">
                <h3>答案解析</h3>
                <p>让我们逐一分析题目中的需求：</p>
                <ul>
                    <li><p><span class="key-term">1. “登陆用户的密码，30天需要修改一次，而且不能使用最近5次的密码，以提高系统的安全性”</span><br>
                        这描述的是系统在安全性方面的要求，属于系统必须具备的品质属性，因此是<span class="key-term">非功能需求</span>（具体是安全性）。非功能需求关注系统“怎么样”，比如性能、安全性、可用性等。</p></li>
                    <li><p><span class="key-term">2. “要求采用国有自主知识产权的数据库”</span><br>
                        这限制了系统在技术选型上的要求，是外部环境或项目决策对系统设计施加的限制或条件，因此是<span class="key-term">设计约束</span>。设计约束是关于系统如何构建的限制。</p></li>
                    <li><p><span class="key-term">3. “在凭证录入时对往来款项进行详细的信息录入”</span><br>
                        这描述的是系统需要实现的具体业务功能，用户将利用此功能完成凭证录入任务，因此是<span class="key-term">功能需求</span>。功能需求关注系统“做什么”，是系统提供给用户的服务或行为。</p></li>
                    <li><p><span class="key-term">4. “用户在凭证录入界面录入信息后，点击保存按钮后，客户认为一般能在3秒钟内保存到数据库中”</span><br>
                        这描述的是系统在性能方面的要求（响应时间），是系统必须具备的品质属性，因此是<span class="key-term">非功能需求</span>（具体是性能）。</p></li>
                </ul>
                <p>所以，与第一条描述对应的需求类型是 <strong>D. 非功能需求</strong>。</p>
            </div>
        </section>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const startScreen = document.getElementById('startScreen');
            const startButton = document.getElementById('startButton');
            const mainContent = document.getElementById('mainContent');
            const sections = document.querySelectorAll('.section');
            const conceptCards = document.querySelectorAll('.concept-card');
            const submitQuizBtn = document.getElementById('submitQuiz');
            const quizFeedback = document.getElementById('quizFeedback');
            const quizExplanation = document.getElementById('quizExplanation');
            const qfdItems = document.querySelectorAll('.qfd-item');
            const qfdCategoryBoxes = document.querySelectorAll('.qfd-category-box');
            const qfdCheckButton = document.getElementById('qfdCheckButton');
            const qfdFeedback = document.getElementById('qfdFeedback');
            const qfdItemPool = document.getElementById('qfdItemPool');

            // --- Canvas Setup and Animation ---
            const canvas = document.getElementById('backgroundCanvas');
            const ctx = canvas.getContext('2d');
            let particles = [];
            let animationFrameId;

            function resizeCanvas() {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }

            window.addEventListener('resize', resizeCanvas);
            resizeCanvas();

            class Particle {
                constructor(x, y, radius, color, velocity) {
                    this.x = x;
                    this.y = y;
                    this.radius = radius;
                    this.color = color;
                    this.velocity = velocity;
                    this.alpha = 1;
                    this.friction = 0.99; // Added friction for a more natural slowdown
                }

                draw() {
                    ctx.save();
                    ctx.globalAlpha = this.alpha;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2, false);
                    ctx.fillStyle = this.color;
                    ctx.fill();
                    ctx.restore();
                }

                update() {
                    this.draw();
                    this.x += this.velocity.x;
                    this.y += this.velocity.y;
                    this.alpha -= 0.008; // Fade out slightly slower
                    this.velocity.x *= this.friction;
                    this.velocity.y *= this.friction;
                }
            }

            function createParticleBurst(x, y, count, color) {
                for (let i = 0; i < count; i++) {
                    const angle = Math.random() * Math.PI * 2;
                    const speed = (Math.random() * 3) + 0.5; // Slightly faster initial speed
                    particles.push(new Particle(x, y, Math.random() * 2 + 1, color, {
                        x: Math.cos(angle) * speed,
                        y: Math.sin(angle) * speed
                    }));
                }
            }

            function animateCanvas() {
                animationFrameId = requestAnimationFrame(animateCanvas);
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                for (let i = particles.length - 1; i >= 0; i--) {
                    particles[i].update();
                    // Remove particles that are too transparent or too small
                    if (particles[i].alpha <= 0.05 || particles[i].radius < 0.5) {
                        particles.splice(i, 1);
                    }
                }
            }
            animateCanvas(); // Start background animation

            // --- Start Screen Logic ---
            startButton.addEventListener('click', () => {
                startScreen.classList.add('hidden');
                setTimeout(() => {
                    startScreen.style.display = 'none';
                    mainContent.style.display = 'block';
                    // Trigger initial animations after main content is visible
                    setTimeout(revealSections, 100);
                }, 1000); // Wait for fade out transition
            });

            // --- Section Reveal Animation (on scroll into view) ---
            const observerOptions = {
                root: null,
                rootMargin: '0px',
                threshold: 0.1 // When 10% of the section is visible
            };

            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        // Add a small particle burst from the center of the revealed section
                        const rect = entry.target.getBoundingClientRect();
                        const centerX = rect.left + rect.width / 2;
                        const centerY = rect.top + rect.height / 2;
                        createParticleBurst(centerX, centerY, 30, '#4CAF50'); // Green burst
                        observer.unobserve(entry.target); // Only animate once per section
                    }
                });
            }, observerOptions);

            function revealSections() {
                sections.forEach(section => {
                    observer.observe(section);
                });
            }

            // --- Concept Card Interaction ---
            conceptCards.forEach(card => {
                card.addEventListener('click', () => {
                    const details = card.querySelector('.concept-details');
                    // Close other open concept cards
                    conceptCards.forEach(c => {
                        if (c !== card) {
                            c.classList.remove('active');
                            c.querySelector('.concept-details').classList.remove('visible');
                        }
                    });
                    // Toggle current card's active state and details visibility
                    card.classList.toggle('active');
                    details.classList.toggle('visible');

                    // Particle effect on click
                    const rect = card.getBoundingClientRect();
                    const x = rect.left + rect.width / 2;
                    const y = rect.top + rect.height / 2;
                    createParticleBurst(x, y, 50, '#2196f3'); // Blue burst
                });
            });

            // --- Quiz Logic ---
            submitQuizBtn.addEventListener('click', () => {
                const selectedOption = document.querySelector('input[name="question"]:checked');
                if (selectedOption) {
                    const answer = selectedOption.value;
                    const correctOption = 'D'; // The correct answer for the first part of the question

                    if (answer === correctOption) {
                        quizFeedback.textContent = '恭喜你，回答正确！🎉';
                        quizFeedback.className = 'feedback correct';
                        createParticleBurst(submitQuizBtn.getBoundingClientRect().x + submitQuizBtn.getBoundingClientRect().width / 2,
                                            submitQuizBtn.getBoundingClientRect().y + submitQuizBtn.getBoundingClientRect().height / 2,
                                            100, '#4CAF50'); // Green burst for correct
                    } else {
                        quizFeedback.textContent = '很遗憾，回答错误。再回顾一下知识点哦！😢';
                        quizFeedback.className = 'feedback incorrect';
                        createParticleBurst(submitQuizBtn.getBoundingClientRect().x + submitQuizBtn.getBoundingClientRect().width / 2,
                                            submitQuizBtn.getBoundingClientRect().y + submitQuizBtn.getBoundingClientRect().height / 2,
                                            100, '#f44336'); // Red burst for incorrect
                    }
                    quizFeedback.style.display = 'block';
                    quizExplanation.classList.add('visible'); // Show explanation after submission

                    // Smooth scroll to explanation
                    setTimeout(() => {
                        quizExplanation.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }, 500);

                } else {
                    alert('请选择一个答案！');
                }
            });

            // --- QFD Game Logic (Drag and Drop) ---
            let draggedItem = null;

            qfdItems.forEach(item => {
                item.addEventListener('dragstart', (e) => {
                    draggedItem = item;
                    e.dataTransfer.setData('text/plain', item.dataset.type); // Store correct type in dataTransfer
                    // Use a small timeout to ensure the drag image is captured before opacity change
                    setTimeout(() => {
                        item.style.opacity = '0.5';
                    }, 0);
                });

                item.addEventListener('dragend', () => {
                    if (draggedItem) {
                        draggedItem.style.opacity = '1'; // Restore opacity after drag ends
                    }
                    draggedItem = null;
                });
            });

            qfdCategoryBoxes.forEach(box => {
                box.addEventListener('dragover', (e) => {
                    e.preventDefault(); // Allow drop
                    box.classList.add('highlight');
                });

                box.addEventListener('dragleave', () => {
                    box.classList.remove('highlight');
                });

                box.addEventListener('drop', (e) => {
                    e.preventDefault();
                    box.classList.remove('highlight');
                    const correctType = e.dataTransfer.getData('text/plain'); // Get the stored correct type

                    if (draggedItem) {
                        // Check if the drop is correct, but don't force placement yet
                        // Only append if it's correct for now. Final check with button.
                        // Append the item to the category box
                        box.appendChild(draggedItem);
                        draggedItem.classList.remove('incorrect-placed'); // Remove incorrect styling if it had it
                        draggedItem.classList.add('correct-placed'); // Indicate it's currently in a correct-looking place
                        createParticleBurst(draggedItem.getBoundingClientRect().x + draggedItem.getBoundingClientRect().width / 2,
                                            draggedItem.getBoundingClientRect().y + draggedItem.getBoundingClientRect().height / 2,
                                            20, '#4CAF50'); // Green burst for good drop
                    }
                });
            });

            qfdCheckButton.addEventListener('click', () => {
                let allCorrect = true;
                qfdFeedback.textContent = ''; // Clear previous feedback
                qfdFeedback.className = 'qfd-feedback'; // Reset classes

                qfdItems.forEach(item => {
                    const parentBox = item.closest('.qfd-category-box');
                    if (!parentBox || item.dataset.type !== parentBox.dataset.category) {
                        allCorrect = false;
                        item.classList.remove('correct-placed');
                        item.classList.add('incorrect-placed'); // Highlight incorrect
                        // Animate wrong items back to pool (optional, more complex)
                        // For now, they just stay colored red
                    } else {
                        item.classList.remove('incorrect-placed');
                        item.classList.add('correct-placed'); // Ensure correct items are green
                    }
                });

                if (allCorrect) {
                    qfdFeedback.textContent = '所有QFD分类都正确！棒极了！🎉';
                    qfdFeedback.classList.add('correct');
                    createParticleBurst(qfdCheckButton.getBoundingClientRect().x + qfdCheckButton.getBoundingClientRect().width / 2,
                                        qfdCheckButton.getBoundingClientRect().y + qfdCheckButton.getBoundingClientRect().height / 2,
                                        150, '#28a745'); // Large green burst
                } else {
                    qfdFeedback.textContent = '还有一些分类不正确，请再试一次！🤔';
                    qfdFeedback.classList.add('incorrect');
                    createParticleBurst(qfdCheckButton.getBoundingClientRect().x + qfdCheckButton.getBoundingClientRect().width / 2,
                                        qfdCheckButton.getBoundingClientRect().y + qfdCheckButton.getBoundingClientRect().height / 2,
                                        50, '#dc3545'); // Smaller red burst
                }
                qfdFeedback.style.display = 'block'; // Show feedback
            });
        });
    </script>
</body>
</html> 