<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PV操作与进程同步 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8em;
            color: #667eea;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .concept-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }

        .process-diagram {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            min-height: 300px;
        }

        .process {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .process.p1 { background: #ff6b6b; }
        .process.p2 { background: #4ecdc4; }
        .process.p3 { background: #45b7d1; }
        .process.p4 { background: #96ceb4; }
        .process.p5 { background: #feca57; }

        .process:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .process.active {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .arrow {
            width: 50px;
            height: 3px;
            background: #333;
            position: relative;
            margin: 0 10px;
        }

        .arrow::after {
            content: '';
            position: absolute;
            right: -5px;
            top: -5px;
            width: 0;
            height: 0;
            border-left: 10px solid #333;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }

        .semaphore-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
        }

        .semaphore {
            display: inline-block;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #6c757d;
            color: white;
            line-height: 60px;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .semaphore.active {
            background: #28a745;
            animation: bounce 0.5s ease;
        }

        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }

        .control-panel {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .explanation {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .option {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateX(5px);
        }

        .option.correct {
            border-color: #28a745;
            background: #d4edda;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #f8d7da;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ddd;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 5px;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            transform: scale(1.2);
        }

        .step.completed {
            background: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 PV操作与进程同步</h1>
            <p>通过动画和交互学习进程同步的核心概念</p>
        </div>

        <!-- 基础概念 -->
        <div class="section">
            <h2 class="section-title">📚 基础概念</h2>
            
            <div class="concept-box">
                <h3>🎯 什么是PV操作？</h3>
                <p><strong>P操作（Wait）</strong>：等待信号量，如果信号量>0则减1并继续，否则阻塞等待</p>
                <p><strong>V操作（Signal）</strong>：释放信号量，将信号量加1，唤醒等待的进程</p>
            </div>

            <div class="explanation">
                <h4>💡 生活中的例子</h4>
                <p>想象一个停车场：</p>
                <ul>
                    <li><strong>信号量</strong> = 停车位数量</li>
                    <li><strong>P操作</strong> = 进入停车场（占用一个车位）</li>
                    <li><strong>V操作</strong> = 离开停车场（释放一个车位）</li>
                </ul>
            </div>
        </div>

        <!-- 进程前趋图演示 -->
        <div class="section">
            <h2 class="section-title">🔗 进程前趋图分析</h2>
            
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
                <div class="step" id="step5">5</div>
            </div>

            <div class="process-diagram" id="processDiagram">
                <!-- 动态生成进程图 -->
            </div>

            <div class="control-panel">
                <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置</button>
                <button class="btn" onclick="nextStep()">➡️ 下一步</button>
            </div>

            <div class="explanation" id="stepExplanation">
                <h4>📖 当前步骤说明</h4>
                <p id="explanationText">点击"开始演示"来查看进程执行顺序</p>
            </div>
        </div>

        <!-- 信号量演示 -->
        <div class="section">
            <h2 class="section-title">🚦 信号量状态演示</h2>

            <div class="semaphore-box">
                <h4>信号量状态（初值都为0）</h4>
                <div class="semaphore" id="s1">S1<br>0</div>
                <div class="semaphore" id="s2">S2<br>0</div>
                <div class="semaphore" id="s3">S3<br>0</div>
                <div class="semaphore" id="s4">S4<br>0</div>
                <div class="semaphore" id="s5">S5<br>0</div>
            </div>

            <div class="explanation">
                <h4>🔍 PV操作分析</h4>
                <div id="pvAnalysis">
                    <p>根据前趋图，我们需要分析每个进程的PV操作...</p>
                </div>
            </div>
        </div>

        <!-- 题目解答 -->
        <div class="section quiz-section">
            <h2 class="section-title">📝 题目解答</h2>

            <div class="explanation">
                <h4>🎯 题目分析</h4>
                <p><strong>空f：</strong>P4进程开始前需要等待P2和P3完成</p>
                <p><strong>空g：</strong>P4进程完成后需要通知P5</p>
                <p><strong>空h：</strong>P5进程开始前需要等待P4完成</p>
            </div>

            <div class="option" onclick="selectOption(this, false)">
                <strong>A.</strong> P（S3）V（S4）、V（S5）和 P（S5）
            </div>
            <div class="option" onclick="selectOption(this, false)">
                <strong>B.</strong> V（S3）V（S4）、P（S5）和 V（S5）
            </div>
            <div class="option" onclick="selectOption(this, true)">
                <strong>C.</strong> P（S3）P（S4）、V（S5）和 P（S5） ✓
            </div>
            <div class="option" onclick="selectOption(this, false)">
                <strong>D.</strong> V（S3）P（S4）、P（S5）和 V（S5）
            </div>

            <div class="explanation" id="answerExplanation" style="display: none;">
                <h4>✅ 正确答案解析</h4>
                <p><strong>f处：P（S3）P（S4）</strong> - P4需要等待P2和P3都完成</p>
                <p><strong>g处：V（S5）</strong> - P4完成后通知P5可以开始</p>
                <p><strong>h处：P（S5）</strong> - P5等待P4的通知</p>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let animationRunning = false;
        const semaphoreValues = { s1: 0, s2: 0, s3: 0, s4: 0, s5: 0 };

        // 进程执行步骤
        const steps = [
            {
                process: 'P1',
                description: 'P1进程开始执行，完成后执行V(S1)和V(S2)操作',
                semaphoreChanges: { s1: 1, s2: 1 },
                pvOperations: ['V(S1)', 'V(S2)']
            },
            {
                process: 'P2',
                description: 'P2进程等待P1完成，执行P(S1)，完成后执行V(S3)',
                semaphoreChanges: { s1: 0, s3: 1 },
                pvOperations: ['P(S1)', 'V(S3)']
            },
            {
                process: 'P3',
                description: 'P3进程等待P1完成，执行P(S2)，完成后执行V(S4)',
                semaphoreChanges: { s2: 0, s4: 1 },
                pvOperations: ['P(S2)', 'V(S4)']
            },
            {
                process: 'P4',
                description: 'P4进程等待P2和P3完成，执行P(S3)P(S4)，完成后执行V(S5)',
                semaphoreChanges: { s3: 0, s4: 0, s5: 1 },
                pvOperations: ['P(S3)', 'P(S4)', 'V(S5)']
            },
            {
                process: 'P5',
                description: 'P5进程等待P4完成，执行P(S5)',
                semaphoreChanges: { s5: 0 },
                pvOperations: ['P(S5)']
            }
        ];

        // 初始化进程图
        function initProcessDiagram() {
            const diagram = document.getElementById('processDiagram');
            diagram.innerHTML = `
                <div class="process p1" id="process-P1">P1</div>
                <div class="arrow"></div>
                <div style="display: flex; flex-direction: column; align-items: center;">
                    <div class="process p2" id="process-P2">P2</div>
                    <div style="margin: 10px 0;">
                        <div class="process p3" id="process-P3">P3</div>
                    </div>
                </div>
                <div class="arrow"></div>
                <div class="process p4" id="process-P4">P4</div>
                <div class="arrow"></div>
                <div class="process p5" id="process-P5">P5</div>
            `;
        }

        // 更新信号量显示
        function updateSemaphore(id, value) {
            const element = document.getElementById(id);
            element.innerHTML = `${id.toUpperCase()}<br>${value}`;
            element.classList.add('active');
            setTimeout(() => element.classList.remove('active'), 500);
        }

        // 开始动画
        function startAnimation() {
            if (animationRunning) return;
            animationRunning = true;
            currentStep = 0;
            resetSemaphores();
            executeStep();
        }

        // 执行步骤
        function executeStep() {
            if (currentStep >= steps.length) {
                animationRunning = false;
                return;
            }

            const step = steps[currentStep];

            // 更新步骤指示器
            updateStepIndicator();

            // 高亮当前进程
            highlightProcess(step.process);

            // 更新说明
            updateExplanation(step);

            // 更新信号量
            setTimeout(() => {
                for (const [sem, value] of Object.entries(step.semaphoreChanges)) {
                    semaphoreValues[sem] = value;
                    updateSemaphore(sem, value);
                }
            }, 1000);

            currentStep++;
        }

        // 下一步
        function nextStep() {
            if (!animationRunning) {
                startAnimation();
                return;
            }
            executeStep();
        }

        // 重置动画
        function resetAnimation() {
            animationRunning = false;
            currentStep = 0;
            resetSemaphores();
            clearHighlights();
            updateStepIndicator();
            document.getElementById('explanationText').textContent = '点击"开始演示"来查看进程执行顺序';
        }

        // 重置信号量
        function resetSemaphores() {
            for (const sem in semaphoreValues) {
                semaphoreValues[sem] = 0;
                updateSemaphore(sem, 0);
            }
        }

        // 高亮进程
        function highlightProcess(processName) {
            clearHighlights();
            const element = document.getElementById(`process-${processName}`);
            if (element) {
                element.classList.add('active');
            }
        }

        // 清除高亮
        function clearHighlights() {
            document.querySelectorAll('.process').forEach(p => p.classList.remove('active'));
        }

        // 更新步骤指示器
        function updateStepIndicator() {
            for (let i = 1; i <= 5; i++) {
                const step = document.getElementById(`step${i}`);
                step.classList.remove('active', 'completed');
                if (i === currentStep + 1) {
                    step.classList.add('active');
                } else if (i <= currentStep) {
                    step.classList.add('completed');
                }
            }
        }

        // 更新说明
        function updateExplanation(step) {
            const text = `
                <strong>${step.process}进程：</strong>${step.description}<br>
                <strong>PV操作：</strong>${step.pvOperations.join(', ')}
            `;
            document.getElementById('explanationText').innerHTML = text;
        }

        // 选择选项
        function selectOption(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 标记选择
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('answerExplanation').style.display = 'block';
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                document.querySelector('.option:nth-child(4)').classList.add('correct');
                document.getElementById('answerExplanation').style.display = 'block';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initProcessDiagram();
            updateStepIndicator();
        });
    </script>
</body>
</html>
