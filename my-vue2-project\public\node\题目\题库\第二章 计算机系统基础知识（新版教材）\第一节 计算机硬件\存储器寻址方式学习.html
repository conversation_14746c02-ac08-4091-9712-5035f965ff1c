<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储器寻址方式 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .question-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            animation: slideInUp 1s ease-out;
        }

        .question-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .option {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: bold;
            border: none;
            font-size: 1em;
        }

        .option:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .option.correct {
            background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%);
            animation: shake 0.6s ease-in-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        canvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
        }

        .explanation {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-top: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            display: none;
            animation: fadeIn 1s ease-out;
        }

        .storage-demo {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
            margin: 20px 0;
        }

        .storage-type {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .storage-type:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        .storage-type.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            transform: scale(1.1);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 存储器寻址方式学习</h1>
            <p>通过动画和交互来理解存储器的分类</p>
        </div>

        <div class="question-card">
            <div class="question-title">
                <strong>题目：</strong>（ ）不属于按寻址方式划分的一类存储器。
            </div>
            <div class="options">
                <button class="option" data-answer="A">A. 随机存储器</button>
                <button class="option" data-answer="B">B. 顺序存储器</button>
                <button class="option" data-answer="C">C. 相联存储器</button>
                <button class="option" data-answer="D">D. 直接存储器</button>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <p><strong>正确答案：C</strong> | <strong>你的答案：B</strong></p>
            </div>
        </div>

        <div class="canvas-container">
            <h3 style="text-align: center; margin-bottom: 20px; color: #333;">🎮 交互式存储器演示</h3>
            <canvas id="storageCanvas"></canvas>
            <div style="text-align: center; margin-top: 15px;">
                <button class="interactive-btn" onclick="startAnimation()">开始演示</button>
                <button class="interactive-btn" onclick="resetAnimation()">重置</button>
            </div>
        </div>

        <div class="storage-demo">
            <div class="storage-type" onclick="showStorageType('random')">
                <h4>🎲 随机存储器</h4>
                <p>可随机访问任意地址</p>
            </div>
            <div class="storage-type" onclick="showStorageType('sequential')">
                <h4>📼 顺序存储器</h4>
                <p>按顺序访问数据</p>
            </div>
            <div class="storage-type" onclick="showStorageType('direct')">
                <h4>🎯 直接存储器</h4>
                <p>直接定位到指定位置</p>
            </div>
            <div class="storage-type" onclick="showStorageType('associative')">
                <h4>🔍 相联存储器</h4>
                <p>按内容查找数据</p>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3 style="color: #667eea; margin-bottom: 20px;">📚 知识点解析</h3>
            <div id="explanationContent">
                <p><strong>存储器按寻址方式分类：</strong></p>
                <ul style="margin: 15px 0; padding-left: 20px; line-height: 1.8;">
                    <li><strong>随机存储器（RAM）：</strong>可以随机访问任意存储单元，访问时间相同</li>
                    <li><strong>顺序存储器：</strong>必须按顺序访问，如磁带存储器</li>
                    <li><strong>直接存储器：</strong>可以直接定位到指定位置，如磁盘存储器</li>
                </ul>
                <p style="margin-top: 20px;"><strong>为什么相联存储器不属于按寻址方式分类？</strong></p>
                <p style="margin: 10px 0; line-height: 1.6;">相联存储器是按<strong>存储内容</strong>来查找和访问数据的，而不是按地址。它根据数据的内容特征进行匹配查找，属于按存储原理分类，不是按寻址方式分类。</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('storageCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        let animationId;
        let currentDemo = 'random';
        let animationStep = 0;

        // 题目选项处理
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'C') {
                    this.classList.add('correct');
                } else {
                    this.classList.add('wrong');
                }
                
                setTimeout(() => {
                    document.getElementById('explanation').style.display = 'block';
                }, 1000);
            });
        });

        function drawMemoryBlock(x, y, width, height, color, label, isActive = false) {
            ctx.fillStyle = isActive ? '#4facfe' : color;
            ctx.fillRect(x, y, width, height);
            
            ctx.strokeStyle = isActive ? '#0099ff' : '#333';
            ctx.lineWidth = isActive ? 3 : 1;
            ctx.strokeRect(x, y, width, height);
            
            ctx.fillStyle = '#fff';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + width/2, y + height/2 + 4);
        }

        function drawArrow(fromX, fromY, toX, toY, color = '#ff6b6b') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const headLength = 10;
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI/6), toY - headLength * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI/6), toY - headLength * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function animateRandomAccess() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const blockWidth = 60;
            const blockHeight = 40;
            const startX = 50;
            const startY = 100;
            
            // 绘制内存块
            for (let i = 0; i < 8; i++) {
                const x = startX + (i % 4) * (blockWidth + 10);
                const y = startY + Math.floor(i / 4) * (blockHeight + 10);
                const isActive = (animationStep % 8) === i;
                drawMemoryBlock(x, y, blockWidth, blockHeight, '#667eea', `地址${i}`, isActive);
            }
            
            // 绘制访问指针
            const activeIndex = animationStep % 8;
            const activeX = startX + (activeIndex % 4) * (blockWidth + 10) + blockWidth/2;
            const activeY = startY + Math.floor(activeIndex / 4) * (blockHeight + 10);
            
            drawArrow(activeX, 50, activeX, activeY, '#ff6b6b');
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('随机访问 - 可直接访问任意地址', canvas.width/2, 30);
        }

        function animateSequentialAccess() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const blockWidth = 50;
            const blockHeight = 40;
            const startX = 50;
            const startY = 120;
            
            // 绘制磁带式存储
            for (let i = 0; i < 10; i++) {
                const x = startX + i * (blockWidth + 5);
                const isActive = i <= (animationStep % 10);
                const isPast = i < (animationStep % 10);
                drawMemoryBlock(x, startY, blockWidth, blockHeight, 
                    isPast ? '#ccc' : '#667eea', `${i}`, isActive && !isPast);
            }
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('顺序访问 - 必须按顺序读取', canvas.width/2, 30);
            ctx.fillText('如磁带存储器', canvas.width/2, 50);
        }

        function animateDirectAccess() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制磁盘
            const centerX = canvas.width / 2;
            const centerY = 150;
            const radius = 80;
            
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 绘制磁道
            for (let i = 1; i <= 3; i++) {
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius * i / 3, 0, 2 * Math.PI);
                ctx.stroke();
            }
            
            // 绘制读写头
            const angle = (animationStep * 0.1) % (2 * Math.PI);
            const headX = centerX + Math.cos(angle) * radius;
            const headY = centerY + Math.sin(angle) * radius;
            
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(headX, headY, 5, 0, 2 * Math.PI);
            ctx.fill();
            
            drawArrow(centerX, centerY, headX, headY, '#ff6b6b');
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('直接访问 - 可直接定位到指定位置', canvas.width/2, 30);
            ctx.fillText('如磁盘存储器', canvas.width/2, 50);
        }

        function animateAssociativeAccess() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const blockWidth = 80;
            const blockHeight = 30;
            const startX = 50;
            const startY = 80;
            
            const data = ['Apple', 'Banana', 'Cherry', 'Date', 'Elderberry'];
            const searchTerm = 'Cherry';
            
            for (let i = 0; i < data.length; i++) {
                const y = startY + i * (blockHeight + 10);
                const isMatch = data[i] === searchTerm && (animationStep % 20) > 10;
                drawMemoryBlock(startX, y, blockWidth, blockHeight, 
                    isMatch ? '#4facfe' : '#667eea', data[i], isMatch);
            }
            
            // 绘制搜索框
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 2;
            ctx.strokeRect(startX + 120, startY + 60, 100, 30);
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('搜索: Cherry', startX + 125, startY + 80);
            
            if ((animationStep % 20) > 10) {
                drawArrow(startX + 170, startY + 75, startX + blockWidth, startY + 2 * (blockHeight + 10) + blockHeight/2, '#4facfe');
            }
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('相联访问 - 按内容查找数据', canvas.width/2, 30);
            ctx.fillText('(不是按寻址方式分类)', canvas.width/2, 50);
        }

        function animate() {
            animationStep++;
            
            switch(currentDemo) {
                case 'random':
                    animateRandomAccess();
                    break;
                case 'sequential':
                    animateSequentialAccess();
                    break;
                case 'direct':
                    animateDirectAccess();
                    break;
                case 'associative':
                    animateAssociativeAccess();
                    break;
            }
            
            animationId = requestAnimationFrame(animate);
        }

        function startAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animate();
        }

        function resetAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animationStep = 0;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function showStorageType(type) {
            currentDemo = type;
            document.querySelectorAll('.storage-type').forEach(el => {
                el.classList.remove('active');
            });
            event.target.closest('.storage-type').classList.add('active');
            
            resetAnimation();
            startAnimation();
        }

        // 初始化
        startAnimation();
    </script>
</body>
</html>
