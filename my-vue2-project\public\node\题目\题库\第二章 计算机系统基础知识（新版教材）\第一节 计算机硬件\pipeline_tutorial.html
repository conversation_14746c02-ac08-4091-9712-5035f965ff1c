<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指令流水线互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .pipeline-container {
            position: relative;
            height: 200px;
            margin: 40px 0;
            overflow: hidden;
        }

        .stage {
            position: absolute;
            width: 120px;
            height: 80px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stage:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .stage1 { left: 50px; top: 60px; }
        .stage2 { left: 200px; top: 60px; background: linear-gradient(45deg, #2196F3, #1976D2); }
        .stage3 { left: 350px; top: 60px; background: linear-gradient(45deg, #FF9800, #F57C00); }
        .stage4 { left: 500px; top: 60px; background: linear-gradient(45deg, #9C27B0, #7B1FA2); }
        .stage5 { left: 650px; top: 60px; background: linear-gradient(45deg, #F44336, #D32F2F); }

        .arrow {
            position: absolute;
            width: 40px;
            height: 3px;
            background: #666;
            top: 99px;
        }

        .arrow::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid #666;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }

        .arrow1 { left: 170px; }
        .arrow2 { left: 320px; }
        .arrow3 { left: 470px; }
        .arrow4 { left: 620px; }

        .instruction {
            position: absolute;
            width: 60px;
            height: 40px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: bold;
            font-size: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.5s ease;
            opacity: 0;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .explanation {
            background: #f8f9fa;
            border-left: 5px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }

        .formula {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            text-align: center;
            font-size: 1.1em;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .active {
            animation: pulse 1s infinite;
        }

        .timeline {
            margin: 30px 0;
            position: relative;
        }

        .time-grid {
            display: grid;
            grid-template-columns: repeat(20, 1fr);
            gap: 2px;
            margin: 20px 0;
        }

        .time-cell {
            height: 30px;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .occupied {
            background: #4CAF50;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 指令流水线互动教学</h1>
            <p>通过动画和游戏学习计算机流水线原理</p>
        </div>

        <div class="section">
            <h2>📚 什么是指令流水线？</h2>
            <div class="explanation">
                <p><strong>流水线就像工厂的装配线！</strong></p>
                <p>想象一下汽车工厂：第一个工人安装轮子，第二个工人安装发动机，第三个工人喷漆...</p>
                <p>每个工人专门负责一个步骤，多辆汽车可以同时在不同阶段进行组装，大大提高了效率！</p>
            </div>
        </div>

        <div class="section">
            <h2>🔧 我们的5段流水线</h2>
            <div class="pipeline-container" id="pipelineContainer">
                <div class="stage stage1" data-time="1">第1段<br>1△t</div>
                <div class="stage stage2" data-time="3">第2段<br>3△t</div>
                <div class="stage stage3" data-time="1">第3段<br>1△t</div>
                <div class="stage stage4" data-time="2">第4段<br>2△t</div>
                <div class="stage stage5" data-time="1">第5段<br>1△t</div>
                
                <div class="arrow arrow1"></div>
                <div class="arrow arrow2"></div>
                <div class="arrow arrow3"></div>
                <div class="arrow arrow4"></div>
            </div>
            
            <div class="explanation">
                <p><strong>每一段代表指令执行的一个步骤：</strong></p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>第1段：取指令 (1△t)</li>
                    <li>第2段：译码 (3△t) - 最慢的环节</li>
                    <li>第3段：执行 (1△t)</li>
                    <li>第4段：访存 (2△t)</li>
                    <li>第5段：写回 (1△t)</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎮 互动演示</h2>
            <div class="controls">
                <button class="btn" onclick="testFunction()">🧪 测试</button>
                <button class="btn" onclick="startSingleInstruction()">单条指令演示</button>
                <button class="btn" onclick="startPipelineDemo()">流水线演示</button>
                <button class="btn" onclick="calculate100Instructions()">计算100条指令</button>
                <button class="btn" onclick="resetDemo()">重置</button>
            </div>

            <div class="timeline" id="timeline">
                <h3>时间轴演示 (每格代表1△t)</h3>
                <div class="time-grid" id="timeGrid"></div>
            </div>
        </div>

        <div class="section">
            <h2>📊 计算结果</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value" id="totalTime">0</div>
                    <div>总执行时间</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="throughput">0</div>
                    <div>吞吐率</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="efficiency">0%</div>
                    <div>流水线效率</div>
                </div>
            </div>

            <div class="formula" id="formulaDisplay">
                点击"计算100条指令"查看详细计算过程
            </div>
        </div>

        <div class="section">
            <h2>🧠 知识要点</h2>
            <div class="explanation">
                <h3>🔑 关键概念：</h3>
                <p><strong>1. 流水线周期 (△t)：</strong>由最慢的段决定，这里是3△t</p>
                <p><strong>2. 总执行时间：</strong>(所有段时间之和) + (指令数-1) × △t</p>
                <p><strong>3. 吞吐率：</strong>指令数 ÷ 总执行时间</p>

                <h3>💡 为什么这样计算？</h3>
                <p>• 第一条指令需要走完所有5段：1+3+1+2+1 = 8△t</p>
                <p>• 后续99条指令每条只需要等待一个流水线周期：99 × 3△t = 297△t</p>
                <p>• 总时间：8△t + 297△t = 305△t</p>
                <p>• 吞吐率：100 ÷ 305△t ≈ 0.328/△t</p>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 0;
        let isAnimating = false;
        let instructions = [];

        // 测试函数
        function testFunction() {
            alert('JavaScript正在工作！');
            console.log('测试函数被调用');

            // 测试DOM元素
            const stages = document.querySelectorAll('.stage');
            console.log('找到', stages.length, '个流水线段');

            const timeGrid = document.getElementById('timeGrid');
            console.log('时间网格元素:', timeGrid);

            // 简单动画测试
            stages.forEach((stage, index) => {
                setTimeout(() => {
                    stage.classList.add('active');
                    setTimeout(() => {
                        stage.classList.remove('active');
                    }, 500);
                }, index * 200);
            });
        }

        // 初始化时间网格
        function initTimeGrid() {
            const grid = document.getElementById('timeGrid');
            grid.innerHTML = '';
            for (let i = 0; i < 100; i++) {
                const cell = document.createElement('div');
                cell.className = 'time-cell';
                cell.textContent = i + 1;
                grid.appendChild(cell);
            }
        }

        // 单条指令演示
        function startSingleInstruction() {
            if (isAnimating) {
                alert('动画正在进行中，请稍等...');
                return;
            }

            console.log('开始单条指令演示');
            isAnimating = true;
            resetDemo();

            const instruction = createInstruction('指令1');
            const stages = document.querySelectorAll('.stage');
            const times = [1, 3, 1, 2, 1];
            let totalTime = 0;
            let currentStageIndex = 0;

            function animateNextStage() {
                if (currentStageIndex >= stages.length) {
                    instruction.style.opacity = '0';
                    isAnimating = false;
                    console.log('单条指令演示完成');
                    return;
                }

                const stage = stages[currentStageIndex];
                stage.classList.add('active');
                instruction.style.left = (stage.offsetLeft + 30) + 'px';
                instruction.style.top = (stage.offsetTop - 50) + 'px';
                instruction.style.opacity = '1';

                totalTime += times[currentStageIndex];
                updateStats(1, totalTime, 1/totalTime);

                setTimeout(function() {
                    stage.classList.remove('active');
                    currentStageIndex++;
                    animateNextStage();
                }, times[currentStageIndex] * 800);
            }

            animateNextStage();
        }

        // 流水线演示
        function startPipelineDemo() {
            if (isAnimating) {
                alert('动画正在进行中，请稍等...');
                return;
            }

            console.log('开始流水线演示');
            isAnimating = true;
            resetDemo();

            const numInstructions = 3; // 简化为3条指令
            const stages = document.querySelectorAll('.stage');
            const grid = document.getElementById('timeGrid');
            const cells = grid.children;

            // 创建指令
            for (let i = 0; i < numInstructions; i++) {
                instructions.push(createInstruction('I' + (i+1)));
            }

            let currentTime = 0;

            function animateTimeStep() {
                if (currentTime >= 20) { // 限制最大时间
                    // 清理
                    stages.forEach(stage => stage.classList.remove('active'));
                    instructions.forEach(inst => inst.style.opacity = '0');
                    isAnimating = false;
                    console.log('流水线演示完成');
                    return;
                }

                // 高亮当前时间格
                if (currentTime < cells.length) {
                    cells[currentTime].classList.add('occupied');
                }

                // 简单的流水线逻辑：每3个时间单位启动一条新指令
                for (let i = 0; i < numInstructions; i++) {
                    const instrStartTime = i * 3;
                    const relativeTime = currentTime - instrStartTime;

                    if (relativeTime >= 0 && relativeTime < 8) { // 指令总共需要8个时间单位
                        const instruction = instructions[i];
                        let stageIndex = -1;

                        // 确定当前在哪个阶段
                        if (relativeTime < 1) stageIndex = 0;      // 第1段：0-1
                        else if (relativeTime < 4) stageIndex = 1; // 第2段：1-4
                        else if (relativeTime < 5) stageIndex = 2; // 第3段：4-5
                        else if (relativeTime < 7) stageIndex = 3; // 第4段：5-7
                        else if (relativeTime < 8) stageIndex = 4; // 第5段：7-8

                        if (stageIndex >= 0 && stageIndex < stages.length) {
                            const stage = stages[stageIndex];
                            stage.classList.add('active');
                            instruction.style.left = (stage.offsetLeft + 30) + 'px';
                            instruction.style.top = (stage.offsetTop - 50 - i * 15) + 'px'; // 错开显示
                            instruction.style.opacity = '1';
                        }
                    }
                }

                updateStats(numInstructions, currentTime + 1, numInstructions / (currentTime + 1));
                currentTime++;

                setTimeout(animateTimeStep, 500);
            }

            animateTimeStep();
        }

        // 计算100条指令
        function calculate100Instructions() {
            console.log('开始计算100条指令');

            const n = 100;
            const stageSum = 1 + 3 + 1 + 2 + 1; // 8△t
            const pipelineCycle = 3; // 最大的段时间
            const totalTime = stageSum + (n - 1) * pipelineCycle; // 8 + 99*3 = 305△t
            const throughput = n / totalTime;
            const efficiency = (n * stageSum) / (5 * totalTime) * 100;

            updateStats(n, totalTime, throughput, efficiency);

            const formulaElement = document.getElementById('formulaDisplay');
            if (formulaElement) {
                formulaElement.innerHTML = `
                    <h3>📐 详细计算过程：</h3>
                    <p><strong>流水线周期 △t = max(1, 3, 1, 2, 1) = 3△t</strong></p>
                    <p><strong>总执行时间 = (1+3+1+2+1) + (100-1)×3 = 8 + 297 = 305△t</strong></p>
                    <p><strong>吞吐率 = 100 ÷ 305△t ≈ 0.328/△t</strong></p>
                    <p><strong>效率 = (100×8) ÷ (5×305) × 100% ≈ 52.5%</strong></p>
                    <p style="color: red; font-weight: bold;">答案：C (约0.328/△t)</p>
                `;
            }

            alert('计算完成！答案是C：约0.328/△t');
        }

        // 重置演示
        function resetDemo() {
            console.log('重置演示');

            currentStep = 0;
            isAnimating = false;

            // 清除所有活动状态
            const stages = document.querySelectorAll('.stage');
            stages.forEach(stage => {
                stage.classList.remove('active');
            });

            // 移除所有指令元素
            const instructionElements = document.querySelectorAll('.instruction');
            instructionElements.forEach(inst => {
                inst.remove();
            });

            // 清除时间格高亮
            const timeCells = document.querySelectorAll('.time-cell');
            timeCells.forEach(cell => {
                cell.classList.remove('occupied');
            });

            instructions = [];
            updateStats(0, 0, 0);

            // 重置公式显示
            const formulaElement = document.getElementById('formulaDisplay');
            if (formulaElement) {
                formulaElement.innerHTML = '点击"计算100条指令"查看详细计算过程';
            }
        }

        // 创建指令元素
        function createInstruction(text) {
            const instruction = document.createElement('div');
            instruction.className = 'instruction';
            instruction.textContent = text;
            document.getElementById('pipelineContainer').appendChild(instruction);
            return instruction;
        }

        // 更新统计信息
        function updateStats(instrCount, time, throughput, efficiency = 0) {
            document.getElementById('totalTime').textContent = time + '△t';
            document.getElementById('throughput').textContent = throughput.toFixed(3) + '/△t';
            document.getElementById('efficiency').textContent = efficiency.toFixed(1) + '%';
        }

        // 延时函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 页面加载时初始化
        window.onload = function() {
            initTimeGrid();

            // 添加阶段点击事件
            document.querySelectorAll('.stage').forEach((stage, index) => {
                stage.addEventListener('click', () => {
                    const time = stage.dataset.time;
                    alert(`第${index + 1}段：需要${time}△t时间\n这是${time == 3 ? '最慢的' : ''}环节`);
                });
            });
        };
    </script>
</body>
</html>
