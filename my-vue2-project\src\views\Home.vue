<template>
  <div class="home" :class="{ 'monitoring-disabled': isMonitoringMode }">
    <PageHeader />

    <div class="container" :class="{ 'monitoring-overlay': isMonitoringMode }">
      <div class="welcome-section fade-in">
        <h2 class="welcome-title">极简笔记</h2>
        <p class="welcome-text">记录灵感，整理思绪</p>
      </div>

      <div class="hero-container fade-in" style="animation-delay: 0.3s">
        <div class="card hero-card">
          <div class="hero-content">
            <h3 class="hero-subtitle">简约而不简单的笔记体验</h3>
            <p class="hero-description">
              专注于内容创作，支持暗黑模式切换，让记录更加轻松自在。
              <br>立即开始使用，随时随地记录你的想法。
            </p>
            <div class="hero-actions">
              <TextButton 
                type="primary" 
                icon="el-icon-notebook-1" 
                @click="goToNotes"
                class="start-btn"
              >
                开始使用
              </TextButton>
              <TextButton 
                type="default" 
                icon="el-icon-time" 
                @click="goToHistory"
                class="history-btn"
                style="margin-left: 15px;"
              >
                阅读历史
              </TextButton>
            </div>
          </div>
          <div class="hero-image">
            <div class="note-illustration">
              <div class="note-line"></div>
              <div class="note-line"></div>
              <div class="note-line short"></div>
              <div class="note-line medium"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="multimodal-section fade-in" style="animation-delay: 0.4s" @click="goToMultimodalPage">
        <div class="card multimodal-card">
          <div class="multimodal-illustration">
            <div class="mockup-window">
              <div class="mockup-header">
                <span class="dot"></span><span class="dot"></span><span class="dot"></span>
              </div>
              <div class="mockup-content">
                <div class="mockup-line title"></div>
                <div class="mockup-line"></div>
                <div class="mockup-image"><i class="el-icon-picture-outline"></i></div>
                <div class="mockup-line short"></div>
                <div class="mockup-checklist">
                  <i class="el-icon-check"></i>
                  <div class="mockup-line tiny"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="multimodal-content">
            <h3 class="multimodal-title">探索多模态笔记</h3>
            <p class="multimodal-description">
              不仅仅是文字，更是想法的画布。
              <br>
              支持图片、待办事项、代码块等多种形式。
            </p>
            <span class="multimodal-link">
              立即体验 <i class="el-icon-arrow-right"></i>
            </span>
          </div>
        </div>
      </div>

      <!-- New Learning Monitoring Section -->
      <div class="learning-monitoring-section fade-in" style="animation-delay: 0.45s" @click="goToLearningMonitoringPage">
        <div class="card learning-monitoring-card">
          <div class="learning-monitoring-illustration">
            <i class="el-icon-monitor"></i>
          </div>
          <div class="learning-monitoring-content">
            <h3 class="learning-monitoring-title">学习行为监控系统</h3>
            <p class="learning-monitoring-description">
              实时、全面、细致地监控用户学习行为与状态，提升学习效率。
              <br>
              了解你的学习习惯，优化你的学习过程。
            </p>
            <span class="learning-monitoring-link">
              立即体验 <i class="el-icon-arrow-right"></i>
            </span>
          </div>
        </div>
      </div>

      <!-- Floating Window Control Section -->
      <div class="floating-window-section fade-in" style="animation-delay: 0.5s">
        <div class="card floating-window-card">
          <div class="floating-window-illustration">
            <i class="el-icon-s-grid"></i>
          </div>
          <div class="floating-window-content">
            <h3 class="floating-window-title">桌面小程序悬浮窗</h3>
            <p class="floating-window-description">
              便捷的桌面小工具，包含计算器、记事本、计时器等实用功能。
              <br>
              随时打开，提升工作效率。
            </p>
            <div class="floating-window-controls">
              <el-button
                type="primary"
                icon="el-icon-plus"
                size="small"
                @click="openFloatingWindow"
                :loading="floatingWindowLoading"
              >
                打开悬浮窗
              </el-button>
              <el-button
                type="danger"
                icon="el-icon-close"
                size="small"
                @click="closeAllFloatingWindows"
                :disabled="!hasActiveFloatingWindows"
                style="margin-left: 10px;"
              >
                关闭所有
              </el-button>
              <span class="floating-window-status">
                当前活动: {{ activeFloatingWindowCount }} 个
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="features-container fade-in" style="animation-delay: 0.5s">
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">
              <i class="el-icon-edit"></i>
            </div>
            <h4 class="feature-title">简洁创作</h4>
            <p class="feature-text">无干扰的写作环境，专注于内容创作</p>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">
              <i class="el-icon-moon"></i>
            </div>
            <h4 class="feature-title">暗黑模式</h4>
            <p class="feature-text">随时切换明亮/暗黑模式，保护你的眼睛</p>
          </div>
          
          <div class="feature-item">
            <div class="feature-icon">
              <i class="el-icon-upload2"></i>
            </div>
            <h4 class="feature-title">本地存储</h4>
            <p class="feature-text">数据安全存储在本地，随时访问</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import floatingWindowService from '../services/floatingWindowService'

export default {
  name: 'HomePage',

  data() {
    return {
      floatingWindowLoading: false,
      activeFloatingWindowCount: 0
    }
  },

  computed: {
    ...mapState(['darkMode', 'isMonitoringMode']),

    hasActiveFloatingWindows() {
      return this.activeFloatingWindowCount > 0
    }
  },

  methods: {
    ...mapActions(['toggleDarkMode', 'fetchNotes']),

    goToNotes() {
      this.$router.push('/notes')
    },

    goToMultimodalPage() {
      this.$router.push('/multimodal');
    },

    goToHistory() {
      this.$router.push('/history');
    },

    goToLearningMonitoringPage() {
      this.$router.push('/learning-monitoring');
    },

    // 悬浮窗控制方法
    async openFloatingWindow() {
      this.floatingWindowLoading = true

      try {
        const result = await floatingWindowService.createFloatingWindow()

        if (result.success) {
          this.$message.success('悬浮窗已打开')
          this.updateFloatingWindowCount()
        } else {
          this.$message.error(`打开悬浮窗失败: ${result.error}`)
        }
      } catch (error) {
        console.error('打开悬浮窗失败:', error)
        this.$message.error('打开悬浮窗失败')
      } finally {
        this.floatingWindowLoading = false
      }
    },

    async closeAllFloatingWindows() {
      try {
        const result = await floatingWindowService.closeAllFloatingWindows()

        if (result.success) {
          this.$message.success('所有悬浮窗已关闭')
          this.updateFloatingWindowCount()
        } else {
          this.$message.error(`关闭悬浮窗失败: ${result.error}`)
        }
      } catch (error) {
        console.error('关闭悬浮窗失败:', error)
        this.$message.error('关闭悬浮窗失败')
      }
    },

    async updateFloatingWindowCount() {
      try {
        const result = await floatingWindowService.getFloatingWindows()
        if (result.success) {
          this.activeFloatingWindowCount = result.windows.length
        }
      } catch (error) {
        console.error('更新悬浮窗数量失败:', error)
      }
    },

    // 悬浮窗事件处理
    onFloatingWindowCreated() {
      this.updateFloatingWindowCount()
    },

    onFloatingWindowClosed() {
      this.updateFloatingWindowCount()
    }
  },

  mounted() {
    // 预加载笔记数据
    this.fetchNotes()

    // 设置悬浮窗事件监听
    floatingWindowService.on('window-created', this.onFloatingWindowCreated)
    floatingWindowService.on('window-closed', this.onFloatingWindowClosed)

    // 初始化悬浮窗数量
    this.updateFloatingWindowCount()
  },

  beforeDestroy() {
    // 清理事件监听
    floatingWindowService.off('window-created', this.onFloatingWindowCreated)
    floatingWindowService.off('window-closed', this.onFloatingWindowClosed)
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 30px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.welcome-section {
  text-align: center;
  margin-bottom: 60px;
  padding-top: 40px;
}

.welcome-title {
  font-size: 42px;
  font-weight: 300;
  margin-bottom: 15px;
  color: var(--text-color);
  letter-spacing: 1px;
}

.welcome-text {
  font-size: 18px;
  color: var(--text-light);
  font-weight: 300;
  letter-spacing: 0.5px;
}

.hero-container {
  margin-bottom: 80px;
}

.hero-card {
  display: flex;
  padding: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.05);
  border: none;
  min-height: 300px;
}

.multimodal-section {
  margin-bottom: 80px;
  cursor: pointer;
}

.multimodal-card {
  display: flex;
  align-items: center;
  padding: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  background: var(--card-bg);
}

.multimodal-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.08);
  border-color: var(--primary-color);
}

.multimodal-content {
  flex: 1;
  padding: 40px;
}

.multimodal-title {
  font-size: 22px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 15px;
}

.multimodal-description {
  font-size: 15px;
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: 25px;
}

.multimodal-link {
  font-size: 15px;
  color: var(--primary-color);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: gap 0.3s ease;
}

.multimodal-card:hover .multimodal-link {
  gap: 10px;
}

.multimodal-illustration {
  flex: 1;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color-light);
}

.mockup-window {
  width: 100%;
  max-width: 320px;
  border-radius: 8px;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: 0 10px 25px rgba(0,0,0,0.08);
}

.mockup-header {
  height: 30px;
  background-color: var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 10px;
  gap: 6px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(128, 128, 128, 0.3);
}

.mockup-content {
  padding: 20px;
}

.mockup-line {
  height: 8px;
  border-radius: 4px;
  background-color: var(--border-color);
  margin-bottom: 12px;
}
.mockup-line.title {
  width: 60%;
  height: 12px;
  margin-bottom: 20px;
}
.mockup-line.short { width: 70%; }
.mockup-line.tiny { flex: 1; margin-bottom: 0; }

.mockup-image {
  height: 80px;
  width: 100%;
  border-radius: 6px;
  background-color: var(--border-color);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-size: 32px;
}

.mockup-checklist {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--primary-color);
  font-size: 16px;
}

.hero-content {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.hero-subtitle {
  font-size: 24px;
  font-weight: 400;
  margin-bottom: 20px;
  color: var(--text-color);
}

.hero-description {
  font-size: 16px;
  line-height: 1.8;
  color: var(--text-light);
  margin-bottom: 30px;
}

.hero-actions {
  margin-top: 10px;
}

.hero-image {
  flex: 1;
  background-color: rgba(52, 152, 219, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.note-illustration {
  width: 100%;
  max-width: 300px;
  background-color: var(--background-color);
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.note-line {
  height: 12px;
  background-color: var(--border-color);
  border-radius: 6px;
  margin-bottom: 15px;
  opacity: 0.7;
}

.note-line.short {
  width: 60%;
}

.note-line.medium {
  width: 80%;
}

.start-btn {
  font-size: 16px;
  transition: all 0.3s ease;
}

.start-btn:hover {
  transform: translateY(-2px);
}

/* Learning Monitoring Section Styles */
.learning-monitoring-section {
  margin-bottom: 80px;
  cursor: pointer;
}

.learning-monitoring-card {
  display: flex;
  align-items: center;
  padding: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  background: var(--card-bg);
}

.learning-monitoring-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.08);
  border-color: var(--primary-color);
}

.learning-monitoring-content {
  flex: 1;
  padding: 40px;
}

.learning-monitoring-title {
  font-size: 22px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 15px;
}

.learning-monitoring-description {
  font-size: 15px;
  color: var(--text-light);
  line-height: 1.7;
  margin-bottom: 25px;
}

.learning-monitoring-link {
  font-size: 15px;
  color: var(--primary-color);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: gap 0.3s ease;
}

.learning-monitoring-card:hover .learning-monitoring-link {
  gap: 10px;
}

.learning-monitoring-illustration {
  flex: 1;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color-light);
}

.learning-monitoring-illustration i {
  font-size: 80px;
  color: var(--primary-color);
  opacity: 0.7;
}

/* Floating Window Section Styles */
.floating-window-section {
  margin-bottom: 40px;
}

.floating-window-card {
  display: flex;
  align-items: center;
  padding: 0;
  overflow: hidden;
  cursor: default;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.floating-window-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.floating-window-content {
  flex: 2;
  padding: 40px;
}

.floating-window-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 15px;
}

.floating-window-description {
  font-size: 16px;
  color: var(--text-color-secondary);
  line-height: 1.7;
  margin-bottom: 25px;
}

.floating-window-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.floating-window-status {
  font-size: 14px;
  color: var(--text-color-secondary);
  background: var(--background-color-light);
  padding: 5px 12px;
  border-radius: 15px;
  border: 1px solid var(--border-color);
}

.floating-window-illustration {
  flex: 1;
  padding: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color-light);
}

.floating-window-illustration i {
  font-size: 80px;
  color: var(--primary-color);
  opacity: 0.7;
}

.features-container {
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.feature-item {
  text-align: center;
  padding: 30px 20px;
  background-color: var(--card-background);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(52, 152, 219, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.feature-icon i {
  font-size: 24px;
  color: var(--primary-color);
}

.feature-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 15px;
  color: var(--text-color);
}

.feature-text {
  font-size: 14px;
  color: var(--text-light);
  line-height: 1.6;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.8s ease forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 768px) {
  .welcome-title {
    font-size: 32px;
  }
  
  .welcome-text {
    font-size: 16px;
  }
  
  .hero-card, .multimodal-card, .learning-monitoring-card {
    flex-direction: column;
  }
  
  .multimodal-card {
    flex-direction: column-reverse;
  }

  .hero-image {
    padding: 30px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .learning-monitoring-illustration {
    padding: 30px;
  }

  .learning-monitoring-content {
    padding: 30px;
  }
}
</style> 