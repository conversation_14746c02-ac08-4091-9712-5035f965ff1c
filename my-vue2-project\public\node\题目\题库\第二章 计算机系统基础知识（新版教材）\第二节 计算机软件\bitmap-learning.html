<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>位示图（Bitmap）学习系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.5rem;
            color: white;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 1.8rem;
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .concept-card {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .formula-box {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
            color: #8b4513;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #48bb78;
            color: white;
        }

        .highlight {
            background: #ffd700;
            padding: 2px 6px;
            border-radius: 4px;
            animation: pulse 2s infinite;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .bounce {
            animation: bounce 1s ease-in-out;
        }

        .result-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.1rem;
            border: 2px solid #48bb78;
        }

        .explanation {
            background: #f0f9ff;
            border-left: 4px solid #0ea5e9;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
        }

        .question-box {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border: 2px solid #fc8181;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(252, 129, 129, 0.2);
        }

        .question-content p {
            margin-bottom: 15px;
            line-height: 1.6;
            font-size: 1.1rem;
        }

        .options {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }

        .option-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 10px;
        }

        .option {
            background: #f8fafc;
            padding: 10px 15px;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .option:hover {
            background: #e2e8f0;
            transform: translateY(-2px);
        }

        .answer-section {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }

        .correct-answer {
            color: #48bb78;
            font-weight: bold;
            background: #c6f6d5;
            padding: 2px 8px;
            border-radius: 4px;
        }

        .wrong-answer {
            color: #e53e3e;
            font-weight: bold;
            background: #fed7d7;
            padding: 2px 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">位示图（Bitmap）学习系统</h1>
            <p class="subtitle">通过动画和交互理解文件系统中的位示图概念</p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 基础概念</h2>
            <div class="concept-grid">
                <div class="concept-card">
                    <h3>🗂️ 什么是位示图？</h3>
                    <p>位示图是一种用二进制位来记录磁盘块使用情况的数据结构。每一位对应一个物理块，0表示空闲，1表示占用。</p>
                </div>
                <div class="concept-card">
                    <h3>💾 物理块编号</h3>
                    <p>磁盘上的物理块按顺序编号：0、1、2、3、4...，每个块可以存储文件数据。</p>
                </div>
                <div class="concept-card">
                    <h3>🔢 字长概念</h3>
                    <p>系统字长为32位，意味着每个字包含32个二进制位，可以表示32个物理块的状态。</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📝 完整题目</h2>
            <div class="question-box">
                <div class="question-content">
                    <p><strong>题目描述：</strong></p>
                    <p>某文件管理系统在磁盘上建立了位示图（bitmap），记录磁盘的使用情况。若磁盘上物理块的编号依次为：0、1、2、....；系统中的字长为32位，位示图中字的编号依次为：0、1、2、..，每个字中的一个二进制位对应文件存储器上的一个物理块，取值0和1分别表示物理块是空闲或占用。</p>

                    <p><strong>问题：</strong></p>
                    <p>假设操作系统将<span class="highlight">2053号物理块</span>分配给某文件，那么该物理块的使用情况在位示图中编号为（ ）的字中描述，系统应该将（ ）</p>

                    <div class="options">
                        <p><strong>选项：</strong></p>
                        <div class="option-grid">
                            <div class="option">A. 32</div>
                            <div class="option">B. 33</div>
                            <div class="option">C. 64</div>
                            <div class="option">D. 65</div>
                        </div>
                    </div>

                    <div class="answer-section">
                        <p><strong>正确答案：</strong><span class="correct-answer">C (64)</span></p>
                        <p><strong>你的答案：</strong><span class="wrong-answer">B (33)</span></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 题目分析</h2>
            <div class="explanation">
                <strong>解题思路：</strong>要找到2053号物理块在位示图中对应的字编号，需要用物理块号除以字长。
            </div>
            
            <div class="step-indicator">
                <div class="step" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>

            <div class="canvas-container">
                <canvas id="mainCanvas" width="800" height="400"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置</button>
                <button class="btn" onclick="showFormula()">📐 显示公式</button>
                <button class="btn" onclick="interactiveMode()">🎮 交互模式</button>
            </div>

            <div class="formula-box" id="formulaBox" style="display: none;">
                <div>核心公式：<span class="highlight">物理块号 ÷ 字长 = 字编号（向下取整）</span></div>
                <div style="margin-top: 10px;">2053 ÷ 32 = 64.156... → 字编号 = 64</div>
            </div>

            <div class="result-box" id="resultBox" style="display: none;">
                <h3>🎉 答案解析</h3>
                <p><strong>2053号物理块</strong>在位示图中的<strong>第64号字</strong>中描述</p>
                <p>因为：2053 ÷ 32 = 64.156，取整数部分得到64</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('mainCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let isAnimating = false;

        // 动画状态
        let currentStep = 0;
        const steps = ['step1', 'step2', 'step3', 'step4'];

        function updateStepIndicator(step) {
            steps.forEach((id, index) => {
                const element = document.getElementById(id);
                element.classList.remove('active', 'completed');
                if (index < step) {
                    element.classList.add('completed');
                } else if (index === step) {
                    element.classList.add('active');
                }
            });
        }

        function drawBitmap() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#4a5568';
            ctx.textAlign = 'center';
            ctx.fillText('位示图结构演示', canvas.width / 2, 30);
            
            // 绘制字的概念
            const wordWidth = 200;
            const wordHeight = 40;
            const startX = 50;
            const startY = 60;
            
            // 绘制几个字的示例
            for (let i = 0; i < 4; i++) {
                const x = startX + i * (wordWidth + 20);
                const y = startY;
                
                // 字的边框
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, wordWidth, wordHeight);
                
                // 字编号
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText(`字 ${i + 63}`, x + wordWidth / 2, y - 10);
                
                // 绘制32个位
                const bitWidth = wordWidth / 32;
                for (let j = 0; j < 32; j++) {
                    const bitX = x + j * bitWidth;
                    const bitY = y;
                    
                    // 位的背景
                    if (i === 1 && j === 21) { // 2053号块对应的位
                        ctx.fillStyle = '#ffd700';
                    } else {
                        ctx.fillStyle = Math.random() > 0.7 ? '#ff6b6b' : '#51cf66';
                    }
                    ctx.fillRect(bitX, bitY, bitWidth, wordHeight);
                    
                    // 位的边框
                    ctx.strokeStyle = '#e2e8f0';
                    ctx.lineWidth = 0.5;
                    ctx.strokeRect(bitX, bitY, bitWidth, wordHeight);
                }
            }
            
            // 绘制物理块编号对应关系
            ctx.font = '12px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'left';
            ctx.fillText('物理块编号范围:', startX, startY + wordHeight + 20);
            
            for (let i = 0; i < 4; i++) {
                const x = startX + i * (wordWidth + 20);
                const startBlock = (i + 63) * 32;
                const endBlock = startBlock + 31;
                ctx.fillText(`${startBlock}-${endBlock}`, x, startY + wordHeight + 35);
            }
        }

        function animateCalculation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 步骤1：显示问题
            if (animationStep >= 0) {
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.fillStyle = '#4a5568';
                ctx.textAlign = 'center';
                ctx.fillText('问题：2053号物理块在哪个字中？', canvas.width / 2, 50);
                updateStepIndicator(0);
            }
            
            // 步骤2：显示公式
            if (animationStep >= 1) {
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = '#667eea';
                ctx.fillText('公式：物理块号 ÷ 字长 = 字编号', canvas.width / 2, 100);
                updateStepIndicator(1);
            }
            
            // 步骤3：代入数值
            if (animationStep >= 2) {
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#e53e3e';
                ctx.fillText('2053 ÷ 32 = ?', canvas.width / 2, 150);
                updateStepIndicator(2);
            }
            
            // 步骤4：显示计算过程
            if (animationStep >= 3) {
                ctx.font = '18px Microsoft YaHei';
                ctx.fillStyle = '#38a169';
                ctx.fillText('2053 ÷ 32 = 64.15625', canvas.width / 2, 200);
                ctx.fillText('取整数部分：64', canvas.width / 2, 230);
                updateStepIndicator(3);
            }
            
            // 步骤5：显示答案
            if (animationStep >= 4) {
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.fillStyle = '#d69e2e';
                ctx.fillText('答案：第64号字', canvas.width / 2, 280);
                
                // 绘制可视化的位示图
                drawDetailedBitmap();
            }
        }

        function drawDetailedBitmap() {
            const y = 320;
            const wordWidth = 150;
            const wordHeight = 30;
            
            // 绘制第64号字
            ctx.strokeStyle = '#d69e2e';
            ctx.lineWidth = 3;
            ctx.strokeRect(canvas.width / 2 - wordWidth / 2, y, wordWidth, wordHeight);
            
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillStyle = '#d69e2e';
            ctx.textAlign = 'center';
            ctx.fillText('第64号字', canvas.width / 2, y - 10);
            ctx.fillText('包含2053号物理块', canvas.width / 2, y + wordHeight + 20);
            
            // 绘制位
            const bitWidth = wordWidth / 32;
            for (let i = 0; i < 32; i++) {
                const bitX = canvas.width / 2 - wordWidth / 2 + i * bitWidth;
                
                if (i === 21) { // 2053 % 32 = 21
                    ctx.fillStyle = '#ffd700';
                } else {
                    ctx.fillStyle = '#f0f0f0';
                }
                ctx.fillRect(bitX, y, bitWidth, wordHeight);
                
                ctx.strokeStyle = '#ccc';
                ctx.lineWidth = 0.5;
                ctx.strokeRect(bitX, y, bitWidth, wordHeight);
            }
        }

        function startAnimation() {
            if (isAnimating) return;
            isAnimating = true;
            animationStep = 0;
            
            const interval = setInterval(() => {
                animateCalculation();
                animationStep++;
                
                if (animationStep > 4) {
                    clearInterval(interval);
                    isAnimating = false;
                    document.getElementById('resultBox').style.display = 'block';
                    document.getElementById('resultBox').classList.add('bounce');
                }
            }, 1500);
        }

        function resetAnimation() {
            animationStep = 0;
            isAnimating = false;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawBitmap();
            updateStepIndicator(-1);
            document.getElementById('resultBox').style.display = 'none';
            document.getElementById('formulaBox').style.display = 'none';
        }

        function showFormula() {
            const formulaBox = document.getElementById('formulaBox');
            formulaBox.style.display = formulaBox.style.display === 'none' ? 'block' : 'none';
        }

        function interactiveMode() {
            const blockNumber = prompt('请输入一个物理块号（例如：2053）：');
            if (blockNumber && !isNaN(blockNumber)) {
                const wordNumber = Math.floor(parseInt(blockNumber) / 32);
                const bitPosition = parseInt(blockNumber) % 32;
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#4a5568';
                ctx.textAlign = 'center';
                ctx.fillText(`物理块 ${blockNumber} 的计算过程`, canvas.width / 2, 50);
                
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = '#667eea';
                ctx.fillText(`${blockNumber} ÷ 32 = ${(parseInt(blockNumber) / 32).toFixed(3)}`, canvas.width / 2, 100);
                ctx.fillText(`字编号：${wordNumber}`, canvas.width / 2, 130);
                ctx.fillText(`在字中的位置：第 ${bitPosition} 位`, canvas.width / 2, 160);
                
                // 可视化显示
                const y = 200;
                const wordWidth = 200;
                const wordHeight = 40;
                
                ctx.strokeStyle = '#d69e2e';
                ctx.lineWidth = 3;
                ctx.strokeRect(canvas.width / 2 - wordWidth / 2, y, wordWidth, wordHeight);
                
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.fillStyle = '#d69e2e';
                ctx.fillText(`第${wordNumber}号字`, canvas.width / 2, y - 10);
                
                // 绘制位
                const bitWidth = wordWidth / 32;
                for (let i = 0; i < 32; i++) {
                    const bitX = canvas.width / 2 - wordWidth / 2 + i * bitWidth;
                    
                    if (i === bitPosition) {
                        ctx.fillStyle = '#ffd700';
                    } else {
                        ctx.fillStyle = '#f0f0f0';
                    }
                    ctx.fillRect(bitX, y, bitWidth, wordHeight);
                    
                    ctx.strokeStyle = '#ccc';
                    ctx.lineWidth = 0.5;
                    ctx.strokeRect(bitX, y, bitWidth, wordHeight);
                }
                
                ctx.font = '12px Microsoft YaHei';
                ctx.fillStyle = '#d69e2e';
                ctx.fillText(`物理块${blockNumber}对应这一位`, canvas.width / 2, y + wordHeight + 20);
            }
        }

        // 初始化
        drawBitmap();
        updateStepIndicator(-1);
    </script>
</body>
</html>
