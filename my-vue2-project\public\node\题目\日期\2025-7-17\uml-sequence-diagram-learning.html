<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UML顺序图互动学习 - 软考达人</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        #sequenceCanvas {
            border: 2px solid #f0f0f0;
            border-radius: 15px;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #sequenceCanvas:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .explanation {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.8;
            font-size: 1.1rem;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 20px;
            padding: 40px;
            color: white;
            text-align: center;
        }

        .question {
            font-size: 1.4rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
            animation: correctAnswer 0.6s ease-out;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
            animation: wrongAnswer 0.6s ease-out;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 4px;
            transition: width 1s ease;
            width: 0%;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongAnswer {
            0% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
            100% { transform: translateX(0); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 UML顺序图互动学习</h1>
            <p class="subtitle">软考达人 - 零基础也能轻松掌握</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 什么是UML顺序图？</h2>
            
            <div class="explanation">
                <h3>🔍 核心概念</h3>
                <p>UML顺序图是一种<span class="highlight">交互图</span>，用来描述对象之间按时间顺序进行的交互过程。想象一下，就像是一场对话的剧本，记录了谁在什么时候对谁说了什么话！</p>
            </div>

            <div class="canvas-container">
                <canvas id="sequenceCanvas" width="800" height="400"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重新开始</button>
            </div>

            <div class="explanation">
                <h3>🎭 关键要素解析</h3>
                <p><strong>序列片段（Sequence Fragment）</strong>：这是UML2.0中的重要概念，用来表示复杂的交互逻辑，比如循环、选择、并行等。就像编程中的if-else语句或for循环一样！</p>
            </div>
        </div>

        <div class="quiz-section">
            <h2 style="margin-bottom: 30px;">🎮 挑战时间！</h2>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="question">
                在UML2.0中，顺序图用来描述对象之间的交互，其中循环、选择等复杂交互使用（ ）表示？
            </div>

            <div class="options">
                <div class="option" onclick="selectOption(this, false)">
                    <strong>A. 嵌套</strong><br>
                    <small>用于表示层次关系</small>
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>B. 泳道</strong><br>
                    <small>用于活动图中的职责分工</small>
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>C. 组合</strong><br>
                    <small>用于表示整体与部分关系</small>
                </div>
                <div class="option" onclick="selectOption(this, true)">
                    <strong>D. 序列片段</strong><br>
                    <small>专门用于复杂交互逻辑</small>
                </div>
            </div>

            <div id="result" style="margin-top: 30px; font-size: 1.2rem; min-height: 60px;"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('sequenceCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;

        // 绘制基础顺序图
        function drawSequenceDiagram() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制对象
            const objects = [
                { name: '用户', x: 100, y: 50 },
                { name: '系统', x: 300, y: 50 },
                { name: '数据库', x: 500, y: 50 }
            ];

            objects.forEach(obj => {
                // 对象框
                ctx.fillStyle = '#667eea';
                ctx.fillRect(obj.x - 40, obj.y - 20, 80, 40);
                
                // 对象名称
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(obj.name, obj.x, obj.y + 5);
                
                // 生命线
                ctx.strokeStyle = '#ddd';
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(obj.x, obj.y + 20);
                ctx.lineTo(obj.x, 350);
                ctx.stroke();
                ctx.setLineDash([]);
            });

            // 绘制序列片段框
            if (animationStep >= 1) {
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 2;
                ctx.strokeRect(80, 120, 440, 180);
                
                // 序列片段标签
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(80, 120, 60, 25);
                ctx.fillStyle = 'white';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'left';
                ctx.fillText('loop', 85, 137);
            }

            // 绘制消息箭头
            if (animationStep >= 2) {
                drawMessage(100, 300, 150, '登录请求', '#4CAF50');
            }
            if (animationStep >= 3) {
                drawMessage(300, 500, 180, '查询用户', '#2196F3');
            }
            if (animationStep >= 4) {
                drawMessage(500, 300, 210, '返回结果', '#FF9800');
            }
            if (animationStep >= 5) {
                drawMessage(300, 100, 240, '登录成功', '#9C27B0');
            }
        }

        function drawMessage(fromX, toX, y, text, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, y);
            ctx.lineTo(toX, y);
            ctx.stroke();
            
            // 箭头
            const direction = toX > fromX ? 1 : -1;
            ctx.beginPath();
            ctx.moveTo(toX, y);
            ctx.lineTo(toX - 10 * direction, y - 5);
            ctx.lineTo(toX - 10 * direction, y + 5);
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
            
            // 消息文本
            ctx.fillStyle = color;
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, (fromX + toX) / 2, y - 10);
        }

        function startAnimation() {
            animationStep = 0;
            const animate = () => {
                drawSequenceDiagram();
                animationStep++;
                
                if (animationStep <= 5) {
                    animationId = setTimeout(animate, 1000);
                }
            };
            animate();
        }

        function resetAnimation() {
            clearTimeout(animationId);
            animationStep = 0;
            drawSequenceDiagram();
        }

        function selectOption(element, isCorrect) {
            // 禁用所有选项
            const options = document.querySelectorAll('.option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt !== element) {
                    opt.style.opacity = '0.5';
                }
            });

            const resultDiv = document.getElementById('result');
            const progressFill = document.getElementById('progressFill');

            if (isCorrect) {
                element.classList.add('correct');
                resultDiv.innerHTML = `
                    <div style="color: #4CAF50; font-weight: bold;">🎉 恭喜答对了！</div>
                    <div style="margin-top: 10px; font-size: 1rem;">
                        <strong>解析：</strong>序列片段（Sequence Fragment）是UML2.0中专门用来表示复杂交互逻辑的构造，
                        包括循环（loop）、选择（alt）、并行（par）等操作符。
                    </div>
                `;
                progressFill.style.width = '100%';
            } else {
                element.classList.add('wrong');
                resultDiv.innerHTML = `
                    <div style="color: #f44336; font-weight: bold;">❌ 再想想看...</div>
                    <div style="margin-top: 10px; font-size: 1rem;">
                        提示：想想UML2.0中专门用来表示复杂交互逻辑的是什么概念？
                    </div>
                `;
                
                // 3秒后重置
                setTimeout(() => {
                    options.forEach(opt => {
                        opt.style.pointerEvents = 'auto';
                        opt.style.opacity = '1';
                        opt.classList.remove('wrong');
                    });
                    resultDiv.innerHTML = '';
                }, 3000);
            }
        }

        // 初始化
        drawSequenceDiagram();
    </script>
</body>
</html>
