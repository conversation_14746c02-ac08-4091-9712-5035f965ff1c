<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构风格解析</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 25px;
        }
        .question-section, .explanation-section {
            margin-bottom: 30px;
        }
        .question-text {
            font-size: 1.1em;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e8f0fe;
            border-left: 5px solid #3498db;
            border-radius: 5px;
        }
        .options {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .options li {
            background-color: #f9f9f9;
            margin-bottom: 10px;
            padding: 12px 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .options li:hover {
            background-color: #eef;
        }
        .options li.correct {
            background-color: #d4edda;
            border-color: #28a745;
            font-weight: bold;
        }
        .answer-reveal {
            margin-top: 20px;
            font-size: 1.1em;
            font-weight: bold;
            color: #28a745;
        }
        .concept-card {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .canvas-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
        }
        canvas {
            border: 2px solid #34495e;
            background-color: #ecf0f1;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .controls button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #2980b9;
        }
        .explanation-footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px dashed #ccc;
            font-style: italic;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件架构风格解析</h1>

        <div class="question-section">
            <h2>题目回顾</h2>
            <p class="question-text">
                软件架构风格描述某一特定领域中的系统组织方式和惯用模式，反映了领域中众多系统所共有的（<span id="blank1-text">结构和语义</span>）特征。<br>
                对于语音识别、知识推理等问题复杂、求解过程不确定的这一类软件系统，通常会采用（<span id="blank2"></span>）架构风格。<br>
                对于因数据输入某个构件，经过内部处理，产生数据输出的系统，通常会采用（<span id="blank3"></span>）架构风格。
            </p>
            <h3>问题：对于求解过程不确定的系统，通常采用哪种架构风格？</h3>
            <ul class="options" id="question-options">
                <li data-value="管道-过滤器">A 管道-过滤器</li>
                <li data-value="解释器">B 解释器</li>
                <li data-value="黑板">C 黑板</li>
                <li data-value="过程控制">D 过程控制</li>
            </ul>
            <p class="answer-reveal">正确答案：C (黑板)</p>
        </div>

        <div class="explanation-section">
            <h2>知识点深度解析</h2>

            <div class="concept-card">
                <h3>什么是软件架构风格？</h3>
                <p>软件架构风格，就像是建筑领域的"设计模式"或"蓝图"。它描述了在特定应用领域中，系统是如何被组织起来的通用方式和惯用模式。它捕捉了该领域中许多系统所共有的结构和语义方面的特征。简单来说，它是一套指导原则，告诉我们如何设计和组织软件的不同部分，以解决某一类特定的问题。</p>
            </div>

            <div class="concept-card">
                <h3>黑板架构风格 (Blackboard Architecture)</h3>
                <p><strong>核心思想：</strong> 黑板架构风格主要用于解决那些问题定义不清晰、求解过程不确定、没有固定算法或步骤的问题。它模拟了人类专家在黑板上共同解决问题的过程。</p>
                <ul>
                    <li><strong>黑板 (Blackboard)：</strong> 一个全局的共享数据区，所有解决问题的中间状态和最终结果都存储在这里。知识源通过读取黑板上的信息来获取当前问题状态，并写入新信息来推进解决方案。</li>
                    <li><strong>知识源 (Knowledge Sources, KS)：</strong> 独立、模块化的程序单元，每个知识源都包含解决问题所需的一部分特定知识。它们只关心自己能处理的问题部分，当黑板上的数据满足其触发条件时，它们就会被激活并执行操作。知识源之间不直接通信。</li>
                    <li><strong>控制组件 (Control Component)：</strong> 负责监控黑板的状态，决定哪个知识源应该在何时被激活并执行。它管理着整个求解过程的协调和推进。</li>
                </ul>
                <p><strong>适用场景：</strong> 语音识别、图像识别、信号处理、知识推理、专家系统、AI问题求解等。这些问题的特点是，达到最终解决方案的路径不唯一，可能需要多次尝试和迭代。</p>
                <div class="canvas-container">
                    <h4>黑板架构风格交互演示</h4>
                    <canvas id="blackboardCanvas" width="700" height="400"></canvas>
                    <div class="controls">
                        <button id="startBlackboard">开始演示</button>
                        <button id="resetBlackboard">重置演示</button>
                    </div>
                </div>
            </div>

            <div class="concept-card">
                <h3>管道-过滤器架构风格 (Pipe-Filter Architecture)</h3>
                <p><strong>核心思想：</strong> 管道-过滤器架构风格是一种面向数据流的体系结构。它将系统的处理过程分解为一系列独立的、可复用的处理单元（过滤器），这些单元通过数据流（管道）连接起来。数据从一个过滤器流向另一个过滤器，每个过滤器对数据进行特定的转换，最终得到处理结果。</p>
                <ul>
                    <li><strong>过滤器 (Filter)：</strong> 一个独立的计算单元，接收来自输入管道的数据，对其进行处理（转换、增强、筛选等），然后将处理后的数据发送到输出管道。过滤器之间是独立的，彼此不知道对方的存在，只通过数据进行交互。</li>
                    <li><strong>管道 (Pipe)：</strong> 连接两个或多个过滤器的数据通道。它负责在过滤器之间传递数据，通常以流的形式。管道是无状态的，只负责数据传输。</li>
                </ul>
                <p><strong>适用场景：</strong> 编译器设计（词法分析、语法分析、语义分析、代码生成）、数据转换工具、信号处理、ETL（提取、转换、加载）系统、批处理系统等。这些场景的特点是数据处理流程固定，且数据流向是线性的。</p>
                <div class="canvas-container">
                    <h4>管道-过滤器架构风格交互演示</h4>
                    <canvas id="pipeFilterCanvas" width="700" height="400"></canvas>
                    <div class="controls">
                        <button id="startPipeFilter">开始演示</button>
                        <button id="resetPipeFilter">重置演示</button>
                    </div>
                </div>
            </div>

            <div class="explanation-footer">
                希望通过这些解释和演示，您能更好地理解这两种重要的软件架构风格！
            </div>
        </div>
    </div>

    <script>
        // JavaScript for interactive elements and Canvas animations will go here
        // Blank 2 should be filled with "黑板"
        document.getElementById('blank2').textContent = '黑板';
        // Blank 3 should be filled with "管道-过滤器"
        document.getElementById('blank3').textContent = '管道-过滤器';

        // --- Question Options Interactivity ---
        const questionOptions = document.getElementById('question-options');
        questionOptions.addEventListener('click', (event) => {
            const target = event.target;
            if (target.tagName === 'LI') {
                // Remove previous correct highlight if any
                const currentCorrect = questionOptions.querySelector('.correct');
                if (currentCorrect) {
                    currentCorrect.classList.remove('correct');
                }

                // Add correct class to the correct answer
                const correctAnswer = questionOptions.querySelector('li[data-value="黑板"]');
                if (correctAnswer) {
                    correctAnswer.classList.add('correct');
                }

                // Optionally, give immediate feedback
                if (target.dataset.value === "黑板") {
                    // alert("恭喜你，回答正确！");
                } else {
                    // alert("很遗憾，答案不正确。请仔细阅读下面的解释。");
                }
            }
        });

        // --- Blackboard Canvas Animation ---
        const blackboardCanvas = document.getElementById('blackboardCanvas');
        const blackboardCtx = blackboardCanvas.getContext('2d');
        const startBlackboardBtn = document.getElementById('startBlackboard');
        const resetBlackboardBtn = document.getElementById('resetBlackboard');

        let blackboardAnimationId;
        let knowledgeSources = [];
        let blackboardData = [];
        let blackboardState = 'idle';
        let stepCount = 0;

        function drawBlackboardScene() {
            blackboardCtx.clearRect(0, 0, blackboardCanvas.width, blackboardCanvas.height);

            // Draw Blackboard
            blackboardCtx.fillStyle = '#4a69bd';
            blackboardCtx.fillRect(150, 50, 400, 250);
            blackboardCtx.strokeStyle = '#2c3e50';
            blackboardCtx.lineWidth = 3;
            blackboardCtx.strokeRect(150, 50, 400, 250);
            blackboardCtx.fillStyle = 'white';
            blackboardCtx.font = '20px Arial';
            blackboardCtx.textAlign = 'center';
            blackboardCtx.fillText('黑板 (共享数据区)', 350, 80);

            // Draw Knowledge Sources
            knowledgeSources.forEach(ks => {
                blackboardCtx.fillStyle = ks.color;
                blackboardCtx.beginPath();
                blackboardCtx.arc(ks.x, ks.y, 40, 0, Math.PI * 2);
                blackboardCtx.fill();
                blackboardCtx.strokeStyle = '#2c3e50';
                blackboardCtx.lineWidth = 2;
                blackboardCtx.stroke();
                blackboardCtx.fillStyle = 'white';
                blackboardCtx.font = '14px Arial';
                blackboardCtx.fillText(ks.name, ks.x, ks.y - 10);
                blackboardCtx.fillText('知识源', ks.x, ks.y + 10);
            });

            // Draw Data on Blackboard
            blackboardData.forEach(data => {
                blackboardCtx.fillStyle = data.color;
                blackboardCtx.beginPath();
                blackboardCtx.arc(data.x, data.y, 10, 0, Math.PI * 2);
                blackboardCtx.fill();
                blackboardCtx.fillStyle = 'black';
                blackboardCtx.font = '12px Arial';
                blackboardCtx.fillText(data.text, data.x, data.y + 5);
            });

            // Draw Control Component
            blackboardCtx.fillStyle = '#e67e22';
            blackboardCtx.fillRect(250, 320, 200, 50);
            blackboardCtx.strokeStyle = '#d35400';
            blackboardCtx.lineWidth = 3;
            blackboardCtx.strokeRect(250, 320, 200, 50);
            blackboardCtx.fillStyle = 'white';
            blackboardCtx.font = '18px Arial';
            blackboardCtx.fillText('控制组件', 350, 350);

            // Draw connecting lines
            blackboardCtx.strokeStyle = '#7f8c8d';
            blackboardCtx.lineWidth = 2;
            blackboardCtx.beginPath();
            blackboardCtx.moveTo(knowledgeSources[0].x, knowledgeSources[0].y + 40);
            blackboardCtx.lineTo(250, 320); // To control
            blackboardCtx.moveTo(knowledgeSources[1].x, knowledgeSources[1].y + 40);
            blackboardCtx.lineTo(350, 320); // To control
            blackboardCtx.moveTo(knowledgeSources[2].x, knowledgeSources[2].y + 40);
            blackboardCtx.lineTo(450, 320); // To control

            blackboardCtx.moveTo(250, 320);
            blackboardCtx.lineTo(250, 300); // Control to blackboard
            blackboardCtx.moveTo(350, 320);
            blackboardCtx.lineTo(350, 300);
            blackboardCtx.moveTo(450, 320);
            blackboardCtx.lineTo(450, 300);

            blackboardCtx.stroke();
        }

        function initBlackboardScene() {
            knowledgeSources = [
                { x: 70, y: 150, name: 'KS A', color: '#1abc9c' },
                { x: 350, y: 350, name: 'KS B', color: '#3498db' }, // Moved KS B to avoid overlap with blackboard data and control
                { x: 630, y: 150, name: 'KS C', color: '#9b59b6' }
            ];
            blackboardData = [
                { x: 250, y: 150, text: '问题1', color: 'red', stage: 0 },
                { x: 450, y: 200, text: '问题2', color: 'blue', stage: 0 }
            ];
            blackboardState = 'idle';
            stepCount = 0;
            drawBlackboardScene();
        }

        function animateBlackboard() {
            if (blackboardState === 'running') {
                drawBlackboardScene(); // Redraw
                stepCount++;

                if (stepCount % 50 === 0) { // Every 50 frames, simulate a step
                    // Simulate KS A processing Problem 1
                    let problem1 = blackboardData.find(d => d.text === '问题1' && d.stage === 0);
                    if (problem1) {
                        blackboardCtx.fillStyle = 'black';
                        blackboardCtx.font = '14px Arial';
                        blackboardCtx.fillText('KS A 读取问题1', knowledgeSources[0].x, knowledgeSources[0].y + 60);
                        problem1.stage = 1; // Mark as processed by KS A
                    } else {
                        let result1 = blackboardData.find(d => d.text === '结果1' && d.stage === 0);
                        if (!result1) {
                             blackboardData.push({ x: 250, y: 200, text: '结果1', color: 'green', stage: 0 });
                        }
                    }

                    // Simulate KS C processing Problem 2
                    let problem2 = blackboardData.find(d => d.text === '问题2' && d.stage === 0);
                    if (problem2) {
                        blackboardCtx.fillStyle = 'black';
                        blackboardCtx.font = '14px Arial';
                        blackboardCtx.fillText('KS C 读取问题2', knowledgeSources[2].x, knowledgeSources[2].y + 60);
                        problem2.stage = 1; // Mark as processed by KS C
                    } else {
                        let result2 = blackboardData.find(d => d.text === '结果2' && d.stage === 0);
                        if (!result2) {
                             blackboardData.push({ x: 450, y: 150, text: '结果2', color: 'purple', stage: 0 });
                        }
                    }

                    // Simulate KS B combining results
                    let result1_processed = blackboardData.find(d => d.text === '结果1' && d.stage === 0);
                    let result2_processed = blackboardData.find(d => d.text === '结果2' && d.stage === 0);
                    if (result1_processed && result2_processed) {
                        blackboardCtx.fillStyle = 'black';
                        blackboardCtx.font = '14px Arial';
                        blackboardCtx.fillText('KS B 整合结果', knowledgeSources[1].x, knowledgeSources[1].y - 60);
                        let finalSolution = blackboardData.find(d => d.text === '最终方案');
                        if (!finalSolution) {
                            blackboardData.push({ x: 350, y: 250, text: '最终方案', color: 'orange', stage: 0 });
                        }
                    }
                }

                blackboardAnimationId = requestAnimationFrame(animateBlackboard);
            }
        }

        startBlackboardBtn.addEventListener('click', () => {
            if (blackboardState !== 'running') {
                blackboardState = 'running';
                animateBlackboard();
            }
        });

        resetBlackboardBtn.addEventListener('click', () => {
            cancelAnimationFrame(blackboardAnimationId);
            initBlackboardScene();
            blackboardState = 'idle';
        });

        initBlackboardScene(); // Initialize scene on page load

        // --- Pipe-Filter Canvas Animation ---
        const pipeFilterCanvas = document.getElementById('pipeFilterCanvas');
        const pipeFilterCtx = pipeFilterCanvas.getContext('2d');
        const startPipeFilterBtn = document.getElementById('startPipeFilter');
        const resetPipeFilterBtn = document.getElementById('resetPipeFilter');

        let pipeFilterAnimationId;
        let filters = [];
        let dataPacket = null;
        let packetProgress = 0;
        let currentFilterIndex = 0;
        let pipeFilterState = 'idle';

        function drawPipeFilterScene() {
            pipeFilterCtx.clearRect(0, 0, pipeFilterCanvas.width, pipeFilterCanvas.height);

            // Draw Filters
            filters.forEach((filter, index) => {
                pipeFilterCtx.fillStyle = filter.color;
                pipeFilterCtx.fillRect(filter.x, filter.y, filter.width, filter.height);
                pipeFilterCtx.strokeStyle = '#2c3e50';
                pipeFilterCtx.lineWidth = 2;
                pipeFilterCtx.strokeRect(filter.x, filter.y, filter.width, filter.height);
                pipeFilterCtx.fillStyle = 'white';
                pipeFilterCtx.font = '16px Arial';
                pipeFilterCtx.textAlign = 'center';
                pipeFilterCtx.fillText(filter.name, filter.x + filter.width / 2, filter.y + filter.height / 2 + 5);
            });

            // Draw Pipes
            pipeFilterCtx.strokeStyle = '#7f8c8d';
            pipeFilterCtx.lineWidth = 5;
            pipeFilterCtx.lineCap = 'round';
            for (let i = 0; i < filters.length - 1; i++) {
                pipeFilterCtx.beginPath();
                pipeFilterCtx.moveTo(filters[i].x + filters[i].width, filters[i].y + filters[i].height / 2);
                pipeFilterCtx.lineTo(filters[i + 1].x, filters[i + 1].y + filters[i + 1].height / 2);
                pipeFilterCtx.stroke();
            }

            // Draw Data Packet
            if (dataPacket) {
                pipeFilterCtx.fillStyle = dataPacket.color;
                pipeFilterCtx.beginPath();
                pipeFilterCtx.arc(dataPacket.x, dataPacket.y, dataPacket.radius, 0, Math.PI * 2);
                pipeFilterCtx.fill();
                pipeFilterCtx.fillStyle = 'white';
                pipeFilterCtx.font = '12px Arial';
                pipeFilterCtx.fillText(dataPacket.text, dataPacket.x, dataPacket.y + 5);
            }
        }

        function initPipeFilterScene() {
            filters = [
                { x: 50, y: 175, width: 100, height: 50, name: '过滤器 A', color: '#2ecc71' },
                { x: 250, y: 175, width: 100, height: 50, name: '过滤器 B', color: '#e74c3c' },
                { x: 450, y: 175, width: 100, height: 50, name: '过滤器 C', color: '#f1c40f' }
            ];
            dataPacket = { x: 0, y: filters[0].y + filters[0].height / 2, radius: 15, text: '原始数据', color: '#3498db' };
            packetProgress = 0;
            currentFilterIndex = 0;
            pipeFilterState = 'idle';
            drawPipeFilterScene();
        }

        function animatePipeFilter() {
            if (pipeFilterState === 'running') {
                drawPipeFilterScene();

                if (currentFilterIndex < filters.length) {
                    const currentFilter = filters[currentFilterIndex];
                    let startX, startY, endX, endY;

                    if (currentFilterIndex === 0) { // Moving into first filter
                        startX = 0;
                        startY = filters[0].y + filters[0].height / 2;
                        endX = currentFilter.x + currentFilter.width / 2;
                        endY = currentFilter.y + currentFilter.height / 2;
                    } else { // Moving from previous filter to current filter
                        const prevFilter = filters[currentFilterIndex - 1];
                        startX = prevFilter.x + prevFilter.width / 2;
                        startY = prevFilter.y + prevFilter.height / 2;
                        endX = currentFilter.x + currentFilter.width / 2;
                        endY = currentFilter.y + currentFilter.height / 2;
                    }

                    // Simulate movement to filter
                    if (packetProgress < 1) {
                        dataPacket.x = startX + (endX - startX) * packetProgress;
                        dataPacket.y = startY + (endY - startY) * packetProgress;
                        packetProgress += 0.01; // Speed of movement
                    } else {
                        // Data inside filter, simulate processing
                        pipeFilterCtx.fillStyle = 'black';
                        pipeFilterCtx.font = '14px Arial';
                        pipeFilterCtx.fillText(`过滤器${String.fromCharCode(65 + currentFilterIndex)} 处理中...`, currentFilter.x + currentFilter.width / 2, currentFilter.y - 20);

                        // Simulate data transformation after a delay
                        if (packetProgress >= 1.5) { // After some processing time
                            dataPacket.color = `hsl(${Math.random() * 360}, 70%, 50%)`; // Change color
                            dataPacket.text = `数据处理(${String.fromCharCode(65 + currentFilterIndex)})`;
                            currentFilterIndex++;
                            packetProgress = 0; // Reset progress for next pipe/filter
                        } else {
                            packetProgress += 0.01;
                        }
                    }
                } else {
                    // Animation complete
                    pipeFilterState = 'finished';
                    cancelAnimationFrame(pipeFilterAnimationId);
                    pipeFilterCtx.fillStyle = 'black';
                    pipeFilterCtx.font = '24px Arial';
                    pipeFilterCtx.fillText('处理完成!', pipeFilterCanvas.width / 2, pipeFilterCanvas.height / 2);
                }

                if (pipeFilterState === 'running') {
                    pipeFilterAnimationId = requestAnimationFrame(animatePipeFilter);
                }
            }
        }

        startPipeFilterBtn.addEventListener('click', () => {
            if (pipeFilterState !== 'running') {
                pipeFilterState = 'running';
                animatePipeFilter();
            }
        });

        resetPipeFilterBtn.addEventListener('click', () => {
            cancelAnimationFrame(pipeFilterAnimationId);
            initPipeFilterScene();
            pipeFilterState = 'idle';
        });

        initPipeFilterScene(); // Initialize scene on page load

    </script>
</body>
</html> 