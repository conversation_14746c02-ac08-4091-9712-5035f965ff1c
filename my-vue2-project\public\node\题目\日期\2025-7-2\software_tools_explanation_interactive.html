<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件工具分类 - 交互式学习</title>
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--light-color);
            color: var(--dark-color);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        .container {
            width: 100%;
            max-width: 900px;
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 1.5em;
        }

        .quiz-container {
            padding: 30px;
        }

        #question {
            font-size: 1.4em;
            margin-bottom: 25px;
            color: #333;
            line-height: 1.6;
        }

        .options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .option {
            background-color: var(--light-color);
            border: 2px solid #eee;
            border-radius: 10px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            font-size: 1.1em;
        }
        
        .option:hover {
            transform: translateX(5px);
            border-color: var(--primary-color);
        }

        .option.correct {
            background-color: #e9f7ef;
            border-color: var(--success-color);
            color: var(--success-color);
            font-weight: bold;
        }
        
        .option.incorrect {
            background-color: #fdecea;
            border-color: var(--danger-color);
            color: var(--danger-color);
        }

        .option.selected {
             border-color: var(--primary-color);
             box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
        }
        
        .option-letter {
            font-weight: bold;
            margin-right: 15px;
            width: 25px;
            height: 25px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            border: 2px solid;
            border-radius: 50%;
        }

        .explanation-container {
            display: none;
            padding: 30px;
            background-color: #fdfdfd;
            border-top: 1px solid #eee;
            animation: fadeIn 0.8s ease-in-out;
        }

        .explanation-container h2 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .explanation-text {
             font-size: 1.1em;
             line-height: 1.8;
             margin-bottom: 20px;
        }

        #canvas-container {
            text-align: center;
        }

        canvas {
            background: #fff;
            border-radius: 8px;
            border: 1px solid #ddd;
            width: 100%;
            max-width: 800px;
            height: auto;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

    </style>
</head>
<body>

    <div class="container">
        <div class="header">软件工程知识小课堂</div>
        <div class="quiz-container">
            <div id="question">在软件系统工具中，软件评价工具属于 ( )。</div>
            <div class="options">
                <div class="option" data-answer="A">
                    <span class="option-letter">A</span>
                    <span class="option-text">逆向工程工具</span>
                </div>
                <div class="option" data-answer="B">
                     <span class="option-letter">B</span>
                    <span class="option-text">开发信息库工具</span>
                </div>
                <div class="option" data-answer="C">
                     <span class="option-letter">C</span>
                    <span class="option-text">编码与排错工具</span>
                </div>
                <div class="option" data-answer="D">
                     <span class="option-letter">D</span>
                    <span class="option-text">软件管理和软件支持工具</span>
                </div>
            </div>
        </div>
        <div class="explanation-container" id="explanation">
            <h2>知识点解析：软件工具的分类</h2>
            <div class="explanation-text">
                你好！为了更好地理解这个问题，我们可以把种类繁多的软件工具想象成一个大工具箱。根据工具在项目中的用途，我们可以把它们分成几个主要类别。这个动画将为你生动地展示这个分类过程！
            </div>
            <div id="canvas-container">
                <canvas id="canvas" width="800" height="450"></canvas>
            </div>
        </div>
    </div>

    <script>
        const options = document.querySelectorAll('.option');
        const explanationDiv = document.getElementById('explanation');
        const correctAnswer = 'D';
        let answered = false;

        options.forEach(option => {
            option.addEventListener('click', () => {
                if (answered) return;
                answered = true;

                const selectedAnswer = option.getAttribute('data-answer');
                
                // 标记所有选项
                options.forEach(opt => {
                    if (opt.getAttribute('data-answer') === correctAnswer) {
                        opt.classList.add('correct');
                    } else {
                        opt.classList.add('incorrect');
                    }
                });
                
                // 如果选错了，短暂高亮自己的错误选项
                if (selectedAnswer !== correctAnswer) {
                    option.classList.add('selected');
                }

                // 显示解释区域
                setTimeout(() => {
                    explanationDiv.style.display = 'block';
                    window.scrollTo({
                        top: explanationDiv.offsetTop,
                        behavior: 'smooth'
                    });
                    initCanvasAnimation();
                }, 800);
            });
        });

        function initCanvasAnimation() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            const nodes = {
                root: { x: 400, y: 50, text: '软件系统工具', children: ['dev', 'maint', 'mgmt'], alpha: 0, finalAlpha: 1 },
                dev: { x: 150, y: 180, text: '软件开发工具', children: ['req', 'design', 'code'], alpha: 0, finalAlpha: 1 },
                maint: { x: 400, y: 180, text: '软件维护工具', children: ['version', 'reverse'], alpha: 0, finalAlpha: 1 },
                mgmt: { x: 650, y: 180, text: '软件管理和支持工具', children: ['project', 'config', 'eval'], isTargetBranch: true, alpha: 0, finalAlpha: 1 },
                req: { x: 100, y: 320, text: '需求分析工具', alpha: 0, finalAlpha: 0.7 },
                design: { x: 200, y: 320, text: '设计工具', alpha: 0, finalAlpha: 0.7 },
                code: { x: 150, y: 400, text: '编码与排错工具', alpha: 0, finalAlpha: 0.7 },
                version: { x: 350, y: 320, text: '版本控制工具', alpha: 0, finalAlpha: 0.7 },
                reverse: { x: 450, y: 320, text: '逆向工程工具', alpha: 0, finalAlpha: 0.7 },
                project: { x: 600, y: 320, text: '项目管理工具', isTargetBranch: true, alpha: 0, finalAlpha: 0.7 },
                config: { x: 700, y: 320, text: '配置管理工具', isTargetBranch: true, alpha: 0, finalAlpha: 0.7 },
                eval: { x: 650, y: 400, text: '软件评价工具', isTarget: true, alpha: 0, finalAlpha: 1 }
            };

            function drawNode(node) {
                ctx.globalAlpha = node.alpha;
                const isHighlighted = node.isTarget || node.isTargetBranch;
                ctx.fillStyle = isHighlighted ? '#fff' : '#f0f8ff';
                ctx.strokeStyle = isHighlighted ? '#28a745' : '#007bff';
                ctx.lineWidth = isHighlighted ? 3 : 2;
                
                const textWidth = ctx.measureText(node.text).width;
                const rectWidth = textWidth + 20;
                const rectHeight = 35;
                ctx.fillRect(node.x - rectWidth / 2, node.y - rectHeight / 2, rectWidth, rectHeight);
                ctx.strokeRect(node.x - rectWidth / 2, node.y - rectHeight / 2, rectWidth, rectHeight);

                ctx.fillStyle = isHighlighted ? '#28a745' : '#333';
                ctx.font = node.isTarget ? 'bold 16px var(--font-family)' : '16px var(--font-family)';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(node.text, node.x, node.y);
            }

            function drawLine(from, to, isTarget) {
                const alpha = Math.min(from.alpha, to.alpha);
                 if (alpha === 0) return;
                ctx.globalAlpha = alpha;
                ctx.beginPath();
                ctx.moveTo(from.x, from.y);
                ctx.lineTo(to.x, to.y);
                ctx.strokeStyle = isTarget ? '#28a745' : '#aaa';
                ctx.lineWidth = isTarget ? 2.5 : 1.5;
                ctx.stroke();
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                frame++;

                // Animate nodes appearing
                for (const key in nodes) {
                    const node = nodes[key];
                    if (node.alpha < node.finalAlpha) {
                        node.alpha += 0.02;
                    }
                }

                const animationOrder = ['root', 'dev', 'maint', 'mgmt', 'req', 'design', 'code', 'version', 'reverse', 'project', 'config', 'eval'];
                const delay = 30; // frames per step
                
                animationOrder.forEach((key, index) => {
                    if (frame > index * delay) {
                         if (nodes[key].alpha < nodes[key].finalAlpha) {
                            nodes[key].alpha += 0.05;
                         }
                    }
                });

                // Draw lines
                for (const key in nodes) {
                    const parentNode = nodes[key];
                    if (parentNode.children) {
                        parentNode.children.forEach(childKey => {
                            const childNode = nodes[childKey];
                            const isTarget = parentNode.isTargetBranch || childNode.isTarget;
                            drawLine(parentNode, childNode, isTarget);
                        });
                    }
                }

                // Draw nodes
                for (const key in nodes) {
                    drawNode(nodes[key]);
                }
                
                ctx.globalAlpha = 1;
                requestAnimationFrame(animate);
            }

            animate();
        }

    </script>
</body>
</html> 