<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inter- 词缀故事：星际外交官的连接之旅</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 星空背景效果 */
        .starfield {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 3s infinite ease-in-out;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* 连接线动画 */
        .connection-lines {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2;
        }

        .connection-line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00d4ff, transparent);
            animation: connectionFlow 4s infinite linear;
            opacity: 0.6;
        }

        @keyframes connectionFlow {
            0% { transform: translateX(-100%); opacity: 0; }
            50% { opacity: 0.8; }
            100% { transform: translateX(100vw); opacity: 0; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
            z-index: 10;
        }

        .title {
            text-align: center;
            color: #00d4ff;
            font-size: 3.2rem;
            margin-bottom: 15px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            animation: fadeInDown 1.5s ease-out;
            position: relative;
            font-weight: 200;
            letter-spacing: 2px;
        }

        .title::before {
            content: '🌌';
            position: absolute;
            left: -100px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2.8rem;
            animation: orbit 4s infinite linear;
        }

        .title::after {
            content: '🛸';
            position: absolute;
            right: -100px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2.8rem;
            animation: orbit 4s infinite linear reverse;
        }

        @keyframes orbit {
            0% { transform: translateY(-50%) rotate(0deg) translateX(20px) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg) translateX(20px) rotate(-360deg); }
        }

        .subtitle {
            text-align: center;
            color: rgba(0, 212, 255, 0.8);
            font-size: 1.5rem;
            margin-bottom: 60px;
            animation: fadeInUp 1.5s ease-out 0.3s both;
            font-weight: 300;
            letter-spacing: 1px;
        }

        .story-canvas {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 30px;
            box-shadow: 
                0 0 50px rgba(0, 212, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            margin-bottom: 60px;
            overflow: hidden;
            animation: slideInUp 1.5s ease-out 0.6s both;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.3);
            position: relative;
        }

        .mission-status {
            position: absolute;
            top: 25px;
            left: 25px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1rem;
            box-shadow: 0 5px 20px rgba(0, 212, 255, 0.3);
            z-index: 100;
            animation: pulse 2s infinite;
        }

        canvas {
            display: block;
            width: 100%;
            height: 550px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 35px;
            margin-bottom: 70px;
            animation: fadeIn 1.5s ease-out 1.5s both;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            padding: 22px 45px;
            border-radius: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
            position: relative;
            overflow: hidden;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.8s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 50px rgba(0, 212, 255, 0.5);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        .btn:active {
            transform: translateY(-5px) scale(1.02);
        }

        .explanation {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 30px;
            padding: 60px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            margin-bottom: 50px;
            animation: slideInUp 1.5s ease-out 1.2s both;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        .explanation h2 {
            color: #00d4ff;
            margin-bottom: 35px;
            font-size: 2.2rem;
            position: relative;
            padding-left: 40px;
            font-weight: 300;
        }

        .explanation h2::before {
            content: '🔗';
            position: absolute;
            left: 0;
            top: 0;
            font-size: 2rem;
            animation: pulse 2s infinite;
        }

        .explanation p {
            color: rgba(255, 255, 255, 0.9);
            line-height: 2.2;
            font-size: 1.3rem;
            margin-bottom: 30px;
        }

        .word-card {
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #006699 100%);
            color: white;
            padding: 35px;
            border-radius: 25px;
            margin: 30px 0;
            cursor: pointer;
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform: translateY(50px);
            opacity: 0;
            position: relative;
            overflow: hidden;
        }

        .word-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transform: translateX(-100%);
            transition: transform 1s;
        }

        .word-card:hover::before {
            transform: translateX(100%);
        }

        .word-card.show {
            transform: translateY(0);
            opacity: 1;
        }

        .word-card:hover {
            transform: scale(1.05) translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 212, 255, 0.4);
        }

        .prefix {
            font-size: 2rem;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .meaning {
            font-size: 1.4rem;
            color: rgba(255, 255, 255, 0.95);
            margin-top: 18px;
            font-weight: 300;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-60px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(60px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(100px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .interactive-area {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 30px;
            padding: 60px;
            margin-top: 50px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        .quiz-btn {
            background: linear-gradient(45deg, #1a1a2e, #16213e);
            color: #00d4ff;
            border: 2px solid #00d4ff;
            padding: 20px 40px;
            border-radius: 40px;
            margin: 12px;
            cursor: pointer;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-size: 1.1rem;
            font-weight: 500;
        }

        .quiz-btn:hover {
            transform: scale(1.1) translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 212, 255, 0.4);
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
        }

        .correct {
            background: linear-gradient(45deg, #00ff88, #00cc66) !important;
            color: white !important;
            border-color: #00ff88 !important;
            animation: correctGlow 1s ease;
        }

        .wrong {
            background: linear-gradient(45deg, #ff4757, #ff3742) !important;
            color: white !important;
            border-color: #ff4757 !important;
            animation: wrongPulse 1s ease;
        }

        @keyframes correctGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(0, 255, 136, 0.5); }
            50% { box-shadow: 0 0 40px rgba(0, 255, 136, 0.8); }
        }

        @keyframes wrongPulse {
            0%, 100% { transform: scale(1); }
            25%, 75% { transform: scale(0.95); }
            50% { transform: scale(1.05); }
        }
    </style>
</head>
<body>
    <!-- 星空背景 -->
    <div class="starfield" id="starfield"></div>
    
    <!-- 连接线动画 -->
    <div class="connection-lines" id="connectionLines"></div>

    <div class="container">
        <h1 class="title">Inter- 词缀星际课堂</h1>
        <p class="subtitle">跟随星际外交官探索"之间、相互"的连接奥秘</p>
        
        <div class="story-canvas">
            <div class="mission-status" id="missionStatus">任务状态：待命中</div>
            <canvas id="storyCanvas" width="1200" height="550"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="startStory()">🚀 启动任务</button>
            <button class="btn" onclick="nextScene()">🌟 下个星系</button>
            <button class="btn" onclick="prevScene()">⬅️ 返回星系</button>
            <button class="btn" onclick="resetStory()">🔄 重置任务</button>
        </div>

        <div class="explanation">
            <h2>为什么选择"星际外交官"的故事？</h2>
            <p>
                我选择用"星际外交官"来讲解 <span class="prefix">inter-</span> 词缀，是因为这个词缀的核心含义就是"在...之间、相互、互相"。
                外交官的工作就是在不同星球、不同文明之间建立连接和沟通，这完美体现了"inter-"的"之间"概念。
            </p>
            <p>
                通过外交官在各个星系间穿梭，建立international(国际的)关系，进行interact(互动)交流，
                使用internet(互联网)通讯，你可以直观地理解"相互连接"、"彼此之间"的含义，
                让抽象的关系概念变得生动具体。
            </p>
            
            <div id="wordCards">
                <!-- 词汇卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="interactive-area">
            <h3 style="color: #00d4ff; margin-bottom: 35px; font-size: 1.8rem;">🎯 星际测试：选择正确的翻译</h3>
            <div id="quizArea">
                <!-- 测试题目将通过JavaScript生成 -->
            </div>
            
            <div style="margin-top: 60px; padding-top: 50px; border-top: 2px solid rgba(0, 212, 255, 0.3);">
                <h3 style="color: #00d4ff; margin-bottom: 30px;">🔊 星际通讯练习</h3>
                <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 30px;">点击下面的按钮听发音，感受星际通讯的回音：</p>
                <div id="pronunciationArea" style="display: flex; flex-wrap: wrap; gap: 18px; justify-content: center;">
                    <!-- 发音按钮将通过JavaScript生成 -->
                </div>
            </div>
            
            <div style="margin-top: 60px; padding-top: 50px; border-top: 2px solid rgba(0, 212, 255, 0.3);">
                <h3 style="color: #00d4ff; margin-bottom: 30px;">💡 星际记忆法</h3>
                <div style="background: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #006699 100%); 
                           color: white; padding: 35px; border-radius: 25px; line-height: 2.2;">
                    <p><strong>🎯 记忆技巧：</strong></p>
                    <p>🌌 <strong>inter-</strong> = "在...之间、相互、互相"</p>
                    <p>🛸 想象一个外交官总是在不同星球"之间"穿梭</p>
                    <p>📝 每次看到 inter- 开头的单词，就想到"两个或多个事物之间"</p>
                    <p>🎯 练习方法：遇到新单词时，问自己"这涉及多个事物之间的关系吗？"</p>
                    <p>🌟 常见搭配：inter- + 名词 = 在某些事物之间的关系</p>
                    <p>⚡ 连接记忆：international(国际间)、internet(网络间)、interact(相互作用)</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 辅助函数：确保半径为正数
        function ensurePositiveRadius(radius, minRadius = 0.1) {
            return Math.max(minRadius, radius);
        }

        // 创建星空背景
        function createStarfield() {
            const starfield = document.getElementById('starfield');
            for (let i = 0; i < 200; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.width = star.style.height = (Math.random() * 3 + 1) + 'px';
                star.style.animationDelay = Math.random() * 3 + 's';
                star.style.animationDuration = (Math.random() * 2 + 2) + 's';
                starfield.appendChild(star);
            }
        }

        // 创建连接线动画
        function createConnectionLines() {
            const container = document.getElementById('connectionLines');
            setInterval(() => {
                const line = document.createElement('div');
                line.className = 'connection-line';
                line.style.top = Math.random() * 100 + '%';
                line.style.width = Math.random() * 200 + 100 + 'px';
                line.style.animationDuration = (Math.random() * 2 + 3) + 's';
                container.appendChild(line);
                
                setTimeout(() => {
                    if (line.parentNode) {
                        line.remove();
                    }
                }, 5000);
            }, 800);
        }

        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        let currentScene = 0;
        let animationFrame = 0;
        let isAnimating = false;

        // 故事场景数据
        const scenes = [
            {
                title: "星际总部",
                description: "外交官准备进行国际任务 (international)",
                words: ["international", "internet", "interview"],
                status: "任务准备中",
                bgColor: "#1a1a2e"
            },
            {
                title: "星系连接",
                description: "建立星际网络通讯 (internet)",
                words: ["internet", "interact", "interface"],
                status: "建立连接中",
                bgColor: "#16213e"
            },
            {
                title: "文明互动",
                description: "与外星文明进行互动 (interact)",
                words: ["interact", "interchange", "intercept"],
                status: "外交进行中",
                bgColor: "#0f0f23"
            },
            {
                title: "和平协议",
                description: "签署星际间和平条约 (international)",
                words: ["international", "internal", "interpret"],
                status: "任务完成",
                bgColor: "#1a1a2e"
            }
        ];

        // 词汇数据
        const vocabulary = [
            {
                word: "international",
                prefix: "inter-",
                root: "national (国家的)",
                meaning: "国际的、国家间的",
                explanation: "inter(在...之间) + national(国家的) = 在不同国家之间的",
                sentence: "The diplomat works on international relations. (外交官从事国际关系工作。)"
            },
            {
                word: "internet",
                prefix: "inter-",
                root: "net (网络)",
                meaning: "互联网、因特网",
                explanation: "inter(相互) + net(网络) = 相互连接的网络",
                sentence: "We communicate through the internet. (我们通过互联网交流。)"
            },
            {
                word: "interact",
                prefix: "inter-",
                root: "act (行动)",
                meaning: "互动、相互作用",
                explanation: "inter(相互) + act(行动) = 相互之间的行动",
                sentence: "Students interact with each other in class. (学生们在课堂上相互互动。)"
            },
            {
                word: "interview",
                prefix: "inter-",
                root: "view (看)",
                meaning: "面试、采访",
                explanation: "inter(相互) + view(看) = 相互观察和交流",
                sentence: "She has a job interview tomorrow. (她明天有一个工作面试。)"
            }
        ];

        function startStory() {
            currentScene = 0;
            animationFrame = 0;
            isAnimating = true;
            updateMissionStatus();
            drawScene();
            showWordCards();
        }

        function nextScene() {
            if (currentScene < scenes.length - 1) {
                currentScene++;
                animationFrame = 0;
                updateMissionStatus();
                drawScene();
                updateWordCards();
            }
        }

        function prevScene() {
            if (currentScene > 0) {
                currentScene--;
                animationFrame = 0;
                updateMissionStatus();
                drawScene();
                updateWordCards();
            }
        }

        function resetStory() {
            currentScene = 0;
            animationFrame = 0;
            isAnimating = false;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('wordCards').innerHTML = '';
            updateMissionStatus();
        }

        function updateMissionStatus() {
            document.getElementById('missionStatus').textContent = `任务状态：${scenes[currentScene].status}`;
        }

        function drawScene() {
            if (!isAnimating) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制太空背景
            drawSpaceBackground();

            // 绘制星球和连接
            drawPlanetsAndConnections();

            // 绘制外交官飞船
            drawDiplomatShip();

            // 绘制场景特效
            drawSceneEffects();

            // 绘制文字说明
            drawSceneText();

            animationFrame++;
            if (isAnimating) {
                requestAnimationFrame(drawScene);
            }
        }

        function drawSpaceBackground() {
            // 深空渐变背景
            const gradient = ctx.createRadialGradient(canvas.width/2, canvas.height/2, 0, canvas.width/2, canvas.height/2, canvas.width);
            gradient.addColorStop(0, scenes[currentScene].bgColor);
            gradient.addColorStop(1, '#000000');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制远景星星
            ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
            for (let i = 0; i < 150; i++) {
                const x = (i * 137.5) % canvas.width;
                const y = (i * 73.3) % canvas.height;
                const size = ensurePositiveRadius(Math.sin(animationFrame * 0.01 + i) * 1 + 0.5);
                const alpha = Math.sin(animationFrame * 0.02 + i) * 0.5 + 0.5;

                ctx.globalAlpha = alpha;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }
            ctx.globalAlpha = 1;

            // 绘制星云效果
            drawNebula();
        }

        function drawNebula() {
            const nebulaGradient = ctx.createRadialGradient(200, 150, 0, 200, 150, 200);
            nebulaGradient.addColorStop(0, 'rgba(0, 212, 255, 0.1)');
            nebulaGradient.addColorStop(0.5, 'rgba(0, 150, 255, 0.05)');
            nebulaGradient.addColorStop(1, 'transparent');

            ctx.fillStyle = nebulaGradient;
            ctx.beginPath();
            ctx.arc(200 + Math.sin(animationFrame * 0.005) * 50, 150, 200, 0, Math.PI * 2);
            ctx.fill();

            // 第二个星云
            const nebula2Gradient = ctx.createRadialGradient(800, 400, 0, 800, 400, 150);
            nebula2Gradient.addColorStop(0, 'rgba(255, 100, 200, 0.08)');
            nebula2Gradient.addColorStop(1, 'transparent');

            ctx.fillStyle = nebula2Gradient;
            ctx.beginPath();
            ctx.arc(800 + Math.sin(animationFrame * 0.008) * 30, 400, 150, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawPlanetsAndConnections() {
            const planets = [
                { x: 200, y: 200, radius: 40, color: '#ff6b6b', name: 'Alpha' },
                { x: 500, y: 150, radius: 35, color: '#4ecdc4', name: 'Beta' },
                { x: 800, y: 250, radius: 45, color: '#45b7d1', name: 'Gamma' },
                { x: 350, y: 350, radius: 30, color: '#96ceb4', name: 'Delta' },
                { x: 650, y: 400, radius: 38, color: '#feca57', name: 'Epsilon' }
            ];

            // 绘制连接线
            ctx.strokeStyle = 'rgba(0, 212, 255, 0.6)';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);

            for (let i = 0; i < planets.length; i++) {
                for (let j = i + 1; j < planets.length; j++) {
                    const alpha = Math.sin(animationFrame * 0.02 + i + j) * 0.3 + 0.4;
                    ctx.globalAlpha = alpha;

                    ctx.beginPath();
                    ctx.moveTo(planets[i].x, planets[i].y);
                    ctx.lineTo(planets[j].x, planets[j].y);
                    ctx.stroke();
                }
            }

            ctx.globalAlpha = 1;
            ctx.setLineDash([]);

            // 绘制星球
            planets.forEach((planet, index) => {
                // 星球主体
                const planetGradient = ctx.createRadialGradient(
                    planet.x - 10, planet.y - 10, 0,
                    planet.x, planet.y, planet.radius
                );
                planetGradient.addColorStop(0, planet.color);
                planetGradient.addColorStop(1, darkenColor(planet.color, 0.3));

                ctx.fillStyle = planetGradient;
                ctx.beginPath();
                ctx.arc(planet.x, planet.y, planet.radius, 0, Math.PI * 2);
                ctx.fill();

                // 星球光环
                const glowIntensity = Math.sin(animationFrame * 0.03 + index) * 0.3 + 0.4;
                ctx.strokeStyle = `rgba(0, 212, 255, ${glowIntensity})`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(planet.x, planet.y, planet.radius + 8, 0, Math.PI * 2);
                ctx.stroke();

                // 星球名称
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(planet.name, planet.x, planet.y + planet.radius + 25);

                // 绘制轨道
                drawOrbit(planet, index);
            });
        }

        function drawOrbit(planet, index) {
            const orbitRadius = planet.radius + 15 + Math.sin(animationFrame * 0.02 + index) * 5;
            ctx.strokeStyle = `rgba(255, 255, 255, 0.2)`;
            ctx.lineWidth = 1;
            ctx.setLineDash([2, 4]);
            ctx.beginPath();
            ctx.arc(planet.x, planet.y, orbitRadius, 0, Math.PI * 2);
            ctx.stroke();
            ctx.setLineDash([]);
        }

        function drawDiplomatShip() {
            const shipX = 100 + Math.sin(animationFrame * 0.01) * 50;
            const shipY = 300 + Math.cos(animationFrame * 0.015) * 30;

            // 飞船主体
            ctx.fillStyle = '#00d4ff';
            ctx.beginPath();
            ctx.ellipse(shipX, shipY, 25, 15, 0, 0, Math.PI * 2);
            ctx.fill();

            // 飞船驾驶舱
            ctx.fillStyle = '#87ceeb';
            ctx.beginPath();
            ctx.ellipse(shipX + 5, shipY - 5, 8, 6, 0, 0, Math.PI * 2);
            ctx.fill();

            // 推进器光芒
            const thrusterGlow = Math.sin(animationFrame * 0.2) * 0.5 + 0.5;
            ctx.fillStyle = `rgba(255, 100, 100, ${thrusterGlow})`;
            ctx.beginPath();
            ctx.ellipse(shipX - 30, shipY, 15, 8, 0, 0, Math.PI * 2);
            ctx.fill();

            // 飞船尾迹
            for (let i = 0; i < 5; i++) {
                const trailX = shipX - 40 - i * 15;
                const trailY = shipY + Math.sin(animationFrame * 0.1 + i) * 5;
                const alpha = (5 - i) / 5 * 0.6;
                const radius = ensurePositiveRadius(3 - i * 0.5, 0.5);

                ctx.fillStyle = `rgba(0, 212, 255, ${alpha})`;
                ctx.beginPath();
                ctx.arc(trailX, trailY, radius, 0, Math.PI * 2);
                ctx.fill();
            }

            // 通讯信号
            drawCommunicationSignals(shipX, shipY);
        }

        function drawCommunicationSignals(shipX, shipY) {
            const signalRadius = 20 + (animationFrame * 0.5) % 60;
            const alpha = 1 - (animationFrame * 0.5) % 60 / 60;

            ctx.strokeStyle = `rgba(0, 255, 136, ${alpha})`;
            ctx.lineWidth = 2;
            ctx.setLineDash([3, 3]);
            ctx.beginPath();
            ctx.arc(shipX, shipY, signalRadius, 0, Math.PI * 2);
            ctx.stroke();
            ctx.setLineDash([]);

            // 信号波纹
            for (let i = 0; i < 3; i++) {
                const waveRadius = 30 + i * 20 + (animationFrame * 0.3) % 40;
                const waveAlpha = (1 - (animationFrame * 0.3) % 40 / 40) * 0.3;

                ctx.strokeStyle = `rgba(0, 212, 255, ${waveAlpha})`;
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(shipX, shipY, waveRadius, 0, Math.PI * 2);
                ctx.stroke();
            }
        }

        function drawSceneEffects() {
            switch(currentScene) {
                case 0:
                    drawHeadquartersEffects();
                    break;
                case 1:
                    drawNetworkEffects();
                    break;
                case 2:
                    drawInteractionEffects();
                    break;
                case 3:
                    drawPeaceTreatyEffects();
                    break;
            }
        }

        function drawHeadquartersEffects() {
            // 绘制总部建筑
            const hqX = 900;
            const hqY = 100;

            ctx.fillStyle = 'rgba(0, 212, 255, 0.3)';
            ctx.fillRect(hqX, hqY, 80, 120);

            // 建筑窗户
            ctx.fillStyle = '#ffff00';
            for (let i = 0; i < 3; i++) {
                for (let j = 0; j < 4; j++) {
                    const windowAlpha = Math.sin(animationFrame * 0.05 + i + j) * 0.5 + 0.5;
                    ctx.globalAlpha = windowAlpha;
                    ctx.fillRect(hqX + 10 + i * 20, hqY + 20 + j * 20, 8, 8);
                }
            }
            ctx.globalAlpha = 1;

            // 通讯天线
            ctx.strokeStyle = '#00d4ff';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(hqX + 40, hqY);
            ctx.lineTo(hqX + 40, hqY - 30);
            ctx.stroke();

            // 天线信号
            const antennaSignal = Math.sin(animationFrame * 0.1) * 0.5 + 0.5;
            ctx.fillStyle = `rgba(0, 212, 255, ${antennaSignal})`;
            ctx.beginPath();
            ctx.arc(hqX + 40, hqY - 30, 5, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawNetworkEffects() {
            // 绘制数据流
            for (let i = 0; i < 10; i++) {
                const dataX = (animationFrame * 2 + i * 100) % canvas.width;
                const dataY = 50 + Math.sin(animationFrame * 0.02 + i) * 20;

                ctx.fillStyle = `hsl(${(animationFrame + i * 36) % 360}, 70%, 60%)`;
                ctx.beginPath();
                ctx.arc(dataX, dataY, 3, 0, Math.PI * 2);
                ctx.fill();
            }

            // 网络节点
            const nodes = [
                { x: 150, y: 100 }, { x: 400, y: 80 }, { x: 650, y: 120 },
                { x: 200, y: 450 }, { x: 500, y: 480 }, { x: 800, y: 460 }
            ];

            nodes.forEach((node, index) => {
                const pulse = Math.sin(animationFrame * 0.05 + index) * 0.3 + 0.7;
                ctx.fillStyle = `rgba(0, 255, 136, ${pulse})`;
                ctx.beginPath();
                ctx.arc(node.x, node.y, 8, 0, Math.PI * 2);
                ctx.fill();

                // 节点连接
                if (index < nodes.length - 1) {
                    ctx.strokeStyle = `rgba(0, 255, 136, ${pulse * 0.5})`;
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(node.x, node.y);
                    ctx.lineTo(nodes[index + 1].x, nodes[index + 1].y);
                    ctx.stroke();
                }
            });
        }

        function drawInteractionEffects() {
            // 绘制文明交流符号
            const symbols = ['👽', '🤖', '🛸', '🌟'];
            symbols.forEach((symbol, index) => {
                const x = 300 + index * 150 + Math.sin(animationFrame * 0.03 + index) * 20;
                const y = 400 + Math.cos(animationFrame * 0.04 + index) * 15;

                ctx.font = '30px Arial';
                ctx.textAlign = 'center';
                ctx.fillStyle = `hsl(${(animationFrame * 2 + index * 90) % 360}, 70%, 60%)`;
                ctx.fillText(symbol, x, y);
            });

            // 交流能量波
            for (let i = 0; i < 5; i++) {
                const waveRadius = 50 + i * 30 + (animationFrame * 0.5) % 100;
                const alpha = (1 - (animationFrame * 0.5) % 100 / 100) * 0.4;

                ctx.strokeStyle = `rgba(255, 215, 0, ${alpha})`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(canvas.width / 2, canvas.height / 2, waveRadius, 0, Math.PI * 2);
                ctx.stroke();
            }
        }

        function drawPeaceTreatyEffects() {
            // 绘制和平条约文档
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(450, 200, 200, 150);

            ctx.strokeStyle = '#00d4ff';
            ctx.lineWidth = 2;
            ctx.strokeRect(450, 200, 200, 150);

            // 文档内容线条
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.lineWidth = 1;
            for (let i = 0; i < 8; i++) {
                ctx.beginPath();
                ctx.moveTo(460, 220 + i * 15);
                ctx.lineTo(630, 220 + i * 15);
                ctx.stroke();
            }

            // 签名效果
            ctx.fillStyle = '#ff6b6b';
            ctx.font = '16px cursive';
            ctx.textAlign = 'center';
            ctx.fillText('✓ 已签署', 550, 330);

            // 庆祝烟花
            for (let i = 0; i < 8; i++) {
                const fireworkX = 200 + i * 100 + Math.sin(animationFrame * 0.1 + i) * 50;
                const fireworkY = 100 + Math.cos(animationFrame * 0.08 + i) * 30;

                ctx.fillStyle = `hsl(${(animationFrame * 3 + i * 45) % 360}, 80%, 60%)`;
                ctx.beginPath();
                ctx.arc(fireworkX, fireworkY, 5, 0, Math.PI * 2);
                ctx.fill();

                // 烟花尾迹
                for (let j = 0; j < 5; j++) {
                    const trailX = fireworkX + Math.sin(animationFrame * 0.2 + j) * (j + 1) * 8;
                    const trailY = fireworkY + Math.cos(animationFrame * 0.2 + j) * (j + 1) * 8;
                    const trailAlpha = (5 - j) / 5 * 0.6;

                    ctx.fillStyle = `hsla(${(animationFrame * 3 + i * 45) % 360}, 80%, 60%, ${trailAlpha})`;
                    ctx.beginPath();
                    ctx.arc(trailX, trailY, 2, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
        }

        function drawSceneText() {
            // 场景标题
            ctx.fillStyle = '#00d4ff';
            ctx.font = 'bold 36px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.shadowColor = 'rgba(0, 212, 255, 0.5)';
            ctx.shadowBlur = 10;
            ctx.fillText(scenes[currentScene].title, canvas.width / 2, 60);

            // 场景描述
            ctx.font = '22px Microsoft YaHei';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillText(scenes[currentScene].description, canvas.width / 2, 90);
            ctx.shadowBlur = 0;
        }

        function darkenColor(color, factor) {
            // 简单的颜色变暗函数
            const hex = color.replace('#', '');
            const r = Math.max(0, parseInt(hex.substr(0, 2), 16) * (1 - factor));
            const g = Math.max(0, parseInt(hex.substr(2, 2), 16) * (1 - factor));
            const b = Math.max(0, parseInt(hex.substr(4, 2), 16) * (1 - factor));
            return `rgb(${Math.floor(r)}, ${Math.floor(g)}, ${Math.floor(b)})`;
        }

        function showWordCards() {
            const container = document.getElementById('wordCards');
            container.innerHTML = '';

            vocabulary.forEach((item, index) => {
                setTimeout(() => {
                    const card = document.createElement('div');
                    card.className = 'word-card';
                    card.innerHTML = `
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 18px;">
                            <span class="prefix">${item.prefix}</span>${item.word.replace(item.prefix.replace('-', ''), '')}
                        </div>
                        <div class="meaning">${item.meaning}</div>
                        <div style="font-size: 1.1rem; margin-top: 15px; opacity: 0.9;">${item.explanation}</div>
                        <div style="font-size: 1.05rem; margin-top: 12px; font-style: italic; opacity: 0.85;
                                   background: rgba(255,255,255,0.1); padding: 12px; border-radius: 10px;">
                            "${item.sentence}"
                        </div>
                    `;

                    card.addEventListener('click', () => {
                        speakWord(item.word);
                        showWordBreakdown(item);
                    });

                    container.appendChild(card);

                    setTimeout(() => {
                        card.classList.add('show');
                    }, 100);
                }, index * 600);
            });
        }

        function updateWordCards() {
            const cards = document.querySelectorAll('.word-card');
            cards.forEach(card => {
                card.style.opacity = '0.5';
                card.style.transform = 'scale(0.95)';
            });

            // 高亮当前场景的词汇
            const currentWords = scenes[currentScene].words;
            cards.forEach((card, index) => {
                if (currentWords.includes(vocabulary[index]?.word)) {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1.05)';
                    card.style.boxShadow = '0 35px 70px rgba(0, 212, 255, 0.5)';
                }
            });
        }

        function showWordBreakdown(wordData) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.5s ease;
                backdrop-filter: blur(10px);
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: linear-gradient(135deg, #00d4ff 0%, #0099cc 50%, #006699 100%);
                color: white;
                padding: 70px;
                border-radius: 30px;
                max-width: 700px;
                text-align: center;
                animation: slideInUp 0.7s ease;
                box-shadow: 0 35px 70px rgba(0, 212, 255, 0.4);
                border: 2px solid rgba(255,255,255,0.2);
            `;

            content.innerHTML = `
                <h2 style="margin-bottom: 35px; font-size: 2.2rem; font-weight: 300;">🌌 星际词汇解析</h2>
                <div style="font-size: 3.5rem; margin: 35px 0;">
                    <span style="color: #FFD700; font-weight: bold;">${wordData.prefix}</span>
                    <span style="color: #87CEEB; font-weight: bold;">${wordData.root}</span>
                </div>
                <div style="font-size: 2.2rem; margin: 30px 0; font-weight: bold;">${wordData.word}</div>
                <div style="font-size: 1.5rem; margin: 30px 0; opacity: 0.9;">${wordData.meaning}</div>
                <div style="line-height: 2; margin: 30px 0; font-size: 1.2rem; opacity: 0.85;">${wordData.explanation}</div>
                <div style="background: rgba(255,255,255,0.15); padding: 25px; border-radius: 15px; margin: 30px 0;
                           font-style: italic; font-size: 1.15rem;">
                    ${wordData.sentence}
                </div>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 35px; padding: 18px 35px; background: linear-gradient(45deg, #1a1a2e, #16213e);
                               color: #00d4ff; border: 2px solid #00d4ff; border-radius: 30px; cursor: pointer; font-size: 1.2rem;
                               transition: all 0.3s ease;">
                    返回星际总部
                </button>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);

            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            };
        }

        function speakWord(word) {
            if ('speechSynthesis' in window) {
                speechSynthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(word);
                utterance.lang = 'en-US';
                utterance.rate = 0.8;
                utterance.pitch = 1.0;

                const feedback = document.createElement('div');
                feedback.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(45deg, #00d4ff, #0099cc);
                    color: white;
                    padding: 35px 55px;
                    border-radius: 50px;
                    font-size: 2rem;
                    z-index: 1000;
                    animation: fadeIn 0.5s ease;
                    box-shadow: 0 25px 50px rgba(0, 212, 255, 0.5);
                    border: 2px solid rgba(255,255,255,0.3);
                `;
                feedback.innerHTML = `🛸 <strong>${word}</strong><br><small style="opacity: 0.8; font-size: 0.7em;">星际通讯中...</small>`;
                document.body.appendChild(feedback);

                utterance.onend = () => {
                    setTimeout(() => {
                        if (feedback.parentNode) {
                            feedback.remove();
                        }
                    }, 1200);
                };

                speechSynthesis.speak(utterance);
            } else {
                alert('您的浏览器不支持语音功能');
            }
        }

        function initQuiz() {
            const quizData = [
                {
                    question: "international 的意思是？",
                    options: ["国际的", "互联网", "互动", "面试"],
                    correct: 0,
                    explanation: "inter(在...之间) + national(国家的) = 在不同国家之间的"
                },
                {
                    question: "internet 的意思是？",
                    options: ["国际的", "互联网", "互动", "面试"],
                    correct: 1,
                    explanation: "inter(相互) + net(网络) = 相互连接的网络"
                },
                {
                    question: "interact 的意思是？",
                    options: ["国际的", "互联网", "互动", "面试"],
                    correct: 2,
                    explanation: "inter(相互) + act(行动) = 相互之间的行动"
                },
                {
                    question: "interview 的意思是？",
                    options: ["国际的", "互联网", "互动", "面试"],
                    correct: 3,
                    explanation: "inter(相互) + view(看) = 相互观察和交流"
                }
            ];

            const quizArea = document.getElementById('quizArea');

            quizData.forEach((quiz, qIndex) => {
                const quizDiv = document.createElement('div');
                quizDiv.style.marginBottom = '35px';
                quizDiv.innerHTML = `<h4 style="margin-bottom: 20px; color: #00d4ff; font-size: 1.4rem;">${quiz.question}</h4>`;

                quiz.options.forEach((option, oIndex) => {
                    const btn = document.createElement('button');
                    btn.className = 'quiz-btn';
                    btn.textContent = option;
                    btn.onclick = () => checkAnswer(btn, oIndex === quiz.correct, quiz.explanation, qIndex);
                    quizDiv.appendChild(btn);
                });

                quizArea.appendChild(quizDiv);
            });
        }

        function checkAnswer(btn, isCorrect, explanation, questionIndex) {
            const buttons = btn.parentNode.querySelectorAll('.quiz-btn');
            buttons.forEach(b => b.disabled = true);

            if (isCorrect) {
                btn.classList.add('correct');
                btn.innerHTML += ' ✓';

                // 显示解释
                setTimeout(() => {
                    const explanationDiv = document.createElement('div');
                    explanationDiv.style.cssText = `
                        margin-top: 18px;
                        padding: 25px;
                        background: linear-gradient(135deg, #00ff88, #00cc66);
                        color: white;
                        border-radius: 15px;
                        font-size: 1.05rem;
                        animation: slideInUp 0.7s ease;
                        box-shadow: 0 15px 30px rgba(0, 255, 136, 0.3);
                    `;
                    explanationDiv.innerHTML = `🌟 ${explanation}`;
                    btn.parentNode.appendChild(explanationDiv);
                }, 700);
            } else {
                btn.classList.add('wrong');
                btn.innerHTML += ' ✗';
            }
        }

        function initPronunciation() {
            const pronunciationArea = document.getElementById('pronunciationArea');

            vocabulary.forEach((item, index) => {
                const btn = document.createElement('button');
                btn.className = 'quiz-btn';
                btn.style.background = 'linear-gradient(45deg, #00d4ff, #0099cc)';
                btn.style.color = 'white';
                btn.style.borderColor = '#00d4ff';
                btn.innerHTML = `🛸 ${item.word}`;

                btn.onclick = () => {
                    speakWord(item.word);
                    btn.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        btn.style.transform = 'scale(1)';
                    }, 200);
                };

                pronunciationArea.appendChild(btn);
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextScene();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    prevScene();
                    break;
                case 'r':
                case 'R':
                    e.preventDefault();
                    resetStory();
                    break;
                case 's':
                case 'S':
                    e.preventDefault();
                    startStory();
                    break;
            }
        });

        // 页面加载时创建星空效果
        window.addEventListener('load', () => {
            createStarfield();
            createConnectionLines();
            initQuiz();
            initPronunciation();
            setTimeout(startStory, 1500);

            // 显示快捷键提示
            setTimeout(() => {
                const hints = document.createElement('div');
                hints.style.cssText = `
                    position: fixed;
                    bottom: 35px;
                    right: 35px;
                    background: rgba(0,0,0,0.9);
                    color: #00d4ff;
                    padding: 30px;
                    border-radius: 25px;
                    font-size: 1rem;
                    z-index: 1000;
                    animation: slideInUp 0.7s ease;
                    backdrop-filter: blur(15px);
                    border: 2px solid rgba(0, 212, 255, 0.3);
                `;
                hints.innerHTML = `
                    <div style="margin-bottom: 12px;"><strong>🛸 星际快捷键：</strong></div>
                    <div>→ 或 空格：下个星系</div>
                    <div>← ：返回星系</div>
                    <div>S：启动任务</div>
                    <div>R：重置任务</div>
                `;

                document.body.appendChild(hints);

                setTimeout(() => {
                    hints.style.animation = 'fadeOut 0.7s ease';
                    setTimeout(() => hints.remove(), 700);
                }, 8000);
            }, 3000);
        });
    </script>
</body>
</html>
