<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库三范式 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 40px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.8s ease;
            border-radius: 4px;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.6s ease;
        }

        .section.active {
            opacity: 1;
            transform: translateY(0);
        }

        .section-title {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .interactive-demo {
            background: #fff;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px dashed #e0e0e0;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .demo-button:active {
            transform: translateY(-1px);
        }

        .navigation {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 40px 0;
        }

        .nav-button {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .nav-button:hover, .nav-button.active {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .game-score {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-weight: bold;
            color: #333;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="game-score">
        <div>学习进度: <span id="score">0</span>/100</div>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">数据库三范式</h1>
            <p class="subtitle">通过动画和交互学习数据库设计的基本原则</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="navigation">
            <button class="nav-button active" onclick="showSection(0)">概述</button>
            <button class="nav-button" onclick="showSection(1)">第一范式</button>
            <button class="nav-button" onclick="showSection(2)">第二范式</button>
            <button class="nav-button" onclick="showSection(3)">第三范式</button>
            <button class="nav-button" onclick="showSection(4)">综合练习</button>
        </div>

        <!-- 概述部分 -->
        <div class="section active" id="section0">
            <h2 class="section-title">什么是数据库范式？</h2>
            <div class="explanation">
                <p><strong>数据库范式</strong>是设计关系数据库时应遵循的规则，目的是减少数据冗余，避免数据异常。</p>
                <p>想象一下，如果你的书架上的书籍摆放得很乱，找书就会很困难。数据库范式就像是整理书架的规则，让数据存储更有序、更高效。</p>
            </div>
            <div class="canvas-container">
                <canvas id="overviewCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animateOverview()">🎬 播放动画演示</button>
                <button class="demo-button" onclick="resetOverview()">🔄 重置演示</button>
            </div>
        </div>

        <!-- 第一范式部分 -->
        <div class="section" id="section1">
            <h2 class="section-title">第一范式 (1NF)</h2>
            <div class="explanation">
                <p><strong>第一范式要求：每个列都不可以再拆分</strong></p>
                <p>就像每个抽屉里只能放一种类型的物品，数据库表中的每个字段只能存储一个值，不能存储多个值或复合值。</p>
            </div>
            <div class="canvas-container">
                <canvas id="firstNFCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animateFirstNF()">🎬 演示违反1NF</button>
                <button class="demo-button" onclick="animateFirstNFCorrect()">✅ 演示符合1NF</button>
                <button class="demo-button" onclick="resetFirstNF()">🔄 重置</button>
            </div>
        </div>

        <!-- 第二范式部分 -->
        <div class="section" id="section2">
            <h2 class="section-title">第二范式 (2NF)</h2>
            <div class="explanation">
                <p><strong>第二范式要求：在第一范式的基础上，非主键列完全依赖于主键，而不能是依赖于主键的一部分</strong></p>
                <p>想象主键是房子的地址，其他信息应该完全依赖于这个地址，而不是只依赖于地址的一部分（比如只依赖于街道名）。</p>
            </div>
            <div class="canvas-container">
                <canvas id="secondNFCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animateSecondNF()">🎬 演示违反2NF</button>
                <button class="demo-button" onclick="animateSecondNFCorrect()">✅ 演示符合2NF</button>
                <button class="demo-button" onclick="resetSecondNF()">🔄 重置</button>
            </div>
        </div>

        <!-- 第三范式部分 -->
        <div class="section" id="section3">
            <h2 class="section-title">第三范式 (3NF)</h2>
            <div class="explanation">
                <p><strong>第三范式要求：在第二范式的基础上，非主键列只依赖于主键，不依赖于其他非主键</strong></p>
                <p>就像员工的工资应该直接依赖于员工ID，而不应该通过部门来间接依赖。消除传递依赖，让关系更直接。</p>
            </div>
            <div class="canvas-container">
                <canvas id="thirdNFCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="animateThirdNF()">🎬 演示违反3NF</button>
                <button class="demo-button" onclick="animateThirdNFCorrect()">✅ 演示符合3NF</button>
                <button class="demo-button" onclick="resetThirdNF()">🔄 重置</button>
            </div>
        </div>

        <!-- 综合练习部分 -->
        <div class="section" id="section4">
            <h2 class="section-title">综合练习与总结</h2>
            <div class="explanation">
                <p><strong>设计原则总结：</strong></p>
                <ul style="margin-left: 20px; margin-top: 15px;">
                    <li>🎯 <strong>第一范式</strong>：每个字段存储单一值</li>
                    <li>🎯 <strong>第二范式</strong>：消除部分依赖</li>
                    <li>🎯 <strong>第三范式</strong>：消除传递依赖</li>
                </ul>
                <p style="margin-top: 20px;"><strong>实际应用中的权衡：</strong>有时为了性能，我们会适当违反范式，但必须有充分的理由。</p>
            </div>
            <div class="canvas-container">
                <canvas id="summaryCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="startQuiz()">🎮 开始互动测试</button>
                <button class="demo-button" onclick="animateSummary()">📊 查看完整对比</button>
                <button class="demo-button" onclick="resetSummary()">🔄 重置</button>
            </div>
        </div>
    </div>

    <script>
        let currentSection = 0;
        let score = 0;
        let quizMode = false;

        // 初始化浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 20; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(element);
            }
        }

        // 显示指定部分
        function showSection(index) {
            // 隐藏所有部分
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });

            // 更新导航按钮
            document.querySelectorAll('.nav-button').forEach((btn, i) => {
                btn.classList.toggle('active', i === index);
            });

            // 显示目标部分
            document.getElementById(`section${index}`).classList.add('active');
            currentSection = index;

            // 更新进度
            updateProgress();

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 更新进度条
        function updateProgress() {
            const progress = ((currentSection + 1) / 5) * 100;
            document.getElementById('progressFill').style.width = progress + '%';

            // 更新分数
            score = Math.max(score, progress);
            document.getElementById('score').textContent = Math.round(score);
        }

        // 绘制箭头函数
        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI / 6), toY - 15 * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI / 6), toY - 15 * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // 绘制表格函数
        function drawTable(ctx, x, y, width, height, headers, rows, colors) {
            const cellHeight = height / (headers.length + 1);
            const cellWidth = width / Math.max(headers.length, rows.length > 0 ? rows[0].length : 1);

            // 绘制表格边框
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);

            // 绘制表头
            ctx.fillStyle = colors.header || '#667eea';
            ctx.fillRect(x, y, width, cellHeight);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';

            headers.forEach((header, index) => {
                const cellX = x + index * cellWidth;
                ctx.fillText(header, cellX + cellWidth / 2, y + cellHeight / 2 + 5);

                // 绘制垂直分割线
                if (index > 0) {
                    ctx.strokeStyle = '#333';
                    ctx.beginPath();
                    ctx.moveTo(cellX, y);
                    ctx.lineTo(cellX, y + height);
                    ctx.stroke();
                }
            });

            // 绘制数据行
            rows.forEach((row, rowIndex) => {
                const rowY = y + (rowIndex + 1) * cellHeight;

                // 绘制行背景
                ctx.fillStyle = rowIndex % 2 === 0 ? '#f8f9fa' : 'white';
                ctx.fillRect(x, rowY, width, cellHeight);

                // 绘制行数据
                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';

                row.forEach((cell, cellIndex) => {
                    const cellX = x + cellIndex * cellWidth;
                    ctx.fillText(cell, cellX + cellWidth / 2, rowY + cellHeight / 2 + 5);
                });

                // 绘制水平分割线
                ctx.strokeStyle = '#ddd';
                ctx.beginPath();
                ctx.moveTo(x, rowY);
                ctx.lineTo(x + width, rowY);
                ctx.stroke();
            });
        }

        // 概述动画
        function animateOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('数据库设计的演进过程', canvas.width / 2, 40);

                if (step >= 1) {
                    // 绘制混乱的数据表
                    ctx.fillStyle = '#ff6b6b';
                    ctx.fillRect(50, 80, 200, 120);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText('混乱的数据存储', 60, 100);
                    ctx.fillText('• 数据重复', 60, 120);
                    ctx.fillText('• 更新异常', 60, 140);
                    ctx.fillText('• 删除异常', 60, 160);
                    ctx.fillText('• 插入异常', 60, 180);
                }

                if (step >= 2) {
                    // 绘制箭头
                    drawArrow(ctx, 270, 140, 350, 140, '#4CAF50');
                    ctx.fillStyle = '#4CAF50';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('应用范式', 310, 120);
                }

                if (step >= 3) {
                    // 绘制规范化后的数据表
                    ctx.fillStyle = '#4CAF50';
                    ctx.fillRect(370, 80, 200, 120);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText('规范化的数据存储', 380, 100);
                    ctx.fillText('• 减少冗余', 380, 120);
                    ctx.fillText('• 数据一致性', 380, 140);
                    ctx.fillText('• 易于维护', 380, 160);
                    ctx.fillText('• 节省空间', 380, 180);
                }

                if (step >= 4) {
                    // 绘制三范式
                    const nfBoxes = [
                        { x: 150, y: 250, text: '第一范式\n原子性', color: '#667eea' },
                        { x: 300, y: 250, text: '第二范式\n完全依赖', color: '#764ba2' },
                        { x: 450, y: 250, text: '第三范式\n直接依赖', color: '#f093fb' }
                    ];

                    nfBoxes.forEach((box, index) => {
                        if (step >= 4 + index * 0.5) {
                            ctx.fillStyle = box.color;
                            ctx.fillRect(box.x - 50, box.y, 100, 80);
                            ctx.fillStyle = 'white';
                            ctx.font = '12px Microsoft YaHei';
                            ctx.textAlign = 'center';
                            const lines = box.text.split('\n');
                            lines.forEach((line, lineIndex) => {
                                ctx.fillText(line, box.x, box.y + 25 + lineIndex * 20);
                            });
                        }
                    });
                }

                step += 0.02;
                if (step < 6) {
                    requestAnimationFrame(animate);
                }
            };

            animate();
        }

        // 重置概述
        function resetOverview() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 第一范式动画 - 违反1NF
        function animateFirstNF() {
            const canvas = document.getElementById('firstNFCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#ff6b6b';
            ctx.textAlign = 'center';
            ctx.fillText('❌ 违反第一范式的表设计', canvas.width / 2, 30);

            // 绘制违反1NF的表
            const headers = ['学生ID', '姓名', '联系方式'];
            const rows = [
                ['001', '张三', '电话:13800138000,邮箱:<EMAIL>'],
                ['002', '李四', '电话:13900139000,QQ:123456789'],
                ['003', '王五', '微信:wangwu,电话:13700137000']
            ];

            drawTable(ctx, 100, 60, 600, 150, headers, rows, { header: '#ff6b6b' });

            // 添加问题说明
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#ff6b6b';
            ctx.textAlign = 'left';
            ctx.fillText('问题：联系方式列包含多个值，违反了原子性原则', 120, 240);

            // 绘制箭头指向问题列
            drawArrow(ctx, 550, 250, 550, 200, '#ff6b6b');
        }

        // 第一范式动画 - 符合1NF
        function animateFirstNFCorrect() {
            const canvas = document.getElementById('firstNFCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#4CAF50';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 符合第一范式的表设计', canvas.width / 2, 30);

            // 绘制符合1NF的表
            const headers = ['学生ID', '姓名', '联系类型', '联系方式'];
            const rows = [
                ['001', '张三', '电话', '13800138000'],
                ['001', '张三', '邮箱', '<EMAIL>'],
                ['002', '李四', '电话', '13900139000'],
                ['002', '李四', 'QQ', '123456789'],
                ['003', '王五', '微信', 'wangwu'],
                ['003', '王五', '电话', '13700137000']
            ];

            drawTable(ctx, 100, 60, 600, 200, headers, rows, { header: '#4CAF50' });

            // 添加说明
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#4CAF50';
            ctx.textAlign = 'left';
            ctx.fillText('✅ 每个字段都是原子的，不可再分', 120, 290);
            ctx.fillText('✅ 一个学生的多个联系方式分别存储在不同行中', 120, 320);
        }

        // 重置第一范式
        function resetFirstNF() {
            const canvas = document.getElementById('firstNFCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 第二范式动画 - 违反2NF
        function animateSecondNF() {
            const canvas = document.getElementById('secondNFCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#ff6b6b';
            ctx.textAlign = 'center';
            ctx.fillText('❌ 违反第二范式的表设计', canvas.width / 2, 30);

            // 绘制违反2NF的表（复合主键）
            const headers = ['学生ID', '课程ID', '学生姓名', '课程名称', '成绩'];
            const rows = [
                ['001', 'C001', '张三', '数学', '85'],
                ['001', 'C002', '张三', '英语', '90'],
                ['002', 'C001', '李四', '数学', '78'],
                ['002', 'C002', '李四', '英语', '88']
            ];

            drawTable(ctx, 50, 60, 700, 150, headers, rows, { header: '#ff6b6b' });

            // 绘制依赖关系说明
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#ff6b6b';
            ctx.textAlign = 'left';
            ctx.fillText('问题分析：', 70, 240);
            ctx.fillText('• 学生姓名只依赖于学生ID（部分依赖）', 70, 260);
            ctx.fillText('• 课程名称只依赖于课程ID（部分依赖）', 70, 280);
            ctx.fillText('• 成绩依赖于完整的主键（学生ID + 课程ID）', 70, 300);

            // 绘制依赖箭头
            drawArrow(ctx, 150, 320, 150, 200, '#ff6b6b');
            drawArrow(ctx, 250, 320, 250, 200, '#ff6b6b');
        }

        // 第二范式动画 - 符合2NF
        function animateSecondNFCorrect() {
            const canvas = document.getElementById('secondNFCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillStyle = '#4CAF50';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 符合第二范式的表设计', canvas.width / 2, 25);

            // 绘制学生表
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'left';
            ctx.fillText('学生表:', 50, 55);

            const studentHeaders = ['学生ID', '学生姓名'];
            const studentRows = [
                ['001', '张三'],
                ['002', '李四']
            ];
            drawTable(ctx, 50, 65, 200, 80, studentHeaders, studentRows, { header: '#4CAF50' });

            // 绘制课程表
            ctx.fillText('课程表:', 300, 55);
            const courseHeaders = ['课程ID', '课程名称'];
            const courseRows = [
                ['C001', '数学'],
                ['C002', '英语']
            ];
            drawTable(ctx, 300, 65, 200, 80, courseHeaders, courseRows, { header: '#4CAF50' });

            // 绘制成绩表
            ctx.fillText('成绩表:', 550, 55);
            const gradeHeaders = ['学生ID', '课程ID', '成绩'];
            const gradeRows = [
                ['001', 'C001', '85'],
                ['001', 'C002', '90'],
                ['002', 'C001', '78'],
                ['002', 'C002', '88']
            ];
            drawTable(ctx, 550, 65, 200, 120, gradeHeaders, gradeRows, { header: '#4CAF50' });

            // 添加说明
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#4CAF50';
            ctx.fillText('✅ 消除了部分依赖，每个非主键字段完全依赖于主键', 50, 220);
        }

        // 重置第二范式
        function resetSecondNF() {
            const canvas = document.getElementById('secondNFCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 第三范式动画 - 违反3NF
        function animateThirdNF() {
            const canvas = document.getElementById('thirdNFCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#ff6b6b';
            ctx.textAlign = 'center';
            ctx.fillText('❌ 违反第三范式的表设计', canvas.width / 2, 30);

            // 绘制违反3NF的表
            const headers = ['员工ID', '姓名', '部门ID', '部门名称', '部门经理'];
            const rows = [
                ['E001', '张三', 'D001', '技术部', '王经理'],
                ['E002', '李四', 'D001', '技术部', '王经理'],
                ['E003', '王五', 'D002', '销售部', '刘经理'],
                ['E004', '赵六', 'D002', '销售部', '刘经理']
            ];

            drawTable(ctx, 50, 60, 700, 150, headers, rows, { header: '#ff6b6b' });

            // 绘制传递依赖说明
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#ff6b6b';
            ctx.textAlign = 'left';
            ctx.fillText('问题：存在传递依赖', 70, 240);
            ctx.fillText('员工ID → 部门ID → 部门名称', 70, 260);
            ctx.fillText('员工ID → 部门ID → 部门经理', 70, 280);
            ctx.fillText('部门信息间接依赖于员工ID，造成数据冗余', 70, 300);

            // 绘制传递依赖箭头
            drawArrow(ctx, 200, 320, 200, 200, '#ff6b6b');
            drawArrow(ctx, 350, 320, 350, 200, '#ff6b6b');
            drawArrow(ctx, 500, 320, 500, 200, '#ff6b6b');
        }

        // 第三范式动画 - 符合3NF
        function animateThirdNFCorrect() {
            const canvas = document.getElementById('thirdNFCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillStyle = '#4CAF50';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 符合第三范式的表设计', canvas.width / 2, 25);

            // 绘制员工表
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'left';
            ctx.fillText('员工表:', 50, 55);

            const empHeaders = ['员工ID', '姓名', '部门ID'];
            const empRows = [
                ['E001', '张三', 'D001'],
                ['E002', '李四', 'D001'],
                ['E003', '王五', 'D002'],
                ['E004', '赵六', 'D002']
            ];
            drawTable(ctx, 50, 65, 300, 120, empHeaders, empRows, { header: '#4CAF50' });

            // 绘制部门表
            ctx.fillText('部门表:', 400, 55);
            const deptHeaders = ['部门ID', '部门名称', '部门经理'];
            const deptRows = [
                ['D001', '技术部', '王经理'],
                ['D002', '销售部', '刘经理']
            ];
            drawTable(ctx, 400, 65, 300, 80, deptHeaders, deptRows, { header: '#4CAF50' });

            // 添加说明
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#4CAF50';
            ctx.textAlign = 'left';
            ctx.fillText('✅ 消除了传递依赖，部门信息直接依赖于部门ID', 50, 220);
            ctx.fillText('✅ 减少了数据冗余，提高了数据一致性', 50, 240);
        }

        // 重置第三范式
        function resetThirdNF() {
            const canvas = document.getElementById('thirdNFCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 综合总结动画
        function animateSummary() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('数据库三范式对比总结', canvas.width / 2, 40);

                const norms = [
                    {
                        title: '第一范式 (1NF)',
                        desc: '每个列都不可再拆分\n确保数据的原子性',
                        color: '#667eea',
                        x: 130,
                        example: '联系方式分离'
                    },
                    {
                        title: '第二范式 (2NF)',
                        desc: '消除部分依赖\n非主键完全依赖主键',
                        color: '#764ba2',
                        x: 400,
                        example: '分离学生课程表'
                    },
                    {
                        title: '第三范式 (3NF)',
                        desc: '消除传递依赖\n非主键直接依赖主键',
                        color: '#f093fb',
                        x: 670,
                        example: '分离员工部门表'
                    }
                ];

                norms.forEach((norm, index) => {
                    if (step >= index + 1) {
                        // 绘制范式框
                        ctx.fillStyle = norm.color;
                        ctx.fillRect(norm.x - 100, 80, 200, 120);

                        // 绘制标题
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Microsoft YaHei';
                        ctx.textAlign = 'center';
                        ctx.fillText(norm.title, norm.x, 110);

                        // 绘制描述
                        ctx.font = '12px Microsoft YaHei';
                        const lines = norm.desc.split('\n');
                        lines.forEach((line, lineIndex) => {
                            ctx.fillText(line, norm.x, 135 + lineIndex * 18);
                        });

                        // 绘制示例
                        ctx.fillStyle = norm.color;
                        ctx.fillRect(norm.x - 80, 220, 160, 60);
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 12px Microsoft YaHei';
                        ctx.fillText('实例:', norm.x, 240);
                        ctx.font = '11px Microsoft YaHei';
                        ctx.fillText(norm.example, norm.x, 260);
                    }
                });

                if (step >= 4) {
                    // 绘制连接箭头
                    drawArrow(ctx, 230, 140, 300, 140, '#4CAF50');
                    drawArrow(ctx, 500, 140, 570, 140, '#4CAF50');

                    // 绘制进化说明
                    ctx.fillStyle = '#4CAF50';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('逐步优化', 265, 125);
                    ctx.fillText('逐步优化', 535, 125);
                }

                if (step >= 5) {
                    // 绘制底部总结
                    ctx.fillStyle = '#333';
                    ctx.font = 'bold 16px Microsoft YaHei';
                    ctx.fillText('设计原则：减少冗余 → 提高一致性 → 便于维护', canvas.width / 2, 320);

                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillStyle = '#666';
                    ctx.fillText('实际应用中可根据性能需求适当权衡', canvas.width / 2, 350);
                }

                step += 0.03;
                if (step < 6) {
                    requestAnimationFrame(animate);
                }
            };

            animate();
        }

        // 开始测试
        function startQuiz() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制测试界面
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#667eea';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 互动测试', canvas.width / 2, 50);

            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('点击下面的表格，判断它们违反了哪个范式：', canvas.width / 2, 100);

            // 绘制测试表格示例
            const testTable = [
                ['学生ID', '课程', '教师', '教师电话'],
                ['001', '数学,英语', '张老师', '13800138000'],
                ['002', '物理', '李老师', '13900139000']
            ];

            ctx.fillStyle = '#ff9800';
            ctx.fillRect(200, 130, 400, 100);

            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';

            testTable.forEach((row, rowIndex) => {
                row.forEach((cell, cellIndex) => {
                    const x = 250 + cellIndex * 90;
                    const y = 155 + rowIndex * 25;
                    ctx.fillText(cell, x, y);
                });
            });

            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('这个表违反了第几范式？', canvas.width / 2, 270);

            // 添加点击提示
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.fillText('提示：观察"课程"列的数据特点', canvas.width / 2, 320);

            // 添加点击事件监听器
            canvas.onclick = function(event) {
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                // 检查是否点击了表格区域
                if (x >= 200 && x <= 600 && y >= 130 && y <= 230) {
                    // 显示答案
                    ctx.fillStyle = '#4CAF50';
                    ctx.font = 'bold 18px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('✅ 正确！违反了第一范式', canvas.width / 2, 360);
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('因为"课程"列包含多个值，不符合原子性要求', canvas.width / 2, 385);

                    // 更新分数
                    score += 10;
                    document.getElementById('score').textContent = Math.round(score);
                }
            };
        }

        // 重置总结
        function resetSummary() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            canvas.onclick = null; // 移除点击事件
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingElements();
            updateProgress();

            // 为所有canvas添加默认的点击效果
            document.querySelectorAll('canvas').forEach(canvas => {
                canvas.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                });

                canvas.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 自动播放概述动画
            setTimeout(() => {
                animateOverview();
            }, 1000);
        });
    </script>
</body>
</html>
