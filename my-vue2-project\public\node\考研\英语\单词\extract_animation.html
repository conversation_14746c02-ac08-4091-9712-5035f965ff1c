<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Extract</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            background-color: #f0f2f5;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 900px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .story-explanation {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            width: 100%;
            border-left: 5px solid #fd7e14;
        }

        .story-explanation p {
            margin: 0;
            line-height: 1.6;
            color: #555;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-radius: 8px;
            overflow: hidden;
        }

        canvas {
            display: block;
            width: 100%;
            height: auto;
            background-color: #ffffff;
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        button {
            padding: 10px 20px;
            font-size: 1em;
            color: #fff;
            background-color: #fd7e14;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        button:hover {
            background-color: #e86a0c;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }

        #explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3e0;
            border-radius: 8px;
            width: 100%;
            text-align: center;
            font-size: 1.2em;
            color: #333;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.5s;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>
        extract
        <span style="font-size: 0.5em; color: #555;">(ex- + tract)</span>
    </h1>

    <div class="story-explanation">
        <p><strong>故事背景：</strong>想象一下，在一个炎热的夏日，我们想喝一杯清凉的橙汁。这个过程，恰好完美地诠释了 "extract" 这个词的含义。让我们看看如何从一个完整的橙子中，"提取"出美味的果汁。</p>
    </div>

    <div class="canvas-container">
        <canvas id="wordAnimation" width="800" height="450"></canvas>
    </div>

    <div class="controls">
        <button id="playBtn">播放完整动画</button>
        <button id="exBtn">第一幕: ex- (出)</button>
        <button id="tractBtn">第二幕: tract (拉)</button>
        <button id="extractBtn">第三幕: extract (提取)</button>
        <button id="resetBtn">重置</button>
    </div>

    <div id="explanation">
        <p>点击按钮，开始探索 "extract" 的含义吧！</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordAnimation');
    const ctx = canvas.getContext('2d');
    const explanationDiv = document.getElementById('explanation');

    const playBtn = document.getElementById('playBtn');
    const exBtn = document.getElementById('exBtn');
    const tractBtn = document.getElementById('tractBtn');
    const extractBtn = document.getElementById('extractBtn');
    const resetBtn = document.getElementById('resetBtn');

    let animationFrameId;

    const colors = {
        background: '#ffffff',
        text: '#333333',
        orange: '#FFA500',
        orangeDark: '#cc8400',
        juice: '#FFD700',
        juicer: '#cccccc',
        juicerDark: '#999999'
    };
    const fonts = {
        title: 'bold 36px Arial',
        text: '24px Arial',
        chinese: '20px "Microsoft YaHei", sans-serif'
    };

    function drawOrange(x, y) {
        // Orange body
        ctx.fillStyle = colors.orange;
        ctx.beginPath();
        ctx.arc(x, y, 50, 0, Math.PI * 2);
        ctx.fill();
        // Orange stem
        ctx.fillStyle = '#6B8E23';
        ctx.beginPath();
        ctx.moveTo(x, y - 50);
        ctx.lineTo(x + 5, y - 60);
        ctx.lineTo(x, y - 55);
        ctx.fill();
    }
    
    function drawJuicer(progress) {
        const juicerX = canvas.width / 2;
        const juicerTopY = 50;
        const juicerBottomY = 200 + (1-progress) * 100; // Juicer moves down

        ctx.fillStyle = colors.juicer;
        ctx.fillRect(juicerX - 60, juicerTopY, 120, 50); // Top part
        ctx.fillRect(juicerX - 10, juicerTopY + 50, 20, juicerBottomY - juicerTopY -50); // Connector
        
        ctx.fillStyle = colors.juicerDark;
        ctx.beginPath();
        ctx.moveTo(juicerX - 50, juicerBottomY);
        ctx.lineTo(juicerX + 50, juicerBottomY);
        ctx.lineTo(juicerX, juicerBottomY + 30);
        ctx.closePath();
        ctx.fill();
    }

    function drawJuice(progress) {
        if (progress <= 0) return;
        ctx.fillStyle = colors.juice;
        ctx.beginPath();
        ctx.moveTo(canvas.width / 2 - 5, 300);
        ctx.lineTo(canvas.width / 2 + 5, 300);
        ctx.lineTo(canvas.width / 2 + 5, 300 + progress * 100);
        ctx.lineTo(canvas.width / 2 - 5, 300 + progress * 100);
        ctx.closePath();
        ctx.fill();
        
        // Glass
        ctx.strokeStyle = colors.juicer;
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(canvas.width / 2 - 40, 420);
        ctx.lineTo(canvas.width / 2 - 30, 320);
        ctx.lineTo(canvas.width / 2 + 30, 320);
        ctx.lineTo(canvas.width / 2 + 40, 420);
        ctx.stroke();
        
        // Juice in glass
        const juiceHeight = progress * 95;
        ctx.beginPath();
        ctx.moveTo(canvas.width / 2 - 30 + (1-progress)*5, 420 - juiceHeight);
        ctx.fillRect(canvas.width / 2 - 30, 420-juiceHeight, 60, juiceHeight);
    }


    // 初始状态
    function drawInitialState() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = colors.background;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = colors.text;
        ctx.font = fonts.title;
        ctx.textAlign = 'center';
        ctx.fillText('extract', canvas.width / 2, 50);

        drawOrange(canvas.width / 2, 250);
        drawJuicer(0);
        drawJuice(0);
        
        explanationDiv.innerHTML = '<p>点击按钮，开始探索 "extract" 的含义吧！</p>';
    }
    
    function resetAnimation() {
        cancelAnimationFrame(animationFrameId);
        drawInitialState();
    }

    // 第一幕: ex-
    function animateEx() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>ex- (出来)</strong>: 想象榨汁机代表一种力量，它的目标是让橙子里面的东西<strong>出来</strong>。</p>';
        
        let progress = 0;
        function animate() {
            resetAnimation();
            ctx.globalAlpha = progress;
            ctx.font = "bold 80px Arial";
            ctx.fillStyle = colors.orange;
            ctx.fillText("→", canvas.width / 2 + 100, 260);
            ctx.globalAlpha = 1;

            if (progress < 1) {
                progress += 0.02;
                animationFrameId = requestAnimationFrame(animate);
            }
        }
        animate();
    }

    // 第二幕: tract
    function animateTract() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>tract (拉)</strong>: 榨汁机开始工作，向下施压，就像一只手在用力<strong>"拉"</strong>出橙子的精华。</p>';
        
        let progress = 0;
        function animate() {
            resetAnimation();
            drawJuicer(progress);
            if (progress < 1) {
                progress += 0.01;
                animationFrameId = requestAnimationFrame(animate);
            } else {
                 drawJuicer(1);
            }
        }
        animate();
    }

    // 第三幕: extract
    function animateExtract() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>extract (提取)</strong>: 最终，果汁从橙子中被成功<strong>提取</strong>出来，装满了杯子。这就是 "extract"！</p>';

        let juicerProgress = 0;
        let juiceProgress = 0;

        function animate() {
             resetAnimation();
             drawJuicer(juicerProgress);
             drawJuice(juiceProgress);

            if (juicerProgress < 1) {
                juicerProgress += 0.01;
            } else {
                if(juiceProgress < 1) {
                    juiceProgress += 0.01;
                }
            }
            
            if (juiceProgress < 1) {
                animationFrameId = requestAnimationFrame(animate);
            }
        }
        animate();
    }
    
    async function playAll() {
        resetAnimation();
        
        animateEx();
        await new Promise(r => setTimeout(r, 2000));
        
        animateTract();
        await new Promise(r => setTimeout(r, 2000));

        animateExtract();
    }

    // Event Listeners
    playBtn.addEventListener('click', playAll);
    exBtn.addEventListener('click', animateEx);
    tractBtn.addEventListener('click', animateTract);
    extractBtn.addEventListener('click', animateExtract);
    resetBtn.addEventListener('click', resetAnimation);

    // Initial draw
    drawInitialState();
</script>

</body>
</html> 