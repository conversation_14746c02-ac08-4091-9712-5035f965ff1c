<!DOCTYPE html>
<html>
<head>
<title>Transmit Animation</title>
<meta charset="UTF-8">
<style>
    body {
        font-family: Arial, sans-serif;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: #f0f8ff;
    }
    #canvas {
        border: 1px solid #000;
        background-color: #ffffff;
    }
    #explanation {
        width: 800px;
        text-align: left;
        margin-top: 20px;
    }
    #controls {
        margin-top: 10px;
    }
    button {
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
    }
</style>
</head>
<body>

<h1>Word Animation: Transmit (传送)</h1>
<canvas id="canvas" width="800" height="400"></canvas>
<div id="controls">
    <button id="playBtn">Play Animation</button>
</div>
<div id="explanation">
    <h2>Transmit (传送) = trans- (横跨) + mit (发送)</h2>
    <p><b>故事:</b> 想象一下，在一个古老的王国里，一位将军（在左边）需要把一条紧急军情（一封信）发送给远方另一座城堡里的国王（在右边）。这个过程就是 "transmit"。</p>
    <p><b>trans- (横跨):</b> 信使需要翻山越岭，<b>横跨</b>整个王国，才能把信送到。动画中，我们会看到信使穿越山脉和河流的场景。</p>
    <p><b>mit (发送):</b> 将军把信<b>发送</b>出去的那一刻，以及国王收到信的那一刻，都体现了 "mit" (发送) 的含义。</p>
    <p><b>交互:</b> 点击 "Play Animation" 按钮，开始这个传送紧急军情的故事。</p>
</div>

<script>
const canvas = document.getElementById('canvas');
const ctx = canvas.getContext('2d');
const playBtn = document.getElementById('playBtn');

let animationId;
let progress = 0; // 0 to 1

const sender = { x: 50, y: 300 };
const receiver = { x: 750, y: 300 };
const message = { x: sender.x + 20, y: sender.y - 40, width: 30, height: 20 };

function drawPerson(x, y, label) {
    // Body
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x, y - 40);
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 3;
    ctx.stroke();

    // Head
    ctx.beginPath();
    ctx.arc(x, y - 50, 10, 0, Math.PI * 2);
    ctx.fillStyle = 'black';
    ctx.fill();
    
    // Arms
    ctx.beginPath();
    ctx.moveTo(x - 15, y - 30);
    ctx.lineTo(x + 15, y - 30);
    ctx.stroke();

    // Label
    ctx.fillStyle = 'black';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(label, x, y + 20);
}

function drawLandscape() {
    // Ground
    ctx.fillStyle = '#8B4513'; // Brown
    ctx.fillRect(0, 310, canvas.width, 90);

    // Mountains
    ctx.fillStyle = '#A9A9A9'; // Gray
    ctx.beginPath();
    ctx.moveTo(150, 310);
    ctx.lineTo(250, 150);
    ctx.lineTo(350, 310);
    ctx.closePath();
    ctx.fill();

    ctx.beginPath();
    ctx.moveTo(300, 310);
    ctx.lineTo(400, 100);
    ctx.lineTo(500, 310);
    ctx.closePath();
    ctx.fill();

    // River
    ctx.fillStyle = '#1E90FF'; // DodgerBlue
    ctx.beginPath();
    ctx.moveTo(550, 310);
    ctx.bezierCurveTo(580, 350, 620, 350, 650, 310);
    ctx.lineTo(630, 400);
    ctx.lineTo(570, 400);
    ctx.closePath();
    ctx.fill();
}

function drawMessage(x, y) {
    ctx.fillStyle = '#FFD700'; // Gold
    ctx.fillRect(x, y, message.width, message.height);
    ctx.strokeStyle = 'black';
    ctx.strokeRect(x, y, message.width, message.height);
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x + message.width / 2, y + message.height / 2);
    ctx.lineTo(x + message.width, y);
    ctx.stroke();
}

function drawArrow(startX, startY, endX, endY, text) {
    const headlen = 10;
    const angle = Math.atan2(endY - startY, endX - startX);
    
    ctx.beginPath();
    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.strokeStyle = 'red';
    ctx.lineWidth = 2;
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(endX, endY);
    ctx.lineTo(endX - headlen * Math.cos(angle - Math.PI / 6), endY - headlen * Math.sin(angle - Math.PI / 6));
    ctx.lineTo(endX - headlen * Math.cos(angle + Math.PI / 6), endY - headlen * Math.sin(angle + Math.PI / 6));
    ctx.lineTo(endX, endY);
    ctx.fillStyle = 'red';
    ctx.fill();

    ctx.font = '24px Arial';
    ctx.fillStyle = 'red';
    ctx.textAlign = 'center';
    ctx.fillText(text, (startX + endX) / 2, startY - 20);
}


function animate() {
    progress += 0.005;
    if (progress > 1) {
        progress = 1;
    }

    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw elements
    drawLandscape();
    drawPerson(sender.x, sender.y, '将军 (Sender)');
    drawPerson(receiver.x, receiver.y, '国王 (Receiver)');

    // Message position
    const messageX = sender.x + 20 + (receiver.x - sender.x - 50) * progress;
    const pathY = 250; 
    
    // Arc path for the message
    const baseY = 270;
    const arcHeight = -150;
    const messageY = baseY + Math.sin(progress * Math.PI) * arcHeight;
    
    drawMessage(messageX, messageY);

    // Draw annotations
    if (progress > 0.01 && progress < 0.99) {
        drawArrow(sender.x + 50, pathY, receiver.x - 50, pathY, 'trans- (横跨)');
        
        ctx.font = '20px Arial';
        ctx.fillStyle = 'blue';
        ctx.textAlign = 'center';
        ctx.fillText('正在传送信息...', canvas.width/2, 50);

    }

    if (progress < 0.1) {
        ctx.font = '24px Arial';
        ctx.fillStyle = 'purple';
        ctx.textAlign = 'left';
        ctx.fillText('mit (发送)', sender.x + 50, sender.y - 80);
    }

    if (progress > 0.9) {
        ctx.font = '24px Arial';
        ctx.fillStyle = 'purple';
        ctx.textAlign = 'right';
        ctx.fillText('mit (收到/发送完成)', receiver.x - 50, receiver.y - 80);
    }
    
    if (progress >= 1) {
        cancelAnimationFrame(animationId);
        playBtn.disabled = false;
        playBtn.textContent = "Play Again";
    } else {
        animationId = requestAnimationFrame(animate);
    }
}

function startAnimation() {
    if (animationId) {
        cancelAnimationFrame(animationId);
    }
    progress = 0;
    playBtn.disabled = true;
    playBtn.textContent = "Animating...";
    animate();
}

playBtn.addEventListener('click', startAnimation);

// Initial draw
ctx.clearRect(0, 0, canvas.width, canvas.height);
drawLandscape();
drawPerson(sender.x, sender.y, '将军 (Sender)');
drawPerson(receiver.x, receiver.y, '国王 (Receiver)');
drawMessage(message.x, message.y);


</script>

</body>
</html> 