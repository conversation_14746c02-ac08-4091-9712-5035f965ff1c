<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式三大分类 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.3rem;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .pattern-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .category-card {
            background: white;
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            transition: all 0.4s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .category-card:hover::before {
            left: 100%;
        }

        .category-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 35px 70px rgba(0,0,0,0.2);
        }

        .category-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .category-icon {
            font-size: 4rem;
            margin-bottom: 15px;
            display: block;
        }

        .creational { color: #4CAF50; }
        .structural { color: #2196F3; }
        .behavioral { color: #FF9800; }

        .category-title {
            font-size: 1.8rem;
            margin-bottom: 10px;
            color: #333;
        }

        .category-subtitle {
            color: #666;
            font-size: 1rem;
            margin-bottom: 20px;
        }

        .pattern-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .pattern-item {
            background: #f8f9ff;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .pattern-item:hover {
            background: #e3f2fd;
            transform: scale(1.05);
            border-color: #2196F3;
        }

        .canvas-container {
            background: white;
            border-radius: 25px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }

        canvas {
            border: 3px solid #e0e0e0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            border-color: #667eea;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            border-radius: 25px;
            padding: 40px;
            color: white;
            text-align: center;
            margin-top: 40px;
        }

        .quiz-question {
            font-size: 1.4rem;
            margin-bottom: 30px;
            line-height: 1.6;
            background: rgba(255,255,255,0.1);
            padding: 25px;
            border-radius: 15px;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .quiz-option {
            background: rgba(255,255,255,0.2);
            padding: 25px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
            backdrop-filter: blur(10px);
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .quiz-option.correct {
            background: #4CAF50;
            border-color: #45a049;
            animation: correctPulse 0.6s ease;
        }

        .quiz-option.wrong {
            background: #f44336;
            border-color: #da190b;
            animation: wrongShake 0.6s ease;
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .progress-container {
            background: rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 10px;
            margin: 20px 0;
        }

        .progress-bar {
            height: 12px;
            background: rgba(255,255,255,0.3);
            border-radius: 6px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 1s ease;
            border-radius: 6px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .explanation-panel {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .pattern-demo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .pattern-demo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .pattern-demo-btn.active {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="floating-particles">
        <canvas id="particleCanvas"></canvas>
    </div>

    <div class="container">
        <div class="header">
            <h1>🎨 设计模式三大分类</h1>
            <p>通过生动的动画和交互，轻松掌握创建型、结构型、行为型设计模式的核心概念！</p>
        </div>

        <!-- 三大模式分类展示 -->
        <div class="pattern-categories">
            <div class="category-card" data-type="creational">
                <div class="category-header">
                    <span class="category-icon creational">🏗️</span>
                    <h2 class="category-title">创建型模式</h2>
                    <p class="category-subtitle">Creational Patterns</p>
                </div>
                <p><strong>目的：</strong>封装对象的创建过程，使系统独立于对象的创建、组合和表示</p>
                <div class="pattern-examples">
                    <div class="pattern-item">
                        <div>🔒</div>
                        <small>Singleton</small>
                    </div>
                    <div class="pattern-item">
                        <div>🏭</div>
                        <small>Factory</small>
                    </div>
                    <div class="pattern-item">
                        <div>👷</div>
                        <small>Builder</small>
                    </div>
                    <div class="pattern-item">
                        <div>🎭</div>
                        <small>Prototype</small>
                    </div>
                </div>
            </div>

            <div class="category-card" data-type="structural">
                <div class="category-header">
                    <span class="category-icon structural">🔧</span>
                    <h2 class="category-title">结构型模式</h2>
                    <p class="category-subtitle">Structural Patterns</p>
                </div>
                <p><strong>目的：</strong>处理类和对象的组合，形成更大的结构</p>
                <div class="pattern-examples">
                    <div class="pattern-item">
                        <div>🔌</div>
                        <small>Adapter</small>
                    </div>
                    <div class="pattern-item">
                        <div>🎨</div>
                        <small>Decorator</small>
                    </div>
                    <div class="pattern-item">
                        <div>🪶</div>
                        <small>Flyweight</small>
                    </div>
                    <div class="pattern-item">
                        <div>🌉</div>
                        <small>Bridge</small>
                    </div>
                </div>
            </div>

            <div class="category-card" data-type="behavioral">
                <div class="category-header">
                    <span class="category-icon behavioral">⚡</span>
                    <h2 class="category-title">行为型模式</h2>
                    <p class="category-subtitle">Behavioral Patterns</p>
                </div>
                <p><strong>目的：</strong>关注对象间的职责分配和通信</p>
                <div class="pattern-examples">
                    <div class="pattern-item">
                        <div>👁️</div>
                        <small>Observer</small>
                    </div>
                    <div class="pattern-item">
                        <div>📋</div>
                        <small>Command</small>
                    </div>
                    <div class="pattern-item">
                        <div>🚶</div>
                        <small>Visitor</small>
                    </div>
                    <div class="pattern-item">
                        <div>🔄</div>
                        <small>Strategy</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 动画演示区域 -->
        <div class="canvas-container">
            <h2 style="margin-bottom: 20px; color: #333;">🎬 设计模式动画演示</h2>
            <p style="margin-bottom: 20px; color: #666;">点击上方的模式分类卡片，观看对应的动画演示</p>
            <canvas id="patternCanvas" width="1000" height="500"></canvas>
        </div>

        <!-- 知识解析 -->
        <div class="explanation-panel">
            <h3>💡 核心知识点</h3>
            <p>设计模式按照<span class="highlight">目的</span>可以分为三大类：</p>
            <ul style="margin: 15px 0; padding-left: 30px; line-height: 1.8;">
                <li><strong>创建型模式</strong>：关注对象的创建过程，如 <span class="highlight">Singleton</span>（单例模式）确保类只有一个实例</li>
                <li><strong>结构型模式</strong>：关注类和对象的组合，如 <span class="highlight">Adapter</span>（适配器模式）让不兼容的接口协同工作</li>
                <li><strong>行为型模式</strong>：关注对象间的职责和通信，如 <span class="highlight">Visitor</span>（访问者模式）分离算法与数据结构</li>
            </ul>
        </div>

        <!-- 结构型模式重点学习 -->
        <div class="explanation-panel" style="background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%); color: white;">
            <h3>🔧 结构型模式深度解析</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                    <h4>🎯 核心目的</h4>
                    <p>处理类和对象的<span style="background: rgba(255,255,255,0.3); padding: 2px 6px; border-radius: 4px;">组合关系</span>，形成更大、更复杂的结构</p>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                    <h4>🔗 关键特征</h4>
                    <p>通过<span style="background: rgba(255,255,255,0.3); padding: 2px 6px; border-radius: 4px;">继承和组合</span>机制，将简单对象组合成复杂对象</p>
                </div>
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                    <h4>📋 常见模式</h4>
                    <p>Adapter、Decorator、Facade、Bridge、Composite、Flyweight、Proxy</p>
                </div>
            </div>
        </div>

        <!-- 结构型模式动画演示 -->
        <div class="canvas-container">
            <h2 style="margin-bottom: 20px; color: #333;">🏗️ 结构型模式构建过程</h2>
            <canvas id="structuralCanvas" width="1000" height="400"></canvas>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px;">
                <button class="pattern-demo-btn" data-pattern="adapter">🔌 Adapter 适配器</button>
                <button class="pattern-demo-btn" data-pattern="decorator">🎨 Decorator 装饰器</button>
                <button class="pattern-demo-btn" data-pattern="facade">🏛️ Facade 外观</button>
                <button class="pattern-demo-btn" data-pattern="composite">🌳 Composite 组合</button>
            </div>
        </div>

        <!-- 测验部分 -->
        <div class="quiz-section">
            <h2 style="margin-bottom: 30px;">🎯 知识测验</h2>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
            <div class="quiz-question">
                按照设计模式的目的进行划分，（ ）模式主要用于如何组合已有的类和对象以获得更大的结构，其代表有 Adapter 模式等？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" data-answer="A">
                    <h3>A. 合成型</h3>
                    <p>❌ 不是标准的设计模式分类</p>
                    <small>这个术语在设计模式中不存在</small>
                </div>
                <div class="quiz-option" data-answer="B">
                    <h3>B. 组合型</h3>
                    <p>❌ 容易混淆的错误选项</p>
                    <small>虽然涉及组合，但不是正确分类名</small>
                </div>
                <div class="quiz-option" data-answer="C">
                    <h3>C. 结构型</h3>
                    <p>✅ 正确答案！</p>
                    <small>专门处理类和对象的组合结构</small>
                </div>
                <div class="quiz-option" data-answer="D">
                    <h3>D. 聚合型</h3>
                    <p>❌ 这是面向对象的关系概念</p>
                    <small>聚合是类关系，不是模式分类</small>
                </div>
            </div>

            <!-- 答案解析 -->
            <div id="answerExplanation" style="display: none; background: rgba(255,255,255,0.1); padding: 25px; border-radius: 15px; margin-top: 30px;">
                <h3>📖 详细解析</h3>
                <div style="text-align: left; line-height: 1.8;">
                    <p><strong>正确答案：C. 结构型</strong></p>
                    <ul style="margin: 15px 0; padding-left: 30px;">
                        <li><strong>结构型模式</strong>：专门处理类和对象的组合，形成更大的结构</li>
                        <li><strong>核心思想</strong>：通过组合现有组件创建新的功能结构</li>
                        <li><strong>典型代表</strong>：Adapter（适配器）、Decorator（装饰器）、Facade（外观）等</li>
                        <li><strong>易错分析</strong>：
                            <ul style="margin-top: 10px;">
                                <li>合成型、聚合型：不是标准的设计模式分类术语</li>
                                <li>组合型：虽然涉及组合概念，但正确术语是"结构型"</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        class DesignPatternLearning {
            constructor() {
                this.initCanvases();
                this.initInteractions();
                this.startAnimations();
                this.currentPattern = null;
                this.currentStructuralPattern = null;
            }

            initCanvases() {
                // 主画布
                this.canvas = document.getElementById('patternCanvas');
                this.ctx = this.canvas.getContext('2d');
                
                // 粒子背景画布
                this.particleCanvas = document.getElementById('particleCanvas');
                this.particleCtx = this.particleCanvas.getContext('2d');
                this.resizeParticleCanvas();
                
                this.animationFrame = 0;
                this.particles = this.createParticles();
            }

            resizeParticleCanvas() {
                this.particleCanvas.width = window.innerWidth;
                this.particleCanvas.height = window.innerHeight;
            }

            createParticles() {
                const particles = [];
                for (let i = 0; i < 50; i++) {
                    particles.push({
                        x: Math.random() * window.innerWidth,
                        y: Math.random() * window.innerHeight,
                        vx: (Math.random() - 0.5) * 0.5,
                        vy: (Math.random() - 0.5) * 0.5,
                        size: Math.random() * 3 + 1,
                        opacity: Math.random() * 0.5 + 0.1
                    });
                }
                return particles;
            }

            initInteractions() {
                // 分类卡片交互
                const categoryCards = document.querySelectorAll('.category-card');
                categoryCards.forEach(card => {
                    card.addEventListener('click', () => {
                        categoryCards.forEach(c => c.style.transform = '');
                        card.style.transform = 'scale(1.05)';
                        this.currentPattern = card.dataset.type;
                        this.animatePattern(this.currentPattern);
                    });
                });

                // 结构型模式演示按钮
                const demoBtns = document.querySelectorAll('.pattern-demo-btn');
                demoBtns.forEach(btn => {
                    btn.addEventListener('click', () => {
                        demoBtns.forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        this.currentStructuralPattern = btn.dataset.pattern;
                        this.animateStructuralPattern(this.currentStructuralPattern);
                    });
                });

                // 测验交互
                const quizOptions = document.querySelectorAll('.quiz-option');
                quizOptions.forEach(option => {
                    option.addEventListener('click', () => {
                        this.handleQuizAnswer(option);
                    });
                });

                // 窗口大小变化
                window.addEventListener('resize', () => {
                    this.resizeParticleCanvas();
                });
            }

            startAnimations() {
                this.animate();
            }

            animate() {
                this.animationFrame++;
                this.drawParticles();
                this.drawPatternAnimation();
                requestAnimationFrame(() => this.animate());
            }

            drawParticles() {
                const ctx = this.particleCtx;
                ctx.clearRect(0, 0, this.particleCanvas.width, this.particleCanvas.height);
                
                this.particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    
                    if (particle.x < 0 || particle.x > this.particleCanvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > this.particleCanvas.height) particle.vy *= -1;
                    
                    ctx.globalAlpha = particle.opacity;
                    ctx.fillStyle = '#ffffff';
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fill();
                });
                
                ctx.globalAlpha = 1;
            }

            drawPatternAnimation() {
                const ctx = this.ctx;
                const canvas = this.canvas;
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                if (!this.currentPattern) {
                    this.drawWelcomeAnimation();
                    return;
                }
                
                switch (this.currentPattern) {
                    case 'creational':
                        this.drawCreationalAnimation();
                        break;
                    case 'structural':
                        this.drawStructuralAnimation();
                        break;
                    case 'behavioral':
                        this.drawBehavioralAnimation();
                        break;
                }
            }

            drawWelcomeAnimation() {
                const ctx = this.ctx;
                const time = this.animationFrame * 0.02;
                
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 36px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎨 点击上方卡片开始学习', 500, 200);
                
                ctx.fillStyle = '#764ba2';
                ctx.font = '20px Arial';
                ctx.fillText('Design Patterns Learning', 500, 250);
                
                // 动态图标
                const icons = ['🏗️', '🔧', '⚡'];
                icons.forEach((icon, i) => {
                    const x = 350 + i * 100;
                    const y = 350 + Math.sin(time + i) * 20;
                    ctx.font = '60px Arial';
                    ctx.fillText(icon, x, y);
                });
            }

            drawCreationalAnimation() {
                const ctx = this.ctx;
                const time = this.animationFrame * 0.03;
                
                // 标题
                ctx.fillStyle = '#4CAF50';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🏗️ 创建型模式 - 对象创建过程', 500, 50);
                
                // Singleton 演示
                ctx.fillStyle = '#333';
                ctx.font = '20px Arial';
                ctx.fillText('Singleton 单例模式演示', 500, 100);
                
                // 绘制单例对象
                const centerX = 500;
                const centerY = 200;
                
                ctx.fillStyle = '#4CAF50';
                ctx.beginPath();
                ctx.arc(centerX, centerY, 40 + Math.sin(time) * 5, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = '24px Arial';
                ctx.fillText('🔒', centerX, centerY + 8);
                
                // 多个请求指向同一个实例
                const requests = 6;
                for (let i = 0; i < requests; i++) {
                    const angle = (i / requests) * Math.PI * 2 + time;
                    const x = centerX + Math.cos(angle) * 150;
                    const y = centerY + Math.sin(angle) * 150;
                    
                    ctx.fillStyle = '#2196F3';
                    ctx.beginPath();
                    ctx.arc(x, y, 15, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 连接线
                    ctx.strokeStyle = '#4CAF50';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(x, y);
                    ctx.lineTo(centerX, centerY);
                    ctx.stroke();
                }
                
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText('多个请求都指向同一个实例', 500, 400);
            }

            drawStructuralAnimation() {
                const ctx = this.ctx;
                const time = this.animationFrame * 0.03;
                
                ctx.fillStyle = '#2196F3';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🔧 结构型模式 - 组合现有结构', 500, 50);
                
                ctx.fillStyle = '#333';
                ctx.font = '20px Arial';
                ctx.fillText('Adapter 适配器模式演示', 500, 100);
                
                // 不兼容的接口
                ctx.fillStyle = '#f44336';
                ctx.fillRect(150, 180, 100, 80);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText('旧接口', 200, 225);
                
                // 适配器
                ctx.fillStyle = '#FF9800';
                ctx.fillRect(350, 180, 100, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('适配器', 400, 225);
                
                // 新接口
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(550, 180, 100, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('新接口', 600, 225);
                
                // 连接动画
                const offset = Math.sin(time) * 10;
                ctx.strokeStyle = '#2196F3';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(250, 220);
                ctx.lineTo(350 + offset, 220);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(450, 220);
                ctx.lineTo(550 - offset, 220);
                ctx.stroke();
                
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText('让不兼容的接口能够协同工作', 500, 320);
            }

            drawBehavioralAnimation() {
                const ctx = this.ctx;
                const time = this.animationFrame * 0.03;
                
                ctx.fillStyle = '#FF9800';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('⚡ 行为型模式 - 对象间通信', 500, 50);
                
                ctx.fillStyle = '#333';
                ctx.font = '20px Arial';
                ctx.fillText('Observer 观察者模式演示', 500, 100);
                
                // 主题对象
                const subjectX = 500;
                const subjectY = 200;
                
                ctx.fillStyle = '#FF9800';
                ctx.beginPath();
                ctx.arc(subjectX, subjectY, 30, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = '20px Arial';
                ctx.fillText('📢', subjectX, subjectY + 6);
                
                // 观察者
                const observers = 8;
                for (let i = 0; i < observers; i++) {
                    const angle = (i / observers) * Math.PI * 2;
                    const distance = 120 + Math.sin(time + i) * 20;
                    const x = subjectX + Math.cos(angle) * distance;
                    const y = subjectY + Math.sin(angle) * distance;
                    
                    ctx.fillStyle = '#4CAF50';
                    ctx.beginPath();
                    ctx.arc(x, y, 20, 0, Math.PI * 2);
                    ctx.fill();
                    
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.fillText('👁️', x, y + 5);
                    
                    // 通知波纹
                    const waveRadius = (time * 50 + i * 20) % 100;
                    ctx.strokeStyle = `rgba(255, 152, 0, ${1 - waveRadius / 100})`;
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.arc(subjectX, subjectY, waveRadius, 0, Math.PI * 2);
                    ctx.stroke();
                }
                
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText('主题状态改变时通知所有观察者', 500, 350);
            }

            animatePattern(type) {
                this.currentPattern = type;
            }

            handleQuizAnswer(selectedOption) {
                const correctAnswer = 'D';
                const userAnswer = selectedOption.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                document.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (userAnswer === correctAnswer) {
                    selectedOption.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！Singleton 是创建型模式的代表，确保类只有一个实例！');
                    }, 600);
                } else {
                    selectedOption.classList.add('wrong');
                    document.querySelector(`[data-answer="${correctAnswer}"]`).classList.add('correct');
                    setTimeout(() => {
                        alert('💡 正确答案是 D. Singleton！\n\n创建型模式关注对象创建，Singleton确保类只有一个实例。\nDecorator和Flyweight是结构型模式，Command是行为型模式。');
                    }, 600);
                }
            }
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            new DesignPatternLearning();
        });
    </script>
</body>
</html>
