<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL读写分离 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .pros-cons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .pros, .cons {
            padding: 20px;
            border-radius: 15px;
            transition: transform 0.3s ease;
        }

        .pros {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .cons {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .pros:hover, .cons:hover {
            transform: translateY(-5px);
        }

        .game-area {
            background: #f0f4ff;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .score {
            font-size: 1.5rem;
            color: #667eea;
            margin-bottom: 20px;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .highlight-box {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #e17055;
            box-shadow: 0 5px 15px rgba(225, 112, 85, 0.2);
        }

        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .floating-tips {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 250px;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .tip-title {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .header h1 { font-size: 2.5rem; }
            .section { padding: 20px; }
            .pros-cons { grid-template-columns: 1fr; }
            .floating-tips { display: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MySQL 读写分离</h1>
            <p>通过动画和交互学习数据库架构设计</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是读写分离？</h2>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <strong>读写分离</strong>是一种数据库架构设计模式，将数据库的读操作和写操作分配到不同的数据库服务器上。
                这种设计基于<strong>主从复制</strong>技术，主库负责写操作，从库负责读操作。
            </div>
            <button class="interactive-btn" onclick="startConceptAnimation()">开始动画演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">主从复制原理</h2>
            <div class="canvas-container">
                <canvas id="replicationCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                主从复制是读写分离的基础。主库执行写操作后，会将变更记录到二进制日志(binlog)中，
                从库通过读取这些日志来同步数据。<strong>重要提醒：</strong>从库只能读不能写，
                如果对从库执行写操作，会导致同步中断！
            </div>
            <button class="interactive-btn" onclick="startReplicationAnimation()">演示复制过程</button>
        </div>

        <div class="section">
            <h2 class="section-title">三种实现方案对比</h2>
            <div class="canvas-container">
                <canvas id="solutionsCanvas" width="800" height="500"></canvas>
            </div>
            <div class="game-area">
                <div class="score">选择最佳方案游戏 - 得分: <span id="gameScore">0</span></div>
                <p>根据不同场景选择最合适的读写分离方案</p>
                <button class="interactive-btn" onclick="startSolutionGame()">开始游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">方案一：MySQL-Proxy代理</h2>
            <div class="canvas-container">
                <canvas id="proxyCanvas" width="800" height="400"></canvas>
            </div>
            <div class="pros-cons">
                <div class="pros">
                    <h3>✅ 优点</h3>
                    <ul>
                        <li>直接实现读写分离</li>
                        <li>支持负载均衡</li>
                        <li>不需要修改应用代码</li>
                        <li>主从库使用相同账号</li>
                    </ul>
                </div>
                <div class="cons">
                    <h3>❌ 缺点</h3>
                    <ul>
                        <li>性能下降（多一层代理）</li>
                        <li>不支持事务</li>
                        <li>官方不建议生产使用</li>
                        <li>单点故障风险</li>
                    </ul>
                </div>
            </div>
            <button class="interactive-btn" onclick="startProxyAnimation()">演示代理方案</button>
        </div>

        <div class="section">
            <h2 class="section-title">方案二：ORM层实现</h2>
            <div class="canvas-container">
                <canvas id="ormCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                使用AbstractRoutingDataSource + AOP + 注解在DAO层决定数据源。
                通过MyBatis插件拦截SQL语句，所有INSERT/UPDATE/DELETE访问主库，
                所有SELECT访问从库。需要重写DataSourceTransactionManager支持事务。
            </div>
            <div class="pros-cons">
                <div class="pros">
                    <h3>✅ 优点</h3>
                    <ul>
                        <li>对DAO层透明</li>
                        <li>自动SQL语句分析</li>
                        <li>可以支持事务（需改造）</li>
                        <li>性能较好</li>
                    </ul>
                </div>
                <div class="cons">
                    <h3>❌ 缺点</h3>
                    <ul>
                        <li>默认不支持事务</li>
                        <li>需要改造事务管理器</li>
                        <li>复杂SQL可能误判</li>
                        <li>框架耦合度高</li>
                    </ul>
                </div>
            </div>
            <button class="interactive-btn" onclick="startOrmAnimation()">演示ORM方案</button>
        </div>

        <div class="section">
            <h2 class="section-title">方案三：Service层实现</h2>
            <div class="canvas-container">
                <canvas id="serviceCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                使用AbstractRoutingDataSource + AOP + 注解在Service层决定数据源。
                这是最推荐的方案，可以完美支持事务，但需要注意类内部方法调用的AOP拦截问题。
            </div>
            <div class="highlight-box">
                <strong>⚠️ 重要提醒：</strong>类内部通过this.method()调用时，AOP不会拦截！
                需要通过ApplicationContext获取代理对象或使用@Async等方式解决。
            </div>
            <div class="code-example">
@Service
public class UserService {

    @Transactional(readOnly = true)
    @TargetDataSource("slave")
    public User findById(Long id) {
        return userDao.findById(id);
    }

    @Transactional
    @TargetDataSource("master")
    public void updateUser(User user) {
        userDao.update(user);
        // 注意：this.findById(id) 不会切换数据源！
        // 应该使用：((UserService)AopContext.currentProxy()).findById(id)
    }
}
            </div>
            <div class="pros-cons">
                <div class="pros">
                    <h3>✅ 优点</h3>
                    <ul>
                        <li>完美支持事务</li>
                        <li>业务逻辑清晰</li>
                        <li>灵活性最高</li>
                        <li>易于维护和扩展</li>
                    </ul>
                </div>
                <div class="cons">
                    <h3>❌ 缺点</h3>
                    <ul>
                        <li>类内部this调用AOP失效</li>
                        <li>需要特殊处理内部调用</li>
                        <li>开发时需要注意注解使用</li>
                        <li>学习成本相对较高</li>
                    </ul>
                </div>
            </div>
            <button class="interactive-btn" onclick="startServiceAnimation()">演示Service方案</button>
        </div>

        <div class="section">
            <h2 class="section-title">知识测试游戏</h2>
            <div class="game-area">
                <div class="score">测试得分: <span id="testScore">0</span>/10</div>
                <div id="questionArea">
                    <p>点击开始测试按钮开始知识测试！</p>
                </div>
                <button class="interactive-btn" onclick="startKnowledgeTest()">开始知识测试</button>
                <button class="interactive-btn" onclick="resetTest()">重新测试</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">学习进度</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <p style="text-align: center; margin-top: 10px;">
                完成度: <span id="progressText">0%</span>
            </p>
            <div style="text-align: center; margin-top: 20px;">
                <button class="interactive-btn" onclick="updateProgress()">更新学习进度</button>
            </div>
        </div>
    </div>

    <!-- 浮动提示 -->
    <div class="floating-tips">
        <div class="tip-title">💡 学习提示</div>
        <div id="currentTip">点击画布可以重新播放动画！</div>
    </div>

    <script>
        // 全局变量
        let conceptAnimationRunning = false;
        let replicationAnimationRunning = false;
        let gameScore = 0;
        let testScore = 0;
        let currentQuestion = 0;
        let questions = [
            {
                question: "读写分离的基础技术是什么？",
                options: ["主从复制", "负载均衡", "数据分片", "缓存技术"],
                correct: 0
            },
            {
                question: "从库可以执行写操作吗？",
                options: ["可以", "不可以", "有条件可以", "看情况"],
                correct: 1
            },
            {
                question: "MySQL-Proxy方案的主要缺点是什么？",
                options: ["代码复杂", "不支持事务", "配置困难", "成本高"],
                correct: 1
            },
            {
                question: "哪种方案最适合生产环境？",
                options: ["MySQL-Proxy", "ORM层实现", "Service层实现", "都一样"],
                correct: 2
            },
            {
                question: "AOP拦截失效的场景是？",
                options: ["外部调用", "类内部this调用", "静态方法调用", "构造方法调用"],
                correct: 1
            }
        ];

        // 概念演示动画
        function startConceptAnimation() {
            if (conceptAnimationRunning) return;
            conceptAnimationRunning = true;
            
            const canvas = document.getElementById('conceptCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#f8f9ff');
                gradient.addColorStop(1, '#e8f0ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 绘制应用程序
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 50, 100, 80);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('应用程序', 100, 95);
                
                // 绘制主库
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(300, 50, 120, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('主库(写)', 360, 95);
                
                // 绘制从库
                ctx.fillStyle = '#fa709a';
                ctx.fillRect(500, 50, 120, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('从库(读)', 560, 95);
                
                // 动画箭头
                const writeArrowX = 150 + Math.sin(frame * 0.1) * 10;
                const readArrowX = 450 + Math.sin(frame * 0.1 + Math.PI) * 10;
                
                // 写操作箭头
                ctx.strokeStyle = '#4facfe';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(writeArrowX, 90);
                ctx.lineTo(300, 90);
                ctx.stroke();
                
                // 箭头头部
                ctx.beginPath();
                ctx.moveTo(295, 85);
                ctx.lineTo(300, 90);
                ctx.lineTo(295, 95);
                ctx.stroke();
                
                // 读操作箭头
                ctx.strokeStyle = '#fa709a';
                ctx.beginPath();
                ctx.moveTo(readArrowX, 110);
                ctx.lineTo(500, 110);
                ctx.stroke();
                
                // 箭头头部
                ctx.beginPath();
                ctx.moveTo(495, 105);
                ctx.lineTo(500, 110);
                ctx.lineTo(495, 115);
                ctx.stroke();
                
                // 同步箭头
                const syncY = 200 + Math.sin(frame * 0.15) * 5;
                ctx.strokeStyle = '#764ba2';
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(360, 130);
                ctx.lineTo(560, syncY);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // 标签
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText('写操作', 225, 85);
                ctx.fillText('读操作', 425, 105);
                ctx.fillText('数据同步', 460, syncY - 10);
                
                frame++;
                
                if (frame < 200) {
                    requestAnimationFrame(animate);
                } else {
                    conceptAnimationRunning = false;
                }
            }
            
            animate();
        }

        // 主从复制动画
        function startReplicationAnimation() {
            if (replicationAnimationRunning) return;
            replicationAnimationRunning = true;
            
            const canvas = document.getElementById('replicationCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            let dataPackets = [];
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#f0f4ff');
                gradient.addColorStop(1, '#e0e8ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 主库
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(100, 150, 150, 100);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('主库', 175, 200);
                ctx.font = '12px Arial';
                ctx.fillText('(Master)', 175, 220);
                
                // 从库
                ctx.fillStyle = '#fa709a';
                ctx.fillRect(550, 150, 150, 100);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText('从库', 625, 200);
                ctx.font = '12px Arial';
                ctx.fillText('(Slave)', 625, 220);
                
                // 生成数据包
                if (frame % 30 === 0) {
                    dataPackets.push({
                        x: 250,
                        y: 200,
                        targetX: 550,
                        targetY: 200,
                        progress: 0
                    });
                }
                
                // 更新和绘制数据包
                for (let i = dataPackets.length - 1; i >= 0; i--) {
                    const packet = dataPackets[i];
                    packet.progress += 0.02;
                    
                    if (packet.progress >= 1) {
                        dataPackets.splice(i, 1);
                        continue;
                    }
                    
                    // 贝塞尔曲线路径
                    const t = packet.progress;
                    const x = (1-t)*(1-t)*packet.x + 2*(1-t)*t*400 + t*t*packet.targetX;
                    const y = (1-t)*(1-t)*packet.y + 2*(1-t)*t*100 + t*t*packet.targetY;
                    
                    // 绘制数据包
                    ctx.fillStyle = '#764ba2';
                    ctx.beginPath();
                    ctx.arc(x, y, 8, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 数据包标签
                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('binlog', x, y + 3);
                }
                
                // 连接线
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.setLineDash([10, 5]);
                ctx.beginPath();
                ctx.moveTo(250, 200);
                ctx.quadraticCurveTo(400, 100, 550, 200);
                ctx.stroke();
                ctx.setLineDash([]);
                
                // 步骤说明
                const steps = [
                    '1. 主库执行写操作',
                    '2. 记录到binlog',
                    '3. 从库读取binlog',
                    '4. 从库应用变更'
                ];
                
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                steps.forEach((step, index) => {
                    const alpha = Math.max(0, Math.min(1, (frame - index * 50) / 50));
                    ctx.globalAlpha = alpha;
                    ctx.fillText(step, 50, 320 + index * 25);
                });
                ctx.globalAlpha = 1;
                
                frame++;
                
                if (frame < 300) {
                    requestAnimationFrame(animate);
                } else {
                    replicationAnimationRunning = false;
                }
            }
            
            animate();
        }

        // 方案对比动画
        function startSolutionGame() {
            const canvas = document.getElementById('solutionsCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#f8f9ff');
                gradient.addColorStop(1, '#e8f0ff');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 三个方案框
                const solutions = [
                    { name: 'MySQL-Proxy', x: 50, color: '#ff6b6b', score: 6 },
                    { name: 'ORM层实现', x: 300, color: '#4ecdc4', score: 7 },
                    { name: 'Service层实现', x: 550, color: '#45b7d1', score: 9 }
                ];

                solutions.forEach((solution, index) => {
                    const animOffset = Math.sin(frame * 0.05 + index) * 5;

                    // 方案框
                    ctx.fillStyle = solution.color;
                    ctx.fillRect(solution.x, 100 + animOffset, 200, 120);

                    // 方案名称
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(solution.name, solution.x + 100, 130 + animOffset);

                    // 评分星星
                    for (let i = 0; i < 10; i++) {
                        ctx.fillStyle = i < solution.score ? '#ffd700' : '#ddd';
                        ctx.font = '20px Arial';
                        ctx.fillText('★', solution.x + 20 + i * 16, 180 + animOffset);
                    }

                    // 推荐度
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(`推荐度: ${solution.score}/10`, solution.x + 100, 200 + animOffset);
                });

                // 场景说明
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('选择最适合生产环境的方案', canvas.width / 2, 50);

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            }

            animate();

            // 添加点击事件
            canvas.onclick = (e) => {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                if (y > 100 && y < 220) {
                    if (x > 550 && x < 750) { // Service层方案
                        gameScore += 10;
                        document.getElementById('gameScore').textContent = gameScore;
                        alert('正确！Service层实现是最推荐的方案！');
                    } else {
                        alert('再想想，哪个方案最适合生产环境？');
                    }
                }
            };
        }

        // 代理方案动画
        function startProxyAnimation() {
            const canvas = document.getElementById('proxyCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                ctx.fillStyle = '#f0f8ff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 应用程序
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 150, 100, 80);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('应用程序', 100, 195);

                // MySQL-Proxy
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(300, 150, 120, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('MySQL-Proxy', 360, 195);

                // 主库
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(550, 100, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('主库', 600, 135);

                // 从库
                ctx.fillStyle = '#fa709a';
                ctx.fillRect(550, 200, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('从库', 600, 235);

                // 动画数据流
                const flowOffset = (frame * 3) % 100;

                // 到代理的流
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(150 + flowOffset, 190);
                ctx.lineTo(300, 190);
                ctx.stroke();

                // 代理到数据库的流
                ctx.strokeStyle = '#4facfe';
                ctx.beginPath();
                ctx.moveTo(420, 170);
                ctx.lineTo(550, 130 + Math.sin(frame * 0.1) * 10);
                ctx.stroke();

                ctx.strokeStyle = '#fa709a';
                ctx.beginPath();
                ctx.moveTo(420, 210);
                ctx.lineTo(550, 230 + Math.sin(frame * 0.1 + Math.PI) * 10);
                ctx.stroke();

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // ORM层动画
        function startOrmAnimation() {
            const canvas = document.getElementById('ormCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                ctx.fillStyle = '#f0fff0';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 应用层
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 50, 150, 60);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('应用层', 125, 85);

                // DAO层 + AOP
                ctx.fillStyle = '#4ecdc4';
                ctx.fillRect(50, 150, 150, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('DAO层 + AOP', 125, 185);
                ctx.fillText('SQL拦截器', 125, 205);

                // 数据源路由
                ctx.fillStyle = '#ffa726';
                ctx.fillRect(300, 150, 150, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('数据源路由', 375, 185);
                ctx.fillText('AbstractRouting', 375, 205);

                // 主从库
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(550, 100, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('主库', 600, 135);

                ctx.fillStyle = '#fa709a';
                ctx.fillRect(550, 200, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('从库', 600, 235);

                // SQL分析动画
                const sqlTypes = ['SELECT', 'INSERT', 'UPDATE', 'DELETE'];
                const currentSql = sqlTypes[Math.floor(frame / 50) % sqlTypes.length];

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText(`当前SQL: ${currentSql}`, 375, 120);

                // 路由决策
                const isWrite = currentSql !== 'SELECT';
                ctx.strokeStyle = isWrite ? '#4facfe' : '#fa709a';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(450, 190);
                ctx.lineTo(550, isWrite ? 130 : 230);
                ctx.stroke();

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // Service层动画
        function startServiceAnimation() {
            const canvas = document.getElementById('serviceCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                ctx.fillStyle = '#fff8f0';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Controller层
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 50, 150, 50);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Controller层', 125, 80);

                // Service层 + AOP
                ctx.fillStyle = '#45b7d1';
                ctx.fillRect(50, 130, 150, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('Service层', 125, 160);
                ctx.fillText('@ReadOnly', 125, 180);
                ctx.fillText('@Transactional', 125, 200);

                // 事务管理器
                ctx.fillStyle = '#26a69a';
                ctx.fillRect(300, 130, 150, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('事务管理器', 375, 160);
                ctx.fillText('DataSource', 375, 180);
                ctx.fillText('Transaction', 375, 200);

                // 主从库
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(550, 100, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('主库', 600, 135);

                ctx.fillStyle = '#fa709a';
                ctx.fillRect(550, 200, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('从库', 600, 235);

                // 注解检测动画
                const annotations = ['@ReadOnly', '@Transactional'];
                const currentAnnotation = annotations[Math.floor(frame / 60) % annotations.length];

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText(`检测到: ${currentAnnotation}`, 375, 120);

                // 事务边界
                if (currentAnnotation === '@Transactional') {
                    ctx.strokeStyle = '#ff9800';
                    ctx.lineWidth = 3;
                    ctx.setLineDash([5, 5]);
                    ctx.strokeRect(45, 125, 610, 110);
                    ctx.setLineDash([]);

                    ctx.fillStyle = '#ff9800';
                    ctx.font = '12px Arial';
                    ctx.fillText('事务边界', 350, 250);
                }

                // 数据源选择
                const isReadOnly = currentAnnotation === '@ReadOnly';
                ctx.strokeStyle = isReadOnly ? '#fa709a' : '#4facfe';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(450, 180);
                ctx.lineTo(550, isReadOnly ? 230 : 130);
                ctx.stroke();

                // AOP拦截提示
                ctx.fillStyle = '#e91e63';
                ctx.font = '10px Arial';
                ctx.fillText('AOP拦截', 250, 170);

                frame++;
                if (frame < 240) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 知识测试功能
        function startKnowledgeTest() {
            currentQuestion = 0;
            testScore = 0;
            document.getElementById('testScore').textContent = testScore;
            showQuestion();
        }

        function showQuestion() {
            if (currentQuestion >= questions.length) {
                document.getElementById('questionArea').innerHTML =
                    `<h3>测试完成！</h3><p>您的得分：${testScore}/${questions.length}</p>
                     <p>${testScore >= 4 ? '优秀！您已经掌握了读写分离的核心概念！' : '继续学习，您会更好地理解读写分离！'}</p>`;
                return;
            }

            const question = questions[currentQuestion];
            let html = `<h3>问题 ${currentQuestion + 1}:</h3>`;
            html += `<p>${question.question}</p>`;

            question.options.forEach((option, index) => {
                html += `<button class="interactive-btn" onclick="selectAnswer(${index})" style="display:block; margin:10px auto;">${String.fromCharCode(65 + index)}. ${option}</button>`;
            });

            document.getElementById('questionArea').innerHTML = html;
        }

        function selectAnswer(selectedIndex) {
            const question = questions[currentQuestion];
            if (selectedIndex === question.correct) {
                testScore++;
                document.getElementById('testScore').textContent = testScore;
                alert('正确！');
            } else {
                alert(`错误！正确答案是：${String.fromCharCode(65 + question.correct)}. ${question.options[question.correct]}`);
            }

            currentQuestion++;
            setTimeout(showQuestion, 1000);
        }

        function resetTest() {
            currentQuestion = 0;
            testScore = 0;
            document.getElementById('testScore').textContent = testScore;
            document.getElementById('questionArea').innerHTML = '<p>点击开始测试按钮开始知识测试！</p>';
        }

        // 学习进度更新
        let completedSections = 0;
        const totalSections = 6;

        function updateProgress() {
            completedSections = Math.min(completedSections + 1, totalSections);
            const percentage = Math.round((completedSections / totalSections) * 100);

            document.getElementById('progressFill').style.width = percentage + '%';
            document.getElementById('progressText').textContent = percentage + '%';

            if (percentage === 100) {
                alert('🎉 恭喜！您已经完成了MySQL读写分离的全部学习内容！');
            }
        }

        // 提示轮换
        const tips = [
            '点击画布可以重新播放动画！',
            '读写分离可以显著提升数据库性能',
            '从库不能执行写操作，否则会中断同步',
            'Service层方案是生产环境的最佳选择',
            'AOP在类内部this调用时会失效',
            '完成知识测试来检验学习效果！'
        ];

        let currentTipIndex = 0;

        function rotateTips() {
            currentTipIndex = (currentTipIndex + 1) % tips.length;
            document.getElementById('currentTip').textContent = tips[currentTipIndex];
        }

        // 每5秒轮换一次提示
        setInterval(rotateTips, 5000);

        // 页面加载时启动初始动画
        window.addEventListener('load', () => {
            setTimeout(startConceptAnimation, 500);
        });

        // 添加交互性
        document.getElementById('conceptCanvas').addEventListener('click', startConceptAnimation);
        document.getElementById('replicationCanvas').addEventListener('click', startReplicationAnimation);
        document.getElementById('solutionsCanvas').addEventListener('click', startSolutionGame);
        document.getElementById('proxyCanvas').addEventListener('click', startProxyAnimation);
        document.getElementById('ormCanvas').addEventListener('click', startOrmAnimation);
        document.getElementById('serviceCanvas').addEventListener('click', startServiceAnimation);
    </script>
</body>
</html>
