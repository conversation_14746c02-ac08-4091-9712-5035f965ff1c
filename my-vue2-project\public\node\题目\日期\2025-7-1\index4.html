<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奈奎斯特采样定理 - 交互式学习</title>
    <style>
        /* 页面整体样式 */
        body {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Microsoft YaHei', sans-serif;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        /* 主容器 */
        .container {
            width: 100%;
            max-width: 800px;
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        h1, h2 {
            color: #1a73e8;
            text-align: center;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 0;
        }

        /* 题目区域 */
        #question-container p {
            font-size: 1.1em;
            font-weight: bold;
        }

        .options div {
            margin: 10px 0;
        }

        .options label {
            margin-left: 8px;
            cursor: pointer;
        }

        #check-answer-btn {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s;
            margin-top: 10px;
        }

        #check-answer-btn:hover {
            background-color: #155ab6;
        }

        #answer-result {
            margin-top: 15px;
            font-weight: bold;
            padding: 10px;
            border-radius: 5px;
        }

        .correct {
            color: #2e7d32;
            background-color: #e8f5e9;
        }

        .incorrect {
            color: #c62828;
            background-color: #ffcdd2;
        }

        /* 交互动画区域 */
        #interactive-demo {
            margin-top: 20px;
        }

        canvas {
            width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        
        .controls {
            margin-top: 15px;
        }

        .slider-container {
            margin-bottom: 15px;
        }

        .slider-container label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .slider-container input[type="range"] {
            width: 100%;
            cursor: pointer;
        }

        #status {
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
            padding: 12px;
            border-radius: 8px;
            margin-top: 15px;
            transition: all 0.3s ease;
        }
        
        /* 知识讲解 */
        #explanation {
            background-color: #e3f2fd;
            border-left: 5px solid #1a73e8;
            padding: 15px 20px;
        }

    </style>
</head>
<body>

    <div class="container">
        <h1>奈奎斯特采样定理交互式演示</h1>
    </div>

    <div class="container" id="question-container">
        <h2>随堂小测</h2>
        <p>题目20：处理一个连续时间信号，对其进行采样的频率为3KHz，要不失真的恢复该连续信号，则该连续信号的最高频率可能为（ ）。</p>
        <div class="options">
            <div><input type="radio" id="optionA" name="quiz" value="A"><label for="optionA">A) 6KHz</label></div>
            <div><input type="radio" id="optionB" name="quiz" value="B"><label for="optionB">B) 1.5KHz</label></div>
            <div><input type="radio" id="optionC" name="quiz" value="C"><label for="optionC">C) 3KHz</label></div>
            <div><input type="radio" id="optionD" name="quiz" value="D"><label for="optionD">D) 1KHz</label></div>
        </div>
        <button id="check-answer-btn">检查答案</button>
        <div id="answer-result"></div>
    </div>

    <div class="container" id="interactive-demo">
        <h2>动手玩一下</h2>
        <canvas id="samplingCanvas" width="800" height="400"></canvas>
        <div class="controls">
            <div class="slider-container">
                <label for="signal-freq-slider">原始信号频率 (f_max): <span id="signal-freq-value">1.5</span> KHz</label>
                <input type="range" id="signal-freq-slider" min="0.5" max="3.0" value="1.5" step="0.1">
            </div>
            <div class="slider-container">
                <label for="sampling-freq-slider">采样频率 (fs): <span id="sampling-freq-value">3.0</span> KHz</label>
                <input type="range" id="sampling-freq-slider" min="0.5" max="8.0" value="3.0" step="0.1">
            </div>
        </div>
        <div id="status"></div>
    </div>

    <div class="container" id="explanation">
        <h2>知识点解析</h2>
        <p><strong>奈奎斯特采样定理：</strong> 这是数字信号处理中的一个核心法则。简单来说就是：</p>
        <p>为了<strong>不失真</strong>地从采样点恢复出原始的连续信号，<strong>采样频率</strong> (<code>fs</code>) 必须<strong>大于或等于</strong>原始信号最高频率 (<code>f_max</code>) 的<strong>两倍</strong>。</p>
        <p>数学公式表达为：<code style="background: #dcdcdc; padding: 2px 5px; border-radius:3px;">fs ≥ 2 * f_max</code></p>
        <ul>
            <li>在上面的动画里，蓝色实线是"原始信号"。</li>
            <li>红色圆点代表我们进行的"采样"。</li>
            <li>绿色虚线是根据采样点"恢复"出来的信号。</li>
        </ul>
        <p>当你拖动滑块，使得采样频率不满足该条件时（即 <code>fs < 2 * f_max</code>），你会看到恢复的绿色信号和原始的蓝色信号不再重合，发生了"失真"。这种失真现象有一个专门的名字，叫做<strong>混叠（Aliasing）</strong>。</p>
    </div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    // 获取DOM元素
    const canvas = document.getElementById('samplingCanvas');
    const ctx = canvas.getContext('2d');
    const signalFreqSlider = document.getElementById('signal-freq-slider');
    const samplingFreqSlider = document.getElementById('sampling-freq-slider');
    const signalFreqValue = document.getElementById('signal-freq-value');
    const samplingFreqValue = document.getElementById('sampling-freq-value');
    const statusDiv = document.getElementById('status');
    const checkAnswerBtn = document.getElementById('check-answer-btn');
    const answerResultDiv = document.getElementById('answer-result');

    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;
    const amplitude = 150; // 振幅
    const centerY = canvasHeight / 2;

    // 绘制正弦波的函数
    function drawWave(freq, color, isDashed = false) {
        ctx.beginPath();
        ctx.lineWidth = 3;
        ctx.strokeStyle = color;
        
        if (isDashed) {
            ctx.setLineDash([10, 10]);
        } else {
            ctx.setLineDash([]);
        }

        for (let x = 0; x < canvasWidth; x++) {
            // 将画布的宽度映射到 2*PI 的周期，freq决定了波的密度
            const angle = (x / canvasWidth) * 2 * Math.PI * freq;
            const y = centerY - amplitude * Math.sin(angle);
            if (x === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.stroke();
        ctx.setLineDash([]); // 重置虚线设置
    }

    // 绘制采样点的函数
    function drawSamplePoints(signalFreq, samplingFreq) {
        const numSamples = Math.floor(canvasWidth * (samplingFreq / (2 * Math.PI * signalFreq)));
        const samplingPeriod = canvasWidth / numSamples;
        
        ctx.fillStyle = '#c62828'; // 红色

        for (let i = 0; i <= numSamples; i++) {
            const x = i * samplingPeriod;
            if (x > canvasWidth) continue;

            const angle = (x / canvasWidth) * 2 * Math.PI * signalFreq;
            const y = centerY - amplitude * Math.sin(angle);
            
            ctx.beginPath();
            ctx.arc(x, y, 6, 0, 2 * Math.PI);
            ctx.fill();
        }
    }

    // 主绘制函数
    function draw() {
        const signalFreq = parseFloat(signalFreqSlider.value);
        const samplingFreq = parseFloat(samplingFreqSlider.value);

        // 更新滑块旁边的数值显示
        signalFreqValue.textContent = signalFreq.toFixed(1);
        samplingFreqValue.textContent = samplingFreq.toFixed(1);

        // 清空画布
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 1. 绘制原始信号 (蓝色)
        drawWave(signalFreq, '#1a73e8');

        // 2. 绘制采样点 (红色)
        drawSamplePoints(signalFreq, samplingFreq);
        
        // 3. 判断奈奎斯特条件并绘制恢复信号 (绿色)
        const isNyquistMet = samplingFreq >= 2 * signalFreq;
        
        if (isNyquistMet) {
            // 条件满足，恢复信号与原始信号一致
            drawWave(signalFreq, '#2e7d32', true);
            statusDiv.textContent = '✅ 采样满足要求 (fs ≥ 2 * f_max)，信号可以完美恢复！';
            statusDiv.style.color = '#2e7d32';
            statusDiv.style.backgroundColor = '#e8f5e9';
        } else {
            // 条件不满足，发生混叠，计算并绘制混叠后的信号
            // 混叠频率 f_alias = |f_signal - fs|
            const aliasFreq = Math.abs(signalFreq - samplingFreq);
            drawWave(aliasFreq, '#e57373', true);
            statusDiv.textContent = '❌ 采样频率不足 (fs < 2 * f_max)，发生混叠失真！';
            statusDiv.style.color = '#c62828';
            statusDiv.style.backgroundColor = '#ffcdd2';
        }
    }
    
    // 给滑块添加事件监听
    signalFreqSlider.addEventListener('input', draw);
    samplingFreqSlider.addEventListener('input', draw);

    // 检查答案按钮的逻辑
    checkAnswerBtn.addEventListener('click', () => {
        const selectedOption = document.querySelector('input[name="quiz"]:checked');
        answerResultDiv.style.display = 'block'; // 显示结果区域

        if (!selectedOption) {
            answerResultDiv.textContent = '🤔 请先选择一个答案！';
            answerResultDiv.className = 'incorrect';
            return;
        }

        if (selectedOption.value === 'B') {
            answerResultDiv.textContent = '🎉 回答正确！根据定理 fs (3KHz) ≥ 2 * f_max，所以 f_max 最高只能是 1.5KHz。';
            answerResultDiv.className = 'correct';
        } else {
            answerResultDiv.textContent = '🙁 回答错误，再想想？提示：采样频率需要大于等于信号最高频率的两倍。';
            answerResultDiv.className = 'incorrect';
        }
    });

    // 初始绘制
    draw();
});
</script>

</body>
</html>
