<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Compress</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
        :root {
            --primary-color: #e65100; /* 橙色，代表压力与能量 */
            --secondary-color: #d84315;
            --accent-color: #03a9f4; /* 蓝色，点缀 */
            --light-bg: #fff3e0;
            --panel-bg: #ffffff;
            --text-color: #bf360c;
        }
        body { font-family: 'Roboto', 'Noto Sans SC', sans-serif; background-color: #ffe0b2; color: var(--text-color); display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; overflow: hidden; }
        .container { display: flex; flex-direction: row; width: 95%; max-width: 1400px; height: 90vh; max-height: 800px; background-color: var(--panel-bg); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .word-panel { flex: 1; padding: 40px; display: flex; flex-direction: column; justify-content: center; background-color: var(--light-bg); overflow-y: auto; }
        .word-panel h1 { font-size: 3.5em; color: var(--primary-color); }
        .word-panel .pronunciation { font-size: 1.5em; color: var(--secondary-color); margin-bottom: 20px; }
        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--secondary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: #d84315; }
        .breakdown-section { margin-top: 25px; padding: 20px; background-color: #ffffff; border-radius: 10px; }
        .morpheme-btn { margin: 5px; padding: 8px 15px; border: 2px solid var(--primary-color); border-radius: 20px; background-color: transparent; color: var(--primary-color); font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; }
        .morpheme-btn:hover, .morpheme-btn.active { background-color: var(--primary-color); color: white; transform: translateY(-2px); }
        .animation-panel { flex: 2; padding: 20px; display: flex; flex-direction: column; justify-content: center; align-items: center; position: relative; background: #455a64; }
        .activity-title { font-size: 1.8em; color: var(--light-bg); margin-bottom: 15px; text-align: center; }
        .activity-wrapper { display: none; width: 100%; height: calc(100% - 100px); flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        .game-container { width: 100%; height: 100%; position: relative; display: flex; align-items: center; justify-content: center; border-radius: 15px; background: #37474f; border: 1px solid #263238; overflow: hidden; }
        .control-button { margin-top: 20px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; }
        
        /* Piston Game */
        #piston { position: absolute; top: 0; left: 0; width: 100%; height: 50px; background: linear-gradient(to bottom, #78909c, #546e7a); border-bottom: 5px solid #37474f; }
        .particle { position: absolute; width: 10px; height: 10px; background: var(--accent-color); border-radius: 50%; }

        /* File Compression Game */
        #file-original, #file-compressed { position: absolute; display: grid; border: 2px solid white; background: #546e7a; padding: 5px; transition: all 1s ease-in-out; }
        #file-original { left: 5%; top: 50%; transform: translateY(-50%); grid-template-columns: repeat(8, 1fr); gap: 3px; }
        #file-compressed { right: 5%; top: 50%; transform: translateY(-50%) scale(0); grid-template-columns: repeat(4, 1fr); gap: 2px; }
        .pixel { width: 15px; height: 15px; background-color: #03a9f4; }
        #compressor { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80px; height: 80px; opacity: 0; }
        #compressor-icon { font-size: 80px; color: var(--primary-color); animation: spin 2s linear infinite paused; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
        .file-size-label { position: absolute; bottom: -40px; left: 50%; transform: translateX(-50%); color: white; font-size: 1.2em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>compress</h1>
            <p class="pronunciation">[kəmˈpres]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 压缩；n. 压布，绷带</p>
                <p><strong>词源:</strong> com-(共同) + press(挤压) → 挤压到一起</p>
                <p><strong>含义：</strong><br>1. (v.) 施加压力使其变小、变紧。<br>2. (v.) 精简（信息）；压缩（文件）。</p>
                <div class="example">
                    <p><strong>例句1:</strong> The machine compresses the cotton into bales.</p>
                    <p><strong>翻译1:</strong> 这台机器将棉花压缩成包。</p>
                    <p><strong>例句2:</strong> This software can compress large image files.</p>
                    <p><strong>翻译2:</strong> 这个软件可以压缩大的图像文件。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>单词动画</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="piston-game">动画: 活塞压缩</button>
                    <button class="morpheme-btn" data-activity="file-game">互动: 文件压缩</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p style="color:white;">点击左侧按钮，感受"压缩"的威力。</p></div>
            
            <div id="piston-game" class="activity-wrapper">
                <div id="piston-container" class="game-container">
                    <div id="piston"></div>
                </div>
                <button class="control-button" id="compress-btn">压缩</button>
            </div>
            
            <div id="file-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="file-original"><div class="file-size-label">Size: 64kb</div></div>
                    <div id="compressor"><div id="compressor-icon">⚙️</div></div>
                    <div id="file-compressed"><div class="file-size-label">Size: 16kb</div></div>
                </div>
                <button class="control-button" id="compress-file-btn">压缩文件</button>
            </div>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');
        let currentCleanup = null;

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                if (currentCleanup) currentCleanup();
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                const targetId = btn.dataset.activity;
                document.getElementById(targetId)?.classList.add('active');
                
                if (targetId === 'piston-game') currentCleanup = setupPistonGame();
                else if (targetId === 'file-game') currentCleanup = setupFileGame();
                else currentCleanup = null;
            });
        });

        function setupPistonGame() {
            const container = document.getElementById('piston-container');
            const piston = document.getElementById('piston');
            const btn = document.getElementById('compress-btn');
        let particles = [];
            let isCompressed = false;
            let tl;

            function init() {
                container.querySelectorAll('.particle').forEach(p => p.remove());
                particles = [];
                for (let i = 0; i < 50; i++) {
                    const p = document.createElement('div');
                    p.className = 'particle';
                    container.appendChild(p);
                    gsap.set(p, {
                        x: Math.random() * (container.clientWidth - 10),
                        y: Math.random() * (container.clientHeight - 60) + 50,
                    });
                    particles.push(p);
                }
            }

            function animate(isCompressing) {
                if (tl) tl.kill();
                tl = gsap.timeline({ defaults: { duration: 2, ease: 'power1.inOut' } });
                if (isCompressing) {
                    tl.to(piston, { y: container.clientHeight - 100 });
                    particles.forEach(p => {
                        tl.to(p, { 
                            y: () => container.clientHeight - 10 - Math.random() * 90,
                            x: () => Math.random() * (container.clientWidth - 10),
                            duration: () => 1.5 + Math.random() * 0.5
                        }, "<");
                    });
                } else {
                    tl.to(piston, { y: 0 });
                particles.forEach(p => {
                        tl.to(p, { 
                            y: () => Math.random() * (container.clientHeight - 60) + 50,
                            duration: () => 1.5 + Math.random() * 0.5
                        }, "<");
                    });
                }
            }

            btn.onclick = () => {
                isCompressed = !isCompressed;
                animate(isCompressed);
                btn.textContent = isCompressed ? '解压' : '压缩';
            };
            
            init();
            return () => { if(tl) tl.kill(); };
        }

        function setupFileGame() {
            const btn = document.getElementById('compress-file-btn');
            const fileOriginal = document.getElementById('file-original');
            const fileCompressed = document.getElementById('file-compressed');
            const compressorIcon = document.getElementById('compressor-icon');
            const compressor = document.getElementById('compressor');
            let isCompressed = false;
            let tl;
            
            function init() {
                fileOriginal.innerHTML = '<div class="file-size-label">Size: 64kb</div>';
                fileCompressed.innerHTML = '<div class="file-size-label">Size: 16kb</div>';
                for(let i=0; i<64; i++) fileOriginal.appendChild(document.createElement('div')).className = 'pixel';
                for(let i=0; i<16; i++) fileCompressed.appendChild(document.createElement('div')).className = 'pixel';
            }

            function animate(compress) {
                if(tl) tl.kill();
                tl = gsap.timeline({ 
                    defaults: { duration: 1, ease: 'power2.inOut' },
                    onComplete: () => btn.disabled = false 
                });
                btn.disabled = true;

                if (compress) {
                    tl.to(fileOriginal, { left: '50%', xPercent: -50, scale: 0, opacity: 0 })
                      .to(compressor, { opacity: 1 }, "-=0.8")
                      .set(compressorIcon, { animationPlayState: 'running' })
                      .to(compressor, { opacity: 0, delay: 1 })
                      .set(compressorIcon, { animationPlayState: 'paused' })
                      .to(fileCompressed, { right: '50%', xPercent: 50, scale: 1, opacity: 1 }, "-=0.5");
                } else {
                     tl.to(fileCompressed, { right: '5%', xPercent: 0, scale: 0, opacity: 0 })
                       .to(compressor, { opacity: 1 }, "-=0.8")
                       .set(compressorIcon, { animationPlayState: 'running' })
                       .to(compressor, { opacity: 0, delay: 1 })
                       .set(compressorIcon, { animationPlayState: 'paused' })
                       .to(fileOriginal, { left: '5%', xPercent: 0, scale: 1, opacity: 1 }, "-=0.5");
                }
            }
            
            btn.onclick = () => {
                isCompressed = !isCompressed;
                animate(isCompressed);
                btn.textContent = isCompressed ? '解压文件' : '压缩文件';
            };

            init();
            return () => { if(tl) tl.kill(); };
        }
    });
    </script>
</body>
</html> 