<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>笔记系统 - 外观模式</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            text-align: center;
            margin-bottom: 30px;
        }
        h1 {
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .note-system {
            display: flex;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .sidebar {
            width: 30%;
            background-color: #f0f0f0;
            padding: 20px;
        }
        .note-list {
            list-style: none;
        }
        .note-item {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .note-item:hover {
            background-color: #e0e0e0;
        }
        .note-item.active {
            background-color: #3498db;
            color: white;
        }
        .content-area {
            flex: 1;
            padding: 20px;
        }
        .toolbar {
            display: flex;
            margin-bottom: 15px;
            gap: 10px;
        }
        button {
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .note-content {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            min-height: 300px;
            background-color: #fff;
        }
        .status-bar {
            margin-top: 20px;
            font-size: 14px;
            color: #777;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>笔记系统 - 外观模式实现</h1>
            <p>通过外观模式简化笔记系统的操作流程</p>
        </header>

        <div id="login-panel">
            <button id="login-btn">登录笔记系统</button>
        </div>

        <div id="note-system" class="note-system hidden">
            <div class="sidebar">
                <h3>笔记列表</h3>
                <ul class="note-list" id="note-list">
                    <!-- 笔记列表将通过JavaScript动态生成 -->
                </ul>
            </div>
            <div class="content-area">
                <div class="toolbar">
                    <button id="new-note-btn">新建笔记</button>
                    <button id="save-note-btn">保存笔记</button>
                    <button id="delete-note-btn">删除笔记</button>
                </div>
                <div class="note-content" id="note-content" contenteditable="true">
                    <!-- 笔记内容将在这里显示和编辑 -->
                </div>
                <div class="status-bar" id="status-bar">
                    就绪
                </div>
            </div>
        </div>
    </div>

    <script>
        // 子系统1：认证系统
        class AuthenticationSystem {
            login() {
                console.log('认证系统：用户登录');
                return true;
            }

            logout() {
                console.log('认证系统：用户登出');
                return true;
            }

            checkPermission(action) {
                console.log(`认证系统：检查权限 - ${action}`);
                return true;
            }
        }

        // 子系统2：笔记存储系统
        class NoteStorage {
            constructor() {
                // 模拟数据库存储
                this.notes = [
                    { id: 1, title: '外观模式介绍', content: '外观模式(Facade Pattern)是一种结构型设计模式，它提供了一个统一的接口，用来访问子系统中的一组接口。外观模式定义了一个高层接口，让子系统更容易使用。' },
                    { id: 2, title: '外观模式的优点', content: '1. 简化接口\n2. 减少客户端与子系统的耦合\n3. 提供统一的接口访问子系统\n4. 隐藏子系统的复杂性' },
                    { id: 3, title: '外观模式的应用场景', content: '当需要为复杂子系统提供一个简单接口时\n当客户端与抽象类的实现部分之间存在很大的依赖性时\n当需要构建一个层次结构的子系统时' }
                ];
            }

            getNotes() {
                console.log('存储系统：获取所有笔记');
                return this.notes;
            }

            getNote(id) {
                console.log(`存储系统：获取笔记 ID=${id}`);
                return this.notes.find(note => note.id === id);
            }

            saveNote(note) {
                console.log(`存储系统：保存笔记 ID=${note.id}`);
                const index = this.notes.findIndex(n => n.id === note.id);
                if (index !== -1) {
                    this.notes[index] = note;
                } else {
                    note.id = this.notes.length > 0 ? Math.max(...this.notes.map(n => n.id)) + 1 : 1;
                    this.notes.push(note);
                }
                return note;
            }

            deleteNote(id) {
                console.log(`存储系统：删除笔记 ID=${id}`);
                const index = this.notes.findIndex(note => note.id === id);
                if (index !== -1) {
                    this.notes.splice(index, 1);
                    return true;
                }
                return false;
            }
        }

        // 子系统3：UI管理系统
        class UIManager {
            displayNoteList(notes) {
                console.log('UI管理器：显示笔记列表');
                const noteList = document.getElementById('note-list');
                noteList.innerHTML = '';
                
                notes.forEach(note => {
                    const li = document.createElement('li');
                    li.className = 'note-item';
                    li.dataset.id = note.id;
                    li.textContent = note.title;
                    noteList.appendChild(li);
                });
            }

            displayNote(note) {
                console.log(`UI管理器：显示笔记内容 ID=${note.id}`);
                const noteContent = document.getElementById('note-content');
                noteContent.innerHTML = note.content;
                
                // 标记当前选中的笔记
                document.querySelectorAll('.note-item').forEach(item => {
                    if (parseInt(item.dataset.id) === note.id) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });
            }

            showNoteSystem() {
                document.getElementById('login-panel').classList.add('hidden');
                document.getElementById('note-system').classList.remove('hidden');
            }

            updateStatus(message) {
                document.getElementById('status-bar').textContent = message;
            }
        }

        // 外观类：笔记系统外观
        class NoteSystemFacade {
            constructor() {
                this.authSystem = new AuthenticationSystem();
                this.storageSystem = new NoteStorage();
                this.uiManager = new UIManager();
                this.currentNote = null;
            }

            // 初始化系统
            init() {
                document.getElementById('login-btn').addEventListener('click', () => this.openNoteSystem());
                
                document.getElementById('note-list').addEventListener('click', (e) => {
                    if (e.target.classList.contains('note-item')) {
                        const noteId = parseInt(e.target.dataset.id);
                        this.selectNote(noteId);
                    }
                });

                document.getElementById('new-note-btn').addEventListener('click', () => this.createNewNote());
                document.getElementById('save-note-btn').addEventListener('click', () => this.saveCurrentNote());
                document.getElementById('delete-note-btn').addEventListener('click', () => this.deleteCurrentNote());
            }

            // 统一接口：打开笔记系统
            openNoteSystem() {
                if (this.authSystem.login()) {
                    this.uiManager.showNoteSystem();
                    const notes = this.storageSystem.getNotes();
                    this.uiManager.displayNoteList(notes);
                    this.uiManager.updateStatus('笔记系统已启动');
                    
                    // 默认选中第一个笔记
                    if (notes.length > 0) {
                        this.selectNote(notes[0].id);
                    }
                }
            }

            // 统一接口：选择笔记
            selectNote(noteId) {
                if (this.authSystem.checkPermission('read')) {
                    const note = this.storageSystem.getNote(noteId);
                    if (note) {
                        this.currentNote = note;
                        this.uiManager.displayNote(note);
                        this.uiManager.updateStatus(`已加载笔记：${note.title}`);
                    }
                }
            }

            // 统一接口：创建新笔记
            createNewNote() {
                if (this.authSystem.checkPermission('write')) {
                    const newNote = {
                        id: null,
                        title: '新笔记',
                        content: '请在此处输入笔记内容...'
                    };
                    const savedNote = this.storageSystem.saveNote(newNote);
                    this.currentNote = savedNote;
                    
                    // 更新UI
                    const notes = this.storageSystem.getNotes();
                    this.uiManager.displayNoteList(notes);
                    this.uiManager.displayNote(savedNote);
                    this.uiManager.updateStatus('新建笔记成功');
                }
            }

            // 统一接口：保存当前笔记
            saveCurrentNote() {
                if (!this.currentNote) {
                    this.uiManager.updateStatus('无选中笔记，无法保存');
                    return;
                }

                if (this.authSystem.checkPermission('write')) {
                    const content = document.getElementById('note-content').innerHTML;
                    this.currentNote.content = content;
                    this.storageSystem.saveNote(this.currentNote);
                    this.uiManager.updateStatus(`笔记"${this.currentNote.title}"已保存`);
                }
            }

            // 统一接口：删除当前笔记
            deleteCurrentNote() {
                if (!this.currentNote) {
                    this.uiManager.updateStatus('无选中笔记，无法删除');
                    return;
                }

                if (this.authSystem.checkPermission('delete')) {
                    const noteId = this.currentNote.id;
                    const noteTitle = this.currentNote.title;
                    
                    if (this.storageSystem.deleteNote(noteId)) {
                        this.currentNote = null;
                        const notes = this.storageSystem.getNotes();
                        this.uiManager.displayNoteList(notes);
                        document.getElementById('note-content').innerHTML = '';
                        this.uiManager.updateStatus(`笔记"${noteTitle}"已删除`);
                        
                        // 如果还有其他笔记，选择第一个
                        if (notes.length > 0) {
                            this.selectNote(notes[0].id);
                        }
                    }
                }
            }
        }

        // 初始化外观类
        document.addEventListener('DOMContentLoaded', () => {
            const noteSystem = new NoteSystemFacade();
            noteSystem.init();
        });
    </script>
</body>
</html> 