<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微内核操作系统 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 60px;
        }

        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            animation: fadeInUp 1s ease-out;
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #4a5568;
        }

        .canvas-container {
            grid-column: 1 / -1;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1.2s ease-out;
        }

        #osCanvas {
            width: 100%;
            height: 500px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn:active {
            transform: translateY(0);
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1.4s ease-out;
        }

        .question {
            font-size: 1.2rem;
            margin-bottom: 20px;
            line-height: 1.6;
            color: #2d3748;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }

        .option {
            padding: 15px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .option:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .option.correct {
            border-color: #48bb78;
            background: rgba(72, 187, 120, 0.1);
        }

        .option.wrong {
            border-color: #f56565;
            background: rgba(245, 101, 101, 0.1);
        }

        .explanation {
            background: rgba(72, 187, 120, 0.1);
            border-left: 4px solid #48bb78;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: none;
            animation: slideInLeft 0.5s ease-out;
        }

        .score {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 600;
            color: #667eea;
            margin-top: 20px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">微内核操作系统</h1>
            <p class="subtitle">交互式学习 - 理解操作系统架构的演进</p>
        </div>

        <div class="content-grid">
            <div class="card">
                <h2 class="card-title">🎯 学习目标</h2>
                <ul style="line-height: 2; color: #4a5568;">
                    <li>理解微内核与宏内核的区别</li>
                    <li>掌握用户态和核心态的概念</li>
                    <li>了解微内核的优势和特点</li>
                    <li>学会分析操作系统架构</li>
                </ul>
            </div>

            <div class="card">
                <h2 class="card-title">🔑 核心概念</h2>
                <div style="line-height: 2; color: #4a5568;">
                    <p><strong>微内核：</strong>只包含最基本功能的内核</p>
                    <p><strong>用户态：</strong>应用程序运行的模式</p>
                    <p><strong>核心态：</strong>内核运行的特权模式</p>
                    <p><strong>服务器：</strong>提供系统服务的进程</p>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <h2 class="card-title">🎮 交互式操作系统架构演示</h2>
            <canvas id="osCanvas"></canvas>
            <div class="controls">
                <button class="btn" onclick="showTraditionalOS()">传统操作系统</button>
                <button class="btn" onclick="showMicrokernel()">微内核系统</button>
                <button class="btn" onclick="animateComparison()">对比演示</button>
                <button class="btn" onclick="startInteractiveDemo()">交互体验</button>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="card-title">📝 知识检测</h2>
            <div class="question">
                微内核的操作系统（OS）结构中，图中①和②分别工作在（ ）方式下，与传统的OS结构模式相比，采用微内核的OS结构模式的优点是提高了系统的灵活性、可扩充性，（ ）。
            </div>
            <div class="options">
                <div class="option" onclick="selectOption(this, false)">
                    A. 并增强了可靠性，可运行于分布式系统中
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    B. 并增强了可靠性，但不适用于分布式系统
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    C. 但降低了可靠性，可运行于分布式系统中
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    D. 但降低了可靠性，不适用于分布式系统
                </div>
            </div>
            <div class="explanation" id="explanation">
                <h3>💡 详细解析</h3>
                <p><strong>正确答案：A</strong></p>
                <p>微内核操作系统的特点：</p>
                <ul style="margin-top: 10px; line-height: 1.8;">
                    <li><strong>①工作在用户态：</strong>大部分系统服务运行在用户态，提高了系统安全性</li>
                    <li><strong>②工作在核心态：</strong>只有最基本的内核功能运行在核心态</li>
                    <li><strong>增强可靠性：</strong>服务隔离，一个服务崩溃不会影响整个系统</li>
                    <li><strong>适用分布式：</strong>客户/服务器模式天然适合分布式环境</li>
                </ul>
            </div>
            <div class="score" id="score"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('osCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        let animationId;
        let currentDemo = 'traditional';
        let interactiveMode = false;
        
        // 颜色主题
        const colors = {
            kernel: '#667eea',
            userSpace: '#48bb78',
            hardware: '#4a5568',
            service: '#ed8936',
            communication: '#38b2ac',
            background: '#f7fafc'
        };
        
        // 绘制传统操作系统
        function drawTraditionalOS() {
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 背景
            ctx.fillStyle = colors.background;
            ctx.fillRect(0, 0, width, height);

            // 硬件层
            ctx.fillStyle = colors.hardware;
            ctx.fillRect(50, height - 80, width - 100, 60);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('硬件层 (Hardware)', width / 2, height - 45);

            // 大内核
            ctx.fillStyle = colors.kernel;
            ctx.fillRect(50, height - 280, width - 100, 180);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('宏内核 (Monolithic Kernel)', width / 2, height - 190);
            ctx.font = '14px Arial';
            ctx.fillText('文件系统 + 网络 + 设备驱动 + 内存管理', width / 2, height - 160);
            ctx.fillText('进程管理 + 系统调用 + 所有服务', width / 2, height - 140);

            // 用户空间
            ctx.fillStyle = colors.userSpace;
            ctx.fillRect(50, 50, width - 100, height - 350);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('用户空间 (User Space)', width / 2, 80);

            // 应用程序
            const appWidth = (width - 200) / 3;
            for (let i = 0; i < 3; i++) {
                ctx.fillStyle = colors.service;
                ctx.fillRect(80 + i * (appWidth + 20), 110, appWidth, 60);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(`应用程序 ${i + 1}`, 80 + i * (appWidth + 20) + appWidth / 2, 145);
            }

            // 标注
            ctx.fillStyle = '#e53e3e';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('核心态 (Kernel Mode)', width - 150, height - 200);
            ctx.fillStyle = '#38a169';
            ctx.fillText('用户态 (User Mode)', width - 150, 100);
        }

        // 绘制微内核操作系统
        function drawMicrokernel() {
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 背景
            ctx.fillStyle = colors.background;
            ctx.fillRect(0, 0, width, height);

            // 硬件层
            ctx.fillStyle = colors.hardware;
            ctx.fillRect(50, height - 80, width - 100, 60);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('硬件层 (Hardware)', width / 2, height - 45);

            // 微内核
            ctx.fillStyle = colors.kernel;
            ctx.fillRect(50, height - 180, width - 100, 80);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('微内核 (Microkernel)', width / 2, height - 150);
            ctx.font = '14px Arial';
            ctx.fillText('进程通信 + 内存管理 + 进程调度', width / 2, height - 125);

            // 用户空间 - 系统服务
            ctx.fillStyle = colors.userSpace;
            ctx.fillRect(50, 50, width - 100, height - 250);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('用户空间 (User Space)', width / 2, 80);

            // 系统服务器
            const serviceWidth = (width - 200) / 4;
            const services = ['文件服务器', '网络服务器', '设备驱动', '窗口服务器'];
            for (let i = 0; i < 4; i++) {
                ctx.fillStyle = colors.service;
                ctx.fillRect(70 + i * (serviceWidth + 10), 110, serviceWidth, 50);
                ctx.fillStyle = 'white';
                ctx.font = '11px Arial';
                ctx.fillText(services[i], 70 + i * (serviceWidth + 10) + serviceWidth / 2, 140);
            }

            // 客户端应用
            for (let i = 0; i < 3; i++) {
                ctx.fillStyle = colors.communication;
                ctx.fillRect(80 + i * (serviceWidth + 30), 180, serviceWidth + 20, 40);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(`客户端 ${i + 1}`, 80 + i * (serviceWidth + 30) + (serviceWidth + 20) / 2, 205);
            }

            // 通信箭头
            ctx.strokeStyle = colors.communication;
            ctx.lineWidth = 2;
            for (let i = 0; i < 3; i++) {
                const x = 90 + i * (serviceWidth + 30) + serviceWidth / 2;
                ctx.beginPath();
                ctx.moveTo(x, 180);
                ctx.lineTo(x, 160);
                ctx.stroke();

                // 箭头
                ctx.beginPath();
                ctx.moveTo(x - 5, 165);
                ctx.lineTo(x, 160);
                ctx.lineTo(x + 5, 165);
                ctx.stroke();
            }

            // 标注
            ctx.fillStyle = '#e53e3e';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('核心态', width - 100, height - 140);
            ctx.fillStyle = '#38a169';
            ctx.fillText('用户态 ①②', width - 100, 130);
        }

        // 动画对比演示
        function animateComparison() {
            let frame = 0;
            const maxFrames = 120;

            function animate() {
                const progress = frame / maxFrames;
                const width = canvas.width / window.devicePixelRatio;
                const height = canvas.height / window.devicePixelRatio;

                ctx.clearRect(0, 0, width, height);

                if (progress < 0.5) {
                    // 显示传统OS
                    drawTraditionalOS();
                    ctx.fillStyle = 'rgba(231, 76, 60, 0.8)';
                    ctx.font = 'bold 24px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('传统宏内核', width / 2, 30);
                } else {
                    // 显示微内核
                    drawMicrokernel();
                    ctx.fillStyle = 'rgba(46, 204, 113, 0.8)';
                    ctx.font = 'bold 24px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('微内核架构', width / 2, 30);
                }

                frame++;
                if (frame <= maxFrames) {
                    animationId = requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 交互式演示
        function startInteractiveDemo() {
            interactiveMode = true;
            drawMicrokernel();

            // 添加点击提示
            ctx.fillStyle = 'rgba(102, 126, 234, 0.8)';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击不同区域了解详细信息！', canvas.width / window.devicePixelRatio / 2, 30);
        }

        // 显示函数
        function showTraditionalOS() {
            currentDemo = 'traditional';
            interactiveMode = false;
            if (animationId) cancelAnimationFrame(animationId);
            drawTraditionalOS();
        }

        function showMicrokernel() {
            currentDemo = 'microkernel';
            interactiveMode = false;
            if (animationId) cancelAnimationFrame(animationId);
            drawMicrokernel();
        }

        // Canvas点击事件
        canvas.addEventListener('click', function(e) {
            if (!interactiveMode) return;

            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            const width = rect.width;
            const height = rect.height;

            // 检测点击区域并显示信息
            if (y > height - 180 && y < height - 100) {
                showInfo('微内核', '只包含最基本的功能：进程通信、内存管理、进程调度。体积小，安全性高。');
            } else if (y > 110 && y < 160) {
                showInfo('系统服务器', '运行在用户态的系统服务，如文件服务器、网络服务器等。服务隔离，提高可靠性。');
            } else if (y > 180 && y < 220) {
                showInfo('客户端应用', '通过IPC与系统服务器通信的应用程序。采用客户/服务器模式。');
            } else if (y > height - 80) {
                showInfo('硬件层', '计算机的物理硬件，包括CPU、内存、存储设备等。');
            }
        });

        // 显示信息弹窗
        function showInfo(title, content) {
            const popup = document.createElement('div');
            popup.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                z-index: 1000;
                max-width: 400px;
                animation: fadeInUp 0.3s ease-out;
            `;

            popup.innerHTML = `
                <h3 style="color: #667eea; margin-bottom: 15px; font-size: 1.3rem;">${title}</h3>
                <p style="line-height: 1.6; color: #4a5568; margin-bottom: 20px;">${content}</p>
                <button onclick="this.parentElement.remove()" style="
                    background: #667eea;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-weight: 600;
                ">关闭</button>
            `;

            document.body.appendChild(popup);

            // 3秒后自动关闭
            setTimeout(() => {
                if (popup.parentElement) {
                    popup.remove();
                }
            }, 3000);
        }

        // 题目相关函数
        let selectedOption = null;
        let answered = false;

        function selectOption(element, isCorrect) {
            if (answered) return;

            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // 标记当前选择
            element.classList.add('selected');
            selectedOption = element;

            // 延迟显示答案
            setTimeout(() => {
                showAnswer();
            }, 1000);
        }

        function showAnswer() {
            answered = true;
            const options = document.querySelectorAll('.option');

            // 显示正确答案（第一个选项）
            options[0].classList.add('correct');

            // 如果选择错误，标记错误选项
            if (selectedOption && selectedOption !== options[0]) {
                selectedOption.classList.add('wrong');
            }

            // 显示解析
            document.getElementById('explanation').style.display = 'block';

            // 显示分数
            const score = selectedOption === options[0] ? 100 : 0;
            document.getElementById('score').textContent = `得分: ${score}/100`;
            document.getElementById('score').style.color = score === 100 ? '#48bb78' : '#f56565';
        }

        // 初始化
        drawTraditionalOS();

        // 添加一些动态效果
        setInterval(() => {
            if (currentDemo === 'microkernel' && !interactiveMode) {
                // 微内核模式下添加通信动画
                const width = canvas.width / window.devicePixelRatio;
                const height = canvas.height / window.devicePixelRatio;

                // 绘制闪烁的通信线
                ctx.strokeStyle = `rgba(56, 178, 172, ${0.5 + 0.5 * Math.sin(Date.now() / 500)})`;
                ctx.lineWidth = 3;

                for (let i = 0; i < 3; i++) {
                    const serviceWidth = (width - 200) / 4;
                    const x = 90 + i * (serviceWidth + 30) + serviceWidth / 2;
                    ctx.beginPath();
                    ctx.moveTo(x, 180);
                    ctx.lineTo(x, 160);
                    ctx.stroke();
                }
            }
        }, 100);
    </script>
</body>
</html>
