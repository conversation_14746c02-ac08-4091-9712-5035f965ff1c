<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进程调度与死锁预防 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
            backdrop-filter: blur(10px);
        }

        .section-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 30px;
            color: #2d3748;
            text-align: center;
        }

        .game-area {
            position: relative;
            height: 400px;
            background: #f8fafc;
            border-radius: 16px;
            border: 2px dashed #e2e8f0;
            margin: 30px 0;
            overflow: hidden;
        }

        .cpu {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            box-shadow: 0 10px 30px rgba(255,107,107,0.3);
            animation: pulse 2s infinite;
        }

        .process {
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .process:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        }

        .process.p1 { background: linear-gradient(45deg, #4facfe, #00f2fe); }
        .process.p2 { background: linear-gradient(45deg, #43e97b, #38f9d7); }
        .process.p3 { background: linear-gradient(45deg, #fa709a, #fee140); }

        .process.active {
            animation: glow 1s infinite alternate;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .explanation {
            background: #f7fafc;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .resource-game {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .resource {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #8b4513;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 auto;
        }

        .resource:hover {
            transform: scale(1.05);
        }

        .resource.allocated {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.05); }
        }

        @keyframes glow {
            from { box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
            to { box-shadow: 0 8px 25px rgba(255,255,255,0.5), 0 0 30px currentColor; }
        }

        .quiz-section {
            margin-top: 40px;
        }

        .quiz-option {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 15px 20px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            border-color: #667eea;
            background: #edf2f7;
        }

        .quiz-option.correct {
            background: #c6f6d5;
            border-color: #38a169;
        }

        .quiz-option.wrong {
            background: #fed7d7;
            border-color: #e53e3e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">进程调度与死锁预防</h1>
            <p class="subtitle">通过动画和游戏理解操作系统核心概念</p>
        </div>

        <!-- 第一部分：单处理器进程调度 -->
        <div class="section">
            <h2 class="section-title">🖥️ 单处理器进程调度演示</h2>
            
            <div class="explanation">
                <h3>💡 核心概念</h3>
                <p><strong>单处理器系统</strong>：只有一个CPU核心，同一时刻只能执行一个进程。</p>
                <p><strong>并发</strong>：多个进程看似同时运行，实际是CPU快速切换执行。</p>
            </div>

            <div class="game-area" id="cpuArea">
                <div class="cpu" id="cpu">CPU</div>
                <div class="process p1" id="process1" style="top: 20%; left: 20%;">P1</div>
                <div class="process p2" id="process2" style="top: 20%; right: 20%;">P2</div>
                <div class="process p3" id="process3" style="bottom: 20%; left: 50%; transform: translateX(-50%);">P3</div>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="startScheduling()">开始调度演示</button>
                <button class="btn btn-primary" onclick="resetScheduling()">重置</button>
            </div>

            <div class="explanation">
                <h3>🎯 观察要点</h3>
                <p>注意看：虽然有3个进程，但CPU同一时刻只能处理1个进程（发光的那个）</p>
                <p><strong>答案：最多为1个</strong> - 这就是单处理器的限制！</p>
            </div>
        </div>

        <!-- 第二部分：死锁预防游戏 -->
        <div class="section">
            <h2 class="section-title">🔒 死锁预防资源分配游戏</h2>

            <div class="explanation">
                <h3>💡 死锁概念</h3>
                <p><strong>死锁</strong>：多个进程互相等待对方释放资源，导致所有进程都无法继续执行。</p>
                <p><strong>银行家算法</strong>：确保系统始终处于安全状态，预防死锁发生。</p>
            </div>

            <div class="game-area" id="resourceArea">
                <div style="text-align: center; padding: 20px;">
                    <h3>资源分配模拟器</h3>
                    <p>3个进程，每个需要2个资源R，试试不同的资源总数！</p>
                </div>

                <div class="resource-game" id="resourceGame">
                    <!-- 资源块将通过JavaScript动态生成 -->
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <p id="resourceStatus">当前资源数：4个</p>
                    <p id="deadlockStatus">状态：安全</p>
                </div>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="setResources(3)">3个资源</button>
                <button class="btn btn-primary" onclick="setResources(4)">4个资源</button>
                <button class="btn btn-primary" onclick="setResources(5)">5个资源</button>
                <button class="btn btn-primary" onclick="simulateDeadlock()">模拟死锁</button>
            </div>

            <div class="explanation">
                <h3>🧮 计算公式</h3>
                <p><strong>最少资源数 = 进程数 + 每进程最大需求 - 1</strong></p>
                <p>3个进程 + 2个资源/进程 - 1 = <strong>5个资源</strong></p>
                <p>为什么？确保至少有一个进程能获得所需的全部资源并完成执行！</p>
            </div>
        </div>

        <!-- 第三部分：互动测验 -->
        <div class="section quiz-section">
            <h2 class="section-title">🎯 知识测验</h2>

            <div class="explanation">
                <h3>问题1：单处理器系统中，3个并发进程同时占用处理器的最大数量？</h3>
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 至少为1个</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">B. 至少为3个</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">C. 最多为1个 ✓</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 最多为3个</div>
            </div>

            <div class="explanation" style="margin-top: 30px;">
                <h3>问题2：3个进程各需2个互斥资源R，不产生死锁的最少R资源数？</h3>
                <div class="quiz-option" onclick="selectAnswer2(this, false)">A. 4个</div>
                <div class="quiz-option" onclick="selectAnswer2(this, true)">B. 5个 ✓</div>
                <div class="quiz-option" onclick="selectAnswer2(this, false)">C. 6个</div>
                <div class="quiz-option" onclick="selectAnswer2(this, false)">D. 3个</div>
            </div>
        </div>
    </div>

    <script>
        let currentProcess = 0;
        let schedulingInterval;
        let resourceCount = 4;

        // 进程调度动画
        function startScheduling() {
            const processes = ['process1', 'process2', 'process3'];
            const cpu = document.getElementById('cpu');

            schedulingInterval = setInterval(() => {
                // 移除所有进程的active类
                processes.forEach(id => {
                    document.getElementById(id).classList.remove('active');
                });

                // 激活当前进程
                const activeProcess = document.getElementById(processes[currentProcess]);
                activeProcess.classList.add('active');

                // 更新CPU显示
                cpu.textContent = `CPU\n运行${processes[currentProcess].slice(-1)}`;
                cpu.style.background = getComputedStyle(activeProcess).background;

                currentProcess = (currentProcess + 1) % 3;
            }, 1500);
        }

        function resetScheduling() {
            clearInterval(schedulingInterval);
            const processes = ['process1', 'process2', 'process3'];
            processes.forEach(id => {
                document.getElementById(id).classList.remove('active');
            });
            document.getElementById('cpu').textContent = 'CPU';
            document.getElementById('cpu').style.background = 'linear-gradient(45deg, #ff6b6b, #ee5a24)';
            currentProcess = 0;
        }

        // 资源分配游戏
        function setResources(count) {
            resourceCount = count;
            updateResourceDisplay();
            checkDeadlockStatus();
        }

        function updateResourceDisplay() {
            const resourceGame = document.getElementById('resourceGame');
            resourceGame.innerHTML = '';

            for (let i = 0; i < resourceCount; i++) {
                const resource = document.createElement('div');
                resource.className = 'resource';
                resource.textContent = `R${i + 1}`;
                resource.onclick = () => toggleResource(resource);
                resourceGame.appendChild(resource);
            }

            document.getElementById('resourceStatus').textContent = `当前资源数：${resourceCount}个`;
        }

        function toggleResource(resource) {
            resource.classList.toggle('allocated');
            checkDeadlockStatus();
        }

        function checkDeadlockStatus() {
            const statusElement = document.getElementById('deadlockStatus');
            const minRequired = 5; // 3进程 + 2需求 - 1

            if (resourceCount >= minRequired) {
                statusElement.textContent = '状态：安全（无死锁风险）';
                statusElement.style.color = '#38a169';
            } else {
                statusElement.textContent = '状态：危险（可能死锁）';
                statusElement.style.color = '#e53e3e';
            }
        }

        function simulateDeadlock() {
            alert('死锁模拟：\n进程P1持有R1，等待R2\n进程P2持有R2，等待R3\n进程P3持有R3，等待R1\n→ 循环等待，系统死锁！');
        }

        // 测验功能
        function selectAnswer(element, isCorrect) {
            // 移除所有选项的样式
            const options = element.parentNode.querySelectorAll('.quiz-option');
            options.forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    alert('🎉 正确！单处理器同一时刻最多只能运行1个进程！');
                }, 100);
            } else {
                element.classList.add('wrong');
                setTimeout(() => {
                    alert('❌ 错误！请观察上面的动画演示，CPU同一时刻只能处理一个进程。');
                }, 100);
            }
        }

        function selectAnswer2(element, isCorrect) {
            const options = element.parentNode.querySelectorAll('.quiz-option');
            options.forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    alert('🎉 正确！使用公式：3 + 2 - 1 = 5个资源可以避免死锁！');
                }, 100);
            } else {
                element.classList.add('wrong');
                setTimeout(() => {
                    alert('❌ 错误！试试用银行家算法公式计算：进程数 + 最大需求 - 1');
                }, 100);
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            updateResourceDisplay();
            checkDeadlockStatus();
        };
    </script>
</body>
</html>
