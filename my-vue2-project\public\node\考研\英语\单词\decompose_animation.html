<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画：Decompose</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
            background: #f0f2f5;
            color: #333;
        }
        #animation-container {
            position: relative;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            max-width: 90%;
            width: 800px;
        }
        h1 {
            color: #1a73e8;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        #word-pronunciation {
            font-size: 1.2em;
            color: #5f6368;
            margin-bottom: 20px;
        }
        canvas {
            background-color: #e8f0fe;
            border-radius: 8px;
            display: block;
            margin: 0 auto;
        }
        #explanation {
            font-size: 1.1em;
            color: #3c4043;
            margin-top: 20px;
            padding: 0 20px;
            min-height: 50px;
            text-align: left;
            line-height: 1.6;
        }
        .controls {
            margin-top: 20px;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 1em;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin: 0 10px;
        }
        button:hover {
            background-color: #1558b8;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #9e9e9e;
            cursor: not-allowed;
            transform: none;
        }
        .etymology {
            font-weight: bold;
            color: #d93025;
        }
    </style>
</head>
<body>

    <div id="animation-container">
        <h1>Decompose</h1>
        <div id="word-pronunciation">/ˌdiːkəmˈpəʊz/</div>
        <canvas id="canvas" width="800" height="400"></canvas>
        <div id="explanation">
            <p>点击"开始动画"按钮，学习 <span class="etymology">de-</span> + <span class="etymology">compose</span> 是如何构成 "decompose" 的。</p>
        </div>
        <div class="controls">
            <button id="start-btn">开始动画</button>
            <button id="replay-btn" disabled>重新播放</button>
        </div>
    </div>

    <script>
        window.onload = () => {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            const explanationDiv = document.getElementById('explanation');
            const startBtn = document.getElementById('start-btn');
            const replayBtn = document.getElementById('replay-btn');

            let animationFrameId;
            let stage = 'idle'; // idle, pose, compose, decompose, end

            const colors = ['#4285F4', '#DB4437', '#F4B400', '#0F9D58', '#DA0D95'];
            const blocks = [];
            const finalPositions = [
                { x: 350, y: 350 }, { x: 400, y: 350 }, { x: 450, y: 350 },
                { x: 375, y: 300 }, { x: 425, y: 300 },
                { x: 400, y: 250 }
            ];

            function createBlocks() {
                blocks.length = 0;
                for (let i = 0; i < finalPositions.length; i++) {
                    blocks.push({
                        x: 50 + i * 60,
                        y: 50,
                        width: 48,
                        height: 48,
                        color: colors[i % colors.length],
                        targetX: finalPositions[i].x,
                        targetY: finalPositions[i].y,
                        isFalling: false,
                        fallSpeedY: 0,
                        rotation: 0,
                        rotationSpeed: 0
                    });
                }
            }
            
            function drawBlock(block) {
                ctx.save();
                ctx.translate(block.x + block.width / 2, block.y + block.height / 2);
                ctx.rotate(block.rotation);
                ctx.fillStyle = block.color;
                ctx.fillRect(-block.width / 2, -block.height / 2, block.width, block.height);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(-block.width / 2, -block.height / 2, block.width, block.height);
                ctx.restore();
            }

            function drawText(text, x, y, size = 24, color = '#333') {
                ctx.fillStyle = color;
                ctx.font = `bold ${size}px sans-serif`;
                ctx.textAlign = 'center';
                ctx.fillText(text, x, y);
            }

            function updateExplanation(htmlContent) {
                explanationDiv.innerHTML = `<p>${htmlContent}</p>`;
            }
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                let allInPlace = true;

                if (stage === 'pose') {
                    const block = blocks[0];
                    const speed = 2;
                    const dx = block.targetX - block.x;
                    const dy = block.targetY - block.y;
                    if (Math.abs(dx) > speed || Math.abs(dy) > speed) {
                        block.x += dx > 0 ? speed : -speed;
                        block.y += dy > 0 ? speed : -speed;
                        allInPlace = false;
                    } else {
                        block.x = block.targetX;
                        block.y = block.targetY;
                    }
                    drawBlock(block);
                    drawText('pose - 放置', canvas.width / 2, 80, 30, '#1a73e8');
                    if (allInPlace) {
                        stage = 'compose';
                        setTimeout(() => {
                           updateExplanation('词根 <span class="etymology">pose</span> 的意思是"放置"。现在，我们加上前缀 <span class="etymology">com-</span> (一起)...');
                        }, 500);
                    }
                }

                if (stage === 'compose') {
                    blocks.forEach((block, index) => {
                        const speed = 2.5;
                        const dx = block.targetX - block.x;
                        const dy = block.targetY - block.y;
                        const distance = Math.sqrt(dx*dx + dy*dy);
                        
                        if (distance > speed) {
                            block.x += (dx / distance) * speed;
                            block.y += (dy / distance) * speed;
                            allInPlace = false;
                        } else {
                            block.x = block.targetX;
                            block.y = block.targetY;
                        }
                        drawBlock(block);
                    });
                    drawText('com- (一起) + pose (放置) = compose (组成)', canvas.width / 2, 80, 30, '#0F9D58');
                    if (allInPlace) {
                        stage = 'decompose';
                         setTimeout(() => {
                           updateExplanation('它们被<span class="etymology">组合</span>在一起。现在，我们看看前缀 <span class="etymology">de-</span> (分离, 向下) 如何改变它...');
                        }, 1000);
                    }
                }

                if (stage === 'decompose') {
                    blocks.forEach(block => {
                        if (!block.isFalling) {
                            block.isFalling = true;
                            block.fallSpeedY = -4 + Math.random() * 2; // Initial upward pop
                            block.fallSpeedX = -3 + Math.random() * 6;
                            block.rotationSpeed = (-0.05 + Math.random() * 0.1);
                        }
                        
                        block.fallSpeedY += 0.1; // Gravity
                        block.y += block.fallSpeedY;
                        block.x += block.fallSpeedX;
                        block.rotation += block.rotationSpeed;
                        
                        if (block.y > canvas.height) {
                            allInPlace = true;
                        } else {
                            allInPlace = false;
                        }
                         drawBlock(block);
                    });
                    drawText('de- (分离) + compose (组成) = decompose (分解)', canvas.width / 2, 80, 30, '#DB4437');
                    if (allInPlace) {
                        stage = 'end';
                    }
                }
                
                blocks.forEach(drawBlock);

                if (stage === 'end') {
                    updateExplanation('结构被<span class="etymology">分解</span>了！所以 <span class="etymology">decompose</span> 的核心意思就是"分解"或"使腐烂"。');
                    replayBtn.disabled = false;
                    cancelAnimationFrame(animationFrameId);
                    return;
                }

                animationFrameId = requestAnimationFrame(animate);
            }

            function startAnimation() {
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                }
                startBtn.disabled = true;
                replayBtn.disabled = true;
                createBlocks();
                stage = 'pose';
                updateExplanation('我们从词根 <span class="etymology">pose</span> 开始，它的意思是"放置"。');
                animate();
            }

            startBtn.addEventListener('click', startAnimation);
            replayBtn.addEventListener('click', startAnimation);
        };
    </script>

</body>
</html> 