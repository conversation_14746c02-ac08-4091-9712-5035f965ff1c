<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POP3端口号学习 - 零基础互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
        }

        .concept-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            color: #333;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .port-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .port-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .port-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .port-card.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .port-card.wrong {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%);
            color: white;
        }

        .quiz-section {
            text-align: center;
        }

        .quiz-question {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            max-width: 600px;
            margin: 0 auto;
        }

        .option {
            background: white;
            border: 3px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option:hover {
            border-color: #667eea;
            transform: scale(1.02);
        }

        .explanation {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            display: none;
        }

        .thinking-steps {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .animated-icon {
            display: inline-block;
            animation: bounce 2s infinite;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">📧 POP3端口号学习之旅 <span class="animated-icon">🚀</span></h1>

        <!-- 概念介绍部分 -->
        <div class="section">
            <h2>🎯 什么是POP3？</h2>
            <div class="concept-box">
                <strong>POP3</strong>（Post Office Protocol 3）是一种<span class="highlight">邮件接收协议</span>，
                就像你去邮局取信一样，POP3帮助你从邮件服务器上下载邮件到你的电脑或手机上。
            </div>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="800" height="300"></canvas>
            </div>
        </div>

        <!-- 端口号概念 -->
        <div class="section">
            <h2>🚪 什么是端口号？</h2>
            <div class="concept-box">
                想象你的电脑是一栋大楼，<span class="highlight">端口号就是房间号</span>。
                不同的服务住在不同的房间里，POP3服务就住在<span class="highlight">110号房间</span>！
            </div>
            <div class="canvas-container">
                <canvas id="portCanvas" width="800" height="400"></canvas>
            </div>
        </div>

        <!-- 常见端口对比 -->
        <div class="section">
            <h2>🏠 常见服务的"房间号"对比</h2>
            <div class="port-demo">
                <div class="port-card" onclick="showPortInfo('ftp')">
                    <h3>📁 FTP</h3>
                    <div style="font-size: 2rem; margin: 10px 0;">20</div>
                    <p>文件传输服务</p>
                </div>
                <div class="port-card" onclick="showPortInfo('smtp')">
                    <h3>📤 SMTP</h3>
                    <div style="font-size: 2rem; margin: 10px 0;">25</div>
                    <p>邮件发送服务</p>
                </div>
                <div class="port-card" onclick="showPortInfo('http')">
                    <h3>🌐 HTTP</h3>
                    <div style="font-size: 2rem; margin: 10px 0;">80</div>
                    <p>网页浏览服务</p>
                </div>
                <div class="port-card correct" onclick="showPortInfo('pop3')">
                    <h3>📧 POP3</h3>
                    <div style="font-size: 2rem; margin: 10px 0;">110</div>
                    <p>邮件接收服务</p>
                </div>
            </div>
        </div>

        <!-- 做题思路 -->
        <div class="section">
            <h2>🧠 做题思路分析</h2>
            <div class="thinking-steps">
                <h3>第一步：理解题目</h3>
                <p>题目问的是<strong>POP3服务</strong>的<strong>默认TCP端口号</strong></p>
            </div>
            <div class="thinking-steps">
                <h3>第二步：回忆知识点</h3>
                <p>POP3是邮件接收协议，记住口诀：<strong>"POP3一一零，邮件接收要记牢"</strong></p>
            </div>
            <div class="thinking-steps">
                <h3>第三步：排除干扰项</h3>
                <p>• 20是FTP端口 • 25是SMTP端口 • 80是HTTP端口 • <strong>110才是POP3端口</strong></p>
            </div>
        </div>

        <!-- 互动测试 -->
        <div class="section quiz-section">
            <h2>🎮 现在来测试一下吧！</h2>
            <div class="quiz-question">
                POP3服务默认的TCP端口号是（ ）？
            </div>
            <div class="options">
                <div class="option" onclick="selectAnswer('A', 20)">A. 20</div>
                <div class="option" onclick="selectAnswer('B', 25)">B. 25</div>
                <div class="option" onclick="selectAnswer('C', 80)">C. 80</div>
                <div class="option" onclick="selectAnswer('D', 110)">D. 110</div>
            </div>
            <div class="explanation" id="explanation">
                <h3>🎉 恭喜答对了！</h3>
                <p><strong>正确答案是D. 110</strong></p>
                <p>POP3（邮件接收协议）的默认端口号是110。记住这个数字，就像记住你家的门牌号一样重要！</p>
                <div style="margin-top: 20px;">
                    <strong>记忆小技巧：</strong><br>
                    POP3 = 110，可以想象成"邮件110，紧急求助取邮件"！
                </div>
            </div>
        </div>
    </div>

    <script>
        // 概念动画
        function drawConceptAnimation() {
            const canvas = document.getElementById('conceptCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制邮件服务器
                ctx.fillStyle = '#4facfe';
                ctx.fillRect(50, 100, 150, 120);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('邮件服务器', 125, 160);
                
                // 绘制用户电脑
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(600, 100, 150, 120);
                ctx.fillStyle = 'white';
                ctx.fillText('你的电脑', 675, 160);
                
                // 绘制动态邮件传输
                const emailX = 250 + Math.sin(frame * 0.05) * 150;
                ctx.fillStyle = '#ffd93d';
                ctx.fillRect(emailX, 140, 40, 30);
                ctx.fillStyle = 'black';
                ctx.font = '12px Arial';
                ctx.fillText('📧', emailX + 20, 160);
                
                // POP3标签
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 18px Arial';
                ctx.fillText('POP3协议 (端口110)', 400, 50);
                
                frame++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 端口号动画
        function drawPortAnimation() {
            const canvas = document.getElementById('portCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制大楼
                ctx.fillStyle = '#e0e0e0';
                ctx.fillRect(300, 50, 200, 300);
                
                // 绘制房间
                const rooms = [
                    {port: 20, service: 'FTP', color: '#ff9999'},
                    {port: 25, service: 'SMTP', color: '#99ff99'},
                    {port: 80, service: 'HTTP', color: '#9999ff'},
                    {port: 110, service: 'POP3', color: '#ffff99'}
                ];
                
                rooms.forEach((room, index) => {
                    const y = 80 + index * 60;
                    const highlight = Math.sin(frame * 0.1 + index) > 0.5;
                    
                    ctx.fillStyle = highlight && room.port === 110 ? '#ffd93d' : room.color;
                    ctx.fillRect(320, y, 160, 40);
                    
                    ctx.fillStyle = 'black';
                    ctx.font = 'bold 14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${room.service} - ${room.port}`, 400, y + 25);
                });
                
                // 标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 20px Arial';
                ctx.fillText('网络服务大楼', 400, 30);
                
                frame++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 显示端口信息
        function showPortInfo(service) {
            const info = {
                ftp: '文件传输协议，用于上传下载文件',
                smtp: '邮件发送协议，用于发送邮件',
                http: '网页传输协议，用于浏览网页',
                pop3: '邮件接收协议，用于接收邮件 - 这就是我们要学的！'
            };
            
            alert(info[service]);
        }

        // 选择答案
        function selectAnswer(option, port) {
            const options = document.querySelectorAll('.option');
            const explanation = document.getElementById('explanation');
            
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt.textContent.includes(port.toString())) {
                    if (port === 110) {
                        opt.style.background = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
                        opt.style.color = 'white';
                        opt.style.animation = 'pulse 1s infinite';
                    } else {
                        opt.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%)';
                        opt.style.color = 'white';
                    }
                }
            });
            
            if (port === 110) {
                explanation.innerHTML = `
                    <h3>🎉 恭喜答对了！</h3>
                    <p><strong>正确答案是D. 110</strong></p>
                    <p>POP3（邮件接收协议）的默认端口号是110。记住这个数字，就像记住你家的门牌号一样重要！</p>
                    <div style="margin-top: 20px;">
                        <strong>记忆小技巧：</strong><br>
                        POP3 = 110，可以想象成"邮件110，紧急求助取邮件"！
                    </div>
                `;
            } else {
                explanation.innerHTML = `
                    <h3>😅 再想想看！</h3>
                    <p><strong>你选择了${option}. ${port}</strong></p>
                    <p>这个端口号不对哦！POP3的正确端口号是<strong>110</strong>。</p>
                    <p>你选择的${port}是其他服务的端口号。记住：POP3专门住在110号房间！</p>
                `;
            }
            
            explanation.style.display = 'block';
            explanation.scrollIntoView({ behavior: 'smooth' });
        }

        // 页面加载完成后启动动画
        window.addEventListener('load', () => {
            setTimeout(drawConceptAnimation, 500);
            setTimeout(drawPortAnimation, 1000);
        });
    </script>
</body>
</html>
