<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构视图学习营</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f8ff, #e0ffff);
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            box-sizing: border-box;
        }
        .container {
            background-color: #fff;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            width: 90%;
            max-width: 900px;
            box-sizing: border-box;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInSlideUp 0.8s forwards;
        }
        @keyframes fadeInSlideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-section, .explanation-section {
            margin-bottom: 25px;
            line-height: 1.8;
            font-size: 1.1em;
        }
        .question-text {
            background-color: #e6f7ff;
            border-left: 5px solid #1890ff;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: bold;
        }
        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .option-button {
            background-color: #f0f2f5;
            border: 2px solid #d9d9d9;
            border-radius: 10px;
            padding: 15px 20px;
            text-align: left;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
            display: flex;
            align-items: center;
            color: #555;
            font-weight: 500;
        }
        .option-button:hover {
            background-color: #e6f7ff;
            border-color: #1890ff;
            transform: translateY(-3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .option-button.selected {
            background-color: #bae7ff;
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .option-button.correct {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
            pointer-events: none; /* Disable click after correct answer */
        }
        .option-button.incorrect {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
            animation: shake 0.5s;
        }
        .option-prefix {
            font-weight: bold;
            font-size: 1.2em;
            margin-right: 10px;
            color: #1890ff;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            20%, 60% { transform: translateX(-5px); }
            40%, 80% { transform: translateX(5px); }
        }
        .feedback {
            text-align: center;
            font-size: 1.2em;
            margin-top: 15px;
            font-weight: bold;
        }
        .feedback.correct-text {
            color: #28a745;
        }
        .feedback.incorrect-text {
            color: #dc3545;
        }
        .explanation-title {
            text-align: center;
            margin-top: 40px;
            margin-bottom: 20px;
            color: #1890ff;
            font-size: 1.8em;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .explanation-item {
            background-color: #f7f9fc;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }
        .explanation-item:hover {
            transform: translateY(-5px);
        }
        .explanation-item h3 {
            color: #2c3e50;
            margin-top: 0;
            font-size: 1.5em;
        }
        .explanation-item p {
            margin-bottom: 15px;
        }
        .canvas-container {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
            background-color: #f0f8ff;
            border-radius: 10px;
            padding: 15px;
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        canvas {
            border: 1px dashed #ccc;
            background-color: #ffffff;
            border-radius: 8px;
            display: block;
            margin: 0 auto;
        }
        .view-controls {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .view-button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        .view-button:hover {
            background-color: #096dd9;
            transform: translateY(-2px);
        }
        .view-button.active {
            background-color: #0056b3;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        .game-intro {
            text-align: center;
            font-size: 1.1em;
            margin-bottom: 30px;
            color: #007bff;
        }
        .game-intro strong {
            color: #e67e22;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .options-container {
                grid-template-columns: 1fr;
            }
            .option-button {
                padding: 12px 15px;
            }
            .view-button {
                padding: 10px 18px;
                margin: 5px;
            }
            h1 {
                font-size: 1.8em;
            }
            .explanation-title {
                font-size: 1.5em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件架构视图探险之旅</h1>
        <p class="game-intro">欢迎来到软件架构学习营！这里我们将通过一个有趣的挑战和动画演示，带您深入了解软件架构的奥秘。<strong>您的任务是回答问题并解锁知识点！</strong></p>

        <div class="question-section">
            <h2>挑战任务：请填空！</h2>
            <div class="question-text">
                <p>考虑软件架构时，重要的是从不同的视角（perspective）来检查，这促使软件设计师考虑架构的不同属性。例如，展示功能组织的（<span id="blank1" style="color: #e67e22; font-weight: bold;">______</span>）能判断质量特性，展示并发行行为的（<span id="blank2" style="color: #e67e22; font-weight: bold;">______</span>）来记录设计元素的功能和概念接口，设计元素的功能定义了它本身在系统中的角色，这些角色包括功能、性能等。</p>
            </div>
            <h3>请选择正确的选项来填充第一个空：</h3>
            <div class="options-container" id="options">
                <button class="option-button" data-value="开发视图"><span class="option-prefix">A</span> 开发视图</button>
                <button class="option-button" data-value="配置视图"><span class="option-prefix">B</span> 配置视图</button>
                <button class="option-button" data-value="部署视图"><span class="option-prefix">C</span> 部署视图</button>
                <button class="option-button" data-value="物理视图"><span class="option-prefix">D</span> 物理视图</button>
            </div>
            <div class="feedback" id="feedback"></div>
        </div>
    </div>

    <div class="container explanation-container" style="display: none;">
        <h2 class="explanation-title">🎉 恭喜您！现在开始解锁知识宝藏！ 🎉</h2>
        <div class="explanation-section">
            <p>这道题目主要考察您对软件架构多视角概念的理解。在软件架构中，我们从不同的“视角”或“视图”来描绘系统结构，就像从不同角度看一栋建筑，每个角度都能看到不同的细节和特性！</p>

            <div class="explanation-item">
                <h3>🔍 题目解析</h3>
                <p>原题目中的第一个空，正确答案是**配置视图**。第二个空，根据解析，应该是**逻辑视图**。</p>
                <p>所以，完整的句子是：展示功能组织的（**配置视图**）能判断质量特性，展示并发行行为的（**逻辑视图**）来记录设计元素的功能和概念接口。</p>
                <p>这可能和您平时接触的“4+1视图”模型略有出入，但这是题目提供的解释，所以我们以此为准进行深入学习！</p>
            </div>

            <div class="explanation-item">
                <h3>💡 什么是软件架构视图？</h3>
                <p>软件架构视图是描述软件系统各个方面的模型或抽象。它们帮助我们理解系统的结构、行为和交互，以便更好地设计、开发、部署和维护系统。</p>
                <p>最经典的软件架构视图模型是Philippe Kruchten的“4+1视图模型”，它包括：</p>
                <ul>
                    <li>**逻辑视图 (Logical View)**：关注系统的功能需求，描述系统提供给最终用户的功能，以及这些功能的组成和交互。</li>
                    <li>**进程视图 (Process View)**：关注系统的并发性、性能和分布式，描述运行时进程和它们之间的通信。</li>
                    <li>**开发视图 (Development View) / 实现视图 (Implementation View)**：关注程序员的组织和管理，描述系统在开发环境中是如何组织的。</li>
                    <li>**物理视图 (Physical View) / 部署视图 (Deployment View)**：关注系统的拓扑结构和物理部署，描述软件组件如何映射到硬件节点上。</li>
                    <li>**场景视图 (Scenario View) / 用例视图**：整合前四个视图，描述它们如何协同工作，并通过用例来说明系统的功能。</li>
                </ul>
                <p>而题目中提及的“配置视图”也是一个重要的视角，它通常更侧重于**如何构建、组装和管理系统的可部署单元**，以及这些配置如何影响质量属性。</p>
            </div>

            <div class="explanation-item">
                <h3>🎯 核心知识点：配置视图 vs 逻辑视图</h3>
                <p>尽管标准“4+1”模型中“逻辑视图”常用来描述功能和质量特性，但本题中，它将“配置视图”与“功能组织”和“质量特性”联系起来，这可能强调了配置管理对最终系统质量的影响。而“逻辑视图”则明确被指定用于记录设计元素的功能和概念接口。</p>
                <p>这提醒我们，在不同的语境下，对视图的侧重和定义会有所不同。重要的是理解每个视图的**核心目的**！</p>
            </div>
        </div>

        <h2 class="explanation-title">✨ 动画演示：软件架构视图的魔法世界 ✨</h2>
        <p style="text-align: center; margin-bottom: 20px;">点击下方的按钮，看看不同视图是如何“动”起来的吧！</p>
        <div class="view-controls">
            <button class="view-button active" data-view="logical">逻辑视图</button>
            <button class="view-button" data-view="process">进程视图</button>
            <button class="view-button" data-view="development">开发视图</button>
            <button class="view-button" data-view="deployment">部署视图</button>
            <button class="view-button" data-view="configuration">配置视图</button>
        </div>
        <div class="canvas-container">
            <canvas id="architectureCanvas" width="800" height="400"></canvas>
            <p id="canvasDescription" style="font-style: italic; margin-top: 10px; color: #666;">点击上方按钮查看对应视图的动画演示。</p>
        </div>
    </div>

    <script>
        const optionsContainer = document.getElementById('options');
        const feedbackDiv = document.getElementById('feedback');
        const blank1Span = document.getElementById('blank1');
        const blank2Span = document.getElementById('blank2');
        const explanationContainer = document.querySelector('.explanation-container');
        const viewButtons = document.querySelectorAll('.view-button');
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');
        const canvasDescription = document.getElementById('canvasDescription');

        const correctAnswer = '配置视图'; // Corresponding to B

        let currentView = 'logical'; // Default view for canvas

        // Function to handle option clicks
        optionsContainer.addEventListener('click', (event) => {
            const selectedButton = event.target.closest('.option-button');
            if (!selectedButton || selectedButton.classList.contains('correct') || selectedButton.classList.contains('incorrect')) {
                return; // Do nothing if not a button or already answered
            }

            // Reset previous selections
            document.querySelectorAll('.option-button').forEach(btn => {
                btn.classList.remove('selected', 'correct', 'incorrect');
            });

            selectedButton.classList.add('selected');
            const selectedValue = selectedButton.dataset.value;

            if (selectedValue === correctAnswer) {
                selectedButton.classList.add('correct');
                feedbackDiv.textContent = '🎉 太棒了！您答对了！第一个空就是 “配置视图”！';
                feedbackDiv.classList.remove('incorrect-text');
                feedbackDiv.classList.add('correct-text');
                blank1Span.textContent = '配置视图';
                blank2Span.textContent = '逻辑视图'; // Fill the second blank too
                // Show explanation section
                setTimeout(() => {
                    explanationContainer.style.display = 'block';
                    explanationContainer.style.animation = 'fadeInSlideUp 0.8s forwards';
                    document.querySelector('.container').style.marginBottom = '20px'; // Adjust margin
                    // Trigger initial logical view animation
                    drawLogicalView();
                    canvasDescription.textContent = '逻辑视图：关注系统功能和模块构成，展示各个模块如何协作完成业务功能。';
                }, 1000); // Delay to show feedback
            } else {
                selectedButton.classList.add('incorrect');
                feedbackDiv.textContent = '🤔 哦豁，差一点！再想想看哪个视图更侧重于“功能组织”的质量特性呢？';
                feedbackDiv.classList.remove('correct-text');
                feedbackDiv.classList.add('incorrect-text');
            }
        });

        // Canvas animation logic
        const clearCanvas = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        };

        const drawText = (text, x, y, color = '#333', font = '18px Arial') => {
            ctx.fillStyle = color;
            ctx.font = font;
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        };

        const drawBox = (x, y, width, height, text, color = '#1890ff') => {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            drawText(text, x + width / 2, y + height / 2 + 5, 'white');
        };

        const drawArrow = (fromX, fromY, toX, toY, color = '#333') => {
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // Arrowhead
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fill();
        };

        let animationFrameId;

        const animate = (drawFunction) => {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            clearCanvas();
            let startTime = null;
            const step = (timestamp) => {
                if (!startTime) startTime = timestamp;
                const progress = (timestamp - startTime) / 2000; // 2 seconds animation
                drawFunction(progress);
                if (progress < 1) {
                    animationFrameId = requestAnimationFrame(step);
                }
            };
            animationFrameId = requestAnimationFrame(step);
        };

        const drawLogicalView = (progress = 1) => {
            clearCanvas();
            canvasDescription.textContent = '逻辑视图：关注系统功能和模块构成，展示各个模块如何协作完成业务功能。';

            const moduleWidth = 120;
            const moduleHeight = 70;
            const gap = 50;

            const x1 = canvas.width / 2 - moduleWidth - gap / 2;
            const x2 = canvas.width / 2 + gap / 2;
            const y1 = canvas.height / 2 - moduleHeight / 2;

            ctx.globalAlpha = Math.min(1, progress * 2); // Fade in faster

            drawBox(x1, y1, moduleWidth, moduleHeight, "用户管理", '#3498db');
            drawBox(x2, y1, moduleWidth, moduleHeight, "订单处理", '#e74c3c');
            drawBox(canvas.width / 2 - moduleWidth / 2, y1 - moduleHeight - gap / 2, moduleWidth, moduleHeight, "商品管理", '#27ae60');

            ctx.globalAlpha = Math.min(1, (progress - 0.3) * 2); // Arrows fade in later
            if (progress > 0.3) {
                drawArrow(x1 + moduleWidth / 2, y1, canvas.width / 2 - moduleWidth / 2, y1 - moduleHeight - gap / 2 + moduleHeight, '#555');
                drawText("查询", (x1 + moduleWidth / 2 + canvas.width / 2 - moduleWidth / 2) / 2, (y1 + y1 - moduleHeight - gap / 2 + moduleHeight) / 2 - 10);

                drawArrow(x2 + moduleWidth / 2, y1, canvas.width / 2 - moduleWidth / 2, y1 - moduleHeight - gap / 2 + moduleHeight, '#555');
                drawText("下单", (x2 + moduleWidth / 2 + canvas.width / 2 - moduleWidth / 2) / 2, (y1 + y1 - moduleHeight - gap / 2 + moduleHeight) / 2 - 10);
            }
            ctx.globalAlpha = 1;
        };

        const drawProcessView = (progress = 1) => {
            clearCanvas();
            canvasDescription.textContent = '进程视图：展示系统运行时如何协调和通信，关注并发和性能。';

            const circleRadius = 40;
            const processY = canvas.height / 2;
            const x1 = canvas.width / 4;
            const x2 = canvas.width / 2;
            const x3 = canvas.width * 3 / 4;

            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.globalAlpha = Math.min(1, progress * 2);

            // Processes
            ctx.fillStyle = '#f39c12';
            ctx.beginPath();
            ctx.arc(x1, processY, circleRadius, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
            drawText("用户服务", x1, processY + 5, 'white');

            ctx.fillStyle = '#2ecc71';
            ctx.beginPath();
            ctx.arc(x2, processY, circleRadius, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
            drawText("订单服务", x2, processY + 5, 'white');

            ctx.fillStyle = '#9b59b6';
            ctx.beginPath();
            ctx.arc(x3, processY, circleRadius, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
            drawText("支付服务", x3, processY + 5, 'white');

            ctx.globalAlpha = Math.min(1, (progress - 0.3) * 2);
            if (progress > 0.3) {
                // Arrows (data flow)
                const arrowLength = circleRadius + (canvas.width / 4 - circleRadius) * progress;
                drawArrow(x1 + circleRadius, processY, x1 + arrowLength, processY, '#333');
                drawText("请求订单", (x1 + x1 + arrowLength) / 2, processY - 20);

                drawArrow(x2 + circleRadius, processY, x2 + arrowLength, processY, '#333');
                drawText("请求支付", (x2 + x2 + arrowLength) / 2, processY - 20);
            }
            ctx.globalAlpha = 1;
        };

        const drawDevelopmentView = (progress = 1) => {
            clearCanvas();
            canvasDescription.textContent = '开发视图：展示系统在开发环境中的结构，如代码文件、目录和组件库。';

            const folderColor = '#feca57';
            const fileColor = '#a29bfe';

            const folderX = canvas.width / 4;
            const folderY = canvas.height / 3;
            const folderWidth = 150;
            const folderHeight = 100;

            ctx.globalAlpha = Math.min(1, progress * 2);

            // Draw Folders
            ctx.fillStyle = folderColor;
            ctx.fillRect(folderX, folderY, folderWidth, folderHeight);
            ctx.fillRect(folderX + folderWidth * 0.7, folderY - 20, folderWidth * 0.6, 20);
            drawText("src/", folderX + folderWidth / 2, folderY + folderHeight + 20, '#333');

            ctx.fillRect(folderX + folderWidth + 80, folderY, folderWidth, folderHeight);
            ctx.fillRect(folderX + folderWidth + 80 + folderWidth * 0.7, folderY - 20, folderWidth * 0.6, 20);
            drawText("node_modules/", folderX + folderWidth + 80 + folderWidth / 2, folderY + folderHeight + 20, '#333');

            // Draw Files
            ctx.fillStyle = fileColor;
            ctx.fillRect(folderX + 20, folderY + 30, 80, 20);
            drawText("index.js", folderX + 60, folderY + 45, 'white', '12px Arial');

            ctx.fillRect(folderX + 20, folderY + 60, 80, 20);
            drawText("app.js", folderX + 60, folderY + 75, 'white', '12px Arial');

            ctx.globalAlpha = 1;
        };

        const drawDeploymentView = (progress = 1) => {
            clearCanvas();
            canvasDescription.textContent = '部署视图：描绘软件组件如何映射到物理硬件节点，关注网络拓扑。';

            const serverColor = '#c0392b';
            const dbColor = '#2980b9';

            const serverWidth = 150;
            const serverHeight = 100;
            const dbRadius = 40;

            const server1X = canvas.width / 4 - serverWidth / 2;
            const server2X = canvas.width * 3 / 4 - serverWidth / 2;
            const serverY = canvas.height / 2 - serverHeight / 2;
            const dbX = canvas.width / 2;
            const dbY = canvas.height * 3 / 4;

            ctx.globalAlpha = Math.min(1, progress * 2);

            // Servers
            drawBox(server1X, serverY, serverWidth, serverHeight, "Web服务器", serverColor);
            drawBox(server2X, serverY, serverWidth, serverHeight, "应用服务器", serverColor);

            // Database
            ctx.fillStyle = dbColor;
            ctx.beginPath();
            ctx.ellipse(dbX, dbY, dbRadius, dbRadius * 0.6, 0, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
            drawText("数据库", dbX, dbY + 5, 'white');

            ctx.globalAlpha = Math.min(1, (progress - 0.3) * 2);
            if (progress > 0.3) {
                // Network connections
                drawArrow(server1X + serverWidth / 2, serverY + serverHeight, dbX, dbY - dbRadius * 0.6, '#333');
                drawArrow(server2X + serverWidth / 2, serverY + serverHeight, dbX, dbY - dbRadius * 0.6, '#333');
                drawArrow(server1X + serverWidth, serverY + serverHeight / 2, server2X, serverY + serverHeight / 2, '#333');
            }
            ctx.globalAlpha = 1;
        };

        const drawConfigurationView = (progress = 1) => {
            clearCanvas();
            canvasDescription.textContent = '配置视图：展示组件如何被组装和配置，强调它们如何影响系统的质量特性。';

            const compWidth = 80;
            const compHeight = 60;
            const configWidth = 180;
            const configHeight = 100;

            const comp1X = canvas.width / 2 - compWidth - 40;
            const comp2X = canvas.width / 2 + 40;
            const compY = canvas.height / 4;

            const configX = canvas.width / 2 - configWidth / 2;
            const configY = canvas.height / 2 + 30;

            ctx.globalAlpha = Math.min(1, progress * 2);

            // Components
            drawBox(comp1X, compY, compWidth, compHeight, "模块A", '#ff7675');
            drawBox(comp2X, compY, compWidth, compHeight, "模块B", '#fdcb6e');

            // Configuration File/Process
            ctx.fillStyle = '#55efc4';
            ctx.fillRect(configX, configY, configWidth, configHeight);
            ctx.strokeStyle = '#00b894';
            ctx.lineWidth = 2;
            ctx.strokeRect(configX, configY, configWidth, configHeight);
            drawText("配置文件 / 组装", configX + configWidth / 2, configY + configHeight / 2 - 10, '#333', '16px Arial');
            drawText("质量特性优化", configX + configWidth / 2, configY + configHeight / 2 + 15, '#333', '14px Arial');

            ctx.globalAlpha = Math.min(1, (progress - 0.3) * 2);
            if (progress > 0.3) {
                // Arrows from components to config
                drawArrow(comp1X + compWidth / 2, compY + compHeight, configX + configWidth / 4, configY, '#333');
                drawArrow(comp2X + compWidth / 2, compY + compHeight, configX + configWidth * 3 / 4, configY, '#333');
                // Quality impact arrow
                ctx.strokeStyle = '#4CAF50';
                ctx.setLineDash([5, 5]); // Dashed line
                drawArrow(configX + configWidth / 2, configY + configHeight, canvas.width / 2, canvas.height - 30, '#4CAF50');
                drawText("提高可维护性", configX + configWidth / 2 + 50, configY + configHeight + 30, '#4CAF50');
                ctx.setLineDash([]); // Reset line dash
            }
            ctx.globalAlpha = 1;
        };


        const viewDrawFunctions = {
            logical: drawLogicalView,
            process: drawProcessView,
            development: drawDevelopmentView,
            deployment: drawDeploymentView,
            configuration: drawConfigurationView
        };

        viewButtons.forEach(button => {
            button.addEventListener('click', () => {
                viewButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                currentView = button.dataset.view;
                animate(viewDrawFunctions[currentView]);
            });
        });

        // Initial canvas setup
        // This will be called after the correct answer is selected.
        // For now, let's just make sure it's cleared if someone directly opens it.
        clearCanvas();

    </script>
</body>
</html> 