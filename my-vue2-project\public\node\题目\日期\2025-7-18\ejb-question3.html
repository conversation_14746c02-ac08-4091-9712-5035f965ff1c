<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EJB 消息驱动构件 - 异步处理学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .game-board {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 40px;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 16px;
            border-left: 4px solid #667eea;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            background: white;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .option-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .option-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .option-card.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            transform: scale(1.05);
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            animation: shake 0.5s ease-in-out;
        }

        .option-letter {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 12px;
        }

        .option-text {
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .explanation {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease-out;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .explanation h3 {
            color: #f57c00;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .async-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .demo-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            text-align: center;
        }

        .demo-card:hover {
            transform: translateY(-5px);
        }

        .demo-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
        }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .message-queue {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 12px;
        }

        .queue-item {
            width: 40px;
            height: 40px;
            background: #2196F3;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            animation: queueMove 3s ease-in-out infinite;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes queueMove {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(20px); }
        }

        @keyframes messageFlow {
            0% { transform: translateX(-50px) scale(0); opacity: 0; }
            50% { opacity: 1; transform: scale(1); }
            100% { transform: translateX(50px) scale(0); opacity: 0; }
        }

        .message-particle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #FF9800;
            border-radius: 50%;
            animation: messageFlow 2s ease-in-out infinite;
        }

        .async-indicator {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            display: inline-block;
            margin: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">📨 EJB 消息驱动构件</h1>
            <p class="subtitle">探索异步处理的魅力 - JMS消息与并发操作</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-text">
                    <strong>题目：</strong>EJB是企业级Java构件，用于开发和部署多层结构的、分布式的、面向对象的Java应用系统。其中，<span style="background: #d1ecf1; padding: 2px 8px; border-radius: 4px;">（会话型构件）</span>负责完成服务端与客户端的交互；<span style="background: #d4edda; padding: 2px 8px; border-radius: 4px;">（实体型构件）</span>用于数据持久化来简化数据库开发工作；<span style="background: #fff3cd; padding: 2px 8px; border-radius: 4px;">（请作答此空）</span>主要用来处理并发和异步访问操作。
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="700" height="400"></canvas>
            </div>

            <div class="options-grid">
                <div class="option-card" data-answer="A">
                    <div class="option-letter">A</div>
                    <div class="option-text">会话型构件</div>
                </div>
                <div class="option-card" data-answer="B">
                    <div class="option-letter">B</div>
                    <div class="option-text">实体型构件</div>
                </div>
                <div class="option-card" data-answer="C">
                    <div class="option-letter">C</div>
                    <div class="option-text">COM构件</div>
                </div>
                <div class="option-card" data-answer="D">
                    <div class="option-letter">D</div>
                    <div class="option-text">消息驱动构件</div>
                </div>
            </div>

            <div class="interactive-demo">
                <h4>🎮 互动演示：消息驱动Bean的异步处理</h4>
                <button class="demo-button" onclick="demoSendMessage()">发送消息</button>
                <button class="demo-button" onclick="demoAsyncProcess()">异步处理</button>
                <button class="demo-button" onclick="demoOrderProcess()">订单处理演示</button>
                <button class="demo-button" onclick="demoConcurrent()">并发处理</button>
                
                <div class="message-queue" id="messageQueue">
                    <div>消息队列:</div>
                    <div class="async-indicator">异步处理中...</div>
                </div>
            </div>

            <div class="explanation" id="explanation">
                <h3>🎉 正确答案：D - 消息驱动构件</h3>
                <p><strong>解析：</strong>消息驱动Bean(MDB)是EJB3.0中引入的新企业Bean，专门处理异步和并发操作。</p>
                
                <div class="async-demo">
                    <div class="demo-card">
                        <div class="demo-icon">⚡</div>
                        <h4>异步处理</h4>
                        <p>客户端调用MDB后无需等待，立刻返回，MDB异步处理请求</p>
                    </div>
                    <div class="demo-card">
                        <div class="demo-icon">📬</div>
                        <h4>JMS消息</h4>
                        <p>基于JMS消息系统，只能接收和处理JMS消息</p>
                    </div>
                    <div class="demo-card">
                        <div class="demo-icon">🔄</div>
                        <h4>并发处理</h4>
                        <p>支持多个消息同时处理，提高系统并发能力</p>
                    </div>
                </div>

                <p><strong>核心特点：</strong></p>
                <ul style="margin-left: 20px; line-height: 1.8;">
                    <li>异步无状态会话Bean，避免客户端长时间等待</li>
                    <li>基于JMS消息机制，支持消息队列和主题</li>
                    <li>适合订单处理、邮件发送等异步场景</li>
                    <li>容器自动管理消息接收和Bean实例</li>
                </ul>

                <p><strong>应用场景：</strong>订单处理、邮件通知、日志记录、批量数据处理等需要异步处理的业务场景。</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const options = document.querySelectorAll('.option-card');
        const explanation = document.getElementById('explanation');

        let animationFrame;
        let particles = [];
        let messages = [];
        let processingAnimation = false;

        // 初始化画布
        function initCanvas() {
            drawAsyncProcessingDemo();
            animate();
        }

        function drawAsyncProcessingDemo() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制客户端
            drawComponent(50, 50, 120, 80, '客户端', '#2196F3', '💻');
            
            // 绘制消息队列
            drawComponent(250, 50, 120, 80, 'JMS\n消息队列', '#FF9800', '📬');
            
            // 绘制消息驱动Bean
            drawComponent(450, 50, 150, 80, '消息驱动Bean\n(MDB)', '#4CAF50', '⚡');

            // 绘制异步处理流程
            drawAsyncFlow();
            
            // 绘制消息
            drawMessages();
            
            // 绘制说明文字
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('发送消息', 150, 40);
            ctx.fillText('异步处理', 375, 40);
            
            // 绘制异步指示器
            if (processingAnimation) {
                drawAsyncIndicators();
            }
        }

        function drawComponent(x, y, width, height, text, color, icon) {
            // 绘制圆角矩形
            const gradient = ctx.createLinearGradient(x, y, x, y + height);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, adjustBrightness(color, -20));
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, 12);
            ctx.fill();
            
            // 绘制边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制图标
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(icon, x + width/2, y + 35);
            
            // 绘制文字
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 14px Arial';
            const lines = text.split('\n');
            lines.forEach((line, index) => {
                ctx.fillText(line, x + width/2, y + 55 + index * 18);
            });
        }

        function drawAsyncFlow() {
            // 绘制异步流程箭头
            drawArrow(170, 90, 250, 90, '#FF9800');
            drawArrow(370, 90, 450, 90, '#4CAF50');
            
            // 绘制立即返回的箭头
            drawCurvedArrow(110, 50, 110, 150, '#2196F3', '立即返回');
        }

        function drawArrow(x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 绘制箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 12 * Math.cos(angle - Math.PI/6), y2 - 12 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 12 * Math.cos(angle + Math.PI/6), y2 - 12 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function drawCurvedArrow(x1, y1, x2, y2, color, label) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.arc(x1, (y1 + y2) / 2, Math.abs(y2 - y1) / 2, -Math.PI/2, Math.PI/2, false);
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 绘制标签
            ctx.fillStyle = color;
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x1 - 30, (y1 + y2) / 2);
        }

        function drawMessages() {
            messages.forEach(message => {
                ctx.fillStyle = message.color;
                ctx.beginPath();
                ctx.arc(message.x, message.y, 8, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = '#fff';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('M', message.x, message.y + 3);
                
                // 更新消息位置
                message.x += message.vx;
                if (message.x > canvas.width) {
                    message.x = -20;
                }
            });
        }

        function drawAsyncIndicators() {
            const time = Date.now() * 0.003;
            
            // 绘制处理中的指示器
            for (let i = 0; i < 3; i++) {
                const x = 500 + Math.sin(time + i * 0.5) * 20;
                const y = 200 + i * 30;
                
                ctx.fillStyle = `hsl(${120 + i * 30}, 70%, 50%)`;
                ctx.beginPath();
                ctx.arc(x, y, 6, 0, Math.PI * 2);
                ctx.fill();
            }
            
            // 绘制并发处理文字
            ctx.fillStyle = '#4CAF50';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('并发处理中...', 525, 180);
        }

        function createParticles(x, y, color) {
            for (let i = 0; i < 12; i++) {
                particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 6,
                    vy: (Math.random() - 0.5) * 6,
                    life: 1,
                    color: color
                });
            }
        }

        function updateParticles() {
            particles = particles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life -= 0.02;
                particle.vy += 0.1;
                
                if (particle.life > 0) {
                    ctx.save();
                    ctx.globalAlpha = particle.life;
                    ctx.fillStyle = particle.color;
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                    return true;
                }
                return false;
            });
        }

        function animate() {
            drawAsyncProcessingDemo();
            updateParticles();
            animationFrame = requestAnimationFrame(animate);
        }

        function adjustBrightness(color, amount) {
            return color; // 简化处理
        }

        // 选项点击事件
        options.forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                
                options.forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'D') {
                    this.classList.add('correct');
                    explanation.classList.add('show');
                    createParticles(canvas.width/2, canvas.height/2, '#4CAF50');
                    processingAnimation = true;
                } else {
                    this.classList.add('wrong');
                    createParticles(canvas.width/2, canvas.height/2, '#f44336');
                }
            });
        });

        // 演示函数
        function demoSendMessage() {
            messages.push({
                x: 50,
                y: 90,
                vx: 3,
                color: '#FF9800'
            });
            createParticles(310, 90, '#FF9800');
            showTooltip('客户端发送JMS消息到消息队列');
        }

        function demoAsyncProcess() {
            processingAnimation = true;
            createParticles(525, 90, '#4CAF50');
            showTooltip('MDB异步处理消息，客户端无需等待');
            setTimeout(() => processingAnimation = false, 4000);
        }

        function demoOrderProcess() {
            demoSendMessage();
            setTimeout(() => {
                processingAnimation = true;
                showTooltip('订单处理：接收订单→异步处理→更新状态');
            }, 1000);
            setTimeout(() => processingAnimation = false, 5000);
        }

        function demoConcurrent() {
            // 发送多个消息
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    messages.push({
                        x: 50,
                        y: 90 + i * 20,
                        vx: 3 + i * 0.5,
                        color: `hsl(${30 + i * 60}, 70%, 50%)`
                    });
                }, i * 500);
            }
            
            processingAnimation = true;
            createParticles(525, 90, '#4CAF50');
            showTooltip('多个消息并发处理，提高系统吞吐量');
            setTimeout(() => processingAnimation = false, 6000);
        }

        function showTooltip(text) {
            const tooltip = document.createElement('div');
            tooltip.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeInOut 3s ease-in-out;
                max-width: 300px;
                text-align: center;
            `;
            tooltip.textContent = text;
            document.body.appendChild(tooltip);
            
            setTimeout(() => {
                if (document.body.contains(tooltip)) {
                    document.body.removeChild(tooltip);
                }
            }, 3000);
        }

        // Canvas点击交互
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            if (x >= 250 && x <= 370 && y >= 50 && y <= 130) {
                demoSendMessage();
            } else if (x >= 450 && x <= 600 && y >= 50 && y <= 130) {
                demoAsyncProcess();
            }
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            }
        `;
        document.head.appendChild(style);

        // 初始化
        initCanvas();
    </script>
</body>
</html>
