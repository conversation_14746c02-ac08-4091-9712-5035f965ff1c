<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧耦合多机系统学习游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .game-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .quiz-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.3);
        }

        .quiz-option.wrong {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.3);
        }

        .score {
            text-align: center;
            font-size: 1.2rem;
            margin-top: 20px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .game-area {
                padding: 20px;
            }
            
            canvas {
                max-width: 100%;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ 紧耦合多机系统学习游戏</h1>
            <p>通过互动动画理解计算机系统架构</p>
        </div>

        <div class="game-area">
            <h2 class="section-title">🎮 系统架构可视化</h2>
            <div class="canvas-container">
                <canvas id="systemCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showTightCoupling()">紧耦合系统</button>
                <button class="btn" onclick="showLooseCoupling()">松耦合系统</button>
                <button class="btn" onclick="showCommunication()">通信演示</button>
                <button class="btn" onclick="resetAnimation()">重置</button>
            </div>
        </div>

        <div class="explanation">
            <h3>💡 知识要点</h3>
            <p><strong>紧耦合多机系统：</strong>计算机间通过高速总线或开关连接，使用<strong>共享内存</strong>进行通信，连接紧密，数据传输速度快。</p>
            <p><strong>松耦合多机系统：</strong>计算机间通过通道或通信线路连接，连接相对松散，通过网络进行通信。</p>
            <p><strong>SMP（对称多处理器）：</strong>属于紧耦合系统，多个处理器共享同一内存和I/O系统。</p>
        </div>

        <div class="quiz-section">
            <h2 class="section-title" style="color: white;">🧠 知识测试</h2>
            <div class="quiz-question">
                紧耦合多机系统一般通过什么实现多机间的通信？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 因特网</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">B. 共享内存</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">C. 进程通信</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 共享寄存器</div>
            </div>
            <div class="score" id="scoreDisplay">点击选项开始答题！</div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('systemCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentAnimation = null;

        // 动画状态
        let animationProgress = 0;
        let dataPackets = [];

        class DataPacket {
            constructor(x, y, targetX, targetY, color = '#4CAF50') {
                this.x = x;
                this.y = y;
                this.targetX = targetX;
                this.targetY = targetY;
                this.color = color;
                this.speed = 2;
                this.size = 8;
                this.trail = [];
            }

            update() {
                this.trail.push({x: this.x, y: this.y});
                if (this.trail.length > 10) this.trail.shift();

                const dx = this.targetX - this.x;
                const dy = this.targetY - this.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance > this.speed) {
                    this.x += (dx / distance) * this.speed;
                    this.y += (dy / distance) * this.speed;
                } else {
                    this.x = this.targetX;
                    this.y = this.targetY;
                }
            }

            draw() {
                // 绘制轨迹
                ctx.strokeStyle = this.color + '40';
                ctx.lineWidth = 2;
                ctx.beginPath();
                for (let i = 0; i < this.trail.length - 1; i++) {
                    const alpha = i / this.trail.length;
                    ctx.globalAlpha = alpha * 0.5;
                    if (i === 0) {
                        ctx.moveTo(this.trail[i].x, this.trail[i].y);
                    } else {
                        ctx.lineTo(this.trail[i].x, this.trail[i].y);
                    }
                }
                ctx.stroke();
                ctx.globalAlpha = 1;

                // 绘制数据包
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                
                // 发光效果
                ctx.shadowColor = this.color;
                ctx.shadowBlur = 15;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size * 0.6, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }
        }

        function drawProcessor(x, y, size, label, isActive = false) {
            // 处理器外框
            ctx.fillStyle = isActive ? '#4CAF50' : '#2196F3';
            ctx.shadowColor = ctx.fillStyle;
            ctx.shadowBlur = isActive ? 20 : 10;
            ctx.fillRect(x - size/2, y - size/2, size, size);
            ctx.shadowBlur = 0;

            // 处理器内部
            ctx.fillStyle = '#fff';
            ctx.fillRect(x - size/2 + 10, y - size/2 + 10, size - 20, size - 20);

            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 5);

            // 活动指示器
            if (isActive) {
                ctx.fillStyle = '#4CAF50';
                ctx.beginPath();
                ctx.arc(x + size/2 - 10, y - size/2 + 10, 5, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function drawMemory(x, y, width, height, label, isShared = false) {
            // 内存块
            ctx.fillStyle = isShared ? '#FF9800' : '#9C27B0';
            ctx.shadowColor = ctx.fillStyle;
            ctx.shadowBlur = 15;
            ctx.fillRect(x - width/2, y - height/2, width, height);
            ctx.shadowBlur = 0;

            // 内存条纹
            ctx.fillStyle = '#fff';
            for (let i = 0; i < 5; i++) {
                ctx.fillRect(x - width/2 + 10, y - height/2 + 10 + i * 15, width - 20, 8);
            }

            // 标签
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + height/2 + 20);
        }

        function drawConnection(x1, y1, x2, y2, type = 'bus', isActive = false) {
            ctx.strokeStyle = isActive ? '#4CAF50' : '#666';
            ctx.lineWidth = isActive ? 4 : 2;
            
            if (type === 'bus') {
                // 总线连接（直线）
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
                
                // 总线标识
                if (isActive) {
                    ctx.strokeStyle = '#4CAF50';
                    ctx.lineWidth = 6;
                    ctx.globalAlpha = 0.3;
                    ctx.stroke();
                    ctx.globalAlpha = 1;
                }
            } else {
                // 网络连接（波浪线）
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                const steps = 20;
                for (let i = 1; i <= steps; i++) {
                    const x = x1 + (x2 - x1) * (i / steps);
                    const y = y1 + (y2 - y1) * (i / steps) + Math.sin(i * 0.5) * 10;
                    ctx.lineTo(x, y);
                }
                ctx.stroke();
            }
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function showTightCoupling() {
            currentAnimation = 'tight';
            animationProgress = 0;
            dataPackets = [];
            animate();
        }

        function showLooseCoupling() {
            currentAnimation = 'loose';
            animationProgress = 0;
            dataPackets = [];
            animate();
        }

        function showCommunication() {
            currentAnimation = 'communication';
            animationProgress = 0;
            dataPackets = [];
            
            // 创建数据包
            dataPackets.push(new DataPacket(150, 150, 400, 250, '#4CAF50'));
            dataPackets.push(new DataPacket(650, 150, 400, 250, '#2196F3'));
            dataPackets.push(new DataPacket(400, 250, 150, 350, '#FF9800'));
            dataPackets.push(new DataPacket(400, 250, 650, 350, '#9C27B0'));
            
            animate();
        }

        function resetAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            currentAnimation = null;
            dataPackets = [];
            clearCanvas();
            
            // 绘制默认状态
            drawProcessor(150, 150, 80, 'CPU1');
            drawProcessor(650, 150, 80, 'CPU2');
            drawMemory(400, 250, 120, 80, '共享内存', true);
            drawConnection(190, 150, 360, 250, 'bus');
            drawConnection(610, 150, 440, 250, 'bus');
        }

        function animate() {
            clearCanvas();
            
            if (currentAnimation === 'tight') {
                // 紧耦合系统动画
                const pulse = Math.sin(animationProgress * 0.1) * 0.5 + 0.5;
                
                drawProcessor(150, 150, 80, 'CPU1', pulse > 0.5);
                drawProcessor(650, 150, 80, 'CPU2', pulse < 0.5);
                drawMemory(400, 250, 120, 80, '共享内存', true);
                
                drawConnection(190, 150, 360, 250, 'bus', pulse > 0.7);
                drawConnection(610, 150, 440, 250, 'bus', pulse < 0.3);
                
                // 高速总线标签
                ctx.fillStyle = '#4CAF50';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('高速总线', 400, 200);
                
            } else if (currentAnimation === 'loose') {
                // 松耦合系统动画
                drawProcessor(150, 150, 80, 'CPU1');
                drawProcessor(650, 150, 80, 'CPU2');
                drawMemory(150, 350, 100, 60, '内存1');
                drawMemory(650, 350, 100, 60, '内存2');
                
                drawConnection(150, 190, 150, 310, 'bus');
                drawConnection(650, 190, 650, 310, 'bus');
                drawConnection(190, 150, 610, 150, 'network');
                
                // 网络连接标签
                ctx.fillStyle = '#FF5722';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('网络连接', 400, 130);
                
            } else if (currentAnimation === 'communication') {
                // 通信演示动画
                drawProcessor(150, 150, 80, 'CPU1', true);
                drawProcessor(650, 150, 80, 'CPU2', true);
                drawProcessor(150, 350, 80, 'CPU3', true);
                drawProcessor(650, 350, 80, 'CPU4', true);
                drawMemory(400, 250, 120, 80, '共享内存', true);
                
                // 绘制连接
                drawConnection(190, 150, 360, 250, 'bus', true);
                drawConnection(610, 150, 440, 250, 'bus', true);
                drawConnection(190, 350, 360, 290, 'bus', true);
                drawConnection(610, 350, 440, 290, 'bus', true);
                
                // 更新和绘制数据包
                dataPackets.forEach(packet => {
                    packet.update();
                    packet.draw();
                });
            }
            
            animationProgress++;
            
            if (currentAnimation) {
                animationId = requestAnimationFrame(animate);
            }
        }

        // 测试功能
        let score = 0;
        let answered = false;

        function selectAnswer(element, isCorrect) {
            if (answered) return;
            
            answered = true;
            
            if (isCorrect) {
                element.classList.add('correct');
                score += 10;
                document.getElementById('scoreDisplay').innerHTML = '🎉 正确！得分：' + score;
                
                // 显示解释
                setTimeout(() => {
                    document.getElementById('scoreDisplay').innerHTML = 
                        '✅ 正确答案是B：共享内存<br>' +
                        '💡 紧耦合系统通过高速总线连接，使用共享内存进行快速通信！';
                }, 1000);
            } else {
                element.classList.add('wrong');
                document.getElementById('scoreDisplay').innerHTML = '❌ 错误，正确答案是B：共享内存';
            }
            
            // 显示正确答案
            const options = document.querySelectorAll('.quiz-option');
            options[1].classList.add('correct');
        }

        // 初始化
        resetAnimation();
        
        // 响应式处理
        function resizeCanvas() {
            const container = canvas.parentElement;
            const maxWidth = container.clientWidth - 40;
            if (maxWidth < 800) {
                canvas.style.width = maxWidth + 'px';
                canvas.style.height = (maxWidth * 0.625) + 'px';
            }
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
    </script>
</body>
</html>
