<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ANSI/IEEE 1471-2000 软件架构标准学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .game-board {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .concept-area {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .concept-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 25px;
            color: white;
            text-align: center;
            cursor: pointer;
            transform: translateY(0);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s;
            opacity: 0;
        }

        .concept-card:hover::before {
            animation: shine 0.6s ease-in-out;
        }

        .concept-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .concept-card.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            transform: translateY(-5px) scale(1.02);
        }

        .concept-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .concept-card p {
            font-size: 1rem;
            line-height: 1.6;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            cursor: pointer;
        }

        .question-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            animation: fadeIn 1s ease-out 0.6s both;
        }

        .question h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .question p {
            color: #555;
            line-height: 1.8;
            margin-bottom: 20px;
            font-size: 1.1rem;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            border-color: #4facfe;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.3);
        }

        .option.correct {
            background: #4caf50;
            color: white;
            border-color: #4caf50;
        }

        .option.wrong {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        .explanation {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #4facfe;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 1s ease-out;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes shine {
            0% { opacity: 0; transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { opacity: 1; }
            100% { opacity: 0; transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .floating-element {
            position: absolute;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ 软件架构标准探索之旅</h1>
            <p>通过互动游戏学习 ANSI/IEEE 1471-2000 标准</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="game-board">
            <div class="concept-area">
                <div class="concept-card" data-concept="stakeholder">
                    <h3>👥 利益相关人</h3>
                    <p>对系统有关注点的人员或组织</p>
                </div>
                <div class="concept-card" data-concept="concern">
                    <h3>🎯 关注点</h3>
                    <p>利益相关人关心的系统方面</p>
                </div>
                <div class="concept-card" data-concept="viewpoint">
                    <h3>👁️ 视角</h3>
                    <p>观察架构的特定角度和方法</p>
                </div>
                <div class="concept-card" data-concept="view">
                    <h3>🖼️ 视图</h3>
                    <p>从特定视角表述的架构方面</p>
                </div>
                <div class="concept-card" data-concept="model">
                    <h3>📐 模型</h3>
                    <p>架构的具体表示和描述</p>
                </div>
                <div class="concept-card" data-concept="architecture">
                    <h3>🏛️ 架构描述</h3>
                    <p>对所有关注点的完整回答</p>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas"></canvas>
            </div>
        </div>

        <div class="question-section">
            <div class="question">
                <h2>📝 知识测试</h2>
                <p><strong>题目：</strong>ANSI/IEEE 1471-2000是对软件密集型系统的架构进行描述的标准。在该标准中，（ ）这一概念主要用于描述软件架构模型。在此基础上，通常采用（ ）描述某个利益相关人（Stakeholder）所关注架构模型的某一方面。（ ）则是对所有利益相关人关注点的响应和回答。</p>
                
                <div class="options" id="optionsContainer">
                    <div class="option" data-answer="A">A. 上下文</div>
                    <div class="option" data-answer="B">B. 架构风格</div>
                    <div class="option" data-answer="C">C. 组件</div>
                    <div class="option" data-answer="D">D. 视图</div>
                </div>

                <div class="explanation" id="explanation" style="display: none;">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：D（视图）</strong></p>
                    <p>在ANSI/IEEE 1471-2000标准中：</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>视图（View）</strong>：是从特定视角表述架构的某个独立方面，主要用于描述软件架构模型</li>
                        <li><strong>视角（Viewpoint）</strong>：决定了创建视图的语言、符号和模型，用于描述利益相关人关注的架构方面</li>
                        <li><strong>架构描述（Architecture Description）</strong>：是对所有利益相关人关注点的响应和回答</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            currentConcept: null,
            progress: 0,
            answered: false
        };

        // Canvas 设置
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth - 40;
            canvas.height = 400;
        }
        
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 概念数据
        const concepts = {
            stakeholder: {
                title: '利益相关人 (Stakeholder)',
                description: '对系统开发、运营有关注的个人或组织',
                color: '#FF6B6B',
                examples: ['开发者', '用户', '管理者', '维护人员']
            },
            concern: {
                title: '关注点 (Concern)',
                description: '利益相关人关心的系统特定方面',
                color: '#4ECDC4',
                examples: ['性能', '安全性', '可维护性', '可扩展性']
            },
            viewpoint: {
                title: '视角 (Viewpoint)',
                description: '观察和分析架构的特定方法和角度',
                color: '#45B7D1',
                examples: ['逻辑视角', '开发视角', '进程视角', '物理视角']
            },
            view: {
                title: '视图 (View)',
                description: '从特定视角表述的架构某一方面',
                color: '#96CEB4',
                examples: ['类图', '组件图', '部署图', '时序图']
            },
            model: {
                title: '模型 (Model)',
                description: '架构的具体表示，可视化架构元素',
                color: '#FFEAA7',
                examples: ['UML图', '架构图', '流程图', '数据模型']
            },
            architecture: {
                title: '架构描述 (Architecture Description)',
                description: '对所有利益相关人关注点的完整响应',
                color: '#DDA0DD',
                examples: ['完整文档', '多视图集合', '标准描述', '综合说明']
            }
        };

        // 动画元素
        let particles = [];
        let animationId;

        // 粒子类
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = (Math.random() - 0.5) * 4;
                this.color = color;
                this.life = 1.0;
                this.decay = 0.02;
                this.size = Math.random() * 4 + 2;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.size *= 0.98;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 绘制概念关系图
        function drawConceptMap() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = 120;
            
            // 绘制中心圆（架构描述）
            ctx.fillStyle = concepts.architecture.color;
            ctx.beginPath();
            ctx.arc(centerX, centerY, 40, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('架构描述', centerX, centerY - 5);
            ctx.fillText('Architecture', centerX, centerY + 10);

            // 绘制周围的概念
            const conceptKeys = ['stakeholder', 'concern', 'viewpoint', 'view', 'model'];
            conceptKeys.forEach((key, index) => {
                const angle = (index * 2 * Math.PI) / conceptKeys.length - Math.PI / 2;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                
                // 绘制连接线
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(x, y);
                ctx.stroke();
                
                // 绘制概念圆
                ctx.fillStyle = concepts[key].color;
                ctx.beginPath();
                ctx.arc(x, y, 30, 0, Math.PI * 2);
                ctx.fill();
                
                // 绘制文字
                ctx.fillStyle = '#333';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                const lines = concepts[key].title.split(' ');
                ctx.fillText(lines[0], x, y - 5);
                if (lines[1]) {
                    ctx.fillText(lines[1], x, y + 8);
                }
            });

            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
        }

        // 动画循环
        function animate() {
            drawConceptMap();
            animationId = requestAnimationFrame(animate);
        }

        // 概念卡片点击事件
        document.querySelectorAll('.concept-card').forEach(card => {
            card.addEventListener('click', function() {
                const concept = this.dataset.concept;
                
                // 移除其他卡片的active状态
                document.querySelectorAll('.concept-card').forEach(c => c.classList.remove('active'));
                this.classList.add('active');
                
                // 创建粒子效果
                const rect = this.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2 - canvas.getBoundingClientRect().left;
                const centerY = rect.top + rect.height / 2 - canvas.getBoundingClientRect().top;
                
                for (let i = 0; i < 20; i++) {
                    particles.push(new Particle(centerX, centerY, concepts[concept].color));
                }
                
                gameState.currentConcept = concept;
                updateProgress();
            });
        });

        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                if (gameState.answered) return;
                
                const answer = this.dataset.answer;
                const explanation = document.getElementById('explanation');
                
                // 显示正确答案
                document.querySelectorAll('.option').forEach(opt => {
                    if (opt.dataset.answer === 'D') {
                        opt.classList.add('correct');
                    } else if (opt === this && answer !== 'D') {
                        opt.classList.add('wrong');
                    }
                });
                
                // 显示解析
                explanation.style.display = 'block';
                explanation.style.animation = 'fadeInUp 0.5s ease-out';
                
                gameState.answered = true;
                updateProgress();
            });
        });

        // 更新进度
        function updateProgress() {
            let progress = 0;
            if (gameState.currentConcept) progress += 50;
            if (gameState.answered) progress += 50;
            
            document.getElementById('progressFill').style.width = progress + '%';
            gameState.progress = progress;
        }

        // 启动动画
        animate();

        // 添加浮动元素
        function createFloatingElements() {
            const symbols = ['🏗️', '👥', '🎯', '👁️', '🖼️', '📐'];
            symbols.forEach((symbol, index) => {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.textContent = symbol;
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.fontSize = '2rem';
                element.style.animationDelay = index * 0.5 + 's';
                element.style.zIndex = '-1';
                element.style.opacity = '0.1';
                document.body.appendChild(element);
            });
        }

        createFloatingElements();
    </script>
</body>
</html>
