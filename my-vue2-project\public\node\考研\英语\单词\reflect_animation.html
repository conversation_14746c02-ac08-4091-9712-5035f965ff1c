<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Reflect</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
        :root {
            --primary-color: #78909c; /* 蓝灰色，如镜面金属 */
            --secondary-color: #546e7a;
            --accent-color: #ffc107; /* 金色，如反射光芒 */
            --thought-color: #7e57c2; /* 紫色，代表思考 */
            --light-bg: #f5f5f5;
            --panel-bg: #ffffff;
            --text-color: #37474f;
        }
        body { font-family: 'Roboto', 'Noto Sans SC', sans-serif; background-color: #e0e0e0; color: var(--text-color); display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; overflow: hidden; }
        .container { display: flex; flex-direction: row; width: 95%; max-width: 1400px; height: 90vh; max-height: 800px; background-color: var(--panel-bg); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .word-panel { flex: 1; padding: 40px; display: flex; flex-direction: column; justify-content: center; background-color: var(--light-bg); overflow-y: auto; }
        .word-panel h1 { font-size: 3.5em; color: var(--primary-color); }
        .word-panel .pronunciation { font-size: 1.5em; color: var(--secondary-color); margin-bottom: 20px; }
        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--primary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: var(--secondary-color); }
        .breakdown-section { margin-top: 25px; padding: 20px; background-color: #fafafa; border-radius: 10px; }
        .morpheme-btn { padding: 8px 15px; border: 2px solid var(--primary-color); border-radius: 20px; background-color: transparent; color: var(--primary-color); font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; }
        .morpheme-btn:hover, .morpheme-btn.active { background-color: var(--primary-color); color: white; transform: translateY(-2px); }
        .animation-panel { flex: 2; padding: 20px; display: flex; flex-direction: column; justify-content: center; align-items: center; position: relative; background: #eceff1; }
        .activity-title { font-size: 1.8em; color: var(--primary-color); margin-bottom: 15px; text-align: center; }
        .activity-wrapper { display: none; width: 100%; height: calc(100% - 100px); flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        .game-container { width: 100%; height: 100%; position: relative; display: flex; align-items: center; justify-content: center; border-radius: 15px; background: #ffffff; border: 1px solid #cfd8dc; overflow: hidden;}
        .control-button { margin-top: 20px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; }
        
        /* 光线反射 */
        #mirror { width: 10px; height: 150px; background: linear-gradient(45deg, #b0bec5, #f5f5f5, #b0bec5); border: 2px solid var(--secondary-color); position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); }
        .light-beam { height: 4px; background-color: var(--accent-color); position: absolute; transform-origin: left; }
        #incident-beam { width: 200px; top: 50px; left: calc(50% - 200px); }
        #reflected-beam { width: 200px; left: 50%; top: 50px; }
        .slider-container { margin-top: 20px; }

        /* 思想涟漪 */
        #reflection-canvas { background: #37474f; cursor: pointer; }

    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>reflect</h1>
            <p class="pronunciation">[rɪˈflekt]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 反射；反思</p>
                <p><strong>含义 1 (物理):</strong> （指表面）反射（光、热、声等）。</p>
                <p><strong>含义 2 (精神):</strong> 认真或仔细地思考；回顾。</p>
                <div class="example">
                    <p><strong>例句1:</strong> The white sand reflected the sun's heat.</p>
                    <p><strong>翻译1:</strong> 白色的沙子反射了太阳的热量。</p>
                    <p><strong>例句2:</strong> He took some time to reflect on his actions.</p>
                    <p><strong>翻译2:</strong> 他花了一些时间反思自己的行为。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>完整单词活动</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="light-reflection">互动: 光线反射</button>
                    <button class="morpheme-btn" data-activity="thought-reflection">互动: 思想涟漪</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p>点击左侧按钮，探索"反射"与"反思"。</p></div>
            
            <div id="light-reflection" class="activity-wrapper">
                <div class="game-container">
                    <div id="incident-beam" class="light-beam"></div>
                    <div id="reflected-beam" class="light-beam"></div>
                    <div id="mirror"></div>
                </div>
                <div class="slider-container">
                    <label for="mirror-angle">调整镜面角度: </label>
                    <input type="range" id="mirror-angle" min="-45" max="45" value="0">
                </div>
            </div>
            
            <div id="thought-reflection" class="activity-wrapper">
                <div class="game-container"><canvas id="reflection-canvas"></canvas></div>
                <button class="control-button" id="reflect-btn">开始反思</button>
            </div>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                const targetId = btn.dataset.activity;
                document.getElementById(targetId)?.classList.add('active');
                if (targetId === 'light-reflection') setupLightReflection();
                if (targetId === 'thought-reflection') setupThoughtReflection();
            });
        });

        // 光线反射
        function setupLightReflection() {
            const angleSlider = document.getElementById('mirror-angle');
            const mirror = document.getElementById('mirror');
            const incidentBeam = document.getElementById('incident-beam');
            const reflectedBeam = document.getElementById('reflected-beam');
            if (!angleSlider) return;

            function updateReflection() {
                const angle = parseInt(angleSlider.value, 10);
                mirror.style.transform = `translate(-50%, -50%) rotate(${angle}deg)`;
                
                // Simplified physics: angle of reflection = - angle of incidence relative to mirror
                const incidentAngle = 0; // Assume horizontal beam
                const reflectedAngle = 2 * angle - incidentAngle;
                reflectedBeam.style.transform = `rotate(${reflectedAngle}deg)`;
            }

            angleSlider.addEventListener('input', updateReflection);
            updateReflection(); // Initial call
        }

        // 思想涟漪
        function setupThoughtReflection() {
            const canvas = document.getElementById('reflection-canvas');
            const btn = document.getElementById('reflect-btn');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            let animationId;
            let particles = [], centerOrb, pulses = [];
            
            function resizeCanvas() {
                const container = canvas.parentElement;
                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;
            }

            class CenterOrb {
                constructor() { this.x = canvas.width / 2; this.y = canvas.height / 2; this.radius = 15; }
                draw() {
                    ctx.fillStyle = getComputedStyle(document.documentElement).getPropertyValue('--accent-color');
                    ctx.beginPath(); ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2); ctx.fill();
                }
            }

            class Particle { // Memory
                constructor() {
                    this.x = Math.random() * canvas.width; this.y = Math.random() * canvas.height;
                    this.vx = (Math.random() - 0.5) * 0.5; this.vy = (Math.random() - 0.5) * 0.5;
                    this.radius = Math.random() * 4 + 2;
                    this.baseColor = `rgba(126, 87, 194, 0.5)`; // --thought-color
                    this.litColor = `rgba(224, 190, 255, 1)`;
                    this.isLit = false;
                }
                update() {
                    this.x += this.vx; this.y += this.vy;
                    if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
                    if (this.y < 0 || this.y > canvas.height) this.vy *= -1;
                }
                draw() {
                    ctx.fillStyle = this.isLit ? this.litColor : this.baseColor;
                    ctx.beginPath(); ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2); ctx.fill();
                }
            }

            class Pulse {
                constructor(x, y) { this.x = x; this.y = y; this.radius = 0; this.maxRadius = Math.min(canvas.width, canvas.height) / 2; this.speed = 2; }
                update() { this.radius += this.speed; }
                draw() {
                    ctx.strokeStyle = `rgba(255, 193, 7, 0.3)`;
                    ctx.lineWidth = 2;
                    ctx.beginPath(); ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2); ctx.stroke();
                }
            }
            
            function init() {
                resizeCanvas();
                centerOrb = new CenterOrb();
                particles = [];
                for(let i = 0; i < 50; i++) particles.push(new Particle());
                pulses = [];
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                centerOrb.draw();

                pulses.forEach((pulse, p_idx) => {
                    pulse.update();
                    pulse.draw();
                    particles.forEach(particle => {
                        const dist = Math.hypot(pulse.x - particle.x, pulse.y - particle.y);
                        if (Math.abs(dist - pulse.radius) < pulse.speed * 2 && !particle.isLit) {
                             particle.isLit = true;
                             // Draw line
                             ctx.beginPath();
                             ctx.moveTo(centerOrb.x, centerOrb.y);
                             ctx.lineTo(particle.x, particle.y);
                             ctx.strokeStyle = `rgba(255, 255, 255, 0.2)`;
                             ctx.stroke();
                             setTimeout(() => particle.isLit = false, 2000);
                        }
                    });
                    if(pulse.radius > pulse.maxRadius) pulses.splice(p_idx, 1);
                });
                
                particles.forEach(p => { p.update(); p.draw(); });
                animationId = requestAnimationFrame(animate);
            }

            btn.onclick = () => {
                pulses.push(new Pulse(centerOrb.x, centerOrb.y));
            };

            const observer = new MutationObserver(() => {
                if(document.getElementById('thought-reflection').classList.contains('active')) {
                    if (!animationId) { init(); animate(); }
                } else {
                    if (animationId) { cancelAnimationFrame(animationId); animationId = null; }
                }
            });
            observer.observe(document.getElementById('thought-reflection'), { attributes: true, attributeFilter:['class'] });
            
            // Initial setup if active on load
            if (document.getElementById('thought-reflection').classList.contains('active')) {
                if (!animationId) { init(); animate(); }
            }
        }
    });
    </script>
</body>
</html> 