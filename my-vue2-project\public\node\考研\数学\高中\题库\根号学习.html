<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>根号是什么？- 零基础互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-50px);
            animation: fadeInDown 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transform: translateY(-5px);
        }

        .explanation {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #555;
            text-align: center;
            margin: 20px 0;
        }

        .interactive-btn {
            display: block;
            margin: 20px auto;
            padding: 15px 30px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .formula-display {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.5rem;
            border-left: 5px solid #667eea;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 5px 10px;
            border-radius: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🌟 根号是什么？零基础互动学习 🌟</h1>

        <div class="section">
            <h2 class="section-title">📚 什么是根号？</h2>
            <div class="explanation">
                <span class="highlight">根号</span>就像是一个"反向"的乘法！<br>
                如果我们知道一个数乘以自己等于另一个数，<br>
                那么根号就是帮我们找到这个"神秘数字"的工具！
            </div>
            <div class="canvas-container">
                <canvas id="introCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="startIntroAnimation()">🎬 开始动画演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">🔍 根号的符号认识</h2>
            <div class="explanation">
                根号的符号是 <span class="highlight">√</span>，它就像一个小房子的屋顶！<br>
                数字住在这个"屋顶"下面，我们要找出它的"平方根"
            </div>
            <div class="canvas-container">
                <canvas id="symbolCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="drawSymbolAnimation()">✏️ 学习画根号符号</button>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 简单例子理解</h2>
            <div class="formula-display">
                √9 = 3，因为 3 × 3 = 9
            </div>
            <div class="explanation">
                让我们用正方形来理解！<br>
                如果一个正方形的面积是9，那么它的边长就是3！
            </div>
            <div class="canvas-container">
                <canvas id="exampleCanvas" width="600" height="400"></canvas>
            </div>
            <button class="interactive-btn" onclick="showSquareExample()">🟦 看正方形演示</button>
        </div>
    </div>

    <script>
        // 获取画布和上下文
        const introCanvas = document.getElementById('introCanvas');
        const introCtx = introCanvas.getContext('2d');
        const symbolCanvas = document.getElementById('symbolCanvas');
        const symbolCtx = symbolCanvas.getContext('2d');
        const exampleCanvas = document.getElementById('exampleCanvas');
        const exampleCtx = exampleCanvas.getContext('2d');

        // 动画状态
        let animationFrame = 0;
        let isAnimating = false;

        // 开始介绍动画
        function startIntroAnimation() {
            if (isAnimating) return;
            isAnimating = true;
            animationFrame = 0;
            animateIntro();
        }

        function animateIntro() {
            introCtx.clearRect(0, 0, introCanvas.width, introCanvas.height);
            
            // 设置字体和样式
            introCtx.font = 'bold 24px Microsoft YaHei';
            introCtx.textAlign = 'center';
            
            if (animationFrame < 60) {
                // 第一阶段：显示乘法
                introCtx.fillStyle = '#667eea';
                introCtx.fillText('3 × 3 = 9', 300, 100);
                
                // 绘制两个3的动画
                const scale = Math.sin(animationFrame * 0.1) * 0.2 + 1;
                introCtx.save();
                introCtx.translate(200, 150);
                introCtx.scale(scale, scale);
                introCtx.fillStyle = '#ff6b6b';
                introCtx.fillText('3', 0, 0);
                introCtx.restore();
                
                introCtx.save();
                introCtx.translate(400, 150);
                introCtx.scale(scale, scale);
                introCtx.fillStyle = '#ff6b6b';
                introCtx.fillText('3', 0, 0);
                introCtx.restore();
                
            } else if (animationFrame < 120) {
                // 第二阶段：显示结果
                introCtx.fillStyle = '#667eea';
                introCtx.fillText('3 × 3 = 9', 300, 100);
                
                const pulseScale = Math.sin((animationFrame - 60) * 0.2) * 0.3 + 1.2;
                introCtx.save();
                introCtx.translate(300, 200);
                introCtx.scale(pulseScale, pulseScale);
                introCtx.fillStyle = '#4ecdc4';
                introCtx.fillText('9', 0, 0);
                introCtx.restore();
                
            } else {
                // 第三阶段：引入根号概念
                introCtx.fillStyle = '#667eea';
                introCtx.fillText('那么反过来...', 300, 80);
                introCtx.fillStyle = '#764ba2';
                introCtx.fillText('√9 = 3', 300, 150);
                introCtx.fillStyle = '#ff6b6b';
                introCtx.font = '18px Microsoft YaHei';
                introCtx.fillText('根号9等于3！', 300, 220);
            }
            
            animationFrame++;
            if (animationFrame < 180) {
                requestAnimationFrame(animateIntro);
            } else {
                isAnimating = false;
            }
        }

        // 根号符号绘制动画
        function drawSymbolAnimation() {
            symbolCtx.clearRect(0, 0, symbolCanvas.width, symbolCanvas.height);
            
            let step = 0;
            const drawStep = () => {
                symbolCtx.strokeStyle = '#667eea';
                symbolCtx.lineWidth = 4;
                symbolCtx.lineCap = 'round';
                
                if (step === 0) {
                    // 绘制左边的短线
                    symbolCtx.beginPath();
                    symbolCtx.moveTo(200, 180);
                    symbolCtx.lineTo(220, 200);
                    symbolCtx.stroke();
                    
                    symbolCtx.fillStyle = '#333';
                    symbolCtx.font = '16px Microsoft YaHei';
                    symbolCtx.textAlign = 'center';
                    symbolCtx.fillText('第一笔：短斜线', 300, 250);
                    
                } else if (step === 1) {
                    // 重绘第一笔
                    symbolCtx.beginPath();
                    symbolCtx.moveTo(200, 180);
                    symbolCtx.lineTo(220, 200);
                    symbolCtx.stroke();
                    
                    // 绘制中间的长线
                    symbolCtx.beginPath();
                    symbolCtx.moveTo(220, 200);
                    symbolCtx.lineTo(260, 120);
                    symbolCtx.stroke();
                    
                    symbolCtx.fillStyle = '#333';
                    symbolCtx.font = '16px Microsoft YaHei';
                    symbolCtx.textAlign = 'center';
                    symbolCtx.fillText('第二笔：长斜线向上', 300, 250);
                    
                } else if (step === 2) {
                    // 重绘前两笔
                    symbolCtx.beginPath();
                    symbolCtx.moveTo(200, 180);
                    symbolCtx.lineTo(220, 200);
                    symbolCtx.lineTo(260, 120);
                    symbolCtx.stroke();
                    
                    // 绘制顶部横线
                    symbolCtx.beginPath();
                    symbolCtx.moveTo(260, 120);
                    symbolCtx.lineTo(380, 120);
                    symbolCtx.stroke();
                    
                    symbolCtx.fillStyle = '#333';
                    symbolCtx.font = '16px Microsoft YaHei';
                    symbolCtx.textAlign = 'center';
                    symbolCtx.fillText('第三笔：横线屋顶', 300, 250);
                    
                } else {
                    // 完整的根号符号
                    symbolCtx.beginPath();
                    symbolCtx.moveTo(200, 180);
                    symbolCtx.lineTo(220, 200);
                    symbolCtx.lineTo(260, 120);
                    symbolCtx.lineTo(380, 120);
                    symbolCtx.stroke();
                    
                    // 添加数字9
                    symbolCtx.fillStyle = '#ff6b6b';
                    symbolCtx.font = 'bold 36px Microsoft YaHei';
                    symbolCtx.textAlign = 'center';
                    symbolCtx.fillText('9', 320, 170);
                    
                    symbolCtx.fillStyle = '#333';
                    symbolCtx.font = '16px Microsoft YaHei';
                    symbolCtx.fillText('完成！这就是 √9', 300, 250);
                }
                
                step++;
                if (step <= 3) {
                    setTimeout(drawStep, 1500);
                }
            };
            
            drawStep();
        }

        // 正方形例子演示
        function showSquareExample() {
            exampleCtx.clearRect(0, 0, exampleCanvas.width, exampleCanvas.height);
            
            let phase = 0;
            const animate = () => {
                exampleCtx.clearRect(0, 0, exampleCanvas.width, exampleCanvas.height);
                
                if (phase === 0) {
                    // 绘制3×3的网格
                    exampleCtx.strokeStyle = '#667eea';
                    exampleCtx.lineWidth = 2;
                    
                    for (let i = 0; i <= 3; i++) {
                        // 垂直线
                        exampleCtx.beginPath();
                        exampleCtx.moveTo(200 + i * 60, 100);
                        exampleCtx.lineTo(200 + i * 60, 280);
                        exampleCtx.stroke();
                        
                        // 水平线
                        exampleCtx.beginPath();
                        exampleCtx.moveTo(200, 100 + i * 60);
                        exampleCtx.lineTo(380, 100 + i * 60);
                        exampleCtx.stroke();
                    }
                    
                    exampleCtx.fillStyle = '#333';
                    exampleCtx.font = '18px Microsoft YaHei';
                    exampleCtx.textAlign = 'center';
                    exampleCtx.fillText('这是一个3×3的正方形网格', 300, 320);
                    
                } else if (phase === 1) {
                    // 填充小方格
                    exampleCtx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                    for (let i = 0; i < 3; i++) {
                        for (let j = 0; j < 3; j++) {
                            exampleCtx.fillRect(202 + i * 60, 102 + j * 60, 56, 56);
                        }
                    }
                    
                    // 绘制网格线
                    exampleCtx.strokeStyle = '#667eea';
                    exampleCtx.lineWidth = 2;
                    for (let i = 0; i <= 3; i++) {
                        exampleCtx.beginPath();
                        exampleCtx.moveTo(200 + i * 60, 100);
                        exampleCtx.lineTo(200 + i * 60, 280);
                        exampleCtx.stroke();
                        
                        exampleCtx.beginPath();
                        exampleCtx.moveTo(200, 100 + i * 60);
                        exampleCtx.lineTo(380, 100 + i * 60);
                        exampleCtx.stroke();
                    }
                    
                    exampleCtx.fillStyle = '#333';
                    exampleCtx.font = '18px Microsoft YaHei';
                    exampleCtx.textAlign = 'center';
                    exampleCtx.fillText('总共有9个小方格，面积=9', 300, 320);
                    
                } else {
                    // 最终解释
                    exampleCtx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                    for (let i = 0; i < 3; i++) {
                        for (let j = 0; j < 3; j++) {
                            exampleCtx.fillRect(202 + i * 60, 102 + j * 60, 56, 56);
                        }
                    }
                    
                    exampleCtx.strokeStyle = '#667eea';
                    exampleCtx.lineWidth = 2;
                    for (let i = 0; i <= 3; i++) {
                        exampleCtx.beginPath();
                        exampleCtx.moveTo(200 + i * 60, 100);
                        exampleCtx.lineTo(200 + i * 60, 280);
                        exampleCtx.stroke();
                        
                        exampleCtx.beginPath();
                        exampleCtx.moveTo(200, 100 + i * 60);
                        exampleCtx.lineTo(380, 100 + i * 60);
                        exampleCtx.stroke();
                    }
                    
                    // 标注边长
                    exampleCtx.fillStyle = '#ff6b6b';
                    exampleCtx.font = 'bold 20px Microsoft YaHei';
                    exampleCtx.textAlign = 'center';
                    exampleCtx.fillText('3', 170, 195);
                    exampleCtx.fillText('3', 290, 70);
                    
                    // 箭头
                    exampleCtx.strokeStyle = '#ff6b6b';
                    exampleCtx.lineWidth = 3;
                    exampleCtx.beginPath();
                    exampleCtx.moveTo(180, 100);
                    exampleCtx.lineTo(180, 280);
                    exampleCtx.stroke();
                    
                    exampleCtx.beginPath();
                    exampleCtx.moveTo(200, 80);
                    exampleCtx.lineTo(380, 80);
                    exampleCtx.stroke();
                    
                    exampleCtx.fillStyle = '#333';
                    exampleCtx.font = '18px Microsoft YaHei';
                    exampleCtx.fillText('边长是3，所以√9 = 3', 300, 350);
                }
                
                phase++;
                if (phase <= 2) {
                    setTimeout(animate, 2000);
                }
            };
            
            animate();
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            // 绘制初始状态
            introCtx.fillStyle = '#ccc';
            introCtx.font = '20px Microsoft YaHei';
            introCtx.textAlign = 'center';
            introCtx.fillText('点击按钮开始学习！', 300, 150);
            
            symbolCtx.fillStyle = '#ccc';
            symbolCtx.font = '20px Microsoft YaHei';
            symbolCtx.textAlign = 'center';
            symbolCtx.fillText('点击按钮学习画根号！', 300, 150);
            
            exampleCtx.fillStyle = '#ccc';
            exampleCtx.font = '20px Microsoft YaHei';
            exampleCtx.textAlign = 'center';
            exampleCtx.fillText('点击按钮看正方形演示！', 300, 200);
        });
    </script>
</body>
</html>
