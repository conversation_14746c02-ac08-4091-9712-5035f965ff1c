<template>
  <div
    id="app"
    :class="[
      { 'dark-mode': darkMode },
      getResponsiveClass()
    ]"
    :style="containerStyle"
  >
    <div class="main-container">
      <transition name="page" mode="out-in">
        <router-view/>
      </transition>

      <!-- 加载状态提示 -->
      <div v-if="loading" class="global-loading">
        <div class="loading-spinner"></div>
      </div>

      <!-- 错误提示 -->
      <el-alert
        v-if="error"
        :title="error"
        type="error"
        show-icon
        center
        class="global-error"
        @close="clearError"
      ></el-alert>

      <!-- 窗口信息显示（开发模式） -->
      <!-- <div v-if="showWindowInfo" class="window-info">
        <p>设备类型: {{ deviceType }}</p>
        <p>窗口大小: {{ windowInfo.width }} x {{ windowInfo.height }}</p>
        <p>最大化: {{ windowInfo.isMaximized ? '是' : '否' }}</p>
        <p>全屏: {{ windowInfo.isFullScreen ? '是' : '否' }}</p>
      </div> -->
    </div>
  </div>
</template>

<script>
import { mapState, mapActions, mapMutations } from 'vuex'
import windowAdaptive from './mixins/windowAdaptive'

export default {
  name: 'App',
  mixins: [windowAdaptive],

  data() {
    return {
      showWindowInfo: process.env.NODE_ENV === 'development' // 只在开发模式显示窗口信息
    }
  },

  computed: {
    ...mapState(['darkMode', 'loading', 'error'])
  },

  methods: {
    ...mapActions(['initApp']),
    ...mapMutations(['setError']),

    clearError() {
      this.setError(null)
    },

    // 重写窗口自适应回调方法
    onWindowResize(size) {
      console.log('App - Window resized:', size)
      // 可以在这里添加应用级别的窗口大小变化处理逻辑
      this.$nextTick(() => {
        // 触发全局事件，让其他组件知道窗口大小变化
        this.$root.$emit('window-resize', size)
      })
    },

    onWindowMaximize(isMaximized) {
      console.log('App - Window maximized:', isMaximized)
      this.$root.$emit('window-maximize', isMaximized)
    },

    onWindowFullScreen(isFullScreen) {
      console.log('App - Window fullscreen:', isFullScreen)
      this.$root.$emit('window-fullscreen', isFullScreen)
    },

    onMenuAction(action) {
      console.log('App - Menu action:', action)
      // 处理菜单操作
      switch (action) {
        case 'new':
          this.$router.push('/create')
          break
        case 'open':
          // 处理打开文件操作
          break
        default:
          break
      }
    },

    setupIPC() {
      // 检测是否在Electron环境中
      if (window.require) {
        const { ipcRenderer } = window.require('electron')

        // 监听导航到笔记页面的消息
        ipcRenderer.on('navigate-to-notes', () => {
          this.$router.push('/notes')
        })
      }
    }
  },

  created() {
    // 初始化应用
    this.initApp()
    this.setupIPC()
  }
}
</script>

<style>
@import './styles/main.css';

#app {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.page-enter,
.page-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 全局加载状态样式 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 全局错误提示样式 */
.global-error {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  min-width: 300px;
}

/* 窗口信息显示样式（开发模式） */
.window-info {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 10000;
  backdrop-filter: blur(10px);
}

.window-info p {
  margin: 2px 0;
}

/* 响应式调整 */
.device-mobile .global-error {
  min-width: 280px;
  left: 10px;
  right: 10px;
  transform: none;
}

.device-mobile .window-info {
  bottom: 10px;
  right: 10px;
  left: 10px;
  font-size: 11px;
}
</style>
