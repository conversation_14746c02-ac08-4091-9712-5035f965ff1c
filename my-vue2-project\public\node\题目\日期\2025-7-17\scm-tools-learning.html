<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件配置管理工具 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .title {
            color: white;
            font-size: 3.2rem;
            margin-bottom: 15px;
            opacity: 0;
            transform: translateY(-50px);
            animation: slideDown 1.5s ease-out forwards;
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.4rem;
            opacity: 0;
            animation: fadeIn 1.8s ease-out 0.5s forwards;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transform: translateY(50px);
            animation: slideUp 1.5s ease-out 1s forwards;
        }

        .question-area {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 50px;
            border: 3px solid #667eea;
            position: relative;
            overflow: hidden;
        }

        .question-area::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            animation: shimmer 4s infinite;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 2;
            color: #2d3436;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .blank-space {
            display: inline-block;
            min-width: 200px;
            height: 50px;
            border: 3px dashed #667eea;
            border-radius: 12px;
            margin: 0 8px;
            vertical-align: middle;
            background: white;
            transition: all 0.4s ease;
            position: relative;
            cursor: pointer;
        }

        .blank-space.filled {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: 3px solid #764ba2;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
            animation: fillPulse 0.8s ease;
        }

        .tools-showcase {
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: 40px;
            margin: 50px 0;
        }

        .tools-panel {
            background: #f8f9ff;
            border-radius: 20px;
            padding: 35px;
            border: 2px solid #ddd;
        }

        .tools-title {
            font-size: 1.6rem;
            color: #2d3436;
            margin-bottom: 30px;
            text-align: center;
            font-weight: bold;
        }

        .tool-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border: 3px solid transparent;
            transform: translateX(-20px);
            opacity: 0;
            animation: slideInLeft 0.8s ease-out forwards;
        }

        .tool-card:nth-child(2) { animation-delay: 1.5s; }
        .tool-card:nth-child(3) { animation-delay: 1.7s; }
        .tool-card:nth-child(4) { animation-delay: 1.9s; }
        .tool-card:nth-child(5) { animation-delay: 2.1s; }

        .tool-card:hover {
            transform: translateX(10px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .tool-card.correct {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctBounce 0.8s ease;
        }

        .tool-card.wrong {
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease;
        }

        .tool-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .tool-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2d3436;
        }

        .tool-desc {
            font-size: 1rem;
            color: #636e72;
            line-height: 1.5;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        #scmCanvas {
            border-radius: 15px;
            border: 2px solid #eee;
        }

        .options-section {
            margin: 50px 0;
        }

        .options-title {
            font-size: 1.8rem;
            color: #2d3436;
            text-align: center;
            margin-bottom: 35px;
            font-weight: bold;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
        }

        .option-card {
            background: white;
            border-radius: 18px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 3px solid transparent;
            transform: translateY(30px);
            opacity: 0;
            animation: cardFloat 0.8s ease-out forwards;
        }

        .option-card:nth-child(1) { animation-delay: 2.3s; }
        .option-card:nth-child(2) { animation-delay: 2.5s; }
        .option-card:nth-child(3) { animation-delay: 2.7s; }
        .option-card:nth-child(4) { animation-delay: 2.9s; }

        .option-card:hover {
            transform: translateY(-15px) scale(1.03);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .option-card.correct {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 1s ease;
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.8s ease;
        }

        .option-letter {
            display: inline-block;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            line-height: 50px;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .option-text {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 15px;
        }

        .option-desc {
            font-size: 1rem;
            color: #636e72;
            line-height: 1.6;
        }

        .progress-container {
            margin: 30px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(102, 126, 234, 0.2);
            border-radius: 6px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 1s ease;
            border-radius: 6px;
        }

        .reset-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 30px auto;
            display: block;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .reset-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
        }

        @keyframes slideDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        @keyframes slideInLeft {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes cardFloat {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes fillPulse {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes correctBounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-15px); }
            60% { transform: translateY(-8px); }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 8s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-40px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-element" style="top: 10%; left: 8%; font-size: 2.5rem;">⚙️</div>
        <div class="floating-element" style="top: 20%; right: 12%; font-size: 2rem;">📋</div>
        <div class="floating-element" style="bottom: 25%; left: 15%; font-size: 2.8rem;">🔧</div>
        <div class="floating-element" style="bottom: 15%; right: 8%; font-size: 2.2rem;">📊</div>
        <div class="floating-element" style="top: 45%; left: 3%; font-size: 1.8rem;">🔒</div>
        <div class="floating-element" style="top: 65%; right: 5%; font-size: 2.4rem;">📁</div>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">🛠️ 软件配置管理工具</h1>
            <p class="subtitle">探索软件开发中的配置管理核心功能</p>
        </div>

        <div class="main-content">
            <div class="question-area">
                <div class="question-text">
                    【软考达人-回忆版】<span class="blank-space" id="answerBlank"></span>的常见功能包括版本控制、变更管理、配置状态管理、访问控制和安全控制等。
                </div>
                
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar"></div>
                    </div>
                    <p style="color: #636e72; margin-top: 10px;">点击正确的工具类型完成填空</p>
                </div>
            </div>

            <div class="tools-showcase">
                <div class="tools-panel">
                    <h3 class="tools-title">🎯 工具类型选择</h3>
                    
                    <div class="tool-card" data-tool="A" data-correct="false">
                        <span class="tool-icon">🧪</span>
                        <div class="tool-name">软件测试工具</div>
                        <div class="tool-desc">主要用于软件测试活动，如单元测试、集成测试、性能测试等</div>
                    </div>

                    <div class="tool-card" data-tool="B" data-correct="false">
                        <span class="tool-icon">📝</span>
                        <div class="tool-name">版本控制工具</div>
                        <div class="tool-desc">专注于代码版本管理，如Git、SVN等，是配置管理的一个子集</div>
                    </div>

                    <div class="tool-card" data-tool="C" data-correct="false">
                        <span class="tool-icon">🔧</span>
                        <div class="tool-name">软件维护工具</div>
                        <div class="tool-desc">用于软件维护阶段的错误修复、功能增强等活动</div>
                    </div>

                    <div class="tool-card" data-tool="D" data-correct="true">
                        <span class="tool-icon">⚙️</span>
                        <div class="tool-name">软件配置管理工具</div>
                        <div class="tool-desc">综合管理软件配置项的完整生命周期，包含题目中提到的所有功能</div>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="scmCanvas" width="700" height="500"></canvas>
                </div>
            </div>

            <div class="options-section">
                <h3 class="options-title">请选择正确答案：</h3>
                <div class="options-grid">
                    <div class="option-card" data-option="A">
                        <div class="option-letter">A</div>
                        <div class="option-text">软件测试工具</div>
                        <div class="option-desc">专门用于软件质量保证和缺陷发现</div>
                    </div>
                    
                    <div class="option-card" data-option="B">
                        <div class="option-letter">B</div>
                        <div class="option-text">版本控制工具</div>
                        <div class="option-desc">管理代码版本和变更历史</div>
                    </div>
                    
                    <div class="option-card" data-option="C">
                        <div class="option-letter">C</div>
                        <div class="option-text">软件维护工具</div>
                        <div class="option-desc">支持软件维护和演化活动</div>
                    </div>
                    
                    <div class="option-card" data-option="D">
                        <div class="option-letter">D</div>
                        <div class="option-text">软件配置管理工具</div>
                        <div class="option-desc">全面管理软件配置项和变更过程</div>
                    </div>
                </div>
            </div>

            <button class="reset-button" onclick="resetGame()">🔄 重新开始学习</button>
        </div>

        <div class="explanation-panel" id="explanationPanel" style="background: linear-gradient(135deg, #e8f4fd 0%, #f8f9ff 100%); border-radius: 20px; padding: 40px; margin-top: 40px; opacity: 0; transform: translateY(30px); transition: all 0.6s ease;">
            <h2 style="color: #667eea; font-size: 2rem; text-align: center; margin-bottom: 30px; font-weight: bold;">📚 软件配置管理深度解析</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 25px;">
                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #667eea;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">📋 版本控制</h3>
                    <p style="color: #636e72; line-height: 1.7;">管理软件配置项的不同版本，跟踪变更历史，支持分支合并，确保代码的完整性和可追溯性。</p>
                </div>

                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #fd79a8;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">🔄 变更管理</h3>
                    <p style="color: #636e72; line-height: 1.7;">控制和管理软件变更请求，评估变更影响，确保变更的有序进行和质量控制。</p>
                </div>

                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #00b894;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">📊 配置状态管理</h3>
                    <p style="color: #636e72; line-height: 1.7;">记录和报告配置项的当前状态，包括版本信息、变更状态、测试状态等。</p>
                </div>

                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #fdcb6e;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">🔒 访问控制</h3>
                    <p style="color: #636e72; line-height: 1.7;">控制不同用户对配置项的访问权限，确保只有授权人员才能进行相应的操作。</p>
                </div>

                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #e17055;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">🛡️ 安全控制</h3>
                    <p style="color: #636e72; line-height: 1.7;">保护配置项的安全性，防止未授权访问、数据泄露和恶意修改。</p>
                </div>

                <div style="background: white; border-radius: 15px; padding: 25px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1); border-left: 5px solid #a29bfe;">
                    <h3 style="color: #2d3436; font-size: 1.3rem; margin-bottom: 15px;">🎯 核心价值</h3>
                    <p style="color: #636e72; line-height: 1.7;">软件配置管理工具是软件开发过程中的基础设施，确保软件产品的质量、一致性和可维护性。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            selectedTool: null,
            selectedOption: null,
            gameCompleted: false
        };

        // 获取Canvas和上下文
        const canvas = document.getElementById('scmCanvas');
        const ctx = canvas.getContext('2d');

        // 动画变量
        let animationFrame = 0;
        let particles = [];
        let scmComponents = [];

        // 初始化游戏
        function initGame() {
            setupEventListeners();
            initSCMComponents();
            startAnimation();
            showExplanation();
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 工具卡片事件
            const toolCards = document.querySelectorAll('.tool-card');
            toolCards.forEach(card => {
                card.addEventListener('click', handleToolClick);
            });

            // 选项卡片事件
            const optionCards = document.querySelectorAll('.option-card');
            optionCards.forEach(card => {
                card.addEventListener('click', handleOptionClick);
            });
        }

        // 初始化SCM组件
        function initSCMComponents() {
            scmComponents = [
                { name: '版本控制', x: 150, y: 100, color: '#667eea', active: false, icon: '📋' },
                { name: '变更管理', x: 400, y: 100, color: '#fd79a8', active: false, icon: '🔄' },
                { name: '配置状态管理', x: 550, y: 100, color: '#00b894', active: false, icon: '📊' },
                { name: '访问控制', x: 200, y: 300, color: '#fdcb6e', active: false, icon: '🔒' },
                { name: '安全控制', x: 500, y: 300, color: '#e17055', active: false, icon: '🛡️' }
            ];
        }

        // 处理工具选择
        function handleToolClick(event) {
            const card = event.currentTarget;
            const tool = card.dataset.tool;
            const isCorrect = card.dataset.correct === 'true';

            // 重置所有工具卡片状态
            document.querySelectorAll('.tool-card').forEach(c => {
                c.classList.remove('correct', 'wrong');
            });

            if (isCorrect) {
                card.classList.add('correct');
                fillAnswer('软件配置管理工具');
                updateProgress(50);
                gameState.selectedTool = tool;

                // 激活所有SCM组件
                scmComponents.forEach((component, index) => {
                    setTimeout(() => {
                        component.active = true;
                        createComponentParticles(component.x, component.y, component.color);
                    }, index * 300);
                });

            } else {
                card.classList.add('wrong');
                setTimeout(() => {
                    card.classList.remove('wrong');
                }, 800);
            }
        }

        // 处理选项点击
        function handleOptionClick(event) {
            const card = event.currentTarget;
            const option = card.dataset.option;

            // 重置所有选项卡片状态
            document.querySelectorAll('.option-card').forEach(c => {
                c.classList.remove('correct', 'wrong');
            });

            if (option === 'D') {
                card.classList.add('correct');
                updateProgress(100);
                gameState.selectedOption = option;
                gameState.gameCompleted = true;

                setTimeout(() => {
                    showCompletionMessage();
                }, 1000);

            } else {
                card.classList.add('wrong');
                setTimeout(() => {
                    card.classList.remove('wrong');
                }, 800);
            }
        }

        // 填充答案
        function fillAnswer(text) {
            const blank = document.getElementById('answerBlank');
            blank.textContent = text;
            blank.classList.add('filled');
        }

        // 更新进度条
        function updateProgress(percentage) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
        }

        // 创建组件粒子效果
        function createComponentParticles(x, y, color) {
            for (let i = 0; i < 15; i++) {
                particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 8,
                    vy: (Math.random() - 0.5) * 8,
                    life: 80,
                    color: color,
                    size: Math.random() * 4 + 2
                });
            }
        }

        // 开始动画循环
        function startAnimation() {
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制背景
                drawBackground();

                // 绘制SCM架构图
                drawSCMArchitecture();

                // 绘制粒子效果
                drawParticles();

                animationFrame++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 绘制背景
        function drawBackground() {
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9ff');
            gradient.addColorStop(1, '#e8f4fd');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制SCM架构图
        function drawSCMArchitecture() {
            // 绘制标题
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 22px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('软件配置管理工具架构', canvas.width / 2, 40);

            // 绘制中心核心
            const centerX = canvas.width / 2;
            const centerY = 200;

            ctx.beginPath();
            ctx.arc(centerX, centerY, 50, 0, Math.PI * 2);
            ctx.fillStyle = '#667eea';
            ctx.fill();
            ctx.strokeStyle = '#5a6fd8';
            ctx.lineWidth = 3;
            ctx.stroke();

            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('SCM', centerX, centerY - 5);
            ctx.fillText('核心', centerX, centerY + 15);

            // 绘制组件连接线和组件
            scmComponents.forEach((component, index) => {
                drawSCMComponent(component, centerX, centerY);
            });

            // 绘制说明文字
            if (gameState.selectedTool) {
                ctx.fillStyle = '#636e72';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('软件配置管理工具集成了以上所有功能模块', centerX, canvas.height - 30);
            }
        }

        // 绘制SCM组件
        function drawSCMComponent(component, centerX, centerY) {
            const isActive = component.active;

            // 绘制连接线
            if (isActive) {
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(component.x, component.y);
                ctx.strokeStyle = component.color;
                ctx.lineWidth = 3;
                ctx.stroke();
            }

            // 绘制组件圆圈
            ctx.beginPath();
            ctx.arc(component.x, component.y, 35, 0, Math.PI * 2);

            if (isActive) {
                const gradient = ctx.createRadialGradient(component.x, component.y, 0, component.x, component.y, 35);
                gradient.addColorStop(0, component.color);
                gradient.addColorStop(1, component.color + '80');
                ctx.fillStyle = gradient;

                // 添加脉动效果
                const pulse = Math.sin(animationFrame * 0.1) * 5;
                ctx.arc(component.x, component.y, 35 + pulse, 0, Math.PI * 2);
            } else {
                ctx.fillStyle = '#f1f2f6';
            }

            ctx.fill();
            ctx.strokeStyle = isActive ? component.color : '#ddd';
            ctx.lineWidth = 2;
            ctx.stroke();

            // 绘制图标
            ctx.fillStyle = isActive ? 'white' : '#636e72';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(component.icon, component.x, component.y + 5);

            // 绘制组件名称
            ctx.fillStyle = isActive ? component.color : '#636e72';
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText(component.name, component.x, component.y + 55);
        }

        // 绘制粒子效果
        function drawParticles() {
            particles = particles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;
                particle.vx *= 0.98;
                particle.vy *= 0.98;

                ctx.globalAlpha = particle.life / 80;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;

                return particle.life > 0;
            });
        }

        // 显示完成消息
        function showCompletionMessage() {
            alert('🎉 恭喜！您已经掌握了软件配置管理工具的概念！\n\n✅ 正确答案：D - 软件配置管理工具\n\n软件配置管理工具是一个综合性的工具集，包含版本控制、变更管理、配置状态管理、访问控制和安全控制等多个功能模块，是软件开发过程中不可缺少的基础设施。');
        }

        // 显示详细解释
        function showExplanation() {
            const explanationPanel = document.getElementById('explanationPanel');
            setTimeout(() => {
                explanationPanel.style.opacity = '1';
                explanationPanel.style.transform = 'translateY(0)';
            }, 3500);
        }

        // 重置游戏
        function resetGame() {
            gameState = {
                selectedTool: null,
                selectedOption: null,
                gameCompleted: false
            };

            // 重置答案空白
            const blank = document.getElementById('answerBlank');
            blank.textContent = '';
            blank.classList.remove('filled');

            // 重置工具卡片
            document.querySelectorAll('.tool-card').forEach(card => {
                card.classList.remove('correct', 'wrong');
            });

            // 重置选项卡片
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('correct', 'wrong');
            });

            // 重置进度条
            updateProgress(0);

            // 重置SCM组件
            scmComponents.forEach(component => {
                component.active = false;
            });

            // 清除粒子
            particles = [];
        }

        // 初始化游戏
        window.addEventListener('load', initGame);
    </script>
</body>
</html>
