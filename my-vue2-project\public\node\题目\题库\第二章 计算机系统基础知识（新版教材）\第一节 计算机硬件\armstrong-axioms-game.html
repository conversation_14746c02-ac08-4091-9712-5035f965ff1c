<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Armstrong公理系统 - 分解规则学习游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .game-board {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .question-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            animation: wrongShake 0.6s ease-out;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #gameCanvas {
            border: 2px solid #667eea;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .axiom-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .play-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .play-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #667eea;
            border-radius: 50%;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Armstrong公理系统学习游戏</h1>
            <p>通过动画和交互理解分解规则</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <h2 class="question-title">📚 软考真题挑战</h2>
                <div class="question-content">
                    <p><strong>题目：</strong>给定关系模式R(U，F)，其中U为属性集，F是U上的一组函数依赖，那么函数依赖的公理系统(Armstrong 公理系统)中的分解规则是指（ ）为F所蕴涵</p>
                </div>

                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 若X→Y，Y→Z，则X→Y
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 若Y⊆X⊆U，则X→Y
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 若X→Y，Z⊆Y，则X→Z
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 若X→Y，Y→Z，则X→YZ
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="gameCanvas" width="800" height="400"></canvas>
                    <br>
                    <button class="play-button" onclick="startAnimation()">🎬 播放分解规则动画</button>
                    <button class="play-button" onclick="startGame()">🎮 开始互动游戏</button>
                </div>
            </div>

            <div class="explanation" id="explanation">
                <h3>🧠 Armstrong公理系统详解</h3>
                <div class="axiom-card">
                    <h4>基本公理（前3条）：</h4>
                    <p><strong>A1 自反律：</strong> 若Y⊆X⊆U，则X→Y</p>
                    <p><strong>A2 增广律：</strong> 若X→Y，且Z⊆U，则XZ→YZ</p>
                    <p><strong>A3 传递律：</strong> 若X→Y，Y→Z，则X→Z</p>
                </div>
                <div class="axiom-card">
                    <h4>推导规则（后3条）：</h4>
                    <p><strong>合并规则：</strong> 若X→Y，X→Z，则X→YZ</p>
                    <p><strong>伪传递规则：</strong> 若X→Y，WY→Z，则XW→Z</p>
                    <p><strong>🎯 分解规则：</strong> 若X→Y，Z⊆Y，则X→Z</p>
                </div>
                <div class="axiom-card" style="border-left-color: #28a745;">
                    <h4>💡 分解规则解释：</h4>
                    <p>如果X能决定Y，而Z是Y的子集，那么X也能决定Z。这就像一个老师能教整个班级，那么他也能教班级中的任何一个学生。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let gameState = 'waiting';
        let particles = [];

        // 初始化
        function init() {
            setupEventListeners();
            drawInitialState();
        }

        function setupEventListeners() {
            document.querySelectorAll('.option').forEach(option => {
                option.addEventListener('click', handleAnswer);
            });
        }

        function handleAnswer(e) {
            const selectedAnswer = e.target.dataset.answer;
            const correctAnswer = 'C';
            
            document.querySelectorAll('.option').forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt.dataset.answer === correctAnswer) {
                    opt.classList.add('correct');
                } else if (opt.dataset.answer === selectedAnswer && selectedAnswer !== correctAnswer) {
                    opt.classList.add('wrong');
                }
            });

            setTimeout(() => {
                document.getElementById('explanation').classList.add('show');
                createCelebrationParticles();
            }, 1000);
        }

        function drawInitialState() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('Armstrong公理系统 - 分解规则演示', canvas.width/2, 40);

            // 绘制提示
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.fillText('点击按钮开始学习动画', canvas.width/2, canvas.height/2);
        }

        function startAnimation() {
            gameState = 'animation';
            animateDecompositionRule();
        }

        function animateDecompositionRule() {
            let step = 0;
            const maxSteps = 300;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#f8f9fa');
                gradient.addColorStop(1, '#e9ecef');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('分解规则：若 X→Y，Z⊆Y，则 X→Z', canvas.width/2, 40);

                // 动画进度
                const progress = step / maxSteps;

                if (step < 100) {
                    // 第一阶段：显示 X→Y
                    drawFunctionDependency(150, 120, 'X', 'Y', progress, '#667eea');

                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText('步骤1: 已知 X 函数决定 Y', 50, 200);

                } else if (step < 200) {
                    // 第二阶段：显示 Z⊆Y
                    drawFunctionDependency(150, 120, 'X', 'Y', 1, '#667eea');

                    const subsetProgress = (step - 100) / 100;
                    drawSubsetRelation(450, 120, 'Z', 'Y', subsetProgress, '#28a745');

                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText('步骤1: 已知 X 函数决定 Y', 50, 200);
                    ctx.fillText('步骤2: Z 是 Y 的子集', 50, 230);

                } else {
                    // 第三阶段：推导出 X→Z
                    drawFunctionDependency(150, 120, 'X', 'Y', 1, '#667eea');
                    drawSubsetRelation(450, 120, 'Z', 'Y', 1, '#28a745');

                    const conclusionProgress = (step - 200) / 100;
                    drawFunctionDependency(300, 280, 'X', 'Z', conclusionProgress, '#dc3545');

                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText('步骤1: 已知 X 函数决定 Y', 50, 200);
                    ctx.fillText('步骤2: Z 是 Y 的子集', 50, 230);
                    ctx.fillText('结论: 因此 X 函数决定 Z', 50, 260);

                    // 绘制连接线动画
                    if (conclusionProgress > 0.5) {
                        drawConnectionLines(conclusionProgress);
                    }
                }

                step++;
                if (step <= maxSteps) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    // 动画结束，显示完整解释
                    showFinalExplanation();
                }
            }

            animate();
        }

        function drawFunctionDependency(x, y, from, to, progress, color) {
            // 绘制起始节点
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, 2 * Math.PI);
            ctx.fill();

            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(from, x, y + 6);

            // 绘制箭头（动画）
            if (progress > 0) {
                const arrowLength = 120 * progress;
                const endX = x + arrowLength;

                ctx.strokeStyle = color;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(x + 30, y);
                ctx.lineTo(endX, y);
                ctx.stroke();

                // 箭头头部
                if (progress > 0.8) {
                    ctx.beginPath();
                    ctx.moveTo(endX, y);
                    ctx.lineTo(endX - 10, y - 5);
                    ctx.lineTo(endX - 10, y + 5);
                    ctx.closePath();
                    ctx.fillStyle = color;
                    ctx.fill();
                }
            }

            // 绘制目标节点
            if (progress > 0.6) {
                const targetX = x + 120;
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(targetX, y, 30, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(to, targetX, y + 6);
            }
        }

        function drawSubsetRelation(x, y, subset, superset, progress, color) {
            // 绘制大集合
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(x, y, 50, 0, 2 * Math.PI);
            ctx.stroke();

            ctx.fillStyle = color;
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(superset, x, y - 60);

            // 绘制小集合（动画）
            if (progress > 0) {
                const radius = 25 * progress;
                ctx.fillStyle = color;
                ctx.globalAlpha = 0.3;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, 2 * Math.PI);
                ctx.fill();
                ctx.globalAlpha = 1;

                ctx.strokeStyle = color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, 2 * Math.PI);
                ctx.stroke();

                if (progress > 0.5) {
                    ctx.fillStyle = color;
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(subset, x, y + 4);
                }
            }
        }

        function drawConnectionLines(progress) {
            ctx.strokeStyle = '#dc3545';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);

            // 从 X→Y 到 X→Z 的连接线
            const alpha = progress * 2 - 1;
            ctx.globalAlpha = Math.max(0, alpha);

            ctx.beginPath();
            ctx.moveTo(200, 150);
            ctx.lineTo(330, 250);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(500, 150);
            ctx.lineTo(370, 250);
            ctx.stroke();

            ctx.globalAlpha = 1;
            ctx.setLineDash([]);
        }

        function showFinalExplanation() {
            ctx.fillStyle = 'rgba(40, 167, 69, 0.1)';
            ctx.fillRect(50, 320, canvas.width - 100, 60);

            ctx.fillStyle = '#28a745';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎉 分解规则演示完成！', canvas.width/2, 340);
            ctx.fillText('X能决定Y，Z是Y的子集，所以X也能决定Z', canvas.width/2, 365);
        }

        function startGame() {
            gameState = 'game';
            initializeGame();
        }

        function initializeGame() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制游戏背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#e3f2fd');
            gradient.addColorStop(1, '#bbdefb');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制游戏标题
            ctx.fillStyle = '#1565c0';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 分解规则互动游戏', canvas.width/2, 40);

            // 绘制游戏说明
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('拖拽属性来验证分解规则！', canvas.width/2, 70);

            // 绘制游戏元素
            drawGameElements();

            // 添加鼠标事件监听
            canvas.addEventListener('mousedown', handleMouseDown);
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('mouseup', handleMouseUp);
        }

        let draggedElement = null;
        let mousePos = { x: 0, y: 0 };
        let gameElements = {
            X: { x: 100, y: 150, dragging: false },
            Y: { x: 300, y: 150, dragging: false },
            Z: { x: 500, y: 150, dragging: false },
            arrow1: { from: 'X', to: 'Y', active: true },
            arrow2: { from: 'X', to: 'Z', active: false }
        };

        function drawGameElements() {
            // 绘制属性节点
            Object.keys(gameElements).forEach(key => {
                if (key.startsWith('arrow')) return;

                const element = gameElements[key];
                const isHovered = isPointInCircle(mousePos.x, mousePos.y, element.x, element.y, 30);

                // 节点阴影
                ctx.shadowColor = 'rgba(0,0,0,0.2)';
                ctx.shadowBlur = 10;
                ctx.shadowOffsetX = 2;
                ctx.shadowOffsetY = 2;

                // 绘制节点
                ctx.fillStyle = element.dragging ? '#ff6b6b' : (isHovered ? '#4ecdc4' : '#667eea');
                ctx.beginPath();
                ctx.arc(element.x, element.y, 30, 0, 2 * Math.PI);
                ctx.fill();

                // 重置阴影
                ctx.shadowColor = 'transparent';
                ctx.shadowBlur = 0;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 0;

                // 绘制文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(key, element.x, element.y + 7);

                // 绘制标签
                ctx.fillStyle = '#333';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(getElementDescription(key), element.x, element.y + 60);
            });

            // 绘制箭头
            if (gameElements.arrow1.active) {
                drawArrow(gameElements.X.x, gameElements.X.y, gameElements.Y.x, gameElements.Y.y, '#28a745');

                ctx.fillStyle = '#28a745';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('X → Y', 200, 130);
            }

            if (gameElements.arrow2.active) {
                drawArrow(gameElements.X.x, gameElements.X.y, gameElements.Z.x, gameElements.Z.y, '#dc3545');

                ctx.fillStyle = '#dc3545';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('X → Z', 300, 250);
            }

            // 绘制集合关系
            drawSetRelation();

            // 绘制游戏提示
            drawGameHints();
        }

        function getElementDescription(key) {
            const descriptions = {
                'X': '属性集X',
                'Y': '属性集Y',
                'Z': '属性集Z'
            };
            return descriptions[key] || '';
        }

        function drawArrow(fromX, fromY, toX, toY, color) {
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const startX = fromX + 30 * Math.cos(angle);
            const startY = fromY + 30 * Math.sin(angle);
            const endX = toX - 30 * Math.cos(angle);
            const endY = toY - 30 * Math.sin(angle);

            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // 箭头头部
            const headLength = 15;
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX - headLength * Math.cos(angle - Math.PI / 6),
                      endY - headLength * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(endX - headLength * Math.cos(angle + Math.PI / 6),
                      endY - headLength * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();
        }

        function drawSetRelation() {
            // 绘制Y集合
            ctx.strokeStyle = '#28a745';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.arc(gameElements.Y.x, gameElements.Y.y + 100, 60, 0, 2 * Math.PI);
            ctx.stroke();

            // 检查Z是否在Y内
            const distance = Math.sqrt(
                Math.pow(gameElements.Z.x - gameElements.Y.x, 2) +
                Math.pow(gameElements.Z.y + 100 - gameElements.Y.y - 100, 2)
            );

            if (distance < 60) {
                // Z在Y内，绘制Z集合
                ctx.strokeStyle = '#dc3545';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(gameElements.Z.x, gameElements.Z.y + 100, 30, 0, 2 * Math.PI);
                ctx.stroke();

                // 激活X→Z箭头
                gameElements.arrow2.active = true;

                // 显示成功消息
                ctx.fillStyle = '#28a745';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('✅ 太棒了！Z⊆Y，所以X→Z成立！', canvas.width/2, 350);
            } else {
                gameElements.arrow2.active = false;

                // 显示提示消息
                ctx.fillStyle = '#ff6b6b';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('💡 试着把Z拖到Y的集合范围内', canvas.width/2, 350);
            }

            ctx.setLineDash([]);
        }

        function drawGameHints() {
            ctx.fillStyle = '#666';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'left';
            ctx.fillText('游戏规则：', 50, 300);
            ctx.fillText('1. 已知：X → Y（X函数决定Y）', 70, 320);
            ctx.fillText('2. 拖拽Z到Y的集合内，验证Z⊆Y', 70, 340);
            ctx.fillText('3. 观察X → Z是否成立', 70, 360);
        }

        function handleMouseDown(e) {
            const rect = canvas.getBoundingClientRect();
            mousePos.x = e.clientX - rect.left;
            mousePos.y = e.clientY - rect.top;

            // 检查是否点击了某个元素
            Object.keys(gameElements).forEach(key => {
                if (key.startsWith('arrow')) return;

                const element = gameElements[key];
                if (isPointInCircle(mousePos.x, mousePos.y, element.x, element.y, 30)) {
                    draggedElement = key;
                    element.dragging = true;
                    canvas.style.cursor = 'grabbing';
                }
            });
        }

        function handleMouseMove(e) {
            const rect = canvas.getBoundingClientRect();
            mousePos.x = e.clientX - rect.left;
            mousePos.y = e.clientY - rect.top;

            if (draggedElement) {
                gameElements[draggedElement].x = mousePos.x;
                gameElements[draggedElement].y = mousePos.y;

                // 重绘游戏
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#e3f2fd');
                gradient.addColorStop(1, '#bbdefb');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#1565c0';
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('🎮 分解规则互动游戏', canvas.width/2, 40);

                drawGameElements();
            } else {
                // 检查鼠标悬停
                let isHovering = false;
                Object.keys(gameElements).forEach(key => {
                    if (key.startsWith('arrow')) return;

                    const element = gameElements[key];
                    if (isPointInCircle(mousePos.x, mousePos.y, element.x, element.y, 30)) {
                        isHovering = true;
                    }
                });

                canvas.style.cursor = isHovering ? 'grab' : 'default';

                if (gameState === 'game') {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);

                    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                    gradient.addColorStop(0, '#e3f2fd');
                    gradient.addColorStop(1, '#bbdefb');
                    ctx.fillStyle = gradient;
                    ctx.fillRect(0, 0, canvas.width, canvas.height);

                    ctx.fillStyle = '#1565c0';
                    ctx.font = 'bold 24px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('🎮 分解规则互动游戏', canvas.width/2, 40);

                    drawGameElements();
                }
            }
        }

        function handleMouseUp(e) {
            if (draggedElement) {
                gameElements[draggedElement].dragging = false;
                draggedElement = null;
                canvas.style.cursor = 'default';

                // 检查是否完成了正确的操作
                checkGameCompletion();
            }
        }

        function isPointInCircle(px, py, cx, cy, radius) {
            const distance = Math.sqrt((px - cx) * (px - cx) + (py - cy) * (py - cy));
            return distance <= radius;
        }

        function checkGameCompletion() {
            const distance = Math.sqrt(
                Math.pow(gameElements.Z.x - gameElements.Y.x, 2) +
                Math.pow(gameElements.Z.y + 100 - gameElements.Y.y - 100, 2)
            );

            if (distance < 60 && !gameElements.arrow2.active) {
                // 第一次完成，创建庆祝效果
                createCelebrationParticles();
                setTimeout(() => {
                    alert('🎉 恭喜！你成功理解了分解规则！\n\n当Z是Y的子集时，如果X能决定Y，那么X也能决定Z。');
                }, 500);
            }
        }

        function createCelebrationParticles() {
            particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push({
                    x: canvas.width / 2,
                    y: canvas.height / 2,
                    vx: (Math.random() - 0.5) * 10,
                    vy: (Math.random() - 0.5) * 10,
                    life: 1,
                    decay: Math.random() * 0.02 + 0.01,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`
                });
            }

            animateParticles();
        }

        function animateParticles() {
            if (particles.length === 0) return;

            // 清除粒子区域
            ctx.save();
            ctx.globalCompositeOperation = 'destination-out';
            ctx.fillStyle = 'rgba(255,255,255,0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.restore();

            // 绘制粒子
            particles.forEach((particle, index) => {
                ctx.save();
                ctx.globalAlpha = particle.life;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, 3, 0, 2 * Math.PI);
                ctx.fill();
                ctx.restore();

                // 更新粒子
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life -= particle.decay;

                // 移除死亡的粒子
                if (particle.life <= 0) {
                    particles.splice(index, 1);
                }
            });

            if (particles.length > 0) {
                requestAnimationFrame(animateParticles);
            }
        }

        // 初始化页面
        init();
    </script>
</body>
</html>
