<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FTP协议双端口学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .question-box h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .blank {
            background: rgba(255, 255, 255, 0.3);
            padding: 5px 15px;
            border-radius: 8px;
            border: 2px dashed rgba(255, 255, 255, 0.6);
            display: inline-block;
            min-width: 80px;
            text-align: center;
            font-weight: bold;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-align: center;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d4aa;
            background: rgba(0, 212, 170, 0.3);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.3);
            animation: shake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        canvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            line-height: 1.6;
        }

        .port-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .port-card {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .port-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(108, 92, 231, 0.3);
        }

        .port-card h3 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .port-card.control {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .port-card.data {
            background: linear-gradient(135deg, #00cec9, #00b894);
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 3px 8px;
            border-radius: 5px;
            color: #2d3436;
            font-weight: bold;
        }

        .step {
            background: rgba(116, 185, 255, 0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateX(5px);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #01a3a4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .legend {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 FTP协议双端口学习</h1>
            <p>探索文件传输协议的端口奥秘</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2>📝 考试题目</h2>
                <div class="question-text">
                    <p><strong>默认情况下，FTP服务器的控制端口为（<span class="blank">21</span>），上传文件时的端口为（<span class="blank" id="answerBlank">?</span>）。</strong></p>
                </div>
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 大于1024的端口
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 20
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 80
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 21
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 什么是FTP？</h2>
            <div class="explanation">
                <p><span class="highlight">FTP</span>（File Transfer Protocol）是<strong>文件传输协议</strong>，就像网络世界的"快递服务"，专门负责在客户端和服务器之间传输文件。</p>
                <p>🔑 <strong>关键特点：</strong>FTP使用<span class="highlight">两个端口</span>来工作，就像快递服务需要"客服电话"和"运输车队"一样！</p>
            </div>
            
            <div class="port-info">
                <div class="port-card control" onclick="highlightPort('control')">
                    <h3>端口 21</h3>
                    <p><strong>控制端口</strong></p>
                    <p>📞 负责发送命令</p>
                    <p>如：登录、切换目录</p>
                </div>
                <div class="port-card data" onclick="highlightPort('data')">
                    <h3>端口 20</h3>
                    <p><strong>数据端口</strong></p>
                    <p>📦 负责传输文件</p>
                    <p>如：上传、下载文件</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 FTP工作原理动画演示</h2>
            <div class="canvas-container">
                <canvas id="ftpCanvas"></canvas>
                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #fd79a8;"></div>
                        <span>控制连接(21端口)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #00cec9;"></div>
                        <span>数据连接(20端口)</span>
                    </div>
                </div>
            </div>

            <div class="controls">
                <button class="btn" onclick="startFTPDemo()">🎬 开始FTP演示</button>
                <button class="btn" onclick="showControlDemo()">📞 控制端口演示</button>
                <button class="btn" onclick="showDataDemo()">📦 数据端口演示</button>
                <button class="btn" onclick="resetCanvas()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2>🔍 FTP双端口详解</h2>

            <div class="step">
                <h3>1️⃣ 控制连接（端口21）</h3>
                <p><strong>作用：</strong>建立连接、发送命令、接收响应</p>
                <p><strong>比喻：</strong>就像打电话给快递公司的客服，告诉他们你要寄什么、寄到哪里</p>
                <p><strong>常见命令：</strong>USER（用户名）、PASS（密码）、LIST（列出文件）、CWD（切换目录）</p>
            </div>

            <div class="step">
                <h3>2️⃣ 数据连接（端口20）</h3>
                <p><strong>作用：</strong>传输文件数据、目录列表</p>
                <p><strong>比喻：</strong>就像快递车实际运送包裹，负责把文件从一个地方搬到另一个地方</p>
                <p><strong>传输内容：</strong>文件上传、文件下载、目录信息</p>
            </div>

            <div class="step">
                <h3>3️⃣ 为什么需要两个端口？</h3>
                <p><strong>分工明确：</strong>控制和数据分离，提高效率和安全性</p>
                <p><strong>并行处理：</strong>可以同时发送命令和传输文件</p>
                <p><strong>错误处理：</strong>即使数据传输出错，控制连接仍然可用</p>
            </div>
        </div>

        <div class="section">
            <h2>🤔 解题思路分析</h2>
            <div class="explanation">
                <h3>让我们分析每个选项：</h3>
            </div>

            <div class="step">
                <h3>选项A：大于1024的端口 ❌</h3>
                <p><strong>错误原因：</strong></p>
                <ul>
                    <li>大于1024的端口通常是<span class="highlight">动态端口</span>或<span class="highlight">临时端口</span></li>
                    <li>FTP使用的是<span class="highlight">标准端口</span>，不是动态分配的</li>
                    <li>这些端口由系统随机分配，不固定</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项B：20 ✅</h3>
                <p><strong>正确答案！</strong></p>
                <ul>
                    <li>端口20是FTP的<span class="highlight">标准数据端口</span></li>
                    <li>专门负责<span class="highlight">文件传输</span>（上传/下载）</li>
                    <li>这是RFC标准规定的固定端口</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项C：80 ❌</h3>
                <p><strong>错误原因：</strong></p>
                <ul>
                    <li>端口80是<span class="highlight">HTTP协议</span>的默认端口</li>
                    <li>用于网页浏览，不是文件传输</li>
                    <li>完全不同的协议和用途</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项D：21 ❌</h3>
                <p><strong>错误原因：</strong></p>
                <ul>
                    <li>端口21是FTP的<span class="highlight">控制端口</span>，不是数据端口</li>
                    <li>负责发送命令，不负责传输文件</li>
                    <li>题目问的是"上传文件时的端口"</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🧠 记忆技巧</h2>
            <div class="explanation">
                <h3>🎯 记忆口诀</h3>
                <p style="font-size: 1.3rem; text-align: center; font-weight: bold; margin: 20px 0;">
                    "FTP双端口，21控制20数据，<br>
                    命令用21，文件走20道"
                </p>

                <h3>🔢 数字联想法</h3>
                <p><strong>端口20：</strong>想象成"20岁"，年轻力壮，负责<span class="highlight">搬运重物</span>（传输文件）</p>
                <p><strong>端口21：</strong>想象成"21岁"，更成熟，负责<span class="highlight">指挥调度</span>（发送命令）</p>

                <h3>🏢 生活比喻</h3>
                <p><strong>快递公司模型：</strong></p>
                <ul>
                    <li>📞 <strong>客服热线（端口21）：</strong>接收订单、确认地址、处理问题</li>
                    <li>🚚 <strong>运输车队（端口20）：</strong>实际运送包裹、装卸货物</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎉 学习总结</h2>
            <div class="explanation">
                <h3>📚 核心知识点</h3>
                <ul>
                    <li><span class="highlight">FTP协议</span>：文件传输协议，使用双端口机制</li>
                    <li><span class="highlight">端口21</span>：控制端口，负责命令传输</li>
                    <li><span class="highlight">端口20</span>：数据端口，负责文件传输</li>
                    <li><span class="highlight">双端口优势</span>：分工明确，提高效率和稳定性</li>
                </ul>

                <h3>⚡ 考试技巧</h3>
                <ul>
                    <li>看到"FTP数据传输"、"上传文件"，答案是<span class="highlight">端口20</span></li>
                    <li>看到"FTP控制"、"发送命令"，答案是<span class="highlight">端口21</span></li>
                    <li>记住：<span class="highlight">20数据，21控制</span></li>
                </ul>
            </div>

            <div class="controls">
                <button class="btn" onclick="reviewQuestion()">🔄 重新答题</button>
                <button class="btn" onclick="showSummary()">📋 显示总结</button>
            </div>
        </div>
    </div>

    <script>
        // Canvas相关变量
        const canvas = document.getElementById('ftpCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;
        let currentDemo = 'none';

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 绘制设备
        function drawDevice(x, y, type, label, color = '#3498db') {
            ctx.save();

            // 设备主体
            ctx.fillStyle = color;
            ctx.fillRect(x - 40, y - 25, 80, 50);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 40, y - 25, 80, 50);

            // 设备图标
            if (type === 'server') {
                // 服务器图标
                ctx.fillStyle = '#fff';
                for (let i = 0; i < 3; i++) {
                    ctx.fillRect(x - 35, y - 20 + i * 12, 70, 8);
                }
            } else {
                // 客户端图标
                ctx.fillStyle = '#fff';
                ctx.fillRect(x - 25, y - 15, 50, 25);
                ctx.fillRect(x - 20, y + 10, 40, 10);
            }

            // 标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 60);

            ctx.restore();
        }

        // 绘制端口
        function drawPort(x, y, port, type, active = false) {
            ctx.save();

            const color = type === 'control' ? '#fd79a8' : '#00cec9';
            ctx.fillStyle = active ? color : '#bdc3c7';
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;

            // 端口圆圈
            ctx.beginPath();
            ctx.arc(x, y, 15, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();

            // 端口号
            ctx.fillStyle = active ? '#fff' : '#2c3e50';
            ctx.font = 'bold 12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(port, x, y + 4);

            // 端口标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '10px Microsoft YaHei';
            ctx.fillText(type === 'control' ? '控制' : '数据', x, y + 35);

            ctx.restore();
        }

        // 绘制连接线
        function drawConnection(fromX, fromY, toX, toY, type, active = false, progress = 1) {
            ctx.save();

            const color = type === 'control' ? '#fd79a8' : '#00cec9';
            ctx.strokeStyle = active ? color : '#bdc3c7';
            ctx.lineWidth = active ? 4 : 2;

            if (active && progress < 1) {
                // 动画效果
                const currentX = fromX + (toX - fromX) * progress;
                const currentY = fromY + (toY - fromY) * progress;

                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(currentX, currentY);
                ctx.stroke();

                // 数据包
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(currentX, currentY, 6, 0, Math.PI * 2);
                ctx.fill();
            } else {
                ctx.beginPath();
                ctx.moveTo(fromX, fromY);
                ctx.lineTo(toX, toY);
                ctx.stroke();
            }

            ctx.restore();
        }

        // 绘制消息
        function drawMessage(x, y, message, type) {
            ctx.save();

            const color = type === 'control' ? '#fd79a8' : '#00cec9';
            ctx.fillStyle = color;
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 1;

            // 消息气泡
            const width = message.length * 8 + 20;
            const height = 25;
            ctx.fillRect(x - width/2, y - height/2, width, height);
            ctx.strokeRect(x - width/2, y - height/2, width, height);

            // 消息文本
            ctx.fillStyle = '#fff';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(message, x, y + 4);

            ctx.restore();
        }

        // FTP完整演示
        function startFTPDemo() {
            currentDemo = 'full';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);
            animateFTP();
        }

        // 控制端口演示
        function showControlDemo() {
            currentDemo = 'control';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);
            animateControl();
        }

        // 数据端口演示
        function showDataDemo() {
            currentDemo = 'data';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);
            animateData();
        }

        // 重置画布
        function resetCanvas() {
            if (animationId) cancelAnimationFrame(animationId);
            currentDemo = 'none';
            animationStep = 0;
            drawStaticFTP();
        }

        // 绘制静态FTP图
        function drawStaticFTP() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const clientX = 150;
            const clientY = 200;
            const serverX = 450;
            const serverY = 200;

            // 绘制设备
            drawDevice(clientX, clientY, 'client', 'FTP客户端', '#3498db');
            drawDevice(serverX, serverY, 'server', 'FTP服务器', '#e67e22');

            // 绘制端口
            drawPort(clientX - 20, clientY - 50, '21', 'control');
            drawPort(clientX + 20, clientY - 50, '20', 'data');
            drawPort(serverX - 20, serverY - 50, '21', 'control');
            drawPort(serverX + 20, serverY - 50, '20', 'data');

            // 绘制连接线
            drawConnection(clientX - 20, clientY - 35, serverX - 20, serverY - 35, 'control');
            drawConnection(clientX + 20, clientY - 35, serverX + 20, serverY - 35, 'data');

            // 添加说明
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('FTP双端口架构', canvas.width / 2 / window.devicePixelRatio, 50);
        }

        // 题目交互逻辑
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                const answerBlank = document.getElementById('answerBlank');
                
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'B') {
                    this.classList.add('correct');
                    progressFill.style.width = '100%';
                    answerBlank.textContent = '20';
                    answerBlank.style.background = 'rgba(0, 212, 170, 0.3)';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n解释：FTP使用端口20作为数据端口，负责文件的实际传输（上传/下载）。端口21是控制端口，负责发送命令。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    progressFill.style.width = '25%';
                    answerBlank.textContent = '?';
                    answerBlank.style.background = 'rgba(255, 71, 87, 0.3)';
                    setTimeout(() => {
                        let hint = '';
                        switch(answer) {
                            case 'A':
                                hint = '大于1024的端口通常是动态端口，不是FTP的标准端口。';
                                break;
                            case 'C':
                                hint = '端口80是HTTP协议的默认端口，不是FTP使用的。';
                                break;
                            case 'D':
                                hint = '端口21是FTP的控制端口，不是数据传输端口。';
                                break;
                        }
                        alert('❌ 答案不正确！\n\n提示：' + hint + '\n\n记住：FTP有两个端口，21是控制，20是数据！');
                    }, 500);
                }
            });
        });

        // 端口高亮功能
        function highlightPort(type) {
            const cards = document.querySelectorAll('.port-card');
            cards.forEach(card => card.classList.remove('pulse'));
            
            if (type === 'control') {
                document.querySelector('.port-card.control').classList.add('pulse');
            } else {
                document.querySelector('.port-card.data').classList.add('pulse');
            }
            
            setTimeout(() => {
                cards.forEach(card => card.classList.remove('pulse'));
            }, 2000);
        }

        // FTP完整动画
        function animateFTP() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const clientX = 150;
            const clientY = 200;
            const serverX = 450;
            const serverY = 200;

            // 绘制设备
            drawDevice(clientX, clientY, 'client', 'FTP客户端', '#3498db');
            drawDevice(serverX, serverY, 'server', 'FTP服务器', '#e67e22');

            const step = Math.floor(animationStep / 60);
            const progress = (animationStep % 60) / 60;

            switch (step) {
                case 0:
                    // 建立控制连接
                    drawPort(clientX - 20, clientY - 50, '21', 'control', true);
                    drawPort(serverX - 20, serverY - 50, '21', 'control', true);
                    drawConnection(clientX - 20, clientY - 35, serverX - 20, serverY - 35, 'control', true, progress);
                    drawMessage(300, 100, '建立控制连接(端口21)', 'control');
                    break;
                case 1:
                    // 发送登录命令
                    drawPort(clientX - 20, clientY - 50, '21', 'control', true);
                    drawPort(serverX - 20, serverY - 50, '21', 'control', true);
                    drawConnection(clientX - 20, clientY - 35, serverX - 20, serverY - 35, 'control', true);
                    drawMessage(300, 100, 'USER username', 'control');
                    break;
                case 2:
                    // 建立数据连接
                    drawPort(clientX + 20, clientY - 50, '20', 'data', true);
                    drawPort(serverX + 20, serverY - 50, '20', 'data', true);
                    drawConnection(clientX + 20, clientY - 35, serverX + 20, serverY - 35, 'data', true, progress);
                    drawMessage(300, 100, '建立数据连接(端口20)', 'data');
                    break;
                case 3:
                    // 传输文件
                    drawPort(clientX + 20, clientY - 50, '20', 'data', true);
                    drawPort(serverX + 20, serverY - 50, '20', 'data', true);
                    drawConnection(clientX + 20, clientY - 35, serverX + 20, serverY - 35, 'data', true);
                    drawMessage(300, 100, '📁 传输文件数据', 'data');
                    break;
                default:
                    // 完成
                    drawPort(clientX - 20, clientY - 50, '21', 'control', true);
                    drawPort(clientX + 20, clientY - 50, '20', 'data', true);
                    drawPort(serverX - 20, serverY - 50, '21', 'control', true);
                    drawPort(serverX + 20, serverY - 50, '20', 'data', true);
                    drawConnection(clientX - 20, clientY - 35, serverX - 20, serverY - 35, 'control', true);
                    drawConnection(clientX + 20, clientY - 35, serverX + 20, serverY - 35, 'data', true);

                    ctx.fillStyle = '#27ae60';
                    ctx.font = '18px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('✅ FTP传输完成！', canvas.width / 2 / window.devicePixelRatio, 100);
                    return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateFTP);
        }

        // 控制端口动画
        function animateControl() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const clientX = 150;
            const clientY = 200;
            const serverX = 450;
            const serverY = 200;

            drawDevice(clientX, clientY, 'client', 'FTP客户端', '#3498db');
            drawDevice(serverX, serverY, 'server', 'FTP服务器', '#e67e22');

            // 高亮控制端口
            drawPort(clientX - 20, clientY - 50, '21', 'control', true);
            drawPort(serverX - 20, serverY - 50, '21', 'control', true);
            drawConnection(clientX - 20, clientY - 35, serverX - 20, serverY - 35, 'control', true);

            // 显示控制命令
            const commands = ['USER admin', 'PASS 123456', 'LIST', 'CWD /home'];
            const commandIndex = Math.floor(animationStep / 60) % commands.length;
            drawMessage(300, 100, commands[commandIndex], 'control');

            ctx.fillStyle = '#fd79a8';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('📞 控制端口21：发送命令和接收响应', canvas.width / 2 / window.devicePixelRatio, 50);

            animationStep++;
            animationId = requestAnimationFrame(animateControl);
        }

        // 数据端口动画
        function animateData() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const clientX = 150;
            const clientY = 200;
            const serverX = 450;
            const serverY = 200;

            drawDevice(clientX, clientY, 'client', 'FTP客户端', '#3498db');
            drawDevice(serverX, serverY, 'server', 'FTP服务器', '#e67e22');

            // 高亮数据端口
            drawPort(clientX + 20, clientY - 50, '20', 'data', true);
            drawPort(serverX + 20, serverY - 50, '20', 'data', true);

            const progress = (animationStep % 120) / 120;
            drawConnection(clientX + 20, clientY - 35, serverX + 20, serverY - 35, 'data', true, progress);

            // 显示传输内容
            const files = ['📄 document.txt', '🖼️ image.jpg', '📊 data.xlsx', '🎵 music.mp3'];
            const fileIndex = Math.floor(animationStep / 120) % files.length;
            drawMessage(300, 100, files[fileIndex], 'data');

            ctx.fillStyle = '#00cec9';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('📦 数据端口20：传输文件和数据', canvas.width / 2 / window.devicePixelRatio, 50);

            animationStep++;
            animationId = requestAnimationFrame(animateData);
        }

        // 重新答题功能
        function reviewQuestion() {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('answerBlank').textContent = '?';
            document.getElementById('answerBlank').style.background = 'rgba(255, 255, 255, 0.3)';

            document.querySelector('.question-box').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            setTimeout(() => {
                document.querySelector('.question-box').classList.add('pulse');
                setTimeout(() => {
                    document.querySelector('.question-box').classList.remove('pulse');
                }, 2000);
            }, 500);
        }

        // 显示总结
        function showSummary() {
            const summary = `
🎯 FTP协议学习总结

✅ 正确答案：B - 端口20

📚 核心概念：
• FTP = File Transfer Protocol（文件传输协议）
• 使用双端口机制：控制端口21 + 数据端口20

🔌 端口详解：
• 端口21（控制端口）：
  - 建立连接、身份验证
  - 发送命令（USER、PASS、LIST等）
  - 接收服务器响应

• 端口20（数据端口）：
  - 传输文件数据
  - 上传/下载文件
  - 传输目录列表

🧠 记忆技巧：
• "20岁搬重物，21岁当指挥"
• "20数据，21控制"
• "文件传输找20，命令控制找21"

⚡ 考试要点：
• 看到"上传文件"、"下载文件"、"数据传输" → 端口20
• 看到"发送命令"、"控制连接"、"身份验证" → 端口21

🎉 恭喜掌握FTP双端口机制！
            `;

            alert(summary);
        }

        // 初始化
        drawStaticFTP();

        // 页面加载完成后的欢迎提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                welcome.innerHTML = `
                    <h3>🌟 欢迎来到FTP学习世界！</h3>
                    <p>让我们一起探索文件传输的双端口奥秘</p>
                    <button onclick="this.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 15px;
                        margin-top: 15px;
                        cursor: pointer;
                    ">开始学习 🚀</button>
                `;
                document.body.appendChild(welcome);
            }, 1000);
        });
    </script>
</body>
</html>
