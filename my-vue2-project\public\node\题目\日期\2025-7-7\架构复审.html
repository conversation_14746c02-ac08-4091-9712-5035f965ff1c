<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>架构复审 - 互动学习</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            background-color: #3498db;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.2em;
        }
        .intro {
            background-color: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .game-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .game-board {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 20px;
        }
        .project {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .project:hover {
            background-color: #e0e0e0;
        }
        .project.selected {
            border: 2px solid #3498db;
        }
        .issues-container {
            margin-top: 20px;
            display: none;
        }
        .issue {
            background-color: #ffebee;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid #f44336;
        }
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .feedback.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .feedback.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .progress-container {
            margin-top: 20px;
            background-color: #e0e0e0;
            border-radius: 5px;
            height: 20px;
        }
        .progress-bar {
            height: 100%;
            background-color: #2ecc71;
            border-radius: 5px;
            width: 0%;
            transition: width 0.5s;
        }
        .score-display {
            text-align: right;
            font-weight: bold;
            margin-top: 10px;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 100;
        }
        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            width: 70%;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .close {
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        #review-canvas {
            display: none;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-top: 20px;
            background-color: #fcfcfc;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>架构复审学习游戏</h1>
        </header>

        <div class="intro">
            <h2>什么是架构复审？</h2>
            <p>架构复审是软件开发中的一个重要活动，它是对系统架构设计进行评估和检查的过程。架构复审的目标是在早期发现架构设计中的潜在风险、缺陷和错误，确保系统架构能够满足需求。</p>
            
            <h3>架构复审的主要目的：</h3>
            <ul>
                <li>确保架构设计满足功能和非功能需求</li>
                <li>识别架构中的潜在风险和问题</li>
                <li>验证架构设计的一致性和完整性</li>
                <li>提高系统的质量和可维护性</li>
            </ul>
        </div>

        <div class="game-container">
            <h2>架构复审游戏</h2>
            <p>在这个游戏中，你将扮演架构师，对不同的项目进行架构复审。你需要找出每个项目中存在的架构问题，并提出改进建议。</p>
            
            <div class="game-board">
                <h3>选择一个项目进行复审：</h3>
                <div class="project" data-id="1">
                    <h4>电子商城系统</h4>
                    <p>一个在线购物平台，包含用户管理、商品展示、购物车和支付模块。</p>
                </div>
                <div class="project" data-id="2">
                    <h4>医院管理系统</h4>
                    <p>一个用于医院日常运营的系统，包含患者管理、预约挂号、医生排班和药品管理等功能。</p>
                </div>
                <div class="project" data-id="3">
                    <h4>智能家居控制系统</h4>
                    <p>一个用于控制家庭设备的系统，包含设备连接、远程控制和自动化场景等功能。</p>
                </div>
            </div>

            <div class="issues-container" id="issues-1">
                <h3>电子商城系统架构问题：</h3>
                <div class="issue">
                    <p><strong>问题1：</strong> 数据库连接池配置不合理，可能导致高并发时系统崩溃。</p>
                    <label><input type="checkbox" class="issue-check"> 标记为需要修复</label>
                </div>
                <div class="issue">
                    <p><strong>问题2：</strong> 支付模块与订单模块耦合度过高，难以维护和扩展。</p>
                    <label><input type="checkbox" class="issue-check"> 标记为需要修复</label>
                </div>
                <div class="issue">
                    <p><strong>问题3：</strong> 缺少缓存机制，可能导致系统响应速度慢。</p>
                    <label><input type="checkbox" class="issue-check"> 标记为需要修复</label>
                </div>
            </div>

            <div class="issues-container" id="issues-2">
                <h3>医院管理系统架构问题：</h3>
                <div class="issue">
                    <p><strong>问题1：</strong> 系统安全性不足，患者隐私数据保护机制不完善。</p>
                    <label><input type="checkbox" class="issue-check"> 标记为需要修复</label>
                </div>
                <div class="issue">
                    <p><strong>问题2：</strong> 系统可扩展性差，难以适应医院规模扩大的需求。</p>
                    <label><input type="checkbox" class="issue-check"> 标记为需要修复</label>
                </div>
                <div class="issue">
                    <p><strong>问题3：</strong> 缺少容错机制，单点故障可能导致整个系统瘫痪。</p>
                    <label><input type="checkbox" class="issue-check"> 标记为需要修复</label>
                </div>
            </div>

            <div class="issues-container" id="issues-3">
                <h3>智能家居控制系统架构问题：</h3>
                <div class="issue">
                    <p><strong>问题1：</strong> 设备通信协议不统一，导致系统兼容性差。</p>
                    <label><input type="checkbox" class="issue-check"> 标记为需要修复</label>
                </div>
                <div class="issue">
                    <p><strong>问题2：</strong> 缺少离线模式，网络中断时系统无法工作。</p>
                    <label><input type="checkbox" class="issue-check"> 标记为需要修复</label>
                </div>
                <div class="issue">
                    <p><strong>问题3：</strong> 系统响应延迟高，影响用户体验。</p>
                    <label><input type="checkbox" class="issue-check"> 标记为需要修复</label>
                </div>
            </div>

            <div class="actions">
                <button id="review-btn" disabled>提交复审结果</button>
                <button id="next-btn" disabled>下一个项目</button>
            </div>

            <canvas id="review-canvas" width="860" height="150" style="display: none;"></canvas>

            <div class="feedback" id="feedback"></div>
            
            <div class="progress-container">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
            
            <div class="score-display">
                得分: <span id="score">0</span>/9
            </div>
        </div>
        
        <div class="modal" id="completion-modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>恭喜你完成了架构复审游戏！</h2>
                <p>你的最终得分是: <span id="final-score">0</span>/9</p>
                <h3>架构复审的关键要点:</h3>
                <ul>
                    <li>架构复审应该在开发早期进行，以便及早发现问题</li>
                    <li>复审团队应包括架构师、开发人员、测试人员和业务分析师</li>
                    <li>复审应关注功能需求和非功能需求（如性能、安全性、可扩展性）</li>
                    <li>复审结果应形成文档，并作为后续开发的指导</li>
                </ul>
                <button id="restart-btn">重新开始</button>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        const gameState = {
            currentProject: null,
            reviewedProjects: [],
            score: 0,
            totalIssues: 9
        };

        // DOM 元素
        const projects = document.querySelectorAll('.project');
        const issuesContainers = document.querySelectorAll('.issues-container');
        const reviewBtn = document.getElementById('review-btn');
        const nextBtn = document.getElementById('next-btn');
        const feedbackEl = document.getElementById('feedback');
        const progressBar = document.getElementById('progress-bar');
        const scoreDisplay = document.getElementById('score');
        const modal = document.getElementById('completion-modal');
        const finalScore = document.getElementById('final-score');
        const closeModal = document.querySelector('.close');
        const restartBtn = document.getElementById('restart-btn');

        // 事件监听器
        projects.forEach(project => {
            project.addEventListener('click', () => selectProject(project));
        });

        reviewBtn.addEventListener('click', submitReview);
        nextBtn.addEventListener('click', nextProject);
        closeModal.addEventListener('click', () => modal.style.display = 'none');
        restartBtn.addEventListener('click', restartGame);

        // 选择项目
        function selectProject(project) {
            // 如果该项目已经被审核过，则不做任何操作
            if (gameState.reviewedProjects.includes(project.dataset.id)) {
                return;
            }

            // 清除之前选择的项目
            projects.forEach(p => p.classList.remove('selected'));
            
            // 隐藏所有问题容器
            issuesContainers.forEach(container => {
                container.style.display = 'none';
            });
            
            // 隐藏动画画布
            document.getElementById('review-canvas').style.display = 'none';
            feedbackEl.style.display = 'none';

            // 选中当前项目并显示其问题
            project.classList.add('selected');
            gameState.currentProject = project.dataset.id;
            document.getElementById(`issues-${gameState.currentProject}`).style.display = 'block';
            
            // 启用提交按钮
            reviewBtn.disabled = false;
        }

        // 提交复审结果
        function submitReview() {
            if (!gameState.currentProject) return;
            
            // 获取当前项目的所有问题复选框
            const issueChecks = document.querySelectorAll(`#issues-${gameState.currentProject} .issue-check`);
            
            // 计算正确标记的问题数量（这里假设所有问题都需要修复）
            let correctIssues = 0;
            const checkedIndices = [];
            issueChecks.forEach((check, index) => {
                if (check.checked) {
                    correctIssues++;
                    checkedIndices.push(index);
                }
            });
            
            // 更新分数
            gameState.score += correctIssues;
            scoreDisplay.textContent = gameState.score;
            
            // 更新进度条
            const progress = (gameState.reviewedProjects.length + 1) / projects.length * 100;
            progressBar.style.width = `${progress}%`;
            
            // 显示反馈
            feedbackEl.textContent = `你在这个项目中发现了 ${correctIssues}/${issueChecks.length} 个架构问题！`;
            feedbackEl.className = 'feedback';
            feedbackEl.classList.add(correctIssues === issueChecks.length ? 'success' : 'error');
            feedbackEl.style.display = 'block';

            // --- Canvas动画 ---
            const canvas = document.getElementById('review-canvas');
            const ctx = canvas.getContext('2d');
            canvas.style.display = 'block';

            if (checkedIndices.length > 0) {
                animateIssues(ctx, gameState.currentProject, checkedIndices);
            } else {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.font = '16px "Microsoft YaHei"';
                ctx.textAlign = 'center';
                ctx.fillStyle = '#666';
                ctx.fillText("你没有标记任何问题。", canvas.width / 2, canvas.height / 2);
            }
            
            // 标记项目为已审核
            gameState.reviewedProjects.push(gameState.currentProject);
            
            // 禁用提交按钮，启用下一个按钮
            reviewBtn.disabled = true;
            nextBtn.disabled = false;
            
            // 如果所有项目都已审核，显示完成模态框
            if (gameState.reviewedProjects.length === projects.length) {
                finalScore.textContent = gameState.score;
                setTimeout(() => {
                    modal.style.display = 'block';
                }, 1000);
            }
        }

        // 下一个项目
        function nextProject() {
            // 清除选择和反馈
            projects.forEach(p => p.classList.remove('selected'));
            feedbackEl.style.display = 'none';
            document.getElementById('review-canvas').style.display = 'none';
            
            // 隐藏所有问题容器
            issuesContainers.forEach(container => {
                container.style.display = 'none';
            });
            
            // 重置复选框
            document.querySelectorAll('.issue-check').forEach(check => {
                check.checked = false;
            });
            
            // 禁用按钮
            reviewBtn.disabled = true;
            nextBtn.disabled = true;
            
            // 重置当前项目
            gameState.currentProject = null;
        }

        // 重新开始游戏
        function restartGame() {
            // 重置游戏状态
            gameState.currentProject = null;
            gameState.reviewedProjects = [];
            gameState.score = 0;
            
            // 更新UI
            scoreDisplay.textContent = '0';
            progressBar.style.width = '0%';
            
            // 隐藏模态框
            modal.style.display = 'none';
            
            // 重置所有项目和问题
            nextProject();
        }

        // --- Canvas Animation Functions ---

        function animateIssues(ctx, projectId, checkedIndices) {
            let frame = 0;
            const duration = 200; // 动画时长
            const canvas = ctx.canvas;

            function animate() {
                frame++;
                if (frame > duration) {
                    drawFinalState(ctx, projectId, checkedIndices);
                    return;
                }
                
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const progress = frame / duration;
                const zoneWidth = canvas.width / 3;
                
                checkedIndices.forEach(index => {
                    const x = zoneWidth * index + (zoneWidth / 2);
                    const y = canvas.height / 2 - 10;
                    const issueId = `${projectId}-${index + 1}`;
                    drawIssueAnimation(ctx, issueId, x, y, progress);
                });

                requestAnimationFrame(animate);
            }
            animate();
        }

        function drawFinalState(ctx, projectId, checkedIndices) {
            const canvas = ctx.canvas;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            const zoneWidth = canvas.width / 3;

            checkedIndices.forEach(index => {
                const x = zoneWidth * index + (zoneWidth / 2);
                const y = canvas.height / 2 - 10;
                const issueId = `${projectId}-${index + 1}`;
                // 重绘动画最后一帧
                drawIssueAnimation(ctx, issueId, x, y, 1); 
                ctx.fillStyle = '#27ae60';
                ctx.font = 'bold 14px "Microsoft YaHei"';
                ctx.fillText("✓ 问题已识别!", x, y + 65);
            });
        }
        
        function drawIssueAnimation(ctx, issueId, x, y, progress) {
            ctx.save();
            ctx.font = '12px "Microsoft YaHei"';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#333';

            const titles = {
                '1-1': "DB连接池过载", '1-2': "模块高度耦合", '1-3': "缺少缓存",
                '2-1': "安全隐私泄露", '2-2': "扩展性差", '2-3': "无容错机制",
                '3-1': "协议不兼容", '3-2': "缺少离线模式", '3-3': "响应延迟高"
            };
            ctx.fillText(titles[issueId] || "未知问题", x, y + 55);

            switch (issueId) {
                case '1-1': // DB Crash
                    const dbColor = progress > 0.5 ? '#e74c3c' : '#3498db';
                    const shake = progress > 0.5 ? (Math.random() - 0.5) * 4 : 0;
                    // Draw DB
                    ctx.fillStyle = dbColor;
                    ctx.beginPath();
                    ctx.ellipse(x + shake, y + 20, 30, 10, 0, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.fillRect(x - 30 + shake, y - 20, 60, 40);
                    ctx.beginPath();
                    ctx.ellipse(x + shake, y - 20, 30, 10, 0, 0, 2 * Math.PI);
                    ctx.fill();
                    // Draw requests
                    for(let i=0; i < 10; i++) {
                        const reqX = x - 80 + (80 * progress * (i%5+1));
                        const reqY = y - 20 + i*5;
                        if(reqX < x - 40) {
                           ctx.fillStyle = '#2ecc71';
                           ctx.fillRect(reqX, reqY, 5, 5);
                        }
                    }
                    if (progress > 0.5) {
                        ctx.font = '20px sans-serif';
                        ctx.fillStyle = 'red';
                        ctx.fillText('!', x + 35, y - 10);
                    }
                    break;
                case '1-2': // Coupling
                     const separation = 20 * progress;
                     ctx.fillStyle = '#34495e';
                     ctx.fillRect(x - 50 - separation, y - 20, 40, 40);
                     ctx.fillRect(x + 10 + separation, y - 20, 40, 40);
                     ctx.fillStyle = 'white';
                     ctx.fillText("支付", x - 30 - separation, y + 5);
                     ctx.fillText("订单", x + 30 + separation, y + 5);
                     // Draw glue
                     ctx.fillStyle = '#f1c40f';
                     ctx.globalAlpha = 1 - progress;
                     ctx.fillRect(x - 10, y - 10, 20, 20);
                     ctx.globalAlpha = 1;
                     if (progress > 0.5) {
                         ctx.strokeStyle = '#e74c3c';
                         ctx.lineWidth = 2;
                         ctx.beginPath();
                         ctx.moveTo(x-10, y-10); ctx.lineTo(x+10, y+10);
                         ctx.moveTo(x+10, y-10); ctx.lineTo(x-10, y+10);
                         ctx.stroke();
                     }
                    break;
                case '1-3': // No Cache
                    // Snail
                    const snailX = x - 50 + 100 * progress;
                    ctx.fillStyle = '#f39c12';
                    ctx.beginPath();
                    ctx.arc(snailX, y, 15, 0, Math.PI, true);
                    ctx.fill();
                    ctx.fillStyle = '#27ae60';
                    ctx.fillRect(snailX - 15, y, 30, 10);
                    ctx.fillText("🐌", snailX-20, y+5);
                    ctx.fillText("Server", x + 60, y + 5);
                    ctx.strokeRect(x+40, y-15, 40, 30);
                    break;
                case '2-1': // Security
                    const thiefX = x - 50 + 80 * progress;
                    ctx.fillText("👤", thiefX, y+10); // Thief
                    ctx.fillText("📄", x + 40, y+10); // File
                    ctx.fillText("隐私", x + 40, y+25);
                    if (progress > 0.8) {
                        ctx.fillStyle="red";
                        ctx.fillText("被盗!", x + 40, y-10);
                    }
                    break;
                case '2-2': // Scalability
                     ctx.fillStyle = '#95a5a6';
                     ctx.fillRect(x - 30, y - 20, 60, 40); // base
                     if(progress > 0.3) {
                         const crack = progress > 0.7;
                         ctx.globalAlpha = (progress - 0.3) / 0.7;
                         ctx.fillRect(x - 30, y - 40, 60, 20); // new floor
                         if(crack) {
                             ctx.strokeStyle="red";
                             ctx.beginPath();
                             ctx.moveTo(x-20, y-10); ctx.lineTo(x+20, y-30);
                             ctx.stroke();
                         }
                         ctx.globalAlpha = 1.0;
                     }
                    break;
                case '2-3': // Fault Tolerance
                    const colors = ['#2ecc71', '#2ecc71', '#2ecc71'];
                    if (progress > 0.5) colors[1] = '#e74c3c';
                    if (progress > 0.8) { colors[0] = '#bdc3c7'; colors[2] = '#bdc3c7'; }
                    for(let i=0; i<3; i++) {
                        ctx.fillStyle = colors[i];
                        ctx.fillRect(x - 60 + i*40, y-15, 30, 30);
                    }
                    break;
                case '3-1': // Compatibility
                    const plugX = x - 40;
                    const plugY = y - 30 + 60 * Math.sin(progress * Math.PI * 2);
                    ctx.fillStyle = '#e74c3c'; // round plug
                    ctx.beginPath(); ctx.arc(plugX, plugY, 10, 0, 2*Math.PI); ctx.fill();
                    ctx.fillStyle = '#34495e'; // square socket
                    ctx.strokeRect(x + 20, y - 10, 20, 20);
                    break;
                case '3-2': // Offline Mode
                    ctx.font = '30px sans-serif';
                    ctx.fillText("🏠", x, y + 20);
                    ctx.fillText("☁️", x, y - 20);
                    if(progress > 0.5) {
                        const prog = (progress - 0.5) / 0.5;
                        ctx.strokeStyle = "red";
                        ctx.lineWidth = 3;
                        ctx.globalAlpha = prog;
                        ctx.beginPath();
                        ctx.moveTo(x-10, y-10); ctx.lineTo(x+10, y-30);
                        ctx.moveTo(x+10, y-10); ctx.lineTo(x-10, y-30);
                        ctx.stroke();
                        ctx.globalAlpha = 1.0;
                        if(prog > 0.5) ctx.fillStyle = `rgba(0,0,0,${(prog-0.5)/0.5 * 0.5})`;
                        ctx.fillRect(x-20, y, 40, 30);
                    }
                    break;
                case '3-3': // Latency
                    const barWidth = 80;
                    ctx.font = '30px sans-serif';
                    ctx.fillText("📱", x - 50, y);
                    ctx.fillText("💡", x + 50, y);
                    ctx.strokeStyle = '#bdc3c7';
                    ctx.strokeRect(x - 40, y + 20, barWidth, 10);
                    if (progress > 0.1) {
                         ctx.fillStyle = '#e67e22';
                         ctx.fillRect(x - 40, y + 20, barWidth * (progress-0.1)/0.9, 10);
                    }
                    if (progress < 1) {
                        ctx.globalAlpha = 0.5;
                    }
                     ctx.font = '30px sans-serif';
                    ctx.fillText("💡", x + 50, y);
                    ctx.globalAlpha = 1.0;
                    break;
            }
            ctx.restore();
        }
    </script>
</body>
</html> 