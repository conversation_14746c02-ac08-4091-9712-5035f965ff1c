<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库三级模式结构 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 50px;
        }

        .theory-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInLeft 1s ease-out;
        }

        .interactive-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: slideInRight 1s ease-out;
        }

        .level-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .level-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .level-card.active {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            transform: scale(1.05);
        }

        .level-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .level-description {
            color: #666;
            line-height: 1.6;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 400px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: inset 0 0 20px rgba(0,0,0,0.1);
        }

        #gameCanvas {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #f0f2f5, #e8ecf0);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .quiz-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .question {
            font-size: 1.3em;
            margin-bottom: 25px;
            color: #333;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .option.correct {
            background: #c8e6c9;
            border-color: #4caf50;
            animation: pulse 0.5s ease-in-out;
        }

        .option.wrong {
            background: #ffcdd2;
            border-color: #f44336;
            animation: shake 0.5s ease-in-out;
        }

        .explanation {
            background: #f0f8ff;
            border-left: 4px solid #2196f3;
            padding: 20px;
            margin-top: 20px;
            border-radius: 0 10px 10px 0;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: slideDown 0.5s ease-out;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes slideDown {
            from { opacity: 0; max-height: 0; }
            to { opacity: 1; max-height: 200px; }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1>🏗️ 数据库三级模式结构</h1>
            <p>通过动画和交互，轻松理解数据库架构的奥秘</p>
        </div>

        <div class="main-content">
            <div class="theory-section">
                <h2 style="color: #333; margin-bottom: 25px; text-align: center;">📚 理论知识</h2>
                
                <div class="level-card" data-level="external" onclick="showLevel('external')">
                    <div class="level-title">🎭 外模式 (External Schema)</div>
                    <div class="level-description">
                        用户视图层，定义用户看到的数据结构。就像不同的人看同一栋建筑，看到的角度和内容都不同。
                    </div>
                </div>

                <div class="level-card" data-level="conceptual" onclick="showLevel('conceptual')">
                    <div class="level-title">🏛️ 模式 (Conceptual Schema)</div>
                    <div class="level-description">
                        逻辑结构层，定义数据库的整体逻辑结构。就像建筑的设计图纸，规定了房间、走廊的布局。
                    </div>
                </div>

                <div class="level-card" data-level="internal" onclick="showLevel('internal')">
                    <div class="level-title">🔧 内模式 (Internal Schema)</div>
                    <div class="level-description">
                        物理存储层，定义数据在磁盘上的存储方式、索引结构等。就像建筑的地基和钢筋结构。
                    </div>
                </div>
            </div>

            <div class="interactive-section">
                <h2 style="color: #333; margin-bottom: 25px; text-align: center;">🎮 互动演示</h2>
                <div class="canvas-container">
                    <canvas id="gameCanvas"></canvas>
                </div>
                <div class="controls">
                    <button class="btn" onclick="startAnimation()">🎬 开始动画</button>
                    <button class="btn" onclick="resetDemo()">🔄 重置</button>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 style="color: #333; margin-bottom: 25px; text-align: center;">🧠 知识检测</h2>
            <div class="question">
                采用三级模式结构的数据库系统中，如果对一个表创建聚集索引，那么改变的是数据库的（ ）
            </div>
            <div class="options">
                <div class="option" onclick="selectOption(this, false)">A. 外模式</div>
                <div class="option" onclick="selectOption(this, false)">B. 模式</div>
                <div class="option" onclick="selectOption(this, true)">C. 内模式</div>
                <div class="option" onclick="selectOption(this, false)">D. 用户模式</div>
            </div>
            <div class="explanation" id="explanation">
                <h3>💡 详细解析</h3>
                <p><strong>正确答案：C. 内模式</strong></p>
                <p>聚集索引改变了数据在物理存储上的组织方式，数据行按照索引键的顺序物理存储。这属于物理存储层面的改变，因此影响的是<strong>内模式</strong>。</p>
                <p>内模式定义了：</p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>存储记录的类型和物理顺序</li>
                    <li>索引和存储路径</li>
                    <li>数据的存储组织方式</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 画布和动画相关变量
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentLevel = null;

        // 设置画布尺寸
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
        }

        // 初始化
        function init() {
            resizeCanvas();
            createFloatingElements();
            drawInitialState();
        }

        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 20; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(element);
            }
        }

        // 绘制初始状态
        function drawInitialState() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制三层架构
            const layerHeight = canvas.height / 4;
            const layerWidth = canvas.width * 0.8;
            const startX = canvas.width * 0.1;
            
            // 外模式层
            drawLayer(startX, layerHeight * 0.5, layerWidth, layerHeight * 0.6, '#ff9a9e', '外模式 - 用户视图');
            
            // 模式层
            drawLayer(startX, layerHeight * 1.5, layerWidth, layerHeight * 0.6, '#a8edea', '模式 - 逻辑结构');
            
            // 内模式层
            drawLayer(startX, layerHeight * 2.5, layerWidth, layerHeight * 0.6, '#ffd89b', '内模式 - 物理存储');
        }

        // 绘制层级
        function drawLayer(x, y, width, height, color, text) {
            // 绘制渐变背景
            const gradient = ctx.createLinearGradient(x, y, x + width, y + height);
            gradient.addColorStop(0, color);
            gradient.addColorStop(1, adjustColor(color, -20));
            
            ctx.fillStyle = gradient;
            ctx.fillRect(x, y, width, height);
            
            // 绘制边框
            ctx.strokeStyle = adjustColor(color, -40);
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            
            // 绘制文字
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + width / 2, y + height / 2 + 5);
        }

        // 调整颜色亮度
        function adjustColor(color, amount) {
            const hex = color.replace('#', '');
            const r = Math.max(0, Math.min(255, parseInt(hex.substr(0, 2), 16) + amount));
            const g = Math.max(0, Math.min(255, parseInt(hex.substr(2, 2), 16) + amount));
            const b = Math.max(0, Math.min(255, parseInt(hex.substr(4, 2), 16) + amount));
            return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
        }

        // 显示特定层级
        function showLevel(level) {
            // 移除之前的活跃状态
            document.querySelectorAll('.level-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // 添加当前活跃状态
            document.querySelector(`[data-level="${level}"]`).classList.add('active');
            
            currentLevel = level;
            animateLevel(level);
        }

        // 动画显示层级
        function animateLevel(level) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawInitialState();
            
            const layerHeight = canvas.height / 4;
            const layerWidth = canvas.width * 0.8;
            const startX = canvas.width * 0.1;
            
            let targetY, color, description;
            
            switch(level) {
                case 'external':
                    targetY = layerHeight * 0.5;
                    color = '#ff9a9e';
                    description = '用户看到的数据视图';
                    break;
                case 'conceptual':
                    targetY = layerHeight * 1.5;
                    color = '#a8edea';
                    description = '数据库的逻辑结构';
                    break;
                case 'internal':
                    targetY = layerHeight * 2.5;
                    color = '#ffd89b';
                    description = '物理存储和索引结构';
                    break;
            }
            
            // 高亮显示选中的层
            ctx.fillStyle = 'rgba(255, 255, 0, 0.3)';
            ctx.fillRect(startX - 10, targetY - 10, layerWidth + 20, layerHeight * 0.6 + 20);
            
            // 添加动画效果
            animateHighlight(startX - 10, targetY - 10, layerWidth + 20, layerHeight * 0.6 + 20);
        }

        // 高亮动画
        function animateHighlight(x, y, width, height) {
            let alpha = 0.3;
            let increasing = false;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                drawInitialState();
                
                ctx.fillStyle = `rgba(255, 255, 0, ${alpha})`;
                ctx.fillRect(x, y, width, height);
                
                if (increasing) {
                    alpha += 0.01;
                    if (alpha >= 0.5) increasing = false;
                } else {
                    alpha -= 0.01;
                    if (alpha <= 0.1) increasing = true;
                }
                
                animationId = requestAnimationFrame(animate);
            }
            
            animate();
        }

        // 开始完整动画
        function startAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            let step = 0;
            const steps = ['external', 'conceptual', 'internal'];
            
            function nextStep() {
                if (step < steps.length) {
                    showLevel(steps[step]);
                    step++;
                    setTimeout(nextStep, 2000);
                } else {
                    // 显示索引创建动画
                    showIndexAnimation();
                }
            }
            
            nextStep();
        }

        // 显示索引创建动画
        function showIndexAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawInitialState();
            
            const layerHeight = canvas.height / 4;
            const layerWidth = canvas.width * 0.8;
            const startX = canvas.width * 0.1;
            const internalY = layerHeight * 2.5;
            
            // 高亮内模式层
            ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
            ctx.fillRect(startX - 10, internalY - 10, layerWidth + 20, layerHeight * 0.6 + 20);
            
            // 绘制索引图标
            drawIndexIcon(startX + layerWidth / 2, internalY + layerHeight * 0.3);
            
            // 添加文字说明
            ctx.fillStyle = '#ff0000';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('创建聚集索引 → 改变内模式', canvas.width / 2, canvas.height - 30);
        }

        // 绘制索引图标
        function drawIndexIcon(x, y) {
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(x, y, 20, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('📊', x, y + 5);
        }

        // 重置演示
        function resetDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            document.querySelectorAll('.level-card').forEach(card => {
                card.classList.remove('active');
            });
            
            currentLevel = null;
            drawInitialState();
        }

        // 选择答案
        function selectOption(element, isCorrect) {
            // 禁用所有选项
            document.querySelectorAll('.option').forEach(opt => {
                opt.style.pointerEvents = 'none';
            });
            
            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    document.getElementById('explanation').classList.add('show');
                }, 500);
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                setTimeout(() => {
                    document.querySelector('.option:nth-child(3)').classList.add('correct');
                    document.getElementById('explanation').classList.add('show');
                }, 1000);
            }
        }

        // 窗口大小改变时重新调整画布
        window.addEventListener('resize', () => {
            resizeCanvas();
            if (currentLevel) {
                showLevel(currentLevel);
            } else {
                drawInitialState();
            }
        });

        // 初始化
        init();
    </script>
</body>
</html>
