<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闪存（Flash Memory）互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .demo-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .memory-demo {
            text-align: center;
            margin: 30px 0;
        }

        #memoryCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .demo-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 25px 0;
            flex-wrap: wrap;
        }

        .demo-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
        }

        .power-btn {
            background: linear-gradient(45deg, #fd79a8, #fdcb6e);
        }

        .access-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .delete-btn {
            background: linear-gradient(45deg, #e17055, #d63031);
        }

        .compare-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .demo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .memory-types {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .memory-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .memory-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .memory-card.flash {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .memory-card.ram {
            border-color: #fd79a8;
        }

        .memory-card.rom {
            border-color: #00b894;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-correct {
            color: #00a085;
            font-weight: bold;
            background: rgba(0,184,148,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .highlight-wrong {
            color: #d63031;
            font-weight: bold;
            background: rgba(214,48,49,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-chip {
            position: absolute;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            animation: floatChip 12s infinite ease-in-out;
        }

        .chip1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .chip2 {
            top: 70%;
            right: 15%;
            animation-delay: 4s;
        }

        .chip3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 8s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatChip {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-25px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .memory-types {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-chip chip1"></div>
        <div class="floating-chip chip2"></div>
        <div class="floating-chip chip3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>💾 闪存学习实验室</h1>
            <p>通过互动动画深度理解Flash Memory的特性与工作原理</p>
        </div>

        <div class="main-grid">
            <div class="demo-section">
                <h2 class="section-title">🔬 闪存特性演示</h2>
                
                <div class="memory-demo">
                    <canvas id="memoryCanvas" width="500" height="350"></canvas>
                </div>

                <div class="demo-controls">
                    <button class="demo-btn power-btn" onclick="demonstratePower()">断电测试</button>
                    <button class="demo-btn access-btn" onclick="demonstrateAccess()">访问方式</button>
                    <button class="demo-btn delete-btn" onclick="demonstrateDelete()">删除操作</button>
                    <button class="demo-btn compare-btn" onclick="compareMemories()">存储器对比</button>
                </div>

                <div class="memory-types">
                    <div class="memory-card flash" onclick="showMemoryInfo('flash')">
                        <h3>💾 Flash</h3>
                        <p>非易失性<br>块删除<br>顺序访问</p>
                    </div>
                    <div class="memory-card ram" onclick="showMemoryInfo('ram')">
                        <h3>🧠 RAM</h3>
                        <p>易失性<br>随机访问<br>主存储器</p>
                    </div>
                    <div class="memory-card rom" onclick="showMemoryInfo('rom')">
                        <h3>📀 ROM</h3>
                        <p>非易失性<br>只读存储<br>固化程序</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 以下关于闪存（Flash Memory）的叙述中，<strong>错误的是</strong>（　　）。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        A. 掉电后信息不会丢失，属于非易失性存储器
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 以块为单位进行删除操作
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        C. 采用随机访问方式，常用来代替主存
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        D. 在嵌入式系统中可以用Flash来代替ROM存储器
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：C（错误选项）</strong></p>
                    <ul>
                        <li><span class="highlight-correct">A正确</span>：闪存掉电后信息不丢失，属于非易失性存储器</li>
                        <li><span class="highlight-correct">B正确</span>：闪存以块为单位进行删除操作，不能单独删除某个字节</li>
                        <li><span class="highlight-wrong">C错误</span>：闪存采用顺序访问方式，代替的是ROM而不是RAM主存</li>
                        <li><span class="highlight-correct">D正确</span>：在嵌入式系统中，Flash已全面代替ROM存储Bootloader和程序代码</li>
                    </ul>
                    <p><strong>关键理解</strong>：Flash Memory主要用于替代ROM存储器，而不是RAM主存储器。它的访问速度比RAM慢，主要用于存储程序代码和数据。</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了Flash Memory的核心特性！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('memoryCanvas');
        const ctx = canvas.getContext('2d');
        let currentDemo = null;

        // 绘制基础存储器结构
        function drawMemoryStructure() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制Flash存储器芯片
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(150, 50, 200, 250);
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 3;
            ctx.strokeRect(150, 50, 200, 250);

            // 芯片标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Flash Memory', 250, 80);

            // 绘制存储块
            const blockSize = 30;
            const blocksPerRow = 4;
            const blockSpacing = 5;
            
            for (let i = 0; i < 16; i++) {
                const row = Math.floor(i / blocksPerRow);
                const col = i % blocksPerRow;
                const x = 170 + col * (blockSize + blockSpacing);
                const y = 100 + row * (blockSize + blockSpacing);
                
                ctx.fillStyle = '#e9ecef';
                ctx.fillRect(x, y, blockSize, blockSize);
                ctx.strokeStyle = '#6c757d';
                ctx.lineWidth = 1;
                ctx.strokeRect(x, y, blockSize, blockSize);
                
                // 块编号
                ctx.fillStyle = '#495057';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(i.toString(), x + blockSize/2, y + blockSize/2 + 3);
            }

            // 绘制控制器
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(200, 270, 100, 40);
            ctx.strokeStyle = '#e84393';
            ctx.lineWidth = 2;
            ctx.strokeRect(200, 270, 100, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('控制器', 250, 295);
        }

        // 演示断电特性
        function demonstratePower() {
            if (currentDemo) clearInterval(currentDemo);
            
            drawMemoryStructure();
            let isPowerOn = true;
            let blinkCount = 0;
            
            currentDemo = setInterval(() => {
                if (blinkCount < 6) {
                    if (isPowerOn) {
                        // 断电效果
                        ctx.fillStyle = 'rgba(0,0,0,0.7)';
                        ctx.fillRect(0, 0, canvas.width, canvas.height);
                        
                        ctx.fillStyle = '#e74c3c';
                        ctx.font = 'bold 24px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('断电！', 250, 180);
                        
                        ctx.fillStyle = 'white';
                        ctx.font = '16px Arial';
                        ctx.fillText('数据仍然保存', 250, 210);
                    } else {
                        // 恢复供电
                        drawMemoryStructure();
                        
                        ctx.fillStyle = '#00b894';
                        ctx.font = 'bold 20px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText('✓ 数据完整保存', 250, 330);
                    }
                    isPowerOn = !isPowerOn;
                    blinkCount++;
                } else {
                    clearInterval(currentDemo);
                    drawMemoryStructure();
                    ctx.fillStyle = '#00b894';
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('非易失性存储器特性', 250, 330);
                }
            }, 800);
        }

        // 演示访问方式
        function demonstrateAccess() {
            if (currentDemo) clearInterval(currentDemo);
            
            drawMemoryStructure();
            let currentBlock = 0;
            
            currentDemo = setInterval(() => {
                drawMemoryStructure();
                
                // 高亮当前访问的块
                const row = Math.floor(currentBlock / 4);
                const col = currentBlock % 4;
                const x = 170 + col * 35;
                const y = 100 + row * 35;
                
                ctx.fillStyle = '#fdcb6e';
                ctx.fillRect(x, y, 30, 30);
                ctx.strokeStyle = '#e17055';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, 30, 30);
                
                ctx.fillStyle = '#2d3436';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(currentBlock.toString(), x + 15, y + 18);
                
                // 显示访问信息
                ctx.fillStyle = '#74b9ff';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`顺序访问块 ${currentBlock}`, 250, 330);
                
                currentBlock = (currentBlock + 1) % 16;
            }, 300);
        }

        // 演示删除操作
        function demonstrateDelete() {
            if (currentDemo) clearInterval(currentDemo);
            
            drawMemoryStructure();
            let step = 0;
            const blocksToDelete = [0, 1, 2, 3]; // 第一行
            
            currentDemo = setInterval(() => {
                if (step < blocksToDelete.length) {
                    const blockIndex = blocksToDelete[step];
                    const row = Math.floor(blockIndex / 4);
                    const col = blockIndex % 4;
                    const x = 170 + col * 35;
                    const y = 100 + row * 35;
                    
                    // 删除动画
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillRect(x, y, 30, 30);
                    ctx.strokeStyle = '#c0392b';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(x, y, 30, 30);
                    
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('X', x + 15, y + 18);
                    
                    step++;
                } else {
                    clearInterval(currentDemo);
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('以块为单位删除', 250, 330);
                }
            }, 500);
        }

        // 对比不同存储器
        function compareMemories() {
            if (currentDemo) clearInterval(currentDemo);
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const memories = [
                { name: 'Flash', x: 80, color: '#74b9ff', features: ['非易失', '块删除', '顺序访问'] },
                { name: 'RAM', x: 250, color: '#fd79a8', features: ['易失性', '随机访问', '主存储'] },
                { name: 'ROM', x: 420, color: '#00b894', features: ['非易失', '只读', '固化程序'] }
            ];
            
            memories.forEach((mem, index) => {
                // 绘制存储器
                ctx.fillStyle = mem.color;
                ctx.fillRect(mem.x - 40, 80, 80, 120);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 3;
                ctx.strokeRect(mem.x - 40, 80, 80, 120);
                
                // 名称
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(mem.name, mem.x, 110);
                
                // 特性
                mem.features.forEach((feature, i) => {
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(feature, mem.x, 135 + i * 20);
                });
                
                // 图标
                const icons = ['💾', '🧠', '📀'];
                ctx.font = '24px Arial';
                ctx.fillText(icons[index], mem.x, 250);
            });
            
            // 标题
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('存储器类型对比', 250, 40);
        }

        // 显示存储器信息
        function showMemoryInfo(type) {
            const info = {
                flash: 'Flash Memory：非易失性存储器，断电不丢失数据，以块为单位删除，常用于存储程序代码',
                ram: 'RAM：随机访问存储器，断电丢失数据，可随机读写，用作主存储器',
                rom: 'ROM：只读存储器，数据固化，主要存储固件和启动程序'
            };
            
            alert(info[type]);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('C. 采用随机访问方式')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画
                    demonstrateAccess();
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            drawMemoryStructure();

            // 自动演示序列
            setTimeout(() => {
                demonstratePower();
            }, 2000);

            setTimeout(() => {
                demonstrateAccess();
            }, 8000);

            setTimeout(() => {
                compareMemories();
            }, 15000);
        };

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case '1':
                    demonstratePower();
                    break;
                case '2':
                    demonstrateAccess();
                    break;
                case '3':
                    demonstrateDelete();
                    break;
                case '4':
                    compareMemories();
                    break;
            }
        });

        // 添加触摸支持
        let touchStartX = 0;
        canvas.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
        });

        canvas.addEventListener('touchend', function(e) {
            const touchEndX = e.changedTouches[0].clientX;
            const diff = touchStartX - touchEndX;

            if (Math.abs(diff) > 50) {
                if (diff > 0) {
                    // 向左滑动
                    demonstrateAccess();
                } else {
                    // 向右滑动
                    demonstratePower();
                }
            }
        });

        // 添加鼠标点击Canvas交互
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检测点击的存储块
            if (x >= 170 && x <= 330 && y >= 100 && y <= 240) {
                const blockCol = Math.floor((x - 170) / 35);
                const blockRow = Math.floor((y - 100) / 35);
                const blockIndex = blockRow * 4 + blockCol;

                if (blockIndex >= 0 && blockIndex < 16) {
                    // 高亮点击的块
                    drawMemoryStructure();
                    const blockX = 170 + blockCol * 35;
                    const blockY = 100 + blockRow * 35;

                    ctx.fillStyle = '#fdcb6e';
                    ctx.fillRect(blockX, blockY, 30, 30);
                    ctx.strokeStyle = '#e17055';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(blockX, blockY, 30, 30);

                    ctx.fillStyle = '#2d3436';
                    ctx.font = 'bold 12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(blockIndex.toString(), blockX + 15, blockY + 18);

                    ctx.fillStyle = '#74b9ff';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText(`选中存储块 ${blockIndex}`, 250, 330);
                }
            }
        });
    </script>
</body>
</html>
