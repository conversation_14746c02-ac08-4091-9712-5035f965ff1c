<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：inter-（在...之间、相互）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
        }

        #galaxyCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .diplomatic-council {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .embassy-station {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .embassy-station::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.6s;
        }

        .embassy-station:hover::before {
            left: 100%;
        }

        .embassy-station:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .embassy-station.connected {
            opacity: 1;
            transform: translateY(0);
        }

        .diplomatic-bridge {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            position: relative;
        }

        .planet-a {
            background: #28a745;
            color: white;
            padding: 15px 25px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 1.1rem;
            position: relative;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .planet-a::after {
            content: '星球A';
            position: absolute;
            top: -15px;
            right: -15px;
            background: #218838;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .communication-beam {
            flex: 1;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            margin: 0 20px;
            position: relative;
            border-radius: 2px;
            animation: dataFlow 2s linear infinite;
        }

        .communication-beam::before {
            content: '📡';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            background: white;
            padding: 5px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .planet-b {
            background: #007bff;
            color: white;
            padding: 15px 25px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 1.1rem;
            position: relative;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .planet-b::after {
            content: '星球B';
            position: absolute;
            top: -15px;
            right: -15px;
            background: #0056b3;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .prefix-highlight {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .diplomatic-meaning {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .treaty-document {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #ffc107;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #ffc107;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .connection-status {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: #6c757d;
            transition: all 0.3s ease;
        }

        .connection-status.connecting {
            background: #ffc107;
            animation: signal 1.5s infinite;
        }

        .connection-status.established {
            background: #28a745;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes dataFlow {
            0% {
                background-position: 0% 50%;
            }
            100% {
                background-position: 100% 50%;
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: translate(-50%, -50%) scale(1);
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2);
            }
        }

        @keyframes signal {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.3);
            }
        }

        @keyframes starTwinkle {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        @keyframes orbit {
            0% {
                transform: rotate(0deg) translateX(50px) rotate(0deg);
            }
            100% {
                transform: rotate(360deg) translateX(50px) rotate(-360deg);
            }
        }

        .interactive-hint {
            text-align: center;
            color: #667eea;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .cosmic-particles {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #ffffff;
            border-radius: 50%;
            pointer-events: none;
            animation: starTwinkle 3s infinite;
        }

        .alliance-network {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 15px;
        }

        .network-node {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: radial-gradient(circle, #e3f2fd, #2196f3);
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }

        .network-node.active {
            transform: scale(1.3);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.6);
            animation: pulse 1s infinite;
        }

        .network-node.connected {
            background: radial-gradient(circle, #c8e6c9, #4caf50);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .network-node::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #2196f3, transparent);
            transform: translateY(-50%);
        }

        .network-node:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>星际前缀：inter-</h1>
            <p>在星际外交联盟中学会"相互连接"的智慧</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🌌 星际外交联盟的故事</h2>
                <p>在遥远的银河系中，有一个神奇的星际外交联盟，专门负责连接不同的星球和文明。联盟的核心技术是"inter-"通信系统，它能在任何两个或多个星球之间建立联系。当普通的词汇通过这个系统时，就会获得"相互"、"在...之间"的神奇能力，让原本独立的概念变成相互连接、相互作用的新概念！</p>
            </div>

            <div class="canvas-container">
                <canvas id="galaxyCanvas"></canvas>
                <div class="alliance-network" id="allianceNetwork">
                    <div class="network-node"></div>
                    <div class="network-node"></div>
                    <div class="network-node"></div>
                    <div class="network-node"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择星际外交联盟的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"星际外交联盟"的比喻，是因为"inter-"前缀的核心含义就是"在...之间"、"相互"，这与星际联盟连接不同星球、促进相互交流的功能完美契合。通信系统的视觉效果帮助学生理解"连接"、"相互作用"的概念，而外交联盟的设定强调了不同实体之间的协作关系。通过星球间的通信桥梁，让抽象的"相互"概念变得生动有趣。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="establishConnection()">建立联盟</button>
                <button class="btn" onclick="showEmbassies()">显示使馆</button>
                <button class="btn" onclick="resetGalaxy()">重置银河</button>
            </div>

            <div class="interactive-hint">
                🚀 点击"建立联盟"观看星际连接过程，点击使馆站查看外交条约
            </div>
        </div>

        <div class="diplomatic-council" id="diplomaticCouncil">
            <div class="embassy-station">
                <div class="connection-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">National → International</h3>
                <div class="diplomatic-bridge">
                    <div class="planet-a">nation</div>
                    <div class="communication-beam"></div>
                    <div class="planet-b"><span class="prefix-highlight">inter</span>nation</div>
                </div>
                <div class="diplomatic-meaning">
                    国家的 → <span class="prefix-highlight">国际间</span>的
                </div>
                <div class="treaty-document">
                    <strong>外交条约：</strong><br>
                    <strong>单一：</strong>This is a national issue. (这是一个国家问题。)<br>
                    <strong>相互：</strong>This is an international issue. (这是一个国际问题。)<br>
                    <strong>解析：</strong>"national"表示国家的，加上"inter-"变成"international"，表示国际间的、涉及多个国家之间的。从单一国家扩展到多国相互关系。
                </div>
            </div>

            <div class="embassy-station">
                <div class="connection-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Act → Interact</h3>
                <div class="diplomatic-bridge">
                    <div class="planet-a">act</div>
                    <div class="communication-beam"></div>
                    <div class="planet-b"><span class="prefix-highlight">inter</span>act</div>
                </div>
                <div class="diplomatic-meaning">
                    行动 → <span class="prefix-highlight">相互</span>作用
                </div>
                <div class="treaty-document">
                    <strong>外交条约：</strong><br>
                    <strong>单一：</strong>He acts alone. (他独自行动。)<br>
                    <strong>相互：</strong>They interact with each other. (他们相互交流。)<br>
                    <strong>解析：</strong>"act"表示行动，加上"inter-"变成"interact"，表示相互作用、相互交流。从单方面行动变成双方或多方的相互作用。
                </div>
            </div>

            <div class="embassy-station">
                <div class="connection-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">View → Interview</h3>
                <div class="diplomatic-bridge">
                    <div class="planet-a">view</div>
                    <div class="communication-beam"></div>
                    <div class="planet-b"><span class="prefix-highlight">inter</span>view</div>
                </div>
                <div class="diplomatic-meaning">
                    观看 → <span class="prefix-highlight">相互</span>观察
                </div>
                <div class="treaty-document">
                    <strong>外交条约：</strong><br>
                    <strong>单一：</strong>I view the document. (我查看文件。)<br>
                    <strong>相互：</strong>The interview went well. (面试进行得很顺利。)<br>
                    <strong>解析：</strong>"view"表示观看，加上"inter-"变成"interview"，表示面试、访谈。从单方面观看变成双方相互了解的过程。
                </div>
            </div>

            <div class="embassy-station">
                <div class="connection-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Net → Internet</h3>
                <div class="diplomatic-bridge">
                    <div class="planet-a">net</div>
                    <div class="communication-beam"></div>
                    <div class="planet-b"><span class="prefix-highlight">inter</span>net</div>
                </div>
                <div class="diplomatic-meaning">
                    网络 → <span class="prefix-highlight">互联</span>网络
                </div>
                <div class="treaty-document">
                    <strong>外交条约：</strong><br>
                    <strong>单一：</strong>Use the local net. (使用本地网络。)<br>
                    <strong>相互：</strong>Browse the internet. (浏览互联网。)<br>
                    <strong>解析：</strong>"net"表示网络，加上"inter-"变成"internet"，表示互联网。从单一网络扩展到全球相互连接的网络系统。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"inter-"前缀表示在两者或多者之间、相互、共同的含义。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"inter-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"inter-"后的词根基本含义</li>
                <li><strong>应用相互概念：</strong>在词根意思前加上"相互"、"之间"、"共同"</li>
                <li><strong>关系调整：</strong>强调多个实体之间的相互关系或作用</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象星际外交联盟的通信系统，"inter-"就像连接不同星球的桥梁，让独立的概念相互连接！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('galaxyCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentConnection = 0;
        let stars = [];
        let planets = [];
        let communicationBeams = [];
        
        const connections = [
            { from: 'national', to: 'international', x1: 150, y1: 200, x2: 350, y2: 200 },
            { from: 'act', to: 'interact', x1: 200, y1: 300, x2: 400, y2: 300 },
            { from: 'view', to: 'interview', x1: 500, y1: 150, x2: 700, y2: 150 },
            { from: 'net', to: 'internet', x1: 550, y1: 350, x2: 750, y2: 350 }
        ];

        class Star {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.size = Math.random() * 2 + 0.5;
                this.twinkle = Math.random() * Math.PI * 2;
                this.twinkleSpeed = 0.02 + Math.random() * 0.02;
            }

            update() {
                this.twinkle += this.twinkleSpeed;
            }

            draw() {
                const alpha = 0.3 + Math.sin(this.twinkle) * 0.4;
                ctx.save();
                ctx.globalAlpha = alpha;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        class Planet {
            constructor(x, y, color, name) {
                this.x = x;
                this.y = y;
                this.color = color;
                this.name = name;
                this.size = 20;
                this.glowIntensity = 0;
            }

            update() {
                if (animationState === 'connecting') {
                    this.glowIntensity = Math.sin(Date.now() * 0.005) * 0.5 + 0.5;
                }
            }

            draw() {
                // 星球光晕
                if (this.glowIntensity > 0) {
                    ctx.save();
                    ctx.globalAlpha = this.glowIntensity * 0.3;
                    const glowGradient = ctx.createRadialGradient(this.x, this.y, this.size, this.x, this.y, this.size * 2);
                    glowGradient.addColorStop(0, this.color);
                    glowGradient.addColorStop(1, 'transparent');
                    ctx.fillStyle = glowGradient;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size * 2, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                }
                
                // 星球主体
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                
                // 星球名称
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(this.name, this.x, this.y + this.size + 15);
            }
        }

        class CommunicationBeam {
            constructor(x1, y1, x2, y2) {
                this.x1 = x1;
                this.y1 = y1;
                this.x2 = x2;
                this.y2 = y2;
                this.progress = 0;
                this.active = false;
            }

            update() {
                if (this.active && this.progress < 1) {
                    this.progress += 0.02;
                }
            }

            draw() {
                if (this.active && this.progress > 0) {
                    const currentX = this.x1 + (this.x2 - this.x1) * this.progress;
                    const currentY = this.y1 + (this.y2 - this.y1) * this.progress;
                    
                    // 通信光束
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(this.x1, this.y1);
                    ctx.lineTo(currentX, currentY);
                    ctx.stroke();
                    
                    // 数据包
                    ctx.fillStyle = '#ffc107';
                    ctx.beginPath();
                    ctx.arc(currentX, currentY, 4, 0, Math.PI * 2);
                    ctx.fill();
                }
            }

            activate() {
                this.active = true;
                this.progress = 0;
            }
        }

        function initStars() {
            stars = [];
            for (let i = 0; i < 100; i++) {
                stars.push(new Star());
            }
        }

        function initPlanets() {
            planets = [
                new Planet(150, 200, '#28a745', 'Alpha'),
                new Planet(350, 200, '#007bff', 'Beta'),
                new Planet(200, 300, '#dc3545', 'Gamma'),
                new Planet(400, 300, '#ffc107', 'Delta'),
                new Planet(500, 150, '#6f42c1', 'Epsilon'),
                new Planet(700, 150, '#20c997', 'Zeta'),
                new Planet(550, 350, '#fd7e14', 'Eta'),
                new Planet(750, 350, '#e83e8c', 'Theta')
            ];
        }

        function initCommunicationBeams() {
            communicationBeams = [];
            connections.forEach(conn => {
                communicationBeams.push(new CommunicationBeam(conn.x1, conn.y1, conn.x2, conn.y2));
            });
        }

        function drawGalaxyBackground() {
            // 绘制星空背景
            const bgGradient = ctx.createRadialGradient(canvas.width/2, canvas.height/2, 0, canvas.width/2, canvas.height/2, Math.max(canvas.width, canvas.height)/2);
            bgGradient.addColorStop(0, '#1a1a2e');
            bgGradient.addColorStop(0.5, '#16213e');
            bgGradient.addColorStop(1, '#0f0f23');
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制星星
            stars.forEach(star => {
                star.update();
                star.draw();
            });
        }

        function drawWordTransformation() {
            if (currentConnection < connections.length && animationState === 'connecting') {
                const conn = connections[currentConnection];
                
                // 显示词汇转换
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.fillRect(canvas.width/2 - 100, 50, 200, 60);
                
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(conn.from, canvas.width/2, 70);
                ctx.fillText('↓', canvas.width/2, 85);
                
                // 高亮inter-前缀
                ctx.fillStyle = '#ffc107';
                ctx.fillText('inter', canvas.width/2 - 20, 100);
                ctx.fillStyle = '#333';
                ctx.fillText(conn.from, canvas.width/2 + 20, 100);
            }
        }

        function updateAllianceNetwork() {
            const nodes = document.querySelectorAll('.network-node');
            nodes.forEach((node, index) => {
                node.classList.remove('active', 'connected');
                if (index < currentConnection) {
                    node.classList.add('connected');
                } else if (index === currentConnection && animationState === 'connecting') {
                    node.classList.add('active');
                }
            });
        }

        function updateConnectionStatus() {
            const stations = document.querySelectorAll('.embassy-station');
            const statuses = document.querySelectorAll('.connection-status');
            
            stations.forEach((station, index) => {
                const status = statuses[index];
                if (index < currentConnection) {
                    status.classList.remove('connecting');
                    status.classList.add('established');
                } else if (index === currentConnection && animationState === 'connecting') {
                    status.classList.add('connecting');
                    status.classList.remove('established');
                } else {
                    status.classList.remove('connecting', 'established');
                }
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制银河背景
            drawGalaxyBackground();
            
            // 绘制星球
            planets.forEach(planet => {
                planet.update();
                planet.draw();
            });
            
            // 绘制通信光束
            communicationBeams.forEach(beam => {
                beam.update();
                beam.draw();
            });
            
            // 绘制词汇转换
            drawWordTransformation();
            
            // 更新界面状态
            updateAllianceNetwork();
            updateConnectionStatus();
        }

        function animate() {
            drawScene();
            
            if (animationState === 'connecting' && currentConnection < connections.length) {
                // 激活当前通信光束
                if (communicationBeams[currentConnection]) {
                    communicationBeams[currentConnection].activate();
                }
                
                // 自动切换到下一个连接
                setTimeout(() => {
                    currentConnection++;
                    if (currentConnection >= connections.length) {
                        animationState = 'completed';
                    }
                }, 3000);
            }
            
            requestAnimationFrame(animate);
        }

        function establishConnection() {
            animationState = 'connecting';
            currentConnection = 0;
            initCommunicationBeams();
        }

        function showEmbassies() {
            const stations = document.querySelectorAll('.embassy-station');
            stations.forEach((station, index) => {
                setTimeout(() => {
                    station.classList.add('connected');
                }, index * 400);
            });
        }

        function resetGalaxy() {
            animationState = 'idle';
            currentConnection = 0;
            initCommunicationBeams();
            
            const stations = document.querySelectorAll('.embassy-station');
            stations.forEach(station => station.classList.remove('connected'));
            
            const statuses = document.querySelectorAll('.connection-status');
            statuses.forEach(status => {
                status.classList.remove('connecting', 'established');
            });
        }

        // 初始化
        initStars();
        initPlanets();
        initCommunicationBeams();
        animate();

        // 点击使馆站的交互
        document.querySelectorAll('.embassy-station').forEach(station => {
            station.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
