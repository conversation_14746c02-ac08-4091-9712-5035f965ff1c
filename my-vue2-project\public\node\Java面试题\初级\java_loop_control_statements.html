<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java循环控制语句详解</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            background: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        h1, h2, h3 {
            color: #0056b3;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        p {
            margin-bottom: 15px;
        }
        .code-block {
            background-color: #e8f0f7;
            border-left: 5px solid #0056b3;
            padding: 15px;
            margin: 20px 0;
            overflow-x: auto;
            border-radius: 5px;
        }
        .code-block pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .canvas-container {
            margin-top: 20px;
            text-align: center;
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .controls {
            margin-top: 15px;
            text-align: center;
        }
        .controls button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #0056b3;
        }
        .explanation-text {
            background-color: #e0f7fa;
            border-left: 5px solid #00bcd4;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .highlight {
            font-weight: bold;
            color: #d84315;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Java循环控制语句详解</h1>

        <div class="explanation-text">
            <p>欢迎来到Java循环控制语句的学习页面！在这里，我们将通过清晰的解释、动画演示和交互实践，帮助您零基础掌握Java中`break`、`continue`和`return`的区别与用法，以及如何巧妙地跳出多重嵌套循环。</p>
            <p><strong>目标：</strong> 让您彻底理解这些语句的工作原理，并能在实际编程中灵活运用。</p>
        </div>

        <h2>1. `break` 的区别与作用</h2>
        <p>`break` 语句的主要作用是<strong>跳出当前所在的循环体或 `switch` 语句</strong>。一旦执行到 `break`，程序会立即终止当前循环的执行，并从循环的下一条语句开始继续执行。</p>
        <div class="code-block">
            <pre><code>
for (int i = 0; i < 5; i++) {
    if (i == 3) {
        break; // 当 i 等于 3 时，跳出循环
    }
    System.out.println("当前 i 的值是: " + i);
}
System.out.println("循环结束后...");
            </code></pre>
        </div>
        <div class="canvas-container">
            <canvas id="breakCanvas" width="500" height="200"></canvas>
            <div class="controls">
                <button onclick="startBreakDemo()">开始演示 break</button>
            </div>
            <p id="breakExplanation" class="explanation-text"></p>
        </div>

        <h2>2. `continue` 的区别与作用</h2>
        <p>`continue` 语句的作用是<strong>跳过当前循环的剩余部分，然后继续下一次循环的执行</strong>。当程序执行到 `continue` 时，会立即终止当前循环体中 `continue` 语句之后的所有代码，直接进入下一次循环条件的判断。</p>
        <div class="code-block">
            <pre><code>
for (int i = 0; i < 5; i++) {
    if (i == 2) {
        continue; // 当 i 等于 2 时，跳过本次循环的剩余代码
    }
    System.out.println("当前 i 的值是: " + i);
}
System.out.println("循环结束后...");
            </code></pre>
        </div>
        <div class="canvas-container">
            <canvas id="continueCanvas" width="500" height="200"></canvas>
            <div class="controls">
                <button onclick="startContinueDemo()">开始演示 continue</button>
            </div>
            <p id="continueExplanation" class="explanation-text"></p>
        </div>

        <h2>3. `return` 的区别与作用</h2>
        <p>`return` 语句的作用是<strong>结束当前方法的执行，并返回到调用该方法的地方</strong>。如果方法有返回值，`return` 语句后面需要跟着要返回的值；如果没有返回值（`void` 方法），`return` 语句可以单独使用，也可以省略。</p>
        <div class="code-block">
            <pre><code>
public class Example {
    public static void checkNumber(int num) {
        if (num < 0) {
            System.out.println("数字是负数，方法结束。");
            return; // 负数时，立即结束方法
        }
        System.out.println("数字是正数或零: " + num);
    }

    public static void main(String[] args) {
        checkNumber(-5);
        System.out.println("调用 checkNumber(-5) 后...");
        checkNumber(10);
        System.out.println("调用 checkNumber(10) 后...");
    }
}
            </code></pre>
        </div>
        <div class="canvas-container">
            <canvas id="returnCanvas" width="500" height="200"></canvas>
            <div class="controls">
                <button onclick="startReturnDemo()">开始演示 return</button>
            </div>
            <p id="returnExplanation" class="explanation-text"></p>
        </div>

        <h2>4. 如何跳出当前的多重嵌套循环</h2>
        <p>在Java中，如果想跳出多重循环，可以使用<strong>带有标签（Label）的 `break` 语句</strong>。您可以在外部循环语句前定义一个标签，然后在内层循环体中使用带有该标签的 `break` 语句，即可跳出整个被标签标记的循环结构。</p>
        <div class="code-block">
            <pre><code>
public class NestedLoopBreak {
    public static void main(String[] args) {
        ok: // 定义一个标签
        for (int i = 0; i < 3; i++) { // 外层循环
            for (int j = 0; j < 5; j++) { // 内层循环
                System.out.println("i=" + i + ", j=" + j);
                if (i == 1 && j == 2) {
                    System.out.println("满足条件：跳出整个 'ok' 循环！");
                    break ok; // 跳出到标记 'ok' 的外层循环
                }
            }
        }
        System.out.println("跳出多重循环后...");
    }
}
            </code></pre>
        </div>
        <div class="canvas-container">
            <canvas id="nestedLoopCanvas" width="600" height="300"></canvas>
            <div class="controls">
                <button onclick="startNestedLoopDemo()">开始演示多重循环跳出</button>
            </div>
            <p id="nestedLoopExplanation" class="explanation-text"></p>
        </div>
    </div>

    <script>
        // 定义颜色
        const COLORS = {
            loop: '#4CAF50', // 循环体颜色
            highlight: '#FFC107', // 高亮颜色
            text: '#333',
            breakEffect: '#F44336', // break效果颜色
            continueEffect: '#2196F3', // continue效果颜色
            returnEffect: '#9C27B0', // return效果颜色
            label: '#0056b3'
        };

        // ----------------- break 演示 -----------------
        const breakCanvas = document.getElementById('breakCanvas');
        const breakCtx = breakCanvas.getContext('2d');
        const breakExplanation = document.getElementById('breakExplanation');

        function drawBreakState(i, message, highlightLine = -1) {
            breakCtx.clearRect(0, 0, breakCanvas.width, breakCanvas.height);
            breakCtx.font = '16px Arial';
            breakCtx.textAlign = 'left';
            breakCtx.fillStyle = COLORS.text;

            // 绘制循环框架
            breakCtx.fillText('for (int i = 0; i < 5; i++) {', 20, 30);
            breakCtx.fillText('    if (i == 3) {', 40, 60);
            breakCtx.fillText('        break;', 60, 90);
            breakCtx.fillText('    }', 40, 120);
            breakCtx.fillText('    System.out.println("当前 i 的值是: " + i);', 40, 150);
            breakCtx.fillText('}', 20, 180);
            breakCtx.fillText('System.out.println("循环结束后...");', 20, breakCanvas.height - 20);


            // 绘制当前i的值
            breakCtx.fillStyle = COLORS.highlight;
            breakCtx.fillText(`i = ${i}`, 300, 30);

            // 高亮当前执行行
            if (highlightLine !== -1) {
                let yPos = 0;
                switch(highlightLine) {
                    case 0: yPos = 30; break; // for
                    case 1: yPos = 60; break; // if
                    case 2: yPos = 90; break; // break
                    case 3: yPos = 120; break; // }
                    case 4: yPos = 150; break; // println
                    case 5: yPos = breakCanvas.height - 20; break; // After loop
                }
                breakCtx.beginPath();
                breakCtx.rect(10, yPos - 15, breakCanvas.width - 20, 25);
                breakCtx.fillStyle = 'rgba(255, 255, 0, 0.3)';
                breakCtx.fill();
            }

            breakExplanation.innerHTML = message;
        }

        async function startBreakDemo() {
            for (let i = 0; i < 5; i++) {
                drawBreakState(i, `现在 i = ${i}。检查条件 i < 5。`, 0);
                await new Promise(resolve => setTimeout(resolve, 1000));

                drawBreakState(i, `判断 if (i == 3) 。`, 1);
                await new Promise(resolve => setTimeout(resolve, 1000));

                if (i === 3) {
                    drawBreakState(i, `<span class="highlight">条件 i == 3 成立！执行 break; 语句。这将立即跳出整个 for 循环。</span>`, 2);
                    breakCtx.fillStyle = COLORS.breakEffect;
                    breakCtx.fillRect(0, 0, breakCanvas.width, breakCanvas.height);
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    break; // 实际跳出
                } else {
                    drawBreakState(i, `条件 i == 3 不成立。`, 3);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    drawBreakState(i, `打印当前 i 的值。`, 4);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            drawBreakState('N/A', `<span class="highlight">循环结束。程序继续执行循环后面的代码。</span>`, 5);
        }

        // ----------------- continue 演示 -----------------
        const continueCanvas = document.getElementById('continueCanvas');
        const continueCtx = continueCanvas.getContext('2d');
        const continueExplanation = document.getElementById('continueExplanation');

        function drawContinueState(i, message, highlightLine = -1) {
            continueCtx.clearRect(0, 0, continueCanvas.width, continueCanvas.height);
            continueCtx.font = '16px Arial';
            continueCtx.textAlign = 'left';
            continueCtx.fillStyle = COLORS.text;

            // 绘制循环框架
            continueCtx.fillText('for (int i = 0; i < 5; i++) {', 20, 30);
            continueCtx.fillText('    if (i == 2) {', 40, 60);
            continueCtx.fillText('        continue;', 60, 90);
            continueCtx.fillText('    }', 40, 120);
            continueCtx.fillText('    System.out.println("当前 i 的值是: " + i);', 40, 150);
            continueCtx.fillText('}', 20, 180);
            continueCtx.fillText('System.out.println("循环结束后...");', 20, continueCanvas.height - 20);

            // 绘制当前i的值
            continueCtx.fillStyle = COLORS.highlight;
            continueCtx.fillText(`i = ${i}`, 300, 30);

            // 高亮当前执行行
            if (highlightLine !== -1) {
                let yPos = 0;
                switch(highlightLine) {
                    case 0: yPos = 30; break; // for
                    case 1: yPos = 60; break; // if
                    case 2: yPos = 90; break; // continue
                    case 3: yPos = 120; break; // }
                    case 4: yPos = 150; break; // println
                    case 5: yPos = continueCanvas.height - 20; break; // After loop
                }
                continueCtx.beginPath();
                continueCtx.rect(10, yPos - 15, continueCanvas.width - 20, 25);
                continueCtx.fillStyle = 'rgba(255, 255, 0, 0.3)';
                continueCtx.fill();
            }

            continueExplanation.innerHTML = message;
        }

        async function startContinueDemo() {
            for (let i = 0; i < 5; i++) {
                drawContinueState(i, `现在 i = ${i}。检查条件 i < 5。`, 0);
                await new Promise(resolve => setTimeout(resolve, 1000));

                drawContinueState(i, `判断 if (i == 2) 。`, 1);
                await new Promise(resolve => setTimeout(resolve, 1000));

                if (i === 2) {
                    drawContinueState(i, `<span class="highlight">条件 i == 2 成立！执行 continue; 语句。这将跳过本次循环中 'continue' 之后的所有代码，直接进入下一次循环。</span>`, 2);
                    continueCtx.fillStyle = COLORS.continueEffect;
                    continueCtx.fillRect(0, 0, continueCanvas.width, continueCanvas.height);
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    continue; // 实际跳过
                } else {
                    drawContinueState(i, `条件 i == 2 不成立。`, 3);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    drawContinueState(i, `打印当前 i 的值。`, 4);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            drawContinueState('N/A', `<span class="highlight">循环结束。程序继续执行循环后面的代码。</span>`, 5);
        }

        // ----------------- return 演示 -----------------
        const returnCanvas = document.getElementById('returnCanvas');
        const returnCtx = returnCanvas.getContext('2d');
        const returnExplanation = document.getElementById('returnExplanation');

        function drawReturnState(num, message, currentMethod, highlightLine = -1) {
            returnCtx.clearRect(0, 0, returnCanvas.width, returnCanvas.height);
            returnCtx.font = '16px Arial';
            returnCtx.textAlign = 'left';
            returnCtx.fillStyle = COLORS.text;

            // 绘制方法框架
            returnCtx.fillText('public static void checkNumber(int num) {', 20, 30);
            returnCtx.fillText('    if (num < 0) {', 40, 60);
            returnCtx.fillText('        System.out.println("数字是负数，方法结束。");', 60, 90);
            returnCtx.fillText('        return; // 负数时，立即结束方法', 60, 120);
            returnCtx.fillText('    }', 40, 150);
            returnCtx.fillText('    System.out.println("数字是正数或零: " + num);', 40, 180);
            returnCtx.fillText('}', 20, 210);

            returnCtx.fillText('public static void main(String[] args) {', 20, 240);
            returnCtx.fillText('    checkNumber(-5);', 40, 270);
            returnCtx.fillText('    System.out.println("调用 checkNumber(-5) 后...");', 40, 300);
            returnCtx.fillText('    checkNumber(10);', 40, 330);
            returnCtx.fillText('    System.out.println("调用 checkNumber(10) 后...");', 40, 360);
            returnCtx.fillText('}', 20, 390);

            // 绘制当前num的值和当前方法
            returnCtx.fillStyle = COLORS.highlight;
            returnCtx.fillText(`num = ${num}`, 350, 30);
            returnCtx.fillText(`当前执行: ${currentMethod}`, 350, 60);


            // 高亮当前执行行
            if (highlightLine !== -1) {
                let yPos = 0;
                switch(highlightLine) {
                    case 0: yPos = 30; break; // checkNumber method
                    case 1: yPos = 60; break; // if
                    case 2: yPos = 90; break; // println negative
                    case 3: yPos = 120; break; // return
                    case 4: yPos = 150; break; // }
                    case 5: yPos = 180; break; // println positive
                    case 6: yPos = 210; break; // checkNumber }
                    case 7: yPos = 270; break; // main: call checkNumber(-5)
                    case 8: yPos = 300; break; // main: println after -5
                    case 9: yPos = 330; break; // main: call checkNumber(10)
                    case 10: yPos = 360; break; // main: println after 10
                }
                returnCtx.beginPath();
                returnCtx.rect(10, yPos - 15, returnCanvas.width - 20, 25);
                returnCtx.fillStyle = 'rgba(255, 255, 0, 0.3)';
                returnCtx.fill();
            }

            returnExplanation.innerHTML = message;
        }

        async function startReturnDemo() {
            returnCanvas.height = 400; // 调整 canvas 高度以适应更多代码

            // 演示 num = -5
            drawReturnState(-5, `从 main 方法开始执行。`, 'main', 7);
            await new Promise(resolve => setTimeout(resolve, 1500));

            drawReturnState(-5, `调用 checkNumber(-5)。进入 checkNumber 方法。`, 'checkNumber', 0);
            await new Promise(resolve => setTimeout(resolve, 1500));

            drawReturnState(-5, `在 checkNumber 方法中，判断 if (num < 0)。`, 'checkNumber', 1);
            await new Promise(resolve => setTimeout(resolve, 1500));

            drawReturnState(-5, `<span class="highlight">num (-5) 小于 0，条件成立。打印 "数字是负数，方法结束。"</span>`, 'checkNumber', 2);
            await new Promise(resolve => setTimeout(resolve, 1500));

            drawReturnState(-5, `<span class="highlight">执行 return; 语句。这将立即结束 checkNumber 方法的执行，返回到 main 方法。</span>`, 'checkNumber', 3);
            returnCtx.fillStyle = COLORS.returnEffect;
            returnCtx.fillRect(0, 0, returnCanvas.width, returnCanvas.height);
            await new Promise(resolve => setTimeout(resolve, 2000));

            drawReturnState(-5, `返回到 main 方法。打印 "调用 checkNumber(-5) 后..."`, 'main', 8);
            await new Promise(resolve => setTimeout(resolve, 1500));

            // 演示 num = 10
            drawReturnState(10, `继续执行 main 方法。调用 checkNumber(10)。进入 checkNumber 方法。`, 'main', 9);
            await new Promise(resolve => setTimeout(resolve, 1500));

            drawReturnState(10, `在 checkNumber 方法中，判断 if (num < 0)。`, 'checkNumber', 1);
            await new Promise(resolve => setTimeout(resolve, 1500));

            drawReturnState(10, `num (10) 不小于 0，条件不成立。跳过 if 块。`, 'checkNumber', 4);
            await new Promise(resolve => setTimeout(resolve, 1500));

            drawReturnState(10, `打印 "数字是正数或零: 10"。`, 'checkNumber', 5);
            await new Promise(resolve => setTimeout(resolve, 1500));

            drawReturnState(10, `checkNumber 方法自然结束。返回到 main 方法。`, 'checkNumber', 6);
            await new Promise(resolve => setTimeout(resolve, 1500));

            drawReturnState('N/A', `<span class="highlight">所有方法执行完毕。</span>`, 'main', 10);
            await new Promise(resolve => setTimeout(resolve, 1500));
        }


        // ----------------- 嵌套循环带标签 break 演示 -----------------
        const nestedLoopCanvas = document.getElementById('nestedLoopCanvas');
        const nestedLoopCtx = nestedLoopCanvas.getContext('2d');
        const nestedLoopExplanation = document.getElementById('nestedLoopExplanation');

        function drawNestedLoopState(i, j, message, highlightLine = -1) {
            nestedLoopCtx.clearRect(0, 0, nestedLoopCanvas.width, nestedLoopCanvas.height);
            nestedLoopCtx.font = '16px Arial';
            nestedLoopCtx.textAlign = 'left';
            nestedLoopCtx.fillStyle = COLORS.text;

            // 绘制循环框架
            nestedLoopCtx.fillText('ok: // 定义一个标签', 20, 30);
            nestedLoopCtx.fillText('for (int i = 0; i < 3; i++) {', 40, 60);
            nestedLoopCtx.fillText('    for (int j = 0; j < 5; j++) {', 60, 90);
            nestedLoopCtx.fillText('        System.out.println("i=" + i + ", j=" + j);', 80, 120);
            nestedLoopCtx.fillText('        if (i == 1 && j == 2) {', 80, 150);
            nestedLoopCtx.fillText('            System.out.println("满足条件：跳出整个 \'ok\' 循环！");', 100, 180);
            nestedLoopCtx.fillText('            break ok;', 100, 210);
            nestedLoopCtx.fillText('        }', 80, 240);
            nestedLoopCtx.fillText('    }', 60, 270);
            nestedLoopCtx.fillText('}', 40, 300);
            nestedLoopCtx.fillText('System.out.println("跳出多重循环后...");', 20, nestedLoopCanvas.height - 20);

            // 绘制当前i和j的值
            nestedLoopCtx.fillStyle = COLORS.highlight;
            nestedLoopCtx.fillText(`i = ${i}, j = ${j}`, 450, 60);

            // 高亮当前执行行
            if (highlightLine !== -1) {
                let yPos = 0;
                switch(highlightLine) {
                    case 0: yPos = 30; break; // ok: label
                    case 1: yPos = 60; break; // outer loop
                    case 2: yPos = 90; break; // inner loop
                    case 3: yPos = 120; break; // println i, j
                    case 4: yPos = 150; break; // if
                    case 5: yPos = 180; break; // println break msg
                    case 6: yPos = 210; break; // break ok;
                    case 7: yPos = 240; break; // inner if }
                    case 8: yPos = 270; break; // inner loop }
                    case 9: yPos = 300; break; // outer loop }
                    case 10: yPos = nestedLoopCanvas.height - 20; break; // after all loops
                }
                nestedLoopCtx.beginPath();
                nestedLoopCtx.rect(10, yPos - 15, nestedLoopCanvas.width - 20, 25);
                nestedLoopCtx.fillStyle = 'rgba(255, 255, 0, 0.3)';
                nestedLoopCtx.fill();
            }

            nestedLoopExplanation.innerHTML = message;
        }

        async function startNestedLoopDemo() {
            nestedLoopCanvas.height = 350; // 调整高度以适应更多代码

            drawNestedLoopState('N/A', 'N/A', `开始执行多重循环。首先定义标签 <span class="highlight">ok:</span>`, 0);
            await new Promise(resolve => setTimeout(resolve, 1500));

            outerLoop:
            for (let i = 0; i < 3; i++) {
                drawNestedLoopState(i, 'N/A', `外层循环：i = ${i}。`, 1);
                await new Promise(resolve => setTimeout(resolve, 1000));

                for (let j = 0; j < 5; j++) {
                    drawNestedLoopState(i, j, `内层循环：j = ${j}。`, 2);
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    drawNestedLoopState(i, j, `打印 i = ${i}, j = ${j}。`, 3);
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    drawNestedLoopState(i, j, `判断 if (i == 1 && j == 2)。`, 4);
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    if (i === 1 && j === 2) {
                        drawNestedLoopState(i, j, `<span class="highlight">条件 i == 1 && j == 2 成立！打印 "满足条件：跳出整个 \'ok\' 循环！"</span>`, 5);
                        await new Promise(resolve => setTimeout(resolve, 1500));

                        drawNestedLoopState(i, j, `<span class="highlight">执行 break ok; 语句。这将立即跳出标记为 'ok' 的外层循环，直接到循环结构之后。</span>`, 6);
                        nestedLoopCtx.fillStyle = COLORS.breakEffect;
                        nestedLoopCtx.fillRect(0, 0, nestedLoopCanvas.width, nestedLoopCanvas.height);
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        break outerLoop; // 模拟带标签的 break
                    } else {
                        drawNestedLoopState(i, j, `条件不成立。`, 7);
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
                if (i !== 1 || j !== 2) { // 只有在没有触发 break ok 的情况下才显示内层循环结束
                    drawNestedLoopState(i, 'N/A', `内层循环 (j) 结束，外层循环继续。`, 8);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
            drawNestedLoopState('N/A', 'N/A', `<span class="highlight">成功跳出所有循环！程序继续执行多重循环后面的代码。</span>`, 10);
            await new Promise(resolve => setTimeout(resolve, 1500));
        }

        // 初始渲染
        document.addEventListener('DOMContentLoaded', () => {
            drawBreakState('?', `点击 "开始演示 break" 按钮查看效果。`);
            drawContinueState('?', `点击 "开始演示 continue" 按钮查看效果。`);
            returnCanvas.height = 400; // 调整 canvas 高度以适应更多代码
            drawReturnState('?', `点击 "开始演示 return" 按钮查看效果。`, 'main');
            nestedLoopCanvas.height = 350; // 调整高度以适应更多代码
            drawNestedLoopState('?', '?', `点击 "开始演示多重循环跳出" 按钮查看效果。`);
        });

    </script>
</body>
</html> 