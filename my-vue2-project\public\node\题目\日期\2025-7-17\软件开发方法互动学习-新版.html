<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件开发方法互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .game-board {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .blank {
            display: inline-block;
            min-width: 100px;
            height: 30px;
            border-bottom: 3px solid #667eea;
            margin: 0 5px;
            position: relative;
            animation: pulse 2s infinite;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .option-card.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: correctPulse 0.6s ease-in-out;
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            animation: shake 0.6s ease-in-out;
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        #gameCanvas {
            border: 3px solid #667eea;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .explanation-panel {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            display: none;
            animation: slideInUp 0.5s ease-out;
        }

        .method-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .method-card:hover {
            transform: scale(1.02);
        }

        .method-title {
            font-size: 1.3rem;
            color: #667eea;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .method-description {
            color: #555;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { border-color: #667eea; }
            50% { border-color: #f093fb; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .score-display {
            text-align: center;
            font-size: 1.5rem;
            color: #333;
            margin: 20px 0;
        }

        .game-instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-circle" style="width: 80px; height: 80px; top: 10%; left: 10%; animation-delay: 0s;"></div>
        <div class="floating-circle" style="width: 120px; height: 120px; top: 20%; right: 10%; animation-delay: 2s;"></div>
        <div class="floating-circle" style="width: 60px; height: 60px; bottom: 20%; left: 20%; animation-delay: 4s;"></div>
        <div class="floating-circle" style="width: 100px; height: 100px; bottom: 10%; right: 20%; animation-delay: 1s;"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">🚀 软件开发方法学习</h1>
            <p class="subtitle">通过互动游戏掌握敏捷开发方法的核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="score-display">
                得分: <span id="score">0</span> / 100
            </div>
        </div>

        <div class="game-board">
            <div class="game-instructions">
                💡 <strong>游戏说明：</strong>请仔细阅读题目，选择正确答案。点击Canvas中的开发者可以了解更多信息！
            </div>

            <div class="question-section">
                <div class="question-text">
                    <strong>题目：</strong>
                    <span class="blank" id="blank1"></span> 适用于程序开发人员在地域上分布很广的开发团队，
                    <span class="blank" id="blank2"></span> 中，程序开发人员分成首席程序员和"类"程序员。
                </div>

                <div class="options-container">
                    <div class="option-card" data-option="A">
                        <div><strong>A.</strong> 自适应软件开发（ASD）</div>
                        <small>核心：猜测、合作与学习</small>
                    </div>
                    <div class="option-card" data-option="B">
                        <div><strong>B.</strong> 极限编程（XP）开发方法</div>
                        <small>特点：高度纪律性</small>
                    </div>
                    <div class="option-card" data-option="C">
                        <div><strong>C.</strong> 开放统一过程开发方法（OpenUP）</div>
                        <small>统一过程的轻量级版本</small>
                    </div>
                    <div class="option-card" data-option="D">
                        <div><strong>D.</strong> 功用驱动开发方法（FDD）</div>
                        <small>特点：首席程序员+类程序员</small>
                    </div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation-panel" id="explanationPanel">
                <h2 style="text-align: center; margin-bottom: 30px; color: #333;">📚 知识点详解</h2>
                
                <div class="method-card">
                    <div class="method-title">🌐 开放源码方法 - 第一空答案</div>
                    <div class="method-description">
                        <span class="highlight">地域分布广泛</span>的特点使其与其他敏捷方法不同。程序开发人员可以在世界各地协作，通过互联网进行代码共享和错误修复。任何人发现错误都可将改正源码的"补丁"文件发给维护者。
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-title">🎯 功用驱动开发方法（FDD）- 第二空答案</div>
                    <div class="method-description">
                        将程序开发人员分为两类：<span class="highlight">首席程序员</span>（最富经验的协调者、设计者、指导者）和<span class="highlight">"类"程序员</span>（主要负责源码编写）。迭代周期通常为两周。
                    </div>
                </div>

                <div class="method-card">
                    <div class="method-title">⚡ 其他开发方法对比</div>
                    <div class="method-description">
                        <strong>极限编程（XP）：</strong>最引人瞩目的敏捷方法，强调高度纪律性<br>
                        <strong>自适应软件开发（ASD）：</strong>三个阶段：猜测、合作与学习<br>
                        <strong>SCRUM：</strong>强调明确定义的可重复方法过程
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const explanationPanel = document.getElementById('explanationPanel');
        const progressFill = document.getElementById('progressFill');
        const scoreElement = document.getElementById('score');
        
        let gameState = {
            score: 0,
            answered: false,
            correctAnswers: ['开放源码', 'FDD'], // 正确答案
            currentStep: 0
        };
        
        let animationFrame;
        let particles = [];
        let developers = [];
        let connections = [];
        
        // 初始化游戏
        function initGame() {
            createDevelopers();
            createParticles();
            animate();
        }
        
        // 创建开发者角色
        function createDevelopers() {
            developers = [
                { x: 150, y: 200, type: 'distributed', name: '远程开发者1', color: '#4CAF50', region: '北京' },
                { x: 250, y: 150, type: 'distributed', name: '远程开发者2', color: '#4CAF50', region: '上海' },
                { x: 350, y: 250, type: 'distributed', name: '远程开发者3', color: '#4CAF50', region: '深圳' },
                { x: 450, y: 180, type: 'distributed', name: '远程开发者4', color: '#4CAF50', region: '美国' },
                { x: 550, y: 220, type: 'distributed', name: '远程开发者5', color: '#4CAF50', region: '欧洲' },
                
                { x: 100, y: 350, type: 'chief', name: '首席程序员', color: '#667eea', role: '协调、设计、指导' },
                { x: 200, y: 320, type: 'class', name: '类程序员1', color: '#764ba2', role: '源码编写' },
                { x: 300, y: 350, type: 'class', name: '类程序员2', color: '#764ba2', role: '源码编写' },
                { x: 400, y: 320, type: 'class', name: '类程序员3', color: '#764ba2', role: '源码编写' }
            ];
        }
        
        // 创建粒子效果
        function createParticles() {
            particles = [];
            for (let i = 0; i < 30; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 1,
                    vy: (Math.random() - 0.5) * 1,
                    size: Math.random() * 2 + 1,
                    opacity: Math.random() * 0.3 + 0.1
                });
            }
        }
        
        // 绘制开发者
        function drawDeveloper(dev) {
            const size = dev.type === 'chief' ? 35 : (dev.type === 'class' ? 25 : 20);
            
            // 绘制开发者圆圈
            ctx.beginPath();
            ctx.arc(dev.x, dev.y, size, 0, Math.PI * 2);
            ctx.fillStyle = dev.color;
            ctx.fill();
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制图标
            ctx.fillStyle = 'white';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            let icon = '👨‍💻';
            if (dev.type === 'chief') icon = '👑';
            else if (dev.type === 'distributed') icon = '🌐';
            ctx.fillText(icon, dev.x, dev.y);
            
            // 绘制名称
            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.fillText(dev.name, dev.x, dev.y + size + 12);
            
            // 绘制地区/角色信息
            if (dev.region) {
                ctx.fillStyle = '#666';
                ctx.font = '8px Arial';
                ctx.fillText(dev.region, dev.x, dev.y + size + 22);
            }
        }
        
        // 绘制连接线
        function drawConnections() {
            // 绘制分布式开发者之间的连接
            const distributedDevs = developers.filter(d => d.type === 'distributed');
            ctx.strokeStyle = 'rgba(76, 175, 80, 0.3)';
            ctx.lineWidth = 1;
            
            for (let i = 0; i < distributedDevs.length; i++) {
                for (let j = i + 1; j < distributedDevs.length; j++) {
                    ctx.beginPath();
                    ctx.moveTo(distributedDevs[i].x, distributedDevs[i].y);
                    ctx.lineTo(distributedDevs[j].x, distributedDevs[j].y);
                    ctx.stroke();
                }
            }
            
            // 绘制FDD团队结构
            const chief = developers.find(d => d.type === 'chief');
            const classDevs = developers.filter(d => d.type === 'class');
            
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.5)';
            ctx.lineWidth = 2;
            
            classDevs.forEach(dev => {
                ctx.beginPath();
                ctx.moveTo(chief.x, chief.y);
                ctx.lineTo(dev.x, dev.y);
                ctx.stroke();
            });
        }
        
        // 绘制粒子
        function drawParticles() {
            particles.forEach(particle => {
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
                ctx.fill();
                
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            });
        }
        
        // 动画循环
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, 'rgba(102, 126, 234, 0.05)');
            gradient.addColorStop(1, 'rgba(118, 75, 162, 0.05)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            drawParticles();
            drawConnections();
            developers.forEach(drawDeveloper);
            
            // 绘制标题和说明
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('软件开发团队结构对比', canvas.width / 2, 25);
            
            ctx.font = '12px Arial';
            ctx.fillText('上方：开放源码（地域分布广）  下方：FDD（首席+类程序员）', canvas.width / 2, 45);
            
            // 绘制分割线
            ctx.strokeStyle = 'rgba(0,0,0,0.2)';
            ctx.lineWidth = 1;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(0, 280);
            ctx.lineTo(canvas.width, 280);
            ctx.stroke();
            ctx.setLineDash([]);
            
            animationFrame = requestAnimationFrame(animate);
        }
        
        // 选项点击事件
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('click', function() {
                if (gameState.answered) return;
                
                const option = this.dataset.option;
                
                // 重置所有选项
                document.querySelectorAll('.option-card').forEach(c => {
                    c.classList.remove('correct', 'wrong');
                });
                
                // 检查答案（这道题的正确答案应该是开放源码方法和FDD）
                // 根据题目，第一空是开放源码方法，第二空是FDD
                let isCorrect = false;
                
                if (option === 'D') { // FDD包含了"类程序员"的概念
                    isCorrect = true;
                    this.classList.add('correct');
                    gameState.score = 100;
                    updateScore();
                    fillBlanks();
                    showExplanation();
                    createSuccessEffect();
                } else {
                    this.classList.add('wrong');
                    // 显示正确答案
                    setTimeout(() => {
                        document.querySelector('[data-option="D"]').classList.add('correct');
                        fillBlanks();
                        showExplanation();
                    }, 1000);
                }
                
                gameState.answered = true;
            });
        });
        
        // 填充空白
        function fillBlanks() {
            document.getElementById('blank1').textContent = '开放源码方法';
            document.getElementById('blank2').textContent = 'FDD';
        }
        
        // 显示解释面板
        function showExplanation() {
            explanationPanel.style.display = 'block';
            explanationPanel.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 更新分数
        function updateScore() {
            scoreElement.textContent = gameState.score;
            progressFill.style.width = gameState.score + '%';
        }
        
        // 成功效果
        function createSuccessEffect() {
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const particle = document.createElement('div');
                    particle.style.cssText = `
                        position: fixed;
                        left: ${Math.random() * window.innerWidth}px;
                        top: ${window.innerHeight}px;
                        width: 8px;
                        height: 8px;
                        background: #4CAF50;
                        border-radius: 50%;
                        pointer-events: none;
                        z-index: 1000;
                        animation: float 3s linear forwards;
                    `;
                    document.body.appendChild(particle);
                    setTimeout(() => particle.remove(), 3000);
                }, i * 50);
            }
        }
        
        // Canvas点击事件
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;
            
            developers.forEach(dev => {
                const distance = Math.sqrt((mouseX - dev.x) ** 2 + (mouseY - dev.y) ** 2);
                const size = dev.type === 'chief' ? 35 : (dev.type === 'class' ? 25 : 20);
                
                if (distance < size) {
                    showDeveloperInfo(dev);
                }
            });
        });
        
        // 显示开发者信息
        function showDeveloperInfo(dev) {
            // 创建信息弹窗
            const infoPanel = document.createElement('div');
            infoPanel.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 30px;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                z-index: 1000;
                max-width: 400px;
                text-align: center;
                animation: fadeInUp 0.3s ease-out;
            `;

            let content = `
                <h3 style="color: ${dev.color}; margin-bottom: 15px;">${dev.name}</h3>
            `;

            if (dev.region) {
                content += `<p><strong>地区:</strong> ${dev.region}</p>`;
            }
            if (dev.role) {
                content += `<p><strong>职责:</strong> ${dev.role}</p>`;
            }

            switch(dev.type) {
                case 'distributed':
                    content += `
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 15px 0;">
                            <h4>🌐 开放源码特点</h4>
                            <p>• 地域分布广泛，通过网络协作</p>
                            <p>• 高度并行的查错排障</p>
                            <p>• 任何人都可以提交补丁</p>
                        </div>
                    `;
                    break;
                case 'chief':
                    content += `
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; margin: 15px 0;">
                            <h4>👑 首席程序员</h4>
                            <p>• 最富经验的开发人员</p>
                            <p>• 项目协调者和设计者</p>
                            <p>• 指导其他开发人员</p>
                        </div>
                    `;
                    break;
                case 'class':
                    content += `
                        <div style="background: #f3e5f5; padding: 15px; border-radius: 10px; margin: 15px 0;">
                            <h4>👨‍💻 类程序员</h4>
                            <p>• 主要负责源码编写</p>
                            <p>• 在首席程序员指导下工作</p>
                            <p>• 专注于具体实现</p>
                        </div>
                    `;
                    break;
            }

            content += `
                <button onclick="this.parentElement.remove()"
                        style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                               color: white; border: none; padding: 10px 20px;
                               border-radius: 25px; cursor: pointer; margin-top: 15px;">
                    关闭
                </button>
            `;

            infoPanel.innerHTML = content;
            document.body.appendChild(infoPanel);

            // 3秒后自动关闭
            setTimeout(() => {
                if (infoPanel.parentElement) {
                    infoPanel.remove();
                }
            }, 5000);
        }

        // 添加知识点测试小游戏
        function startMiniGame() {
            const questions = [
                {
                    question: "FDD中的迭代周期通常是多长时间？",
                    options: ["一周", "两周", "一个月", "三个月"],
                    correct: 1,
                    explanation: "FDD的迭代周期一般是两周，这样可以快速交付可见可用的功能。"
                },
                {
                    question: "开放源码开发的突出特点是什么？",
                    options: ["高度纪律性", "地域分布广", "严格流程", "集中管理"],
                    correct: 1,
                    explanation: "开放源码开发的突出特点是程序开发人员在地域上分布很广，通过网络进行协作。"
                }
            ];

            // 这里可以添加更多的小游戏逻辑
            console.log("小游戏功能待开发...");
        }

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case '1':
                    document.querySelector('[data-option="A"]').click();
                    break;
                case '2':
                    document.querySelector('[data-option="B"]').click();
                    break;
                case '3':
                    document.querySelector('[data-option="C"]').click();
                    break;
                case '4':
                    document.querySelector('[data-option="D"]').click();
                    break;
                case 'r':
                    location.reload();
                    break;
            }
        });

        // 添加提示功能
        function showHint() {
            const hint = document.createElement('div');
            hint.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 15px;
                border-radius: 10px;
                max-width: 300px;
                z-index: 1000;
                animation: slideInRight 0.3s ease-out;
            `;

            hint.innerHTML = `
                <h4>💡 解题提示</h4>
                <p>• 第一空：哪种方法适合地域分布广的团队？</p>
                <p>• 第二空：哪种方法有"首席程序员"和"类程序员"？</p>
                <p>• 快捷键：1-4选择选项，R重新开始</p>
                <button onclick="this.parentElement.remove()"
                        style="background: #667eea; color: white; border: none;
                               padding: 5px 10px; border-radius: 5px; cursor: pointer; margin-top: 10px;">
                    关闭
                </button>
            `;

            document.body.appendChild(hint);

            setTimeout(() => {
                if (hint.parentElement) {
                    hint.remove();
                }
            }, 8000);
        }

        // 添加提示按钮
        const hintButton = document.createElement('button');
        hintButton.innerHTML = '💡 提示';
        hintButton.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            z-index: 100;
            font-weight: bold;
        `;
        hintButton.onclick = showHint;
        document.body.appendChild(hintButton);

        // 初始化
        initGame();
        updateScore();

        // 添加欢迎动画
        setTimeout(() => {
            const welcome = document.createElement('div');
            welcome.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 20px;
                text-align: center;
                z-index: 1000;
                animation: fadeInUp 0.5s ease-out;
            `;

            welcome.innerHTML = `
                <h2>🎉 欢迎来到软件开发方法学习！</h2>
                <p>点击Canvas中的开发者了解更多信息</p>
                <p>使用键盘1-4快速选择答案</p>
                <button onclick="this.parentElement.remove()"
                        style="background: white; color: #667eea; border: none;
                               padding: 10px 20px; border-radius: 25px; cursor: pointer;
                               margin-top: 15px; font-weight: bold;">
                    开始学习
                </button>
            `;

            document.body.appendChild(welcome);
        }, 1000);
    </script>
</body>
</html>
