<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java集合框架 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .learning-section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 50px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 300;
        }

        .hierarchy-canvas {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 16px;
            background: #f8f9fa;
            margin: 30px 0;
            cursor: pointer;
        }

        .interactive-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .demo-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .demo-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .demo-card h3 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .demo-card p {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .demo-canvas {
            width: 100%;
            height: 200px;
            border-radius: 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin: 30px 0;
            text-align: center;
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 30px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00c9ff, #92fe9d);
            width: 0%;
            transition: width 1s ease;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .animated-element {
            animation: pulse 2s infinite;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .game-score {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px 25px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            font-weight: 500;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="game-score">
        学习进度: <span id="score">0</span>/100
    </div>

    <div class="container">
        <div class="header">
            <h1>Java集合框架</h1>
            <p>通过动画和交互学习Java集合的奥秘</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">集合框架层次结构</h2>
            <div class="explanation">
                <p>Java集合框架就像一个大家族，有两个主要的祖先：<strong>Collection接口</strong> 和 <strong>Map接口</strong></p>
                <p>点击下面的图表来探索这个家族树！</p>
            </div>
            <canvas id="hierarchyCanvas" class="hierarchy-canvas"></canvas>
        </div>

        <div class="learning-section">
            <h2 class="section-title">交互式集合演示</h2>
            <div class="interactive-demo">
                <div class="demo-card" onclick="showListDemo()">
                    <h3>📋 List接口</h3>
                    <p>有序集合，允许重复元素，可以通过索引访问</p>
                    <canvas id="listCanvas" class="demo-canvas"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="addToList()">添加元素</button>
                        <button class="btn" onclick="removeFromList()">删除元素</button>
                        <button class="btn" onclick="clearList()">清空</button>
                    </div>
                </div>

                <div class="demo-card" onclick="showSetDemo()">
                    <h3>🎯 Set接口</h3>
                    <p>不允许重复元素的集合，确保元素唯一性</p>
                    <canvas id="setCanvas" class="demo-canvas"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="addToSet()">添加元素</button>
                        <button class="btn" onclick="removeFromSet()">删除元素</button>
                        <button class="btn" onclick="clearSet()">清空</button>
                    </div>
                </div>

                <div class="demo-card" onclick="showMapDemo()">
                    <h3>🗝️ Map接口</h3>
                    <p>键值对存储，每个键对应一个值</p>
                    <canvas id="mapCanvas" class="demo-canvas"></canvas>
                    <div class="controls">
                        <button class="btn" onclick="addToMap()">添加键值对</button>
                        <button class="btn" onclick="removeFromMap()">删除</button>
                        <button class="btn" onclick="clearMap()">清空</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // 全局变量
        let score = 0;
        let currentStep = 0;
        const totalSteps = 10;
        
        // 数据结构
        let listData = [];
        let setData = new Set();
        let mapData = new Map();

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initHierarchyCanvas();
            initDemoCanvases();
            updateProgress();
        });

        // 更新进度
        function updateProgress() {
            const progressPercent = (score / 100) * 100;
            document.getElementById('progressFill').style.width = progressPercent + '%';
            document.getElementById('score').textContent = score;
        }

        // 增加分数
        function addScore(points) {
            score = Math.min(100, score + points);
            updateProgress();
            
            if (score >= 100) {
                setTimeout(() => {
                    alert('🎉 恭喜！您已经完全掌握了Java集合框架的基础知识！');
                }, 500);
            }
        }

        // 初始化层次结构画布
        function initHierarchyCanvas() {
            const canvas = document.getElementById('hierarchyCanvas');
            const ctx = canvas.getContext('2d');

            // 设置画布尺寸
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            drawHierarchy(ctx, canvas.width, canvas.height);

            // 添加点击事件
            canvas.addEventListener('click', function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                handleHierarchyClick(x, y, canvas.width, canvas.height);
            });
        }

        // 绘制层次结构
        function drawHierarchy(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            // 设置字体
            ctx.font = '16px SF Pro Display, sans-serif';
            ctx.textAlign = 'center';

            // 定义节点位置
            const nodes = {
                // 顶层接口
                collection: { x: width * 0.3, y: height * 0.15, text: 'Collection接口', color: '#3498db' },
                map: { x: width * 0.7, y: height * 0.15, text: 'Map接口', color: '#e74c3c' },

                // Collection的子接口
                list: { x: width * 0.15, y: height * 0.35, text: 'List接口', color: '#2ecc71' },
                set: { x: width * 0.45, y: height * 0.35, text: 'Set接口', color: '#f39c12' },

                // List实现类
                arrayList: { x: width * 0.05, y: height * 0.55, text: 'ArrayList', color: '#27ae60' },
                linkedList: { x: width * 0.15, y: height * 0.55, text: 'LinkedList', color: '#27ae60' },
                vector: { x: width * 0.25, y: height * 0.55, text: 'Vector', color: '#27ae60' },

                // Set实现类
                hashSet: { x: width * 0.35, y: height * 0.55, text: 'HashSet', color: '#e67e22' },
                treeSet: { x: width * 0.45, y: height * 0.55, text: 'TreeSet', color: '#e67e22' },
                linkedHashSet: { x: width * 0.55, y: height * 0.55, text: 'LinkedHashSet', color: '#e67e22' },

                // Map实现类
                hashMap: { x: width * 0.65, y: height * 0.35, text: 'HashMap', color: '#c0392b' },
                treeMap: { x: width * 0.75, y: height * 0.35, text: 'TreeMap', color: '#c0392b' },
                hashtable: { x: width * 0.85, y: height * 0.35, text: 'Hashtable', color: '#c0392b' },
                concurrentHashMap: { x: width * 0.7, y: height * 0.55, text: 'ConcurrentHashMap', color: '#c0392b' }
            };

            // 绘制连接线
            ctx.strokeStyle = '#bdc3c7';
            ctx.lineWidth = 2;

            // Collection到子接口的连线
            drawLine(ctx, nodes.collection, nodes.list);
            drawLine(ctx, nodes.collection, nodes.set);

            // List到实现类的连线
            drawLine(ctx, nodes.list, nodes.arrayList);
            drawLine(ctx, nodes.list, nodes.linkedList);
            drawLine(ctx, nodes.list, nodes.vector);

            // Set到实现类的连线
            drawLine(ctx, nodes.set, nodes.hashSet);
            drawLine(ctx, nodes.set, nodes.treeSet);
            drawLine(ctx, nodes.set, nodes.linkedHashSet);

            // Map到实现类的连线
            drawLine(ctx, nodes.map, nodes.hashMap);
            drawLine(ctx, nodes.map, nodes.treeMap);
            drawLine(ctx, nodes.map, nodes.hashtable);
            drawLine(ctx, nodes.map, nodes.concurrentHashMap);

            // 绘制节点
            Object.values(nodes).forEach(node => {
                drawNode(ctx, node);
            });

            // 添加动画效果
            animateNodes(ctx, nodes, width, height);
        }

        // 绘制节点
        function drawNode(ctx, node) {
            const radius = 8;

            // 绘制节点圆圈
            ctx.beginPath();
            ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI);
            ctx.fillStyle = node.color;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.stroke();

            // 绘制文字
            ctx.fillStyle = '#2c3e50';
            ctx.fillText(node.text, node.x, node.y + 25);
        }

        // 绘制连接线
        function drawLine(ctx, from, to) {
            ctx.beginPath();
            ctx.moveTo(from.x, from.y);
            ctx.lineTo(to.x, to.y);
            ctx.stroke();
        }

        // 节点动画
        function animateNodes(ctx, nodes, width, height) {
            let animationFrame = 0;

            function animate() {
                // 重绘背景
                ctx.clearRect(0, 0, width, height);

                // 重绘连接线
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 2;

                // 绘制所有连接线（简化版）
                const connections = [
                    [nodes.collection, nodes.list],
                    [nodes.collection, nodes.set],
                    [nodes.list, nodes.arrayList],
                    [nodes.list, nodes.linkedList],
                    [nodes.list, nodes.vector],
                    [nodes.set, nodes.hashSet],
                    [nodes.set, nodes.treeSet],
                    [nodes.set, nodes.linkedHashSet],
                    [nodes.map, nodes.hashMap],
                    [nodes.map, nodes.treeMap],
                    [nodes.map, nodes.hashtable],
                    [nodes.map, nodes.concurrentHashMap]
                ];

                connections.forEach(([from, to]) => {
                    drawLine(ctx, from, to);
                });

                // 绘制带动画效果的节点
                Object.values(nodes).forEach((node, index) => {
                    const scale = 1 + 0.1 * Math.sin(animationFrame * 0.02 + index * 0.5);
                    const originalRadius = 8;
                    const radius = originalRadius * scale;

                    // 绘制节点圆圈
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI);
                    ctx.fillStyle = node.color;
                    ctx.fill();
                    ctx.strokeStyle = '#fff';
                    ctx.lineWidth = 3;
                    ctx.stroke();

                    // 绘制文字
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '16px SF Pro Display, sans-serif';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.text, node.x, node.y + 25);
                });

                animationFrame++;
                requestAnimationFrame(animate);
            }

            animate();
        }

        // 处理层次结构点击
        function handleHierarchyClick(x, y, width, height) {
            // 简化的点击检测
            const clickRadius = 30;

            // 检查点击的是哪个节点
            const nodes = [
                { x: width * 0.3, y: height * 0.15, name: 'Collection接口' },
                { x: width * 0.7, y: height * 0.15, name: 'Map接口' },
                { x: width * 0.15, y: height * 0.35, name: 'List接口' },
                { x: width * 0.45, y: height * 0.35, name: 'Set接口' }
            ];

            nodes.forEach(node => {
                const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
                if (distance < clickRadius) {
                    showNodeExplanation(node.name);
                    addScore(10);
                }
            });
        }

        // 显示节点说明
        function showNodeExplanation(nodeName) {
            const explanations = {
                'Collection接口': '📚 Collection是所有单值集合的根接口，定义了集合的基本操作方法。',
                'Map接口': '🗝️ Map接口用于存储键值对，每个键对应一个值，键不能重复。',
                'List接口': '📋 List是有序集合，允许重复元素，可以通过索引访问元素。',
                'Set接口': '🎯 Set是不允许重复元素的集合，确保集合中每个元素都是唯一的。'
            };

            alert(explanations[nodeName] || '点击了未知节点');
        }

        // 初始化演示画布
        function initDemoCanvases() {
            initListCanvas();
            initSetCanvas();
            initMapCanvas();
        }

        // 初始化List演示画布
        function initListCanvas() {
            const canvas = document.getElementById('listCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            drawListDemo(ctx, canvas.width, canvas.height);
        }

        // 绘制List演示
        function drawListDemo(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            // 绘制容器
            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 3;
            ctx.strokeRect(20, 20, width - 40, height - 40);

            // 绘制索引标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px SF Pro Display, sans-serif';
            ctx.textAlign = 'center';

            // 绘制List元素
            const cellWidth = (width - 80) / Math.max(listData.length, 5);
            listData.forEach((item, index) => {
                const x = 40 + index * cellWidth;
                const y = 40;
                const cellHeight = height - 80;

                // 绘制单元格
                ctx.fillStyle = '#ecf0f1';
                ctx.fillRect(x, y, cellWidth - 5, cellHeight);

                // 绘制边框
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 1;
                ctx.strokeRect(x, y, cellWidth - 5, cellHeight);

                // 绘制索引
                ctx.fillStyle = '#e74c3c';
                ctx.fillText(index.toString(), x + cellWidth/2 - 2.5, y - 5);

                // 绘制元素值
                ctx.fillStyle = '#2c3e50';
                ctx.fillText(item.toString(), x + cellWidth/2 - 2.5, y + cellHeight/2 + 5);
            });

            // 如果列表为空，显示提示
            if (listData.length === 0) {
                ctx.fillStyle = '#95a5a6';
                ctx.font = '16px SF Pro Display, sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText('点击"添加元素"开始体验List！', width/2, height/2);
            }
        }

        // 初始化Set演示画布
        function initSetCanvas() {
            const canvas = document.getElementById('setCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            drawSetDemo(ctx, canvas.width, canvas.height);
        }

        // 绘制Set演示
        function drawSetDemo(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            // 绘制圆形容器
            const centerX = width / 2;
            const centerY = height / 2;
            const radius = Math.min(width, height) / 2 - 30;

            ctx.strokeStyle = '#f39c12';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.stroke();

            // 绘制Set元素（圆形分布）
            const setArray = Array.from(setData);
            setArray.forEach((item, index) => {
                const angle = (2 * Math.PI * index) / setArray.length;
                const x = centerX + Math.cos(angle) * (radius - 40);
                const y = centerY + Math.sin(angle) * (radius - 40);

                // 绘制元素圆圈
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, 2 * Math.PI);
                ctx.fillStyle = '#f1c40f';
                ctx.fill();
                ctx.strokeStyle = '#f39c12';
                ctx.lineWidth = 2;
                ctx.stroke();

                // 绘制元素值
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px SF Pro Display, sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText(item.toString(), x, y + 5);
            });

            // 如果集合为空，显示提示
            if (setData.size === 0) {
                ctx.fillStyle = '#95a5a6';
                ctx.font = '16px SF Pro Display, sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText('点击"添加元素"开始体验Set！', centerX, centerY);
                ctx.fillText('Set不允许重复元素', centerX, centerY + 25);
            }
        }

        // 初始化Map演示画布
        function initMapCanvas() {
            const canvas = document.getElementById('mapCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            drawMapDemo(ctx, canvas.width, canvas.height);
        }

        // 绘制Map演示
        function drawMapDemo(ctx, width, height) {
            ctx.clearRect(0, 0, width, height);

            // 绘制键值对
            const mapArray = Array.from(mapData.entries());
            const pairHeight = Math.min(40, (height - 40) / Math.max(mapArray.length, 3));

            mapArray.forEach(([key, value], index) => {
                const y = 20 + index * (pairHeight + 10);

                // 绘制键
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(20, y, width/2 - 30, pairHeight);
                ctx.strokeStyle = '#c0392b';
                ctx.lineWidth = 2;
                ctx.strokeRect(20, y, width/2 - 30, pairHeight);

                // 绘制值
                ctx.fillStyle = '#3498db';
                ctx.fillRect(width/2 + 10, y, width/2 - 30, pairHeight);
                ctx.strokeStyle = '#2980b9';
                ctx.strokeRect(width/2 + 10, y, width/2 - 30, pairHeight);

                // 绘制箭头
                ctx.strokeStyle = '#2c3e50';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(width/2 - 10, y + pairHeight/2);
                ctx.lineTo(width/2 + 5, y + pairHeight/2);
                ctx.moveTo(width/2, y + pairHeight/2 - 5);
                ctx.lineTo(width/2 + 5, y + pairHeight/2);
                ctx.moveTo(width/2, y + pairHeight/2 + 5);
                ctx.lineTo(width/2 + 5, y + pairHeight/2);
                ctx.stroke();

                // 绘制文字
                ctx.fillStyle = 'white';
                ctx.font = '14px SF Pro Display, sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText(key.toString(), 20 + (width/2 - 30)/2, y + pairHeight/2 + 5);
                ctx.fillText(value.toString(), width/2 + 10 + (width/2 - 30)/2, y + pairHeight/2 + 5);
            });

            // 如果Map为空，显示提示
            if (mapData.size === 0) {
                ctx.fillStyle = '#95a5a6';
                ctx.font = '16px SF Pro Display, sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText('点击"添加键值对"开始体验Map！', width/2, height/2);
                ctx.fillText('每个键对应一个值', width/2, height/2 + 25);
            }
        }

        // List操作函数
        function showListDemo() {
            addScore(5);
            alert('📋 List接口特点：\n• 有序集合\n• 允许重复元素\n• 可通过索引访问\n• 主要实现类：ArrayList、LinkedList、Vector');
        }

        function addToList() {
            const randomValues = ['苹果', '香蕉', '橙子', '葡萄', '草莓', '西瓜', '桃子'];
            const randomValue = randomValues[Math.floor(Math.random() * randomValues.length)];
            listData.push(randomValue);

            const canvas = document.getElementById('listCanvas');
            const ctx = canvas.getContext('2d');
            drawListDemo(ctx, canvas.width, canvas.height);

            addScore(5);

            // 添加动画效果
            animateAddition(canvas, listData.length - 1);
        }

        function removeFromList() {
            if (listData.length > 0) {
                listData.pop();
                const canvas = document.getElementById('listCanvas');
                const ctx = canvas.getContext('2d');
                drawListDemo(ctx, canvas.width, canvas.height);
                addScore(3);
            }
        }

        function clearList() {
            listData = [];
            const canvas = document.getElementById('listCanvas');
            const ctx = canvas.getContext('2d');
            drawListDemo(ctx, canvas.width, canvas.height);
            addScore(2);
        }

        // Set操作函数
        function showSetDemo() {
            addScore(5);
            alert('🎯 Set接口特点：\n• 不允许重复元素\n• 元素唯一性\n• 无序（HashSet）或有序（TreeSet）\n• 主要实现类：HashSet、TreeSet、LinkedHashSet');
        }

        function addToSet() {
            const randomValues = ['红色', '蓝色', '绿色', '黄色', '紫色', '橙色', '粉色'];
            const randomValue = randomValues[Math.floor(Math.random() * randomValues.length)];

            const sizeBefore = setData.size;
            setData.add(randomValue);

            const canvas = document.getElementById('setCanvas');
            const ctx = canvas.getContext('2d');
            drawSetDemo(ctx, canvas.width, canvas.height);

            if (setData.size > sizeBefore) {
                addScore(5);
                animateSetAddition(canvas);
            } else {
                alert('🚫 Set不允许重复元素！"' + randomValue + '"已经存在。');
                addScore(3); // 学习了重复元素的概念
            }
        }

        function removeFromSet() {
            const setArray = Array.from(setData);
            if (setArray.length > 0) {
                const randomIndex = Math.floor(Math.random() * setArray.length);
                setData.delete(setArray[randomIndex]);

                const canvas = document.getElementById('setCanvas');
                const ctx = canvas.getContext('2d');
                drawSetDemo(ctx, canvas.width, canvas.height);
                addScore(3);
            }
        }

        function clearSet() {
            setData.clear();
            const canvas = document.getElementById('setCanvas');
            const ctx = canvas.getContext('2d');
            drawSetDemo(ctx, canvas.width, canvas.height);
            addScore(2);
        }

        // Map操作函数
        function showMapDemo() {
            addScore(5);
            alert('🗝️ Map接口特点：\n• 键值对存储\n• 键不能重复\n• 一个键对应一个值\n• 主要实现类：HashMap、TreeMap、Hashtable、ConcurrentHashMap');
        }

        function addToMap() {
            const keys = ['姓名', '年龄', '城市', '职业', '爱好', '学校', '专业'];
            const values = ['张三', '25', '北京', '程序员', '编程', '清华大学', '计算机'];

            const randomKey = keys[Math.floor(Math.random() * keys.length)];
            const randomValue = values[Math.floor(Math.random() * values.length)];

            mapData.set(randomKey, randomValue);

            const canvas = document.getElementById('mapCanvas');
            const ctx = canvas.getContext('2d');
            drawMapDemo(ctx, canvas.width, canvas.height);

            addScore(5);
            animateMapAddition(canvas);
        }

        function removeFromMap() {
            const mapArray = Array.from(mapData.keys());
            if (mapArray.length > 0) {
                const randomIndex = Math.floor(Math.random() * mapArray.length);
                mapData.delete(mapArray[randomIndex]);

                const canvas = document.getElementById('mapCanvas');
                const ctx = canvas.getContext('2d');
                drawMapDemo(ctx, canvas.width, canvas.height);
                addScore(3);
            }
        }

        function clearMap() {
            mapData.clear();
            const canvas = document.getElementById('mapCanvas');
            const ctx = canvas.getContext('2d');
            drawMapDemo(ctx, canvas.width, canvas.height);
            addScore(2);
        }

        // 动画效果函数
        function animateAddition(canvas, index) {
            const ctx = canvas.getContext('2d');
            let scale = 0;

            function animate() {
                if (scale < 1) {
                    scale += 0.1;

                    // 重绘整个画布
                    drawListDemo(ctx, canvas.width, canvas.height);

                    // 在新元素位置添加放大效果
                    const cellWidth = (canvas.width - 80) / Math.max(listData.length, 5);
                    const x = 40 + index * cellWidth;
                    const y = 40;
                    const cellHeight = canvas.height - 80;

                    ctx.save();
                    ctx.translate(x + cellWidth/2 - 2.5, y + cellHeight/2);
                    ctx.scale(scale, scale);
                    ctx.fillStyle = 'rgba(46, 204, 113, 0.5)';
                    ctx.fillRect(-cellWidth/2, -cellHeight/2, cellWidth - 5, cellHeight);
                    ctx.restore();

                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function animateSetAddition(canvas) {
            const ctx = canvas.getContext('2d');
            let opacity = 1;

            function animate() {
                if (opacity > 0) {
                    opacity -= 0.05;

                    // 重绘整个画布
                    drawSetDemo(ctx, canvas.width, canvas.height);

                    // 添加闪烁效果
                    ctx.save();
                    ctx.globalAlpha = opacity;
                    ctx.fillStyle = '#f1c40f';
                    ctx.beginPath();
                    ctx.arc(canvas.width/2, canvas.height/2, 50, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.restore();

                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function animateMapAddition(canvas) {
            const ctx = canvas.getContext('2d');
            let glow = 0;

            function animate() {
                if (glow < 20) {
                    glow += 1;

                    // 重绘整个画布
                    drawMapDemo(ctx, canvas.width, canvas.height);

                    // 添加发光效果
                    ctx.save();
                    ctx.shadowColor = '#3498db';
                    ctx.shadowBlur = glow;
                    ctx.strokeStyle = '#3498db';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);
                    ctx.restore();

                    requestAnimationFrame(animate);
                }
            }

            animate();
        }
    </script>
</body>
</html>
