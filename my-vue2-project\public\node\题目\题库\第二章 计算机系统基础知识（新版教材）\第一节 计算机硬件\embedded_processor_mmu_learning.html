<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式处理器与MMU学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
        }

        .quiz-title {
            color: white;
        }

        .quiz-title::after {
            background: white;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 15px 25px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(10px);
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
            animation: correctAnswer 0.6s ease;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
            animation: wrongAnswer 0.6s ease;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctAnswer {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongAnswer {
            0% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
            100% { transform: translateX(0); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 嵌入式处理器与MMU</h1>
            <p class="subtitle">通过动画和交互学习内存管理单元的奥秘</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- MMU概念介绍 -->
        <div class="section">
            <h2 class="section-title">🧠 什么是MMU？</h2>
            <div class="canvas-container">
                <canvas id="mmuCanvas" width="600" height="400"></canvas>
            </div>
            <div class="explanation">
                <strong>MMU (Memory Management Unit)</strong> 是内存管理单元，就像一个智能的"地址翻译官"。
                它的主要工作是将程序使用的虚拟地址转换成实际的物理地址，并控制内存访问权限。
                <br><br>
                点击上方画布，观看MMU工作原理的动画演示！
            </div>
            <div class="controls">
                <button class="btn" onclick="startMMUAnimation()">🎬 播放MMU动画</button>
                <button class="btn" onclick="resetMMUAnimation()">🔄 重置</button>
            </div>
        </div>

        <!-- 处理器类型对比 -->
        <div class="section">
            <h2 class="section-title">⚡ 处理器类型对比</h2>
            <div class="canvas-container">
                <canvas id="processorCanvas" width="700" height="450"></canvas>
            </div>
            <div class="explanation">
                不同的处理器有不同的特点：
                <br>• <strong>高端处理器</strong>：通常配备MMU，支持复杂的操作系统
                <br>• <strong>中端处理器</strong>：可能有简化的内存保护单元
                <br>• <strong>低端处理器</strong>：没有MMU，适合简单的实时系统
            </div>
            <div class="controls">
                <button class="btn" onclick="showProcessorComparison()">📊 对比处理器</button>
            </div>
        </div>

        <!-- 操作系统匹配游戏 -->
        <div class="section quiz-section">
            <h2 class="section-title quiz-title">🎮 操作系统匹配挑战</h2>
            <div class="canvas-container">
                <canvas id="gameCanvas" width="700" height="400"></canvas>
            </div>
            <div class="explanation">
                拖拽操作系统到正确的处理器类型上！看看哪些操作系统可以在没有MMU的处理器上运行。
            </div>
            <div class="controls">
                <button class="btn" onclick="startMatchingGame()">🎯 开始游戏</button>
                <button class="btn" onclick="resetGame()">🔄 重新开始</button>
            </div>
        </div>

        <!-- 题目测试 -->
        <div class="section">
            <h2 class="section-title">📝 知识测试</h2>
            <div class="explanation">
                <strong>题目：</strong>以下嵌入式处理器类型中不具备内存管理单元(MMU)的是（ ），嵌入式操作系统（ ）可以运行在它上面。
            </div>
            <div id="quizOptions">
                <div class="option" onclick="selectAnswer(this, false)">A. Linux</div>
                <div class="option" onclick="selectAnswer(this, false)">B. VxWorks653</div>
                <div class="option" onclick="selectAnswer(this, true)">C. μC/OS-Ⅱ</div>
                <div class="option" onclick="selectAnswer(this, false)">D. Windows CE</div>
            </div>
            <div class="explanation" id="answerExplanation" style="display: none;">
                <strong>正确答案：C</strong><br>
                μC/OS-Ⅱ是一个实时操作系统，专门设计用于没有MMU的微控制器。它使用静态内存分配，
                不需要虚拟内存管理，因此可以在Cortex-M3等不具备MMU的处理器上运行。
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentStep = 0;
        let totalSteps = 4;
        let gameScore = 0;
        let mmuAnimationRunning = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingElements();
            initializeCanvases();
            updateProgress();
        });

        // 创建浮动装饰元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 15; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.width = (Math.random() * 20 + 10) + 'px';
                element.style.height = element.style.width;
                element.style.animationDelay = Math.random() * 6 + 's';
                container.appendChild(element);
            }
        }

        // 初始化画布
        function initializeCanvases() {
            drawMMUDiagram();
            drawProcessorComparison();
            drawMatchingGame();
        }

        // 更新进度条
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // MMU动画
        function startMMUAnimation() {
            if (mmuAnimationRunning) return;
            mmuAnimationRunning = true;

            const canvas = document.getElementById('mmuCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制基础结构
                drawMMUBase(ctx);

                // 动画效果
                drawMMUAnimation(ctx, frame);

                frame++;
                if (frame < 120) {
                    requestAnimationFrame(animate);
                } else {
                    mmuAnimationRunning = false;
                    currentStep = Math.max(currentStep, 1);
                    updateProgress();
                }
            }

            animate();
        }

        // 绘制MMU基础图
        function drawMMUBase(ctx) {
            // CPU
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(50, 150, 100, 80);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('CPU', 100, 195);

            // MMU
            ctx.fillStyle = '#FF9800';
            ctx.fillRect(200, 150, 100, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('MMU', 250, 195);

            // 内存
            ctx.fillStyle = '#2196F3';
            ctx.fillRect(450, 100, 100, 180);
            ctx.fillStyle = 'white';
            ctx.fillText('物理内存', 500, 195);
        }

        // 绘制MMU动画
        function drawMMUAnimation(ctx, frame) {
            // 虚拟地址数据包
            if (frame > 20) {
                const x = 150 + (frame - 20) * 2;
                if (x < 200) {
                    ctx.fillStyle = '#E91E63';
                    ctx.fillRect(x, 170, 30, 20);
                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('虚拟地址', x + 15, 182);
                }
            }

            // 物理地址数据包
            if (frame > 60) {
                const x = 300 + (frame - 60) * 2.5;
                if (x < 450) {
                    ctx.fillStyle = '#9C27B0';
                    ctx.fillRect(x, 170, 30, 20);
                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('物理地址', x + 15, 182);
                }
            }

            // 箭头和标签
            if (frame > 10) {
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(150, 190);
                ctx.lineTo(200, 190);
                ctx.stroke();

                // 箭头头部
                ctx.beginPath();
                ctx.moveTo(195, 185);
                ctx.lineTo(200, 190);
                ctx.lineTo(195, 195);
                ctx.stroke();
            }

            if (frame > 50) {
                ctx.beginPath();
                ctx.moveTo(300, 190);
                ctx.lineTo(450, 190);
                ctx.stroke();

                // 箭头头部
                ctx.beginPath();
                ctx.moveTo(445, 185);
                ctx.lineTo(450, 190);
                ctx.lineTo(445, 195);
                ctx.stroke();
            }
        }

        // 重置MMU动画
        function resetMMUAnimation() {
            const canvas = document.getElementById('mmuCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawMMUDiagram();
        }

        // 绘制MMU图表
        function drawMMUDiagram() {
            const canvas = document.getElementById('mmuCanvas');
            const ctx = canvas.getContext('2d');

            drawMMUBase(ctx);

            // 添加说明文字
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MMU工作原理', canvas.width / 2, 30);

            ctx.font = '12px Arial';
            ctx.fillText('点击"播放MMU动画"按钮查看地址转换过程', canvas.width / 2, 350);
        }

        // 绘制处理器对比
        function drawProcessorComparison() {
            const canvas = document.getElementById('processorCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('处理器类型与MMU支持对比', canvas.width / 2, 30);

            // 高端处理器
            drawProcessorBox(ctx, 50, 80, '高端处理器', '#4CAF50', true, 'ARM Cortex-A系列');

            // 中端处理器
            drawProcessorBox(ctx, 250, 80, '中端处理器', '#FF9800', false, 'ARM Cortex-R系列');

            // 低端处理器
            drawProcessorBox(ctx, 450, 80, '低端处理器', '#f44336', false, 'ARM Cortex-M系列');

            // 图例
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(50, 380, 15, 15);
            ctx.fillStyle = '#333';
            ctx.fillText('支持MMU', 75, 392);

            ctx.fillStyle = '#f44336';
            ctx.fillRect(200, 380, 15, 15);
            ctx.fillStyle = '#333';
            ctx.fillText('不支持MMU', 225, 392);
        }

        function drawProcessorBox(ctx, x, y, title, color, hasMMU, example) {
            // 主框
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 150, 200);

            // 标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(title, x + 75, y + 30);

            // MMU状态
            ctx.font = '14px Arial';
            ctx.fillText(hasMMU ? '✓ 支持MMU' : '✗ 无MMU', x + 75, y + 60);

            // 示例
            ctx.font = '12px Arial';
            ctx.fillText(example, x + 75, y + 90);

            // 特性列表
            ctx.font = '10px Arial';
            ctx.textAlign = 'left';
            if (hasMMU) {
                ctx.fillText('• 虚拟内存管理', x + 10, y + 120);
                ctx.fillText('• 内存保护', x + 10, y + 140);
                ctx.fillText('• 多任务支持', x + 10, y + 160);
                ctx.fillText('• 复杂OS支持', x + 10, y + 180);
            } else {
                ctx.fillText('• 实时性能好', x + 10, y + 120);
                ctx.fillText('• 功耗低', x + 10, y + 140);
                ctx.fillText('• 成本低', x + 10, y + 160);
                ctx.fillText('• 简单OS支持', x + 10, y + 180);
            }
        }

        function showProcessorComparison() {
            drawProcessorComparison();
            currentStep = Math.max(currentStep, 2);
            updateProgress();
        }

        // 匹配游戏
        let gameData = {
            processors: [
                {name: 'Cortex-M3', hasMMU: false, x: 100, y: 100},
                {name: 'Cortex-A9', hasMMU: true, x: 100, y: 200},
                {name: 'Cortex-R5', hasMMU: false, x: 100, y: 300}
            ],
            operatingSystems: [
                {name: 'μC/OS-Ⅱ', needsMMU: false, x: 500, y: 100, dragging: false},
                {name: 'Linux', needsMMU: true, x: 500, y: 150, dragging: false},
                {name: 'VxWorks653', needsMMU: true, x: 500, y: 200, dragging: false},
                {name: 'Windows CE', needsMMU: true, x: 500, y: 250, dragging: false}
            ],
            matches: [],
            draggedOS: null
        };

        function drawMatchingGame() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('拖拽操作系统到合适的处理器', canvas.width / 2, 30);

            // 绘制处理器
            gameData.processors.forEach(processor => {
                ctx.fillStyle = processor.hasMMU ? '#4CAF50' : '#f44336';
                ctx.fillRect(processor.x, processor.y, 120, 60);

                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(processor.name, processor.x + 60, processor.y + 25);
                ctx.fillText(processor.hasMMU ? '支持MMU' : '无MMU', processor.x + 60, processor.y + 45);
            });

            // 绘制操作系统
            gameData.operatingSystems.forEach(os => {
                ctx.fillStyle = '#2196F3';
                ctx.fillRect(os.x, os.y, 100, 40);

                ctx.fillStyle = 'white';
                ctx.font = '11px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(os.name, os.x + 50, os.y + 25);
            });

            // 绘制连接线
            gameData.matches.forEach(match => {
                ctx.strokeStyle = match.correct ? '#4CAF50' : '#f44336';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(match.processor.x + 120, match.processor.y + 30);
                ctx.lineTo(match.os.x, match.os.y + 20);
                ctx.stroke();
            });
        }

        function startMatchingGame() {
            const canvas = document.getElementById('gameCanvas');

            // 添加鼠标事件
            canvas.addEventListener('mousedown', handleMouseDown);
            canvas.addEventListener('mousemove', handleMouseMove);
            canvas.addEventListener('mouseup', handleMouseUp);

            drawMatchingGame();
            currentStep = Math.max(currentStep, 3);
            updateProgress();
        }

        function handleMouseDown(e) {
            const rect = e.target.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检查是否点击了操作系统
            gameData.operatingSystems.forEach(os => {
                if (x >= os.x && x <= os.x + 100 && y >= os.y && y <= os.y + 40) {
                    gameData.draggedOS = os;
                    os.dragging = true;
                }
            });
        }

        function handleMouseMove(e) {
            if (gameData.draggedOS && gameData.draggedOS.dragging) {
                const rect = e.target.getBoundingClientRect();
                gameData.draggedOS.x = e.clientX - rect.left - 50;
                gameData.draggedOS.y = e.clientY - rect.top - 20;
                drawMatchingGame();
            }
        }

        function handleMouseUp(e) {
            if (gameData.draggedOS) {
                const rect = e.target.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 检查是否放在处理器上
                gameData.processors.forEach(processor => {
                    if (x >= processor.x && x <= processor.x + 120 && y >= processor.y && y <= processor.y + 60) {
                        const isCorrect = (processor.hasMMU && gameData.draggedOS.needsMMU) ||
                                        (!processor.hasMMU && !gameData.draggedOS.needsMMU);

                        gameData.matches.push({
                            processor: processor,
                            os: gameData.draggedOS,
                            correct: isCorrect
                        });

                        if (isCorrect) {
                            gameScore++;
                        }
                    }
                });

                gameData.draggedOS.dragging = false;
                gameData.draggedOS = null;
                drawMatchingGame();
            }
        }

        function resetGame() {
            gameData.matches = [];
            gameScore = 0;
            gameData.operatingSystems.forEach((os, index) => {
                os.x = 500;
                os.y = 100 + index * 50;
                os.dragging = false;
            });
            gameData.draggedOS = null;
            drawMatchingGame();
        }

        // 答题功能
        function selectAnswer(element, isCorrect) {
            // 移除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('answerExplanation').style.display = 'block';
                currentStep = Math.max(currentStep, 4);
                updateProgress();
            } else {
                element.classList.add('wrong');
            }
        }
    </script>
</body>
</html>
