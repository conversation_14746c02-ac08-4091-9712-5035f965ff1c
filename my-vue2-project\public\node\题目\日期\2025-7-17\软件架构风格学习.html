<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构风格 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .game-board {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .blank {
            display: inline-block;
            min-width: 120px;
            height: 40px;
            border: 2px dashed #667eea;
            border-radius: 8px;
            margin: 0 5px;
            position: relative;
            vertical-align: middle;
            background: rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .blank.filled {
            background: #667eea;
            color: white;
            border: 2px solid #667eea;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .option.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            animation: correctPulse 0.6s ease;
        }

        .option.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            animation: wrongShake 0.6s ease;
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        #architectureCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .explanation h3 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .explanation p {
            color: #333;
            line-height: 1.6;
            font-size: 1.1rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🏗️ 软件架构风格</h1>
            <p class="subtitle">通过互动游戏学习软件架构的核心概念</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-text">
                    软件架构风格是描述某一特定应用领域中系统组织方式的惯用模式。架构风格反映领域中众多系统所共有的结构和<span class="blank" id="blank1"></span>，强调对架构<span class="blank" id="blank2"></span>的重用。
                </div>

                <div class="options">
                    <div class="option" data-answer="语义特性" data-blank="1">A. 语义特性</div>
                    <div class="option" data-answer="功能需求" data-blank="1">B. 功能需求</div>
                    <div class="option" data-answer="质量属性" data-blank="1">C. 质量属性</div>
                    <div class="option" data-answer="业务规则" data-blank="1">D. 业务规则</div>
                </div>

                <div class="options" style="margin-top: 40px;">
                    <div class="option" data-answer="模式" data-blank="2">模式</div>
                    <div class="option" data-answer="组件" data-blank="2">组件</div>
                    <div class="option" data-answer="接口" data-blank="2">接口</div>
                    <div class="option" data-answer="设计" data-blank="2">设计</div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="architectureCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation" id="explanation">
                <h3>🎯 知识解析</h3>
                <p><strong>软件架构风格</strong>是软件工程中的重要概念，它定义了系统组织的通用模式。</p>
                <br>
                <p><strong>语义特性</strong>指的是架构中各组件的含义、作用和相互关系，这些特性在同一领域的不同系统中往往是相似的。</p>
                <br>
                <p><strong>架构模式的重用</strong>是软件架构设计的核心目标，通过重用成熟的架构模式，可以提高开发效率和系统质量。</p>
            </div>
        </div>
    </div>

    <script>
        class ArchitectureLearning {
            constructor() {
                this.canvas = document.getElementById('architectureCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.currentStep = 0;
                this.totalSteps = 2;
                this.answers = { 1: '语义特性', 2: '模式' };
                this.userAnswers = {};
                
                this.initializeParticles();
                this.bindEvents();
                this.startAnimation();
            }

            initializeParticles() {
                const particlesContainer = document.getElementById('particles');
                for (let i = 0; i < 20; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.top = Math.random() * 100 + '%';
                    particle.style.width = Math.random() * 10 + 5 + 'px';
                    particle.style.height = particle.style.width;
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    particlesContainer.appendChild(particle);
                }
            }

            bindEvents() {
                document.querySelectorAll('.option').forEach(option => {
                    option.addEventListener('click', (e) => this.handleOptionClick(e));
                });
            }

            handleOptionClick(e) {
                const option = e.target;
                const answer = option.dataset.answer;
                const blankNumber = option.dataset.blank;
                const correctAnswer = this.answers[blankNumber];

                if (answer === correctAnswer) {
                    option.classList.add('correct');
                    this.fillBlank(blankNumber, answer);
                    this.userAnswers[blankNumber] = answer;
                    this.currentStep++;
                    this.updateProgress();
                    
                    setTimeout(() => {
                        this.animateArchitecture();
                        if (this.currentStep === this.totalSteps) {
                            this.showExplanation();
                        }
                    }, 500);
                } else {
                    option.classList.add('wrong');
                    setTimeout(() => {
                        option.classList.remove('wrong');
                    }, 600);
                }
            }

            fillBlank(blankNumber, answer) {
                const blank = document.getElementById(`blank${blankNumber}`);
                blank.textContent = answer;
                blank.classList.add('filled');
            }

            updateProgress() {
                const progressFill = document.getElementById('progressFill');
                const percentage = (this.currentStep / this.totalSteps) * 100;
                progressFill.style.width = percentage + '%';
            }

            animateArchitecture() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                
                // 绘制架构风格概念图
                this.drawArchitectureStyle();
            }

            drawArchitectureStyle() {
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;

                // 绘制中心概念
                this.drawCircle(centerX, centerY, 60, '#667eea', '架构风格');

                // 绘制周围的概念
                const concepts = [
                    { text: '语义特性', angle: 0, color: '#4CAF50' },
                    { text: '结构模式', angle: Math.PI / 2, color: '#FF9800' },
                    { text: '重用性', angle: Math.PI, color: '#E91E63' },
                    { text: '组织方式', angle: 3 * Math.PI / 2, color: '#9C27B0' }
                ];

                concepts.forEach((concept, index) => {
                    setTimeout(() => {
                        const x = centerX + Math.cos(concept.angle) * 150;
                        const y = centerY + Math.sin(concept.angle) * 100;
                        
                        this.drawCircle(x, y, 40, concept.color, concept.text);
                        this.drawConnection(centerX, centerY, x, y);
                    }, index * 300);
                });

                // 绘制应用领域示例
                setTimeout(() => {
                    this.drawDomainExamples();
                }, 1500);
            }

            drawCircle(x, y, radius, color, text) {
                // 绘制圆形
                this.ctx.beginPath();
                this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
                this.ctx.fillStyle = color;
                this.ctx.fill();
                this.ctx.strokeStyle = '#fff';
                this.ctx.lineWidth = 3;
                this.ctx.stroke();

                // 绘制文字
                this.ctx.fillStyle = '#fff';
                this.ctx.font = 'bold 14px Microsoft YaHei';
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.fillText(text, x, y);
            }

            drawConnection(x1, y1, x2, y2) {
                this.ctx.beginPath();
                this.ctx.moveTo(x1, y1);
                this.ctx.lineTo(x2, y2);
                this.ctx.strokeStyle = 'rgba(102, 126, 234, 0.5)';
                this.ctx.lineWidth = 2;
                this.ctx.stroke();
            }

            drawDomainExamples() {
                const examples = ['Web应用', '移动应用', '桌面应用', '嵌入式系统'];
                examples.forEach((example, index) => {
                    const x = 100 + index * 150;
                    const y = 350;
                    
                    this.ctx.fillStyle = '#f0f0f0';
                    this.ctx.fillRect(x - 50, y - 20, 100, 40);
                    this.ctx.strokeStyle = '#ddd';
                    this.ctx.strokeRect(x - 50, y - 20, 100, 40);
                    
                    this.ctx.fillStyle = '#333';
                    this.ctx.font = '12px Microsoft YaHei';
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText(example, x, y);
                });
            }

            showExplanation() {
                const explanation = document.getElementById('explanation');
                explanation.classList.add('show');
            }

            startAnimation() {
                this.animateArchitecture();
            }
        }

        // 启动应用
        window.addEventListener('load', () => {
            new ArchitectureLearning();
        });
    </script>
</body>
</html>
