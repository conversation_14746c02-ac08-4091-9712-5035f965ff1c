<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRC校验码互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        .image-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 20px;
            padding: 20px;
            border: 3px solid #e2e8f0;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: #f8fafc;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        #crcDiagramCanvas {
            background: white;
            border: 3px solid #667eea;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
        }

        .btn:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
        }

        .btn:disabled:hover {
            transform: none !important;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
        }

        .explanation {
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            gap: 20px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
            position: relative;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .quiz-section {
            background: linear-gradient(135deg, #fef5e7, #fed7aa);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #744210;
            font-weight: 600;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            padding: 15px;
            background: white;
            border: 2px solid #fed7aa;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            background: #fef5e7;
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: #c6f6d5;
            border-color: #48bb78;
        }

        .quiz-option.wrong {
            background: #fed7d7;
            border-color: #f56565;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔢 CRC校验码学习之旅</h1>
            <p class="subtitle">通过动画和互动，轻松掌握循环冗余校验的奥秘</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 知识介绍部分 -->
        <div class="learning-section">
            <h2 class="section-title">📚 什么是CRC校验码？</h2>
            <div class="explanation">
                <p><strong>CRC（循环冗余校验）</strong>是一种错误检测技术，就像给数据加上"身份证"，确保数据在传输过程中没有被篡改。</p>
                <br>
                <p>🎯 <strong>核心思想：</strong>通过数学运算为原始数据生成一个"指纹"（校验码），接收方可以验证数据完整性。</p>
            </div>
        </div>

        <!-- CRC计算过程图解部分 -->
        <div class="learning-section">
            <h2 class="section-title">🖼️ CRC计算过程图解</h2>
            <div class="image-container">
                <canvas id="crcDiagramCanvas" width="800" height="600"></canvas>
            </div>
            <div class="explanation">
                <h3>📖 图解说明：</h3>
                <p><strong>1. 数据准备：</strong>信息码字 111000110，生成多项式 G(X) = x⁵ + x³ + x + 1 → 101011</p>
                <p><strong>2. 补零操作：</strong>在信息码字后补5个0，得到 11100011000000</p>
                <p><strong>3. 模2除法：</strong>使用异或运算进行除法，每次对齐最高位的1</p>
                <p><strong>4. 逐步计算：</strong>重复异或运算，直到无法继续除法</p>
                <p><strong>5. 得到余数：</strong>最后的余数就是CRC校验码：11001</p>

                <h3>🔍 详细计算过程：</h3>
                <div style="background: #f8fafc; padding: 15px; border-radius: 10px; margin: 10px 0; font-family: monospace;">
                    <p><strong>第1步：</strong>111000 ⊕ 101011 = 010000</p>
                    <p style="margin-left: 20px;">11100011000000</p>
                    <p style="margin-left: 20px;">101011</p>
                    <p style="margin-left: 20px;">──────</p>
                    <p style="margin-left: 20px;">01000011000000</p>

                    <p><strong>第2步：</strong>100001 ⊕ 101011 = 001010</p>
                    <p style="margin-left: 20px;">01000011000000</p>
                    <p style="margin-left: 20px;">  101011</p>
                    <p style="margin-left: 20px;">  ──────</p>
                    <p style="margin-left: 20px;">00100001000000</p>

                    <p><strong>第3步：</strong>100010 ⊕ 101011 = 001001</p>
                    <p style="margin-left: 20px;">00100001000000</p>
                    <p style="margin-left: 20px;">     101011</p>
                    <p style="margin-left: 20px;">     ──────</p>
                    <p style="margin-left: 20px;">00000011001000</p>

                    <p><strong>第4步：</strong>110010 ⊕ 101011 = 011001</p>
                    <p style="margin-left: 20px;">00000011001000</p>
                    <p style="margin-left: 20px;">        101011</p>
                    <p style="margin-left: 20px;">        ──────</p>
                    <p style="margin-left: 20px;">00000000011001</p>

                    <p style="color: #e53e3e; font-weight: bold;">🎯 最终CRC校验码：11001</p>
                </div>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="drawCRCDiagram()">🎨 显示计算图解</button>
                <button class="btn btn-secondary" onclick="animateCRCSteps()">🎬 动画演示步骤</button>
                <button class="btn btn-primary" onclick="drawVisualCRCProcess()">📊 可视化计算过程</button>
            </div>
        </div>

        <!-- 详细步骤指示器 -->
        <div class="step-indicator">
            <div class="step active" id="step0">开始</div>
            <div class="step" id="step1">1.1</div>
            <div class="step" id="step2">1.2</div>
            <div class="step" id="step3">1.3</div>
            <div class="step" id="step4">2.1</div>
            <div class="step" id="step5">2.2</div>
            <div class="step" id="step6">3.1</div>
            <div class="step" id="step7">3.2</div>
            <div class="step" id="step8">4.1</div>
            <div class="step" id="step9">4.2</div>
            <div class="step" id="step10">4.3</div>
            <div class="step" id="step11">结果</div>
        </div>

        <!-- 动画演示部分 -->
        <div class="learning-section">
            <h2 class="section-title">🎬 CRC计算动画演示</h2>
            <div class="canvas-container">
                <canvas id="crcCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="startDemo()">🚀 开始演示</button>
                <button class="btn btn-secondary" onclick="prevStep()" id="prevBtn">⬅️ 上一步</button>
                <button class="btn btn-secondary" onclick="nextStep()" id="nextBtn">➡️ 下一步</button>
                <button class="btn btn-primary" onclick="resetDemo()">🔄 重新开始</button>
            </div>
            <div class="explanation" id="stepExplanation">
                <p>🚀 点击开始，看动画学CRC！</p>
            </div>
            <div class="explanation" style="background: linear-gradient(135deg, #e6fffa, #b2f5ea); border-left-color: #38a169;">
                <p><strong>💡 快捷键提示：</strong></p>
                <p>⬅️ 左箭头：上一步 | ➡️ 右箭头：下一步 | 空格/回车：开始/下一步 | ESC：重新开始</p>
            </div>
        </div>

        <!-- CRC原理详解部分 -->
        <div class="learning-section">
            <h2 class="section-title">🔬 CRC计算原理深度解析</h2>
            <div class="explanation">
                <h3>🎯 为什么这样计算？</h3>
                <p><strong>1. 模2除法的本质：</strong>CRC使用的是模2除法，也就是异或运算。这种运算有个特点：相同为0，不同为1。</p>
                <p><strong>2. 为什么要补零：</strong>补零的位数等于生成多项式的最高次幂，这样可以确保余数的位数正好等于校验码的位数。</p>
                <p><strong>3. 除法过程：</strong>每次找到被除数中最左边的1，将除数对齐到这个位置，然后进行异或运算。</p>
                <p><strong>4. 校验原理：</strong>发送方将原始数据和CRC校验码一起发送，接收方用同样的方法计算，如果余数为0，说明数据正确。</p>
            </div>

            <div class="explanation" style="background: linear-gradient(135deg, #fff5f5, #fed7d7); border-left-color: #f56565;">
                <h3>💡 关键理解点：</h3>
                <ul style="text-align: left; margin-left: 20px;">
                    <li><strong>异或运算：</strong>0⊕0=0, 0⊕1=1, 1⊕0=1, 1⊕1=0</li>
                    <li><strong>对齐规则：</strong>除数的最高位1要对齐被除数中当前最左边的1</li>
                    <li><strong>继续条件：</strong>只要被除数中还有1，就继续除法过程</li>
                    <li><strong>结果提取：</strong>最后的余数就是CRC校验码</li>
                </ul>
            </div>
        </div>

        <!-- 互动练习部分 -->
        <div class="quiz-section">
            <h2 class="section-title">🎮 互动练习</h2>
            <div class="quiz-question">
                根据演示，信息码字111000110，生成多项式G(X)=x⁵+x³+x+1，CRC校验码是？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="checkAnswer(this, false)">A. 01101</div>
                <div class="quiz-option" onclick="checkAnswer(this, true)">B. 11001</div>
                <div class="quiz-option" onclick="checkAnswer(this, false)">C. 001101</div>
                <div class="quiz-option" onclick="checkAnswer(this, false)">D. 011001</div>
            </div>
            <div id="quizResult"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('crcCanvas');
        const ctx = canvas.getContext('2d');
        let currentStep = 0;
        let animationId;
        let isAnimating = false;

        // 题目数据
        const data = {
            message: '111000110',
            polynomial: '101011',
            paddedMessage: '11100011000000',
            steps: []
        };

        // 计算CRC的步骤
        function calculateCRC() {
            const message = data.paddedMessage;
            const poly = data.polynomial;
            let remainder = message;
            data.steps = [];

            for (let i = 0; i <= message.length - poly.length; i++) {
                if (remainder[i] === '1') {
                    let newRemainder = '';
                    for (let j = 0; j < remainder.length; j++) {
                        if (j >= i && j < i + poly.length) {
                            newRemainder += (remainder[j] === poly[j - i]) ? '0' : '1';
                        } else {
                            newRemainder += remainder[j];
                        }
                    }
                    data.steps.push({
                        step: i + 1,
                        dividend: remainder,
                        divisor: poly,
                        position: i,
                        result: newRemainder,
                        description: `第${i + 1}步：在位置${i}处进行异或运算`
                    });
                    remainder = newRemainder;
                }
            }
            
            // 提取最后5位作为CRC
            data.crc = remainder.slice(-5);
        }

        // 动画状态
        let animationFrame = 0;
        let maxAnimationFrames = 120;
        let detailedStepIndex = 0;
        const totalSteps = 12; // 0-11

        // CRC计算图解绘制函数
        function drawCRCDiagram() {
            const diagramCanvas = document.getElementById('crcDiagramCanvas');
            const diagramCtx = diagramCanvas.getContext('2d');

            diagramCtx.clearRect(0, 0, diagramCanvas.width, diagramCanvas.height);

            // 设置字体和样式
            diagramCtx.font = '14px Arial';
            diagramCtx.textAlign = 'left';

            // 绘制标题
            diagramCtx.fillStyle = '#4a5568';
            diagramCtx.font = 'bold 20px Arial';
            diagramCtx.textAlign = 'center';
            diagramCtx.fillText('CRC计算过程详解', diagramCanvas.width/2, 30);

            // 绘制计算步骤
            drawCRCCalculationSteps(diagramCtx);
        }

        function drawCRCCalculationSteps(ctx) {
            const startY = 60;
            const lineHeight = 35;
            let currentY = startY;

            // 设置字体
            ctx.font = '12px monospace';
            ctx.textAlign = 'left';

            // 步骤1：显示原始数据和多项式
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('1. 原始数据和生成多项式：', 50, currentY);
            currentY += 25;

            ctx.fillStyle = '#4a5568';
            ctx.font = '12px monospace';
            ctx.fillText('信息码字：111000110', 70, currentY);
            currentY += 20;
            ctx.fillText('生成多项式 G(X) = x⁵ + x³ + x + 1 → 101011', 70, currentY);
            currentY += 30;

            // 步骤2：补零操作
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('2. 补零操作（补5个0）：', 50, currentY);
            currentY += 25;

            ctx.fillStyle = '#4a5568';
            ctx.font = '12px monospace';
            ctx.fillText('111000110 + 00000 = 11100011000000', 70, currentY);
            currentY += 30;

            // 步骤3：模2除法过程
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('3. 模2除法过程（异或运算）：', 50, currentY);
            currentY += 25;

            // 绘制详细的除法过程
            drawDetailedDivisionProcess(ctx, 70, currentY);
        }

        function drawDetailedDivisionProcess(ctx, startX, startY) {
            ctx.font = '12px monospace';
            ctx.textAlign = 'left';

            const steps = [
                {
                    label: '第1步：',
                    dividend: '11100011000000',
                    divisor: '101011',
                    position: 0,
                    result: '01000011000000',
                    description: '111000 ⊕ 101011 = 010000',
                    color: '#667eea'
                },
                {
                    label: '第2步：',
                    dividend: '01000011000000',
                    divisor: '101011',
                    position: 2,
                    result: '00100001000000',
                    description: '100001 ⊕ 101011 = 001010',
                    color: '#48bb78'
                },
                {
                    label: '第3步：',
                    dividend: '00100001000000',
                    divisor: '101011',
                    position: 5,
                    result: '00000011001000',
                    description: '100010 ⊕ 101011 = 001001',
                    color: '#f56565'
                },
                {
                    label: '第4步：',
                    dividend: '00000011001000',
                    divisor: '101011',
                    position: 8,
                    result: '00000000011001',
                    description: '110010 ⊕ 101011 = 011001',
                    color: '#764ba2'
                }
            ];

            let y = startY;

            for (let i = 0; i < steps.length; i++) {
                const step = steps[i];

                // 绘制步骤标签背景
                ctx.fillStyle = step.color;
                ctx.fillRect(startX - 5, y - 12, 50, 18);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(step.label, startX, y);

                // 绘制被除数
                ctx.fillStyle = '#4a5568';
                ctx.font = '12px monospace';
                ctx.fillText(step.dividend, startX + 60, y);
                y += 18;

                // 绘制对齐的除数
                const divisorX = startX + 60 + step.position * 9;
                ctx.fillStyle = step.color;
                ctx.fillText(step.divisor, divisorX, y);

                // 绘制异或线
                ctx.strokeStyle = step.color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(divisorX, y + 3);
                ctx.lineTo(divisorX + step.divisor.length * 9, y + 3);
                ctx.stroke();
                y += 18;

                // 绘制结果
                ctx.fillStyle = '#38a169';
                ctx.font = 'bold 12px monospace';
                ctx.fillText(step.result, startX + 60, y);

                // 绘制说明
                ctx.fillStyle = '#666';
                ctx.font = '11px Arial';
                ctx.fillText(step.description, startX + 250, y - 18);

                y += 25;
            }

            // 绘制最终结果框
            y += 10;
            ctx.fillStyle = '#e53e3e';
            ctx.fillRect(startX - 5, y - 15, 200, 25);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('🎯 CRC校验码：11001', startX, y);

            // 绘制解释
            y += 35;
            ctx.fillStyle = '#4a5568';
            ctx.font = '12px Arial';
            ctx.fillText('💡 余数的最后5位就是CRC校验码', startX, y);
            ctx.fillText('💡 这个校验码用于检测数据传输错误', startX, y + 20);
        }

        // 可视化CRC计算过程
        function drawVisualCRCProcess() {
            const diagramCanvas = document.getElementById('crcDiagramCanvas');
            const diagramCtx = diagramCanvas.getContext('2d');

            diagramCtx.clearRect(0, 0, diagramCanvas.width, diagramCanvas.height);

            // 绘制标题
            diagramCtx.fillStyle = '#4a5568';
            diagramCtx.font = 'bold 20px Arial';
            diagramCtx.textAlign = 'center';
            diagramCtx.fillText('CRC计算过程可视化', diagramCanvas.width/2, 30);

            // 绘制类似您图片中的计算过程
            drawLongDivisionStyle(diagramCtx);
        }

        function drawLongDivisionStyle(ctx) {
            const startX = 50;
            const startY = 80;
            const bitWidth = 25;
            const lineHeight = 30;

            // 设置字体
            ctx.font = '14px monospace';
            ctx.textAlign = 'center';

            // 绘制被除数
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('被除数：11100011000000', startX, startY);
            ctx.fillText('除数：  101011', startX, startY + 25);

            // 绘制长除法格式
            let y = startY + 60;

            // 第一行：原始数据
            const originalData = '11100011000000';
            ctx.fillStyle = '#667eea';
            ctx.font = '14px monospace';
            ctx.textAlign = 'center';

            for (let i = 0; i < originalData.length; i++) {
                ctx.fillRect(startX + 100 + i * bitWidth, y - 15, bitWidth - 2, 20);
                ctx.fillStyle = 'white';
                ctx.fillText(originalData[i], startX + 100 + i * bitWidth + bitWidth/2, y);
                ctx.fillStyle = '#667eea';
            }

            y += lineHeight;

            // 绘制除法步骤
            const steps = [
                {
                    divisor: '101011',
                    position: 0,
                    result: '010000',
                    remainder: '01000011000000',
                    color: '#f56565'
                },
                {
                    divisor: '101011',
                    position: 2,
                    result: '001010',
                    remainder: '00100001000000',
                    color: '#48bb78'
                },
                {
                    divisor: '101011',
                    position: 5,
                    result: '001001',
                    remainder: '00000011001000',
                    color: '#764ba2'
                },
                {
                    divisor: '101011',
                    position: 8,
                    result: '011001',
                    remainder: '00000000011001',
                    color: '#e53e3e'
                }
            ];

            for (let stepIndex = 0; stepIndex < steps.length; stepIndex++) {
                const step = steps[stepIndex];

                // 绘制除数位置
                ctx.fillStyle = step.color;
                for (let i = 0; i < step.divisor.length; i++) {
                    ctx.fillRect(startX + 100 + (step.position + i) * bitWidth, y - 15, bitWidth - 2, 20);
                    ctx.fillStyle = 'white';
                    ctx.fillText(step.divisor[i], startX + 100 + (step.position + i) * bitWidth + bitWidth/2, y);
                    ctx.fillStyle = step.color;
                }

                // 绘制异或线
                ctx.strokeStyle = step.color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(startX + 100 + step.position * bitWidth, y + 5);
                ctx.lineTo(startX + 100 + (step.position + step.divisor.length) * bitWidth, y + 5);
                ctx.stroke();

                y += lineHeight;

                // 绘制余数
                ctx.fillStyle = '#38a169';
                for (let i = 0; i < step.remainder.length; i++) {
                    if (step.remainder[i] === '1') {
                        ctx.fillRect(startX + 100 + i * bitWidth, y - 15, bitWidth - 2, 20);
                        ctx.fillStyle = 'white';
                        ctx.fillText(step.remainder[i], startX + 100 + i * bitWidth + bitWidth/2, y);
                        ctx.fillStyle = '#38a169';
                    } else {
                        ctx.fillStyle = '#e2e8f0';
                        ctx.fillRect(startX + 100 + i * bitWidth, y - 15, bitWidth - 2, 20);
                        ctx.fillStyle = '#666';
                        ctx.fillText(step.remainder[i], startX + 100 + i * bitWidth + bitWidth/2, y);
                        ctx.fillStyle = '#38a169';
                    }
                }

                // 绘制步骤说明
                ctx.fillStyle = step.color;
                ctx.font = '12px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`第${stepIndex + 1}步`, startX, y - 5);

                y += lineHeight + 10;
            }

            // 绘制最终结果
            y += 20;
            ctx.fillStyle = '#e53e3e';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🎯 CRC校验码：11001', diagramCanvas.width/2, y);

            // 绘制说明
            y += 30;
            ctx.fillStyle = '#4a5568';
            ctx.font = '14px Arial';
            ctx.fillText('最后5位余数就是CRC校验码', diagramCanvas.width/2, y);
        }

        // 动画演示CRC步骤
        function animateCRCSteps() {
            const diagramCanvas = document.getElementById('crcDiagramCanvas');
            const diagramCtx = diagramCanvas.getContext('2d');

            let step = 0;
            const maxSteps = 4;

            function animateStep() {
                diagramCtx.clearRect(0, 0, diagramCanvas.width, diagramCanvas.height);

                // 绘制标题
                diagramCtx.fillStyle = '#4a5568';
                diagramCtx.font = 'bold 20px Arial';
                diagramCtx.textAlign = 'center';
                diagramCtx.fillText('CRC计算动画演示', diagramCanvas.width/2, 30);

                // 绘制当前步骤
                drawAnimatedStep(diagramCtx, step);

                step = (step + 1) % (maxSteps + 1);

                setTimeout(animateStep, 2000);
            }

            animateStep();
        }

        function drawAnimatedStep(ctx, stepIndex) {
            const startY = 80;
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';

            const steps = [
                {
                    title: '步骤1：准备数据',
                    content: ['信息码字：111000110', '生成多项式：101011', '补零后：11100011000000'],
                    color: '#667eea'
                },
                {
                    title: '步骤2：第一次异或',
                    content: ['111000 ⊕ 101011 = 010000', '结果：01000011000000'],
                    color: '#48bb78'
                },
                {
                    title: '步骤3：继续异或',
                    content: ['100001 ⊕ 101011 = 001010', '结果：00100001000000'],
                    color: '#f56565'
                },
                {
                    title: '步骤4：重复过程',
                    content: ['继续找1的位置', '重复异或运算'],
                    color: '#764ba2'
                },
                {
                    title: '步骤5：得到结果',
                    content: ['最终余数：00000000011001', 'CRC校验码：11001'],
                    color: '#e53e3e'
                }
            ];

            if (stepIndex < steps.length) {
                const currentStep = steps[stepIndex];

                // 绘制步骤标题
                ctx.fillStyle = currentStep.color;
                ctx.font = 'bold 18px Arial';
                ctx.fillText(currentStep.title, canvas.width/2, startY);

                // 绘制步骤内容
                ctx.fillStyle = '#4a5568';
                ctx.font = '16px Arial';
                for (let i = 0; i < currentStep.content.length; i++) {
                    ctx.fillText(currentStep.content[i], canvas.width/2, startY + 40 + i * 30);
                }

                // 绘制进度指示器
                const progressY = startY + 200;
                for (let i = 0; i < steps.length; i++) {
                    ctx.fillStyle = i === stepIndex ? currentStep.color : '#e2e8f0';
                    ctx.beginPath();
                    ctx.arc(300 + i * 40, progressY, 15, 0, 2 * Math.PI);
                    ctx.fill();

                    ctx.fillStyle = i === stepIndex ? 'white' : '#666';
                    ctx.font = 'bold 12px Arial';
                    ctx.fillText((i + 1).toString(), 300 + i * 40, progressY + 4);
                }
            }
        }

        // 绘制函数
        function drawStep(stepIndex) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 设置字体
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';

            switch(stepIndex) {
                case 0: drawIntroduction(); break;
                case 1: drawStep1_1_WhatIsCRC(); break;
                case 2: drawStep1_2_UnderstandPolynomial(); break;
                case 3: drawStep1_3_ConvertToBinary(); break;
                case 4: drawStep2_1_WhyPadZeros(); break;
                case 5: drawStep2_2_PaddingProcess(); break;
                case 6: drawStep3_1_SetupDivision(); break;
                case 7: drawStep3_2_UnderstandXOR(); break;
                case 8: drawStep4_1_FirstDivision(); break;
                case 9: drawStep4_2_ContinueDivision(); break;
                case 10: drawStep4_3_FinalSteps(); break;
                case 11: drawStep5_GetResult(); break;
            }
        }

        function drawIntroduction() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 28px Arial';
            ctx.fillText('🎯 CRC校验码学习', canvas.width/2, 50);

            const time = Date.now() * 0.003;

            // 题目信息动画
            ctx.font = '20px Arial';
            ctx.fillStyle = '#667eea';
            const pulse1 = 1 + 0.1 * Math.sin(time * 2);
            ctx.save();
            ctx.scale(pulse1, pulse1);
            ctx.fillText('📝 信息码：111000110', canvas.width/2/pulse1, 120/pulse1);
            ctx.restore();

            const pulse2 = 1 + 0.1 * Math.sin(time * 2 + 1);
            ctx.save();
            ctx.scale(pulse2, pulse2);
            ctx.fillText('🔧 多项式：x⁵ + x³ + x + 1', canvas.width/2/pulse2, 160/pulse2);
            ctx.restore();

            // 鼓励文字
            const bounce = 10 * Math.sin(time * 3);
            ctx.fillStyle = '#764ba2';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('🚀 我们一步步来学习！', canvas.width/2, 220 + bounce);

            // 提示
            ctx.font = '16px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('别担心，每一步都会有详细动画解释', canvas.width/2, 280);
        }

        // 步骤1.1：什么是CRC
        function drawStep1_1_WhatIsCRC() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤1.1：什么是CRC？', canvas.width/2, 40);

            const time = Date.now() * 0.003;

            // CRC概念动画
            ctx.font = '18px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('🔍 CRC = 循环冗余校验', canvas.width/2, 80);

            // 比喻动画
            const float1 = 5 * Math.sin(time * 2);
            ctx.fillStyle = '#48bb78';
            ctx.fillText('📱 就像手机的"验证码"', canvas.width/2, 120 + float1);

            const float2 = 5 * Math.sin(time * 2 + 1);
            ctx.fillStyle = '#f56565';
            ctx.fillText('🔐 确保数据没有错误', canvas.width/2, 160 + float2);

            // 示意图动画
            const centerX = canvas.width/2;
            const centerY = 250;

            // 数据包
            const dataScale = 1 + 0.1 * Math.sin(time * 3);
            ctx.save();
            ctx.translate(centerX - 100, centerY);
            ctx.scale(dataScale, dataScale);
            ctx.fillStyle = '#667eea';
            ctx.fillRect(-40, -20, 80, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('数据', 0, 5);
            ctx.restore();

            // 箭头
            ctx.fillStyle = '#764ba2';
            ctx.font = '20px Arial';
            ctx.fillText('→', centerX - 30, centerY + 5);

            // CRC处理
            const crcScale = 1 + 0.1 * Math.sin(time * 3 + 1);
            ctx.save();
            ctx.translate(centerX + 30, centerY);
            ctx.scale(crcScale, crcScale);
            ctx.fillStyle = '#48bb78';
            ctx.fillRect(-30, -20, 60, 40);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('CRC', 0, 5);
            ctx.restore();

            // 箭头
            ctx.fillStyle = '#764ba2';
            ctx.font = '20px Arial';
            ctx.fillText('→', centerX + 80, centerY + 5);

            // 安全数据
            const safeScale = 1 + 0.1 * Math.sin(time * 3 + 2);
            ctx.save();
            ctx.translate(centerX + 130, centerY);
            ctx.scale(safeScale, safeScale);
            ctx.fillStyle = '#38a169';
            ctx.fillRect(-40, -20, 80, 40);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('安全数据', 0, 5);
            ctx.restore();

            // 底部提示
            ctx.font = '16px Arial';
            ctx.fillStyle = '#e53e3e';
            ctx.fillText('💡 现在我们来学习如何计算CRC', canvas.width/2, 350);
        }

        // 步骤1.2：理解多项式
        function drawStep1_2_UnderstandPolynomial() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤1.2：理解生成多项式', canvas.width/2, 40);

            const time = Date.now() * 0.003;

            // 显示多项式
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('G(X) = x⁵ + x³ + x + 1', canvas.width/2, 80);

            // 分解说明
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('🔍 让我们分解这个多项式：', canvas.width/2, 120);

            // 逐项显示动画
            const terms = [
                {text: 'x⁵', power: 5, x: 200, color: '#f56565'},
                {text: 'x³', power: 3, x: 300, color: '#48bb78'},
                {text: 'x¹', power: 1, x: 400, color: '#667eea'},
                {text: 'x⁰', power: 0, x: 500, color: '#764ba2'}
            ];

            for (let i = 0; i < terms.length; i++) {
                const term = terms[i];
                const delay = i * 60;
                const showTime = (time * 60) % 240;

                if (showTime > delay) {
                    const scale = Math.min(1, (showTime - delay) / 30);
                    const bounce = 5 * Math.sin(time * 4 + i);

                    ctx.save();
                    ctx.translate(term.x, 170 + bounce);
                    ctx.scale(scale, scale);

                    // 项的背景
                    ctx.fillStyle = term.color;
                    ctx.fillRect(-25, -20, 50, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText(term.text, 0, 5);

                    // 次数标注
                    ctx.fillStyle = term.color;
                    ctx.font = '12px Arial';
                    ctx.fillText(`${term.power}次`, 0, 35);
                    ctx.restore();
                }
            }

            // 解释文字
            ctx.font = '14px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('每一项代表一个位置', canvas.width/2, 250);
            ctx.fillText('有这一项 = 1，没有这一项 = 0', canvas.width/2, 275);

            // 缺失项提示
            const missingPulse = 1 + 0.2 * Math.sin(time * 5);
            ctx.save();
            ctx.translate(canvas.width/2, 320);
            ctx.scale(missingPulse, missingPulse);
            ctx.fillStyle = '#e53e3e';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('⚠️ 注意：没有x⁴和x²项！', 0, 0);
            ctx.restore();
        }

        // 步骤1.3：转换为二进制
        function drawStep1_3_ConvertToBinary() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤1.3：转换为二进制', canvas.width/2, 40);

            const time = Date.now() * 0.003;

            // 转换规则
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('🔄 转换规则：有项=1，无项=0', canvas.width/2, 80);

            // 位置表
            const positions = [
                {pos: 5, has: true, x: 150},
                {pos: 4, has: false, x: 220},
                {pos: 3, has: true, x: 290},
                {pos: 2, has: false, x: 360},
                {pos: 1, has: true, x: 430},
                {pos: 0, has: true, x: 500}
            ];

            // 表头
            ctx.font = '14px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('位置', canvas.width/2, 120);

            for (let i = 0; i < positions.length; i++) {
                const pos = positions[i];
                const highlight = Math.floor((time * 2) % 6) === i;
                const scale = highlight ? 1.2 : 1;

                ctx.save();
                ctx.translate(pos.x, 150);
                ctx.scale(scale, scale);

                // 位置标签
                ctx.fillStyle = '#666';
                ctx.font = '12px Arial';
                ctx.fillText(`x${pos.pos}`, 0, -10);

                // 值框
                ctx.fillStyle = pos.has ? '#48bb78' : '#f56565';
                ctx.fillRect(-15, 0, 30, 30);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.fillText(pos.has ? '1' : '0', 0, 20);

                // 说明
                ctx.fillStyle = pos.has ? '#2d3748' : '#a0aec0';
                ctx.font = '10px Arial';
                ctx.fillText(pos.has ? '有此项' : '无此项', 0, 45);
                ctx.restore();
            }

            // 结果动画
            const resultY = 250 + 10 * Math.sin(time * 3);
            ctx.fillStyle = '#764ba2';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('结果：101011', canvas.width/2, resultY);

            // 重要提示
            ctx.font = '16px Arial';
            ctx.fillStyle = '#e53e3e';
            ctx.fillText('💡 这就是我们的"除数"！', canvas.width/2, 300);
        }

        function drawPolynomialDetailed() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤1：理解生成多项式 G(X)', canvas.width/2, 40);

            // 详细解释区域
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('🔍 什么是生成多项式？', canvas.width/2, 80);
            ctx.font = '14px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('生成多项式是CRC算法的"密钥"，它决定了如何计算校验码', canvas.width/2, 105);

            // 显示原始多项式
            ctx.font = 'bold 18px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('G(X) = x⁵ + x³ + x + 1', canvas.width/2, 140);

            // 动画箭头
            const time = Date.now() * 0.005;
            const arrowY = 160 + 5 * Math.sin(time);
            ctx.fillStyle = '#f56565';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('↓ 转换为二进制', canvas.width/2, arrowY);

            // 详细转换过程
            ctx.font = '16px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('转换规则：有该次幂项 = 1，无该次幂项 = 0', canvas.width/2, 190);

            // 绘制详细的对应关系
            const startX = 150;
            const positions = [startX, startX + 80, startX + 160, startX + 240, startX + 320, startX + 400];
            const coefficients = ['1', '0', '1', '0', '1', '1'];
            const powers = ['x⁵', 'x⁴', 'x³', 'x²', 'x¹', 'x⁰'];
            const hasTerms = [true, false, true, false, true, true];

            // 绘制位置标识
            ctx.font = '12px Arial';
            ctx.fillStyle = '#666';
            for (let i = 0; i < 6; i++) {
                ctx.fillText(`位置${5-i}`, positions[i] + 20, 220);
            }

            // 绘制系数框
            for (let i = 0; i < 6; i++) {
                // 动画效果：依次高亮显示
                const highlightTime = (time * 2 + i) % 6;
                const isHighlighted = Math.floor(highlightTime) === i;

                if (hasTerms[i]) {
                    ctx.fillStyle = isHighlighted ? '#48bb78' : '#c6f6d5';
                    ctx.strokeStyle = '#48bb78';
                } else {
                    ctx.fillStyle = isHighlighted ? '#fed7d7' : '#f7fafc';
                    ctx.strokeStyle = '#e2e8f0';
                }

                ctx.lineWidth = isHighlighted ? 3 : 1;
                ctx.fillRect(positions[i], 240, 40, 40);
                ctx.strokeRect(positions[i], 240, 40, 40);

                // 系数
                ctx.fillStyle = '#4a5568';
                ctx.font = 'bold 18px Arial';
                ctx.fillText(coefficients[i], positions[i] + 20, 265);

                // 次幂
                ctx.font = '14px Arial';
                ctx.fillStyle = hasTerms[i] ? '#48bb78' : '#a0aec0';
                ctx.fillText(powers[i], positions[i] + 20, 300);

                // 说明
                ctx.font = '10px Arial';
                ctx.fillStyle = hasTerms[i] ? '#2d3748' : '#a0aec0';
                ctx.fillText(hasTerms[i] ? '有此项' : '无此项', positions[i] + 20, 315);
            }

            // 最终结果
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('二进制结果：101011', canvas.width/2, 350);

            // 重要提示
            ctx.font = '14px Arial';
            ctx.fillStyle = '#e53e3e';
            ctx.fillText('💡 记住：这个101011就是我们的"除数"！', canvas.width/2, 380);
        }

        // 步骤2.1：为什么要补零
        function drawStep2_1_WhyPadZeros() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤2.1：为什么要补零？', canvas.width/2, 40);

            const time = Date.now() * 0.003;

            // 问题动画
            const questionScale = 1 + 0.1 * Math.sin(time * 3);
            ctx.save();
            ctx.translate(canvas.width/2, 90);
            ctx.scale(questionScale, questionScale);
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 20px Arial';
            ctx.fillText('🤔 为什么要在后面加0？', 0, 0);
            ctx.restore();

            // 原因解释动画
            const reasons = [
                {text: '📏 让数据变长', y: 140, delay: 0},
                {text: '➗ 方便做除法', y: 180, delay: 60},
                {text: '🎯 得到正确余数', y: 220, delay: 120}
            ];

            for (let i = 0; i < reasons.length; i++) {
                const reason = reasons[i];
                const showTime = (time * 60) % 180;

                if (showTime > reason.delay) {
                    const alpha = Math.min(1, (showTime - reason.delay) / 30);
                    const slide = Math.min(0, -50 + (showTime - reason.delay) * 2);

                    ctx.globalAlpha = alpha;
                    ctx.fillStyle = '#48bb78';
                    ctx.font = '18px Arial';
                    ctx.fillText(reason.text, canvas.width/2 + slide, reason.y);
                    ctx.globalAlpha = 1;
                }
            }

            // 类比动画
            ctx.font = '16px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('💡 就像做长除法时要补位一样', canvas.width/2, 280);

            // 示例动画
            const exampleY = 320 + 5 * Math.sin(time * 4);
            ctx.fillStyle = '#f56565';
            ctx.font = '14px Arial';
            ctx.fillText('例如：123 ÷ 7 → 1230 ÷ 7', canvas.width/2, exampleY);
        }

        // 步骤2.2：补零过程
        function drawStep2_2_PaddingProcess() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤2.2：补零过程', canvas.width/2, 40);

            const time = Date.now() * 0.003;

            // 原始数据
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('📝 原始信息码：', 150, 90);

            const originalBits = '111000110';
            for (let i = 0; i < originalBits.length; i++) {
                const pulse = 1 + 0.1 * Math.sin(time * 2 + i * 0.3);
                ctx.save();
                ctx.translate(200 + i * 35, 120);
                ctx.scale(pulse, pulse);

                ctx.fillStyle = '#48bb78';
                ctx.fillRect(-15, -15, 30, 30);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(originalBits[i], 0, 5);

                // 位置标号
                ctx.fillStyle = '#666';
                ctx.font = '10px Arial';
                ctx.fillText(`${8-i}`, 0, -25);
                ctx.restore();
            }

            // 补零说明
            ctx.font = '16px Arial';
            ctx.fillStyle = '#f56565';
            ctx.fillText('➕ 补5个零：', 150, 200);

            // 补零动画 - 逐个出现
            const paddingBits = '00000';
            for (let i = 0; i < paddingBits.length; i++) {
                const delay = i * 30;
                const showTime = (time * 60) % 150;

                if (showTime > delay) {
                    const scale = Math.min(1, (showTime - delay) / 20);
                    const bounce = 5 * Math.sin(time * 5 + i);

                    ctx.save();
                    ctx.translate(200 + (originalBits.length + i) * 35, 230 + bounce);
                    ctx.scale(scale, scale);

                    ctx.fillStyle = '#f56565';
                    ctx.fillRect(-15, -15, 30, 30);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 14px Arial';
                    ctx.fillText('0', 0, 5);

                    // 位置标号
                    ctx.fillStyle = '#666';
                    ctx.font = '10px Arial';
                    ctx.fillText(`${4-i}`, 0, -25);
                    ctx.restore();
                }
            }

            // 最终结果
            const resultY = 320 + 8 * Math.sin(time * 3);
            ctx.font = 'bold 18px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('🎯 最终：11100011000000', canvas.width/2, resultY);

            // 长度说明
            ctx.font = '14px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('9位 + 5位 = 14位总长度', canvas.width/2, 360);
        }

        function drawPaddingDetailed() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤2：补零操作', canvas.width/2, 40);

            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('补5个零', canvas.width/2, 75);

            // 显示原始信息码
            ctx.font = 'bold 18px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('原始信息码：111000110 (9位)', canvas.width/2, 155);

            // 动画显示补零过程
            const originalBits = '111000110';
            const paddingBits = '00000';
            const time = Date.now() * 0.003;

            // 原始数据
            ctx.font = '12px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('原始数据', 120, 190);

            for (let i = 0; i < originalBits.length; i++) {
                const pulse = 1 + 0.1 * Math.sin(time + i * 0.5);
                ctx.save();
                ctx.translate(150 + i * 35, 210);
                ctx.scale(pulse, pulse);

                ctx.fillStyle = '#48bb78';
                ctx.fillRect(-15, -15, 30, 30);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(originalBits[i], 0, 5);
                ctx.restore();

                // 位置标号
                ctx.fillStyle = '#666';
                ctx.font = '10px Arial';
                ctx.fillText(`${8-i}`, 150 + i * 35, 245);
            }

            // 加号动画
            const plusScale = 1 + 0.2 * Math.sin(time * 3);
            ctx.save();
            ctx.translate(470, 210);
            ctx.scale(plusScale, plusScale);
            ctx.fillStyle = '#f56565';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('+', 0, 5);
            ctx.restore();

            // 补零数据
            ctx.font = '12px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('补零部分', 520, 190);

            for (let i = 0; i < paddingBits.length; i++) {
                const delay = i * 20;
                const showFrame = (time * 60) % 120;
                const alpha = showFrame > delay ? 1 : 0.3;

                ctx.globalAlpha = alpha;
                ctx.fillStyle = '#f56565';
                ctx.fillRect(550 + i * 35, 195, 30, 30);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(paddingBits[i], 565 + i * 35, 215);

                // 位置标号
                ctx.fillStyle = '#666';
                ctx.font = '10px Arial';
                ctx.fillText(`${4-i}`, 565 + i * 35, 245);
                ctx.globalAlpha = 1;
            }

            // 结果
            ctx.font = 'bold 18px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('补零后：11100011000000 (14位)', canvas.width/2, 280);

            // 解释为什么是14位
            ctx.font = '14px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('9位原始数据 + 5位补零 = 14位总长度', canvas.width/2, 305);

            // 重要提示
            ctx.font = '14px Arial';
            ctx.fillStyle = '#e53e3e';
            ctx.fillText('💡 现在这14位数据就是我们的"被除数"！', canvas.width/2, 340);

            // 下一步预告
            ctx.font = '12px Arial';
            ctx.fillStyle = '#666';
            ctx.fillText('接下来我们将用101011去除11100011000000', canvas.width/2, 365);
        }

        // 步骤3.1：设置除法
        function drawStep3_1_SetupDivision() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤3.1：设置除法运算', canvas.width/2, 40);

            const time = Date.now() * 0.003;

            // 除法框架动画
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('📐 设置除法框架：', canvas.width/2, 80);

            // 被除数
            ctx.font = '14px Arial';
            ctx.fillStyle = '#48bb78';
            ctx.fillText('被除数（要除的数）', 200, 120);

            const dividend = '11100011000000';
            for (let i = 0; i < dividend.length; i++) {
                const wave = 3 * Math.sin(time * 2 + i * 0.2);
                ctx.fillStyle = '#48bb78';
                ctx.fillRect(150 + i * 25, 140 + wave, 20, 20);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(dividend[i], 160 + i * 25, 154 + wave);
            }

            // 除数
            ctx.font = '14px Arial';
            ctx.fillStyle = '#f56565';
            ctx.fillText('除数（用来除的数）', 200, 200);

            const divisor = '101011';
            for (let i = 0; i < divisor.length; i++) {
                const bounce = Math.abs(5 * Math.sin(time * 3 + i * 0.4));
                ctx.fillStyle = '#f56565';
                ctx.fillRect(200 + i * 25, 220 - bounce, 20, 20);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(divisor[i], 210 + i * 25, 234 - bounce);
            }

            // 除法符号
            const symbolScale = 1 + 0.2 * Math.sin(time * 4);
            ctx.save();
            ctx.translate(canvas.width/2, 280);
            ctx.scale(symbolScale, symbolScale);
            ctx.fillStyle = '#764ba2';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('÷', 0, 0);
            ctx.restore();

            // 提示
            ctx.font = '16px Arial';
            ctx.fillStyle = '#e53e3e';
            ctx.fillText('⚠️ 但这不是普通除法！', canvas.width/2, 330);
        }

        // 步骤3.2：理解异或运算
        function drawStep3_2_UnderstandXOR() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤3.2：什么是异或运算？', canvas.width/2, 40);

            const time = Date.now() * 0.003;

            // 异或符号动画
            const xorScale = 1 + 0.3 * Math.sin(time * 3);
            ctx.save();
            ctx.translate(canvas.width/2, 90);
            ctx.scale(xorScale, xorScale);
            ctx.fillStyle = '#764ba2';
            ctx.font = 'bold 32px Arial';
            ctx.fillText('⊕', 0, 0);
            ctx.restore();

            // 规则说明
            ctx.font = '18px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('🔍 异或规则：', canvas.width/2, 140);

            // 规则表格动画
            const rules = [
                {a: '0', b: '0', result: '0', x: 200, color: '#48bb78'},
                {a: '0', b: '1', result: '1', x: 300, color: '#f56565'},
                {a: '1', b: '0', result: '1', x: 400, color: '#f56565'},
                {a: '1', b: '1', result: '0', x: 500, color: '#48bb78'}
            ];

            for (let i = 0; i < rules.length; i++) {
                const rule = rules[i];
                const highlight = Math.floor((time * 2) % 4) === i;
                const scale = highlight ? 1.2 : 1;
                const glow = highlight ? 1 + 0.3 * Math.sin(time * 8) : 1;

                ctx.save();
                ctx.translate(rule.x, 180);
                ctx.scale(scale, scale);

                // 背景
                ctx.globalAlpha = glow;
                ctx.fillStyle = rule.color;
                ctx.fillRect(-30, -25, 60, 50);
                ctx.globalAlpha = 1;

                // 文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(`${rule.a}⊕${rule.b}`, 0, -5);
                ctx.fillText(`=${rule.result}`, 0, 10);
                ctx.restore();
            }

            // 记忆口诀
            const tipY = 250 + 5 * Math.sin(time * 4);
            ctx.font = '16px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('💡 记忆口诀：相同为0，不同为1', canvas.width/2, tipY);

            // 示例动画
            ctx.font = '14px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('🎯 示例：1011 ⊕ 1010 = 0001', canvas.width/2, 300);
        }

        function drawDivisionSetupDetailed() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤3：模2除法', canvas.width/2, 40);

            const time = Date.now() * 0.003;

            // 被除数动画
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('被除数', 150, 80);

            const dividend = '11100011000000';
            for (let i = 0; i < dividend.length; i++) {
                const wave = 5 * Math.sin(time * 2 + i * 0.3);
                ctx.fillStyle = '#48bb78';
                ctx.fillRect(100 + i * 30, 100 + wave, 25, 25);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(dividend[i], 112 + i * 30, 118 + wave);
            }

            // 除数动画
            ctx.font = '16px Arial';
            ctx.fillStyle = '#f56565';
            ctx.fillText('除数', 150, 180);

            const divisor = '101011';
            for (let i = 0; i < divisor.length; i++) {
                const bounce = Math.abs(10 * Math.sin(time * 3 + i * 0.5));
                ctx.fillStyle = '#f56565';
                ctx.fillRect(100 + i * 30, 200 - bounce, 25, 25);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(divisor[i], 112 + i * 30, 218 - bounce);
            }

            // 异或符号动画
            const xorScale = 1 + 0.3 * Math.sin(time * 4);
            ctx.save();
            ctx.translate(canvas.width/2, 280);
            ctx.scale(xorScale, xorScale);
            ctx.fillStyle = '#764ba2';
            ctx.font = 'bold 32px Arial';
            ctx.fillText('⊕', 0, 0);
            ctx.restore();

            ctx.font = '14px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('异或运算', canvas.width/2, 320);
        }

        // 步骤4.1：第一次除法
        function drawStep4_1_FirstDivision() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤4.1：第一次除法运算', canvas.width/2, 40);

            const time = Date.now() * 0.003;
            calculateCRC();

            // 说明
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('🎯 找到第一个1，开始除法', canvas.width/2, 80);

            // 显示被除数，高亮第一个1
            const dividend = '11100011000000';
            ctx.font = '12px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('被除数：', 100, 120);

            for (let i = 0; i < dividend.length; i++) {
                const isFirst = i === 0;
                const glow = isFirst ? 1 + 0.5 * Math.sin(time * 6) : 1;

                ctx.save();
                ctx.translate(150 + i * 25, 130);
                ctx.scale(glow, glow);

                ctx.fillStyle = isFirst ? '#f56565' : '#48bb78';
                ctx.fillRect(-10, -10, 20, 20);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(dividend[i], 0, 4);
                ctx.restore();

                if (isFirst) {
                    ctx.fillStyle = '#f56565';
                    ctx.font = '10px Arial';
                    ctx.fillText('开始位置', 150 + i * 25 - 15, 155);
                }
            }

            // 显示除数对齐
            ctx.font = '12px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('除数：', 100, 180);

            const divisor = '101011';
            for (let i = 0; i < divisor.length; i++) {
                const float = 5 * Math.sin(time * 4 + i * 0.5);
                ctx.fillStyle = '#764ba2';
                ctx.fillRect(150 + i * 25, 190 - float, 20, 20);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(divisor[i], 160 + i * 25, 204 - float);
            }

            // 异或运算过程
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('⊕ 异或运算：', 100, 240);

            // 逐位异或动画
            const result1 = '01000011000000';
            for (let i = 0; i < 6; i++) {
                const delay = i * 20;
                const showTime = (time * 60) % 120;

                if (showTime > delay) {
                    const alpha = Math.min(1, (showTime - delay) / 15);
                    const sparkle = 1 + 0.3 * Math.sin(time * 8 + i);

                    ctx.globalAlpha = alpha;
                    ctx.save();
                    ctx.translate(150 + i * 25, 260);
                    ctx.scale(sparkle, sparkle);

                    ctx.fillStyle = '#38a169';
                    ctx.fillRect(-10, -10, 20, 20);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.fillText(result1[i], 0, 4);
                    ctx.restore();
                    ctx.globalAlpha = 1;
                }
            }

            // 结果说明
            ctx.font = '14px Arial';
            ctx.fillStyle = '#e53e3e';
            ctx.fillText('💡 第一步完成！继续找下一个1', canvas.width/2, 320);
        }

        // 步骤4.2：继续除法
        function drawStep4_2_ContinueDivision() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤4.2：继续除法运算', canvas.width/2, 40);

            const time = Date.now() * 0.003;
            calculateCRC();

            // 当前状态
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('🔄 继续寻找下一个1', canvas.width/2, 80);

            // 显示当前余数
            const currentRemainder = '01000011000000';
            ctx.font = '12px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('当前余数：', 100, 120);

            for (let i = 0; i < currentRemainder.length; i++) {
                const isNext = i === 2; // 下一个1的位置
                const pulse = isNext ? 1 + 0.4 * Math.sin(time * 6) : 1;

                ctx.save();
                ctx.translate(150 + i * 25, 130);
                ctx.scale(pulse, pulse);

                ctx.fillStyle = isNext ? '#f56565' : (currentRemainder[i] === '1' ? '#48bb78' : '#e2e8f0');
                ctx.fillRect(-10, -10, 20, 20);
                ctx.fillStyle = isNext ? 'white' : (currentRemainder[i] === '1' ? 'white' : '#666');
                ctx.font = 'bold 12px Arial';
                ctx.fillText(currentRemainder[i], 0, 4);
                ctx.restore();

                if (isNext) {
                    ctx.fillStyle = '#f56565';
                    ctx.font = '10px Arial';
                    ctx.fillText('下一个1', 150 + i * 25 - 12, 155);
                }
            }

            // 对齐除数
            ctx.font = '12px Arial';
            ctx.fillStyle = '#4a5568';
            ctx.fillText('对齐除数：', 100, 180);

            const divisor = '101011';
            for (let i = 0; i < divisor.length; i++) {
                const wave = 3 * Math.sin(time * 5 + i * 0.3);
                ctx.fillStyle = '#764ba2';
                ctx.fillRect(150 + (2 + i) * 25, 190 + wave, 20, 20); // 从位置2开始
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(divisor[i], 160 + (2 + i) * 25, 204 + wave);
            }

            // 计算结果
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('⊕ 继续异或：', 100, 240);

            // 显示这一步的结果
            const stepResult = '00100001000000';
            for (let i = 0; i < stepResult.length; i++) {
                const highlight = i >= 2 && i < 8;
                const glow = highlight ? 1 + 0.2 * Math.sin(time * 7) : 1;

                ctx.globalAlpha = glow;
                ctx.fillStyle = highlight ? '#38a169' : '#e2e8f0';
                ctx.fillRect(150 + i * 25, 250, 20, 20);
                ctx.fillStyle = highlight ? 'white' : '#666';
                ctx.font = 'bold 12px Arial';
                ctx.fillText(stepResult[i], 160 + i * 25, 264);
                ctx.globalAlpha = 1;
            }

            // 进度提示
            ctx.font = '14px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('🎯 重复这个过程，直到没有更多的1', canvas.width/2, 300);
        }

        // 步骤4.3：最后几步
        function drawStep4_3_FinalSteps() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤4.3：完成所有运算', canvas.width/2, 40);

            const time = Date.now() * 0.003;
            calculateCRC();

            // 显示最终几步
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('🏁 完成所有除法步骤', canvas.width/2, 80);

            // 显示计算过程摘要
            const steps = [
                {desc: '第1步', remainder: '01000011000000'},
                {desc: '第2步', remainder: '00100001000000'},
                {desc: '第3步', remainder: '00000011001000'},
                {desc: '最终', remainder: '00000000011001'}
            ];

            for (let i = 0; i < steps.length; i++) {
                const step = steps[i];
                const y = 120 + i * 60;
                const highlight = Math.floor((time * 1.5) % 4) === i;
                const scale = highlight ? 1.1 : 1;

                ctx.save();
                ctx.translate(100, y);
                ctx.scale(scale, scale);

                // 步骤标签
                ctx.fillStyle = highlight ? '#f56565' : '#4a5568';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(step.desc, 0, 0);
                ctx.restore();

                // 余数显示
                for (let j = 0; j < step.remainder.length; j++) {
                    const isImportant = j >= step.remainder.length - 5; // 最后5位
                    const glow = highlight && isImportant ? 1 + 0.3 * Math.sin(time * 8) : 1;

                    ctx.save();
                    ctx.translate(200 + j * 20, y);
                    ctx.scale(glow, glow);

                    ctx.fillStyle = isImportant ? '#f56565' : (step.remainder[j] === '1' ? '#48bb78' : '#e2e8f0');
                    ctx.fillRect(-8, -8, 16, 16);
                    ctx.fillStyle = isImportant ? 'white' : (step.remainder[j] === '1' ? 'white' : '#666');
                    ctx.font = 'bold 10px Arial';
                    ctx.fillText(step.remainder[j], 0, 3);
                    ctx.restore();
                }
            }

            // 重点提示
            const tipY = 360 + 5 * Math.sin(time * 4);
            ctx.font = '16px Arial';
            ctx.fillStyle = '#e53e3e';
            ctx.fillText('💡 注意最后5位：11001', canvas.width/2, tipY);
        }

        function drawDivisionProcessDetailed() {
            calculateCRC();
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤4：计算过程', canvas.width/2, 40);

            const time = Date.now() * 0.002;
            const currentStep = Math.floor(time) % 3;

            if (data.steps.length > 0) {
                // 显示当前步骤
                const step = data.steps[Math.min(currentStep, data.steps.length - 1)];

                // 步骤标题动画
                const titlePulse = 1 + 0.1 * Math.sin(time * 5);
                ctx.save();
                ctx.translate(canvas.width/2, 80);
                ctx.scale(titlePulse, titlePulse);
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 16px Arial';
                ctx.fillText(`第${currentStep + 1}步`, 0, 0);
                ctx.restore();

                // 被除数显示
                const dividend = step.dividend;
                for (let i = 0; i < dividend.length; i++) {
                    const highlight = i >= step.position && i < step.position + 6;
                    const glow = highlight ? 1 + 0.3 * Math.sin(time * 8) : 1;

                    ctx.save();
                    ctx.translate(50 + i * 25, 120);
                    ctx.scale(glow, glow);

                    ctx.fillStyle = highlight ? '#f56565' : '#48bb78';
                    ctx.fillRect(-10, -10, 20, 20);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.fillText(dividend[i], 0, 4);
                    ctx.restore();
                }

                // 异或运算动画
                if (currentStep < data.steps.length) {
                    const divisor = '101011';
                    for (let i = 0; i < divisor.length; i++) {
                        const x = 50 + (step.position + i) * 25;
                        const float = 10 * Math.sin(time * 6 + i * 0.5);

                        ctx.fillStyle = '#764ba2';
                        ctx.fillRect(x - 10, 160 - float, 20, 20);
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 12px Arial';
                        ctx.fillText(divisor[i], x, 174 - float);
                    }

                    // 等号和结果
                    ctx.fillStyle = '#4a5568';
                    ctx.font = 'bold 20px Arial';
                    ctx.fillText('=', canvas.width/2, 220);

                    // 结果显示
                    const result = step.result;
                    for (let i = 0; i < result.length; i++) {
                        const sparkle = Math.random() * 0.2 + 0.8;
                        ctx.globalAlpha = sparkle;

                        ctx.fillStyle = '#38a169';
                        ctx.fillRect(50 + i * 25 - 10, 240, 20, 20);
                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 12px Arial';
                        ctx.fillText(result[i], 50 + i * 25, 254);
                        ctx.globalAlpha = 1;
                    }
                }
            }
        }

        // 步骤5：获取最终结果
        function drawStep5_GetResult() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤5：提取CRC校验码', canvas.width/2, 40);

            const time = Date.now() * 0.003;
            calculateCRC();

            // 最终余数
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('🎯 最终余数：', canvas.width/2, 80);

            const finalRemainder = '00000000011001';
            for (let i = 0; i < finalRemainder.length; i++) {
                const isResult = i >= finalRemainder.length - 5; // 最后5位
                const wave = isResult ? 8 * Math.sin(time * 3 + i * 0.4) : 0;
                const scale = isResult ? 1 + 0.3 * Math.sin(time * 4) : 1;

                ctx.save();
                ctx.translate(150 + i * 30, 120 + wave);
                ctx.scale(scale, scale);

                ctx.fillStyle = isResult ? '#f56565' : '#e2e8f0';
                ctx.fillRect(-12, -12, 24, 24);
                ctx.fillStyle = isResult ? 'white' : '#666';
                ctx.font = 'bold 14px Arial';
                ctx.fillText(finalRemainder[i], 0, 5);
                ctx.restore();

                // 标注
                if (isResult) {
                    ctx.fillStyle = '#f56565';
                    ctx.font = '10px Arial';
                    ctx.fillText('CRC', 150 + i * 30 - 8, 150);
                }
            }

            // 提取过程动画
            const arrowY = 180 + 10 * Math.sin(time * 5);
            ctx.fillStyle = '#764ba2';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('↓ 取最后5位', canvas.width/2, arrowY);

            // CRC结果
            const resultScale = 1 + 0.4 * Math.sin(time * 2);
            ctx.save();
            ctx.translate(canvas.width/2, 250);
            ctx.scale(resultScale, resultScale);

            // 结果框
            ctx.fillStyle = '#48bb78';
            ctx.fillRect(-100, -30, 200, 60);
            ctx.strokeStyle = '#38a169';
            ctx.lineWidth = 4;
            ctx.strokeRect(-100, -30, 200, 60);

            // CRC码
            ctx.fillStyle = 'white';
            ctx.font = 'bold 32px Arial';
            ctx.fillText('11001', 0, 10);
            ctx.restore();

            // 答案确认
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('✅ 答案是：B. 11001', canvas.width/2, 320);

            // 庆祝动画
            for (let i = 0; i < 12; i++) {
                const angle = time * 2 + i * 0.5;
                const radius = 80 + 20 * Math.sin(time * 3 + i);
                const x = canvas.width/2 + radius * Math.cos(angle);
                const y = 380 + radius * Math.sin(angle) * 0.6;
                const size = 4 + 3 * Math.sin(time * 4 + i);

                ctx.fillStyle = `hsl(${(time * 120 + i * 30) % 360}, 80%, 60%)`;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }

            // 完成提示
            const successPulse = 1 + 0.15 * Math.sin(time * 6);
            ctx.save();
            ctx.translate(canvas.width/2, 450);
            ctx.scale(successPulse, successPulse);
            ctx.fillStyle = '#48bb78';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('🎉 CRC计算完成！', 0, 0);
            ctx.restore();
        }

        function drawResultDetailed() {
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('步骤5：CRC校验码', canvas.width/2, 40);

            const time = Date.now() * 0.004;
            calculateCRC();

            // 显示最终余数
            ctx.font = '16px Arial';
            ctx.fillStyle = '#667eea';
            ctx.fillText('最终余数', canvas.width/2, 80);

            const finalRemainder = '00011001';
            for (let i = 0; i < finalRemainder.length; i++) {
                const isResult = i >= 3; // 后5位是结果
                const wave = 8 * Math.sin(time * 3 + i * 0.4);
                const scale = isResult ? 1 + 0.2 * Math.sin(time * 4) : 1;

                ctx.save();
                ctx.translate(200 + i * 40, 120 + wave);
                ctx.scale(scale, scale);

                ctx.fillStyle = isResult ? '#f56565' : '#e2e8f0';
                ctx.fillRect(-18, -18, 36, 36);
                ctx.fillStyle = isResult ? 'white' : '#666';
                ctx.font = 'bold 16px Arial';
                ctx.fillText(finalRemainder[i], 0, 6);
                ctx.restore();

                // 标注
                if (isResult) {
                    ctx.fillStyle = '#f56565';
                    ctx.font = '10px Arial';
                    ctx.fillText('CRC', 200 + i * 40 - 8, 160);
                }
            }

            // 箭头指向结果
            const arrowX = canvas.width/2 + 20;
            const arrowY = 180 + 10 * Math.sin(time * 5);
            ctx.fillStyle = '#f56565';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('↓ 取后5位', arrowX, arrowY);

            // 最终结果动画
            const resultScale = 1 + 0.3 * Math.sin(time * 2);
            ctx.save();
            ctx.translate(canvas.width/2, 250);
            ctx.scale(resultScale, resultScale);

            // 结果框
            ctx.fillStyle = '#48bb78';
            ctx.fillRect(-120, -40, 240, 80);
            ctx.strokeStyle = '#38a169';
            ctx.lineWidth = 4;
            ctx.strokeRect(-120, -40, 240, 80);

            // 结果文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 36px Arial';
            ctx.fillText('11001', 0, 12);
            ctx.restore();

            // 答案确认
            ctx.font = 'bold 18px Arial';
            ctx.fillStyle = '#764ba2';
            ctx.fillText('答案：B', canvas.width/2, 320);

            // 庆祝粒子
            for (let i = 0; i < 15; i++) {
                const angle = time + i * 0.4;
                const radius = 100 + 30 * Math.sin(time * 2 + i);
                const x = canvas.width/2 + radius * Math.cos(angle);
                const y = 350 + radius * Math.sin(angle) * 0.5;
                const size = 3 + 2 * Math.sin(time * 3 + i);

                ctx.fillStyle = `hsl(${(time * 100 + i * 25) % 360}, 80%, 60%)`;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }

            // 成功提示
            const successPulse = 1 + 0.1 * Math.sin(time * 6);
            ctx.save();
            ctx.translate(canvas.width/2, 420);
            ctx.scale(successPulse, successPulse);
            ctx.fillStyle = '#48bb78';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('🎉 计算完成！', 0, 0);
            ctx.restore();
        }

        // 控制函数
        function startDemo() {
            currentStep = 0;
            updateProgress();
            updateButtonStates();
            nextStep();
        }

        function nextStep() {
            if (currentStep < totalSteps - 1) {
                currentStep++;
                updateStepIndicator();
                updateProgress();
                drawStep(currentStep);
                updateExplanation();
                updateButtonStates();

                if (currentStep < totalSteps - 1) {
                    animateStep();
                }
            }
        }

        function prevStep() {
            if (currentStep > 0) {
                currentStep--;
                updateStepIndicator();
                updateProgress();
                drawStep(currentStep);
                updateExplanation();
                updateButtonStates();
                animateStep();
            }
        }

        function resetDemo() {
            currentStep = 0;
            updateStepIndicator();
            updateProgress();
            updateButtonStates();
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('stepExplanation').innerHTML =
                '<p>🚀 点击开始，看动画学CRC！</p>';
        }

        function updateStepIndicator() {
            for (let i = 0; i < totalSteps; i++) {
                const step = document.getElementById(`step${i}`);
                if (step) {
                    step.classList.remove('active', 'completed');
                    if (i === currentStep) {
                        step.classList.add('active');
                    } else if (i < currentStep) {
                        step.classList.add('completed');
                    }
                }
            }
        }

        function updateProgress() {
            const progress = (currentStep / (totalSteps - 1)) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }

        function updateButtonStates() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            // 上一步按钮状态
            if (currentStep <= 0) {
                prevBtn.disabled = true;
                prevBtn.style.opacity = '0.5';
                prevBtn.style.cursor = 'not-allowed';
            } else {
                prevBtn.disabled = false;
                prevBtn.style.opacity = '1';
                prevBtn.style.cursor = 'pointer';
            }

            // 下一步按钮状态
            if (currentStep >= totalSteps - 1) {
                nextBtn.disabled = true;
                nextBtn.style.opacity = '0.5';
                nextBtn.style.cursor = 'not-allowed';
                nextBtn.innerHTML = '✅ 已完成';
            } else {
                nextBtn.disabled = false;
                nextBtn.style.opacity = '1';
                nextBtn.style.cursor = 'pointer';
                nextBtn.innerHTML = '➡️ 下一步';
            }
        }

        function updateExplanation() {
            const explanations = [
                '<p>🚀 <strong>开始：</strong>准备学习CRC校验码</p>',
                '<p>� <strong>1.1：</strong>理解什么是CRC</p>',
                '<p>📐 <strong>1.2：</strong>分析生成多项式</p>',
                '<p>🔄 <strong>1.3：</strong>转换为二进制</p>',
                '<p>🤔 <strong>2.1：</strong>为什么要补零？</p>',
                '<p>➕ <strong>2.2：</strong>执行补零操作</p>',
                '<p>📐 <strong>3.1：</strong>设置除法框架</p>',
                '<p>⚡ <strong>3.2：</strong>学习异或运算</p>',
                '<p>🎯 <strong>4.1：</strong>第一次除法</p>',
                '<p>🔄 <strong>4.2：</strong>继续除法运算</p>',
                '<p>🏁 <strong>4.3：</strong>完成所有运算</p>',
                '<p>🎉 <strong>完成！</strong>CRC = <strong>11001</strong></p>'
            ];

            document.getElementById('stepExplanation').innerHTML = explanations[currentStep];
        }

        function animateStep() {
            let frame = 0;
            function animate() {
                if (frame < 60) {
                    drawStep(currentStep);
                    frame++;
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        function checkAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('B. 11001')) {
                    option.classList.add('correct');
                }
            });
            
            const resultDiv = document.getElementById('quizResult');
            if (isCorrect) {
                resultDiv.innerHTML = '<p style="color: #48bb78; font-weight: bold; margin-top: 20px;">🎉 恭喜你答对了！CRC校验码确实是11001。</p>';
            } else {
                resultDiv.innerHTML = '<p style="color: #f56565; font-weight: bold; margin-top: 20px;">❌ 答案不对哦，正确答案是B. 11001。再看看演示过程吧！</p>';
            }
        }

        // 键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    prevStep();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    nextStep();
                    break;
                case ' ':
                case 'Enter':
                    event.preventDefault();
                    if (currentStep === 0) {
                        startDemo();
                    } else {
                        nextStep();
                    }
                    break;
                case 'Escape':
                    event.preventDefault();
                    resetDemo();
                    break;
            }
        });

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 初始化主要动画
            drawStep(0);
            calculateCRC();
            updateButtonStates();

            // 自动显示CRC计算图解
            setTimeout(function() {
                drawCRCDiagram();
            }, 500);
        });
    </script>
</body>
</html>
