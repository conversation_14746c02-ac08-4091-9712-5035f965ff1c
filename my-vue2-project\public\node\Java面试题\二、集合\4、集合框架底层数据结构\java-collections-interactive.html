<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java集合框架 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e6ed;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #2c3e50;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .game-score {
            text-align: center;
            font-size: 1.5rem;
            color: #667eea;
            font-weight: 600;
            margin: 20px 0;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .interactive-element {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .interactive-element:hover {
            transform: scale(1.1);
            filter: brightness(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Java集合框架</h1>
            <p>通过动画和交互学习Java集合的奥秘</p>
        </div>

        <div class="section">
            <h2 class="section-title">List 列表家族</h2>
            <div class="explanation">
                <p><span class="highlight">List</span> 是有序的集合，允许重复元素。就像一排座位，每个位置都有编号，可以坐相同的人。</p>
            </div>
            <div class="canvas-container">
                <canvas id="listCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="demonstrateArrayList()">ArrayList演示</button>
                <button class="btn" onclick="demonstrateVector()">Vector演示</button>
                <button class="btn" onclick="demonstrateLinkedList()">LinkedList演示</button>
                <button class="btn" onclick="playListGame()">玩游戏</button>
            </div>
            <div class="game-score" id="listScore">得分: 0</div>
        </div>

        <div class="section">
            <h2 class="section-title">Set 集合家族</h2>
            <div class="explanation">
                <p><span class="highlight">Set</span> 是不允许重复元素的集合。就像一个俱乐部，每个成员都是独一无二的。</p>
            </div>
            <div class="canvas-container">
                <canvas id="setCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="demonstrateHashSet()">HashSet演示</button>
                <button class="btn" onclick="demonstrateLinkedHashSet()">LinkedHashSet演示</button>
                <button class="btn" onclick="demonstrateTreeSet()">TreeSet演示</button>
                <button class="btn" onclick="playSetGame()">玩游戏</button>
            </div>
            <div class="game-score" id="setScore">得分: 0</div>
        </div>

        <div class="section">
            <h2 class="section-title">Map 映射家族</h2>
            <div class="explanation">
                <p><span class="highlight">Map</span> 是键值对的集合，每个键都是唯一的。就像一个字典，每个词条都有唯一的解释。</p>
            </div>
            <div class="canvas-container">
                <canvas id="mapCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="demonstrateHashMap()">HashMap演示</button>
                <button class="btn" onclick="demonstrateLinkedHashMap()">LinkedHashMap演示</button>
                <button class="btn" onclick="demonstrateTreeMap()">TreeMap演示</button>
                <button class="btn" onclick="demonstrateHashTable()">HashTable演示</button>
                <button class="btn" onclick="playMapGame()">玩游戏</button>
            </div>
            <div class="game-score" id="mapScore">得分: 0</div>
        </div>
    </div>

    <script>
        // 全局变量
        let listScore = 0;
        let setScore = 0;
        let mapScore = 0;
        let animationId;

        // Canvas上下文
        const listCanvas = document.getElementById('listCanvas');
        const listCtx = listCanvas.getContext('2d');
        const setCanvas = document.getElementById('setCanvas');
        const setCtx = setCanvas.getContext('2d');
        const mapCanvas = document.getElementById('mapCanvas');
        const mapCtx = mapCanvas.getContext('2d');

        // 动画帧计数器
        let frame = 0;

        // 工具函数
        function clearCanvas(ctx, canvas) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawRoundedRect(ctx, x, y, width, height, radius, fillColor, strokeColor) {
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, radius);
            if (fillColor) {
                ctx.fillStyle = fillColor;
                ctx.fill();
            }
            if (strokeColor) {
                ctx.strokeStyle = strokeColor;
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        }

        function drawText(ctx, text, x, y, font, color, align = 'center') {
            ctx.font = font;
            ctx.fillStyle = color;
            ctx.textAlign = align;
            ctx.fillText(text, x, y);
        }

        function animateElement(element, startX, endX, startY, endY, duration, callback) {
            const startTime = Date.now();
            
            function animate() {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // 使用缓动函数
                const easeProgress = 1 - Math.pow(1 - progress, 3);
                
                element.x = startX + (endX - startX) * easeProgress;
                element.y = startY + (endY - startY) * easeProgress;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else if (callback) {
                    callback();
                }
            }
            
            animate();
        }

        // ArrayList演示
        function demonstrateArrayList() {
            clearCanvas(listCtx, listCanvas);
            
            // 绘制标题
            drawText(listCtx, 'ArrayList - 动态数组', 400, 30, '24px Arial', '#2c3e50');
            drawText(listCtx, '基于Object数组实现，支持随机访问', 400, 60, '16px Arial', '#7f8c8d');
            
            // 绘制数组结构
            const elements = ['Java', 'Python', 'C++', 'JavaScript'];
            let animationStep = 0;
            
            function animateArrayList() {
                clearCanvas(listCtx, listCanvas);
                drawText(listCtx, 'ArrayList - 动态数组', 400, 30, '24px Arial', '#2c3e50');
                drawText(listCtx, '基于Object数组实现，支持随机访问', 400, 60, '16px Arial', '#7f8c8d');
                
                // 绘制数组框架
                for (let i = 0; i < 6; i++) {
                    const x = 150 + i * 100;
                    const y = 150;
                    
                    if (i < elements.length && animationStep > i) {
                        // 有数据的格子
                        drawRoundedRect(listCtx, x, y, 80, 60, 10, '#3498db', '#2980b9');
                        drawText(listCtx, elements[i], x + 40, y + 35, '14px Arial', 'white');
                        drawText(listCtx, `[${i}]`, x + 40, y + 50, '12px Arial', 'white');
                    } else {
                        // 空格子
                        drawRoundedRect(listCtx, x, y, 80, 60, 10, '#ecf0f1', '#bdc3c7');
                        drawText(listCtx, 'null', x + 40, y + 35, '14px Arial', '#7f8c8d');
                        drawText(listCtx, `[${i}]`, x + 40, y + 50, '12px Arial', '#7f8c8d');
                    }
                }
                
                // 绘制特性说明
                drawText(listCtx, '特点：', 100, 280, '18px Arial', '#2c3e50', 'left');
                drawText(listCtx, '• 支持索引访问 O(1)', 120, 310, '16px Arial', '#27ae60', 'left');
                drawText(listCtx, '• 插入删除 O(n)', 120, 335, '16px Arial', '#e74c3c', 'left');
                drawText(listCtx, '• 动态扩容', 120, 360, '16px Arial', '#f39c12', 'left');
                
                animationStep++;
                if (animationStep <= elements.length + 2) {
                    setTimeout(animateArrayList, 800);
                }
            }
            
            animateArrayList();
        }

        // Vector演示
        function demonstrateVector() {
            clearCanvas(listCtx, listCanvas);
            
            drawText(listCtx, 'Vector - 线程安全的动态数组', 400, 30, '24px Arial', '#2c3e50');
            drawText(listCtx, '与ArrayList类似，但是线程安全', 400, 60, '16px Arial', '#7f8c8d');
            
            // 绘制同步锁的动画
            let lockAnimation = 0;
            
            function animateVector() {
                clearCanvas(listCtx, listCanvas);
                drawText(listCtx, 'Vector - 线程安全的动态数组', 400, 30, '24px Arial', '#2c3e50');
                drawText(listCtx, '与ArrayList类似，但是线程安全', 400, 60, '16px Arial', '#7f8c8d');
                
                // 绘制数组
                for (let i = 0; i < 4; i++) {
                    const x = 200 + i * 100;
                    const y = 150;
                    drawRoundedRect(listCtx, x, y, 80, 60, 10, '#9b59b6', '#8e44ad');
                    drawText(listCtx, `Item${i+1}`, x + 40, y + 35, '14px Arial', 'white');
                }
                
                // 绘制锁动画
                const lockSize = 30 + Math.sin(lockAnimation * 0.2) * 5;
                listCtx.beginPath();
                listCtx.arc(400, 250, lockSize, 0, 2 * Math.PI);
                listCtx.fillStyle = '#e74c3c';
                listCtx.fill();
                drawText(listCtx, '🔒', 400, 255, '20px Arial', 'white');
                
                drawText(listCtx, '线程安全保护', 400, 290, '16px Arial', '#e74c3c');
                
                lockAnimation++;
                if (lockAnimation < 50) {
                    requestAnimationFrame(animateVector);
                }
            }
            
            animateVector();
        }

        // LinkedList演示
        function demonstrateLinkedList() {
            clearCanvas(listCtx, listCanvas);
            
            drawText(listCtx, 'LinkedList - 双向链表', 400, 30, '24px Arial', '#2c3e50');
            drawText(listCtx, '每个节点包含数据和前后指针', 400, 60, '16px Arial', '#7f8c8d');
            
            const nodes = ['Node1', 'Node2', 'Node3', 'Node4'];
            let step = 0;
            
            function animateLinkedList() {
                clearCanvas(listCtx, listCanvas);
                drawText(listCtx, 'LinkedList - 双向链表', 400, 30, '24px Arial', '#2c3e50');
                drawText(listCtx, '每个节点包含数据和前后指针', 400, 60, '16px Arial', '#7f8c8d');
                
                for (let i = 0; i < nodes.length && i <= step; i++) {
                    const x = 100 + i * 150;
                    const y = 150;
                    
                    // 绘制节点
                    drawRoundedRect(listCtx, x, y, 120, 80, 15, '#1abc9c', '#16a085');
                    drawText(listCtx, nodes[i], x + 60, y + 35, '14px Arial', 'white');
                    drawText(listCtx, 'prev | next', x + 60, y + 55, '10px Arial', 'white');
                    
                    // 绘制箭头
                    if (i < nodes.length - 1 && i < step) {
                        // 向右箭头
                        listCtx.beginPath();
                        listCtx.moveTo(x + 120, y + 40);
                        listCtx.lineTo(x + 140, y + 40);
                        listCtx.moveTo(x + 135, y + 35);
                        listCtx.lineTo(x + 140, y + 40);
                        listCtx.lineTo(x + 135, y + 45);
                        listCtx.strokeStyle = '#e74c3c';
                        listCtx.lineWidth = 3;
                        listCtx.stroke();
                    }
                    
                    if (i > 0) {
                        // 向左箭头
                        listCtx.beginPath();
                        listCtx.moveTo(x, y + 60);
                        listCtx.lineTo(x - 20, y + 60);
                        listCtx.moveTo(x - 15, y + 55);
                        listCtx.lineTo(x - 20, y + 60);
                        listCtx.lineTo(x - 15, y + 65);
                        listCtx.strokeStyle = '#3498db';
                        listCtx.lineWidth = 3;
                        listCtx.stroke();
                    }
                }
                
                step++;
                if (step <= nodes.length + 1) {
                    setTimeout(animateLinkedList, 1000);
                }
            }
            
            animateLinkedList();
        }

        // List游戏
        function playListGame() {
            clearCanvas(listCtx, listCanvas);
            
            drawText(listCtx, '🎮 List类型识别游戏', 400, 30, '24px Arial', '#2c3e50');
            drawText(listCtx, '点击正确的List实现类型！', 400, 60, '16px Arial', '#7f8c8d');
            
            const scenarios = [
                { question: '需要频繁随机访问元素', answer: 'ArrayList', options: ['ArrayList', 'LinkedList', 'Vector'] },
                { question: '需要线程安全', answer: 'Vector', options: ['ArrayList', 'LinkedList', 'Vector'] },
                { question: '频繁插入删除中间元素', answer: 'LinkedList', options: ['ArrayList', 'LinkedList', 'Vector'] }
            ];
            
            let currentQuestion = 0;
            
            function showQuestion() {
                if (currentQuestion >= scenarios.length) {
                    drawText(listCtx, '🎉 游戏完成！', 400, 200, '32px Arial', '#27ae60');
                    drawText(listCtx, `最终得分: ${listScore}`, 400, 240, '24px Arial', '#2c3e50');
                    return;
                }
                
                const scenario = scenarios[currentQuestion];
                clearCanvas(listCtx, listCanvas);
                
                drawText(listCtx, '🎮 List类型识别游戏', 400, 30, '24px Arial', '#2c3e50');
                drawText(listCtx, scenario.question, 400, 100, '20px Arial', '#2c3e50');
                
                // 绘制选项按钮
                scenario.options.forEach((option, index) => {
                    const x = 200 + index * 150;
                    const y = 200;
                    
                    drawRoundedRect(listCtx, x, y, 120, 60, 15, '#3498db', '#2980b9');
                    drawText(listCtx, option, x + 60, y + 35, '16px Arial', 'white');
                });
                
                // 添加点击事件
                listCanvas.onclick = function(event) {
                    const rect = listCanvas.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;
                    
                    scenario.options.forEach((option, index) => {
                        const btnX = 200 + index * 150;
                        const btnY = 200;
                        
                        if (x >= btnX && x <= btnX + 120 && y >= btnY && y <= btnY + 60) {
                            if (option === scenario.answer) {
                                listScore += 10;
                                document.getElementById('listScore').textContent = `得分: ${listScore}`;
                                
                                // 显示正确动画
                                drawText(listCtx, '✅ 正确！', 400, 300, '24px Arial', '#27ae60');
                            } else {
                                drawText(listCtx, '❌ 错误！', 400, 300, '24px Arial', '#e74c3c');
                            }
                            
                            currentQuestion++;
                            setTimeout(showQuestion, 1500);
                        }
                    });
                };
            }
            
            showQuestion();
        }

        // HashSet演示
        function demonstrateHashSet() {
            clearCanvas(setCtx, setCanvas);

            drawText(setCtx, 'HashSet - 基于HashMap的无序唯一集合', 400, 30, '24px Arial', '#2c3e50');
            drawText(setCtx, '底层使用哈希表，查找速度快O(1)', 400, 60, '16px Arial', '#7f8c8d');

            const elements = ['Java', 'Python', 'Java', 'C++', 'Python', 'Go'];
            const uniqueElements = [...new Set(elements)];
            let step = 0;

            function animateHashSet() {
                clearCanvas(setCtx, setCanvas);
                drawText(setCtx, 'HashSet - 基于HashMap的无序唯一集合', 400, 30, '24px Arial', '#2c3e50');
                drawText(setCtx, '底层使用哈希表，查找速度快O(1)', 400, 60, '16px Arial', '#7f8c8d');

                // 显示输入序列
                drawText(setCtx, '输入序列:', 50, 120, '18px Arial', '#2c3e50', 'left');
                for (let i = 0; i < elements.length && i <= step; i++) {
                    const x = 50 + i * 80;
                    const y = 140;
                    const isDuplicate = elements.slice(0, i).includes(elements[i]);

                    drawRoundedRect(setCtx, x, y, 70, 40, 8,
                        isDuplicate ? '#e74c3c' : '#3498db',
                        isDuplicate ? '#c0392b' : '#2980b9');
                    drawText(setCtx, elements[i], x + 35, y + 25, '12px Arial', 'white');

                    if (isDuplicate && i === step) {
                        drawText(setCtx, '重复!', x + 35, y + 60, '12px Arial', '#e74c3c');
                    }
                }

                // 显示HashSet结果
                drawText(setCtx, 'HashSet结果:', 50, 220, '18px Arial', '#2c3e50', 'left');
                const currentUnique = [...new Set(elements.slice(0, step + 1))];
                for (let i = 0; i < currentUnique.length; i++) {
                    const x = 50 + i * 100;
                    const y = 240;

                    drawRoundedRect(setCtx, x, y, 90, 50, 10, '#27ae60', '#229954');
                    drawText(setCtx, currentUnique[i], x + 45, y + 30, '14px Arial', 'white');
                }

                // 显示哈希桶概念
                if (step >= elements.length) {
                    drawText(setCtx, '哈希桶结构:', 50, 320, '18px Arial', '#2c3e50', 'left');
                    for (let i = 0; i < 4; i++) {
                        const x = 50 + i * 150;
                        const y = 340;
                        drawRoundedRect(setCtx, x, y, 120, 30, 5, '#f39c12', '#e67e22');
                        drawText(setCtx, `Bucket ${i}`, x + 60, y + 20, '12px Arial', 'white');
                    }
                }

                step++;
                if (step <= elements.length + 2) {
                    setTimeout(animateHashSet, 1000);
                }
            }

            animateHashSet();
        }

        // LinkedHashSet演示
        function demonstrateLinkedHashSet() {
            clearCanvas(setCtx, setCanvas);

            drawText(setCtx, 'LinkedHashSet - 保持插入顺序的HashSet', 400, 30, '24px Arial', '#2c3e50');
            drawText(setCtx, '在HashSet基础上增加双向链表维护顺序', 400, 60, '16px Arial', '#7f8c8d');

            const elements = ['C++', 'Java', 'Python', 'Go'];
            let step = 0;

            function animateLinkedHashSet() {
                clearCanvas(setCtx, setCanvas);
                drawText(setCtx, 'LinkedHashSet - 保持插入顺序的HashSet', 400, 30, '24px Arial', '#2c3e50');
                drawText(setCtx, '在HashSet基础上增加双向链表维护顺序', 400, 60, '16px Arial', '#7f8c8d');

                // 绘制哈希表
                drawText(setCtx, '哈希表:', 50, 120, '18px Arial', '#2c3e50', 'left');
                for (let i = 0; i < 4; i++) {
                    const x = 50 + i * 150;
                    const y = 140;

                    if (i < step) {
                        drawRoundedRect(setCtx, x, y, 120, 60, 10, '#9b59b6', '#8e44ad');
                        drawText(setCtx, elements[i], x + 60, y + 35, '14px Arial', 'white');
                    } else {
                        drawRoundedRect(setCtx, x, y, 120, 60, 10, '#ecf0f1', '#bdc3c7');
                        drawText(setCtx, 'Empty', x + 60, y + 35, '14px Arial', '#7f8c8d');
                    }
                }

                // 绘制链表连接
                drawText(setCtx, '插入顺序链表:', 50, 250, '18px Arial', '#2c3e50', 'left');
                for (let i = 0; i < step; i++) {
                    const x = 100 + i * 120;
                    const y = 270;

                    drawRoundedRect(setCtx, x, y, 100, 40, 8, '#1abc9c', '#16a085');
                    drawText(setCtx, elements[i], x + 50, y + 25, '14px Arial', 'white');

                    if (i < step - 1) {
                        // 绘制箭头
                        setCtx.beginPath();
                        setCtx.moveTo(x + 100, y + 20);
                        setCtx.lineTo(x + 115, y + 20);
                        setCtx.moveTo(x + 110, y + 15);
                        setCtx.lineTo(x + 115, y + 20);
                        setCtx.lineTo(x + 110, y + 25);
                        setCtx.strokeStyle = '#e74c3c';
                        setCtx.lineWidth = 2;
                        setCtx.stroke();
                    }
                }

                step++;
                if (step <= elements.length + 1) {
                    setTimeout(animateLinkedHashSet, 1200);
                }
            }

            animateLinkedHashSet();
        }

        // TreeSet演示
        function demonstrateTreeSet() {
            clearCanvas(setCtx, setCanvas);

            drawText(setCtx, 'TreeSet - 基于红黑树的有序集合', 400, 30, '24px Arial', '#2c3e50');
            drawText(setCtx, '自动排序，查找插入删除O(log n)', 400, 60, '16px Arial', '#7f8c8d');

            const elements = [50, 30, 70, 20, 40, 60, 80];
            let step = 0;

            function animateTreeSet() {
                clearCanvas(setCtx, setCanvas);
                drawText(setCtx, 'TreeSet - 基于红黑树的有序集合', 400, 30, '24px Arial', '#2c3e50');
                drawText(setCtx, '自动排序，查找插入删除O(log n)', 400, 60, '16px Arial', '#7f8c8d');

                // 绘制二叉树结构
                if (step > 0) {
                    // 根节点
                    drawCircle(setCtx, 400, 120, 25, '#e74c3c', 'white', elements[0].toString());
                }

                if (step > 1) {
                    // 左子树
                    drawCircle(setCtx, 300, 180, 20, '#3498db', 'white', elements[1].toString());
                    drawLine(setCtx, 380, 140, 320, 160);
                }

                if (step > 2) {
                    // 右子树
                    drawCircle(setCtx, 500, 180, 20, '#3498db', 'white', elements[2].toString());
                    drawLine(setCtx, 420, 140, 480, 160);
                }

                if (step > 3) {
                    // 更多节点
                    drawCircle(setCtx, 250, 240, 18, '#27ae60', 'white', elements[3].toString());
                    drawLine(setCtx, 285, 195, 265, 225);
                }

                if (step > 4) {
                    drawCircle(setCtx, 350, 240, 18, '#27ae60', 'white', elements[4].toString());
                    drawLine(setCtx, 315, 195, 335, 225);
                }

                if (step > 5) {
                    drawCircle(setCtx, 450, 240, 18, '#27ae60', 'white', elements[5].toString());
                    drawLine(setCtx, 485, 195, 465, 225);
                }

                if (step > 6) {
                    drawCircle(setCtx, 550, 240, 18, '#27ae60', 'white', elements[6].toString());
                    drawLine(setCtx, 515, 195, 535, 225);
                }

                // 显示排序结果
                if (step > elements.length) {
                    drawText(setCtx, '中序遍历结果（有序）:', 50, 320, '18px Arial', '#2c3e50', 'left');
                    const sorted = [...elements].slice(0, step - 1).sort((a, b) => a - b);
                    for (let i = 0; i < sorted.length; i++) {
                        const x = 50 + i * 60;
                        const y = 340;
                        drawRoundedRect(setCtx, x, y, 50, 30, 5, '#f39c12', '#e67e22');
                        drawText(setCtx, sorted[i].toString(), x + 25, y + 20, '14px Arial', 'white');
                    }
                }

                step++;
                if (step <= elements.length + 3) {
                    setTimeout(animateTreeSet, 800);
                }
            }

            animateTreeSet();
        }

        // 辅助函数
        function drawCircle(ctx, x, y, radius, fillColor, textColor, text) {
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, 2 * Math.PI);
            ctx.fillStyle = fillColor;
            ctx.fill();
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.fillStyle = textColor;
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 5);
        }

        function drawLine(ctx, x1, y1, x2, y2) {
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        // Set游戏
        function playSetGame() {
            clearCanvas(setCtx, setCanvas);

            drawText(setCtx, '🎮 Set特性识别游戏', 400, 30, '24px Arial', '#2c3e50');
            drawText(setCtx, '选择最适合的Set实现！', 400, 60, '16px Arial', '#7f8c8d');

            const scenarios = [
                { question: '需要快速查找，不关心顺序', answer: 'HashSet', options: ['HashSet', 'LinkedHashSet', 'TreeSet'] },
                { question: '需要保持插入顺序', answer: 'LinkedHashSet', options: ['HashSet', 'LinkedHashSet', 'TreeSet'] },
                { question: '需要自动排序', answer: 'TreeSet', options: ['HashSet', 'LinkedHashSet', 'TreeSet'] }
            ];

            let currentQuestion = 0;

            function showSetQuestion() {
                if (currentQuestion >= scenarios.length) {
                    drawText(setCtx, '🎉 游戏完成！', 400, 200, '32px Arial', '#27ae60');
                    drawText(setCtx, `最终得分: ${setScore}`, 400, 240, '24px Arial', '#2c3e50');
                    return;
                }

                const scenario = scenarios[currentQuestion];
                clearCanvas(setCtx, setCanvas);

                drawText(setCtx, '🎮 Set特性识别游戏', 400, 30, '24px Arial', '#2c3e50');
                drawText(setCtx, scenario.question, 400, 100, '20px Arial', '#2c3e50');

                scenario.options.forEach((option, index) => {
                    const x = 150 + index * 170;
                    const y = 200;

                    drawRoundedRect(setCtx, x, y, 150, 60, 15, '#9b59b6', '#8e44ad');
                    drawText(setCtx, option, x + 75, y + 35, '14px Arial', 'white');
                });

                setCanvas.onclick = function(event) {
                    const rect = setCanvas.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    scenario.options.forEach((option, index) => {
                        const btnX = 150 + index * 170;
                        const btnY = 200;

                        if (x >= btnX && x <= btnX + 150 && y >= btnY && y <= btnY + 60) {
                            if (option === scenario.answer) {
                                setScore += 10;
                                document.getElementById('setScore').textContent = `得分: ${setScore}`;
                                drawText(setCtx, '✅ 正确！', 400, 300, '24px Arial', '#27ae60');
                            } else {
                                drawText(setCtx, '❌ 错误！', 400, 300, '24px Arial', '#e74c3c');
                            }

                            currentQuestion++;
                            setTimeout(showSetQuestion, 1500);
                        }
                    });
                };
            }

            showSetQuestion();
        }

        // HashMap演示
        function demonstrateHashMap() {
            clearCanvas(mapCtx, mapCanvas);

            drawText(mapCtx, 'HashMap - 基于哈希表的键值对存储', 400, 30, '24px Arial', '#2c3e50');
            drawText(mapCtx, 'JDK1.8后：数组+链表+红黑树', 400, 60, '16px Arial', '#7f8c8d');

            const pairs = [
                { key: 'name', value: 'Java' },
                { key: 'type', value: 'Language' },
                { key: 'year', value: '1995' },
                { key: 'company', value: 'Oracle' }
            ];

            let step = 0;

            function animateHashMap() {
                clearCanvas(mapCtx, mapCanvas);
                drawText(mapCtx, 'HashMap - 基于哈希表的键值对存储', 400, 30, '24px Arial', '#2c3e50');
                drawText(mapCtx, 'JDK1.8后：数组+链表+红黑树', 400, 60, '16px Arial', '#7f8c8d');

                // 绘制哈希桶数组
                drawText(mapCtx, '哈希桶数组:', 50, 100, '18px Arial', '#2c3e50', 'left');
                for (let i = 0; i < 4; i++) {
                    const x = 50 + i * 150;
                    const y = 120;

                    drawRoundedRect(mapCtx, x, y, 130, 40, 8, '#ecf0f1', '#bdc3c7');
                    drawText(mapCtx, `Bucket[${i}]`, x + 65, y + 25, '12px Arial', '#7f8c8d');

                    // 如果有数据，显示键值对
                    if (i < step && i < pairs.length) {
                        const pair = pairs[i];
                        drawRoundedRect(mapCtx, x + 10, y + 50, 110, 60, 8, '#3498db', '#2980b9');
                        drawText(mapCtx, `${pair.key}`, x + 65, y + 70, '12px Arial', 'white');
                        drawText(mapCtx, `${pair.value}`, x + 65, y + 90, '12px Arial', 'white');

                        // 绘制哈希过程
                        if (i === step - 1) {
                            drawText(mapCtx, `hash("${pair.key}") % 4 = ${i}`, x + 65, y + 130, '10px Arial', '#e74c3c');
                        }
                    }
                }

                // 显示链表转红黑树的概念
                if (step > pairs.length) {
                    drawText(mapCtx, '当链表长度 > 8 时，转换为红黑树:', 50, 250, '16px Arial', '#2c3e50', 'left');

                    // 绘制链表
                    drawText(mapCtx, '链表结构:', 50, 280, '14px Arial', '#7f8c8d', 'left');
                    for (let i = 0; i < 3; i++) {
                        const x = 50 + i * 80;
                        const y = 300;
                        drawRoundedRect(mapCtx, x, y, 70, 30, 5, '#f39c12', '#e67e22');
                        drawText(mapCtx, `Node${i+1}`, x + 35, y + 20, '10px Arial', 'white');

                        if (i < 2) {
                            mapCtx.beginPath();
                            mapCtx.moveTo(x + 70, y + 15);
                            mapCtx.lineTo(x + 75, y + 15);
                            mapCtx.strokeStyle = '#2c3e50';
                            mapCtx.lineWidth = 2;
                            mapCtx.stroke();
                        }
                    }

                    // 绘制转换箭头
                    drawText(mapCtx, '→', 300, 320, '24px Arial', '#e74c3c');

                    // 绘制红黑树
                    drawText(mapCtx, '红黑树结构:', 350, 280, '14px Arial', '#7f8c8d', 'left');
                    drawCircle(mapCtx, 450, 310, 15, '#e74c3c', 'white', 'R');
                    drawCircle(mapCtx, 400, 340, 12, '#2c3e50', 'white', 'B');
                    drawCircle(mapCtx, 500, 340, 12, '#2c3e50', 'white', 'B');
                    drawLine(mapCtx, 440, 320, 410, 330);
                    drawLine(mapCtx, 460, 320, 490, 330);
                }

                step++;
                if (step <= pairs.length + 3) {
                    setTimeout(animateHashMap, 1000);
                }
            }

            animateHashMap();
        }

        // LinkedHashMap演示
        function demonstrateLinkedHashMap() {
            clearCanvas(mapCtx, mapCanvas);

            drawText(mapCtx, 'LinkedHashMap - 保持插入顺序的HashMap', 400, 30, '24px Arial', '#2c3e50');
            drawText(mapCtx, '在HashMap基础上增加双向链表', 400, 60, '16px Arial', '#7f8c8d');

            const pairs = [
                { key: 'first', value: '1st' },
                { key: 'second', value: '2nd' },
                { key: 'third', value: '3rd' }
            ];

            let step = 0;

            function animateLinkedHashMap() {
                clearCanvas(mapCtx, mapCanvas);
                drawText(mapCtx, 'LinkedHashMap - 保持插入顺序的HashMap', 400, 30, '24px Arial', '#2c3e50');
                drawText(mapCtx, '在HashMap基础上增加双向链表', 400, 60, '16px Arial', '#7f8c8d');

                // 绘制HashMap部分
                drawText(mapCtx, 'HashMap结构:', 50, 100, '16px Arial', '#2c3e50', 'left');
                for (let i = 0; i < 3; i++) {
                    const x = 50 + i * 200;
                    const y = 120;

                    if (i < step) {
                        drawRoundedRect(mapCtx, x, y, 180, 50, 10, '#3498db', '#2980b9');
                        drawText(mapCtx, `${pairs[i].key} → ${pairs[i].value}`, x + 90, y + 30, '12px Arial', 'white');
                    } else {
                        drawRoundedRect(mapCtx, x, y, 180, 50, 10, '#ecf0f1', '#bdc3c7');
                        drawText(mapCtx, 'Empty', x + 90, y + 30, '12px Arial', '#7f8c8d');
                    }
                }

                // 绘制双向链表
                drawText(mapCtx, '插入顺序双向链表:', 50, 220, '16px Arial', '#2c3e50', 'left');
                for (let i = 0; i < step; i++) {
                    const x = 100 + i * 150;
                    const y = 250;

                    drawRoundedRect(mapCtx, x, y, 120, 40, 8, '#1abc9c', '#16a085');
                    drawText(mapCtx, pairs[i].key, x + 60, y + 25, '12px Arial', 'white');

                    // 绘制双向箭头
                    if (i < step - 1) {
                        // 向右箭头
                        mapCtx.beginPath();
                        mapCtx.moveTo(x + 120, y + 15);
                        mapCtx.lineTo(x + 135, y + 15);
                        mapCtx.moveTo(x + 130, y + 10);
                        mapCtx.lineTo(x + 135, y + 15);
                        mapCtx.lineTo(x + 130, y + 20);
                        mapCtx.strokeStyle = '#e74c3c';
                        mapCtx.lineWidth = 2;
                        mapCtx.stroke();

                        // 向左箭头
                        mapCtx.beginPath();
                        mapCtx.moveTo(x + 135, y + 25);
                        mapCtx.lineTo(x + 120, y + 25);
                        mapCtx.moveTo(x + 125, y + 20);
                        mapCtx.lineTo(x + 120, y + 25);
                        mapCtx.lineTo(x + 125, y + 30);
                        mapCtx.strokeStyle = '#3498db';
                        mapCtx.lineWidth = 2;
                        mapCtx.stroke();
                    }
                }

                // 显示访问顺序功能
                if (step > pairs.length) {
                    drawText(mapCtx, '支持访问顺序模式 (accessOrder=true)', 50, 340, '16px Arial', '#9b59b6', 'left');
                    drawText(mapCtx, '最近访问的元素会移到链表末尾', 50, 365, '14px Arial', '#7f8c8d', 'left');
                }

                step++;
                if (step <= pairs.length + 2) {
                    setTimeout(animateLinkedHashMap, 1200);
                }
            }

            animateLinkedHashMap();
        }

        // TreeMap演示
        function demonstrateTreeMap() {
            clearCanvas(mapCtx, mapCanvas);

            drawText(mapCtx, 'TreeMap - 基于红黑树的有序映射', 400, 30, '24px Arial', '#2c3e50');
            drawText(mapCtx, '键自动排序，操作复杂度O(log n)', 400, 60, '16px Arial', '#7f8c8d');

            const pairs = [
                { key: 'dog', value: '狗' },
                { key: 'cat', value: '猫' },
                { key: 'bird', value: '鸟' },
                { key: 'fish', value: '鱼' }
            ];

            let step = 0;

            function animateTreeMap() {
                clearCanvas(mapCtx, mapCanvas);
                drawText(mapCtx, 'TreeMap - 基于红黑树的有序映射', 400, 30, '24px Arial', '#2c3e50');
                drawText(mapCtx, '键自动排序，操作复杂度O(log n)', 400, 60, '16px Arial', '#7f8c8d');

                // 绘制红黑树结构
                if (step > 0) {
                    // 根节点 (dog)
                    drawMapNode(mapCtx, 400, 120, pairs[0], '#e74c3c');
                }

                if (step > 1) {
                    // 左子树 (cat)
                    drawMapNode(mapCtx, 300, 180, pairs[1], '#2c3e50');
                    drawLine(mapCtx, 380, 140, 320, 160);
                }

                if (step > 2) {
                    // 左子树的左子树 (bird)
                    drawMapNode(mapCtx, 250, 240, pairs[2], '#e74c3c');
                    drawLine(mapCtx, 285, 200, 265, 220);
                }

                if (step > 3) {
                    // 右子树 (fish)
                    drawMapNode(mapCtx, 500, 180, pairs[3], '#2c3e50');
                    drawLine(mapCtx, 420, 140, 480, 160);
                }

                // 显示排序结果
                if (step > pairs.length) {
                    drawText(mapCtx, '按键排序的结果:', 50, 320, '18px Arial', '#2c3e50', 'left');
                    const sorted = [...pairs].sort((a, b) => a.key.localeCompare(b.key));
                    for (let i = 0; i < sorted.length; i++) {
                        const x = 50 + i * 150;
                        const y = 340;
                        drawRoundedRect(mapCtx, x, y, 130, 40, 8, '#27ae60', '#229954');
                        drawText(mapCtx, `${sorted[i].key}→${sorted[i].value}`, x + 65, y + 25, '12px Arial', 'white');
                    }
                }

                step++;
                if (step <= pairs.length + 2) {
                    setTimeout(animateTreeMap, 1000);
                }
            }

            animateTreeMap();
        }

        // HashTable演示
        function demonstrateHashTable() {
            clearCanvas(mapCtx, mapCanvas);

            drawText(mapCtx, 'HashTable - 线程安全的哈希表', 400, 30, '24px Arial', '#2c3e50');
            drawText(mapCtx, '所有方法都是同步的，性能较HashMap低', 400, 60, '16px Arial', '#7f8c8d');

            const pairs = [
                { key: 'user1', value: 'Alice' },
                { key: 'user2', value: 'Bob' },
                { key: 'user3', value: 'Charlie' }
            ];

            let step = 0;
            let lockAnimation = 0;

            function animateHashTable() {
                clearCanvas(mapCtx, mapCanvas);
                drawText(mapCtx, 'HashTable - 线程安全的哈希表', 400, 30, '24px Arial', '#2c3e50');
                drawText(mapCtx, '所有方法都是同步的，性能较HashMap低', 400, 60, '16px Arial', '#7f8c8d');

                // 绘制哈希表
                for (let i = 0; i < 3; i++) {
                    const x = 150 + i * 150;
                    const y = 150;

                    if (i < step) {
                        drawRoundedRect(mapCtx, x, y, 130, 60, 10, '#9b59b6', '#8e44ad');
                        drawText(mapCtx, pairs[i].key, x + 65, y + 25, '12px Arial', 'white');
                        drawText(mapCtx, pairs[i].value, x + 65, y + 45, '12px Arial', 'white');
                    } else {
                        drawRoundedRect(mapCtx, x, y, 130, 60, 10, '#ecf0f1', '#bdc3c7');
                        drawText(mapCtx, 'Empty', x + 65, y + 35, '12px Arial', '#7f8c8d');
                    }
                }

                // 绘制同步锁动画
                const lockSize = 25 + Math.sin(lockAnimation * 0.3) * 5;
                mapCtx.beginPath();
                mapCtx.arc(400, 280, lockSize, 0, 2 * Math.PI);
                mapCtx.fillStyle = '#e74c3c';
                mapCtx.fill();
                drawText(mapCtx, '🔒', 400, 285, '20px Arial', 'white');
                drawText(mapCtx, 'synchronized', 400, 320, '16px Arial', '#e74c3c');

                // 显示线程安全特性
                drawText(mapCtx, '特点:', 50, 350, '16px Arial', '#2c3e50', 'left');
                drawText(mapCtx, '• 线程安全', 70, 370, '14px Arial', '#27ae60', 'left');
                drawText(mapCtx, '• 不允许null键值', 250, 370, '14px Arial', '#e74c3c', 'left');
                drawText(mapCtx, '• 性能较HashMap低', 450, 370, '14px Arial', '#f39c12', 'left');

                step++;
                lockAnimation++;
                if (step <= pairs.length + 5) {
                    setTimeout(animateHashTable, 600);
                }
            }

            animateHashTable();
        }

        // 辅助函数：绘制Map节点
        function drawMapNode(ctx, x, y, pair, color) {
            drawCircle(ctx, x, y, 30, color, 'white', '');
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(pair.key, x, y - 5);
            ctx.fillText(pair.value, x, y + 8);
        }

        // Map游戏
        function playMapGame() {
            clearCanvas(mapCtx, mapCanvas);

            drawText(mapCtx, '🎮 Map选择游戏', 400, 30, '24px Arial', '#2c3e50');
            drawText(mapCtx, '根据需求选择最合适的Map！', 400, 60, '16px Arial', '#7f8c8d');

            const scenarios = [
                { question: '需要最快的查找速度', answer: 'HashMap', options: ['HashMap', 'LinkedHashMap', 'TreeMap', 'HashTable'] },
                { question: '需要线程安全', answer: 'HashTable', options: ['HashMap', 'LinkedHashMap', 'TreeMap', 'HashTable'] },
                { question: '需要保持插入顺序', answer: 'LinkedHashMap', options: ['HashMap', 'LinkedHashMap', 'TreeMap', 'HashTable'] },
                { question: '需要键自动排序', answer: 'TreeMap', options: ['HashMap', 'LinkedHashMap', 'TreeMap', 'HashTable'] }
            ];

            let currentQuestion = 0;

            function showMapQuestion() {
                if (currentQuestion >= scenarios.length) {
                    drawText(mapCtx, '🎉 恭喜完成！', 400, 200, '32px Arial', '#27ae60');
                    drawText(mapCtx, `最终得分: ${mapScore}`, 400, 240, '24px Arial', '#2c3e50');
                    return;
                }

                const scenario = scenarios[currentQuestion];
                clearCanvas(mapCtx, mapCanvas);

                drawText(mapCtx, '🎮 Map选择游戏', 400, 30, '24px Arial', '#2c3e50');
                drawText(mapCtx, scenario.question, 400, 100, '20px Arial', '#2c3e50');

                scenario.options.forEach((option, index) => {
                    const x = 50 + index * 180;
                    const y = 200;

                    drawRoundedRect(mapCtx, x, y, 160, 60, 15, '#e67e22', '#d35400');
                    drawText(mapCtx, option, x + 80, y + 35, '12px Arial', 'white');
                });

                mapCanvas.onclick = function(event) {
                    const rect = mapCanvas.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;

                    scenario.options.forEach((option, index) => {
                        const btnX = 50 + index * 180;
                        const btnY = 200;

                        if (x >= btnX && x <= btnX + 160 && y >= btnY && y <= btnY + 60) {
                            if (option === scenario.answer) {
                                mapScore += 10;
                                document.getElementById('mapScore').textContent = `得分: ${mapScore}`;
                                drawText(mapCtx, '✅ 正确！', 400, 300, '24px Arial', '#27ae60');
                            } else {
                                drawText(mapCtx, '❌ 错误！', 400, 300, '24px Arial', '#e74c3c');
                            }

                            currentQuestion++;
                            setTimeout(showMapQuestion, 1500);
                        }
                    });
                };
            }

            showMapQuestion();
        }
    </script>
</body>
</html>
