<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sub- 词缀故事：地下探险家的深度之旅</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(180deg, #87CEEB 0%, #4682B4 30%, #2F4F4F 60%, #1C1C1C 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 地下层次背景效果 */
        .underground-layers {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .layer {
            position: absolute;
            width: 100%;
            height: 25%;
            opacity: 0.1;
            animation: layerShift 8s infinite ease-in-out;
        }

        .layer:nth-child(1) { top: 25%; background: #8B4513; animation-delay: 0s; }
        .layer:nth-child(2) { top: 50%; background: #654321; animation-delay: 2s; }
        .layer:nth-child(3) { top: 75%; background: #2F1B14; animation-delay: 4s; }

        @keyframes layerShift {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(10px); }
        }

        /* 漂浮的地下元素 */
        .underground-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2;
        }

        .element {
            position: absolute;
            font-size: 1.5rem;
            opacity: 0.3;
            animation: float 6s infinite ease-in-out;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
            z-index: 10;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.5);
            animation: fadeInDown 1.2s ease-out;
            position: relative;
            font-weight: 300;
        }

        .title::before {
            content: '⛏️';
            position: absolute;
            left: -80px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2.5rem;
            animation: swing 2s infinite ease-in-out;
        }

        .title::after {
            content: '🕳️';
            position: absolute;
            right: -80px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes swing {
            0%, 100% { transform: translateY(-50%) rotate(-10deg); }
            50% { transform: translateY(-50%) rotate(10deg); }
        }

        .subtitle {
            text-align: center;
            color: rgba(255,255,255,0.9);
            font-size: 1.4rem;
            margin-bottom: 50px;
            animation: fadeInUp 1.2s ease-out 0.3s both;
            font-weight: 300;
            letter-spacing: 1px;
        }

        .story-canvas {
            background: rgba(255,255,255,0.98);
            border-radius: 30px;
            box-shadow: 
                0 30px 60px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(255,255,255,0.3);
            margin-bottom: 50px;
            overflow: hidden;
            animation: slideInUp 1.2s ease-out 0.6s both;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255,255,255,0.1);
            position: relative;
        }

        .depth-indicator {
            position: absolute;
            top: 20px;
            left: 20px;
            background: linear-gradient(45deg, #8B4513, #654321);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 0.9rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 100;
        }

        canvas {
            display: block;
            width: 100%;
            height: 500px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 60px;
            animation: fadeIn 1.2s ease-out 1.2s both;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #8B4513, #A0522D);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 10px 30px rgba(139, 69, 19, 0.3);
            position: relative;
            overflow: hidden;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 20px 40px rgba(139, 69, 19, 0.4);
        }

        .btn:active {
            transform: translateY(-4px) scale(1.02);
        }

        .explanation {
            background: rgba(255,255,255,0.98);
            border-radius: 30px;
            padding: 50px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            margin-bottom: 40px;
            animation: slideInUp 1.2s ease-out 1s both;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255,255,255,0.1);
        }

        .explanation h2 {
            color: #2F4F4F;
            margin-bottom: 30px;
            font-size: 2rem;
            position: relative;
            padding-left: 30px;
            font-weight: 300;
        }

        .explanation h2::before {
            content: '🔍';
            position: absolute;
            left: 0;
            top: 0;
            font-size: 1.8rem;
        }

        .explanation p {
            color: #555;
            line-height: 2;
            font-size: 1.2rem;
            margin-bottom: 25px;
        }

        .word-card {
            background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #654321 100%);
            color: white;
            padding: 30px;
            border-radius: 25px;
            margin: 25px 0;
            cursor: pointer;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform: translateY(40px);
            opacity: 0;
            position: relative;
            overflow: hidden;
        }

        .word-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.15), transparent);
            transform: translateX(-100%);
            transition: transform 0.8s;
        }

        .word-card:hover::before {
            transform: translateX(100%);
        }

        .word-card.show {
            transform: translateY(0);
            opacity: 1;
        }

        .word-card:hover {
            transform: scale(1.05) translateY(-8px);
            box-shadow: 0 25px 50px rgba(139, 69, 19, 0.4);
        }

        .prefix {
            font-size: 1.8rem;
            font-weight: bold;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .meaning {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.95);
            margin-top: 15px;
            font-weight: 300;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(80px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-50%) scale(1.3); }
        }

        .interactive-area {
            background: rgba(255,255,255,0.98);
            border-radius: 30px;
            padding: 50px;
            margin-top: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255,255,255,0.1);
        }

        .quiz-btn {
            background: linear-gradient(45deg, #2F4F4F, #4682B4);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 35px;
            margin: 10px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-size: 1.05rem;
            font-weight: 500;
        }

        .quiz-btn:hover {
            transform: scale(1.1) translateY(-3px);
            box-shadow: 0 12px 25px rgba(47, 79, 79, 0.4);
        }

        .correct {
            background: linear-gradient(45deg, #228B22, #32CD32) !important;
            animation: correctBounce 0.8s ease;
        }

        .wrong {
            background: linear-gradient(45deg, #DC143C, #FF6347) !important;
            animation: wrongShake 0.8s ease;
        }

        @keyframes correctBounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.15); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }
    </style>
</head>
<body>
    <!-- 地下层次背景 -->
    <div class="underground-layers">
        <div class="layer"></div>
        <div class="layer"></div>
        <div class="layer"></div>
    </div>

    <!-- 地下漂浮元素 -->
    <div class="underground-elements" id="undergroundElements"></div>

    <div class="container">
        <h1 class="title">Sub- 词缀探险课堂</h1>
        <p class="subtitle">跟随地下探险家探索"下面、次级"的神秘世界</p>
        
        <div class="story-canvas">
            <div class="depth-indicator" id="depthIndicator">地表 0m</div>
            <canvas id="storyCanvas" width="1200" height="500"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="startStory()">🚀 开始探险</button>
            <button class="btn" onclick="nextScene()">⬇️ 深入地下</button>
            <button class="btn" onclick="prevScene()">⬆️ 返回上层</button>
            <button class="btn" onclick="resetStory()">🔄 重新探险</button>
        </div>

        <div class="explanation">
            <h2>为什么选择"地下探险家"的故事？</h2>
            <p>
                我选择用"地下探险家"来讲解 <span class="prefix">sub-</span> 词缀，是因为这个词缀的核心含义就是"在下面、次级、副的"。
                探险家不断向地下深入的过程，完美体现了"sub-"的空间和层次概念。从地表到地下，从主要到次要，
                每一层的探索都让你更深入地理解"下面"和"次级"的含义。
            </p>
            <p>
                通过探险家在不同深度的发现，你可以直观地理解submarine(潜水艇在海下)、subway(地铁在地下)、
                subtitle(副标题在主标题下)等词汇的构成逻辑，让抽象的空间概念变得生动具体。
            </p>
            
            <div id="wordCards">
                <!-- 词汇卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="interactive-area">
            <h3 style="color: #2F4F4F; margin-bottom: 30px; font-size: 1.6rem;">🎯 深度测试：选择正确的翻译</h3>
            <div id="quizArea">
                <!-- 测试题目将通过JavaScript生成 -->
            </div>
            
            <div style="margin-top: 50px; padding-top: 40px; border-top: 3px solid #eee;">
                <h3 style="color: #2F4F4F; margin-bottom: 25px;">🔊 发音练习</h3>
                <p style="color: #666; margin-bottom: 25px;">点击下面的按钮听发音，感受地下世界的回音：</p>
                <div id="pronunciationArea" style="display: flex; flex-wrap: wrap; gap: 15px; justify-content: center;">
                    <!-- 发音按钮将通过JavaScript生成 -->
                </div>
            </div>
            
            <div style="margin-top: 50px; padding-top: 40px; border-top: 3px solid #eee;">
                <h3 style="color: #2F4F4F; margin-bottom: 25px;">💡 探险记忆法</h3>
                <div style="background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #654321 100%); 
                           color: white; padding: 30px; border-radius: 25px; line-height: 2;">
                    <p><strong>🎯 记忆技巧：</strong></p>
                    <p>⛏️ <strong>sub-</strong> = "在下面、次级、副的"</p>
                    <p>🕳️ 想象一个探险家总是在"地下"探索</p>
                    <p>📝 每次看到 sub- 开头的单词，就想到"位置在下面"或"级别较低"</p>
                    <p>🎯 练习方法：遇到新单词时，问自己"这个东西是在下面的吗？"</p>
                    <p>🌟 常见搭配：sub- + 名词 = 在某物下面的东西</p>
                    <p>⚡ 空间记忆：submarine(海下)、subway(地下)、subterranean(地下的)</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建地下漂浮元素
        function createUndergroundElements() {
            const container = document.getElementById('undergroundElements');
            const elements = ['💎', '🪨', '⚱️', '🦴', '🗿', '💰', '🔮', '⚒️'];
            
            for (let i = 0; i < 20; i++) {
                const element = document.createElement('div');
                element.className = 'element';
                element.textContent = elements[Math.floor(Math.random() * elements.length)];
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(element);
            }
        }

        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        let currentScene = 0;
        let animationFrame = 0;
        let isAnimating = false;

        // 故事场景数据
        const scenes = [
            {
                title: "地表准备",
                description: "探险家准备下潜到地下 (submarine)",
                words: ["submarine", "subway", "submerge"],
                depth: "地表 0m",
                bgColor: "#87CEEB"
            },
            {
                title: "浅层地下",
                description: "发现地下通道系统 (subway)",
                words: ["subway", "subterranean", "subdivide"],
                depth: "地下 -50m",
                bgColor: "#8B4513"
            },
            {
                title: "深层探索",
                description: "探索次级洞穴结构 (subordinate)",
                words: ["subordinate", "subtitle", "substitute"],
                depth: "地下 -100m",
                bgColor: "#654321"
            },
            {
                title: "最深处",
                description: "发现地下文明的替代品 (substitute)",
                words: ["substitute", "subsequent", "substantial"],
                depth: "地下 -200m",
                bgColor: "#2F1B14"
            }
        ];

        // 词汇数据
        const vocabulary = [
            {
                word: "submarine",
                prefix: "sub-",
                root: "marine (海的)",
                meaning: "潜水艇",
                explanation: "sub(在下面) + marine(海的) = 在海面下面的船",
                sentence: "The submarine dives deep under the ocean. (潜水艇潜入海洋深处。)"
            },
            {
                word: "subway",
                prefix: "sub-",
                root: "way (道路)",
                meaning: "地铁",
                explanation: "sub(在下面) + way(道路) = 在地面下面的道路",
                sentence: "I take the subway to work every day. (我每天坐地铁上班。)"
            },
            {
                word: "subordinate",
                prefix: "sub-",
                root: "ordinate (排列)",
                meaning: "下级的、次要的",
                explanation: "sub(次级) + ordinate(排列) = 在等级排列中较低的",
                sentence: "He is subordinate to the manager. (他是经理的下属。)"
            },
            {
                word: "substitute",
                prefix: "sub-",
                root: "stitute (放置)",
                meaning: "替代品、代替",
                explanation: "sub(代替) + stitute(放置) = 放在原物下面作为替代",
                sentence: "Use honey as a substitute for sugar. (用蜂蜜代替糖。)"
            }
        ];

        function startStory() {
            currentScene = 0;
            animationFrame = 0;
            isAnimating = true;
            updateDepthIndicator();
            drawScene();
            showWordCards();
        }

        function nextScene() {
            if (currentScene < scenes.length - 1) {
                currentScene++;
                animationFrame = 0;
                updateDepthIndicator();
                drawScene();
                updateWordCards();
            }
        }

        function prevScene() {
            if (currentScene > 0) {
                currentScene--;
                animationFrame = 0;
                updateDepthIndicator();
                drawScene();
                updateWordCards();
            }
        }

        function resetStory() {
            currentScene = 0;
            animationFrame = 0;
            isAnimating = false;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('wordCards').innerHTML = '';
            updateDepthIndicator();
        }

        function updateDepthIndicator() {
            document.getElementById('depthIndicator').textContent = scenes[currentScene].depth;
        }

        function drawScene() {
            if (!isAnimating) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制分层背景
            drawLayeredBackground();

            // 绘制探险家
            drawExplorer();

            // 绘制场景特效
            drawSceneEffects();

            // 绘制地下元素
            drawUndergroundElements();

            // 绘制文字说明
            drawSceneText();

            animationFrame++;
            if (isAnimating) {
                requestAnimationFrame(drawScene);
            }
        }

        function drawLayeredBackground() {
            // 根据当前场景绘制不同深度的背景
            const scene = scenes[currentScene];

            // 天空层（只在地表显示）
            if (currentScene === 0) {
                const skyGradient = ctx.createLinearGradient(0, 0, 0, canvas.height * 0.3);
                skyGradient.addColorStop(0, '#87CEEB');
                skyGradient.addColorStop(1, '#4682B4');
                ctx.fillStyle = skyGradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height * 0.3);

                // 绘制云朵
                drawClouds();
            }

            // 地面/地下层
            const groundGradient = ctx.createLinearGradient(0, currentScene === 0 ? canvas.height * 0.3 : 0, 0, canvas.height);
            groundGradient.addColorStop(0, scene.bgColor);
            groundGradient.addColorStop(1, '#1C1C1C');
            ctx.fillStyle = groundGradient;
            ctx.fillRect(0, currentScene === 0 ? canvas.height * 0.3 : 0, canvas.width, canvas.height);

            // 绘制地层纹理
            drawGroundTexture();
        }

        function drawClouds() {
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            for (let i = 0; i < 5; i++) {
                const x = (i * 200 + animationFrame * 0.5) % (canvas.width + 100);
                const y = 50 + Math.sin(animationFrame * 0.01 + i) * 10;

                // 绘制云朵
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, Math.PI * 2);
                ctx.arc(x + 25, y, 25, 0, Math.PI * 2);
                ctx.arc(x + 50, y, 20, 0, Math.PI * 2);
                ctx.arc(x + 25, y - 15, 15, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function drawGroundTexture() {
            // 绘制地层线条
            ctx.strokeStyle = 'rgba(0,0,0,0.2)';
            ctx.lineWidth = 1;

            for (let i = 0; i < 10; i++) {
                const y = (canvas.height / 10) * i + Math.sin(animationFrame * 0.02 + i) * 5;
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }

            // 绘制岩石纹理
            ctx.fillStyle = 'rgba(0,0,0,0.1)';
            for (let i = 0; i < 20; i++) {
                const x = (i * 60 + animationFrame * 0.1) % canvas.width;
                const y = 200 + (i % 3) * 100 + Math.sin(animationFrame * 0.03 + i) * 10;
                const size = 3 + Math.sin(animationFrame * 0.05 + i) * 2;

                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function drawExplorer() {
            const x = 150 + Math.sin(animationFrame * 0.02) * 3;
            const y = 300 + currentScene * 50; // 随着场景深入而下降

            // 探险家身体
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(x - 12, y, 24, 60);

            // 探险家头部
            ctx.fillStyle = '#FDBCB4';
            ctx.beginPath();
            ctx.arc(x, y - 15, 18, 0, Math.PI * 2);
            ctx.fill();

            // 探险帽
            ctx.fillStyle = '#2F4F4F';
            ctx.beginPath();
            ctx.arc(x, y - 15, 20, Math.PI, 0);
            ctx.fill();

            // 头灯
            const lightGlow = Math.sin(animationFrame * 0.1) * 0.3 + 0.7;
            ctx.fillStyle = `rgba(255, 255, 0, ${lightGlow})`;
            ctx.beginPath();
            ctx.arc(x, y - 25, 5, 0, Math.PI * 2);
            ctx.fill();

            // 头灯光束
            ctx.fillStyle = `rgba(255, 255, 0, ${lightGlow * 0.3})`;
            ctx.beginPath();
            ctx.moveTo(x - 5, y - 25);
            ctx.lineTo(x + 200, y - 50);
            ctx.lineTo(x + 200, y);
            ctx.lineTo(x + 5, y - 25);
            ctx.fill();

            // 探险装备
            drawExplorerGear(x, y);
        }

        function drawExplorerGear(x, y) {
            // 背包
            ctx.fillStyle = '#654321';
            ctx.fillRect(x - 20, y + 10, 15, 30);

            // 工具带
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x - 15, y + 20);
            ctx.lineTo(x + 15, y + 20);
            ctx.stroke();

            // 铲子
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x + 20, y + 10);
            ctx.lineTo(x + 35, y - 5);
            ctx.stroke();

            ctx.fillStyle = '#C0C0C0';
            ctx.fillRect(x + 35, y - 10, 8, 10);

            // 绳索
            ctx.strokeStyle = '#DAA520';
            ctx.lineWidth = 2;
            for (let i = 0; i < 5; i++) {
                const ropeY = y + 60 + i * 20;
                ctx.beginPath();
                ctx.moveTo(x - 5, ropeY);
                ctx.lineTo(x + 5, ropeY + 10);
                ctx.stroke();
            }
        }

        function drawSceneEffects() {
            switch(currentScene) {
                case 0:
                    drawSurfaceElements();
                    break;
                case 1:
                    drawShallowUnderground();
                    break;
                case 2:
                    drawDeepCaves();
                    break;
                case 3:
                    drawDeepestLevel();
                    break;
            }
        }

        function drawSurfaceElements() {
            // 绘制地面入口
            ctx.fillStyle = '#2F4F4F';
            ctx.beginPath();
            ctx.ellipse(500, 350, 60, 30, 0, 0, Math.PI * 2);
            ctx.fill();

            // 入口标志
            ctx.fillStyle = '#FFD700';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('SUBWAY', 500, 355);

            // 地面建筑轮廓
            ctx.strokeStyle = 'rgba(47, 79, 79, 0.5)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(700, 150);
            ctx.lineTo(750, 100);
            ctx.lineTo(800, 100);
            ctx.lineTo(850, 150);
            ctx.lineTo(850, 200);
            ctx.lineTo(700, 200);
            ctx.closePath();
            ctx.stroke();
        }

        function drawShallowUnderground() {
            // 绘制地铁隧道
            ctx.strokeStyle = '#C0C0C0';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(600, 300, 80, 0, Math.PI);
            ctx.stroke();

            // 铁轨
            ctx.strokeStyle = '#708090';
            ctx.lineWidth = 3;
            for (let i = 0; i < 3; i++) {
                ctx.beginPath();
                ctx.moveTo(520, 350 + i * 10);
                ctx.lineTo(680, 350 + i * 10);
                ctx.stroke();
            }

            // 移动的地铁车厢
            const trainX = 550 + Math.sin(animationFrame * 0.05) * 30;
            ctx.fillStyle = '#4682B4';
            ctx.fillRect(trainX, 320, 100, 30);

            // 车厢窗户
            ctx.fillStyle = '#87CEEB';
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(trainX + 10 + i * 25, 325, 15, 10);
            }
        }

        function drawDeepCaves() {
            // 绘制洞穴结构
            ctx.fillStyle = 'rgba(47, 79, 79, 0.3)';
            ctx.beginPath();
            ctx.ellipse(600, 250, 120, 80, 0, 0, Math.PI * 2);
            ctx.fill();

            ctx.beginPath();
            ctx.ellipse(750, 350, 80, 60, 0, 0, Math.PI * 2);
            ctx.fill();

            // 钟乳石
            ctx.fillStyle = '#696969';
            for (let i = 0; i < 5; i++) {
                const x = 550 + i * 50;
                const length = 20 + Math.sin(animationFrame * 0.03 + i) * 10;
                ctx.beginPath();
                ctx.moveTo(x, 200);
                ctx.lineTo(x - 5, 200 + length);
                ctx.lineTo(x + 5, 200 + length);
                ctx.closePath();
                ctx.fill();
            }

            // 石笋
            for (let i = 0; i < 4; i++) {
                const x = 600 + i * 40;
                const height = 15 + Math.sin(animationFrame * 0.04 + i) * 8;
                ctx.beginPath();
                ctx.moveTo(x, 400);
                ctx.lineTo(x - 4, 400 - height);
                ctx.lineTo(x + 4, 400 - height);
                ctx.closePath();
                ctx.fill();
            }
        }

        function drawDeepestLevel() {
            // 绘制古代遗迹
            ctx.fillStyle = 'rgba(218, 165, 32, 0.6)';
            ctx.fillRect(500, 300, 200, 100);

            // 古代柱子
            ctx.fillStyle = '#CD853F';
            for (let i = 0; i < 3; i++) {
                const x = 520 + i * 60;
                ctx.fillRect(x, 250, 20, 150);

                // 柱头
                ctx.fillRect(x - 5, 245, 30, 10);
            }

            // 神秘符号
            ctx.fillStyle = '#FFD700';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            const symbols = ['⚱️', '🗿', '💎', '📜'];
            symbols.forEach((symbol, i) => {
                const x = 530 + i * 40;
                const y = 350 + Math.sin(animationFrame * 0.1 + i) * 5;
                ctx.fillText(symbol, x, y);
            });

            // 发光效果
            const glowIntensity = Math.sin(animationFrame * 0.08) * 0.3 + 0.4;
            ctx.fillStyle = `rgba(255, 215, 0, ${glowIntensity})`;
            ctx.beginPath();
            ctx.arc(600, 350, 50, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawUndergroundElements() {
            // 绘制漂浮的地下宝石
            for (let i = 0; i < 8; i++) {
                const x = (i * 150 + animationFrame * 0.3) % canvas.width;
                const y = 100 + Math.sin(animationFrame * 0.02 + i) * 20;
                const size = 3 + Math.sin(animationFrame * 0.05 + i) * 2;

                ctx.fillStyle = `hsl(${(animationFrame + i * 45) % 360}, 70%, 60%)`;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function drawSceneText() {
            // 场景标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 32px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.shadowColor = 'rgba(0,0,0,0.7)';
            ctx.shadowBlur = 6;
            ctx.fillText(scenes[currentScene].title, canvas.width / 2, 50);

            // 场景描述
            ctx.font = '20px Microsoft YaHei';
            ctx.fillText(scenes[currentScene].description, canvas.width / 2, 80);
            ctx.shadowBlur = 0;
        }

        function showWordCards() {
            const container = document.getElementById('wordCards');
            container.innerHTML = '';

            vocabulary.forEach((item, index) => {
                setTimeout(() => {
                    const card = document.createElement('div');
                    card.className = 'word-card';
                    card.innerHTML = `
                        <div style="font-size: 1.8rem; font-weight: bold; margin-bottom: 15px;">
                            <span class="prefix">${item.prefix}</span>${item.word.replace(item.prefix.replace('-', ''), '')}
                        </div>
                        <div class="meaning">${item.meaning}</div>
                        <div style="font-size: 1.05rem; margin-top: 12px; opacity: 0.9;">${item.explanation}</div>
                        <div style="font-size: 1rem; margin-top: 10px; font-style: italic; opacity: 0.85;
                                   background: rgba(255,255,255,0.1); padding: 10px; border-radius: 8px;">
                            "${item.sentence}"
                        </div>
                    `;

                    card.addEventListener('click', () => {
                        speakWord(item.word);
                        showWordBreakdown(item);
                    });

                    container.appendChild(card);

                    setTimeout(() => {
                        card.classList.add('show');
                    }, 100);
                }, index * 500);
            });
        }

        function updateWordCards() {
            const cards = document.querySelectorAll('.word-card');
            cards.forEach(card => {
                card.style.opacity = '0.6';
                card.style.transform = 'scale(0.95)';
            });

            // 高亮当前场景的词汇
            const currentWords = scenes[currentScene].words;
            cards.forEach((card, index) => {
                if (currentWords.includes(vocabulary[index]?.word)) {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1.05)';
                    card.style.boxShadow = '0 30px 60px rgba(139, 69, 19, 0.5)';
                }
            });
        }

        function showWordBreakdown(wordData) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.85);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.4s ease;
                backdrop-filter: blur(8px);
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #654321 100%);
                color: white;
                padding: 60px;
                border-radius: 30px;
                max-width: 650px;
                text-align: center;
                animation: slideInUp 0.6s ease;
                box-shadow: 0 30px 60px rgba(0,0,0,0.4);
                border: 3px solid rgba(255,255,255,0.1);
            `;

            content.innerHTML = `
                <h2 style="margin-bottom: 30px; font-size: 2rem; font-weight: 300;">🔍 地下词汇探索</h2>
                <div style="font-size: 3rem; margin: 30px 0;">
                    <span style="color: #FFD700; font-weight: bold;">${wordData.prefix}</span>
                    <span style="color: #87CEEB; font-weight: bold;">${wordData.root}</span>
                </div>
                <div style="font-size: 2rem; margin: 25px 0; font-weight: bold;">${wordData.word}</div>
                <div style="font-size: 1.4rem; margin: 25px 0; opacity: 0.9;">${wordData.meaning}</div>
                <div style="line-height: 1.9; margin: 25px 0; font-size: 1.15rem; opacity: 0.85;">${wordData.explanation}</div>
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin: 25px 0;
                           font-style: italic; font-size: 1.1rem;">
                    ${wordData.sentence}
                </div>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 30px; padding: 15px 30px; background: linear-gradient(45deg, #2F4F4F, #4682B4);
                               color: white; border: none; border-radius: 30px; cursor: pointer; font-size: 1.1rem;
                               transition: all 0.3s ease;">
                    返回地面
                </button>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);

            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            };
        }

        function speakWord(word) {
            if ('speechSynthesis' in window) {
                speechSynthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(word);
                utterance.lang = 'en-US';
                utterance.rate = 0.8;
                utterance.pitch = 1.0;

                const feedback = document.createElement('div');
                feedback.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(45deg, #8B4513, #A0522D);
                    color: white;
                    padding: 30px 50px;
                    border-radius: 50px;
                    font-size: 1.8rem;
                    z-index: 1000;
                    animation: fadeIn 0.4s ease;
                    box-shadow: 0 20px 40px rgba(139, 69, 19, 0.5);
                    border: 2px solid rgba(255,255,255,0.2);
                `;
                feedback.innerHTML = `🔊 <strong>${word}</strong><br><small style="opacity: 0.8;">地下回音</small>`;
                document.body.appendChild(feedback);

                utterance.onend = () => {
                    setTimeout(() => {
                        if (feedback.parentNode) {
                            feedback.remove();
                        }
                    }, 1000);
                };

                speechSynthesis.speak(utterance);
            } else {
                alert('您的浏览器不支持语音功能');
            }
        }

        function initQuiz() {
            const quizData = [
                {
                    question: "submarine 的意思是？",
                    options: ["潜水艇", "地铁", "下级的", "替代品"],
                    correct: 0,
                    explanation: "sub(在下面) + marine(海的) = 在海面下面的船"
                },
                {
                    question: "subway 的意思是？",
                    options: ["潜水艇", "地铁", "下级的", "替代品"],
                    correct: 1,
                    explanation: "sub(在下面) + way(道路) = 在地面下面的道路"
                },
                {
                    question: "subordinate 的意思是？",
                    options: ["潜水艇", "地铁", "下级的", "替代品"],
                    correct: 2,
                    explanation: "sub(次级) + ordinate(排列) = 在等级排列中较低的"
                },
                {
                    question: "substitute 的意思是？",
                    options: ["潜水艇", "地铁", "下级的", "替代品"],
                    correct: 3,
                    explanation: "sub(代替) + stitute(放置) = 放在原物下面作为替代"
                }
            ];

            const quizArea = document.getElementById('quizArea');

            quizData.forEach((quiz, qIndex) => {
                const quizDiv = document.createElement('div');
                quizDiv.style.marginBottom = '30px';
                quizDiv.innerHTML = `<h4 style="margin-bottom: 18px; color: #2F4F4F; font-size: 1.3rem;">${quiz.question}</h4>`;

                quiz.options.forEach((option, oIndex) => {
                    const btn = document.createElement('button');
                    btn.className = 'quiz-btn';
                    btn.textContent = option;
                    btn.onclick = () => checkAnswer(btn, oIndex === quiz.correct, quiz.explanation, qIndex);
                    quizDiv.appendChild(btn);
                });

                quizArea.appendChild(quizDiv);
            });
        }

        function checkAnswer(btn, isCorrect, explanation, questionIndex) {
            const buttons = btn.parentNode.querySelectorAll('.quiz-btn');
            buttons.forEach(b => b.disabled = true);

            if (isCorrect) {
                btn.classList.add('correct');
                btn.innerHTML += ' ✓';

                // 显示解释
                setTimeout(() => {
                    const explanationDiv = document.createElement('div');
                    explanationDiv.style.cssText = `
                        margin-top: 15px;
                        padding: 20px;
                        background: linear-gradient(135deg, #228B22, #32CD32);
                        color: white;
                        border-radius: 15px;
                        font-size: 1rem;
                        animation: slideInUp 0.6s ease;
                        box-shadow: 0 10px 25px rgba(34, 139, 34, 0.3);
                    `;
                    explanationDiv.innerHTML = `💡 ${explanation}`;
                    btn.parentNode.appendChild(explanationDiv);
                }, 600);
            } else {
                btn.classList.add('wrong');
                btn.innerHTML += ' ✗';
            }
        }

        function initPronunciation() {
            const pronunciationArea = document.getElementById('pronunciationArea');

            vocabulary.forEach((item, index) => {
                const btn = document.createElement('button');
                btn.className = 'quiz-btn';
                btn.style.background = 'linear-gradient(45deg, #8B4513, #A0522D)';
                btn.innerHTML = `🔊 ${item.word}`;

                btn.onclick = () => {
                    speakWord(item.word);
                    btn.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        btn.style.transform = 'scale(1)';
                    }, 200);
                };

                pronunciationArea.appendChild(btn);
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowDown':
                case ' ':
                    e.preventDefault();
                    nextScene();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    prevScene();
                    break;
                case 'r':
                case 'R':
                    e.preventDefault();
                    resetStory();
                    break;
                case 's':
                case 'S':
                    e.preventDefault();
                    startStory();
                    break;
            }
        });

        // 页面加载时创建地下元素
        window.addEventListener('load', () => {
            createUndergroundElements();
            initQuiz();
            initPronunciation();
            setTimeout(startStory, 1200);

            // 显示快捷键提示
            setTimeout(() => {
                const hints = document.createElement('div');
                hints.style.cssText = `
                    position: fixed;
                    bottom: 30px;
                    right: 30px;
                    background: rgba(0,0,0,0.85);
                    color: white;
                    padding: 25px;
                    border-radius: 20px;
                    font-size: 0.95rem;
                    z-index: 1000;
                    animation: slideInUp 0.6s ease;
                    backdrop-filter: blur(12px);
                    border: 2px solid rgba(255,255,255,0.1);
                `;
                hints.innerHTML = `
                    <div style="margin-bottom: 10px;"><strong>⌨️ 探险快捷键：</strong></div>
                    <div>↓ 或 空格：深入地下</div>
                    <div>↑ ：返回上层</div>
                    <div>S：开始探险</div>
                    <div>R：重新探险</div>
                `;

                document.body.appendChild(hints);

                setTimeout(() => {
                    hints.style.animation = 'fadeOut 0.6s ease';
                    setTimeout(() => hints.remove(), 600);
                }, 7000);
            }, 2500);
        });
    </script>
</body>
</html>
