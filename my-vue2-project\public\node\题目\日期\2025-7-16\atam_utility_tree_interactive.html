<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ATAM效用树 - 交互式学习游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            color: white;
            margin-bottom: 20px;
            letter-spacing: -1px;
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 300;
        }

        .quiz-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease-out 0.2s both;
        }

        .quiz-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #34495e;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .option-card {
            background: #f8f9fa;
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .option-card:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-2px);
        }

        .option-card.selected {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .option-card.correct {
            background: #e8f5e8;
            border-color: #4caf50;
            animation: pulse 0.6s ease-in-out;
        }

        .option-card.incorrect {
            background: #ffebee;
            border-color: #f44336;
            animation: shake 0.6s ease-in-out;
        }

        .feedback {
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            font-weight: 500;
            text-align: center;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .feedback.show {
            opacity: 1;
            transform: translateY(0);
        }

        .feedback.correct {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }

        .feedback.incorrect {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }

        .game-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease-out 0.4s both;
        }

        .game-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .game-subtitle {
            font-size: 1.1rem;
            color: #7f8c8d;
            text-align: center;
            margin-bottom: 40px;
        }

        .canvas-container {
            background: #fafafa;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
        }

        #treeCanvas {
            display: block;
            margin: 0 auto;
            border-radius: 12px;
            background: white;
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
        }

        .btn.secondary:hover {
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
        }

        .explanation-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .explanation-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
        }

        .knowledge-card {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }

        .knowledge-card h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .knowledge-card p {
            line-height: 1.8;
            color: #34495e;
            margin-bottom: 15px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .game-progress {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .progress-bar {
            background: #e0e0e0;
            border-radius: 10px;
            height: 8px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 100%;
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes treeGrow {
            from {
                stroke-dashoffset: 1000;
            }
            to {
                stroke-dashoffset: 0;
            }
        }

        .tree-line {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: treeGrow 2s ease-out forwards;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .quiz-section, .game-section, .explanation-section {
                padding: 25px;
            }
            
            .options-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>ATAM效用树探索之旅</h1>
            <p>通过互动游戏学习软件架构评估的核心工具</p>
        </div>

        <!-- 题目测试区 -->
        <div class="quiz-section">
            <div class="quiz-title">🎯 知识检测</div>
            <div class="question-text">
                在软件架构评估中，<span class="highlight">(____)</span>方法采用效用树这一工具来对质量属性进行分类和优先级排序。
            </div>
            <div class="options-grid">
                <div class="option-card" data-option="A">A. SAAM</div>
                <div class="option-card" data-option="B">B. ATAM</div>
                <div class="option-card" data-option="C">C. SAEM</div>
                <div class="option-card" data-option="D">D. CBAM</div>
            </div>
            <div id="quiz-feedback" class="feedback"></div>
        </div>

        <!-- 游戏互动区 -->
        <div class="game-section">
            <div class="game-title">🌳 效用树构建游戏</div>
            <div class="game-subtitle">点击下方按钮，一步步构建ATAM效用树，理解其结构和作用</div>
            
            <div class="game-progress">
                <div>构建进度: <span id="progress-text">0/4</span></div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="treeCanvas" width="900" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn" id="step1-btn">1. 添加树根</button>
                <button class="btn" id="step2-btn" disabled>2. 添加质量属性</button>
                <button class="btn" id="step3-btn" disabled>3. 添加属性分类</button>
                <button class="btn" id="step4-btn" disabled>4. 添加场景</button>
                <button class="btn secondary" id="reset-btn">重新开始</button>
            </div>

            <div id="step-explanation" style="text-align: center; margin-top: 20px; font-size: 1.1rem; color: #666;">
                点击"添加树根"开始构建效用树
            </div>
        </div>

        <!-- 知识解释区 -->
        <div class="explanation-section">
            <div class="explanation-title">📚 深度解析</div>
            
            <div class="knowledge-card">
                <h3>什么是ATAM？</h3>
                <p><span class="highlight">ATAM (Architecture Tradeoff Analysis Method)</span> 是一种系统化的软件架构评估方法，专门用于分析架构决策中的权衡关系。</p>
                <p>想象你在设计一辆汽车：要速度快，还是要省油？要安全性高，还是要成本低？ATAM就是帮你找到最佳平衡点的方法。</p>
            </div>

            <div class="knowledge-card">
                <h3>效用树的作用</h3>
                <p>效用树是ATAM的核心工具，它像一个<span class="highlight">质量属性的分类目录</span>，帮助团队：</p>
                <p>• 系统地识别所有重要的质量需求<br>
                • 按优先级排序这些需求<br>
                • 将抽象的质量目标转化为具体可测试的场景</p>
            </div>

            <div class="knowledge-card">
                <h3>效用树的四层结构</h3>
                <p><strong>第1层 - 树根：</strong>整个系统的质量目标，如"构建高质量的电商系统"</p>
                <p><strong>第2层 - 质量属性：</strong>具体的质量维度，如性能、安全性、可用性</p>
                <p><strong>第3层 - 属性分类：</strong>对质量属性的细分，如性能可分为响应时间、吞吐量</p>
                <p><strong>第4层 - 质量场景：</strong>具体可测试的场景，如"1000用户同时访问时响应时间<2秒"</p>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            currentStep: 0,
            maxSteps: 4,
            treeData: {
                root: null,
                attributes: [],
                classifications: [],
                scenarios: []
            }
        };

        // Canvas相关
        const canvas = document.getElementById('treeCanvas');
        const ctx = canvas.getContext('2d');

        // 题目测试逻辑
        const quizOptions = document.querySelectorAll('.option-card');
        const quizFeedback = document.getElementById('quiz-feedback');
        const correctAnswer = 'B';

        quizOptions.forEach(option => {
            option.addEventListener('click', () => {
                // 清除之前的选择
                quizOptions.forEach(opt => {
                    opt.classList.remove('selected', 'correct', 'incorrect');
                });

                // 标记当前选择
                option.classList.add('selected');
                
                const selectedOption = option.dataset.option;
                
                setTimeout(() => {
                    if (selectedOption === correctAnswer) {
                        option.classList.add('correct');
                        showFeedback('正确！ATAM方法确实使用效用树来分类和排序质量属性。让我们通过下面的游戏来深入了解效用树的结构！', 'correct');
                    } else {
                        option.classList.add('incorrect');
                        // 显示正确答案
                        quizOptions.forEach(opt => {
                            if (opt.dataset.option === correctAnswer) {
                                opt.classList.add('correct');
                            }
                        });
                        showFeedback('不对哦！正确答案是ATAM。SAAM是早期的架构分析方法，CBAM专注成本效益分析，而ATAM才是使用效用树的方法。', 'incorrect');
                    }
                }, 300);
            });
        });

        function showFeedback(message, type) {
            quizFeedback.textContent = message;
            quizFeedback.className = `feedback ${type}`;
            setTimeout(() => {
                quizFeedback.classList.add('show');
            }, 100);
        }

        // 游戏控制按钮
        const step1Btn = document.getElementById('step1-btn');
        const step2Btn = document.getElementById('step2-btn');
        const step3Btn = document.getElementById('step3-btn');
        const step4Btn = document.getElementById('step4-btn');
        const resetBtn = document.getElementById('reset-btn');
        const stepExplanation = document.getElementById('step-explanation');
        const progressText = document.getElementById('progress-text');
        const progressFill = document.getElementById('progress-fill');

        // 绑定事件
        step1Btn.addEventListener('click', () => executeStep(1));
        step2Btn.addEventListener('click', () => executeStep(2));
        step3Btn.addEventListener('click', () => executeStep(3));
        step4Btn.addEventListener('click', () => executeStep(4));
        resetBtn.addEventListener('click', resetGame);

        function executeStep(step) {
            if (step <= gameState.currentStep + 1) {
                gameState.currentStep = step;
                updateProgress();
                updateButtons();
                
                switch(step) {
                    case 1:
                        drawStep1();
                        stepExplanation.innerHTML = '🎯 <strong>树根已添加！</strong> 这是整个系统的质量目标，为所有质量属性提供统一的方向。';
                        break;
                    case 2:
                        drawStep2();
                        stepExplanation.innerHTML = '🏗️ <strong>质量属性已添加！</strong> 这些是系统需要关注的主要质量维度，每个都很重要。';
                        break;
                    case 3:
                        drawStep3();
                        stepExplanation.innerHTML = '🔍 <strong>属性分类已添加！</strong> 将抽象的质量属性细分为更具体的方面，便于分析。';
                        break;
                    case 4:
                        drawStep4();
                        stepExplanation.innerHTML = '✅ <strong>效用树构建完成！</strong> 现在有了具体可测试的场景，可以进行实际的架构评估了！';
                        break;
                }
            }
        }

        function updateProgress() {
            progressText.textContent = `${gameState.currentStep}/${gameState.maxSteps}`;
            progressFill.style.width = `${(gameState.currentStep / gameState.maxSteps) * 100}%`;
        }

        function updateButtons() {
            const buttons = [step1Btn, step2Btn, step3Btn, step4Btn];
            buttons.forEach((btn, index) => {
                if (index < gameState.currentStep) {
                    btn.disabled = false;
                    btn.style.opacity = '0.7';
                } else if (index === gameState.currentStep) {
                    btn.disabled = false;
                    btn.style.opacity = '1';
                } else {
                    btn.disabled = true;
                    btn.style.opacity = '0.4';
                }
            });
        }

        function resetGame() {
            gameState.currentStep = 0;
            updateProgress();
            updateButtons();
            clearCanvas();
            stepExplanation.innerHTML = '点击"添加树根"开始构建效用树';
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制函数
        function drawStep1() {
            clearCanvas();
            
            // 绘制树根
            const rootX = canvas.width / 2;
            const rootY = 80;
            
            // 根节点背景
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.roundRect(rootX - 100, rootY - 25, 200, 50, 25);
            ctx.fill();
            
            // 根节点文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('高质量电商系统', rootX, rootY + 5);
            
            // 添加动画效果
            animateNodeAppear(rootX, rootY);
        }

        function drawStep2() {
            drawStep1();
            
            const rootX = canvas.width / 2;
            const rootY = 80;
            const level2Y = 200;
            
            const attributes = [
                { name: '性能', x: rootX - 200, color: '#e74c3c' },
                { name: '安全性', x: rootX, color: '#f39c12' },
                { name: '可用性', x: rootX + 200, color: '#27ae60' }
            ];
            
            // 绘制连接线
            attributes.forEach(attr => {
                drawAnimatedLine(rootX, rootY + 25, attr.x, level2Y - 25, '#bdc3c7');
            });
            
            // 绘制属性节点
            attributes.forEach((attr, index) => {
                setTimeout(() => {
                    drawNode(attr.x, level2Y, attr.name, attr.color);
                    animateNodeAppear(attr.x, level2Y);
                }, index * 300);
            });
        }

        function drawStep3() {
            drawStep2();
            
            const level3Y = 320;
            
            const classifications = [
                { name: '响应时间', x: canvas.width / 2 - 250, parent: canvas.width / 2 - 200 },
                { name: '吞吐量', x: canvas.width / 2 - 150, parent: canvas.width / 2 - 200 },
                { name: '身份认证', x: canvas.width / 2 - 50, parent: canvas.width / 2 },
                { name: '数据加密', x: canvas.width / 2 + 50, parent: canvas.width / 2 },
                { name: '故障恢复', x: canvas.width / 2 + 150, parent: canvas.width / 2 + 200 },
                { name: '系统监控', x: canvas.width / 2 + 250, parent: canvas.width / 2 + 200 }
            ];
            
            setTimeout(() => {
                classifications.forEach((cls, index) => {
                    setTimeout(() => {
                        drawAnimatedLine(cls.parent, 225, cls.x, level3Y - 25, '#95a5a6');
                        drawNode(cls.x, level3Y, cls.name, '#9b59b6', 80, 35, '12px');
                        animateNodeAppear(cls.x, level3Y);
                    }, index * 200);
                });
            }, 1000);
        }

        function drawStep4() {
            drawStep3();
            
            const level4Y = 440;
            
            const scenarios = [
                { name: '1000用户\n<2秒响应', x: canvas.width / 2 - 250 },
                { name: '10000并发\n99%成功', x: canvas.width / 2 - 150 },
                { name: '多因子\n认证', x: canvas.width / 2 - 50 },
                { name: 'AES256\n加密', x: canvas.width / 2 + 50 },
                { name: '30秒内\n自动恢复', x: canvas.width / 2 + 150 },
                { name: '实时\n性能监控', x: canvas.width / 2 + 250 }
            ];
            
            setTimeout(() => {
                scenarios.forEach((scenario, index) => {
                    setTimeout(() => {
                        drawAnimatedLine(scenario.x, 355, scenario.x, level4Y - 25, '#34495e');
                        drawNode(scenario.x, level4Y, scenario.name, '#1abc9c', 70, 40, '10px');
                        animateNodeAppear(scenario.x, level4Y);
                    }, index * 250);
                });
            }, 2000);
        }

        function drawNode(x, y, text, color, width = 120, height = 50, fontSize = '14px') {
            // 节点背景
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.roundRect(x - width/2, y - height/2, width, height, 15);
            ctx.fill();
            
            // 节点边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 节点文字
            ctx.fillStyle = 'white';
            ctx.font = `bold ${fontSize} Arial`;
            ctx.textAlign = 'center';
            
            const lines = text.split('\n');
            const lineHeight = parseInt(fontSize) + 2;
            const startY = y - (lines.length - 1) * lineHeight / 2;
            
            lines.forEach((line, index) => {
                ctx.fillText(line, x, startY + index * lineHeight);
            });
        }

        function drawAnimatedLine(x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.lineCap = 'round';
            
            let progress = 0;
            const animate = () => {
                if (progress <= 1) {
                    const currentX = x1 + (x2 - x1) * progress;
                    const currentY = y1 + (y2 - y1) * progress;
                    
                    ctx.beginPath();
                    ctx.moveTo(x1, y1);
                    ctx.lineTo(currentX, currentY);
                    ctx.stroke();
                    
                    progress += 0.05;
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        function animateNodeAppear(x, y) {
            let scale = 0;
            const animate = () => {
                if (scale < 1) {
                    scale += 0.1;
                    
                    ctx.save();
                    ctx.translate(x, y);
                    ctx.scale(scale, scale);
                    ctx.translate(-x, -y);
                    
                    // 重绘节点（这里简化处理）
                    
                    ctx.restore();
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        // 添加圆角矩形支持
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }

        // 初始化
        updateButtons();
        clearCanvas();
    </script>
</body>
</html>