<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>ABSD架构演化学习游戏</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: #f0f2f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .question {
            font-size: 18px;
            margin-bottom: 20px;
            padding: 10px;
            background: #e8f4ff;
            border-radius: 5px;
        }
        .game-area {
            border: 2px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .architecture {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .component {
            padding: 10px;
            background: #e1e1e1;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .component:hover {
            background: #d1d1d1;
        }
        .selected {
            background: #4CAF50;
            color: white;
        }
        .button {
            padding: 10px 20px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .feedback {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        #architecture-canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            background-color: #fdfdfd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ABSD方法 - 架构演化学习游戏</h1>
        
        <div class="question">
            题目：ABSD方法主要包括架构构筑等6个主要活动，其中（架构演化）活动的目标是识别现有风险，及早发现架构设计中的缺陷和错误；活动针对用户的需求变化，修改应用架构，满足新的需求。
        </div>

        <div class="game-area">
            <h2>架构演化模拟游戏</h2>
            <p>请选择需要演化的架构组件，并应对新的需求变化：</p>
            
            <canvas id="architecture-canvas" width="760" height="150"></canvas>
            <div class="architecture" id="architecture" style="display: none;">
                <div class="component" onclick="selectComponent(this)">用户界面</div>
                <div class="component" onclick="selectComponent(this)">业务逻辑</div>
                <div class="component" onclick="selectComponent(this)">数据存储</div>
            </div>

            <div id="challenge" style="margin: 20px 0;">
                <h3>当前挑战：</h3>
                <p id="current-challenge">用户需要一个新的移动端界面，请选择需要演化的组件。</p>
            </div>

            <button class="button" onclick="evolveArchitecture()">执行架构演化</button>
            
            <div id="feedback" class="feedback"></div>
        </div>

        <div class="explanation">
            <h3>知识要点：</h3>
            <ul>
                <li>架构演化是为了应对需求变化而进行的架构调整</li>
                <li>需要识别现有系统中的风险和缺陷</li>
                <li>确保新的架构满足变化的需求</li>
            </ul>
        </div>
    </div>

    <script>
        let selectedComponents = [];
        let currentChallenge = 0;
        const challenges = [
            {
                description: "用户需要一个新的移动端界面，请选择需要演化的组件。",
                solution: ["用户界面"],
                feedback: "正确！移动端界面的需求主要影响用户界面层。",
                animationType: "addMobileUI"
            },
            {
                description: "需要添加新的支付功能，请选择需要演化的组件。",
                solution: ["业务逻辑", "数据存储"],
                feedback: "正确！支付功能需要修改业务逻辑和数据存储。",
                animationType: "addPayment"
            },
            {
                description: "系统需要支持多语言，请选择需要演化的组件。",
                solution: ["用户界面", "业务逻辑"],
                feedback: "正确！多语言支持需要修改界面显示和相关业务逻辑。",
                animationType: "addI18n"
            }
        ];

        const canvas = document.getElementById('architecture-canvas');
        const ctx = canvas.getContext('2d');
        const components = [
            { name: "用户界面", x: 50, y: 30, w: 200, h: 90 },
            { name: "业务逻辑", x: 280, y: 30, w: 200, h: 90 },
            { name: "数据存储", x: 510, y: 30, w: 200, h: 90 }
        ];

        function drawArchitecture() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            components.forEach(comp => {
                const isSelected = selectedComponents.includes(comp.name);
                ctx.strokeStyle = isSelected ? '#4CAF50' : '#1890ff';
                ctx.fillStyle = isSelected ? '#f6ffed' : '#e8f4ff';
                ctx.lineWidth = isSelected ? 3 : 2;

                ctx.fillRect(comp.x, comp.y, comp.w, comp.h);
                ctx.strokeRect(comp.x, comp.y, comp.w, comp.h);
                
                ctx.fillStyle = '#333';
                ctx.font = '16px "Microsoft YaHei"';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(comp.name, comp.x + comp.w / 2, comp.y + comp.h / 2);
            });
        }

        canvas.addEventListener('click', function(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            components.forEach(comp => {
                if (x > comp.x && x < comp.x + comp.w && y > comp.y && y < comp.y + comp.h) {
                    selectComponent(comp.name);
                }
            });
        });

        function selectComponent(componentName) {
            if (selectedComponents.includes(componentName)) {
                selectedComponents = selectedComponents.filter(c => c !== componentName);
            } else {
                selectedComponents.push(componentName);
            }
            drawArchitecture();
        }

        function evolveArchitecture() {
            const challenge = challenges[currentChallenge];
            const feedback = document.getElementById('feedback');
            
            // 检查选择是否正确
            const isCorrect = challenge.solution.length === selectedComponents.length &&
                            challenge.solution.every(c => selectedComponents.includes(c));

            if (isCorrect) {
                feedback.className = 'feedback success';
                feedback.textContent = challenge.feedback;
                playAnimation(challenge.animationType);
                
                // 进入下一个挑战
                currentChallenge = (currentChallenge + 1) % challenges.length;
                setTimeout(() => {
                    document.getElementById('current-challenge').textContent = challenges[currentChallenge].description;
                    // 重置选择
                    selectedComponents = [];
                    drawArchitecture();
                    feedback.textContent = '';
                }, 2000);
            } else {
                feedback.className = 'feedback error';
                feedback.textContent = "请重试！考虑哪些组件真正需要修改。";
                playAnimation('error');
            }
        }

        function playAnimation(type) {
            let frame = 0;
            const duration = 60; // 60 frames

            function animate() {
                if (frame >= duration) {
                    drawArchitecture(); // 动画结束后恢复
                    return;
                }
                frame++;
                const progress = frame / duration;
                drawArchitecture(); // 先绘制基础架构

                ctx.save();
                switch(type) {
                    case 'error':
                        const shake = (Math.random() - 0.5) * 10 * Math.sin(progress * Math.PI * 2);
                        ctx.translate(shake, 0);
                        drawArchitecture();
                        break;
                    case 'addMobileUI':
                        ctx.globalAlpha = progress;
                        const uiComp = components[0];
                        ctx.font = "40px sans-serif";
                        ctx.fillText("📱", uiComp.x + uiComp.w / 2, uiComp.y - 20 * progress);
                        break;
                    case 'addPayment':
                        ctx.globalAlpha = progress;
                        const logicComp = components[1];
                        const dataComp = components[2];
                        ctx.fillStyle = "rgba(76, 175, 80, 0.7)";
                        ctx.fillRect(logicComp.x + 20, logicComp.y + logicComp.h - 30 * progress, logicComp.w - 40, 20 * progress);
                        ctx.fillRect(dataComp.x + 20, dataComp.y + dataComp.h - 30 * progress, dataComp.w - 40, 20 * progress);
                        ctx.fillStyle="white";
                        ctx.font = "12px sans-serif";
                        ctx.fillText("支付网关", logicComp.x + logicComp.w/2, logicComp.y + logicComp.h - 15);
                        ctx.fillText("交易记录", dataComp.x + dataComp.w/2, dataComp.y + dataComp.h - 15);
                        break;
                    case 'addI18n':
                        ctx.globalAlpha = progress;
                        const uiComp_i18n = components[0];
                        const logicComp_i18n = components[1];
                        ctx.font = "20px sans-serif";
                        ctx.fillText("文案", uiComp_i18n.x + 25, uiComp_i18n.y + 25);
                        ctx.fillText("🌐", logicComp_i18n.x + 25, logicComp_i18n.y + 25);
                        break;
                }
                ctx.restore();

                requestAnimationFrame(animate);
            }
            animate();
        }

        // Initial draw
        drawArchitecture();
    </script>
</body>
</html> 