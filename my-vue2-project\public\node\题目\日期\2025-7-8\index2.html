<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面导航 - 科技风格</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00aaff;
            --background-color: #1a1a2e;
            --surface-color: #162447;
            --text-color: #e0e0e0;
            --header-color: #fca311;
            --hover-color: #1f4068;
            --border-color: rgba(0, 170, 255, 0.3);
        }

        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            margin: 0;
            background-color: var(--background-color);
            color: var(--text-color);
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        #sidebar {
            width: 320px;
            background: var(--surface-color);
            padding: 25px;
            box-shadow: 3px 0 15px rgba(0,0,0,0.3);
            overflow-y: auto;
            height: 100%;
            border-right: 1px solid var(--border-color);
            transition: width 0.3s ease;
        }

        #main-content {
            flex-grow: 1;
            padding: 25px;
            height: 100%;
            display: flex;
            flex-direction: column;
            background: linear-gradient(to right, var(--background-color), #162447);
        }

        h1 {
            font-family: 'Orbitron', sans-serif;
            color: var(--primary-color);
            text-align: center;
            margin-top: 0;
            margin-bottom: 30px;
            text-shadow: 0 0 10px var(--primary-color);
        }

        .category {
            margin-bottom: 12px;
        }

        .category-title {
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 700;
            padding: 12px 15px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s ease, color 0.3s ease;
            border-left: 3px solid transparent;
        }

        .category-title:hover {
            background-color: var(--hover-color);
            border-left: 3px solid var(--primary-color);
        }

        .category-title .arrow {
            transition: transform 0.3s ease;
            font-size: 0.8em;
        }

        .category-content {
            padding-left: 20px;
            display: none;
            border-left: 1px solid var(--border-color);
            margin-top: 8px;
            margin-left: 5px;
        }

        ul {
            list-style-type: none;
            padding: 0;
        }

        li {
            margin: 8px 0;
        }

        .file-link {
            text-decoration: none;
            color: var(--text-color);
            font-size: 0.95em;
            transition: color 0.3s, background-color 0.3s, padding-left 0.3s;
            cursor: pointer;
            display: block;
            padding: 8px 10px;
            border-radius: 4px;
            position: relative;
        }

        .file-link:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 100%;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
            border-radius: 4px;
            opacity: 0.3;
        }
        
        .file-link.active, .file-link:hover {
            color: #fff;
            background-color: var(--hover-color);
        }

        .file-link.active:before, .file-link:hover:before {
            width: 100%;
        }

        #iframe-card {
            flex-grow: 1;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 170, 255, 0.15);
            background-color: #fff;
        }
    </style>
</head>
<body>

<div id="sidebar">
    <h1>导航中心</h1>

    <div class="category">
        <div class="category-title" onclick="toggleCategory(this)">
            <span>单词</span>
            <span class="arrow">▶</span>
        </div>
        <div class="category-content">
            <ul>
                <li><span class="file-link" onclick="loadFile(this, './单词/convene_animation.html')">convene_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/demote_animation.html')">demote_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/adhere_animation.html')">adhere_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/transcend_animation.html')">transcend_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/provoke_animation.html')">provoke_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/advocate_animation.html')">advocate_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/induce_animation.html')">induce_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/import_animation.html')">import_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/intervene_animation.html')">intervene_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/contribute_animation.html')">contribute_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/describe_animation.html')">describe_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/progress_animation.html')">progress_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/review_animation.html')">review_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/circumvent_animation.html')">circumvent_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/benevolent_animation.html')">benevolent_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/imminent_animation.html')">imminent_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/retrospect_animation.html')">retrospect_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/assess_animation.html')">assess_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/construct_animation.html')">construct_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/circumstance_animation.html')">circumstance_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/export_animation.html')">export_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/extract_animation.html')">extract_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/preclude_animation.html')">preclude_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/contradict_animation.html')">contradict_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/insert_animation.html')">insert_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/submit_animation.html')">submit_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/conclude_animation.html')">conclude_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/attribute_animation.html')">attribute_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/perceive_animation.html')">perceive_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/append_animation.html')">append_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/subvert_animation.html')">subvert_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/perspective_animation.html')">perspective_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/accommodate_animation.html')">accommodate_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/transform_animation.html')">transform_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/evolve_animation.html')">evolve_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/generate_animation.html')">generate_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/migrate_animation.html')">migrate_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/retract_animation.html')">retract_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/disrupt_animation.html')">disrupt_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/expel_animation.html')">expel_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/reflect_animation.html')">reflect_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/transmit_animation.html')">transmit_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/interrupt_animation.html')">interrupt_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/expand_animation.html')">expand_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/eject_animation.html')">eject_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/compress_animation.html')">compress_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/subscribe_animation.html')">subscribe_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/distribute_animation.html')">distribute_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/reveal_animation.html')">reveal_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/propose_animation.html')">propose_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/decompose_animation.html')">decompose_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/inhibit_animation.html')">inhibit_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/acquire_animation.html')">acquire_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/assemble_animation.html')">assemble_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/abstract_animation.html')">abstract_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/adapt_animation.html')">adapt_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/integrate_animation.html')">integrate_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/prospect_animation.html')">prospect_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/coincidence_animation.html')">coincidence_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/sustain_animation.html')">sustain_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/disperse_animation.html')">disperse_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/deduce_animation.html')">deduce_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/emerge_animation.html')">emerge_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/convert_animation.html')">convert_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/component_animation.html')">component_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/predict_animation.html')">predict_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/transport_animation.html')">transport_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/inspect_animation.html')">inspect_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/innovate_animation.html')">innovate_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './单词/illuminate_animation.html')">illuminate_animation.html</span></li>
            </ul>
        </div>
    </div>

    <div class="category">
        <div class="category-title" onclick="toggleCategory(this)">
            <span>小学</span>
            <span class="arrow">▶</span>
        </div>
        <div class="category-content">
            <div class="category">
                <div class="category-title" onclick="toggleCategory(this, event)">
                    <span>词缀故事动画</span>
                    <span class="arrow">▶</span>
                </div>
                <div class="category-content">
                    <ul>
                        <li><span class="file-link" onclick="loadFile(this, './小学/词缀故事动画/word_story_2.html')">word_story_2.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './小学/词缀故事动画/index.html')">index.html</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="category">
        <div class="category-title" onclick="toggleCategory(this)">
            <span>考研</span>
            <span class="arrow">▶</span>
        </div>
        <div class="category-content">
            <ul>
                <li><span class="file-link" onclick="loadFile(this, './考研/exam_sentence_animation_2.html')">exam_sentence_animation_2.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './考研/exam_sentence_animation.html')">exam_sentence_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './考研/sentence_animation.html')">sentence_animation.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './考研/word_animation.html')">word_animation.html</span></li>
            </ul>
        </div>
    </div>

    <div class="category">
        <div class="category-title" onclick="toggleCategory(this)">
            <span>题目</span>
            <span class="arrow">▶</span>
        </div>
        <div class="category-content">
            <div class="category">
                <div class="category-title" onclick="toggleCategory(this, event)">
                    <span>2025-7-1</span>
                    <span class="arrow">▶</span>
                </div>
                <div class="category-content">
                    <ul>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index4.html')">index4.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index14.html')">index14.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index13.html')">index13.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index12.html')">index12.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index11.html')">index11.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index10.html')">index10.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index9.html')">index9.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index8.html')">index8.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index7.html')">index7.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index6.html')">index6.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index5.html')">index5.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index3.html')">index3.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index2.html')">index2.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/index.html')">index.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/process_sync_demo.html')">process_sync_demo.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/database_normalization_tutorial.html')">database_normalization_tutorial.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-1/crc_demo.html')">crc_demo.html</span></li>
                    </ul>
                </div>
            </div>
            <div class="category">
                <div class="category-title" onclick="toggleCategory(this, event)">
                    <span>2025-7-2</span>
                    <span class="arrow">▶</span>
                </div>
                <div class="category-content">
                    <ul>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/absd_interactive_explanation_v5.html')">absd_interactive_explanation_v5.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/absd_interactive_explanation_v4.html')">absd_interactive_explanation_v4.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/absd_interactive_explanation_v3.html')">absd_interactive_explanation_v3.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/absd_interactive_explanation_v2.html')">absd_interactive_explanation_v2.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/absd_interactive_explanation.html')">absd_interactive_explanation.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/dssa_interactive_explanation.html')">dssa_interactive_explanation.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/database_dump_interactive.html')">database_dump_interactive.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/snmpv3_security_interactive.html')">snmpv3_security_interactive.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/software_architecture_evaluation_interactive_v2.html')">software_architecture_evaluation_interactive_v2.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/software_architecture_evaluation_interactive.html')">software_architecture_evaluation_interactive.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/software_tools_explanation_interactive.html')">software_tools_explanation_interactive.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/process_synchronization_interactive_tutorial.html')">process_synchronization_interactive_tutorial.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/process_sync_explanation.html')">process_sync_explanation.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/software_model_explanation.html')">software_model_explanation.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/omt_explanation.html')">omt_explanation.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/up_interactive_explanation.html')">up_interactive_explanation.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/microprogram_explanation.html')">microprogram_explanation.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/disk_io_explanation.html')">disk_io_explanation.html</span></li>
                        <li><span class="file-link" onclick="loadFile(this, './题目/2025-7-2/trap_interrupt_explanation.html')">trap_interrupt_explanation.html</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="category">
        <div class="category-title" onclick="toggleCategory(this)">
            <span>知识</span>
            <span class="arrow">▶</span>
        </div>
        <div class="category-content">
            <ul>
                <li><span class="file-link" onclick="loadFile(this, './知识/ABSD_Game.html')">ABSD_Game.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './知识/user_requirements_game.html')">user_requirements_game.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './知识/design_constraints_demo.html')">design_constraints_demo.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './知识/functional_requirements_demo.html')">functional_requirements_demo.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './知识/functional_vs_nonfunctional_requirements.html')">functional_vs_nonfunctional_requirements.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './知识/different_representations_demo.html')">different_representations_demo.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './知识/creator_pattern_demo.html')">creator_pattern_demo.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './知识/waiter_builder_demo.html')">waiter_builder_demo.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './知识/builder_pattern_demo.html')">builder_pattern_demo.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './知识/doc_sync_game.html')">doc_sync_game.html</span></li>
            </ul>
        </div>
    </div>

    <div class="category">
        <div class="category-title" onclick="toggleCategory(this)">
            <span>架构</span>
            <span class="arrow">▶</span>
        </div>
        <div class="category-content">
            <ul>
                <li><span class="file-link" onclick="loadFile(this, './架构/ABSD方法 - 架构演化学习游戏.html')">ABSD方法 - 架构演化学习游戏.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './架构/隐式调用.html')">隐式调用.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './架构/数据共享.html')">数据共享.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './架构/架构复审.html')">架构复审.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './架构/架构风格.html')">架构风格.html</span></li>
                <li><span class="file-link" onclick="loadFile(this, './架构/顺序批处理.html')">顺序批处理.html</span></li>
            </ul>
        </div>
    </div>
</div>

<div id="main-content">
    <iframe id="iframe-card" src="" name="content-frame"></iframe>
</div>

<script>
    let fileLinks = [];
    let currentIndex = -1;
    let linkElements = [];

    document.addEventListener('DOMContentLoaded', () => {
        linkElements = document.querySelectorAll('.file-link');
        linkElements.forEach(link => {
            const onclickAttr = link.getAttribute('onclick');
            const filePath = onclickAttr.match(/'([^']+)'/)[1];
            fileLinks.push(filePath);
        });
    });

    function toggleCategory(element, event) {
        if (event) {
            event.stopPropagation();
        }
        const content = element.nextElementSibling;
        const arrow = element.querySelector('.arrow');
        if (content.style.display === "block") {
            content.style.display = "none";
            arrow.style.transform = "rotate(0deg)";
        } else {
            content.style.display = "block";
            arrow.style.transform = "rotate(90deg)";
        }
    }

    function loadFile(element, filePath) {
        const iframe = document.getElementById('iframe-card');
        iframe.src = filePath;
        
        currentIndex = fileLinks.indexOf(filePath);

        linkElements.forEach(link => link.classList.remove('active'));
        if(element) {
            element.classList.add('active');
        } else {
            // Find the link element by filePath when using arrows
            const activeLink = Array.from(linkElements).find(link => link.getAttribute('onclick').includes(`'${filePath}'`));
            if(activeLink) activeLink.classList.add('active');
        }
    }

    document.addEventListener('keydown', (event) => {
        if (fileLinks.length === 0) return;
        if (event.key === 'ArrowLeft') {
            if (currentIndex <= 0) {
                currentIndex = fileLinks.length - 1;
            } else {
                currentIndex--;
            }
            loadFile(null, fileLinks[currentIndex]);
        } else if (event.key === 'ArrowRight') {
            if (currentIndex >= fileLinks.length - 1) {
                currentIndex = 0;
            } else {
                currentIndex++;
            }
            loadFile(null, fileLinks[currentIndex]);
        }
    });
</script>

</body>
</html> 