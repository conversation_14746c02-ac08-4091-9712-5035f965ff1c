<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Inspect</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto+Mono&family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #00E676; /* Scanner Green */
            --secondary-color: #00BFA5;
            --glow-color: #1DE9B6;
            --light-bg: #263238; /* Dark Panel Background */
            --panel-bg: #37474F;
            --text-color: #ECEFF1;
            --canvas-bg: #11191f; 
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #212121;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-family: 'Roboto Mono', monospace;
            font-size: 3.5em;
            color: var(--primary-color);
            margin: 0;
            text-shadow: 0 0 10px var(--glow-color);
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: #B0BEC5;
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--primary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding: 10px 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            background: #212121;
            border-radius: 5px;
            color: #CFD8DC;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #455A64;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            font-family: 'Roboto Mono', monospace;
            margin-top: 0;
            color: var(--text-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: var(--canvas-bg);
            box-shadow: 0 0 15px var(--glow-color);
            transform: translateY(-2px);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            background: var(--canvas-bg);
        }

        #animation-canvas {
            width: 100%;
            height: calc(100% - 80px);
            border-radius: 15px;
        }
        
        .control-button {
            position: absolute;
            bottom: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: var(--canvas-bg);
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 0 20px var(--glow-color);
            z-index: 10;
        }
        .control-button:hover { background-color: var(--secondary-color); }
        .control-button.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>inspect</h1>
            <p class="pronunciation">[ɪnˈspekt]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 检查, 视察, 审视</p>
                <div class="example">
                    <p><strong>例句：</strong> Health officials were sent to inspect the restaurant's kitchen.</p>
                    <p><strong>翻译：</strong> 卫生官员被派去检查餐厅的厨房。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="in-game">in- (in, into, 进入)</button>
                    <button class="morpheme-btn" data-activity="spect-game">-spect- (to look, 看)</button>
                </div>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动 (Canvas)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示：X光扫描仪</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <canvas id="animation-canvas"></canvas>
            <button id="control-btn" class="control-button hidden">Inspect!</button>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const canvas = document.getElementById('animation-canvas');
        const ctx = canvas.getContext('2d');
        const controlBtn = document.getElementById('control-btn');
        let animationFrameId;

        const panel = canvas.parentElement;
        canvas.width = panel.clientWidth;
        canvas.height = panel.clientHeight - 80;

        let elements = {};

        function welcomeScreen() {
            ctx.fillStyle = 'var(--canvas-bg)';
            ctx.fillRect(0,0,canvas.width,canvas.height);
            ctx.fillStyle = 'var(--primary-color)';
            ctx.font = 'bold 28px Roboto';
            ctx.textAlign = 'center';
            ctx.fillText('🚀 新的篇章已开启! 🚀', canvas.width / 2, canvas.height / 2 - 30);
            ctx.font = '20px Roboto';
            ctx.fillText('第一个单词: inspect. 准备好深入观察了吗?', canvas.width / 2, canvas.height / 2 + 20);
            controlBtn.classList.add('hidden');
        }

        // --- Morpheme Games ---
        function initInGame() {
            let box = { x: canvas.width/2 - 50, y: canvas.height/2 - 50, w: 100, h: 100, zoom: 1 };
            controlBtn.textContent = '深入 (in-)';
            controlBtn.classList.remove('hidden');
            let zooming = false;
            controlBtn.onclick = () => { zooming = true; };
            
            function animateInGame() {
                ctx.fillStyle = 'var(--canvas-bg)'; ctx.fillRect(0,0,canvas.width,canvas.height);
                ctx.save();
                if(zooming) box.zoom += 0.05;
                ctx.translate(canvas.width/2, canvas.height/2);
                ctx.scale(box.zoom, box.zoom);
                ctx.translate(-canvas.width/2, -canvas.height/2);
                
                ctx.strokeStyle = 'var(--primary-color)';
                ctx.lineWidth = 3 / box.zoom;
                ctx.strokeRect(box.x, box.y, box.w, box.h);
                ctx.fillStyle = 'var(--primary-color)';
                ctx.font = `${10 / box.zoom}px Roboto Mono`;
                ctx.fillText("INSIDE", box.x + box.w/2, box.y + box.h/2);
                ctx.restore();
                
                animationFrameId = requestAnimationFrame(animateInGame);
            }
            animateInGame();
        }

        function initSpectGame() {
             let eye = { x: 50, y: canvas.height/2 };
             let targets = Array(5).fill().map(() => ({x: Math.random()*canvas.width/2 + canvas.width/2, y: Math.random()*canvas.height, size: 10, visible: false}));
             controlBtn.textContent = '观察 (spect-)';
             controlBtn.classList.remove('hidden');
             let looking = false;
             controlBtn.onclick = () => { looking = true; };

             function animateSpectGame() {
                ctx.fillStyle = 'var(--canvas-bg)'; ctx.fillRect(0,0,canvas.width,canvas.height);
                // Draw eye
                ctx.fillStyle = 'white'; ctx.beginPath(); ctx.arc(eye.x, eye.y, 20, 0, Math.PI*2); ctx.fill();
                ctx.fillStyle = 'var(--primary-color)'; ctx.beginPath(); ctx.arc(eye.x, eye.y, 10, 0, Math.PI*2); ctx.fill();

                // Draw vision cone
                if(looking) {
                    ctx.beginPath();
                    ctx.moveTo(eye.x, eye.y);
                    ctx.lineTo(canvas.width, 0);
                    ctx.lineTo(canvas.width, canvas.height);
                    ctx.closePath();
                    ctx.fillStyle = 'rgba(0, 230, 118, 0.2)';
                    ctx.fill();

                    targets.forEach(t => t.visible = true);
                }

                // Draw targets
                targets.forEach(t => {
                    if(t.visible) ctx.fillStyle = 'var(--glow-color)';
                    else ctx.fillStyle = '#455A64';
                    ctx.beginPath(); ctx.arc(t.x, t.y, t.size, 0, Math.PI*2); ctx.fill();
                });

                animationFrameId = requestAnimationFrame(animateSpectGame);
             }
             animateSpectGame();
        }
        
        // --- Full Animation ---
        function initFullAnimation() {
            let scannerY = 0;
            let artifact = {
                x: canvas.width/2, y: canvas.height/2,
                points: Array(8).fill().map((_, i) => ({
                    angle: i/8 * Math.PI*2,
                    radius: 100 + Math.random()*50,
                    innerRadius: 40 + Math.random()*20
                }))
            };
            
            controlBtn.textContent = 'Inspect!';
            controlBtn.classList.remove('hidden');
            let scanning = false;
            controlBtn.onclick = () => { scanning = true; scannerY = 0; };
            
            function drawArtifact(wireframe = false) {
                ctx.beginPath();
                const firstPoint = artifact.points[0];
                const firstX = artifact.x + Math.cos(firstPoint.angle) * firstPoint.radius;
                const firstY = artifact.y + Math.sin(firstPoint.angle) * firstPoint.radius;
                ctx.moveTo(firstX, firstY);

                artifact.points.forEach(p => {
                    const x = artifact.x + Math.cos(p.angle) * p.radius;
                    const y = artifact.y + Math.sin(p.angle) * p.radius;
                    ctx.lineTo(x, y);
                });
                ctx.closePath();
                
                if(wireframe) {
                    ctx.strokeStyle = 'var(--primary-color)';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    // draw inner parts
                    ctx.beginPath();
                    artifact.points.forEach(p => {
                         const x = artifact.x + Math.cos(p.angle) * p.innerRadius;
                         const y = artifact.y + Math.sin(p.angle) * p.innerRadius;
                         ctx.lineTo(x, y);
                    });
                    ctx.closePath();
                    ctx.stroke();

                } else {
                    ctx.fillStyle = '#546E7A';
                    ctx.fill();
                }
            }

            function animateFull() {
                ctx.fillStyle = 'var(--canvas-bg)'; ctx.fillRect(0,0,canvas.width,canvas.height);
                
                // Draw normal artifact
                drawArtifact(false);
                
                // Draw scanner effect
                if (scanning) {
                    ctx.save();
                    ctx.beginPath();
                    ctx.rect(0, scannerY - 20, canvas.width, 40);
                    ctx.clip();
                    // Draw wireframe version inside the clipped area
                    drawArtifact(true);
                    ctx.restore();

                    // Draw scanner line
                    ctx.fillStyle = 'var(--primary-color)';
                    ctx.shadowColor = 'var(--glow-color)';
                    ctx.shadowBlur = 20;
                    ctx.fillRect(0, scannerY, canvas.width, 3);
                    ctx.shadowBlur = 0;

                    scannerY += 3;
                    if(scannerY > canvas.height) scanning = false;
                }

                animationFrameId = requestAnimationFrame(animateFull);
            }
            animateFull();
        }

        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                if (animationFrameId) cancelAnimationFrame(animationFrameId);
                const activity = btn.dataset.activity;
                if (activity === 'in-game') initInGame();
                else if (activity === 'spect-game') initSpectGame();
                else if (activity === 'full-animation') initFullAnimation();
            });
        });

        welcomeScreen();
    });
    </script>
</body>
</html> 