<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>磁盘I/O调度动画演示</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #2c3e50;
            margin: 0;
            padding: 40px 20px;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 60px;
            max-width: 1200px;
            margin: 0 auto;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 60px;
            position: relative;
            letter-spacing: -0.02em;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        h2 {
            color: #34495e;
            font-size: 1.8rem;
            font-weight: 600;
            margin: 40px 0 20px 0;
            position: relative;
            padding-left: 20px;
        }

        h2::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 3px;
        }

        .concept-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }

        .concept-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .concept-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
            border-color: #667eea;
        }

        .concept-card:hover::before {
            transform: scaleX(1);
        }

        .concept-card.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
        }

        .concept-card.active::before {
            transform: scaleX(1);
        }

        .concept-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 24px;
            color: white;
            transition: all 0.3s ease;
        }

        .concept-card:hover .concept-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .concept-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .concept-description {
            color: #7f8c8d;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .problem-box {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            border: none;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
        }

        .problem-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .problem-box strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .visualization-area {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: start;
            margin: 50px 0;
        }

        .canvas-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
        }

        #diskCanvas {
            border: none;
            border-radius: 50%;
            background: radial-gradient(circle, #f8f9ff 0%, #e8f0ff 100%);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        #diskCanvas:hover {
            transform: scale(1.02);
        }

        .controls-explanation-area {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .controls button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 15px 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .controls button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .controls button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
        }

        .controls button:hover::before {
            left: 100%;
        }

        .controls button:active {
            transform: translateY(0);
        }

        .controls button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .controls button:disabled::before {
            display: none;
        }

        #explanation {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            border: none;
            border-radius: 16px;
            padding: 25px;
            font-size: 1rem;
            line-height: 1.7;
            transition: all 0.5s ease;
            position: relative;
            overflow: hidden;
            min-height: 150px;
        }

        #explanation::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        #explanation p {
            margin: 0 0 15px 0;
            transition: all 0.5s ease;
            position: relative;
            padding-left: 20px;
        }

        .summary, .optimization-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
            transition: all 0.5s ease;
            transform: translateY(20px);
            opacity: 0;
        }

        .summary.show, .optimization-section.show {
            transform: translateY(0);
            opacity: 1;
        }

        .summary code, .optimization-section code {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            padding: 6px 12px;
            border-radius: 8px;
            font-family: 'JetBrains Mono', 'Courier New', monospace;
            font-weight: 500;
            color: #2c3e50;
            border: 1px solid #e8f0ff;
        }

        .interactive-timeline {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
        }

        .timeline-step {
            display: flex;
            align-items: center;
            padding: 20px;
            margin: 10px 0;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .timeline-step:hover {
            background: #f8f9ff;
            border-color: #667eea;
        }

        .timeline-step.active {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            border-color: #667eea;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 20px;
            transition: all 0.3s ease;
        }

        .timeline-step:hover .step-number {
            transform: scale(1.1);
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .step-description {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px;
            }

            .visualization-area {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .concept-breakdown {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <h1>磁盘I/O时间计算交互演示</h1>

    <!-- 概念分解卡片 -->
    <div class="concept-breakdown">
        <div class="concept-card" data-concept="disk-structure">
            <div class="concept-icon">💿</div>
            <div class="concept-title">磁盘结构</div>
            <div class="concept-description">了解磁盘的物理结构：磁道、扇区、磁头等基本概念</div>
        </div>
        <div class="concept-card" data-concept="rotation-time">
            <div class="concept-icon">🔄</div>
            <div class="concept-title">旋转时间</div>
            <div class="concept-description">磁盘旋转一周需要20ms，每个块占用2ms时间</div>
        </div>
        <div class="concept-card" data-concept="processing-time">
            <div class="concept-icon">⚡</div>
            <div class="concept-title">处理时间</div>
            <div class="concept-description">CPU处理一个记录需要4ms，期间磁盘继续旋转</div>
        </div>
        <div class="concept-card" data-concept="sequential-access">
            <div class="concept-icon">📋</div>
            <div class="concept-title">顺序访问</div>
            <div class="concept-description">按R1, R2, ..., R10顺序处理，但可能产生等待时间</div>
        </div>
    </div>

    <div class="problem-box">
        <strong>题目背景:</strong>
        <p>数据存储在磁盘上的排列方式会影响I/O服务的总时间。假设一个磁道划分为10个物理块，每块存放1个逻辑记录。逻辑记录R1, R2, ..., R10存放在同一个磁道上，记录的安排顺序如下图所示（R1在1号块，R2在2号块，以此类推）。</p>
        <p><strong>已知条件:</strong></p>
        <ul>
            <li>磁盘旋转速度为 <strong>20ms/周</strong>。</li>
            <li>系统处理一个记录需要 <strong>4ms</strong> 的时间。</li>
            <li>处理方式是：按 R1, R2, ... R10 的顺序，<strong>处理完一个记录后，再读取下一个</strong>。</li>
        </ul>
        <p><strong>问题:</strong> 按照这种方式，处理完所有10个记录的总时间是多少？</p>
    </div>

    <!-- 交互式时间线 -->
    <div class="interactive-timeline">
        <h2>处理步骤分解</h2>
        <div class="timeline-step" data-step="1">
            <div class="step-number">1</div>
            <div class="step-content">
                <div class="step-title">读取并处理R1</div>
                <div class="step-description">读取2ms + 处理4ms = 6ms，磁头移动到第4块位置</div>
            </div>
        </div>
        <div class="timeline-step" data-step="2">
            <div class="step-number">2</div>
            <div class="step-content">
                <div class="step-title">等待并读取R2</div>
                <div class="step-description">等待16ms + 读取2ms + 处理4ms = 22ms</div>
            </div>
        </div>
        <div class="timeline-step" data-step="3">
            <div class="step-number">3</div>
            <div class="step-content">
                <div class="step-title">重复处理R3-R10</div>
                <div class="step-description">每个记录都需要22ms，共8个记录 = 176ms</div>
            </div>
        </div>
        <div class="timeline-step" data-step="4">
            <div class="step-number">4</div>
            <div class="step-content">
                <div class="step-title">计算总时间</div>
                <div class="step-description">6ms + 9×22ms = 204ms</div>
            </div>
        </div>
    </div>

    <div class="visualization-area">
        <div class="canvas-container">
            <canvas id="diskCanvas" width="400" height="400"></canvas>
        </div>
        <div class="controls-explanation-area">
            <h2>动画演示</h2>
            <div class="controls">
                <button id="playBtn">▶️ 开始</button>
                <button id="pauseBtn">⏸️ 暂停</button>
                <button id="resetBtn">🔄 重置</button>
                <button id="stepBtn">👆 单步</button>
                <button id="speedBtn">⚡ 2x速度</button>
            </div>
            <div id="explanation">
                <p>点击"开始"按钮，观看动画演示。</p>
            </div>
        </div>
    </div>

    <div class="summary" id="summary">
        <h2>📊 详细计算过程</h2>
        <div class="concept-breakdown">
            <div class="concept-card">
                <div class="concept-icon">1️⃣</div>
                <div class="concept-title">处理R1</div>
                <div class="concept-description">
                    读取(2ms) + 处理(4ms) = <code>6ms</code><br>
                    磁头此时越过了3个块，到达第4块位置
                </div>
            </div>
            <div class="concept-card">
                <div class="concept-icon">2️⃣</div>
                <div class="concept-title">处理R2</div>
                <div class="concept-description">
                    等待(16ms) + 读取(2ms) + 处理(4ms) = <code>22ms</code><br>
                    磁头需要从第4块转回第2块，等待8个块
                </div>
            </div>
            <div class="concept-card">
                <div class="concept-icon">🔄</div>
                <div class="concept-title">处理R3-R10</div>
                <div class="concept-description">
                    每个记录耗时 <code>22ms</code><br>
                    共9个记录：9 × 22ms = <code>198ms</code>
                </div>
            </div>
            <div class="concept-card">
                <div class="concept-icon">🎯</div>
                <div class="concept-title">总时间</div>
                <div class="concept-description">
                    6ms + 198ms = <code>204ms</code><br>
                    这就是非优化布局的总时间
                </div>
            </div>
        </div>
    </div>

    <div class="optimization-section" id="optimization">
        <h2>🚀 优化策略分析</h2>
        <div class="problem-box">
            <strong>💡 关键洞察：</strong>
            <p>读取并处理一个记录共耗时 <code>6ms</code> (2ms读取 + 4ms处理)。在这6ms内，磁头刚好转过了 <code>3</code> 个块 (6ms ÷ (20ms/10块) = 3块)。</p>
            <p><strong>优化思路：</strong>如果我们把下一个要读的记录，正好放在这第3个块之后的位置，不就省去了所有等待时间吗？</p>
        </div>

        <div class="concept-breakdown">
            <div class="concept-card">
                <div class="concept-icon">📐</div>
                <div class="concept-title">最优布局计算</div>
                <div class="concept-description">
                    R1(块1) → R2(块4) → R3(块7) → R4(块10)<br>
                    R5(块3) → R6(块6) → R7(块9) → R8(块2)<br>
                    R9(块5) → R10(块8)
                </div>
            </div>
            <div class="concept-card">
                <div class="concept-icon">⚡</div>
                <div class="concept-title">优化效果</div>
                <div class="concept-description">
                    每个记录无缝衔接，耗时都是 <code>6ms</code><br>
                    总时间：10 × 6ms = <code>60ms</code><br>
                    效率提升：204ms → 60ms (提升70%)
                </div>
            </div>
            <div class="concept-card">
                <div class="concept-icon">🎓</div>
                <div class="concept-title">学习要点</div>
                <div class="concept-description">
                    这就是<strong>磁盘调度算法</strong>的核心思想：<br>
                    通过合理安排数据布局，减少磁头等待时间，<br>
                    大幅提升I/O性能
                </div>
            </div>
        </div>

        <div class="controls" style="margin-top: 30px;">
            <button id="showOptimalBtn">🎯 查看最优布局动画</button>
            <button id="compareBtn">📊 对比两种方案</button>
        </div>
    </div>

</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('diskCanvas');
    const ctx = canvas.getContext('2d');
    const playBtn = document.getElementById('playBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const resetBtn = document.getElementById('resetBtn');
    const stepBtn = document.getElementById('stepBtn');
    const speedBtn = document.getElementById('speedBtn');
    const showOptimalBtn = document.getElementById('showOptimalBtn');
    const compareBtn = document.getElementById('compareBtn');
    const explanationDiv = document.getElementById('explanation');
    const summaryDiv = document.getElementById('summary');
    const optimizationDiv = document.getElementById('optimization');

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = 160;
    const numBlocks = 10;
    const blockAngle = (2 * Math.PI) / numBlocks;

    const timePerBlock = 2; // ms
    const timeToProcess = 4; // ms

    let animationFrameId;
    let isPaused = true;
    let currentAngle = -Math.PI / 2 - blockAngle / 2;
    let headAngle = currentAngle;
    let simulationTime = 0;
    let currentStep = 0;
    let lastTimestamp = 0;
    let speed = 1; // Visual speed multiplier
    let isStepMode = false;
    let isOptimalLayout = false;
    let showComparison = false;

    // 概念卡片交互
    const conceptCards = document.querySelectorAll('.concept-card');
    const timelineSteps = document.querySelectorAll('.timeline-step');

    conceptCards.forEach(card => {
        card.addEventListener('click', () => {
            conceptCards.forEach(c => c.classList.remove('active'));
            card.classList.add('active');

            const concept = card.dataset.concept;
            showConceptExplanation(concept);
        });
    });

    timelineSteps.forEach(step => {
        step.addEventListener('click', () => {
            timelineSteps.forEach(s => s.classList.remove('active'));
            step.classList.add('active');

            const stepNum = parseInt(step.dataset.step);
            jumpToStep(stepNum);
        });
    });

    const steps = [
        { record: 1, action: 'read' },
        { record: 1, action: 'process' },
        { record: 2, action: 'wait' },
        { record: 2, action: 'read' },
        { record: 2, action: 'process' },
    ];

    for (let i = 3; i <= 10; i++) {
        steps.push({ record: i, action: 'wait' });
        steps.push({ record: i, action: 'read' });
        steps.push({ record: i, action: 'process' });
    }

    let actionStartTime = 0;
    let actionDuration = 0;

    function drawDisk() {
        // Clear canvas with gradient background
        const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius + 50);
        gradient.addColorStop(0, '#f8f9ff');
        gradient.addColorStop(1, '#e8f0ff');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw outer ring
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius + 25, 0, 2 * Math.PI);
        ctx.strokeStyle = '#e0e6ed';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw track with gradient
        const trackGradient = ctx.createRadialGradient(centerX, centerY, radius - 15, centerX, centerY, radius + 15);
        trackGradient.addColorStop(0, '#ffffff');
        trackGradient.addColorStop(0.5, '#f0f2f5');
        trackGradient.addColorStop(1, '#e8eaed');

        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.fillStyle = trackGradient;
        ctx.fill();
        ctx.strokeStyle = '#d1d5db';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw block divisions and labels
        for (let i = 0; i < numBlocks; i++) {
            const angle = i * blockAngle - Math.PI / 2;

            // Highlight current action block with animation
            if(!isPaused && currentStep < steps.length) {
                const currentRecordIndex = steps[currentStep].record - 1;
                if(i === currentRecordIndex) {
                    const pulseIntensity = 0.5 + 0.5 * Math.sin(Date.now() * 0.01);

                    ctx.beginPath();
                    ctx.arc(centerX, centerY, radius, angle - blockAngle/2, angle + blockAngle/2);
                    const action = steps[currentStep].action;

                    if (action === 'read') {
                        ctx.strokeStyle = `rgba(102, 126, 234, ${pulseIntensity})`;
                        ctx.shadowColor = '#667eea';
                        ctx.shadowBlur = 15;
                    } else if (action === 'process') {
                        ctx.strokeStyle = `rgba(40, 167, 69, ${pulseIntensity})`;
                        ctx.shadowColor = '#28a745';
                        ctx.shadowBlur = 15;
                    } else if (action === 'wait') {
                        ctx.strokeStyle = `rgba(220, 53, 69, ${pulseIntensity})`;
                        ctx.shadowColor = '#dc3545';
                        ctx.shadowBlur = 15;
                    }

                    ctx.lineWidth = 25;
                    ctx.stroke();
                    ctx.shadowBlur = 0;
                }
            }

            // Division lines with subtle styling
            const startX = centerX + (radius-15) * Math.cos(angle - blockAngle / 2);
            const startY = centerY + (radius-15) * Math.sin(angle - blockAngle / 2);
            const endX = centerX + (radius+15) * Math.cos(angle - blockAngle / 2);
            const endY = centerY + (radius+15) * Math.sin(angle - blockAngle / 2);
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.strokeStyle = '#d1d5db';
            ctx.lineWidth = 1;
            ctx.stroke();

            // Block numbers with better styling
            const textAngle = angle;
            const textX = centerX + (radius + 40) * Math.cos(textAngle);
            const textY = centerY + (radius + 40) * Math.sin(textAngle);

            // Block number background
            ctx.beginPath();
            ctx.arc(textX, textY, 18, 0, 2 * Math.PI);
            ctx.fillStyle = '#667eea';
            ctx.fill();

            // Block number text
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 14px Inter, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(i + 1, textX, textY);

            // Record labels with better positioning
            const recordAngle = angle;
            const recordX = centerX + radius * Math.cos(recordAngle);
            const recordY = centerY + radius * Math.sin(recordAngle);

            // Record background
            ctx.beginPath();
            ctx.arc(recordX, recordY, 15, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fill();
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.stroke();

            // Record text
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 12px Inter, sans-serif';
            ctx.fillText(`R${i+1}`, recordX, recordY);
        }

        // Draw center hub
        ctx.beginPath();
        ctx.arc(centerX, centerY, 30, 0, 2 * Math.PI);
        const hubGradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, 30);
        hubGradient.addColorStop(0, '#667eea');
        hubGradient.addColorStop(1, '#764ba2');
        ctx.fillStyle = hubGradient;
        ctx.fill();

        // Hub highlight
        ctx.beginPath();
        ctx.arc(centerX - 8, centerY - 8, 12, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.fill();
    }

    function drawHead() {
        const headX = centerX + (radius - 35) * Math.cos(headAngle);
        const headY = centerY + (radius - 35) * Math.sin(headAngle);

        // Draw head arm with gradient
        const armGradient = ctx.createLinearGradient(centerX, centerY, headX, headY);
        armGradient.addColorStop(0, '#2c3e50');
        armGradient.addColorStop(1, '#34495e');

        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.lineTo(headX, headY);
        ctx.strokeStyle = armGradient;
        ctx.lineWidth = 4;
        ctx.lineCap = 'round';
        ctx.stroke();

        // Draw head with glow effect
        ctx.shadowColor = '#667eea';
        ctx.shadowBlur = 10;
        ctx.beginPath();
        ctx.arc(headX, headY, 12, 0, 2 * Math.PI);
        const headGradient = ctx.createRadialGradient(headX, headY, 0, headX, headY, 12);
        headGradient.addColorStop(0, '#667eea');
        headGradient.addColorStop(1, '#764ba2');
        ctx.fillStyle = headGradient;
        ctx.fill();
        ctx.shadowBlur = 0;

        // Head highlight
        ctx.beginPath();
        ctx.arc(headX - 3, headY - 3, 4, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.fill();

        // Draw rotation direction indicator
        const indicatorRadius = radius + 60;
        const indicatorX = centerX + indicatorRadius * Math.cos(headAngle);
        const indicatorY = centerY + indicatorRadius * Math.sin(headAngle);

        ctx.beginPath();
        ctx.arc(indicatorX, indicatorY, 8, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
        ctx.fill();

        // Arrow showing rotation direction
        const arrowAngle = headAngle + Math.PI / 2;
        const arrowX1 = indicatorX + 15 * Math.cos(arrowAngle);
        const arrowY1 = indicatorY + 15 * Math.sin(arrowAngle);
        const arrowX2 = indicatorX + 10 * Math.cos(arrowAngle + 0.5);
        const arrowY2 = indicatorY + 10 * Math.sin(arrowAngle + 0.5);
        const arrowX3 = indicatorX + 10 * Math.cos(arrowAngle - 0.5);
        const arrowY3 = indicatorY + 10 * Math.sin(arrowAngle - 0.5);

        ctx.beginPath();
        ctx.moveTo(arrowX1, arrowY1);
        ctx.lineTo(arrowX2, arrowY2);
        ctx.lineTo(arrowX3, arrowY3);
        ctx.closePath();
        ctx.fillStyle = '#667eea';
        ctx.fill();
    }

    function updateExplanation() {
        if(isPaused && currentStep === 0) {
            explanationDiv.innerHTML = '<p>💡 点击"开始"按钮，观看动画演示。</p><p>🎯 或点击上方概念卡片了解基础知识。</p>';
            return;
        }
        if (currentStep >= steps.length) {
            explanationDiv.innerHTML = `
                <p><strong>🎉 演示完成!</strong></p>
                <p>📊 所有记录处理完毕</p>
                <p>⏱️ 总耗时: <strong>${simulationTime.toFixed(0)}ms</strong></p>
                <p>📈 查看下方详细分析和优化方案</p>
            `;
            summaryDiv.classList.add('show');
            optimizationDiv.classList.add('show');
            return;
        }

        const step = steps[currentStep];
        const timeInAction = simulationTime - actionStartTime;
        const progress = Math.min(timeInAction / actionDuration * 100, 100);

        let html = `<p><strong>⏰ 第 ${simulationTime.toFixed(0)} ms</strong> - 当前目标: <strong>R${step.record}</strong></p>`;
        html += `<div style="background: #e9ecef; border-radius: 10px; height: 8px; margin: 10px 0; overflow: hidden;">
                    <div style="background: linear-gradient(90deg, #667eea, #764ba2); height: 100%; width: ${progress}%; transition: width 0.1s ease;"></div>
                 </div>`;

        switch(step.action) {
            case 'read':
                html += `<p style="color:#667eea; font-weight: 600;">📖 正在读取 R${step.record}... (${timeInAction.toFixed(0)}ms / ${actionDuration}ms)</p>
                        <p>💾 读取1个数据块需要2ms时间</p>`;
                break;
            case 'process':
                html += `<p style="color:#28a745; font-weight: 600;">⚡ 正在处理 R${step.record}... (${timeInAction.toFixed(0)}ms / ${actionDuration}ms)</p>
                        <p>🖥️ CPU处理耗时4ms，期间磁盘继续旋转</p>`;
                break;
            case 'wait':
                const blocksToWait = (actionDuration / timePerBlock);
                html += `<p style="color:#dc3545; font-weight: 600;">⏳ 等待磁头旋转到 R${step.record}... (${timeInAction.toFixed(0)}ms / ${actionDuration}ms)</p>
                        <p>🔄 需等待${blocksToWait.toFixed(0)}个块通过，耗时${actionDuration}ms</p>`;
                break;
        }
        explanationDiv.innerHTML = html;
    }

    function showConceptExplanation(concept) {
        const explanations = {
            'disk-structure': `
                <p><strong>💿 磁盘结构基础</strong></p>
                <p>🔹 磁盘由多个同心圆磁道组成</p>
                <p>🔹 每个磁道分为若干扇区/块</p>
                <p>🔹 磁头负责读写数据</p>
                <p>🔹 磁盘以恒定速度旋转</p>
            `,
            'rotation-time': `
                <p><strong>🔄 旋转时间计算</strong></p>
                <p>🔹 磁盘旋转一周：20ms</p>
                <p>🔹 10个块，每块：20ms ÷ 10 = 2ms</p>
                <p>🔹 磁头位置决定等待时间</p>
                <p>🔹 最坏情况：等待整圈(20ms)</p>
            `,
            'processing-time': `
                <p><strong>⚡ 处理时间分析</strong></p>
                <p>🔹 读取时间：2ms (固定)</p>
                <p>🔹 处理时间：4ms (CPU计算)</p>
                <p>🔹 总计：6ms/记录</p>
                <p>🔹 处理期间磁盘继续旋转</p>
            `,
            'sequential-access': `
                <p><strong>📋 顺序访问问题</strong></p>
                <p>🔹 按R1→R2→R3...顺序处理</p>
                <p>🔹 处理完R1，磁头已转过3个块</p>
                <p>🔹 要读R2，需等待磁头转回</p>
                <p>🔹 产生大量等待时间</p>
            `
        };

        explanationDiv.innerHTML = explanations[concept] || '<p>选择一个概念了解详情</p>';
    }

    function jumpToStep(stepNum) {
        // 根据步骤跳转到对应的模拟状态
        reset();

        switch(stepNum) {
            case 1:
                currentStep = 1; // R1 processing
                simulationTime = 6;
                break;
            case 2:
                currentStep = 4; // R2 processing
                simulationTime = 28;
                break;
            case 3:
                currentStep = 10; // R4 processing
                simulationTime = 72;
                break;
            case 4:
                currentStep = steps.length; // Complete
                simulationTime = 204;
                break;
        }

        updateExplanation();
        drawDisk();
        drawHead();
    }

    function startAction(stepIndex) {
        if (stepIndex >= steps.length) {
            isPaused = true;
            playBtn.innerHTML = '✅ 完成';
            playBtn.disabled = true;
            pauseBtn.disabled = true;
            summaryDiv.classList.add('show');
            optimizationDiv.classList.add('show');
            return;
        }
        currentStep = stepIndex;
        const step = steps[currentStep];
        actionStartTime = simulationTime;

        if (step.action === 'read') {
            actionDuration = timePerBlock;
        } else if (step.action === 'process') {
            actionDuration = timeToProcess;
        } else if (step.action === 'wait') {
            const currentRecordIndex = (Math.floor((headAngle + Math.PI/2 + blockAngle/2) / blockAngle) + numBlocks) % numBlocks;
            const targetRecordIndex = step.record - 1;
            const diff = (targetRecordIndex - currentRecordIndex + numBlocks) % numBlocks;
            actionDuration = diff * timePerBlock;
        }
        if (actionDuration === 0 && step.action === 'wait') {
             startAction(currentStep + 1);
        }
    }

    function animate(timestamp) {
        if (!lastTimestamp) lastTimestamp = timestamp;
        const deltaTime = timestamp - lastTimestamp;
        lastTimestamp = timestamp;

        if (!isPaused && !isStepMode) {
            const timePassed = deltaTime * speed;
            simulationTime += timePassed;

            const angleChange = (timePassed / timePerBlock) * blockAngle;
            headAngle += angleChange;
            if (headAngle > 2 * Math.PI - Math.PI / 2 - blockAngle / 2) {
                 headAngle -= 2 * Math.PI;
            }

            if (simulationTime >= actionStartTime + actionDuration) {
                simulationTime = actionStartTime + actionDuration;
                if (isStepMode) {
                    isPaused = true;
                    isStepMode = false;
                }
                startAction(currentStep + 1);
            }
        }

        drawDisk();
        drawHead();
        updateExplanation();

        animationFrameId = requestAnimationFrame(animate);
    }

    function reset() {
        cancelAnimationFrame(animationFrameId);
        isPaused = true;
        isStepMode = false;
        currentStep = 0;
        simulationTime = 0;
        headAngle = -Math.PI / 2 - blockAngle / 2;
        lastTimestamp = 0;
        speed = 1;

        playBtn.innerHTML = '▶️ 开始';
        playBtn.disabled = false;
        pauseBtn.disabled = false;
        speedBtn.innerHTML = '⚡ 2x速度';

        summaryDiv.classList.remove('show');
        optimizationDiv.classList.remove('show');

        // 重置概念卡片和时间线
        conceptCards.forEach(c => c.classList.remove('active'));
        timelineSteps.forEach(s => s.classList.remove('active'));

        startAction(0);
        actionStartTime = 0;
        actionDuration = 0;
        requestAnimationFrame(animate);
    }

    // 事件监听器
    playBtn.addEventListener('click', () => {
        if(isPaused) {
            isPaused = false;
            isStepMode = false;
            lastTimestamp = performance.now();
            if(currentStep === 0 && simulationTime === 0) {
                 startAction(0);
            }
            playBtn.innerHTML = '⏸️ 暂停';
        } else {
            isPaused = true;
            playBtn.innerHTML = '▶️ 继续';
        }
    });

    pauseBtn.addEventListener('click', () => {
        isPaused = true;
        playBtn.innerHTML = '▶️ 继续';
    });

    resetBtn.addEventListener('click', reset);

    stepBtn.addEventListener('click', () => {
        if (isPaused && currentStep < steps.length) {
            isStepMode = true;
            isPaused = false;
            lastTimestamp = performance.now();
            if(currentStep === 0 && simulationTime === 0) {
                startAction(0);
            }
        }
    });

    speedBtn.addEventListener('click', () => {
        if (speed === 1) {
            speed = 2;
            speedBtn.innerHTML = '🐌 1x速度';
        } else {
            speed = 1;
            speedBtn.innerHTML = '⚡ 2x速度';
        }
    });

    showOptimalBtn.addEventListener('click', () => {
        // 这里可以添加最优布局的演示
        alert('🚀 最优布局演示功能开发中...\n\n💡 提示：最优布局将记录按间隔3个块的方式排列，\n使得处理完一个记录后，磁头正好到达下一个记录的位置，\n从而消除等待时间！');
    });

    compareBtn.addEventListener('click', () => {
        // 这里可以添加对比功能
        const comparison = `
📊 两种方案对比：

🔴 当前布局（顺序排列）：
• R1耗时：6ms
• R2-R10耗时：每个22ms
• 总时间：204ms

🟢 最优布局（间隔排列）：
• 每个记录耗时：6ms
• 总时间：60ms
• 效率提升：70%

💡 关键差异：最优布局消除了等待时间！
        `;
        alert(comparison);
    });

    // 添加键盘快捷键
    document.addEventListener('keydown', (e) => {
        switch(e.key) {
            case ' ':
                e.preventDefault();
                playBtn.click();
                break;
            case 'r':
                resetBtn.click();
                break;
            case 's':
                stepBtn.click();
                break;
        }
    });

    reset(); // 初始化
});
</script>

</body>
</html> 