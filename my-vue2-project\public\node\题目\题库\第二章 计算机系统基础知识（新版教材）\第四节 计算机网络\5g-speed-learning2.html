<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>5G网络速率学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .speed-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .speed-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 3px solid transparent;
        }

        .speed-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
        }

        .speed-card.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .speed-value {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .speed-label {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        #speedCanvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
        }

        .question-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.6;
            margin-bottom: 30px;
            text-align: center;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .option.correct {
            background: #4CAF50;
            border-color: #4CAF50;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            background: #f44336;
            border-color: #f44336;
            animation: shake 0.6s ease-in-out;
        }

        .explanation {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 3px;
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">5G网络速率学习</h1>
            <p class="subtitle">通过动画和交互理解网络速度概念</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📡 什么是网络速率？</h2>
            <div class="canvas-container">
                <canvas id="speedCanvas" width="800" height="400"></canvas>
            </div>
            <p style="text-align: center; margin-top: 20px; color: #666; font-size: 1.1rem;">
                点击下方的速度卡片，观看数据传输动画演示
            </p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🚀 不同网络速度对比</h2>
            <div class="speed-comparison">
                <div class="speed-card" data-speed="100" onclick="showSpeedDemo(100, '100M')">
                    <div class="speed-value">100M</div>
                    <div class="speed-label">4G网络</div>
                </div>
                <div class="speed-card" data-speed="1000" onclick="showSpeedDemo(1000, '1G')">
                    <div class="speed-value">1G</div>
                    <div class="speed-label">5G网络</div>
                </div>
                <div class="speed-card" data-speed="10000" onclick="showSpeedDemo(10000, '10G')">
                    <div class="speed-value">10G</div>
                    <div class="speed-label">理论最高</div>
                </div>
                <div class="speed-card" data-speed="1000000" onclick="showSpeedDemo(1000000, '1T')">
                    <div class="speed-value">1T</div>
                    <div class="speed-label">未来网络</div>
                </div>
            </div>
        </div>

        <div class="question-section">
            <h2 class="section-title" style="color: white;">📝 现在来答题吧！</h2>
            <div class="question-text">
                2019年我国将在多地展开5G试点，届时将在人口密集区为用户提供（ ）bps的用户体验速率。
            </div>
            
            <div class="options">
                <div class="option" onclick="selectOption(this, false)">
                    <strong>A. 100M</strong>
                </div>
                <div class="option" onclick="selectOption(this, true)">
                    <strong>B. 1G</strong>
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>C. 10G</strong>
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>D. 1T</strong>
                </div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="explanation" id="explanation">
                <h3>💡 答案解析</h3>
                <p><strong>正确答案：B (1G)</strong></p>
                <p>🎯 <strong>解题思路：</strong></p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>5G网络是4G的升级版，速度要比4G快很多</li>
                    <li>4G网络速度大约是100M，5G要比这快10倍左右</li>
                    <li>1G = 1000M，这是5G网络的典型用户体验速率</li>
                    <li>10G和1T都太快了，是理论值或未来技术</li>
                </ul>
                <button class="btn" onclick="resetQuiz()">🔄 重新练习</button>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('speedCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let particles = [];

        // 初始化画布
        function initCanvas() {
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制基础网络图
            drawNetwork();
        }

        function drawNetwork() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制发送端
            ctx.fillStyle = '#667eea';
            ctx.fillRect(50, 150, 80, 100);
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('发送端', 90, 205);
            
            // 绘制接收端
            ctx.fillStyle = '#764ba2';
            ctx.fillRect(670, 150, 80, 100);
            ctx.fillStyle = 'white';
            ctx.fillText('接收端', 710, 205);
            
            // 绘制传输路径
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(130, 200);
            ctx.lineTo(670, 200);
            ctx.stroke();
        }

        function createParticle(speed, label) {
            return {
                x: 130,
                y: 200,
                speed: speed / 100,
                size: Math.random() * 8 + 4,
                color: `hsl(${Math.random() * 60 + 200}, 70%, 60%)`,
                label: label
            };
        }

        function showSpeedDemo(speed, label) {
            // 清除之前的动画
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            // 重置粒子数组
            particles = [];
            
            // 更新活跃状态
            document.querySelectorAll('.speed-card').forEach(card => {
                card.classList.remove('active');
            });
            event.target.closest('.speed-card').classList.add('active');
            
            // 创建粒子
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    particles.push(createParticle(speed, label));
                }, i * 200);
            }
            
            animate();
        }

        function animate() {
            drawNetwork();
            
            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.x += particle.speed;
                
                // 绘制粒子
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y + Math.sin(particle.x * 0.01) * 10, particle.size, 0, Math.PI * 2);
                ctx.fill();
                
                // 粒子到达终点时的效果
                if (particle.x >= 670) {
                    // 创建爆炸效果
                    for (let i = 0; i < 5; i++) {
                        ctx.fillStyle = particle.color;
                        ctx.beginPath();
                        ctx.arc(
                            670 + Math.random() * 40 - 20,
                            200 + Math.random() * 40 - 20,
                            Math.random() * 3 + 1,
                            0, Math.PI * 2
                        );
                        ctx.fill();
                    }
                    return false;
                }
                
                return true;
            });
            
            // 显示速度信息
            if (particles.length > 0) {
                ctx.fillStyle = '#333';
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(`传输速度: ${particles[0].label}bps`, canvas.width / 2, 50);
            }
            
            if (particles.length > 0) {
                animationId = requestAnimationFrame(animate);
            }
        }

        function selectOption(element, isCorrect) {
            // 禁用所有选项
            document.querySelectorAll('.option').forEach(opt => {
                opt.style.pointerEvents = 'none';
            });
            
            if (isCorrect) {
                element.classList.add('correct');
                updateProgress(100);
                setTimeout(() => {
                    document.getElementById('explanation').style.display = 'block';
                }, 1000);
            } else {
                element.classList.add('wrong');
                updateProgress(25);
                // 显示正确答案
                setTimeout(() => {
                    document.querySelectorAll('.option')[1].classList.add('correct');
                    setTimeout(() => {
                        document.getElementById('explanation').style.display = 'block';
                    }, 1000);
                }, 1000);
            }
        }

        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        function resetQuiz() {
            // 重置所有选项
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
                opt.style.pointerEvents = 'auto';
            });
            
            // 隐藏解析
            document.getElementById('explanation').style.display = 'none';
            
            // 重置进度条
            updateProgress(0);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initCanvas();
            
            // 自动演示1G速度
            setTimeout(() => {
                showSpeedDemo(1000, '1G');
                document.querySelector('[data-speed="1000"]').classList.add('active');
            }, 1000);
        });
    </script>
</body>
</html>
