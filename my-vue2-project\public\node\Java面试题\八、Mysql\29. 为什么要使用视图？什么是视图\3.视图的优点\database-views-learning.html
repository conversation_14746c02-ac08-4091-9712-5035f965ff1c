<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库视图 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: justify;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: 600;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .game-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }

        .score {
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 20px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title floating">数据库视图</h1>
            <p class="subtitle">让我们一起探索数据库视图的奥秘</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是数据库视图？</h2>
            <div class="canvas-container">
                <canvas id="viewIntroCanvas" width="600" height="300"></canvas>
            </div>
            <p class="explanation">
                数据库视图就像是一个<span class="highlight">虚拟的窗户</span>，让我们可以从不同的角度观察同一份数据。
                想象一下，你有一个装满玩具的大箱子，视图就像是不同颜色的滤镜，让你只看到特定类型的玩具。
            </p>
            <div class="game-area">
                <button class="interactive-btn" onclick="startViewDemo()">点击体验视图魔法</button>
                <div class="score" id="score">得分: 0</div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">优点一：查询简单化</h2>
            <div class="canvas-container">
                <canvas id="simplificationCanvas" width="700" height="400"></canvas>
            </div>
            <p class="explanation">
                视图能够<span class="highlight">简化复杂的查询操作</span>。就像把复杂的魔法咒语变成简单的一个词！
                原本需要写很长很复杂的查询语句，现在只需要一个简单的视图名称就能搞定。
            </p>
            <div class="game-area">
                <button class="interactive-btn" onclick="demonstrateSimplification()">体验查询简化</button>
                <button class="interactive-btn" onclick="playSimplificationGame()">挑战简化游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">优点二：数据安全性</h2>
            <div class="canvas-container">
                <canvas id="securityCanvas" width="700" height="400"></canvas>
            </div>
            <p class="explanation">
                视图就像是数据的<span class="highlight">安全卫士</span>！它可以隐藏敏感信息，只显示用户需要看到的部分。
                就像给重要文件加上马赛克，保护隐私的同时还能正常使用数据。
            </p>
            <div class="game-area">
                <button class="interactive-btn" onclick="demonstrateSecurity()">体验安全保护</button>
                <button class="interactive-btn" onclick="playSecurityGame()">安全守护游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">优点三：逻辑数据独立性</h2>
            <div class="canvas-container">
                <canvas id="independenceCanvas" width="700" height="400"></canvas>
            </div>
            <p class="explanation">
                逻辑数据独立性听起来很复杂，其实就像是<span class="highlight">变形金刚</span>！
                即使底层的数据库结构发生变化，视图依然能保持稳定，用户感觉不到任何变化。
            </p>
            <div class="game-area">
                <button class="interactive-btn" onclick="demonstrateIndependence()">体验数据独立</button>
                <button class="interactive-btn" onclick="playIndependenceGame()">变形挑战游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">综合挑战</h2>
            <div class="canvas-container">
                <canvas id="finalGameCanvas" width="700" height="500"></canvas>
            </div>
            <p class="explanation">
                现在让我们把所有知识点结合起来，完成最终的<span class="highlight">视图大师挑战</span>！
                通过这个游戏，你将成为真正的数据库视图专家！
            </p>
            <div class="game-area">
                <button class="interactive-btn" onclick="startFinalGame()">开始终极挑战</button>
                <div class="score" id="finalScore">总得分: 0</div>
            </div>
        </div>
    </div>

    <script>
        let score = 0;
        let finalScore = 0;
        let animationIds = [];

        // 视图介绍动画
        function initViewIntroCanvas() {
            const canvas = document.getElementById('viewIntroCanvas');
            const ctx = canvas.getContext('2d');

            let time = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制数据库表（大箱子）
                ctx.fillStyle = '#4a90e2';
                ctx.fillRect(50, 150, 200, 100);
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('原始数据表', 150, 205);

                // 绘制数据记录（小方块）
                const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
                for (let i = 0; i < 5; i++) {
                    ctx.fillStyle = colors[i];
                    ctx.fillRect(70 + i * 30, 170, 20, 20);
                }

                // 绘制视图（滤镜效果）
                const viewX = 350 + Math.sin(time * 0.02) * 10;
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.fillRect(viewX, 100, 180, 150);
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.strokeRect(viewX, 100, 180, 150);

                ctx.fillStyle = '#333';
                ctx.fillText('视图窗口', viewX + 90, 130);

                // 绘制箭头
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(270, 200);
                ctx.lineTo(330, 175);
                ctx.stroke();

                // 绘制箭头头部
                ctx.beginPath();
                ctx.moveTo(330, 175);
                ctx.lineTo(320, 170);
                ctx.moveTo(330, 175);
                ctx.lineTo(320, 180);
                ctx.stroke();

                time++;
                animationIds[0] = requestAnimationFrame(animate);
            }

            animate();
        }

        // 查询简单化演示
        function initSimplificationCanvas() {
            const canvas = document.getElementById('simplificationCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            let time = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制复杂查询
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(50, 50, 250, 150);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('复杂SQL查询', 175, 80);
                ctx.fillText('SELECT * FROM table1', 175, 100);
                ctx.fillText('JOIN table2 ON...', 175, 120);
                ctx.fillText('WHERE condition...', 175, 140);
                ctx.fillText('GROUP BY...', 175, 160);
                ctx.fillText('ORDER BY...', 175, 180);

                // 绘制魔法变换效果
                const sparkles = [];
                for (let i = 0; i < 10; i++) {
                    const x = 320 + Math.cos(time * 0.1 + i) * 30;
                    const y = 125 + Math.sin(time * 0.1 + i) * 20;
                    ctx.fillStyle = `hsl(${(time + i * 36) % 360}, 70%, 60%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, 3, 0, Math.PI * 2);
                    ctx.fill();
                }

                // 绘制简化后的视图
                ctx.fillStyle = '#4ecdc4';
                ctx.fillRect(400, 100, 200, 100);
                ctx.fillStyle = 'white';
                ctx.fillText('简单视图', 500, 130);
                ctx.fillText('SELECT * FROM view', 500, 150);
                ctx.fillText('就这么简单！', 500, 170);

                // 绘制箭头
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(320, 125);
                ctx.lineTo(380, 150);
                ctx.stroke();

                time++;
                animationIds[1] = requestAnimationFrame(animate);
            }

            animate();
        }

        function startViewDemo() {
            score += 10;
            finalScore += 10;
            document.getElementById('score').textContent = `得分: ${score}`;
            document.getElementById('finalScore').textContent = `总得分: ${finalScore}`;

            // 添加脉冲效果
            const canvas = document.getElementById('viewIntroCanvas');
            canvas.classList.add('pulse');

            setTimeout(() => {
                canvas.classList.remove('pulse');
            }, 1000);

            // 显示鼓励信息
            showEncouragement();
        }

        function demonstrateSimplification() {
            score += 15;
            finalScore += 15;
            updateScores();

            const canvas = document.getElementById('simplificationCanvas');
            canvas.classList.add('pulse');

            setTimeout(() => {
                canvas.classList.remove('pulse');
            }, 1000);

            showEncouragement('查询简化演示完成！你看到了视图如何让复杂变简单！');
        }

        function playSimplificationGame() {
            // 简单的点击游戏
            let clicks = 0;
            const maxClicks = 5;

            const gameArea = event.target.parentElement;
            const originalHTML = gameArea.innerHTML;

            gameArea.innerHTML = `
                <div style="font-size: 1.2rem; margin-bottom: 20px;">
                    快速点击简化复杂查询！还需要点击 <span id="clicksLeft">${maxClicks}</span> 次
                </div>
                <button class="interactive-btn" id="gameBtn" style="font-size: 2rem; padding: 20px 40px;">
                    🔮 简化魔法
                </button>
            `;

            const btn = document.getElementById('gameBtn');
            const clicksLeft = document.getElementById('clicksLeft');

            btn.onclick = () => {
                clicks++;
                clicksLeft.textContent = maxClicks - clicks;

                if (clicks >= maxClicks) {
                    score += 25;
                    finalScore += 25;
                    updateScores();

                    gameArea.innerHTML = originalHTML;
                    showEncouragement('恭喜！你掌握了查询简化的精髓！');
                }
            };
        }

        function updateScores() {
            document.getElementById('score').textContent = `得分: ${score}`;
            if (document.getElementById('finalScore')) {
                document.getElementById('finalScore').textContent = `总得分: ${finalScore}`;
            }
        }

        function showEncouragement(customMessage = null) {
            const messages = customMessage ? [customMessage] : [
                '太棒了！你理解了视图的基本概念！',
                '继续探索，你会发现更多有趣的内容！',
                '视图让数据管理变得更简单！',
                '你正在成为数据库专家的路上！'
            ];

            const message = messages[Math.floor(Math.random() * messages.length)];

            // 创建临时提示
            const tip = document.createElement('div');
            tip.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px 40px;
                border-radius: 15px;
                font-size: 1.2rem;
                z-index: 1000;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                animation: fadeInDown 0.5s ease-out;
            `;
            tip.textContent = message;
            document.body.appendChild(tip);

            setTimeout(() => {
                tip.style.animation = 'fadeInDown 0.5s ease-out reverse';
                setTimeout(() => tip.remove(), 500);
            }, 2000);
        }

        // 数据安全性演示
        function initSecurityCanvas() {
            const canvas = document.getElementById('securityCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            let time = 0;
            let showSensitive = false;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制原始数据（包含敏感信息）
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(50, 50, 250, 200);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'left';
                ctx.fillText('原始数据表', 60, 75);
                ctx.fillText('姓名: 张三', 60, 100);
                ctx.fillText('年龄: 25', 60, 120);

                // 敏感信息
                if (showSensitive) {
                    ctx.fillStyle = '#ff0000';
                    ctx.fillText('身份证: 123456789012345678', 60, 140);
                    ctx.fillText('银行卡: 6222 0000 0000 0000', 60, 160);
                    ctx.fillText('密码: ********', 60, 180);
                } else {
                    ctx.fillStyle = '#333';
                    ctx.fillText('身份证: ███████████████████', 60, 140);
                    ctx.fillText('银行卡: ████ ████ ████ ████', 60, 160);
                    ctx.fillText('密码: ████████', 60, 180);
                }

                // 绘制安全卫士（盾牌）
                const shieldX = 350 + Math.sin(time * 0.05) * 5;
                const shieldY = 125;

                ctx.fillStyle = '#4ecdc4';
                ctx.beginPath();
                ctx.moveTo(shieldX, shieldY - 30);
                ctx.lineTo(shieldX - 20, shieldY - 10);
                ctx.lineTo(shieldX - 20, shieldY + 20);
                ctx.lineTo(shieldX, shieldY + 35);
                ctx.lineTo(shieldX + 20, shieldY + 20);
                ctx.lineTo(shieldX + 20, shieldY - 10);
                ctx.closePath();
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = '20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('🛡️', shieldX, shieldY + 5);

                // 绘制安全视图
                ctx.fillStyle = '#96ceb4';
                ctx.fillRect(450, 100, 200, 150);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'left';
                ctx.fillText('安全视图', 460, 125);
                ctx.fillText('姓名: 张三', 460, 145);
                ctx.fillText('年龄: 25', 460, 165);
                ctx.fillText('(敏感信息已隐藏)', 460, 185);
                ctx.fillStyle = '#4ecdc4';
                ctx.fillText('✓ 安全访问', 460, 220);

                time++;
                animationIds[2] = requestAnimationFrame(animate);
            }

            animate();
        }

        function demonstrateSecurity() {
            score += 15;
            finalScore += 15;
            updateScores();

            const canvas = document.getElementById('securityCanvas');
            canvas.classList.add('pulse');

            setTimeout(() => {
                canvas.classList.remove('pulse');
            }, 1000);

            showEncouragement('安全保护演示完成！视图就像数据的守护神！');
        }

        function playSecurityGame() {
            const gameArea = event.target.parentElement;
            const originalHTML = gameArea.innerHTML;

            gameArea.innerHTML = `
                <div style="font-size: 1.2rem; margin-bottom: 20px;">
                    点击隐藏敏感信息！保护数据安全！
                </div>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <div>姓名: 李四</div>
                    <div id="sensitive1" style="color: red;">身份证: 987654321098765432</div>
                    <div id="sensitive2" style="color: red;">银行卡: 6666 8888 9999 0000</div>
                    <div>职业: 工程师</div>
                </div>
                <button class="interactive-btn" onclick="hideSensitiveInfo()">🛡️ 启动安全保护</button>
                <button class="interactive-btn" onclick="restoreGame()">重置游戏</button>
            `;
        }

        function hideSensitiveInfo() {
            const sensitive1 = document.getElementById('sensitive1');
            const sensitive2 = document.getElementById('sensitive2');

            if (sensitive1 && sensitive2) {
                sensitive1.innerHTML = '身份证: ███████████████████';
                sensitive1.style.color = '#333';
                sensitive2.innerHTML = '银行卡: ████ ████ ████ ████';
                sensitive2.style.color = '#333';

                score += 20;
                finalScore += 20;
                updateScores();

                showEncouragement('太棒了！你成功保护了敏感数据！');
            }
        }

        function restoreGame() {
            const sensitive1 = document.getElementById('sensitive1');
            const sensitive2 = document.getElementById('sensitive2');

            if (sensitive1 && sensitive2) {
                sensitive1.innerHTML = '身份证: 987654321098765432';
                sensitive1.style.color = 'red';
                sensitive2.innerHTML = '银行卡: 6666 8888 9999 0000';
                sensitive2.style.color = 'red';
            }
        }

        // 数据独立性演示
        function initIndependenceCanvas() {
            const canvas = document.getElementById('independenceCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            let time = 0;
            let transforming = false;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制底层数据库结构
                const dbY = transforming ? 200 + Math.sin(time * 0.1) * 20 : 200;
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(50, dbY, 200, 100);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('底层数据库', 150, dbY + 30);
                ctx.fillText(transforming ? '结构变化中...' : '原始结构', 150, dbY + 50);

                // 绘制变形效果
                if (transforming) {
                    for (let i = 0; i < 15; i++) {
                        const x = 150 + Math.cos(time * 0.1 + i * 0.4) * 40;
                        const y = dbY + 50 + Math.sin(time * 0.1 + i * 0.4) * 20;
                        ctx.fillStyle = `hsl(${(time * 2 + i * 24) % 360}, 70%, 60%)`;
                        ctx.beginPath();
                        ctx.arc(x, y, 2, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }

                // 绘制稳定的视图层
                ctx.fillStyle = '#4ecdc4';
                ctx.fillRect(400, 150, 200, 100);
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.fillText('视图层', 500, 180);
                ctx.fillText('始终稳定', 500, 200);
                ctx.fillText('用户无感知', 500, 220);

                // 绘制连接线
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(270, dbY + 50);
                ctx.lineTo(380, 200);
                ctx.stroke();
                ctx.setLineDash([]);

                time++;
                animationIds[3] = requestAnimationFrame(animate);
            }

            animate();
        }

        function demonstrateIndependence() {
            score += 15;
            finalScore += 15;
            updateScores();

            const canvas = document.getElementById('independenceCanvas');
            canvas.classList.add('pulse');

            setTimeout(() => {
                canvas.classList.remove('pulse');
            }, 1000);

            showEncouragement('数据独立性演示完成！视图就像变形金刚一样灵活！');
        }

        function playIndependenceGame() {
            const gameArea = event.target.parentElement;
            const originalHTML = gameArea.innerHTML;

            let transformCount = 0;
            const maxTransforms = 3;

            gameArea.innerHTML = `
                <div style="font-size: 1.2rem; margin-bottom: 20px;">
                    测试数据独立性！点击变形按钮 ${maxTransforms} 次
                </div>
                <div style="display: flex; justify-content: space-around; margin: 20px 0;">
                    <div style="background: #ff6b6b; color: white; padding: 20px; border-radius: 10px;">
                        <div>底层数据库</div>
                        <div id="dbStatus">结构A</div>
                    </div>
                    <div style="background: #4ecdc4; color: white; padding: 20px; border-radius: 10px;">
                        <div>视图层</div>
                        <div>始终稳定</div>
                    </div>
                </div>
                <button class="interactive-btn" id="transformBtn">🔄 数据库变形</button>
                <div style="margin-top: 10px;">变形次数: <span id="transformCount">0</span>/${maxTransforms}</div>
            `;

            const transformBtn = document.getElementById('transformBtn');
            const dbStatus = document.getElementById('dbStatus');
            const countDisplay = document.getElementById('transformCount');

            const structures = ['结构A', '结构B', '结构C', '结构D'];

            transformBtn.onclick = () => {
                transformCount++;
                dbStatus.textContent = structures[transformCount % structures.length];
                countDisplay.textContent = transformCount;

                if (transformCount >= maxTransforms) {
                    score += 30;
                    finalScore += 30;
                    updateScores();

                    setTimeout(() => {
                        gameArea.innerHTML = originalHTML;
                        showEncouragement('恭喜！你理解了数据独立性的强大之处！');
                    }, 1000);
                }
            };
        }

        // 终极挑战游戏
        function initFinalGameCanvas() {
            const canvas = document.getElementById('finalGameCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            let time = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制游戏背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制星星背景
                for (let i = 0; i < 20; i++) {
                    const x = (i * 37 + time * 0.5) % canvas.width;
                    const y = (i * 23 + Math.sin(time * 0.01 + i) * 10) % canvas.height;
                    ctx.fillStyle = `rgba(255, 255, 255, ${0.3 + Math.sin(time * 0.02 + i) * 0.2})`;
                    ctx.beginPath();
                    ctx.arc(x, y, 2, 0, Math.PI * 2);
                    ctx.fill();
                }

                // 绘制中央标题
                ctx.fillStyle = 'white';
                ctx.font = 'bold 32px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('视图大师挑战', canvas.width / 2, 100);

                // 绘制三个知识点图标
                const icons = ['🔍', '🛡️', '🔄'];
                const labels = ['查询简化', '数据安全', '数据独立'];

                for (let i = 0; i < 3; i++) {
                    const x = 150 + i * 200;
                    const y = 250 + Math.sin(time * 0.03 + i) * 10;

                    // 绘制圆形背景
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
                    ctx.beginPath();
                    ctx.arc(x, y, 50, 0, Math.PI * 2);
                    ctx.fill();

                    // 绘制图标
                    ctx.font = '40px Microsoft YaHei';
                    ctx.fillStyle = 'white';
                    ctx.fillText(icons[i], x, y + 10);

                    // 绘制标签
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText(labels[i], x, y + 80);
                }

                // 绘制连接线
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(200, 250);
                ctx.lineTo(350, 250);
                ctx.moveTo(350, 250);
                ctx.lineTo(500, 250);
                ctx.stroke();

                time++;
                animationIds[4] = requestAnimationFrame(animate);
            }

            animate();
        }

        function startFinalGame() {
            const gameArea = event.target.parentElement;
            const originalHTML = gameArea.innerHTML;

            let currentQuestion = 0;
            const questions = [
                {
                    question: "视图的主要作用是什么？",
                    options: ["存储数据", "简化查询", "删除数据", "备份数据"],
                    correct: 1,
                    explanation: "正确！视图主要用于简化复杂的查询操作。"
                },
                {
                    question: "视图如何保护数据安全？",
                    options: ["加密数据", "隐藏敏感信息", "删除数据", "压缩数据"],
                    correct: 1,
                    explanation: "正确！视图可以隐藏敏感信息，只显示用户需要的部分。"
                },
                {
                    question: "什么是逻辑数据独立性？",
                    options: ["数据不能修改", "视图不受底层结构变化影响", "数据完全独立", "视图可以独立存在"],
                    correct: 1,
                    explanation: "正确！即使底层数据库结构改变，视图依然保持稳定。"
                }
            ];

            function showQuestion() {
                const q = questions[currentQuestion];
                gameArea.innerHTML = `
                    <div style="font-size: 1.3rem; margin-bottom: 20px; color: #333;">
                        问题 ${currentQuestion + 1}/3: ${q.question}
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0;">
                        ${q.options.map((option, index) =>
                            `<button class="interactive-btn" onclick="selectAnswer(${index})" style="padding: 15px; text-align: left;">
                                ${String.fromCharCode(65 + index)}. ${option}
                            </button>`
                        ).join('')}
                    </div>
                    <div style="margin-top: 20px; color: #666;">
                        进度: ${currentQuestion + 1}/3
                    </div>
                `;
            }

            window.selectAnswer = function(selectedIndex) {
                const q = questions[currentQuestion];
                const isCorrect = selectedIndex === q.correct;

                if (isCorrect) {
                    score += 50;
                    finalScore += 50;
                    updateScores();
                }

                // 显示结果
                gameArea.innerHTML = `
                    <div style="font-size: 1.5rem; margin-bottom: 20px; color: ${isCorrect ? '#4ecdc4' : '#ff6b6b'};">
                        ${isCorrect ? '🎉 正确！' : '❌ 错误'}
                    </div>
                    <div style="font-size: 1.1rem; margin-bottom: 20px;">
                        ${q.explanation}
                    </div>
                    <button class="interactive-btn" onclick="nextQuestion()">
                        ${currentQuestion < questions.length - 1 ? '下一题' : '查看结果'}
                    </button>
                `;
            };

            window.nextQuestion = function() {
                currentQuestion++;
                if (currentQuestion < questions.length) {
                    showQuestion();
                } else {
                    // 游戏结束
                    const totalPossible = 150; // 3题 × 50分
                    const percentage = Math.round((score / totalPossible) * 100);

                    gameArea.innerHTML = `
                        <div style="text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 20px;">🏆 挑战完成！</div>
                            <div style="font-size: 1.5rem; margin-bottom: 10px;">最终得分: ${finalScore}</div>
                            <div style="font-size: 1.2rem; margin-bottom: 20px;">答题得分率: ${percentage}%</div>
                            <div style="font-size: 1.1rem; color: #666; margin-bottom: 20px;">
                                ${percentage >= 80 ? '🌟 优秀！你已经是视图大师了！' :
                                  percentage >= 60 ? '👍 不错！继续加油！' :
                                  '💪 还需要多练习哦！'}
                            </div>
                            <button class="interactive-btn" onclick="location.reload()">重新开始学习</button>
                        </div>
                    `;

                    // 清理全局函数
                    delete window.selectAnswer;
                    delete window.nextQuestion;
                }
            };

            showQuestion();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initViewIntroCanvas();
            setTimeout(() => initSimplificationCanvas(), 1000);
            setTimeout(() => initSecurityCanvas(), 2000);
            setTimeout(() => initIndependenceCanvas(), 3000);
            setTimeout(() => initFinalGameCanvas(), 4000);
        });

        // 页面卸载时清理动画
        window.addEventListener('beforeunload', () => {
            animationIds.forEach(id => {
                if (id) cancelAnimationFrame(id);
            });
        });
    </script>
</body>
</html>
