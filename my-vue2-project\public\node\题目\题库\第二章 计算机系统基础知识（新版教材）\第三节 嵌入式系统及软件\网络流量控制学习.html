<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络流量控制 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2em;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
            position: relative;
            overflow: hidden;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .canvas-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 2px solid #e9ecef;
        }

        canvas {
            border-radius: 10px;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3em;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            transform: translateX(10px);
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
        }

        .quiz-option.wrong {
            background: rgba(244, 67, 54, 0.3);
            border-color: #f44336;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            transition: transform 0.3s ease;
        }

        .concept-card:hover {
            transform: translateY(-5px);
        }

        .concept-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .concept-desc {
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 网络流量控制学习</h1>
            <p>通过动画和交互理解网络协议的核心概念</p>
        </div>

        <!-- 知识点介绍 -->
        <div class="section">
            <div class="section-title">
                <div class="icon">📚</div>
                核心概念介绍
            </div>
            
            <div class="concept-card">
                <div class="concept-title">1. TCP滑动窗口协议</div>
                <div class="concept-desc">TCP使用可变大小的滑动窗口来控制数据流量，窗口大小可以动态调整，不是固定的。</div>
            </div>

            <div class="concept-card">
                <div class="concept-title">2. 前向纠错 vs 后向纠错</div>
                <div class="concept-desc">前向纠错：接收端自行纠错；后向纠错：接收端请求重发。</div>
            </div>

            <div class="concept-card">
                <div class="concept-title">3. 数据报系统</div>
                <div class="concept-desc">IP协议不预先建立虚电路，每个数据报独立选择路由。</div>
            </div>
        </div>

        <!-- TCP窗口演示 -->
        <div class="section">
            <div class="section-title">
                <div class="icon">🔄</div>
                TCP滑动窗口演示
            </div>
            <div class="canvas-container">
                <canvas id="tcpCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="startTCPDemo()">开始演示</button>
                <button class="btn" onclick="adjustWindow()">调整窗口大小</button>
                <button class="btn" onclick="resetTCPDemo()">重置</button>
            </div>
            <div class="explanation">
                <strong>观察要点：</strong>TCP窗口大小是可变的，可以根据网络状况动态调整。窗口越大，可以同时发送的数据包越多。
            </div>
        </div>

        <!-- 纠错系统演示 -->
        <div class="section">
            <div class="section-title">
                <div class="icon">🔧</div>
                纠错系统对比演示
            </div>
            <div class="canvas-container">
                <canvas id="errorCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showForwardCorrection()">前向纠错演示</button>
                <button class="btn" onclick="showBackwardCorrection()">后向纠错演示</button>
                <button class="btn" onclick="resetErrorDemo()">重置</button>
            </div>
            <div class="explanation">
                <strong>关键区别：</strong>前向纠错系统中，接收端检测到错误后会自行纠正；后向纠错系统中，接收端会请求发送端重发。
            </div>
        </div>

        <!-- 数据报路由演示 -->
        <div class="section">
            <div class="section-title">
                <div class="icon">📡</div>
                数据报路由演示
            </div>
            <div class="canvas-container">
                <canvas id="routingCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="startRoutingDemo()">数据报路由演示</button>
                <button class="btn" onclick="showVirtualCircuit()">虚电路对比</button>
                <button class="btn" onclick="resetRoutingDemo()">重置</button>
            </div>
            <div class="explanation">
                <strong>重要概念：</strong>数据报系统中，每个数据包独立选择路由，不预先建立固定路径。
            </div>
        </div>

        <!-- 题目测试 -->
        <div class="quiz-section">
            <div class="section-title" style="color: white;">
                <div class="icon">🎯</div>
                题目测试
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="quiz-question">
                关于网络控制的叙述，正确的是（ ）。
            </div>

            <div class="quiz-options">
                <div class="quiz-option" onclick="selectOption(this, false)">
                    <strong>A.</strong> 由于TCP的窗口大小是固定的，所以防止拥塞的方法只能是超时重发
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    <strong>B.</strong> 在前向纠错系统中，当接收端检测到错误后就要请求发送端重发出错分组
                </div>
                <div class="quiz-option" onclick="selectOption(this, true)">
                    <strong>C.</strong> 在滑动窗口协议中，窗口的大小以及确认应答使得可以连续发送多个数据
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    <strong>D.</strong> 在数据报系统中，所有连续发送的数据都可以沿着预先建立的虚通路传送
                </div>
            </div>

            <div id="quizResult" style="margin-top: 20px; font-size: 1.1em; display: none;"></div>
        </div>
    </div>

    <script>
        // TCP窗口演示相关变量
        let tcpCanvas, tcpCtx;
        let windowSize = 4;
        let packets = [];
        let animationId;

        // 纠错系统演示相关变量
        let errorCanvas, errorCtx;
        let errorAnimationId;

        // 路由演示相关变量
        let routingCanvas, routingCtx;
        let routingAnimationId;

        // 初始化
        window.onload = function() {
            initTCPCanvas();
            initErrorCanvas();
            initRoutingCanvas();
        };

        // 初始化TCP画布
        function initTCPCanvas() {
            tcpCanvas = document.getElementById('tcpCanvas');
            tcpCtx = tcpCanvas.getContext('2d');
            drawTCPWindow();
        }

        // 绘制TCP窗口
        function drawTCPWindow() {
            tcpCtx.clearRect(0, 0, tcpCanvas.width, tcpCanvas.height);

            // 绘制发送端
            tcpCtx.fillStyle = '#667eea';
            tcpCtx.fillRect(50, 50, 100, 80);
            tcpCtx.fillStyle = 'white';
            tcpCtx.font = '14px Arial';
            tcpCtx.textAlign = 'center';
            tcpCtx.fillText('发送端', 100, 95);

            // 绘制接收端
            tcpCtx.fillStyle = '#764ba2';
            tcpCtx.fillRect(650, 50, 100, 80);
            tcpCtx.fillStyle = 'white';
            tcpCtx.fillText('接收端', 700, 95);

            // 绘制窗口
            tcpCtx.strokeStyle = '#667eea';
            tcpCtx.lineWidth = 3;
            tcpCtx.strokeRect(200, 150, windowSize * 60, 50);
            tcpCtx.fillStyle = '#667eea';
            tcpCtx.font = '16px Arial';
            tcpCtx.fillText(`窗口大小: ${windowSize}`, 200 + windowSize * 30, 140);

            // 绘制数据包
            for (let i = 0; i < windowSize; i++) {
                tcpCtx.fillStyle = i < packets.length ? '#4CAF50' : '#e0e0e0';
                tcpCtx.fillRect(210 + i * 60, 160, 40, 30);
                tcpCtx.fillStyle = 'white';
                tcpCtx.font = '12px Arial';
                tcpCtx.fillText(i + 1, 230 + i * 60, 178);
            }
        }

        // 开始TCP演示
        function startTCPDemo() {
            packets = [];
            let packetIndex = 0;

            const sendPacket = () => {
                if (packetIndex < windowSize) {
                    packets.push(packetIndex + 1);
                    packetIndex++;
                    drawTCPWindow();

                    // 添加发送动画
                    animatePacketSend(packetIndex);

                    setTimeout(sendPacket, 800);
                } else {
                    // 模拟确认应答
                    setTimeout(() => {
                        packets = [];
                        drawTCPWindow();
                        tcpCtx.fillStyle = '#4CAF50';
                        tcpCtx.font = '16px Arial';
                        tcpCtx.fillText('✓ 确认应答收到，可以发送下一批', 300, 250);
                    }, 1000);
                }
            };

            sendPacket();
        }

        // 数据包发送动画
        function animatePacketSend(packetNum) {
            let x = 150;
            const targetX = 650;
            const y = 90;

            const animate = () => {
                tcpCtx.fillStyle = '#FF9800';
                tcpCtx.beginPath();
                tcpCtx.arc(x, y, 8, 0, 2 * Math.PI);
                tcpCtx.fill();

                x += 5;
                if (x < targetX) {
                    setTimeout(() => {
                        tcpCtx.clearRect(x - 10, y - 10, 20, 20);
                        animate();
                    }, 50);
                }
            };

            animate();
        }

        // 调整窗口大小
        function adjustWindow() {
            windowSize = windowSize === 4 ? 8 : 4;
            packets = [];
            drawTCPWindow();

            // 显示调整说明
            tcpCtx.fillStyle = '#FF5722';
            tcpCtx.font = '16px Arial';
            tcpCtx.fillText(`窗口大小调整为: ${windowSize}`, 300, 250);
        }

        // 重置TCP演示
        function resetTCPDemo() {
            packets = [];
            windowSize = 4;
            drawTCPWindow();
        }

        // 初始化纠错系统画布
        function initErrorCanvas() {
            errorCanvas = document.getElementById('errorCanvas');
            errorCtx = errorCanvas.getContext('2d');
            drawErrorSystem();
        }

        // 绘制纠错系统
        function drawErrorSystem() {
            errorCtx.clearRect(0, 0, errorCanvas.width, errorCanvas.height);

            // 绘制发送端
            errorCtx.fillStyle = '#667eea';
            errorCtx.fillRect(50, 50, 100, 80);
            errorCtx.fillStyle = 'white';
            errorCtx.font = '14px Arial';
            errorCtx.textAlign = 'center';
            errorCtx.fillText('发送端', 100, 95);

            // 绘制接收端
            errorCtx.fillStyle = '#764ba2';
            errorCtx.fillRect(650, 50, 100, 80);
            errorCtx.fillStyle = 'white';
            errorCtx.fillText('接收端', 700, 95);

            // 绘制传输通道
            errorCtx.strokeStyle = '#ccc';
            errorCtx.lineWidth = 2;
            errorCtx.setLineDash([5, 5]);
            errorCtx.beginPath();
            errorCtx.moveTo(150, 90);
            errorCtx.lineTo(650, 90);
            errorCtx.stroke();
            errorCtx.setLineDash([]);
        }

        // 前向纠错演示
        function showForwardCorrection() {
            drawErrorSystem();

            // 标题
            errorCtx.fillStyle = '#333';
            errorCtx.font = 'bold 18px Arial';
            errorCtx.textAlign = 'center';
            errorCtx.fillText('前向纠错系统', 400, 30);

            // 步骤1：发送数据
            setTimeout(() => {
                errorCtx.fillStyle = '#4CAF50';
                errorCtx.fillRect(200, 80, 60, 20);
                errorCtx.fillStyle = 'white';
                errorCtx.font = '12px Arial';
                errorCtx.fillText('数据+纠错码', 230, 93);
            }, 500);

            // 步骤2：传输中出错
            setTimeout(() => {
                errorCtx.fillStyle = '#f44336';
                errorCtx.fillRect(350, 80, 60, 20);
                errorCtx.fillStyle = 'white';
                errorCtx.fillText('出错数据', 380, 93);

                // 错误标记
                errorCtx.fillStyle = '#f44336';
                errorCtx.font = '20px Arial';
                errorCtx.fillText('❌', 380, 120);
            }, 1500);

            // 步骤3：接收端自行纠错
            setTimeout(() => {
                errorCtx.fillStyle = '#4CAF50';
                errorCtx.fillRect(500, 80, 60, 20);
                errorCtx.fillStyle = 'white';
                errorCtx.font = '12px Arial';
                errorCtx.fillText('自动纠错', 530, 93);

                // 纠错过程
                errorCtx.fillStyle = '#4CAF50';
                errorCtx.font = '16px Arial';
                errorCtx.fillText('🔧 自行纠正', 530, 120);
            }, 2500);

            // 说明文字
            setTimeout(() => {
                errorCtx.fillStyle = '#333';
                errorCtx.font = '14px Arial';
                errorCtx.textAlign = 'left';
                errorCtx.fillText('前向纠错：接收端检测到错误后，利用纠错码自行纠正错误', 50, 200);
                errorCtx.fillText('优点：不需要重传，效率高', 50, 220);
                errorCtx.fillText('缺点：需要额外的纠错码，增加传输开销', 50, 240);
            }, 3500);
        }

        // 后向纠错演示
        function showBackwardCorrection() {
            drawErrorSystem();

            // 标题
            errorCtx.fillStyle = '#333';
            errorCtx.font = 'bold 18px Arial';
            errorCtx.textAlign = 'center';
            errorCtx.fillText('后向纠错系统', 400, 30);

            // 步骤1：发送数据
            setTimeout(() => {
                errorCtx.fillStyle = '#4CAF50';
                errorCtx.fillRect(200, 80, 60, 20);
                errorCtx.fillStyle = 'white';
                errorCtx.font = '12px Arial';
                errorCtx.fillText('原始数据', 230, 93);
            }, 500);

            // 步骤2：传输中出错
            setTimeout(() => {
                errorCtx.fillStyle = '#f44336';
                errorCtx.fillRect(350, 80, 60, 20);
                errorCtx.fillStyle = 'white';
                errorCtx.fillText('出错数据', 380, 93);

                errorCtx.fillStyle = '#f44336';
                errorCtx.font = '20px Arial';
                errorCtx.fillText('❌', 380, 120);
            }, 1500);

            // 步骤3：接收端检测错误
            setTimeout(() => {
                errorCtx.fillStyle = '#FF9800';
                errorCtx.fillRect(500, 80, 60, 20);
                errorCtx.fillStyle = 'white';
                errorCtx.font = '12px Arial';
                errorCtx.fillText('检测错误', 530, 93);
            }, 2500);

            // 步骤4：请求重发
            setTimeout(() => {
                // 绘制返回箭头
                errorCtx.strokeStyle = '#f44336';
                errorCtx.lineWidth = 3;
                errorCtx.beginPath();
                errorCtx.moveTo(650, 150);
                errorCtx.lineTo(150, 150);
                errorCtx.stroke();

                // 箭头
                errorCtx.beginPath();
                errorCtx.moveTo(150, 150);
                errorCtx.lineTo(170, 140);
                errorCtx.moveTo(150, 150);
                errorCtx.lineTo(170, 160);
                errorCtx.stroke();

                errorCtx.fillStyle = '#f44336';
                errorCtx.font = '14px Arial';
                errorCtx.fillText('请求重发', 350, 170);
            }, 3500);

            // 步骤5：重新发送
            setTimeout(() => {
                errorCtx.fillStyle = '#4CAF50';
                errorCtx.fillRect(200, 180, 60, 20);
                errorCtx.fillStyle = 'white';
                errorCtx.font = '12px Arial';
                errorCtx.fillText('重发数据', 230, 193);
            }, 4500);

            // 说明文字
            setTimeout(() => {
                errorCtx.fillStyle = '#333';
                errorCtx.font = '14px Arial';
                errorCtx.textAlign = 'left';
                errorCtx.fillText('后向纠错：接收端检测到错误后，请求发送端重新发送', 50, 280);
                errorCtx.fillText('优点：实现简单，不需要复杂的纠错码', 50, 300);
                errorCtx.fillText('缺点：需要重传，降低传输效率', 50, 320);
            }, 5500);
        }

        // 重置纠错演示
        function resetErrorDemo() {
            drawErrorSystem();
        }

        // 初始化路由画布
        function initRoutingCanvas() {
            routingCanvas = document.getElementById('routingCanvas');
            routingCtx = routingCanvas.getContext('2d');
            drawRoutingSystem();
        }

        // 绘制路由系统
        function drawRoutingSystem() {
            routingCtx.clearRect(0, 0, routingCanvas.width, routingCanvas.height);

            // 绘制网络节点
            const nodes = [
                {x: 100, y: 100, label: '源'},
                {x: 300, y: 50, label: 'R1'},
                {x: 300, y: 150, label: 'R2'},
                {x: 500, y: 50, label: 'R3'},
                {x: 500, y: 150, label: 'R4'},
                {x: 700, y: 100, label: '目标'}
            ];

            // 绘制连接线
            routingCtx.strokeStyle = '#ccc';
            routingCtx.lineWidth = 2;
            const connections = [
                [0, 1], [0, 2], [1, 3], [1, 4], [2, 3], [2, 4], [3, 5], [4, 5]
            ];

            connections.forEach(([from, to]) => {
                routingCtx.beginPath();
                routingCtx.moveTo(nodes[from].x, nodes[from].y);
                routingCtx.lineTo(nodes[to].x, nodes[to].y);
                routingCtx.stroke();
            });

            // 绘制节点
            nodes.forEach(node => {
                routingCtx.fillStyle = '#667eea';
                routingCtx.beginPath();
                routingCtx.arc(node.x, node.y, 25, 0, 2 * Math.PI);
                routingCtx.fill();

                routingCtx.fillStyle = 'white';
                routingCtx.font = '12px Arial';
                routingCtx.textAlign = 'center';
                routingCtx.fillText(node.label, node.x, node.y + 4);
            });
        }

        // 数据报路由演示
        function startRoutingDemo() {
            drawRoutingSystem();

            routingCtx.fillStyle = '#333';
            routingCtx.font = 'bold 16px Arial';
            routingCtx.textAlign = 'center';
            routingCtx.fillText('数据报路由 - 每个包独立选择路径', 400, 30);

            // 模拟三个数据包走不同路径
            const packets = [
                {id: 1, path: [0, 1, 3, 5], color: '#f44336'},
                {id: 2, path: [0, 2, 4, 5], color: '#4CAF50'},
                {id: 3, path: [0, 1, 4, 5], color: '#FF9800'}
            ];

            const nodes = [
                {x: 100, y: 100}, {x: 300, y: 50}, {x: 300, y: 150},
                {x: 500, y: 50}, {x: 500, y: 150}, {x: 700, y: 100}
            ];

            packets.forEach((packet, index) => {
                setTimeout(() => {
                    animatePacketRoute(packet, nodes);
                }, index * 1000);
            });

            // 说明文字
            setTimeout(() => {
                routingCtx.fillStyle = '#333';
                routingCtx.font = '14px Arial';
                routingCtx.textAlign = 'left';
                routingCtx.fillText('数据报特点：', 50, 220);
                routingCtx.fillText('• 每个数据包独立选择路由', 70, 240);
                routingCtx.fillText('• 不预先建立固定路径', 70, 260);
                routingCtx.fillText('• 包可能走不同路径到达目标', 70, 280);
            }, 4000);
        }

        // 数据包路由动画
        function animatePacketRoute(packet, nodes) {
            let currentStep = 0;

            const moveToNext = () => {
                if (currentStep < packet.path.length - 1) {
                    const from = nodes[packet.path[currentStep]];
                    const to = nodes[packet.path[currentStep + 1]];

                    animatePacketMove(from, to, packet.color, packet.id, () => {
                        currentStep++;
                        setTimeout(moveToNext, 300);
                    });
                }
            };

            moveToNext();
        }

        // 数据包移动动画
        function animatePacketMove(from, to, color, id, callback) {
            let progress = 0;
            const speed = 0.05;

            const animate = () => {
                progress += speed;

                const x = from.x + (to.x - from.x) * progress;
                const y = from.y + (to.y - from.y) * progress;

                // 清除之前的包
                routingCtx.clearRect(x - 15, y - 15, 30, 30);
                drawRoutingSystem();

                // 绘制当前包
                routingCtx.fillStyle = color;
                routingCtx.beginPath();
                routingCtx.arc(x, y, 8, 0, 2 * Math.PI);
                routingCtx.fill();

                routingCtx.fillStyle = 'white';
                routingCtx.font = '10px Arial';
                routingCtx.textAlign = 'center';
                routingCtx.fillText(id, x, y + 3);

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    callback();
                }
            };

            animate();
        }

        // 虚电路对比演示
        function showVirtualCircuit() {
            drawRoutingSystem();

            routingCtx.fillStyle = '#333';
            routingCtx.font = 'bold 16px Arial';
            routingCtx.textAlign = 'center';
            routingCtx.fillText('虚电路 - 预先建立固定路径', 400, 30);

            // 绘制固定路径
            const path = [0, 1, 3, 5];
            const nodes = [
                {x: 100, y: 100}, {x: 300, y: 50}, {x: 300, y: 150},
                {x: 500, y: 50}, {x: 500, y: 150}, {x: 700, y: 100}
            ];

            // 高亮固定路径
            routingCtx.strokeStyle = '#f44336';
            routingCtx.lineWidth = 4;
            for (let i = 0; i < path.length - 1; i++) {
                const from = nodes[path[i]];
                const to = nodes[path[i + 1]];
                routingCtx.beginPath();
                routingCtx.moveTo(from.x, from.y);
                routingCtx.lineTo(to.x, to.y);
                routingCtx.stroke();
            }

            // 说明文字
            setTimeout(() => {
                routingCtx.fillStyle = '#333';
                routingCtx.font = '14px Arial';
                routingCtx.textAlign = 'left';
                routingCtx.fillText('虚电路特点：', 50, 220);
                routingCtx.fillText('• 预先建立固定路径', 70, 240);
                routingCtx.fillText('• 所有数据沿同一路径传送', 70, 260);
                routingCtx.fillText('• IP协议不使用虚电路', 70, 280);
            }, 1000);
        }

        // 重置路由演示
        function resetRoutingDemo() {
            drawRoutingSystem();
        }

        // 题目选择功能
        function selectOption(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 标记选择
            if (isCorrect) {
                element.classList.add('correct');
                showResult(true);
            } else {
                element.classList.add('wrong');
                // 同时显示正确答案
                document.querySelectorAll('.quiz-option')[2].classList.add('correct');
                showResult(false);
            }

            // 更新进度条
            document.getElementById('progressFill').style.width = '100%';
        }

        // 显示结果
        function showResult(isCorrect) {
            const resultDiv = document.getElementById('quizResult');
            resultDiv.style.display = 'block';

            if (isCorrect) {
                resultDiv.innerHTML = `
                    <div style="color: #4CAF50; font-weight: bold;">
                        ✅ 回答正确！
                    </div>
                    <div style="margin-top: 10px;">
                        <strong>解析：</strong>选项C正确。在滑动窗口协议中，窗口大小和确认应答机制确实使得可以连续发送多个数据包，这是滑动窗口协议的核心特点。
                    </div>
                    <div style="margin-top: 10px;">
                        <strong>错误选项分析：</strong><br>
                        A. TCP窗口大小是可变的，不是固定的<br>
                        B. 前向纠错是自行纠错，不是请求重发<br>
                        D. 数据报系统不预先建立虚通路
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="color: #f44336; font-weight: bold;">
                        ❌ 回答错误，正确答案是C
                    </div>
                    <div style="margin-top: 10px;">
                        <strong>解析：</strong>滑动窗口协议允许发送方在收到确认之前连续发送多个数据包，窗口大小决定了可以同时发送的数据包数量。
                    </div>
                `;
            }
        }

        // 添加一些交互提示
        document.addEventListener('DOMContentLoaded', function() {
            // 为按钮添加点击效果
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 为概念卡片添加悬停效果
            document.querySelectorAll('.concept-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.2)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
                });
            });
        });
    </script>
</body>
</html>
