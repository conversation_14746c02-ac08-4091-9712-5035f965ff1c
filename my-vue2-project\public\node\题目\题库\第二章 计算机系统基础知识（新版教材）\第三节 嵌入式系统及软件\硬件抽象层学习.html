<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硬件抽象层 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .section {
            background: white;
            border-radius: 25px;
            padding: 50px;
            margin-bottom: 50px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2.2rem;
            color: #333;
            margin-bottom: 40px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        canvas {
            border: 3px solid #e0e0e0;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .explanation {
            font-size: 1.2rem;
            line-height: 2;
            color: #555;
            margin: 30px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .concept-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s ease;
            transform: translateY(0);
            position: relative;
            overflow: hidden;
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .concept-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4);
        }

        .concept-card:hover::before {
            opacity: 1;
            left: 100%;
        }

        .concept-title {
            font-size: 1.4rem;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .concept-desc {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: white;
        }

        .quiz-question {
            font-size: 1.4rem;
            margin-bottom: 40px;
            line-height: 1.8;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
        }

        .options {
            display: grid;
            gap: 20px;
            margin-bottom: 40px;
        }

        .option {
            background: rgba(255,255,255,0.15);
            padding: 25px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.6s ease;
        }

        .option:hover {
            background: rgba(255,255,255,0.25);
            transform: translateX(10px);
        }

        .option:hover::before {
            left: 100%;
        }

        .option.selected {
            border-color: white;
            background: rgba(255,255,255,0.3);
            transform: scale(1.02);
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
            animation: correctPulse 1s ease-out;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
            animation: wrongShake 0.5s ease-out;
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1.02); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 30px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.4s ease;
            margin: 15px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.6s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .result {
            margin-top: 30px;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            font-size: 1.3rem;
            display: none;
            line-height: 1.8;
        }

        .result.show {
            display: block;
            animation: resultSlideIn 0.8s ease-out;
        }

        @keyframes resultSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .control-panel {
            text-align: center;
            margin: 30px 0;
        }

        .control-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid white;
            padding: 12px 25px;
            border-radius: 25px;
            margin: 0 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: white;
            color: #667eea;
        }

        .control-btn.active {
            background: white;
            color: #667eea;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 硬件抽象层 (HAL)</h1>
            <p class="subtitle">Hardware Abstraction Layer - 软硬件之间的神奇桥梁</p>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是硬件抽象层？</h2>
            <div class="canvas-container">
                <canvas id="halCanvas" width="1000" height="500"></canvas>
            </div>
            <div class="explanation">
                <strong>🌟 简单理解：</strong>硬件抽象层就像是一个"翻译官"，它站在操作系统和硬件之间，把复杂多样的硬件"翻译"成操作系统能理解的统一语言。这样，操作系统就不用关心具体是什么硬件了！
            </div>
            <div class="control-panel">
                <button class="control-btn active" onclick="showDemo('overview')">整体架构</button>
                <button class="control-btn" onclick="showDemo('hiding')">隐藏多样性</button>
                <button class="control-btn" onclick="showDemo('isolation')">软硬件隔离</button>
                <button class="control-btn" onclick="showDemo('parallel')">并行开发</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">✨ 硬件抽象层的四大作用</h2>
            <div class="concept-grid">
                <div class="concept-card" onclick="showConcept('hide')">
                    <div class="concept-title">🎭 隐藏硬件多样性</div>
                    <div class="concept-desc">让操作系统看不到硬件的复杂差异，提供统一接口</div>
                </div>
                <div class="concept-card" onclick="showConcept('isolate')">
                    <div class="concept-title">🚧 软硬件隔离</div>
                    <div class="concept-desc">在操作系统和硬件之间建立隔离层，互不干扰</div>
                </div>
                <div class="concept-card" onclick="showConcept('parallel')">
                    <div class="concept-title">⚡ 并行开发</div>
                    <div class="concept-desc">软件和硬件可以同时开发，提高效率</div>
                </div>
                <div class="concept-card" onclick="showConcept('wrong')">
                    <div class="concept-title">❌ 不包含的内容</div>
                    <div class="concept-desc">HAL不包括设备驱动和任务调度</div>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="1000" height="400"></canvas>
            </div>
        </div>

        <div class="section quiz-section">
            <h2 class="section-title">🎯 现在来做题吧！</h2>
            <div class="quiz-question">
                以下关于嵌入式系统硬件抽象层的叙述，<strong>错误</strong>的是（ ）
            </div>
            <div class="options">
                <div class="option" onclick="selectOption(this, 'A')">
                    <strong>A.</strong> 硬件抽象层与硬件密切相关，可对操作系统隐藏硬件的多样性
                </div>
                <div class="option" onclick="selectOption(this, 'B')">
                    <strong>B.</strong> 硬件抽象层将操作系统与硬件平台隔开
                </div>
                <div class="option" onclick="selectOption(this, 'C')">
                    <strong>C.</strong> 硬件抽象层使软硬件的设计与调试可以并行
                </div>
                <div class="option" onclick="selectOption(this, 'D')">
                    <strong>D.</strong> 硬件抽象层应包括设备驱动程序和任务调度
                </div>
            </div>
            <button class="btn" onclick="checkAnswer()">🎯 提交答案</button>
            <button class="btn" onclick="showExplanation()">📚 查看解析</button>
            <div id="result" class="result"></div>
        </div>
    </div>

    <script>
        // Canvas 元素和上下文
        const halCanvas = document.getElementById('halCanvas');
        const halCtx = halCanvas.getContext('2d');
        const conceptCanvas = document.getElementById('conceptCanvas');
        const conceptCtx = conceptCanvas.getContext('2d');
        
        let animationFrame = 0;
        let currentDemo = 'overview';
        let selectedConcept = null;
        let selectedOption = null;
        
        // 绘制主要的HAL架构图
        function drawHALArchitecture() {
            halCtx.clearRect(0, 0, 1000, 500);
            
            // 背景渐变
            const gradient = halCtx.createLinearGradient(0, 0, 1000, 500);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            halCtx.fillStyle = gradient;
            halCtx.fillRect(0, 0, 1000, 500);
            
            switch(currentDemo) {
                case 'overview':
                    drawOverview();
                    break;
                case 'hiding':
                    drawHiding();
                    break;
                case 'isolation':
                    drawIsolation();
                    break;
                case 'parallel':
                    drawParallel();
                    break;
            }
        }
        
        function drawOverview() {
            // 三层架构
            const layers = [
                {name: '应用程序', y: 50, color: '#e74c3c', icon: '📱'},
                {name: '操作系统内核', y: 150, color: '#3498db', icon: '🖥️'},
                {name: '硬件抽象层 (HAL)', y: 250, color: '#f39c12', icon: '🔧'},
                {name: '硬件平台', y: 350, color: '#27ae60', icon: '💾'}
            ];
            
            layers.forEach((layer, index) => {
                // 层背景
                const pulse = Math.sin(animationFrame * 0.05 + index) * 0.1 + 0.9;
                halCtx.fillStyle = layer.color;
                halCtx.globalAlpha = pulse;
                halCtx.fillRect(200, layer.y, 600, 80);
                halCtx.globalAlpha = 1;
                
                // 层边框
                halCtx.strokeStyle = layer.color;
                halCtx.lineWidth = 3;
                halCtx.strokeRect(200, layer.y, 600, 80);
                
                // 图标和文字
                halCtx.fillStyle = 'white';
                halCtx.font = '24px Arial';
                halCtx.textAlign = 'center';
                halCtx.fillText(layer.icon, 250, layer.y + 35);
                
                halCtx.fillStyle = 'white';
                halCtx.font = 'bold 20px Arial';
                halCtx.fillText(layer.name, 500, layer.y + 50);
                
                // 连接箭头
                if (index < layers.length - 1) {
                    drawArrow(500, layer.y + 80, 500, layers[index + 1].y, '#666');
                }
            });
            
            // 标题
            halCtx.fillStyle = '#2c3e50';
            halCtx.font = 'bold 24px Arial';
            halCtx.fillText('硬件抽象层 - 系统架构图', 500, 30);
        }
        
        function drawHiding() {
            halCtx.fillStyle = '#2c3e50';
            halCtx.font = 'bold 24px Arial';
            halCtx.textAlign = 'center';
            halCtx.fillText('🎭 隐藏硬件多样性', 500, 40);
            
            // 多种硬件
            const hardwares = [
                {name: 'ARM芯片', x: 100, color: '#e74c3c'},
                {name: 'x86芯片', x: 250, color: '#3498db'},
                {name: 'MIPS芯片', x: 400, color: '#f39c12'},
                {name: 'RISC-V芯片', x: 550, color: '#9b59b6'},
                {name: '其他芯片', x: 700, color: '#27ae60'}
            ];
            
            hardwares.forEach((hw, index) => {
                // 硬件框
                const bounce = Math.sin(animationFrame * 0.1 + index) * 5;
                halCtx.fillStyle = hw.color;
                halCtx.fillRect(hw.x, 350 + bounce, 120, 60);
                
                halCtx.fillStyle = 'white';
                halCtx.font = '14px Arial';
                halCtx.fillText(hw.name, hw.x + 60, 385 + bounce);
                
                // 向上的箭头
                drawArrow(hw.x + 60, 350 + bounce, hw.x + 60, 280, hw.color);
            });
            
            // HAL层
            halCtx.fillStyle = '#f39c12';
            halCtx.fillRect(150, 250, 600, 60);
            halCtx.fillStyle = 'white';
            halCtx.font = 'bold 18px Arial';
            halCtx.fillText('🔧 硬件抽象层 - 统一接口', 450, 285);
            
            // 向上的统一箭头
            drawArrow(450, 250, 450, 180, '#f39c12');
            
            // 操作系统
            halCtx.fillStyle = '#3498db';
            halCtx.fillRect(300, 150, 300, 60);
            halCtx.fillStyle = 'white';
            halCtx.font = 'bold 18px Arial';
            halCtx.fillText('🖥️ 操作系统', 450, 185);
            
            // 说明文字
            halCtx.fillStyle = '#2c3e50';
            halCtx.font = '16px Arial';
            halCtx.fillText('多种不同的硬件', 450, 450);
            halCtx.fillText('↓', 450, 470);
            halCtx.fillText('HAL提供统一接口', 450, 320);
            halCtx.fillText('↓', 450, 340);
            halCtx.fillText('操作系统看到的是统一硬件', 450, 120);
        }
        
        function drawIsolation() {
            halCtx.fillStyle = '#2c3e50';
            halCtx.font = 'bold 24px Arial';
            halCtx.textAlign = 'center';
            halCtx.fillText('🚧 软硬件隔离', 500, 40);
            
            // 软件区域
            halCtx.fillStyle = 'rgba(52, 152, 219, 0.3)';
            halCtx.fillRect(50, 80, 400, 150);
            halCtx.strokeStyle = '#3498db';
            halCtx.lineWidth = 3;
            halCtx.strokeRect(50, 80, 400, 150);
            
            halCtx.fillStyle = '#3498db';
            halCtx.font = 'bold 20px Arial';
            halCtx.fillText('💻 软件区域', 250, 110);
            halCtx.font = '16px Arial';
            halCtx.fillText('应用程序 + 操作系统', 250, 140);
            halCtx.fillText('不需要了解硬件细节', 250, 165);
            halCtx.fillText('专注于功能实现', 250, 190);
            
            // 硬件区域
            halCtx.fillStyle = 'rgba(39, 174, 96, 0.3)';
            halCtx.fillRect(550, 80, 400, 150);
            halCtx.strokeStyle = '#27ae60';
            halCtx.lineWidth = 3;
            halCtx.strokeRect(550, 80, 400, 150);
            
            halCtx.fillStyle = '#27ae60';
            halCtx.font = 'bold 20px Arial';
            halCtx.fillText('🔧 硬件区域', 750, 110);
            halCtx.font = '16px Arial';
            halCtx.fillText('各种硬件平台', 750, 140);
            halCtx.fillText('不需要考虑软件', 750, 165);
            halCtx.fillText('专注于性能优化', 750, 190);
            
            // HAL隔离层
            const halY = 280;
            const thickness = 40;
            halCtx.fillStyle = '#f39c12';
            halCtx.fillRect(200, halY, 600, thickness);
            
            // 动态效果
            const wave = Math.sin(animationFrame * 0.1) * 10;
            halCtx.fillStyle = 'rgba(243, 156, 18, 0.8)';
            halCtx.fillRect(200, halY + wave, 600, thickness);
            
            halCtx.fillStyle = 'white';
            halCtx.font = 'bold 18px Arial';
            halCtx.fillText('🛡️ 硬件抽象层 - 隔离屏障', 500, halY + 25);
            
            // 连接线
            drawArrow(250, 230, 250, halY, '#3498db');
            drawArrow(750, 230, 750, halY, '#27ae60');
            
            // 硬件平台
            halCtx.fillStyle = '#34495e';
            halCtx.fillRect(300, 380, 400, 80);
            halCtx.fillStyle = 'white';
            halCtx.font = 'bold 18px Arial';
            halCtx.fillText('💾 具体硬件平台', 500, 425);
            
            drawArrow(500, halY + thickness, 500, 380, '#f39c12');
        }
        
        function drawParallel() {
            halCtx.fillStyle = '#2c3e50';
            halCtx.font = 'bold 24px Arial';
            halCtx.textAlign = 'center';
            halCtx.fillText('⚡ 软硬件并行开发', 500, 40);
            
            // 时间轴
            halCtx.strokeStyle = '#34495e';
            halCtx.lineWidth = 3;
            halCtx.beginPath();
            halCtx.moveTo(100, 100);
            halCtx.lineTo(900, 100);
            halCtx.stroke();
            
            // 时间刻度
            for (let i = 0; i <= 8; i++) {
                const x = 100 + i * 100;
                halCtx.beginPath();
                halCtx.moveTo(x, 95);
                halCtx.lineTo(x, 105);
                halCtx.stroke();
                
                halCtx.fillStyle = '#34495e';
                halCtx.font = '12px Arial';
                halCtx.fillText(`${i}月`, x - 10, 120);
            }
            
            // 软件开发进度
            const softwareProgress = Math.min((animationFrame * 0.5) % 800, 700);
            halCtx.fillStyle = '#3498db';
            halCtx.fillRect(100, 150, 700, 40);
            halCtx.fillStyle = '#2980b9';
            halCtx.fillRect(100, 150, softwareProgress, 40);
            
            halCtx.fillStyle = 'white';
            halCtx.font = 'bold 16px Arial';
            halCtx.fillText('💻 软件开发', 450, 175);
            
            // 硬件开发进度
            const hardwareProgress = Math.min((animationFrame * 0.5) % 800, 700);
            halCtx.fillStyle = '#27ae60';
            halCtx.fillRect(100, 220, 700, 40);
            halCtx.fillStyle = '#229954';
            halCtx.fillRect(100, 220, hardwareProgress, 40);
            
            halCtx.fillStyle = 'white';
            halCtx.font = 'bold 16px Arial';
            halCtx.fillText('🔧 硬件开发', 450, 245);
            
            // HAL开发
            halCtx.fillStyle = '#f39c12';
            halCtx.fillRect(100, 290, 700, 40);
            halCtx.fillStyle = 'white';
            halCtx.font = 'bold 16px Arial';
            halCtx.fillText('🛡️ HAL开发 - 连接桥梁', 450, 315);
            
            // 优势说明
            halCtx.fillStyle = '#2c3e50';
            halCtx.font = '18px Arial';
            halCtx.fillText('✅ 软件团队和硬件团队可以同时工作', 500, 370);
            halCtx.fillText('✅ 互不影响，提高开发效率', 500, 400);
            halCtx.fillText('✅ 缩短产品上市时间', 500, 430);
        }
        
        function drawArrow(fromX, fromY, toX, toY, color) {
            halCtx.strokeStyle = color;
            halCtx.lineWidth = 2;
            halCtx.beginPath();
            halCtx.moveTo(fromX, fromY);
            halCtx.lineTo(toX, toY);
            halCtx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            halCtx.beginPath();
            halCtx.moveTo(toX, toY);
            halCtx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            halCtx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            halCtx.closePath();
            halCtx.fillStyle = color;
            halCtx.fill();
        }
        
        // 绘制概念详解
        function drawConcepts() {
            conceptCtx.clearRect(0, 0, 1000, 400);
            
            if (!selectedConcept) {
                conceptCtx.fillStyle = '#34495e';
                conceptCtx.font = 'bold 24px Arial';
                conceptCtx.textAlign = 'center';
                conceptCtx.fillText('点击上方概念卡片查看详细动画演示', 500, 200);
                return;
            }
            
            switch(selectedConcept) {
                case 'hide':
                    drawHideDetail();
                    break;
                case 'isolate':
                    drawIsolateDetail();
                    break;
                case 'parallel':
                    drawParallelDetail();
                    break;
                case 'wrong':
                    drawWrongDetail();
                    break;
            }
        }
        
        function drawHideDetail() {
            conceptCtx.fillStyle = '#e74c3c';
            conceptCtx.font = 'bold 20px Arial';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('🎭 隐藏硬件多样性 - 详细解析', 500, 40);
            
            // 问题：多样化硬件
            conceptCtx.fillStyle = '#34495e';
            conceptCtx.font = '16px Arial';
            conceptCtx.fillText('问题：不同硬件有不同的接口和特性', 500, 80);
            
            // 多种硬件接口
            const interfaces = ['GPIO_A', 'GPIO_B', 'PORT_X', 'PIN_Y'];
            interfaces.forEach((intf, index) => {
                const x = 150 + index * 180;
                conceptCtx.fillStyle = `hsl(${index * 60}, 70%, 60%)`;
                conceptCtx.fillRect(x, 120, 120, 60);
                conceptCtx.fillStyle = 'white';
                conceptCtx.font = '14px Arial';
                conceptCtx.fillText(intf, x + 60, 155);
            });
            
            // HAL统一接口
            conceptCtx.fillStyle = '#f39c12';
            conceptCtx.fillRect(300, 220, 400, 60);
            conceptCtx.fillStyle = 'white';
            conceptCtx.font = 'bold 16px Arial';
            conceptCtx.fillText('HAL_GPIO_Write() - 统一接口', 500, 255);
            
            // 操作系统视角
            conceptCtx.fillStyle = '#3498db';
            conceptCtx.fillRect(400, 320, 200, 50);
            conceptCtx.fillStyle = 'white';
            conceptCtx.font = '14px Arial';
            conceptCtx.fillText('操作系统只看到', 500, 340);
            conceptCtx.fillText('统一的接口', 500, 355);
        }
        
        function drawIsolateDetail() {
            conceptCtx.fillStyle = '#3498db';
            conceptCtx.font = 'bold 20px Arial';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('🚧 软硬件隔离 - 详细解析', 500, 40);
            
            // 隔离前
            conceptCtx.fillStyle = '#e74c3c';
            conceptCtx.font = '16px Arial';
            conceptCtx.fillText('隔离前：软硬件紧密耦合', 250, 80);
            
            conceptCtx.strokeStyle = '#e74c3c';
            conceptCtx.lineWidth = 3;
            conceptCtx.beginPath();
            conceptCtx.moveTo(150, 100);
            conceptCtx.lineTo(350, 140);
            conceptCtx.stroke();
            
            // 隔离后
            conceptCtx.fillStyle = '#27ae60';
            conceptCtx.fillText('隔离后：通过HAL解耦', 750, 80);
            
            conceptCtx.fillStyle = '#f39c12';
            conceptCtx.fillRect(600, 120, 300, 30);
            conceptCtx.fillStyle = 'white';
            conceptCtx.font = '14px Arial';
            conceptCtx.fillText('HAL隔离层', 750, 140);
        }
        
        function drawParallelDetail() {
            conceptCtx.fillStyle = '#27ae60';
            conceptCtx.font = 'bold 20px Arial';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('⚡ 并行开发 - 详细解析', 500, 40);
            
            // 传统开发模式
            conceptCtx.fillStyle = '#e74c3c';
            conceptCtx.font = '16px Arial';
            conceptCtx.fillText('传统模式：串行开发', 200, 80);
            
            conceptCtx.fillStyle = '#e74c3c';
            conceptCtx.fillRect(50, 100, 150, 30);
            conceptCtx.fillRect(220, 100, 150, 30);
            
            conceptCtx.fillStyle = 'white';
            conceptCtx.font = '12px Arial';
            conceptCtx.fillText('硬件开发', 125, 120);
            conceptCtx.fillText('软件开发', 295, 120);
            
            // HAL模式
            conceptCtx.fillStyle = '#27ae60';
            conceptCtx.font = '16px Arial';
            conceptCtx.fillText('HAL模式：并行开发', 700, 80);
            
            conceptCtx.fillStyle = '#27ae60';
            conceptCtx.fillRect(550, 100, 150, 30);
            conceptCtx.fillRect(550, 150, 150, 30);
            
            conceptCtx.fillStyle = 'white';
            conceptCtx.font = '12px Arial';
            conceptCtx.fillText('硬件开发', 625, 120);
            conceptCtx.fillText('软件开发', 625, 170);
        }
        
        function drawWrongDetail() {
            conceptCtx.fillStyle = '#e74c3c';
            conceptCtx.font = 'bold 20px Arial';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('❌ HAL不包含的内容', 500, 40);

            // 系统架构图
            const layers = [
                {name: '应用程序', y: 80, color: '#9b59b6'},
                {name: '操作系统内核', y: 130, color: '#3498db'},
                {name: '设备驱动', y: 160, color: '#e74c3c', wrong: true},
                {name: '任务调度', y: 190, color: '#e74c3c', wrong: true},
                {name: 'HAL', y: 240, color: '#f39c12'},
                {name: '硬件', y: 290, color: '#27ae60'}
            ];

            layers.forEach((layer, index) => {
                const x = layer.wrong ? 700 : 200;
                const width = layer.wrong ? 150 : 300;

                // 错误位置闪烁效果
                if (layer.wrong) {
                    const flash = Math.sin(animationFrame * 0.2) * 0.3 + 0.7;
                    conceptCtx.globalAlpha = flash;
                }

                conceptCtx.fillStyle = layer.color;
                conceptCtx.fillRect(x, layer.y, width, 25);

                conceptCtx.fillStyle = 'white';
                conceptCtx.font = '14px Arial';
                conceptCtx.fillText(layer.name, x + width/2, layer.y + 17);

                if (layer.wrong) {
                    conceptCtx.globalAlpha = 1;
                    // 画X标记
                    conceptCtx.strokeStyle = 'white';
                    conceptCtx.lineWidth = 3;
                    conceptCtx.beginPath();
                    conceptCtx.moveTo(x + width - 20, layer.y + 5);
                    conceptCtx.lineTo(x + width - 5, layer.y + 20);
                    conceptCtx.moveTo(x + width - 5, layer.y + 5);
                    conceptCtx.lineTo(x + width - 20, layer.y + 20);
                    conceptCtx.stroke();
                }
            });

            // 箭头指向正确位置
            drawArrow(650, 170, 520, 140, '#e74c3c');
            drawArrow(650, 200, 520, 140, '#e74c3c');

            // 说明文字
            conceptCtx.fillStyle = '#34495e';
            conceptCtx.font = '12px Arial';
            conceptCtx.fillText('设备驱动和任务调度', 750, 350);
            conceptCtx.fillText('应该在操作系统内核中', 750, 370);
        }
        
        // 动画循环
        function animate() {
            animationFrame++;
            drawHALArchitecture();
            drawConcepts();
            requestAnimationFrame(animate);
        }
        
        // 演示切换
        function showDemo(demo) {
            currentDemo = demo;
            document.querySelectorAll('.control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }
        
        // 概念选择
        function showConcept(concept) {
            selectedConcept = concept;
            document.querySelectorAll('.concept-card').forEach(card => {
                card.style.transform = 'translateY(0)';
            });
            event.target.closest('.concept-card').style.transform = 'translateY(-15px) scale(1.02)';
        }
        
        // 题目相关函数
        function selectOption(element, option) {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            element.classList.add('selected');
            selectedOption = option;
        }
        
        function checkAnswer() {
            if (!selectedOption) {
                alert('请先选择一个答案！');
                return;
            }
            
            const result = document.getElementById('result');
            const options = document.querySelectorAll('.option');
            
            options.forEach((opt, index) => {
                const optionLetter = ['A', 'B', 'C', 'D'][index];
                if (optionLetter === 'D') {
                    opt.classList.add('correct');
                } else if (opt.classList.contains('selected') && optionLetter !== 'D') {
                    opt.classList.add('wrong');
                }
            });
            
            if (selectedOption === 'D') {
                result.innerHTML = '🎉 恭喜你答对了！选项D确实是错误的，HAL不包括设备驱动程序和任务调度。';
                result.style.background = 'rgba(76, 175, 80, 0.8)';
            } else {
                result.innerHTML = '❌ 答案不正确。正确答案是D。让我们来看看详细解析...';
                result.style.background = 'rgba(244, 67, 54, 0.8)';
            }
            
            result.classList.add('show');
        }
        
        function showExplanation() {
            const result = document.getElementById('result');
            result.innerHTML = `
                <h3>📚 详细解析：</h3>
                <p><strong>正确答案：D（错误的选项）</strong></p>
                
                <div class="highlight">
                    <h4>🎯 为什么D选项是错误的？</h4>
                    <p><strong>硬件抽象层(HAL)不包括：</strong></p>
                    <ul style="text-align: left; margin: 15px 0;">
                        <li>❌ <strong>设备驱动程序</strong>：属于操作系统内核层，不是HAL的组成部分</li>
                        <li>❌ <strong>任务调度</strong>：属于操作系统的核心功能，与硬件抽象无关</li>
                    </ul>
                </div>
                
                <div class="highlight">
                    <h4>✅ 其他选项为什么正确？</h4>
                    <ul style="text-align: left; margin: 15px 0;">
                        <li><strong>A选项</strong>：HAL确实与硬件密切相关，并隐藏硬件多样性</li>
                        <li><strong>B选项</strong>：HAL在操作系统和硬件之间建立隔离层</li>
                        <li><strong>C选项</strong>：HAL使软硬件开发可以并行进行</li>
                    </ul>
                </div>
                
                <p><strong>🔑 记忆要点：</strong>HAL只负责硬件抽象，不涉及具体的系统功能实现！</p>
            `;
            result.style.background = 'rgba(52, 152, 219, 0.8)';
            result.classList.add('show');
        }
        
        // 启动动画
        animate();
    </script>
</body>
</html>
