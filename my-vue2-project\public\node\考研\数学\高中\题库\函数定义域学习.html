<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函数定义域 - 零基础互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateY(-5px);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            transform: scale(1.2);
        }

        .problem-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.3rem;
            color: #333;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">函数的定义域</h1>
            <p class="subtitle">让我们用动画和互动来理解这个重要概念！</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是函数定义域？</h2>
            <div class="explanation">
                <strong>定义域</strong>就是函数中<span class="highlight">x可以取哪些值</span>的范围。
                <br>想象一下：函数就像一台神奇的机器，你输入x，它输出y。
                <br>但这台机器很挑剔，只接受特定的x值！
            </div>
            
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="600" height="300"></canvas>
            </div>
            
            <button class="interactive-btn" onclick="startConceptAnimation()">🎬 播放概念动画</button>
        </div>

        <div class="section">
            <h2 class="section-title">典型题目演示</h2>
            <div class="problem-box">
                <strong>题目：求函数 f(x) = √(x-2) 的定义域</strong>
            </div>
            
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>
            
            <div class="canvas-container">
                <canvas id="problemCanvas" width="700" height="400"></canvas>
            </div>
            
            <button class="interactive-btn" onclick="nextStep()">▶️ 下一步</button>
            <button class="interactive-btn" onclick="resetProblem()">🔄 重新开始</button>
        </div>

        <div class="section">
            <h2 class="section-title">解题思路总结</h2>
            <div class="explanation">
                <strong>解题步骤：</strong><br>
                1️⃣ <strong>观察函数形式</strong> - 看看有什么特殊结构<br>
                2️⃣ <strong>找限制条件</strong> - 什么情况下函数无意义<br>
                3️⃣ <strong>列不等式</strong> - 根据限制条件写出不等式<br>
                4️⃣ <strong>求解范围</strong> - 解不等式得到定义域
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let animationId;

        // 概念动画
        function startConceptAnimation() {
            const canvas = document.getElementById('conceptCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制函数机器
                ctx.fillStyle = '#667eea';
                ctx.fillRect(200, 100, 200, 100);
                ctx.fillStyle = 'white';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('函数机器', 300, 150);
                
                // 输入箭头动画
                const inputX = 50 + Math.sin(frame * 0.1) * 20;
                ctx.fillStyle = '#ff6b6b';
                ctx.beginPath();
                ctx.arc(inputX, 150, 15, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText('x', inputX, 155);
                
                // 输出箭头动画
                const outputX = 450 + Math.sin(frame * 0.1) * 20;
                ctx.fillStyle = '#4ecdc4';
                ctx.beginPath();
                ctx.arc(outputX, 150, 15, 0, 2 * Math.PI);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.fillText('y', outputX, 155);
                
                // 定义域标注
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.fillText('定义域：x可以取的值', 300, 50);
                
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 问题解决步骤
        function nextStep() {
            if (currentStep < 4) {
                document.getElementById(`step${currentStep}`).classList.remove('active');
                currentStep++;
                document.getElementById(`step${currentStep}`).classList.add('active');
            }
            drawProblemStep();
        }

        function resetProblem() {
            document.getElementById(`step${currentStep}`).classList.remove('active');
            currentStep = 1;
            document.getElementById('step1').classList.add('active');
            drawProblemStep();
        }

        function drawProblemStep() {
            const canvas = document.getElementById('problemCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制坐标轴
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(50, 200);
            ctx.lineTo(650, 200);
            ctx.moveTo(350, 50);
            ctx.lineTo(350, 350);
            ctx.stroke();
            
            // 标注坐标轴
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('x', 660, 205);
            ctx.fillText('y', 355, 45);
            ctx.fillText('0', 340, 220);
            
            switch(currentStep) {
                case 1:
                    drawStep1(ctx);
                    break;
                case 2:
                    drawStep2(ctx);
                    break;
                case 3:
                    drawStep3(ctx);
                    break;
                case 4:
                    drawStep4(ctx);
                    break;
            }
        }

        function drawStep1(ctx) {
            ctx.fillStyle = '#ff6b6b';
            ctx.font = '18px Arial';
            ctx.fillText('步骤1: 观察函数 f(x) = √(x-2)', 50, 30);
            ctx.fillText('根号函数的特点：被开方数必须 ≥ 0', 50, 320);
            
            // 高亮根号
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(200, 150, 100, 50);
            ctx.fillStyle = 'white';
            ctx.font = '24px Arial';
            ctx.fillText('√', 220, 180);
            ctx.fillText('x-2', 240, 180);
        }

        function drawStep2(ctx) {
            ctx.fillStyle = '#4ecdc4';
            ctx.font = '18px Arial';
            ctx.fillText('步骤2: 找限制条件', 50, 30);
            ctx.fillText('根号下的数不能为负数！', 50, 320);
            ctx.fillText('所以：x - 2 ≥ 0', 50, 340);
            
            // 绘制不等式
            ctx.fillStyle = '#4ecdc4';
            ctx.fillRect(200, 150, 150, 50);
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.fillText('x - 2 ≥ 0', 220, 180);
        }

        function drawStep3(ctx) {
            ctx.fillStyle = '#ffd93d';
            ctx.font = '18px Arial';
            ctx.fillText('步骤3: 解不等式', 50, 30);
            ctx.fillText('x - 2 ≥ 0', 50, 320);
            ctx.fillText('x ≥ 2', 50, 340);
            
            // 动画显示解题过程
            ctx.fillStyle = '#ffd93d';
            ctx.fillRect(200, 120, 200, 80);
            ctx.fillStyle = 'black';
            ctx.font = '18px Arial';
            ctx.fillText('x - 2 ≥ 0', 220, 145);
            ctx.fillText('x ≥ 2', 220, 175);
        }

        function drawStep4(ctx) {
            ctx.fillStyle = '#6c5ce7';
            ctx.font = '18px Arial';
            ctx.fillText('步骤4: 写出定义域', 50, 30);
            ctx.fillText('定义域：[2, +∞)', 50, 320);
            
            // 绘制数轴表示
            ctx.strokeStyle = '#6c5ce7';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(400, 200);
            ctx.lineTo(600, 200);
            ctx.stroke();
            
            // 标记点2
            ctx.fillStyle = '#6c5ce7';
            ctx.beginPath();
            ctx.arc(400, 200, 8, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'black';
            ctx.font = '14px Arial';
            ctx.fillText('2', 395, 230);
            
            // 箭头表示延伸到无穷
            ctx.strokeStyle = '#6c5ce7';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(590, 195);
            ctx.lineTo(600, 200);
            ctx.lineTo(590, 205);
            ctx.stroke();
        }

        // 初始化
        window.onload = function() {
            startConceptAnimation();
            drawProblemStep();
        };
    </script>
</body>
</html>
