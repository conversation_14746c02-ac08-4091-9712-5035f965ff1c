<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pre- 词缀故事：时间预言师的预知之旅</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 背景粒子效果 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.6);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            position: relative;
            z-index: 10;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.8rem;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInDown 1s ease-out;
            position: relative;
        }

        .title::after {
            content: '🔮';
            position: absolute;
            right: -60px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
            animation: pulse 2s infinite;
        }

        .subtitle {
            text-align: center;
            color: rgba(255,255,255,0.9);
            font-size: 1.3rem;
            margin-bottom: 50px;
            animation: fadeInUp 1s ease-out 0.3s both;
            font-weight: 300;
        }

        .story-canvas {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            margin-bottom: 40px;
            overflow: hidden;
            animation: slideInUp 1s ease-out 0.6s both;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        canvas {
            display: block;
            width: 100%;
            height: 450px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 25px;
            margin-bottom: 50px;
            animation: fadeIn 1s ease-out 1s both;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            font-weight: 500;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(-2px) scale(1.02);
        }

        .explanation {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 45px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin-bottom: 35px;
            animation: slideInUp 1s ease-out 0.9s both;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .explanation h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.8rem;
            position: relative;
            padding-left: 20px;
        }

        .explanation h2::before {
            content: '💡';
            position: absolute;
            left: -10px;
            top: 0;
        }

        .explanation p {
            color: #666;
            line-height: 1.9;
            font-size: 1.15rem;
            margin-bottom: 20px;
        }

        .word-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 20px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform: translateX(-30px);
            opacity: 0;
            position: relative;
            overflow: hidden;
        }

        .word-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .word-card:hover::before {
            transform: translateX(100%);
        }

        .word-card.show {
            transform: translateX(0);
            opacity: 1;
        }

        .word-card:hover {
            transform: scale(1.03) translateY(-5px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }

        .prefix {
            font-size: 1.6rem;
            font-weight: bold;
            color: #ffd700;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .meaning {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.95);
            margin-top: 12px;
            font-weight: 300;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-40px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(40px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(60px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-50%) scale(1.2); }
        }

        .interactive-area {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            margin-top: 35px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .quiz-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            margin: 8px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-size: 1rem;
            font-weight: 500;
        }

        .quiz-btn:hover {
            transform: scale(1.08) translateY(-2px);
            box-shadow: 0 8px 20px rgba(78, 205, 196, 0.4);
        }

        .correct {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf) !important;
            animation: correctPulse 0.6s ease;
        }

        .wrong {
            background: linear-gradient(45deg, #ff416c, #ff4b2b) !important;
            animation: wrongShake 0.6s ease;
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .scene-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            color: #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- 背景粒子 -->
    <div class="particles" id="particles"></div>

    <div class="container">
        <h1 class="title">Pre- 词缀魔法课堂</h1>
        <p class="subtitle">跟随时间预言师探索"预先、提前"的神秘力量</p>
        
        <div class="story-canvas">
            <div class="scene-indicator" id="sceneIndicator">场景 1/4</div>
            <canvas id="storyCanvas" width="1000" height="450"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="startStory()">🎬 开始预言</button>
            <button class="btn" onclick="nextScene()">⏭️ 下个预言</button>
            <button class="btn" onclick="prevScene()">⏮️ 上个预言</button>
            <button class="btn" onclick="resetStory()">🔄 重新开始</button>
        </div>

        <div class="explanation">
            <h2>为什么选择"时间预言师"的故事？</h2>
            <p>
                我选择用"时间预言师"来讲解 <span class="prefix">pre-</span> 词缀，是因为这个词缀的核心含义就是"预先、提前、在...之前"。
                预言师能够预见未来，这完美体现了"pre-"的时间概念。通过预言师在不同时间点的预测故事，
                你可以直观地理解"提前发生"、"预先准备"的含义，让抽象的时间概念变得生动具体。
            </p>
            <p>
                每个场景都展示了不同的"pre-"词汇使用情境，从预测(predict)到预防(prevent)，
                从准备(prepare)到精确(precise)，帮助你建立完整的词汇网络。
            </p>
            
            <div id="wordCards">
                <!-- 词汇卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="interactive-area">
            <h3 style="color: #333; margin-bottom: 25px; font-size: 1.5rem;">🎯 互动测试：选择正确的翻译</h3>
            <div id="quizArea">
                <!-- 测试题目将通过JavaScript生成 -->
            </div>
            
            <div style="margin-top: 40px; padding-top: 30px; border-top: 2px solid #eee;">
                <h3 style="color: #333; margin-bottom: 20px;">🔊 语音练习</h3>
                <p style="color: #666; margin-bottom: 20px;">点击下面的按钮听发音，然后跟读：</p>
                <div id="pronunciationArea" style="display: flex; flex-wrap: wrap; gap: 12px; justify-content: center;">
                    <!-- 发音按钮将通过JavaScript生成 -->
                </div>
            </div>
            
            <div style="margin-top: 40px; padding-top: 30px; border-top: 2px solid #eee;">
                <h3 style="color: #333; margin-bottom: 20px;">💡 记忆小贴士</h3>
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                           color: white; padding: 25px; border-radius: 20px; line-height: 1.9;">
                    <p><strong>🎯 记忆技巧：</strong></p>
                    <p>🔮 <strong>pre-</strong> = "预先、提前、在...之前"</p>
                    <p>⏰ 想象一个预言师总是能"提前"看到未来</p>
                    <p>📝 每次看到 pre- 开头的单词，就想到"时间上的提前"</p>
                    <p>🎯 练习方法：遇到新单词时，问自己"这个动作是提前发生的吗？"</p>
                    <p>🌟 常见搭配：pre- + 动词 = 提前做某事</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建背景粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        let currentScene = 0;
        let animationFrame = 0;
        let isAnimating = false;

        // 故事场景数据
        const scenes = [
            {
                title: "预言师的水晶球",
                description: "预言师正在预测未来 (predict)",
                words: ["predict", "preview", "preface"],
                color: "#667eea"
            },
            {
                title: "提前的准备",
                description: "预言师提前准备魔法仪式 (prepare)",
                words: ["prepare", "prevent", "preserve"],
                color: "#4ecdc4"
            },
            {
                title: "精确的预言",
                description: "预言师给出精确的预测 (precise)",
                words: ["precise", "premium", "prelude"],
                color: "#ff6b6b"
            },
            {
                title: "预先的警告",
                description: "预言师预先警告即将到来的危险 (premonition)",
                words: ["premonition", "prerequisite", "preliminary"],
                color: "#ffd700"
            }
        ];

        // 词汇数据
        const vocabulary = [
            {
                word: "predict",
                prefix: "pre-",
                root: "dict (说)",
                meaning: "预测、预言",
                explanation: "pre(提前) + dict(说) = 提前说出将要发生的事",
                sentence: "The prophet can predict the future. (预言师能够预测未来。)"
            },
            {
                word: "prepare",
                prefix: "pre-",
                root: "pare (准备)",
                meaning: "准备、预备",
                explanation: "pre(提前) + pare(准备) = 提前做好准备",
                sentence: "We must prepare for the ceremony. (我们必须为仪式做准备。)"
            },
            {
                word: "prevent",
                prefix: "pre-",
                root: "vent (来)",
                meaning: "预防、阻止",
                explanation: "pre(提前) + vent(来) = 提前阻止某事发生",
                sentence: "Magic can prevent disasters. (魔法可以预防灾难。)"
            },
            {
                word: "precise",
                prefix: "pre-",
                root: "cise (切)",
                meaning: "精确的、准确的",
                explanation: "pre(预先) + cise(切) = 预先切得很准确",
                sentence: "The prediction is very precise. (这个预测非常精确。)"
            }
        ];

        function startStory() {
            currentScene = 0;
            animationFrame = 0;
            isAnimating = true;
            updateSceneIndicator();
            drawScene();
            showWordCards();
        }

        function nextScene() {
            if (currentScene < scenes.length - 1) {
                currentScene++;
                animationFrame = 0;
                updateSceneIndicator();
                drawScene();
                updateWordCards();
            }
        }

        function prevScene() {
            if (currentScene > 0) {
                currentScene--;
                animationFrame = 0;
                updateSceneIndicator();
                drawScene();
                updateWordCards();
            }
        }

        function resetStory() {
            currentScene = 0;
            animationFrame = 0;
            isAnimating = false;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('wordCards').innerHTML = '';
            updateSceneIndicator();
        }

        function updateSceneIndicator() {
            document.getElementById('sceneIndicator').textContent = `场景 ${currentScene + 1}/${scenes.length}`;
        }

        function drawScene() {
            if (!isAnimating) return;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, scenes[currentScene].color);
            gradient.addColorStop(1, '#2c3e50');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制星空背景
            drawStars();

            // 绘制预言师
            drawProphet();

            // 绘制场景特效
            drawSceneEffects();

            // 绘制文字说明
            drawSceneText();

            animationFrame++;
            if (isAnimating) {
                requestAnimationFrame(drawScene);
            }
        }

        function drawStars() {
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            for (let i = 0; i < 100; i++) {
                const x = (i * 137.5) % canvas.width;
                const y = (i * 73.3) % canvas.height;
                const size = Math.sin(animationFrame * 0.02 + i) * 1 + 1;

                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function drawProphet() {
            const x = 150 + Math.sin(animationFrame * 0.03) * 5;
            const y = 250;

            // 预言师长袍
            ctx.fillStyle = '#2c3e50';
            ctx.beginPath();
            ctx.ellipse(x, y + 40, 40, 60, 0, 0, Math.PI * 2);
            ctx.fill();

            // 预言师头部
            ctx.fillStyle = '#fdbcb4';
            ctx.beginPath();
            ctx.arc(x, y - 20, 25, 0, Math.PI * 2);
            ctx.fill();

            // 预言师帽子
            ctx.fillStyle = '#1a252f';
            ctx.beginPath();
            ctx.moveTo(x - 30, y - 20);
            ctx.lineTo(x, y - 70);
            ctx.lineTo(x + 30, y - 20);
            ctx.fill();

            // 预言师胡须
            ctx.fillStyle = '#ecf0f1';
            ctx.beginPath();
            ctx.ellipse(x, y - 5, 15, 8, 0, 0, Math.PI * 2);
            ctx.fill();

            // 预言师手臂和法杖
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(x + 25, y + 10);
            ctx.lineTo(x + 50, y - 10);
            ctx.stroke();

            // 法杖
            ctx.strokeStyle = '#8b4513';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(x + 50, y - 10);
            ctx.lineTo(x + 50, y - 50);
            ctx.stroke();

            // 法杖顶部宝石
            const gemGlow = Math.sin(animationFrame * 0.1) * 0.3 + 0.7;
            ctx.fillStyle = `rgba(255, 215, 0, ${gemGlow})`;
            ctx.beginPath();
            ctx.arc(x + 50, y - 50, 8, 0, Math.PI * 2);
            ctx.fill();

            // 宝石光环
            ctx.strokeStyle = `rgba(255, 215, 0, ${gemGlow * 0.5})`;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(x + 50, y - 50, 12 + Math.sin(animationFrame * 0.1) * 3, 0, Math.PI * 2);
            ctx.stroke();
        }

        function drawSceneEffects() {
            switch(currentScene) {
                case 0:
                    drawCrystalBall();
                    break;
                case 1:
                    drawPreparationItems();
                    break;
                case 2:
                    drawPrecisionSymbols();
                    break;
                case 3:
                    drawWarningSignals();
                    break;
            }
        }

        function drawCrystalBall() {
            const x = 600;
            const y = 300;
            const radius = 60;

            // 水晶球底座
            ctx.fillStyle = '#34495e';
            ctx.beginPath();
            ctx.ellipse(x, y + 50, 40, 15, 0, 0, Math.PI * 2);
            ctx.fill();

            // 水晶球主体
            const ballGradient = ctx.createRadialGradient(x - 20, y - 20, 0, x, y, radius);
            ballGradient.addColorStop(0, 'rgba(255,255,255,0.8)');
            ballGradient.addColorStop(0.7, 'rgba(173,216,230,0.6)');
            ballGradient.addColorStop(1, 'rgba(70,130,180,0.8)');

            ctx.fillStyle = ballGradient;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();

            // 水晶球内的预言图像
            ctx.fillStyle = `rgba(255, 215, 0, ${Math.sin(animationFrame * 0.05) * 0.3 + 0.4})`;
            for (let i = 0; i < 5; i++) {
                const angle = (animationFrame * 0.02 + i * Math.PI * 2 / 5);
                const px = x + Math.cos(angle) * 20;
                const py = y + Math.sin(angle) * 20;

                ctx.beginPath();
                ctx.arc(px, py, 3, 0, Math.PI * 2);
                ctx.fill();
            }

            // 水晶球反光
            ctx.fillStyle = 'rgba(255,255,255,0.6)';
            ctx.beginPath();
            ctx.ellipse(x - 15, y - 15, 12, 20, -0.3, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawPreparationItems() {
            // 绘制魔法书
            const bookX = 500;
            const bookY = 350;

            ctx.fillStyle = '#8b4513';
            ctx.fillRect(bookX, bookY, 80, 60);

            ctx.fillStyle = '#ffd700';
            ctx.fillRect(bookX + 5, bookY + 5, 70, 50);

            // 书页翻动效果
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            for (let i = 0; i < 3; i++) {
                const offset = Math.sin(animationFrame * 0.1 + i) * 2;
                ctx.fillRect(bookX + 10 + offset, bookY + 10 + i * 15, 60, 2);
            }

            // 绘制魔法药瓶
            const potionX = 650;
            const potionY = 320;

            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(potionX, potionY + 30, 30, 40);

            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(potionX + 2, potionY + 32, 26, 30);

            // 药瓶冒泡效果
            for (let i = 0; i < 3; i++) {
                const bubbleY = potionY + 35 - (animationFrame * 0.5 + i * 20) % 30;
                ctx.fillStyle = `rgba(231, 76, 60, ${1 - (animationFrame * 0.5 + i * 20) % 30 / 30})`;
                ctx.beginPath();
                ctx.arc(potionX + 15, bubbleY, 2, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function drawPrecisionSymbols() {
            // 绘制精确的几何图形
            const centerX = 600;
            const centerY = 300;

            // 精确的圆形
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(centerX, centerY, 50, 0, Math.PI * 2);
            ctx.stroke();

            // 精确的三角形
            ctx.strokeStyle = '#3498db';
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - 30);
            ctx.lineTo(centerX - 26, centerY + 15);
            ctx.lineTo(centerX + 26, centerY + 15);
            ctx.closePath();
            ctx.stroke();

            // 精确的正方形
            ctx.strokeStyle = '#2ecc71';
            ctx.strokeRect(centerX - 20, centerY - 20, 40, 40);

            // 旋转的精确指针
            ctx.save();
            ctx.translate(centerX, centerY);
            ctx.rotate(animationFrame * 0.02);
            ctx.strokeStyle = '#f39c12';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(35, 0);
            ctx.stroke();
            ctx.restore();
        }

        function drawWarningSignals() {
            // 绘制警告闪电
            for (let i = 0; i < 3; i++) {
                const x = 500 + i * 100;
                const y = 200 + Math.sin(animationFrame * 0.1 + i) * 20;
                const alpha = Math.sin(animationFrame * 0.15 + i) * 0.5 + 0.5;

                ctx.strokeStyle = `rgba(241, 196, 15, ${alpha})`;
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(x, y);
                ctx.lineTo(x - 10, y + 30);
                ctx.lineTo(x + 5, y + 30);
                ctx.lineTo(x - 5, y + 60);
                ctx.lineTo(x + 10, y + 30);
                ctx.lineTo(x - 5, y + 30);
                ctx.closePath();
                ctx.stroke();
                ctx.fillStyle = `rgba(241, 196, 15, ${alpha * 0.3})`;
                ctx.fill();
            }

            // 绘制警告符号
            const symbolX = 700;
            const symbolY = 350;
            const pulse = Math.sin(animationFrame * 0.2) * 0.3 + 0.7;

            ctx.fillStyle = `rgba(231, 76, 60, ${pulse})`;
            ctx.beginPath();
            ctx.moveTo(symbolX, symbolY - 20);
            ctx.lineTo(symbolX - 17, symbolY + 10);
            ctx.lineTo(symbolX + 17, symbolY + 10);
            ctx.closePath();
            ctx.fill();

            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('!', symbolX, symbolY + 5);
        }

        function drawSceneText() {
            // 场景标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.shadowColor = 'rgba(0,0,0,0.5)';
            ctx.shadowBlur = 4;
            ctx.fillText(scenes[currentScene].title, canvas.width / 2, 60);

            // 场景描述
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText(scenes[currentScene].description, canvas.width / 2, 90);
            ctx.shadowBlur = 0;
        }

        function showWordCards() {
            const container = document.getElementById('wordCards');
            container.innerHTML = '';

            vocabulary.forEach((item, index) => {
                setTimeout(() => {
                    const card = document.createElement('div');
                    card.className = 'word-card';
                    card.innerHTML = `
                        <div style="font-size: 1.6rem; font-weight: bold; margin-bottom: 12px;">
                            <span class="prefix">${item.prefix}</span>${item.word.replace(item.prefix.replace('-', ''), '')}
                        </div>
                        <div class="meaning">${item.meaning}</div>
                        <div style="font-size: 1rem; margin-top: 10px; opacity: 0.9;">${item.explanation}</div>
                        <div style="font-size: 0.95rem; margin-top: 8px; font-style: italic; opacity: 0.8;">
                            "${item.sentence}"
                        </div>
                    `;

                    card.addEventListener('click', () => {
                        speakWord(item.word);
                        showWordBreakdown(item);
                    });

                    container.appendChild(card);

                    setTimeout(() => {
                        card.classList.add('show');
                    }, 100);
                }, index * 400);
            });
        }

        function updateWordCards() {
            const cards = document.querySelectorAll('.word-card');
            cards.forEach(card => {
                card.style.opacity = '0.7';
                card.style.transform = 'scale(0.98)';
            });

            // 高亮当前场景的词汇
            const currentWords = scenes[currentScene].words;
            cards.forEach((card, index) => {
                if (currentWords.includes(vocabulary[index]?.word)) {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1.02)';
                    card.style.boxShadow = '0 25px 50px rgba(102, 126, 234, 0.4)';
                }
            });
        }

        function showWordBreakdown(wordData) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
                backdrop-filter: blur(5px);
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 50px;
                border-radius: 25px;
                max-width: 600px;
                text-align: center;
                animation: slideInUp 0.5s ease;
                box-shadow: 0 25px 50px rgba(0,0,0,0.3);
            `;

            content.innerHTML = `
                <h2 style="color: #333; margin-bottom: 25px; font-size: 1.8rem;">🔍 词汇深度解析</h2>
                <div style="font-size: 2.5rem; margin: 25px 0;">
                    <span style="color: #667eea; font-weight: bold;">${wordData.prefix}</span>
                    <span style="color: #4ecdc4; font-weight: bold;">${wordData.root}</span>
                </div>
                <div style="font-size: 1.8rem; color: #333; margin: 20px 0; font-weight: bold;">${wordData.word}</div>
                <div style="font-size: 1.3rem; color: #666; margin: 20px 0;">${wordData.meaning}</div>
                <div style="color: #888; line-height: 1.8; margin: 20px 0; font-size: 1.1rem;">${wordData.explanation}</div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 20px 0; font-style: italic; color: #555;">
                    ${wordData.sentence}
                </div>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 25px; padding: 12px 25px; background: linear-gradient(45deg, #667eea, #764ba2);
                               color: white; border: none; border-radius: 25px; cursor: pointer; font-size: 1rem;">
                    关闭详情
                </button>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);

            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            };
        }

        function speakWord(word) {
            if ('speechSynthesis' in window) {
                speechSynthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(word);
                utterance.lang = 'en-US';
                utterance.rate = 0.8;
                utterance.pitch = 1.1;

                const feedback = document.createElement('div');
                feedback.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(45deg, #667eea, #764ba2);
                    color: white;
                    padding: 25px 45px;
                    border-radius: 50px;
                    font-size: 1.6rem;
                    z-index: 1000;
                    animation: fadeIn 0.3s ease;
                    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
                `;
                feedback.innerHTML = `🔊 <strong>${word}</strong>`;
                document.body.appendChild(feedback);

                utterance.onend = () => {
                    setTimeout(() => {
                        if (feedback.parentNode) {
                            feedback.remove();
                        }
                    }, 800);
                };

                speechSynthesis.speak(utterance);
            } else {
                alert('您的浏览器不支持语音功能');
            }
        }

        function initQuiz() {
            const quizData = [
                {
                    question: "predict 的意思是？",
                    options: ["预测", "准备", "预防", "精确"],
                    correct: 0,
                    explanation: "pre(提前) + dict(说) = 提前说出将要发生的事"
                },
                {
                    question: "prepare 的意思是？",
                    options: ["预测", "准备", "预防", "精确"],
                    correct: 1,
                    explanation: "pre(提前) + pare(准备) = 提前做好准备"
                },
                {
                    question: "prevent 的意思是？",
                    options: ["预测", "准备", "预防", "精确"],
                    correct: 2,
                    explanation: "pre(提前) + vent(来) = 提前阻止某事发生"
                },
                {
                    question: "precise 的意思是？",
                    options: ["预测", "准备", "预防", "精确"],
                    correct: 3,
                    explanation: "pre(预先) + cise(切) = 预先切得很准确"
                }
            ];

            const quizArea = document.getElementById('quizArea');

            quizData.forEach((quiz, qIndex) => {
                const quizDiv = document.createElement('div');
                quizDiv.style.marginBottom = '25px';
                quizDiv.innerHTML = `<h4 style="margin-bottom: 15px; color: #333; font-size: 1.2rem;">${quiz.question}</h4>`;

                quiz.options.forEach((option, oIndex) => {
                    const btn = document.createElement('button');
                    btn.className = 'quiz-btn';
                    btn.textContent = option;
                    btn.onclick = () => checkAnswer(btn, oIndex === quiz.correct, quiz.explanation, qIndex);
                    quizDiv.appendChild(btn);
                });

                quizArea.appendChild(quizDiv);
            });
        }

        function checkAnswer(btn, isCorrect, explanation, questionIndex) {
            const buttons = btn.parentNode.querySelectorAll('.quiz-btn');
            buttons.forEach(b => b.disabled = true);

            if (isCorrect) {
                btn.classList.add('correct');
                btn.innerHTML += ' ✓';

                // 显示解释
                setTimeout(() => {
                    const explanationDiv = document.createElement('div');
                    explanationDiv.style.cssText = `
                        margin-top: 10px;
                        padding: 15px;
                        background: linear-gradient(135deg, #56ab2f, #a8e6cf);
                        color: white;
                        border-radius: 10px;
                        font-size: 0.95rem;
                        animation: slideInUp 0.5s ease;
                    `;
                    explanationDiv.innerHTML = `💡 ${explanation}`;
                    btn.parentNode.appendChild(explanationDiv);
                }, 500);
            } else {
                btn.classList.add('wrong');
                btn.innerHTML += ' ✗';
            }
        }

        function initPronunciation() {
            const pronunciationArea = document.getElementById('pronunciationArea');

            vocabulary.forEach((item, index) => {
                const btn = document.createElement('button');
                btn.className = 'quiz-btn';
                btn.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                btn.innerHTML = `🔊 ${item.word}`;

                btn.onclick = () => {
                    speakWord(item.word);
                    btn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        btn.style.transform = 'scale(1)';
                    }, 150);
                };

                pronunciationArea.appendChild(btn);
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextScene();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    prevScene();
                    break;
                case 'r':
                case 'R':
                    e.preventDefault();
                    resetStory();
                    break;
                case 's':
                case 'S':
                    e.preventDefault();
                    startStory();
                    break;
            }
        });

        // 页面加载时创建粒子效果
        window.addEventListener('load', () => {
            createParticles();
            initQuiz();
            initPronunciation();
            setTimeout(startStory, 1000);

            // 显示快捷键提示
            setTimeout(() => {
                const hints = document.createElement('div');
                hints.style.cssText = `
                    position: fixed;
                    bottom: 25px;
                    right: 25px;
                    background: rgba(0,0,0,0.8);
                    color: white;
                    padding: 20px;
                    border-radius: 15px;
                    font-size: 0.9rem;
                    z-index: 1000;
                    animation: slideInUp 0.5s ease;
                    backdrop-filter: blur(10px);
                `;
                hints.innerHTML = `
                    <div style="margin-bottom: 8px;"><strong>⌨️ 快捷键：</strong></div>
                    <div>→ 或 空格：下一个预言</div>
                    <div>← ：上一个预言</div>
                    <div>S：开始预言</div>
                    <div>R：重新开始</div>
                `;

                document.body.appendChild(hints);

                setTimeout(() => {
                    hints.style.animation = 'fadeOut 0.5s ease';
                    setTimeout(() => hints.remove(), 500);
                }, 6000);
            }, 2000);
        });
    </script>
</body>
</html>
