<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>沉浸式阅读模拟</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Microsoft YaHei', sans-serif;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            width: 100%;
            max-width: 800px;
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        h1, h2 {
            color: #1a73e8;
            text-align: center;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 0;
        }

        #reading-text {
            font-size: 1.5em;
            text-align: justify;
            min-height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
            line-height: 1.8;
        }

        #reading-text .highlight {
            background-color: #a7d9ff; /* 突出显示当前阅读的字 */
            padding: 2px 0;
            border-radius: 3px;
        }

        .controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .controls button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s;
        }

        .controls button:hover {
            background-color: #155ab6;
        }

        .controls button.distracted {
            background-color: #e67e22; /* 分心状态按钮颜色 */
        }
        .controls button.distracted:hover {
            background-color: #d35400;
        }

        #status-display {
            margin-top: 20px;
            padding: 12px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            background-color: #e3f2fd;
            color: #1a73e8;
            border: 1px solid #90caf9;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>沉浸式阅读模拟</h1>
        <p>体验在不同专注度下的文字阅读过程。</p>
    </div>

    <div class="container">
        <h2>模拟阅读区</h2>
        <div id="reading-text"></div>
        <div class="controls">
            <button id="start-reading-btn">开始阅读</button>
            <button id="toggle-distraction-btn">模拟分心</button>
        </div>
        <div id="status-display">当前状态：准备就绪</div>
    </div>

    <div class="container">
        <h2>模拟说明</h2>
        <p>这个模拟器旨在帮助您理解在特定条件下（例如注意力不集中时）的阅读体验。它不是真正的眼球追踪技术，而是通过编程模拟您的阅读速度和注意力状态。</p>
        <ul>
            <li>点击“开始阅读”按钮，文字将开始逐字（或逐词）显示。</li>
            <li>“模拟分心”按钮将切换您的专注度：
                <ul>
                    <li><strong>专注模式：</strong>每次理解3个字。</li>
                    <li><strong>分心模式：</strong>每次理解1.5个字（这里通过每次显示1-2个字并在视觉上模拟“半个字”的理解来体现，实际显示仍是整数个字）。</li>
                </ul>
            </li>
            <li>当前状态会显示在下方。</li>
        </ul>
        <p><strong>注：</strong>“眼球监听”和“当前情况”是概念性的，本模拟器通过简单的按键切换来模拟，不涉及真实硬件交互。</p>
    </div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const readingTextDiv = document.getElementById('reading-text');
    const startReadingBtn = document.getElementById('start-reading-btn');
    const toggleDistractionBtn = document.getElementById('toggle-distraction-btn');
    const statusDisplay = document.getElementById('status-display');

    // 示例连续文字句子，灵感来源于奈奎斯特采样定理
    const fullText = "奈奎斯特采样定理是数字信号处理中的一个核心法则，它规定了为了不失真地从采样点恢复出原始的连续信号，采样频率必须大于或等于原始信号最高频率的两倍。当采样频率不足时，会发生混叠，导致信号失真。";
    let currentIndex = 0;
    let isDistracted = false;
    let readingInterval = null; // 用于存储setInterval的ID

    const READING_SPEED_FOCUSED_MS = 600; // 专注时，每3个字阅读所需时间（毫秒）
    const READING_SPEED_DISTRACTED_MS = 1000; // 分心时，每1.5个字阅读所需时间（毫秒）

    const CHARS_PER_STEP_FOCUSED = 3;
    const CHARS_PER_STEP_DISTRACTED = 1; // 模拟1.5个字，实际显示1个，通过间隔来体现慢

    function updateStatus() {
        const state = isDistracted ? '分心模式' : '专注模式';
        statusDisplay.textContent = `当前状态：${state}`;
        statusDisplay.style.backgroundColor = isDistracted ? '#fff3e0' : '#e3f2fd';
        statusDisplay.style.color = isDistracted ? '#e67e22' : '#1a73e8';
        toggleDistractionBtn.classList.toggle('distracted', isDistracted);
    }

    function startReading() {
        if (readingInterval) {
            clearInterval(readingInterval);
        }

        const currentCharsPerStep = isDistracted ? CHARS_PER_STEP_DISTRACTED : CHARS_PER_STEP_FOCUSED;
        const currentReadingSpeed = isDistracted ? READING_SPEED_DISTRACTED_MS : READING_SPEED_FOCUSED_MS;

        readingInterval = setInterval(() => {
            if (currentIndex >= fullText.length) {
                clearInterval(readingInterval);
                statusDisplay.textContent = '当前状态：阅读完成！';
                return;
            }

            // 获取当前要显示的文本部分
            const nextIndex = currentIndex + currentCharsPerStep;
            const currentSegment = fullText.substring(currentIndex, nextIndex);

            // 构建带有高亮的HTML
            const prefix = fullText.substring(0, currentIndex);
            const suffix = fullText.substring(nextIndex);
            
            // 确保高亮部分是可见的字符，而不是空的
            const highlightedHtml = currentSegment ? `<span class="highlight">${currentSegment}</span>` : '';

            readingTextDiv.innerHTML = prefix + highlightedHtml + suffix;

            currentIndex = nextIndex; // 更新索引

        }, currentReadingSpeed);
        updateStatus(); // 更新状态显示
    }

    // 重置并开始阅读
    startReadingBtn.addEventListener('click', () => {
        currentIndex = 0; // 从头开始
        readingTextDiv.innerHTML = ''; // 清空之前的文本
        startReading();
        statusDisplay.textContent = '当前状态：正在阅读...';
    });

    // 切换分心状态
    toggleDistractionBtn.addEventListener('click', () => {
        isDistracted = !isDistracted;
        // 如果正在阅读，则重新启动阅读以便应用新的速度
        if (readingInterval) {
            startReading();
        }
        updateStatus();
    });

    // 初始状态显示
    updateStatus();
});
</script>

</body>
</html>
