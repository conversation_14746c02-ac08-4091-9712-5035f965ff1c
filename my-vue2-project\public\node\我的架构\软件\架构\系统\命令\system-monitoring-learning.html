<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监视学习 - 互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            animation: slideInLeft 0.8s ease-out;
        }

        .method-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .method-card.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea10, #764ba210);
        }

        .method-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .method-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .quiz-title {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 30px;
        }

        .question {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255,255,255,0.2);
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .option.selected {
            border-color: #4CAF50;
            background: rgba(76,175,80,0.3);
        }

        .option.correct {
            border-color: #4CAF50;
            background: rgba(76,175,80,0.5);
        }

        .option.wrong {
            border-color: #f44336;
            background: rgba(244,67,54,0.3);
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🖥️ 系统监视学习</h1>
            <p class="subtitle">通过动画和交互学习系统监视的三种方式</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 知识概览</h2>
            <div class="explanation">
                <h3>什么是系统监视？</h3>
                <p>系统监视是指对计算机系统的运行状态、性能指标、资源使用情况等进行实时或定期的观察和记录，以确保系统正常运行并及时发现问题。</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="overviewCanvas" width="800" height="400"></canvas>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🔧 三种监视方式</h2>
            
            <div class="method-card" data-method="1">
                <div class="method-title">
                    <div class="method-icon" style="background: #FF6B6B;">💻</div>
                    方式一：系统命令
                </div>
                <p>通过操作系统提供的命令行工具来查看系统状态，如 ps、top、netstat 等命令。</p>
            </div>

            <div class="method-card" data-method="2">
                <div class="method-title">
                    <div class="method-icon" style="background: #4ECDC4;">📄</div>
                    方式二：系统记录文件
                </div>
                <p>通过查阅系统日志文件来了解系统在特定时间内的运行状态和事件记录。</p>
            </div>

            <div class="method-card" data-method="3">
                <div class="method-title">
                    <div class="method-icon" style="background: #45B7D1;">📊</div>
                    方式三：监控工具
                </div>
                <p>使用集成的可视化监控工具，如 Perfmon、iptables、top 等专业监控软件。</p>
            </div>

            <div class="canvas-container">
                <canvas id="methodCanvas" width="800" height="500"></canvas>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="quiz-title">🎯 知识测试</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="question">
                <h3>进行系统监视通常有三种方式：一是通过（ ），如UNIX/Linux系统中的ps、last等；二是通过系统记录文件查阅系统在特定时间内的运行状态；三是集成命令、文件记录和可视化技术的监控工具，如（ ）。</h3>
                
                <div class="options">
                    <div class="option" data-answer="A">A. 系统命令</div>
                    <div class="option" data-answer="B">B. 系统调用</div>
                    <div class="option" data-answer="C">C. 系统接口</div>
                    <div class="option" data-answer="D">D. 系统功能</div>
                </div>
                
                <button class="btn" id="submitBtn" onclick="checkAnswer()">提交答案</button>
                <button class="btn" id="nextBtn" onclick="showExplanation()" style="display:none;">查看解析</button>
            </div>
            
            <div id="explanation" style="display:none;">
                <div class="explanation">
                    <h3>📖 详细解析</h3>
                    <p><strong>正确答案：A. 系统命令</strong></p>
                    <p>系统监视的第一种方式是通过<strong>系统命令</strong>来实现的。这些命令是操作系统提供的工具，可以直接在命令行中执行：</p>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li><strong>ps</strong>：显示当前运行的进程信息</li>
                        <li><strong>last</strong>：显示用户登录历史</li>
                        <li><strong>top</strong>：实时显示系统进程状态</li>
                        <li><strong>netstat</strong>：显示网络连接状态</li>
                    </ul>
                    <p>而第三种方式提到的监控工具如 <strong>Perfmon</strong>（Windows性能监控器）则是集成了命令、文件记录和可视化技术的专业监控工具。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 20; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.width = (Math.random() * 20 + 10) + 'px';
                element.style.height = element.style.width;
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(element);
            }
        }

        // 概览画布动画
        function initOverviewCanvas() {
            const canvas = document.getElementById('overviewCanvas');
            const ctx = canvas.getContext('2d');
            let animationFrame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制系统监视概念图
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                
                // 中心圆 - 系统
                ctx.beginPath();
                ctx.arc(centerX, centerY, 60, 0, 2 * Math.PI);
                ctx.fillStyle = '#667eea';
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('系统', centerX, centerY + 5);
                
                // 三个监视方式
                const methods = [
                    { name: '系统命令', color: '#FF6B6B', angle: 0 },
                    { name: '记录文件', color: '#4ECDC4', angle: 2 * Math.PI / 3 },
                    { name: '监控工具', color: '#45B7D1', angle: 4 * Math.PI / 3 }
                ];
                
                methods.forEach((method, index) => {
                    const radius = 150;
                    const x = centerX + Math.cos(method.angle + animationFrame * 0.01) * radius;
                    const y = centerY + Math.sin(method.angle + animationFrame * 0.01) * radius;
                    
                    // 连接线
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(x, y);
                    ctx.strokeStyle = method.color;
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    
                    // 方法圆圈
                    ctx.beginPath();
                    ctx.arc(x, y, 40, 0, 2 * Math.PI);
                    ctx.fillStyle = method.color;
                    ctx.fill();
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText(method.name, x, y + 3);
                });
                
                animationFrame++;
                requestAnimationFrame(animate);
            }
            
            animate();
        }

        // 方法详细画布
        function initMethodCanvas() {
            const canvas = document.getElementById('methodCanvas');
            const ctx = canvas.getContext('2d');
            let currentMethod = 1;
            let animationProgress = 0;

            function drawMethod1() {
                // 绘制命令行界面
                ctx.fillStyle = '#2c3e50';
                ctx.fillRect(50, 50, 700, 400);
                
                // 标题栏
                ctx.fillStyle = '#34495e';
                ctx.fillRect(50, 50, 700, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('终端 - 系统命令演示', 60, 70);
                
                // 命令示例
                const commands = [
                    '$ ps aux',
                    'USER  PID  %CPU %MEM    VSZ   RSS TTY',
                    'root    1   0.0  0.1  19356  1544 ?',
                    'user  1234  2.1  5.2  98765 12345 pts/0',
                    '',
                    '$ top',
                    'PID USER %CPU %MEM COMMAND',
                    '1234 user  15.2  8.1 firefox'
                ];
                
                ctx.fillStyle = '#2ecc71';
                ctx.font = '12px Consolas, monospace';
                commands.forEach((cmd, index) => {
                    if (index < animationProgress / 10) {
                        ctx.fillText(cmd, 70, 110 + index * 25);
                    }
                });
            }

            function drawMethod2() {
                // 绘制日志文件
                ctx.fillStyle = '#ecf0f1';
                ctx.fillRect(100, 80, 600, 340);
                
                // 文件头
                ctx.fillStyle = '#3498db';
                ctx.fillRect(100, 80, 600, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('系统日志文件 - /var/log/syslog', 120, 105);
                
                // 日志内容
                const logs = [
                    '2024-01-15 10:30:15 [INFO] System startup complete',
                    '2024-01-15 10:31:22 [WARN] High memory usage detected',
                    '2024-01-15 10:32:45 [ERROR] Network connection failed',
                    '2024-01-15 10:33:12 [INFO] Service restarted successfully',
                    '2024-01-15 10:34:33 [DEBUG] Process monitoring active'
                ];
                
                ctx.fillStyle = '#2c3e50';
                ctx.font = '12px Consolas, monospace';
                logs.forEach((log, index) => {
                    if (index < animationProgress / 15) {
                        const color = log.includes('ERROR') ? '#e74c3c' : 
                                     log.includes('WARN') ? '#f39c12' : '#27ae60';
                        ctx.fillStyle = color;
                        ctx.fillText(log, 120, 150 + index * 30);
                    }
                });
            }

            function drawMethod3() {
                // 绘制监控工具界面
                ctx.fillStyle = '#34495e';
                ctx.fillRect(50, 50, 700, 400);
                
                // 工具栏
                ctx.fillStyle = '#2c3e50';
                ctx.fillRect(50, 50, 700, 40);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('Perfmon - 性能监控器', 60, 75);
                
                // CPU使用率图表
                ctx.fillStyle = '#3498db';
                ctx.fillRect(80, 120, 300, 20);
                ctx.fillText('CPU使用率', 80, 115);
                
                const cpuUsage = Math.sin(animationProgress * 0.1) * 0.3 + 0.5;
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(80, 120, 300 * cpuUsage, 20);
                
                // 内存使用率图表
                ctx.fillStyle = '#3498db';
                ctx.fillRect(80, 180, 300, 20);
                ctx.fillText('内存使用率', 80, 175);
                
                const memUsage = Math.cos(animationProgress * 0.08) * 0.2 + 0.6;
                ctx.fillStyle = '#f39c12';
                ctx.fillRect(80, 180, 300 * memUsage, 20);
                
                // 网络活动图
                ctx.strokeStyle = '#2ecc71';
                ctx.lineWidth = 2;
                ctx.beginPath();
                for (let i = 0; i < 200; i++) {
                    const x = 450 + i * 2;
                    const y = 300 + Math.sin((animationProgress + i) * 0.1) * 50;
                    if (i === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.stroke();
                ctx.fillStyle = 'white';
                ctx.fillText('网络活动', 450, 250);
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                switch(currentMethod) {
                    case 1: drawMethod1(); break;
                    case 2: drawMethod2(); break;
                    case 3: drawMethod3(); break;
                }
                
                animationProgress++;
                requestAnimationFrame(animate);
            }

            // 方法卡片点击事件
            document.querySelectorAll('.method-card').forEach(card => {
                card.addEventListener('click', () => {
                    document.querySelectorAll('.method-card').forEach(c => c.classList.remove('active'));
                    card.classList.add('active');
                    currentMethod = parseInt(card.dataset.method);
                    animationProgress = 0;
                });
            });

            animate();
        }

        // 测试功能
        let selectedAnswer = null;

        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.option').forEach(o => o.classList.remove('selected'));
                option.classList.add('selected');
                selectedAnswer = option.dataset.answer;
            });
        });

        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }

            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                if (option.dataset.answer === 'A') {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && option.dataset.answer !== 'A') {
                    option.classList.add('wrong');
                }
            });

            document.getElementById('submitBtn').style.display = 'none';
            document.getElementById('nextBtn').style.display = 'inline-block';
            
            // 更新进度条
            document.getElementById('progressFill').style.width = '100%';
        }

        function showExplanation() {
            document.getElementById('explanation').style.display = 'block';
            document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
        }

        // 初始化
        window.addEventListener('load', () => {
            createFloatingElements();
            initOverviewCanvas();
            initMethodCanvas();
            
            // 默认选中第一个方法
            document.querySelector('.method-card[data-method="1"]').classList.add('active');
        });
    </script>
</body>
</html>
