<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式学习：命令模式</title>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #f5a623;
            --background-color: #f4f7f9;
            --text-color: #333;
            --card-bg: #ffffff;
            --shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            width: 100%;
            max-width: 900px;
        }
        h1, h2 {
            color: var(--primary-color);
            text-align: center;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .card {
            background-color: var(--card-bg);
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: var(--shadow);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .question-box p {
            font-size: 1.1em;
            margin-bottom: 15px;
        }
        .question-box ul {
            list-style-type: decimal;
            padding-left: 20px;
        }
        .question-box li {
            margin-bottom: 10px;
        }
        .answer {
            background-color: #e8f4ff;
            border: 1px solid var(--primary-color);
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .answer span {
            color: var(--primary-color);
            font-size: 1.2em;
        }

        #interactive-demo {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        #canvas-container {
            flex-grow: 1;
            text-align: center;
        }
        #canvas {
            background-color: #fff;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.05);
        }
        #controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
            min-width: 200px;
        }
        button {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease, transform 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        button:hover {
            background-color: #357abd;
            transform: scale(1.05);
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
        }
        button.secondary {
            background-color: var(--secondary-color);
        }
        button.secondary:hover {
            background-color: #d98e1a;
        }
        button.tertiary {
            background-color: #e74c3c;
        }
        button.tertiary:hover {
            background-color: #c0392b;
        }
        
        .explanation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .explanation-card {
            background: #e8f4ff;
            border-left: 5px solid var(--primary-color);
            padding: 20px;
            border-radius: 5px;
        }
        .explanation-card h3 {
            margin-top: 0;
            color: #357abd;
        }

        @media (max-width: 768px) {
            #interactive-demo {
                flex-direction: column;
            }
            #controls {
                flex-direction: row;
                flex-wrap: wrap;
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <h1>命令模式 (Command Pattern) 深入理解</h1>

    <div class="card question-box">
        <h2>原题回顾</h2>
        <p>某软件公司欲设计一款图像处理软件，帮助用户对拍摄的照片进行后期处理。在软件需求分析阶段，公司的系统分析师识别出了如下3个关键需求：</p>
        <ul>
            <li>图像处理软件需要记录用户在处理照片时的所有动作，并能够支持用户对动作的<strong>撤销与重做</strong>等行为。</li>
            <li>图像处理软件需要根据当前正在处理的照片的不同特征选择合适的处理操作，处理操作与照片特征之间具有较为复杂的逻辑关系。</li>
            <li>图像处理软件需要封装各种图像处理算法，用户能够根据需要灵活选择合适的处理算法；软件还要支持高级用户根据一定的规则添加自定义处理算法。</li>
        </ul>
        <p>在系统设计阶段，公司的架构师决定采用设计模式满足上述关键需求中对系统灵活性与扩展性的要求。具体来说，为了支持灵活的<strong>撤销与重做</strong>等行为，采用 (D) 最为合适。</p>
        <div class="answer">
            正确答案: <span>D. 命令模式</span>
        </div>
    </div>

    <div class="card">
        <h2>交互式体验：模拟图像处理</h2>
        <p>下方是一个简化的"画板"。您可以把它想象成一个图像处理软件。每次"绘制"都是一个操作，就像给图片加滤镜或裁剪。请试着执行一些操作，然后使用"撤销"和"重做"按钮，直观感受命令模式是如何工作的。</p>
        <div id="interactive-demo">
            <div id="controls">
                <button id="draw-circle-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle></svg>
                    添加圆形
                </button>
                <button id="draw-square-btn" class="secondary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg>
                    添加方形
                </button>
                <button id="undo-btn" class="tertiary">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 4H8l-7 8 7 8h13a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2z"></path><line x1="18" y1="9" x2="12" y2="15"></line><line x1="12" y1="9" x2="18" y2="15"></line></svg>
                    撤销 (Undo)
                </button>
                <button id="redo-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 4h13l7 8-7 8H3a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z"></path><line x1="12" y1="9" x2="18" y2="15"></line><line x1="18" y1="9" x2="12" y2="15"></line></svg>
                    重做 (Redo)
                </button>
            </div>
            <div id="canvas-container">
                <canvas id="canvas" width="600" height="400"></canvas>
            </div>
        </div>
    </div>

    <div class="card">
        <h2>知识解析：命令模式是什么？</h2>
        <p>命令模式的核心思想是：<strong>将一个请求封装成一个对象，从而使您可以用不同的请求对客户进行参数化，对请求排队或记录请求日志，以及支持可撤销的操作。</strong></p>
        <p>听起来很抽象？让我们结合上面的例子来理解：</p>
        <div class="explanation-grid">
            <div class="explanation-card">
                <h3>1. 请求变成了"命令"对象</h3>
                <p>您每次点击"添加圆形"，程序并不是直接去画图。相反，它创建了一个"绘制圆形命令"的对象。这个对象包含了执行（画圆）和撤销（擦除这个圆）所需的所有信息（如坐标、颜色、半径）。</p>
            </div>
            <div class="explanation-card">
                <h3>2. "历史记录"列表</h3>
                <p>程序维护了一个列表，专门存放您执行过的所有"命令"对象。这就像您在图像处理软件里的操作历史记录。</p>
            </div>
            <div class="explanation-card">
                <h3>3. 撤销 (Undo) 和 重做 (Redo)</h3>
                <p><strong>撤销</strong>：点击"撤销"时，程序从"历史记录"列表的末尾取走最后一个命令对象，然后调用该对象的<code>undo()</code>方法。最简单的实现就是清空画布，然后把历史记录里剩下的命令重新执行一遍。</p>
                <p><strong>重做</strong>：被撤销的命令会被放入一个"重做列表"。点击"重做"，就从这个列表里取出命令，重新执行并放回"历史记录"中。</p>
            </div>
        </div>
        <p style="margin-top: 20px;"><strong>总结：</strong>通过将操作封装成一个个独立的对象，命令模式成功地将"操作的请求者"（按钮）与"操作的执行者"（画板）解耦。这使得记录、排队、以及最重要的——撤销和重做操作——变得非常容易实现，完美地解决了原题中的第一个需求。</p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    const drawCircleBtn = document.getElementById('draw-circle-btn');
    const drawSquareBtn = document.getElementById('draw-square-btn');
    const undoBtn = document.getElementById('undo-btn');
    const redoBtn = document.getElementById('redo-btn');

    // 命令的历史记录 (已执行)
    let history = [];
    // 重做栈 (已撤销)
    let redoStack = [];

    // --- 命令对象的定义 ---
    // 这是一个"基类"，定义了所有命令的通用接口
    class Command {
        execute() {
            throw new Error("execute() must be implemented by subclasses.");
        }
        undo() {
             // 在这个简单的实现中，undo是通过重绘所有历史命令实现的
             // 更复杂的应用可能需要每个命令自己实现具体的undo逻辑
        }
    }

    // "绘制圆形"的具体命令
    class DrawCircleCommand extends Command {
        constructor(x, y, radius, color) {
            super();
            this.x = x;
            this.y = y;
            this.radius = radius;
            this.color = color;
        }

        execute() {
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
            ctx.fillStyle = this.color;
            ctx.fill();
        }
    }

    // "绘制方形"的具体命令
    class DrawSquareCommand extends Command {
        constructor(x, y, size, color) {
            super();
            this.x = x;
            this.y = y;
            this.size = size;
            this.color = color;
        }
        
        execute() {
            ctx.fillStyle = this.color;
            ctx.fillRect(this.x, this.y, this.size, this.size);
        }
    }

    // --- 核心逻辑 ---

    // 执行一个命令
    function executeCommand(command) {
        command.execute();
        history.push(command);
        // 当有新操作时，清空重做栈
        redoStack = [];
        updateButtons();
    }

    // 撤销上一个命令
    function undoCommand() {
        if (history.length > 0) {
            const commandToUndo = history.pop();
            redoStack.push(commandToUndo);
            redrawCanvas();
            updateButtons();
        }
    }

    // 重做一个命令
    function redoCommand() {
        if (redoStack.length > 0) {
            const commandToRedo = redoStack.pop();
            commandToRedo.execute();
            history.push(commandToRedo);
            updateButtons();
        }
    }

    // 重绘整个画布
    function redrawCanvas() {
        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        // 重新执行历史记录中的所有命令
        history.forEach(command => command.execute());
    }
    
    // 更新按钮的可用状态
    function updateButtons() {
        undoBtn.disabled = history.length === 0;
        redoBtn.disabled = redoStack.length === 0;
    }

    // --- 事件监听 ---

    drawCircleBtn.addEventListener('click', () => {
        const x = Math.random() * canvas.width * 0.8 + canvas.width * 0.1;
        const y = Math.random() * canvas.height * 0.8 + canvas.height * 0.1;
        const radius = Math.random() * 30 + 10;
        const color = `rgba(74, 144, 226, 0.7)`;
        const command = new DrawCircleCommand(x, y, radius, color);
        executeCommand(command);
    });

    drawSquareBtn.addEventListener('click', () => {
        const size = Math.random() * 60 + 20;
        const x = Math.random() * (canvas.width - size) * 0.9 + (canvas.width - size) * 0.05;
        const y = Math.random() * (canvas.height - size) * 0.9 + (canvas.height - size) * 0.05;
        const color = `rgba(245, 166, 35, 0.7)`;
        const command = new DrawSquareCommand(x, y, size, color);
        executeCommand(command);
    });
    
    undoBtn.addEventListener('click', undoCommand);
    redoBtn.addEventListener('click', redoCommand);

    // --- 初始化 ---
    updateButtons(); // 初始状态下禁用撤销和重做
});
</script>

</body>
</html>
