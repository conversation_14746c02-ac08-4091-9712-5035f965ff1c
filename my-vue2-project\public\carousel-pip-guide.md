# 轮播与画中画功能使用指南

## 🎠 轮播功能

### 基本操作
- **开启轮播**: 点击"轮播"按钮开始自动播放
- **停止轮播**: 点击"停止"按钮暂停自动播放
- **手动切换**: 使用左右箭头按钮或键盘方向键

### 轮播设置
点击"设置"按钮可以自定义以下选项：

#### ⏱️ 轮播间隔
- 范围：1-60秒
- 默认：5秒
- 说明：每隔指定时间自动切换到下一个文件

#### 🔄 轮播方向
- **向前轮播**: 按顺序播放文件
- **向后轮播**: 按倒序播放文件  
- **随机轮播**: 随机选择文件播放

#### 🔁 循环播放
- 开启：到达最后一个文件时回到第一个文件继续播放
- 关闭：到达最后一个文件时停止轮播

#### 🖱️ 鼠标悬停
- 开启：鼠标悬停在内容区域时暂停轮播
- 关闭：鼠标悬停时继续轮播

#### ⌨️ 键盘控制
- 开启：可以使用方向键控制轮播
- 关闭：禁用键盘控制

### 键盘快捷键
- `←` / `→`: 左右切换文件
- `↑` / `↓`: 上下切换文件
- `空格键`: 暂停/继续轮播

## 📺 画中画功能

### 什么是画中画？
画中画（Picture-in-Picture, PiP）允许您在一个小的浮动窗口中继续观看内容，同时浏览其他网页或应用程序。

### 使用方法
1. **开启画中画**: 点击"画中画"按钮
2. **调整窗口**: 拖拽画中画窗口到合适位置
3. **调整大小**: 拖拽窗口边角调整大小
4. **退出画中画**: 点击画中画窗口的关闭按钮或主窗口的"画中画"按钮

### 画中画内容
画中画窗口会显示：
- 📚 当前文件名和路径
- 📊 阅读进度（当前文件/总文件数）
- 🔄 轮播状态指示
- 📄 内容预览
- ⌨️ 控制提示

### 浏览器支持
- ✅ Chrome 70+
- ✅ Firefox 71+
- ✅ Safari 13.1+
- ✅ Edge 79+
- ❌ Internet Explorer（不支持）

### 注意事项
- 画中画功能需要现代浏览器支持
- 某些浏览器可能需要用户手势（如点击）才能启动
- 画中画窗口会自动跟随轮播切换内容
- 可以通过主窗口控制轮播，画中画窗口会同步更新

## 🎯 最佳实践

### 学习场景
1. **在线学习**: 开启画中画继续学习，同时做笔记
2. **资料对比**: 在画中画中查看参考资料，主窗口编辑文档
3. **多任务处理**: 画中画监控重要信息，主窗口处理其他任务

### 设置建议
- **快速浏览**: 轮播间隔设为2-3秒，开启循环播放
- **仔细阅读**: 轮播间隔设为10-15秒，开启鼠标悬停暂停
- **随机复习**: 选择随机轮播模式，间隔5-8秒

### 性能优化
- 画中画使用15fps渲染，平衡性能与流畅度
- 长时间使用建议定期关闭画中画释放资源
- 在低性能设备上可以关闭动画效果

## 🔧 故障排除

### 轮播问题
- **轮播不工作**: 检查是否有文件可供轮播
- **键盘控制失效**: 确保键盘控制选项已开启
- **轮播太快/太慢**: 调整轮播间隔设置

### 画中画问题
- **无法开启**: 检查浏览器是否支持画中画功能
- **画面卡顿**: 尝试刷新页面或重启浏览器
- **窗口消失**: 检查是否被其他窗口遮挡

### 兼容性问题
- **功能缺失**: 更新到最新版本的现代浏览器
- **性能问题**: 关闭其他占用资源的标签页

## 📞 技术支持

如果您遇到任何问题或有功能建议，请：
1. 检查浏览器控制台是否有错误信息
2. 尝试刷新页面或重启浏览器
3. 确认浏览器版本是否支持相关功能
4. 联系技术支持团队

---

*最后更新: 2025年1月*
