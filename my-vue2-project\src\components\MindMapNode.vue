<template>
  <div class="mind-map-node">
    <div 
      class="node-item" 
      :class="{ 
        'viewed': isNodeViewed, 
        'has-children': node.children && node.children.length > 0,
        'level-1': level === 1,
        'level-2': level === 2,
        'level-3': level >= 3
      }"
      @click="handleClick"
    >
      <i :class="node.icon || 'el-icon-folder'"></i>
      <span>{{ node.name }}</span>
      <span v-if="node.files && node.files.length > 0" class="file-count">
        ({{ node.files.length }})
      </span>
    </div>
    
    <!-- 子节点 -->
    <div class="children-grid" v-if="node.children && node.children.length > 0 && expanded">
      <div 
        v-for="child in limitedChildren" 
        :key="child.name"
        class="child-item"
      >
        <div class="connection-line" v-if="level < 3"></div>
        <mind-map-node 
          :node="child" 
          :level="level + 1"
          :is-viewed="isViewed"
          @node-click="$emit('node-click', $event)"
        />
      </div>
      
      <!-- 显示更多按钮 -->
      <div v-if="node.children.length > maxItemsPerRow" class="show-more">
        <el-button 
          type="text" 
          size="small"
          @click="toggleShowAll"
        >
          {{ showAll ? '收起' : `显示更多 (${node.children.length - maxItemsPerRow})` }}
        </el-button>
      </div>
    </div>
    
    <!-- 展开/收起按钮 -->
    <div 
      v-if="node.children && node.children.length > 0" 
      class="expand-button"
      @click="toggleExpanded"
    >
      <i :class="expanded ? 'el-icon-minus' : 'el-icon-plus'"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MindMapNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    level: {
      type: Number,
      default: 0
    },
    isViewed: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      expanded: this.level <= 1,
      showAll: false,
      maxItemsPerRow: 10
    }
  },
  computed: {
    isNodeViewed() {
      return this.isViewed(this.node)
    },
    limitedChildren() {
      if (!this.node.children) return []
      
      if (this.showAll) {
        return this.node.children
      }
      
      return this.node.children.slice(0, this.maxItemsPerRow)
    }
  },
  methods: {
    handleClick() {
      this.$emit('node-click', this.node)
    },
    
    toggleExpanded() {
      this.expanded = !this.expanded
    },
    
    toggleShowAll() {
      this.showAll = !this.showAll
    }
  }
}
</script>

<style scoped>
.mind-map-node {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  margin: 4px;
  border-radius: 20px;
  background: #f5f5f5;
  border: 2px solid #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  min-width: 100px;
  justify-content: center;
  position: relative;
}

.node-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}

.node-item.level-1 {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  font-weight: 600;
  border: none;
}

.node-item.level-2 {
  background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  color: white;
  font-weight: 500;
  border: none;
}

.node-item.level-3 {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  color: white;
  font-weight: 500;
  border: none;
}

.node-item.viewed {
  background: linear-gradient(135deg, #00b894 0%, #00a085 100%) !important;
  color: white !important;
  border-color: #00b894 !important;
}

.node-item:not(.viewed):not(.level-1):not(.level-2):not(.level-3) {
  background: #e0e0e0;
  color: #666;
  border-color: #ccc;
}

.node-item i {
  margin-right: 6px;
  font-size: 14px;
}

.file-count {
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.8;
}

.children-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  max-width: 800px;
  margin-top: 20px;
  gap: 8px;
}

.child-item {
  position: relative;
  flex: 0 0 auto;
}

.connection-line {
  position: absolute;
  top: -15px;
  left: 50%;
  width: 1px;
  height: 15px;
  background: #ddd;
  transform: translateX(-50%);
}

.expand-button {
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.expand-button:hover {
  background: #337ecc;
  transform: translateX(-50%) scale(1.1);
}

.show-more {
  width: 100%;
  text-align: center;
  margin-top: 10px;
}

:global(.dark-mode) .node-item:not(.viewed):not(.level-1):not(.level-2):not(.level-3) {
  background: #424242;
  color: #bbb;
  border-color: #555;
}

:global(.dark-mode) .connection-line {
  background: #555;
}

@media (max-width: 768px) {
  .children-grid {
    max-width: 100%;
  }
  
  .node-item {
    min-width: 80px;
    padding: 6px 12px;
    font-size: 13px;
  }
}
</style>
