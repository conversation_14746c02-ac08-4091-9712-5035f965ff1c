<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：-able/-ible（能够、可以）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 450px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        #magicCanvas {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #f0f8ff, #e6f3ff);
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .word-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .word-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .word-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .word-card:hover::before {
            left: 100%;
        }

        .word-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .word-card.show {
            opacity: 1;
            transform: translateY(0);
        }

        .word-card h3 {
            color: #667eea;
            font-size: 1.8rem;
            margin-bottom: 20px;
            position: relative;
        }

        .transformation {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .base-word {
            background: #ff6b6b;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .suffix-part {
            background: #4ecdc4;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .arrow {
            font-size: 1.5rem;
            color: #667eea;
            margin: 0 10px;
        }

        .result-word {
            background: #45b7d1;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .meaning {
            color: #666;
            font-size: 1.1rem;
            margin: 15px 0;
            font-style: italic;
        }

        .example {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            color: #555;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #ffa500;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .magic-particles {
            position: absolute;
            width: 6px;
            height: 6px;
            background: #ffd700;
            border-radius: 50%;
            pointer-events: none;
            animation: sparkle 2s infinite;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes sparkle {
            0%, 100% {
                opacity: 0;
                transform: scale(0);
            }
            50% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 5px #ffd700;
            }
            50% {
                box-shadow: 0 0 20px #ffd700, 0 0 30px #ffd700;
            }
        }

        .interactive-hint {
            text-align: center;
            color: #667eea;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>词缀魔法：-able/-ible</h1>
            <p>在魔法工坊中学会"能力变身术"</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🪄 魔法变身工坊的故事</h2>
                <p>在一个神奇的魔法工坊里，有位魔法师专门帮助普通的动词获得"能力"。他有两种神奇的魔法粉末："-able"和"-ible"。当他把这些魔法粉末撒在动词上时，这些词就会变身，获得"能够被..."或"可以..."的神奇能力！</p>
            </div>

            <div class="canvas-container">
                <canvas id="magicCanvas"></canvas>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择魔法工坊的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"魔法变身"的比喻，是因为"-able/-ible"这个词缀的作用就像魔法一样，能够改变词的性质。普通的动词（如read读）通过添加"-able"变成形容词（readable可读的），就像获得了新的"能力"。魔法粉末的视觉效果帮助你理解词缀的"转化"作用，让抽象的语法概念变得生动有趣。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="startMagic()">开始魔法</button>
                <button class="btn" onclick="showTransformations()">显示变身</button>
                <button class="btn" onclick="resetMagic()">重新施法</button>
            </div>

            <div class="interactive-hint">
                ✨ 点击"开始魔法"观看词汇变身过程，点击单词卡片查看详细解释
            </div>
        </div>

        <div class="word-showcase" id="wordCards">
            <div class="word-card">
                <h3>Readable</h3>
                <div class="transformation">
                    <span class="base-word">read</span>
                    <span class="arrow">+</span>
                    <span class="suffix-part">-able</span>
                    <span class="arrow">→</span>
                    <span class="result-word">readable</span>
                </div>
                <div class="meaning">读 + 能够 = 可读的</div>
                <div class="example">
                    <strong>例句：</strong>This book is very readable.<br>
                    <strong>翻译：</strong>这本书很容易阅读。
                </div>
            </div>

            <div class="word-card">
                <h3>Visible</h3>
                <div class="transformation">
                    <span class="base-word">vis</span>
                    <span class="arrow">+</span>
                    <span class="suffix-part">-ible</span>
                    <span class="arrow">→</span>
                    <span class="result-word">visible</span>
                </div>
                <div class="meaning">看 + 能够 = 可见的</div>
                <div class="example">
                    <strong>例句：</strong>The stars are visible tonight.<br>
                    <strong>翻译：</strong>今晚能看到星星。
                </div>
            </div>

            <div class="word-card">
                <h3>Comfortable</h3>
                <div class="transformation">
                    <span class="base-word">comfort</span>
                    <span class="arrow">+</span>
                    <span class="suffix-part">-able</span>
                    <span class="arrow">→</span>
                    <span class="result-word">comfortable</span>
                </div>
                <div class="meaning">舒适 + 能够 = 舒适的</div>
                <div class="example">
                    <strong>例句：</strong>This chair is very comfortable.<br>
                    <strong>翻译：</strong>这把椅子很舒适。
                </div>
            </div>

            <div class="word-card">
                <h3>Possible</h3>
                <div class="transformation">
                    <span class="base-word">poss</span>
                    <span class="arrow">+</span>
                    <span class="suffix-part">-ible</span>
                    <span class="arrow">→</span>
                    <span class="result-word">possible</span>
                </div>
                <div class="meaning">能够 + 能够 = 可能的</div>
                <div class="example">
                    <strong>例句：</strong>Everything is possible.<br>
                    <strong>翻译：</strong>一切皆有可能。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"-able/-ible"词缀表示"能够被..."或"可以..."的意思。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>找词根：</strong>去掉"-able/-ible"后缀，找到基础词根</li>
                <li><strong>理解词根：</strong>明确词根的基本含义</li>
                <li><strong>添加能力：</strong>在词根含义前加上"可以"或"能够被"</li>
                <li><strong>调整语序：</strong>根据中文习惯调整表达方式</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象魔法师给词汇施加"能力魔法"，让它们获得新的特性！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('magicCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let magician = { x: 100, y: canvas.height - 120 };
        let words = [
            { base: 'read', suffix: '-able', result: 'readable', x: 300, y: canvas.height - 100, transformed: false },
            { base: 'vis', suffix: '-ible', result: 'visible', x: 500, y: canvas.height - 100, transformed: false },
            { base: 'comfort', suffix: '-able', result: 'comfortable', x: 700, y: canvas.height - 100, transformed: false },
            { base: 'poss', suffix: '-ible', result: 'possible', x: 900, y: canvas.height - 100, transformed: false }
        ];
        let currentWord = 0;
        let particles = [];
        let magicWand = { x: 120, y: canvas.height - 140, angle: 0 };

        class Particle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = (Math.random() - 0.5) * 4;
                this.life = 1;
                this.decay = 0.02;
                this.size = Math.random() * 4 + 2;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.size *= 0.98;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = '#ffd700';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        function drawMagician() {
            const { x, y } = magician;
            
            // 身体
            ctx.fillStyle = '#4B0082';
            ctx.fillRect(x - 15, y - 40, 30, 50);
            
            // 头部
            ctx.fillStyle = '#FDBCB4';
            ctx.beginPath();
            ctx.arc(x, y - 50, 15, 0, Math.PI * 2);
            ctx.fill();
            
            // 帽子
            ctx.fillStyle = '#4B0082';
            ctx.beginPath();
            ctx.moveTo(x - 20, y - 45);
            ctx.lineTo(x, y - 80);
            ctx.lineTo(x + 20, y - 45);
            ctx.closePath();
            ctx.fill();
            
            // 星星装饰
            ctx.fillStyle = '#FFD700';
            for (let i = 0; i < 3; i++) {
                const starX = x - 10 + i * 10;
                const starY = y - 30 + i * 5;
                drawStar(starX, starY, 3);
            }
            
            // 魔法棒
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(magicWand.x, magicWand.y);
            ctx.lineTo(magicWand.x + 40, magicWand.y - 20);
            ctx.stroke();
            
            // 魔法棒顶端
            ctx.fillStyle = '#FFD700';
            drawStar(magicWand.x + 40, magicWand.y - 20, 6);
        }

        function drawStar(x, y, size) {
            ctx.save();
            ctx.translate(x, y);
            ctx.beginPath();
            for (let i = 0; i < 5; i++) {
                ctx.lineTo(Math.cos((18 + i * 72) * Math.PI / 180) * size, 
                          Math.sin((18 + i * 72) * Math.PI / 180) * size);
                ctx.lineTo(Math.cos((54 + i * 72) * Math.PI / 180) * size / 2, 
                          Math.sin((54 + i * 72) * Math.PI / 180) * size / 2);
            }
            ctx.closePath();
            ctx.fill();
            ctx.restore();
        }

        function drawWord(word) {
            const { base, suffix, result, x, y, transformed } = word;
            
            if (!transformed) {
                // 原始词
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(x - 30, y - 20, 60, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(base, x, y - 5);
            } else {
                // 变身后的词
                ctx.fillStyle = '#45b7d1';
                ctx.fillRect(x - 40, y - 20, 80, 30);
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(result, x, y - 5);
                
                // 光环效果
                ctx.strokeStyle = '#ffd700';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(x, y - 5, 50, 0, Math.PI * 2);
                ctx.stroke();
            }
        }

        function createMagicEffect(x, y) {
            for (let i = 0; i < 15; i++) {
                particles.push(new Particle(x, y));
            }
        }

        function updateParticles() {
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#E6E6FA');
            gradient.addColorStop(1, '#F0F8FF');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制地面
            ctx.fillStyle = '#DDA0DD';
            ctx.fillRect(0, canvas.height - 60, canvas.width, 60);
            
            // 绘制魔法师
            drawMagician();
            
            // 绘制词汇
            words.forEach(drawWord);
            
            // 更新粒子效果
            updateParticles();
            
            // 绘制进度
            if (animationState === 'transforming') {
                const progress = (currentWord / words.length) * 100;
                document.getElementById('progressFill').style.width = progress + '%';
            }
        }

        function animateMagic() {
            if (animationState === 'transforming' && currentWord < words.length) {
                const word = words[currentWord];
                
                // 创建魔法效果
                if (Math.random() < 0.3) {
                    createMagicEffect(word.x, word.y);
                }
                
                // 延迟变身
                setTimeout(() => {
                    word.transformed = true;
                    currentWord++;
                    
                    if (currentWord >= words.length) {
                        animationState = 'completed';
                        document.getElementById('progressFill').style.width = '100%';
                    }
                }, 1500);
            }
            
            drawScene();
            requestAnimationFrame(animateMagic);
        }

        function startMagic() {
            animationState = 'transforming';
            currentWord = 0;
            words.forEach(word => word.transformed = false);
            particles = [];
            document.getElementById('progressFill').style.width = '0%';
        }

        function showTransformations() {
            const cards = document.querySelectorAll('.word-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('show');
                }, index * 300);
            });
        }

        function resetMagic() {
            animationState = 'idle';
            currentWord = 0;
            words.forEach(word => word.transformed = false);
            particles = [];
            document.getElementById('progressFill').style.width = '0%';
            
            const cards = document.querySelectorAll('.word-card');
            cards.forEach(card => card.classList.remove('show'));
        }

        // 初始化
        drawScene();
        animateMagic();

        // 点击单词卡片的交互
        document.querySelectorAll('.word-card').forEach(card => {
            card.addEventListener('click', function() {
                // 创建闪光效果
                this.style.animation = 'glow 0.8s ease';
                setTimeout(() => {
                    this.style.animation = '';
                }, 800);
            });
        });
    </script>
</body>
</html>
