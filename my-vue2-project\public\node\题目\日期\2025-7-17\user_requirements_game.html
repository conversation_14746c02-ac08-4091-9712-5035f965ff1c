<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户需求探索游戏</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f4f7f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        .explanation {
            background: #e9f7fd;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .highlight {
            background-color: #fffacd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .game-container {
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f0f8ff;
        }
        .game-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .game-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .game-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .game-card:hover {
            transform: translateY(-3px);
        }
        .card-header {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .card-content {
            margin-bottom: 15px;
        }
        .card-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .option-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .option-btn:hover {
            background-color: #e9ecef;
        }
        .option-btn.selected {
            background-color: #3498db;
            color: white;
            border-color: #2980b9;
        }
        .feedback {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .feedback.correct {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .feedback.incorrect {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .next-btn, .check-btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            transition: background-color 0.3s;
        }
        .next-btn:hover, .check-btn:hover {
            background-color: #2980b9;
        }
        .next-btn:disabled, .check-btn:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .progress-container {
            margin: 20px 0;
            background-color: #e9ecef;
            border-radius: 5px;
            height: 10px;
        }
        .progress-bar {
            height: 100%;
            border-radius: 5px;
            background-color: #3498db;
            width: 0%;
            transition: width 0.5s;
        }
        .score-display {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
        }
        .requirement-type {
            font-weight: bold;
        }
        .requirement-type.user {
            color: #28a745;
        }
        .requirement-type.system {
            color: #dc3545;
        }
        .requirement-type.constraint {
            color: #fd7e14;
        }
        .game-result {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            display: none;
        }
        .project-scenario {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }
        .project-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .project-description {
            margin-bottom: 15px;
        }
        .tab-container {
            margin-top: 20px;
        }
        .tab-buttons {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }
        .tab-button {
            padding: 10px 20px;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            cursor: pointer;
        }
        .tab-button.active {
            background-color: white;
            font-weight: bold;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 5px;
            background-color: white;
        }
        .tab-content.active {
            display: block;
        }
        .requirement-example {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .requirement-example.user {
            background-color: #d4edda;
            border-left: 5px solid #28a745;
        }
        .requirement-example.system {
            background-color: #f8d7da;
            border-left: 5px solid #dc3545;
        }
        .requirement-example.constraint {
            background-color: #fff3cd;
            border-left: 5px solid #fd7e14;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>用户需求探索游戏</h1>
        
        <div class="explanation">
            <h2>什么是用户需求？</h2>
            <p>用户需求是指<strong>用户希望系统能够满足的功能、特性或服务</strong>。它们直接来源于用户的期望和要求，反映了用户想要通过系统实现的目标。</p>
            <p>在软件开发中，需求通常分为三大类：</p>
            <ul>
                <li><span class="requirement-type user">用户需求</span>：用户直接表达的期望和要求</li>
                <li><span class="requirement-type system">系统需求</span>：为满足用户需求而定义的系统功能和特性</li>
                <li><span class="requirement-type constraint">约束条件</span>：限制系统设计和实现方式的条件或规则</li>
            </ul>
        </div>
        
        <div class="tab-container">
            <div class="tab-buttons">
                <div class="tab-button active" data-tab="tab-user">用户需求</div>
                <div class="tab-button" data-tab="tab-system">系统需求</div>
                <div class="tab-button" data-tab="tab-constraint">约束条件</div>
            </div>
            
            <div class="tab-content active" id="tab-user">
                <h3>用户需求的特点</h3>
                <ul>
                    <li>直接来源于<strong>用户的期望和要求</strong></li>
                    <li>通常使用<strong>用户的语言</strong>表达</li>
                    <li>关注<strong>用户想要实现的目标</strong></li>
                    <li>通常较为<strong>抽象</strong>，不涉及技术细节</li>
                </ul>
                
                <h4>用户需求示例：</h4>
                <div class="requirement-example user">
                    <p>"我希望能够在线购买商品并追踪订单状态"</p>
                </div>
                <div class="requirement-example user">
                    <p>"我需要一个工具来管理我的日常任务和提醒"</p>
                </div>
                <div class="requirement-example user">
                    <p>"我想要能够在手机上查看我的银行账户余额和交易记录"</p>
                </div>
            </div>
            
            <div class="tab-content" id="tab-system">
                <h3>系统需求的特点</h3>
                <ul>
                    <li>由<strong>开发团队</strong>根据用户需求转化而来</li>
                    <li>使用<strong>技术语言</strong>表达</li>
                    <li>详细描述系统<strong>应该做什么</strong>和<strong>如何做</strong></li>
                    <li>更加<strong>具体</strong>，包含技术细节</li>
                </ul>
                
                <h4>系统需求示例：</h4>
                <div class="requirement-example system">
                    <p>"系统应提供购物车功能，允许用户添加、删除商品，并在结账时计算总价"</p>
                </div>
                <div class="requirement-example system">
                    <p>"系统应实现任务管理模块，支持创建、编辑、删除任务，并允许设置提醒时间"</p>
                </div>
                <div class="requirement-example system">
                    <p>"移动应用应通过安全API连接银行系统，获取并显示用户账户信息和最近30天的交易记录"</p>
                </div>
            </div>
            
            <div class="tab-content" id="tab-constraint">
                <h3>约束条件的特点</h3>
                <ul>
                    <li>限制系统<strong>设计和实现方式</strong>的条件或规则</li>
                    <li>通常由<strong>外部因素</strong>决定，如法律、政策、标准等</li>
                    <li>开发团队<strong>必须遵守</strong>，没有选择的余地</li>
                    <li>不是描述系统功能，而是<strong>限制条件</strong></li>
                </ul>
                
                <h4>约束条件示例：</h4>
                <div class="requirement-example constraint">
                    <p>"系统必须使用国有知识产权的数据库"</p>
                </div>
                <div class="requirement-example constraint">
                    <p>"系统必须符合GDPR数据保护规定"</p>
                </div>
                <div class="requirement-example constraint">
                    <p>"系统必须在现有的硬件基础设施上运行"</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container game-container">
        <div class="game-header">
            <h2>需求分类游戏</h2>
            <p>在这个游戏中，你将看到不同的需求描述，请判断它们属于哪种类型的需求。</p>
        </div>
        
        <div class="project-scenario">
            <div class="project-title">项目背景：在线教育平台</div>
            <div class="project-description">
                你正在参与开发一个在线教育平台，该平台将允许教师创建课程，学生注册学习，并提供各种学习工具和资源。以下是收集到的各种需求，请判断它们的类型。
            </div>
        </div>
        
        <div class="progress-container">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
        
        <div class="score-display">
            得分: <span id="score">0</span>/<span id="total">0</span>
        </div>
        
        <div class="game-area" id="game-area">
            <!-- 游戏卡片将在这里动态生成 -->
        </div>
        
        <div class="game-result" id="game-result">
            <h3>游戏结束！</h3>
            <p>你的最终得分是: <span id="final-score">0</span>/<span id="final-total">0</span></p>
            <p>你已经掌握了如何区分不同类型的需求！</p>
            <button class="next-btn" id="restart-btn">重新开始</button>
        </div>
    </div>

    <div class="container">
        <h2>"在验证录入时对注册款项进行详细的信息录入"是什么类型的需求？</h2>
        
        <div class="explanation">
            <p>"在验证录入时对注册款项进行详细的信息录入"属于<span class="requirement-type user">用户需求</span>，因为：</p>
            <ol>
                <li>它直接描述了<strong>用户希望系统能够提供的功能</strong>（对注册款项进行详细信息录入）</li>
                <li>它表达了<strong>用户想要实现的目标</strong>（在验证录入过程中记录详细信息）</li>
                <li>它是从<strong>用户的角度</strong>描述的，而不是系统实现的技术细节</li>
                <li>它没有限制系统的设计或实现方式，只是表达了功能需求</li>
            </ol>
        </div>
        
        <div class="explanation">
            <h3>需求类型对比：</h3>
            <ul>
                <li><span class="requirement-type user">用户需求</span>："在验证录入时对注册款项进行详细的信息录入"</li>
                <li><span class="requirement-type system">系统需求</span>："系统应提供注册款项详细信息录入表单，包含金额、来源、日期、用途等字段，并在验证过程中强制填写这些信息"</li>
                <li><span class="requirement-type constraint">约束条件</span>："系统必须使用国家认可的财务软件进行款项录入"</li>
            </ul>
        </div>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有标签页的活动状态
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // 设置当前标签页为活动状态
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });
        
        // 游戏数据
        const gameQuestions = [
            {
                id: 1,
                text: "学生希望能够在线观看视频课程并下载学习资料。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "用户需求",
                explanation: "这是学生直接表达的期望和要求，描述了他们希望系统提供的功能，因此属于用户需求。"
            },
            {
                id: 2,
                text: "系统应该实现视频流媒体服务，支持多种格式的视频文件，并提供不同清晰度的选项。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "系统需求",
                explanation: "这是开发团队根据用户需求转化的具体技术实现，包含了技术细节，因此属于系统需求。"
            },
            {
                id: 3,
                text: "平台必须符合教育部关于在线教育的相关规定和标准。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "约束条件",
                explanation: "这是一个外部限制条件，来自于法规和标准，开发团队必须遵守，因此属于约束条件。"
            },
            {
                id: 4,
                text: "教师需要能够创建测验和作业，并自动评分。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "用户需求",
                explanation: "这是教师直接表达的功能需求，描述了他们希望系统能够提供的服务，因此属于用户需求。"
            },
            {
                id: 5,
                text: "系统应实现测验模块，支持多种题型（单选、多选、填空等），并提供自动评分算法。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "系统需求",
                explanation: "这是开发团队根据用户需求转化的具体功能描述，包含了技术实现细节，因此属于系统需求。"
            },
            {
                id: 6,
                text: "平台必须使用SSL加密所有传输的数据。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "约束条件",
                explanation: "这是一个安全性要求，限制了系统的实现方式，属于必须遵守的规则，因此属于约束条件。"
            },
            {
                id: 7,
                text: "学生希望能够在手机上学习课程。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "用户需求",
                explanation: "这是学生直接表达的期望，描述了他们希望系统提供的功能，因此属于用户需求。"
            },
            {
                id: 8,
                text: "系统应开发响应式界面，适配不同尺寸的屏幕，并提供iOS和Android原生应用。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "系统需求",
                explanation: "这是开发团队根据用户需求转化的具体技术实现方案，包含了技术细节，因此属于系统需求。"
            },
            {
                id: 9,
                text: "系统必须能够在高峰时段支持至少10,000名并发用户。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "约束条件",
                explanation: "这是一个性能限制条件，规定了系统必须满足的性能标准，因此属于约束条件。"
            },
            {
                id: 10,
                text: "在验证录入时对注册款项进行详细的信息录入。",
                options: ["用户需求", "系统需求", "约束条件"],
                correctAnswer: "用户需求",
                explanation: "这是用户直接表达的功能需求，描述了他们希望系统提供的功能（详细信息录入），因此属于用户需求。"
            }
        ];
        
        // 游戏状态
        let currentQuestionIndex = 0;
        let score = 0;
        let selectedAnswer = null;
        
        // 游戏元素
        const gameArea = document.getElementById('game-area');
        const progressBar = document.getElementById('progress-bar');
        const scoreDisplay = document.getElementById('score');
        const totalDisplay = document.getElementById('total');
        const gameResult = document.getElementById('game-result');
        const finalScore = document.getElementById('final-score');
        const finalTotal = document.getElementById('final-total');
        const restartBtn = document.getElementById('restart-btn');
        
        // 初始化游戏
        function initGame() {
            currentQuestionIndex = 0;
            score = 0;
            selectedAnswer = null;
            
            totalDisplay.textContent = gameQuestions.length;
            scoreDisplay.textContent = score;
            
            showQuestion(currentQuestionIndex);
            updateProgress();
            
            gameResult.style.display = 'none';
        }
        
        // 显示问题
        function showQuestion(index) {
            const question = gameQuestions[index];
            
            gameArea.innerHTML = `
                <div class="game-card">
                    <div class="card-header">问题 ${index + 1}/${gameQuestions.length}</div>
                    <div class="card-content">${question.text}</div>
                    <div class="card-options">
                        ${question.options.map((option, i) => `
                            <button class="option-btn" data-option="${option}">${option}</button>
                        `).join('')}
                    </div>
                    <div class="feedback" id="question-feedback"></div>
                    <button class="check-btn" id="check-btn" disabled>检查答案</button>
                    <button class="next-btn" id="next-btn" style="display: none;">下一题</button>
                </div>
            `;
            
            // 添加选项点击事件
            document.querySelectorAll('.option-btn').forEach(button => {
                button.addEventListener('click', function() {
                    document.querySelectorAll('.option-btn').forEach(btn => {
                        btn.classList.remove('selected');
                    });
                    this.classList.add('selected');
                    selectedAnswer = this.dataset.option;
                    document.getElementById('check-btn').disabled = false;
                });
            });
            
            // 添加检查答案事件
            document.getElementById('check-btn').addEventListener('click', function() {
                checkAnswer();
            });
            
            // 添加下一题事件
            document.getElementById('next-btn').addEventListener('click', function() {
                nextQuestion();
            });
        }
        
        // 检查答案
        function checkAnswer() {
            const question = gameQuestions[currentQuestionIndex];
            const feedback = document.getElementById('question-feedback');
            const checkBtn = document.getElementById('check-btn');
            const nextBtn = document.getElementById('next-btn');
            
            feedback.style.display = 'block';
            
            if (selectedAnswer === question.correctAnswer) {
                feedback.className = 'feedback correct';
                feedback.innerHTML = `<p>✓ 正确！</p><p>${question.explanation}</p>`;
                score++;
                scoreDisplay.textContent = score;
            } else {
                feedback.className = 'feedback incorrect';
                feedback.innerHTML = `<p>✗ 不正确。正确答案是：${question.correctAnswer}</p><p>${question.explanation}</p>`;
            }
            
            checkBtn.style.display = 'none';
            nextBtn.style.display = 'block';
        }
        
        // 下一题
        function nextQuestion() {
            currentQuestionIndex++;
            selectedAnswer = null;
            
            if (currentQuestionIndex < gameQuestions.length) {
                showQuestion(currentQuestionIndex);
                updateProgress();
            } else {
                endGame();
            }
        }
        
        // 更新进度条
        function updateProgress() {
            const progress = ((currentQuestionIndex + 1) / gameQuestions.length) * 100;
            progressBar.style.width = `${progress}%`;
        }
        
        // 结束游戏
        function endGame() {
            gameArea.innerHTML = '';
            gameResult.style.display = 'block';
            finalScore.textContent = score;
            finalTotal.textContent = gameQuestions.length;
        }
        
        // 重新开始游戏
        restartBtn.addEventListener('click', initGame);
        
        // 初始化游戏
        initGame();
    </script>

</body>
</html> 