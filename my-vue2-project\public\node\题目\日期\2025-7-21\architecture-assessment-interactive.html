<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>架构评估学习 - 敏感点与权衡点</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .concept-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .concept-card.active {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .concept-title {
            font-size: 1.8rem;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .concept-description {
            font-size: 1.1rem;
            line-height: 1.6;
            opacity: 0.9;
        }

        .animation-canvas {
            width: 100%;
            height: 400px;
            border-radius: 15px;
            background: #f8f9fa;
            margin: 30px 0;
            border: 2px solid #e9ecef;
        }

        .quiz-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
            color: #333;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }

        .option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .option.correct {
            border-color: #28a745;
            background: #28a745;
            color: white;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #dc3545;
            color: white;
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: fadeIn 0.5s ease-out;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .floating-element {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="floating-element" style="top: 10%; left: 10%; animation-delay: 0s;"></div>
    <div class="floating-element" style="top: 20%; right: 15%; animation-delay: 2s;"></div>
    <div class="floating-element" style="bottom: 30%; left: 20%; animation-delay: 4s;"></div>

    <div class="container">
        <div class="header">
            <h1 class="title">架构评估学习</h1>
            <p class="subtitle">敏感点与权衡点的交互式探索</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="learning-section">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">核心概念理解</h2>
            
            <div class="concept-card" id="sensitivityCard" onclick="showConcept('sensitivity')">
                <div class="concept-title">🎯 敏感点 (Sensitivity Point)</div>
                <div class="concept-description">
                    一个或多个构件（和/或构件之间的关系）的特性，影响单一质量属性
                </div>
            </div>

            <div class="concept-card" id="tradeoffCard" onclick="showConcept('tradeoff')">
                <div class="concept-title">⚖️ 权衡点 (Tradeoff Point)</div>
                <div class="concept-description">
                    影响多个质量属性的特性，是多个质量属性的敏感点
                </div>
            </div>

            <canvas class="animation-canvas" id="animationCanvas"></canvas>
            
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn" onclick="startAnimation()">🎬 开始动画演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重置演示</button>
            </div>
        </div>

        <div class="quiz-section">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">实战练习</h2>
            
            <div class="question">
                在架构评估中，<strong>（ ）</strong>是一个或多个构件(和或构件之间的关系)的特性。改变加密级别的设计决策属于<strong>（ ）</strong>，因为它可能会对安全性和性能产生非常重要的影响。
            </div>

            <div class="options">
                <div class="option" onclick="selectOption(this, 'A')">
                    <strong>A.</strong> 敏感点
                </div>
                <div class="option" onclick="selectOption(this, 'B')">
                    <strong>B.</strong> 非风险点
                </div>
                <div class="option" onclick="selectOption(this, 'C')">
                    <strong>C.</strong> 权衡点
                </div>
                <div class="option" onclick="selectOption(this, 'D')">
                    <strong>D.</strong> 风险点
                </div>
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="checkAnswer()" id="checkBtn">检查答案</button>
                <button class="btn" onclick="showExplanation()" id="explainBtn" style="display: none;">查看解析</button>
            </div>

            <div class="explanation" id="explanation">
                <h3 style="color: #667eea; margin-bottom: 15px;">📚 详细解析</h3>
                <p><strong>正确答案：A (敏感点)</strong></p>
                <br>
                <p><strong>🎯 敏感点 (Sensitivity Point)：</strong></p>
                <p>• 定义：一个或多个构件（和/或构件之间的关系）的特性</p>
                <p>• 作用：帮助设计人员明确在实现质量目标时应注意什么</p>
                <br>
                <p><strong>⚖️ 权衡点 (Tradeoff Point)：</strong></p>
                <p>• 定义：影响多个质量属性的特性，是多个质量属性的敏感点</p>
                <p>• 特点：需要在不同质量属性之间进行平衡</p>
                <br>
                <p><strong>🔐 加密级别示例：</strong></p>
                <p>• 提高加密级别 → 增强安全性 ✅</p>
                <p>• 提高加密级别 → 增加处理时间，影响性能 ⚠️</p>
                <p>• 因此，加密级别是一个<strong>权衡点</strong>，需要在安全性和性能之间找到平衡</p>
            </div>
        </div>
    </div>

    <script>
        let currentConcept = null;
        let animationRunning = false;
        let selectedAnswer = null;

        // 画布相关变量
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        // 动画对象
        let components = [];
        let arrows = [];
        let particles = [];

        function showConcept(type) {
            // 重置所有卡片
            document.querySelectorAll('.concept-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // 激活选中的卡片
            const cardId = type === 'sensitivity' ? 'sensitivityCard' : 'tradeoffCard';
            document.getElementById(cardId).classList.add('active');
            
            currentConcept = type;
            updateProgress(25);
            
            // 清空画布并绘制对应概念
            clearCanvas();
            if (type === 'sensitivity') {
                drawSensitivityPoint();
            } else {
                drawTradeoffPoint();
            }
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            components = [];
            arrows = [];
            particles = [];
        }

        function drawSensitivityPoint() {
            // 绘制敏感点示例
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 绘制组件
            drawComponent(centerX - 100, centerY - 50, '加密模块', '#4facfe');
            drawComponent(centerX + 100, centerY - 50, '安全性', '#00f2fe');
            
            // 绘制箭头
            drawArrow(centerX - 50, centerY - 40, centerX + 50, centerY - 40, '#667eea');
            
            // 添加文字说明
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('敏感点：影响单一质量属性', centerX, centerY + 80);
        }

        function drawTradeoffPoint() {
            // 绘制权衡点示例
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 绘制中心组件
            drawComponent(centerX, centerY - 80, '加密级别', '#f093fb');
            
            // 绘制多个质量属性
            drawComponent(centerX - 120, centerY + 40, '安全性', '#28a745');
            drawComponent(centerX + 120, centerY + 40, '性能', '#dc3545');
            
            // 绘制箭头
            drawArrow(centerX - 30, centerY - 40, centerX - 90, centerY + 20, '#28a745');
            drawArrow(centerX + 30, centerY - 40, centerX + 90, centerY + 20, '#dc3545');
            
            // 添加文字说明
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('权衡点：影响多个质量属性', centerX, centerY + 120);
        }

        function drawComponent(x, y, text, color) {
            // 绘制圆角矩形
            const width = 80;
            const height = 40;
            const radius = 10;
            
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.roundRect(x - width/2, y - height/2, width, height, radius);
            ctx.fill();
            
            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 5);
        }

        function drawArrow(x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 绘制箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            const headLength = 15;
            
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - headLength * Math.cos(angle - Math.PI/6), y2 - headLength * Math.sin(angle - Math.PI/6));
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - headLength * Math.cos(angle + Math.PI/6), y2 - headLength * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function startAnimation() {
            if (animationRunning) return;
            
            animationRunning = true;
            updateProgress(50);
            
            // 创建动画粒子效果
            createParticles();
            animateParticles();
        }

        function createParticles() {
            particles = [];
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: Math.random() * 5 + 2,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`,
                    life: 1
                });
            }
        }

        function animateParticles() {
            if (!animationRunning) return;
            
            clearCanvas();
            
            // 重绘当前概念
            if (currentConcept === 'sensitivity') {
                drawSensitivityPoint();
            } else if (currentConcept === 'tradeoff') {
                drawTradeoffPoint();
            }
            
            // 绘制粒子
            particles.forEach((particle, index) => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life -= 0.01;
                
                if (particle.life <= 0) {
                    particles.splice(index, 1);
                    return;
                }
                
                ctx.globalAlpha = particle.life;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;
            });
            
            if (particles.length > 0) {
                requestAnimationFrame(animateParticles);
            } else {
                animationRunning = false;
            }
        }

        function resetAnimation() {
            animationRunning = false;
            particles = [];
            clearCanvas();
            if (currentConcept) {
                showConcept(currentConcept);
            }
        }

        function selectOption(element, answer) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // 选中当前选项
            element.classList.add('selected');
            selectedAnswer = answer;
            updateProgress(75);
        }

        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }
            
            const options = document.querySelectorAll('.option');
            
            options.forEach((option, index) => {
                const letter = ['A', 'B', 'C', 'D'][index];
                if (letter === 'A') {
                    option.classList.add('correct');
                } else if (letter === selectedAnswer && selectedAnswer !== 'A') {
                    option.classList.add('wrong');
                }
            });
            
            document.getElementById('checkBtn').style.display = 'none';
            document.getElementById('explainBtn').style.display = 'inline-block';
            updateProgress(90);
        }

        function showExplanation() {
            document.getElementById('explanation').classList.add('show');
            updateProgress(100);
        }

        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // 添加圆角矩形支持
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
            };
        }

        // 初始化
        window.addEventListener('load', () => {
            updateProgress(10);
        });
    </script>
</body>
</html>
