<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ANSI/IEEE 1471-2000 软件架构标准 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .game-board {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .canvas-container {
            position: relative;
            margin: 30px 0;
            text-align: center;
        }

        #architectureCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #architectureCanvas:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            font-size: 1.1rem;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .option-card.correct {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            animation: pulse 0.6s ease-in-out;
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            animation: shake 0.6s ease-in-out;
        }

        .explanation {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #4facfe;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .concept-item {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .concept-item:hover {
            transform: translateX(10px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .concept-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .concept-desc {
            color: #666;
            line-height: 1.5;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 1s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .floating-element {
            position: absolute;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .next-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .next-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">ANSI/IEEE 1471-2000</h1>
            <p class="subtitle">软件架构标准 - 交互式学习体验</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <h2 class="question-title">
                    ANSI/IEEE 1471-2000是对软件密集型系统的架构进行描述的标准。在该标准中，（ ）这一概念主要用于描述软件架构模型。
                </h2>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <div class="canvas-container">
                    <canvas id="architectureCanvas" width="800" height="400"></canvas>
                </div>

                <div class="options-container">
                    <button class="option-card" data-option="A">A. 架构</button>
                    <button class="option-card" data-option="B">B. 系统</button>
                    <button class="option-card" data-option="C">C. 模型</button>
                    <button class="option-card" data-option="D">D. 使命</button>
                </div>

                <div class="explanation" id="explanation">
                    <h3 style="color: #333; margin-bottom: 20px;">📚 知识解析</h3>
                    <div class="concept-item">
                        <div class="concept-title">🏗️ 架构 (Architecture)</div>
                        <div class="concept-desc">是对所有利益相关人关注点的响应和回答，通过架构描述来说明</div>
                    </div>
                    <div class="concept-item">
                        <div class="concept-title">🎯 系统 (System)</div>
                        <div class="concept-desc">为了达成利益相关人的某些使命，在特定环境中构建的</div>
                    </div>
                    <div class="concept-item">
                        <div class="concept-title">📋 模型 (Model)</div>
                        <div class="concept-desc">用于可视化、检查、分析、管理和集成架构的表述方式</div>
                    </div>
                    <div class="concept-item">
                        <div class="concept-title">🚀 使命 (Mission)</div>
                        <div class="concept-desc">利益相关人希望系统达成的目标</div>
                    </div>
                    <button class="next-button" onclick="nextQuestion()">继续学习 →</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');
        let animationFrame = 0;
        let selectedAnswer = null;
        let showingExplanation = false;

        // 动画元素
        const elements = [
            { x: 100, y: 100, type: 'stakeholder', label: '利益相关人', color: '#ff6b6b' },
            { x: 300, y: 80, type: 'mission', label: '使命', color: '#4ecdc4' },
            { x: 500, y: 120, type: 'system', label: '系统', color: '#45b7d1' },
            { x: 700, y: 100, type: 'architecture', label: '架构', color: '#96ceb4' },
            { x: 200, y: 250, type: 'concern', label: '关注点', color: '#feca57' },
            { x: 400, y: 280, type: 'view', label: '视图', color: '#ff9ff3' },
            { x: 600, y: 260, type: 'model', label: '模型', color: '#54a0ff' }
        ];

        function drawElement(element, highlight = false) {
            const radius = highlight ? 35 : 30;
            const alpha = highlight ? 1 : 0.8;
            
            ctx.save();
            ctx.globalAlpha = alpha;
            
            // 绘制圆形背景
            ctx.beginPath();
            ctx.arc(element.x, element.y, radius, 0, Math.PI * 2);
            ctx.fillStyle = element.color;
            ctx.fill();
            
            // 绘制边框
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(element.label, element.x, element.y);
            
            ctx.restore();
        }

        function drawConnection(from, to, animated = false) {
            ctx.save();
            ctx.strokeStyle = animated ? '#667eea' : '#ddd';
            ctx.lineWidth = animated ? 3 : 2;
            ctx.setLineDash(animated ? [5, 5] : []);
            
            if (animated) {
                ctx.lineDashOffset = -animationFrame * 0.5;
            }
            
            ctx.beginPath();
            ctx.moveTo(from.x, from.y);
            ctx.lineTo(to.x, to.y);
            ctx.stroke();
            ctx.restore();
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            animationFrame++;
            
            // 绘制连接线
            drawConnection(elements[0], elements[1]); // 利益相关人 -> 使命
            drawConnection(elements[1], elements[2]); // 使命 -> 系统
            drawConnection(elements[2], elements[3], true); // 系统 -> 架构 (动画)
            drawConnection(elements[0], elements[4]); // 利益相关人 -> 关注点
            drawConnection(elements[4], elements[5]); // 关注点 -> 视图
            drawConnection(elements[5], elements[6]); // 视图 -> 模型
            
            // 绘制元素
            elements.forEach((element, index) => {
                const highlight = (animationFrame % 120) > 60 && element.type === 'architecture';
                drawElement(element, highlight);
            });
            
            requestAnimationFrame(animate);
        }

        // 选项点击处理
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('click', function() {
                if (selectedAnswer) return;
                
                selectedAnswer = this.dataset.option;
                const isCorrect = selectedAnswer === 'A';
                
                // 更新进度条
                document.getElementById('progressFill').style.width = '100%';
                
                // 标记答案
                this.classList.add(isCorrect ? 'correct' : 'wrong');
                
                // 显示正确答案
                if (!isCorrect) {
                    document.querySelector('[data-option="A"]').classList.add('correct');
                }
                
                // 延迟显示解析
                setTimeout(() => {
                    document.getElementById('explanation').classList.add('show');
                    showingExplanation = true;
                }, 1000);
            });
        });

        function nextQuestion() {
            alert('恭喜完成第一题！\n\n正确答案是 A. 架构\n\n在ANSI/IEEE 1471-2000标准中，架构是核心概念，用于描述软件架构模型，是对所有利益相关人关注点的响应和回答。');
        }

        // 画布点击交互
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            elements.forEach(element => {
                const distance = Math.sqrt((x - element.x) ** 2 + (y - element.y) ** 2);
                if (distance < 30) {
                    showElementInfo(element);
                }
            });
        });

        function showElementInfo(element) {
            const info = {
                'stakeholder': '利益相关人：对系统开发、运营或其他方面有利益的人',
                'mission': '使命：利益相关人希望系统达成的目标',
                'system': '系统：为达成使命而在特定环境中构建的',
                'architecture': '架构：对所有利益相关人关注点的响应和回答',
                'concern': '关注点：对利益相关人重要的系统相关利益',
                'view': '视图：从特定视角表述架构的某个方面',
                'model': '模型：用于可视化、检查、分析的架构表述'
            };
            
            alert(info[element.type] || '点击了未知元素');
        }

        // 启动动画
        animate();
        
        // 初始化进度条动画
        setTimeout(() => {
            document.getElementById('progressFill').style.width = '25%';
        }, 500);
    </script>
</body>
</html>
