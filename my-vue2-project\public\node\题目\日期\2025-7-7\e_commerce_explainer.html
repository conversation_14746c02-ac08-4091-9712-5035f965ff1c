<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子商务知识讲解与互动</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .question-section, .answer-section, .explanation-section, .demo-section {
            margin-bottom: 30px;
        }
        .question-text {
            font-size: 1.2em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .options div {
            margin-bottom: 10px;
        }
        .options label {
            display: block;
            background-color: #e8f0fe;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .options label:hover {
            background-color: #d0e4ff;
        }
        .options input[type="radio"] {
            margin-right: 10px;
        }
        .feedback {
            margin-top: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            font-weight: bold;
            display: none; /* Hidden by default */
        }
        .feedback.correct {
            background-color: #e6ffe6;
            color: #28a745;
            border: 1px solid #28a745;
        }
        .feedback.incorrect {
            background-color: #ffe6e6;
            color: #dc3545;
            border: 1px solid #dc3545;
        }

        .explanation-points p {
            margin-bottom: 15px;
        }

        .demo-buttons button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        .demo-buttons button:hover {
            background-color: #0056b3;
        }
        .canvas-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
            overflow: hidden; /* For animations that might briefly go out of bounds */
        }
        canvas {
            display: block;
            background-color: #fff;
        }
        .demo-explanation {
            margin-top: 15px;
            padding: 10px;
            background-color: #f0f8ff;
            border-left: 5px solid #add8e6;
            border-radius: 0 5px 5px 0;
            font-style: italic;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>电子商务知识讲解与互动页面</h1>

        <div class="question-section">
            <h2>题目回顾</h2>
            <p class="question-text">
                随着互联网的普及，电子商务已经进入人们日常生活，下列 (<span id="question-blank"></span>) 业务全部属于电子商务的范畴。
            </p>
            <div class="options">
                <div>① 网上客服</div>
                <div>② 电视购物</div>
                <div>③ 网上营销</div>
                <div>④ 电话交易</div>
                <div>⑤ 商场广播</div>
                <div>⑥ 网上调查</div>
            </div>

            <h3>选择您的答案:</h3>
            <div class="answer-options">
                <div>
                    <label>
                        <input type="radio" name="answer" value="A"> A: ①②③④⑤⑥
                    </label>
                </div>
                <div>
                    <label>
                        <input type="radio" name="answer" value="B"> B: ①②③④⑤
                    </label>
                </div>
                <div>
                    <label>
                        <input type="radio" name="answer" value="C"> C: ①②③④⑥
                    </label>
                </div>
                <div>
                    <label>
                        <input type="radio" name="answer" value="D"> D: ①③④⑤⑥
                    </label>
                </div>
            </div>
            <button id="submit-answer">提交答案</button>
            <div id="feedback" class="feedback"></div>
        </div>

        <div class="explanation-section">
            <h2>题目知识讲解：什么是电子商务？</h2>
            <p>
                电子商务（E-commerce）是指在互联网等电子媒介上进行商务活动。它不仅仅是网上购物，还包括了商品或服务的推广、支付、物流、客户服务以及市场调研等一系列环节。理解电子商务，关键在于它利用"电子化"的手段来促进和完成商业交易。
            </p>
            <h3>选项解析与电子商务范畴：</h3>
            <div class="explanation-points">
                <p><strong>① 网上客服：</strong> 这是电子商务中服务环节的重要组成部分。通过在线聊天、邮件、社交媒体等方式为顾客提供咨询、解答疑问、处理售后等服务，直接影响购物体验和交易完成。</p>
                <p><strong>② 电视购物：</strong> 虽然不完全依赖互联网，但它通过电视这一"电子媒介"进行商品展示和推广，消费者通过电话或网络下单完成交易。这是一种典型的电子化商业模式。</p>
                <p><strong>③ 网上营销：</strong> 电子商务的核心环节之一。通过搜索引擎优化（SEO）、社交媒体广告、电子邮件营销、内容营销等多种在线渠道吸引顾客、推广商品，是促进在线交易的重要手段。</p>
                <p><strong>④ 电话交易：</strong> 消费者通过电话订购商品或服务，商家通过电话确认订单和支付信息。这是一种传统的电子化交易方式，常见于电视购物或在线订单的辅助完成。</p>
                <p><strong>⑤ 商场广播：</strong> 这看起来似乎与"电子"和"商务"关系不大，但如果从更广义的电子商务生态系统来看，特别是O2O（线上到线下）模式下，商场广播可以用来引导顾客关注商场的线上店铺、参与线上活动或使用手机支付。它通过电子媒介（广播系统）对商业活动进行宣传和辅助，促进了整体商业流转的"电子化"。因此，可以纳入其范畴。</p>
                <p><strong>⑥ 网上调查：</strong> 虽然不是直接的交易行为，但网上调查是电子商务企业进行市场研究、收集用户反馈、改进产品和服务、优化营销策略的重要工具。它是电子商务生态系统中的关键数据收集和分析环节，间接促进了商业活动的优化和完成。</p>
            </div>
        </div>

        <div class="demo-section">
            <h2>交互演示：电子商务环节动画</h2>
            <p>点击下方按钮，观看各电子商务环节的动画演示和简要说明。</p>
            <div class="demo-buttons">
                <button data-concept="online_customer_service">网上客服</button>
                <button data-concept="tv_shopping">电视购物</button>
                <button data-concept="online_marketing">网上营销</button>
                <button data-concept="phone_transaction">电话交易</button>
                <button data-concept="mall_broadcasting">商场广播</button>
                <button data-concept="online_survey">网上调查</button>
            </div>
            <div class="canvas-container">
                <canvas id="eCommerceCanvas" width="600" height="300"></canvas>
            </div>
            <div id="demo-explanation-text" class="demo-explanation hidden"></div>
        </div>
    </div>

    <script>
        const submitButton = document.getElementById('submit-answer');
        const feedbackDiv = document.getElementById('feedback');
        const canvas = document.getElementById('eCommerceCanvas');
        const ctx = canvas.getContext('2d');
        const demoExplanationText = document.getElementById('demo-explanation-text');
        const questionBlank = document.getElementById('question-blank');

        // Initial content for the blank in the question
        questionBlank.textContent = '()';

        submitButton.addEventListener('click', () => {
            const selectedAnswer = document.querySelector('input[name="answer"]:checked');
            if (selectedAnswer) {
                if (selectedAnswer.value === 'A') {
                    feedbackDiv.textContent = '恭喜您，回答正确！';
                    feedbackDiv.className = 'feedback correct';
                } else {
                    feedbackDiv.textContent = '抱歉，回答错误。正确答案是 A: ①②③④⑤⑥。请查看下方的知识讲解和演示。';
                    feedbackDiv.className = 'feedback incorrect';
                }
                feedbackDiv.style.display = 'block';
            } else {
                feedbackDiv.textContent = '请选择一个答案。';
                feedbackDiv.className = 'feedback incorrect';
                feedbackDiv.style.display = 'block';
            }
        });

        // Animation logic
        let animationFrameId;

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawText(text, x, y, color = '#333', font = '20px Arial') {
            ctx.fillStyle = color;
            ctx.font = font;
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        function drawRect(x, y, width, height, color = '#007bff') {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
        }

        function drawCircle(x, y, radius, color = '#007bff') {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawRoundedRect(x, y, width, height, radius, color = '#007bff') {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
            ctx.fill();
        }

        // Animation functions for each concept
        function animateOnlineCustomerService() {
            let frame = 0;
            const maxFrames = 100;
            const chatBubbleX = canvas.width / 2;
            const chatBubbleY = canvas.height / 2 - 30;
            const messageInterval = 20; // frames per message

            const messages = [
                "您好，有什么可以帮您？",
                "这个商品有货吗？",
                "有的，请问您需要什么尺码？",
                "好的，谢谢！"
            ];
            let currentMessageIndex = 0;
            let messageTimer = 0;

            function drawCustomerService() {
                clearCanvas();
                // Draw a simple person icon (customer)
                drawCircle(chatBubbleX - 100, chatBubbleY + 80, 20, '#5cb85c'); // Head
                drawRect(chatBubbleX - 110, chatBubbleY + 100, 40, 60, '#5cb85c'); // Body

                // Draw a simple person icon (service agent)
                drawCircle(chatBubbleX + 100, chatBubbleY + 80, 20, '#f0ad4e'); // Head
                drawRect(chatBubbleX + 90, chatBubbleY + 100, 40, 60, '#f0ad4e'); // Body

                // Draw chat bubbles
                ctx.fillStyle = '#e8f0fe';
                ctx.strokeStyle = '#add8e6';
                ctx.lineWidth = 2;

                // Bubbles for customer (left)
                if (currentMessageIndex >= 1) {
                    drawRoundedRect(chatBubbleX - 250, chatBubbleY, 140, 40, 10, '#e8f0fe');
                    ctx.fillStyle = '#333';
                    drawText(messages[1], chatBubbleX - 180, chatBubbleY + 25, '#333', '14px Arial');
                }
                 if (currentMessageIndex >= 3) {
                    drawRoundedRect(chatBubbleX - 250, chatBubbleY + 60, 140, 40, 10, '#e8f0fe');
                    ctx.fillStyle = '#333';
                    drawText(messages[3], chatBubbleX - 180, chatBubbleY + 85, '#333', '14px Arial');
                }


                // Bubbles for agent (right)
                if (currentMessageIndex >= 0) {
                    drawRoundedRect(chatBubbleX + 110, chatBubbleY, 180, 40, 10, '#d0e4ff');
                    ctx.fillStyle = '#333';
                    drawText(messages[0], chatBubbleX + 200, chatBubbleY + 25, '#333', '14px Arial');
                }

                if (currentMessageIndex >= 2) {
                    drawRoundedRect(chatBubbleX + 110, chatBubbleY + 60, 180, 40, 10, '#d0e4ff');
                    ctx.fillStyle = '#333';
                    drawText(messages[2], chatBubbleX + 200, chatBubbleY + 85, '#333', '14px Arial');
                }


                messageTimer++;
                if (messageTimer >= messageInterval && currentMessageIndex < messages.length -1) {
                    currentMessageIndex++;
                    messageTimer = 0;
                }

                if (frame < maxFrames) {
                    animationFrameId = requestAnimationFrame(drawCustomerService);
                    frame++;
                } else {
                     demoExplanationText.textContent = '网上客服是电子商务中不可或缺的服务环节，通过在线沟通为顾客提供支持和解答疑问。';
                     demoExplanationText.classList.remove('hidden');
                }
            }
            cancelAnimationFrame(animationFrameId); // Stop any previous animation
            drawCustomerService();
        }

        function animateTvShopping() {
            let frame = 0;
            const maxFrames = 150;
            const tvX = canvas.width / 2 - 120;
            const tvY = canvas.height / 2 - 80;
            const tvWidth = 240;
            const tvHeight = 160;

            function drawTvShopping() {
                clearCanvas();
                // Draw TV frame
                drawRoundedRect(tvX, tvY, tvWidth, tvHeight, 15, '#333'); // TV body
                drawRect(tvX + tvWidth / 2 - 20, tvY + tvHeight, 40, 20, '#555'); // Stand

                // Draw screen
                drawRect(tvX + 10, tvY + 10, tvWidth - 20, tvHeight - 20, '#cfe2f3');

                // Simulate product display
                let currentItemY = tvY + 50 + (frame % 60 < 30 ? 0 : 5); // Simple bobbing effect
                drawText('热卖商品!', tvX + tvWidth / 2, tvY + 50, '#2c3e50', '24px Arial');
                drawRect(tvX + tvWidth / 2 - 40, currentItemY + 20, 80, 60, '#f0ad4e'); // Product placeholder
                drawText('$99.99', tvX + tvWidth / 2, tvY + tvHeight - 30, '#dc3545', '20px Arial');

                // Simulate phone call instruction
                if (frame > 50) {
                     drawCircle(tvX + tvWidth / 2 + 100, tvY + tvHeight + 50, 20, '#28a745');
                     ctx.fillStyle = 'white';
                     drawText('📞', tvX + tvWidth / 2 + 100, tvY + tvHeight + 58, 'white', '20px Arial');
                     drawText('拨打 ************ 订购！', tvX + tvWidth / 2 + 100, tvY + tvHeight + 90, '#28a745', '16px Arial');
                }


                if (frame < maxFrames) {
                    animationFrameId = requestAnimationFrame(drawTvShopping);
                    frame++;
                } else {
                     demoExplanationText.textContent = '电视购物通过电视展示商品，消费者通过电话或网络下单，是一种重要的电子化交易形式。';
                     demoExplanationText.classList.remove('hidden');
                }
            }
            cancelAnimationFrame(animationFrameId);
            drawTvShopping();
        }

        function animateOnlineMarketing() {
            let frame = 0;
            const maxFrames = 150;
            const center = { x: canvas.width / 2, y: canvas.height / 2 };

            function drawOnlineMarketing() {
                clearCanvas();

                // Draw a simple website/product icon
                drawRoundedRect(center.x - 50, center.y - 50, 100, 100, 10, '#add8e6');
                drawText('您的网站/产品', center.x, center.y, '#333', '14px Arial');

                // Simulate different marketing channels
                const channels = [
                    { name: '搜索引擎', emoji: '🔍' },
                    { name: '社交媒体', emoji: '📱' },
                    { name: '邮件推广', emoji: '✉️' }
                ];

                channels.forEach((channel, index) => {
                    let angle = (Math.PI * 2 / channels.length) * index - Math.PI / 2;
                    let radius = 80;
                    if (frame > 30 * index) { // Staggered appearance
                        radius = 80 + Math.sin(frame * 0.1) * 5; // Slight animation
                        let channelX = center.x + radius * Math.cos(angle);
                        let channelY = center.y + radius * Math.sin(angle);

                        drawCircle(channelX, channelY, 25, '#d0e4ff');
                        drawText(channel.emoji, channelX, channelY + 8, '#333', '24px Arial');
                        drawText(channel.name, channelX, channelY + 40, '#333', '14px Arial');

                        // Draw arrow towards product
                        ctx.strokeStyle = '#007bff';
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(channelX, channelY);
                        ctx.lineTo(center.x, center.y);
                        ctx.stroke();
                    }
                });


                if (frame < maxFrames) {
                    animationFrameId = requestAnimationFrame(drawOnlineMarketing);
                    frame++;
                } else {
                     demoExplanationText.textContent = '网上营销是电子商务吸引顾客的关键，通过各种在线渠道（如搜索引擎、社交媒体）推广产品和服务。';
                     demoExplanationText.classList.remove('hidden');
                }
            }
            cancelAnimationFrame(animationFrameId);
            drawOnlineMarketing();
        }

        function animatePhoneTransaction() {
            let frame = 0;
            const maxFrames = 100;
            const phoneX = canvas.width / 2;
            const phoneY = canvas.height / 2;

            function drawPhoneTransaction() {
                clearCanvas();
                // Draw a simple phone icon
                drawRoundedRect(phoneX - 50, phoneY - 80, 100, 160, 15, '#333'); // Phone body
                drawRect(phoneX - 40, phoneY - 60, 80, 100, '#666'); // Screen
                drawCircle(phoneX, phoneY + 50, 10, '#aaa'); // Home button

                // Simulate call animation
                if (frame % 30 < 15) {
                    ctx.strokeStyle = '#28a745';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.arc(phoneX, phoneY - 40, 30, 0, Math.PI * 2);
                    ctx.stroke();
                }
                drawText('📞', phoneX, phoneY - 30, '#fff', '40px Arial');
                drawText('交易进行中...', phoneX, phoneY + 20, '#fff', '18px Arial');


                if (frame < maxFrames) {
                    animationFrameId = requestAnimationFrame(drawPhoneTransaction);
                    frame++;
                } else {
                     demoExplanationText.textContent = '电话交易是消费者通过电话完成商品或服务订购的方式，是电子商务的辅助或独立形式。';
                     demoExplanationText.classList.remove('hidden');
                }
            }
            cancelAnimationFrame(animationFrameId);
            drawPhoneTransaction();
        }

        function animateMallBroadcasting() {
            let frame = 0;
            const maxFrames = 120;
            const speakerX = canvas.width / 2;
            const speakerY = canvas.height / 2;

            function drawMallBroadcasting() {
                clearCanvas();
                // Draw a simple speaker icon
                drawRect(speakerX - 50, speakerY - 50, 100, 100, '#607d8b'); // Speaker body
                drawCircle(speakerX, speakerY, 30, '#455a64'); // Cone

                // Simulate sound waves
                const waveCount = 3;
                for (let i = 0; i < waveCount; i++) {
                    let radius = 50 + (frame * 1.5 + i * 20) % 70;
                    let opacity = 1 - (radius - 50) / 70;
                    if (opacity > 0) {
                        ctx.strokeStyle = `rgba(0, 123, 255, ${opacity})`;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.arc(speakerX, speakerY, radius, 0, Math.PI * 2);
                        ctx.stroke();
                    }
                }

                drawText('欢迎光临，今日优惠！', speakerX, speakerY + 80, '#007bff', '20px Arial');
                drawText('扫描二维码，线上享更多折扣！', speakerX, speakerY + 110, '#007bff', '16px Arial');


                if (frame < maxFrames) {
                    animationFrameId = requestAnimationFrame(drawMallBroadcasting);
                    frame++;
                } else {
                     demoExplanationText.textContent = '商场广播在O2O模式下，可用于引导顾客至线上平台，是辅助电子商务的重要线下电子化手段。';
                     demoExplanationText.classList.remove('hidden');
                }
            }
            cancelAnimationFrame(animationFrameId);
            drawMallBroadcasting();
        }

        function animateOnlineSurvey() {
            let frame = 0;
            const maxFrames = 100;
            const formX = canvas.width / 2 - 150;
            const formY = canvas.height / 2 - 80;
            const formWidth = 300;
            const formHeight = 200;

            function drawOnlineSurvey() {
                clearCanvas();
                // Draw a simple survey form
                drawRoundedRect(formX, formY, formWidth, formHeight, 10, '#f8f9fa');
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.strokeRect(formX, formY, formWidth, formHeight);

                drawText('满意度调查', formX + formWidth / 2, formY + 30, '#2c3e50', '22px Arial');

                // Simulate questions and radio buttons
                drawRect(formX + 30, formY + 60, formWidth - 60, 20, '#e9ecef'); // Question 1 background
                drawText('您对本次购物满意吗？', formX + formWidth / 2, formY + 75, '#555', '14px Arial');

                // Radio buttons
                drawCircle(formX + 60, formY + 100, 8, '#bbb');
                drawText('满意', formX + 90, formY + 105, '#333', '14px Arial');
                drawCircle(formX + 160, formY + 100, 8, '#bbb');
                drawText('一般', formX + 190, formY + 105, '#333', '14px Arial');
                drawCircle(formX + 260, formY + 100, 8, '#bbb');
                drawText('不满意', formX + 290, formY + 105, '#333', '14px Arial');

                // Simulate filling
                if (frame > 30) {
                     drawCircle(formX + 60, formY + 100, 5, '#007bff'); // Filled "满意"
                }

                drawRect(formX + 30, formY + 130, formWidth - 60, 20, '#e9ecef'); // Question 2 background
                drawText('您是如何得知我们的？', formX + formWidth / 2, formY + 145, '#555', '14px Arial');

                drawRect(formX + formWidth / 2 - 40, formY + 170, 80, 25, '#28a745'); // Submit button
                drawText('提交', formX + formWidth / 2, formY + 188, 'white', '16px Arial');


                if (frame < maxFrames) {
                    animationFrameId = requestAnimationFrame(drawOnlineSurvey);
                    frame++;
                } else {
                     demoExplanationText.textContent = '网上调查是电子商务企业收集市场信息和用户反馈的重要手段，有助于优化产品和服务。';
                     demoExplanationText.classList.remove('hidden');
                }
            }
            cancelAnimationFrame(animationFrameId);
            drawOnlineSurvey();
        }


        // Event listeners for demo buttons
        document.querySelectorAll('.demo-buttons button').forEach(button => {
            button.addEventListener('click', (event) => {
                const concept = event.target.dataset.concept;
                demoExplanationText.classList.add('hidden'); // Hide text until animation finishes
                switch (concept) {
                    case 'online_customer_service':
                        animateOnlineCustomerService();
                        break;
                    case 'tv_shopping':
                        animateTvShopping();
                        break;
                    case 'online_marketing':
                        animateOnlineMarketing();
                        break;
                    case 'phone_transaction':
                        animatePhoneTransaction();
                        break;
                    case 'mall_broadcasting':
                        animateMallBroadcasting();
                        break;
                    case 'online_survey':
                        animateOnlineSurvey();
                        break;
                    default:
                        clearCanvas();
                        demoExplanationText.textContent = '请选择一个演示。';
                        demoExplanationText.classList.remove('hidden');
                        break;
                }
            });
        });

        // Initial clear of canvas
        clearCanvas();
        drawText('点击上方按钮查看电子商务环节演示', canvas.width / 2, canvas.height / 2, '#888', '18px Arial');

    </script>
</body>
</html> 