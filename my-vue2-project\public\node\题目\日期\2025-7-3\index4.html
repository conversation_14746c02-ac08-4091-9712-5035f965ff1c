<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式操作系统内核与IPC</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #0056b3;
        }
        .quiz-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #e9f5ff;
        }
        .question {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: #004085;
        }
        .options label {
            display: block;
            margin-bottom: 10px;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }
        .options label:hover {
            background-color: #d6eaff;
        }
        .options input[type="radio"] {
            margin-right: 10px;
        }
        .check-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin-top: 15px;
            transition: background-color 0.3s ease;
        }
        .check-button:hover {
            background-color: #0056b3;
        }
        .feedback {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
            display: none; /* Hidden by default */
        }
        .feedback.correct {
            background-color: #d4edda;
            color: #155724;
        }
        .feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
        }
        .explanation-section {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        canvas {
            display: block;
            margin: 20px auto;
            border: 1px solid #ccc;
            background-color: #fff;
            box-shadow: 0 0 8px rgba(0,0,0,0.1);
            border-radius: 5px;
        }
        .interactive-controls {
            text-align: center;
            margin-top: 20px;
        }
        .interactive-controls button {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
            margin: 5px;
        }
        .interactive-controls button:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>嵌入式操作系统内核与IPC学习</h1>

        <div class="quiz-section">
            <p class="question">关于嵌入式操作系统内核，以下说法正确的是（）；微内核架构中，进程间通信（IPC）通常通过（）实现。</p>
            <div class="options">
                <label><input type="radio" name="answer" value="A"> A. 直接内存访问 (DMA)</label>
                <label><input type="radio" name="answer" value="B"> B. 消息传送机制</label>
                <label><input type="radio" name="answer" value="C"> C. 共享内存</label>
                <label><input type="radio" name="answer" value="D"> D. 硬件中断</label>
            </div>
            <button class="check-button" onclick="checkAnswer()">检查答案</button>
            <div class="feedback" id="feedback"></div>
        </div>

        <div class="explanation-section">
            <h2>知识点解析</h2>
            <h3>宏内核与微内核的区别</h3>
            <p>宏内核和微内核的主要区别在于它们的架构和设计哲学。宏内核将所有系统服务（包括设备驱动程序、内存管理、进程管理等）都集成在单一的内核空间中，这通常能提供较高的性能，但降低了模块化和可维护性。</p>
            <p>相反，微内核只包含最基本的操作系统服务（如进程调度和通信），将其他服务（如设备驱动程序）放在用户空间或作为服务器进程运行，这提供了更高的模块化和安全性，但可能牺牲一些性能。</p>

            <canvas id="kernelArchitectureCanvas" width="800" height="300"></canvas>
            <p><em>👆 上方是宏内核与微内核的简化架构演示。左侧是宏内核，右侧是微内核。点击下方的按钮切换显示。</em></p>
            <div class="interactive-controls">
                <button onclick="drawMonolithic()">宏内核演示</button>
                <button onclick="drawMicrokernel()">微内核演示</button>
            </div>

            <h3>微内核中的进程间通信（IPC）</h3>
            <p>在微内核架构中，由于大部分系统服务都运行在用户空间，进程间通信（IPC）通常通过消息传送机制来实现。这种机制允许进程通过发送和接收消息来进行通信，而不需要直接访问对方的内存空间，从而提高了系统的安全性和模块化。</p>

            <canvas id="ipcCanvas" width="800" height="300"></canvas>
            <p><em>👆 上方是微内核中消息传送机制的IPC演示。点击下方的按钮发送消息。</em></p>
            <div class="interactive-controls">
                <button onclick="sendMessage()">发送消息</button>
            </div>
        </div>
    </div>

    <script>
        const correctAnswer = "B";

        function checkAnswer() {
            const selectedOption = document.querySelector('input[name="answer"]:checked');
            const feedbackDiv = document.getElementById('feedback');
            feedbackDiv.style.display = 'block';

            if (selectedOption && selectedOption.value === correctAnswer) {
                feedbackDiv.className = 'feedback correct';
                feedbackDiv.textContent = '恭喜你，答案正确！';
            } else {
                feedbackDiv.className = 'feedback incorrect';
                feedbackDiv.textContent = '答案错误，请再想想。正确答案是 B. 消息传送机制。';
            }
        }

        // --- Canvas 演示部分 ---
        const kernelArchitectureCanvas = document.getElementById('kernelArchitectureCanvas');
        const archCtx = kernelArchitectureCanvas.getContext('2d');

        const ipcCanvas = document.getElementById('ipcCanvas');
        const ipcCtx = ipcCanvas.getContext('2d');

        function clearCanvas(ctx, canvas) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制宏内核
        function drawMonolithic() {
            clearCanvas(archCtx, kernelArchitectureCanvas);
            archCtx.font = '16px Arial';
            archCtx.textAlign = 'center';

            // 宏内核 (Monolithic Kernel)
            archCtx.fillStyle = '#ADD8E6'; // Light blue
            archCtx.fillRect(50, 50, 300, 200);
            archCtx.fillStyle = '#000';
            archCtx.fillText('宏内核 (Monolithic Kernel)', 200, 30);
            archCtx.fillText('所有服务 (驱动, 内存管理, IPC等)', 200, 150);
            archCtx.fillText('都在内核空间', 200, 170);

            // 用户应用
            archCtx.fillStyle = '#FFD700'; // Gold
            archCtx.fillRect(100, 0, 200, 40);
            archCtx.fillStyle = '#000';
            archCtx.fillText('用户应用', 200, 25);
            archCtx.fillText('用户应用', 200, 275);
            archCtx.fillRect(100, 260, 200, 40);

            archCtx.fillText('所有服务都在内核态，性能高但维护性差', 200, 290);
        }

        // 绘制微内核
        function drawMicrokernel() {
            clearCanvas(archCtx, kernelArchitectureCanvas);
            archCtx.font = '16px Arial';
            archCtx.textAlign = 'center';

            // 微内核 (Microkernel)
            archCtx.fillStyle = '#90EE90'; // Light green
            archCtx.fillRect(450, 100, 100, 100);
            archCtx.fillStyle = '#000';
            archCtx.fillText('微内核', 500, 80);
            archCtx.fillText('基本服务 (IPC, 调度)', 500, 150);

            // 用户空间的服务
            archCtx.fillStyle = '#FFB6C1'; // Light pink
            archCtx.fillRect(400, 0, 150, 80);
            archCtx.fillStyle = '#000';
            archCtx.fillText('设备驱动', 475, 40);

            archCtx.fillRect(600, 0, 150, 80);
            archCtx.fillStyle = '#000';
            archCtx.fillText('文件系统', 675, 40);

            archCtx.fillRect(400, 220, 150, 80);
            archCtx.fillStyle = '#000';
            archCtx.fillText('内存管理', 475, 260);

            archCtx.fillRect(600, 220, 150, 80);
            archCtx.fillStyle = '#000';
            archCtx.fillText('其他服务', 675, 260);

            archCtx.fillText('大部分服务在用户态，安全性高，模块化好', 600, 290);
        }

        // 绘制IPC演示初始状态
        function drawIPCInitial() {
            clearCanvas(ipcCtx, ipcCanvas);
            ipcCtx.font = '16px Arial';
            ipcCtx.textAlign = 'center';

            // 进程A
            ipcCtx.fillStyle = '#FFD700';
            ipcCtx.fillRect(50, 100, 150, 80);
            ipcCtx.fillStyle = '#000';
            ipcCtx.fillText('进程 A', 125, 140);

            // 微内核 (IPC 中介)
            ipcCtx.fillStyle = '#90EE90';
            ipcCtx.fillRect(325, 100, 150, 80);
            ipcCtx.fillStyle = '#000';
            ipcCtx.fillText('微内核 (IPC)', 400, 140);

            // 进程B
            ipcCtx.fillStyle = '#ADD8E6';
            ipcCtx.fillRect(600, 100, 150, 80);
            ipcCtx.fillStyle = '#000';
            ipcCtx.fillText('进程 B', 675, 140);

            ipcCtx.fillText('消息传送机制: 进程通过微内核交换消息', ipcCanvas.width / 2, 250);
        }

        // 消息发送动画
        function sendMessage() {
            drawIPCInitial(); // 重置到初始状态

            let messageX = 125;
            const messageY = 160;
            const messageWidth = 50;
            const messageHeight = 20;
            const speed = 5; // 动画速度

            function animateMessage() {
                clearCanvas(ipcCtx, ipcCanvas);
                drawIPCInitial(); // 重新绘制背景

                // 绘制移动中的消息
                ipcCtx.fillStyle = '#FF4500'; // OrangeRed
                ipcCtx.fillRect(messageX - messageWidth / 2, messageY - messageHeight / 2, messageWidth, messageHeight);
                ipcCtx.fillStyle = '#FFF';
                ipcCtx.fillText('消息', messageX, messageY + 5);

                messageX += speed;

                if (messageX < 675) { // 消息还没到达进程B
                    requestAnimationFrame(animateMessage);
                } else {
                    // 消息到达后的提示
                    ipcCtx.fillStyle = '#000';
                    ipcCtx.fillText('消息已送达进程 B!', ipcCanvas.width / 2, 200);
                }
            }
            animateMessage();
        }

        // 初始化 canvas
        document.addEventListener('DOMContentLoaded', () => {
            drawMonolithic(); // 默认显示宏内核
            drawIPCInitial(); // 默认显示IPC初始状态
        });

    </script>
</body>
</html>