<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络安全技术 - 传输层安全</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.4rem;
            color: #333;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .option.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-color: #4CAF50;
        }

        .option.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            border-color: #f44336;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        #networkCanvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            background: linear-gradient(45deg, #f0f2f5, #ffffff);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .layer-demo {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .layer {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 150px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .layer:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .layer.active {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            transform: scale(1.1);
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .tech-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .tech-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .tech-card.transport {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #e8f5e8, #f8f9fa);
        }

        .tech-card.network {
            border-color: #f44336;
            background: linear-gradient(135deg, #ffeaea, #f8f9fa);
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">网络安全技术学习</h1>
            <p class="subtitle">传输层安全技术详解</p>
        </div>

        <div class="question-card">
            <h2 class="section-title">题目分析</h2>
            <div class="question-text">
                下列技术中，不是传输层安全技术的是（ ）。
            </div>
            <div class="options" id="options">
                <div class="option" data-answer="A">
                    <strong>A. SSL</strong><br>
                    安全套接字层
                </div>
                <div class="option" data-answer="B">
                    <strong>B. SOCKS</strong><br>
                    代理协议
                </div>
                <div class="option" data-answer="C">
                    <strong>C. IPSEC</strong><br>
                    IP安全协议
                </div>
                <div class="option" data-answer="D">
                    <strong>D. 安全RPC</strong><br>
                    远程过程调用
                </div>
            </div>
            <button class="btn" onclick="showAnswer()">显示答案解析</button>
        </div>

        <div class="canvas-container">
            <h2 class="section-title">网络层次结构可视化</h2>
            <canvas id="networkCanvas"></canvas>
            <div class="layer-demo">
                <div class="layer" data-layer="application" onclick="highlightLayer('application')">
                    应用层
                </div>
                <div class="layer" data-layer="transport" onclick="highlightLayer('transport')">
                    传输层
                </div>
                <div class="layer" data-layer="network" onclick="highlightLayer('network')">
                    网络层
                </div>
                <div class="layer" data-layer="datalink" onclick="highlightLayer('datalink')">
                    数据链路层
                </div>
            </div>
        </div>

        <div class="explanation">
            <h2 class="section-title">技术分类详解</h2>
            <div class="tech-grid">
                <div class="tech-card transport" onclick="showTechDetail('ssl')">
                    <h3>SSL/TLS</h3>
                    <p>传输层安全协议</p>
                    <small>在传输层提供加密</small>
                </div>
                <div class="tech-card transport" onclick="showTechDetail('socks')">
                    <h3>SOCKS</h3>
                    <p>传输层代理协议</p>
                    <small>在传输层进行代理</small>
                </div>
                <div class="tech-card network" onclick="showTechDetail('ipsec')">
                    <h3>IPSEC</h3>
                    <p>网络层安全协议</p>
                    <small>在IP层提供安全</small>
                </div>
                <div class="tech-card transport" onclick="showTechDetail('rpc')">
                    <h3>安全RPC</h3>
                    <p>传输层安全调用</p>
                    <small>在传输层保护RPC</small>
                </div>
            </div>
            <button class="btn" onclick="startAnimation()">开始动画演示</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('networkCanvas');
        const ctx = canvas.getContext('2d');
        let animationFrame;
        let currentLayer = null;

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // 绘制网络层次结构
        function drawNetworkLayers() {
            const width = canvas.offsetWidth;
            const height = canvas.offsetHeight;
            
            ctx.clearRect(0, 0, width, height);
            
            const layers = [
                { name: '应用层', color: '#FF6B6B', y: 50 },
                { name: '传输层', color: '#4ECDC4', y: 130 },
                { name: '网络层', color: '#45B7D1', y: 210 },
                { name: '数据链路层', color: '#96CEB4', y: 290 }
            ];
            
            layers.forEach((layer, index) => {
                const isActive = currentLayer === layer.name.replace('层', '');
                
                // 绘制层级矩形
                ctx.fillStyle = isActive ? '#FFD93D' : layer.color;
                ctx.fillRect(50, layer.y, width - 100, 60);
                
                // 绘制层级文字
                ctx.fillStyle = isActive ? '#333' : 'white';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(layer.name, width / 2, layer.y + 35);
                
                // 绘制技术示例
                if (layer.name === '传输层') {
                    ctx.fillStyle = isActive ? '#333' : 'rgba(255,255,255,0.8)';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('SSL/TLS, SOCKS, 安全RPC', width / 2, layer.y + 55);
                } else if (layer.name === '网络层') {
                    ctx.fillStyle = isActive ? '#333' : 'rgba(255,255,255,0.8)';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('IPSEC', width / 2, layer.y + 55);
                }
                
                // 绘制连接线
                if (index < layers.length - 1) {
                    ctx.strokeStyle = '#ddd';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(width / 2 - 20, layer.y + 60);
                    ctx.lineTo(width / 2 - 20, layer.y + 70);
                    ctx.moveTo(width / 2 + 20, layer.y + 60);
                    ctx.lineTo(width / 2 + 20, layer.y + 70);
                    ctx.stroke();
                }
            });
        }

        // 高亮特定层
        function highlightLayer(layerName) {
            currentLayer = layerName;
            
            // 更新UI
            document.querySelectorAll('.layer').forEach(el => {
                el.classList.remove('active');
            });
            document.querySelector(`[data-layer="${layerName}"]`).classList.add('active');
            
            drawNetworkLayers();
            
            // 显示相关信息
            showLayerInfo(layerName);
        }

        // 显示层级信息
        function showLayerInfo(layerName) {
            const info = {
                'application': '应用层：为用户提供网络服务接口',
                'transport': '传输层：提供端到端的数据传输服务，SSL、SOCKS等在此层工作',
                'network': '网络层：负责数据包的路由和转发，IPSEC在此层提供安全',
                'datalink': '数据链路层：负责相邻节点间的数据传输'
            };
            
            // 这里可以添加信息显示逻辑
            console.log(info[layerName]);
        }

        // 显示答案
        function showAnswer() {
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                if (option.dataset.answer === 'C') {
                    option.classList.add('correct');
                } else {
                    option.classList.add('wrong');
                }
            });
            
            // 高亮网络层
            highlightLayer('network');
        }

        // 显示技术详情
        function showTechDetail(tech) {
            const details = {
                'ssl': 'SSL/TLS工作在传输层，为应用层提供加密通信',
                'socks': 'SOCKS是传输层代理协议，用于网络代理',
                'ipsec': 'IPSEC工作在网络层（IP层），提供IP数据包的安全',
                'rpc': '安全RPC在传输层保护远程过程调用'
            };
            
            alert(details[tech]);
        }

        // 开始动画演示
        function startAnimation() {
            let step = 0;
            const steps = ['application', 'transport', 'network', 'datalink'];
            
            function animate() {
                if (step < steps.length) {
                    highlightLayer(steps[step]);
                    step++;
                    setTimeout(animate, 1500);
                }
            }
            
            animate();
        }

        // 初始化
        drawNetworkLayers();
        
        // 添加交互效果
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                if (this.dataset.answer === 'C') {
                    this.classList.add('correct');
                    highlightLayer('network');
                } else {
                    this.classList.add('wrong');
                }
            });
        });
    </script>
</body>
</html>
