<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象分析与设计模型 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.8rem;
            color: white;
            margin-bottom: 20px;
            font-weight: 300;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 300;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .model-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            animation: slideInUp 1s ease-out;
        }

        .model-title {
            font-size: 1.8rem;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
        }

        .analysis-model {
            border-left: 5px solid #ff6b6b;
        }

        .design-model {
            border-left: 5px solid #4ecdc4;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        canvas {
            width: 100%;
            height: 300px;
            border-radius: 10px;
            cursor: pointer;
        }

        .component-list {
            list-style: none;
            padding: 0;
        }

        .component-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            margin: 8px 0;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .component-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .component-item.active {
            background: rgba(102, 126, 234, 0.1);
            border-left-color: #667eea;
        }

        .component-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .analysis-icon { background: #ff6b6b; }
        .design-icon { background: #4ecdc4; }

        .quiz-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .quiz-question {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
            color: #667eea;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .option.correct {
            border-color: #4caf50;
            background: rgba(76, 175, 80, 0.1);
            animation: correctPulse 0.6s ease-out;
        }

        .option.incorrect {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
            animation: incorrectShake 0.6s ease-out;
        }

        .option-label {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .option.correct .option-label {
            background: #4caf50;
        }

        .option.incorrect .option-label {
            background: #f44336;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .quiz-result {
            text-align: center;
            padding: 30px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            margin-top: 20px;
            display: none;
        }

        .result-correct {
            color: #4caf50;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .result-incorrect {
            color: #f44336;
            font-size: 1.2rem;
            font-weight: bold;
        }

        .explanation {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            border-left: 5px solid #667eea;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        @keyframes incorrectShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">面向对象分析与设计模型</h1>
            <p class="subtitle">Object-Oriented Analysis & Design Models</p>
        </div>

        <div class="main-content">
            <!-- 分析模型 -->
            <div class="model-section analysis-model">
                <h2 class="model-title">📊 分析模型</h2>
                <div class="canvas-container">
                    <canvas id="analysisCanvas"></canvas>
                </div>
                <ul class="component-list">
                    <li class="component-item" data-component="architecture">
                        <div class="component-icon analysis-icon">🏗️</div>
                        <span>顶层架构图</span>
                    </li>
                    <li class="component-item" data-component="usecase">
                        <div class="component-icon analysis-icon">👤</div>
                        <span>用例与用例图</span>
                    </li>
                    <li class="component-item" data-component="domain">
                        <div class="component-icon analysis-icon">🎯</div>
                        <span>领域概念模型</span>
                    </li>
                </ul>
            </div>

            <!-- 设计模型 -->
            <div class="model-section design-model">
                <h2 class="model-title">🎨 设计模型</h2>
                <div class="canvas-container">
                    <canvas id="designCanvas"></canvas>
                </div>
                <ul class="component-list">
                    <li class="component-item" data-component="package">
                        <div class="component-icon design-icon">📦</div>
                        <span>包图（软件体系架构）</span>
                    </li>
                    <li class="component-item" data-component="interaction">
                        <div class="component-icon design-icon">🔄</div>
                        <span>交互图（用例实现）</span>
                    </li>
                    <li class="component-item" data-component="class">
                        <div class="component-icon design-icon">📋</div>
                        <span>完整精确的类图</span>
                    </li>
                    <li class="component-item" data-component="state">
                        <div class="component-icon design-icon">⚡</div>
                        <span>状态图（复杂对象）</span>
                    </li>
                    <li class="component-item" data-component="activity">
                        <div class="component-icon design-icon">🔀</div>
                        <span>活动图（流程化处理）</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="startDemo()">开始演示</button>
            <button class="btn" onclick="resetDemo()">重置</button>
            <button class="btn" onclick="showComparison()">对比分析</button>
        </div>

        <!-- 选择题部分 -->
        <div class="quiz-section">
            <h2 style="text-align: center; color: #667eea; margin-bottom: 30px;">📝 知识测试</h2>

            <div class="quiz-question">
                面向对象的分析模型主要由顶层架构图、用例与用例图和<span class="highlight">（请作答此空）</span>构成；设计模型则包含以<span class="highlight">（ ）</span>表示的软件体系机构图、以交互图表示的用例实现图、完整精确的类图、描述复杂对象的<span class="highlight">（ ）</span>和用以描述流程化处理过程的活动图等。
            </div>

            <div class="quiz-options">
                <div class="option" data-answer="A">
                    <span class="option-label">A</span>
                    <span class="option-text">数据流模型</span>
                </div>
                <div class="option" data-answer="B">
                    <span class="option-label">B</span>
                    <span class="option-text">领域概念模型</span>
                </div>
                <div class="option" data-answer="C">
                    <span class="option-label">C</span>
                    <span class="option-text">功能分解图</span>
                </div>
                <div class="option" data-answer="D">
                    <span class="option-label">D</span>
                    <span class="option-text">功能需求模型</span>
                </div>
            </div>

            <div class="quiz-result" id="quizResult">
                <div class="result-content" id="resultContent"></div>
                <button class="btn" onclick="resetQuiz()">重新答题</button>
                <button class="btn" onclick="showDetailedExplanation()">详细解析</button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3>💡 学习指南</h3>
            <p>点击左右两侧的组件列表，了解分析模型和设计模型的不同组成部分！观察它们之间的关系和演进过程。</p>
        </div>
    </div>

    <script>
        // Canvas 设置
        const analysisCanvas = document.getElementById('analysisCanvas');
        const designCanvas = document.getElementById('designCanvas');
        const analysisCtx = analysisCanvas.getContext('2d');
        const designCtx = designCanvas.getContext('2d');

        function resizeCanvas(canvas) {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            const ctx = canvas.getContext('2d');
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        window.addEventListener('resize', () => {
            resizeCanvas(analysisCanvas);
            resizeCanvas(designCanvas);
        });

        resizeCanvas(analysisCanvas);
        resizeCanvas(designCanvas);

        // 动画状态
        let animationState = {
            time: 0,
            activeComponent: null,
            particles: [],
            showAnalysis: false,
            showDesign: false
        };

        // 粒子系统
        class Particle {
            constructor(x, y, color, canvas) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 3;
                this.vy = (Math.random() - 0.5) * 3;
                this.color = color;
                this.life = 1;
                this.decay = 0.02;
                this.canvas = canvas;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
            }

            draw() {
                const ctx = this.canvas.getContext('2d');
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 绘制分析模型组件
        function drawAnalysisComponent(type, x, y, size, alpha = 1) {
            analysisCtx.save();
            analysisCtx.globalAlpha = alpha;

            switch(type) {
                case 'architecture':
                    // 顶层架构图
                    analysisCtx.fillStyle = '#ff6b6b';
                    analysisCtx.fillRect(x - size/2, y - size/3, size, size/1.5);
                    analysisCtx.fillStyle = 'white';
                    analysisCtx.font = '12px Arial';
                    analysisCtx.textAlign = 'center';
                    analysisCtx.fillText('架构', x, y + 5);
                    break;

                case 'usecase':
                    // 用例图
                    analysisCtx.strokeStyle = '#ff6b6b';
                    analysisCtx.lineWidth = 3;
                    analysisCtx.beginPath();
                    analysisCtx.ellipse(x, y, size/2, size/3, 0, 0, Math.PI * 2);
                    analysisCtx.stroke();
                    analysisCtx.fillStyle = '#ff6b6b';
                    analysisCtx.font = '12px Arial';
                    analysisCtx.textAlign = 'center';
                    analysisCtx.fillText('用例', x, y + 5);
                    break;

                case 'domain':
                    // 领域概念模型
                    analysisCtx.fillStyle = '#ff6b6b';
                    analysisCtx.beginPath();
                    analysisCtx.arc(x, y, size/2, 0, Math.PI * 2);
                    analysisCtx.fill();
                    analysisCtx.fillStyle = 'white';
                    analysisCtx.font = '12px Arial';
                    analysisCtx.textAlign = 'center';
                    analysisCtx.fillText('领域', x, y + 5);
                    break;
            }

            analysisCtx.restore();
        }

        // 绘制设计模型组件
        function drawDesignComponent(type, x, y, size, alpha = 1) {
            designCtx.save();
            designCtx.globalAlpha = alpha;

            switch(type) {
                case 'package':
                    // 包图
                    designCtx.fillStyle = '#4ecdc4';
                    designCtx.fillRect(x - size/2, y - size/3, size, size/1.5);
                    designCtx.strokeStyle = 'white';
                    designCtx.lineWidth = 2;
                    designCtx.strokeRect(x - size/2, y - size/3, size, size/1.5);
                    designCtx.fillStyle = 'white';
                    designCtx.font = '10px Arial';
                    designCtx.textAlign = 'center';
                    designCtx.fillText('包图', x, y + 3);
                    break;

                case 'interaction':
                    // 交互图
                    designCtx.strokeStyle = '#4ecdc4';
                    designCtx.lineWidth = 3;
                    designCtx.beginPath();
                    designCtx.moveTo(x - size/3, y);
                    designCtx.lineTo(x + size/3, y);
                    designCtx.moveTo(x, y - size/3);
                    designCtx.lineTo(x, y + size/3);
                    designCtx.stroke();
                    break;

                case 'class':
                    // 类图
                    designCtx.fillStyle = '#4ecdc4';
                    designCtx.fillRect(x - size/2, y - size/2, size, size);
                    designCtx.strokeStyle = 'white';
                    designCtx.lineWidth = 2;
                    designCtx.strokeRect(x - size/2, y - size/2, size, size);
                    designCtx.fillStyle = 'white';
                    designCtx.font = '10px Arial';
                    designCtx.textAlign = 'center';
                    designCtx.fillText('类图', x, y + 3);
                    break;

                case 'state':
                    // 状态图
                    designCtx.fillStyle = '#4ecdc4';
                    designCtx.beginPath();
                    designCtx.arc(x, y, size/2, 0, Math.PI * 2);
                    designCtx.fill();
                    designCtx.fillStyle = 'white';
                    designCtx.font = '10px Arial';
                    designCtx.textAlign = 'center';
                    designCtx.fillText('状态', x, y + 3);
                    break;

                case 'activity':
                    // 活动图
                    designCtx.fillStyle = '#4ecdc4';
                    designCtx.beginPath();
                    designCtx.roundRect(x - size/2, y - size/3, size, size/1.5, 10);
                    designCtx.fill();
                    designCtx.fillStyle = 'white';
                    designCtx.font = '10px Arial';
                    designCtx.textAlign = 'center';
                    designCtx.fillText('活动', x, y + 3);
                    break;
            }

            designCtx.restore();
        }

        // 动画循环
        function animate() {
            // 清空画布
            analysisCtx.clearRect(0, 0, analysisCanvas.width / window.devicePixelRatio, analysisCanvas.height / window.devicePixelRatio);
            designCtx.clearRect(0, 0, designCanvas.width / window.devicePixelRatio, designCanvas.height / window.devicePixelRatio);

            const analysisWidth = analysisCanvas.width / window.devicePixelRatio;
            const analysisHeight = analysisCanvas.height / window.devicePixelRatio;
            const designWidth = designCanvas.width / window.devicePixelRatio;
            const designHeight = designCanvas.height / window.devicePixelRatio;

            // 绘制分析模型组件
            if (animationState.showAnalysis || animationState.activeComponent) {
                const bounce = Math.sin(animationState.time * 2) * 3;
                drawAnalysisComponent('architecture', analysisWidth * 0.2, analysisHeight * 0.3 + bounce, 40);
                drawAnalysisComponent('usecase', analysisWidth * 0.5, analysisHeight * 0.6 - bounce, 40);
                drawAnalysisComponent('domain', analysisWidth * 0.8, analysisHeight * 0.4 + bounce, 40);
            }

            // 绘制设计模型组件
            if (animationState.showDesign || animationState.activeComponent) {
                const pulse = 1 + Math.sin(animationState.time * 3) * 0.1;
                drawDesignComponent('package', designWidth * 0.2, designHeight * 0.2, 35 * pulse);
                drawDesignComponent('interaction', designWidth * 0.5, designHeight * 0.2, 35);
                drawDesignComponent('class', designWidth * 0.8, designHeight * 0.2, 35);
                drawDesignComponent('state', designWidth * 0.35, designHeight * 0.7, 35 * pulse);
                drawDesignComponent('activity', designWidth * 0.65, designHeight * 0.7, 35);
            }

            // 更新和绘制粒子
            animationState.particles = animationState.particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });

            animationState.time += 0.02;
            requestAnimationFrame(animate);
        }

        // 控制函数
        function startDemo() {
            resetDemo();

            setTimeout(() => {
                animationState.showAnalysis = true;
                updateExplanation('analysis');
                addParticles(analysisCanvas, '#ff6b6b');
            }, 500);

            setTimeout(() => {
                animationState.showDesign = true;
                updateExplanation('design');
                addParticles(designCanvas, '#4ecdc4');
            }, 2000);

            setTimeout(() => {
                updateExplanation('complete');
            }, 3500);
        }

        function resetDemo() {
            animationState.showAnalysis = false;
            animationState.showDesign = false;
            animationState.activeComponent = null;
            animationState.particles = [];

            // 清除所有active状态
            document.querySelectorAll('.component-item').forEach(item => {
                item.classList.remove('active');
            });
        }

        function showComparison() {
            animationState.showAnalysis = true;
            animationState.showDesign = true;
            updateExplanation('comparison');
            addParticles(analysisCanvas, '#ff6b6b');
            addParticles(designCanvas, '#4ecdc4');
        }

        function addParticles(canvas, color) {
            const rect = canvas.getBoundingClientRect();
            for (let i = 0; i < 8; i++) {
                animationState.particles.push(new Particle(
                    Math.random() * rect.width,
                    Math.random() * rect.height,
                    color,
                    canvas
                ));
            }
        }

        // 组件交互
        document.querySelectorAll('.component-item').forEach(item => {
            item.addEventListener('click', function() {
                // 清除其他active状态
                document.querySelectorAll('.component-item').forEach(i => i.classList.remove('active'));

                // 设置当前active
                this.classList.add('active');

                const component = this.dataset.component;
                animationState.activeComponent = component;

                // 根据组件类型显示对应模型
                if (['architecture', 'usecase', 'domain'].includes(component)) {
                    animationState.showAnalysis = true;
                    addParticles(analysisCanvas, '#ff6b6b');
                } else {
                    animationState.showDesign = true;
                    addParticles(designCanvas, '#4ecdc4');
                }

                updateExplanation(component);
            });
        });

        // 选择题功能
        let quizAnswered = false;
        let selectedAnswer = null;

        function initQuiz() {
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.addEventListener('click', function() {
                    if (quizAnswered) return;

                    options.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedAnswer = this.dataset.answer;

                    setTimeout(() => {
                        checkAnswer();
                    }, 500);
                });
            });
        }

        function checkAnswer() {
            if (quizAnswered) return;

            quizAnswered = true;
            const correctAnswer = 'B';
            const options = document.querySelectorAll('.option');
            const resultDiv = document.getElementById('quizResult');
            const resultContent = document.getElementById('resultContent');

            options.forEach(option => {
                if (option.dataset.answer === correctAnswer) {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== correctAnswer) {
                    option.classList.add('incorrect');
                }
            });

            if (selectedAnswer === correctAnswer) {
                resultContent.innerHTML = `
                    <div class="result-correct">
                        🎉 恭喜答对了！
                    </div>
                    <p>正确答案是 <strong>B. 领域概念模型</strong></p>
                    <p>您已经掌握了面向对象分析模型的组成！</p>
                `;
                addParticles(analysisCanvas, '#4caf50');
                addParticles(designCanvas, '#4caf50');
            } else {
                resultContent.innerHTML = `
                    <div class="result-incorrect">
                        ❌ 答案不正确
                    </div>
                    <p>正确答案是 <strong>B. 领域概念模型</strong></p>
                    <p>让我们重新学习分析模型和设计模型的区别吧！</p>
                `;
            }

            resultDiv.style.display = 'block';
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function resetQuiz() {
            quizAnswered = false;
            selectedAnswer = null;

            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.classList.remove('selected', 'correct', 'incorrect');
            });

            document.getElementById('quizResult').style.display = 'none';
        }

        function showDetailedExplanation() {
            updateExplanation('detailed');
            document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
        }

        // 解析更新函数
        function updateExplanation(type) {
            const explanations = {
                analysis: {
                    title: '📊 分析模型',
                    content: '分析模型关注"做什么"，主要包含三个核心组件：顶层架构图展示系统整体结构，用例图描述用户需求，领域概念模型定义业务领域的核心概念和关系。'
                },
                design: {
                    title: '🎨 设计模型',
                    content: '设计模型关注"怎么做"，包含五个主要组件：包图表示软件架构，交互图展示用例实现，类图定义精确的类结构，状态图描述复杂对象的状态变化，活动图展示流程化处理过程。'
                },
                complete: {
                    title: '🔄 完整流程',
                    content: '从分析到设计是一个渐进的过程：分析模型捕获需求和业务逻辑，设计模型将其转化为可实现的技术方案。两者相互补充，共同构成完整的面向对象开发过程。'
                },
                comparison: {
                    title: '⚖️ 对比分析',
                    content: '分析模型（左侧）专注于问题域，设计模型（右侧）专注于解决方案域。分析模型相对稳定，设计模型会根据技术选型和实现约束进行调整。'
                },
                architecture: {
                    title: '🏗️ 顶层架构图',
                    content: '展示系统的整体结构和主要组件之间的关系，是分析阶段的重要产出，为后续详细设计提供指导框架。'
                },
                usecase: {
                    title: '👤 用例与用例图',
                    content: '描述系统的功能需求，以用户的角度定义系统应该提供什么服务，是连接业务需求和技术实现的桥梁。'
                },
                domain: {
                    title: '🎯 领域概念模型',
                    content: '定义业务领域中的核心概念、属性和关系，是面向对象分析的核心产出，为设计阶段的类设计提供基础。'
                },
                package: {
                    title: '📦 包图',
                    content: '以包的形式组织系统架构，展示软件体系结构，定义模块间的依赖关系，是设计阶段架构设计的重要工具。'
                },
                interaction: {
                    title: '🔄 交互图',
                    content: '展示用例的具体实现过程，描述对象间的交互序列，将分析阶段的用例转化为设计阶段的具体实现方案。'
                },
                class: {
                    title: '📋 类图',
                    content: '完整精确地定义系统中的类、属性、方法和关系，是面向对象设计的核心产出，直接指导编码实现。'
                },
                state: {
                    title: '⚡ 状态图',
                    content: '描述复杂对象的状态变化和状态转换条件，特别适用于具有复杂生命周期的业务对象建模。'
                },
                activity: {
                    title: '🔀 活动图',
                    content: '描述流程化的处理过程，展示活动的执行顺序、并发和同步关系，适用于业务流程和算法建模。'
                },
                detailed: {
                    title: '📚 详细解析',
                    content: `
                        <div style="text-align: left;">
                            <h4>🎯 题目解析</h4>
                            <p><strong>正确答案：B. 领域概念模型</strong></p>

                            <h4>📖 知识要点</h4>

                            <div style="margin: 20px 0; padding: 15px; background: rgba(255, 107, 107, 0.1); border-radius: 8px;">
                                <strong>📊 分析模型三要素</strong><br>
                                • <strong>顶层架构图</strong>：系统整体结构<br>
                                • <strong>用例与用例图</strong>：功能需求描述<br>
                                • <strong>领域概念模型</strong>：业务领域核心概念
                            </div>

                            <div style="margin: 20px 0; padding: 15px; background: rgba(78, 205, 196, 0.1); border-radius: 8px;">
                                <strong>🎨 设计模型五要素</strong><br>
                                • <strong>包图</strong>：软件体系架构表示<br>
                                • <strong>交互图</strong>：用例实现图<br>
                                • <strong>类图</strong>：完整精确的类定义<br>
                                • <strong>状态图</strong>：复杂对象状态描述<br>
                                • <strong>活动图</strong>：流程化处理过程
                            </div>

                            <h4>🔍 为什么选择"领域概念模型"？</h4>
                            <p>题目明确提到分析模型的三个组成部分，前两个已经给出（顶层架构图、用例与用例图），第三个空缺就是<strong>领域概念模型</strong>。领域概念模型是面向对象分析的核心，它定义了业务领域中的关键概念、属性和关系，为后续的设计提供基础。</p>

                            <h4>🚫 其他选项为什么不对？</h4>
                            <p>• <strong>数据流模型</strong>：属于结构化分析方法，不是面向对象分析的组成部分<br>
                            • <strong>功能分解图</strong>：也是结构化方法的产物<br>
                            • <strong>功能需求模型</strong>：过于宽泛，不是面向对象分析的特定组件</p>
                        </div>
                    `
                }
            };

            const explanation = explanations[type];
            if (explanation) {
                document.getElementById('explanation').innerHTML = `
                    <h3>${explanation.title}</h3>
                    <div>${explanation.content}</div>
                `;
            }
        }

        // 初始化
        animate();
        initQuiz();
        updateExplanation('analysis');
    </script>
</body>
</html>
