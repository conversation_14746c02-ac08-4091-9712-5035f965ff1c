<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Circumstance</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            background-color: #f0f2f5;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 900px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .story-explanation {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            width: 100%;
            border-left: 5px solid #ff9800;
        }

        .story-explanation p {
            margin: 0;
            line-height: 1.6;
            color: #555;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-radius: 8px;
            overflow: hidden;
        }

        canvas {
            display: block;
            width: 100%;
            height: auto;
            background-color: #ffffff;
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        button {
            padding: 10px 20px;
            font-size: 1em;
            color: #fff;
            background-color: #ff9800;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        button:hover {
            background-color: #e68900;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }

        #explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3e0;
            border-radius: 8px;
            width: 100%;
            text-align: center;
            font-size: 1.2em;
            color: #333;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.5s;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>
        circumstance
        <span style="font-size: 0.5em; color: #555;">(circum- + -stance)</span>
    </h1>

    <div class="story-explanation">
        <p><strong>故事背景：</strong>想象一个人，稳稳地站在世界的中心。他的周围不断发生着各种各样的事情，这些事情共同构成了他的"境遇"或"情况"。这个故事将通过 "circumstance" 的词源来生动地展示其含义。</p>
    </div>

    <div class="canvas-container">
        <canvas id="wordAnimation" width="800" height="450"></canvas>
    </div>

    <div class="controls">
        <button id="playBtn">播放完整动画</button>
        <button id="stanceBtn">第一幕: -stance (站立)</button>
        <button id="circumBtn">第二幕: circum- (环绕)</button>
        <button id="circumstanceBtn">第三幕: circumstance (环境/情况)</button>
        <button id="resetBtn">重置</button>
    </div>

    <div id="explanation">
        <p>点击按钮，开始探索 "circumstance" 的含义吧！</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordAnimation');
    const ctx = canvas.getContext('2d');
    const explanationDiv = document.getElementById('explanation');

    const playBtn = document.getElementById('playBtn');
    const stanceBtn = document.getElementById('stanceBtn');
    const circumBtn = document.getElementById('circumBtn');
    const circumstanceBtn = document.getElementById('circumstanceBtn');
    const resetBtn = document.getElementById('resetBtn');

    let animationFrameId;

    const colors = {
        background: '#f0f8ff',
        text: '#333333',
        primary: '#ff9800',
        secondary: '#03a9f4',
        accent: '#4caf50',
        sun: '#ffeb3b',
        rain: '#2196f3',
        cloud: '#90a4ae'
    };
    const fonts = {
        title: 'bold 36px Arial',
        text: '24px Arial',
        chinese: '20px "Microsoft YaHei", sans-serif'
    };

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2 + 50;

    function drawMan(x, y) {
        // 简笔画小人
        ctx.strokeStyle = colors.text;
        ctx.lineWidth = 3;
        // Body
        ctx.beginPath();
        ctx.moveTo(x, y - 20);
        ctx.lineTo(x, y + 10);
        ctx.stroke();
        // Head
        ctx.beginPath();
        ctx.arc(x, y - 30, 10, 0, Math.PI * 2);
        ctx.stroke();
        // Legs
        ctx.beginPath();
        ctx.moveTo(x, y + 10);
        ctx.lineTo(x - 10, y + 30);
        ctx.moveTo(x, y + 10);
        ctx.lineTo(x + 10, y + 30);
        ctx.stroke();
        // Arms
        ctx.beginPath();
        ctx.moveTo(x, y - 10);
        ctx.lineTo(x - 15, y + 5);
        ctx.moveTo(x, y - 10);
        ctx.lineTo(x + 15, y + 5);
        ctx.stroke();
    }
    
    function drawInitialState() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = colors.background;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = colors.text;
        ctx.font = fonts.title;
        ctx.textAlign = 'center';
        ctx.fillText('circumstance', centerX, 50);
        
        explanationDiv.innerHTML = '<p>点击按钮，开始探索 "circumstance" 的含义吧！</p>';
    }
    
    function resetAnimation() {
        cancelAnimationFrame(animationFrameId);
        drawInitialState();
    }

    // 第一幕: -stance (站立)
    function animateStance() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>-stance (站立)</strong>: 首先，我们看到一个人稳稳地<strong>站</strong>在地面上。这是构成单词的基础。</p>';
        
        let progress = 0;
        function animate() {
            if (progress > 1) {
                drawMan(centerX, centerY - 20 * (1 - (progress - 1) * 5));
                if(progress > 1.2) return;
            } else {
                 ctx.clearRect(0, 60, canvas.width, canvas.height-60);
                 ctx.fillStyle = colors.background;
                 ctx.fillRect(0, 60, canvas.width, canvas.height-60);
                 drawMan(centerX, centerY - 20 * (1-progress));
            }
           
            progress += 0.01;
            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }

    // 第二幕: circum- (环绕)
    function animateCircum() {
        resetAnimation();
        drawMan(centerX, centerY);
        explanationDiv.innerHTML = '<p><strong>circum- (环绕/周围)</strong>: 接着，我们看到一个圆圈在这个人<strong>周围</strong>形成，代表着"环绕"的概念。</p>';

        let angle = 0;
        function animate() {
            //  clear just the circle part
            ctx.save();
            ctx.beginPath();
            ctx.arc(centerX, centerY - 15, 120, 0, Math.PI * 2);
            ctx.clip();
            ctx.clearRect(centerX - 121, centerY - 136, 242, 242);
            ctx.restore();


            ctx.strokeStyle = colors.primary;
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(centerX, centerY - 15, 100, -Math.PI / 2, -Math.PI / 2 + angle);
            ctx.stroke();
            
            angle += 0.05;
            if (angle <= Math.PI * 2) {
                animationFrameId = requestAnimationFrame(animate);
            }
        }
        animate();
    }

    // 第三幕: circumstance (环境/情况)
    function animateCircumstance() {
        resetAnimation();
        drawMan(centerX, centerY);
        explanationDiv.innerHTML = '<p><strong>circumstance (环境/情况)</strong>: 最后，各种事物 (天气、机会、挑战) 在周围出现，共同构成了这个人的<strong>"环境"</strong>和<strong>"情况"</strong>。请点击画布上的图标进行交互！</p>';
        
        const objects = [
            { type: 'sun', angle: 45, radius: 150, draw: drawSun, explanation: '晴天，代表顺利的境遇。' },
            { type: 'rain', angle: 135, radius: 150, draw: drawRain, explanation: '下雨，代表遇到的困难。' },
            { type: 'idea', angle: 225, radius: 150, draw: drawIdea, explanation: '一个灯泡，代表新的想法或机遇。' },
            { type: 'obstacle', angle: 315, radius: 150, draw: drawObstacle, explanation: '一堵墙，代表前进的障碍。' }
        ];

        objects.forEach(obj => obj.draw(obj, false));

        function getMousePos(canvas, evt) {
            const rect = canvas.getBoundingClientRect();
            return {
                x: evt.clientX - rect.left,
                y: evt.clientY - rect.top
            };
        }

        canvas.onclick = function(event) {
            const mousePos = getMousePos(canvas, event);
            let clickedOnObject = false;
            objects.forEach(obj => {
                 const objX = centerX + obj.radius * Math.cos(obj.angle * Math.PI / 180);
                 const objY = centerY - 15 + obj.radius * Math.sin(obj.angle * Math.PI / 180);
                 if (ctx.isPointInPath(obj.path, mousePos.x, mousePos.y)) {
                     explanationDiv.innerHTML = `<p>${obj.explanation}</p>`;
                     objects.forEach(o => o.draw(o, o === obj));
                     clickedOnObject = true;
                 }
            });
        };

        function drawSun(obj, isHighlighted) {
            const x = centerX + obj.radius * Math.cos(obj.angle * Math.PI / 180);
            const y = centerY - 15 + obj.radius * Math.sin(obj.angle * Math.PI / 180);
            obj.path = new Path2D();
            obj.path.arc(x, y, 20, 0, Math.PI * 2);
            ctx.fillStyle = colors.sun;
            ctx.fill(obj.path);
            if(isHighlighted) {
                ctx.strokeStyle = colors.primary;
                ctx.lineWidth = 3;
                ctx.stroke(obj.path);
            }
        }

        function drawRain(obj, isHighlighted) {
            const x = centerX + obj.radius * Math.cos(obj.angle * Math.PI / 180);
            const y = centerY - 15 + obj.radius * Math.sin(obj.angle * Math.PI / 180);
             obj.path = new Path2D();
             obj.path.rect(x-20, y-20, 40, 40);
             ctx.fillStyle = colors.cloud;
             ctx.fill(obj.path);
             ctx.strokeStyle = colors.rain;
             ctx.lineWidth = 2;
             for(let i=0; i<3; i++) {
                 ctx.beginPath();
                 ctx.moveTo(x - 10 + i*10, y + 5);
                 ctx.lineTo(x - 15 + i*10, y + 20);
                 ctx.stroke();
             }
             if(isHighlighted) {
                ctx.strokeStyle = colors.primary;
                ctx.lineWidth = 3;
                ctx.stroke(obj.path);
            }
        }
        
        function drawIdea(obj, isHighlighted) {
             const x = centerX + obj.radius * Math.cos(obj.angle * Math.PI / 180);
             const y = centerY - 15 + obj.radius * Math.sin(obj.angle * Math.PI / 180);
             obj.path = new Path2D();
             obj.path.rect(x-20, y-25, 40, 50); // Bounding box for click
             ctx.fillStyle = '#fff9c4'; // Light bulb color
             ctx.strokeStyle = '#757575';
             ctx.lineWidth = 2;
             // Draw bulb shape
             ctx.beginPath();
             ctx.arc(x, y, 15, Math.PI, 0);
             ctx.lineTo(x+8, y+15);
             ctx.lineTo(x-8, y+15);
             ctx.closePath();
             ctx.fill();
             ctx.stroke();
             // Draw base
             ctx.fillRect(x-8, y+15, 16, 5);
             if(isHighlighted) {
                ctx.strokeStyle = colors.primary;
                ctx.lineWidth = 3;
                ctx.stroke(obj.path);
            }
        }

        function drawObstacle(obj, isHighlighted) {
            const x = centerX + obj.radius * Math.cos(obj.angle * Math.PI / 180);
            const y = centerY - 15 + obj.radius * Math.sin(obj.angle * Math.PI / 180);
            obj.path = new Path2D();
            obj.path.rect(x - 25, y - 15, 50, 30);
            ctx.fillStyle = '#a1887f';
            ctx.strokeStyle = '#5d4037';
            ctx.lineWidth = 1;
            ctx.fillRect(x-25, y-15, 50, 30);
            // Draw brick lines
            for(let i = 0; i < 3; i++) {
                ctx.beginPath();
                ctx.moveTo(x-25, y-15 + i*10);
                ctx.lineTo(x+25, y-15 + i*10);
                ctx.stroke();
            }
             if(isHighlighted) {
                ctx.strokeStyle = colors.primary;
                ctx.lineWidth = 3;
                ctx.stroke(obj.path);
            }
        }

    }

    async function playAll() {
        resetAnimation();
        animateStance();
        await new Promise(resolve => setTimeout(resolve, 2000));
        animateCircum();
        await new Promise(resolve => setTimeout(resolve, 2000));
        animateCircumstance();
    }

    playBtn.addEventListener('click', playAll);
    stanceBtn.addEventListener('click', animateStance);
    circumBtn.addEventListener('click', animateCircum);
    circumstanceBtn.addEventListener('click', animateCircumstance);
    resetBtn.addEventListener('click', resetAnimation);

    // Initial draw
    drawInitialState();
</script>
</body>
</html> 