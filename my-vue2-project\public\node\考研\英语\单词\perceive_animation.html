<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Perceive</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            width: 100%;
            padding: 20px;
            box-sizing: border-box;
            text-align: center;
        }
        h1 {
            font-size: 3em;
            color: #007bff;
            margin-bottom: 10px;
        }
        .etymology {
            font-size: 1.2em;
            color: #555;
            margin-bottom: 20px;
        }
        .story {
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: left;
            line-height: 1.6;
        }
        .story h3 {
            color: #007bff;
            margin-top: 0;
        }
        canvas {
            display: block;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            cursor: none; /* Hide default cursor */
        }
        .controls {
            margin-top: 20px;
        }
        button {
            padding: 12px 25px;
            font-size: 1.1em;
            color: #fff;
            background-color: #007bff;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        button:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }
        button:disabled {
            background-color: #a0a0a0;
            cursor: not-allowed;
            transform: none;
        }
        .word-display {
            font-size: 2.5em;
            font-weight: bold;
            color: #28a745;
            margin-top: 20px;
            opacity: 0;
            transition: opacity 1s ease-in-out;
        }
        .word-display.visible {
            opacity: 1;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>Perceive</h1>
    <p class="etymology">
        词源: <strong>per-</strong> (through, thoroughly) + <strong>-ceive</strong> (to take, to seize)
    </p>

    <div class="story">
        <h3>动画故事：感知与理解</h3>
        <p>你好！这次我们来学习单词 <strong>perceive</strong>。想象一下，我们的大脑是如何理解这个世界的？它需要先"接收"信息，然后"彻底看透"这些信息。</p>
        <p>在这个动画里，屏幕上漂浮的模糊粒子代表着外界的零散信息。前缀 <strong>per-</strong> (彻底) 被具象化为一个你可以移动的"洞察透镜"，当你用它扫过粒子时，粒子会变得清晰，这模拟了我们集中注意力、深入观察的过程。</p>
        <p>词根 <strong>-ceive</strong> (抓住) 则化身为一只"理解之手"。当你点击按钮，这只手就会出现，将你看清的信息"抓住"并汇集起来，最终形成一个象征"顿悟"的灯泡。这个过程完整地演示了从"彻底抓住信息"到"形成感知与理解"的含义演变。</p>
    </div>

    <canvas id="wordCanvas" width="800" height="400"></canvas>

    <div class="controls">
        <button id="startAnimationBtn">开始感知 (Start Perceiving)</button>
    </div>

    <div id="wordDisplay" class="word-display">
        perceive: to notice, see, or recognize something; to understand or think of something in a particular way.
    </div>
</div>

<script>
const canvas = document.getElementById('wordCanvas');
const ctx = canvas.getContext('2d');
const startBtn = document.getElementById('startAnimationBtn');
const wordDisplay = document.getElementById('wordDisplay');

const particleCount = 100;
let particles = [];
let mouse = { x: -200, y: -200, radius: 80 };
let animationState = 'idle'; // idle, focusing, seizing, bulb, finished

// Lens (per-) object
const lens = {
    x: -200,
    y: -200,
    radius: 80,
    draw() {
        if (animationState === 'idle' || animationState === 'focusing') {
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(0, 123, 255, 0.5)';
            ctx.lineWidth = 4;
            ctx.stroke();
            ctx.fillStyle = 'rgba(0, 123, 255, 0.05)';
            ctx.fill();
        }
    }
};

// Hand (-ceive) object
const hand = {
    x: -200,
    y: canvas.height + 100,
    targetX: canvas.width / 2,
    targetY: canvas.height / 2,
    width: 80,
    height: 100,
    speed: 5,
    progress: 0,
    draw() {
        if (animationState !== 'seizing') return;
        ctx.save();
        ctx.translate(this.x, this.y);
        
        // Simple hand shape
        ctx.fillStyle = '#ffc107'; // A golden hand
        ctx.beginPath();
        // Palm
        ctx.roundRect(0, 20, this.width, this.height - 20, 10);
        // Fingers
        for (let i = 0; i < 4; i++) {
            ctx.roundRect(i * (this.width / 4) + 5, 0, 10, 40, 5);
        }
        // Thumb
        ctx.roundRect(-15, 40, 40, 15, 5);
        ctx.fill();

        ctx.restore();
    },
    update() {
        if (animationState === 'seizing') {
            let dx = this.targetX - this.x;
            let dy = this.targetY - this.y;
            let dist = Math.sqrt(dx*dx + dy*dy);
            if (dist > 1) {
                this.x += dx / dist * this.speed;
                this.y += dy / dist * this.speed;
            } else if (this.progress === 0) {
                this.progress = 1; // Reached center, start collecting
                collectParticles();
            }
        }
    }
};

// Bulb object
const bulb = {
    x: canvas.width / 2,
    y: canvas.height / 2,
    radius: 0,
    targetRadius: 50,
    glow: 0,
    draw() {
        if (animationState !== 'bulb') return;
        ctx.save();
        // Glow effect
        if (this.glow > 0) {
            ctx.shadowBlur = this.glow * 2;
            ctx.shadowColor = 'yellow';
        }
        
        // Bulb base
        ctx.fillStyle = '#aaa';
        ctx.fillRect(this.x - 15, this.y + this.radius - 10, 30, 20);

        // Bulb glass
        ctx.fillStyle = 'rgba(255, 255, 0, ' + (this.glow / this.targetRadius) + ')';
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.fill();

        ctx.restore();
    },
    update() {
        if (animationState === 'bulb') {
            if (this.radius < this.targetRadius) {
                this.radius += 1;
                this.glow = this.radius;
            } else {
                 setTimeout(() => {
                    animationState = 'finished';
                    wordDisplay.classList.add('visible');
                 }, 500);
            }
        }
    }
};


class Particle {
    constructor() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.size = Math.random() * 5 + 2;
        this.speedX = Math.random() * 1 - 0.5;
        this.speedY = Math.random() * 1 - 0.5;
        this.color = 'rgba(150, 150, 150, 0.5)';
        this.isFocused = false;
        this.target = null;
    }

    update() {
        if (this.target) {
            let dx = this.target.x - this.x;
            let dy = this.target.y - this.y;
            let dist = Math.sqrt(dx*dx + dy*dy);
            if(dist < 5) {
                this.x = this.target.x;
                this.y = this.target.y;
            } else {
                this.x += dx * 0.1;
                this.y += dy * 0.1;
            }
        } else {
            this.x += this.speedX;
            this.y += this.speedY;

            if (this.x > canvas.width || this.x < 0) this.speedX *= -1;
            if (this.y > canvas.height || this.y < 0) this.speedY *= -1;

            const dx = this.x - mouse.x;
            const dy = this.y - mouse.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            this.isFocused = distance < mouse.radius;
        }
    }

    draw() {
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        if (this.isFocused) {
            ctx.fillStyle = `rgba(0, 123, 255, 0.9)`;
            ctx.shadowBlur = 10;
            ctx.shadowColor = "cyan";
        } else {
            ctx.fillStyle = this.color;
            ctx.shadowBlur = 0;
        }
        ctx.fill();
        ctx.shadowBlur = 0;
    }
}

function init() {
    particles = [];
    for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle());
    }
    animationState = 'idle';
    wordDisplay.classList.remove('visible');
    startBtn.disabled = false;
    hand.x = -200;
    hand.y = canvas.height + 100;
    hand.progress = 0;
    bulb.radius = 0;
    bulb.glow = 0;
    animate();
}

function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    particles.forEach(p => {
        p.update();
        p.draw();
    });
    
    lens.x = mouse.x;
    lens.y = mouse.y;
    lens.draw();

    hand.update();
    hand.draw();
    
    bulb.update();
    bulb.draw();

    if(animationState !== 'finished') {
        requestAnimationFrame(animate);
    }
}

function collectParticles() {
    particles.forEach(p => {
        if (p.isFocused) {
            p.target = { x: bulb.x, y: bulb.y };
        } else {
            // Unfocused particles scatter away
            p.speedX = (Math.random() - 0.5) * 5;
            p.speedY = (Math.random() - 0.5) * 5;
        }
    });

    setTimeout(() => {
        // Remove unfocused particles
        particles = particles.filter(p => p.isFocused);
        animationState = 'bulb';
    }, 1000);
}

canvas.addEventListener('mousemove', (e) => {
    const rect = canvas.getBoundingClientRect();
    mouse.x = e.clientX - rect.left;
    mouse.y = e.clientY - rect.top;
});

canvas.addEventListener('mouseleave', () => {
    mouse.x = -200;
    mouse.y = -200;
})

startBtn.addEventListener('click', () => {
    if (animationState === 'idle' || animationState === 'focusing') {
        animationState = 'seizing';
        startBtn.disabled = true;
        hand.x = canvas.width / 2;
        hand.y = canvas.height + 100;
    } else if (animationState === 'finished') {
        init();
        startBtn.innerText = "开始感知 (Start Perceiving)";
    }
});


init();
</script>

</body>
</html> 