<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收益矩阵表 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.2);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes glow {
            from {
                text-shadow: 0 4px 20px rgba(255,255,255,0.3);
            }
            to {
                text-shadow: 0 4px 30px rgba(255,255,255,0.6);
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .highlight {
            animation: pulse 1s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">收益矩阵表</h1>
            <p class="subtitle">博弈论中的决策工具 - 互动学习体验</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是收益矩阵表？</h2>
            <div class="explanation">
                <strong>收益矩阵表</strong>是博弈论中用来分析多个参与者决策的工具。想象你和朋友在玩石头剪刀布，每种组合都有不同的结果，收益矩阵表就是把所有可能的结果整理成表格的形式。
            </div>
            <div class="canvas-container">
                <canvas id="introCanvas" width="600" height="300"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="startIntroAnimation()">开始动画演示</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">基础概念学习</h2>
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="700" height="400"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="nextStep()">下一步</button>
                <button class="interactive-btn" onclick="resetSteps()">重新开始</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">经典例题：囚徒困境</h2>
            <div class="explanation">
                两个囚徒被分别关押，无法沟通。每人都可以选择"合作"（不招供）或"背叛"（招供）。让我们看看不同选择的结果！
            </div>
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="500"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="playGame('cooperate', 'cooperate')">都合作</button>
                <button class="interactive-btn" onclick="playGame('cooperate', 'defect')">A合作 B背叛</button>
                <button class="interactive-btn" onclick="playGame('defect', 'cooperate')">A背叛 B合作</button>
                <button class="interactive-btn" onclick="playGame('defect', 'defect')">都背叛</button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let animationId;

        // 初始化画布
        function initCanvas() {
            drawIntroCanvas();
            drawConceptCanvas();
            drawGameCanvas();
        }

        // 介绍画布
        function drawIntroCanvas() {
            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('收益矩阵表的基本结构', canvas.width/2, 40);

            // 绘制简单的2x2矩阵框架
            const startX = 200;
            const startY = 80;
            const cellWidth = 100;
            const cellHeight = 60;

            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;

            // 绘制表格
            for(let i = 0; i <= 2; i++) {
                ctx.beginPath();
                ctx.moveTo(startX + i * cellWidth, startY);
                ctx.lineTo(startX + i * cellWidth, startY + 2 * cellHeight);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(startX, startY + i * cellHeight);
                ctx.lineTo(startX + 2 * cellWidth, startY + i * cellHeight);
                ctx.stroke();
            }

            // 添加标签
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.fillText('参与者A的策略', startX - 80, startY + cellHeight);
            ctx.fillText('参与者B的策略', startX + cellWidth, startY - 20);
        }

        // 开始介绍动画
        function startIntroAnimation() {
            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('收益矩阵表的基本结构', canvas.width/2, 40);

                const startX = 200;
                const startY = 80;
                const cellWidth = 100;
                const cellHeight = 60;

                // 动画绘制表格
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;

                const progress = Math.min(frame / 60, 1);

                // 绘制表格线条（带动画效果）
                for(let i = 0; i <= 2; i++) {
                    if(progress > i * 0.2) {
                        ctx.beginPath();
                        ctx.moveTo(startX + i * cellWidth, startY);
                        ctx.lineTo(startX + i * cellWidth, startY + 2 * cellHeight);
                        ctx.stroke();
                    }

                    if(progress > 0.6 + i * 0.1) {
                        ctx.beginPath();
                        ctx.moveTo(startX, startY + i * cellHeight);
                        ctx.lineTo(startX + 2 * cellWidth, startY + i * cellHeight);
                        ctx.stroke();
                    }
                }

                // 添加闪烁的标签
                if(progress > 0.8) {
                    const alpha = 0.5 + 0.5 * Math.sin(frame * 0.1);
                    ctx.globalAlpha = alpha;
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillStyle = '#667eea';
                    ctx.fillText('参与者A的策略', startX - 80, startY + cellHeight);
                    ctx.fillText('参与者B的策略', startX + cellWidth, startY - 20);
                    ctx.globalAlpha = 1;
                }

                frame++;
                if(frame < 120) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 概念学习画布
        function drawConceptCanvas() {
            const canvas = document.getElementById('conceptCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const startX = 150;
            const startY = 100;
            const cellWidth = 120;
            const cellHeight = 80;

            // 根据当前步骤绘制不同内容
            switch(currentStep) {
                case 1:
                    drawStep1(ctx, startX, startY, cellWidth, cellHeight);
                    break;
                case 2:
                    drawStep2(ctx, startX, startY, cellWidth, cellHeight);
                    break;
                case 3:
                    drawStep3(ctx, startX, startY, cellWidth, cellHeight);
                    break;
                case 4:
                    drawStep4(ctx, startX, startY, cellWidth, cellHeight);
                    break;
            }
        }

        function drawStep1(ctx, startX, startY, cellWidth, cellHeight) {
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('步骤1: 理解参与者', 350, 50);

            // 绘制两个参与者
            ctx.fillStyle = '#667eea';
            ctx.fillRect(100, 150, 100, 100);
            ctx.fillRect(450, 150, 100, 100);

            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('参与者A', 150, 205);
            ctx.fillText('参与者B', 500, 205);

            // 绘制箭头
            ctx.strokeStyle = '#764ba2';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(220, 200);
            ctx.lineTo(430, 200);
            ctx.stroke();

            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(420, 190);
            ctx.lineTo(430, 200);
            ctx.lineTo(420, 210);
            ctx.stroke();

            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('互相影响的决策', 325, 180);
        }

        function drawStep2(ctx, startX, startY, cellWidth, cellHeight) {
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('步骤2: 策略选择', 350, 50);

            // 绘制策略选项
            ctx.fillStyle = '#667eea';
            ctx.fillRect(50, 120, 120, 60);
            ctx.fillRect(50, 200, 120, 60);

            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('策略A1', 110, 155);
            ctx.fillText('策略A2', 110, 235);

            ctx.fillStyle = '#764ba2';
            ctx.fillRect(480, 120, 120, 60);
            ctx.fillRect(480, 200, 120, 60);

            ctx.fillStyle = 'white';
            ctx.fillText('策略B1', 540, 155);
            ctx.fillText('策略B2', 540, 235);

            // 中间说明
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('每个参与者都有', 350, 140);
            ctx.fillText('多种策略可选择', 350, 160);
        }

        function drawStep3(ctx, startX, startY, cellWidth, cellHeight) {
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('步骤3: 构建矩阵', 350, 50);

            // 绘制矩阵框架
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;

            for(let i = 0; i <= 2; i++) {
                ctx.beginPath();
                ctx.moveTo(startX + i * cellWidth, startY);
                ctx.lineTo(startX + i * cellWidth, startY + 2 * cellHeight);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(startX, startY + i * cellHeight);
                ctx.lineTo(startX + 2 * cellWidth, startY + i * cellHeight);
                ctx.stroke();
            }

            // 添加标签
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#667eea';
            ctx.fillText('B策略1', startX + cellWidth/2, startY - 10);
            ctx.fillText('B策略2', startX + cellWidth + cellWidth/2, startY - 10);

            ctx.save();
            ctx.translate(startX - 30, startY + cellHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('A策略1', 0, 0);
            ctx.restore();

            ctx.save();
            ctx.translate(startX - 30, startY + cellHeight + cellHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('A策略2', 0, 0);
            ctx.restore();
        }

        function drawStep4(ctx, startX, startY, cellWidth, cellHeight) {
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('步骤4: 填入收益值', 350, 50);

            // 绘制完整的矩阵
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;

            for(let i = 0; i <= 2; i++) {
                ctx.beginPath();
                ctx.moveTo(startX + i * cellWidth, startY);
                ctx.lineTo(startX + i * cellWidth, startY + 2 * cellHeight);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(startX, startY + i * cellHeight);
                ctx.lineTo(startX + 2 * cellWidth, startY + i * cellHeight);
                ctx.stroke();
            }

            // 填入示例收益值
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';

            // 格子内容 (A收益, B收益)
            ctx.fillText('(3, 3)', startX + cellWidth/2, startY + cellHeight/2 + 5);
            ctx.fillText('(0, 5)', startX + cellWidth + cellWidth/2, startY + cellHeight/2 + 5);
            ctx.fillText('(5, 0)', startX + cellWidth/2, startY + cellHeight + cellHeight/2 + 5);
            ctx.fillText('(1, 1)', startX + cellWidth + cellWidth/2, startY + cellHeight + cellHeight/2 + 5);

            // 添加标签
            ctx.font = '12px Microsoft YaHei';
            ctx.fillStyle = '#667eea';
            ctx.fillText('合作', startX + cellWidth/2, startY - 10);
            ctx.fillText('背叛', startX + cellWidth + cellWidth/2, startY - 10);

            ctx.save();
            ctx.translate(startX - 40, startY + cellHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('合作', 0, 0);
            ctx.restore();

            ctx.save();
            ctx.translate(startX - 40, startY + cellHeight + cellHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('背叛', 0, 0);
            ctx.restore();
        }

        // 下一步函数
        function nextStep() {
            if(currentStep < 4) {
                // 更新步骤指示器
                document.getElementById(`step${currentStep}`).classList.remove('active');
                currentStep++;
                document.getElementById(`step${currentStep}`).classList.add('active');

                // 重绘画布
                drawConceptCanvas();
            }
        }

        // 重置步骤
        function resetSteps() {
            document.getElementById(`step${currentStep}`).classList.remove('active');
            currentStep = 1;
            document.getElementById('step1').classList.add('active');
            drawConceptCanvas();
        }

        // 游戏画布
        function drawGameCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制标题
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('囚徒困境 - 收益矩阵', canvas.width/2, 40);

            const startX = 250;
            const startY = 100;
            const cellWidth = 150;
            const cellHeight = 100;

            // 绘制矩阵
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;

            for(let i = 0; i <= 2; i++) {
                ctx.beginPath();
                ctx.moveTo(startX + i * cellWidth, startY);
                ctx.lineTo(startX + i * cellWidth, startY + 2 * cellHeight);
                ctx.stroke();

                ctx.beginPath();
                ctx.moveTo(startX, startY + i * cellHeight);
                ctx.lineTo(startX + 2 * cellWidth, startY + i * cellHeight);
                ctx.stroke();
            }

            // 添加标签
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#667eea';
            ctx.fillText('B合作', startX + cellWidth/2, startY - 15);
            ctx.fillText('B背叛', startX + cellWidth + cellWidth/2, startY - 15);

            ctx.save();
            ctx.translate(startX - 50, startY + cellHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('A合作', 0, 0);
            ctx.restore();

            ctx.save();
            ctx.translate(startX - 50, startY + cellHeight + cellHeight/2);
            ctx.rotate(-Math.PI/2);
            ctx.fillText('A背叛', 0, 0);
            ctx.restore();

            // 填入收益值
            ctx.font = '20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';

            // 格子内容
            ctx.fillText('A: -1年', startX + cellWidth/2, startY + cellHeight/2 - 10);
            ctx.fillText('B: -1年', startX + cellWidth/2, startY + cellHeight/2 + 15);

            ctx.fillText('A: -3年', startX + cellWidth + cellWidth/2, startY + cellHeight/2 - 10);
            ctx.fillText('B: 0年', startX + cellWidth + cellWidth/2, startY + cellHeight/2 + 15);

            ctx.fillText('A: 0年', startX + cellWidth/2, startY + cellHeight + cellHeight/2 - 10);
            ctx.fillText('B: -3年', startX + cellWidth/2, startY + cellHeight + cellHeight/2 + 15);

            ctx.fillText('A: -2年', startX + cellWidth + cellWidth/2, startY + cellHeight + cellHeight/2 - 10);
            ctx.fillText('B: -2年', startX + cellWidth + cellWidth/2, startY + cellHeight + cellHeight/2 + 15);

            // 添加说明
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.fillText('点击上方按钮查看不同策略组合的结果', canvas.width/2, 350);
            ctx.fillText('负数表示监禁年数，0表示释放', canvas.width/2, 370);
        }

        // 游戏交互
        function playGame(playerA, playerB) {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            // 重绘基础矩阵
            drawGameCanvas();

            const startX = 250;
            const startY = 100;
            const cellWidth = 150;
            const cellHeight = 100;

            // 确定高亮的格子
            let highlightX, highlightY;
            let resultText = '';

            if(playerA === 'cooperate' && playerB === 'cooperate') {
                highlightX = startX;
                highlightY = startY;
                resultText = '双方合作：都被判1年！相对最好的结果';
            } else if(playerA === 'cooperate' && playerB === 'defect') {
                highlightX = startX + cellWidth;
                highlightY = startY;
                resultText = 'A合作B背叛：A被判3年，B释放！A吃亏了';
            } else if(playerA === 'defect' && playerB === 'cooperate') {
                highlightX = startX;
                highlightY = startY + cellHeight;
                resultText = 'A背叛B合作：A释放，B被判3年！B吃亏了';
            } else {
                highlightX = startX + cellWidth;
                highlightY = startY + cellHeight;
                resultText = '双方背叛：都被判2年！都不信任的结果';
            }

            // 高亮选中的格子
            ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
            ctx.fillRect(highlightX, highlightY, cellWidth, cellHeight);

            // 绘制边框
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 5;
            ctx.strokeRect(highlightX, highlightY, cellWidth, cellHeight);

            // 显示结果文本
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText(resultText, canvas.width/2, 420);

            // 添加动画效果
            let pulseFrame = 0;
            function pulseAnimation() {
                if(pulseFrame < 30) {
                    const alpha = 0.1 + 0.2 * Math.sin(pulseFrame * 0.3);
                    ctx.fillStyle = `rgba(102, 126, 234, ${alpha})`;
                    ctx.fillRect(highlightX, highlightY, cellWidth, cellHeight);

                    pulseFrame++;
                    requestAnimationFrame(pulseAnimation);
                }
            }
            pulseAnimation();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initCanvas();
        });
    </script>
</body>
</html>
