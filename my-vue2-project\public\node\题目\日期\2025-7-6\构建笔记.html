<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的私密笔记</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #0056b3;
            text-align: center;
        }
        textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .note-item {
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .note-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        .note-content {
            white-space: pre-wrap; /* Preserve formatting */
            word-wrap: break-word; /* Break long words */
        }
        .note-content.hidden {
            display: none;
        }
        .toggle-button {
            background-color: #6c757d;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-left: 10px;
        }
        .toggle-button:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>我的私密笔记</h1>
        <textarea id="noteInput" rows="10" placeholder="在这里输入你的笔记..."></textarea>
        <button id="saveNoteBtn">保存笔记</button>
        <div id="notesList">
            <!-- Notes will be loaded here -->
        </div>
    </div>

    <script>
        const noteInput = document.getElementById('noteInput');
        const saveNoteBtn = document.getElementById('saveNoteBtn');
        const notesList = document.getElementById('notesList');

        let notes = JSON.parse(localStorage.getItem('notes')) || [];

        function saveNotes() {
            localStorage.setItem('notes', JSON.stringify(notes));
        }

        function renderNotes() {
            notesList.innerHTML = ''; // Clear existing notes
            notes.forEach((note, index) => {
                const noteItem = document.createElement('div');
                noteItem.classList.add('note-item');

                const noteHeader = document.createElement('div');
                noteHeader.classList.add('note-header');

                const noteTimestamp = document.createElement('span');
                noteTimestamp.textContent = new Date(note.timestamp).toLocaleString();
                noteHeader.appendChild(noteTimestamp);

                const actionsDiv = document.createElement('div');

                const toggleButton = document.createElement('button');
                toggleButton.classList.add('toggle-button');
                toggleButton.textContent = '隐藏内容';
                toggleButton.addEventListener('click', () => toggleNoteVisibility(index));
                actionsDiv.appendChild(toggleButton);

                const deleteButton = document.createElement('button');
                deleteButton.classList.add('toggle-button'); // Reusing style, could be a new class
                deleteButton.textContent = '删除';
                deleteButton.style.backgroundColor = '#dc3545';
                deleteButton.style.marginLeft = '5px';
                deleteButton.addEventListener('click', () => deleteNote(index));
                actionsDiv.appendChild(deleteButton);


                noteHeader.appendChild(actionsDiv);
                noteItem.appendChild(noteHeader);

                const noteContent = document.createElement('div');
                noteContent.classList.add('note-content');
                noteContent.textContent = note.content;
                if (note.hidden) {
                    noteContent.classList.add('hidden');
                    toggleButton.textContent = '显示内容';
                }
                noteItem.appendChild(noteContent);

                notesList.appendChild(noteItem);
            });
        }

        function addNote() {
            const content = noteInput.value.trim();
            if (content) {
                notes.unshift({
                    content: content,
                    timestamp: new Date().toISOString(),
                    hidden: false // New notes are visible by default
                });
                noteInput.value = '';
                saveNotes();
                renderNotes();
            }
        }

        function toggleNoteVisibility(index) {
            notes[index].hidden = !notes[index].hidden;
            saveNotes();
            renderNotes();
        }

        function deleteNote(index) {
            notes.splice(index, 1);
            saveNotes();
            renderNotes();
        }

        saveNoteBtn.addEventListener('click', addNote);

        // Initial render
        renderNotes();
    </script>
</body>
</html> 