<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息中间件学习游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
        }

        .game-area {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 30px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .option.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-color: #4CAF50;
        }

        .option.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            border-color: #f44336;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #gameCanvas {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            border-left: 5px solid #2196F3;
        }

        .explanation h3 {
            color: #1976D2;
            margin-bottom: 15px;
        }

        .middleware-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .middleware-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .middleware-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .middleware-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .score {
            text-align: center;
            font-size: 1.2rem;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 消息中间件学习游戏</h1>
            <p class="subtitle">通过动画和交互学习消息中间件概念</p>
        </div>

        <div class="game-area">
            <div class="question-section">
                <h2 class="question-title">📝 题目练习</h2>
                <p style="text-align: center; margin-bottom: 20px; color: #666;">
                    通常，嵌入式中间件没有统一的架构风格，根据应用对象的不同可存在多种类型，比较常见的是消息中间件和分布式对象中间件。以下有关消息中间件的描述中，<strong>不正确</strong>的是（ ）。
                </p>
                
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 消息中间件是消息传输过程中保存消息的一种容器
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 消息中间件具有两个基本特点：采用异步处理模式、应用程序和应用程序调用关系为松耦合关系
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 消息中间件主要由一组对象来提供系统服务，对象间能够跨平台通信
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 消息中间件的消息传递服务模型有点对点模型和发布-订阅模型之分
                    </div>
                </div>

                <div class="score" id="score">得分: 0</div>
            </div>

            <div class="canvas-container">
                <h3 style="margin-bottom: 15px; color: #333;">🎮 交互式消息中间件演示</h3>
                <canvas id="gameCanvas" width="800" height="400"></canvas>
                <div class="controls">
                    <button class="btn" onclick="startDemo('pointToPoint')">点对点模式</button>
                    <button class="btn" onclick="startDemo('pubSub')">发布-订阅模式</button>
                    <button class="btn" onclick="startDemo('distributed')">分布式对象</button>
                    <button class="btn" onclick="resetDemo()">重置演示</button>
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>📚 知识点解析</h3>
            <div class="middleware-types">
                <div class="middleware-card">
                    <div class="middleware-icon">📨</div>
                    <h4>消息中间件</h4>
                    <p>异步通信、松耦合、消息队列</p>
                </div>
                <div class="middleware-card">
                    <div class="middleware-icon">🔗</div>
                    <h4>分布式对象中间件</h4>
                    <p>对象服务、跨平台通信</p>
                </div>
            </div>
            
            <p><strong>正确答案：C</strong></p>
            <p>选项C描述的是<strong>分布式对象中间件</strong>的特征，而不是消息中间件的特征。</p>
            
            <h4>消息中间件的特点：</h4>
            <ul style="margin-left: 20px; margin-top: 10px;">
                <li>✅ 是消息传输过程中保存消息的容器（消息队列）</li>
                <li>✅ 采用异步处理模式</li>
                <li>✅ 应用程序间松耦合关系</li>
                <li>✅ 支持点对点和发布-订阅模型</li>
                <li>❌ 不是通过对象来提供服务（这是分布式对象中间件的特征）</li>
            </ul>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentDemo = null;
        let score = 0;
        let answered = false;

        // 动画对象
        class AnimatedObject {
            constructor(x, y, color, label) {
                this.x = x;
                this.y = y;
                this.targetX = x;
                this.targetY = y;
                this.color = color;
                this.label = label;
                this.size = 40;
                this.pulse = 0;
            }

            update() {
                this.x += (this.targetX - this.x) * 0.1;
                this.y += (this.targetY - this.y) * 0.1;
                this.pulse += 0.1;
            }

            draw() {
                ctx.save();
                ctx.fillStyle = this.color;
                const pulseSize = this.size + Math.sin(this.pulse) * 5;

                // 绘制圆形
                ctx.beginPath();
                ctx.arc(this.x, this.y, pulseSize / 2, 0, Math.PI * 2);
                ctx.fill();

                // 绘制标签
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(this.label, this.x, this.y + 4);
                ctx.restore();
            }

            moveTo(x, y) {
                this.targetX = x;
                this.targetY = y;
            }
        }

        // 消息对象
        class Message {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.targetX = x;
                this.targetY = y;
                this.color = '#FF6B6B';
                this.size = 20;
                this.visible = true;
            }

            update() {
                this.x += (this.targetX - this.x) * 0.05;
                this.y += (this.targetY - this.y) * 0.05;
            }

            draw() {
                if (!this.visible) return;
                ctx.save();
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.roundRect(this.x - this.size/2, this.y - this.size/2, this.size, this.size, 5);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('📧', this.x, this.y + 3);
                ctx.restore();
            }

            moveTo(x, y) {
                this.targetX = x;
                this.targetY = y;
            }
        }

        let objects = [];
        let messages = [];

        function initCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 重绘背景网格
            ctx.strokeStyle = '#f0f0f0';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 更新和绘制对象
            objects.forEach(obj => {
                obj.update();
                obj.draw();
            });

            messages.forEach(msg => {
                msg.update();
                msg.draw();
            });

            animationId = requestAnimationFrame(animate);
        }

        function startDemo(type) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            objects = [];
            messages = [];
            currentDemo = type;

            switch(type) {
                case 'pointToPoint':
                    setupPointToPointDemo();
                    break;
                case 'pubSub':
                    setupPubSubDemo();
                    break;
                case 'distributed':
                    setupDistributedDemo();
                    break;
            }

            animate();
        }

        function setupPointToPointDemo() {
            // 创建发送者、消息队列、接收者
            const sender = new AnimatedObject(100, 200, '#4CAF50', '发送者');
            const queue = new AnimatedObject(400, 200, '#FF9800', '消息队列');
            const receiver = new AnimatedObject(700, 200, '#2196F3', '接收者');

            objects.push(sender, queue, receiver);

            // 模拟消息传递
            setTimeout(() => {
                const msg = new Message(100, 200);
                messages.push(msg);
                msg.moveTo(400, 200);

                setTimeout(() => {
                    msg.moveTo(700, 200);
                    setTimeout(() => {
                        msg.visible = false;
                    }, 2000);
                }, 2000);
            }, 1000);
        }

        function setupPubSubDemo() {
            // 创建发布者、消息中间件、多个订阅者
            const publisher = new AnimatedObject(100, 200, '#E91E63', '发布者');
            const middleware = new AnimatedObject(400, 200, '#FF9800', '消息中间件');
            const sub1 = new AnimatedObject(650, 120, '#2196F3', '订阅者1');
            const sub2 = new AnimatedObject(650, 200, '#2196F3', '订阅者2');
            const sub3 = new AnimatedObject(650, 280, '#2196F3', '订阅者3');

            objects.push(publisher, middleware, sub1, sub2, sub3);

            // 模拟消息广播
            setTimeout(() => {
                const msg1 = new Message(100, 200);
                const msg2 = new Message(100, 200);
                const msg3 = new Message(100, 200);
                messages.push(msg1, msg2, msg3);

                msg1.moveTo(400, 200);
                msg2.moveTo(400, 200);
                msg3.moveTo(400, 200);

                setTimeout(() => {
                    msg1.moveTo(650, 120);
                    msg2.moveTo(650, 200);
                    msg3.moveTo(650, 280);

                    setTimeout(() => {
                        messages.forEach(msg => msg.visible = false);
                    }, 2000);
                }, 2000);
            }, 1000);
        }

        function setupDistributedDemo() {
            // 创建分布式对象
            const obj1 = new AnimatedObject(150, 150, '#9C27B0', '对象A');
            const obj2 = new AnimatedObject(400, 100, '#9C27B0', '对象B');
            const obj3 = new AnimatedObject(650, 150, '#9C27B0', '对象C');
            const obj4 = new AnimatedObject(400, 300, '#9C27B0', '对象D');

            objects.push(obj1, obj2, obj3, obj4);

            // 绘制连接线表示对象间通信
            setTimeout(() => {
                setInterval(() => {
                    ctx.strokeStyle = '#9C27B0';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);

                    // 绘制对象间的连接
                    ctx.beginPath();
                    ctx.moveTo(150, 150);
                    ctx.lineTo(400, 100);
                    ctx.stroke();

                    ctx.beginPath();
                    ctx.moveTo(400, 100);
                    ctx.lineTo(650, 150);
                    ctx.stroke();

                    ctx.beginPath();
                    ctx.moveTo(150, 150);
                    ctx.lineTo(400, 300);
                    ctx.stroke();

                    ctx.setLineDash([]);
                }, 100);
            }, 500);
        }

        function resetDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            objects = [];
            messages = [];
            currentDemo = null;
            initCanvas();
        }

        // 题目交互
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                if (answered) return;

                const answer = this.dataset.answer;
                answered = true;

                document.querySelectorAll('.option').forEach(opt => {
                    if (opt.dataset.answer === 'C') {
                        opt.classList.add('correct');
                    } else if (opt === this && answer !== 'C') {
                        opt.classList.add('wrong');
                    }
                });

                if (answer === 'C') {
                    score += 100;
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n选项C描述的是分布式对象中间件的特征，不是消息中间件的特征。');
                    }, 500);
                } else {
                    setTimeout(() => {
                        alert('❌ 答错了！\n\n正确答案是C。选项C描述的是分布式对象中间件的特征，而不是消息中间件的特征。');
                    }, 500);
                }

                document.getElementById('score').textContent = `得分: ${score}`;
            });
        });

        // 初始化
        initCanvas();

        // 添加欢迎动画
        setTimeout(() => {
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击按钮开始学习消息中间件！', canvas.width/2, canvas.height/2);
        }, 500);
    </script>
</body>
</html>
