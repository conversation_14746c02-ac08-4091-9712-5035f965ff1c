<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Subscribe</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            background-color: #f0f2f5;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 900px;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .story-explanation {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            width: 100%;
            border-left: 5px solid #5cb85c;
        }

        .story-explanation p {
            margin: 0;
            line-height: 1.6;
            color: #555;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            border-radius: 8px;
            overflow: hidden;
        }

        canvas {
            display: block;
            width: 100%;
            height: auto;
            background-color: #ffffff;
        }
        
        .controls {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        button {
            padding: 10px 20px;
            font-size: 1em;
            color: #fff;
            background-color: #5cb85c;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        button:hover {
            background-color: #4cae4c;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }

        #explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 8px;
            width: 100%;
            text-align: center;
            font-size: 1.2em;
            color: #333;
            min-height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: background-color 0.5s;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>
        subscribe
        <span style="font-size: 0.5em; color: #555;">(sub- + scribe)</span>
    </h1>

    <div class="story-explanation">
        <p><strong>故事背景：</strong>想象一下，你在一份重要的文件（比如一份期刊订阅单或一份协议）的末尾签下你的名字。你的动作是在文件的文字内容<strong>下方</strong>(sub-)<strong>书写</strong>(scribe)你的承诺。这个动作就是'subscribe'的本意。</p>
    </div>

    <div class="canvas-container">
        <canvas id="wordAnimation" width="800" height="450"></canvas>
    </div>

    <div class="controls">
        <button id="playBtn">播放完整动画</button>
        <button id="subBtn">第一幕: sub- (在...下面)</button>
        <button id="scribeBtn">第二幕: scribe (写)</button>
        <button id="subscribeBtn">第三幕: subscribe (订阅)</button>
        <button id="resetBtn">重置</button>
    </div>

    <div id="explanation">
        <p>点击按钮，开始探索 "subscribe" 的含义吧！</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordAnimation');
    const ctx = canvas.getContext('2d');
    const explanationDiv = document.getElementById('explanation');

    const playBtn = document.getElementById('playBtn');
    const subBtn = document.getElementById('subBtn');
    const scribeBtn = document.getElementById('scribeBtn');
    const subscribeBtn = document.getElementById('subscribeBtn');
    const resetBtn = document.getElementById('resetBtn');

    let animationFrameId;

    const colors = {
        background: '#ffffff',
        document: '#fdfdfd',
        text: '#333333',
        line: '#cccccc',
        signature: '#005a9c',
        highlight: 'rgba(92, 184, 92, 0.3)'
    };
    
    const fonts = {
        title: 'bold 36px Arial',
        text: '24px Arial',
        chinese: '20px "Microsoft YaHei", sans-serif'
    };
    
    const documentRect = { x: 200, y: 100, width: 400, height: 250 };
    const signatureLineY = documentRect.y + documentRect.height - 40;
    const signatureStart = { x: documentRect.x + 50, y: signatureLineY };
    const signatureEnd = { x: documentRect.x + documentRect.width - 50, y: signatureLineY };


    function drawDocument() {
        ctx.fillStyle = colors.document;
        ctx.shadowColor = 'rgba(0,0,0,0.1)';
        ctx.shadowBlur = 10;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 4;
        ctx.fillRect(documentRect.x, documentRect.y, documentRect.width, documentRect.height);
        ctx.shadowColor = 'transparent';

        // Draw some dummy text lines
        ctx.strokeStyle = colors.line;
        ctx.lineWidth = 1;
        for (let i = 0; i < 6; i++) {
            ctx.beginPath();
            ctx.moveTo(documentRect.x + 30, documentRect.y + 30 + i * 20);
            ctx.lineTo(documentRect.x + documentRect.width - 30, documentRect.y + 30 + i * 20);
            ctx.stroke();
        }
        
        // Draw signature line
        ctx.beginPath();
        ctx.moveTo(signatureStart.x, signatureLineY);
        ctx.lineTo(signatureEnd.x, signatureLineY);
        ctx.stroke();
    }

    function drawInitialState() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        drawDocument();
        ctx.fillStyle = colors.text;
        ctx.font = fonts.title;
        ctx.textAlign = 'center';
        ctx.fillText('subscribe', canvas.width / 2, 60);
        explanationDiv.innerHTML = '<p>点击按钮，开始探索 "subscribe" 的含义吧！</p>';
    }
    
    function resetAnimation() {
        cancelAnimationFrame(animationFrameId);
        drawInitialState();
    }

    // 第一幕: sub-
    function animateSub() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>sub- (under, 在...下面)</strong>: 这个前缀意味着"在...下面"。动画将高亮文件下方的签名区域。</p>';
        
        let alpha = 0;
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawDocument();
            
            ctx.fillStyle = `rgba(92, 184, 92, ${alpha})`;
            ctx.fillRect(documentRect.x, signatureLineY - 30, documentRect.width, 50);

            ctx.fillStyle = colors.text;
            ctx.font = fonts.text;
            ctx.textAlign = 'center';
            ctx.fillText('sub- (在...下面)', canvas.width / 2, signatureLineY + 60);

            if (alpha < 0.3) {
                alpha += 0.01;
                animationFrameId = requestAnimationFrame(animate);
            }
        }
        animate();
    }

    // 第二幕: scribe
    function animateScribe() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>scribe (write, 写)</strong>: 这个词根意味着"写"。动画将演示签名的过程。</p>';
        let progress = 0;
        const pen = { x: signatureStart.x, y: signatureStart.y - 20, color: colors.signature };

        function animate() {
            if (progress > 1) {
                 ctx.fillStyle = colors.text;
                 ctx.font = fonts.text;
                 ctx.textAlign = 'center';
                 ctx.fillText('scribe (写)', canvas.width / 2, signatureLineY + 60);
                return;
            }
            progress += 0.01;

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawDocument();

            // Draw signature
            ctx.strokeStyle = colors.signature;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(signatureStart.x, signatureStart.y);
            const currentX = signatureStart.x + (signatureEnd.x - signatureStart.x) * progress;
            const currentY = signatureStart.y + Math.sin(progress * Math.PI * 4) * 10; // Scribble effect
            ctx.lineTo(currentX, currentY);
            ctx.stroke();

            // Draw pen
            ctx.fillStyle = pen.color;
            ctx.beginPath();
            ctx.arc(currentX, currentY-5, 5, 0, Math.PI * 2);
            ctx.fill();

            animationFrameId = requestAnimationFrame(animate);
        }
        animate();
    }

    // 第三幕: subscribe
    function animateSubscribe() {
        resetAnimation();
        explanationDiv.innerHTML = '<p><strong>subscribe (订阅/签署)</strong>: "在下面写上名字" -> 意味着你同意并加入。这就是订阅！</p>';

        // Draw final state directly
        drawDocument();
        ctx.strokeStyle = colors.signature;
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(signatureStart.x, signatureStart.y);
        let lastX = signatureStart.x, lastY = signatureStart.y;
        for(let i=0; i<=1; i+=0.01){
             const currentX = signatureStart.x + (signatureEnd.x - signatureStart.x) * i;
             const currentY = signatureStart.y + Math.sin(i * Math.PI * 4) * 10;
             ctx.moveTo(lastX, lastY);
             ctx.lineTo(currentX, currentY);
             lastX = currentX;
             lastY = currentY;
        }
        ctx.stroke();

        ctx.fillStyle = colors.text;
        ctx.font = fonts.title;
        ctx.textAlign = 'center';
        ctx.fillText('subscribe', canvas.width / 2, 60);
        
        ctx.font = fonts.chinese;
        ctx.fillText('(订阅/签署)', canvas.width / 2, 100);
    }

    async function playAll() {
        resetAnimation();
        
        animateSub();
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        animateScribe();
        await new Promise(resolve => setTimeout(resolve, 2500));

        animateSubscribe();
    }

    playBtn.addEventListener('click', playAll);
    subBtn.addEventListener('click', animateSub);
    scribeBtn.addEventListener('click', animateScribe);
    subscribeBtn.addEventListener('click', animateSubscribe);
    resetBtn.addEventListener('click', resetAnimation);

    // Initial Draw
    drawInitialState();
</script>
</body>
</html> 