<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考研句子词缀故事动画</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(to right top, #65dfc9, #6cdbeb);
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 10px;
        }
        .sentence {
            font-size: 1.3em;
            margin: 20px 0;
            color: #34495e;
            font-weight: bold;
        }
        canvas {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            cursor: pointer;
            display: block;
            margin: 0 auto 20px auto;
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        .controls button {
            padding: 10px 20px;
            font-size: 1em;
            border: none;
            border-radius: 8px;
            background-color: #3498db;
            color: white;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08);
        }
        .controls button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        .controls button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        .explanation {
            min-height: 80px;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #3498db;
            text-align: left;
            line-height: 1.6;
        }
        .footer {
            margin-top: 20px;
            font-size: 0.9em;
            color: #7f8c8d;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>考研句子词缀故事动画</h1>
    <p class="sentence">The <strong>international</strong> <strong>community</strong> has made <strong>unprecedented</strong> <strong>efforts</strong> to <strong>resolve</strong> the <strong>conflict</strong>.</p>
    <canvas id="wordCanvas" width="800" height="300"></canvas>
    <div class="controls">
        <button id="prevBtn">上一个单词</button>
        <button id="nextBtn">下一个单词</button>
    </div>
    <div class="explanation" id="explanationBox">
        <p>点击按钮开始学习，动画将为您生动演示每个重点单词的词缀故事。</p>
    </div>
    <div class="footer">
        <p>这是一个帮助您通过故事理解和记忆英语单词的工具。</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordCanvas');
    const ctx = canvas.getContext('2d');
    const explanationBox = document.getElementById('explanationBox');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    const wordsData = [
        {
            word: 'international',
            parts: [
                { text: 'inter-', meaning: '之间', story: '词根nation(国家)前，加上前缀inter-(之间)，就有了国家之间的含义。' },
                { text: 'nation', meaning: '国家', story: '这是一个核心词根，代表"国家"或"民族"。' },
                { text: 'al', meaning: '(形容词后缀)', story: '最后加上形容词后缀-al(…的)，就构成了"国际的"。' }
            ],
            fullStory: '<strong>international (国际的):</strong> 在一个分裂的世界里，各个<strong>国家(nation)</strong> <strong>之间(inter-)</strong>需要沟通，才能形成一个国际大家庭。',
            animation: (progress) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';

                // Draw two globes
                const globe1X = 200;
                const globe2X = 600;
                const y = 150;
                const radius = 50;

                ctx.fillStyle = '#3498db';
                ctx.beginPath();
                ctx.arc(globe1X, y, radius, 0, Math.PI * 2);
                ctx.fill();
                ctx.beginPath();
                ctx.arc(globe2X, y, radius, 0, Math.PI * 2);
                ctx.fill();

                // Draw connecting line based on progress
                if (progress > 0.3) {
                    const lineProgress = Math.min(1, (progress - 0.3) / 0.4);
                    ctx.beginPath();
                    ctx.moveTo(globe1X + radius, y);
                    ctx.lineTo(globe1X + radius + (globe2X - radius - (globe1X + radius)) * lineProgress, y);
                    ctx.strokeStyle = '#2c3e50';
                    ctx.lineWidth = 5;
                    ctx.stroke();
                }

                // Draw text
                 if (progress > 0.7) {
                    ctx.fillStyle = '#e67e22';
                    const textProgress = (progress - 0.7) / 0.3;
                    ctx.globalAlpha = textProgress;
                    ctx.fillText('inter- (之间)', 400, y - 30);
                    ctx.globalAlpha = 1;
                }
                ctx.fillStyle = '#2c3e50';
                ctx.fillText('nation', globe1X, y + 70);
                ctx.fillText('nation', globe2X, y + 70);
            }
        },
        {
            word: 'community',
            parts: [
                { text: 'com-', meaning: '共同', story: '前缀com-代表"共同"或"一起"。' },
                { text: 'mun', meaning: '服务/责任', story: '词根mun与责任、服务有关。' },
                { text: 'ity', meaning: '(名词后缀)', story: '后缀-ity表示某种状态或性质，构成名词。' }
            ],
            fullStory: '<strong>community (社区/共同体):</strong> 在这个大家庭里，人们<strong>共同(com-)</strong>承担<strong>责任(mun)</strong>，形成了一个紧密的共同体。',
            animation: (progress) => {
                 ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.textAlign = 'center';
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = 80;

                const p = Math.min(1, progress / 0.8);

                for (let i = 0; i < 5; i++) {
                    const angle = (i / 5) * Math.PI * 2 + p * Math.PI * 0.2;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    // Draw person icon
                    ctx.fillStyle = '#3498db';
                    ctx.beginPath();
                    ctx.arc(x, y - 10, 15, 0, Math.PI * 2); // Head
                    ctx.fill();
                    ctx.fillRect(x - 15, y + 5, 30, 20); // Body
                }

                if (progress > 0.5) {
                    const textProgress = (progress - 0.5) / 0.5;
                    ctx.globalAlpha = textProgress;
                    ctx.fillStyle = '#e67e22';
                    ctx.font = '24px Arial';
                    ctx.fillText('com- (共同)', centerX, centerY);
                    ctx.globalAlpha = 1;
                }
            }
        },
        {
            word: 'unprecedented',
            parts: [
                { text: 'un-', meaning: '不', story: '前缀un-表示否定，"不"。' },
                { text: 'pre-', meaning: '在...前', story: '前缀pre-表示时间或空间上的"在...之前"。' },
                { text: 'ced', meaning: '行走', story: '词根ced/ceed意为"行走"。' },
                { text: 'ent', meaning: '(形容词后缀)', story: '形容词后缀。' }
            ],
            fullStory: '<strong>unprecedented (空前的):</strong> 为了和平，他们做出了<strong>前所未有(un- + pre- + ced)</strong>的努力，这是史无前例的。',
             animation: (progress) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const y = 150;
                
                // Draw timeline
                ctx.beginPath();
                ctx.moveTo(50, y);
                ctx.lineTo(750, y);
                ctx.strokeStyle = '#bdc3c7';
                ctx.lineWidth = 3;
                ctx.stroke();

                ctx.font = '18px Arial';
                ctx.fillStyle = '#7f8c8d';
                ctx.fillText('过去 (Past)', 100, y + 30);
                ctx.fillText('现在 (Now)', 680, y + 30);

                // Animate marker
                const markerPos = 50 + (700 * Math.min(1, progress * 1.2));
                ctx.beginPath();
                ctx.moveTo(markerPos, y - 20);
                ctx.lineTo(markerPos, y + 20);
                ctx.strokeStyle = '#e74c3c';
                ctx.lineWidth = 5;
                ctx.stroke();

                if (progress > 0.8) {
                    ctx.fillStyle = '#c0392b';
                    ctx.font = '24px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('un- (没有) + pre- (先例)', markerPos - 150, y - 40);
                }
            }
        },
        {
            word: 'efforts',
            parts: [
                { text: 'ef-', meaning: '出', story: 'ef-是ex-的变体，表示"出，向外"。' },
                { text: 'fort', meaning: '力量', story: '词根fort表示"力量，强大"。' }
            ],
            fullStory: '<strong>efforts (努力):</strong> 这种努力是发自内在的<strong>强大(fort)</strong>力量(<strong>ef-</strong> = ex- 向外)。',
            animation: (progress) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const p = Math.min(1, progress);

                const startX = 200;
                const endX = 600;
                const rockX = startX + (endX - startX) * p;
                const rockY = 180;
                
                // Draw hill
                ctx.beginPath();
                ctx.moveTo(100, 250);
                ctx.bezierCurveTo(300, 100, 500, 300, 700, 150);
                ctx.lineTo(700, 280);
                ctx.lineTo(100, 280);
                ctx.closePath();
                ctx.fillStyle = '#966F33';
                ctx.fill();


                // Draw person pushing
                const personX = rockX - 60;
                const personY = rockY - 10;
                ctx.fillStyle = '#3498db';
                ctx.beginPath();
                ctx.arc(personX, personY - 20, 15, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillRect(personX - 10, personY, 20, 30);

                // Draw rock
                ctx.fillStyle = '#7f8c8d';
                ctx.beginPath();
                ctx.arc(rockX, rockY, 50, 0, Math.PI * 2);
                ctx.fill();
                
                if (progress > 0.5) {
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '20px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('ef- (出) + fort (力量)', canvas.width / 2, 50);
                }
            }
        },
        {
            word: 'resolve',
            parts: [
                { text: 're-', meaning: '再', story: '前缀re-有"再次，往回"的意思。' },
                { text: 'solv', meaning: '解开', story: '词根solv/solu表示"松开，解开"。' }
            ],
            fullStory: '<strong>resolve (解决):</strong> 他们希望能<strong>再一次(re-)</strong><strong>解开(solv-)</strong>这个难题。',
             animation: (progress) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const p = Math.min(1, progress);
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;

                ctx.lineWidth = 10;
                ctx.lineCap = 'round';
                
                // Draw tangled knot
                const segments = [
                    { x1: -100, y1: 0, x2: 100, y2: 0, c1x: -50, c1y: -80, c2x: 50, c2y: 80 },
                    { x1: 0, y1: -100, x2: 0, y2: 100, c1x: 80, c1y: -50, c2x: -80, c2y: 50 }
                ];
                
                segments.forEach((seg, i) => {
                    const untangleFactor = p * 150;
                    ctx.beginPath();
                    ctx.moveTo(centerX + seg.x1, centerY + seg.y1);
                    const c1x = centerX + seg.c1x * (1 - p);
                    const c1y = centerY + seg.c1y * (1 - p);
                    const c2x = centerX + seg.c2x * (1 - p);
                    const c2y = centerY + seg.c2y * (1 - p);
                    const x2 = centerX + seg.x2 + (i === 0 ? untangleFactor : 0);
                    const y2 = centerY + seg.y2 + (i === 1 ? untangleFactor : 0);
                    
                    ctx.strokeStyle = i === 0 ? '#e74c3c' : '#3498db';
                    ctx.bezierCurveTo(c1x, c1y, c2x, c2y, x2, y2);
                    ctx.stroke();
                });

                if (progress > 0.7) {
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '24px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('re- (再次) + solv (解开)', centerX, 50);
                }
            }
        },
        {
            word: 'conflict',
            parts: [
                { text: 'con-', meaning: '共同', story: '前缀con-表示"共同，一起"。' },
                { text: 'flict', meaning: '打击', story: '词根flict意为"打击"。' }
            ],
            fullStory: '<strong>conflict (冲突):</strong> 因为难题的根源在于多方力量的<strong>相互(con-)</strong><strong>撞击(flict)</strong>。',
            animation: (progress) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const p = Math.min(1, progress);

                const sword1StartX = 150;
                const sword1EndX = 350 - p * 50;
                const sword2StartX = 650;
                const sword2EndX = 450 + p * 50;

                // Draw swords
                function drawSword(x, y, angle) {
                    ctx.save();
                    ctx.translate(x, y);
                    ctx.rotate(angle);
                    ctx.fillStyle = '#7f8c8d';
                    ctx.fillRect(-100, -5, 200, 10); // Blade
                    ctx.fillStyle = '#c0392b';
                    ctx.fillRect(-120, -10, 20, 20); // Hilt
                    ctx.restore();
                }

                drawSword(sword1EndX, 150, Math.PI / 4);
                drawSword(sword2EndX, 150, -Math.PI / 4);

                // Draw impact spark
                if (p > 0.8) {
                    const sparkProgress = (p - 0.8) / 0.2;
                    ctx.fillStyle = '#f1c40f';
                    for(let i = 0; i < 20; i++) {
                        const angle = Math.random() * Math.PI * 2;
                        const radius = Math.random() * 20 * sparkProgress;
                        ctx.beginPath();
                        ctx.arc(400, 150, Math.random() * 5, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }
                 if (progress > 0.5) {
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '24px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('con- (一起) + flict (打击)', 400, 50);
                }
            }
        }
    ];

    let currentWordIndex = -1;
    let animationStartTime = 0;
    let animationFrameId;

    function animate(time) {
        if (!animationStartTime) animationStartTime = time;
        const progress = (time - animationStartTime) / 2000; // 2-second animation

        if (progress < 1) {
            wordsData[currentWordIndex].animation(progress);
            animationFrameId = requestAnimationFrame(animate);
        } else {
            wordsData[currentWordIndex].animation(1); // Ensure final state
        }
    }

    function displayWord(index) {
        currentWordIndex = index;
        
        // Update sentence highlight
        document.querySelectorAll('.sentence strong').forEach((el, i) => {
            if (i === index) {
                el.style.color = '#e67e22';
                el.style.transform = 'scale(1.1)';
            } else {
                el.style.color = '#34495e';
                el.style.transform = 'scale(1)';
            }
        });

        const word = wordsData[index];
        explanationBox.innerHTML = word.fullStory;
        
        // Add detailed breakdown
        const breakdown = word.parts.map(p => `<li><strong>${p.text}</strong>: ${p.meaning} — ${p.story}</li>`).join('');
        explanationBox.innerHTML += `<ul>${breakdown}</ul>`;

        // Start animation
        if (animationFrameId) cancelAnimationFrame(animationFrameId);
        animationStartTime = 0;
        animate(performance.now());
        
        // Update buttons
        prevBtn.disabled = index === 0;
        nextBtn.disabled = index === wordsData.length - 1;
    }

    nextBtn.addEventListener('click', () => {
        if (currentWordIndex < wordsData.length - 1) {
            displayWord(currentWordIndex + 1);
        }
    });

    prevBtn.addEventListener('click', () => {
        if (currentWordIndex > 0) {
            displayWord(currentWordIndex - 1);
        }
    });
    
    // Initial state
    prevBtn.disabled = true;
    if (wordsData.length <= 1) {
        nextBtn.disabled = true;
    }

</script>

</body>
</html> 