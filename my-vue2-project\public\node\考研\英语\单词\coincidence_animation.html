<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Coincidence</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <style>
        /* CSS 样式 */
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #ab47bc; /* 紫色，代表神秘与巧合 */
            --secondary-color: #8e24aa;
            --accent-color: #ffb300; /* 橙色，点亮巧合瞬间 */
            --danger-color: #e74c3c;
            --success-color: #2ecc71;
            --light-bg: #f3e5f5;
            --panel-bg: #ffffff;
            --text-color: #4a148c;
            --text-muted: #7f8c8d;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #e1bee7;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--primary-color);
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #6a1b9a;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #fce4ec;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .breakdown-section .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            margin: 5px;
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .breakdown-section .insight {
            margin-top: 15px;
            font-style: italic;
            color: #555;
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            background: #212121;
        }

        .activity-title {
            font-size: 1.8em;
            color: var(--light-bg);
            margin-bottom: 15px;
            text-align: center;
        }
        
        .activity-wrapper {
            display: none;
            width: 100%;
            height: calc(100% - 100px);
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .activity-wrapper.active {
            display: flex;
        }
        
        .game-container {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px;
            background: linear-gradient(135deg, #424242, #212121);
            overflow: hidden;
        }

        .control-button {
            margin-top: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .control-button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        .control-button:active { transform: translateY(1px); }

        #welcome-screen p { font-size: 1.2em; color: var(--text-muted); text-align: center; }

        /* --- Coincidence Game Styles --- */
        .track { position: absolute; top: 0; bottom: 10%; width: 10px; background: rgba(255,255,255,0.1); }
        .ball { position: absolute; top: 0; width: 30px; height: 30px; border-radius: 50%; }
        #ball1 { background: #29b6f6; left: calc(50% - 100px); }
        #ball2 { background: #fdd835; left: calc(50% + 70px); }
        .sparkle { position: absolute; width: 10px; height: 10px; background: white; border-radius: 50%; opacity: 0; }
        
        /* --- Canvas Game Styles --- */
        #meteor-canvas { cursor: pointer; }
        .target-zone { position: absolute; bottom: 10%; width: 80px; height: 80px; border: 2px dashed var(--accent-color); border-radius: 50%; opacity: 0.5; }
        #target1 { left: 25%; }
        #target2 { right: 25%; }
        .target-zone.hit { animation: hit-effect 0.5s; }
        @keyframes hit-effect { from { transform: scale(1); border-color: var(--accent-color); } to { transform: scale(1.2); border-color: white; } }

    </style>
</head>
<body>

    <div class="container">
        <div class="word-panel">
            <h1>coincidence</h1>
            <p class="pronunciation">[kəʊˈɪnsɪdəns]</p>
            <div class="details">
                <p><strong>词性：</strong> n. 巧合，偶然</p>
                <p><strong>词源:</strong> co(共同) + in(进入) + cid(落下) → 共同落到一点。</p>
                <p><strong>含义：</strong> 两件或多件事情在同一时间发生，尤其指出人意料地、无计划地同时发生。</p>
                <div class="example">
                    <p><strong>例句:</strong> What a coincidence! I was just about to call you.</p>
                    <p><strong>翻译:</strong> 真巧啊！我正要给你打电话呢。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>单词动画</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="fall-game">动画: 巧合的相遇</button>
                    <button class="morpheme-btn" data-activity="meteor-game">互动: 流星雨的同步</button>
                </div>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画: 命运的相遇</button>
                    <button class="morpheme-btn" data-activity="canvas-animation">互动: 落入星辰</button>
                </div>
            </div>

        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>

            <div id="welcome-screen" class="activity-wrapper active">
                <p style="color:white;">点击左侧按钮，见证"巧合"的瞬间。</p>
            </div>

            <div id="fall-game" class="activity-wrapper">
                <div class="game-container">
                    <div class="track" style="left: calc(50% - 85px);"></div>
                    <div class="track" style="left: calc(50% + 85px);"></div>
                    <div id="ball1" class="ball"></div>
                    <div id="ball2" class="ball"></div>
                </div>
                <button class="control-button" id="coincidence-btn">制造巧合</button>
            </div>
            
            <div id="meteor-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="target1" class="target-zone"></div>
                    <div id="target2" class="target-zone"></div>
                    <canvas id="meteor-canvas"></canvas>
                </div>
                <button class="control-button" id="sync-btn">同步!</button>
            </div>

            <div id="full-animation" class="activity-wrapper">
                <div class="game-container" id="path-container">
                    <div id="ball1" class="path-ball"></div>
                    <div id="ball2" class="path-ball"></div>
                </div>
                <button class="control-button" id="path-btn">开始相遇</button>
            </div>

            <div id="canvas-animation" class="activity-wrapper">
                <div class="game-container"><canvas id="star-canvas" width="600" height="400"></canvas></div>
                <button class="control-button" id="star-btn">见证奇迹</button>
            </div>

        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');
        let currentCleanup = null;

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                if (currentCleanup) currentCleanup();
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                const targetId = btn.dataset.activity;
                document.getElementById(targetId)?.classList.add('active');
                
                if (targetId === 'fall-game') currentCleanup = setupFallGame();
                else if (targetId === 'meteor-game') currentCleanup = setupMeteorGame();
                else if (targetId === 'full-animation') currentCleanup = setupPathGame();
                else if (targetId === 'canvas-animation') currentCleanup = setupCanvasGame();
                else currentCleanup = null;
            });
        });

        function setupFallGame() {
            const btn = document.getElementById('coincidence-btn');
            const ball1 = document.getElementById('ball1');
            const ball2 = document.getElementById('ball2');
            const container = document.querySelector('#fall-game .game-container');
            let tl;

            function runAnimation(isCoincidence) {
                if(tl) tl.kill();
                tl = gsap.timeline({ onComplete: () => btn.disabled = false });
                btn.disabled = true;

                const duration1 = isCoincidence ? 2 : 1.5;
                const duration2 = isCoincidence ? 2 : 2.5;

                tl.fromTo([ball1, ball2], { y: 0 }, { y: container.clientHeight - 30, duration: (i) => i === 0 ? duration1: duration2, ease: 'power1.in' });
                
                if(isCoincidence) {
                    tl.add(() => {
                        for(let i=0; i<20; i++) createSparkle(container);
                    }, "-=0.1");
                }
            }
            
            function createSparkle(cont) {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';
                cont.appendChild(sparkle);
                gsap.fromTo(sparkle, {
                    x: cont.clientWidth / 2 + (Math.random() - 0.5) * 50,
                    y: cont.clientHeight - 30,
                    opacity: 1
                }, {
                    x: '+= (Math.random() - 0.5) * 100',
                    y: '+= (Math.random() - 0.5) * 100',
                    opacity: 0,
                    duration: 0.8,
                    onComplete: () => sparkle.remove()
                });
            }
            
            let toggle = false;
            btn.onclick = () => {
                toggle = !toggle;
                runAnimation(toggle);
                btn.textContent = toggle ? "普通下落" : "制造巧合";
            };
            runAnimation(false);
            return () => { if(tl) tl.kill(); };
        }

        function setupMeteorGame() {
            const canvas = document.getElementById('meteor-canvas');
            const btn = document.getElementById('sync-btn');
            if(!canvas) return null;

            const ctx = canvas.getContext('2d');
            let animationId;
            let meteors = [];

            class Meteor {
                constructor() {
                    this.x = Math.random() * canvas.width;
                    this.y = -20;
                    this.vy = Math.random() * 2 + 2;
                    this.vx = (Math.random() - 0.5) * 2;
                    this.radius = Math.random() * 3 + 1;
                }
                update() { this.x += this.vx; this.y += this.vy; }
                draw() {
                    ctx.fillStyle = '#fff';
                    ctx.beginPath(); ctx.arc(this.x, this.y, this.radius, 0, Math.PI*2); ctx.fill();
                    ctx.fillStyle = 'rgba(255,255,255,0.1)';
                    ctx.beginPath(); ctx.arc(this.x, this.y, this.radius * 3, 0, Math.PI*2); ctx.fill();
                }
            }

            function init() {
                const container = canvas.parentElement;
                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;
                meteors = [];
            }
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                if (Math.random() < 0.05) meteors.push(new Meteor());

                meteors.forEach((m, i) => {
                    m.update();
                    m.draw();
                    if (m.y > canvas.height + 20) meteors.splice(i, 1);
                });
                animationId = requestAnimationFrame(animate);
            }
            
            btn.onclick = () => {
                const target1 = document.getElementById('target1');
                const target2 = document.getElementById('target2');
                const t1Rect = target1.getBoundingClientRect();
                const t2Rect = target2.getBoundingClientRect();
                const canvasRect = canvas.getBoundingClientRect();

                const t1x = t1Rect.left - canvasRect.left + t1Rect.width / 2;
                const t2x = t2Rect.left - canvasRect.left + t2Rect.width / 2;
                const ty = t1Rect.top - canvasRect.top + t1Rect.height / 2;

                meteors.forEach(m => {
                    // This is a simplified logic. A real game would need predictive pathing.
                    if(m.y < ty && m.y > ty - 100){ // if in range
                        gsap.to(m, { x: Math.abs(m.x - t1x) < Math.abs(m.x-t2x) ? t1x : t2x, y: ty, duration: 0.5, ease: 'power2.out' });
                    }
                });
            };

            init();
            animate();
            return () => { if(animationId) cancelAnimationFrame(animationId); };
        }

        function setupPathGame() {
            const btn = document.getElementById('path-btn');
            const container = document.querySelector('#full-animation .game-container');
            let tl;

            function runAnimation(isCoincidence) {
                if(tl) tl.kill();
                tl = gsap.timeline({ onComplete: () => btn.disabled = false });
                btn.disabled = true;

                const duration1 = isCoincidence ? 2 : 1.5;
                const duration2 = isCoincidence ? 2 : 2.5;

                tl.fromTo([container.children[0], container.children[1]], { offsetDistance: 0 }, { offsetDistance: 100, duration: (i) => i === 0 ? duration1: duration2, ease: 'power1.in' });
                
                if(isCoincidence) {
                    tl.add(() => {
                        for(let i=0; i<20; i++) createSparkle(container);
                    }, "-=0.1");
                }
            }
            
            function createSparkle(cont) {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';
                cont.appendChild(sparkle);
                gsap.fromTo(sparkle, {
                    x: cont.clientWidth / 2 + (Math.random() - 0.5) * 50,
                    y: cont.clientHeight - 30,
                    opacity: 1
                }, {
                    x: '+= (Math.random() - 0.5) * 100',
                    y: '+= (Math.random() - 0.5) * 100',
                    opacity: 0,
                    duration: 0.8,
                    onComplete: () => sparkle.remove()
                });
            }
            
            let toggle = false;
            btn.onclick = () => {
                toggle = !toggle;
                runAnimation(toggle);
                btn.textContent = toggle ? "普通相遇" : "制造巧合";
            };
            runAnimation(false);
            return () => { if(tl) tl.kill(); };
        }

        function setupCanvasGame() {
            const canvas = document.getElementById('star-canvas');
            const btn = document.getElementById('star-btn');
            if (!canvas || !btn) return;
            const ctx = canvas.getContext('2d');
            let particles = [];
            let isAssembled = false;

            const starPoints = [ // 5-pointed star
                {x: 0, y: -100}, {x: 22, y: -31}, {x: 95, y: -31}, {x: 36, y: 12},
                {x: 59, y: 81}, {x: 0, y: 38}, {x: -59, y: 81}, {x: -36, y: 12},
                {x: -95, y: -31}, {x: -22, y: -31}
            ];
            const starParticles = [];

            class Particle {
                constructor() {
                    this.x = Math.random() * canvas.width;
                    this.y = Math.random() * canvas.height;
                    this.vx = (Math.random() - 0.5) * 2;
                    this.vy = (Math.random() - 0.5) * 2;
                    this.size = Math.random() * 2 + 1;
                    this.color = `hsla(${180 + Math.random() * 180}, 70%, 70%, 0.8)`;
                    this.isStar = false;
                }
                draw() {
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                    ctx.fill();
                }
                update() {
                    this.x += this.vx;
                    this.y += this.vy;
                    if (this.x < 0 || this.x > canvas.width) this.vx *= -1;
                    if (this.y < 0 || this.y > canvas.height) this.vy *= -1;
                }
                assemble(targetX, targetY) {
                    const dx = targetX - this.x;
                    const dy = targetY - this.y;
                    this.x += dx * 0.05;
                    this.y += dy * 0.05;
                }
            }
            
            function init() {
                particles = [];
                starParticles.length = 0;
                isAssembled = false;
                for (let i = 0; i < 200; i++) {
                    particles.push(new Particle());
                }
                // Randomly select particles to form the star
                const shuffled = particles.slice().sort(() => 0.5 - Math.random());
                for(let i = 0; i < starPoints.length * 5; i++){
                    const p = shuffled[i];
                    p.isStar = true;
                    p.color = `hsla(${35 + Math.random()*20}, 90%, 60%, 1)`;
                    starParticles.push({
                        particle: p,
                        target: starPoints[i % starPoints.length]
                    });
                }
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                particles.forEach(p => {
                    if (isAssembled && p.isStar) {
                        // This particle is part of the star, but this logic is handled below
                    } else {
                        p.update();
                    }
                    p.draw();
                });

                if(isAssembled) {
                    starParticles.forEach(sp => {
                        sp.particle.assemble(
                            canvas.width / 2 + sp.target.x, 
                            canvas.height / 2 + sp.target.y
                        );
                    });
                }
                requestAnimationFrame(animate);
            }

            btn.addEventListener('click', () => {
                isAssembled = !isAssembled;
                btn.textContent = isAssembled ? "打乱星辰" : "见证奇迹";
            });

            const observer = new MutationObserver((mutations) => {
                 if (document.getElementById('canvas-animation').classList.contains('active')) {
                    init();
                }
            });
            observer.observe(document.getElementById('canvas-animation'), { attributes: true });

            init();
            animate();
        }
    });
    </script>

</body>
</html> 