<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：pre-（预先、提前）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #1a1a2e, #16213e);
        }

        #crystalCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .prophecy-academy {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .crystal-ball {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .crystal-ball::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.6s;
        }

        .crystal-ball:hover::before {
            left: 100%;
        }

        .crystal-ball:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .crystal-ball.revealed {
            opacity: 1;
            transform: translateY(0);
        }

        .prophecy-vision {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            position: relative;
        }

        .present-word {
            background: #6c757d;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .present-word::after {
            content: '现在';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #5a6268;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .crystal-sphere {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, #ffffff, #e3f2fd, #2196f3);
            position: relative;
            margin: 0 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: float 3s ease-in-out infinite;
            box-shadow: 0 10px 20px rgba(33, 150, 243, 0.3);
        }

        .crystal-sphere::before {
            content: '🔮';
            font-size: 1.5rem;
        }

        .crystal-sphere::after {
            content: '';
            position: absolute;
            width: 80px;
            height: 80px;
            border: 2px solid rgba(33, 150, 243, 0.3);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .future-word {
            background: #007bff;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .future-word::after {
            content: '未来';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #0056b3;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .prefix-highlight {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .prophecy-meaning {
            background: rgba(0, 123, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .vision-example {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #ffc107;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #ffc107;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .prophecy-status {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6c757d;
            transition: all 0.3s ease;
        }

        .prophecy-status.seeing {
            background: #007bff;
            animation: glow 1.5s infinite;
        }

        .prophecy-status.revealed {
            background: #28a745;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.1);
            }
        }

        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 5px #007bff;
            }
            50% {
                box-shadow: 0 0 20px #007bff, 0 0 30px #007bff;
            }
        }

        @keyframes starTwinkle {
            0%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        .interactive-hint {
            text-align: center;
            color: #667eea;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .mystical-particles {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #007bff;
            border-radius: 50%;
            pointer-events: none;
            animation: starTwinkle 3s infinite;
        }

        .vision-timeline {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 15px;
        }

        .timeline-crystal {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: radial-gradient(circle, #e3f2fd, #2196f3);
            position: relative;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }

        .timeline-crystal.active {
            transform: scale(1.3);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.6);
        }

        .timeline-crystal.complete {
            background: radial-gradient(circle, #c8e6c9, #4caf50);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        .timeline-crystal::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #2196f3, transparent);
            transform: translateY(-50%);
        }

        .timeline-crystal:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>预言前缀：pre-</h1>
            <p>在未来预言师学院中学会"提前预见"的智慧</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🔮 未来预言师学院的故事</h2>
                <p>在一座神秘的预言师学院里，学生们学习使用水晶球预见未来。学院最重要的课程是"pre-"预言术，这种魔法能让普通的动词获得"预先"和"提前"的能力。当预言师们在动词前加上"pre-"这个神奇前缀时，就能看到动作发生之前的状态，预见未来将要发生的事情！</p>
            </div>

            <div class="canvas-container">
                <canvas id="crystalCanvas"></canvas>
                <div class="vision-timeline" id="visionTimeline">
                    <div class="timeline-crystal"></div>
                    <div class="timeline-crystal"></div>
                    <div class="timeline-crystal"></div>
                    <div class="timeline-crystal"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择未来预言师学院的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"预言师学院"的比喻，是因为"pre-"前缀的核心含义就是"预先"、"提前"，这与预言师预见未来的能力完美契合。水晶球的视觉效果帮助学生理解"预先看到"的概念，而学院的设定强调了这种能力需要学习和练习。通过"现在→未来"的时间轴展示，让抽象的"提前"概念变得具体可感，帮助学生建立清晰的时间顺序概念。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="startProphecy()">启动预言术</button>
                <button class="btn" onclick="revealVisions()">显示预言</button>
                <button class="btn" onclick="resetAcademy()">重置学院</button>
            </div>

            <div class="interactive-hint">
                🌟 点击"启动预言术"观看词汇预言过程，点击水晶球查看详细预言
            </div>
        </div>

        <div class="prophecy-academy" id="prophecyAcademy">
            <div class="crystal-ball">
                <div class="prophecy-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">View → Preview</h3>
                <div class="prophecy-vision">
                    <div class="present-word">view</div>
                    <div class="crystal-sphere"></div>
                    <div class="future-word"><span class="prefix-highlight">pre</span>view</div>
                </div>
                <div class="prophecy-meaning">
                    观看 → <span class="prefix-highlight">预先</span>观看
                </div>
                <div class="vision-example">
                    <strong>预言展示：</strong><br>
                    <strong>现在：</strong>Let's view the movie. (让我们观看这部电影。)<br>
                    <strong>未来：</strong>Let's preview the movie. (让我们预览这部电影。)<br>
                    <strong>解析：</strong>"view"表示观看，加上"pre-"变成"preview"，表示预先观看、预览。就像预言师提前看到未来一样。
                </div>
            </div>

            <div class="crystal-ball">
                <div class="prophecy-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Pay → Prepay</h3>
                <div class="prophecy-vision">
                    <div class="present-word">pay</div>
                    <div class="crystal-sphere"></div>
                    <div class="future-word"><span class="prefix-highlight">pre</span>pay</div>
                </div>
                <div class="prophecy-meaning">
                    支付 → <span class="prefix-highlight">预先</span>支付
                </div>
                <div class="vision-example">
                    <strong>预言展示：</strong><br>
                    <strong>现在：</strong>I will pay the bill. (我会付账单。)<br>
                    <strong>未来：</strong>I will prepay the bill. (我会预付账单。)<br>
                    <strong>解析：</strong>"pay"表示支付，加上"pre-"变成"prepay"，表示预先支付、预付。提前完成支付动作。
                </div>
            </div>

            <div class="crystal-ball">
                <div class="prophecy-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Heat → Preheat</h3>
                <div class="prophecy-vision">
                    <div class="present-word">heat</div>
                    <div class="crystal-sphere"></div>
                    <div class="future-word"><span class="prefix-highlight">pre</span>heat</div>
                </div>
                <div class="prophecy-meaning">
                    加热 → <span class="prefix-highlight">预先</span>加热
                </div>
                <div class="vision-example">
                    <strong>预言展示：</strong><br>
                    <strong>现在：</strong>Heat the oven to 200°C. (把烤箱加热到200度。)<br>
                    <strong>未来：</strong>Preheat the oven to 200°C. (把烤箱预热到200度。)<br>
                    <strong>解析：</strong>"heat"表示加热，加上"pre-"变成"preheat"，表示预先加热、预热。在正式使用前提前加热。
                </div>
            </div>

            <div class="crystal-ball">
                <div class="prophecy-status"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Pare → Prepare</h3>
                <div class="prophecy-vision">
                    <div class="present-word">pare</div>
                    <div class="crystal-sphere"></div>
                    <div class="future-word"><span class="prefix-highlight">pre</span>pare</div>
                </div>
                <div class="prophecy-meaning">
                    准备 → <span class="prefix-highlight">预先</span>准备
                </div>
                <div class="vision-example">
                    <strong>预言展示：</strong><br>
                    <strong>现在：</strong>Pare the ingredients. (处理食材。)<br>
                    <strong>未来：</strong>Prepare the ingredients. (准备食材。)<br>
                    <strong>解析：</strong>"pare"表示准备、处理，加上"pre-"变成"prepare"，表示预先准备。提前做好准备工作。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"pre-"前缀表示在时间或顺序上的"预先"、"提前"、"在...之前"。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"pre-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"pre-"后的动词基本含义</li>
                <li><strong>应用时间概念：</strong>在词根意思前加上"预先"、"提前"、"预"</li>
                <li><strong>时序调整：</strong>强调动作发生在正常时间之前</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象预言师的水晶球，"pre-"就像预言术，能让你提前看到动作的未来！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('crystalCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentVision = 0;
        let stars = [];
        let mysticalParticles = [];
        let crystalGlow = 0;
        
        const visions = [
            { present: 'view', future: 'preview', x: 150, y: 200 },
            { present: 'pay', future: 'prepay', x: 350, y: 250 },
            { present: 'heat', future: 'preheat', x: 550, y: 200 },
            { present: 'pare', future: 'prepare', x: 750, y: 250 }
        ];

        class Star {
            constructor() {
                this.x = Math.random() * canvas.width;
                this.y = Math.random() * canvas.height;
                this.size = Math.random() * 2 + 1;
                this.twinkle = Math.random() * Math.PI * 2;
                this.twinkleSpeed = 0.02 + Math.random() * 0.02;
            }

            update() {
                this.twinkle += this.twinkleSpeed;
            }

            draw() {
                const alpha = 0.3 + Math.sin(this.twinkle) * 0.3;
                ctx.save();
                ctx.globalAlpha = alpha;
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        class MysticalParticle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 2;
                this.vy = (Math.random() - 0.5) * 2;
                this.life = 1;
                this.decay = 0.02;
                this.size = Math.random() * 3 + 1;
                this.color = `hsl(${200 + Math.random() * 60}, 70%, 70%)`;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.size *= 0.99;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        function initStars() {
            stars = [];
            for (let i = 0; i < 50; i++) {
                stars.push(new Star());
            }
        }

        function drawCrystalBall() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = 80;
            
            // 水晶球底座
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(centerX - 60, centerY + 60, 120, 30);
            
            // 水晶球主体
            const gradient = ctx.createRadialGradient(centerX - 20, centerY - 20, 10, centerX, centerY, radius);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
            gradient.addColorStop(0.3, 'rgba(227, 242, 253, 0.6)');
            gradient.addColorStop(0.7, 'rgba(33, 150, 243, 0.4)');
            gradient.addColorStop(1, 'rgba(13, 71, 161, 0.8)');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.fill();
            
            // 水晶球光泽
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.beginPath();
            ctx.arc(centerX - 25, centerY - 25, 20, 0, Math.PI * 2);
            ctx.fill();
            
            // 神秘光环
            if (animationState === 'prophesying') {
                ctx.strokeStyle = `rgba(33, 150, 243, ${0.5 + Math.sin(crystalGlow) * 0.3})`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius + 20, 0, Math.PI * 2);
                ctx.stroke();
                
                ctx.strokeStyle = `rgba(33, 150, 243, ${0.3 + Math.sin(crystalGlow + 1) * 0.2})`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius + 40, 0, Math.PI * 2);
                ctx.stroke();
                
                crystalGlow += 0.1;
            }
        }

        function drawProphecyVision() {
            if (currentVision < visions.length && animationState === 'prophesying') {
                const vision = visions[currentVision];
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                
                // 在水晶球内显示预言
                ctx.save();
                ctx.beginPath();
                ctx.arc(centerX, centerY, 70, 0, Math.PI * 2);
                ctx.clip();
                
                // 现在的词（上方）
                ctx.fillStyle = 'rgba(108, 117, 125, 0.8)';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(vision.present, centerX, centerY - 20);
                
                // 箭头
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.font = '20px Arial';
                ctx.fillText('↓', centerX, centerY);
                
                // 未来的词（下方）
                ctx.fillStyle = 'rgba(0, 123, 255, 0.9)';
                ctx.font = '16px Arial';
                ctx.fillText('pre' + vision.present, centerX, centerY + 20);
                
                ctx.restore();
            }
        }

        function createMysticalEffect(x, y) {
            for (let i = 0; i < 8; i++) {
                mysticalParticles.push(new MysticalParticle(x, y));
            }
        }

        function updateParticles() {
            mysticalParticles = mysticalParticles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });
        }

        function updateVisionTimeline() {
            const crystals = document.querySelectorAll('.timeline-crystal');
            crystals.forEach((crystal, index) => {
                crystal.classList.remove('active', 'complete');
                if (index < currentVision) {
                    crystal.classList.add('complete');
                } else if (index === currentVision && animationState === 'prophesying') {
                    crystal.classList.add('active');
                }
            });
        }

        function updateProphecyStatus() {
            const balls = document.querySelectorAll('.crystal-ball');
            const statuses = document.querySelectorAll('.prophecy-status');
            
            balls.forEach((ball, index) => {
                const status = statuses[index];
                if (index < currentVision) {
                    status.classList.remove('seeing');
                    status.classList.add('revealed');
                } else if (index === currentVision && animationState === 'prophesying') {
                    status.classList.add('seeing');
                    status.classList.remove('revealed');
                } else {
                    status.classList.remove('seeing', 'revealed');
                }
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制夜空背景
            const bgGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            bgGradient.addColorStop(0, '#1a1a2e');
            bgGradient.addColorStop(1, '#16213e');
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制星星
            stars.forEach(star => {
                star.update();
                star.draw();
            });
            
            // 绘制水晶球
            drawCrystalBall();
            
            // 绘制预言视觉
            drawProphecyVision();
            
            // 更新粒子效果
            updateParticles();
            
            // 更新界面状态
            updateVisionTimeline();
            updateProphecyStatus();
        }

        function animate() {
            drawScene();
            
            if (animationState === 'prophesying' && currentVision < visions.length) {
                // 创建神秘粒子效果
                if (Math.random() < 0.1) {
                    createMysticalEffect(canvas.width / 2 + (Math.random() - 0.5) * 160, 
                                      canvas.height / 2 + (Math.random() - 0.5) * 160);
                }
                
                // 自动切换到下一个预言
                setTimeout(() => {
                    currentVision++;
                    if (currentVision >= visions.length) {
                        animationState = 'completed';
                    }
                }, 3000);
            }
            
            requestAnimationFrame(animate);
        }

        function startProphecy() {
            animationState = 'prophesying';
            currentVision = 0;
            crystalGlow = 0;
            mysticalParticles = [];
        }

        function revealVisions() {
            const balls = document.querySelectorAll('.crystal-ball');
            balls.forEach((ball, index) => {
                setTimeout(() => {
                    ball.classList.add('revealed');
                }, index * 400);
            });
        }

        function resetAcademy() {
            animationState = 'idle';
            currentVision = 0;
            crystalGlow = 0;
            mysticalParticles = [];
            
            const balls = document.querySelectorAll('.crystal-ball');
            balls.forEach(ball => ball.classList.remove('revealed'));
            
            const statuses = document.querySelectorAll('.prophecy-status');
            statuses.forEach(status => {
                status.classList.remove('seeing', 'revealed');
            });
        }

        // 初始化
        initStars();
        animate();

        // 点击水晶球的交互
        document.querySelectorAll('.crystal-ball').forEach(ball => {
            ball.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
