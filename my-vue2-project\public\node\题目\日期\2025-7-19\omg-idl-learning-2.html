<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OMG IDL学习 - 模块定义专题</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;dd
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .idl-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .idl-demo {
            text-align: center;
            margin: 30px 0;
        }

        #idlCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .element-controls {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 25px 0;
        }

        .element-btn {
            padding: 20px 15px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .interface-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .module-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .element-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .element-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .comparison-section {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 30px 0;
        }

        .comparison-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .comparison-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .comparison-card.core {
            border-color: #fd79a8;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
        }

        .comparison-card.mapping {
            border-color: #74b9ff;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }

        .comparison-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .comparison-card p {
            font-size: 1rem;
            line-height: 1.6;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-module {
            color: #0984e3;
            font-weight: bold;
            background: rgba(116,185,255,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-module {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            animation: floatModule 16s infinite ease-in-out;
        }

        .mod1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .mod2 {
            top: 70%;
            right: 15%;
            animation-delay: 5s;
        }

        .mod3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatModule {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-25px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .element-controls {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-module mod1"></div>
        <div class="floating-module mod2"></div>
        <div class="floating-module mod3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>📦 OMG IDL模块定义专题</h1>
            <p>深度理解模块定义的命名空间映射特性</p>
        </div>

        <div class="main-grid">
            <div class="idl-section">
                <h2 class="section-title">🔧 IDL核心概念对比</h2>
                
                <div class="idl-demo">
                    <canvas id="idlCanvas" width="700" height="400"></canvas>
                </div>

                <div class="element-controls">
                    <button class="element-btn interface-btn" onclick="demonstrateElement('interface')">
                        接口描述 - IDL文件最核心内容<br><small>Interface Description</small>
                    </button>
                    <button class="element-btn module-btn" onclick="demonstrateElement('module')">
                        模块定义 - 映射为包/命名空间<br><small>Module Definition</small>
                    </button>
                </div>

                <div class="comparison-section">
                    <div class="comparison-card core">
                        <h3>🎯 接口描述 (Interface) - 第一空答案</h3>
                        <p><strong>IDL文件最核心的内容</strong><br>
                        • 定义对象的操作和属性<br>
                        • 描述服务的接口契约<br>
                        • 包含方法签名、参数、返回值<br>
                        • 是CORBA分布式对象的核心概念</p>
                    </div>
                    <div class="comparison-card mapping">
                        <h3>📦 模块定义 (Module) - 第二空答案</h3>
                        <p><strong>映射为Java包或C++命名空间</strong><br>
                        • 提供命名空间功能，避免命名冲突<br>
                        • Java映射：module → package<br>
                        • C++映射：module → namespace<br>
                        • 组织和管理IDL定义的逻辑结构</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 OMG接口定义语言IDL文件包含了六种不同的元素，（　　）是一个IDL文件最核心的内容，<strong>（请作答此空）将映射为Java语言中的包(Package)或C++语言中的命名空间(Namespace)</strong>。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        A. 模块定义
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. 消息结构
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. 接口描述
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        D. 值类型
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：A. 模块定义</strong></p>
                    <p>根据题目的两个空的描述：</p>
                    <ul>
                        <li><strong>第一空</strong>：接口描述 - IDL文件最核心的内容</li>
                        <li><strong>第二空</strong>：<span class="highlight-module">模块定义</span> - 映射为Java包或C++命名空间</li>
                    </ul>
                    <p><strong>模块定义的映射机制</strong>：</p>
                    <ul>
                        <li><strong>Java语言映射</strong>：
                            <br>• IDL模块 → Java包 (package)
                            <br>• 例：module MyModule → package MyModule;</li>
                        <li><strong>C++语言映射</strong>：
                            <br>• IDL模块 → C++命名空间 (namespace)
                            <br>• 例：module MyModule → namespace MyModule { }</li>
                        <li><strong>命名空间功能</strong>：
                            <br>• 避免命名冲突
                            <br>• 提供逻辑分组
                            <br>• 支持嵌套结构</li>
                    </ul>
                    <p><strong>IDL六种元素</strong>：模块定义、类型定义、常量定义、异常、接口描述、值类型</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了模块定义的映射特性！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('idlCanvas');
        const ctx = canvas.getContext('2d');
        let currentElement = 'module';
        let animationId = null;

        // 演示不同IDL元素
        function demonstrateElement(elementType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            currentElement = elementType;
            
            // 更新按钮状态
            document.querySelectorAll('.element-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${elementType}-btn`).classList.add('active');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(elementType) {
                case 'interface':
                    drawInterfaceCore();
                    break;
                case 'module':
                    drawModuleMapping();
                    break;
            }
        }

        // 绘制接口描述（第一空）
        function drawInterfaceCore() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('接口描述 - IDL文件最核心内容', 350, 40);

            // 核心标识
            ctx.fillStyle = '#e17055';
            ctx.fillRect(250, 60, 200, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 60, 200, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('🎯 最核心内容', 350, 85);

            // 接口定义示例
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(150, 120, 400, 180);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(150, 120, 400, 180);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Arial';
            ctx.fillText('Interface Definition', 350, 150);
            
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('interface Calculator {', 170, 175);
            ctx.fillText('  // 操作定义', 190, 195);
            ctx.fillText('  long add(in long a, in long b);', 190, 215);
            ctx.fillText('  long subtract(in long a, in long b);', 190, 235);
            ctx.fillText('  double divide(in double a, in double b)', 190, 255);
            ctx.fillText('    raises(DivisionByZero);', 190, 275);
            ctx.fillText('};', 170, 295);

            // 核心特性
            const coreFeatures = [
                {x: 50, y: 320, text: '定义对象操作'},
                {x: 200, y: 320, text: '描述接口契约'},
                {x: 350, y: 320, text: '方法签名规范'},
                {x: 500, y: 320, text: '分布式对象核心'}
            ];

            coreFeatures.forEach(feature => {
                ctx.fillStyle = '#e84393';
                ctx.fillRect(feature.x, feature.y, 120, 40);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.strokeRect(feature.x, feature.y, 120, 40);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(feature.text, feature.x + 60, feature.y + 25);
            });

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('接口描述是IDL文件的核心，定义了完整的服务契约', 350, 390);
        }

        // 绘制模块定义映射（第二空）
        function drawModuleMapping() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('模块定义 - 命名空间映射', 350, 40);

            // 映射标识
            ctx.fillStyle = '#0984e3';
            ctx.fillRect(250, 60, 200, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 60, 200, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('📦 映射机制', 350, 85);

            // IDL模块定义
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(50, 120, 200, 120);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 120, 200, 120);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('IDL Module', 150, 150);
            ctx.font = '12px Arial';
            ctx.fillText('module Banking {', 150, 175);
            ctx.fillText('  interface Account {', 150, 195);
            ctx.fillText('    void deposit(...);', 150, 215);
            ctx.fillText('  };', 150, 235);
            ctx.fillText('};', 150, 255);

            // 映射箭头
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 5;
            ctx.beginPath();
            ctx.moveTo(250, 180);
            ctx.lineTo(320, 150);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(250, 180);
            ctx.lineTo(320, 210);
            ctx.stroke();

            // 箭头头部
            ctx.fillStyle = '#0984e3';
            ctx.beginPath();
            ctx.moveTo(320, 150);
            ctx.lineTo(310, 145);
            ctx.lineTo(310, 155);
            ctx.closePath();
            ctx.fill();

            ctx.beginPath();
            ctx.moveTo(320, 210);
            ctx.lineTo(310, 205);
            ctx.lineTo(310, 215);
            ctx.closePath();
            ctx.fill();

            // Java Package映射
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(350, 120, 150, 80);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 2;
            ctx.strokeRect(350, 120, 150, 80);
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Java Package', 425, 145);
            ctx.font = '11px Arial';
            ctx.fillText('package Banking;', 425, 165);
            ctx.fillText('public interface', 425, 180);
            ctx.fillText('Account { ... }', 425, 195);

            // C++ Namespace映射
            ctx.fillStyle = '#a29bfe';
            ctx.fillRect(350, 220, 150, 80);
            ctx.strokeStyle = '#6c5ce7';
            ctx.lineWidth = 2;
            ctx.strokeRect(350, 220, 150, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('C++ Namespace', 425, 245);
            ctx.font = '11px Arial';
            ctx.fillText('namespace Banking {', 425, 265);
            ctx.fillText('  class Account', 425, 280);
            ctx.fillText('  { ... };', 425, 295);

            // 映射特性
            const mappingFeatures = [
                {x: 550, y: 130, text: '避免命名冲突'},
                {x: 550, y: 180, text: '逻辑分组'},
                {x: 550, y: 230, text: '嵌套支持'},
                {x: 550, y: 280, text: '跨语言一致'}
            ];

            mappingFeatures.forEach(feature => {
                ctx.fillStyle = '#00b894';
                ctx.fillRect(feature.x, feature.y, 100, 35);
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 1;
                ctx.strokeRect(feature.x, feature.y, 100, 35);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(feature.text, feature.x + 50, feature.y + 22);
            });

            // 说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('模块定义映射为不同语言的命名空间机制', 350, 350);
            
            ctx.font = 'bold 14px Arial';
            ctx.fillText('Java: module → package  |  C++: module → namespace', 350, 380);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('A. 模块定义')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画
                    demonstrateElement('module');
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateElement('module');
            
            // 自动演示序列
            setTimeout(() => demonstrateElement('interface'), 4000);
            setTimeout(() => demonstrateElement('module'), 8000);
        };
    </script>
</body>
</html>
