<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java 访问修饰符交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            max-width: 900px;
            width: 100%;
            background: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #1a237e;
            border-bottom: 2px solid #e8eaf6;
            padding-bottom: 10px;
        }
        .intro, .rules-table {
            margin-bottom: 20px;
        }
        .intro ul {
            list-style-type: none;
            padding-left: 0;
        }
        .intro li {
            background: #e8eaf6;
            margin-bottom: 8px;
            padding: 10px;
            border-radius: 6px;
        }
        .intro code {
            font-weight: bold;
            color: #c51162;
            background: #fce4ec;
            padding: 2px 5px;
            border-radius: 4px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
        }
        th, td {
            border: 1px solid #c5cae9;
            padding: 12px;
        }
        th {
            background-color: #3f51b5;
            color: white;
        }
        td:first-child {
            font-weight: bold;
            background-color: #e8eaf6;
        }
        .check { color: #4caf50; font-weight: bold; font-size: 1.5em; }
        .cross { color: #f44336; font-weight: bold; font-size: 1.5em; }
        
        #interactive-demo {
            margin-top: 30px;
        }
        .controls {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            background: #f4f6f8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .control-group {
            margin: 5px 15px;
        }
        .control-group label {
            margin-right: 10px;
            font-weight: bold;
        }
        canvas {
            background-color: #ffffff;
            border: 1px solid #c5cae9;
            border-radius: 8px;
            width: 100%;
        }
        #explanation {
            margin-top: 15px;
            padding: 15px;
            background: #e3f2fd;
            border-left: 5px solid #2196f3;
            border-radius: 4px;
            font-size: 1.1em;
            text-align: center;
            min-height: 50px;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Java 访问修饰符 (public, private, protected, default)</h1>

        <div class="intro">
            <h2>基本概念</h2>
            <p>在 Java 中，访问修饰符用来保护对类、变量、方法和构造方法的访问。Java 支持 4 种不同的访问权限：</p>
            <ul>
                <li><code>private</code> (私有的): 仅在同一类内可见。</li>
                <li><code>default</code> (默认, 不写关键字): 在同一包内可见。</li>
                <li><code>protected</code> (受保护的): 对同一包内的类和所有子类可见。</li>
                <li><code>public</code> (公共的): 对所有类可见。</li>
            </ul>
        </div>

        <div class="rules-table">
            <h2>访问权限总结</h2>
            <table>
                <thead>
                    <tr>
                        <th>修饰符</th>
                        <th>同类</th>
                        <th>同包</th>
                        <th>子类 (不同包)</th>
                        <th>不同包 (非子类)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>public</code></td>
                        <td class="check">✔</td>
                        <td class="check">✔</td>
                        <td class="check">✔</td>
                        <td class="check">✔</td>
                    </tr>
                    <tr>
                        <td><code>protected</code></td>
                        <td class="check">✔</td>
                        <td class="check">✔</td>
                        <td class="check">✔</td>
                        <td class="cross">✘</td>
                    </tr>
                    <tr>
                        <td><code>default</code></td>
                        <td class="check">✔</td>
                        <td class="check">✔</td>
                        <td class="cross">✘</td>
                        <td class="cross">✘</td>
                    </tr>
                    <tr>
                        <td><code>private</code></td>
                        <td class="check">✔</td>
                        <td class="cross">✘</td>
                        <td class="cross">✘</td>
                        <td class="cross">✘</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div id="interactive-demo">
            <h2>交互式动画演示</h2>
            <p>请选择一个访问修饰符和一个访问位置，观察是否能够成功访问 <code>ClassA</code> 中的成员。</p>
            <div class="controls">
                <div class="control-group">
                    <label>修饰符:</label>
                    <input type="radio" id="public" name="modifier" value="public" checked> public
                    <input type="radio" id="protected" name="modifier" value="protected"> protected
                    <input type="radio" id="default" name="modifier" value="default"> default
                    <input type="radio" id="private" name="modifier" value="private"> private
                </div>
                <div class="control-group">
                    <label>访问位置:</label>
                    <input type="radio" id="same-class" name="location" value="sameClass" checked> 同类
                    <input type="radio" id="same-package" name="location" value="samePackage"> 同包
                    <input type="radio" id="subclass" name="location" value="subClass"> 子类(不同包)
                    <input type="radio" id="other-package" name="location" value="otherPackage"> 不同包
                </div>
            </div>
            <canvas id="demoCanvas" width="840" height="400"></canvas>
            <div id="explanation">请选择选项开始演示。</div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('demoCanvas');
        const ctx = canvas.getContext('2d');
        const explanationDiv = document.getElementById('explanation');

        const controls = document.querySelector('.controls');
        
        const state = {
            modifier: 'public',
            location: 'sameClass'
        };

        const rules = {
            public:     { sameClass: true,  samePackage: true,  subClass: true,  otherPackage: true },
            protected:  { sameClass: true,  samePackage: true,  subClass: true,  otherPackage: false },
            default:    { sameClass: true,  samePackage: true,  subClass: false, otherPackage: false },
            private:    { sameClass: false, samePackage: false, subClass: false, otherPackage: false }
        };
        // private 规则特殊，只有在 ClassA 内部访问才是 true
        rules.private.sameClass = true;


        const positions = {
            package1: { x: 50, y: 50, w: 350, h: 300 },
            package2: { x: 450, y: 50, w: 350, h: 300 },
            classA:   { x: 70, y: 100, w: 140, h: 100 },
            classB:   { x: 240, y: 100, w: 140, h: 100 },
            classC:   { x: 470, y: 100, w: 140, h: 100 },
            classD:   { x: 650, y: 100, w: 140, h: 100 }
        };

        const accessPoints = {
            sameClass:    { x: positions.classA.x + positions.classA.w / 2, y: positions.classA.y + positions.classA.h },
            samePackage:  { x: positions.classB.x + positions.classB.w / 2, y: positions.classB.y + positions.classB.h / 2 },
            subClass:     { x: positions.classC.x + positions.classC.w / 2, y: positions.classC.y + positions.classC.h / 2 },
            otherPackage: { x: positions.classD.x + positions.classD.w / 2, y: positions.classD.y + positions.classD.h / 2 },
        };
        
        const targetPoint = {
            x: positions.classA.x + positions.classA.w / 2,
            y: positions.classA.y + 30
        };

        function drawBox(x, y, w, h, text, color, textColor = '#333') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, w, h);
            ctx.fillStyle = textColor;
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + w / 2, y + 20);
        }

        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw Packages
            drawBox(positions.package1.x, positions.package1.y, positions.package1.w, positions.package1.h, 'Package 1', '#42a5f5');
            drawBox(positions.package2.x, positions.package2.y, positions.package2.w, positions.package2.h, 'Package 2', '#66bb6a');
            
            // Draw Classes
            drawBox(positions.classA.x, positions.classA.y, positions.classA.w, positions.classA.h, 'ClassA', '#1e88e5', 'white');
            ctx.fillStyle = '#1e88e5';
            ctx.fillRect(positions.classA.x, positions.classA.y, positions.classA.w, positions.classA.h);
            ctx.fillStyle = 'white';
            ctx.fillText('ClassA', positions.classA.x + positions.classA.w / 2, positions.classA.y + 25);
            
            drawBox(positions.classB.x, positions.classB.y, positions.classB.w, positions.classB.h, 'ClassB (同包)', '#1e88e5');
            drawBox(positions.classC.x, positions.classC.y, positions.classC.w, positions.classC.h, 'ClassC (子类)', '#2e7d32');
            drawBox(positions.classD.x, positions.classD.y, positions.classD.w, positions.classD.h, 'ClassD (不同包)', '#2e7d32');
            
            // Draw inheritance
            ctx.beginPath();
            ctx.moveTo(positions.classC.x + positions.classC.w / 2, positions.classC.y);
            ctx.lineTo(positions.classC.x + positions.classC.w / 2, positions.classC.y - 20);
            ctx.lineTo(positions.classA.x + positions.classA.w / 2, positions.classC.y - 20);
            ctx.lineTo(positions.classA.x + positions.classA.w / 2, positions.classA.y + positions.classA.h);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;
            ctx.stroke();
            ctx.fillStyle = '#333';
            ctx.fillText('继承', positions.classC.x + positions.classC.w / 2, positions.classC.y - 25);

            // Draw member in ClassA
            const memberText = `${state.modifier} member`;
            ctx.fillStyle = '#FFF';
            ctx.font = '14px sans-serif';
            ctx.fillText(memberText, positions.classA.x + positions.classA.w / 2, positions.classA.y + 60);

        }

        function animateAccess() {
            const startPoint = accessPoints[state.location];
            const canAccess = rules[state.modifier][state.location];

            let progress = 0;
            const duration = 1000;

            function animate(time) {
                const timeFraction = progress / duration;
                const easedTime = timeFraction < 0.5 ? 2 * timeFraction * timeFraction : -1 + (4 - 2 * timeFraction) * timeFraction;
                
                drawScene();
                
                const currentX = startPoint.x + (targetPoint.x - startPoint.x) * easedTime;
                const currentY = startPoint.y + (targetPoint.y - startPoint.y) * easedTime;
                
                // Draw animated arrow
                ctx.beginPath();
                ctx.moveTo(startPoint.x, startPoint.y);
                ctx.lineTo(currentX, currentY);
                ctx.strokeStyle = canAccess ? '#4caf50' : '#f44336';
                ctx.lineWidth = 3;
                ctx.setLineDash([5, 5]);
                ctx.stroke();
                ctx.setLineDash([]);
                
                if (progress < duration) {
                    progress += 16;
                    requestAnimationFrame(animate);
                } else {
                    drawScene(); // Redraw to clean up
                    // Draw final line
                    ctx.beginPath();
                    ctx.moveTo(startPoint.x, startPoint.y);
                    ctx.lineTo(targetPoint.x, targetPoint.y);
                    ctx.strokeStyle = canAccess ? '#4caf50' : '#f44336';
                    ctx.lineWidth = 3;
                    ctx.stroke();

                    // Draw result icon
                    ctx.font = 'bold 48px sans-serif';
                    ctx.textAlign = 'center';
                    if (canAccess) {
                        ctx.fillStyle = '#4caf50';
                        ctx.fillText('✔', targetPoint.x, targetPoint.y + 50);
                    } else {
                        ctx.fillStyle = '#f44336';
                        ctx.fillText('✘', targetPoint.x, targetPoint.y + 50);
                    }
                }
            }
            requestAnimationFrame(animate);
            updateExplanation(canAccess);
        }
        
        function updateExplanation(canAccess) {
             const modifierText = document.querySelector(`input[name="modifier"]:checked`).id;
             const locationText = document.querySelector(`label[for="${document.querySelector(`input[name="location"]:checked`).id}"]`).innerText;

             let resultText = `从 ${state.location === 'sameClass' ? 'ClassA 内部' : 
                                  state.location === 'samePackage' ? '同包的 ClassB' :
                                  state.location === 'subClass' ? '不同包的子类 ClassC' :
                                  '不同包的 ClassD'
                                 } 访问 ClassA 的 ${modifierText} 成员...`;

             if (canAccess) {
                 resultText += " <span style='color:#4caf50; font-weight:bold;'>允许访问！</span>";
                 explanationDiv.style.borderColor = "#4caf50";
                 explanationDiv.style.background = "#e8f5e9";
             } else {
                 resultText += " <span style='color:#f44336; font-weight:bold;'>访问被拒绝！</span>";
                 explanationDiv.style.borderColor = "#f44336";
                 explanationDiv.style.background = "#ffebee";
             }
             explanationDiv.innerHTML = resultText;
        }

        function updateDemo() {
            state.modifier = document.querySelector('input[name="modifier"]:checked').value;
            state.location = document.querySelector('input[name="location"]:checked').value;
            animateAccess();
        }

        controls.addEventListener('change', updateDemo);

        // Initial draw
        drawScene();
        updateDemo();

    </script>
</body>
</html> 