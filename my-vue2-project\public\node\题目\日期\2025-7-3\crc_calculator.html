<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRC校验码计算演示</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            width: 90%;
            margin: 2rem auto;
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        h1 {
            text-align: center;
            color: #1a73e8;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }
        
        .description {
            margin-bottom: 2rem;
            font-size: 1rem;
        }
        
        .steps {
            margin-bottom: 2rem;
            padding-left: 1.2rem;
        }
        
        .steps li {
            margin-bottom: 0.5rem;
        }
        
        .input-section {
            margin-bottom: 2rem;
        }
        
        .input-group {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
        }
        
        .input-group label {
            width: 120px;
            font-weight: 500;
        }
        
        .input-group input {
            flex: 1;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 1rem;
        }
        
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 0.6rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
            margin-right: 0.5rem;
        }
        
        button:hover {
            background-color: #1557b0;
        }
        
        button:disabled {
            background-color: #a8c7fa;
            cursor: not-allowed;
        }
        
        .canvas-container {
            width: 100%;
            margin: 1.5rem 0;
        }
        
        canvas {
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 0 auto;
            display: block;
        }
        
        .result {
            background-color: #f1f8ff;
            padding: 1rem;
            border-radius: 4px;
            margin-top: 1.5rem;
            font-family: monospace;
            font-size: 1.1rem;
            text-align: center;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
            gap: 1rem;
        }
        
        .step-display {
            text-align: center;
            margin-bottom: 1rem;
            font-weight: 500;
        }
        
        .explanation {
            margin-top: 1.5rem;
            padding: 1rem;
            background-color: #f9f9f9;
            border-left: 4px solid #1a73e8;
        }
        
        .footer {
            text-align: center;
            margin-top: 2rem;
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>循环冗余校验 (CRC) 计算演示</h1>
        
        <div class="description">
            <p>循环冗余校验（Cyclic Redundancy Check，CRC）是一种常用的差错检测技术，广泛应用于数据通信和存储系统中。</p>
        </div>
        
        <div class="input-section">
            <div class="input-group">
                <label for="message">信息码字:</label>
                <input type="text" id="message" value="111000110" placeholder="输入二进制数据，如：111000110">
            </div>
            
            <div class="input-group">
                <label for="generator">生成多项式:</label>
                <input type="text" id="generator" value="101011" placeholder="输入二进制系数，如：101011">
            </div>
            
            <div class="input-group">
                <label>多项式表示:</label>
                <div id="polynomial-display">x<sup>5</sup> + x<sup>3</sup> + x + 1</div>
            </div>
            
            <div class="controls">
                <button id="calculate">计算CRC</button>
                <button id="step" disabled>逐步演示</button>
                <button id="reset" disabled>重置</button>
            </div>
        </div>
        
        <div class="step-display" id="step-display"></div>
        
        <div class="canvas-container">
            <canvas id="crcCanvas" width="700" height="400"></canvas>
        </div>
        
        <div class="result" id="result"></div>
        
        <div class="explanation">
            <h3>CRC计算步骤：</h3>
            <ol class="steps">
                <li>将生成多项式的系数作为除数</li>
                <li>生成多项式的最高幂次数作为校验码的位数</li>
                <li>将信息码左移生成多项式的最高幂次数位（相当于右边补0），作为被除数</li>
                <li>执行模2除法，即异或操作</li>
                <li>得到的余数即为CRC校验码</li>
            </ol>
        </div>
    </div>
    
    <script>
        // 获取DOM元素
        const messageInput = document.getElementById('message');
        const generatorInput = document.getElementById('generator');
        const polynomialDisplay = document.getElementById('polynomial-display');
        const calculateBtn = document.getElementById('calculate');
        const stepBtn = document.getElementById('step');
        const resetBtn = document.getElementById('reset');
        const resultDisplay = document.getElementById('result');
        const stepDisplay = document.getElementById('step-display');
        const canvas = document.getElementById('crcCanvas');
        const ctx = canvas.getContext('2d');
        
        // CRC计算状态
        let state = {
            message: '',
            generator: '',
            paddedMessage: '',
            currentStep: 0,
            totalSteps: 0,
            remainder: '',
            divisionSteps: [],
            animating: false
        };
        
        // 更新多项式显示
        generatorInput.addEventListener('input', updatePolynomialDisplay);
        
        function updatePolynomialDisplay() {
            const polynomial = generatorInput.value.trim();
            if (!/^[01]+$/.test(polynomial)) {
                polynomialDisplay.textContent = '请输入有效的二进制数';
                return;
            }
            
            let polyText = '';
            const degree = polynomial.length - 1;
            
            for (let i = 0; i < polynomial.length; i++) {
                if (polynomial[i] === '1') {
                    const currentDegree = degree - i;
                    if (polyText !== '') {
                        polyText += ' + ';
                    }
                    
                    if (currentDegree === 0) {
                        polyText += '1';
                    } else if (currentDegree === 1) {
                        polyText += 'x';
                    } else {
                        polyText += 'x<sup>' + currentDegree + '</sup>';
                    }
                }
            }
            
            polynomialDisplay.innerHTML = polyText;
        }
        
        // 初始化
        updatePolynomialDisplay();
        
        // 计算CRC
        calculateBtn.addEventListener('click', startCrcCalculation);
        stepBtn.addEventListener('click', showNextStep);
        resetBtn.addEventListener('click', resetCalculation);
        
        function resetCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.font = '14px monospace';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('点击"计算CRC"按钮开始演示', canvas.width / 2, canvas.height / 2);
        }
        
        function startCrcCalculation() {
            const message = messageInput.value.trim();
            const generator = generatorInput.value.trim();
            
            if (!/^[01]+$/.test(message) || !/^[01]+$/.test(generator)) {
                resultDisplay.textContent = '请输入有效的二进制数据';
                return;
            }
            
            if (generator[0] !== '1') {
                resultDisplay.textContent = '生成多项式必须以1开头';
                return;
            }
            
            state = {
                message: message,
                generator: generator,
                paddedMessage: message + '0'.repeat(generator.length - 1),
                currentStep: 0,
                remainder: '',
                divisionSteps: [],
                animating: false
            };
            
            // 计算CRC并记录过程
            calculateCRC();
            
            // 准备动画展示
            resetBtn.disabled = false;
            stepBtn.disabled = false;
            calculateBtn.disabled = true;
            
            // 显示第一步
            showNextStep();
        }
        
        function calculateCRC() {
            const n = state.generator.length;
            let dividend = state.paddedMessage.slice(0, n);
            const steps = [];
            
            let remainingMessage = state.paddedMessage.slice(n);
            let stepCounter = 0;
            
            steps.push({
                type: 'init',
                message: state.message,
                paddedMessage: state.paddedMessage,
                explanation: `初始化: 信息码字 ${state.message} 补0后变为 ${state.paddedMessage}`
            });
            
            while (true) {
                stepCounter++;
                
                // 如果当前被除数的最高位是1，则异或除数
                const xorResult = xorBinary(dividend, dividend[0] === '1' ? state.generator : '0'.repeat(n));
                
                steps.push({
                    type: 'division',
                    dividend: dividend,
                    divisor: dividend[0] === '1' ? state.generator : '0'.repeat(n),
                    result: xorResult,
                    explanation: `步骤 ${stepCounter}: ${dividend[0] === '1' ? '除数参与异或' : '除数不参与异或'}`
                });
                
                // 更新被除数
                if (remainingMessage.length === 0) {
                    state.remainder = xorResult.slice(1);
                    break;
                }
                
                dividend = xorResult.slice(1) + remainingMessage[0];
                remainingMessage = remainingMessage.slice(1);
            }
            
            steps.push({
                type: 'result',
                remainder: state.remainder,
                explanation: `计算结果: CRC校验码为 ${state.remainder}`
            });
            
            state.divisionSteps = steps;
            state.totalSteps = steps.length;
        }
        
        function xorBinary(a, b) {
            let result = '';
            for (let i = 0; i < a.length; i++) {
                result += a[i] === b[i] ? '0' : '1';
            }
            return result;
        }
        
        function showNextStep() {
            if (state.currentStep >= state.totalSteps) {
                return;
            }
            
            const step = state.divisionSteps[state.currentStep];
            state.currentStep++;
            
            stepDisplay.textContent = `${step.explanation} (${state.currentStep}/${state.totalSteps})`;
            
            drawStep(step);
            
            if (step.type === 'result') {
                resultDisplay.textContent = `CRC校验码: ${step.remainder}`;
                stepBtn.disabled = true;
            }
        }
        
        function drawStep(step) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            const cellWidth = 30;
            const cellHeight = 40;
            const startX = 50;
            const startY = 50;
            
            if (step.type === 'init') {
                // 绘制原始信息码字
                drawBinaryNumber(startX, startY, step.message, '信息码字');
                
                // 绘制补0后的信息码字
                drawBinaryNumber(startX, startY + 80, step.paddedMessage, '补0后的信息码字');
                
                // 绘制生成多项式
                drawBinaryNumber(startX, startY + 160, state.generator, '生成多项式');
            } else if (step.type === 'division') {
                // 绘制当前被除数
                drawBinaryNumber(startX, startY, step.dividend, '当前被除数');
                
                // 绘制参与异或的除数
                drawBinaryNumber(startX, startY + 60, step.divisor, step.divisor === '0'.repeat(step.divisor.length) ? '除数不参与' : '除数参与异或');
                
                // 绘制异或结果
                ctx.beginPath();
                ctx.moveTo(startX, startY + 100);
                ctx.lineTo(startX + cellWidth * step.dividend.length, startY + 100);
                ctx.strokeStyle = '#333';
                ctx.stroke();
                
                drawBinaryNumber(startX, startY + 120, step.result, '异或结果');
            } else if (step.type === 'result') {
                // 绘制最终结果
                ctx.font = '24px Arial';
                ctx.fillStyle = '#1a73e8';
                ctx.textAlign = 'center';
                ctx.fillText('CRC校验码计算完成', canvas.width / 2, 100);
                
                drawBinaryNumber(canvas.width / 2 - (step.remainder.length * cellWidth) / 2, 150, step.remainder, '余数 (CRC校验码)');
                
                // 显示最终编码
                const finalCode = state.message + step.remainder;
                drawBinaryNumber(canvas.width / 2 - (finalCode.length * cellWidth) / 2, 250, finalCode, '信息码字 + CRC校验码');
            }
        }
        
        function drawBinaryNumber(x, y, binary, label) {
            const cellWidth = 30;
            const cellHeight = 40;
            
            // 绘制标签
            ctx.font = '14px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'left';
            ctx.fillText(label + ':', x, y - 10);
            
            // 绘制二进制数的每一位
            for (let i = 0; i < binary.length; i++) {
                // 绘制单元格
                ctx.beginPath();
                ctx.rect(x + i * cellWidth, y, cellWidth, cellHeight);
                ctx.strokeStyle = '#ddd';
                ctx.stroke();
                
                // 绘制数字
                ctx.font = '16px monospace';
                ctx.fillStyle = binary[i] === '1' ? '#1a73e8' : '#333';
                ctx.textAlign = 'center';
                ctx.fillText(binary[i], x + i * cellWidth + cellWidth / 2, y + cellHeight / 2 + 6);
            }
        }
        
        function resetCalculation() {
            state = {
                message: '',
                generator: '',
                paddedMessage: '',
                currentStep: 0,
                totalSteps: 0,
                remainder: '',
                divisionSteps: [],
                animating: false
            };
            
            resetBtn.disabled = true;
            stepBtn.disabled = true;
            calculateBtn.disabled = false;
            
            resultDisplay.textContent = '';
            stepDisplay.textContent = '';
            
            resetCanvas();
        }
        
        // 初始化画布
        resetCanvas();
    </script>
</body>
</html> 