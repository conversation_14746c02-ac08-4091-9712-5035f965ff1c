<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Append</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f0f2f5;
            color: #333;
            margin: 0;
            flex-direction: column;
        }
        .container {
            text-align: center;
            background-color: #fff;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
            margin: 1rem;
        }
        h1 {
            color: #2e7d32; /* Green color for 'append' */
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        canvas {
            background-color: #e8f5e9; /* Lighter green */
            border-radius: 8px;
            margin-top: 1rem;
            cursor: pointer;
        }
        .controls {
            margin-top: 1.5rem;
        }
        button {
            background-color: #4caf50; /* Green button */
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 1rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }
        button:hover {
            background-color: #388e3c;
            transform: translateY(-2px);
        }
        .explanation {
            margin-top: 2rem;
            text-align: left;
            line-height: 1.8;
            background-color: #f9f9f9;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 5px solid #4caf50; /* Green accent */
        }
        .explanation h2 {
            color: #2e7d32;
            margin-top: 0;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>Append (v.) 附加, 增补</h1>
    <canvas id="wordCanvas" width="600" height="400"></canvas>
    <div class="controls">
        <button id="playBtn">开始动画</button>
    </div>
    <div class="explanation">
        <h2>为什么要这样设计？</h2>
        <p>你好！今天我们学习的单词是 <strong>append</strong>。</p>
        <p>
            <strong>单词拆解：</strong><br>
            <ul>
                <li><strong>ap-</strong>: 这是前缀 'ad-' 的变体，意思是"去..."或"朝向..."(to)。</li>
                <li><strong>pend</strong>: 这是一个词根，意思是"悬挂"(to hang)。我们可以在 'depend'(依赖，悬挂在...)或 'pendant'(吊坠)等词中看到它。</li>
            </ul>
        </p>
        <p>
            <strong>动画故事：</strong><br>
            为了让你更好地记住 'append'(附加)这个词，我设计了这个动画：
            <ol>
                <li>动画开始时，你会看到一个主文档。</li>
                <li>旁边有一个小的"补充说明"纸条。</li>
                <li>动画的核心就是演示这个小纸条移动，并最终"挂"（<strong>pend</strong>）到（<strong>ap-</strong>）主文档末尾的过程。</li>
            </ol>
        </p>
        <p>这个过程就是 <strong>"把一个东西挂(pend)到(ap-)另一个东西上"</strong>，非常直观地解释了"附加、增补"的含义。希望这个故事能帮你牢牢记住 'append'！请点击"开始动画"按钮，亲自体验一下"附加"的过程吧！</p>
    </div>
</div>

<script>
const canvas = document.getElementById('wordCanvas');
const ctx = canvas.getContext('2d');
const playBtn = document.getElementById('playBtn');

let stage = 'initial'; // initial, move, append, end
let animationFrameId;

// Animation objects
const mainDoc = {
    x: 150,
    y: 80,
    width: 300,
    height: 280,
    color: '#fffde7'
};

const note = {
    x: 480,
    y: 100,
    width: 100,
    height: 120,
    color: '#lightyellow',
    targetX: mainDoc.x + mainDoc.width,
    targetY: mainDoc.y + 50,
    angle: 0
};

function drawText(text, x, y, size = 20, color = '#000', alpha = 1, textAlign = 'center') {
    ctx.globalAlpha = alpha;
    ctx.fillStyle = color;
    ctx.font = `bold ${size}px Arial`;
    ctx.textAlign = textAlign;
    ctx.fillText(text, x, y);
    ctx.globalAlpha = 1;
}

function drawDocument(doc) {
    ctx.fillStyle = doc.color;
    ctx.strokeStyle = '#a1887f';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(doc.x, doc.y);
    ctx.lineTo(doc.x + doc.width, doc.y);
    ctx.lineTo(doc.x + doc.width, doc.y + doc.height);
    ctx.lineTo(doc.x, doc.y + doc.height);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    // Draw some lines to represent text
    ctx.strokeStyle = '#ccc';
    ctx.lineWidth = 1;
    for (let i = 0; i < 7; i++) {
        ctx.beginPath();
        ctx.moveTo(doc.x + 20, doc.y + 30 + i * 35);
        ctx.lineTo(doc.x + doc.width - 20, doc.y + 30 + i * 35);
        ctx.stroke();
    }
}

function drawNote(n) {
    ctx.save();
    ctx.translate(n.x, n.y);
    ctx.rotate(n.angle);
    
    // Draw note paper
    ctx.fillStyle = '#ffffe0';
    ctx.strokeStyle = '#ccc';
    ctx.lineWidth = 1;
    ctx.fillRect(-n.width / 2, -n.height / 2, n.width, n.height);
    ctx.strokeRect(-n.width / 2, -n.height / 2, n.width, n.height);

    // Draw "clip"
    ctx.fillStyle = '#8d6e63';
    ctx.fillRect(-10, -n.height / 2 - 5, 20, 10);

    // Draw some text on note
    drawText('补充', 0, -10, 18, '#555');
    drawText('说明', 0, 20, 18, '#555');

    ctx.restore();
}

function reset() {
    if (animationFrameId) cancelAnimationFrame(animationFrameId);
    stage = 'initial';
    note.x = 480;
    note.y = 100;
    note.angle = 0;
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    drawDocument(mainDoc);
    drawNote(note);
    drawText('准备附加文件', canvas.width / 2, 50);
}

function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    drawDocument(mainDoc);

    switch(stage) {
        case 'initial':
            drawNote(note);
            drawText('点击开始', canvas.width / 2, 50);
            break;
            
        case 'move':
            let dx = note.targetX - note.x;
            let dy = note.targetY - note.y;
            let dist = Math.sqrt(dx*dx + dy*dy);
            
            if (dist > 2) {
                note.x += dx * 0.05;
                note.y += dy * 0.05;
                note.angle = Math.sin(note.x * 0.1) * 0.1; // Add a little swing
            } else {
                note.x = note.targetX;
                note.y = note.targetY;
                note.angle = 0;
                stage = 'pend';
            }
            drawNote(note);
            drawText('ap- (to) + pend (hang)', canvas.width / 2, 50, 30, '#2e7d32');
            break;

        case 'pend':
            // Animate the "hanging" or "pending" action
            if (note.angle < Math.PI / 12) {
                note.angle += 0.02;
            } else {
                stage = 'end';
            }
            // Adjust position slightly to look like it's hanging on the edge
            note.x = mainDoc.x + mainDoc.width - 5; 
            note.y = mainDoc.y + 50 + note.height / 2;

            ctx.save();
            ctx.translate(note.x, note.y - note.height/2);
            ctx.rotate(note.angle);
            ctx.translate(-note.x, -(note.y - note.height/2));
            
            drawNote({ ...note, x: note.x, y: note.y, angle: 0 }); // Draw note without double rotation
            ctx.restore();
            
            drawText('Append: 附加, 增补', canvas.width / 2, 50, 30, '#1b5e20');
            break;

        case 'end':
            note.x = mainDoc.x + mainDoc.width - 5;
            note.y = mainDoc.y + 50 + note.height / 2;
            note.angle = Math.PI / 12;

            ctx.save();
            ctx.translate(note.x, note.y - note.height/2);
            ctx.rotate(note.angle);
            ctx.translate(-note.x, -(note.y - note.height/2));
            drawNote({ ...note, x: note.x, y: note.y, angle: 0 });
            ctx.restore();

            drawText('完成！已附加。', canvas.width / 2, 50, 30, '#1b5e20');
            drawText('点击按钮可重置', canvas.width / 2, 380, 16, '#555');
            break;
    }
    
    animationFrameId = requestAnimationFrame(animate);
}

playBtn.addEventListener('click', () => {
    if (stage === 'initial' || stage === 'end') {
        reset();
        stage = 'move';
        if(!animationFrameId) animate();
    }
});

// Initial draw
reset();
</script>

</body>
</html> 