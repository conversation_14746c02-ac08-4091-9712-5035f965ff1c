<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式处理器探索之旅</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 60px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            animation: titleFloat 3s ease-in-out infinite;
        }

        @keyframes titleFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .game-board {
            background: rgba(255,255,255,0.95);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .processor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .processor-card {
            background: linear-gradient(145deg, #f0f0f0, #ffffff);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .processor-card:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .processor-card.correct {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            animation: correctPulse 0.6s ease-in-out;
        }

        .processor-card.wrong {
            background: linear-gradient(145deg, #f44336, #d32f2f);
            color: white;
            animation: wrongShake 0.6s ease-in-out;
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .processor-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .processor-name {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .processor-desc {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        #gameCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .explanation-panel {
            background: linear-gradient(145deg, #e3f2fd, #bbdefb);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            display: none;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .explanation-title {
            font-size: 1.4rem;
            color: #1976d2;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .explanation-text {
            line-height: 1.6;
            color: #333;
        }

        .reset-btn {
            background: linear-gradient(145deg, #ff6b6b, #ee5a52);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 20px auto;
            display: block;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .reset-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
    </style>
</head>
<body>
    <canvas class="floating-particles" id="particleCanvas"></canvas>
    
    <div class="container">
        <h1 class="title">🚀 嵌入式处理器探索之旅</h1>
        
        <div class="game-board">
            <div class="question-section">
                <div class="question-title">
                    📚 <strong>题目：</strong>嵌入式处理器是嵌入式系统的核心部件，一般可分为嵌入式微处理器(MPU)、微控制器(MCU)、数字信号处理器(DSP)和片上系统(SOC)。以下叙述中，<span style="color: #f44336; font-weight: bold;">错误的是</span>（ ）
                </div>
                
                <div class="processor-grid">
                    <div class="processor-card" data-option="A" data-correct="true">
                        <div class="processor-icon">🧠</div>
                        <div class="processor-name">选项 A - MPU</div>
                        <div class="processor-desc">MPU在安全性和可靠性等方面进行增强，适用于运算量较大的智能系统</div>
                    </div>
                    
                    <div class="processor-card" data-option="B" data-correct="false">
                        <div class="processor-icon">🔧</div>
                        <div class="processor-name">选项 B - MCU</div>
                        <div class="processor-desc">MCU典型代表是单片机，体积小从而使功耗和成本下降</div>
                    </div>
                    
                    <div class="processor-card" data-option="C" data-correct="false">
                        <div class="processor-icon">📡</div>
                        <div class="processor-name">选项 C - DSP</div>
                        <div class="processor-desc">DSP处理器对系统结构和指令进行了特殊设计，适合数字信号处理</div>
                    </div>
                    
                    <div class="processor-card" data-option="D" data-correct="false">
                        <div class="processor-icon">🎯</div>
                        <div class="processor-name">选项 D - SOC</div>
                        <div class="processor-desc">SOC是一个有专用目标的集成电路，其中包括完整系统并有嵌入式软件的全部内容</div>
                    </div>
                </div>
            </div>
            
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="400"></canvas>
            </div>
            
            <button class="reset-btn" onclick="resetGame()">🔄 重新开始学习</button>
            
            <div class="explanation-panel" id="explanationPanel">
                <div class="explanation-title">💡 知识解析</div>
                <div class="explanation-text" id="explanationText"></div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            answered: false,
            selectedOption: null
        };

        // Canvas 动画
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const particleCanvas = document.getElementById('particleCanvas');
        const particleCtx = particleCanvas.getContext('2d');

        // 设置粒子画布大小
        function resizeParticleCanvas() {
            particleCanvas.width = window.innerWidth;
            particleCanvas.height = window.innerHeight;
        }
        resizeParticleCanvas();
        window.addEventListener('resize', resizeParticleCanvas);

        // 粒子系统
        const particles = [];
        for (let i = 0; i < 50; i++) {
            particles.push({
                x: Math.random() * particleCanvas.width,
                y: Math.random() * particleCanvas.height,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: Math.random() * 3 + 1,
                opacity: Math.random() * 0.5 + 0.2
            });
        }

        function animateParticles() {
            particleCtx.clearRect(0, 0, particleCanvas.width, particleCanvas.height);
            
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > particleCanvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > particleCanvas.height) particle.vy *= -1;
                
                particleCtx.beginPath();
                particleCtx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                particleCtx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
                particleCtx.fill();
            });
            
            requestAnimationFrame(animateParticles);
        }
        animateParticles();

        // 处理器动画
        let processors = [
            { name: 'MPU', x: 100, y: 200, color: '#ff6b6b', size: 0, targetSize: 60 },
            { name: 'MCU', x: 250, y: 200, color: '#4ecdc4', size: 0, targetSize: 50 },
            { name: 'DSP', x: 400, y: 200, color: '#45b7d1', size: 0, targetSize: 55 },
            { name: 'SOC', x: 550, y: 200, color: '#96ceb4', size: 0, targetSize: 65 }
        ];

        function drawProcessors() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            processors.forEach((proc, index) => {
                // 动画大小增长
                if (proc.size < proc.targetSize) {
                    proc.size += 2;
                }
                
                // 绘制处理器圆形
                ctx.beginPath();
                ctx.arc(proc.x, proc.y, proc.size, 0, Math.PI * 2);
                ctx.fillStyle = proc.color;
                ctx.fill();
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 3;
                ctx.stroke();
                
                // 绘制名称
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(proc.name, proc.x, proc.y + 5);
                
                // 绘制连接线（延迟显示）
                if (proc.size >= proc.targetSize - 10 && index < processors.length - 1) {
                    ctx.beginPath();
                    ctx.moveTo(proc.x + proc.size, proc.y);
                    ctx.lineTo(processors[index + 1].x - processors[index + 1].size, processors[index + 1].y);
                    ctx.strokeStyle = '#ddd';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                }
            });
            
            requestAnimationFrame(drawProcessors);
        }
        drawProcessors();

        // 选项点击处理
        document.querySelectorAll('.processor-card').forEach(card => {
            card.addEventListener('click', function() {
                if (gameState.answered) return;
                
                const option = this.dataset.option;
                const isCorrect = this.dataset.correct === 'true';
                
                gameState.answered = true;
                gameState.selectedOption = option;
                
                // 显示结果
                if (isCorrect) {
                    this.classList.add('correct');
                    showExplanation(true);
                } else {
                    this.classList.add('wrong');
                    showExplanation(false);
                }
                
                // 禁用其他选项
                document.querySelectorAll('.processor-card').forEach(otherCard => {
                    if (otherCard !== this) {
                        otherCard.style.opacity = '0.5';
                        otherCard.style.pointerEvents = 'none';
                    }
                });
            });
        });

        function showExplanation(isCorrect) {
            const panel = document.getElementById('explanationPanel');
            const text = document.getElementById('explanationText');
            
            if (isCorrect) {
                text.innerHTML = `
                    <p><strong>🎉 恭喜你答对了！</strong></p>
                    <p><strong>正确答案是 A</strong></p>
                    <br>
                    <p><strong>📖 详细解析：</strong></p>
                    <p><strong>选项A是错误的：</strong>MPU采用增强型通用微处理器。虽然MPU在工作温度、电磁兼容性以及可靠性方面要求较高，但MPU在功能方面与标准的微处理器基本相同，<span style="color: #f44336;">并不是专门为运算量较大的智能系统设计的</span>。</p>
                    <br>
                    <p><strong>其他选项为什么是正确的：</strong></p>
                    <p><strong>B选项正确：</strong>MCU（单片机）将CPU、RAM、ROM等集成在一片芯片上，体积小、功耗低、成本低。</p>
                    <p><strong>C选项正确：</strong>DSP专门为数字信号处理设计，运算速度快，适合处理大量数字信号。</p>
                    <p><strong>D选项正确：</strong>SOC是系统级芯片，包含完整系统和嵌入式软件的全部内容。</p>
                `;
            } else {
                text.innerHTML = `
                    <p><strong>❌ 很遗憾，答案不正确</strong></p>
                    <p><strong>正确答案是 A</strong></p>
                    <br>
                    <p><strong>💡 让我们一起学习：</strong></p>
                    <p>题目问的是<strong>错误的</strong>选项，选项A的描述是错误的，因为MPU并不是专门为运算量较大的智能系统设计的，它只是在可靠性方面有所增强的通用微处理器。</p>
                    <br>
                    <p><strong>🔍 记忆技巧：</strong></p>
                    <p>• <strong>MPU</strong>：增强版通用处理器（不是专用的）</p>
                    <p>• <strong>MCU</strong>：单片机，小巧省电</p>
                    <p>• <strong>DSP</strong>：数字信号处理专家</p>
                    <p>• <strong>SOC</strong>：系统级芯片，功能最全</p>
                `;
            }
            
            panel.style.display = 'block';
        }

        function resetGame() {
            gameState.answered = false;
            gameState.selectedOption = null;
            
            // 重置卡片状态
            document.querySelectorAll('.processor-card').forEach(card => {
                card.classList.remove('correct', 'wrong');
                card.style.opacity = '1';
                card.style.pointerEvents = 'auto';
            });
            
            // 隐藏解析面板
            document.getElementById('explanationPanel').style.display = 'none';
            
            // 重置处理器动画
            processors.forEach(proc => {
                proc.size = 0;
            });
        }
    </script>
</body>
</html>
