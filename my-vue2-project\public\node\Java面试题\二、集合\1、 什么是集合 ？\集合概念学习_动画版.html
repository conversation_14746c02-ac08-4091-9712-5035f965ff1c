<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集合概念学习 - 动画互动版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }
        .section:nth-child(5) { animation-delay: 0.8s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            cursor: pointer;
        }

        .text-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 集合概念学习</h1>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎪 什么是集合？</h2>
            <div class="canvas-container">
                <canvas id="introCanvas" width="600" height="300"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">集合框架</span>是用于存储数据的容器，就像一个神奇的魔法盒子！</p>
                <p>它是为表示和操作集合而规定的一种统一的标准的体系结构。</p>
                <button class="interactive-btn" onclick="animateIntro()">🎬 播放动画</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🏗️ 集合框架的三大组成</h2>
            <div class="canvas-container">
                <canvas id="frameworkCanvas" width="700" height="400"></canvas>
            </div>
            <div class="text-content">
                <p>任何集合框架都包含三大块内容：</p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li><span class="highlight">接口</span> - 规范和标准</li>
                    <li><span class="highlight">实现</span> - 具体的数据结构</li>
                    <li><span class="highlight">算法</span> - 操作和计算方法</li>
                </ul>
                <button class="interactive-btn" onclick="animateFramework()">🎯 展示三大组成</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎭 接口：规范的制定者</h2>
            <div class="canvas-container">
                <canvas id="interfaceCanvas" width="600" height="350"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">接口</span>表示集合的抽象数据类型，就像建筑的设计图纸！</p>
                <p>接口允许我们操作集合时不必关注具体实现，从而达到"多态"。</p>
                <p>在面向对象编程语言中，接口通常用来形成规范。</p>
                <button class="interactive-btn" onclick="animateInterface()">📋 看看接口如何工作</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🏭 实现：具体的建造者</h2>
            <div class="canvas-container">
                <canvas id="implementationCanvas" width="600" height="350"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">实现</span>是集合接口的具体实现，是重用性很高的数据结构。</p>
                <p>就像根据设计图纸建造出的真实建筑物！</p>
                <button class="interactive-btn" onclick="animateImplementation()">🔧 观看实现过程</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚡ 算法：智能的操作者</h2>
            <div class="canvas-container">
                <canvas id="algorithmCanvas" width="600" height="350"></canvas>
            </div>
            <div class="text-content">
                <p><span class="highlight">算法</span>是在集合对象上完成某种有用计算的方法，例如查找、排序等。</p>
                <p>这些算法通常是多态的，因为相同的方法可以在不同的实现上有不同的表现。</p>
                <p>算法是可复用的函数，它减少了程序设计的辛劳。</p>
                <button class="interactive-btn" onclick="animateAlgorithm()">🚀 体验算法魔法</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 集合框架的优势</h2>
            <div class="canvas-container">
                <canvas id="benefitsCanvas" width="700" height="300"></canvas>
            </div>
            <div class="text-content">
                <p>集合框架带来的好处：</p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>🎯 <span class="highlight">专注核心</span> - 让你专注于程序的重要部分</li>
                    <li>🔄 <span class="highlight">简易互用</span> - 免除大量适配代码</li>
                    <li>⚡ <span class="highlight">提升性能</span> - 提高程序速度和质量</li>
                </ul>
                <button class="interactive-btn" onclick="animateBenefits()">✨ 展示优势</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎓 学习总结</h2>
            <div class="canvas-container">
                <canvas id="summaryCanvas" width="700" height="250"></canvas>
            </div>
            <div class="text-content">
                <p>🎉 恭喜你！现在你已经了解了集合框架的核心概念：</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: linear-gradient(45deg, #ff6b6b, #ff8e8e); padding: 20px; border-radius: 15px; color: white; text-align: center;">
                        <h3>🎭 接口</h3>
                        <p>规范和标准</p>
                    </div>
                    <div style="background: linear-gradient(45deg, #4ecdc4, #6ee5dd); padding: 20px; border-radius: 15px; color: white; text-align: center;">
                        <h3>🏭 实现</h3>
                        <p>具体数据结构</p>
                    </div>
                    <div style="background: linear-gradient(45deg, #45b7d1, #6cc5e0); padding: 20px; border-radius: 15px; color: white; text-align: center;">
                        <h3>⚡ 算法</h3>
                        <p>操作和计算</p>
                    </div>
                </div>
                <button class="interactive-btn" onclick="animateSummary()">🎊 播放总结动画</button>
                <button class="interactive-btn" onclick="playAllAnimations()">🎬 播放全部动画</button>
            </div>
        </div>
    </div>

    <script>
        let currentProgress = 0;
        
        function updateProgress(progress) {
            currentProgress = Math.min(100, currentProgress + progress);
            document.getElementById('progressFill').style.width = currentProgress + '%';
        }

        // 介绍动画
        function animateIntro() {
            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景渐变
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#f093fb');
                gradient.addColorStop(1, '#f5576c');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 动态容器
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const time = frame * 0.1;
                
                // 主容器
                ctx.save();
                ctx.translate(centerX, centerY);
                ctx.rotate(time * 0.02);
                
                // 容器外框
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 4;
                ctx.setLineDash([10, 5]);
                ctx.lineDashOffset = -time * 2;
                ctx.strokeRect(-100, -60, 200, 120);
                
                ctx.restore();
                
                // 数据元素飞入动画
                for (let i = 0; i < 5; i++) {
                    const angle = (time + i * 1.2) * 0.05;
                    const radius = 80 + Math.sin(time * 0.03 + i) * 20;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    ctx.fillStyle = `hsl(${(time * 2 + i * 60) % 360}, 70%, 60%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, 8 + Math.sin(time * 0.1 + i) * 3, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 数据标签
                    ctx.fillStyle = '#fff';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`数据${i+1}`, x, y + 25);
                }
                
                // 标题文字
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('集合 = 数据容器', centerX, 50);
                
                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(25);
                }
            }
            
            draw();
        }

        // 框架组成动画
        function animateFramework() {
            const canvas = document.getElementById('frameworkCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            const components = [
                { name: '接口', color: '#ff6b6b', x: 150, y: 200, desc: '规范标准' },
                { name: '实现', color: '#4ecdc4', x: 350, y: 200, desc: '数据结构' },
                { name: '算法', color: '#45b7d1', x: 550, y: 200, desc: '操作方法' }
            ];
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                const gradient = ctx.createRadialGradient(350, 200, 0, 350, 200, 300);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                const progress = Math.min(1, frame / 120);
                
                // 绘制组件
                components.forEach((comp, index) => {
                    const delay = index * 40;
                    const localProgress = Math.max(0, Math.min(1, (frame - delay) / 60));
                    
                    if (localProgress > 0) {
                        const scale = localProgress;
                        const alpha = localProgress;
                        
                        ctx.save();
                        ctx.globalAlpha = alpha;
                        ctx.translate(comp.x, comp.y);
                        ctx.scale(scale, scale);
                        
                        // 组件圆圈
                        ctx.fillStyle = comp.color;
                        ctx.beginPath();
                        ctx.arc(0, 0, 50, 0, Math.PI * 2);
                        ctx.fill();
                        
                        // 组件名称
                        ctx.fillStyle = '#fff';
                        ctx.font = 'bold 18px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(comp.name, 0, 5);
                        
                        // 描述文字
                        ctx.font = '14px Arial';
                        ctx.fillText(comp.desc, 0, 80);
                        
                        // 连接线动画
                        if (index < components.length - 1 && localProgress > 0.5) {
                            const nextComp = components[index + 1];
                            const lineProgress = (localProgress - 0.5) * 2;
                            
                            ctx.strokeStyle = '#fff';
                            ctx.lineWidth = 3;
                            ctx.setLineDash([5, 5]);
                            ctx.lineDashOffset = -frame * 0.5;
                            
                            ctx.beginPath();
                            ctx.moveTo(50, 0);
                            ctx.lineTo(50 + (nextComp.x - comp.x - 100) * lineProgress, 0);
                            ctx.stroke();
                        }
                        
                        ctx.restore();
                    }
                });
                
                // 标题
                if (progress > 0.8) {
                    ctx.fillStyle = '#fff';
                    ctx.font = 'bold 28px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('集合框架三大组成', canvas.width / 2, 80);
                }
                
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(25);
                }
            }
            
            draw();
        }

        // 接口动画
        function animateInterface() {
            const canvas = document.getElementById('interfaceCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#ff9a9e');
                gradient.addColorStop(1, '#fecfef');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const time = frame * 0.1;

                // 接口规范书
                ctx.save();
                ctx.translate(centerX, centerY);

                // 规范文档
                ctx.fillStyle = '#fff';
                ctx.fillRect(-80, -60, 160, 120);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(-80, -60, 160, 120);

                // 文档内容线条
                for (let i = 0; i < 6; i++) {
                    const lineProgress = Math.max(0, Math.min(1, (frame - i * 10) / 30));
                    ctx.strokeStyle = '#667eea';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(-60, -30 + i * 15);
                    ctx.lineTo(-60 + 120 * lineProgress, -30 + i * 15);
                    ctx.stroke();
                }

                // 标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('接口规范', 0, -70);

                ctx.restore();

                // 多态箭头
                if (frame > 60) {
                    const arrowProgress = Math.min(1, (frame - 60) / 40);

                    for (let i = 0; i < 3; i++) {
                        const angle = (i * 120 - 90) * Math.PI / 180;
                        const startX = centerX + Math.cos(angle) * 100;
                        const startY = centerY + Math.sin(angle) * 100;
                        const endX = centerX + Math.cos(angle) * (100 + 80 * arrowProgress);
                        const endY = centerY + Math.sin(angle) * (100 + 80 * arrowProgress);

                        ctx.strokeStyle = '#ff6b6b';
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(startX, startY);
                        ctx.lineTo(endX, endY);
                        ctx.stroke();

                        // 箭头头部
                        ctx.fillStyle = '#ff6b6b';
                        ctx.beginPath();
                        ctx.arc(endX, endY, 5, 0, Math.PI * 2);
                        ctx.fill();

                        // 实现标签
                        ctx.fillStyle = '#333';
                        ctx.font = '12px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(`实现${i+1}`, endX, endY + 20);
                    }
                }

                frame++;
                if (frame < 150) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(20);
                }
            }

            draw();
        }

        // 实现动画
        function animateImplementation() {
            const canvas = document.getElementById('implementationCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            const structures = [
                { name: 'ArrayList', x: 150, y: 150, color: '#ff6b6b' },
                { name: 'LinkedList', x: 300, y: 150, color: '#4ecdc4' },
                { name: 'HashSet', x: 450, y: 150, color: '#45b7d1' }
            ];

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#a8edea');
                gradient.addColorStop(1, '#fed6e3');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制数据结构
                structures.forEach((struct, index) => {
                    const delay = index * 30;
                    const progress = Math.max(0, Math.min(1, (frame - delay) / 60));

                    if (progress > 0) {
                        ctx.save();
                        ctx.translate(struct.x, struct.y);
                        ctx.scale(progress, progress);

                        // 结构容器
                        ctx.fillStyle = struct.color;
                        ctx.fillRect(-40, -30, 80, 60);

                        // 数据元素
                        for (let i = 0; i < 3; i++) {
                            ctx.fillStyle = '#fff';
                            ctx.fillRect(-30 + i * 20, -15, 15, 15);
                            ctx.fillStyle = '#333';
                            ctx.font = '10px Arial';
                            ctx.textAlign = 'center';
                            ctx.fillText(i+1, -22.5 + i * 20, -7);
                        }

                        // 名称
                        ctx.fillStyle = '#333';
                        ctx.font = 'bold 14px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(struct.name, 0, 50);

                        ctx.restore();
                    }
                });

                frame++;
                if (frame < 150) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(20);
                }
            }

            draw();
        }

        // 算法动画
        function animateAlgorithm() {
            const canvas = document.getElementById('algorithmCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            const data = [64, 34, 25, 12, 22, 11, 90];
            let sortedData = [...data];
            let currentStep = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#ffecd2');
                gradient.addColorStop(1, '#fcb69f');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const startY = 200;

                // 排序算法演示
                if (frame < 200) {
                    // 显示原始数据
                    data.forEach((value, index) => {
                        const x = centerX - (data.length * 30) / 2 + index * 40;
                        const height = value * 2;

                        ctx.fillStyle = '#ff6b6b';
                        ctx.fillRect(x, startY - height, 30, height);

                        ctx.fillStyle = '#333';
                        ctx.font = '12px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(value, x + 15, startY + 20);
                    });

                    ctx.fillStyle = '#333';
                    ctx.font = 'bold 18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('原始数据', centerX, 50);
                } else {
                    // 排序过程
                    const step = Math.floor((frame - 200) / 30);
                    if (step < data.length - 1) {
                        // 简单的冒泡排序可视化
                        for (let i = 0; i < data.length - step - 1; i++) {
                            if (sortedData[i] > sortedData[i + 1]) {
                                [sortedData[i], sortedData[i + 1]] = [sortedData[i + 1], sortedData[i]];
                            }
                        }
                    }

                    sortedData.forEach((value, index) => {
                        const x = centerX - (sortedData.length * 30) / 2 + index * 40;
                        const height = value * 2;

                        // 高亮正在比较的元素
                        const isComparing = step < data.length - 1 &&
                                          (index === step || index === step + 1);

                        ctx.fillStyle = isComparing ? '#4ecdc4' : '#45b7d1';
                        ctx.fillRect(x, startY - height, 30, height);

                        ctx.fillStyle = '#333';
                        ctx.font = '12px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(value, x + 15, startY + 20);
                    });

                    ctx.fillStyle = '#333';
                    ctx.font = 'bold 18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('排序算法工作中...', centerX, 50);
                }

                frame++;
                if (frame < 400) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(20);
                }
            }

            draw();
        }

        // 优势展示动画
        function animateBenefits() {
            const canvas = document.getElementById('benefitsCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            const benefits = [
                { text: '专注核心', icon: '🎯', x: 150, color: '#ff6b6b' },
                { text: '简易互用', icon: '🔄', x: 350, color: '#4ecdc4' },
                { text: '提升性能', icon: '⚡', x: 550, color: '#45b7d1' }
            ];

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const centerY = canvas.height / 2;

                benefits.forEach((benefit, index) => {
                    const delay = index * 40;
                    const progress = Math.max(0, Math.min(1, (frame - delay) / 60));

                    if (progress > 0) {
                        const y = centerY + Math.sin(frame * 0.05 + index) * 10;

                        ctx.save();
                        ctx.translate(benefit.x, y);
                        ctx.scale(progress, progress);

                        // 优势圆圈
                        ctx.fillStyle = benefit.color;
                        ctx.beginPath();
                        ctx.arc(0, 0, 40, 0, Math.PI * 2);
                        ctx.fill();

                        // 图标
                        ctx.font = '24px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(benefit.icon, 0, 8);

                        // 文字
                        ctx.fillStyle = '#fff';
                        ctx.font = 'bold 14px Arial';
                        ctx.fillText(benefit.text, 0, 70);

                        ctx.restore();
                    }
                });

                // 连接线
                if (frame > 120) {
                    ctx.strokeStyle = '#fff';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);
                    ctx.lineDashOffset = -frame * 0.3;

                    ctx.beginPath();
                    ctx.moveTo(190, centerY);
                    ctx.lineTo(310, centerY);
                    ctx.moveTo(390, centerY);
                    ctx.lineTo(510, centerY);
                    ctx.stroke();
                }

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(15);
                }
            }

            draw();
        }

        // 总结动画
        function animateSummary() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                const gradient = ctx.createRadialGradient(350, 125, 0, 350, 125, 300);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;

                // 庆祝粒子效果
                for (let i = 0; i < 20; i++) {
                    const angle = (frame * 0.02 + i * 0.3) % (Math.PI * 2);
                    const radius = 50 + Math.sin(frame * 0.05 + i) * 30;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;

                    ctx.fillStyle = `hsl(${(frame + i * 20) % 360}, 70%, 60%)`;
                    ctx.beginPath();
                    ctx.arc(x, y, 3 + Math.sin(frame * 0.1 + i) * 2, 0, Math.PI * 2);
                    ctx.fill();
                }

                // 中心文字
                ctx.fillStyle = '#fff';
                ctx.font = 'bold 28px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 学习完成！', centerX, centerY - 20);

                ctx.font = '16px Arial';
                ctx.fillText('你已经掌握了集合框架的核心概念', centerX, centerY + 20);

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                } else {
                    updateProgress(100);
                }
            }

            draw();
        }

        // 播放全部动画
        async function playAllAnimations() {
            const animations = [
                animateIntro,
                animateFramework,
                animateInterface,
                animateImplementation,
                animateAlgorithm,
                animateBenefits,
                animateSummary
            ];

            for (let i = 0; i < animations.length; i++) {
                animations[i]();
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }

        // 页面加载完成后自动播放介绍动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                animateIntro();
            }, 1000);
        });

        // 鼠标悬停效果
        document.querySelectorAll('.section').forEach(section => {
            section.addEventListener('mouseenter', () => {
                section.style.transform = 'translateY(-5px)';
                section.style.boxShadow = '0 25px 50px rgba(0,0,0,0.15)';
            });

            section.addEventListener('mouseleave', () => {
                section.style.transform = 'translateY(0)';
                section.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';
            });
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case '1':
                    animateIntro();
                    break;
                case '2':
                    animateFramework();
                    break;
                case '3':
                    animateInterface();
                    break;
                case '4':
                    animateImplementation();
                    break;
                case '5':
                    animateAlgorithm();
                    break;
                case '6':
                    animateBenefits();
                    break;
                case '7':
                    animateSummary();
                    break;
                case ' ':
                    e.preventDefault();
                    playAllAnimations();
                    break;
            }
        });

        // 添加提示信息
        const hint = document.createElement('div');
        hint.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 1000;
        `;
        hint.innerHTML = '💡 提示：按数字键1-7播放对应动画，空格键播放全部动画';
        document.body.appendChild(hint);

        // 3秒后隐藏提示
        setTimeout(() => {
            hint.style.opacity = '0';
            hint.style.transition = 'opacity 1s';
        }, 3000);
    </script>
</body>
</html>
