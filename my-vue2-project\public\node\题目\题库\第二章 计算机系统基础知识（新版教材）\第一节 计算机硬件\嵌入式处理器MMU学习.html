<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式处理器与MMU互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .processor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .processor-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .processor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .processor-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .processor-card.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .processor-card.wrong {
            border-color: #f44336;
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            color: white;
        }

        .mmu-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-left: 10px;
            transition: all 0.3s ease;
        }

        .has-mmu {
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }

        .no-mmu {
            background: #f44336;
            box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .game-controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .score {
            text-align: center;
            font-size: 1.2rem;
            margin: 20px 0;
            color: #333;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 嵌入式处理器与MMU探索之旅</h1>
            <p>通过互动游戏学习处理器架构知识</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🧠 什么是MMU？</h2>
            <div class="canvas-container">
                <canvas id="mmuCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation" id="mmuExplanation">
                <h3>💡 MMU (内存管理单元) 详解：</h3>
                <p><strong>MMU</strong> 是存储器管理单元，就像一个智能的"地址翻译官"！</p>
                <ul>
                    <li>🔄 <strong>虚拟地址转换</strong>：将程序使用的虚拟地址转换为实际的物理地址</li>
                    <li>🛡️ <strong>内存保护</strong>：防止程序访问不该访问的内存区域</li>
                    <li>📚 <strong>TLB缓存</strong>：存储地址转换表，提高转换速度</li>
                    <li>⚡ <strong>页面管理</strong>：管理内存页面的分配和回收</li>
                </ul>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🎯 处理器识别游戏</h2>
            <div class="score" id="gameScore">得分: 0 | 正确率: 0%</div>
            <div class="processor-grid" id="processorGrid">
                <!-- 处理器卡片将通过JavaScript动态生成 -->
            </div>
            <div class="game-controls">
                <button class="btn" onclick="startNewGame()">🎮 开始新游戏</button>
                <button class="btn" onclick="showHint()">💡 提示</button>
                <button class="btn" onclick="showExplanation()">📚 查看解析</button>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🔍 深度解析</h2>
            <div class="canvas-container">
                <canvas id="architectureCanvas" width="800" height="500"></canvas>
            </div>
            <div class="explanation show">
                <h3>🏗️ 各处理器架构特点：</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 15px;">
                    <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50;">
                        <h4>✅ 具备MMU的处理器</h4>
                        <p><strong>PowerPC750</strong>：高性能RISC处理器</p>
                        <p><strong>ARM920T</strong>：ARM9系列，支持虚拟内存</p>
                        <p><strong>MIPS32 24K</strong>：MIPS架构，企业级应用</p>
                    </div>
                    <div style="background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #f44336;">
                        <h4>❌ 不具备MMU的处理器</h4>
                        <p><strong>Cortex-M3</strong>：ARMv7-M架构</p>
                        <p>• 专为微控制器设计</p>
                        <p>• 支持μC/OS-II等实时操作系统</p>
                        <p>• 低功耗、高效率</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            score: 0,
            attempts: 0,
            currentQuestion: 0
        };

        // 处理器数据
        const processors = [
            { name: 'PowerPC750', hasMmu: true, description: '高性能RISC处理器，支持虚拟内存管理' },
            { name: 'ARM920T', hasMmu: true, description: 'ARM9系列处理器，具备完整MMU功能' },
            { name: 'Cortex-M3', hasMmu: false, description: 'ARMv7-M架构，专为微控制器设计，无MMU' },
            { name: 'MIPS32 24K', hasMmu: true, description: 'MIPS架构处理器，支持虚拟内存' }
        ];

        // 初始化MMU动画
        function initMmuAnimation() {
            const canvas = document.getElementById('mmuCanvas');
            const ctx = canvas.getContext('2d');
            
            let animationFrame = 0;
            
            function drawMmuDiagram() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制虚拟地址
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50, 100, 150, 60);
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('虚拟地址', 125, 135);
                
                // 绘制MMU
                const mmuX = 300;
                const mmuY = 80;
                ctx.fillStyle = '#764ba2';
                ctx.beginPath();
                ctx.roundRect(mmuX, mmuY, 200, 100, 10);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.fillText('MMU', mmuX + 100, mmuY + 35);
                ctx.fillText('地址转换', mmuX + 100, mmuY + 55);
                ctx.fillText('内存保护', mmuX + 100, mmuY + 75);
                
                // 绘制物理地址
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(600, 100, 150, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('物理地址', 675, 135);
                
                // 绘制箭头动画
                const arrowOffset = Math.sin(animationFrame * 0.1) * 10;
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(200, 130);
                ctx.lineTo(290 + arrowOffset, 130);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(510, 130);
                ctx.lineTo(590 + arrowOffset, 130);
                ctx.stroke();
                
                // 绘制TLB缓存
                ctx.fillStyle = '#FF9800';
                ctx.fillRect(320, 220, 160, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('TLB缓存', 400, 250);
                ctx.fillText('地址转换表', 400, 270);
                
                animationFrame++;
                requestAnimationFrame(drawMmuDiagram);
            }
            
            drawMmuDiagram();
            
            // 点击显示解释
            canvas.addEventListener('click', () => {
                document.getElementById('mmuExplanation').classList.add('show');
            });
        }

        // 初始化处理器游戏
        function initProcessorGame() {
            const grid = document.getElementById('processorGrid');
            grid.innerHTML = '';
            
            processors.forEach((processor, index) => {
                const card = document.createElement('div');
                card.className = 'processor-card';
                card.innerHTML = `
                    <h3>${processor.name}</h3>
                    <div class="mmu-indicator ${processor.hasMmu ? 'has-mmu' : 'no-mmu'}"></div>
                    <p style="margin-top: 10px; font-size: 0.9rem;">${processor.description}</p>
                `;
                
                card.addEventListener('click', () => selectProcessor(index, card));
                grid.appendChild(card);
            });
        }

        // 选择处理器
        function selectProcessor(index, cardElement) {
            // 清除之前的选择
            document.querySelectorAll('.processor-card').forEach(card => {
                card.classList.remove('selected', 'correct', 'wrong');
            });
            
            const processor = processors[index];
            gameState.attempts++;
            
            if (!processor.hasMmu) {
                // 正确答案
                cardElement.classList.add('correct');
                gameState.score++;
                showFeedback('🎉 正确！Cortex-M3确实没有MMU！', 'success');
            } else {
                // 错误答案
                cardElement.classList.add('wrong');
                showFeedback(`❌ 错误！${processor.name}是有MMU的处理器`, 'error');
                
                // 高亮正确答案
                setTimeout(() => {
                    const correctCard = document.querySelectorAll('.processor-card')[2]; // Cortex-M3
                    correctCard.classList.add('correct', 'pulse');
                }, 1000);
            }
            
            updateScore();
        }

        // 显示反馈
        function showFeedback(message, type) {
            const feedback = document.createElement('div');
            feedback.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: ${type === 'success' ? '#4CAF50' : '#f44336'};
                color: white;
                padding: 20px 40px;
                border-radius: 10px;
                font-size: 1.2rem;
                z-index: 1000;
                animation: fadeInUp 0.5s ease-out;
            `;
            feedback.textContent = message;
            document.body.appendChild(feedback);
            
            setTimeout(() => {
                feedback.remove();
            }, 3000);
        }

        // 更新分数
        function updateScore() {
            const accuracy = gameState.attempts > 0 ? Math.round((gameState.score / gameState.attempts) * 100) : 0;
            document.getElementById('gameScore').textContent = `得分: ${gameState.score} | 正确率: ${accuracy}%`;
        }

        // 开始新游戏
        function startNewGame() {
            gameState = { score: 0, attempts: 0, currentQuestion: 0 };
            updateScore();
            initProcessorGame();
        }

        // 显示提示
        function showHint() {
            showFeedback('💡 提示：Cortex-M3是专为微控制器设计的处理器，不需要复杂的虚拟内存管理', 'info');
        }

        // 显示解释
        function showExplanation() {
            const explanation = `
                📚 详细解析：
                
                MMU主要用于支持虚拟内存系统，这在复杂的操作系统中很重要。
                
                Cortex-M3采用ARMv7-M架构，专为实时嵌入式应用设计：
                • 不需要虚拟内存管理
                • 直接使用物理地址
                • 支持μC/OS-II等实时操作系统
                • 更低的功耗和更高的实时性
            `;
            showFeedback(explanation, 'info');
        }

        // 初始化架构对比动画
        function initArchitectureAnimation() {
            const canvas = document.getElementById('architectureCanvas');
            const ctx = canvas.getContext('2d');
            
            function drawArchitecture() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制有MMU的处理器架构
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(50, 50, 300, 180);
                ctx.fillStyle = 'white';
                ctx.font = '18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('具备MMU的处理器', 200, 80);
                
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('• 支持虚拟内存', 200, 110);
                ctx.fillText('• 复杂操作系统', 200, 130);
                ctx.fillText('• 多任务管理', 200, 150);
                ctx.fillText('• 内存保护', 200, 170);
                ctx.fillText('PowerPC750, ARM920T, MIPS32', 200, 200);
                
                // 绘制无MMU的处理器架构
                ctx.fillStyle = '#f44336';
                ctx.fillRect(450, 50, 300, 180);
                ctx.fillStyle = 'white';
                ctx.fillText('不具备MMU的处理器', 600, 80);
                
                ctx.fillText('• 直接物理地址', 600, 110);
                ctx.fillText('• 实时操作系统', 600, 130);
                ctx.fillText('• 低功耗设计', 600, 150);
                ctx.fillText('• 快速响应', 600, 170);
                ctx.fillText('Cortex-M3', 600, 200);
                
                // 绘制操作系统兼容性
                ctx.fillStyle = '#667eea';
                ctx.fillRect(200, 300, 400, 120);
                ctx.fillStyle = 'white';
                ctx.fillText('适用的嵌入式操作系统', 400, 330);
                
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('μC/OS-II', 300, 360);
                ctx.fillText('FreeRTOS', 400, 360);
                ctx.fillText('RT-Thread', 500, 360);
                
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText('这些实时操作系统可以在Cortex-M3上运行', 400, 390);
                ctx.fillText('无需MMU支持，直接管理物理内存', 400, 405);
            }
            
            drawArchitecture();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initMmuAnimation();
            initProcessorGame();
            initArchitectureAnimation();
        });
    </script>
</body>
</html>
