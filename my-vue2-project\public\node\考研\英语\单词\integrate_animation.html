<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Integrate</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #00838f; /* 青色，代表技术与融合 */
            --secondary-color: #006064;
            --accent-color: #ffab00; /* 橙色，点缀 */
            --light-bg: #e0f7fa;
            --panel-bg: #ffffff;
            --text-color: #004d40;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #b2ebf2;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--primary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #004d40;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .breakdown-section .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            margin: 5px;
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .breakdown-section .insight {
            margin-top: 15px;
            font-style: italic;
            color: #555;
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            background: #eceff1;
        }

        .activity-title {
            font-size: 1.8em;
            color: var(--primary-color);
            margin-bottom: 15px;
            text-align: center;
        }
        
        .activity-wrapper {
            display: none;
            width: 100%;
            height: calc(100% - 100px);
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .activity-wrapper.active {
            display: flex;
        }
        
        .game-container {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px;
            background: #cfd8dc;
            overflow: hidden;
        }

        .control-button {
            margin-top: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .control-button:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
        }
        .control-button:active { transform: translateY(1px); }

        #welcome-screen p { 
            font-size: 1.2em; 
            color: var(--text-muted); 
            text-align: center; 
        }

        /* Puzzle Game */
        #puzzle-container { display: grid; grid-template-columns: repeat(2, 100px); grid-template-rows: repeat(2, 100px); gap: 5px; border: 2px dashed var(--secondary-color); padding: 5px; background: #b0bec5; }
        .puzzle-slot { width: 100px; height: 100px; background-color: #90a4ae; border: 1px solid #78909c; }
        #puzzle-pieces { position: absolute; bottom: 10px; left: 10px; right: 10px; height: 120px; display: flex; justify-content: center; align-items: center; gap: 10px; }
        .puzzle-piece { width: 100px; height: 100px; background-size: 200px 200px; cursor: grab; transition: transform 0.2s; }
        .puzzle-piece:active { cursor: grabbing; transform: scale(1.1); }
        #piece1 { background-image: url('https://picsum.photos/id/1015/200/200'); background-position: 0 0; }
        #piece2 { background-image: url('https://picsum.photos/id/1015/200/200'); background-position: -100px 0; }
        #piece3 { background-image: url('https://picsum.photos/id/1015/200/200'); background-position: 0 -100px; }
        #piece4 { background-image: url('https://picsum.photos/id/1015/200/200'); background-position: -100px -100px; }
        .puzzle-slot.filled { border: 2px solid var(--accent-color); box-shadow: 0 0 10px var(--accent-color) inset; }

        /* Dataflow Game */
        #dataflow-canvas { background-color: #37474f; }
    </style>
</head>
<body>

    <div class="container">
        <div class="word-panel">
            <h1>integrate</h1>
            <p class="pronunciation">[ˈɪntɪɡreɪt]</p>
            <div class="details">
                <p><strong>词性：</strong> v. (使)合并，(使)成为一体</p>
                <p><strong>词源:</strong> 来自拉丁语 `integer` (完整的)，`in-` (非) + `tangere` (触摸) → 未被接触过的 → 完整的。</p>
                <p><strong>含义：</strong> 将不同的部分结合起来，形成一个更完整、统一、协调的整体。</p>
                <div class="example">
                    <p><strong>例句1:</strong> We need to integrate the new features into the existing system.</p>
                    <p><strong>翻译1:</strong> 我们需要将新功能整合到现有系统中。</p>
                    <p><strong>例句2:</strong> He integrated well into the new team.</p>
                    <p><strong>翻译2:</strong> 他很好地融入了新团队。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>单词动画</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="puzzle-game">互动: 拼图游戏</button>
                    <button class="morpheme-btn" data-activity="dataflow-game">动画: 数据流整合</button>
                </div>
            </div>

        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>

            <div id="welcome-screen" class="activity-wrapper active">
                <p>点击左侧按钮，体验"整合"的力量。</p>
            </div>

            <div id="puzzle-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="puzzle-container">
                        <div class="puzzle-slot" data-slot="1"></div><div class="puzzle-slot" data-slot="2"></div>
                        <div class="puzzle-slot" data-slot="3"></div><div class="puzzle-slot" data-slot="4"></div>
                    </div>
                    <div id="puzzle-pieces">
                        <div id="piece1" class="puzzle-piece" draggable="true" data-piece="1"></div>
                        <div id="piece2" class="puzzle-piece" draggable="true" data-piece="2"></div>
                        <div id="piece3" class="puzzle-piece" draggable="true" data-piece="3"></div>
                        <div id="piece4" class="puzzle-piece" draggable="true" data-piece="4"></div>
                    </div>
                </div>
            </div>
            
            <div id="dataflow-game" class="activity-wrapper">
                <div class="game-container"><canvas id="dataflow-canvas"></canvas></div>
                <button class="control-button" id="integrate-btn">整合</button>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');
        let currentCleanup = null;

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                if (currentCleanup) currentCleanup();
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                const targetId = btn.dataset.activity;
                document.getElementById(targetId)?.classList.add('active');
                
                if (targetId === 'puzzle-game') currentCleanup = setupPuzzleGame();
                else if (targetId === 'dataflow-game') currentCleanup = setupDataflowGame();
                else currentCleanup = null;
            });
        });

        function setupPuzzleGame() {
            const pieces = document.querySelectorAll('.puzzle-piece');
            const slots = document.querySelectorAll('.puzzle-slot');
            let draggedPiece = null;

            pieces.forEach(piece => {
                piece.addEventListener('dragstart', (e) => {
                    draggedPiece = e.target;
                    setTimeout(() => e.target.style.display = 'none', 0);
                });
                piece.addEventListener('dragend', (e) => {
                    draggedPiece = null;
                    e.target.style.display = 'block';
                });
            });

            slots.forEach(slot => {
                slot.addEventListener('dragover', e => e.preventDefault());
                slot.addEventListener('drop', (e) => {
                    e.preventDefault();
                    if (e.target.classList.contains('puzzle-slot') && !e.target.hasChildNodes()) {
                        if(draggedPiece && e.target.dataset.slot === draggedPiece.dataset.piece) {
                           e.target.appendChild(draggedPiece);
                           draggedPiece.style.position = 'absolute';
                           draggedPiece.style.left = '0';
                           draggedPiece.style.top = '0';
                           e.target.classList.add('filled');
                           checkWin();
                        }
                    }
                });
            });
            
            function checkWin() {
                const filledSlots = document.querySelectorAll('.puzzle-slot.filled').length;
                if(filledSlots === 4) {
                    gsap.to('.puzzle-slot.filled', {
                        boxShadow: "0 0 20px 10px var(--accent-color)",
                        duration: 0.5,
                        repeat: 3,
                        yoyo: true
                    });
                }
            }

            return () => { /* No specific cleanup needed for this simple drag-drop */ };
        }

        function setupDataflowGame() {
            const canvas = document.getElementById('dataflow-canvas');
            const btn = document.getElementById('integrate-btn');
            if (!canvas) return null;

            const ctx = canvas.getContext('2d');
            let animationId;
            let particles = [];
            let isIntegrated = false;
            const colors = ['#81d4fa', '#a5d6a7', '#ffcc80'];
            const integrator = { x: canvas.width / 2, y: canvas.height / 2, width: 100, height: 200 };

            class Particle {
                constructor(x, y, color) {
                    this.x = x; this.y = y; this.color = color;
                    this.vx = (Math.random() - 0.2) * 4;
                    this.vy = (Math.random() - 0.5) * 4;
                    this.radius = Math.random() * 2 + 2;
                }
                update() {
                    if (isIntegrated) {
                        const dx = integrator.x - this.x;
                        const dy = integrator.y - this.y;
                        const dist = Math.hypot(dx, dy);
                        if (dist < 150) {
                            this.vx += dx * 0.001;
                            this.vy += dy * 0.001;
                        }
                        if (this.x > integrator.x) {
                           this.color = '#ce93d8'; // Integrated color
                           this.vx = 2; // Move out in an ordered fashion
                           this.vy = 0;
                        }
                    }
                    this.x += this.vx; this.y += this.vy;
                    if (this.x > canvas.width || this.x < 0 || this.y > canvas.height || this.y < 0) {
                       // Reset particle
                       this.x = Math.random() * 50;
                       this.y = Math.random() * canvas.height;
                    }
                }
                draw() { ctx.fillStyle = this.color; ctx.beginPath(); ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2); ctx.fill(); }
            }

            function init() {
                const container = canvas.parentElement;
                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;
                integrator.x = canvas.width / 2 - 50;
                integrator.y = canvas.height / 2 - 100;
                particles = [];
                for (let i = 0; i < 300; i++) {
                    particles.push(new Particle(Math.random() * 50, Math.random() * canvas.height, colors[i % 3]));
                }
            }

            function animate() {
                ctx.fillStyle = 'rgba(55, 71, 79, 0.2)';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                if (isIntegrated) {
                    ctx.fillStyle = 'rgba(0, 131, 143, 0.5)';
                    ctx.fillRect(integrator.x, integrator.y, integrator.width, integrator.height);
                    ctx.strokeStyle = 'var(--accent-color)';
                    ctx.strokeRect(integrator.x, integrator.y, integrator.width, integrator.height);
                }

                particles.forEach(p => { p.update(); p.draw(); });
                animationId = requestAnimationFrame(animate);
            }

            btn.onclick = () => {
                isIntegrated = !isIntegrated;
                btn.textContent = isIntegrated ? '取消整合' : '整合';
            };

            init();
            animate();

            return () => { if (animationId) cancelAnimationFrame(animationId); };
        }
    });
    </script>

</body>
</html> 