<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Demote</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            user-select: none;
        }
        .container {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 30px;
            width: 90%;
            max-width: 800px;
            text-align: center;
        }
        h1 {
            font-size: 3em;
            color: #1a73e8;
            margin-bottom: 10px;
            font-weight: 600;
        }
        .pronunciation {
            font-size: 1.2em;
            color: #5f6368;
            margin-bottom: 20px;
        }
        #wordCanvas {
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .controls {
            margin-bottom: 20px;
        }
        .button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 1em;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin: 0 10px;
        }
        .button:hover {
            background-color: #155ab6;
        }
        .button:active {
            transform: scale(0.98);
        }
        .explanation-box {
            text-align: left;
            padding: 20px;
            background-color: #e8f0fe;
            border-left: 5px solid #1a73e8;
            border-radius: 4px;
            margin-top: 20px;
        }
        .explanation-box h2 {
            margin-top: 0;
            color: #1a73e8;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>demote</h1>
    <p class="pronunciation">[diˈmoʊt]</p>
    
    <canvas id="wordCanvas" width="800" height="400"></canvas>
    
    <div class="controls">
        <button class="button" onclick="playAnimation()">播放动画</button>
        <button class="button" onclick="resetAnimation()">重置</button>
    </div>

    <div class="explanation-box">
        <h2>单词翻译与解析</h2>
        <p><strong>demote (v.)</strong>: 降级，降职</p>
        <p><strong>记忆方法</strong>: 这是一个非常形象的单词，我们可以把它拆解成两部分来理解：</p>
        <ul>
            <li><strong>de-</strong>: 一个常见的前缀，表示"向下"（down）或"离开"（away from）。</li>
            <li><strong>mote</strong>: 这部分与词根 <strong>mot</strong> 同源，来自拉丁语的 <em>movere</em>，意思是"移动"（move）。</li>
        </ul>
        <p>所以，<strong>demote</strong> 的字面意思就是"<strong>向下移动</strong>"，引申为"降级"或"降职"。动画将生动地为您演示这个过程。</p>
    </div>
    
    <div class="explanation-box">
        <h2>例句</h2>
        <p>He was <strong>demoted</strong> from manager to assistant for his poor performance.</p>
        <p>他因业绩不佳，从经理<strong>降职</strong>为助理。</p>
    </div>
</div>

<script>
const canvas = document.getElementById('wordCanvas');
const ctx = canvas.getContext('2d');
let animationFrameId;

const config = {
    width: canvas.width,
    height: canvas.height,
    font: "bold 48px Arial",
    colors: {
        text: '#333',
        prefix: '#e74c3c',
        root: '#3498db',
        arrow: '#e74c3c',
        main: '#1a73e8'
    },
    stages: {
        START: 0,
        SPLIT: 1,
        DE_EXPLAIN: 2,
        MOTE_EXPLAIN: 3,
        REJOIN: 4,
        LADDER_SCENE: 5,
        FINISH: 6
    }
};

let currentStage = config.stages.START;
let progress = 0; // Animation progress from 0 to 1

function drawText(text, x, y, color, font) {
    ctx.fillStyle = color || config.colors.text;
    ctx.font = font || config.font;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, x, y);
}

function drawArrow(fromX, fromY, toX, toY, color) {
    const headlen = 15;
    const dx = toX - fromX;
    const dy = toY - fromY;
    const angle = Math.atan2(dy, dx);
    ctx.strokeStyle = color;
    ctx.lineWidth = 5;
    ctx.beginPath();
    ctx.moveTo(fromX, fromY);
    ctx.lineTo(toX, toY);
    ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
    ctx.moveTo(toX, toY);
    ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
    ctx.stroke();
}

function drawLadder(x, height) {
    ctx.strokeStyle = '#a0522d';
    ctx.lineWidth = 8;
    const width = 80;
    const rungs = 5;
    const rungSpacing = height / (rungs + 1);

    // Rails
    ctx.beginPath();
    ctx.moveTo(x - width / 2, 50);
    ctx.lineTo(x - width / 2, 50 + height);
    ctx.moveTo(x + width / 2, 50);
    ctx.lineTo(x + width / 2, 50 + height);
    ctx.stroke();

    // Rungs
    ctx.lineWidth = 6;
    for (let i = 1; i <= rungs; i++) {
        ctx.beginPath();
        ctx.moveTo(x - width / 2, 50 + i * rungSpacing);
        ctx.lineTo(x + width / 2, 50 + i * rungSpacing);
        ctx.stroke();
    }
}

function drawStickFigure(x, y) {
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 4;

    // Head
    ctx.beginPath();
    ctx.arc(x, y - 20, 10, 0, Math.PI * 2);
    ctx.stroke();

    // Body
    ctx.beginPath();
    ctx.moveTo(x, y - 10);
    ctx.lineTo(x, y + 20);
    ctx.stroke();

    // Arms
    ctx.beginPath();
    ctx.moveTo(x - 15, y);
    ctx.lineTo(x, y + 10);
    ctx.lineTo(x + 15, y);
    ctx.stroke();

    // Legs
    ctx.beginPath();
    ctx.moveTo(x, y + 20);
    ctx.lineTo(x - 10, y + 40);
    ctx.moveTo(x, y + 20);
    ctx.lineTo(x + 10, y + 40);
    ctx.stroke();
}

function animate() {
    ctx.clearRect(0, 0, config.width, config.height);
    
    progress += 0.01;
    if (progress > 1) progress = 1;

    switch(currentStage) {
        case config.stages.START:
            drawText('demote', config.width / 2, config.height / 2, config.colors.main);
            break;
        case config.stages.SPLIT: {
            const dePos = config.width / 2 - 80 - 100 * progress;
            const motePos = config.width / 2 + 50 + 100 * progress;
            drawText('de', dePos, config.height / 2, config.colors.prefix);
            drawText('mote', motePos, config.height / 2, config.colors.root);
            break;
        }
        case config.stages.DE_EXPLAIN: {
            drawText('de', config.width / 2 - 180, config.height / 2, config.colors.prefix);
            drawText('mote', config.width / 2 + 150, config.height / 2, config.colors.root);
            
            ctx.globalAlpha = progress;
            drawText("前缀: 向下", config.width / 2 - 180, config.height / 2 + 60, '#555', '30px Arial');
            drawArrow(config.width / 2 - 180, config.height / 2 + 90, config.width / 2 - 180, config.height / 2 + 140, config.colors.arrow);
            ctx.globalAlpha = 1;
            break;
        }
        case config.stages.MOTE_EXPLAIN: {
            drawText('de', config.width / 2 - 180, config.height / 2, config.colors.prefix);
            drawText("前缀: 向下", config.width / 2 - 180, config.height / 2 + 60, '#555', '30px Arial');
            drawArrow(config.width / 2 - 180, config.height / 2 + 90, config.width / 2 - 180, config.height / 2 + 140, config.colors.arrow);

            drawText('mote', config.width / 2 + 150, config.height / 2, config.colors.root);
            ctx.globalAlpha = progress;
            drawText("词根: 移动", config.width / 2 + 150, config.height / 2 + 60, '#555', '30px Arial');
             // simple walking animation
            const walkOffset = (progress * 20) % 20 - 10;
            drawStickFigure(config.width / 2 + 150 + walkOffset, config.height / 2 + 120);
            ctx.globalAlpha = 1;
            break;
        }
        case config.stages.REJOIN: {
            const dePos = config.width / 2 - 180 + 100 * progress;
            const motePos = config.width / 2 + 150 - 100 * progress;
            drawText('de', dePos, config.height / 2, config.colors.prefix);
            drawText('mote', motePos, config.height / 2, config.colors.root);
            if(progress >= 1) {
                ctx.clearRect(0, 0, config.width, config.height);
                drawText('demote', config.width / 2, config.height / 2, config.colors.main);
            }
            break;
        }
        case config.stages.LADDER_SCENE: {
            ctx.globalAlpha = 1 - progress;
            drawText('demote', config.width / 2, config.height / 2, config.colors.main);
            ctx.globalAlpha = progress;

            const ladderX = config.width / 2;
            const ladderHeight = 300;
            drawLadder(ladderX, ladderHeight);

            const rungHeight = ladderHeight / 6;
            const startRung = 2;
            const endRung = 3;
            const currentRung = startRung + (endRung - startRung) * progress;
            const figureY = 50 + currentRung * rungHeight - 40;
            
            drawStickFigure(ladderX, figureY);

            drawText('Manager', ladderX - 100, 50 + startRung * rungHeight);
            drawText('Assistant', ladderX - 100, 50 + endRung * rungHeight);
            
            if (progress > 0.3 && progress < 0.8) {
                const arrowProgress = (progress - 0.3) / 0.5;
                ctx.globalAlpha = Math.sin(arrowProgress * Math.PI);
                drawText('de-', ladderX + 100, figureY - 20, config.colors.prefix, "bold 40px Arial");
                drawArrow(ladderX + 100, figureY, ladderX + 20, figureY + 20, config.colors.arrow);
            }
            
            if (progress >= 1) {
                 drawText('demote: 降级, 降职', config.width/2, config.height - 30, config.colors.main, '32px Arial');
            }
            ctx.globalAlpha = 1;
            break;
        }
    }
    
    if (progress < 1) {
        animationFrameId = requestAnimationFrame(animate);
    } else {
        // Automatically advance to the next stage
        setTimeout(() => {
            if (currentStage < config.stages.FINISH) {
                currentStage++;
                progress = 0;
                if(currentStage !== config.stages.FINISH) {
                    animationFrameId = requestAnimationFrame(animate);
                }
            }
        }, currentStage === config.stages.SPLIT || currentStage === config.stages.REJOIN ? 500 : 1500);
    }
}

function playAnimation() {
    if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
    }
    resetAnimation();
    currentStage = config.stages.START;
    progress = 0;

    // Start the sequence after a short delay
    setTimeout(() => {
        currentStage++;
        progress = 0;
        animate();
    }, 500);
}

function resetAnimation() {
    if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
    }
    currentStage = config.stages.START;
    progress = 0;
    ctx.clearRect(0, 0, config.width, config.height);
    drawText('demote', config.width / 2, config.height / 2, config.colors.main);
}

// Initial draw
window.onload = resetAnimation;

</script>
</body>
</html> 