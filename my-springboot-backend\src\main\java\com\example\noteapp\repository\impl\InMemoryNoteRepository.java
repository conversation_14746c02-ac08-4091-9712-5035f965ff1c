package com.example.noteapp.repository.impl;

import com.example.noteapp.model.Note;
import com.example.noteapp.repository.NoteRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Repository
@Primary
public class InMemoryNoteRepository implements NoteRepository {

    private static final Logger logger = LoggerFactory.getLogger(InMemoryNoteRepository.class);

    private final Map<Long, Note> noteMap = new ConcurrentHashMap<>();
    private final AtomicLong idCounter = new AtomicLong(0);

    public InMemoryNoteRepository() {
        logger.info("使用内存存储作为笔记仓库");
    }

    @Override
    public List<Note> findAll() {
        return new ArrayList<>(noteMap.values());
    }

    @Override
    public Optional<Note> findById(Long id) {
        return Optional.ofNullable(noteMap.get(id));
    }

    @Override
    public Note save(Note note) {
        if (note.getId() == null) {
            // 新笔记，分配新 ID
            Long newId = idCounter.incrementAndGet();
            note.setId(newId);
        }
        
        noteMap.put(note.getId(), note);
        return note;
    }

    @Override
    public void deleteById(Long id) {
        noteMap.remove(id);
    }

    @Override
    public boolean existsById(Long id) {
        return noteMap.containsKey(id);
    }
} 