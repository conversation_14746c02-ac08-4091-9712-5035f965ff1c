<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构件分类方法</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .tab-container {
            margin-top: 20px;
        }
        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #ddd;
        }
        .tab-btn {
            padding: 10px 20px;
            background-color: #f1f1f1;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: 0.3s;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
        }
        .tab-btn.active {
            background-color: #3498db;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
            animation: fadeIn 0.5s;
            min-height: 300px;
        }
        .tab-content.active {
            display: block;
        }
        .canvas-container {
            margin-top: 20px;
            display: flex;
            justify-content: center;
        }
        canvas {
            border: 1px solid #ddd;
            background-color: #fff;
        }
        .controls {
            margin-top: 10px;
            text-align: center;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>基于构件的软件开发中的构件分类方法</h1>
        
        <div class="tab-container">
            <div class="tab-buttons">
                <button class="tab-btn active" onclick="openTab('keyword')">关键字分类法</button>
                <button class="tab-btn" onclick="openTab('facet')">刻面分类法</button>
                <button class="tab-btn" onclick="openTab('hypertext')">超文本方法</button>
            </div>
            
            <div id="keyword" class="tab-content active">
                <h2>关键字分类法</h2>
                <p>根据领域分析的结果将应用领域的概念按照从抽象到具体的顺序逐次分解为树形或有向无回路图结构。</p>
                <div class="canvas-container">
                    <canvas id="keywordCanvas" width="600" height="300"></canvas>
                </div>
                <div class="controls">
                    <button onclick="animateKeyword()">演示分类过程</button>
                    <button onclick="resetKeyword()">重置</button>
                </div>
            </div>
            
            <div id="facet" class="tab-content">
                <h2>刻面分类法</h2>
                <p>利用Facet（刻面）描述构件执行的功能、被操作的数据、构件应用的语境或任意其他特征。</p>
                <div class="canvas-container">
                    <canvas id="facetCanvas" width="600" height="300"></canvas>
                </div>
                <div class="controls">
                    <button onclick="animateFacet()">演示刻面分类</button>
                    <button onclick="resetFacet()">重置</button>
                </div>
            </div>
            
            <div id="hypertext" class="tab-content">
                <h2>超文本方法</h2>
                <p>基于全文检索技术，使得检索者在阅读文档过程中可以按照人类的联想思维方式任意地转到包含相关概念或构件的文档。</p>
                <div class="canvas-container">
                    <canvas id="hypertextCanvas" width="600" height="300"></canvas>
                </div>
                <div class="controls">
                    <button onclick="animateHypertext()">演示超文本连接</button>
                    <button onclick="resetHypertext()">重置</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页
        function openTab(tabName) {
            const tabContents = document.getElementsByClassName("tab-content");
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove("active");
            }
            const tabButtons = document.getElementsByClassName("tab-btn");
            for (let i = 0; i < tabButtons.length; i++) {
                tabButtons[i].classList.remove("active");
            }
            document.getElementById(tabName).classList.add("active");
            event.currentTarget.classList.add("active");
        }

        // 关键字分类法动画
        const keywordCanvas = document.getElementById("keywordCanvas");
        const keywordCtx = keywordCanvas.getContext("2d");
        let keywordAnimationId;

        function drawKeywordTree() {
            keywordCtx.clearRect(0, 0, keywordCanvas.width, keywordCanvas.height);
            
            // 绘制树根节点
            keywordCtx.fillStyle = "#3498db";
            keywordCtx.beginPath();
            keywordCtx.arc(300, 50, 25, 0, Math.PI * 2);
            keywordCtx.fill();
            keywordCtx.fillStyle = "white";
            keywordCtx.font = "12px Arial";
            keywordCtx.textAlign = "center";
            keywordCtx.fillText("软件构件", 300, 55);
            
            // 第二层节点
            const secondLevel = [
                {x: 150, y: 120, text: "用户界面构件", color: "#2ecc71"},
                {x: 300, y: 120, text: "业务逻辑构件", color: "#e74c3c"},
                {x: 450, y: 120, text: "数据访问构件", color: "#9b59b6"}
            ];
            
            // 第三层节点
            const thirdLevel = [
                {x: 100, y: 200, text: "按钮", parent: 0},
                {x: 200, y: 200, text: "表单", parent: 0},
                {x: 300, y: 200, text: "计算器", parent: 1},
                {x: 400, y: 200, text: "验证器", parent: 1},
                {x: 500, y: 200, text: "数据连接", parent: 2}
            ];
            
            // 绘制连线
            keywordCtx.strokeStyle = "#95a5a6";
            keywordCtx.lineWidth = 2;
            
            // 根到第二层的连线
            for (const node of secondLevel) {
                keywordCtx.beginPath();
                keywordCtx.moveTo(300, 75);
                keywordCtx.lineTo(node.x, node.y - 20);
                keywordCtx.stroke();
            }
            
            // 第二层到第三层的连线
            for (const node of thirdLevel) {
                const parentNode = secondLevel[node.parent];
                keywordCtx.beginPath();
                keywordCtx.moveTo(parentNode.x, parentNode.y + 20);
                keywordCtx.lineTo(node.x, node.y - 15);
                keywordCtx.stroke();
            }
            
            // 绘制第二层节点
            for (const node of secondLevel) {
                keywordCtx.fillStyle = node.color;
                keywordCtx.beginPath();
                keywordCtx.arc(node.x, node.y, 20, 0, Math.PI * 2);
                keywordCtx.fill();
                keywordCtx.fillStyle = "white";
                keywordCtx.font = "10px Arial";
                keywordCtx.fillText(node.text, node.x, node.y + 3);
            }
            
            // 绘制第三层节点
            for (const node of thirdLevel) {
                keywordCtx.fillStyle = secondLevel[node.parent].color;
                keywordCtx.beginPath();
                keywordCtx.arc(node.x, node.y, 15, 0, Math.PI * 2);
                keywordCtx.fill();
                keywordCtx.fillStyle = "white";
                keywordCtx.font = "10px Arial";
                keywordCtx.fillText(node.text, node.x, node.y + 3);
            }
        }

        function animateKeyword() {
            resetKeyword();
            let step = 0;
            
            function animate() {
                keywordCtx.clearRect(0, 0, keywordCanvas.width, keywordCanvas.height);
                
                // 绘制树根节点
                keywordCtx.fillStyle = "#3498db";
                keywordCtx.beginPath();
                keywordCtx.arc(300, 50, 25, 0, Math.PI * 2);
                keywordCtx.fill();
                keywordCtx.fillStyle = "white";
                keywordCtx.font = "12px Arial";
                keywordCtx.textAlign = "center";
                keywordCtx.fillText("软件构件", 300, 55);
                
                if (step > 30) {
                    // 第二层节点
                    const secondLevel = [
                        {x: 150, y: 120, text: "用户界面构件", color: "#2ecc71"},
                        {x: 300, y: 120, text: "业务逻辑构件", color: "#e74c3c"},
                        {x: 450, y: 120, text: "数据访问构件", color: "#9b59b6"}
                    ];
                    
                    // 绘制第二层节点的连线
                    keywordCtx.strokeStyle = "#95a5a6";
                    keywordCtx.lineWidth = 2;
                    
                    for (let i = 0; i < Math.min(3, Math.floor((step - 30) / 10) + 1); i++) {
                        const node = secondLevel[i];
                        keywordCtx.beginPath();
                        keywordCtx.moveTo(300, 75);
                        keywordCtx.lineTo(node.x, node.y - 20);
                        keywordCtx.stroke();
                        
                        keywordCtx.fillStyle = node.color;
                        keywordCtx.beginPath();
                        keywordCtx.arc(node.x, node.y, 20, 0, Math.PI * 2);
                        keywordCtx.fill();
                        keywordCtx.fillStyle = "white";
                        keywordCtx.font = "10px Arial";
                        keywordCtx.fillText(node.text, node.x, node.y + 3);
                    }
                }
                
                if (step > 60) {
                    // 第三层节点
                    const thirdLevel = [
                        {x: 100, y: 200, text: "按钮", parent: 0},
                        {x: 200, y: 200, text: "表单", parent: 0},
                        {x: 300, y: 200, text: "计算器", parent: 1},
                        {x: 400, y: 200, text: "验证器", parent: 1},
                        {x: 500, y: 200, text: "数据连接", parent: 2}
                    ];
                    
                    const secondLevel = [
                        {x: 150, y: 120, text: "用户界面构件", color: "#2ecc71"},
                        {x: 300, y: 120, text: "业务逻辑构件", color: "#e74c3c"},
                        {x: 450, y: 120, text: "数据访问构件", color: "#9b59b6"}
                    ];
                    
                    for (let i = 0; i < Math.min(5, Math.floor((step - 60) / 8) + 1); i++) {
                        const node = thirdLevel[i];
                        const parentNode = secondLevel[node.parent];
                        
                        keywordCtx.beginPath();
                        keywordCtx.moveTo(parentNode.x, parentNode.y + 20);
                        keywordCtx.lineTo(node.x, node.y - 15);
                        keywordCtx.stroke();
                        
                        keywordCtx.fillStyle = secondLevel[node.parent].color;
                        keywordCtx.beginPath();
                        keywordCtx.arc(node.x, node.y, 15, 0, Math.PI * 2);
                        keywordCtx.fill();
                        keywordCtx.fillStyle = "white";
                        keywordCtx.font = "10px Arial";
                        keywordCtx.fillText(node.text, node.x, node.y + 3);
                    }
                }
                
                step++;
                
                if (step <= 100) {
                    keywordAnimationId = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function resetKeyword() {
            if (keywordAnimationId) {
                cancelAnimationFrame(keywordAnimationId);
                keywordAnimationId = null;
            }
            drawKeywordTree();
        }

        // 刻面分类法动画
        const facetCanvas = document.getElementById("facetCanvas");
        const facetCtx = facetCanvas.getContext("2d");
        let facetAnimationId;

        function drawFacet() {
            facetCtx.clearRect(0, 0, facetCanvas.width, facetCanvas.height);
            
            // 中心构件
            facetCtx.fillStyle = "#3498db";
            facetCtx.beginPath();
            facetCtx.arc(300, 150, 30, 0, Math.PI * 2);
            facetCtx.fill();
            facetCtx.fillStyle = "white";
            facetCtx.font = "12px Arial";
            facetCtx.textAlign = "center";
            facetCtx.fillText("数据库访问构件", 300, 155);
            
            // 刻面
            const facets = [
                {angle: 0, text: "功能：数据查询", color: "#2ecc71"},
                {angle: Math.PI/2, text: "数据：用户信息", color: "#e74c3c"},
                {angle: Math.PI, text: "语境：身份验证", color: "#9b59b6"},
                {angle: Math.PI*3/2, text: "接口：REST API", color: "#f39c12"}
            ];
            
            facetCtx.lineWidth = 2;
            
            for (const facet of facets) {
                const x = 300 + Math.cos(facet.angle) * 100;
                const y = 150 + Math.sin(facet.angle) * 100;
                
                // 绘制连线
                facetCtx.strokeStyle = facet.color;
                facetCtx.beginPath();
                facetCtx.moveTo(300, 150);
                facetCtx.lineTo(x, y);
                facetCtx.stroke();
                
                // 绘制刻面
                facetCtx.fillStyle = facet.color;
                facetCtx.beginPath();
                facetCtx.arc(x, y, 25, 0, Math.PI * 2);
                facetCtx.fill();
                
                // 绘制文本
                facetCtx.fillStyle = "white";
                facetCtx.font = "10px Arial";
                const textLines = facet.text.split("：");
                facetCtx.fillText(textLines[0], x, y - 5);
                facetCtx.fillText(textLines[1], x, y + 10);
            }
        }

        function animateFacet() {
            resetFacet();
            let step = 0;
            
            function animate() {
                facetCtx.clearRect(0, 0, facetCanvas.width, facetCanvas.height);
                
                // 中心构件
                facetCtx.fillStyle = "#3498db";
                facetCtx.beginPath();
                facetCtx.arc(300, 150, 30, 0, Math.PI * 2);
                facetCtx.fill();
                facetCtx.fillStyle = "white";
                facetCtx.font = "12px Arial";
                facetCtx.textAlign = "center";
                facetCtx.fillText("数据库访问构件", 300, 155);
                
                // 刻面
                const facets = [
                    {angle: 0, text: "功能：数据查询", color: "#2ecc71"},
                    {angle: Math.PI/2, text: "数据：用户信息", color: "#e74c3c"},
                    {angle: Math.PI, text: "语境：身份验证", color: "#9b59b6"},
                    {angle: Math.PI*3/2, text: "接口：REST API", color: "#f39c12"}
                ];
                
                facetCtx.lineWidth = 2;
                
                for (let i = 0; i < Math.min(4, Math.floor(step / 20) + 1); i++) {
                    const facet = facets[i];
                    const distance = Math.min(100, step * 2 - i * 20);
                    const x = 300 + Math.cos(facet.angle) * distance;
                    const y = 150 + Math.sin(facet.angle) * distance;
                    
                    // 绘制连线
                    facetCtx.strokeStyle = facet.color;
                    facetCtx.beginPath();
                    facetCtx.moveTo(300, 150);
                    facetCtx.lineTo(x, y);
                    facetCtx.stroke();
                    
                    // 绘制刻面
                    facetCtx.fillStyle = facet.color;
                    facetCtx.beginPath();
                    facetCtx.arc(x, y, 25, 0, Math.PI * 2);
                    facetCtx.fill();
                    
                    // 绘制文本
                    facetCtx.fillStyle = "white";
                    facetCtx.font = "10px Arial";
                    const textLines = facet.text.split("：");
                    facetCtx.fillText(textLines[0], x, y - 5);
                    facetCtx.fillText(textLines[1], x, y + 10);
                }
                
                step++;
                
                if (step <= 100) {
                    facetAnimationId = requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        function resetFacet() {
            if (facetAnimationId) {
                cancelAnimationFrame(facetAnimationId);
                facetAnimationId = null;
            }
            drawFacet();
        }

        // 超文本方法动画
        const hypertextCanvas = document.getElementById("hypertextCanvas");
        const hypertextCtx = hypertextCanvas.getContext("2d");
        let hypertextAnimationId;
        let selectedNode = null;

        function drawHypertext() {
            hypertextCtx.clearRect(0, 0, hypertextCanvas.width, hypertextCanvas.height);
            
            // 文档节点
            const nodes = [
                {x: 100, y: 100, text: "登录构件", color: "#3498db"},
                {x: 250, y: 80, text: "用户认证", color: "#e74c3c"},
                {x: 400, y: 120, text: "权限管理", color: "#2ecc71"},
                {x: 150, y: 200, text: "数据加密", color: "#9b59b6"},
                {x: 300, y: 230, text: "密码重置", color: "#f39c12"},
                {x: 450, y: 180, text: "安全日志", color: "#1abc9c"}
            ];
            
            // 绘制连接线
            hypertextCtx.lineWidth = 1;
            
            // 完全图连接 - 每个节点都可能和其他节点相连
            for (let i = 0; i < nodes.length; i++) {
                for (let j = i + 1; j < nodes.length; j++) {
                    hypertextCtx.strokeStyle = "#95a5a6";
                    hypertextCtx.setLineDash([5, 3]);
                    hypertextCtx.beginPath();
                    hypertextCtx.moveTo(nodes[i].x, nodes[i].y);
                    hypertextCtx.lineTo(nodes[j].x, nodes[j].y);
                    hypertextCtx.stroke();
                }
            }
            hypertextCtx.setLineDash([]);
            
            // 绘制节点
            for (const node of nodes) {
                hypertextCtx.fillStyle = node.color;
                hypertextCtx.beginPath();
                hypertextCtx.arc(node.x, node.y, 25, 0, Math.PI * 2);
                hypertextCtx.fill();
                
                hypertextCtx.fillStyle = "white";
                hypertextCtx.font = "12px Arial";
                hypertextCtx.textAlign = "center";
                hypertextCtx.fillText(node.text, node.x, node.y + 4);
            }
            
            // 添加点击事件监听器
            hypertextCanvas.onclick = function(event) {
                const rect = hypertextCanvas.getBoundingClientRect();
                const mouseX = event.clientX - rect.left;
                const mouseY = event.clientY - rect.top;
                
                // 检查点击是否在节点上
                for (const node of nodes) {
                    const dx = mouseX - node.x;
                    const dy = mouseY - node.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (distance <= 25) {
                        selectedNode = node;
                        animateHypertext();
                        break;
                    }
                }
            };
        }

        function animateHypertext() {
            if (hypertextAnimationId) {
                cancelAnimationFrame(hypertextAnimationId);
                hypertextAnimationId = null;
            }
            
            if (!selectedNode) {
                // 如果没有选定节点，选择第一个作为默认
                selectedNode = {x: 100, y: 100, text: "登录构件", color: "#3498db"};
            }
            
            let step = 0;
            
            function animate() {
                hypertextCtx.clearRect(0, 0, hypertextCanvas.width, hypertextCanvas.height);
                
                // 文档节点
                const nodes = [
                    {x: 100, y: 100, text: "登录构件", color: "#3498db"},
                    {x: 250, y: 80, text: "用户认证", color: "#e74c3c"},
                    {x: 400, y: 120, text: "权限管理", color: "#2ecc71"},
                    {x: 150, y: 200, text: "数据加密", color: "#9b59b6"},
                    {x: 300, y: 230, text: "密码重置", color: "#f39c12"},
                    {x: 450, y: 180, text: "安全日志", color: "#1abc9c"}
                ];
                
                // 绘制虚线连接
                hypertextCtx.lineWidth = 1;
                hypertextCtx.strokeStyle = "#95a5a6";
                hypertextCtx.setLineDash([5, 3]);
                
                for (let i = 0; i < nodes.length; i++) {
                    for (let j = i + 1; j < nodes.length; j++) {
                        hypertextCtx.beginPath();
                        hypertextCtx.moveTo(nodes[i].x, nodes[i].y);
                        hypertextCtx.lineTo(nodes[j].x, nodes[j].y);
                        hypertextCtx.stroke();
                    }
                }
                hypertextCtx.setLineDash([]);
                
                // 绘制从选定节点到其他节点的实线连接 (动画效果)
                if (step > 20) {
                    hypertextCtx.lineWidth = 3;
                    
                    for (let i = 0; i < nodes.length; i++) {
                        if (nodes[i] === selectedNode) continue;
                        
                        const progress = Math.min(1, (step - 20) / 40);
                        
                        hypertextCtx.strokeStyle = selectedNode.color;
                        hypertextCtx.beginPath();
                        hypertextCtx.moveTo(selectedNode.x, selectedNode.y);
                        
                        // 线性插值实现动画效果
                        const targetX = nodes[i].x;
                        const targetY = nodes[i].y;
                        const currentX = selectedNode.x + (targetX - selectedNode.x) * progress;
                        const currentY = selectedNode.y + (targetY - selectedNode.y) * progress;
                        
                        hypertextCtx.lineTo(currentX, currentY);
                        hypertextCtx.stroke();
                        
                        // 如果线已经完成
                        if (progress === 1) {
                            // 绘制一个小动画效果在目标节点上
                            hypertextCtx.fillStyle = "rgba(255,255,255,0.7)";
                            hypertextCtx.beginPath();
                            hypertextCtx.arc(targetX, targetY, 30 * Math.sin(step / 10), 0, Math.PI * 2);
                            hypertextCtx.fill();
                        }
                    }
                }
                
                // 绘制所有节点
                for (const node of nodes) {
                    // 选定节点变大一点
                    const radius = (node === selectedNode) ? 28 : 25;
                    
                    hypertextCtx.fillStyle = node.color;
                    hypertextCtx.beginPath();
                    hypertextCtx.arc(node.x, node.y, radius, 0, Math.PI * 2);
                    hypertextCtx.fill();
                    
                    hypertextCtx.fillStyle = "white";
                    hypertextCtx.font = (node === selectedNode) ? "bold 12px Arial" : "12px Arial";
                    hypertextCtx.textAlign = "center";
                    hypertextCtx.fillText(node.text, node.x, node.y + 4);
                }
                
                step++;
                
                if (step <= 120) {
                    hypertextAnimationId = requestAnimationFrame(animate);
                } else {
                    drawHypertext(); // 重新启用点击功能
                }
            }
            
            animate();
        }

        function resetHypertext() {
            if (hypertextAnimationId) {
                cancelAnimationFrame(hypertextAnimationId);
                hypertextAnimationId = null;
            }
            selectedNode = null;
            drawHypertext();
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            drawKeywordTree();
            drawFacet();
            drawHypertext();
        });
    </script>
</body>
</html> 