<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>包图学习 - 软件体系架构的可视化</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.2rem;
            color: white;
            margin-bottom: 20px;
            font-weight: 300;
            letter-spacing: 3px;
        }

        .subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 300;
        }

        .main-canvas-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(15px);
            animation: slideInUp 1s ease-out 0.3s both;
        }

        .canvas-title {
            text-align: center;
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .game-canvas {
            width: 100%;
            height: 500px;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9ff 0%, #e8f0ff 100%);
            border: 3px solid #667eea;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .game-canvas:hover {
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        .controls-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .control-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .control-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .control-card.active {
            background: rgba(102, 126, 234, 0.1);
            border-color: #667eea;
        }

        .control-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .control-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .control-desc {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }

        .quiz-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(15px);
        }

        .quiz-title {
            text-align: center;
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .quiz-question {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 35px;
            padding: 25px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 10px;
            border-radius: 8px;
            font-weight: 700;
            color: #667eea;
            animation: pulse 2s infinite;
        }

        .quiz-options {
            display: grid;
            gap: 20px;
            margin-bottom: 35px;
        }

        .option {
            display: flex;
            align-items: center;
            padding: 20px 25px;
            background: white;
            border: 3px solid #e0e0e0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .option:hover::before {
            left: 100%;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
        }

        .option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            transform: scale(1.02);
        }

        .option.correct {
            border-color: #4caf50;
            background: rgba(76, 175, 80, 0.15);
            animation: correctPulse 1s ease-out;
        }

        .option.incorrect {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.15);
            animation: incorrectShake 0.8s ease-out;
        }

        .option-label {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            font-weight: bold;
            font-size: 1.1rem;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .option.correct .option-label {
            background: #4caf50;
        }

        .option.incorrect .option-label {
            background: #f44336;
        }

        .option-text {
            font-size: 1.1rem;
            color: #333;
            font-weight: 500;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            margin: 30px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            width: 0%;
            transition: width 1s ease-out;
        }

        .score-display {
            text-align: center;
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin: 25px 0;
        }

        .quiz-result {
            text-align: center;
            padding: 35px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-top: 25px;
            display: none;
            animation: slideInUp 0.5s ease-out;
        }

        .result-correct {
            color: #4caf50;
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .result-incorrect {
            color: #f44336;
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 30px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .explanation {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 35px;
            margin-top: 35px;
            border-left: 6px solid #667eea;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes incorrectShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">包图学习游戏</h1>
            <p class="subtitle">Package Diagram - 软件体系架构的可视化表示</p>
        </div>

        <div class="main-canvas-section">
            <h2 class="canvas-title">🎮 交互式包图构建游戏</h2>
            <canvas id="gameCanvas" class="game-canvas"></canvas>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="score-display" id="scoreDisplay">点击下方卡片开始学习包图！</div>
        </div>

        <div class="controls-section">
            <div class="control-card" data-action="showPackages" onclick="activateCard(this)">
                <span class="control-icon">📦</span>
                <div class="control-title">包的概念</div>
                <div class="control-desc">了解什么是包，包如何组织代码</div>
            </div>

            <div class="control-card" data-action="showDependencies" onclick="activateCard(this)">
                <span class="control-icon">🔗</span>
                <div class="control-title">依赖关系</div>
                <div class="control-desc">学习包之间的依赖关系</div>
            </div>

            <div class="control-card" data-action="showArchitecture" onclick="activateCard(this)">
                <span class="control-icon">🏗️</span>
                <div class="control-title">软件架构</div>
                <div class="control-desc">包图如何表示软件体系架构</div>
            </div>

            <div class="control-card" data-action="showComparison" onclick="activateCard(this)">
                <span class="control-icon">⚖️</span>
                <div class="control-title">对比其他图</div>
                <div class="control-desc">包图vs组件图vs类图的区别</div>
            </div>

            <div class="control-card" data-action="playGame" onclick="activateCard(this)">
                <span class="control-icon">🎯</span>
                <div class="control-title">拖拽游戏</div>
                <div class="control-desc">通过拖拽构建包图结构</div>
            </div>

            <div class="control-card" data-action="showComplete" onclick="activateCard(this)">
                <span class="control-icon">🎉</span>
                <div class="control-title">完整演示</div>
                <div class="control-desc">观看完整的包图构建过程</div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="quiz-title">📝 知识测试</h2>

            <div class="quiz-question">
                面向对象的分析模型主要由顶层架构图、用例与用例图和（ ）构成；设计模型则包含以<span class="highlight">（请作答此空）</span>表示的软件体系机构图、以交互图表示的用例实现图、完整精确的类图、描述复杂对象的（ ）和用以描述流程化处理过程的活动图等。
            </div>

            <div class="quiz-options">
                <div class="option" data-answer="A" onclick="selectOption(this)">
                    <span class="option-label">A</span>
                    <span class="option-text">模型视图控制器</span>
                </div>
                <div class="option" data-answer="B" onclick="selectOption(this)">
                    <span class="option-label">B</span>
                    <span class="option-text">组件图</span>
                </div>
                <div class="option" data-answer="C" onclick="selectOption(this)">
                    <span class="option-label">C</span>
                    <span class="option-text">包图</span>
                </div>
                <div class="option" data-answer="D" onclick="selectOption(this)">
                    <span class="option-label">D</span>
                    <span class="option-text">2层、3层或N层</span>
                </div>
            </div>

            <div class="quiz-result" id="quizResult">
                <div class="result-content" id="resultContent"></div>
                <button class="btn" onclick="resetQuiz()">重新答题</button>
                <button class="btn" onclick="showDetailedExplanation()">详细解析</button>
            </div>
        </div>

        <div class="explanation" id="explanation">
            <h3>💡 学习提示</h3>
            <p>点击上方的学习卡片，通过动画和游戏了解包图的概念和作用！包图是设计模型中表示软件体系架构的重要工具。</p>
        </div>
    </div>

    <script>
        // Canvas 设置
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;

        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // 游戏状态
        let gameState = {
            time: 0,
            currentAction: null,
            packages: [],
            dependencies: [],
            particles: [],
            score: 0,
            progress: 0,
            draggedPackage: null,
            mouseX: 0,
            mouseY: 0
        };

        // 包对象类
        class Package {
            constructor(x, y, name, color, size = 80) {
                this.x = x;
                this.y = y;
                this.name = name;
                this.color = color;
                this.size = size;
                this.targetX = x;
                this.targetY = y;
                this.isDragging = false;
                this.alpha = 0;
                this.scale = 0;
            }

            update() {
                // 平滑移动到目标位置
                this.x += (this.targetX - this.x) * 0.1;
                this.y += (this.targetY - this.y) * 0.1;

                // 渐入动画
                if (this.alpha < 1) this.alpha += 0.02;
                if (this.scale < 1) this.scale += 0.03;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.alpha;
                ctx.translate(this.x, this.y);
                ctx.scale(this.scale, this.scale);

                // 包的主体
                ctx.fillStyle = this.color;
                ctx.fillRect(-this.size/2, -this.size/2, this.size, this.size);

                // 包的标签部分
                ctx.fillStyle = this.isDragging ? '#fff' : 'rgba(255,255,255,0.9)';
                ctx.fillRect(-this.size/2, -this.size/2, this.size, this.size/4);

                // 边框
                ctx.strokeStyle = this.isDragging ? '#667eea' : 'rgba(255,255,255,0.8)';
                ctx.lineWidth = this.isDragging ? 3 : 2;
                ctx.strokeRect(-this.size/2, -this.size/2, this.size, this.size);

                // 包名
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(this.name, 0, 5);

                ctx.restore();
            }

            contains(x, y) {
                return x >= this.x - this.size/2 && x <= this.x + this.size/2 &&
                       y >= this.y - this.size/2 && y <= this.y + this.size/2;
            }
        }

        // 依赖关系类
        class Dependency {
            constructor(from, to, type = 'uses') {
                this.from = from;
                this.to = to;
                this.type = type;
                this.alpha = 0;
                this.animationOffset = 0;
            }

            update() {
                if (this.alpha < 1) this.alpha += 0.02;
                this.animationOffset += 0.05;
            }

            draw() {
                if (!this.from || !this.to) return;

                ctx.save();
                ctx.globalAlpha = this.alpha;

                // 计算连接点
                const dx = this.to.x - this.from.x;
                const dy = this.to.y - this.from.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                const unitX = dx / distance;
                const unitY = dy / distance;

                const startX = this.from.x + unitX * this.from.size/2;
                const startY = this.from.y + unitY * this.from.size/2;
                const endX = this.to.x - unitX * this.to.size/2;
                const endY = this.to.y - unitY * this.to.size/2;

                // 绘制依赖线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.setLineDash([10, 5]);
                ctx.lineDashOffset = this.animationOffset * 10;

                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();

                // 绘制箭头
                const arrowSize = 10;
                const arrowX = endX - unitX * arrowSize;
                const arrowY = endY - unitY * arrowSize;

                ctx.setLineDash([]);
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.moveTo(endX, endY);
                ctx.lineTo(arrowX - unitY * arrowSize/2, arrowY + unitX * arrowSize/2);
                ctx.lineTo(arrowX + unitY * arrowSize/2, arrowY - unitX * arrowSize/2);
                ctx.closePath();
                ctx.fill();

                ctx.restore();
            }
        }

        // 粒子系统
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = (Math.random() - 0.5) * 4;
                this.color = color;
                this.life = 1;
                this.decay = 0.02;
                this.size = Math.random() * 4 + 2;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.vx *= 0.99;
                this.vy *= 0.99;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 主动画循环
        function animate() {
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            // 清空画布
            ctx.clearRect(0, 0, width, height);

            // 绘制背景网格
            drawGrid(width, height);

            // 更新和绘制包
            gameState.packages.forEach(pkg => {
                pkg.update();
                pkg.draw();
            });

            // 更新和绘制依赖关系
            gameState.dependencies.forEach(dep => {
                dep.update();
                dep.draw();
            });

            // 更新和绘制粒子
            gameState.particles = gameState.particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });

            // 绘制拖拽中的包
            if (gameState.draggedPackage) {
                gameState.draggedPackage.x = gameState.mouseX;
                gameState.draggedPackage.y = gameState.mouseY;
                gameState.draggedPackage.draw();
            }

            gameState.time += 0.02;
            animationId = requestAnimationFrame(animate);
        }

        function drawGrid(width, height) {
            ctx.save();
            ctx.strokeStyle = 'rgba(102, 126, 234, 0.1)';
            ctx.lineWidth = 1;

            const gridSize = 30;
            for (let x = 0; x < width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, height);
                ctx.stroke();
            }

            for (let y = 0; y < height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(width, y);
                ctx.stroke();
            }
            ctx.restore();
        }

        // 控制卡片功能
        function activateCard(card) {
            // 清除其他卡片的active状态
            document.querySelectorAll('.control-card').forEach(c => c.classList.remove('active'));
            card.classList.add('active');

            const action = card.dataset.action;
            gameState.currentAction = action;

            // 清空当前状态
            gameState.packages = [];
            gameState.dependencies = [];
            gameState.particles = [];

            // 根据动作执行相应功能
            switch(action) {
                case 'showPackages':
                    showPackageConcept();
                    break;
                case 'showDependencies':
                    showDependencies();
                    break;
                case 'showArchitecture':
                    showArchitecture();
                    break;
                case 'showComparison':
                    showComparison();
                    break;
                case 'playGame':
                    startDragGame();
                    break;
                case 'showComplete':
                    showCompleteDemo();
                    break;
            }

            updateProgress();
        }

        function showPackageConcept() {
            updateExplanation('packages');

            // 创建示例包
            const packages = [
                new Package(150, 200, 'UI层', '#ff6b6b', 100),
                new Package(350, 200, '业务层', '#4ecdc4', 100),
                new Package(550, 200, '数据层', '#45b7d1', 100)
            ];

            packages.forEach((pkg, index) => {
                setTimeout(() => {
                    gameState.packages.push(pkg);
                    addParticles(pkg.x, pkg.y, pkg.color);
                    updateScore(100);
                }, index * 800);
            });
        }

        function showDependencies() {
            updateExplanation('dependencies');

            // 先创建包
            const uiPkg = new Package(150, 150, 'UI包', '#ff6b6b');
            const businessPkg = new Package(350, 150, '业务包', '#4ecdc4');
            const dataPkg = new Package(550, 150, '数据包', '#45b7d1');

            gameState.packages = [uiPkg, businessPkg, dataPkg];

            // 延迟添加依赖关系
            setTimeout(() => {
                gameState.dependencies.push(new Dependency(uiPkg, businessPkg));
                addParticles(250, 150, '#667eea');
            }, 1000);

            setTimeout(() => {
                gameState.dependencies.push(new Dependency(businessPkg, dataPkg));
                addParticles(450, 150, '#667eea');
            }, 2000);
        }

        function showArchitecture() {
            updateExplanation('architecture');

            // 创建分层架构
            const layers = [
                { name: '表示层', y: 100, color: '#ff6b6b' },
                { name: '业务层', y: 200, color: '#4ecdc4' },
                { name: '持久层', y: 300, color: '#45b7d1' },
                { name: '数据库层', y: 400, color: '#9b59b6' }
            ];

            layers.forEach((layer, index) => {
                setTimeout(() => {
                    const pkg = new Package(350, layer.y, layer.name, layer.color, 120);
                    gameState.packages.push(pkg);

                    if (index > 0) {
                        const prevPkg = gameState.packages[index - 1];
                        gameState.dependencies.push(new Dependency(prevPkg, pkg));
                    }

                    addParticles(350, layer.y, layer.color);
                    updateScore(150);
                }, index * 600);
            });
        }

        function showComparison() {
            updateExplanation('comparison');

            // 显示不同图形的对比
            const diagrams = [
                { name: '包图', x: 200, y: 150, color: '#667eea', desc: '架构组织' },
                { name: '组件图', x: 400, y: 150, color: '#e74c3c', desc: '组件关系' },
                { name: '类图', x: 300, y: 300, color: '#f39c12', desc: '类结构' }
            ];

            diagrams.forEach((diagram, index) => {
                setTimeout(() => {
                    const pkg = new Package(diagram.x, diagram.y, diagram.name, diagram.color, 90);
                    gameState.packages.push(pkg);
                    addParticles(diagram.x, diagram.y, diagram.color);
                }, index * 500);
            });
        }

        function startDragGame() {
            updateExplanation('game');
            updateScore(0, true); // 重置分数

            // 创建可拖拽的包
            const gamePackages = [
                new Package(100, 400, '控制器', '#ff6b6b', 70),
                new Package(200, 400, '服务', '#4ecdc4', 70),
                new Package(300, 400, '模型', '#45b7d1', 70),
                new Package(400, 400, '视图', '#9b59b6', 70)
            ];

            gameState.packages = gamePackages;

            // 设置目标位置（正确的架构位置）
            gameState.targetPositions = [
                { x: 350, y: 100 }, // 控制器
                { x: 250, y: 200 }, // 服务
                { x: 450, y: 200 }, // 模型
                { x: 350, y: 300 }  // 视图
            ];
        }

        function showCompleteDemo() {
            updateExplanation('complete');

            // 完整的包图演示
            setTimeout(() => showPackageConcept(), 500);
            setTimeout(() => showDependencies(), 3000);
            setTimeout(() => showArchitecture(), 6000);
            setTimeout(() => {
                updateScore(500);
                addCelebrationParticles();
            }, 9000);
        }

        // 工具函数
        function addParticles(x, y, color) {
            for (let i = 0; i < 12; i++) {
                gameState.particles.push(new Particle(x, y, color));
            }
        }

        function addCelebrationParticles() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#9b59b6', '#f39c12'];
            for (let i = 0; i < 50; i++) {
                const x = Math.random() * (canvas.width / window.devicePixelRatio);
                const y = Math.random() * (canvas.height / window.devicePixelRatio);
                const color = colors[Math.floor(Math.random() * colors.length)];
                gameState.particles.push(new Particle(x, y, color));
            }
        }

        function updateScore(points, reset = false) {
            if (reset) {
                gameState.score = 0;
            } else {
                gameState.score += points;
            }

            document.getElementById('scoreDisplay').textContent = `学习积分: ${gameState.score}分 🎯`;
        }

        function updateProgress() {
            gameState.progress = Math.min(gameState.progress + 16.67, 100);
            document.getElementById('progressFill').style.width = gameState.progress + '%';
        }

        // 鼠标事件处理
        canvas.addEventListener('mousedown', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检查是否点击了包
            for (let pkg of gameState.packages) {
                if (pkg.contains(x, y)) {
                    gameState.draggedPackage = pkg;
                    pkg.isDragging = true;
                    addParticles(x, y, pkg.color);
                    break;
                }
            }
        });

        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            gameState.mouseX = e.clientX - rect.left;
            gameState.mouseY = e.clientY - rect.top;
        });

        canvas.addEventListener('mouseup', (e) => {
            if (gameState.draggedPackage) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                gameState.draggedPackage.targetX = x;
                gameState.draggedPackage.targetY = y;
                gameState.draggedPackage.isDragging = false;

                // 检查是否放置在正确位置（如果是游戏模式）
                if (gameState.currentAction === 'playGame' && gameState.targetPositions) {
                    checkGameCompletion();
                }

                gameState.draggedPackage = null;
                addParticles(x, y, '#4caf50');
                updateScore(50);
            }
        });

        function checkGameCompletion() {
            let correctPlacements = 0;
            const tolerance = 50;

            gameState.packages.forEach((pkg, index) => {
                if (gameState.targetPositions[index]) {
                    const target = gameState.targetPositions[index];
                    const distance = Math.sqrt(
                        Math.pow(pkg.x - target.x, 2) + Math.pow(pkg.y - target.y, 2)
                    );

                    if (distance < tolerance) {
                        correctPlacements++;
                    }
                }
            });

            if (correctPlacements === gameState.packages.length) {
                setTimeout(() => {
                    addCelebrationParticles();
                    updateScore(1000);
                    updateExplanation('gameComplete');
                }, 500);
            }
        }

        // 选择题功能
        let quizAnswered = false;
        let selectedAnswer = null;

        function selectOption(option) {
            if (quizAnswered) return;

            // 清除其他选择
            document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
            option.classList.add('selected');
            selectedAnswer = option.dataset.answer;

            setTimeout(() => {
                checkQuizAnswer();
            }, 800);
        }

        function checkQuizAnswer() {
            if (quizAnswered) return;

            quizAnswered = true;
            const correctAnswer = 'C';
            const options = document.querySelectorAll('.option');
            const resultDiv = document.getElementById('quizResult');
            const resultContent = document.getElementById('resultContent');

            options.forEach(option => {
                if (option.dataset.answer === correctAnswer) {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== correctAnswer) {
                    option.classList.add('incorrect');
                }
            });

            if (selectedAnswer === correctAnswer) {
                resultContent.innerHTML = `
                    <div class="result-correct">
                        🎉 恭喜答对了！
                    </div>
                    <p>正确答案是 <strong>C. 包图</strong></p>
                    <p>您已经掌握了包图在设计模型中的重要作用！</p>
                `;
                addCelebrationParticles();
                updateScore(300);
            } else {
                resultContent.innerHTML = `
                    <div class="result-incorrect">
                        ❌ 答案不正确
                    </div>
                    <p>正确答案是 <strong>C. 包图</strong></p>
                    <p>让我们重新学习包图的概念和作用吧！</p>
                `;
            }

            resultDiv.style.display = 'block';
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function resetQuiz() {
            quizAnswered = false;
            selectedAnswer = null;

            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.classList.remove('selected', 'correct', 'incorrect');
            });

            document.getElementById('quizResult').style.display = 'none';
        }

        function showDetailedExplanation() {
            updateExplanation('detailed');
            document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
        }

        // 解析更新函数
        function updateExplanation(type) {
            const explanations = {
                packages: {
                    title: '📦 包的概念',
                    content: '包（Package）是面向对象设计中用来组织和管理代码的逻辑容器。它将相关的类、接口和其他元素组织在一起，形成一个有意义的功能模块。包图通过可视化的方式展示这些包之间的组织结构。'
                },
                dependencies: {
                    title: '🔗 依赖关系',
                    content: '包之间的依赖关系表示一个包需要使用另一个包中的元素。依赖关系用虚线箭头表示，箭头指向被依赖的包。合理的依赖关系设计是良好软件架构的基础。'
                },
                architecture: {
                    title: '🏗️ 软件体系架构',
                    content: '包图是表示软件体系架构的重要工具。它展示了系统的分层结构、模块划分和组件关系，帮助开发者理解系统的整体结构和设计思路。'
                },
                comparison: {
                    title: '⚖️ 包图 vs 其他图',
                    content: '包图关注架构组织，组件图关注组件接口，类图关注类的详细结构。包图是更高层次的抽象，用于表示软件的宏观架构。'
                },
                game: {
                    title: '🎯 拖拽游戏',
                    content: '通过拖拽包到正确位置，学习典型的软件架构模式。尝试将控制器、服务、模型、视图放置到合适的架构层次中！'
                },
                gameComplete: {
                    title: '🎉 游戏完成！',
                    content: '恭喜！您成功构建了一个标准的MVC架构。这种分层设计是现代软件开发的重要模式，包图完美地展示了这种架构关系。'
                },
                complete: {
                    title: '🔄 完整演示',
                    content: '观看包图的完整构建过程：从基本概念到依赖关系，再到完整的软件架构。包图是设计模型中不可或缺的重要组成部分。'
                },
                detailed: {
                    title: '📚 详细解析',
                    content: `
                        <div style="text-align: left;">
                            <h4>🎯 题目解析</h4>
                            <p><strong>正确答案：C. 包图</strong></p>

                            <h4>📖 核心知识点</h4>

                            <div style="margin: 20px 0; padding: 15px; background: rgba(102, 126, 234, 0.1); border-radius: 8px;">
                                <strong>🎨 设计模型的五大组成</strong><br>
                                • <strong>包图</strong>：表示软件体系架构<br>
                                • <strong>交互图</strong>：表示用例实现<br>
                                • <strong>类图</strong>：完整精确的类定义<br>
                                • <strong>状态图</strong>：描述复杂对象<br>
                                • <strong>活动图</strong>：描述流程化处理
                            </div>

                            <div style="margin: 20px 0; padding: 15px; background: rgba(255, 107, 107, 0.1); border-radius: 8px;">
                                <strong>📦 包图的特点</strong><br>
                                • 高层次的架构视图<br>
                                • 展示模块间的依赖关系<br>
                                • 组织和管理系统复杂性<br>
                                • 支持分层架构设计
                            </div>

                            <h4>🔍 为什么选择"包图"？</h4>
                            <p>题目明确提到"以（ ）表示的软件体系机构图"，这正是<strong>包图</strong>的核心作用。包图专门用于表示软件的体系架构，展示系统的模块划分和组织结构。</p>

                            <h4>🚫 其他选项分析</h4>
                            <p>• <strong>模型视图控制器</strong>：这是一种架构模式，不是UML图形<br>
                            • <strong>组件图</strong>：关注组件接口，不是专门表示体系架构<br>
                            • <strong>2层、3层或N层</strong>：这是架构风格，不是UML图形</p>

                            <h4>💡 记忆技巧</h4>
                            <p>记住：<strong>包图 = 软件体系架构图</strong>。包图就像建筑的结构图，展示整个软件系统的"骨架"和"布局"。</p>
                        </div>
                    `
                }
            };

            const explanation = explanations[type];
            if (explanation) {
                document.getElementById('explanation').innerHTML = `
                    <h3>${explanation.title}</h3>
                    <div>${explanation.content}</div>
                `;
            }
        }

        // 创建浮动元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            const symbols = ['📦', '🔗', '🏗️', '⚖️', '🎯', '🎨'];

            for (let i = 0; i < 6; i++) {
                const element = document.createElement('div');
                element.className = 'floating-element';
                element.textContent = symbols[i];
                element.style.left = Math.random() * 100 + '%';
                element.style.top = Math.random() * 100 + '%';
                element.style.animationDelay = Math.random() * 6 + 's';
                element.style.fontSize = (Math.random() * 20 + 30) + 'px';
                container.appendChild(element);
            }
        }

        // 初始化
        function init() {
            createFloatingElements();
            animate();
            updateExplanation('packages');

            // 默认显示包的概念
            setTimeout(() => {
                document.querySelector('[data-action="showPackages"]').click();
            }, 1000);
        }

        // 启动应用
        init();
    </script>
</body>
</html>
