<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库约束知识详解 - 外键的奥秘</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }

        header {
            background-color: #28a745;
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        h1, h2, h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        main {
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        section {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        section:last-child {
            border-bottom: none;
        }

        p, ul {
            margin-bottom: 10px;
        }

        ul {
            list-style-type: none;
            padding: 0;
        }

        ul li {
            background-color: #e9ecef;
            padding: 8px 15px;
            margin-bottom: 5px;
            border-radius: 4px;
        }

        ul li span {
            font-weight: bold;
        }

        .concept-item {
            background-color: #e8f5e9;
            border-left: 5px solid #4CAF50;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            transition: background-color 0.3s ease;
        }

        button:hover {
            background-color: #0056b3;
        }

        #dbCanvas {
            border: 1px solid #ccc;
            display: block;
            margin: 20px auto;
            background-color: #fcfcfc;
            border-radius: 5px;
        }

        .controls {
            text-align: center;
            margin-top: 10px;
        }

        #message-box {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            border: 1px solid #ccc;
            background-color: #fff3cd;
            color: #856404;
            border-radius: 5px;
            display: none; /* Hidden by default */
        }
    </style>
</head>
<body>
    <header>
        <h1>数据库约束知识详解 - 外键的奥秘</h1>
    </header>
    <main>
        <section id="introduction">
            <h2>欢迎来到数据库世界！</h2>
            <p>我们今天要学习的是数据库中非常重要的一个概念：<strong>外键</strong>。通过一个实际的例子，我们会一起探索外键的作用以及它如何保证数据的准确性。</p>
            <p><strong>题目背景：</strong></p>
            <p>给定员工关系EMP (EmpID, Ename, sex, age, tel, DepID) ，其属性含义分别为：员工号、姓名、性别、年龄、电话、部门号；部门关系DEP (DepID, Dname, Dtel, DEmpID) ，其属性含义分别为：部门号、部门名、电话、负责人号。若要求DepID参照部门关系DEP的主码DepID，则可以在定义EMP时用 () 进行约束。</p>
            <p><strong>问题：</strong></p>
            <p>下列哪个选项正确地描述了在定义EMP表时，DepID引用DEP表的主键DepID的约束？</p>
            <ul>
                <li>A. Primary Key (DepID) On DEP (DepID)</li>
                <li>B. Primary Key (DepID) On EMP (DepID)</li>
                <li><li><span style="color: green;">C. Foreign Key (DepID) References DEP (DepID)</span></li></li>
                <li>D. Foreign Key (DepID) References EMP (DepID)</li>
            </ul>
        </section>

        <section id="concept-explanation">
            <h2>核心概念解析</h2>
            <div class="concept-item">
                <h3>什么是主键 (Primary Key)？</h3>
                <p>主键是数据库表中一列或一组列，它的值能<strong>唯一标识</strong>表中的每一行。主键列的值不能重复，也不能为NULL（空）。每个表最多只能有一个主键。</p>
                <button onclick="animatePrimaryKey()">演示主键</button>
            </div>
            <div class="concept-item">
                <h3>什么是外键 (Foreign Key)？</h3>
                <p>外键是数据库表中一列或一组列，它的值<strong>参照（引用）</strong>另一个表（通常是主键表）的主键。外键的作用是建立两个表之间的关联，并维护参照完整性。</p>
                <button onclick="animateForeignKey()">演示外键</button>
            </div>
            <div class="concept-item">
                <h3>什么是参照完整性 (Referential Integrity)？</h3>
                <p>参照完整性是数据库的一种规则，它确保外键列中的值必须在它所参照的主键列中存在。这意味着，你不能在子表中插入一个不存在于父表中的部门ID，也不能删除或更新父表中已被子表参照的部门ID（除非你定义了级联操作）。</p>
                <button onclick="animateReferentialIntegrity()">演示参照完整性</button>
            </div>
        </section>

        <section id="interactive-demo">
            <h2>交互演示区</h2>
            <canvas id="dbCanvas" width="800" height="400"></canvas>
            <div class="controls">
                <button onclick="resetCanvas()">重置演示</button>
                <button onclick="showTables()">显示表结构</button>
                <!-- 更多具体的演示按钮将在JS中添加，以保持代码清晰 -->
            </div>
            <p id="message-box" style="color: red; font-weight: bold;"></p>
        </section>

        <section id="solution-explanation">
            <h2>答案解析</h2>
            <p>根据上述概念，我们来分析题目：</p>
            <ul>
                <li>员工表EMP中有一个列<code>DepID</code>（部门号）。</li>
                <li>部门表DEP中也有一个列<code>DepID</code>，并且这个<code>DepID</code>是部门表的主键。</li>
                <li>题目要求EMP表的<code>DepID</code>参照DEP表的<code>DepID</code>（主键）。</li>
            </ul>
            <p>这正是<strong>外键</strong>的定义！EMP表的<code>DepID</code>是外键，它参照了DEP表的主键<code>DepID</code>。</p>
            <p>SQL语言中，定义外键的语法通常是：<code>FOREIGN KEY (外键列名) REFERENCES 参照表名 (参照主键列名)</code>。</p>
            <p>因此，正确的约束表达式是：<code>Foreign Key (DepID) References DEP (DepID)</code>。</p>
            <p>选择C是正确的，因为它符合外键的定义和语法。</p>
        </section>
    </main>

    <script>
        const canvas = document.getElementById('dbCanvas');
        const ctx = canvas.getContext('2d');
        const messageBox = document.getElementById('message-box');

        let empTable = {
            name: 'EMP (员工)',
            x: 50,
            y: 50,
            columns: [
                { name: 'EmpID', type: 'VARCHAR(10)', data: ['E001', 'E002', 'E003', 'E004'] },
                { name: 'Ename', type: 'VARCHAR(50)', data: ['张三', '李四', '王五', '赵六'] },
                { name: 'DepID', type: 'VARCHAR(10)', data: ['D101', 'D102', 'D101', 'D103'] } // Foreign Key
            ]
        };

        let depTable = {
            name: 'DEP (部门)',
            x: 450,
            y: 50,
            columns: [
                { name: 'DepID', type: 'VARCHAR(10)', data: ['D101', 'D102', 'D103'], isPK: true }, // Primary Key
                { name: 'Dname', type: 'VARCHAR(50)', data: ['研发部', '市场部', '财务部'] },
                { name: 'DEmpID', type: 'VARCHAR(10)', data: ['E001', 'E002', 'E003'] }
            ]
        };

        const COLUMN_HEIGHT = 30;
        const ROW_HEIGHT = 25;
        const CELL_PADDING = 5;

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawTable(table) {
            let currentY = table.y;
            let maxWidth = 0;

            // Calculate max width for columns
            ctx.font = '14px Arial';
            table.columns.forEach(col => {
                let colWidth = ctx.measureText(col.name + ' (' + col.type + ')').width;
                col.data.forEach(d => {
                    colWidth = Math.max(colWidth, ctx.measureText(d).width);
                });
                maxWidth = Math.max(maxWidth, colWidth);
            });
            maxWidth += CELL_PADDING * 2; // Add padding

            // Draw table name
            ctx.fillStyle = '#2c3e50';
            ctx.font = '16px Arial';
            ctx.fillText(table.name, table.x, currentY);
            currentY += 20; // Space for table name

            // Draw header
            ctx.fillStyle = '#34495e';
            ctx.fillRect(table.x, currentY, maxWidth * table.columns.length, COLUMN_HEIGHT);
            ctx.strokeStyle = '#ccc';
            ctx.strokeRect(table.x, currentY, maxWidth * table.columns.length, COLUMN_HEIGHT);

            let currentX = table.x;
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            table.columns.forEach(col => {
                ctx.fillText(col.name + (col.isPK ? ' (PK)' : ''), currentX + CELL_PADDING, currentY + COLUMN_HEIGHT / 2 + 5);
                currentX += maxWidth;
            });
            currentY += COLUMN_HEIGHT;

            // Draw rows
            ctx.fillStyle = '#eee';
            const numRows = Math.max(...table.columns.map(c => c.data.length));

            for (let i = 0; i < numRows; i++) {
                currentX = table.x;
                ctx.fillStyle = (i % 2 === 0) ? '#f9f9f9' : '#e9e9e9';
                ctx.fillRect(currentX, currentY, maxWidth * table.columns.length, ROW_HEIGHT);
                ctx.strokeStyle = '#ccc';
                ctx.strokeRect(currentX, currentY, maxWidth * table.columns.length, ROW_HEIGHT);

                ctx.fillStyle = '#333';
                table.columns.forEach(col => {
                    const data = col.data[i] || '';
                    ctx.fillText(data, currentX + CELL_PADDING, currentY + ROW_HEIGHT / 2 + 5);
                    currentX += maxWidth;
                });
                currentY += ROW_HEIGHT;
            }
            table.width = maxWidth * table.columns.length;
            table.height = currentY - table.y;
        }

        function showTables() {
            clearCanvas();
            drawTable(empTable);
            drawTable(depTable);
            showMessage('数据库表结构已显示。');
        }

        function animatePrimaryKey() {
            showTables();
            const pkColumnIndex = depTable.columns.findIndex(col => col.isPK);
            if (pkColumnIndex !== -1) {
                const pkColumnX = depTable.x + (depTable.width / depTable.columns.length) * pkColumnIndex;
                const pkColumnWidth = depTable.width / depTable.columns.length;
                const pkColumnY = depTable.y + 20; // Below table name

                ctx.save();
                ctx.strokeStyle = 'red';
                ctx.lineWidth = 3;
                ctx.globalAlpha = 0.8;
                ctx.strokeRect(pkColumnX, pkColumnY, pkColumnWidth, COLUMN_HEIGHT + depTable.columns[pkColumnIndex].data.length * ROW_HEIGHT);
                ctx.restore();
                showMessage('DEP表的DepID列被高亮，它作为主键唯一标识每个部门。');
            }
        }

        function animateForeignKey() {
            showTables();
            const empDepIDColIndex = empTable.columns.findIndex(col => col.name === 'DepID');
            const depDepIDColIndex = depTable.columns.findIndex(col => col.name === 'DepID');

            if (empDepIDColIndex !== -1 && depDepIDColIndex !== -1) {
                const empColWidth = empTable.width / empTable.columns.length;
                const depColWidth = depTable.width / depTable.columns.length;

                const empDepID_X = empTable.x + empColWidth * empDepIDColIndex + empColWidth / 2;
                const empDepID_Y = empTable.y + 20 + COLUMN_HEIGHT + (empTable.columns[empDepIDColIndex].data.length * ROW_HEIGHT) / 2;

                const depDepID_X = depTable.x + depColWidth * depDepIDColIndex + depColWidth / 2;
                const depDepID_Y = depTable.y + 20 + COLUMN_HEIGHT + (depTable.columns[depDepIDColIndex].data.length * ROW_HEIGHT) / 2;

                ctx.save();
                ctx.strokeStyle = 'blue';
                ctx.lineWidth = 2;
                ctx.globalAlpha = 0.7;
                ctx.beginPath();
                ctx.moveTo(empDepID_X, empDepID_Y);
                ctx.lineTo(depDepID_X, depDepID_Y);
                ctx.stroke();

                // Draw arrow head
                drawArrowHead(ctx, empDepID_X, empDepID_Y, depDepID_X, depDepID_Y, 'blue');
                ctx.restore();
                showMessage('EMP表的DepID列被高亮，它是一个外键，指向DEP表的主键DepID。');
            }
        }

        function drawArrowHead(ctx, fromX, fromY, toX, toY, color) {
            const headlen = 10; // length of head in pixels
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.save();
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
            ctx.restore();
        }

        function animateReferentialIntegrity() {
            showTables();
            const empDepIDColIndex = empTable.columns.findIndex(col => col.name === 'DepID');
            const depDepIDColIndex = depTable.columns.findIndex(col => col.name === 'DepID');

            if (empDepIDColIndex === -1 || depDepIDColIndex === -1) {
                showMessage('无法找到DepID列进行参照完整性演示。');
                return;
            }

            const empDepID_ColumnX = empTable.x + (empTable.width / empTable.columns.length) * empDepIDColIndex;
            const empDepID_ColumnWidth = empTable.width / empTable.columns.length;

            const depPK_Values = depTable.columns[depDepIDColIndex].data;

            // Scenario 1: Try to insert an invalid DepID into EMP
            showMessage('尝试在EMP表中插入一个不存在的部门ID (例如: D999)...');
            setTimeout(() => {
                const invalidDepID_Value = 'D999';
                let empRowY = empTable.y + 20 + COLUMN_HEIGHT + empTable.columns[0].data.length * ROW_HEIGHT;
                let empRowX = empTable.x;

                ctx.fillStyle = '#ffcccc'; // Red highlight for invalid row
                ctx.fillRect(empRowX, empRowY, empTable.width, ROW_HEIGHT);
                ctx.strokeStyle = '#cc0000';
                ctx.strokeRect(empRowX, empRowY, empTable.width, ROW_HEIGHT);

                ctx.fillStyle = '#333';
                ctx.fillText('E005', empRowX + CELL_PADDING, empRowY + ROW_HEIGHT / 2 + 5);
                ctx.fillText('新员工', empRowX + empDepID_ColumnWidth + CELL_PADDING, empRowY + ROW_HEIGHT / 2 + 5);
                ctx.fillText(invalidDepID_Value, empDepID_ColumnX + CELL_PADDING, empRowY + ROW_HEIGHT / 2 + 5);

                showMessage('"D999"不存在于DEP表中！违反参照完整性，该操作将被拒绝。', 'red');
            }, 1500);

            // Scenario 2: Successfully insert a valid DepID
            setTimeout(() => {
                showTables(); // Clear previous animation
                showMessage('尝试在EMP表中插入一个存在的部门ID (例如: D102)...');
                const validDepID_Value = 'D102';
                let empRowY = empTable.y + 20 + COLUMN_HEIGHT + empTable.columns[0].data.length * ROW_HEIGHT;
                let empRowX = empTable.x;

                ctx.fillStyle = '#ccffcc'; // Green highlight for valid row
                ctx.fillRect(empRowX, empRowY, empTable.width, ROW_HEIGHT);
                ctx.strokeStyle = '#00cc00';
                ctx.strokeRect(empRowX, empRowY, empTable.width, ROW_HEIGHT);

                ctx.fillStyle = '#333';
                ctx.fillText('E005', empRowX + CELL_PADDING, empRowY + ROW_HEIGHT / 2 + 5);
                ctx.fillText('新员工', empRowX + empDepID_ColumnWidth + CELL_PADDING, empRowY + ROW_HEIGHT / 2 + 5);
                ctx.fillText(validDepID_Value, empDepID_ColumnX + CELL_PADDING, empRowY + ROW_HEIGHT / 2 + 5);

                showMessage('"D102"存在于DEP表中！操作成功。', 'green');
            }, 4000);

            // Scenario 3: Try to delete a referenced DepID from DEP
            setTimeout(() => {
                showTables(); // Clear previous animation
                showMessage('尝试删除DEP表中已被EMP表引用的部门ID (例如: D101)...');
                const referencedDepID_Value = 'D101';
                const depRowIndex = depTable.columns[depDepIDColIndex].data.indexOf(referencedDepID_Value);
                if (depRowIndex !== -1) {
                    const depRowY = depTable.y + 20 + COLUMN_HEIGHT + depRowIndex * ROW_HEIGHT;
                    const depRowX = depTable.x;

                    ctx.fillStyle = '#ffcccc'; // Red highlight for invalid delete
                    ctx.fillRect(depRowX, depRowY, depTable.width, ROW_HEIGHT);
                    ctx.strokeStyle = '#cc0000';
                    ctx.strokeRect(depRowX, depRowY, depTable.width, ROW_HEIGHT);
                    showMessage('DEP表中的"D101"已被EMP表引用！无法直接删除，违反参照完整性。', 'red');
                }
            }, 7000);

            setTimeout(() => {
                resetCanvas();
                showMessage('参照完整性演示完毕。');
            }, 10000);
        }


        function showMessage(msg, color = 'black') {
            messageBox.style.display = 'block';
            messageBox.style.color = color;
            messageBox.innerHTML = msg;
            setTimeout(() => {
                messageBox.style.display = 'none';
            }, 2500); // Hide message after 2.5 seconds
        }

        function resetCanvas() {
            clearCanvas();
            showMessage('画布已重置。');
        }

        // Initial setup when page loads
        document.addEventListener('DOMContentLoaded', showTables);
    </script>
</body>
</html> 