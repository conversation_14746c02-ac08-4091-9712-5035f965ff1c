<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简易笔记替换工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .panel {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            flex: 1;
        }
        h1, h2 {
            color: #0056b3;
        }
        textarea {
            width: 100%;
            height: 120px;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box; /* 保持内边距和边框在宽度内 */
        }
        input[type="text"] {
            width: calc(100% - 22px); /* 减去padding和border */
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .note-item, .rule-item {
            background-color: #e9ecef;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            word-break: break-all; /* 防止内容溢出 */
        }
        .note-content, .rule-content {
            flex-grow: 1;
            margin-right: 10px;
        }
        .note-item button, .rule-item button {
            background-color: #dc3545;
            padding: 5px 10px;
            font-size: 14px;
        }
        .note-item button:hover, .rule-item button:hover {
            background-color: #c82333;
        }
        .rule-input-group {
            display: flex;
            margin-bottom: 10px;
            gap: 5px;
        }
        .rule-input-group input {
            flex: 1;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <h1>简易笔记替换工具</h1>
    <div class="container">
        <div class="panel">
            <h2>笔记管理</h2>
            <textarea id="noteInput" placeholder="输入您的笔记内容..."></textarea>
            <button onclick="addNote()">添加笔记</button>
            <div id="notesList"></div>
        </div>

        <div class="panel">
            <h2>替换规则</h2>
            <div class="rule-input-group">
                <input type="text" id="findInput" placeholder="要查找的文本...">
                <input type="text" id="replaceInput" placeholder="替换为...">
            </div>
            <button onclick="addRule()">添加规则</button>
            <div id="rulesList"></div>
            <hr style="margin: 20px 0;">
            <button onclick="applyRules()">应用所有规则到笔记</button>
        </div>
    </div>

    <script>
        let notes = [];
        let rules = [];

        // 加载数据 (简单地使用localStorage)
        function loadData() {
            const storedNotes = localStorage.getItem('notes');
            const storedRules = localStorage.getItem('rules');
            if (storedNotes) {
                notes = JSON.parse(storedNotes);
                renderNotes();
            }
            if (storedRules) {
                rules = JSON.parse(storedRules);
                renderRules();
            }
        }

        // 保存数据
        function saveData() {
            localStorage.setItem('notes', JSON.stringify(notes));
            localStorage.setItem('rules', JSON.stringify(rules));
        }

        // 渲染笔记列表
        function renderNotes() {
            const notesList = document.getElementById('notesList');
            notesList.innerHTML = '';
            notes.forEach((note, index) => {
                const noteItem = document.createElement('div');
                noteItem.className = 'note-item';
                noteItem.innerHTML = `
                    <div class="note-content">${note}</div>
                    <button onclick="deleteNote(${index})">删除</button>
                `;
                notesList.appendChild(noteItem);
            });
        }

        // 渲染规则列表
        function renderRules() {
            const rulesList = document.getElementById('rulesList');
            rulesList.innerHTML = '';
            rules.forEach((rule, index) => {
                const ruleItem = document.createElement('div');
                ruleItem.className = 'rule-item';
                ruleItem.innerHTML = `
                    <div class="rule-content">"${rule.find}" 替换为 "${rule.replace}"</div>
                    <button onclick="deleteRule(${index})">删除</button>
                `;
                rulesList.appendChild(ruleItem);
            });
        }

        // 添加笔记
        function addNote() {
            const noteInput = document.getElementById('noteInput');
            const noteContent = noteInput.value.trim();
            if (noteContent) {
                notes.push(noteContent);
                noteInput.value = '';
                saveData();
                renderNotes();
            }
        }

        // 删除笔记
        function deleteNote(index) {
            notes.splice(index, 1);
            saveData();
            renderNotes();
        }

        // 添加规则
        function addRule() {
            const findInput = document.getElementById('findInput');
            const replaceInput = document.getElementById('replaceInput');
            const findText = findInput.value.trim();
            const replaceText = replaceInput.value.trim();

            if (findText && replaceText) {
                rules.push({ find: findText, replace: replaceText });
                findInput.value = '';
                replaceInput.value = '';
                saveData();
                renderRules();
            } else {
                alert('查找和替换内容都不能为空！');
            }
        }

        // 删除规则
        function deleteRule(index) {
            rules.splice(index, 1);
            saveData();
            renderRules();
        }

        // 应用规则
        function applyRules() {
            if (notes.length === 0) {
                alert('没有笔记可以应用规则。');
                return;
            }
            if (rules.length === 0) {
                alert('没有定义替换规则。');
                return;
            }

            const newNotes = notes.map(note => {
                let modifiedNote = note;
                rules.forEach(rule => {
                    // 使用正则表达式进行全局替换，考虑特殊字符转义
                    const escapedFind = rule.find.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const regex = new RegExp(escapedFind, 'g');
                    modifiedNote = modifiedNote.replace(regex, rule.replace);
                });
                return modifiedNote;
            });
            notes = newNotes; // 更新笔记
            saveData();
            renderNotes();
            alert('规则已应用到所有笔记！');
        }

        // 初始化加载数据
        window.onload = loadData;
    </script>
</body>
</html> 