<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Inhibit</title>
    <!-- 引入第三方库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
        :root {
            --primary-color: #ab47bc; /* 紫色，代表抑制与约束 */
            --secondary-color: #8e24aa;
            --accent-color: #fce4ec; 
            --light-bg: #f3e5f5;
            --panel-bg: #ffffff;
            --text-color: #4a148c;
        }
        body, .container, .word-panel, h1, p, .breakdown-section, .morpheme-btn, .animation-panel, .activity-title, .activity-wrapper, .control-button { box-sizing: border-box; }
        body { font-family: 'Roboto', 'Noto Sans SC', sans-serif; background-color: #e1bee7; color: var(--text-color); display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; overflow: hidden; }
        .container { display: flex; flex-direction: row; width: 95%; max-width: 1400px; height: 90vh; max-height: 800px; background-color: var(--panel-bg); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .word-panel { flex: 1; padding: 40px; display: flex; flex-direction: column; justify-content: center; background-color: var(--light-bg); overflow-y: auto; }
        .word-panel h1 { font-size: 3.5em; color: var(--primary-color); }
        .word-panel .pronunciation { font-size: 1.5em; color: var(--secondary-color); margin-bottom: 20px; }
        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--secondary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: #4a148c; }
        .animation-panel { flex: 2; padding: 20px; display: flex; flex-direction: column; justify-content: center; align-items: center; position: relative; background: #424242; }
        .activity-title { font-size: 1.8em; color: var(--light-bg); margin-bottom: 15px; text-align: center; }
        .game-container { width: 100%; height: calc(100% - 100px); position: relative; display: flex; align-items: center; justify-content: center; border-radius: 15px; background: #303030; overflow: hidden; }
        #story-canvas { width: 100%; height: 100%; }
        .control-button { margin-top: 20px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; }
        .control-button:hover { background-color: var(--secondary-color); }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>inhibit</h1>
            <p class="pronunciation">[ɪnˈhɪbɪt]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 抑制，约束，禁止</p>
                <p><strong>词源:</strong> in-(在内) + hibit(持有) → 在内部控制住 → 抑制</p>
                <p><strong>含义：</strong><br>阻止某事发生或减缓其过程。</p>
                <div class="example">
                    <p><strong>例句:</strong> This drug will inhibit the growth of tumors.</p>
                    <p><strong>翻译:</strong> 这种药会抑制肿瘤的生长。</p>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">词源故事：内在的约束</h2>
            <div class="game-container">
                <div id="story-canvas" style="width: 100%; height: 100%;"></div>
            </div>
            <button class="control-button" id="play-story-btn">开始故事</button>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const morphemeStory = {
            canvasContainer: document.getElementById('story-canvas'),
            btn: document.getElementById('play-story-btn'),
            scene: null, camera: null, renderer: null,
            character: null, cage: null, textMeshes: {},
            currentStep: 0,
            isPlaying: false,
            init() {
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x303030);
                this.camera = new THREE.PerspectiveCamera(75, this.canvasContainer.clientWidth / this.canvasContainer.clientHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(this.canvasContainer.clientWidth, this.canvasContainer.clientHeight);
                this.canvasContainer.appendChild(this.renderer.domElement);
                this.camera.position.set(0, 5, 15);
                this.camera.lookAt(0, 0, 0);

                const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
                this.scene.add(ambientLight);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(5, 10, 7);
                this.scene.add(directionalLight);
                
                this.character = new THREE.Mesh(
                    new THREE.SphereGeometry(1, 32, 32),
                    new THREE.MeshStandardMaterial({ color: 0x03a9f4, metalness: 0.5, roughness: 0.5 })
                );
                this.scene.add(this.character);

                this.createCage();
                
                this.btn.addEventListener('click', () => this.play());
                this.animate();
                window.addEventListener('resize', () => this.onResize());
            },

            createCage() {
                this.cage = new THREE.Group();
                const barGeometry = new THREE.CylinderGeometry(0.1, 0.1, 8, 8);
                const barMaterial = new THREE.MeshStandardMaterial({ color: 0x9e9e9e, metalness: 0.8, roughness: 0.2});
                const size = 4;
                // Vertical bars
                for(let i=0; i<4; i++) {
                    const bar = new THREE.Mesh(barGeometry, barMaterial);
                    bar.position.set( (i%2===0 ? -size:size), 0, (i<2 ? -size:size));
                    this.cage.add(bar);
                }
                // Horizontal bars
                const hBarGeometry = new THREE.CylinderGeometry(0.1, 0.1, 8, 8);
                hBarGeometry.rotateZ(Math.PI/2);
                 for(let i=0; i<4; i++) {
                    const bar = new THREE.Mesh(hBarGeometry, barMaterial);
                    bar.position.set(0, (i%2===0 ? -size:size), (i<2 ? -size:size));
                    this.cage.add(bar);
                }
                this.cage.scale.set(0,0,0);
                this.scene.add(this.cage);
            },
            
            createText(text, position, size = 1) {
                return new Promise(resolve => {
                    const loader = new THREE.FontLoader();
                    loader.load('https://cdn.jsdelivr.net/npm/three/examples/fonts/helvetiker_bold.typeface.json', (font) => {
                        const geometry = new THREE.TextGeometry(text, { font, size, height: 0.1 });
                        const material = new THREE.MeshStandardMaterial({ color: 0xeeeeee });
                        const mesh = new THREE.Mesh(geometry, material);
                        mesh.position.copy(position);
                        mesh.visible = false;
                        this.scene.add(mesh);
                        resolve(mesh);
                    });
                });
            },
            
            async setupTexts() {
                this.textMeshes.in = await this.createText('in-', new THREE.Vector3(-6, 3, 0));
                this.textMeshes.hibit = await this.createText('hibit', new THREE.Vector3(1, 3, 0));
                this.textMeshes.meaningHibit = await this.createText('"to hold"', new THREE.Vector3(1, 1.5, 0), 0.8);
                this.textMeshes.meaningIn = await this.createText('"in"', new THREE.Vector3(-6, 1.5, 0), 0.8);
                this.textMeshes.inhibit = await this.createText('inhibit = to hold in, restrain', new THREE.Vector3(-5, -2, 0), 1);
            },

            play() {
                if (this.isPlaying) return;
                this.isPlaying = true;
                this.btn.textContent = "正在播放...";
                this.btn.disabled = true;

                if (this.currentStep > 0) {
                    this.reset();
                }
                this.setupTexts().then(() => this.runSequence());
            },
            
            runSequence() {
                const stepActions = [
                    // Step 1: Character bounces around, "hibit" appears
                    () => {
                        this.textMeshes.hibit.visible = true;
                        this.textMeshes.meaningHibit.visible = true;
                        gsap.from(this.textMeshes.hibit.scale, { x: 0, y: 0, z: 0, duration: 1 });
                        gsap.from(this.textMeshes.meaningHibit.scale, { x: 0, y: 0, z: 0, duration: 1, delay: 0.5 });
                        gsap.to(this.character.position, { y: 2, x: -3, duration: 1, yoyo: true, repeat: 1, ease: 'power1.inOut'});
                        setTimeout(() => this.runSequence(), 3000);
                    },
                    // Step 2: "in-" appears, cage forms around character
                    () => {
                        this.textMeshes.in.visible = true;
                        this.textMeshes.meaningIn.visible = true;
                        gsap.from(this.textMeshes.in.scale, { x: 0, y: 0, z: 0, duration: 1 });
                        gsap.from(this.textMeshes.meaningIn.scale, { x: 0, y: 0, z: 0, duration: 1, delay: 0.5 });
                        gsap.to(this.cage.scale, { x: 1, y: 1, z: 1, duration: 1.5, ease: 'elastic.out(1, 0.5)' });
                        gsap.to(this.character.position, { x: 0, y: 0, z: 0, duration: 1 });
                        setTimeout(() => this.runSequence(), 3000);
                    },
                    // Step 3: "inhibit" is formed, character tries to escape and fails
                    () => {
                        [this.textMeshes.in, this.textMeshes.hibit, this.textMeshes.meaningIn, this.textMeshes.meaningHibit].forEach(t => gsap.to(t.scale, { x: 0.01, y: 0.01, z: 0.01, duration: 1 }));
                        this.textMeshes.inhibit.visible = true;
                        gsap.from(this.textMeshes.inhibit.scale, { x: 0, y: 0, z: 0, duration: 1.5 });
                        
                        // Character bumps into cage
                        const timeline = gsap.timeline();
                        timeline.to(this.character.position, { x: 3, duration: 0.5, ease: 'power2.in' })
                                .to(this.character.position, { x: 2.8, duration: 0.3 })
                                .to(this.character.position, { y: -3, duration: 0.5, ease: 'power2.in' })
                                .to(this.character.position, { y: -2.8, duration: 0.3 })
                                .to(this.character.position, { x: 0, y: 0, duration: 1});

                        setTimeout(() => this.runSequence(), 5000);
                    },
                    // Step 4: Finish
                    () => {
                        this.btn.textContent = "重新播放";
                        this.btn.disabled = false;
                        this.isPlaying = false;
                    }
                ];

                if (this.currentStep < stepActions.length) {
                    stepActions[this.currentStep]();
                    this.currentStep++;
                }
            },
            
            reset() {
                this.currentStep = 0;
                this.character.position.set(0, 0, 0);
                this.cage.scale.set(0, 0, 0);

                for (const key in this.textMeshes) {
                    this.scene.remove(this.textMeshes[key]);
                    this.textMeshes[key].geometry.dispose();
                    this.textMeshes[key].material.dispose();
                }
                this.textMeshes = {};
            },

            animate() {
                requestAnimationFrame(() => this.animate());
                if(!this.isPlaying) {
                     this.character.position.x = Math.sin(Date.now() * 0.001) * 2;
                     this.character.position.y = Math.cos(Date.now() * 0.001) * 2;
                }
                this.renderer.render(this.scene, this.camera);
            },
            onResize() {
                if (!this.renderer) return;
                this.camera.aspect = this.canvasContainer.clientWidth / this.canvasContainer.clientHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(this.canvasContainer.clientWidth, this.canvasContainer.clientHeight);
            }
        };

        morphemeStory.init();
    });
    </script>
</body>
</html> 