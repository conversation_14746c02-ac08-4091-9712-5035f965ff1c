<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Acquire</title>
    <!-- 引入第三方库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');
        :root {
            --primary-color: #26a69a; /* 青色，代表成长与获取 */
            --secondary-color: #00897b;
            --accent-color: #ffd54f; /* 金色，代表宝藏 */
            --light-bg: #e0f2f1;
            --panel-bg: #ffffff;
            --text-color: #004d40;
        }
        body, .container, .word-panel, h1, p, .breakdown-section, .morpheme-btn, .animation-panel, .activity-title, .activity-wrapper, .control-button { box-sizing: border-box; }
        body { font-family: 'Roboto', 'Noto Sans SC', sans-serif; background-color: #b2dfdb; color: var(--text-color); display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; overflow: hidden; }
        .container { display: flex; flex-direction: row; width: 95%; max-width: 1400px; height: 90vh; max-height: 800px; background-color: var(--panel-bg); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .word-panel { flex: 1; padding: 40px; display: flex; flex-direction: column; justify-content: center; background-color: var(--light-bg); overflow-y: auto; }
        .word-panel h1 { font-size: 3.5em; color: var(--primary-color); }
        .word-panel .pronunciation { font-size: 1.5em; color: var(--secondary-color); margin-bottom: 20px; }
        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--secondary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: #004d40; }
        .animation-panel { flex: 2; padding: 20px; display: flex; flex-direction: column; justify-content: center; align-items: center; position: relative; background: #37474f; }
        .activity-title { font-size: 1.8em; color: var(--light-bg); margin-bottom: 15px; text-align: center; }
        .game-container { width: 100%; height: calc(100% - 100px); position: relative; display: flex; align-items: center; justify-content: center; border-radius: 15px; background: #263238; overflow: hidden; }
        #story-canvas { width: 100%; height: 100%; }
        .story-narration {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            background: rgba(0, 77, 64, 0.8);
            color: white;
            border-radius: 8px;
            font-size: 1.2em;
            text-align: center;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s, visibility 0.5s;
        }
        .story-narration.visible {
            opacity: 1;
            visibility: visible;
        }
        .control-button { margin-top: 20px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; }
        .control-button:hover { background-color: var(--secondary-color); }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>acquire</h1>
            <p class="pronunciation">[əˈkwaɪər]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 获得，取得</p>
                <p><strong>词源:</strong> ac-(朝向) + quire(寻找) → 朝着一个方向去寻找 → 获得</p>
                <p><strong>含义：</strong><br>通过自身的努力、技能或行为得到某物。</p>
                <div class="example">
                    <p><strong>例句:</strong> He acquired the firm in 1998.</p>
                    <p><strong>翻译:</strong> 他于1998年购得了这家公司。</p>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">词源故事：寻宝之旅</h2>
            <div class="game-container">
                <div id="story-canvas" style="width: 100%; height: 100%;"></div>
                <div id="story-narration" class="story-narration"></div>
            </div>
            <button class="control-button" id="play-story-btn">开始故事</button>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const morphemeStory = {
            canvasContainer: document.getElementById('story-canvas'),
            btn: document.getElementById('play-story-btn'),
            narrationElem: document.getElementById('story-narration'),
            scene: null, camera: null, renderer: null,
            character: null, path: null, treasure: null, textMeshes: {},
            currentStep: 0,
            isPlaying: false,
            init() {
                // 基本设置
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x263238);
                this.camera = new THREE.PerspectiveCamera(75, this.canvasContainer.clientWidth / this.canvasContainer.clientHeight, 0.1, 1000);
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(this.canvasContainer.clientWidth, this.canvasContainer.clientHeight);
                this.canvasContainer.appendChild(this.renderer.domElement);
                this.camera.position.set(0, 4, 12);
                this.camera.lookAt(0, 0, 0);

                // 光照
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                this.scene.add(ambientLight);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(5, 10, 7);
                this.scene.add(directionalLight);
                
                // 创建地面
                const ground = new THREE.Mesh(
                    new THREE.PlaneGeometry(30, 30),
                    new THREE.MeshStandardMaterial({ color: 0x4caf50 })
                );
                ground.rotation.x = -Math.PI / 2;
                this.scene.add(ground);

                // 创建角色 (一个小球体)
                this.character = new THREE.Mesh(
                    new THREE.SphereGeometry(0.5, 32, 32),
                    new THREE.MeshStandardMaterial({ color: 0x42a5f5 })
                );
                this.character.position.set(-10, 0.5, 0);
                this.scene.add(this.character);

                // 创建宝箱
                this.treasure = new THREE.Group();
                const box = new THREE.Mesh(new THREE.BoxGeometry(1, 1, 1), new THREE.MeshStandardMaterial({ color: 0x8d6e63 }));
                const lid = new THREE.Mesh(new THREE.BoxGeometry(1, 0.2, 1), new THREE.MeshStandardMaterial({ color: 0x8d6e63 }));
                lid.position.y = 0.6;
                this.treasure.add(box, lid);
                this.treasure.position.set(10, 0.5, 0);
                this.treasure.visible = false;
                this.scene.add(this.treasure);
                
                this.btn.addEventListener('click', () => this.play());
                this.animate();
                window.addEventListener('resize', () => this.onResize());
            },

            showNarration(text) {
                this.narrationElem.textContent = text;
                this.narrationElem.classList.add('visible');
            },

            hideNarration() {
                this.narrationElem.classList.remove('visible');
            },

            createText(text, position, size = 1) {
                return new Promise(resolve => {
                    const loader = new THREE.FontLoader();
                    loader.load('https://cdn.jsdelivr.net/npm/three/examples/fonts/helvetiker_regular.typeface.json', (font) => {
                        const geometry = new THREE.TextGeometry(text, {
                            font: font,
                            size: size,
                            height: 0.1,
                        });
                        const material = new THREE.MeshStandardMaterial({ color: 0xffffff });
                        const mesh = new THREE.Mesh(geometry, material);
                        mesh.position.copy(position);
                        mesh.visible = false;
                        this.scene.add(mesh);
                        resolve(mesh);
                    });
                });
            },
            
            async setupTexts() {
                this.textMeshes.acquire = await this.createText('acquire', new THREE.Vector3(-3, 5, 0), 1.5);
                this.textMeshes.ac = await this.createText('ac-', new THREE.Vector3(-5, 3, 0));
                this.textMeshes.quire = await this.createText('quire', new THREE.Vector3(2, 3, 0));
                this.textMeshes.meaningQuire = await this.createText('means "to seek"', new THREE.Vector3(2, 1.5, 0), 0.5);
                this.textMeshes.meaningAc = await this.createText('means "toward"', new THREE.Vector3(-5, 1.5, 0), 0.5);
                this.textMeshes.meaningAcquire = await this.createText('= to get, to obtain', new THREE.Vector3(-3, 1, 0), 0.8);
            },

            play() {
                if (this.isPlaying) return;
                this.isPlaying = true;
                this.btn.textContent = "正在播放...";
                this.btn.disabled = true;

                if (this.currentStep === 0) {
                     this.setupTexts().then(() => this.runSequence());
                } else {
                    this.reset();
                    this.setupTexts().then(() => this.runSequence());
                }
            },
            
            runSequence() {
                const stepActions = [
                    // Step 1: Show "quire" and its meaning, character starts seeking
                    () => {
                        this.textMeshes.quire.visible = true;
                        this.textMeshes.meaningQuire.visible = true;
                        gsap.from(this.textMeshes.quire.scale, { x: 0, y: 0, z: 0, duration: 1 });
                        gsap.from(this.textMeshes.meaningQuire.scale, { x: 0, y: 0, z: 0, duration: 1, delay: 0.5 });
                        gsap.to(this.character.position, { x: 0, duration: 3, ease: 'power1.inOut' });
                        setTimeout(() => this.runSequence(), 4000);
                    },
                    // Step 2: Show "ac-" and its meaning, treasure appears, character moves toward it
                    () => {
                        this.textMeshes.ac.visible = true;
                        this.textMeshes.meaningAc.visible = true;
                        this.treasure.visible = true;
                        gsap.from(this.textMeshes.ac.scale, { x: 0, y: 0, z: 0, duration: 1 });
                        gsap.from(this.textMeshes.meaningAc.scale, { x: 0, y: 0, z: 0, duration: 1, delay: 0.5 });
                        gsap.from(this.treasure.scale, { x: 0, y: 0, z: 0, duration: 1 });
                        gsap.to(this.character.position, { x: 9, duration: 3, ease: 'power1.inOut' });
                        setTimeout(() => this.runSequence(), 4000);
                    },
                    // Step 3: Character reaches treasure, "acquire" is formed
                    () => {
                        // Open treasure chest
                        gsap.to(this.treasure.children[1].rotation, { x: -Math.PI / 2, duration: 1 });
                        gsap.to(this.treasure.children[1].position, { y: 0.5, z: -0.5, duration: 1 });

                        // Animate texts coming together
                        gsap.to(this.textMeshes.ac.position, { x: -4, y: 5, duration: 1.5, ease: 'back.inOut(1.7)' });
                        gsap.to(this.textMeshes.quire.position, { x: -0.5, y: 5, duration: 1.5, ease: 'back.inOut(1.7)' });
                        gsap.to(this.textMeshes.ac.scale, { x:1.5, y:1.5, z:1.5, duration: 1.5 });
                        gsap.to(this.textMeshes.quire.scale, { x:1.5, y:1.5, z:1.5, duration: 1.5 });
                        
                        [this.textMeshes.meaningAc, this.textMeshes.meaningQuire].forEach(t => gsap.to(t.scale, { x: 0, y: 0, z: 0, duration: 1 }));

                        setTimeout(() => {
                             this.textMeshes.meaningAcquire.visible = true;
                             gsap.from(this.textMeshes.meaningAcquire.scale, { x: 0, y: 0, z: 0, duration: 1 });
                        }, 2000);

                        setTimeout(() => this.runSequence(), 5000);
                    },
                    // Step 4: Finish
                    () => {
                        this.btn.textContent = "重新播放";
                        this.btn.disabled = false;
                        this.isPlaying = false;
                        this.currentStep = 0; // Reset for next play
                    }
                ];

                if (this.currentStep < stepActions.length) {
                    stepActions[this.currentStep]();
                    this.currentStep++;
                }
            },
            
            reset() {
                this.currentStep = 0;
                this.character.position.set(-10, 0.5, 0);
                this.treasure.visible = false;
                this.treasure.children[1].rotation.x = 0;
                this.treasure.children[1].position.set(0, 0.6, 0);

                for (const key in this.textMeshes) {
                    this.scene.remove(this.textMeshes[key]);
                    this.textMeshes[key].geometry.dispose();
                    this.textMeshes[key].material.dispose();
                }
                this.textMeshes = {};
            },

            animate() {
                requestAnimationFrame(() => this.animate());
                this.renderer.render(this.scene, this.camera);
            },
            onResize() {
                if (!this.renderer) return;
                this.camera.aspect = this.canvasContainer.clientWidth / this.canvasContainer.clientHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(this.canvasContainer.clientWidth, this.canvasContainer.clientHeight);
            }
        };

        morphemeStory.init();
    });
    </script>
</body>
</html> 