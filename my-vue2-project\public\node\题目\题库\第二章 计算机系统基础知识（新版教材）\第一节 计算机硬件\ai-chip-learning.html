<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI芯片探索之旅 - 软考达人</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            border-color: #667eea;
        }

        .option.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-color: #4CAF50;
        }

        .option.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            border-color: #f44336;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        #chipCanvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            background: linear-gradient(45deg, #f0f2f5, #ffffff);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.9s both;
        }

        .chip-type {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .chip-type:hover {
            transform: translateX(10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .chip-name {
            font-size: 1.4rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .chip-desc {
            color: #666;
            line-height: 1.6;
        }

        .start-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
        }

        .start-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .floating {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🧠 AI芯片探索之旅</h1>
            <p class="subtitle">通过动画和交互学习AI芯片的三大架构</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>【软考达人-回忆版】</strong><br>
                AI芯片是当前人工智能技术发展的核心技术，其能力要支持训练和推理。通常，AI芯片的技术架构包括（ ）等三种。
            </div>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> GPU、FPGA、ASIC
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> CPU、PPGA、DSP
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> GPU、CPU、ASIC
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> GPU、FPGA、SOC
                </div>
            </div>
            
            <button class="start-btn" onclick="startAnimation()">🚀 开始AI芯片动画演示</button>
            <button class="start-btn" onclick="showComparison()" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">📊 查看详细对比</button>
        </div>

        <div class="canvas-container">
            <canvas id="chipCanvas"></canvas>
        </div>

        <div class="explanation">
            <h2 style="color: #333; margin-bottom: 30px; text-align: center;">🎯 AI芯片三大架构详解</h2>

            <div class="chip-type" onclick="animateChip('GPU')">
                <div class="chip-name">🎮 GPU (图形处理器)</div>
                <div class="chip-desc">
                    专门设计用于并行计算，拥有数千个小核心，非常适合AI训练中的矩阵运算。
                    就像一个拥有很多工人的工厂，每个工人都能同时处理简单任务。
                    <br><strong>💡 点击查看详细介绍</strong>
                </div>
            </div>

            <div class="chip-type" onclick="animateChip('FPGA')">
                <div class="chip-name">🔧 FPGA (现场可编程门阵列)</div>
                <div class="chip-desc">
                    可以重新配置的芯片，像乐高积木一样可以根据需要重新组装电路。
                    功耗低，延迟小，特别适合AI推理阶段。
                    <br><strong>💡 点击查看详细介绍</strong>
                </div>
            </div>

            <div class="chip-type" onclick="animateChip('ASIC')">
                <div class="chip-name">⚡ ASIC (专用集成电路)</div>
                <div class="chip-desc">
                    专门为特定AI算法定制的芯片，性能最高，功耗最低。
                    就像为特定任务量身定制的专业工具。
                    <br><strong>💡 点击查看详细介绍</strong>
                </div>
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <button class="start-btn" onclick="startQuiz()" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">🎮 开始知识小测验</button>
            </div>
        </div>

        <!-- 测验模态框 -->
        <div id="quizModal" style="display: none;"></div>
    </div>

    <script>
        const canvas = document.getElementById('chipCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        let animationId;
        let currentChip = null;
        let particles = [];

        // 粒子类
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 2;
                this.vy = (Math.random() - 0.5) * 2;
                this.color = color;
                this.life = 1;
                this.decay = 0.02;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 绘制芯片
        function drawChip(type, x, y, size, highlight = false) {
            ctx.save();
            
            if (highlight) {
                ctx.shadowColor = '#667eea';
                ctx.shadowBlur = 20;
            }

            // 芯片主体
            ctx.fillStyle = highlight ? '#667eea' : '#34495e';
            ctx.fillRect(x - size/2, y - size/2, size, size);

            // 芯片引脚
            ctx.fillStyle = '#95a5a6';
            for (let i = 0; i < 8; i++) {
                const pinX = x - size/2 - 10;
                const pinY = y - size/2 + (i * size/7);
                ctx.fillRect(pinX, pinY, 10, 5);
                
                const pinX2 = x + size/2;
                ctx.fillRect(pinX2, pinY, 10, 5);
            }

            // 芯片标签
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(type, x, y + 5);

            ctx.restore();
        }

        // 绘制连接线
        function drawConnection(x1, y1, x2, y2, animated = false) {
            ctx.strokeStyle = animated ? '#e74c3c' : '#3498db';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();

            if (animated) {
                // 添加流动效果
                const time = Date.now() * 0.005;
                const dotX = x1 + (x2 - x1) * (Math.sin(time) + 1) / 2;
                const dotY = y1 + (y2 - y1) * (Math.sin(time) + 1) / 2;
                
                ctx.fillStyle = '#e74c3c';
                ctx.beginPath();
                ctx.arc(dotX, dotY, 5, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        // 主动画循环
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制三个芯片
            drawChip('GPU', centerX - 150, centerY, 80, currentChip === 'GPU');
            drawChip('FPGA', centerX, centerY - 100, 80, currentChip === 'FPGA');
            drawChip('ASIC', centerX + 150, centerY, 80, currentChip === 'ASIC');

            // 绘制连接线
            drawConnection(centerX - 150, centerY, centerX, centerY - 100, currentChip === 'GPU');
            drawConnection(centerX, centerY - 100, centerX + 150, centerY, currentChip === 'FPGA');
            drawConnection(centerX + 150, centerY, centerX - 150, centerY, currentChip === 'ASIC');

            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return particle.life > 0;
            });

            // 添加新粒子
            if (currentChip && Math.random() < 0.3) {
                let chipX, chipY;
                switch(currentChip) {
                    case 'GPU': chipX = centerX - 150; chipY = centerY; break;
                    case 'FPGA': chipX = centerX; chipY = centerY - 100; break;
                    case 'ASIC': chipX = centerX + 150; chipY = centerY; break;
                }
                particles.push(new Particle(chipX, chipY, '#667eea'));
            }

            animationId = requestAnimationFrame(animate);
        }

        // 开始动画
        function startAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animate();
        }

        // 芯片点击动画
        function animateChip(chipType) {
            currentChip = chipType;
            
            // 添加爆炸效果
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            let chipX, chipY;
            
            switch(chipType) {
                case 'GPU': chipX = centerX - 150; chipY = centerY; break;
                case 'FPGA': chipX = centerX; chipY = centerY - 100; break;
                case 'ASIC': chipX = centerX + 150; chipY = centerY; break;
            }

            for (let i = 0; i < 20; i++) {
                particles.push(new Particle(chipX, chipY, '#667eea'));
            }

            setTimeout(() => {
                currentChip = null;
            }, 3000);
        }

        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                
                // 清除之前的样式
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });

                if (answer === 'A') {
                    this.classList.add('correct');
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\nAI芯片的三大架构确实是：\n• GPU - 并行计算能力强\n• FPGA - 可重新配置\n• ASIC - 专用定制芯片\n\n让我们继续探索每种芯片的特点吧！');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    // 同时显示正确答案
                    document.querySelector('[data-answer="A"]').classList.add('correct');
                    setTimeout(() => {
                        alert('❌ 答案不正确\n\n正确答案是 A：GPU、FPGA、ASIC\n\n让我们通过动画来学习这三种芯片的区别吧！');
                    }, 500);
                }
            });
        });

        // 知识卡片动画
        function showKnowledgeCard(chipType) {
            const cards = {
                'GPU': {
                    title: '🎮 GPU - 并行计算之王',
                    content: `
                        <div style="text-align: left; line-height: 1.8;">
                            <h3>🔥 核心特点：</h3>
                            <p>• 拥有数千个小核心，擅长并行计算</p>
                            <p>• 原本为图形渲染设计，后来发现非常适合AI计算</p>
                            <p>• 特别适合深度学习的训练阶段</p>

                            <h3>🎯 应用场景：</h3>
                            <p>• 神经网络训练</p>
                            <p>• 图像识别</p>
                            <p>• 自然语言处理</p>

                            <h3>💡 形象比喻：</h3>
                            <p>就像一个拥有很多工人的工厂，每个工人都能同时处理简单的重复任务，虽然单个工人不如专家厉害，但胜在数量多、协作好！</p>
                        </div>
                    `
                },
                'FPGA': {
                    title: '🔧 FPGA - 灵活变身专家',
                    content: `
                        <div style="text-align: left; line-height: 1.8;">
                            <h3>🔥 核心特点：</h3>
                            <p>• 可重新配置的硬件电路</p>
                            <p>• 功耗低，延迟小</p>
                            <p>• 可以根据不同算法调整电路结构</p>

                            <h3>🎯 应用场景：</h3>
                            <p>• AI推理加速</p>
                            <p>• 边缘计算</p>
                            <p>• 实时图像处理</p>

                            <h3>💡 形象比喻：</h3>
                            <p>就像乐高积木，可以根据需要重新组装成不同的形状和功能。今天是汽车，明天可以变成飞机！</p>
                        </div>
                    `
                },
                'ASIC': {
                    title: '⚡ ASIC - 专业定制高手',
                    content: `
                        <div style="text-align: left; line-height: 1.8;">
                            <h3>🔥 核心特点：</h3>
                            <p>• 专门为特定AI算法定制</p>
                            <p>• 性能最高，功耗最低</p>
                            <p>• 一旦制造完成就无法修改</p>

                            <h3>🎯 应用场景：</h3>
                            <p>• 比特币挖矿</p>
                            <p>• 手机AI芯片</p>
                            <p>• 专用AI加速器</p>

                            <h3>💡 形象比喻：</h3>
                            <p>就像为特定任务量身定制的专业工具，比如专业的蛋糕模具，只能做蛋糕，但做出来的蛋糕是最完美的！</p>
                        </div>
                    `
                }
            };

            const card = cards[chipType];
            if (card) {
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                    animation: fadeIn 0.3s ease;
                `;

                modal.innerHTML = `
                    <div style="
                        background: white;
                        border-radius: 20px;
                        padding: 40px;
                        max-width: 600px;
                        max-height: 80vh;
                        overflow-y: auto;
                        position: relative;
                        animation: slideIn 0.3s ease;
                    ">
                        <button onclick="this.closest('.modal').remove()" style="
                            position: absolute;
                            top: 15px;
                            right: 20px;
                            background: none;
                            border: none;
                            font-size: 24px;
                            cursor: pointer;
                            color: #999;
                        ">×</button>
                        <h2 style="color: #333; margin-bottom: 20px;">${card.title}</h2>
                        ${card.content}
                    </div>
                `;

                modal.className = 'modal';
                document.body.appendChild(modal);

                // 点击背景关闭
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });
            }
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes slideIn {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        // 修改芯片点击事件，添加知识卡片
        function animateChip(chipType) {
            currentChip = chipType;

            // 添加爆炸效果
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            let chipX, chipY;

            switch(chipType) {
                case 'GPU': chipX = centerX - 150; chipY = centerY; break;
                case 'FPGA': chipX = centerX; chipY = centerY - 100; break;
                case 'ASIC': chipX = centerX + 150; chipY = centerY; break;
            }

            for (let i = 0; i < 20; i++) {
                particles.push(new Particle(chipX, chipY, '#667eea'));
            }

            // 显示知识卡片
            setTimeout(() => {
                showKnowledgeCard(chipType);
            }, 500);

            setTimeout(() => {
                currentChip = null;
            }, 3000);
        }

        // 添加比较功能
        function showComparison() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 20px;
                    padding: 40px;
                    max-width: 900px;
                    max-height: 80vh;
                    overflow-y: auto;
                    position: relative;
                    animation: slideIn 0.3s ease;
                ">
                    <button onclick="this.closest('.modal').remove()" style="
                        position: absolute;
                        top: 15px;
                        right: 20px;
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #999;
                    ">×</button>
                    <h2 style="color: #333; margin-bottom: 30px; text-align: center;">🔍 AI芯片三大架构对比</h2>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 15px; border: 1px solid #dee2e6;">特性</th>
                                <th style="padding: 15px; border: 1px solid #dee2e6;">GPU</th>
                                <th style="padding: 15px; border: 1px solid #dee2e6;">FPGA</th>
                                <th style="padding: 15px; border: 1px solid #dee2e6;">ASIC</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="padding: 15px; border: 1px solid #dee2e6; font-weight: bold;">性能</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">高</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">中等</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">最高</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding: 15px; border: 1px solid #dee2e6; font-weight: bold;">功耗</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">高</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">低</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">最低</td>
                            </tr>
                            <tr>
                                <td style="padding: 15px; border: 1px solid #dee2e6; font-weight: bold;">灵活性</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">中等</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">高</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">无</td>
                            </tr>
                            <tr style="background: #f8f9fa;">
                                <td style="padding: 15px; border: 1px solid #dee2e6; font-weight: bold;">开发成本</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">低</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">中等</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">高</td>
                            </tr>
                            <tr>
                                <td style="padding: 15px; border: 1px solid #dee2e6; font-weight: bold;">适用场景</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">训练、通用计算</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">推理、边缘计算</td>
                                <td style="padding: 15px; border: 1px solid #dee2e6;">专用任务</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);

            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }

        // 测验功能
        const quizQuestions = [
            {
                question: "哪种AI芯片最适合深度学习训练？",
                options: ["CPU", "GPU", "FPGA", "ASIC"],
                correct: 1,
                explanation: "GPU拥有数千个小核心，非常适合并行计算，是深度学习训练的首选。"
            },
            {
                question: "哪种AI芯片可以重新配置电路？",
                options: ["GPU", "FPGA", "ASIC", "CPU"],
                correct: 1,
                explanation: "FPGA（现场可编程门阵列）可以根据需要重新配置硬件电路。"
            },
            {
                question: "哪种AI芯片功耗最低？",
                options: ["GPU", "FPGA", "ASIC", "CPU"],
                correct: 2,
                explanation: "ASIC是专门定制的芯片，针对特定任务优化，因此功耗最低。"
            },
            {
                question: "AI芯片的三大架构不包括以下哪个？",
                options: ["GPU", "CPU", "FPGA", "ASIC"],
                correct: 1,
                explanation: "AI芯片的三大架构是GPU、FPGA、ASIC，CPU虽然也能做AI计算，但不是专门的AI芯片架构。"
            }
        ];

        let currentQuestionIndex = 0;
        let score = 0;

        function startQuiz() {
            currentQuestionIndex = 0;
            score = 0;
            showQuestion();
        }

        function showQuestion() {
            const question = quizQuestions[currentQuestionIndex];
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 20px;
                    padding: 40px;
                    max-width: 600px;
                    position: relative;
                    animation: slideIn 0.3s ease;
                ">
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h3>🎯 问题 ${currentQuestionIndex + 1}/${quizQuestions.length}</h3>
                        <div style="background: #f0f2f5; border-radius: 10px; padding: 5px; margin: 10px 0;">
                            <div style="background: #667eea; height: 10px; border-radius: 5px; width: ${((currentQuestionIndex + 1) / quizQuestions.length) * 100}%; transition: width 0.3s ease;"></div>
                        </div>
                        <p style="font-size: 18px; margin: 20px 0; color: #333;">${question.question}</p>
                    </div>
                    <div style="display: grid; gap: 15px;">
                        ${question.options.map((option, index) => `
                            <button onclick="selectAnswer(${index}, this)" style="
                                background: #f8f9fa;
                                border: 2px solid #e9ecef;
                                border-radius: 10px;
                                padding: 15px;
                                cursor: pointer;
                                transition: all 0.3s ease;
                                font-size: 16px;
                            " onmouseover="this.style.background='#e9ecef'" onmouseout="this.style.background='#f8f9fa'">
                                ${String.fromCharCode(65 + index)}. ${option}
                            </button>
                        `).join('')}
                    </div>
                </div>
            `;

            modal.className = 'quiz-modal';
            document.body.appendChild(modal);
        }

        function selectAnswer(selectedIndex, buttonElement) {
            const question = quizQuestions[currentQuestionIndex];
            const buttons = buttonElement.parentElement.querySelectorAll('button');

            // 禁用所有按钮
            buttons.forEach(btn => btn.disabled = true);

            if (selectedIndex === question.correct) {
                buttonElement.style.background = '#4CAF50';
                buttonElement.style.color = 'white';
                score++;

                // 添加成功动画
                for (let i = 0; i < 10; i++) {
                    particles.push(new Particle(
                        canvas.width / 2 + (Math.random() - 0.5) * 100,
                        canvas.height / 2 + (Math.random() - 0.5) * 100,
                        '#4CAF50'
                    ));
                }
            } else {
                buttonElement.style.background = '#f44336';
                buttonElement.style.color = 'white';
                buttons[question.correct].style.background = '#4CAF50';
                buttons[question.correct].style.color = 'white';
            }

            // 显示解释
            setTimeout(() => {
                const explanation = document.createElement('div');
                explanation.style.cssText = `
                    background: #f0f2f5;
                    border-radius: 10px;
                    padding: 15px;
                    margin-top: 20px;
                    border-left: 4px solid #667eea;
                `;
                explanation.innerHTML = `<strong>💡 解释：</strong> ${question.explanation}`;
                buttonElement.parentElement.parentElement.appendChild(explanation);

                // 下一题按钮
                const nextButton = document.createElement('button');
                nextButton.textContent = currentQuestionIndex < quizQuestions.length - 1 ? '下一题 →' : '查看结果 🎉';
                nextButton.style.cssText = `
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    border: none;
                    padding: 12px 30px;
                    border-radius: 25px;
                    cursor: pointer;
                    margin-top: 20px;
                    font-size: 16px;
                    display: block;
                    margin-left: auto;
                    margin-right: auto;
                `;
                nextButton.onclick = () => {
                    document.querySelector('.quiz-modal').remove();
                    currentQuestionIndex++;
                    if (currentQuestionIndex < quizQuestions.length) {
                        setTimeout(showQuestion, 300);
                    } else {
                        setTimeout(showQuizResult, 300);
                    }
                };
                buttonElement.parentElement.parentElement.appendChild(nextButton);
            }, 1000);
        }

        function showQuizResult() {
            const percentage = Math.round((score / quizQuestions.length) * 100);
            let message, emoji;

            if (percentage >= 80) {
                message = "太棒了！你已经掌握了AI芯片的核心知识！";
                emoji = "🏆";
            } else if (percentage >= 60) {
                message = "不错！继续学习，你会更棒的！";
                emoji = "👍";
            } else {
                message = "加油！多复习一下，你一定能掌握的！";
                emoji = "💪";
            }

            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 20px;
                    padding: 40px;
                    max-width: 500px;
                    text-align: center;
                    position: relative;
                    animation: slideIn 0.3s ease;
                ">
                    <h2 style="color: #333; margin-bottom: 20px;">${emoji} 测验完成！</h2>
                    <div style="font-size: 48px; margin: 20px 0;">${percentage}%</div>
                    <p style="font-size: 18px; color: #666; margin-bottom: 20px;">
                        你答对了 ${score}/${quizQuestions.length} 题
                    </p>
                    <p style="color: #333; margin-bottom: 30px;">${message}</p>
                    <button onclick="this.closest('.quiz-modal').remove(); startQuiz();" style="
                        background: linear-gradient(135deg, #667eea, #764ba2);
                        color: white;
                        border: none;
                        padding: 12px 30px;
                        border-radius: 25px;
                        cursor: pointer;
                        margin: 10px;
                        font-size: 16px;
                    ">🔄 重新测验</button>
                    <button onclick="this.closest('.quiz-modal').remove();" style="
                        background: linear-gradient(135deg, #27ae60, #2ecc71);
                        color: white;
                        border: none;
                        padding: 12px 30px;
                        border-radius: 25px;
                        cursor: pointer;
                        margin: 10px;
                        font-size: 16px;
                    ">✅ 完成学习</button>
                </div>
            `;

            modal.className = 'quiz-modal';
            document.body.appendChild(modal);

            // 庆祝动画
            if (percentage >= 80) {
                for (let i = 0; i < 50; i++) {
                    setTimeout(() => {
                        particles.push(new Particle(
                            Math.random() * canvas.width,
                            Math.random() * canvas.height,
                            ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1'][Math.floor(Math.random() * 4)]
                        ));
                    }, i * 50);
                }
            }
        }

        // 初始化
        startAnimation();
    </script>
</body>
</html>
