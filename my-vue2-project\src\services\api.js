const mockNotes = [
  { id: 1, title: '模拟笔记 1', content: '这是第一条模拟笔记的内容。', category: '笔记系统', createdAt: new Date(Date.now() - 2 * 60 * 1000).toISOString() },
  { id: 2, title: '模拟笔记 2', content: '这是第二条模拟笔记的内容。', category: '知识', createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() },
  { id: 3, title: '模拟笔记 3', content: '这是第三条模拟笔记的内容。', category: '笔记系统', createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() }
];

const noteApi = {
  getAllNotes() {
    return Promise.resolve(mockNotes);
  },
  
  getNoteById(id) {
    const note = mockNotes.find(note => note.id === parseInt(id));
    return note ? Promise.resolve(note) : Promise.reject(new Error('Note not found'));
  }
};

export default noteApi; 