<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Imminent</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f7ff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #333;
            overflow: hidden;
        }
        #animation-container {
            text-align: center;
            position: relative;
        }
        canvas {
            background-color: #ffffff;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }
        #instruction {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 16px;
            color: #555;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 8px 15px;
            border-radius: 15px;
            animation: fadeIn 1s ease-in-out;
            opacity: 0;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="animation-container">
        <canvas id="word-canvas" width="800" height="600"></canvas>
        <div id="instruction">点击屏幕继续动画</div>
    </div>

    <script>
        const canvas = document.getElementById('word-canvas');
        const ctx = canvas.getContext('2d');
        const instructionEl = document.getElementById('instruction');

        let stage = 0;
        let animationFrame;
        const totalStages = 6;

        const colors = {
            primary: '#2E86C1', // A strong blue
            secondary: '#E74C3C', // A danger red
            dark: '#2C3E50',
            light: '#ECF0F1',
            green: '#28B463',
        };

        // Animation properties
        let alpha = 0;
        let wordPos = { x: canvas.width / 2, y: canvas.height / 2 };
        let imPos = { x: canvas.width / 2 - 150, y: canvas.height / 2 };
        let minentPos = { x: canvas.width / 2 + 100, y: canvas.height / 2 };
        let mountainPath;
        let rock = { x: 450, y: 180, angle: 0, shake: 0 };

        // Helper functions
        function lerp(start, end, t) {
            return start * (1 - t) + end * t;
        }

        function drawText(text, x, y, size = 60, color = colors.dark, a = 1) {
            ctx.save();
            ctx.globalAlpha = a;
            ctx.font = `bold ${size}px 'Segoe UI'`;
            ctx.fillStyle = color;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
            ctx.restore();
        }

        function drawExplanation(text, x, y, a = 1) {
            drawText(text, x, y, 24, colors.primary, a);
        }

        function drawMountain() {
            if (!mountainPath) {
                mountainPath = new Path2D();
                mountainPath.moveTo(100, 500);
                mountainPath.lineTo(300, 250);
                mountainPath.lineTo(450, 400); // Peak
                mountainPath.lineTo(550, 200); // Higher Peak
                mountainPath.lineTo(700, 500);
                mountainPath.closePath();
            }
            ctx.fillStyle = '#95A5A6';
            ctx.fill(mountainPath);
            
            // Snow caps
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.moveTo(550, 200);
            ctx.lineTo(520, 250);
            ctx.lineTo(580, 250);
            ctx.closePath();
            ctx.fill();
        }

        function drawRock(r) {
            ctx.save();
            ctx.translate(r.x, r.y);
            ctx.rotate(r.angle);
            ctx.fillStyle = colors.secondary;
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(-20, -10);
            ctx.lineTo(-10, -30);
            ctx.lineTo(15, -25);
            ctx.lineTo(10, -5);
            ctx.closePath();
            ctx.fill();
            ctx.restore();
        }
        
        // Animation Stages
        function intro() {
            alpha = lerp(alpha, 1, 0.05);
            drawText('imminent', wordPos.x, wordPos.y, 80, colors.dark, alpha);
            if (alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }

        function splitWord() {
            instructionEl.style.opacity = 0;
            let targetImX = canvas.width / 2 - 180;
            let targetMinentX = canvas.width / 2 + 130;
            imPos.x = lerp(imPos.x, targetImX, 0.1);
            minentPos.x = lerp(minentPos.x, targetMinentX, 0.1);

            drawText('im', imPos.x, imPos.y, 80, colors.secondary);
            drawText('minent', minentPos.x, minentPos.y, 80, colors.primary);

            if (Math.abs(imPos.x - targetImX) < 1) {
                stage++;
                alpha = 0;
            }
        }
        
        function showMinent() {
            alpha = lerp(alpha, 1, 0.05);
            ctx.globalAlpha = alpha;
            drawText('minent', canvas.width / 2, 100, 80, colors.primary);
            drawExplanation('~ minere = to project (突出)', canvas.width / 2, 160, alpha);
            
            drawMountain();
            
            if(alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }

        function showIm() {
            instructionEl.style.opacity = 0;
            alpha = lerp(alpha, 1, 0.05);

            drawMountain();
            drawText('im', 200, 100, 80, colors.secondary);
            drawExplanation('= in, on (在...之上)', 200, 160, alpha);
            
            rock.y = lerp(rock.y, 235, 0.05); // Rock falls onto the mountain
            drawRock(rock);
           
            if(alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }
        
        function combine() {
            instructionEl.style.opacity = 0;
            alpha = lerp(alpha, 1, 0.05);

            drawMountain();

            // Make the rock shake
            rock.shake = Math.sin(Date.now() / 50) * 0.05;
            rock.angle += rock.shake;
            drawRock(rock);

            drawText('imminent', wordPos.x, 30, 70, colors.dark, alpha);
            drawExplanation('即将发生的, 临近的', wordPos.x, 80, alpha);
            drawExplanation('形容一种紧张的、一触即发的状态', wordPos.x, 120, alpha);

            if(alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }

        function finalScene() {
            instructionEl.style.opacity = 0;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            drawMountain();
            
            // Rock falls
            rock.y += 5;
            rock.angle += 0.1;
            drawRock(rock);

            drawText('imminent', wordPos.x, 80, 80, colors.dark, 1);

             if (rock.y > canvas.height + 50) {
                drawText('再看一遍?', canvas.width / 2, canvas.height / 2, 40, colors.primary);
             }
        }


        // Main animation loop
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            switch (stage) {
                case 0:
                    intro();
                    break;
                case 1:
                    splitWord();
                    break;
                case 2:
                    showMinent();
                    break;
                case 3:
                    showIm();
                    break;
                case 4:
                    combine();
                    break;
                case 5:
                    finalScene();
                    break;
            }

            animationFrame = requestAnimationFrame(animate);
        }

        // Event listener
        canvas.addEventListener('click', () => {
            if (stage < totalStages - 1) {
                stage++;
                alpha = 0; // Reset alpha for next stage transitions
            } else {
                // Reset animation
                stage = 0;
                alpha = 0;
                wordPos = { x: canvas.width / 2, y: canvas.height / 2 };
                imPos = { x: canvas.width / 2 - 150, y: canvas.height / 2 };
                minentPos = { x: canvas.width / 2 + 100, y: canvas.height / 2 };
                rock = { x: 540, y: 180, angle: 0, shake: 0 };
            }
        });

        // Start animation
        animate();
    </script>
</body>
</html> 