<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EJB 企业级Java构件 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .game-board {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 40px;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-text {
            font-size: 1.4rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 16px;
            border-left: 4px solid #667eea;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .option-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .option-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .option-card.correct {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            transform: scale(1.05);
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
            animation: shake 0.5s ease-in-out;
        }

        .option-letter {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 12px;
        }

        .option-text {
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .explanation {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 16px;
            padding: 30px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease-out;
        }

        .explanation.show {
            opacity: 1;
            transform: translateY(0);
        }

        .explanation h3 {
            color: #1976d2;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .bean-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .bean-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .bean-card:hover {
            transform: translateY(-5px);
        }

        .bean-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
        }

        .session-bean { background: linear-gradient(135deg, #ff9a9e, #fecfef); }
        .entity-bean { background: linear-gradient(135deg, #a8edea, #fed6e3); }
        .message-bean { background: linear-gradient(135deg, #ffecd2, #fcb69f); }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse { animation: pulse 2s infinite; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 EJB 企业级Java构件</h1>
            <p class="subtitle">通过互动游戏学习EJB的三种构件类型</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-text">
                    <strong>题目：</strong>EJB是企业级Java构件，用于开发和部署多层结构的、分布式的、面向对象的Java应用系统。其中，<span style="background: #fff3cd; padding: 2px 8px; border-radius: 4px;">（请作答此空）</span>负责完成服务端与客户端的交互；<span style="background: #d1ecf1; padding: 2px 8px; border-radius: 4px;">（实体型构件）</span>用于数据持久化来简化数据库开发工作；<span style="background: #f8d7da; padding: 2px 8px; border-radius: 4px;">（消息驱动构件）</span>主要用来处理并发和异步访问操作。
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="600" height="300"></canvas>
            </div>

            <div class="options-grid">
                <div class="option-card" data-answer="A">
                    <div class="option-letter">A</div>
                    <div class="option-text">会话型构件</div>
                </div>
                <div class="option-card" data-answer="B">
                    <div class="option-letter">B</div>
                    <div class="option-text">实体型构件</div>
                </div>
                <div class="option-card" data-answer="C">
                    <div class="option-letter">C</div>
                    <div class="option-text">COM构件</div>
                </div>
                <div class="option-card" data-answer="D">
                    <div class="option-letter">D</div>
                    <div class="option-text">消息驱动构件</div>
                </div>
            </div>

            <div class="explanation" id="explanation">
                <h3>🎉 正确答案：A - 会话型构件</h3>
                <p><strong>解析：</strong>EJB分为三种主要类型的Bean，每种都有特定的职责：</p>
                
                <div class="bean-types">
                    <div class="bean-card">
                        <div class="bean-icon session-bean">💬</div>
                        <h4>会话Bean (Session Bean)</h4>
                        <p>负责业务逻辑处理和客户端交互，可以是有状态或无状态的。每当客户端请求时，容器选择一个会话Bean为客户端服务。</p>
                    </div>
                    <div class="bean-card">
                        <div class="bean-icon entity-bean">💾</div>
                        <h4>实体Bean (Entity Bean)</h4>
                        <p>用于数据持久化，简化数据库开发工作。代表数据库中的数据，提供对象关系映射功能。</p>
                    </div>
                    <div class="bean-card">
                        <div class="bean-icon message-bean">📨</div>
                        <h4>消息驱动Bean (Message-Driven Bean)</h4>
                        <p>处理并发和异步访问操作，响应JMS消息，支持异步处理和消息队列功能。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const options = document.querySelectorAll('.option-card');
        const explanation = document.getElementById('explanation');

        let animationFrame;
        let particles = [];
        let showAnimation = false;

        // 初始化画布动画
        function initCanvas() {
            drawEJBArchitecture();
            animate();
        }

        function drawEJBArchitecture() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制客户端
            drawComponent(50, 50, 100, 60, '客户端', '#ff9a9e');
            
            // 绘制服务器端组件
            drawComponent(250, 30, 120, 50, '会话Bean', '#a8edea');
            drawComponent(250, 100, 120, 50, '实体Bean', '#ffecd2');
            drawComponent(250, 170, 120, 50, '消息驱动Bean', '#d4edda');
            
            // 绘制数据库
            drawComponent(450, 120, 100, 60, '数据库', '#f8d7da');

            // 绘制连接线
            drawArrow(150, 80, 250, 55, '#667eea');
            drawArrow(370, 125, 450, 150, '#28a745');
            
            // 添加标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '14px Arial';
            ctx.fillText('客户端交互', 160, 45);
            ctx.fillText('数据持久化', 380, 110);
        }

        function drawComponent(x, y, width, height, text, color) {
            // 绘制圆角矩形
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.roundRect(x, y, width, height, 10);
            ctx.fill();
            
            // 绘制边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制文字
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + width/2, y + height/2 + 5);
        }

        function drawArrow(x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 绘制箭头
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle - Math.PI/6), y2 - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle + Math.PI/6), y2 - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function createParticles(x, y, color) {
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 10,
                    vy: (Math.random() - 0.5) * 10,
                    life: 1,
                    color: color
                });
            }
        }

        function updateParticles() {
            particles = particles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life -= 0.02;
                particle.vy += 0.2; // 重力
                
                if (particle.life > 0) {
                    ctx.save();
                    ctx.globalAlpha = particle.life;
                    ctx.fillStyle = particle.color;
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                    return true;
                }
                return false;
            });
        }

        function animate() {
            drawEJBArchitecture();
            updateParticles();
            animationFrame = requestAnimationFrame(animate);
        }

        // 选项点击事件
        options.forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                
                // 重置所有选项
                options.forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    explanation.classList.add('show');
                    createParticles(canvas.width/2, canvas.height/2, '#4CAF50');
                    
                    // 高亮会话Bean
                    setTimeout(() => {
                        ctx.save();
                        ctx.shadowColor = '#4CAF50';
                        ctx.shadowBlur = 20;
                        drawComponent(250, 30, 120, 50, '会话Bean', '#4CAF50');
                        ctx.restore();
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    createParticles(canvas.width/2, canvas.height/2, '#f44336');
                }
            });
        });

        // 初始化
        initCanvas();

        // Canvas点击交互
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 检查点击的组件
            if (x >= 250 && x <= 370 && y >= 30 && y <= 80) {
                // 点击会话Bean
                createParticles(x, y, '#a8edea');
                showTooltip('会话Bean：处理业务逻辑，与客户端交互');
            } else if (x >= 250 && x <= 370 && y >= 100 && y <= 150) {
                // 点击实体Bean
                createParticles(x, y, '#ffecd2');
                showTooltip('实体Bean：数据持久化，简化数据库操作');
            } else if (x >= 250 && x <= 370 && y >= 170 && y <= 220) {
                // 点击消息驱动Bean
                createParticles(x, y, '#d4edda');
                showTooltip('消息驱动Bean：处理异步消息和并发操作');
            }
        });

        function showTooltip(text) {
            // 创建临时提示
            const tooltip = document.createElement('div');
            tooltip.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeInOut 2s ease-in-out;
            `;
            tooltip.textContent = text;
            document.body.appendChild(tooltip);
            
            setTimeout(() => {
                document.body.removeChild(tooltip);
            }, 2000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                50% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
