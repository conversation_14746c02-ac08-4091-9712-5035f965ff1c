<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成器模式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f4f7f9;
        }
        h1, h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .game-area {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
            flex-wrap: wrap;
        }
        .pizza-builder {
            width: 45%;
            text-align: center;
            margin-bottom: 20px;
        }
        .pizza-display {
            font-size: 24px;
            line-height: 1.5;
            border: 2px dashed #bdc3c7;
            padding: 20px;
            border-radius: 8px;
            background: #fff;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        .controls h3 {
            color: #2c3e50;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 5px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .explanation {
            background: #e9f7fd;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .code-block {
            background: #f8f9fa;
            border-left: 5px solid #2ecc71;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre;
            overflow-x: auto;
        }
        .uml-diagram {
            text-align: center;
            margin: 20px 0;
        }
        .uml-diagram img {
            max-width: 100%;
            height: auto;
        }
        .pizza-options {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        .pizza-option {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .pizza-option:hover {
            background-color: #e0e0e0;
        }
        .pizza-option.selected {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .step-display {
            margin-top: 20px;
            text-align: left;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>知识点：生成器模式 (Builder Pattern)</h1>
        
        <div class="explanation">
            <p><strong>生成器模式</strong>是一种创建型设计模式，它可以让你分步骤创建复杂对象。该模式允许你使用相同的创建代码生成不同类型和形式的对象。</p>
            <p>就像餐厅服务员（Waiter）不需要知道如何制作披萨，但可以告诉厨师（Builder）制作什么样的披萨。不同的厨师（具体生成器）可以制作不同风格的披萨，但都遵循相同的制作步骤。</p>
        </div>

        <div class="uml-diagram">
            <pre>
┌─────────┐      ┌───────────────┐
│  Waiter │─────>│ PizzaBuilder  │
└─────────┘      └───────────────┘
                         ▲
                         │
                 ┌───────┴───────┐
                 │               │
        ┌────────────────┐ ┌────────────────┐
        │ ItalianBuilder │ │ AmericanBuilder│
        └────────────────┘ └────────────────┘
            </pre>
        </div>

        <h2>小游戏：披萨制作师</h2>

        <div class="game-area">
            <div class="pizza-builder">
                <h3>🍕 披萨生成器</h3>
                <div id="pizza-display" class="pizza-display">
                    <p>请选择披萨类型开始制作</p>
                </div>
                <div class="pizza-options">
                    <div class="pizza-option" id="italian">意大利风格</div>
                    <div class="pizza-option" id="american">美式风格</div>
                </div>
            </div>
            
            <div class="pizza-builder">
                <h3>🧑‍🍳 制作过程</h3>
                <div id="steps-display" class="pizza-display step-display">
                    <p>等待选择披萨类型...</p>
                </div>
                <div class="controls">
                    <button id="prepare-dough" disabled>1. 准备面团</button>
                    <button id="add-sauce" disabled>2. 添加酱料</button>
                    <button id="add-toppings" disabled>3. 添加配料</button>
                    <button id="bake" disabled>4. 烘烤</button>
                </div>
            </div>
        </div>

        <div class="code-block">
// 披萨生成器接口
interface PizzaBuilder {
    prepareDough(): void;
    addSauce(): void;
    addToppings(): void;
    bake(): void;
}

// 意大利风格披萨生成器
class ItalianPizzaBuilder implements PizzaBuilder {
    prepareDough() { /* 准备薄脆面团 */ }
    addSauce() { /* 添加番茄酱 */ }
    addToppings() { /* 添加马苏里拉奶酪、罗勒叶 */ }
    bake() { /* 在石窑中高温快速烘烤 */ }
}

// 美式风格披萨生成器
class AmericanPizzaBuilder implements PizzaBuilder {
    prepareDough() { /* 准备厚实松软面团 */ }
    addSauce() { /* 添加番茄酱和大蒜 */ }
    addToppings() { /* 添加大量奶酪、香肠和蔬菜 */ }
    bake() { /* 在常规烤箱中中温烘烤较长时间 */ }
}

// 服务员类 - 指导生成器
class Waiter {
    private builder: PizzaBuilder;
    
    setBuilder(builder: PizzaBuilder) {
        this.builder = builder;
    }
    
    constructPizza() {
        this.builder.prepareDough();
        this.builder.addSauce();
        this.builder.addToppings();
        this.builder.bake();
    }
}

// 使用示例
const waiter = new Waiter();
const italianBuilder = new ItalianPizzaBuilder();

waiter.setBuilder(italianBuilder);  // 设置使用意大利风格生成器
waiter.constructPizza();            // 指导生成器制作披萨
        </div>

        <div class="explanation">
            <h3>生成器模式的关键点：</h3>
            <ol>
                <li><strong>分步创建</strong>：将复杂对象的创建过程分解为多个简单步骤。</li>
                <li><strong>相同的创建过程</strong>：不同的生成器遵循相同的创建步骤，但实现方式不同。</li>
                <li><strong>指导者（Director）</strong>：服务员（Waiter）类负责指导生成器完成创建过程，客户端与指导者交互而不是直接与生成器交互。</li>
                <li><strong>灵活性</strong>：可以创建不同风格的对象，而不需要修改现有代码。</li>
            </ol>
        </div>
    </div>

    <script>
        // 获取DOM元素
        const pizzaDisplay = document.getElementById('pizza-display');
        const stepsDisplay = document.getElementById('steps-display');
        const italianOption = document.getElementById('italian');
        const americanOption = document.getElementById('american');
        const prepareDoughBtn = document.getElementById('prepare-dough');
        const addSauceBtn = document.getElementById('add-sauce');
        const addToppingsBtn = document.getElementById('add-toppings');
        const bakeBtn = document.getElementById('bake');
        
        // 披萨状态
        let pizzaType = null;
        let currentStep = 0;
        
        // 披萨制作步骤
        const pizzaSteps = {
            italian: {
                dough: "准备薄脆的意式面团 🫓",
                sauce: "涂抹传统番茄酱 🍅",
                toppings: "撒上马苏里拉奶酪和新鲜罗勒叶 🧀🌿",
                bake: "在石窑中高温快速烘烤 🔥"
            },
            american: {
                dough: "准备厚实松软的美式面团 🫓",
                sauce: "涂抹浓郁的番茄酱和大蒜 🍅🧄",
                toppings: "铺上大量奶酪、香肠和蔬菜 🧀🥓🫑",
                bake: "在烤箱中中温烘烤较长时间 🔥"
            }
        };
        
        // 选择披萨类型
        italianOption.addEventListener('click', () => selectPizzaType('italian'));
        americanOption.addEventListener('click', () => selectPizzaType('american'));
        
        // 步骤按钮事件
        prepareDoughBtn.addEventListener('click', () => executeStep(1));
        addSauceBtn.addEventListener('click', () => executeStep(2));
        addToppingsBtn.addEventListener('click', () => executeStep(3));
        bakeBtn.addEventListener('click', () => executeStep(4));
        
        // 选择披萨类型
        function selectPizzaType(type) {
            pizzaType = type;
            currentStep = 0;
            
            // 更新UI
            italianOption.classList.remove('selected');
            americanOption.classList.remove('selected');
            document.getElementById(type).classList.add('selected');
            
            pizzaDisplay.innerHTML = `<p>已选择${type === 'italian' ? '意大利' : '美式'}风格披萨</p>`;
            stepsDisplay.innerHTML = `<p>准备开始制作${type === 'italian' ? '意大利' : '美式'}风格披萨...</p>`;
            
            // 启用第一个按钮
            prepareDoughBtn.disabled = false;
            addSauceBtn.disabled = true;
            addToppingsBtn.disabled = true;
            bakeBtn.disabled = true;
        }
        
        // 执行步骤
        function executeStep(step) {
            if (step !== currentStep + 1) return;
            
            currentStep = step;
            let pizzaState = "";
            let stepDescription = "";
            
            switch(step) {
                case 1: // 准备面团
                    pizzaState = "🫓";
                    stepDescription = pizzaSteps[pizzaType].dough;
                    prepareDoughBtn.disabled = true;
                    addSauceBtn.disabled = false;
                    break;
                case 2: // 添加酱料
                    pizzaState = "🫓🍅";
                    stepDescription = pizzaSteps[pizzaType].sauce;
                    addSauceBtn.disabled = true;
                    addToppingsBtn.disabled = false;
                    break;
                case 3: // 添加配料
                    pizzaState = pizzaType === 'italian' ? "🫓🍅🧀🌿" : "🫓🍅🧀🥓🫑";
                    stepDescription = pizzaSteps[pizzaType].toppings;
                    addToppingsBtn.disabled = true;
                    bakeBtn.disabled = false;
                    break;
                case 4: // 烘烤
                    pizzaState = pizzaType === 'italian' ? "🍕 (薄脆意式)" : "🍕 (厚实美式)";
                    stepDescription = pizzaSteps[pizzaType].bake;
                    bakeBtn.disabled = true;
                    
                    // 完成后允许重新选择
                    setTimeout(() => {
                        stepsDisplay.innerHTML += `<p><strong>披萨制作完成！</strong></p>`;
                    }, 1000);
                    break;
            }
            
            // 更新UI
            pizzaDisplay.innerHTML = `<p style="font-size: 40px">${pizzaState}</p>`;
            stepsDisplay.innerHTML += `<p><strong>步骤 ${step}:</strong> ${stepDescription}</p>`;
        }
    </script>

</body>
</html> 