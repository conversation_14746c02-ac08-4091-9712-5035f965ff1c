<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抽象类与接口的区别</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #3498db;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background-color: #e0e0e0;
            cursor: pointer;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            transition: background-color 0.3s;
        }
        
        .tab.active {
            background-color: #3498db;
            color: white;
        }
        
        .content-area {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 5px;
            animation: fadeIn 0.5s;
        }
        
        .content-area.active {
            display: block;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        table, th, td {
            border: 1px solid #ddd;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
        }
        
        .canvas-container {
            width: 100%;
            margin: 20px 0;
        }
        
        canvas {
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        
        .code-example {
            background-color: #f8f8f8;
            border-left: 4px solid #3498db;
            padding: 10px;
            margin: 10px 0;
            font-family: Consolas, monospace;
            overflow-x: auto;
        }
        
        .metaphor {
            background-color: #fffde7;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }
        
        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #2980b9;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .feature {
            margin: 5px 0;
            padding: 10px;
            border-radius: 5px;
            transition: transform 0.3s;
        }
        
        .feature:hover {
            transform: scale(1.02);
        }
        
        .abstract-class {
            background-color: #d4e6f1;
        }
        
        .interface {
            background-color: #d5f5e3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>抽象类与接口的区别</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('overview')">概述</div>
            <div class="tab" onclick="showTab('comparison')">对比</div>
            <div class="tab" onclick="showTab('demo')">动画演示</div>
            <div class="tab" onclick="showTab('metaphor')">形象比喻</div>
        </div>
        
        <div id="overview" class="content-area active">
            <h2>什么是抽象类和接口？</h2>
            <p>在Java面向对象编程中，抽象类和接口是两种不同的抽象机制，它们用于定义类的行为规范。</p>
            
            <div class="feature abstract-class">
                <h3>抽象类 (Abstract Class)</h3>
                <p>抽象类是一种不能被实例化的类，它可以包含抽象方法（没有实现的方法）和具体方法（有实现的方法）。</p>
            </div>
            
            <div class="feature interface">
                <h3>接口 (Interface)</h3>
                <p>接口是一种完全抽象的类型，它只定义行为但不实现行为（Java 8之前）。接口定义了一组方法，实现接口的类必须提供这些方法的具体实现。</p>
            </div>
            
            <button class="btn" onclick="showTab('comparison')">查看详细对比</button>
        </div>
        
        <div id="comparison" class="content-area">
            <h2>抽象类与接口的主要区别</h2>
            
            <table>
                <tr>
                    <th>特性</th>
                    <th>抽象类</th>
                    <th>接口</th>
                </tr>
                <tr>
                    <td>继承/实现</td>
                    <td>只能单继承</td>
                    <td>可以实现多个</td>
                </tr>
                <tr>
                    <td>构造方法</td>
                    <td>有构造方法</td>
                    <td>没有构造方法</td>
                </tr>
                <tr>
                    <td>变量</td>
                    <td>可以有实例变量</td>
                    <td>只有常量（public static final）</td>
                </tr>
                <tr>
                    <td>方法实现</td>
                    <td>可以包含非抽象方法</td>
                    <td>Java 7之前全是抽象方法，Java 8后可有默认方法和静态方法</td>
                </tr>
                <tr>
                    <td>访问修饰符</td>
                    <td>方法可以用任意修饰符</td>
                    <td>Java 8之前都是public，Java 9后支持private</td>
                </tr>
            </table>
            
            <h3>代码示例</h3>
            
            <div class="code-example">
                <p>// 抽象类示例</p>
                <p>abstract class Animal {</p>
                <p>    protected String name;</p>
                <p>    </p>
                <p>    public Animal(String name) {</p>
                <p>        this.name = name;</p>
                <p>    }</p>
                <p>    </p>
                <p>    public void eat() {</p>
                <p>        System.out.println(name + "正在吃东西");</p>
                <p>    }</p>
                <p>    </p>
                <p>    // 抽象方法</p>
                <p>    public abstract void makeSound();</p>
                <p>}</p>
            </div>
            
            <div class="code-example">
                <p>// 接口示例</p>
                <p>interface Flyable {</p>
                <p>    // 常量</p>
                <p>    int MAX_SPEED = 100;</p>
                <p>    </p>
                <p>    // 抽象方法</p>
                <p>    void fly();</p>
                <p>    </p>
                <p>    // Java 8 默认方法</p>
                <p>    default void glide() {</p>
                <p>        System.out.println("滑翔中...");</p>
                <p>    }</p>
                <p>}</p>
            </div>
            
            <button class="btn" onclick="showTab('demo')">查看动画演示</button>
        </div>
        
        <div id="demo" class="content-area">
            <h2>动画演示</h2>
            <p>点击下面的按钮，观察抽象类和接口在继承和实现方面的不同行为：</p>
            
            <div class="canvas-container">
                <canvas id="demoCanvas" width="900" height="500"></canvas>
            </div>
            
            <div>
                <button class="btn" id="showAbstractBtn">演示抽象类</button>
                <button class="btn" id="showInterfaceBtn">演示接口</button>
                <button class="btn" id="showMultipleBtn">演示多接口实现</button>
                <button class="btn" id="resetBtn">重置</button>
            </div>
        </div>
        
        <div id="metaphor" class="content-area">
            <h2>形象比喻</h2>
            
            <div class="metaphor">
                <h3>三种不同的"老师"</h3>
                <p><strong>普通类是亲爹：</strong>手把手教你怎么做，告诉你所有细节和步骤。</p>
                <p><strong>抽象类是师傅：</strong>教你一部分具体技能，另一部分只告诉你方向，让你自己去摸索完成。</p>
                <p><strong>接口是干爹：</strong>只给你一本秘籍(规范)，告诉你要达到什么目标，但具体怎么练完全靠你自己。</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="metaphorCanvas" width="900" height="300"></canvas>
            </div>
            <button class="btn" id="playMetaphorBtn">播放比喻动画</button>
        </div>
    </div>

    <script>
        // Tab切换功能
        function showTab(tabId) {
            // 隐藏所有内容区域
            const contentAreas = document.getElementsByClassName('content-area');
            for (let i = 0; i < contentAreas.length; i++) {
                contentAreas[i].classList.remove('active');
            }
            
            // 取消所有标签页的活动状态
            const tabs = document.getElementsByClassName('tab');
            for (let i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // 显示选中的内容区域
            document.getElementById(tabId).classList.add('active');
            
            // 设置选中标签页的活动状态
            const activeTabIndex = ['overview', 'comparison', 'demo', 'metaphor'].indexOf(tabId);
            tabs[activeTabIndex].classList.add('active');
        }
        
        // Canvas动画
        const canvas = document.getElementById('demoCanvas');
        const ctx = canvas.getContext('2d');
        
        // 颜色定义
        const colors = {
            abstract: '#3498db',
            interface: '#2ecc71',
            class: '#e67e22',
            arrow: '#7f8c8d',
            text: '#333333',
            background: '#f9f9f9'
        };
        
        // 清除画布
        function clearCanvas() {
            ctx.fillStyle = colors.background;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
        
        // 绘制类/接口框
        function drawBox(x, y, width, height, label, color, isAbstract) {
            // 绘制框
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            
            // 绘制标签
            ctx.fillStyle = '#fff';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + width/2, y + 30);
            
            // 如果是抽象类或接口，添加标记
            if (isAbstract) {
                ctx.font = '14px Arial';
                ctx.fillStyle = '#fff';
                ctx.fillText(isAbstract === 'abstract' ? '<<抽象类>>' : '<<接口>>', x + width/2, y + 50);
            }
        }
        
        // 绘制连接箭头
        function drawArrow(fromX, fromY, toX, toY, label) {
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.strokeStyle = colors.arrow;
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制箭头
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 15 * Math.cos(angle - Math.PI/6), toY - 15 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 15 * Math.cos(angle + Math.PI/6), toY - 15 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = colors.arrow;
            ctx.fill();
            
            // 绘制标签
            if (label) {
                ctx.fillStyle = colors.text;
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(label, (fromX + toX) / 2, (fromY + toY) / 2 - 10);
            }
        }
        
        // 演示抽象类
        function demoAbstractClass() {
            clearCanvas();
            
            // 绘制抽象类
            drawBox(350, 50, 200, 100, 'Animal', colors.abstract, 'abstract');
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('eat() { ... }', 350 + 100, 50 + 70);
            ctx.fillText('abstract makeSound()', 350 + 100, 50 + 90);
            
            // 绘制具体类
            drawBox(200, 250, 150, 80, 'Dog', colors.class);
            drawBox(450, 250, 150, 80, 'Cat', colors.class);
            
            // 绘制继承关系
            drawArrow(250 + 25, 250, 350 + 100, 150, '继承');
            drawArrow(450 + 75, 250, 350 + 100, 150, '继承');
            
            // 添加说明
            ctx.fillStyle = colors.text;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('抽象类特点：', 50, 400);
            ctx.font = '14px Arial';
            ctx.fillText('• 只能被单继承', 70, 430);
            ctx.fillText('• 可以有构造方法和实例变量', 70, 450);
            ctx.fillText('• 可以包含具体方法实现', 70, 470);
        }
        
        // 演示接口
        function demoInterface() {
            clearCanvas();
            
            // 绘制接口
            drawBox(350, 50, 200, 100, 'Flyable', colors.interface, 'interface');
            ctx.fillStyle = '#fff';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MAX_SPEED = 100', 350 + 100, 50 + 70);
            ctx.fillText('void fly()', 350 + 100, 50 + 90);
            
            // 绘制实现类
            drawBox(350, 250, 200, 80, 'Bird', colors.class);
            
            // 绘制实现关系
            drawArrow(350 + 100, 250, 350 + 100, 150, '实现');
            
            // 添加说明
            ctx.fillStyle = colors.text;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('接口特点：', 50, 400);
            ctx.font = '14px Arial';
            ctx.fillText('• 没有构造方法', 70, 430);
            ctx.fillText('• 只有常量，没有实例变量', 70, 450);
            ctx.fillText('• Java 8前全是抽象方法', 70, 470);
        }
        
        // 演示多接口实现
        function demoMultipleInterfaces() {
            clearCanvas();
            
            // 绘制多个接口
            drawBox(150, 50, 180, 80, 'Flyable', colors.interface, 'interface');
            drawBox(450, 50, 180, 80, 'Swimmable', colors.interface, 'interface');
            
            // 绘制实现类
            drawBox(300, 250, 200, 80, 'Duck', colors.class);
            
            // 绘制实现关系
            drawArrow(350 + 50, 250, 150 + 90, 130, '实现');
            drawArrow(350 + 50, 250, 450 + 90, 130, '实现');
            
            // 添加说明
            ctx.fillStyle = colors.text;
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('多接口实现：', 50, 400);
            ctx.font = '14px Arial';
            ctx.fillText('• 一个类可以实现多个接口', 70, 430);
            ctx.fillText('• 必须实现所有接口中定义的抽象方法', 70, 450);
            ctx.fillText('• 解决了Java单继承的局限性', 70, 470);
        }
        
        // 初始化事件监听
        window.onload = function() {
            clearCanvas();
            
            // 绑定按钮事件
            document.getElementById('showAbstractBtn').addEventListener('click', demoAbstractClass);
            document.getElementById('showInterfaceBtn').addEventListener('click', demoInterface);
            document.getElementById('showMultipleBtn').addEventListener('click', demoMultipleInterfaces);
            document.getElementById('resetBtn').addEventListener('click', clearCanvas);
            
            // 比喻动画
            const metaphorCanvas = document.getElementById('metaphorCanvas');
            const metaphorCtx = metaphorCanvas.getContext('2d');
            
            document.getElementById('playMetaphorBtn').addEventListener('click', function() {
                playMetaphorAnimation(metaphorCanvas, metaphorCtx);
            });
        };
        
        // 比喻动画
        function playMetaphorAnimation(canvas, ctx) {
            // 清除画布
            ctx.fillStyle = '#f9f9f9';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            const totalFrames = 180;
            let frameCount = 0;
            
            // 创建角色图像
            const normalClass = { x: 150, y: 150, color: '#e67e22', label: '普通类（亲爹）' };
            const abstractClass = { x: 450, y: 150, color: '#3498db', label: '抽象类（师傅）' };
            const interfaceClass = { x: 750, y: 150, color: '#2ecc71', label: '接口（干爹）' };
            
            // 学生/子类
            const student = { x: 450, y: 250, color: '#9b59b6', label: '子类/学生' };
            
            function drawCharacter(char) {
                ctx.fillStyle = char.color;
                ctx.beginPath();
                ctx.arc(char.x, char.y, 30, 0, Math.PI * 2);
                ctx.fill();
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(char.label, char.x, char.y + 50);
            }
            
            function drawSpeechBubble(x, y, width, height, text) {
                // 绘制气泡
                ctx.fillStyle = '#fff';
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 1;
                
                ctx.beginPath();
                ctx.moveTo(x, y);
                ctx.lineTo(x - 10, y + 10);
                ctx.lineTo(x, y + 20);
                ctx.lineTo(x + width, y + 20);
                ctx.lineTo(x + width, y - height);
                ctx.lineTo(x, y - height);
                ctx.closePath();
                
                ctx.fill();
                ctx.stroke();
                
                // 绘制文本
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(text, x + width/2, y - height/2);
            }
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制三个角色
                drawCharacter(normalClass);
                drawCharacter(abstractClass);
                drawCharacter(interfaceClass);
                drawCharacter(student);
                
                // 根据帧数绘制不同场景
                if (frameCount < 60) {
                    // 第一阶段：普通类场景
                    drawSpeechBubble(normalClass.x + 40, normalClass.y - 20, 150, 40, "我手把手教你具体怎么做");
                    
                    // 连接线
                    ctx.beginPath();
                    ctx.moveTo(normalClass.x, normalClass.y + 30);
                    ctx.lineTo(student.x - 30, student.y - 20);
                    ctx.strokeStyle = normalClass.color;
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    
                } else if (frameCount < 120) {
                    // 第二阶段：抽象类场景
                    drawSpeechBubble(abstractClass.x + 40, abstractClass.y - 20, 150, 40, "我教你一部分，其他靠你自己");
                    
                    // 连接线
                    ctx.beginPath();
                    ctx.moveTo(abstractClass.x, abstractClass.y + 30);
                    ctx.lineTo(student.x, student.y - 30);
                    ctx.strokeStyle = abstractClass.color;
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    
                } else {
                    // 第三阶段：接口场景
                    drawSpeechBubble(interfaceClass.x + 40, interfaceClass.y - 20, 150, 40, "这是规范，自己去实现吧");
                    
                    // 连接线
                    ctx.beginPath();
                    ctx.moveTo(interfaceClass.x, interfaceClass.y + 30);
                    ctx.lineTo(student.x + 30, student.y - 20);
                    ctx.strokeStyle = interfaceClass.color;
                    ctx.lineWidth = 2;
                    ctx.stroke();
                }
                
                frameCount++;
                if (frameCount < totalFrames) {
                    requestAnimationFrame(animate);
                }
            }
            
            animate();
        }
    </script>
</body>
</html> 