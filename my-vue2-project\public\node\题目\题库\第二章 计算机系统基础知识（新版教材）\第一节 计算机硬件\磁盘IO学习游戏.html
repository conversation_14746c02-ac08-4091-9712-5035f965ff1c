<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>磁盘I/O时间计算 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .game-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .game-section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8em;
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .disk-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            position: relative;
        }

        .disk {
            width: 400px;
            height: 400px;
            border: 8px solid #4a5568;
            border-radius: 50%;
            position: relative;
            background: radial-gradient(circle, #f7fafc 0%, #e2e8f0 100%);
            animation: rotate 4s linear infinite;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }

        .disk.paused {
            animation-play-state: paused;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .track {
            position: absolute;
            border: 2px dashed #cbd5e0;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .track-1 {
            width: 300px;
            height: 300px;
        }

        .record {
            position: absolute;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            color: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .record:hover {
            transform: scale(1.2);
            z-index: 10;
        }

        .record.active {
            animation: pulse 1s infinite;
            box-shadow: 0 0 20px rgba(255,255,255,0.8);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .head {
            position: absolute;
            width: 60px;
            height: 8px;
            background: #e53e3e;
            top: 50%;
            left: 50%;
            transform-origin: left center;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(229,62,62,0.5);
            z-index: 20;
        }

        .head::after {
            content: '磁头';
            position: absolute;
            right: -40px;
            top: -20px;
            font-size: 12px;
            color: #e53e3e;
            font-weight: bold;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .info-panel {
            background: #f7fafc;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }

        .data-table tr:hover {
            background: #f7fafc;
        }

        .highlight {
            background: #fef5e7 !important;
            animation: highlight 2s ease;
        }

        @keyframes highlight {
            0%, 100% { background: #fef5e7; }
            50% { background: #fed7aa; }
        }

        .explanation {
            background: #e6fffa;
            border: 2px solid #4fd1c7;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .explanation::before {
            content: '💡';
            position: absolute;
            top: -15px;
            left: 20px;
            background: #4fd1c7;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 20px;
        }

        .formula {
            background: #fff5f5;
            border: 2px solid #fc8181;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            text-align: center;
            font-size: 1.1em;
        }

        @media (max-width: 768px) {
            .disk {
                width: 300px;
                height: 300px;
            }
            
            .track-1 {
                width: 220px;
                height: 220px;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 磁盘I/O时间计算游戏</h1>
            <p>通过动画和交互学习磁盘存储优化原理</p>
        </div>

        <!-- 题目展示 -->
        <div class="game-section">
            <h2 class="section-title">📚 题目理解</h2>
            <div class="info-panel">
                <h3>题目条件：</h3>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>每个磁道划分成 <strong>10个物理块</strong></li>
                    <li>每块存放 <strong>1个逻辑记录</strong></li>
                    <li>磁盘旋转速度：<strong>20ms/周</strong></li>
                    <li>每个记录处理时间：<strong>4ms</strong></li>
                    <li>使用 <strong>单缓冲区</strong></li>
                </ul>
            </div>
            
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>物理块</th>
                            <th>1</th><th>2</th><th>3</th><th>4</th><th>5</th>
                            <th>6</th><th>7</th><th>8</th><th>9</th><th>10</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>逻辑记录</strong></td>
                            <td>R1</td><td>R2</td><td>R3</td><td>R4</td><td>R5</td>
                            <td>R6</td><td>R7</td><td>R8</td><td>R9</td><td>R10</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 磁盘动画演示 -->
        <div class="game-section">
            <h2 class="section-title">🎯 磁盘工作原理演示</h2>
            
            <div class="disk-container">
                <div class="disk" id="disk">
                    <div class="track track-1"></div>
                    <div class="head" id="head"></div>
                    <!-- 记录位置将通过JavaScript动态生成 -->
                </div>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="startDemo()">▶️ 开始演示</button>
                <button class="btn btn-secondary" onclick="pauseDemo()">⏸️ 暂停</button>
                <button class="btn btn-primary" onclick="resetDemo()">🔄 重置</button>
                <button class="btn btn-secondary" onclick="optimizeLayout()">✨ 优化布局</button>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value" id="currentTime">0</div>
                    <div class="stat-label">当前时间 (ms)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="processedRecords">0</div>
                    <div class="stat-label">已处理记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalTime">0</div>
                    <div class="stat-label">总时间 (ms)</div>
                </div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 计算过程详解 -->
        <div class="game-section">
            <h2 class="section-title">🧮 计算过程详解</h2>
            
            <div class="explanation">
                <h3>基础计算：</h3>
                <div class="formula">
                    读取每个记录时间 = 20ms ÷ 10 = 2ms
                </div>
                <div class="formula">
                    处理每个记录时间 = 4ms
                </div>
                <div class="formula">
                    读取+处理时间 = 2ms + 4ms = 6ms
                </div>
            </div>

            <div class="explanation">
                <h3>问题分析：</h3>
                <p>当处理完R1后，磁头已经转到了R4的位置！要读取R2，需要等待磁盘转动，经过8个记录位置才能到达R2。</p>
                <div class="formula">
                    等待时间 = 8 × 2ms = 16ms
                </div>
                <div class="formula">
                    R2总处理时间 = 16ms + 2ms + 4ms = 22ms
                </div>
            </div>

            <div class="explanation">
                <h3>最终答案：</h3>
                <div class="formula">
                    原始布局总时间 = 6ms + 22ms × 9 = 204ms
                </div>
                <div class="formula">
                    优化布局总时间 = 6ms × 10 = 60ms
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            isRunning: false,
            currentRecord: 0,
            currentTime: 0,
            processedRecords: 0,
            isOptimized: false,
            animationId: null
        };

        // 记录配置
        const originalLayout = ['R1', 'R2', 'R3', 'R4', 'R5', 'R6', 'R7', 'R8', 'R9', 'R10'];
        const optimizedLayout = ['R1', 'R4', 'R7', 'R10', 'R3', 'R6', 'R9', 'R2', 'R5', 'R8'];
        
        // 颜色配置
        const colors = [
            '#e53e3e', '#dd6b20', '#d69e2e', '#38a169', '#319795',
            '#3182ce', '#553c9a', '#805ad5', '#d53f8c', '#e53e3e'
        ];

        // 初始化磁盘
        function initializeDisk() {
            const disk = document.getElementById('disk');
            const records = disk.querySelectorAll('.record');
            records.forEach(record => record.remove());

            const layout = gameState.isOptimized ? optimizedLayout : originalLayout;
            
            for (let i = 0; i < 10; i++) {
                const angle = (i * 36) - 90; // 从顶部开始
                const record = document.createElement('div');
                record.className = 'record';
                record.id = `record-${i}`;
                record.textContent = layout[i];
                record.style.background = colors[i];
                
                // 计算位置
                const radius = 150; // 轨道半径
                const x = Math.cos(angle * Math.PI / 180) * radius;
                const y = Math.sin(angle * Math.PI / 180) * radius;
                
                record.style.left = `calc(50% + ${x}px - 15px)`;
                record.style.top = `calc(50% + ${y}px - 15px)`;
                
                record.addEventListener('click', () => highlightRecord(i));
                disk.appendChild(record);
            }
        }

        // 高亮记录
        function highlightRecord(index) {
            const records = document.querySelectorAll('.record');
            records.forEach(r => r.classList.remove('active'));
            document.getElementById(`record-${index}`).classList.add('active');
        }

        // 开始演示
        function startDemo() {
            if (gameState.isRunning) return;
            
            gameState.isRunning = true;
            gameState.currentRecord = 0;
            gameState.currentTime = 0;
            gameState.processedRecords = 0;
            
            document.getElementById('disk').classList.remove('paused');
            simulateProcessing();
        }

        // 暂停演示
        function pauseDemo() {
            gameState.isRunning = false;
            document.getElementById('disk').classList.add('paused');
            if (gameState.animationId) {
                clearTimeout(gameState.animationId);
            }
        }

        // 重置演示
        function resetDemo() {
            pauseDemo();
            gameState.currentRecord = 0;
            gameState.currentTime = 0;
            gameState.processedRecords = 0;
            updateStats();
            
            const records = document.querySelectorAll('.record');
            records.forEach(r => r.classList.remove('active'));
        }

        // 优化布局
        function optimizeLayout() {
            gameState.isOptimized = !gameState.isOptimized;
            resetDemo();
            initializeDisk();
            
            const btn = event.target;
            btn.textContent = gameState.isOptimized ? '📋 原始布局' : '✨ 优化布局';
            btn.style.background = gameState.isOptimized ? 
                'linear-gradient(135deg, #e53e3e 0%, #c53030 100%)' : 
                'linear-gradient(135deg, #48bb78 0%, #38a169 100%)';
        }

        // 模拟处理过程
        function simulateProcessing() {
            if (!gameState.isRunning || gameState.processedRecords >= 10) {
                gameState.isRunning = false;
                return;
            }

            highlightRecord(gameState.currentRecord);
            
            // 计算处理时间
            let processingTime;
            if (gameState.processedRecords === 0) {
                // 第一个记录
                processingTime = 6; // 2ms读取 + 4ms处理
            } else {
                if (gameState.isOptimized) {
                    processingTime = 6; // 优化后每个记录都是6ms
                } else {
                    processingTime = 22; // 16ms等待 + 2ms读取 + 4ms处理
                }
            }

            // 动画更新时间
            animateTimeUpdate(processingTime, () => {
                gameState.processedRecords++;
                gameState.currentRecord = (gameState.currentRecord + 1) % 10;
                updateStats();
                
                gameState.animationId = setTimeout(() => {
                    simulateProcessing();
                }, 500);
            });
        }

        // 动画更新时间
        function animateTimeUpdate(duration, callback) {
            const startTime = gameState.currentTime;
            const endTime = startTime + duration;
            const animationDuration = 1000; // 1秒动画
            const startTimestamp = Date.now();

            function animate() {
                const elapsed = Date.now() - startTimestamp;
                const progress = Math.min(elapsed / animationDuration, 1);
                
                gameState.currentTime = startTime + (duration * progress);
                updateStats();
                
                if (progress < 1 && gameState.isRunning) {
                    requestAnimationFrame(animate);
                } else {
                    gameState.currentTime = endTime;
                    updateStats();
                    if (callback) callback();
                }
            }
            
            animate();
        }

        // 更新统计信息
        function updateStats() {
            document.getElementById('currentTime').textContent = Math.round(gameState.currentTime);
            document.getElementById('processedRecords').textContent = gameState.processedRecords;
            
            let totalTime;
            if (gameState.isOptimized) {
                totalTime = 60;
            } else {
                totalTime = 204;
            }
            document.getElementById('totalTime').textContent = totalTime;
            
            const progress = (gameState.processedRecords / 10) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDisk();
            updateStats();
            
            // 添加一些交互提示
            setTimeout(() => {
                alert('🎮 欢迎来到磁盘I/O学习游戏！\n\n点击"开始演示"观看磁盘工作过程\n点击"优化布局"看看如何提升效率\n点击磁盘上的记录可以高亮显示');
            }, 1000);
        });
    </script>
</body>
</html>
