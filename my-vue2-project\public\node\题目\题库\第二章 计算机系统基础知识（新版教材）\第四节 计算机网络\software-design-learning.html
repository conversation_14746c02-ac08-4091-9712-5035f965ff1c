<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件结构化设计学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .question-box h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d4aa;
            background: rgba(0, 212, 170, 0.3);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.3);
            animation: shake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            position: relative;
        }

        canvas {
            width: 100%;
            height: 500px;
            border-radius: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.active {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            line-height: 1.6;
        }

        .design-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .design-card {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .design-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(108, 92, 231, 0.3);
        }

        .design-card.architecture {
            background: linear-gradient(135deg, #fd79a8, #e84393);
        }

        .design-card.interface {
            background: linear-gradient(135deg, #00d4aa, #01a3a4);
        }

        .design-card.data {
            background: linear-gradient(135deg, #fdcb6e, #e17055);
        }

        .design-card.process {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
        }

        .design-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .design-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .design-number {
            position: absolute;
            top: 10px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 3px 8px;
            border-radius: 5px;
            color: #2d3436;
            font-weight: bold;
        }

        .step {
            background: rgba(116, 185, 255, 0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateX(5px);
        }

        .building-metaphor {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .building-part {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .building-part:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .building-part .emoji {
            font-size: 3rem;
            margin-bottom: 10px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #01a3a4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .design-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .concept-box {
            background: linear-gradient(135deg, #a29bfe, #6c5ce7);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
        }

        .concept-box h4 {
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .flow-diagram {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .flow-step {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            flex: 1;
            margin: 10px;
            min-width: 150px;
            position: relative;
        }

        .flow-step::after {
            content: '→';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            color: #74b9ff;
        }

        .flow-step:last-child::after {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ 软件结构化设计学习</h1>
            <p>探索软件设计的四大核心任务</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2>📝 考试题目</h2>
                <p><strong>软件结构化设计包括（ ）等任务。</strong></p>
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 架构设计、数据设计、过程设计、原型设计
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 架构设计、过程设计、程序设计、原型设计
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 数据设计、过程设计、交互设计、程序设计
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 架构设计、接口设计、数据设计、过程设计
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 什么是软件结构化设计？</h2>
            <div class="explanation">
                <p><span class="highlight">软件结构化设计</span>是一种面向数据流的设计方法，以结构化分析的成果为基础，采用<strong>自顶而下、逐步求精、模块化</strong>的设计过程。</p>
                <p>🔑 <strong>核心理念：</strong>就像建造一座大楼，需要有清晰的架构图纸、详细的施工计划和标准化的建造流程。</p>
            </div>
            
            <div class="building-metaphor">
                <div class="building-part">
                    <div class="emoji">🏗️</div>
                    <h4>架构设计</h4>
                    <p>建筑的整体结构</p>
                </div>
                <div class="building-part">
                    <div class="emoji">🔌</div>
                    <h4>接口设计</h4>
                    <p>房间之间的门窗</p>
                </div>
                <div class="building-part">
                    <div class="emoji">🗄️</div>
                    <h4>数据设计</h4>
                    <p>房间内的家具布局</p>
                </div>
                <div class="building-part">
                    <div class="emoji">⚙️</div>
                    <h4>过程设计</h4>
                    <p>房间的使用流程</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 软件结构化设计的四大任务</h2>
            <div class="design-grid">
                <div class="design-card architecture" onclick="showDesignDemo('architecture')">
                    <div class="design-number">1</div>
                    <span class="icon">🏗️</span>
                    <h3>架构设计</h3>
                    <p>定义系统整体结构</p>
                    <p>模块划分和组织</p>
                </div>
                <div class="design-card interface" onclick="showDesignDemo('interface')">
                    <div class="design-number">2</div>
                    <span class="icon">🔌</span>
                    <h3>接口设计</h3>
                    <p>定义模块间接口</p>
                    <p>数据传递规范</p>
                </div>
                <div class="design-card data" onclick="showDesignDemo('data')">
                    <div class="design-number">3</div>
                    <span class="icon">🗄️</span>
                    <h3>数据设计</h3>
                    <p>设计数据结构</p>
                    <p>数据库设计</p>
                </div>
                <div class="design-card process" onclick="showDesignDemo('process')">
                    <div class="design-number">4</div>
                    <span class="icon">⚙️</span>
                    <h3>过程设计</h3>
                    <p>设计算法流程</p>
                    <p>处理逻辑设计</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎬 软件设计过程动画演示</h2>
            <div class="canvas-container">
                <canvas id="designCanvas"></canvas>
                <div class="design-indicator" id="designIndicator">
                    🎯 点击按钮开始演示
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startFullDemo()">🎬 完整设计流程</button>
                <button class="btn" onclick="showStructuredApproach()">📊 结构化方法</button>
                <button class="btn" onclick="showTaskComparison()">🔍 任务对比</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2>🔍 详细解析每个选项</h2>

            <div class="step">
                <h3>选项A：架构设计、数据设计、过程设计、原型设计 ❌</h3>
                <p><strong>错误原因：</strong>包含了<span class="highlight">原型设计</span>，缺少了<span class="highlight">接口设计</span></p>
                <ul>
                    <li>❌ <strong>原型设计：</strong>属于需求分析阶段，不是结构化设计的核心任务</li>
                    <li>✅ <strong>正确部分：</strong>架构设计、数据设计、过程设计都是正确的</li>
                    <li>❌ <strong>缺失部分：</strong>接口设计是结构化设计的重要组成部分</li>
                    <li>🔍 <strong>原型设计作用：</strong>用于验证需求，不是设计阶段的核心任务</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项B：架构设计、过程设计、程序设计、原型设计 ❌</h3>
                <p><strong>错误原因：</strong>包含了<span class="highlight">程序设计和原型设计</span>，缺少了<span class="highlight">接口设计和数据设计</span></p>
                <ul>
                    <li>❌ <strong>程序设计：</strong>属于编码实现阶段，不是设计阶段</li>
                    <li>❌ <strong>原型设计：</strong>属于需求分析阶段</li>
                    <li>✅ <strong>正确部分：</strong>架构设计、过程设计是正确的</li>
                    <li>❌ <strong>缺失部分：</strong>接口设计和数据设计都是核心任务</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项C：数据设计、过程设计、交互设计、程序设计 ❌</h3>
                <p><strong>错误原因：</strong>包含了<span class="highlight">交互设计和程序设计</span>，缺少了<span class="highlight">架构设计和接口设计</span></p>
                <ul>
                    <li>❌ <strong>交互设计：</strong>属于用户界面设计，不是结构化设计的核心</li>
                    <li>❌ <strong>程序设计：</strong>属于编码实现阶段</li>
                    <li>✅ <strong>正确部分：</strong>数据设计、过程设计是正确的</li>
                    <li>❌ <strong>缺失部分：</strong>架构设计和接口设计是最重要的任务</li>
                </ul>
            </div>

            <div class="step">
                <h3>选项D：架构设计、接口设计、数据设计、过程设计 ✅</h3>
                <p><strong>正确答案！</strong>这正是<span class="highlight">软件结构化设计</span>的四大核心任务</p>
                <ul>
                    <li>🏗️ <strong>架构设计：</strong>定义系统整体结构和模块划分</li>
                    <li>🔌 <strong>接口设计：</strong>定义模块间的接口和数据传递规范</li>
                    <li>🗄️ <strong>数据设计：</strong>设计数据结构和数据库</li>
                    <li>⚙️ <strong>过程设计：</strong>设计算法流程和处理逻辑</li>
                </ul>
                <div class="concept-box">
                    <h4>🔑 关键理解</h4>
                    <p>这四个任务构成了结构化设计的完整体系，缺一不可，共同确保软件系统的质量和可维护性。</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🧠 软件结构化设计深度解析</h2>

            <div class="concept-box">
                <h4>🎯 结构化设计的核心理念</h4>
                <p><strong>面向数据流：</strong>以数据流图为基础进行设计</p>
                <p><strong>自顶而下：</strong>从整体到局部，逐步细化</p>
                <p><strong>逐步求精：</strong>每一层都比上一层更加详细</p>
                <p><strong>模块化：</strong>将系统分解为独立的功能模块</p>
            </div>

            <div class="flow-diagram">
                <div class="flow-step">
                    <h4>🏗️ 架构设计</h4>
                    <p>系统分解</p>
                    <p>模块划分</p>
                </div>
                <div class="flow-step">
                    <h4>🔌 接口设计</h4>
                    <p>模块连接</p>
                    <p>数据传递</p>
                </div>
                <div class="flow-step">
                    <h4>🗄️ 数据设计</h4>
                    <p>数据结构</p>
                    <p>存储设计</p>
                </div>
                <div class="flow-step">
                    <h4>⚙️ 过程设计</h4>
                    <p>算法设计</p>
                    <p>流程控制</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 四大设计任务详细对比</h2>

            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>设计任务</th>
                        <th>主要内容</th>
                        <th>设计重点</th>
                        <th>输出成果</th>
                    </tr>
                </thead>
                <tbody>
                    <tr style="background: rgba(253, 121, 168, 0.1);">
                        <td><strong>架构设计</strong></td>
                        <td>系统整体结构，模块划分</td>
                        <td>系统分解，模块组织</td>
                        <td>系统架构图，模块结构图</td>
                    </tr>
                    <tr style="background: rgba(0, 212, 170, 0.1);">
                        <td><strong>接口设计</strong></td>
                        <td>模块间接口，数据传递</td>
                        <td>接口规范，通信协议</td>
                        <td>接口说明书，API文档</td>
                    </tr>
                    <tr style="background: rgba(253, 203, 110, 0.1);">
                        <td><strong>数据设计</strong></td>
                        <td>数据结构，数据库设计</td>
                        <td>数据组织，存储优化</td>
                        <td>数据字典，数据库设计</td>
                    </tr>
                    <tr style="background: rgba(116, 185, 255, 0.1);">
                        <td><strong>过程设计</strong></td>
                        <td>算法流程，处理逻辑</td>
                        <td>算法效率，逻辑正确性</td>
                        <td>流程图，伪代码</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🏗️ 建筑类比理解</h2>
            <div class="explanation">
                <p>软件结构化设计就像建造一座大楼，需要有序的规划和设计：</p>
            </div>

            <div class="building-metaphor">
                <div class="building-part">
                    <div class="emoji">🏗️</div>
                    <h4>架构设计 = 建筑设计</h4>
                    <p>确定大楼的整体结构</p>
                    <p>楼层分布和功能划分</p>
                </div>
                <div class="building-part">
                    <div class="emoji">🔌</div>
                    <h4>接口设计 = 门窗设计</h4>
                    <p>房间之间的连接方式</p>
                    <p>通道和出入口规划</p>
                </div>
                <div class="building-part">
                    <div class="emoji">🗄️</div>
                    <h4>数据设计 = 家具布局</h4>
                    <p>房间内的家具摆放</p>
                    <p>存储空间的规划</p>
                </div>
                <div class="building-part">
                    <div class="emoji">⚙️</div>
                    <h4>过程设计 = 使用流程</h4>
                    <p>房间的使用方式</p>
                    <p>人员活动的流程</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 记忆技巧和考试要点</h2>

            <div class="explanation">
                <h3>🧠 记忆口诀</h3>
                <p style="font-size: 1.3rem; text-align: center; font-weight: bold; margin: 20px 0;">
                    "架构接口数据过程，结构设计四大任务，<br>
                    自顶而下逐步精，模块化是好方法"
                </p>

                <h3>🔑 关键词记忆法</h3>
                <p><strong>架构设计：</strong>"整体结构"、"模块划分"、"系统分解"</p>
                <p><strong>接口设计：</strong>"模块连接"、"数据传递"、"通信协议"</p>
                <p><strong>数据设计：</strong>"数据结构"、"数据库"、"存储设计"</p>
                <p><strong>过程设计：</strong>"算法流程"、"处理逻辑"、"控制结构"</p>

                <h3>🎯 考试技巧</h3>
                <ul>
                    <li>看到"结构化设计任务" → <span class="highlight">架构、接口、数据、过程</span></li>
                    <li>排除"原型设计"、"程序设计"、"交互设计" → <span class="highlight">不是核心任务</span></li>
                    <li>记住四个任务的<span class="highlight">英文首字母：AIDP</span></li>
                    <li>理解<span class="highlight">结构化方法的特点</span>：自顶而下、逐步求精、模块化</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎉 学习总结</h2>
            <div class="explanation">
                <h3>📚 核心知识点</h3>
                <ul>
                    <li><span class="highlight">软件结构化设计</span>：面向数据流的设计方法</li>
                    <li><span class="highlight">四大任务</span>：架构设计、接口设计、数据设计、过程设计</li>
                    <li><span class="highlight">设计原则</span>：自顶而下、逐步求精、模块化</li>
                    <li><span class="highlight">设计基础</span>：以结构化分析的成果为基础</li>
                </ul>

                <h3>⚡ 实际应用</h3>
                <ul>
                    <li>企业管理系统的模块化设计</li>
                    <li>电商平台的分层架构设计</li>
                    <li>数据库管理系统的结构设计</li>
                    <li>嵌入式系统的功能模块设计</li>
                </ul>
            </div>

            <div class="controls">
                <button class="btn" onclick="reviewQuestion()">🔄 重新答题</button>
                <button class="btn" onclick="showSummary()">📋 显示总结</button>
            </div>
        </div>
    </div>

    <script>
        // Canvas相关变量
        const canvas = document.getElementById('designCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;
        let currentDemo = 'none';

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 题目交互逻辑
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'D') {
                    this.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n解释：软件结构化设计包括架构设计、接口设计、数据设计和过程设计四个核心任务。这是结构化设计方法的标准组成部分。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    progressFill.style.width = '25%';
                    setTimeout(() => {
                        let hint = '';
                        switch(answer) {
                            case 'A':
                                hint = '原型设计不是结构化设计的核心任务，缺少了接口设计。';
                                break;
                            case 'B':
                                hint = '程序设计和原型设计不是结构化设计的核心任务，缺少了接口设计和数据设计。';
                                break;
                            case 'C':
                                hint = '交互设计和程序设计不是结构化设计的核心任务，缺少了架构设计和接口设计。';
                                break;
                        }
                        alert('❌ 答案不正确！\n\n提示：' + hint + '\n\n记住：结构化设计的四大任务是架构、接口、数据、过程！');
                    }, 500);
                }
            });
        });

        // 绘制设计任务图标
        function drawTaskIcon(x, y, task, active = false, size = 40) {
            ctx.save();

            const colors = {
                'architecture': '#fd79a8',
                'interface': '#00d4aa',
                'data': '#fdcb6e',
                'process': '#74b9ff'
            };

            const icons = {
                'architecture': '🏗️',
                'interface': '🔌',
                'data': '🗄️',
                'process': '⚙️'
            };

            // 背景圆圈
            ctx.fillStyle = active ? colors[task] : '#bdc3c7';
            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fill();

            if (active) {
                // 发光效果
                ctx.shadowColor = colors[task];
                ctx.shadowBlur = 20;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;
            }

            // 图标
            ctx.font = `${size * 0.6}px Microsoft YaHei`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(icons[task], x, y);

            ctx.restore();
        }

        // 绘制连接线
        function drawConnection(fromX, fromY, toX, toY, active = false) {
            ctx.save();

            ctx.strokeStyle = active ? '#667eea' : '#bdc3c7';
            ctx.lineWidth = active ? 4 : 2;

            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            ctx.restore();
        }

        // 绘制模块
        function drawModule(x, y, width, height, label, active = false) {
            ctx.save();

            ctx.fillStyle = active ? '#667eea' : '#f8f9fa';
            ctx.strokeStyle = active ? '#667eea' : '#bdc3c7';
            ctx.lineWidth = 2;

            ctx.fillRect(x - width/2, y - height/2, width, height);
            ctx.strokeRect(x - width/2, y - height/2, width, height);

            // 标签
            ctx.fillStyle = active ? '#fff' : '#2c3e50';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 4);

            ctx.restore();
        }

        // 完整设计流程演示
        function startFullDemo() {
            currentDemo = 'full';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);
            animateFullDesign();
        }

        function animateFullDesign() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;
            const spacing = 150;

            const tasks = ['architecture', 'interface', 'data', 'process'];
            const taskNames = ['架构设计', '接口设计', '数据设计', '过程设计'];

            const step = Math.floor(animationStep / 60);
            const currentTask = Math.min(step, tasks.length - 1);

            // 绘制所有任务
            tasks.forEach((task, index) => {
                const x = centerX - spacing * 1.5 + index * spacing;
                const y = centerY;
                const active = index <= currentTask;

                drawTaskIcon(x, y, task, active);

                // 任务名称
                ctx.fillStyle = active ? '#2c3e50' : '#bdc3c7';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(taskNames[index], x, y + 70);

                // 连接线
                if (index < tasks.length - 1) {
                    drawConnection(x + 40, y, x + spacing - 40, y, index < currentTask);
                }
            });

            // 当前任务说明
            const designIndicator = document.getElementById('designIndicator');
            if (step < tasks.length) {
                const descriptions = [
                    '🏗️ 架构设计：定义系统整体结构和模块划分',
                    '🔌 接口设计：定义模块间的接口和数据传递',
                    '🗄️ 数据设计：设计数据结构和数据库',
                    '⚙️ 过程设计：设计算法流程和处理逻辑'
                ];
                designIndicator.textContent = descriptions[step];
            } else {
                designIndicator.textContent = '✅ 软件结构化设计完成！';
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateFullDesign);
        }

        // 结构化方法演示
        function showStructuredApproach() {
            currentDemo = 'structured';
            animationStep = 0;
            if (animationId) cancelAnimationFrame(animationId);

            const designIndicator = document.getElementById('designIndicator');
            designIndicator.textContent = '📊 结构化设计方法：自顶而下、逐步求精';

            animateStructuredApproach();
        }

        function animateStructuredApproach() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            const step = Math.floor(animationStep / 40);

            // 绘制层次结构
            if (step >= 0) {
                // 顶层模块
                drawModule(centerX, centerY - 120, 120, 40, '主系统', true);
            }

            if (step >= 1) {
                // 第二层模块
                drawModule(centerX - 100, centerY - 40, 80, 30, '模块A', true);
                drawModule(centerX + 100, centerY - 40, 80, 30, '模块B', true);

                // 连接线
                drawConnection(centerX, centerY - 100, centerX - 100, centerY - 25, true);
                drawConnection(centerX, centerY - 100, centerX + 100, centerY - 25, true);
            }

            if (step >= 2) {
                // 第三层模块
                drawModule(centerX - 150, centerY + 40, 60, 25, '子模块A1', true);
                drawModule(centerX - 50, centerY + 40, 60, 25, '子模块A2', true);
                drawModule(centerX + 50, centerY + 40, 60, 25, '子模块B1', true);
                drawModule(centerX + 150, centerY + 40, 60, 25, '子模块B2', true);

                // 连接线
                drawConnection(centerX - 100, centerY - 25, centerX - 150, centerY + 27, true);
                drawConnection(centerX - 100, centerY - 25, centerX - 50, centerY + 27, true);
                drawConnection(centerX + 100, centerY - 25, centerX + 50, centerY + 27, true);
                drawConnection(centerX + 100, centerY - 25, centerX + 150, centerY + 27, true);
            }

            // 说明文字
            if (step >= 3) {
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('自顶而下，逐步求精的模块化设计', centerX, centerY + 120);
                return;
            }

            animationStep++;
            animationId = requestAnimationFrame(animateStructuredApproach);
        }

        // 任务对比演示
        function showTaskComparison() {
            currentDemo = 'comparison';
            if (animationId) cancelAnimationFrame(animationId);

            const designIndicator = document.getElementById('designIndicator');
            designIndicator.textContent = '🔍 四大设计任务详细对比';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            // 绘制四个象限
            const tasks = [
                { name: '架构设计', color: '#fd79a8', pos: {x: centerX - 120, y: centerY - 80} },
                { name: '接口设计', color: '#00d4aa', pos: {x: centerX + 120, y: centerY - 80} },
                { name: '数据设计', color: '#fdcb6e', pos: {x: centerX - 120, y: centerY + 80} },
                { name: '过程设计', color: '#74b9ff', pos: {x: centerX + 120, y: centerY + 80} }
            ];

            tasks.forEach((task, index) => {
                // 背景区域
                ctx.fillStyle = task.color + '20';
                ctx.fillRect(task.pos.x - 80, task.pos.y - 50, 160, 100);

                // 边框
                ctx.strokeStyle = task.color;
                ctx.lineWidth = 2;
                ctx.strokeRect(task.pos.x - 80, task.pos.y - 50, 160, 100);

                // 任务名称
                ctx.fillStyle = task.color;
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(task.name, task.pos.x, task.pos.y - 20);

                // 任务描述
                ctx.fillStyle = '#2c3e50';
                ctx.font = '12px Microsoft YaHei';
                const descriptions = [
                    '系统整体结构',
                    '模块间接口',
                    '数据结构设计',
                    '算法流程设计'
                ];
                ctx.fillText(descriptions[index], task.pos.x, task.pos.y + 10);
            });

            // 中心连接
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX - 40, centerY);
            ctx.lineTo(centerX + 40, centerY);
            ctx.moveTo(centerX, centerY - 40);
            ctx.lineTo(centerX, centerY + 40);
            ctx.stroke();

            // 中心文字
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('结构化设计', centerX, centerY + 5);
        }

        // 设计任务演示
        function showDesignDemo(task) {
            currentDemo = task;
            if (animationId) cancelAnimationFrame(animationId);

            const designIndicator = document.getElementById('designIndicator');
            const descriptions = {
                'architecture': '🏗️ 架构设计：定义系统整体结构和模块划分',
                'interface': '🔌 接口设计：定义模块间接口和数据传递规范',
                'data': '🗄️ 数据设计：设计数据结构和数据库',
                'process': '⚙️ 过程设计：设计算法流程和处理逻辑'
            };
            designIndicator.textContent = descriptions[task];

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            drawTaskIcon(centerX, centerY, task, true, 80);

            ctx.fillStyle = '#2c3e50';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(descriptions[task], centerX, centerY + 120);
        }

        // 重置演示
        function resetDemo() {
            if (animationId) cancelAnimationFrame(animationId);
            currentDemo = 'none';
            animationStep = 0;

            const designIndicator = document.getElementById('designIndicator');
            designIndicator.textContent = '🎯 点击按钮开始演示';

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2 / window.devicePixelRatio;
            const centerY = canvas.height / 2 / window.devicePixelRatio;

            ctx.fillStyle = '#2c3e50';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('选择上方按钮查看软件设计过程演示', centerX, centerY);
        }

        // 重新答题功能
        function reviewQuestion() {
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            document.getElementById('progressFill').style.width = '0%';

            document.querySelector('.question-box').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            setTimeout(() => {
                document.querySelector('.question-box').classList.add('pulse');
                setTimeout(() => {
                    document.querySelector('.question-box').classList.remove('pulse');
                }, 2000);
            }, 500);
        }

        // 显示总结
        function showSummary() {
            const summary = `
🎯 软件结构化设计学习总结

✅ 正确答案：D - 架构设计、接口设计、数据设计、过程设计

📚 核心概念：
• 软件结构化设计是面向数据流的设计方法
• 以结构化分析的成果为基础
• 采用自顶而下、逐步求精、模块化的设计过程

🏗️ 四大核心任务：

1️⃣ 架构设计：
   • 定义系统整体结构
   • 进行模块划分和组织
   • 输出：系统架构图、模块结构图

2️⃣ 接口设计：
   • 定义模块间的接口
   • 规范数据传递方式
   • 输出：接口说明书、API文档

3️⃣ 数据设计：
   • 设计数据结构
   • 进行数据库设计
   • 输出：数据字典、数据库设计

4️⃣ 过程设计：
   • 设计算法流程
   • 定义处理逻辑
   • 输出：流程图、伪代码

🧠 记忆技巧：
• "架构接口数据过程，结构设计四大任务"
• 建筑类比：架构=建筑设计，接口=门窗，数据=家具，过程=使用流程
• 英文首字母：AIDP (Architecture, Interface, Data, Process)

⚡ 考试要点：
• 四大任务：架构、接口、数据、过程 ✅
• 排除项：原型设计、程序设计、交互设计 ❌
• 设计特点：自顶而下、逐步求精、模块化
• 设计基础：结构化分析的成果

🔑 关键理解：
结构化设计是一个系统性的过程，四个任务相互关联：
- 架构设计确定整体框架
- 接口设计连接各个模块
- 数据设计支撑系统运行
- 过程设计实现具体功能

🎉 恭喜掌握软件结构化设计知识！
            `;

            alert(summary);
        }

        // 添加CSS动画类
        const style = document.createElement('style');
        style.textContent = `
            .pulse {
                animation: pulse 1s ease-in-out 3;
            }

            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.02);
                    box-shadow: 0 25px 50px rgba(255, 107, 107, 0.4);
                }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的欢迎提示
        window.addEventListener('load', function() {
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                welcome.innerHTML = `
                    <h3>🌟 欢迎来到软件设计学习世界！</h3>
                    <p>让我们一起探索软件结构化设计的四大任务</p>
                    <button onclick="this.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 15px;
                        margin-top: 15px;
                        cursor: pointer;
                    ">开始学习 🚀</button>
                `;
                document.body.appendChild(welcome);
            }, 1000);
        });

        // 初始化
        resetDemo();
    </script>
</body>
</html>
