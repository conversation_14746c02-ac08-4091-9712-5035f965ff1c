<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RUP软件过程 - 互动学习游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .game-board {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .phases-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
            gap: 20px;
        }

        .phase-card {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .phase-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .phase-card:hover::before {
            left: 100%;
        }

        .phase-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .phase-card.active {
            border-color: #667eea;
            transform: scale(1.05);
        }

        .phase-number {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            margin: 0 auto 15px;
        }

        .phase-name {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .phase-desc {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        #gameCanvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .question-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .question-text {
            font-size: 1.1rem;
            color: #555;
            line-height: 1.6;
            margin-bottom: 25px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .option {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .option.correct {
            border-color: #28a745;
            background: #28a745;
            color: white;
        }

        .option.wrong {
            border-color: #dc3545;
            background: #dc3545;
            color: white;
        }

        .submit-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .explanation {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #17a2b8;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚀 RUP软件过程学习游戏</h1>
            <p class="subtitle">通过互动游戏理解软件开发的四个阶段</p>
        </div>

        <div class="game-board">
            <div class="phases-container">
                <div class="phase-card" data-phase="0">
                    <div class="phase-number">1</div>
                    <div class="phase-name">初始阶段</div>
                    <div class="phase-desc">建立业务模型<br>确定项目边界</div>
                </div>
                <div class="phase-card" data-phase="1">
                    <div class="phase-number">2</div>
                    <div class="phase-name">细化阶段</div>
                    <div class="phase-desc">分析问题领域<br>建立完善架构</div>
                </div>
                <div class="phase-card" data-phase="2">
                    <div class="phase-number">3</div>
                    <div class="phase-name">构建阶段</div>
                    <div class="phase-desc">开发剩余构件<br>集成为产品</div>
                </div>
                <div class="phase-card" data-phase="3">
                    <div class="phase-number">4</div>
                    <div class="phase-name">移交阶段</div>
                    <div class="phase-desc">确保软件可用<br>交付给用户</div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="400"></canvas>
            </div>

            <div class="question-section">
                <h2 class="question-title">📝 知识测试</h2>
                <div class="question-text">
                    基于 RUP 的软件过程是一个迭代过程，一个开发周期包括初始、细化、构建和移交4个阶段。每次通过这4个阶段就会产生一代软件，其中<strong>定义最终业务模型</strong>是（）阶段的任务。
                </div>
                <div class="options">
                    <div class="option" data-answer="A">A. 初始</div>
                    <div class="option" data-answer="B">B. 细化</div>
                    <div class="option" data-answer="C">C. 构建</div>
                    <div class="option" data-answer="D">D. 移交</div>
                </div>
                <button class="submit-btn" id="submitBtn" disabled>提交答案</button>
                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：A. 初始</strong></p>
                    <p>在RUP的<strong>初始阶段</strong>，主要任务包括：</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>为系统建立业务模型</li>
                        <li>确定项目的边界和范围</li>
                        <li>识别关键的利益相关者</li>
                        <li>定义项目的愿景和目标</li>
                    </ul>
                    <p>这个阶段就像盖房子前的规划设计，需要明确要建什么样的房子，给谁住，有什么功能需求。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Canvas动画相关变量
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let currentPhase = 0;
        let animationFrame = 0;
        let particles = [];

        // 阶段数据
        const phases = [
            { name: '初始', color: '#FF6B6B', icon: '💡', tasks: ['业务建模', '项目边界', '利益相关者'] },
            { name: '细化', color: '#4ECDC4', icon: '🔍', tasks: ['问题分析', '架构设计', '风险控制'] },
            { name: '构建', color: '#45B7D1', icon: '🔨', tasks: ['功能开发', '构件集成', '产品构建'] },
            { name: '移交', color: '#96CEB4', icon: '🚀', tasks: ['用户测试', '部署交付', '维护支持'] }
        ];

        // 初始化
        function init() {
            createParticles();
            animate();
            setupEventListeners();
        }

        // 创建粒子效果
        function createParticles() {
            particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    radius: Math.random() * 3 + 1,
                    alpha: Math.random() * 0.5 + 0.2
                });
            }
        }

        // 动画循环
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景粒子
            drawParticles();
            
            // 绘制RUP流程图
            drawRUPProcess();
            
            animationFrame++;
            requestAnimationFrame(animate);
        }

        // 绘制粒子
        function drawParticles() {
            particles.forEach(particle => {
                ctx.save();
                ctx.globalAlpha = particle.alpha;
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();

                // 更新粒子位置
                particle.x += particle.vx;
                particle.y += particle.vy;

                // 边界检测
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            });
        }

        // 绘制RUP流程
        function drawRUPProcess() {
            const centerY = canvas.height / 2;
            const spacing = canvas.width / 5;
            const startX = spacing;

            phases.forEach((phase, index) => {
                const x = startX + index * spacing;
                const y = centerY;
                const isActive = index === currentPhase;
                const radius = isActive ? 60 : 50;

                // 绘制连接线
                if (index < phases.length - 1) {
                    ctx.strokeStyle = '#ddd';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(x + radius, y);
                    ctx.lineTo(x + spacing - radius, y);
                    ctx.stroke();
                }

                // 绘制阶段圆圈
                ctx.save();
                if (isActive) {
                    ctx.shadowColor = phase.color;
                    ctx.shadowBlur = 20;
                }
                
                ctx.fillStyle = phase.color;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, Math.PI * 2);
                ctx.fill();

                // 绘制图标
                ctx.fillStyle = 'white';
                ctx.font = '30px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText(phase.icon, x, y - 5);

                // 绘制阶段名称
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.fillText(phase.name, x, y + radius + 25);

                // 绘制任务列表（仅当前阶段）
                if (isActive) {
                    phase.tasks.forEach((task, taskIndex) => {
                        ctx.fillStyle = '#666';
                        ctx.font = '12px Microsoft YaHei';
                        ctx.fillText(task, x, y + radius + 50 + taskIndex * 20);
                    });
                }

                ctx.restore();
            });

            // 绘制进度指示器
            const progress = (currentPhase + 1) / phases.length;
            const progressWidth = canvas.width * 0.8;
            const progressX = (canvas.width - progressWidth) / 2;
            const progressY = canvas.height - 50;

            ctx.fillStyle = '#e9ecef';
            ctx.fillRect(progressX, progressY, progressWidth, 8);
            
            ctx.fillStyle = '#667eea';
            ctx.fillRect(progressX, progressY, progressWidth * progress, 8);
        }

        // 设置事件监听
        function setupEventListeners() {
            // 阶段卡片点击
            document.querySelectorAll('.phase-card').forEach(card => {
                card.addEventListener('click', () => {
                    const phase = parseInt(card.dataset.phase);
                    selectPhase(phase);
                });
            });

            // 选项点击
            document.querySelectorAll('.option').forEach(option => {
                option.addEventListener('click', () => {
                    document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                    document.getElementById('submitBtn').disabled = false;
                });
            });

            // 提交按钮
            document.getElementById('submitBtn').addEventListener('click', checkAnswer);

            // Canvas点击切换阶段
            canvas.addEventListener('click', (e) => {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const spacing = canvas.width / 5;
                const clickedPhase = Math.floor(x / spacing);
                if (clickedPhase >= 0 && clickedPhase < phases.length) {
                    selectPhase(clickedPhase);
                }
            });
        }

        // 选择阶段
        function selectPhase(phase) {
            currentPhase = phase;
            document.querySelectorAll('.phase-card').forEach(card => card.classList.remove('active'));
            document.querySelector(`[data-phase="${phase}"]`).classList.add('active');
        }

        // 检查答案
        function checkAnswer() {
            const selected = document.querySelector('.option.selected');
            if (!selected) return;

            const answer = selected.dataset.answer;
            const isCorrect = answer === 'A';

            document.querySelectorAll('.option').forEach(option => {
                if (option.dataset.answer === 'A') {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && !isCorrect) {
                    option.classList.add('wrong');
                }
            });

            document.getElementById('submitBtn').disabled = true;
            document.getElementById('explanation').classList.add('show');

            // 如果答对了，播放庆祝动画
            if (isCorrect) {
                celebrateCorrectAnswer();
            }
        }

        // 庆祝动画
        function celebrateCorrectAnswer() {
            // 创建庆祝粒子
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: canvas.width / 2,
                    y: canvas.height / 2,
                    vx: (Math.random() - 0.5) * 10,
                    vy: (Math.random() - 0.5) * 10,
                    radius: Math.random() * 5 + 3,
                    alpha: 1,
                    color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'][Math.floor(Math.random() * 4)]
                });
            }

            // 阶段卡片脉冲动画
            document.querySelector('[data-phase="0"]').classList.add('pulse');
            setTimeout(() => {
                document.querySelector('[data-phase="0"]').classList.remove('pulse');
            }, 2000);
        }

        // 启动应用
        init();
    </script>
</body>
</html>
