<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构视角学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
            box-sizing: border-box;
        }
        h1, h2, h3 {
            color: #0056b3;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-section, .explanation-section, .game-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fdfdff;
        }
        .question-section h2, .explanation-section h2, .game-section h2 {
            margin-top: 0;
            color: #007bff;
        }
        .question-text {
            font-size: 1.2em;
            margin-bottom: 20px;
            text-align: left;
            font-weight: bold;
        }
        .options label {
            display: block;
            margin-bottom: 12px;
            padding: 12px 15px;
            border: 1px solid #ccc;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1em;
            background-color: #f9f9f9;
        }
        .options label:hover {
            background-color: #e6f7ff;
            border-color: #007bff;
        }
        .options input[type="radio"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        .options input[type="radio"]:checked + span {
            color: #0056b3;
            font-weight: bold;
        }
        .feedback {
            margin-top: 20px;
            padding: 10px 15px;
            border-radius: 6px;
            text-align: center;
            font-weight: bold;
            display: none; /* Hidden by default */
        }
        .feedback.correct {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .explanation-step {
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px dashed #eee;
        }
        .explanation-step:last-child {
            border-bottom: none;
        }
        .explanation-content {
            font-size: 1.1em;
            background-color: #eaf6ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        .explanation-buttons {
            text-align: center;
            margin-top: 20px;
        }
        .explanation-buttons button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin: 0 10px;
            transition: background-color 0.3s ease;
        }
        .explanation-buttons button:hover {
            background-color: #0056b3;
        }
        canvas {
            border: 2px solid #007bff;
            background-color: #eaf6ff;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
        }
        .game-controls {
            text-align: center;
            margin-top: 20px;
        }
        .game-controls button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        .game-controls button:hover {
            background-color: #218838;
        }
        .next-section-button {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            margin-top: 30px;
            transition: background-color 0.3s ease;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        .next-section-button:hover {
            background-color: #5a6268;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件架构视角学习之旅</h1>

        <div id="question-section" class="question-section">
            <h2>问题2 [单选题]</h2>
            <p class="question-text">
                考虑软件架构时，重要的是从不同的视角（perspective）来检查，这促使软件设计师考虑架构的不同属性。例如，展示功能组织的()能判断质量特性，展示并发行行为的()能判断系统行为特性。选择的特定视角或视图也就是逻辑视图、进程视图、实现视图和()。使用()来记录设计元素的功能和概念接口，设计元素的功能定义了它本身在系统中的角色，这些角色包括功能、性能等。
            </p>
            <div class="options" id="options">
                <label><input type="radio" name="answer" value="A"> <span>A 开发视角</span></label>
                <label><input type="radio" name="answer" value="B"> <span>B 动态视角</span></label>
                <label><input type="radio" name="answer" value="C"> <span>C 部署视角</span></label>
                <label><input type="radio" name="answer" value="D"> <span>D 功能视角</span></label>
            </div>
            <div id="feedback" class="feedback"></div>
            <button id="check-answer-button" class="next-section-button">提交答案</button>
        </div>

        <div id="explanation-section" class="explanation-section hidden">
            <h2>软件架构视角知识解释</h2>
            <div id="explanation-steps">
                <!-- Explanation steps will be dynamically loaded here -->
            </div>
            <div class="explanation-buttons">
                <button id="prev-explanation-button" disabled>上一步</button>
                <button id="next-explanation-button">下一步</button>
            </div>
            <canvas id="architectureCanvas" width="700" height="400"></canvas>
            <button id="start-game-button" class="next-section-button hidden">进入小游戏</button>
        </div>

        <div id="game-section" class="game-section hidden">
            <h2>小游戏：匹配系统行为</h2>
            <p>请将右侧的行为拖动到左侧对应的视角区域中。</p>
            <div style="display: flex; justify-content: space-around; margin-top: 20px;">
                <div style="border: 2px dashed #007bff; padding: 20px; min-width: 300px; text-align: center; border-radius: 8px;">
                    <h3>动态视角 (Dynamic View)</h3>
                    <div id="dynamic-view-dropzone" style="min-height: 150px; border: 1px solid #ccc; background-color: #f0f8ff; border-radius: 5px; padding: 10px;">
                        拖动到这里
                    </div>
                </div>
                <div style="border: 2px dashed #28a745; padding: 20px; min-width: 300px; text-align: center; border-radius: 8px;">
                    <h3>其他视角</h3>
                    <div id="other-views-dropzone" style="min-height: 150px; border: 1px solid #ccc; background-color: #f0fff0; border-radius: 5px; padding: 10px;">
                        拖动到这里
                    </div>
                </div>
            </div>
            <div id="draggable-behaviors" style="margin-top: 30px; text-align: center;">
                <div class="draggable-item" draggable="true" data-view="dynamic">用户登录流程</div>
                <div class="draggable-item" draggable="true" data-view="other">类之间的继承关系</div>
                <div class="draggable-item" draggable="true" data-view="dynamic">系统并发处理</div>
                <div class="draggable-item" draggable="true" data-view="other">数据库表结构</div>
                <div class="draggable-item" draggable="true" data-view="dynamic">消息队列传输</div>
            </div>
            <div id="game-feedback" class="feedback" style="margin-top: 20px;"></div>
            <button id="check-game-button" class="next-section-button">检查匹配</button>
            <button id="reset-game-button" class="next-section-button">重新开始</button>
        </div>

    </div>

    <script>
        const questionSection = document.getElementById('question-section');
        const explanationSection = document.getElementById('explanation-section');
        const gameSection = document.getElementById('game-section');
        const checkAnswerButton = document.getElementById('check-answer-button');
        const feedbackDiv = document.getElementById('feedback');
        const optionsDiv = document.getElementById('options');
        const startGameButton = document.getElementById('start-game-button');

        const explanationStepsDiv = document.getElementById('explanation-steps');
        const prevExplanationButton = document.getElementById('prev-explanation-button');
        const nextExplanationButton = document.getElementById('next-explanation-button');

        const canvas = document.getElementById('architectureCanvas');
        const ctx = canvas.getContext('2d');

        let currentExplanationStep = 0;

        const explanationContents = [
            {
                title: "什么是软件架构视角？",
                content: "在设计软件系统时，我们需要从不同的角度（视角）来观察它，就像建造房屋需要有平面图、立面图、电路图一样。这些视角帮助我们全面理解系统的不同方面。",
                animation: "intro"
            },
            {
                title: "为什么需要不同的视角？",
                content: "不同的视角关注系统不同的属性：有些关注功能，有些关注性能，有些关注如何部署。这有助于设计师全面考虑系统的质量特性。",
                animation: "multipleViews"
            },
            {
                title: "逻辑视图 (Logical View)",
                content: "逻辑视图关注系统的功能是如何被划分和组织的，比如类、对象和它们之间的关系。它描述的是系统的“静态结构”。",
                animation: "logical"
            },
            {
                title: "进程视图 (Process View)",
                content: "进程视图关注系统运行时并发和同步的问题，即不同的进程或线程是如何协同工作和通信的。",
                animation: "process"
            },
            {
                title: "实现视图 (Implementation View)",
                content: "实现视图关注系统代码的物理组织，比如文件、模块、库以及它们之间的依赖关系。这更接近于开发者看到的代码结构。",
                animation: "implementation"
            },
            {
                title: "部署视图 (Deployment View)",
                content: "部署视图关注系统如何映射到物理硬件环境上，包括服务器、网络设备以及软件组件在这些设备上的分布。",
                animation: "deployment"
            },
            {
                title: "动态视角 (Dynamic View) - 答案解析！",
                content: "✨ **动态视角** 关注的是系统在运行时的“行为”！它描述了组件之间如何交互、数据如何流动、消息如何传递、以及系统状态如何随时间变化。当你想要理解系统的性能、并发性、响应速度等“行为特性”时，动态视角就派上用场了！它能让你看到系统“动起来”的样子。",
                animation: "dynamic"
            }
        ];

        // --- Question Section Logic ---
        checkAnswerButton.addEventListener('click', () => {
            const selectedOption = document.querySelector('input[name="answer"]:checked');
            if (selectedOption) {
                if (selectedOption.value === 'B') {
                    feedbackDiv.textContent = '恭喜你，回答正确！动态视角正是关注系统行为的！';
                    feedbackDiv.className = 'feedback correct';
                    setTimeout(() => {
                        questionSection.classList.add('hidden');
                        explanationSection.classList.remove('hidden');
                        displayExplanationStep(currentExplanationStep);
                    }, 1500);
                } else {
                    feedbackDiv.textContent = '很遗憾，回答错误。再想想看哪个视角关注系统的运行时行为？';
                    feedbackDiv.className = 'feedback incorrect';
                }
                feedbackDiv.style.display = 'block';
            } else {
                feedbackDiv.textContent = '请选择一个答案！';
                feedbackDiv.className = 'feedback incorrect';
                feedbackDiv.style.display = 'block';
            }
        });

        // --- Explanation Section Logic ---
        function displayExplanationStep(index) {
            explanationStepsDiv.innerHTML = '';
            const step = explanationContents[index];
            const div = document.createElement('div');
            div.className = 'explanation-step';
            div.innerHTML = `
                <h3>${step.title}</h3>
                <div class="explanation-content">${step.content}</div>
            `;
            explanationStepsDiv.appendChild(div);

            // Update button states
            prevExplanationButton.disabled = index === 0;
            nextExplanationButton.disabled = index === explanationContents.length - 1;

            if (index === explanationContents.length - 1) {
                startGameButton.classList.remove('hidden');
            } else {
                startGameButton.classList.add('hidden');
            }

            // Run corresponding animation
            runCanvasAnimation(step.animation);
        }

        nextExplanationButton.addEventListener('click', () => {
            if (currentExplanationStep < explanationContents.length - 1) {
                currentExplanationStep++;
                displayExplanationStep(currentExplanationStep);
            }
        });

        prevExplanationButton.addEventListener('click', () => {
            if (currentExplanationStep > 0) {
                currentExplanationStep--;
                displayExplanationStep(currentExplanationStep);
            }
        });

        // --- Canvas Animation Logic ---
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
        }

        function drawComponent(x, y, label, color = '#007bff') {
            ctx.fillStyle = color;
            ctx.fillRect(x - 30, y - 15, 60, 30);
            ctx.strokeStyle = color;
            ctx.strokeRect(x - 30, y - 15, 60, 30);
            ctx.fillStyle = 'white';
            ctx.fillText(label, x, y);
        }

        function drawArrow(startX, startY, endX, endY, label = '', color = '#333') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            const headlen = 10; // length of head in pixels
            const angle = Math.atan2(endY - startY, endX - startX);
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX - headlen * Math.cos(angle - Math.PI / 6), endY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(endX - headlen * Math.cos(angle + Math.PI / 6), endY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = color;
            ctx.fill();

            if (label) {
                ctx.fillStyle = '#333';
                ctx.fillText(label, (startX + endX) / 2, (startY + endY) / 2 - 10);
            }
        }

        let animationFrameId;

        function runCanvasAnimation(type) {
            cancelAnimationFrame(animationFrameId); // Stop any previous animation
            clearCanvas();

            if (type === "intro") {
                ctx.fillStyle = '#333';
                ctx.fillText("这里将展示软件架构的不同视角...", canvas.width / 2, canvas.height / 2);
            } else if (type === "multipleViews") {
                drawComponent(canvas.width / 4, canvas.height / 2, "功能");
                drawComponent(canvas.width / 2, canvas.height / 2, "性能", '#28a745');
                drawComponent(canvas.width * 3 / 4, canvas.height / 2, "部署", '#dc3545');
                ctx.fillStyle = '#333';
                ctx.fillText("系统有多种属性，需要多视角考量", canvas.width / 2, canvas.height / 4);
            } else if (type === "logical") {
                drawComponent(canvas.width / 2, canvas.height / 4, "用户管理");
                drawComponent(canvas.width / 4, canvas.height * 3 / 4, "订单模块");
                drawComponent(canvas.width * 3 / 4, canvas.height * 3 / 4, "支付模块");
                drawArrow(canvas.width / 2, canvas.height / 4 + 15, canvas.width / 4 + 30, canvas.height * 3 / 4 - 15, "包含");
                drawArrow(canvas.width / 2, canvas.height / 4 + 15, canvas.width * 3 / 4 - 30, canvas.height * 3 / 4 - 15, "包含");
                ctx.fillStyle = '#333';
                ctx.fillText("逻辑视图：功能如何划分和组织", canvas.width / 2, 30);
            } else if (type === "process") {
                drawComponent(canvas.width / 4, canvas.height / 2, "进程A", '#ffc107');
                drawComponent(canvas.width * 3 / 4, canvas.height / 2, "进程B", '#17a2b8');
                let messageX = canvas.width / 4 + 30;
                let messageSpeed = 2;
                function animateProcess() {
                    clearCanvas();
                    drawComponent(canvas.width / 4, canvas.height / 2, "进程A", '#ffc107');
                    drawComponent(canvas.width * 3 / 4, canvas.height / 2, "进程B", '#17a2b8');
                    
                    ctx.fillStyle = '#000';
                    ctx.beginPath();
                    ctx.arc(messageX, canvas.height / 2, 8, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.fillText("消息", messageX, canvas.height / 2 - 20);

                    messageX += messageSpeed;
                    if (messageX > canvas.width * 3 / 4 - 30 || messageX < canvas.width / 4 + 30) {
                        messageSpeed *= -1; // Reverse direction
                        if (messageX < canvas.width / 4 + 30) messageX = canvas.width / 4 + 30;
                        if (messageX > canvas.width * 3 / 4 - 30) messageX = canvas.width * 3 / 4 - 30;
                    }
                    ctx.fillStyle = '#333';
                    ctx.fillText("进程视图：并发与消息通信", canvas.width / 2, 30);
                    animationFrameId = requestAnimationFrame(animateProcess);
                }
                animateProcess();
            } else if (type === "implementation") {
                drawComponent(canvas.width / 2, canvas.height / 4, "src/main.js");
                drawComponent(canvas.width / 4, canvas.height * 3 / 4, "lib/utils.js");
                drawComponent(canvas.width * 3 / 4, canvas.height * 3 / 4, "components/Button.js");
                drawArrow(canvas.width / 2, canvas.height / 4 + 15, canvas.width / 4 + 30, canvas.height * 3 / 4 - 15, "import");
                drawArrow(canvas.width / 2, canvas.height / 4 + 15, canvas.width * 3 / 4 - 30, canvas.height * 3 / 4 - 15, "import");
                ctx.fillStyle = '#333';
                ctx.fillText("实现视图：代码文件和依赖关系", canvas.width / 2, 30);
            } else if (type === "deployment") {
                ctx.strokeStyle = '#6c757d';
                ctx.lineWidth = 3;
                ctx.strokeRect(50, 50, 200, 300);
                ctx.fillText("服务器A", 150, 70);
                ctx.strokeRect(450, 50, 200, 300);
                ctx.fillText("服务器B", 550, 70);

                drawComponent(150, 150, "App Service", '#6f42c1');
                drawComponent(150, 250, "DB Service", '#fd7e14');
                drawComponent(550, 150, "Web Server", '#20c997');
                drawComponent(550, 250, "Load Balancer", '#6610f2');

                drawArrow(250, 200, 450, 200, "网络", '#333');
                ctx.fillStyle = '#333';
                ctx.fillText("部署视图：软件如何部署到硬件", canvas.width / 2, 30);
            } else if (type === "dynamic") {
                let compA = { x: 100, y: canvas.height / 2, label: "用户界面", color: "#007bff" };
                let compB = { x: canvas.width / 2, y: canvas.height / 2, label: "业务逻辑", color: "#28a745" };
                let compC = { x: canvas.width - 100, y: canvas.height / 2, label: "数据库", color: "#dc3545" };

                let messageProgress = 0;
                let messageDirection = 1; // 1 for A->B, 2 for B->C, 3 for C->B, 4 for B->A

                function animateDynamic() {
                    clearCanvas();
                    ctx.fillStyle = '#333';
                    ctx.fillText("动态视角：系统行为与交互流程", canvas.width / 2, 30);

                    drawComponent(compA.x, compA.y, compA.label, compA.color);
                    drawComponent(compB.x, compB.y, compB.label, compB.color);
                    drawComponent(compC.x, compC.y, compC.label, compC.color);

                    let msg1StartX, msg1StartY, msg1EndX, msg1EndY;
                    let msg2StartX, msg2StartY, msg2EndX, msg2EndY;

                    if (messageDirection === 1) { // A -> B
                        msg1StartX = compA.x + 30; msg1StartY = compA.y;
                        msg1EndX = compB.x - 30; msg1EndY = compB.y;
                        let currentX = msg1StartX + (msg1EndX - msg1StartX) * messageProgress;
                        drawArrow(currentX - 10, msg1StartY, currentX, msg1StartY, "请求", "orange");
                    } else if (messageDirection === 2) { // B -> C
                        msg1StartX = compA.x + 30; msg1StartY = compA.y;
                        msg1EndX = compB.x - 30; msg1EndY = compB.y;
                        drawArrow(msg1StartX, msg1StartY, msg1EndX, msg1EndY, "请求", "orange"); // Draw static message
                        
                        msg2StartX = compB.x + 30; msg2StartY = compB.y;
                        msg2EndX = compC.x - 30; msg2EndY = compC.y;
                        let currentX = msg2StartX + (msg2EndX - msg2StartX) * messageProgress;
                        drawArrow(currentX - 10, msg2StartY, currentX, msg2StartY, "查询", "purple");
                    } else if (messageDirection === 3) { // C -> B
                        msg1StartX = compA.x + 30; msg1StartY = compA.y;
                        msg1EndX = compB.x - 30; msg1EndY = compB.y;
                        drawArrow(msg1StartX, msg1StartY, msg1EndX, msg1EndY, "请求", "orange"); 

                        msg2StartX = compB.x + 30; msg2StartY = compB.y;
                        msg2EndX = compC.x - 30; msg2EndY = compC.y;
                        drawArrow(msg2StartX, msg2StartY, msg2EndX, msg2EndY, "查询", "purple");

                        msg2StartX = compC.x - 30; msg2StartY = compC.y;
                        msg2EndX = compB.x + 30; msg2EndY = compB.y;
                        let currentX = msg2StartX - (msg2StartX - msg2EndX) * messageProgress;
                        drawArrow(currentX + 10, msg2StartY, currentX, msg2StartY, "结果", "brown");
                    } else if (messageDirection === 4) { // B -> A
                        msg1StartX = compA.x + 30; msg1StartY = compA.y;
                        msg1EndX = compB.x - 30; msg1EndY = compB.y;
                        drawArrow(msg1StartX, msg1StartY, msg1EndX, msg1EndY, "请求", "orange"); 

                        msg2StartX = compB.x + 30; msg2StartY = compB.y;
                        msg2EndX = compC.x - 30; msg2EndY = compC.y;
                        drawArrow(msg2StartX, msg2StartY, msg2EndX, msg2EndY, "查询", "purple");

                        let msg3StartX = compC.x - 30; let msg3StartY = compC.y;
                        let msg3EndX = compB.x + 30; let msg3EndY = compB.y;
                        drawArrow(msg3StartX, msg3StartY, msg3EndX, msg3EndY, "结果", "brown");

                        msg1StartX = compB.x - 30; msg1StartY = compB.y;
                        msg1EndX = compA.x + 30; msg1EndY = compA.y;
                        let currentX = msg1StartX - (msg1StartX - msg1EndX) * messageProgress;
                        drawArrow(currentX + 10, msg1StartY, currentX, msg1StartY, "响应", "green");
                    }

                    messageProgress += 0.02; // Speed of animation
                    if (messageProgress > 1) {
                        messageProgress = 0;
                        messageDirection++;
                        if (messageDirection > 4) {
                            messageDirection = 1; // Loop back
                        }
                    }
                    animationFrameId = requestAnimationFrame(animateDynamic);
                }
                animateDynamic();
            }
        }


        // --- Game Section Logic ---
        startGameButton.addEventListener('click', () => {
            explanationSection.classList.add('hidden');
            gameSection.classList.remove('hidden');
            setupGame();
        });

        const dynamicDropzone = document.getElementById('dynamic-view-dropzone');
        const otherDropzone = document.getElementById('other-views-dropzone');
        const draggableItems = document.querySelectorAll('.draggable-item');
        const checkGameButton = document.getElementById('check-game-button');
        const resetGameButton = document.getElementById('reset-game-button');
        const gameFeedbackDiv = document.getElementById('game-feedback');

        let draggedItem = null;

        function setupGame() {
            gameFeedbackDiv.style.display = 'none';
            dynamicDropzone.innerHTML = '拖动到这里';
            otherDropzone.innerHTML = '拖动到这里';
            document.getElementById('draggable-behaviors').innerHTML = `
                <div class="draggable-item" draggable="true" data-view="dynamic">用户登录流程</div>
                <div class="draggable-item" draggable="true" data-view="other">类之间的继承关系</div>
                <div class="draggable-item" draggable="true" data-view="dynamic">系统并发处理</div>
                <div class="draggable-item" draggable="true" data-view="other">数据库表结构</div>
                <div class="draggable-item" draggable="true" data-view="dynamic">消息队列传输</div>
            `;
            // Re-select draggable items as they are re-rendered
            document.querySelectorAll('.draggable-item').forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
            });
            dynamicDropzone.style.backgroundColor = '#f0f8ff';
            otherDropzone.style.backgroundColor = '#f0fff0';
        }

        draggableItems.forEach(item => {
            item.addEventListener('dragstart', handleDragStart);
        });

        function handleDragStart(e) {
            draggedItem = e.target;
            e.dataTransfer.setData('text/plain', e.target.id); // Not using id, but good practice
        }

        dynamicDropzone.addEventListener('dragover', handleDragOver);
        otherDropzone.addEventListener('dragover', handleDragOver);

        function handleDragOver(e) {
            e.preventDefault(); // Allow drop
        }

        dynamicDropzone.addEventListener('drop', handleDrop);
        otherDropzone.addEventListener('drop', handleDrop);

        function handleDrop(e) {
            e.preventDefault();
            if (e.target.id === 'dynamic-view-dropzone' || e.target.id === 'other-views-dropzone' || e.target.closest('.draggable-item')) {
                // Check if dropping onto a dropzone or existing draggable item within a dropzone
                const dropzone = e.target.id === 'dynamic-view-dropzone' ? dynamicDropzone : (e.target.id === 'other-views-dropzone' ? otherDropzone : e.target.closest('.draggable-item')?.parentElement);
                
                if (dropzone) {
                    if (dropzone.innerHTML.includes('拖动到这里')) {
                        dropzone.innerHTML = ''; // Clear placeholder text
                    }
                    dropzone.appendChild(draggedItem);
                }
            }
        }

        checkGameButton.addEventListener('click', () => {
            let correctCount = 0;
            let totalItems = 0;

            const droppedInDynamic = dynamicDropzone.querySelectorAll('.draggable-item');
            droppedInDynamic.forEach(item => {
                totalItems++;
                if (item.dataset.view === 'dynamic') {
                    correctCount++;
                    item.style.backgroundColor = '#d4edda';
                } else {
                    item.style.backgroundColor = '#f8d7da';
                }
            });

            const droppedInOther = otherDropzone.querySelectorAll('.draggable-item');
            droppedInOther.forEach(item => {
                totalItems++;
                if (item.dataset.view === 'other') {
                    correctCount++;
                    item.style.backgroundColor = '#d4edda';
                } else {
                    item.style.backgroundColor = '#f8d7da';
                }
            });

            if (correctCount === totalItems && totalItems > 0) {
                gameFeedbackDiv.textContent = '太棒了！所有匹配都正确！你已经掌握了动态视角！';
                gameFeedbackDiv.className = 'feedback correct';
                dynamicDropzone.style.backgroundColor = '#d4edda';
                otherDropzone.style.backgroundColor = '#d4edda';
            } else {
                gameFeedbackDiv.textContent = `还有一些需要改进，你正确匹配了 ${correctCount} / ${totalItems} 个。请看红色背景的卡片，再想想它们应该属于哪个视角。`;
                gameFeedbackDiv.className = 'feedback incorrect';
            }
            gameFeedbackDiv.style.display = 'block';
        });

        resetGameButton.addEventListener('click', setupGame);

        // Initial setup
        // Hide explanation and game sections initially
        explanationSection.classList.add('hidden');
        gameSection.classList.add('hidden');
    </script>
</body>
</html> 