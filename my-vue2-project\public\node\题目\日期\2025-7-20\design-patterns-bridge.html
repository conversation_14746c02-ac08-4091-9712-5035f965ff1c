<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式学习 - Bridge桥接模式</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .nav-item {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 25px;
            padding: 12px 24px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .content-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.5s ease;
        }

        .content-section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        #animationCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: white;
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: justify;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .pattern-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .pattern-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .pattern-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .pattern-card.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
        }

        .pattern-card.incorrect {
            border-color: #f44336;
            background: linear-gradient(135deg, #ffebee 0%, #fce4ec 100%);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .hidden {
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">设计模式学习中心</h1>
            <p class="subtitle">零基础学习Bridge桥接模式 - 让抽象与实现分离</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="nav-menu">
            <div class="nav-item active" data-section="overview">模式概述</div>
            <div class="nav-item" data-section="problem">问题场景</div>
            <div class="nav-item" data-section="solution">桥接解决方案</div>
            <div class="nav-item" data-section="demo">交互演示</div>
            <div class="nav-item" data-section="comparison">模式对比</div>
            <div class="nav-item" data-section="practice">实践练习</div>
        </div>

        <!-- 模式概述 -->
        <div class="content-section visible" id="overview">
            <h2 class="section-title">什么是Bridge桥接模式？</h2>
            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">Bridge桥接模式</span>是一种结构型设计模式，它将<span class="highlight">抽象部分</span>与<span class="highlight">实现部分</span>分离，使它们都可以独立地变化。</p>
                <p>就像现实中的桥梁连接两岸，让两岸可以独立发展一样，桥接模式在抽象和实现之间建立了一座"桥梁"。</p>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="startOverviewAnimation()">🎬 观看桥接动画</button>
            </div>
        </div>

        <!-- 问题场景 -->
        <div class="content-section hidden" id="problem">
            <h2 class="section-title">广告公司的困扰</h2>
            <div class="canvas-container">
                <canvas id="problemCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p>某广告公司有多种<span class="highlight">宣传产品</span>：宣传册、文章、传单</p>
                <p>有多种<span class="highlight">出版方式</span>：纸质、CD、DVD、在线发布</p>
                <p>如果直接组合，会产生 3×4=12 种类，代码会变得复杂且难以维护。</p>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="showProblemDemo()">🔍 查看问题演示</button>
            </div>
        </div>

        <!-- 桥接解决方案 -->
        <div class="content-section hidden" id="solution">
            <h2 class="section-title">桥接模式解决方案</h2>
            <div class="canvas-container">
                <canvas id="solutionCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><span class="highlight">桥接模式</span>将宣传产品（抽象）和出版方式（实现）分离：</p>
                <p>• <strong>抽象层</strong>：定义宣传产品的基本结构</p>
                <p>• <strong>实现层</strong>：定义出版方式的具体操作</p>
                <p>• <strong>桥接</strong>：通过组合而非继承连接两者</p>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="showSolutionDemo()">✨ 查看解决方案</button>
            </div>
        </div>

        <!-- 交互演示 -->
        <div class="content-section hidden" id="demo">
            <h2 class="section-title">交互式演示</h2>
            <div class="canvas-container">
                <canvas id="demoCanvas" width="800" height="500"></canvas>
            </div>
            <div class="interactive-demo">
                <button class="demo-button" onclick="createProduct('brochure')">📖 创建宣传册</button>
                <button class="demo-button" onclick="createProduct('article')">📄 创建文章</button>
                <button class="demo-button" onclick="createProduct('flyer')">📋 创建传单</button>
                <br><br>
                <button class="demo-button" onclick="setPublishMethod('paper')">📰 纸质出版</button>
                <button class="demo-button" onclick="setPublishMethod('cd')">💿 CD出版</button>
                <button class="demo-button" onclick="setPublishMethod('dvd')">📀 DVD出版</button>
                <button class="demo-button" onclick="setPublishMethod('online')">🌐 在线发布</button>
                <br><br>
                <button class="demo-button" onclick="publishProduct()">🚀 执行发布</button>
                <button class="demo-button" onclick="resetDemo()">🔄 重置演示</button>
            </div>
        </div>

        <!-- 模式对比 -->
        <div class="content-section hidden" id="comparison">
            <h2 class="section-title">设计模式对比</h2>
            <div class="pattern-comparison">
                <div class="pattern-card" onclick="selectPattern('bridge')">
                    <h3>🌉 Bridge桥接模式</h3>
                    <p>分离抽象与实现，使两者可以独立变化</p>
                    <p><strong>适用场景：</strong>避免抽象与实现的固定绑定</p>
                </div>
                <div class="pattern-card" onclick="selectPattern('adapter')">
                    <h3>🔌 Adapter适配器模式</h3>
                    <p>让不兼容的接口能够协同工作</p>
                    <p><strong>适用场景：</strong>接口不匹配的类需要合作</p>
                </div>
                <div class="pattern-card" onclick="selectPattern('decorator')">
                    <h3>🎨 Decorator装饰器模式</h3>
                    <p>动态地给对象添加新的功能</p>
                    <p><strong>适用场景：</strong>需要动态扩展对象功能</p>
                </div>
                <div class="pattern-card" onclick="selectPattern('facade')">
                    <h3>🏢 Facade外观模式</h3>
                    <p>为复杂子系统提供简单统一接口</p>
                    <p><strong>适用场景：</strong>简化复杂系统的使用</p>
                </div>
            </div>
            <div class="explanation" id="comparisonResult"></div>
        </div>

        <!-- 实践练习 -->
        <div class="content-section hidden" id="practice">
            <h2 class="section-title">实践练习</h2>
            <div class="explanation">
                <p><strong>题目：</strong>某广告公司的宣传产品有宣传册、文章、传单等多种形式，宣传产品的出版方式包括纸质方式、CD、DVD、在线发布等。现要求为该广告公司设计一个管理这些宣传产品的应用，采用（）设计模式较为合适。</p>
            </div>
            <div class="pattern-comparison">
                <div class="pattern-card" onclick="answerQuestion('A')">
                    <h3>A. Decorator装饰器模式</h3>
                    <p>动态添加功能</p>
                </div>
                <div class="pattern-card" onclick="answerQuestion('B')">
                    <h3>B. Adapter适配器模式</h3>
                    <p>接口适配转换</p>
                </div>
                <div class="pattern-card" onclick="answerQuestion('C')">
                    <h3>C. Bridge桥接模式</h3>
                    <p>分离抽象与实现</p>
                </div>
                <div class="pattern-card" onclick="answerQuestion('D')">
                    <h3>D. Facade外观模式</h3>
                    <p>简化复杂接口</p>
                </div>
            </div>
            <div class="explanation" id="practiceResult"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = 'overview';
        let animationId;
        let currentProduct = null;
        let currentPublishMethod = null;
        let selectedPattern = null;

        // 画布上下文
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        const problemCanvas = document.getElementById('problemCanvas');
        const problemCtx = problemCanvas.getContext('2d');
        const solutionCanvas = document.getElementById('solutionCanvas');
        const solutionCtx = solutionCanvas.getContext('2d');
        const demoCanvas = document.getElementById('demoCanvas');
        const demoCtx = demoCanvas.getContext('2d');

        // 导航功能
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', () => {
                const section = item.dataset.section;
                showSection(section);
                updateProgress(section);
            });
        });

        function showSection(sectionName) {
            // 隐藏所有section
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.add('hidden');
                section.classList.remove('visible');
            });

            // 显示目标section
            const targetSection = document.getElementById(sectionName);
            targetSection.classList.remove('hidden');
            setTimeout(() => {
                targetSection.classList.add('visible');
            }, 50);

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

            currentSection = sectionName;

            // 根据section初始化相应动画
            initSectionAnimation(sectionName);
        }

        function updateProgress(section) {
            const sections = ['overview', 'problem', 'solution', 'demo', 'comparison', 'practice'];
            const currentIndex = sections.indexOf(section);
            const progress = ((currentIndex + 1) / sections.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function initSectionAnimation(section) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }

            switch(section) {
                case 'overview':
                    drawOverviewAnimation();
                    break;
                case 'problem':
                    drawProblemScene();
                    break;
                case 'solution':
                    drawSolutionScene();
                    break;
                case 'demo':
                    drawDemoScene();
                    break;
            }
        }

        // 概述动画
        function drawOverviewAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制桥梁概念图
            const time = Date.now() * 0.002;

            // 左岸（抽象层）
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(50, 200, 150, 150);
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('抽象层', 125, 270);
            ctx.fillText('(宣传产品)', 125, 290);

            // 右岸（实现层）
            ctx.fillStyle = '#2196F3';
            ctx.fillRect(600, 200, 150, 150);
            ctx.fillStyle = 'white';
            ctx.fillText('实现层', 675, 270);
            ctx.fillText('(出版方式)', 675, 290);

            // 桥梁
            const bridgeY = 275 + Math.sin(time) * 5;
            ctx.strokeStyle = '#FF9800';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(200, bridgeY);
            ctx.lineTo(600, bridgeY);
            ctx.stroke();

            // 桥梁装饰
            ctx.fillStyle = '#FF9800';
            for(let i = 0; i < 5; i++) {
                const x = 250 + i * 80;
                ctx.fillRect(x - 2, bridgeY - 20, 4, 40);
            }

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('Bridge 桥接模式', 400, 100);

            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('连接抽象与实现，让两者独立变化', 400, 130);
        }

        function startOverviewAnimation() {
            let frame = 0;
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                const progress = (frame % 180) / 180;

                // 动画效果：桥梁建造过程
                if (progress < 0.3) {
                    // 阶段1：显示两岸
                    drawSides();
                } else if (progress < 0.7) {
                    // 阶段2：建造桥梁
                    drawSides();
                    drawBridgeConstruction((progress - 0.3) / 0.4);
                } else {
                    // 阶段3：完整桥梁
                    drawSides();
                    drawCompleteBridge();
                    drawConnectionFlow(progress);
                }

                frame++;
                animationId = requestAnimationFrame(animate);
            }
            animate();
        }

        function drawSides() {
            // 左岸
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(50, 200, 150, 150);
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('抽象层', 125, 270);

            // 右岸
            ctx.fillStyle = '#2196F3';
            ctx.fillRect(600, 200, 150, 150);
            ctx.fillStyle = 'white';
            ctx.fillText('实现层', 675, 270);
        }

        function drawBridgeConstruction(progress) {
            const bridgeLength = 400 * progress;
            ctx.strokeStyle = '#FF9800';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(200, 275);
            ctx.lineTo(200 + bridgeLength, 275);
            ctx.stroke();
        }

        function drawCompleteBridge() {
            ctx.strokeStyle = '#FF9800';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(200, 275);
            ctx.lineTo(600, 275);
            ctx.stroke();
        }

        function drawConnectionFlow(progress) {
            // 数据流动效果
            const flowProgress = (progress - 0.7) / 0.3;
            if (flowProgress > 0) {
                const x = 200 + 400 * flowProgress;
                ctx.fillStyle = '#FFD700';
                ctx.beginPath();
                ctx.arc(x, 275, 8, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        // 问题场景演示
        function drawProblemScene() {
            problemCtx.clearRect(0, 0, problemCanvas.width, problemCanvas.height);

            // 标题
            problemCtx.fillStyle = '#333';
            problemCtx.font = 'bold 20px Microsoft YaHei';
            problemCtx.textAlign = 'center';
            problemCtx.fillText('传统方式：类爆炸问题', 400, 30);

            // 宣传产品
            const products = ['宣传册', '文章', '传单'];
            const methods = ['纸质', 'CD', 'DVD', '在线'];

            // 绘制产品
            products.forEach((product, i) => {
                problemCtx.fillStyle = '#E91E63';
                problemCtx.fillRect(50, 80 + i * 80, 100, 60);
                problemCtx.fillStyle = 'white';
                problemCtx.font = '14px Microsoft YaHei';
                problemCtx.textAlign = 'center';
                problemCtx.fillText(product, 100, 115 + i * 80);
            });

            // 绘制出版方式
            methods.forEach((method, i) => {
                problemCtx.fillStyle = '#3F51B5';
                problemCtx.fillRect(650, 60 + i * 70, 100, 50);
                problemCtx.fillStyle = 'white';
                problemCtx.fillText(method, 700, 90 + i * 70);
            });

            // 绘制复杂的连接线
            problemCtx.strokeStyle = '#FF5722';
            problemCtx.lineWidth = 2;
            products.forEach((_, i) => {
                methods.forEach((_, j) => {
                    problemCtx.beginPath();
                    problemCtx.moveTo(150, 110 + i * 80);
                    problemCtx.lineTo(650, 85 + j * 70);
                    problemCtx.stroke();
                });
            });

            // 问题说明
            problemCtx.fillStyle = '#F44336';
            problemCtx.font = '16px Microsoft YaHei';
            problemCtx.textAlign = 'center';
            problemCtx.fillText('3 × 4 = 12 种组合类', 400, 360);
            problemCtx.fillText('代码复杂，难以维护！', 400, 385);
        }

        function showProblemDemo() {
            let step = 0;
            const totalSteps = 12;

            function animateConnections() {
                problemCtx.clearRect(0, 0, problemCanvas.width, problemCanvas.height);
                drawProblemScene();

                // 高亮当前连接
                if (step < totalSteps) {
                    const productIndex = Math.floor(step / 4);
                    const methodIndex = step % 4;

                    problemCtx.strokeStyle = '#FFD700';
                    problemCtx.lineWidth = 4;
                    problemCtx.beginPath();
                    problemCtx.moveTo(150, 110 + productIndex * 80);
                    problemCtx.lineTo(650, 85 + methodIndex * 70);
                    problemCtx.stroke();

                    // 显示类名
                    problemCtx.fillStyle = '#FFD700';
                    problemCtx.font = 'bold 14px Microsoft YaHei';
                    problemCtx.textAlign = 'center';
                    const products = ['宣传册', '文章', '传单'];
                    const methods = ['纸质', 'CD', 'DVD', '在线'];
                    problemCtx.fillText(
                        `${products[productIndex]}${methods[methodIndex]}类`,
                        400, 330
                    );
                }

                step = (step + 1) % (totalSteps + 5);
                setTimeout(() => requestAnimationFrame(animateConnections), 500);
            }
            animateConnections();
        }

        // 解决方案演示
        function drawSolutionScene() {
            solutionCtx.clearRect(0, 0, solutionCanvas.width, solutionCanvas.height);

            // 标题
            solutionCtx.fillStyle = '#333';
            solutionCtx.font = 'bold 20px Microsoft YaHei';
            solutionCtx.textAlign = 'center';
            solutionCtx.fillText('桥接模式：分离抽象与实现', 400, 30);

            // 抽象层
            solutionCtx.fillStyle = '#4CAF50';
            solutionCtx.fillRect(50, 100, 200, 200);
            solutionCtx.fillStyle = 'white';
            solutionCtx.font = '16px Microsoft YaHei';
            solutionCtx.textAlign = 'center';
            solutionCtx.fillText('抽象层', 150, 130);
            solutionCtx.fillText('宣传产品', 150, 150);

            const products = ['宣传册', '文章', '传单'];
            products.forEach((product, i) => {
                solutionCtx.fillStyle = 'rgba(255,255,255,0.8)';
                solutionCtx.fillRect(70, 170 + i * 35, 160, 25);
                solutionCtx.fillStyle = '#333';
                solutionCtx.font = '12px Microsoft YaHei';
                solutionCtx.fillText(product, 150, 187 + i * 35);
            });

            // 实现层
            solutionCtx.fillStyle = '#2196F3';
            solutionCtx.fillRect(550, 100, 200, 200);
            solutionCtx.fillStyle = 'white';
            solutionCtx.font = '16px Microsoft YaHei';
            solutionCtx.textAlign = 'center';
            solutionCtx.fillText('实现层', 650, 130);
            solutionCtx.fillText('出版方式', 650, 150);

            const methods = ['纸质', 'CD', 'DVD', '在线'];
            methods.forEach((method, i) => {
                solutionCtx.fillStyle = 'rgba(255,255,255,0.8)';
                solutionCtx.fillRect(570, 170 + i * 30, 160, 25);
                solutionCtx.fillStyle = '#333';
                solutionCtx.font = '12px Microsoft YaHei';
                solutionCtx.fillText(method, 650, 187 + i * 30);
            });

            // 桥接
            solutionCtx.strokeStyle = '#FF9800';
            solutionCtx.lineWidth = 6;
            solutionCtx.beginPath();
            solutionCtx.moveTo(250, 200);
            solutionCtx.lineTo(550, 200);
            solutionCtx.stroke();

            // 桥接标签
            solutionCtx.fillStyle = '#FF9800';
            solutionCtx.fillRect(350, 180, 100, 40);
            solutionCtx.fillStyle = 'white';
            solutionCtx.font = 'bold 14px Microsoft YaHei';
            solutionCtx.textAlign = 'center';
            solutionCtx.fillText('桥接', 400, 205);

            // 优势说明
            solutionCtx.fillStyle = '#4CAF50';
            solutionCtx.font = '14px Microsoft YaHei';
            solutionCtx.textAlign = 'center';
            solutionCtx.fillText('✓ 只需 3 + 4 = 7 个类', 400, 340);
            solutionCtx.fillText('✓ 抽象与实现独立变化', 400, 365);
        }

        function showSolutionDemo() {
            let phase = 0;

            function animateSolution() {
                solutionCtx.clearRect(0, 0, solutionCanvas.width, solutionCanvas.height);

                if (phase === 0) {
                    // 阶段1：显示抽象层
                    drawAbstractionLayer();
                } else if (phase === 1) {
                    // 阶段2：显示实现层
                    drawAbstractionLayer();
                    drawImplementationLayer();
                } else {
                    // 阶段3：显示桥接
                    drawSolutionScene();
                }

                phase = (phase + 1) % 4;
                setTimeout(() => requestAnimationFrame(animateSolution), 1500);
            }
            animateSolution();
        }

        function drawAbstractionLayer() {
            solutionCtx.fillStyle = '#4CAF50';
            solutionCtx.fillRect(50, 100, 200, 200);
            solutionCtx.fillStyle = 'white';
            solutionCtx.font = '16px Microsoft YaHei';
            solutionCtx.textAlign = 'center';
            solutionCtx.fillText('抽象层', 150, 200);
        }

        function drawImplementationLayer() {
            solutionCtx.fillStyle = '#2196F3';
            solutionCtx.fillRect(550, 100, 200, 200);
            solutionCtx.fillStyle = 'white';
            solutionCtx.font = '16px Microsoft YaHei';
            solutionCtx.textAlign = 'center';
            solutionCtx.fillText('实现层', 650, 200);
        }

        // 交互演示功能
        function drawDemoScene() {
            demoCtx.clearRect(0, 0, demoCanvas.width, demoCanvas.height);

            // 标题
            demoCtx.fillStyle = '#333';
            demoCtx.font = 'bold 20px Microsoft YaHei';
            demoCtx.textAlign = 'center';
            demoCtx.fillText('交互式桥接模式演示', 400, 30);

            // 当前选择显示
            demoCtx.font = '16px Microsoft YaHei';
            demoCtx.fillText(`当前产品: ${currentProduct || '未选择'}`, 200, 70);
            demoCtx.fillText(`当前出版方式: ${currentPublishMethod || '未选择'}`, 600, 70);

            // 抽象层区域
            demoCtx.fillStyle = currentProduct ? '#4CAF50' : '#E0E0E0';
            demoCtx.fillRect(50, 120, 300, 150);
            demoCtx.fillStyle = 'white';
            demoCtx.font = '18px Microsoft YaHei';
            demoCtx.textAlign = 'center';
            demoCtx.fillText('抽象层 - 宣传产品', 200, 150);

            if (currentProduct) {
                demoCtx.fillStyle = '#FFD700';
                demoCtx.fillRect(100, 180, 200, 60);
                demoCtx.fillStyle = '#333';
                demoCtx.font = 'bold 16px Microsoft YaHei';
                demoCtx.fillText(getProductName(currentProduct), 200, 215);
            }

            // 实现层区域
            demoCtx.fillStyle = currentPublishMethod ? '#2196F3' : '#E0E0E0';
            demoCtx.fillRect(450, 120, 300, 150);
            demoCtx.fillStyle = 'white';
            demoCtx.font = '18px Microsoft YaHei';
            demoCtx.textAlign = 'center';
            demoCtx.fillText('实现层 - 出版方式', 600, 150);

            if (currentPublishMethod) {
                demoCtx.fillStyle = '#FFD700';
                demoCtx.fillRect(500, 180, 200, 60);
                demoCtx.fillStyle = '#333';
                demoCtx.font = 'bold 16px Microsoft YaHei';
                demoCtx.fillText(getMethodName(currentPublishMethod), 600, 215);
            }

            // 桥接连接
            if (currentProduct && currentPublishMethod) {
                demoCtx.strokeStyle = '#FF9800';
                demoCtx.lineWidth = 6;
                demoCtx.beginPath();
                demoCtx.moveTo(350, 195);
                demoCtx.lineTo(450, 195);
                demoCtx.stroke();

                // 桥接标签
                demoCtx.fillStyle = '#FF9800';
                demoCtx.fillRect(375, 175, 50, 40);
                demoCtx.fillStyle = 'white';
                demoCtx.font = 'bold 12px Microsoft YaHei';
                demoCtx.fillText('桥接', 400, 200);
            }

            // 结果显示区域
            demoCtx.fillStyle = '#F5F5F5';
            demoCtx.fillRect(50, 320, 700, 120);
            demoCtx.strokeStyle = '#DDD';
            demoCtx.lineWidth = 2;
            demoCtx.strokeRect(50, 320, 700, 120);

            demoCtx.fillStyle = '#333';
            demoCtx.font = '16px Microsoft YaHei';
            demoCtx.textAlign = 'center';
            demoCtx.fillText('执行结果', 400, 345);

            if (currentProduct && currentPublishMethod) {
                demoCtx.fillStyle = '#4CAF50';
                demoCtx.font = '14px Microsoft YaHei';
                demoCtx.fillText(
                    `${getProductName(currentProduct)} 通过 ${getMethodName(currentPublishMethod)} 方式发布`,
                    400, 375
                );
                demoCtx.fillText('✓ 抽象与实现成功分离，可独立变化', 400, 400);
            } else {
                demoCtx.fillStyle = '#999';
                demoCtx.font = '14px Microsoft YaHei';
                demoCtx.fillText('请先选择产品和出版方式', 400, 380);
            }
        }

        function createProduct(type) {
            currentProduct = type;
            drawDemoScene();

            // 添加选择动画效果
            const productArea = { x: 50, y: 120, width: 300, height: 150 };
            animateSelection(productArea, '#4CAF50');
        }

        function setPublishMethod(method) {
            currentPublishMethod = method;
            drawDemoScene();

            // 添加选择动画效果
            const methodArea = { x: 450, y: 120, width: 300, height: 150 };
            animateSelection(methodArea, '#2196F3');
        }

        function publishProduct() {
            if (currentProduct && currentPublishMethod) {
                // 发布动画
                animatePublish();
            } else {
                alert('请先选择产品和出版方式！');
            }
        }

        function resetDemo() {
            currentProduct = null;
            currentPublishMethod = null;
            drawDemoScene();
        }

        function getProductName(type) {
            const names = {
                'brochure': '宣传册',
                'article': '文章',
                'flyer': '传单'
            };
            return names[type] || type;
        }

        function getMethodName(method) {
            const names = {
                'paper': '纸质出版',
                'cd': 'CD出版',
                'dvd': 'DVD出版',
                'online': '在线发布'
            };
            return names[method] || method;
        }

        function animateSelection(area, color) {
            let frame = 0;
            function animate() {
                if (frame < 20) {
                    demoCtx.strokeStyle = color;
                    demoCtx.lineWidth = 4;
                    demoCtx.setLineDash([10, 5]);
                    demoCtx.strokeRect(area.x - 5, area.y - 5, area.width + 10, area.height + 10);
                    frame++;
                    requestAnimationFrame(animate);
                } else {
                    demoCtx.setLineDash([]);
                    drawDemoScene();
                }
            }
            animate();
        }

        function animatePublish() {
            let frame = 0;
            function animate() {
                drawDemoScene();

                // 发布效果动画
                const progress = frame / 30;
                if (progress <= 1) {
                    // 粒子效果
                    for (let i = 0; i < 10; i++) {
                        const angle = (i / 10) * Math.PI * 2;
                        const radius = progress * 100;
                        const x = 400 + Math.cos(angle) * radius;
                        const y = 380 + Math.sin(angle) * radius;

                        demoCtx.fillStyle = `rgba(255, 215, 0, ${1 - progress})`;
                        demoCtx.beginPath();
                        demoCtx.arc(x, y, 5, 0, Math.PI * 2);
                        demoCtx.fill();
                    }

                    frame++;
                    requestAnimationFrame(animate);
                } else {
                    drawDemoScene();
                    // 显示成功消息
                    demoCtx.fillStyle = '#4CAF50';
                    demoCtx.font = 'bold 18px Microsoft YaHei';
                    demoCtx.textAlign = 'center';
                    demoCtx.fillText('🎉 发布成功！', 400, 420);
                }
            }
            animate();
        }

        // 模式对比功能
        function selectPattern(pattern) {
            // 重置所有卡片
            document.querySelectorAll('.pattern-card').forEach(card => {
                card.classList.remove('correct', 'incorrect');
            });

            const resultDiv = document.getElementById('comparisonResult');
            const selectedCard = document.querySelector(`[onclick="selectPattern('${pattern}')"]`);

            if (pattern === 'bridge') {
                selectedCard.classList.add('correct');
                resultDiv.innerHTML = `
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin-top: 20px;">
                        <h3 style="color: #4CAF50;">✅ 正确选择！</h3>
                        <p><strong>Bridge桥接模式</strong>确实是最适合的选择，因为：</p>
                        <ul style="text-align: left; margin: 10px 0;">
                            <li>将宣传产品（抽象）与出版方式（实现）分离</li>
                            <li>避免了固定的绑定关系</li>
                            <li>两者可以独立变化和扩展</li>
                            <li>减少了类的数量，提高了代码的可维护性</li>
                        </ul>
                    </div>
                `;
            } else {
                selectedCard.classList.add('incorrect');
                const explanations = {
                    'adapter': 'Adapter模式主要用于接口不匹配的情况，而题目中的问题是避免固定绑定关系。',
                    'decorator': 'Decorator模式用于动态添加功能，不适合解决抽象与实现分离的问题。',
                    'facade': 'Facade模式用于简化复杂接口，不是为了分离抽象与实现。'
                };

                resultDiv.innerHTML = `
                    <div style="background: #ffebee; padding: 20px; border-radius: 10px; margin-top: 20px;">
                        <h3 style="color: #f44336;">❌ 不正确</h3>
                        <p>${explanations[pattern]}</p>
                        <p><strong>正确答案是Bridge桥接模式</strong>，因为它专门用于分离抽象与实现。</p>
                    </div>
                `;
            }

            selectedPattern = pattern;
        }

        // 实践练习功能
        function answerQuestion(answer) {
            // 重置所有卡片
            document.querySelectorAll('#practice .pattern-card').forEach(card => {
                card.classList.remove('correct', 'incorrect');
            });

            const resultDiv = document.getElementById('practiceResult');
            const selectedCard = document.querySelector(`[onclick="answerQuestion('${answer}')"]`);

            if (answer === 'C') {
                selectedCard.classList.add('correct');
                resultDiv.innerHTML = `
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; margin-top: 20px;">
                        <h3 style="color: #4CAF50;">🎉 恭喜答对了！</h3>
                        <p><strong>正确答案：C. Bridge桥接模式</strong></p>
                        <p><strong>解析：</strong></p>
                        <ul style="text-align: left; margin: 10px 0;">
                            <li>题目要求避免宣传产品与出版方式之间的固定绑定关系</li>
                            <li>Bridge模式将抽象部分与实现部分分离</li>
                            <li>使抽象和实现都可以独立地变化</li>
                            <li>避免了类爆炸问题（从12个类减少到7个类）</li>
                        </ul>
                        <p style="color: #4CAF50; font-weight: bold;">你已经掌握了Bridge模式的核心概念！</p>
                    </div>
                `;

                // 更新进度到100%
                document.getElementById('progressFill').style.width = '100%';

                // 添加庆祝动画
                celebrateSuccess();
            } else {
                selectedCard.classList.add('incorrect');
                const explanations = {
                    'A': 'Decorator模式用于动态添加功能，不是用来分离抽象与实现的。',
                    'B': 'Adapter模式用于接口适配，解决接口不匹配问题。',
                    'D': 'Facade模式用于简化复杂系统的接口，提供统一入口。'
                };

                resultDiv.innerHTML = `
                    <div style="background: #ffebee; padding: 20px; border-radius: 10px; margin-top: 20px;">
                        <h3 style="color: #f44336;">再想想看 🤔</h3>
                        <p><strong>你选择了：${answer}选项</strong></p>
                        <p>${explanations[answer]}</p>
                        <p><strong>提示：</strong>题目的关键是"不希望在抽象与实现之间有固定的绑定关系"，哪个模式专门解决这个问题？</p>
                    </div>
                `;
            }
        }

        function celebrateSuccess() {
            // 创建庆祝粒子效果
            const celebration = document.createElement('div');
            celebration.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1000;
            `;
            document.body.appendChild(celebration);

            // 创建多个庆祝粒子
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.textContent = ['🎉', '✨', '🌟', '💫'][Math.floor(Math.random() * 4)];
                particle.style.cssText = `
                    position: absolute;
                    font-size: 20px;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation: celebrate 3s ease-out forwards;
                `;
                celebration.appendChild(particle);
            }

            // 添加庆祝动画CSS
            const style = document.createElement('style');
            style.textContent = `
                @keyframes celebrate {
                    0% {
                        transform: translateY(0) rotate(0deg) scale(0);
                        opacity: 1;
                    }
                    50% {
                        transform: translateY(-100px) rotate(180deg) scale(1);
                        opacity: 1;
                    }
                    100% {
                        transform: translateY(-200px) rotate(360deg) scale(0);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);

            // 3秒后清理
            setTimeout(() => {
                document.body.removeChild(celebration);
                document.head.removeChild(style);
            }, 3000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化第一个section
            initSectionAnimation('overview');
            updateProgress('overview');

            // 添加键盘导航
            document.addEventListener('keydown', function(e) {
                const sections = ['overview', 'problem', 'solution', 'demo', 'comparison', 'practice'];
                const currentIndex = sections.indexOf(currentSection);

                if (e.key === 'ArrowRight' && currentIndex < sections.length - 1) {
                    showSection(sections[currentIndex + 1]);
                    updateProgress(sections[currentIndex + 1]);
                } else if (e.key === 'ArrowLeft' && currentIndex > 0) {
                    showSection(sections[currentIndex - 1]);
                    updateProgress(sections[currentIndex - 1]);
                }
            });

            // 添加滚动效果
            window.addEventListener('scroll', function() {
                const sections = document.querySelectorAll('.content-section');
                sections.forEach(section => {
                    const rect = section.getBoundingClientRect();
                    if (rect.top < window.innerHeight && rect.bottom > 0) {
                        section.classList.add('visible');
                    }
                });
            });
        });
    </script>
</body>
</html>
