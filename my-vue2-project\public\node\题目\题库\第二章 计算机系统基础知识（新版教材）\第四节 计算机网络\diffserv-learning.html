<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DiffServ模型交互学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: 600;
            color: #333;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .quiz-title {
            font-size: 2rem;
            color: #8b4513;
            text-align: center;
            margin-bottom: 30px;
        }

        .question {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .options {
            display: grid;
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .option:hover {
            border-color: #667eea;
            background: #e3f2fd;
            transform: translateX(5px);
        }

        .option.correct {
            border-color: #4caf50;
            background: #e8f5e8;
            animation: pulse 0.6s ease-in-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: #ffebee;
            animation: shake 0.6s ease-in-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">DiffServ模型学习</h1>
            <p class="subtitle">通过动画和交互理解网络服务质量</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是IP协议头？</h2>
            <div class="canvas-container">
                <canvas id="ipHeaderCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="animateIPHeader()">显示IP协议头结构</button>
                <button class="btn" onclick="highlightTOSField()">突出显示服务类型字段</button>
                <button class="btn" onclick="resetIPHeader()">重置动画</button>
            </div>
            <div class="explanation">
                <p>IP协议头就像是<span class="highlight">快递包裹的标签</span>，包含了数据包传输所需的所有信息。其中<span class="highlight">服务类型(ToS)字段</span>就是告诉路由器这个包裹有多重要，需要什么样的服务质量。</p>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">DiffServ模型工作原理</h2>
            <div class="canvas-container">
                <canvas id="diffservCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="animateDiffServ()">演示DiffServ工作流程</button>
                <button class="btn" onclick="showDSCPCoding()">显示DSCP编码过程</button>
                <button class="btn" onclick="resetDiffServ()">重置演示</button>
            </div>
            <div class="explanation">
                <p>DiffServ就像是<span class="highlight">高速公路的车道分类系统</span>：</p>
                <p>1. 在网络边界给数据包<span class="highlight">贴上标签</span>（设置DS码点）</p>
                <p>2. 路由器根据标签决定<span class="highlight">走哪条车道</span>（不同的转发优先级）</p>
                <p>3. 重要的数据包走<span class="highlight">快速车道</span>，普通数据包走普通车道</p>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="quiz-title">🎯 练习题目</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="question">
                <h3>IETF定义的区分服务（DiffServ）模型要求每个IP分组都要根据IPv4协议头中的（ ）字段加上一个DS码点，然后内部路由器根据DS码点的值对分组进行调度和转发。</h3>

                <div class="options" id="options">
                    <div class="option" onclick="selectOption(this, 'A', false)">
                        <strong>A. 数据报生存期</strong>
                        <div style="margin-top: 8px; font-size: 0.9em; color: #666;">
                            TTL字段，用于防止数据包在网络中无限循环
                        </div>
                    </div>
                    <div class="option" onclick="selectOption(this, 'B', true)">
                        <strong>B. 服务类型</strong>
                        <div style="margin-top: 8px; font-size: 0.9em; color: #666;">
                            ToS字段，DiffServ正是使用这个字段来标记服务质量
                        </div>
                    </div>
                    <div class="option" onclick="selectOption(this, 'C', false)">
                        <strong>C. 段偏置值</strong>
                        <div style="margin-top: 8px; font-size: 0.9em; color: #666;">
                            Fragment Offset，用于IP分片重组
                        </div>
                    </div>
                    <div class="option" onclick="selectOption(this, 'D', false)">
                        <strong>D. 源地址</strong>
                        <div style="margin-top: 8px; font-size: 0.9em; color: #666;">
                            Source Address，标识数据包的发送方
                        </div>
                    </div>
                </div>

                <div id="explanation" style="display: none; margin-top: 20px; padding: 20px; background: #e8f5e8; border-radius: 10px; border-left: 5px solid #4caf50;">
                    <h4>🎉 答案解析</h4>
                    <p><strong>正确答案：B - 服务类型</strong></p>
                    <p>DiffServ模型的核心就是利用IPv4协议头中的<span class="highlight">服务类型(ToS)字段</span>来实现服务质量区分：</p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>ToS字段有8位，其中前6位用作<span class="highlight">DSCP(区分服务码点)</span></li>
                        <li>网络边界设备根据流量特征设置DSCP值</li>
                        <li>内部路由器根据DSCP值进行<span class="highlight">不同的转发处理</span></li>
                        <li>这样就能为不同类型的流量提供<span class="highlight">差异化服务</span></li>
                    </ul>
                </div>

                <div class="controls" style="margin-top: 20px;">
                    <button class="btn" onclick="showDetailedExplanation()">详细解释DiffServ工作原理</button>
                    <button class="btn" onclick="compareAllOptions()">对比所有选项</button>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🔍 深度理解</h2>
            <div class="canvas-container">
                <canvas id="detailCanvas" width="800" height="600"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showTOSFieldDetail()">ToS字段详细结构</button>
                <button class="btn" onclick="showDSCPValues()">常见DSCP值含义</button>
                <button class="btn" onclick="showQoSComparison()">QoS技术对比</button>
                <button class="btn" onclick="resetDetailCanvas()">重置</button>
            </div>
        </div>
    </div>

    <script>
        // IP协议头动画
        function animateIPHeader() {
            const canvas = document.getElementById('ipHeaderCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制IP协议头结构
            const fields = [
                {name: '版本', x: 50, y: 50, width: 80, height: 40, color: '#ffcdd2'},
                {name: '头部长度', x: 130, y: 50, width: 80, height: 40, color: '#f8bbd9'},
                {name: '服务类型', x: 210, y: 50, width: 120, height: 40, color: '#e1bee7'},
                {name: '总长度', x: 330, y: 50, width: 120, height: 40, color: '#d1c4e9'},
                {name: '标识', x: 50, y: 90, width: 100, height: 40, color: '#c5cae9'},
                {name: '标志', x: 150, y: 90, width: 80, height: 40, color: '#bbdefb'},
                {name: '片偏移', x: 230, y: 90, width: 100, height: 40, color: '#b3e5fc'},
                {name: '生存时间', x: 330, y: 90, width: 120, height: 40, color: '#b2dfdb'}
            ];
            
            let delay = 0;
            fields.forEach((field, index) => {
                setTimeout(() => {
                    // 绘制字段框
                    ctx.fillStyle = field.color;
                    ctx.fillRect(field.x, field.y, field.width, field.height);
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(field.x, field.y, field.width, field.height);
                    
                    // 绘制字段名称
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(field.name, field.x + field.width/2, field.y + field.height/2 + 5);
                    
                    // 添加动画效果
                    animateFieldAppear(ctx, field);
                }, delay);
                delay += 300;
            });
        }
        
        function animateFieldAppear(ctx, field) {
            let scale = 0;
            const animate = () => {
                if (scale < 1) {
                    scale += 0.1;
                    const centerX = field.x + field.width/2;
                    const centerY = field.y + field.height/2;
                    
                    ctx.save();
                    ctx.translate(centerX, centerY);
                    ctx.scale(scale, scale);
                    ctx.translate(-centerX, -centerY);
                    
                    ctx.fillStyle = field.color;
                    ctx.fillRect(field.x, field.y, field.width, field.height);
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(field.x, field.y, field.width, field.height);
                    
                    ctx.restore();
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }
        
        function highlightTOSField() {
            const canvas = document.getElementById('ipHeaderCanvas');
            const ctx = canvas.getContext('2d');
            
            // 突出显示服务类型字段
            ctx.save();
            ctx.shadowColor = '#ff4444';
            ctx.shadowBlur = 20;
            ctx.fillStyle = '#ff6b6b';
            ctx.fillRect(210, 50, 120, 40);
            ctx.strokeStyle = '#ff4444';
            ctx.lineWidth = 4;
            ctx.strokeRect(210, 50, 120, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('服务类型', 270, 75);
            ctx.restore();
            
            // 添加箭头和说明
            drawArrow(ctx, 270, 90, 270, 150);
            ctx.fillStyle = '#333';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('这就是DiffServ使用的字段！', 270, 170);
        }
        
        function drawArrow(ctx, fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#ff4444';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.closePath();
            ctx.fillStyle = '#ff4444';
            ctx.fill();
        }
        
        function resetIPHeader() {
            const canvas = document.getElementById('ipHeaderCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        // DiffServ动画
        function animateDiffServ() {
            const canvas = document.getElementById('diffservCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 第一步：数据包到达网络边界
            setTimeout(() => {
                drawPacket(ctx, 50, 100, '普通数据包', '#e3f2fd');
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('1. 数据包到达网络边界', 50, 50);
            }, 500);
            
            // 第二步：添加DS码点
            setTimeout(() => {
                drawPacket(ctx, 250, 100, 'DS码点:101110', '#c8e6c9');
                drawArrow(ctx, 150, 120, 230, 120);
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('2. 边界路由器添加DS码点', 200, 80);
            }, 1500);
            
            // 第三步：路由器转发
            setTimeout(() => {
                drawRouter(ctx, 450, 80);
                drawArrow(ctx, 350, 120, 430, 120);
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('3. 内部路由器根据DS码点转发', 350, 50);
            }, 2500);
            
            // 第四步：不同优先级处理
            setTimeout(() => {
                drawQueue(ctx, 600, 60, '高优先级队列', '#ffcdd2');
                drawQueue(ctx, 600, 140, '普通优先级队列', '#f0f4c3');
                drawArrow(ctx, 520, 100, 580, 80);
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('4. 不同优先级处理', 550, 30);
            }, 3500);
        }
        
        function drawPacket(ctx, x, y, label, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 100, 40);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 100, 40);
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + 50, y + 25);
        }
        
        function drawRouter(ctx, x, y) {
            ctx.fillStyle = '#ffb74d';
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, 2 * Math.PI);
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('路由器', x, y + 5);
        }
        
        function drawQueue(ctx, x, y, label, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 120, 30);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, 120, 30);
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x + 60, y + 20);
        }
        
        function showDSCPCoding() {
            const canvas = document.getElementById('diffservCanvas');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 显示DSCP编码过程
            ctx.fillStyle = '#333';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('DSCP编码过程', 400, 50);
            
            // 绘制ToS字段
            ctx.fillStyle = '#e1bee7';
            ctx.fillRect(200, 100, 400, 60);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(200, 100, 400, 60);
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('服务类型(ToS)字段 - 8位', 400, 135);
            
            // 显示DSCP位
            const bits = ['D', 'S', 'C', 'P', '1', '2', 'C', 'U'];
            for (let i = 0; i < 8; i++) {
                const x = 220 + i * 45;
                ctx.fillStyle = i < 6 ? '#ffcdd2' : '#c8e6c9';
                ctx.fillRect(x, 180, 40, 40);
                ctx.strokeStyle = '#333';
                ctx.strokeRect(x, 180, 40, 40);
                
                ctx.fillStyle = '#333';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(bits[i], x + 20, 205);
            }
            
            // 标注
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('DSCP (6位)', 310, 250);
            ctx.fillText('未使用 (2位)', 520, 250);
        }
        
        function resetDiffServ() {
            const canvas = document.getElementById('diffservCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        // 题目交互功能
        let answered = false;

        function selectOption(element, option, isCorrect) {
            if (answered) return;

            answered = true;
            const options = document.querySelectorAll('.option');

            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    if (isCorrect) {
                        opt.classList.add('correct');
                    } else {
                        opt.classList.add('wrong');
                    }
                } else if (opt.onclick.toString().includes('true')) {
                    opt.classList.add('correct');
                }
            });

            // 显示解析
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                updateProgress(100);
            }, 1000);

            // 如果答错了，显示正确思路
            if (!isCorrect) {
                setTimeout(() => {
                    showCorrectThinking();
                }, 2000);
            }
        }

        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        function showCorrectThinking() {
            const canvas = document.getElementById('detailCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 显示解题思路
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🤔 解题思路分析', 400, 50);

            const steps = [
                '1. 题目关键词：DiffServ模型、DS码点',
                '2. 问题核心：哪个字段用于添加DS码点？',
                '3. 回忆知识：DiffServ使用ToS字段',
                '4. 排除干扰：其他字段功能不同'
            ];

            steps.forEach((step, index) => {
                setTimeout(() => {
                    ctx.fillStyle = '#555';
                    ctx.font = '18px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText(step, 100, 120 + index * 60);

                    // 添加动画效果
                    animateThinkingStep(ctx, 80, 100 + index * 60, index);
                }, index * 800);
            });
        }

        function animateThinkingStep(ctx, x, y, step) {
            let progress = 0;
            const animate = () => {
                if (progress < 1) {
                    progress += 0.05;

                    ctx.save();
                    ctx.globalAlpha = progress;

                    // 绘制思考气泡
                    ctx.fillStyle = '#e3f2fd';
                    ctx.beginPath();
                    ctx.arc(x, y, 15, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.strokeStyle = '#2196f3';
                    ctx.lineWidth = 2;
                    ctx.stroke();

                    // 绘制步骤编号
                    ctx.fillStyle = '#2196f3';
                    ctx.font = 'bold 14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText((step + 1).toString(), x, y + 5);

                    ctx.restore();
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        function showDetailedExplanation() {
            const canvas = document.getElementById('detailCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 详细解释DiffServ工作原理
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('DiffServ工作原理详解', 400, 40);

            // 绘制完整的工作流程
            setTimeout(() => drawNetworkTopology(ctx), 500);
        }

        function drawNetworkTopology(ctx) {
            // 绘制网络拓扑
            const elements = [
                {type: 'source', x: 50, y: 150, label: '数据源'},
                {type: 'edge', x: 200, y: 150, label: '边界路由器'},
                {type: 'core', x: 350, y: 100, label: '核心路由器1'},
                {type: 'core', x: 350, y: 200, label: '核心路由器2'},
                {type: 'edge', x: 500, y: 150, label: '出口路由器'},
                {type: 'dest', x: 650, y: 150, label: '目标'}
            ];

            elements.forEach((elem, index) => {
                setTimeout(() => {
                    drawNetworkElement(ctx, elem);
                }, index * 300);
            });

            // 绘制连接线
            setTimeout(() => {
                drawConnections(ctx);
                animateDataFlow(ctx);
            }, elements.length * 300);
        }

        function drawNetworkElement(ctx, elem) {
            const colors = {
                source: '#4caf50',
                edge: '#ff9800',
                core: '#2196f3',
                dest: '#9c27b0'
            };

            ctx.fillStyle = colors[elem.type];
            ctx.beginPath();
            ctx.arc(elem.x, elem.y, 25, 0, 2 * Math.PI);
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.fillStyle = '#333';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(elem.label, elem.x, elem.y + 45);
        }

        function drawConnections(ctx) {
            const connections = [
                [75, 150, 175, 150],
                [225, 150, 325, 120],
                [225, 150, 325, 180],
                [375, 120, 475, 150],
                [375, 180, 475, 150],
                [525, 150, 625, 150]
            ];

            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            connections.forEach(([x1, y1, x2, y2]) => {
                ctx.beginPath();
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            });
        }

        function animateDataFlow(ctx) {
            let step = 0;
            const steps = [
                {x: 50, y: 150, text: '1. 原始数据包'},
                {x: 200, y: 150, text: '2. 添加DSCP标记'},
                {x: 350, y: 140, text: '3. 根据DSCP转发'},
                {x: 500, y: 150, text: '4. 维持QoS处理'},
                {x: 650, y: 150, text: '5. 到达目标'}
            ];

            function animateStep() {
                if (step < steps.length) {
                    const currentStep = steps[step];

                    // 绘制数据包
                    ctx.fillStyle = step === 0 ? '#ffcdd2' : '#c8e6c9';
                    ctx.fillRect(currentStep.x - 15, currentStep.y - 30, 30, 20);
                    ctx.strokeStyle = '#333';
                    ctx.strokeRect(currentStep.x - 15, currentStep.y - 30, 30, 20);

                    // 显示说明文字
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(currentStep.text, currentStep.x, currentStep.y + 70);

                    step++;
                    setTimeout(animateStep, 1500);
                }
            }

            animateStep();
        }

        function compareAllOptions() {
            const canvas = document.getElementById('detailCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('各选项详细对比', 400, 40);

            const options = [
                {
                    name: 'A. 数据报生存期 (TTL)',
                    purpose: '防止数据包无限循环',
                    relation: '与QoS无关',
                    color: '#ffcdd2'
                },
                {
                    name: 'B. 服务类型 (ToS)',
                    purpose: 'DiffServ使用此字段',
                    relation: '直接相关 ✓',
                    color: '#c8e6c9'
                },
                {
                    name: 'C. 段偏置值',
                    purpose: 'IP分片重组',
                    relation: '与QoS无关',
                    color: '#ffcdd2'
                },
                {
                    name: 'D. 源地址',
                    purpose: '标识发送方',
                    relation: '与QoS无关',
                    color: '#ffcdd2'
                }
            ];

            options.forEach((option, index) => {
                setTimeout(() => {
                    const y = 100 + index * 120;

                    // 绘制选项框
                    ctx.fillStyle = option.color;
                    ctx.fillRect(50, y, 700, 80);
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(50, y, 700, 80);

                    // 绘制文字
                    ctx.fillStyle = '#333';
                    ctx.font = 'bold 16px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText(option.name, 70, y + 25);

                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('用途: ' + option.purpose, 70, y + 45);
                    ctx.fillText('与DiffServ关系: ' + option.relation, 70, y + 65);

                }, index * 500);
            });
        }

        function showTOSFieldDetail() {
            const canvas = document.getElementById('detailCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('ToS字段详细结构分析', 400, 40);

            // 绘制8位ToS字段
            const bitWidth = 60;
            const startX = 160;
            const y = 100;

            for (let i = 0; i < 8; i++) {
                const x = startX + i * bitWidth;

                // 区分DSCP和ECN部分
                const color = i < 6 ? '#e3f2fd' : '#fff3e0';
                ctx.fillStyle = color;
                ctx.fillRect(x, y, bitWidth, 60);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, bitWidth, 60);

                // 位编号
                ctx.fillStyle = '#333';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(i.toString(), x + bitWidth/2, y + 35);

                // 位名称
                ctx.font = '12px Microsoft YaHei';
                const bitNames = ['D5', 'D4', 'D3', 'D2', 'D1', 'D0', 'ECN1', 'ECN0'];
                ctx.fillText(bitNames[i], x + bitWidth/2, y + 50);
            }

            // 标注说明
            ctx.fillStyle = '#2196f3';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('DSCP (6位)', 280, y + 90);

            ctx.fillStyle = '#ff9800';
            ctx.fillText('ECN (2位)', 520, y + 90);

            // 详细说明
            const explanations = [
                'DSCP (Differentiated Services Code Point):',
                '• 6位编码，提供64种不同的服务类别',
                '• 前3位表示类别，后3位表示丢弃优先级',
                '',
                'ECN (Explicit Congestion Notification):',
                '• 2位用于拥塞通知',
                '• 00: 不支持ECN',
                '• 01/10: 支持ECN',
                '• 11: 遇到拥塞'
            ];

            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'left';
            explanations.forEach((text, index) => {
                ctx.fillText(text, 100, 220 + index * 25);
            });
        }

        function showDSCPValues() {
            const canvas = document.getElementById('detailCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('常见DSCP值及其含义', 400, 40);

            const dscpValues = [
                {value: '000000', name: 'BE (Best Effort)', desc: '尽力而为服务', color: '#f5f5f5'},
                {value: '101110', name: 'EF (Expedited Forwarding)', desc: '加速转发，最高优先级', color: '#ffcdd2'},
                {value: '001010', name: 'AF11', desc: '确保转发类1，低丢弃', color: '#e1f5fe'},
                {value: '001100', name: 'AF12', desc: '确保转发类1，中丢弃', color: '#b3e5fc'},
                {value: '001110', name: 'AF13', desc: '确保转发类1，高丢弃', color: '#81d4fa'},
                {value: '011000', name: 'CS3', desc: '类选择器3', color: '#c8e6c9'}
            ];

            dscpValues.forEach((dscp, index) => {
                setTimeout(() => {
                    const y = 80 + index * 80;

                    // 绘制DSCP值框
                    ctx.fillStyle = dscp.color;
                    ctx.fillRect(50, y, 700, 60);
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(50, y, 700, 60);

                    // 二进制值
                    ctx.fillStyle = '#333';
                    ctx.font = 'bold 16px Courier New';
                    ctx.textAlign = 'left';
                    ctx.fillText(dscp.value, 70, y + 25);

                    // 名称
                    ctx.font = 'bold 16px Microsoft YaHei';
                    ctx.fillText(dscp.name, 200, y + 25);

                    // 描述
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(dscp.desc, 350, y + 25);

                    // 优先级指示器
                    const priority = index === 1 ? '🔴 最高' : index < 3 ? '🟡 高' : '🟢 普通';
                    ctx.fillText(priority, 600, y + 25);

                }, index * 300);
            });
        }

        function showQoSComparison() {
            const canvas = document.getElementById('detailCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('QoS技术对比', 400, 40);

            const qosTechs = [
                {
                    name: 'IntServ (集成服务)',
                    features: ['逐流状态', '资源预留', 'RSVP协议'],
                    pros: '精确QoS保证',
                    cons: '扩展性差',
                    color: '#ffcdd2'
                },
                {
                    name: 'DiffServ (区分服务)',
                    features: ['无状态', '类别标记', '简单实现'],
                    pros: '扩展性好',
                    cons: '相对粗糙',
                    color: '#c8e6c9'
                },
                {
                    name: 'Traffic Engineering',
                    features: ['路径优化', 'MPLS', '负载均衡'],
                    pros: '网络优化',
                    cons: '复杂配置',
                    color: '#e1f5fe'
                }
            ];

            qosTechs.forEach((tech, index) => {
                setTimeout(() => {
                    const x = 50 + index * 250;
                    const y = 100;

                    // 绘制技术框
                    ctx.fillStyle = tech.color;
                    ctx.fillRect(x, y, 200, 300);
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(x, y, 200, 300);

                    // 技术名称
                    ctx.fillStyle = '#333';
                    ctx.font = 'bold 16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(tech.name, x + 100, y + 30);

                    // 特性
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText('特性:', x + 20, y + 60);
                    tech.features.forEach((feature, fIndex) => {
                        ctx.fillText('• ' + feature, x + 20, y + 85 + fIndex * 20);
                    });

                    // 优点
                    ctx.fillStyle = '#4caf50';
                    ctx.font = 'bold 14px Microsoft YaHei';
                    ctx.fillText('优点:', x + 20, y + 170);
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(tech.pros, x + 20, y + 190);

                    // 缺点
                    ctx.fillStyle = '#f44336';
                    ctx.font = 'bold 14px Microsoft YaHei';
                    ctx.fillText('缺点:', x + 20, y + 220);
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(tech.cons, x + 20, y + 240);

                }, index * 500);
            });
        }

        function resetDetailCanvas() {
            const canvas = document.getElementById('detailCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        // 页面加载时的初始动画
        window.addEventListener('load', () => {
            setTimeout(animateIPHeader, 1000);
            updateProgress(0);
        });
    </script>
</body>
</html>
