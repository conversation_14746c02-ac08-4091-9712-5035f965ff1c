<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java面试题交互式讲解</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 30px auto;
            background: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .question-section {
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px dashed #e0e0e0;
        }
        .question-section:last-child {
            border-bottom: none;
        }
        .explanation {
            background-color: #e8f5e9;
            border-left: 5px solid #4caf50;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        canvas {
            display: block;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            margin-top: 20px;
            border-radius: 5px;
        }
        .controls {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 5px;
            border: 1px solid #eee;
        }
        .controls label {
            margin-right: 10px;
            font-weight: bold;
        }
        .controls input[type="number"],
        .controls button {
            padding: 8px 12px;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-size: 1rem;
        }
        .controls button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #0056b3;
        }
        p strong {
            color: #d35400;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Java面试题交互式讲解</h1>

        <div class="question-section">
            <h2>题目 18：用最有效率的方法计算 2 乘以 8</h2>
            <p><strong>题目原文：</strong> 2 乘以 8</p>
            <p class="explanation">
                <strong>答案：</strong> 2 << 3 (左移 3 位相当于乘以 2 的 3 次方，右移 3 位相当于除以 2 的 3 次方)。
                <br>
                在计算机中，位运算是一种非常高效的操作，因为它直接操作二进制位，通常比乘法或除法指令更快。
            </p>
            <h3>知识点讲解与交互演示：位移运算</h3>
            <p>让我们通过动画来理解位移运算的原理。</p>
            <div class="controls">
                <label for="num18">输入一个数字：</label>
                <input type="number" id="num18" value="2" min="0">
                <label for="shift18">左移位数：</label>
                <input type="number" id="shift18" value="3" min="0" max="31">
                <button id="startShiftDemo">开始演示</button>
            </div>
            <canvas id="bitShiftCanvas" width="800" height="250"></canvas>
            <p><strong>解释：</strong> 左移操作 `<<` 将一个数的二进制位向左移动指定的位数。每向左移动一位，相当于将该数乘以 2。所以，向左移动 <code>n</code> 位，就相当于乘以 <code>2<sup>n</sup></code>。</p>
        </div>

        <div class="question-section">
            <h2>题目 19：float f=3.4;是否正确</h2>
            <p><strong>题目原文：</strong> float f=3.4;是否正确</p>
            <p class="explanation">
                <strong>答案：</strong> 不正确。3.4 是双精度数，将双精度型（double）赋值给浮点型（float）属于下转型（down-casting，也称为窄化）会造成精度损失，因此需要强制类型转换 <code>float f =(float)3.4;</code> 或者写成 <code>float f =3.4F;</code>。
                <br>
                在Java中，默认的小数类型是 <code>double</code>。
            </p>
            <h3>知识点讲解与交互演示：数据类型转换与精度</h3>
            <p>让我们通过动画来理解Java中浮点数类型和转换的规则。</p>
            <div class="controls">
                <label for="doubleVal19">输入一个小数：</label>
                <input type="number" id="doubleVal19" value="3.4" step="any">
                <button id="startFloatDemo">开始演示</button>
            </div>
            <canvas id="floatCastingCanvas" width="800" height="250"></canvas>
            <p><strong>解释：</strong> Java中，像 <code>3.4</code> 这样的浮点数字面量默认被认为是 <code>double</code> 类型。<code>double</code> 占用8个字节，提供更高的精度。而 <code>float</code> 占用4个字节，精度较低。将一个高精度类型（<code>double</code>）赋值给一个低精度类型（<code>float</code>）被称为"窄化转换"或"下转型"。这种转换可能会导致精度损失，因此Java编译器会要求你进行显式的强制类型转换，例如 <code>(float)3.4</code>，或者在数字后面添加 <code>F</code> 或 <code>f</code> 后缀，例如 <code>3.4F</code>，明确告诉编译器这是一个 <code>float</code> 类型。</p>
        </div>
    </div>

    <script>
        // JavaScript for interactive demos will go here
        // Question 18: Bit Shift Demo
        const bitShiftCanvas = document.getElementById('bitShiftCanvas');
        const bitShiftCtx = bitShiftCanvas.getContext('2d');
        const num18Input = document.getElementById('num18');
        const shift18Input = document.getElementById('shift18');
        const startShiftDemoBtn = document.getElementById('startShiftDemo');

        let animationFrameId;

        function drawBitRepresentation(ctx, number, yOffset, highlightBit = -1, color = '#333') {
            const binary = number.toString(2);
            const paddedBinary = binary.padStart(16, '0'); // Show 16 bits for clarity

            ctx.font = '20px Arial';
            ctx.fillStyle = color;
            ctx.textAlign = 'center';

            const bitWidth = 35;
            const startX = (ctx.canvas.width - paddedBinary.length * bitWidth) / 2;

            for (let i = 0; i < paddedBinary.length; i++) {
                const bit = paddedBinary[i];
                const x = startX + i * bitWidth + bitWidth / 2;

                if (highlightBit !== -1 && i === paddedBinary.length - 1 - highlightBit) {
                    ctx.fillStyle = 'red';
                    ctx.fillRect(x - bitWidth / 2 + 2, yOffset + 25, bitWidth - 4, 30);
                    ctx.fillStyle = 'white';
                } else {
                    ctx.fillStyle = color;
                }
                ctx.fillText(bit, x, yOffset + 50);
                ctx.fillStyle = color; // Reset color
            }
            ctx.fillText(`十进制: ${number}`, ctx.canvas.width / 2, yOffset + 15);
        }

        function animateBitShift() {
            const num = parseInt(num18Input.value);
            const shift = parseInt(shift18Input.value);
            let currentShift = 0;
            const targetResult = num << shift;

            const animationDuration = 2000; // milliseconds
            const startTime = performance.now();

            function frame(currentTime) {
                const elapsed = currentTime - startTime;
                bitShiftCtx.clearRect(0, 0, bitShiftCanvas.width, bitShiftCanvas.height);

                drawBitRepresentation(bitShiftCtx, num, 0, -1, '#2980b9'); // Original number

                if (elapsed < animationDuration) {
                    currentShift = Math.min(shift, Math.floor((elapsed / animationDuration) * (shift + 1)));

                    // Draw intermediate shifted state
                    const intermediateNum = num << currentShift;
                    drawBitRepresentation(bitShiftCtx, intermediateNum, 100, -1, '#27ae60');

                    bitShiftCtx.fillStyle = '#333';
                    bitShiftCtx.font = '16px Arial';
                    bitShiftCtx.fillText(`左移 ${currentShift} 位...`, bitShiftCanvas.width / 2, bitShiftCanvas.height - 10);

                    animationFrameId = requestAnimationFrame(frame);
                } else {
                    // Final state
                    drawBitRepresentation(bitShiftCtx, targetResult, 100, -1, '#c0392b');
                    bitShiftCtx.fillStyle = '#333';
                    bitShiftCtx.font = '16px Arial';
                    bitShiftCtx.fillText(`最终结果: ${targetResult}`, bitShiftCanvas.width / 2, bitShiftCanvas.height - 10);
                }
            }

            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            animationFrameId = requestAnimationFrame(frame);
        }

        startShiftDemoBtn.addEventListener('click', animateBitShift);

        // Initial draw for bit shift canvas
        animateBitShift();


        // Question 19: Float Casting Demo
        const floatCastingCanvas = document.getElementById('floatCastingCanvas');
        const floatCastingCtx = floatCastingCanvas.getContext('2d');
        const doubleVal19Input = document.getElementById('doubleVal19');
        const startFloatDemoBtn = document.getElementById('startFloatDemo');

        function drawPrecisionDemo() {
            const val = parseFloat(doubleVal19Input.value);
            floatCastingCtx.clearRect(0, 0, floatCastingCanvas.width, floatCastingCanvas.height);

            floatCastingCtx.font = '18px Arial';
            floatCastingCtx.textAlign = 'left';

            const startX = 50;
            let currentY = 30;

            // Double representation
            floatCastingCtx.fillStyle = '#2980b9';
            floatCastingCtx.fillText(`原始值 (double): ${val}`, startX, currentY);
            currentY += 25;
            floatCastingCtx.fillText(`内存大小: 8 字节`, startX + 20, currentY);
            currentY += 25;
            floatCastingCtx.fillText(`精度示例: ${val.toFixed(10)}`, startX + 20, currentY); // Show high precision
            currentY += 40;

            // Float representation (after casting)
            const floatVal = parseFloat(val.toFixed(7)); // Simulate float precision for display
            floatCastingCtx.fillStyle = '#c0392b';
            floatCastingCtx.fillText(`强制转换为 float: (float)${val} 或 ${val}F`, startX, currentY);
            currentY += 25;
            floatCastingCtx.fillText(`结果: ${floatVal}`, startX + 20, currentY);
            currentY += 25;
            floatCastingCtx.fillText(`内存大小: 4 字节`, startX + 20, currentY);
            currentY += 25;
            floatCastingCtx.fillText(`精度示例: ${floatVal.toFixed(7)}`, startX + 20, currentY); // Show lower precision

            floatCastingCtx.fillStyle = '#333';
            floatCastingCtx.textAlign = 'center';
            floatCastingCtx.fillText('注意 float 和 double 的精度差异', floatCastingCanvas.width / 2, floatCastingCanvas.height - 10);
        }

        startFloatDemoBtn.addEventListener('click', drawPrecisionDemo);

        // Initial draw for float casting canvas
        drawPrecisionDemo();
    </script>
</body>
</html> 