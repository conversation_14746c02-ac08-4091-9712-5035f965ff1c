<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>画中画功能演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn.primary {
            background: #409EFF;
            border-color: #409EFF;
        }
        
        .btn.primary:hover {
            background: #66b1ff;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .status.info {
            background: rgba(64, 158, 255, 0.2);
            border: 1px solid rgba(64, 158, 255, 0.5);
        }
        
        .status.success {
            background: rgba(103, 194, 58, 0.2);
            border: 1px solid rgba(103, 194, 58, 0.5);
        }
        
        .status.warning {
            background: rgba(230, 162, 60, 0.2);
            border: 1px solid rgba(230, 162, 60, 0.5);
        }
        
        .status.error {
            background: rgba(245, 108, 108, 0.2);
            border: 1px solid rgba(245, 108, 108, 0.5);
        }
        
        .content-preview {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            min-height: 400px;
            position: relative;
            overflow: hidden;
        }
        
        .content-preview.pip-active {
            border: 3px solid #409EFF;
            box-shadow: 0 0 20px rgba(64, 158, 255, 0.3);
        }
        
        .content-preview::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(64, 158, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
            pointer-events: none;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .pip-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #409EFF;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: none;
        }
        
        .content-preview.pip-active .pip-indicator {
            display: block;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .feature-item h3 {
            margin-top: 0;
            color: #409EFF;
        }
        
        .demo-content {
            line-height: 1.8;
        }
        
        .demo-content h2 {
            color: #409EFF;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 10px;
        }
        
        .demo-content p {
            margin-bottom: 15px;
        }
        
        .highlight {
            background: rgba(255, 235, 59, 0.3);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📺 画中画功能演示</h1>
            <p>Picture-in-Picture (PiP) 让您在浏览其他内容时继续观看当前页面</p>
        </div>

        <div class="demo-section">
            <h2>🎮 控制面板</h2>
            <div class="controls">
                <button class="btn primary" id="startPip" onclick="startPictureInPicture()">
                    📺 开启画中画
                </button>
                <button class="btn" id="stopPip" onclick="stopPictureInPicture()" disabled>
                    ⏹️ 停止画中画
                </button>
                <button class="btn" onclick="updateContent()">
                    🔄 更新内容
                </button>
                <button class="btn" onclick="toggleAnimation()">
                    ✨ 切换动画
                </button>
            </div>
            
            <div id="status" class="status info">
                点击"开启画中画"按钮开始演示
            </div>
        </div>

        <div class="demo-section">
            <h2>📄 演示内容</h2>
            <div class="content-preview" id="contentPreview">
                <div class="pip-indicator">📺 画中画模式</div>
                <div class="demo-content" id="demoContent">
                    <h2>欢迎使用画中画功能</h2>
                    <p>这是一个<span class="highlight">画中画功能</span>的演示页面。当您开启画中画模式后，这个内容区域将会在一个小窗口中显示，您可以：</p>
                    <ul>
                        <li>🔄 继续浏览其他网页或应用程序</li>
                        <li>📱 调整画中画窗口的大小和位置</li>
                        <li>⚡ 实时查看内容更新</li>
                        <li>🎯 保持对重要信息的关注</li>
                    </ul>
                    <p>画中画技术特别适用于：</p>
                    <ul>
                        <li>📚 在线学习和阅读</li>
                        <li>📊 数据监控和仪表板</li>
                        <li>💬 视频会议和直播</li>
                        <li>🎮 游戏攻略和教程</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🌟 功能特性</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h3>🎯 智能内容捕获</h3>
                    <p>自动捕获页面内容并在画中画窗口中实时显示，支持文本、图片和动画效果。</p>
                </div>
                <div class="feature-item">
                    <h3>⚡ 高性能渲染</h3>
                    <p>采用Canvas技术，确保流畅的15fps渲染，平衡性能与视觉效果。</p>
                </div>
                <div class="feature-item">
                    <h3>🎮 便捷控制</h3>
                    <p>支持键盘快捷键和鼠标操作，轻松控制轮播和画中画功能。</p>
                </div>
                <div class="feature-item">
                    <h3>🔧 自适应设计</h3>
                    <p>自动适配不同屏幕尺寸，在各种设备上都能提供最佳体验。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let pipVideo = null;
        let pipCanvas = null;
        let pipContext = null;
        let animationId = null;
        let contentCounter = 0;
        let animationEnabled = true;

        // 检查浏览器支持
        function checkPipSupport() {
            const supported = 'pictureInPictureEnabled' in document;
            const startBtn = document.getElementById('startPip');
            
            if (!supported) {
                startBtn.disabled = true;
                startBtn.textContent = '❌ 不支持画中画';
                updateStatus('您的浏览器不支持画中画功能', 'error');
            } else {
                updateStatus('✅ 浏览器支持画中画功能，点击按钮开始演示', 'success');
            }
            
            return supported;
        }

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        async function startPictureInPicture() {
            if (!checkPipSupport()) return;

            try {
                updateStatus('正在启动画中画...', 'info');

                // 创建canvas
                pipCanvas = document.createElement('canvas');
                pipContext = pipCanvas.getContext('2d');
                pipCanvas.width = 800;
                pipCanvas.height = 600;

                // 创建video元素
                pipVideo = document.createElement('video');
                pipVideo.srcObject = pipCanvas.captureStream(15);
                pipVideo.muted = true;
                pipVideo.autoplay = true;
                pipVideo.style.display = 'none';
                document.body.appendChild(pipVideo);

                // 等待video准备就绪
                await new Promise((resolve) => {
                    pipVideo.addEventListener('loadedmetadata', resolve, { once: true });
                });

                // 进入画中画模式
                await pipVideo.requestPictureInPicture();

                // 更新UI状态
                document.getElementById('startPip').disabled = true;
                document.getElementById('stopPip').disabled = false;
                document.getElementById('contentPreview').classList.add('pip-active');

                // 开始渲染
                startRendering();

                // 监听退出事件
                pipVideo.addEventListener('leavepictureinpicture', stopPictureInPicture, { once: true });

                updateStatus('✅ 画中画已启动！您现在可以切换到其他窗口', 'success');

            } catch (error) {
                console.error('启动画中画失败:', error);
                updateStatus('❌ 启动画中画失败: ' + error.message, 'error');
                cleanup();
            }
        }

        function stopPictureInPicture() {
            try {
                if (document.pictureInPictureElement) {
                    document.exitPictureInPicture();
                }
            } catch (error) {
                console.error('退出画中画失败:', error);
            }

            cleanup();
            updateStatus('画中画已停止', 'info');
        }

        function cleanup() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }

            if (pipVideo && pipVideo.parentNode) {
                pipVideo.parentNode.removeChild(pipVideo);
            }
            pipVideo = null;
            pipCanvas = null;
            pipContext = null;

            document.getElementById('startPip').disabled = false;
            document.getElementById('stopPip').disabled = true;
            document.getElementById('contentPreview').classList.remove('pip-active');
        }

        function startRendering() {
            let frameCount = 0;

            function render() {
                if (!pipContext || !pipCanvas) return;

                // 清空canvas
                pipContext.fillStyle = '#f8f9fa';
                pipContext.fillRect(0, 0, pipCanvas.width, pipCanvas.height);

                // 绘制标题栏
                drawHeader();

                // 绘制内容
                drawContent(frameCount);

                // 绘制底部信息
                drawFooter(frameCount);

                frameCount++;
                if (pipVideo && !pipVideo.paused) {
                    animationId = requestAnimationFrame(render);
                }
            }

            render();
        }

        function drawHeader() {
            // 标题栏背景
            pipContext.fillStyle = '#409EFF';
            pipContext.fillRect(0, 0, pipCanvas.width, 60);

            // 标题
            pipContext.fillStyle = '#ffffff';
            pipContext.font = 'bold 20px Microsoft YaHei';
            pipContext.textAlign = 'left';
            pipContext.fillText('📺 画中画演示', 20, 35);

            // 时间
            const now = new Date();
            pipContext.textAlign = 'right';
            pipContext.font = '14px Microsoft YaHei';
            pipContext.fillText(now.toLocaleTimeString(), pipCanvas.width - 20, 35);
        }

        function drawContent(frameCount) {
            // 内容区域背景
            pipContext.fillStyle = '#ffffff';
            pipContext.fillRect(20, 80, pipCanvas.width - 40, pipCanvas.height - 140);

            // 内容标题
            pipContext.fillStyle = '#333333';
            pipContext.font = 'bold 18px Microsoft YaHei';
            pipContext.textAlign = 'left';
            pipContext.fillText('实时内容演示', 40, 110);

            // 动态内容
            if (animationEnabled) {
                // 绘制动画圆圈
                const centerX = pipCanvas.width / 2;
                const centerY = pipCanvas.height / 2;
                const radius = 30 + Math.sin(frameCount * 0.1) * 10;

                pipContext.fillStyle = `hsl(${frameCount % 360}, 70%, 60%)`;
                pipContext.beginPath();
                pipContext.arc(centerX, centerY, radius, 0, Math.PI * 2);
                pipContext.fill();

                // 绘制旋转的文字
                pipContext.save();
                pipContext.translate(centerX, centerY);
                pipContext.rotate(frameCount * 0.02);
                pipContext.fillStyle = '#ffffff';
                pipContext.font = 'bold 16px Microsoft YaHei';
                pipContext.textAlign = 'center';
                pipContext.fillText('PiP', 0, 5);
                pipContext.restore();
            }

            // 计数器
            pipContext.fillStyle = '#666666';
            pipContext.font = '14px Microsoft YaHei';
            pipContext.textAlign = 'left';
            pipContext.fillText(`帧数: ${frameCount}`, 40, 140);
            pipContext.fillText(`内容更新: ${contentCounter}`, 40, 160);
        }

        function drawFooter(frameCount) {
            // 底部栏
            pipContext.fillStyle = 'rgba(0, 0, 0, 0.8)';
            pipContext.fillRect(0, pipCanvas.height - 40, pipCanvas.width, 40);

            // 状态信息
            pipContext.fillStyle = '#ffffff';
            pipContext.font = '12px Microsoft YaHei';
            pipContext.textAlign = 'center';
            pipContext.fillText('画中画模式运行中 | 点击主窗口控制', pipCanvas.width / 2, pipCanvas.height - 20);
        }

        function updateContent() {
            contentCounter++;
            const content = document.getElementById('demoContent');
            const newParagraph = document.createElement('p');
            newParagraph.innerHTML = `<span class="highlight">更新 #${contentCounter}</span>: 这是第 ${contentCounter} 次内容更新，时间: ${new Date().toLocaleTimeString()}`;
            content.appendChild(newParagraph);

            // 限制内容数量
            const paragraphs = content.querySelectorAll('p');
            if (paragraphs.length > 10) {
                paragraphs[0].remove();
            }

            updateStatus(`内容已更新 #${contentCounter}`, 'success');
        }

        function toggleAnimation() {
            animationEnabled = !animationEnabled;
            updateStatus(`动画已${animationEnabled ? '开启' : '关闭'}`, 'info');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            checkPipSupport();
        });
    </script>
</body>
</html>
