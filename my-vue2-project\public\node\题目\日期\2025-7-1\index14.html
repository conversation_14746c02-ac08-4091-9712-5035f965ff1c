<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UML 用例关系交互解释</title>
    <style>
        /* CSS 样式，用于美化页面 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f0f2f5;
            color: #333;
            padding: 20px;
            box-sizing: border-box;
        }

        #container {
            background-color: white;
            padding: 20px 40px;
            border-radius: 12px;
            box-shadow: 0 6px 12px rgba(0,0,0,0.1);
            text-align: center;
            width: 100%;
            max-width: 800px;
        }

        h1 {
            color: #0056b3;
            font-size: 24px;
        }

        p.question {
            font-size: 18px;
            margin-bottom: 25px;
        }

        .options {
            margin: 20px 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .option {
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .option:hover {
            background-color: #e9f5ff;
            border-color: #007bff;
            transform: translateY(-2px);
        }

        .option.correct {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
            font-weight: bold;
        }

        .option.incorrect {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
            font-weight: bold;
        }
        
        /* 防止选定后还可以hover */
        .option.correct:hover, .option.incorrect:hover {
             transform: translateY(0);
        }

        canvas {
            border: 1px solid #ccc;
            border-radius: 8px;
            margin-top: 20px;
            width: 100%;
            height: auto;
        }

        #explanation {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            min-height: 100px;
            text-align: left;
            line-height: 1.7;
        }

        #explanation h4 {
            margin-top: 0;
            color: #0056b3;
        }
    </style>
</head>
<body>

    <div id="container">
        <h1>题目19</h1>
        <p class="question">在 UML 用例图中，不属于用例与用例之间关系的是 ()。</p>
        
        <div class="options">
            <div class="option" data-option="A">A. 扩展关系 (Extend)</div>
            <div class="option" data-option="B">B. 聚合关系 (Aggregation)</div>
            <div class="option" data-option="C">C. 包含关系 (Include)</div>
            <div class="option" data-option="D">D. 继承关系 (Generalization)</div>
        </div>
        
        <canvas id="umlCanvas" width="800" height="350"></canvas>
        
        <div id="explanation">
            <p>将鼠标悬停在选项上可以查看关系图示。点击选项进行作答。</p>
        </div>
    </div>

    <script>
    // --- JavaScript 和 Canvas 绘图逻辑 ---

    // 1. 获取需要操作的HTML元素
    const canvas = document.getElementById('umlCanvas');
    const ctx = canvas.getContext('2d');
    const explanationDiv = document.getElementById('explanation');
    const options = document.querySelectorAll('.option');

    // 2. 定义一些状态变量
    let currentView = 'initial'; // 当前画布上显示的内容
    let answerSelected = false;  // 用户是否已经作答

    // --- 绘图辅助函数 ---

    // 绘制一个用例（椭圆形）
    function drawUseCase(x, y, label) {
        const rx = 80; // 水平半径
        const ry = 40; // 垂直半径
        ctx.beginPath();
        ctx.ellipse(x, y, rx, ry, 0, 0, 2 * Math.PI);
        ctx.strokeStyle = '#0056b3'; // 边框颜色
        ctx.fillStyle = '#ffffff'; // 填充颜色
        ctx.fill();
        ctx.lineWidth = 2;
        ctx.stroke();
        ctx.fillStyle = '#333'; // 文字颜色
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(label, x, y);
    }

    // 绘制一个类（矩形）
    function drawClass(x, y, label) {
        const width = 140;
        const height = 50;
        ctx.strokeStyle = '#28a745';
        ctx.fillStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.fillRect(x - width / 2, y - height / 2, width, height);
        ctx.strokeRect(x - width / 2, y - height / 2, width, height);
        ctx.fillStyle = '#333';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(label, x, y);
    }
    
    // 绘制带箭头的关系线
    function drawArrow(x1, y1, x2, y2, type) {
        ctx.save();
        ctx.strokeStyle = '#333';
        ctx.fillStyle = '#333';
        ctx.lineWidth = 2;

        ctx.beginPath();
        if (type === 'extend' || type === 'include') {
            ctx.setLineDash([8, 6]); // 虚线
        } else {
            ctx.setLineDash([]); // 实线
        }
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();
        ctx.restore();

        // 绘制箭头
        const headlen = 15;
        const angle = Math.atan2(y2 - y1, x2 - x1);
        ctx.save();
        ctx.translate(x2,y2);
        ctx.rotate(angle);
        ctx.beginPath();
        if (type === 'generalization') { // 继承关系的空心三角箭头
            ctx.moveTo(0,0);
            ctx.lineTo(-headlen, -headlen/2);
            ctx.lineTo(-headlen, headlen/2);
            ctx.closePath();
            ctx.strokeStyle = '#333';
            ctx.fillStyle = 'white';
            ctx.fill();
            ctx.stroke();
        } else { // 扩展和包含关系的普通箭头
            ctx.moveTo(0,0);
            ctx.lineTo(-headlen, -headlen/2);
            ctx.moveTo(0,0);
            ctx.lineTo(-headlen, headlen/2);
            ctx.stroke();
        }
        ctx.restore();
    }

    // 绘制聚合关系（带空心菱形）
    function drawAggregation(x1, y1, x2, y2) {
        ctx.save();
        ctx.lineWidth = 2;
        ctx.strokeStyle = '#28a745';

        // 绘制连接线
        const angle = Math.atan2(y2 - y1, x2 - x1);
        const diamondSize = 12;
        const startX = x1 + diamondSize * 2 * Math.cos(angle);
        const startY = y1 + diamondSize * 2 * Math.sin(angle);
        ctx.beginPath();
        ctx.moveTo(startX, startY);
        ctx.lineTo(x2, y2);
        ctx.stroke();
        
        // 绘制空心菱形
        ctx.translate(x1, y1);
        ctx.rotate(angle);
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(diamondSize, -diamondSize/1.5);
        ctx.lineTo(diamondSize * 2, 0);
        ctx.lineTo(diamondSize, diamondSize/1.5);
        ctx.closePath();
        ctx.fillStyle = 'white';
        ctx.fill();
        ctx.stroke();
        ctx.restore();
    }
    
    // 在关系线上方绘制标签，如 <<extend>>
    function drawLabel(x, y, text) {
        ctx.fillStyle = '#007bff';
        ctx.font = 'italic 16px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(text, x, y);
    }

    // 3. 核心绘图函数，根据 currentView 的值绘制不同内容
    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height); // 每次绘制前清空画布
        
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const x1 = centerX - 250;
        const x2 = centerX + 250;
        const y1 = centerY;

        const useCase1_x = centerX - 180;
        const useCase2_x = centerX + 180;
        
        switch (currentView) {
            case 'A': // 扩展 (Extend)
                drawUseCase(useCase2_x, y1, '处理订单');
                drawUseCase(useCase1_x, y1, '加急处理');
                drawArrow(useCase1_x + 80, y1, useCase2_x - 80, y1, 'extend');
                drawLabel(centerX, y1 - 40, '<<extend>>');
                break;
            case 'B': // 聚合 (Aggregation)
                drawClass(useCase1_x, y1, '汽车 (整体)');
                drawClass(useCase2_x, y1, '轮胎 (部分)');
                // 聚合关系，空心菱形在整体一侧
                drawAggregation(useCase1_x + 70, y1, useCase2_x - 70, y1);
                break;
            case 'C': // 包含 (Include)
                drawUseCase(useCase1_x, y1, '在线购物');
                drawUseCase(useCase2_x, y1, '用户登录');
                drawArrow(useCase1_x + 80, y1, useCase2_x - 80, y1, 'include');
                drawLabel(centerX, y1 - 40, '<<include>>');
                break;
            case 'D': // 继承 (Generalization)
                drawUseCase(useCase2_x, y1, '支付');
                drawUseCase(useCase1_x, y1, '信用卡支付');
                // 继承关系，箭头指向父用例
                drawArrow(useCase1_x + 80, y1, useCase2_x - 80, y1, 'generalization');
                break;
            case 'initial':
            default:
                ctx.fillStyle = '#6c757d';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('请将鼠标悬停在选项上查看图示', centerX, canvas.height / 2);
                break;
        }
    }

    // 4. 更新下方解释区域的文本内容
    function updateExplanation(state) {
        let html = '';
        switch (state) {
            case 'A':
                html = `<h4>扩展 (Extend)</h4><p>一个用例（扩展用例）可以为另一个用例（基础用例）在特定条件下增加一些额外的、可选的行为。它像是一个"插件"。图中，"加急处理"是"处理订单"的一个可选扩展。</p>`;
                break;
            case 'B':
                html = `<h4>聚合 (Aggregation)</h4><p>表示类与类之间"整体与部分"的关系，且部分可以独立于整体存在。例如"汽车"和"轮胎"。<b>这是类图中的关系，不用于描述用例之间的行为关系。</b></p>`;
                break;
            case 'C':
                html = `<h4>包含 (Include)</h4><p>一个用例（基础用例）的行为包含了另一个用例（被包含用例）的行为。这通常是强制性的，是完成基础用例的必要步骤。图中，"在线购物"这个行为必须包含"用户登录"。</p>`;
                break;
            case 'D':
                html = `<h4>继承 (Generalization)</h4><p>一个用例（子用例）继承了另一个用例（父用例）的行为，并可添加或覆盖某些行为。子用例是父用例的一种特殊形式。图中，"信用卡支付"是"支付"的一种具体方式。</p>`;
                break;
            case 'correct':
                html = `<h4>回答正确! <span style="font-size: 24px;">🎉</span></h4><p><b>聚合关系</b>是类图中的概念，用于表示类之间的"整体-部分"结构关系，而不适用于描述用例之间的行为关系。用例之间的三大关系是：<b>扩展（Extend）、包含（Include）和继承（Generalization）</b>。</p>`;
                break;
            case 'incorrect':
                html = `<h4>回答错误!</h4><p>正确答案是 <b>B. 聚合关系</b>。聚合关系用于类图，表示对象之间的组合。用例图中的关系是关于行为的，包括扩展、包含和继承。请再次将鼠标悬停到各个选项上，观察它们的区别。</p>`;
                break;
            default:
                html = '<p>将鼠标悬停在选项上可以查看关系图示。点击选项进行作答。</p>';
        }
        explanationDiv.innerHTML = html;
    }

    // 5. 绑定事件监听器
    options.forEach(option => {
        // 鼠标悬停事件
        option.addEventListener('mouseover', () => {
            if (!answerSelected) { // 如果还没作答
                currentView = option.dataset.option;
                updateExplanation(currentView);
                draw();
            }
        });

        // 鼠标点击事件
        option.addEventListener('click', () => {
            if (answerSelected) return; // 如果已经作答，则不响应
            
            answerSelected = true;
            const selectedOption = option.dataset.option;

            // 移除所有选项的临时样式
            options.forEach(opt => opt.classList.remove('correct', 'incorrect'));

            if (selectedOption === 'B') {
                option.classList.add('correct');
                updateExplanation('correct');
                // 将画布也切换到正确答案的图示
                currentView = 'B';
                draw();
            } else {
                option.classList.add('incorrect');
                // 找到正确答案并高亮
                document.querySelector('[data-option="B"]').classList.add('correct');
                updateExplanation('incorrect');
            }
        });
    });
    
    // 6. 页面加载完成后，首次绘制
    draw();

    </script>
</body>
</html>
