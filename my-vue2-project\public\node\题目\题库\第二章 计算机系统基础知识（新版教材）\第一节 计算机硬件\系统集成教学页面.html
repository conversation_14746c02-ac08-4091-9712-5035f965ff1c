<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统集成方法 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option-card {
            background: #f8f9ff;
            border: 2px solid #e1e5f2;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .option-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .option-card.correct {
            border-color: #4CAF50;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .option-card.wrong {
            border-color: #f44336;
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin: 40px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        #gameCanvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            cursor: pointer;
        }

        .explanation-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .integration-method {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .integration-method:hover {
            transform: translateX(10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .method-title {
            font-size: 1.3rem;
            color: #667eea;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .method-description {
            color: #666;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 1s ease;
            border-radius: 4px;
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }

        .quiz-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .system-icon {
            display: inline-block;
            margin-right: 10px;
            font-size: 1.2em;
        }

        .knowledge-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            transform: translateX(-20px);
            opacity: 0;
            animation: slideInLeft 0.8s ease-out forwards;
        }

        @keyframes slideInLeft {
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .interactive-hint {
            background: rgba(255,255,255,0.9);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- 浮动粒子背景 -->
    <div class="floating-particles" id="particles"></div>

    <div class="container">
        <div class="header">
            <h1 class="title">🏢 系统集成方法学习</h1>
            <p class="subtitle">通过动画和交互，轻松掌握企业系统集成的核心概念</p>
            <div class="interactive-hint">
                <strong>💡 学习提示：</strong> 点击选项卡片选择答案，然后观看动画演示来理解不同的集成方法！
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <h2 style="color: #667eea; margin-bottom: 20px;">📋 题目分析</h2>
            <div class="question-text">
                <span class="system-icon">🏢</span>某公司内部的<span class="highlight">📦 库存管理系统</span>和<span class="highlight">💰 财务系统</span>均为独立开发且具有<span class="highlight">🖥️ C/S结构</span>，公司在进行信息系统改造时，明确指出要采用<span class="highlight">💡 最小的代价</span>实现库存系统和财务系统的<span class="highlight">🔗 一体化操作与管理</span>。针对这种应用集成需求，以下集成方法中，最适合的是（ ）
            </div>
            
            <div class="options-container">
                <div class="option-card" data-option="A">
                    <h3>🗄️ A. 数据集成</h3>
                    <p>整合不同系统的数据库</p>
                    <small style="color: #666; margin-top: 10px; display: block;">需要重构数据库结构</small>
                </div>
                <div class="option-card" data-option="B">
                    <h3>🖥️ B. 界面集成</h3>
                    <p>统一用户操作界面</p>
                    <small style="color: #666; margin-top: 10px; display: block;">代价最小的集成方式</small>
                </div>
                <div class="option-card" data-option="C">
                    <h3>⚙️ C. 方法集成</h3>
                    <p>整合系统功能方法</p>
                    <small style="color: #666; margin-top: 10px; display: block;">需要重写业务逻辑</small>
                </div>
                <div class="option-card" data-option="D">
                    <h3>🔌 D. 接口集成</h3>
                    <p>通过API连接系统</p>
                    <small style="color: #666; margin-top: 10px; display: block;">需要开发API接口</small>
                </div>
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="showAnswer()">查看正确答案</button>
                <button class="btn" onclick="startAnimation()">开始动画演示</button>
            </div>
        </div>

        <div class="canvas-container">
            <h3 style="text-align: center; margin-bottom: 20px; color: #667eea;">🎮 交互式系统集成演示</h3>
            <canvas id="gameCanvas" width="800" height="400"></canvas>
            <div style="text-align: center; margin-top: 15px;">
                <button class="btn" onclick="resetAnimation()">重新演示</button>
                <button class="btn" onclick="nextStep()">下一步</button>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let isAnimating = false;

        // 调整canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        // 系统对象
        class System {
            constructor(x, y, name, color) {
                this.x = x;
                this.y = y;
                this.name = name;
                this.color = color;
                this.width = 120;
                this.height = 80;
                this.opacity = 1;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.opacity;
                
                // 绘制系统框
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x, this.y, this.width, this.height);
                
                // 绘制边框
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.strokeRect(this.x, this.y, this.width, this.height);
                
                // 绘制文字
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(this.name, this.x + this.width/2, this.y + this.height/2 + 5);
                
                ctx.restore();
            }
        }

        // 创建系统实例
        const inventorySystem = new System(100, 150, '库存管理系统', '#4CAF50');
        const financeSystem = new System(300, 150, '财务系统', '#2196F3');

        // 绘制连接线
        function drawConnection(x1, y1, x2, y2, style = 'solid', color = '#666') {
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            
            if (style === 'dashed') {
                ctx.setLineDash([10, 5]);
            } else {
                ctx.setLineDash([]);
            }
            
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }

        // 绘制集成方法演示
        function drawIntegrationMethod(method) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            inventorySystem.draw();
            financeSystem.draw();
            
            switch(method) {
                case 'data':
                    // 数据集成 - 显示数据库连接
                    ctx.fillStyle = '#FF9800';
                    ctx.fillRect(200, 280, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('共享数据库', 250, 315);
                    
                    drawConnection(160, 230, 220, 280, 'solid', '#FF9800');
                    drawConnection(340, 230, 280, 280, 'solid', '#FF9800');
                    break;
                    
                case 'interface':
                    // 界面集成 - 显示统一界面
                    ctx.fillStyle = '#9C27B0';
                    ctx.fillRect(150, 50, 200, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('统一操作界面', 250, 75);
                    
                    drawConnection(200, 90, 160, 150, 'solid', '#9C27B0');
                    drawConnection(300, 90, 340, 150, 'solid', '#9C27B0');
                    break;
                    
                case 'method':
                    // 方法集成 - 显示功能整合
                    ctx.fillStyle = '#795548';
                    ctx.fillRect(180, 280, 140, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('整合功能模块', 250, 315);
                    
                    drawConnection(160, 230, 200, 280, 'solid', '#795548');
                    drawConnection(340, 230, 300, 280, 'solid', '#795548');
                    break;
                    
                case 'api':
                    // 接口集成 - 显示API连接
                    drawConnection(220, 190, 280, 190, 'dashed', '#F44336');
                    
                    ctx.fillStyle = '#F44336';
                    ctx.beginPath();
                    ctx.arc(250, 190, 15, 0, 2 * Math.PI);
                    ctx.fill();
                    
                    ctx.fillStyle = 'white';
                    ctx.font = '10px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('API', 250, 195);
                    break;
            }
        }

        // 动画演示
        function startAnimation() {
            if (isAnimating) return;
            isAnimating = true;
            animationStep = 0;
            
            const methods = ['data', 'interface', 'method', 'api'];
            const methodNames = ['数据集成', '界面集成', '方法集成', '接口集成'];
            
            function animate() {
                if (animationStep < methods.length) {
                    drawIntegrationMethod(methods[animationStep]);
                    
                    // 显示当前方法名称
                    ctx.fillStyle = 'rgba(0,0,0,0.8)';
                    ctx.fillRect(10, 10, 200, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText(`演示: ${methodNames[animationStep]}`, 20, 35);
                    
                    setTimeout(() => {
                        animationStep++;
                        animate();
                    }, 2000);
                } else {
                    // 高亮正确答案
                    drawIntegrationMethod('interface');
                    ctx.fillStyle = 'rgba(76, 175, 80, 0.3)';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    ctx.fillStyle = '#4CAF50';
                    ctx.font = 'bold 20px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('✓ 正确答案：界面集成', canvas.width/2, 50);
                    
                    isAnimating = false;
                    updateProgress(100);
                }
            }
            
            animate();
        }

        // 显示答案
        function showAnswer() {
            const options = document.querySelectorAll('.option-card');
            options.forEach(option => {
                if (option.dataset.option === 'B') {
                    option.classList.add('correct');
                } else if (option.dataset.option === 'D') {
                    option.classList.add('wrong');
                }
            });
            
            updateProgress(50);
        }

        // 重置动画
        function resetAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            inventorySystem.draw();
            financeSystem.draw();
            
            const options = document.querySelectorAll('.option-card');
            options.forEach(option => {
                option.classList.remove('correct', 'wrong', 'selected');
            });
            
            updateProgress(0);
        }

        // 下一步
        function nextStep() {
            if (!isAnimating) {
                showExplanation();
            }
        }

        // 显示详细解释
        function showExplanation() {
            const explanationHTML = `
                <div class="explanation-section">
                    <h2 style="color: #667eea; margin-bottom: 30px;">📚 详细解析</h2>
                    
                    <div class="integration-method">
                        <div class="method-title">🎯 题目关键信息</div>
                        <div class="method-description">
                            • 两个系统都是<strong>独立开发</strong>的C/S结构<br>
                            • 要求<strong>最小代价</strong>实现集成<br>
                            • 需要<strong>一体化操作与管理</strong>
                        </div>
                    </div>

                    <div class="integration-method">
                        <div class="method-title">🔍 四种集成方法对比</div>
                        <div class="method-description">
                            <strong>A. 数据集成</strong> - 需要重构数据库，代价较高<br>
                            <strong>B. 界面集成</strong> - 只需统一界面，代价最小 ✓<br>
                            <strong>C. 方法集成</strong> - 需要重写业务逻辑，代价很高<br>
                            <strong>D. 接口集成</strong> - 需要开发API接口，有一定代价
                        </div>
                    </div>

                    <div class="integration-method">
                        <div class="method-title">💡 为什么选择界面集成？</div>
                        <div class="method-description">
                            界面集成是最经济的集成方式，通过统一的用户界面，用户可以在一个界面中操作多个系统，
                            实现"一体化操作与管理"的需求，而无需修改底层系统架构。
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button class="btn pulse" onclick="startQuiz()">🎮 开始互动测试</button>
                    </div>
                </div>
            `;
            
            document.querySelector('.container').insertAdjacentHTML('beforeend', explanationHTML);
            updateProgress(100);
        }

        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        // 选项点击事件
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.option-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 开始互动测试
        function startQuiz() {
            const quizHTML = `
                <div class="quiz-section" style="background: white; border-radius: 20px; padding: 40px; margin-top: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                    <h2 style="color: #667eea; margin-bottom: 30px;">🎯 知识巩固测试</h2>

                    <div class="quiz-question" style="margin-bottom: 30px;">
                        <h3 style="margin-bottom: 15px;">1. C/S结构是什么意思？</h3>
                        <div class="quiz-options">
                            <button class="quiz-btn" onclick="checkAnswer(this, false)" style="display: block; width: 100%; margin: 10px 0; padding: 15px; border: 2px solid #e1e5f2; background: #f8f9ff; border-radius: 10px; cursor: pointer; transition: all 0.3s;">A. 客户端/服务器结构</button>
                            <button class="quiz-btn" onclick="checkAnswer(this, true)" style="display: block; width: 100%; margin: 10px 0; padding: 15px; border: 2px solid #e1e5f2; background: #f8f9ff; border-radius: 10px; cursor: pointer; transition: all 0.3s;">B. Client/Server结构</button>
                            <button class="quiz-btn" onclick="checkAnswer(this, false)" style="display: block; width: 100%; margin: 10px 0; padding: 15px; border: 2px solid #e1e5f2; background: #f8f9ff; border-radius: 10px; cursor: pointer; transition: all 0.3s;">C. 计算机/系统结构</button>
                        </div>
                    </div>

                    <div class="quiz-question" style="margin-bottom: 30px;">
                        <h3 style="margin-bottom: 15px;">2. 为什么界面集成代价最小？</h3>
                        <div class="quiz-options">
                            <button class="quiz-btn" onclick="checkAnswer(this, false)" style="display: block; width: 100%; margin: 10px 0; padding: 15px; border: 2px solid #e1e5f2; background: #f8f9ff; border-radius: 10px; cursor: pointer; transition: all 0.3s;">A. 不需要任何技术</button>
                            <button class="quiz-btn" onclick="checkAnswer(this, true)" style="display: block; width: 100%; margin: 10px 0; padding: 15px; border: 2px solid #e1e5f2; background: #f8f9ff; border-radius: 10px; cursor: pointer; transition: all 0.3s;">B. 不需要修改底层系统</button>
                            <button class="quiz-btn" onclick="checkAnswer(this, false)" style="display: block; width: 100%; margin: 10px 0; padding: 15px; border: 2px solid #e1e5f2; background: #f8f9ff; border-radius: 10px; cursor: pointer; transition: all 0.3s;">C. 最容易实现</button>
                        </div>
                    </div>

                    <div class="quiz-question">
                        <h3 style="margin-bottom: 15px;">3. 企业系统集成的主要目标是什么？</h3>
                        <div class="quiz-options">
                            <button class="quiz-btn" onclick="checkAnswer(this, false)" style="display: block; width: 100%; margin: 10px 0; padding: 15px; border: 2px solid #e1e5f2; background: #f8f9ff; border-radius: 10px; cursor: pointer; transition: all 0.3s;">A. 减少系统数量</button>
                            <button class="quiz-btn" onclick="checkAnswer(this, true)" style="display: block; width: 100%; margin: 10px 0; padding: 15px; border: 2px solid #e1e5f2; background: #f8f9ff; border-radius: 10px; cursor: pointer; transition: all 0.3s;">B. 实现统一管理和操作</button>
                            <button class="quiz-btn" onclick="checkAnswer(this, false)" style="display: block; width: 100%; margin: 10px 0; padding: 15px; border: 2px solid #e1e5f2; background: #f8f9ff; border-radius: 10px; cursor: pointer; transition: all 0.3s;">C. 提高系统性能</button>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button class="btn" onclick="showFinalSummary()">📋 查看学习总结</button>
                    </div>
                </div>
            `;

            document.querySelector('.container').insertAdjacentHTML('beforeend', quizHTML);
        }

        // 检查答案
        function checkAnswer(button, isCorrect) {
            const buttons = button.parentElement.querySelectorAll('.quiz-btn');
            buttons.forEach(btn => {
                btn.disabled = true;
                if (btn === button) {
                    btn.style.background = isCorrect ? '#4CAF50' : '#f44336';
                    btn.style.color = 'white';
                    btn.style.transform = 'scale(1.05)';
                } else if (btn.onclick.toString().includes('true')) {
                    btn.style.background = '#4CAF50';
                    btn.style.color = 'white';
                }
            });

            // 添加反馈动画
            if (isCorrect) {
                button.innerHTML += ' ✓';
                setTimeout(() => {
                    showCelebration();
                }, 500);
            } else {
                button.innerHTML += ' ✗';
            }
        }

        // 庆祝动画
        function showCelebration() {
            const celebration = document.createElement('div');
            celebration.innerHTML = '🎉';
            celebration.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                font-size: 3rem;
                z-index: 1000;
                animation: celebrationBounce 1s ease-out;
                pointer-events: none;
            `;
            document.body.appendChild(celebration);

            setTimeout(() => {
                document.body.removeChild(celebration);
            }, 1000);
        }

        // 显示最终总结
        function showFinalSummary() {
            const summaryHTML = `
                <div class="final-summary" style="background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 20px; padding: 40px; margin-top: 40px; color: white; text-align: center;">
                    <h2 style="margin-bottom: 30px;">🎓 学习完成！</h2>

                    <div style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 30px; margin: 20px 0;">
                        <h3 style="margin-bottom: 20px;">📝 核心知识点回顾</h3>
                        <div style="text-align: left; line-height: 2;">
                            ✅ 系统集成的四种主要方法<br>
                            ✅ 界面集成适用于最小代价集成需求<br>
                            ✅ C/S结构系统的特点<br>
                            ✅ 企业信息系统改造的考虑因素
                        </div>
                    </div>

                    <div style="margin-top: 30px;">
                        <h3>🌟 恭喜你掌握了系统集成的核心概念！</h3>
                        <p style="margin-top: 15px; opacity: 0.9;">
                            通过这次学习，你已经理解了为什么在最小代价的要求下，
                            界面集成是最适合的解决方案。
                        </p>
                    </div>

                    <button class="btn" onclick="restartLearning()" style="background: white; color: #667eea; margin-top: 20px;">
                        🔄 重新开始学习
                    </button>
                </div>
            `;

            document.querySelector('.container').insertAdjacentHTML('beforeend', summaryHTML);

            // 添加庆祝样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes celebrationBounce {
                    0% { transform: translate(-50%, -50%) scale(0); }
                    50% { transform: translate(-50%, -50%) scale(1.2); }
                    100% { transform: translate(-50%, -50%) scale(1); }
                }
            `;
            document.head.appendChild(style);
        }

        // 重新开始学习
        function restartLearning() {
            location.reload();
        }

        // 创建浮动粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');

            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 添加鼠标悬停效果
        function addHoverEffects() {
            const optionCards = document.querySelectorAll('.option-card');
            optionCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('selected')) {
                        this.style.transform = 'translateY(0) scale(1)';
                    }
                });
            });
        }

        // 添加知识点卡片动画
        function animateKnowledgeCards() {
            const cards = document.querySelectorAll('.integration-method');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.animation = `slideInLeft 0.8s ease-out forwards`;
                    card.style.animationDelay = `${index * 0.2}s`;
                }, index * 200);
            });
        }

        // 初始化所有效果
        function initializeEffects() {
            createParticles();
            addHoverEffects();

            // 延迟执行知识点动画
            setTimeout(() => {
                animateKnowledgeCards();
            }, 1000);
        }

        // 初始化画布和效果
        resetAnimation();
        initializeEffects();
    </script>
</body>
</html>
