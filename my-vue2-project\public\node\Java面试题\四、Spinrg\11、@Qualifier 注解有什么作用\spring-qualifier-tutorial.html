<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spring @Qualifier 注解 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
            line-height: 1.6;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 50px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(30px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2.2rem;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
        }

        .concept-text {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #34495e;
            margin-bottom: 40px;
            text-align: center;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 40px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .bean-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 30px;
        }

        .bean {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .bean:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .bean.mysql {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .bean.postgresql {
            background: linear-gradient(135deg, #4834d4, #686de0);
        }

        .bean.oracle {
            background: linear-gradient(135deg, #ff9ff3, #f368e0);
        }

        .bean.selected {
            transform: scale(1.2);
            box-shadow: 0 0 30px rgba(52, 152, 219, 0.6);
            animation: pulse 2s infinite;
        }

        .qualifier-tag {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #3498db;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .bean.show-qualifier .qualifier-tag {
            opacity: 1;
            top: -25px;
        }

        .autowired-arrow {
            position: absolute;
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 2px;
            opacity: 0;
            transition: all 0.5s ease;
        }

        .autowired-arrow.show {
            opacity: 1;
            animation: flowRight 2s infinite;
        }

        .autowired-arrow::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -6px;
            width: 0;
            height: 0;
            border-left: 12px solid #2ecc71;
            border-top: 7px solid transparent;
            border-bottom: 7px solid transparent;
        }

        .service-class {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            padding: 30px;
            border-radius: 16px;
            text-align: center;
            margin: 30px auto;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(46, 204, 113, 0.3);
        }

        .interactive-button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .interactive-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
        }

        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 30px;
            border-radius: 12px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.95rem;
            line-height: 1.6;
            margin: 30px 0;
            overflow-x: auto;
            position: relative;
        }

        .code-example::before {
            content: 'Java Code';
            position: absolute;
            top: -12px;
            left: 20px;
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .highlight {
            background: rgba(241, 196, 15, 0.3);
            padding: 2px 4px;
            border-radius: 4px;
        }

        .game-area {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin: 40px 0;
        }

        .game-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .score {
            font-size: 1.3rem;
            color: #27ae60;
            font-weight: bold;
            margin-bottom: 20px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                box-shadow: 0 0 30px rgba(52, 152, 219, 0.6);
            }
            50% {
                box-shadow: 0 0 50px rgba(52, 152, 219, 0.9);
            }
        }

        @keyframes flowRight {
            0% {
                transform: translateX(-20px);
                opacity: 0.5;
            }
            50% {
                opacity: 1;
            }
            100% {
                transform: translateX(20px);
                opacity: 0.5;
            }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.6);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2.5rem;
            }
            
            .section {
                padding: 30px 20px;
            }
            
            .bean-container {
                flex-direction: column;
            }
            
            .bean {
                width: 100px;
                height: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">Spring @Qualifier 注解</h1>
            <p class="subtitle">当有多个相同类型的Bean时，如何精确指定装配哪一个？</p>
        </div>

        <div class="section">
            <h2 class="section-title">🤔 问题场景</h2>
            <p class="concept-text">
                想象一下，你的应用程序需要连接数据库。但是你有多个数据库：MySQL、PostgreSQL、Oracle。
                Spring容器中有3个DataSource类型的Bean，当你使用@Autowired时，Spring不知道该注入哪一个！
            </p>
            
            <div class="demo-area">
                <div class="bean-container" id="problemDemo">
                    <div class="bean mysql" data-type="mysql">
                        <div>MySQL<br>DataSource</div>
                    </div>
                    <div class="bean postgresql" data-type="postgresql">
                        <div>PostgreSQL<br>DataSource</div>
                    </div>
                    <div class="bean oracle" data-type="oracle">
                        <div>Oracle<br>DataSource</div>
                    </div>
                </div>
                
                <div class="service-class">
                    <h3>UserService</h3>
                    <p>@Autowired DataSource dataSource;</p>
                    <p style="color: #e74c3c; margin-top: 10px;">❌ Spring不知道注入哪个！</p>
                </div>
                
                <button class="interactive-button" onclick="showProblem()">演示问题</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">✨ @Qualifier 解决方案</h2>
            <p class="concept-text">
                @Qualifier注解就像给每个Bean贴上标签，告诉Spring："我要的就是这个特定的Bean！"
                它与@Autowired配合使用，通过Bean的名称或自定义标识符来精确指定。
            </p>

            <div class="demo-area">
                <div class="bean-container" id="solutionDemo">
                    <div class="bean mysql" data-type="mysql">
                        <div class="qualifier-tag">@Qualifier("mysqlDB")</div>
                        <div>MySQL<br>DataSource</div>
                    </div>
                    <div class="bean postgresql" data-type="postgresql">
                        <div class="qualifier-tag">@Qualifier("postgresDB")</div>
                        <div>PostgreSQL<br>DataSource</div>
                    </div>
                    <div class="bean oracle" data-type="oracle">
                        <div class="qualifier-tag">@Qualifier("oracleDB")</div>
                        <div>Oracle<br>DataSource</div>
                    </div>
                </div>

                <div class="autowired-arrow" id="arrow" style="top: 50%; left: 45%;"></div>

                <div class="service-class">
                    <h3>UserService</h3>
                    <p>@Autowired</p>
                    <p><span class="highlight">@Qualifier("mysqlDB")</span></p>
                    <p>DataSource dataSource;</p>
                    <p style="color: #27ae60; margin-top: 10px;">✅ 精确指定MySQL数据源！</p>
                </div>

                <button class="interactive-button" onclick="showSolution()">演示解决方案</button>
                <button class="interactive-button" onclick="switchDatabase()">切换数据库</button>
            </div>

            <div class="code-example">
// Bean定义
@Configuration
public class DatabaseConfig {

    @Bean
    <span class="highlight">@Qualifier("mysqlDB")</span>
    public DataSource mysqlDataSource() {
        return new MySQLDataSource();
    }

    @Bean
    <span class="highlight">@Qualifier("postgresDB")</span>
    public DataSource postgresDataSource() {
        return new PostgreSQLDataSource();
    }

    @Bean
    <span class="highlight">@Qualifier("oracleDB")</span>
    public DataSource oracleDataSource() {
        return new OracleDataSource();
    }
}

// 使用@Qualifier指定具体的Bean
@Service
public class UserService {

    @Autowired
    <span class="highlight">@Qualifier("mysqlDB")</span>
    private DataSource dataSource;

    // 现在Spring知道要注入MySQL数据源了！
}
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 互动游戏：Bean配对挑战</h2>
            <div class="game-area">
                <h3 class="game-title">帮助Spring找到正确的Bean！</h3>
                <div class="score" id="score">得分: 0</div>
                <p style="margin-bottom: 20px;">点击正确的Bean来满足@Qualifier的要求</p>

                <div id="gameChallenge" style="margin: 30px 0;">
                    <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                        <h4>需要注入：<span id="requiredQualifier" style="color: #e74c3c;">@Qualifier("postgresDB")</span></h4>
                    </div>

                    <div class="bean-container" id="gameBeansContainer">
                        <div class="bean mysql game-bean" data-qualifier="mysqlDB" onclick="selectGameBean(this)">
                            <div>MySQL<br>DataSource</div>
                        </div>
                        <div class="bean postgresql game-bean" data-qualifier="postgresDB" onclick="selectGameBean(this)">
                            <div>PostgreSQL<br>DataSource</div>
                        </div>
                        <div class="bean oracle game-bean" data-qualifier="oracleDB" onclick="selectGameBean(this)">
                            <div>Oracle<br>DataSource</div>
                        </div>
                    </div>
                </div>

                <button class="interactive-button" onclick="startNewGame()">开始新游戏</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📚 知识要点总结</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-top: 30px;">
                <div style="background: #ecf0f1; padding: 25px; border-radius: 12px; border-left: 5px solid #3498db;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🎯 @Qualifier的作用</h4>
                    <p style="color: #34495e; line-height: 1.6;">当存在多个相同类型的Bean时，@Qualifier通过名称或标识符精确指定要注入的Bean，消除歧义。</p>
                </div>

                <div style="background: #ecf0f1; padding: 25px; border-radius: 12px; border-left: 5px solid #27ae60;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🔧 使用方式</h4>
                    <p style="color: #34495e; line-height: 1.6;">与@Autowired配合使用，可以放在字段、方法参数、构造函数参数上。</p>
                </div>

                <div style="background: #ecf0f1; padding: 25px; border-radius: 12px; border-left: 5px solid #e74c3c;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">⚠️ 注意事项</h4>
                    <p style="color: #34495e; line-height: 1.6;">Qualifier的值必须与Bean定义时的名称或@Qualifier标识符完全匹配。</p>
                </div>

                <div style="background: #ecf0f1; padding: 25px; border-radius: 12px; border-left: 5px solid #f39c12;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">💡 最佳实践</h4>
                    <p style="color: #34495e; line-height: 1.6;">使用有意义的名称，保持一致性，避免硬编码字符串，考虑使用常量。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建浮动粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');

            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 演示问题场景
        function showProblem() {
            const beans = document.querySelectorAll('#problemDemo .bean');
            const serviceClass = document.querySelector('#problemDemo .service-class');

            // 重置状态
            beans.forEach(bean => {
                bean.classList.remove('selected');
                bean.style.animation = '';
            });

            // 显示困惑动画
            setTimeout(() => {
                beans.forEach((bean, index) => {
                    setTimeout(() => {
                        bean.style.animation = 'pulse 0.5s ease-in-out';
                        setTimeout(() => {
                            bean.style.animation = '';
                        }, 500);
                    }, index * 200);
                });
            }, 300);

            // 显示错误信息
            setTimeout(() => {
                serviceClass.style.animation = 'pulse 1s ease-in-out 3';
            }, 1000);
        }

        // 演示解决方案
        function showSolution() {
            const beans = document.querySelectorAll('#solutionDemo .bean');
            const arrow = document.getElementById('arrow');

            // 显示所有qualifier标签
            beans.forEach(bean => {
                bean.classList.add('show-qualifier');
            });

            // 选中MySQL bean
            setTimeout(() => {
                const mysqlBean = document.querySelector('#solutionDemo .bean.mysql');
                mysqlBean.classList.add('selected');
                arrow.classList.add('show');
            }, 1000);
        }

        // 切换数据库演示
        let currentDB = 0;
        const databases = ['mysql', 'postgresql', 'oracle'];
        const qualifiers = ['mysqlDB', 'postgresDB', 'oracleDB'];

        function switchDatabase() {
            const beans = document.querySelectorAll('#solutionDemo .bean');
            const serviceClass = document.querySelector('#solutionDemo .service-class');
            const arrow = document.getElementById('arrow');

            // 清除当前选择
            beans.forEach(bean => bean.classList.remove('selected'));

            // 切换到下一个数据库
            currentDB = (currentDB + 1) % databases.length;

            setTimeout(() => {
                const selectedBean = document.querySelector(`#solutionDemo .bean.${databases[currentDB]}`);
                selectedBean.classList.add('selected');

                // 更新服务类显示
                const qualifierSpan = serviceClass.querySelector('.highlight');
                qualifierSpan.textContent = `@Qualifier("${qualifiers[currentDB]}")`;

                // 重新显示箭头
                arrow.classList.remove('show');
                setTimeout(() => arrow.classList.add('show'), 100);
            }, 300);
        }

        // 游戏逻辑
        let gameScore = 0;
        let currentChallenge = '';
        const challenges = ['mysqlDB', 'postgresDB', 'oracleDB'];

        function startNewGame() {
            gameScore = 0;
            updateScore();
            generateNewChallenge();
        }

        function generateNewChallenge() {
            currentChallenge = challenges[Math.floor(Math.random() * challenges.length)];
            document.getElementById('requiredQualifier').textContent = `@Qualifier("${currentChallenge}")`;

            // 重置游戏bean状态
            const gameBeans = document.querySelectorAll('.game-bean');
            gameBeans.forEach(bean => {
                bean.classList.remove('selected');
                bean.style.pointerEvents = 'auto';
            });
        }

        function selectGameBean(bean) {
            const selectedQualifier = bean.getAttribute('data-qualifier');
            const gameBeans = document.querySelectorAll('.game-bean');

            // 禁用所有bean的点击
            gameBeans.forEach(b => b.style.pointerEvents = 'none');

            if (selectedQualifier === currentChallenge) {
                // 正确答案
                bean.classList.add('selected');
                gameScore += 10;
                updateScore();

                // 显示成功动画
                bean.style.animation = 'pulse 0.5s ease-in-out 3';

                setTimeout(() => {
                    bean.style.animation = '';
                    generateNewChallenge();
                }, 2000);
            } else {
                // 错误答案
                bean.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
                bean.style.animation = 'pulse 0.3s ease-in-out 5';

                setTimeout(() => {
                    bean.style.background = '';
                    bean.style.animation = '';
                    // 重新启用点击
                    gameBeans.forEach(b => b.style.pointerEvents = 'auto');
                }, 1500);
            }
        }

        function updateScore() {
            document.getElementById('score').textContent = `得分: ${gameScore}`;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            generateNewChallenge();

            // 添加滚动动画效果
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // 观察所有section元素
            document.querySelectorAll('.section').forEach(section => {
                observer.observe(section);
            });
        });
    </script>
</body>
</html>
