<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式操作系统知识解析</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #0056b3;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-section, .explanation-section, .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fdfdfd;
        }
        .question-section p {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .options div {
            margin-bottom: 10px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .options div:hover {
            background-color: #e9f0f8;
        }
        .options div.selected {
            background-color: #d1e2f8;
            border-color: #0056b3;
        }
        .options div.correct {
            background-color: #d4edda;
            border-color: #28a745;
        }
        .options div.incorrect {
            background-color: #f8d7da;
            border-color: #dc3545;
        }
        button {
            display: block;
            margin: 20px auto 0;
            padding: 12px 25px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background-color: #0056b3;
        }
        .explanation-section h3, .demo-section h3 {
            color: #007bff;
            margin-bottom: 15px;
            text-align: center;
        }
        .explanation-section p {
            margin-bottom: 10px;
            text-indent: 2em;
        }
        #gui-animation-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin-top: 20px;
            height: 150px;
            background-color: #e9edf1;
            border-radius: 8px;
            padding: 10px;
        }
        .gui-item {
            width: 45%;
            height: 120px;
            background-color: #ffc107;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            color: #333;
            transition: all 0.5s ease-in-out;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .gui-item.embedded-os {
            background-color: #28a745;
            color: white;
        }
        .gui-item p {
            margin: 5px 0;
        }
        .gui-item .detail {
            font-size: 0.9em;
            font-weight: normal;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        .gui-item.active .detail {
            opacity: 1;
            transform: translateY(0);
        }

        #rms-canvas {
            border: 1px solid #ccc;
            background-color: #fff;
            display: block;
            margin: 20px auto;
        }
        .demo-controls {
            text-align: center;
            margin-top: 15px;
        }
        .demo-controls button {
            display: inline-block;
            margin: 0 10px;
            background-color: #6c757d;
        }
        .demo-controls button:hover {
            background-color: #5a6268;
        }
        ul {
            list-style-type: none;
            padding: 0;
            text-align: center;
        }
        ul li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>嵌入式操作系统知识解析</h1>

        <div class="question-section">
            <p>题目16：嵌入式操作系统与通用操作系统相比，通常不具有（）特点；下列嵌入式操作系统的调度算法中，（）是基于任务周期的。</p>
            <div class="options">
                <div data-value="A">A. EDF (Earliest Deadline First)</div>
                <div data-value="B">B. LLF (Least Laxity First)</div>
                <div data-value="C">C. RMS (Rate Monotonic Scheduling)</div>
                <div data-value="D">D. 时间片轮转 (Round Robin)</div>
            </div>
            <button id="submit-answer">提交答案</button>
            <div id="feedback" style="text-align: center; margin-top: 15px; font-weight: bold;"></div>
        </div>

        <div class="explanation-section" style="display: none;">
            <h3>题目解析</h3>
            <p><strong>第一空：</strong> 嵌入式操作系统通常不具备<strong>丰富的图形用户界面（GUI）</strong>。通用操作系统（如Windows, macOS）以其复杂的图形界面著称，而嵌入式系统（如洗衣机控制板、智能手环固件）则更侧重于实时响应、资源效率、可裁剪性和可移植性，往往不需要或只有非常简单的用户界面。</p>
            <p style="text-align: center;"><strong>知识点一：嵌入式操作系统与通用操作系统特性对比动画演示</strong></p>
            <div id="gui-animation-container">
                <div class="gui-item general-os">
                    <p>通用操作系统</p>
                    <p class="detail">✅ 丰富图形用户界面 (GUI)</p>
                    <p class="detail">❌ 实时性不保证</p>
                </div>
                <div class="gui-item embedded-os">
                    <p>嵌入式操作系统</p>
                    <p class="detail">✅ 实时性</p>
                    <p class="detail">✅ 可裁剪性</p>
                    <p class="detail">✅ 可移植性</p>
                    <p class="detail">❌ 通常无丰富GUI</p>
                </div>
            </div>
            <p style="text-align: center; font-style: italic; margin-top: 10px;">点击上方方块查看特性细节</p>
            <br>
            <p><strong>第二空：</strong> 题目中的"基于任务周期的"调度算法是 <strong>RMS (Rate Monotonic Scheduling)</strong>。RMS是实时操作系统中一种常用的静态优先级调度算法。它根据任务的周期来确定优先级：**任务周期越短，其优先级越高。**</p>
            <p style="text-align: center;"><strong>知识点二：RMS 调度算法交互演示</strong></p>
            <p>在实时系统中，任务的"周期"是指任务重复执行的时间间隔。RMS 算法会为每个周期性任务分配一个固定的优先级，并且在运行时，总是优先执行当前优先级最高的任务。</p>
            <canvas id="rms-canvas" width="700" height="300"></canvas>
            <div class="demo-controls">
                <button id="start-rms-demo">开始演示</button>
                <button id="reset-rms-demo">重置演示</button>
                <p><strong>任务定义与优先级 (周期越短，优先级越高):</strong></p>
                <ul>
                    <li><span style="color:#f44336;font-weight:bold;">T1</span>: 周期 50, 执行时间 10 (高优先级)</li>
                    <li><span style="color:#2196f3;font-weight:bold;">T2</span>: 周期 80, 执行时间 15 (中优先级)</li>
                    <li><span style="color:#4caf50;font-weight:bold;">T3</span>: 周期 120, 执行时间 20 (低优先级)</li>
                </ul>
                <p>观察它们如何按照优先级顺序执行，高优先级任务可以抢占低优先级任务。</p>
            </div>
        </div>
    </div>

    <script>
        const optionsContainer = document.querySelector('.options');
        const submitButton = document.getElementById('submit-answer');
        const feedbackDiv = document.getElementById('feedback');
        const explanationSection = document.querySelector('.explanation-section');
        let selectedOption = null;

        optionsContainer.addEventListener('click', (event) => {
            const target = event.target.closest('div');
            if (target && target.parentElement === optionsContainer) {
                if (selectedOption) {
                    selectedOption.classList.remove('selected');
                }
                selectedOption = target;
                selectedOption.classList.add('selected');
            }
        });

        submitButton.addEventListener('click', () => {
            if (!selectedOption) {
                feedbackDiv.textContent = '请选择一个答案！';
                feedbackDiv.style.color = '#dc3545';
                return;
            }

            const correctAnswer = 'C';
            const userAnswer = selectedOption.dataset.value;

            // Clear previous feedback classes
            Array.from(optionsContainer.children).forEach(option => {
                option.classList.remove('correct', 'incorrect');
            });

            if (userAnswer === correctAnswer) {
                selectedOption.classList.add('correct');
                feedbackDiv.textContent = '恭喜你，回答正确！';
                feedbackDiv.style.color = '#28a745';
            } else {
                selectedOption.classList.add('incorrect');
                // Highlight the correct answer as well
                document.querySelector(`.options div[data-value="${correctAnswer}"]`).classList.add('correct');
                feedbackDiv.textContent = `回答错误。正确答案是 ${correctAnswer}。`;
                feedbackDiv.style.color = '#dc3545';
            }

            // Disable further interaction and show explanation
            optionsContainer.style.pointerEvents = 'none';
            submitButton.style.display = 'none';
            setTimeout(() => {
                explanationSection.style.display = 'block';
                explanationSection.scrollIntoView({ behavior: 'smooth' });
            }, 1000);
        });

        // GUI Animation Logic
        const guiItems = document.querySelectorAll('.gui-item');

        guiItems.forEach(item => {
            item.addEventListener('click', () => {
                guiItems.forEach(i => i.classList.remove('active'));
                item.classList.add('active');
            });
        });

        // Initialize: show details for the general-os box initially, user can click to switch
        document.querySelector('.gui-item.general-os').classList.add('active');


        // RMS Canvas Demo Logic
        const rmsCanvas = document.getElementById('rms-canvas');
        const ctx = rmsCanvas.getContext('2d');
        const startRmsButton = document.getElementById('start-rms-demo');
        const resetRmsButton = document.getElementById('reset-rms-demo');

        const tasks = [
            { id: 'T1', period: 50, executionTime: 10, color: '#f44336', priority: 0, laneIndex: 0 },
            { id: 'T2', period: 80, executionTime: 15, color: '#2196f3', priority: 0, laneIndex: 1 },
            { id: 'T3', period: 120, executionTime: 20, color: '#4caf50', priority: 0, laneIndex: 2 }
        ];

        // Assign priorities based on period (shorter period = higher priority)
        tasks.sort((a, b) => a.period - b.period);
        tasks.forEach((task, index) => {
            task.priority = tasks.length - index;
            task.laneIndex = index;
        });

        const SIMULATION_DURATION = 250;
        const SCALE_X = (rmsCanvas.width - 150) / SIMULATION_DURATION; // Pixels per time unit, leaving space for labels
        const TASK_HEIGHT = 40;
        const TASK_SPACING = 10;
        const START_Y = 50;
        const INFO_OFFSET_X = 150; // Offset for task info text

        let schedule = []; // Stores [{taskId, startTime, endTime, color, laneIndex}]
        let animationTime = 0;
        let animationFrameId;

        function initializeTasksForSimulation() {
            return tasks.map(task => ({
                ...task,
                nextRelease: 0, // Next time this task instance is released
                remainingExecution: task.executionTime, // How much execution time is left for this instance
                currentDeadline: task.period // Simple deadline for visualization
            }));
        }

        function precomputeRMSchedule() {
            let simulatedTasks = initializeTasksForSimulation();
            let currentSimTime = 0;
            schedule = [];

            // Array to store what task runs at each time unit
            const timeline = Array(SIMULATION_DURATION).fill(null); // null means idle

            while (currentSimTime < SIMULATION_DURATION) {
                let highestPriorityReadyTask = null;

                simulatedTasks.forEach(task => {
                    // Check if a new instance of the task is released
                    // This logic makes sure 'remainingExecution' is reset at each period.
                    while (currentSimTime >= task.nextRelease + task.period && task.remainingExecution === task.executionTime) {
                        task.nextRelease += task.period;
                        task.currentDeadline = task.nextRelease + task.period;
                    }

                    if (currentSimTime >= task.nextRelease && task.remainingExecution > 0) {
                        if (!highestPriorityReadyTask || task.priority > highestPriorityReadyTask.priority) {
                            highestPriorityReadyTask = task;
                        }
                    }
                });

                if (highestPriorityReadyTask) {
                    timeline[currentSimTime] = highestPriorityReadyTask.id;
                    highestPriorityReadyTask.remainingExecution--;

                    // If task completed its current instance
                    if (highestPriorityReadyTask.remainingExecution === 0) {
                        highestPriorityReadyTask.nextRelease += highestPriorityReadyTask.period;
                        highestPriorityReadyTask.currentDeadline = highestPriorityReadyTask.nextRelease + highestPriorityReadyTask.period;
                    }
                } else {
                    timeline[currentSimTime] = null; // CPU idle
                }
                currentSimTime++;
            }

            // Convert timeline array into segments for drawing
            let currentSegment = null;
            for (let i = 0; i < SIMULATION_DURATION; i++) {
                const taskId = timeline[i];
                if (taskId !== null) {
                    const task = tasks.find(t => t.id === taskId);
                    if (currentSegment && currentSegment.taskId === taskId) {
                        currentSegment.endTime = i + 1; // Extend segment
                    } else {
                        if (currentSegment) {
                            schedule.push(currentSegment); // Push previous segment
                        }
                        currentSegment = {
                            taskId: taskId,
                            startTime: i,
                            endTime: i + 1,
                            color: task.color,
                            laneIndex: task.laneIndex
                        };
                    }
                } else { // CPU idle
                    if (currentSegment) {
                        schedule.push(currentSegment); // Push previous task segment
                        currentSegment = null;
                    }
                }
            }
            if (currentSegment) {
                schedule.push(currentSegment); // Push the last segment if any
            }
        }


        function drawRmsSchedule(animationProgressTime) {
            ctx.clearRect(0, 0, rmsCanvas.width, rmsCanvas.height);
            ctx.strokeStyle = '#eee';
            ctx.lineWidth = 1;

            // Draw time axis
            ctx.beginPath();
            ctx.moveTo(INFO_OFFSET_X, START_Y - 20);
            ctx.lineTo(rmsCanvas.width, START_Y - 20);
            ctx.stroke();

            ctx.fillStyle = '#666';
            ctx.font = '10px Arial';
            for (let i = 0; i <= SIMULATION_DURATION; i += 20) {
                const x = INFO_OFFSET_X + i * SCALE_X;
                ctx.fillText(i, x - 5, START_Y - 25); // Adjust text position
                ctx.beginPath();
                ctx.moveTo(x, START_Y - 20);
                ctx.lineTo(x, START_Y - 15);
                ctx.stroke();
            }

            // Draw task lanes and labels
            tasks.forEach((task, index) => {
                const y = START_Y + index * (TASK_HEIGHT + TASK_SPACING);
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText(`任务 ${task.id}`, 5, y + TASK_HEIGHT / 2 + 5);
                ctx.strokeStyle = '#ddd';
                ctx.beginPath();
                ctx.moveTo(INFO_OFFSET_X, y);
                ctx.lineTo(rmsCanvas.width, y);
                ctx.stroke();
            });

            // Draw scheduled segments up to current animation time
            schedule.forEach(segment => {
                const startPixel = INFO_OFFSET_X + segment.startTime * SCALE_X;
                const endPixel = INFO_OFFSET_X + Math.min(segment.endTime, animationProgressTime) * SCALE_X;
                const width = endPixel - startPixel;

                if (width > 0 && segment.startTime < animationProgressTime) {
                    const y = START_Y + segment.laneIndex * (TASK_HEIGHT + TASK_SPACING);
                    ctx.fillStyle = segment.color;
                    ctx.fillRect(startPixel, y, width, TASK_HEIGHT);
                    ctx.strokeStyle = '#333'; // Border for clarity
                    ctx.strokeRect(startPixel, y, width, TASK_HEIGHT);
                }
            });

            // Draw current time marker
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            const markerX = INFO_OFFSET_X + animationProgressTime * SCALE_X;
            ctx.beginPath();
            ctx.moveTo(markerX, START_Y - 30);
            ctx.lineTo(markerX, rmsCanvas.height - 10);
            ctx.stroke();
            ctx.fillStyle = '#007bff';
            ctx.font = '14px Arial';
            ctx.fillText(`当前时间: ${Math.floor(animationProgressTime)}`, Math.max(INFO_OFFSET_X, markerX - 60), START_Y - 35);
        }

        function animateRms() {
            if (animationTime <= SIMULATION_DURATION) {
                drawRmsSchedule(animationTime);
                animationTime++;
                animationFrameId = requestAnimationFrame(animateRms);
            } else {
                startRmsButton.disabled = true;
                resetRmsButton.disabled = false;
                feedbackDiv.textContent = 'RMS调度演示完成。';
                feedbackDiv.style.color = '#0056b3';
            }
        }

        function resetRmsDemo() {
            cancelAnimationFrame(animationFrameId);
            animationTime = 0;
            schedule = [];
            drawRmsSchedule(0);
            startRmsButton.disabled = false;
            resetRmsButton.disabled = true;
            feedbackDiv.textContent = '';
        }

        startRmsButton.addEventListener('click', () => {
            resetRmsDemo(); // Ensure reset before new start
            precomputeRMSchedule();
            animateRms();
        });
        resetRmsButton.addEventListener('click', resetRmsDemo);

        // Initial draw for RMS canvas
        drawRmsSchedule(0);
    </script>
</body>
</html> 