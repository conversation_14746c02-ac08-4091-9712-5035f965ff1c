<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库范式交互动画教程</title>
    <style>
        /* 全局样式 */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8faff; /* 极浅的蓝色背景 */
            color: #334155; /* 深灰色文本 */
            line-height: 1.8;
            margin: 0;
            padding: 40px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            box-sizing: border-box;
        }

        h1 {
            color: #1e40af; /* 深蓝色标题 */
            text-align: center;
            margin-bottom: 50px;
            font-size: 2.8em;
            letter-spacing: 1px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.05);
        }

        .container {
            max-width: 1000px;
            width: 100%;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            gap: 40px;
        }

        /* 范式卡片 */
        .paradigm-card {
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            padding: 35px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden; /* 防止内容溢出，特别是动画时 */
        }

        .paradigm-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
        }

        .paradigm-card h2 {
            margin-top: 0;
            font-size: 2.2em;
            color: #1d4ed8; /* 蓝色副标题 */
            border-bottom: 2px solid #e0e7ff; /* 浅蓝色下划线 */
            padding-bottom: 15px;
            margin-bottom: 25px;
            text-align: center;
        }

        .explanation {
            background-color: #eef2ff; /* 浅蓝色背景 */
            border-left: 5px solid #4338ca; /* 紫蓝色左边框 */
            padding: 20px 25px;
            margin: 25px 0;
            border-radius: 8px;
            font-size: 1.1em;
            color: #374151;
            line-height: 1.7;
        }

        .explanation strong {
            color: #1c3a7a;
        }

        /* 演示区域 */
        .demo {
            margin-top: 30px;
            text-align: center;
        }

        .demo h3 {
            color: #059669; /* 绿色 */
            font-size: 1.6em;
            margin-bottom: 20px;
        }

        /* 表格样式 */
        table {
            width: 90%;
            border-collapse: collapse;
            margin: 20px auto;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            overflow: hidden; /* 保证圆角 */
        }

        th, td {
            border: 1px solid #d1d5db; /* 浅灰色边框 */
            padding: 15px 10px;
            text-align: center;
            font-size: 1em;
        }

        th {
            background-color: #e0e7ff; /* 浅蓝色表头 */
            font-weight: bold;
            color: #1e40af;
        }

        td {
            background-color: #ffffff;
            transition: background-color 0.3s ease;
        }

        .pk {
            background-color: #fffbeb; /* 浅黄色为主键 */
            font-weight: bold;
            color: #92400e;
        }

        .non-atomic {
            background-color: #fef2f2; /* 浅红色突出非原子属性 */
            color: #b91c1c;
            border: 2px dashed #fca5a5;
        }

        /* 动画容器 */
        .demo-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
            min-height: 350px; /* 确保动画区域有足够高度 */
            position: relative; /* 用于Canvas定位 */
        }

        .table-state {
            position: absolute;
            width: 100%;
            transition: opacity 0.6s ease, transform 0.6s ease;
            opacity: 1;
            transform: translateY(0);
            backface-visibility: hidden; /* 修复一些动画闪烁问题 */
        }

        .table-state.hidden {
            opacity: 0;
            transform: translateY(20px);
            pointer-events: none;
        }

        .table-state.active {
            position: relative; /* 激活时回到文档流 */
        }

        .after-state {
            display: none; /* 默认隐藏，JavaScript控制显示 */
            justify-content: center; /* 居中表格组 */
            flex-wrap: wrap; /* 允许表格换行 */
        }

        .table-group {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            align-items: flex-start;
        }

        .table-group table {
            margin: 0; /* 组内表格去除外边距 */
            width: auto; /* 允许表格根据内容调整宽度 */
            min-width: 300px; /* 最小宽度 */
        }

        caption {
            font-weight: bold;
            margin-bottom: 10px;
            color: #059669;
            font-size: 1.1em;
        }

        /* 按钮样式 */
        .btn-container {
            text-align: center;
            margin-top: 30px;
        }

        button {
            background-color: #2563eb; /* 蓝色按钮 */
            color: white;
            border: none;
            padding: 15px 35px;
            border-radius: 30px; /* 圆角按钮 */
            font-size: 1.1em;
            cursor: pointer;
            transition: background-color 0.3s ease, box-shadow 0.3s ease, transform 0.2s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            outline: none;
        }

        button:hover {
            background-color: #1d4ed8; /* 深蓝色 */
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .note {
            font-size: 0.95em;
            color: #6b7280;
            margin-top: 20px;
            line-height: 1.6;
        }

        .canvas-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none; /* 让鼠标事件穿透Canvas */
            z-index: 10; /* 确保在表格之上 */
        }
    </style>
</head>
<body>

    <h1>🚀 数据库范式奇妙之旅 💡</h1>

    <div class="container">
        <!-- 第一范式 (1NF) -->
        <div class="paradigm-card" id="nf1">
            <h2>第一范式 (1NF): 属性的原子性</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 想象一下，你的数据表格就像是乐高积木！第一范式就是要求每一块积木（单元格）都必须是【最小、不可再分】的单独一块。不能是“一堆积木”，也不能是“一半积木”。简单来说，每个单元格只能包含一个单一的值。
                <br><br>
                <strong>为什么重要？</strong> 如果单元格里有多个值，计算机就搞不清楚你在说什么了！比如，“产品”一列里写着“手机, 充电器”，当你想查找所有“手机”订单时，它可能就会漏掉这条记录，因为“充电器”也在这里面。这就像你给乐高机器人下指令，告诉它“拿起蓝色积木”，但你给它的是一堆混杂着红黄绿的蓝色积木，它就懵了！
            </p>
            <div class="demo">
                <div class="demo-area">
                    <canvas id="nf1Canvas" class="canvas-overlay"></canvas>

                    <div class="before-state table-state active" data-state="before">
                        <h3>优化前 (不满足1NF) - ❌ 糟糕的数据</h3>
                        <p class="note">看！"产品"列的单元格里，藏着多个产品，就像一个格子放了好几个东西，这可不符合原子性原则！</p>
                        <table>
                            <thead>
                                <tr><th>订单ID</th><th>客户名</th><th class="non-atomic">产品 (非原子)</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>101</td><td>张三</td><td>手机, 充电器</td></tr>
                                <tr><td>102</td><td>李四</td><td>电脑</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-state hidden" data-state="after">
                         <h3>优化后 (满足1NF) - ✅ 完美的数据</h3>
                        <p class="note">现在，每个单元格都只包含一个产品，数据清晰明了！查询起来也超级方便。乐高机器人也能精确地拿起它需要的积木了！</p>
                        <table>
                             <thead>
                                <tr><th>订单ID</th><th>客户名</th><th>产品</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>101</td><td>张三</td><td>手机</td></tr>
                                <tr><td>101</td><td>张三</td><td>充电器</td></tr>
                                <tr><td>102</td><td>李四</td><td>电脑</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf1">开始转换 (变成乐高积木!)</button>
                </div>
            </div>
        </div>
        
        <!-- 第二范式 (2NF) -->
        <div class="paradigm-card" id="nf2">
            <h2>第二范式 (2NF): 消除部分依赖</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 在满足1NF的基础上，非主键列必须完全依赖于整个主键，而不是主键的一部分。这通常针对联合主键的表。
                <br><br>
                <strong>为什么重要？</strong> 想象你有一个“课程注册”表，主键是（学生ID，课程ID）。如果“学生姓名”这个属性只和“学生ID”有关，和“课程ID”没关系，那么每次同一个学生注册新课程时，都要重复记录他的姓名。这就像一个图书馆，你借书时，每次都要把你的身高、体重、血型都重新登记一遍，是不是很麻烦？而且一旦你的名字写错了，要改就得改好几条记录，非常容易出错！2NF就是为了解决这个问题，让数据更整洁，更新更容易，减少冗余。
            </p>
            <div class="demo">
                <div class="demo-area">
                    <div class="before-state table-state active" data-state="before">
                        <h3>优化前 (不满足2NF) - ❌ 数据重复又难改</h3>
                        <p class="note">看！'学生姓名'只依赖于'学生ID'（主键的一部分），而和'课程ID'没关系，这就是“部分依赖”！</p>
                        <table>
                            <thead>
                                <tr><th class="pk">学生ID</th><th class="pk">课程ID</th><th>课程名称</th><th>分数</th><th class="non-atomic">学生姓名 (部分依赖)</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>1</td><td>C101</td><td>数学</td><td>90</td><td>张三</td></tr>
                                <tr><td>1</td><td>C102</td><td>物理</td><td>85</td><td>张三</td></tr>
                                <tr><td>2</td><td>C101</td><td>数学</td><td>92</td><td>李四</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-state hidden" data-state="after">
                        <h3>优化后 (满足2NF) - ✅ 数据分离，整洁高效！</h3>
                        <p class="note">我们把“学生姓名”独立成一张“学生表”。现在，学生信息只记录一次，完美消除了部分依赖！</p>
                        <div class="table-group">
                            <table>
                                <caption>成绩表</caption>
                                <thead>
                                    <tr><th class="pk">学生ID</th><th class="pk">课程ID</th><th>课程名称</th><th>分数</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>1</td><td>C101</td><td>数学</td><td>90</td></tr>
                                    <tr><td>1</td><td>C102</td><td>物理</td><td>85</td></tr>
                                    <tr><td>2</td><td>C101</td><td>数学</td><td>92</td></tr>
                                </tbody>
                            </table>
                            <table>
                                <caption>学生表</caption>
                                <thead>
                                    <tr><th class="pk">学生ID</th><th>学生姓名</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>1</td><td>张三</td></tr>
                                    <tr><td>2</td><td>李四</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf2">开始转换 (数据瘦身！)</button>
                </div>
            </div>
        </div>

        <!-- 第三范式 (3NF) -->
        <div class="paradigm-card" id="nf3">
            <h2>第三范式 (3NF): 消除传递依赖</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 在满足2NF的基础上，任何非主键列都不能依赖于其他非主键列。也就是说，不能存在 A -> B -> C 的关系 (A是主键, B,C是非主键)。
                <br><br>
                <strong>为什么重要？</strong> 想象一下你有一个“员工信息表”，主键是“员工ID”，其中有“部门名称”和“部门电话”。“部门电话”不是直接由“员工ID”决定的，而是由“部门名称”决定的，而“部门名称”又由“员工ID”决定。这就像一个电话号码本，你不仅记录了每个人的电话，还在每个人名下重复记录了他们部门的公共电话。如果部门电话换了，你得挨个修改每个员工的记录，这不仅麻烦，还容易改错！3NF就是为了解决这种“间接依赖”的问题，让数据更规范，修改更方便，减少数据冗余和更新异常。
            </p>
            <div class="demo">
                <div class="demo-area">
                    <div class="before-state table-state active" data-state="before">
                        <h3>优化前 (不满足3NF) - ❌ 间接依赖，多余又麻烦</h3>
                        <p class="note">看！'部门电话'依赖于'部门名称'，而'部门名称'依赖于'员工ID'。形成了“传递依赖”：员工ID -> 部门名称 -> 部门电话！</p>
                        <table>
                            <thead>
                                <tr><th class="pk">员工ID</th><th>员工姓名</th><th>部门名称</th><th class="non-atomic">部门电话 (传递依赖)</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>101</td><td>小明</td><td>销售部</td><td>12345678</td></tr>
                                <tr><td>102</td><td>小红</td><td>销售部</td><td>12345678</td></tr>
                                <tr><td>103</td><td>小刚</td><td>研发部</td><td>87654321</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-state hidden" data-state="after">
                        <h3>优化后 (满足3NF) - ✅ 独立清晰，一目了然！</h3>
                        <p class="note">我们把“部门信息”独立成一张“部门表”。现在，每个信息都有了直接的“负责人”，彻底消除了传递依赖！</p>
                        <div class="table-group">
                            <table>
                                <caption>员工表</caption>
                                <thead>
                                    <tr><th class="pk">员工ID</th><th>员工姓名</th><th>部门名称</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>101</td><td>小明</td><td>销售部</td></tr>
                                    <tr><td>102</td><td>小红</td><td>销售部</td></tr>
                                    <tr><td>103</td><td>小刚</td><td>研发部</td></tr>
                                </tbody>
                            </table>
                            <table>
                                <caption>部门表</caption>
                                <thead>
                                    <tr><th class="pk">部门名称</th><th>部门电话</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>销售部</td><td>12345678</td></tr>
                                    <tr><td>研发部</td><td>87654321</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf3">开始转换 (告别连环依赖！)</button>
                </div>
            </div>
        </div>

         <!-- 巴斯-科德范式 (BCNF) -->
        <div class="paradigm-card" id="bcnf">
            <h2>巴斯-科德范式 (BCNF)</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 在3NF基础上，要求每个决定因素（能决定其他属性的属性或属性组）都必须包含候选键。这是更严格的3NF。
                <br><br>
                <strong>为什么重要？</strong> 想象一个“课程安排”表，里面有“老师”、“学生”和“课程”。假设规定：一个老师只教一门课，但一门课可以由多个老师教。此时，(老师, 学生) 是主键，但“老师”这个非候选键却决定了“课程”（因为一个老师只教一门课）。这就像你的手机里，同一个联系人存了多遍，每遍都带着他教的课程信息。如果老师教的课变了，你得改好多条联系人记录，既麻烦又容易出错！BCNF就是为了解决这种“隐藏的依赖”问题，让数据更加纯粹，避免不必要的冗余和更新异常。
            </p>
             <div class="demo">
                 <div class="demo-area">
                    <div class="before-state table-state active" data-state="before">
                         <h3>优化前 (满足3NF但不满足BCNF) - ❌ 隐藏的依赖，数据很别扭</h3>
                         <p class="note">主键是 (老师, 学生)。但存在'老师 -> 课程'的依赖，而'老师'不是候选键，只是候选键的一部分，所以不满足BCNF。</p>
                        <table>
                            <thead>
                                <tr><th class="pk">老师</th><th class="pk">学生</th><th class="non-atomic">课程 (被非候选键决定)</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>王教授</td><td>学生A</td><td>物理</td></tr>
                                <tr><td>王教授</td><td>学生B</td><td>物理</td></tr>
                                <tr><td>李教授</td><td>学生C</td><td>化学</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-state hidden" data-state="after">
                        <h3>优化后 (满足BCNF) - ✅ 数据纯净，逻辑清晰！</h3>
                        <p class="note">分解成两个表，让每个决定因素都包含候选键，彻底消除了隐藏的依赖。</p>
                        <div class="table-group">
                             <table>
                                <caption>师生关系表</caption>
                                <thead>
                                    <tr><th class="pk">老师</th><th class="pk">学生</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>王教授</td><td>学生A</td></tr>
                                    <tr><td>王教授</td><td>学生B</td></tr>
                                    <tr><td>李教授</td><td>学生C</td></tr>
                                </tbody>
                            </table>
                             <table>
                                <caption>教师课程表</caption>
                                <thead>
                                    <tr><th class="pk">老师</th><th>课程</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>王教授</td><td>物理</td></tr>
                                    <tr><td>李教授</td><td>化学</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="bcnf">开始转换 (让数据回归本真！)</button>
                </div>
            </div>
        </div>

        <!-- 第四范式 (4NF) -->
        <div class="paradigm-card" id="nf4">
            <h2>第四范式 (4NF): 消除多值依赖</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 消除表中的多值依赖。简单说，如果一个表中的某一行，可以对应【多个独立的】属性集合，那就需要拆分。比如，一个老师可以教多种课程，同时可以负责多个项目，但"课程"和"项目"之间没有直接关系，它们都只和老师有关。
                <br><br>
                <strong>为什么重要？</strong> 想象你是一个“超级老师”，不仅教好几门课（比如语文、数学），还同时负责好几个社团（比如象棋社、篮球社）。如果你的“老师信息表”里，为了同时记录你的课程和社团，每次都把所有课程和所有社团组合起来写成多条记录（比如：王老师-语文-象棋社，王老师-语文-篮球社，王老师-数学-象棋社，王老师-数学-篮球社），你是不是觉得很头疼？课程和社团明明是两个独立的信息，却被“捆绑销售”，导致数据量爆炸式增长，而且任何一个信息变了，都要改一堆记录！4NF就是来解决这个问题的，它让相互独立的多值信息分开管理，让你的数据表变得简洁、高效，不再“多此一举”！
            </p>
            <div class="demo">
                <div class="demo-area">
                    <div class="before-state table-state active" data-state="before">
                        <h3>优化前 (不满足4NF) - ❌ 信息膨胀，冗余缠身</h3>
                        <p class="note">为了记录王教授教'物理'和'化学'，同时负责'项目A'和'项目B'，我们被迫创建了 2x2=4 条记录来表示所有组合，这导致了严重的数据冗余。</p>
                        <table>
                            <thead>
                                <tr><th class="pk">老师</th><th class="non-atomic">所教课程 (多值)</th><th class="non-atomic">负责项目 (多值)</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>王教授</td><td>物理</td><td>项目A</td></tr>
                                <tr><td>王教授</td><td>物理</td><td>项目B</td></tr>
                                <tr><td>王教授</td><td>化学</td><td>项目A</td></tr>
                                <tr><td>王教授</td><td>化学</td><td>项目B</td></tr>
                                <tr><td>李教授</td><td>数学</td><td>项目C</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-state hidden" data-state="after">
                        <h3>优化后 (满足4NF) - ✅ 信息解耦，清爽明了！</h3>
                        <p class="note">我们将"课程"和"项目"这两个相互独立的多值属性拆分成两个表，彻底消除了多值依赖和数据冗余。</p>
                        <div class="table-group">
                             <table>
                                <caption>教师课程表</caption>
                                <thead>
                                    <tr><th class="pk">老师</th><th>所教课程</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>王教授</td><td>物理</td></tr>
                                    <tr><td>王教授</td><td>化学</td></tr>
                                    <tr><td>李教授</td><td>数学</td></tr>
                                </tbody>
                            </table>
                             <table>
                                <caption>教师项目表</caption>
                                <thead>
                                    <tr><th class="pk">老师</th><th>负责项目</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>王教授</td><td>项目A</td></tr>
                                    <tr><td>王教授</td><td>项目B</td></tr>
                                    <tr><td>李教授</td><td>项目C</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf4">开始转换 (告别信息爆炸！)</button>
                </div>
            </div>
        </div>

        <!-- 第五范式 (5NF) -->
        <div class="paradigm-card" id="nf5">
            <h2>第五范式 (5NF): 消除连接依赖</h2>
            <p class="explanation">
                <strong>核心思想：</strong> 在满足4NF的基础上，消除表中的【连接依赖】。这意味着一个表不能被无损地分解成更多更小的表，然后通过连接这些小表来恢复原始数据。简单来说，如果一个表的【所有信息】无法在不产生冗余的情况下，只通过它的【键】来完全决定，那么就需要拆分。
                <br><br>
                <strong>为什么重要？</strong> 想象你经营一个小型咨询公司，有“咨询师”、“产品”和“客户”三方合作关系。可能存在这样的业务规则：如果某个咨询师销售某个产品给某个客户，那么这个客户就只能从这个咨询师那里购买这个产品。但是，这个信息在普通表格里很难直接表达。如果尝试将所有信息都放在一张大表里，就可能出现奇怪的冗余，或者无法精确表达所有业务关系。5NF就是处理这种复杂的多方关系，确保你的数据模型能够完美、无冗余地表达所有业务规则，避免因为数据无法准确还原而导致的“信息失真”问题！
            </p>
            <div class="demo">
                <div class="demo-area">
                    <div class="before-state table-state active" data-state="before">
                        <h3>优化前 (不满足5NF) - ❌ 复杂的连接，信息易失真</h3>
                        <p class="note">这张表记录了咨询师为客户销售产品的情况。假设：<br>1. 如果咨询师A能销售产品P，客户C能购买产品P，那么咨询师A就能为客户C销售产品P。<br>这种业务规则导致了潜在的连接依赖问题。</p>
                        <table>
                            <thead>
                                <tr><th class="pk">咨询师</th><th class="pk">产品</th><th class="pk">客户</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>张三</td><td>咨询服务</td><td>A客户</td></tr>
                                <tr><td>张三</td><td>咨询服务</td><td>B客户</td></tr>
                                <tr><td>李四</td><td>数据分析</td><td>B客户</td></tr>
                                <tr><td>李四</td><td>数据分析</td><td>C客户</td></tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="after-state table-state hidden" data-state="after">
                        <h3>优化后 (满足5NF) - ✅ 精确表达，数据完整无冗余！</h3>
                        <p class="note">我们将复杂的"咨询师-产品-客户"关系拆分成三个更小的表，每个表都只表示两个实体之间的关系。这样，既能精确表达业务规则，又能避免冗余。</p>
                        <div class="table-group">
                             <table>
                                <caption>咨询师-产品表</caption>
                                <thead>
                                    <tr><th class="pk">咨询师</th><th class="pk">产品</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>张三</td><td>咨询服务</td></tr>
                                    <tr><td>李四</td><td>数据分析</td></tr>
                                </tbody>
                            </table>
                             <table>
                                <caption>产品-客户表</caption>
                                <thead>
                                    <tr><th class="pk">产品</th><th class="pk">客户</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>咨询服务</td><td>A客户</td></tr>
                                    <tr><td>咨询服务</td><td>B客户</td></tr>
                                    <tr><td>数据分析</td><td>B客户</td></tr>
                                    <tr><td>数据分析</td><td>C客户</td></tr>
                                </tbody>
                            </table>
                             <table>
                                <caption>咨询师-客户表</caption>
                                <thead>
                                    <tr><th class="pk">咨询师</th><th class="pk">客户</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>张三</td><td>A客户</td></tr>
                                    <tr><td>张三</td><td>B客户</td></tr>
                                    <tr><td>李四</td><td>B客户</td></tr>
                                    <tr><td>李四</td><td>C客户</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf5">开始转换 (让关系更清晰！)</button>
                </div>
            </div>
        </div>

        <!-- 2NF vs 3NF 对比 -->
        <div class="paradigm-card" id="nf2-vs-nf3">
            <h2>2NF vs 3NF: 一步步看区别</h2>
            <p class="explanation">
                <strong>关键区别：</strong>2NF 解决的是【非主键】对【部分主键】的依赖（通常发生在联合主键的表中）。而 3NF 解决的是【非主键】对【另一个非主键】的依赖。
                <br>让我们通过一个例子，一步步将一个混乱的表规范到 3NF，来感受它们的区别。
            </p>
            <div class="demo">
                <div class="demo-area">
                    <!-- 步骤 0: 原始状态 (不满足2NF和3NF) -->
                    <div class="step-0 table-state active" data-step="0">
                        <h3>原始表 (不满足 2NF & 3NF)</h3>
                        <p class="note">主键是 (学生ID, 活动ID)。<br>
                        <span style="color:#c00;"><b>2NF问题:</b> '学生姓名' 和 '所在系' 只依赖于 '学生ID'，这是部分依赖。</span><br>
                        <span style="color:#e67e22;"><b>3NF问题:</b> '系主任' 依赖于 '所在系'，而'所在系'又依赖于'学生ID'，这是传递依赖。</span>
                        </p>
                        <table>
                            <thead>
                                <tr><th class="pk">学生ID</th><th class="pk">活动ID</th><th class="non-atomic">学生姓名</th><th class="non-atomic">所在系</th><th class="non-atomic">系主任</th><th>活动名称</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>1</td><td>A1</td><td>张三</td><td>计算机系</td><td>王教授</td><td>迎新晚会</td></tr>
                                <tr><td>1</td><td>A2</td><td>张三</td><td>计算机系</td><td>王教授</td><td>编程大赛</td></tr>
                                <tr><td>2</td><td>A1</td><td>李四</td><td>物理系</td><td>刘教授</td><td>迎新晚会</td></tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 步骤 1: 优化到 2NF -->
                    <div class="step-1 table-state hidden" data-step="1">
                        <h3>第一步: 优化到 2NF (已消除部分依赖)</h3>
                        <p class="note">我们将只依赖'学生ID'的列（学生姓名, 所在系, 系主任）和只依赖'活动ID'的列（活动名称）拆分出去，形成了三个表。现在每个表都不存在部分依赖了。<br>
                        <span style="color:#e67e22;"><b>新问题:</b> 看新的"学生信息表"，'系主任'依赖于'所在系'，传递依赖问题依然存在。</span>
                        </p>
                        <div class="table-group">
                            <table><caption>报名表</caption><thead><tr><th class="pk">学生ID</th><th class="pk">活动ID</th></tr></thead><tbody><tr><td>1</td><td>A1</td></tr><tr><td>1</td><td>A2</td></tr><tr><td>2</td><td>A1</td></tr></tbody></table>
                            <table><caption>活动表</caption><thead><tr><th class="pk">活动ID</th><th>活动名称</th></tr></thead><tbody><tr><td>A1</td><td>迎新晚会</td></tr><tr><td>A2</td><td>编程大赛</td></tr></tbody></table>
                            <table style="border: 2px solid #e67e22;"><caption>学生信息表 (不满足3NF)</caption><thead><tr><th class="pk">学生ID</th><th>学生姓名</th><th>所在系</th><th class="non-atomic">系主任</th></tr></thead><tbody><tr><td>1</td><td>张三</td><td>计算机系</td><td>王教授</td></tr><tr><td>2</td><td>李四</td><td>物理系</td><td>刘教授</td></tr></tbody></table>
                        </div>
                    </div>

                    <!-- 步骤 2: 优化到 3NF -->
                    <div class="step-2 table-state hidden" data-step="2">
                        <h3>第二步: 优化到 3NF (已消除传递依赖)</h3>
                        <p class="note">我们继续将"学生信息表"拆分，把传递依赖的'系主任'也独立出去。现在所有的表都满足3NF了！数据冗余更少，结构更清晰。</p>
                        <div class="table-group">
                             <table><caption>报名表</caption><thead><tr><th class="pk">学生ID</th><th class="pk">活动ID</th></tr></thead><tbody><tr><td>1</td><td>A1</td></tr><tr><td>1</td><td>A2</td></tr><tr><td>2</td><td>A1</td></tr></tbody></table>
                             <table><caption>活动表</caption><thead><tr><th class="pk">活动ID</th><th>活动名称</th></tr></thead><tbody><tr><td>A1</td><td>迎新晚会</td></tr><tr><td>A2</td><td>编程大赛</td></tr></tbody></table>
                             <table><caption>学生表</caption><thead><tr><th class="pk">学生ID</th><th>学生姓名</th><th>所在系</th></tr></thead><tbody><tr><td>1</td><td>张三</td><td>计算机系</td></tr><tr><td>2</td><td>李四</td><td>物理系</td></tr></tbody></table>
                             <table><caption>院系表</caption><thead><tr><th class="pk">所在系</th><th>系主任</th></tr></thead><tbody><tr><td>计算机系</td><td>王教授</td></tr><tr><td>物理系</td><td>刘教授</td></tr></tbody></table>
                        </div>
                    </div>
                </div>
                <div class="btn-container">
                    <button data-target="nf2-vs-nf3" data-step="0">第一步: 优化到 2NF</button>
                </div>
            </div>
        </div>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 通用函数：处理范式卡片内的动画切换
            const setupParadigmCard = (cardId, hasCanvas = false, canvasAnimationFunc = null) => {
                const card = document.getElementById(cardId);
                if (!card) return; // 如果卡片不存在，则退出

                const button = card.querySelector('button[data-target="' + cardId + '"]');
                
                // 特殊处理 2NF vs 3NF 的多步骤逻辑
                if (cardId === 'nf2-vs-nf3') {
                    let currentStep = parseInt(button.getAttribute('data-step'), 10);
                    const steps = [
                        card.querySelector('.step-0'),
                        card.querySelector('.step-1'),
                        card.querySelector('.step-2')
                    ];

                    button.addEventListener('click', () => {
                        const currentVisible = steps[currentStep];
                        let nextStepIndex;

                        if (currentStep === 0) nextStepIndex = 1;
                        else if (currentStep === 1) nextStepIndex = 2;
                        else nextStepIndex = 0; // 从最后一步回到第一步

                        const nextVisible = steps[nextStepIndex];

                        currentVisible.classList.add('hidden');
                        setTimeout(() => {
                            currentVisible.style.display = 'none';
                            nextVisible.style.display = 'flex'; // 使用flex或block，根据内容调整
                            
                            setTimeout(() => {
                                nextVisible.classList.remove('hidden');
                            }, 20); // 短暂延时确保 display 生效，触发transition

                            currentStep = nextStepIndex; // 更新当前步骤
                            button.setAttribute('data-step', currentStep);

                            // 更新按钮文本
                            if (currentStep === 0) button.textContent = '第一步: 优化到 2NF';
                            else if (currentStep === 1) button.textContent = '第二步: 优化到 3NF';
                            else button.textContent = '返回重置';

                        }, 600); // 等待淡出动画
                    });
                    return; // 结束此函数，不走通用逻辑
                }

                // 通用逻辑（适用于 1NF, 2NF, 3NF, BCNF, 4NF, 5NF）
                const beforeState = card.querySelector('.before-state');
                const afterState = card.querySelector('.after-state');

                // 如果有Canvas，设置Canvas尺寸并绑定resize事件
                if (hasCanvas) {
                    const canvas = card.querySelector('#' + cardId + 'Canvas');
                    const ctx = canvas.getContext('2d');
                    const demoArea = card.querySelector('.demo-area');

                    const resizeCanvas = () => {
                        canvas.width = demoArea.offsetWidth;
                        canvas.height = demoArea.offsetHeight;
                        if (canvasAnimationFunc) {
                            canvasAnimationFunc(ctx, beforeState, afterState, demoArea, false); // 每次resize都清空并重绘
                        }
                    };
                    window.addEventListener('resize', resizeCanvas);
                    resizeCanvas(); // 初始设置
                }

                button.addEventListener('click', () => {
                    const isBeforeState = beforeState.classList.contains('active');

                    if (isBeforeState) {
                        // 从Before切换到After
                        beforeState.classList.remove('active');
                        beforeState.classList.add('hidden');
                        setTimeout(() => {
                            beforeState.style.display = 'none';
                            afterState.style.display = 'flex'; // 使用flex以便表格居中
                            afterState.classList.remove('hidden');
                            afterState.classList.add('active');
                            button.textContent = '返回 (还原)';
                            if (hasCanvas && canvasAnimationFunc) {
                                const canvas = card.querySelector('#' + cardId + 'Canvas');
                                const ctx = canvas.getContext('2d');
                                const demoArea = card.querySelector('.demo-area');
                                canvasAnimationFunc(ctx, beforeState, afterState, demoArea, true); // 开始Canvas动画
                            }
                        }, 600); // 等待隐藏动画完成
                    } else {
                        // 从After切换回Before
                        afterState.classList.remove('active');
                        afterState.classList.add('hidden');
                        setTimeout(() => {
                            afterState.style.display = 'none';
                            beforeState.style.display = 'block'; // 使用block
                            beforeState.classList.remove('hidden');
                            beforeState.classList.add('active');
                            button.textContent = '开始转换';
                            if (hasCanvas && canvasAnimationFunc) {
                                const canvas = card.querySelector('#' + cardId + 'Canvas');
                                const ctx = canvas.getContext('2d');
                                const demoArea = card.querySelector('.demo-area');
                                canvasAnimationFunc(ctx, beforeState, afterState, demoArea, false); // 停止Canvas动画并清空
                            }
                        }, 600); // 等待隐藏动画完成
                    }
                });
            };

            // 1NF Canvas 动画逻辑
            const drawNF1Animation = (ctx, beforeState, afterState, demoArea, animate) => {
                ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height); // 清空画布

                if (!animate) return; // 如果不是动画模式，直接返回

                const firstRowNonAtomicCell = beforeState.querySelector('tbody tr:first-child td:nth-child(3)'); // 手机, 充电器
                const afterTable = afterState.querySelector('table');

                if (!firstRowNonAtomicCell || !afterTable) {
                    return; // 如果元素不存在，则不绘制
                }

                const demoAreaRect = demoArea.getBoundingClientRect();
                
                const getRelativePos = (element) => {
                    const rect = element.getBoundingClientRect();
                    return {
                        x: rect.left - demoAreaRect.left + rect.width / 2,
                        y: rect.top - demoAreaRect.top + rect.height / 2
                    };
                };

                const startPos = getRelativePos(firstRowNonAtomicCell);
                const endPos1 = getRelativePos(afterTable.querySelector('tbody tr:first-child td:nth-child(3)')); // 手机
                const endPos2 = getRelativePos(afterTable.querySelector('tbody tr:nth-child(2) td:nth-child(3)')); // 充电器

                ctx.strokeStyle = '#f59e0b'; // 橙色
                ctx.lineWidth = 4;
                ctx.setLineDash([10, 5]); // 虚线

                let progress = 0;
                const duration = 800; // 动画时长

                let startTime = null;
                const animateArrow = (timestamp) => {
                    if (!startTime) startTime = timestamp;
                    progress = (timestamp - startTime) / duration;

                    if (progress < 1) {
                        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

                        // 绘制第一条线
                        ctx.beginPath();
                        ctx.moveTo(startPos.x, startPos.y);
                        const currentX1 = startPos.x + (endPos1.x - startPos.x) * progress;
                        const currentY1 = startPos.y + (endPos1.y - startPos.y) * progress;
                        ctx.lineTo(currentX1, currentY1);
                        ctx.stroke();

                        // 绘制第二条线
                        ctx.beginPath();
                        ctx.moveTo(startPos.x, startPos.y);
                        const currentX2 = startPos.x + (endPos2.x - startPos.x) * progress;
                        const currentY2 = startPos.y + (endPos2.y - startPos.y) * progress;
                        ctx.lineTo(currentX2, currentY2);
                        ctx.stroke();
                        
                        requestAnimationFrame(animateArrow);
                    } else {
                        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height); // 动画结束后清空
                        startTime = null; // 重置
                    }
                };
                requestAnimationFrame(animateArrow);
            };

            // 初始化范式卡片
            setupParadigmCard('nf1', true, drawNF1Animation);
            setupParadigmCard('nf2'); // 2NF 无Canvas动画
            setupParadigmCard('nf3'); // 3NF 无Canvas动画
            setupParadigmCard('bcnf'); // BCNF 无Canvas动画
            setupParadigmCard('nf4'); // 4NF 无Canvas动画
            setupParadigmCard('nf5'); // 5NF 无Canvas动画
            setupParadigmCard('nf2-vs-nf3'); // 2NF vs 3NF 多步骤动画
        });
    </script>

</body>
</html> 