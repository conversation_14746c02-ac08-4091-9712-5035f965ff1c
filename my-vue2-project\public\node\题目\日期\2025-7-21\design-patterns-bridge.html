<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式学习 - Bridge桥接模式</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .nav-menu {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: slideInUp 1s ease-out 0.3s both;
        }

        .nav-menu h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.8rem;
            text-align: center;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .menu-item {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            color: white;
            font-size: 1rem;
            font-weight: 500;
            text-align: left;
            position: relative;
            overflow: hidden;
        }

        .menu-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .menu-item:hover::before {
            left: 100%;
        }

        .content-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            color: #333;
            font-size: 2rem;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            text-align: center;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            max-width: 100%;
            height: auto;
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.8;
            color: #333;
        }

        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            position: relative;
        }

        .code-example::before {
            content: 'JavaScript';
            position: absolute;
            top: 10px;
            right: 15px;
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
        }

        .interactive-demo {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .demo-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 15px 30px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .demo-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
            color: #2d3436;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌉 设计模式学习中心</h1>
            <p>深入理解Bridge桥接模式 - 将抽象与实现分离，让它们可以独立变化</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 原题展示 -->
        <div class="nav-menu" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: white;">
            <h2 style="color: white; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">📝 原题回顾</h2>
            <div style="background: rgba(255,255,255,0.95); color: #333; padding: 25px; border-radius: 15px; margin: 20px 0;">
                <h3 style="color: #d32f2f; margin-bottom: 15px;">🎯 设计模式选择题</h3>
                <p style="line-height: 1.8; font-size: 1.1rem; margin-bottom: 20px;">
                    某广告公司的宣传产品有<span class="highlight">宣传册、文章、传单</span>等多种形式，宣传产品的出版方式包括<span class="highlight">纸质方式、CD、DVD、在线发布</span>等。现要求为该广告公司设计一个管理这些宣传产品的应用，采用（<strong style="color: #d32f2f;">作答此空</strong>）设计模式较为合适，该模式（ ）。
                </p>

                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin: 20px 0;">
                    <div style="background: #ffebee; padding: 15px; border-radius: 10px; border: 2px solid #f44336;">
                        <strong style="color: #d32f2f;">A. Decorator</strong><br>
                        <small style="color: #666;">装饰器模式</small>
                    </div>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 10px; border: 2px solid #ff9800;">
                        <strong style="color: #f57c00;">B. Adapter</strong><br>
                        <small style="color: #666;">适配器模式</small>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; border: 3px solid #4caf50; box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);">
                        <strong style="color: #2e7d32;">C. Bridge ✓</strong><br>
                        <small style="color: #2e7d32; font-weight: bold;">桥接模式 - 正确答案</small>
                    </div>
                    <div style="background: #f3e5f5; padding: 15px; border-radius: 10px; border: 2px solid #9c27b0;">
                        <strong style="color: #7b1fa2;">D. Facade</strong><br>
                        <small style="color: #666;">外观模式</small>
                    </div>
                </div>

                <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; border-left: 5px solid #4caf50; margin-top: 20px;">
                    <h4 style="color: #2e7d32; margin-bottom: 10px;">✅ 正确答案：C. Bridge（桥接模式）</h4>
                    <p style="margin: 0; line-height: 1.6;">
                        <strong>解析：</strong>题目中不希望在不同的宣传产品与具体出版方式之间建立固定的绑定关系，以避免紧耦合。Bridge模式将抽象部分与实现部分分离，使它们都可以独立地变化，完美解决了这个问题。
                    </p>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button class="demo-button" onclick="showSection('overview')" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); font-size: 1.1rem; padding: 15px 30px;">
                        🚀 开始深入学习Bridge模式
                    </button>
                </div>
            </div>
        </div>

        <div class="nav-menu">
            <h2>📚 学习目录</h2>
            <div class="menu-grid">
                <button class="menu-item" onclick="showSection('overview')">
                    <strong>1. 概述与问题</strong><br>
                    了解广告公司案例和设计模式选择
                </button>
                <button class="menu-item" onclick="showSection('bridge-intro')">
                    <strong>2. Bridge模式介绍</strong><br>
                    桥接模式的核心概念和结构
                </button>
                <button class="menu-item" onclick="showSection('structure')">
                    <strong>3. 模式结构</strong><br>
                    抽象、实现、具体类的关系
                </button>
                <button class="menu-item" onclick="showSection('implementation')">
                    <strong>4. 代码实现</strong><br>
                    JavaScript实现广告公司案例
                </button>
                <button class="menu-item" onclick="showSection('animation')">
                    <strong>5. 动画演示</strong><br>
                    可视化桥接模式工作原理
                </button>
                <button class="menu-item" onclick="showSection('comparison')">
                    <strong>6. 模式对比</strong><br>
                    与其他模式的区别和优势
                </button>
                <button class="menu-item" onclick="showSection('practice')">
                    <strong>7. 实践练习</strong><br>
                    交互式练习和测试
                </button>
                <button class="menu-item" onclick="showSection('summary')">
                    <strong>8. 总结回顾</strong><br>
                    知识点总结和应用场景
                </button>
            </div>
        </div>

        <!-- 1. 概述与问题 -->
        <div id="overview" class="content-section active">
            <h2 class="section-title">📋 概述与问题分析</h2>

            <div class="explanation">
                <h3>🏢 广告公司案例背景</h3>
                <p>某广告公司需要管理多种宣传产品：</p>
                <ul style="margin: 15px 0; padding-left: 30px;">
                    <li><strong>宣传产品类型：</strong>宣传册、文章、传单</li>
                    <li><strong>出版方式：</strong>纸质方式、CD、DVD、在线发布</li>
                </ul>
                <p><span class="highlight">核心问题：</span>如何避免宣传产品与出版方式之间的紧耦合关系？</p>
            </div>

            <div class="canvas-container">
                <canvas id="problemCanvas" width="800" height="400"></canvas>
                <p style="margin-top: 15px; color: #666;">点击下方按钮查看问题演示</p>
                <button class="demo-button" onclick="animateProblem()">🔍 演示耦合问题</button>
                <button class="demo-button" onclick="animateSolution()">✨ 展示解决方案</button>
            </div>

            <div class="explanation">
                <h3>🤔 为什么选择Bridge模式？</h3>
                <p>让我们分析各个选项：</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                    <div style="background: #ffebee; padding: 15px; border-radius: 10px; border-left: 4px solid #f44336;">
                        <strong>A. Decorator</strong><br>
                        <small>用于动态添加功能，不适合此场景</small>
                    </div>
                    <div style="background: #fff3e0; padding: 15px; border-radius: 10px; border-left: 4px solid #ff9800;">
                        <strong>B. Adapter</strong><br>
                        <small>用于接口转换，不是主要需求</small>
                    </div>
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #4caf50;">
                        <strong>C. Bridge ✓</strong><br>
                        <small>分离抽象与实现，完美匹配</small>
                    </div>
                    <div style="background: #f3e5f5; padding: 15px; border-radius: 10px; border-left: 4px solid #9c27b0;">
                        <strong>D. Facade</strong><br>
                        <small>简化接口，不解决耦合问题</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. Bridge模式介绍 -->
        <div id="bridge-intro" class="content-section">
            <h2 class="section-title">🌉 Bridge桥接模式介绍</h2>

            <div class="explanation">
                <h3>📖 定义</h3>
                <p><strong>Bridge模式</strong>将抽象部分与它的实现部分分离，使它们都可以独立地变化。</p>

                <h3>🎯 核心思想</h3>
                <p>通过组合而非继承来连接抽象和实现，就像一座桥梁连接两岸。</p>
            </div>

            <div class="canvas-container">
                <canvas id="bridgeIntroCanvas" width="800" height="500"></canvas>
                <p style="margin-top: 15px; color: #666;">桥接模式概念图</p>
                <button class="demo-button" onclick="animateBridgeConcept()">🎬 播放概念动画</button>
            </div>

            <div class="explanation">
                <h3>✅ 适用场景</h3>
                <ul style="margin: 15px 0; padding-left: 30px; line-height: 2;">
                    <li>不希望在抽象和实现之间有固定的绑定关系</li>
                    <li>抽象和实现都应该可以通过子类扩充</li>
                    <li>对实现的修改不应影响客户端代码</li>
                    <li>需要在多个对象间共享实现</li>
                </ul>
            </div>
        </div>

        <!-- 3. 模式结构 -->
        <div id="structure" class="content-section">
            <h2 class="section-title">🏗️ Bridge模式结构</h2>

            <div class="explanation">
                <h3>🧩 核心组件</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: #e3f2fd; padding: 20px; border-radius: 10px;">
                        <h4 style="color: #1976d2;">🎭 Abstraction</h4>
                        <p>定义抽象类的接口，维护一个指向Implementor的引用</p>
                    </div>
                    <div style="background: #f3e5f5; padding: 20px; border-radius: 10px;">
                        <h4 style="color: #7b1fa2;">🔧 Implementor</h4>
                        <p>定义实现类的接口，不必与Abstraction接口完全一致</p>
                    </div>
                    <div style="background: #e8f5e8; padding: 20px; border-radius: 10px;">
                        <h4 style="color: #388e3c;">📝 RefinedAbstraction</h4>
                        <p>扩充由Abstraction定义的接口</p>
                    </div>
                    <div style="background: #fff3e0; padding: 20px; border-radius: 10px;">
                        <h4 style="color: #f57c00;">⚙️ ConcreteImplementor</h4>
                        <p>实现Implementor接口并定义具体实现</p>
                    </div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="structureCanvas" width="800" height="600"></canvas>
                <p style="margin-top: 15px; color: #666;">Bridge模式UML结构图</p>
                <button class="demo-button" onclick="animateStructure()">📊 显示结构关系</button>
                <button class="demo-button" onclick="highlightComponents()">🔍 高亮组件</button>
            </div>
        </div>

        <!-- 4. 代码实现 -->
        <div id="implementation" class="content-section">
            <h2 class="section-title">💻 代码实现</h2>

            <div class="explanation">
                <h3>🏢 广告公司案例实现</h3>
                <p>让我们用JavaScript实现广告公司的宣传产品管理系统：</p>
            </div>

            <div class="code-example">
// 实现接口 - 出版方式
class PublishingMethod {
    publish(content) {
        throw new Error("必须实现publish方法");
    }
}

// 具体实现 - 各种出版方式
class PaperPublishing extends PublishingMethod {
    publish(content) {
        return `📄 纸质出版: ${content}`;
    }
}

class CDPublishing extends PublishingMethod {
    publish(content) {
        return `💿 CD出版: ${content}`;
    }
}

class DVDPublishing extends PublishingMethod {
    publish(content) {
        return `📀 DVD出版: ${content}`;
    }
}

class OnlinePublishing extends PublishingMethod {
    publish(content) {
        return `🌐 在线发布: ${content}`;
    }
}
            </div>

            <div class="code-example">
// 抽象类 - 宣传产品
class PromotionalProduct {
    constructor(publishingMethod) {
        this.publishingMethod = publishingMethod;
    }

    produce() {
        throw new Error("必须实现produce方法");
    }

    setPublishingMethod(method) {
        this.publishingMethod = method;
    }
}

// 具体抽象 - 各种宣传产品
class Brochure extends PromotionalProduct {
    constructor(publishingMethod, title) {
        super(publishingMethod);
        this.title = title;
    }

    produce() {
        const content = `宣传册《${this.title}》`;
        return this.publishingMethod.publish(content);
    }
}

class Article extends PromotionalProduct {
    constructor(publishingMethod, title) {
        super(publishingMethod);
        this.title = title;
    }

    produce() {
        const content = `文章《${this.title}》`;
        return this.publishingMethod.publish(content);
    }
}

class Flyer extends PromotionalProduct {
    constructor(publishingMethod, title) {
        super(publishingMethod);
        this.title = title;
    }

    produce() {
        const content = `传单《${this.title}》`;
        return this.publishingMethod.publish(content);
    }
}
            </div>

            <div class="interactive-demo">
                <h3>🎮 交互式代码演示</h3>
                <p>选择宣传产品和出版方式，看看Bridge模式如何工作：</p>
                <div style="margin: 20px 0;">
                    <select id="productType" style="padding: 10px; margin: 5px; border-radius: 5px; border: 1px solid #ddd;">
                        <option value="brochure">宣传册</option>
                        <option value="article">文章</option>
                        <option value="flyer">传单</option>
                    </select>
                    <select id="publishMethod" style="padding: 10px; margin: 5px; border-radius: 5px; border: 1px solid #ddd;">
                        <option value="paper">纸质出版</option>
                        <option value="cd">CD出版</option>
                        <option value="dvd">DVD出版</option>
                        <option value="online">在线发布</option>
                    </select>
                    <input type="text" id="productTitle" placeholder="输入产品标题" style="padding: 10px; margin: 5px; border-radius: 5px; border: 1px solid #ddd;">
                </div>
                <button class="demo-button" onclick="demonstrateCode()">🚀 运行代码</button>
                <div id="codeResult" style="margin-top: 20px; padding: 15px; background: white; border-radius: 10px; min-height: 50px; border: 2px dashed #ddd;"></div>
            </div>
        </div>

        <!-- 5. 动画演示 -->
        <div id="animation" class="content-section">
            <h2 class="section-title">🎬 动画演示</h2>

            <div class="explanation">
                <h3>🎭 可视化Bridge模式工作原理</h3>
                <p>通过动画了解抽象与实现如何通过桥接分离：</p>
            </div>

            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="600"></canvas>
                <p style="margin-top: 15px; color: #666;">Bridge模式动画演示</p>
                <div style="margin: 20px 0;">
                    <button class="demo-button" onclick="startBridgeAnimation()">▶️ 开始动画</button>
                    <button class="demo-button" onclick="pauseAnimation()">⏸️ 暂停</button>
                    <button class="demo-button" onclick="resetAnimation()">🔄 重置</button>
                    <button class="demo-button" onclick="stepByStep()">👣 逐步演示</button>
                </div>
            </div>

            <div class="explanation">
                <h3>🔍 动画说明</h3>
                <ol style="margin: 15px 0; padding-left: 30px; line-height: 2;">
                    <li><strong>抽象层：</strong>宣传产品类定义统一接口</li>
                    <li><strong>桥接：</strong>通过组合连接抽象和实现</li>
                    <li><strong>实现层：</strong>出版方式类提供具体实现</li>
                    <li><strong>独立变化：</strong>两边可以独立扩展和修改</li>
                </ol>
            </div>
        </div>

        <!-- 6. 模式对比 -->
        <div id="comparison" class="content-section">
            <h2 class="section-title">⚖️ 模式对比</h2>

            <div class="explanation">
                <h3>🔍 Bridge vs 其他模式</h3>
                <p>了解Bridge模式与其他结构型模式的区别：</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                <div style="background: #e8f5e8; padding: 20px; border-radius: 15px; border: 3px solid #4caf50;">
                    <h4 style="color: #2e7d32;">🌉 Bridge模式</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>分离抽象与实现</li>
                        <li>运行时切换实现</li>
                        <li>避免类爆炸</li>
                        <li>组合优于继承</li>
                    </ul>
                </div>
                <div style="background: #fff3e0; padding: 20px; border-radius: 15px; border: 3px solid #ff9800;">
                    <h4 style="color: #f57c00;">🔌 Adapter模式</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>接口转换</li>
                        <li>兼容不同接口</li>
                        <li>事后补救</li>
                        <li>包装现有类</li>
                    </ul>
                </div>
                <div style="background: #f3e5f5; padding: 20px; border-radius: 15px; border: 3px solid #9c27b0;">
                    <h4 style="color: #7b1fa2;">🎨 Decorator模式</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>动态添加功能</li>
                        <li>保持接口一致</li>
                        <li>层层包装</li>
                        <li>功能增强</li>
                    </ul>
                </div>
                <div style="background: #e3f2fd; padding: 20px; border-radius: 15px; border: 3px solid #2196f3;">
                    <h4 style="color: #1976d2;">🏢 Facade模式</h4>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>简化复杂接口</li>
                        <li>统一访问入口</li>
                        <li>隐藏子系统</li>
                        <li>降低耦合</li>
                    </ul>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="comparisonCanvas" width="800" height="500"></canvas>
                <p style="margin-top: 15px; color: #666;">模式对比可视化</p>
                <button class="demo-button" onclick="showPatternComparison()">📊 显示对比</button>
            </div>
        </div>

        <!-- 7. 实践练习 -->
        <div id="practice" class="content-section">
            <h2 class="section-title">🎯 实践练习</h2>

            <div class="explanation">
                <h3>🧠 知识测试</h3>
                <p>通过练习巩固对Bridge模式的理解：</p>
            </div>

            <div class="interactive-demo">
                <h3>📝 练习1：识别Bridge模式应用场景</h3>
                <p>以下哪个场景最适合使用Bridge模式？</p>
                <div id="quiz1" style="margin: 20px 0;">
                    <div style="margin: 10px 0;">
                        <input type="radio" name="q1" value="a" id="q1a">
                        <label for="q1a">为现有类添加新功能</label>
                    </div>
                    <div style="margin: 10px 0;">
                        <input type="radio" name="q1" value="b" id="q1b">
                        <label for="q1b">图形界面在不同操作系统上的实现</label>
                    </div>
                    <div style="margin: 10px 0;">
                        <input type="radio" name="q1" value="c" id="q1c">
                        <label for="q1c">简化复杂系统的接口</label>
                    </div>
                    <div style="margin: 10px 0;">
                        <input type="radio" name="q1" value="d" id="q1d">
                        <label for="q1d">转换不兼容的接口</label>
                    </div>
                </div>
                <button class="demo-button" onclick="checkQuiz1()">✅ 检查答案</button>
                <div id="quiz1Result" style="margin-top: 15px;"></div>
            </div>

            <div class="interactive-demo">
                <h3>🛠️ 练习2：设计Bridge模式</h3>
                <p>为音乐播放器设计Bridge模式，支持不同音频格式和播放设备：</p>
                <div style="margin: 20px 0;">
                    <textarea id="userCode" placeholder="在这里编写你的Bridge模式实现..."
                        style="width: 100%; height: 200px; padding: 15px; border-radius: 10px; border: 1px solid #ddd; font-family: monospace;"></textarea>
                </div>
                <button class="demo-button" onclick="validateUserCode()">🔍 验证代码</button>
                <div id="codeValidation" style="margin-top: 15px;"></div>
            </div>
        </div>

        <!-- 8. 总结回顾 -->
        <div id="summary" class="content-section">
            <h2 class="section-title">📚 总结回顾</h2>

            <div class="explanation">
                <h3>🎯 核心要点</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px;">
                        <h4>🎭 分离关注点</h4>
                        <p>将抽象与实现分离，各自独立变化</p>
                    </div>
                    <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 25px; border-radius: 15px;">
                        <h4>🔗 组合优于继承</h4>
                        <p>通过组合建立连接，避免类层次爆炸</p>
                    </div>
                    <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 25px; border-radius: 15px;">
                        <h4>🔄 运行时切换</h4>
                        <p>可以在运行时动态切换实现方式</p>
                    </div>
                    <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 25px; border-radius: 15px;">
                        <h4>📈 易于扩展</h4>
                        <p>新增抽象或实现都不影响现有代码</p>
                    </div>
                </div>
            </div>

            <div class="explanation">
                <h3>💡 应用场景总结</h3>
                <ul style="margin: 15px 0; padding-left: 30px; line-height: 2;">
                    <li><strong>跨平台应用：</strong>GUI在不同操作系统上的实现</li>
                    <li><strong>数据库驱动：</strong>统一接口支持多种数据库</li>
                    <li><strong>图形渲染：</strong>不同渲染引擎的抽象</li>
                    <li><strong>消息传递：</strong>多种通信协议的统一接口</li>
                    <li><strong>设备驱动：</strong>硬件抽象层的实现</li>
                </ul>
            </div>

            <div class="canvas-container">
                <canvas id="summaryCanvas" width="800" height="400"></canvas>
                <p style="margin-top: 15px; color: #666;">Bridge模式知识图谱</p>
                <button class="demo-button" onclick="showKnowledgeMap()">🗺️ 显示知识图谱</button>
            </div>

            <div class="explanation" style="text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px; padding: 30px;">
                <h3>🎉 恭喜完成学习！</h3>
                <p style="font-size: 1.2rem; margin: 20px 0;">你已经掌握了Bridge桥接模式的核心概念和应用方法</p>
                <button class="demo-button" onclick="restartLearning()" style="background: white; color: #667eea;">🔄 重新学习</button>
                <button class="demo-button" onclick="downloadNotes()" style="background: rgba(255,255,255,0.2); color: white;">📝 下载笔记</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = 'overview';
        let animationId = null;
        let isAnimating = false;
        let animationStep = 0;

        // 进度跟踪
        const sections = ['overview', 'bridge-intro', 'structure', 'implementation', 'animation', 'comparison', 'practice', 'summary'];

        // 显示指定章节
        function showSection(sectionId) {
            // 隐藏所有章节
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // 显示指定章节
            document.getElementById(sectionId).classList.add('active');
            currentSection = sectionId;

            // 更新进度条
            updateProgress();

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 更新进度条
        function updateProgress() {
            const currentIndex = sections.indexOf(currentSection);
            const progress = ((currentIndex + 1) / sections.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // Bridge模式实现类
        class PublishingMethod {
            publish(content) {
                throw new Error("必须实现publish方法");
            }
        }

        class PaperPublishing extends PublishingMethod {
            publish(content) {
                return `📄 纸质出版: ${content}`;
            }
        }

        class CDPublishing extends PublishingMethod {
            publish(content) {
                return `💿 CD出版: ${content}`;
            }
        }

        class DVDPublishing extends PublishingMethod {
            publish(content) {
                return `📀 DVD出版: ${content}`;
            }
        }

        class OnlinePublishing extends PublishingMethod {
            publish(content) {
                return `🌐 在线发布: ${content}`;
            }
        }

        class PromotionalProduct {
            constructor(publishingMethod) {
                this.publishingMethod = publishingMethod;
            }

            produce() {
                throw new Error("必须实现produce方法");
            }

            setPublishingMethod(method) {
                this.publishingMethod = method;
            }
        }

        class Brochure extends PromotionalProduct {
            constructor(publishingMethod, title) {
                super(publishingMethod);
                this.title = title;
            }

            produce() {
                const content = `宣传册《${this.title}》`;
                return this.publishingMethod.publish(content);
            }
        }

        class Article extends PromotionalProduct {
            constructor(publishingMethod, title) {
                super(publishingMethod);
                this.title = title;
            }

            produce() {
                const content = `文章《${this.title}》`;
                return this.publishingMethod.publish(content);
            }
        }

        class Flyer extends PromotionalProduct {
            constructor(publishingMethod, title) {
                super(publishingMethod);
                this.title = title;
            }

            produce() {
                const content = `传单《${this.title}》`;
                return this.publishingMethod.publish(content);
            }
        }

        // 演示代码功能
        function demonstrateCode() {
            const productType = document.getElementById('productType').value;
            const publishMethod = document.getElementById('publishMethod').value;
            const title = document.getElementById('productTitle').value || '示例产品';

            // 创建出版方式实例
            let publishing;
            switch(publishMethod) {
                case 'paper': publishing = new PaperPublishing(); break;
                case 'cd': publishing = new CDPublishing(); break;
                case 'dvd': publishing = new DVDPublishing(); break;
                case 'online': publishing = new OnlinePublishing(); break;
            }

            // 创建产品实例
            let product;
            switch(productType) {
                case 'brochure': product = new Brochure(publishing, title); break;
                case 'article': product = new Article(publishing, title); break;
                case 'flyer': product = new Flyer(publishing, title); break;
            }

            // 执行并显示结果
            const result = product.produce();
            const resultDiv = document.getElementById('codeResult');
            resultDiv.innerHTML = `
                <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #4caf50;">
                    <h4 style="color: #2e7d32; margin-bottom: 10px;">✅ 执行结果：</h4>
                    <p style="font-size: 1.1rem; margin: 0;">${result}</p>
                </div>
                <div style="background: #f3e5f5; padding: 15px; border-radius: 10px; margin-top: 10px; border-left: 4px solid #9c27b0;">
                    <h4 style="color: #7b1fa2; margin-bottom: 10px;">🔍 Bridge模式体现：</h4>
                    <p style="margin: 0;">产品类型(${productType})和出版方式(${publishMethod})通过桥接模式实现了分离，可以独立变化和组合。</p>
                </div>
            `;

            // 添加动画效果
            resultDiv.style.animation = 'fadeIn 0.5s ease-out';
        }

        // Canvas动画函数
        function animateProblem() {
            const canvas = document.getElementById('problemCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制耦合问题
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('❌ 紧耦合问题演示', 20, 30);

            // 绘制产品类
            const products = ['宣传册', '文章', '传单'];
            const methods = ['纸质', 'CD', 'DVD', '在线'];

            let y = 80;
            products.forEach((product, i) => {
                methods.forEach((method, j) => {
                    const x = 50 + j * 180;
                    const currentY = y + i * 100;

                    // 绘制耦合的类
                    ctx.fillStyle = '#ffcdd2';
                    ctx.fillRect(x, currentY, 150, 60);
                    ctx.strokeStyle = '#f44336';
                    ctx.strokeRect(x, currentY, 150, 60);

                    ctx.fillStyle = '#333';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText(`${product}${method}`, x + 10, currentY + 25);
                    ctx.fillText('出版类', x + 10, currentY + 45);
                });
            });

            // 添加说明
            ctx.fillStyle = '#f44336';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('每种组合都需要一个单独的类，导致类爆炸！', 50, 380);
        }

        function animateSolution() {
            const canvas = document.getElementById('problemCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制Bridge解决方案
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('✅ Bridge模式解决方案', 20, 30);

            // 绘制抽象层
            ctx.fillStyle = '#e3f2fd';
            ctx.fillRect(50, 80, 200, 120);
            ctx.strokeStyle = '#2196f3';
            ctx.strokeRect(50, 80, 200, 120);
            ctx.fillStyle = '#1976d2';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('抽象层', 130, 100);
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('宣传册', 70, 130);
            ctx.fillText('文章', 70, 150);
            ctx.fillText('传单', 70, 170);

            // 绘制桥接
            ctx.strokeStyle = '#ff9800';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(250, 140);
            ctx.lineTo(350, 140);
            ctx.stroke();
            ctx.fillStyle = '#ff9800';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('桥接', 280, 130);

            // 绘制实现层
            ctx.fillStyle = '#f3e5f5';
            ctx.fillRect(350, 80, 200, 120);
            ctx.strokeStyle = '#9c27b0';
            ctx.strokeRect(350, 80, 200, 120);
            ctx.fillStyle = '#7b1fa2';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('实现层', 430, 100);
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('纸质出版', 370, 130);
            ctx.fillText('CD出版', 370, 150);
            ctx.fillText('DVD出版', 370, 170);
            ctx.fillText('在线发布', 370, 190);

            // 添加说明
            ctx.fillStyle = '#4caf50';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('只需要 3 + 4 = 7 个类，而不是 3 × 4 = 12 个类！', 50, 250);
        }

        function animateBridgeConcept() {
            const canvas = document.getElementById('bridgeIntroCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let frame = 0;
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = '20px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.fillText('🌉 Bridge桥接模式概念', 250, 40);

                // 绘制左岸（抽象）
                const leftX = 100;
                const leftY = 150;
                ctx.fillStyle = '#e3f2fd';
                ctx.fillRect(leftX, leftY, 150, 200);
                ctx.strokeStyle = '#2196f3';
                ctx.strokeRect(leftX, leftY, 150, 200);

                ctx.fillStyle = '#1976d2';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('抽象世界', leftX + 40, leftY + 30);
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText('• 宣传产品', leftX + 20, leftY + 60);
                ctx.fillText('• 统一接口', leftX + 20, leftY + 80);
                ctx.fillText('• 业务逻辑', leftX + 20, leftY + 100);

                // 绘制右岸（实现）
                const rightX = 550;
                const rightY = 150;
                ctx.fillStyle = '#f3e5f5';
                ctx.fillRect(rightX, rightY, 150, 200);
                ctx.strokeStyle = '#9c27b0';
                ctx.strokeRect(rightX, rightY, 150, 200);

                ctx.fillStyle = '#7b1fa2';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('实现世界', rightX + 40, rightY + 30);
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText('• 出版方式', rightX + 20, rightY + 60);
                ctx.fillText('• 具体实现', rightX + 20, rightY + 80);
                ctx.fillText('• 技术细节', rightX + 20, rightY + 100);

                // 绘制桥梁动画
                const bridgeY = 250;
                const bridgeProgress = Math.min(frame / 60, 1);
                const bridgeWidth = 200 * bridgeProgress;

                ctx.fillStyle = '#ff9800';
                ctx.fillRect(250, bridgeY, bridgeWidth, 20);

                if (bridgeProgress >= 1) {
                    ctx.fillStyle = '#ff9800';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('🌉 Bridge桥接', 320, bridgeY + 35);
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('组合连接，独立变化', 300, bridgeY + 55);
                }

                frame++;
                if (frame <= 120) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        function animateStructure() {
            const canvas = document.getElementById('structureCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制UML结构图
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('Bridge模式UML结构图', 300, 30);

            // Abstraction
            ctx.fillStyle = '#e3f2fd';
            ctx.fillRect(50, 80, 180, 100);
            ctx.strokeStyle = '#2196f3';
            ctx.strokeRect(50, 80, 180, 100);
            ctx.fillStyle = '#1976d2';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('Abstraction', 100, 105);
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('+ implementor', 60, 125);
            ctx.fillText('+ operation()', 60, 145);
            ctx.fillText('+ setImplementor()', 60, 165);

            // RefinedAbstraction
            ctx.fillStyle = '#e8f5e8';
            ctx.fillRect(50, 220, 180, 80);
            ctx.strokeStyle = '#4caf50';
            ctx.strokeRect(50, 220, 180, 80);
            ctx.fillStyle = '#2e7d32';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('RefinedAbstraction', 70, 245);
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('+ refinedOperation()', 60, 265);
            ctx.fillText('+ extendedOperation()', 60, 285);

            // Implementor
            ctx.fillStyle = '#f3e5f5';
            ctx.fillRect(570, 80, 180, 100);
            ctx.strokeStyle = '#9c27b0';
            ctx.strokeRect(570, 80, 180, 100);
            ctx.fillStyle = '#7b1fa2';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('Implementor', 620, 105);
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('+ operationImpl()', 580, 125);

            // ConcreteImplementor
            ctx.fillStyle = '#fff3e0';
            ctx.fillRect(570, 220, 180, 80);
            ctx.strokeStyle = '#f57c00';
            ctx.strokeRect(570, 220, 180, 80);
            ctx.fillStyle = '#ef6c00';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('ConcreteImplementor', 590, 245);
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('+ operationImpl()', 580, 265);

            // 绘制关系线
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;

            // 继承关系
            ctx.beginPath();
            ctx.moveTo(140, 220);
            ctx.lineTo(140, 180);
            ctx.stroke();

            // 实现关系
            ctx.beginPath();
            ctx.moveTo(660, 220);
            ctx.lineTo(660, 180);
            ctx.stroke();

            // 组合关系（桥接）
            ctx.strokeStyle = '#ff9800';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(230, 130);
            ctx.lineTo(570, 130);
            ctx.stroke();

            // 添加箭头
            ctx.beginPath();
            ctx.moveTo(560, 125);
            ctx.lineTo(570, 130);
            ctx.lineTo(560, 135);
            ctx.stroke();

            ctx.fillStyle = '#ff9800';
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('桥接关系', 380, 120);
        }

        function highlightComponents() {
            const canvas = document.getElementById('structureCanvas');
            const ctx = canvas.getContext('2d');

            // 重绘结构图
            animateStructure();

            // 添加高亮效果
            setTimeout(() => {
                ctx.strokeStyle = '#ff4444';
                ctx.lineWidth = 4;
                ctx.setLineDash([5, 5]);
                ctx.strokeRect(48, 78, 184, 104); // Abstraction

                setTimeout(() => {
                    ctx.strokeRect(568, 78, 184, 104); // Implementor
                }, 500);

                setTimeout(() => {
                    ctx.strokeRect(48, 218, 184, 84); // RefinedAbstraction
                }, 1000);

                setTimeout(() => {
                    ctx.strokeRect(568, 218, 184, 84); // ConcreteImplementor
                }, 1500);
            }, 100);
        }

        // 动画演示相关函数
        function startBridgeAnimation() {
            const canvas = document.getElementById('animationCanvas');
            const ctx = canvas.getContext('2d');

            isAnimating = true;
            animationStep = 0;

            function animate() {
                if (!isAnimating) return;

                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.font = '18px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.fillText('Bridge模式工作流程动画', 250, 30);

                // 步骤1：创建抽象产品
                if (animationStep >= 0) {
                    ctx.fillStyle = '#e3f2fd';
                    ctx.fillRect(100, 80, 150, 80);
                    ctx.strokeStyle = '#2196f3';
                    ctx.strokeRect(100, 80, 150, 80);
                    ctx.fillStyle = '#1976d2';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('宣传册', 150, 110);
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('抽象产品', 140, 130);
                }

                // 步骤2：创建具体实现
                if (animationStep >= 30) {
                    ctx.fillStyle = '#f3e5f5';
                    ctx.fillRect(550, 80, 150, 80);
                    ctx.strokeStyle = '#9c27b0';
                    ctx.strokeRect(550, 80, 150, 80);
                    ctx.fillStyle = '#7b1fa2';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('纸质出版', 590, 110);
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('具体实现', 590, 130);
                }

                // 步骤3：建立桥接
                if (animationStep >= 60) {
                    ctx.strokeStyle = '#ff9800';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(250, 120);
                    ctx.lineTo(550, 120);
                    ctx.stroke();

                    ctx.fillStyle = '#ff9800';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('桥接连接', 370, 110);
                }

                // 步骤4：执行操作
                if (animationStep >= 90) {
                    ctx.fillStyle = '#4caf50';
                    ctx.fillRect(300, 200, 200, 60);
                    ctx.strokeStyle = '#2e7d32';
                    ctx.strokeRect(300, 200, 200, 60);
                    ctx.fillStyle = '#1b5e20';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('执行produce()', 350, 225);
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('📄 纸质出版: 宣传册', 320, 245);
                }

                animationStep++;
                if (animationStep <= 120) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        function pauseAnimation() {
            isAnimating = false;
        }

        function resetAnimation() {
            isAnimating = false;
            animationStep = 0;
            const canvas = document.getElementById('animationCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function stepByStep() {
            const canvas = document.getElementById('animationCanvas');
            const ctx = canvas.getContext('2d');

            const steps = [
                () => {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillStyle = '#333';
                    ctx.fillText('步骤1: 定义抽象产品类', 50, 50);

                    ctx.fillStyle = '#e3f2fd';
                    ctx.fillRect(100, 100, 200, 100);
                    ctx.strokeStyle = '#2196f3';
                    ctx.strokeRect(100, 100, 200, 100);
                    ctx.fillStyle = '#1976d2';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('PromotionalProduct', 140, 130);
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('+ publishingMethod', 110, 150);
                    ctx.fillText('+ produce()', 110, 170);
                },
                () => {
                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText('步骤2: 定义实现接口', 400, 50);

                    ctx.fillStyle = '#f3e5f5';
                    ctx.fillRect(500, 100, 200, 100);
                    ctx.strokeStyle = '#9c27b0';
                    ctx.strokeRect(500, 100, 200, 100);
                    ctx.fillStyle = '#7b1fa2';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText('PublishingMethod', 540, 130);
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('+ publish(content)', 510, 150);
                },
                () => {
                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText('步骤3: 建立桥接关系', 300, 250);

                    ctx.strokeStyle = '#ff9800';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(300, 150);
                    ctx.lineTo(500, 150);
                    ctx.stroke();

                    ctx.fillStyle = '#ff9800';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('组合关系', 380, 140);
                },
                () => {
                    ctx.fillStyle = '#333';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText('步骤4: 创建具体类', 50, 350);

                    // 具体产品
                    ctx.fillStyle = '#e8f5e8';
                    ctx.fillRect(50, 400, 150, 80);
                    ctx.strokeStyle = '#4caf50';
                    ctx.strokeRect(50, 400, 150, 80);
                    ctx.fillStyle = '#2e7d32';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('Brochure', 100, 430);
                    ctx.fillText('Article', 100, 450);
                    ctx.fillText('Flyer', 100, 470);

                    // 具体实现
                    ctx.fillStyle = '#fff3e0';
                    ctx.fillRect(600, 400, 150, 80);
                    ctx.strokeStyle = '#f57c00';
                    ctx.strokeRect(600, 400, 150, 80);
                    ctx.fillStyle = '#ef6c00';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.fillText('PaperPublishing', 620, 430);
                    ctx.fillText('CDPublishing', 620, 450);
                    ctx.fillText('OnlinePublishing', 620, 470);
                }
            ];

            let currentStep = 0;
            steps[currentStep]();

            const nextStep = () => {
                currentStep = (currentStep + 1) % steps.length;
                steps[currentStep]();
                setTimeout(nextStep, 3000);
            };

            setTimeout(nextStep, 3000);
        }

        // 模式对比功能
        function showPatternComparison() {
            const canvas = document.getElementById('comparisonCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('设计模式对比图', 300, 30);

            const patterns = [
                { name: 'Bridge', color: '#4caf50', x: 100, y: 80, desc: '分离抽象与实现' },
                { name: 'Adapter', color: '#ff9800', x: 300, y: 80, desc: '接口转换适配' },
                { name: 'Decorator', color: '#9c27b0', x: 500, y: 80, desc: '动态添加功能' },
                { name: 'Facade', color: '#2196f3', x: 200, y: 250, desc: '简化复杂接口' }
            ];

            patterns.forEach(pattern => {
                ctx.fillStyle = pattern.color;
                ctx.fillRect(pattern.x, pattern.y, 150, 100);
                ctx.strokeStyle = pattern.color;
                ctx.strokeRect(pattern.x, pattern.y, 150, 100);

                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText(pattern.name, pattern.x + 50, pattern.y + 40);
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(pattern.desc, pattern.x + 20, pattern.y + 65);
            });

            // 高亮Bridge模式
            ctx.strokeStyle = '#ff4444';
            ctx.lineWidth = 4;
            ctx.setLineDash([5, 5]);
            ctx.strokeRect(98, 78, 154, 104);
        }

        // 练习功能
        function checkQuiz1() {
            const selected = document.querySelector('input[name="q1"]:checked');
            const resultDiv = document.getElementById('quiz1Result');

            if (!selected) {
                resultDiv.innerHTML = '<p style="color: #f44336;">请选择一个答案！</p>';
                return;
            }

            if (selected.value === 'b') {
                resultDiv.innerHTML = `
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #4caf50;">
                        <h4 style="color: #2e7d32;">✅ 正确！</h4>
                        <p>图形界面在不同操作系统上的实现是Bridge模式的典型应用场景。抽象的GUI组件与具体的操作系统实现可以独立变化。</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="background: #ffebee; padding: 15px; border-radius: 10px; border-left: 4px solid #f44336;">
                        <h4 style="color: #c62828;">❌ 不正确</h4>
                        <p>正确答案是B。Bridge模式适用于需要将抽象与实现分离的场景，如跨平台GUI开发。</p>
                    </div>
                `;
            }
        }

        function validateUserCode() {
            const code = document.getElementById('userCode').value;
            const resultDiv = document.getElementById('codeValidation');

            if (!code.trim()) {
                resultDiv.innerHTML = '<p style="color: #f44336;">请输入代码！</p>';
                return;
            }

            // 简单的代码验证
            const hasAbstraction = code.includes('class') && (code.includes('MusicPlayer') || code.includes('Player'));
            const hasImplementor = code.includes('class') && (code.includes('AudioDevice') || code.includes('Device'));
            const hasBridge = code.includes('constructor') && code.includes('this.');

            if (hasAbstraction && hasImplementor && hasBridge) {
                resultDiv.innerHTML = `
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #4caf50;">
                        <h4 style="color: #2e7d32;">✅ 很好！</h4>
                        <p>你的代码体现了Bridge模式的核心思想：抽象类、实现接口和桥接关系。</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="background: #fff3e0; padding: 15px; border-radius: 10px; border-left: 4px solid #ff9800;">
                        <h4 style="color: #f57c00;">💡 提示</h4>
                        <p>Bridge模式需要：1) 抽象类（如MusicPlayer）2) 实现接口（如AudioDevice）3) 桥接关系（组合）</p>
                    </div>
                `;
            }
        }

        // 总结功能
        function showKnowledgeMap() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('Bridge模式知识图谱', 280, 30);

            // 中心节点
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(400, 200, 60, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('Bridge', 375, 205);
            ctx.fillText('模式', 380, 220);

            // 周围节点
            const nodes = [
                { text: '分离抽象\n与实现', x: 200, y: 100, color: '#4caf50' },
                { text: '组合优于\n继承', x: 600, y: 100, color: '#ff9800' },
                { text: '运行时\n切换', x: 200, y: 300, color: '#9c27b0' },
                { text: '易于\n扩展', x: 600, y: 300, color: '#2196f3' }
            ];

            nodes.forEach(node => {
                ctx.fillStyle = node.color;
                ctx.beginPath();
                ctx.arc(node.x, node.y, 40, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = '12px Microsoft YaHei';
                const lines = node.text.split('\n');
                lines.forEach((line, i) => {
                    ctx.fillText(line, node.x - 20, node.y - 5 + i * 15);
                });

                // 连接线
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(node.x, node.y);
                ctx.lineTo(400, 200);
                ctx.stroke();
            });
        }

        function restartLearning() {
            showSection('overview');
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function downloadNotes() {
            const notes = `
Bridge桥接模式学习笔记

1. 定义：将抽象部分与它的实现部分分离，使它们都可以独立地变化

2. 核心组件：
   - Abstraction：定义抽象类的接口
   - RefinedAbstraction：扩充抽象接口
   - Implementor：定义实现类的接口
   - ConcreteImplementor：实现具体功能

3. 优势：
   - 分离抽象与实现
   - 组合优于继承
   - 运行时切换实现
   - 易于扩展

4. 应用场景：
   - 跨平台应用开发
   - 数据库驱动程序
   - 图形渲染引擎
   - 设备驱动程序

5. 与其他模式的区别：
   - vs Adapter：Bridge是设计时考虑，Adapter是事后补救
   - vs Decorator：Bridge关注结构分离，Decorator关注功能增强
   - vs Facade：Bridge分离抽象实现，Facade简化复杂接口
            `;

            const blob = new Blob([notes], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'Bridge模式学习笔记.txt';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            updateProgress();

            // 为所有canvas添加响应式处理
            const canvases = document.querySelectorAll('canvas');
            canvases.forEach(canvas => {
                const container = canvas.parentElement;
                const resizeCanvas = () => {
                    const containerWidth = container.clientWidth - 40;
                    if (containerWidth < canvas.width) {
                        const scale = containerWidth / canvas.width;
                        canvas.style.transform = `scale(${scale})`;
                        canvas.style.transformOrigin = 'top left';
                    }
                };
                resizeCanvas();
                window.addEventListener('resize', resizeCanvas);
            });
        });
    </script>
</body>
</html>
