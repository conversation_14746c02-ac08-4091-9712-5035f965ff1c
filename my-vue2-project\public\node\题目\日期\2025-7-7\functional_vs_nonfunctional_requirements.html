<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能需求与非功能需求</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f4f7f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .explanation {
            background: #e9f7fd;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .highlight {
            background-color: #fffacd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .example-box {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            background-color: #f9f9f9;
        }
        .example-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .functional {
            color: #27ae60;
        }
        .non-functional {
            color: #e74c3c;
        }
        .interactive-demo {
            margin: 20px 0;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .requirement-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background-color: #fff;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .requirement-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .requirement-card.selected {
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
        }
        .requirement-text {
            margin-bottom: 10px;
        }
        .requirement-type {
            font-weight: bold;
            text-align: right;
        }
        .feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .feedback.correct {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .feedback.incorrect {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .btn-check {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
            transition: background-color 0.3s;
        }
        .btn-check:hover {
            background-color: #2980b9;
        }
        .btn-check:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .password-example {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
        .password-form {
            margin-top: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .password-message {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .password-message.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .password-message.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .password-message.info {
            background-color: #e9f7fd;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>功能需求与非功能需求</h1>
        
        <div class="explanation">
            <p>在软件工程中，需求通常分为两大类：<strong>功能需求</strong>和<strong>非功能需求</strong>。</p>
            <p>题目中的需求："<span class="highlight">登录用户的密码30天需要修改一次，而且不能使用最近5次的密码，以提高系统的安全性</span>"属于<strong>非功能需求</strong>。</p>
        </div>

        <h2>功能需求与非功能需求的区别</h2>
        
        <div class="explanation">
            <h3>功能需求（Functional Requirements）</h3>
            <p>功能需求描述系统<strong>应该做什么</strong>，即系统应该提供的功能或服务。它们定义了系统的行为，用户可以使用系统做什么。</p>
            
            <h3>非功能需求（Non-functional Requirements）</h3>
            <p>非功能需求描述系统<strong>应该如何做</strong>，即系统应该具有的品质属性或约束条件。它们定义了系统的特性和约束，而不是具体的功能。</p>
        </div>

        <table class="comparison-table">
            <tr>
                <th>功能需求</th>
                <th>非功能需求</th>
            </tr>
            <tr>
                <td>描述系统<strong>做什么</strong></td>
                <td>描述系统<strong>如何做</strong></td>
            </tr>
            <tr>
                <td>关注系统的<strong>功能和行为</strong></td>
                <td>关注系统的<strong>品质和约束</strong></td>
            </tr>
            <tr>
                <td>例如：用户可以登录系统</td>
                <td>例如：系统响应时间不超过2秒</td>
            </tr>
            <tr>
                <td>通常使用动词描述</td>
                <td>通常使用形容词或副词描述</td>
            </tr>
        </table>

        <h2>为什么密码策略是非功能需求？</h2>
        
        <div class="explanation">
            <p>"登录用户的密码30天需要修改一次，而且不能使用最近5次的密码，以提高系统的安全性"是非功能需求，因为：</p>
            <ol>
                <li>它不是描述系统<strong>应该做什么</strong>（功能），而是描述系统<strong>应该如何做</strong>（质量属性）</li>
                <li>它关注的是系统的<strong>安全性</strong>这一品质属性，而不是具体的功能</li>
                <li>它是对<strong>已有功能</strong>（用户登录）的约束和要求，而不是新功能</li>
                <li>它定义了系统的<strong>安全策略</strong>，这属于非功能性的约束</li>
            </ol>
        </div>

        <h2>密码策略的分类</h2>
        
        <div class="example-box">
            <div class="example-title">相关的功能需求：</div>
            <ul>
                <li class="functional">系统应该允许用户登录</li>
                <li class="functional">系统应该允许用户修改密码</li>
                <li class="functional">系统应该能够存储用户的历史密码记录</li>
            </ul>
            
            <div class="example-title">相关的非功能需求：</div>
            <ul>
                <li class="non-functional">用户密码必须每30天更改一次（安全性）</li>
                <li class="non-functional">新密码不能与最近5次使用的密码相同（安全性）</li>
                <li class="non-functional">密码必须包含大小写字母、数字和特殊字符（安全性）</li>
                <li class="non-functional">密码长度必须至少为8个字符（安全性）</li>
            </ul>
        </div>

        <h2>交互演示：识别功能需求与非功能需求</h2>
        
        <div class="interactive-demo">
            <p>请选择下面的需求，判断它是功能需求还是非功能需求：</p>
            
            <div class="requirement-card" data-type="functional">
                <div class="requirement-text">系统应该允许用户上传文件</div>
                <div class="requirement-type" id="type1">?</div>
            </div>
            
            <div class="requirement-card" data-type="non-functional">
                <div class="requirement-text">系统应该在3秒内响应用户请求</div>
                <div class="requirement-type" id="type2">?</div>
            </div>
            
            <div class="requirement-card" data-type="non-functional">
                <div class="requirement-text">系统应该能够同时处理1000个用户的请求</div>
                <div class="requirement-type" id="type3">?</div>
            </div>
            
            <div class="requirement-card" data-type="functional">
                <div class="requirement-text">系统应该允许管理员删除用户账号</div>
                <div class="requirement-type" id="type4">?</div>
            </div>
            
            <div class="requirement-card" data-type="non-functional">
                <div class="requirement-text">系统应该使用SSL加密所有传输的数据</div>
                <div class="requirement-type" id="type5">?</div>
            </div>
            
            <div class="requirement-card" data-type="non-functional">
                <div class="requirement-text">用户密码必须至少包含一个大写字母、一个小写字母和一个数字</div>
                <div class="requirement-type" id="type6">?</div>
            </div>
            
            <button class="btn-check" id="btn-check">检查答案</button>
            
            <div class="feedback" id="feedback"></div>
        </div>

        <h2>密码策略示例</h2>
        
        <div class="password-example">
            <h3>密码更新演示</h3>
            <p>这个演示展示了"密码30天需要修改一次，不能使用最近5次的密码"这一非功能需求的实现效果：</p>
            
            <div class="password-form">
                <div class="form-group">
                    <label for="current-password">当前密码：</label>
                    <input type="password" id="current-password">
                </div>
                
                <div class="form-group">
                    <label for="new-password">新密码：</label>
                    <input type="password" id="new-password">
                </div>
                
                <div class="form-group">
                    <label for="confirm-password">确认新密码：</label>
                    <input type="password" id="confirm-password">
                </div>
                
                <button class="btn-check" id="btn-update-password">更新密码</button>
                
                <div class="password-message" id="password-message"></div>
            </div>
        </div>

        <h2>总结</h2>
        
        <div class="explanation">
            <p>"登录用户的密码30天需要修改一次，而且不能使用最近5次的密码，以提高系统的安全性"之所以属于非功能需求，是因为：</p>
            <ol>
                <li>它描述的是系统的<strong>安全性</strong>这一品质属性</li>
                <li>它是对系统<strong>如何实现</strong>用户认证的约束，而不是系统<strong>应该做什么</strong></li>
                <li>它关注的是<strong>质量属性</strong>（安全性），而不是功能本身</li>
            </ol>
            <p>在软件开发中，区分功能需求和非功能需求非常重要，因为它们的实现方式和优先级通常不同。功能需求通常直接影响用户体验和系统功能，而非功能需求则影响系统的质量、性能和安全性等方面。</p>
        </div>
    </div>

    <script>
        // 需求卡片交互
        const requirementCards = document.querySelectorAll('.requirement-card');
        const btnCheck = document.getElementById('btn-check');
        const feedback = document.getElementById('feedback');
        
        requirementCards.forEach(card => {
            card.addEventListener('click', () => {
                card.classList.toggle('selected');
                
                const typeElement = card.querySelector('.requirement-type');
                if (card.classList.contains('selected')) {
                    const type = card.dataset.type;
                    typeElement.innerText = type === 'functional' ? '功能需求' : '非功能需求';
                    typeElement.className = 'requirement-type ' + type;
                } else {
                    typeElement.innerText = '?';
                    typeElement.className = 'requirement-type';
                }
            });
        });
        
        btnCheck.addEventListener('click', () => {
            let allCorrect = true;
            let correctCount = 0;
            
            requirementCards.forEach(card => {
                const typeElement = card.querySelector('.requirement-type');
                const selected = card.classList.contains('selected');
                
                if (selected) {
                    const type = card.dataset.type;
                    const isCorrect = (typeElement.innerText === '功能需求' && type === 'functional') || 
                                     (typeElement.innerText === '非功能需求' && type === 'non-functional');
                    
                    if (isCorrect) {
                        correctCount++;
                    } else {
                        allCorrect = false;
                    }
                } else {
                    allCorrect = false;
                }
            });
            
            feedback.style.display = 'block';
            
            if (allCorrect) {
                feedback.className = 'feedback correct';
                feedback.innerText = '恭喜！你正确识别了所有需求类型。';
            } else {
                feedback.className = 'feedback incorrect';
                feedback.innerText = `你正确识别了 ${correctCount} 个需求类型，请继续尝试！`;
            }
        });
        
        // 密码更新演示
        const currentPasswordInput = document.getElementById('current-password');
        const newPasswordInput = document.getElementById('new-password');
        const confirmPasswordInput = document.getElementById('confirm-password');
        const btnUpdatePassword = document.getElementById('btn-update-password');
        const passwordMessage = document.getElementById('password-message');
        
        // 模拟的历史密码
        const passwordHistory = ['Password123!', 'Secure456@', 'Strong789#', 'Complex000$', 'Safety111%'];
        const currentPassword = 'Password123!';
        
        btnUpdatePassword.addEventListener('click', () => {
            const current = currentPasswordInput.value;
            const newPass = newPasswordInput.value;
            const confirm = confirmPasswordInput.value;
            
            passwordMessage.style.display = 'block';
            
            // 检查当前密码
            if (current !== currentPassword) {
                passwordMessage.className = 'password-message error';
                passwordMessage.innerText = '当前密码不正确！';
                return;
            }
            
            // 检查两次输入是否一致
            if (newPass !== confirm) {
                passwordMessage.className = 'password-message error';
                passwordMessage.innerText = '两次输入的新密码不一致！';
                return;
            }
            
            // 检查是否与历史密码重复
            if (passwordHistory.includes(newPass)) {
                passwordMessage.className = 'password-message error';
                passwordMessage.innerText = '新密码不能与最近5次使用的密码相同！（非功能需求：安全性）';
                return;
            }
            
            // 密码复杂度检查（示例）
            if (newPass.length < 8) {
                passwordMessage.className = 'password-message error';
                passwordMessage.innerText = '密码长度必须至少为8个字符！（非功能需求：安全性）';
                return;
            }
            
            // 成功更新
            passwordMessage.className = 'password-message success';
            passwordMessage.innerHTML = '密码更新成功！<br>注意：根据安全策略，你将在30天后需要再次修改密码。（非功能需求：安全性）';
            
            // 清空输入
            currentPasswordInput.value = '';
            newPasswordInput.value = '';
            confirmPasswordInput.value = '';
        });
    </script>

</body>
</html> 