<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机存储器存取方式 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .quiz-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .quiz-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .quiz-content {
            font-size: 1.1em;
            line-height: 1.8;
            color: #555;
            margin-bottom: 20px;
        }

        .answer-reveal {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .correct-answer {
            color: #28a745;
            font-weight: bold;
            font-size: 1.2em;
        }

        .learning-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .storage-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .storage-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.25);
        }

        .storage-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .card-title {
            font-size: 1.4em;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            color: white;
        }

        .sequential { background: #ff6b6b; }
        .direct { background: #4ecdc4; }
        .random { background: #45b7d1; }
        .associative { background: #96ceb4; }

        .card-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .demo-canvas {
            width: 100%;
            height: 200px;
            border: 2px solid #eee;
            border-radius: 10px;
            margin-top: 15px;
            cursor: pointer;
        }

        .interactive-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }

        .control-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: #667eea;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .control-btn:hover {
            background: #5a67d8;
            transform: scale(1.05);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            font-size: 0.95em;
            color: #555;
            line-height: 1.5;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            animation: pulse 2s infinite;
        }

        .game-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }

        .game-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
        }

        .game-canvas {
            width: 100%;
            max-width: 800px;
            height: 400px;
            border: 2px solid #eee;
            border-radius: 15px;
            margin: 20px auto;
            display: block;
            cursor: pointer;
        }

        .game-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .game-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-weight: bold;
        }

        .btn-sequential { background: #ff6b6b; }
        .btn-direct { background: #4ecdc4; }
        .btn-random { background: #45b7d1; }
        .btn-associative { background: #96ceb4; }

        .game-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .score-board {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 计算机存储器存取方式</h1>
            <p>通过动画和游戏学习四种存取方式的区别</p>
        </div>

        <div class="quiz-section">
            <div class="quiz-title">📝 原题回顾</div>
            <div class="quiz-content">
                <strong>题目：</strong>计算机系统中，（ ）方式是根据所访问的内容来决定要访问的存储单元，常用在（ ）存储器中。
                <br><br>
                <strong>选项：</strong><br>
                A. 顺序存取<br>
                B. 直接存取<br>
                C. 随机存取<br>
                D. 相联存取
            </div>
            <div class="answer-reveal">
                <div class="correct-answer">✅ 正确答案：D - 相联存取</div>
                <p><strong>关键理解：</strong>"根据所访问的内容来决定要访问的存储单元" - 这是相联存取的核心特征！</p>
            </div>
        </div>

        <div class="learning-section">
            <div class="storage-card" onclick="demonstrateSequential()">
                <div class="card-title">
                    <div class="card-icon sequential">📼</div>
                    顺序存取
                </div>
                <div class="card-description">
                    必须按照固定的线性顺序访问数据，就像播放磁带一样，要听第10首歌必须先播放前9首。
                </div>
                <canvas class="demo-canvas" id="sequentialCanvas"></canvas>
                <div class="interactive-controls">
                    <button class="control-btn" onclick="startSequentialDemo()">开始演示</button>
                    <button class="control-btn" onclick="resetSequentialDemo()">重置</button>
                </div>
                <div class="explanation">
                    <strong>典型应用：</strong>磁带存储器<br>
                    <strong>特点：</strong>访问时间可变，必须按顺序访问
                </div>
            </div>

            <div class="storage-card" onclick="demonstrateDirect()">
                <div class="card-title">
                    <div class="card-icon direct">💿</div>
                    直接存取
                </div>
                <div class="card-description">
                    每个数据块有唯一地址，可以直接跳转到目标位置，就像CD播放器可以直接跳到任意曲目。
                </div>
                <canvas class="demo-canvas" id="directCanvas"></canvas>
                <div class="interactive-controls">
                    <button class="control-btn" onclick="startDirectDemo()">开始演示</button>
                    <button class="control-btn" onclick="resetDirectDemo()">重置</button>
                </div>
                <div class="explanation">
                    <strong>典型应用：</strong>磁盘存储器<br>
                    <strong>特点：</strong>可直接定位，访问时间仍可变
                </div>
            </div>
            <div class="storage-card" onclick="demonstrateRandom()">
                <div class="card-title">
                    <div class="card-icon random">🧠</div>
                    随机存取
                </div>
                <div class="card-description">
                    每个存储单元都有独立的读写装置，可以在相同时间内访问任意位置，就像内存条一样。
                </div>
                <canvas class="demo-canvas" id="randomCanvas"></canvas>
                <div class="interactive-controls">
                    <button class="control-btn" onclick="startRandomDemo()">开始演示</button>
                    <button class="control-btn" onclick="resetRandomDemo()">重置</button>
                </div>
                <div class="explanation">
                    <strong>典型应用：</strong>主存储器(RAM)<br>
                    <strong>特点：</strong>访问时间恒定，可随机访问任意位置
                </div>
            </div>

            <div class="storage-card" onclick="demonstrateAssociative()">
                <div class="card-title">
                    <div class="card-icon associative">🔍</div>
                    相联存取 ⭐
                </div>
                <div class="card-description">
                    <strong>根据内容而非地址来访问！</strong>系统会比较所有存储单元的特定位，找到匹配的内容。
                </div>
                <canvas class="demo-canvas" id="associativeCanvas"></canvas>
                <div class="interactive-controls">
                    <button class="control-btn" onclick="startAssociativeDemo()">开始演示</button>
                    <button class="control-btn" onclick="resetAssociativeDemo()">重置</button>
                </div>
                <div class="explanation">
                    <strong>典型应用：</strong>Cache存储器<br>
                    <strong>特点：</strong>按内容查找，所有单元同时比较
                </div>
            </div>
        </div>

        <div class="game-section">
            <div class="game-title">🎮 存取方式识别游戏</div>
            <p>根据描述选择正确的存取方式，测试你的理解程度！</p>
            <canvas class="game-canvas" id="gameCanvas"></canvas>
            <div class="game-controls">
                <button class="game-btn btn-sequential" onclick="selectAnswer('sequential')">顺序存取</button>
                <button class="game-btn btn-direct" onclick="selectAnswer('direct')">直接存取</button>
                <button class="game-btn btn-random" onclick="selectAnswer('random')">随机存取</button>
                <button class="game-btn btn-associative" onclick="selectAnswer('associative')">相联存取</button>
            </div>
            <div class="score-board">
                <div id="scoreDisplay">得分: 0 | 题目: 1/5</div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationId;
        let currentDemo = null;
        let gameScore = 0;
        let currentQuestion = 1;
        let gameQuestions = [
            {
                question: "磁带播放器要播放第10首歌，必须先播放前9首歌",
                answer: "sequential",
                explanation: "这是典型的顺序存取，必须按固定顺序访问"
            },
            {
                question: "内存可以在相同时间内访问任意地址的数据",
                answer: "random",
                explanation: "随机存取的特点是访问时间恒定，可随机访问"
            },
            {
                question: "Cache根据数据内容而不是地址来查找匹配的数据",
                answer: "associative",
                explanation: "相联存取的核心特征：根据内容查找"
            },
            {
                question: "硬盘可以直接跳转到指定扇区，但访问时间可变",
                answer: "direct",
                explanation: "直接存取可以直接定位，但时间仍可变"
            },
            {
                question: "所有存储单元同时进行内容比较，找到匹配项",
                answer: "associative",
                explanation: "相联存取的工作方式：并行比较所有单元"
            }
        ];

        // 顺序存取演示
        function startSequentialDemo() {
            const canvas = document.getElementById('sequentialCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            
            let position = 0;
            let target = 8; // 目标是第8个数据块
            let speed = 2;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制磁带
                ctx.fillStyle = '#8B4513';
                ctx.fillRect(20, canvas.height/2 - 20, canvas.width - 40, 40);
                
                // 绘制数据块
                for (let i = 0; i < 10; i++) {
                    const x = 40 + i * (canvas.width - 80) / 9;
                    const y = canvas.height/2 - 15;
                    
                    if (i === target) {
                        ctx.fillStyle = '#ff6b6b'; // 目标块为红色
                    } else if (i <= position) {
                        ctx.fillStyle = '#90EE90'; // 已访问的块为绿色
                    } else {
                        ctx.fillStyle = '#D3D3D3'; // 未访问的块为灰色
                    }
                    
                    ctx.fillRect(x - 10, y, 20, 30);
                    ctx.fillStyle = '#000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(i + 1, x, y + 20);
                }
                
                // 绘制读取头
                const headX = 40 + position * (canvas.width - 80) / 9;
                ctx.fillStyle = '#FF4500';
                ctx.beginPath();
                ctx.moveTo(headX, canvas.height/2 - 40);
                ctx.lineTo(headX - 10, canvas.height/2 - 25);
                ctx.lineTo(headX + 10, canvas.height/2 - 25);
                ctx.closePath();
                ctx.fill();
                
                // 显示提示文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`正在访问第 ${position + 1} 个数据块`, 20, 30);
                ctx.fillText(`目标：第 ${target + 1} 个数据块`, 20, 50);
                
                if (position < target) {
                    position += 0.1;
                    animationId = requestAnimationFrame(animate);
                } else {
                    ctx.fillStyle = '#28a745';
                    ctx.font = '18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('✅ 到达目标！必须按顺序访问每个数据块', canvas.width/2, canvas.height - 20);
                }
            }
            
            animate();
        }
        
        function resetSequentialDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            const canvas = document.getElementById('sequentialCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制初始状态
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(20, canvas.height/2 - 20, canvas.width - 40, 40);
            
            for (let i = 0; i < 10; i++) {
                const x = 40 + i * (canvas.width - 80) / 9;
                const y = canvas.height/2 - 15;
                
                ctx.fillStyle = i === 8 ? '#ff6b6b' : '#D3D3D3';
                ctx.fillRect(x - 10, y, 20, 30);
                ctx.fillStyle = '#000';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(i + 1, x, y + 20);
            }
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击"开始演示"看看顺序存取如何工作', canvas.width/2, 30);
        }
        
        // 直接存取演示
        function startDirectDemo() {
            const canvas = document.getElementById('directCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
            
            let currentTrack = 0;
            let targetTrack = 6;
            let animationStep = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制磁盘
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const radius = Math.min(centerX, centerY) - 20;
                
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                ctx.stroke();
                
                // 绘制磁道
                for (let i = 1; i <= 8; i++) {
                    const trackRadius = radius * i / 8;
                    ctx.strokeStyle = i === targetTrack + 1 ? '#ff6b6b' : '#ccc';
                    ctx.lineWidth = i === targetTrack + 1 ? 3 : 1;
                    ctx.beginPath();
                    ctx.arc(centerX, centerY, trackRadius, 0, 2 * Math.PI);
                    ctx.stroke();
                    
                    // 标记磁道号
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`T${i}`, centerX + trackRadius - 15, centerY - 5);
                }
                
                // 绘制读写头
                const headAngle = animationStep * 0.1;
                const headRadius = radius * (currentTrack + 1) / 8;
                const headX = centerX + Math.cos(headAngle) * headRadius;
                const headY = centerY + Math.sin(headAngle) * headRadius;
                
                ctx.fillStyle = '#FF4500';
                ctx.beginPath();
                ctx.arc(headX, headY, 8, 0, 2 * Math.PI);
                ctx.fill();
                
                // 显示信息
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`当前磁道: ${currentTrack + 1}`, 20, 30);
                ctx.fillText(`目标磁道: ${targetTrack + 1}`, 20, 50);
                
                if (currentTrack !== targetTrack) {
                    if (currentTrack < targetTrack) {
                        currentTrack += 0.05;
                    } else {
                        currentTrack -= 0.05;
                    }
                    animationStep++;
                    animationId = requestAnimationFrame(animate);
                } else {
                    ctx.fillStyle = '#28a745';
                    ctx.font = '18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('✅ 直接跳转到目标磁道！', centerX, canvas.height - 20);
                }
            }
            
            animate();
        }
        
        function resetDirectDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            const canvas = document.getElementById('directCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制初始状态
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = Math.min(centerX, centerY) - 20;
            
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.stroke();
            
            for (let i = 1; i <= 8; i++) {
                const trackRadius = radius * i / 8;
                ctx.strokeStyle = '#ccc';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.arc(centerX, centerY, trackRadius, 0, 2 * Math.PI);
                ctx.stroke();
                
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`T${i}`, centerX + trackRadius - 15, centerY - 5);
            }
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击"开始演示"看看直接存取如何工作', centerX, 30);
        }
        
        // 随机存取演示
        function startRandomDemo() {
            const canvas = document.getElementById('randomCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let accessSequence = [2, 7, 1, 5, 9, 3]; // 随机访问序列
            let currentAccess = 0;
            let animationStep = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制内存条
                const memoryWidth = canvas.width - 40;
                const memoryHeight = 40;
                const memoryX = 20;
                const memoryY = canvas.height/2 - 20;

                ctx.fillStyle = '#2E8B57';
                ctx.fillRect(memoryX, memoryY, memoryWidth, memoryHeight);

                // 绘制内存单元
                const unitWidth = memoryWidth / 10;
                for (let i = 0; i < 10; i++) {
                    const x = memoryX + i * unitWidth;
                    const y = memoryY;

                    if (currentAccess < accessSequence.length && i === accessSequence[currentAccess] - 1) {
                        ctx.fillStyle = '#FFD700'; // 当前访问的单元为金色
                    } else {
                        ctx.fillStyle = '#87CEEB'; // 其他单元为浅蓝色
                    }

                    ctx.fillRect(x + 2, y + 2, unitWidth - 4, memoryHeight - 4);

                    // 绘制地址
                    ctx.fillStyle = '#000';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(i + 1, x + unitWidth/2, y + memoryHeight/2 + 4);
                }

                // 显示访问信息
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                if (currentAccess < accessSequence.length) {
                    ctx.fillText(`正在访问地址: ${accessSequence[currentAccess]}`, 20, 30);
                    ctx.fillText(`访问序列: ${accessSequence.join(' → ')}`, 20, 50);

                    // 显示访问时间
                    ctx.fillStyle = '#28a745';
                    ctx.fillText('⏱️ 访问时间: 恒定 (每次都一样快)', 20, canvas.height - 20);
                }

                animationStep++;
                if (animationStep % 60 === 0 && currentAccess < accessSequence.length - 1) {
                    currentAccess++;
                }

                if (currentAccess < accessSequence.length - 1) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    ctx.fillStyle = '#28a745';
                    ctx.font = '18px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('✅ 随机存取完成！任意位置访问时间相同', canvas.width/2, canvas.height - 40);
                }
            }

            animate();
        }

        function resetRandomDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            const canvas = document.getElementById('randomCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制初始状态
            const memoryWidth = canvas.width - 40;
            const memoryHeight = 40;
            const memoryX = 20;
            const memoryY = canvas.height/2 - 20;

            ctx.fillStyle = '#2E8B57';
            ctx.fillRect(memoryX, memoryY, memoryWidth, memoryHeight);

            const unitWidth = memoryWidth / 10;
            for (let i = 0; i < 10; i++) {
                const x = memoryX + i * unitWidth;
                const y = memoryY;

                ctx.fillStyle = '#87CEEB';
                ctx.fillRect(x + 2, y + 2, unitWidth - 4, memoryHeight - 4);

                ctx.fillStyle = '#000';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(i + 1, x + unitWidth/2, y + memoryHeight/2 + 4);
            }

            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击"开始演示"看看随机存取如何工作', canvas.width/2, 30);
        }

        // 相联存取演示
        function startAssociativeDemo() {
            const canvas = document.getElementById('associativeCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            let searchContent = "DATA_X";
            let memoryData = ["DATA_A", "DATA_B", "DATA_X", "DATA_C", "DATA_D", "DATA_E", "DATA_F", "DATA_X"];
            let animationStep = 0;
            let foundIndices = [];
            let searchComplete = false;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制存储单元
                const unitWidth = (canvas.width - 40) / 8;
                const unitHeight = 60;
                const startY = canvas.height/2 - 30;

                for (let i = 0; i < 8; i++) {
                    const x = 20 + i * unitWidth;
                    const y = startY;

                    // 判断是否匹配
                    const isMatch = memoryData[i] === searchContent;
                    const isFound = foundIndices.includes(i);

                    if (isMatch && isFound) {
                        ctx.fillStyle = '#FF6B6B'; // 匹配且已找到 - 红色
                    } else if (animationStep > i * 10) {
                        ctx.fillStyle = '#90EE90'; // 已比较 - 绿色
                    } else {
                        ctx.fillStyle = '#D3D3D3'; // 未比较 - 灰色
                    }

                    ctx.fillRect(x, y, unitWidth - 5, unitHeight);

                    // 绘制数据内容
                    ctx.fillStyle = '#000';
                    ctx.font = '10px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(memoryData[i], x + unitWidth/2 - 2.5, y + 20);
                    ctx.fillText(`地址${i}`, x + unitWidth/2 - 2.5, y + 35);

                    // 绘制比较器
                    if (animationStep > i * 10 && !searchComplete) {
                        ctx.strokeStyle = '#FF4500';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(x - 2, y - 2, unitWidth - 1, unitHeight + 4);
                    }
                }

                // 显示搜索信息
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'left';
                ctx.fillText(`搜索内容: ${searchContent}`, 20, 30);

                if (!searchComplete) {
                    ctx.fillText('🔍 所有单元同时进行内容比较...', 20, 50);
                } else {
                    ctx.fillStyle = '#28a745';
                    ctx.fillText(`✅ 找到匹配项在地址: ${foundIndices.join(', ')}`, 20, 50);
                }

                // 检查匹配
                if (animationStep > 80 && !searchComplete) {
                    for (let i = 0; i < memoryData.length; i++) {
                        if (memoryData[i] === searchContent) {
                            foundIndices.push(i);
                        }
                    }
                    searchComplete = true;
                }

                animationStep++;
                if (!searchComplete || animationStep < 120) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    ctx.fillStyle = '#28a745';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('相联存取的特点：根据内容查找，所有单元并行比较', canvas.width/2, canvas.height - 10);
                }
            }

            animate();
        }

        function resetAssociativeDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            const canvas = document.getElementById('associativeCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制初始状态
            const unitWidth = (canvas.width - 40) / 8;
            const unitHeight = 60;
            const startY = canvas.height/2 - 30;
            const memoryData = ["DATA_A", "DATA_B", "DATA_X", "DATA_C", "DATA_D", "DATA_E", "DATA_F", "DATA_X"];

            for (let i = 0; i < 8; i++) {
                const x = 20 + i * unitWidth;
                const y = startY;

                ctx.fillStyle = '#D3D3D3';
                ctx.fillRect(x, y, unitWidth - 5, unitHeight);

                ctx.fillStyle = '#000';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(memoryData[i], x + unitWidth/2 - 2.5, y + 20);
                ctx.fillText(`地址${i}`, x + unitWidth/2 - 2.5, y + 35);
            }

            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击"开始演示"看看相联存取如何根据内容查找', canvas.width/2, 30);
        }

        // 游戏相关函数
        function initGame() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;

            drawGameQuestion();
        }

        function drawGameQuestion() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (currentQuestion <= gameQuestions.length) {
                const question = gameQuestions[currentQuestion - 1];

                // 绘制问题背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(20, 20, canvas.width - 40, canvas.height - 40);

                // 绘制问题文本
                ctx.fillStyle = '#333';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';

                // 分行显示问题
                const words = question.question.split('');
                let line = '';
                let y = canvas.height/2 - 20;

                for (let i = 0; i < words.length; i++) {
                    const testLine = line + words[i];
                    const metrics = ctx.measureText(testLine);

                    if (metrics.width > canvas.width - 80 && i > 0) {
                        ctx.fillText(line, canvas.width/2, y);
                        line = words[i];
                        y += 30;
                    } else {
                        line = testLine;
                    }
                }
                ctx.fillText(line, canvas.width/2, y);

                // 绘制提示
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText('请选择正确的存取方式：', canvas.width/2, canvas.height - 40);
            } else {
                // 游戏结束
                ctx.fillStyle = '#28a745';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 游戏完成！', canvas.width/2, canvas.height/2 - 40);

                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.fillText(`最终得分: ${gameScore}/${gameQuestions.length}`, canvas.width/2, canvas.height/2);

                let performance = '';
                if (gameScore === gameQuestions.length) {
                    performance = '完美！你已经完全掌握了存取方式的概念！';
                } else if (gameScore >= gameQuestions.length * 0.8) {
                    performance = '很好！你对存取方式有很好的理解！';
                } else {
                    performance = '继续学习，你会掌握得更好的！';
                }

                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText(performance, canvas.width/2, canvas.height/2 + 40);
            }
        }

        function selectAnswer(answer) {
            if (currentQuestion > gameQuestions.length) return;

            const question = gameQuestions[currentQuestion - 1];
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');

            if (answer === question.answer) {
                gameScore++;
                // 显示正确反馈
                ctx.fillStyle = '#28a745';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('✅ 正确！', canvas.width/2, canvas.height - 80);
            } else {
                // 显示错误反馈
                ctx.fillStyle = '#dc3545';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('❌ 错误', canvas.width/2, canvas.height - 80);
            }

            // 显示解释
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.fillText(question.explanation, canvas.width/2, canvas.height - 60);

            // 更新得分显示
            document.getElementById('scoreDisplay').textContent =
                `得分: ${gameScore} | 题目: ${currentQuestion}/${gameQuestions.length}`;

            // 延迟显示下一题
            setTimeout(() => {
                currentQuestion++;
                drawGameQuestion();
                document.getElementById('scoreDisplay').textContent =
                    `得分: ${gameScore} | 题目: ${Math.min(currentQuestion, gameQuestions.length)}/${gameQuestions.length}`;
            }, 2000);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            resetSequentialDemo();
            resetDirectDemo();
            resetRandomDemo();
            resetAssociativeDemo();
            initGame();
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            setTimeout(() => {
                resetSequentialDemo();
                resetDirectDemo();
                resetRandomDemo();
                resetAssociativeDemo();
                drawGameQuestion();
            }, 100);
        });
    </script>
</body>
</html>
