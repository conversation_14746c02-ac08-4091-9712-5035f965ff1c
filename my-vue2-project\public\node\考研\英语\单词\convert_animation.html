<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Convert</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #9c27b0; /* A purple color for "convert" */
            --secondary-color: #6a1b9a;
            --accent-color: #ff9800;
            --light-bg: #f3e5f5;
            --panel-bg: #ffffff;
            --text-color: #333;
            --text-muted: #7f8c8d;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #f0f2f5;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--secondary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--text-muted);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #555;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .breakdown-section .morpheme-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .morpheme-btn {
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .breakdown-section .insight {
            margin-top: 15px;
            font-style: italic;
            color: #555;
        }

        .animation-panel {
            flex: 2;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            background: #fdfdfd;
        }

        .activity-title {
            font-size: 1.8em;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }
        
        .activity-wrapper { display: none; width: 100%; height: 100%; flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        
        .game-container { width: 90%; max-width: 500px; height: 350px; border: 2px dashed #bdc3c7; border-radius: 15px; position: relative; overflow: hidden; background: linear-gradient(135deg, #ecf0f1, #ffffff); display: flex; align-items: center; justify-content: center; }

        .control-button { margin-top: 30px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; box-shadow: 0 4px 15px rgba(156, 39, 176, 0.4); }
        .control-button:hover { background-color: #7b1fa2; transform: translateY(-2px); }
        
        #welcome-screen p { font-size: 1.2em; color: var(--text-muted); text-align: center; }

        /* --- Morpheme Game Styles for Convert --- */
        #con-circle { position: relative; width: 150px; height: 150px; }
        #con-circle-part { position: absolute; width: 150px; height: 150px; border: 15px solid transparent; border-top: 15px solid var(--primary-color); border-right: 15px solid var(--primary-color); border-radius: 50%; transition: all 1s; }
        .con-game-run #con-circle-part { border-bottom: 15px solid var(--primary-color); border-left: 15px solid var(--primary-color); }

        #vert-square { width: 100px; height: 100px; background: var(--accent-color); transition: all 1s ease-in-out; }
        .vert-game-run #vert-square { transform: rotate(180deg); }
        
        #ible-shape { width: 100px; height: 100px; background: var(--accent-color); transition: all 0.7s ease-in-out; border-radius: 0; transform: rotate(0deg); }
        .ible-game-run #ible-shape { background: var(--primary-color); border-radius: 50%; transform: rotate(360deg); }

        .ion-machine { position: absolute; width: 120px; height: 120px; background: #7f8c8d; color: white; font-size: 20px; display: flex; align-items: center; justify-content: center; top: 50%; left: 50%; transform: translate(-50%, -50%); border-radius: 10px; z-index: 2; }
        .ion-particle { position: absolute; width: 25px; height: 25px; background: var(--accent-color); border-radius: 50%; top: calc(50% - 12.5px); left: -30px; opacity: 0; }
        .ion-game-run .ion-particle { animation: convert-process 2.5s ease-in-out forwards; }
        .ion-game-run .p2 { animation-delay: 0.4s; }
        .ion-game-run .p3 { animation-delay: 0.8s; }

        @keyframes convert-process {
            0% { left: 5%; opacity: 1; transform: scale(1); background: var(--accent-color); }
            45% { left: 50%; transform: scale(0); }
            55% { left: 50%; transform: scale(0); background: var(--primary-color); }
            100% { left: 95%; transform: translateX(-100%) scale(1); opacity: 1; background: var(--primary-color); }
        }

        /* --- Full Animation for Convert --- */
        #convert-scene { width: 100%; height: 100%; position: relative; overflow: hidden; }
        #convert-leaf { position: absolute; width: 200px; height: 100px; background: #4CAF50; bottom: 50px; left: 50%; transform: translateX(-50%); border-radius: 100px / 50px; }
        #convert-caterpillar { position: absolute; width: 80px; height: 30px; background: #8bc34a; border-radius: 15px; bottom: 150px; left: 45%; transition: all 1s; }
        #convert-cocoon { position: absolute; width: 40px; height: 60px; background: #d4e157; border-radius: 20px; opacity: 0; bottom: 150px; left: 47%; transition: all 1s; }
        #convert-butterfly { position: absolute; width: 60px; height: 40px; background: var(--accent-color); bottom: 150px; left: 46%; opacity: 0; transition: all 2s; clip-path: polygon(0% 50%, 33% 0%, 66% 0%, 100% 50%, 66% 100%, 33% 100%); }
        #convert-butterfly::before, #convert-butterfly::after { content: ''; position: absolute; width: 30px; height: 20px; background: var(--primary-color); top: 10px; border-radius: 50%; }
        #convert-butterfly::before { left: -15px; }
        #convert-butterfly::after { right: -15px; }

        .convert-animation-step1 #convert-caterpillar { opacity: 0; }
        .convert-animation-step1 #convert-cocoon { opacity: 1; }
        .convert-animation-step2 #convert-cocoon { opacity: 0; }
        .convert-animation-step2 #convert-butterfly { opacity: 1; bottom: 200px; transform: rotate(-5deg) translateX(-20px); }

    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>convert</h1>
            <p class="pronunciation">[kənˈvɜːt]</p>
            <div class="details">
                <p><strong>词性：</strong> 动词 (v.)</p>
                <p><strong>含义：</strong> 转变，转换，改变信仰</p>
                <div class="example">
                    <p><strong>例句：</strong> We need to convert this file to PDF format.</p>
                    <p><strong>翻译：</strong> 我们需要将这个文件转换成PDF格式。</p>
                </div>
            </div>

            <div class="breakdown-section">
                <h3>交互式词缀解析</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="con-game">con- (彻底地)</button>
                    <button class="morpheme-btn" data-activity="vert-game">-vert- (转动)</button>
                    <button class="morpheme-btn" data-activity="ible-game">-ible (能够...的)</button>
                    <button class="morpheme-btn" data-activity="ion-game">-ion (过程/结果)</button>
                </div>
                <p class="insight">点击上方词缀，体验其含义的互动游戏！</p>
            </div>
            
            <div class="breakdown-section">
                <h3>完整单词活动</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画演示</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎！</h2>

            <div id="welcome-screen" class="activity-wrapper active">
                <p>点击左侧的词缀按钮<br>开始你的交互式学习之旅！</p>
            </div>

            <!-- Morpheme Games -->
            <div id="con-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="con-circle">
                        <div id="con-circle-part"></div>
                    </div>
                </div>
                <button class="control-button" id="con-btn">"彻底"完成 (con-)</button>
            </div>
            
            <div id="vert-game" class="activity-wrapper">
                 <div class="game-container">
                    <div id="vert-square"></div>
                </div>
                <button class="control-button" id="vert-btn">开始"转动" (-vert-)</button>
            </div>

            <div id="ible-game" class="activity-wrapper">
                <div class="game-container">
                   <div id="ible-shape"></div>
               </div>
               <button class="control-button" id="ible-btn">测试"能否"转换 (-ible)</button>
           </div>

            <div id="ion-game" class="activity-wrapper">
                <div class="game-container">
                    <div class="ion-machine">转换器</div>
                    <div class="ion-particle p1"></div>
                    <div class="ion-particle p2"></div>
                    <div class="ion-particle p3"></div>
                </div>
                <button class="control-button" id="ion-btn">开始"转换过程" (-ion)</button>
            </div>

            <!-- Full Word Activities -->
            <div id="full-animation" class="activity-wrapper">
                <div class="game-container">
                    <div id="convert-scene">
                        <div id="convert-leaf"></div>
                        <div id="convert-caterpillar"></div>
                        <div id="convert-cocoon"></div>
                        <div id="convert-butterfly"></div>
                    </div>
                </div>
                <button class="control-button" id="convert-btn">开始转变！</button>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetId = btn.dataset.activity;
                const targetWrapper = document.getElementById(targetId);
                
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                activityWrappers.forEach(w => w.classList.remove('active'));
                targetWrapper.classList.add('active');
                
                activityTitle.textContent = btn.textContent;
            });
        });

        // Morpheme Games Logic
        const conBtn = document.getElementById('con-btn');
        const conContainer = conBtn.previousElementSibling;
        conBtn.addEventListener('click', () => {
            conContainer.classList.toggle('con-game-run');
        });

        const vertBtn = document.getElementById('vert-btn');
        const vertContainer = vertBtn.previousElementSibling;
        vertBtn.addEventListener('click', () => {
            vertContainer.classList.toggle('vert-game-run');
        });

        const ibleBtn = document.getElementById('ible-btn');
        const ibleContainer = ibleBtn.previousElementSibling;
        ibleBtn.addEventListener('click', () => {
            ibleContainer.classList.toggle('ible-game-run');
        });

        const ionBtn = document.getElementById('ion-btn');
        const ionContainer = ionBtn.previousElementSibling;
        ionBtn.addEventListener('click', () => {
            ionContainer.classList.remove('ion-game-run');
            void ionContainer.offsetWidth; // Trigger reflow to restart animation
            ionContainer.classList.add('ion-game-run');
        });

        // Full Word Animation Logic
        const convertBtn = document.getElementById('convert-btn');
        const convertScene = document.getElementById('convert-scene');
        let animationState = 0;

        convertBtn.addEventListener('click', () => {
            // Reset animation
            convertScene.classList.remove('convert-animation-step1', 'convert-animation-step2');
            
            // Progress through animation states
            animationState = (animationState + 1) % 3;
            
            if (animationState === 1) {
                convertScene.classList.add('convert-animation-step1');
                convertBtn.textContent = '完成转变！';
            } else if (animationState === 2) {
                convertScene.classList.add('convert-animation-step2');
                convertBtn.textContent = '重新开始';
            } else {
                convertBtn.textContent = '开始转变！';
            }
        });
    });
    </script>

</body>
</html> 