<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前趋图学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 2.5em;
            color: white;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2em;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .content-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8em;
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        #graphCanvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: #f8fafc;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .explanation {
            background: #f7fafc;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            animation: slideInLeft 0.8s ease-out;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            transform: scale(1.1);
        }

        .step.completed {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .answer-section {
            margin-top: 30px;
            padding: 20px;
            background: #f0fff4;
            border-radius: 15px;
            border: 2px solid #68d391;
        }

        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .option.correct {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .option.wrong {
            border-color: #f56565;
            background: #fed7d7;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            animation: pulse 1s infinite;
        }

        .process-node {
            transition: all 0.3s ease;
        }

        .process-node:hover {
            filter: brightness(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔄 前趋图学习</h1>
            <p class="subtitle">通过动画和交互理解进程前趋关系</p>
        </div>

        <div class="content-section">
            <h2 class="section-title">📚 什么是前趋图？</h2>
            <div class="explanation">
                <h3>🎯 零基础理解</h3>
                <p><strong>前趋图就像做菜的步骤图！</strong></p>
                <p>想象你要做一道菜：</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>🥕 先洗菜 → 再切菜 → 最后炒菜</li>
                    <li>🔥 先开火 → 再放油 → 最后放菜</li>
                </ul>
                <p>前趋图就是用箭头来表示这种"必须先做什么，才能做什么"的关系。</p>
                <br>
                <h3>🔬 专业定义</h3>
                <p><strong>前趋图（Precedence Graph）</strong>是一个有向无环图，用来表示进程之间的执行顺序关系。</p>
                <p><strong>数学表示：</strong>→ = {(Pi, Pj) | Pi must complete before Pj may start}</p>
                <p><strong>简单理解：</strong>如果有一条从P1指向P2的箭头，那么P1必须先完成，P2才能开始执行。</p>
                <br>
                <h3>🎨 图形元素</h3>
                <div style="display: flex; gap: 20px; margin: 15px 0; flex-wrap: wrap;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 30px; height: 30px; border-radius: 50%; background: #667eea; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold;">P</div>
                        <span>= 进程（Process）</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 40px; height: 2px; background: #4a5568; position: relative;">
                            <div style="position: absolute; right: -5px; top: -3px; width: 0; height: 0; border-left: 8px solid #4a5568; border-top: 4px solid transparent; border-bottom: 4px solid transparent;"></div>
                        </div>
                        <span>= 前趋关系（必须先后顺序）</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加生活例子演示 -->
        <div class="content-section">
            <h2 class="section-title">� 生活中的前趋关系</h2>
            <div class="explanation">
                <h3>做早餐的例子：</h3>
                <div style="display: flex; justify-content: space-around; margin: 20px 0; flex-wrap: wrap; gap: 15px;">
                    <div style="text-align: center; padding: 15px; background: #f0f9ff; border-radius: 10px; border: 2px solid #0ea5e9;">
                        <div style="font-size: 2em; margin-bottom: 10px;">🥚</div>
                        <div><strong>打鸡蛋</strong></div>
                        <div style="font-size: 0.9em; color: #666;">必须先做</div>
                    </div>
                    <div style="display: flex; align-items: center; font-size: 1.5em; color: #0ea5e9;">→</div>
                    <div style="text-align: center; padding: 15px; background: #fef3c7; border-radius: 10px; border: 2px solid #f59e0b;">
                        <div style="font-size: 2em; margin-bottom: 10px;">🔥</div>
                        <div><strong>热锅</strong></div>
                        <div style="font-size: 0.9em; color: #666;">然后做</div>
                    </div>
                    <div style="display: flex; align-items: center; font-size: 1.5em; color: #f59e0b;">→</div>
                    <div style="text-align: center; padding: 15px; background: #fef2f2; border-radius: 10px; border: 2px solid #ef4444;">
                        <div style="font-size: 2em; margin-bottom: 10px;">🍳</div>
                        <div><strong>煎蛋</strong></div>
                        <div style="font-size: 0.9em; color: #666;">最后做</div>
                    </div>
                </div>
                <p style="text-align: center; font-weight: bold; color: #4a5568;">
                    这就是前趋关系：打鸡蛋 → 热锅 → 煎蛋
                </p>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎯 交互式前趋图</h2>

            <div class="step-indicator">
                <div class="step active" data-step="0">1</div>
                <div class="step" data-step="1">2</div>
                <div class="step" data-step="2">3</div>
                <div class="step" data-step="3">4</div>
                <div class="step" data-step="4">5</div>
            </div>

            <div class="canvas-container">
                <canvas id="graphCanvas" width="800" height="500"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="startAnimation()">🎬 开始动画演示</button>
                <button class="btn btn-secondary" onclick="showConnections()">🔗 显示所有连接</button>
                <button class="btn btn-success" onclick="resetGraph()">🔄 重置图形</button>
                <button class="btn" style="background: linear-gradient(135deg, #a78bfa, #c084fc); color: white;" onclick="showStepByStep()">📖 逐步解读</button>
            </div>

            <div id="currentExplanation" class="explanation">
                <p>点击"开始动画演示"来学习如何读取前趋图中的关系！</p>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🧠 零基础解题思路</h2>
            <div class="explanation">
                <h3>🎯 第一步：数一数有多少个圆圈</h3>
                <div style="background: #f0f9ff; padding: 15px; border-radius: 10px; margin: 10px 0;">
                    <p>• 仔细观察图形，数出所有的圆圈（节点）</p>
                    <p>• 每个圆圈代表一个进程，通常标记为P1、P2、P3...等</p>
                    <p>• <strong>本题中有：P1, P2, P3, P4, P5, P6, P7, P8 共8个进程</strong></p>
                </div>

                <h3>🔍 第二步：找出所有的箭头</h3>
                <div style="background: #fef3c7; padding: 15px; border-radius: 10px; margin: 10px 0;">
                    <p>• 仔细观察每条箭头的起点和终点</p>
                    <p>• 箭头从A指向B，就写成(A, B)</p>
                    <p>• <strong>技巧：</strong>按照从左到右、从上到下的顺序来找，不容易遗漏</p>
                </div>

                <h3>📝 第三步：按顺序写出所有关系</h3>
                <div style="background: #f0fff4; padding: 15px; border-radius: 10px; margin: 10px 0;">
                    <p><strong>从P1开始：</strong></p>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>P1 → P2，写成 (P1,P2)</li>
                        <li>P1 → P3，写成 (P1,P3)</li>
                        <li>P1 → P4，写成 (P1,P4)</li>
                    </ul>
                    <p><strong>然后P2：</strong></p>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        <li>P2 → P3，写成 (P2,P3)</li>
                        <li>P2 → P5，写成 (P2,P5)</li>
                    </ul>
                    <p><strong>继续这样找完所有箭头...</strong></p>
                </div>

                <h3>✅ 第四步：检查答案</h3>
                <div style="background: #fef2f2; padding: 15px; border-radius: 10px; margin: 10px 0;">
                    <p>• 数一数你找到的关系总数</p>
                    <p>• 对比选项中的关系数量</p>
                    <p>• <strong>本题正确答案应该有12条关系</strong></p>
                </div>

                <h3>🎨 记忆技巧</h3>
                <div style="background: #f3e8ff; padding: 15px; border-radius: 10px; margin: 10px 0;">
                    <p><strong>口诀：</strong>"箭头指向谁，谁就要等待"</p>
                    <p><strong>理解：</strong>如果有箭头从A指向B，那么B必须等A完成后才能开始</p>
                    <p><strong>类比：</strong>就像排队买票，前面的人买完了，后面的人才能买</p>
                </div>
            </div>
        </div>

        <div class="content-section answer-section">
            <h2 class="section-title">📝 练习题目</h2>
            <p><strong>题目：</strong>根据上面的前趋图，该前趋图可记为（ ）</p>
            
            <div class="options">
                <div class="option" onclick="selectOption(this, false)">
                    <strong>A.</strong> →={(P1,P2),(P1,P3),(P1,P4),(P2,P5),(P3,P5),(P4,P7),(P5,P6),(P5,P7),(P7,P6),(P4,P5),(P6,P7),(P7,P8)}
                </div>
                <div class="option" onclick="selectOption(this, true)">
                    <strong>B.</strong> →={(P1,P2),(P1,P3),(P1,P4),(P2,P3),(P2,P5),(P3,P4),(P3,P6),(P4,P7),(P5,P6),(P5,P8),(P6,P7),(P7,P8)}
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>C.</strong> →={(P1,P2),(P1,P3),(P1,P4),(P2,P3),(P2,P5),(P3,P4),(P3,P5),(P4,P6),(P5,P7),(P5,P8),(P6,P7),(P7,P8)}
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>D.</strong> →={(P1,P2),(P1,P3),(P2,P3),(P2,P5),(P3,P4),(P3,P6),(P4,P7),(P5,P6),(P5,P8),(P6,P7),(P6,P8),(P7,P8)}
                </div>
            </div>
            
            <div id="answerFeedback" style="margin-top: 20px; padding: 15px; border-radius: 10px; display: none;"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('graphCanvas');
        const ctx = canvas.getContext('2d');
        
        // 进程节点位置
        const nodes = {
            P1: {x: 100, y: 250, color: '#667eea'},
            P2: {x: 250, y: 150, color: '#f093fb'},
            P3: {x: 250, y: 250, color: '#4facfe'},
            P4: {x: 250, y: 350, color: '#48bb78'},
            P5: {x: 400, y: 150, color: '#f6ad55'},
            P6: {x: 550, y: 250, color: '#ed64a6'},
            P7: {x: 550, y: 350, color: '#9f7aea'},
            P8: {x: 700, y: 250, color: '#38b2ac'}
        };

        // 连接关系（正确答案B）
        const connections = [
            ['P1', 'P2'], ['P1', 'P3'], ['P1', 'P4'],
            ['P2', 'P3'], ['P2', 'P5'],
            ['P3', 'P4'], ['P3', 'P6'],
            ['P4', 'P7'],
            ['P5', 'P6'], ['P5', 'P8'],
            ['P6', 'P7'],
            ['P7', 'P8']
        ];

        let currentStep = 0;
        let animationIndex = 0;
        let isAnimating = false;

        function drawNode(name, node, highlight = false) {
            ctx.save();
            
            if (highlight) {
                ctx.shadowColor = node.color;
                ctx.shadowBlur = 20;
            }
            
            // 绘制节点圆圈
            ctx.beginPath();
            ctx.arc(node.x, node.y, 30, 0, 2 * Math.PI);
            ctx.fillStyle = highlight ? node.color : '#ffffff';
            ctx.fill();
            ctx.strokeStyle = node.color;
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 绘制节点文字
            ctx.fillStyle = highlight ? '#ffffff' : node.color;
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(name, node.x, node.y);
            
            ctx.restore();
        }

        function drawArrow(from, to, highlight = false, animated = false) {
            const fromNode = nodes[from];
            const toNode = nodes[to];
            
            // 计算箭头起点和终点（在圆圈边缘）
            const dx = toNode.x - fromNode.x;
            const dy = toNode.y - fromNode.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const unitX = dx / distance;
            const unitY = dy / distance;
            
            const startX = fromNode.x + unitX * 30;
            const startY = fromNode.y + unitY * 30;
            const endX = toNode.x - unitX * 30;
            const endY = toNode.y - unitY * 30;
            
            ctx.save();
            
            if (highlight) {
                ctx.strokeStyle = '#f56565';
                ctx.lineWidth = 4;
                ctx.shadowColor = '#f56565';
                ctx.shadowBlur = 10;
            } else {
                ctx.strokeStyle = '#4a5568';
                ctx.lineWidth = 2;
            }
            
            // 绘制箭头线
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
            
            // 绘制箭头头部
            const arrowLength = 15;
            const arrowAngle = Math.PI / 6;
            const angle = Math.atan2(dy, dx);
            
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(
                endX - arrowLength * Math.cos(angle - arrowAngle),
                endY - arrowLength * Math.sin(angle - arrowAngle)
            );
            ctx.moveTo(endX, endY);
            ctx.lineTo(
                endX - arrowLength * Math.cos(angle + arrowAngle),
                endY - arrowLength * Math.sin(angle + arrowAngle)
            );
            ctx.stroke();
            
            ctx.restore();
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawGraph(highlightConnections = [], highlightNodes = []) {
            clearCanvas();
            
            // 绘制所有连接
            connections.forEach(([from, to]) => {
                const isHighlighted = highlightConnections.some(([hFrom, hTo]) => 
                    hFrom === from && hTo === to
                );
                drawArrow(from, to, isHighlighted);
            });
            
            // 绘制所有节点
            Object.entries(nodes).forEach(([name, node]) => {
                const isHighlighted = highlightNodes.includes(name);
                drawNode(name, node, isHighlighted);
            });
        }

        function updateExplanation(text) {
            document.getElementById('currentExplanation').innerHTML = `<p>${text}</p>`;
        }

        function updateStep(step) {
            document.querySelectorAll('.step').forEach((el, index) => {
                el.classList.remove('active', 'completed');
                if (index < step) {
                    el.classList.add('completed');
                } else if (index === step) {
                    el.classList.add('active');
                }
            });
        }

        async function startAnimation() {
            if (isAnimating) return;
            isAnimating = true;

            const explanations = [
                "🎯 步骤1：首先观察图中的所有进程节点。我们有P1到P8共8个进程，就像8个不同的任务。",
                "🚀 步骤2：识别从P1出发的所有箭头。P1指向P2、P3、P4，表示P1完成后，P2、P3、P4才能开始。",
                "🔍 步骤3：继续识别其他节点的连接关系。每条箭头都有特定的含义！",
                "📊 步骤4：逐个添加所有连接，观察整个执行流程的形成过程。",
                "✅ 步骤5：完整的前趋关系已标出。总共有12条边，表示12个前趋关系。"
            ];

            // 步骤1：高亮所有节点
            updateStep(0);
            updateExplanation(explanations[0]);
            drawGraph([], Object.keys(nodes));
            await sleep(3000);

            // 步骤2：高亮P1的连接
            updateStep(1);
            updateExplanation(explanations[1]);
            const p1Connections = connections.filter(([from]) => from === 'P1');
            drawGraph(p1Connections, ['P1', 'P2', 'P3', 'P4']);
            await sleep(3000);

            // 步骤3：显示P2的连接
            updateStep(2);
            updateExplanation("🔗 现在看P2：P2完成后，P3和P5才能开始执行。");
            const p1p2Connections = connections.filter(([from]) => from === 'P1' || from === 'P2');
            drawGraph(p1p2Connections, ['P2', 'P3', 'P5']);
            await sleep(2500);

            // 步骤4：逐个显示其他连接
            updateStep(3);
            updateExplanation(explanations[3]);
            for (let i = 0; i < connections.length; i++) {
                const currentConnections = connections.slice(0, i + 1);
                const [from, to] = connections[i];
                drawGraph(currentConnections, [from, to]);

                // 添加连接说明
                const connectionText = `添加连接：${from} → ${to} (${from}必须在${to}之前完成)`;
                updateExplanation(`${explanations[3]}<br><strong style="color: #f56565;">${connectionText}</strong>`);
                await sleep(800);
            }

            // 步骤5：显示完整图形
            updateStep(4);
            updateExplanation(explanations[4]);
            drawGraph(connections, []);

            isAnimating = false;
        }

        function showConnections() {
            drawGraph(connections, []);
            updateExplanation("完整的前趋图显示了所有12条边的关系。每条边(Pi,Pj)表示Pi必须在Pj之前完成。");
        }

        function resetGraph() {
            drawGraph();
            updateStep(0);
            updateExplanation("点击\"开始动画演示\"来学习如何读取前趋图中的关系！");
        }

        function selectOption(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            const feedback = document.getElementById('answerFeedback');

            if (isCorrect) {
                element.classList.add('correct');
                feedback.style.display = 'block';
                feedback.style.background = '#f0fff4';
                feedback.style.border = '2px solid #48bb78';
                feedback.innerHTML =
                    '<h3 style="color: #38a169;">🎉 恭喜你答对了！</h3>' +
                    '<p><strong>正确答案是B。</strong></p>' +
                    '<p><strong>解析：</strong>通过仔细观察前趋图，我们可以识别出以下12条边：</p>' +
                    '<ul style="margin: 10px 0; padding-left: 20px;">' +
                        '<li>(P1,P2), (P1,P3), (P1,P4) - P1指向的三个节点</li>' +
                        '<li>(P2,P3), (P2,P5) - P2指向的节点</li>' +
                        '<li>(P3,P4), (P3,P6) - P3指向的节点</li>' +
                        '<li>(P4,P7) - P4指向P7</li>' +
                        '<li>(P5,P6), (P5,P8) - P5指向的节点</li>' +
                        '<li>(P6,P7) - P6指向P7</li>' +
                        '<li>(P7,P8) - P7指向P8</li>' +
                    '</ul>';
            } else {
                element.classList.add('wrong');
                feedback.style.display = 'block';
                feedback.style.background = '#fed7d7';
                feedback.style.border = '2px solid #f56565';
                feedback.innerHTML =
                    '<h3 style="color: #c53030;">❌ 答案不正确</h3>' +
                    '<p>请仔细观察前趋图中的每条边，确保没有遗漏或多加任何连接关系。</p>' +
                    '<p><strong>提示：</strong>正确答案应该有12条边，按照从左到右、从上到下的顺序来识别。</p>';
            }
        }

        async function showStepByStep() {
            if (isAnimating) return;
            isAnimating = true;

            const stepExplanations = [
                {
                    title: "🎯 第一步：理解节点",
                    content: "每个圆圈代表一个进程（Process）。进程就是计算机正在执行的程序。",
                    highlight: Object.keys(nodes),
                    connections: []
                },
                {
                    title: "🔗 第二步：理解箭头",
                    content: "箭头表示执行顺序。从P1指向P2的箭头意思是：P1必须完成后，P2才能开始。",
                    highlight: ['P1', 'P2'],
                    connections: [['P1', 'P2']]
                },
                {
                    title: "🌟 第三步：并行执行",
                    content: "P1完成后，P2、P3、P4可以同时开始执行（并行），因为它们之间没有直接的依赖关系。",
                    highlight: ['P1', 'P2', 'P3', 'P4'],
                    connections: [['P1', 'P2'], ['P1', 'P3'], ['P1', 'P4']]
                },
                {
                    title: "🔄 第四步：复杂依赖",
                    content: "P2完成后影响P3和P5，P3完成后影响P4和P6。这形成了复杂的依赖网络。",
                    highlight: ['P2', 'P3', 'P4', 'P5', 'P6'],
                    connections: [['P1', 'P2'], ['P1', 'P3'], ['P1', 'P4'], ['P2', 'P3'], ['P2', 'P5'], ['P3', 'P4'], ['P3', 'P6']]
                },
                {
                    title: "✅ 第五步：完整图形",
                    content: "最终形成完整的前趋图，包含12条边，表示所有进程之间的执行顺序关系。",
                    highlight: [],
                    connections: connections
                }
            ];

            for (let i = 0; i < stepExplanations.length; i++) {
                const step = stepExplanations[i];
                updateStep(i);
                updateExplanation(`<h3>${step.title}</h3><p>${step.content}</p>`);
                drawGraph(step.connections, step.highlight);
                await sleep(4000);
            }

            isAnimating = false;
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 初始化
        drawGraph();

        // 添加点击步骤指示器的功能
        document.querySelectorAll('.step').forEach((step, index) => {
            step.addEventListener('click', () => {
                if (!isAnimating) {
                    updateStep(index);
                }
            });
        });

        // 添加节点点击交互
        canvas.addEventListener('click', (event) => {
            if (isAnimating) return;

            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // 检查点击的是哪个节点
            for (const [name, node] of Object.entries(nodes)) {
                const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
                if (distance <= 30) {
                    // 高亮该节点的所有连接
                    const relatedConnections = connections.filter(([from, to]) =>
                        from === name || to === name
                    );
                    const relatedNodes = [...new Set(relatedConnections.flat())];

                    drawGraph(relatedConnections, relatedNodes);

                    const incomingCount = connections.filter(([from, to]) => to === name).length;
                    const outgoingCount = connections.filter(([from, to]) => from === name).length;

                    updateExplanation(
                        `<h3>🎯 节点 ${name} 的详细信息</h3>` +
                        `<p><strong>前驱进程：</strong>${incomingCount}个（必须在${name}之前完成）</p>` +
                        `<p><strong>后继进程：</strong>${outgoingCount}个（${name}完成后才能开始）</p>` +
                        `<p><strong>相关连接：</strong>${relatedConnections.map(([f, t]) => `${f}→${t}`).join(', ')}</p>`
                    );
                    break;
                }
            }
        });
    </script>
</body>
</html>
