<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件开发方法互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .game-board {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            text-align: center;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        #gameCanvas {
            border: 3px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .option-card:hover::before {
            left: 100%;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .option-card.correct {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-color: #2e7d32;
            animation: correctPulse 0.6s ease-in-out;
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            border-color: #c62828;
            animation: wrongShake 0.6s ease-in-out;
        }

        .explanation-panel {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation-panel.show {
            opacity: 1;
            transform: translateY(0);
        }

        .method-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .method-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #667eea;
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .method-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .method-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .highlight {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .score-display {
            text-align: center;
            margin: 20px 0;
            font-size: 1.5rem;
            color: #333;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1>🚀 软件开发方法学习之旅</h1>
            <p>通过有趣的互动游戏，轻松掌握各种软件开发方法的特点和应用场景</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-title">
                    📝 题目：哪种开发方法适用于程序开发人员在地域上分布很广的开发团队？
                </div>
                
                <div class="score-display">
                    得分: <span id="score">0</span> / 100
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>

                <div class="canvas-container">
                    <canvas id="gameCanvas" width="800" height="400"></canvas>
                </div>

                <div class="options-grid">
                    <div class="option-card" data-option="A">
                        <h3>A. 水晶系列（Crystal）开发方法</h3>
                        <p>以人为中心，最少纪律约束的敏捷方法</p>
                    </div>
                    <div class="option-card" data-option="B">
                        <h3>B. 开放式源码（Open Source）开发方法</h3>
                        <p>程序开发人员地域分布广泛的协作方式</p>
                    </div>
                    <div class="option-card" data-option="C">
                        <h3>C. SCRUM开发方法</h3>
                        <p>强调明确定义的可重复方法过程</p>
                    </div>
                    <div class="option-card" data-option="D">
                        <h3>D. 功用驱动开发方法（FDD）</h3>
                        <p>首席程序员和"类"程序员分工合作</p>
                    </div>
                </div>

                <div class="explanation-panel" id="explanationPanel">
                    <h3>🎯 正确答案：B - 开放式源码（Open Source）开发方法</h3>
                    <p><strong>关键特征：</strong></p>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li>程序开发人员在<span class="highlight">地域上分布很广</span></li>
                        <li>与其他敏捷方法不同（一般要求同地点工作）</li>
                        <li>高度并行的查错排障机制</li>
                        <li>任何人都可以提交"补丁"文件</li>
                        <li>维护者负责将补丁并入源码库</li>
                    </ul>
                </div>
            </div>

            <div class="method-cards">
                <div class="method-card">
                    <h3>🔮 水晶系列（Crystal）</h3>
                    <p><strong>特点：</strong>以人为中心，最少纪律约束</p>
                    <p><strong>理念：</strong>在产出效率与易于运作间达到平衡</p>
                    <p><strong>适用：</strong>希望更多人能接受并遵循的项目</p>
                </div>
                
                <div class="method-card">
                    <h3>🌐 开放式源码（Open Source）</h3>
                    <p><strong>特点：</strong>开发人员地域分布广泛</p>
                    <p><strong>机制：</strong>高度并行的查错排障</p>
                    <p><strong>流程：</strong>补丁提交→维护者审核→并入源码库</p>
                </div>
                
                <div class="method-card">
                    <h3>🏃 SCRUM开发方法</h3>
                    <p><strong>强调：</strong>明确定义的可重复方法过程</p>
                    <p><strong>环境：</strong>明确定义的可重复环境</p>
                    <p><strong>人员：</strong>明确定义的可重复人员</p>
                </div>
                
                <div class="method-card">
                    <h3>⚡ 功用驱动开发（FDD）</h3>
                    <p><strong>迭代：</strong>短时迭代（一般两周）</p>
                    <p><strong>角色：</strong>首席程序员（协调、设计、指导）</p>
                    <p><strong>分工：</strong>"类"程序员（源码编写）</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            score: 0,
            answered: false,
            correctAnswer: 'B'
        };

        // Canvas 动画
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 开发方法可视化数据
        const methods = [
            { name: 'Crystal', x: 150, y: 200, color: '#9C27B0', size: 60, distributed: false },
            { name: 'Open Source', x: 300, y: 200, color: '#4CAF50', size: 80, distributed: true },
            { name: 'SCRUM', x: 500, y: 200, color: '#FF9800', size: 65, distributed: false },
            { name: 'FDD', x: 650, y: 200, color: '#2196F3', size: 70, distributed: false }
        ];

        // 开发者节点（用于演示分布式特性）
        let developers = [];
        
        function initDevelopers() {
            developers = [];
            for (let i = 0; i < 12; i++) {
                developers.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    connected: false,
                    alpha: 0.7
                });
            }
        }

        function drawMethod(method) {
            // 绘制方法圆圈
            ctx.beginPath();
            ctx.arc(method.x, method.y, method.size, 0, Math.PI * 2);
            ctx.fillStyle = method.color;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 3;
            ctx.stroke();
            
            // 绘制方法名称
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(method.name, method.x, method.y + 5);
            
            // 如果是分布式方法，绘制连接线
            if (method.distributed) {
                developers.forEach(dev => {
                    if (dev.connected) {
                        ctx.beginPath();
                        ctx.moveTo(method.x, method.y);
                        ctx.lineTo(dev.x, dev.y);
                        ctx.strokeStyle = `rgba(76, 175, 80, ${dev.alpha})`;
                        ctx.lineWidth = 2;
                        ctx.stroke();
                    }
                });
            }
        }

        function drawDeveloper(dev) {
            ctx.beginPath();
            ctx.arc(dev.x, dev.y, 8, 0, Math.PI * 2);
            ctx.fillStyle = dev.connected ? '#4CAF50' : '#666';
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制开发者图标
            ctx.fillStyle = '#fff';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('👨‍💻', dev.x, dev.y + 3);
        }

        function updateDevelopers() {
            developers.forEach(dev => {
                dev.x += dev.vx;
                dev.y += dev.vy;
                
                // 边界反弹
                if (dev.x <= 8 || dev.x >= canvas.width - 8) dev.vx *= -1;
                if (dev.y <= 8 || dev.y >= canvas.height - 8) dev.vy *= -1;
                
                // 检查是否连接到Open Source方法
                const openSource = methods.find(m => m.name === 'Open Source');
                const distance = Math.sqrt((dev.x - openSource.x) ** 2 + (dev.y - openSource.y) ** 2);
                dev.connected = distance < 150;
                
                // 动态透明度
                dev.alpha = 0.3 + 0.4 * Math.sin(Date.now() * 0.003 + dev.x * 0.01);
            });
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景网格
            ctx.strokeStyle = 'rgba(255,255,255,0.1)';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }
            
            updateDevelopers();
            
            // 绘制开发者
            developers.forEach(drawDeveloper);
            
            // 绘制方法
            methods.forEach(drawMethod);
            
            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('软件开发方法可视化演示', canvas.width / 2, 30);
            
            ctx.font = '14px Arial';
            ctx.fillText('绿色连线表示分布式协作特性', canvas.width / 2, 50);
            
            requestAnimationFrame(animate);
        }

        // 选项点击处理
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('click', function() {
                if (gameState.answered) return;
                
                const selectedOption = this.dataset.option;
                gameState.answered = true;
                
                // 重置所有卡片样式
                document.querySelectorAll('.option-card').forEach(c => {
                    c.classList.remove('correct', 'wrong');
                });
                
                if (selectedOption === gameState.correctAnswer) {
                    this.classList.add('correct');
                    gameState.score = 100;
                    updateScore();
                    showExplanation();
                    createSuccessParticles();
                } else {
                    this.classList.add('wrong');
                    // 显示正确答案
                    document.querySelector(`[data-option="${gameState.correctAnswer}"]`).classList.add('correct');
                    showExplanation();
                }
                
                // 3秒后允许重新答题
                setTimeout(() => {
                    gameState.answered = false;
                    document.querySelectorAll('.option-card').forEach(c => {
                        c.classList.remove('correct', 'wrong');
                    });
                    hideExplanation();
                }, 5000);
            });
        });

        function updateScore() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('progressFill').style.width = gameState.score + '%';
        }

        function showExplanation() {
            document.getElementById('explanationPanel').classList.add('show');
        }

        function hideExplanation() {
            document.getElementById('explanationPanel').classList.remove('show');
        }

        function createSuccessParticles() {
            for (let i = 0; i < 20; i++) {
                setTimeout(() => {
                    const particle = document.createElement('div');
                    particle.style.position = 'fixed';
                    particle.style.left = Math.random() * window.innerWidth + 'px';
                    particle.style.top = window.innerHeight + 'px';
                    particle.style.width = '10px';
                    particle.style.height = '10px';
                    particle.style.background = '#4CAF50';
                    particle.style.borderRadius = '50%';
                    particle.style.pointerEvents = 'none';
                    particle.style.zIndex = '1000';
                    particle.style.animation = 'float 3s linear forwards';
                    document.body.appendChild(particle);
                    
                    setTimeout(() => particle.remove(), 3000);
                }, i * 100);
            }
        }

        // 创建背景粒子
        function createBackgroundParticles() {
            const particlesContainer = document.getElementById('particles');
            
            setInterval(() => {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.width = particle.style.height = Math.random() * 10 + 5 + 'px';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
                
                setTimeout(() => particle.remove(), 6000);
            }, 300);
        }

        // 初始化
        initDevelopers();
        animate();
        createBackgroundParticles();
        updateScore();
    </script>
</body>
</html>
