<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DHCP协议互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .question-box h2 {
            font-size: 1.8rem;
            margin-bottom: 20px;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .option {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .option.correct {
            border-color: #00d4aa;
            background: rgba(0, 212, 170, 0.3);
        }

        .option.wrong {
            border-color: #ff4757;
            background: rgba(255, 71, 87, 0.3);
        }

        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        canvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            line-height: 1.6;
        }

        .step {
            background: rgba(116, 185, 255, 0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            transition: all 0.3s ease;
        }

        .step:hover {
            background: rgba(116, 185, 255, 0.2);
            transform: translateX(5px);
        }

        .highlight {
            background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
            padding: 3px 8px;
            border-radius: 5px;
            color: #2d3436;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #01a3a4);
            width: 0%;
            transition: width 0.5s ease;
        }

        .step-detail {
            margin-top: 10px;
            padding: 10px;
            background: rgba(116, 185, 255, 0.1);
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .analysis-step {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .analysis-step:hover {
            background: rgba(116, 185, 255, 0.15);
            transform: translateX(10px);
        }

        .analysis-step ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .analysis-step li {
            margin: 5px 0;
            list-style: none;
            position: relative;
        }

        .analysis-step li:before {
            content: '';
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            width: 6px;
            height: 6px;
            background: #74b9ff;
            border-radius: 50%;
        }

        .step.pulse {
            animation: pulse 1s ease-in-out 3;
            background: rgba(116, 185, 255, 0.3);
            border-left-color: #0984e3;
        }

        #practiceCanvas {
            width: 100%;
            height: 300px;
            border-radius: 10px;
        }

        .interactive-hint {
            text-align: center;
            color: #74b9ff;
            font-style: italic;
            margin: 10px 0;
            animation: fadeInUp 1s ease-out 2s both;
        }

        .key-point {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            box-shadow: 0 5px 15px rgba(232, 67, 147, 0.3);
        }

        .key-point h4 {
            margin-bottom: 8px;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 DHCP协议互动学习</h1>
            <p>让我们用动画来理解网络中的IP地址分配魔法</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2>📝 考试题目</h2>
                <p><strong>以下关于DHCP服务的说法中，正确的是（）。</strong></p>
                <div class="options">
                    <div class="option" data-answer="A">
                        <strong>A.</strong> 在一个园区网中可以存在多台DHCP服务器
                    </div>
                    <div class="option" data-answer="B">
                        <strong>B.</strong> 默认情况下，客户端要使用DHCP服务需指定DHCP服务器地址
                    </div>
                    <div class="option" data-answer="C">
                        <strong>C.</strong> 默认情况下，DHCP客户端选择本网段内的IP地址作为本地地址
                    </div>
                    <div class="option" data-answer="D">
                        <strong>D.</strong> 在DHCP服务器上，DHCP服务功能默认开启
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 什么是DHCP？</h2>
            <div class="explanation">
                <p><span class="highlight">DHCP</span>（Dynamic Host Configuration Protocol）是<strong>动态主机配置协议</strong>，就像网络世界的"房屋中介"，专门负责给新来的设备分配"网络地址"。</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="dhcpCanvas"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startDHCPDemo()">🎬 开始DHCP演示</button>
                <button class="btn" onclick="resetDemo()">🔄 重置演示</button>
            </div>
        </div>

        <div class="section">
            <h2>🔍 DHCP工作原理 - 四个步骤</h2>
            <div class="step" onclick="highlightStep(1)">
                <h3>1️⃣ 发现阶段（DISCOVER）</h3>
                <p>客户端大喊："有没有DHCP服务器？我需要IP地址！"（广播消息到67端口）</p>
                <div class="step-detail">
                    <p>💡 <strong>关键点：</strong>客户端不知道服务器在哪里，所以用<span class="highlight">广播</span>的方式寻找</p>
                </div>
            </div>
            <div class="step" onclick="highlightStep(2)">
                <h3>2️⃣ 提供阶段（OFFER）</h3>
                <p>DHCP服务器回应："我这里有IP地址，给你这个：*************"</p>
                <div class="step-detail">
                    <p>💡 <strong>关键点：</strong>如果有多个服务器，客户端只接受<span class="highlight">第一个</span>收到的OFFER</p>
                </div>
            </div>
            <div class="step" onclick="highlightStep(3)">
                <h3>3️⃣ 请求阶段（REQUEST）</h3>
                <p>客户端说："好的，我要这个IP地址！"</p>
                <div class="step-detail">
                    <p>💡 <strong>关键点：</strong>客户端向服务器的<span class="highlight">68端口</span>发送确认请求</p>
                </div>
            </div>
            <div class="step" onclick="highlightStep(4)">
                <h3>4️⃣ 确认阶段（ACK）</h3>
                <p>服务器确认："好的，这个IP地址现在是你的了！"</p>
                <div class="step-detail">
                    <p>💡 <strong>关键点：</strong>服务器最终确认，客户端获得完整的网络配置信息</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🤔 解题思路分析</h2>
            <div class="explanation">
                <h3>让我们逐个分析每个选项：</h3>
            </div>

            <div class="step analysis-step" data-option="A">
                <h3>选项A：在一个园区网中可以存在多台DHCP服务器 ✅</h3>
                <p><strong>正确！</strong>就像一个大商场可以有多个信息台一样，园区网可以有多台DHCP服务器：</p>
                <ul>
                    <li>🏢 不同楼层/区域可以有不同的DHCP服务器</li>
                    <li>🔄 可以设置主备服务器，提供冗余保护</li>
                    <li>📊 可以负责不同的IP地址段</li>
                </ul>
            </div>

            <div class="step analysis-step" data-option="B">
                <h3>选项B：默认情况下，客户端要使用DHCP服务需指定DHCP服务器地址 ❌</h3>
                <p><strong>错误！</strong>这就是DHCP的魅力所在：</p>
                <ul>
                    <li>📢 客户端使用<span class="highlight">广播</span>寻找服务器</li>
                    <li>🔍 不需要事先知道服务器地址</li>
                    <li>🎯 自动发现网络中的DHCP服务器</li>
                </ul>
            </div>

            <div class="step analysis-step" data-option="C">
                <h3>选项C：默认情况下，DHCP客户端选择本网段内的IP地址作为本地地址 ❌</h3>
                <p><strong>错误！</strong>客户端不会自己选择IP地址：</p>
                <ul>
                    <li>🎲 IP地址是由<span class="highlight">DHCP服务器分配</span>的</li>
                    <li>📋 客户端只是接受服务器提供的地址</li>
                    <li>🚫 客户端没有权限自己决定使用哪个IP</li>
                </ul>
            </div>

            <div class="step analysis-step" data-option="D">
                <h3>选项D：在DHCP服务器上，DHCP服务功能默认开启 ❌</h3>
                <p><strong>错误！</strong>安全考虑，服务器功能通常默认关闭：</p>
                <ul>
                    <li>🔒 需要管理员<span class="highlight">手动启用</span>DHCP服务</li>
                    <li>⚙️ 需要配置IP地址池等参数</li>
                    <li>🛡️ 避免意外的IP地址冲突</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎮 互动练习</h2>
            <div class="canvas-container">
                <canvas id="practiceCanvas"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="startMultiServerDemo()">🏢 多服务器演示</button>
                <button class="btn" onclick="startBroadcastDemo()">📢 广播寻找演示</button>
                <button class="btn" onclick="showPortDemo()">🔌 端口通信演示</button>
            </div>
            <div class="interactive-hint">
                💡 点击上面的按钮体验不同的DHCP场景演示
            </div>
        </div>

        <div class="section">
            <h2>🎯 记忆要点总结</h2>

            <div class="key-point">
                <h4>🏆 正确答案：A</h4>
                <p>在一个园区网中<strong>可以存在多台DHCP服务器</strong>，这是网络设计的常见做法。</p>
            </div>

            <div class="explanation">
                <h3>🧠 记忆口诀</h3>
                <p style="font-size: 1.2rem; text-align: center; font-weight: bold;">
                    "多台服务器可共存，广播寻找不指定，<br>
                    服务器分配IP地址，功能需要手动启"
                </p>
            </div>

            <div class="step">
                <h3>📚 核心知识点</h3>
                <ul>
                    <li><span class="highlight">DHCP协议</span>：动态主机配置协议，自动分配IP地址</li>
                    <li><span class="highlight">四个阶段</span>：发现→提供→请求→确认</li>
                    <li><span class="highlight">通信端口</span>：客户端68端口，服务器67端口</li>
                    <li><span class="highlight">传输协议</span>：使用UDP协议进行通信</li>
                    <li><span class="highlight">寻找方式</span>：客户端使用广播寻找服务器</li>
                </ul>
            </div>

            <div class="step">
                <h3>⚡ 考试技巧</h3>
                <ul>
                    <li>看到"多台服务器"相关题目，通常是<span class="highlight">正确</span>的</li>
                    <li>看到"需要指定服务器地址"，通常是<span class="highlight">错误</span>的</li>
                    <li>看到"客户端自己选择IP"，通常是<span class="highlight">错误</span>的</li>
                    <li>看到"默认开启"相关功能，通常是<span class="highlight">错误</span>的</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎉 恭喜完成学习！</h2>
            <div class="explanation">
                <p>现在你已经掌握了DHCP协议的核心概念！记住：</p>
                <p style="text-align: center; font-size: 1.1rem; margin: 20px 0;">
                    <strong>DHCP就像网络世界的"自动分房系统"，让设备轻松获得网络身份证！</strong>
                </p>
            </div>

            <div class="controls">
                <button class="btn" onclick="reviewQuestion()">🔄 重新答题</button>
                <button class="btn" onclick="showSummary()">📋 显示总结</button>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('dhcpCanvas');
        const ctx = canvas.getContext('2d');
        let animationStep = 0;
        let animationId;

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width * window.devicePixelRatio;
            canvas.height = rect.height * window.devicePixelRatio;
            ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        }

        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 绘制网络设备
        function drawDevice(x, y, type, label, color = '#3498db') {
            ctx.save();
            
            // 设备主体
            ctx.fillStyle = color;
            ctx.fillRect(x - 30, y - 20, 60, 40);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 30, y - 20, 60, 40);
            
            // 设备图标
            if (type === 'server') {
                // 服务器图标
                ctx.fillStyle = '#fff';
                for (let i = 0; i < 3; i++) {
                    ctx.fillRect(x - 25, y - 15 + i * 10, 50, 6);
                }
            } else {
                // 客户端图标
                ctx.fillStyle = '#fff';
                ctx.fillRect(x - 20, y - 10, 40, 20);
                ctx.fillRect(x - 15, y + 10, 30, 8);
            }
            
            // 标签
            ctx.fillStyle = '#2c3e50';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, x, y + 50);
            
            ctx.restore();
        }

        // 绘制消息
        function drawMessage(fromX, fromY, toX, toY, message, progress, color = '#e74c3c') {
            const currentX = fromX + (toX - fromX) * progress;
            const currentY = fromY + (toY - fromY) * progress;
            
            // 消息包
            ctx.save();
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(currentX, currentY, 8, 0, Math.PI * 2);
            ctx.fill();
            
            // 消息文本
            ctx.fillStyle = '#2c3e50';
            ctx.font = '10px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(message, currentX, currentY - 15);
            
            ctx.restore();
        }

        // DHCP演示动画
        function animateDHCP() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const clientX = 150;
            const clientY = 200;
            const serverX = 450;
            const serverY = 200;
            
            // 绘制设备
            drawDevice(clientX, clientY, 'client', 'DHCP客户端', '#3498db');
            drawDevice(serverX, serverY, 'server', 'DHCP服务器', '#e67e22');
            
            // 绘制网络连接线
            ctx.strokeStyle = '#bdc3c7';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(clientX + 30, clientY);
            ctx.lineTo(serverX - 30, serverY);
            ctx.stroke();
            
            const step = Math.floor(animationStep / 60); // 每60帧一个步骤
            const progress = (animationStep % 60) / 60;
            
            switch (step) {
                case 0:
                    drawMessage(clientX, clientY, serverX, serverY, 'DISCOVER', progress, '#e74c3c');
                    break;
                case 1:
                    drawMessage(serverX, serverY, clientX, clientY, 'OFFER', progress, '#27ae60');
                    break;
                case 2:
                    drawMessage(clientX, clientY, serverX, serverY, 'REQUEST', progress, '#f39c12');
                    break;
                case 3:
                    drawMessage(serverX, serverY, clientX, clientY, 'ACK', progress, '#9b59b6');
                    break;
                default:
                    // 显示完成状态
                    ctx.fillStyle = '#27ae60';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('✅ IP地址分配完成！', canvas.width / 2 / window.devicePixelRatio, 100);
                    return;
            }
            
            animationStep++;
            animationId = requestAnimationFrame(animateDHCP);
        }

        function startDHCPDemo() {
            animationStep = 0;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animateDHCP();
        }

        function resetDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            animationStep = 0;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制初始状态
            const clientX = 150;
            const clientY = 200;
            const serverX = 450;
            const serverY = 200;
            
            drawDevice(clientX, clientY, 'client', 'DHCP客户端', '#3498db');
            drawDevice(serverX, serverY, 'server', 'DHCP服务器', '#e67e22');
            
            ctx.strokeStyle = '#bdc3c7';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(clientX + 30, clientY);
            ctx.lineTo(serverX - 30, serverY);
            ctx.stroke();
        }

        // 题目交互
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const progressFill = document.getElementById('progressFill');
                
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                if (answer === 'A') {
                    this.classList.add('correct');
                    progressFill.style.width = '100%';
                    setTimeout(() => {
                        alert('🎉 恭喜答对了！\n\n解释：在一个园区网中确实可以存在多台DHCP服务器，它们可以负责不同的网段，或者作为备份服务器提供冗余。');
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    progressFill.style.width = '25%';
                    setTimeout(() => {
                        alert('❌ 答案不正确，请重新思考。\n\n提示：想想网络中是否可以有多个"房屋中介"同时工作？');
                    }, 500);
                }
            });
        });

        // 高亮步骤功能
        function highlightStep(stepNumber) {
            // 移除所有高亮
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('pulse');
            });

            // 高亮当前步骤
            const steps = document.querySelectorAll('.step');
            if (steps[stepNumber - 1]) {
                steps[stepNumber - 1].classList.add('pulse');

                // 3秒后移除高亮
                setTimeout(() => {
                    steps[stepNumber - 1].classList.remove('pulse');
                }, 3000);
            }
        }

        // 多服务器演示
        function startMultiServerDemo() {
            const practiceCanvas = document.getElementById('practiceCanvas');
            const practiceCtx = practiceCanvas.getContext('2d');

            // 设置canvas尺寸
            const rect = practiceCanvas.getBoundingClientRect();
            practiceCanvas.width = rect.width * window.devicePixelRatio;
            practiceCanvas.height = rect.height * window.devicePixelRatio;
            practiceCtx.scale(window.devicePixelRatio, window.devicePixelRatio);

            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);

            // 绘制多个服务器和客户端
            const clientX = 100;
            const clientY = 200;
            const server1X = 300;
            const server1Y = 150;
            const server2X = 500;
            const server2Y = 250;

            drawDeviceOnCanvas(practiceCtx, clientX, clientY, 'client', '客户端', '#3498db');
            drawDeviceOnCanvas(practiceCtx, server1X, server1Y, 'server', 'DHCP服务器1\n(192.168.1.x)', '#e67e22');
            drawDeviceOnCanvas(practiceCtx, server2X, server2Y, 'server', 'DHCP服务器2\n(192.168.2.x)', '#27ae60');

            // 绘制网络连接
            practiceCtx.strokeStyle = '#bdc3c7';
            practiceCtx.lineWidth = 2;
            practiceCtx.beginPath();
            practiceCtx.moveTo(clientX + 30, clientY);
            practiceCtx.lineTo(server1X - 30, server1Y);
            practiceCtx.moveTo(clientX + 30, clientY);
            practiceCtx.lineTo(server2X - 30, server2Y);
            practiceCtx.stroke();

            // 添加说明文字
            practiceCtx.fillStyle = '#2c3e50';
            practiceCtx.font = '14px Microsoft YaHei';
            practiceCtx.textAlign = 'center';
            practiceCtx.fillText('✅ 多台DHCP服务器可以同时存在', practiceCanvas.width / 2 / window.devicePixelRatio, 50);
            practiceCtx.fillText('每台服务器负责不同的网段或作为备份', practiceCanvas.width / 2 / window.devicePixelRatio, 70);
        }

        // 广播寻找演示
        function startBroadcastDemo() {
            const practiceCanvas = document.getElementById('practiceCanvas');
            const practiceCtx = practiceCanvas.getContext('2d');

            const rect = practiceCanvas.getBoundingClientRect();
            practiceCanvas.width = rect.width * window.devicePixelRatio;
            practiceCanvas.height = rect.height * window.devicePixelRatio;
            practiceCtx.scale(window.devicePixelRatio, window.devicePixelRatio);

            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);

            const clientX = 150;
            const clientY = 200;

            drawDeviceOnCanvas(practiceCtx, clientX, clientY, 'client', '客户端', '#3498db');

            // 绘制广播波纹效果
            let radius = 0;
            const maxRadius = 200;

            function animateBroadcast() {
                practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);
                drawDeviceOnCanvas(practiceCtx, clientX, clientY, 'client', '客户端', '#3498db');

                // 绘制广播圆圈
                practiceCtx.strokeStyle = '#e74c3c';
                practiceCtx.lineWidth = 3;
                practiceCtx.globalAlpha = 1 - (radius / maxRadius);
                practiceCtx.beginPath();
                practiceCtx.arc(clientX, clientY, radius, 0, Math.PI * 2);
                practiceCtx.stroke();

                // 添加广播文字
                practiceCtx.globalAlpha = 1;
                practiceCtx.fillStyle = '#e74c3c';
                practiceCtx.font = '12px Microsoft YaHei';
                practiceCtx.textAlign = 'center';
                practiceCtx.fillText('DISCOVER广播', clientX, clientY - radius - 10);

                practiceCtx.fillStyle = '#2c3e50';
                practiceCtx.font = '14px Microsoft YaHei';
                practiceCtx.fillText('❌ 客户端不需要指定服务器地址', practiceCanvas.width / 2 / window.devicePixelRatio, 50);
                practiceCtx.fillText('📢 使用广播自动寻找DHCP服务器', practiceCanvas.width / 2 / window.devicePixelRatio, 70);

                radius += 3;
                if (radius <= maxRadius) {
                    requestAnimationFrame(animateBroadcast);
                }
            }

            animateBroadcast();
        }

        // 端口通信演示
        function showPortDemo() {
            const practiceCanvas = document.getElementById('practiceCanvas');
            const practiceCtx = practiceCanvas.getContext('2d');

            const rect = practiceCanvas.getBoundingClientRect();
            practiceCanvas.width = rect.width * window.devicePixelRatio;
            practiceCanvas.height = rect.height * window.devicePixelRatio;
            practiceCtx.scale(window.devicePixelRatio, window.devicePixelRatio);

            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);

            const clientX = 150;
            const clientY = 200;
            const serverX = 450;
            const serverY = 200;

            drawDeviceOnCanvas(practiceCtx, clientX, clientY, 'client', '客户端\n端口68', '#3498db');
            drawDeviceOnCanvas(practiceCtx, serverX, serverY, 'server', 'DHCP服务器\n端口67', '#e67e22');

            // 绘制端口连接线
            practiceCtx.strokeStyle = '#9b59b6';
            practiceCtx.lineWidth = 4;
            practiceCtx.beginPath();
            practiceCtx.moveTo(clientX + 30, clientY);
            practiceCtx.lineTo(serverX - 30, serverY);
            practiceCtx.stroke();

            // 添加端口说明
            practiceCtx.fillStyle = '#9b59b6';
            practiceCtx.font = '12px Microsoft YaHei';
            practiceCtx.textAlign = 'center';
            practiceCtx.fillText('UDP协议', (clientX + serverX) / 2, clientY - 20);

            practiceCtx.fillStyle = '#2c3e50';
            practiceCtx.font = '14px Microsoft YaHei';
            practiceCtx.fillText('🔌 DHCP使用UDP协议进行通信', practiceCanvas.width / 2 / window.devicePixelRatio, 50);
            practiceCtx.fillText('客户端68端口 ↔ 服务器67端口', practiceCanvas.width / 2 / window.devicePixelRatio, 70);
        }

        // 在不同canvas上绘制设备的辅助函数
        function drawDeviceOnCanvas(ctx, x, y, type, label, color = '#3498db') {
            ctx.save();

            // 设备主体
            ctx.fillStyle = color;
            ctx.fillRect(x - 30, y - 20, 60, 40);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 30, y - 20, 60, 40);

            // 设备图标
            if (type === 'server') {
                ctx.fillStyle = '#fff';
                for (let i = 0; i < 3; i++) {
                    ctx.fillRect(x - 25, y - 15 + i * 10, 50, 6);
                }
            } else {
                ctx.fillStyle = '#fff';
                ctx.fillRect(x - 20, y - 10, 40, 20);
                ctx.fillRect(x - 15, y + 10, 30, 8);
            }

            // 标签（支持多行）
            ctx.fillStyle = '#2c3e50';
            ctx.font = '10px Microsoft YaHei';
            ctx.textAlign = 'center';
            const lines = label.split('\n');
            lines.forEach((line, index) => {
                ctx.fillText(line, x, y + 50 + index * 12);
            });

            ctx.restore();
        }

        // 重新答题功能
        function reviewQuestion() {
            // 清除所有选项的状态
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 重置进度条
            document.getElementById('progressFill').style.width = '0%';

            // 滚动到题目位置
            document.querySelector('.question-box').scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 添加提示动画
            setTimeout(() => {
                document.querySelector('.question-box').classList.add('pulse');
                setTimeout(() => {
                    document.querySelector('.question-box').classList.remove('pulse');
                }, 2000);
            }, 500);
        }

        // 显示总结功能
        function showSummary() {
            const summary = `
🎯 DHCP学习总结

✅ 正确答案：A - 在一个园区网中可以存在多台DHCP服务器

📚 核心概念：
• DHCP = Dynamic Host Configuration Protocol（动态主机配置协议）
• 作用：自动为网络设备分配IP地址和网络配置

🔄 工作流程（四步骤）：
1. DISCOVER - 客户端广播寻找服务器
2. OFFER - 服务器提供IP地址
3. REQUEST - 客户端请求使用该地址
4. ACK - 服务器确认分配

🔌 技术细节：
• 协议：UDP
• 端口：客户端68，服务器67
• 寻找方式：广播（不需指定服务器地址）

💡 记忆要点：
• 多台服务器可共存 ✅
• 广播寻找不指定 ✅
• 服务器分配IP地址 ✅
• 功能需要手动启 ✅

🎉 恭喜掌握DHCP协议！
            `;

            alert(summary);
        }

        // 添加页面加载完成后的欢迎动画
        window.addEventListener('load', function() {
            setTimeout(() => {
                const welcome = document.createElement('div');
                welcome.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 30px;
                    border-radius: 20px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                    z-index: 1000;
                    animation: fadeInUp 0.5s ease-out;
                `;
                welcome.innerHTML = `
                    <h3>🌟 欢迎来到DHCP学习世界！</h3>
                    <p>让我们一起探索网络协议的奥秘</p>
                    <button onclick="this.parentElement.remove()" style="
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        padding: 10px 20px;
                        border-radius: 15px;
                        margin-top: 15px;
                        cursor: pointer;
                    ">开始学习 🚀</button>
                `;
                document.body.appendChild(welcome);
            }, 1000);
        });

        // 初始化画布
        resetDemo();
    </script>
</body>
</html>
