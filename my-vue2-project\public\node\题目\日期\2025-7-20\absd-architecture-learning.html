<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ABSD软件架构设计 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .learning-section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        canvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .interactive-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f7fafc;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            border-left: 4px solid #667eea;
            animation: slideInLeft 0.8s ease-out;
        }

        .explanation h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .quiz-container {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px;
            padding: 40px;
            color: white;
            text-align: center;
        }

        .quiz-question {
            font-size: 1.4rem;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .quiz-option {
            padding: 15px 20px;
            background: rgba(255,255,255,0.2);
            border: 2px solid transparent;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: rgba(72, 187, 120, 0.8);
            border-color: #48bb78;
        }

        .quiz-option.wrong {
            background: rgba(245, 101, 101, 0.8);
            border-color: #f56565;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .knowledge-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .knowledge-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .knowledge-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .card-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h3 {
            margin: 0;
            font-size: 1.2rem;
        }

        .toggle-icon {
            font-size: 1.5rem;
            font-weight: bold;
            transition: transform 0.3s ease;
        }

        .knowledge-card.expanded .toggle-icon {
            transform: rotate(45deg);
        }

        .card-content {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .knowledge-card.expanded .card-content {
            padding: 20px;
            max-height: 300px;
        }

        .card-content p {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .card-content ul {
            list-style: none;
            padding: 0;
        }

        .card-content li {
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }

        .card-content li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #667eea;
            font-size: 0.8rem;
        }

        .special-card {
            border: 3px solid #f093fb;
            position: relative;
            overflow: visible;
        }

        .special-card::before {
            content: '⭐ 重点';
            position: absolute;
            top: -15px;
            right: 20px;
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
        }

        .principles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .principle-item {
            padding: 15px;
            border-radius: 12px;
            border: 2px solid;
            transition: all 0.3s ease;
        }

        .principle-item.correct {
            background: rgba(72, 187, 120, 0.1);
            border-color: #48bb78;
        }

        .principle-item.wrong {
            background: rgba(245, 101, 101, 0.1);
            border-color: #f56565;
            animation: shake 0.5s ease-in-out;
        }

        .principle-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .principle-icon {
            font-size: 1.2rem;
            margin-right: 8px;
        }

        .principle-item strong {
            display: block;
            margin-bottom: 8px;
            color: #2d3748;
        }

        .principle-item p {
            margin: 0;
            font-size: 0.9rem;
            color: #4a5568;
        }

        .output-results {
            margin-top: 25px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            color: white;
        }

        .output-results h4 {
            margin: 0 0 15px 0;
            text-align: center;
        }

        .output-items {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .output-item {
            background: rgba(255,255,255,0.2);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .explanation-popup {
            margin-top: 20px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .explanation-content {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 20px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 2px solid;
        }

        .explanation-content.correct {
            background: rgba(72, 187, 120, 0.2);
            border-color: #48bb78;
        }

        .explanation-content.wrong {
            background: rgba(245, 101, 101, 0.2);
            border-color: #f56565;
        }

        .explanation-icon {
            font-size: 2rem;
            flex-shrink: 0;
        }

        .explanation-text {
            flex: 1;
        }

        .explanation-text strong {
            display: block;
            margin-bottom: 8px;
            color: white;
            font-size: 1.1rem;
        }

        .explanation-text p {
            margin: 0;
            color: rgba(255,255,255,0.9);
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title floating">ABSD软件架构设计</h1>
            <p class="subtitle">Architecture-Based Software Design 交互式学习体验</p>
        </div>

        <!-- ABSD三大基础 -->
        <div class="learning-section">
            <h2 class="section-title">ABSD方法的三大基础</h2>
            <div class="canvas-container">
                <canvas id="foundationCanvas" width="800" height="400"></canvas>
            </div>
            <div class="interactive-buttons">
                <button class="btn" onclick="showFoundation(0)">功能分解</button>
                <button class="btn" onclick="showFoundation(1)">架构风格</button>
                <button class="btn" onclick="showFoundation(2)">软件模板</button>
                <button class="btn" onclick="animateAll()">完整演示</button>
            </div>
            <div class="explanation">
                <h3>ABSD方法基础知识</h3>
                <p><strong>功能分解：</strong>将复杂的系统按照功能模块进行分解，使系统结构清晰明了。</p>
                <p><strong>架构风格：</strong>采用特定的架构风格来实现质量属性与商业需求，如分层架构、微服务架构等。</p>
                <p><strong>软件模板：</strong>使用预定义的软件模板来设计软件结构，提高开发效率和质量。</p>
            </div>
        </div>

        <!-- ABSD六大活动 -->
        <div class="learning-section">
            <h2 class="section-title">ABSD方法的六大活动</h2>
            <div class="canvas-container">
                <canvas id="activitiesCanvas" width="800" height="500"></canvas>
            </div>
            <div class="interactive-buttons">
                <button class="btn" onclick="showActivity('requirements')">架构需求</button>
                <button class="btn" onclick="showActivity('design')">架构设计</button>
                <button class="btn" onclick="showActivity('review')">架构复审</button>
                <button class="btn" onclick="showActivity('implementation')">架构实现</button>
                <button class="btn" onclick="showActivity('evolution')">架构演化</button>
                <button class="btn" onclick="showActivity('documentation')">架构文档化</button>
            </div>
            <div class="explanation">
                <h3>重点活动解析</h3>
                <p><strong>架构复审：</strong>目标是标识潜在的风险，及早发现架构设计中的缺陷和错误。</p>
                <p><strong>架构演化：</strong>针对用户的需求变化，修改应用架构，满足新的需求。</p>
                <p><strong>架构文档化：</strong>主要输出架构规格说明书和架构质量说明书。</p>
            </div>
        </div>

        <!-- 知识点详解 -->
        <div class="learning-section">
            <h2 class="section-title">深度解析</h2>
            <div class="knowledge-cards">
                <div class="knowledge-card" onclick="toggleCard(this)">
                    <div class="card-header">
                        <h3>🏗️ 架构风格详解</h3>
                        <span class="toggle-icon">+</span>
                    </div>
                    <div class="card-content">
                        <p>架构风格是软件架构设计中的核心概念，它定义了系统组件的组织方式和交互模式。</p>
                        <ul>
                            <li><strong>分层架构：</strong>将系统分为多个层次，每层只与相邻层交互</li>
                            <li><strong>微服务架构：</strong>将应用拆分为多个独立的小服务</li>
                            <li><strong>事件驱动架构：</strong>通过事件进行组件间的通信</li>
                            <li><strong>管道过滤器：</strong>数据通过一系列处理步骤流动</li>
                        </ul>
                    </div>
                </div>

                <div class="knowledge-card" onclick="toggleCard(this)">
                    <div class="card-header">
                        <h3>🔍 架构复审要点</h3>
                        <span class="toggle-icon">+</span>
                    </div>
                    <div class="card-content">
                        <p>架构复审是确保架构质量的关键活动，主要关注以下方面：</p>
                        <ul>
                            <li><strong>风险识别：</strong>发现潜在的技术和业务风险</li>
                            <li><strong>质量评估：</strong>评估架构是否满足质量属性要求</li>
                            <li><strong>一致性检查：</strong>确保架构设计的一致性</li>
                            <li><strong>可行性分析：</strong>验证架构的技术可行性</li>
                        </ul>
                    </div>
                </div>

                <div class="knowledge-card" onclick="toggleCard(this)">
                    <div class="card-header">
                        <h3>📋 架构演化策略</h3>
                        <span class="toggle-icon">+</span>
                    </div>
                    <div class="card-content">
                        <p>架构演化是应对需求变化的重要机制：</p>
                        <ul>
                            <li><strong>增量演化：</strong>逐步添加新功能和组件</li>
                            <li><strong>重构演化：</strong>改进现有架构结构</li>
                            <li><strong>迁移演化：</strong>向新技术平台迁移</li>
                            <li><strong>版本管理：</strong>管理架构的不同版本</li>
                        </ul>
                    </div>
                </div>

                <div class="knowledge-card special-card" onclick="toggleCard(this)">
                    <div class="card-header">
                        <h3>📚 架构文档化原则</h3>
                        <span class="toggle-icon">+</span>
                    </div>
                    <div class="card-content">
                        <p>架构文档化是ABSD方法的重要环节，需要遵循以下原则：</p>
                        <div class="principles-grid">
                            <div class="principle-item correct">
                                <span class="principle-icon">✅</span>
                                <strong>从使用者角度书写</strong>
                                <p>文档应该站在读者的角度，便于理解和使用</p>
                            </div>
                            <div class="principle-item correct">
                                <span class="principle-icon">✅</span>
                                <strong>针对不同背景人员采用不同方式</strong>
                                <p>根据读者的技术背景调整文档内容和表达方式</p>
                            </div>
                            <div class="principle-item correct">
                                <span class="principle-icon">✅</span>
                                <strong>将文档分发给相关人员</strong>
                                <p>确保文档能够及时传达给需要的团队成员</p>
                            </div>
                            <div class="principle-item wrong">
                                <span class="principle-icon">❌</span>
                                <strong>随时保证文档最新</strong>
                                <p>错误做法！应保持较新但不要随时更新，要保持稳定性</p>
                            </div>
                        </div>
                        <div class="output-results">
                            <h4>主要输出结果：</h4>
                            <div class="output-items">
                                <span class="output-item">📋 架构规格说明书</span>
                                <span class="output-item">📊 架构质量说明书</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 互动测验 -->
        <div class="learning-section">
            <div class="quiz-container">
                <h2 class="section-title" style="color: white;">知识测验</h2>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="quizContent">
                    <div class="quiz-question" id="questionText">
                        ABSD方法的三个基础中，用于实现质量属性与商业需求的是？
                    </div>
                    <div class="quiz-options" id="optionsContainer">
                        <div class="quiz-option" onclick="selectAnswer(this, false)">A. 设计模式</div>
                        <div class="quiz-option" onclick="selectAnswer(this, true)">B. 架构风格</div>
                        <div class="quiz-option" onclick="selectAnswer(this, false)">C. 架构策略</div>
                        <div class="quiz-option" onclick="selectAnswer(this, false)">D. 架构描述</div>
                    </div>
                </div>
                <button class="btn" id="nextBtn" onclick="nextQuestion()" style="display: none;">下一题</button>
            </div>
        </div>
    </div>

    <script>
        // 画布和动画控制
        const foundationCanvas = document.getElementById('foundationCanvas');
        const foundationCtx = foundationCanvas.getContext('2d');
        const activitiesCanvas = document.getElementById('activitiesCanvas');
        const activitiesCtx = activitiesCanvas.getContext('2d');

        let animationFrame;
        let currentFoundation = -1;
        let currentActivity = '';

        // ABSD三大基础动画
        function drawFoundations() {
            foundationCtx.clearRect(0, 0, foundationCanvas.width, foundationCanvas.height);
            
            const foundations = [
                { name: '功能分解', x: 150, y: 200, color: '#4facfe', icon: '🔧' },
                { name: '架构风格', x: 400, y: 200, color: '#43e97b', icon: '🏗️' },
                { name: '软件模板', x: 650, y: 200, color: '#fa709a', icon: '📋' }
            ];

            foundations.forEach((foundation, index) => {
                const isActive = currentFoundation === index || currentFoundation === -1;
                const alpha = isActive ? 1 : 0.3;
                
                foundationCtx.save();
                foundationCtx.globalAlpha = alpha;
                
                // 绘制圆形背景
                foundationCtx.beginPath();
                foundationCtx.arc(foundation.x, foundation.y, 60, 0, Math.PI * 2);
                foundationCtx.fillStyle = foundation.color;
                foundationCtx.fill();
                foundationCtx.shadowColor = foundation.color;
                foundationCtx.shadowBlur = 20;
                foundationCtx.fill();
                
                // 绘制图标
                foundationCtx.font = '30px Arial';
                foundationCtx.textAlign = 'center';
                foundationCtx.fillStyle = 'white';
                foundationCtx.fillText(foundation.icon, foundation.x, foundation.y + 10);
                
                // 绘制标题
                foundationCtx.font = 'bold 16px Arial';
                foundationCtx.fillStyle = '#2d3748';
                foundationCtx.fillText(foundation.name, foundation.x, foundation.y + 90);
                
                foundationCtx.restore();
            });

            // 绘制连接线
            if (currentFoundation === -1) {
                foundationCtx.strokeStyle = '#cbd5e0';
                foundationCtx.lineWidth = 3;
                foundationCtx.setLineDash([5, 5]);
                foundationCtx.beginPath();
                foundationCtx.moveTo(210, 200);
                foundationCtx.lineTo(340, 200);
                foundationCtx.moveTo(460, 200);
                foundationCtx.lineTo(590, 200);
                foundationCtx.stroke();
                foundationCtx.setLineDash([]);
            }
        }

        function showFoundation(index) {
            currentFoundation = index;
            drawFoundations();
            
            // 添加脉冲效果
            setTimeout(() => {
                currentFoundation = -1;
                drawFoundations();
            }, 2000);
        }

        function animateAll() {
            let index = 0;
            const interval = setInterval(() => {
                showFoundation(index);
                index++;
                if (index >= 3) {
                    clearInterval(interval);
                    setTimeout(() => {
                        currentFoundation = -1;
                        drawFoundations();
                    }, 2000);
                }
            }, 1000);
        }

        // ABSD六大活动动画
        function drawActivities() {
            activitiesCtx.clearRect(0, 0, activitiesCanvas.width, activitiesCanvas.height);
            
            const activities = [
                { name: '架构需求', x: 150, y: 150, color: '#667eea', key: 'requirements' },
                { name: '架构设计', x: 400, y: 150, color: '#764ba2', key: 'design' },
                { name: '架构复审', x: 650, y: 150, color: '#f093fb', key: 'review' },
                { name: '架构实现', x: 150, y: 350, color: '#4facfe', key: 'implementation' },
                { name: '架构演化', x: 400, y: 350, color: '#43e97b', key: 'evolution' },
                { name: '架构文档化', x: 650, y: 350, color: '#fa709a', key: 'documentation' }
            ];

            activities.forEach((activity) => {
                const isActive = currentActivity === activity.key || currentActivity === '';
                const alpha = isActive ? 1 : 0.3;
                const scale = isActive && currentActivity === activity.key ? 1.2 : 1;
                
                activitiesCtx.save();
                activitiesCtx.globalAlpha = alpha;
                activitiesCtx.translate(activity.x, activity.y);
                activitiesCtx.scale(scale, scale);
                
                // 绘制六边形
                activitiesCtx.beginPath();
                for (let i = 0; i < 6; i++) {
                    const angle = (i * Math.PI) / 3;
                    const x = Math.cos(angle) * 50;
                    const y = Math.sin(angle) * 50;
                    if (i === 0) {
                        activitiesCtx.moveTo(x, y);
                    } else {
                        activitiesCtx.lineTo(x, y);
                    }
                }
                activitiesCtx.closePath();
                activitiesCtx.fillStyle = activity.color;
                activitiesCtx.fill();
                activitiesCtx.shadowColor = activity.color;
                activitiesCtx.shadowBlur = 15;
                activitiesCtx.fill();
                
                // 绘制文字
                activitiesCtx.font = 'bold 14px Arial';
                activitiesCtx.textAlign = 'center';
                activitiesCtx.fillStyle = 'white';
                activitiesCtx.fillText(activity.name, 0, 5);
                
                activitiesCtx.restore();
            });
        }

        function showActivity(key) {
            currentActivity = key;
            drawActivities();
            
            setTimeout(() => {
                currentActivity = '';
                drawActivities();
            }, 3000);
        }

        // 测验系统
        const questions = [
            {
                question: "ABSD方法的三个基础中，用于实现质量属性与商业需求的是？",
                options: ["A. 设计模式", "B. 架构风格", "C. 架构策略", "D. 架构描述"],
                correct: 1,
                explanation: "架构风格是ABSD方法三大基础之一，专门用于实现质量属性与商业需求。"
            },
            {
                question: "哪个活动的目标是标识潜在的风险，及早发现架构设计中的缺陷和错误？",
                options: ["A. 架构需求", "B. 架构设计", "C. 架构复审", "D. 架构实现"],
                correct: 2,
                explanation: "架构复审活动专门用于风险识别，及早发现架构设计中的问题。"
            },
            {
                question: "架构文档化的主要输出结果包括架构规格说明书和什么？",
                options: ["A. 架构设计书", "B. 架构质量说明书", "C. 架构需求书", "D. 架构测试书"],
                correct: 1,
                explanation: "架构文档化的两个主要输出是：架构规格说明书和架构质量说明书。"
            },
            {
                question: "小王作为架构师，以下哪种做法不符合架构文档化的原则？",
                options: [
                    "A. 从使用者的角度书写文档",
                    "B. 随时保证文档都是最新的",
                    "C. 将文档分发给相关人员",
                    "D. 针对不同背景的人员采用不同书写方式"
                ],
                correct: 1,
                explanation: "架构文档要保持较新，但不要随时保证文档最新，要保持文档的稳定性。频繁更新会影响文档的稳定性。"
            },
            {
                question: "ABSD方法中，哪个活动针对用户的需求变化，修改应用架构，满足新的需求？",
                options: ["A. 架构需求", "B. 架构复审", "C. 架构演化", "D. 架构文档化"],
                correct: 2,
                explanation: "架构演化活动专门处理需求变化，通过修改架构来适应新需求。"
            }
        ];

        let currentQuestionIndex = 0;
        let score = 0;

        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes(questions[currentQuestionIndex].options[questions[currentQuestionIndex].correct])) {
                    option.classList.add('correct');
                }
            });

            if (isCorrect) {
                score++;
                checkAchievements();
            }

            // 显示解释
            showExplanation(questions[currentQuestionIndex].explanation, isCorrect);

            document.getElementById('nextBtn').style.display = 'block';
            updateProgress();
        }

        function showExplanation(explanation, isCorrect) {
            const explanationDiv = document.createElement('div');
            explanationDiv.className = 'explanation-popup';
            explanationDiv.innerHTML = `
                <div class="explanation-content ${isCorrect ? 'correct' : 'wrong'}">
                    <div class="explanation-icon">${isCorrect ? '🎉' : '💡'}</div>
                    <div class="explanation-text">
                        <strong>${isCorrect ? '回答正确！' : '正确答案解析：'}</strong>
                        <p>${explanation}</p>
                    </div>
                </div>
            `;

            document.getElementById('quizContent').appendChild(explanationDiv);

            // 添加动画
            setTimeout(() => {
                explanationDiv.style.opacity = '1';
                explanationDiv.style.transform = 'translateY(0)';
            }, 100);
        }

        function nextQuestion() {
            currentQuestionIndex++;
            if (currentQuestionIndex < questions.length) {
                loadQuestion();
            } else {
                showResults();
            }
        }

        function loadQuestion() {
            const question = questions[currentQuestionIndex];
            document.getElementById('questionText').textContent = question.question;

            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';

            // 清除之前的解释
            const existingExplanation = document.querySelector('.explanation-popup');
            if (existingExplanation) {
                existingExplanation.remove();
            }

            question.options.forEach((option, index) => {
                const optionElement = document.createElement('div');
                optionElement.className = 'quiz-option';
                optionElement.textContent = option;
                optionElement.onclick = () => selectAnswer(optionElement, index === question.correct);
                optionsContainer.appendChild(optionElement);
            });

            document.getElementById('nextBtn').style.display = 'none';
        }

        function updateProgress() {
            const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function showResults() {
            const percentage = (score / questions.length) * 100;
            let resultMessage = '';
            let resultIcon = '';

            if (percentage === 100) {
                resultMessage = '完美！您完全掌握了ABSD架构设计的核心知识！';
                resultIcon = '🏆';
            } else if (percentage >= 80) {
                resultMessage = '很好！您对ABSD方法有很好的理解！';
                resultIcon = '🎉';
            } else if (percentage >= 60) {
                resultMessage = '不错！继续学习可以进一步提升！';
                resultIcon = '👍';
            } else {
                resultMessage = '需要加强学习，建议重新阅读知识点！';
                resultIcon = '📚';
            }

            document.getElementById('quizContent').innerHTML = `
                <div class="quiz-question">
                    ${resultIcon} 测验完成！<br>
                    您的得分：${score}/${questions.length} (${percentage.toFixed(0)}%)<br>
                    <span style="font-size: 1rem; margin-top: 10px; display: block;">${resultMessage}</span>
                </div>
                <div style="margin: 20px 0;">
                    <h4 style="color: white; margin-bottom: 15px;">知识要点回顾：</h4>
                    <div style="text-align: left; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 12px;">
                        <p>• ABSD三大基础：功能分解、<strong>架构风格</strong>、软件模板</p>
                        <p>• <strong>架构复审</strong>：识别风险，发现缺陷</p>
                        <p>• <strong>架构演化</strong>：应对需求变化</p>
                        <p>• 文档化原则：保持较新但<strong>不要随时最新</strong></p>
                        <p>• 输出结果：架构规格说明书 + <strong>架构质量说明书</strong></p>
                    </div>
                </div>
                <button class="btn" onclick="restartQuiz()">重新开始</button>
            `;
        }

        function restartQuiz() {
            currentQuestionIndex = 0;
            score = 0;
            loadQuestion();
            updateProgress();
        }

        // 知识卡片交互
        function toggleCard(card) {
            card.classList.toggle('expanded');

            // 添加展开动画效果
            if (card.classList.contains('expanded')) {
                card.style.background = 'linear-gradient(135deg, #f7fafc, #edf2f7)';
            } else {
                card.style.background = 'white';
            }
        }

        // 添加键盘导航支持
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft' && currentQuestionIndex > 0) {
                // 上一题功能（如果需要）
            } else if (e.key === 'ArrowRight') {
                if (document.getElementById('nextBtn').style.display !== 'none') {
                    nextQuestion();
                }
            } else if (e.key >= '1' && e.key <= '4') {
                // 数字键选择答案
                const optionIndex = parseInt(e.key) - 1;
                const options = document.querySelectorAll('.quiz-option');
                if (options[optionIndex] && options[optionIndex].style.pointerEvents !== 'none') {
                    const isCorrect = optionIndex === questions[currentQuestionIndex].correct;
                    selectAnswer(options[optionIndex], isCorrect);
                }
            }
        });

        // 添加成就系统
        let achievements = {
            firstCorrect: false,
            perfectScore: false,
            architectExpert: false,
            documentationMaster: false
        };

        function checkAchievements() {
            if (score === 1 && !achievements.firstCorrect) {
                achievements.firstCorrect = true;
                showAchievement('🎯 首次答对！', '恭喜您答对了第一题！');
            }

            if (score === 3 && !achievements.architectExpert) {
                achievements.architectExpert = true;
                showAchievement('🏗️ 架构专家！', '您已掌握ABSD核心概念！');
            }

            if (currentQuestionIndex === 3 && score >= currentQuestionIndex && !achievements.documentationMaster) {
                achievements.documentationMaster = true;
                showAchievement('📚 文档化大师！', '您深刻理解架构文档化原则！');
            }

            if (score === questions.length && !achievements.perfectScore) {
                achievements.perfectScore = true;
                showAchievement('🏆 完美得分！', '您完全掌握了ABSD方法！');
            }
        }

        function showAchievement(title, message) {
            const achievement = document.createElement('div');
            achievement.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #48bb78, #38a169);
                color: white;
                padding: 15px 20px;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
                z-index: 1000;
                animation: slideInRight 0.5s ease-out;
            `;
            achievement.innerHTML = `<strong>${title}</strong><br>${message}`;
            document.body.appendChild(achievement);

            setTimeout(() => {
                achievement.style.animation = 'slideOutRight 0.5s ease-in';
                setTimeout(() => {
                    document.body.removeChild(achievement);
                }, 500);
            }, 3000);
        }

        // 添加成就动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 添加架构文档化原则演示
        function showDocumentationPrinciples() {
            const principles = [
                "从使用者角度书写",
                "针对不同背景人员采用不同方式",
                "保持文档较新但不随时最新",
                "保持文档稳定性"
            ];

            let index = 0;
            const interval = setInterval(() => {
                if (index < principles.length) {
                    showPrincipleAnimation(principles[index], index);
                    index++;
                } else {
                    clearInterval(interval);
                }
            }, 1500);
        }

        function showPrincipleAnimation(principle, index) {
            // 在活动画布上显示文档化原则
            activitiesCtx.save();
            activitiesCtx.fillStyle = `rgba(102, 126, 234, ${0.8 - index * 0.1})`;
            activitiesCtx.font = 'bold 16px Arial';
            activitiesCtx.textAlign = 'center';
            activitiesCtx.fillText(principle, 400, 50 + index * 30);
            activitiesCtx.restore();
        }

        // 添加鼠标悬停效果
        foundationCanvas.addEventListener('mousemove', (e) => {
            const rect = foundationCanvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检测是否悬停在基础元素上
            const foundations = [
                { x: 150, y: 200 },
                { x: 400, y: 200 },
                { x: 650, y: 200 }
            ];

            let hovering = false;
            foundations.forEach((foundation, index) => {
                const distance = Math.sqrt((x - foundation.x) ** 2 + (y - foundation.y) ** 2);
                if (distance < 60) {
                    foundationCanvas.style.cursor = 'pointer';
                    hovering = true;
                    showFoundation(index);
                }
            });

            if (!hovering) {
                foundationCanvas.style.cursor = 'default';
            }
        });

        activitiesCanvas.addEventListener('mousemove', (e) => {
            const rect = activitiesCanvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const activities = [
                { x: 150, y: 150, key: 'requirements' },
                { x: 400, y: 150, key: 'design' },
                { x: 650, y: 150, key: 'review' },
                { x: 150, y: 350, key: 'implementation' },
                { x: 400, y: 350, key: 'evolution' },
                { x: 650, y: 350, key: 'documentation' }
            ];

            let hovering = false;
            activities.forEach((activity) => {
                const distance = Math.sqrt((x - activity.x) ** 2 + (y - activity.y) ** 2);
                if (distance < 50) {
                    activitiesCanvas.style.cursor = 'pointer';
                    hovering = true;
                    showActivity(activity.key);
                }
            });

            if (!hovering) {
                activitiesCanvas.style.cursor = 'default';
            }
        });

        // 添加点击事件
        activitiesCanvas.addEventListener('click', (e) => {
            const rect = activitiesCanvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检测点击文档化活动
            const docActivity = { x: 650, y: 350 };
            const distance = Math.sqrt((x - docActivity.x) ** 2 + (y - docActivity.y) ** 2);
            if (distance < 50) {
                showDocumentationPrinciples();
            }
        });

        // 初始化
        drawFoundations();
        drawActivities();
        updateProgress();

        // 添加页面加载动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                animateAll();
            }, 1000);
        });
    </script>
</body>
</html>
