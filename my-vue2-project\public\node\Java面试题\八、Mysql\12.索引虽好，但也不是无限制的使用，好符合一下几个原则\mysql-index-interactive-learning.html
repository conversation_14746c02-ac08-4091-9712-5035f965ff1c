<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL索引学习 - 交互式教程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .principle-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transform: translateY(50px);
            opacity: 0;
            transition: all 0.6s ease;
        }

        .principle-card.visible {
            transform: translateY(0);
            opacity: 1;
        }

        .principle-title {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .principle-number {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            animation: pulse 2s infinite;
        }

        .principle-content {
            color: #34495e;
            line-height: 1.8;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .game-score {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
            font-weight: bold;
            color: #667eea;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MySQL索引学习之旅</h1>
            <p>通过动画和游戏，轻松掌握数据库索引的8大原则</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 原则1：左前缀匹配 -->
        <div class="principle-card" data-principle="1">
            <div class="principle-title">
                <div class="principle-number">1</div>
                左前缀匹配原则
            </div>
            <div class="principle-content">
                组合索引非常重要的原则，MySQL会一直向右匹配直到遇到范围查询(>、<、between、like)就停止匹配。
                比如a=1 and b=2 and c>3 and d=4，如果建立(a,b,c,d)顺序的索引，d是用不到索引的，
                如果建立(a,b,d,c)的索引则都可以用到，a,b,d的顺序可以任意调整。
            </div>
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="canvas1" width="800" height="300"></canvas>
                </div>
                <button class="interactive-btn" onclick="startPrefixDemo()">开始演示左前缀匹配</button>
                <button class="interactive-btn" onclick="playPrefixGame()">玩匹配游戏</button>
                <div class="game-score" id="score1">点击按钮开始学习！</div>
            </div>
        </div>

        <!-- 原则2：查询频率 -->
        <div class="principle-card" data-principle="2">
            <div class="principle-title">
                <div class="principle-number">2</div>
                查询频率原则
            </div>
            <div class="principle-content">
                较频繁作为查询条件的字段才去创建索引。就像图书馆的目录，只有经常被查找的书籍才会被放在显眼的位置。
            </div>
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="canvas2" width="800" height="300"></canvas>
                </div>
                <button class="interactive-btn" onclick="startFrequencyDemo()">演示查询频率</button>
                <div class="game-score" id="score2">观察哪些字段被频繁查询</div>
            </div>
        </div>

        <!-- 原则3：更新频率 -->
        <div class="principle-card" data-principle="3">
            <div class="principle-title">
                <div class="principle-number">3</div>
                更新频率原则
            </div>
            <div class="principle-content">
                更新频繁的字段不适合创建索引。因为每次更新数据时，索引也需要重新维护，会影响性能。
            </div>
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="canvas3" width="800" height="300"></canvas>
                </div>
                <button class="interactive-btn" onclick="startUpdateDemo()">演示更新影响</button>
                <button class="interactive-btn" onclick="playUpdateGame()">体验更新游戏</button>
                <div class="game-score" id="score3">看看频繁更新对索引的影响</div>
            </div>
        </div>

        <!-- 原则4：区分度原则 -->
        <div class="principle-card" data-principle="4">
            <div class="principle-title">
                <div class="principle-number">4</div>
                数据区分度原则
            </div>
            <div class="principle-content">
                不能有效区分数据的列不适合做索引列（如性别：男、女、未知，最多三种值，区分度实在太低）。
            </div>
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="canvas4" width="800" height="300"></canvas>
                </div>
                <button class="interactive-btn" onclick="startSelectivityDemo()">演示数据区分度</button>
                <div class="game-score" id="score4">比较不同字段的区分度</div>
            </div>
        </div>

        <!-- 原则5：扩展索引 -->
        <div class="principle-card" data-principle="5">
            <div class="principle-title">
                <div class="principle-number">5</div>
                扩展索引原则
            </div>
            <div class="principle-content">
                尽量扩展索引，不要新建索引。比如表中已经有a的索引，现在要加(a,b)的索引，那么只需要修改原来的索引即可。
            </div>
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="canvas5" width="800" height="300"></canvas>
                </div>
                <button class="interactive-btn" onclick="startExtendDemo()">演示索引扩展</button>
                <div class="game-score" id="score5">学习如何优化索引结构</div>
            </div>
        </div>

        <!-- 原则6：外键索引 -->
        <div class="principle-card" data-principle="6">
            <div class="principle-title">
                <div class="principle-number">6</div>
                外键索引原则
            </div>
            <div class="principle-content">
                定义有外键的数据列一定要建立索引。外键关联查询非常频繁，没有索引会严重影响性能。
            </div>
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="canvas6" width="800" height="300"></canvas>
                </div>
                <button class="interactive-btn" onclick="startForeignKeyDemo()">演示外键查询</button>
                <div class="game-score" id="score6">理解外键索引的重要性</div>
            </div>
        </div>

        <!-- 原则7：避免冗余索引 -->
        <div class="principle-card" data-principle="7">
            <div class="principle-title">
                <div class="principle-number">7</div>
                避免冗余索引原则
            </div>
            <div class="principle-content">
                对于查询中很少涉及的列，重复值比较多的列不要建立索引。这些索引不仅占用空间，还会拖慢写操作。
            </div>
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="canvas7" width="800" height="300"></canvas>
                </div>
                <button class="interactive-btn" onclick="startRedundantDemo()">演示冗余索引</button>
                <div class="game-score" id="score7">识别不必要的索引</div>
            </div>
        </div>

        <!-- 原则8：数据类型限制 -->
        <div class="principle-card" data-principle="8">
            <div class="principle-title">
                <div class="principle-number">8</div>
                数据类型限制原则
            </div>
            <div class="principle-content">
                对于定义为text、image和bit的数据类型的列不要建立索引。这些大对象类型不适合作为索引键。
            </div>
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="canvas8" width="800" height="300"></canvas>
                </div>
                <button class="interactive-btn" onclick="startDataTypeDemo()">演示数据类型影响</button>
                <button class="interactive-btn" onclick="startFinalQuiz()">最终测试</button>
                <div class="game-score" id="score8">完成所有学习内容</div>
            </div>
        </div>

        <!-- 学习总结 -->
        <div class="principle-card" data-principle="9">
            <div class="principle-title">
                <div class="principle-number">🎓</div>
                学习总结
            </div>
            <div class="principle-content">
                恭喜您完成了MySQL索引的8大原则学习！现在您已经掌握了索引设计的核心知识。
            </div>
            <div class="demo-area">
                <div class="canvas-container">
                    <canvas id="canvasSummary" width="800" height="400"></canvas>
                </div>
                <button class="interactive-btn" onclick="showSummary()">显示学习成果</button>
                <div class="game-score" id="finalScore">总得分：0 分</div>
            </div>
        </div>
    </div>

    <script>
        let currentPrinciple = 0;
        let gameScore = 0;
        let animationId;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            observeCards();
            updateProgress();
        });

        // 观察卡片进入视口
        function observeCards() {
            const cards = document.querySelectorAll('.principle-card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        const principleNum = parseInt(entry.target.dataset.principle);
                        if (principleNum > currentPrinciple) {
                            currentPrinciple = principleNum;
                            updateProgress();
                        }
                    }
                });
            }, { threshold: 0.3 });

            cards.forEach(card => observer.observe(card));
        }

        // 更新进度条
        function updateProgress() {
            const progress = (currentPrinciple / 8) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 左前缀匹配演示
        function startPrefixDemo() {
            const canvas = document.getElementById('canvas1');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制索引结构
            const indexColumns = ['a', 'b', 'c', 'd'];
            const queryConditions = ['a=1', 'b=2', 'c>3', 'd=4'];
            
            let step = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制索引列
                indexColumns.forEach((col, i) => {
                    const x = 100 + i * 150;
                    const y = 100;
                    
                    // 绘制列框
                    ctx.fillStyle = step > i ? '#667eea' : '#e0e0e0';
                    ctx.fillRect(x, y, 120, 60);
                    
                    // 绘制列名
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 20px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(col, x + 60, y + 40);
                    
                    // 绘制查询条件
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '16px Arial';
                    ctx.fillText(queryConditions[i], x + 60, y + 80);
                    
                    // 绘制箭头
                    if (i < indexColumns.length - 1 && step > i) {
                        drawArrow(ctx, x + 120, y + 30, x + 150, y + 30);
                    }
                    
                    // 范围查询停止标记
                    if (i === 2 && step > 2) {
                        ctx.fillStyle = '#e74c3c';
                        ctx.font = 'bold 16px Arial';
                        ctx.fillText('停止匹配!', x + 60, y + 100);
                    }
                });
                
                step++;
                if (step <= 4) {
                    setTimeout(animate, 1000);
                } else {
                    document.getElementById('score1').textContent = '演示完成！范围查询会停止索引匹配。';
                }
            }
            
            animate();
        }

        // 绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        // 左前缀匹配游戏
        function playPrefixGame() {
            const canvas = document.getElementById('canvas1');
            const ctx = canvas.getContext('2d');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 游戏说明
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击正确的索引顺序！', canvas.width/2, 50);
            
            const options = [
                { text: '(a,b,c,d)', correct: false, x: 150, y: 150 },
                { text: '(a,b,d,c)', correct: true, x: 450, y: 150 }
            ];
            
            options.forEach(option => {
                ctx.fillStyle = '#667eea';
                ctx.fillRect(option.x, option.y, 200, 80);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 18px Arial';
                ctx.fillText(option.text, option.x + 100, option.y + 50);
            });
            
            canvas.onclick = function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                options.forEach(option => {
                    if (x >= option.x && x <= option.x + 200 && 
                        y >= option.y && y <= option.y + 80) {
                        if (option.correct) {
                            gameScore += 10;
                            document.getElementById('score1').textContent = `正确！得分：${gameScore}`;
                            ctx.fillStyle = '#27ae60';
                            ctx.fillRect(option.x, option.y, 200, 80);
                            ctx.fillStyle = 'white';
                            ctx.fillText('正确！', option.x + 100, option.y + 50);
                        } else {
                            document.getElementById('score1').textContent = '错误！范围查询会停止匹配，所以d用不到索引。';
                            ctx.fillStyle = '#e74c3c';
                            ctx.fillRect(option.x, option.y, 200, 80);
                            ctx.fillStyle = 'white';
                            ctx.fillText('错误！', option.x + 100, option.y + 50);
                        }
                    }
                });
            };
        }

        // 查询频率演示
        function startFrequencyDemo() {
            const canvas = document.getElementById('canvas2');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const fields = [
                { name: 'user_id', frequency: 90, x: 100, y: 100 },
                { name: 'email', frequency: 75, x: 300, y: 100 },
                { name: 'gender', frequency: 20, x: 500, y: 100 },
                { name: 'hobby', frequency: 15, x: 700, y: 100 }
            ];

            let animationStep = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('字段查询频率统计', canvas.width/2, 30);

                fields.forEach((field, i) => {
                    if (animationStep > i * 10) {
                        // 绘制字段名
                        ctx.fillStyle = '#34495e';
                        ctx.font = '16px Arial';
                        ctx.fillText(field.name, field.x, field.y - 10);

                        // 绘制频率条
                        const barHeight = Math.min((animationStep - i * 10) * 2, field.frequency);
                        const color = field.frequency > 50 ? '#27ae60' : '#e74c3c';

                        ctx.fillStyle = color;
                        ctx.fillRect(field.x - 30, field.y + 10, 60, barHeight);

                        // 绘制百分比
                        ctx.fillStyle = '#2c3e50';
                        ctx.font = 'bold 14px Arial';
                        ctx.fillText(field.frequency + '%', field.x, field.y + barHeight + 30);

                        // 建议
                        if (animationStep > 100) {
                            const suggestion = field.frequency > 50 ? '建议建索引' : '不建议建索引';
                            const suggestionColor = field.frequency > 50 ? '#27ae60' : '#e74c3c';
                            ctx.fillStyle = suggestionColor;
                            ctx.font = '12px Arial';
                            ctx.fillText(suggestion, field.x, field.y + barHeight + 50);
                        }
                    }
                });

                animationStep += 2;
                if (animationStep <= 120) {
                    requestAnimationFrame(animate);
                } else {
                    document.getElementById('score2').textContent = '演示完成！频繁查询的字段适合建立索引。';
                }
            }

            animate();
        }

        // 更新频率演示
        function startUpdateDemo() {
            const canvas = document.getElementById('canvas3');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let updateCount = 0;
            let indexMaintenance = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('频繁更新对索引的影响', canvas.width/2, 30);

                // 绘制数据表
                ctx.fillStyle = '#3498db';
                ctx.fillRect(100, 80, 200, 150);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.fillText('数据表', 200, 110);
                ctx.fillText('更新次数: ' + updateCount, 200, 140);

                // 绘制索引
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(500, 80, 200, 150);
                ctx.fillStyle = 'white';
                ctx.fillText('索引', 600, 110);
                ctx.fillText('维护成本: ' + indexMaintenance, 600, 140);

                // 绘制更新箭头
                if (updateCount < 10) {
                    drawArrow(ctx, 350, 155, 450, 155);
                    ctx.fillStyle = '#f39c12';
                    ctx.font = '14px Arial';
                    ctx.fillText('每次更新都需要', 375, 180);
                    ctx.fillText('维护索引', 385, 200);
                }

                updateCount++;
                indexMaintenance += 2;

                if (updateCount <= 10) {
                    setTimeout(animate, 800);
                } else {
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 18px Arial';
                    ctx.fillText('频繁更新会严重影响性能！', canvas.width/2, 270);
                    document.getElementById('score3').textContent = '演示完成！更新频繁的字段不适合建索引。';
                }
            }

            animate();
        }

        // 更新游戏
        function playUpdateGame() {
            const canvas = document.getElementById('canvas3');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('选择适合建索引的字段', canvas.width/2, 50);

            const fields = [
                { name: 'user_id (很少更新)', correct: true, x: 100, y: 120 },
                { name: 'login_count (频繁更新)', correct: false, x: 450, y: 120 },
                { name: 'last_login (每次登录更新)', correct: false, x: 100, y: 220 },
                { name: 'email (偶尔更新)', correct: true, x: 450, y: 220 }
            ];

            fields.forEach(field => {
                ctx.fillStyle = '#667eea';
                ctx.fillRect(field.x, field.y, 250, 60);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText(field.name, field.x + 125, field.y + 40);
            });

            canvas.onclick = function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                fields.forEach(field => {
                    if (x >= field.x && x <= field.x + 250 &&
                        y >= field.y && y <= field.y + 60) {
                        if (field.correct) {
                            gameScore += 10;
                            ctx.fillStyle = '#27ae60';
                            ctx.fillRect(field.x, field.y, 250, 60);
                            ctx.fillStyle = 'white';
                            ctx.fillText('正确！', field.x + 125, field.y + 40);
                            document.getElementById('score3').textContent = `正确！得分：${gameScore}`;
                        } else {
                            ctx.fillStyle = '#e74c3c';
                            ctx.fillRect(field.x, field.y, 250, 60);
                            ctx.fillStyle = 'white';
                            ctx.fillText('错误！', field.x + 125, field.y + 40);
                            document.getElementById('score3').textContent = '错误！频繁更新的字段不适合建索引。';
                        }
                    }
                });
            };
        }

        // 数据区分度演示
        function startSelectivityDemo() {
            const canvas = document.getElementById('canvas4');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const fields = [
                { name: 'gender', values: ['男', '女', '未知'], selectivity: 'low', x: 100, y: 80 },
                { name: 'user_id', values: ['1001', '1002', '1003', '...'], selectivity: 'high', x: 450, y: 80 }
            ];

            let step = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据区分度对比', canvas.width/2, 30);

                fields.forEach((field, fieldIndex) => {
                    if (step > fieldIndex * 30) {
                        // 字段名
                        ctx.fillStyle = '#34495e';
                        ctx.font = 'bold 16px Arial';
                        ctx.fillText(field.name, field.x + 100, field.y);

                        // 绘制数据分布
                        field.values.forEach((value, i) => {
                            if (step > fieldIndex * 30 + i * 10) {
                                const y = field.y + 30 + i * 30;
                                const width = field.selectivity === 'low' ? 150 : 50 + i * 20;

                                ctx.fillStyle = field.selectivity === 'low' ? '#e74c3c' : '#27ae60';
                                ctx.fillRect(field.x, y, width, 20);

                                ctx.fillStyle = 'white';
                                ctx.font = '12px Arial';
                                ctx.fillText(value, field.x + 10, y + 15);
                            }
                        });

                        // 结论
                        if (step > 80) {
                            const conclusion = field.selectivity === 'low' ? '区分度低\n不适合建索引' : '区分度高\n适合建索引';
                            const color = field.selectivity === 'low' ? '#e74c3c' : '#27ae60';
                            ctx.fillStyle = color;
                            ctx.font = 'bold 14px Arial';
                            const lines = conclusion.split('\n');
                            lines.forEach((line, i) => {
                                ctx.fillText(line, field.x + 100, field.y + 180 + i * 20);
                            });
                        }
                    }
                });

                step += 5;
                if (step <= 100) {
                    requestAnimationFrame(animate);
                } else {
                    document.getElementById('score4').textContent = '演示完成！高区分度的字段适合建索引。';
                }
            }

            animate();
        }

        // 扩展索引演示
        function startExtendDemo() {
            const canvas = document.getElementById('canvas5');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let step = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('索引扩展 vs 新建索引', canvas.width/2, 30);

                // 原有索引
                ctx.fillStyle = '#3498db';
                ctx.fillRect(100, 80, 100, 60);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText('索引(a)', 150, 115);

                if (step > 20) {
                    // 扩展索引（推荐）
                    ctx.fillStyle = '#27ae60';
                    ctx.fillRect(100, 160, 200, 60);
                    ctx.fillStyle = 'white';
                    ctx.fillText('扩展为索引(a,b)', 200, 195);

                    ctx.fillStyle = '#27ae60';
                    ctx.font = '12px Arial';
                    ctx.fillText('✓ 推荐：扩展现有索引', 100, 240);
                    ctx.fillText('✓ 节省存储空间', 100, 255);
                    ctx.fillText('✓ 减少维护成本', 100, 270);
                }

                if (step > 40) {
                    // 新建索引（不推荐）
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillRect(500, 80, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 14px Arial';
                    ctx.fillText('索引(a)', 550, 115);

                    ctx.fillStyle = '#e74c3c';
                    ctx.fillRect(500, 160, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.fillText('索引(b)', 550, 195);

                    ctx.fillStyle = '#e74c3c';
                    ctx.font = '12px Arial';
                    ctx.fillText('✗ 不推荐：新建索引', 500, 240);
                    ctx.fillText('✗ 浪费存储空间', 500, 255);
                    ctx.fillText('✗ 增加维护成本', 500, 270);
                }

                step += 2;
                if (step <= 60) {
                    requestAnimationFrame(animate);
                } else {
                    document.getElementById('score5').textContent = '演示完成！优先扩展现有索引而不是新建。';
                }
            }

            animate();
        }

        // 外键索引演示
        function startForeignKeyDemo() {
            const canvas = document.getElementById('canvas6');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let step = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('外键关联查询', canvas.width/2, 30);

                // 用户表
                ctx.fillStyle = '#3498db';
                ctx.fillRect(100, 80, 150, 100);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText('用户表', 175, 105);
                ctx.font = '12px Arial';
                ctx.fillText('user_id (PK)', 175, 125);
                ctx.fillText('name', 175, 145);
                ctx.fillText('email', 175, 165);

                // 订单表
                if (step > 20) {
                    ctx.fillStyle = '#e67e22';
                    ctx.fillRect(450, 80, 150, 100);
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 14px Arial';
                    ctx.fillText('订单表', 525, 105);
                    ctx.font = '12px Arial';
                    ctx.fillText('order_id (PK)', 525, 125);
                    ctx.fillText('user_id (FK)', 525, 145);
                    ctx.fillText('amount', 525, 165);

                    // 关联线
                    drawArrow(ctx, 250, 130, 450, 145);
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 12px Arial';
                    ctx.fillText('外键关联', 330, 125);
                }

                if (step > 40) {
                    // 查询示例
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = '14px Arial';
                    ctx.fillText('SELECT * FROM 订单表 WHERE user_id = 1001', canvas.width/2, 220);

                    if (step > 60) {
                        ctx.fillStyle = '#27ae60';
                        ctx.font = 'bold 16px Arial';
                        ctx.fillText('外键字段必须建立索引！', canvas.width/2, 250);
                        ctx.font = '12px Arial';
                        ctx.fillText('否则关联查询会非常慢', canvas.width/2, 270);
                    }
                }

                step += 2;
                if (step <= 80) {
                    requestAnimationFrame(animate);
                } else {
                    document.getElementById('score6').textContent = '演示完成！外键字段必须建立索引。';
                }
            }

            animate();
        }

        // 冗余索引演示
        function startRedundantDemo() {
            const canvas = document.getElementById('canvas7');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            let step = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('识别冗余索引', canvas.width/2, 30);

                const indexes = [
                    { name: 'status (很少查询)', usage: 5, x: 100, y: 80 },
                    { name: 'created_at (经常查询)', usage: 85, x: 300, y: 80 },
                    { name: 'deleted (重复值多)', usage: 10, x: 500, y: 80 }
                ];

                indexes.forEach((index, i) => {
                    if (step > i * 20) {
                        // 索引框
                        const color = index.usage > 50 ? '#27ae60' : '#e74c3c';
                        ctx.fillStyle = color;
                        ctx.fillRect(index.x, index.y, 150, 80);

                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.fillText(index.name, index.x + 75, index.y + 25);
                        ctx.fillText(`使用率: ${index.usage}%`, index.x + 75, index.y + 45);

                        // 建议
                        if (step > 80) {
                            const suggestion = index.usage > 50 ? '保留' : '删除';
                            ctx.fillStyle = color;
                            ctx.font = 'bold 14px Arial';
                            ctx.fillText(suggestion, index.x + 75, index.y + 110);
                        }
                    }
                });

                step += 5;
                if (step <= 100) {
                    requestAnimationFrame(animate);
                } else {
                    document.getElementById('score7').textContent = '演示完成！删除使用率低的冗余索引。';
                }
            }

            animate();
        }

        // 数据类型演示
        function startDataTypeDemo() {
            const canvas = document.getElementById('canvas8');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const dataTypes = [
                { name: 'INT', suitable: true, x: 100, y: 80 },
                { name: 'VARCHAR(50)', suitable: true, x: 250, y: 80 },
                { name: 'TEXT', suitable: false, x: 400, y: 80 },
                { name: 'IMAGE', suitable: false, x: 550, y: 80 },
                { name: 'DATETIME', suitable: true, x: 100, y: 180 },
                { name: 'BIT', suitable: false, x: 250, y: 180 }
            ];

            let step = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据类型与索引适用性', canvas.width/2, 30);

                dataTypes.forEach((type, i) => {
                    if (step > i * 10) {
                        const color = type.suitable ? '#27ae60' : '#e74c3c';
                        ctx.fillStyle = color;
                        ctx.fillRect(type.x, type.y, 120, 60);

                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 12px Arial';
                        ctx.fillText(type.name, type.x + 60, type.y + 30);

                        const status = type.suitable ? '✓ 适合' : '✗ 不适合';
                        ctx.fillText(status, type.x + 60, type.y + 50);
                    }
                });

                step += 5;
                if (step <= 80) {
                    requestAnimationFrame(animate);
                } else {
                    document.getElementById('score8').textContent = '演示完成！大对象类型不适合建索引。';
                }
            }

            animate();
        }

        // 最终测试
        function startFinalQuiz() {
            const canvas = document.getElementById('canvas8');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const questions = [
                {
                    question: '以下哪个字段最适合建立索引？',
                    options: ['gender (性别)', 'user_id (用户ID)', 'description (TEXT)', 'status (BIT)'],
                    correct: 1,
                    answered: false
                }
            ];

            let currentQuestion = 0;

            function drawQuestion() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                const q = questions[currentQuestion];

                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(q.question, canvas.width/2, 50);

                q.options.forEach((option, i) => {
                    const x = 100 + (i % 2) * 300;
                    const y = 100 + Math.floor(i / 2) * 80;

                    ctx.fillStyle = '#667eea';
                    ctx.fillRect(x, y, 250, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.fillText(option, x + 125, y + 40);
                });
            }

            drawQuestion();

            canvas.onclick = function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const q = questions[currentQuestion];
                if (q.answered) return;

                q.options.forEach((option, i) => {
                    const optionX = 100 + (i % 2) * 300;
                    const optionY = 100 + Math.floor(i / 2) * 80;

                    if (x >= optionX && x <= optionX + 250 &&
                        y >= optionY && y <= optionY + 60) {

                        const color = i === q.correct ? '#27ae60' : '#e74c3c';
                        ctx.fillStyle = color;
                        ctx.fillRect(optionX, optionY, 250, 60);
                        ctx.fillStyle = 'white';
                        ctx.fillText(option, optionX + 125, optionY + 40);

                        if (i === q.correct) {
                            gameScore += 20;
                            document.getElementById('score8').textContent = `恭喜！最终得分：${gameScore}`;
                            document.getElementById('finalScore').textContent = `总得分：${gameScore} 分`;
                        } else {
                            document.getElementById('score8').textContent = '答错了！user_id具有高区分度，适合建索引。';
                        }

                        q.answered = true;
                    }
                });
            };
        }

        // 学习总结
        function showSummary() {
            const canvas = document.getElementById('canvasSummary');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const principles = [
                '1. 左前缀匹配原则',
                '2. 查询频率原则',
                '3. 更新频率原则',
                '4. 数据区分度原则',
                '5. 扩展索引原则',
                '6. 外键索引原则',
                '7. 避免冗余索引原则',
                '8. 数据类型限制原则'
            ];

            let step = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 MySQL索引8大原则 🎉', canvas.width/2, 40);

                // 绘制原则列表
                principles.forEach((principle, i) => {
                    if (step > i * 10) {
                        const x = 100;
                        const y = 80 + i * 35;

                        // 动画效果
                        const alpha = Math.min((step - i * 10) / 20, 1);
                        ctx.globalAlpha = alpha;

                        ctx.fillStyle = '#667eea';
                        ctx.fillRect(x - 20, y - 15, 20, 20);

                        ctx.fillStyle = '#2c3e50';
                        ctx.font = '18px Arial';
                        ctx.textAlign = 'left';
                        ctx.fillText(principle, x + 20, y);

                        ctx.globalAlpha = 1;
                    }
                });

                // 最终评价
                if (step > 100) {
                    ctx.fillStyle = '#27ae60';
                    ctx.font = 'bold 20px Arial';
                    ctx.textAlign = 'center';

                    let message = '';
                    if (gameScore >= 80) {
                        message = '🏆 优秀！您已经掌握了索引设计的精髓！';
                    } else if (gameScore >= 60) {
                        message = '👍 良好！继续加油，多多练习！';
                    } else {
                        message = '💪 加油！建议重新学习巩固知识点！';
                    }

                    ctx.fillText(message, canvas.width/2, 360);
                }

                step += 3;
                if (step <= 120) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }
    </script>
</body>
</html>
