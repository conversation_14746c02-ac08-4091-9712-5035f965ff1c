<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Construct</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f2f5;
            color: #333;
            overflow: hidden;
        }

        .container {
            text-align: center;
            background-color: #ffffff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 800px;
        }

        h1 {
            font-size: 3em;
            color: #1a237e;
            margin-bottom: 10px;
        }
        
        .pronunciation {
            font-size: 1.2em;
            color: #555;
            margin-bottom: 20px;
        }

        p {
            font-size: 1.2em;
            line-height: 1.6;
            color: #444;
        }

        #canvas-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin-top: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
            overflow: hidden;
        }

        canvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        .button-container {
            margin-top: 25px;
        }

        button {
            background-color: #3949ab;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 1em;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            margin: 0 10px;
        }

        button:hover {
            background-color: #283593;
            transform: translateY(-2px);
        }

        button:disabled {
            background-color: #9fa8da;
            cursor: not-allowed;
        }
        
        .info-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2em;
            font-weight: bold;
            color: #1a237e;
            opacity: 0;
            white-space: nowrap;
        }
        
        .sub-text {
             font-size: 0.7em;
             color: #5c6bc0;
             margin-top: 10px;
        }

    </style>
</head>
<body>

<div class="container">
    <h1>construct</h1>
    <p class="pronunciation">/kənˈstrʌkt/</p>
    <p>v. 建造，构成</p>
    <p id="story-text">故事：这个单词由前缀 <strong>con-</strong> (共同, 一起) 和词根 <strong>struct</strong> (建造) 组成。想象一下，把一堆零件 (struct) 全部放到一起 (con-)... 会发生什么呢？</p>
    
    <div id="canvas-container">
        <canvas id="animationCanvas"></canvas>
        <div id="text-struct" class="info-text">struct<div class="sub-text">(建造)</div></div>
        <div id="text-con" class="info-text">con-<div class="sub-text">(共同, 一起)</div></div>
        <div id="text-construct" class="info-text">construct<div class="sub-text">(建造, 构成)</div></div>
    </div>

    <div class="button-container">
        <button id="playBtn">开始动画</button>
        <button id="resetBtn" disabled>重置</button>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js"></script>
<script>
    const canvas = document.getElementById('animationCanvas');
    const ctx = canvas.getContext('2d');
    const playBtn = document.getElementById('playBtn');
    const resetBtn = document.getElementById('resetBtn');
    const storyText = document.getElementById('story-text');

    const textStruct = document.getElementById('text-struct');
    const textCon = document.getElementById('text-con');
    const textConstruct = document.getElementById('text-construct');

    let animation;
    let blocks = [];
    let isPlaying = false;

    // Set canvas size
    const container = document.getElementById('canvas-container');
    let width = container.clientWidth;
    let height = container.clientHeight;
    canvas.width = width;
    canvas.height = height;

    window.onresize = () => {
        width = container.clientWidth;
        height = container.clientHeight;
        canvas.width = width;
        canvas.height = height;
        if (!isPlaying) {
             reset();
        }
    };

    const blockSettings = {
        count: 12,
        size: 25,
        colors: ['#42a5f5', '#66bb6a', '#ffa726', '#ef5350', '#ab47bc']
    };

    class Block {
        constructor(x, y, tx, ty) {
            this.x = x;
            this.y = y;
            this.tx = tx; // target x
            this.ty = ty; // target y
            this.size = blockSettings.size;
            this.color = blockSettings.colors[Math.floor(Math.random() * blockSettings.colors.length)];
            this.rotation = Math.random() * Math.PI * 2;
        }

        draw() {
            ctx.save();
            ctx.translate(this.x, this.y);
            ctx.rotate(this.rotation);
            ctx.fillStyle = this.color;
            ctx.fillRect(-this.size / 2, -this.size / 2, this.size, this.size);
            ctx.restore();
        }
    }

    function createBlocks() {
        blocks = [];
        const targetStructure = [
            {x: 0, y: 0}, {x: 1, y: 0}, {x: 2, y: 0}, {x: 3, y: 0},
            {x: 0.5, y: -1}, {x: 1.5, y: -1}, {x: 2.5, y: -1},
            {x: 1, y: -2}, {x: 2, y: -2},
            {x: 1.5, y: -3},
            {x: 0, y: -1.5, isTriangle: true}, // roof
            {x: 3, y: -1.5, isTriangle: true} // roof
        ];

        targetStructure.forEach(pos => {
            const initialX = Math.random() * width;
            const initialY = Math.random() * height;
            const targetX = width / 2 + pos.x * blockSettings.size - (3 * blockSettings.size) / 2;
            const targetY = height - 50 + pos.y * blockSettings.size;
            blocks.push(new Block(initialX, initialY, targetX, targetY));
        });
    }
    
    function reset() {
        if (animation) animation.pause();
        ctx.clearRect(0, 0, width, height);
        isPlaying = false;
        
        anime.set([textStruct, textCon, textConstruct], { opacity: 0 });

        createBlocks();
        blocks.forEach(block => block.draw());
        
        playBtn.disabled = false;
        resetBtn.disabled = true;
        storyText.innerHTML = '故事：这个单词由前缀 <strong>con-</strong> (共同, 一起) 和词根 <strong>struct</strong> (建造) 组成。想象一下，把一堆零件 (struct) 全部放到一起 (con-)... 会发生什么呢？';
    }

    function playAnimation() {
        if (isPlaying) return;
        isPlaying = true;
        playBtn.disabled = true;

        const tl = anime.timeline({
            duration: 1200,
            easing: 'easeInOutSine',
            complete: () => {
                resetBtn.disabled = false;
                isPlaying = false;
            }
        });

        // 1. Show 'struct'
        tl.add({
            targets: textStruct,
            opacity: [0, 1],
            scale: [0.8, 1],
            duration: 800,
            begin: () => {
                storyText.textContent = '首先，我们有词根 struct，意思是"建造"。看，这些就是我们用来"建造"的零散部件...';
            },
            complete: () => {
                storyText.textContent = '这些零散的部件 (struct) 四散各处。';
            }
        });
        
        tl.add({
            duration: 1500 // Pause
        });
        
        // 2. Hide 'struct', show 'con-'
        tl.add({
            targets: textStruct,
            opacity: 0,
            scale: 0.8,
            duration: 800
        }, '-=800');
        
        tl.add({
            targets: textCon,
            opacity: [0, 1],
            scale: [0.8, 1],
            duration: 800,
            begin: () => {
                storyText.textContent = '然后，前缀 con- 出现了，它意味着"共同，一起"。';
            }
        });
        
        tl.add({
            duration: 500 // Pause
        });

        // 3. 'con-' brings 'struct' together
        tl.add({
            targets: blocks,
            x: (b) => b.tx,
            y: (b) => b.ty,
            rotation: anime.stagger(30, {start: 0, direction: 'reverse'}),
            delay: anime.stagger(100, {grid: [4, 3], from: 'center'}),
            duration: 2000,
            easing: 'easeInOutCubic',
            begin: () => {
                anime({ targets: textCon, opacity: 0, duration: 500 });
                storyText.textContent = '在 con- (一起) 的作用下，所有的部件 (struct) 开始向中心聚集并建造起来...';
            },
            update: () => {
                ctx.clearRect(0, 0, width, height);
                blocks.forEach(b => b.draw());
            }
        });
        
        // 4. Show 'construct'
        tl.add({
            targets: textConstruct,
            opacity: [0, 1],
            scale: [0.8, 1],
            duration: 1000,
            begin: () => {
                storyText.innerHTML = '看！这些部件 (struct) 被放到了一起 (con-), 它们 <strong>construct</strong> (建造) 了一座房子！';
            }
        }, '-=500');

        animation = tl;
    }

    playBtn.addEventListener('click', playAnimation);
    resetBtn.addEventListener('click', reset);

    // Initial state
    reset();

</script>
</body>
</html> 