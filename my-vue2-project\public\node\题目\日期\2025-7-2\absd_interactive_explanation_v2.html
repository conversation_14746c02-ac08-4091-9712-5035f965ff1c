<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式理解：基于架构的软件设计 (ABSD)</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Microsoft YaHei', 'Helvetica Neue', sans-serif;
            background-color: #f0f4f8;
            color: #333;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            max-width: 900px;
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            padding: 30px;
            box-sizing: border-box;
        }

        h1, h2 {
            color: #1a4a7e;
            text-align: center;
            border-bottom: 2px solid #e0e6ec;
            padding-bottom: 10px;
        }

        .question-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            line-height: 1.8;
        }
        
        .question-box p {
            margin: 0 0 10px 0;
        }

        .question-box code {
            background-color: #e9ecef;
            color: #c7254e;
            padding: 2px 6px;
            border-radius: 4px;
        }

        .interactive-area {
            text-align: center;
        }

        #abds-canvas {
            border: 1px solid #d1d9e1;
            border-radius: 8px;
            background-color: #fdfdfd;
            cursor: pointer;
            box-shadow: inset 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background-color: #e7f3ff;
            border-left: 5px solid #0d6efd;
            border-radius: 0 8px 8px 0;
            text-align: left;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .info-panel p {
            margin: 0;
            font-size: 1.1em;
            color: #0a58ca;
        }

        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 0.9em;
            color: #777;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>交互式理解：ABSD 方法</h1>
        
        <div class="question-box">
            <h2>原题回顾</h2>
            <p>某公司采用基于架构的软件设计 (ABSD) 方法进行软件设计与开发。ABSD方法有三个基础，分别是：对系统进行功能分解、采用 <code>架构风格</code> 实现质量属性与商业需求、采用 <code>软件模板</code> 设计软件结构。</p>
            <p>ABSD方法主要包括架构需求等6个主要活动，其中 <code>( ? )</code> 活动的目标是标识潜在的风险，及早发现架构设计中的缺陷和错误；<code>( ? )</code> 活动针对用户的需求变化，修改应用架构，满足新的需求。</p>
            <p><strong>问题：</strong> 上文中的 <code>( ? )</code> 处，应该分别填入什么？</p>
        </div>

        <div class="interactive-area">
            <h2>交互式动画演示：ABSD 的核心活动</h2>
            <canvas id="abds-canvas" width="840" height="400"></canvas>
            <div class="info-panel">
                <p><strong>提示：</strong>请点击画布中的任意一个活动模块，来查看它的作用和动画演示！</p>
            </div>
        </div>
        
        <div class="footer">
            <p>通过上面的交互演示，我们可以知道第一个空应该填"架构复审"，它的目标就是发现风险和缺陷。</p>
        </div>
    </div>

    <script>
    const canvas = document.getElementById('abds-canvas');
    const ctx = canvas.getContext('2d');
    const infoPanel = document.querySelector('.info-panel p');

    const activities = [
        { id: 'req', name: '架构需求', x: 50, y: 50, width: 150, height: 70, color: '#28a745', hoverColor: '#218838', explanation: '收集和分析对软件架构有重要影响的需求，是所有工作的基础。' },
        { id: 'design', name: '架构设计', x: 240, y: 50, width: 150, height: 70, color: '#007bff', hoverColor: '#0069d9', explanation: '根据需求，创建软件的整体结构和蓝图，就像建筑师画图纸。' },
        { id: 'doc', name: '架构文档化', x: 430, y: 50, width: 150, height: 70, color: '#6f42c1', hoverColor: '#5a32a3', explanation: '将设计好的架构清晰地记录下来，方便团队沟通和未来维护。' },
        { id: 'review', name: '架构复审', x: 145, y: 180, width: 150, height: 70, color: '#dc3545', hoverColor: '#c82333', explanation: '【题目考点】像侦探一样，主动寻找架构中的潜在风险和错误，确保它足够健壮。' },
        { id: 'impl', name: '架构实现', x: 335, y: 180, width: 150, height: 70, color: '#ffc107', hoverColor: '#e0a800', explanation: '按照架构蓝图，通过编程将设计变为可以运行的真实软件。' },
        { id: 'evo', name: '架构演化', x: 525, y: 180, width: 150, height: 70, color: '#fd7e14', hoverColor: '#e66a0a', explanation: '【题目考点】当用户需求变化或新技术出现时，对架构进行修改和升级。' }
    ];

    let mouseX = 0;
    let mouseY = 0;
    let selectedActivity = null;
    let animationState = {
        type: null,
        progress: 0,
        done: true
    };

    function drawRoundedRect(x, y, width, height, radius, color) {
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.moveTo(x + radius, y);
        ctx.lineTo(x + width - radius, y);
        ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        ctx.lineTo(x + width, y + height - radius);
        ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        ctx.lineTo(x + radius, y + height);
        ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        ctx.lineTo(x, y + radius);
        ctx.quadraticCurveTo(x, y, x + radius, y);
        ctx.closePath();
        ctx.fill();
    }

    function drawMain() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        ctx.font = '16px "Microsoft YaHei"';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        activities.forEach(act => {
            const isHovered = mouseX > act.x && mouseX < act.x + act.width && mouseY > act.y && mouseY < act.y + act.height;
            const color = isHovered ? act.hoverColor : act.color;
            
            drawRoundedRect(act.x, act.y, act.width, act.height, 10, color);
            
            ctx.fillStyle = '#fff';
            ctx.fillText(act.name, act.x + act.width / 2, act.y + act.height / 2);

            if (selectedActivity && selectedActivity.id === act.id) {
                ctx.strokeStyle = '#1a4a7e';
                ctx.lineWidth = 4;
                ctx.strokeRect(act.x - 4, act.y - 4, act.width + 8, act.height + 8);
            }
        });

        drawConnectors();
        runAnimation();
    }
    
    function drawConnectors() {
        ctx.strokeStyle = '#a0a0a0';
        ctx.lineWidth = 2;
        ctx.beginPath();
        // Design -> Review
        ctx.moveTo(315, 120);
        ctx.lineTo(220, 180);
        // Design -> Impl
        ctx.moveTo(315, 120);
        ctx.lineTo(410, 180);
        // Impl -> Evo
        ctx.moveTo(410, 250);
        ctx.lineTo(525, 220); // Just suggestive lines
        ctx.stroke();
    }

    function runAnimation() {
        if (animationState.done) return;
        
        const { type, progress } = animationState;
        
        ctx.save();
        if (type === 'review') {
            drawReviewAnimation(progress);
        } else if (type === 'evo') {
            drawEvoAnimation(progress);
        }
        ctx.restore();

        animationState.progress += 0.01;
        if (animationState.progress >= 1) {
            animationState.done = true;
            animationState.progress = 0;
        }
    }

    function drawReviewAnimation(p) {
        const cx = 750, cy = 280;
        // Draw a mini architecture
        ctx.strokeStyle = '#999';
        ctx.strokeRect(cx - 40, cy - 20, 30, 30);
        ctx.strokeRect(cx + 10, cy - 20, 30, 30);
        ctx.strokeRect(cx - 15, cy + 20, 30, 30);
        
        // Magnifying glass
        const angle = p * Math.PI * 4;
        const glassX = cx + Math.cos(angle) * 50;
        const glassY = cy + Math.sin(angle) * 30;
        ctx.lineWidth = 3;
        ctx.strokeStyle = '#007bff';
        ctx.beginPath();
        ctx.arc(glassX, glassY, 15, 0, Math.PI * 2);
        ctx.moveTo(glassX + 10, glassY + 10);
        ctx.lineTo(glassX + 25, glassY + 25);
        ctx.stroke();

        // Finding bugs (red X's)
        if (p > 0.2 && p < 0.7) {
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = 'red';
            ctx.fillText('X', cx - 25, cy - 5);
        }
        if (p > 0.4 && p < 0.9) {
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = 'red';
            ctx.fillText('X', cx, cy + 35);
        }

        // Fixed (green checks)
        if (p >= 0.7) {
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = 'green';
            ctx.fillText('✓', cx - 25, cy - 5);
        }
        if (p >= 0.9) {
             ctx.font = 'bold 20px Arial';
            ctx.fillStyle = 'green';
            ctx.fillText('✓', cx, cy + 35);
        }
    }

    function drawEvoAnimation(p) {
        const cx = 750, cy = 280;
        // Draw base architecture
        ctx.strokeStyle = '#999';
        ctx.strokeRect(cx - 50, cy, 30, 30);
        ctx.strokeRect(cx, cy, 30, 30);
        
        // Show "new requirement"
        if (p > 0.1 && p < 0.5) {
            ctx.fillStyle = '#fd7e14';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('+ New', cx - 10, cy - 30);
        }

        // Draw new module
        if (p > 0.5) {
            const newY = cy - (p - 0.5) * 100;
            ctx.strokeStyle = '#fd7e14';
            ctx.lineWidth = 2;
            ctx.strokeRect(cx + 50, newY, 30, 30);
            
            // Connecting line
            ctx.beginPath();
            ctx.moveTo(cx + 15, cy);
            ctx.lineTo(cx + 50, newY + 15);
            ctx.stroke();
        }
    }
    
    function startAnimation(type) {
        animationState.type = type;
        animationState.progress = 0;
        animationState.done = false;
    }

    canvas.addEventListener('mousemove', e => {
        const rect = canvas.getBoundingClientRect();
        mouseX = e.clientX - rect.left;
        mouseY = e.clientY - rect.top;
    });

    canvas.addEventListener('click', e => {
        const rect = canvas.getBoundingClientRect();
        const clickX = e.clientX - rect.left;
        const clickY = e.clientY - rect.top;

        selectedActivity = null;
        let clickedOnSomething = false;
        activities.forEach(act => {
            if (clickX > act.x && clickX < act.x + act.width && clickY > act.y && clickY < act.y + act.height) {
                selectedActivity = act;
                infoPanel.innerHTML = `<strong>${act.name}:</strong> ${act.explanation}`;
                clickedOnSomething = true;
                if(act.id === 'review') {
                    startAnimation('review');
                } else if (act.id === 'evo') {
                    startAnimation('evo');
                } else {
                    animationState.done = true;
                }
            }
        });

        if (!clickedOnSomething) {
            infoPanel.innerHTML = '<strong>提示：</strong>请点击画布中的任意一个活动模块，来查看它的作用和动画演示！';
        }
    });

    setInterval(drawMain, 1000 / 60);

    </script>
</body>
</html> 