<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件开发方法互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .game-board {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            text-align: center;
        }

        .methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .method-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .method-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .method-card.correct {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            animation: correctPulse 0.6s ease-out;
        }

        .method-card.wrong {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            animation: wrongShake 0.6s ease-out;
        }

        .method-name {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .method-description {
            font-size: 0.95rem;
            line-height: 1.5;
            opacity: 0.9;
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border: 3px solid #667eea;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .explanation {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
            animation: fadeIn 1s ease-out;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.4rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .score {
            text-align: center;
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin: 20px 0;
        }

        .restart-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto;
        }

        .restart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
    </style>
</head>
<body>
    <canvas class="floating-particles" id="particleCanvas"></canvas>
    
    <div class="container">
        <div class="header">
            <h1>🚀 软件开发方法学习游戏</h1>
            <p>通过有趣的互动游戏，轻松掌握各种软件开发方法的特点和应用场景</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-title">
                    🎯 题目：哪种开发方法适用于程序开发人员在地域上分布很广的开发团队，并且程序开发人员分成首席程序员和"类"程序员？
                </div>
                
                <div class="score" id="score">得分: 0</div>
                
                <div class="methods-grid" id="methodsGrid">
                    <!-- 方法卡片将通过JavaScript动态生成 -->
                </div>
                
                <button class="restart-btn" onclick="restartGame()">🔄 重新开始</button>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation" id="explanation" style="display: none;">
                <h3>📚 详细解析</h3>
                <div id="explanationContent"></div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let score = 0;
        let gameCompleted = false;
        
        // 软件开发方法数据
        const methods = [
            {
                name: "自适应软件开发（ASD）",
                description: "由Jim Highsmith提出，核心是3个非线性且重叠的开发阶段：猜测、合作与学习",
                isCorrect: false,
                details: "ASD强调适应性和学习，但不特别强调地域分布或角色分工"
            },
            {
                name: "极限编程（XP）",
                description: "最引人瞩目的敏捷方法，源于Smalltalk圈子，强调高度纪律性和团队协作",
                isCorrect: false,
                details: "XP强调团队在同一地点工作，不适合地域分布广的团队"
            },
            {
                name: "开放统一过程（OpenUP）",
                description: "开放源码界的运作方式，程序开发人员在地域上分布很广",
                isCorrect: false,
                details: "虽然适合地域分布，但没有明确的首席程序员和类程序员角色分工"
            },
            {
                name: "功用驱动开发（FDD）",
                description: "由Jeff De Luca和Peter Coad提出，程序员分为首席程序员和"类"程序员",
                isCorrect: true,
                details: "FDD明确将开发人员分为首席程序员（协调者、设计者、指导者）和类程序员（源码编写），适合分布式团队"
            }
        ];

        // 初始化游戏
        function initGame() {
            renderMethodCards();
            initCanvas();
            initParticles();
        }

        // 渲染方法卡片
        function renderMethodCards() {
            const grid = document.getElementById('methodsGrid');
            grid.innerHTML = '';
            
            methods.forEach((method, index) => {
                const card = document.createElement('div');
                card.className = 'method-card';
                card.innerHTML = `
                    <div class="method-name">${method.name}</div>
                    <div class="method-description">${method.description}</div>
                `;
                card.addEventListener('click', () => selectMethod(index, card));
                grid.appendChild(card);
            });
        }

        // 选择方法
        function selectMethod(index, cardElement) {
            if (gameCompleted) return;
            
            const method = methods[index];
            
            if (method.isCorrect) {
                cardElement.classList.add('correct');
                score += 100;
                gameCompleted = true;
                showExplanation();
                animateSuccess();
            } else {
                cardElement.classList.add('wrong');
                score = Math.max(0, score - 20);
                setTimeout(() => {
                    cardElement.classList.remove('wrong');
                }, 600);
            }
            
            updateScore();
        }

        // 更新分数
        function updateScore() {
            document.getElementById('score').textContent = `得分: ${score}`;
        }

        // 显示解析
        function showExplanation() {
            const explanation = document.getElementById('explanation');
            const content = document.getElementById('explanationContent');
            
            content.innerHTML = `
                <p><strong>正确答案：功用驱动开发方法（FDD）</strong></p>
                <p><strong>关键特征：</strong></p>
                <p>1. <strong>角色分工明确：</strong>FDD将程序开发人员分为两类：</p>
                <p>   • 首席程序员：最富有经验的开发人员，担任项目协调者、设计者和指导者</p>
                <p>   • "类"程序员：主要负责源码编写工作</p>
                <p>2. <strong>短迭代周期：</strong>一般为两周的迭代周期</p>
                <p>3. <strong>功能驱动：</strong>致力于短时的迭代阶段和可见可用的功能</p>
                <p><strong>为什么其他选项不正确：</strong></p>
                <p>• XP：强调团队在同一地点工作，不适合地域分布广的团队</p>
                <p>• ASD：强调适应性学习，但没有特定的角色分工</p>
                <p>• OpenUP：虽然适合分布式开发，但角色分工不如FDD明确</p>
            `;
            
            explanation.style.display = 'block';
        }

        // 重新开始游戏
        function restartGame() {
            score = 0;
            gameCompleted = false;
            updateScore();
            document.getElementById('explanation').style.display = 'none';
            
            // 重置所有卡片
            const cards = document.querySelectorAll('.method-card');
            cards.forEach(card => {
                card.classList.remove('correct', 'wrong');
            });
            
            clearCanvas();
        }

        // Canvas动画
        function initCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            // 绘制初始状态
            drawTeamStructure(ctx);
        }

        function drawTeamStructure(ctx) {
            ctx.clearRect(0, 0, 800, 400);
            
            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('FDD开发团队结构', 400, 40);
            
            // 绘制首席程序员
            ctx.fillStyle = '#667eea';
            ctx.fillRect(350, 80, 100, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('首席程序员', 400, 105);
            ctx.fillText('(协调/设计)', 400, 125);
            
            // 绘制类程序员
            const positions = [
                {x: 150, y: 200}, {x: 300, y: 200}, 
                {x: 450, y: 200}, {x: 600, y: 200}
            ];
            
            positions.forEach((pos, index) => {
                ctx.fillStyle = '#764ba2';
                ctx.fillRect(pos.x, pos.y, 100, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('类程序员', pos.x + 50, pos.y + 25);
                ctx.fillText(`(编码${index + 1})`, pos.x + 50, pos.y + 45);
                
                // 绘制连接线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(400, 140);
                ctx.lineTo(pos.x + 50, pos.y);
                ctx.stroke();
            });
            
            // 绘制地域分布标识
            ctx.fillStyle = '#f093fb';
            ctx.font = '12px Microsoft YaHei';
            ctx.fillText('🌍 北京', 200, 290);
            ctx.fillText('🌍 上海', 350, 290);
            ctx.fillText('🌍 深圳', 500, 290);
            ctx.fillText('🌍 成都', 650, 290);
        }

        function animateSuccess() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            
            let frame = 0;
            const animate = () => {
                if (frame < 60) {
                    ctx.save();
                    ctx.globalAlpha = 0.1;
                    ctx.fillStyle = '#4CAF50';
                    ctx.fillRect(0, 0, 800, 400);
                    ctx.restore();
                    
                    // 添加成功特效
                    ctx.fillStyle = '#4CAF50';
                    ctx.font = 'bold 36px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('🎉 正确！', 400, 350);
                    
                    frame++;
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        function clearCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            drawTeamStructure(ctx);
        }

        // 粒子背景动画
        function initParticles() {
            const canvas = document.getElementById('particleCanvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            const particles = [];
            
            for (let i = 0; i < 50; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: Math.random() * 3 + 1,
                    opacity: Math.random() * 0.5 + 0.2
                });
            }
            
            function animateParticles() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    
                    if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
                    
                    ctx.save();
                    ctx.globalAlpha = particle.opacity;
                    ctx.fillStyle = 'white';
                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                });
                
                requestAnimationFrame(animateParticles);
            }
            
            animateParticles();
        }

        // 窗口大小改变时重新调整粒子画布
        window.addEventListener('resize', () => {
            const canvas = document.getElementById('particleCanvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });

        // 初始化游戏
        window.addEventListener('load', initGame);
    </script>
</body>
</html>
