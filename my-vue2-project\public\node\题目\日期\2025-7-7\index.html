<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UML 图知识学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .question-section {
            margin-bottom: 30px;
        }
        .question-text {
            font-size: 1.2em;
            margin-bottom: 20px;
            background-color: #e8f5e9;
            padding: 15px;
            border-radius: 8px;
            border-left: 5px solid #4CAF50;
        }
        .options-list {
            list-style: none;
            padding: 0;
        }
        .options-list li {
            background-color: #f9f9f9;
            margin-bottom: 10px;
            padding: 12px 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            transition: background-color 0.3s ease;
        }
        .options-list li:hover {
            background-color: #eef;
        }
        .options-list li.correct {
            background-color: #d4edda;
            border-color: #28a745;
            font-weight: bold;
        }
        .explanation-section {
            margin-top: 40px;
            background-color: #e0f2f7;
            padding: 25px;
            border-radius: 10px;
            border-left: 5px solid #2196F3;
        }
        .explanation-section p {
            margin-bottom: 10px;
        }
        .explanation-item {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #ccc;
        }
        .explanation-item:last-child {
            border-bottom: none;
        }
        .canvas-section {
            margin-top: 30px;
            text-align: center;
        }
        canvas {
            border: 2px solid #3498db;
            background-color: #ecf0f1;
            display: block;
            margin: 20px auto;
            border-radius: 8px;
        }
        .interactive-info {
            font-size: 0.9em;
            color: #555;
            margin-top: 10px;
            background-color: #fff3cd;
            border-left: 4px solid #ffe082;
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>UML 图知识学习与交互演示</h1>

        <div class="question-section">
            <h2>题目</h2>
            <div class="question-text">
                在uml图中，(<span id="blank1"></span>) 展现了一组对象、接口、协作和它们之间的关系。 (<span id="blank2"></span>) 强调消息跨越不同对象或参与者的实际时间，而不仅仅是关心消息的相对顺序。 (<span id="blank3"></span>) 软件和硬件组件之间的物理关系以及处理节点的组件分布情况。 (<span id="blank4"></span>) 描述由模型本身分解而成的组织单元，以及它们之间的依赖关系。
            </div>
            <h3>问题 1 [单选题]</h3>
            <ul class="options-list">
                <li id="optionA">A 类图</li>
                <li id="optionB">B 对象图</li>
                <li id="optionC">C 部署图</li>
                <li id="optionD">D 定时图</li>
            </ul>
            <p><strong>正确答案: A</strong></p>
        </div>

        <div class="explanation-section">
            <h2>题目解析与知识讲解</h2>

            <div class="explanation-item">
                <h3>1. 类图 (Class Diagram)</h3>
                <p><strong>定义:</strong> 类图展现了一组对象、接口、协作和它们之间的关系。它是面向对象系统建模中最常用的一种图，用于描述系统的静态结构。</p>
                <p><strong>作用:</strong></p>
                <ul>
                    <li>显示类、接口和协作的属性和操作。</li>
                    <li>展示类之间的关系，如继承（泛化）、实现、关联、聚合和组合。</li>
                    <li>是构建其他UML图的基础，例如对象图、组件图等。</li>
                </ul>
                <div class="canvas-section">
                    <h3>类图交互演示</h3>
                    <canvas id="classDiagramCanvas" width="700" height="400"></canvas>
                    <p class="interactive-info">点击并拖动类框，观察其移动效果。点击类框可以查看更多信息（目前为占位符）。</p>
                </div>
            </div>

            <div class="explanation-item">
                <h3>2. 定时图 (Timing Diagram)</h3>
                <p><strong>定义:</strong> 定时图是交互图的一种，它强调消息跨越不同对象或参与者的实际时间，而不仅仅是关心消息的相对顺序。</p>
                <p><strong>作用:</strong></p>
                <ul>
                    <li>显示对象状态随时间的变化。</li>
                    <li>展示对象在特定时间点之间的相互作用。</li>
                    <li>适用于实时系统和嵌入式系统的建模，对时间约束敏感的场景特别有用。</li>
                </ul>
                <!-- 这里可以添加定时图的Canvas演示，目前为占位符 -->
            </div>

            <div class="explanation-item">
                <h3>3. 部署图 (Deployment Diagram)</h3>
                <p><strong>定义:</strong> 部署图展现了软件和硬件组件之间的物理关系以及处理节点的组件分布情况。</p>
                <p><strong>作用:</strong></p>
                <ul>
                    <li>显示系统的物理架构。</li>
                    <li>描述硬件设备（节点）和部署在其上的软件组件（构件）之间的连接。</li>
                    <li>帮助理解系统在实际部署环境中的运行方式。</li>
                </ul>
                <!-- 这里可以添加部署图的Canvas演示，目前为占位符 -->
            </div>

            <div class="explanation-item">
                <h3>4. 包图 (Package Diagram)</h3>
                <p><strong>定义:</strong> 包图描述了由模型本身分解而成的组织单元（包），以及它们之间的依赖关系。</p>
                <p><strong>作用:</strong></p>
                <ul>
                    <li>用于组织和管理大型复杂系统的模型元素。</li>
                    <li>显示不同包之间的依赖关系，有助于理解系统的分层和模块化结构。</li>
                    <li>提高模型的可读性和可维护性。</li>
                </ul>
                <!-- 这里可以添加包图的Canvas演示，目前为占位符 -->
            </div>
        </div>
    </div>

    <script>
        // 自动填充题目中的空白
        document.getElementById('blank1').textContent = '类图';
        document.getElementById('blank2').textContent = '定时图';
        document.getElementById('blank3').textContent = '部署图';
        document.getElementById('blank4').textContent = '包图';

        // 标记正确答案
        document.getElementById('optionA').classList.add('correct');

        // Canvas 交互演示 - 类图
        const canvas = document.getElementById('classDiagramCanvas');
        const ctx = canvas.getContext('2d');

        let classes = [
            { name: '用户', x: 100, y: 100, width: 100, height: 70, color: '#f39c12' },
            { name: '订单', x: 300, y: 50, width: 100, height: 70, color: '#2ecc71' },
            { name: '商品', x: 350, y: 250, width: 100, height: 70, color: '#e74c3c' }
        ];

        let selectedClass = null;
        let offsetX, offsetY;

        function drawClass(cls) {
            ctx.fillStyle = cls.color;
            ctx.fillRect(cls.x, cls.y, cls.width, cls.height);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.strokeRect(cls.x, cls.y, cls.width, cls.height);

            ctx.fillStyle = '#fff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(cls.name, cls.x + cls.width / 2, cls.y + cls.height / 2);
        }

        function drawRelation(cls1, cls2) {
            ctx.strokeStyle = '#34495e';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(cls1.x + cls1.width / 2, cls1.y + cls1.height / 2);
            ctx.lineTo(cls2.x + cls2.width / 2, cls2.y + cls2.height / 2);
            ctx.stroke();
            // 绘制箭头（简化版）
            let angle = Math.atan2(cls2.y - cls1.y, cls2.x - cls1.x);
            ctx.save();
            ctx.translate(cls2.x + cls2.width / 2, cls2.y + cls2.height / 2);
            ctx.rotate(angle);
            ctx.beginPath();
            ctx.moveTo(-10, -5);
            ctx.lineTo(0, 0);
            ctx.lineTo(-10, 5);
            ctx.stroke();
            ctx.restore();
        }

        function drawAll() {
            ctx.clearRect(0, 0, canvas.width, canvas.height); // 清空画布
            // 绘制关系
            drawRelation(classes[0], classes[1]); // 用户 -> 订单
            drawRelation(classes[1], classes[2]); // 订单 -> 商品
            // 绘制类
            classes.forEach(drawClass);
        }

        canvas.addEventListener('mousedown', (e) => {
            const rect = canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            for (let i = classes.length - 1; i >= 0; i--) {
                const cls = classes[i];
                if (mouseX >= cls.x && mouseX <= cls.x + cls.width &&
                    mouseY >= cls.y && mouseY <= cls.y + cls.height) {
                    selectedClass = cls;
                    offsetX = mouseX - cls.x;
                    offsetY = mouseY - cls.y;
                    canvas.style.cursor = 'grabbing';
                    break;
                }
            }
        });

        canvas.addEventListener('mousemove', (e) => {
            if (selectedClass) {
                const rect = canvas.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                selectedClass.x = mouseX - offsetX;
                selectedClass.y = mouseY - offsetY;

                // 边界检查
                if (selectedClass.x < 0) selectedClass.x = 0;
                if (selectedClass.y < 0) selectedClass.y = 0;
                if (selectedClass.x + selectedClass.width > canvas.width) selectedClass.x = canvas.width - selectedClass.width;
                if (selectedClass.y + selectedClass.height > canvas.height) selectedClass.y = canvas.height - selectedClass.height;

                drawAll();
            }
        });

        canvas.addEventListener('mouseup', () => {
            selectedClass = null;
            canvas.style.cursor = 'grab';
        });

        canvas.addEventListener('mouseout', () => { // 防止鼠标拖出画布后仍保持拖动状态
            selectedClass = null;
            canvas.style.cursor = 'grab';
        });

        canvas.style.cursor = 'grab'; // 默认鼠标样式
        drawAll(); // 初始绘制
    </script>
</body>
</html> 