<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流水线加速比 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        .section h2 {
            font-size: 2rem;
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .pipeline-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            min-height: 200px;
        }

        .stage {
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .stage:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 25px rgba(79, 172, 254, 0.4);
        }

        .stage-time {
            position: absolute;
            bottom: -25px;
            font-size: 0.9rem;
            color: #666;
        }

        .arrow {
            width: 30px;
            height: 2px;
            background: #666;
            position: relative;
            margin: 0 5px;
        }

        .arrow::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid #666;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }

        .control-panel {
            text-align: center;
            margin: 40px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: white;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .explanation {
            background: #f7fafc;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #666;
            margin-bottom: 15px;
        }

        .formula {
            background: #667eea;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2rem;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
        }

        .result-display {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            margin: 30px 0;
            font-size: 1.3rem;
            font-weight: bold;
        }

        .task-item {
            width: 40px;
            height: 40px;
            background: #ff6b6b;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .task-item.processing {
            background: #4ecdc4;
            animation: pulse 1s infinite;
        }

        .task-item.completed {
            background: #45b7d1;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        .interactive-demo {
            text-align: center;
            margin: 40px 0;
        }

        .time-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }

        .interactive-quiz {
            margin: 30px 0;
        }

        .quiz-question {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .quiz-question h4 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.2rem;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            text-align: left;
        }

        .quiz-btn:hover {
            border-color: #667eea;
            background: #f7fafc;
            transform: translateY(-2px);
        }

        .quiz-btn.correct {
            background: #48bb78;
            color: white;
            border-color: #48bb78;
        }

        .quiz-btn.incorrect {
            background: #f56565;
            color: white;
            border-color: #f56565;
        }

        .quiz-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            display: none;
        }

        .quiz-result.show {
            display: block;
        }

        .quiz-result.correct {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #48bb78;
        }

        .quiz-result.incorrect {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #f56565;
        }

        .timeline-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .timeline-container h3 {
            color: #4a5568;
            margin-bottom: 20px;
        }

        .timeline-legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .speed-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }

        .comparison-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .comparison-card h4 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .comparison-time {
            font-size: 2rem;
            font-weight: bold;
            margin: 15px 0;
        }

        .sequential-time {
            color: #f56565;
        }

        .pipeline-time {
            color: #48bb78;
        }

        /* 新增：概念分解样式 */
        .concept-breakdown {
            background: #f8fafc;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            border: 2px solid #e2e8f0;
        }

        .concept-breakdown h3 {
            text-align: center;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 1.5rem;
        }

        .breakdown-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .concept-step {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            opacity: 0.3;
            transform: scale(0.95);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
        }

        .concept-step.active {
            opacity: 1;
            transform: scale(1);
            border-color: #667eea;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }

        .concept-step .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .concept-step.active .step-number {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .step-content h4 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .step-content p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .concept-visual {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .visual-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .visual-item span {
            min-width: 80px;
            font-size: 0.9rem;
            color: #666;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress {
            height: 100%;
            border-radius: 4px;
            transition: width 2s ease-in-out;
            width: 0;
        }

        .sequential-progress {
            background: linear-gradient(90deg, #f56565, #e53e3e);
        }

        .pipeline-progress {
            background: linear-gradient(90deg, #48bb78, #38a169);
        }

        .calculation-visual {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .calc-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: #f7fafc;
            border-radius: 8px;
            border-left: 3px solid #667eea;
        }

        .calc-item span {
            color: #4a5568;
            font-weight: 500;
        }

        .calc-formula {
            font-family: 'Courier New', monospace;
            color: #667eea;
            font-weight: bold;
            background: white;
            padding: 5px 10px;
            border-radius: 5px;
            border: 1px solid #e2e8f0;
        }

        .final-calculation {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            color: white;
        }

        .calc-display {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            font-size: 1.3rem;
            font-weight: bold;
        }

        .fraction {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .fraction::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: white;
            transform: translateY(-50%);
        }

        .numerator, .denominator {
            padding: 5px 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 5px;
            margin: 2px 0;
        }

        .calc-result {
            color: #ffd700;
            font-size: 1.5rem;
        }

        .breakdown-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .step-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            color: #4a5568;
            font-weight: 500;
        }

        .step-btn:hover {
            border-color: #667eea;
            background: #f7fafc;
            transform: translateY(-2px);
        }

        .step-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .auto-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }

        .auto-btn:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 流水线加速比学习</h1>
            <p>通过动画和交互体验，轻松掌握计算机组成原理中的流水线概念</p>
        </div>

        <div class="section">
            <h2>📚 什么是流水线？</h2>
            <div class="explanation">
                <h3>🎯 核心概念</h3>
                <p><strong>流水线</strong>就像工厂的生产线一样，将一个复杂的任务分解成多个简单的步骤，每个步骤由专门的"工作站"负责。</p>
                <p>在计算机中，流水线将指令的执行过程分为多个阶段，每个阶段可以同时处理不同的指令，从而提高整体效率。</p>
            </div>
            
            <div class="pipeline-container">
                <div class="stage" data-stage="1">
                    <div>阶段1<div class="stage-time">Δt</div></div>
                </div>
                <div class="arrow"></div>
                <div class="stage" data-stage="2">
                    <div>阶段2<div class="stage-time">3Δt</div></div>
                </div>
                <div class="arrow"></div>
                <div class="stage" data-stage="3">
                    <div>阶段3<div class="stage-time">2Δt</div></div>
                </div>
                <div class="arrow"></div>
                <div class="stage" data-stage="4">
                    <div>阶段4<div class="stage-time">Δt</div></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎮 交互式演示</h2>
            <div class="control-panel">
                <button class="btn" onclick="startSequentialDemo()">🔄 顺序执行演示</button>
                <button class="btn" onclick="startPipelineDemo()">⚡ 流水线执行演示</button>
                <button class="btn" onclick="showTimeline()">📊 时间轴对比</button>
                <button class="btn" onclick="resetDemo()">🔄 重置</button>
            </div>

            <div class="timeline-container" id="timelineContainer" style="display: none;">
                <h3>📈 执行时间轴对比</h3>
                <canvas id="timelineCanvas" width="800" height="300"></canvas>
                <div class="timeline-legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #ff6b6b;"></div>
                        <span>阶段1 (Δt)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #4ecdc4;"></div>
                        <span>阶段2 (3Δt)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #45b7d1;"></div>
                        <span>阶段3 (2Δt)</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #96ceb4;"></div>
                        <span>阶段4 (Δt)</span>
                    </div>
                </div>
            </div>
            
            <div class="canvas-container">
                <canvas id="demoCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="time-display" id="timeDisplay">点击按钮开始演示</div>
        </div>

        <div class="section">
            <h2>📊 加速比计算</h2>

            <!-- 新增：概念分解动画区域 -->
            <div class="concept-breakdown">
                <h3>🎯 概念分解学习</h3>
                <div class="breakdown-container">
                    <div class="concept-step" id="step1">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>理解基本概念</h4>
                            <p>加速比衡量流水线相对于顺序执行的性能提升</p>
                            <div class="concept-visual">
                                <div class="visual-item sequential">
                                    <span>顺序执行</span>
                                    <div class="progress-bar">
                                        <div class="progress sequential-progress"></div>
                                    </div>
                                </div>
                                <div class="visual-item pipeline">
                                    <span>流水线执行</span>
                                    <div class="progress-bar">
                                        <div class="progress pipeline-progress"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="concept-step" id="step2">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>计算顺序执行时间</h4>
                            <p>每个任务必须完整执行完所有阶段</p>
                            <div class="calculation-visual">
                                <div class="calc-item">
                                    <span>单任务时间</span>
                                    <div class="calc-formula">Δt + 3Δt + 2Δt + Δt = 7Δt</div>
                                </div>
                                <div class="calc-item">
                                    <span>4个任务总时间</span>
                                    <div class="calc-formula">4 × 7Δt = 28Δt</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="concept-step" id="step3">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>计算流水线执行时间</h4>
                            <p>任务可以在不同阶段并行处理</p>
                            <div class="calculation-visual">
                                <div class="calc-item">
                                    <span>第一个任务完成</span>
                                    <div class="calc-formula">7Δt</div>
                                </div>
                                <div class="calc-item">
                                    <span>后续任务间隔</span>
                                    <div class="calc-formula">3 × 3Δt = 9Δt</div>
                                </div>
                                <div class="calc-item">
                                    <span>总时间</span>
                                    <div class="calc-formula">7Δt + 9Δt = 16Δt</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="concept-step" id="step4">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4>计算最终加速比</h4>
                            <p>比较两种执行方式的时间差异</p>
                            <div class="final-calculation">
                                <div class="calc-display">
                                    <span class="calc-label">加速比 =</span>
                                    <div class="fraction">
                                        <div class="numerator">28Δt</div>
                                        <div class="denominator">16Δt</div>
                                    </div>
                                    <span class="calc-result">= 1.75</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="breakdown-controls">
                    <button class="step-btn" onclick="showStep(1)">概念理解</button>
                    <button class="step-btn" onclick="showStep(2)">顺序计算</button>
                    <button class="step-btn" onclick="showStep(3)">流水线计算</button>
                    <button class="step-btn" onclick="showStep(4)">最终结果</button>
                    <button class="step-btn auto-btn" onclick="autoPlaySteps()">🎬 自动播放</button>
                </div>
            </div>

            <div class="explanation">
                <h3>🧮 计算公式</h3>
                <p><strong>加速比 = 顺序执行时间 ÷ 流水线执行时间</strong></p>
            </div>

            <div class="formula">
                加速比 = (不使用流水线的时间) ÷ (使用流水线的时间)
            </div>

            <div class="explanation">
                <h3>📝 题目分析</h3>
                <p><strong>题目条件：</strong></p>
                <p>• 4级流水线：4个处理阶段</p>
                <p>• 每个阶段耗时：Δt, 3Δt, 2Δt, Δt</p>
                <p>• 每3Δt输入一个任务</p>
                <p>• 连续输入4个任务</p>
            </div>

            <div class="explanation">
                <h3>🔢 详细计算</h3>
                <p><strong>1. 顺序执行时间：</strong></p>
                <p>单个任务时间 = Δt + 3Δt + 2Δt + Δt = 7Δt</p>
                <p>4个任务总时间 = 4 × 7Δt = 28Δt</p>

                <p><strong>2. 流水线执行时间：</strong></p>
                <p>第一个任务完成时间 = 7Δt</p>
                <p>后续3个任务，每3Δt完成一个</p>
                <p>总时间 = 7Δt + 3 × 3Δt = 7Δt + 9Δt = 16Δt</p>
            </div>

            <div class="result-display" id="resultDisplay">
                加速比 = 28Δt ÷ 16Δt = 1.75
            </div>
        </div>

        <div class="section">
            <h2>🎯 互动练习</h2>
            <div class="explanation">
                <h3>🧠 测试你的理解</h3>
                <p>尝试回答以下问题，加深对流水线概念的理解：</p>
            </div>

            <div class="interactive-quiz">
                <div class="quiz-question">
                    <h4>问题1：如果改为每2Δt输入一个任务，加速比会是多少？</h4>
                    <div class="quiz-options">
                        <button class="quiz-btn" onclick="checkAnswer(1, 'A')">A. 2.0</button>
                        <button class="quiz-btn" onclick="checkAnswer(1, 'B')">B. 2.33</button>
                        <button class="quiz-btn" onclick="checkAnswer(1, 'C')">C. 1.75</button>
                        <button class="quiz-btn" onclick="checkAnswer(1, 'D')">D. 1.5</button>
                    </div>
                    <div class="quiz-result" id="result1"></div>
                </div>

                <div class="quiz-question">
                    <h4>问题2：流水线的主要优势是什么？</h4>
                    <div class="quiz-options">
                        <button class="quiz-btn" onclick="checkAnswer(2, 'A')">A. 减少单个任务的执行时间</button>
                        <button class="quiz-btn" onclick="checkAnswer(2, 'B')">B. 提高系统的吞吐量</button>
                        <button class="quiz-btn" onclick="checkAnswer(2, 'C')">C. 减少硬件成本</button>
                        <button class="quiz-btn" onclick="checkAnswer(2, 'D')">D. 简化程序设计</button>
                    </div>
                    <div class="quiz-result" id="result2"></div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔍 深入理解</h2>
            <div class="explanation">
                <h3>💡 关键概念解析</h3>
                <p><strong>1. 流水线深度：</strong>流水线包含的阶段数量，本题为4级</p>
                <p><strong>2. 流水线周期：</strong>相邻两个任务进入流水线的时间间隔，本题为3Δt</p>
                <p><strong>3. 流水线延迟：</strong>单个任务从开始到完成的总时间，本题为7Δt</p>
                <p><strong>4. 吞吐率：</strong>单位时间内完成的任务数量</p>
            </div>

            <div class="explanation">
                <h3>🚀 为什么流水线能提高性能？</h3>
                <p>流水线技术的核心思想是<strong>并行处理</strong>：</p>
                <p>• 当第一个任务在阶段2执行时，第二个任务可以在阶段1开始</p>
                <p>• 多个任务可以同时在不同阶段处理</p>
                <p>• 充分利用了硬件资源，避免了资源闲置</p>
            </div>

            <div class="explanation">
                <h3>⚠️ 流水线的限制</h3>
                <p><strong>1. 数据相关：</strong>后续指令依赖前面指令的结果</p>
                <p><strong>2. 控制相关：</strong>分支指令改变程序执行流程</p>
                <p><strong>3. 结构相关：</strong>硬件资源冲突</p>
                <p><strong>4. 流水线建立时间：</strong>需要一定时间才能达到最大效率</p>
            </div>
        </div>

        <div class="section">
            <h2>📈 性能对比总结</h2>
            <div class="speed-comparison">
                <div class="comparison-card">
                    <h4>🐌 顺序执行</h4>
                    <div class="comparison-time sequential-time">28Δt</div>
                    <p>每个任务必须等待前一个任务完全完成后才能开始</p>
                    <p><strong>特点：</strong>简单但效率低</p>
                </div>
                <div class="comparison-card">
                    <h4>⚡ 流水线执行</h4>
                    <div class="comparison-time pipeline-time">16Δt</div>
                    <p>多个任务可以在不同阶段同时进行</p>
                    <p><strong>特点：</strong>复杂但效率高</p>
                </div>
            </div>

            <div class="result-display">
                🎯 最终结果：加速比 = 28Δt ÷ 16Δt = 1.75
                <br>
                💡 流水线技术使处理速度提升了75%！
            </div>

            <div class="explanation">
                <h3>🎓 学习要点总结</h3>
                <p><strong>1. 流水线原理：</strong>将复杂任务分解为多个简单阶段，实现并行处理</p>
                <p><strong>2. 加速比计算：</strong>顺序执行时间除以流水线执行时间</p>
                <p><strong>3. 性能提升：</strong>通过充分利用硬件资源，显著提高系统吞吐量</p>
                <p><strong>4. 实际应用：</strong>现代CPU、GPU等处理器都广泛采用流水线技术</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('demoCanvas');
        const ctx = canvas.getContext('2d');
        const timeDisplay = document.getElementById('timeDisplay');
        
        let animationId;
        let currentTime = 0;
        let isAnimating = false;
        
        // 流水线阶段配置
        const stages = [
            { name: '阶段1', time: 1, color: '#ff6b6b' },
            { name: '阶段2', time: 3, color: '#4ecdc4' },
            { name: '阶段3', time: 2, color: '#45b7d1' },
            { name: '阶段4', time: 1, color: '#96ceb4' }
        ];
        
        const tasks = ['T1', 'T2', 'T3', 'T4'];
        
        function drawStage(x, y, width, height, stage, isActive = false) {
            ctx.fillStyle = isActive ? stage.color : '#e2e8f0';
            ctx.fillRect(x, y, width, height);
            
            // 绘制边框
            ctx.strokeStyle = '#cbd5e0';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            
            // 绘制文字
            ctx.fillStyle = isActive ? 'white' : '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(stage.name, x + width/2, y + height/2 - 5);
            ctx.fillText(`${stage.time}Δt`, x + width/2, y + height/2 + 10);
        }
        
        function drawTask(x, y, taskName, color = '#ff6b6b') {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 15, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(taskName, x, y + 4);
        }
        
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        function startSequentialDemo() {
            if (isAnimating) return;
            
            isAnimating = true;
            currentTime = 0;
            
            function animate() {
                clearCanvas();
                
                // 绘制流水线阶段
                for (let i = 0; i < 4; i++) {
                    drawStage(50 + i * 150, 100, 120, 60, stages[i]);
                }
                
                // 顺序执行逻辑
                let totalTime = 0;
                for (let taskIndex = 0; taskIndex < 4; taskIndex++) {
                    let taskStartTime = taskIndex * 7;
                    
                    if (currentTime >= taskStartTime && currentTime < taskStartTime + 7) {
                        // 当前任务正在执行
                        let taskProgress = currentTime - taskStartTime;
                        let currentStage = Math.floor(taskProgress);
                        
                        if (currentStage < 4) {
                            // 高亮当前阶段
                            drawStage(50 + currentStage * 150, 100, 120, 60, stages[currentStage], true);
                            
                            // 绘制任务
                            drawTask(110 + currentStage * 150, 130, tasks[taskIndex]);
                        }
                    }
                }
                
                timeDisplay.textContent = `顺序执行时间: ${currentTime}Δt / 28Δt`;
                
                currentTime += 0.1;
                
                if (currentTime < 28) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    isAnimating = false;
                    timeDisplay.textContent = `顺序执行完成！总时间: 28Δt`;
                }
            }
            
            animate();
        }
        
        function startPipelineDemo() {
            if (isAnimating) return;
            
            isAnimating = true;
            currentTime = 0;
            
            function animate() {
                clearCanvas();
                
                // 绘制流水线阶段
                for (let i = 0; i < 4; i++) {
                    drawStage(50 + i * 150, 100, 120, 60, stages[i]);
                }
                
                // 流水线执行逻辑
                const taskPositions = [];
                
                for (let taskIndex = 0; taskIndex < 4; taskIndex++) {
                    let taskStartTime = taskIndex * 3; // 每3Δt输入一个任务
                    
                    if (currentTime >= taskStartTime) {
                        let taskTime = currentTime - taskStartTime;
                        let cumulativeTime = 0;
                        
                        for (let stageIndex = 0; stageIndex < 4; stageIndex++) {
                            let stageTime = stages[stageIndex].time;
                            
                            if (taskTime >= cumulativeTime && taskTime < cumulativeTime + stageTime) {
                                // 任务在当前阶段
                                drawStage(50 + stageIndex * 150, 100, 120, 60, stages[stageIndex], true);
                                drawTask(110 + stageIndex * 150, 130, tasks[taskIndex]);
                                break;
                            }
                            
                            cumulativeTime += stageTime;
                        }
                    }
                }
                
                timeDisplay.textContent = `流水线执行时间: ${currentTime.toFixed(1)}Δt / 16Δt`;
                
                currentTime += 0.1;
                
                if (currentTime < 16) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    isAnimating = false;
                    timeDisplay.textContent = `流水线执行完成！总时间: 16Δt，加速比: 1.75`;
                }
            }
            
            animate();
        }
        
        function resetDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            isAnimating = false;
            currentTime = 0;
            clearCanvas();

            // 绘制初始状态
            for (let i = 0; i < 4; i++) {
                drawStage(50 + i * 150, 100, 120, 60, stages[i]);
            }

            timeDisplay.textContent = '点击按钮开始演示';

            // 隐藏时间轴
            document.getElementById('timelineContainer').style.display = 'none';
        }

        function showTimeline() {
            const timelineContainer = document.getElementById('timelineContainer');
            timelineContainer.style.display = 'block';

            const timelineCanvas = document.getElementById('timelineCanvas');
            const timelineCtx = timelineCanvas.getContext('2d');

            // 清空画布
            timelineCtx.clearRect(0, 0, timelineCanvas.width, timelineCanvas.height);

            // 绘制时间轴
            drawTimeline(timelineCtx);
        }

        function drawTimeline(ctx) {
            const startX = 50;
            const startY = 50;
            const timeUnit = 20; // 每个时间单位的像素宽度
            const taskHeight = 30;
            const taskSpacing = 40;

            // 绘制标题
            ctx.fillStyle = '#4a5568';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('顺序执行 (28Δt)', startX, startY - 10);
            ctx.fillText('流水线执行 (16Δt)', startX, startY + 150);

            // 绘制顺序执行时间轴
            for (let taskIndex = 0; taskIndex < 4; taskIndex++) {
                let currentX = startX;
                let currentY = startY + taskIndex * taskSpacing;

                // 绘制任务标签
                ctx.fillStyle = '#666';
                ctx.font = '14px Arial';
                ctx.textAlign = 'right';
                ctx.fillText(`T${taskIndex + 1}`, currentX - 10, currentY + taskHeight/2 + 5);

                // 绘制各阶段
                for (let stageIndex = 0; stageIndex < 4; stageIndex++) {
                    const stageWidth = stages[stageIndex].time * timeUnit;

                    ctx.fillStyle = stages[stageIndex].color;
                    ctx.fillRect(currentX, currentY, stageWidth, taskHeight);

                    // 绘制边框
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(currentX, currentY, stageWidth, taskHeight);

                    // 绘制阶段标签
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`S${stageIndex + 1}`, currentX + stageWidth/2, currentY + taskHeight/2 + 4);

                    currentX += stageWidth;
                }
            }

            // 绘制流水线执行时间轴
            const pipelineStartY = startY + 180;

            // 计算每个任务在流水线中的执行情况
            for (let taskIndex = 0; taskIndex < 4; taskIndex++) {
                let taskStartTime = taskIndex * 3; // 每3Δt输入一个任务
                let currentY = pipelineStartY + taskIndex * taskSpacing;

                // 绘制任务标签
                ctx.fillStyle = '#666';
                ctx.font = '14px Arial';
                ctx.textAlign = 'right';
                ctx.fillText(`T${taskIndex + 1}`, startX - 10, currentY + taskHeight/2 + 5);

                let currentTime = 0;
                let currentX = startX + taskStartTime * timeUnit;

                // 绘制各阶段
                for (let stageIndex = 0; stageIndex < 4; stageIndex++) {
                    const stageWidth = stages[stageIndex].time * timeUnit;

                    ctx.fillStyle = stages[stageIndex].color;
                    ctx.fillRect(currentX, currentY, stageWidth, taskHeight);

                    // 绘制边框
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 2;
                    ctx.strokeRect(currentX, currentY, stageWidth, taskHeight);

                    // 绘制阶段标签
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`S${stageIndex + 1}`, currentX + stageWidth/2, currentY + taskHeight/2 + 4);

                    currentX += stageWidth;
                    currentTime += stages[stageIndex].time;
                }
            }

            // 绘制时间刻度
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 1;
            ctx.fillStyle = '#666';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';

            for (let t = 0; t <= 28; t += 2) {
                const x = startX + t * timeUnit;

                // 顺序执行时间刻度
                ctx.beginPath();
                ctx.moveTo(x, startY - 5);
                ctx.lineTo(x, startY + 160);
                ctx.stroke();

                // 流水线执行时间刻度
                ctx.beginPath();
                ctx.moveTo(x, pipelineStartY - 5);
                ctx.lineTo(x, pipelineStartY + 160);
                ctx.stroke();

                // 时间标签
                if (t <= 16 || t % 4 === 0) {
                    ctx.fillText(`${t}Δt`, x, startY - 10);
                    ctx.fillText(`${t}Δt`, x, pipelineStartY + 170);
                }
            }
        }
        
        // 初始化画布
        resetDemo();
        
        // 添加交互效果
        document.querySelectorAll('.stage').forEach(stage => {
            stage.addEventListener('click', function() {
                const stageNum = this.dataset.stage;
                alert(`阶段${stageNum}：处理时间为${stages[stageNum-1].time}Δt`);
            });
        });

        // 测验功能
        const quizAnswers = {
            1: 'B', // 每2Δt输入，总时间为7+3*2=13Δt，加速比=28/13≈2.15，最接近B
            2: 'B'  // 流水线主要提高吞吐量
        };

        const quizExplanations = {
            1: {
                correct: '正确！当每2Δt输入一个任务时，流水线执行时间为7Δt + 3×2Δt = 13Δt，加速比 = 28Δt ÷ 13Δt ≈ 2.15，最接近2.33。',
                incorrect: '不正确。重新计算：流水线执行时间 = 第一个任务完成时间(7Δt) + 后续任务间隔时间(3×2Δt) = 13Δt，加速比 = 28Δt ÷ 13Δt ≈ 2.15。'
            },
            2: {
                correct: '正确！流水线的主要优势是提高系统的吞吐量，即单位时间内能处理更多的任务。',
                incorrect: '不正确。流水线并不能减少单个任务的执行时间，而是通过并行处理提高整体吞吐量。'
            }
        };

        function checkAnswer(questionNum, selectedAnswer) {
            const correctAnswer = quizAnswers[questionNum];
            const resultDiv = document.getElementById(`result${questionNum}`);
            const buttons = document.querySelectorAll(`#result${questionNum}`).parentNode.querySelectorAll('.quiz-btn');

            // 重置所有按钮样式
            document.querySelectorAll('.quiz-btn').forEach(btn => {
                btn.classList.remove('correct', 'incorrect');
            });

            // 找到当前问题的按钮
            const questionDiv = resultDiv.parentNode;
            const questionButtons = questionDiv.querySelectorAll('.quiz-btn');

            if (selectedAnswer === correctAnswer) {
                // 正确答案
                questionButtons.forEach(btn => {
                    if (btn.textContent.startsWith(selectedAnswer)) {
                        btn.classList.add('correct');
                    }
                });

                resultDiv.className = 'quiz-result show correct';
                resultDiv.textContent = quizExplanations[questionNum].correct;
            } else {
                // 错误答案
                questionButtons.forEach(btn => {
                    if (btn.textContent.startsWith(selectedAnswer)) {
                        btn.classList.add('incorrect');
                    }
                    if (btn.textContent.startsWith(correctAnswer)) {
                        btn.classList.add('correct');
                    }
                });

                resultDiv.className = 'quiz-result show incorrect';
                resultDiv.textContent = quizExplanations[questionNum].incorrect;
            }
        }

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case '1':
                    startSequentialDemo();
                    break;
                case '2':
                    startPipelineDemo();
                    break;
                case 'r':
                case 'R':
                    resetDemo();
                    break;
            }
        });

        // 新增：概念分解功能
        let currentStep = 0;
        let autoPlayInterval;

        function showStep(stepNumber) {
            // 重置所有步骤
            document.querySelectorAll('.concept-step').forEach(step => {
                step.classList.remove('active');
            });

            document.querySelectorAll('.step-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 激活当前步骤
            const currentStepElement = document.getElementById(`step${stepNumber}`);
            if (currentStepElement) {
                currentStepElement.classList.add('active');

                // 激活对应按钮
                const buttons = document.querySelectorAll('.step-btn');
                if (buttons[stepNumber - 1]) {
                    buttons[stepNumber - 1].classList.add('active');
                }

                // 添加特殊动画效果
                setTimeout(() => {
                    addStepAnimations(stepNumber);
                }, 300);
            }

            currentStep = stepNumber;
        }

        function addStepAnimations(stepNumber) {
            switch(stepNumber) {
                case 1:
                    // 进度条动画
                    setTimeout(() => {
                        document.querySelector('.sequential-progress').style.width = '100%';
                    }, 500);
                    setTimeout(() => {
                        document.querySelector('.pipeline-progress').style.width = '57%'; // 16/28 ≈ 0.57
                    }, 1000);
                    break;

                case 2:
                    // 计算项逐个显示
                    const calcItems2 = document.querySelectorAll('#step2 .calc-item');
                    calcItems2.forEach((item, index) => {
                        item.style.opacity = '0';
                        item.style.transform = 'translateX(-20px)';
                        setTimeout(() => {
                            item.style.transition = 'all 0.5s ease';
                            item.style.opacity = '1';
                            item.style.transform = 'translateX(0)';
                        }, index * 300);
                    });
                    break;

                case 3:
                    // 计算项逐个显示
                    const calcItems3 = document.querySelectorAll('#step3 .calc-item');
                    calcItems3.forEach((item, index) => {
                        item.style.opacity = '0';
                        item.style.transform = 'translateX(-20px)';
                        setTimeout(() => {
                            item.style.transition = 'all 0.5s ease';
                            item.style.opacity = '1';
                            item.style.transform = 'translateX(0)';
                        }, index * 400);
                    });
                    break;

                case 4:
                    // 最终结果动画
                    const finalCalc = document.querySelector('.final-calculation');
                    finalCalc.style.transform = 'scale(0.8)';
                    finalCalc.style.opacity = '0.8';
                    setTimeout(() => {
                        finalCalc.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                        finalCalc.style.transform = 'scale(1)';
                        finalCalc.style.opacity = '1';
                    }, 200);

                    // 结果数字动画
                    setTimeout(() => {
                        const result = document.querySelector('.calc-result');
                        result.style.animation = 'pulse 1s ease-in-out 3';
                    }, 800);
                    break;
            }
        }

        function autoPlaySteps() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
                document.querySelector('.auto-btn').textContent = '🎬 自动播放';
                return;
            }

            document.querySelector('.auto-btn').textContent = '⏸️ 暂停播放';
            let step = 1;

            showStep(step);

            autoPlayInterval = setInterval(() => {
                step++;
                if (step > 4) {
                    step = 1;
                }
                showStep(step);
            }, 3000);
        }

        // 初始化概念分解
        function initConceptBreakdown() {
            // 默认显示第一步
            showStep(1);

            // 重置进度条
            document.querySelector('.sequential-progress').style.width = '0';
            document.querySelector('.pipeline-progress').style.width = '0';

            // 重置计算项
            document.querySelectorAll('.calc-item').forEach(item => {
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            });
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            setTimeout(initConceptBreakdown, 500);
        });

        // 添加提示信息
        console.log('💡 快捷键提示：');
        console.log('按 1 键：开始顺序执行演示');
        console.log('按 2 键：开始流水线执行演示');
        console.log('按 R 键：重置演示');
        console.log('💡 新功能：概念分解学习，点击步骤按钮或使用自动播放！');
    </script>
</body>
</html>
