<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>竖式除法专项教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #2c3e50;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255,255,255,0.98);
            border-radius: 24px;
            padding: 50px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.08);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 10px;
            animation: fadeInDown 1s ease-out;
        }

        .subtitle {
            font-size: 1.1rem;
            color: #7f8c8d;
            margin-bottom: 30px;
        }

        .lesson-nav {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .nav-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .nav-btn:hover, .nav-btn.active {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .difficulty-selector {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .difficulty-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .difficulty-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
        }

        .difficulty-btn:hover, .difficulty-btn.active {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
        }

        .difficulty-btn.active {
            background: linear-gradient(45deg, #27ae60, #229954);
        }

        .achievement-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .achievement-panel h3 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .progress-bar {
            position: relative;
            background: rgba(255,255,255,0.2);
            border-radius: 25px;
            height: 30px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            height: 100%;
            border-radius: 25px;
            transition: width 0.5s ease;
            width: 0%;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .achievements {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .achievement-badge {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .achievement-badge.unlocked {
            background: linear-gradient(45deg, #f1c40f, #f39c12);
            box-shadow: 0 5px 15px rgba(241, 196, 15, 0.4);
            animation: glow 2s infinite alternate;
        }

        .achievement-badge.locked {
            background: rgba(255,255,255,0.2);
            color: rgba(255,255,255,0.5);
        }

        .achievement-badge:hover {
            transform: scale(1.1);
        }

        @keyframes glow {
            from { box-shadow: 0 5px 15px rgba(241, 196, 15, 0.4); }
            to { box-shadow: 0 5px 25px rgba(241, 196, 15, 0.8); }
        }

        .achievement-tooltip {
            position: absolute;
            bottom: 70px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .achievement-badge:hover .achievement-tooltip {
            opacity: 1;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .bounce {
            animation: bounce 1s ease;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .demo-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .demo-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 450px;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 16px;
            box-shadow:
                inset 0 2px 10px rgba(0,0,0,0.05),
                0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 25px;
            border: 1px solid rgba(0,0,0,0.05);
        }

        canvas {
            border-radius: 16px;
            cursor: pointer;
            width: 100%;
            height: 100%;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(45deg, #27ae60, #229954);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
        }

        .control-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .explanation {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .explanation h4 {
            color: #856404;
            margin-bottom: 10px;
        }

        .explanation p {
            color: #856404;
            line-height: 1.6;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }

        .step {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ecf0f1;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #3498db;
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #27ae60;
            color: white;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            animation: pulse 1s infinite;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 20px;
            }
            
            .title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">📐 竖式除法专项教学</h1>
            <p class="subtitle">从零开始，逐步掌握竖式除法的每一个步骤</p>
        </div>

        <div class="difficulty-selector">
            <h3 style="text-align: center; margin-bottom: 15px; color: #2c3e50;">🎯 选择难度等级</h3>
            <div class="difficulty-buttons">
                <button class="difficulty-btn active" onclick="switchDifficulty(0)">🟢 入门级</button>
                <button class="difficulty-btn" onclick="switchDifficulty(1)">🟡 基础级</button>
                <button class="difficulty-btn" onclick="switchDifficulty(2)">🟠 进阶级</button>
                <button class="difficulty-btn" onclick="switchDifficulty(3)">🔴 高级</button>
            </div>
        </div>

        <div class="lesson-nav" id="lessonNav">
            <!-- 课程按钮将根据难度动态生成 -->
        </div>

        <div class="main-content">
            <div class="demo-section">
                <h3 class="demo-title">📊 动画演示区</h3>
                <div class="canvas-container">
                    <canvas id="demoCanvas" width="500" height="380"></canvas>
                </div>
                <div class="controls">
                    <button class="control-btn" onclick="startDemo()">开始演示</button>
                    <button class="control-btn" onclick="nextStep()">下一步</button>
                    <button class="control-btn" onclick="prevStep()">上一步</button>
                    <button class="control-btn" onclick="resetDemo()">重新开始</button>
                </div>
            </div>

            <div class="demo-section">
                <h3 class="demo-title">🎯 互动练习区</h3>
                <div class="canvas-container">
                    <canvas id="practiceCanvas" width="500" height="380"></canvas>
                </div>
                <div class="controls">
                    <button class="control-btn" onclick="newProblem()">🎲 新题目</button>
                    <button class="control-btn" onclick="showHint()">💡 智能提示</button>
                    <button class="control-btn" onclick="startInteractiveMode()">✏️ 互动填空</button>
                    <button class="control-btn" onclick="checkAnswer()">✅ 检查答案</button>
                    <button class="control-btn" onclick="showSolution()">📖 查看解答</button>
                    <button class="control-btn" onclick="startDragMode()">🎯 拖拽练习</button>
                </div>
            </div>
        </div>

        <div class="step-indicator" id="stepIndicator">
            <!-- 步骤指示器将通过JavaScript生成 -->
        </div>

        <div class="explanation" id="explanation">
            <h4>💡 当前步骤说明</h4>
            <p id="explanationText">点击"开始演示"来学习竖式除法的第一步</p>
        </div>

        <div class="achievement-panel" id="achievementPanel">
            <h3>🏆 学习成就</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
                <span class="progress-text" id="progressText">0%</span>
            </div>
            <div class="achievements" id="achievements">
                <!-- 成就徽章将通过JavaScript生成 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentLesson = 0;
        let currentStep = 0;
        let currentDifficulty = 0;
        let isAnimating = false;
        let demoCanvas, practiceCanvas;
        let demoCtx, practiceCtx;

        // 成就系统变量
        let userProgress = {
            completedLessons: new Set(),
            practiceCount: 0,
            correctAnswers: 0,
            totalAnswers: 0,
            achievements: new Set()
        };

        // 成就定义
        const achievements = [
            { id: 'first_lesson', icon: '🎯', name: '初学者', description: '完成第一个课程' },
            { id: 'practice_master', icon: '💪', name: '练习达人', description: '完成10次练习' },
            { id: 'accuracy_expert', icon: '🎯', name: '精准专家', description: '答题正确率达到90%' },
            { id: 'difficulty_climber', icon: '🏔️', name: '难度攀登者', description: '解锁所有难度等级' },
            { id: 'speed_demon', icon: '⚡', name: '速度恶魔', description: '快速完成5个课程' },
            { id: 'perfectionist', icon: '💎', name: '完美主义者', description: '连续答对20题' },
            { id: 'explorer', icon: '🗺️', name: '探索者', description: '尝试所有除法类型' },
            { id: 'interactive_pro', icon: '🎮', name: '互动专家', description: '使用所有互动功能' }
        ];

        // 难度分级数据
        const difficultyLevels = [
            {
                name: "入门级",
                description: "适合初学者，从最基础的除法开始",
                lessons: [0, 1] // 两位数÷一位数, 三位数÷一位数
            },
            {
                name: "基础级",
                description: "掌握基本除法后的进阶练习",
                lessons: [2, 3, 4] // 有余数除法, 被除数有0, 商中有0
            },
            {
                name: "进阶级",
                description: "多位数除法，提升计算能力",
                lessons: [5, 6] // 两位数÷两位数, 三位数÷两位数
            },
            {
                name: "高级",
                description: "小数和分数除法，全面掌握",
                lessons: [7, 8] // 小数除法, 分数除法
            }
        ];

        // 课程数据
        const lessons = [
            {
                title: "两位数除以一位数",
                example: { dividend: 84, divisor: 4, quotient: 21, remainder: 0 },
                steps: [
                    "写出除法竖式框架",
                    "从最高位开始除",
                    "8÷4=2，写在十位上",
                    "2×4=8，写在8下面",
                    "8-8=0",
                    "把个位4移下来",
                    "4÷4=1，写在个位上",
                    "1×4=4，写在4下面",
                    "4-4=0，除法完成"
                ]
            },
            {
                title: "三位数除以一位数",
                example: { dividend: 246, divisor: 2, quotient: 123, remainder: 0 },
                steps: [
                    "写出除法竖式框架",
                    "从百位开始：2÷2=1",
                    "1×2=2，写在2下面",
                    "2-2=0",
                    "把十位4移下来",
                    "4÷2=2，写在十位上",
                    "2×2=4，写在4下面",
                    "4-4=0",
                    "把个位6移下来",
                    "6÷2=3，写在个位上",
                    "3×2=6，写在6下面",
                    "6-6=0，除法完成"
                ]
            },
            {
                title: "有余数的除法",
                example: { dividend: 85, divisor: 4, quotient: 21, remainder: 1 },
                steps: [
                    "写出除法竖式框架",
                    "8÷4=2，写在十位上",
                    "2×4=8，写在8下面",
                    "8-8=0",
                    "把个位5移下来",
                    "5÷4=1余1，写在个位上",
                    "1×4=4，写在5下面",
                    "5-4=1，余数是1"
                ]
            },
            {
                title: "被除数中有0",
                example: { dividend: 306, divisor: 3, quotient: 102, remainder: 0 },
                steps: [
                    "写出除法竖式框架",
                    "3÷3=1，写在百位上",
                    "1×3=3，写在3下面",
                    "3-3=0",
                    "十位是0，0÷3=0",
                    "在商的十位写0",
                    "把个位6移下来",
                    "6÷3=2，写在个位上",
                    "2×3=6，写在6下面",
                    "6-6=0，除法完成"
                ]
            },
            {
                title: "商中有0",
                example: { dividend: 618, divisor: 3, quotient: 206, remainder: 0 },
                steps: [
                    "写出除法竖式框架",
                    "6÷3=2，写在百位上",
                    "2×3=6，写在6下面",
                    "6-6=0",
                    "把十位1移下来",
                    "1÷3=0余1，商的十位写0",
                    "把个位8移下来，得18",
                    "18÷3=6，写在个位上",
                    "6×3=18，写在18下面",
                    "18-18=0，除法完成"
                ]
            },
            {
                title: "两位数除以两位数",
                example: { dividend: 84, divisor: 12, quotient: 7, remainder: 0 },
                steps: [
                    "写出除法竖式框架",
                    "看84能否被12整除",
                    "试商：8÷1=8，但8×12=96>84",
                    "试商：7×12=84，正好整除",
                    "商是7，写在个位上",
                    "7×12=84，写在84下面",
                    "84-84=0，除法完成"
                ]
            },
            {
                title: "三位数除以两位数",
                example: { dividend: 368, divisor: 23, quotient: 16, remainder: 0 },
                steps: [
                    "写出除法竖式框架",
                    "看36能否被23整除",
                    "36÷23=1余13，商的十位写1",
                    "1×23=23，写在36下面",
                    "36-23=13",
                    "把个位8移下来，得138",
                    "138÷23=6，写在个位上",
                    "6×23=138，写在138下面",
                    "138-138=0，除法完成"
                ]
            },
            {
                title: "小数除法",
                example: { dividend: 12.6, divisor: 3, quotient: 4.2, remainder: 0 },
                steps: [
                    "写出除法竖式框架",
                    "12÷3=4，写在个位上",
                    "3×4=12，写在12下面",
                    "12-12=0",
                    "小数点对齐，继续除",
                    "6÷3=2，写在小数点后",
                    "答案是4.2"
                ]
            },
            {
                title: "分数除法",
                example: { dividend: "3/4", divisor: 2, quotient: "3/8", remainder: 0 },
                steps: [
                    "分数除以整数的方法",
                    "3/4 ÷ 2 = 3/4 × 1/2",
                    "分子相乘：3×1=3",
                    "分母相乘：4×2=8",
                    "结果是3/8",
                    "检查是否需要约分",
                    "3/8已是最简分数"
                ]
            }
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            demoCanvas = document.getElementById('demoCanvas');
            practiceCanvas = document.getElementById('practiceCanvas');
            demoCtx = demoCanvas.getContext('2d');
            practiceCtx = practiceCanvas.getContext('2d');

            // 加载用户进度
            loadUserProgress();

            generateLessonButtons();
            initializeCanvases();
            createStepIndicator();
            updateExplanation();
            initializeAchievements();
            updateProgress();
        });

        // 加载用户进度
        function loadUserProgress() {
            const saved = localStorage.getItem('divisionLearningProgress');
            if (saved) {
                const data = JSON.parse(saved);
                userProgress.completedLessons = new Set(data.completedLessons || []);
                userProgress.practiceCount = data.practiceCount || 0;
                userProgress.correctAnswers = data.correctAnswers || 0;
                userProgress.totalAnswers = data.totalAnswers || 0;
                userProgress.achievements = new Set(data.achievements || []);
            }
        }

        // 保存用户进度
        function saveUserProgress() {
            const data = {
                completedLessons: Array.from(userProgress.completedLessons),
                practiceCount: userProgress.practiceCount,
                correctAnswers: userProgress.correctAnswers,
                totalAnswers: userProgress.totalAnswers,
                achievements: Array.from(userProgress.achievements)
            };
            localStorage.setItem('divisionLearningProgress', JSON.stringify(data));
        }

        // 初始化成就系统
        function initializeAchievements() {
            const achievementsContainer = document.getElementById('achievements');
            achievementsContainer.innerHTML = '';

            achievements.forEach(achievement => {
                const badge = document.createElement('div');
                badge.className = 'achievement-badge ' +
                    (userProgress.achievements.has(achievement.id) ? 'unlocked' : 'locked');
                badge.innerHTML = `
                    ${achievement.icon}
                    <div class="achievement-tooltip">
                        ${achievement.name}<br>
                        ${achievement.description}
                    </div>
                `;
                achievementsContainer.appendChild(badge);
            });
        }

        // 更新进度条
        function updateProgress() {
            const totalLessons = lessons.length;
            const completedCount = userProgress.completedLessons.size;
            const progressPercent = Math.round((completedCount / totalLessons) * 100);

            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            progressFill.style.width = progressPercent + '%';
            progressText.textContent = progressPercent + '%';
        }

        // 检查并解锁成就
        function checkAchievements() {
            let newAchievements = [];

            // 初学者成就
            if (userProgress.completedLessons.size >= 1 && !userProgress.achievements.has('first_lesson')) {
                userProgress.achievements.add('first_lesson');
                newAchievements.push('first_lesson');
            }

            // 练习达人成就
            if (userProgress.practiceCount >= 10 && !userProgress.achievements.has('practice_master')) {
                userProgress.achievements.add('practice_master');
                newAchievements.push('practice_master');
            }

            // 精准专家成就
            const accuracy = userProgress.totalAnswers > 0 ?
                (userProgress.correctAnswers / userProgress.totalAnswers) * 100 : 0;
            if (accuracy >= 90 && userProgress.totalAnswers >= 10 &&
                !userProgress.achievements.has('accuracy_expert')) {
                userProgress.achievements.add('accuracy_expert');
                newAchievements.push('accuracy_expert');
            }

            // 探索者成就
            if (userProgress.completedLessons.size >= 5 && !userProgress.achievements.has('explorer')) {
                userProgress.achievements.add('explorer');
                newAchievements.push('explorer');
            }

            // 显示新成就
            newAchievements.forEach(achievementId => {
                showAchievementNotification(achievementId);
            });

            // 更新显示
            initializeAchievements();
            saveUserProgress();
        }

        // 显示成就通知
        function showAchievementNotification(achievementId) {
            const achievement = achievements.find(a => a.id === achievementId);
            if (!achievement) return;

            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(45deg, #f1c40f, #f39c12);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(241, 196, 15, 0.4);
                z-index: 1000;
                animation: slideIn 0.5s ease;
                font-weight: bold;
            `;
            notification.innerHTML = `
                🏆 成就解锁！<br>
                ${achievement.icon} ${achievement.name}<br>
                <small>${achievement.description}</small>
            `;

            document.body.appendChild(notification);

            // 3秒后移除通知
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.5s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 500);
            }, 3000);
        }

        // 切换难度
        function switchDifficulty(difficultyIndex) {
            currentDifficulty = difficultyIndex;
            currentLesson = 0; // 重置到第一个课程
            currentStep = 0;

            // 更新难度按钮状态
            document.querySelectorAll('.difficulty-btn').forEach((btn, index) => {
                btn.classList.toggle('active', index === difficultyIndex);
            });

            // 重新生成课程按钮
            generateLessonButtons();

            // 重置画布和指示器
            initializeCanvases();
            createStepIndicator();
            updateExplanation();
        }

        // 动态生成课程按钮
        function generateLessonButtons() {
            const lessonNav = document.getElementById('lessonNav');
            lessonNav.innerHTML = '';

            const currentDifficultyLessons = difficultyLevels[currentDifficulty].lessons;

            currentDifficultyLessons.forEach((lessonIndex, buttonIndex) => {
                const button = document.createElement('button');
                button.className = 'nav-btn' + (buttonIndex === 0 ? ' active' : '');
                button.textContent = lessons[lessonIndex].title;
                button.onclick = () => switchLesson(buttonIndex);
                lessonNav.appendChild(button);
            });
        }

        // 初始化画布
        function initializeCanvases() {
            // 清空画布
            demoCtx.clearRect(0, 0, demoCanvas.width, demoCanvas.height);
            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);

            // 绘制初始状态
            drawWelcomeMessage(demoCtx, "点击开始演示学习竖式除法");
            drawWelcomeMessage(practiceCtx, "点击新题目开始练习");
        }

        // 绘制欢迎信息
        function drawWelcomeMessage(ctx, message) {
            const centerX = ctx.canvas.width / 2;
            const centerY = ctx.canvas.height / 2;

            // 清空画布
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            // 绘制渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 0, ctx.canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            // 绘制大图标
            ctx.font = '80px Microsoft YaHei';
            ctx.fillStyle = '#3498db';
            ctx.textAlign = 'center';
            ctx.fillText('📐', centerX, centerY - 40);

            // 绘制主要信息
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#2c3e50';
            ctx.fillText(message, centerX, centerY + 20);

            // 绘制副标题
            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText('让我们一起学习竖式除法吧！', centerX, centerY + 50);

            // 添加装饰性边框
            ctx.strokeStyle = '#bdc3c7';
            ctx.lineWidth = 2;
            ctx.setLineDash([10, 5]);
            ctx.strokeRect(30, 30, ctx.canvas.width - 60, ctx.canvas.height - 60);
            ctx.setLineDash([]);
        }

        // 切换课程
        function switchLesson(buttonIndex) {
            // 根据当前难度获取实际的课程索引
            const actualLessonIndex = difficultyLevels[currentDifficulty].lessons[buttonIndex];
            currentLesson = actualLessonIndex;
            currentStep = 0;

            // 更新导航按钮状态
            document.querySelectorAll('.nav-btn').forEach((btn, index) => {
                btn.classList.toggle('active', index === buttonIndex);
            });

            // 重置画布
            initializeCanvases();
            createStepIndicator();
            updateExplanation();
        }

        // 创建步骤指示器
        function createStepIndicator() {
            const indicator = document.getElementById('stepIndicator');
            indicator.innerHTML = '';

            const lesson = lessons[currentLesson];
            lesson.steps.forEach((step, index) => {
                const stepDiv = document.createElement('div');
                stepDiv.className = 'step';
                stepDiv.textContent = index + 1;
                stepDiv.title = step;
                indicator.appendChild(stepDiv);
            });

            updateStepIndicator();
        }

        // 更新步骤指示器
        function updateStepIndicator() {
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                step.classList.remove('active', 'completed');
                if (index < currentStep) {
                    step.classList.add('completed');
                } else if (index === currentStep) {
                    step.classList.add('active');
                }
            });
        }

        // 更新说明文字
        function updateExplanation() {
            const lesson = lessons[currentLesson];
            const explanationText = document.getElementById('explanationText');

            if (currentStep < lesson.steps.length) {
                explanationText.textContent = lesson.steps[currentStep];
            } else {
                explanationText.textContent = "恭喜！您已完成本课程的学习。";
            }
        }

        // 开始演示
        function startDemo() {
            currentStep = 0;
            isAnimating = true;
            demoCtx.clearRect(0, 0, demoCanvas.width, demoCanvas.height);

            const lesson = lessons[currentLesson];
            drawDivisionStep(demoCtx, lesson.example, currentStep);
            updateStepIndicator();
            updateExplanation();
        }

        // 下一步
        function nextStep() {
            const lesson = lessons[currentLesson];
            if (currentStep < lesson.steps.length - 1) {
                currentStep++;
                drawDivisionStep(demoCtx, lesson.example, currentStep);
                updateStepIndicator();
                updateExplanation();
                updateButtonStates();
            } else {
                // 课程完成
                userProgress.completedLessons.add(currentLesson);
                updateProgress();
                checkAchievements();
                saveUserProgress();
            }
        }

        // 上一步
        function prevStep() {
            if (currentStep > 0) {
                currentStep--;
                const lesson = lessons[currentLesson];
                drawDivisionStep(demoCtx, lesson.example, currentStep);
                updateStepIndicator();
                updateExplanation();
                updateButtonStates();
            }
        }

        // 更新按钮状态
        function updateButtonStates() {
            const lesson = lessons[currentLesson];
            const prevBtn = document.querySelector('.control-btn:nth-child(3)'); // 上一步按钮
            const nextBtn = document.querySelector('.control-btn:nth-child(2)'); // 下一步按钮

            if (prevBtn) {
                prevBtn.disabled = (currentStep <= 0);
            }
            if (nextBtn) {
                nextBtn.disabled = (currentStep >= lesson.steps.length - 1);
            }
        }

        // 重新开始
        function resetDemo() {
            currentStep = 0;
            demoCtx.clearRect(0, 0, demoCanvas.width, demoCanvas.height);
            drawWelcomeMessage(demoCtx, "点击开始演示学习竖式除法");
            updateStepIndicator();
            updateExplanation();
        }

        // 绘制除法步骤
        function drawDivisionStep(ctx, example, step) {
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            const { dividend, divisor, quotient, remainder } = example;
            const centerX = ctx.canvas.width / 2;
            const centerY = ctx.canvas.height / 2;

            // 根据当前课程类型绘制相应的步骤
            switch(currentLesson) {
                case 0: // 两位数除法
                    drawTwoDigitDivisionStep(ctx, example, step, centerX, centerY);
                    break;
                case 1: // 三位数除法
                    drawThreeDigitDivisionStep(ctx, example, step, centerX, centerY);
                    break;
                case 2: // 有余数除法
                    drawRemainderDivisionStep(ctx, example, step, centerX, centerY);
                    break;
                case 3: // 被除数有0
                    drawZeroDividendStep(ctx, example, step, centerX, centerY);
                    break;
                case 4: // 商中有0
                    drawZeroQuotientStep(ctx, example, step, centerX, centerY);
                    break;
                case 5: // 两位数除以两位数
                    drawTwoDigitByTwoDigitStep(ctx, example, step, centerX, centerY);
                    break;
                case 6: // 三位数除以两位数
                    drawThreeDigitByTwoDigitStep(ctx, example, step, centerX, centerY);
                    break;
                case 7: // 小数除法
                    drawDecimalDivisionStep(ctx, example, step, centerX, centerY);
                    break;
                case 8: // 分数除法
                    drawFractionDivisionStep(ctx, example, step, centerX, centerY);
                    break;
                default:
                    drawTwoDigitDivisionStep(ctx, example, step, centerX, centerY);
            }
        }

        // 两位数除法详细步骤
        function drawTwoDigitDivisionStep(ctx, example, step, centerX, centerY) {
            // 清空画布
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            const startX = centerX - 100;
            const startY = centerY - 100;

            // 84 ÷ 4 的详细步骤
            switch(step) {
                case 0: // 框架
                    drawCleanFramework(ctx, 84, 4, startX, startY);
                    drawStepText(ctx, '第1步：写出除法竖式框架', centerX, startY + 200);
                    break;
                case 1: // 高亮第一位
                    drawCleanFramework(ctx, 84, 4, startX, startY);
                    highlightDigit(ctx, startX + 100, startY + 35, '8', '#ff6b6b');
                    drawStepText(ctx, '第2步：从最高位开始，看8能否被4整除', centerX, startY + 200);
                    break;
                case 2: // 第一步除法
                    drawCleanFramework(ctx, 84, 4, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 25, '2', '#27ae60');
                    drawStepText(ctx, '第3步：8 ÷ 4 = 2，在商的十位写2', centerX, startY + 200);
                    break;
                case 3: // 第一步乘法
                    drawCleanFramework(ctx, 84, 4, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 25, '2', '#27ae60');
                    drawCalculationLine(ctx, startX + 100, startY + 70, '8', '#3498db');
                    drawStepText(ctx, '第4步：2 × 4 = 8，写在被除数8的下面', centerX, startY + 200);
                    break;
                case 4: // 第一步减法
                    drawCleanFramework(ctx, 84, 4, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 25, '2', '#27ae60');
                    drawCalculationLine(ctx, startX + 100, startY + 70, '8', '#3498db');
                    drawSubtractionLine(ctx, startX + 90, startY + 90);
                    drawCalculationLine(ctx, startX + 100, startY + 110, '0', '#9b59b6');
                    drawStepText(ctx, '第5步：8 - 8 = 0', centerX, startY + 200);
                    break;
                case 5: // 移下来个位
                    drawCleanFramework(ctx, 84, 4, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 25, '2', '#27ae60');
                    drawCalculationLine(ctx, startX + 100, startY + 70, '8', '#3498db');
                    drawSubtractionLine(ctx, startX + 90, startY + 90);
                    drawCalculationLine(ctx, startX + 115, startY + 110, '4', '#e67e22');
                    drawArrowDown(ctx, startX + 130, startY + 45, startX + 125, startY + 100);
                    drawStepText(ctx, '第6步：把个位数字4移下来', centerX, startY + 200);
                    break;
                case 6: // 第二步除法
                    drawCleanFramework(ctx, 84, 4, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 25, '2', '#27ae60');
                    drawQuotientDigit(ctx, startX + 130, startY - 25, '1', '#27ae60');
                    drawCalculationLine(ctx, startX + 100, startY + 70, '8', '#3498db');
                    drawSubtractionLine(ctx, startX + 90, startY + 90);
                    drawCalculationLine(ctx, startX + 115, startY + 110, '4', '#e67e22');
                    drawStepText(ctx, '第7步：4 ÷ 4 = 1，在商的个位写1', centerX, startY + 200);
                    break;
                case 7: // 第二步乘法
                    drawCleanFramework(ctx, 84, 4, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 25, '2', '#27ae60');
                    drawQuotientDigit(ctx, startX + 130, startY - 25, '1', '#27ae60');
                    drawCalculationLine(ctx, startX + 100, startY + 70, '8', '#3498db');
                    drawSubtractionLine(ctx, startX + 90, startY + 90);
                    drawCalculationLine(ctx, startX + 115, startY + 110, '4', '#e67e22');
                    drawCalculationLine(ctx, startX + 115, startY + 130, '4', '#3498db');
                    drawStepText(ctx, '第8步：1 × 4 = 4，写在4的下面', centerX, startY + 200);
                    break;
                case 8: // 最终结果
                    drawCleanFramework(ctx, 84, 4, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 25, '2', '#27ae60');
                    drawQuotientDigit(ctx, startX + 130, startY - 25, '1', '#27ae60');
                    drawCalculationLine(ctx, startX + 100, startY + 70, '8', '#3498db');
                    drawSubtractionLine(ctx, startX + 90, startY + 90);
                    drawCalculationLine(ctx, startX + 115, startY + 110, '4', '#e67e22');
                    drawCalculationLine(ctx, startX + 115, startY + 130, '4', '#3498db');
                    drawSubtractionLine(ctx, startX + 110, startY + 150);
                    drawCalculationLine(ctx, startX + 115, startY + 170, '0', '#9b59b6');

                    // 最终答案 - 移到右侧避免重叠
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 24px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText('✓ 答案：21', startX + 200, startY + 80);
                    drawStepText(ctx, '第9步：4 - 4 = 0，除法计算完成！', centerX, startY + 200);
                    break;
            }
        }

        // 清晰的除法框架 - 避免重叠
        function drawCleanFramework(ctx, dividend, divisor, startX, startY) {
            // 绘制除号框架
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;

            // 横线
            ctx.beginPath();
            ctx.moveTo(startX + 80, startY);
            ctx.lineTo(startX + 180, startY);
            ctx.stroke();

            // 竖线
            ctx.beginPath();
            ctx.moveTo(startX + 80, startY);
            ctx.lineTo(startX + 80, startY + 180);
            ctx.stroke();

            // 写除数
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(divisor.toString(), startX + 40, startY + 35);

            // 写被除数 - 分开显示每位数字
            const dividendStr = dividend.toString();
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 28px Microsoft YaHei';

            for (let i = 0; i < dividendStr.length; i++) {
                ctx.fillText(dividendStr[i], startX + 100 + i * 30, startY + 35);
            }

            // 添加标签 - 位置调整避免重叠
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText('除数', startX + 40, startY - 20);
            ctx.fillText('被除数', startX + 115, startY - 20);
            ctx.fillText('商', startX + 115, startY - 40);
        }

        // 增强的除法框架 - 保持兼容性
        function drawEnhancedFramework(ctx, dividend, divisor, startX, startY) {
            drawCleanFramework(ctx, dividend, divisor, startX, startY);
        }

        // 小数除法框架
        function drawDecimalFramework(ctx, dividend, divisor, startX, startY) {
            // 绘制除号框架
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;

            // 横线
            ctx.beginPath();
            ctx.moveTo(startX + 80, startY);
            ctx.lineTo(startX + 200, startY);
            ctx.stroke();

            // 竖线
            ctx.beginPath();
            ctx.moveTo(startX + 80, startY);
            ctx.lineTo(startX + 80, startY + 180);
            ctx.stroke();

            // 写除数
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(divisor.toString(), startX + 40, startY + 35);

            // 写被除数（小数）
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(dividend.toString(), startX + 130, startY + 35);

            // 添加标签
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText('除数', startX + 40, startY - 20);
            ctx.fillText('被除数', startX + 130, startY - 20);
            ctx.fillText('商', startX + 130, startY - 40);
        }

        // 分数问题显示
        function drawFractionProblem(ctx, dividend, divisor, startX, startY) {
            // 绘制分数除法问题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 32px Microsoft YaHei';
            ctx.textAlign = 'center';

            // 绘制分数
            const parts = dividend.split('/');
            ctx.fillText(parts[0], startX + 100, startY + 20);
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(startX + 80, startY + 30);
            ctx.lineTo(startX + 120, startY + 30);
            ctx.stroke();
            ctx.fillText(parts[1], startX + 100, startY + 55);

            // 除号
            ctx.fillText('÷', startX + 150, startY + 35);

            // 整数
            ctx.fillText(divisor.toString(), startX + 200, startY + 35);

            // 等号
            ctx.fillText('=', startX + 250, startY + 35);

            // 添加标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#3498db';
            ctx.fillText('分数除法', startX + 150, startY - 20);
        }

        // 高亮数字 - 优化间距
        function highlightDigit(ctx, x, y, digit, color) {
            // 绘制高亮背景
            ctx.fillStyle = color + '30'; // 更透明
            ctx.fillRect(x - 18, y - 28, 36, 40);

            // 绘制数字
            ctx.fillStyle = color;
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(digit, x, y);

            // 添加边框效果
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.strokeRect(x - 18, y - 28, 36, 40);
        }

        // 绘制商的数字 - 统一大小
        function drawQuotientDigit(ctx, x, y, digit, color) {
            ctx.fillStyle = color;
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(digit, x, y);
        }

        // 绘制计算行 - 统一大小
        function drawCalculationLine(ctx, x, y, text, color) {
            ctx.fillStyle = color;
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        // 绘制减法线 - 调整长度
        function drawSubtractionLine(ctx, x, y) {
            ctx.strokeStyle = '#34495e';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x, y);
            ctx.lineTo(x + 50, y);
            ctx.stroke();
        }

        // 绘制向下箭头 - 优化样式
        function drawArrowDown(ctx, fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#e67e22';
            ctx.lineWidth = 2;

            // 箭头线
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 6, toY - 10);
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX + 6, toY - 10);
            ctx.stroke();
        }

        // 绘制步骤说明文字 - 优化字体
        function drawStepText(ctx, text, x, y) {
            // 添加背景
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            const textWidth = ctx.measureText(text).width;
            ctx.fillRect(x - textWidth/2 - 10, y - 25, textWidth + 20, 35);

            // 绘制边框
            ctx.strokeStyle = '#ecf0f1';
            ctx.lineWidth = 1;
            ctx.strokeRect(x - textWidth/2 - 10, y - 25, textWidth + 20, 35);

            // 绘制文字
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y - 5);
        }

        // 三位数除法步骤
        function drawThreeDigitDivisionStep(ctx, example, step, centerX, centerY) {
            const startX = centerX - 140;
            const startY = centerY - 80;

            // 246 ÷ 2 的步骤
            switch(step) {
                case 0:
                    drawEnhancedFramework(ctx, 246, 2, startX, startY);
                    break;
                case 1:
                    drawEnhancedFramework(ctx, 246, 2, startX, startY);
                    highlightDigit(ctx, startX + 100, startY + 35, '2', '#ff6b6b');
                    drawStepText(ctx, '从百位开始：2', centerX, startY + 180);
                    break;
                case 2:
                    drawEnhancedFramework(ctx, 246, 2, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '1', '#27ae60');
                    drawStepText(ctx, '2 ÷ 2 = 1，写在百位上', centerX, startY + 180);
                    break;
                // ... 继续其他步骤
                default:
                    drawEnhancedFramework(ctx, 246, 2, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '1', '#27ae60');
                    drawQuotientDigit(ctx, startX + 130, startY - 20, '2', '#27ae60');
                    drawQuotientDigit(ctx, startX + 160, startY - 20, '3', '#27ae60');
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 28px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('答案：123', centerX + 100, startY + 60);
                    drawStepText(ctx, '三位数除法完成！', centerX, startY + 180);
            }
        }

        // 有余数除法步骤
        function drawRemainderDivisionStep(ctx, example, step, centerX, centerY) {
            const startX = centerX - 120;
            const startY = centerY - 80;

            // 85 ÷ 4 的步骤
            switch(step) {
                case 0:
                    drawEnhancedFramework(ctx, 85, 4, startX, startY);
                    break;
                case 1:
                    drawEnhancedFramework(ctx, 85, 4, startX, startY);
                    highlightDigit(ctx, startX + 100, startY + 35, '8', '#ff6b6b');
                    drawStepText(ctx, '8 ÷ 4 = 2', centerX, startY + 180);
                    break;
                // ... 其他步骤
                case 7:
                    drawEnhancedFramework(ctx, 85, 4, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '2', '#27ae60');
                    drawQuotientDigit(ctx, startX + 115, startY - 20, '1', '#27ae60');

                    // 显示余数
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 28px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('答案：21 余 1', centerX + 80, startY + 60);
                    drawStepText(ctx, '5 - 4 = 1，余数是1', centerX, startY + 180);
                    break;
                default:
                    drawEnhancedFramework(ctx, 85, 4, startX, startY);
                    drawStepText(ctx, '有余数的除法', centerX, startY + 180);
            }
        }

        // 被除数有0的步骤
        function drawZeroDividendStep(ctx, example, step, centerX, centerY) {
            const startX = centerX - 140;
            const startY = centerY - 80;

            switch(step) {
                case 0:
                    drawEnhancedFramework(ctx, 306, 3, startX, startY);
                    break;
                case 4:
                    drawEnhancedFramework(ctx, 306, 3, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '1', '#27ae60');
                    highlightDigit(ctx, startX + 130, startY + 35, '0', '#ff6b6b');
                    drawStepText(ctx, '十位是0，0 ÷ 3 = 0', centerX, startY + 180);
                    break;
                case 5:
                    drawEnhancedFramework(ctx, 306, 3, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '1', '#27ae60');
                    drawQuotientDigit(ctx, startX + 130, startY - 20, '0', '#f39c12');
                    drawStepText(ctx, '在商的十位写0', centerX, startY + 180);
                    break;
                default:
                    drawEnhancedFramework(ctx, 306, 3, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '1', '#27ae60');
                    drawQuotientDigit(ctx, startX + 130, startY - 20, '0', '#f39c12');
                    drawQuotientDigit(ctx, startX + 160, startY - 20, '2', '#27ae60');
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 28px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('答案：102', centerX + 100, startY + 60);
                    drawStepText(ctx, '被除数有0的除法完成！', centerX, startY + 180);
            }
        }

        // 商中有0的步骤
        function drawZeroQuotientStep(ctx, example, step, centerX, centerY) {
            const startX = centerX - 140;
            const startY = centerY - 80;

            switch(step) {
                case 0:
                    drawEnhancedFramework(ctx, 618, 3, startX, startY);
                    break;
                case 5:
                    drawEnhancedFramework(ctx, 618, 3, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '2', '#27ae60');
                    highlightDigit(ctx, startX + 130, startY + 35, '1', '#ff6b6b');
                    drawStepText(ctx, '1 ÷ 3 = 0 余 1，商的十位写0', centerX, startY + 180);
                    break;
                default:
                    drawEnhancedFramework(ctx, 618, 3, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '2', '#27ae60');
                    drawQuotientDigit(ctx, startX + 130, startY - 20, '0', '#f39c12');
                    drawQuotientDigit(ctx, startX + 160, startY - 20, '6', '#27ae60');
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 28px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('答案：206', centerX + 100, startY + 60);
                    drawStepText(ctx, '商中有0的除法完成！', centerX, startY + 180);
            }
        }

        // 高亮第一位数字
        function highlightFirstDigit(ctx, dividend, centerX, centerY) {
            const startX = centerX - 80;
            const startY = centerY - 60;

            // 高亮第一位
            ctx.fillStyle = 'rgba(231, 76, 60, 0.3)';
            ctx.fillRect(startX + 85, startY + 5, 25, 30);

            // 添加箭头和说明
            ctx.fillStyle = '#e74c3c';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('从最高位开始', startX + 110, startY + 60);

            // 绘制箭头
            drawArrow(ctx, startX + 97, startY + 45, startX + 97, startY + 35);
        }

        // 绘制第一个商的数字
        function drawFirstQuotientDigit(ctx, dividend, divisor, centerX, centerY) {
            const startX = centerX - 80;
            const startY = centerY - 60;

            const firstDigit = Math.floor(dividend / Math.pow(10, dividend.toString().length - 1));
            const quotientDigit = Math.floor(firstDigit / divisor);

            // 写商的第一位
            ctx.fillStyle = '#27ae60';
            ctx.font = '28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(quotientDigit.toString(), startX + 97, startY - 15);

            // 添加计算过程
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#27ae60';
            ctx.fillText(`${firstDigit} ÷ ${divisor} = ${quotientDigit}`, startX + 110, startY + 80);
        }

        // 绘制完整步骤
        function drawCompleteStep(ctx, example, step, centerX, centerY) {
            drawDivisionFramework(ctx, example.dividend, example.divisor, centerX, centerY);

            // 根据当前课程和步骤绘制相应内容
            const lesson = lessons[currentLesson];

            // 这里可以根据不同的步骤绘制更详细的计算过程
            ctx.fillStyle = '#27ae60';
            ctx.font = '20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(`步骤 ${step + 1}`, centerX, centerY + 100);

            // 如果是最后一步，显示最终答案
            if (step === lesson.steps.length - 1) {
                ctx.fillStyle = '#e74c3c';
                ctx.font = '24px Microsoft YaHei';
                const answerText = example.remainder > 0
                    ? `答案: ${example.quotient} 余 ${example.remainder}`
                    : `答案: ${example.quotient}`;
                ctx.fillText(answerText, centerX, centerY + 130);
            }
        }

        // 绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#e74c3c';
            ctx.lineWidth = 2;

            // 箭头线
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const arrowLength = 8;

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle - Math.PI/6),
                      toY - arrowLength * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle + Math.PI/6),
                      toY - arrowLength * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        // 练习模式变量
        let currentPractice = null;
        let practiceStep = 0;
        let showingHint = false;

        // 生成新题目
        function newProblem() {
            practiceStep = 0;
            showingHint = false;

            // 根据当前课程生成相应难度的题目
            currentPractice = generatePracticeProblems()[currentLesson];

            // 增加练习计数
            userProgress.practiceCount++;
            saveUserProgress();

            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);
            drawPracticeProblem(practiceCtx, currentPractice);
        }

        // 生成练习题
        function generatePracticeProblems() {
            return [
                { dividend: 96, divisor: 3, quotient: 32, remainder: 0 }, // 两位数
                { dividend: 369, divisor: 3, quotient: 123, remainder: 0 }, // 三位数
                { dividend: 97, divisor: 4, quotient: 24, remainder: 1 }, // 有余数
                { dividend: 408, divisor: 4, quotient: 102, remainder: 0 }, // 被除数有0
                { dividend: 824, divisor: 4, quotient: 206, remainder: 0 }, // 商中有0
                { dividend: 96, divisor: 12, quotient: 8, remainder: 0 }, // 两位数÷两位数
                { dividend: 456, divisor: 24, quotient: 19, remainder: 0 }, // 三位数÷两位数
                { dividend: 12.6, divisor: 3, quotient: 4.2, remainder: 0 }, // 小数除法
                { dividend: "3/4", divisor: 2, quotient: "3/8", remainder: 0 } // 分数除法
            ];
        }

        // 绘制练习题目
        function drawPracticeProblem(ctx, problem) {
            const centerX = ctx.canvas.width / 2;
            const centerY = ctx.canvas.height / 2;

            // 清空画布
            ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

            // 绘制美化的标题
            ctx.fillStyle = '#3498db';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎯 练习题目', centerX, centerY - 120);

            // 绘制增强的除法框架
            const startX = centerX - 120;
            const startY = centerY - 60;
            drawEnhancedFramework(ctx, problem.dividend, problem.divisor, startX, startY);

            // 添加美化的提示文字
            ctx.fillStyle = '#7f8c8d';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillText('💡 请计算上面的除法题', centerX, centerY + 100);

            ctx.fillStyle = '#95a5a6';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('点击下方按钮获得帮助或检查答案', centerX, centerY + 125);

            // 添加装饰性边框
            ctx.strokeStyle = '#ecf0f1';
            ctx.lineWidth = 2;
            ctx.strokeRect(20, 20, ctx.canvas.width - 40, ctx.canvas.height - 40);
        }

        // 显示提示
        function showHint() {
            if (!currentPractice) {
                alert('请先点击"新题目"生成练习题');
                return;
            }

            showingHint = true;
            const centerX = practiceCanvas.width / 2;
            const centerY = practiceCanvas.height / 2;

            // 清空并重绘
            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);
            drawPracticeProblem(practiceCtx, currentPractice);

            // 添加提示信息
            practiceCtx.fillStyle = '#f39c12';
            practiceCtx.font = '18px Microsoft YaHei';
            practiceCtx.textAlign = 'center';

            const lesson = lessons[currentLesson];
            const hintText = lesson.steps[0] + " → " + lesson.steps[1];
            practiceCtx.fillText('💡 ' + hintText, centerX, centerY + 120);
        }

        // 检查答案
        function checkAnswer() {
            if (!currentPractice) {
                alert('请先点击"新题目"生成练习题');
                return;
            }

            const userAnswer = prompt('请输入您的答案（如果有余数，格式为：商 余 余数）：');
            if (!userAnswer) return;

            const correctAnswer = currentPractice.remainder > 0
                ? `${currentPractice.quotient} 余 ${currentPractice.remainder}`
                : currentPractice.quotient.toString();

            const centerX = practiceCanvas.width / 2;
            const centerY = practiceCanvas.height / 2;

            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);
            drawPracticeProblem(practiceCtx, currentPractice);

            // 更新答题统计
            userProgress.totalAnswers++;

            if (userAnswer.trim() === correctAnswer) {
                userProgress.correctAnswers++;
                practiceCtx.fillStyle = '#27ae60';
                practiceCtx.font = '20px Microsoft YaHei';
                practiceCtx.textAlign = 'center';
                practiceCtx.fillText('🎉 答案正确！', centerX, centerY + 120);
                practiceCtx.fillText('您真棒！', centerX, centerY + 145);

                // 添加庆祝动画
                showCelebrationAnimation();
            } else {
                practiceCtx.fillStyle = '#e74c3c';
                practiceCtx.font = '18px Microsoft YaHei';
                practiceCtx.textAlign = 'center';
                practiceCtx.fillText('❌ 答案不正确', centerX, centerY + 120);
                practiceCtx.fillText(`正确答案是：${correctAnswer}`, centerX, centerY + 145);
            }

            // 检查成就并保存进度
            checkAchievements();
            saveUserProgress();
        }

        // 庆祝动画
        function showCelebrationAnimation() {
            // 创建彩色粒子效果
            const particles = [];
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: practiceCanvas.width / 2,
                    y: practiceCanvas.height / 2,
                    vx: (Math.random() - 0.5) * 10,
                    vy: (Math.random() - 0.5) * 10,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`,
                    life: 1.0
                });
            }

            function animateParticles() {
                practiceCtx.save();
                particles.forEach((particle, index) => {
                    if (particle.life <= 0) {
                        particles.splice(index, 1);
                        return;
                    }

                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    particle.vy += 0.2; // 重力
                    particle.life -= 0.02;

                    practiceCtx.globalAlpha = particle.life;
                    practiceCtx.fillStyle = particle.color;
                    practiceCtx.beginPath();
                    practiceCtx.arc(particle.x, particle.y, 3, 0, Math.PI * 2);
                    practiceCtx.fill();
                });
                practiceCtx.restore();

                if (particles.length > 0) {
                    requestAnimationFrame(animateParticles);
                }
            }

            animateParticles();
        }

        // 显示完整解答
        function showSolution() {
            if (!currentPractice) {
                alert('请先点击"新题目"生成练习题');
                return;
            }

            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);

            const centerX = practiceCanvas.width / 2;
            const centerY = practiceCanvas.height / 2;

            // 绘制完整的解答过程
            drawDivisionFramework(practiceCtx, currentPractice.dividend, currentPractice.divisor, centerX, centerY - 40);

            // 显示答案
            practiceCtx.fillStyle = '#27ae60';
            practiceCtx.font = '24px Microsoft YaHei';
            practiceCtx.textAlign = 'center';

            const answerText = currentPractice.remainder > 0
                ? `答案: ${currentPractice.quotient} 余 ${currentPractice.remainder}`
                : `答案: ${currentPractice.quotient}`;
            practiceCtx.fillText(answerText, centerX, centerY + 60);

            // 显示解题步骤
            practiceCtx.fillStyle = '#7f8c8d';
            practiceCtx.font = '16px Microsoft YaHei';
            practiceCtx.fillText('详细步骤请查看左侧演示区', centerX, centerY + 90);
        }

        // 两位数除以两位数步骤
        function drawTwoDigitByTwoDigitStep(ctx, example, step, centerX, centerY) {
            const startX = centerX - 120;
            const startY = centerY - 80;

            // 84 ÷ 12 的步骤
            switch(step) {
                case 0:
                    drawEnhancedFramework(ctx, 84, 12, startX, startY);
                    drawStepText(ctx, '写出除法竖式框架', centerX, startY + 180);
                    break;
                case 1:
                    drawEnhancedFramework(ctx, 84, 12, startX, startY);
                    highlightDigit(ctx, startX + 100, startY + 35, '8', '#ff6b6b');
                    highlightDigit(ctx, startX + 130, startY + 35, '4', '#ff6b6b');
                    drawStepText(ctx, '看84能否被12整除', centerX, startY + 180);
                    break;
                case 2:
                    drawEnhancedFramework(ctx, 84, 12, startX, startY);
                    drawStepText(ctx, '试商：8÷1=8，但8×12=96>84', centerX, startY + 180);
                    break;
                case 3:
                    drawEnhancedFramework(ctx, 84, 12, startX, startY);
                    drawStepText(ctx, '试商：7×12=84，正好整除', centerX, startY + 180);
                    break;
                case 4:
                    drawEnhancedFramework(ctx, 84, 12, startX, startY);
                    drawQuotientDigit(ctx, startX + 115, startY - 20, '7', '#27ae60');
                    drawStepText(ctx, '商是7，写在个位上', centerX, startY + 180);
                    break;
                case 5:
                    drawEnhancedFramework(ctx, 84, 12, startX, startY);
                    drawQuotientDigit(ctx, startX + 115, startY - 20, '7', '#27ae60');
                    drawCalculationLine(ctx, startX + 115, startY + 70, '84', '#3498db');
                    drawStepText(ctx, '7×12=84，写在84下面', centerX, startY + 180);
                    break;
                case 6:
                    drawEnhancedFramework(ctx, 84, 12, startX, startY);
                    drawQuotientDigit(ctx, startX + 115, startY - 20, '7', '#27ae60');
                    drawCalculationLine(ctx, startX + 115, startY + 70, '84', '#3498db');
                    drawSubtractionLine(ctx, startX + 100, startY + 90);
                    drawCalculationLine(ctx, startX + 115, startY + 110, '0', '#9b59b6');

                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 24px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('✓ 答案：7', startX + 200, startY + 80);
                    drawStepText(ctx, '84-84=0，除法完成！', centerX, startY + 180);
                    break;
                default:
                    drawEnhancedFramework(ctx, 84, 12, startX, startY);
                    drawStepText(ctx, '两位数除以两位数', centerX, startY + 180);
            }
        }

        // 三位数除以两位数步骤
        function drawThreeDigitByTwoDigitStep(ctx, example, step, centerX, centerY) {
            const startX = centerX - 140;
            const startY = centerY - 80;

            // 368 ÷ 23 的步骤
            switch(step) {
                case 0:
                    drawEnhancedFramework(ctx, 368, 23, startX, startY);
                    drawStepText(ctx, '写出除法竖式框架', centerX, startY + 180);
                    break;
                case 1:
                    drawEnhancedFramework(ctx, 368, 23, startX, startY);
                    highlightDigit(ctx, startX + 100, startY + 35, '3', '#ff6b6b');
                    highlightDigit(ctx, startX + 130, startY + 35, '6', '#ff6b6b');
                    drawStepText(ctx, '看36能否被23整除', centerX, startY + 180);
                    break;
                case 2:
                    drawEnhancedFramework(ctx, 368, 23, startX, startY);
                    drawQuotientDigit(ctx, startX + 115, startY - 20, '1', '#27ae60');
                    drawStepText(ctx, '36÷23=1余13，商的十位写1', centerX, startY + 180);
                    break;
                case 7:
                    drawEnhancedFramework(ctx, 368, 23, startX, startY);
                    drawQuotientDigit(ctx, startX + 115, startY - 20, '1', '#27ae60');
                    drawQuotientDigit(ctx, startX + 145, startY - 20, '6', '#27ae60');

                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 24px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('✓ 答案：16', startX + 220, startY + 80);
                    drawStepText(ctx, '138-138=0，除法完成！', centerX, startY + 180);
                    break;
                default:
                    drawEnhancedFramework(ctx, 368, 23, startX, startY);
                    drawStepText(ctx, '三位数除以两位数', centerX, startY + 180);
            }
        }

        // 小数除法步骤
        function drawDecimalDivisionStep(ctx, example, step, centerX, centerY) {
            const startX = centerX - 120;
            const startY = centerY - 80;

            switch(step) {
                case 0:
                    drawDecimalFramework(ctx, 12.6, 3, startX, startY);
                    drawStepText(ctx, '写出除法竖式框架', centerX, startY + 180);
                    break;
                case 1:
                    drawDecimalFramework(ctx, 12.6, 3, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '4', '#27ae60');
                    drawStepText(ctx, '12÷3=4，写在个位上', centerX, startY + 180);
                    break;
                case 6:
                    drawDecimalFramework(ctx, 12.6, 3, startX, startY);
                    drawQuotientDigit(ctx, startX + 100, startY - 20, '4', '#27ae60');
                    drawQuotientDigit(ctx, startX + 130, startY - 20, '.', '#2c3e50');
                    drawQuotientDigit(ctx, startX + 145, startY - 20, '2', '#27ae60');

                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 24px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('✓ 答案：4.2', startX + 200, startY + 80);
                    drawStepText(ctx, '答案是4.2', centerX, startY + 180);
                    break;
                default:
                    drawDecimalFramework(ctx, 12.6, 3, startX, startY);
                    drawStepText(ctx, '小数除法', centerX, startY + 180);
            }
        }

        // 分数除法步骤
        function drawFractionDivisionStep(ctx, example, step, centerX, centerY) {
            const startX = centerX - 120;
            const startY = centerY - 80;

            switch(step) {
                case 0:
                    drawFractionProblem(ctx, '3/4', 2, startX, startY);
                    drawStepText(ctx, '分数除以整数的方法', centerX, startY + 180);
                    break;
                case 1:
                    drawFractionProblem(ctx, '3/4', 2, startX, startY);
                    ctx.fillStyle = '#3498db';
                    ctx.font = 'bold 20px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('3/4 ÷ 2 = 3/4 × 1/2', centerX, startY + 100);
                    drawStepText(ctx, '3/4 ÷ 2 = 3/4 × 1/2', centerX, startY + 180);
                    break;
                case 6:
                    drawFractionProblem(ctx, '3/4', 2, startX, startY);
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = 'bold 28px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('✓ 答案：3/8', centerX, startY + 120);
                    drawStepText(ctx, '3/8已是最简分数', centerX, startY + 180);
                    break;
                default:
                    drawFractionProblem(ctx, '3/4', 2, startX, startY);
                    drawStepText(ctx, '分数除法', centerX, startY + 180);
            }
        }

        // 互动填空模式变量
        let interactiveMode = false;
        let dragMode = false;
        let currentInputStep = 0;
        let userInputs = [];
        let dragElements = [];

        // 启动互动填空模式
        function startInteractiveMode() {
            if (!currentPractice) {
                alert('请先点击"新题目"生成练习题');
                return;
            }

            interactiveMode = true;
            dragMode = false;
            currentInputStep = 0;
            userInputs = [];

            // 清空画布并准备互动模式
            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);

            // 绘制互动填空界面
            drawInteractiveMode();
        }

        // 绘制互动填空界面
        function drawInteractiveMode() {
            const centerX = practiceCanvas.width / 2;
            const centerY = practiceCanvas.height / 2;

            // 清空画布
            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);

            // 绘制标题
            practiceCtx.fillStyle = '#3498db';
            practiceCtx.font = 'bold 24px Microsoft YaHei';
            practiceCtx.textAlign = 'center';
            practiceCtx.fillText('✏️ 互动填空练习', centerX, 50);

            // 绘制当前问题
            const problem = currentPractice;
            const startX = centerX - 120;
            const startY = centerY - 60;

            // 绘制除法框架
            drawEnhancedFramework(practiceCtx, problem.dividend, problem.divisor, startX, startY);

            // 根据当前步骤显示填空提示
            const lesson = lessons[currentLesson];

            practiceCtx.fillStyle = '#e74c3c';
            practiceCtx.font = 'bold 18px Microsoft YaHei';
            practiceCtx.textAlign = 'center';

            if (currentInputStep < lesson.steps.length) {
                practiceCtx.fillText('请完成步骤 ' + (currentInputStep + 1) + ':', centerX, centerY + 100);
                practiceCtx.fillText(lesson.steps[currentInputStep], centerX, centerY + 130);

                // 添加输入框
                practiceCtx.strokeStyle = '#3498db';
                practiceCtx.lineWidth = 2;
                practiceCtx.strokeRect(centerX - 50, centerY + 150, 100, 30);

                // 显示用户已输入的内容
                if (userInputs[currentInputStep]) {
                    practiceCtx.fillStyle = '#2c3e50';
                    practiceCtx.font = '18px Microsoft YaHei';
                    practiceCtx.fillText(userInputs[currentInputStep], centerX, centerY + 170);
                }

                // 添加提交按钮
                practiceCtx.fillStyle = '#27ae60';
                practiceCtx.fillRect(centerX - 40, centerY + 190, 80, 30);
                practiceCtx.fillStyle = 'white';
                practiceCtx.font = '16px Microsoft YaHei';
                practiceCtx.fillText('提交', centerX, centerY + 210);
            } else {
                practiceCtx.fillStyle = '#27ae60';
                practiceCtx.font = 'bold 24px Microsoft YaHei';
                practiceCtx.fillText('恭喜！您已完成所有步骤！', centerX, centerY + 120);
            }
        }

        // 处理互动填空的点击事件
        practiceCanvas.addEventListener('click', function(e) {
            if (!interactiveMode) return;

            const rect = practiceCanvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = practiceCanvas.width / 2;
            const centerY = practiceCanvas.height / 2;

            // 检查是否点击了提交按钮
            if (x >= centerX - 40 && x <= centerX + 40 &&
                y >= centerY + 190 && y <= centerY + 220) {

                // 获取用户输入
                const userInput = prompt('请输入您的答案:');
                if (userInput) {
                    userInputs[currentInputStep] = userInput;

                    // 检查答案并前进到下一步
                    checkStepAnswer();
                }
            }
        });

        // 检查步骤答案
        function checkStepAnswer() {
            const lesson = lessons[currentLesson];

            // 这里可以添加更复杂的答案检查逻辑
            // 简单起见，我们只检查用户是否输入了内容

            if (userInputs[currentInputStep]) {
                // 显示反馈
                const centerX = practiceCanvas.width / 2;
                const centerY = practiceCanvas.height / 2;

                practiceCtx.fillStyle = '#27ae60';
                practiceCtx.font = 'bold 18px Microsoft YaHei';
                practiceCtx.textAlign = 'center';
                practiceCtx.fillText('✓ 步骤完成！', centerX, centerY + 250);

                // 延迟后进入下一步
                setTimeout(() => {
                    currentInputStep++;
                    drawInteractiveMode();
                }, 1000);
            }
        }

        // 启动拖拽练习模式
        function startDragMode() {
            if (!currentPractice) {
                alert('请先点击"新题目"生成练习题');
                return;
            }

            dragMode = true;
            interactiveMode = false;

            // 清空画布并准备拖拽模式
            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);

            // 初始化拖拽元素
            initDragElements();

            // 绘制拖拽练习界面
            drawDragMode();
        }

        // 初始化拖拽元素
        function initDragElements() {
            dragElements = [];

            // 根据当前课程创建拖拽元素
            const problem = currentPractice;
            const quotientStr = problem.quotient.toString();

            // 创建商的每一位数字作为拖拽元素
            for (let i = 0; i < quotientStr.length; i++) {
                dragElements.push({
                    id: i,
                    text: quotientStr[i],
                    x: 50 + i * 60,
                    y: 300,
                    width: 40,
                    height: 40,
                    isDragging: false,
                    correctX: 100 + i * 30,
                    correctY: 80,
                    isPlaced: false
                });
            }

            // 打乱顺序
            dragElements.sort(() => Math.random() - 0.5);

            // 重新排列位置
            dragElements.forEach((elem, index) => {
                elem.x = 50 + index * 60;
            });
        }

        // 绘制拖拽模式
        function drawDragMode() {
            const centerX = practiceCanvas.width / 2;
            const centerY = practiceCanvas.height / 2;

            // 清空画布
            practiceCtx.clearRect(0, 0, practiceCanvas.width, practiceCanvas.height);

            // 绘制标题
            practiceCtx.fillStyle = '#3498db';
            practiceCtx.font = 'bold 24px Microsoft YaHei';
            practiceCtx.textAlign = 'center';
            practiceCtx.fillText('🎯 拖拽练习', centerX, 50);

            // 绘制当前问题
            const problem = currentPractice;
            const startX = centerX - 120;
            const startY = centerY - 60;

            // 绘制除法框架（不显示商）
            drawDragFramework(practiceCtx, problem.dividend, problem.divisor, startX, startY);

            // 绘制拖拽元素
            dragElements.forEach(elem => {
                // 绘制元素背景
                practiceCtx.fillStyle = elem.isPlaced ? '#27ae60' : '#3498db';
                practiceCtx.fillRect(elem.x, elem.y, elem.width, elem.height);

                // 绘制元素文本
                practiceCtx.fillStyle = 'white';
                practiceCtx.font = 'bold 20px Microsoft YaHei';
                practiceCtx.textAlign = 'center';
                practiceCtx.fillText(elem.text, elem.x + elem.width/2, elem.y + elem.height/2 + 7);
            });

            // 绘制指导文字
            practiceCtx.fillStyle = '#2c3e50';
            practiceCtx.font = '18px Microsoft YaHei';
            practiceCtx.textAlign = 'center';
            practiceCtx.fillText('拖动数字到正确位置组成商', centerX, centerY + 150);

            // 检查是否所有元素都放置正确
            const allPlaced = dragElements.every(elem => elem.isPlaced);
            if (allPlaced) {
                practiceCtx.fillStyle = '#27ae60';
                practiceCtx.font = 'bold 24px Microsoft YaHei';
                practiceCtx.fillText('✓ 恭喜！答案正确！', centerX, centerY + 200);
            }
        }

        // 拖拽框架（不显示商）
        function drawDragFramework(ctx, dividend, divisor, startX, startY) {
            // 绘制除号框架
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 3;

            // 横线
            ctx.beginPath();
            ctx.moveTo(startX + 80, startY);
            ctx.lineTo(startX + 180, startY);
            ctx.stroke();

            // 竖线
            ctx.beginPath();
            ctx.moveTo(startX + 80, startY);
            ctx.lineTo(startX + 80, startY + 180);
            ctx.stroke();

            // 写除数
            ctx.fillStyle = '#e74c3c';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(divisor.toString(), startX + 40, startY + 35);

            // 写被除数
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 28px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(dividend.toString(), startX + 130, startY + 35);

            // 添加标签
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillStyle = '#7f8c8d';
            ctx.fillText('除数', startX + 40, startY - 20);
            ctx.fillText('被除数', startX + 130, startY - 20);
            ctx.fillText('商', startX + 130, startY - 40);

            // 绘制商的放置区域
            const quotientStr = currentPractice.quotient.toString();
            for (let i = 0; i < quotientStr.length; i++) {
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 2;
                ctx.strokeRect(startX + 100 + i * 30 - 15, startY - 40, 30, 30);
            }
        }

        // 处理拖拽事件
        let isDragging = false;
        let draggedElemIndex = -1;

        practiceCanvas.addEventListener('mousedown', function(e) {
            if (!dragMode) return;

            const rect = practiceCanvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检查是否点击了拖拽元素
            dragElements.forEach((elem, index) => {
                if (x >= elem.x && x <= elem.x + elem.width &&
                    y >= elem.y && y <= elem.y + elem.height) {
                    isDragging = true;
                    draggedElemIndex = index;
                    elem.isDragging = true;
                }
            });
        });

        practiceCanvas.addEventListener('mousemove', function(e) {
            if (!dragMode || !isDragging || draggedElemIndex === -1) return;

            const rect = practiceCanvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 更新拖拽元素位置
            dragElements[draggedElemIndex].x = x - dragElements[draggedElemIndex].width/2;
            dragElements[draggedElemIndex].y = y - dragElements[draggedElemIndex].height/2;

            // 重绘
            drawDragMode();
        });

        practiceCanvas.addEventListener('mouseup', function(e) {
            if (!dragMode || !isDragging || draggedElemIndex === -1) return;

            const elem = dragElements[draggedElemIndex];

            // 检查是否放置在正确位置
            if (Math.abs(elem.x - elem.correctX) < 30 && Math.abs(elem.y - elem.correctY) < 30) {
                // 放置正确
                elem.x = elem.correctX;
                elem.y = elem.correctY;
                elem.isPlaced = true;
            }

            // 重置拖拽状态
            isDragging = false;
            elem.isDragging = false;
            draggedElemIndex = -1;

            // 重绘
            drawDragMode();
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextStep();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    prevStep();
                    break;
                case 'r':
                case 'R':
                    resetDemo();
                    break;
                case 'n':
                case 'N':
                    newProblem();
                    break;
            }
        });
    </script>
</body>
</html>
