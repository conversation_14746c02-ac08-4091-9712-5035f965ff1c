<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识前驱图系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background-color: #4a6fa5;
            color: white;
            padding: 20px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        h1 {
            font-size: 2.2em;
        }
        
        .main-content {
            display: flex;
            gap: 20px;
        }
        
        .control-panel {
            flex: 1;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .knowledge-map {
            flex: 2;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            min-height: 500px;
            position: relative;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1em;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
        
        button {
            background-color: #4a6fa5;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #3a5a80;
        }
        
        .node {
            position: absolute;
            background-color: #fff;
            border: 2px solid #4a6fa5;
            border-radius: 8px;
            padding: 10px;
            width: 180px;
            cursor: pointer;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: all 0.3s;
        }
        
        .node:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transform: translateY(-2px);
        }
        
        .node.completed {
            border-color: #4CAF50;
            background-color: #e8f5e9;
        }
        
        .node.locked {
            border-color: #9e9e9e;
            background-color: #f5f5f5;
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        .node.active {
            border-color: #ff9800;
            background-color: #fff8e1;
        }
        
        .node-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .node-description {
            font-size: 0.9em;
            color: #666;
        }
        
        .edge {
            position: absolute;
            height: 2px;
            background-color: #4a6fa5;
            transform-origin: 0 0;
            z-index: -1;
        }
        
        .status-panel {
            margin-top: 20px;
            padding: 15px;
            background-color: #e3f2fd;
            border-radius: 5px;
        }
        
        .status-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .node-actions {
            margin-top: 10px;
            display: flex;
            gap: 5px;
        }
        
        .node-actions button {
            padding: 5px 10px;
            font-size: 0.8em;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 5px;
            width: 60%;
            max-width: 600px;
        }
        
        .close {
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #4a6fa5;
        }
        
        .tab-container {
            margin-top: 20px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
        }
        
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        
        .tab.active {
            background-color: white;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }
        
        .tab-content {
            display: none;
            padding: 15px;
            border: 1px solid #ddd;
            border-top: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .prerequisite-list {
            margin-top: 10px;
        }
        
        .prerequisite-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .prerequisite-item input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>知识前驱图系统</h1>
            <p>根据前驱关系组织和规划您的学习内容</p>
        </header>
        
        <div class="main-content">
            <div class="control-panel">
                <h2>控制面板</h2>
                
                <div class="form-group">
                    <label for="node-title">知识点标题</label>
                    <input type="text" id="node-title" placeholder="输入知识点标题">
                </div>
                
                <div class="form-group">
                    <label for="node-description">知识点描述</label>
                    <textarea id="node-description" placeholder="输入知识点的详细描述"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="node-content">学习内容</label>
                    <textarea id="node-content" placeholder="输入学习内容或资源链接"></textarea>
                </div>
                
                <button id="add-node-btn">添加知识点</button>
                
                <div class="status-panel">
                    <div class="status-title">学习状态</div>
                    <div id="learning-status">
                        <p>当前进度：未开始学习</p>
                        <p>已完成知识点：0</p>
                        <p>待学习知识点：0</p>
                    </div>
                </div>
            </div>
            
            <div class="knowledge-map" id="knowledge-map">
                <!-- 知识图谱将在这里动态生成 -->
            </div>
        </div>
    </div>
    
    <!-- 知识点详情模态框 -->
    <div id="node-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 id="modal-title">知识点详情</h2>
            
            <div class="tab-container">
                <div class="tabs">
                    <div class="tab active" data-tab="info">基本信息</div>
                    <div class="tab" data-tab="content">学习内容</div>
                    <div class="tab" data-tab="prerequisites">前置要求</div>
                </div>
                
                <div class="tab-content active" id="info-tab">
                    <div class="form-group">
                        <label>标题</label>
                        <p id="detail-title"></p>
                    </div>
                    <div class="form-group">
                        <label>描述</label>
                        <p id="detail-description"></p>
                    </div>
                    <div class="form-group">
                        <label>状态</label>
                        <p id="detail-status"></p>
                    </div>
                </div>
                
                <div class="tab-content" id="content-tab">
                    <div class="form-group">
                        <label>学习内容</label>
                        <div id="detail-content"></div>
                    </div>
                    <div class="node-actions">
                        <button id="mark-completed-btn">标记为已完成</button>
                    </div>
                </div>
                
                <div class="tab-content" id="prerequisites-tab">
                    <div class="form-group">
                        <label>前置知识点</label>
                        <div id="detail-prerequisites" class="prerequisite-list">
                            <!-- 前置知识点列表将在这里动态生成 -->
                        </div>
                    </div>
                    <div class="form-group">
                        <label>添加前置知识点</label>
                        <select id="add-prerequisite-select">
                            <option value="">选择知识点</option>
                        </select>
                        <button id="add-prerequisite-btn" style="margin-top: 10px;">添加前置条件</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 知识点数据存储
        let knowledgeNodes = [];
        let connections = [];
        let currentNodeId = null;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从本地存储加载数据
            loadData();
            
            // 绑定事件
            document.getElementById('add-node-btn').addEventListener('click', addNewNode);
            document.querySelector('.close').addEventListener('click', closeModal);
            document.getElementById('mark-completed-btn').addEventListener('click', markNodeAsCompleted);
            document.getElementById('add-prerequisite-btn').addEventListener('click', addPrerequisite);
            
            // 标签切换事件
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                    
                    this.classList.add('active');
                    document.getElementById(this.getAttribute('data-tab') + '-tab').classList.add('active');
                });
            });
            
            // 点击空白处关闭模态框
            window.addEventListener('click', function(event) {
                if (event.target === document.getElementById('node-modal')) {
                    closeModal();
                }
            });
            
            // 渲染知识图谱
            renderKnowledgeMap();
            updateLearningStatus();
        });
        
        // 添加新知识点
        function addNewNode() {
            const title = document.getElementById('node-title').value.trim();
            const description = document.getElementById('node-description').value.trim();
            const content = document.getElementById('node-content').value.trim();
            
            if (!title) {
                alert('请输入知识点标题');
                return;
            }
            
            const newNode = {
                id: Date.now().toString(),
                title: title,
                description: description,
                content: content,
                status: 'locked', // 初始状态为锁定
                position: {
                    x: Math.random() * 500,
                    y: Math.random() * 300
                }
            };
            
            knowledgeNodes.push(newNode);
            
            // 清空输入框
            document.getElementById('node-title').value = '';
            document.getElementById('node-description').value = '';
            document.getElementById('node-content').value = '';
            
            // 重新渲染知识图谱
            renderKnowledgeMap();
            updateLearningStatus();
            saveData();
        }
        
        // 渲染知识图谱
        function renderKnowledgeMap() {
            const mapContainer = document.getElementById('knowledge-map');
            mapContainer.innerHTML = '';
            
            // 计算节点可访问状态
            updateNodesStatus();
            
            // 创建连接线
            connections.forEach(conn => {
                const sourceNode = knowledgeNodes.find(node => node.id === conn.source);
                const targetNode = knowledgeNodes.find(node => node.id === conn.target);
                
                if (sourceNode && targetNode) {
                    const dx = targetNode.position.x - sourceNode.position.x;
                    const dy = targetNode.position.y - sourceNode.position.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                    
                    const edge = document.createElement('div');
                    edge.className = 'edge';
                    edge.style.width = `${distance}px`;
                    edge.style.left = `${sourceNode.position.x + 90}px`;
                    edge.style.top = `${sourceNode.position.y + 25}px`;
                    edge.style.transform = `rotate(${angle}deg)`;
                    
                    mapContainer.appendChild(edge);
                }
            });
            
            // 创建节点
            knowledgeNodes.forEach(node => {
                const nodeElement = document.createElement('div');
                nodeElement.className = `node ${node.status}`;
                nodeElement.style.left = `${node.position.x}px`;
                nodeElement.style.top = `${node.position.y}px`;
                nodeElement.dataset.id = node.id;
                
                const nodeTitle = document.createElement('div');
                nodeTitle.className = 'node-title';
                nodeTitle.textContent = node.title;
                
                const nodeDescription = document.createElement('div');
                nodeDescription.className = 'node-description';
                nodeDescription.textContent = node.description.length > 50 ? 
                    node.description.substring(0, 50) + '...' : 
                    node.description;
                
                nodeElement.appendChild(nodeTitle);
                nodeElement.appendChild(nodeDescription);
                
                // 添加拖拽功能
                nodeElement.addEventListener('mousedown', startDrag);
                
                // 点击查看详情
                nodeElement.addEventListener('click', function(e) {
                    if (!e.target.classList.contains('node-action')) {
                        showNodeDetails(node.id);
                    }
                });
                
                mapContainer.appendChild(nodeElement);
            });
            
            // 更新前置知识点选择框
            updatePrerequisiteSelect();
        }
        
        // 拖拽功能
        function startDrag(e) {
            e.preventDefault();
            
            const nodeElement = this;
            const nodeId = nodeElement.dataset.id;
            
            let startX = e.clientX;
            let startY = e.clientY;
            
            function moveNode(e) {
                const dx = e.clientX - startX;
                const dy = e.clientY - startY;
                
                const node = knowledgeNodes.find(n => n.id === nodeId);
                if (node) {
                    node.position.x += dx;
                    node.position.y += dy;
                    
                    nodeElement.style.left = `${node.position.x}px`;
                    nodeElement.style.top = `${node.position.y}px`;
                    
                    startX = e.clientX;
                    startY = e.clientY;
                    
                    // 重新渲染连接线
                    renderKnowledgeMap();
                }
            }
            
            function stopDrag() {
                document.removeEventListener('mousemove', moveNode);
                document.removeEventListener('mouseup', stopDrag);
                saveData();
            }
            
            document.addEventListener('mousemove', moveNode);
            document.addEventListener('mouseup', stopDrag);
        }
        
        // 显示节点详情
        function showNodeDetails(nodeId) {
            const node = knowledgeNodes.find(n => n.id === nodeId);
            if (!node) return;
            
            currentNodeId = nodeId;
            
            document.getElementById('detail-title').textContent = node.title;
            document.getElementById('detail-description').textContent = node.description || '无描述';
            document.getElementById('detail-content').textContent = node.content || '无学习内容';
            
            let statusText = '';
            switch(node.status) {
                case 'locked':
                    statusText = '锁定（需要完成前置知识点）';
                    break;
                case 'active':
                    statusText = '可学习';
                    break;
                case 'completed':
                    statusText = '已完成';
                    break;
                default:
                    statusText = '未知状态';
            }
            
            document.getElementById('detail-status').textContent = statusText;
            
            // 更新前置知识点列表
            const prerequisitesList = document.getElementById('detail-prerequisites');
            prerequisitesList.innerHTML = '';
            
            const nodePrerequisites = connections.filter(conn => conn.target === nodeId);
            
            if (nodePrerequisites.length === 0) {
                prerequisitesList.innerHTML = '<p>无前置知识点</p>';
            } else {
                nodePrerequisites.forEach(conn => {
                    const prereqNode = knowledgeNodes.find(n => n.id === conn.source);
                    if (prereqNode) {
                        const item = document.createElement('div');
                        item.className = 'prerequisite-item';
                        
                        const status = document.createElement('span');
                        status.textContent = prereqNode.title + ' - ' + 
                            (prereqNode.status === 'completed' ? '已完成' : '未完成');
                        status.style.color = prereqNode.status === 'completed' ? 'green' : 'red';
                        
                        const removeBtn = document.createElement('button');
                        removeBtn.textContent = '移除';
                        removeBtn.style.marginLeft = '10px';
                        removeBtn.addEventListener('click', function() {
                            removePrerequisite(conn.source, nodeId);
                        });
                        
                        item.appendChild(status);
                        item.appendChild(removeBtn);
                        prerequisitesList.appendChild(item);
                    }
                });
            }
            
            // 显示模态框
            document.getElementById('node-modal').style.display = 'block';
            
            // 根据节点状态禁用或启用"标记为已完成"按钮
            const completeBtn = document.getElementById('mark-completed-btn');
            if (node.status === 'locked') {
                completeBtn.disabled = true;
                completeBtn.textContent = '需要先完成前置知识点';
            } else if (node.status === 'completed') {
                completeBtn.textContent = '标记为未完成';
                completeBtn.disabled = false;
            } else {
                completeBtn.textContent = '标记为已完成';
                completeBtn.disabled = false;
            }
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('node-modal').style.display = 'none';
            currentNodeId = null;
        }
        
        // 标记节点为已完成
        function markNodeAsCompleted() {
            if (!currentNodeId) return;
            
            const node = knowledgeNodes.find(n => n.id === currentNodeId);
            if (!node) return;
            
            if (node.status === 'completed') {
                node.status = 'active';
            } else if (node.status === 'active') {
                node.status = 'completed';
            }
            
            // 重新渲染知识图谱
            renderKnowledgeMap();
            updateLearningStatus();
            saveData();
            
            // 更新模态框内容
            showNodeDetails(currentNodeId);
        }
        
        // 添加前置条件
        function addPrerequisite() {
            if (!currentNodeId) return;
            
            const prerequisiteId = document.getElementById('add-prerequisite-select').value;
            if (!prerequisiteId) {
                alert('请选择前置知识点');
                return;
            }
            
            // 检查是否已存在此前置条件
            const existingConn = connections.find(
                conn => conn.source === prerequisiteId && conn.target === currentNodeId
            );
            
            if (existingConn) {
                alert('该前置条件已存在');
                return;
            }
            
            // 检查是否会形成循环依赖
            if (wouldCreateCycle(prerequisiteId, currentNodeId)) {
                alert('添加此前置条件会导致循环依赖，无法添加');
                return;
            }
            
            connections.push({
                source: prerequisiteId,
                target: currentNodeId
            });
            
            // 重新渲染知识图谱
            renderKnowledgeMap();
            updateLearningStatus();
            saveData();
            
            // 更新模态框内容
            showNodeDetails(currentNodeId);
        }
        
        // 移除前置条件
        function removePrerequisite(sourceId, targetId) {
            const index = connections.findIndex(
                conn => conn.source === sourceId && conn.target === targetId
            );
            
            if (index !== -1) {
                connections.splice(index, 1);
                
                // 重新渲染知识图谱
                renderKnowledgeMap();
                updateLearningStatus();
                saveData();
                
                // 更新模态框内容
                showNodeDetails(targetId);
            }
        }
        
        // 更新前置知识点选择框
        function updatePrerequisiteSelect() {
            const select = document.getElementById('add-prerequisite-select');
            select.innerHTML = '<option value="">选择知识点</option>';
            
            knowledgeNodes.forEach(node => {
                if (node.id !== currentNodeId) {
                    const option = document.createElement('option');
                    option.value = node.id;
                    option.textContent = node.title;
                    select.appendChild(option);
                }
            });
        }
        
        // 检查是否会形成循环依赖
        function wouldCreateCycle(sourceId, targetId) {
            // 检查是否存在从目标节点到源节点的路径
            function hasPath(from, to, visited = new Set()) {
                if (from === to) return true;
                if (visited.has(from)) return false;
                
                visited.add(from);
                
                const outgoingConnections = connections.filter(conn => conn.source === from);
                for (const conn of outgoingConnections) {
                    if (hasPath(conn.target, to, visited)) {
                        return true;
                    }
                }
                
                return false;
            }
            
            return hasPath(targetId, sourceId);
        }
        
        // 更新节点状态
        function updateNodesStatus() {
            // 首先找出没有前置条件的节点，将其设置为active
            knowledgeNodes.forEach(node => {
                const hasPrerequisites = connections.some(conn => conn.target === node.id);
                
                if (!hasPrerequisites && node.status !== 'completed') {
                    node.status = 'active';
                }
            });
            
            // 然后检查每个节点的前置条件是否都已完成
            let changed = true;
            while (changed) {
                changed = false;
                
                knowledgeNodes.forEach(node => {
                    if (node.status === 'locked') {
                        const prerequisites = connections.filter(conn => conn.target === node.id)
                            .map(conn => knowledgeNodes.find(n => n.id === conn.source));
                        
                        const allPrerequisitesCompleted = prerequisites.every(p => p && p.status === 'completed');
                        
                        if (allPrerequisitesCompleted) {
                            node.status = 'active';
                            changed = true;
                        }
                    }
                });
            }
        }
        
        // 更新学习状态
        function updateLearningStatus() {
            const completedCount = knowledgeNodes.filter(node => node.status === 'completed').length;
            const activeCount = knowledgeNodes.filter(node => node.status === 'active').length;
            const lockedCount = knowledgeNodes.filter(node => node.status === 'locked').length;
            
            let statusText = '';
            
            if (knowledgeNodes.length === 0) {
                statusText = '未开始学习';
            } else if (completedCount === knowledgeNodes.length) {
                statusText = '所有知识点已学习完成';
            } else if (activeCount > 0) {
                statusText = '有可学习的知识点';
            } else {
                statusText = '所有可学习的知识点已完成';
            }
            
            document.getElementById('learning-status').innerHTML = `
                <p>当前进度：${statusText}</p>
                <p>已完成知识点：${completedCount}</p>
                <p>可学习知识点：${activeCount}</p>
                <p>待解锁知识点：${lockedCount}</p>
            `;
        }
        
        // 保存数据到本地存储
        function saveData() {
            localStorage.setItem('knowledgeNodes', JSON.stringify(knowledgeNodes));
            localStorage.setItem('connections', JSON.stringify(connections));
        }
        
        // 从本地存储加载数据
        function loadData() {
            const savedNodes = localStorage.getItem('knowledgeNodes');
            const savedConnections = localStorage.getItem('connections');
            
            if (savedNodes) {
                knowledgeNodes = JSON.parse(savedNodes);
            }
            
            if (savedConnections) {
                connections = JSON.parse(savedConnections);
            }
        }
    </script>
</body>
</html> 