<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件方法学 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out;
        }

        .method-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .method-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .method-card:hover::before {
            left: 100%;
        }

        .method-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .method-icon {
            width: 40px;
            height: 40px;
            margin-right: 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .top-down { background: linear-gradient(45deg, #ff6b6b, #ee5a24); }
        .bottom-up { background: linear-gradient(45deg, #4834d4, #686de0); }
        .formal { background: linear-gradient(45deg, #00d2d3, #54a0ff); }

        .method-desc {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .canvas-container {
            width: 100%;
            height: 300px;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
        }

        canvas {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.02);
        }

        .option.selected {
            background: rgba(255,255,255,0.3);
            border-color: white;
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .explanation {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse { animation: pulse 2s infinite; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件方法学</h1>
            <p class="subtitle">通过动画和交互学习软件开发方法</p>
        </div>

        <div class="section">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">三种主要开发方法</h2>
            
            <div class="method-card" onclick="showAnimation('topDown')">
                <div class="method-title">
                    <div class="method-icon top-down">↓</div>
                    自顶向下开发方法
                </div>
                <div class="method-desc">
                    先对最高层次中的问题进行定义、设计、编程和测试，而将其中未解决的问题作为一个子任务放到下一层次中去解决。
                </div>
                <div class="canvas-container">
                    <canvas id="topDownCanvas" width="800" height="300"></canvas>
                </div>
            </div>

            <div class="method-card" onclick="showAnimation('bottomUp')">
                <div class="method-title">
                    <div class="method-icon bottom-up">↑</div>
                    自底向上开发方法
                </div>
                <div class="method-desc">
                    根据系统功能要求，从具体的器件、逻辑部件或者相似系统开始，通过对其进行相互连接、修改和扩大，构成所要求的系统。
                </div>
                <div class="canvas-container">
                    <canvas id="bottomUpCanvas" width="800" height="300"></canvas>
                </div>
            </div>

            <div class="method-card" onclick="showAnimation('formal')">
                <div class="method-title">
                    <div class="method-icon formal">∑</div>
                    形式化开发方法
                </div>
                <div class="method-desc">
                    建立在严格数学基础上的软件开发方法，使用数学符号和公式来精确描述系统行为。
                </div>
                <div class="canvas-container">
                    <canvas id="formalCanvas" width="800" height="300"></canvas>
                </div>
            </div>
        </div>

        <div class="section quiz-section">
            <h2 style="text-align: center; margin-bottom: 30px;">测试你的理解</h2>
            <div class="quiz-question">
                软件方法学是以软件开发方法为研究对象的学科。其中，<strong>（ ）</strong>是先对最高层次中的问题进行定义、设计、编程和测试，而将其中未解决的问题作为一个子任务放到下一层次中去解决。<strong>（ ）</strong>是根据系统功能要求，从具体的器件、逻辑部件或者相似系统开始，通过对其进行相互连接、修改和扩大，构成所要求的系统。<strong>（ ）</strong>是建立在严格数学基础上的软件开发方法。
            </div>
            
            <div class="options">
                <div class="option" onclick="selectOption(this, false)">
                    <strong>A</strong><br>自底向上开发方法
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>B</strong><br>形式化开发方法
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>C</strong><br>非形式化开发方法
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>D</strong><br>原型开发方法
                </div>
            </div>

            <button class="btn" onclick="checkAnswer()">检查答案</button>
            
            <div class="explanation" id="explanation">
                <h3>答案解析：</h3>
                <p><strong>正确答案：A - 自底向上开发方法</strong></p>
                <p>根据题目描述的三种方法特征：</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>第一空</strong>：描述的是"先对最高层次问题进行处理" → <strong>自顶向下开发方法</strong></li>
                    <li><strong>第二空</strong>：描述的是"从具体器件开始构建系统" → <strong>自底向上开发方法</strong></li>
                    <li><strong>第三空</strong>：描述的是"基于严格数学基础" → <strong>形式化开发方法</strong></li>
                </ul>
                <p>所以答案是A，因为题目问的是第二空的答案。</p>
            </div>
        </div>
    </div>

    <script>
        let currentAnimation = null;
        let selectedOption = null;

        // 自顶向下动画
        function animateTopDown() {
            const canvas = document.getElementById('topDownCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制层次结构
                const levels = [
                    { y: 50, boxes: 1, label: '主系统' },
                    { y: 120, boxes: 3, label: '子系统' },
                    { y: 190, boxes: 6, label: '模块' },
                    { y: 260, boxes: 12, label: '组件' }
                ];
                
                levels.forEach((level, levelIndex) => {
                    if (step > levelIndex) {
                        const boxWidth = 60;
                        const spacing = canvas.width / (level.boxes + 1);
                        
                        for (let i = 0; i < level.boxes; i++) {
                            const x = spacing * (i + 1) - boxWidth / 2;
                            
                            // 绘制盒子
                            ctx.fillStyle = `hsl(${200 + levelIndex * 30}, 70%, ${70 - levelIndex * 10}%)`;
                            ctx.fillRect(x, level.y, boxWidth, 40);
                            
                            // 绘制边框
                            ctx.strokeStyle = '#333';
                            ctx.lineWidth = 2;
                            ctx.strokeRect(x, level.y, boxWidth, 40);
                            
                            // 绘制连接线
                            if (levelIndex > 0 && step > levelIndex) {
                                const parentIndex = Math.floor(i / (level.boxes / levels[levelIndex - 1].boxes));
                                const parentX = spacing * (parentIndex + 1);
                                const parentY = levels[levelIndex - 1].y + 40;
                                
                                ctx.beginPath();
                                ctx.moveTo(parentX, parentY);
                                ctx.lineTo(x + boxWidth / 2, level.y);
                                ctx.stroke();
                            }
                        }
                        
                        // 绘制标签
                        ctx.fillStyle = '#333';
                        ctx.font = '14px Microsoft YaHei';
                        ctx.textAlign = 'left';
                        ctx.fillText(level.label, 10, level.y + 25);
                    }
                });
                
                // 绘制箭头
                if (step > 0) {
                    ctx.fillStyle = '#ff6b6b';
                    ctx.beginPath();
                    ctx.moveTo(canvas.width - 50, 30);
                    ctx.lineTo(canvas.width - 30, 40);
                    ctx.lineTo(canvas.width - 50, 50);
                    ctx.closePath();
                    ctx.fill();
                    
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'right';
                    ctx.fillText('逐层分解', canvas.width - 60, 35);
                }
                
                step++;
                if (step <= 5) {
                    setTimeout(draw, 800);
                }
            }
            
            draw();
        }

        // 自底向上动画
        function animateBottomUp() {
            const canvas = document.getElementById('bottomUpCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const components = [
                    { x: 50, y: 200, width: 40, height: 30, label: '组件A' },
                    { x: 120, y: 200, width: 40, height: 30, label: '组件B' },
                    { x: 190, y: 200, width: 40, height: 30, label: '组件C' },
                    { x: 260, y: 200, width: 40, height: 30, label: '组件D' }
                ];
                
                // 绘制基础组件
                components.forEach((comp, index) => {
                    if (step >= 0) {
                        ctx.fillStyle = '#4834d4';
                        ctx.fillRect(comp.x, comp.y, comp.width, comp.height);
                        ctx.strokeStyle = '#333';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(comp.x, comp.y, comp.width, comp.height);
                        
                        ctx.fillStyle = '#333';
                        ctx.font = '10px Microsoft YaHei';
                        ctx.textAlign = 'center';
                        ctx.fillText(comp.label, comp.x + comp.width/2, comp.y + comp.height + 15);
                    }
                });
                
                // 组合成子系统
                if (step >= 1) {
                    ctx.fillStyle = '#686de0';
                    ctx.fillRect(40, 130, 120, 50);
                    ctx.fillRect(180, 130, 120, 50);
                    ctx.strokeStyle = '#333';
                    ctx.strokeRect(40, 130, 120, 50);
                    ctx.strokeRect(180, 130, 120, 50);
                    
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('子系统1', 100, 160);
                    ctx.fillText('子系统2', 240, 160);
                    
                    // 连接线
                    [0, 1].forEach(i => {
                        ctx.beginPath();
                        ctx.moveTo(components[i].x + components[i].width/2, components[i].y);
                        ctx.lineTo(100, 180);
                        ctx.stroke();
                    });
                    
                    [2, 3].forEach(i => {
                        ctx.beginPath();
                        ctx.moveTo(components[i].x + components[i].width/2, components[i].y);
                        ctx.lineTo(240, 180);
                        ctx.stroke();
                    });
                }
                
                // 组合成主系统
                if (step >= 2) {
                    ctx.fillStyle = '#764ba2';
                    ctx.fillRect(110, 60, 180, 50);
                    ctx.strokeStyle = '#333';
                    ctx.strokeRect(110, 60, 180, 50);
                    
                    ctx.fillStyle = '#fff';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('主系统', 200, 90);
                    
                    // 连接线
                    ctx.strokeStyle = '#333';
                    ctx.beginPath();
                    ctx.moveTo(100, 130);
                    ctx.lineTo(170, 110);
                    ctx.stroke();
                    
                    ctx.beginPath();
                    ctx.moveTo(240, 130);
                    ctx.lineTo(230, 110);
                    ctx.stroke();
                }
                
                // 绘制向上箭头
                if (step >= 1) {
                    ctx.fillStyle = '#4834d4';
                    ctx.beginPath();
                    ctx.moveTo(canvas.width - 50, 250);
                    ctx.lineTo(canvas.width - 40, 230);
                    ctx.lineTo(canvas.width - 30, 250);
                    ctx.closePath();
                    ctx.fill();
                    
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'right';
                    ctx.fillText('逐层组合', canvas.width - 60, 245);
                }
                
                step++;
                if (step <= 3) {
                    setTimeout(draw, 1000);
                }
            }
            
            draw();
        }

        // 形式化方法动画
        function animateFormal() {
            const canvas = document.getElementById('formalCanvas');
            const ctx = canvas.getContext('2d');
            let step = 0;
            
            const formulas = [
                'P(x) → Q(x)',
                '∀x ∈ D: P(x)',
                'f: A → B',
                '{P} S {Q}'
            ];
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制数学公式背景
                ctx.fillStyle = 'rgba(0, 210, 211, 0.1)';
                ctx.fillRect(50, 50, canvas.width - 100, canvas.height - 100);
                ctx.strokeStyle = '#00d2d3';
                ctx.lineWidth = 2;
                ctx.strokeRect(50, 50, canvas.width - 100, canvas.height - 100);
                
                // 绘制标题
                ctx.fillStyle = '#333';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('形式化规约', canvas.width / 2, 80);
                
                // 逐个显示公式
                formulas.forEach((formula, index) => {
                    if (step > index) {
                        ctx.fillStyle = '#00d2d3';
                        ctx.font = '20px Times New Roman';
                        ctx.textAlign = 'center';
                        ctx.fillText(formula, canvas.width / 2, 120 + index * 40);
                        
                        // 添加解释
                        ctx.fillStyle = '#666';
                        ctx.font = '12px Microsoft YaHei';
                        const explanations = [
                            '逻辑蕴含关系',
                            '全称量词表达',
                            '函数映射定义',
                            'Hoare三元组'
                        ];
                        ctx.fillText(explanations[index], canvas.width / 2 + 150, 125 + index * 40);
                    }
                });
                
                // 绘制数学符号装饰
                if (step > 0) {
                    ctx.fillStyle = 'rgba(84, 160, 255, 0.3)';
                    ctx.beginPath();
                    ctx.arc(100, 150, 20, 0, Math.PI * 2);
                    ctx.fill();
                    
                    ctx.fillStyle = '#54a0ff';
                    ctx.font = '24px Times New Roman';
                    ctx.textAlign = 'center';
                    ctx.fillText('∑', 100, 158);
                }
                
                step++;
                if (step <= formulas.length + 1) {
                    setTimeout(draw, 1000);
                }
            }
            
            draw();
        }

        function showAnimation(type) {
            switch(type) {
                case 'topDown':
                    animateTopDown();
                    break;
                case 'bottomUp':
                    animateBottomUp();
                    break;
                case 'formal':
                    animateFormal();
                    break;
            }
        }

        function selectOption(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // 选择当前选项
            element.classList.add('selected');
            selectedOption = element;
        }

        function checkAnswer() {
            const options = document.querySelectorAll('.option');
            const explanation = document.getElementById('explanation');
            
            // 显示正确答案（第一个选项是正确的）
            options[0].classList.add('correct');
            
            // 如果用户选择了错误答案，标记为错误
            if (selectedOption && selectedOption !== options[0]) {
                selectedOption.classList.add('wrong');
            }
            
            // 显示解析
            explanation.style.display = 'block';
            explanation.scrollIntoView({ behavior: 'smooth' });
        }

        // 页面加载完成后自动播放第一个动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                showAnimation('topDown');
            }, 1000);
        });
    </script>
</body>
</html>
