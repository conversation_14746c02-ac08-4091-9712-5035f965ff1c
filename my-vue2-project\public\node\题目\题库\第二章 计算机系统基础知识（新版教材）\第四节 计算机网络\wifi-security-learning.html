<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WiFi安全认证方式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            color: white;
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
            font-weight: 300;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .option {
            background: #f8f9fa;
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .option.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .option.correct {
            border-color: #28a745;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .option.wrong {
            border-color: #dc3545;
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #animationCanvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            background: #f8f9fa;
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .security-level {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .security-level:hover {
            transform: translateX(10px);
        }

        .security-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .low { background: #dc3545; }
        .medium { background: #ffc107; }
        .high { background: #28a745; }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse { animation: pulse 2s infinite; }

        .step {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s ease;
        }

        .step.active {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">WiFi安全认证方式学习</h1>
            <p class="subtitle">从零开始理解WiFi安全技术</p>
        </div>

        <div class="question-card">
            <h2 class="question-title">【软考达人-回忆版】以下wifi认证方式中，（ ）使用了AES加密算法，安全性更高。</h2>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <h3>A. 开放式</h3>
                    <p>无密码，任何人都可以连接</p>
                </div>
                <div class="option" data-answer="B">
                    <h3>B. WP</h3>
                    <p>这个选项有问题，应该是WPA</p>
                </div>
                <div class="option" data-answer="C">
                    <h3>C. WPA2</h3>
                    <p>使用AES加密的安全协议</p>
                </div>
                <div class="option" data-answer="D">
                    <h3>D. WEP</h3>
                    <p>早期的加密方式，已被破解</p>
                </div>
            </div>

            <div style="text-align: center;">
                <button class="btn" onclick="checkAnswer()">提交答案</button>
                <button class="btn" onclick="showExplanation()">查看解析</button>
                <button class="btn" onclick="startAnimation()">开始动画演示</button>
            </div>
        </div>

        <div class="learning-section">
            <h2>📚 知识点详解</h2>
            
            <div class="canvas-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation step">
                <h3>🔐 WiFi安全认证方式对比</h3>
                
                <div class="security-level">
                    <div class="security-icon low">1</div>
                    <div>
                        <strong>开放式（Open）</strong><br>
                        无任何加密，数据明文传输，极不安全
                    </div>
                </div>

                <div class="security-level">
                    <div class="security-icon low">2</div>
                    <div>
                        <strong>WEP（有线等效保密）</strong><br>
                        使用RC4加密，密钥固定，容易被破解
                    </div>
                </div>

                <div class="security-level">
                    <div class="security-icon medium">3</div>
                    <div>
                        <strong>WPA（WiFi保护访问）</strong><br>
                        使用TKIP加密，比WEP安全但仍有漏洞
                    </div>
                </div>

                <div class="security-level">
                    <div class="security-icon high">4</div>
                    <div>
                        <strong>WPA2（WiFi保护访问2）</strong><br>
                        使用AES加密算法，目前最安全的标准
                    </div>
                </div>
            </div>

            <div class="explanation step">
                <h3>🎯 解题思路</h3>
                <p><strong>关键词识别：</strong>"AES加密算法" + "安全性更高"</p>
                <p><strong>知识点：</strong>只有WPA2使用AES（高级加密标准）</p>
                <p><strong>排除法：</strong></p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>开放式：无加密</li>
                    <li>WEP：使用RC4加密（已被破解）</li>
                    <li>WPA：使用TKIP加密</li>
                    <li>WPA2：使用AES加密 ✓</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let selectedAnswer = null;
        let currentStep = 0;
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');

        // 选择答案
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedAnswer = this.dataset.answer;
            });
        });

        // 检查答案
        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }

            document.querySelectorAll('.option').forEach(option => {
                if (option.dataset.answer === 'C') {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== 'C') {
                    option.classList.add('wrong');
                }
            });

            if (selectedAnswer === 'C') {
                setTimeout(() => alert('🎉 恭喜答对了！WPA2确实使用AES加密算法。'), 500);
            } else {
                setTimeout(() => alert('❌ 答案错误。正确答案是C，让我们来学习一下为什么。'), 500);
            }
        }

        // 显示解析
        function showExplanation() {
            document.querySelectorAll('.step').forEach((step, index) => {
                setTimeout(() => {
                    step.classList.add('active');
                }, index * 800);
            });
        }

        // 动画演示
        function startAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            animateWiFiSecurity();
        }

        function animateWiFiSecurity() {
            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制WiFi图标
                drawWiFi(100, 200, frame);
                
                // 绘制加密方式
                drawEncryptionMethods(frame);
                
                // 绘制数据传输
                drawDataTransmission(frame);
                
                frame++;
                if (frame < 300) {
                    requestAnimationFrame(animate);
                }
            };
            animate();
        }

        function drawWiFi(x, y, frame) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            
            // WiFi信号圈
            for (let i = 1; i <= 3; i++) {
                ctx.beginPath();
                ctx.arc(x, y, 20 + i * 15, -Math.PI * 0.7, -Math.PI * 0.3);
                ctx.globalAlpha = 0.3 + Math.sin(frame * 0.1 + i) * 0.3;
                ctx.stroke();
            }
            ctx.globalAlpha = 1;
        }

        function drawEncryptionMethods(frame) {
            const methods = [
                { name: 'Open', color: '#dc3545', security: 0 },
                { name: 'WEP', color: '#dc3545', security: 1 },
                { name: 'WPA', color: '#ffc107', security: 2 },
                { name: 'WPA2', color: '#28a745', security: 3 }
            ];

            methods.forEach((method, index) => {
                const x = 250 + index * 130;
                const y = 150;
                
                // 绘制方法框
                ctx.fillStyle = method.color;
                ctx.globalAlpha = 0.8;
                ctx.fillRect(x, y, 100, 60);
                ctx.globalAlpha = 1;
                
                // 绘制文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(method.name, x + 50, y + 35);
                
                // 绘制安全等级
                for (let i = 0; i < 4; i++) {
                    ctx.fillStyle = i <= method.security ? '#fff' : 'rgba(255,255,255,0.3)';
                    ctx.fillRect(x + 10 + i * 20, y + 45, 15, 8);
                }
                
                // AES标识（仅WPA2）
                if (method.name === 'WPA2') {
                    ctx.fillStyle = '#fff';
                    ctx.font = 'bold 10px Arial';
                    ctx.fillText('AES', x + 50, y + 25);
                    
                    // 闪烁效果
                    if (Math.sin(frame * 0.2) > 0) {
                        ctx.strokeStyle = '#fff';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(x - 5, y - 5, 110, 70);
                    }
                }
            });
        }

        function drawDataTransmission(frame) {
            // 绘制数据包
            const packetX = 100 + (frame * 2) % 600;
            const packetY = 300;
            
            ctx.fillStyle = '#667eea';
            ctx.fillRect(packetX, packetY, 30, 20);
            
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('数据', packetX + 15, packetY + 13);
            
            // 加密状态指示
            if (packetX > 250 && packetX < 750) {
                const methodIndex = Math.floor((packetX - 250) / 130);
                if (methodIndex === 3) { // WPA2
                    ctx.fillStyle = '#28a745';
                    ctx.fillText('🔒AES加密', packetX + 15, packetY - 10);
                }
            }
        }

        // 初始化
        window.onload = function() {
            startAnimation();
        };
    </script>
</body>
</html>
