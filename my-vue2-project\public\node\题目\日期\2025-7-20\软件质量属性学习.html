<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件质量属性互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateY(-5px);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .btn:active {
            transform: translateY(-1px);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            animation: slideInLeft 0.8s ease-out;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            color: #2d3436;
            margin-bottom: 20px;
            font-weight: bold;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            padding: 15px;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            text-align: center;
        }

        .quiz-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quiz-option.correct {
            background: #00b894;
            color: white;
            border-color: #00a085;
        }

        .quiz-option.wrong {
            background: #e17055;
            color: white;
            border-color: #d63031;
        }

        .timer {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .score {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="score">
        <span id="scoreText">学习进度: 0%</span>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">🏠 车库门系统质量属性学习</h1>
            <p class="subtitle">通过互动游戏理解软件架构中的性能与可测试性</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
        </div>

        <!-- 性能质量属性演示 -->
        <div class="section">
            <h2 class="section-title">🚀 性能质量属性 - 响应时间演示</h2>
            <div class="explanation">
                <h3>💡 什么是性能质量属性？</h3>
                <p>性能是指系统在特定条件下完成任务的速度和效率。在车库门系统中，当检测到障碍物时，系统必须在0.1秒内停止下降，这就是对<strong>响应时间</strong>的性能要求。</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="performanceCanvas" width="600" height="400"></canvas>
                <div class="timer" id="responseTimer">响应时间: 0.000s</div>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startGarageDoorDemo()">🚪 开始车库门演示</button>
                <button class="btn btn-danger" onclick="addObstacle()">⚠️ 添加障碍物</button>
                <button class="btn btn-success" onclick="resetDemo()">🔄 重置演示</button>
            </div>
        </div>

        <!-- 可测试性质量属性演示 -->
        <div class="section">
            <h2 class="section-title">🔧 可测试性质量属性 - 远程诊断演示</h2>
            <div class="explanation">
                <h3>💡 什么是可测试性质量属性？</h3>
                <p>可测试性是指系统能够被有效测试、调试和诊断的能力。车库门系统需要支持远程错误诊断与调试，这要求系统具备良好的<strong>可观测性</strong>和<strong>可控制性</strong>。</p>
            </div>

            <div class="canvas-container">
                <canvas id="testabilityCanvas" width="600" height="400"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="startRemoteDiagnosis()">🖥️ 启动远程诊断</button>
                <button class="btn btn-danger" onclick="simulateError()">❌ 模拟系统错误</button>
                <button class="btn btn-success" onclick="runDiagnostic()">🔍 运行诊断测试</button>
            </div>
        </div>

        <!-- 互动测验 -->
        <div class="quiz-section">
            <h2 class="section-title">🎯 知识测验</h2>
            <div class="quiz-question" id="quizQuestion">
                当车库门正常下降时，如果发现下面有障碍物，则系统停止下降的时间需要控制在0.1秒内，这与哪个质量属性相关？
            </div>
            <div class="quiz-options" id="quizOptions">
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 可用性</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">B. 性能</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">C. 可修改性</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 可测试性</div>
            </div>
            <div id="quizFeedback"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let score = 0;
        let progress = 0;
        let performanceCtx, testabilityCtx;
        let garageDoorY = 50;
        let obstacleX = 300;
        let isMoving = false;
        let startTime = 0;
        let responseTime = 0;
        let animationId;
        let diagnosticState = 'idle';
        let errorSimulated = false;

        // 初始化
        window.onload = function() {
            initCanvases();
            updateProgress(10);
        };

        function initCanvases() {
            const performanceCanvas = document.getElementById('performanceCanvas');
            const testabilityCanvas = document.getElementById('testabilityCanvas');

            performanceCtx = performanceCanvas.getContext('2d');
            testabilityCtx = testabilityCanvas.getContext('2d');

            drawPerformanceScene();
            drawTestabilityScene();
        }

        // 性能演示相关函数
        function drawPerformanceScene() {
            performanceCtx.clearRect(0, 0, 600, 400);

            // 绘制车库框架
            performanceCtx.strokeStyle = '#333';
            performanceCtx.lineWidth = 4;
            performanceCtx.strokeRect(100, 50, 400, 300);

            // 绘制车库门
            performanceCtx.fillStyle = '#8B4513';
            performanceCtx.fillRect(120, garageDoorY, 360, 20);

            // 绘制车库门纹理
            for(let i = 0; i < 8; i++) {
                performanceCtx.strokeStyle = '#654321';
                performanceCtx.lineWidth = 1;
                performanceCtx.beginPath();
                performanceCtx.moveTo(120 + i * 45, garageDoorY);
                performanceCtx.lineTo(120 + i * 45, garageDoorY + 20);
                performanceCtx.stroke();
            }

            // 绘制障碍物（如果存在）
            if (obstacleX < 500) {
                performanceCtx.fillStyle = '#FF6B6B';
                performanceCtx.fillRect(obstacleX - 15, 320, 30, 30);
                performanceCtx.fillStyle = '#FFF';
                performanceCtx.font = '20px Arial';
                performanceCtx.textAlign = 'center';
                performanceCtx.fillText('⚠️', obstacleX, 340);
            }

            // 绘制传感器
            performanceCtx.fillStyle = '#4ECDC4';
            performanceCtx.fillRect(280, 300, 40, 10);
            performanceCtx.fillStyle = '#FFF';
            performanceCtx.font = '12px Arial';
            performanceCtx.textAlign = 'center';
            performanceCtx.fillText('传感器', 300, 325);

            // 绘制检测光束
            if (obstacleX < 500 && garageDoorY > 200) {
                performanceCtx.strokeStyle = '#FF0000';
                performanceCtx.lineWidth = 2;
                performanceCtx.setLineDash([5, 5]);
                performanceCtx.beginPath();
                performanceCtx.moveTo(300, 310);
                performanceCtx.lineTo(obstacleX, 320);
                performanceCtx.stroke();
                performanceCtx.setLineDash([]);
            }
        }

        function startGarageDoorDemo() {
            if (isMoving) return;

            isMoving = true;
            garageDoorY = 50;
            startTime = Date.now();

            function animate() {
                if (garageDoorY < 280 && isMoving) {
                    garageDoorY += 2;

                    // 检测障碍物
                    if (obstacleX < 500 && garageDoorY > 200) {
                        responseTime = (Date.now() - startTime) / 1000;
                        document.getElementById('responseTimer').textContent = `响应时间: ${responseTime.toFixed(3)}s`;

                        if (responseTime <= 0.1) {
                            isMoving = false;
                            showPerformanceResult(true);
                        } else {
                            showPerformanceResult(false);
                        }
                    }

                    drawPerformanceScene();
                    animationId = requestAnimationFrame(animate);
                } else {
                    isMoving = false;
                    if (obstacleX >= 500) {
                        showPerformanceResult(true, "正常完成");
                    }
                }
            }

            animate();
        }

        function addObstacle() {
            obstacleX = 300;
            drawPerformanceScene();
            updateProgress(30);
        }

        function resetDemo() {
            isMoving = false;
            garageDoorY = 50;
            obstacleX = 600;
            responseTime = 0;
            document.getElementById('responseTimer').textContent = '响应时间: 0.000s';
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            drawPerformanceScene();
        }

        function showPerformanceResult(success, message = '') {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'explanation';
            resultDiv.style.marginTop = '20px';

            if (success) {
                resultDiv.style.borderLeftColor = '#00b894';
                resultDiv.innerHTML = `
                    <h3>✅ 性能要求达标！</h3>
                    <p>${message || `系统在${responseTime.toFixed(3)}秒内成功停止，满足0.1秒的性能要求。`}</p>
                    <p><strong>这就是性能质量属性的体现：系统能够在规定时间内响应并执行操作。</strong></p>
                `;
                updateProgress(50);
            } else {
                resultDiv.style.borderLeftColor = '#e17055';
                resultDiv.innerHTML = `
                    <h3>❌ 性能要求未达标</h3>
                    <p>系统响应时间为${responseTime.toFixed(3)}秒，超过了0.1秒的要求。</p>
                    <p><strong>这说明系统的性能质量属性需要优化。</strong></p>
                `;
            }

            const section = document.querySelector('.section');
            const existingResult = section.querySelector('.explanation:last-child');
            if (existingResult && existingResult.innerHTML.includes('性能要求')) {
                existingResult.remove();
            }
            section.appendChild(resultDiv);
        }

        // 可测试性演示相关函数
        function drawTestabilityScene() {
            testabilityCtx.clearRect(0, 0, 600, 400);

            // 绘制车库门系统
            testabilityCtx.fillStyle = '#34495e';
            testabilityCtx.fillRect(50, 150, 200, 100);
            testabilityCtx.fillStyle = '#FFF';
            testabilityCtx.font = '14px Arial';
            testabilityCtx.textAlign = 'center';
            testabilityCtx.fillText('车库门系统', 150, 200);

            // 绘制远程PC
            testabilityCtx.fillStyle = '#3498db';
            testabilityCtx.fillRect(350, 150, 200, 100);
            testabilityCtx.fillStyle = '#FFF';
            testabilityCtx.fillText('智能家居系统', 450, 190);
            testabilityCtx.fillText('(远程PC)', 450, 210);

            // 绘制连接线
            testabilityCtx.strokeStyle = diagnosticState === 'connected' ? '#2ecc71' : '#95a5a6';
            testabilityCtx.lineWidth = 3;
            testabilityCtx.beginPath();
            testabilityCtx.moveTo(250, 200);
            testabilityCtx.lineTo(350, 200);
            testabilityCtx.stroke();

            // 绘制数据传输动画
            if (diagnosticState === 'diagnosing') {
                const time = Date.now() / 1000;
                const x = 250 + (Math.sin(time * 3) + 1) * 50;
                testabilityCtx.fillStyle = '#f39c12';
                testabilityCtx.beginPath();
                testabilityCtx.arc(x, 200, 5, 0, Math.PI * 2);
                testabilityCtx.fill();
            }

            // 绘制错误指示器
            if (errorSimulated) {
                testabilityCtx.fillStyle = '#e74c3c';
                testabilityCtx.beginPath();
                testabilityCtx.arc(150, 120, 15, 0, Math.PI * 2);
                testabilityCtx.fill();
                testabilityCtx.fillStyle = '#FFF';
                testabilityCtx.font = '16px Arial';
                testabilityCtx.textAlign = 'center';
                testabilityCtx.fillText('!', 150, 125);
            }

            // 绘制诊断信息
            if (diagnosticState === 'completed') {
                testabilityCtx.fillStyle = '#2ecc71';
                testabilityCtx.fillRect(300, 280, 250, 80);
                testabilityCtx.fillStyle = '#FFF';
                testabilityCtx.font = '12px Arial';
                testabilityCtx.textAlign = 'left';
                testabilityCtx.fillText('诊断报告:', 310, 300);
                testabilityCtx.fillText('✓ 传感器状态: 正常', 310, 320);
                testabilityCtx.fillText('✓ 电机状态: 正常', 310, 340);
                testabilityCtx.fillText(errorSimulated ? '✗ 控制模块: 异常' : '✓ 控制模块: 正常', 310, 360);
            }
        }

        function startRemoteDiagnosis() {
            diagnosticState = 'connected';
            drawTestabilityScene();
            updateProgress(60);

            setTimeout(() => {
                showTestabilityResult('连接成功', '远程诊断接口已建立连接，系统具备良好的可控制性。');
            }, 1000);
        }

        function simulateError() {
            errorSimulated = !errorSimulated;
            drawTestabilityScene();
            updateProgress(70);

            const message = errorSimulated ?
                '系统错误已模拟，错误状态可被远程观测到。' :
                '错误状态已清除。';
            showTestabilityResult('错误模拟', message);
        }

        function runDiagnostic() {
            if (diagnosticState !== 'connected') {
                showTestabilityResult('诊断失败', '请先启动远程诊断连接。');
                return;
            }

            diagnosticState = 'diagnosing';
            let animationCount = 0;

            const diagnosticAnimation = setInterval(() => {
                drawTestabilityScene();
                animationCount++;

                if (animationCount > 10) {
                    clearInterval(diagnosticAnimation);
                    diagnosticState = 'completed';
                    drawTestabilityScene();
                    updateProgress(80);
                    showTestabilityResult('诊断完成', '系统具备良好的可观测性，能够提供详细的状态信息用于远程诊断。');
                }
            }, 200);
        }

        function showTestabilityResult(title, message) {
            const resultDiv = document.createElement('div');
            resultDiv.className = 'explanation';
            resultDiv.style.marginTop = '20px';
            resultDiv.style.borderLeftColor = '#3498db';
            resultDiv.innerHTML = `
                <h3>🔧 ${title}</h3>
                <p>${message}</p>
                <p><strong>这体现了可测试性质量属性：系统支持远程监控、诊断和调试。</strong></p>
            `;

            const sections = document.querySelectorAll('.section');
            const testabilitySection = sections[1];
            const existingResult = testabilitySection.querySelector('.explanation:last-child');
            if (existingResult && existingResult.innerHTML.includes('🔧')) {
                existingResult.remove();
            }
            testabilitySection.appendChild(resultDiv);
        }

        // 测验相关函数
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('B. 性能')) {
                    option.classList.add('correct');
                }
            });

            const feedback = document.getElementById('quizFeedback');
            if (isCorrect) {
                feedback.innerHTML = `
                    <div class="explanation" style="border-left-color: #00b894; margin-top: 20px;">
                        <h3>🎉 回答正确！</h3>
                        <p>车库门系统需要在0.1秒内停止下降，这确实是对<strong>响应时间</strong>的要求，属于性能质量属性。</p>
                        <p>性能质量属性关注系统的时间行为、资源利用率和吞吐量等指标。</p>
                    </div>
                `;
                updateProgress(90);
                setTimeout(showSecondQuestion, 2000);
            } else {
                feedback.innerHTML = `
                    <div class="explanation" style="border-left-color: #e17055; margin-top: 20px;">
                        <h3>❌ 回答错误</h3>
                        <p>正确答案是B. 性能。0.1秒的响应时间要求属于性能质量属性。</p>
                        <p>让我们继续学习，加深理解！</p>
                    </div>
                `;
                setTimeout(showSecondQuestion, 3000);
            }
        }

        function showSecondQuestion() {
            document.getElementById('quizQuestion').textContent =
                '系统需要为部署在远程PC机上的智能家居系统留有控制接口，并支持远程错误诊断与调试，这与哪个质量属性相关？';

            const optionsContainer = document.getElementById('quizOptions');
            optionsContainer.innerHTML = `
                <div class="quiz-option" onclick="selectSecondAnswer(this, false)">A. 可用性</div>
                <div class="quiz-option" onclick="selectSecondAnswer(this, false)">B. 性能</div>
                <div class="quiz-option" onclick="selectSecondAnswer(this, false)">C. 可修改性</div>
                <div class="quiz-option" onclick="selectSecondAnswer(this, true)">D. 可测试性</div>
            `;

            document.getElementById('quizFeedback').innerHTML = '';
        }

        function selectSecondAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('D. 可测试性')) {
                    option.classList.add('correct');
                }
            });

            const feedback = document.getElementById('quizFeedback');
            if (isCorrect) {
                feedback.innerHTML = `
                    <div class="explanation" style="border-left-color: #00b894; margin-top: 20px;">
                        <h3>🎉 完全正确！</h3>
                        <p>远程控制接口和错误诊断功能确实属于<strong>可测试性</strong>质量属性。</p>
                        <p>可测试性包括可观测性（能够观察系统状态）和可控制性（能够控制系统行为）。</p>
                        <h3>🏆 恭喜完成学习！</h3>
                    </div>
                `;
                updateProgress(100);
            } else {
                feedback.innerHTML = `
                    <div class="explanation" style="border-left-color: #e17055; margin-top: 20px;">
                        <h3>❌ 回答错误</h3>
                        <p>正确答案是D. 可测试性。远程诊断和调试功能属于可测试性质量属性。</p>
                        <h3>📚 学习完成！</h3>
                    </div>
                `;
                updateProgress(100);
            }
        }

        // 工具函数
        function updateProgress(newProgress) {
            progress = newProgress;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('scoreText').textContent = `学习进度: ${progress}%`;

            if (progress === 100) {
                setTimeout(() => {
                    alert('🎉 恭喜您完成了软件质量属性的学习！\n\n您已经掌握了：\n• 性能质量属性（响应时间要求）\n• 可测试性质量属性（远程诊断能力）\n\n继续加油！');
                }, 1000);
            }
        }

        // 启动可测试性场景的动画循环
        setInterval(() => {
            if (diagnosticState === 'diagnosing') {
                drawTestabilityScene();
            }
        }, 100);
    </script>
</body>
</html>
