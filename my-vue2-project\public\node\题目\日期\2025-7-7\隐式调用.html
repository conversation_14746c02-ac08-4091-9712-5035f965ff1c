<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐式调用小游戏</title>
    <style>
        /* CSS 样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            max-width: 800px;
            margin: auto;
        }
        h1, h2 {
            color: #1a2a4c;
            border-bottom: 2px solid #6c5ce7;
            padding-bottom: 10px;
        }
        .container {
            background-color: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        .explanation-box {
            background-color: #e8eaf6;
            border-left: 5px solid #6c5ce7;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .game-title {
            text-align: center;
            color: #d6336c;
        }
        .editor-area {
            width: 100%;
            height: 150px;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            font-size: 16px;
            box-sizing: border-box; /* 确保 padding 不会撑大宽度 */
        }
        .output-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .output-box {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            min-height: 80px;
            background-color: #fafafa;
        }
        .output-box h3 {
            margin-top: 0;
            color: #4a4a4a;
        }
        #syntax-highlight-output {
            grid-column: span 2; /* 语法高亮区独占一行 */
            font-family: 'Courier New', Courier, monospace;
            white-space: pre-wrap; /* 保持换行和空格 */
            word-wrap: break-word;
        }
        .keyword {
            color: #6c5ce7;
            font-weight: bold;
        }
        .error {
            background-color: #ffcccc;
            color: #a00;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .stats-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        #event-bus-canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
            margin-top: 20px;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>理解"隐式调用"</h1>
        <div class="explanation-box">
            <p><strong>核心思想：</strong>一个组件（比如代码输入框）只管"广播事件"（比如"我里面的内容变了！"），但它不关心谁会听到，也不直接去命令其他组件工作。</p>
            <p>其他组件（比如语法检查器）则独立"订阅"这个事件。当事件发生时，系统会自动"调用"这些订阅者来完成它们自己的任务。这种方式让组件之间关系松散，易于维护。</p>
        </div>

        <h2 class="game-title">🎮 迷你代码编辑器游戏 🎮</h2>
        <p>在下面的文本框里输入内容，看看会发生什么。一个"输入"事件会同时触发下面三个独立模块的更新。</p>
        <textarea id="codeInput" class="editor-area" placeholder="试试输入 'function' 'error' 或者任何文字..."></textarea>
    </div>

    <div class="container">
        <h2>事件总线可视化</h2>
        <canvas id="event-bus-canvas" width="750" height="200"></canvas>
    </div>

    <div class="container output-container">
        <!-- 模块1: 语法高亮 -->
        <div class="output-box" id="syntax-highlight-output">
            <h3>模块1: 语法高亮结果</h3>
            <div id="syntaxDisplay"></div>
        </div>

        <!-- 模块2: 错误检查 -->
        <div class="output-box">
            <h3>模块2: 错误检查</h3>
            <div id="errorDisplay"></div>
        </div>

        <!-- 模块3: 字数统计 -->
        <div class="output-box">
            <h3>模块3: 字数统计</h3>
            <div id="statsDisplay"><span class="stats-value">0</span> 词</div>
        </div>
    </div>

    <script>
        // JavaScript 逻辑
        document.addEventListener('DOMContentLoaded', () => {
            const codeInput = document.getElementById('codeInput');

            const syntaxDisplay = document.getElementById('syntaxDisplay');
            const errorDisplay = document.getElementById('errorDisplay');
            const statsDisplay = document.getElementById('statsDisplay');

            // --- Canvas 相关 ---
            const canvas = document.getElementById('event-bus-canvas');
            const ctx = canvas.getContext('2d');
            let particles = [];
            const publisher = { x: 100, y: 100, label: '事件发布者 (输入框)' };
            const subscribers = [
                { x: 650, y: 50, label: '订阅者1 (语法高亮)', active: 0 },
                { x: 650, y: 100, label: '订阅者2 (错误检查)', active: 0 },
                { x: 650, y: 150, label: '订阅者3 (字数统计)', active: 0 }
            ];

            // 定义一些关键词用于高亮
            const KEYWORDS = ['function', 'var', 'let', 'const', 'return', 'if', 'else', 'for', 'while'];
            // 定义一个"错误"单词
            const FORBIDDEN_WORD = 'error';

            // --- 模块1: 语法高亮处理器 ---
            function handleSyntaxHighlighting(text) {
                let highlightedText = text;
                // 用正则表达式给所有关键词加上高亮样式
                const regex = new RegExp(`\\b(${KEYWORDS.join('|')})\\b`, 'g');
                highlightedText = highlightedText.replace(regex, '<span class="keyword">$1</span>');
                syntaxDisplay.innerHTML = highlightedText;
            }

            // --- 模块2: 错误检查处理器 ---
            function handleErrorChecking(text) {
                if (text.includes(FORBIDDEN_WORD)) {
                    errorDisplay.innerHTML = `发现禁止词: <span class="error">${FORBIDDEN_WORD}</span>`;
                } else {
                    errorDisplay.innerHTML = '✅ 暂无错误';
                }
            }

            // --- 模块3: 字数统计处理器 ---
            function handleStats(text) {
                // 一个简单的词数计算
                const words = text.trim().split(/\s+/).filter(word => word.length > 0);
                const wordCount = words[0] === '' ? 0 : words.length;
                statsDisplay.innerHTML = `<span class="stats-value">${wordCount}</span> 词`;
            }

            // =================================================================
            // == 核心部分: 隐式调用的实现 ==
            // 我们为同一个"输入"事件，注册了三个独立的监听器（处理器）。
            // 当 codeInput 触发 input 事件时，浏览器（系统）会自动"隐式地"
            // 调用下面这三个函数。输入框本身并不知道这三个模块的存在。
            // =================================================================
            codeInput.addEventListener('input', (event) => {
                const currentText = event.target.value;
                
                // 触发Canvas动画
                triggerEventAnimation();

                // 系统调用所有"订阅"了 input 事件的模块
                handleSyntaxHighlighting(currentText);
                handleErrorChecking(currentText);
                handleStats(currentText);
            });
            
            // --- Canvas 动画逻辑 ---

            function triggerEventAnimation() {
                // 为每个订阅者创建一个粒子
                for (let i = 0; i < subscribers.length; i++) {
                    particles.push({
                        x: publisher.x,
                        y: publisher.y,
                        targetX: subscribers[i].x,
                        targetY: subscribers[i].y,
                        subscriberIndex: i,
                        size: 5,
                        color: `hsl(${120 + i * 60}, 80%, 60%)`
                    });
                }
            }

            function drawBus() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.font = '14px sans-serif';
                ctx.textAlign = 'center';

                // 绘制发布者
                ctx.fillStyle = '#6c5ce7';
                ctx.fillRect(publisher.x - 70, publisher.y - 20, 140, 40);
                ctx.fillStyle = 'white';
                ctx.fillText(publisher.label, publisher.x, publisher.y + 5);

                // 绘制订阅者
                subscribers.forEach((sub, i) => {
                    if (sub.active > 0) {
                        ctx.fillStyle = '#d6336c'; // 激活状态
                        sub.active--;
                    } else {
                        ctx.fillStyle = '#4a4a4a'; // 普通状态
                    }
                    ctx.fillRect(sub.x - 70, sub.y - 20, 140, 40);
                    ctx.fillStyle = 'white';
                    ctx.fillText(sub.label, sub.x, sub.y + 5);
                });

                // 绘制粒子
                particles.forEach((p, index) => {
                    p.x += (p.targetX - p.x) * 0.1;
                    p.y += (p.targetY - p.y) * 0.1;
                    
                    ctx.beginPath();
                    ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
                    ctx.fillStyle = p.color;
                    ctx.fill();

                    // 如果粒子接近目标，则移除并激活订阅者
                    if (Math.abs(p.x - p.targetX) < 10) {
                        subscribers[p.subscriberIndex].active = 15; // 激活15帧
                        particles.splice(index, 1);
                    }
                });
            }

            function animate() {
                drawBus();
                requestAnimationFrame(animate);
            }

            // 首次绘制和启动动画循环
            animate();

            // 初始化显示
            handleSyntaxHighlighting('');
            handleErrorChecking('');
            handleStats('');
        });
    </script>

</body>
</html> 