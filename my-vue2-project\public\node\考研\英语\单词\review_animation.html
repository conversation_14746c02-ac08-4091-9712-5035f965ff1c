<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画：review</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            font-size: 3em;
            color: #1a73e8;
            margin-bottom: 10px;
        }
        #word-pronunciation {
            font-size: 1.2em;
            color: #5f6368;
            margin-bottom: 20px;
        }
        canvas {
            background: #ffffff;
            border: 1px solid #dcdcdc;
            border-radius: 8px;
            margin-bottom: 20px;
            cursor: pointer;
        }
        .explanation {
            text-align: left;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
        }
        .explanation h2 {
            color: #1a73e8;
            border-bottom: 2px solid #1a73e8;
            padding-bottom: 5px;
            margin-top: 0;
        }
        .explanation p {
            font-size: 1.1em;
            line-height: 1.8;
        }
        .explanation .highlight {
            font-weight: bold;
            color: #d93025;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin-top: 20px;
        }
        button:hover {
            background-color: #155ab6;
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        #interactive-instruction {
            color: #5f6368;
            margin-top: 10px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>review</h1>
        <p id="word-pronunciation">[rɪˈvjuː] / "里VIEW"</p>
        
        <canvas id="wordCanvas" width="600" height="300"></canvas>
        <p id="interactive-instruction">点击画布或按钮开始动画</p>
        <button id="playButton">播放动画</button>

        <div class="explanation">
            <h2>单词拆解教学 📖</h2>
            <p>
                你好！今天我们来学习一个非常常用的单词：<span class="highlight">review</span>。
                我们可以把它拆成两部分来理解，就像搭积木一样！
            </p>
            <p>
                1. 前缀 <span class="highlight">re-</span>：这个前缀表示"<span class="highlight">再一次</span>"或者"<span class="highlight">往回</span>"的意思。想象一下，你想"再"玩一次游戏（replay），或者"回"到上一个画面（return）。
            </p>
            <p>
                2. 词根 <span class="highlight">view</span>：这个词根大家可能很熟悉，就是"<span class="highlight">看</span>"的意思。比如风景（view），或者电视（television - "远距离看"）。
            </p>
            <p>
                所以，把它们合在一起：<span class="highlight">re (再一次)</span> + <span class="highlight">view (看)</span> = <span class="highlight">review (再看一遍)</span>。
                这也就是"复习"、"审查"的意思啦！是不是很简单？
            </p>
            <h2>翻译与用法 ✍️</h2>
            <p>
                <b>及物动词 (vt.):</b>
                <ul>
                    <li><b>审查，审核:</b> The committee will <em>review</em> your application. (委员会将审查你的申请。)</li>
                    <li><b>复习:</b> You should <em>review</em> your notes before the exam. (考试前你应该复习笔记。)</li>
                    <li><b>评论:</b> A famous critic will <em>review</em> the new movie. (一位著名评论家将评论这部新电影。)</li>
                </ul>
            </p>
            <p>
                <b>名词 (n.):</b>
                <ul>
                    <li><b>审查，复习:</b> The project is under <em>review</em>. (这个项目正在审查中。)</li>
                    <li><b>评论:</b> The book received a good <em>review</em>. (这本书获得了好评。)</li>
                </ul>
            </p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('wordCanvas');
        const ctx = canvas.getContext('2d');
        const playButton = document.getElementById('playButton');

        let animationState = 'initial'; // initial, re, view, combined, finished
        let frame = 0;
        const totalFrames = 400;

        function drawInitial() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            
            // Draw a document
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 80, 100, 140);
            for(let i = 0; i < 5; i++) {
                ctx.beginPath();
                ctx.moveTo(260, 100 + i * 20);
                ctx.lineTo(340, 100 + i * 20);
                ctx.stroke();
            }

            // Draw an eye (representing "view")
            ctx.beginPath();
            ctx.ellipse(150, 150, 50, 25, 0, 0, Math.PI * 2);
            ctx.fillStyle = 'white';
            ctx.fill();
            ctx.stroke();
            ctx.beginPath();
            ctx.arc(150, 150, 10, 0, Math.PI * 2);
            ctx.fillStyle = '#333';
            ctx.fill();

            ctx.font = '30px Arial';
            ctx.fillText('看 (view)', 150, 250);
        }

        function animate() {
            frame++;
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Eye and Document
            drawInitial();
            
            if (animationState === 'initial') {
                drawInitial();
                return;
            }

            // Phase 1: "re-" appears
            if (frame > 50 && frame <= 150) {
                const alpha = Math.min(1, (frame - 50) / 50);
                ctx.font = 'bold 80px Arial';
                ctx.fillStyle = `rgba(217, 48, 37, ${alpha})`;
                ctx.fillText('re-', 300, 150);
                ctx.font = 'bold 30px Arial';
                ctx.fillText('再一次', 300, 200);
            }

            // Phase 2: "re-" moves to combine with view
            if (frame > 150 && frame <= 250) {
                const progress = (frame - 150) / 100;
                
                // Keep "re-"
                ctx.font = 'bold 80px Arial';
                ctx.fillStyle = `rgba(217, 48, 37, 1)`;
                ctx.fillText('re-', 300, 150);
                ctx.font = 'bold 30px Arial';
                ctx.fillText('再一次', 300, 200);

                // Draw arrow
                const startX = 200;
                const endX = 150;
                const currentX = startX + (endX - startX) * progress;
                ctx.beginPath();
                ctx.moveTo(200, 150);
                ctx.lineTo(currentX, 150);
                 ctx.strokeStyle = '#1a73e8';
                ctx.lineWidth = 5;
                ctx.stroke();
                
                // Arrowhead
                ctx.beginPath();
                ctx.moveTo(currentX, 150);
                ctx.lineTo(currentX + 10, 140);
                ctx.moveTo(currentX, 150);
                ctx.lineTo(currentX + 10, 160);
                ctx.stroke();
            }
            
            // Phase 3: Combined word appears
            if (frame > 250) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                const alpha = Math.min(1, (frame - 250) / 50);
                ctx.font = 'bold 90px Arial';
                ctx.fillStyle = `rgba(26, 115, 232, ${alpha})`;
                ctx.textAlign = 'center';
                ctx.fillText('review', canvas.width / 2, 150);

                ctx.font = '40px Arial';
                ctx.fillStyle = `rgba(51, 51, 51, ${alpha})`;
                ctx.fillText('再看一遍 = 复习 / 审查', canvas.width / 2, 230);
            }


            if (frame < totalFrames) {
                requestAnimationFrame(animate);
            } else {
                animationState = 'finished';
                 playButton.textContent = '重新播放';
            }
        }

        function startAnimation() {
            if (animationState === 'finished' || animationState === 'initial') {
                frame = 0;
                animationState = 'playing';
                playButton.textContent = '播放中...';
                requestAnimationFrame(animate);
            }
        }

        canvas.addEventListener('click', startAnimation);
        playButton.addEventListener('click', startAnimation);

        // Draw initial state
        drawInitial();
    </script>
</body>
</html> 