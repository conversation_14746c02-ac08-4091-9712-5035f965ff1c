<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：un-（否定、相反）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        }

        #mirrorCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .mirror-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .mirror-frame {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .mirror-frame::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.6s;
        }

        .mirror-frame:hover::before {
            left: 100%;
        }

        .mirror-frame:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .mirror-frame.revealed {
            opacity: 1;
            transform: translateY(0);
        }

        .word-reflection {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            position: relative;
        }

        .original-word {
            background: #28a745;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .original-word::after {
            content: '正面';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #218838;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .mirror-line {
            width: 3px;
            height: 60px;
            background: linear-gradient(to bottom, #silver, #gold, #silver);
            position: relative;
            margin: 0 20px;
        }

        .mirror-line::before {
            content: '🪞';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5rem;
            background: white;
            padding: 5px;
            border-radius: 50%;
        }

        .reversed-word {
            background: #dc3545;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            position: relative;
        }

        .reversed-word::after {
            content: '反面';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #c82333;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .prefix-highlight {
            background: #ffc107;
            color: #212529;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .meaning-explanation {
            background: rgba(102, 126, 234, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #495057;
        }

        .example-showcase {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #ffc107;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #ffc107;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .reflection-effect {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent 40%, rgba(255,255,255,0.3) 50%, transparent 60%);
            top: 0;
            left: -100%;
            transition: left 0.8s ease;
        }

        .mirror-frame:hover .reflection-effect {
            left: 100%;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes mirrorFlip {
            0% {
                transform: rotateY(0deg);
            }
            50% {
                transform: rotateY(90deg);
            }
            100% {
                transform: rotateY(0deg);
            }
        }

        @keyframes sparkle {
            0%, 100% {
                opacity: 0;
                transform: scale(0) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1) rotate(180deg);
            }
        }

        .interactive-hint {
            text-align: center;
            color: #667eea;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .magic-sparkles {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #ffd700;
            border-radius: 50%;
            pointer-events: none;
            animation: sparkle 2s infinite;
        }

        .progress-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .progress-step {
            width: 40px;
            height: 4px;
            background: #dee2e6;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            background: #667eea;
        }

        .progress-step.complete {
            background: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>魔法前缀：un-</h1>
            <p>在逆转镜的世界中发现词汇的另一面</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🪞 魔法逆转镜的故事</h2>
                <p>在一个神秘的魔法图书馆里，有一面古老的逆转镜。这面镜子有着神奇的力量：当你把任何一个词放在镜子前面时，镜子会显示出这个词的"相反面"。魔法师发现，在词的前面加上"un-"这个魔法前缀，就能创造出意思完全相反的新词！这就是逆转镜的秘密。</p>
            </div>

            <div class="canvas-container">
                <canvas id="mirrorCanvas"></canvas>
                <div class="progress-indicator" id="progressIndicator">
                    <div class="progress-step"></div>
                    <div class="progress-step"></div>
                    <div class="progress-step"></div>
                    <div class="progress-step"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择魔法逆转镜的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"逆转镜"的比喻，是因为"un-"前缀的核心作用就是"逆转"或"否定"原词的意思。镜子的反射特性完美地体现了这种"相反"的概念。当学生看到镜子中的反射时，能够直观地理解"un-"前缀如何将正面意思转换为负面意思。这种视觉化的对比帮助建立清晰的概念联系，让抽象的语法规则变得具体可感。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="startMirrorMagic()">启动逆转镜</button>
                <button class="btn" onclick="revealReflections()">显示反射</button>
                <button class="btn" onclick="resetMirror()">重置镜子</button>
            </div>

            <div class="interactive-hint">
                ✨ 点击"启动逆转镜"观看词汇逆转过程，点击镜框查看详细解释
            </div>
        </div>

        <div class="mirror-gallery" id="mirrorGallery">
            <div class="mirror-frame">
                <div class="reflection-effect"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Happy → Unhappy</h3>
                <div class="word-reflection">
                    <div class="original-word">happy</div>
                    <div class="mirror-line"></div>
                    <div class="reversed-word"><span class="prefix-highlight">un</span>happy</div>
                </div>
                <div class="meaning-explanation">
                    快乐的 → <span class="prefix-highlight">不</span>快乐的
                </div>
                <div class="example-showcase">
                    <strong>例句对比：</strong><br>
                    <strong>正面：</strong>She is happy today. (她今天很快乐。)<br>
                    <strong>反面：</strong>She is unhappy about the news. (她对这个消息感到不快。)<br>
                    <strong>解析：</strong>"happy"表示快乐，加上"un-"变成"unhappy"，表示不快乐、难过。
                </div>
            </div>

            <div class="mirror-frame">
                <div class="reflection-effect"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Lock → Unlock</h3>
                <div class="word-reflection">
                    <div class="original-word">lock</div>
                    <div class="mirror-line"></div>
                    <div class="reversed-word"><span class="prefix-highlight">un</span>lock</div>
                </div>
                <div class="meaning-explanation">
                    锁上 → <span class="prefix-highlight">解</span>锁
                </div>
                <div class="example-showcase">
                    <strong>例句对比：</strong><br>
                    <strong>正面：</strong>Please lock the door. (请锁上门。)<br>
                    <strong>反面：</strong>Can you unlock the phone? (你能解锁手机吗？)<br>
                    <strong>解析：</strong>"lock"表示锁上，加上"un-"变成"unlock"，表示解锁、打开。
                </div>
            </div>

            <div class="mirror-frame">
                <div class="reflection-effect"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Known → Unknown</h3>
                <div class="word-reflection">
                    <div class="original-word">known</div>
                    <div class="mirror-line"></div>
                    <div class="reversed-word"><span class="prefix-highlight">un</span>known</div>
                </div>
                <div class="meaning-explanation">
                    已知的 → <span class="prefix-highlight">未</span>知的
                </div>
                <div class="example-showcase">
                    <strong>例句对比：</strong><br>
                    <strong>正面：</strong>This is a known fact. (这是一个已知的事实。)<br>
                    <strong>反面：</strong>The future is unknown. (未来是未知的。)<br>
                    <strong>解析：</strong>"known"表示已知的，加上"un-"变成"unknown"，表示未知的、不为人知的。
                </div>
            </div>

            <div class="mirror-frame">
                <div class="reflection-effect"></div>
                <h3 style="text-align: center; color: #667eea; margin-bottom: 20px;">Fair → Unfair</h3>
                <div class="word-reflection">
                    <div class="original-word">fair</div>
                    <div class="mirror-line"></div>
                    <div class="reversed-word"><span class="prefix-highlight">un</span>fair</div>
                </div>
                <div class="meaning-explanation">
                    公平的 → <span class="prefix-highlight">不</span>公平的
                </div>
                <div class="example-showcase">
                    <strong>例句对比：</strong><br>
                    <strong>正面：</strong>The game is fair. (这个游戏是公平的。)<br>
                    <strong>反面：</strong>The decision seems unfair. (这个决定似乎不公平。)<br>
                    <strong>解析：</strong>"fair"表示公平的，加上"un-"变成"unfair"，表示不公平的、不合理的。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"un-"前缀表示否定、相反或逆转原词的意思。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别前缀：</strong>看到"un-"开头的词，先分离前缀和词根</li>
                <li><strong>理解词根：</strong>明确去掉"un-"后的词根含义</li>
                <li><strong>应用逆转：</strong>在词根意思前加上"不"、"非"、"未"等否定词</li>
                <li><strong>语境调整：</strong>根据句子语境选择最合适的中文表达</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象逆转镜的魔法，"un-"就像镜子一样，能够反射出词汇的相反面！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('mirrorCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentReflection = 0;
        let mirrorGlow = 0;
        let sparkles = [];
        
        const reflections = [
            { original: 'happy', reversed: 'unhappy', x: 150, y: 200 },
            { original: 'lock', reversed: 'unlock', x: 350, y: 250 },
            { original: 'known', reversed: 'unknown', x: 550, y: 200 },
            { original: 'fair', reversed: 'unfair', x: 750, y: 250 }
        ];

        class Sparkle {
            constructor(x, y) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 3;
                this.vy = (Math.random() - 0.5) * 3;
                this.life = 1;
                this.decay = 0.02;
                this.size = Math.random() * 3 + 1;
                this.rotation = 0;
                this.rotationSpeed = Math.random() * 0.2;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life -= this.decay;
                this.rotation += this.rotationSpeed;
                this.size *= 0.99;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life;
                ctx.translate(this.x, this.y);
                ctx.rotate(this.rotation);
                ctx.fillStyle = '#ffd700';
                ctx.fillRect(-this.size/2, -this.size/2, this.size, this.size);
                ctx.restore();
            }
        }

        function drawMirror() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const mirrorWidth = 200;
            const mirrorHeight = 300;
            
            // 镜框
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(centerX - mirrorWidth/2 - 20, centerY - mirrorHeight/2 - 20, mirrorWidth + 40, mirrorHeight + 40);
            
            // 镜面
            const gradient = ctx.createLinearGradient(centerX - mirrorWidth/2, centerY - mirrorHeight/2, 
                                                   centerX + mirrorWidth/2, centerY + mirrorHeight/2);
            gradient.addColorStop(0, '#E6E6FA');
            gradient.addColorStop(0.5, '#F0F8FF');
            gradient.addColorStop(1, '#E6E6FA');
            ctx.fillStyle = gradient;
            ctx.fillRect(centerX - mirrorWidth/2, centerY - mirrorHeight/2, mirrorWidth, mirrorHeight);
            
            // 镜面光泽效果
            if (animationState === 'reflecting') {
                ctx.save();
                ctx.globalAlpha = 0.3 + Math.sin(mirrorGlow) * 0.2;
                const glowGradient = ctx.createLinearGradient(centerX - mirrorWidth/2, centerY - mirrorHeight/2,
                                                            centerX + mirrorWidth/2, centerY + mirrorHeight/2);
                glowGradient.addColorStop(0, 'transparent');
                glowGradient.addColorStop(0.5, '#ffffff');
                glowGradient.addColorStop(1, 'transparent');
                ctx.fillStyle = glowGradient;
                ctx.fillRect(centerX - mirrorWidth/2, centerY - mirrorHeight/2, mirrorWidth, mirrorHeight);
                ctx.restore();
                mirrorGlow += 0.1;
            }
            
            // 装饰性边框
            ctx.strokeStyle = '#DAA520';
            ctx.lineWidth = 3;
            ctx.strokeRect(centerX - mirrorWidth/2 - 15, centerY - mirrorHeight/2 - 15, mirrorWidth + 30, mirrorHeight + 30);
        }

        function drawWordReflection() {
            if (currentReflection < reflections.length && animationState === 'reflecting') {
                const reflection = reflections[currentReflection];
                const centerX = canvas.width / 2;
                
                // 原词（左侧）
                ctx.fillStyle = '#28a745';
                ctx.fillRect(centerX - 250, reflection.y, 100, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(reflection.original, centerX - 200, reflection.y + 25);
                
                // 箭头
                ctx.fillStyle = '#667eea';
                ctx.beginPath();
                ctx.moveTo(centerX - 130, reflection.y + 20);
                ctx.lineTo(centerX - 110, reflection.y + 15);
                ctx.lineTo(centerX - 110, reflection.y + 25);
                ctx.closePath();
                ctx.fill();
                
                // 反射词（右侧）
                ctx.fillStyle = '#dc3545';
                ctx.fillRect(centerX + 150, reflection.y, 120, 40);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                
                // 高亮un-前缀
                ctx.fillStyle = '#ffc107';
                ctx.fillText('un', centerX + 185, reflection.y + 25);
                ctx.fillStyle = 'white';
                ctx.fillText(reflection.original, centerX + 225, reflection.y + 25);
            }
        }

        function createSparkleEffect(x, y) {
            for (let i = 0; i < 8; i++) {
                sparkles.push(new Sparkle(x, y));
            }
        }

        function updateSparkles() {
            sparkles = sparkles.filter(sparkle => {
                sparkle.update();
                sparkle.draw();
                return sparkle.life > 0;
            });
        }

        function updateProgressIndicator() {
            const steps = document.querySelectorAll('.progress-step');
            steps.forEach((step, index) => {
                step.classList.remove('active', 'complete');
                if (index < currentReflection) {
                    step.classList.add('complete');
                } else if (index === currentReflection && animationState === 'reflecting') {
                    step.classList.add('active');
                }
            });
        }

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const bgGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            bgGradient.addColorStop(0, '#f8f9fa');
            bgGradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = bgGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制魔法圆圈
            if (animationState === 'reflecting') {
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.arc(canvas.width / 2, canvas.height / 2, 250, 0, Math.PI * 2);
                ctx.stroke();
                ctx.setLineDash([]);
            }
            
            // 绘制镜子
            drawMirror();
            
            // 绘制词汇反射
            drawWordReflection();
            
            // 更新粒子效果
            updateSparkles();
            
            // 更新进度指示器
            updateProgressIndicator();
        }

        function animate() {
            drawScene();
            
            if (animationState === 'reflecting' && currentReflection < reflections.length) {
                // 创建魔法效果
                if (Math.random() < 0.1) {
                    createSparkleEffect(canvas.width / 2 + (Math.random() - 0.5) * 200, 
                                      canvas.height / 2 + (Math.random() - 0.5) * 200);
                }
                
                // 自动切换到下一个反射
                setTimeout(() => {
                    currentReflection++;
                    if (currentReflection >= reflections.length) {
                        animationState = 'completed';
                    }
                }, 2500);
            }
            
            requestAnimationFrame(animate);
        }

        function startMirrorMagic() {
            animationState = 'reflecting';
            currentReflection = 0;
            mirrorGlow = 0;
            sparkles = [];
        }

        function revealReflections() {
            const frames = document.querySelectorAll('.mirror-frame');
            frames.forEach((frame, index) => {
                setTimeout(() => {
                    frame.classList.add('revealed');
                }, index * 300);
            });
        }

        function resetMirror() {
            animationState = 'idle';
            currentReflection = 0;
            mirrorGlow = 0;
            sparkles = [];
            
            const frames = document.querySelectorAll('.mirror-frame');
            frames.forEach(frame => frame.classList.remove('revealed'));
        }

        // 初始化
        animate();

        // 点击镜框的交互
        document.querySelectorAll('.mirror-frame').forEach(frame => {
            frame.addEventListener('click', function() {
                this.style.animation = 'mirrorFlip 0.8s ease';
                setTimeout(() => {
                    this.style.animation = '';
                }, 800);
            });
        });
    </script>
</body>
</html>
