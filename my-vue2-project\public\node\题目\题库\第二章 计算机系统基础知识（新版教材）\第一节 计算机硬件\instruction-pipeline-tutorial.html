<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>计算机指令执行 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 0.8s ease-out;
        }

        .section h2 {
            font-size: 2rem;
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .instruction-demo {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px 0;
            min-height: 200px;
        }

        .instruction-box {
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin: 0 10px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .instruction-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }

        .phase {
            text-align: center;
            margin: 20px;
        }

        .phase-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #4a5568;
        }

        .time-display {
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            background: white;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f7fafc;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            line-height: 1.8;
            color: #718096;
            margin-bottom: 15px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 40px 0;
        }

        .comparison-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .comparison-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .comparison-item h4 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .result-display {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulsing {
            animation: pulse 1s infinite;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
        }

        .quiz-question {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2d3436;
            margin-bottom: 20px;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            background: white;
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: #00b894;
            color: white;
        }

        .quiz-option.wrong {
            background: #e17055;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ 计算机指令执行原理</h1>
            <p>通过动画和交互体验，深入理解顺序执行与流水线执行的区别</p>
        </div>

        <div class="section">
            <h2>📚 基础知识</h2>
            <div class="explanation">
                <h3>什么是指令执行？</h3>
                <p>计算机执行程序时，需要逐条处理指令。每条指令的执行包含三个基本步骤：</p>
                <div class="instruction-demo">
                    <div class="phase">
                        <div class="instruction-box" style="background: linear-gradient(135deg, #74b9ff, #0984e3);">
                            取指令
                        </div>
                        <div class="phase-title">取指令 (Fetch)</div>
                        <div class="time-display">4Δt</div>
                    </div>
                    <div style="font-size: 2rem; color: #667eea;">→</div>
                    <div class="phase">
                        <div class="instruction-box" style="background: linear-gradient(135deg, #55a3ff, #003d82);">
                            分析指令
                        </div>
                        <div class="phase-title">分析指令 (Decode)</div>
                        <div class="time-display">2Δt</div>
                    </div>
                    <div style="font-size: 2rem; color: #667eea;">→</div>
                    <div class="phase">
                        <div class="instruction-box" style="background: linear-gradient(135deg, #00b894, #00a085);">
                            执行指令
                        </div>
                        <div class="phase-title">执行指令 (Execute)</div>
                        <div class="time-display">3Δt</div>
                    </div>
                </div>
                <p><span class="highlight">总时间 = 4Δt + 2Δt + 3Δt = 9Δt</span></p>
            </div>
        </div>

        <div class="section">
            <h2>🔄 顺序执行方式</h2>
            <div class="canvas-container">
                <canvas id="sequentialCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="startSequentialDemo()">🎬 开始顺序执行演示</button>
                <button class="btn" onclick="resetSequentialDemo()">🔄 重置</button>
            </div>
            <div class="explanation">
                <h3>顺序执行特点：</h3>
                <p>• 必须完成一条指令的所有步骤后，才能开始下一条指令</p>
                <p>• 执行600条指令需要：<span class="highlight">9Δt × 600 = 5400Δt</span></p>
                <p>• 优点：简单易懂，逻辑清晰</p>
                <p>• 缺点：效率较低，资源利用率不高</p>
            </div>
        </div>

        <div class="section">
            <h2>⚡ 流水线执行方式</h2>
            <div class="canvas-container">
                <canvas id="pipelineCanvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="startPipelineDemo()">🚀 开始流水线演示</button>
                <button class="btn" onclick="resetPipelineDemo()">🔄 重置</button>
            </div>
            <div class="explanation">
                <h3>流水线执行特点：</h3>
                <p>• 同时进行：执行第i条，分析第i+1条，读取第i+2条</p>
                <p>• 执行600条指令需要：<span class="highlight">4Δt × 600 + 2Δt + 3Δt = 2405Δt</span></p>
                <p>• 优点：大幅提高效率，充分利用硬件资源</p>
                <p>• 缺点：设计复杂，需要处理数据依赖等问题</p>
            </div>
        </div>

        <div class="section">
            <h2>📊 效率对比</h2>
            <div class="comparison">
                <div class="comparison-item">
                    <h4>🐌 顺序执行</h4>
                    <div class="result-display">5400Δt</div>
                    <p>传统方式，一步一步执行</p>
                </div>
                <div class="comparison-item">
                    <h4>🚀 流水线执行</h4>
                    <div class="result-display">2405Δt</div>
                    <p>现代方式，并行处理</p>
                </div>
            </div>
            <div class="explanation">
                <h3>性能提升：</h3>
                <p>流水线执行比顺序执行快了：<span class="highlight">(5400 - 2405) / 5400 ≈ 55.5%</span></p>
                <p>这就是为什么现代处理器都采用流水线技术的原因！</p>
            </div>
        </div>

        <div class="quiz-section">
            <h2>🎯 知识检测</h2>
            <div class="quiz-question">
                按顺序方式从头到尾执行完600条指令所需时间为多少Δt？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="checkAnswer(this, false)">A. 2400Δt</div>
                <div class="quiz-option" onclick="checkAnswer(this, false)">B. 3000Δt</div>
                <div class="quiz-option" onclick="checkAnswer(this, false)">C. 3600Δt</div>
                <div class="quiz-option" onclick="checkAnswer(this, true)">D. 5400Δt</div>
            </div>
            <div id="quizResult" style="margin-top: 20px; font-weight: bold; text-align: center;"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let sequentialAnimationId;
        let pipelineAnimationId;
        let sequentialStep = 0;
        let pipelineStep = 0;

        // 顺序执行动画
        function startSequentialDemo() {
            const canvas = document.getElementById('sequentialCanvas');
            const ctx = canvas.getContext('2d');
            sequentialStep = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.font = '20px Microsoft YaHei';
                ctx.fillStyle = '#4a5568';
                ctx.textAlign = 'center';
                ctx.fillText('顺序执行演示 - 一次只能执行一个步骤', canvas.width/2, 30);
                
                // 绘制指令框
                const instructionWidth = 100;
                const instructionHeight = 60;
                const startX = 50;
                const startY = 80;
                const spacing = 120;
                
                // 绘制3条指令
                for (let i = 0; i < 3; i++) {
                    const x = startX + i * spacing * 3;
                    const y = startY;
                    
                    // 取指令
                    drawInstructionBox(ctx, x, y, instructionWidth, instructionHeight, 
                        `指令${i+1}`, '#74b9ff', sequentialStep >= i*3 + 1);
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText('取指令 4Δt', x + instructionWidth/2, y + instructionHeight + 20);
                    
                    // 分析指令
                    drawInstructionBox(ctx, x + spacing, y, instructionWidth, instructionHeight, 
                        `分析${i+1}`, '#55a3ff', sequentialStep >= i*3 + 2);
                    ctx.fillText('分析 2Δt', x + spacing + instructionWidth/2, y + instructionHeight + 20);
                    
                    // 执行指令
                    drawInstructionBox(ctx, x + spacing*2, y, instructionWidth, instructionHeight, 
                        `执行${i+1}`, '#00b894', sequentialStep >= i*3 + 3);
                    ctx.fillText('执行 3Δt', x + spacing*2 + instructionWidth/2, y + instructionHeight + 20);
                    
                    // 绘制箭头
                    if (i < 2) {
                        drawArrow(ctx, x + spacing*2 + instructionWidth + 10, y + instructionHeight/2, 
                                 x + spacing*3 - 10, y + instructionHeight/2);
                    }
                }
                
                // 显示当前时间
                ctx.font = '18px Microsoft YaHei';
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'left';
                ctx.fillText(`当前时间: ${sequentialStep * 3}Δt`, 50, 250);
                ctx.fillText(`总时间: ${9 * 3}Δt = 27Δt (3条指令)`, 50, 280);
                
                sequentialStep++;
                if (sequentialStep <= 9) {
                    sequentialAnimationId = setTimeout(animate, 800);
                }
            }
            
            animate();
        }
        
        // 流水线执行动画
        function startPipelineDemo() {
            const canvas = document.getElementById('pipelineCanvas');
            const ctx = canvas.getContext('2d');
            pipelineStep = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.font = '20px Microsoft YaHei';
                ctx.fillStyle = '#4a5568';
                ctx.textAlign = 'center';
                ctx.fillText('流水线执行演示 - 三个步骤同时进行', canvas.width/2, 30);
                
                // 绘制时间轴
                const timelineY = 80;
                const stepWidth = 80;
                const stepHeight = 50;
                
                // 绘制表头
                ctx.font = '14px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText('时间', 30, timelineY + 25);
                ctx.fillText('取指令', 30, timelineY + 80);
                ctx.fillText('分析', 30, timelineY + 140);
                ctx.fillText('执行', 30, timelineY + 200);
                
                // 绘制流水线步骤
                for (let t = 0; t < Math.min(pipelineStep + 1, 8); t++) {
                    const x = 80 + t * stepWidth;
                    
                    // 时间标签
                    ctx.fillStyle = '#667eea';
                    ctx.fillText(`${t+1}Δt`, x + stepWidth/2, timelineY + 25);
                    
                    // 取指令阶段
                    if (t < 5) {
                        drawPipelineBox(ctx, x, timelineY + 60, stepWidth-5, stepHeight, 
                            `指令${t+1}`, '#74b9ff', t <= pipelineStep);
                    }
                    
                    // 分析阶段
                    if (t >= 1 && t < 6) {
                        drawPipelineBox(ctx, x, timelineY + 120, stepWidth-5, stepHeight, 
                            `指令${t}`, '#55a3ff', t <= pipelineStep);
                    }
                    
                    // 执行阶段
                    if (t >= 2 && t < 7) {
                        drawPipelineBox(ctx, x, timelineY + 180, stepWidth-5, stepHeight, 
                            `指令${t-1}`, '#00b894', t <= pipelineStep);
                    }
                }
                
                // 显示说明
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = '#4a5568';
                ctx.textAlign = 'left';
                ctx.fillText(`当前时间: ${pipelineStep + 1}Δt`, 50, 320);
                ctx.fillText('流水线优势：多个指令同时在不同阶段执行', 50, 350);
                
                pipelineStep++;
                if (pipelineStep < 8) {
                    pipelineAnimationId = setTimeout(animate, 1000);
                }
            }
            
            animate();
        }
        
        // 绘制指令框
        function drawInstructionBox(ctx, x, y, width, height, text, color, active) {
            ctx.fillStyle = active ? color : '#e2e8f0';
            ctx.fillRect(x, y, width, height);
            
            ctx.fillStyle = active ? 'white' : '#a0aec0';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + width/2, y + height/2 + 5);
            
            if (active) {
                ctx.strokeStyle = color;
                ctx.lineWidth = 3;
                ctx.strokeRect(x-2, y-2, width+4, height+4);
            }
        }
        
        // 绘制流水线框
        function drawPipelineBox(ctx, x, y, width, height, text, color, active) {
            ctx.fillStyle = active ? color : '#f7fafc';
            ctx.fillRect(x, y, width, height);
            
            ctx.strokeStyle = active ? color : '#e2e8f0';
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            
            ctx.fillStyle = active ? 'white' : '#a0aec0';
            ctx.font = '12px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + width/2, y + height/2 + 4);
        }
        
        // 绘制箭头
        function drawArrow(ctx, fromX, fromY, toX, toY) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle - Math.PI/6), toY - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - 10 * Math.cos(angle + Math.PI/6), toY - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }
        
        // 重置动画
        function resetSequentialDemo() {
            clearTimeout(sequentialAnimationId);
            const canvas = document.getElementById('sequentialCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            sequentialStep = 0;
        }
        
        function resetPipelineDemo() {
            clearTimeout(pipelineAnimationId);
            const canvas = document.getElementById('pipelineCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            pipelineStep = 0;
        }
        
        // 检查答案
        function checkAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const result = document.getElementById('quizResult');
            
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('5400')) {
                    option.classList.add('correct');
                }
            });
            
            if (isCorrect) {
                result.innerHTML = '🎉 恭喜答对了！顺序执行需要 9Δt × 600 = 5400Δt';
                result.style.color = '#00b894';
            } else {
                result.innerHTML = '❌ 答案错误。正确答案是D：每条指令需要9Δt，600条指令需要5400Δt';
                result.style.color = '#e17055';
            }
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 添加一些交互提示
            const instructionBoxes = document.querySelectorAll('.instruction-box');
            instructionBoxes.forEach(box => {
                box.addEventListener('click', function() {
                    this.classList.add('pulsing');
                    setTimeout(() => {
                        this.classList.remove('pulsing');
                    }, 1000);
                });
            });
        });
    </script>
</body>
</html>
