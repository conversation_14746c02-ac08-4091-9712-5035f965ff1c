<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件需求分析 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .game-board {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-section {
            margin-bottom: 40px;
        }

        .question-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            text-align: center;
        }

        .pyramid-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 40px 0;
            position: relative;
        }

        .pyramid-level {
            margin: 10px 0;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .pyramid-level:hover {
            transform: scale(1.05);
        }

        .level-box {
            padding: 20px 40px;
            border-radius: 15px;
            color: white;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .level-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .level-box:hover::before {
            left: 100%;
        }

        .business-level {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            width: 300px;
        }

        .user-level {
            background: linear-gradient(45deg, #4834d4, #686de0);
            width: 400px;
        }

        .function-level {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
            width: 500px;
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            background: white;
        }

        .interactive-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
        }

        .demo-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .requirement-cards {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 30px 0;
        }

        .requirement-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            width: 300px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }

        .requirement-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px rgba(0,0,0,0.15);
        }

        .requirement-card.selected {
            border-color: #4834d4;
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .card-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .card-content {
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .feedback {
            margin-top: 20px;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            font-weight: bold;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .feedback.show {
            opacity: 1;
        }

        .feedback.correct {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
            color: white;
        }

        .feedback.incorrect {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .explanation {
            background: #e8f4fd;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #4834d4;
        }

        .explanation h3 {
            color: #4834d4;
            margin-bottom: 15px;
        }

        .explanation p {
            line-height: 1.6;
            margin-bottom: 10px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .reset-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
        }

        .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
        }

        .analysis-section {
            margin: 20px 0;
        }

        .analysis-section h4 {
            color: #4834d4;
            margin: 20px 0 10px 0;
            font-size: 1.1rem;
        }

        .analysis-section p {
            margin: 8px 0;
            line-height: 1.6;
        }

        .memory-tips {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
        }

        .memory-tips h4 {
            color: white;
            margin-bottom: 15px;
        }

        .memory-tips ul {
            list-style: none;
            padding: 0;
        }

        .memory-tips li {
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.3);
        }

        .memory-tips li:last-child {
            border-bottom: none;
        }

        .conversion-exercise {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }

        .exercise-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .exercise-card h4 {
            color: #ff6b6b;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.2rem;
        }

        .input-group {
            margin: 15px 0;
        }

        .input-group label {
            display: block;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .input-group textarea {
            width: 100%;
            min-height: 80px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #4834d4;
            box-shadow: 0 0 0 3px rgba(72, 52, 212, 0.1);
        }

        .check-btn {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 20px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 15px auto;
            display: block;
        }

        .check-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }

        .conversion-feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .conversion-feedback.show {
            opacity: 1;
        }

        .conversion-feedback.good {
            background: linear-gradient(45deg, #00d2d3, #54a0ff);
            color: white;
        }

        .conversion-feedback.needs-improvement {
            background: linear-gradient(45deg, #ffa726, #ff7043);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件需求分析</h1>
            <p class="subtitle">通过互动游戏学习业务需求、用户需求和功能需求的区别</p>
        </div>

        <div class="game-board">
            <div class="question-section">
                <div class="question-title">
                    📝 题目：字处理器拼写检查功能的需求分析
                </div>
                
                <div class="explanation">
                    <h3>🎯 学习目标</h3>
                    <p>理解软件需求的三个层次：<strong>业务需求</strong>、<strong>用户需求</strong>和<strong>功能需求</strong></p>
                    <p>通过字处理器的拼写检查功能实例，掌握如何区分不同类型的需求</p>
                </div>

                <div class="pyramid-container">
                    <div class="pyramid-level">
                        <div class="level-box business-level" onclick="showLevelInfo('business')">
                            <div class="card-title">业务需求 (Business Requirements)</div>
                            <div class="card-content">高层次的目标要求</div>
                        </div>
                    </div>
                    <div class="pyramid-level">
                        <div class="level-box user-level" onclick="showLevelInfo('user')">
                            <div class="card-title">用户需求 (User Requirements)</div>
                            <div class="card-content">用户必须完成的任务</div>
                        </div>
                    </div>
                    <div class="pyramid-level">
                        <div class="level-box function-level" onclick="showLevelInfo('function')">
                            <div class="card-title">功能需求 (Functional Requirements)</div>
                            <div class="card-content">开发人员必须实现的软件功能</div>
                        </div>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="animationCanvas" width="800" height="400"></canvas>
                </div>
            </div>

            <div class="interactive-demo">
                <div class="demo-title">🎮 互动练习：将下列需求分类到正确的类型</div>
                
                <div class="requirement-cards">
                    <div class="requirement-card" onclick="selectCard(this, 'user')" data-type="user">
                        <div class="card-title">需求A</div>
                        <div class="card-content">"找出文档中的拼写错误并提供一个替换项列表来供选择替换拼错的词"</div>
                    </div>
                    
                    <div class="requirement-card" onclick="selectCard(this, 'function')" data-type="function">
                        <div class="card-title">需求B</div>
                        <div class="card-content">"显示提供替换词的对话框以及实现整个文档范围的替换"</div>
                    </div>
                    
                    <div class="requirement-card" onclick="selectCard(this, 'business')" data-type="business">
                        <div class="card-title">需求C</div>
                        <div class="card-content">"用户能有效地纠正文档中的拼写错误"</div>
                    </div>
                </div>

                <div id="feedback" class="feedback"></div>
                
                <button class="reset-btn" onclick="resetGame()">🔄 重新开始</button>
            </div>

            <div class="explanation">
                <h3>📚 详细解析</h3>
                <div class="analysis-section">
                    <h4>🎯 业务需求 (Business Requirements)</h4>
                    <p><strong>定义：</strong>反映组织机构或客户对系统、产品高层次的目标要求</p>
                    <p><strong>特点：</strong>宏观、战略性、面向商业价值</p>
                    <p><strong>示例：</strong>"用户能有效地纠正文档中的拼写错误" - 这是产品的核心价值主张</p>

                    <h4>👤 用户需求 (User Requirements)</h4>
                    <p><strong>定义：</strong>描述用户使用产品必须要完成的任务</p>
                    <p><strong>特点：</strong>面向用户、描述用户行为和目标</p>
                    <p><strong>示例：</strong>"找出文档中的拼写错误并提供一个替换项列表来供选择替换拼错的词" - 描述了用户的具体操作流程</p>

                    <h4>⚙️ 功能需求 (Functional Requirements)</h4>
                    <p><strong>定义：</strong>定义开发人员必须实现的软件功能</p>
                    <p><strong>特点：</strong>技术性、具体、可实现</p>
                    <p><strong>示例：</strong>"显示提供替换词的对话框以及实现整个文档范围的替换" - 具体的技术实现要求</p>
                </div>

                <div class="memory-tips">
                    <h4>🧠 记忆技巧</h4>
                    <ul>
                        <li><strong>业务需求</strong> = "为什么要做" (Why) - 商业目标</li>
                        <li><strong>用户需求</strong> = "做什么" (What) - 用户任务</li>
                        <li><strong>功能需求</strong> = "怎么做" (How) - 技术实现</li>
                    </ul>
                </div>
            </div>

            <div class="interactive-demo">
                <div class="demo-title">🎯 进阶练习：需求层次转换</div>
                <div class="conversion-exercise">
                    <p>给定一个业务需求，尝试推导出对应的用户需求和功能需求：</p>
                    <div class="exercise-card">
                        <h4>业务需求：提高用户文档编辑效率</h4>
                        <div class="input-group">
                            <label>推导的用户需求：</label>
                            <textarea id="userRequirement" placeholder="请输入您认为对应的用户需求..."></textarea>
                        </div>
                        <div class="input-group">
                            <label>推导的功能需求：</label>
                            <textarea id="functionRequirement" placeholder="请输入您认为对应的功能需求..."></textarea>
                        </div>
                        <button class="check-btn" onclick="checkConversion()">检查答案</button>
                        <div id="conversionFeedback" class="conversion-feedback"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedCards = [];
        let gameCompleted = false;

        // Canvas动画
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationFrame = 0;

        function drawAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制需求层次结构动画
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 业务需求圆圈
            ctx.beginPath();
            ctx.arc(centerX, centerY - 100, 60 + Math.sin(animationFrame * 0.02) * 5, 0, 2 * Math.PI);
            ctx.fillStyle = '#ff6b6b';
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('业务需求', centerX, centerY - 100);
            
            // 用户需求圆圈
            ctx.beginPath();
            ctx.arc(centerX - 80, centerY + 50, 50 + Math.sin(animationFrame * 0.03) * 3, 0, 2 * Math.PI);
            ctx.fillStyle = '#4834d4';
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.fillText('用户需求', centerX - 80, centerY + 50);
            
            ctx.beginPath();
            ctx.arc(centerX + 80, centerY + 50, 50 + Math.sin(animationFrame * 0.03) * 3, 0, 2 * Math.PI);
            ctx.fillStyle = '#4834d4';
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.fillText('用户需求', centerX + 80, centerY + 50);
            
            // 功能需求圆圈
            for (let i = 0; i < 4; i++) {
                const angle = (i * Math.PI / 2) + animationFrame * 0.01;
                const x = centerX + Math.cos(angle) * 120;
                const y = centerY + 150 + Math.sin(angle) * 20;
                
                ctx.beginPath();
                ctx.arc(x, y, 30, 0, 2 * Math.PI);
                ctx.fillStyle = '#00d2d3';
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText('功能需求', x, y);
            }
            
            // 绘制连接线
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            
            // 业务需求到用户需求的连线
            ctx.beginPath();
            ctx.moveTo(centerX - 30, centerY - 60);
            ctx.lineTo(centerX - 50, centerY + 10);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(centerX + 30, centerY - 60);
            ctx.lineTo(centerX + 50, centerY + 10);
            ctx.stroke();
            
            animationFrame++;
            requestAnimationFrame(drawAnimation);
        }

        function showLevelInfo(level) {
            const info = {
                business: {
                    title: '业务需求 (Business Requirements)',
                    content: '反映组织机构或客户对系统的高层次目标要求。例如："用户能有效地纠正文档中的拼写错误"'
                },
                user: {
                    title: '用户需求 (User Requirements)', 
                    content: '描述用户使用产品必须完成的任务。例如："找出文档中的拼写错误并提供替换项列表"'
                },
                function: {
                    title: '功能需求 (Functional Requirements)',
                    content: '定义开发人员必须实现的具体软件功能。例如："显示替换词对话框和实现文档范围替换"'
                }
            };
            
            alert(`${info[level].title}\n\n${info[level].content}`);
        }

        function selectCard(card, type) {
            if (gameCompleted) return;
            
            card.classList.toggle('selected');
            
            const cardIndex = Array.from(card.parentNode.children).indexOf(card);
            const existingIndex = selectedCards.findIndex(item => item.index === cardIndex);
            
            if (existingIndex > -1) {
                selectedCards.splice(existingIndex, 1);
            } else {
                selectedCards.push({ index: cardIndex, type: type, element: card });
            }
            
            if (selectedCards.length === 3) {
                checkAnswers();
            }
        }

        function checkAnswers() {
            const correctAnswers = ['user', 'function', 'business'];
            const userAnswers = selectedCards.sort((a, b) => a.index - b.index).map(item => item.type);
            
            const feedback = document.getElementById('feedback');
            
            if (JSON.stringify(userAnswers) === JSON.stringify(correctAnswers)) {
                feedback.textContent = '🎉 恭喜！答案完全正确！';
                feedback.className = 'feedback correct show';
                gameCompleted = true;
                
                // 添加成功动画
                selectedCards.forEach(item => {
                    item.element.classList.add('pulse');
                });
            } else {
                feedback.textContent = '❌ 答案不正确，请重新思考各需求的层次关系';
                feedback.className = 'feedback incorrect show';
                
                setTimeout(() => {
                    resetGame();
                }, 2000);
            }
        }

        function resetGame() {
            selectedCards = [];
            gameCompleted = false;
            
            document.querySelectorAll('.requirement-card').forEach(card => {
                card.classList.remove('selected', 'pulse');
            });
            
            const feedback = document.getElementById('feedback');
            feedback.className = 'feedback';
            feedback.textContent = '';
        }

        // 启动动画
        drawAnimation();
        
        function checkConversion() {
            const userReq = document.getElementById('userRequirement').value.trim();
            const funcReq = document.getElementById('functionRequirement').value.trim();
            const feedback = document.getElementById('conversionFeedback');

            if (!userReq || !funcReq) {
                feedback.textContent = '请填写完整的用户需求和功能需求';
                feedback.className = 'conversion-feedback needs-improvement show';
                return;
            }

            // 简单的关键词检查
            const userKeywords = ['用户', '操作', '任务', '完成', '使用', '编辑', '输入', '查看'];
            const funcKeywords = ['系统', '实现', '功能', '界面', '按钮', '对话框', '算法', '处理'];

            const userScore = userKeywords.filter(keyword => userReq.includes(keyword)).length;
            const funcScore = funcKeywords.filter(keyword => funcReq.includes(keyword)).length;

            let feedbackText = '';
            let feedbackClass = '';

            if (userScore >= 2 && funcScore >= 2) {
                feedbackText = '🎉 很好！您的回答体现了需求层次的区别。用户需求关注用户行为，功能需求关注技术实现。';
                feedbackClass = 'conversion-feedback good show';
            } else if (userScore >= 1 || funcScore >= 1) {
                feedbackText = '👍 不错的尝试！建议：用户需求多描述"用户做什么"，功能需求多描述"系统如何实现"。';
                feedbackClass = 'conversion-feedback needs-improvement show';
            } else {
                feedbackText = '💡 提示：用户需求应该描述用户的操作和任务，功能需求应该描述系统的具体实现方式。';
                feedbackClass = 'conversion-feedback needs-improvement show';
            }

            feedback.textContent = feedbackText;
            feedback.className = feedbackClass;

            // 显示参考答案
            setTimeout(() => {
                const referenceAnswer = `
📖 参考答案：
用户需求示例：
• 用户能够快速找到并选择常用的编辑工具
• 用户能够通过快捷键快速执行常用操作
• 用户能够自定义工作界面布局

功能需求示例：
• 系统提供可自定义的工具栏
• 系统支持快捷键配置功能
• 系统实现拖拽式界面布局调整
                `;

                if (confirm('查看参考答案？')) {
                    alert(referenceAnswer);
                }
            }, 2000);
        }

        // 页面加载完成后的欢迎动画
        window.addEventListener('load', () => {
            setTimeout(() => {
                alert('🎓 欢迎来到软件需求分析学习页面！\n\n点击金字塔的各个层次了解详细信息，然后完成下方的分类练习。\n\n记住：业务需求→用户需求→功能需求，层次递减，具体程度递增！');
            }, 1000);
        });
    </script>
</body>
</html>
