<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件需求工程 - 需求基线学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .title {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-50px);
            animation: slideInDown 1.2s ease-out forwards;
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.4rem;
            opacity: 0;
            animation: fadeIn 1.5s ease-out 0.5s forwards;
        }

        .main-content {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            opacity: 0;
            transform: translateY(40px);
            animation: slideInUp 1.2s ease-out 1s forwards;
        }

        .question-area {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 50px;
            border: 3px solid #74b9ff;
            position: relative;
            overflow: hidden;
        }

        .question-area::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(116, 185, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        .question-text {
            font-size: 1.5rem;
            line-height: 2;
            color: #2d3436;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .blank-space {
            display: inline-block;
            min-width: 150px;
            height: 50px;
            border: 3px dashed #74b9ff;
            border-radius: 12px;
            margin: 0 8px;
            vertical-align: middle;
            background: white;
            transition: all 0.4s ease;
            position: relative;
            cursor: pointer;
        }

        .blank-space.filled {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            border: 3px solid #0984e3;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
            animation: fillAnimation 0.6s ease;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 50px 0;
        }

        .option-card {
            background: white;
            border-radius: 18px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 3px solid transparent;
            transform: translateY(30px);
            opacity: 0;
            animation: cardAppear 0.8s ease-out forwards;
        }

        .option-card:nth-child(1) { animation-delay: 1.3s; }
        .option-card:nth-child(2) { animation-delay: 1.5s; }
        .option-card:nth-child(3) { animation-delay: 1.7s; }
        .option-card:nth-child(4) { animation-delay: 1.9s; }

        .option-card:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: #74b9ff;
        }

        .option-card.correct {
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.8s ease;
        }

        .option-card.wrong {
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease;
        }

        .option-letter {
            display: inline-block;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            border-radius: 50%;
            line-height: 50px;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
        }

        .option-text {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 15px;
        }

        .option-desc {
            font-size: 1rem;
            color: #636e72;
            line-height: 1.6;
        }

        .canvas-section {
            margin: 50px 0;
            text-align: center;
        }

        #processCanvas {
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: white;
            border: 2px solid #ddd;
        }

        .explanation-panel {
            background: linear-gradient(135deg, #e8f4fd 0%, #f8f9ff 100%);
            border-radius: 20px;
            padding: 40px;
            margin-top: 50px;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .explanation-panel.show {
            opacity: 1;
            transform: translateY(0);
        }

        .explanation-title {
            color: #0984e3;
            font-size: 2rem;
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }

        .explanation-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            border-radius: 2px;
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            border-left: 5px solid #74b9ff;
            transition: transform 0.3s ease;
        }

        .concept-card:hover {
            transform: translateY(-5px);
        }

        .concept-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .concept-title {
            color: #2d3436;
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .concept-text {
            color: #636e72;
            line-height: 1.7;
            font-size: 1.1rem;
        }

        .progress-container {
            margin: 30px 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(116, 185, 255, 0.2);
            border-radius: 6px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            width: 0%;
            transition: width 0.8s ease;
            border-radius: 6px;
        }

        .reset-button {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 30px auto;
            display: block;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(116, 185, 255, 0.3);
        }

        .reset-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(116, 185, 255, 0.4);
        }

        @keyframes slideInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        @keyframes cardAppear {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @keyframes fillAnimation {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-element {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-element" style="top: 10%; left: 10%; font-size: 2rem;">📋</div>
        <div class="floating-element" style="top: 20%; right: 15%; font-size: 1.5rem;">⚙️</div>
        <div class="floating-element" style="bottom: 30%; left: 20%; font-size: 2.5rem;">📊</div>
        <div class="floating-element" style="bottom: 20%; right: 10%; font-size: 2rem;">🎯</div>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">🎯 软件需求工程</h1>
            <p class="subtitle">探索需求管理的核心概念 - 需求基线</p>
        </div>

        <div class="main-content">
            <div class="question-area">
                <div class="question-text">
                    在软件需求工程中，需求管理贯穿整个过程。需求管理最基本的任务是明确需求，并使项目团队和用户达成共识，即建立<span class="blank-space" id="answerBlank"></span>。
                </div>
                
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressBar"></div>
                    </div>
                    <p style="color: #636e72; margin-top: 10px;">点击正确答案完成填空</p>
                </div>
            </div>

            <div class="options-grid">
                <div class="option-card" data-option="A" data-correct="false">
                    <div class="option-letter">A</div>
                    <div class="option-text">需求跟踪说明</div>
                    <div class="option-desc">用于跟踪需求在整个开发过程中的变化和实现状态</div>
                </div>

                <div class="option-card" data-option="B" data-correct="false">
                    <div class="option-letter">B</div>
                    <div class="option-text">需求变更管理文档</div>
                    <div class="option-desc">管理和控制需求变更的流程和规范文档</div>
                </div>

                <div class="option-card" data-option="C" data-correct="false">
                    <div class="option-letter">C</div>
                    <div class="option-text">需求分析计划</div>
                    <div class="option-desc">制定需求分析活动的详细计划和时间安排</div>
                </div>

                <div class="option-card" data-option="D" data-correct="true">
                    <div class="option-letter">D</div>
                    <div class="option-text">需求基线</div>
                    <div class="option-desc">经过评审和批准的需求文档，作为后续工作的基础</div>
                </div>
            </div>

            <div class="canvas-section">
                <canvas id="processCanvas" width="900" height="500"></canvas>
            </div>

            <button class="reset-button" onclick="resetGame()">🔄 重新开始学习</button>
        </div>

        <div class="explanation-panel" id="explanationPanel">
            <h2 class="explanation-title">📚 深度解析：需求基线</h2>

            <div class="concept-grid">
                <div class="concept-card">
                    <span class="concept-icon">🎯</span>
                    <h3 class="concept-title">什么是需求基线？</h3>
                    <p class="concept-text">需求基线是经过正式评审、批准和确认的需求文档集合，它代表了项目团队和用户对系统功能的共同理解和约定。</p>
                </div>

                <div class="concept-card">
                    <span class="concept-icon">🔒</span>
                    <h3 class="concept-title">基线的重要性</h3>
                    <p class="concept-text">建立需求基线后，任何变更都需要通过正式的变更控制流程，这确保了项目的稳定性和可控性。</p>
                </div>

                <div class="concept-card">
                    <span class="concept-icon">👥</span>
                    <h3 class="concept-title">达成共识</h3>
                    <p class="concept-text">需求基线的建立过程需要项目团队、用户和利益相关者的共同参与，确保所有人对需求有一致的理解。</p>
                </div>

                <div class="concept-card">
                    <span class="concept-icon">📋</span>
                    <h3 class="concept-title">基线内容</h3>
                    <p class="concept-text">包括功能需求、非功能需求、约束条件、验收标准等完整的需求规格说明。</p>
                </div>

                <div class="concept-card">
                    <span class="concept-icon">🔄</span>
                    <h3 class="concept-title">变更管理</h3>
                    <p class="concept-text">基线建立后，所有需求变更都必须经过评估、批准和记录，保证项目的可追溯性。</p>
                </div>

                <div class="concept-card">
                    <span class="concept-icon">✅</span>
                    <h3 class="concept-title">质量保证</h3>
                    <p class="concept-text">需求基线为后续的设计、开发、测试等活动提供了明确的依据和标准。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let gameCompleted = false;
        let selectedOption = null;

        // 获取画布和上下文
        const canvas = document.getElementById('processCanvas');
        const ctx = canvas.getContext('2d');

        // 动画变量
        let animationFrame = 0;
        let particles = [];
        let processSteps = [];

        // 初始化游戏
        function initGame() {
            setupEventListeners();
            initProcessSteps();
            startAnimation();
            showExplanation();
        }

        // 设置事件监听器
        function setupEventListeners() {
            const optionCards = document.querySelectorAll('.option-card');
            optionCards.forEach(card => {
                card.addEventListener('click', handleOptionClick);
            });
        }

        // 初始化流程步骤
        function initProcessSteps() {
            processSteps = [
                { name: '需求收集', x: 150, y: 150, color: '#74b9ff', active: false },
                { name: '需求分析', x: 350, y: 150, color: '#fd79a8', active: false },
                { name: '需求评审', x: 550, y: 150, color: '#fdcb6e', active: false },
                { name: '建立基线', x: 750, y: 150, color: '#00b894', active: false },
                { name: '变更管理', x: 450, y: 350, color: '#e17055', active: false }
            ];
        }

        // 处理选项点击
        function handleOptionClick(event) {
            if (gameCompleted) return;

            const card = event.currentTarget;
            const isCorrect = card.dataset.correct === 'true';

            // 重置所有卡片状态
            document.querySelectorAll('.option-card').forEach(c => {
                c.classList.remove('correct', 'wrong');
            });

            if (isCorrect) {
                card.classList.add('correct');
                fillAnswer('需求基线');
                updateProgress(100);
                gameCompleted = true;
                selectedOption = card;

                // 激活所有流程步骤
                processSteps.forEach((step, index) => {
                    setTimeout(() => {
                        step.active = true;
                        createSuccessParticles(step.x, step.y);
                    }, index * 500);
                });

                setTimeout(() => {
                    showCompletionMessage();
                }, 3000);

            } else {
                card.classList.add('wrong');
                setTimeout(() => {
                    card.classList.remove('wrong');
                }, 800);
            }
        }

        // 填充答案
        function fillAnswer(text) {
            const blank = document.getElementById('answerBlank');
            blank.textContent = text;
            blank.classList.add('filled');
        }

        // 更新进度条
        function updateProgress(percentage) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
        }

        // 创建成功粒子效果
        function createSuccessParticles(x, y) {
            for (let i = 0; i < 20; i++) {
                particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 8,
                    vy: (Math.random() - 0.5) * 8,
                    life: 80,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`,
                    size: Math.random() * 4 + 2
                });
            }
        }

        // 开始动画循环
        function startAnimation() {
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制背景
                drawBackground();

                // 绘制需求管理流程
                drawRequirementsProcess();

                // 绘制粒子效果
                drawParticles();

                animationFrame++;
                requestAnimationFrame(animate);
            }
            animate();
        }

        // 绘制背景
        function drawBackground() {
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9ff');
            gradient.addColorStop(1, '#e8f4fd');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // 绘制需求管理流程
        function drawRequirementsProcess() {
            // 绘制标题
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('需求管理流程', canvas.width / 2, 50);

            // 绘制流程步骤之间的连接线
            drawProcessConnections();

            // 绘制每个流程步骤
            processSteps.forEach((step, index) => {
                drawProcessStep(step, index);
            });

            // 绘制基线说明
            if (gameCompleted) {
                drawBaselineExplanation();
            }
        }

        // 绘制流程连接线
        function drawProcessConnections() {
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 3;

            // 主流程线
            for (let i = 0; i < processSteps.length - 2; i++) {
                const current = processSteps[i];
                const next = processSteps[i + 1];

                ctx.beginPath();
                ctx.moveTo(current.x + 40, current.y);
                ctx.lineTo(next.x - 40, next.y);
                ctx.stroke();

                // 绘制箭头
                drawArrow(next.x - 40, next.y, 0);
            }

            // 从建立基线到变更管理的连接线
            ctx.beginPath();
            ctx.moveTo(processSteps[3].x, processSteps[3].y + 40);
            ctx.lineTo(processSteps[4].x, processSteps[4].y - 40);
            ctx.stroke();
            drawArrow(processSteps[4].x, processSteps[4].y - 40, Math.PI / 2);
        }

        // 绘制箭头
        function drawArrow(x, y, angle) {
            ctx.save();
            ctx.translate(x, y);
            ctx.rotate(angle);
            ctx.beginPath();
            ctx.moveTo(-10, -5);
            ctx.lineTo(0, 0);
            ctx.lineTo(-10, 5);
            ctx.stroke();
            ctx.restore();
        }

        // 绘制流程步骤
        function drawProcessStep(step, index) {
            const isActive = step.active || (!gameCompleted && index === 0);
            const radius = 40;

            // 绘制圆形背景
            ctx.beginPath();
            ctx.arc(step.x, step.y, radius, 0, Math.PI * 2);

            if (isActive) {
                const gradient = ctx.createRadialGradient(step.x, step.y, 0, step.x, step.y, radius);
                gradient.addColorStop(0, step.color);
                gradient.addColorStop(1, step.color + '80');
                ctx.fillStyle = gradient;

                // 添加脉动效果
                const pulse = Math.sin(animationFrame * 0.1 + index) * 5;
                ctx.arc(step.x, step.y, radius + pulse, 0, Math.PI * 2);
            } else {
                ctx.fillStyle = '#f1f2f6';
            }

            ctx.fill();
            ctx.strokeStyle = isActive ? step.color : '#ddd';
            ctx.lineWidth = 3;
            ctx.stroke();

            // 绘制步骤编号
            ctx.fillStyle = isActive ? 'white' : '#636e72';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText((index + 1).toString(), step.x, step.y + 6);

            // 绘制步骤名称
            ctx.fillStyle = isActive ? step.color : '#636e72';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText(step.name, step.x, step.y + radius + 25);

            // 特殊标记需求基线步骤
            if (index === 3 && gameCompleted) {
                ctx.fillStyle = '#00b894';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.fillText('✓ 正确答案', step.x, step.y + radius + 45);
            }
        }

        // 绘制基线说明
        function drawBaselineExplanation() {
            const centerX = canvas.width / 2;
            const centerY = 280;

            // 绘制说明框
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.strokeStyle = '#00b894';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.roundRect(centerX - 200, centerY - 40, 400, 80, 10);
            ctx.fill();
            ctx.stroke();

            // 绘制说明文字
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('需求基线：项目团队和用户达成共识的基础', centerX, centerY - 10);
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('为后续开发活动提供稳定的需求依据', centerX, centerY + 15);
        }

        // 绘制粒子效果
        function drawParticles() {
            particles = particles.filter(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;
                particle.vx *= 0.98;
                particle.vy *= 0.98;

                ctx.globalAlpha = particle.life / 80;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalAlpha = 1;

                return particle.life > 0;
            });
        }

        // 显示完成消息
        function showCompletionMessage() {
            setTimeout(() => {
                alert('🎉 恭喜！您已经掌握了需求基线的概念！\n\n✅ 正确答案：D - 需求基线\n\n需求基线是需求管理的核心，它确保项目团队和用户对需求有统一的理解，为项目成功奠定基础。');
            }, 500);
        }

        // 显示详细解释
        function showExplanation() {
            const explanationPanel = document.getElementById('explanationPanel');
            setTimeout(() => {
                explanationPanel.classList.add('show');
            }, 2500);
        }

        // 重置游戏
        function resetGame() {
            gameCompleted = false;
            selectedOption = null;

            // 重置答案空白
            const blank = document.getElementById('answerBlank');
            blank.textContent = '';
            blank.classList.remove('filled');

            // 重置选项卡片
            document.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('correct', 'wrong');
            });

            // 重置进度条
            updateProgress(0);

            // 重置流程步骤
            processSteps.forEach(step => {
                step.active = false;
            });

            // 清除粒子
            particles = [];
        }

        // Canvas roundRect polyfill
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
            };
        }

        // 初始化游戏
        window.addEventListener('load', initGame);
    </script>
</body>
</html>
