<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流水线吞吐率交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 800px;
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            text-align: center;
            color: #1a2a4c;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 18px;
            margin-bottom: 15px;
            border-bottom: 2px solid #e8e8e8;
            padding-bottom: 10px;
        }
        .question-box {
            background-color: #f9f9f9;
            border: 1px solid #e8e8e8;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .question-box p { margin: 0; }
        .options { margin-top: 15px; padding-left: 20px; }
        .correct-answer { color: #4CAF50; font-weight: bold; }
        .interactive-section { text-align: center; }
        canvas {
            border: 1px solid #ccc;
            border-radius: 8px;
            background: linear-gradient(to bottom, #eef2f7, #ffffff);
        }
        .controls {
            margin: 15px 0;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
        }
        button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }
        button:hover {
            background-color: #218838;
            transform: translateY(-2px);
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }
        #resetBtn {
            background-color: #dc3545;
        }
        #resetBtn:hover {
            background-color: #c82333;
        }
        .explanation {
            margin-top: 10px;
            padding: 15px;
            background-color: #e9f5ff;
            border-left: 5px solid #007bff;
            text-align: left;
            border-radius: 0 8px 8px 0;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .stats {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>计算机流水线问题</h1>
    <div class="question-box">
        <p><strong>题目：</strong>流水线的吞吐率是指单位时间流水线处理的任务数，如果各段流水（线）的操作时间不同，则流水线的吞吐率是（ C ）的倒数。</p>
        <div class="options">
            A. 最短流水段操作时间 <br>
            B. 各段流水的操作时间总和 <br>
            C. <span class="correct-answer">最长流水段操作时间</span> <br>
            D. 流水段数乘以最长流水段操作时间
        </div>
    </div>
</div>

<div class="container interactive-section">
    <h2>交互式理解：流水线吞吐率</h2>
    <canvas id="pipelineCanvas" width="750" height="250"></canvas>
    <div class="controls">
        <button id="startBtn">启动流水线</button>
        <button id="resetBtn">重置</button>
        <div id="stats" class="stats">已完成任务: 0</div>
    </div>
    <div class="explanation" id="explanationText">
        <p>把一个任务分成几步，就像工厂里的装配线。点击"启动"看看会发生什么。</p>
    </div>
</div>

<script>
    // --- Canvas和动画逻辑 ---
    const canvas = document.getElementById('pipelineCanvas');
    const ctx = canvas.getContext('2d');
    const startBtn = document.getElementById('startBtn');
    const resetBtn = document.getElementById('resetBtn');
    const explanationText = document.getElementById('explanationText');
    const statsDiv = document.getElementById('stats');

    const STAGE_WIDTH = 150;
    const STAGE_GAP = 30;
    const TASK_HEIGHT = 40;
    const TASK_WIDTH = 60;
    
    // 流水线各阶段定义
    const stages = [
        { name: '取指令', duration: 2000, color: '#3498db' },
        { name: '译码', duration: 1000, color: '#f1c40f' },
        { name: '执行', duration: 4000, color: '#e74c3c' },
        { name: '写回', duration: 1500, color: '#2ecc71' }
    ];
    
    // 计算最长阶段时间，即流水线周期
    const pipelineCycle = Math.max(...stages.map(s => s.duration));
    const longestStageIndex = stages.findIndex(s => s.duration === pipelineCycle);

    let tasks = [];
    let completedTasks = 0;
    let pipelineInterval;
    let animationFrameId;

    // --- 绘图函数 ---
    function drawStage(stage, index, isLongest) {
        const x = STAGE_GAP + index * (STAGE_WIDTH + STAGE_GAP);
        const y = 50;
        ctx.strokeStyle = isLongest ? '#d35400' : '#bdc3c7';
        ctx.lineWidth = isLongest ? 4 : 2;
        ctx.strokeRect(x, y, STAGE_WIDTH, 150);
        
        ctx.fillStyle = '#34495e';
        ctx.font = 'bold 16px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(stage.name, x + STAGE_WIDTH / 2, y + 30);
        
        ctx.font = '14px sans-serif';
        ctx.fillText(`耗时: ${stage.duration / 1000}s`, x + STAGE_WIDTH / 2, y + 60);
    }

    function drawTask(task) {
        ctx.fillStyle = task.color;
        ctx.strokeStyle = '#2c3e50';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.roundRect(task.x, task.y, TASK_WIDTH, TASK_HEIGHT, [8]);
        ctx.fill();
        ctx.stroke();

        ctx.fillStyle = 'white';
        ctx.font = 'bold 14px sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(`任务${task.id}`, task.x + TASK_WIDTH / 2, task.y + TASK_HEIGHT / 2 + 1);
    }
    
    function drawAll() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        stages.forEach((s, i) => drawStage(s, i, i === longestStageIndex));
        tasks.forEach(drawTask);
    }

    // --- 动画与控制 ---
    function animate() {
        tasks.forEach(task => {
            const targetX = STAGE_GAP + task.stage * (STAGE_WIDTH + STAGE_GAP) + (STAGE_WIDTH - TASK_WIDTH) / 2;
            // 缓动动画
            task.x += (targetX - task.x) * 0.1;
        });
        drawAll();
        animationFrameId = requestAnimationFrame(animate);
    }

    let taskIdCounter = 0;
    function runPipelineStep() {
        // 1. 移动现有任务到下一阶段
        tasks.forEach(task => task.stage++);
        
        // 2. 移除完成的任务
        const newTasks = [];
        for (const task of tasks) {
            if (task.stage < stages.length) {
                newTasks.push(task);
            } else {
                completedTasks++;
            }
        }
        tasks = newTasks;

        // 3. 加入新任务
        taskIdCounter++;
        tasks.unshift({
            id: taskIdCounter,
            stage: 0,
            x: -TASK_WIDTH, // 从左侧屏幕外进入
            y: 120,
            color: `hsl(${taskIdCounter * 40 % 360}, 70%, 60%)`
        });
        
        statsDiv.textContent = `已完成任务: ${completedTasks}`;
    }

    function start() {
        if (pipelineInterval) return; // 防止重复启动
        startBtn.disabled = true;

        explanationText.innerHTML = `<p>流水线启动！请注意，整个生产线的节奏由<b>最慢的"执行"阶段（${pipelineCycle/1000}秒）</b>决定。</p>`;
        
        runPipelineStep(); // 立即执行第一步
        pipelineInterval = setInterval(runPipelineStep, pipelineCycle);
        
        if (!animationFrameId) {
            animate();
        }
    }

    function reset() {
        clearInterval(pipelineInterval);
        pipelineInterval = null;
        
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;

        tasks = [];
        completedTasks = 0;
        taskIdCounter = 0;
        
        statsDiv.textContent = `已完成任务: 0`;
        startBtn.disabled = false;
        explanationText.innerHTML = '<p>流水线已重置。一个任务被分成几个步骤，点击"启动"来观察效率如何。</p>';
        drawAll();
    }

    startBtn.addEventListener('click', start);
    resetBtn.addEventListener('click', reset);
    
    // 初始绘制
    drawAll();
</script>

<div class="container interactive-section">
    <h2>"倒数"是什么意思？</h2>
    <canvas id="reciprocalCanvas" width="750" height="200"></canvas>
    <div class="controls">
        <button id="reciprocalBtn" style="background-color: #007bff;">开始演示 "倒数"</button>
    </div>
    <div class="explanation" id="reciprocalExplanation">
        <p>我们已经知道，<b>流水线周期</b>（完成一个任务所需的时间）是4秒。现在，我们用动画看看"倒数"如何帮我们计算出<b>吞吐率</b>（每秒能完成多少任务）。</p>
    </div>
</div>

<script>
    // --- 倒数演示的逻辑 ---
    const rCanvas = document.getElementById('reciprocalCanvas');
    const rCtx = rCanvas.getContext('2d');
    const rBtn = document.getElementById('reciprocalBtn');
    const rExplanation = document.getElementById('reciprocalExplanation');

    const duration = 4; // 4秒
    let progress = 0; // 0 to duration
    let rAnimationFrameId;

    function drawReciprocalScene(progress) {
        rCtx.clearRect(0, 0, rCanvas.width, rCanvas.height);
        
        const barY = 50;
        const barHeight = 40;
        const totalWidth = rCanvas.width - 100;
        const startX = 50;

        // --- 上方的进度条：完成1个任务需要多久 ---
        rCtx.font = '16px sans-serif';
        rCtx.fillStyle = '#333';
        rCtx.textAlign = 'left';
        rCtx.fillText(`视角一：完成1个任务需要 ${duration} 秒`, startX, barY - 20);

        // 背景条
        rCtx.fillStyle = '#e9ecef';
        rCtx.fillRect(startX, barY, totalWidth, barHeight);
        
        // 进度条
        const progressWidth = (progress / duration) * totalWidth;
        rCtx.fillStyle = '#28a745';
        rCtx.fillRect(startX, barY, progressWidth, barHeight);
        
        // 时间标记
        for (let i = 0; i <= duration; i++) {
            const x = startX + (i / duration) * totalWidth;
            rCtx.fillStyle = '#888';
            rCtx.fillText(`${i}s`, x - 10, barY + barHeight + 20);
            rCtx.beginPath();
            rCtx.moveTo(x, barY + barHeight);
            rCtx.lineTo(x, barY + barHeight + 10);
            rCtx.stroke();
        }
        rCtx.fillStyle = '#fff';
        rCtx.textAlign = 'center';
        rCtx.fillText('1个任务', startX + totalWidth / 2, barY + barHeight/2 + 5);

        // --- 下方的结果：1秒能完成多少任务 ---
        const resultY = 150;
        rCtx.font = '16px sans-serif';
        rCtx.fillStyle = '#333';
        rCtx.textAlign = 'left';
        rCtx.fillText('视角二：1秒内能完成多少任务？(吞吐率)', startX, resultY - 20);
        
        const oneSecondMarkX = startX + (1 / duration) * totalWidth;

        // 灰色背景代表 "1个任务"
        rCtx.fillStyle = '#e9ecef';
        rCtx.fillRect(startX, resultY, totalWidth, barHeight);
        rCtx.fillStyle = '#333';
        rCtx.textAlign = 'center';
        rCtx.fillText('1个任务', startX + totalWidth / 2, resultY + barHeight/2 + 5);

        // 高亮 "1秒" 能完成的部分
        if (progress > 0) {
            const resultWidth = (Math.min(progress, 1.0) / 1.0) * oneSecondMarkX - startX;
            rCtx.fillStyle = '#007bff';
            rCtx.fillRect(startX, resultY, resultWidth, barHeight);
        }
        
        // 1秒的标记线
        rCtx.strokeStyle = '#e74c3c';
        rCtx.lineWidth = 2;
        rCtx.beginPath();
        rCtx.moveTo(oneSecondMarkX, resultY - 10);
        rCtx.lineTo(oneSecondMarkX, resultY + barHeight + 10);
        rCtx.stroke();
        rCtx.fillStyle = '#e74c3c';
        rCtx.fillText('1秒位置', oneSecondMarkX, resultY + barHeight + 25);
        
        if (progress >= 1) {
             rCtx.fillStyle = '#fff';
             rCtx.font = 'bold 16px sans-serif';
             rCtx.textAlign = 'center';
             rCtx.fillText('1/4', startX + (oneSecondMarkX - startX)/2 , resultY + barHeight/2 + 5);
        }
    }

    let startTime;
    function animateReciprocal(timestamp) {
        if (!startTime) startTime = timestamp;
        const elapsed = (timestamp - startTime) / 1000; // in seconds
        progress = elapsed;

        drawReciprocalScene(progress);

        if (progress <= duration) {
            rAnimationFrameId = requestAnimationFrame(animateReciprocal);
        } else {
            progress = duration;
            drawReciprocalScene(progress);
            rExplanation.innerHTML = `<p>动画结束！可以看到，当完成1个任务需要<b>4</b>秒时，在<b>1</b>秒的时间点，我们恰好完成了任务的 <b>1/4</b>。所以，吞吐率就是 <b>1/4</b> 任务/秒。<b>"吞吐率"</b>正是<b>"周期时间"</b>的<b>倒数</b>。</p>`;
            rBtn.innerText = '重新演示';
            rBtn.disabled = false;
        }
    }

    function startReciprocalDemo() {
        rBtn.disabled = true;
        rBtn.innerText = '演示中...';
        rExplanation.innerHTML = '<p>请看上方进度条，它完整走完代表"完成1个任务"，需要4秒。同时请观察下方，在1秒的位置会发生什么。</p>'
        
        startTime = null;
        progress = 0;
        if(rAnimationFrameId) {
            cancelAnimationFrame(rAnimationFrameId);
        }
        animateReciprocal();
    }

    rBtn.addEventListener('click', startReciprocalDemo);
    drawReciprocalScene(0);

</script>

</body>
</html>
