<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>String、StringBuffer和StringBuilder的区别</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        h1, h2 {
            color: #2c3e50;
            text-align: center;
        }
        
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            transition: background-color 0.3s;
        }
        
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
        
        .content {
            display: none;
        }
        
        .content.active {
            display: block;
        }
        
        .animation-container {
            width: 100%;
            height: 300px;
            margin: 20px 0;
            position: relative;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        
        canvas {
            background-color: #fff;
        }
        
        .control-panel {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            margin: 0 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .comparison-table th, .comparison-table td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: center;
        }
        
        .comparison-table th {
            background-color: #4CAF50;
            color: white;
        }
        
        .comparison-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .code-block {
            background-color: #f8f8f8;
            padding: 15px;
            border-left: 4px solid #4CAF50;
            font-family: monospace;
            margin: 15px 0;
            white-space: pre;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>String、StringBuffer和StringBuilder的区别</h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="overview">概述</div>
            <div class="tab" data-tab="string">String</div>
            <div class="tab" data-tab="stringbuffer">StringBuffer</div>
            <div class="tab" data-tab="stringbuilder">StringBuilder</div>
            <div class="tab" data-tab="comparison">比较</div>
        </div>
        
        <div id="overview" class="content active">
            <h2>Java字符串类型概述</h2>
            <p>Java提供了三种主要的字符串类型：</p>
            <ul>
                <li><strong>String</strong> - 不可变字符串</li>
                <li><strong>StringBuffer</strong> - 可变字符串，线程安全</li>
                <li><strong>StringBuilder</strong> - 可变字符串，非线程安全，性能更高</li>
            </ul>
            <p>点击下方按钮查看这三种类型的内存使用和操作效率的直观比较：</p>
            <div class="control-panel">
                <button id="start-overview-animation">开始动画演示</button>
                <button id="reset-overview-animation">重置演示</button>
            </div>
            <div class="animation-container">
                <canvas id="overview-canvas" width="900" height="300"></canvas>
            </div>
        </div>
        
        <div id="string" class="content">
            <h2>String（不可变字符串）</h2>
            <p>String是<span class="highlight">不可变的</span>，一旦创建，其包含的字符序列不可改变，直到被销毁。</p>
            <div class="code-block">String s = "abc";
s = s + "def"; // 不是修改原字符串，而是创建一个新的字符串对象</div>
            <p>每次对String的操作，如连接、裁剪等，都会生成一个新的String对象，而原来的String对象在没有引用的情况下会被垃圾回收。</p>
            <div class="control-panel">
                <button id="start-string-animation">演示String操作</button>
                <button id="reset-string-animation">重置演示</button>
            </div>
            <div class="animation-container">
                <canvas id="string-canvas" width="900" height="300"></canvas>
            </div>
        </div>
        
        <div id="stringbuffer" class="content">
            <h2>StringBuffer（线程安全的可变字符串）</h2>
            <p>StringBuffer代表一个<span class="highlight">可变的字符序列</span>。创建后，可以通过多种方法改变字符串内容。</p>
            <div class="code-block">StringBuffer sb = new StringBuffer("abc");
sb.append("def"); // 直接修改原对象，不创建新对象</div>
            <p>StringBuffer是<span class="highlight">线程安全的</span>，其方法都添加了synchronized关键字，适用于多线程环境。</p>
            <div class="control-panel">
                <button id="start-stringbuffer-animation">演示StringBuffer操作</button>
                <button id="reset-stringbuffer-animation">重置演示</button>
            </div>
            <div class="animation-container">
                <canvas id="stringbuffer-canvas" width="900" height="300"></canvas>
            </div>
        </div>
        
        <div id="stringbuilder" class="content">
            <h2>StringBuilder（非线程安全的可变字符串）</h2>
            <p>StringBuilder也代表<span class="highlight">可变的字符串</span>，与StringBuffer基本相似。</p>
            <div class="code-block">StringBuilder sb = new StringBuilder("abc");
sb.append("def"); // 直接修改原对象，不创建新对象</div>
            <p>不同之处是：StringBuilder<span class="highlight">不是线程安全的</span>，因此在单线程环境下性能比StringBuffer更好。</p>
            <div class="control-panel">
                <button id="start-stringbuilder-animation">演示StringBuilder操作</button>
                <button id="reset-stringbuilder-animation">重置演示</button>
            </div>
            <div class="animation-container">
                <canvas id="stringbuilder-canvas" width="900" height="300"></canvas>
            </div>
        </div>
        
        <div id="comparison" class="content">
            <h2>三者比较</h2>
            <table class="comparison-table">
                <tr>
                    <th>特性</th>
                    <th>String</th>
                    <th>StringBuffer</th>
                    <th>StringBuilder</th>
                </tr>
                <tr>
                    <td>可变性</td>
                    <td>不可变</td>
                    <td>可变</td>
                    <td>可变</td>
                </tr>
                <tr>
                    <td>线程安全</td>
                    <td>是（不可变所以线程安全）</td>
                    <td>是（synchronized）</td>
                    <td>否</td>
                </tr>
                <tr>
                    <td>性能</td>
                    <td>较低（每次操作创建新对象）</td>
                    <td>中等（线程安全开销）</td>
                    <td>较高</td>
                </tr>
                <tr>
                    <td>适用场景</td>
                    <td>字符串不经常变化</td>
                    <td>多线程环境下频繁修改</td>
                    <td>单线程环境下频繁修改</td>
                </tr>
            </table>
            <div class="control-panel">
                <button id="start-performance-test">性能比较测试</button>
            </div>
            <div class="animation-container">
                <canvas id="performance-canvas" width="900" height="300"></canvas>
            </div>
        </div>
    </div>

    <script>
        // 切换标签页的功能
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.content').forEach(c => c.classList.remove('active'));
                
                tab.classList.add('active');
                document.getElementById(tab.getAttribute('data-tab')).classList.add('active');
            });
        });

        // 概述页面的动画
        const overviewCanvas = document.getElementById('overview-canvas');
        const overviewCtx = overviewCanvas.getContext('2d');
        let overviewAnimation;

        document.getElementById('start-overview-animation').addEventListener('click', startOverviewAnimation);
        document.getElementById('reset-overview-animation').addEventListener('click', resetOverviewAnimation);

        function resetOverviewAnimation() {
            if (overviewAnimation) {
                cancelAnimationFrame(overviewAnimation);
            }
            overviewCtx.clearRect(0, 0, overviewCanvas.width, overviewCanvas.height);
            drawOverviewBackground();
        }

        function drawOverviewBackground() {
            overviewCtx.fillStyle = '#ffffff';
            overviewCtx.fillRect(0, 0, overviewCanvas.width, overviewCanvas.height);
            
            overviewCtx.fillStyle = '#2c3e50';
            overviewCtx.font = '18px Arial';
            overviewCtx.fillText('字符串操作性能比较', 350, 30);
            
            // 画坐标轴
            overviewCtx.beginPath();
            overviewCtx.moveTo(50, 250);
            overviewCtx.lineTo(850, 250);
            overviewCtx.stroke();
            
            overviewCtx.beginPath();
            overviewCtx.moveTo(50, 250);
            overviewCtx.lineTo(50, 50);
            overviewCtx.stroke();
            
            // 标记
            overviewCtx.fillText('操作次数', 400, 280);
            overviewCtx.save();
            overviewCtx.translate(20, 150);
            overviewCtx.rotate(-Math.PI/2);
            overviewCtx.fillText('执行时间', 0, 0);
            overviewCtx.restore();
        }

        function startOverviewAnimation() {
            resetOverviewAnimation();
            
            let step = 0;
            const maxSteps = 100;
            
            function animate() {
                if (step >= maxSteps) {
                    return;
                }
                
                step++;
                
                // String性能曲线（指数上升）
                overviewCtx.beginPath();
                overviewCtx.strokeStyle = 'red';
                overviewCtx.lineWidth = 3;
                for (let i = 0; i <= step; i++) {
                    const x = 50 + (i / maxSteps) * 800;
                    const y = 250 - (Math.pow(i / maxSteps, 2) * 200);
                    if (i === 0) {
                        overviewCtx.moveTo(x, y);
                    } else {
                        overviewCtx.lineTo(x, y);
                    }
                }
                overviewCtx.stroke();
                
                // StringBuffer性能曲线（线性上升但较陡）
                overviewCtx.beginPath();
                overviewCtx.strokeStyle = 'green';
                overviewCtx.lineWidth = 3;
                for (let i = 0; i <= step; i++) {
                    const x = 50 + (i / maxSteps) * 800;
                    const y = 250 - (i / maxSteps * 150);
                    if (i === 0) {
                        overviewCtx.moveTo(x, y);
                    } else {
                        overviewCtx.lineTo(x, y);
                    }
                }
                overviewCtx.stroke();
                
                // StringBuilder性能曲线（线性上升但较缓）
                overviewCtx.beginPath();
                overviewCtx.strokeStyle = 'blue';
                overviewCtx.lineWidth = 3;
                for (let i = 0; i <= step; i++) {
                    const x = 50 + (i / maxSteps) * 800;
                    const y = 250 - (i / maxSteps * 100);
                    if (i === 0) {
                        overviewCtx.moveTo(x, y);
                    } else {
                        overviewCtx.lineTo(x, y);
                    }
                }
                overviewCtx.stroke();
                
                // 添加图例
                overviewCtx.fillStyle = 'red';
                overviewCtx.fillRect(650, 50, 20, 10);
                overviewCtx.fillStyle = '#333';
                overviewCtx.fillText('String', 675, 60);
                
                overviewCtx.fillStyle = 'green';
                overviewCtx.fillRect(650, 70, 20, 10);
                overviewCtx.fillStyle = '#333';
                overviewCtx.fillText('StringBuffer', 675, 80);
                
                overviewCtx.fillStyle = 'blue';
                overviewCtx.fillRect(650, 90, 20, 10);
                overviewCtx.fillStyle = '#333';
                overviewCtx.fillText('StringBuilder', 675, 100);
                
                overviewAnimation = requestAnimationFrame(animate);
            }
            
            animate();
        }

        // String动画
        const stringCanvas = document.getElementById('string-canvas');
        const stringCtx = stringCanvas.getContext('2d');
        let stringAnimation;

        document.getElementById('start-string-animation').addEventListener('click', startStringAnimation);
        document.getElementById('reset-string-animation').addEventListener('click', resetStringAnimation);

        function resetStringAnimation() {
            if (stringAnimation) {
                cancelAnimationFrame(stringAnimation);
            }
            stringCtx.clearRect(0, 0, stringCanvas.width, stringCanvas.height);
            drawStringBackground();
        }

        function drawStringBackground() {
            stringCtx.fillStyle = '#ffffff';
            stringCtx.fillRect(0, 0, stringCanvas.width, stringCanvas.height);
            
            stringCtx.fillStyle = '#2c3e50';
            stringCtx.font = '18px Arial';
            stringCtx.fillText('String操作演示 - 每次操作创建新对象', 300, 30);
        }

        function startStringAnimation() {
            resetStringAnimation();
            
            let step = 0;
            const stringOperations = [
                { code: 'String s = "abc";', result: 'abc', objects: 1 },
                { code: 's = s + "def";', result: 'abcdef', objects: 2 },
                { code: 's = s + "ghi";', result: 'abcdefghi', objects: 3 },
                { code: 's = s.substring(3, 6);', result: 'def', objects: 4 }
            ];
            
            function drawMemoryBlock(x, y, width, height, content, isActive) {
                stringCtx.fillStyle = isActive ? '#4CAF50' : '#f1f1f1';
                stringCtx.strokeStyle = '#333';
                stringCtx.lineWidth = 2;
                
                // 绘制内存块
                stringCtx.fillRect(x, y, width, height);
                stringCtx.strokeRect(x, y, width, height);
                
                // 绘制文本
                stringCtx.fillStyle = isActive ? '#fff' : '#333';
                stringCtx.font = '16px Arial';
                stringCtx.textAlign = 'center';
                stringCtx.textBaseline = 'middle';
                stringCtx.fillText(content, x + width/2, y + height/2);
            }
            
            function animate() {
                if (step >= stringOperations.length) {
                    return;
                }
                
                stringCtx.clearRect(0, 0, stringCanvas.width, stringCanvas.height);
                drawStringBackground();
                
                // 绘制代码
                stringCtx.fillStyle = '#333';
                stringCtx.font = '16px monospace';
                stringCtx.textAlign = 'left';
                stringCtx.fillText(stringOperations[step].code, 100, 70);
                
                // 绘制堆内存中的对象
                for (let i = 0; i <= step; i++) {
                    const isActive = i === step;
                    const x = 100 + i * 180;
                    const y = 120;
                    const width = 150;
                    const height = 60;
                    
                    drawMemoryBlock(x, y, width, height, stringOperations[i].result, isActive);
                    
                    if (i < step) {
                        // 绘制垃圾回收标记
                        stringCtx.fillStyle = '#ff6b6b';
                        stringCtx.font = '14px Arial';
                        stringCtx.fillText('等待垃圾回收', x + 75, y + 90);
                    }
                }
                
                // 绘制栈中的引用
                stringCtx.fillStyle = '#333';
                stringCtx.font = '16px Arial';
                stringCtx.fillText('变量 s 引用:', 100, 240);
                
                // 绘制引用箭头
                stringCtx.beginPath();
                stringCtx.moveTo(200, 240);
                stringCtx.lineTo(100 + step * 180 + 75, 190);
                stringCtx.strokeStyle = '#4CAF50';
                stringCtx.lineWidth = 2;
                stringCtx.stroke();
                
                // 绘制对象数统计
                stringCtx.fillStyle = '#333';
                stringCtx.font = '16px Arial';
                stringCtx.fillText(`已创建对象数: ${stringOperations[step].objects}`, 600, 240);
                
                step++;
                setTimeout(() => {
                    stringAnimation = requestAnimationFrame(animate);
                }, 1500);
            }
            
            animate();
        }

        // StringBuffer动画
        const stringBufferCanvas = document.getElementById('stringbuffer-canvas');
        const stringBufferCtx = stringBufferCanvas.getContext('2d');
        let stringBufferAnimation;

        document.getElementById('start-stringbuffer-animation').addEventListener('click', startStringBufferAnimation);
        document.getElementById('reset-stringbuffer-animation').addEventListener('click', resetStringBufferAnimation);

        function resetStringBufferAnimation() {
            if (stringBufferAnimation) {
                cancelAnimationFrame(stringBufferAnimation);
            }
            stringBufferCtx.clearRect(0, 0, stringBufferCanvas.width, stringBufferCanvas.height);
            drawStringBufferBackground();
        }

        function drawStringBufferBackground() {
            stringBufferCtx.fillStyle = '#ffffff';
            stringBufferCtx.fillRect(0, 0, stringBufferCanvas.width, stringBufferCanvas.height);
            
            stringBufferCtx.fillStyle = '#2c3e50';
            stringBufferCtx.font = '18px Arial';
            stringBufferCtx.fillText('StringBuffer操作演示 - 线程安全的可变字符串', 300, 30);
        }

        function startStringBufferAnimation() {
            resetStringBufferAnimation();
            
            let step = 0;
            const sbOperations = [
                { code: 'StringBuffer sb = new StringBuffer("abc");', result: 'abc', threads: [] },
                { code: 'sb.append("def");', result: 'abcdef', threads: ['Thread 1'] },
                { code: 'sb.append("ghi");', result: 'abcdefghi', threads: ['Thread 1', 'Thread 2'] },
                { code: 'sb.replace(3, 6, "xyz");', result: 'abcxyzghi', threads: ['Thread 1', 'Thread 2', 'Thread 3'] }
            ];
            
            function drawSyncBlock(x, y, width, height, content, threads) {
                // 绘制同步块背景
                stringBufferCtx.fillStyle = '#4CAF50';
                stringBufferCtx.fillRect(x, y, width, height);
                stringBufferCtx.strokeStyle = '#333';
                stringBufferCtx.lineWidth = 2;
                stringBufferCtx.strokeRect(x, y, width, height);
                
                // 绘制锁图标
                stringBufferCtx.fillStyle = '#fff';
                stringBufferCtx.beginPath();
                stringBufferCtx.arc(x + 25, y + height/2, 15, 0, Math.PI * 2);
                stringBufferCtx.fill();
                stringBufferCtx.stroke();
                stringBufferCtx.fillStyle = '#4CAF50';
                stringBufferCtx.fillText('🔒', x + 18, y + height/2 + 7);
                
                // 绘制内容
                stringBufferCtx.fillStyle = '#fff';
                stringBufferCtx.font = '16px Arial';
                stringBufferCtx.textAlign = 'center';
                stringBufferCtx.textBaseline = 'middle';
                stringBufferCtx.fillText(content, x + width/2, y + height/2);
                
                // 绘制等待的线程
                stringBufferCtx.fillStyle = '#333';
                stringBufferCtx.textAlign = 'left';
                for (let i = 0; i < threads.length; i++) {
                    if (i === 0) {
                        stringBufferCtx.fillStyle = '#4CAF50';
                        stringBufferCtx.fillText(`${threads[i]} (执行中)`, x + 50, y + height + 30);
                    } else {
                        stringBufferCtx.fillStyle = '#ff9800';
                        stringBufferCtx.fillText(`${threads[i]} (等待锁)`, x + 50, y + height + 30 + i * 25);
                    }
                }
            }
            
            function animate() {
                if (step >= sbOperations.length) {
                    return;
                }
                
                stringBufferCtx.clearRect(0, 0, stringBufferCanvas.width, stringBufferCanvas.height);
                drawStringBufferBackground();
                
                // 绘制代码
                stringBufferCtx.fillStyle = '#333';
                stringBufferCtx.font = '16px monospace';
                stringBufferCtx.textAlign = 'left';
                stringBufferCtx.fillText(sbOperations[step].code, 100, 70);
                
                // 绘制StringBuffer对象
                const x = 100;
                const y = 120;
                const width = 300;
                const height = 60;
                
                drawSyncBlock(x, y, width, height, sbOperations[step].result, sbOperations[step].threads);
                
                // 绘制对象说明
                stringBufferCtx.fillStyle = '#333';
                stringBufferCtx.font = '16px Arial';
                stringBufferCtx.fillText('单个StringBuffer对象，通过synchronized关键字保证线程安全', 100, 250);
                
                step++;
                setTimeout(() => {
                    stringBufferAnimation = requestAnimationFrame(animate);
                }, 1500);
            }
            
            animate();
        }

        // StringBuilder动画
        const stringBuilderCanvas = document.getElementById('stringbuilder-canvas');
        const stringBuilderCtx = stringBuilderCanvas.getContext('2d');
        let stringBuilderAnimation;

        document.getElementById('start-stringbuilder-animation').addEventListener('click', startStringBuilderAnimation);
        document.getElementById('reset-stringbuilder-animation').addEventListener('click', resetStringBuilderAnimation);

        function resetStringBuilderAnimation() {
            if (stringBuilderAnimation) {
                cancelAnimationFrame(stringBuilderAnimation);
            }
            stringBuilderCtx.clearRect(0, 0, stringBuilderCanvas.width, stringBuilderCanvas.height);
            drawStringBuilderBackground();
        }

        function drawStringBuilderBackground() {
            stringBuilderCtx.fillStyle = '#ffffff';
            stringBuilderCtx.fillRect(0, 0, stringBuilderCanvas.width, stringBuilderCanvas.height);
            
            stringBuilderCtx.fillStyle = '#2c3e50';
            stringBuilderCtx.font = '18px Arial';
            stringBuilderCtx.fillText('StringBuilder操作演示 - 非线程安全的可变字符串', 300, 30);
        }

        function startStringBuilderAnimation() {
            resetStringBuilderAnimation();
            
            let step = 0;
            const sbOperations = [
                { code: 'StringBuilder sb = new StringBuilder("abc");', result: 'abc' },
                { code: 'sb.append("def");', result: 'abcdef' },
                { code: 'sb.append("ghi");', result: 'abcdefghi' },
                { code: 'sb.replace(3, 6, "xyz");', result: 'abcxyzghi' }
            ];
            
            function drawMemoryBlock(x, y, width, height, content, speed) {
                stringBuilderCtx.fillStyle = '#3498db';
                stringBuilderCtx.strokeStyle = '#333';
                stringBuilderCtx.lineWidth = 2;
                
                // 绘制内存块
                stringBuilderCtx.fillRect(x, y, width, height);
                stringBuilderCtx.strokeRect(x, y, width, height);
                
                // 绘制文本
                stringBuilderCtx.fillStyle = '#fff';
                stringBuilderCtx.font = '16px Arial';
                stringBuilderCtx.textAlign = 'center';
                stringBuilderCtx.textBaseline = 'middle';
                stringBuilderCtx.fillText(content, x + width/2, y + height/2);
                
                // 绘制速度指示器
                if (speed) {
                    stringBuilderCtx.fillStyle = '#333';
                    stringBuilderCtx.fillText(`执行速度: ${speed}`, x + width/2, y + height + 30);
                }
            }
            
            function animate() {
                if (step >= sbOperations.length) {
                    return;
                }
                
                stringBuilderCtx.clearRect(0, 0, stringBuilderCanvas.width, stringBuilderCanvas.height);
                drawStringBuilderBackground();
                
                // 绘制代码
                stringBuilderCtx.fillStyle = '#333';
                stringBuilderCtx.font = '16px monospace';
                stringBuilderCtx.textAlign = 'left';
                stringBuilderCtx.fillText(sbOperations[step].code, 100, 70);
                
                // 绘制StringBuilder对象
                const x = 100;
                const y = 120;
                const width = 300;
                const height = 60;
                let speed = '⚡️⚡️⚡️⚡️⚡️'; // 5颗星表示最快
                
                drawMemoryBlock(x, y, width, height, sbOperations[step].result, speed);
                
                // 绘制说明
                stringBuilderCtx.fillStyle = '#333';
                stringBuilderCtx.font = '16px Arial';
                stringBuilderCtx.fillText('单个StringBuilder对象，无同步锁，性能更优但不保证线程安全', 100, 250);
                
                // 绘制警告（在多线程环境下的风险）
                if (step > 0) {
                    stringBuilderCtx.fillStyle = '#ff6b6b';
                    stringBuilderCtx.fillText('⚠️ 警告：在多线程环境下可能导致数据不一致', 500, 180);
                }
                
                step++;
                setTimeout(() => {
                    stringBuilderAnimation = requestAnimationFrame(animate);
                }, 1500);
            }
            
            animate();
        }

        // 性能比较测试
        const performanceCanvas = document.getElementById('performance-canvas');
        const performanceCtx = performanceCanvas.getContext('2d');
        let performanceAnimation;

        document.getElementById('start-performance-test').addEventListener('click', startPerformanceTest);

        function startPerformanceTest() {
            if (performanceAnimation) {
                cancelAnimationFrame(performanceAnimation);
            }
            performanceCtx.clearRect(0, 0, performanceCanvas.width, performanceCanvas.height);
            
            performanceCtx.fillStyle = '#ffffff';
            performanceCtx.fillRect(0, 0, performanceCanvas.width, performanceCanvas.height);
            
            performanceCtx.fillStyle = '#2c3e50';
            performanceCtx.font = '18px Arial';
            performanceCtx.fillText('10000次字符串拼接操作性能比较', 320, 30);
            
            // 模拟进度条
            const results = [
                { type: 'String', time: '8.5秒', color: 'red' },
                { type: 'StringBuffer', time: '0.3秒', color: 'green' },
                { type: 'StringBuilder', time: '0.1秒', color: 'blue' }
            ];
            
            let progress = 0;
            const maxProgress = 100;
            
            function animate() {
                if (progress >= maxProgress) {
                    // 完成后显示结果
                    for (let i = 0; i < results.length; i++) {
                        const y = 80 + i * 60;
                        
                        performanceCtx.fillStyle = '#333';
                        performanceCtx.font = '16px Arial';
                        performanceCtx.textAlign = 'left';
                        performanceCtx.fillText(results[i].type, 100, y);
                        
                        performanceCtx.fillStyle = results[i].color;
                        
                        let width = 0;
                        if (i === 0) width = 700; // String - 最长
                        else if (i === 1) width = 200; // StringBuffer - 中等
                        else width = 100; // StringBuilder - 最短
                        
                        performanceCtx.fillRect(200, y - 15, width, 20);
                        
                        performanceCtx.fillStyle = '#333';
                        performanceCtx.textAlign = 'left';
                        performanceCtx.fillText(results[i].time, width + 210, y);
                    }
                    
                    // 添加解释
                    performanceCtx.fillStyle = '#333';
                    performanceCtx.font = '16px Arial';
                    performanceCtx.fillText('结论：', 100, 240);
                    performanceCtx.fillText('• String适合少量修改的场景', 120, 270);
                    performanceCtx.fillText('• StringBuffer适合多线程下频繁修改的场景', 120, 300);
                    performanceCtx.fillText('• StringBuilder适合单线程下频繁修改的场景，性能最佳', 120, 330);
                    return;
                }
                
                progress += 2;
                
                performanceCtx.clearRect(0, 0, performanceCanvas.width, performanceCanvas.height);
                performanceCtx.fillStyle = '#ffffff';
                performanceCtx.fillRect(0, 0, performanceCanvas.width, performanceCanvas.height);
                
                performanceCtx.fillStyle = '#2c3e50';
                performanceCtx.font = '18px Arial';
                performanceCtx.fillText('10000次字符串拼接操作性能比较', 320, 30);
                
                // 显示进度
                performanceCtx.fillStyle = '#333';
                performanceCtx.fillText(`测试进度: ${progress}%`, 400, 60);
                
                for (let i = 0; i < results.length; i++) {
                    const y = 100 + i * 50;
                    
                    performanceCtx.fillStyle = '#333';
                    performanceCtx.font = '16px Arial';
                    performanceCtx.fillText(results[i].type, 100, y);
                    
                    // 绘制进度条背景
                    performanceCtx.fillStyle = '#f1f1f1';
                    performanceCtx.fillRect(200, y - 15, 500, 20);
                    
                    // 绘制进度
                    let currentProgress = progress;
                    // String较慢，降低进度
                    if (i === 0) currentProgress = progress * 0.3;
                    // StringBuffer中等
                    else if (i === 1) currentProgress = progress * 0.8;
                    
                    performanceCtx.fillStyle = results[i].color;
                    performanceCtx.fillRect(200, y - 15, 5 * currentProgress, 20);
                }
                
                performanceAnimation = requestAnimationFrame(animate);
            }
            
            animate();
        }

        // 初始化所有动画背景
        drawOverviewBackground();
        drawStringBackground();
        drawStringBufferBackground();
        drawStringBuilderBackground();
    </script>
</body>
</html> 