<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库视图 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 80px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 60px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            opacity: 0;
            transform: translateY(50px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2.2rem;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        canvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .content-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .game-area {
            background: #f7fafc;
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .score {
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 20px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">数据库视图</h1>
            <p class="subtitle">简化SQL查询，提高开发效率的神奇工具</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是数据库视图？</h2>
            <p class="content-text">
                数据库视图就像是一个<span class="highlight">虚拟的表</span>，它不存储实际数据，而是基于一个或多个真实表的查询结果。
                想象一下，视图就像是给复杂数据穿上了一件<span class="highlight">简单易懂的外衣</span>！
            </p>
            <div class="canvas-container">
                <canvas id="viewConceptCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="animateViewConcept()">🎬 观看动画演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">视图的主要用途</h2>
            <p class="content-text">
                视图有两个核心作用：<span class="highlight">简化SQL查询</span>和<span class="highlight">提高开发效率</span>。
                另外还能帮助我们兼容老的表结构，真是一举多得！
            </p>
            <div class="canvas-container">
                <canvas id="purposeCanvas" width="700" height="400"></canvas>
            </div>
            <button class="interactive-btn" onclick="animatePurpose()">✨ 展示用途动画</button>
        </div>

        <div class="section">
            <h2 class="section-title">视图的使用场景</h2>
            <div class="canvas-container">
                <canvas id="scenarioCanvas" width="800" height="500"></canvas>
            </div>
            <div class="game-area">
                <div class="score">得分: <span id="score">0</span></div>
                <p>点击下面的场景卡片，学习不同的使用场景！</p>
                <button class="interactive-btn" onclick="startScenarioGame()">🎮 开始场景学习游戏</button>
                <button class="interactive-btn" onclick="resetGame()">🔄 重新开始</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">实战演练 - SQL视图创建</h2>
            <p class="content-text">
                让我们通过一个实际例子来学习如何创建和使用视图！
            </p>
            <div class="canvas-container">
                <canvas id="sqlDemoCanvas" width="900" height="600"></canvas>
            </div>
            <div class="game-area">
                <button class="interactive-btn" onclick="startSQLDemo()">📝 开始SQL演示</button>
                <button class="interactive-btn" onclick="nextSQLStep()">➡️ 下一步</button>
                <button class="interactive-btn" onclick="resetSQLDemo()">🔄 重新开始</button>
                <div style="margin-top: 20px;">
                    <span style="font-weight: 600;">当前步骤: </span>
                    <span id="currentStep">准备开始</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">知识总结</h2>
            <div class="canvas-container">
                <canvas id="summaryCanvas" width="800" height="400"></canvas>
            </div>
            <p class="content-text">
                🎉 恭喜你完成了数据库视图的学习！视图是数据库中非常实用的工具，
                它能让我们的SQL查询变得更简单、更安全、更高效。记住：
                <span class="highlight">视图 = 虚拟表 + 简化查询 + 数据保护</span>
            </p>
            <button class="interactive-btn pulse" onclick="showCelebration()">🎊 查看学习成果</button>
        </div>
    </div>

    <script>
        let score = 0;
        let currentScenario = 0;
        let sqlStep = 0;

        const scenarios = [
            {
                title: "重用SQL语句",
                description: "将复杂的查询保存为视图，可以反复使用",
                color: "#ff6b6b",
                icon: "🔄"
            },
            {
                title: "简化复杂操作",
                description: "隐藏复杂的JOIN和子查询，让使用更简单",
                color: "#4ecdc4",
                icon: "⚡"
            },
            {
                title: "使用表的部分",
                description: "只显示表中需要的列，隐藏敏感信息",
                color: "#45b7d1",
                icon: "🎯"
            },
            {
                title: "保护数据",
                description: "控制用户访问权限，只能看到允许的数据",
                color: "#96ceb4",
                icon: "🛡️"
            },
            {
                title: "格式转换",
                description: "改变数据的显示格式，让数据更易读",
                color: "#feca57",
                icon: "🎨"
            }
        ];

        const sqlSteps = [
            {
                title: "创建基础表",
                description: "首先我们有一个员工表和部门表",
                code: "CREATE TABLE employees (\n  id INT,\n  name VARCHAR(50),\n  dept_id INT,\n  salary DECIMAL(10,2)\n);",
                visual: "tables"
            },
            {
                title: "复杂查询",
                description: "需要查询员工姓名和部门名称",
                code: "SELECT e.name, d.dept_name, e.salary\nFROM employees e\nJOIN departments d ON e.dept_id = d.id\nWHERE e.salary > 5000;",
                visual: "complex"
            },
            {
                title: "创建视图",
                description: "将复杂查询保存为视图",
                code: "CREATE VIEW employee_info AS\nSELECT e.name, d.dept_name, e.salary\nFROM employees e\nJOIN departments d ON e.dept_id = d.id;",
                visual: "view"
            },
            {
                title: "使用视图",
                description: "现在可以像使用普通表一样使用视图",
                code: "SELECT * FROM employee_info\nWHERE salary > 5000;",
                visual: "usage"
            }
        ];

        // 视图概念动画
        function animateViewConcept() {
            const canvas = document.getElementById('viewConceptCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制真实表
                ctx.fillStyle = '#667eea';
                ctx.fillRect(50 + Math.sin(frame * 0.1) * 5, 100, 200, 120);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('真实数据表', 150, 160);
                
                // 绘制箭头
                ctx.strokeStyle = '#764ba2';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(270, 160);
                ctx.lineTo(330, 160);
                ctx.stroke();
                
                // 绘制箭头头部
                ctx.beginPath();
                ctx.moveTo(325, 155);
                ctx.lineTo(335, 160);
                ctx.lineTo(325, 165);
                ctx.stroke();
                
                // 绘制视图
                ctx.fillStyle = '#ff6b6b';
                ctx.fillRect(350 + Math.sin(frame * 0.1 + Math.PI) * 5, 100, 200, 120);
                ctx.fillStyle = 'white';
                ctx.fillText('视图 (虚拟表)', 450, 160);
                
                // 添加闪烁效果
                if (Math.sin(frame * 0.2) > 0) {
                    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
                    ctx.fillRect(350, 100, 200, 120);
                }
                
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                }
            }
            
            draw();
        }

        // 用途动画
        function animatePurpose() {
            const canvas = document.getElementById('purposeCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            
            const purposes = [
                { text: "简化SQL查询", x: 150, y: 150, color: "#667eea" },
                { text: "提高开发效率", x: 350, y: 150, color: "#764ba2" },
                { text: "兼容老表结构", x: 550, y: 150, color: "#ff6b6b" }
            ];
            
            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                purposes.forEach((purpose, index) => {
                    const delay = index * 30;
                    if (frame > delay) {
                        const scale = Math.min(1, (frame - delay) / 30);
                        const radius = 60 * scale;
                        
                        // 绘制圆形
                        ctx.fillStyle = purpose.color;
                        ctx.beginPath();
                        ctx.arc(purpose.x, purpose.y, radius, 0, Math.PI * 2);
                        ctx.fill();
                        
                        // 绘制文字
                        ctx.fillStyle = 'white';
                        ctx.font = '14px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(purpose.text, purpose.x, purpose.y);
                        
                        // 添加波纹效果
                        if (frame > delay + 30) {
                            const ripple = ((frame - delay - 30) % 60) / 60;
                            ctx.strokeStyle = purpose.color;
                            ctx.lineWidth = 3 * (1 - ripple);
                            ctx.globalAlpha = 1 - ripple;
                            ctx.beginPath();
                            ctx.arc(purpose.x, purpose.y, radius + ripple * 30, 0, Math.PI * 2);
                            ctx.stroke();
                            ctx.globalAlpha = 1;
                        }
                    }
                });
                
                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                }
            }
            
            draw();
        }

        // 场景游戏
        function startScenarioGame() {
            const canvas = document.getElementById('scenarioCanvas');
            const ctx = canvas.getContext('2d');
            
            drawScenarioCards();
            
            canvas.onclick = function(event) {
                const rect = canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                
                // 检查点击的卡片
                const cardWidth = 140;
                const cardHeight = 100;
                const startX = 50;
                const startY = 50;
                
                for (let i = 0; i < scenarios.length; i++) {
                    const row = Math.floor(i / 5);
                    const col = i % 5;
                    const cardX = startX + col * (cardWidth + 20);
                    const cardY = startY + row * (cardHeight + 30);
                    
                    if (x >= cardX && x <= cardX + cardWidth && 
                        y >= cardY && y <= cardY + cardHeight) {
                        showScenarioDetail(i);
                        score += 10;
                        document.getElementById('score').textContent = score;
                        break;
                    }
                }
            };
        }
        
        function drawScenarioCards() {
            const canvas = document.getElementById('scenarioCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const cardWidth = 140;
            const cardHeight = 100;
            const startX = 50;
            const startY = 50;
            
            scenarios.forEach((scenario, index) => {
                const row = Math.floor(index / 5);
                const col = index % 5;
                const x = startX + col * (cardWidth + 20);
                const y = startY + row * (cardHeight + 30);
                
                // 绘制卡片
                ctx.fillStyle = scenario.color;
                ctx.fillRect(x, y, cardWidth, cardHeight);
                
                // 绘制图标
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(scenario.icon, x + cardWidth/2, y + 35);
                
                // 绘制标题
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.fillText(scenario.title, x + cardWidth/2, y + 60);
                
                // 添加悬浮效果
                ctx.strokeStyle = 'rgba(255,255,255,0.5)';
                ctx.lineWidth = 2;
                ctx.strokeRect(x, y, cardWidth, cardHeight);
            });
        }
        
        function showScenarioDetail(index) {
            const scenario = scenarios[index];
            alert(`${scenario.icon} ${scenario.title}\n\n${scenario.description}`);
        }
        
        function resetGame() {
            score = 0;
            document.getElementById('score').textContent = score;
            drawScenarioCards();
        }

        // SQL演示功能
        function startSQLDemo() {
            sqlStep = 0;
            document.getElementById('currentStep').textContent = sqlSteps[sqlStep].title;
            drawSQLStep();
        }

        function nextSQLStep() {
            if (sqlStep < sqlSteps.length - 1) {
                sqlStep++;
                document.getElementById('currentStep').textContent = sqlSteps[sqlStep].title;
                drawSQLStep();
            } else {
                alert('🎉 SQL演示完成！你已经掌握了视图的基本用法！');
            }
        }

        function resetSQLDemo() {
            sqlStep = 0;
            document.getElementById('currentStep').textContent = '准备开始';
            const canvas = document.getElementById('sqlDemoCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawSQLStep() {
            const canvas = document.getElementById('sqlDemoCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            const step = sqlSteps[sqlStep];

            // 绘制标题
            ctx.fillStyle = '#2d3748';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(step.title, canvas.width/2, 40);

            // 绘制描述
            ctx.fillStyle = '#4a5568';
            ctx.font = '16px Arial';
            ctx.fillText(step.description, canvas.width/2, 70);

            // 绘制代码框
            ctx.fillStyle = '#1a202c';
            ctx.fillRect(50, 100, canvas.width-100, 200);

            // 绘制代码
            ctx.fillStyle = '#68d391';
            ctx.font = '14px Consolas, monospace';
            ctx.textAlign = 'left';
            const lines = step.code.split('\n');
            lines.forEach((line, index) => {
                ctx.fillText(line, 70, 130 + index * 20);
            });

            // 绘制可视化部分
            drawSQLVisualization(step.visual, ctx);
        }

        function drawSQLVisualization(type, ctx) {
            const startY = 320;

            switch(type) {
                case 'tables':
                    // 绘制两个表
                    ctx.fillStyle = '#667eea';
                    ctx.fillRect(100, startY, 200, 120);
                    ctx.fillRect(500, startY, 200, 120);

                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('员工表', 200, startY + 30);
                    ctx.fillText('部门表', 600, startY + 30);
                    break;

                case 'complex':
                    // 绘制复杂查询的连接
                    ctx.fillStyle = '#667eea';
                    ctx.fillRect(100, startY, 150, 100);
                    ctx.fillRect(550, startY, 150, 100);

                    // 绘制连接线
                    ctx.strokeStyle = '#ff6b6b';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(250, startY + 50);
                    ctx.lineTo(550, startY + 50);
                    ctx.stroke();

                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('员工表', 175, startY + 30);
                    ctx.fillText('部门表', 625, startY + 30);
                    ctx.fillText('JOIN', 400, startY + 45);
                    break;

                case 'view':
                    // 绘制视图创建过程
                    ctx.fillStyle = '#4ecdc4';
                    ctx.fillRect(300, startY, 200, 100);
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('📋 视图', 400, startY + 30);
                    ctx.fillText('employee_info', 400, startY + 55);

                    // 添加闪烁效果
                    setTimeout(() => {
                        ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
                        ctx.fillRect(300, startY, 200, 100);
                    }, 500);
                    break;

                case 'usage':
                    // 绘制简单使用
                    ctx.fillStyle = '#96ceb4';
                    ctx.fillRect(350, startY, 200, 80);
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('✨ 简单查询', 450, startY + 30);
                    ctx.fillText('SELECT * FROM view', 450, startY + 55);
                    break;
            }
        }

        // 总结动画
        function showCelebration() {
            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制庆祝文字
                ctx.fillStyle = `hsl(${frame * 2}, 70%, 50%)`;
                ctx.font = 'bold 36px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 学习完成！', canvas.width/2, 100);

                // 绘制知识点总结
                const points = [
                    '✅ 理解了视图的概念',
                    '✅ 掌握了视图的用途',
                    '✅ 学会了使用场景',
                    '✅ 实践了SQL创建'
                ];

                ctx.fillStyle = '#2d3748';
                ctx.font = '18px Arial';
                points.forEach((point, index) => {
                    const delay = index * 20;
                    if (frame > delay) {
                        const alpha = Math.min(1, (frame - delay) / 20);
                        ctx.globalAlpha = alpha;
                        ctx.fillText(point, canvas.width/2, 160 + index * 40);
                        ctx.globalAlpha = 1;
                    }
                });

                // 绘制飘落的星星
                for (let i = 0; i < 10; i++) {
                    const x = (canvas.width / 10) * i + Math.sin(frame * 0.1 + i) * 20;
                    const y = (frame * 2 + i * 50) % canvas.height;
                    ctx.fillStyle = `hsl(${(frame + i * 36) % 360}, 70%, 60%)`;
                    ctx.font = '20px Arial';
                    ctx.fillText('⭐', x, y);
                }

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 初始化
        window.onload = function() {
            animateViewConcept();
            setTimeout(animatePurpose, 1000);
            setTimeout(drawScenarioCards, 2000);
            setTimeout(() => {
                const canvas = document.getElementById('summaryCanvas');
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#f7fafc';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#4a5568';
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('点击上方按钮查看学习成果！', canvas.width/2, canvas.height/2);
            }, 3000);
        };
    </script>
</body>
</html>
