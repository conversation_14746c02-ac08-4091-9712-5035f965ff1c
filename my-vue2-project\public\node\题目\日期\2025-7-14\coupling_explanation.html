<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块耦合度学习</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700&display=swap');

        body {
            font-family: 'Noto Sans SC', sans-serif;
            margin: 0;
            background-color: #f7f9fc;
            color: #333;
            line-height: 1.6;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
            overflow-x: hidden;
        }

        .container {
            background-color: #ffffff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            width: 100%;
            max-width: 1200px;
            padding: 40px 60px;
            box-sizing: border-box;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInScaleUp 0.8s forwards ease-out;
            display: flex;
            flex-direction: column;
            gap: 40px;
        }

        @keyframes fadeInScaleUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            text-align: center;
            color: #2c3e50;
            font-weight: 700;
            font-size: 2.5em;
            margin-bottom: 20px;
            letter-spacing: 1px;
        }

        h2 {
            color: #34495e;
            font-weight: 600;
            font-size: 1.8em;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
            margin-top: 30px;
        }

        p {
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .section {
            padding: 20px;
            background-color: #fcfdff;
            border-radius: 10px;
            border: 1px solid #e9f0f8;
        }

        .concept-intro {
            text-align: center;
            margin-bottom: 40px;
        }

        .coupling-types {
            display: flex;
            gap: 30px;
        }

        .type-list {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .type-item {
            background-color: #ebf5ff;
            border: 1px solid #a3daff;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1em;
            color: #336699;
            font-weight: 500;
            text-align: center;
        }

        .type-item:hover {
            background-color: #cfe9ff;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .type-item.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 123, 255, 0.2);
        }

        .demonstration-area {
            flex: 2;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .coupling-meter {
            width: 100%;
            height: 30px;
            background: linear-gradient(to right, #28a745, #ffc107, #dc3545);
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.1);
            margin-bottom: 10px;
        }

        .meter-fill {
            height: 100%;
            width: 0%;
            background-color: rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            transition: width 0.8s ease-out;
            position: absolute;
            left: 0;
            top: 0;
        }

        .meter-label {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            font-weight: bold;
            font-size: 0.9em;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            left: 5px;
        }

        .meter-label.tight {
            right: 5px;
            left: auto;
        }


        #couplingCanvas {
            border: 1px solid #e0e0e0;
            background-color: #fdfefe;
            border-radius: 10px;
            box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
        }

        .description-box {
            background-color: #f0f8ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 8px;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 1.1em;
            color: #555;
            font-style: italic;
            transition: all 0.3s ease;
        }

        .quiz-section {
            margin-top: 40px;
        }

        .question {
            font-size: 1.2em;
            font-weight: 500;
            margin-bottom: 20px;
            background-color: #f7fcff;
            padding: 20px;
            border-radius: 10px;
            border: 1px dashed #cceeff;
        }

        .options label {
            display: block;
            background-color: #f0f0f0;
            padding: 12px 18px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1.1em;
        }

        .options label:hover {
            background-color: #e0e0e0;
            transform: translateX(5px);
        }

        .options input[type="radio"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        #feedback {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            font-size: 1.1em;
            display: none; /* Hidden by default */
        }

        #feedback.correct {
            background-color: #d4edda;
            color: #155724;
        }

        #feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
        }

        .explanation {
            margin-top: 20px;
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            padding: 20px;
            border-radius: 8px;
            display: none; /* Hidden by default */
        }

        .explanation h3 {
            color: #1890ff;
            margin-top: 0;
            margin-bottom: 10px;
        }

        .explanation p {
            font-size: 1em;
            line-height: 1.5;
        }

        /* Responsive adjustments */
        @media (max-width: 900px) {
            .container {
                padding: 30px 40px;
            }
            .coupling-types {
                flex-direction: column;
            }
            .type-list, .demonstration-area {
                flex: none;
                width: 100%;
            }
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px 25px;
            }
            h1 {
                font-size: 2em;
            }
            h2 {
                font-size: 1.5em;
            }
            .type-item, .options label, .question, .description-box, .explanation p {
                font-size: 1em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>模块耦合度：代码模块间的“关系”</h1>

        <div class="section concept-intro">
            <h2>概念介绍：什么是模块耦合？</h2>
            <p>想象一下，你的代码就像一个团队里的不同成员（模块）。模块耦合度，就是衡量这些成员之间“联系有多紧密”的一个指标。</p>
            <p>如果他们之间联系很紧密，一个成员动一下，其他成员可能都要跟着大变，这叫<b>“紧密耦合”</b>。就像牵一发而动全身。</p>
            <p>如果他们之间联系很松散，一个成员改动了，其他成员基本不受影响，这叫<b>“松散耦合”</b>。这样团队协作效率高，改动起来也方便。</p>
            <p>所以，在编程中，我们通常追求<b>低耦合</b>（松散耦合），这样代码更容易维护、测试和复用！</p>
        </div>

        <div class="section">
            <h2>七种耦合类型：从松到紧</h2>
            <div class="coupling-types">
                <div class="type-list">
                    <div class="type-item" data-type="noDirect">非直接耦合</div>
                    <div class="type-item" data-type="data">数据耦合</div>
                    <div class="type-item" data-type="stamp">标记耦合</div>
                    <div class="type-item" data-type="control">控制耦合</div>
                    <div class="type-item" data-type="external">外部耦合</div>
                    <div class="type-item" data-type="common">公共耦合</div>
                    <div class="type-item" data-type="content">内容耦合</div>
                </div>
                <div class="demonstration-area">
                    <div class="coupling-meter">
                        <div class="meter-fill" style="width: 0%;"></div>
                        <span class="meter-label">松散</span>
                        <span class="meter-label tight">紧密</span>
                    </div>
                    <canvas id="couplingCanvas" width="600" height="350"></canvas>
                    <div class="description-box" id="descriptionBox">点击左侧耦合类型，查看动画演示和详细解释！</div>
                </div>
            </div>
        </div>

        <div class="section quiz-section">
            <h2>小练习：考考你！</h2>
            <div class="question" id="quizQuestion">
                <!-- Question will be inserted here by JS -->
            </div>
            <div class="options" id="quizOptions">
                <!-- Options will be inserted here by JS -->
            </div>
            <div id="feedback"></div>
            <div class="explanation" id="quizExplanation">
                <h3>解析</h3>
                <p id="explanationContent"></p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('couplingCanvas');
        const ctx = canvas.getContext('2d');
        const descriptionBox = document.getElementById('descriptionBox');
        const meterFill = document.querySelector('.meter-fill');
        const typeItems = document.querySelectorAll('.type-item');

        const moduleColor = '#66ccff'; // Light blue for modules
        const moduleBorder = '#3399ff'; // Darker blue for module border
        const arrowColor = '#5cb85c'; // Green for data/control flow
        const specialColor = '#ff6666'; // Red for warning/high coupling

        let animationFrameId; // To store requestAnimationFrame ID
        let currentAnimation = null; // Stores the current animation function

        const couplingDescriptions = {
            noDirect: {
                name: "非直接耦合 (No direct coupling)",
                desc: "两个模块之间没有直接关系，联系完全通过主模块的控制和调用来实现。就像两个员工只听老板的，彼此不直接交流。",
                degree: 10
            },
            data: {
                name: "数据耦合 (Data coupling)",
                desc: "一组模块借助参数表传递简单数据。就像两个员工之间只传递明确的、简单的小纸条。",
                degree: 30
            },
            stamp: {
                name: "标记耦合 (Stamp coupling)",
                desc: "一组模块通过参数表传递记录信息（数据结构）。就像两个员工之间传递的是一个包含多个信息的“档案夹”，虽然只传递一个对象，但对方能访问其中所有内容。",
                degree: 50
            },
            control: {
                name: "控制耦合 (Control coupling)",
                desc: "模块之间传递的信息中包含用于控制模块内部逻辑的信息。就像一个员工不仅给另一个员工信息，还告诉对方“收到这个就这么做，收到那个就那么做”。",
                degree: 70
            },
            external: {
                name: "外部耦合 (External coupling)",
                desc: "一组模块都访问同一全局简单变量（非全局数据结构），并且不是通过参数传递该全局变量信息。就像多个员工都直接从办公室里的“公共白板”上读写信息，但不通过面对面交流。",
                degree: 80
            },
            common: {
                name: "公共耦合 (Common coupling)",
                desc: "多个模块都访问同一个公共数据环境（如全局数据结构、共享的通信区、内存的公共覆盖区）等。就像所有员工都直接在同一个大仓库里取放物品，互相影响大。",
                degree: 90
            },
            content: {
                name: "内容耦合 (Content coupling)",
                desc: "一个模块直接访问另一个模块的内部数据；一个模块不通过正常入口直接访问另一个模块的内部数据；两个模块有一部分程序代码重叠；一个模块有多个入口。这是耦合度最高的一种，一个模块直接“入侵”另一个模块的内部，非常危险！",
                degree: 100
            }
        };

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawModule(x, y, width, height, label, fill = moduleColor, border = moduleBorder) {
            ctx.fillStyle = fill;
            ctx.strokeStyle = border;
            ctx.lineWidth = 3;
            ctx.roundRect(x, y, width, height, 10);
            ctx.fill();
            ctx.stroke();

            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Noto Sans SC';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(label, x + width / 2, y + height / 2);
        }

        function drawArrow(startX, startY, endX, endY, color = arrowColor, headSize = 10) {
            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();

            // Draw arrow head
            const angle = Math.atan2(endY - startY, endX - startX);
            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(endX - headSize * Math.cos(angle - Math.PI / 6), endY - headSize * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(endX - headSize * Math.cos(angle + Math.PI / 6), endY - headSize * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fill();
        }

        function drawText(text, x, y, color = '#333', font = '14px Noto Sans SC', align = 'center') {
            ctx.fillStyle = color;
            ctx.font = font;
            ctx.textAlign = align;
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
        }

        // --- Animation Functions ---

        function animateNoDirectCoupling(time) {
            clearCanvas();
            const moduleWidth = 100;
            const moduleHeight = 70;
            const module1X = canvas.width / 4 - moduleWidth / 2;
            const module2X = canvas.width * 3 / 4 - moduleWidth / 2;
            const moduleY = canvas.height / 2 - moduleHeight / 2;
            const mainModuleX = canvas.width / 2 - moduleWidth / 2;
            const mainModuleY = moduleY - 100;

            drawModule(module1X, moduleY, moduleWidth, moduleHeight, "模块A");
            drawModule(module2X, moduleY, moduleWidth, moduleHeight, "模块B");
            drawModule(mainModuleX, mainModuleY, moduleWidth + 40, moduleHeight, "主模块");

            // Animate indirect calls from main module
            const numParticles = 10;
            const speed = 0.005;
            for (let i = 0; i < numParticles; i++) {
                const offset = (time * speed + i * (Math.PI * 2 / numParticles)) % (Math.PI * 2);
                const x1 = mainModuleX + (moduleWidth + 40) / 2 + Math.sin(offset) * 10;
                const y1 = mainModuleY + moduleHeight + Math.cos(offset) * 10;
                const x2 = module1X + moduleWidth / 2 + Math.cos(offset * 1.5) * 10;
                const y2 = moduleY - Math.sin(offset * 1.5) * 10;
                const x3 = module2X + moduleWidth / 2 + Math.sin(offset * 0.8) * 10;
                const y3 = moduleY - Math.cos(offset * 0.8) * 10;

                ctx.fillStyle = arrowColor;
                ctx.beginPath();
                ctx.arc(x1, y1, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.beginPath();
                ctx.arc(x2, y2, 3, 0, Math.PI * 2);
                ctx.fill();
                ctx.beginPath();
                ctx.arc(x3, y3, 3, 0, Math.PI * 2);
                ctx.fill();

                drawArrow(mainModuleX + (moduleWidth + 40) / 2, mainModuleY + moduleHeight, x1, y1, arrowColor, 0);
                drawArrow(x1, y1, module1X + moduleWidth / 2, moduleY, arrowColor, 0);
                drawArrow(mainModuleX + (moduleWidth + 40) / 2, mainModuleY + moduleHeight, x3, y3, arrowColor, 0);
                drawArrow(x3, y3, module2X + moduleWidth / 2, moduleY, arrowColor, 0);
            }
            drawText("间接调用", mainModuleX + (moduleWidth + 40) / 2, moduleY + moduleHeight / 2 - 20, '#555');

            animationFrameId = requestAnimationFrame(animateNoDirectCoupling);
        }


        function animateDataCoupling(time) {
            clearCanvas();
            const moduleWidth = 120;
            const moduleHeight = 80;
            const module1X = canvas.width / 4 - moduleWidth / 2;
            const module2X = canvas.width * 3 / 4 - moduleWidth / 2;
            const moduleY = canvas.height / 2 - moduleHeight / 2;

            drawModule(module1X, moduleY, moduleWidth, moduleHeight, "模块A");
            drawModule(module2X, moduleY, moduleWidth, moduleHeight, "模块B");

            // Animate data packet
            const dataSize = 20;
            const startX = module1X + moduleWidth;
            const startY = moduleY + moduleHeight / 2;
            const endX = module2X;
            const endY = moduleY + moduleHeight / 2;

            const progress = (Math.sin(time * 0.003) + 1) / 2; // oscillates between 0 and 1
            const currentX = startX + (endX - startX) * progress;
            const currentY = startY + (endY - startY) * progress;

            drawArrow(startX, startY, endX, endY);

            ctx.fillStyle = 'orange';
            ctx.strokeStyle = 'darkorange';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.rect(currentX - dataSize / 2, currentY - dataSize / 2, dataSize, dataSize);
            ctx.fill();
            ctx.stroke();
            drawText("简单数据", currentX, currentY, 'white', 'bold 12px Arial');
            drawText("通过参数传递", canvas.width / 2, moduleY + moduleHeight / 2 + 30, '#555');

            animationFrameId = requestAnimationFrame(animateDataCoupling);
        }

        function animateStampCoupling(time) {
            clearCanvas();
            const moduleWidth = 120;
            const moduleHeight = 80;
            const module1X = canvas.width / 4 - moduleWidth / 2;
            const module2X = canvas.width * 3 / 4 - moduleWidth / 2;
            const moduleY = canvas.height / 2 - moduleHeight / 2;

            drawModule(module1X, moduleY, moduleWidth, moduleHeight, "模块A");
            drawModule(module2X, moduleY, moduleWidth, moduleHeight, "模块B");

            // Animate data structure (record)
            const dataSize = 25;
            const startX = module1X + moduleWidth;
            const startY = moduleY + moduleHeight / 2;
            const endX = module2X;
            const endY = moduleY + moduleHeight / 2;

            const progress = (Math.sin(time * 0.003) + 1) / 2;
            const currentX = startX + (endX - startX) * progress;
            const currentY = startY + (endY - startY) * progress;

            drawArrow(startX, startY, endX, endY);

            ctx.fillStyle = '#9966ff'; // Purple for data structure
            ctx.strokeStyle = '#663399';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(currentX - dataSize / 2, currentY - dataSize / 2);
            ctx.lineTo(currentX + dataSize / 2, currentY - dataSize / 2);
            ctx.lineTo(currentX + dataSize / 2, currentY + dataSize / 2 - 5);
            ctx.lineTo(currentX + dataSize / 2 - 5, currentY + dataSize / 2);
            ctx.lineTo(currentX - dataSize / 2 + 5, currentY + dataSize / 2);
            ctx.lineTo(currentX - dataSize / 2, currentY + dataSize / 2 - 5);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            drawText("{数据结构}", currentX, currentY, 'white', 'bold 12px Arial');
            drawText("通过参数传递 (整个结构)", canvas.width / 2, moduleY + moduleHeight / 2 + 30, '#555');

            animationFrameId = requestAnimationFrame(animateStampCoupling);
        }

        function animateControlCoupling(time) {
            clearCanvas();
            const moduleWidth = 120;
            const moduleHeight = 80;
            const module1X = canvas.width / 4 - moduleWidth / 2;
            const module2X = canvas.width * 3 / 4 - moduleWidth / 2;
            const moduleY = canvas.height / 2 - moduleHeight / 2;

            drawModule(module1X, moduleY, moduleWidth, moduleHeight, "模块A");
            drawModule(module2X, moduleY, moduleWidth, moduleHeight, "模块B");

            // Animate control flag
            const flagSize = 20;
            const startX = module1X + moduleWidth;
            const startY = moduleY + moduleHeight / 2;
            const endX = module2X;
            const endY = moduleY + moduleHeight / 2;

            const progress = (Math.sin(time * 0.003) + 1) / 2;
            const currentX = startX + (endX - startX) * progress;
            const currentY = startY + (endY - startY) * progress;

            drawArrow(startX, startY, endX, endY, specialColor); // Control signal is more critical

            // Draw a "decision" or "control" icon
            ctx.fillStyle = specialColor;
            ctx.strokeStyle = '#cc0000';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(currentX - flagSize, currentY);
            ctx.lineTo(currentX, currentY - flagSize);
            ctx.lineTo(currentX + flagSize, currentY);
            ctx.lineTo(currentX, currentY + flagSize);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            drawText("控制信息", currentX, currentY, 'white', 'bold 12px Arial');
            drawText("（影响模块B内部逻辑）", canvas.width / 2, moduleY + moduleHeight / 2 + 30, '#555');

            animationFrameId = requestAnimationFrame(animateControlCoupling);
        }

        function animateExternalCoupling(time) {
            clearCanvas();
            const moduleWidth = 120;
            const moduleHeight = 80;
            const module1X = canvas.width / 4 - moduleWidth / 2;
            const module2X = canvas.width * 3 / 4 - moduleWidth / 2;
            const moduleY = canvas.height / 2 - moduleHeight / 2 + 40;

            drawModule(module1X, moduleY, moduleWidth, moduleHeight, "模块A");
            drawModule(module2X, moduleY, moduleWidth, moduleHeight, "模块B");

            // Draw a global variable
            const globalVarX = canvas.width / 2;
            const globalVarY = moduleY - 70;
            ctx.fillStyle = '#ffd700'; // Gold for global variable
            ctx.strokeStyle = '#b8860b';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(globalVarX, globalVarY, 30, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
            drawText("全局变量", globalVarX, globalVarY, '#333', 'bold 14px Noto Sans SC');

            // Animate access
            const pulse = (Math.sin(time * 0.005) + 1) / 2;
            const alpha = 0.5 + pulse * 0.5;

            ctx.strokeStyle = `rgba(0, 150, 0, ${alpha})`;
            ctx.lineWidth = 3;
            drawArrow(module1X + moduleWidth / 2, moduleY, globalVarX, globalVarY + 30);
            drawArrow(globalVarX, globalVarY - 30, module1X + moduleWidth / 2, moduleY);

            drawArrow(module2X + moduleWidth / 2, moduleY, globalVarX, globalVarY + 30);
            drawArrow(globalVarX, globalVarY - 30, module2X + moduleWidth / 2, moduleY);

            drawText("模块都直接访问全局变量", canvas.width / 2, moduleY + moduleHeight / 2 + 50, '#555');

            animationFrameId = requestAnimationFrame(animateExternalCoupling);
        }

        function animateCommonCoupling(time) {
            clearCanvas();
            const moduleWidth = 100;
            const moduleHeight = 70;

            const centerPoolX = canvas.width / 2;
            const centerPoolY = canvas.height / 2;
            const poolRadius = 60;

            ctx.fillStyle = '#cceeff'; // Light blue for common pool
            ctx.strokeStyle = '#66aadd';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(centerPoolX, centerPoolY, poolRadius, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
            drawText("公共数据环境", centerPoolX, centerPoolY, '#333', 'bold 14px Noto Sans SC');

            const modules = [
                { x: centerPoolX - 180, y: centerPoolY - 50, label: "模块A" },
                { x: centerPoolX + 80, y: centerPoolY - 120, label: "模块B" },
                { x: centerPoolX + 80, y: centerPoolY + 50, label: "模块C" },
                { x: centerPoolX - 180, y: centerPoolY + 120, label: "模块D" },
            ];

            modules.forEach(mod => {
                drawModule(mod.x, mod.y, moduleWidth, moduleHeight, mod.label);
                drawArrow(mod.x + moduleWidth / 2, mod.y + moduleHeight / 2, centerPoolX, centerPoolY, '#009933'); // Inbound
                drawArrow(centerPoolX, centerPoolY, mod.x + moduleWidth / 2, mod.y + moduleHeight / 2, '#cc3333'); // Outbound
            });

            // Animate data flow in and out of the common pool
            const dataParticleRadius = 5;
            const speed = 0.002;

            for (let i = 0; i < modules.length; i++) {
                const mod = modules[i];
                const angleOffset = (time * speed + i * Math.PI / 2) % (Math.PI * 2);
                const currentDistance = poolRadius + 30 + Math.sin(angleOffset) * 20;

                const startX = mod.x + moduleWidth / 2;
                const startY = mod.y + moduleHeight / 2;

                const pathLength = Math.sqrt(Math.pow(centerPoolX - startX, 2) + Math.pow(centerPoolY - startY, 2));
                const dx = (centerPoolX - startX) / pathLength;
                const dy = (centerPoolY - startY) / pathLength;

                const particleX = startX + dx * currentDistance;
                const particleY = startY + dy * currentDistance;

                ctx.fillStyle = `rgba(255, 100, 0, ${0.5 + Math.abs(Math.cos(angleOffset)) * 0.5})`;
                ctx.beginPath();
                ctx.arc(particleX, particleY, dataParticleRadius, 0, Math.PI * 2);
                ctx.fill();
            }

            drawText("所有模块共享同一个数据环境", canvas.width / 2, canvas.height - 30, '#555');

            animationFrameId = requestAnimationFrame(animateCommonCoupling);
        }

        function animateContentCoupling(time) {
            clearCanvas();
            const moduleWidth = 150;
            const moduleHeight = 100;
            const module1X = canvas.width / 2 - moduleWidth - 20;
            const module2X = canvas.width / 2 + 20;
            const moduleY = canvas.height / 2 - moduleHeight / 2;

            // Draw module A
            drawModule(module1X, moduleY, moduleWidth, moduleHeight, "模块A");

            // Draw module B (with internal structure)
            ctx.fillStyle = moduleColor;
            ctx.strokeStyle = moduleBorder;
            ctx.lineWidth = 3;
            ctx.roundRect(module2X, moduleY, moduleWidth, moduleHeight, 10);
            ctx.fill();
            ctx.stroke();
            drawText("模块B", module2X + moduleWidth / 2, moduleY + moduleHeight / 2 - 20);

            // Draw internal "private" data in module B
            ctx.fillStyle = '#f0f0f0';
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.rect(module2X + 20, moduleY + 60, moduleWidth - 40, 20);
            ctx.fill();
            ctx.stroke();
            drawText("B的内部数据", module2X + moduleWidth / 2, moduleY + 70, '#777', '12px Noto Sans SC');


            // Animate Module A "invading" Module B's internal data
            const invaderX = module1X + moduleWidth;
            const invaderY = moduleY + moduleHeight / 2;

            const targetX = module2X + moduleWidth / 2;
            const targetY = moduleY + 70;

            const progress = (Math.sin(time * 0.005) + 1) / 2;
            const currentX = invaderX + (targetX - invaderX) * progress;
            const currentY = invaderY + (targetY - invaderY) * progress;

            // Draw a "probe" or "invasion" line
            ctx.strokeStyle = specialColor; // Red for high coupling
            ctx.lineWidth = 4;
            ctx.setLineDash([5, 5]); // Dashed line
            ctx.beginPath();
            ctx.moveTo(invaderX, invaderY);
            ctx.lineTo(currentX, currentY);
            ctx.stroke();
            ctx.setLineDash([]); // Reset line dash

            // Draw a "direct access" icon
            ctx.fillStyle = specialColor;
            ctx.beginPath();
            ctx.arc(currentX, currentY, 8, 0, Math.PI * 2);
            ctx.fill();
            drawText("模块A直接访问模块B内部数据", canvas.width / 2, moduleY + moduleHeight + 30, specialColor);
            drawText("危险！耦合度最高！", canvas.width / 2, moduleY + moduleHeight + 55, specialColor, 'bold 16px Noto Sans SC');


            animationFrameId = requestAnimationFrame(animateContentCoupling);
        }

        // --- Event Listeners and Logic ---
        typeItems.forEach(item => {
            item.addEventListener('click', () => {
                // Remove active class from all
                typeItems.forEach(i => i.classList.remove('active'));
                // Add active class to clicked one
                item.classList.add('active');

                const type = item.dataset.type;
                const info = couplingDescriptions[type];

                descriptionBox.textContent = info.desc;
                meterFill.style.width = info.degree + '%';
                meterFill.style.backgroundColor = `hsl(${120 - info.degree * 1.2}, 70%, 50%)`; // Color changes with degree

                // Stop previous animation
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                }

                // Start new animation
                switch (type) {
                    case 'noDirect':
                        currentAnimation = animateNoDirectCoupling;
                        break;
                    case 'data':
                        currentAnimation = animateDataCoupling;
                        break;
                    case 'stamp':
                        currentAnimation = animateStampCoupling;
                        break;
                    case 'control':
                        currentAnimation = animateControlCoupling;
                        break;
                    case 'external':
                        currentAnimation = animateExternalCoupling;
                        break;
                    case 'common':
                        currentAnimation = animateCommonCoupling;
                        break;
                    case 'content':
                        currentAnimation = animateContentCoupling;
                        break;
                    default:
                        currentAnimation = null;
                        clearCanvas();
                }
                if (currentAnimation) {
                    currentAnimation(0); // Start animation from scratch
                }
            });
        });

        // --- Quiz Logic ---
        const quizQuestionDiv = document.getElementById('quizQuestion');
        const quizOptionsDiv = document.getElementById('quizOptions');
        const feedbackDiv = document.getElementById('feedback');
        const quizExplanationDiv = document.getElementById('quizExplanation');
        const explanationContent = document.getElementById('explanationContent');

        const quizData = {
            question: "问题1：一组模块通过参数传递信息属于（ ）。 一个模块可直接访问另一个模块的内部数据属于（ ）。（ ）表示模块之间的关联程度最高。",
            options: {
                A: "内容耦合",
                B: "标记耦合",
                C: "数据耦合",
                D: "控制耦合"
            },
            correctAnswer: "B",
            // The explanation provided in the original prompt is for "标记耦合" only.
            // I'll expand it to cover the other parts of the question.
            explanation: `
                <p><b>耦合度</b>表示模块之间联系的程度。紧密耦合表示模块之间联系非常强，松散耦合表示模块之间联系比较弱，非耦合则表示模块之间无任何联系，是完全独立的。</p>
                <p>模块的耦合类型通常分为7种，根据耦合度从低到高排序如下：</p>
                <ul>
                    <li><b>非直接耦合</b>：无直接联系，通过主模块调用。</li>
                    <li><b>数据耦合</b>：通过参数传递简单数据。</li>
                    <li><b>标记耦合</b>：通过参数传递记录信息（数据结构）。</li>
                    <li><b>控制耦合</b>：传递用于控制模块内部逻辑的信息。</li>
                    <li><b>外部耦合</b>：访问同一全局简单变量。</li>
                    <li><b>公共耦合</b>：访问同一个公共数据环境。</li>
                    <li><b>内容耦合</b>：一个模块直接访问另一个模块的内部数据，关联程度最高。</li>
                </ul>
                <p><b>第一空：“一组模块通过参数传递信息”</b></p>
                <p>当模块之间通过参数传递数据时，可能是简单数据（数据耦合），也可能是复杂的数据结构或记录信息（标记耦合）。题目中未明确是“简单数据”还是“记录信息”，但根据选项和提供的答案“B 标记耦合”，这里指的是通过参数传递<b>记录信息（数据结构）</b>，因此答案为“标记耦合”。</p>
                <p><b>第二空：“一个模块可直接访问另一个模块的内部数据”</b></p>
                <p>这直接对应<b>“内容耦合”</b>的定义。内容耦合意味着模块间的界限被打破，一个模块直接侵入另一个模块的内部结构或代码，这是耦合度最高的。</p>
                <p><b>第三空：“（ ）表示模块之间的关联程度最高”</b></p>
                <p>根据耦合度从低到高的排序，<b>“内容耦合”</b>是关联程度最高的一种，也是最不推荐的。</p>
                <p>因此，结合题目的要求和选项，第一空的答案是“标记耦合”，而“内容耦合”表示关联程度最高。</p>
            `
        };

        function loadQuiz() {
            quizQuestionDiv.innerHTML = quizData.question;
            quizOptionsDiv.innerHTML = '';
            for (const key in quizData.options) {
                const label = document.createElement('label');
                label.innerHTML = `<input type="radio" name="coupling_quiz" value="${key}"> ${key}. ${quizData.options[key]}`;
                quizOptionsDiv.appendChild(label);
            }

            quizOptionsDiv.addEventListener('change', checkAnswer);
        }

        function checkAnswer(event) {
            const selectedOption = event.target.value;
            feedbackDiv.style.display = 'block';
            quizExplanationDiv.style.display = 'block';
            explanationContent.innerHTML = quizData.explanation;

            if (selectedOption === quizData.correctAnswer) {
                feedbackDiv.className = 'correct';
                feedbackDiv.textContent = '恭喜你，回答正确！';
            } else {
                feedbackDiv.className = 'incorrect';
                feedbackDiv.textContent = '很遗憾，回答错误。请看解析！';
            }

            // Disable further selection after answer
            const radioButtons = quizOptionsDiv.querySelectorAll('input[type="radio"]');
            radioButtons.forEach(radio => radio.disabled = true);
        }

        // Initialize quiz on page load
        loadQuiz();

        // Initial canvas setup (optional, could show a brief intro animation)
        clearCanvas();
        drawText("点击左侧按钮开始学习!", canvas.width / 2, canvas.height / 2, '#888', 'bold 20px Noto Sans SC');

    </script>
</body>
</html> 