<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函数的定义域 - 零基础互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .interactive-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.2);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        .example-box {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .danger-zone {
            background: #f8d7da;
            border: 2px solid #f5c6cb;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">函数的定义域</h1>
            <p class="subtitle">零基础互动学习 - 让数学变得有趣！</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是函数的定义域？</h2>
            <div class="explanation">
                <p>想象一下，函数就像一台<span class="highlight">神奇的机器</span>！</p>
                <p>• 你往机器里放入一个数字（输入）</p>
                <p>• 机器会给你输出另一个数字（输出）</p>
                <p>• <span class="highlight">定义域</span>就是这台机器能够接受的所有输入数字的集合</p>
            </div>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="600" height="300"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="startConceptAnimation()">点击看动画演示</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">如何找定义域？</h2>
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
            </div>
            <div class="explanation" id="stepExplanation">
                <p><strong>第一步：观察函数表达式</strong></p>
                <p>看看函数里有没有这些"危险"的地方：</p>
                <div class="danger-zone">
                    <p>⚠️ 分母（不能为0）</p>
                    <p>⚠️ 根号（根号下不能为负数）</p>
                    <p>⚠️ 对数（真数必须大于0）</p>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="methodCanvas" width="600" height="350"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="nextStep()">下一步</button>
                <button class="interactive-btn" onclick="resetSteps()">重新开始</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">实战练习</h2>
            <div class="example-box">
                <h3>例题：求函数 f(x) = 1/(x-2) 的定义域</h3>
            </div>
            <div class="canvas-container">
                <canvas id="practiceCanvas" width="600" height="400"></canvas>
            </div>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="startPractice()">开始解题</button>
                <button class="interactive-btn" onclick="showAnswer()">查看答案</button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let animationRunning = false;
        let practiceStep = 0;

        // 概念演示动画
        function startConceptAnimation() {
            if (animationRunning) return;
            animationRunning = true;

            const canvas = document.getElementById('conceptCanvas');
            const ctx = canvas.getContext('2d');

            let frame = 0;
            const animate = () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制函数机器
                drawMachine(ctx, frame);

                // 绘制输入数字
                drawInputNumbers(ctx, frame);

                // 绘制输出
                drawOutput(ctx, frame);

                // 绘制定义域说明
                drawDomainExplanation(ctx, frame);

                frame++;
                if (frame < 400) {
                    requestAnimationFrame(animate);
                } else {
                    animationRunning = false;
                }
            };
            animate();
        }

        function drawMachine(ctx, frame) {
            // 机器主体
            const gradient = ctx.createLinearGradient(200, 100, 400, 200);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(200, 100, 200, 100);

            // 机器边框
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(200, 100, 200, 100);

            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('函数机器', 300, 130);
            ctx.font = '16px Arial';
            ctx.fillText('f(x) = x²', 300, 155);
            ctx.fillText('(所有实数都可以)', 300, 175);

            // 机器闪烁效果
            if (frame % 60 < 30) {
                ctx.strokeStyle = '#ffd700';
                ctx.lineWidth = 4;
                ctx.strokeRect(198, 98, 204, 104);
            }
        }

        function drawInputNumbers(ctx, frame) {
            const inputs = [-2, -1, 0, 1, 2];
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];

            inputs.forEach((num, index) => {
                const progress = (frame - index * 20) / 150;
                if (progress > 0 && progress < 1) {
                    const x = 50 + progress * 130;
                    const y = 150 + Math.sin(progress * Math.PI) * 30;

                    ctx.fillStyle = colors[index];
                    ctx.beginPath();
                    ctx.arc(x, y, 25, 0, Math.PI * 2);
                    ctx.fill();

                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.stroke();

                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(num.toString(), x, y + 5);
                }
            });
        }

        function drawOutput(ctx, frame) {
            if (frame > 150) {
                const outputs = [4, 1, 0, 1, 4];
                const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];

                outputs.forEach((num, index) => {
                    const progress = (frame - 150 - index * 20) / 150;
                    if (progress > 0 && progress < 1) {
                        const x = 420 + progress * 130;
                        const y = 150 + Math.sin(progress * Math.PI) * 30;

                        ctx.fillStyle = colors[index];
                        ctx.beginPath();
                        ctx.arc(x, y, 25, 0, Math.PI * 2);
                        ctx.fill();

                        ctx.strokeStyle = '#333';
                        ctx.lineWidth = 2;
                        ctx.stroke();

                        ctx.fillStyle = 'white';
                        ctx.font = 'bold 16px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(num.toString(), x, y + 5);
                    }
                });
            }
        }

        function drawDomainExplanation(ctx, frame) {
            if (frame > 300) {
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.fillRect(50, 220, 500, 60);
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.strokeRect(50, 220, 500, 60);

                ctx.fillStyle = '#333';
                ctx.font = 'bold 18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('定义域：所有能放入机器的数字', 300, 245);
                ctx.font = '16px Arial';
                ctx.fillText('对于 f(x) = x²，定义域是所有实数 ℝ', 300, 265);
            }
        }

        // 步骤演示
        function nextStep() {
            currentStep = (currentStep % 3) + 1;
            updateStep();
        }

        function resetSteps() {
            currentStep = 1;
            updateStep();
        }

        function updateStep() {
            // 更新步骤指示器
            document.querySelectorAll('.step').forEach((step, index) => {
                step.classList.toggle('active', index + 1 === currentStep);
            });

            // 更新说明文字
            const explanations = [
                {
                    title: "第一步：观察函数表达式",
                    content: `看看函数里有没有这些"危险"的地方：<br>
                    <div class="danger-zone">
                        <p>⚠️ 分母（不能为0）</p>
                        <p>⚠️ 根号（根号下不能为负数）</p>
                        <p>⚠️ 对数（真数必须大于0）</p>
                    </div>`
                },
                {
                    title: "第二步：找出限制条件",
                    content: `根据"危险"的地方，列出限制条件：<br>
                    • 如果有分母，让分母≠0<br>
                    • 如果有根号，让根号下≥0<br>
                    • 如果有对数，让真数>0`
                },
                {
                    title: "第三步：求解并写出定义域",
                    content: `解出x的取值范围，这就是定义域！<br>
                    • 用区间表示法：[a,b] 或 (a,b)<br>
                    • 用集合表示法：{x|条件}<br>
                    • 注意开区间和闭区间的区别`
                }
            ];

            const explanation = explanations[currentStep - 1];
            document.getElementById('stepExplanation').innerHTML = `
                <p><strong>${explanation.title}</strong></p>
                <p>${explanation.content}</p>
            `;

            // 绘制对应的图示
            drawMethodDiagram();
        }

        function drawMethodDiagram() {
            const canvas = document.getElementById('methodCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            switch(currentStep) {
                case 1:
                    drawStep1(ctx);
                    break;
                case 2:
                    drawStep2(ctx);
                    break;
                case 3:
                    drawStep3(ctx);
                    break;
            }
        }

        function drawStep1(ctx) {
            // 绘制几个函数例子
            const functions = [
                { expr: "f(x) = 1/x", danger: "分母", x: 100, y: 80, color: '#ff6b6b' },
                { expr: "g(x) = √x", danger: "根号", x: 300, y: 80, color: '#4ecdc4' },
                { expr: "h(x) = ln(x)", danger: "对数", x: 500, y: 80, color: '#feca57' }
            ];

            functions.forEach((func, index) => {
                // 函数表达式背景
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.fillRect(func.x - 60, func.y - 30, 120, 80);
                ctx.strokeStyle = func.color;
                ctx.lineWidth = 2;
                ctx.strokeRect(func.x - 60, func.y - 30, 120, 80);

                // 函数表达式
                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(func.expr, func.x, func.y);

                // 危险标记
                ctx.fillStyle = func.color;
                ctx.fillRect(func.x - 40, func.y + 15, 80, 25);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Arial';
                ctx.fillText('⚠️ ' + func.danger, func.x, func.y + 32);

                // 动画效果
                setTimeout(() => {
                    ctx.save();
                    ctx.globalAlpha = 0.3;
                    ctx.fillStyle = func.color;
                    ctx.beginPath();
                    ctx.arc(func.x, func.y, 80, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.restore();
                }, index * 500);
            });
        }

        function drawStep2(ctx) {
            // 绘制限制条件分析
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('例：f(x) = 1/(x-2)', 300, 50);

            // 箭头动画
            let arrowY = 70;
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(300, arrowY);
            ctx.lineTo(300, arrowY + 50);
            // 箭头头部
            ctx.moveTo(290, arrowY + 40);
            ctx.lineTo(300, arrowY + 50);
            ctx.lineTo(310, arrowY + 40);
            ctx.stroke();

            // 分析框
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(100, 140, 400, 120);
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.strokeRect(100, 140, 400, 120);

            // 分析内容
            ctx.fillStyle = '#333';
            ctx.font = '20px Arial';
            ctx.fillText('🔍 分析过程：', 300, 170);
            ctx.font = '18px Arial';
            ctx.fillText('分母不能为0', 300, 200);
            ctx.fillText('所以：x - 2 ≠ 0', 300, 225);
            ctx.fillText('即：x ≠ 2', 300, 250);

            // 高亮危险区域
            ctx.fillStyle = 'rgba(255, 107, 107, 0.3)';
            ctx.fillRect(280, 30, 40, 25);
        }

        function drawStep3(ctx) {
            // 绘制数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(50, 180);
            ctx.lineTo(550, 180);
            ctx.stroke();

            // 箭头
            ctx.beginPath();
            ctx.moveTo(540, 175);
            ctx.lineTo(550, 180);
            ctx.lineTo(540, 185);
            ctx.stroke();

            // 标记点
            const points = [-3, -2, -1, 0, 1, 2, 3, 4, 5];
            points.forEach(point => {
                const x = 300 + point * 40;
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x, 170);
                ctx.lineTo(x, 190);
                ctx.stroke();

                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(point.toString(), x, 210);

                // 特殊标记x=2（不能取）
                if (point === 2) {
                    ctx.fillStyle = '#ff6b6b';
                    ctx.beginPath();
                    ctx.arc(x, 180, 12, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 16px Arial';
                    ctx.fillText('×', x, 185);
                } else {
                    // 其他点可以取
                    ctx.fillStyle = '#4ecdc4';
                    ctx.beginPath();
                    ctx.arc(x, 180, 8, 0, Math.PI * 2);
                    ctx.fill();
                }
            });

            // 定义域标注
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillRect(150, 240, 300, 80);
            ctx.strokeStyle = '#4ecdc4';
            ctx.lineWidth = 2;
            ctx.strokeRect(150, 240, 300, 80);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('定义域', 300, 265);
            ctx.font = '18px Arial';
            ctx.fillText('(-∞, 2) ∪ (2, +∞)', 300, 290);
            ctx.font = '14px Arial';
            ctx.fillText('除了2以外的所有实数', 300, 310);
        }

        // 实战练习
        function startPractice() {
            practiceStep = 1;
            drawPractice();
        }

        function showAnswer() {
            practiceStep = 4;
            drawPractice();
        }

        function drawPractice() {
            const canvas = document.getElementById('practiceCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 题目
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('求函数 f(x) = 1/(x-2) 的定义域', 300, 40);

            switch(practiceStep) {
                case 1:
                    drawPracticeStep1(ctx);
                    break;
                case 2:
                    drawPracticeStep2(ctx);
                    break;
                case 3:
                    drawPracticeStep3(ctx);
                    break;
                case 4:
                    drawPracticeAnswer(ctx);
                    break;
            }

            if (practiceStep < 4) {
                setTimeout(() => {
                    practiceStep++;
                    drawPractice();
                }, 2000);
            }
        }

        function drawPracticeStep1(ctx) {
            ctx.fillStyle = '#667eea';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('第1步：观察函数表达式', 300, 100);

            // 高亮分母
            ctx.fillStyle = 'rgba(255, 107, 107, 0.5)';
            ctx.fillRect(280, 20, 40, 25);

            ctx.fillStyle = '#ff6b6b';
            ctx.font = '18px Arial';
            ctx.fillText('发现分母：x - 2', 300, 140);
            ctx.fillText('⚠️ 分母不能为0！', 300, 170);
        }

        function drawPracticeStep2(ctx) {
            ctx.fillStyle = '#667eea';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('第2步：列出限制条件', 300, 100);

            ctx.fillStyle = '#333';
            ctx.font = '18px Arial';
            ctx.fillText('x - 2 ≠ 0', 300, 140);
            ctx.fillText('解得：x ≠ 2', 300, 170);
        }

        function drawPracticeStep3(ctx) {
            ctx.fillStyle = '#667eea';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('第3步：写出定义域', 300, 100);

            // 绘制简化的数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(150, 150);
            ctx.lineTo(450, 150);
            ctx.stroke();

            // 标记x=2
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(300, 150, 10, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.fillText('2', 300, 155);
        }

        function drawPracticeAnswer(ctx) {
            ctx.fillStyle = '#4ecdc4';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 答案', 300, 100);

            // 答案框
            ctx.fillStyle = 'rgba(78, 205, 196, 0.1)';
            ctx.fillRect(100, 120, 400, 200);
            ctx.strokeStyle = '#4ecdc4';
            ctx.lineWidth = 3;
            ctx.strokeRect(100, 120, 400, 200);

            ctx.fillStyle = '#333';
            ctx.font = '20px Arial';
            ctx.fillText('定义域：(-∞, 2) ∪ (2, +∞)', 300, 160);
            ctx.font = '16px Arial';
            ctx.fillText('或写成：{x | x ∈ ℝ, x ≠ 2}', 300, 190);
            ctx.fillText('意思：除了2以外的所有实数', 300, 220);

            // 绘制完整数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(120, 260);
            ctx.lineTo(480, 260);
            ctx.stroke();

            // 标记可取的范围
            ctx.strokeStyle = '#4ecdc4';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(120, 260);
            ctx.lineTo(290, 260);
            ctx.moveTo(310, 260);
            ctx.lineTo(480, 260);
            ctx.stroke();

            // 标记x=2不可取
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(300, 260, 8, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = 'bold 10px Arial';
            ctx.fillText('×', 300, 264);

            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.fillText('2', 300, 280);
        }

        // 初始化
        window.onload = function() {
            updateStep();
        };
    </script>
</body>
</html>
