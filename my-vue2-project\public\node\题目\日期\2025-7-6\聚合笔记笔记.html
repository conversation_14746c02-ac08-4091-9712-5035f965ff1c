<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识关系笔记系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 40px);
        }
        
        .sidebar {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow-y: auto;
            height: 100%;
        }
        
        .main-content {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow-y: auto;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        h2 {
            color: #3498db;
            margin-bottom: 15px;
        }
        
        button {
            background-color: #3498db;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-size: 14px;
        }
        
        button:hover {
            background-color: #2980b9;
        }
        
        .note-list {
            margin-top: 20px;
        }
        
        .note-item {
            background-color: #f9f9f9;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .note-item:hover {
            background-color: #eaeaea;
        }
        
        .selected {
            background-color: #e1f0fa;
            border-left: 4px solid #3498db;
        }
        
        .note-editor {
            margin-top: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }
        
        .note-title {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .note-content {
            width: 100%;
            height: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            resize: none;
            flex-grow: 1;
            font-size: 16px;
        }
        
        .relationship-section {
            margin-top: 20px;
        }
        
        .relationship-container {
            margin-top: 10px;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
        }
        
        .relationship-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .relationship-item:last-child {
            border-bottom: none;
        }
        
        .relationship-type {
            display: flex;
            align-items: center;
            margin-top: 10px;
        }
        
        .relationship-type label {
            margin-right: 10px;
        }
        
        .relationship-select {
            padding: 5px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .visualization {
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            height: 300px;
            position: relative;
        }
        
        .node {
            position: absolute;
            background-color: #3498db;
            color: white;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            width: 80px;
            height: 80px;
            text-align: center;
            overflow: hidden;
            word-break: break-all;
            z-index: 2;
        }
        
        .node.whole {
            background-color: #e74c3c;
        }
        
        .node.part {
            background-color: #2ecc71;
        }
        
        .edge {
            position: absolute;
            height: 2px;
            background-color: #7f8c8d;
            transform-origin: left center;
            z-index: 1;
        }
        
        .ai-button {
            background-color: #9b59b6;
            margin-top: 10px;
        }
        
        .ai-button:hover {
            background-color: #8e44ad;
        }
        
        .ai-analysis {
            margin-top: 20px;
            padding: 15px;
            background-color: #f0f7fb;
            border-left: 4px solid #9b59b6;
            display: none;
        }
        
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <h1>知识笔记系统</h1>
            <button id="addNoteBtn">新建笔记</button>
            <div class="note-list" id="noteList"></div>
        </div>
        
        <div class="main-content">
            <div class="note-editor">
                <input type="text" class="note-title" id="noteTitle" placeholder="笔记标题">
                <textarea class="note-content" id="noteContent" placeholder="在此输入笔记内容..."></textarea>
                
                <div class="button-group">
                    <button id="saveNoteBtn">保存笔记</button>
                    <button id="deleteNoteBtn">删除笔记</button>
                </div>
            </div>
            
            <div class="relationship-section">
                <h2>知识关系管理</h2>
                <div class="relationship-type">
                    <label for="parentNote">选择整体知识点:</label>
                    <select id="parentNote" class="relationship-select"></select>
                </div>
                <div class="relationship-type">
                    <label for="childNote">选择部分知识点:</label>
                    <select id="childNote" class="relationship-select"></select>
                </div>
                <div class="button-group">
                    <button id="addRelationBtn">添加关系</button>
                    <button id="removeRelationBtn">移除关系</button>
                </div>
                
                <div class="relationship-container" id="relationshipContainer">
                    <p>当前笔记的关系：</p>
                    <div id="relationshipsList"></div>
                </div>
            </div>
            
            <h2>知识关系可视化</h2>
            <div class="visualization" id="visualization"></div>
            
            <button class="ai-button" id="analyzeBtn">AI分析知识结构</button>
            <div class="ai-analysis" id="aiAnalysis">
                <h3>AI分析结果</h3>
                <p id="aiAnalysisContent">这里将展示AI分析的结果...</p>
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        let notes = [];
        let relationships = [];
        let currentNoteId = null;
        
        // DOM元素
        const noteList = document.getElementById('noteList');
        const noteTitle = document.getElementById('noteTitle');
        const noteContent = document.getElementById('noteContent');
        const parentSelect = document.getElementById('parentNote');
        const childSelect = document.getElementById('childNote');
        const relationshipsList = document.getElementById('relationshipsList');
        const visualization = document.getElementById('visualization');
        const aiAnalysis = document.getElementById('aiAnalysis');
        
        // 从localStorage加载数据
        function loadData() {
            const savedNotes = localStorage.getItem('knowledgeNotes');
            const savedRelationships = localStorage.getItem('knowledgeRelationships');
            
            if (savedNotes) {
                notes = JSON.parse(savedNotes);
            }
            
            if (savedRelationships) {
                relationships = JSON.parse(savedRelationships);
            }
            
            renderNoteList();
            updateRelationshipSelects();
        }
        
        // 保存数据到localStorage
        function saveData() {
            localStorage.setItem('knowledgeNotes', JSON.stringify(notes));
            localStorage.setItem('knowledgeRelationships', JSON.stringify(relationships));
        }
        
        // 渲染笔记列表
        function renderNoteList() {
            noteList.innerHTML = '';
            
            notes.forEach(note => {
                const noteElement = document.createElement('div');
                noteElement.className = `note-item ${note.id === currentNoteId ? 'selected' : ''}`;
                noteElement.textContent = note.title || '无标题笔记';
                noteElement.dataset.id = note.id;
                
                noteElement.addEventListener('click', () => {
                    selectNote(note.id);
                });
                
                noteList.appendChild(noteElement);
            });
        }
        
        // 选择笔记
        function selectNote(id) {
            currentNoteId = id;
            const note = notes.find(n => n.id === id);
            
            noteTitle.value = note.title || '';
            noteContent.value = note.content || '';
            
            renderNoteList();
            renderRelationships();
            renderVisualization();
        }
        
        // 创建新笔记
        function createNote() {
            const newNote = {
                id: Date.now().toString(),
                title: '新笔记',
                content: '',
                createdAt: new Date().toISOString()
            };
            
            notes.push(newNote);
            saveData();
            renderNoteList();
            updateRelationshipSelects();
            selectNote(newNote.id);
        }
        
        // 保存当前笔记
        function saveCurrentNote() {
            if (!currentNoteId) return;
            
            const noteIndex = notes.findIndex(n => n.id === currentNoteId);
            if (noteIndex === -1) return;
            
            notes[noteIndex].title = noteTitle.value;
            notes[noteIndex].content = noteContent.value;
            notes[noteIndex].updatedAt = new Date().toISOString();
            
            saveData();
            renderNoteList();
            updateRelationshipSelects();
        }
        
        // 删除当前笔记
        function deleteCurrentNote() {
            if (!currentNoteId) return;
            
            // 删除与该笔记相关的所有关系
            relationships = relationships.filter(r => 
                r.parentId !== currentNoteId && r.childId !== currentNoteId
            );
            
            notes = notes.filter(n => n.id !== currentNoteId);
            saveData();
            renderNoteList();
            updateRelationshipSelects();
            
            if (notes.length > 0) {
                selectNote(notes[0].id);
            } else {
                currentNoteId = null;
                noteTitle.value = '';
                noteContent.value = '';
                renderRelationships();
                renderVisualization();
            }
        }
        
        // 更新关系选择框
        function updateRelationshipSelects() {
            parentSelect.innerHTML = '<option value="">请选择整体知识点</option>';
            childSelect.innerHTML = '<option value="">请选择部分知识点</option>';
            
            notes.forEach(note => {
                const parentOption = document.createElement('option');
                parentOption.value = note.id;
                parentOption.textContent = note.title || '无标题笔记';
                
                const childOption = document.createElement('option');
                childOption.value = note.id;
                childOption.textContent = note.title || '无标题笔记';
                
                parentSelect.appendChild(parentOption);
                childSelect.appendChild(childOption);
            });
        }
        
        // 添加关系
        function addRelationship() {
            const parentId = parentSelect.value;
            const childId = childSelect.value;
            
            if (!parentId || !childId || parentId === childId) {
                alert('请选择不同的整体和部分知识点');
                return;
            }
            
            // 检查是否存在循环关系
            if (hasCircularRelationship(parentId, childId)) {
                alert('添加此关系会导致循环依赖，无法添加');
                return;
            }
            
            // 检查关系是否已存在
            const existingRelationship = relationships.find(r => 
                r.parentId === parentId && r.childId === childId
            );
            
            if (existingRelationship) {
                alert('该关系已存在');
                return;
            }
            
            const newRelationship = {
                id: Date.now().toString(),
                parentId,
                childId,
                createdAt: new Date().toISOString()
            };
            
            relationships.push(newRelationship);
            saveData();
            renderRelationships();
            renderVisualization();
        }
        
        // 检查是否会导致循环关系
        function hasCircularRelationship(parentId, childId) {
            // 如果子节点已经是父节点的祖先，则会形成循环
            return isAncestor(childId, parentId);
        }
        
        // 检查potentialAncestorId是否是nodeId的祖先
        function isAncestor(potentialAncestorId, nodeId) {
            // 获取nodeId的所有父节点
            const parents = relationships
                .filter(r => r.childId === nodeId)
                .map(r => r.parentId);
            
            // 如果直接父节点中包含potentialAncestorId，返回true
            if (parents.includes(potentialAncestorId)) {
                return true;
            }
            
            // 递归检查每个父节点
            for (const parentId of parents) {
                if (isAncestor(potentialAncestorId, parentId)) {
                    return true;
                }
            }
            
            return false;
        }
        
        // 移除关系
        function removeRelationship() {
            const parentId = parentSelect.value;
            const childId = childSelect.value;
            
            if (!parentId || !childId) {
                alert('请选择整体和部分知识点');
                return;
            }
            
            relationships = relationships.filter(r => 
                !(r.parentId === parentId && r.childId === childId)
            );
            
            saveData();
            renderRelationships();
            renderVisualization();
        }
        
        // 渲染当前笔记的关系
        function renderRelationships() {
            relationshipsList.innerHTML = '';
            
            if (!currentNoteId) return;
            
            // 作为整体的关系
            const asParent = relationships.filter(r => r.parentId === currentNoteId);
            if (asParent.length > 0) {
                const parentHeader = document.createElement('h4');
                parentHeader.textContent = '作为整体，包含以下部分：';
                relationshipsList.appendChild(parentHeader);
                
                asParent.forEach(relation => {
                    const childNote = notes.find(n => n.id === relation.childId);
                    if (childNote) {
                        const relationItem = document.createElement('div');
                        relationItem.className = 'relationship-item';
                        relationItem.textContent = `- ${childNote.title || '无标题笔记'}`;
                        relationshipsList.appendChild(relationItem);
                    }
                });
            }
            
            // 作为部分的关系
            const asChild = relationships.filter(r => r.childId === currentNoteId);
            if (asChild.length > 0) {
                const childHeader = document.createElement('h4');
                childHeader.textContent = '作为部分，属于以下整体：';
                relationshipsList.appendChild(childHeader);
                
                asChild.forEach(relation => {
                    const parentNote = notes.find(n => n.id === relation.parentId);
                    if (parentNote) {
                        const relationItem = document.createElement('div');
                        relationItem.className = 'relationship-item';
                        relationItem.textContent = `- ${parentNote.title || '无标题笔记'}`;
                        relationshipsList.appendChild(relationItem);
                    }
                });
            }
            
            if (asParent.length === 0 && asChild.length === 0) {
                relationshipsList.textContent = '当前笔记没有关联关系';
            }
        }
        
        // 渲染可视化图表
        function renderVisualization() {
            visualization.innerHTML = '';
            
            if (notes.length === 0) return;
            
            // 创建节点和边
            const nodes = [];
            const edges = [];
            
            // 计算节点位置
            const centerX = visualization.clientWidth / 2;
            const centerY = visualization.clientHeight / 2;
            const radius = Math.min(centerX, centerY) * 0.7;
            
            notes.forEach((note, index) => {
                // 简单的圆形布局
                const angle = (index / notes.length) * 2 * Math.PI;
                const x = centerX + radius * Math.cos(angle);
                const y = centerY + radius * Math.sin(angle);
                
                // 创建节点元素
                const nodeElement = document.createElement('div');
                nodeElement.className = 'node';
                nodeElement.textContent = note.title || '无标题';
                nodeElement.dataset.id = note.id;
                nodeElement.style.left = `${x - 40}px`;
                nodeElement.style.top = `${y - 40}px`;
                
                // 确定节点类型（是整体还是部分或两者都是）
                const isParent = relationships.some(r => r.parentId === note.id);
                const isChild = relationships.some(r => r.childId === note.id);
                
                if (isParent && !isChild) {
                    nodeElement.classList.add('whole');
                } else if (!isParent && isChild) {
                    nodeElement.classList.add('part');
                }
                
                // 点击节点选择对应笔记
                nodeElement.addEventListener('click', () => {
                    selectNote(note.id);
                });
                
                visualization.appendChild(nodeElement);
                nodes.push({ id: note.id, element: nodeElement, x, y });
            });
            
            // 创建边
            relationships.forEach(rel => {
                const parentNode = nodes.find(n => n.id === rel.parentId);
                const childNode = nodes.find(n => n.id === rel.childId);
                
                if (parentNode && childNode) {
                    const dx = childNode.x - parentNode.x;
                    const dy = childNode.y - parentNode.y;
                    const length = Math.sqrt(dx * dx + dy * dy);
                    const angle = Math.atan2(dy, dx) * 180 / Math.PI;
                    
                    const edge = document.createElement('div');
                    edge.className = 'edge';
                    edge.style.width = `${length}px`;
                    edge.style.left = `${parentNode.x}px`;
                    edge.style.top = `${parentNode.y}px`;
                    edge.style.transform = `rotate(${angle}deg)`;
                    
                    visualization.appendChild(edge);
                    edges.push(edge);
                }
            });
        }
        
        // AI分析功能（模拟）
        function analyzeKnowledgeStructure() {
            aiAnalysis.style.display = 'block';
            
            // 在实际应用中，这里会调用AI服务进行分析
            // 这里只是简单模拟分析结果
            
            let analysisText = '知识结构分析：\n\n';
            
            // 1. 找出根节点（不是任何节点的子节点）
            const rootNodeIds = notes
                .filter(note => !relationships.some(r => r.childId === note.id))
                .map(note => note.id);
            
            if (rootNodeIds.length > 0) {
                analysisText += '● 顶层知识点：\n';
                rootNodeIds.forEach(id => {
                    const note = notes.find(n => n.id === id);
                    analysisText += `  - ${note.title}\n`;
                });
            }
            
            // 2. 找出叶节点（不是任何节点的父节点）
            const leafNodeIds = notes
                .filter(note => !relationships.some(r => r.parentId === note.id))
                .map(note => note.id);
            
            if (leafNodeIds.length > 0) {
                analysisText += '\n● 基础知识点：\n';
                leafNodeIds.forEach(id => {
                    const note = notes.find(n => n.id === id);
                    analysisText += `  - ${note.title}\n`;
                });
            }
            
            // 3. 知识密度最高的节点（有最多关系的节点）
            const nodeRelationCount = {};
            notes.forEach(note => {
                nodeRelationCount[note.id] = 0;
            });
            
            relationships.forEach(rel => {
                nodeRelationCount[rel.parentId] = (nodeRelationCount[rel.parentId] || 0) + 1;
                nodeRelationCount[rel.childId] = (nodeRelationCount[rel.childId] || 0) + 1;
            });
            
            const mostConnectedIds = Object.entries(nodeRelationCount)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 3)
                .map(entry => entry[0]);
            
            if (mostConnectedIds.length > 0) {
                analysisText += '\n● 知识枢纽点：\n';
                mostConnectedIds.forEach(id => {
                    const note = notes.find(n => n.id === id);
                    const count = nodeRelationCount[id];
                    analysisText += `  - ${note.title} (${count}个关联)\n`;
                });
            }
            
            // 4. 建议补充的知识点
            analysisText += '\n● AI建议：\n';
            if (notes.length < 3) {
                analysisText += '  - 知识库内容较少，建议添加更多笔记丰富知识结构\n';
            } else if (relationships.length < notes.length - 1) {
                analysisText += '  - 知识点之间的联系不够紧密，建议建立更多关联关系\n';
            }
            
            if (rootNodeIds.length > 3) {
                analysisText += '  - 顶层知识点过多，建议整合或添加更高层次的知识点\n';
            }
            
            document.getElementById('aiAnalysisContent').innerText = analysisText;
        }
        
        // 事件监听
        document.getElementById('addNoteBtn').addEventListener('click', createNote);
        document.getElementById('saveNoteBtn').addEventListener('click', saveCurrentNote);
        document.getElementById('deleteNoteBtn').addEventListener('click', deleteCurrentNote);
        document.getElementById('addRelationBtn').addEventListener('click', addRelationship);
        document.getElementById('removeRelationBtn').addEventListener('click', removeRelationship);
        document.getElementById('analyzeBtn').addEventListener('click', analyzeKnowledgeStructure);
        
        // 初始化
        loadData();
        
        // 如果没有笔记，创建一个示例笔记
        if (notes.length === 0) {
            const exampleNote1 = {
                id: '1',
                title: '编程基础',
                content: '编程是通过编写代码来控制计算机行为的过程。',
                createdAt: new Date().toISOString()
            };
            
            const exampleNote2 = {
                id: '2',
                title: 'JavaScript语言',
                content: 'JavaScript是一种脚本语言，可以在网页中实现交互效果。',
                createdAt: new Date().toISOString()
            };
            
            const exampleNote3 = {
                id: '3',
                title: 'HTML基础',
                content: 'HTML是构建网页的基础标记语言。',
                createdAt: new Date().toISOString()
            };
            
            notes = [exampleNote1, exampleNote2, exampleNote3];
            
            // 添加示例关系
            relationships = [
                {
                    id: '1',
                    parentId: '1',
                    childId: '2',
                    createdAt: new Date().toISOString()
                },
                {
                    id: '2',
                    parentId: '1',
                    childId: '3',
                    createdAt: new Date().toISOString()
                }
            ];
            
            saveData();
            renderNoteList();
            updateRelationshipSelects();
            selectNote('1');
        } else if (notes.length > 0 && !currentNoteId) {
            selectNote(notes[0].id);
        }
        
        // 自动调整可视化区域大小
        window.addEventListener('resize', renderVisualization);
    </script>
</body>
</html> 