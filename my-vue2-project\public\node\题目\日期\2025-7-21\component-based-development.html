<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于构件的开发模型 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: bold;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .option {
            background: #f8f9fa;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .option.correct {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            animation: wrongShake 0.6s ease-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
        }

        #gameCanvas {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #gameCanvas:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .controls {
            margin-top: 20px;
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .explanation {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 30px;
            margin-top: 40px;
            animation: fadeIn 1s ease-out;
        }

        .explanation h3 {
            color: #d63384;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .phase-list {
            list-style: none;
            padding: 0;
        }

        .phase-item {
            background: white;
            margin: 10px 0;
            padding: 15px 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            animation: slideInLeft 0.6s ease-out;
            animation-fill-mode: both;
        }

        .phase-item:nth-child(1) { animation-delay: 0.1s; }
        .phase-item:nth-child(2) { animation-delay: 0.2s; }
        .phase-item:nth-child(3) { animation-delay: 0.3s; }
        .phase-item:nth-child(4) { animation-delay: 0.4s; }
        .phase-item:nth-child(5) { animation-delay: 0.5s; }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
    </style>
</head>
<body>
    <canvas class="floating-particles" id="particleCanvas"></canvas>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🏗️ 基于构件的开发模型</h1>
            <p class="subtitle">通过互动游戏学习软件开发的五个阶段</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                基于构件的开发模型包括软件的<span class="highlight">需求分析定义</span>、<span class="highlight">（ ）</span>、<span class="highlight">（/）</span>、<span class="highlight">（/）</span>，以及<span class="highlight">测试和发布</span>5个顺序执行的阶段。
            </div>
            
            <div class="options">
                <div class="option" data-answer="A">
                    <strong>A.</strong> 构件接口设计
                </div>
                <div class="option" data-answer="B">
                    <strong>B.</strong> 体系结构设计
                </div>
                <div class="option" data-answer="C">
                    <strong>C.</strong> 元数据设计
                </div>
                <div class="option" data-answer="D">
                    <strong>D.</strong> 集成环境设计
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <h3>🎮 互动演示：构建你的软件开发流程</h3>
            <canvas id="gameCanvas" width="800" height="500"></canvas>
            <div class="controls">
                <button class="btn" onclick="startAnimation()">🚀 开始演示</button>
                <button class="btn" onclick="resetGame()">🔄 重新开始</button>
                <button class="btn" onclick="showNextPhase()">➡️ 下一阶段</button>
            </div>
        </div>

        <div class="explanation">
            <h3>📚 知识解析</h3>
            <p style="margin-bottom: 20px; font-size: 1.1rem; line-height: 1.6;">
                基于构件的开发模型是一种<strong>模块化</strong>的软件开发方法，它将复杂的系统分解为可重用的构件。这种方法提高了开发效率和软件质量。
            </p>
            
            <ul class="phase-list">
                <li class="phase-item">
                    <strong>1. 需求分析定义</strong> - 明确软件要解决什么问题，用户需要什么功能
                </li>
                <li class="phase-item">
                    <strong>2. 体系结构设计</strong> - 设计软件的整体架构，决定如何组织各个部分
                </li>
                <li class="phase-item">
                    <strong>3. 构件库建立</strong> - 创建或选择可重用的软件构件
                </li>
                <li class="phase-item">
                    <strong>4. 应用软件构建</strong> - 将构件组合起来构建最终的应用程序
                </li>
                <li class="phase-item">
                    <strong>5. 测试和发布</strong> - 验证软件质量并发布给用户使用
                </li>
            </ul>
        </div>
    </div>

    <script>
        // 游戏状态
        let currentPhase = 0;
        let isAnimating = false;
        let gameStarted = false;
        
        // Canvas 设置
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 粒子背景
        const particleCanvas = document.getElementById('particleCanvas');
        const particleCtx = particleCanvas.getContext('2d');
        
        // 调整粒子画布大小
        function resizeParticleCanvas() {
            particleCanvas.width = window.innerWidth;
            particleCanvas.height = window.innerHeight;
        }
        resizeParticleCanvas();
        window.addEventListener('resize', resizeParticleCanvas);
        
        // 粒子系统
        const particles = [];
        for (let i = 0; i < 50; i++) {
            particles.push({
                x: Math.random() * particleCanvas.width,
                y: Math.random() * particleCanvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 3 + 1,
                opacity: Math.random() * 0.5 + 0.2
            });
        }
        
        function animateParticles() {
            particleCtx.clearRect(0, 0, particleCanvas.width, particleCanvas.height);
            
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > particleCanvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > particleCanvas.height) particle.vy *= -1;
                
                particleCtx.beginPath();
                particleCtx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                particleCtx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
                particleCtx.fill();
            });
            
            requestAnimationFrame(animateParticles);
        }
        animateParticles();

        // 开发阶段数据
        const phases = [
            {
                name: "需求分析定义",
                icon: "📋",
                color: "#FF6B6B",
                description: "分析用户需求，明确软件功能",
                x: 100,
                y: 200
            },
            {
                name: "体系结构设计",
                icon: "🏗️",
                color: "#4ECDC4",
                description: "设计软件整体架构",
                x: 250,
                y: 150
            },
            {
                name: "构件库建立",
                icon: "🧩",
                color: "#45B7D1",
                description: "创建可重用的软件构件",
                x: 400,
                y: 200
            },
            {
                name: "应用软件构建",
                icon: "⚙️",
                color: "#96CEB4",
                description: "组合构件构建应用",
                x: 550,
                y: 150
            },
            {
                name: "测试和发布",
                icon: "🚀",
                color: "#FFEAA7",
                description: "测试质量并发布软件",
                x: 700,
                y: 200
            }
        ];

        // 绘制函数
        function drawPhase(phase, index, isActive = false, progress = 0) {
            const scale = isActive ? 1.2 + Math.sin(Date.now() * 0.01) * 0.1 : 1;
            const alpha = isActive ? 1 : (index <= currentPhase ? 0.8 : 0.3);

            ctx.save();
            ctx.globalAlpha = alpha;
            ctx.translate(phase.x, phase.y);
            ctx.scale(scale, scale);

            // 绘制圆形背景
            ctx.beginPath();
            ctx.arc(0, 0, 40, 0, Math.PI * 2);
            ctx.fillStyle = phase.color;
            ctx.fill();

            // 绘制进度环
            if (isActive && progress > 0) {
                ctx.beginPath();
                ctx.arc(0, 0, 45, -Math.PI/2, -Math.PI/2 + (Math.PI * 2 * progress));
                ctx.strokeStyle = '#FFD93D';
                ctx.lineWidth = 4;
                ctx.stroke();
            }

            // 绘制图标
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            ctx.fillText(phase.icon, 0, 0);

            // 绘制标题
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText(phase.name, 0, 60);

            // 绘制描述（当激活时）
            if (isActive) {
                ctx.font = '12px Microsoft YaHei';
                ctx.fillStyle = '#666';
                ctx.fillText(phase.description, 0, 80);
            }

            ctx.restore();
        }

        function drawConnections() {
            ctx.strokeStyle = '#DDD';
            ctx.lineWidth = 3;
            ctx.setLineDash([5, 5]);

            for (let i = 0; i < phases.length - 1; i++) {
                const current = phases[i];
                const next = phases[i + 1];

                ctx.beginPath();
                ctx.moveTo(current.x + 40, current.y);
                ctx.lineTo(next.x - 40, next.y);
                ctx.stroke();
            }
            ctx.setLineDash([]);
        }

        function drawGame() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制连接线
            drawConnections();

            // 绘制阶段
            phases.forEach((phase, index) => {
                const isActive = index === currentPhase && isAnimating;
                const progress = isActive ? (Date.now() % 3000) / 3000 : 0;
                drawPhase(phase, index, isActive, progress);
            });

            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#333';
            ctx.fillText('基于构件的开发模型 - 五个阶段', canvas.width / 2, 50);

            // 绘制当前阶段信息
            if (currentPhase < phases.length) {
                const phase = phases[currentPhase];
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = phase.color;
                ctx.fillText(`当前阶段: ${phase.name}`, canvas.width / 2, canvas.height - 50);
            }
        }

        // 动画控制
        function startAnimation() {
            if (!gameStarted) {
                gameStarted = true;
                currentPhase = 0;
            }
            isAnimating = true;
            animateGame();
        }

        function animateGame() {
            drawGame();
            if (isAnimating) {
                requestAnimationFrame(animateGame);
            }
        }

        function showNextPhase() {
            if (currentPhase < phases.length - 1) {
                currentPhase++;
                drawGame();
            } else {
                // 显示完成动画
                showCompletionAnimation();
            }
        }

        function resetGame() {
            currentPhase = 0;
            isAnimating = false;
            gameStarted = false;
            drawGame();
        }

        function showCompletionAnimation() {
            let animationFrame = 0;
            const maxFrames = 60;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景闪烁效果
                const alpha = 0.5 + 0.5 * Math.sin(animationFrame * 0.3);
                ctx.fillStyle = `rgba(255, 215, 0, ${alpha * 0.3})`;
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 绘制所有阶段
                phases.forEach((phase, index) => {
                    drawPhase(phase, index, true, 1);
                });

                // 绘制庆祝文字
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillStyle = '#FF6B6B';
                ctx.fillText('🎉 恭喜！你已掌握基于构件的开发模型！', canvas.width / 2, canvas.height - 30);

                animationFrame++;
                if (animationFrame < maxFrames) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        // 选择题逻辑
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                const isCorrect = answer === 'B';

                // 清除之前的状态
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });

                if (isCorrect) {
                    this.classList.add('correct');
                    setTimeout(() => {
                        alert('🎉 正确！体系结构设计是基于构件开发模型的第二个阶段，它决定了软件的整体架构和组织方式。');
                        startAnimation();
                    }, 600);
                } else {
                    this.classList.add('wrong');
                    setTimeout(() => {
                        alert('❌ 不正确，请再想想。提示：这个阶段是关于设计软件的整体架构的。');
                    }, 600);
                }
            });
        });

        // 初始化游戏
        drawGame();

        // Canvas 点击事件
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检查是否点击了某个阶段
            phases.forEach((phase, index) => {
                const distance = Math.sqrt((x - phase.x) ** 2 + (y - phase.y) ** 2);
                if (distance < 50) {
                    currentPhase = index;
                    drawGame();

                    // 显示阶段详情
                    setTimeout(() => {
                        alert(`${phase.icon} ${phase.name}\n\n${phase.description}\n\n这是基于构件开发模型的第${index + 1}个阶段。`);
                    }, 100);
                }
            });
        });
    </script>
</body>
</html>
