<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SysML vs UML 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .nav-tabs {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
        }

        .tab-btn {
            padding: 15px 30px;
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .tab-btn:hover, .tab-btn.active {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .content-section {
            display: none;
            animation: fadeInUp 0.8s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .diagram-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 30px;
        }

        .diagram-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 30px;
            color: white;
            text-align: center;
            min-width: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .diagram-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .diagram-box:hover::before {
            animation: shine 0.6s ease-in-out;
        }

        .diagram-box:hover {
            transform: scale(1.05) rotate(2deg);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

        .canvas-container {
            margin: 40px 0;
            text-align: center;
        }

        #animationCanvas {
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }

        .interactive-demo {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            color: white;
        }

        .demo-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .demo-btn {
            padding: 12px 25px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 25px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .demo-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shine {
            0% {
                opacity: 0;
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }
            50% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .comparison-table th,
        .comparison-table td {
            padding: 20px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .comparison-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }

        .comparison-table tr:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        @media (max-width: 768px) {
            .title {
                font-size: 2.5rem;
            }
            
            .diagram-container {
                flex-direction: column;
            }
            
            .demo-controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">SysML vs UML</h1>
            <p class="subtitle">系统建模语言交互式学习平台</p>

            <!-- 题目展示区域 -->
            <div class="question-card" style="background: rgba(255,255,255,0.95); border-radius: 20px; padding: 30px; margin: 30px auto; max-width: 800px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: left;">
                <h2 style="color: #667eea; margin-bottom: 20px; text-align: center;">📝 考试题目</h2>

                <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 25px; border-radius: 15px; margin-bottom: 20px;">
                    <h3 style="margin-bottom: 15px;">题目内容</h3>
                    <p style="font-size: 1.1rem; line-height: 1.6; margin-bottom: 15px;">
                        和 UML 相比 SysML 新增了<strong>（问题 1）</strong>，其中<strong>（问题 2）</strong>用于描绘需求。
                    </p>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                    <div style="background: rgba(102, 126, 234, 0.1); padding: 20px; border-radius: 10px;">
                        <h4 style="color: #667eea; margin-bottom: 10px;">问题1 [单选题]</h4>
                        <div style="margin: 10px 0;">
                            <label style="display: block; margin: 8px 0; padding: 8px; background: rgba(255,255,255,0.5); border-radius: 5px;">
                                <input type="radio" name="q1" value="A" style="margin-right: 8px;"> A. 需求图
                            </label>
                            <label style="display: block; margin: 8px 0; padding: 8px; background: rgba(255,255,255,0.5); border-radius: 5px;">
                                <input type="radio" name="q1" value="B" style="margin-right: 8px;"> B. 用例图
                            </label>
                            <label style="display: block; margin: 8px 0; padding: 8px; background: rgba(255,255,255,0.5); border-radius: 5px;">
                                <input type="radio" name="q1" value="C" style="margin-right: 8px;"> C. 活动图
                            </label>
                            <label style="display: block; margin: 8px 0; padding: 8px; background: rgba(255,255,255,0.5); border-radius: 5px;">
                                <input type="radio" name="q1" value="D" style="margin-right: 8px;"> D. 时序图
                            </label>
                        </div>
                    </div>

                    <div style="background: rgba(240, 147, 251, 0.1); padding: 20px; border-radius: 10px;">
                        <h4 style="color: #f093fb; margin-bottom: 10px;">问题2</h4>
                        <p style="margin: 10px 0; padding: 15px; background: rgba(255,255,255,0.7); border-radius: 8px; border-left: 4px solid #f093fb;">
                            根据问题1的答案，哪种图表用于描绘需求？
                        </p>
                    </div>
                </div>

                <div style="text-align: center; margin: 20px 0;">
                    <button onclick="checkAnswers()" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 15px 30px; border-radius: 25px; font-size: 1.1rem; cursor: pointer; transition: all 0.3s ease;">
                        🎯 查看答案解析
                    </button>
                </div>

                <div id="answerResult" style="margin-top: 20px; padding: 20px; border-radius: 10px; display: none;">
                    <!-- 答案结果将在这里显示 -->
                </div>
            </div>
        </div>

        <div class="nav-tabs">
            <div class="tab-btn active" onclick="showSection('overview')">概述对比</div>
            <div class="tab-btn" onclick="showSection('diagrams')">图表类型</div>
            <div class="tab-btn" onclick="showSection('requirements')">需求建模</div>
            <div class="tab-btn" onclick="showSection('interactive')">交互演示</div>
        </div>

        <!-- 概述对比部分 -->
        <div id="overview" class="content-section active">
            <div class="card">
                <h2>UML vs SysML 基础对比</h2>
                <div class="highlight">
                    <h3>🎯 核心问题解答</h3>
                    <p><strong>问题1：</strong>与UML相比，SysML新增了<strong>需求图</strong></p>
                    <p><strong>问题2：</strong>其中<strong>需求图</strong>用于描绘需求</p>
                </div>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>特性</th>
                            <th>UML</th>
                            <th>SysML</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>主要用途</td>
                            <td>软件系统建模</td>
                            <td>系统工程建模（硬件+软件+人员+流程）</td>
                        </tr>
                        <tr>
                            <td>图表数量</td>
                            <td>14种图表</td>
                            <td>9种主要图表</td>
                        </tr>
                        <tr>
                            <td>新增图表</td>
                            <td>-</td>
                            <td>需求图、参数图、块定义图、内部块图</td>
                        </tr>
                        <tr>
                            <td>应用领域</td>
                            <td>软件开发</td>
                            <td>航空航天、汽车、国防、医疗设备等</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 图表类型部分 -->
        <div id="diagrams" class="content-section">
            <div class="card">
                <h2>SysML 九种主要图表</h2>
                <div class="diagram-container">
                    <div class="diagram-box" onclick="animateDiagram('requirements')">
                        <h3>📋 需求图</h3>
                        <p>Requirements Diagram</p>
                        <small>捕获系统需求</small>
                    </div>
                    <div class="diagram-box" onclick="animateDiagram('usecase')">
                        <h3>👥 用例图</h3>
                        <p>Use Case Diagram</p>
                        <small>描述系统功能</small>
                    </div>
                    <div class="diagram-box" onclick="animateDiagram('block')">
                        <h3>🧱 块定义图</h3>
                        <p>Block Definition Diagram</p>
                        <small>建模系统结构</small>
                    </div>
                    <div class="diagram-box" onclick="animateDiagram('internal')">
                        <h3>🔗 内部块图</h3>
                        <p>Internal Block Diagram</p>
                        <small>内部结构连接</small>
                    </div>
                    <div class="diagram-box" onclick="animateDiagram('parametric')">
                        <h3>📊 参数图</h3>
                        <p>Parametric Diagram</p>
                        <small>约束系统参数</small>
                    </div>
                    <div class="diagram-box" onclick="animateDiagram('activity')">
                        <h3>⚡ 活动图</h3>
                        <p>Activity Diagram</p>
                        <small>建模系统行为</small>
                    </div>
                    <div class="diagram-box" onclick="animateDiagram('sequence')">
                        <h3>📈 序列图</h3>
                        <p>Sequence Diagram</p>
                        <small>时间序列交互</small>
                    </div>
                    <div class="diagram-box" onclick="animateDiagram('state')">
                        <h3>🔄 状态机图</h3>
                        <p>State Machine Diagram</p>
                        <small>状态转换</small>
                    </div>
                    <div class="diagram-box" onclick="animateDiagram('package')">
                        <h3>📦 包图</h3>
                        <p>Package Diagram</p>
                        <small>组织模型元素</small>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="animationCanvas" width="800" height="400"></canvas>
                </div>
            </div>
        </div>

        <!-- 需求建模部分 -->
        <div id="requirements" class="content-section">
            <div class="card">
                <h2>需求图详解</h2>
                <div class="highlight">
                    <h3>🎯 需求图的核心作用</h3>
                    <p>需求图是SysML相对于UML的重要新增，专门用于<strong>捕获和管理系统需求</strong></p>
                </div>

                <div class="interactive-demo">
                    <h3>需求图组成要素</h3>
                    <div class="demo-controls">
                        <button class="demo-btn" onclick="showRequirement('functional')">功能需求</button>
                        <button class="demo-btn" onclick="showRequirement('performance')">性能需求</button>
                        <button class="demo-btn" onclick="showRequirement('interface')">接口需求</button>
                        <button class="demo-btn" onclick="showRequirement('design')">设计约束</button>
                    </div>
                    <div id="requirementDemo" style="min-height: 200px; background: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin: 20px 0;">
                        <p>点击上方按钮查看不同类型的需求示例</p>
                    </div>
                </div>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>需求类型</th>
                            <th>描述</th>
                            <th>示例</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>功能需求</td>
                            <td>系统必须执行的功能</td>
                            <td>系统应能处理用户登录</td>
                        </tr>
                        <tr>
                            <td>性能需求</td>
                            <td>系统性能指标</td>
                            <td>响应时间不超过2秒</td>
                        </tr>
                        <tr>
                            <td>接口需求</td>
                            <td>与外部系统的接口</td>
                            <td>支持REST API接口</td>
                        </tr>
                        <tr>
                            <td>设计约束</td>
                            <td>设计和实现的限制</td>
                            <td>必须使用Java语言开发</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 交互演示部分 -->
        <div id="interactive" class="content-section">
            <div class="card">
                <h2>交互式学习演示</h2>
                <div class="interactive-demo">
                    <h3>SysML vs UML 动态对比</h3>
                    <div class="demo-controls">
                        <button class="demo-btn" onclick="startComparison()">开始对比动画</button>
                        <button class="demo-btn" onclick="showEvolution()">演化历程</button>
                        <button class="demo-btn" onclick="showApplications()">应用场景</button>
                        <button class="demo-btn" onclick="resetDemo()">重置演示</button>
                    </div>
                    <div id="interactiveDemo" style="min-height: 300px; background: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin: 20px 0; position: relative; overflow: hidden;">
                        <div id="demoContent">
                            <p style="text-align: center; margin-top: 100px;">选择上方按钮开始交互式学习</p>
                        </div>
                    </div>
                </div>

                <div class="highlight">
                    <h3>🚀 学习要点总结</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                            ✅ SysML是UML的扩展，专门用于系统工程
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                            ✅ 新增了需求图、参数图、块定义图、内部块图
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                            ✅ 需求图专门用于捕获和管理系统需求
                        </li>
                        <li style="margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                            ✅ 适用于复杂系统工程项目
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 答案检查功能
        function checkAnswers() {
            const q1Answer = document.querySelector('input[name="q1"]:checked');
            const resultDiv = document.getElementById('answerResult');

            let resultHTML = '';

            if (!q1Answer) {
                resultHTML = `
                    <div style="background: rgba(255, 193, 7, 0.2); border-left: 4px solid #ffc107; padding: 20px;">
                        <h4 style="color: #856404;">⚠️ 请先选择答案</h4>
                        <p>请选择问题1的答案后再查看解析。</p>
                    </div>
                `;
            } else {
                const userAnswer = q1Answer.value;
                const isCorrect = userAnswer === 'A';

                resultHTML = `
                    <div style="background: ${isCorrect ? 'rgba(40, 167, 69, 0.2)' : 'rgba(220, 53, 69, 0.2)'}; border-left: 4px solid ${isCorrect ? '#28a745' : '#dc3545'}; padding: 20px; margin-bottom: 20px;">
                        <h4 style="color: ${isCorrect ? '#155724' : '#721c24'};">
                            ${isCorrect ? '✅ 回答正确！' : '❌ 回答错误'}
                        </h4>
                        <p><strong>您的答案：</strong> ${userAnswer}</p>
                        <p><strong>正确答案：</strong> A. 需求图</p>
                    </div>

                    <div style="background: rgba(23, 162, 184, 0.1); border-left: 4px solid #17a2b8; padding: 20px; margin-bottom: 20px;">
                        <h4 style="color: #0c5460;">📚 详细解析</h4>
                        <p><strong>问题1解析：</strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>SysML相对于UML新增了4种主要图表：<strong>需求图、参数图、块定义图、内部块图</strong></li>
                            <li>其中<strong>需求图</strong>是最重要的新增，专门用于系统工程中的需求管理</li>
                            <li>用例图、活动图、时序图在UML中就已经存在</li>
                        </ul>

                        <p><strong>问题2解析：</strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>需求图</strong>专门用于描绘和管理系统需求</li>
                            <li>它可以表示功能需求、性能需求、接口需求、设计约束等</li>
                            <li>需求图支持需求的层次化组织和追踪</li>
                        </ul>
                    </div>

                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px;">
                        <h4>🎯 核心要点</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
                            <div>
                                <h5>SysML新增图表：</h5>
                                <ul style="list-style: none; padding: 0;">
                                    <li>📋 需求图</li>
                                    <li>📊 参数图</li>
                                    <li>🧱 块定义图</li>
                                    <li>🔗 内部块图</li>
                                </ul>
                            </div>
                            <div>
                                <h5>需求图的作用：</h5>
                                <ul style="list-style: none; padding: 0;">
                                    <li>✓ 捕获系统需求</li>
                                    <li>✓ 需求分类管理</li>
                                    <li>✓ 需求追踪</li>
                                    <li>✓ 需求验证</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `;
            }

            resultDiv.innerHTML = resultHTML;
            resultDiv.style.display = 'block';
            resultDiv.style.animation = 'fadeInUp 0.5s ease-out';

            // 滚动到结果区域
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // 页面切换功能
        function showSection(sectionId) {
            // 隐藏所有内容区域
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // 移除所有标签的活动状态
            const tabs = document.querySelectorAll('.tab-btn');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的内容区域
            document.getElementById(sectionId).classList.add('active');

            // 激活对应的标签
            event.target.classList.add('active');
        }

        // Canvas动画功能
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;

        function animateDiagram(diagramType) {
            clearCanvas();

            const animations = {
                requirements: drawRequirementsAnimation,
                usecase: drawUseCaseAnimation,
                block: drawBlockAnimation,
                internal: drawInternalBlockAnimation,
                parametric: drawParametricAnimation,
                activity: drawActivityAnimation,
                sequence: drawSequenceAnimation,
                state: drawStateAnimation,
                package: drawPackageAnimation
            };

            if (animations[diagramType]) {
                animations[diagramType]();
            }
        }

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        }

        function drawRequirementsAnimation() {
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制需求框
                const x = 100 + Math.sin(frame * 0.05) * 20;
                const y = 100;

                ctx.fillStyle = '#667eea';
                ctx.fillRect(x, y, 200, 80);

                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.fillText('需求: 用户登录', x + 20, y + 30);
                ctx.fillText('ID: REQ-001', x + 20, y + 50);

                // 绘制连接线到其他需求
                ctx.strokeStyle = '#764ba2';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(x + 200, y + 40);
                ctx.lineTo(400 + Math.cos(frame * 0.03) * 15, y + 40);
                ctx.stroke();

                // 绘制子需求
                const x2 = 400 + Math.cos(frame * 0.03) * 15;
                ctx.fillStyle = '#f093fb';
                ctx.fillRect(x2, y, 180, 80);

                ctx.fillStyle = 'white';
                ctx.fillText('子需求: 密码验证', x2 + 10, y + 30);
                ctx.fillText('ID: REQ-002', x2 + 10, y + 50);

                frame++;
                animationId = requestAnimationFrame(animate);
            }

            animate();
        }

        function drawUseCaseAnimation() {
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制用例椭圆
                const centerX = 400;
                const centerY = 200;
                const radiusX = 80 + Math.sin(frame * 0.1) * 10;
                const radiusY = 40;

                ctx.fillStyle = '#4facfe';
                ctx.beginPath();
                ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('用户登录', centerX, centerY);

                // 绘制参与者
                const actorX = 150;
                const actorY = 200;

                ctx.strokeStyle = '#00f2fe';
                ctx.lineWidth = 3;

                // 头部
                ctx.beginPath();
                ctx.arc(actorX, actorY - 40, 15, 0, 2 * Math.PI);
                ctx.stroke();

                // 身体
                ctx.beginPath();
                ctx.moveTo(actorX, actorY - 25);
                ctx.lineTo(actorX, actorY + 20);
                ctx.stroke();

                // 手臂
                ctx.beginPath();
                ctx.moveTo(actorX - 20, actorY - 10);
                ctx.lineTo(actorX + 20, actorY - 10);
                ctx.stroke();

                // 腿
                ctx.beginPath();
                ctx.moveTo(actorX, actorY + 20);
                ctx.lineTo(actorX - 15, actorY + 40);
                ctx.moveTo(actorX, actorY + 20);
                ctx.lineTo(actorX + 15, actorY + 40);
                ctx.stroke();

                // 连接线
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(actorX + 20, actorY);
                ctx.lineTo(centerX - radiusX, centerY);
                ctx.stroke();

                frame++;
                animationId = requestAnimationFrame(animate);
            }

            animate();
        }

        // 其他图表动画函数（简化版本）
        function drawBlockAnimation() {
            ctx.fillStyle = '#f093fb';
            ctx.fillRect(200, 150, 150, 100);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('Block: 系统', 220, 180);
            ctx.fillText('属性: 名称', 220, 200);
            ctx.fillText('操作: 启动()', 220, 220);
        }

        function drawInternalBlockAnimation() {
            // 外部块
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.strokeRect(150, 100, 300, 200);

            // 内部组件
            ctx.fillStyle = '#4facfe';
            ctx.fillRect(180, 130, 80, 60);
            ctx.fillRect(280, 130, 80, 60);
            ctx.fillRect(180, 220, 80, 60);
            ctx.fillRect(280, 220, 80, 60);

            // 连接线
            ctx.strokeStyle = '#00f2fe';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(260, 160);
            ctx.lineTo(280, 160);
            ctx.moveTo(220, 190);
            ctx.lineTo(220, 220);
            ctx.stroke();
        }

        function drawParametricAnimation() {
            ctx.fillStyle = '#a8edea';
            ctx.fillRect(200, 150, 200, 100);
            ctx.fillStyle = 'black';
            ctx.font = '14px Arial';
            ctx.fillText('参数约束', 250, 180);
            ctx.fillText('F = m × a', 250, 200);
            ctx.fillText('力 = 质量 × 加速度', 220, 220);
        }

        function drawActivityAnimation() {
            // 开始节点
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(200, 200, 15, 0, 2 * Math.PI);
            ctx.fill();

            // 活动节点
            ctx.fillStyle = '#4facfe';
            ctx.fillRect(300, 180, 120, 40);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('处理数据', 330, 205);

            // 结束节点
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(500, 200, 15, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(500, 200, 8, 0, 2 * Math.PI);
            ctx.fill();

            // 连接线
            ctx.strokeStyle = '#764ba2';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(215, 200);
            ctx.lineTo(300, 200);
            ctx.moveTo(420, 200);
            ctx.lineTo(485, 200);
            ctx.stroke();
        }

        function drawSequenceAnimation() {
            // 生命线
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(200, 100);
            ctx.lineTo(200, 300);
            ctx.moveTo(400, 100);
            ctx.lineTo(400, 300);
            ctx.stroke();

            // 对象
            ctx.fillStyle = '#4facfe';
            ctx.fillRect(150, 80, 100, 30);
            ctx.fillRect(350, 80, 100, 30);

            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('用户', 180, 100);
            ctx.fillText('系统', 380, 100);

            // 消息
            ctx.strokeStyle = '#f093fb';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(200, 150);
            ctx.lineTo(400, 150);
            ctx.stroke();

            ctx.fillStyle = 'black';
            ctx.fillText('登录请求', 280, 145);
        }

        function drawStateAnimation() {
            // 状态
            ctx.fillStyle = '#f093fb';
            ctx.fillRect(200, 150, 120, 60);
            ctx.fillRect(400, 150, 120, 60);

            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('未登录', 240, 185);
            ctx.fillText('已登录', 440, 185);

            // 转换
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(320, 180);
            ctx.lineTo(400, 180);
            ctx.stroke();

            // 箭头
            ctx.beginPath();
            ctx.moveTo(400, 180);
            ctx.lineTo(390, 175);
            ctx.lineTo(390, 185);
            ctx.closePath();
            ctx.fill();

            ctx.fillStyle = 'black';
            ctx.fillText('登录', 350, 175);
        }

        function drawPackageAnimation() {
            // 包
            ctx.fillStyle = '#a8edea';
            ctx.fillRect(200, 150, 200, 120);

            // 包标签
            ctx.fillStyle = '#667eea';
            ctx.fillRect(200, 130, 60, 20);

            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('包名', 215, 145);

            // 包内容
            ctx.fillStyle = 'black';
            ctx.font = '14px Arial';
            ctx.fillText('+ 类A', 220, 180);
            ctx.fillText('+ 类B', 220, 200);
            ctx.fillText('+ 接口C', 220, 220);
        }

        // 需求演示功能
        function showRequirement(type) {
            const demo = document.getElementById('requirementDemo');
            const requirements = {
                functional: {
                    title: '功能需求示例',
                    content: `
                        <h4>🎯 功能需求</h4>
                        <p><strong>需求ID:</strong> REQ-F-001</p>
                        <p><strong>描述:</strong> 系统应当支持用户通过用户名和密码进行登录</p>
                        <p><strong>优先级:</strong> 高</p>
                        <p><strong>验收标准:</strong> 用户输入正确凭据后能成功登录系统</p>
                    `
                },
                performance: {
                    title: '性能需求示例',
                    content: `
                        <h4>⚡ 性能需求</h4>
                        <p><strong>需求ID:</strong> REQ-P-001</p>
                        <p><strong>描述:</strong> 系统登录响应时间不得超过2秒</p>
                        <p><strong>测量标准:</strong> 在正常负载下95%的登录请求</p>
                        <p><strong>测试条件:</strong> 并发用户数不超过1000</p>
                    `
                },
                interface: {
                    title: '接口需求示例',
                    content: `
                        <h4>🔌 接口需求</h4>
                        <p><strong>需求ID:</strong> REQ-I-001</p>
                        <p><strong>描述:</strong> 系统应提供RESTful API接口供第三方系统调用</p>
                        <p><strong>协议:</strong> HTTPS</p>
                        <p><strong>数据格式:</strong> JSON</p>
                    `
                },
                design: {
                    title: '设计约束示例',
                    content: `
                        <h4>🏗️ 设计约束</h4>
                        <p><strong>需求ID:</strong> REQ-D-001</p>
                        <p><strong>描述:</strong> 系统必须使用Java 11或更高版本开发</p>
                        <p><strong>数据库:</strong> 必须支持MySQL 8.0</p>
                        <p><strong>部署:</strong> 支持Docker容器化部署</p>
                    `
                }
            };

            if (requirements[type]) {
                demo.innerHTML = requirements[type].content;
                demo.style.animation = 'fadeInUp 0.5s ease-out';
            }
        }

        // 交互演示功能
        function startComparison() {
            const demo = document.getElementById('demoContent');
            demo.innerHTML = `
                <div style="display: flex; justify-content: space-around; align-items: center; height: 250px;">
                    <div style="text-align: center; animation: pulse 2s infinite;">
                        <h3>UML</h3>
                        <div style="width: 150px; height: 100px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; margin: 10px auto; display: flex; align-items: center; justify-content: center; color: white;">
                            14种图表
                        </div>
                        <p>软件系统建模</p>
                    </div>
                    <div style="font-size: 2rem; animation: pulse 1s infinite;">→</div>
                    <div style="text-align: center; animation: pulse 2s infinite 0.5s;">
                        <h3>SysML</h3>
                        <div style="width: 150px; height: 100px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 10px; margin: 10px auto; display: flex; align-items: center; justify-content: center; color: white;">
                            9种图表
                        </div>
                        <p>系统工程建模</p>
                    </div>
                </div>
            `;
        }

        function showEvolution() {
            const demo = document.getElementById('demoContent');
            demo.innerHTML = `
                <div style="text-align: center;">
                    <h3>SysML发展历程</h3>
                    <div style="display: flex; justify-content: space-around; margin: 30px 0; flex-wrap: wrap;">
                        <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 5px; min-width: 150px;">
                            <h4>1997年</h4>
                            <p>UML 1.0发布</p>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 5px; min-width: 150px;">
                            <h4>2003年</h4>
                            <p>SysML项目启动</p>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 5px; min-width: 150px;">
                            <h4>2007年</h4>
                            <p>SysML 1.0发布</p>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; margin: 5px; min-width: 150px;">
                            <h4>现在</h4>
                            <p>SysML 1.6版本</p>
                        </div>
                    </div>
                </div>
            `;
        }

        function showApplications() {
            const demo = document.getElementById('demoContent');
            demo.innerHTML = `
                <div>
                    <h3>应用场景对比</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                        <div style="background: rgba(102, 126, 234, 0.2); padding: 20px; border-radius: 10px;">
                            <h4>UML适用场景</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>✓ Web应用开发</li>
                                <li>✓ 移动应用开发</li>
                                <li>✓ 企业软件系统</li>
                                <li>✓ 数据库设计</li>
                            </ul>
                        </div>
                        <div style="background: rgba(240, 147, 251, 0.2); padding: 20px; border-radius: 10px;">
                            <h4>SysML适用场景</h4>
                            <ul style="list-style: none; padding: 0;">
                                <li>✓ 航空航天系统</li>
                                <li>✓ 汽车电子系统</li>
                                <li>✓ 医疗设备</li>
                                <li>✓ 国防系统</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        }

        function resetDemo() {
            const demo = document.getElementById('demoContent');
            demo.innerHTML = '<p style="text-align: center; margin-top: 100px;">选择上方按钮开始交互式学习</p>';
            clearCanvas();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些初始动画效果
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.2}s`;
                card.style.animation = 'fadeInUp 0.8s ease-out forwards';
            });
        });
    </script>
</body>
</html>
