<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式系统中断机制 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 60px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transform: translateY(-5px);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: justify;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .interactive-btn:active {
            transform: translateY(0);
        }

        .quiz-container {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .quiz-question {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: scale(1.02);
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
        }

        .quiz-option.wrong {
            background: #f8d7da;
            border-color: #dc3545;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .animated {
            animation: pulse 2s infinite;
        }

        .bouncing {
            animation: bounce 1s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🖥️ 嵌入式系统中断机制学习</h1>
        
        <div class="section">
            <h2 class="section-title">💡 什么是中断？</h2>
            <div class="explanation">
                <span class="highlight">中断</span>是计算机系统中一种重要的机制，就像生活中的"紧急电话"一样。
                想象一下：你正在专心工作，突然电话响了，你需要暂停工作去接电话，处理完后再回到原来的工作。
                计算机的中断机制就是这样工作的！
            </div>
            <div class="canvas-container">
                <canvas id="interruptDemo" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="startInterruptDemo()">🎮 开始中断演示</button>
            <button class="interactive-btn" onclick="resetInterruptDemo()">🔄 重置演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">⚡ 为什么使用中断方式？</h2>
            <div class="explanation">
                在嵌入式系统中，CPU需要处理各种任务。如果没有中断机制，CPU就需要不断地<span class="highlight">轮询检查</span>每个设备的状态，
                这就像你每隔几秒钟就看一次手机，看有没有新消息一样，非常浪费时间和精力！
            </div>
            <div class="canvas-container">
                <canvas id="comparisonDemo" width="600" height="350"></canvas>
            </div>
            <button class="interactive-btn" onclick="showPollingMode()">📱 轮询模式演示</button>
            <button class="interactive-btn" onclick="showInterruptMode()">⚡ 中断模式演示</button>
        </div>

        <div class="section">
            <h2 class="section-title">💾 断点信息保存到哪里？</h2>
            <div class="explanation">
                当中断发生时，CPU需要记住当前正在做什么，这样处理完中断后能够回到原来的位置继续工作。
                这些<span class="highlight">断点信息</span>（包括程序计数器、寄存器状态等）会被保存到<span class="highlight">栈（Stack）</span>中。
                栈就像一个"记忆盒子"，遵循"后进先出"的原则。
            </div>
            <div class="canvas-container">
                <canvas id="stackDemo" width="600" height="400"></canvas>
            </div>
            <button class="interactive-btn" onclick="demonstrateStack()">📚 栈操作演示</button>
            <button class="interactive-btn" onclick="resetStack()">🗑️ 清空栈</button>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 互动测试</h2>
            <div class="quiz-container">
                <div class="quiz-question">
                    嵌入式系统中采用中断方式实现输入输出的主要原因是？
                </div>
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, false)">A. 速度最快</div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">B. CPU不参与操作</div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">C. 实现起来比较容易</div>
                    <div class="quiz-option" onclick="selectAnswer(this, true)">D. 能对突发事件做出快速响应</div>
                </div>
                <div id="quizResult" style="text-align: center; font-weight: bold; margin-top: 20px;"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let animationId;
        let currentStep = 0;
        
        // 中断演示
        function startInterruptDemo() {
            const canvas = document.getElementById('interruptDemo');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制CPU
            drawCPU(ctx, 100, 100, 'normal');
            
            // 绘制正在执行的程序
            drawProgram(ctx, 300, 50, '主程序', '#4CAF50');
            
            // 绘制外设
            drawDevice(ctx, 500, 200, '外设', '#FF9800');
            
            // 开始动画
            animateInterrupt(ctx);
        }
        
        function drawCPU(ctx, x, y, state) {
            ctx.fillStyle = state === 'interrupt' ? '#FF5722' : '#2196F3';
            ctx.fillRect(x, y, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('CPU', x + 40, y + 35);
        }
        
        function drawProgram(ctx, x, y, text, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, 100, 40);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x + 50, y + 25);
        }
        
        function drawDevice(ctx, x, y, text, color) {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, 2 * Math.PI);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y + 5);
        }
        
        function animateInterrupt(ctx) {
            let step = 0;
            const animate = () => {
                ctx.clearRect(0, 0, 600, 300);
                
                switch(step) {
                    case 0:
                        // 正常执行
                        drawCPU(ctx, 100, 100, 'normal');
                        drawProgram(ctx, 300, 50, '主程序执行中...', '#4CAF50');
                        drawDevice(ctx, 500, 200, '外设', '#FF9800');
                        ctx.fillStyle = '#333';
                        ctx.font = '16px Arial';
                        ctx.textAlign = 'left';
                        ctx.fillText('1. CPU正在执行主程序', 50, 250);
                        break;
                        
                    case 1:
                        // 中断信号
                        drawCPU(ctx, 100, 100, 'normal');
                        drawProgram(ctx, 300, 50, '主程序', '#4CAF50');
                        drawDevice(ctx, 500, 200, '外设', '#FF5722');
                        
                        // 绘制中断信号
                        ctx.strokeStyle = '#FF5722';
                        ctx.lineWidth = 3;
                        ctx.beginPath();
                        ctx.moveTo(470, 200);
                        ctx.lineTo(180, 130);
                        ctx.stroke();
                        
                        // 绘制闪电符号
                        ctx.fillStyle = '#FFD700';
                        ctx.font = '20px Arial';
                        ctx.fillText('⚡', 320, 150);
                        
                        ctx.fillStyle = '#333';
                        ctx.font = '16px Arial';
                        ctx.fillText('2. 外设发送中断信号', 50, 250);
                        break;
                        
                    case 2:
                        // CPU响应中断
                        drawCPU(ctx, 100, 100, 'interrupt');
                        drawProgram(ctx, 300, 50, '主程序(暂停)', '#9E9E9E');
                        drawProgram(ctx, 300, 120, '中断服务程序', '#FF5722');
                        drawDevice(ctx, 500, 200, '外设', '#FF9800');
                        
                        ctx.fillStyle = '#333';
                        ctx.font = '16px Arial';
                        ctx.fillText('3. CPU暂停主程序，执行中断服务程序', 50, 250);
                        break;
                        
                    case 3:
                        // 恢复执行
                        drawCPU(ctx, 100, 100, 'normal');
                        drawProgram(ctx, 300, 50, '主程序(恢复执行)', '#4CAF50');
                        drawDevice(ctx, 500, 200, '外设', '#FF9800');
                        
                        ctx.fillStyle = '#333';
                        ctx.font = '16px Arial';
                        ctx.fillText('4. 中断处理完成，恢复主程序执行', 50, 250);
                        break;
                }
                
                step = (step + 1) % 4;
                setTimeout(() => requestAnimationFrame(animate), 2000);
            };
            animate();
        }
        
        function resetInterruptDemo() {
            const canvas = document.getElementById('interruptDemo');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制初始状态
            drawCPU(ctx, 100, 100, 'normal');
            drawProgram(ctx, 300, 50, '主程序', '#4CAF50');
            drawDevice(ctx, 500, 200, '外设', '#FF9800');
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('点击"开始中断演示"查看动画', 50, 250);
        }
        
        // 比较演示
        function showPollingMode() {
            const canvas = document.getElementById('comparisonDemo');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('轮询模式 - CPU需要不断检查', 300, 30);
            
            // 绘制CPU和多个设备
            drawCPU(ctx, 50, 150, 'normal');
            
            const devices = ['键盘', '鼠标', '网卡', '硬盘'];
            for(let i = 0; i < devices.length; i++) {
                drawDevice(ctx, 200 + i * 100, 150, devices[i], '#FF9800');
                
                // 绘制检查箭头
                ctx.strokeStyle = '#666';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(130, 150);
                ctx.lineTo(170 + i * 100, 150);
                ctx.stroke();
                
                // 绘制问号
                ctx.fillStyle = '#666';
                ctx.font = '16px Arial';
                ctx.fillText('?', 150 + i * 50, 140);
            }
            
            ctx.fillStyle = '#FF5722';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('CPU必须逐个询问每个设备："你有数据吗？"', 300, 250);
            ctx.fillText('即使设备没有数据，CPU也要花时间检查', 300, 270);
            ctx.fillText('效率低下！', 300, 290);
        }
        
        function showInterruptMode() {
            const canvas = document.getElementById('comparisonDemo');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('中断模式 - 设备主动通知CPU', 300, 30);
            
            // 绘制CPU
            drawCPU(ctx, 50, 150, 'normal');
            
            const devices = ['键盘', '鼠标', '网卡', '硬盘'];
            for(let i = 0; i < devices.length; i++) {
                const color = i === 1 ? '#FF5722' : '#9E9E9E'; // 鼠标有数据
                drawDevice(ctx, 200 + i * 100, 150, devices[i], color);
                
                if(i === 1) {
                    // 绘制中断信号
                    ctx.strokeStyle = '#FF5722';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(270, 150);
                    ctx.lineTo(130, 150);
                    ctx.stroke();
                    
                    // 绘制感叹号
                    ctx.fillStyle = '#FF5722';
                    ctx.font = '20px Arial';
                    ctx.fillText('!', 200, 140);
                }
            }
            
            ctx.fillStyle = '#4CAF50';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('只有需要服务的设备才会发送中断信号', 300, 250);
            ctx.fillText('CPU可以专心处理其他任务', 300, 270);
            ctx.fillText('高效响应！', 300, 290);
        }
        
        // 栈演示
        function demonstrateStack() {
            const canvas = document.getElementById('stackDemo');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制栈结构
            const stackX = 250;
            const stackY = 100;
            const stackWidth = 100;
            const stackHeight = 40;
            
            // 栈底
            ctx.fillStyle = '#E0E0E0';
            ctx.fillRect(stackX, stackY + stackHeight * 4, stackWidth, stackHeight);
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('栈底', stackX + stackWidth/2, stackY + stackHeight * 4 + 25);
            
            // 保存的信息
            const stackData = ['程序计数器', '寄存器A', '寄存器B', '状态寄存器'];
            const colors = ['#FF9800', '#4CAF50', '#2196F3', '#9C27B0'];
            
            for(let i = 0; i < stackData.length; i++) {
                ctx.fillStyle = colors[i];
                ctx.fillRect(stackX, stackY + stackHeight * (3-i), stackWidth, stackHeight);
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.fillText(stackData[i], stackX + stackWidth/2, stackY + stackHeight * (3-i) + 25);
            }
            
            // 栈顶指针
            ctx.fillStyle = '#FF5722';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('栈顶 →', stackX + stackWidth + 10, stackY + 25);
            
            // 说明文字
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('中断发生时，CPU状态信息保存到栈中', 300, 50);
            
            // 绘制箭头和说明
            ctx.fillStyle = '#666';
            ctx.font = '12px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('1. 最后保存的信息', 400, 120);
            ctx.fillText('2. 第三个保存的信息', 400, 160);
            ctx.fillText('3. 第二个保存的信息', 400, 200);
            ctx.fillText('4. 最先保存的信息', 400, 240);
            
            // 绘制连接线
            for(let i = 0; i < 4; i++) {
                ctx.strokeStyle = '#666';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(stackX + stackWidth, stackY + stackHeight * i + stackHeight/2);
                ctx.lineTo(390, 115 + i * 40);
                ctx.stroke();
            }
            
            ctx.fillStyle = '#FF5722';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('恢复时按相反顺序取出（后进先出）', 300, 350);
        }
        
        function resetStack() {
            const canvas = document.getElementById('stackDemo');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击"栈操作演示"查看栈的工作原理', 300, 200);
        }
        
        // 测试功能
        function selectAnswer(element, isCorrect) {
            // 清除之前的选择
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.classList.remove('correct', 'wrong');
            });
            
            // 显示结果
            if(isCorrect) {
                element.classList.add('correct');
                document.getElementById('quizResult').innerHTML = 
                    '🎉 正确！中断机制能让CPU对突发事件做出快速响应，提高系统效率！';
                document.getElementById('quizResult').style.color = '#28a745';
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                options[3].classList.add('correct');
                document.getElementById('quizResult').innerHTML = 
                    '❌ 不正确。正确答案是D：能对突发事件做出快速响应。';
                document.getElementById('quizResult').style.color = '#dc3545';
            }
            
            // 添加动画效果
            element.classList.add('bouncing');
            setTimeout(() => {
                element.classList.remove('bouncing');
            }, 1000);
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            resetInterruptDemo();
            resetStack();
            
            // 添加一些交互提示
            const canvas1 = document.getElementById('interruptDemo');
            const canvas2 = document.getElementById('comparisonDemo');
            const canvas3 = document.getElementById('stackDemo');
            
            [canvas1, canvas2, canvas3].forEach(canvas => {
                canvas.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                });
                
                canvas.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        };
    </script>
</body>
</html>
