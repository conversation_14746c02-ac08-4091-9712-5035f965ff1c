<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件可靠性 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-secondary:hover {
            background: #667eea;
            color: white;
        }

        .explanation {
            background: #f8f9ff;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.8;
            margin-bottom: 15px;
        }

        .quiz-container {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .quiz-option {
            background: rgba(255,255,255,0.2);
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .quiz-option.selected {
            border-color: white;
            background: rgba(255,255,255,0.4);
        }

        .quiz-option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .quiz-option.incorrect {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        .score-display {
            text-align: center;
            font-size: 1.5rem;
            margin: 20px 0;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .metric-title {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 10px;
        }

        .metric-desc {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件可靠性学习</h1>
            <p class="subtitle">通过互动动画理解软件可靠性的核心概念</p>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是软件可靠性？</h2>
            <div class="explanation">
                <h3>核心定义</h3>
                <p><strong>软件可靠性</strong>是指软件系统在规定的时间内和规定条件下能有效地实现规定功能的能力。</p>
                <p>简单来说，就是软件能够<strong>稳定运行、不出错</strong>的能力！</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="reliabilityCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startReliabilityDemo()">🚀 开始演示</button>
                <button class="btn btn-secondary" onclick="resetReliabilityDemo()">🔄 重置</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📊 可靠性度量指标</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-icon">⚡</div>
                    <div class="metric-title">故障率</div>
                    <div class="metric-desc">单位时间内发生故障的次数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">⏱️</div>
                    <div class="metric-title">平均失效等待时间</div>
                    <div class="metric-desc">从开始运行到第一次失效的平均时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">🔄</div>
                    <div class="metric-title">平均失效间隔时间</div>
                    <div class="metric-desc">两次连续失效之间的平均时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-icon">📈</div>
                    <div class="metric-title">可靠度</div>
                    <div class="metric-desc">规定时间内无故障的概率</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎮 可靠度互动游戏</h2>
            <div class="explanation">
                <h3>可靠度 = 无故障概率</h3>
                <p>可靠度是软件系统在规定条件下、规定时间内<strong>不发生失效的概率</strong>。</p>
                <p>点击下面的服务器，观察它们的运行状态，理解可靠度的概念！</p>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="300"></canvas>
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="startGame()">🎯 开始游戏</button>
                <button class="btn btn-secondary" onclick="resetGame()">🔄 重新开始</button>
            </div>

            <div class="score-display" id="scoreDisplay">
                点击开始游戏，测试不同软件的可靠性！
            </div>
        </div>

        <div class="quiz-container">
            <h2 style="text-align: center; margin-bottom: 30px;">🧠 知识测验</h2>
            <div class="quiz-question" id="quizQuestion">
                系统（ ）是指在规定的时间内和规定条件下能有效地实现规定功能的能力。它不仅取决于规定的使用条件等因素,还与设计技术有关。常用的度量指标主要有故障率(或失效率)、平均失效等待时间、平均失效间隔时间和可靠度等。其中，（ ）是系统在规定工作时间内无故障的概率。
            </div>
            <div class="quiz-options" id="quizOptions">
                <div class="quiz-option" onclick="selectOption(this, 'A')">A. 可靠性</div>
                <div class="quiz-option" onclick="selectOption(this, 'B')">B. 可用性</div>
                <div class="quiz-option" onclick="selectOption(this, 'C')">C. 可理解性</div>
                <div class="quiz-option" onclick="selectOption(this, 'D')">D. 可测试性</div>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="checkAnswer()">✅ 提交答案</button>
                <button class="btn btn-secondary" onclick="showExplanation()">💡 查看解析</button>
            </div>
            <div id="answerFeedback" style="margin-top: 20px; display: none;"></div>
        </div>

        <div class="section" id="explanationSection" style="display: none;">
            <h2 class="section-title">📚 详细解析</h2>
            <div class="highlight">
                <h3>正确答案：A. 可靠性</h3>
                <p><strong>解析：</strong></p>
                <p>1. <strong>可靠性(Reliability)</strong>：指产品在规定的条件下和规定的时间内完成规定功能的能力。</p>
                <p>2. <strong>可靠度</strong>：就是软件系统在规定的条件下、规定的时间内不发生失效的概率。</p>
                <p>3. 其他选项解释：</p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li><strong>可用性</strong>：系统能够正常运行的时间比例</li>
                    <li><strong>可理解性</strong>：软件代码和文档的易理解程度</li>
                    <li><strong>可测试性</strong>：软件被测试的难易程度</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 可靠性演示动画
        let reliabilityCanvas = document.getElementById('reliabilityCanvas');
        let reliabilityCtx = reliabilityCanvas.getContext('2d');
        let reliabilityAnimationId;
        let reliabilityTime = 0;
        let systems = [];

        // 游戏相关变量
        let gameCanvas = document.getElementById('gameCanvas');
        let gameCtx = gameCanvas.getContext('2d');
        let gameAnimationId;
        let servers = [];
        let gameScore = 0;
        let gameTime = 0;
        let gameRunning = false;

        // 测验相关变量
        let selectedAnswer = null;
        let quizCompleted = false;

        // 初始化系统
        function initSystems() {
            systems = [
                { x: 100, y: 200, reliability: 0.95, status: 'running', name: '高可靠系统', color: '#4CAF50', failures: 0 },
                { x: 300, y: 200, reliability: 0.80, status: 'running', name: '中等可靠系统', color: '#FF9800', failures: 0 },
                { x: 500, y: 200, reliability: 0.60, status: 'running', name: '低可靠系统', color: '#f44336', failures: 0 },
                { x: 700, y: 200, reliability: 0.99, status: 'running', name: '超高可靠系统', color: '#2196F3', failures: 0 }
            ];
        }

        // 绘制系统
        function drawSystems() {
            reliabilityCtx.clearRect(0, 0, reliabilityCanvas.width, reliabilityCanvas.height);

            // 绘制标题
            reliabilityCtx.fillStyle = '#333';
            reliabilityCtx.font = 'bold 20px Microsoft YaHei';
            reliabilityCtx.textAlign = 'center';
            reliabilityCtx.fillText('软件系统可靠性对比演示', reliabilityCanvas.width / 2, 30);

            systems.forEach((system, index) => {
                // 绘制系统图标
                reliabilityCtx.fillStyle = system.status === 'running' ? system.color : '#ccc';
                reliabilityCtx.fillRect(system.x - 30, system.y - 30, 60, 60);

                // 绘制系统状态指示灯
                reliabilityCtx.beginPath();
                reliabilityCtx.arc(system.x + 25, system.y - 25, 8, 0, 2 * Math.PI);
                reliabilityCtx.fillStyle = system.status === 'running' ? '#4CAF50' : '#f44336';
                reliabilityCtx.fill();

                // 绘制系统名称
                reliabilityCtx.fillStyle = '#333';
                reliabilityCtx.font = '12px Microsoft YaHei';
                reliabilityCtx.textAlign = 'center';
                reliabilityCtx.fillText(system.name, system.x, system.y + 50);

                // 绘制可靠性数值
                reliabilityCtx.fillText(`可靠度: ${(system.reliability * 100).toFixed(0)}%`, system.x, system.y + 65);

                // 绘制故障次数
                reliabilityCtx.fillText(`故障次数: ${system.failures}`, system.x, system.y + 80);

                // 模拟故障
                if (Math.random() > system.reliability && system.status === 'running') {
                    system.status = 'failed';
                    system.failures++;
                    setTimeout(() => {
                        system.status = 'running';
                    }, 2000);
                }
            });

            // 绘制时间轴
            reliabilityCtx.fillStyle = '#666';
            reliabilityCtx.font = '16px Microsoft YaHei';
            reliabilityCtx.textAlign = 'left';
            reliabilityCtx.fillText(`运行时间: ${Math.floor(reliabilityTime / 60)}秒`, 20, 350);
        }

        // 开始可靠性演示
        function startReliabilityDemo() {
            initSystems();
            reliabilityTime = 0;

            function animate() {
                reliabilityTime++;
                drawSystems();
                reliabilityAnimationId = requestAnimationFrame(animate);
            }

            animate();
        }

        // 重置可靠性演示
        function resetReliabilityDemo() {
            if (reliabilityAnimationId) {
                cancelAnimationFrame(reliabilityAnimationId);
            }
            reliabilityCtx.clearRect(0, 0, reliabilityCanvas.width, reliabilityCanvas.height);
            reliabilityCtx.fillStyle = '#333';
            reliabilityCtx.font = '20px Microsoft YaHei';
            reliabilityCtx.textAlign = 'center';
            reliabilityCtx.fillText('点击"开始演示"观看可靠性动画', reliabilityCanvas.width / 2, reliabilityCanvas.height / 2);
        }

        // 初始化游戏服务器
        function initServers() {
            servers = [];
            for (let i = 0; i < 6; i++) {
                servers.push({
                    x: 100 + i * 120,
                    y: 150,
                    reliability: 0.7 + Math.random() * 0.3,
                    status: 'running',
                    clicked: false,
                    uptime: 0
                });
            }
        }

        // 绘制游戏
        function drawGame() {
            gameCtx.clearRect(0, 0, gameCanvas.width, gameCanvas.height);

            // 绘制标题
            gameCtx.fillStyle = '#333';
            gameCtx.font = 'bold 18px Microsoft YaHei';
            gameCtx.textAlign = 'center';
            gameCtx.fillText('点击服务器测试可靠性 - 绿色=正常运行，红色=故障', gameCanvas.width / 2, 30);

            servers.forEach((server, index) => {
                // 绘制服务器
                gameCtx.fillStyle = server.status === 'running' ? '#4CAF50' : '#f44336';
                gameCtx.fillRect(server.x - 25, server.y - 25, 50, 50);

                // 绘制可靠度
                gameCtx.fillStyle = '#333';
                gameCtx.font = '12px Microsoft YaHei';
                gameCtx.textAlign = 'center';
                gameCtx.fillText(`${(server.reliability * 100).toFixed(0)}%`, server.x, server.y + 40);

                // 绘制运行时间
                gameCtx.fillText(`${server.uptime}s`, server.x, server.y + 55);

                // 模拟故障
                if (gameRunning && Math.random() > server.reliability) {
                    server.status = 'failed';
                    setTimeout(() => {
                        server.status = 'running';
                    }, 1000);
                }

                if (server.status === 'running') {
                    server.uptime++;
                }
            });

            // 绘制游戏时间
            gameCtx.fillStyle = '#666';
            gameCtx.font = '16px Microsoft YaHei';
            gameCtx.textAlign = 'left';
            gameCtx.fillText(`游戏时间: ${Math.floor(gameTime / 60)}秒`, 20, 250);
        }

        // 开始游戏
        function startGame() {
            initServers();
            gameTime = 0;
            gameScore = 0;
            gameRunning = true;

            function gameLoop() {
                if (gameRunning) {
                    gameTime++;
                    drawGame();
                    updateScore();
                    gameAnimationId = requestAnimationFrame(gameLoop);
                }
            }

            gameLoop();
        }

        // 重置游戏
        function resetGame() {
            gameRunning = false;
            if (gameAnimationId) {
                cancelAnimationFrame(gameAnimationId);
            }
            gameCtx.clearRect(0, 0, gameCanvas.width, gameCanvas.height);
            gameCtx.fillStyle = '#333';
            gameCtx.font = '20px Microsoft YaHei';
            gameCtx.textAlign = 'center';
            gameCtx.fillText('点击"开始游戏"体验可靠性测试', gameCanvas.width / 2, gameCanvas.height / 2);
            document.getElementById('scoreDisplay').textContent = '点击开始游戏，测试不同软件的可靠性！';
        }

        // 更新分数
        function updateScore() {
            let totalUptime = servers.reduce((sum, server) => sum + server.uptime, 0);
            let avgReliability = servers.reduce((sum, server) => sum + server.reliability, 0) / servers.length;
            gameScore = Math.floor(totalUptime * avgReliability);

            document.getElementById('scoreDisplay').innerHTML = `
                总运行时间: ${totalUptime}秒 | 平均可靠度: ${(avgReliability * 100).toFixed(1)}% | 得分: ${gameScore}
            `;
        }

        // 游戏画布点击事件
        gameCanvas.addEventListener('click', function(e) {
            if (!gameRunning) return;

            let rect = gameCanvas.getBoundingClientRect();
            let x = e.clientX - rect.left;
            let y = e.clientY - rect.top;

            servers.forEach(server => {
                if (x >= server.x - 25 && x <= server.x + 25 &&
                    y >= server.y - 25 && y <= server.y + 25) {
                    server.clicked = true;
                    // 点击效果
                    gameCtx.strokeStyle = '#FFD700';
                    gameCtx.lineWidth = 3;
                    gameCtx.strokeRect(server.x - 25, server.y - 25, 50, 50);
                }
            });
        });

        // 选择答案
        function selectOption(element, answer) {
            if (quizCompleted) return;

            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // 标记当前选择
            element.classList.add('selected');
            selectedAnswer = answer;
        }

        // 检查答案
        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }

            quizCompleted = true;
            let options = document.querySelectorAll('.quiz-option');

            options.forEach(option => {
                if (option.textContent.startsWith('A.')) {
                    option.classList.add('correct');
                } else if (option.classList.contains('selected') && selectedAnswer !== 'A') {
                    option.classList.add('incorrect');
                }
            });

            let feedback = document.getElementById('answerFeedback');
            feedback.style.display = 'block';

            if (selectedAnswer === 'A') {
                feedback.innerHTML = `
                    <div style="background: rgba(76, 175, 80, 0.2); padding: 20px; border-radius: 10px; border-left: 4px solid #4CAF50;">
                        <h3 style="color: #4CAF50; margin-bottom: 10px;">🎉 回答正确！</h3>
                        <p>可靠性确实是指系统在规定时间内和条件下能有效实现规定功能的能力。</p>
                    </div>
                `;
            } else {
                feedback.innerHTML = `
                    <div style="background: rgba(244, 67, 54, 0.2); padding: 20px; border-radius: 10px; border-left: 4px solid #f44336;">
                        <h3 style="color: #f44336; margin-bottom: 10px;">❌ 回答错误</h3>
                        <p>正确答案是A. 可靠性。点击"查看解析"了解详细说明。</p>
                    </div>
                `;
            }
        }

        // 显示解析
        function showExplanation() {
            document.getElementById('explanationSection').style.display = 'block';
            document.getElementById('explanationSection').scrollIntoView({ behavior: 'smooth' });
        }

        // 初始化页面
        window.onload = function() {
            resetReliabilityDemo();
            resetGame();
        };
    </script>
</body>
</html>
