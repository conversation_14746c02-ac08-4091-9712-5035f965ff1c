<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桥接模式 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .game-area {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .section {
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
            line-height: 1.6;
        }

        .quiz-container {
            background: linear-gradient(135deg, #ffeaa7, #fab1a0);
            padding: 30px;
            border-radius: 20px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #2d3436;
            font-weight: bold;
        }

        .quiz-options {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }

        .quiz-option {
            padding: 15px;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .quiz-option:hover {
            background: #e8f4f8;
            transform: translateX(5px);
        }

        .quiz-option.selected {
            border-color: #667eea;
            background: #e8f4f8;
        }

        .quiz-option.correct {
            border-color: #00b894;
            background: #d1f2eb;
        }

        .quiz-option.wrong {
            border-color: #e17055;
            background: #ffeaa7;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .floating-element {
            position: absolute;
            animation: bounce 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌉 桥接模式学习之旅</h1>
            <p>通过互动动画理解设计模式的奥秘</p>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="game-area">
            <!-- 第一部分：题目展示 -->
            <div class="section visible" id="section1">
                <h2 class="section-title">📚 原题回顾</h2>
                <div class="explanation">
                    <h3>题目背景：</h3>
                    <p>某广告公司的宣传产品有<strong>宣传册、文章、传单</strong>等多种形式，宣传产品的出版方式包括<strong>纸质方式、CD、DVD、在线发布</strong>等。现要求为该广告公司设计一个管理这些宣传产品的应用。</p>
                    <br>
                    <h3>问题：采用（）设计模式较为合适？</h3>
                    <p><strong>A.</strong> 将一系列复杂的类包装成一个简单的封闭接口</p>
                    <p><strong>B.</strong> 将抽象部分与它的实现部分分离，使它们都可以独立地变化</p>
                    <p><strong>C.</strong> 可在不影响其他对象的情况下，以动态、透明的方式给单个对象添加职责</p>
                    <p><strong>D.</strong> 将一个接口转换为客户希望的另一个接口</p>
                </div>
                <div class="controls">
                    <button class="btn" onclick="nextSection()">开始学习 🚀</button>
                </div>
            </div>

            <!-- 第二部分：问题分析 -->
            <div class="section" id="section2">
                <h2 class="section-title">🔍 问题分析</h2>
                <div class="canvas-container">
                    <canvas id="problemCanvas" width="800" height="400"></canvas>
                </div>
                <div class="explanation">
                    <p>让我们分析一下这个问题：我们有两个维度的变化</p>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li><strong>宣传产品类型</strong>：宣传册、文章、传单（可能还会增加新类型）</li>
                        <li><strong>出版方式</strong>：纸质、CD、DVD、在线发布（可能还会增加新方式）</li>
                    </ul>
                    <p style="margin-top: 15px;">如果用传统方式，我们需要为每种组合创建一个类，这会导致类爆炸问题！</p>
                </div>
                <div class="controls">
                    <button class="btn" onclick="animateProblem()">演示问题 🎭</button>
                    <button class="btn" onclick="nextSection()">继续学习</button>
                </div>
            </div>

            <!-- 第三部分：桥接模式介绍 -->
            <div class="section" id="section3">
                <h2 class="section-title">🌉 桥接模式解决方案</h2>
                <div class="canvas-container">
                    <canvas id="bridgeCanvas" width="800" height="500"></canvas>
                </div>
                <div class="explanation">
                    <h3>桥接模式的核心思想：</h3>
                    <p><strong>"将抽象部分与它的实现部分分离，使它们都可以独立地变化"</strong></p>
                    <ul style="margin-left: 20px; margin-top: 10px;">
                        <li>🎯 <strong>抽象部分</strong>：宣传产品（宣传册、文章、传单）</li>
                        <li>⚙️ <strong>实现部分</strong>：出版方式（纸质、CD、DVD、在线）</li>
                        <li>🌉 <strong>桥接</strong>：通过组合而非继承连接两者</li>
                    </ul>
                </div>
                <div class="controls">
                    <button class="btn" onclick="animateBridge()">演示桥接 🎬</button>
                    <button class="btn" onclick="nextSection()">继续学习</button>
                </div>
            </div>

            <!-- 第四部分：交互式演示 -->
            <div class="section" id="section4">
                <h2 class="section-title">🎮 交互式体验</h2>
                <div class="canvas-container">
                    <canvas id="interactiveCanvas" width="800" height="500"></canvas>
                </div>
                <div class="explanation">
                    <p>点击下面的按钮，体验桥接模式的灵活性：</p>
                </div>
                <div class="controls">
                    <button class="btn" onclick="selectProduct('brochure')">选择宣传册 📖</button>
                    <button class="btn" onclick="selectProduct('article')">选择文章 📄</button>
                    <button class="btn" onclick="selectProduct('flyer')">选择传单 📋</button>
                </div>
                <div class="controls">
                    <button class="btn" onclick="selectMethod('paper')">纸质出版 📰</button>
                    <button class="btn" onclick="selectMethod('cd')">CD出版 💿</button>
                    <button class="btn" onclick="selectMethod('dvd')">DVD出版 📀</button>
                    <button class="btn" onclick="selectMethod('online')">在线发布 🌐</button>
                </div>
                <div class="controls">
                    <button class="btn" onclick="nextSection()">查看对比</button>
                </div>
            </div>

            <!-- 第五部分：模式对比 -->
            <div class="section" id="section5">
                <h2 class="section-title">⚖️ 设计模式对比</h2>
                <div class="canvas-container">
                    <canvas id="comparisonCanvas" width="800" height="400"></canvas>
                </div>
                <div class="explanation">
                    <h3>四个选项对应的设计模式：</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                        <div style="padding: 15px; background: #e8f4f8; border-radius: 10px;">
                            <strong>A. 外观模式 (Facade)</strong><br>
                            简化复杂子系统的接口
                        </div>
                        <div style="padding: 15px; background: #d1f2eb; border-radius: 10px;">
                            <strong>B. 桥接模式 (Bridge)</strong><br>
                            分离抽象与实现，独立变化
                        </div>
                        <div style="padding: 15px; background: #ffeaa7; border-radius: 10px;">
                            <strong>C. 装饰器模式 (Decorator)</strong><br>
                            动态添加对象职责
                        </div>
                        <div style="padding: 15px; background: #fab1a0; border-radius: 10px;">
                            <strong>D. 适配器模式 (Adapter)</strong><br>
                            接口转换和兼容
                        </div>
                    </div>
                </div>
                <div class="controls">
                    <button class="btn" onclick="showPatternComparison()">演示对比 🔄</button>
                    <button class="btn" onclick="nextSection()">进入测试</button>
                </div>
            </div>

            <!-- 第六部分：知识测试 -->
            <div class="section" id="section6">
                <h2 class="section-title">🎯 知识测试</h2>
                <div class="quiz-container">
                    <div class="quiz-question">
                        根据学习内容，某广告公司的宣传产品管理应用应该采用哪种设计模式？
                    </div>
                    <div class="quiz-options">
                        <div class="quiz-option" onclick="selectAnswer(this, 'A')">
                            <strong>A.</strong> 外观模式 - 将一系列复杂的类包装成一个简单的封闭接口
                        </div>
                        <div class="quiz-option" onclick="selectAnswer(this, 'B')">
                            <strong>B.</strong> 桥接模式 - 将抽象部分与它的实现部分分离，使它们都可以独立地变化
                        </div>
                        <div class="quiz-option" onclick="selectAnswer(this, 'C')">
                            <strong>C.</strong> 装饰器模式 - 可在不影响其他对象的情况下，以动态、透明的方式给单个对象添加职责
                        </div>
                        <div class="quiz-option" onclick="selectAnswer(this, 'D')">
                            <strong>D.</strong> 适配器模式 - 将一个接口转换为客户希望的另一个接口
                        </div>
                    </div>
                    <div class="controls">
                        <button class="btn" id="submitBtn" onclick="submitAnswer()" style="display: none;">提交答案 ✅</button>
                        <button class="btn" id="restartBtn" onclick="restart()" style="display: none;">重新学习 🔄</button>
                    </div>
                </div>
                <div id="result" class="explanation" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let currentSection = 1;
        let animationId;
        let selectedProduct = null;
        let selectedMethod = null;
        let selectedAnswer = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
            initProblemCanvas();
        });

        function updateProgress() {
            const progress = (currentSection / 6) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        function nextSection() {
            // 隐藏当前section
            document.getElementById(`section${currentSection}`).classList.remove('visible');

            currentSection++;
            updateProgress();

            // 显示下一个section
            setTimeout(() => {
                if (currentSection <= 6) {
                    const nextSectionElement = document.getElementById(`section${currentSection}`);
                    if (nextSectionElement) {
                        nextSectionElement.classList.add('visible');
                        nextSectionElement.scrollIntoView({ behavior: 'smooth' });
                    }

                    // 根据section初始化相应的canvas
                    if (currentSection === 3) {
                        initBridgeCanvas();
                    } else if (currentSection === 4) {
                        initInteractiveCanvas();
                    } else if (currentSection === 5) {
                        initComparisonCanvas();
                    }
                }
            }, 300);
        }

        function initProblemCanvas() {
            const canvas = document.getElementById('problemCanvas');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            // 绘制问题场景
            function drawProblemScene() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 标题
                ctx.fillStyle = '#2d3436';
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('传统方式 vs 桥接模式', canvas.width/2, 40);
                
                // 绘制产品类型
                const products = ['宣传册', '文章', '传单'];
                const methods = ['纸质', 'CD', 'DVD', '在线'];
                
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = '#667eea';
                ctx.fillText('宣传产品类型', 150, 80);
                
                products.forEach((product, i) => {
                    ctx.fillStyle = '#74b9ff';
                    ctx.fillRect(50 + i * 80, 100, 70, 40);
                    ctx.fillStyle = 'white';
                    ctx.textAlign = 'center';
                    ctx.fillText(product, 85 + i * 80, 125);
                });
                
                // 绘制出版方式
                ctx.fillStyle = '#667eea';
                ctx.textAlign = 'center';
                ctx.fillText('出版方式', 150, 180);
                
                methods.forEach((method, i) => {
                    ctx.fillStyle = '#fd79a8';
                    ctx.fillRect(50 + i * 60, 200, 50, 40);
                    ctx.fillStyle = 'white';
                    ctx.textAlign = 'center';
                    ctx.fillText(method, 75 + i * 60, 225);
                });
                
                // 绘制连接线（表示组合爆炸）
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 1;
                products.forEach((_, i) => {
                    methods.forEach((_, j) => {
                        ctx.beginPath();
                        ctx.moveTo(85 + i * 80, 140);
                        ctx.lineTo(75 + j * 60, 200);
                        ctx.stroke();
                    });
                });
                
                // 右侧显示问题
                ctx.fillStyle = '#e17055';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'left';
                ctx.fillText('问题：', 450, 120);
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('• 需要 3×4=12 个类', 450, 150);
                ctx.fillText('• 新增产品类型需要4个新类', 450, 170);
                ctx.fillText('• 新增出版方式需要3个新类', 450, 190);
                ctx.fillText('• 类数量呈指数增长！', 450, 210);
                
                ctx.fillStyle = '#00b894';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.fillText('桥接模式解决方案：', 450, 250);
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('• 分离抽象和实现', 450, 280);
                ctx.fillText('• 独立变化，松耦合', 450, 300);
                ctx.fillText('• 只需要 3+4=7 个类', 450, 320);
            }
            
            drawProblemScene();
        }

        function animateProblem() {
            const canvas = document.getElementById('problemCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 重绘基础场景
                initProblemCanvas();

                // 添加闪烁效果突出问题
                if (Math.sin(frame * 0.2) > 0) {
                    ctx.fillStyle = 'rgba(225, 112, 85, 0.3)';
                    ctx.fillRect(440, 110, 300, 120);
                }

                frame++;
                if (frame < 60) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function initBridgeCanvas() {
            const canvas = document.getElementById('bridgeCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');

            function drawBridgePattern() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 背景
                ctx.fillStyle = '#f8f9fa';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2d3436';
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('桥接模式结构图', canvas.width/2, 40);

                // 抽象部分
                ctx.fillStyle = '#74b9ff';
                ctx.fillRect(50, 80, 200, 120);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('抽象部分', 150, 110);
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('宣传产品', 150, 130);
                ctx.fillText('• 宣传册', 150, 150);
                ctx.fillText('• 文章', 150, 170);
                ctx.fillText('• 传单', 150, 190);

                // 实现部分
                ctx.fillStyle = '#fd79a8';
                ctx.fillRect(550, 80, 200, 120);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('实现部分', 650, 110);
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('出版方式', 650, 130);
                ctx.fillText('• 纸质', 650, 150);
                ctx.fillText('• CD/DVD', 650, 170);
                ctx.fillText('• 在线发布', 650, 190);

                // 桥接线
                ctx.strokeStyle = '#00b894';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(250, 140);
                ctx.lineTo(550, 140);
                ctx.stroke();

                // 桥接标签
                ctx.fillStyle = '#00b894';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('🌉 桥接', 400, 135);

                // 优势说明
                ctx.fillStyle = '#2d3436';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('优势：两个维度可以独立扩展，避免类爆炸', 400, 250);

                // 示例代码框架
                ctx.fillStyle = '#ddd';
                ctx.fillRect(100, 280, 600, 180);
                ctx.fillStyle = '#333';
                ctx.font = '12px Consolas';
                ctx.textAlign = 'left';
                ctx.fillText('// 抽象产品类', 120, 300);
                ctx.fillText('class Product {', 120, 320);
                ctx.fillText('  constructor(publisher) {', 120, 340);
                ctx.fillText('    this.publisher = publisher;  // 桥接到实现', 120, 360);
                ctx.fillText('  }', 120, 380);
                ctx.fillText('  publish() { this.publisher.publish(); }', 120, 400);
                ctx.fillText('}', 120, 420);
                ctx.fillText('// 具体产品：Brochure, Article, Flyer...', 120, 440);
            }

            drawBridgePattern();
        }

        function animateBridge() {
            const canvas = document.getElementById('bridgeCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                initBridgeCanvas();

                // 添加动画效果
                const pulse = Math.sin(frame * 0.1) * 0.5 + 0.5;
                ctx.shadowColor = '#00b894';
                ctx.shadowBlur = 20 * pulse;
                ctx.strokeStyle = '#00b894';
                ctx.lineWidth = 4 + 2 * pulse;
                ctx.beginPath();
                ctx.moveTo(250, 140);
                ctx.lineTo(550, 140);
                ctx.stroke();
                ctx.shadowBlur = 0;

                frame++;
                if (frame < 100) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function initInteractiveCanvas() {
            const canvas = document.getElementById('interactiveCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            drawInteractiveScene();
        }

        function drawInteractiveScene() {
            const canvas = document.getElementById('interactiveCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('选择产品和出版方式，看看桥接模式如何工作', canvas.width/2, 40);

            // 绘制选中的产品
            if (selectedProduct) {
                const productNames = {
                    'brochure': '宣传册 📖',
                    'article': '文章 📄',
                    'flyer': '传单 📋'
                };

                ctx.fillStyle = '#74b9ff';
                ctx.fillRect(100, 100, 200, 80);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('选中的产品', 200, 125);
                ctx.fillText(productNames[selectedProduct], 200, 150);
            }

            // 绘制选中的方式
            if (selectedMethod) {
                const methodNames = {
                    'paper': '纸质出版 📰',
                    'cd': 'CD出版 💿',
                    'dvd': 'DVD出版 📀',
                    'online': '在线发布 🌐'
                };

                ctx.fillStyle = '#fd79a8';
                ctx.fillRect(500, 100, 200, 80);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('选中的方式', 600, 125);
                ctx.fillText(methodNames[selectedMethod], 600, 150);
            }

            // 如果两者都选中，显示桥接效果
            if (selectedProduct && selectedMethod) {
                // 桥接线
                ctx.strokeStyle = '#00b894';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(300, 140);
                ctx.lineTo(500, 140);
                ctx.stroke();

                // 结果展示
                ctx.fillStyle = '#00b894';
                ctx.fillRect(250, 220, 300, 120);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('桥接成功！', 400, 250);
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('产品和出版方式成功组合', 400, 275);
                ctx.fillText('无需创建新的组合类', 400, 295);
                ctx.fillText('体现了桥接模式的灵活性', 400, 315);

                // 添加动画效果
                const time = Date.now() * 0.005;
                const pulse = Math.sin(time) * 0.3 + 0.7;
                ctx.globalAlpha = pulse;
                ctx.fillStyle = '#00b894';
                ctx.fillText('✨ 松耦合设计 ✨', 400, 380);
                ctx.globalAlpha = 1;
            }
        }

        function selectProduct(product) {
            selectedProduct = product;
            drawInteractiveScene();
        }

        function selectMethod(method) {
            selectedMethod = method;
            drawInteractiveScene();
        }

        function initComparisonCanvas() {
            const canvas = document.getElementById('comparisonCanvas');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            drawComparisonScene();
        }

        function drawComparisonScene() {
            const canvas = document.getElementById('comparisonCanvas');
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 标题
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('四种设计模式对比', canvas.width/2, 40);

            const patterns = [
                { name: 'A. 外观模式', color: '#74b9ff', desc: '简化接口' },
                { name: 'B. 桥接模式', color: '#00b894', desc: '分离抽象实现' },
                { name: 'C. 装饰器模式', color: '#fdcb6e', desc: '动态添加功能' },
                { name: 'D. 适配器模式', color: '#e84393', desc: '接口转换' }
            ];

            patterns.forEach((pattern, i) => {
                const x = 50 + i * 180;
                const y = 80;

                ctx.fillStyle = pattern.color;
                ctx.fillRect(x, y, 150, 100);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(pattern.name, x + 75, y + 30);
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(pattern.desc, x + 75, y + 50);

                // 突出显示桥接模式
                if (i === 1) {
                    ctx.strokeStyle = '#00b894';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(x - 5, y - 5, 160, 110);
                    ctx.fillStyle = '#00b894';
                    ctx.font = 'bold 16px Microsoft YaHei';
                    ctx.fillText('✓ 正确答案', x + 75, y + 75);
                }
            });

            // 解释
            ctx.fillStyle = '#2d3436';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('本题场景：两个独立变化的维度需要灵活组合', 400, 220);
            ctx.fillText('桥接模式最适合解决这类问题', 400, 250);
        }

        function showPatternComparison() {
            const canvas = document.getElementById('comparisonCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                drawComparisonScene();

                // 添加闪烁效果突出正确答案
                if (Math.sin(frame * 0.3) > 0) {
                    ctx.strokeStyle = '#00b894';
                    ctx.lineWidth = 6;
                    ctx.strokeRect(225, 75, 160, 110);
                }

                frame++;
                if (frame < 60) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function selectAnswer(element, answer) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected');
            });

            // 标记当前选择
            element.classList.add('selected');
            selectedAnswer = answer;

            // 显示提交按钮
            document.getElementById('submitBtn').style.display = 'inline-block';
        }

        function submitAnswer() {
            const options = document.querySelectorAll('.quiz-option');
            const result = document.getElementById('result');

            options.forEach((opt, index) => {
                const letter = ['A', 'B', 'C', 'D'][index];
                if (letter === 'B') {
                    opt.classList.add('correct');
                } else if (letter === selectedAnswer && letter !== 'B') {
                    opt.classList.add('wrong');
                }
            });

            if (selectedAnswer === 'B') {
                result.innerHTML = `
                    <h3 style="color: #00b894;">🎉 恭喜你答对了！</h3>
                    <p><strong>桥接模式</strong>确实是最佳选择。在这个场景中：</p>
                    <ul style="margin-left: 20px;">
                        <li>宣传产品类型和出版方式是两个独立变化的维度</li>
                        <li>桥接模式将它们分离，避免了类爆炸问题</li>
                        <li>新增产品类型或出版方式都不会影响对方</li>
                        <li>体现了"开闭原则"：对扩展开放，对修改关闭</li>
                    </ul>
                `;
            } else {
                result.innerHTML = `
                    <h3 style="color: #e17055;">❌ 答案不正确</h3>
                    <p>正确答案是 <strong>B. 桥接模式</strong></p>
                    <p>你选择的模式虽然也有其应用场景，但不适合解决本题中的问题。建议重新学习桥接模式的特点。</p>
                `;
            }

            result.style.display = 'block';
            document.getElementById('submitBtn').style.display = 'none';
            document.getElementById('restartBtn').style.display = 'inline-block';
        }

        function restart() {
            currentSection = 1;
            selectedProduct = null;
            selectedMethod = null;
            selectedAnswer = null;

            // 重置所有section
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('visible');
            });

            // 重置quiz
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'wrong');
            });

            document.getElementById('result').style.display = 'none';
            document.getElementById('submitBtn').style.display = 'none';
            document.getElementById('restartBtn').style.display = 'none';

            // 显示第一个section
            document.getElementById('section1').classList.add('visible');
            updateProgress();

            // 滚动到顶部
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // 观察者模式：监听滚动事件，显示相应的section
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top < window.innerHeight * 0.8) {
                    section.classList.add('visible');
                }
            });
        });

        // 定期更新交互式canvas的动画
        setInterval(() => {
            if (selectedProduct && selectedMethod && currentSection === 4) {
                drawInteractiveScene();
            }
        }, 100);
    </script>
</body>
</html>
