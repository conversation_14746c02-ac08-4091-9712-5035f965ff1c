<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能机器人系统质量属性学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            max-width: 900px;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
        }
        .question-text {
            font-size: 1.1em;
            margin-bottom: 20px;
            background-color: #e8f0fe;
            padding: 15px;
            border-left: 5px solid #3498db;
            border-radius: 5px;
        }
        .options-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }
        .option-button {
            background-color: #ecf0f1;
            border: 1px solid #ccc;
            padding: 12px 20px;
            font-size: 1em;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }
        .option-button:hover {
            background-color: #dbe4e7;
            border-color: #999;
        }
        .option-button.correct {
            background-color: #e6ffe6;
            border-color: #28a745;
            color: #28a745;
            font-weight: bold;
        }
        .option-button.incorrect {
            background-color: #ffe6e6;
            border-color: #dc3545;
            color: #dc3545;
            font-weight: bold;
        }
        #feedback {
            text-align: center;
            font-size: 1.2em;
            margin-bottom: 20px;
            padding: 10px;
            border-radius: 5px;
            display: none; /* Hidden initially */
        }
        #feedback.correct-feedback {
            color: #28a745;
            background-color: #e6ffe6;
            border: 1px solid #28a745;
        }
        #feedback.incorrect-feedback {
            color: #dc3545;
            background-color: #ffe6e6;
            border: 1px solid #dc3545;
        }

        .explanation-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px dashed #ccc;
            display: none; /* Hidden initially */
        }
        .explanation-section h3 {
            color: #2980b9;
            text-align: center;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        .explanation-section p {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #5dade2;
            margin-bottom: 15px;
        }
        canvas {
            display: block;
            margin: 20px auto;
            border: 2px solid #ccc;
            background-color: #fdfdfd;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .play-button {
            display: block;
            margin: 15px auto;
            padding: 10px 25px;
            font-size: 1.1em;
            background-color: #2ecc71;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        .play-button:hover {
            background-color: #27ae60;
        }
        .quiz-title {
            color: #34495e;
            font-size: 1.8em;
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #777;
            text-align: center;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="quiz-title">智能机器人系统质量属性学习之旅</h1>

        <div class="question-container">
            <h2>问题2 [单选题]</h2>
            <p class="question-text">
                某公司欲开发一个智能机器人系统，在架构设计阶段，公司的架构师识别出3个核心质量属性场景。其中 “机器人系统主电源断电后，能够在10秒内自动启动备用电源并进行切换，恢复正常运行” 主要与 (/) 质量属性相关，通常可采用 (/) 架构策略实现该属性； “机器人在正常运动过程中如果发现前方2米内有人或者障碍物，应在1秒内停止并在2秒内选择一条新的运行路径” 主要与 (/) 质量属性相关，通常可采用 (/) 架构策略实现该属性； “对机器人的远程控制命令应该进行加密，从而能够抵挡恶意的入侵破坏行为，并对攻击进行报警和记录” 主要与 (/) 质量属性相关，通常可采用 (/) 架构策略实现该属性。
            </p>
        </div>

        <div class="options-container">
            <button class="option-button" data-option="A">A 抽象接口</button>
            <button class="option-button" data-option="B">B 信息隐藏</button>
            <button class="option-button" data-option="C">C 主动冗余</button>
            <button class="option-button" data-option="D">D 记录/回放</button>
        </div>

        <div id="feedback"></div>

        <div id="explanation-container" class="explanation-section">
            <h2>答案解析与互动演示</h2>

            <!-- 场景1：可用性与主动冗余 -->
            <h3>场景1：可用性 - 确保系统持续运行</h3>
            <p>
                <strong>知识点：可用性 (Availability)</strong> 是指系统在面对故障时，能够继续提供服务的能力。题目中“机器人系统主电源断电后，能够在10秒内自动启动备用电源并进行切换，恢复正常运行”就是典型的可用性场景，它强调系统在出现故障（主电源断电）时能快速恢复，不影响正常运作。
            </p>
            <p>
                <strong>策略：主动冗余 (Active Redundancy)</strong> 是一种实现高可用性的常见策略。这意味着系统在正常运行时就有多个功能相同的组件（如主电源和备用电源）同时工作或备用，当主组件发生故障时，备用组件能立即接管，从而实现无缝切换和快速恢复。这就像您家里有两路供电，一路断了，另一路立即顶上，保证电器正常运行。
            </p>
            <canvas id="scenario1-canvas" width="600" height="200"></canvas>
            <button class="play-button" onclick="animateScenario1()">播放可用性演示</button>

            <!-- 场景2：性能与资源调度 -->
            <h3>场景2：性能 - 响应速度与效率</h3>
            <p>
                <strong>知识点：性能 (Performance)</strong> 关注系统对事件的响应速度和处理能力。题目中“机器人在正常运动过程中如果发现前方2米内有人或者障碍物，应在1秒内停止并在2秒内选择一条新的运行路径”体现了对性能的要求：快速响应（1秒内停止）和高效决策（2秒内选择新路径），以确保机器人流畅、安全地运行。
            </p>
            <p>
                <strong>策略：资源调度 (Resource Scheduling)</strong> 是提升性能的关键策略。它涉及到合理分配和管理系统资源（如计算能力、传感器数据、路径规划算法），确保在紧急情况下能迅速完成任务。比如，机器人需要优先级地处理避障任务，并快速计算出最优的新路径，这些都依赖于有效的资源调度。
            </p>
            <canvas id="scenario2-canvas" width="600" height="200"></canvas>
            <button class="play-button" onclick="animateScenario2()">播放性能演示</button>

            <!-- 场景3：安全性与追踪审计 -->
            <h3>场景3：安全性 - 抵御威胁与保护数据</h3>
            <p>
                <strong>知识点：安全性 (Security)</strong> 是指系统保护信息和资源不受未经授权访问、使用、泄露、破坏或修改的能力。题目中“对机器人的远程控制命令应该进行加密，从而能够抵挡恶意的入侵破坏行为，并对攻击进行报警和记录”明确指向了安全性：防止非法控制、数据泄露和系统破坏。
            </p>
            <p>
                <strong>策略：追踪审计 (Trace Audit)</strong> 是保障安全性的重要手段。它通过记录所有关键操作（如远程控制命令、系统访问、异常行为），以便在发生安全事件时能够追踪攻击来源、分析攻击过程，并提供证据进行后续处理。加密则是在传输过程中保护数据不被窃取或篡改，是安全的第一道防线。
            </p>
            <canvas id="scenario3-canvas" width="600" height="200"></canvas>
            <button class="play-button" onclick="animateScenario3()">播放安全性演示</button>

        </div>
    </div>
    <footer>
        <p>&copy; 2023 智能学习助手. 版权所有.</p>
    </footer>

    <script>
        const correctOption = 'C';
        const optionButtons = document.querySelectorAll('.option-button');
        const feedbackDiv = document.getElementById('feedback');
        const explanationContainer = document.getElementById('explanation-container');

        optionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const selectedOption = button.dataset.option;
                if (selectedOption === correctOption) {
                    feedbackDiv.textContent = '太棒了！你答对了！🎉';
                    feedbackDiv.className = 'correct-feedback';
                    optionButtons.forEach(btn => {
                        if (btn.dataset.option === correctOption) {
                            btn.classList.add('correct');
                        }
                        btn.disabled = true; // Disable all buttons after correct answer
                    });
                } else {
                    feedbackDiv.textContent = '哎呀，选错了哦，再试试看！🤔';
                    feedbackDiv.className = 'incorrect-feedback';
                    button.classList.add('incorrect'); // Highlight incorrect
                    setTimeout(() => {
                        button.classList.remove('incorrect'); // Remove highlight after a short delay
                    }, 800);
                }
                feedbackDiv.style.display = 'block';
                explanationContainer.style.display = 'block'; // Always show explanation after first attempt
            });
        });

        // --- Canvas Animations ---

        // Scenario 1: Availability (Active Redundancy)
        const canvas1 = document.getElementById('scenario1-canvas');
        const ctx1 = canvas1.getContext('2d');

        function animateScenario1() {
            let frame = 0;
            const maxFrames = 200; // Total animation frames
            const mainPowerOffFrame = 50;
            const backupOnFrame = 100;
            const totalTime = 10000; // 10 seconds in ms
            let startTime = null;

            function drawScenario1() {
                ctx1.clearRect(0, 0, canvas1.width, canvas1.height);

                // Main power icon
                ctx1.font = '24px Arial';
                ctx1.fillText('🔌主电源', 50, 50);

                // Backup power icon
                ctx1.fillText('🔋备用电源', 50, 150);

                // Robot icon
                ctx1.fillText('🤖机器人', 400, 100);

                // Power lines
                ctx1.beginPath();
                ctx1.moveTo(150, 45);
                ctx1.lineTo(380, 90);
                ctx1.strokeStyle = (frame < mainPowerOffFrame) ? 'green' : 'lightgray'; // Main power color
                ctx1.lineWidth = 3;
                ctx1.stroke();

                ctx1.beginPath();
                ctx1.moveTo(150, 145);
                ctx1.lineTo(380, 110);
                ctx1.strokeStyle = (frame < backupOnFrame || frame < mainPowerOffFrame) ? 'lightgray' : 'blue'; // Backup power color
                ctx1.lineWidth = 3;
                ctx1.stroke();

                // Status text
                if (frame < mainPowerOffFrame) {
                    ctx1.fillStyle = 'green';
                    ctx1.fillText('⚡运行中', 480, 100);
                } else if (frame < backupOnFrame) {
                    ctx1.fillStyle = 'red';
                    ctx1.fillText('❌主电源故障!', 480, 100);
                    ctx1.fillText('⏳切换中...', 480, 130);
                } else {
                    ctx1.fillStyle = 'blue';
                    ctx1.fillText('✅备用电源已接管', 480, 100);
                    const elapsedTime = Math.min(10, Math.floor((frame - backupOnFrame) / (maxFrames - backupOnFrame) * 10)); // Simulate 10s recovery
                    ctx1.fillText(`恢复中: ${elapsedTime}s`, 480, 130);
                    if (elapsedTime >= 10) {
                        ctx1.fillStyle = 'black';
                        ctx1.fillText('✅系统恢复正常!', 480, 160);
                    }
                }
            }

            function animate(timestamp) {
                if (!startTime) startTime = timestamp;
                const elapsed = timestamp - startTime;

                frame = Math.min(maxFrames, Math.floor(elapsed / (totalTime / maxFrames)));

                drawScenario1();

                if (frame < maxFrames) {
                    requestAnimationFrame(animate);
                } else {
                    startTime = null; // Reset for next play
                }
            }
            requestAnimationFrame(animate);
        }


        // Scenario 2: Performance (Resource Scheduling)
        const canvas2 = document.getElementById('scenario2-canvas');
        const ctx2 = canvas2.getContext('2d');

        function animateScenario2() {
            let robotX = 50;
            let obstacleX = 400;
            let obstacleY = 100;
            let stopFrame = -1;
            let pathChooseFrame = -1;
            let animationStartTime = null;
            let newPathChosen = false;
            let targetX = 550;
            let targetY = 150;
            let currentPathColor = 'blue';

            function drawRobot(x, y, color) {
                ctx2.fillStyle = color;
                ctx2.beginPath();
                ctx2.arc(x, y, 15, 0, Math.PI * 2);
                ctx2.fill();
                ctx2.fillStyle = 'black';
                ctx2.font = '16px Arial';
                ctx2.fillText('🤖', x - 10, y + 5);
            }

            function drawObstacle(x, y) {
                ctx2.fillStyle = 'red';
                ctx2.fillRect(x - 15, y - 15, 30, 30);
                ctx2.fillStyle = 'white';
                ctx2.font = '16px Arial';
                ctx2.fillText('🚧', x - 10, y + 5);
            }

            function drawScenario2(timestamp) {
                if (!animationStartTime) animationStartTime = timestamp;
                const elapsedTime = timestamp - animationStartTime;

                ctx2.clearRect(0, 0, canvas2.width, canvas2.height);

                // Draw ground
                ctx2.fillStyle = '#eee';
                ctx2.fillRect(0, canvas2.height - 30, canvas2.width, 30);

                // Initial path
                ctx2.strokeStyle = 'lightgray';
                ctx2.lineWidth = 2;
                ctx2.beginPath();
                ctx2.moveTo(0, 100);
                ctx2.lineTo(canvas2.width, 100);
                ctx2.stroke();

                // Robot movement
                if (!newPathChosen) {
                    if (robotX < obstacleX - 50) { // Robot approaches obstacle
                        robotX += 1;
                    } else if (stopFrame === -1) { // Robot stops within 1 second
                        stopFrame = elapsedTime;
                        currentPathColor = 'red'; // Indicate stop
                        ctx2.fillStyle = 'red';
                        ctx2.fillText('🔴 1秒内停止!', robotX + 30, 70);
                    } else if (elapsedTime - stopFrame < 2000) { // Choose new path within 2 seconds
                        ctx2.fillStyle = 'orange';
                        ctx2.fillText('⏳ 2秒内选择新路径...', robotX + 30, 70);
                        // Simulate path calculation
                        if (!pathChooseFrame) pathChooseFrame = elapsedTime;
                        currentPathColor = 'orange';
                    } else { // New path chosen, resume
                        newPathChosen = true;
                        currentPathColor = 'green';
                        ctx2.fillStyle = 'green';
                        ctx2.fillText('✅ 新路径已选择!', robotX + 30, 70);
                    }
                } else {
                    // Move towards new target
                    if (robotX < targetX) robotX += 1;
                    if (robotX >= targetX && robotY < targetY) robotY += 1;
                }

                drawRobot(robotX, 100);
                drawObstacle(obstacleX, obstacleY);

                // New path visualization
                if (newPathChosen) {
                    ctx2.strokeStyle = 'green';
                    ctx2.lineWidth = 2;
                    ctx2.beginPath();
                    ctx2.moveTo(obstacleX, 100);
                    ctx2.lineTo(targetX, targetY);
                    ctx2.stroke();
                    drawRobot(robotX, robotY, 'green'); // Robot on new path
                } else {
                    drawRobot(robotX, 100, currentPathColor);
                }


                if (robotX < canvas2.width) {
                    requestAnimationFrame(drawScenario2);
                } else {
                    animationStartTime = null; // Reset for next play
                }
            }
            requestAnimationFrame(drawScenario2);
        }

        // Scenario 3: Security (Trace Audit)
        const canvas3 = document.getElementById('scenario3-canvas');
        const ctx3 = canvas3.getContext('2d');

        function animateScenario3() {
            let frame = 0;
            const maxFrames = 250;
            const encryptFrame = 30;
            const sendFrame = 60;
            const attackFrame = 120;
            const alarmFrame = 180;
            const totalTime = 12000; // 12 seconds
            let startTime = null;

            function drawScenario3() {
                ctx3.clearRect(0, 0, canvas3.width, canvas3.height);

                // Controller
                ctx3.font = '24px Arial';
                ctx3.fillText('👨‍💻 控制器', 50, 50);

                // Robot
                ctx3.fillText('🤖 机器人', 500, 50);

                // Attacker
                ctx3.fillText('🕵️‍♂️ 攻击者', 250, 150);


                // Command text
                let commandText = '指令: 移动10步';
                let encryptedText = 'XyZ7#@AbC9!';

                // Step 1: Command preparation
                if (frame < encryptFrame) {
                    ctx3.fillStyle = 'black';
                    ctx3.fillText(commandText, 180, 80);
                    ctx3.fillText('准备指令...', 180, 110);
                }
                // Step 2: Encryption
                else if (frame < sendFrame) {
                    ctx3.fillStyle = 'purple';
                    ctx3.fillText(encryptedText, 180, 80);
                    ctx3.fillText('加密中...', 180, 110);
                }
                // Step 3: Sending command
                else if (frame < attackFrame) {
                    ctx3.fillStyle = 'blue';
                    ctx3.fillText(encryptedText, 180 + (frame - sendFrame) * 2, 80);
                    ctx3.fillText('传输中...', 180, 110);
                }
                // Step 4: Attacker tries to intercept
                else if (frame < alarmFrame) {
                    ctx3.fillStyle = 'red';
                    ctx3.fillText(encryptedText, 180 + (sendFrame - sendFrame) * 2, 80); // Keep it at start
                    ctx3.fillText('⚠️攻击者尝试拦截!', 250, 180);
                    ctx3.fillText('🔐加密保护!', 250, 20); // Show encryption working
                    ctx3.fillText('🚨报警!', 50, 180);
                    ctx3.fillText('📜记录日志!', 500, 180);
                }
                // Step 5: Command delivered, audit completed
                else {
                    ctx3.fillStyle = 'green';
                    ctx3.fillText('✅指令安全送达!', 300, 80);
                    ctx3.fillText('✅审计完成!', 300, 110);
                }

                // Draw arrows
                ctx3.strokeStyle = 'lightgray';
                ctx3.lineWidth = 2;
                ctx3.beginPath();
                ctx3.moveTo(150, 50);
                ctx3.lineTo(480, 50);
                ctx3.stroke();
                ctx3.fillStyle = 'lightgray';
                ctx3.beginPath();
                ctx3.lineTo(480, 50);
                ctx3.lineTo(470, 45);
                ctx3.lineTo(470, 55);
                ctx3.fill();


            }

            function animate(timestamp) {
                if (!startTime) startTime = timestamp;
                const elapsed = timestamp - startTime;
                frame = Math.min(maxFrames, Math.floor(elapsed / (totalTime / maxFrames)));

                drawScenario3();

                if (frame < maxFrames) {
                    requestAnimationFrame(animate);
                } else {
                    startTime = null; // Reset for next play
                }
            }
            requestAnimationFrame(animate);
        }
    </script>
</body>
</html> 