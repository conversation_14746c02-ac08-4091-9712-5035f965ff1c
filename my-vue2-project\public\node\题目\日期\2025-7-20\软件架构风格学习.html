<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构风格 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 1s ease-out;
        }

        .question-box {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            color: #333;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 20px;
        }

        .blank {
            display: inline-block;
            min-width: 120px;
            height: 40px;
            background: rgba(255,255,255,0.8);
            border: 2px dashed #ff6b6b;
            border-radius: 8px;
            margin: 0 5px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .blank:hover {
            background: rgba(255,255,255,1);
            transform: scale(1.05);
        }

        .options-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 30px 0;
        }

        .option {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            user-select: none;
        }

        .option:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(116,185,255,0.4);
        }

        .option.selected {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            transform: scale(1.1);
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        #architectureCanvas {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            background: white;
        }

        .explanation {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            display: none;
        }

        .explanation.show {
            display: block;
            animation: slideIn 0.5s ease-out;
        }

        .check-btn {
            background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
        }

        .check-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(232,67,147,0.4);
        }

        .result {
            text-align: center;
            font-size: 1.2rem;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .result.correct {
            background: rgba(0,184,148,0.2);
            color: #00b894;
            border: 2px solid #00b894;
        }

        .result.incorrect {
            background: rgba(255,107,107,0.2);
            color: #ff6b6b;
            border: 2px solid #ff6b6b;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .architecture-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }

        .arch-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .arch-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .arch-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .knowledge-point {
            background: rgba(116,185,255,0.1);
            border-left: 4px solid #74b9ff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🏗️ 软件架构风格</h1>
            <p class="subtitle">通过互动学习理解软件架构的奥秘</p>
        </div>

        <div class="section">
            <div class="question-box">
                <div class="question-text">
                    软件架构风格描述某一特定领域中的系统组织方式和惯用模式，反映了领域中众多系统所共有的
                    <span class="blank" data-answer="结构和语义" id="blank1"></span>
                    特征。对于语音识别、知识推理等问题复杂、解空间很大、求解过程不确定的这一类软件系统，通常会采用
                    <span class="blank" data-answer="黑板架构" id="blank2"></span>
                    架构风格。
                </div>
                
                <div class="options-container">
                    <div class="option" data-target="blank1" data-value="语法和语义">语法和语义</div>
                    <div class="option" data-target="blank1" data-value="结构和语义">结构和语义</div>
                    <div class="option" data-target="blank1" data-value="静态和动态">静态和动态</div>
                    <div class="option" data-target="blank1" data-value="行为和约束">行为和约束</div>
                    <div class="option" data-target="blank2" data-value="分层架构">分层架构</div>
                    <div class="option" data-target="blank2" data-value="黑板架构">黑板架构</div>
                    <div class="option" data-target="blank2" data-value="管道过滤器">管道过滤器</div>
                    <div class="option" data-target="blank2" data-value="客户端服务器">客户端服务器</div>
                </div>

                <button class="check-btn" onclick="checkAnswers()">检查答案</button>
                <button class="check-btn" onclick="showExplanation()">查看解析</button>
                
                <div class="result" id="result"></div>
            </div>

            <div class="canvas-container">
                <canvas id="architectureCanvas" width="800" height="400"></canvas>
            </div>

            <div class="explanation" id="explanation">
                <h3>📚 知识解析</h3>
                <div class="knowledge-point">
                    <h4>🎯 第一题解析：结构和语义</h4>
                    <p><strong>结构特征：</strong>描述系统组件之间的组织关系和连接方式</p>
                    <p><strong>语义特征：</strong>描述组件的行为规则和交互约束</p>
                </div>
                <div class="knowledge-point">
                    <h4>🎯 第二题解析：黑板架构</h4>
                    <p><strong>适用场景：</strong>问题复杂、解空间大、求解过程不确定</p>
                    <p><strong>典型应用：</strong>语音识别、知识推理、专家系统</p>
                    <p><strong>核心思想：</strong>以知识为中心，多个专家协同解决问题</p>
                </div>
            </div>

            <div class="architecture-demo">
                <div class="arch-card" onclick="showArchitecture('layered')">
                    <div class="arch-icon">🏢</div>
                    <h3>分层架构</h3>
                    <p>层次分明，职责清晰</p>
                </div>
                <div class="arch-card" onclick="showArchitecture('blackboard')">
                    <div class="arch-icon">🧠</div>
                    <h3>黑板架构</h3>
                    <p>知识驱动，协同推理</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAnswers = {};
        let canvas = document.getElementById('architectureCanvas');
        let ctx = canvas.getContext('2d');
        let animationFrame = 0;

        // 初始化画布动画
        function initCanvas() {
            drawWelcomeAnimation();
        }

        function drawWelcomeAnimation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景渐变
            let gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制浮动的架构图标
            ctx.font = '40px Arial';
            ctx.textAlign = 'center';
            
            let icons = ['🏗️', '🧠', '⚙️', '🔧', '📐'];
            icons.forEach((icon, index) => {
                let x = 150 + index * 130;
                let y = 200 + Math.sin(animationFrame * 0.02 + index) * 20;
                ctx.fillStyle = 'rgba(255,255,255,0.8)';
                ctx.fillText(icon, x, y);
            });
            
            // 绘制标题
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = 'white';
            ctx.fillText('点击下方卡片探索不同架构风格', canvas.width/2, 100);
            
            animationFrame++;
            requestAnimationFrame(drawWelcomeAnimation);
        }

        // 选项点击处理
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                let target = this.dataset.target;
                let value = this.dataset.value;
                
                // 移除同组其他选项的选中状态
                document.querySelectorAll(`[data-target="${target}"]`).forEach(opt => {
                    opt.classList.remove('selected');
                });
                
                // 添加当前选项的选中状态
                this.classList.add('selected');
                
                // 更新空白处显示
                let blank = document.getElementById(target);
                blank.textContent = value;
                blank.style.background = 'rgba(116,185,255,0.3)';
                blank.style.border = '2px solid #74b9ff';
                
                // 保存答案
                selectedAnswers[target] = value;
                
                // 添加动画效果
                blank.classList.add('pulse');
                setTimeout(() => blank.classList.remove('pulse'), 1000);
            });
        });

        function checkAnswers() {
            let correct = 0;
            let total = 2;
            
            document.querySelectorAll('.blank').forEach(blank => {
                let correctAnswer = blank.dataset.answer;
                let userAnswer = selectedAnswers[blank.id];
                
                if (userAnswer === correctAnswer) {
                    blank.style.background = 'rgba(0,184,148,0.3)';
                    blank.style.border = '2px solid #00b894';
                    correct++;
                } else {
                    blank.style.background = 'rgba(255,107,107,0.3)';
                    blank.style.border = '2px solid #ff6b6b';
                }
            });
            
            let result = document.getElementById('result');
            result.style.display = 'block';
            
            if (correct === total) {
                result.className = 'result correct';
                result.innerHTML = '🎉 恭喜！全部答对了！<br>您已经掌握了软件架构风格的基本概念！';
                showCelebration();
            } else {
                result.className = 'result incorrect';
                result.innerHTML = `😊 答对了 ${correct}/${total} 题<br>继续加油，点击"查看解析"学习更多！`;
            }
        }

        function showExplanation() {
            let explanation = document.getElementById('explanation');
            explanation.classList.add('show');
            explanation.scrollIntoView({ behavior: 'smooth' });
        }

        function showCelebration() {
            // 在画布上显示庆祝动画
            let celebrationCount = 0;
            function celebrate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 背景
                let gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#00b894');
                gradient.addColorStop(1, '#00a085');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 庆祝文字
                ctx.font = 'bold 36px Microsoft YaHei';
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.fillText('🎉 恭喜通过！🎉', canvas.width/2, canvas.height/2);
                
                // 飘落的星星
                for (let i = 0; i < 20; i++) {
                    let x = Math.random() * canvas.width;
                    let y = (celebrationCount * 2 + i * 20) % canvas.height;
                    ctx.font = '20px Arial';
                    ctx.fillText('⭐', x, y);
                }
                
                celebrationCount++;
                if (celebrationCount < 100) {
                    requestAnimationFrame(celebrate);
                } else {
                    initCanvas();
                }
            }
            celebrate();
        }

        function showArchitecture(type) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (type === 'layered') {
                drawLayeredArchitecture();
            } else if (type === 'blackboard') {
                drawBlackboardArchitecture();
            }
        }

        function drawLayeredArchitecture() {
            // 绘制分层架构
            let layers = ['表示层', '业务层', '数据层'];
            let colors = ['#ff7675', '#74b9ff', '#00b894'];
            
            layers.forEach((layer, index) => {
                let y = 80 + index * 80;
                ctx.fillStyle = colors[index];
                ctx.fillRect(200, y, 400, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(layer, 400, y + 35);
                
                // 绘制箭头
                if (index < layers.length - 1) {
                    ctx.strokeStyle = '#2d3436';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(400, y + 60);
                    ctx.lineTo(400, y + 80);
                    ctx.moveTo(390, y + 70);
                    ctx.lineTo(400, y + 80);
                    ctx.lineTo(410, y + 70);
                    ctx.stroke();
                }
            });
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillText('分层架构：层次分明，单向依赖', 400, 350);
        }

        function drawBlackboardArchitecture() {
            // 绘制黑板架构
            ctx.fillStyle = '#2d3436';
            ctx.fillRect(300, 100, 200, 150);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('黑板', 400, 130);
            ctx.fillText('(共享数据)', 400, 155);
            
            // 绘制专家系统
            let experts = ['语音专家', '语法专家', '语义专家'];
            let positions = [
                {x: 150, y: 175},
                {x: 650, y: 175},
                {x: 400, y: 320}
            ];
            
            experts.forEach((expert, index) => {
                let pos = positions[index];
                ctx.fillStyle = '#74b9ff';
                ctx.fillRect(pos.x - 50, pos.y - 20, 100, 40);
                
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(expert, pos.x, pos.y + 5);
                
                // 绘制连接线
                ctx.strokeStyle = '#fd79a8';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(pos.x, pos.y);
                ctx.lineTo(400, 175);
                ctx.stroke();
            });
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 16px Microsoft YaHei';
            ctx.fillText('黑板架构：多专家协同，知识驱动', 400, 380);
        }

        // 初始化
        window.onload = function() {
            initCanvas();
        };
    </script>
</body>
</html>
