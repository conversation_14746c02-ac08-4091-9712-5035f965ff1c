<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transform - 词缀动画故事学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .pronunciation {
            font-size: 1.2em;
            color: #ffd700;
            margin-bottom: 15px;
        }

        .main-content {
            display: flex;
            gap: 20px;
            flex: 1;
        }

        .word-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .animation-panel {
            flex: 1.5;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .word-details {
            margin-bottom: 25px;
        }

        .word-details h2 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .meaning-box {
            background: #f8f9ff;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            margin-bottom: 15px;
        }

        .example-box {
            background: #fff5f5;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #ff6b6b;
        }

        .morpheme-section {
            margin-bottom: 25px;
        }

        .morpheme-section h3 {
            color: #764ba2;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .morpheme-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .morpheme-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .morpheme-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .morpheme-btn.active {
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        #story-canvas {
            width: 100%;
            height: 400px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            background: #f0f8ff;
        }

        .story-text {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-buttons {
            margin-top: 20px;
            display: flex;
            gap: 15px;
        }

        .control-btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .control-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .highlight {
            color: #ff6b6b;
            font-weight: bold;
        }

        .morpheme-highlight {
            color: #667eea;
            font-weight: bold;
            font-size: 1.1em;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .main-content {
                flex-direction: column;
            }

            .header h1 {
                font-size: 2em;
            }

            .word-panel, .animation-panel {
                padding: 15px;
            }

            #story-canvas {
                height: 300px;
            }

            .morpheme-buttons {
                justify-content: center;
            }

            .control-buttons {
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8em;
            }

            .morpheme-btn {
                padding: 10px 15px;
                font-size: 12px;
            }

            .control-btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            #story-canvas {
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>transform</h1>
            <div class="pronunciation">[trænsˈfɔːrm]</div>
        </div>

        <div class="main-content">
            <div class="word-panel">
                <div class="word-details">
                    <h2>单词详解</h2>
                    <div class="meaning-box">
                        <strong>词性：</strong>动词 (v.) / 名词 (n.)<br>
                        <strong>含义：</strong>转变，改变，变换<br>
                        <strong>词根分析：</strong><span class="morpheme-highlight">trans-</span> (跨越) + <span class="morpheme-highlight">-form</span> (形状)
                    </div>
                    <div class="example-box">
                        <strong>例句：</strong>Technology can transform our lives completely.<br>
                        <strong>翻译：</strong>科技能够彻底改变我们的生活。
                    </div>
                </div>

                <div class="morpheme-section">
                    <h3>词缀故事动画</h3>
                    <div class="morpheme-buttons">
                        <button class="morpheme-btn" data-story="trans">trans- (跨越)</button>
                        <button class="morpheme-btn" data-story="form">-form (形状)</button>
                        <button class="morpheme-btn" data-story="complete">完整故事</button>
                    </div>
                    <p style="color: #666; font-size: 14px; text-align: center;">
                        点击上方按钮，观看词缀的动画故事演示！
                    </p>
                </div>
            </div>

            <div class="animation-panel">
                <canvas id="story-canvas"></canvas>
                <div class="story-text" id="story-text">
                    欢迎来到 transform 的词缀世界！点击左侧按钮开始你的学习之旅。
                </div>
                <div class="control-buttons">
                    <button class="control-btn" id="play-btn">播放动画</button>
                    <button class="control-btn" id="reset-btn">重新开始</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('story-canvas');
        const ctx = canvas.getContext('2d');
        const storyText = document.getElementById('story-text');
        const playBtn = document.getElementById('play-btn');
        const resetBtn = document.getElementById('reset-btn');

        let currentStory = null;
        let animationId = null;
        let animationStep = 0;
        let isPlaying = false;

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
        }

        // 初始化
        window.addEventListener('load', () => {
            resizeCanvas();
            drawWelcomeScreen();
        });

        window.addEventListener('resize', resizeCanvas);

        // 故事数据
        const stories = {
            trans: {
                title: "trans- 的故事：跨越的魔法",
                steps: [
                    {
                        text: "很久很久以前，有一个神奇的前缀叫做 <span class='highlight'>trans-</span>，它的意思是<span class='morpheme-highlight'>跨越、穿过、转移</span>。想象一座神奇的桥梁...",
                        animate: drawTransBridge
                    },
                    {
                        text: "<span class='highlight'>trans-</span> 就像一个魔法师，能让事物从一边<span class='morpheme-highlight'>跨越</span>到另一边，发生神奇的变化！",
                        animate: drawTransMagic
                    },
                    {
                        text: "无论是<span class='highlight'>transport</span>(运输)、<span class='highlight'>translate</span>(翻译)，还是<span class='highlight'>transform</span>(转变)，都有这种跨越的力量！",
                        animate: drawTransExamples
                    }
                ]
            },
            form: {
                title: "-form 的故事：形状的艺术家",
                steps: [
                    {
                        text: "在词汇王国里，住着一位神奇的艺术家，名叫 <span class='highlight'>-form</span>，它的专长是<span class='morpheme-highlight'>塑造形状</span>！",
                        animate: drawFormArtist
                    },
                    {
                        text: "<span class='highlight'>-form</span> 拥有神奇的画笔，能够创造各种形状：圆形、方形、三角形...",
                        animate: drawFormShapes
                    },
                    {
                        text: "当 <span class='highlight'>-form</span> 遇到其他词缀时，就能创造出新的含义：<span class='highlight'>inform</span>(告知)、<span class='highlight'>perform</span>(表演)！",
                        animate: drawFormCombination
                    }
                ]
            },
            complete: {
                title: "transform 的完整故事：跨越形状的奇迹",
                steps: [
                    {
                        text: "当 <span class='highlight'>trans-</span>(跨越) 遇到 <span class='highlight'>-form</span>(形状)时，会发生什么呢？",
                        animate: drawMeeting
                    },
                    {
                        text: "它们携手合作，创造了 <span class='highlight'>transform</span> —— 让形状<span class='morpheme-highlight'>跨越</span>原有的界限，变成全新的样子！",
                        animate: drawTransformation
                    },
                    {
                        text: "就像毛毛虫<span class='highlight'>transform</span>成蝴蝶，冰块<span class='highlight'>transform</span>成水，这就是转变的魔法！",
                        animate: drawRealExamples
                    },
                    {
                        text: "现在你明白了吗？<span class='highlight'>transform</span> = <span class='morpheme-highlight'>跨越(trans-) + 形状(-form)</span> = 转变！",
                        animate: drawFinalUnderstanding
                    }
                ]
            }
        };

        // 欢迎屏幕
        function drawWelcomeScreen() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#e3f2fd');
            gradient.addColorStop(1, '#f3e5f5');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制欢迎文字
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Transform 词缀动画学习', canvas.width / 2, canvas.height / 2 - 40);

            ctx.fillStyle = '#764ba2';
            ctx.font = '18px Arial';
            ctx.fillText('点击左侧按钮开始学习', canvas.width / 2, canvas.height / 2 + 10);

            // 绘制装饰性图标
            drawWelcomeIcon();
        }

        function drawWelcomeIcon() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2 + 60;

            // 绘制变换箭头
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX - 50, centerY);
            ctx.lineTo(centerX + 50, centerY);
            ctx.stroke();

            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(centerX + 40, centerY - 10);
            ctx.lineTo(centerX + 50, centerY);
            ctx.lineTo(centerX + 40, centerY + 10);
            ctx.stroke();

            // 装饰圆圈
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(centerX - 50, centerY, 8, 0, Math.PI * 2);
            ctx.fill();

            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(centerX + 50, centerY, 8, 0, Math.PI * 2);
            ctx.fill();
        }

        // trans- 动画函数
        function drawTransBridge() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#87CEEB');
            gradient.addColorStop(1, '#98FB98');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.002;
            const centerY = canvas.height / 2;

            // 绘制河流
            ctx.fillStyle = '#4169E1';
            ctx.fillRect(0, centerY + 50, canvas.width, 100);

            // 绘制桥梁
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(50, centerY + 50);
            ctx.quadraticCurveTo(canvas.width / 2, centerY - 30, canvas.width - 50, centerY + 50);
            ctx.stroke();

            // 绘制移动的小球（表示跨越）
            const ballX = 50 + (canvas.width - 100) * (0.5 + 0.4 * Math.sin(time));
            const ballY = centerY + 50 - 30 * Math.sin(Math.PI * (ballX - 50) / (canvas.width - 100));

            ctx.fillStyle = '#FF6347';
            ctx.beginPath();
            ctx.arc(ballX, ballY, 12, 0, Math.PI * 2);
            ctx.fill();

            // 绘制 "trans-" 文字
            ctx.fillStyle = '#2F4F4F';
            ctx.font = 'bold 28px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('trans-', canvas.width / 2, 40);
            ctx.font = '16px Arial';
            ctx.fillText('跨越、穿过', canvas.width / 2, 65);
        }

        function drawTransMagic() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createRadialGradient(canvas.width/2, canvas.height/2, 0, canvas.width/2, canvas.height/2, canvas.width/2);
            gradient.addColorStop(0, '#E6E6FA');
            gradient.addColorStop(1, '#DDA0DD');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.003;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制魔法师
            ctx.fillStyle = '#4B0082';
            ctx.fillRect(centerX - 15, centerY - 20, 30, 60);

            // 魔法师帽子
            ctx.beginPath();
            ctx.moveTo(centerX - 20, centerY - 20);
            ctx.lineTo(centerX, centerY - 50);
            ctx.lineTo(centerX + 20, centerY - 20);
            ctx.closePath();
            ctx.fill();

            // 魔法棒
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(centerX + 20, centerY - 10);
            ctx.lineTo(centerX + 50, centerY - 30);
            ctx.stroke();

            // 魔法星星
            for (let i = 0; i < 5; i++) {
                const angle = time + i * Math.PI * 2 / 5;
                const x = centerX + 70 + 30 * Math.cos(angle);
                const y = centerY - 20 + 20 * Math.sin(angle);
                drawStar(x, y, 8, '#FFD700');
            }

            // trans- 文字
            ctx.fillStyle = '#4B0082';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('trans- 魔法师', centerX, centerY + 80);
        }

        function drawTransExamples() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            ctx.fillStyle = '#F0F8FF';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.002;

            // 绘制三个例子
            const examples = [
                { word: 'transport', x: canvas.width * 0.2, icon: '🚛' },
                { word: 'translate', x: canvas.width * 0.5, icon: '📖' },
                { word: 'transform', x: canvas.width * 0.8, icon: '🦋' }
            ];

            examples.forEach((example, index) => {
                const y = canvas.height / 2 + 20 * Math.sin(time + index);

                // 绘制图标
                ctx.font = '40px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(example.icon, example.x, y - 20);

                // 绘制单词
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 16px Arial';
                ctx.fillText(example.word, example.x, y + 20);

                // 高亮 trans-
                ctx.fillStyle = '#ff6b6b';
                ctx.font = 'bold 18px Arial';
                ctx.fillText('trans-', example.x, y + 40);
            });
        }

        // -form 动画函数
        function drawFormArtist() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#FFF8DC');
            gradient.addColorStop(1, '#F5DEB3');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制艺术家
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(centerX - 20, centerY - 10, 40, 50);

            // 艺术家头部
            ctx.fillStyle = '#FDBCB4';
            ctx.beginPath();
            ctx.arc(centerX, centerY - 25, 15, 0, Math.PI * 2);
            ctx.fill();

            // 画笔
            ctx.strokeStyle = '#8B4513';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(centerX + 25, centerY - 5);
            ctx.lineTo(centerX + 45, centerY - 15);
            ctx.stroke();

            // 调色板
            ctx.fillStyle = '#D2B48C';
            ctx.beginPath();
            ctx.arc(centerX - 40, centerY, 20, 0, Math.PI * 2);
            ctx.fill();

            // 颜料点
            const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
            colors.forEach((color, index) => {
                ctx.fillStyle = color;
                ctx.beginPath();
                const angle = index * Math.PI / 2;
                const x = centerX - 40 + 12 * Math.cos(angle);
                const y = centerY + 12 * Math.sin(angle);
                ctx.arc(x, y, 4, 0, Math.PI * 2);
                ctx.fill();
            });

            // -form 文字
            ctx.fillStyle = '#8B4513';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('-form 艺术家', centerX, centerY + 80);
        }

        function drawFormShapes() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            ctx.fillStyle = '#F0FFFF';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.003;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制各种形状
            const shapes = [
                { type: 'circle', x: centerX - 80, y: centerY - 40, color: '#FF6B6B' },
                { type: 'square', x: centerX, y: centerY - 40, color: '#4ECDC4' },
                { type: 'triangle', x: centerX + 80, y: centerY - 40, color: '#45B7D1' }
            ];

            shapes.forEach((shape, index) => {
                const scale = 1 + 0.2 * Math.sin(time + index);
                ctx.save();
                ctx.translate(shape.x, shape.y);
                ctx.scale(scale, scale);
                ctx.fillStyle = shape.color;

                switch (shape.type) {
                    case 'circle':
                        ctx.beginPath();
                        ctx.arc(0, 0, 25, 0, Math.PI * 2);
                        ctx.fill();
                        break;
                    case 'square':
                        ctx.fillRect(-25, -25, 50, 50);
                        break;
                    case 'triangle':
                        ctx.beginPath();
                        ctx.moveTo(0, -25);
                        ctx.lineTo(-25, 25);
                        ctx.lineTo(25, 25);
                        ctx.closePath();
                        ctx.fill();
                        break;
                }
                ctx.restore();
            });

            // -form 文字
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('-form 创造各种形状', centerX, centerY + 80);
        }

        function drawFormCombination() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            ctx.fillStyle = '#FFFACD';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.002;
            const centerY = canvas.height / 2;

            // 绘制组合示例
            const combinations = [
                { prefix: 'in', word: 'inform', x: canvas.width * 0.3, meaning: '告知' },
                { prefix: 'per', word: 'perform', x: canvas.width * 0.7, meaning: '表演' }
            ];

            combinations.forEach((combo, index) => {
                const y = centerY + 15 * Math.sin(time + index);

                // 前缀
                ctx.fillStyle = '#667eea';
                ctx.font = 'bold 18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(combo.prefix + '-', combo.x - 30, y - 10);

                // +号
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.fillText('+', combo.x, y - 10);

                // -form
                ctx.fillStyle = '#ff6b6b';
                ctx.font = 'bold 18px Arial';
                ctx.fillText('-form', combo.x + 30, y - 10);

                // =号
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.fillText('=', combo.x, y + 15);

                // 完整单词
                ctx.fillStyle = '#4CAF50';
                ctx.font = 'bold 20px Arial';
                ctx.fillText(combo.word, combo.x, y + 40);

                // 中文含义
                ctx.fillStyle = '#666';
                ctx.font = '14px Arial';
                ctx.fillText(combo.meaning, combo.x, y + 60);
            });
        }

        // 完整故事动画函数
        function drawMeeting() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createRadialGradient(canvas.width/2, canvas.height/2, 0, canvas.width/2, canvas.height/2, canvas.width/2);
            gradient.addColorStop(0, '#FFE4E1');
            gradient.addColorStop(1, '#FFC0CB');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.003;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // trans- 角色（左侧）
            const transX = centerX - 80 + 10 * Math.sin(time);
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 32px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('trans-', transX, centerY);

            // 绘制移动轨迹
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(transX - 50, centerY + 20);
            ctx.lineTo(transX + 50, centerY + 20);
            ctx.stroke();
            ctx.setLineDash([]);

            // -form 角色（右侧）
            const formX = centerX + 80 - 10 * Math.sin(time);
            ctx.fillStyle = '#ff6b6b';
            ctx.font = 'bold 32px Arial';
            ctx.fillText('-form', formX, centerY);

            // 绘制形状轨迹
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(formX - 50, centerY + 20);
            ctx.lineTo(formX + 50, centerY + 20);
            ctx.stroke();
            ctx.setLineDash([]);

            // 中间的心形连接
            if (Math.abs(transX - formX) < 100) {
                drawHeart(centerX, centerY - 40, '#FFB6C1');
            }

            // 说明文字
            ctx.fillStyle = '#333';
            ctx.font = '18px Arial';
            ctx.fillText('两个词缀即将相遇...', centerX, centerY + 60);
        }

        function drawTransformation() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#E0E6FF');
            gradient.addColorStop(0.5, '#FFE0E6');
            gradient.addColorStop(1, '#E6FFE0');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.004;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制合并过程
            const mergeProgress = (Math.sin(time) + 1) / 2;

            // trans- 部分
            ctx.fillStyle = `rgba(102, 126, 234, ${1 - mergeProgress * 0.5})`;
            ctx.font = 'bold 28px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('trans-', centerX - 60 + mergeProgress * 30, centerY - 20);

            // -form 部分
            ctx.fillStyle = `rgba(255, 107, 107, ${1 - mergeProgress * 0.5})`;
            ctx.fillText('-form', centerX + 60 - mergeProgress * 30, centerY - 20);

            // 合并后的单词
            ctx.fillStyle = `rgba(76, 175, 80, ${mergeProgress})`;
            ctx.font = 'bold 36px Arial';
            ctx.fillText('transform', centerX, centerY + 20);

            // 魔法效果
            for (let i = 0; i < 8; i++) {
                const angle = time + i * Math.PI / 4;
                const x = centerX + 80 * Math.cos(angle);
                const y = centerY + 50 * Math.sin(angle);
                drawStar(x, y, 6, '#FFD700');
            }

            // 说明文字
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.fillText('跨越 + 形状 = 转变！', centerX, centerY + 80);
        }

        function drawRealExamples() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            ctx.fillStyle = '#F0F8FF';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.002;

            // 毛毛虫变蝴蝶
            const leftX = canvas.width * 0.25;
            const leftY = canvas.height * 0.4;

            ctx.fillStyle = '#8FBC8F';
            ctx.font = '40px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🐛', leftX, leftY);

            // 变换箭头
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(leftX + 30, leftY - 10);
            ctx.lineTo(leftX + 80, leftY - 10);
            ctx.stroke();

            // 箭头头部
            ctx.beginPath();
            ctx.moveTo(leftX + 70, leftY - 20);
            ctx.lineTo(leftX + 80, leftY - 10);
            ctx.lineTo(leftX + 70, leftY);
            ctx.stroke();

            ctx.fillStyle = '#FF69B4';
            ctx.fillText('🦋', leftX + 110, leftY);

            // 冰块变水
            const rightX = canvas.width * 0.75;
            const rightY = canvas.height * 0.4;

            ctx.fillStyle = '#87CEEB';
            ctx.fillText('🧊', rightX - 110, rightY);

            // 变换箭头
            ctx.strokeStyle = '#667eea';
            ctx.beginPath();
            ctx.moveTo(rightX - 80, rightY - 10);
            ctx.lineTo(rightX - 30, rightY - 10);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(rightX - 40, rightY - 20);
            ctx.lineTo(rightX - 30, rightY - 10);
            ctx.lineTo(rightX - 40, rightY);
            ctx.stroke();

            ctx.fillStyle = '#4169E1';
            ctx.fillText('💧', rightX, rightY);

            // transform 文字
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('transform', canvas.width / 2, canvas.height * 0.7);

            // 波动效果
            const waveY = canvas.height * 0.8 + 10 * Math.sin(time * 3);
            ctx.fillStyle = '#ff6b6b';
            ctx.font = '18px Arial';
            ctx.fillText('转变的魔法无处不在！', canvas.width / 2, waveY);
        }

        function drawFinalUnderstanding() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 背景
            const gradient = ctx.createRadialGradient(canvas.width/2, canvas.height/2, 0, canvas.width/2, canvas.height/2, canvas.width/2);
            gradient.addColorStop(0, '#FFFACD');
            gradient.addColorStop(1, '#F0E68C');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            const time = Date.now() * 0.003;
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            // 绘制公式
            ctx.fillStyle = '#667eea';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('trans-', centerX - 80, centerY - 40);

            ctx.fillStyle = '#333';
            ctx.font = '20px Arial';
            ctx.fillText('+', centerX - 30, centerY - 40);

            ctx.fillStyle = '#ff6b6b';
            ctx.font = 'bold 24px Arial';
            ctx.fillText('-form', centerX + 20, centerY - 40);

            ctx.fillStyle = '#333';
            ctx.font = '20px Arial';
            ctx.fillText('=', centerX, centerY - 10);

            // 最终单词（带动画效果）
            const scale = 1 + 0.1 * Math.sin(time * 2);
            ctx.save();
            ctx.translate(centerX, centerY + 30);
            ctx.scale(scale, scale);
            ctx.fillStyle = '#4CAF50';
            ctx.font = 'bold 32px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('TRANSFORM', 0, 0);
            ctx.restore();

            // 含义解释
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.fillText('跨越形状的界限 = 转变', centerX, centerY + 80);

            // 庆祝星星
            for (let i = 0; i < 12; i++) {
                const angle = time + i * Math.PI / 6;
                const radius = 100 + 20 * Math.sin(time * 2);
                const x = centerX + radius * Math.cos(angle);
                const y = centerY + radius * Math.sin(angle) * 0.5;
                drawStar(x, y, 8, '#FFD700');
            }
        }

        // 辅助函数
        function drawStar(x, y, size, color) {
            ctx.save();
            ctx.translate(x, y);
            ctx.fillStyle = color;
            ctx.beginPath();
            for (let i = 0; i < 5; i++) {
                const angle = (i * 4 * Math.PI) / 5;
                const x1 = Math.cos(angle) * size;
                const y1 = Math.sin(angle) * size;
                if (i === 0) ctx.moveTo(x1, y1);
                else ctx.lineTo(x1, y1);
            }
            ctx.closePath();
            ctx.fill();
            ctx.restore();
        }

        function drawHeart(x, y, color) {
            ctx.save();
            ctx.translate(x, y);
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.moveTo(0, 5);
            ctx.bezierCurveTo(-10, -5, -20, -5, -10, 0);
            ctx.bezierCurveTo(-10, -10, 0, -10, 0, -5);
            ctx.bezierCurveTo(0, -10, 10, -10, 10, 0);
            ctx.bezierCurveTo(20, -5, 10, -5, 0, 5);
            ctx.closePath();
            ctx.fill();
            ctx.restore();
        }

        // 动画控制
        function startAnimation() {
            if (!currentStory) return;

            isPlaying = true;
            playBtn.textContent = '暂停动画';
            animateCurrentStep();
        }

        function stopAnimation() {
            isPlaying = false;
            playBtn.textContent = '播放动画';
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
        }

        function resetAnimation() {
            stopAnimation();
            animationStep = 0;
            if (currentStory) {
                updateStoryDisplay();
            }
        }

        function animateCurrentStep() {
            if (!isPlaying || !currentStory) return;

            const step = currentStory.steps[animationStep];
            if (step && step.animate) {
                step.animate();
                animationId = requestAnimationFrame(animateCurrentStep);
            }
        }

        function updateStoryDisplay() {
            if (!currentStory) return;

            const step = currentStory.steps[animationStep];
            if (step) {
                storyText.innerHTML = step.text;
                if (step.animate) {
                    step.animate();
                }
            }
        }

        function nextStep() {
            if (!currentStory) return;

            if (animationStep < currentStory.steps.length - 1) {
                animationStep++;
                updateStoryDisplay();
            }
        }

        function prevStep() {
            if (!currentStory) return;

            if (animationStep > 0) {
                animationStep--;
                updateStoryDisplay();
            }
        }

        // 事件监听器
        document.querySelectorAll('.morpheme-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除所有按钮的active类
                document.querySelectorAll('.morpheme-btn').forEach(b => b.classList.remove('active'));
                // 添加当前按钮的active类
                this.classList.add('active');

                const storyType = this.dataset.story;
                currentStory = stories[storyType];
                animationStep = 0;

                if (currentStory) {
                    updateStoryDisplay();
                    stopAnimation();
                }
            });
        });

        playBtn.addEventListener('click', function() {
            if (isPlaying) {
                stopAnimation();
            } else {
                startAnimation();
            }
        });

        resetBtn.addEventListener('click', resetAnimation);

        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (!currentStory) return;

            switch(e.key) {
                case 'ArrowLeft':
                    prevStep();
                    break;
                case 'ArrowRight':
                    nextStep();
                    break;
                case ' ':
                    e.preventDefault();
                    if (isPlaying) {
                        stopAnimation();
                    } else {
                        startAnimation();
                    }
                    break;
                case 'r':
                case 'R':
                    resetAnimation();
                    break;
            }
        });

        // 触摸控制（移动端）
        let touchStartX = 0;
        canvas.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
        });

        canvas.addEventListener('touchend', function(e) {
            const touchEndX = e.changedTouches[0].clientX;
            const diff = touchStartX - touchEndX;

            if (Math.abs(diff) > 50) { // 最小滑动距离
                if (diff > 0) {
                    nextStep(); // 向左滑动，下一步
                } else {
                    prevStep(); // 向右滑动，上一步
                }
            }
        });

        // 点击canvas切换播放状态
        canvas.addEventListener('click', function() {
            if (currentStory) {
                if (isPlaying) {
                    stopAnimation();
                } else {
                    startAnimation();
                }
            }
        });
    </script>
</body>
</html>
