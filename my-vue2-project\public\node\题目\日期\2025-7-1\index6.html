<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式交互学习：状态模式</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
            transition: all 0.3s ease;
        }
        h1, h2 {
            color: #1e3a8a; /* 深蓝色 */
            border-bottom: 2px solid #dbeafe; /* 浅蓝色 */
            padding-bottom: 10px;
        }
        .question-box {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .question-box p {
            line-height: 1.6;
        }
        .interactive-demo {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        canvas {
            border: 2px solid #dbeafe;
            border-radius: 8px;
            background-color: #fff;
        }
        .controls {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 10px;
        }
        button {
            background-color: #2563eb; /* 蓝色 */
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease, transform 0.1s ease;
        }
        button:hover {
            background-color: #1d4ed8; /* 深蓝色 */
        }
        button:active {
            transform: scale(0.98);
        }
        button.active {
            background-color: #1e3a8a;
            box-shadow: 0 0 0 3px #93c5fd;
        }
        .explanation {
            margin-top: 20px;
            background-color: #eef2ff; /* 靛蓝色 */
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #4338ca; /* 深靛蓝 */
        }
        .explanation h3 {
            margin-top: 0;
            color: #4338ca;
        }
        .code-snippet {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', Courier, monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .code-snippet .comment { color: #6a9955; }
        .code-snippet .keyword { color: #569cd6; }
        .code-snippet .class { color: #4ec9b0; }
        .code-snippet .property { color: #9cdcfe; }
        
        @media (max-width: 600px) {
            .interactive-demo {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <h1>题目：图像处理软件设计</h1>
    <div class="question-box">
        <p><strong>背景：</strong>某软件公司欲设计一款图像处理软件，帮助用户对拍摄的照片进行后期处理。</p>
        <p><strong>需求分析：</strong></p>
        <ul>
            <li><strong>需求1：</strong>需要记录用户在处理照片时的所有动作，并能够支持用户动作的撤销与重做。</li>
            <li><strong>需求2：</strong>需要根据当前正在处理的照片的不同特征选择合适的处理操作，处理操作与照片特征之间具有较为复杂的逻辑关系。</li>
            <li><strong>需求3：</strong>需要封装各种图像处理算法，用户能够根据需要灵活选择合适的处理算法。</li>
        </ul>
        <p><strong>问题：</strong>为了封装图像操作与照片特征之间的复杂逻辑关系，采用 <strong>( A ) 状态模式</strong> 最为合适。</p>
    </div>

    <h2>交互式动画：理解状态模式</h2>
    <div class="interactive-demo">
        <canvas id="imageCanvas" width="400" height="300"></canvas>
        <div class="controls">
            <button id="normalBtn" class="active">正常模式</button>
            <button id="grayscaleBtn">黑白模式</button>
            <button id="sepiaBtn">复古模式</button>
        </div>
    </div>
    
    <div class="explanation">
        <h3>知识点讲解：什么是状态模式？</h3>
        <p>状态模式允许一个对象在其<strong>内部状态改变时改变它的行为</strong>。从外部看，这个对象看起来就像是改变了它的类。</p>
        <p><strong>核心思想：</strong>将与特定状态相关的行为局部化，并且将不同状态的行为分割开来，封装到不同的<strong>状态对象</strong>中。环境(Context)对象持有对一个状态对象的引用，并将所有与状态相关的请求委托给它。</p>
        <p>在上面的交互演示中：</p>
        <ul>
            <li><strong>环境 (Context)</strong> 是"图像编辑器"本身。</li>
            <li><strong>状态 (State)</strong> 是"正常模式"、"黑白模式"和"复古模式"。</li>
            <li>当您点击按钮时，您正在改变编辑器的<strong>内部状态</strong>。</li>
            <li>编辑器根据其当前状态，决定了它在画布上的<strong>绘制行为</strong>。</li>
        </ul>
        <p>这样做的好处是，我们避免了在编辑器代码中使用大量的 `if/else` 或 `switch` 语句来判断当前状态，使得添加新的状态（比如"模糊模式"）变得非常容易，而无需修改现有代码。</p>
        <div class="code-snippet">
<pre><span class="comment">// 伪代码演示</span>

<span class="comment">// 环境类：我们的编辑器</span>
<span class="keyword">class</span> <span class="class">ImageEditor</span> {
  <span class="property">currentState</span>: State; <span class="comment">// 持有一个状态对象</span>

  <span class="comment">// 编辑器的行为委托给当前状态</span>
  <span class="keyword">function</span> draw() {
    <span class="keyword">this</span>.<span class="property">currentState</span>.handleDraw();
  }
}

<span class="comment">// 状态接口</span>
<span class="keyword">interface</span> <span class="class">State</span> {
  handleDraw();
}

<span class="comment">// 具体的状态类</span>
<span class="keyword">class</span> <span class="class">GrayscaleState</span> <span class="keyword">implements</span> <span class="class">State</span> {
  handleDraw() {
    <span class="comment">// 执行黑白滤镜的绘制逻辑</span>
  }
}
</pre>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('imageCanvas');
    const ctx = canvas.getContext('2d');
    const buttons = {
        normal: document.getElementById('normalBtn'),
        grayscale: document.getElementById('grayscaleBtn'),
        sepia: document.getElementById('sepiaBtn')
    };

    // --- 状态模式实现 ---

    // 状态基类 (或接口)
    // 定义了所有具体状态共有的行为。
    class DrawingState {
        draw(context, canvas) {
            throw new Error("This method should be overridden!");
        }
    }

    // 具体状态 1: 正常状态
    class NormalState extends DrawingState {
        draw(context, canvas) {
            context.clearRect(0, 0, canvas.width, canvas.height);
            // 创建一个渐变来模拟"彩色"图片
            const gradient = context.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#8ec5fc');
            gradient.addColorStop(1, '#e0c3fc');
            context.fillStyle = gradient;
            context.fillRect(0, 0, canvas.width, canvas.height);
            context.fillStyle = 'black';
            context.font = '24px Arial';
            context.textAlign = 'center';
            context.fillText('正常模式', canvas.width / 2, canvas.height / 2);
        }
    }

    // 具体状态 2: 黑白状态
    class GrayscaleState extends DrawingState {
        draw(context, canvas) {
            context.clearRect(0, 0, canvas.width, canvas.height);
            context.fillStyle = '#a0a0a0';
            context.fillRect(0, 0, canvas.width, canvas.height);
            context.fillStyle = 'white';
            context.font = '24px Arial';
            context.textAlign = 'center';
            context.fillText('黑白模式', canvas.width / 2, canvas.height / 2);
        }
    }

    // 具体状态 3: 复古状态
    class SepiaState extends DrawingState {
        draw(context, canvas) {
            context.clearRect(0, 0, canvas.width, canvas.height);
            context.fillStyle = '#f4a261';
            context.fillRect(0, 0, canvas.width, canvas.height);
            context.fillStyle = '#264653';
            context.font = '24px Arial';
            context.textAlign = 'center';
            context.fillText('复古模式', canvas.width / 2, canvas.height / 2);
        }
    }

    // 环境类: 图像编辑器
    // 它持有一个状态对象，并将绘制工作委托给这个状态。
    class ImageEditor {
        constructor() {
            this.states = {
                normal: new NormalState(),
                grayscale: new GrayscaleState(),
                sepia: new SepiaState()
            };
            this.currentState = this.states.normal; // 初始状态
        }

        // 改变状态的方法
        setState(stateName) {
            if (this.states[stateName]) {
                this.currentState = this.states[stateName];
                this.draw();
            }
        }

        // 将行为委托给当前状态
        draw() {
            this.currentState.draw(ctx, canvas);
        }
    }

    // --- 初始化和事件绑定 ---

    const editor = new ImageEditor();

    function updateActiveButton(activeBtnId) {
        for (const key in buttons) {
            buttons[key].classList.remove('active');
        }
        buttons[activeBtnId].classList.add('active');
    }

    buttons.normal.addEventListener('click', () => {
        editor.setState('normal');
        updateActiveButton('normal');
    });

    buttons.grayscale.addEventListener('click', () => {
        editor.setState('grayscale');
        updateActiveButton('grayscale');
    });

    buttons.sepia.addEventListener('click', () => {
        editor.setState('sepia');
        updateActiveButton('sepia');
    });

    // 初始绘制
    editor.draw();
});
</script>

</body>
</html>
