<template>
  <button
    class="text-button"
    :class="[
      `text-button--${type}`,
      { 'is-disabled': disabled },
      { 'has-icon': icon }
    ]"
    :disabled="disabled"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    ref="button"
  >
    <i v-if="icon" :class="icon" class="button-icon"></i>
    <span class="button-text"><slot></slot></span>
    <span class="button-effect" ref="effect"></span>
  </button>
</template>

<script>
export default {
  name: 'TextButton',
  props: {
    type: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'success', 'warning', 'danger', 'info'].includes(value)
    },
    icon: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick(event) {
      if (this.disabled) return
      this.$emit('click', event)
    },
    handleMouseEnter(event) {
      if (this.disabled) return
      
      const button = this.$refs.button
      const effect = this.$refs.effect
      
      // 获取鼠标相对于按钮的位置
      const rect = button.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top
      
      // 设置涟漪效果的起始位置
      effect.style.left = `${x}px`
      effect.style.top = `${y}px`
      
      // 添加动画类
      effect.classList.add('animate')
      
      // 动画结束后移除类
      effect.addEventListener('animationend', () => {
        effect.classList.remove('animate')
      }, { once: true })
    },
    handleMouseLeave() {
      // 可以添加额外的鼠标离开效果
    }
  }
}
</script>

<style scoped>
.text-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  background: transparent;
  color: var(--text-color);
  cursor: pointer;
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s ease;
  outline: none;
  letter-spacing: 0.5px;
}

.text-button:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.dark-mode .text-button:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.text-button--default {
  color: var(--text-color);
}

.text-button--primary {
  color: var(--primary-color);
}

.text-button--success {
  color: #67c23a;
}

.text-button--warning {
  color: #e6a23c;
}

.text-button--danger {
  color: #f56c6c;
}

.text-button--info {
  color: #909399;
}

.text-button.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.7;
}

.text-button.is-disabled:hover {
  background-color: transparent;
}

.button-icon {
  margin-right: 6px;
  font-size: 16px;
}

.button-text {
  position: relative;
  z-index: 1;
}

.button-effect {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.1);
  width: 0;
  height: 0;
  opacity: 0;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.dark-mode .button-effect {
  background-color: rgba(255, 255, 255, 0.15);
}

.button-effect.animate {
  animation: ripple 0.8s ease-out;
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 0.5;
  }
  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

/* 悬停时的下划线效果 */
.text-button::after {
  content: '';
  position: absolute;
  bottom: 4px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: currentColor;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.text-button:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* 点击效果 */
.text-button:active {
  transform: translateY(1px);
}

/* 有图标的按钮样式调整 */
.text-button.has-icon {
  padding-left: 12px;
}
</style> 