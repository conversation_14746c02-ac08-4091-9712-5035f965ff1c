<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件系统质量属性 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: bold;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .option-card {
            background: #f8f9fa;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .option-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .option-card.correct {
            border-color: #28a745;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .option-card.wrong {
            border-color: #dc3545;
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }

        #gameCanvas {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #gameCanvas:hover {
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .explanation-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .concept-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            transform: translateX(-100px);
            opacity: 0;
            transition: all 0.6s ease;
        }

        .concept-card.visible {
            transform: translateX(0);
            opacity: 1;
        }

        .concept-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .concept-description {
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .game-instructions {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #2196f3;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .score-display {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            font-size: 1.2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 软件系统质量属性</h1>
            <p class="subtitle">通过互动游戏学习软件质量的核心概念</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>【软考达人-回忆版】</strong><br><br>
                软件系统质量属性（Quality Attribute）是一个系统的可测量或者可测试的属性，它被用来描述系统满足利益相关者需求的程度，其中：<br><br>
                <span class="highlight">（ ）</span> 关注的是当需要修改缺陷、增加功能、提高质量属性时，定位修改点并实施修改的难易程度<br><br>
                <span class="highlight">（ ）</span> 关注的是当用户数和数据量增加时，软件系统维持高服务质量的能力
            </div>

            <div class="options-container">
                <div class="option-card" data-option="A">
                    <h3>A. 可用性</h3>
                    <p>系统正常运行的时间比例</p>
                </div>
                <div class="option-card" data-option="B">
                    <h3>B. 可扩展性</h3>
                    <p>系统功能扩展的容易程度</p>
                </div>
                <div class="option-card" data-option="C">
                    <h3>C. 可伸缩性</h3>
                    <p>系统处理负载变化的能力</p>
                </div>
                <div class="option-card" data-option="D">
                    <h3>D. 可移植性</h3>
                    <p>系统在不同环境运行的能力</p>
                </div>
            </div>

            <div class="score-display" id="scoreDisplay">
                🎮 点击选项开始答题！正确答案：第一空是B，第二空是C
            </div>
        </div>

        <div class="canvas-container">
            <h2>🎮 互动演示游戏</h2>
            <div class="game-instructions">
                <strong>游戏说明：</strong> 点击下方画布中的不同区域，体验软件系统在面临各种挑战时的表现！
            </div>
            <canvas id="gameCanvas" width="800" height="400"></canvas>
            <br>
            <button class="btn" onclick="resetGame()">🔄 重新开始</button>
            <button class="btn" onclick="showConcepts()">📚 查看概念解释</button>
        </div>

        <div class="explanation-section" id="explanationSection">
            <h2>📖 核心概念详解</h2>
            
            <div class="concept-card" id="concept1">
                <div class="concept-title">🔧 可扩展性 (Extensibility)</div>
                <div class="concept-description">
                    可扩展性关注的是系统在需要修改、增加功能时的难易程度。就像搭积木一样，设计良好的系统可以轻松添加新模块，而不会影响现有功能。
                </div>
            </div>

            <div class="concept-card" id="concept2">
                <div class="concept-title">📈 可伸缩性 (Scalability)</div>
                <div class="concept-description">
                    可伸缩性关注的是系统在用户数量和数据量增加时，仍能维持良好性能的能力。就像餐厅能否在客人增多时依然提供优质服务。
                </div>
            </div>

            <div class="concept-card" id="concept3">
                <div class="concept-title">⚡ 可用性 (Availability)</div>
                <div class="concept-description">
                    可用性指系统正常运行的时间比例，通常用"几个9"来表示，如99.9%可用性意味着一年中只有8.76小时的停机时间。
                </div>
            </div>

            <div class="concept-card" id="concept4">
                <div class="concept-title">🔄 可移植性 (Portability)</div>
                <div class="concept-description">
                    可移植性指系统在不同硬件、操作系统或环境中运行的能力。就像一个应用能在Windows、Mac、Linux上都能正常工作。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            score: 0,
            selectedAnswer: null,
            gameStarted: false
        };

        // Canvas 游戏
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 游戏对象
        let servers = [];
        let users = [];
        let features = [];
        let animationId;

        // 初始化游戏
        function initGame() {
            // 创建服务器
            servers = [
                { x: 100, y: 200, width: 80, height: 60, load: 0, maxLoad: 100, type: 'server' },
                { x: 300, y: 200, width: 80, height: 60, load: 0, maxLoad: 100, type: 'server' },
                { x: 500, y: 200, width: 80, height: 60, load: 0, maxLoad: 100, type: 'server' }
            ];
            
            // 创建用户
            users = [];
            
            // 创建功能模块
            features = [
                { x: 650, y: 100, width: 120, height: 40, name: '登录模块', connected: true },
                { x: 650, y: 160, width: 120, height: 40, name: '支付模块', connected: true },
                { x: 650, y: 220, width: 120, height: 40, name: '新功能', connected: false }
            ];
            
            drawGame();
        }

        // 绘制游戏
        function drawGame() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制服务器
            servers.forEach((server, index) => {
                // 服务器主体
                ctx.fillStyle = server.load > 80 ? '#dc3545' : server.load > 50 ? '#ffc107' : '#28a745';
                ctx.fillRect(server.x, server.y, server.width, server.height);
                
                // 服务器标签
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`服务器${index + 1}`, server.x + server.width/2, server.y - 10);
                
                // 负载条
                ctx.fillStyle = '#e9ecef';
                ctx.fillRect(server.x, server.y + server.height + 10, server.width, 10);
                ctx.fillStyle = server.load > 80 ? '#dc3545' : '#28a745';
                ctx.fillRect(server.x, server.y + server.height + 10, server.width * (server.load / 100), 10);
                
                // 负载文字
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.fillText(`${Math.round(server.load)}%`, server.x + server.width/2, server.y + server.height + 35);
            });
            
            // 绘制用户
            users.forEach(user => {
                ctx.fillStyle = '#007bff';
                ctx.beginPath();
                ctx.arc(user.x, user.y, 8, 0, 2 * Math.PI);
                ctx.fill();
                
                // 用户到服务器的连线
                if (user.targetServer) {
                    ctx.strokeStyle = '#6c757d';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(user.x, user.y);
                    ctx.lineTo(user.targetServer.x + user.targetServer.width/2, user.targetServer.y + user.targetServer.height/2);
                    ctx.stroke();
                }
            });
            
            // 绘制功能模块
            features.forEach(feature => {
                ctx.fillStyle = feature.connected ? '#28a745' : '#6c757d';
                ctx.fillRect(feature.x, feature.y, feature.width, feature.height);
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(feature.name, feature.x + feature.width/2, feature.y + feature.height/2 + 4);
            });
            
            // 绘制说明文字
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('点击不同区域体验：', 50, 50);
            ctx.font = '14px Arial';
            ctx.fillText('• 左侧：添加用户（测试可伸缩性）', 50, 80);
            ctx.fillText('• 右侧：添加新功能（测试可扩展性）', 50, 100);
        }

        // Canvas 点击事件
        canvas.addEventListener('click', function(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            // 点击左半部分 - 添加用户（可伸缩性测试）
            if (x < canvas.width / 2) {
                addUser();
            }
            // 点击右半部分 - 添加功能（可扩展性测试）
            else {
                addFeature();
            }
        });

        // 添加用户
        function addUser() {
            const newUser = {
                x: Math.random() * 200 + 50,
                y: Math.random() * 100 + 350,
                targetServer: null
            };
            
            // 找到负载最低的服务器
            let minLoadServer = servers[0];
            servers.forEach(server => {
                if (server.load < minLoadServer.load) {
                    minLoadServer = server;
                }
            });
            
            newUser.targetServer = minLoadServer;
            minLoadServer.load += 20;
            
            users.push(newUser);
            drawGame();
            
            // 显示可伸缩性提示
            if (minLoadServer.load > 80) {
                showToast('⚠️ 服务器负载过高！需要扩展更多服务器来提高可伸缩性！');
            } else {
                showToast('✅ 系统成功处理了新用户，展现了良好的可伸缩性！');
            }
        }

        // 添加功能
        function addFeature() {
            const disconnectedFeature = features.find(f => !f.connected);
            if (disconnectedFeature) {
                disconnectedFeature.connected = true;
                drawGame();
                showToast('✅ 新功能成功集成！系统展现了良好的可扩展性！');
            } else {
                showToast('🎉 所有功能都已连接！系统具有优秀的可扩展性！');
            }
        }

        // 显示提示
        function showToast(message) {
            const scoreDisplay = document.getElementById('scoreDisplay');
            scoreDisplay.textContent = message;
            scoreDisplay.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
            scoreDisplay.style.color = 'white';
            
            setTimeout(() => {
                scoreDisplay.style.background = 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)';
                scoreDisplay.style.color = '#333';
            }, 3000);
        }

        // 重置游戏
        function resetGame() {
            initGame();
            document.getElementById('scoreDisplay').textContent = '🎮 游戏已重置！点击画布开始体验！';
        }

        // 显示概念
        function showConcepts() {
            const concepts = document.querySelectorAll('.concept-card');
            concepts.forEach((concept, index) => {
                setTimeout(() => {
                    concept.classList.add('visible');
                }, index * 200);
            });
        }

        // 选项点击事件
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('click', function() {
                const option = this.dataset.option;
                
                // 清除之前的选择
                document.querySelectorAll('.option-card').forEach(c => {
                    c.classList.remove('selected', 'correct', 'wrong');
                });
                
                // 标记当前选择
                this.classList.add('selected');
                
                // 显示正确答案
                setTimeout(() => {
                    if (option === 'B') {
                        this.classList.remove('selected');
                        this.classList.add('correct');
                        showToast('🎉 第一空正确！可扩展性确实关注修改和扩展的难易程度！');
                    } else if (option === 'C') {
                        this.classList.remove('selected');
                        this.classList.add('correct');
                        showToast('🎉 第二空正确！可伸缩性关注处理负载增长的能力！');
                    } else {
                        this.classList.remove('selected');
                        this.classList.add('wrong');
                        showToast('❌ 再想想看！提示：第一空是B，第二空是C');
                    }
                }, 500);
            });
        });

        // 初始化
        window.addEventListener('load', function() {
            initGame();
            setTimeout(showConcepts, 1000);
        });
    </script>
</body>
</html>
