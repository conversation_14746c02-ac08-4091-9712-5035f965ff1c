<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前趋图学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .content-section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8em;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #fafafa;
            cursor: pointer;
            transition: box-shadow 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9ff;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 10px 10px 0;
            font-size: 16px;
            line-height: 1.6;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .answer-section {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #667eea;
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: #4caf50;
            color: white;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header fade-in">
            <h1>🎯 前趋图学习教程</h1>
            <p>零基础也能轻松掌握！</p>
        </div>

        <!-- 知识点介绍 -->
        <div class="content-section fade-in">
            <h2 class="section-title">📚 什么是前趋图？</h2>
            <div class="explanation">
                <p><span class="highlight">前趋图</span>是一个有向无环图，用来表示进程之间的执行顺序关系。</p>
                <p>🔹 <strong>有向</strong>：箭头表示方向，从前面的进程指向后面的进程</p>
                <p>🔹 <strong>无环</strong>：不能形成循环，避免死锁</p>
                <p>🔹 <strong>前趋关系</strong>：Pi → Pj 表示Pi必须在Pj开始前完成</p>
            </div>
        </div>

        <!-- 交互式图形展示 -->
        <div class="content-section fade-in">
            <h2 class="section-title">🎨 交互式前趋图</h2>
            <div class="step-indicator">
                <div class="step active" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
            </div>
            <div class="canvas-container">
                <canvas id="graphCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showStep(1)">🎬 开始演示</button>
                <button class="btn" onclick="showStep(2)">📝 标记关系</button>
                <button class="btn" onclick="showStep(3)">🔍 分析路径</button>
                <button class="btn" onclick="showStep(4)">✅ 查看答案</button>
            </div>
        </div>

        <!-- 题目分析 -->
        <div class="content-section fade-in">
            <h2 class="section-title">🧠 解题思路</h2>
            <div class="explanation">
                <h3>📋 题目要求：</h3>
                <p>根据给定的前趋图，找出所有的前趋关系对 (Pi, Pj)</p>

                <h3>🔍 解题步骤：</h3>
                <ol style="margin-left: 20px; line-height: 2;">
                    <li><strong>观察图形</strong>：找出所有的箭头连接</li>
                    <li><strong>记录关系</strong>：每个箭头代表一个前趋关系</li>
                    <li><strong>按顺序列出</strong>：从起始节点开始，逐一记录</li>
                    <li><strong>检查完整性</strong>：确保没有遗漏任何箭头</li>
                </ol>
            </div>
        </div>

        <!-- 答案解析 -->
        <div class="content-section fade-in">
            <h2 class="section-title">✨ 答案解析</h2>
            <div class="answer-section">
                <h3>🎯 正确答案：A</h3>
                <p><strong>前趋关系集合：</strong></p>
                <div style="background: white; padding: 15px; border-radius: 10px; margin: 10px 0; font-family: monospace;">
                    →={（P1,P2）,（P1,P3）,（P1,P4）,（P2,P4）,（P3,P4）,（P3,P5）,（P4,P5）,（P5,P6）,（P5,P7）,（P5,P8）,（P6,P7）}
                </div>
                <p><strong>📊 总共11个前趋关系</strong></p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('graphCanvas');
        const ctx = canvas.getContext('2d');

        // 节点位置定义
        const nodes = {
            P1: { x: 100, y: 150, color: '#ff6b6b' },
            P2: { x: 200, y: 250, color: '#4ecdc4' },
            P3: { x: 200, y: 100, color: '#45b7d1' },
            P4: { x: 350, y: 200, color: '#96ceb4' },
            P5: { x: 500, y: 150, color: '#feca57' },
            P6: { x: 600, y: 100, color: '#ff9ff3' },
            P7: { x: 700, y: 200, color: '#54a0ff' },
            P8: { x: 600, y: 250, color: '#5f27cd' }
        };

        // 边的定义
        const edges = [
            { from: 'P1', to: 'P2' },
            { from: 'P1', to: 'P3' },
            { from: 'P1', to: 'P4' },
            { from: 'P2', to: 'P4' },
            { from: 'P3', to: 'P4' },
            { from: 'P3', to: 'P5' },
            { from: 'P4', to: 'P5' },
            { from: 'P5', to: 'P6' },
            { from: 'P5', to: 'P7' },
            { from: 'P5', to: 'P8' },
            { from: 'P6', to: 'P7' }
        ];

        let currentStep = 0;
        let animationFrame = 0;
        let highlightedEdges = [];
        let highlightedNodes = [];

        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function drawNode(nodeId, highlight = false, pulse = false) {
            const node = nodes[nodeId];
            const radius = pulse ? 25 + Math.sin(animationFrame * 0.1) * 5 : 25;

            ctx.beginPath();
            ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI);
            ctx.fillStyle = highlight ? '#ff4757' : node.color;
            ctx.fill();
            ctx.strokeStyle = highlight ? '#ff3742' : '#333';
            ctx.lineWidth = highlight ? 3 : 2;
            ctx.stroke();

            // 绘制节点标签
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(nodeId, node.x, node.y);
        }

        function drawArrow(fromNode, toNode, highlight = false, animated = false) {
            const from = nodes[fromNode];
            const to = nodes[toNode];

            // 计算箭头位置
            const angle = Math.atan2(to.y - from.y, to.x - from.x);
            const startX = from.x + 25 * Math.cos(angle);
            const startY = from.y + 25 * Math.sin(angle);
            const endX = to.x - 25 * Math.cos(angle);
            const endY = to.y - 25 * Math.sin(angle);

            // 动画效果
            let progress = 1;
            if (animated) {
                progress = Math.min(1, (animationFrame % 60) / 60);
            }

            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.strokeStyle = highlight ? '#ff4757' : '#333';
            ctx.lineWidth = highlight ? 4 : 2;
            ctx.stroke();

            // 绘制箭头头部
            const arrowLength = 15;
            const arrowAngle = Math.PI / 6;

            ctx.beginPath();
            ctx.moveTo(endX, endY);
            ctx.lineTo(
                endX - arrowLength * Math.cos(angle - arrowAngle),
                endY - arrowLength * Math.sin(angle - arrowAngle)
            );
            ctx.moveTo(endX, endY);
            ctx.lineTo(
                endX - arrowLength * Math.cos(angle + arrowAngle),
                endY - arrowLength * Math.sin(angle + arrowAngle)
            );
            ctx.stroke();
        }

        function drawGraph() {
            clearCanvas();

            // 绘制所有边
            edges.forEach((edge, index) => {
                const isHighlighted = highlightedEdges.includes(index);
                const isAnimated = currentStep === 2 && isHighlighted;
                drawArrow(edge.from, edge.to, isHighlighted, isAnimated);
            });

            // 绘制所有节点
            Object.keys(nodes).forEach(nodeId => {
                const isHighlighted = highlightedNodes.includes(nodeId);
                const isPulsing = currentStep === 1 && nodeId === 'P1';
                drawNode(nodeId, isHighlighted, isPulsing);
            });

            animationFrame++;
            requestAnimationFrame(drawGraph);
        }

        function updateStepIndicator(step) {
            for (let i = 1; i <= 4; i++) {
                const stepEl = document.getElementById(`step${i}`);
                stepEl.classList.remove('active', 'completed');
                if (i < step) {
                    stepEl.classList.add('completed');
                } else if (i === step) {
                    stepEl.classList.add('active');
                }
            }
        }

        function showStep(step) {
            currentStep = step;
            updateStepIndicator(step);
            highlightedEdges = [];
            highlightedNodes = [];

            switch(step) {
                case 1:
                    // 显示基本图形
                    highlightedNodes = ['P1'];
                    showExplanation("🎬 这是我们的前趋图！P1是起始节点，观察所有的箭头方向。");
                    break;
                case 2:
                    // 逐步标记关系
                    highlightedEdges = [0, 1, 2]; // P1的所有出边
                    showExplanation("📝 从P1开始：P1→P2, P1→P3, P1→P4");
                    setTimeout(() => {
                        highlightedEdges = [3, 4]; // P2和P3到P4
                        showExplanation("📝 继续：P2→P4, P3→P4");
                    }, 2000);
                    setTimeout(() => {
                        highlightedEdges = [5, 6]; // P3和P4到P5
                        showExplanation("📝 然后：P3→P5, P4→P5");
                    }, 4000);
                    setTimeout(() => {
                        highlightedEdges = [7, 8, 9]; // P5的所有出边
                        showExplanation("📝 P5的出边：P5→P6, P5→P7, P5→P8");
                    }, 6000);
                    setTimeout(() => {
                        highlightedEdges = [10]; // P6到P7
                        showExplanation("📝 最后：P6→P7");
                    }, 8000);
                    break;
                case 3:
                    // 分析路径
                    highlightedEdges = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
                    showExplanation("🔍 总共11条边，每条边代表一个前趋关系！");
                    break;
                case 4:
                    // 显示答案
                    highlightedEdges = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
                    showExplanation("✅ 答案A正确！包含了所有11个前趋关系。");
                    break;
            }
        }

        function showExplanation(text) {
            // 创建临时提示框
            const tooltip = document.createElement('div');
            tooltip.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 20px;
                border-radius: 10px;
                font-size: 18px;
                z-index: 1000;
                max-width: 400px;
                text-align: center;
                animation: fadeIn 0.5s ease-in;
            `;
            tooltip.textContent = text;
            document.body.appendChild(tooltip);

            setTimeout(() => {
                if (document.body.contains(tooltip)) {
                    document.body.removeChild(tooltip);
                }
            }, 3000);
        }

        // 初始化
        drawGraph();
        showStep(1);
    </script>
</body>
</html>
