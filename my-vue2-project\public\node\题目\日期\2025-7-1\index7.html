<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            padding: 25px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        h1, h2 {
            color: #1a2a4c;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .question-box, .interactive-box, .explanation-box {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        .question-box {
            background-color: #f9f9f9;
        }
        .question-box ul {
            list-style-type: none;
            padding-left: 0;
        }
        .question-box li {
            background-color: #e9ecef;
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 5px;
        }
        .options {
            margin-top: 15px;
        }
        .options label {
            display: block;
            margin-bottom: 10px;
            cursor: pointer;
            padding: 10px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .options label:hover {
            background-color: #e9ecef;
        }
        .options input {
            margin-right: 10px;
        }
        #answer {
            font-weight: bold;
            color: #28a745;
        }
        .interactive-area {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: space-around;
            flex-wrap: wrap;
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #fff;
            border-radius: 8px;
        }
        .controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .controls button {
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s, transform 0.2s;
        }
        .controls button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        .controls button.active {
            background-color: #28a745;
        }
        #explanation-text {
            margin-top: 15px;
            padding: 15px;
            background-color: #e3f2fd;
            border-left: 5px solid #007bff;
            border-radius: 5px;
            min-height: 50px;
            transition: opacity 0.5s;
        }
        .explanation-box ul {
            padding-left: 20px;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>题目 10：设计模式选择</h1>

    <div class="question-box">
        <p>某软件公司欲设计一款图像处理软件，帮助用户对拍摄的照片进行后期处理。在软件需求分析阶段，公司的系统分析师识别出了如下3个关键需求：</p>
        <ul>
            <li><b>需求1:</b> 图像处理软件需要记录用户在处理照片时的所有动作，并能够支持用户动作的撤销与重做等行为。</li>
            <li><b>需求2:</b> 图像处理软件需要根据当前正在处理的照片的不同特征选择合适的处理操作，处理操作与照片特征之间具有较为复杂的逻辑关系。</li>
            <li><b>需求3:</b> 图像处理软件需要封装各种图像处理算法，用户能够根据需要灵活选择合适的处理算法；软件还要支持高级用户根据一定的规则添加自定义处理算法。</li>
        </ul>
        <p>在系统设计阶段，公司的架构师决定采用设计模式满足上述关键需求中对系统灵活性与扩展性的要求。具体来说，为了实现图像处理算法的灵活选择与替换，采用 ( <b>C</b> ) 最为合适。</p>
        <div class="options">
            <label><input type="radio" name="pattern" value="A"> A) 模板方法模式</label>
            <label><input type="radio" name="pattern" value="B"> B) 访问者模式</label>
            <label><input type="radio" name="pattern" value="C" checked> C) 策略模式</label>
            <label><input type="radio" name="pattern" value="D"> D) 观察者模式</label>
        </div>
        <p><b>正确答案： <span id="answer">C</span></b></p>
    </div>

    <div class="interactive-box">
        <h2>交互式动画演示 - 策略模式</h2>
        <div class="interactive-area">
            <div class="controls">
                <p><b>请为"图片"选择一个处理策略：</b></p>
                <button id="normalBtn" class="active">原始效果</button>
                <button id="grayscaleBtn">黑白滤镜</button>
                <button id="sepiaBtn">复古滤镜</button>
                <button id="invertBtn">负片效果</button>
            </div>
            <canvas id="canvas" width="400" height="300"></canvas>
        </div>
        <div id="explanation-text">
            <p>这里将显示当前操作的解释。</p>
        </div>
    </div>

    <div class="explanation-box">
        <h2>知识点解析</h2>
        <p>这道题的核心是为不同的需求选择最合适的设计模式。设计模式是解决软件设计中常见问题的"标准答案"。</p>
        <ul>
            <li>
                <p><b>策略模式 (Strategy Pattern) - 对应需求3 (本题答案)</b></p>
                <p><b>是什么：</b>它定义了一系列算法，将每个算法都封装起来，并且使它们可以相互替换。这样，算法的变化就不会影响到使用算法的客户端。</p>
                <p><b>怎么用：</b>在我们的动画里，"图片"就是客户端。"滤镜"就是各种可以被替换的算法（策略）。你点击不同按钮，就相当于给图片动态地换上了不同的处理策略，而不需要改变图片本身的代码。这完美契合了"灵活选择与替换算法"的需求。</p>
            </li>
            <li>
                <p><b>命令模式 (Command Pattern) - 对应需求1</b></p>
                <p><b>是什么：</b>将一个请求封装为一个对象，从而使你可用不同的请求对客户进行参数化，对请求排队或记录请求日志，以及支持可撤销的操作。</p>
                <p><b>怎么用：</b>用户的每一步操作（比如使用黑白滤镜、调整亮度）都可以变成一个"命令"对象。软件把这些命令记下来，想撤销（Undo）时，就执行这个命令的"反操作"即可。</p>
            </li>
            <li>
                <p><b>状态模式 (State Pattern) - 对应需求2</b></p>
                <p><b>是什么：</b>允许一个对象在其内部状态改变时改变它的行为。对象看起来似乎修改了它的类。</p>
                <p><b>怎么用：</b>比如，一张照片有"白天拍摄"和"夜晚拍摄"两种状态。在"白天"状态下，可用的处理操作可能是"增加饱和度"；而在"夜晚"状态下，可用的操作会自动变为"增加亮度"。状态一变，行为就跟着变。</p>
            </li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');
    const explanationText = document.getElementById('explanation-text').querySelector('p');
    
    const normalBtn = document.getElementById('normalBtn');
    const grayscaleBtn = document.getElementById('grayscaleBtn');
    const sepiaBtn = document.getElementById('sepiaBtn');
    const invertBtn = document.getElementById('invertBtn');
    const buttons = [normalBtn, grayscaleBtn, sepiaBtn, invertBtn];

    // --- 策略的定义 ---
    // 这是一个基础的"策略"接口，虽然在JS中不是必须的，但有助于理解
    class FilterStrategy {
        apply(context) {
            throw new Error("This method should be overridden!");
        }
        getExplanation() {
            return "这是一个基础策略。";
        }
    }

    // 具体策略1：原始效果
    class NormalStrategy extends FilterStrategy {
        apply(ctx) {
            ctx.fillStyle = 'deepskyblue';
            ctx.fillRect(100, 75, 200, 150);
            ctx.fillStyle = 'gold';
            ctx.beginPath();
            ctx.arc(260, 120, 25, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = 'green';
            ctx.fillRect(100, 225, 200, 25);
        }
        getExplanation() {
            return "<b>原始策略：</b>不应用任何特殊效果，显示图片的本来面貌。";
        }
    }

    // 具体策略2：黑白滤镜
    class GrayscaleStrategy extends FilterStrategy {
        apply(ctx) {
            ctx.fillStyle = '#aaa';
            ctx.fillRect(100, 75, 200, 150);
            ctx.fillStyle = '#ddd';
            ctx.beginPath();
            ctx.arc(260, 120, 25, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = '#666';
            ctx.fillRect(100, 225, 200, 25);
        }
        getExplanation() {
            return "<b>黑白策略：</b>将图片处理成灰度图。你看，我们只是切换了策略，图片就变了！";
        }
    }

    // 具体策略3：复古滤镜
    class SepiaStrategy extends FilterStrategy {
        apply(ctx) {
            ctx.fillStyle = '#a58d6f';
            ctx.fillRect(100, 75, 200, 150);
            ctx.fillStyle = '#e3d5b8';
            ctx.beginPath();
            ctx.arc(260, 120, 25, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = '#706049';
            ctx.fillRect(100, 225, 200, 25);
        }
        getExplanation() {
            return "<b>复古策略：</b>应用复古色调。这就是策略模式的威力：算法可以轻松地相互替换。";
        }
    }
    
    // 具体策略4：负片效果
    class InvertStrategy extends FilterStrategy {
         apply(ctx) {
            ctx.fillStyle = 'rgb(85, 146, 255)'; // Inverted deepskyblue
            ctx.fillRect(100, 75, 200, 150);
            ctx.fillStyle = 'rgb(255, 21, 0)'; // Inverted gold
            ctx.beginPath();
            ctx.arc(260, 120, 25, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = 'rgb(255, 0, 255)'; // Inverted green
            ctx.fillRect(100, 225, 200, 25);
        }
        getExplanation() {
            return "<b>负片策略：</b>将图片的颜色反转。我们可以随时定义和添加新的策略，而不需要修改核心代码。";
        }
    }


    // --- 上下文的定义 ---
    // ImageProcessor是使用策略的对象
    class ImageProcessor {
        constructor(strategy) {
            this.strategy = strategy;
        }

        setStrategy(strategy) {
            this.strategy = strategy;
        }

        process(ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            this.strategy.apply(ctx);
            explanationText.innerHTML = this.strategy.getExplanation();
        }
    }

    // --- 初始化 ---
    const strategies = {
        normal: new NormalStrategy(),
        grayscale: new GrayscaleStrategy(),
        sepia: new SepiaStrategy(),
        invert: new InvertStrategy()
    };
    
    // 创建一个图像处理器，默认使用"原始"策略
    const processor = new ImageProcessor(strategies.normal);
    
    function setActiveButton(activeBtn) {
        buttons.forEach(btn => {
            btn.classList.remove('active');
        });
        activeBtn.classList.add('active');
    }

    // --- 事件绑定 ---
    normalBtn.addEventListener('click', () => {
        processor.setStrategy(strategies.normal);
        processor.process(ctx);
        setActiveButton(normalBtn);
    });

    grayscaleBtn.addEventListener('click', () => {
        processor.setStrategy(strategies.grayscale);
        processor.process(ctx);
        setActiveButton(grayscaleBtn);
    });

    sepiaBtn.addEventListener('click', () => {
        processor.setStrategy(strategies.sepia);
        processor.process(ctx);
        setActiveButton(sepiaBtn);
    });
    
    invertBtn.addEventListener('click', () => {
        processor.setStrategy(strategies.invert);
        processor.process(ctx);
        setActiveButton(invertBtn);
    });

    // --- 初始绘制 ---
    processor.process(ctx);
    setActiveButton(normalBtn);

});
</script>

</body>
</html>
