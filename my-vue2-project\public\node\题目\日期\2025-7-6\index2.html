<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构风格学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 25px;
        }
        section {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            padding: 30px;
            max-width: 900px;
            width: 100%;
        }
        #question-section .question-text {
            font-size: 1.25em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        #question-section .options label {
            display: block;
            background-color: #ecf0f1;
            padding: 15px 20px;
            margin-bottom: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-size: 1.1em;
            display: flex;
            align-items: center;
        }
        #question-section .options label:hover {
            background-color: #dcdfe1;
        }
        #question-section .options input[type="radio"] {
            margin-right: 15px;
            transform: scale(1.2);
        }
        #question-section .feedback {
            margin-top: 20px;
            font-weight: bold;
            font-size: 1.1em;
            text-align: center;
        }
        #question-section .feedback.correct {
            color: #27ae60;
        }
        #question-section .feedback.incorrect {
            color: #e74c3c;
        }
        .correct-answer-display {
            font-size: 1.1em;
            margin-top: 15px;
            text-align: center;
            font-weight: bold;
            color: #27ae60;
        }
        #explanation-section p {
            font-size: 1.05em;
            margin-bottom: 15px;
        }
        .canvas-container {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        canvas {
            border: 2px solid #ccc;
            background-color: #f9f9f9;
            display: block;
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }
        .controls {
            text-align: center;
            margin-top: 15px;
        }
        .controls button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }
        .controls button:hover {
            background-color: #2980b9;
        }
    </style>
</head>
<body>
    <h1>软件架构风格学习与演示</h1>

    <section id="question-section">
        <h2>问题</h2>
        <div class="question-text">
            软件架构风格描述某一特定领域中的系统组织方式和惯用模式，反映了领域中众多系统所具有的（ ）特征。对于语音识别、知识推理等问题复杂、解空间很大、求解过程不确定的这一类软件系统，通常会采用（/）架构风格。
        </div>
        <div class="options">
            <label>
                <input type="radio" name="answer" value="A"> A. 语法和语义
            </label>
            <label>
                <input type="radio" name="answer" value="B"> B. 结构和语义
            </label>
            <label>
                <input type="radio" name="answer" value="C"> C. 静态和动态
            </label>
            <label>
                <input type="radio" name="answer" value="D"> D. 行为和约束
            </label>
        </div>
        <div class="feedback" id="question-feedback"></div>
        <div class="correct-answer-display" id="correct-answer-display" style="display:none;">正确答案：B. 结构和语义</div>
    </section>

    <section id="explanation-section">
        <h2>知识点解释</h2>
        <p>软件架构风格是描述某一特定领域中系统组织方式和惯用模式的抽象。它不仅仅是关于代码的组织，更是反映了系统中组件、连接件以及它们之间如何交互的**结构**，以及这些组件和连接件所表达的**语义**，即它们如何协同工作以实现系统的功能。</p>
        <p><strong>结构（Structure）：</strong> 指的是系统的组成部分，如组件（模块、类、服务等）以及它们之间的关系（连接、依赖、层次等）。它描述了系统"长什么样"。</p>
        <p><strong>语义（Semantics）：</strong> 指的是这些组成部分的功能、行为和它们之间交互的含义。它描述了系统"如何工作"以及为什么这样工作。</p>
        <p>因此，一个完整的软件架构风格既包含了系统的组织结构，也包含了这种结构背后所承载的功能和意义，所以"结构和语义"是其核心特征。</p>
        
        <h3>两种典型的架构风格：</h3>
        <h4>1. 黑板架构（Blackboard Architecture）</h4>
        <p>对于语音识别、知识推理等问题复杂、解空间很大、求解过程不确定的这类系统，通常会采用黑板架构风格。黑板架构的核心思想是：有一个共享的"黑板"数据区，多个独立的"知识源"通过读取和写入黑板上的数据来协同工作，一个"控制组件"负责协调知识源的活动。</p>
        <p>它适用于那些没有确定性算法，需要探索性、迭代性解决问题的场景。</p>
        <div class="canvas-container">
            <canvas id="blackboardCanvas" width="700" height="400"></canvas>
        </div>
        <div class="controls">
            <button onclick="startBlackboardDemo()">开始黑板架构演示</button>
            <button onclick="resetBlackboardDemo()">重置演示</button>
        </div>

        <h4>2. 管道-过滤器体系结构（Pipe-Filter Architecture）</h4>
        <p>对于因数据而驱动，数据到达某个构件，经过内部处理，产生数据输出的系统，通常采用管道-过滤器体系结构风格。这种风格由一系列的"过滤器"组成，每个过滤器对数据流进行独立的转换处理，而"管道"负责在过滤器之间传递数据。</p>
        <p>它适用于数据处理流水线，如编译器、图像处理系统等。</p>
        <div class="canvas-container">
            <canvas id="pipeFilterCanvas" width="700" height="300"></canvas>
        </div>
        <div class="controls">
            <button onclick="startPipeFilterDemo()">开始管道-过滤器演示</button>
            <button onclick="resetPipeFilterDemo()">重置演示</button>
        </div>
    </section>

    <script>
        const questionFeedback = document.getElementById('question-feedback');
        const correctAnswerDisplay = document.getElementById('correct-answer-display');
        const options = document.querySelectorAll('input[name="answer"]');
        const correctOptionValue = 'B'; // The correct answer for the question

        options.forEach(option => {
            option.addEventListener('change', function() {
                if (this.value === correctOptionValue) {
                    questionFeedback.textContent = '恭喜你，回答正确！';
                    questionFeedback.className = 'feedback correct';
                    correctAnswerDisplay.style.display = 'block'; // Show correct answer explanation
                } else {
                    questionFeedback.textContent = '很遗憾，回答错误。请再思考一下。';
                    questionFeedback.className = 'feedback incorrect';
                    correctAnswerDisplay.style.display = 'block'; // Still show correct answer explanation
                }
            });
        });

        // --- Blackboard Architecture Canvas Demo ---
        const blackboardCanvas = document.getElementById('blackboardCanvas');
        const ctxBlackboard = blackboardCanvas.getContext('2d');
        let blackboardAnimationId;
        let ksState = { ks1: 0, ks2: 0, ks3: 0 }; // 0: inactive, 1: processing, 2: finished
        let blackboardContent = []; // To store content written on blackboard

        function drawBlackboardScene() {
            ctxBlackboard.clearRect(0, 0, blackboardCanvas.width, blackboardCanvas.height);

            // Draw Blackboard
            ctxBlackboard.fillStyle = '#34495e'; // Dark blue-gray
            ctxBlackboard.fillRect(150, 50, 400, 250);
            ctxBlackboard.strokeStyle = '#2c3e50';
            ctxBlackboard.lineWidth = 5;
            ctxBlackboard.strokeRect(150, 50, 400, 250);
            ctxBlackboard.fillStyle = '#ecf0f1';
            ctxBlackboard.font = '20px Arial';
            ctxBlackboard.textAlign = 'center';
            ctxBlackboard.fillText('黑板 (共享数据区)', blackboardCanvas.width / 2, 30);

            // Draw Knowledge Sources
            const ksPositions = [{x: 50, y: 150}, {x: blackboardCanvas.width - 100, y: 150}, {x: blackboardCanvas.width / 2, y: 350}];
            const ksNames = ['知识源 A', '知识源 B', '知识源 C'];

            ksPositions.forEach((pos, index) => {
                ctxBlackboard.fillStyle = ksState['ks' + (index + 1)] === 1 ? '#f39c12' : '#2ecc71'; // Orange if processing, green otherwise
                ctxBlackboard.fillRect(pos.x, pos.y, 100, 50);
                ctxBlackboard.strokeStyle = '#2c3e50';
                ctxBlackboard.strokeRect(pos.x, pos.y, 100, 50);
                ctxBlackboard.fillStyle = 'white';
                ctxBlackboard.font = '16px Arial';
                ctxBlackboard.fillText(ksNames[index], pos.x + 50, pos.y + 30);

                // Draw arrows to blackboard (simplified)
                if (ksState['ks' + (index + 1)] > 0) {
                    ctxBlackboard.strokeStyle = '#e74c3c'; // Red for active interaction
                    ctxBlackboard.lineWidth = 3;
                    ctxBlackboard.beginPath();
                    // Arrow from KS A to blackboard
                    if (index === 0) {
                        ctxBlackboard.moveTo(pos.x + 100, pos.y + 25);
                        ctxBlackboard.lineTo(150, 175);
                    }
                    // Arrow from KS B to blackboard
                    else if (index === 1) {
                        ctxBlackboard.moveTo(pos.x, pos.y + 25);
                        ctxBlackboard.lineTo(550, 175);
                    }
                    // Arrow from KS C to blackboard (below)
                    else if (index === 2) {
                        ctxBlackboard.moveTo(pos.x + 50, pos.y);
                        ctxBlackboard.lineTo(blackboardCanvas.width / 2, 300);
                    }
                    ctxBlackboard.stroke();
                    drawArrowhead(ctxBlackboard, ctxBlackboard.currentPoint.x, ctxBlackboard.currentPoint.y, 
                                 (index === 0 ? 150 : (index === 1 ? 550 : blackboardCanvas.width / 2)),
                                 (index === 0 || index === 1 ? 175 : 300));
                }
            });

            // Draw content on blackboard
            ctxBlackboard.fillStyle = 'white';
            ctxBlackboard.font = '18px Arial';
            let textY = 80;
            blackboardContent.forEach(item => {
                ctxBlackboard.fillText(item.text, blackboardCanvas.width / 2, textY);
                textY += 25;
            });

            // Draw Control Component
            ctxBlackboard.fillStyle = '#9b59b6'; // Purple
            ctxBlackboard.beginPath();
            ctxBlackboard.arc(blackboardCanvas.width / 2, 350, 40, 0, Math.PI * 2);
            ctxBlackboard.fill();
            ctxBlackboard.strokeStyle = '#8e44ad';
            ctxBlackboard.lineWidth = 3;
            ctxBlackboard.stroke();
            ctxBlackboard.fillStyle = 'white';
            ctxBlackboard.font = '16px Arial';
            ctxBlackboard.fillText('控制', blackboardCanvas.width / 2, 355);
        }

        // Helper to draw arrowhead (simplified)
        function drawArrowhead(ctx, fromX, fromY, toX, toY) {
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        function animateBlackboard() {
            let activeKS = null;
            for (const key in ksState) {
                if (ksState[key] === 1) {
                    activeKS = key;
                    break;
                }
            }

            if (activeKS) {
                // Simulate processing
                setTimeout(() => {
                    const ksIndex = parseInt(activeKS.replace('ks', '')) - 1;
                    const knowledge = ['问题分解', '假设生成', '方案评估'];
                    blackboardContent.push({ text: `[${knowledge[ksIndex]}] 由 ${activeKS} 写入` });
                    ksState[activeKS] = 2; // Mark as finished

                    // Simple control logic: activate next KS
                    let nextKSFound = false;
                    for (let i = 0; i < 3; i++) {
                        const nextKsKey = 'ks' + ((ksIndex + 1 + i) % 3 + 1); // Cycle through KS
                        if (ksState[nextKsKey] === 0) { // If not processed yet
                            ksState[nextKsKey] = 1;
                            nextKSFound = true;
                            break;
                        }
                    }
                    if (!nextKSFound && blackboardContent.length < 5) { // If all processed, but not enough content, restart some
                         // This is a simplified demo, in real blackboard, control logic is more complex
                         // For demo purposes, just trigger a KS if less than 5 entries
                         const randomKs = 'ks' + (Math.floor(Math.random() * 3) + 1);
                         if (ksState[randomKs] === 2) ksState[randomKs] = 0; // reset a finished KS for more interaction
                         ksState[randomKs] = 1;
                    }
                     if (blackboardContent.length >= 5) { // Stop after some content
                        cancelAnimationFrame(blackboardAnimationId);
                        blackboardAnimationId = null;
                        setTimeout(() => {
                            alert("黑板架构演示完成，知识源通过共享黑板协同解决复杂问题！");
                        }, 500);
                    } else if (nextKSFound || blackboardContent.length < 5) { // Continue if there's more to do
                         blackboardAnimationId = requestAnimationFrame(animateBlackboard);
                    }
                }, 1500); // Simulate processing time
            }
            drawBlackboardScene();
        }

        function startBlackboardDemo() {
            if (blackboardAnimationId) return; // Already running
            resetBlackboardDemo(); // Reset before starting
            ksState.ks1 = 1; // Start with KS1 active
            blackboardAnimationId = requestAnimationFrame(animateBlackboard);
        }

        function resetBlackboardDemo() {
            cancelAnimationFrame(blackboardAnimationId);
            blackboardAnimationId = null;
            ksState = { ks1: 0, ks2: 0, ks3: 0 };
            blackboardContent = [];
            drawBlackboardScene();
        }

        // Initial draw for blackboard
        drawBlackboardScene();

        // --- Pipe-Filter Architecture Canvas Demo ---
        const pipeFilterCanvas = document.getElementById('pipeFilterCanvas');
        const ctxPipeFilter = pipeFilterCanvas.getContext('2d');
        let pipeFilterAnimationId;
        let dataPacket = { x: 0, y: 0, activeFilter: 0 }; // 0: start, 1: F1, 2: F2, 3: F3, 4: end
        const filterPositions = [
            { x: 100, y: 150, width: 80, height: 60, name: '过滤器A' },
            { x: 300, y: 150, width: 80, height: 60, name: '过滤器B' },
            { x: 500, y: 150, width: 80, height: 60, name: '过滤器C' }
        ];

        function drawPipeFilterScene() {
            ctxPipeFilter.clearRect(0, 0, pipeFilterCanvas.width, pipeFilterCanvas.height);

            // Draw filters
            filterPositions.forEach((filter, index) => {
                ctxPipeFilter.fillStyle = '#1abc9c'; // Green
                ctxPipeFilter.fillRect(filter.x, filter.y, filter.width, filter.height);
                ctxPipeFilter.strokeStyle = '#16a085';
                ctxPipeFilter.lineWidth = 3;
                ctxPipeFilter.strokeRect(filter.x, filter.y, filter.width, filter.height);
                ctxPipeFilter.fillStyle = 'white';
                ctxPipeFilter.font = '16px Arial';
                ctxPipeFilter.textAlign = 'center';
                ctxPipeFilter.fillText(filter.name, filter.x + filter.width / 2, filter.y + filter.height / 2 + 5);

                // Draw pipes
                if (index < filterPositions.length - 1) {
                    const startX = filter.x + filter.width;
                    const startY = filter.y + filter.height / 2;
                    const endX = filterPositions[index + 1].x;
                    const endY = filterPositions[index + 1].y + filterPositions[index + 1].height / 2;

                    ctxPipeFilter.strokeStyle = '#34495e'; // Dark
                    ctxPipeFilter.lineWidth = 4;
                    ctxPipeFilter.beginPath();
                    ctxPipeFilter.moveTo(startX, startY);
                    ctxPipeFilter.lineTo(endX, endY);
                    ctxPipeFilter.stroke();
                    drawArrowhead(ctxPipeFilter, startX, startY, endX, endY);
                }
            });

            // Draw data packet
            if (dataPacket.activeFilter < 4) { // Only draw if not at the end
                ctxPipeFilter.fillStyle = '#e74c3c'; // Red
                ctxPipeFilter.beginPath();
                ctxPipeFilter.arc(dataPacket.x, dataPacket.y, 10, 0, Math.PI * 2);
                ctxPipeFilter.fill();
                ctxPipeFilter.fillStyle = 'white';
                ctxPipeFilter.font = '12px Arial';
                ctxPipeFilter.fillText('数据', dataPacket.x, dataPacket.y + 4);
            }
        }

        function animatePipeFilter() {
            const speed = 2; // pixels per frame

            if (dataPacket.activeFilter === 0) { // Moving from start to Filter A
                dataPacket.x += speed;
                dataPacket.y = filterPositions[0].y + filterPositions[0].height / 2;
                if (dataPacket.x >= filterPositions[0].x) {
                    dataPacket.activeFilter = 1;
                    dataPacket.x = filterPositions[0].x + filterPositions[0].width / 2; // Center in filter
                    console.log('数据进入过滤器A');
                }
            } else if (dataPacket.activeFilter === 1) { // Inside Filter A
                // Simulate processing
                setTimeout(() => {
                    dataPacket.activeFilter = 1.5; // Moving out of Filter A
                    console.log('过滤器A处理完成');
                }, 500);
            } else if (dataPacket.activeFilter === 1.5) { // Moving from Filter A to Filter B
                dataPacket.x += speed;
                if (dataPacket.x >= filterPositions[1].x) {
                    dataPacket.activeFilter = 2;
                    dataPacket.x = filterPositions[1].x + filterPositions[1].width / 2;
                    console.log('数据进入过滤器B');
                }
            } else if (dataPacket.activeFilter === 2) { // Inside Filter B
                setTimeout(() => {
                    dataPacket.activeFilter = 2.5; // Moving out of Filter B
                    console.log('过滤器B处理完成');
                }, 500);
            } else if (dataPacket.activeFilter === 2.5) { // Moving from Filter B to Filter C
                dataPacket.x += speed;
                if (dataPacket.x >= filterPositions[2].x) {
                    dataPacket.activeFilter = 3;
                    dataPacket.x = filterPositions[2].x + filterPositions[2].width / 2;
                    console.log('数据进入过滤器C');
                }
            } else if (dataPacket.activeFilter === 3) { // Inside Filter C
                setTimeout(() => {
                    dataPacket.activeFilter = 3.5; // Moving out of Filter C
                    console.log('过滤器C处理完成');
                }, 500);
            } else if (dataPacket.activeFilter === 3.5) { // Moving from Filter C to End
                dataPacket.x += speed;
                if (dataPacket.x > pipeFilterCanvas.width) {
                    dataPacket.activeFilter = 4; // Finished
                    console.log('数据处理完成并输出');
                    cancelAnimationFrame(pipeFilterAnimationId);
                    pipeFilterAnimationId = null;
                    alert("管道-过滤器架构演示完成，数据已按顺序处理并输出！");
                }
            }

            drawPipeFilterScene();
            if (dataPacket.activeFilter < 4) {
                pipeFilterAnimationId = requestAnimationFrame(animatePipeFilter);
            }
        }

        function startPipeFilterDemo() {
            if (pipeFilterAnimationId) return; // Already running
            resetPipeFilterDemo(); // Reset before starting
            dataPacket = { x: 0, y: filterPositions[0].y + filterPositions[0].height / 2, activeFilter: 0 };
            pipeFilterAnimationId = requestAnimationFrame(animatePipeFilter);
        }

        function resetPipeFilterDemo() {
            cancelAnimationFrame(pipeFilterAnimationId);
            pipeFilterAnimationId = null;
            dataPacket = { x: 0, y: filterPositions[0].y + filterPositions[0].height / 2, activeFilter: 0 };
            drawPipeFilterScene();
        }

        // Initial draw for pipe-filter
        drawPipeFilterScene();

    </script>
</body>
</html> 