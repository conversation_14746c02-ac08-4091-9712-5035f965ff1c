<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词缀学习：-tion/-sion（动作、状态、结果）</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(-30px);
            animation: fadeInDown 1s ease-out forwards;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0;
            animation: fadeInUp 1s ease-out 0.5s forwards;
        }

        .story-stage {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 500px;
            margin: 30px 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        }

        #factoryCanvas {
            width: 100%;
            height: 100%;
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
            font-size: 1.1rem;
            line-height: 1.8;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .word-factory {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .production-line {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
            cursor: pointer;
            opacity: 0;
            transform: translateY(30px);
            position: relative;
            overflow: hidden;
        }

        .production-line::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.6s;
        }

        .production-line:hover::before {
            left: 100%;
        }

        .production-line:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .production-line.active {
            opacity: 1;
            transform: translateY(0);
        }

        .verb-input {
            background: #e74c3c;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
        }

        .verb-input::after {
            content: '动词';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #c0392b;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .transformation-process {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .machine-part {
            background: #95a5a6;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            position: relative;
        }

        .machine-part::before {
            content: '⚙️';
            position: absolute;
            top: -5px;
            left: -5px;
        }

        .arrow-flow {
            font-size: 1.5rem;
            color: #3498db;
            animation: pulse 2s infinite;
        }

        .noun-output {
            background: #27ae60;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.2rem;
            text-align: center;
            position: relative;
        }

        .noun-output::after {
            content: '名词';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #229954;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        .meaning-display {
            background: rgba(52, 152, 219, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-style: italic;
            color: #2c3e50;
        }

        .example-sentence {
            background: rgba(255, 248, 220, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            font-size: 0.95rem;
            border-left: 3px solid #f39c12;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .explanation {
            background: rgba(255, 248, 220, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin: 25px 0;
            border-left: 5px solid #f39c12;
            font-size: 1.05rem;
            line-height: 1.8;
        }

        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #95a5a6;
            transition: background 0.3s;
        }

        .status-indicator.working {
            background: #e74c3c;
            animation: blink 1s infinite;
        }

        .status-indicator.complete {
            background: #27ae60;
        }

        @keyframes fadeInDown {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.2);
            }
        }

        @keyframes blink {
            0%, 50% {
                opacity: 1;
            }
            51%, 100% {
                opacity: 0.3;
            }
        }

        @keyframes conveyor {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        .interactive-hint {
            text-align: center;
            color: #3498db;
            font-size: 1rem;
            margin: 20px 0;
            opacity: 0.8;
        }

        .progress-tracker {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .progress-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #bdc3c7;
            transition: all 0.3s ease;
        }

        .progress-dot.active {
            background: #3498db;
            transform: scale(1.3);
        }

        .progress-dot.complete {
            background: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>词缀工厂：-tion/-sion</h1>
            <p>在时光机器工厂中见证动词的华丽转身</p>
        </div>

        <div class="story-stage">
            <div class="story-text">
                <h2>🏭 时光机器工厂的故事</h2>
                <p>在未来世界有一座神奇的时光机器工厂，这里专门生产"动作结果"。工厂里有两条神奇的生产线："-tion生产线"和"-sion生产线"。当动词们进入这些生产线时，它们会经历一场奇妙的变身，从表示"动作"的动词转变成表示"动作的结果或状态"的名词！</p>
            </div>

            <div class="canvas-container">
                <canvas id="factoryCanvas"></canvas>
                <div class="progress-tracker" id="progressTracker">
                    <div class="progress-dot"></div>
                    <div class="progress-dot"></div>
                    <div class="progress-dot"></div>
                    <div class="progress-dot"></div>
                </div>
            </div>

            <div class="explanation">
                <h3>🎯 为什么选择时光机器工厂的故事？</h3>
                <p><strong>教学设计理念：</strong>我选择"工厂生产线"的比喻，是因为"-tion/-sion"词缀的作用就像工业生产一样，有着清晰的"输入-处理-输出"过程。动词（原材料）经过词缀生产线的"加工"，变成名词（成品）。这个过程体现了语言的"制造"特性，让抽象的词性转换变得具体可感。工厂的机械感也暗示了这种转换的规律性和可预测性。</p>
            </div>

            <div class="controls">
                <button class="btn" onclick="startProduction()">启动生产线</button>
                <button class="btn" onclick="showProducts()">展示产品</button>
                <button class="btn" onclick="resetFactory()">重置工厂</button>
            </div>

            <div class="interactive-hint">
                🔧 点击"启动生产线"观看动词变身过程，点击产品卡片查看详细说明
            </div>
        </div>

        <div class="word-factory" id="productionLines">
            <div class="production-line">
                <div class="status-indicator"></div>
                <div class="verb-input">create</div>
                <div class="transformation-process">
                    <span class="machine-part">-tion生产线</span>
                    <span class="arrow-flow">→</span>
                    <div class="noun-output">creation</div>
                </div>
                <div class="meaning-display">创造 + 动作结果 = 创造、创作</div>
                <div class="example-sentence">
                    <strong>例句：</strong>The creation of this artwork took months.<br>
                    <strong>翻译：</strong>这件艺术品的创作花了几个月时间。<br>
                    <strong>解析：</strong>"create"是动词"创造"，加上"-tion"变成名词"creation"，表示"创造的行为或结果"。
                </div>
            </div>

            <div class="production-line">
                <div class="status-indicator"></div>
                <div class="verb-input">decide</div>
                <div class="transformation-process">
                    <span class="machine-part">-sion生产线</span>
                    <span class="arrow-flow">→</span>
                    <div class="noun-output">decision</div>
                </div>
                <div class="meaning-display">决定 + 动作结果 = 决定、决策</div>
                <div class="example-sentence">
                    <strong>例句：</strong>Making this decision was difficult.<br>
                    <strong>翻译：</strong>做出这个决定很困难。<br>
                    <strong>解析：</strong>"decide"是动词"决定"，加上"-sion"变成名词"decision"，表示"决定的行为或结果"。
                </div>
            </div>

            <div class="production-line">
                <div class="status-indicator"></div>
                <div class="verb-input">educate</div>
                <div class="transformation-process">
                    <span class="machine-part">-tion生产线</span>
                    <span class="arrow-flow">→</span>
                    <div class="noun-output">education</div>
                </div>
                <div class="meaning-display">教育 + 动作结果 = 教育</div>
                <div class="example-sentence">
                    <strong>例句：</strong>Education is very important.<br>
                    <strong>翻译：</strong>教育非常重要。<br>
                    <strong>解析：</strong>"educate"是动词"教育"，加上"-tion"变成名词"education"，表示"教育的过程或系统"。
                </div>
            </div>

            <div class="production-line">
                <div class="status-indicator"></div>
                <div class="verb-input">express</div>
                <div class="transformation-process">
                    <span class="machine-part">-sion生产线</span>
                    <span class="arrow-flow">→</span>
                    <div class="noun-output">expression</div>
                </div>
                <div class="meaning-display">表达 + 动作结果 = 表达、表情</div>
                <div class="example-sentence">
                    <strong>例句：</strong>His facial expression showed surprise.<br>
                    <strong>翻译：</strong>他的面部表情显示出惊讶。<br>
                    <strong>解析：</strong>"express"是动词"表达"，加上"-sion"变成名词"expression"，表示"表达的方式或结果"。
                </div>
            </div>
        </div>

        <div class="explanation">
            <h3>🧠 翻译技巧总结</h3>
            <p><strong>识别规律：</strong>"-tion/-sion"词缀将动词转换为名词，表示动作的过程、结果或状态。</p>
            <p><strong>翻译步骤：</strong></p>
            <ol style="margin-left: 20px; margin-top: 10px;">
                <li><strong>识别动词词根：</strong>去掉"-tion/-sion"后缀，找到原始动词</li>
                <li><strong>理解动作含义：</strong>明确动词的基本意思</li>
                <li><strong>转换为名词概念：</strong>将动作转换为"...的行为/过程/结果"</li>
                <li><strong>选择合适表达：</strong>根据语境选择最自然的中文表达</li>
            </ol>
            <p><strong>记忆技巧：</strong>想象工厂生产线，动词是原材料，经过加工变成表示结果的名词产品！</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('factoryCanvas');
        const ctx = canvas.getContext('2d');
        
        // 设置canvas尺寸
        function resizeCanvas() {
            canvas.width = canvas.offsetWidth;
            canvas.height = canvas.offsetHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 动画状态
        let animationState = 'idle';
        let currentLine = 0;
        let conveyorBelt = { x: 0, speed: 2 };
        let productionLines = [
            { verb: 'create', suffix: '-tion', noun: 'creation', processed: false },
            { verb: 'decide', suffix: '-sion', noun: 'decision', processed: false },
            { verb: 'educate', suffix: '-tion', noun: 'education', processed: false },
            { verb: 'express', suffix: '-sion', noun: 'expression', processed: false }
        ];

        function drawFactory() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制工厂背景
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#ecf0f1');
            gradient.addColorStop(1, '#bdc3c7');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制传送带
            drawConveyorBelt();
            
            // 绘制生产机器
            drawProductionMachine();
            
            // 绘制词汇处理过程
            if (animationState === 'producing') {
                drawWordProcessing();
            }
        }

        function drawConveyorBelt() {
            // 传送带轨道
            ctx.fillStyle = '#34495e';
            ctx.fillRect(0, canvas.height - 100, canvas.width, 20);
            ctx.fillRect(0, canvas.height - 40, canvas.width, 20);
            
            // 传送带移动效果
            ctx.fillStyle = '#2c3e50';
            for (let i = conveyorBelt.x; i < canvas.width + 50; i += 50) {
                ctx.fillRect(i, canvas.height - 95, 30, 10);
                ctx.fillRect(i, canvas.height - 35, 30, 10);
            }
            
            if (animationState === 'producing') {
                conveyorBelt.x -= conveyorBelt.speed;
                if (conveyorBelt.x <= -50) {
                    conveyorBelt.x = 0;
                }
            }
        }

        function drawProductionMachine() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 主机器体
            ctx.fillStyle = '#7f8c8d';
            ctx.fillRect(centerX - 100, centerY - 80, 200, 120);
            
            // 机器顶部
            ctx.fillStyle = '#95a5a6';
            ctx.fillRect(centerX - 120, centerY - 100, 240, 20);
            
            // 控制面板
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(centerX - 80, centerY - 60, 160, 80);
            
            // 指示灯
            const lightColor = animationState === 'producing' ? '#e74c3c' : '#27ae60';
            ctx.fillStyle = lightColor;
            ctx.beginPath();
            ctx.arc(centerX - 60, centerY - 40, 8, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#f39c12';
            ctx.beginPath();
            ctx.arc(centerX, centerY - 40, 8, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#3498db';
            ctx.beginPath();
            ctx.arc(centerX + 60, centerY - 40, 8, 0, Math.PI * 2);
            ctx.fill();
            
            // 机器标签
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('-tion/-sion', centerX, centerY - 10);
            ctx.fillText('生产线', centerX, centerY + 10);
        }

        function drawWordProcessing() {
            if (currentLine < productionLines.length) {
                const line = productionLines[currentLine];
                const centerX = canvas.width / 2;
                
                // 输入词汇
                ctx.fillStyle = '#e74c3c';
                ctx.fillRect(centerX - 200, canvas.height - 80, 80, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(line.verb, centerX - 160, canvas.height - 60);
                
                // 处理中效果
                if (line.processed) {
                    // 输出词汇
                    ctx.fillStyle = '#27ae60';
                    ctx.fillRect(centerX + 120, canvas.height - 80, 100, 30);
                    ctx.fillStyle = 'white';
                    ctx.fillText(line.noun, centerX + 170, canvas.height - 60);
                    
                    // 连接线
                    ctx.strokeStyle = '#3498db';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(centerX - 120, canvas.height - 65);
                    ctx.lineTo(centerX + 120, canvas.height - 65);
                    ctx.stroke();
                    
                    // 箭头
                    ctx.fillStyle = '#3498db';
                    ctx.beginPath();
                    ctx.moveTo(centerX + 110, canvas.height - 70);
                    ctx.lineTo(centerX + 120, canvas.height - 65);
                    ctx.lineTo(centerX + 110, canvas.height - 60);
                    ctx.closePath();
                    ctx.fill();
                }
            }
        }

        function updateProgressTracker() {
            const dots = document.querySelectorAll('.progress-dot');
            dots.forEach((dot, index) => {
                dot.classList.remove('active', 'complete');
                if (index < currentLine) {
                    dot.classList.add('complete');
                } else if (index === currentLine) {
                    dot.classList.add('active');
                }
            });
        }

        function updateProductionLineStatus() {
            const lines = document.querySelectorAll('.production-line');
            const indicators = document.querySelectorAll('.status-indicator');
            
            lines.forEach((line, index) => {
                const indicator = indicators[index];
                if (index < currentLine) {
                    indicator.classList.remove('working');
                    indicator.classList.add('complete');
                } else if (index === currentLine && animationState === 'producing') {
                    indicator.classList.add('working');
                    indicator.classList.remove('complete');
                } else {
                    indicator.classList.remove('working', 'complete');
                }
            });
        }

        function animate() {
            drawFactory();
            updateProgressTracker();
            updateProductionLineStatus();
            
            if (animationState === 'producing' && currentLine < productionLines.length) {
                // 模拟处理时间
                setTimeout(() => {
                    productionLines[currentLine].processed = true;
                    setTimeout(() => {
                        currentLine++;
                        if (currentLine >= productionLines.length) {
                            animationState = 'completed';
                        }
                    }, 1000);
                }, 2000);
            }
            
            requestAnimationFrame(animate);
        }

        function startProduction() {
            animationState = 'producing';
            currentLine = 0;
            productionLines.forEach(line => line.processed = false);
            conveyorBelt.x = 0;
        }

        function showProducts() {
            const lines = document.querySelectorAll('.production-line');
            lines.forEach((line, index) => {
                setTimeout(() => {
                    line.classList.add('active');
                }, index * 400);
            });
        }

        function resetFactory() {
            animationState = 'idle';
            currentLine = 0;
            productionLines.forEach(line => line.processed = false);
            conveyorBelt.x = 0;
            
            const lines = document.querySelectorAll('.production-line');
            lines.forEach(line => line.classList.remove('active'));
            
            const indicators = document.querySelectorAll('.status-indicator');
            indicators.forEach(indicator => {
                indicator.classList.remove('working', 'complete');
            });
        }

        // 初始化
        animate();

        // 点击生产线卡片的交互
        document.querySelectorAll('.production-line').forEach(line => {
            line.addEventListener('click', function() {
                this.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });
    </script>
</body>
</html>
