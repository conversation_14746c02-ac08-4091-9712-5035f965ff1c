<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>面向对象分析教学</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .question {
            background-color: #e8f4f8;
            padding: 15px;
            border-left: 5px solid #3498db;
            margin-bottom: 20px;
        }
        .explanation {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .animation-container {
            position: relative;
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        canvas {
            background-color: white;
        }
        .controls {
            display: flex;
            justify-content: center;
            margin: 15px 0;
            gap: 10px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .step {
            display: none;
            margin-top: 10px;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        .step.active {
            display: block;
        }
        .options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        .option {
            padding: 10px 15px;
            background-color: #f1f1f1;
            border: 2px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .option:hover {
            background-color: #e9e9e9;
        }
        .option.selected {
            border-color: #3498db;
            background-color: #e8f4f8;
        }
        .option.correct {
            border-color: #2ecc71;
            background-color: #e8f8e8;
        }
        .feedback {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .feedback.correct {
            display: block;
            background-color: #d4edda;
            color: #155724;
        }
        .feedback.incorrect {
            display: block;
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>面向对象分析教学</h1>
        
        <div class="question">
            <h2>题目：</h2>
            <p>在面向对象分析中，利用（）表示需求，并从中提炼出中（），以上两者形成（），之后再进行后续的开发工作。</p>
            <div class="options">
                <div class="option" data-option="A">类图</div>
                <div class="option" data-option="B">数据流图</div>
                <div class="option" data-option="C">包图</div>
                <div class="option" data-option="D">用例与用例图</div>
            </div>
            <div class="feedback" id="feedback"></div>
        </div>

        <div class="explanation">
            <h2>知识点解释：</h2>
            <p>面向对象分析是软件开发的重要阶段，它帮助我们理解问题域并创建系统模型。这个过程包含几个关键步骤：</p>
            <ul>
                <li><strong>用例与用例图</strong>：捕获系统需求，描述用户与系统的交互</li>
                <li><strong>对象/类</strong>：从用例中提炼出系统中的核心对象和类</li>
                <li><strong>领域模型</strong>：用例和对象/类共同形成领域模型</li>
            </ul>
        </div>
        
        <div class="animation-container">
            <canvas id="animationCanvas" width="1000" height="400"></canvas>
            <div class="controls">
                <button id="prevStep">上一步</button>
                <button id="nextStep">下一步</button>
                <button id="resetAnimation">重置</button>
            </div>
            <div class="steps-container">
                <div class="step" data-step="1">
                    <h3>第1步：用例图表示需求</h3>
                    <p>用例图捕获系统的功能需求，描述用户（角色）与系统之间的交互。</p>
                </div>
                <div class="step" data-step="2">
                    <h3>第2步：从用例中提炼对象/类</h3>
                    <p>分析用例描述，识别名词和动词，提取可能的对象和类。</p>
                </div>
                <div class="step" data-step="3">
                    <h3>第3步：形成领域模型</h3>
                    <p>用例和对象/类共同形成领域模型，这是系统的概念模型。</p>
                </div>
                <div class="step" data-step="4">
                    <h3>第4步：进行后续开发</h3>
                    <p>基于领域模型进行详细设计、编码和测试等后续工作。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        let currentStep = 1;
        const totalSteps = 4;
        
        // 动画对象
        const animation = {
            // 用例图元素
            actors: [],
            useCases: [],
            connections: [],
            
            // 类/对象元素
            classes: [],
            
            // 领域模型元素
            domainModel: null,
            
            // 开发阶段元素
            developmentStages: [],
            
            // 初始化动画
            init: function() {
                // 创建角色
                this.actors = [
                    { x: 100, y: 150, name: "用户", radius: 20, visible: false }
                ];
                
                // 创建用例
                this.useCases = [
                    { x: 250, y: 120, name: "登录系统", width: 120, height: 60, visible: false },
                    { x: 250, y: 220, name: "查看信息", width: 120, height: 60, visible: false }
                ];
                
                // 创建连接
                this.connections = [
                    { from: this.actors[0], to: this.useCases[0], visible: false },
                    { from: this.actors[0], to: this.useCases[1], visible: false }
                ];
                
                // 创建类
                this.classes = [
                    { x: 500, y: 120, name: "用户", attributes: ["用户名", "密码"], methods: ["登录()"], width: 120, height: 100, visible: false },
                    { x: 500, y: 250, name: "信息", attributes: ["标题", "内容"], methods: ["显示()"], width: 120, height: 100, visible: false }
                ];
                
                // 创建领域模型
                this.domainModel = { x: 350, y: 200, width: 300, height: 200, visible: false };
                
                // 创建开发阶段
                this.developmentStages = [
                    { x: 700, y: 100, name: "详细设计", visible: false },
                    { x: 700, y: 180, name: "编码实现", visible: false },
                    { x: 700, y: 260, name: "测试部署", visible: false }
                ];
                
                this.draw();
            },
            
            // 绘制动画
            draw: function() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制标题
                ctx.font = "bold 18px Arial";
                ctx.fillStyle = "#2c3e50";
                ctx.textAlign = "center";
                ctx.fillText("面向对象分析过程", canvas.width / 2, 30);
                
                // 绘制步骤标题
                ctx.font = "16px Arial";
                ctx.fillStyle = "#3498db";
                
                if (currentStep >= 1) {
                    ctx.fillText("1. 用例图表示需求", 200, 60);
                }
                
                if (currentStep >= 2) {
                    ctx.fillText("2. 从用例中提炼对象/类", 500, 60);
                }
                
                if (currentStep >= 3) {
                    ctx.fillText("3. 形成领域模型", 350, 350);
                }
                
                if (currentStep >= 4) {
                    ctx.fillText("4. 进行后续开发", 800, 60);
                }
                
                // 绘制用例图元素（第1步）
                if (currentStep >= 1) {
                    // 绘制角色
                    this.actors.forEach(actor => {
                        if (actor.visible || currentStep >= 1) {
                            // 绘制小人图标
                            ctx.beginPath();
                            ctx.arc(actor.x, actor.y - 15, actor.radius, 0, Math.PI * 2);
                            ctx.stroke();
                            
                            // 绘制身体
                            ctx.beginPath();
                            ctx.moveTo(actor.x, actor.y - 15 + actor.radius);
                            ctx.lineTo(actor.x, actor.y + 20);
                            ctx.stroke();
                            
                            // 绘制手臂
                            ctx.beginPath();
                            ctx.moveTo(actor.x - 15, actor.y);
                            ctx.lineTo(actor.x + 15, actor.y);
                            ctx.stroke();
                            
                            // 绘制腿
                            ctx.beginPath();
                            ctx.moveTo(actor.x, actor.y + 20);
                            ctx.lineTo(actor.x - 10, actor.y + 40);
                            ctx.stroke();
                            
                            ctx.beginPath();
                            ctx.moveTo(actor.x, actor.y + 20);
                            ctx.lineTo(actor.x + 10, actor.y + 40);
                            ctx.stroke();
                            
                            // 绘制名称
                            ctx.font = "12px Arial";
                            ctx.fillStyle = "#000";
                            ctx.textAlign = "center";
                            ctx.fillText(actor.name, actor.x, actor.y + 55);
                        }
                    });
                    
                    // 绘制用例
                    this.useCases.forEach(useCase => {
                        if (useCase.visible || currentStep >= 1) {
                            ctx.beginPath();
                            ctx.ellipse(useCase.x, useCase.y, useCase.width / 2, useCase.height / 2, 0, 0, Math.PI * 2);
                            ctx.stroke();
                            
                            ctx.font = "12px Arial";
                            ctx.fillStyle = "#000";
                            ctx.textAlign = "center";
                            ctx.fillText(useCase.name, useCase.x, useCase.y);
                        }
                    });
                    
                    // 绘制连接线
                    this.connections.forEach(conn => {
                        if (conn.visible || currentStep >= 1) {
                            ctx.beginPath();
                            ctx.moveTo(conn.from.x + conn.from.radius, conn.from.y);
                            ctx.lineTo(conn.to.x - conn.to.width / 2, conn.to.y);
                            ctx.stroke();
                        }
                    });
                }
                
                // 绘制类图元素（第2步）
                if (currentStep >= 2) {
                    this.classes.forEach(cls => {
                        if (cls.visible || currentStep >= 2) {
                            // 绘制类框
                            ctx.beginPath();
                            ctx.rect(cls.x - cls.width / 2, cls.y - cls.height / 2, cls.width, cls.height);
                            ctx.stroke();
                            
                            // 绘制类名
                            ctx.font = "bold 12px Arial";
                            ctx.fillStyle = "#000";
                            ctx.textAlign = "center";
                            ctx.fillText(cls.name, cls.x, cls.y - cls.height / 2 + 15);
                            
                            // 绘制分隔线
                            ctx.beginPath();
                            ctx.moveTo(cls.x - cls.width / 2, cls.y - cls.height / 2 + 25);
                            ctx.lineTo(cls.x + cls.width / 2, cls.y - cls.height / 2 + 25);
                            ctx.stroke();
                            
                            // 绘制属性
                            ctx.font = "12px Arial";
                            cls.attributes.forEach((attr, index) => {
                                ctx.fillText(attr, cls.x, cls.y - cls.height / 2 + 40 + index * 15);
                            });
                            
                            // 绘制第二条分隔线
                            const attrHeight = 25 + cls.attributes.length * 15;
                            ctx.beginPath();
                            ctx.moveTo(cls.x - cls.width / 2, cls.y - cls.height / 2 + attrHeight);
                            ctx.lineTo(cls.x + cls.width / 2, cls.y - cls.height / 2 + attrHeight);
                            ctx.stroke();
                            
                            // 绘制方法
                            cls.methods.forEach((method, index) => {
                                ctx.fillText(method, cls.x, cls.y - cls.height / 2 + attrHeight + 15 + index * 15);
                            });
                        }
                    });
                    
                    // 绘制提炼箭头
                    if (currentStep === 2) {
                        ctx.beginPath();
                        ctx.moveTo(320, 150);
                        ctx.lineTo(420, 150);
                        ctx.lineTo(420, 120);
                        ctx.lineTo(450, 120);
                        ctx.stroke();
                        
                        ctx.beginPath();
                        ctx.moveTo(445, 115);
                        ctx.lineTo(450, 120);
                        ctx.lineTo(445, 125);
                        ctx.stroke();
                        
                        ctx.beginPath();
                        ctx.moveTo(320, 220);
                        ctx.lineTo(420, 220);
                        ctx.lineTo(420, 250);
                        ctx.lineTo(450, 250);
                        ctx.stroke();
                        
                        ctx.beginPath();
                        ctx.moveTo(445, 245);
                        ctx.lineTo(450, 250);
                        ctx.lineTo(445, 255);
                        ctx.stroke();
                    }
                }
                
                // 绘制领域模型（第3步）
                if (currentStep >= 3) {
                    // 绘制领域模型框
                    ctx.beginPath();
                    ctx.rect(this.domainModel.x - this.domainModel.width / 2, this.domainModel.y - this.domainModel.height / 2, 
                             this.domainModel.width, this.domainModel.height);
                    ctx.setLineDash([5, 3]);
                    ctx.strokeStyle = "#3498db";
                    ctx.stroke();
                    ctx.setLineDash([]);
                    ctx.strokeStyle = "#000";
                    
                    // 绘制领域模型标签
                    ctx.font = "bold 14px Arial";
                    ctx.fillStyle = "#3498db";
                    ctx.textAlign = "center";
                    ctx.fillText("领域模型", this.domainModel.x, this.domainModel.y - this.domainModel.height / 2 - 10);
                    
                    // 绘制用例和类之间的关系
                    ctx.beginPath();
                    ctx.moveTo(250, 150);
                    ctx.quadraticCurveTo(350, 180, 500, 150);
                    ctx.stroke();
                    
                    ctx.beginPath();
                    ctx.moveTo(250, 250);
                    ctx.quadraticCurveTo(350, 220, 500, 250);
                    ctx.stroke();
                }
                
                // 绘制开发阶段（第4步）
                if (currentStep >= 4) {
                    // 绘制从领域模型到开发阶段的箭头
                    ctx.beginPath();
                    ctx.moveTo(500, 200);
                    ctx.lineTo(650, 200);
                    ctx.stroke();
                    
                    ctx.beginPath();
                    ctx.moveTo(645, 195);
                    ctx.lineTo(650, 200);
                    ctx.lineTo(645, 205);
                    ctx.stroke();
                    
                    // 绘制开发阶段
                    this.developmentStages.forEach(stage => {
                        if (stage.visible || currentStep >= 4) {
                            ctx.beginPath();
                            ctx.rect(stage.x, stage.y, 150, 40);
                            ctx.stroke();
                            
                            ctx.font = "12px Arial";
                            ctx.fillStyle = "#000";
                            ctx.textAlign = "center";
                            ctx.fillText(stage.name, stage.x + 75, stage.y + 25);
                            
                            if (stage !== this.developmentStages[this.developmentStages.length - 1]) {
                                ctx.beginPath();
                                ctx.moveTo(stage.x + 75, stage.y + 40);
                                ctx.lineTo(stage.x + 75, stage.y + 60);
                                ctx.stroke();
                                
                                ctx.beginPath();
                                ctx.moveTo(stage.x + 70, stage.y + 55);
                                ctx.lineTo(stage.x + 75, stage.y + 60);
                                ctx.lineTo(stage.x + 80, stage.y + 55);
                                ctx.stroke();
                            }
                        }
                    });
                }
            },
            
            // 更新动画状态
            updateStep: function(step) {
                currentStep = step;
                if (currentStep < 1) currentStep = 1;
                if (currentStep > totalSteps) currentStep = totalSteps;
                
                // 更新步骤说明
                document.querySelectorAll('.step').forEach(el => {
                    el.classList.remove('active');
                });
                document.querySelector(`.step[data-step="${currentStep}"]`).classList.add('active');
                
                this.draw();
            }
        };
        
        // 初始化动画
        animation.init();
        document.querySelector(`.step[data-step="1"]`).classList.add('active');
        
        // 按钮事件处理
        document.getElementById('prevStep').addEventListener('click', function() {
            animation.updateStep(currentStep - 1);
        });
        
        document.getElementById('nextStep').addEventListener('click', function() {
            animation.updateStep(currentStep + 1);
        });
        
        document.getElementById('resetAnimation').addEventListener('click', function() {
            animation.updateStep(1);
        });
        
        // 选项点击事件
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('selected');
                });
                this.classList.add('selected');
                
                const selectedOption = this.getAttribute('data-option');
                const feedback = document.getElementById('feedback');
                
                if (selectedOption === 'D') {
                    feedback.textContent = '正确！在面向对象分析中，我们使用用例与用例图表示需求，从中提炼出对象/类，两者共同形成领域模型。';
                    feedback.className = 'feedback correct';
                    this.classList.add('correct');
                } else {
                    feedback.textContent = '不正确，请再试一次。提示：考虑哪种图表最适合表示用户需求。';
                    feedback.className = 'feedback incorrect';
                }
            });
        });
        
        // 响应式调整
        function resizeCanvas() {
            const container = document.querySelector('.animation-container');
            canvas.width = container.clientWidth;
            animation.draw();
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
    </script>
</body>
</html> 