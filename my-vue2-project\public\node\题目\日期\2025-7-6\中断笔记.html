<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阅读辅助工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        h1, h2 {
            color: #0056b3;
            text-align: center;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
            resize: vertical;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
            align-self: flex-end; /* Align button to the right */
        }
        button:hover {
            background-color: #0056b3;
        }
        .basket {
            border: 1px solid #ccc;
            padding: 15px;
            min-height: 100px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
        .basket-item {
            background-color: #fff;
            border: 1px solid #dee2e6;
            padding: 8px;
            margin-bottom: 5px;
            border-radius: 3px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .basket-item:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>阅读辅助工具</h1>
        <p>在这里输入您想阅读的文本，系统将模拟评估其难度，并将识别出的“困难知识点”放入下方的“篮筐”中。</p>

        <textarea id="readingText" placeholder="请输入您的文本..."></textarea>
        <button id="evaluateButton">评估阅读难度</button>

        <h2>困难知识点篮筐 (堆栈)</h2>
        <div id="knowledgeBasket" class="basket">
            <p>篮筐中暂无内容。</p>
        </div>
    </div>

    <script>
        const readingTextElement = document.getElementById('readingText');
        const evaluateButton = document.getElementById('evaluateButton');
        const knowledgeBasketElement = document.getElementById('knowledgeBasket');

        // 模拟的困难词汇或知识点列表
        const difficultWords = ['宏观经济', '微观经济', '量子力学', '神经网络', '算法复杂度', '数据结构', '异步编程', '递归', '高阶函数', '并发'];

        let basketItems = []; // 用于存储困难知识点的数组，模拟堆栈

        function evaluateText() {
            const text = readingTextElement.value;
            knowledgeBasketElement.innerHTML = ''; // 清空之前的篮筐内容
            basketItems = []; // 重置篮筐

            if (text.trim() === '') {
                knowledgeBasketElement.innerHTML = '<p>篮筐中暂无内容。</p>';
                return;
            }

            let foundDifficulties = false;

            difficultWords.forEach(word => {
                if (text.includes(word)) {
                    basketItems.push(word); // 将困难词汇推入篮筐
                    foundDifficulties = true;
                }
            });

            if (basketItems.length > 0) {
                basketItems.reverse().forEach(item => { // 反转数组以模拟堆栈的LIFO（后进先出）显示
                    const itemDiv = document.createElement('div');
                    itemDiv.classList.add('basket-item');
                    itemDiv.textContent = item;
                    knowledgeBasketElement.appendChild(itemDiv);
                });
            } else {
                knowledgeBasketElement.innerHTML = '<p>当前文本没有发现阅读困难。</p>';
            }
        }

        evaluateButton.addEventListener('click', evaluateText);
    </script>
</body>
</html> 