<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trans- 词缀故事：穿越时空的魔法师</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInDown 1s ease-out;
        }

        .subtitle {
            text-align: center;
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            margin-bottom: 50px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .story-canvas {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            overflow: hidden;
            animation: slideInUp 1s ease-out 0.6s both;
        }

        canvas {
            display: block;
            width: 100%;
            height: 400px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
            animation: fadeIn 1s ease-out 1s both;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            animation: slideInUp 1s ease-out 0.9s both;
        }

        .word-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translateX(-20px);
            opacity: 0;
        }

        .word-card.show {
            transform: translateX(0);
            opacity: 1;
        }

        .word-card:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .prefix {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff6b6b;
        }

        .meaning {
            font-size: 1.1rem;
            color: #333;
            margin-top: 10px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
            50% { box-shadow: 0 5px 25px rgba(102, 126, 234, 0.4); }
        }

        .btn:hover {
            transform: translateY(-3px);
            animation: glow 2s infinite;
        }

        .word-card:hover {
            animation: pulse 1s infinite;
        }

        .quiz-btn:hover {
            animation: bounce 0.6s ease;
        }

        .interactive-area {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .quiz-btn {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-btn:hover {
            transform: scale(1.05);
        }

        .correct {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf) !important;
        }

        .wrong {
            background: linear-gradient(45deg, #ff416c, #ff4b2b) !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">Trans- 词缀魔法课堂</h1>
        <p class="subtitle">跟随魔法师学习"穿越、转换"的神奇力量</p>
        
        <div class="story-canvas">
            <canvas id="storyCanvas" width="800" height="400"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="startStory()">开始故事</button>
            <button class="btn" onclick="nextScene()">下一幕</button>
            <button class="btn" onclick="resetStory()">重新开始</button>
        </div>

        <div class="explanation">
            <h2 style="color: #333; margin-bottom: 20px;">为什么选择"穿越魔法师"的故事？</h2>
            <p style="color: #666; line-height: 1.8; font-size: 1.1rem;">
                我选择用"穿越魔法师"来讲解 <span class="prefix">trans-</span> 词缀，是因为这个词缀的核心含义就是"穿越、跨越、转换"。
                通过魔法师在不同场景间穿越的故事，你可以直观地理解这个词缀的含义。每当魔法师使用魔法时，
                都体现了"trans-"的转换特性，让抽象的语法概念变得生动有趣。
            </p>
            
            <div id="wordCards">
                <!-- 词汇卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="interactive-area">
            <h3 style="color: #333; margin-bottom: 20px;">互动测试：选择正确的翻译</h3>
            <div id="quizArea">
                <!-- 测试题目将通过JavaScript生成 -->
            </div>

            <div style="margin-top: 30px; padding-top: 30px; border-top: 2px solid #eee;">
                <h3 style="color: #333; margin-bottom: 20px;">语音练习</h3>
                <p style="color: #666; margin-bottom: 15px;">点击下面的按钮听发音，然后跟读：</p>
                <div id="pronunciationArea" style="display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
                    <!-- 发音按钮将通过JavaScript生成 -->
                </div>
            </div>

            <div style="margin-top: 30px; padding-top: 30px; border-top: 2px solid #eee;">
                <h3 style="color: #333; margin-bottom: 20px;">记忆小贴士</h3>
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                           color: white; padding: 20px; border-radius: 15px; line-height: 1.8;">
                    <p><strong>记忆技巧：</strong></p>
                    <p>🔄 <strong>trans-</strong> = "穿越、转换"</p>
                    <p>💡 想象一个魔法师用魔法棒"穿越"不同的世界</p>
                    <p>📝 每次看到 trans- 开头的单词，就想到"从一个状态到另一个状态"</p>
                    <p>🎯 练习方法：看到新单词时，先找出词缀，再猜测意思</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        let currentScene = 0;
        let animationFrame = 0;

        // 故事场景数据
        const scenes = [
            {
                title: "魔法师的传送门",
                description: "魔法师正在准备使用传送魔法 (transport)",
                words: ["transport", "transform", "translate"]
            },
            {
                title: "穿越时空",
                description: "魔法师成功穿越到了另一个世界 (transfer)",
                words: ["transfer", "transmit", "transparent"]
            },
            {
                title: "魔法转换",
                description: "魔法师将石头转换成了黄金 (transform)",
                words: ["transaction", "transition", "transplant"]
            }
        ];

        // 词汇数据
        const vocabulary = [
            {
                word: "transport",
                prefix: "trans-",
                root: "port (携带)",
                meaning: "运输、传送",
                explanation: "trans(穿越) + port(携带) = 把东西从一个地方携带到另一个地方"
            },
            {
                word: "transform",
                prefix: "trans-",
                root: "form (形状)",
                meaning: "转换、变形",
                explanation: "trans(转换) + form(形状) = 改变形状或性质"
            },
            {
                word: "translate",
                prefix: "trans-",
                root: "late (携带)",
                meaning: "翻译、转换语言",
                explanation: "trans(转换) + late(携带) = 把一种语言转换成另一种语言"
            }
        ];

        function startStory() {
            currentScene = 0;
            animationFrame = 0;
            drawScene();
            showWordCards();
        }

        function nextScene() {
            if (currentScene < scenes.length - 1) {
                currentScene++;
                animationFrame = 0;
                drawScene();
                updateWordCards();
            }
        }

        function resetStory() {
            currentScene = 0;
            animationFrame = 0;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('wordCards').innerHTML = '';
        }

        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制魔法师
            drawWizard();
            
            // 绘制场景特效
            drawSceneEffects();
            
            // 绘制文字说明
            drawSceneText();
            
            animationFrame++;
            requestAnimationFrame(drawScene);
        }

        function drawWizard() {
            const x = 100 + Math.sin(animationFrame * 0.05) * 10;
            const y = 200;
            
            // 魔法师身体
            ctx.fillStyle = '#4a4a4a';
            ctx.fillRect(x - 15, y, 30, 80);
            
            // 魔法师头部
            ctx.fillStyle = '#fdbcb4';
            ctx.beginPath();
            ctx.arc(x, y - 10, 20, 0, Math.PI * 2);
            ctx.fill();
            
            // 魔法师帽子
            ctx.fillStyle = '#2c3e50';
            ctx.beginPath();
            ctx.moveTo(x - 25, y - 10);
            ctx.lineTo(x, y - 50);
            ctx.lineTo(x + 25, y - 10);
            ctx.fill();
            
            // 魔法棒
            ctx.strokeStyle = '#8b4513';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(x + 20, y + 20);
            ctx.lineTo(x + 40, y - 10);
            ctx.stroke();
            
            // 魔法星星
            drawStar(x + 40, y - 10, 5, '#ffd700');
        }

        function drawStar(x, y, size, color) {
            ctx.fillStyle = color;
            ctx.beginPath();
            for (let i = 0; i < 5; i++) {
                const angle = (i * 4 * Math.PI) / 5;
                const px = x + Math.cos(angle) * size;
                const py = y + Math.sin(angle) * size;
                if (i === 0) ctx.moveTo(px, py);
                else ctx.lineTo(px, py);
            }
            ctx.closePath();
            ctx.fill();
        }

        function drawSceneEffects() {
            switch(currentScene) {
                case 0:
                    // 传送门效果
                    drawPortal(600, 200);
                    break;
                case 1:
                    // 穿越效果
                    drawTransferEffect();
                    break;
                case 2:
                    // 转换效果
                    drawTransformEffect();
                    break;
            }
        }

        function drawPortal(x, y) {
            const radius = 50 + Math.sin(animationFrame * 0.1) * 10;
            
            // 外圈
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 5;
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.stroke();
            
            // 内圈
            ctx.strokeStyle = '#4ecdc4';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(x, y, radius - 15, 0, Math.PI * 2);
            ctx.stroke();
            
            // 中心光点
            ctx.fillStyle = '#ffd700';
            ctx.beginPath();
            ctx.arc(x, y, 5, 0, Math.PI * 2);
            ctx.fill();
        }

        function drawTransferEffect() {
            // 粒子效果
            for (let i = 0; i < 20; i++) {
                const x = 200 + i * 20 + Math.sin(animationFrame * 0.1 + i) * 10;
                const y = 200 + Math.cos(animationFrame * 0.1 + i) * 20;
                
                ctx.fillStyle = `hsl(${(animationFrame + i * 10) % 360}, 70%, 60%)`;
                ctx.beginPath();
                ctx.arc(x, y, 3, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        function drawTransformEffect() {
            // 变换光环
            const x = 500;
            const y = 200;
            
            for (let i = 0; i < 3; i++) {
                const radius = 30 + i * 20 + Math.sin(animationFrame * 0.1) * 5;
                ctx.strokeStyle = `rgba(255, 107, 107, ${0.7 - i * 0.2})`;
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, Math.PI * 2);
                ctx.stroke();
            }
        }

        function drawSceneText() {
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(scenes[currentScene].title, canvas.width / 2, 50);
            
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText(scenes[currentScene].description, canvas.width / 2, 80);
        }

        function showWordCards() {
            const container = document.getElementById('wordCards');
            container.innerHTML = '';
            
            vocabulary.forEach((item, index) => {
                setTimeout(() => {
                    const card = document.createElement('div');
                    card.className = 'word-card';
                    card.innerHTML = `
                        <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 10px;">
                            <span style="color: #ff6b6b;">${item.prefix}</span>${item.word.replace(item.prefix.replace('-', ''), '')}
                        </div>
                        <div style="font-size: 1.1rem; margin-bottom: 5px;">${item.meaning}</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">${item.explanation}</div>
                    `;
                    
                    card.addEventListener('click', () => {
                        speakWord(item.word);
                    });
                    
                    container.appendChild(card);
                    
                    setTimeout(() => {
                        card.classList.add('show');
                    }, 100);
                }, index * 300);
            });
        }

        function updateWordCards() {
            // 更新当前场景相关的词汇高亮
            const cards = document.querySelectorAll('.word-card');
            cards.forEach(card => {
                card.style.opacity = '0.6';
            });
            
            // 高亮当前场景的词汇
            const currentWords = scenes[currentScene].words;
            cards.forEach((card, index) => {
                if (currentWords.includes(vocabulary[index].word)) {
                    card.style.opacity = '1';
                    card.style.transform = 'scale(1.05)';
                }
            });
        }

        function speakWord(word) {
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(word);
                utterance.lang = 'en-US';
                speechSynthesis.speak(utterance);
            }
        }

        // 初始化发音练习
        function initPronunciation() {
            const pronunciationArea = document.getElementById('pronunciationArea');
            const allWords = [...vocabulary, ...moreVocabulary];

            allWords.forEach((item, index) => {
                const btn = document.createElement('button');
                btn.className = 'quiz-btn';
                btn.style.background = 'linear-gradient(45deg, #667eea, #764ba2)';
                btn.innerHTML = `🔊 ${item.word}`;

                btn.onclick = () => {
                    speakWord(item.word);
                    btn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        btn.style.transform = 'scale(1)';
                    }, 150);
                };

                pronunciationArea.appendChild(btn);
            });
        }

        // 改进语音功能
        function speakWord(word) {
            if ('speechSynthesis' in window) {
                // 停止当前播放
                speechSynthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(word);
                utterance.lang = 'en-US';
                utterance.rate = 0.8; // 稍慢的语速
                utterance.pitch = 1.1; // 稍高的音调

                // 添加视觉反馈
                const feedback = document.createElement('div');
                feedback.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(102, 126, 234, 0.9);
                    color: white;
                    padding: 20px 40px;
                    border-radius: 50px;
                    font-size: 1.5rem;
                    z-index: 1000;
                    animation: fadeIn 0.3s ease;
                `;
                feedback.textContent = `🔊 ${word}`;
                document.body.appendChild(feedback);

                utterance.onend = () => {
                    setTimeout(() => {
                        feedback.remove();
                    }, 500);
                };

                speechSynthesis.speak(utterance);
            } else {
                alert('您的浏览器不支持语音功能');
            }
        }

        // 初始化测试题目
        function initQuiz() {
            const quizData = [
                {
                    question: "transport 的意思是？",
                    options: ["运输", "转换", "翻译", "透明"],
                    correct: 0
                },
                {
                    question: "transform 的意思是？",
                    options: ["运输", "转换", "翻译", "移植"],
                    correct: 1
                },
                {
                    question: "translate 的意思是？",
                    options: ["运输", "转换", "翻译", "传输"],
                    correct: 2
                }
            ];

            const quizArea = document.getElementById('quizArea');
            
            quizData.forEach((quiz, qIndex) => {
                const quizDiv = document.createElement('div');
                quizDiv.style.marginBottom = '20px';
                quizDiv.innerHTML = `<h4 style="margin-bottom: 10px;">${quiz.question}</h4>`;
                
                quiz.options.forEach((option, oIndex) => {
                    const btn = document.createElement('button');
                    btn.className = 'quiz-btn';
                    btn.textContent = option;
                    btn.onclick = () => checkAnswer(btn, oIndex === quiz.correct, qIndex);
                    quizDiv.appendChild(btn);
                });
                
                quizArea.appendChild(quizDiv);
            });
        }

        function checkAnswer(btn, isCorrect, questionIndex) {
            const buttons = btn.parentNode.querySelectorAll('.quiz-btn');
            buttons.forEach(b => b.disabled = true);
            
            if (isCorrect) {
                btn.classList.add('correct');
                btn.textContent += ' ✓';
            } else {
                btn.classList.add('wrong');
                btn.textContent += ' ✗';
            }
        }

        // 添加更多词汇示例
        const moreVocabulary = [
            {
                word: "transfer",
                prefix: "trans-",
                root: "fer (携带)",
                meaning: "转移、传递",
                explanation: "trans(穿越) + fer(携带) = 把东西从一个地方携带到另一个地方"
            },
            {
                word: "transmit",
                prefix: "trans-",
                root: "mit (发送)",
                meaning: "传输、发送",
                explanation: "trans(穿越) + mit(发送) = 通过某种方式发送信息"
            },
            {
                word: "transparent",
                prefix: "trans-",
                root: "parent (显现)",
                meaning: "透明的、清楚的",
                explanation: "trans(穿越) + parent(显现) = 光线可以穿越显现"
            },
            {
                word: "transaction",
                prefix: "trans-",
                root: "action (行动)",
                meaning: "交易、事务",
                explanation: "trans(转换) + action(行动) = 双方之间的转换行动"
            }
        ];

        // 添加词汇探索功能
        function exploreMoreWords() {
            const container = document.getElementById('wordCards');
            const exploreBtn = document.createElement('button');
            exploreBtn.className = 'btn';
            exploreBtn.textContent = '探索更多词汇';
            exploreBtn.style.margin = '20px auto';
            exploreBtn.style.display = 'block';

            exploreBtn.onclick = () => {
                exploreBtn.style.display = 'none';
                showMoreWords();
            };

            container.appendChild(exploreBtn);
        }

        function showMoreWords() {
            const container = document.getElementById('wordCards');

            moreVocabulary.forEach((item, index) => {
                setTimeout(() => {
                    const card = document.createElement('div');
                    card.className = 'word-card';
                    card.style.background = 'linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%)';
                    card.innerHTML = `
                        <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 10px;">
                            <span style="color: #ffd700;">${item.prefix}</span>${item.word.replace(item.prefix.replace('-', ''), '')}
                        </div>
                        <div style="font-size: 1.1rem; margin-bottom: 5px;">${item.meaning}</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">${item.explanation}</div>
                    `;

                    card.addEventListener('click', () => {
                        speakWord(item.word);
                        showWordBreakdown(item);
                    });

                    container.appendChild(card);

                    setTimeout(() => {
                        card.classList.add('show');
                    }, 100);
                }, index * 200);
            });
        }

        // 添加词汇分解动画
        function showWordBreakdown(wordData) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: white;
                padding: 40px;
                border-radius: 20px;
                max-width: 500px;
                text-align: center;
                animation: slideInUp 0.5s ease;
            `;

            content.innerHTML = `
                <h2 style="color: #333; margin-bottom: 20px;">词汇分解</h2>
                <div style="font-size: 2rem; margin: 20px 0;">
                    <span style="color: #ff6b6b; font-weight: bold;">${wordData.prefix}</span>
                    <span style="color: #4ecdc4; font-weight: bold;">${wordData.root}</span>
                </div>
                <div style="font-size: 1.5rem; color: #333; margin: 15px 0;">${wordData.word}</div>
                <div style="font-size: 1.2rem; color: #666; margin: 15px 0;">${wordData.meaning}</div>
                <div style="color: #888; line-height: 1.6;">${wordData.explanation}</div>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="margin-top: 20px; padding: 10px 20px; background: #ff6b6b;
                               color: white; border: none; border-radius: 25px; cursor: pointer;">
                    关闭
                </button>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);

            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            };
        }

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextScene();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    if (currentScene > 0) {
                        currentScene--;
                        drawScene();
                        updateWordCards();
                    }
                    break;
                case 'r':
                case 'R':
                    e.preventDefault();
                    resetStory();
                    break;
                case 's':
                case 'S':
                    e.preventDefault();
                    startStory();
                    break;
            }
        });

        // 添加提示信息
        function showKeyboardHints() {
            const hints = document.createElement('div');
            hints.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: rgba(0,0,0,0.8);
                color: white;
                padding: 15px;
                border-radius: 10px;
                font-size: 0.9rem;
                z-index: 1000;
                animation: slideInUp 0.5s ease;
            `;
            hints.innerHTML = `
                <div style="margin-bottom: 5px;"><strong>快捷键：</strong></div>
                <div>→ 或 空格：下一幕</div>
                <div>← ：上一幕</div>
                <div>S：开始故事</div>
                <div>R：重新开始</div>
            `;

            document.body.appendChild(hints);

            // 5秒后自动隐藏
            setTimeout(() => {
                hints.style.animation = 'fadeOut 0.5s ease';
                setTimeout(() => hints.remove(), 500);
            }, 5000);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initQuiz();
            initPronunciation();
            // 自动开始故事
            setTimeout(startStory, 1000);
            // 添加探索按钮
            setTimeout(exploreMoreWords, 3000);
            // 显示快捷键提示
            setTimeout(showKeyboardHints, 2000);
        });
    </script>
</body>
</html>
