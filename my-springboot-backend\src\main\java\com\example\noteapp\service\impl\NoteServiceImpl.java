package com.example.noteapp.service.impl;

import com.example.noteapp.dto.NoteDTO;
import com.example.noteapp.exception.EntityNotFoundException;
import com.example.noteapp.model.Note;
import com.example.noteapp.repository.NoteRepository;
import com.example.noteapp.service.NoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class NoteServiceImpl implements NoteService {

    private final NoteRepository noteRepository;

    @Autowired
    public NoteServiceImpl(NoteRepository noteRepository) {
        this.noteRepository = noteRepository;
    }

    @Override
    public List<NoteDTO> getAllNotes() {
        return noteRepository.findAll().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public NoteDTO getNoteById(Long id) {
        Note note = noteRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("笔记不存在，ID: " + id));
        return convertToDTO(note);
    }

    @Override
    public NoteDTO createNote(NoteDTO noteDTO) {
        Note note = new Note(noteDTO.getTitle(), noteDTO.getContent());
        Note savedNote = noteRepository.save(note);
        return convertToDTO(savedNote);
    }

    @Override
    public NoteDTO updateNote(Long id, NoteDTO noteDTO) {
        Note note = noteRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("笔记不存在，ID: " + id));
        
        note.setTitle(noteDTO.getTitle());
        note.setContent(noteDTO.getContent());
        
        Note updatedNote = noteRepository.save(note);
        return convertToDTO(updatedNote);
    }

    @Override
    public void deleteNote(Long id) {
        if (!noteRepository.existsById(id)) {
            throw new EntityNotFoundException("笔记不存在，ID: " + id);
        }
        noteRepository.deleteById(id);
    }
    
    private NoteDTO convertToDTO(Note note) {
        return new NoteDTO(
                note.getId(),
                note.getTitle(),
                note.getContent(),
                note.getFormattedCreatedAt()
        );
    }
} 