<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求获取方法 - 互动学习</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        header {
            width: 100%;
            max-width: 960px;
            text-align: center;
            margin-bottom: 40px;
        }

        h1, h2, h3 {
            color: #0056b3;
            margin-bottom: 15px;
        }

        main {
            width: 100%;
            max-width: 960px;
            background-color: #fff;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        section {
            margin-bottom: 50px;
            padding-bottom: 30px;
            border-bottom: 1px solid #eee;
        }

        section:last-of-type {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .method-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fcfcfc;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
        }

        canvas {
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 20px;
            background-color: #fff;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.05);
        }

        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease, transform 0.1s ease;
            margin-top: 10px;
        }

        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }

        button:active {
            transform: translateY(0);
        }

        .explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: #e9f7fe;
            border-left: 5px solid #007bff;
            border-radius: 5px;
            line-height: 1.8;
        }

        .explanation p {
            margin-bottom: 10px;
        }

        .explanation p:last-child {
            margin-bottom: 0;
        }

        .hidden {
            display: none;
        }

        .question-box {
            background-color: #fff;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.1);
        }

        .question-box ol {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .question-box li {
            background-color: #f0f8ff;
            border: 1px solid #cceeff;
            padding: 10px 15px;
            margin-bottom: 8px;
            border-radius: 5px;
            transition: background-color 0.2s ease;
        }

        .question-box li:hover {
            background-color: #e0f2ff;
        }

        #answer-explanation {
            background-color: #e8f5e9; /* Light green for answer */
            border-left-color: #4CAF50; /* Green border */
            margin-top: 25px;
        }

        footer {
            width: 100%;
            max-width: 960px;
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #888;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <header>
        <h1>需求获取方法 - 互动学习</h1>
    </header>

    <main>
        <section id="introduction">
            <h2>什么是需求获取？</h2>
            <p>需求获取就像是侦探破案，我们要找出项目背后的“真凶”——也就是客户和用户的真实需求！这个过程非常重要，决定了我们能不能把事情做好。</p>
        </section>

        <section id="methods-overview">
            <h2>三大常用需求获取方法</h2>
            <div class="method-card" id="user-interview-card">
                <h3>用户访谈 (User Interview)</h3>
                <canvas id="userInterviewCanvas" width="400" height="200"></canvas>
                <button onclick="startUserInterviewDemo()">开始演示</button>
                <div class="explanation" id="userInterviewExplanation">
                    <p>想象一下，你和用户一对一聊天。这种方法非常灵活，可以深入了解用户的想法。但是，信息量太大，可能不好记录，而且需要你对这个领域很了解哦！</p>
                    <p><strong>优点：</strong>灵活，适用范围广，能深入沟通。</p>
                    <p><strong>缺点：</strong>信息量大难记录，需要技巧和领域知识，可能涉及敏感信息。</p>
                </div>
            </div>

            <div class="method-card" id="sampling-card">
                <h3>采样 (Sampling)</h3>
                <canvas id="samplingCanvas" width="400" height="200"></canvas>
                <button onclick="startSamplingDemo()">开始演示</button>
                <div class="explanation" id="samplingExplanation">
                    <p>就像你品尝一锅汤，不用尝完所有，尝一小勺就知道味道了！采样就是从一大堆数据或人群中，选出有代表性的一部分来研究。这样不仅快，还能减少偏差！</p>
                    <p><strong>优点：</strong>基于统计学原理，减少数据偏差，效率高，成本低。</p>
                    <p><strong>缺点：</strong>样本规模依赖经验和主观判断。</p>
                </div>
            </div>

            <div class="method-card" id="jrp-card">
                <h3>联合需求计划 (JRP)</h3>
                <canvas id="jrpCanvas" width="400" height="200"></canvas>
                <button onclick="startJrpDemo()">开始演示</button>
                <div class="explanation" id="jrpExplanation">
                    <p>这不是一个人的战斗，而是一群人的智慧结晶！JRP 是大家坐在一起开会，集思广益，高效地找出系统需求的方法。就像一场头脑风暴，但更有组织！</p>
                    <p><strong>优点：</strong>高效，集体智慧，减少独立访谈。</p>
                    <p><strong>缺点：</strong>需要高度组织和引导。</p>
                </div>
            </div>
        </section>

        <section id="quiz-section">
            <h2>小测验</h2>
            <div class="question-box">
                <p>需求获取是确定和理解不同的项目干系人的需求和约束的过程，需求获取是否科学、准备充分，对获取的结果影响很大。在多种需求获取方式中，（ ）方法具有良好的灵活性，有较宽广的应用范围，但存在获取需求时信息量大、记录较为困难、需要足够的领域知识等问题。（ ）方法基于数理统计原理，不仅可以用于收集数据，还可以用于采集访谈用户或者是采集观察用户，并可以减少数据收集偏差。（ ）方法通过高度组织的群体会议来分析企业内的问题，并从中获取系统需求。</p>
                <p><strong>问题1:</strong></p>
                <p>[ 单选题 ]</p>
                <ol type="A">
                    <li>用户访谈</li>
                    <li>问卷调查</li>
                    <li>联合需求计划</li>
                    <li>采样</li>
                </ol>
                <button onclick="revealAnswer()">揭示答案</button>
                <div id="answer-explanation" class="hidden">
                    <p><strong>正确答案：A</strong></p>
                    <h3>解析：</h3>
                    <p><strong>用户访谈：</strong>用户访谈是最基本的一种需求获取手段，其形式包括结构化和非结构化两种。用户访谈是通过1对1（或1对2，1对3）的形式与用户面对面进行沟通，以获取用户需求。用户访谈具有良好的灵活性，有较宽广的应用范围。但是，也存在着许多困难。例如，用户经常较忙，难以安排时间；面谈时信息量大，记录较为困难；沟通需要很多技巧，同时需要系统分析师具有足够的领域知识等。另外，在访谈时，还可能会遇到一些对于企业来说比较机密和敏感的话题。因此，这看似简单的技术，也需要系统分析师具有丰富的经验和较强的沟通能力。</p>
                    <p><strong>采样：</strong>采样是指从种群中系统地选出有代表性的样本集的过程，通过认真研究所选出的样本集，可以从整体上揭示种群的有用信息。对于信息系统的开发而言，现有系统的文档（文件）就是采样种群。当开始对一个系统做需求分析时，查看现有系统的文档是对系统有初步了解的最好方法。但是，系统分析师应该查看哪些类型的文档，当文档的数据庞大，无法一一研究时，就需要使用采样技术选出有代表性的数据。</p>
                    <p>采样技术不仅可以用于收集数据，还可以用于采集访谈用户或者采集观察用户。在对人员进行采样时，上面介绍的采样技术同样适用。通过采样技术，选择部分而不是选择种群的全部，不仅加快了数据收集的过程，而且提高了效率，从而降低了开发成本。另外，采样技术使用了数理统计原理，能减少数据收集的偏差。但是，由于采样技术基于统计学原理，样本规模的确定依赖于期望的可信度和已有的先验知识，很大程度上取决于系统分析师的主观因素，对系统分析师个人的经验和能力依赖性很强，要求系统分析师具有较高的水平和丰富的经验。</p>
                    <p><strong>联合需求计划：</strong>为了提高需求获取的效率，越来越多的企业倾向于使用小组工作会议来代替大量独立的访谈。联合需求计划（Joint Requirement Planning，JRP）是一个通过高度组织的群体会议来分析企业内的问题并获取需求的过程，它是联合应用开发（Joint Application Development，JAD）的一部分。</p>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2023 互动学习</p>
    </footer>

    <script>
        // Global animation control
        let animationFrames = {};

        // Helper function to stop ongoing animation for a canvas
        function stopAnimation(canvasId) {
            if (animationFrames[canvasId]) {
                cancelAnimationFrame(animationFrames[canvasId]);
                animationFrames[canvasId] = null;
            }
            // Clear the canvas when stopping
            const canvas = document.getElementById(canvasId);
            if (canvas) {
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
        }

        // --- 用户访谈 (User Interview) Canvas ---
        function drawPerson(ctx, x, y, headColor, bodyColor) {
            // Head
            ctx.fillStyle = headColor;
            ctx.beginPath();
            ctx.arc(x, y - 20, 15, 0, Math.PI * 2);
            ctx.fill();
            // Body
            ctx.fillStyle = bodyColor;
            ctx.fillRect(x - 10, y - 5, 20, 30);
            // Legs
            ctx.fillRect(x - 10, y + 25, 8, 15);
            ctx.fillRect(x + 2, y + 25, 8, 15);
        }

        function drawSpeechBubble(ctx, x, y, text, opacity, fromLeft) {
            ctx.globalAlpha = opacity;
            ctx.fillStyle = 'white';
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 1;

            const padding = 5;
            const fontSize = 12;
            ctx.font = `${fontSize}px Arial`;
            const textMetrics = ctx.measureText(text);
            const textWidth = textMetrics.width;
            const textHeight = fontSize + padding; // Approximate height

            const bubbleWidth = textWidth + padding * 2;
            const bubbleHeight = textHeight + padding * 2;

            const radius = 8;
            let bubbleX = x;
            let bubbleY = y;

            // Adjust bubble position for visual appeal
            if (fromLeft) {
                bubbleX = x + 20; // Bubble starts right of person
                bubbleY = y - 60;
            } else { // From right
                bubbleX = x - bubbleWidth - 20; // Bubble ends left of person
                bubbleY = y - 60;
            }


            ctx.beginPath();
            ctx.moveTo(bubbleX + radius, bubbleY);
            ctx.lineTo(bubbleX + bubbleWidth - radius, bubbleY);
            ctx.quadraticCurveTo(bubbleX + bubbleWidth, bubbleY, bubbleX + bubbleWidth, bubbleY + radius);
            ctx.lineTo(bubbleX + bubbleWidth, bubbleY + bubbleHeight - radius);
            ctx.quadraticCurveTo(bubbleX + bubbleWidth, bubbleY + bubbleHeight, bubbleX + bubbleWidth - radius, bubbleY + bubbleHeight);
            ctx.lineTo(bubbleX + radius, bubbleY + bubbleHeight);
            ctx.quadraticCurveTo(bubbleX, bubbleY + bubbleHeight, bubbleX, bubbleY + bubbleHeight - radius);
            ctx.lineTo(bubbleX, bubbleY + radius);
            ctx.quadraticCurveTo(bubbleX, bubbleY, bubbleX + radius, bubbleY);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();

            // Tail pointing to speaker
            if (fromLeft) {
                ctx.beginPath();
                ctx.moveTo(x + 5, y - 25); // Point near head of left person
                ctx.lineTo(bubbleX + 15, bubbleY + bubbleHeight);
                ctx.lineTo(bubbleX + 30, bubbleY + bubbleHeight);
                ctx.closePath();
                ctx.fill();
                ctx.stroke();
            } else {
                ctx.beginPath();
                ctx.moveTo(x - 5, y - 25); // Point near head of right person
                ctx.lineTo(bubbleX + bubbleWidth - 15, bubbleY + bubbleHeight);
                ctx.lineTo(bubbleX + bubbleWidth - 30, bubbleY + bubbleHeight);
                ctx.closePath();
                ctx.fill();
                ctx.stroke();
            }


            ctx.fillStyle = '#333';
            ctx.textAlign = 'left';
            ctx.textBaseline = 'top';
            ctx.fillText(text, bubbleX + padding, bubbleY + padding);
            ctx.globalAlpha = 1; // Reset
        }

        function startUserInterviewDemo() {
            stopAnimation('userInterviewCanvas'); // Stop previous animation
            const canvas = document.getElementById('userInterviewCanvas');
            const ctx = canvas.getContext('2d');
            const explanation = document.getElementById('userInterviewExplanation');
            explanation.classList.remove('hidden');

            let frame = 0;
            let bubbleOpacity = 0;
            let currentTextIndex = 0;
            const texts = [
                { speaker: 0, text: "您好！能聊聊您的需求吗？" },
                { speaker: 1, text: "嗯，我们现在的系统有点麻烦..." },
                { speaker: 0, text: "具体有哪些痛点呢？" },
                { speaker: 1, text: "信息太多了，记录起来很困难！" },
                { speaker: 0, text: "别急，我们来一起梳理！" }
            ];

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                drawPerson(ctx, 100, 100, '#FFDDC1', '#ADD8E6'); // 访谈者 (左)
                drawPerson(ctx, 300, 100, '#DDA0DD', '#90EE90'); // 用户 (右)

                const currentDialogue = texts[currentTextIndex];
                const speakerX = currentDialogue.speaker === 0 ? 100 : 300; // Person's X for tail
                const speakerY = 100; // Person's Y for tail

                drawSpeechBubble(ctx, speakerX, speakerY, currentDialogue.text, bubbleOpacity, currentDialogue.speaker === 0);

                bubbleOpacity += 0.015; // Fade in faster
                if (bubbleOpacity >= 1) {
                    bubbleOpacity = 1; // Fully visible
                    if (frame % 150 === 0) { // Stay visible for 2.5 seconds then next
                         bubbleOpacity = 0; // Reset for next bubble fade-in
                         currentTextIndex = (currentTextIndex + 1) % texts.length;
                    }
                }
                frame++;
                animationFrames.userInterview = requestAnimationFrame(animate);
            }
            animate();
        }

        // --- 采样 (Sampling) Canvas ---
        function drawDot(ctx, x, y, color) {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 5, 0, Math.PI * 2);
            ctx.fill();
        }

        function startSamplingDemo() {
            stopAnimation('samplingCanvas'); // Stop previous animation
            const canvas = document.getElementById('samplingCanvas');
            const ctx = canvas.getContext('2d');
            const explanation = document.getElementById('samplingExplanation');
            explanation.classList.remove('hidden');

            const populationSize = 80;
            const sampleSize = 15;
            let dots = []; // Reset dots for fresh animation
            const colors = ['#6495ED', '#FF6347', '#3CB371', '#DA70D6']; // Different "types" of data

            for (let i = 0; i < populationSize; i++) {
                dots.push({
                    x: Math.random() * 250 + 20, // Initial position on left
                    y: Math.random() * (canvas.height - 40) + 20,
                    color: colors[Math.floor(Math.random() * colors.length)],
                    isSample: false,
                    targetX: 0,
                    targetY: 0,
                    moving: false
                });
            }

            // Select random sample
            const shuffledIndices = Array.from({length: populationSize}, (_, i) => i).sort(() => 0.5 - Math.random());
            const sampleIndices = shuffledIndices.slice(0, sampleSize);

            // Assign target positions for sampled dots in the sample box
            let sampleDotIndex = 0;
            const sampleBoxX = 290;
            const sampleBoxY = 40;
            const sampleBoxWidth = 90;
            const sampleBoxHeight = 120;

            for (let i = 0; i < populationSize; i++) {
                if (sampleIndices.includes(i)) {
                    dots[i].isSample = true;
                    // Distribute sampled dots within the sample box
                    dots[i].targetX = sampleBoxX + 10 + (sampleDotIndex % 5) * 15;
                    dots[i].targetY = sampleBoxY + 10 + Math.floor(sampleDotIndex / 5) * 15;
                    dots[i].moving = false;
                    sampleDotIndex++;
                }
            }

            let frame = 0;
            let startMovingFrame = 60; // Start moving after 1 second

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Draw population on left side
                ctx.fillStyle = '#333';
                ctx.font = '14px Arial';
                ctx.fillText(`总数据量: ${populationSize}`, 10, 20);

                dots.forEach(dot => {
                    if (!dot.moving || (dot.moving && frame < startMovingFrame)) { // Draw population before moving
                        drawDot(ctx, dot.x, dot.y, dot.color);
                    }
                });

                // Draw sample box
                ctx.strokeStyle = '#007bff';
                ctx.lineWidth = 2;
                ctx.strokeRect(sampleBoxX, sampleBoxY, sampleBoxWidth, sampleBoxHeight);
                ctx.fillStyle = '#007bff';
                ctx.font = '14px Arial';
                ctx.fillText('样本区', sampleBoxX + 20, sampleBoxY - 5);


                if (frame > startMovingFrame) {
                    dots.forEach(dot => {
                        if (dot.isSample) {
                            dot.moving = true;
                            // Simple easing for movement
                            dot.x += (dot.targetX - dot.x) * 0.08;
                            dot.y += (dot.targetY - dot.y) * 0.08;
                            drawDot(ctx, dot.x, dot.y, dot.color);
                        }
                    });
                    ctx.fillStyle = '#333';
                    ctx.font = '14px Arial';
                    ctx.fillText(`样本数: ${sampleSize}`, 10, 40);
                }

                frame++;
                animationFrames.sampling = requestAnimationFrame(animate);
            }
            animate();
        }

        // --- 联合需求计划 (JRP) Canvas ---
        function drawTable(ctx, centerX, centerY, width, height) {
            ctx.fillStyle = '#A0522D'; // Sienna
            ctx.fillRect(centerX - width / 2, centerY - height / 2, width, height);
            ctx.strokeStyle = '#8B4513'; // SaddleBrown
            ctx.lineWidth = 3;
            ctx.strokeRect(centerX - width / 2, centerY - height / 2, width, height);
        }

        function drawCollaborator(ctx, x, y, color) {
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, 12, 0, Math.PI * 2); // Head
            ctx.fill();
            ctx.fillRect(x - 10, y + 10, 20, 25); // Body
        }

        function drawThoughtBubble(ctx, x, y, text, opacity) {
            ctx.globalAlpha = opacity;
            ctx.fillStyle = 'white'; // Thought bubble background
            ctx.strokeStyle = '#FFD700'; // Gold border
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.arc(x, y, 15, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();

            ctx.fillStyle = '#333';
            ctx.font = '10px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
            ctx.globalAlpha = 1;
        }

        function startJrpDemo() {
            stopAnimation('jrpCanvas'); // Stop previous animation
            const canvas = document.getElementById('jrpCanvas');
            const ctx = canvas.getContext('2d');
            const explanation = document.getElementById('jrpExplanation');
            explanation.classList.remove('hidden');

            const collaborators = [
                { x: 100, y: 100, color: '#FFB6C1', idea: "用户界面" }, // LightPink
                { x: 300, y: 100, color: '#ADD8E6', idea: "数据库" }, // LightBlue
                { x: 100, y: 200, color: '#90EE90', idea: "业务逻辑" }, // LightGreen
                { x: 300, y: 200, color: '#DA70D6', idea: "报告功能" }  // Orchid
            ];

            let frame = 0;
            let thoughtBubbleOpacity = 0;
            let currentCollaboratorIndex = 0;
            let centralSolutionOpacity = 0;
            const centralSolutionX = canvas.width / 2;
            const centralSolutionY = canvas.height / 2 - 30; // Above the table

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                drawTable(ctx, canvas.width / 2, canvas.height / 2 + 20, 250, 100);

                collaborators.forEach(p => {
                    drawCollaborator(ctx, p.x, p.y - 20, p.color);
                });

                // Animate thought bubbles appearing from collaborators
                if (frame % 90 === 0 && frame < 90 * collaborators.length) {
                    thoughtBubbleOpacity = 0;
                    currentCollaboratorIndex = (currentCollaboratorIndex + 1) % collaborators.length;
                }

                if (thoughtBubbleOpacity < 1) {
                    thoughtBubbleOpacity += 0.02;
                }

                if (frame < 90 * collaborators.length + 60) { // Show individual ideas for a bit
                    drawThoughtBubble(ctx, collaborators[currentCollaboratorIndex].x, collaborators[currentCollaboratorIndex].y - 50, collaborators[currentCollaboratorIndex].idea, thoughtBubbleOpacity);
                } else { // After individual ideas, start merging to central solution
                    if (centralSolutionOpacity < 1) {
                        centralSolutionOpacity += 0.015;
                    }

                    // Animate ideas flying towards the center
                    collaborators.forEach((p, index) => {
                        if (index <= currentCollaboratorIndex || frame >= 90 * collaborators.length + 60) {
                            const currentX = p.x + (centralSolutionX - p.x) * centralSolutionOpacity;
                            const currentY = (p.y - 50) + (centralSolutionY - (p.y - 50)) * centralSolutionOpacity;
                            drawThoughtBubble(ctx, currentX, currentY, p.idea, 1 - centralSolutionOpacity); // Fade out individual as central fades in
                        }
                    });


                    ctx.globalAlpha = centralSolutionOpacity;
                    ctx.fillStyle = '#0056b3';
                    ctx.font = '24px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText("系统需求！", centralSolutionX, centralSolutionY);
                    ctx.globalAlpha = 1;
                }

                frame++;
                animationFrames.jrp = requestAnimationFrame(animate);
            }
            animate();
        }

        // --- Quiz Section ---
        function revealAnswer() {
            const answerDiv = document.getElementById('answer-explanation');
            answerDiv.classList.toggle('hidden');
        }

        // Initial state (hide explanations and stop all animations)
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('userInterviewExplanation').classList.add('hidden');
            document.getElementById('samplingExplanation').classList.add('hidden');
            document.getElementById('jrpExplanation').classList.add('hidden');
            document.getElementById('answer-explanation').classList.add('hidden');

            // Ensure all canvases are initially cleared
            stopAnimation('userInterviewCanvas');
            stopAnimation('samplingCanvas');
            stopAnimation('jrpCanvas');
        });
    </script>
</body>
</html>