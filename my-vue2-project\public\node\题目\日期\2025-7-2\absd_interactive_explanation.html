<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交互式学习：基于架构的软件设计 (ABSD)</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f4f8;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        .container {
            width: 100%;
            max-width: 900px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background-color: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
        }
        .content {
            padding: 25px;
            display: flex;
            gap: 25px;
        }
        .left-panel, .right-panel {
            flex: 1;
        }
        .question-box {
            background-color: #f8f9fa;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        h3 {
            margin-top: 0;
            color: #4a90e2;
            border-bottom: 2px solid #e1e8ed;
            padding-bottom: 10px;
        }
        .question-text {
            line-height: 1.8;
            margin-bottom: 20px;
        }
        .options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .option {
            display: block;
            padding: 12px 15px;
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
        }
        .option:hover {
            background: #e9f2fc;
            border-color: #4a90e2;
        }
        .option.selected {
            background-color: #4a90e2;
            color: white;
            border-color: #4a90e2;
        }
        .option.correct {
            background-color: #28a745;
            color: white;
            border-color: #28a745;
        }
        .option.incorrect {
            background-color: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        .feedback {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .feedback.correct {
            background-color: #e9f7eb;
            color: #155724;
            display: block;
        }
        .feedback.incorrect {
            background-color: #f8d7da;
            color: #721c24;
            display: block;
        }
        .explanation, .canvas-container {
            background-color: #f8f9fa;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 20px;
        }
        canvas {
            width: 100%;
            height: auto;
            display: block;
            background-color: #fff;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">交互式学习：基于架构的软件设计 (ABSD)</div>
    <div class="content">
        <div class="left-panel">
            <div class="question-box">
                <h3>随堂练习</h3>
                <p class="question-text">
                    某公司采用基于架构的软件设计 (ABSD) 方法进行软件设计与开发。ABSD方法有三个基础，分别是：对系统进行功能分解、采用 ( <b>?</b> ) 实现质量属性与商业需求、采用软件模板设计软件结构。
                </p>
                <div class="options">
                    <button class="option" data-answer="A">A) 架构风格</button>
                    <button class="option" data-answer="B">B) 设计模式</button>
                    <button class="option" data-answer="C">C) 架构策略</button>
                    <button class="option" data-answer="D">D) 架构描述</button>
                </div>
                <div class="feedback" id="feedback-box"></div>
            </div>
            <div class="explanation" id="explanation-box" style="display: none;">
                <h3>知识点解析</h3>
                <p><b>正确答案是 A) 架构风格。</b></p>
                <p><b>架构风格 (Architectural Style)</b> 是什么？</p>
                <p>你可以把它想象成盖房子的"风格蓝图"。比如你要盖一座别墅，可以选择"中式园林风格"或"现代极简风格"。</p>
                <ul>
                    <li>不同的<b>风格</b>决定了房子的基本结构、外观和特点（如是否节能、采光好坏）。</li>
                    <li>在软件世界里，"架构风格"（如"客户端-服务器"或"微服务"）就是一套指导原则，它定义了系统的组织方式，以实现特定的<b>质量目标</b>（如高性能、高安全性或易于扩展）。</li>
                </ul>
                <p>所以，题目中说要用一种方式来实现"质量属性"，这种方式就是"架构风格"。</p>
            </div>
        </div>
        <div class="right-panel">
            <div class="canvas-container">
                <h3>动画演示：ABSD 工作流程</h3>
                <canvas id="absd-canvas" width="400" height="450"></canvas>
                <p style="text-align:center; font-size: 14px; color: #666;">点击选项，观看动画演示</p>
            </div>
        </div>
    </div>
</div>

<script>
class AnimatedElement {
    constructor(x, y, width, height, color, text = '', alpha = 1) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.color = color;
        this.text = text;
        this.alpha = alpha;
        this.targetX = x;
        this.targetY = y;
        this.targetAlpha = alpha;
    }

    update(speedFactor = 0.05) {
        this.x += (this.targetX - this.x) * speedFactor;
        this.y += (this.targetY - this.y) * speedFactor;
        this.alpha += (this.targetAlpha - this.alpha) * speedFactor;
    }

    draw(ctx) {
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);
        if (this.text) {
            ctx.fillStyle = '#fff';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(this.text, this.x + this.width / 2, this.y + this.height / 2);
        }
        ctx.restore();
    }
}

const canvas = document.getElementById('absd-canvas');
const ctx = canvas.getContext('2d');
const options = document.querySelectorAll('.option');
const feedbackBox = document.getElementById('feedback-box');
const explanationBox = document.getElementById('explanation-box');

let animationState = 'initial'; // initial, correct, incorrect

// --- Animation Elements ---
const REQ_COLOR = '#4a90e2';
const STYLE_COLOR = '#28a745';
const WRONG_COLOR = '#dc3545';
const ABSD_COLOR = '#f39c12';
const ARCH_COLOR = '#8e44ad';

const requirements = new AnimatedElement(150, 20, 100, 40, REQ_COLOR, '软件需求');
const absdProcess = new AnimatedElement(125, 200, 150, 60, ABSD_COLOR, 'ABSD 方法');
const archStyle = new AnimatedElement(-150, 120, 100, 40, STYLE_COLOR, '架构风格');
const wrongAnswer = new AnimatedElement(450, 120, 100, 40, WRONG_COLOR, '错误方式');
const finalArch = new AnimatedElement(150, 380, 100, 40, ARCH_COLOR, '软件架构');

function drawArrow(from, to, color = '#333', text = '') {
    const headlen = 10;
    const dx = to.x - from.x;
    const dy = to.y - from.y;
    const angle = Math.atan2(dy, dx);
    
    ctx.save();
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(from.x, from.y);
    ctx.lineTo(to.x, to.y);
    ctx.lineTo(to.x - headlen * Math.cos(angle - Math.PI / 6), to.y - headlen * Math.sin(angle - Math.PI / 6));
    ctx.moveTo(to.x, to.y);
    ctx.lineTo(to.x - headlen * Math.cos(angle + Math.PI / 6), to.y - headlen * Math.sin(angle + Math.PI / 6));
    ctx.stroke();

    if (text) {
        ctx.fillStyle = '#333';
        ctx.font = '11px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(text, from.x + dx / 2, from.y + dy / 2 - 5);
    }
    ctx.restore();
}


function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Update and draw elements
    [requirements, absdProcess, archStyle, wrongAnswer, finalArch].forEach(el => {
        el.update();
        el.draw(ctx);
    });
    
    // Draw connecting lines based on state
    if (requirements.alpha > 0.5) {
       drawArrow(
            { x: requirements.x + requirements.width / 2, y: requirements.y + requirements.height },
            { x: absdProcess.x + absdProcess.width / 2, y: absdProcess.y },
            '#333',
            '输入'
        );
    }

    if (animationState === 'correct' && archStyle.alpha > 0.5) {
        drawArrow(
            { x: archStyle.x + archStyle.width, y: archStyle.y + archStyle.height / 2 },
            { x: absdProcess.x, y: absdProcess.y + absdProcess.height / 2 },
            STYLE_COLOR,
            '采用'
        );
    }

    if (finalArch.alpha > 0.5) {
         drawArrow(
            { x: absdProcess.x + absdProcess.width / 2, y: absdProcess.y + absdProcess.height },
            { x: finalArch.x + finalArch.width / 2, y: finalArch.y },
            '#333',
            '产出'
        );
    }
    
    requestAnimationFrame(animate);
}

function resetAnimation() {
    animationState = 'initial';
    archStyle.targetX = -150;
    archStyle.targetAlpha = 0;
    wrongAnswer.targetX = 450;
    wrongAnswer.targetAlpha = 0;
    finalArch.targetAlpha = 0;
}

function triggerCorrectAnimation() {
    animationState = 'correct';
    archStyle.targetX = 10;
    archStyle.targetAlpha = 1;
    wrongAnswer.targetAlpha = 0;
    wrongAnswer.targetX = 450;
    setTimeout(() => {
        finalArch.targetAlpha = 1;
    }, 1000); // Show final architecture after a delay
}

function triggerIncorrectAnimation(elementText) {
    animationState = 'incorrect';
    wrongAnswer.text = elementText;
    wrongAnswer.targetX = 290;
    wrongAnswer.targetAlpha = 1;
    archStyle.targetAlpha = 0;
    archStyle.targetX = -150;
    finalArch.targetAlpha = 0;
}


options.forEach(option => {
    option.addEventListener('click', () => {
        // Clear previous selections
        options.forEach(opt => {
            opt.classList.remove('selected', 'correct', 'incorrect');
        });

        option.classList.add('selected');
        const isCorrect = option.getAttribute('data-answer') === 'A';

        if (isCorrect) {
            option.classList.add('correct');
            feedbackBox.className = 'feedback correct';
            feedbackBox.textContent = '回答正确！"架构风格"是连接需求和最终架构的桥梁。';
            explanationBox.style.display = 'block';
            triggerCorrectAnimation();
        } else {
            option.classList.add('incorrect');
            feedbackBox.className = 'feedback incorrect';
            feedbackBox.textContent = `回答错误。再想想看，什么东西能够定义一套规则来实现特定的"质量"？`;
            explanationBox.style.display = 'none';
            triggerIncorrectAnimation(option.textContent.substring(3));
        }
    });
});

// Initial canvas state
function init() {
    resetAnimation();
    requirements.targetAlpha = 1;
    absdProcess.targetAlpha = 1;
    animate();
}

init();

</script>
</body>
</html> 