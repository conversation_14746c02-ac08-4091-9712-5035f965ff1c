<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件架构风格互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .question-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .question-text {
            font-size: 1.3rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .options-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option {
            background: #f8f9fa;
            border: 3px solid transparent;
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .option.selected {
            border-color: #4CAF50;
            background: #e8f5e8;
        }

        .option.correct {
            border-color: #4CAF50;
            background: #d4edda;
            animation: correctPulse 0.6s ease-in-out;
        }

        .option.wrong {
            border-color: #f44336;
            background: #f8d7da;
            animation: wrongShake 0.6s ease-in-out;
        }

        .canvas-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            text-align: center;
        }

        #gameCanvas {
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            max-width: 100%;
            height: auto;
        }

        .explanation {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            animation: fadeIn 1s ease-out;
        }

        .architecture-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .arch-card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .arch-card:hover {
            transform: scale(1.05);
            background: rgba(255,255,255,0.2);
        }

        .arch-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            display: block;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes correctPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🏗️ 软件架构风格学习</h1>
            <p class="subtitle">通过互动游戏理解四种经典架构风格</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>某公司拟开发一套在线游戏系统，该系统的设计目标之一是支持用户自行定义游戏对象的属性、行为和对象之间的交互关系。为了实现上述目标，公司应该采用（ ）架构风格最为合适。
            </div>
            
            <div class="options-container">
                <div class="option" data-option="A">
                    <strong>A. 管道—过滤器</strong>
                    <div>🔄 数据流处理</div>
                </div>
                <div class="option" data-option="B">
                    <strong>B. 隐式调用</strong>
                    <div>📡 事件驱动</div>
                </div>
                <div class="option" data-option="C">
                    <strong>C. 主程序—子程序</strong>
                    <div>🎯 层次调用</div>
                </div>
                <div class="option" data-option="D">
                    <strong>D. 解释器</strong>
                    <div>🧠 动态执行</div>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <h3>🎮 架构风格互动演示</h3>
            <canvas id="gameCanvas" width="800" height="500"></canvas>
            <div class="controls">
                <button class="btn" onclick="startDemo()">开始演示</button>
                <button class="btn" onclick="resetDemo()">重置</button>
            </div>
        </div>

        <div class="explanation" id="explanation" style="display: none;">
            <h2>💡 知识解析</h2>
            <div class="architecture-demo">
                <div class="arch-card" onclick="showArchitecture('pipe')">
                    <span class="arch-icon">🔄</span>
                    <h3>管道-过滤器</h3>
                    <p>数据流式处理</p>
                </div>
                <div class="arch-card" onclick="showArchitecture('event')">
                    <span class="arch-icon">📡</span>
                    <h3>隐式调用</h3>
                    <p>事件驱动机制</p>
                </div>
                <div class="arch-card" onclick="showArchitecture('main')">
                    <span class="arch-icon">🎯</span>
                    <h3>主程序-子程序</h3>
                    <p>层次化调用</p>
                </div>
                <div class="arch-card" onclick="showArchitecture('interpreter')">
                    <span class="arch-icon">🧠</span>
                    <h3>解释器</h3>
                    <p>动态执行引擎</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let currentDemo = null;
        let animationId = null;
        let progress = 0;

        // 选项点击处理
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                // 清除之前的选择
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('selected', 'correct', 'wrong');
                });
                
                // 标记当前选择
                this.classList.add('selected');
                
                // 检查答案
                setTimeout(() => {
                    checkAnswer(this.dataset.option);
                }, 300);
            });
        });

        function checkAnswer(selected) {
            const correct = 'D';
            const options = document.querySelectorAll('.option');
            
            options.forEach(option => {
                if (option.dataset.option === correct) {
                    option.classList.add('correct');
                } else if (option.dataset.option === selected && selected !== correct) {
                    option.classList.add('wrong');
                }
            });

            // 显示解析
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                updateProgress(100);
            }, 1000);
        }

        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        function startDemo() {
            resetDemo();
            currentDemo = 'interpreter';
            animateInterpreter();
        }

        function resetDemo() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            progress = 0;
        }

        function animateInterpreter() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制解释器架构演示
            drawInterpreterDemo();
            
            progress += 0.02;
            if (progress < 1) {
                animationId = requestAnimationFrame(animateInterpreter);
            }
        }

        function drawInterpreterDemo() {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 绘制解释器核心
            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 60, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('解释器', centerX, centerY - 5);
            ctx.fillText('引擎', centerX, centerY + 15);
            
            // 绘制用户定义的规则
            const rules = [
                { x: centerX - 200, y: centerY - 100, text: '用户规则1' },
                { x: centerX + 200, y: centerY - 100, text: '用户规则2' },
                { x: centerX - 200, y: centerY + 100, text: '用户规则3' },
                { x: centerX + 200, y: centerY + 100, text: '用户规则4' }
            ];
            
            rules.forEach((rule, index) => {
                const alpha = Math.sin(progress * Math.PI * 2 + index * Math.PI / 2) * 0.3 + 0.7;
                ctx.fillStyle = `rgba(102, 126, 234, ${alpha})`;
                ctx.fillRect(rule.x - 60, rule.y - 20, 120, 40);
                
                ctx.fillStyle = 'white';
                ctx.fillText(rule.text, rule.x, rule.y + 5);
                
                // 绘制连接线
                ctx.strokeStyle = `rgba(102, 126, 234, ${alpha})`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(rule.x, rule.y);
                ctx.lineTo(centerX, centerY);
                ctx.stroke();
            });
            
            // 绘制动态执行效果
            const radius = 80 + Math.sin(progress * Math.PI * 4) * 20;
            ctx.strokeStyle = 'rgba(76, 175, 80, 0.6)';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
            ctx.stroke();
        }

        function showArchitecture(type) {
            resetDemo();
            currentDemo = type;
            
            switch(type) {
                case 'pipe':
                    animatePipeFilter();
                    break;
                case 'event':
                    animateEventDriven();
                    break;
                case 'main':
                    animateMainSub();
                    break;
                case 'interpreter':
                    animateInterpreter();
                    break;
            }
        }

        function animatePipeFilter() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制管道-过滤器演示
            const y = canvas.height / 2;
            const stages = ['输入', '过滤器1', '过滤器2', '过滤器3', '输出'];
            
            stages.forEach((stage, index) => {
                const x = 100 + index * 150;
                
                // 绘制过滤器
                ctx.fillStyle = '#FF9800';
                ctx.fillRect(x - 40, y - 30, 80, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(stage, x, y + 5);
                
                // 绘制管道
                if (index < stages.length - 1) {
                    ctx.strokeStyle = '#2196F3';
                    ctx.lineWidth = 4;
                    ctx.beginPath();
                    ctx.moveTo(x + 40, y);
                    ctx.lineTo(x + 110, y);
                    ctx.stroke();
                    
                    // 绘制流动的数据
                    const dataX = x + 40 + (progress * 70) % 70;
                    ctx.fillStyle = '#2196F3';
                    ctx.beginPath();
                    ctx.arc(dataX, y, 5, 0, Math.PI * 2);
                    ctx.fill();
                }
            });
            
            progress += 0.02;
            if (progress < 2) {
                animationId = requestAnimationFrame(animatePipeFilter);
            }
        }

        function animateEventDriven() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制事件驱动演示
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 事件总线
            ctx.fillStyle = '#9C27B0';
            ctx.fillRect(centerX - 100, centerY - 20, 200, 40);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('事件总线', centerX, centerY + 5);
            
            // 组件
            const components = [
                { x: centerX - 200, y: centerY - 150, name: '组件A' },
                { x: centerX + 200, y: centerY - 150, name: '组件B' },
                { x: centerX - 200, y: centerY + 150, name: '组件C' },
                { x: centerX + 200, y: centerY + 150, name: '组件D' }
            ];
            
            components.forEach((comp, index) => {
                const pulse = Math.sin(progress * Math.PI * 3 + index) * 0.2 + 0.8;
                ctx.fillStyle = `rgba(156, 39, 176, ${pulse})`;
                ctx.fillRect(comp.x - 40, comp.y - 20, 80, 40);
                
                ctx.fillStyle = 'white';
                ctx.fillText(comp.name, comp.x, comp.y + 5);
                
                // 事件传播线
                ctx.strokeStyle = `rgba(156, 39, 176, ${pulse})`;
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(comp.x, comp.y);
                ctx.lineTo(centerX, centerY);
                ctx.stroke();
            });
            
            progress += 0.02;
            if (progress < 2) {
                animationId = requestAnimationFrame(animateEventDriven);
            }
        }

        function animateMainSub() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制主程序-子程序演示
            const mainX = canvas.width / 2;
            const mainY = 100;
            
            // 主程序
            ctx.fillStyle = '#F44336';
            ctx.fillRect(mainX - 60, mainY - 30, 120, 60);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主程序', mainX, mainY + 5);
            
            // 子程序
            const subs = [
                { x: mainX - 200, y: mainY + 150, name: '子程序1' },
                { x: mainX, y: mainY + 150, name: '子程序2' },
                { x: mainX + 200, y: mainY + 150, name: '子程序3' }
            ];
            
            subs.forEach((sub, index) => {
                const active = Math.floor(progress * 6) % 3 === index;
                ctx.fillStyle = active ? '#4CAF50' : '#FFC107';
                ctx.fillRect(sub.x - 50, sub.y - 25, 100, 50);
                
                ctx.fillStyle = 'white';
                ctx.fillText(sub.name, sub.x, sub.y + 5);
                
                // 调用线
                ctx.strokeStyle = active ? '#4CAF50' : '#666';
                ctx.lineWidth = active ? 3 : 1;
                ctx.beginPath();
                ctx.moveTo(mainX, mainY + 30);
                ctx.lineTo(sub.x, sub.y - 25);
                ctx.stroke();
            });
            
            progress += 0.02;
            if (progress < 2) {
                animationId = requestAnimationFrame(animateMainSub);
            }
        }

        // 初始化进度
        updateProgress(25);
    </script>
</body>
</html>
