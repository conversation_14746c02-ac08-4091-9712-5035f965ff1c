<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流水线吞吐率 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .pipeline-container {
            position: relative;
            height: 300px;
            margin: 40px 0;
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            border: 2px solid #e9ecef;
        }

        .pipeline-stage {
            position: absolute;
            height: 80px;
            top: 50%;
            transform: translateY(-50%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .stage1 { background: #ff6b6b; left: 50px; width: 120px; }
        .stage2 { background: #4ecdc4; left: 200px; width: 180px; }
        .stage3 { background: #45b7d1; left: 410px; width: 100px; }
        .stage4 { background: #96ceb4; left: 540px; width: 150px; }

        .task {
            position: absolute;
            width: 40px;
            height: 40px;
            background: #ffd93d;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #333;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: all 0.5s ease;
            z-index: 10;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .quiz-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .quiz-option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            background: #4CAF50;
            border-color: #4CAF50;
        }

        .quiz-option.wrong {
            background: #f44336;
            border-color: #f44336;
        }

        .explanation {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            line-height: 1.6;
            display: none;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .highlight {
            animation: pulse 1s infinite;
        }

        .time-display {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🏭 流水线吞吐率</h1>
            <p class="subtitle">通过动画和交互学习计算机流水线处理原理</p>
        </div>

        <div class="section">
            <h2 class="section-title">💡 什么是流水线？</h2>
            <p style="font-size: 1.1rem; line-height: 1.8; color: #555; text-align: center; margin-bottom: 30px;">
                想象一个汽车装配线，每个工位负责不同的工作。计算机的流水线也是如此，将复杂的指令执行过程分解为多个阶段，每个阶段并行工作。
            </p>
            
            <div class="pipeline-container" id="pipelineDemo">
                <div class="pipeline-stage stage1">
                    <div class="time-display">2秒</div>
                    取指令
                </div>
                <div class="pipeline-stage stage2">
                    <div class="time-display">3秒</div>
                    译码
                </div>
                <div class="pipeline-stage stage3">
                    <div class="time-display">1秒</div>
                    执行
                </div>
                <div class="pipeline-stage stage4">
                    <div class="time-display">2.5秒</div>
                    写回
                </div>
            </div>

            <div class="controls">
                <button class="btn" onclick="startPipeline()">🚀 启动流水线</button>
                <button class="btn" onclick="resetPipeline()">🔄 重置</button>
                <button class="btn" onclick="showBottleneck()">🔍 找瓶颈</button>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value" id="throughput">0</div>
                    <div class="stat-label">吞吐率 (任务/秒)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="bottleneck">3秒</div>
                    <div class="stat-label">瓶颈时间</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="completed">0</div>
                    <div class="stat-label">完成任务数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="efficiency">0%</div>
                    <div class="stat-label">效率</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 核心概念</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px;">
                <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 5px solid #667eea;">
                    <h3 style="color: #667eea; margin-bottom: 15px;">🔗 流水线原理</h3>
                    <p style="line-height: 1.6; color: #555;">
                        流水线将指令执行分为多个阶段，每个阶段可以同时处理不同的指令，就像工厂的装配线一样。
                    </p>
                </div>
                <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 5px solid #4ecdc4;">
                    <h3 style="color: #4ecdc4; margin-bottom: 15px;">⏱️ 瓶颈效应</h3>
                    <p style="line-height: 1.6; color: #555;">
                        流水线的速度由最慢的阶段决定，这个最慢的阶段就是"瓶颈"。
                    </p>
                </div>
                <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 5px solid #45b7d1;">
                    <h3 style="color: #45b7d1; margin-bottom: 15px;">📊 吞吐率计算</h3>
                    <p style="line-height: 1.6; color: #555;">
                        吞吐率 = 1 / 最长流水段时间，表示单位时间内完成的任务数。
                    </p>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 style="text-align: center; margin-bottom: 30px; font-size: 2rem;">🧠 知识测试</h2>
            <div class="quiz-question">
                流水线的吞吐率是指单位时间流水线处理的任务数，如果各段流水的操作时间不同，则流水线的吞吐率是（ ）的倒数。
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectOption(this, false)">
                    A. 最短流水段操作时间
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    B. 各段流水的操作时间总和
                </div>
                <div class="quiz-option" onclick="selectOption(this, true)">
                    C. 最长流水段操作时间
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    D. 流水段数乘以最长流水段操作时间
                </div>
            </div>
            <div class="explanation" id="explanation">
                <h3>💡 详细解析</h3>
                <p>
                    <strong>正确答案：C. 最长流水段操作时间</strong><br><br>

                    <strong>原理解释：</strong><br>
                    • 流水线就像一条装配线，每个工位的速度不同<br>
                    • 整条生产线的速度由最慢的工位决定<br>
                    • 即使其他工位很快，也要等最慢的工位完成<br>
                    • 因此，吞吐率 = 1 / 最长流水段时间<br><br>

                    <strong>举例说明：</strong><br>
                    如果流水线有4个阶段，时间分别是2秒、3秒、1秒、2.5秒，那么瓶颈是3秒的阶段。
                    每3秒才能输出一个完整的结果，所以吞吐率 = 1/3 ≈ 0.33 任务/秒。
                </p>
            </div>
        </div>
    </div>

    <script>
        let taskCounter = 0;
        let completedTasks = 0;
        let isRunning = false;
        let animationId;

        // 流水线阶段时间（秒）
        const stageTimes = [2, 3, 1, 2.5];
        const stageNames = ['取指令', '译码', '执行', '写回'];

        // 找到瓶颈时间
        const bottleneckTime = Math.max(...stageTimes);
        const bottleneckIndex = stageTimes.indexOf(bottleneckTime);

        function updateStats() {
            const throughput = isRunning ? (1 / bottleneckTime).toFixed(2) : 0;
            const efficiency = isRunning ? ((1 / bottleneckTime) / (1 / Math.min(...stageTimes)) * 100).toFixed(1) : 0;

            document.getElementById('throughput').textContent = throughput;
            document.getElementById('bottleneck').textContent = bottleneckTime + '秒';
            document.getElementById('completed').textContent = completedTasks;
            document.getElementById('efficiency').textContent = efficiency + '%';
        }

        function createTask() {
            taskCounter++;
            const task = document.createElement('div');
            task.className = 'task';
            task.textContent = taskCounter;
            task.style.left = '10px';
            task.style.top = '50%';
            task.style.transform = 'translateY(-50%)';
            document.getElementById('pipelineDemo').appendChild(task);

            animateTask(task);
        }

        function animateTask(task) {
            let currentStage = 0;
            const stagePositions = [75, 290, 460, 615]; // 每个阶段的中心位置

            function moveToNextStage() {
                if (currentStage < stageTimes.length) {
                    // 移动到当前阶段
                    task.style.left = stagePositions[currentStage] + 'px';
                    task.style.transition = 'left 0.5s ease';

                    // 高亮当前阶段
                    const stages = document.querySelectorAll('.pipeline-stage');
                    stages.forEach(stage => stage.classList.remove('highlight'));
                    stages[currentStage].classList.add('highlight');

                    // 等待当前阶段完成
                    setTimeout(() => {
                        currentStage++;
                        if (currentStage < stageTimes.length) {
                            moveToNextStage();
                        } else {
                            // 任务完成
                            completedTasks++;
                            updateStats();
                            task.style.left = '750px';
                            task.style.opacity = '0';
                            setTimeout(() => {
                                if (task.parentNode) {
                                    task.parentNode.removeChild(task);
                                }
                            }, 500);

                            // 移除高亮
                            stages.forEach(stage => stage.classList.remove('highlight'));
                        }
                    }, stageTimes[currentStage] * 1000);
                }
            }

            moveToNextStage();
        }

        function startPipeline() {
            if (isRunning) return;

            isRunning = true;
            updateStats();

            // 每隔瓶颈时间创建新任务
            function scheduleNextTask() {
                if (isRunning) {
                    createTask();
                    setTimeout(scheduleNextTask, bottleneckTime * 1000);
                }
            }

            scheduleNextTask();
        }

        function resetPipeline() {
            isRunning = false;
            taskCounter = 0;
            completedTasks = 0;

            // 清除所有任务
            const tasks = document.querySelectorAll('.task');
            tasks.forEach(task => task.remove());

            // 移除高亮
            const stages = document.querySelectorAll('.pipeline-stage');
            stages.forEach(stage => stage.classList.remove('highlight'));

            updateStats();
        }

        function showBottleneck() {
            const stages = document.querySelectorAll('.pipeline-stage');
            stages.forEach((stage, index) => {
                stage.classList.remove('highlight');
                if (index === bottleneckIndex) {
                    stage.classList.add('highlight');
                    stage.style.background = '#ff4757';
                    setTimeout(() => {
                        stage.style.background = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'][index];
                        stage.classList.remove('highlight');
                    }, 3000);
                }
            });

            // 显示提示信息
            alert(`瓶颈阶段：${stageNames[bottleneckIndex]} (${bottleneckTime}秒)\n这是决定整个流水线速度的关键阶段！`);
        }

        function selectOption(element, isCorrect) {
            // 移除之前的选择
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'wrong');
            });

            // 标记选择
            if (isCorrect) {
                element.classList.add('correct');
                setTimeout(() => {
                    document.getElementById('explanation').style.display = 'block';
                    document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
                }, 500);
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                setTimeout(() => {
                    document.querySelectorAll('.quiz-option')[2].classList.add('correct');
                    document.getElementById('explanation').style.display = 'block';
                    document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
                }, 1000);
            }
        }

        // 初始化
        updateStats();

        // 添加一些视觉效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为阶段添加悬停效果
            const stages = document.querySelectorAll('.pipeline-stage');
            stages.forEach((stage, index) => {
                stage.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-50%) scale(1.05)';
                    this.style.zIndex = '5';
                });

                stage.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(-50%) scale(1)';
                    this.style.zIndex = '1';
                });

                stage.addEventListener('click', function() {
                    alert(`${stageNames[index]}阶段\n处理时间：${stageTimes[index]}秒\n${index === bottleneckIndex ? '⚠️ 这是瓶颈阶段！' : '✅ 运行正常'}`);
                });
            });
        });
    </script>
</body>
</html>
