<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>总线知识互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #ffecd2, #fcb69f);
            color: #333;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .explanation {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .quiz-section {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
        }

        .option {
            background: rgba(255,255,255,0.2);
            padding: 15px 20px;
            margin: 10px 0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(10px);
        }

        .option.selected {
            background: rgba(255,255,255,0.4);
            border-color: white;
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .highlight {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #333;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚌 总线知识互动学习</h1>
            <p class="subtitle">让我们用动画来理解计算机总线的工作原理</p>
        </div>

        <div class="section">
            <h2 class="section-title">📚 什么是总线？</h2>
            <div class="explanation">
                <p>想象一下公交车站🚌，多个乘客要上车，但车门一次只能让一个人通过。</p>
                <p><span class="highlight">总线</span>就像这个车门，是连接计算机各个部件的"公共通道"。</p>
                <p>所有部件都要<span class="highlight">排队分时</span>使用这条通道来传输数据！</p>
            </div>
            
            <div class="canvas-container">
                <canvas id="busCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startBusAnimation()">🎬 开始动画演示</button>
                <button class="btn btn-secondary" onclick="resetAnimation()">🔄 重置动画</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 核心概念解析</h2>
            <div class="canvas-container">
                <canvas id="conceptCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="showConcept('shared')">📤 共享性演示</button>
                <button class="btn btn-primary" onclick="showConcept('timeshare')">⏰ 分时性演示</button>
                <button class="btn btn-secondary" onclick="resetConcept()">🔄 重置</button>
            </div>
            <div class="explanation">
                <p><span class="highlight">共享性</span>：所有部件都可以使用总线传输数据</p>
                <p><span class="highlight">分时性</span>：同一时刻只能有一个部件发送数据到总线</p>
                <p><span class="highlight">关键理解</span>：发送是分时的，但接收可以同时进行！</p>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title" style="color: white;">🧠 题目解析</h2>
            <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>挂接在总线上的多个部件（ ）。</h3>
            </div>

            <div class="option" onclick="selectOption(this, 'A')">
                A. 只能分时向总线发送数据，并只能分时从总线接收数据
            </div>
            <div class="option" onclick="selectOption(this, 'B')">
                B. 只能分时向总线发送数据，但可同时从总线接收数据
            </div>
            <div class="option" onclick="selectOption(this, 'C')">
                C. 可同时向总线发送数据，并同时从总线接收数据
            </div>
            <div class="option" onclick="selectOption(this, 'D')">
                D. 可同时向总线发送数据，但只能分时从总线接收数据
            </div>

            <div class="controls">
                <button class="btn btn-primary" onclick="showAnswer()" style="margin-top: 20px;">💡 查看答案解析</button>
            </div>

            <div id="answerExplanation" style="display: none; margin-top: 20px; padding: 20px; background: rgba(255,255,255,0.2); border-radius: 10px;">
                <h4>✅ 正确答案：B</h4>
                <p><strong>解析思路：</strong></p>
                <p>1️⃣ <span class="highlight">发送数据</span>：想象只有一个麦克风，大家要排队说话 → 分时</p>
                <p>2️⃣ <span class="highlight">接收数据</span>：想象广播，所有人都能同时听到 → 同时</p>
                <p>3️⃣ 总线就像单行道：只能一个方向有车（发送），但路边的人都能看到车（接收）</p>
            </div>
        </div>
    </div>

    <script>
        // 总线动画相关变量
        let busCanvas, busCtx;
        let conceptCanvas, conceptCtx;
        let animationId;
        let components = [];
        let busData = [];
        let animationStep = 0;
        let selectedOption = null;

        // 初始化画布
        function initCanvas() {
            busCanvas = document.getElementById('busCanvas');
            busCtx = busCanvas.getContext('2d');
            conceptCanvas = document.getElementById('conceptCanvas');
            conceptCtx = conceptCanvas.getContext('2d');

            // 初始化组件
            components = [
                {name: 'CPU', x: 100, y: 100, color: '#FF6B6B', sending: false},
                {name: '内存', x: 100, y: 200, color: '#4ECDC4', sending: false},
                {name: '硬盘', x: 100, y: 300, color: '#45B7D1', sending: false},
                {name: 'GPU', x: 700, y: 100, color: '#96CEB4', sending: false},
                {name: '网卡', x: 700, y: 200, color: '#FFEAA7', sending: false}
            ];

            drawInitialBus();
            drawInitialConcept();
        }

        // 绘制初始总线图
        function drawInitialBus() {
            busCtx.clearRect(0, 0, busCanvas.width, busCanvas.height);

            // 绘制总线
            busCtx.strokeStyle = '#333';
            busCtx.lineWidth = 8;
            busCtx.beginPath();
            busCtx.moveTo(200, 200);
            busCtx.lineTo(600, 200);
            busCtx.stroke();

            // 总线标签
            busCtx.fillStyle = '#333';
            busCtx.font = 'bold 16px Microsoft YaHei';
            busCtx.textAlign = 'center';
            busCtx.fillText('🚌 总线 (Bus)', 400, 180);

            // 绘制组件
            components.forEach(comp => {
                drawComponent(busCtx, comp);
            });

            // 连接线
            busCtx.strokeStyle = '#ddd';
            busCtx.lineWidth = 2;
            components.forEach(comp => {
                busCtx.beginPath();
                if (comp.x < 400) {
                    busCtx.moveTo(comp.x + 60, comp.y + 15);
                    busCtx.lineTo(200, 200);
                } else {
                    busCtx.moveTo(comp.x - 10, comp.y + 15);
                    busCtx.lineTo(600, 200);
                }
                busCtx.stroke();
            });
        }

        // 绘制组件
        function drawComponent(ctx, comp) {
            // 组件背景
            ctx.fillStyle = comp.sending ? '#FFD93D' : comp.color;
            ctx.fillRect(comp.x, comp.y, 80, 30);

            // 组件边框
            ctx.strokeStyle = comp.sending ? '#FF6B35' : '#333';
            ctx.lineWidth = comp.sending ? 3 : 1;
            ctx.strokeRect(comp.x, comp.y, 80, 30);

            // 组件文字
            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(comp.name, comp.x + 40, comp.y + 20);
        }

        // 开始总线动画
        function startBusAnimation() {
            resetAnimation();
            animationStep = 0;
            animateBus();
        }

        // 总线动画主循环
        function animateBus() {
            if (animationStep >= components.length * 60) {
                resetAnimation();
                return;
            }

            const currentComp = Math.floor(animationStep / 60);
            const stepInComp = animationStep % 60;

            // 重置所有组件状态
            components.forEach(comp => comp.sending = false);

            // 设置当前发送组件
            if (currentComp < components.length) {
                components[currentComp].sending = true;
            }

            drawInitialBus();

            // 绘制数据包动画
            if (stepInComp < 40 && currentComp < components.length) {
                drawDataPacket(currentComp, stepInComp);
            }

            // 显示说明文字
            busCtx.fillStyle = '#FF6B35';
            busCtx.font = 'bold 18px Microsoft YaHei';
            busCtx.textAlign = 'center';
            if (currentComp < components.length) {
                busCtx.fillText(`${components[currentComp].name} 正在发送数据...`, 400, 50);
                busCtx.fillStyle = '#666';
                busCtx.font = '14px Microsoft YaHei';
                busCtx.fillText('其他组件必须等待！', 400, 350);
            }

            animationStep++;
            animationId = requestAnimationFrame(animateBus);
        }

        // 绘制数据包
        function drawDataPacket(compIndex, step) {
            const comp = components[compIndex];
            let startX, endX, y = 200;

            if (comp.x < 400) {
                startX = comp.x + 60;
                endX = 600;
            } else {
                startX = comp.x - 10;
                endX = 200;
            }

            const progress = step / 40;
            const currentX = startX + (endX - startX) * progress;

            // 数据包
            busCtx.fillStyle = '#FF6B35';
            busCtx.beginPath();
            busCtx.arc(currentX, y, 8, 0, 2 * Math.PI);
            busCtx.fill();

            // 数据包标签
            busCtx.fillStyle = '#333';
            busCtx.font = '12px Microsoft YaHei';
            busCtx.textAlign = 'center';
            busCtx.fillText('📦', currentX, y - 15);
        }

        // 重置动画
        function resetAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            components.forEach(comp => comp.sending = false);
            drawInitialBus();
        }

        // 绘制概念图
        function drawInitialConcept() {
            conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);

            conceptCtx.fillStyle = '#333';
            conceptCtx.font = 'bold 20px Microsoft YaHei';
            conceptCtx.textAlign = 'center';
            conceptCtx.fillText('点击按钮查看概念演示', 400, 150);
        }

        // 显示概念
        function showConcept(type) {
            conceptCtx.clearRect(0, 0, conceptCanvas.width, conceptCanvas.height);

            if (type === 'shared') {
                // 共享性演示
                conceptCtx.fillStyle = '#4ECDC4';
                conceptCtx.font = 'bold 24px Microsoft YaHei';
                conceptCtx.textAlign = 'center';
                conceptCtx.fillText('🤝 共享性：所有部件都能使用总线', 400, 50);

                // 绘制多个组件指向总线
                const positions = [{x: 150, y: 150}, {x: 300, y: 120}, {x: 500, y: 120}, {x: 650, y: 150}];
                positions.forEach((pos, i) => {
                    conceptCtx.fillStyle = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'][i];
                    conceptCtx.fillRect(pos.x, pos.y, 60, 25);
                    conceptCtx.strokeStyle = '#333';
                    conceptCtx.strokeRect(pos.x, pos.y, 60, 25);

                    // 箭头指向总线
                    conceptCtx.strokeStyle = '#FF6B35';
                    conceptCtx.lineWidth = 3;
                    conceptCtx.beginPath();
                    conceptCtx.moveTo(pos.x + 30, pos.y + 25);
                    conceptCtx.lineTo(400, 200);
                    conceptCtx.stroke();
                });

                // 总线
                conceptCtx.fillStyle = '#FFD93D';
                conceptCtx.fillRect(300, 190, 200, 20);
                conceptCtx.strokeStyle = '#333';
                conceptCtx.strokeRect(300, 190, 200, 20);
                conceptCtx.fillStyle = '#333';
                conceptCtx.font = '16px Microsoft YaHei';
                conceptCtx.fillText('总线', 400, 225);

            } else if (type === 'timeshare') {
                // 分时性演示
                conceptCtx.fillStyle = '#FF6B6B';
                conceptCtx.font = 'bold 24px Microsoft YaHei';
                conceptCtx.textAlign = 'center';
                conceptCtx.fillText('⏰ 分时性：同一时刻只能一个发送', 400, 50);

                // 时间轴
                conceptCtx.strokeStyle = '#333';
                conceptCtx.lineWidth = 2;
                conceptCtx.beginPath();
                conceptCtx.moveTo(100, 150);
                conceptCtx.lineTo(700, 150);
                conceptCtx.stroke();

                // 时间段
                const timeSlots = ['CPU', '内存', '硬盘', 'GPU'];
                timeSlots.forEach((name, i) => {
                    const x = 150 + i * 120;
                    conceptCtx.fillStyle = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'][i];
                    conceptCtx.fillRect(x, 130, 80, 40);
                    conceptCtx.strokeStyle = '#333';
                    conceptCtx.strokeRect(x, 130, 80, 40);
                    conceptCtx.fillStyle = '#333';
                    conceptCtx.font = '14px Microsoft YaHei';
                    conceptCtx.fillText(name, x + 40, 152);

                    // 时间标记
                    conceptCtx.fillText(`T${i+1}`, x + 40, 190);
                });
            }
        }

        // 重置概念图
        function resetConcept() {
            drawInitialConcept();
        }

        // 选择选项
        function selectOption(element, option) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });

            element.classList.add('selected');
            selectedOption = option;
        }

        // 显示答案
        function showAnswer() {
            const options = document.querySelectorAll('.option');
            options.forEach((opt, index) => {
                const letter = ['A', 'B', 'C', 'D'][index];
                if (letter === 'B') {
                    opt.classList.add('correct');
                } else if (opt.classList.contains('selected') && letter !== 'B') {
                    opt.classList.add('wrong');
                }
            });

            document.getElementById('answerExplanation').style.display = 'block';
        }

        // 页面加载完成后初始化
        window.onload = function() {
            initCanvas();
        };
    </script>
</body>
</html>
