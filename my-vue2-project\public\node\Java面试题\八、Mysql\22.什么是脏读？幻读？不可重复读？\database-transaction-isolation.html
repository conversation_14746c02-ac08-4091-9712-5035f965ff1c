<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库事务隔离级别 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .concept-card {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(30px);
        }

        .concept-card.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 80px rgba(0,0,0,0.15);
        }

        .concept-title {
            font-size: 2.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .concept-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .dirty-read .concept-icon { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
        .non-repeatable .concept-icon { background: linear-gradient(135deg, #4834d4, #686de0); }
        .phantom-read .concept-icon { background: linear-gradient(135deg, #00d2d3, #54a0ff); }

        .concept-description {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin-bottom: 30px;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 30px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }

        .transaction-container {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            margin-bottom: 30px;
        }

        .transaction {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .transaction-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .data-row {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .data-row:hover {
            background: #bbdefb;
            transform: translateX(5px);
        }

        .control-panel {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .animation-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            opacity: 0;
            pointer-events: none;
        }

        .pulse {
            animation: pulse 0.6s ease-in-out;
        }

        @keyframes pulse {
            0% { opacity: 0; transform: translate(-50%, -50%) scale(0.5); }
            50% { opacity: 1; transform: translate(-50%, -50%) scale(1.2); }
            100% { opacity: 0; transform: translate(-50%, -50%) scale(1); }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .highlight {
            background: #fff3cd !important;
            border: 2px solid #ffc107;
            animation: highlight 0.5s ease-in-out;
        }

        @keyframes highlight {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .error {
            background: #f8d7da !important;
            border: 2px solid #dc3545;
            color: #721c24;
        }

        .success {
            background: #d4edda !important;
            border: 2px solid #28a745;
            color: #155724;
        }

        .step-indicator {
            text-align: center;
            margin: 20px 0;
            font-weight: 600;
            color: #666;
        }

        canvas {
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        /* 测试游戏样式 */
        .quiz-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }

        #questionTitle {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        #questionText {
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .quiz-option {
            background: #f8f9fa;
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 15px 20px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .quiz-option:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .quiz-option.selected {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .quiz-option.correct {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .quiz-option.incorrect {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .quiz-progress {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            display: block;
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
        }

        .score-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 16px;
            padding: 30px;
            text-align: center;
        }

        .score-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        #finalScore {
            font-size: 3rem;
            font-weight: bold;
            margin: 20px 0;
        }

        #scoreMessage {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        /* 总结网格样式 */
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .summary-item {
            background: white;
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .summary-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .summary-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .dirty-summary::before { background: linear-gradient(135deg, #ff6b6b, #ee5a24); }
        .nonrepeatable-summary::before { background: linear-gradient(135deg, #4834d4, #686de0); }
        .phantom-summary::before { background: linear-gradient(135deg, #00d2d3, #54a0ff); }

        .summary-item h4 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .summary-item p {
            color: #666;
            font-size: 1rem;
            margin-bottom: 15px;
        }

        .summary-icon {
            font-size: 2.5rem;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">数据库事务隔离级别</h1>
            <p class="subtitle">通过交互式动画学习脏读、不可重复读和幻读</p>
        </div>

        <!-- 脏读演示 -->
        <div class="concept-card dirty-read">
            <h2 class="concept-title">
                <div class="concept-icon">💥</div>
                脏读 (Dirty Read)
            </h2>
            <p class="concept-description">
                脏读是指一个事务读取了另一个事务尚未提交的数据。如果第一个事务回滚，那么第二个事务读取的数据就是"脏"的，即无效的数据。
            </p>
            <div class="demo-area">
                <div class="transaction-container">
                    <div class="transaction">
                        <div class="transaction-title">事务A</div>
                        <div class="data-row" id="dataA1">账户余额: ¥1000</div>
                        <div class="step-indicator" id="stepA">等待操作...</div>
                    </div>
                    <div class="transaction">
                        <div class="transaction-title">事务B</div>
                        <div class="data-row" id="dataB1">读取到的余额: ¥1000</div>
                        <div class="step-indicator" id="stepB">等待操作...</div>
                    </div>
                </div>
                <div class="control-panel">
                    <button class="btn btn-primary" onclick="startDirtyReadDemo()">开始演示</button>
                    <button class="btn btn-danger" onclick="resetDirtyRead()">重置</button>
                </div>
                <div class="animation-indicator" id="dirtyIndicator"></div>
            </div>
        </div>

        <!-- 不可重复读演示 -->
        <div class="concept-card non-repeatable">
            <h2 class="concept-title">
                <div class="concept-icon">🔄</div>
                不可重复读 (Non-repeatable Read)
            </h2>
            <p class="concept-description">
                不可重复读是指在同一个事务中，两次读取同一数据得到了不同的结果。这是因为在两次读取之间，另一个事务修改了这个数据。
            </p>
            <div class="demo-area">
                <div class="transaction-container">
                    <div class="transaction">
                        <div class="transaction-title">事务A (读取)</div>
                        <div class="data-row" id="readA1">第一次读取: ¥1000</div>
                        <div class="data-row" id="readA2">第二次读取: ¥1000</div>
                        <div class="step-indicator" id="stepReadA">等待操作...</div>
                    </div>
                    <div class="transaction">
                        <div class="transaction-title">事务B (修改)</div>
                        <div class="data-row" id="modifyB">修改余额: ¥1000 → ¥1500</div>
                        <div class="step-indicator" id="stepModifyB">等待操作...</div>
                    </div>
                </div>
                <div class="control-panel">
                    <button class="btn btn-primary" onclick="startNonRepeatableDemo()">开始演示</button>
                    <button class="btn btn-danger" onclick="resetNonRepeatable()">重置</button>
                </div>
                <div class="animation-indicator" id="nonRepeatableIndicator"></div>
            </div>
        </div>

        <!-- 幻读演示 -->
        <div class="concept-card phantom-read">
            <h2 class="concept-title">
                <div class="concept-icon">👻</div>
                幻读 (Phantom Read)
            </h2>
            <p class="concept-description">
                幻读是指在同一个事务中，两次查询得到了不同数量的记录。这是因为在两次查询之间，另一个事务插入或删除了符合查询条件的记录。
            </p>
            <div class="demo-area">
                <div class="transaction-container">
                    <div class="transaction">
                        <div class="transaction-title">事务A (查询)</div>
                        <div id="queryResults1">
                            <div class="data-row">用户1: 张三</div>
                            <div class="data-row">用户2: 李四</div>
                        </div>
                        <div class="step-indicator" id="stepQueryA">第一次查询: 2条记录</div>
                    </div>
                    <div class="transaction">
                        <div class="transaction-title">事务B (插入)</div>
                        <div class="data-row" id="insertB">插入新用户: 王五</div>
                        <div class="step-indicator" id="stepInsertB">等待操作...</div>
                    </div>
                </div>
                <div class="control-panel">
                    <button class="btn btn-primary" onclick="startPhantomDemo()">开始演示</button>
                    <button class="btn btn-danger" onclick="resetPhantom()">重置</button>
                </div>
                <div class="animation-indicator" id="phantomIndicator"></div>
            </div>
        </div>

        <!-- Canvas动画区域 -->
        <div class="concept-card">
            <h2 class="concept-title">
                <div class="concept-icon">🎨</div>
                可视化时间线
            </h2>
            <canvas id="timelineCanvas" width="800" height="300"></canvas>
        </div>

        <!-- 知识测试游戏 -->
        <div class="concept-card">
            <h2 class="concept-title">
                <div class="concept-icon">🎮</div>
                知识测试游戏
            </h2>
            <p class="concept-description">
                通过互动问答来测试您对事务隔离级别的理解程度！
            </p>
            <div class="demo-area">
                <div id="quizContainer">
                    <div id="questionCard" class="quiz-card">
                        <h3 id="questionTitle">准备开始测试？</h3>
                        <p id="questionText">点击开始按钮来测试您的知识！</p>
                        <div id="optionsContainer"></div>
                        <div class="quiz-progress">
                            <div id="progressBar" class="progress-bar"></div>
                            <span id="progressText">0/5</span>
                        </div>
                    </div>
                    <div class="control-panel">
                        <button class="btn btn-primary" id="startQuizBtn" onclick="startQuiz()">开始测试</button>
                        <button class="btn btn-danger" id="resetQuizBtn" onclick="resetQuiz()" style="display:none;">重新开始</button>
                    </div>
                    <div id="scoreCard" class="score-card" style="display:none;">
                        <h3>测试完成！</h3>
                        <div id="finalScore"></div>
                        <div id="scoreMessage"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总结卡片 -->
        <div class="concept-card">
            <h2 class="concept-title">
                <div class="concept-icon">📚</div>
                知识总结
            </h2>
            <div class="summary-grid">
                <div class="summary-item dirty-summary">
                    <h4>脏读</h4>
                    <p>读取未提交数据</p>
                    <div class="summary-icon">💥</div>
                </div>
                <div class="summary-item nonrepeatable-summary">
                    <h4>不可重复读</h4>
                    <p>同一事务多次读取结果不同</p>
                    <div class="summary-icon">🔄</div>
                </div>
                <div class="summary-item phantom-summary">
                    <h4>幻读</h4>
                    <p>查询结果记录数量变化</p>
                    <div class="summary-icon">👻</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentDemo = null;
        let animationStep = 0;

        // 页面加载动画
        window.addEventListener('load', () => {
            const cards = document.querySelectorAll('.concept-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('visible');
                }, index * 200);
            });
        });

        // 脏读演示
        function startDirtyReadDemo() {
            if (currentDemo) return;
            currentDemo = 'dirty';
            animationStep = 0;
            
            const steps = [
                () => {
                    document.getElementById('stepA').textContent = '事务A开始，修改余额为¥1500';
                    document.getElementById('dataA1').textContent = '账户余额: ¥1500 (未提交)';
                    document.getElementById('dataA1').classList.add('highlight');
                    showAnimation('dirtyIndicator', '✏️');
                },
                () => {
                    document.getElementById('stepB').textContent = '事务B读取数据';
                    document.getElementById('dataB1').textContent = '读取到的余额: ¥1500';
                    document.getElementById('dataB1').classList.add('error');
                    showAnimation('dirtyIndicator', '👁️');
                },
                () => {
                    document.getElementById('stepA').textContent = '事务A回滚！';
                    document.getElementById('dataA1').textContent = '账户余额: ¥1000 (已回滚)';
                    document.getElementById('dataA1').classList.remove('highlight');
                    document.getElementById('dataA1').classList.add('success');
                    showAnimation('dirtyIndicator', '↩️');
                },
                () => {
                    document.getElementById('stepB').textContent = '事务B读取到了脏数据！';
                    showAnimation('dirtyIndicator', '💥');
                    currentDemo = null;
                }
            ];

            executeSteps(steps, 2000);
        }

        function resetDirtyRead() {
            currentDemo = null;
            document.getElementById('dataA1').textContent = '账户余额: ¥1000';
            document.getElementById('dataB1').textContent = '读取到的余额: ¥1000';
            document.getElementById('stepA').textContent = '等待操作...';
            document.getElementById('stepB').textContent = '等待操作...';
            clearClasses(['dataA1', 'dataB1']);
        }

        // 不可重复读演示
        function startNonRepeatableDemo() {
            if (currentDemo) return;
            currentDemo = 'nonrepeatable';
            
            const steps = [
                () => {
                    document.getElementById('stepReadA').textContent = '事务A第一次读取';
                    document.getElementById('readA1').classList.add('highlight');
                    showAnimation('nonRepeatableIndicator', '👁️');
                },
                () => {
                    document.getElementById('stepModifyB').textContent = '事务B修改并提交';
                    document.getElementById('modifyB').classList.add('highlight');
                    showAnimation('nonRepeatableIndicator', '✏️');
                },
                () => {
                    document.getElementById('stepReadA').textContent = '事务A第二次读取';
                    document.getElementById('readA2').textContent = '第二次读取: ¥1500';
                    document.getElementById('readA2').classList.add('error');
                    document.getElementById('readA1').classList.remove('highlight');
                    showAnimation('nonRepeatableIndicator', '👁️');
                },
                () => {
                    document.getElementById('stepReadA').textContent = '同一事务中读取到不同值！';
                    showAnimation('nonRepeatableIndicator', '🔄');
                    currentDemo = null;
                }
            ];

            executeSteps(steps, 2000);
        }

        function resetNonRepeatable() {
            currentDemo = null;
            document.getElementById('readA1').textContent = '第一次读取: ¥1000';
            document.getElementById('readA2').textContent = '第二次读取: ¥1000';
            document.getElementById('modifyB').textContent = '修改余额: ¥1000 → ¥1500';
            document.getElementById('stepReadA').textContent = '等待操作...';
            document.getElementById('stepModifyB').textContent = '等待操作...';
            clearClasses(['readA1', 'readA2', 'modifyB']);
        }

        // 幻读演示
        function startPhantomDemo() {
            if (currentDemo) return;
            currentDemo = 'phantom';
            
            const steps = [
                () => {
                    document.getElementById('stepQueryA').textContent = '事务A第一次查询: 2条记录';
                    document.getElementById('queryResults1').classList.add('highlight');
                    showAnimation('phantomIndicator', '🔍');
                },
                () => {
                    document.getElementById('stepInsertB').textContent = '事务B插入新记录并提交';
                    document.getElementById('insertB').classList.add('highlight');
                    showAnimation('phantomIndicator', '➕');
                },
                () => {
                    document.getElementById('stepQueryA').textContent = '事务A第二次查询: 3条记录';
                    const newRow = document.createElement('div');
                    newRow.className = 'data-row error';
                    newRow.textContent = '用户3: 王五 (新出现)';
                    document.getElementById('queryResults1').appendChild(newRow);
                    showAnimation('phantomIndicator', '🔍');
                },
                () => {
                    document.getElementById('stepQueryA').textContent = '出现了幻影记录！';
                    showAnimation('phantomIndicator', '👻');
                    currentDemo = null;
                }
            ];

            executeSteps(steps, 2000);
        }

        function resetPhantom() {
            currentDemo = null;
            const queryResults = document.getElementById('queryResults1');
            queryResults.innerHTML = `
                <div class="data-row">用户1: 张三</div>
                <div class="data-row">用户2: 李四</div>
            `;
            document.getElementById('stepQueryA').textContent = '第一次查询: 2条记录';
            document.getElementById('stepInsertB').textContent = '等待操作...';
            clearClasses(['insertB']);
            queryResults.classList.remove('highlight');
        }

        // 工具函数
        function executeSteps(steps, delay) {
            steps.forEach((step, index) => {
                setTimeout(step, index * delay);
            });
        }

        function showAnimation(elementId, emoji) {
            const indicator = document.getElementById(elementId);
            indicator.textContent = emoji;
            indicator.classList.add('pulse');
            setTimeout(() => {
                indicator.classList.remove('pulse');
            }, 600);
        }

        function clearClasses(elementIds) {
            elementIds.forEach(id => {
                const element = document.getElementById(id);
                element.classList.remove('highlight', 'error', 'success');
            });
        }

        // Canvas时间线动画
        const canvas = document.getElementById('timelineCanvas');
        const ctx = canvas.getContext('2d');
        let timelineAnimation = null;
        let currentTimelineStep = 0;

        function drawTimeline(step = 0) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制背景网格
            ctx.strokeStyle = 'rgba(200, 200, 200, 0.3)';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 30) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            // 绘制时间轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(50, 150);
            ctx.lineTo(750, 150);
            ctx.stroke();

            // 绘制时间点
            const timePoints = [100, 200, 300, 400, 500, 600, 700];
            const timeLabels = ['开始', '读取', '修改', '提交', '回滚', '再读', '结束'];

            timePoints.forEach((x, index) => {
                const isActive = index <= step;
                ctx.fillStyle = isActive ? '#667eea' : '#ddd';
                ctx.beginPath();
                ctx.arc(x, 150, 10, 0, 2 * Math.PI);
                ctx.fill();

                if (isActive) {
                    ctx.fillStyle = 'white';
                    ctx.beginPath();
                    ctx.arc(x, 150, 6, 0, 2 * Math.PI);
                    ctx.fill();
                }

                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(timeLabels[index], x, 180);
            });

            // 绘制事务A线条
            if (step >= 0) {
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(100, 100);
                const endX = Math.min(100 + step * 100, 500);
                ctx.lineTo(endX, 100);
                ctx.stroke();

                // 添加箭头
                if (step > 0) {
                    drawArrow(ctx, endX - 10, 100, endX, 100, '#ff6b6b');
                }
            }

            // 绘制事务B线条
            if (step >= 1) {
                ctx.strokeStyle = '#4834d4';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(200, 200);
                const endX = Math.min(200 + (step - 1) * 100, 600);
                ctx.lineTo(endX, 200);
                ctx.stroke();

                // 添加箭头
                if (step > 1) {
                    drawArrow(ctx, endX - 10, 200, endX, 200, '#4834d4');
                }
            }

            // 绘制冲突点
            if (step >= 3) {
                ctx.fillStyle = 'rgba(255, 107, 107, 0.3)';
                ctx.beginPath();
                ctx.arc(300, 150, 30, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = '#ff6b6b';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('⚠️', 300, 155);
            }

            // 添加事务标签
            ctx.fillStyle = '#ff6b6b';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('事务A', 110, 85);

            ctx.fillStyle = '#4834d4';
            ctx.fillText('事务B', 210, 215);

            // 添加说明文字
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('时间线演示 - 点击下方按钮查看不同场景', canvas.width / 2, 30);
        }

        function drawArrow(ctx, fromX, fromY, toX, toY, color) {
            const headlen = 8;
            const angle = Math.atan2(toY - fromY, toX - fromX);

            ctx.strokeStyle = color;
            ctx.fillStyle = color;
            ctx.lineWidth = 2;

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        function animateTimeline() {
            if (timelineAnimation) {
                clearInterval(timelineAnimation);
            }

            currentTimelineStep = 0;
            timelineAnimation = setInterval(() => {
                drawTimeline(currentTimelineStep);
                currentTimelineStep++;

                if (currentTimelineStep > 6) {
                    currentTimelineStep = 0;
                }
            }, 1500);
        }

        // 添加时间线控制按钮
        function addTimelineControls() {
            const timelineCard = document.querySelector('.concept-card:last-child .demo-area');
            if (!timelineCard) {
                const lastCard = document.querySelector('.concept-card:last-child');
                const demoArea = document.createElement('div');
                demoArea.className = 'demo-area';
                lastCard.appendChild(demoArea);
            }

            const controlPanel = document.createElement('div');
            controlPanel.className = 'control-panel';
            controlPanel.innerHTML = `
                <button class="btn btn-primary" onclick="animateTimeline()">播放动画</button>
                <button class="btn btn-primary" onclick="drawTimeline(0)">重置</button>
                <button class="btn btn-primary" onclick="drawTimeline(3)">显示冲突</button>
            `;

            const lastCard = document.querySelector('.concept-card:last-child');
            lastCard.appendChild(controlPanel);
        }

        // 初始化时间线
        drawTimeline(0);
        addTimelineControls();

        // 测试游戏逻辑
        const quizQuestions = [
            {
                question: "什么是脏读？",
                options: [
                    "读取了另一个事务未提交的数据",
                    "同一事务中两次读取结果不同",
                    "查询结果记录数量发生变化",
                    "读取了已删除的数据"
                ],
                correct: 0,
                explanation: "脏读是指一个事务读取了另一个事务尚未提交的数据。"
            },
            {
                question: "不可重复读的特征是什么？",
                options: [
                    "读取了未提交的数据",
                    "在同一事务中，两次读取同一数据得到不同结果",
                    "查询到了不存在的记录",
                    "无法读取任何数据"
                ],
                correct: 1,
                explanation: "不可重复读是指在同一个事务中，两次读取同一数据得到了不同的结果。"
            },
            {
                question: "幻读主要体现在什么方面？",
                options: [
                    "数据值的变化",
                    "数据的删除",
                    "查询结果记录数量的变化",
                    "数据类型的改变"
                ],
                correct: 2,
                explanation: "幻读是指在同一个事务中，两次查询得到了不同数量的记录。"
            },
            {
                question: "如果事务A修改了数据但未提交，事务B读取了这个数据，然后事务A回滚，这种情况叫什么？",
                options: [
                    "幻读",
                    "不可重复读",
                    "脏读",
                    "死锁"
                ],
                correct: 2,
                explanation: "这是典型的脏读场景，事务B读取了事务A未提交的数据。"
            },
            {
                question: "以下哪种情况最可能导致幻读？",
                options: [
                    "UPDATE操作",
                    "DELETE操作",
                    "INSERT操作",
                    "SELECT操作"
                ],
                correct: 2,
                explanation: "INSERT操作会增加新记录，容易导致幻读现象。"
            }
        ];

        let currentQuestionIndex = 0;
        let score = 0;
        let quizStarted = false;

        function startQuiz() {
            quizStarted = true;
            currentQuestionIndex = 0;
            score = 0;

            document.getElementById('startQuizBtn').style.display = 'none';
            document.getElementById('resetQuizBtn').style.display = 'inline-block';
            document.getElementById('scoreCard').style.display = 'none';

            showQuestion();
        }

        function showQuestion() {
            if (currentQuestionIndex >= quizQuestions.length) {
                showResults();
                return;
            }

            const question = quizQuestions[currentQuestionIndex];
            document.getElementById('questionTitle').textContent = `问题 ${currentQuestionIndex + 1}`;
            document.getElementById('questionText').textContent = question.question;

            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';

            question.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'quiz-option';
                optionDiv.textContent = option;
                optionDiv.onclick = () => selectOption(index);
                optionsContainer.appendChild(optionDiv);
            });

            updateProgress();
        }

        function selectOption(selectedIndex) {
            const question = quizQuestions[currentQuestionIndex];
            const options = document.querySelectorAll('.quiz-option');

            // 禁用所有选项
            options.forEach(option => {
                option.onclick = null;
                option.style.cursor = 'default';
            });

            // 显示正确答案
            options[question.correct].classList.add('correct');

            if (selectedIndex === question.correct) {
                score++;
                showAnimation('', '✅');
            } else {
                options[selectedIndex].classList.add('incorrect');
                showAnimation('', '❌');
            }

            // 显示解释
            setTimeout(() => {
                const explanation = document.createElement('div');
                explanation.style.cssText = `
                    background: #e8f4fd;
                    border-left: 4px solid #2196f3;
                    padding: 15px;
                    margin-top: 15px;
                    border-radius: 8px;
                    font-style: italic;
                `;
                explanation.textContent = question.explanation;
                document.getElementById('optionsContainer').appendChild(explanation);

                setTimeout(() => {
                    currentQuestionIndex++;
                    showQuestion();
                }, 2000);
            }, 1000);
        }

        function updateProgress() {
            const progress = (currentQuestionIndex / quizQuestions.length) * 100;
            const progressBar = document.querySelector('.progress-bar');
            progressBar.style.setProperty('--progress', `${progress}%`);
            progressBar.querySelector('::after') || (progressBar.style.background = `linear-gradient(to right, #667eea ${progress}%, #e9ecef ${progress}%)`);

            document.getElementById('progressText').textContent = `${currentQuestionIndex}/${quizQuestions.length}`;
        }

        function showResults() {
            document.getElementById('questionCard').style.display = 'none';
            document.getElementById('scoreCard').style.display = 'block';

            const percentage = Math.round((score / quizQuestions.length) * 100);
            document.getElementById('finalScore').textContent = `${score}/${quizQuestions.length} (${percentage}%)`;

            let message = '';
            if (percentage >= 90) {
                message = '🎉 优秀！您完全掌握了事务隔离级别的概念！';
            } else if (percentage >= 70) {
                message = '👍 很好！您对事务隔离级别有了良好的理解！';
            } else if (percentage >= 50) {
                message = '📚 不错！建议再复习一下相关概念。';
            } else {
                message = '💪 继续努力！多看看演示动画会有帮助。';
            }

            document.getElementById('scoreMessage').textContent = message;
        }

        function resetQuiz() {
            quizStarted = false;
            currentQuestionIndex = 0;
            score = 0;

            document.getElementById('startQuizBtn').style.display = 'inline-block';
            document.getElementById('resetQuizBtn').style.display = 'none';
            document.getElementById('questionCard').style.display = 'block';
            document.getElementById('scoreCard').style.display = 'none';

            document.getElementById('questionTitle').textContent = '准备开始测试？';
            document.getElementById('questionText').textContent = '点击开始按钮来测试您的知识！';
            document.getElementById('optionsContainer').innerHTML = '';
            document.getElementById('progressText').textContent = '0/5';

            const progressBar = document.querySelector('.progress-bar');
            progressBar.style.background = '#e9ecef';
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            if (quizStarted && currentQuestionIndex < quizQuestions.length) {
                const key = e.key;
                if (key >= '1' && key <= '4') {
                    const index = parseInt(key) - 1;
                    const options = document.querySelectorAll('.quiz-option');
                    if (options[index] && options[index].onclick) {
                        selectOption(index);
                    }
                }
            }
        });

        // 添加鼠标悬停效果
        document.addEventListener('mouseover', (e) => {
            if (e.target.classList.contains('data-row')) {
                e.target.style.transform = 'translateX(5px) scale(1.02)';
            }
        });

        document.addEventListener('mouseout', (e) => {
            if (e.target.classList.contains('data-row')) {
                e.target.style.transform = '';
            }
        });

        // 添加滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animation = 'fadeInUp 0.6s ease-out forwards';
                }
            });
        }, observerOptions);

        // 观察所有概念卡片
        document.querySelectorAll('.concept-card').forEach(card => {
            observer.observe(card);
        });

        // 添加粒子效果
        function createParticles(x, y, color) {
            for (let i = 0; i < 6; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: fixed;
                    width: 6px;
                    height: 6px;
                    background: ${color};
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 1000;
                    left: ${x}px;
                    top: ${y}px;
                `;

                document.body.appendChild(particle);

                const angle = (Math.PI * 2 * i) / 6;
                const velocity = 2 + Math.random() * 3;
                const vx = Math.cos(angle) * velocity;
                const vy = Math.sin(angle) * velocity;

                let px = x, py = y;
                let opacity = 1;

                const animate = () => {
                    px += vx;
                    py += vy;
                    opacity -= 0.02;

                    particle.style.left = px + 'px';
                    particle.style.top = py + 'px';
                    particle.style.opacity = opacity;

                    if (opacity > 0) {
                        requestAnimationFrame(animate);
                    } else {
                        document.body.removeChild(particle);
                    }
                };

                requestAnimationFrame(animate);
            }
        }

        // 为按钮添加点击效果
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn')) {
                const rect = e.target.getBoundingClientRect();
                const x = rect.left + rect.width / 2;
                const y = rect.top + rect.height / 2;
                createParticles(x, y, '#667eea');
            }
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .quiz-option {
                position: relative;
                overflow: hidden;
            }

            .quiz-option::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                transition: left 0.5s;
            }

            .quiz-option:hover::before {
                left: 100%;
            }
        `;
        document.head.appendChild(style);

        // 响应式处理
        window.addEventListener('resize', () => {
            const container = document.querySelector('.container');
            if (window.innerWidth < 768) {
                container.style.padding = '20px 10px';
                // 调整canvas大小
                const canvas = document.getElementById('timelineCanvas');
                canvas.width = Math.min(600, window.innerWidth - 40);
            }
        });

        // 初始化提示
        console.log('🎓 数据库事务隔离级别学习页面已加载！');
        console.log('💡 提示：在测试游戏中可以使用数字键1-4快速选择答案！');
    </script>
</body>
</html>
