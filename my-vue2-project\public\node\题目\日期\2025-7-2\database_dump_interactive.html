<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库转储交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            font-size: 16px;
        }
        #container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            padding: 2rem;
            width: 90%;
            max-width: 900px;
            text-align: center;
        }
        h1 {
            color: #1a73e8;
            margin-bottom: 1.5rem;
        }
        #question-box {
            background-color: #e8f0fe;
            border-left: 5px solid #1a73e8;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            text-align: left;
            border-radius: 8px;
            line-height: 1.6;
        }
        #canvas-container {
            position: relative;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            background: #fafafa;
            overflow: hidden;
        }
        canvas {
            display: block;
            width: 100%;
            height: auto;
        }
        #explanation {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0,0,0,0.6);
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-size: 0.9em;
            text-align: center;
            opacity: 0;
            transition: opacity 0.5s ease;
            pointer-events: none; /* So it doesn't block clicks on canvas if needed */
        }
        #options-box {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        .option-btn {
            background-color: #f8f9fa;
            border: 2px solid #dadce0;
            border-radius: 8px;
            padding: 1rem;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            color: #3c4043;
        }
        .option-btn:hover {
            background-color: #e8f0fe;
            border-color: #1a73e8;
        }
        .option-btn:disabled {
            cursor: not-allowed;
            opacity: 0.6;
        }
        .correct {
            background-color: #e6f4ea;
            border-color: #34a853;
        }
        .incorrect {
            background-color: #fce8e6;
            border-color: #ea4335;
        }
        #result-box {
            margin-top: 1.5rem;
            padding: 1rem;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: bold;
            display: none; /* Initially hidden */
        }
        .result-correct {
            background-color: #e6f4ea;
            color: #1e8e3e;
        }
        .result-incorrect {
            background-color: #fce8e6;
            color: #d93025;
        }
    </style>
</head>
<body>

<div id="container">
    <h1>数据库转储技术小测验</h1>
    <div id="question-box">
        <p><strong>题目：</strong>假设某证券公司的股票交易系统中正在运行的事务，此时若要转储该交易系统数据库中的全部数据，则应采用( )方式。</p>
    </div>
    
    <div id="canvas-container">
        <canvas id="demo-canvas" width="800" height="350"></canvas>
        <div id="explanation">请选择一个选项，观看下方动画演示。</div>
    </div>

    <div id="options-box">
        <button class="option-btn" id="btn-a" data-value="A">A. 静态全局转储</button>
        <button class="option-btn" id="btn-b" data-value="B">B. 动态全局转储</button>
        <button class="option-btn" id="btn-c" data-value="C">C. 静态增量转储</button>
        <button class="option-btn" id="btn-d" data-value="D">D. 动态增量转储</button>
    </div>

    <div id="result-box"></div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('demo-canvas');
    const ctx = canvas.getContext('2d');
    const explanationDiv = document.getElementById('explanation');
    const optionButtons = document.querySelectorAll('.option-btn');
    const resultBox = document.getElementById('result-box');

    // Canvas dimensions
    const width = canvas.width;
    const height = canvas.height;

    // Animation state
    let animationId;
    let currentScenario = null;
    let dumper, transactions;
    
    // --- Core Concepts ---
    const DB_X = width / 2 - 150;
    const DB_Y = height / 2 - 50;
    const DB_WIDTH = 300;
    const DB_HEIGHT = 100;

    const DUMPER_START_Y = 50;
    const DUMP_TARGET_Y = height - 70;

    class Database {
        constructor() {
            this.blocks = [];
            for(let i = 0; i < 10; i++) {
                this.blocks.push({ color: '#4285F4', changed: Math.random() > 0.7 });
            }
        }
        draw(locked = false) {
            ctx.strokeStyle = '#3c4043';
            ctx.lineWidth = 2;
            ctx.strokeRect(DB_X, DB_Y, DB_WIDTH, DB_HEIGHT);
            ctx.font = 'bold 16px sans-serif';
            ctx.fillStyle = '#3c4043';
            ctx.fillText('数据库', DB_X + DB_WIDTH / 2 - 30, DB_Y - 15);

            const blockWidth = DB_WIDTH / this.blocks.length;
            this.blocks.forEach((block, i) => {
                ctx.fillStyle = block.changed ? '#FBBC05' : '#4285F4'; // Yellow for changed
                ctx.fillRect(DB_X + i * blockWidth + 2, DB_Y + 2, blockWidth - 4, DB_HEIGHT - 4);
            });
            
            if (locked) {
                ctx.fillStyle = 'rgba(234, 67, 53, 0.5)';
                ctx.fillRect(DB_X, DB_Y, DB_WIDTH, DB_HEIGHT);
                ctx.font = 'bold 48px sans-serif';
                ctx.fillStyle = '#ea4335';
                ctx.fillText('🔒', DB_X + DB_WIDTH/2 - 24, DB_Y + DB_HEIGHT/2 + 18);
            }
        }
    }

    class Transaction {
        constructor() {
            this.x = Math.random() * width;
            this.y = Math.random() * height;
            this.targetX = DB_X + Math.random() * DB_WIDTH;
            this.targetY = DB_Y + Math.random() * DB_HEIGHT;
            this.speed = (Math.random() * 1 + 1) * 0.5; // Reduced speed
            this.color = '#34A853';
            this.size = 8;
            this.paused = false;
        }
        update() {
            if (this.paused) return;
            const dx = this.targetX - this.x;
            const dy = this.targetY - this.y;
            const dist = Math.sqrt(dx * dx + dy * dy);
            if (dist < 10) {
                this.targetX = DB_X + Math.random() * DB_WIDTH;
                this.targetY = DB_Y + Math.random() * DB_HEIGHT;
            }
            this.x += (dx / dist) * this.speed;
            this.y += (dy / dist) * this.speed;
        }
        draw() {
            ctx.fillStyle = this.color;
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
            ctx.fill();
        }
    }

    class Dumper {
        constructor(type) {
            this.type = type; // 'static-full', 'dynamic-full', 'static-incremental', 'dynamic-incremental'
            this.x = width / 2;
            this.y = DUMPER_START_Y;
            this.progress = 0; // 0 to 1
            this.speed = 0.005;
            this.active = false;
        }
        update() {
            if (this.active && this.progress < 1) {
                this.progress += this.speed;
            }
        }
        draw(db) {
            if (!this.active) return;
            
            // Dumper icon
            ctx.font = '32px sans-serif';
            ctx.fillText('🔄', this.x - 16, this.y);
            ctx.font = '14px sans-serif';
            ctx.fillStyle = '#333';
            ctx.fillText('转储进程', this.x-30, this.y + 20);

            // Progress line
            const dumpY = this.y + (DUMP_TARGET_Y - this.y) * this.progress;
            ctx.beginPath();
            ctx.moveTo(this.x, this.y + 25);
            ctx.lineTo(this.x, dumpY);
            ctx.strokeStyle = '#DB4437';
            ctx.lineWidth = 4;
            ctx.stroke();

            // Dumped data
            const dumpWidth = DB_WIDTH;
            const dumpHeight = DB_HEIGHT * this.progress;
            ctx.strokeStyle = '#3c4043';
            ctx.lineWidth = 1;
            ctx.strokeRect(DB_X, DUMP_TARGET_Y, dumpWidth, DB_HEIGHT);
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(DB_X + 1, DUMP_TARGET_Y + 1, dumpWidth - 2, DB_HEIGHT - 2);

            const blockWidth = DB_WIDTH / db.blocks.length;

            if (this.type.includes('full')) {
                 db.blocks.forEach((block, i) => {
                    const h = (DB_HEIGHT - 4) * this.progress;
                    ctx.fillStyle = block.color;
                    ctx.fillRect(DB_X + i * blockWidth + 2, DUMP_TARGET_Y + 2, blockWidth - 4, h);
                });
            } else { // incremental
                 db.blocks.forEach((block, i) => {
                    if (block.changed) {
                        const h = (DB_HEIGHT - 4) * this.progress;
                        ctx.fillStyle = '#FBBC05';
                        ctx.fillRect(DB_X + i * blockWidth + 2, DUMP_TARGET_Y + 2, blockWidth - 4, h);
                    }
                });
            }
            
            ctx.fillStyle = '#3c4043';
            ctx.font = 'bold 16px sans-serif';
            ctx.fillText('转储备份', DB_X + DB_WIDTH / 2 - 40, DUMP_TARGET_Y + DB_HEIGHT + 20);
        }
    }

    // --- Animation Loop ---
    const db = new Database();

    function initAnimation() {
        if (animationId) cancelAnimationFrame(animationId);

        transactions = [];
        for (let i = 0; i < 5; i++) {
            transactions.push(new Transaction());
        }
        dumper = new Dumper('none');
        currentScenario = null;
        
        // Reset UI
        explanationDiv.style.opacity = '1';
        explanationDiv.innerText = '背景：有5个绿色小点代表正在运行的事务，它们在持续访问数据库。黄色块代表有更新的数据。';
        resultBox.style.display = 'none';
        optionButtons.forEach(btn => {
            btn.disabled = false;
            btn.className = 'option-btn';
        });

        animate();
    }

    function animate() {
        ctx.clearRect(0, 0, width, height);
        
        let isLocked = false;
        if (currentScenario && currentScenario.startsWith('static')) {
            if (dumper.active) {
                isLocked = true;
                transactions.forEach(t => t.paused = true);
            }
        } else {
             transactions.forEach(t => t.paused = false);
        }

        db.draw(isLocked);
        transactions.forEach(t => {
            t.update();
            t.draw();
        });

        if (dumper.active) {
            dumper.update();
            dumper.draw(db);
        }

        if (dumper.progress >= 1) {
            cancelAnimationFrame(animationId);
            showResult();
        } else {
            animationId = requestAnimationFrame(animate);
        }
    }

    function startScenario(scenario, explanation) {
        if (animationId) cancelAnimationFrame(animationId);
        
        transactions = [];
        for (let i = 0; i < 5; i++) {
            transactions.push(new Transaction());
        }

        currentScenario = scenario;
        dumper = new Dumper(scenario);
        dumper.active = true;

        explanationDiv.innerText = explanation;
        explanationDiv.style.opacity = '1';

        optionButtons.forEach(btn => btn.disabled = true);
        animate();
    }

    function showResult() {
        const selectedOption = currentScenario.split('-')[1] === 'full' ?
            (currentScenario.startsWith('static') ? 'A' : 'B') :
            (currentScenario.startsWith('static') ? 'C' : 'D');
        
        const correct = selectedOption === 'B';
        resultBox.style.display = 'block';
        resultBox.className = correct ? 'result-correct' : 'result-incorrect';
        resultBox.innerHTML = `${correct ? '回答正确！' : '回答错误。'} `
        if (correct) {
            resultBox.innerHTML += '动态全局转储可以在不影响业务的情况下备份所有数据，是最佳选择。'
        } else {
            resultBox.innerHTML += '请再想想题目要求："正在运行的事务"和"转储全部数据"。'
        }
        
        const selectedBtn = document.getElementById(`btn-${selectedOption.toLowerCase()}`);
        selectedBtn.classList.add(correct ? 'correct' : 'incorrect');

        setTimeout(() => {
            initAnimation();
            explanationDiv.innerText = '动画已重置。您可以选择其他选项再次尝试。';
        }, 5000);
    }


    // --- Event Listeners ---
    document.getElementById('btn-a').addEventListener('click', () => {
        startScenario('static-full', '静态全局转储：所有事务已暂停（数据库锁定🔒），开始复制所有数据...');
    });
    document.getElementById('btn-b').addEventListener('click', () => {
        startScenario('dynamic-full', '动态全局转储：事务仍在运行，同时开始复制所有数据...');
    });
    document.getElementById('btn-c').addEventListener('click', () => {
        startScenario('static-incremental', '静态增量转储：所有事务已暂停（数据库锁定🔒），仅复制已更改的数据（黄色块）...');
    });
    document.getElementById('btn-d').addEventListener('click', () => {
        startScenario('dynamic-incremental', '动态增量转储：事务仍在运行，同时仅复制已更改的数据（黄色块）...');
    });

    // Initial setup
    initAnimation();
});
</script>
</body>
</html> 