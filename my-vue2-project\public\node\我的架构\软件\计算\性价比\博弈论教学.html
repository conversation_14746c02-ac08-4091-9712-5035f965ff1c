<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博弈论互动教学 - 网站广告定价策略</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .game-board {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .websites-container {
            display: flex;
            justify-content: space-around;
            margin-bottom: 40px;
        }

        .website {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 200px;
        }

        .website-a {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .website-b {
            background: linear-gradient(135deg, #4834d4, #686de0);
            color: white;
        }

        .website:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .website h3 {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .profit-display {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }

        .strategy-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 15px;
        }

        .strategy-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .strategy-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }

        .strategy-btn.active {
            background: rgba(255,255,255,0.9);
            color: #333;
        }

        .matrix-container {
            margin: 40px 0;
            text-align: center;
        }

        .matrix-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #333;
        }

        .payoff-matrix {
            display: inline-block;
            border-collapse: collapse;
            margin: 20px auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .payoff-matrix th, .payoff-matrix td {
            padding: 20px;
            text-align: center;
            font-weight: bold;
            position: relative;
        }

        .payoff-matrix th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-size: 1.1em;
        }

        .payoff-matrix td {
            background: white;
            border: 2px solid #f0f0f0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .payoff-matrix td:hover {
            background: #f8f9ff;
            transform: scale(1.05);
        }

        .payoff-matrix td.highlight {
            background: #ffe066;
            animation: pulse 1s infinite;
        }

        .profit-split {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .profit-a {
            color: #ff6b6b;
            font-weight: bold;
        }

        .profit-b {
            color: #4834d4;
            font-weight: bold;
        }

        .analysis-section {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            animation: fadeIn 1s ease-out 0.6s both;
        }

        .analysis-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .step {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            opacity: 0;
            transform: translateX(-20px);
            transition: all 0.5s ease;
        }

        .step.show {
            opacity: 1;
            transform: translateX(0);
        }

        .step h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .control-btn {
            padding: 15px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .conclusion {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
            animation: fadeIn 1s ease-out 0.9s both;
        }

        .conclusion h3 {
            font-size: 1.8em;
            margin-bottom: 15px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #gameCanvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 博弈论互动教学</h1>
            <p>网站广告定价策略的博弈分析</p>
        </div>

        <div class="game-board">
            <div class="websites-container">
                <div class="website website-a" id="websiteA">
                    <h3>🌐 甲网站</h3>
                    <div class="profit-display" id="profitA">1000万元</div>
                    <div class="strategy-buttons">
                        <button class="strategy-btn active" onclick="setStrategy('A', 'high')">高价策略</button>
                        <button class="strategy-btn" onclick="setStrategy('A', 'low')">低价策略</button>
                    </div>
                </div>

                <div class="website website-b" id="websiteB">
                    <h3>🌐 乙网站</h3>
                    <div class="profit-display" id="profitB">1000万元</div>
                    <div class="strategy-buttons">
                        <button class="strategy-btn active" onclick="setStrategy('B', 'high')">高价策略</button>
                        <button class="strategy-btn" onclick="setStrategy('B', 'low')">低价策略</button>
                    </div>
                </div>
            </div>

            <div class="matrix-container">
                <h3 class="matrix-title">📊 收益矩阵表</h3>
                <table class="payoff-matrix" id="payoffMatrix">
                    <tr>
                        <th></th>
                        <th colspan="2">乙网站策略</th>
                    </tr>
                    <tr>
                        <th>甲网站策略</th>
                        <th>高价</th>
                        <th>低价</th>
                    </tr>
                    <tr>
                        <th>高价</th>
                        <td id="cell-high-high" onclick="highlightCell('high', 'high')">
                            <div class="profit-split">
                                <span class="profit-a">1000</span>
                                <span>,</span>
                                <span class="profit-b">1000</span>
                            </div>
                        </td>
                        <td id="cell-high-low" onclick="highlightCell('high', 'low')">
                            <div class="profit-split">
                                <span class="profit-a">200</span>
                                <span>,</span>
                                <span class="profit-b">1500</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th>低价</th>
                        <td id="cell-low-high" onclick="highlightCell('low', 'high')">
                            <div class="profit-split">
                                <span class="profit-a">1500</span>
                                <span>,</span>
                                <span class="profit-b">200</span>
                            </div>
                        </td>
                        <td id="cell-low-low" onclick="highlightCell('low', 'low')">
                            <div class="profit-split">
                                <span class="profit-a">700</span>
                                <span>,</span>
                                <span class="profit-b">700</span>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="400"></canvas>
            </div>

            <div class="controls">
                <button class="control-btn" onclick="startAnalysis()">🔍 开始分析</button>
                <button class="control-btn" onclick="showOptimalStrategy()">💡 显示最优策略</button>
                <button class="control-btn" onclick="resetGame()">🔄 重置游戏</button>
            </div>
        </div>

        <div class="analysis-section">
            <h3 class="analysis-title">🧠 理性分析过程</h3>
            <div class="step" id="step1">
                <h4>第一步：甲网站的思考</h4>
                <p>如果乙网站采用高价策略，甲网站选择高价得1000万，选择低价得1500万 → 应选择低价</p>
                <p>如果乙网站采用低价策略，甲网站选择高价得200万，选择低价得700万 → 应选择低价</p>
                <p><strong>结论：无论乙网站如何选择，甲网站的最优策略都是低价</strong></p>
            </div>
            <div class="step" id="step2">
                <h4>第二步：乙网站的思考</h4>
                <p>如果甲网站采用高价策略，乙网站选择高价得1000万，选择低价得1500万 → 应选择低价</p>
                <p>如果甲网站采用低价策略，乙网站选择高价得200万，选择低价得700万 → 应选择低价</p>
                <p><strong>结论：无论甲网站如何选择，乙网站的最优策略都是低价</strong></p>
            </div>
            <div class="step" id="step3">
                <h4>第三步：纳什均衡</h4>
                <p>由于双方都独立理性分析，最终结果必然是：<strong>双方都选择低价策略</strong></p>
                <p>这就是著名的"囚徒困境"，虽然合作（都选高价）对双方更有利，但理性选择导致了次优结果。</p>
            </div>
        </div>

        <div class="conclusion">
            <h3>🎯 答案解析</h3>
            <p><strong>正确答案：C - 甲采取低价策略，乙采取低价策略</strong></p>
            <p>这是一个典型的非合作博弈问题。在信息不对称和无法建立信任的情况下，</p>
            <p>理性的参与者会选择对自己最有利的策略，最终导致双方都选择低价策略。</p>
        </div>
    </div>

    <script>
        let currentStrategyA = 'high';
        let currentStrategyB = 'high';
        let analysisStep = 0;

        // 收益矩阵
        const payoffMatrix = {
            'high-high': [1000, 1000],
            'high-low': [200, 1500],
            'low-high': [1500, 200],
            'low-low': [700, 700]
        };

        // Canvas动画
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');

        function drawGameVisualization() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制网站图标
            drawWebsite(150, 200, '甲网站', '#ff6b6b', currentStrategyA);
            drawWebsite(650, 200, '乙网站', '#4834d4', currentStrategyB);
            
            // 绘制连接线和收益
            drawConnection();
            
            // 绘制当前收益
            const key = `${currentStrategyA}-${currentStrategyB}`;
            const profits = payoffMatrix[key];
            
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`甲网站收益: ${profits[0]}万元`, 200, 350);
            ctx.fillText(`乙网站收益: ${profits[1]}万元`, 600, 350);
        }

        function drawWebsite(x, y, name, color, strategy) {
            // 绘制网站圆圈
            ctx.beginPath();
            ctx.arc(x, y, 60, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 4;
            ctx.stroke();
            
            // 绘制网站名称
            ctx.fillStyle = '#fff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y - 10);
            
            // 绘制策略
            ctx.font = '14px Arial';
            ctx.fillText(strategy === 'high' ? '高价' : '低价', x, y + 10);
        }

        function drawConnection() {
            ctx.beginPath();
            ctx.moveTo(210, 200);
            ctx.lineTo(590, 200);
            ctx.strokeStyle = '#ddd';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.stroke();
            ctx.setLineDash([]);
            
            // 绘制箭头
            ctx.beginPath();
            ctx.moveTo(580, 195);
            ctx.lineTo(590, 200);
            ctx.lineTo(580, 205);
            ctx.strokeStyle = '#666';
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        function setStrategy(website, strategy) {
            if (website === 'A') {
                currentStrategyA = strategy;
                // 更新按钮状态
                const buttons = document.querySelectorAll('#websiteA .strategy-btn');
                buttons.forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');
            } else {
                currentStrategyB = strategy;
                // 更新按钮状态
                const buttons = document.querySelectorAll('#websiteB .strategy-btn');
                buttons.forEach(btn => btn.classList.remove('active'));
                event.target.classList.add('active');
            }
            
            updateProfits();
            drawGameVisualization();
            highlightMatrixCell();
        }

        function updateProfits() {
            const key = `${currentStrategyA}-${currentStrategyB}`;
            const profits = payoffMatrix[key];
            
            document.getElementById('profitA').textContent = `${profits[0]}万元`;
            document.getElementById('profitB').textContent = `${profits[1]}万元`;
        }

        function highlightMatrixCell() {
            // 清除所有高亮
            document.querySelectorAll('.payoff-matrix td').forEach(cell => {
                cell.classList.remove('highlight');
            });
            
            // 高亮当前策略组合
            const cellId = `cell-${currentStrategyA}-${currentStrategyB}`;
            document.getElementById(cellId).classList.add('highlight');
        }

        function highlightCell(strategyA, strategyB) {
            currentStrategyA = strategyA;
            currentStrategyB = strategyB;
            
            // 更新按钮状态
            updateButtonStates();
            updateProfits();
            drawGameVisualization();
            highlightMatrixCell();
        }

        function updateButtonStates() {
            // 更新甲网站按钮
            const buttonsA = document.querySelectorAll('#websiteA .strategy-btn');
            buttonsA.forEach(btn => {
                btn.classList.remove('active');
                if ((currentStrategyA === 'high' && btn.textContent === '高价策略') ||
                    (currentStrategyA === 'low' && btn.textContent === '低价策略')) {
                    btn.classList.add('active');
                }
            });
            
            // 更新乙网站按钮
            const buttonsB = document.querySelectorAll('#websiteB .strategy-btn');
            buttonsB.forEach(btn => {
                btn.classList.remove('active');
                if ((currentStrategyB === 'high' && btn.textContent === '高价策略') ||
                    (currentStrategyB === 'low' && btn.textContent === '低价策略')) {
                    btn.classList.add('active');
                }
            });
        }

        function startAnalysis() {
            analysisStep = 0;
            showNextStep();
        }

        function showNextStep() {
            if (analysisStep < 3) {
                analysisStep++;
                const step = document.getElementById(`step${analysisStep}`);
                step.classList.add('show');
                
                if (analysisStep < 3) {
                    setTimeout(showNextStep, 2000);
                }
            }
        }

        function showOptimalStrategy() {
            currentStrategyA = 'low';
            currentStrategyB = 'low';
            updateButtonStates();
            updateProfits();
            drawGameVisualization();
            highlightMatrixCell();
            
            // 显示提示
            alert('🎯 最优策略：双方都选择低价策略！\n这是纳什均衡点，虽然不是帕累托最优，但是稳定的均衡状态。');
        }

        function resetGame() {
            currentStrategyA = 'high';
            currentStrategyB = 'high';
            analysisStep = 0;
            
            updateButtonStates();
            updateProfits();
            drawGameVisualization();
            highlightMatrixCell();
            
            // 隐藏分析步骤
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('show');
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateProfits();
            drawGameVisualization();
            highlightMatrixCell();
        });
    </script>
</body>
</html>
