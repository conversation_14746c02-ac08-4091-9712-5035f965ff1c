<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双缓冲工作原理 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2em;
        }

        .content-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8em;
            color: #4a5568;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .demo-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            background: #f8fafc;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:active {
            transform: translateY(0);
        }

        .info-panel {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #f6ad55;
        }

        .parameters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .param-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .param-card:hover {
            transform: scale(1.05);
        }

        .param-value {
            font-size: 2em;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .param-label {
            color: #4a5568;
            font-size: 0.9em;
        }

        .explanation {
            background: #f7fafc;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid #e2e8f0;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            gap: 10px;
        }

        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.2);
        }

        .step.completed {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3em;
            margin-bottom: 20px;
            color: #2d3748;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            padding: 15px;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            border: 2px solid transparent;
        }

        .quiz-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .quiz-option.selected {
            border-color: #667eea;
            background: #edf2f7;
        }

        .quiz-option.correct {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .quiz-option.incorrect {
            border-color: #f56565;
            background: #fff5f5;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 双缓冲工作原理</h1>
            <p>通过动画和交互学习计算机系统的缓冲机制</p>
        </div>

        <div class="content-section">
            <h2 class="section-title">📚 基础概念</h2>
            <div class="explanation">
                <h3>什么是缓冲区？</h3>
                <p>缓冲区就像一个临时的"中转站"，用来暂存数据。想象一下：</p>
                <ul style="margin: 15px 0; padding-left: 20px;">
                    <li>🏪 <strong>单缓冲</strong>：只有一个收银台，顾客必须排队等待</li>
                    <li>🏪🏪 <strong>双缓冲</strong>：有两个收银台，可以同时服务，效率更高</li>
                </ul>
            </div>

            <div class="parameters">
                <div class="param-card">
                    <div class="param-value">10μs</div>
                    <div class="param-label">磁盘读取时间 (T)</div>
                </div>
                <div class="param-card">
                    <div class="param-value">6μs</div>
                    <div class="param-label">缓冲区传输时间 (M)</div>
                </div>
                <div class="param-card">
                    <div class="param-value">2μs</div>
                    <div class="param-label">CPU处理时间 (C)</div>
                </div>
                <div class="param-card">
                    <div class="param-value">10块</div>
                    <div class="param-label">文件大小</div>
                </div>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎮 动画演示</h2>
            <div class="demo-container">
                <canvas id="animationCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="startSingleBuffer()">单缓冲演示</button>
                <button class="btn btn-secondary" onclick="startDoubleBuffer()">双缓冲演示</button>
                <button class="btn btn-success" onclick="resetAnimation()">重置</button>
            </div>

            <div class="step-indicator">
                <div class="step" id="step1">1</div>
                <div class="step" id="step2">2</div>
                <div class="step" id="step3">3</div>
                <div class="step" id="step4">4</div>
                <div class="step" id="step5">5</div>
            </div>

            <div class="info-panel">
                <h3>📊 实时状态</h3>
                <p id="statusText">点击按钮开始演示</p>
                <p id="timeText">总用时：0μs</p>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🧮 计算过程详解</h2>
            <div class="explanation">
                <h3>单缓冲计算公式：</h3>
                <div style="background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 15px 0;">
                    <p><strong>第一块：</strong> T + M + C = 10 + 6 + 2 = 18μs</p>
                    <p><strong>后续9块：</strong> 每块用时 = max(T+M, C) + min(T+M, C) = (10+6) + 2 = 18μs</p>
                    <p><strong>总时间：</strong> 18 + 9×16 = 18 + 144 = 162μs</p>
                    <p style="color: #666; font-size: 0.9em;">注：后续块可以在处理前一块的同时读取，所以是max(T+M, C)</p>
                </div>

                <h3>双缓冲计算公式：</h3>
                <div style="background: #f0fff0; padding: 20px; border-radius: 10px; margin: 15px 0;">
                    <p><strong>第一块：</strong> T + M + C = 10 + 6 + 2 = 18μs</p>
                    <p><strong>后续9块：</strong> 每块用时 = max(T, C) + M = max(10, 2) + 6 = 16μs</p>
                    <p><strong>总时间：</strong> 18 + 9×10 = 18 + 90 = 108μs</p>
                    <p style="color: #666; font-size: 0.9em;">注：双缓冲可以让读取T和处理C并行进行</p>
                </div>

                <div class="info-panel">
                    <h3>💡 关键理解点</h3>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>并行性：</strong> 双缓冲允许同时进行读取、传输和处理</li>
                        <li><strong>瓶颈分析：</strong> 当T > C时，读取时间成为瓶颈</li>
                        <li><strong>效率提升：</strong> 节约时间 = 162 - 108 = 54μs</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title">🎯 互动测验</h2>
            <div class="quiz-question">
                根据题目条件，双缓冲需要花费多少时间？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 100μs</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">B. 108μs</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">C. 162μs</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 180μs</div>
            </div>
            <div id="quizResult" style="margin-top: 20px; font-weight: bold;"></div>

            <div class="quiz-question" style="margin-top: 30px;">
                双缓冲比单缓冲节约了多少时间？
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer2(this, false)">A. 44μs</div>
                <div class="quiz-option" onclick="selectAnswer2(this, false)">B. 50μs</div>
                <div class="quiz-option" onclick="selectAnswer2(this, true)">C. 54μs</div>
                <div class="quiz-option" onclick="selectAnswer2(this, false)">D. 60μs</div>
            </div>
            <div id="quizResult2" style="margin-top: 20px; font-weight: bold;"></div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎨 可视化时间轴</h2>
            <div class="demo-container">
                <canvas id="timelineCanvas" width="800" height="300"></canvas>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="showSingleTimeline()">单缓冲时间轴</button>
                <button class="btn btn-secondary" onclick="showDoubleTimeline()">双缓冲时间轴</button>
                <button class="btn btn-success" onclick="compareTimelines()">对比分析</button>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🏆 学习总结</h2>
            <div class="explanation">
                <h3>🎯 核心概念掌握</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); padding: 20px; border-radius: 15px;">
                        <h4>🔄 单缓冲特点</h4>
                        <ul>
                            <li>串行处理，效率较低</li>
                            <li>简单实现，资源占用少</li>
                            <li>适合简单的I/O操作</li>
                        </ul>
                    </div>
                    <div style="background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%); padding: 20px; border-radius: 15px;">
                        <h4>⚡ 双缓冲优势</h4>
                        <ul>
                            <li>并行处理，效率更高</li>
                            <li>减少等待时间</li>
                            <li>适合高频I/O操作</li>
                        </ul>
                    </div>
                </div>

                <div class="info-panel">
                    <h3>📈 性能对比</h3>
                    <div style="display: flex; justify-content: space-around; align-items: center; margin: 20px 0;">
                        <div style="text-align: center;">
                            <div style="font-size: 2em; color: #f44336;">162μs</div>
                            <div>单缓冲用时</div>
                        </div>
                        <div style="font-size: 3em; color: #4caf50;">VS</div>
                        <div style="text-align: center;">
                            <div style="font-size: 2em; color: #4caf50;">108μs</div>
                            <div>双缓冲用时</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 2em; color: #2196f3;">33%</div>
                            <div>效率提升</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('animationCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationState = {
            isRunning: false,
            currentStep: 0,
            totalTime: 0,
            mode: 'single', // 'single' or 'double'
            blocks: [],
            buffers: [],
            currentBlock: 0
        };

        // 初始化
        function init() {
            // 创建10个数据块
            animationState.blocks = [];
            for (let i = 0; i < 10; i++) {
                animationState.blocks.push({
                    id: i + 1,
                    x: 50,
                    y: 100 + i * 25,
                    processed: false,
                    inBuffer: false,
                    inUserArea: false
                });
            }
            
            // 初始化缓冲区
            animationState.buffers = [
                { id: 1, x: 300, y: 150, occupied: false, blockId: null },
                { id: 2, x: 300, y: 250, occupied: false, blockId: null }
            ];
            
            draw();
        }

        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景区域
            drawArea('磁盘区', 20, 80, 150, 280, '#e3f2fd');
            drawArea('缓冲区', 250, 120, 150, 200, '#f3e5f5');
            drawArea('用户区', 500, 150, 150, 140, '#e8f5e8');
            drawArea('CPU处理', 680, 180, 100, 80, '#fff3e0');
            
            // 绘制数据块
            animationState.blocks.forEach(block => {
                drawBlock(block);
            });
            
            // 绘制缓冲区
            animationState.buffers.forEach((buffer, index) => {
                if (animationState.mode === 'single' && index > 0) return;
                drawBuffer(buffer, index);
            });
            
            // 绘制箭头和流程
            drawArrows();
        }

        function drawArea(title, x, y, width, height, color) {
            ctx.fillStyle = color;
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = '#666';
            ctx.strokeRect(x, y, width, height);
            
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(title, x + width/2, y - 10);
        }

        function drawBlock(block) {
            let color = '#2196f3';
            if (block.processed) color = '#4caf50';
            else if (block.inUserArea) color = '#ff9800';
            else if (block.inBuffer) color = '#9c27b0';
            
            ctx.fillStyle = color;
            ctx.fillRect(block.x, block.y, 40, 20);
            ctx.strokeStyle = '#333';
            ctx.strokeRect(block.x, block.y, 40, 20);
            
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(block.id.toString(), block.x + 20, block.y + 14);
        }

        function drawBuffer(buffer, index) {
            let color = buffer.occupied ? '#9c27b0' : '#f5f5f5';
            ctx.fillStyle = color;
            ctx.fillRect(buffer.x, buffer.y, 80, 40);
            ctx.strokeStyle = '#333';
            ctx.strokeRect(buffer.x, buffer.y, 80, 40);
            
            ctx.fillStyle = '#333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`缓冲${index + 1}`, buffer.x + 40, buffer.y - 5);
            
            if (buffer.occupied && buffer.blockId) {
                ctx.fillStyle = 'white';
                ctx.fillText(`块${buffer.blockId}`, buffer.x + 40, buffer.y + 25);
            }
        }

        function drawArrows() {
            // 磁盘到缓冲区的箭头
            drawArrow(200, 200, 250, 180, '#2196f3');
            // 缓冲区到用户区的箭头
            drawArrow(400, 180, 500, 180, '#ff9800');
            // 用户区到CPU的箭头
            drawArrow(650, 200, 680, 200, '#4caf50');
        }

        function drawArrow(x1, y1, x2, y2, color) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
            
            // 箭头头部
            const angle = Math.atan2(y2 - y1, x2 - x1);
            ctx.beginPath();
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle - Math.PI/6), y2 - 10 * Math.sin(angle - Math.PI/6));
            ctx.moveTo(x2, y2);
            ctx.lineTo(x2 - 10 * Math.cos(angle + Math.PI/6), y2 - 10 * Math.sin(angle + Math.PI/6));
            ctx.stroke();
        }

        function updateStatus(text) {
            document.getElementById('statusText').textContent = text;
        }

        function updateTime(time) {
            document.getElementById('timeText').textContent = `总用时：${time}μs`;
            animationState.totalTime = time;
        }

        function updateStep(step) {
            // 重置所有步骤
            for (let i = 1; i <= 5; i++) {
                const stepEl = document.getElementById(`step${i}`);
                stepEl.classList.remove('active', 'completed');
            }
            
            // 设置当前步骤
            if (step > 0) {
                document.getElementById(`step${step}`).classList.add('active');
            }
            
            // 设置已完成的步骤
            for (let i = 1; i < step; i++) {
                document.getElementById(`step${i}`).classList.add('completed');
            }
        }

        async function startSingleBuffer() {
            if (animationState.isRunning) return;
            
            animationState.isRunning = true;
            animationState.mode = 'single';
            animationState.currentBlock = 0;
            animationState.totalTime = 0;
            
            init();
            updateStatus('开始单缓冲演示...');
            updateStep(1);
            
            // 单缓冲逻辑：T + M + C 的串行处理
            for (let i = 0; i < 10; i++) {
                updateStep(i % 3 + 1);
                
                // 第一步：从磁盘读取到缓冲区 (T = 10μs)
                updateStatus(`正在读取第${i+1}块数据到缓冲区... (10μs)`);
                await animateBlockToBuffer(i, 0);
                animationState.totalTime += 10;
                updateTime(animationState.totalTime);
                
                // 第二步：从缓冲区传输到用户区 (M = 6μs)
                updateStatus(`正在传输第${i+1}块数据到用户区... (6μs)`);
                await animateBlockToUser(i);
                animationState.totalTime += 6;
                updateTime(animationState.totalTime);
                
                // 第三步：CPU处理 (C = 2μs)
                updateStatus(`CPU正在处理第${i+1}块数据... (2μs)`);
                await animateProcessing(i);
                animationState.totalTime += 2;
                updateTime(animationState.totalTime);
            }
            
            updateStatus(`单缓冲完成！总用时：${animationState.totalTime}μs`);
            updateStep(0);
            animationState.isRunning = false;
        }

        async function startDoubleBuffer() {
            if (animationState.isRunning) return;
            
            animationState.isRunning = true;
            animationState.mode = 'double';
            animationState.currentBlock = 0;
            animationState.totalTime = 0;
            
            init();
            updateStatus('开始双缓冲演示...');
            updateStep(1);
            
            // 双缓冲逻辑：并行处理
            // 第一块特殊处理
            updateStatus('读取第1块数据到缓冲区1... (10μs)');
            await animateBlockToBuffer(0, 0);
            animationState.totalTime += 10;
            
            updateStatus('传输第1块数据到用户区... (6μs)');
            await animateBlockToUser(0);
            animationState.totalTime += 6;
            
            updateStatus('CPU处理第1块数据... (2μs)');
            await animateProcessing(0);
            animationState.totalTime += 2;
            updateTime(animationState.totalTime);
            
            // 后续块并行处理
            for (let i = 1; i < 10; i++) {
                updateStep((i % 3) + 2);
                
                // 并行：读取下一块 + 处理当前块
                updateStatus(`并行处理：读取第${i+1}块 & 处理第${i}块... (10μs)`);
                
                // 同时进行读取和处理
                const readPromise = animateBlockToBuffer(i, i % 2);
                const processTime = Math.max(10, 2); // max(T, C)
                
                await Promise.all([readPromise, sleep(processTime * 50)]);
                animationState.totalTime += 10; // 因为T > C，所以时间以T为准
                
                updateStatus(`传输第${i+1}块数据到用户区... (6μs)`);
                await animateBlockToUser(i);
                animationState.totalTime += 6;
                
                await animateProcessing(i);
                updateTime(animationState.totalTime);
            }
            
            updateStatus(`双缓冲完成！总用时：${animationState.totalTime}μs，比单缓冲节约了${162 - animationState.totalTime}μs`);
            updateStep(0);
            animationState.isRunning = false;
        }

        async function animateBlockToBuffer(blockIndex, bufferIndex) {
            const block = animationState.blocks[blockIndex];
            const buffer = animationState.buffers[bufferIndex];
            
            // 动画移动到缓冲区
            const startX = block.x;
            const startY = block.y;
            const endX = buffer.x + 20;
            const endY = buffer.y + 10;
            
            for (let t = 0; t <= 1; t += 0.05) {
                block.x = startX + (endX - startX) * t;
                block.y = startY + (endY - startY) * t;
                draw();
                await sleep(20);
            }
            
            block.inBuffer = true;
            buffer.occupied = true;
            buffer.blockId = block.id;
            draw();
        }

        async function animateBlockToUser(blockIndex) {
            const block = animationState.blocks[blockIndex];
            
            // 动画移动到用户区
            const startX = block.x;
            const startY = block.y;
            const endX = 550;
            const endY = 200;
            
            for (let t = 0; t <= 1; t += 0.05) {
                block.x = startX + (endX - startX) * t;
                block.y = startY + (endY - startY) * t;
                draw();
                await sleep(15);
            }
            
            block.inUserArea = true;
            // 清空缓冲区
            animationState.buffers.forEach(buffer => {
                if (buffer.blockId === block.id) {
                    buffer.occupied = false;
                    buffer.blockId = null;
                }
            });
            draw();
        }

        async function animateProcessing(blockIndex) {
            const block = animationState.blocks[blockIndex];
            
            // 动画移动到CPU处理区
            const startX = block.x;
            const startY = block.y;
            const endX = 720;
            const endY = 210;
            
            for (let t = 0; t <= 1; t += 0.1) {
                block.x = startX + (endX - startX) * t;
                block.y = startY + (endY - startY) * t;
                draw();
                await sleep(10);
            }
            
            // 处理完成效果
            block.processed = true;
            for (let i = 0; i < 5; i++) {
                ctx.fillStyle = `rgba(76, 175, 80, ${0.8 - i * 0.15})`;
                ctx.beginPath();
                ctx.arc(block.x + 20, block.y + 10, 30 + i * 10, 0, Math.PI * 2);
                ctx.fill();
                draw();
                await sleep(50);
            }
            
            draw();
        }

        function resetAnimation() {
            animationState.isRunning = false;
            animationState.currentStep = 0;
            animationState.totalTime = 0;
            init();
            updateStatus('点击按钮开始演示');
            updateTime(0);
            updateStep(0);
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 测验功能
        function selectAnswer(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'incorrect');
            });

            // 标记当前选择
            element.classList.add('selected');

            setTimeout(() => {
                if (isCorrect) {
                    element.classList.add('correct');
                    document.getElementById('quizResult').innerHTML = '🎉 正确！双缓冲确实需要108μs';
                    document.getElementById('quizResult').style.color = '#4caf50';
                } else {
                    element.classList.add('incorrect');
                    document.getElementById('quizResult').innerHTML = '❌ 不正确，请重新思考计算过程';
                    document.getElementById('quizResult').style.color = '#f44336';
                    // 显示正确答案
                    setTimeout(() => {
                        document.querySelectorAll('.quiz-option')[1].classList.add('correct');
                    }, 1000);
                }
            }, 500);
        }

        function selectAnswer2(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-options')[1].querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'incorrect');
            });

            // 标记当前选择
            element.classList.add('selected');

            setTimeout(() => {
                if (isCorrect) {
                    element.classList.add('correct');
                    document.getElementById('quizResult2').innerHTML = '🎉 正确！节约了162-108=54μs';
                    document.getElementById('quizResult2').style.color = '#4caf50';
                } else {
                    element.classList.add('incorrect');
                    document.getElementById('quizResult2').innerHTML = '❌ 不正确，计算：162μs - 108μs = 54μs';
                    document.getElementById('quizResult2').style.color = '#f44336';
                    // 显示正确答案
                    setTimeout(() => {
                        document.querySelectorAll('.quiz-options')[1].querySelectorAll('.quiz-option')[2].classList.add('correct');
                    }, 1000);
                }
            }, 500);
        }

        // 时间轴可视化
        const timelineCanvas = document.getElementById('timelineCanvas');
        const timelineCtx = timelineCanvas.getContext('2d');

        function showSingleTimeline() {
            timelineCtx.clearRect(0, 0, timelineCanvas.width, timelineCanvas.height);

            // 绘制标题
            timelineCtx.fillStyle = '#333';
            timelineCtx.font = 'bold 16px Arial';
            timelineCtx.textAlign = 'center';
            timelineCtx.fillText('单缓冲时间轴 (总计162μs)', timelineCanvas.width/2, 30);

            let currentTime = 0;
            const scale = 4; // 时间缩放比例
            const startY = 80;
            const blockHeight = 30;

            // 绘制时间刻度
            for (let i = 0; i <= 180; i += 20) {
                const x = 50 + i * scale;
                timelineCtx.strokeStyle = '#ddd';
                timelineCtx.beginPath();
                timelineCtx.moveTo(x, startY - 10);
                timelineCtx.lineTo(x, startY + 200);
                timelineCtx.stroke();

                timelineCtx.fillStyle = '#666';
                timelineCtx.font = '10px Arial';
                timelineCtx.textAlign = 'center';
                timelineCtx.fillText(i + 'μs', x, startY - 15);
            }

            // 绘制每个数据块的处理过程
            for (let i = 0; i < 3; i++) { // 只显示前3块作为示例
                const y = startY + i * 60;

                // 读取阶段 (T = 10μs)
                drawTimeBlock(50 + currentTime * scale, y, 10 * scale, blockHeight, '#2196f3', `块${i+1}读取(10μs)`);

                // 传输阶段 (M = 6μs)
                drawTimeBlock(50 + (currentTime + 10) * scale, y, 6 * scale, blockHeight, '#ff9800', `块${i+1}传输(6μs)`);

                // 处理阶段 (C = 2μs)
                drawTimeBlock(50 + (currentTime + 16) * scale, y, 2 * scale, blockHeight, '#4caf50', `块${i+1}处理(2μs)`);

                currentTime += 18;
            }

            // 显示省略号
            timelineCtx.fillStyle = '#333';
            timelineCtx.font = '14px Arial';
            timelineCtx.textAlign = 'center';
            timelineCtx.fillText('... (共10块)', timelineCanvas.width/2, startY + 220);
        }

        function showDoubleTimeline() {
            timelineCtx.clearRect(0, 0, timelineCanvas.width, timelineCanvas.height);

            // 绘制标题
            timelineCtx.fillStyle = '#333';
            timelineCtx.font = 'bold 16px Arial';
            timelineCtx.textAlign = 'center';
            timelineCtx.fillText('双缓冲时间轴 (总计108μs)', timelineCanvas.width/2, 30);

            const scale = 6; // 时间缩放比例
            const startY = 80;
            const blockHeight = 25;

            // 绘制时间刻度
            for (let i = 0; i <= 120; i += 10) {
                const x = 50 + i * scale;
                timelineCtx.strokeStyle = '#ddd';
                timelineCtx.beginPath();
                timelineCtx.moveTo(x, startY - 10);
                timelineCtx.lineTo(x, startY + 180);
                timelineCtx.stroke();

                timelineCtx.fillStyle = '#666';
                timelineCtx.font = '10px Arial';
                timelineCtx.textAlign = 'center';
                timelineCtx.fillText(i + 'μs', x, startY - 15);
            }

            // 第一块特殊处理
            drawTimeBlock(50, startY, 10 * scale, blockHeight, '#2196f3', '块1读取');
            drawTimeBlock(50 + 10 * scale, startY, 6 * scale, blockHeight, '#ff9800', '块1传输');
            drawTimeBlock(50 + 16 * scale, startY, 2 * scale, blockHeight, '#4caf50', '块1处理');

            // 后续块并行处理
            let currentTime = 18;
            for (let i = 1; i < 4; i++) { // 显示前几块作为示例
                const y = startY + i * 40;

                // 读取和处理并行
                drawTimeBlock(50 + currentTime * scale, y, 10 * scale, blockHeight, '#2196f3', `块${i+1}读取`);
                drawTimeBlock(50 + currentTime * scale, y + blockHeight + 5, 2 * scale, blockHeight, '#4caf50', `块${i}处理`, true);

                // 传输
                drawTimeBlock(50 + (currentTime + 10) * scale, y, 6 * scale, blockHeight, '#ff9800', `块${i+1}传输`);

                currentTime += 10;
            }

            // 显示省略号
            timelineCtx.fillStyle = '#333';
            timelineCtx.font = '14px Arial';
            timelineCtx.textAlign = 'center';
            timelineCtx.fillText('... (并行处理)', timelineCanvas.width/2, startY + 200);
        }

        function drawTimeBlock(x, y, width, height, color, text, isDashed = false) {
            timelineCtx.fillStyle = color;
            timelineCtx.fillRect(x, y, width, height);

            if (isDashed) {
                timelineCtx.setLineDash([5, 5]);
            }
            timelineCtx.strokeStyle = '#333';
            timelineCtx.strokeRect(x, y, width, height);
            timelineCtx.setLineDash([]);

            // 添加文字
            timelineCtx.fillStyle = 'white';
            timelineCtx.font = '10px Arial';
            timelineCtx.textAlign = 'center';
            if (width > 40) {
                timelineCtx.fillText(text, x + width/2, y + height/2 + 3);
            }
        }

        function compareTimelines() {
            timelineCtx.clearRect(0, 0, timelineCanvas.width, timelineCanvas.height);

            // 绘制对比标题
            timelineCtx.fillStyle = '#333';
            timelineCtx.font = 'bold 16px Arial';
            timelineCtx.textAlign = 'center';
            timelineCtx.fillText('单缓冲 vs 双缓冲 对比', timelineCanvas.width/2, 30);

            const scale = 4;
            const startY = 60;

            // 单缓冲 (上半部分)
            timelineCtx.fillStyle = '#f44336';
            timelineCtx.font = '12px Arial';
            timelineCtx.textAlign = 'left';
            timelineCtx.fillText('单缓冲 (162μs):', 50, startY + 20);

            drawTimeBlock(150, startY, 162 * scale, 30, '#f44336', '162μs');

            // 双缓冲 (下半部分)
            timelineCtx.fillStyle = '#4caf50';
            timelineCtx.fillText('双缓冲 (108μs):', 50, startY + 80);

            drawTimeBlock(150, startY + 60, 108 * scale, 30, '#4caf50', '108μs');

            // 节约的时间
            timelineCtx.fillStyle = '#ff9800';
            timelineCtx.fillText('节约时间 (54μs):', 50, startY + 140);
            drawTimeBlock(150 + 108 * scale, startY + 120, 54 * scale, 30, '#ff9800', '54μs');

            // 添加百分比
            timelineCtx.fillStyle = '#333';
            timelineCtx.font = 'bold 14px Arial';
            timelineCtx.textAlign = 'center';
            timelineCtx.fillText('效率提升: 33.3%', timelineCanvas.width/2, startY + 180);
        }

        // 初始化
        init();
    </script>
</body>
</html>
