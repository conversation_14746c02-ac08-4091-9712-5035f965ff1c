<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式互动学习 - 零基础入门</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            margin-bottom: 40px;
        }

        .question-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: slideInUp 0.8s ease-out;
        }

        .question-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #333;
            margin-bottom: 30px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        #gameCanvas:hover {
            transform: scale(1.02);
        }

        .pattern-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .pattern-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .pattern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .pattern-card:hover::before {
            left: 100%;
        }

        .pattern-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .pattern-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .pattern-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #333;
        }

        .pattern-desc {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .pattern-examples {
            font-size: 0.9rem;
            color: #888;
            font-style: italic;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #667eea;
            animation: fadeIn 0.5s ease-out;
        }

        .explanation h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #666;
            line-height: 1.7;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .bounce {
            animation: bounce 1s ease-in-out;
        }

        .score {
            text-align: center;
            font-size: 1.2rem;
            color: white;
            margin: 20px 0;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 设计模式互动学习</h1>
            <p class="subtitle">通过动画和游戏理解三大设计模式类型</p>
        </div>

        <div class="question-card">
            <div class="question-text">
                <strong>题目：</strong>按照设计模式的目的进行划分，现有的设计模式可以分为三类。其中<span class="highlight">创建型模式</span>通过采用抽象类所定义的接口，封装了系统中对象如何创建、组合等信息，其代表有（）模式等；<span class="highlight">（请作答此空）</span>模式主要用于如何组合已有的类和对象以获得更大的结构，其代表有 Adapter 模式等；<span class="highlight">（）</span>模式主要用于对象之间的职责及其提供服务的分配方式，其代表有（）模式等。
            </div>
        </div>

        <div class="canvas-container">
            <canvas id="gameCanvas" width="800" height="500"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="startAnimation()">🎬 开始动画演示</button>
            <button class="btn" onclick="resetGame()">🔄 重新开始</button>
            <button class="btn" onclick="showAnswer()">💡 查看答案</button>
        </div>

        <div class="score" id="scoreDisplay">得分: 0 | 正确率: 0%</div>

        <div class="pattern-grid">
            <div class="pattern-card" onclick="selectPattern('creational')" id="creational">
                <span class="pattern-icon">🏭</span>
                <h3 class="pattern-title">创建型模式</h3>
                <p class="pattern-desc">负责对象的创建和初始化，封装对象创建的复杂性</p>
                <p class="pattern-examples">代表：Singleton, Factory, Builder</p>
            </div>
            <div class="pattern-card" onclick="selectPattern('structural')" id="structural">
                <span class="pattern-icon">🔧</span>
                <h3 class="pattern-title">结构型模式</h3>
                <p class="pattern-desc">处理类和对象的组合，形成更大的结构</p>
                <p class="pattern-examples">代表：Adapter, Decorator, Facade</p>
            </div>
            <div class="pattern-card" onclick="selectPattern('behavioral')" id="behavioral">
                <span class="pattern-icon">🤝</span>
                <h3 class="pattern-title">行为型模式</h3>
                <p class="pattern-desc">关注对象间的通信和职责分配</p>
                <p class="pattern-examples">代表：Observer, Strategy, Visitor</p>
            </div>
        </div>

        <div class="explanation" id="explanation" style="display: none;">
            <h3>💡 知识点解析</h3>
            <p id="explanationText"></p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        let gameState = {
            score: 0,
            attempts: 0,
            currentAnimation: null,
            selectedPattern: null
        };

        // 动画对象
        const animations = {
            creational: {
                objects: [],
                factory: { x: 100, y: 250, size: 60, angle: 0 }
            },
            structural: {
                components: [],
                adapter: { x: 400, y: 250, rotation: 0 }
            },
            behavioral: {
                agents: [],
                messages: []
            }
        };

        function initCanvas() {
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击下方卡片选择设计模式类型', canvas.width/2, 50);
            
            // 绘制提示
            ctx.font = '16px Arial';
            ctx.fillStyle = '#666';
            ctx.fillText('观察动画理解每种模式的特点', canvas.width/2, 80);
        }

        function drawCreationalPattern() {
            // 清空画布
            ctx.fillStyle = '#f0f8ff';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🏭 创建型模式 - 对象工厂', canvas.width/2, 40);
            
            // 工厂
            const factory = animations.creational.factory;
            factory.angle += 0.05;
            
            ctx.save();
            ctx.translate(factory.x, factory.y);
            ctx.rotate(factory.angle);
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(-factory.size/2, -factory.size/2, factory.size, factory.size);
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🏭', 0, 8);
            ctx.restore();
            
            // 生产对象
            if (Math.random() < 0.02) {
                animations.creational.objects.push({
                    x: factory.x + 80,
                    y: factory.y,
                    vx: 2 + Math.random() * 2,
                    vy: (Math.random() - 0.5) * 2,
                    type: Math.floor(Math.random() * 3),
                    life: 100
                });
            }
            
            // 绘制生产的对象
            animations.creational.objects.forEach((obj, index) => {
                obj.x += obj.vx;
                obj.y += obj.vy;
                obj.life--;
                
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1'];
                const shapes = ['●', '■', '▲'];
                
                ctx.fillStyle = colors[obj.type];
                ctx.font = '24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(shapes[obj.type], obj.x, obj.y);
                
                if (obj.life <= 0 || obj.x > canvas.width) {
                    animations.creational.objects.splice(index, 1);
                }
            });
            
            // 说明文字
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('工厂负责创建不同类型的对象', 50, canvas.height - 60);
            ctx.fillText('封装了对象创建的复杂逻辑', 50, canvas.height - 40);
            ctx.fillText('代表模式：Singleton, Factory, Builder', 50, canvas.height - 20);
        }

        function drawStructuralPattern() {
            ctx.fillStyle = '#fff8e1';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🔧 结构型模式 - 组件组合', canvas.width/2, 40);
            
            // 适配器动画
            const adapter = animations.structural.adapter;
            adapter.rotation += 0.03;
            
            // 旧接口
            ctx.fillStyle = '#FF9800';
            ctx.fillRect(100, 200, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('旧接口', 140, 235);
            
            // 适配器
            ctx.save();
            ctx.translate(300, 230);
            ctx.rotate(adapter.rotation);
            ctx.fillStyle = '#9C27B0';
            ctx.fillRect(-40, -30, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('适配器', 0, 5);
            ctx.restore();
            
            // 新接口
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(500, 200, 80, 60);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('新接口', 540, 235);
            
            // 连接线
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(180, 230);
            ctx.lineTo(260, 230);
            ctx.moveTo(340, 230);
            ctx.lineTo(500, 230);
            ctx.stroke();
            
            // 箭头
            ctx.fillStyle = '#333';
            ctx.beginPath();
            ctx.moveTo(250, 225);
            ctx.lineTo(260, 230);
            ctx.lineTo(250, 235);
            ctx.fill();
            
            ctx.beginPath();
            ctx.moveTo(490, 225);
            ctx.lineTo(500, 230);
            ctx.lineTo(490, 235);
            ctx.fill();
            
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('适配器连接不兼容的接口', 50, canvas.height - 60);
            ctx.fillText('组合现有组件形成新结构', 50, canvas.height - 40);
            ctx.fillText('代表模式：Adapter, Decorator, Facade', 50, canvas.height - 20);
        }

        function drawBehavioralPattern() {
            ctx.fillStyle = '#f3e5f5';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            ctx.fillStyle = '#333';
            ctx.font = 'bold 20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('🤝 行为型模式 - 对象协作', canvas.width/2, 40);
            
            // 观察者模式动画
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 主题对象
            ctx.fillStyle = '#E91E63';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 40, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('主题', centerX, centerY + 5);
            
            // 观察者
            const observers = [
                {x: centerX - 150, y: centerY - 100, name: '观察者1'},
                {x: centerX + 150, y: centerY - 100, name: '观察者2'},
                {x: centerX - 150, y: centerY + 100, name: '观察者3'},
                {x: centerX + 150, y: centerY + 100, name: '观察者4'}
            ];
            
            observers.forEach((obs, index) => {
                ctx.fillStyle = '#2196F3';
                ctx.beginPath();
                ctx.arc(obs.x, obs.y, 25, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = 'white';
                ctx.font = '10px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(obs.name, obs.x, obs.y + 3);
                
                // 消息传递动画
                const time = Date.now() * 0.005;
                const phase = (time + index * Math.PI / 2) % (Math.PI * 2);
                if (phase < Math.PI) {
                    const progress = Math.sin(phase);
                    const msgX = centerX + (obs.x - centerX) * progress;
                    const msgY = centerY + (obs.y - centerY) * progress;
                    
                    ctx.fillStyle = '#FFC107';
                    ctx.beginPath();
                    ctx.arc(msgX, msgY, 5, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                // 连接线
                ctx.strokeStyle = '#ddd';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(obs.x, obs.y);
                ctx.stroke();
                ctx.setLineDash([]);
            });
            
            ctx.fillStyle = '#666';
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('主题通知所有观察者状态变化', 50, canvas.height - 60);
            ctx.fillText('定义对象间的通信方式', 50, canvas.height - 40);
            ctx.fillText('代表模式：Observer, Strategy, Visitor', 50, canvas.height - 20);
        }

        function animate() {
            if (gameState.currentAnimation === 'creational') {
                drawCreationalPattern();
            } else if (gameState.currentAnimation === 'structural') {
                drawStructuralPattern();
            } else if (gameState.currentAnimation === 'behavioral') {
                drawBehavioralPattern();
            }
            
            if (gameState.currentAnimation) {
                requestAnimationFrame(animate);
            }
        }

        function selectPattern(pattern) {
            gameState.selectedPattern = pattern;
            gameState.currentAnimation = pattern;
            gameState.attempts++;
            
            // 添加选中效果
            document.querySelectorAll('.pattern-card').forEach(card => {
                card.style.transform = '';
                card.style.boxShadow = '';
            });
            
            const selectedCard = document.getElementById(pattern);
            selectedCard.classList.add('bounce');
            selectedCard.style.transform = 'scale(1.05)';
            selectedCard.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.3)';
            
            setTimeout(() => selectedCard.classList.remove('bounce'), 1000);
            
            animate();
            showExplanation(pattern);
            updateScore(pattern);
        }

        function showExplanation(pattern) {
            const explanations = {
                creational: '创建型模式主要解决对象创建的问题。它通过封装对象的创建过程，使系统独立于对象的创建、组合和表示方式。常见的创建型模式包括单例模式(Singleton)、工厂模式(Factory)、建造者模式(Builder)等。',
                structural: '结构型模式主要处理类和对象的组合问题。它通过组合类和对象来形成更大的结构，同时保持结构的灵活性和效率。Adapter模式就是典型的结构型模式，它允许接口不兼容的类一起工作。',
                behavioral: '行为型模式主要关注对象之间的通信和职责分配。它定义了对象间的交互方式和职责分配，使系统更加灵活和可维护。Observer模式、Strategy模式、Visitor模式都是行为型模式的代表。'
            };
            
            document.getElementById('explanation').style.display = 'block';
            document.getElementById('explanationText').textContent = explanations[pattern];
        }

        function updateScore(pattern) {
            if (pattern === 'structural') {
                gameState.score += 10;
            }
            
            const accuracy = gameState.attempts > 0 ? Math.round((gameState.score / (gameState.attempts * 10)) * 100) : 0;
            document.getElementById('scoreDisplay').textContent = `得分: ${gameState.score} | 正确率: ${accuracy}%`;
        }

        function startAnimation() {
            if (!gameState.currentAnimation) {
                alert('请先选择一个设计模式类型！');
                return;
            }
            animate();
        }

        function resetGame() {
            gameState = {
                score: 0,
                attempts: 0,
                currentAnimation: null,
                selectedPattern: null
            };
            
            document.querySelectorAll('.pattern-card').forEach(card => {
                card.style.transform = '';
                card.style.boxShadow = '';
            });
            
            document.getElementById('explanation').style.display = 'none';
            document.getElementById('scoreDisplay').textContent = '得分: 0 | 正确率: 0%';
            
            initCanvas();
        }

        function showAnswer() {
            alert('正确答案是：结构型模式\n\n解析：\n创建型模式 - 负责对象创建\n结构型模式 - 负责对象组合\n行为型模式 - 负责对象交互');
            
            // 高亮正确答案
            const correctCard = document.getElementById('structural');
            correctCard.style.background = 'linear-gradient(45deg, #4CAF50, #8BC34A)';
            correctCard.style.color = 'white';
            
            setTimeout(() => {
                correctCard.style.background = '';
                correctCard.style.color = '';
            }, 3000);
        }

        // 初始化
        initCanvas();
    </script>
</body>
</html>
