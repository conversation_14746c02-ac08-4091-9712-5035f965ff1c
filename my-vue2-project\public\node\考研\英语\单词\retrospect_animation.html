<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Retrospect</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #f0f7ff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #333;
            overflow: hidden;
        }
        #animation-container {
            text-align: center;
            position: relative;
        }
        canvas {
            background-color: #ffffff;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }
        #instruction {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 16px;
            color: #555;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 8px 15px;
            border-radius: 15px;
            animation: fadeIn 1s ease-in-out;
            opacity: 0;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="animation-container">
        <canvas id="word-canvas" width="800" height="600"></canvas>
        <div id="instruction">点击屏幕继续动画</div>
    </div>

    <script>
        const canvas = document.getElementById('word-canvas');
        const ctx = canvas.getContext('2d');
        const instructionEl = document.getElementById('instruction');

        let stage = 0;
        let animationFrame;
        const totalStages = 6;

        const colors = {
            primary: '#4A90E2', // A calming blue
            secondary: '#F5A623', // A warm orange
            dark: '#4A4A4A',
            light: '#F8F9FA',
            green: '#50E3C2', // A fresh teal
        };

        // Animation properties
        let alpha = 0;
        let wordPos = { x: canvas.width / 2, y: canvas.height / 2 };
        let retroPos = { x: canvas.width / 2 - 120, y: canvas.height / 2 };
        let spectPos = { x: canvas.width / 2 + 120, y: canvas.height / 2 };
        let personAngle = 0;
        let eye = { pupilSize: 5 };
        let memories = [];

        // Helper functions
        function lerp(start, end, t) {
            return start * (1 - t) + end * t;
        }

        function drawText(text, x, y, size = 60, color = colors.dark, a = 1) {
            ctx.save();
            ctx.globalAlpha = a;
            ctx.font = `bold ${size}px 'Segoe UI'`;
            ctx.fillStyle = color;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x, y);
            ctx.restore();
        }

        function drawExplanation(text, x, y, a = 1) {
            drawText(text, x, y, 24, colors.primary, a);
        }

        function drawPersonLookingBack(x, y, angle, a = 1) {
            ctx.save();
            ctx.globalAlpha = a;
            ctx.translate(x, y);
            
            // Body
            ctx.fillStyle = colors.primary;
            ctx.beginPath();
            ctx.moveTo(-20, 50);
            ctx.lineTo(20, 50);
            ctx.lineTo(10, -40);
            ctx.lineTo(-10, -40);
            ctx.closePath();
            ctx.fill();

            // Head
            ctx.fillStyle = '#F2D6B5'; // skin color
            ctx.beginPath();
            ctx.arc(0, -60, 30, 0, Math.PI * 2);
            ctx.fill();
            
            // Eye looking back
            ctx.save();
            ctx.translate(Math.cos(angle) * 15, -65);
            ctx.rotate(angle);
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.ellipse(0, 0, 8, 5, 0, 0, Math.PI * 2);
            ctx.fill();
            ctx.fillStyle = 'black';
            ctx.beginPath();
            ctx.arc(2, 0, 2, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();

            ctx.restore();
        }

        function drawEye(x, y, a = 1) {
            ctx.save();
            ctx.globalAlpha = a;
            // Eye shape
            ctx.fillStyle = 'white';
            ctx.strokeStyle = colors.dark;
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(x - 60, y);
            ctx.quadraticCurveTo(x, y - 60, x + 60, y);
            ctx.quadraticCurveTo(x, y + 60, x - 60, y);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();

            // Iris
            ctx.fillStyle = colors.green;
            ctx.beginPath();
            ctx.arc(x, y, 25, 0, Math.PI * 2);
            ctx.fill();

            // Pupil
            ctx.fillStyle = 'black';
            ctx.beginPath();
            ctx.arc(x, y, eye.pupilSize, 0, Math.PI * 2);
            ctx.fill();
            ctx.restore();
        }
        
        function drawMemory(x, y, w, h, text, a = 1) {
            ctx.save();
            ctx.globalAlpha = a;
            ctx.fillStyle = 'white';
            ctx.strokeStyle = colors.secondary;
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.rect(x - w/2, y - h/2, w, h);
            ctx.fill();
            ctx.stroke();
            
            // Simplified drawing for memory
            drawText(text, x, y, 30, colors.dark, a);

            ctx.restore();
        }

        // Animation Stages
        function intro() {
            alpha = lerp(alpha, 1, 0.05);
            drawText('retrospect', wordPos.x, wordPos.y, 80, colors.dark, alpha);
            if (alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }

        function splitWord() {
            instructionEl.style.opacity = 0;
            let targetRetroX = canvas.width / 2 - 140;
            let targetSpectX = canvas.width / 2 + 130;
            retroPos.x = lerp(retroPos.x, targetRetroX, 0.1);
            spectPos.x = lerp(spectPos.x, targetSpectX, 0.1);

            drawText('retro', retroPos.x, retroPos.y, 80, colors.primary);
            drawText('spect', spectPos.x, spectPos.y, 80, colors.green);

            if (Math.abs(retroPos.x - targetRetroX) < 1) {
                stage++;
                alpha = 0;
            }
        }
        
        function showRetro() {
            drawText('retro', canvas.width / 4, 100, 80, colors.primary);
            alpha = lerp(alpha, 1, 0.05);
            drawExplanation('=  back, backward (向后)', canvas.width / 4 + 250, 100, alpha);
            
            personAngle = lerp(personAngle, Math.PI, 0.04);
            drawPersonLookingBack(canvas.width / 2, canvas.height / 2 + 100, personAngle, alpha);
            
            if(alpha > 0.95) {
                instructionEl.style.opacity = 1;
            }
        }

        function showSpect() {
            instructionEl.style.opacity = 0;
            alpha = 0; // Reset for this stage's elements
            drawText('spect', canvas.width / 4, 100, 80, colors.green);
            alpha = lerp(alpha, 1, 0.05);
            drawExplanation('=  to look, to see (看)', canvas.width / 4 + 200, 100, alpha);
            
            eye.pupilSize = lerp(eye.pupilSize, 15, 0.1);
            drawEye(canvas.width / 2, canvas.height / 2 + 50, alpha);
            if (Math.abs(eye.pupilSize - 15) < 1) {
                 eye.pupilSize = 5; // Reset for blinking effect
            }

            if(alpha > 0.95) {
                instructionEl.style.opacity = 1;
                 // Init memories for next stage
                 if (memories.length === 0) {
                    memories = [
                        { x: canvas.width + 100, y: 300, w: 120, h: 90, text: 'A', a: 0, tx: 650 },
                        { x: canvas.width + 100, y: 300, w: 120, h: 90, text: 'B', a: 0, tx: 450 },
                        { x: canvas.width + 100, y: 300, w: 120, h: 90, text: 'C', a: 0, tx: 250 },
                    ];
                }
            }
        }
        
        function combine() {
            instructionEl.style.opacity = 0;
            alpha = lerp(alpha, 1, 0.05);

            // Animate word back to center-top
            retroPos.x = lerp(retroPos.x, wordPos.x - 140, 0.05);
            spectPos.x = lerp(spectPos.x, wordPos.x + 130, 0.05);
            retroPos.y = lerp(retroPos.y, 100, 0.05);
            spectPos.y = lerp(spectPos.y, 100, 0.05);
            
            drawText('retro', retroPos.x, retroPos.y, 80, colors.primary, alpha);
            drawText('spect', spectPos.x, spectPos.y, 80, colors.green, alpha);
            drawExplanation('retrospect: 回顾, 追溯', wordPos.x, 180, alpha);

            // Animate memories
            let allInPlace = true;
            memories.forEach((mem, index) => {
                mem.x = lerp(mem.x, mem.tx, 0.05 + index * 0.01);
                mem.a = lerp(mem.a, 1, 0.05);
                drawMemory(mem.x, mem.y, mem.w, mem.h, mem.text, mem.a);
                if (Math.abs(mem.x - mem.tx) > 1) allInPlace = false;
            });
            
            drawPersonLookingBack(100, 300, Math.PI, alpha);

            if(allInPlace) {
                instructionEl.innerHTML = "点击重新播放";
                instructionEl.style.opacity = 1;
            }
        }
        
        function end() {
             // Just holds the final screen
             // The click handler will reset the animation
        }

        // Main animation loop
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(stage) {
                case 0: intro(); break;
                case 1: splitWord(); break;
                case 2: showRetro(); break;
                case 3: showSpect(); break;
                case 4: combine(); break;
                case 5: end(); break;
            }

            animationFrame = requestAnimationFrame(animate);
        }

        // Event listener
        canvas.addEventListener('click', () => {
            if (stage === 0 && alpha < 0.95) return;
            if (stage === 2 && alpha < 0.95) return;
            if (stage === 3 && alpha < 0.95) return;

            if (stage === 5) { // Reset animation
                stage = 0;
                alpha = 0;
                retroPos = { x: canvas.width / 2, y: canvas.height / 2 };
                spectPos = { x: canvas.width / 2, y: canvas.height / 2 };
                personAngle = 0;
                eye = { pupilSize: 5 };
                memories = [];
                instructionEl.innerHTML = "点击屏幕继续动画";
                instructionEl.style.opacity = 0;
            } else {
                stage = (stage + 1) % totalStages;
                alpha = 0; // Reset alpha for next stage transitions
            }
        });

        animate();
    </script>
</body>
</html> 