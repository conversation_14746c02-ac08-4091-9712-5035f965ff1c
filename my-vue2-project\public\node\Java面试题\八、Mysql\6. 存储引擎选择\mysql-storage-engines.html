<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL存储引擎 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: slideUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .engine-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-top: 40px;
        }

        .engine-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 30px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .engine-card.innodb {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .engine-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        }

        .engine-card h3 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .feature-list {
            list-style: none;
            margin-top: 20px;
        }

        .feature-list li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
            opacity: 0;
            animation: fadeInLeft 0.5s ease-out forwards;
        }

        .feature-list li:nth-child(1) { animation-delay: 0.1s; }
        .feature-list li:nth-child(2) { animation-delay: 0.2s; }
        .feature-list li:nth-child(3) { animation-delay: 0.3s; }
        .feature-list li:nth-child(4) { animation-delay: 0.4s; }

        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #fff;
            font-weight: bold;
        }

        .canvas-container {
            text-align: center;
            margin: 40px 0;
        }

        #gameCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #gameCanvas:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .game-controls {
            margin-top: 20px;
            text-align: center;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .scenario-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .scenario-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .scenario-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .scenario-card:hover {
            transform: translateY(-5px);
        }

        .scenario-card h4 {
            color: #333;
            font-size: 1.3rem;
            margin-bottom: 15px;
        }

        .scenario-card p {
            color: #666;
            line-height: 1.6;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MySQL存储引擎探索之旅</h1>
            <p>让我们通过有趣的动画和交互游戏，深入了解MySQL的两大主要存储引擎：InnoDB和MyISAM</p>
        </div>

        <div class="section">
            <h2 class="section-title">什么是存储引擎？</h2>
            <p style="font-size: 1.1rem; line-height: 1.8; color: #555; text-align: center; margin-bottom: 30px;">
                存储引擎就像是数据库的"心脏"，它决定了数据如何存储、如何检索、如何更新。
                不同的存储引擎有不同的特点和适用场景。
            </p>
            
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="game-controls">
                <button class="btn" onclick="startAnimation()">开始动画演示</button>
                <button class="btn" onclick="resetAnimation()">重置</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">存储引擎对比</h2>
            <div class="engine-comparison">
                <div class="engine-card myisam tooltip" onclick="showMyISAMDetails()">
                    <h3>MyISAM</h3>
                    <p style="text-align: center; margin-bottom: 20px;">读写插入专家</p>
                    <ul class="feature-list">
                        <li>读取速度超快</li>
                        <li>插入操作高效</li>
                        <li>不支持事务</li>
                        <li>表级锁定</li>
                    </ul>
                    <span class="tooltiptext">点击查看详细信息</span>
                </div>
                
                <div class="engine-card innodb tooltip" onclick="showInnoDBDetails()">
                    <h3>InnoDB</h3>
                    <p style="text-align: center; margin-bottom: 20px;">事务处理专家</p>
                    <ul class="feature-list">
                        <li>支持事务处理</li>
                        <li>行级锁定</li>
                        <li>支持外键</li>
                        <li>崩溃恢复能力强</li>
                    </ul>
                    <span class="tooltiptext">点击查看详细信息</span>
                </div>
            </div>
        </div>

        <div class="scenario-section">
            <h2 class="section-title" style="color: #333;">实际应用场景</h2>
            <div class="scenario-grid">
                <div class="scenario-card">
                    <h4>🌐 MyISAM适用场景</h4>
                    <p><strong>博客系统：</strong>文章发布后很少修改，主要是读取操作</p>
                    <p><strong>新闻门户：</strong>新闻发布频繁，但发布后基本不变</p>
                    <p><strong>数据仓库：</strong>大量数据导入，主要用于查询分析</p>
                </div>
                
                <div class="scenario-card">
                    <h4>🏢 InnoDB适用场景</h4>
                    <p><strong>OA办公系统：</strong>需要频繁更新，要求数据一致性</p>
                    <p><strong>电商平台：</strong>订单处理需要事务支持</p>
                    <p><strong>金融系统：</strong>对数据完整性要求极高</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationId;
        let currentStep = 0;
        let particles = [];
        
        // 初始化粒子系统
        function initParticles() {
            particles = [];
            for (let i = 0; i < 50; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: Math.random() * 3 + 1,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`
                });
            }
        }
        
        // 绘制粒子
        function drawParticles() {
            particles.forEach(particle => {
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = particle.color;
                ctx.fill();
                
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
            });
        }
        
        // 绘制存储引擎示意图
        function drawStorageEngines() {
            // MyISAM引擎
            ctx.fillStyle = '#f093fb';
            ctx.fillRect(100, 150, 200, 100);
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('MyISAM', 200, 190);
            ctx.font = '14px Arial';
            ctx.fillText('快速读写', 200, 210);
            ctx.fillText('表级锁', 200, 230);
            
            // InnoDB引擎
            ctx.fillStyle = '#4facfe';
            ctx.fillRect(500, 150, 200, 100);
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.fillText('InnoDB', 600, 190);
            ctx.font = '14px Arial';
            ctx.fillText('事务支持', 600, 210);
            ctx.fillText('行级锁', 600, 230);
            
            // 连接线和数据流动画
            if (currentStep > 0) {
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(200, 150);
                ctx.lineTo(400, 100);
                ctx.lineTo(600, 150);
                ctx.stroke();
                
                // 数据流动画
                const flowX = 200 + (currentStep % 100) * 4;
                ctx.beginPath();
                ctx.arc(flowX, 125, 8, 0, Math.PI * 2);
                ctx.fillStyle = '#ff6b6b';
                ctx.fill();
            }
        }
        
        // 主动画循环
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9ff');
            gradient.addColorStop(1, '#e8f0ff');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            drawParticles();
            drawStorageEngines();
            
            currentStep++;
            animationId = requestAnimationFrame(animate);
        }
        
        // 开始动画
        function startAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            initParticles();
            currentStep = 0;
            animate();
        }
        
        // 重置动画
        function resetAnimation() {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            currentStep = 0;
            particles = [];
        }
        
        // 显示MyISAM详细信息
        function showMyISAMDetails() {
            alert(`MyISAM存储引擎详解：

🚀 优势：
• 读取速度极快，适合查询密集型应用
• 插入操作效率高
• 占用存储空间小
• 支持全文索引

⚠️ 限制：
• 不支持事务处理
• 使用表级锁，并发性能较差
• 不支持外键约束
• 崩溃后恢复困难

💡 最佳应用场景：
• 博客、新闻网站（读多写少）
• 数据仓库和报表系统
• 日志记录系统`);
        }
        
        // 显示InnoDB详细信息
        function showInnoDBDetails() {
            alert(`InnoDB存储引擎详解：

🛡️ 优势：
• 完整的ACID事务支持
• 行级锁定，高并发性能
• 支持外键约束
• 自动崩溃恢复
• 支持热备份

⚠️ 考虑因素：
• 占用更多存储空间
• 读取速度相对较慢
• 配置相对复杂

💡 最佳应用场景：
• 电商平台（需要事务）
• 金融系统（数据一致性要求高）
• OA办公系统（频繁更新操作）
• 高并发Web应用`);
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            // 添加鼠标悬停效果
            canvas.addEventListener('mousemove', function(e) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // 在鼠标位置添加特效
                if (particles.length > 0) {
                    particles.push({
                        x: x,
                        y: y,
                        vx: (Math.random() - 0.5) * 4,
                        vy: (Math.random() - 0.5) * 4,
                        size: Math.random() * 5 + 2,
                        color: `hsl(${Math.random() * 360}, 80%, 70%)`
                    });
                    
                    // 限制粒子数量
                    if (particles.length > 100) {
                        particles.shift();
                    }
                }
            });
            
            // 自动开始动画
            setTimeout(startAnimation, 1000);
        });
    </script>
</body>
</html>
