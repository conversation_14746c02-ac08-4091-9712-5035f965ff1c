const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 设置公共路径为相对路径，这样在 Electron 中可以正确加载资源
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  
  // 输出目录
  outputDir: 'dist',
  
  // 静态资源目录
  assetsDir: '',
  
  // 生产环境下不生成 source map
  productionSourceMap: false,
  
  // 开发服务器配置
  devServer: {
    port: 8080,
    open: false
  },
  
  // 配置 webpack
  configureWebpack: {
    // 在 Electron 环境中，我们需要设置 target 为 electron-renderer
    target: process.env.IS_ELECTRON ? 'electron-renderer' : 'web'
  }
})
