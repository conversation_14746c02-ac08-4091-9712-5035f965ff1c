<template>
  <div class="page-header">
    <div class="container">
      <h1 class="logo" @click="goHome">笔记<span>.</span></h1>
      <div class="header-actions">
        <!-- 插槽用于放置页面特定的操作按钮 -->
        <slot name="actions"></slot>
        
        <!-- 暗黑模式切换开关 -->
        <div class="mode-switch">
          <el-switch
            :value="darkMode"
            active-color="#3498db"
            inactive-color="#bdc3c7"
            @change="toggleDarkMode"
          ></el-switch>
          <span class="mode-label">{{ darkMode ? '暗黑' : '明亮' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'PageHeader',
  computed: {
    ...mapState(['darkMode'])
  },
  methods: {
    ...mapActions(['toggleDarkMode']),
    goHome() {
      if (this.$route.path !== '/') {
        this.$router.push('/')
      }
    }
  }
}
</script>

<style scoped>
.page-header {
  padding: 30px 0;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--header-background);
  transition: background-color 0.3s, border-color 0.3s;
}

.page-header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 30px;
}

.logo {
  font-size: 28px;
  font-weight: 300;
  letter-spacing: 2px;
  margin: 0;
  cursor: pointer;
  transition: opacity 0.3s;
}

.logo:hover {
  opacity: 0.8;
}

.logo span {
  color: var(--primary-color);
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.mode-label {
  margin-left: 10px;
  font-size: 14px;
  color: var(--text-light);
  font-weight: 300;
}
</style> 