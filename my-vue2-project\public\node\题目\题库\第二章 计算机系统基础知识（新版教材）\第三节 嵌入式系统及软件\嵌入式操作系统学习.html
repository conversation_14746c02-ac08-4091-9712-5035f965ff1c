<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式操作系统 - 互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translateY(0);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }

        .feature-title {
            font-size: 1.3rem;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .feature-desc {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
            color: white;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateX(10px);
        }

        .option.selected {
            border-color: white;
            background: rgba(255,255,255,0.4);
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2rem;
            display: none;
        }

        .result.show {
            display: block;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🔧 嵌入式操作系统</h1>
            <p class="subtitle">让我们用动画来理解这个神奇的世界！</p>
        </div>

        <div class="section">
            <h2 class="section-title">🎯 什么是嵌入式操作系统？</h2>
            <div class="canvas-container">
                <canvas id="embeddedCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <strong>简单理解：</strong>嵌入式操作系统就像是一个超级小巧的管家，住在各种智能设备里（比如手机、智能手表、汽车电脑等），负责管理和协调设备内部的所有资源和功能。
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">✨ 五大特点详解</h2>
            <div class="features-grid">
                <div class="feature-card" onclick="showFeature('micro')">
                    <div class="feature-title">🔬 微型化</div>
                    <div class="feature-desc">体积小，占用资源少</div>
                </div>
                <div class="feature-card" onclick="showFeature('custom')">
                    <div class="feature-title">🎨 可定制</div>
                    <div class="feature-desc">根据需求量身定做</div>
                </div>
                <div class="feature-card" onclick="showFeature('portable')">
                    <div class="feature-title">🚀 易移植</div>
                    <div class="feature-desc">可以轻松搬到不同设备</div>
                </div>
                <div class="feature-card" onclick="showFeature('realtime')">
                    <div class="feature-title">⚡ 实时性</div>
                    <div class="feature-desc">反应超快，不能延迟</div>
                </div>
                <div class="feature-card" onclick="showFeature('reliable')">
                    <div class="feature-title">🛡️ 可靠性</div>
                    <div class="feature-desc">稳定运行，不能出错</div>
                </div>
            </div>
            <div class="canvas-container">
                <canvas id="featuresCanvas" width="800" height="300"></canvas>
            </div>
        </div>

        <div class="section quiz-section">
            <h2 class="section-title">🎯 现在来做题吧！</h2>
            <div class="quiz-question">
                嵌入式操作系统运行在嵌入式智能芯片环境中，对整个智能芯片以及它所操作、控制的各种部件装置等资源进行统一协调、处理、指挥和控制。其特点是：（ ）
            </div>
            <div class="options">
                <div class="option" onclick="selectOption(this, 'A')">
                    <strong>A.</strong> 微型化，可定制，易移植性，实时性，可靠性
                </div>
                <div class="option" onclick="selectOption(this, 'B')">
                    <strong>B.</strong> 各结点自治性好，资源共享透明性强但控制机构复杂
                </div>
                <div class="option" onclick="selectOption(this, 'C')">
                    <strong>C.</strong> 多路性、独立性、及时性、交互性和同时性
                </div>
                <div class="option" onclick="selectOption(this, 'D')">
                    <strong>D.</strong> 资源利用率高，系统吞吐量大，但作业的平均周转时间较长
                </div>
            </div>
            <button class="btn" onclick="checkAnswer()">提交答案</button>
            <button class="btn" onclick="showExplanation()">查看解析</button>
            <div id="result" class="result"></div>
        </div>
    </div>

    <script>
        // 嵌入式系统动画
        const embeddedCanvas = document.getElementById('embeddedCanvas');
        const embeddedCtx = embeddedCanvas.getContext('2d');
        
        // 特性展示动画
        const featuresCanvas = document.getElementById('featuresCanvas');
        const featuresCtx = featuresCanvas.getContext('2d');
        
        let animationFrame = 0;
        let selectedFeature = null;
        let selectedOption = null;
        
        // 绘制嵌入式系统示意图
        function drawEmbeddedSystem() {
            embeddedCtx.clearRect(0, 0, 800, 400);
            
            // 背景
            const gradient = embeddedCtx.createLinearGradient(0, 0, 800, 400);
            gradient.addColorStop(0, '#f0f8ff');
            gradient.addColorStop(1, '#e6f3ff');
            embeddedCtx.fillStyle = gradient;
            embeddedCtx.fillRect(0, 0, 800, 400);
            
            // 中央芯片
            const chipX = 400;
            const chipY = 200;
            const chipSize = 80;
            
            // 芯片外壳
            embeddedCtx.fillStyle = '#2c3e50';
            embeddedCtx.fillRect(chipX - chipSize/2, chipY - chipSize/2, chipSize, chipSize);
            
            // 芯片内核（操作系统）
            const pulse = Math.sin(animationFrame * 0.1) * 0.2 + 1;
            embeddedCtx.fillStyle = `rgba(102, 126, 234, ${pulse})`;
            embeddedCtx.fillRect(chipX - 30, chipY - 30, 60, 60);
            
            // OS标签
            embeddedCtx.fillStyle = 'white';
            embeddedCtx.font = 'bold 14px Arial';
            embeddedCtx.textAlign = 'center';
            embeddedCtx.fillText('OS', chipX, chipY + 5);
            
            // 周围的设备组件
            const devices = [
                {x: 200, y: 100, name: '传感器', icon: '📡'},
                {x: 600, y: 100, name: '显示屏', icon: '📱'},
                {x: 200, y: 300, name: '存储器', icon: '💾'},
                {x: 600, y: 300, name: '网络模块', icon: '📶'},
                {x: 100, y: 200, name: '电源管理', icon: '🔋'},
                {x: 700, y: 200, name: '输入设备', icon: '⌨️'}
            ];
            
            devices.forEach((device, index) => {
                // 设备框
                embeddedCtx.fillStyle = '#ecf0f1';
                embeddedCtx.fillRect(device.x - 40, device.y - 25, 80, 50);
                embeddedCtx.strokeStyle = '#bdc3c7';
                embeddedCtx.strokeRect(device.x - 40, device.y - 25, 80, 50);
                
                // 设备图标
                embeddedCtx.font = '20px Arial';
                embeddedCtx.fillText(device.icon, device.x, device.y - 5);
                
                // 设备名称
                embeddedCtx.font = '12px Arial';
                embeddedCtx.fillStyle = '#2c3e50';
                embeddedCtx.fillText(device.name, device.x, device.y + 15);
                
                // 连接线动画
                const lineOffset = Math.sin(animationFrame * 0.05 + index) * 5;
                embeddedCtx.strokeStyle = `rgba(102, 126, 234, 0.6)`;
                embeddedCtx.lineWidth = 2;
                embeddedCtx.beginPath();
                embeddedCtx.moveTo(device.x, device.y);
                embeddedCtx.lineTo(chipX + lineOffset, chipY + lineOffset);
                embeddedCtx.stroke();
                
                // 数据流动画
                const progress = (animationFrame * 0.02 + index * 0.5) % 1;
                const dataX = device.x + (chipX - device.x) * progress;
                const dataY = device.y + (chipY - device.y) * progress;
                embeddedCtx.fillStyle = '#e74c3c';
                embeddedCtx.beginPath();
                embeddedCtx.arc(dataX, dataY, 3, 0, Math.PI * 2);
                embeddedCtx.fill();
            });
            
            // 标题
            embeddedCtx.fillStyle = '#2c3e50';
            embeddedCtx.font = 'bold 18px Arial';
            embeddedCtx.fillText('嵌入式操作系统 - 统一协调管理所有资源', 400, 30);
        }
        
        // 绘制特性展示
        function drawFeatures() {
            featuresCtx.clearRect(0, 0, 800, 300);
            
            if (!selectedFeature) {
                // 默认显示所有特性
                featuresCtx.fillStyle = '#34495e';
                featuresCtx.font = 'bold 24px Arial';
                featuresCtx.textAlign = 'center';
                featuresCtx.fillText('点击上方特性卡片查看详细动画演示', 400, 150);
                return;
            }
            
            // 根据选中的特性显示不同动画
            switch(selectedFeature) {
                case 'micro':
                    drawMicroAnimation();
                    break;
                case 'custom':
                    drawCustomAnimation();
                    break;
                case 'portable':
                    drawPortableAnimation();
                    break;
                case 'realtime':
                    drawRealtimeAnimation();
                    break;
                case 'reliable':
                    drawReliableAnimation();
                    break;
            }
        }
        
        function drawMicroAnimation() {
            // 微型化动画：显示大小对比
            featuresCtx.fillStyle = '#3498db';
            featuresCtx.font = 'bold 20px Arial';
            featuresCtx.textAlign = 'center';
            featuresCtx.fillText('🔬 微型化 - 小巧精致', 400, 40);
            
            // 传统操作系统（大）
            featuresCtx.fillStyle = '#e74c3c';
            featuresCtx.fillRect(100, 100, 150, 100);
            featuresCtx.fillStyle = 'white';
            featuresCtx.font = '16px Arial';
            featuresCtx.fillText('传统OS', 175, 155);
            
            // 嵌入式操作系统（小）
            const scale = Math.sin(animationFrame * 0.1) * 0.1 + 0.9;
            featuresCtx.fillStyle = '#27ae60';
            featuresCtx.fillRect(550 - 30*scale, 130 - 15*scale, 60*scale, 30*scale);
            featuresCtx.fillStyle = 'white';
            featuresCtx.font = `${12*scale}px Arial`;
            featuresCtx.fillText('嵌入式OS', 550, 150);
            
            // 箭头和说明
            featuresCtx.strokeStyle = '#f39c12';
            featuresCtx.lineWidth = 3;
            featuresCtx.beginPath();
            featuresCtx.moveTo(280, 150);
            featuresCtx.lineTo(520, 150);
            featuresCtx.stroke();
            
            featuresCtx.fillStyle = '#f39c12';
            featuresCtx.font = '14px Arial';
            featuresCtx.fillText('体积缩小90%+', 400, 180);
        }
        
        function drawCustomAnimation() {
            // 可定制动画：显示不同配置
            featuresCtx.fillStyle = '#9b59b6';
            featuresCtx.font = 'bold 20px Arial';
            featuresCtx.textAlign = 'center';
            featuresCtx.fillText('🎨 可定制 - 量身定做', 400, 40);
            
            const configs = ['智能手表', '汽车系统', '智能家居'];
            configs.forEach((config, index) => {
                const x = 150 + index * 200;
                const y = 120;
                
                // 基础框架
                featuresCtx.fillStyle = '#ecf0f1';
                featuresCtx.fillRect(x - 60, y, 120, 80);
                
                // 定制模块（动态变化）
                const moduleHeight = Math.sin(animationFrame * 0.1 + index) * 10 + 30;
                featuresCtx.fillStyle = `hsl(${index * 120}, 70%, 60%)`;
                featuresCtx.fillRect(x - 50, y + 10, 100, moduleHeight);
                
                featuresCtx.fillStyle = '#2c3e50';
                featuresCtx.font = '12px Arial';
                featuresCtx.fillText(config, x, y + 95);
            });
        }
        
        function drawPortableAnimation() {
            // 易移植动画：显示跨平台
            featuresCtx.fillStyle = '#e67e22';
            featuresCtx.font = 'bold 20px Arial';
            featuresCtx.textAlign = 'center';
            featuresCtx.fillText('🚀 易移植 - 跨平台运行', 400, 40);
            
            // OS核心
            const coreX = 400;
            const coreY = 150;
            featuresCtx.fillStyle = '#3498db';
            featuresCtx.beginPath();
            featuresCtx.arc(coreX, coreY, 30, 0, Math.PI * 2);
            featuresCtx.fill();
            
            featuresCtx.fillStyle = 'white';
            featuresCtx.font = '12px Arial';
            featuresCtx.fillText('OS核心', coreX, coreY + 3);
            
            // 不同平台
            const platforms = [
                {x: 200, y: 100, name: 'ARM', color: '#e74c3c'},
                {x: 600, y: 100, name: 'x86', color: '#27ae60'},
                {x: 200, y: 200, name: 'MIPS', color: '#f39c12'},
                {x: 600, y: 200, name: 'RISC-V', color: '#9b59b6'}
            ];
            
            platforms.forEach((platform, index) => {
                // 平台框
                featuresCtx.fillStyle = platform.color;
                featuresCtx.fillRect(platform.x - 40, platform.y - 20, 80, 40);
                
                featuresCtx.fillStyle = 'white';
                featuresCtx.font = '14px Arial';
                featuresCtx.fillText(platform.name, platform.x, platform.y + 3);
                
                // 移植箭头动画
                const arrowProgress = (animationFrame * 0.05 + index * 0.25) % 1;
                if (arrowProgress > 0.8) {
                    featuresCtx.strokeStyle = platform.color;
                    featuresCtx.lineWidth = 3;
                    featuresCtx.beginPath();
                    featuresCtx.moveTo(platform.x, platform.y);
                    featuresCtx.lineTo(coreX, coreY);
                    featuresCtx.stroke();
                }
            });
        }
        
        function drawRealtimeAnimation() {
            // 实时性动画：显示快速响应
            featuresCtx.fillStyle = '#e74c3c';
            featuresCtx.font = 'bold 20px Arial';
            featuresCtx.textAlign = 'center';
            featuresCtx.fillText('⚡ 实时性 - 毫秒级响应', 400, 40);
            
            // 时间轴
            featuresCtx.strokeStyle = '#34495e';
            featuresCtx.lineWidth = 2;
            featuresCtx.beginPath();
            featuresCtx.moveTo(100, 150);
            featuresCtx.lineTo(700, 150);
            featuresCtx.stroke();
            
            // 时间刻度
            for (let i = 0; i <= 10; i++) {
                const x = 100 + i * 60;
                featuresCtx.beginPath();
                featuresCtx.moveTo(x, 145);
                featuresCtx.lineTo(x, 155);
                featuresCtx.stroke();
                
                featuresCtx.fillStyle = '#34495e';
                featuresCtx.font = '10px Arial';
                featuresCtx.fillText(`${i}ms`, x - 8, 170);
            }
            
            // 响应脉冲
            const pulseX = 100 + (animationFrame * 3) % 600;
            featuresCtx.fillStyle = '#e74c3c';
            featuresCtx.beginPath();
            featuresCtx.arc(pulseX, 150, 8, 0, Math.PI * 2);
            featuresCtx.fill();
            
            // 响应时间标注
            featuresCtx.fillStyle = '#e74c3c';
            featuresCtx.font = '14px Arial';
            featuresCtx.fillText('< 1ms 响应时间', 400, 200);
        }
        
        function drawReliableAnimation() {
            // 可靠性动画：显示稳定运行
            featuresCtx.fillStyle = '#27ae60';
            featuresCtx.font = 'bold 20px Arial';
            featuresCtx.textAlign = 'center';
            featuresCtx.fillText('🛡️ 可靠性 - 7×24小时稳定运行', 400, 40);
            
            // 系统状态监控
            const centerX = 400;
            const centerY = 150;
            
            // 外圈（系统边界）
            featuresCtx.strokeStyle = '#27ae60';
            featuresCtx.lineWidth = 4;
            featuresCtx.beginPath();
            featuresCtx.arc(centerX, centerY, 80, 0, Math.PI * 2);
            featuresCtx.stroke();
            
            // 内核（稳定运行）
            const heartbeat = Math.sin(animationFrame * 0.2) * 5 + 40;
            featuresCtx.fillStyle = '#27ae60';
            featuresCtx.beginPath();
            featuresCtx.arc(centerX, centerY, heartbeat, 0, Math.PI * 2);
            featuresCtx.fill();
            
            featuresCtx.fillStyle = 'white';
            featuresCtx.font = '16px Arial';
            featuresCtx.fillText('稳定', centerX, centerY + 5);
            
            // 运行时间
            const uptime = Math.floor(animationFrame / 10);
            featuresCtx.fillStyle = '#27ae60';
            featuresCtx.font = '14px Arial';
            featuresCtx.fillText(`运行时间: ${uptime} 小时`, centerX, centerY + 120);
        }
        
        // 动画循环
        function animate() {
            animationFrame++;
            drawEmbeddedSystem();
            drawFeatures();
            requestAnimationFrame(animate);
        }
        
        // 特性选择
        function showFeature(feature) {
            selectedFeature = feature;
            // 添加选中效果
            document.querySelectorAll('.feature-card').forEach(card => {
                card.style.transform = 'translateY(0)';
            });
            event.target.closest('.feature-card').style.transform = 'translateY(-10px)';
        }
        
        // 题目相关函数
        function selectOption(element, option) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // 选中当前选项
            element.classList.add('selected');
            selectedOption = option;
        }
        
        function checkAnswer() {
            if (!selectedOption) {
                alert('请先选择一个答案！');
                return;
            }
            
            const result = document.getElementById('result');
            const options = document.querySelectorAll('.option');
            
            // 显示正确答案
            options.forEach((opt, index) => {
                const optionLetter = ['A', 'B', 'C', 'D'][index];
                if (optionLetter === 'A') {
                    opt.classList.add('correct');
                } else if (opt.classList.contains('selected') && optionLetter !== 'A') {
                    opt.classList.add('wrong');
                }
            });
            
            if (selectedOption === 'A') {
                result.innerHTML = '🎉 恭喜你答对了！嵌入式操作系统的特点确实是：微型化、可定制、易移植性、实时性、可靠性。';
                result.style.background = 'rgba(76, 175, 80, 0.8)';
            } else {
                result.innerHTML = '❌ 答案不正确。正确答案是A。让我们来看看为什么...';
                result.style.background = 'rgba(244, 67, 54, 0.8)';
            }
            
            result.classList.add('show');
        }
        
        function showExplanation() {
            const result = document.getElementById('result');
            result.innerHTML = `
                <h3>📚 详细解析：</h3>
                <p><strong>正确答案：A</strong></p>
                <p><strong>嵌入式操作系统的五大特点：</strong></p>
                <ul style="text-align: left; margin: 10px 0;">
                    <li>🔬 <strong>微型化</strong>：体积小，资源占用少</li>
                    <li>🎨 <strong>可定制</strong>：根据具体应用需求定制功能</li>
                    <li>🚀 <strong>易移植</strong>：可以在不同硬件平台间移植</li>
                    <li>⚡ <strong>实时性</strong>：能够及时响应外部事件</li>
                    <li>🛡️ <strong>可靠性</strong>：长期稳定运行，不能出错</li>
                </ul>
                <p><strong>其他选项说明：</strong></p>
                <p>B选项描述的是分布式系统的特点</p>
                <p>C选项描述的是分时操作系统的特点</p>
                <p>D选项描述的是多道批处理系统的特点</p>
            `;
            result.style.background = 'rgba(52, 152, 219, 0.8)';
            result.classList.add('show');
        }
        
        // 启动动画
        animate();
    </script>
</body>
</html>
