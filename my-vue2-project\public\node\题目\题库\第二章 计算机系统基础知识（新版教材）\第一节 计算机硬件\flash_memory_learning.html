<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>闪存知识互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 40px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
        }

        .section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.6s ease;
        }

        .section.active {
            opacity: 1;
            transform: translateY(0);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            position: relative;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        canvas:hover {
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .interactive-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }

        .quiz-container {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            text-align: center;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .quiz-option {
            padding: 15px 20px;
            background: rgba(255,255,255,0.1);
            border: 2px solid transparent;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.3);
        }

        .quiz-option.wrong {
            border-color: #f44336;
            background: rgba(244, 67, 54, 0.3);
        }

        .next-btn {
            display: block;
            margin: 30px auto 0;
            padding: 15px 30px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid white;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            opacity: 0;
        }

        .next-btn.show {
            opacity: 1;
        }

        .next-btn:hover {
            background: white;
            color: #667eea;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .knowledge-point {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .knowledge-point::before {
            content: '💡';
            font-size: 1.5rem;
            margin-right: 10px;
        }

        .highlight {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .error-highlight {
            background: linear-gradient(135deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 3px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .success-message {
            background: linear-gradient(135deg, #4CAF50, #8BC34A);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
            animation: slideInUp 0.5s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 10px;
            }

            .title {
                font-size: 2rem;
            }

            .section {
                padding: 20px;
            }

            canvas {
                width: 100%;
                max-width: 500px;
                height: auto;
            }

            .interactive-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 200px;
                margin: 5px 0;
            }

            .quiz-options {
                grid-template-columns: 1fr;
            }
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body>
    <canvas class="floating-particles" id="particleCanvas"></canvas>
    
    <div class="container">
        <div class="header">
            <h1 class="title">闪存知识互动学习</h1>
            <p class="subtitle">通过动画和游戏深入理解Flash Memory</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 第一部分：什么是闪存 -->
        <div class="section active" id="section1">
            <h2 class="section-title">什么是闪存？</h2>
            <div class="canvas-container">
                <canvas id="flashIntroCanvas" width="600" height="400"></canvas>
            </div>
            <div class="explanation">
                <strong>闪存（Flash Memory）</strong>是一种非易失性存储器，即使断电后数据也不会丢失。
                它就像一个永远不会忘记的记忆盒子！点击上方动画来看看闪存是如何工作的。
            </div>
            <div class="knowledge-point">
                <strong>关键特点：</strong>非易失性存储器 - 断电后数据<span class="highlight">不会丢失</span>
            </div>
            <div class="interactive-buttons">
                <button class="btn btn-primary" onclick="animateFlashIntro()">开始动画</button>
                <button class="btn btn-secondary" onclick="resetFlashIntro()">重置</button>
                <button class="btn btn-primary" onclick="nextSection()">下一节</button>
            </div>
        </div>

        <!-- 第二部分：闪存的块删除操作 -->
        <div class="section" id="section2">
            <h2 class="section-title">闪存的块删除操作</h2>
            <div class="canvas-container">
                <canvas id="blockEraseCanvas" width="600" height="400"></canvas>
            </div>
            <div class="explanation">
                闪存有一个重要特点：<strong>以块为单位进行删除操作</strong>。
                就像清理房间时，你不能只擦掉一个字，而是要擦掉整个黑板！
                点击下面的按钮来体验块删除的过程。
            </div>
            <div class="knowledge-point">
                <strong>工作原理：</strong>删除操作必须<span class="highlight">以块为单位</span>进行，不能单独删除某个位
            </div>
            <div class="interactive-buttons">
                <button class="btn btn-primary" onclick="animateBlockErase()">演示块删除</button>
                <button class="btn btn-secondary" onclick="resetBlockErase()">重置</button>
                <button class="btn btn-danger" onclick="interactiveErase()">互动删除游戏</button>
                <button class="btn btn-primary" onclick="nextSection()">下一节</button>
            </div>
        </div>

        <!-- 第三部分：闪存 vs RAM 访问方式 -->
        <div class="section" id="section3">
            <h2 class="section-title">闪存 vs RAM：访问方式对比</h2>
            <div class="canvas-container">
                <canvas id="accessCompareCanvas" width="600" height="400"></canvas>
            </div>
            <div class="explanation">
                这是题目中的关键点！闪存<strong>不能</strong>代替主存（RAM）。
                虽然闪存支持随机访问，但它的访问速度比RAM慢很多，而且写入次数有限制。
                让我们通过动画来看看它们的区别！
            </div>
            <div class="knowledge-point">
                <strong>重要区别：</strong>闪存<span class="error-highlight">不能代替RAM</span>作为主存使用
            </div>
            <div class="knowledge-point">
                <strong>原因：</strong>访问速度慢、写入次数有限、主要用于<span class="highlight">存储而非运算</span>
            </div>
            <div class="interactive-buttons">
                <button class="btn btn-primary" onclick="animateAccessComparison()">速度对比动画</button>
                <button class="btn btn-secondary" onclick="resetAccessComparison()">重置</button>
                <button class="btn btn-primary" onclick="nextSection()">下一节</button>
            </div>
        </div>

        <!-- 第四部分：闪存在嵌入式系统中的应用 -->
        <div class="section" id="section4">
            <h2 class="section-title">闪存在嵌入式系统中的应用</h2>
            <div class="canvas-container">
                <canvas id="embeddedCanvas" width="600" height="400"></canvas>
            </div>
            <div class="explanation">
                在嵌入式系统中，闪存完全可以代替ROM存储器！
                过去20年里，闪存已经全面代替了ROM(EPROM)，用来存储Bootloader、操作系统代码，
                甚至直接当作硬盘使用（比如U盘）。
            </div>
            <div class="knowledge-point">
                <strong>应用场景：</strong>闪存<span class="highlight">可以代替ROM</span>在嵌入式系统中使用
            </div>
            <div class="knowledge-point">
                <strong>具体用途：</strong>存储Bootloader、操作系统、应用程序、用户数据
            </div>
            <div class="interactive-buttons">
                <button class="btn btn-primary" onclick="animateEmbeddedSystem()">嵌入式系统演示</button>
                <button class="btn btn-secondary" onclick="resetEmbeddedSystem()">重置</button>
                <button class="btn btn-primary" onclick="nextSection()">进入测试</button>
            </div>
        </div>

        <!-- 第五部分：知识测试 -->
        <div class="section" id="section5">
            <div class="quiz-container">
                <h2 class="section-title" style="color: white;">知识测试</h2>
                <div class="quiz-question" id="quizQuestion">
                    以下关于闪存（Flash Memory）的叙述中，错误的是？
                </div>
                <div class="quiz-options" id="quizOptions">
                    <div class="quiz-option" onclick="selectOption(this, false)">
                        A. 掉电后信息不会丢失，属于非易失性存储器
                    </div>
                    <div class="quiz-option" onclick="selectOption(this, false)">
                        B. 以块为单位进行删除操作
                    </div>
                    <div class="quiz-option" onclick="selectOption(this, true)">
                        C. 采用随机访问方式，常用来代替主存
                    </div>
                    <div class="quiz-option" onclick="selectOption(this, false)">
                        D. 在嵌入式系统中可以用Flash来代替ROM存储器
                    </div>
                </div>
                <button class="next-btn" id="nextBtn" onclick="showExplanation()">查看解析</button>
            </div>
        </div>

        <!-- 解析部分 -->
        <div class="section" id="section6">
            <h2 class="section-title">答案解析</h2>
            <div class="success-message">
                <h3>🎉 正确答案：C</h3>
            </div>
            <div class="explanation">
                <strong style="color: #27ae60;">✅ A正确：</strong>闪存掉电后信息不丢失，这是<span class="highlight">非易失性存储器</span>的特点。<br><br>
                <strong style="color: #27ae60;">✅ B正确：</strong>闪存<span class="highlight">以块为单位进行删除操作</span>，这是其工作原理。<br><br>
                <strong style="color: #e74c3c;">❌ C错误：</strong>虽然闪存支持随机访问，但它<span class="error-highlight">不能代替主存（RAM）</span>！
                原因是闪存的访问速度比RAM慢很多，写入次数有限制，主要用于存储而非运算。<br><br>
                <strong style="color: #27ae60;">✅ D正确：</strong>在嵌入式系统中，闪存已经<span class="highlight">全面代替了ROM</span>，用于存储程序代码。
            </div>
            <div class="knowledge-point">
                <strong>记忆口诀：</strong>"闪存不易失，块删ROM替，就是不能当RAM用！"
            </div>
            <div class="interactive-buttons">
                <button class="btn btn-primary" onclick="restartLearning()">重新学习</button>
                <button class="btn btn-secondary" onclick="showDetailedSummary()">详细总结</button>
                <button class="btn btn-primary" onclick="showFinalSummary()">完成学习</button>
            </div>
        </div>

        <!-- 详细总结页面 -->
        <div class="section" id="section7">
            <h2 class="section-title">🎓 学习总结</h2>
            <div class="success-message">
                恭喜你完成了闪存知识的学习！
            </div>

            <div class="knowledge-point">
                <strong>知识点1：</strong>闪存是<span class="highlight">非易失性存储器</span>，断电不丢失数据
            </div>

            <div class="knowledge-point">
                <strong>知识点2：</strong>闪存<span class="highlight">以块为单位删除</span>，不能单独删除某个位
            </div>

            <div class="knowledge-point">
                <strong>知识点3：</strong>闪存<span class="error-highlight">不能代替RAM</span>作为主存使用
            </div>

            <div class="knowledge-point">
                <strong>知识点4：</strong>闪存<span class="highlight">可以代替ROM</span>在嵌入式系统中使用
            </div>

            <div class="canvas-container">
                <canvas id="summaryCanvas" width="600" height="300"></canvas>
            </div>

            <div class="explanation">
                <h3>🔍 深入理解</h3>
                <p><strong>为什么闪存不能代替RAM？</strong></p>
                <ul style="text-align: left; margin: 20px 0;">
                    <li>🐌 <strong>速度差异：</strong>RAM访问速度是纳秒级，闪存是微秒级</li>
                    <li>📝 <strong>写入限制：</strong>闪存写入次数有限（约10万次），RAM无限制</li>
                    <li>🎯 <strong>用途不同：</strong>RAM用于运算，闪存用于存储</li>
                    <li>💰 <strong>成本考虑：</strong>大容量闪存成本高于RAM</li>
                </ul>
            </div>

            <div class="interactive-buttons">
                <button class="btn btn-primary" onclick="animateSummary()">播放总结动画</button>
                <button class="btn btn-secondary" onclick="restartLearning()">重新学习</button>
                <button class="btn btn-primary" onclick="showFinalMessage()">获得学习证书</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = 1;
        let totalSections = 7;
        let animationRunning = false;
        let eraseGameActive = false;

        // 粒子背景动画
        function initParticles() {
            const canvas = document.getElementById('particleCanvas');
            const ctx = canvas.getContext('2d');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;

            const particles = [];
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                particles.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5,
                    size: Math.random() * 3 + 1,
                    opacity: Math.random() * 0.5 + 0.2
                });
            }

            function animateParticles() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                particles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;

                    if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                    if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

                    ctx.beginPath();
                    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                    ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
                    ctx.fill();
                });

                requestAnimationFrame(animateParticles);
            }

            animateParticles();
        }

        // 闪存介绍动画
        function animateFlashIntro() {
            if (animationRunning) return;
            animationRunning = true;

            const canvas = document.getElementById('flashIntroCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制闪存芯片
                const chipX = 200;
                const chipY = 150;
                const chipWidth = 200;
                const chipHeight = 100;

                // 芯片主体
                ctx.fillStyle = '#2c3e50';
                ctx.fillRect(chipX, chipY, chipWidth, chipHeight);
                
                // 芯片标签
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Flash Memory', chipX + chipWidth/2, chipY + chipHeight/2);

                // 数据流动画
                const dataBlocks = 8;
                for (let i = 0; i < dataBlocks; i++) {
                    const x = chipX + 20 + (i * 20);
                    const y = chipY + 20;
                    const alpha = Math.sin(frame * 0.1 + i * 0.5) * 0.5 + 0.5;
                    
                    ctx.fillStyle = `rgba(52, 152, 219, ${alpha})`;
                    ctx.fillRect(x, y, 15, 15);
                }

                // 电源断开/连接动画
                const powerX = 50;
                const powerY = 200;
                
                if (Math.floor(frame / 60) % 4 < 2) {
                    // 电源连接
                    ctx.fillStyle = '#27ae60';
                    ctx.fillRect(powerX, powerY, 30, 20);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText('电源ON', powerX + 15, powerY + 35);
                } else {
                    // 电源断开
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillRect(powerX, powerY, 30, 20);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText('电源OFF', powerX + 15, powerY + 35);
                }

                // 数据保持指示
                ctx.fillStyle = '#f39c12';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('数据永久保存，断电不丢失！', chipX + chipWidth + 20, chipY + chipHeight/2);

                frame++;
                if (frame < 300) {
                    requestAnimationFrame(draw);
                } else {
                    animationRunning = false;
                }
            }

            draw();
        }

        function resetFlashIntro() {
            const canvas = document.getElementById('flashIntroCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            animationRunning = false;
        }

        // 块删除动画
        function animateBlockErase() {
            if (animationRunning) return;
            animationRunning = true;

            const canvas = document.getElementById('blockEraseCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;
            const blocks = [];

            // 初始化块数据
            for (let i = 0; i < 8; i++) {
                for (let j = 0; j < 4; j++) {
                    blocks.push({
                        x: 100 + i * 50,
                        y: 100 + j * 50,
                        data: Math.random() > 0.5,
                        erasing: false,
                        erased: false
                    });
                }
            }

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 绘制标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('闪存块删除演示', canvas.width/2, 30);

                // 绘制块
                blocks.forEach((block, index) => {
                    if (block.erased) {
                        ctx.fillStyle = '#ecf0f1';
                    } else if (block.erasing) {
                        const alpha = Math.sin(frame * 0.3) * 0.5 + 0.5;
                        ctx.fillStyle = `rgba(231, 76, 60, ${alpha})`;
                    } else {
                        ctx.fillStyle = block.data ? '#3498db' : '#95a5a6';
                    }

                    ctx.fillRect(block.x, block.y, 40, 40);
                    ctx.strokeStyle = '#34495e';
                    ctx.strokeRect(block.x, block.y, 40, 40);

                    // 显示数据
                    if (!block.erased) {
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(block.data ? '1' : '0', block.x + 20, block.y + 25);
                    }
                });

                // 删除动画逻辑
                if (frame > 60 && frame < 180) {
                    const blockIndex = Math.floor((frame - 60) / 15);
                    if (blockIndex < blocks.length) {
                        blocks[blockIndex].erasing = true;
                    }
                }

                if (frame > 180) {
                    blocks.forEach(block => {
                        block.erasing = false;
                        block.erased = true;
                    });
                }

                // 说明文字
                ctx.fillStyle = '#7f8c8d';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                if (frame < 60) {
                    ctx.fillText('闪存中存储的数据', canvas.width/2, canvas.height - 60);
                } else if (frame < 180) {
                    ctx.fillText('正在按块删除...', canvas.width/2, canvas.height - 60);
                } else {
                    ctx.fillText('整个块已被删除，准备写入新数据', canvas.width/2, canvas.height - 60);
                }

                frame++;
                if (frame < 240) {
                    requestAnimationFrame(draw);
                } else {
                    animationRunning = false;
                }
            }

            draw();
        }

        function resetBlockErase() {
            const canvas = document.getElementById('blockEraseCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            animationRunning = false;
            eraseGameActive = false;
        }

        // 互动删除游戏
        function interactiveErase() {
            if (eraseGameActive) return;
            eraseGameActive = true;

            const canvas = document.getElementById('blockEraseCanvas');
            const ctx = canvas.getContext('2d');
            const blocks = [];

            // 初始化游戏块
            for (let i = 0; i < 6; i++) {
                for (let j = 0; j < 3; j++) {
                    blocks.push({
                        x: 150 + i * 50,
                        y: 120 + j * 50,
                        data: Math.random() > 0.5,
                        selected: false,
                        erased: false
                    });
                }
            }

            function drawGame() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                ctx.fillStyle = '#2c3e50';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('点击任意块来删除整行（模拟块删除）', canvas.width/2, 30);

                blocks.forEach(block => {
                    if (block.erased) {
                        ctx.fillStyle = '#ecf0f1';
                    } else if (block.selected) {
                        ctx.fillStyle = '#e74c3c';
                    } else {
                        ctx.fillStyle = block.data ? '#3498db' : '#95a5a6';
                    }

                    ctx.fillRect(block.x, block.y, 40, 40);
                    ctx.strokeStyle = '#34495e';
                    ctx.strokeRect(block.x, block.y, 40, 40);

                    if (!block.erased) {
                        ctx.fillStyle = 'white';
                        ctx.font = '12px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(block.data ? '1' : '0', block.x + 20, block.y + 25);
                    }
                });

                ctx.fillStyle = '#7f8c8d';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('注意：点击一个块会删除整行！', canvas.width/2, canvas.height - 30);
            }

            canvas.onclick = function(e) {
                if (!eraseGameActive) return;

                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                blocks.forEach((block, index) => {
                    if (x >= block.x && x <= block.x + 40 &&
                        y >= block.y && y <= block.y + 40 && !block.erased) {

                        // 删除同一行的所有块
                        const row = Math.floor(index / 6);
                        for (let i = row * 6; i < (row + 1) * 6; i++) {
                            blocks[i].erased = true;
                        }
                        drawGame();
                    }
                });
            };

            drawGame();
        }

        // 访问速度对比动画
        function animateAccessComparison() {
            if (animationRunning) return;
            animationRunning = true;

            const canvas = document.getElementById('accessCompareCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('闪存 vs RAM 访问速度对比', canvas.width/2, 30);

                // RAM部分
                const ramX = 100;
                const ramY = 100;
                ctx.fillStyle = '#27ae60';
                ctx.fillRect(ramX, ramY, 150, 80);
                ctx.fillStyle = 'white';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('RAM', ramX + 75, ramY + 45);

                // Flash部分
                const flashX = 350;
                const flashY = 100;
                ctx.fillStyle = '#3498db';
                ctx.fillRect(flashX, flashY, 150, 80);
                ctx.fillStyle = 'white';
                ctx.fillText('Flash', flashX + 75, flashY + 45);

                // 数据访问动画
                const ramSpeed = frame * 8; // RAM很快
                const flashSpeed = frame * 2; // Flash较慢

                if (ramSpeed < 200) {
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillRect(ramX + 160, ramY + 35, Math.min(ramSpeed, 180), 10);
                }

                if (flashSpeed < 200) {
                    ctx.fillStyle = '#f39c12';
                    ctx.fillRect(flashX + 160, flashY + 35, Math.min(flashSpeed, 180), 10);
                }

                // 速度标签
                ctx.fillStyle = '#2c3e50';
                ctx.font = '14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('访问速度: 极快', ramX, ramY + 100);
                ctx.fillText('访问速度: 较慢', flashX, flashY + 100);
                ctx.fillText('用途: 运算存储', ramX, ramY + 120);
                ctx.fillText('用途: 数据存储', flashX, flashY + 120);

                // 结论
                if (frame > 100) {
                    ctx.fillStyle = '#e74c3c';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('因此，Flash不能代替RAM作为主存！', canvas.width/2, 280);
                }

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                } else {
                    animationRunning = false;
                }
            }

            draw();
        }

        function resetAccessComparison() {
            const canvas = document.getElementById('accessCompareCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            animationRunning = false;
        }

        // 嵌入式系统动画
        function animateEmbeddedSystem() {
            if (animationRunning) return;
            animationRunning = true;

            const canvas = document.getElementById('embeddedCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('嵌入式系统中的Flash应用', canvas.width/2, 30);

                // 嵌入式设备
                const deviceX = 200;
                const deviceY = 80;
                ctx.fillStyle = '#34495e';
                ctx.fillRect(deviceX, deviceY, 200, 150);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('嵌入式设备', deviceX + 100, deviceY + 20);

                // Flash存储区域
                const flashAreas = [
                    {name: 'Bootloader', y: deviceY + 40, color: '#e74c3c'},
                    {name: '操作系统', y: deviceY + 70, color: '#f39c12'},
                    {name: '应用程序', y: deviceY + 100, color: '#27ae60'},
                    {name: '用户数据', y: deviceY + 130, color: '#3498db'}
                ];

                flashAreas.forEach((area, index) => {
                    const alpha = frame > index * 30 ? 1 : 0.3;
                    ctx.fillStyle = area.color;
                    ctx.globalAlpha = alpha;
                    ctx.fillRect(deviceX + 20, area.y, 160, 25);
                    ctx.globalAlpha = 1;

                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(area.name, deviceX + 100, area.y + 17);
                });

                // 对比旧的ROM
                if (frame > 120) {
                    ctx.fillStyle = '#95a5a6';
                    ctx.fillRect(50, 280, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('旧ROM', 100, 315);
                    ctx.fillText('(已淘汰)', 100, 330);

                    // 箭头
                    ctx.strokeStyle = '#2c3e50';
                    ctx.lineWidth = 3;
                    ctx.beginPath();
                    ctx.moveTo(150, 310);
                    ctx.lineTo(190, 310);
                    ctx.lineTo(180, 300);
                    ctx.moveTo(190, 310);
                    ctx.lineTo(180, 320);
                    ctx.stroke();

                    ctx.fillStyle = '#27ae60';
                    ctx.fillRect(450, 280, 100, 60);
                    ctx.fillStyle = 'white';
                    ctx.fillText('Flash', 500, 315);
                    ctx.fillText('(现代选择)', 500, 330);
                }

                frame++;
                if (frame < 180) {
                    requestAnimationFrame(draw);
                } else {
                    animationRunning = false;
                }
            }

            draw();
        }

        function resetEmbeddedSystem() {
            const canvas = document.getElementById('embeddedCanvas');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            animationRunning = false;
        }

        // 测试相关功能
        function selectOption(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(opt => {
                opt.style.pointerEvents = 'none';
                if (opt === element) {
                    opt.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (opt.textContent.includes('C.')) {
                    opt.classList.add('correct');
                }
            });

            document.getElementById('nextBtn').classList.add('show');
        }

        function showExplanation() {
            nextSection();
        }

        // 导航功能
        function nextSection() {
            if (currentSection < totalSections) {
                document.getElementById(`section${currentSection}`).classList.remove('active');
                currentSection++;
                setTimeout(() => {
                    document.getElementById(`section${currentSection}`).classList.add('active');
                    updateProgress();
                }, 300);
            }
        }

        function restartLearning() {
            currentSection = 1;
            for (let i = 1; i <= totalSections; i++) {
                document.getElementById(`section${i}`).classList.remove('active');
            }
            document.getElementById('section1').classList.add('active');
            updateProgress();

            // 重置测试
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(opt => {
                opt.classList.remove('correct', 'wrong');
                opt.style.pointerEvents = 'auto';
            });
            document.getElementById('nextBtn').classList.remove('show');
        }

        function showDetailedSummary() {
            nextSection();
        }

        function showFinalSummary() {
            nextSection();
        }

        function animateSummary() {
            if (animationRunning) return;
            animationRunning = true;

            const canvas = document.getElementById('summaryCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 标题
                ctx.fillStyle = '#2c3e50';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('闪存知识总结', canvas.width/2, 30);

                // 四个知识点
                const points = [
                    {text: '非易失性', x: 150, y: 80, color: '#27ae60'},
                    {text: '块删除', x: 450, y: 80, color: '#3498db'},
                    {text: '不能代替RAM', x: 150, y: 180, color: '#e74c3c'},
                    {text: '可代替ROM', x: 450, y: 180, color: '#f39c12'}
                ];

                points.forEach((point, index) => {
                    if (frame > index * 30) {
                        const alpha = Math.min((frame - index * 30) / 30, 1);
                        ctx.globalAlpha = alpha;

                        // 圆形背景
                        ctx.fillStyle = point.color;
                        ctx.beginPath();
                        ctx.arc(point.x, point.y, 50, 0, Math.PI * 2);
                        ctx.fill();

                        // 文字
                        ctx.fillStyle = 'white';
                        ctx.font = '14px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillText(point.text, point.x, point.y + 5);

                        ctx.globalAlpha = 1;
                    }
                });

                // 连接线
                if (frame > 120) {
                    ctx.strokeStyle = '#95a5a6';
                    ctx.lineWidth = 2;
                    ctx.setLineDash([5, 5]);

                    // 水平线
                    ctx.beginPath();
                    ctx.moveTo(200, 80);
                    ctx.lineTo(400, 80);
                    ctx.moveTo(200, 180);
                    ctx.lineTo(400, 180);
                    // 垂直线
                    ctx.moveTo(150, 130);
                    ctx.lineTo(150, 130);
                    ctx.moveTo(450, 130);
                    ctx.lineTo(450, 130);
                    ctx.stroke();
                    ctx.setLineDash([]);
                }

                // 中心总结
                if (frame > 150) {
                    ctx.fillStyle = '#34495e';
                    ctx.fillRect(250, 120, 100, 40);
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('Flash', 300, 145);
                    ctx.fillText('Memory', 300, 160);
                }

                frame++;
                if (frame < 200) {
                    requestAnimationFrame(draw);
                } else {
                    animationRunning = false;
                }
            }

            draw();
        }

        function showFinalMessage() {
            const certificate = `
🏆 学习证书 🏆

恭喜 ${new Date().toLocaleDateString()} 完成
《闪存知识互动学习》课程

✅ 掌握闪存基本概念
✅ 理解块删除机制
✅ 明确与RAM的区别
✅ 了解嵌入式应用

继续保持学习热情！
            `;
            alert(certificate);
        }

        // 更新进度条
        function updateProgress() {
            const progress = (currentSection / totalSections) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 初始化
        window.addEventListener('load', () => {
            initParticles();
            updateProgress();
        });

        window.addEventListener('resize', () => {
            const canvas = document.getElementById('particleCanvas');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
    </script>
</body>
</html>
