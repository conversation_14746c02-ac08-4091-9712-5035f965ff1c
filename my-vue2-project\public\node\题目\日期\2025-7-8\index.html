<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式处理器知识交互学习</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
        }

        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        h1, h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }

        h2 {
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            margin-top: 30px;
        }

        .question-section, .explanation-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background-color: #fdfdfd;
        }

        .question-text {
            font-size: 1.1em;
            margin-bottom: 15px;
        }

        .options {
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
        }

        .options li {
            margin-bottom: 10px;
        }

        .options label {
            display: block;
            padding: 10px 15px;
            border: 1px solid #d0d0d0;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.2s, border-color 0.2s;
        }

        .options label:hover {
            background-color: #eef;
            border-color: #aaccff;
        }

        .options input[type="radio"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .options input[type="radio"]:checked + label {
            background-color: #e6f7ff;
            border-color: #1890ff;
            font-weight: bold;
            color: #1890ff;
        }

        .correct-answer {
            font-weight: bold;
            color: #28a745;
            text-align: center;
            font-size: 1.2em;
            padding: 10px;
            background-color: #e6ffe6;
            border-radius: 5px;
            margin-top: 20px;
        }

        .interactive-sections h3 {
            color: #34495e;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 1.15em;
        }

        .interactive-section-content {
            background-color: #f9f9f9;
            border: 1px solid #e9e9e9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        canvas {
            display: block;
            border: 1px solid #ccc;
            background-color: #fff;
            margin: 20px auto;
            border-radius: 5px;
        }

        .controls {
            text-align: center;
            margin-top: 15px;
        }

        .controls button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
        }

        .controls button:hover {
            background-color: #0056b3;
        }

        .tooltip {
            position: relative;
            display: inline-block;
            cursor: help;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px 0;
            position: absolute;
            z-index: 1;
            bottom: 125%; /* At the top of the tooltip */
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #555 transparent transparent transparent;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>嵌入式处理器知识交互学习</h1>

        <div class="question-section">
            <h2>问题1 [单选题]</h2>
            <p class="question-text">以下嵌入式处理器类型中不具备内存管理单元(MMU)的是（作答此空），嵌入式操作系统（）可以运行在它上面。</p>
            <ul class="options">
                <li><input type="radio" name="answer" id="optionA" value="A"><label for="optionA">A. PowerPC750</label></li>
                <li><input type="radio" name="answer" id="optionB" value="B"><label for="optionB">B. ARM920T</label></li>
                <li><input type="radio" name="answer" id="optionC" value="C"><label for="optionC">C. Cortex-M3</label></li>
                <li><input type="radio" name="answer" id="optionD" value="D"><label for="optionD">D. MIPS32 24K</label></li>
            </ul>
            <p class="correct-answer">正确答案：C</p>
        </div>

        <div class="explanation-section">
            <h2>解析</h2>
            <p>本题考查嵌入式处理器知识。MMU是存储器管理单元的缩写，是用来管理虚拟内存系统的器件。MMU通常是CPU的一部分，本身有少量存储空间存放从虚拟地址到物理地址的匹配表。此表称作TLB(转换旁置缓冲器)。所有数据请求都送往MMU，由MMU决定数据是在RAM内还是在大容量存储器设备内。如果数据不在存储空间内，MMU将产生页面错误中断。MMU的两个主要功能是将虚地址转换成物理地址，控制存储器存取允许。MMU关掉时，虚地址直接输出到物理地址总线。Cortex-M3处理器采用ARMv7-M架构，它包括所有的16位Thumb指令集和基本的32位Thumb-2指令集架构。Cortex-M3支持线程模式和处理模式。在复位时处理器进入“线程模式”，异常返回时也会进入该模式，特权和用户(非特权)模式代码能够在“线程模式”下运行。出现异常模式时处理器进入“处理模式”，在处理模式下，所有代码都是特权访问的。µC/OS-II可以运行在Cortex-M3处理器上。</p>

            <div class="interactive-sections">
                <h3>1. MMU基础概念</h3>
                <div class="interactive-section-content">
                    <p>MMU是内存管理单元，负责虚拟地址到物理地址的转换。它内部有一个TLB（转换旁置缓冲器），存储着地址映射关系。</p>
                    <canvas id="mmuBasicsCanvas" width="700" height="200"></canvas>
                    <div class="controls">
                        <button onclick="startMMUBasicsAnimation()">开始演示</button>
                        <button onclick="resetMMUBasicsAnimation()">重置</button>
                    </div>
                </div>

                <h3>2. MMU的功能：地址转换与访问控制</h3>
                <div class="interactive-section-content">
                    <p>MMU的两个主要功能是将虚拟地址转换成物理地址，并控制存储器的访问权限。您可以输入一个虚拟地址，看看MMU如何将其转换为物理地址。</p>
                    <canvas id="mmuTranslationCanvas" width="700" height="250"></canvas>
                    <div class="controls">
                        <label for="virtualAddressInput">输入虚拟地址 (例如: 0x1000, 0x2000, 0x3000, 0x4000 (页面错误)):</label>
                        <input type="text" id="virtualAddressInput" value="0x1000" style="margin: 0 10px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                        <button onclick="startMMUTranslationAnimation()">执行转换</button>
                        <button onclick="resetMMUTranslationAnimation()">重置</button>
                    </div>
                </div>

                <h3>3. 页面错误中断 (Page Fault)</h3>
                <div class="interactive-section-content">
                    <p>当请求的数据不在内存中（即虚拟地址没有对应的物理地址映射）时，MMU会产生页面错误中断，操作系统会介入处理。</p>
                    <canvas id="pageFaultCanvas" width="700" height="200"></canvas>
                    <div class="controls">
                        <button onclick="startPageFaultAnimation()">演示页面错误</button>
                        <button onclick="resetPageFaultAnimation()">重置</button>
                    </div>
                </div>

                <h3>4. MMU关闭时</h3>
                <div class="interactive-section-content">
                    <p>如果MMU被关闭（或者处理器本身不带MMU，例如Cortex-M3），虚拟地址会直接作为物理地址使用，没有地址转换过程。</p>
                    <canvas id="mmuDisabledCanvas" width="700" height="150"></canvas>
                    <div class="controls">
                        <button onclick="startMMUDisabledAnimation()">开始演示</button>
                        <button onclick="resetMMUDisabledAnimation()">重置</button>
                    </div>
                </div>

                <h3>5. Cortex-M3 特性与模式</h3>
                <div class="interactive-section-content">
                    <p>Cortex-M3处理器是本题的正确答案，因为它不具备MMU。它支持两种主要运行模式：线程模式和处理模式。µC/OS-II等实时操作系统可以运行在其上。</p>
                    <canvas id="cortexM3ModesCanvas" width="700" height="250"></canvas>
                    <div class="controls">
                        <button onclick="toggleCortexM3Mode()">切换模式 (线程/处理)</button>
                        <button onclick="resetCortexM3Animation()">重置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Common drawing functions
        function drawBox(ctx, text, x, y, width, height, fillColor = '#f0f8ff', borderColor = '#6495ed') {
            ctx.fillStyle = fillColor;
            ctx.fillRect(x, y, width, height);
            ctx.strokeStyle = borderColor;
            ctx.lineWidth = 2;
            ctx.strokeRect(x, y, width, height);
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + width / 2, y + height / 2);
        }

        function drawArrow(ctx, fromX, fromY, toX, toY, color = '#333', arrowHeadSize = 10) {
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            // Arrowhead
            const angle = Math.atan2(toY - fromY, toX - fromX);
            ctx.lineTo(toX - arrowHeadSize * Math.cos(angle - Math.PI / 6), toY - arrowHeadSize * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowHeadSize * Math.cos(angle + Math.PI / 6), toY - arrowHeadSize * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
        }

        // 1. MMU Basics Animation
        const mmuBasicsCanvas = document.getElementById('mmuBasicsCanvas');
        const mmuBasicsCtx = mmuBasicsCanvas.getContext('2d');
        let mmuBasicsAnimationId = null;
        let mmuBasicsProgress = 0;

        function animateMMUBasics() {
            mmuBasicsCtx.clearRect(0, 0, mmuBasicsCanvas.width, mmuBasicsCanvas.height);

            const boxWidth = 120;
            const boxHeight = 60;
            const gap = 100;
            const startY = (mmuBasicsCanvas.height - boxHeight) / 2;

            const vaX = mmuBasicsCanvas.width / 2 - boxWidth * 1.5 - gap;
            const mmuX = mmuBasicsCanvas.width / 2 - boxWidth / 2;
            const paX = mmuBasicsCanvas.width / 2 + boxWidth * 0.5 + gap;

            drawBox(mmuBasicsCtx, '虚拟地址 (VA)', vaX, startY, boxWidth, boxHeight);
            drawBox(mmuBasicsCtx, 'MMU', mmuX, startY, boxWidth, boxHeight, '#f8f8ff', '#9370db');
            drawBox(mmuBasicsCtx, '物理地址 (PA)', paX, startY, boxWidth, boxHeight);

            // TLB representation inside MMU
            mmuBasicsCtx.fillStyle = '#add8e6';
            mmuBasicsCtx.fillRect(mmuX + 10, startY + boxHeight / 2 - 15, boxWidth - 20, 30);
            mmuBasicsCtx.strokeStyle = '#4682b4';
            mmuBasicsCtx.lineWidth = 1;
            mmuBasicsCtx.strokeRect(mmuX + 10, startY + boxHeight / 2 - 15, boxWidth - 20, 30);
            mmuBasicsCtx.fillStyle = '#333';
            mmuBasicsCtx.font = '12px Arial';
            mmuBasicsCtx.fillText('TLB (地址映射表)', mmuX + boxWidth / 2, startY + boxHeight / 2);

            if (mmuBasicsProgress < 1) {
                const arrowX1 = vaX + boxWidth + (gap * mmuBasicsProgress);
                drawArrow(mmuBasicsCtx, vaX + boxWidth, startY + boxHeight / 2, arrowX1, startY + boxHeight / 2);
            } else if (mmuBasicsProgress < 2) {
                drawArrow(mmuBasicsCtx, vaX + boxWidth, startY + boxHeight / 2, mmuX, startY + boxHeight / 2);
                const arrowX2 = mmuX + boxWidth + (gap * (mmuBasicsProgress - 1));
                drawArrow(mmuBasicsCtx, mmuX + boxWidth, startY + boxHeight / 2, arrowX2, startY + boxHeight / 2);
            } else {
                drawArrow(mmuBasicsCtx, vaX + boxWidth, startY + boxHeight / 2, mmuX, startY + boxHeight / 2);
                drawArrow(mmuBasicsCtx, mmuX + boxWidth, startY + boxHeight / 2, paX, startY + boxHeight / 2);
            }

            mmuBasicsProgress += 0.02;
            if (mmuBasicsProgress < 2.5) { // Adjusted to allow full arrow drawing
                mmuBasicsAnimationId = requestAnimationFrame(animateMMUBasics);
            }
        }

        function startMMUBasicsAnimation() {
            if (mmuBasicsAnimationId) cancelAnimationFrame(mmuBasicsAnimationId);
            mmuBasicsProgress = 0;
            mmuBasicsAnimationId = requestAnimationFrame(animateMMUBasics);
        }

        function resetMMUBasicsAnimation() {
            if (mmuBasicsAnimationId) cancelAnimationFrame(mmuBasicsAnimationId);
            mmuBasicsCtx.clearRect(0, 0, mmuBasicsCanvas.width, mmuBasicsCanvas.height);
            const boxWidth = 120;
            const boxHeight = 60;
            const gap = 100;
            const startY = (mmuBasicsCanvas.height - boxHeight) / 2;

            const vaX = mmuBasicsCanvas.width / 2 - boxWidth * 1.5 - gap;
            const mmuX = mmuBasicsCanvas.width / 2 - boxWidth / 2;
            const paX = mmuBasicsCanvas.width / 2 + boxWidth * 0.5 + gap;

            drawBox(mmuBasicsCtx, '虚拟地址 (VA)', vaX, startY, boxWidth, boxHeight);
            drawBox(mmuBasicsCtx, 'MMU', mmuX, startY, boxWidth, boxHeight, '#f8f8ff', '#9370db');
            drawBox(mmuBasicsCtx, '物理地址 (PA)', paX, startY, boxWidth, boxHeight);
            // TLB representation inside MMU
            mmuBasicsCtx.fillStyle = '#add8e6';
            mmuBasicsCtx.fillRect(mmuX + 10, startY + boxHeight / 2 - 15, boxWidth - 20, 30);
            mmuBasicsCtx.strokeStyle = '#4682b4';
            mmuBasicsCtx.lineWidth = 1;
            mmuBasicsCtx.fillText('TLB (地址映射表)', mmuX + boxWidth / 2, startY + boxHeight / 2);
        }

        // 2. MMU Translation Animation
        const mmuTranslationCanvas = document.getElementById('mmuTranslationCanvas');
        const mmuTranslationCtx = mmuTranslationCanvas.getContext('2d');
        let mmuTranslationAnimationId = null;
        let translationProgress = 0;
        let currentVirtualAddress = '0x1000';
        const translationMap = {
            '0x1000': '0xA000 (RAM)',
            '0x2000': '0xB000 (RAM)',
            '0x3000': '0xC000 (Flash)',
            '0x4000': '未映射 (Page Fault)'
        };

        function drawMMUTranslation() {
            mmuTranslationCtx.clearRect(0, 0, mmuTranslationCanvas.width, mmuTranslationCanvas.height);

            const vaBox = { x: 50, y: 100, width: 120, height: 60 };
            const mmuBox = { x: 280, y: 100, width: 140, height: 60 };
            const paBox = { x: 530, y: 100, width: 120, height: 60 };
            const tlbBox = { x: mmuBox.x + 10, y: mmuBox.y + 10, width: mmuBox.width - 20, height: mmuBox.height - 20 };

            drawBox(mmuTranslationCtx, '虚拟地址', vaBox.x, vaBox.y, vaBox.width, vaBox.height);
            drawBox(mmuTranslationCtx, 'MMU', mmuBox.x, mmuBox.y, mmuBox.width, mmuBox.height, '#e0ffff', '#00ced1');
            drawBox(mmuTranslationCtx, '物理地址', paBox.x, paBox.y, paBox.width, paBox.height);

            // Draw TLB inside MMU
            mmuTranslationCtx.fillStyle = '#f0f8ff';
            mmuTranslationCtx.fillRect(tlbBox.x, tlbBox.y, tlbBox.width, tlbBox.height);
            mmuTranslationCtx.strokeStyle = '#6495ed';
            mmuTranslationCtx.lineWidth = 1;
            mmuTranslationCtx.strokeRect(tlbBox.x, tlbBox.y, tlbBox.width, tlbBox.height);

            mmuTranslationCtx.fillStyle = '#333';
            mmuTranslationCtx.font = '12px Arial';
            mmuTranslationCtx.fillText('TLB (地址映射)', tlbBox.x + tlbBox.width / 2, tlbBox.y + tlbBox.height / 4);
            
            let yOffset = tlbBox.y + tlbBox.height / 2;
            for (const va in translationMap) {
                if (translationMap[va] !== '未映射 (Page Fault)') {
                    mmuTranslationCtx.fillText(`${va} -> ${translationMap[va]}`, tlbBox.x + tlbBox.width / 2, yOffset);
                    yOffset += 15;
                }
            }


            // Animation for VA to MMU
            if (translationProgress < 1) {
                const currentX = vaBox.x + vaBox.width + (mmuBox.x - (vaBox.x + vaBox.width)) * translationProgress;
                drawArrow(mmuTranslationCtx, vaBox.x + vaBox.width, vaBox.y + vaBox.height / 2, currentX, vaBox.y + vaBox.height / 2, '#ff4500');
                mmuTranslationCtx.fillStyle = '#000';
                mmuTranslationCtx.font = '14px Arial';
                mmuTranslationCtx.fillText(currentVirtualAddress, vaBox.x + vaBox.width / 2, vaBox.y + vaBox.height / 2 - 20); // Address text above box
            } else if (translationProgress < 2) {
                drawArrow(mmuTranslationCtx, vaBox.x + vaBox.width, vaBox.y + vaBox.height / 2, mmuBox.x, mmuBox.y + mmuBox.height / 2, '#ff4500');
                mmuTranslationCtx.fillStyle = '#000';
                mmuTranslationCtx.font = '14px Arial';
                mmuTranslationCtx.fillText(currentVirtualAddress, vaBox.x + vaBox.width / 2, vaBox.y + vaBox.height / 2 - 20);

                const currentPa = translationMap[currentVirtualAddress];
                if (currentPa && currentPa !== '未映射 (Page Fault)') {
                    const currentX = mmuBox.x + mmuBox.width + (paBox.x - (mmuBox.x + mmuBox.width)) * (translationProgress - 1);
                    drawArrow(mmuTranslationCtx, mmuBox.x + mmuBox.width, mmuBox.y + mmuBox.height / 2, currentX, mmuBox.y + mmuBox.height / 2, '#32cd32');
                    mmuTranslationCtx.fillStyle = '#000';
                    mmuTranslationCtx.font = '14px Arial';
                    mmuTranslationCtx.fillText(currentPa, mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height / 2 + 30); // Show translated PA near MMU
                } else {
                     // If page fault, show error message
                    mmuTranslationCtx.fillStyle = 'red';
                    mmuTranslationCtx.font = '20px Arial';
                    mmuTranslationCtx.fillText('页面错误!', mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height / 2);
                }

            } else {
                drawArrow(mmuTranslationCtx, vaBox.x + vaBox.width, vaBox.y + vaBox.height / 2, mmuBox.x, mmuBox.y + mmuBox.height / 2, '#ff4500');
                mmuTranslationCtx.fillStyle = '#000';
                mmuTranslationCtx.font = '14px Arial';
                mmuTranslationCtx.fillText(currentVirtualAddress, vaBox.x + vaBox.width / 2, vaBox.y + vaBox.height / 2 - 20);

                const currentPa = translationMap[currentVirtualAddress];
                if (currentPa && currentPa !== '未映射 (Page Fault)') {
                    drawArrow(mmuTranslationCtx, mmuBox.x + mmuBox.width, mmuBox.y + mmuBox.height / 2, paBox.x, paBox.y + paBox.height / 2, '#32cd32');
                    mmuTranslationCtx.fillStyle = '#000';
                    mmuTranslationCtx.font = '14px Arial';
                    mmuTranslationCtx.fillText(currentPa, paBox.x + paBox.width / 2, paBox.y + paBox.height / 2 - 20);
                } else {
                    mmuTranslationCtx.fillStyle = 'red';
                    mmuTranslationCtx.font = '20px Arial';
                    mmuTranslationCtx.fillText('页面错误!', mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height / 2);
                }
            }


            translationProgress += 0.01;
            if (translationProgress < 2.5) {
                mmuTranslationAnimationId = requestAnimationFrame(drawMMUTranslation);
            }
        }

        function startMMUTranslationAnimation() {
            if (mmuTranslationAnimationId) cancelAnimationFrame(mmuTranslationAnimationId);
            currentVirtualAddress = document.getElementById('virtualAddressInput').value;
            translationProgress = 0;
            mmuTranslationAnimationId = requestAnimationFrame(drawMMUTranslation);
        }

        function resetMMUTranslationAnimation() {
            if (mmuTranslationAnimationId) cancelAnimationFrame(mmuTranslationAnimationId);
            mmuTranslationCtx.clearRect(0, 0, mmuTranslationCanvas.width, mmuTranslationCanvas.height);
            const vaBox = { x: 50, y: 100, width: 120, height: 60 };
            const mmuBox = { x: 280, y: 100, width: 140, height: 60 };
            const paBox = { x: 530, y: 100, width: 120, height: 60 };
            const tlbBox = { x: mmuBox.x + 10, y: mmuBox.y + 10, width: mmuBox.width - 20, height: mmuBox.height - 20 };

            drawBox(mmuTranslationCtx, '虚拟地址', vaBox.x, vaBox.y, vaBox.width, vaBox.height);
            drawBox(mmuTranslationCtx, 'MMU', mmuBox.x, mmuBox.y, mmuBox.width, mmuBox.height, '#e0ffff', '#00ced1');
            drawBox(mmuTranslationCtx, '物理地址', paBox.x, paBox.y, paBox.width, paBox.height);
            
            mmuTranslationCtx.fillStyle = '#f0f8ff';
            mmuTranslationCtx.fillRect(tlbBox.x, tlbBox.y, tlbBox.width, tlbBox.height);
            mmuTranslationCtx.strokeStyle = '#6495ed';
            mmuTranslationCtx.lineWidth = 1;
            mmuTranslationCtx.strokeRect(tlbBox.x, tlbBox.y, tlbBox.width, tlbBox.height);
            mmuTranslationCtx.fillStyle = '#333';
            mmuTranslationCtx.font = '12px Arial';
            mmuTranslationCtx.fillText('TLB (地址映射)', tlbBox.x + tlbBox.width / 2, tlbBox.y + tlbBox.height / 4);

            let yOffset = tlbBox.y + tlbBox.height / 2;
            for (const va in translationMap) {
                if (translationMap[va] !== '未映射 (Page Fault)') {
                    mmuTranslationCtx.fillText(`${va} -> ${translationMap[va]}`, tlbBox.x + tlbBox.width / 2, yOffset);
                    yOffset += 15;
                }
            }
        }


        // 3. Page Fault Animation
        const pageFaultCanvas = document.getElementById('pageFaultCanvas');
        const pageFaultCtx = pageFaultCanvas.getContext('2d');
        let pageFaultAnimationId = null;
        let pageFaultProgress = 0;

        function drawPageFault() {
            pageFaultCtx.clearRect(0, 0, pageFaultCanvas.width, pageFaultCanvas.height);

            const vaBox = { x: 50, y: 50, width: 120, height: 60 };
            const mmuBox = { x: 280, y: 50, width: 120, height: 60 };
            const storageBox = { x: 530, y: 50, width: 120, height: 60 };

            drawBox(pageFaultCtx, '请求者 (CPU)', vaBox.x, vaBox.y, vaBox.width, vaBox.height);
            drawBox(pageFaultCtx, 'MMU', mmuBox.x, mmuBox.y, mmuBox.width, mmuBox.height, '#fffafa', '#dc143c');
            drawBox(pageFaultCtx, '物理内存/存储', storageBox.x, storageBox.y, storageBox.width, storageBox.height);

            // Animation
            if (pageFaultProgress < 1) {
                const currentX = vaBox.x + vaBox.width + (mmuBox.x - (vaBox.x + vaBox.width)) * pageFaultProgress;
                drawArrow(pageFaultCtx, vaBox.x + vaBox.width, vaBox.y + vaBox.height / 2, currentX, vaBox.y + vaBox.height / 2, '#ff4500');
                pageFaultCtx.fillStyle = '#000';
                pageFaultCtx.font = '14px Arial';
                pageFaultCtx.fillText('虚拟地址请求', vaBox.x + vaBox.width / 2, vaBox.y + vaBox.height / 2 - 20);
            } else if (pageFaultProgress < 2) {
                drawArrow(pageFaultCtx, vaBox.x + vaBox.width, vaBox.y + vaBox.height / 2, mmuBox.x, mmuBox.y + mmuBox.height / 2, '#ff4500');
                pageFaultCtx.fillStyle = '#000';
                pageFaultCtx.font = '14px Arial';
                pageFaultCtx.fillText('虚拟地址请求', vaBox.x + vaBox.width / 2, vaBox.y + vaBox.height / 2 - 20);

                pageFaultCtx.fillStyle = 'red';
                pageFaultCtx.font = '24px Arial';
                pageFaultCtx.fillText('X', mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height / 2); // Show 'X' for not found

                if (pageFaultProgress > 1.5) { // Animate page fault message
                    pageFaultCtx.fillText('页面错误中断!', pageFaultCanvas.width / 2, mmuBox.y + mmuBox.height + 40);
                    // Arrow from MMU to nothing (or back to CPU for OS handling)
                    drawArrow(pageFaultCtx, mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height, mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height + 30, 'red');
                }
            } else {
                drawArrow(pageFaultCtx, vaBox.x + vaBox.width, vaBox.y + vaBox.height / 2, mmuBox.x, mmuBox.y + mmuBox.height / 2, '#ff4500');
                pageFaultCtx.fillStyle = '#000';
                pageFaultCtx.font = '14px Arial';
                pageFaultCtx.fillText('虚拟地址请求', vaBox.x + vaBox.width / 2, vaBox.y + vaBox.height / 2 - 20);

                pageFaultCtx.fillStyle = 'red';
                pageFaultCtx.font = '24px Arial';
                pageFaultCtx.fillText('X', mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height / 2);
                pageFaultCtx.fillText('页面错误中断!', pageFaultCanvas.width / 2, mmuBox.y + mmuBox.height + 40);
                drawArrow(pageFaultCtx, mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height, mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height + 30, 'red');
            }


            pageFaultProgress += 0.01;
            if (pageFaultProgress < 2.5) {
                pageFaultAnimationId = requestAnimationFrame(drawPageFault);
            }
        }

        function startPageFaultAnimation() {
            if (pageFaultAnimationId) cancelAnimationFrame(pageFaultAnimationId);
            pageFaultProgress = 0;
            pageFaultAnimationId = requestAnimationFrame(drawPageFault);
        }

        function resetPageFaultAnimation() {
            if (pageFaultAnimationId) cancelAnimationFrame(pageFaultAnimationId);
            pageFaultCtx.clearRect(0, 0, pageFaultCanvas.width, pageFaultCanvas.height);
            const vaBox = { x: 50, y: 50, width: 120, height: 60 };
            const mmuBox = { x: 280, y: 50, width: 120, height: 60 };
            const storageBox = { x: 530, y: 50, width: 120, height: 60 };
            drawBox(pageFaultCtx, '请求者 (CPU)', vaBox.x, vaBox.y, vaBox.width, vaBox.height);
            drawBox(pageFaultCtx, 'MMU', mmuBox.x, mmuBox.y, mmuBox.width, mmuBox.height, '#fffafa', '#dc143c');
            drawBox(pageFaultCtx, '物理内存/存储', storageBox.x, storageBox.y, storageBox.width, storageBox.height);
        }

        // 4. MMU Disabled Animation
        const mmuDisabledCanvas = document.getElementById('mmuDisabledCanvas');
        const mmuDisabledCtx = mmuDisabledCanvas.getContext('2d');
        let mmuDisabledAnimationId = null;
        let mmuDisabledProgress = 0;

        function drawMMUDisabled() {
            mmuDisabledCtx.clearRect(0, 0, mmuDisabledCanvas.width, mmuDisabledCanvas.height);

            const vaBox = { x: 100, y: 40, width: 150, height: 60 };
            const paBox = { x: 450, y: 40, width: 150, height: 60 };

            drawBox(mmuDisabledCtx, '虚拟地址 (VA)', vaBox.x, vaBox.y, vaBox.width, vaBox.height);
            drawBox(mmuDisabledCtx, '物理地址 (PA)', paBox.x, paBox.y, paBox.width, paBox.height);

            mmuDisabledCtx.fillStyle = '#808080';
            mmuDisabledCtx.font = '16px Arial';
            mmuDisabledCtx.fillText('MMU 被禁用 / 不存在', mmuDisabledCanvas.width / 2, vaBox.y - 15);


            if (mmuDisabledProgress < 1) {
                const arrowX = vaBox.x + vaBox.width + (paBox.x - (vaBox.x + vaBox.width)) * mmuDisabledProgress;
                drawArrow(mmuDisabledCtx, vaBox.x + vaBox.width, vaBox.y + vaBox.height / 2, arrowX, vaBox.y + vaBox.height / 2, '#4682b4');
            } else {
                drawArrow(mmuDisabledCtx, vaBox.x + vaBox.width, vaBox.y + vaBox.height / 2, paBox.x, paBox.y + paBox.height / 2, '#4682b4');
                mmuDisabledCtx.fillStyle = '#000';
                mmuDisabledCtx.font = '16px Arial';
                mmuDisabledCtx.fillText('直接映射', mmuDisabledCanvas.width / 2, vaBox.y + vaBox.height / 2 + 30);
            }

            mmuDisabledProgress += 0.01;
            if (mmuDisabledProgress < 1.5) {
                mmuDisabledAnimationId = requestAnimationFrame(drawMMUDisabled);
            }
        }

        function startMMUDisabledAnimation() {
            if (mmuDisabledAnimationId) cancelAnimationFrame(mmuDisabledAnimationId);
            mmuDisabledProgress = 0;
            mmuDisabledAnimationId = requestAnimationFrame(drawMMUDisabled);
        }

        function resetMMUDisabledAnimation() {
            if (mmuDisabledAnimationId) cancelAnimationFrame(mmuDisabledAnimationId);
            mmuDisabledCtx.clearRect(0, 0, mmuDisabledCanvas.width, mmuDisabledCanvas.height);
            const vaBox = { x: 100, y: 40, width: 150, height: 60 };
            const paBox = { x: 450, y: 40, width: 150, height: 60 };
            drawBox(mmuDisabledCtx, '虚拟地址 (VA)', vaBox.x, vaBox.y, vaBox.width, vaBox.height);
            drawBox(mmuDisabledCtx, '物理地址 (PA)', paBox.x, paBox.y, paBox.width, paBox.height);
            mmuDisabledCtx.fillStyle = '#808080';
            mmuDisabledCtx.font = '16px Arial';
            mmuDisabledCtx.fillText('MMU 被禁用 / 不存在', mmuDisabledCanvas.width / 2, vaBox.y - 15);
        }

        // 5. Cortex-M3 Modes Animation
        const cortexM3ModesCanvas = document.getElementById('cortexM3ModesCanvas');
        const cortexM3ModesCtx = cortexM3ModesCanvas.getContext('2d');
        let currentMode = '线程模式'; // '线程模式' or '处理模式'
        let cortexM3AnimationId = null;

        function drawCortexM3Modes() {
            cortexM3ModesCtx.clearRect(0, 0, cortexM3ModesCanvas.width, cortexM3ModesCanvas.height);

            const threadModeBox = { x: 100, y: 50, width: 150, height: 80 };
            const handlerModeBox = { x: 450, y: 50, width: 150, height: 80 };

            drawBox(cortexM3ModesCtx, '线程模式 (用户/特权)', threadModeBox.x, threadModeBox.y, threadModeBox.width, threadModeBox.height, currentMode === '线程模式' ? '#dff0d8' : '#f0f0f0', currentMode === '线程模式' ? '#4CAF50' : '#ccc');
            drawBox(cortexM3ModesCtx, '处理模式 (特权)', handlerModeBox.x, handlerModeBox.y, handlerModeBox.width, handlerModeBox.height, currentMode === '处理模式' ? '#dff0d8' : '#f0f0f0', currentMode === '处理模式' ? '#4CAF50' : '#ccc');

            cortexM3ModesCtx.fillStyle = '#333';
            cortexM3ModesCtx.font = '14px Arial';
            cortexM3ModesCtx.textAlign = 'center';
            cortexM3ModesCtx.fillText('复位时进入', threadModeBox.x + threadModeBox.width / 2, threadModeBox.y - 15);
            cortexM3ModesCtx.fillText('异常时进入', handlerModeBox.x + handlerModeBox.width / 2, handlerModeBox.y - 15);

            // Arrows indicating transitions
            drawArrow(cortexM3ModesCtx, threadModeBox.x + threadModeBox.width, threadModeBox.y + threadModeBox.height / 2, handlerModeBox.x, handlerModeBox.y + handlerModeBox.height / 2, '#007bff');
            cortexM3ModesCtx.fillText('异常/中断', threadModeBox.x + threadModeBox.width + 70, threadModeBox.y + threadModeBox.height / 2 - 10);

            drawArrow(cortexM3ModesCtx, handlerModeBox.x, handlerModeBox.y + handlerModeBox.height / 2 + 30, threadModeBox.x + threadModeBox.width, threadModeBox.y + threadModeBox.height / 2 + 30, '#007bff');
            cortexM3ModesCtx.fillText('异常返回', handlerModeBox.x - 70, handlerModeBox.y + handlerModeBox.height / 2 + 20);

            // Current Mode Indicator
            cortexM3ModesCtx.fillStyle = '#ff6347';
            cortexM3ModesCtx.font = '18px Arial';
            cortexM3ModesCtx.fillText(`当前模式: ${currentMode}`, cortexM3ModesCanvas.width / 2, cortexM3ModesCanvas.height - 30);
        }

        function toggleCortexM3Mode() {
            currentMode = currentMode === '线程模式' ? '处理模式' : '线程模式';
            drawCortexM3Modes();
        }

        function resetCortexM3Animation() {
            currentMode = '线程模式';
            drawCortexM3Modes();
        }

        // Initial draws when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            resetMMUBasicsAnimation();
            resetMMUTranslationAnimation();
            resetPageFaultAnimation();
            resetMMUDisabledAnimation();
            drawCortexM3Modes(); // Initial draw for Cortex-M3 modes
        });
    </script>
</body>
</html> 