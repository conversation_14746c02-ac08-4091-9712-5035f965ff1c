<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Prospect</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #1976d2; /* 蓝色，代表深远和未来 */
            --secondary-color: #1565c0;
            --accent-color: #ffca28; /* 金色，代表希望和财富 */
            --light-bg: #e3f2fd;
            --panel-bg: #ffffff;
            --text-color: #0d47a1;
        }

        body {
            font-family: 'Roboto', 'Noto Sans SC', sans-serif;
            background-color: #bbdefb;
            color: var(--text-color);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: row;
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            max-height: 800px;
            background-color: var(--panel-bg);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .word-panel {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background-color: var(--light-bg);
            overflow-y: auto;
        }

        .word-panel h1 {
            font-size: 3.5em;
            color: var(--primary-color);
            margin: 0;
        }

        .word-panel .pronunciation {
            font-size: 1.5em;
            color: var(--secondary-color);
            margin-bottom: 20px;
        }

        .word-panel .details p {
            font-size: 1.1em;
            line-height: 1.6;
            margin: 10px 0;
        }

        .word-panel .details strong {
            color: var(--secondary-color);
        }

        .word-panel .example {
            margin-top: 20px;
            padding-left: 15px;
            border-left: 3px solid var(--primary-color);
            font-style: italic;
            color: #1a237e;
        }
        
        .breakdown-section {
            margin-top: 25px;
            padding: 20px;
            background-color: #fffde7;
            border-radius: 10px;
        }

        .breakdown-section h3 {
            margin-top: 0;
            color: var(--secondary-color);
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .morpheme-btn {
            margin: 5px;
            padding: 8px 15px;
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            background-color: transparent;
            color: var(--primary-color);
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }

        .morpheme-btn:hover, .morpheme-btn.active {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .animation-panel {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            background: #fafafa;
        }

        .activity-title {
            font-size: 1.8em;
            color: var(--primary-color);
            margin-bottom: 15px;
            text-align: center;
        }
        
        .activity-wrapper {
            display: none;
            width: 100%;
            height: calc(100% - 100px);
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .activity-wrapper.active {
            display: flex;
        }
        
        .game-container {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px;
            background: #f1f8e9;
            border: 1px solid #dcedc8;
            overflow: hidden;
        }

        .control-button {
            margin-top: 20px;
            padding: 15px 30px;
            font-size: 1.2em;
            color: #fff;
            background-color: var(--primary-color);
            border: none;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.3s;
        }

        /* Look Forward Game */
        #character {
            width: 50px;
            height: 100px;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 100"><circle cx="25" cy="20" r="15" fill="%2364b5f6"/><rect x="15" y="40" width="20" height="60" fill="%2364b5f6"/></svg>');
            position: absolute;
            left: 10%;
            bottom: 0;
        }
        .future-path {
            position: absolute;
            right: 10%;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 3em;
            color: var(--accent-color);
            background: rgba(255,255,255,0.8);
            transition: all 1s ease;
        }
        #path1 { top: 20%; transform: scale(0.8); filter: blur(5px); opacity: 0.5; }
        #path2 { top: 50%; transform: translateY(-50%) scale(0.8); filter: blur(5px); opacity: 0.5; }
        #path3 { bottom: 20%; transform: scale(0.8); filter: blur(5px); opacity: 0.5; }
        .look-forward .future-path { filter: blur(0); opacity: 1; transform: scale(1); }
        
        /* Prospecting Game */
        #prospecting-canvas {
            background: #8d6e63;
            cursor: crosshair;
        }
        #score-display {
            position: absolute;
            top: 10px;
            right: 20px;
            font-size: 1.5em;
            color: var(--accent-color);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>prospect</h1>
            <p class="pronunciation">[ˈprɒspekt]</p>
            <div class="details">
                <p><strong>词性：</strong> n. 前景, 展望; v. 勘探</p>
                <p><strong>词源:</strong> pro-(向前) + spect(看) → 向前看</p>
                <p><strong>含义：</strong><br>1. (n.) 对未来可能发生之事的期待。<br>2. (v.) 勘探（矿物）。</p>
                <div class="example">
                    <p><strong>例句1:</strong> The prospect of a long holiday is very exciting.</p>
                    <p><strong>翻译1:</strong> 一想到长假前景就非常激动人心。</p>
                    <p><strong>例句2:</strong> They are prospecting for gold in the mountains.</p>
                    <p><strong>翻译2:</strong> 他们正在山里勘探金矿。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>单词动画</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="look-forward-game">动画: 展望未来</button>
                    <button class="morpheme-btn" data-activity="prospecting-game">互动: 淘金热</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p>点击左侧按钮，开启探索之旅！</p></div>
            
            <div id="look-forward-game" class="activity-wrapper">
                <div class="game-container">
                    <div id="character"></div>
                    <div id="path1" class="future-path">🌟</div>
                    <div id="path2" class="future-path">📚</div>
                    <div id="path3" class="future-path">💡</div>
                </div>
                <button class="control-button" id="look-forward-btn">展望</button>
            </div>
            
            <div id="prospecting-game" class="activity-wrapper">
                <div class="game-container">
                    <canvas id="prospecting-canvas"></canvas>
                    <div id="score-display">Gold: 0</div>
                </div>
            </div>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');
        let currentCleanup = null;

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                if (currentCleanup) currentCleanup();
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                const targetId = btn.dataset.activity;
                document.getElementById(targetId)?.classList.add('active');
                
                if (targetId === 'look-forward-game') currentCleanup = setupLookForwardGame();
                else if (targetId === 'prospecting-game') currentCleanup = setupProspectingGame();
                else currentCleanup = null;
            });
        });

        function setupLookForwardGame() {
            const btn = document.getElementById('look-forward-btn');
            const gameContainer = document.querySelector('#look-forward-game .game-container');
            const action = () => gameContainer.classList.toggle('look-forward');
            btn.onclick = action;
            return () => gameContainer.classList.remove('look-forward');
        }

        function setupProspectingGame() {
            const canvas = document.getElementById('prospecting-canvas');
            const scoreDisplay = document.getElementById('score-display');
            if (!canvas) return null;

            const ctx = canvas.getContext('2d');
            let animationId;
            let nuggets = [];
            let score = 0;
            const drillSize = 20;

            class Nugget {
                constructor(x, y, r) {
                    this.x = x; this.y = y; this.r = r; this.found = false;
                }
                draw() {
                    if (this.found) {
                        ctx.fillStyle = 'gold';
                        ctx.beginPath();
                        ctx.arc(this.x, this.y, this.r, 0, Math.PI * 2);
                        ctx.fill();
                    }
                }
            }
            
            function init() {
                const container = canvas.parentElement;
                canvas.width = container.clientWidth;
                canvas.height = container.clientHeight;
                score = 0;
                updateScore();
                nuggets = [];
                for(let i = 0; i < 15; i++) {
                    nuggets.push(new Nugget(
                        Math.random() * canvas.width,
                        Math.random() * (canvas.height - 100) + 100, // Deeper down
                        Math.random() * 5 + 3
                    ));
                }
            }

            function drawGround() {
                ctx.fillStyle = '#6d4c41';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#a1887f';
                ctx.fillRect(0, 0, canvas.width, canvas.height * 0.6);
                ctx.fillStyle = '#d7ccc8';
                ctx.fillRect(0, 0, canvas.width, canvas.height * 0.3);
            }

            function animate() {
                drawGround();
                nuggets.forEach(n => n.draw());
                animationId = requestAnimationFrame(animate);
            }
            
            function updateScore() {
                scoreDisplay.textContent = `Gold: ${score}`;
            }

            function handleDrill(e) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // Animate drill hole
                gsap.fromTo(canvas, {
                    '--hole-x': `${x}px`,
                    '--hole-y': `${y}px`,
                    '--hole-size': '0px'
                }, {
                    '--hole-size': `${drillSize*2}px`,
                    duration: 0.5,
                    onUpdate: function() {
                        const holeX = parseFloat(gsap.getProperty(canvas, '--hole-x'));
                        const holeY = parseFloat(gsap.getProperty(canvas, '--hole-y'));
                        const holeSize = parseFloat(gsap.getProperty(canvas, '--hole-size'));
                        ctx.globalCompositeOperation = 'destination-out';
                        ctx.beginPath();
                        ctx.arc(holeX, holeY, holeSize/2, 0, Math.PI * 2);
                        ctx.fill();
                        ctx.globalCompositeOperation = 'source-over';
                    }
                });

                nuggets.forEach(n => {
                    if (!n.found) {
                        const dist = Math.hypot(n.x - x, n.y - y);
                        if (dist < n.r + drillSize) {
                            n.found = true;
                            score++;
                            updateScore();
                        }
                    }
                });
            }
            
            canvas.addEventListener('click', handleDrill);
            init();
            animate();
            
            return () => {
                if (animationId) cancelAnimationFrame(animationId);
                canvas.removeEventListener('click', handleDrill);
            };
        }
    });
    </script>
</body>
</html> 