package com.example.noteapp.config;

import com.example.noteapp.model.Note;
import com.example.noteapp.repository.NoteRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.RedisConnectionFailureException;

import java.util.List;

@Configuration
public class DataInitializer {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Bean
    public CommandLineRunner initData(NoteRepository noteRepository) {
        return args -> {
            try {
                // 检查是否已有笔记数据
                List<Note> existingNotes = noteRepository.findAll();
                if (!existingNotes.isEmpty()) {
                    logger.info("数据库中已有 {} 条笔记数据，跳过初始化", existingNotes.size());
                    return;
                }

                // 创建示例笔记
                logger.info("初始化示例笔记数据...");
                
                Note note1 = new Note("欢迎使用笔记应用", "这是一个简单的笔记应用，使用 Spring Boot 构建。");
                Note note2 = new Note("存储方案", "本应用可以使用 Redis 或内存存储作为数据存储。");
                Note note3 = new Note("功能特点", "支持创建、查看、编辑和删除笔记，还支持暗黑模式切换。");
                
                noteRepository.save(note1);
                noteRepository.save(note2);
                noteRepository.save(note3);
                
                logger.info("示例笔记数据初始化完成");
            } catch (Exception e) {
                logger.error("初始化数据时出错: {}", e.getMessage(), e);
            }
        };
    }
} 