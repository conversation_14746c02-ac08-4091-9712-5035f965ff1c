<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTOS 实时操作系统 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .concept-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .concept-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .concept-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .concept-card:hover::before {
            left: 100%;
        }

        .concept-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .concept-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .concept-desc {
            color: #666;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .quiz-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .quiz-title {
            font-size: 2rem;
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .question {
            background: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .question-text {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            gap: 15px;
        }

        .option {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .option:hover {
            background: #e9ecef;
            transform: translateX(10px);
        }

        .option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .option.correct {
            border-color: #28a745;
            background: rgba(40, 167, 69, 0.1);
            animation: correctPulse 0.6s ease-out;
        }

        .option.wrong {
            border-color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
            display: none;
            animation: fadeIn 0.5s ease-out;
        }

        .explanation.show {
            display: block;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 1s ease-out;
            border-radius: 4px;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-10px); }
            75% { transform: translateX(10px); }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-elements" id="floatingElements"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">RTOS 实时操作系统</h1>
            <p class="subtitle">Real-Time Operating System 交互式学习体验</p>
        </div>

        <!-- 基础概念学习区 -->
        <div class="learning-section">
            <h2 class="section-title">🎯 什么是RTOS？</h2>
            <div class="canvas-container">
                <canvas id="rtosCanvas" width="800" height="400"></canvas>
            </div>
            <div class="concept-grid">
                <div class="concept-card" onclick="showConcept('realtime')">
                    <span class="concept-icon">⏰</span>
                    <h3 class="concept-title">实时性</h3>
                    <p class="concept-desc">必须在规定时间内响应和处理任务</p>
                </div>
                <div class="concept-card" onclick="showConcept('scheduling')">
                    <span class="concept-icon">📋</span>
                    <h3 class="concept-title">任务调度</h3>
                    <p class="concept-desc">合理分配系统资源给各个任务</p>
                </div>
                <div class="concept-card" onclick="showConcept('configurable')">
                    <span class="concept-icon">⚙️</span>
                    <h3 class="concept-title">可配置性</h3>
                    <p class="concept-desc">可以根据需求裁剪和重配内核</p>
                </div>
                <div class="concept-card" onclick="showConcept('interrupt')">
                    <span class="concept-icon">⚡</span>
                    <h3 class="concept-title">中断响应</h3>
                    <p class="concept-desc">及时响应外部事件和中断信号</p>
                </div>
            </div>
        </div>

        <!-- 题目解析区 -->
        <div class="quiz-section">
            <h2 class="quiz-title">📝 题目解析与练习</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="question">
                <p class="question-text">
                    以下关于 RTOS (实时操作系统）的叙述中，<strong>不正确</strong>的是（ ）。
                </p>
                <div class="options">
                    <div class="option" onclick="selectOption(this, 'A')">
                        <strong>A.</strong> RTOS 不能针对硬件变化进行结构与功能上的配置及裁剪
                    </div>
                    <div class="option" onclick="selectOption(this, 'B')">
                        <strong>B.</strong> RTOS 可以根据应用环境的要求对内核进行裁剪和重配
                    </div>
                    <div class="option" onclick="selectOption(this, 'C')">
                        <strong>C.</strong> RTOS 的首要任务是调度一切可利用的资源来完成实时控制任务
                    </div>
                    <div class="option" onclick="selectOption(this, 'D')">
                        <strong>D.</strong> RTOS 实质上就是一个计算机资源管理程序，需要及时响应实时事件和中断
                    </div>
                </div>
                
                <div class="explanation" id="explanation">
                    <h4>💡 详细解析：</h4>
                    <p><strong>正确答案：A</strong></p>
                    <p>选项A是<strong>错误</strong>的，因为：</p>
                    <ul style="margin: 15px 0; padding-left: 20px;">
                        <li>RTOS的一个重要特点就是<strong>可配置性和可裁剪性</strong></li>
                        <li>它可以根据不同的硬件平台和应用需求进行<strong>结构和功能的配置</strong></li>
                        <li>这种灵活性是RTOS相比通用操作系统的重要优势</li>
                    </ul>
                    <p>其他选项都是正确的RTOS特性描述。</p>
                </div>
                
                <button class="btn" onclick="checkAnswer()" id="checkBtn">检查答案</button>
                <button class="btn" onclick="showThinking()" id="thinkingBtn" style="display:none;">解题思路</button>
                <button class="btn" onclick="resetQuiz()" id="resetBtn" style="display:none;">重新开始</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedAnswer = null;
        let currentStep = 0;
        const totalSteps = 4;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingElements();
            initRTOSCanvas();
            updateProgress(0);
        });

        // 创建浮动装饰元素
        function createFloatingElements() {
            const container = document.getElementById('floatingElements');
            for (let i = 0; i < 8; i++) {
                const circle = document.createElement('div');
                circle.className = 'floating-circle';
                circle.style.width = Math.random() * 100 + 50 + 'px';
                circle.style.height = circle.style.width;
                circle.style.left = Math.random() * 100 + '%';
                circle.style.top = Math.random() * 100 + '%';
                circle.style.animationDelay = Math.random() * 6 + 's';
                circle.style.animationDuration = (Math.random() * 4 + 4) + 's';
                container.appendChild(circle);
            }
        }

        // 初始化RTOS概念动画
        function initRTOSCanvas() {
            const canvas = document.getElementById('rtosCanvas');
            const ctx = canvas.getContext('2d');
            
            let animationFrame = 0;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制RTOS核心
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                
                // 核心圆圈
                ctx.beginPath();
                ctx.arc(centerX, centerY, 60, 0, 2 * Math.PI);
                ctx.fillStyle = `hsl(${animationFrame % 360}, 70%, 60%)`;
                ctx.fill();
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 3;
                ctx.stroke();
                
                // 核心文字
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('RTOS', centerX, centerY - 5);
                ctx.fillText('核心', centerX, centerY + 15);
                
                // 绘制任务节点
                const tasks = [
                    { name: '任务A', angle: 0, color: '#ff6b6b' },
                    { name: '任务B', angle: Math.PI / 2, color: '#4ecdc4' },
                    { name: '任务C', angle: Math.PI, color: '#45b7d1' },
                    { name: '中断', angle: 3 * Math.PI / 2, color: '#f9ca24' }
                ];
                
                tasks.forEach((task, index) => {
                    const radius = 120;
                    const x = centerX + Math.cos(task.angle + animationFrame * 0.01) * radius;
                    const y = centerY + Math.sin(task.angle + animationFrame * 0.01) * radius;
                    
                    // 连接线
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(x, y);
                    ctx.strokeStyle = task.color;
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    
                    // 任务节点
                    ctx.beginPath();
                    ctx.arc(x, y, 25, 0, 2 * Math.PI);
                    ctx.fillStyle = task.color;
                    ctx.fill();
                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    ctx.stroke();
                    
                    // 任务文字
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(task.name, x, y + 4);
                });
                
                // 绘制时间轴
                const timelineY = canvas.height - 50;
                ctx.beginPath();
                ctx.moveTo(50, timelineY);
                ctx.lineTo(canvas.width - 50, timelineY);
                ctx.strokeStyle = '#666';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                // 时间刻度
                for (let i = 0; i < 8; i++) {
                    const x = 50 + (canvas.width - 100) * i / 7;
                    const height = (animationFrame + i * 20) % 100 < 50 ? 10 : 5;
                    
                    ctx.beginPath();
                    ctx.moveTo(x, timelineY);
                    ctx.lineTo(x, timelineY - height);
                    ctx.strokeStyle = '#666';
                    ctx.stroke();
                }
                
                // 实时性指示器
                ctx.fillStyle = '#e74c3c';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'left';
                ctx.fillText('实时性要求', 60, timelineY - 20);
                
                animationFrame++;
                requestAnimationFrame(animate);
            }
            
            animate();
        }

        // 显示概念详解
        function showConcept(concept) {
            const concepts = {
                realtime: {
                    title: '实时性 - RTOS的核心特征',
                    content: '实时系统必须在严格的时间限制内完成任务。这不是指速度快，而是指可预测性和确定性。'
                },
                scheduling: {
                    title: '任务调度 - 资源管理的艺术',
                    content: 'RTOS使用优先级调度算法，确保高优先级任务能够及时得到CPU资源。'
                },
                configurable: {
                    title: '可配置性 - 灵活适应需求',
                    content: 'RTOS可以根据具体应用裁剪不需要的功能，优化内存使用和性能。'
                },
                interrupt: {
                    title: '中断响应 - 快速反应机制',
                    content: '当外部事件发生时，RTOS能够立即暂停当前任务，处理紧急事件。'
                }
            };
            
            const info = concepts[concept];
            alert(`${info.title}\n\n${info.content}`);
            updateProgress(25);
        }

        // 选择答案
        function selectOption(element, answer) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected');
            });
            
            // 标记当前选择
            element.classList.add('selected');
            selectedAnswer = answer;
            updateProgress(50);
        }

        // 检查答案
        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请先选择一个答案！');
                return;
            }
            
            const options = document.querySelectorAll('.option');
            const correctAnswer = 'A';
            
            options.forEach(option => {
                const letter = option.textContent.trim().charAt(0);
                if (letter === correctAnswer) {
                    option.classList.add('correct');
                } else if (letter === selectedAnswer && selectedAnswer !== correctAnswer) {
                    option.classList.add('wrong');
                }
            });
            
            // 显示解析
            document.getElementById('explanation').classList.add('show');
            document.getElementById('checkBtn').style.display = 'none';
            document.getElementById('thinkingBtn').style.display = 'inline-block';
            document.getElementById('resetBtn').style.display = 'inline-block';
            
            updateProgress(75);
        }

        // 显示解题思路
        function showThinking() {
            const thinkingSteps = [
                "🤔 第一步：理解题目关键词",
                "题目问的是'不正确'的选项，要找错误的描述",
                "",
                "🔍 第二步：分析各选项",
                "A: 说RTOS不能配置和裁剪 - 这与RTOS的特点矛盾",
                "B: 说可以裁剪和重配 - 这是RTOS的优势",
                "C: 说首要任务是调度资源 - 符合实时系统特点",
                "D: 说是资源管理程序 - 正确描述了RTOS本质",
                "",
                "✅ 第三步：得出结论",
                "选项A明显与RTOS的可配置特性相矛盾，是错误描述"
            ];
            
            alert(thinkingSteps.join('\n'));
            updateProgress(100);
        }

        // 重置测验
        function resetQuiz() {
            selectedAnswer = null;
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('selected', 'correct', 'wrong');
            });
            document.getElementById('explanation').classList.remove('show');
            document.getElementById('checkBtn').style.display = 'inline-block';
            document.getElementById('thinkingBtn').style.display = 'none';
            document.getElementById('resetBtn').style.display = 'none';
            updateProgress(0);
        }

        // 更新进度条
        function updateProgress(percentage) {
            document.getElementById('progressFill').style.width = percentage + '%';
        }
    </script>
</body>
</html>
