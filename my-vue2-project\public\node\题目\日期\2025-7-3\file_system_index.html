<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件系统索引演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .explanation {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        #canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        canvas {
            border: 1px solid #ddd;
            background-color: white;
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .step-description {
            min-height: 60px;
            text-align: center;
            margin: 10px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .highlight {
            color: #e74c3c;
            font-weight: bold;
        }
        .quiz {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .quiz-options {
            margin-top: 10px;
        }
        .quiz-option {
            margin: 5px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        .quiz-option:hover {
            background-color: #f0f0f0;
        }
        .correct {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .incorrect {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .feedback {
            margin-top: 10px;
            font-weight: bold;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>文件系统索引演示</h1>
        
        <div class="explanation">
            <h2>文件系统索引是什么？</h2>
            <p>文件系统索引是操作系统用来管理和查找文件的一种机制。它就像是一本书的目录，帮助计算机快速定位文件在存储设备上的位置。</p>
            <p>在这个演示中，我们将学习一个特定的索引结构，它包含8个地址项：<span class="highlight">iaddr[0] ~ iaddr[7]</span>，每个地址项大小为4字节。</p>
        </div>

        <div id="canvas-container">
            <canvas id="indexCanvas" width="800" height="400"></canvas>
        </div>

        <div class="step-description" id="description">
            点击"开始演示"按钮，了解文件系统索引的工作原理。
        </div>

        <div class="controls">
            <button id="startBtn">开始演示</button>
            <button id="prevBtn" disabled>上一步</button>
            <button id="nextBtn" disabled>下一步</button>
            <button id="resetBtn">重置</button>
        </div>

        <div class="explanation">
            <h2>索引结构说明</h2>
            <ul>
                <li><strong>直接地址索引</strong>：iaddr[0] ~ iaddr[5]，直接指向数据块，范围为0-5号块</li>
                <li><strong>一级间接地址索引</strong>：iaddr[6]，指向一个索引块，该索引块包含多个指针，指向数据块，范围为6-1029号块</li>
                <li><strong>二级间接地址索引</strong>：iaddr[7]，指向一个索引块，该索引块的每个指针指向另一个索引块，每个二级索引块再指向数据块，范围为1030及以上号块</li>
            </ul>
            <p>每个索引块和磁盘数据块大小为4KB。</p>
        </div>

        <div class="quiz">
            <h2>小测验</h2>
            <p>问题：在这个文件系统中，一级间接地址索引可以索引的数据块范围是？</p>
            <div class="quiz-options">
                <div class="quiz-option" data-correct="false">A. 0-5号块</div>
                <div class="quiz-option" data-correct="true">B. 6-1029号块</div>
                <div class="quiz-option" data-correct="false">C. 1030及以上号块</div>
                <div class="quiz-option" data-correct="false">D. 0-1029号块</div>
            </div>
            <div class="feedback" id="feedback"></div>
        </div>
    </div>

    <script>
        // 获取Canvas和控件元素
        const canvas = document.getElementById('indexCanvas');
        const ctx = canvas.getContext('2d');
        const description = document.getElementById('description');
        const startBtn = document.getElementById('startBtn');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const resetBtn = document.getElementById('resetBtn');

        // 动画状态
        let currentStep = 0;
        const totalSteps = 6;
        let animationState = {
            highlightDirect: false,
            highlightIndirect1: false,
            highlightIndirect2: false,
            showDataBlocks: false,
            showIndirectBlocks: false,
            showSecondIndirectBlocks: false
        };

        // 颜色定义
        const colors = {
            indexNode: '#3498db',
            directPointer: '#2ecc71',
            indirectPointer1: '#e67e22',
            indirectPointer2: '#9b59b6',
            dataBlock: '#f1c40f',
            indexBlock: '#e74c3c',
            text: '#2c3e50',
            highlight: '#e74c3c'
        };

        // 初始化
        function init() {
            drawIndexStructure();
            setupQuiz();
        }

        // 绘制索引结构
        function drawIndexStructure() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制索引节点
            drawIndexNode();
            
            // 根据当前步骤绘制不同部分
            if (animationState.highlightDirect) {
                drawDirectPointers();
            }
            
            if (animationState.highlightIndirect1) {
                drawIndirectPointer1();
            }
            
            if (animationState.highlightIndirect2) {
                drawIndirectPointer2();
            }
            
            if (animationState.showDataBlocks) {
                drawDataBlocks();
            }
            
            if (animationState.showIndirectBlocks) {
                drawIndirectBlocks();
            }
            
            if (animationState.showSecondIndirectBlocks) {
                drawSecondIndirectBlocks();
            }
        }

        // 绘制索引节点
        function drawIndexNode() {
            const nodeX = 100;
            const nodeY = 100;
            const nodeWidth = 300;
            const nodeHeight = 200;
            
            // 绘制索引节点框
            ctx.fillStyle = colors.indexNode;
            ctx.fillRect(nodeX, nodeY, nodeWidth, nodeHeight);
            
            // 绘制索引节点标题
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('索引节点 (Inode)', nodeX + nodeWidth / 2, nodeY + 25);
            
            // 绘制索引项
            const itemHeight = 20;
            const startY = nodeY + 40;
            
            ctx.font = '14px Arial';
            ctx.textAlign = 'left';
            
            for (let i = 0; i < 8; i++) {
                const itemY = startY + i * itemHeight;
                let color = 'white';
                
                // 根据当前高亮状态设置颜色
                if (animationState.highlightDirect && i <= 5) {
                    color = colors.directPointer;
                } else if (animationState.highlightIndirect1 && i === 6) {
                    color = colors.indirectPointer1;
                } else if (animationState.highlightIndirect2 && i === 7) {
                    color = colors.indirectPointer2;
                }
                
                ctx.fillStyle = color;
                ctx.fillText(`iaddr[${i}]`, nodeX + 20, itemY);
                
                // 添加说明文本
                if (i <= 5) {
                    ctx.fillText('直接地址索引', nodeX + 100, itemY);
                } else if (i === 6) {
                    ctx.fillText('一级间接地址索引', nodeX + 100, itemY);
                } else if (i === 7) {
                    ctx.fillText('二级间接地址索引', nodeX + 100, itemY);
                }
            }
        }

        // 绘制直接指针
        function drawDirectPointers() {
            const startX = 400;
            const startY = 140;
            const blockSize = 30;
            const spacing = 10;
            
            ctx.fillStyle = colors.dataBlock;
            ctx.strokeStyle = colors.directPointer;
            ctx.lineWidth = 2;
            
            for (let i = 0; i < 6; i++) {
                const x = startX + 100;
                const y = startY + i * (blockSize + spacing);
                
                // 绘制数据块
                ctx.fillRect(x, y, blockSize, blockSize);
                
                // 绘制从索引节点到数据块的线
                ctx.beginPath();
                ctx.moveTo(400, startY + 5 + i * 20);
                ctx.lineTo(x, y + blockSize / 2);
                ctx.stroke();
                
                // 添加块号标签
                ctx.fillStyle = colors.text;
                ctx.textAlign = 'center';
                ctx.fillText(`块${i}`, x + blockSize / 2, y + blockSize / 2 + 5);
                ctx.fillStyle = colors.dataBlock;
            }
        }

        // 绘制一级间接指针
        function drawIndirectPointer1() {
            const indexBlockX = 500;
            const indexBlockY = 150;
            const blockSize = 80;
            
            // 绘制索引块
            ctx.fillStyle = colors.indexBlock;
            ctx.fillRect(indexBlockX, indexBlockY, blockSize, blockSize);
            
            // 绘制从索引节点到索引块的线
            ctx.strokeStyle = colors.indirectPointer1;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(400, 160);
            ctx.lineTo(indexBlockX, indexBlockY + blockSize / 2);
            ctx.stroke();
            
            // 添加索引块标签
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.fillText('一级索引块', indexBlockX + blockSize / 2, indexBlockY + blockSize / 2);
            
            if (animationState.showIndirectBlocks) {
                // 绘制从索引块指向的数据块
                const dataStartX = 650;
                const dataStartY = 120;
                const dataBlockSize = 25;
                const spacing = 8;
                const blocksPerRow = 4;
                
                ctx.fillStyle = colors.dataBlock;
                
                for (let i = 0; i < 12; i++) {
                    const row = Math.floor(i / blocksPerRow);
                    const col = i % blocksPerRow;
                    const x = dataStartX + col * (dataBlockSize + spacing);
                    const y = dataStartY + row * (dataBlockSize + spacing);
                    
                    // 绘制数据块
                    ctx.fillRect(x, y, dataBlockSize, dataBlockSize);
                    
                    // 只为前几个块绘制连接线，避免过于拥挤
                    if (i < 6) {
                        ctx.strokeStyle = colors.indirectPointer1;
                        ctx.beginPath();
                        ctx.moveTo(indexBlockX + blockSize, indexBlockY + 20 + i * 10);
                        ctx.lineTo(x, y + dataBlockSize / 2);
                        ctx.stroke();
                    }
                    
                    // 添加块号
                    const blockNum = i + 6; // 从6开始编号
                    if (i < 6) {
                        ctx.fillStyle = colors.text;
                        ctx.textAlign = 'center';
                        ctx.font = '10px Arial';
                        ctx.fillText(`${blockNum}`, x + dataBlockSize / 2, y + dataBlockSize / 2 + 3);
                        ctx.fillStyle = colors.dataBlock;
                    }
                }
                
                // 添加省略号表示更多块
                ctx.fillStyle = colors.text;
                ctx.textAlign = 'center';
                ctx.font = '16px Arial';
                ctx.fillText('...', dataStartX + 50, dataStartY + 120);
                ctx.fillText('(块6-1029)', dataStartX + 50, dataStartY + 145);
            }
        }

        // 绘制二级间接指针
        function drawIndirectPointer2() {
            const indexBlockX = 500;
            const indexBlockY = 270;
            const blockSize = 80;
            
            // 绘制索引块
            ctx.fillStyle = colors.indexBlock;
            ctx.fillRect(indexBlockX, indexBlockY, blockSize, blockSize);
            
            // 绘制从索引节点到索引块的线
            ctx.strokeStyle = colors.indirectPointer2;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(400, 180);
            ctx.lineTo(indexBlockX, indexBlockY + blockSize / 2);
            ctx.stroke();
            
            // 添加索引块标签
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.fillText('二级索引块', indexBlockX + blockSize / 2, indexBlockY + blockSize / 2);
            
            if (animationState.showSecondIndirectBlocks) {
                // 绘制二级索引块
                const secondIndexX = 650;
                const secondIndexY = 260;
                const secondBlockSize = 60;
                
                ctx.fillStyle = colors.indexBlock;
                ctx.fillRect(secondIndexX, secondIndexY, secondBlockSize, secondBlockSize);
                
                // 绘制从一级索引块到二级索引块的线
                ctx.strokeStyle = colors.indirectPointer2;
                ctx.beginPath();
                ctx.moveTo(indexBlockX + blockSize, indexBlockY + blockSize / 2);
                ctx.lineTo(secondIndexX, secondIndexY + secondBlockSize / 2);
                ctx.stroke();
                
                // 添加二级索引块标签
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.font = '12px Arial';
                ctx.fillText('二级索引块', secondIndexX + secondBlockSize / 2, secondIndexY + secondBlockSize / 2);
                
                // 绘制从二级索引块指向的数据块
                const dataStartX = 750;
                const dataStartY = 240;
                const dataBlockSize = 20;
                const spacing = 5;
                
                ctx.fillStyle = colors.dataBlock;
                
                for (let i = 0; i < 8; i++) {
                    const x = dataStartX;
                    const y = dataStartY + i * (dataBlockSize + spacing);
                    
                    // 绘制数据块
                    ctx.fillRect(x, y, dataBlockSize, dataBlockSize);
                    
                    // 绘制连接线
                    if (i < 4) {
                        ctx.strokeStyle = colors.indirectPointer2;
                        ctx.beginPath();
                        ctx.moveTo(secondIndexX + secondBlockSize, secondIndexY + 15 + i * 10);
                        ctx.lineTo(x, y + dataBlockSize / 2);
                        ctx.stroke();
                    }
                }
                
                // 添加块号范围
                ctx.fillStyle = colors.text;
                ctx.textAlign = 'center';
                ctx.font = '12px Arial';
                ctx.fillText('块1030+', dataStartX + 30, dataStartY + 100);
            }
        }

        // 绘制数据块
        function drawDataBlocks() {
            // 这个函数在其他函数中已经实现了数据块的绘制
        }

        // 绘制间接索引块
        function drawIndirectBlocks() {
            // 这个函数在drawIndirectPointer1中已经实现
        }

        // 绘制二级间接索引块
        function drawSecondIndirectBlocks() {
            // 这个函数在drawIndirectPointer2中已经实现
        }

        // 更新步骤描述
        function updateDescription() {
            const descriptions = [
                "文件系统索引结构包含8个地址项：iaddr[0] ~ iaddr[7]，每个地址项大小为4字节。",
                "直接地址索引：iaddr[0] ~ iaddr[5] 直接指向数据块，可以访问0-5号数据块。",
                "一级间接地址索引：iaddr[6] 指向一个索引块，该索引块包含多个指针。",
                "每个索引块大小为4KB，可以存储1024个指针(4KB/4B=1024)，指向1024个数据块。",
                "一级间接地址索引可以访问的数据块范围为6-1029号块。",
                "二级间接地址索引：iaddr[7] 指向一个索引块，该索引块的每个指针指向另一个索引块。",
                "二级间接地址索引可以访问1030及以上号块，最大可达1024×1024个数据块。"
            ];
            
            description.textContent = descriptions[currentStep];
        }

        // 更新动画状态
        function updateAnimationState() {
            switch(currentStep) {
                case 0:
                    animationState = {
                        highlightDirect: false,
                        highlightIndirect1: false,
                        highlightIndirect2: false,
                        showDataBlocks: false,
                        showIndirectBlocks: false,
                        showSecondIndirectBlocks: false
                    };
                    break;
                case 1:
                    animationState.highlightDirect = true;
                    animationState.showDataBlocks = true;
                    break;
                case 2:
                    animationState.highlightIndirect1 = true;
                    break;
                case 3:
                case 4:
                    animationState.showIndirectBlocks = true;
                    break;
                case 5:
                    animationState.highlightIndirect2 = true;
                    break;
                case 6:
                    animationState.showSecondIndirectBlocks = true;
                    break;
            }
            
            drawIndexStructure();
            updateDescription();
        }

        // 设置小测验交互
        function setupQuiz() {
            const quizOptions = document.querySelectorAll('.quiz-option');
            const feedback = document.getElementById('feedback');
            
            quizOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // 移除之前的样式
                    quizOptions.forEach(opt => {
                        opt.classList.remove('correct', 'incorrect');
                    });
                    
                    // 检查答案
                    const isCorrect = this.getAttribute('data-correct') === 'true';
                    
                    if (isCorrect) {
                        this.classList.add('correct');
                        feedback.textContent = '正确！一级间接地址索引可以索引6-1029号数据块。';
                        feedback.style.color = 'green';
                    } else {
                        this.classList.add('incorrect');
                        feedback.textContent = '不正确，请再试一次。';
                        feedback.style.color = 'red';
                    }
                    
                    feedback.style.display = 'block';
                });
            });
        }

        // 按钮事件处理
        startBtn.addEventListener('click', function() {
            currentStep = 0;
            animationState = {
                highlightDirect: false,
                highlightIndirect1: false,
                highlightIndirect2: false,
                showDataBlocks: false,
                showIndirectBlocks: false,
                showSecondIndirectBlocks: false
            };
            
            nextBtn.disabled = false;
            prevBtn.disabled = true;
            
            updateAnimationState();
        });

        nextBtn.addEventListener('click', function() {
            if (currentStep < totalSteps) {
                currentStep++;
                prevBtn.disabled = false;
                
                if (currentStep === totalSteps) {
                    nextBtn.disabled = true;
                }
                
                updateAnimationState();
            }
        });

        prevBtn.addEventListener('click', function() {
            if (currentStep > 0) {
                currentStep--;
                nextBtn.disabled = false;
                
                if (currentStep === 0) {
                    prevBtn.disabled = true;
                }
                
                updateAnimationState();
            }
        });

        resetBtn.addEventListener('click', function() {
            currentStep = 0;
            animationState = {
                highlightDirect: false,
                highlightIndirect1: false,
                highlightIndirect2: false,
                showDataBlocks: false,
                showIndirectBlocks: false,
                showSecondIndirectBlocks: false
            };
            
            nextBtn.disabled = false;
            prevBtn.disabled = true;
            
            // 重置测验
            const quizOptions = document.querySelectorAll('.quiz-option');
            const feedback = document.getElementById('feedback');
            
            quizOptions.forEach(opt => {
                opt.classList.remove('correct', 'incorrect');
            });
            
            feedback.style.display = 'none';
            
            updateAnimationState();
        });

        // 初始化页面
        window.onload = init;
    </script>
</body>
</html> 