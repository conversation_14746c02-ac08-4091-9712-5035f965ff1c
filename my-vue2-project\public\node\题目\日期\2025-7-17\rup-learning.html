<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RUP软件过程学习 - 交互式教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .rup-canvas {
            width: 100%;
            height: 400px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .rup-canvas:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .quiz-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 25px 0;
        }

        .quiz-option {
            padding: 15px 20px;
            background: rgba(255,255,255,0.2);
            border: 2px solid transparent;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .quiz-option:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .quiz-option.correct {
            border-color: #4CAF50;
            background: rgba(76,175,80,0.3);
        }

        .quiz-option.wrong {
            border-color: #f44336;
            background: rgba(244,67,54,0.3);
        }

        .explanation {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
            display: none;
        }

        .knowledge-points {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .knowledge-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
        }

        .knowledge-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .knowledge-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .knowledge-card p {
            color: #666;
            line-height: 1.6;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 1s ease;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 RUP软件过程学习</h1>
            <p>通过动画和交互，轻松掌握软件工程核心概念</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">📚 RUP四个阶段动画演示</h2>
            <canvas id="rupCanvas" class="rup-canvas"></canvas>
            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始演示</button>
                <button class="btn" onclick="resetAnimation()">🔄 重新开始</button>
                <button class="btn" onclick="showPhaseDetails()">📖 阶段详情</button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">🎯 知识要点</h2>
            <div class="knowledge-points">
                <div class="knowledge-card">
                    <h3>🌱 初始阶段 (Inception)</h3>
                    <p>建立业务模型，确定项目边界，定义最终业务模型。这是整个项目的起点，需要明确项目目标和范围。</p>
                </div>
                <div class="knowledge-card">
                    <h3>🏗️ 细化阶段 (Elaboration)</h3>
                    <p>分析问题领域，建立完善的架构，淘汰项目中最高风险的元素。重点是架构设计和风险控制。</p>
                </div>
                <div class="knowledge-card">
                    <h3>🔨 构建阶段 (Construction)</h3>
                    <p>开发所有剩余的构件和应用程序功能，把这些构件集成为产品。这是主要的编码实现阶段。</p>
                </div>
                <div class="knowledge-card">
                    <h3>🚀 移交阶段 (Transition)</h3>
                    <p>确保软件对最终用户是可用的，包括部署、培训、维护等工作。</p>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <h2 class="section-title" style="color: white;">🎮 互动测试</h2>
            <div class="quiz-question">
                <strong>题目：</strong>基于RUP的软件过程是一个迭代过程，一个开发周期包括初始、细化、构建和移交4个阶段。每次通过这4个阶段就会产生一代软件，其中定义最终业务模型是（）阶段的任务。采用迭代式开发，（）。
            </div>
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectOption(this, false)">
                    A. 每一个迭代都是一个完整的开发过程
                </div>
                <div class="quiz-option" onclick="selectOption(this, true)">
                    B. 每一轮迭代的重点是对特定的用例进行部分实现
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    C. 在后续迭代中强调用户的主动参与
                </div>
                <div class="quiz-option" onclick="selectOption(this, false)">
                    D. 通常以功能分解为基础
                </div>
            </div>
            <div class="explanation" id="explanation">
                <h3>📝 详细解析</h3>
                <p><strong>正确答案：A</strong></p>
                <p><strong>解释：</strong>RUP中的每一个迭代都是一个完整的开发过程，包含分析、设计、实现、测试等活动。每个迭代都会产生一个可执行的软件版本。</p>
                <p><strong>关键知识点：</strong></p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>初始阶段：定义最终业务模型</li>
                    <li>迭代不是重复做相同的事，而是针对不同用例的细化和实现</li>
                    <li>每个迭代都是完整的开发过程</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('rupCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;
        let currentPhase = 0;
        let animationProgress = 0;

        // 设置canvas尺寸
        function resizeCanvas() {
            const rect = canvas.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
        }

        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        const phases = [
            { name: '初始阶段', color: '#FF6B6B', icon: '🌱', description: '建立业务模型' },
            { name: '细化阶段', color: '#4ECDC4', icon: '🏗️', description: '架构设计' },
            { name: '构建阶段', color: '#45B7D1', icon: '🔨', description: '编码实现' },
            { name: '移交阶段', color: '#96CEB4', icon: '🚀', description: '部署交付' }
        ];

        function drawPhase(phase, index, progress) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const radius = 80;
            const angle = (index * Math.PI * 2) / 4 - Math.PI / 2;
            
            const x = centerX + Math.cos(angle) * 120;
            const y = centerY + Math.sin(angle) * 120;

            // 绘制连接线
            if (progress > index) {
                ctx.strokeStyle = phase.color;
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(x, y);
                ctx.stroke();
            }

            // 绘制阶段圆圈
            const phaseProgress = Math.max(0, Math.min(1, progress - index));
            const currentRadius = radius * phaseProgress;
            
            ctx.fillStyle = phase.color;
            ctx.beginPath();
            ctx.arc(x, y, currentRadius, 0, Math.PI * 2);
            ctx.fill();

            // 绘制阶段文字
            if (phaseProgress > 0.5) {
                ctx.fillStyle = 'white';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(phase.icon, x, y - 10);
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(phase.name, x, y + 10);
            }
        }

        function drawCenterCircle(progress) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 40, 0, Math.PI * 2);
            ctx.fill();

            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('RUP', centerX, centerY - 5);
            ctx.font = '10px Microsoft YaHei';
            ctx.fillText('迭代过程', centerX, centerY + 10);
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景网格
            ctx.strokeStyle = 'rgba(0,0,0,0.05)';
            ctx.lineWidth = 1;
            for (let i = 0; i < canvas.width; i += 50) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, canvas.height);
                ctx.stroke();
            }
            for (let i = 0; i < canvas.height; i += 50) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(canvas.width, i);
                ctx.stroke();
            }

            drawCenterCircle(animationProgress);
            
            phases.forEach((phase, index) => {
                drawPhase(phase, index, animationProgress);
            });

            // 更新进度条
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = (animationProgress / 4 * 100) + '%';

            animationProgress += 0.02;
            
            if (animationProgress < 4.5) {
                animationId = requestAnimationFrame(animate);
            }
        }

        function startAnimation() {
            cancelAnimationFrame(animationId);
            animationProgress = 0;
            animate();
        }

        function resetAnimation() {
            cancelAnimationFrame(animationId);
            animationProgress = 0;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            document.getElementById('progressFill').style.width = '0%';
        }

        function showPhaseDetails() {
            alert('RUP四个阶段详情：\n\n' +
                  '🌱 初始阶段：建立业务模型，确定项目边界\n' +
                  '🏗️ 细化阶段：架构设计，风险控制\n' +
                  '🔨 构建阶段：编码实现，功能开发\n' +
                  '🚀 移交阶段：部署交付，用户培训');
        }

        function selectOption(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.quiz-option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });

            // 标记选择
            if (isCorrect) {
                element.classList.add('correct');
            } else {
                element.classList.add('wrong');
                // 显示正确答案
                document.querySelectorAll('.quiz-option')[0].classList.add('correct');
            }

            // 显示解析
            document.getElementById('explanation').style.display = 'block';
        }

        // 初始化
        resetAnimation();
    </script>
</body>
</html>
