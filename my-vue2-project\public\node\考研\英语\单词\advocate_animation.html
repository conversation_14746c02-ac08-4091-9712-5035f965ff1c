<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Advocate Animation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        .container {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ccc;
            background-color: #fff;
        }
        .translation {
            margin-top: 10px;
            font-size: 1.2em;
        }
        .explanation {
            margin-top: 10px;
            text-align: left;
            max-width: 600px;
        }
        button {
            margin-top: 10px;
            padding: 10px 20px;
            font-size: 1em;
            cursor: pointer;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>Advocate</h1>
    <canvas id="wordCanvas" width="600" height="300"></canvas>
    <div class="translation">
        <p><strong>翻译:</strong> v. 提倡，主张，拥护 &nbsp;&nbsp; n. 提倡者，拥护者</p>
    </div>
    <div class="explanation">
        <p><strong>词源拆解:</strong></p>
        <p>单词 <strong>advocate</strong> 由两部分组成：</p>
        <ul>
            <li><strong>ad-</strong>: 一个前缀，意思是 "朝向" (to, toward) 或 "加强"。</li>
            <li><strong>vocate</strong>: 一个词根，源自拉丁语 'vocare'，意思是 "呼唤" (to call) 或 'vox' (声音, voice)。</li>
        </ul>
        <p><strong>故事记忆法:</strong></p>
        <p>想象一个场景：一位演说家（the advocate）站了出来，他走<strong>向</strong>（`ad-`）一个需要支持的理念或事业，并为之大声<strong>呼吁</strong>（`vocate`）。他的声音和言辞充满了力量，旨在说服并召集他人共同支持。这个"为一个目标发声"的行动，就是 "advocate" 的核心含义。</p>
        <p>这个动画将为你展示一个人物为远方的目标发声、表示支持的过程。</p>
    </div>
    <button id="replay">Replay Animation</button>
</div>

<script>
    const canvas = document.getElementById('wordCanvas');
    const ctx = canvas.getContext('2d');
    const replayBtn = document.getElementById('replay');

    let animationFrameId;

    const advocate = {
        x: 100,
        y: 200,
        draw() {
            // Body
            ctx.beginPath();
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x, this.y - 40);
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 3;
            ctx.stroke();

            // Head
            ctx.beginPath();
            ctx.arc(this.x, this.y - 50, 10, 0, Math.PI * 2);
            ctx.fillStyle = 'black';
            ctx.fill();

            // Arms (one raised)
            ctx.beginPath();
            ctx.moveTo(this.x, this.y - 35);
            ctx.lineTo(this.x - 15, this.y - 20);
            ctx.moveTo(this.x, this.y - 35);
            ctx.lineTo(this.x + 15, this.y - 50); // Raised arm
            ctx.stroke();

            // Legs
            ctx.beginPath();
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x - 10, this.y + 20);
            ctx.moveTo(this.x, this.y);
            ctx.lineTo(this.x + 10, this.y + 20);
            ctx.stroke();
        }
    };
    
    const goal = {
        x: 500,
        y: 180,
        draw() {
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = 'green';
            ctx.fillText('Cause', this.x, this.y);
            ctx.strokeStyle = 'green';
            ctx.lineWidth = 2;
            ctx.strokeRect(this.x - 10, this.y - 25, 80, 35);
        }
    }

    class SpeechWave {
        constructor(x, y) {
            this.x = x;
            this.y = y;
            this.targetX = goal.x - 20;
            this.radius = 2;
            this.speed = 2;
            this.alpha = 1;
        }

        update() {
            this.x += this.speed;
            if (this.x > this.targetX) {
                this.alpha -= 0.05;
            }
        }

        draw() {
            if (this.alpha <= 0) return;
            ctx.beginPath();
            ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(0, 0, 255, ${this.alpha})`;
            ctx.fill();
        }
    }

    let speechWaves = [];
    let adText = { alpha: 0 };
    let vocateText = { alpha: 0 };
    let frameCount = 0;

    function draw() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        advocate.draw();
        goal.draw();

        speechWaves.forEach(wave => wave.draw());

        // Draw "ad-"
        ctx.font = 'bold 40px Arial';
        ctx.fillStyle = `rgba(0, 0, 255, ${adText.alpha})`;
        ctx.fillText('ad-', advocate.x + 30, 100);
        
        // Draw "vocate"
        ctx.font = 'bold 40px Arial';
        ctx.fillStyle = `rgba(255, 0, 0, ${vocateText.alpha})`;
        ctx.fillText('vocate', advocate.x + 100, 100);
    }

    function animate() {
        frameCount++;
        
        // Generate new wave periodically
        if (frameCount % 30 === 0 && speechWaves.length < 15) {
            speechWaves.push(new SpeechWave(advocate.x + 20, advocate.y - 50));
            // Trigger text animation
            if(vocateText.alpha === 0) vocateText.alpha = 1;
        }

        speechWaves.forEach(wave => wave.update());
        speechWaves = speechWaves.filter(wave => wave.alpha > 0);

        if (speechWaves.some(w => w.x > advocate.x + 50)) {
             adText.alpha = Math.min(1, adText.alpha + 0.05);
        }
        
        draw();

        if (adText.alpha < 1 || speechWaves.length > 0) {
            animationFrameId = requestAnimationFrame(animate);
        }
    }
    
    function startAnimation() {
        cancelAnimationFrame(animationFrameId);
        speechWaves = [];
        adText.alpha = 0;
        vocateText.alpha = 0;
        frameCount = 0;
        animate();
    }

    replayBtn.addEventListener('click', startAnimation);

    startAnimation();

</script>
</body>
</html> 