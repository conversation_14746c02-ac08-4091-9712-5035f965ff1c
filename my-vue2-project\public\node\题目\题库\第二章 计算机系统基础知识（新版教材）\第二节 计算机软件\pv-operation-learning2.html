<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PV操作学习 - 进程同步</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .content-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .content-section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 1.8em;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
            transition: box-shadow 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        .process-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .process-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .process-card:hover {
            transform: scale(1.05);
        }

        .semaphore-display {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .semaphore {
            background: white;
            border: 3px solid #667eea;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            min-width: 100px;
            transition: all 0.3s ease;
        }

        .semaphore.active {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .answer-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            border-radius: 20px;
            padding: 25px;
            margin: 20px 0;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .option.correct {
            background: #4CAF50;
            color: white;
            border-color: #4CAF50;
        }

        .option.wrong {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 PV操作学习</h1>
            <p>进程同步与信号量机制</p>
        </div>

        <div class="content-section">
            <h2 class="section-title">📚 基础知识</h2>
            <div class="explanation">
                <h3>什么是PV操作？</h3>
                <p><strong>P操作（Wait）</strong>：等待信号量，如果信号量>0则减1并继续执行，否则阻塞等待</p>
                <p><strong>V操作（Signal）</strong>：释放信号量，将信号量加1，唤醒等待的进程</p>
                <div class="highlight">
                    <strong>记忆口诀：</strong>P操作是"等待许可"，V操作是"发放许可"
                </div>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎯 题目分析</h2>
            <div class="canvas-container">
                <canvas id="processCanvas" width="800" height="500"></canvas>
            </div>
            <div class="controls">
                <button class="btn" onclick="showPrecedenceGraph()">显示前趋图</button>
                <button class="btn" onclick="showPVOperations()">显示PV操作</button>
                <button class="btn" onclick="animateExecution()">动画演示</button>
                <button class="btn" onclick="resetAnimation()">重置</button>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">📊 信号量状态</h2>
            <div class="semaphore-display">
                <div class="semaphore" id="s1">
                    <h4>S1</h4>
                    <div id="s1-value">0</div>
                </div>
                <div class="semaphore" id="s2">
                    <h4>S2</h4>
                    <div id="s2-value">0</div>
                </div>
                <div class="semaphore" id="s3">
                    <h4>S3</h4>
                    <div id="s3-value">0</div>
                </div>
                <div class="semaphore" id="s4">
                    <h4>S4</h4>
                    <div id="s4-value">0</div>
                </div>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🤔 解题思路</h2>
            <div class="process-info">
                <div class="process-card">
                    <h4>步骤1</h4>
                    <p>分析前趋关系：P3需要等待P1和P2完成</p>
                </div>
                <div class="process-card">
                    <h4>步骤2</h4>
                    <p>P1、P2完成后用V操作通知P3</p>
                </div>
                <div class="process-card">
                    <h4>步骤3</h4>
                    <p>P3用P操作等待P1、P2的信号</p>
                </div>
                <div class="process-card">
                    <h4>步骤4</h4>
                    <p>P3完成后用V操作通知P4、P5</p>
                </div>
                <div class="process-card">
                    <h4>步骤5</h4>
                    <p>P4、P5用P操作等待P3的信号</p>
                </div>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">✅ 答案解析</h2>
            <div class="answer-section">
                <h3>正确答案分析：</h3>
                <p><strong>a处：V(S1)</strong> - P1完成后通知P3</p>
                <p><strong>b处：V(S2)</strong> - P2完成后通知P3</p>
                <p><strong>c处：P(S1)</strong> - P3等待P1的信号</p>
                <p><strong>d处：P(S2)</strong> - P3等待P2的信号</p>
                <p><strong>e处：P(S3)</strong> - P4等待P3的信号</p>
                <p><strong>f处：P(S4)</strong> - P5等待P3的信号</p>
                
                <div class="highlight">
                    <strong>关键理解：</strong>谁完成了用V操作"发信号"，谁需要等待用P操作"收信号"
                </div>
            </div>
        </div>

        <div class="content-section">
            <h2 class="section-title">🎮 互动测试</h2>
            <p>根据题目，e和f应该分别填写什么？</p>
            <div class="quiz-options">
                <div class="option" onclick="selectOption(this, false)">
                    <strong>A</strong><br>P(S3)和P(S4)
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>B</strong><br>P(S3)和V(S4)
                </div>
                <div class="option" onclick="selectOption(this, false)">
                    <strong>C</strong><br>V(S3)和V(S4)
                </div>
                <div class="option" onclick="selectOption(this, true)">
                    <strong>D</strong><br>V(S3)和P(S4)
                </div>
            </div>
            <div id="quiz-feedback" style="margin-top: 20px; text-align: center;"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('processCanvas');
        const ctx = canvas.getContext('2d');
        
        let animationStep = 0;
        let isAnimating = false;
        
        // 进程位置
        const processes = {
            P1: { x: 150, y: 100, completed: false },
            P2: { x: 150, y: 200, completed: false },
            P3: { x: 400, y: 150, completed: false },
            P4: { x: 650, y: 100, completed: false },
            P5: { x: 650, y: 200, completed: false }
        };
        
        // 信号量值
        const semaphores = { S1: 0, S2: 0, S3: 0, S4: 0 };
        
        function drawProcess(name, x, y, completed = false) {
            ctx.save();
            
            // 绘制进程圆圈
            ctx.beginPath();
            ctx.arc(x, y, 30, 0, 2 * Math.PI);
            ctx.fillStyle = completed ? '#4CAF50' : '#667eea';
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 绘制进程名称
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(name, x, y);
            
            ctx.restore();
        }
        
        function drawArrow(fromX, fromY, toX, toY, color = '#333') {
            ctx.save();
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            
            // 计算箭头方向
            const angle = Math.atan2(toY - fromY, toX - fromX);
            const arrowLength = 15;
            
            // 绘制箭头线
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();
            
            // 绘制箭头头部
            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle - Math.PI / 6), 
                      toY - arrowLength * Math.sin(angle - Math.PI / 6));
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - arrowLength * Math.cos(angle + Math.PI / 6), 
                      toY - arrowLength * Math.sin(angle + Math.PI / 6));
            ctx.stroke();
            
            ctx.restore();
        }
        
        function showPrecedenceGraph() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('进程前趋图', canvas.width / 2, 40);
            
            // 绘制进程
            Object.entries(processes).forEach(([name, pos]) => {
                drawProcess(name, pos.x, pos.y, pos.completed);
            });
            
            // 绘制前趋关系箭头
            drawArrow(180, 100, 370, 130); // P1 -> P3
            drawArrow(180, 200, 370, 170); // P2 -> P3
            drawArrow(430, 130, 620, 110); // P3 -> P4
            drawArrow(430, 170, 620, 190); // P3 -> P5
            
            // 添加说明文字
            ctx.font = '14px Arial';
            ctx.fillStyle = '#666';
            ctx.textAlign = 'left';
            ctx.fillText('P3需要等待P1和P2完成', 50, 350);
            ctx.fillText('P4和P5需要等待P3完成', 50, 370);
        }
        
        function showPVOperations() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.font = 'bold 24px Arial';
            ctx.fillStyle = '#333';
            ctx.textAlign = 'center';
            ctx.fillText('PV操作控制图', canvas.width / 2, 40);
            
            // 绘制进程
            Object.entries(processes).forEach(([name, pos]) => {
                drawProcess(name, pos.x, pos.y, pos.completed);
            });
            
            // 绘制PV操作标注
            ctx.font = '12px Arial';
            ctx.fillStyle = '#d32f2f';
            ctx.textAlign = 'center';
            
            // P1的V(S1)操作
            ctx.fillText('a: V(S1)', 150, 70);
            // P2的V(S2)操作
            ctx.fillText('b: V(S2)', 150, 240);
            // P3的P操作
            ctx.fillText('c: P(S1)', 350, 130);
            ctx.fillText('d: P(S2)', 350, 170);
            // P3的V操作
            ctx.fillText('V(S3)', 450, 130);
            ctx.fillText('V(S4)', 450, 170);
            // P4和P5的P操作
            ctx.fillText('e: P(S3)', 650, 70);
            ctx.fillText('f: P(S4)', 650, 240);
            
            // 绘制箭头和信号流
            drawArrow(180, 100, 370, 130, '#d32f2f'); // P1 -> P3 (S1)
            drawArrow(180, 200, 370, 170, '#d32f2f'); // P2 -> P3 (S2)
            drawArrow(430, 130, 620, 110, '#d32f2f'); // P3 -> P4 (S3)
            drawArrow(430, 170, 620, 190, '#d32f2f'); // P3 -> P5 (S4)
        }
        
        function animateExecution() {
            if (isAnimating) return;
            isAnimating = true;
            animationStep = 0;
            
            // 重置状态
            Object.keys(processes).forEach(key => {
                processes[key].completed = false;
            });
            Object.keys(semaphores).forEach(key => {
                semaphores[key] = 0;
            });
            updateSemaphoreDisplay();
            
            executeStep();
        }
        
        function executeStep() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            showPVOperations();
            
            switch(animationStep) {
                case 0:
                    highlightProcess('P1');
                    showMessage('P1开始执行...');
                    break;
                case 1:
                    processes.P1.completed = true;
                    semaphores.S1 = 1;
                    highlightOperation('V(S1)', 150, 70);
                    showMessage('P1完成，执行V(S1)，S1=1');
                    break;
                case 2:
                    highlightProcess('P2');
                    showMessage('P2开始执行...');
                    break;
                case 3:
                    processes.P2.completed = true;
                    semaphores.S2 = 1;
                    highlightOperation('V(S2)', 150, 240);
                    showMessage('P2完成，执行V(S2)，S2=1');
                    break;
                case 4:
                    highlightProcess('P3');
                    highlightOperation('P(S1)', 350, 130);
                    showMessage('P3执行P(S1)，S1减1变为0');
                    semaphores.S1 = 0;
                    break;
                case 5:
                    highlightOperation('P(S2)', 350, 170);
                    showMessage('P3执行P(S2)，S2减1变为0');
                    semaphores.S2 = 0;
                    break;
                case 6:
                    processes.P3.completed = true;
                    semaphores.S3 = 1;
                    semaphores.S4 = 1;
                    highlightOperation('V(S3)', 450, 130);
                    highlightOperation('V(S4)', 450, 170);
                    showMessage('P3完成，执行V(S3)和V(S4)');
                    break;
                case 7:
                    highlightProcess('P4');
                    highlightOperation('P(S3)', 650, 70);
                    showMessage('P4执行P(S3)，开始运行');
                    semaphores.S3 = 0;
                    break;
                case 8:
                    processes.P4.completed = true;
                    highlightProcess('P5');
                    highlightOperation('P(S4)', 650, 240);
                    showMessage('P5执行P(S4)，开始运行');
                    semaphores.S4 = 0;
                    break;
                case 9:
                    processes.P5.completed = true;
                    showMessage('所有进程执行完毕！');
                    isAnimating = false;
                    break;
            }
            
            updateSemaphoreDisplay();
            
            if (isAnimating) {
                setTimeout(() => {
                    animationStep++;
                    executeStep();
                }, 2000);
            }
        }
        
        function highlightProcess(name) {
            const pos = processes[name];
            ctx.save();
            ctx.beginPath();
            ctx.arc(pos.x, pos.y, 35, 0, 2 * Math.PI);
            ctx.strokeStyle = '#ff9800';
            ctx.lineWidth = 4;
            ctx.stroke();
            ctx.restore();
        }
        
        function highlightOperation(text, x, y) {
            ctx.save();
            ctx.fillStyle = '#ff9800';
            ctx.fillRect(x - 30, y - 15, 60, 20);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
            ctx.restore();
        }
        
        function showMessage(message) {
            ctx.save();
            ctx.fillStyle = 'rgba(0,0,0,0.8)';
            ctx.fillRect(50, 400, 700, 40);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(message, 60, 425);
            ctx.restore();
        }
        
        function updateSemaphoreDisplay() {
            Object.entries(semaphores).forEach(([name, value]) => {
                const element = document.getElementById(name.toLowerCase());
                const valueElement = document.getElementById(name.toLowerCase() + '-value');
                valueElement.textContent = value;
                
                if (value > 0) {
                    element.classList.add('active');
                } else {
                    element.classList.remove('active');
                }
            });
        }
        
        function resetAnimation() {
            isAnimating = false;
            animationStep = 0;
            Object.keys(processes).forEach(key => {
                processes[key].completed = false;
            });
            Object.keys(semaphores).forEach(key => {
                semaphores[key] = 0;
            });
            updateSemaphoreDisplay();
            showPrecedenceGraph();
        }
        
        function selectOption(element, isCorrect) {
            // 清除之前的选择
            document.querySelectorAll('.option').forEach(opt => {
                opt.classList.remove('correct', 'wrong');
            });
            
            // 标记选择
            if (isCorrect) {
                element.classList.add('correct');
                document.getElementById('quiz-feedback').innerHTML = 
                    '<div style="color: #4CAF50; font-weight: bold;">✅ 错误！正确答案应该是A：P(S3)和P(S4)</div>' +
                    '<div style="margin-top: 10px;">解释：P4和P5都需要等待P3完成，所以要用P操作等待信号量S3和S4</div>';
            } else {
                element.classList.add('wrong');
                document.getElementById('quiz-feedback').innerHTML = 
                    '<div style="color: #f44336; font-weight: bold;">❌ 错误！正确答案是A：P(S3)和P(S4)</div>' +
                    '<div style="margin-top: 10px;">记住：谁需要等待就用P操作，谁完成了就用V操作通知别人</div>';
            }
        }
        
        // 初始化显示
        showPrecedenceGraph();
        updateSemaphoreDisplay();
    </script>
</body>
</html>
