<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中断与轮询 - 交互式解释</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            min-height: 100vh;
            margin: 0;
            background-color: #f0f2f5;
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #1a73e8;
            font-size: 24px;
        }
        h2 {
            color: #333;
            font-size: 18px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .question-box {
            text-align: left;
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 16px;
        }
        .options .option {
            display: block;
            margin: 10px 0;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s, border-color 0.3s;
        }
        .options .option:hover {
            background-color: #e9f5ff;
            border-color: #1a73e8;
        }
        .options .option.correct {
            background-color: #e6ffed;
            border-color: #28a745;
            font-weight: bold;
        }
        .options .option.incorrect {
            background-color: #fff0f0;
            border-color: #dc3545;
        }
        .second-question-input {
            width: 80px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            text-align: center;
        }
        .second-question-result {
            font-weight: bold;
            margin-left: 10px;
        }
        canvas {
            background: #fafafa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            width: 100%;
        }
        .controls, .explanation {
            margin-top: 20px;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            margin: 0 10px;
        }
        button:hover {
            background-color: #1558b8;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .explanation {
            text-align: left;
            padding: 15px;
            background: #e9f5ff;
            border-left: 4px solid #1a73e8;
            min-height: 50px;
            border-radius: 4px;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>嵌入式系统I/O原理：中断 vs 轮询</h1>
    
    <div class="question-box">
        <h2>题目12</h2>
        <p>嵌入式系统中采用中断方式实现输入输出的主要原因是 (&nbsp;<span class="placeholder"></span>&nbsp;)。</p>
        <div class="options">
            <div class="option" data-answer="A">A. 速度最快</div>
            <div class="option" data-answer="B">B. CPU不参与操作</div>
            <div class="option" data-answer="C">C. 实现起来比较容易</div>
            <div class="option" data-answer="D">D. 能对突发事件做出快速响应</div>
        </div>
        <p style="margin-top: 20px;">
            在中断时，CPU断点信息一般保存到 (
            <input type="text" class="second-question-input" id="stack-input" placeholder="填空">
            ) 中。<span id="stack-answer-result" class="second-question-result"></span>
        </p>
    </div>

    <canvas id="canvas" width="800" height="400"></canvas>
    
    <div class="controls">
        <button id="interruptBtn">演示中断模式</button>
        <button id="pollingBtn">演示轮询模式</button>
        <button id="resetBtn">重置动画</button>
    </div>

    <div class="explanation">
        <p id="explanationText">点击按钮开始观看动画演示。让我为您揭示CPU是如何处理I/O请求的！</p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');
    const explanationTextEl = document.getElementById('explanationText');
    
    let animationFrameId;
    let mode = 'idle'; // idle, polling, interrupt
    let state = {};

    const colors = {
        cpu: '#2196F3',
        device: '#4CAF50',
        stack: '#FF9800',
        text: '#333333',
        signal: '#F44336',
        checking: '#FFC107'
    };

    function initState() {
        state = {
            cpu: { x: 50, y: 180, taskProgress: 0, status: '处理主程序' },
            devices: [
                { x: 650, y: 80, name: '键盘', ready: false, serviced: false },
                { x: 650, y: 180, name: '鼠标', ready: false, serviced: false },
                { x: 650, y: 280, name: '磁盘', ready: false, serviced: false }
            ],
            stack: { x: 250, y: 350, items: [] },
            polling: { checkIndex: 0, checkingTime: 0 },
            interrupt: { signalTime: 0, servicingTime: 0 },
            time: 0
        };
        explanationTextEl.textContent = '点击按钮开始观看动画演示。让我为您揭示CPU是如何处理I/O请求的！';
    }

    function drawText(text, x, y, color = colors.text, size = 16) {
        ctx.fillStyle = color;
        ctx.font = `${size}px sans-serif`;
        ctx.textAlign = 'center';
        ctx.fillText(text, x, y);
    }

    function drawCpu() {
        const { x, y, status } = state.cpu;
        ctx.fillStyle = colors.cpu;
        ctx.fillRect(x, y, 100, 80);
        drawText('CPU', x + 50, y + 30, 'white', 20);
        drawText(status, x + 50, y + 60, 'white', 14);
    }
    
    function drawDevices() {
        state.devices.forEach(dev => {
            ctx.fillStyle = dev.ready ? colors.signal : (dev.serviced ? '#BDBDBD' : colors.device);
            ctx.fillRect(dev.x, dev.y, 100, 50);
            drawText(dev.name, dev.x + 50, dev.y + 30, 'white');
        });
    }

    function drawStack() {
        const { x, y, items } = state.stack;
        ctx.strokeStyle = colors.stack;
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y - 100, 120, 100);
        drawText('堆栈 (Stack)', x + 60, y - 110);
        items.forEach((item, i) => {
            ctx.fillStyle = '#FFEB3B';
            ctx.fillRect(x + 10, y - 20 - i * 20, 100, 18);
            drawText(item, x + 60, y - 7 - i * 20, colors.text, 12);
        });
    }

    function drawArrow(fromX, fromY, toX, toY, color = 'black', text = '') {
        ctx.beginPath();
        ctx.moveTo(fromX, fromY);
        ctx.lineTo(toX, toY);
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.stroke();

        if (text) {
            ctx.save();
            ctx.translate((fromX + toX) / 2, (fromY + toY) / 2);
            ctx.fillStyle = color;
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(text, 0, -5);
            ctx.restore();
        }
    }

    function updatePolling() {
        const cpuPos = { x: state.cpu.x + 100, y: state.cpu.y + 40 };
        const dev = state.devices[state.polling.checkIndex];
        const devPos = { x: dev.x, y: dev.y + 25 };

        state.cpu.status = `检查 ${dev.name}...`;
        drawArrow(cpuPos.x, cpuPos.y, devPos.x, devPos.y, colors.checking);
        explanationTextEl.textContent = `[轮询模式] CPU花费宝贵的处理时间，依次检查每一个设备是否需要服务。这就像不停地问"你好了吗？"。`;

        state.polling.checkingTime++;
        if (state.polling.checkingTime > 30) { // Check every 0.5s
            state.polling.checkingTime = 0;
            
            // On the second device check, make it ready
            if(state.polling.checkIndex === 1 && !dev.ready && !dev.serviced) {
                dev.ready = true;
                explanationTextEl.textContent = `啊哈！[${dev.name}] 准备就绪了，CPU可以为它服务了。`;
            }
            
            if (dev.ready && !dev.serviced) {
                state.cpu.status = `服务 ${dev.name}`;
                setTimeout(() => {
                    dev.serviced = true;
                    dev.ready = false;
                    state.cpu.status = '处理主程序';
                    explanationTextEl.textContent = `[${dev.name}] 服务完成！但CPU在检查其他设备时浪费了时间。`;
                    state.polling.checkIndex = (state.polling.checkIndex + 1) % state.devices.length;
                }, 500);
            } else {
                 state.polling.checkIndex = (state.polling.checkIndex + 1) % state.devices.length;
            }
        }
    }

    function updateInterrupt() {
        state.cpu.taskProgress = (state.cpu.taskProgress + 0.5) % 100;
        state.cpu.status = `处理主程序...${Math.round(state.cpu.taskProgress)}%`;
        
        // Let main program run for a bit
        if (state.time < 120) {
            explanationTextEl.textContent = `[中断模式] CPU专注于执行主程序，效率很高。它不关心外部设备，直到...`;
            return;
        }

        const devToInterrupt = state.devices[1]; // Mouse device
        if (!devToInterrupt.ready && !devToInterrupt.serviced) {
            devToInterrupt.ready = true;
            explanationTextEl.textContent = `突然！一个突发事件！[${devToInterrupt.name}] 发出了一个中断信号！`;
        }

        if (devToInterrupt.ready) {
            const cpuPos = { x: state.cpu.x + 50, y: state.cpu.y };
            const devPos = { x: devToInterrupt.x, y: devToInterrupt.y + 25 };
            
            drawArrow(devPos.x, devPos.y, cpuPos.x + 50, cpuPos.y + 80, colors.signal, '中断请求！');
            
            state.interrupt.signalTime++;

            if (state.interrupt.signalTime > 60) { // After 1s
                if (state.stack.items.length === 0) {
                    state.cpu.status = '保存现场...';
                    explanationTextEl.innerHTML = `CPU必须暂停当前工作，并把它的"断点信息"（工作到哪了）保存到 <b>堆栈(Stack)</b> 中，以便稍后回来继续。`;
                    state.stack.items.push('CPU断点');
                } else {
                    state.cpu.status = `服务 ${devToInterrupt.name}`;
                    explanationTextEl.textContent = `现在，CPU可以安心地为设备服务了。这就是对突发事件的快速响应！`;

                    state.interrupt.servicingTime++;
                    if(state.interrupt.servicingTime > 120) { // After 2s
                        devToInterrupt.serviced = true;
                        devToInterrupt.ready = false;
                        state.cpu.status = '恢复现场...';
                        explanationTextEl.innerHTML = `服务完成！CPU从 <b>堆栈(Stack)</b> 中取出之前保存的信息，准备回到原来的任务。`;
                        state.stack.items.pop();
                        
                        setTimeout(() => {
                           mode = 'idle_after_interrupt';
                        }, 1500);
                    }
                }
            }
        }
    }
    
    function updateIdleAfterInterrupt() {
         state.cpu.status = `处理主程序...`;
         explanationTextEl.textContent = 'CPU无缝地回到了之前的工作，没有浪费任何检查时间。这就是中断的高效之处！';
    }


    function animate() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        state.time++;

        drawCpu();
        drawDevices();
        drawStack();

        if (mode === 'polling') {
            updatePolling();
        } else if (mode === 'interrupt') {
            updateInterrupt();
        } else if (mode === 'idle_after_interrupt') {
            updateIdleAfterInterrupt();
        }

        animationFrameId = requestAnimationFrame(animate);
    }
    
    function reset() {
        cancelAnimationFrame(animationFrameId);
        initState();
        mode = 'idle';
        document.querySelectorAll('button').forEach(b => b.disabled = false);
        animate();
    }
    
    document.getElementById('pollingBtn').addEventListener('click', () => {
        if (mode === 'polling') return;
        reset();
        mode = 'polling';
        document.getElementById('pollingBtn').disabled = true;
    });

    document.getElementById('interruptBtn').addEventListener('click', () => {
        if (mode === 'interrupt') return;
        reset();
        mode = 'interrupt';
        document.getElementById('interruptBtn').disabled = true;
    });

    document.getElementById('resetBtn').addEventListener('click', () => {
        reset();
    });

    // --- Question Logic ---
    document.querySelectorAll('.options .option').forEach(el => {
        el.addEventListener('click', (e) => {
            document.querySelectorAll('.options .option').forEach(o => o.classList.remove('correct', 'incorrect'));
            const answer = e.target.closest('.option').dataset.answer;
            if (answer === 'D') {
                e.target.closest('.option').classList.add('correct');
            } else {
                e.target.closest('.option').classList.add('incorrect');
            }
        });
    });

    document.getElementById('stack-input').addEventListener('input', (e) => {
        const resultEl = document.getElementById('stack-answer-result');
        const answer = e.target.value.trim();
        if (answer === '堆栈' || answer === '栈') {
            resultEl.textContent = '✅ 正确!';
            resultEl.style.color = '#28a745';
        } else if (answer === '') {
            resultEl.textContent = '';
        } else {
            resultEl.textContent = '❌ 再想想?';
            resultEl.style.color = '#dc3545';
        }
    });


    // Initial setup
    initState();
    animate();
});
</script>

</body>
</html>
