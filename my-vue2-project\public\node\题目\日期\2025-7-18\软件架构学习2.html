 <!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件系统架构 - 交互式学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .learning-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            animation: fadeInUp 1s ease-out;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: translateY(-5px);
        }

        .explanation {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .quiz-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin-top: 40px;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 1.1rem;
        }

        .option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-3px);
        }

        .option.selected {
            background: rgba(255,255,255,0.3);
            border-color: white;
        }

        .option.correct {
            background: rgba(76, 175, 80, 0.8);
            border-color: #4CAF50;
        }

        .option.wrong {
            background: rgba(244, 67, 54, 0.8);
            border-color: #f44336;
        }

        .btn {
            background: white;
            color: #667eea;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 4px;
            transition: width 0.5s ease;
            width: 0%;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 5px 10px;
            border-radius: 8px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">软件系统架构</h1>
            <p class="subtitle">通过动画和交互学习软件架构的核心概念</p>
        </div>

        <div class="learning-section">
            <h2 class="section-title">什么是软件系统架构？</h2>
            <div class="canvas-container">
                <canvas id="architectureCanvas" width="800" height="400"></canvas>
            </div>
            <div class="explanation">
                <p><strong>软件系统架构</strong>是关于软件系统的<span class="highlight">结构、行为和属性</span>的高级抽象。</p>
                <p>点击上方画布，观看架构组件的动画演示！</p>
            </div>
        </div>

        <div class="learning-section">
            <h2 class="section-title">架构的两个阶段</h2>
            <div class="canvas-container">
                <canvas id="phasesCanvas" width="800" height="300"></canvas>
            </div>
            <div class="explanation">
                <p><strong>描述阶段：</strong>描述抽象组件和连接规则，特别是组件的<span class="highlight">交互关系</span></p>
                <p><strong>实现阶段：</strong>抽象组件被细化为实际组件（类、对象等）</p>
            </div>
        </div>

        <div class="quiz-container">
            <h2 class="section-title" style="color: white;">知识测试</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="quiz-question" id="quizQuestion">
                软件系统架构不仅指定了软件系统的组织和（ ）结构，而且显示了系统需求和组件之间的对应关系...
            </div>
            <div class="options" id="quizOptions">
                <div class="option" data-answer="A">A. 进程</div>
                <div class="option" data-answer="B">B. 拓扑</div>
                <div class="option" data-answer="C">C. 处理</div>
                <div class="option" data-answer="D">D. 数据</div>
            </div>
            <button class="btn" id="submitBtn" onclick="checkAnswer()">提交答案</button>
            <div id="result" style="margin-top: 20px; font-size: 1.2rem;"></div>
        </div>
    </div>

    <script>
        // 架构演示画布
        const archCanvas = document.getElementById('architectureCanvas');
        const archCtx = archCanvas.getContext('2d');
        
        // 阶段演示画布
        const phasesCanvas = document.getElementById('phasesCanvas');
        const phasesCtx = phasesCanvas.getContext('2d');

        let animationFrame = 0;
        let isAnimating = false;

        // 绘制架构组件
        function drawArchitecture() {
            archCtx.clearRect(0, 0, archCanvas.width, archCanvas.height);
            
            // 背景
            const gradient = archCtx.createLinearGradient(0, 0, archCanvas.width, archCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            archCtx.fillStyle = gradient;
            archCtx.fillRect(0, 0, archCanvas.width, archCanvas.height);

            // 组件
            const components = [
                {x: 150, y: 100, label: '用户界面', color: '#667eea'},
                {x: 400, y: 100, label: '业务逻辑', color: '#764ba2'},
                {x: 650, y: 100, label: '数据层', color: '#f093fb'}
            ];

            components.forEach((comp, index) => {
                const pulse = Math.sin(animationFrame * 0.1 + index) * 0.1 + 1;
                
                // 组件圆形
                archCtx.beginPath();
                archCtx.arc(comp.x, comp.y, 50 * pulse, 0, Math.PI * 2);
                archCtx.fillStyle = comp.color;
                archCtx.fill();
                archCtx.strokeStyle = 'white';
                archCtx.lineWidth = 3;
                archCtx.stroke();

                // 标签
                archCtx.fillStyle = 'white';
                archCtx.font = 'bold 14px Microsoft YaHei';
                archCtx.textAlign = 'center';
                archCtx.fillText(comp.label, comp.x, comp.y + 5);
            });

            // 连接线
            archCtx.strokeStyle = '#6c757d';
            archCtx.lineWidth = 3;
            archCtx.setLineDash([5, 5]);
            archCtx.beginPath();
            archCtx.moveTo(200, 100);
            archCtx.lineTo(350, 100);
            archCtx.moveTo(450, 100);
            archCtx.lineTo(600, 100);
            archCtx.stroke();
            archCtx.setLineDash([]);

            // 架构标题
            archCtx.fillStyle = '#333';
            archCtx.font = 'bold 24px Microsoft YaHei';
            archCtx.textAlign = 'center';
            archCtx.fillText('软件系统架构', archCanvas.width / 2, 50);

            // 属性标签
            const attributes = ['结构', '行为', '属性'];
            attributes.forEach((attr, index) => {
                archCtx.fillStyle = '#495057';
                archCtx.font = '16px Microsoft YaHei';
                archCtx.fillText(attr, 150 + index * 250, 200);
            });
        }

        // 绘制阶段演示
        function drawPhases() {
            phasesCtx.clearRect(0, 0, phasesCanvas.width, phasesCanvas.height);
            
            // 背景
            const gradient = phasesCtx.createLinearGradient(0, 0, phasesCanvas.width, phasesCanvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            phasesCtx.fillStyle = gradient;
            phasesCtx.fillRect(0, 0, phasesCanvas.width, phasesCanvas.height);

            // 描述阶段
            phasesCtx.fillStyle = '#667eea';
            phasesCtx.fillRect(50, 50, 300, 100);
            phasesCtx.fillStyle = 'white';
            phasesCtx.font = 'bold 18px Microsoft YaHei';
            phasesCtx.textAlign = 'center';
            phasesCtx.fillText('描述阶段', 200, 90);
            phasesCtx.font = '14px Microsoft YaHei';
            phasesCtx.fillText('抽象组件 + 连接规则', 200, 115);
            phasesCtx.fillText('交互关系', 200, 135);

            // 箭头
            phasesCtx.strokeStyle = '#6c757d';
            phasesCtx.lineWidth = 4;
            phasesCtx.beginPath();
            phasesCtx.moveTo(370, 100);
            phasesCtx.lineTo(430, 100);
            phasesCtx.stroke();
            
            // 箭头头部
            phasesCtx.beginPath();
            phasesCtx.moveTo(420, 90);
            phasesCtx.lineTo(430, 100);
            phasesCtx.lineTo(420, 110);
            phasesCtx.stroke();

            // 实现阶段
            phasesCtx.fillStyle = '#764ba2';
            phasesCtx.fillRect(450, 50, 300, 100);
            phasesCtx.fillStyle = 'white';
            phasesCtx.font = 'bold 18px Microsoft YaHei';
            phasesCtx.fillText('实现阶段', 600, 90);
            phasesCtx.font = '14px Microsoft YaHei';
            phasesCtx.fillText('具体类和对象', 600, 115);
            phasesCtx.fillText('实际组件', 600, 135);

            // 拓扑结构示意
            phasesCtx.fillStyle = '#f093fb';
            phasesCtx.font = 'bold 20px Microsoft YaHei';
            phasesCtx.textAlign = 'center';
            phasesCtx.fillText('拓扑结构', phasesCanvas.width / 2, 220);
            
            // 网络节点示意
            const nodes = [
                {x: 300, y: 250}, {x: 400, y: 230}, {x: 500, y: 250},
                {x: 350, y: 280}, {x: 450, y: 280}
            ];
            
            nodes.forEach(node => {
                phasesCtx.beginPath();
                phasesCtx.arc(node.x, node.y, 8, 0, Math.PI * 2);
                phasesCtx.fillStyle = '#f093fb';
                phasesCtx.fill();
            });
            
            // 连接线
            phasesCtx.strokeStyle = '#f093fb';
            phasesCtx.lineWidth = 2;
            phasesCtx.beginPath();
            phasesCtx.moveTo(300, 250);
            phasesCtx.lineTo(400, 230);
            phasesCtx.lineTo(500, 250);
            phasesCtx.lineTo(450, 280);
            phasesCtx.lineTo(350, 280);
            phasesCtx.lineTo(300, 250);
            phasesCtx.stroke();
        }

        // 动画循环
        function animate() {
            animationFrame++;
            if (isAnimating) {
                drawArchitecture();
                drawPhases();
            }
            requestAnimationFrame(animate);
        }

        // 点击事件
        archCanvas.addEventListener('click', () => {
            isAnimating = !isAnimating;
            if (isAnimating) {
                archCanvas.style.animation = 'pulse 2s infinite';
            } else {
                archCanvas.style.animation = '';
            }
        });

        phasesCanvas.addEventListener('click', () => {
            isAnimating = !isAnimating;
            if (isAnimating) {
                phasesCanvas.style.animation = 'pulse 2s infinite';
            } else {
                phasesCanvas.style.animation = '';
            }
        });

        // 测验功能
        let selectedAnswer = null;

        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
                selectedAnswer = option.dataset.answer;
            });
        });

        function checkAnswer() {
            if (!selectedAnswer) {
                alert('请选择一个答案！');
                return;
            }

            const options = document.querySelectorAll('.option');
            const result = document.getElementById('result');
            const progressFill = document.getElementById('progressFill');

            options.forEach(option => {
                if (option.dataset.answer === 'B') {
                    option.classList.add('correct');
                } else if (option.dataset.answer === selectedAnswer && selectedAnswer !== 'B') {
                    option.classList.add('wrong');
                }
            });

            if (selectedAnswer === 'B') {
                result.innerHTML = '🎉 正确！拓扑结构是指组件之间的连接和布局关系。';
                result.style.color = '#4CAF50';
                progressFill.style.width = '100%';
            } else {
                result.innerHTML = '❌ 不正确。正确答案是B：拓扑。拓扑结构描述了系统组件的组织方式和连接关系。';
                result.style.color = '#f44336';
                progressFill.style.width = '50%';
            }

            document.getElementById('submitBtn').style.display = 'none';
        }

        // 初始化
        drawArchitecture();
        drawPhases();
        animate();
    </script>
</body>
</html>
