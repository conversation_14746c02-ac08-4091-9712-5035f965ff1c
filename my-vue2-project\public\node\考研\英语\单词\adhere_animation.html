<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单词动画 - Adhere</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f8ff;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #005a9c;
            font-size: 3em;
            text-shadow: 2px 2px 4px #ccc;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: flex-start;
            gap: 20px;
            width: 100%;
            max-width: 1200px;
        }
        canvas {
            border: 2px solid #005a9c;
            border-radius: 10px;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .explanation {
            background-color: #fff;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
        }
        h2 {
            color: #005a9c;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        p, li {
            font-size: 1.1em;
        }
        strong {
            color: #d9534f;
        }
        code {
            background-color: #eef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: Consolas, 'Courier New', monospace;
        }
        .button-container {
            margin-top: 20px;
            text-align: center;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            font-size: 1.2em;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        .footer {
            margin-top: 30px;
            font-size: 0.9em;
            color: #777;
        }
    </style>
</head>
<body>

    <h1>adhere /ədˈhɪər/</h1>

    <div class="container">
        <canvas id="wordAnimation" width="600" height="400"></canvas>
        <div class="explanation">
            <h2>📖 词源故事：贴海报</h2>
            <p>单词 <code>adhere</code> 可以拆分为两个部分来理解：</p>
            <ul>
                <li><code>ad-</code>: 一个常见的前缀，表示"朝向、去" (to, toward)。</li>
                <li><code>here</code>: 这部分源自拉丁语 <code>haerere</code>，意思是"粘贴、附着" (to stick)。</li>
            </ul>
            <p>所以，<code>adhere</code> 的字面意思就是"<strong>朝一个方向粘过去</strong>"。我们的动画就生动地演示了这个过程：一个人想把一张代表 <code>here</code> (粘贴) 的海报，贴 <strong>到</strong> (<code>ad-</code>) 墙上去。最终，海报牢牢地 <strong>adhere</strong> (粘附) 在了墙上。</p>

            <h2>🧠 如何理解与翻译？</h2>
            <p><strong>1. 核心含义 - 物理上的"粘附"</strong></p>
            <p>当你看到 <code>adhere</code>，首先可以想到胶水、贴纸等东西"粘"在另一个物体表面。这是它最直接的意思。</p>
            <p><strong>例句：</strong>The stamp failed to <strong>adhere</strong> to the envelope. (邮票没能粘在信封上。)</p>
            
            <p><strong>2. 引申含义 - 思想上的"坚持、遵守"</strong></p>
            <p>这个词更重要的用法是它的引申义。想象一下，如果一个规定或信念像胶水一样"粘"在你的行为或思想上，那你就是在"坚持"或"遵守"它。</p>
            <p><strong>例句：</strong>All contestants must <strong>adhere</strong> to the rules. (所有参赛者都必须遵守规则。)</p>
            <p><strong>翻译技巧：</strong>看到这个词，先想"粘住"，再根据语境判断是具体的"粘附"，还是抽象的"坚持"或"遵守"。</p>
        </div>
    </div>

    <div class="button-container">
        <button id="replayButton">🔄 重新播放动画</button>
    </div>

    <p class="footer">一个单词，一个故事。让学习变得有趣！</p>

    <script>
        const canvas = document.getElementById('wordAnimation');
        const ctx = canvas.getContext('2d');
        const button = document.getElementById('replayButton');

        let phase = 'initial'; // initial, falling, gluing, stuck
        let posterY = 50;
        let posterFallSpeed = 2;
        let glueOpacity = 0;
        let animationFrameId;

        function drawWall() {
            ctx.fillStyle = '#d2b48c'; // Tan color for the wall
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#a0522d'; // Sienna for texture
            for (let i = 0; i < 20; i++) {
                ctx.fillRect(Math.random() * canvas.width, Math.random() * canvas.height, 2, 2);
            }
        }

        function drawPoster(y) {
            ctx.fillStyle = '#ffffff';
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetX = 5;
            ctx.shadowOffsetY = 5;
            ctx.beginPath();
            ctx.rect(150, y, 300, 200);
            ctx.fill();
            ctx.stroke();
            ctx.shadowColor = 'transparent';

            ctx.fillStyle = '#005a9c';
            ctx.font = 'bold 50px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('here', 300, y + 120);
            
            ctx.fillStyle = '#666';
            ctx.font = '20px Arial';
            ctx.fillText('(to stick)', 300, y + 160);
        }

        function drawArrow() {
            ctx.fillStyle = '#d9534f';
            ctx.font = 'bold 70px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('ad-', 300, 100);
            
            ctx.fillStyle = '#555';
            ctx.font = '25px Arial';
            ctx.fillText('(to, toward)', 300, 140);
        }
        
        function drawGlue(y) {
            ctx.globalAlpha = glueOpacity;
            ctx.fillStyle = 'rgba(173, 216, 230, 0.7)'; // Light blue for glue
            ctx.beginPath();
            ctx.rect(150, y, 300, 200);
            ctx.fill();
            ctx.globalAlpha = 1.0;
        }

        function drawFinalText() {
            ctx.fillStyle = 'green';
            ctx.font = 'bold 45px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('adhere = 粘附 / 坚持', 300, canvas.height - 50);
        }

        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawWall();

            if (phase === 'initial') {
                drawArrow();
                drawPoster(posterY);
                posterY += 0.5;
                if (posterY > 70) {
                    phase = 'falling';
                }
            } else if (phase === 'falling') {
                drawArrow();
                drawPoster(posterY);
                posterY += posterFallSpeed;
                posterFallSpeed += 0.1;
                if (posterY > canvas.height) {
                    phase = 'gluing';
                    setTimeout(() => {
                         posterY = 150; // Reset poster position for gluing
                    }, 500);
                }
            } else if (phase === 'gluing') {
                drawArrow();
                drawPoster(posterY);
                if (glueOpacity < 1) {
                    glueOpacity += 0.05;
                }
                drawGlue(posterY);
                if (glueOpacity >= 1) {
                    phase = 'stuck';
                }
            } else if (phase === 'stuck') {
                drawArrow();
                drawPoster(posterY);
                drawGlue(posterY);
                drawFinalText();
            }

            animationFrameId = requestAnimationFrame(animate);
        }

        function startAnimation() {
            phase = 'initial';
            posterY = 50;
            posterFallSpeed = 2;
            glueOpacity = 0;
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            animate();
        }

        button.addEventListener('click', startAnimation);

        // Start animation on page load
        startAnimation();

    </script>

</body>
</html> 