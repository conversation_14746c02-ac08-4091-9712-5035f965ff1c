<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集合之间的关系 - 零基础互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: fadeInUp 0.8s ease-out;
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
            transform: scale(1.02);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: center;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .concept-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .concept-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #667eea;
        }

        .concept-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .concept-title {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .concept-desc {
            color: #666;
            line-height: 1.6;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .nav-item {
            display: block;
            color: #333;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            margin: 5px 0;
            font-size: 0.9rem;
        }

        .nav-item:hover {
            background: #667eea;
            color: white;
        }

        .control-panel {
            background: rgba(255,255,255,0.9);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            display: none;
        }

        .control-panel.active {
            display: block;
            animation: slideIn 0.5s ease-out;
        }

        .slider-container {
            margin: 15px 0;
        }

        .slider {
            width: 200px;
            margin: 0 10px;
        }

        .formula-display {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="#subset" class="nav-item">包含与子集</a>
        <a href="#parameters" class="nav-item">求参数值</a>
        <a href="#inequalities" class="nav-item">不等式关系</a>
        <a href="#equal-sets" class="nav-item">集合相等</a>
        <a href="#quadratic" class="nav-item">二次方程</a>
        <a href="#subset-count" class="nav-item">子集个数</a>
        <a href="#root-distribution" class="nav-item">根的分布</a>
        <a href="#practice" class="nav-item">综合练习</a>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">集合之间的关系</h1>
            <p class="subtitle">零基础互动教学 - 从包含到相等的完整学习</p>
        </div>

        <!-- 1. 包含与子集 -->
        <div class="section" id="subset">
            <h2 class="section-title">1. 包含与子集</h2>
            <div class="canvas-container">
                <canvas id="subsetCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                子集是集合论中最基本的关系。如果集合A的每一个元素都是集合B的元素，那么A是B的子集，记作A⊆B。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="demonstrateSubset()">演示子集概念</button>
                <button class="interactive-btn" onclick="showSubsetTypes()">子集类型</button>
                <button class="interactive-btn" onclick="interactiveSubset()">🎮 互动演示</button>
                <button class="interactive-btn" onclick="resetCanvas('subset')">重置</button>
            </div>
            
            <div class="control-panel" id="subsetPanel">
                <h3>🎯 互动子集演示</h3>
                <p>拖拽元素来体验子集关系</p>
                <div>
                    <button class="interactive-btn" onclick="addElement()">添加元素</button>
                    <button class="interactive-btn" onclick="removeElement()">移除元素</button>
                    <button class="interactive-btn" onclick="checkSubsetRelation()">检查关系</button>
                </div>
            </div>

            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">子集定义</div>
                    <div class="concept-desc">
                        如果A中的每个元素都属于B，则A⊆B<br>
                        <span class="highlight">∀x ∈ A ⇒ x ∈ B</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">真子集</div>
                    <div class="concept-desc">
                        A⊆B 且 A≠B，则A是B的真子集<br>
                        记作：<span class="highlight">A⊊B</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">空集性质</div>
                    <div class="concept-desc">
                        空集是任何集合的子集<br>
                        <span class="highlight">∅⊆A</span> 对任意集合A成立
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. 已知包含关系求参数值 -->
        <div class="section" id="parameters">
            <h2 class="section-title">2. 已知包含关系求参数值</h2>
            <div class="canvas-container">
                <canvas id="parametersCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                当集合中含有参数时，根据包含关系可以确定参数的取值范围。这是集合关系的重要应用。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="solveParameterProblem()">求解参数</button>
                <button class="interactive-btn" onclick="visualizeParameter()">可视化过程</button>
                <button class="interactive-btn" onclick="parameterPractice()">🎮 参数练习</button>
                <button class="interactive-btn" onclick="resetCanvas('parameters')">重置</button>
            </div>

            <div class="control-panel" id="parameterPanel">
                <h3>🎯 参数求解器</h3>
                <div class="slider-container">
                    <label>参数 a 的值：</label>
                    <input type="range" class="slider" id="paramA" min="-5" max="5" step="0.1" value="1" oninput="updateParameter()">
                    <span id="paramAValue">1</span>
                </div>
                <div class="formula-display" id="parameterFormula">
                    A = {x | x² + ax + 1 = 0}, B = {1, 2}
                </div>
                <button class="interactive-btn" onclick="checkParameterCondition()">检查条件</button>
            </div>
        </div>

        <!-- 3. 一次不等式解集间的关系 -->
        <div class="section" id="inequalities">
            <h2 class="section-title">3. 一次不等式解集间的关系</h2>
            <div class="canvas-container">
                <canvas id="inequalitiesCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                一次不等式的解集在数轴上表现为区间，不同区间之间的包含关系可以直观地在数轴上观察。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="showInequalityRelations()">区间关系</button>
                <button class="interactive-btn" onclick="animateIntervals()">动画演示</button>
                <button class="interactive-btn" onclick="interactiveIntervals()">🎮 区间操作</button>
                <button class="interactive-btn" onclick="resetCanvas('inequalities')">重置</button>
            </div>

            <div class="control-panel" id="intervalPanel">
                <h3>🎯 区间关系探索</h3>
                <div class="slider-container">
                    <label>区间A左端点：</label>
                    <input type="range" class="slider" id="intervalA1" min="-10" max="10" step="1" value="-2" oninput="updateIntervals()">
                    <span id="intervalA1Value">-2</span>
                </div>
                <div class="slider-container">
                    <label>区间A右端点：</label>
                    <input type="range" class="slider" id="intervalA2" min="-10" max="10" step="1" value="3" oninput="updateIntervals()">
                    <span id="intervalA2Value">3</span>
                </div>
                <div class="slider-container">
                    <label>区间B左端点：</label>
                    <input type="range" class="slider" id="intervalB1" min="-10" max="10" step="1" value="-1" oninput="updateIntervals()">
                    <span id="intervalB1Value">-1</span>
                </div>
                <div class="slider-container">
                    <label>区间B右端点：</label>
                    <input type="range" class="slider" id="intervalB2" min="-10" max="10" step="1" value="2" oninput="updateIntervals()">
                    <span id="intervalB2Value">2</span>
                </div>
                <div id="relationResult" class="formula-display">调整滑块查看区间关系</div>
            </div>
        </div>

        <!-- 4. 集合相等 -->
        <div class="section" id="equal-sets">
            <h2 class="section-title">4. 集合相等</h2>
            <div class="canvas-container">
                <canvas id="equalSetsCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                两个集合相等当且仅当它们包含完全相同的元素。即A=B ⟺ A⊆B且B⊆A。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="demonstrateEquality()">演示相等</button>
                <button class="interactive-btn" onclick="showEqualityConditions()">相等条件</button>
                <button class="interactive-btn" onclick="equalityPuzzle()">🎮 相等谜题</button>
                <button class="interactive-btn" onclick="resetCanvas('equal-sets')">重置</button>
            </div>

            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">相等定义</div>
                    <div class="concept-desc">
                        A = B ⟺ A⊆B 且 B⊆A<br>
                        即：<span class="highlight">两个集合互为子集</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">元素角度</div>
                    <div class="concept-desc">
                        A = B ⟺ ∀x(x∈A ⟺ x∈B)<br>
                        即：<span class="highlight">元素完全相同</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">判断方法</div>
                    <div class="concept-desc">
                        1. 证明A⊆B<br>
                        2. 证明B⊆A<br>
                        3. 结论：<span class="highlight">A = B</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 5. 二次方程解集相等的条件 -->
        <div class="section" id="quadratic">
            <h2 class="section-title">5. 二次方程解集相等的条件</h2>
            <div class="canvas-container">
                <canvas id="quadraticCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                二次方程的解集取决于判别式，通过分析判别式可以确定解集的包含关系和相等条件。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="analyzeQuadraticSets()">分析解集</button>
                <button class="interactive-btn" onclick="showDiscriminantEffect()">判别式影响</button>
                <button class="interactive-btn" onclick="quadraticComparison()">🎮 方程比较</button>
                <button class="interactive-btn" onclick="resetCanvas('quadratic')">重置</button>
            </div>

            <div class="control-panel" id="quadraticPanel">
                <h3>🎯 二次方程解集分析</h3>
                <div class="slider-container">
                    <label>方程1系数 a₁：</label>
                    <input type="range" class="slider" id="quadA1" min="1" max="5" step="1" value="1" oninput="updateQuadratic()">
                    <span id="quadA1Value">1</span>
                </div>
                <div class="slider-container">
                    <label>方程1系数 b₁：</label>
                    <input type="range" class="slider" id="quadB1" min="-10" max="10" step="1" value="-3" oninput="updateQuadratic()">
                    <span id="quadB1Value">-3</span>
                </div>
                <div class="slider-container">
                    <label>方程1系数 c₁：</label>
                    <input type="range" class="slider" id="quadC1" min="-10" max="10" step="1" value="2" oninput="updateQuadratic()">
                    <span id="quadC1Value">2</span>
                </div>
                <div class="formula-display" id="quadraticFormula1">
                    x² - 3x + 2 = 0
                </div>
                <div class="formula-display" id="quadraticResult1">
                    解集：{1, 2}
                </div>
            </div>
        </div>

        <!-- 6. 子集的个数公式 -->
        <div class="section" id="subset-count">
            <h2 class="section-title">6. 子集的个数公式</h2>
            <div class="canvas-container">
                <canvas id="subsetCountCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                n个元素的集合有2ⁿ个子集，其中包括空集和集合本身。这个公式有着深刻的组合数学背景。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="demonstrateSubsetCount()">演示公式</button>
                <button class="interactive-btn" onclick="visualizeSubsets()">可视化子集</button>
                <button class="interactive-btn" onclick="subsetGenerator()">🎮 子集生成器</button>
                <button class="interactive-btn" onclick="resetCanvas('subset-count')">重置</button>
            </div>

            <div class="control-panel" id="subsetCountPanel">
                <h3>🎯 子集计数器</h3>
                <div class="slider-container">
                    <label>集合元素个数：</label>
                    <input type="range" class="slider" id="elementCount" min="1" max="5" step="1" value="3" oninput="updateSubsetCount()">
                    <span id="elementCountValue">3</span>
                </div>
                <div class="formula-display" id="subsetFormula">
                    集合 {a, b, c} 的子集个数：2³ = 8
                </div>
                <button class="interactive-btn" onclick="generateAllSubsets()">生成所有子集</button>
            </div>

            <div class="concept-grid">
                <div class="concept-card">
                    <div class="concept-title">公式推导</div>
                    <div class="concept-desc">
                        每个元素有两种选择：<br>
                        • 包含在子集中<br>
                        • 不包含在子集中<br>
                        所以：<span class="highlight">2ⁿ 个子集</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">特殊情况</div>
                    <div class="concept-desc">
                        • 真子集个数：2ⁿ - 1<br>
                        • 非空子集个数：2ⁿ - 1<br>
                        • 非空真子集：<span class="highlight">2ⁿ - 2</span>
                    </div>
                </div>
                <div class="concept-card">
                    <div class="concept-title">实际应用</div>
                    <div class="concept-desc">
                        • 组合选择问题<br>
                        • 幂集的构造<br>
                        • 布尔代数基础<br>
                        • <span class="highlight">计算机科学</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 7. 二次方程根的分布 -->
        <div class="section" id="root-distribution">
            <h2 class="section-title">7. 二次方程根的分布</h2>
            <div class="canvas-container">
                <canvas id="rootDistributionCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                二次方程根的分布情况决定了解集的结构，通过图像可以直观理解根与系数的关系。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="analyzeRootDistribution()">分析根分布</button>
                <button class="interactive-btn" onclick="showParabolaAnimation()">抛物线动画</button>
                <button class="interactive-btn" onclick="rootExplorer()">🎮 根的探索</button>
                <button class="interactive-btn" onclick="resetCanvas('root-distribution')">重置</button>
            </div>

            <div class="control-panel" id="rootPanel">
                <h3>🎯 根分布分析器</h3>
                <div class="slider-container">
                    <label>系数 a：</label>
                    <input type="range" class="slider" id="rootA" min="0.1" max="3" step="0.1" value="1" oninput="updateRootDistribution()">
                    <span id="rootAValue">1</span>
                </div>
                <div class="slider-container">
                    <label>系数 b：</label>
                    <input type="range" class="slider" id="rootB" min="-6" max="6" step="0.5" value="-2" oninput="updateRootDistribution()">
                    <span id="rootBValue">-2</span>
                </div>
                <div class="slider-container">
                    <label>系数 c：</label>
                    <input type="range" class="slider" id="rootC" min="-5" max="5" step="0.5" value="1" oninput="updateRootDistribution()">
                    <span id="rootCValue">1</span>
                </div>
                <div class="formula-display" id="rootEquation">
                    y = x² - 2x + 1
                </div>
                <div class="formula-display" id="rootAnalysis">
                    判别式 Δ = 0，有一个重根
                </div>
            </div>
        </div>

        <!-- 8. 综合练习 -->
        <div class="section" id="practice">
            <h2 class="section-title">8. 综合练习</h2>
            <div class="canvas-container">
                <canvas id="practiceCanvas" width="800" height="500"></canvas>
            </div>
            <p class="explanation">
                通过综合练习巩固所学知识，包括子集关系、参数求解、集合相等等各种题型。
            </p>
            <div style="text-align: center;">
                <button class="interactive-btn" onclick="startQuiz()">开始测验</button>
                <button class="interactive-btn" onclick="showSolution()">查看解答</button>
                <button class="interactive-btn" onclick="nextProblem()">下一题</button>
                <button class="interactive-btn" onclick="resetCanvas('practice')">重置</button>
            </div>

            <div class="control-panel" id="practicePanel">
                <h3>🎯 智能练习系统</h3>
                <div id="currentProblem" class="formula-display">
                    点击"开始测验"开始练习
                </div>
                <div style="margin: 15px 0;">
                    <input type="text" id="answerInput" placeholder="输入你的答案" style="padding: 10px; border-radius: 5px; border: 2px solid #667eea; width: 200px;">
                    <button class="interactive-btn" onclick="checkAnswer()">提交答案</button>
                </div>
                <div id="feedback" class="formula-display" style="display: none;">
                    反馈信息
                </div>
                <div id="scoreDisplay" class="formula-display">
                    得分：0 / 0
                </div>
            </div>
        </div>
    </div>

    <script>
        // 获取所有画布
        const canvases = {
            subset: document.getElementById('subsetCanvas'),
            parameters: document.getElementById('parametersCanvas'),
            inequalities: document.getElementById('inequalitiesCanvas'),
            equalSets: document.getElementById('equalSetsCanvas'),
            quadratic: document.getElementById('quadraticCanvas'),
            subsetCount: document.getElementById('subsetCountCanvas'),
            rootDistribution: document.getElementById('rootDistributionCanvas'),
            practice: document.getElementById('practiceCanvas')
        };

        const contexts = {};
        Object.keys(canvases).forEach(key => {
            contexts[key] = canvases[key].getContext('2d');
        });

        // 动画变量
        let animationFrames = {};
        let currentAnimations = {};

        // 初始化所有画布
        function initializeCanvases() {
            Object.keys(contexts).forEach(key => {
                resetCanvas(key);
            });
        }

        function resetCanvas(canvasName) {
            const ctx = contexts[canvasName];
            ctx.clearRect(0, 0, canvases[canvasName].width, canvases[canvasName].height);

            ctx.fillStyle = '#999';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('点击按钮开始学习', canvases[canvasName].width / 2, canvases[canvasName].height / 2);
        }

        // 1. 子集演示功能
        function demonstrateSubset() {
            const ctx = contexts.subset;
            ctx.clearRect(0, 0, canvases.subset.width, canvases.subset.height);

            // 标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('子集关系演示', 400, 40);

            // 绘制集合B (大圆)
            setTimeout(() => {
                ctx.beginPath();
                ctx.arc(400, 250, 150, 0, 2 * Math.PI);
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.stroke();

                ctx.fillStyle = '#667eea';
                ctx.font = '20px Microsoft YaHei';
                ctx.fillText('集合 B', 400, 120);
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('B = {1, 2, 3, 4, 5, 6}', 400, 450);
            }, 500);

            // 绘制集合A (小圆)
            setTimeout(() => {
                ctx.beginPath();
                ctx.arc(350, 220, 80, 0, 2 * Math.PI);
                ctx.strokeStyle = '#ff6b6b';
                ctx.lineWidth = 3;
                ctx.stroke();
                ctx.fillStyle = 'rgba(255, 107, 107, 0.2)';
                ctx.fill();

                ctx.fillStyle = '#ff6b6b';
                ctx.font = '18px Microsoft YaHei';
                ctx.fillText('集合 A', 350, 160);
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText('A = {2, 3, 4}', 350, 300);
            }, 1500);

            // 添加元素
            const elementsB = [
                {value: '1', x: 280, y: 200},
                {value: '2', x: 320, y: 190},
                {value: '3', x: 350, y: 200},
                {value: '4', x: 380, y: 220},
                {value: '5', x: 480, y: 220},
                {value: '6', x: 520, y: 250}
            ];

            elementsB.forEach((element, index) => {
                setTimeout(() => {
                    ctx.beginPath();
                    ctx.arc(element.x, element.y, 15, 0, 2 * Math.PI);

                    // A的元素用红色，B独有的用蓝色
                    if (['2', '3', '4'].includes(element.value)) {
                        ctx.fillStyle = '#ff6b6b';
                    } else {
                        ctx.fillStyle = '#667eea';
                    }
                    ctx.fill();

                    ctx.fillStyle = 'white';
                    ctx.font = '12px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(element.value, element.x, element.y + 4);
                }, 2000 + index * 300);
            });

            // 显示结论
            setTimeout(() => {
                ctx.fillStyle = '#44ff44';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('A ⊆ B (A是B的子集)', 400, 480);
            }, 4000);
        }

        function showSubsetTypes() {
            const ctx = contexts.subset;
            ctx.clearRect(0, 0, canvases.subset.width, canvases.subset.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('子集的类型', 400, 40);

            // 真子集示例
            ctx.fillStyle = '#667eea';
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText('真子集 (A ⊊ B)', 200, 100);

            ctx.beginPath();
            ctx.arc(200, 180, 60, 0, 2 * Math.PI);
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.beginPath();
            ctx.arc(180, 160, 30, 0, 2 * Math.PI);
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 2;
            ctx.stroke();
            ctx.fillStyle = 'rgba(255, 107, 107, 0.3)';
            ctx.fill();

            // 相等集合示例
            ctx.fillStyle = '#44ff44';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('相等集合 (A = B)', 600, 100);

            ctx.beginPath();
            ctx.arc(600, 180, 60, 0, 2 * Math.PI);
            ctx.strokeStyle = '#44ff44';
            ctx.lineWidth = 3;
            ctx.stroke();
            ctx.fillStyle = 'rgba(68, 255, 68, 0.3)';
            ctx.fill();

            // 空集示例
            ctx.fillStyle = '#999';
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText('空集 (∅ ⊆ A)', 400, 320);

            ctx.beginPath();
            ctx.arc(400, 380, 50, 0, 2 * Math.PI);
            ctx.strokeStyle = '#999';
            ctx.lineWidth = 2;
            ctx.stroke();

            ctx.fillStyle = '#999';
            ctx.font = '24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('∅', 400, 390);
        }

        function interactiveSubset() {
            document.getElementById('subsetPanel').classList.add('active');
            const ctx = contexts.subset;
            ctx.clearRect(0, 0, canvases.subset.width, canvases.subset.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 互动子集演示', 400, 40);

            // 绘制两个可编辑的集合
            drawEditableSet(ctx, 250, 200, 100, 'A', ['1', '2']);
            drawEditableSet(ctx, 550, 200, 120, 'B', ['1', '2', '3', '4']);

            ctx.fillStyle = '#666';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('使用下方按钮添加或移除元素', 400, 450);
        }

        function drawEditableSet(ctx, x, y, radius, name, elements) {
            // 绘制集合圆圈
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, 2 * Math.PI);
            ctx.strokeStyle = name === 'A' ? '#ff6b6b' : '#667eea';
            ctx.lineWidth = 3;
            ctx.stroke();

            // 集合名称
            ctx.fillStyle = name === 'A' ? '#ff6b6b' : '#667eea';
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(`集合 ${name}`, x, y - radius - 20);

            // 绘制元素
            elements.forEach((element, index) => {
                const angle = (index * 2 * Math.PI) / elements.length;
                const elemX = x + Math.cos(angle) * (radius * 0.6);
                const elemY = y + Math.sin(angle) * (radius * 0.6);

                ctx.beginPath();
                ctx.arc(elemX, elemY, 15, 0, 2 * Math.PI);
                ctx.fillStyle = name === 'A' ? '#ff6b6b' : '#667eea';
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = '12px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(element, elemX, elemY + 4);
            });
        }

        // 2. 参数求解功能
        function solveParameterProblem() {
            const ctx = contexts.parameters;
            ctx.clearRect(0, 0, canvases.parameters.width, canvases.parameters.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('参数求解演示', 400, 40);

            // 问题描述
            const steps = [
                '已知：A = {x | x² - 3x + a = 0}, B = {1, 2}',
                '条件：A ⊆ B',
                '求：参数 a 的值',
                '分析：A的元素必须都在B中',
                '情况1：A = ∅ (无解)',
                '情况2：A = {1}',
                '情况3：A = {2}',
                '情况4：A = {1, 2}',
                '解得：a = 2'
            ];

            steps.forEach((step, index) => {
                setTimeout(() => {
                    ctx.fillStyle = index < 3 ? '#667eea' : '#333';
                    ctx.font = index < 3 ? 'bold 18px Microsoft YaHei' : '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(step, 400, 100 + index * 35);

                    if (index === steps.length - 1) {
                        ctx.fillStyle = '#44ff44';
                        ctx.font = 'bold 20px Microsoft YaHei';
                        ctx.fillText('答案：a = 2', 400, 450);
                    }
                }, index * 800);
            });
        }

        function visualizeParameter() {
            const ctx = contexts.parameters;
            ctx.clearRect(0, 0, canvases.parameters.width, canvases.parameters.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('参数可视化过程', 400, 40);

            // 绘制数轴
            drawNumberLine(ctx, 100, 200, 600, [-1, 0, 1, 2, 3, 4]);

            // 标记集合B
            ctx.fillStyle = '#667eea';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('集合 B = {1, 2}', 400, 120);

            // 高亮B的元素
            [1, 2].forEach(num => {
                const x = 100 + (num + 1) * 100;
                ctx.beginPath();
                ctx.arc(x, 200, 20, 0, 2 * Math.PI);
                ctx.fillStyle = 'rgba(102, 126, 234, 0.3)';
                ctx.fill();
                ctx.strokeStyle = '#667eea';
                ctx.lineWidth = 3;
                ctx.stroke();
            });

            // 显示A的可能情况
            setTimeout(() => {
                ctx.fillStyle = '#ff6b6b';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('A ⊆ B 的可能情况：', 400, 280);
                ctx.fillText('A = ∅, A = {1}, A = {2}, A = {1,2}', 400, 310);
            }, 1000);
        }

        function parameterPractice() {
            document.getElementById('parameterPanel').classList.add('active');
            updateParameter();
        }

        function updateParameter() {
            const a = parseFloat(document.getElementById('paramA').value);
            document.getElementById('paramAValue').textContent = a;

            const formula = `A = {x | x² + ${a}x + 1 = 0}, B = {1, 2}`;
            document.getElementById('parameterFormula').textContent = formula;
        }

        function checkParameterCondition() {
            const a = parseFloat(document.getElementById('paramA').value);
            const discriminant = a * a - 4;

            let result = '';
            if (discriminant < 0) {
                result = 'A = ∅ (空集)，满足 A ⊆ B';
            } else if (discriminant === 0) {
                const root = -a / 2;
                if (root === 1 || root === 2) {
                    result = `A = {${root}}，满足 A ⊆ B`;
                } else {
                    result = `A = {${root}}，不满足 A ⊆ B`;
                }
            } else {
                const root1 = (-a + Math.sqrt(discriminant)) / 2;
                const root2 = (-a - Math.sqrt(discriminant)) / 2;
                const roots = [root1, root2].map(r => Math.round(r * 100) / 100);

                const validRoots = roots.filter(r => r === 1 || r === 2);
                if (validRoots.length === roots.length) {
                    result = `A = {${roots.join(', ')}}，满足 A ⊆ B`;
                } else {
                    result = `A = {${roots.join(', ')}}，不满足 A ⊆ B`;
                }
            }

            alert(result);
        }

        // 3. 不等式关系功能
        function showInequalityRelations() {
            const ctx = contexts.inequalities;
            ctx.clearRect(0, 0, canvases.inequalities.width, canvases.inequalities.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('一次不等式解集关系', 400, 40);

            // 绘制数轴
            drawNumberLine(ctx, 100, 200, 600, [-3, -2, -1, 0, 1, 2, 3, 4, 5]);

            // 区间A: [-1, 3]
            drawInterval(ctx, 100 + 200, 100 + 600, 180, '#ff6b6b', 'A: [-1, 3]');

            // 区间B: [0, 2]
            drawInterval(ctx, 100 + 300, 100 + 500, 220, '#667eea', 'B: [0, 2]');

            // 显示关系
            setTimeout(() => {
                ctx.fillStyle = '#44ff44';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('B ⊆ A (B是A的子集)', 400, 300);
            }, 1000);
        }

        function drawInterval(ctx, startX, endX, y, color, label) {
            // 绘制区间线段
            ctx.strokeStyle = color;
            ctx.lineWidth = 6;
            ctx.beginPath();
            ctx.moveTo(startX, y);
            ctx.lineTo(endX, y);
            ctx.stroke();

            // 绘制端点
            ctx.beginPath();
            ctx.arc(startX, y, 5, 0, 2 * Math.PI);
            ctx.arc(endX, y, 5, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();

            // 标签
            ctx.fillStyle = color;
            ctx.font = '14px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText(label, (startX + endX) / 2, y - 15);
        }

        function animateIntervals() {
            const ctx = contexts.inequalities;
            ctx.clearRect(0, 0, canvases.inequalities.width, canvases.inequalities.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('区间关系动画', 400, 40);

            drawNumberLine(ctx, 100, 200, 600, [-3, -2, -1, 0, 1, 2, 3, 4, 5]);

            // 动画绘制区间A
            setTimeout(() => {
                animateIntervalDraw(ctx, 100 + 200, 100 + 600, 180, '#ff6b6b', 'A');
            }, 500);

            // 动画绘制区间B
            setTimeout(() => {
                animateIntervalDraw(ctx, 100 + 300, 100 + 500, 220, '#667eea', 'B');
            }, 1500);

            // 分析关系
            setTimeout(() => {
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('分析：B的所有元素都在A中', 400, 280);
                ctx.fillText('因此：B ⊆ A', 400, 310);
            }, 2500);
        }

        function animateIntervalDraw(ctx, startX, endX, y, color, name) {
            let currentX = startX;
            const speed = 3;

            const drawFrame = () => {
                if (currentX <= endX) {
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 6;
                    ctx.beginPath();
                    ctx.moveTo(startX, y);
                    ctx.lineTo(currentX, y);
                    ctx.stroke();

                    currentX += speed;
                    requestAnimationFrame(drawFrame);
                } else {
                    // 绘制端点
                    ctx.beginPath();
                    ctx.arc(startX, y, 5, 0, 2 * Math.PI);
                    ctx.arc(endX, y, 5, 0, 2 * Math.PI);
                    ctx.fillStyle = color;
                    ctx.fill();

                    // 标签
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(name, (startX + endX) / 2, y - 15);
                }
            };

            drawFrame();
        }

        function interactiveIntervals() {
            document.getElementById('intervalPanel').classList.add('active');
            updateIntervals();
        }

        function updateIntervals() {
            const a1 = parseInt(document.getElementById('intervalA1').value);
            const a2 = parseInt(document.getElementById('intervalA2').value);
            const b1 = parseInt(document.getElementById('intervalB1').value);
            const b2 = parseInt(document.getElementById('intervalB2').value);

            document.getElementById('intervalA1Value').textContent = a1;
            document.getElementById('intervalA2Value').textContent = a2;
            document.getElementById('intervalB1Value').textContent = b1;
            document.getElementById('intervalB2Value').textContent = b2;

            // 绘制区间
            const ctx = contexts.inequalities;
            ctx.clearRect(0, 0, canvases.inequalities.width, canvases.inequalities.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 互动区间关系', 400, 40);

            drawNumberLine(ctx, 100, 200, 600, [-10, -5, 0, 5, 10]);

            // 计算位置
            const scale = 30;
            const offset = 400;

            const startA = offset + a1 * scale;
            const endA = offset + a2 * scale;
            const startB = offset + b1 * scale;
            const endB = offset + b2 * scale;

            if (a1 < a2) drawInterval(ctx, startA, endA, 180, '#ff6b6b', `A: [${a1}, ${a2}]`);
            if (b1 < b2) drawInterval(ctx, startB, endB, 220, '#667eea', `B: [${b1}, ${b2}]`);

            // 分析关系
            let relation = '';
            if (a1 < a2 && b1 < b2) {
                if (b1 >= a1 && b2 <= a2) {
                    relation = 'B ⊆ A (B是A的子集)';
                } else if (a1 >= b1 && a2 <= b2) {
                    relation = 'A ⊆ B (A是B的子集)';
                } else if (a1 === b1 && a2 === b2) {
                    relation = 'A = B (A与B相等)';
                } else {
                    relation = 'A与B没有包含关系';
                }
            } else {
                relation = '请确保区间有效 (左端点 < 右端点)';
            }

            document.getElementById('relationResult').textContent = relation;
        }

        // 4. 集合相等功能
        function demonstrateEquality() {
            const ctx = contexts.equalSets;
            ctx.clearRect(0, 0, canvases.equalSets.width, canvases.equalSets.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('集合相等演示', 400, 40);

            // 演示A = B的过程
            const steps = [
                '证明 A = B 需要两步：',
                '第一步：证明 A ⊆ B',
                '第二步：证明 B ⊆ A',
                '结论：A = B'
            ];

            steps.forEach((step, index) => {
                setTimeout(() => {
                    ctx.fillStyle = index === 0 ? '#667eea' : '#333';
                    ctx.font = index === 0 ? 'bold 18px Microsoft YaHei' : '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(step, 400, 120 + index * 40);
                }, index * 1000);
            });

            // 绘制重叠的圆表示相等
            setTimeout(() => {
                ctx.beginPath();
                ctx.arc(400, 300, 80, 0, 2 * Math.PI);
                ctx.strokeStyle = '#44ff44';
                ctx.lineWidth = 4;
                ctx.stroke();
                ctx.fillStyle = 'rgba(68, 255, 68, 0.2)';
                ctx.fill();

                ctx.fillStyle = '#44ff44';
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('A = B', 400, 310);
            }, 4000);
        }

        function showEqualityConditions() {
            const ctx = contexts.equalSets;
            ctx.clearRect(0, 0, canvases.equalSets.width, canvases.equalSets.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('集合相等的条件', 400, 40);

            // 条件1：元素完全相同
            ctx.fillStyle = '#667eea';
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText('条件1：元素完全相同', 200, 120);

            drawSet(ctx, 200, 200, 60, ['1', '2', '3'], '#667eea');
            drawSet(ctx, 200, 320, 60, ['3', '1', '2'], '#667eea');

            ctx.fillStyle = '#44ff44';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('相等！', 200, 400);

            // 条件2：互为子集
            ctx.fillStyle = '#ff6b6b';
            ctx.font = '18px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('条件2：A⊆B 且 B⊆A', 600, 120);

            // 绘制韦恩图
            ctx.beginPath();
            ctx.arc(580, 200, 50, 0, 2 * Math.PI);
            ctx.strokeStyle = '#ff6b6b';
            ctx.lineWidth = 3;
            ctx.stroke();

            ctx.beginPath();
            ctx.arc(620, 200, 50, 0, 2 * Math.PI);
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.stroke();

            ctx.fillStyle = '#333';
            ctx.font = '14px Microsoft YaHei';
            ctx.fillText('A', 560, 180);
            ctx.fillText('B', 640, 180);

            setTimeout(() => {
                // 重叠显示相等
                ctx.beginPath();
                ctx.arc(600, 280, 50, 0, 2 * Math.PI);
                ctx.strokeStyle = '#44ff44';
                ctx.lineWidth = 4;
                ctx.stroke();
                ctx.fillStyle = 'rgba(68, 255, 68, 0.3)';
                ctx.fill();

                ctx.fillStyle = '#44ff44';
                ctx.font = 'bold 16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('A = B', 600, 290);
            }, 2000);
        }

        function drawSet(ctx, x, y, radius, elements, color) {
            // 绘制集合圆圈
            ctx.beginPath();
            ctx.arc(x, y, radius, 0, 2 * Math.PI);
            ctx.strokeStyle = color;
            ctx.lineWidth = 2;
            ctx.stroke();

            // 绘制元素
            elements.forEach((element, index) => {
                const angle = (index * 2 * Math.PI) / elements.length;
                const elemX = x + Math.cos(angle) * (radius * 0.6);
                const elemY = y + Math.sin(angle) * (radius * 0.6);

                ctx.beginPath();
                ctx.arc(elemX, elemY, 12, 0, 2 * Math.PI);
                ctx.fillStyle = color;
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = '10px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(element, elemX, elemY + 3);
            });
        }

        function equalityPuzzle() {
            const ctx = contexts.equalSets;
            ctx.clearRect(0, 0, canvases.equalSets.width, canvases.equalSets.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 相等谜题', 400, 40);

            ctx.fillStyle = '#667eea';
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText('判断下列集合是否相等：', 400, 100);

            const puzzles = [
                'A = {1, 2, 3}  B = {3, 2, 1}',
                'C = {x | x² = 4}  D = {-2, 2}',
                'E = {0}  F = {∅}'
            ];

            puzzles.forEach((puzzle, index) => {
                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(puzzle, 400, 150 + index * 60);

                setTimeout(() => {
                    let answer = '';
                    if (index === 0) answer = '✓ 相等（元素相同）';
                    else if (index === 1) answer = '✓ 相等（解集相同）';
                    else answer = '✗ 不相等（0 ≠ ∅）';

                    ctx.fillStyle = index < 2 ? '#44ff44' : '#ff6b6b';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(answer, 400, 170 + index * 60);
                }, (index + 1) * 2000);
            });
        }

        // 5. 二次方程解集功能
        function analyzeQuadraticSets() {
            const ctx = contexts.quadratic;
            ctx.clearRect(0, 0, canvases.quadratic.width, canvases.quadratic.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('二次方程解集分析', 400, 40);

            // 分析不同情况
            const cases = [
                'Δ > 0：两个不等实根 → 解集有2个元素',
                'Δ = 0：一个重根 → 解集有1个元素',
                'Δ < 0：无实根 → 解集为空集'
            ];

            cases.forEach((caseText, index) => {
                setTimeout(() => {
                    const colors = ['#44ff44', '#ffaa00', '#ff6b6b'];
                    ctx.fillStyle = colors[index];
                    ctx.font = '18px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(caseText, 400, 120 + index * 50);

                    // 绘制对应的抛物线示意图
                    drawParabolaCase(ctx, 150 + index * 200, 300, index);
                }, index * 1000);
            });
        }

        function drawParabolaCase(ctx, x, y, caseType) {
            const width = 120;
            const height = 80;

            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;

            // 绘制坐标轴
            ctx.beginPath();
            ctx.moveTo(x - width/2, y);
            ctx.lineTo(x + width/2, y);
            ctx.moveTo(x, y - height/2);
            ctx.lineTo(x, y + height/2);
            ctx.stroke();

            // 绘制抛物线
            ctx.beginPath();
            const colors = ['#44ff44', '#ffaa00', '#ff6b6b'];
            ctx.strokeStyle = colors[caseType];
            ctx.lineWidth = 3;

            for (let i = -width/2; i <= width/2; i += 2) {
                const t = i / (width/4);
                let parabY;

                if (caseType === 0) {
                    // 两个交点
                    parabY = y + (t * t - 1) * 20;
                } else if (caseType === 1) {
                    // 一个交点
                    parabY = y + t * t * 20;
                } else {
                    // 无交点
                    parabY = y + (t * t + 1) * 15;
                }

                if (i === -width/2) {
                    ctx.moveTo(x + i, parabY);
                } else {
                    ctx.lineTo(x + i, parabY);
                }
            }
            ctx.stroke();

            // 标记交点
            if (caseType === 0) {
                // 两个交点
                ctx.beginPath();
                ctx.arc(x - 20, y, 4, 0, 2 * Math.PI);
                ctx.arc(x + 20, y, 4, 0, 2 * Math.PI);
                ctx.fillStyle = '#44ff44';
                ctx.fill();
            } else if (caseType === 1) {
                // 一个交点
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI);
                ctx.fillStyle = '#ffaa00';
                ctx.fill();
            }
        }

        // 辅助函数
        function drawNumberLine(ctx, startX, y, width, numbers) {
            // 绘制数轴
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(startX, y);
            ctx.lineTo(startX + width, y);
            ctx.stroke();

            // 绘制刻度和数字
            numbers.forEach((num, index) => {
                const x = startX + (index * width) / (numbers.length - 1);

                // 刻度线
                ctx.beginPath();
                ctx.moveTo(x, y - 10);
                ctx.lineTo(x, y + 10);
                ctx.stroke();

                // 数字
                ctx.fillStyle = '#333';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(num.toString(), x, y + 30);
            });
        }

        // 6. 子集个数功能
        function demonstrateSubsetCount() {
            const ctx = contexts.subsetCount;
            ctx.clearRect(0, 0, canvases.subsetCount.width, canvases.subsetCount.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('子集个数公式演示', 400, 40);

            // 以3元素集合为例
            const elements = ['a', 'b', 'c'];
            const subsets = [
                '∅',
                '{a}', '{b}', '{c}',
                '{a,b}', '{a,c}', '{b,c}',
                '{a,b,c}'
            ];

            ctx.fillStyle = '#667eea';
            ctx.font = '18px Microsoft YaHei';
            ctx.fillText('集合 {a, b, c} 的所有子集：', 400, 100);

            subsets.forEach((subset, index) => {
                setTimeout(() => {
                    const row = Math.floor(index / 4);
                    const col = index % 4;
                    const x = 150 + col * 150;
                    const y = 150 + row * 60;

                    ctx.fillStyle = index === 0 ? '#ff6b6b' :
                                   index === subsets.length - 1 ? '#44ff44' : '#667eea';
                    ctx.font = '16px Microsoft YaHei';
                    ctx.textAlign = 'center';
                    ctx.fillText(subset, x, y);
                }, index * 300);
            });

            setTimeout(() => {
                ctx.fillStyle = '#333';
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText('总计：2³ = 8 个子集', 400, 400);
            }, subsets.length * 300 + 500);
        }

        function visualizeSubsets() {
            const ctx = contexts.subsetCount;
            ctx.clearRect(0, 0, canvases.subsetCount.width, canvases.subsetCount.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('子集生成过程可视化', 400, 40);

            // 二进制表示法
            ctx.fillStyle = '#667eea';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('每个元素有两种选择：包含(1) 或 不包含(0)', 400, 100);

            const binarySubsets = [
                {binary: '000', subset: '∅'},
                {binary: '001', subset: '{c}'},
                {binary: '010', subset: '{b}'},
                {binary: '011', subset: '{b,c}'},
                {binary: '100', subset: '{a}'},
                {binary: '101', subset: '{a,c}'},
                {binary: '110', subset: '{a,b}'},
                {binary: '111', subset: '{a,b,c}'}
            ];

            binarySubsets.forEach((item, index) => {
                setTimeout(() => {
                    const row = Math.floor(index / 2);
                    const col = index % 2;
                    const x = 250 + col * 300;
                    const y = 150 + row * 50;

                    ctx.fillStyle = '#333';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.textAlign = 'left';
                    ctx.fillText(`${item.binary} → ${item.subset}`, x, y);
                }, index * 400);
            });
        }

        function subsetGenerator() {
            document.getElementById('subsetCountPanel').classList.add('active');
            updateSubsetCount();
        }

        function updateSubsetCount() {
            const n = parseInt(document.getElementById('elementCount').value);
            document.getElementById('elementCountValue').textContent = n;

            const elements = ['a', 'b', 'c', 'd', 'e'].slice(0, n);
            const count = Math.pow(2, n);

            document.getElementById('subsetFormula').textContent =
                `集合 {${elements.join(', ')}} 的子集个数：2^${n} = ${count}`;
        }

        function generateAllSubsets() {
            const n = parseInt(document.getElementById('elementCount').value);
            const elements = ['a', 'b', 'c', 'd', 'e'].slice(0, n);
            const subsets = [];

            // 生成所有子集
            for (let i = 0; i < Math.pow(2, n); i++) {
                const subset = [];
                for (let j = 0; j < n; j++) {
                    if (i & (1 << j)) {
                        subset.push(elements[j]);
                    }
                }
                subsets.push(subset.length === 0 ? '∅' : `{${subset.join(',')}}`);
            }

            alert(`所有子集：\n${subsets.join('\n')}`);
        }

        // 7. 根分布功能
        function analyzeRootDistribution() {
            const ctx = contexts.rootDistribution;
            ctx.clearRect(0, 0, canvases.rootDistribution.width, canvases.rootDistribution.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('二次方程根的分布', 400, 40);

            // 绘制坐标系
            drawCoordinateSystem(ctx, 400, 250, 150);

            // 绘制标准抛物线
            drawParabola(ctx, 400, 250, 150, 1, -2, 1);

            ctx.fillStyle = '#667eea';
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('y = x² - 2x + 1', 400, 100);
            ctx.fillText('根的分布决定解集结构', 400, 450);
        }

        function drawCoordinateSystem(ctx, centerX, centerY, scale) {
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 1;

            // x轴
            ctx.beginPath();
            ctx.moveTo(centerX - scale, centerY);
            ctx.lineTo(centerX + scale, centerY);
            ctx.stroke();

            // y轴
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - scale);
            ctx.lineTo(centerX, centerY + scale);
            ctx.stroke();

            // 刻度
            for (let i = -2; i <= 2; i++) {
                if (i !== 0) {
                    const x = centerX + i * scale / 2;
                    const y = centerY + i * scale / 2;

                    // x轴刻度
                    ctx.beginPath();
                    ctx.moveTo(x, centerY - 5);
                    ctx.lineTo(x, centerY + 5);
                    ctx.stroke();

                    // y轴刻度
                    ctx.beginPath();
                    ctx.moveTo(centerX - 5, y);
                    ctx.lineTo(centerX + 5, y);
                    ctx.stroke();
                }
            }
        }

        function drawParabola(ctx, centerX, centerY, scale, a, b, c) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();

            for (let x = -3; x <= 3; x += 0.1) {
                const y = a * x * x + b * x + c;
                const screenX = centerX + x * scale / 2;
                const screenY = centerY - y * scale / 4;

                if (x === -3) {
                    ctx.moveTo(screenX, screenY);
                } else {
                    ctx.lineTo(screenX, screenY);
                }
            }
            ctx.stroke();

            // 标记根
            const discriminant = b * b - 4 * a * c;
            if (discriminant >= 0) {
                const root1 = (-b + Math.sqrt(discriminant)) / (2 * a);
                const root2 = (-b - Math.sqrt(discriminant)) / (2 * a);

                ctx.beginPath();
                ctx.arc(centerX + root1 * scale / 2, centerY, 5, 0, 2 * Math.PI);
                if (discriminant > 0) {
                    ctx.arc(centerX + root2 * scale / 2, centerY, 5, 0, 2 * Math.PI);
                }
                ctx.fillStyle = '#ff6b6b';
                ctx.fill();
            }
        }

        function rootExplorer() {
            document.getElementById('rootPanel').classList.add('active');
            updateRootDistribution();
        }

        function updateRootDistribution() {
            const a = parseFloat(document.getElementById('rootA').value);
            const b = parseFloat(document.getElementById('rootB').value);
            const c = parseFloat(document.getElementById('rootC').value);

            document.getElementById('rootAValue').textContent = a;
            document.getElementById('rootBValue').textContent = b;
            document.getElementById('rootCValue').textContent = c;

            document.getElementById('rootEquation').textContent =
                `y = ${a}x² + ${b}x + ${c}`;

            const discriminant = b * b - 4 * a * c;
            let analysis = '';

            if (discriminant > 0) {
                const root1 = (-b + Math.sqrt(discriminant)) / (2 * a);
                const root2 = (-b - Math.sqrt(discriminant)) / (2 * a);
                analysis = `判别式 Δ = ${discriminant.toFixed(2)} > 0，有两个不等实根：${root1.toFixed(2)}, ${root2.toFixed(2)}`;
            } else if (discriminant === 0) {
                const root = -b / (2 * a);
                analysis = `判别式 Δ = 0，有一个重根：${root.toFixed(2)}`;
            } else {
                analysis = `判别式 Δ = ${discriminant.toFixed(2)} < 0，无实根`;
            }

            document.getElementById('rootAnalysis').textContent = analysis;

            // 重新绘制抛物线
            const ctx = contexts.rootDistribution;
            ctx.clearRect(0, 0, canvases.rootDistribution.width, canvases.rootDistribution.height);

            ctx.fillStyle = '#333';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('🎮 根分布探索器', 400, 40);

            drawCoordinateSystem(ctx, 400, 250, 150);
            drawParabola(ctx, 400, 250, 150, a, b, c);
        }

        // 8. 练习功能
        let currentProblemIndex = 0;
        let score = 0;
        let totalProblems = 0;

        const problems = [
            {
                question: "设A = {1, 2}, B = {1, 2, 3}，则A与B的关系是？",
                answer: "A⊆B",
                options: ["A⊆B", "B⊆A", "A=B", "无关系"]
            },
            {
                question: "集合{x | x² = 4}等于哪个集合？",
                answer: "{-2, 2}",
                options: ["{2}", "{-2, 2}", "{4}", "∅"]
            },
            {
                question: "3个元素的集合有多少个子集？",
                answer: "8",
                options: ["6", "7", "8", "9"]
            }
        ];

        function startQuiz() {
            document.getElementById('practicePanel').classList.add('active');
            currentProblemIndex = 0;
            score = 0;
            totalProblems = 0;
            showCurrentProblem();
        }

        function showCurrentProblem() {
            if (currentProblemIndex < problems.length) {
                const problem = problems[currentProblemIndex];
                document.getElementById('currentProblem').textContent = problem.question;
                document.getElementById('answerInput').value = '';
                document.getElementById('feedback').style.display = 'none';
            } else {
                document.getElementById('currentProblem').textContent = '测验完成！';
                document.getElementById('answerInput').style.display = 'none';
            }
            updateScore();
        }

        function checkAnswer() {
            const userAnswer = document.getElementById('answerInput').value.trim();
            const correctAnswer = problems[currentProblemIndex].answer;
            const feedback = document.getElementById('feedback');

            totalProblems++;

            if (userAnswer === correctAnswer) {
                score++;
                feedback.textContent = '✓ 正确！';
                feedback.style.color = '#44ff44';
            } else {
                feedback.textContent = `✗ 错误。正确答案是：${correctAnswer}`;
                feedback.style.color = '#ff6b6b';
            }

            feedback.style.display = 'block';
            updateScore();
        }

        function nextProblem() {
            currentProblemIndex++;
            showCurrentProblem();
        }

        function updateScore() {
            document.getElementById('scoreDisplay').textContent = `得分：${score} / ${totalProblems}`;
        }

        function showSolution() {
            if (currentProblemIndex < problems.length) {
                const answer = problems[currentProblemIndex].answer;
                alert(`答案：${answer}`);
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initializeCanvases();

            // 平滑滚动
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    target.scrollIntoView({ behavior: 'smooth' });
                });
            });
        });
    </script>
</body>
</html>
