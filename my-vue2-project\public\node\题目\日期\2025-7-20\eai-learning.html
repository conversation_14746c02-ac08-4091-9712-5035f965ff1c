<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EAI技术学习 - 词汇动画故事</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .story-canvas {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 40px;
            overflow: hidden;
            position: relative;
        }

        canvas {
            display: block;
            width: 100%;
            height: 500px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .explanation {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .word-card {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            transform: translateX(-100px);
            opacity: 0;
            transition: all 0.6s ease;
        }

        .word-card.show {
            transform: translateX(0);
            opacity: 1;
        }

        .word-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .word-meaning {
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .word-example {
            font-style: italic;
            opacity: 0.9;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .interactive-area {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            text-align: center;
        }

        .quiz-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
            margin: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #74b9ff, #0984e3);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>EAI技术词汇学习之旅</h1>
            <p>通过动画故事学习企业应用集成技术</p>
        </div>

        <div class="story-canvas">
            <canvas id="storyCanvas" width="1000" height="500"></canvas>
        </div>

        <div class="controls">
            <button class="btn" onclick="startStory()">开始故事</button>
            <button class="btn" onclick="nextScene()">下一幕</button>
            <button class="btn" onclick="resetStory()">重新开始</button>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="explanation">
            <h2 style="color: #2d3436; margin-bottom: 20px;">故事解析与词汇学习</h2>
            <div id="wordCards"></div>
        </div>

        <div class="interactive-area">
            <h3 style="color: #2d3436; margin-bottom: 20px;">互动测试</h3>
            <p style="margin-bottom: 20px;">点击正确的翻译来加深理解：</p>
            <div id="quizArea"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('storyCanvas');
        const ctx = canvas.getContext('2d');
        let currentScene = 0;
        let animationFrame = 0;

        // 故事场景数据
        const scenes = [
            {
                title: "企业孤岛的困境",
                description: "从前，有一个大企业，它的各个部门就像孤立的岛屿...",
                words: [
                    {
                        word: "Enterprise",
                        chinese: "企业",
                        meaning: "大型商业组织",
                        example: "Enterprise Application Integration - 企业应用集成"
                    },
                    {
                        word: "Integration",
                        chinese: "集成",
                        meaning: "将分离的部分组合成整体",
                        example: "Data Integration - 数据集成"
                    }
                ]
            },
            {
                title: "EAI魔法师的出现",
                description: "这时，一位名叫EAI的魔法师出现了，他有四种神奇的魔法...",
                words: [
                    {
                        word: "Presentation",
                        chinese: "表示/展现",
                        meaning: "数据的显示和用户界面",
                        example: "Presentation Integration - 表示集成（黑盒集成）"
                    },
                    {
                        word: "Data",
                        chinese: "数据",
                        meaning: "信息的数字化表示",
                        example: "Data Integration - 数据集成（白盒集成）"
                    }
                ]
            }
        ];

        // 绘制函数
        function drawScene() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#74b9ff');
            gradient.addColorStop(1, '#0984e3');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            if (currentScene === 0) {
                drawIslandScene();
            } else if (currentScene === 1) {
                drawMagicianScene();
            }

            animationFrame++;
        }

        function drawIslandScene() {
            // 绘制孤立的岛屿（代表企业部门）
            const islands = [
                {x: 150, y: 300, label: "财务部"},
                {x: 400, y: 250, label: "销售部"},
                {x: 650, y: 320, label: "库存部"},
                {x: 850, y: 280, label: "客服部"}
            ];

            islands.forEach((island, index) => {
                // 岛屿动画效果
                const bounce = Math.sin(animationFrame * 0.05 + index) * 5;
                
                // 绘制岛屿
                ctx.fillStyle = '#00b894';
                ctx.beginPath();
                ctx.ellipse(island.x, island.y + bounce, 60, 30, 0, 0, 2 * Math.PI);
                ctx.fill();

                // 绘制标签
                ctx.fillStyle = 'white';
                ctx.font = '16px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(island.label, island.x, island.y + bounce - 10);

                // 绘制困惑的表情
                ctx.fillStyle = '#fdcb6e';
                ctx.beginPath();
                ctx.arc(island.x, island.y + bounce - 40, 15, 0, 2 * Math.PI);
                ctx.fill();
                
                // 眼睛
                ctx.fillStyle = 'black';
                ctx.fillRect(island.x - 5, island.y + bounce - 45, 2, 2);
                ctx.fillRect(island.x + 3, island.y + bounce - 45, 2, 2);
                
                // 困惑的嘴
                ctx.strokeStyle = 'black';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(island.x, island.y + bounce - 35, 5, 0, Math.PI);
                ctx.stroke();
            });

            // 绘制标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('企业各部门无法有效沟通', canvas.width / 2, 50);
        }

        function drawMagicianScene() {
            // 绘制EAI魔法师
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            
            // 魔法师身体
            ctx.fillStyle = '#6c5ce7';
            ctx.fillRect(centerX - 20, centerY, 40, 80);
            
            // 魔法师头部
            ctx.fillStyle = '#fdcb6e';
            ctx.beginPath();
            ctx.arc(centerX, centerY - 20, 25, 0, 2 * Math.PI);
            ctx.fill();
            
            // 魔法帽
            ctx.fillStyle = '#2d3436';
            ctx.beginPath();
            ctx.moveTo(centerX - 30, centerY - 20);
            ctx.lineTo(centerX, centerY - 70);
            ctx.lineTo(centerX + 30, centerY - 20);
            ctx.closePath();
            ctx.fill();

            // 魔法棒
            const wandX = centerX + 40;
            const wandY = centerY - 10;
            ctx.strokeStyle = '#8b4513';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(wandX, wandY);
            ctx.lineTo(wandX + 30, wandY - 20);
            ctx.stroke();

            // 魔法星星
            const stars = [
                {x: wandX + 35, y: wandY - 25, size: 8},
                {x: wandX + 45, y: wandY - 15, size: 6},
                {x: wandX + 40, y: wandY - 35, size: 5}
            ];

            stars.forEach(star => {
                const twinkle = Math.sin(animationFrame * 0.1) * 0.5 + 0.5;
                ctx.fillStyle = `rgba(255, 255, 0, ${twinkle})`;
                drawStar(star.x, star.y, star.size);
            });

            // 四种集成类型的图标
            const integrationTypes = [
                {x: 200, y: 150, label: "表示集成", color: '#e17055'},
                {x: 800, y: 150, label: "数据集成", color: '#74b9ff'},
                {x: 200, y: 350, label: "控制集成", color: '#00b894'},
                {x: 800, y: 350, label: "应用集成", color: '#fdcb6e'}
            ];

            integrationTypes.forEach((type, index) => {
                const pulse = Math.sin(animationFrame * 0.08 + index * Math.PI / 2) * 10 + 50;
                
                ctx.fillStyle = type.color;
                ctx.beginPath();
                ctx.arc(type.x, type.y, pulse / 2, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.textAlign = 'center';
                ctx.fillText(type.label, type.x, type.y + 5);
            });

            // 标题
            ctx.fillStyle = 'white';
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillText('EAI魔法师的四种集成魔法', canvas.width / 2, 50);
        }

        function drawStar(x, y, size) {
            ctx.beginPath();
            for (let i = 0; i < 5; i++) {
                const angle = (i * 4 * Math.PI) / 5;
                const x1 = x + Math.cos(angle) * size;
                const y1 = y + Math.sin(angle) * size;
                if (i === 0) ctx.moveTo(x1, y1);
                else ctx.lineTo(x1, y1);
            }
            ctx.closePath();
            ctx.fill();
        }

        function startStory() {
            currentScene = 0;
            animationFrame = 0;
            updateWordCards();
            updateProgress();
            animate();
        }

        function nextScene() {
            if (currentScene < scenes.length - 1) {
                currentScene++;
                updateWordCards();
                updateProgress();
            }
        }

        function resetStory() {
            currentScene = 0;
            animationFrame = 0;
            updateWordCards();
            updateProgress();
        }

        function animate() {
            drawScene();
            requestAnimationFrame(animate);
        }

        function updateWordCards() {
            const wordCardsContainer = document.getElementById('wordCards');
            wordCardsContainer.innerHTML = '';

            if (scenes[currentScene]) {
                scenes[currentScene].words.forEach((wordData, index) => {
                    setTimeout(() => {
                        const card = document.createElement('div');
                        card.className = 'word-card';
                        card.innerHTML = `
                            <div class="word-title">${wordData.word} - ${wordData.chinese}</div>
                            <div class="word-meaning">${wordData.meaning}</div>
                            <div class="word-example">${wordData.example}</div>
                        `;
                        wordCardsContainer.appendChild(card);
                        
                        setTimeout(() => {
                            card.classList.add('show');
                        }, 100);
                    }, index * 500);
                });
            }
        }

        function updateProgress() {
            const progressFill = document.getElementById('progressFill');
            const progress = ((currentScene + 1) / scenes.length) * 100;
            progressFill.style.width = progress + '%';
        }

        // 初始化
        startStory();

        // 互动测试
        function createQuiz() {
            const quizArea = document.getElementById('quizArea');
            const questions = [
                {
                    question: "哪种集成是黑盒集成？",
                    options: ["表示集成", "数据集成", "控制集成"],
                    correct: 0
                },
                {
                    question: "EAI的英文全称是什么？",
                    options: ["Enterprise Application Integration", "Electronic Application Interface", "Extended Application Implementation"],
                    correct: 0
                }
            ];

            let currentQuestion = 0;

            function showQuestion() {
                if (currentQuestion < questions.length) {
                    const q = questions[currentQuestion];
                    quizArea.innerHTML = `
                        <h4>${q.question}</h4>
                        ${q.options.map((option, index) => 
                            `<button class="btn quiz-btn" onclick="checkAnswer(${index}, ${q.correct})">${option}</button>`
                        ).join('')}
                    `;
                }
            }

            window.checkAnswer = function(selected, correct) {
                if (selected === correct) {
                    quizArea.innerHTML += '<p style="color: green; margin-top: 20px;">✓ 正确！</p>';
                } else {
                    quizArea.innerHTML += '<p style="color: red; margin-top: 20px;">✗ 错误，请重新思考</p>';
                }
                
                setTimeout(() => {
                    currentQuestion++;
                    showQuestion();
                }, 2000);
            };

            showQuestion();
        }

        // 启动测试
        setTimeout(createQuiz, 3000);
    </script>
</body>
</html>
