<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供应链信息流学习游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .game-area {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .supply-chain {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 40px 0;
            position: relative;
        }

        .chain-node {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .supplier { background: linear-gradient(45deg, #ff6b6b, #ee5a24); }
        .manufacturer { background: linear-gradient(45deg, #4834d4, #686de0); }
        .distributor { background: linear-gradient(45deg, #00d2d3, #01a3a4); }
        .retailer { background: linear-gradient(45deg, #ff9ff3, #f368e0); }

        .chain-node:hover {
            transform: scale(1.1);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .chain-arrow {
            position: absolute;
            top: 50%;
            width: 80px;
            height: 4px;
            background: #ddd;
            z-index: 1;
        }

        .arrow1 { left: 140px; }
        .arrow2 { left: 340px; }
        .arrow3 { left: 540px; }

        .info-flow {
            margin: 40px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .flow-section {
            margin: 20px 0;
        }

        .flow-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }

        .demand-flow { color: #e74c3c; }
        .supply-flow { color: #27ae60; }

        .flow-items {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .flow-item {
            padding: 10px 20px;
            border-radius: 25px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .demand-item { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .supply-item { background: linear-gradient(45deg, #27ae60, #229954); }

        .flow-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .quiz-section {
            background: #fff;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #3498db;
        }

        .quiz-question {
            font-size: 1.3rem;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }

        .quiz-option {
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: bold;
        }

        .quiz-option:hover {
            border-color: #3498db;
            background: #ecf0f1;
        }

        .quiz-option.correct {
            background: #2ecc71;
            color: white;
            border-color: #27ae60;
        }

        .quiz-option.wrong {
            background: #e74c3c;
            color: white;
            border-color: #c0392b;
        }

        .explanation {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #27ae60;
            display: none;
        }

        .canvas-container {
            text-align: center;
            margin: 30px 0;
        }

        #gameCanvas {
            border: 2px solid #ddd;
            border-radius: 10px;
            background: white;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .floating-particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: rgba(255,255,255,0.7);
            border-radius: 50%;
            animation: float 3s infinite ease-in-out;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚚 供应链信息流学习游戏</h1>
            <p>通过互动动画学习供应链中的需求信息流和供应信息流</p>
        </div>

        <div class="game-area">
            <h2 style="text-align: center; margin-bottom: 30px; color: #2c3e50;">供应链环节</h2>
            
            <div class="supply-chain">
                <div class="chain-node supplier" onclick="showNodeInfo('supplier')">
                    <div>🏭</div>
                    <div>供应商</div>
                </div>
                <div class="chain-arrow arrow1"></div>
                
                <div class="chain-node manufacturer" onclick="showNodeInfo('manufacturer')">
                    <div>🔧</div>
                    <div>制造商</div>
                </div>
                <div class="chain-arrow arrow2"></div>
                
                <div class="chain-node distributor" onclick="showNodeInfo('distributor')">
                    <div>📦</div>
                    <div>分销商</div>
                </div>
                <div class="chain-arrow arrow3"></div>
                
                <div class="chain-node retailer" onclick="showNodeInfo('retailer')">
                    <div>🏪</div>
                    <div>零售商</div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="200"></canvas>
                <p style="margin-top: 10px; color: #666;">点击上方环节查看信息流动画</p>
            </div>
        </div>

        <div class="info-flow">
            <div class="flow-section">
                <div class="flow-title demand-flow">📈 需求信息流（从需方→供方）</div>
                <div class="flow-items">
                    <div class="flow-item demand-item" onclick="animateFlow('demand', '客户订单')">客户订单</div>
                    <div class="flow-item demand-item" onclick="animateFlow('demand', '生产计划')">生产计划</div>
                    <div class="flow-item demand-item" onclick="animateFlow('demand', '采购合同')">采购合同</div>
                </div>
            </div>

            <div class="flow-section">
                <div class="flow-title supply-flow">📦 供应信息流（从供方→需方）</div>
                <div class="flow-items">
                    <div class="flow-item supply-item" onclick="animateFlow('supply', '入库单')">入库单</div>
                    <div class="flow-item supply-item" onclick="animateFlow('supply', '完工报告单')">完工报告单</div>
                    <div class="flow-item supply-item" onclick="animateFlow('supply', '库存记录')">库存记录</div>
                    <div class="flow-item supply-item" onclick="animateFlow('supply', '可供销售量')">可供销售量</div>
                    <div class="flow-item supply-item" onclick="animateFlow('supply', '提货发运单')">提货发运单</div>
                </div>
            </div>
        </div>

        <div class="quiz-section">
            <div class="quiz-question">
                📝 <strong>练习题：</strong>供应链中的信息流覆盖了从供应商、制造商到分销商，再到零售商等供应链中的所有环节，其信息流分为需求信息流和供应信息流，（　　）属于需求信息流，（　　）属于供应信息流。
            </div>
            
            <div class="quiz-options">
                <div class="quiz-option" onclick="selectAnswer(this, false)">A. 库存记录</div>
                <div class="quiz-option" onclick="selectAnswer(this, true)">B. 生产计划</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">C. 商品入库单</div>
                <div class="quiz-option" onclick="selectAnswer(this, false)">D. 提货发运单</div>
            </div>

            <div class="explanation" id="explanation">
                <h3>💡 详细解析：</h3>
                <p><strong>正确答案：B</strong></p>
                <p>供应链中的信息流分为两个方向：</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>需求信息流</strong>：从需方向供方流动，包括客户订单、<span style="color: #e74c3c; font-weight: bold;">生产计划</span>、采购合同等</li>
                    <li><strong>供应信息流</strong>：从供方向需方流动，包括入库单、完工报告单、<span style="color: #27ae60; font-weight: bold;">库存记录</span>、可供销售量、提货发运单等</li>
                </ul>
                <p>生产计划是制造商根据市场需求制定的，属于需求信息流；库存记录反映当前库存状况，属于供应信息流。</p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        let animationId;

        // 创建浮动粒子效果
        function createFloatingParticles() {
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'floating-particle';
                particle.style.left = Math.random() * window.innerWidth + 'px';
                particle.style.top = Math.random() * window.innerHeight + 'px';
                particle.style.animationDelay = Math.random() * 3 + 's';
                document.body.appendChild(particle);
            }
        }

        // 显示节点信息
        function showNodeInfo(nodeType) {
            const info = {
                supplier: '供应商：提供原材料和零部件',
                manufacturer: '制造商：生产和组装产品',
                distributor: '分销商：批发和配送产品',
                retailer: '零售商：直接销售给消费者'
            };
            
            alert(info[nodeType]);
            animateChain();
        }

        // 动画显示供应链流程
        function animateChain() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const nodes = [
                { x: 100, y: 100, name: '供应商', color: '#ff6b6b' },
                { x: 300, y: 100, name: '制造商', color: '#4834d4' },
                { x: 500, y: 100, name: '分销商', color: '#00d2d3' },
                { x: 700, y: 100, name: '零售商', color: '#ff9ff3' }
            ];

            // 绘制节点
            nodes.forEach((node, index) => {
                setTimeout(() => {
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, 40, 0, 2 * Math.PI);
                    ctx.fillStyle = node.color;
                    ctx.fill();
                    
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.name, node.x, node.y + 5);
                }, index * 500);
            });

            // 绘制连接线
            setTimeout(() => {
                for (let i = 0; i < nodes.length - 1; i++) {
                    setTimeout(() => {
                        ctx.beginPath();
                        ctx.moveTo(nodes[i].x + 40, nodes[i].y);
                        ctx.lineTo(nodes[i + 1].x - 40, nodes[i + 1].y);
                        ctx.strokeStyle = '#333';
                        ctx.lineWidth = 3;
                        ctx.stroke();
                        
                        // 绘制箭头
                        const angle = Math.atan2(0, nodes[i + 1].x - nodes[i].x);
                        const arrowX = nodes[i + 1].x - 40;
                        const arrowY = nodes[i + 1].y;
                        
                        ctx.beginPath();
                        ctx.moveTo(arrowX, arrowY);
                        ctx.lineTo(arrowX - 10, arrowY - 5);
                        ctx.lineTo(arrowX - 10, arrowY + 5);
                        ctx.closePath();
                        ctx.fillStyle = '#333';
                        ctx.fill();
                    }, i * 300);
                }
            }, 2000);
        }

        // 动画显示信息流
        function animateFlow(type, itemName) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            const startX = type === 'demand' ? 700 : 100;
            const endX = type === 'demand' ? 100 : 700;
            const y = 100;
            const color = type === 'demand' ? '#e74c3c' : '#27ae60';
            
            let currentX = startX;
            const speed = type === 'demand' ? -5 : 5;
            
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制信息包
                ctx.beginPath();
                ctx.arc(currentX, y, 20, 0, 2 * Math.PI);
                ctx.fillStyle = color;
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(itemName, currentX, y + 30);
                
                currentX += speed;
                
                if ((type === 'demand' && currentX > endX) || (type === 'supply' && currentX < endX)) {
                    animationId = requestAnimationFrame(animate);
                } else {
                    // 显示到达效果
                    ctx.beginPath();
                    ctx.arc(endX, y, 25, 0, 2 * Math.PI);
                    ctx.fillStyle = color;
                    ctx.fill();
                    ctx.strokeStyle = 'white';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                }
            }
            
            animate();
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('B. 生产计划')) {
                    option.classList.add('correct');
                }
            });
            
            document.getElementById('explanation').style.display = 'block';
            
            if (isCorrect) {
                setTimeout(() => {
                    alert('🎉 恭喜答对了！你已经掌握了供应链信息流的基本概念！');
                }, 1000);
            }
        }

        // 初始化
        window.onload = function() {
            createFloatingParticles();
            animateChain();
        };
    </script>
</body>
</html>
