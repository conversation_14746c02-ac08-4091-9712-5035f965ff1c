<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建者模式详解</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 20px auto;
            padding: 0 20px;
            background-color: #f4f7f9;
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            background: #fff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .demo-area {
            display: flex;
            flex-direction: column;
            margin: 30px 0;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
        }
        .demo-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .demo-box {
            width: 48%;
            padding: 15px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        .code-block {
            background: #f8f9fa;
            border-left: 5px solid #2ecc71;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            white-space: pre;
            overflow-x: auto;
            font-size: 14px;
        }
        .explanation {
            background: #e9f7fd;
            border-left: 5px solid #3498db;
            padding: 15px;
            margin: 20px 0;
        }
        .highlight {
            background-color: #fffacd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .btn-group {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .animation-area {
            min-height: 300px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
            background-color: #f9f9f9;
            padding: 20px;
        }
        .factory-container {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        .factory {
            width: 150px;
            height: 150px;
            background-color: #fff;
            border: 2px solid #3498db;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 10px;
            transition: all 0.3s;
        }
        .factory:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .factory-icon {
            font-size: 40px;
            margin-bottom: 10px;
        }
        .factory-name {
            font-weight: bold;
            text-align: center;
        }
        .product {
            width: 100px;
            height: 100px;
            background-color: #fff;
            border: 2px solid #2ecc71;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30px;
            position: absolute;
            opacity: 0;
            transition: all 0.5s;
        }
        .step-indicator {
            text-align: center;
            font-weight: bold;
            margin-top: 20px;
            color: #2c3e50;
            padding: 10px;
            background-color: #fff;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>

    <div class="container">
        <h1>创建者对象（Creator）详解</h1>
        
        <div class="explanation">
            <p><strong>创建者对象</strong>是设计模式中的一个重要概念，特别是在<strong>工厂方法模式</strong>中。创建者对象负责定义一个用于创建对象的接口，但让子类决定实例化哪个类。</p>
            <p>简单来说，创建者对象就是<span class="highlight">负责创建其他对象的对象</span>。它通常包含一个工厂方法，这个方法会返回新创建的对象。</p>
        </div>

        <h2>创建者对象与工厂方法模式</h2>
        
        <div class="demo-area">
            <div class="demo-row">
                <div class="demo-box">
                    <h3>创建者对象的结构</h3>
                    <pre>
Creator (创建者)
    │
    │ 定义
    ▼
factoryMethod() : Product
    │
    │ 返回
    ▼
Product (产品)
                    </pre>
                    <p>创建者对象定义了一个工厂方法，这个方法返回一个产品对象。具体的创建者子类会重写这个方法，返回具体的产品。</p>
                </div>
                <div class="demo-box">
                    <h3>代码中的体现</h3>
                    <div class="code-block">
// 抽象产品
interface Product {
    operation(): string;
}

// 抽象创建者
abstract class Creator {
    // 工厂方法
    abstract factoryMethod(): Product;
    
    // 使用工厂方法的业务逻辑
    someOperation(): string {
        // 调用工厂方法创建产品
        const product = this.factoryMethod();
        
        // 使用产品
        return `Creator: 使用 ${product.operation()}`;
    }
}</div>
                </div>
            </div>
            
            <h3>交互演示：创建者对象如何工作</h3>
            <p>点击不同的创建者，看看它们如何创建不同的产品：</p>
            
            <div class="factory-container" id="factory-container">
                <!-- 工厂会通过JavaScript动态添加 -->
            </div>
            
            <div class="animation-area" id="animation-area">
                <!-- 产品会通过JavaScript动态添加 -->
            </div>
            
            <div id="step-text" class="step-indicator">
                点击上方的创建者来创建产品
            </div>
        </div>

        <h2>详细解释</h2>
        
        <div class="explanation">
            <h3>什么是创建者对象？</h3>
            <p>创建者对象是一种设计模式中的角色，它主要有以下特点：</p>
            <ol>
                <li><strong>封装对象创建逻辑</strong>：它将对象的创建过程封装在自己的方法中，使客户端代码不需要关心具体的创建细节。</li>
                <li><strong>提供抽象接口</strong>：通常定义一个抽象方法（工厂方法），让子类实现这个方法来创建具体的对象。</li>
                <li><strong>解耦客户端与具体产品</strong>：客户端只需要与创建者和抽象产品交互，不需要知道具体产品的类。</li>
                <li><strong>支持扩展</strong>：可以通过添加新的创建者子类来创建新类型的产品，而不需要修改现有代码。</li>
            </ol>
        </div>
        
        <div class="code-block">
// 完整代码示例

// 抽象产品
interface Product {
    operation(): string;
}

// 具体产品 A
class ConcreteProductA implements Product {
    operation(): string {
        return '产品A的结果';
    }
}

// 具体产品 B
class ConcreteProductB implements Product {
    operation(): string {
        return '产品B的结果';
    }
}

// 抽象创建者
abstract class Creator {
    // 工厂方法
    abstract factoryMethod(): Product;
    
    // 使用工厂方法的业务逻辑
    someOperation(): string {
        // 调用工厂方法创建产品
        const product = this.factoryMethod();
        
        // 使用产品
        return `Creator: 使用 ${product.operation()}`;
    }
}

// 具体创建者 A
class ConcreteCreatorA extends Creator {
    factoryMethod(): Product {
        return new ConcreteProductA();
    }
}

// 具体创建者 B
class ConcreteCreatorB extends Creator {
    factoryMethod(): Product {
        return new ConcreteProductB();
    }
}

// 客户端代码
function clientCode(creator: Creator) {
    console.log(creator.someOperation());
}

// 使用
console.log('客户端使用 ConcreteCreatorA:');
clientCode(new ConcreteCreatorA());

console.log('客户端使用 ConcreteCreatorB:');
clientCode(new ConcreteCreatorB());</div>

        <h2>创建者对象与其他创建型模式的比较</h2>
        
        <table class="comparison-table">
            <tr>
                <th>模式</th>
                <th>主要特点</th>
                <th>适用场景</th>
            </tr>
            <tr>
                <td><strong>工厂方法（创建者模式）</strong></td>
                <td>定义一个创建对象的接口，但让子类决定实例化哪个类</td>
                <td>当一个类不知道它所必须创建的对象的类时；当一个类希望由它的子类来指定它所创建的对象时</td>
            </tr>
            <tr>
                <td><strong>抽象工厂</strong></td>
                <td>提供一个接口来创建一系列相关或相互依赖的对象</td>
                <td>当系统需要独立于它的产品的创建、组合和表示时；当系统要由多个产品系列中的一个来配置时</td>
            </tr>
            <tr>
                <td><strong>生成器（Builder）</strong></td>
                <td>将一个复杂对象的构建与它的表示分离</td>
                <td>当创建复杂对象的算法应该独立于该对象的组成部分以及它们的装配方式时</td>
            </tr>
            <tr>
                <td><strong>单例</strong></td>
                <td>确保一个类只有一个实例，并提供一个全局访问点</td>
                <td>当类只能有一个实例而且客户可以从一个众所周知的访问点访问它时</td>
            </tr>
        </table>

        <div class="explanation">
            <h3>总结：创建者对象的核心价值</h3>
            <ol>
                <li><strong>封装变化</strong>：将可能变化的部分（具体产品的创建）封装在子类中</li>
                <li><strong>开闭原则</strong>：符合"开放扩展，关闭修改"的原则</li>
                <li><strong>依赖倒置</strong>：高层模块依赖抽象，而不依赖具体实现</li>
                <li><strong>单一职责</strong>：将对象的创建和使用分离</li>
            </ol>
            <p>创建者对象是面向对象设计中非常重要的一个概念，掌握它可以帮助你设计出更加灵活、可扩展的系统。</p>
        </div>
    </div>

    <script>
        // 创建者和产品数据
        const creators = [
            { id: 'creator-a', name: '手机创建者', icon: '📱', productType: '手机', productIcon: '📱' },
            { id: 'creator-b', name: '电脑创建者', icon: '💻', productType: '电脑', productIcon: '💻' },
            { id: 'creator-c', name: '耳机创建者', icon: '🎧', productType: '耳机', productIcon: '🎧' },
            { id: 'creator-d', name: '手表创建者', icon: '⌚', productType: '手表', productIcon: '⌚' }
        ];
        
        // 获取容器元素
        const factoryContainer = document.getElementById('factory-container');
        const animationArea = document.getElementById('animation-area');
        const stepText = document.getElementById('step-text');
        
        // 创建工厂元素
        creators.forEach(creator => {
            const factoryElement = document.createElement('div');
            factoryElement.className = 'factory';
            factoryElement.id = creator.id;
            factoryElement.innerHTML = `
                <div class="factory-icon">${creator.icon}</div>
                <div class="factory-name">${creator.name}</div>
            `;
            
            // 添加点击事件
            factoryElement.addEventListener('click', () => {
                createProduct(creator);
            });
            
            factoryContainer.appendChild(factoryElement);
        });
        
        // 创建产品的函数
        function createProduct(creator) {
            // 清除之前的产品
            const oldProducts = document.querySelectorAll('.product');
            oldProducts.forEach(product => product.remove());
            
            // 创建新产品
            const product = document.createElement('div');
            product.className = 'product';
            product.innerHTML = creator.productIcon;
            product.style.left = '50%';
            product.style.top = '50%';
            product.style.transform = 'translate(-50%, -50%) scale(0.5)';
            product.style.opacity = '0';
            
            animationArea.appendChild(product);
            
            // 更新步骤文本
            stepText.innerHTML = `<strong>${creator.name}</strong> 正在创建产品...`;
            
            // 动画：创建产品
            setTimeout(() => {
                product.style.opacity = '1';
                product.style.transform = 'translate(-50%, -50%) scale(1)';
                
                setTimeout(() => {
                    stepText.innerHTML = `<strong>${creator.name}</strong> 创建了一个 <strong>${creator.productType}</strong> 产品！`;
                }, 500);
            }, 100);
        }
    </script>

</body>
</html> 