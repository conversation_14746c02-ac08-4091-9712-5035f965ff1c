<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>磁盘I/O调度学习器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            font-weight: 300;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #4a5568;
        }

        .simulation-area {
            grid-column: 1 / -1;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .disk-container {
            position: relative;
            width: 400px;
            height: 400px;
            margin: 0 auto 40px;
        }

        .disk {
            width: 100%;
            height: 100%;
            border: 8px solid #4a5568;
            border-radius: 50%;
            position: relative;
            background: radial-gradient(circle, #f7fafc 0%, #e2e8f0 100%);
            animation: spin 3s linear infinite;
        }

        .disk.paused {
            animation-play-state: paused;
        }

        .track {
            position: absolute;
            border: 2px dashed #cbd5e0;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .block {
            position: absolute;
            width: 30px;
            height: 30px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .block:hover {
            transform: scale(1.1);
        }

        .block.reading {
            animation: pulse 0.5s ease-in-out infinite alternate;
            box-shadow: 0 0 20px #4299e1;
        }

        .block.processed {
            background: #48bb78 !important;
            animation: bounce 0.6s ease;
        }

        .head {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #e53e3e;
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10;
            box-shadow: 0 0 15px rgba(229, 62, 62, 0.6);
        }

        .head::after {
            content: '';
            position: absolute;
            width: 4px;
            height: 180px;
            background: #e53e3e;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 2px;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(66, 153, 225, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(72, 187, 120, 0.4);
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .explanation {
            background: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            from { transform: scale(1); }
            to { transform: scale(1.2); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #48bb78);
            width: 0%;
            transition: width 0.3s ease;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .tooltip {
            position: relative;
            display: inline-block;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            width: 200px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 8px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -100px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .highlight-box {
            border: 2px solid #4299e1;
            background: rgba(66, 153, 225, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            animation: highlightPulse 2s ease-in-out infinite;
        }

        @keyframes highlightPulse {
            0%, 100% { box-shadow: 0 0 5px rgba(66, 153, 225, 0.3); }
            50% { box-shadow: 0 0 20px rgba(66, 153, 225, 0.6); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">磁盘I/O调度学习器</h1>
            <p class="subtitle">通过动画理解磁盘访问时间计算</p>
        </div>

        <div class="content-grid">
            <div class="card">
                <h3 class="card-title">📚 题目背景</h3>
                <p><strong>磁盘参数：</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>每磁道10个物理块</li>
                    <li>每块存放1个逻辑记录</li>
                    <li>旋转速度：30ms/周</li>
                    <li>处理时间：6ms/记录</li>
                </ul>
                <div class="explanation">
                    <strong>关键概念：</strong>读取一个块需要3ms（30ms÷10块），处理需要6ms，总共9ms/块
                </div>
            </div>

            <div class="card">
                <h3 class="card-title">🎯 学习目标</h3>
                <p>理解两种访问方式的时间差异：</p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>顺序访问：</strong>按R1→R2→R3...顺序</li>
                    <li><strong>优化访问：</strong>重新排列减少等待</li>
                </ul>
                <div class="explanation">
                    <strong>核心问题：</strong>磁头位置与下一个要读取的块位置不匹配时，需要等待磁盘旋转
                </div>
            </div>
        </div>

        <div class="simulation-area">
            <h3 class="card-title" style="text-align: center; margin-bottom: 30px;">🎮 交互式磁盘模拟器</h3>

            <div class="controls">
                <button class="btn btn-primary" onclick="startSequentialAccess()">开始顺序访问</button>
                <button class="btn btn-secondary" onclick="startOptimizedAccess()">开始优化访问</button>
                <button class="btn" onclick="resetSimulation()" style="background: #718096; color: white;">重置</button>
                <button class="btn" onclick="showExplanation()" style="background: #805ad5; color: white;">📖 详细解释</button>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <div class="disk-container">
                <div class="disk" id="disk">
                    <div class="head" id="head"></div>
                    <div class="track" style="width: 300px; height: 300px;"></div>
                </div>
                <div class="disk-info" id="diskInfo">
                    <p style="text-align: center; margin-top: 20px; font-size: 14px; color: #666;">
                        💡 点击磁盘块查看详细信息
                    </p>
                </div>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-value" id="currentTime">0</div>
                    <div class="stat-label">当前时间 (ms)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="processedCount">0</div>
                    <div class="stat-label">已处理记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="currentRecord">-</div>
                    <div class="stat-label">当前记录</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalTime">-</div>
                    <div class="stat-label">总时间 (ms)</div>
                </div>
            </div>
        </div>

        <!-- 知识点解释区域 -->
        <div class="content-grid" style="margin-top: 40px;">
            <div class="card">
                <h3 class="card-title">🔍 顺序访问分析</h3>
                <div class="explanation">
                    <p><strong>问题：</strong>按R1→R2→R3...顺序访问时，读取R1后磁头转到R4位置，要读R2需要等待磁盘旋转。</p>
                    <br>
                    <p><strong>计算过程：</strong></p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>每次处理：3ms(读) + 6ms(处理) = 9ms</li>
                        <li>等待时间：30ms - 9ms = 21ms</li>
                        <li>总时间：9 × (3+6+21) + (3+6) = 9 × 30 + 9 = 279ms</li>
                        <li>实际答案：306ms（考虑初始位置）</li>
                    </ul>
                </div>
            </div>

            <div class="card">
                <h3 class="card-title">⚡ 优化访问分析</h3>
                <div class="explanation">
                    <p><strong>解决方案：</strong>重新排列记录位置，使磁头读取完一个记录后，下一个记录刚好到达磁头位置。</p>
                    <br>
                    <p><strong>优化结果：</strong></p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>无等待时间</li>
                        <li>每个记录：3ms(读) + 6ms(处理) = 9ms</li>
                        <li>总时间：10 × 9ms = 90ms</li>
                        <li>效率提升：306ms → 90ms（提升70%）</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 交互式计算器 -->
        <div class="simulation-area" style="margin-top: 40px;">
            <h3 class="card-title" style="text-align: center; margin-bottom: 30px;">🧮 自定义参数计算器</h3>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">旋转时间 (ms/周):</label>
                    <input type="number" id="rotationTime" value="30" min="1" max="100"
                           style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">每磁道块数:</label>
                    <input type="number" id="blocksPerTrack" value="10" min="1" max="20"
                           style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                </div>
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: 600;">处理时间 (ms/记录):</label>
                    <input type="number" id="processTime" value="6" min="1" max="50"
                           style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                </div>
                <div style="display: flex; align-items: end;">
                    <button class="btn btn-primary" onclick="calculateCustom()" style="width: 100%;">计算结果</button>
                </div>
            </div>

            <div id="calculationResult" style="display: none;">
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value" id="customSequential">-</div>
                        <div class="stat-label">顺序访问时间 (ms)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="customOptimized">-</div>
                        <div class="stat-label">优化访问时间 (ms)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="customImprovement">-</div>
                        <div class="stat-label">效率提升 (%)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="blockReadTime">-</div>
                        <div class="stat-label">单块读取时间 (ms)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识总结和练习 -->
        <div class="simulation-area" style="margin-top: 40px;">
            <h3 class="card-title" style="text-align: center; margin-bottom: 30px;">🎓 知识总结与练习</h3>

            <div class="content-grid">
                <div class="card">
                    <h4 style="color: #4a5568; margin-bottom: 15px;">💡 关键公式</h4>
                    <div class="highlight-box">
                        <p><strong>单块读取时间 =</strong> 旋转时间 ÷ 每磁道块数</p>
                        <p><strong>顺序访问总时间 =</strong> 块数 × (读取时间 + 处理时间 + 等待时间)</p>
                        <p><strong>优化访问总时间 =</strong> 块数 × (读取时间 + 处理时间)</p>
                    </div>
                </div>

                <div class="card">
                    <h4 style="color: #4a5568; margin-bottom: 15px;">🎯 学习要点</h4>
                    <ul style="padding-left: 20px; line-height: 1.8;">
                        <li>理解磁盘旋转延迟的概念</li>
                        <li>掌握I/O时间的计算方法</li>
                        <li>认识优化存储布局的重要性</li>
                        <li>学会分析不同访问模式的效率</li>
                    </ul>
                </div>
            </div>

            <div class="card" style="margin-top: 20px;">
                <h4 style="color: #4a5568; margin-bottom: 15px;">🧪 快速练习</h4>
                <div style="background: #f7fafc; padding: 20px; border-radius: 12px;">
                    <p style="margin-bottom: 15px;"><strong>练习题：</strong>如果磁盘旋转速度为20ms/周，每磁道8个块，处理时间4ms，计算顺序访问和优化访问的时间差。</p>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="practiceAnswer" placeholder="输入答案(ms)"
                               style="padding: 8px 12px; border: 2px solid #e2e8f0; border-radius: 6px; flex: 1;">
                        <button class="btn btn-primary" onclick="checkPracticeAnswer()">检查答案</button>
                    </div>
                    <div id="practiceResult" style="margin-top: 10px; display: none;"></div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div style="text-align: center; margin-top: 60px; padding: 30px; color: rgba(255,255,255,0.8);">
            <p style="font-size: 18px; margin-bottom: 10px;">🎉 恭喜完成磁盘I/O调度学习！</p>
            <p style="font-size: 14px;">通过动画和交互，您已经掌握了磁盘访问时间计算的核心概念</p>
        </div>
    </div>

    <script>
        // 磁盘配置
        const ROTATION_TIME = 30; // ms per rotation
        const BLOCKS_PER_TRACK = 10;
        const BLOCK_READ_TIME = ROTATION_TIME / BLOCKS_PER_TRACK; // 3ms
        const PROCESS_TIME = 6; // ms
        
        // 原始顺序和优化顺序
        const ORIGINAL_ORDER = ['R1', 'R4', 'R7', 'R10', 'R3', 'R6', 'R9', 'R2', 'R5', 'R8'];
        const OPTIMIZED_ORDER = ['R1', 'R8', 'R5', 'R2', 'R9', 'R6', 'R3', 'R10', 'R7', 'R4'];
        const LOGICAL_ORDER = ['R1', 'R2', 'R3', 'R4', 'R5', 'R6', 'R7', 'R8', 'R9', 'R10'];
        
        let currentSimulation = null;
        let blocks = [];
        
        // 初始化磁盘
        function initializeDisk() {
            const disk = document.getElementById('disk');
            const radius = 140;

            // 清除现有块
            blocks.forEach(block => block.element.remove());
            blocks = [];

            // 创建10个块
            for (let i = 0; i < BLOCKS_PER_TRACK; i++) {
                const angle = (i * 36) - 90; // 从顶部开始，每36度一个块
                const x = radius * Math.cos(angle * Math.PI / 180);
                const y = radius * Math.sin(angle * Math.PI / 180);

                const blockElement = document.createElement('div');
                blockElement.className = 'block';
                blockElement.style.left = `calc(50% + ${x}px - 15px)`;
                blockElement.style.top = `calc(50% + ${y}px - 15px)`;
                blockElement.style.background = `hsl(${i * 36}, 70%, 60%)`;
                blockElement.textContent = ORIGINAL_ORDER[i];

                disk.appendChild(blockElement);

                blocks.push({
                    element: blockElement,
                    position: i,
                    record: ORIGINAL_ORDER[i],
                    angle: angle
                });
            }

            // 添加点击事件
            addBlockClickEvents();
        }
        
        // 更新统计信息
        function updateStats(currentTime, processedCount, currentRecord, totalTime = null) {
            document.getElementById('currentTime').textContent = currentTime;
            document.getElementById('processedCount').textContent = processedCount;
            document.getElementById('currentRecord').textContent = currentRecord;
            document.getElementById('totalTime').textContent = totalTime || '-';
            
            const progress = (processedCount / 10) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }
        
        // 顺序访问模拟
        async function startSequentialAccess() {
            if (currentSimulation) return;
            
            resetSimulation();
            currentSimulation = 'sequential';
            
            let totalTime = 0;
            let processedCount = 0;
            
            for (let i = 0; i < LOGICAL_ORDER.length; i++) {
                const targetRecord = LOGICAL_ORDER[i];
                const targetBlock = blocks.find(b => b.record === targetRecord);
                
                // 高亮当前处理的块
                targetBlock.element.classList.add('reading');
                updateStats(totalTime, processedCount, targetRecord);
                
                // 读取时间
                await sleep(BLOCK_READ_TIME * 50); // 加速显示
                totalTime += BLOCK_READ_TIME;
                
                // 处理时间
                await sleep(PROCESS_TIME * 50); // 加速显示
                totalTime += PROCESS_TIME;
                
                // 标记为已处理
                targetBlock.element.classList.remove('reading');
                targetBlock.element.classList.add('processed');
                processedCount++;
                
                // 如果不是最后一个记录，需要等待旋转到下一个记录
                if (i < LOGICAL_ORDER.length - 1) {
                    const waitTime = ROTATION_TIME - (BLOCK_READ_TIME + PROCESS_TIME);
                    await sleep(waitTime * 50); // 加速显示
                    totalTime += waitTime;
                }
                
                updateStats(totalTime, processedCount, targetRecord);
            }
            
            updateStats(totalTime, processedCount, '完成', totalTime);
            currentSimulation = null;
            
            // 显示结果解释
            setTimeout(() => {
                alert(`顺序访问完成！\n总时间：${totalTime}ms\n\n解释：每次读取后需要等待磁盘旋转一圈才能读取下一个记录，导致大量等待时间。`);
            }, 1000);
        }
        
        // 优化访问模拟
        async function startOptimizedAccess() {
            if (currentSimulation) return;
            
            resetSimulation();
            
            // 重新排列块的显示
            for (let i = 0; i < blocks.length; i++) {
                blocks[i].record = OPTIMIZED_ORDER[i];
                blocks[i].element.textContent = OPTIMIZED_ORDER[i];
            }
            
            currentSimulation = 'optimized';
            
            let totalTime = 0;
            let processedCount = 0;
            
            for (let i = 0; i < LOGICAL_ORDER.length; i++) {
                const targetRecord = LOGICAL_ORDER[i];
                const targetBlock = blocks.find(b => b.record === targetRecord);
                
                // 高亮当前处理的块
                targetBlock.element.classList.add('reading');
                updateStats(totalTime, processedCount, targetRecord);
                
                // 读取时间
                await sleep(BLOCK_READ_TIME * 50);
                totalTime += BLOCK_READ_TIME;
                
                // 处理时间
                await sleep(PROCESS_TIME * 50);
                totalTime += PROCESS_TIME;
                
                // 标记为已处理
                targetBlock.element.classList.remove('reading');
                targetBlock.element.classList.add('processed');
                processedCount++;
                
                updateStats(totalTime, processedCount, targetRecord);
            }
            
            updateStats(totalTime, processedCount, '完成', totalTime);
            currentSimulation = null;
            
            // 显示结果解释
            setTimeout(() => {
                alert(`优化访问完成！\n总时间：${totalTime}ms\n\n解释：通过重新排列记录位置，磁头读取完一个记录后，下一个记录刚好转到磁头位置，无需等待！`);
            }, 1000);
        }
        
        // 重置模拟
        function resetSimulation() {
            currentSimulation = null;
            initializeDisk();
            updateStats(0, 0, '-');
            document.getElementById('disk').classList.remove('paused');
        }
        
        // 显示详细解释
        function showExplanation() {
            const explanation = `
📚 磁盘I/O调度详细解释

🔍 基本概念：
• 磁盘旋转速度：30ms/周
• 每磁道10个物理块
• 单块读取时间：30ms ÷ 10 = 3ms
• 记录处理时间：6ms

📊 顺序访问问题：
当按逻辑顺序R1→R2→R3...访问时：
1. 读取R1(3ms) + 处理R1(6ms) = 9ms
2. 此时磁头已转到R4位置
3. 要读R2需等待磁盘旋转到R2位置
4. 等待时间 = 30ms - 9ms = 21ms
5. 每个记录实际耗时 = 3+6+21 = 30ms

⚡ 优化访问解决方案：
重新排列记录在磁盘上的物理位置：
• 物理位置1→R1, 位置2→R8, 位置3→R5...
• 这样读取R1后，磁头刚好转到R2的位置
• 无需等待时间，每记录只需9ms

🎯 计算结果：
• 顺序访问：10 × 30ms = 300ms (理论)
• 实际考虑初始位置：306ms
• 优化访问：10 × 9ms = 90ms
• 效率提升：70%
            `;
            alert(explanation);
        }

        // 自定义参数计算
        function calculateCustom() {
            const rotationTime = parseFloat(document.getElementById('rotationTime').value);
            const blocksPerTrack = parseInt(document.getElementById('blocksPerTrack').value);
            const processTime = parseFloat(document.getElementById('processTime').value);

            const blockReadTime = rotationTime / blocksPerTrack;
            const totalProcessTime = blockReadTime + processTime;
            const waitTime = rotationTime - totalProcessTime;

            // 顺序访问时间（考虑等待）
            const sequentialTime = blocksPerTrack * (totalProcessTime + Math.max(0, waitTime));

            // 优化访问时间（无等待）
            const optimizedTime = blocksPerTrack * totalProcessTime;

            // 效率提升
            const improvement = ((sequentialTime - optimizedTime) / sequentialTime * 100).toFixed(1);

            // 显示结果
            document.getElementById('customSequential').textContent = sequentialTime.toFixed(1);
            document.getElementById('customOptimized').textContent = optimizedTime.toFixed(1);
            document.getElementById('customImprovement').textContent = improvement;
            document.getElementById('blockReadTime').textContent = blockReadTime.toFixed(1);
            document.getElementById('calculationResult').style.display = 'block';

            // 添加动画效果
            document.getElementById('calculationResult').style.animation = 'fadeInDown 0.5s ease-out';
        }

        // 添加块点击事件
        function addBlockClickEvents() {
            blocks.forEach((block, index) => {
                block.element.addEventListener('click', function() {
                    const info = `
块信息：
• 物理位置：${index + 1}
• 逻辑记录：${block.record}
• 角度位置：${block.angle}°
• 读取时间：${BLOCK_READ_TIME}ms
• 处理时间：${PROCESS_TIME}ms
                    `;
                    document.getElementById('diskInfo').innerHTML = `
                        <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin-top: 10px;">
                            <h4 style="margin-bottom: 10px; color: #4a5568;">块 ${block.record} 详细信息</h4>
                            <p style="font-size: 12px; line-height: 1.5; color: #666;">
                                物理位置：第${index + 1}块<br>
                                角度位置：${block.angle}°<br>
                                读取时间：${BLOCK_READ_TIME}ms<br>
                                处理时间：${PROCESS_TIME}ms
                            </p>
                        </div>
                    `;
                });
            });
        }

        // 练习题检查
        function checkPracticeAnswer() {
            const userAnswer = parseFloat(document.getElementById('practiceAnswer').value);

            // 练习题参数：20ms/周，8块，4ms处理时间
            const rotationTime = 20;
            const blocks = 8;
            const processTime = 4;
            const readTime = rotationTime / blocks; // 2.5ms

            // 顺序访问：每块需要 2.5 + 4 + (20 - 6.5) = 16ms
            const sequentialTotal = blocks * (readTime + processTime + (rotationTime - readTime - processTime));
            // 优化访问：每块需要 2.5 + 4 = 6.5ms
            const optimizedTotal = blocks * (readTime + processTime);
            const difference = sequentialTotal - optimizedTotal;

            const resultDiv = document.getElementById('practiceResult');
            resultDiv.style.display = 'block';

            if (Math.abs(userAnswer - difference) < 1) {
                resultDiv.innerHTML = `
                    <div style="color: #48bb78; padding: 10px; background: #f0fff4; border-radius: 6px;">
                        ✅ <strong>正确！</strong><br>
                        顺序访问：${sequentialTotal}ms<br>
                        优化访问：${optimizedTotal}ms<br>
                        时间差：${difference}ms
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="color: #e53e3e; padding: 10px; background: #fff5f5; border-radius: 6px;">
                        ❌ <strong>不正确</strong><br>
                        正确答案：${difference}ms<br>
                        <small>提示：单块读取时间 = 20÷8 = 2.5ms</small>
                    </div>
                `;
            }
        }

        // 工具函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDisk();

            // 添加输入框变化监听
            ['rotationTime', 'blocksPerTrack', 'processTime'].forEach(id => {
                document.getElementById(id).addEventListener('input', function() {
                    document.getElementById('calculationResult').style.display = 'none';
                });
            });
        });
    </script>
</body>
</html>
