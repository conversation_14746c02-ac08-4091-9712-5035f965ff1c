<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>顺序批处理模拟</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #2980b9;
            margin-top: 30px;
        }
        .simulation {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .job-list {
            display: flex;
            margin-bottom: 20px;
            gap: 10px;
        }
        .job {
            padding: 10px;
            background-color: #e74c3c;
            color: white;
            border-radius: 4px;
            text-align: center;
            width: 80px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .job.selected {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .processor {
            height: 100px;
            background-color: #3498db;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 18px;
            position: relative;
        }
        .output {
            min-height: 100px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background-color: #fff;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #2ecc71;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #27ae60;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .job-queue {
            display: flex;
            min-height: 60px;
            background-color: #ecf0f1;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 20px;
            align-items: center;
            gap: 10px;
        }
        .status {
            margin-top: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
        .explanation {
            background-color: #f0f8ff;
            padding: 15px;
            border-left: 4px solid #3498db;
            margin: 20px 0;
        }
        .workflow-simulation {
            display: flex;
            align-items: center;
            justify-content: space-around;
            margin-top: 20px;
            text-align: center;
            flex-wrap: wrap;
        }
        .workflow-simulation .step {
            width: 30%;
            min-width: 150px;
            margin-bottom: 20px;
        }
        .workflow-simulation .arrow {
            font-size: 2em;
            color: #3498db;
            margin: 0 10px;
        }
        .punch-card-stack {
            position: relative;
            height: 60px;
            width: 80px;
            margin: 10px auto;
        }
        .punch-card {
            position: absolute;
            width: 100%;
            height: 40px;
            background-color: #f5deb3;
            border: 1px solid #d2b48c;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            box-shadow: 2px 2px 5px rgba(0,0,0,0.1);
        }
        .punch-card:nth-child(1) { top: 0; left: 0; transform: rotate(-5deg); }
        .punch-card:nth-child(2) { top: 5px; left: 0; transform: rotate(2deg); z-index: 1; }
        .punch-card:nth-child(3) { top: 10px; left: 0; transform: rotate(-3deg); z-index: 2; }
        .operator, .computer {
            font-size: 3em;
            line-height: 1;
        }

        /* Compiler Simulation Styles */
        #compiler-pipeline {
            display: flex;
            justify-content: space-between;
            position: relative;
            height: 80px;
            align-items: center;
            padding: 0 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
            margin-top: 20px;
        }
        .pipeline-stage {
            border: 2px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            color: #495057;
            background-color: white;
            flex-basis: 22%;
            transition: background-color 0.5s, border-color 0.5s, font-weight 0.5s;
        }
        .pipeline-stage.active {
            background-color: #d1e7dd;
            border-color: #2ecc71;
            font-weight: bold;
        }
        #source-code {
            position: absolute;
            top: 50%;
            left: -100px; /* Start off-screen */
            transform: translateY(-50%);
            padding: 10px 15px;
            background-color: #e74c3c;
            color: white;
            border-radius: 5px;
            font-weight: bold;
            transition: all 1s ease-in-out;
            white-space: nowrap;
            opacity: 0;
        }

        .game-container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .game-status {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .game-area {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .job-pool, .game-queue {
            min-height: 150px;
            border: 2px dashed #3498db;
            border-radius: 8px;
            padding: 10px;
            background-color: #f8f9fa;
        }
        
        .game-job {
            background-color: #3498db;
            color: white;
            padding: 10px;
            margin: 5px;
            border-radius: 4px;
            cursor: move;
            user-select: none;
            display: inline-block;
        }
        
        .game-job.priority-high {
            background-color: #e74c3c;
        }
        
        .game-job.priority-medium {
            background-color: #f39c12;
        }
        
        .game-job.priority-low {
            background-color: #2ecc71;
        }
        
        .game-controls {
            text-align: center;
            margin-top: 20px;
        }
        
        .game-feedback {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        
        .game-feedback.success {
            background-color: #d4edda;
            color: #155724;
            display: block;
        }
        
        .game-feedback.error {
            background-color: #f8d7da;
            color: #721c24;
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>顺序批处理模拟</h1>
        
        <div class="explanation">
            <h2>什么是顺序批处理？</h2>
            <p>顺序批处理是早期计算机操作系统使用的一种作业调度方式。在这种模式下，计算机按照作业提交的顺序依次处理每个作业，一次只处理一个作业，当前作业完成后才开始下一个作业。</p>
            
            <h2>顺序批处理的特点：</h2>
            <ul>
                <li><strong>单道处理：</strong>一次只处理一个作业</li>
                <li><strong>按顺序执行：</strong>严格按照作业进入系统的顺序处理</li>
                <li><strong>资源独占：</strong>当前作业独占所有系统资源</li>
                <li><strong>自动连续：</strong>一个作业完成后自动执行下一个作业</li>
            </ul>
            
            <h2>优点：</h2>
            <ul>
                <li>实现简单，系统开销小</li>
                <li>无需复杂的调度算法</li>
                <li>作业之间不会互相干扰</li>
            </ul>
            
            <h2>缺点：</h2>
            <ul>
                <li>CPU利用率低，资源浪费严重</li>
                <li>平均周转时间长</li>
                <li>无法处理紧急任务</li>
                <li>用户交互性差</li>
            </ul>
        </div>

        <div class="explanation">
            <h2>为什么早期计算机会选择批处理？</h2>
            <p>在计算机发展的早期，硬件资源（如CPU、内存）非常昂贵和有限。为了最大化利用这些宝贵的资源，批处理应运而生。它的核心思想是：<strong>让机器不停地工作，减少人工干预的等待时间。</strong></p>
            
            <h4>早期工作流程模拟：</h4>
            <div class="workflow-simulation">
                <div class="step">
                    <strong>1. 准备作业 (打孔卡片)</strong>
                    <div class="punch-card-stack">
                        <div class="punch-card">作业A</div>
                        <div class="punch-card">作业B</div>
                        <div class="punch-card">作业C</div>
                    </div>
                    <p>程序员将程序和数据制作成一叠打孔卡片。</p>
                </div>
                <div class="arrow">➔</div>
                <div class="step">
                    <strong>2. 提交批处理作业</strong>
                    <div class="operator">👨‍💻<br>操作员</div>
                    <p>操作员收集多个程序员的卡片，组成一个"批次"。</p>
                </div>
                <div class="arrow">➔</div>
                <div class="step">
                    <strong>3. 机器自动处理</strong>
                    <div class="computer">🖥️<br>计算机</div>
                    <p>计算机按顺序自动读取和执行批次中的所有作业，无需停顿。</p>
                </div>
            </div>
            
            <p style="margin-top: 15px;">这种方式虽然对单个用户不友好（需要长时间等待结果），但它能让昂贵的计算机几乎不停机地运行，在当时极大地提高了计算效率。</p>
        </div>

        <div class="explanation">
            <h2>再举一个例子：编译器的工作过程</h2>
            <p>您提供的图片描述了一个非常经典的批处理应用：<strong>传统编译器</strong>。我们可以把它想象成一条流水线，您的源代码就是那个等待被加工的"产品"。</p>
            <p>这条流水线有多个"工站"（处理模块），比如词法分析、语法分析等。您的整个源代码文件会作为一个"批次"，从第一个工站开始，完整地处理完，再被整体传递给下一个工站，直到最后生产出可执行文件。中间过程是自动连续的，完全符合顺序批处理的模式。</p>
            
            <h4>编译器流水线模拟：</h4>
            <div id="compiler-pipeline">
                <div class="pipeline-stage" data-stage="1">词法分析</div>
                <div class="pipeline-stage" data-stage="2">语法分析</div>
                <div class="pipeline-stage" data-stage="3">语义分析</div>
                <div class="pipeline-stage" data-stage="4">代码生成</div>
                <div id="source-code">源代码</div>
            </div>
            <div class="controls" style="justify-content: center; margin-top: 20px;">
                <button id="start-compilation">开始编译</button>
            </div>
            <div id="compiler-status" style="text-align:center; margin-top: 10px; font-weight: bold; color: #2c3e50;"></div>
        </div>

        <div class="explanation">
            <h2>顺序批处理调度员游戏</h2>
            <p>在这个游戏中,你将扮演一名计算机操作员,负责安排作业的执行顺序。你的目标是在有限时间内尽可能高效地处理所有作业。</p>
            
            <div class="game-container">
                <div class="game-status">
                    <div class="timer">剩余时间: <span id="game-timer">60</span>秒</div>
                    <div class="score">得分: <span id="game-score">0</span></div>
                </div>
                
                <div class="game-area">
                    <div class="incoming-jobs">
                        <h3>待处理作业:</h3>
                        <div id="job-pool" class="job-pool"></div>
                    </div>
                    
                    <div class="processing-area">
                        <h3>处理队列:</h3>
                        <div id="game-queue" class="game-queue"></div>
                    </div>
                </div>
                
                <div class="game-controls">
                    <button id="start-game">开始游戏</button>
                    <button id="submit-schedule">提交安排</button>
                </div>
                
                <div id="game-feedback" class="game-feedback"></div>
            </div>
        </div>

        <h2>顺序批处理模拟操作</h2>
        <p>下面是一个简单的模拟，展示顺序批处理如何工作。点击"添加作业"按钮添加作业到队列，然后点击"开始处理"按钮观察系统如何按顺序处理这些作业。</p>
        
        <div class="simulation">
            <h3>可选作业：</h3>
            <div class="job-list">
                <div class="job" data-time="3" data-name="作业A">作业A<br>(3秒)</div>
                <div class="job" data-time="5" data-name="作业B">作业B<br>(5秒)</div>
                <div class="job" data-time="2" data-name="作业C">作业C<br>(2秒)</div>
                <div class="job" data-time="7" data-name="作业D">作业D<br>(7秒)</div>
                <div class="job" data-time="1" data-name="作业E">作业E<br>(1秒)</div>
            </div>
            
            <h3>作业队列：</h3>
            <div class="job-queue" id="job-queue">
                <span id="empty-queue">队列为空，请添加作业</span>
            </div>
            
            <div class="controls">
                <button id="add-job">添加选中作业</button>
                <button id="start-processing">开始处理</button>
                <button id="reset">重置模拟</button>
            </div>
            
            <h3>处理器：</h3>
            <div class="processor" id="processor">
                空闲
            </div>
            
            <h3>输出结果：</h3>
            <div class="output" id="output"></div>
            
            <div class="status" id="status"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const jobs = document.querySelectorAll('.job');
            const jobQueue = document.getElementById('job-queue');
            const addJobBtn = document.getElementById('add-job');
            const startBtn = document.getElementById('start-processing');
            const resetBtn = document.getElementById('reset');
            const processor = document.getElementById('processor');
            const output = document.getElementById('output');
            const status = document.getElementById('status');
            const emptyQueue = document.getElementById('empty-queue');
            
            let selectedJob = null;
            let isProcessing = false;
            let totalTime = 0;
            let completedJobs = 0;
            
            // 选择作业
            jobs.forEach(job => {
                job.addEventListener('click', function() {
                    if (isProcessing) return;
                    
                    jobs.forEach(j => j.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedJob = this;
                });
            });
            
            // 添加作业到队列
            addJobBtn.addEventListener('click', function() {
                if (isProcessing || !selectedJob) return;
                
                const jobClone = selectedJob.cloneNode(true);
                jobClone.classList.remove('selected');
                
                if (emptyQueue && emptyQueue.parentNode === jobQueue) {
                    jobQueue.removeChild(emptyQueue);
                }
                
                jobQueue.appendChild(jobClone);
                selectedJob.classList.remove('selected');
                selectedJob = null;
            });
            
            // 开始处理作业
            startBtn.addEventListener('click', function() {
                if (isProcessing || jobQueue.children.length === 0 || jobQueue.children[0].id === 'empty-queue') {
                    return;
                }
                
                isProcessing = true;
                addJobBtn.disabled = true;
                startBtn.disabled = true;
                
                processNextJob();
            });
            
            // 处理下一个作业
            function processNextJob() {
                if (jobQueue.children.length === 0) {
                    finishProcessing();
                    return;
                }
                
                const job = jobQueue.children[0];
                const jobName = job.getAttribute('data-name');
                const jobTime = parseInt(job.getAttribute('data-time'), 10);
                
                // 更新处理器状态
                processor.textContent = `正在处理: ${jobName} (剩余 ${jobTime} 秒)`;
                processor.style.backgroundColor = job.style.backgroundColor || '#3498db';
                
                // 记录开始时间
                const startTime = totalTime;
                totalTime += jobTime;
                
                // 模拟作业处理
                let remainingTime = jobTime;
                const timer = setInterval(() => {
                    remainingTime--;
                    processor.textContent = `正在处理: ${jobName} (剩余 ${remainingTime} 秒)`;
                    
                    if (remainingTime <= 0) {
                        clearInterval(timer);
                        
                        // 记录完成情况
                        completedJobs++;
                        output.innerHTML += `<p>${jobName} 完成！开始时间: ${startTime}秒, 结束时间: ${totalTime}秒, 用时: ${jobTime}秒</p>`;
                        
                        // 从队列中移除作业
                        jobQueue.removeChild(job);
                        
                        // 处理下一个作业
                        processNextJob();
                    }
                }, 1000);
            }
            
            // 完成所有作业
            function finishProcessing() {
                isProcessing = false;
                processor.textContent = '空闲';
                processor.style.backgroundColor = '#3498db';
                
                status.textContent = `所有作业处理完成！总耗时: ${totalTime}秒, 平均周转时间: ${(totalTime / completedJobs).toFixed(2)}秒`;
                
                if (jobQueue.children.length === 0) {
                    jobQueue.appendChild(emptyQueue);
                }
                
                addJobBtn.disabled = false;
                startBtn.disabled = false;
            }
            
            // 重置模拟
            resetBtn.addEventListener('click', function() {
                if (isProcessing) return;
                
                while (jobQueue.firstChild) {
                    jobQueue.removeChild(jobQueue.firstChild);
                }
                
                jobQueue.appendChild(emptyQueue);
                output.innerHTML = '';
                status.textContent = '';
                processor.textContent = '空闲';
                processor.style.backgroundColor = '#3498db';
                
                totalTime = 0;
                completedJobs = 0;
                
                jobs.forEach(job => job.classList.remove('selected'));
                selectedJob = null;
                
                addJobBtn.disabled = false;
                startBtn.disabled = false;
            });

            // Compiler simulation logic
            const startCompilationBtn = document.getElementById('start-compilation');
            const sourceCode = document.getElementById('source-code');
            const stages = document.querySelectorAll('.pipeline-stage');
            const compilerStatus = document.getElementById('compiler-status');
            let isCompiling = false;

            startCompilationBtn.addEventListener('click', () => {
                if (isCompiling) return;
                isCompiling = true;
                startCompilationBtn.disabled = true;

                // Reset styles
                stages.forEach(s => s.classList.remove('active'));
                sourceCode.style.left = '-100px';
                sourceCode.style.opacity = '0';
                sourceCode.textContent = '源代码';
                sourceCode.style.backgroundColor = '#e74c3c';
                compilerStatus.textContent = '';

                let currentStage = -1;
                
                const processStage = () => {
                    if (currentStage >= 0 && currentStage < stages.length) {
                        stages[currentStage].classList.remove('active');
                    }

                    currentStage++;

                    if (currentStage < stages.length) {
                        const stage = stages[currentStage];
                        stage.classList.add('active');
                        compilerStatus.textContent = `处理中: ${stage.textContent}`;
                        
                        const stageRect = stage.getBoundingClientRect();
                        const pipelineRect = sourceCode.parentElement.getBoundingClientRect();
                        const targetX = stageRect.left - pipelineRect.left + (stageRect.width / 2) - (sourceCode.offsetWidth / 2);
                        
                        sourceCode.style.left = `${targetX}px`;
                        
                        setTimeout(processStage, 2000);
                    } else {
                        // Compilation finished
                        compilerStatus.textContent = "编译完成！";
                        sourceCode.textContent = '目标代码';
                        sourceCode.style.backgroundColor = '#3498db';
                        isCompiling = false;
                        startCompilationBtn.disabled = false;
                    }
                };

                compilerStatus.textContent = "编译开始...";
                sourceCode.style.opacity = '1';
                setTimeout(processStage, 500);
            });

            // 游戏相关的JavaScript代码
            const gameTimer = document.getElementById('game-timer');
            const gameScore = document.getElementById('game-score');
            const jobPool = document.getElementById('job-pool');
            const gameQueue = document.getElementById('game-queue');
            const startGameBtn = document.getElementById('start-game');
            const submitScheduleBtn = document.getElementById('submit-schedule');
            const gameFeedback = document.getElementById('game-feedback');
            
            let timer;
            let score = 0;
            let isGameRunning = false;
            
            // 生成随机作业
            function generateJob() {
                const priorities = ['high', 'medium', 'low'];
                const priority = priorities[Math.floor(Math.random() * priorities.length)];
                const executionTime = Math.floor(Math.random() * 8) + 1;
                const jobId = Math.random().toString(36).substr(2, 9);
                
                const jobElement = document.createElement('div');
                jobElement.className = `game-job priority-${priority}`;
                jobElement.draggable = true;
                jobElement.dataset.jobId = jobId;
                jobElement.dataset.executionTime = executionTime;
                jobElement.dataset.priority = priority;
                jobElement.textContent = `作业 ${jobId.substr(0, 3)} (${executionTime}秒)`;
                
                return jobElement;
            }
            
            // 初始化游戏
            function initGame() {
                jobPool.innerHTML = '';
                gameQueue.innerHTML = '';
                score = 0;
                gameScore.textContent = score;
                gameTimer.textContent = '60';
                
                // 生成初始作业
                for (let i = 0; i < 5; i++) {
                    jobPool.appendChild(generateJob());
                }
                
                // 启动定时器
                let timeLeft = 60;
                timer = setInterval(() => {
                    timeLeft--;
                    gameTimer.textContent = timeLeft;
                    
                    if (timeLeft <= 0) {
                        endGame();
                    }
                }, 1000);
                
                isGameRunning = true;
                setupDragAndDrop();
            }
            
            // 设置拖放功能
            function setupDragAndDrop() {
                const jobs = document.querySelectorAll('.game-job');
                
                jobs.forEach(job => {
                    job.addEventListener('dragstart', (e) => {
                        e.dataTransfer.setData('text/plain', job.dataset.jobId);
                    });
                });
                
                [jobPool, gameQueue].forEach(container => {
                    container.addEventListener('dragover', (e) => {
                        e.preventDefault();
                    });
                    
                    container.addEventListener('drop', (e) => {
                        e.preventDefault();
                        const jobId = e.dataTransfer.getData('text/plain');
                        const job = document.querySelector(`[data-job-id="${jobId}"]`);
                        
                        if (job) {
                            container.appendChild(job);
                        }
                    });
                });
            }
            
            // 计算得分
            function calculateScore() {
                const jobs = Array.from(gameQueue.children);
                let totalTurnaroundTime = 0;
                let currentTime = 0;
                
                jobs.forEach(job => {
                    const executionTime = parseInt(job.dataset.executionTime);
                    const priority = job.dataset.priority;
                    
                    currentTime += executionTime;
                    totalTurnaroundTime += currentTime;
                    
                    // 根据优先级给予额外分数
                    switch(priority) {
                        case 'high':
                            score += 100;
                            break;
                        case 'medium':
                            score += 50;
                            break;
                        case 'low':
                            score += 25;
                            break;
                    }
                });
                
                // 根据平均周转时间计算基础分数
                const avgTurnaroundTime = totalTurnaroundTime / jobs.length;
                score = Math.max(0, score - Math.floor(avgTurnaroundTime * 10));
                
                return score;
            }
            
            // 结束游戏
            function endGame() {
                clearInterval(timer);
                isGameRunning = false;
                
                const finalScore = calculateScore();
                gameScore.textContent = finalScore;
                
                gameFeedback.className = 'game-feedback success';
                gameFeedback.textContent = `游戏结束！最终得分: ${finalScore}。`;
                
                startGameBtn.disabled = false;
                submitScheduleBtn.disabled = true;
            }
            
            // 事件监听器
            startGameBtn.addEventListener('click', () => {
                if (!isGameRunning) {
                    startGameBtn.disabled = true;
                    submitScheduleBtn.disabled = false;
                    gameFeedback.style.display = 'none';
                    initGame();
                }
            });
            
            submitScheduleBtn.addEventListener('click', () => {
                if (isGameRunning) {
                    endGame();
                }
            });
        });
    </script>
</body>
</html> 