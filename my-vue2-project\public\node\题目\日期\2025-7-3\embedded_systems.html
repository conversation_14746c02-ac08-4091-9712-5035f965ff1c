<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式系统与片上系统(SoC)对比</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f7f9fc;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 8px;
            background-color: #f0f8ff;
        }
        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: white;
        }
        .button-container {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .component-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f4f8;
            border-radius: 8px;
            min-height: 80px;
        }
        .quiz-container {
            margin-top: 30px;
            padding: 20px;
            background-color: #ebf5fb;
            border-radius: 8px;
        }
        .quiz-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-top: 15px;
        }
        .quiz-option {
            padding: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            cursor: pointer;
        }
        .quiz-option:hover {
            background-color: #f0f0f0;
        }
        .correct {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .incorrect {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            display: none;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: center;
        }
        .comparison-table th {
            background-color: #3498db;
            color: white;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>嵌入式系统与片上系统(SoC)对比</h1>
        
        <div class="section">
            <h2>传统嵌入式系统 vs 片上系统(SoC)</h2>
            <p>在嵌入式开发领域，我们有两种主要的系统结构：传统的嵌入式系统和片上系统(SoC)。它们在设计、功能和应用上有着明显的区别。</p>
            
            <div class="canvas-container">
                <canvas id="systemCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="button-container">
                <button id="showEmbedded">显示传统嵌入式系统</button>
                <button id="showSoC">显示片上系统(SoC)</button>
                <button id="showComparison">对比两种系统</button>
            </div>
            
            <div class="component-info" id="componentInfo">
                点击上方按钮查看系统结构，然后点击图中组件可查看详细信息。
            </div>
        </div>
        
        <div class="section">
            <h2>关键区别</h2>
            <table class="comparison-table">
                <tr>
                    <th>特性</th>
                    <th>传统嵌入式系统</th>
                    <th>片上系统(SoC)</th>
                </tr>
                <tr>
                    <td>集成度</td>
                    <td>各组件独立，分布在不同芯片上</td>
                    <td>高度集成，主要组件在单个芯片上</td>
                </tr>
                <tr>
                    <td>体积</td>
                    <td>较大</td>
                    <td>小型化</td>
                </tr>
                <tr>
                    <td>功耗</td>
                    <td>较高</td>
                    <td>较低</td>
                </tr>
                <tr>
                    <td>硬件配置</td>
                    <td>物理连接，难以更改</td>
                    <td>可通过编程配置硬件功能</td>
                </tr>
                <tr>
                    <td>主要应用</td>
                    <td>传统工业控制、专用设备</td>
                    <td>智能手机、平板电脑、智能家居设备</td>
                </tr>
            </table>
        </div>
        
        <div class="quiz-container">
            <h2>知识检验</h2>
            <p>在传统嵌入式系统的硬件组成中，下列哪一项不是其主要组成部分？</p>
            <div class="quiz-options">
                <div class="quiz-option" data-correct="false">微处理器(MCU/MPU)</div>
                <div class="quiz-option" data-correct="false">存储器(RAM/ROM)</div>
                <div class="quiz-option" data-correct="true">片上系统(SoC)</div>
                <div class="quiz-option" data-correct="false">输入/输出接口</div>
            </div>
            <div class="result" id="quizResult"></div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('systemCanvas');
        const ctx = canvas.getContext('2d');
        const componentInfo = document.getElementById('componentInfo');
        const quizOptions = document.querySelectorAll('.quiz-option');
        const quizResult = document.getElementById('quizResult');
        
        // 定义组件及其位置信息
        let activeComponents = [];
        let currentSystem = '';
        
        // 传统嵌入式系统组件
        const embeddedComponents = [
            { x: 100, y: 80, width: 150, height: 80, color: '#3498db', name: '微处理器(MCU/MPU)', 
              info: '微处理器是嵌入式系统的大脑，负责执行指令和控制系统运行。' },
            { x: 350, y: 80, width: 150, height: 80, color: '#e74c3c', name: 'ROM/Flash', 
              info: '只读存储器存储系统固件和程序代码，掉电不丢失数据。' },
            { x: 600, y: 80, width: 150, height: 80, color: '#2ecc71', name: 'RAM', 
              info: '随机存取存储器用于存储运行时数据，掉电后数据丢失。' },
            { x: 100, y: 230, width: 150, height: 80, color: '#f1c40f', name: 'I/O接口', 
              info: '输入输出接口连接外部设备，如传感器、显示器等。' },
            { x: 350, y: 230, width: 150, height: 80, color: '#9b59b6', name: '总线', 
              info: '总线用于连接各个组件，传输数据、地址和控制信号。' },
            { x: 600, y: 230, width: 150, height: 80, color: '#e67e22', name: '外部设备', 
              info: '如LED、按钮、LCD显示器、通信模块等外部连接设备。' }
        ];
        
        // 片上系统组件
        const socComponents = [
            { x: 350, y: 100, width: 400, height: 250, color: '#34495e', name: '片上系统(SoC)', 
              info: '片上系统将多个组件集成到单个芯片中，提高集成度和性能。' },
            { x: 380, y: 130, width: 100, height: 60, color: '#3498db', name: 'CPU核心', 
              info: '中央处理单元，负责执行程序指令。' },
            { x: 500, y: 130, width: 100, height: 60, color: '#e74c3c', name: 'GPU', 
              info: '图形处理单元，加速图形渲染和显示。' },
            { x: 620, y: 130, width: 100, height: 60, color: '#2ecc71', name: 'DSP', 
              info: '数字信号处理器，用于处理音频和视频等信号。' },
            { x: 380, y: 210, width: 100, height: 60, color: '#f1c40f', name: '内置存储器', 
              info: '集成在芯片内部的RAM和ROM，减少外部连接。' },
            { x: 500, y: 210, width: 100, height: 60, color: '#9b59b6', name: 'I/O控制器', 
              info: '管理各种输入输出接口，如USB、UART等。' },
            { x: 620, y: 210, width: 100, height: 60, color: '#e67e22', name: '可编程逻辑', 
              info: '可以通过软件配置的硬件逻辑单元，增加灵活性。' },
            { x: 100, y: 180, width: 150, height: 80, color: '#95a5a6', name: '外部设备', 
              info: '连接到SoC的外部设备，数量比传统嵌入式系统少。' }
        ];

        // 绘制系统
        function drawSystem(components) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制组件
            components.forEach(comp => {
                ctx.fillStyle = comp.color;
                ctx.fillRect(comp.x, comp.y, comp.width, comp.height);
                
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                // 处理多行文本
                const words = comp.name.split(' ');
                if (words.length > 1 && comp.width < 200) {
                    ctx.fillText(words[0], comp.x + comp.width/2, comp.y + comp.height/2 - 10);
                    ctx.fillText(words[1], comp.x + comp.width/2, comp.y + comp.height/2 + 10);
                } else {
                    ctx.fillText(comp.name, comp.x + comp.width/2, comp.y + comp.height/2);
                }
            });
            
            // 移除这里对动画函数的直接调用，避免递归
            // 在系统切换时单独启动动画
        }
        
        // 显示传统嵌入式系统
        document.getElementById('showEmbedded').addEventListener('click', function() {
            currentSystem = 'embedded';
            activeComponents = embeddedComponents;
            drawSystem(embeddedComponents);
            componentInfo.textContent = '传统嵌入式系统由分离的硬件组件组成，包括微处理器、存储器、I/O接口等，通过总线相互连接。点击各组件查看详情。';
            
            // 停止之前的动画并启动新动画
            stopAllAnimations();
            animateDataFlow();
        });
        
        // 显示片上系统
        document.getElementById('showSoC').addEventListener('click', function() {
            currentSystem = 'soc';
            activeComponents = socComponents;
            drawSystem(socComponents);
            componentInfo.textContent = '片上系统(SoC)将主要组件集成在单个芯片上，具有高集成度、低功耗等特点。点击各组件查看详情。';
            
            // 停止之前的动画并启动新动画
            stopAllAnimations();
            animateSoCIntegration();
        });
        
        // 显示对比
        document.getElementById('showComparison').addEventListener('click', function() {
            currentSystem = 'comparison';
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = '#333';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('传统嵌入式系统', 200, 40);
            ctx.fillText('片上系统(SoC)', 600, 40);
            
            // 绘制简化版的两种系统
            // 传统嵌入式系统
            ctx.fillStyle = '#3498db';
            ctx.fillRect(120, 80, 80, 80);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('MCU', 160, 120);
            
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(220, 80, 60, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('ROM', 250, 120);
            
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(300, 80, 60, 80);
            ctx.fillStyle = 'white';
            ctx.fillText('RAM', 330, 120);
            
            ctx.fillStyle = '#9b59b6';
            ctx.fillRect(200, 200, 100, 20);
            ctx.fillStyle = 'white';
            ctx.fillText('总线', 250, 210);
            
            ctx.fillStyle = '#f1c40f';
            ctx.fillRect(120, 250, 80, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('I/O', 160, 280);
            
            ctx.fillStyle = '#e67e22';
            ctx.fillRect(220, 250, 140, 60);
            ctx.fillStyle = 'white';
            ctx.fillText('外部设备', 290, 280);
            
            // 连接线
            ctx.strokeStyle = '#7f8c8d';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(160, 160);
            ctx.lineTo(160, 200);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(250, 160);
            ctx.lineTo(250, 200);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(330, 160);
            ctx.lineTo(330, 200);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(160, 220);
            ctx.lineTo(160, 250);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(290, 220);
            ctx.lineTo(290, 250);
            ctx.stroke();
            
            // 片上系统
            ctx.fillStyle = '#34495e';
            ctx.fillRect(500, 100, 200, 180);
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.fillText('片上系统(SoC)', 600, 140);
            
            ctx.fillStyle = '#3498db';
            ctx.fillRect(520, 160, 50, 40);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('CPU', 545, 180);
            
            ctx.fillStyle = '#e74c3c';
            ctx.fillRect(580, 160, 50, 40);
            ctx.fillStyle = 'white';
            ctx.fillText('GPU', 605, 180);
            
            ctx.fillStyle = '#2ecc71';
            ctx.fillRect(640, 160, 50, 40);
            ctx.fillStyle = 'white';
            ctx.fillText('DSP', 665, 180);
            
            ctx.fillStyle = '#f1c40f';
            ctx.fillRect(520, 210, 80, 30);
            ctx.fillStyle = 'white';
            ctx.fillText('存储器', 560, 225);
            
            ctx.fillStyle = '#9b59b6';
            ctx.fillRect(610, 210, 80, 30);
            ctx.fillStyle = 'white';
            ctx.fillText('I/O控制器', 650, 225);
            
            ctx.fillStyle = '#e67e22';
            ctx.fillRect(500, 300, 200, 40);
            ctx.fillStyle = 'white';
            ctx.fillText('少量外部设备', 600, 320);
            
            // 连接线
            ctx.strokeStyle = '#7f8c8d';
            ctx.beginPath();
            ctx.moveTo(600, 280);
            ctx.lineTo(600, 300);
            ctx.stroke();
            
            // 对比标签
            ctx.fillStyle = '#333';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('多芯片、体积大', 200, 350);
            ctx.fillText('单芯片、体积小', 600, 350);
            ctx.fillText('硬件连接固定', 200, 370);
            ctx.fillText('可编程配置硬件功能', 600, 370);
            
            componentInfo.innerHTML = '<strong>主要区别：</strong><br>' +
                                     '1. 集成度：SoC将主要组件集成在单个芯片上，传统系统使用分离的芯片<br>' +
                                     '2. 灵活性：SoC可通过编程配置硬件功能，传统系统硬件连接固定<br>' +
                                     '3. 体积和功耗：SoC体积更小、功耗更低<br>' +
                                     '4. 应用领域：SoC主要用于现代智能设备，传统系统用于特定工业应用';
        });
        
        // 处理Canvas点击事件
        canvas.addEventListener('click', function(event) {
            const rect = canvas.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            // 检查是否点击了组件
            for (let i = 0; i < activeComponents.length; i++) {
                const comp = activeComponents[i];
                if (x >= comp.x && x <= comp.x + comp.width &&
                    y >= comp.y && y <= comp.y + comp.height) {
                    componentInfo.textContent = `${comp.name}: ${comp.info}`;
                    highlightComponent(comp);
                    return;
                }
            }
        });
        
        // 高亮显示选中的组件
        function highlightComponent(component) {
            // 不要在这里调用动画函数，只绘制基本组件
            drawSystem(activeComponents);
            
            // 绘制高亮边框
            ctx.strokeStyle = 'yellow';
            ctx.lineWidth = 3;
            ctx.strokeRect(component.x - 3, component.y - 3, component.width + 6, component.height + 6);
            
            // 如果是SoC中的组件，显示连接
            if (currentSystem === 'soc' && component.name !== '片上系统(SoC)' && component.name !== '外部设备') {
                const socMain = activeComponents.find(comp => comp.name === '片上系统(SoC)');
                if (socMain) {
                    // 绘制连接线
                    ctx.strokeStyle = 'yellow';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(component.x + component.width/2, component.y + component.height/2);
                    
                    // 找到最近的边
                    const edges = [
                        { x: socMain.x, y: component.y + component.height/2 }, // 左
                        { x: socMain.x + socMain.width, y: component.y + component.height/2 }, // 右
                        { x: component.x + component.width/2, y: socMain.y }, // 上
                        { x: component.x + component.width/2, y: socMain.y + socMain.height } // 下
                    ];
                    
                    let minDist = Infinity;
                    let closestEdge = null;
                    
                    edges.forEach(edge => {
                        const dist = Math.sqrt(
                            Math.pow(component.x + component.width/2 - edge.x, 2) +
                            Math.pow(component.y + component.height/2 - edge.y, 2)
                        );
                        if (dist < minDist) {
                            minDist = dist;
                            closestEdge = edge;
                        }
                    });
                    
                    ctx.lineTo(closestEdge.x, closestEdge.y);
                    ctx.stroke();
                }
            }
        }
        
        // 添加一个变量来跟踪动画帧
        let animationFrameId = null;
        
        // 停止所有动画的函数
        function stopAllAnimations() {
            if (animationFrameId !== null) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
        }
        
        // 动画：数据流动
        function animateDataFlow() {
            if (currentSystem !== 'embedded') return;
            
            const busComp = embeddedComponents.find(comp => comp.name === '总线');
            if (!busComp) return;
            
            // 创建数据包
            const dataPacket = {
                x: embeddedComponents[0].x + embeddedComponents[0].width/2,
                y: embeddedComponents[0].y + embeddedComponents[0].height,
                targetIndex: 1
            };
            
            function animate() {
                if (currentSystem !== 'embedded') {
                    stopAllAnimations();
                    return;
                }
                
                // 清除并重绘基本组件，不调用动画函数
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制组件
                embeddedComponents.forEach(comp => {
                    ctx.fillStyle = comp.color;
                    ctx.fillRect(comp.x, comp.y, comp.width, comp.height);
                    
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    
                    // 处理多行文本
                    const words = comp.name.split(' ');
                    if (words.length > 1 && comp.width < 200) {
                        ctx.fillText(words[0], comp.x + comp.width/2, comp.y + comp.height/2 - 10);
                        ctx.fillText(words[1], comp.x + comp.width/2, comp.y + comp.height/2 + 10);
                    } else {
                        ctx.fillText(comp.name, comp.x + comp.width/2, comp.y + comp.height/2);
                    }
                });
                
                // 绘制连接线
                ctx.strokeStyle = '#7f8c8d';
                ctx.lineWidth = 2;
                embeddedComponents.forEach(comp => {
                    if (comp.name !== '总线' && comp.name !== '外部设备') {
                        ctx.beginPath();
                        ctx.moveTo(comp.x + comp.width/2, comp.y + comp.height);
                        ctx.lineTo(busComp.x + busComp.width/2, busComp.y);
                        ctx.stroke();
                    }
                });
                
                // 绘制数据包
                ctx.fillStyle = 'yellow';
                ctx.beginPath();
                ctx.arc(dataPacket.x, dataPacket.y, 5, 0, Math.PI * 2);
                ctx.fill();
                
                // 移动数据包
                const target = embeddedComponents[dataPacket.targetIndex];
                const bus = {
                    x: busComp.x + busComp.width/2,
                    y: busComp.y
                };
                
                // 数据包移动逻辑
                if (dataPacket.y < bus.y) {
                    dataPacket.y += 3;
                } else if (Math.abs(dataPacket.x - bus.x) > 3) {
                    dataPacket.x += (bus.x > dataPacket.x) ? 3 : -3;
                } else if (Math.abs(dataPacket.x - (target.x + target.width/2)) > 3) {
                    dataPacket.x += ((target.x + target.width/2) > dataPacket.x) ? 3 : -3;
                } else if (dataPacket.y > bus.y) {
                    dataPacket.y -= 3;
                    
                    // 到达目标组件
                    if (dataPacket.y <= target.y + target.height) {
                        // 切换到下一个目标
                        dataPacket.targetIndex = (dataPacket.targetIndex + 1) % (embeddedComponents.length - 2);
                        if (dataPacket.targetIndex === 0) dataPacket.targetIndex = 1;
                    }
                }
                
                // 存储动画帧ID以便后续取消
                animationFrameId = requestAnimationFrame(animate);
            }
            
            animate();
        }
        
        // 动画：SoC集成
        function animateSoCIntegration() {
            if (currentSystem !== 'soc') return;
            
            const socMain = socComponents.find(comp => comp.name === '片上系统(SoC)');
            if (!socMain) return;
            
            function animate() {
                if (currentSystem !== 'soc') {
                    stopAllAnimations();
                    return;
                }
                
                // 清除并重绘基本组件，不调用动画函数
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制组件
                socComponents.forEach(comp => {
                    ctx.fillStyle = comp.color;
                    ctx.fillRect(comp.x, comp.y, comp.width, comp.height);
                    
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    
                    // 处理多行文本
                    const words = comp.name.split(' ');
                    if (words.length > 1 && comp.width < 200) {
                        ctx.fillText(words[0], comp.x + comp.width/2, comp.y + comp.height/2 - 10);
                        ctx.fillText(words[1], comp.x + comp.width/2, comp.y + comp.height/2 + 10);
                    } else {
                        ctx.fillText(comp.name, comp.x + comp.width/2, comp.y + comp.height/2);
                    }
                });
                
                // 绘制集成指示器
                for (const comp of socComponents) {
                    if (comp.name !== '片上系统(SoC)' && comp.name !== '外部设备') {
                        ctx.strokeStyle = 'rgba(52, 152, 219, 0.7)';
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        
                        // 创建脉动效果
                        const time = new Date().getTime() / 1000;
                        const pulseSize = Math.sin(time * 3) * 3 + 5;
                        
                        ctx.arc(
                            comp.x + comp.width/2, 
                            comp.y + comp.height/2, 
                            pulseSize, 
                            0, Math.PI * 2
                        );
                        ctx.stroke();
                    }
                }
                
                // 存储动画帧ID以便后续取消
                animationFrameId = requestAnimationFrame(animate);
            }
            
            animate();
        }
        
        // 初始状态显示
        document.getElementById('showEmbedded').click();
        
        // 处理测验选项点击
        quizOptions.forEach(option => {
            option.addEventListener('click', function() {
                // 重置所有选项
                quizOptions.forEach(opt => {
                    opt.classList.remove('correct', 'incorrect');
                });
                
                // 判断答案
                const isCorrect = option.getAttribute('data-correct') === 'true';
                option.classList.add(isCorrect ? 'correct' : 'incorrect');
                
                // 显示结果
                quizResult.style.display = 'block';
                quizResult.className = 'result ' + (isCorrect ? 'correct' : 'incorrect');
                
                if (isCorrect) {
                    quizResult.textContent = '回答正确！片上系统(SoC)不是传统嵌入式系统的组成部分，而是一种不同的系统结构。';
                } else {
                    quizResult.textContent = '回答错误，请再试一次！提示：想想哪一项是一种不同的系统架构，而不是组成部分。';
                }
            });
        });
    </script>
</body>
</html> 