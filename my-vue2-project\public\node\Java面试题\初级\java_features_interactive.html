<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java特性交互式学习</title>
    <style>
        :root {
            --primary-color: #4a90e2;
            --secondary-color: #f5a623;
            --background-color: #f4f7f9;
            --text-color: #333;
            --light-text-color: #f8f9fa;
            --border-color: #d1d8de;
            --success-color: #7ed321;
            --error-color: #d0021b;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
            color: var(--text-color);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        header {
            background-color: var(--primary-color);
            color: var(--light-text-color);
            padding: 20px 40px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        header h1 {
            margin: 0;
            font-size: 2em;
        }

        .container {
            display: flex;
            flex: 1;
            margin-top: 20px;
        }

        nav {
            width: 200px;
            padding: 20px;
            background-color: #fff;
            border-right: 1px solid var(--border-color);
        }

        nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        nav li a {
            display: block;
            padding: 12px 15px;
            text-decoration: none;
            color: var(--primary-color);
            font-weight: bold;
            border-radius: 8px;
            transition: background-color 0.3s, color 0.3s;
        }

        nav li a:hover {
            background-color: var(--primary-color);
            color: white;
        }

        nav li a.active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 4px rgba(74, 144, 226, 0.4);
        }

        main {
            flex: 1;
            padding: 20px;
            background-color: #fff;
        }

        .topic-content {
            display: none;
        }

        .topic-content.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        canvas {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-top: 20px;
            background-color: #ffffff;
        }
        
        .explanation {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--background-color);
            border-left: 4px solid var(--primary-color);
            border-radius: 4px;
        }

        .explanation ul {
            padding-left: 20px;
        }

        button {
            background-color: var(--secondary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 1em;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            margin-top: 10px;
        }

        button:hover {
            background-color: #d88f1b;
            transform: translateY(-2px);
        }
        
        footer {
            text-align: center;
            padding: 20px;
            background-color: #fff;
            border-top: 1px solid var(--border-color);
            font-size: 0.9em;
            color: #666;
        }

    </style>
</head>
<body>

    <header>
        <h1>Java核心特性 — 交互式动画讲解</h1>
        <p>你好！欢迎来到Java世界的探索之旅。我们把图片中的知识点做成了动画，希望能帮助你这位初学者轻松理解。</p>
    </header>

    <div class="container">
        <nav>
            <ul>
                <li><a href="#security" class="topic-btn active" data-topic="security">6. 安全性</a></li>
                <li><a href="#library" class="topic-btn" data-topic="library">7. 丰富的类库</a></li>
                <li><a href="#compiler" class="topic-btn" data-topic="compiler">8. 编译与解释</a></li>
                <li><a href="#distributed" class="topic-btn" data-topic="distributed">9. 分布式支持</a></li>
            </ul>
        </nav>

        <main>
            <div id="security" class="topic-content active">
                <h2>6. 安全性</h2>
                <p>Java非常注重安全，就像给代码一个"保险箱"（沙箱），并时刻检查代码行为是否规范。</p>
                <button id="security-reset-btn">重置动画</button>
                <canvas id="security-canvas" width="800" height="400"></canvas>
                <div class="explanation">
                    <p><strong>动画演示：</strong> 点击"重置动画"开始。</p>
                    <ul>
                        <li><strong>沙箱机制 (左侧)：</strong> "沙箱"保护着系统资源。当里面的"危险代码"想偷偷访问外部的"敏感文件"时，会被"安全管理员"立刻阻止。</li>
                        <li><strong>边界检查 (右侧)：</strong> 一个数组就像一排格子。当代码想访问格子范围之外（越界）的地方时，系统会马上报错，防止程序出错。</li>
                    </ul>
                </div>
            </div>

            <div id="library" class="topic-content">
                <h2>7. 丰富的类库</h2>
                <p>Java提供了海量的"工具包"（类库），让开发者能快速构建功能强大的应用，而不用事事都从零开始。</p>
                 <button id="library-reset-btn">重置动画</button>
                <canvas id="library-canvas" width="800" height="400"></canvas>
                 <div class="explanation">
                    <p><strong>动画演示：</strong>点击"重置动画"开始</p>
                    <ul>
                        <li><strong>标准库：</strong>Java自带了网络、文件操作(IO)、数据结构(集合)等大量基础工具。</li>
                        <li><strong>第三方库：</strong>更有庞大的社区提供了如 Spring、Hibernate 等功能更强大的框架，极大地提高了开发效率。</li>
                    </ul>
                </div>
            </div>

            <div id="compiler" class="topic-content">
                <h2>8. 编译与解释并存</h2>
                <p>Java代码执行时，结合了"先翻译再执行"和"边翻译边执行"两种方式，兼顾了运行效率和平台无关性。</p>
                <button id="compiler-reset-btn">开始动画</button>
                <canvas id="compiler-canvas" width="800" height="400"></canvas>
                <div class="explanation">
                    <p><strong>动画演示：</strong>点击"开始动画"观看流程。</p>
                    <ul>
                        <li><strong>编译：</strong>我们写的 `.java` 源码先被编译器（javac）一次性翻译成一种中间语言——字节码（.class文件）。</li>
                        <li><strong>解释：</strong>Java虚拟机（JVM）逐行读取并执行字节码。这让Java可以"一次编译，到处运行"。</li>
                        <li><strong>JIT优化：</strong>对于经常被执行的"热点代码"，JIT编译器会将其直接翻译成速度更快的本地机器码，大幅提升性能。</li>
                    </ul>
                </div>
            </div>

            <div id="distributed" class="topic-content">
                <h2>9. 分布式支持</h2>
                <p>Java天生就善于构建可以跨越多台计算机协同工作的大型系统。</p>
                <button id="distributed-reset-btn">发起远程调用 (RMI)</button>
                <canvas id="distributed-canvas" width="800" height="400"></canvas>
                <div class="explanation">
                    <p><strong>动画演示：</strong>点击"发起远程调用"按钮。</p>
                    <ul>
                        <li><strong>远程方法调用(RMI)：</strong>允许一台计算机（客户端）上的程序，像调用本地功能一样，直接调用另一台计算机（服务器）上的功能。</li>
                        <li><strong>构建分布式系统：</strong>这个特性使得将一个大型应用拆分到不同服务器上变得更容易，方便构建强大、可扩展的企业级应用。</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>
    
    <footer>
        <p>为零基础学习者设计的交互式教程</p>
    </footer>

    <script>
    // 全局动画循环ID
    let globalAnimationId;

    // --- 主导航逻辑 ---
    document.addEventListener('DOMContentLoaded', () => {
        const topicButtons = document.querySelectorAll('.topic-btn');
        const topicContents = document.querySelectorAll('.topic-content');
        const canvases = {
            security: initSecurityAnimation,
            library: initLibraryAnimation,
            compiler: initCompilerAnimation,
            distributed: initDistributedAnimation,
        };

        function switchTopic(topicName) {
            // 停止当前动画
            if (globalAnimationId) {
                cancelAnimationFrame(globalAnimationId);
                globalAnimationId = null;
            }

            topicContents.forEach(content => {
                content.classList.remove('active');
            });
            topicButtons.forEach(button => {
                button.classList.remove('active');
            });

            document.querySelector(`#${topicName}`).classList.add('active');
            document.querySelector(`a[data-topic="${topicName}"]`).classList.add('active');

            if (canvases[topicName]) {
                canvases[topicName]();
            }
        }

        topicButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const topicName = e.target.getAttribute('data-topic');
                switchTopic(topicName);
            });
        });

        // 默认启动第一个
        switchTopic('security');
    });

    // --- 6. 安全性动画 ---
    function initSecurityAnimation() {
        const canvas = document.getElementById('security-canvas');
        const ctx = canvas.getContext('2d');
        const resetButton = document.getElementById('security-reset-btn');

        let state = {};

        function resetState() {
            // Sandbox
            state.sandbox = { x: 50, y: 50, width: 300, height: 300 };
            state.maliciousCode = { x: 180, y: 180, width: 40, height: 40, targetX: 400, blocked: false, step: 1 };
            state.systemFile = { x: 450, y: 180, width: 50, height: 60 };

            // Bounds check
            state.array = { x: 550, y: 150, cells: 5, cellSize: 40 };
            state.pointer = { index: 0, targetIndex: 6, step: 0.02, moving: true };
            state.outOfBounds = false;
        }

        function drawText(text, x, y, size = 16, color = '#333') {
            ctx.fillStyle = color;
            ctx.font = `bold ${size}px sans-serif`;
            ctx.textAlign = 'center';
            ctx.fillText(text, x, y);
        }

        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // --- Draw Sandbox part ---
            drawText("沙箱机制", state.sandbox.x + state.sandbox.width / 2, 30);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(state.sandbox.x, state.sandbox.y, state.sandbox.width, state.sandbox.height);
            drawText("安全区 (Sandbox)", state.sandbox.x + state.sandbox.width / 2, state.sandbox.y + state.sandbox.height + 20);

            // System File
            ctx.fillStyle = '#fdebd0';
            ctx.fillRect(state.systemFile.x, state.systemFile.y, state.systemFile.width, state.systemFile.height);
            ctx.strokeRect(state.systemFile.x, state.systemFile.y, state.systemFile.width, state.systemFile.height);
            drawText("敏感文件", state.systemFile.x + state.systemFile.width / 2, state.systemFile.y + 80, 14);

            // Malicious Code
            ctx.fillStyle = state.maliciousCode.blocked ? 'grey' : 'var(--error-color)';
            ctx.fillRect(state.maliciousCode.x, state.maliciousCode.y, state.maliciousCode.width, state.maliciousCode.height);
            drawText("危险代码", state.maliciousCode.x + 20, state.maliciousCode.y + 60, 12);
            
            // Security Manager block
            if (state.maliciousCode.blocked) {
                ctx.fillStyle = 'var(--secondary-color)';
                ctx.fillRect(state.sandbox.x + state.sandbox.width - 5, state.sandbox.y, 10, state.sandbox.height);
                drawText("安全管理员", state.sandbox.x + state.sandbox.width - 5, state.sandbox.y - 10, 14, 'var(--secondary-color)');
                drawText("访问被拒绝!", state.maliciousCode.x + 100, state.maliciousCode.y + 25, 16, 'var(--error-color)');
            }

            // --- Draw Bounds Check part ---
            drawText("边界检查", state.array.x + (state.array.cells * state.array.cellSize) / 2, 30);
            for (let i = 0; i < state.array.cells; i++) {
                ctx.strokeRect(state.array.x + i * state.array.cellSize, state.array.y, state.array.cellSize, state.array.cellSize);
                drawText(i, state.array.x + i * state.array.cellSize + state.array.cellSize/2, state.array.y + state.array.cellSize + 20);
            }
            
            // Pointer
            const pointerX = state.array.x + state.pointer.index * state.array.cellSize + state.array.cellSize / 2;
            ctx.fillStyle = state.outOfBounds ? 'var(--error-color)' : 'var(--primary-color)';
            ctx.beginPath();
            ctx.moveTo(pointerX - 10, state.array.y - 30);
            ctx.lineTo(pointerX + 10, state.array.y - 30);
            ctx.lineTo(pointerX, state.array.y - 10);
            ctx.closePath();
            ctx.fill();
            
            if(state.outOfBounds) {
                 drawText("越界!", pointerX, state.array.y - 40, 16, 'var(--error-color)');
            }
        }
        
        function update() {
            // Sandbox logic
            if (state.maliciousCode.x < state.sandbox.x + state.sandbox.width - state.maliciousCode.width) {
                 state.maliciousCode.x += state.maliciousCode.step;
            } else {
                state.maliciousCode.blocked = true;
            }
            
            // Bounds check logic
            if (state.pointer.moving) {
                if (state.pointer.index < state.pointer.targetIndex) {
                    state.pointer.index += state.pointer.step;
                } else {
                    state.pointer.moving = false;
                }
                
                if (Math.floor(state.pointer.index) >= state.array.cells) {
                    state.outOfBounds = true;
                    // Stop once out of bounds
                     if (state.pointer.index >= state.array.cells) {
                        state.pointer.index = state.array.cells;
                        state.pointer.moving = false;
                    }
                }
            }
        }

        function animate() {
            update();
            draw();
            globalAnimationId = requestAnimationFrame(animate);
        }

        resetButton.onclick = () => {
            if (globalAnimationId) cancelAnimationFrame(globalAnimationId);
            resetState();
            animate();
        };
        
        resetState();
        draw(); // Initial draw
    }

    // --- 7. 丰富的类库动画 ---
    function initLibraryAnimation() {
        const canvas = document.getElementById('library-canvas');
        const ctx = canvas.getContext('2d');
        const resetButton = document.getElementById('library-reset-btn');

        let state;

        function resetState() {
             state = {
                app: { x: canvas.width / 2, y: canvas.height / 2, width: 150, height: 80 },
                libs: [
                    { name: '网络', x: 100, y: 50, connected: false, step: 2, color: '#3498db' },
                    { name: 'IO(文件)', x: 100, y: 250, connected: false, step: 2, color: '#2ecc71' },
                    { name: '集合', x: 600, y: 50, connected: false, step: 2, color: '#9b59b6' },
                    { name: 'Spring', x: 600, y: 150, connected: false, step: 2, color: '#e67e22' },
                    { name: 'Hibernate', x: 600, y: 250, connected: false, step: 2, color: '#e74c3c' },
                ],
                animating: false,
                libIndex: 0
            };
        }
        
        function drawLib(lib) {
            ctx.fillStyle = lib.color;
            ctx.fillRect(lib.x - 50, lib.y - 20, 100, 40);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(lib.name, lib.x, lib.y);
            
            if (lib.connected) {
                ctx.beginPath();
                ctx.moveTo(lib.x, lib.y);
                ctx.lineTo(state.app.x, state.app.y);
                ctx.strokeStyle = lib.color;
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        }

        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw central App
            ctx.fillStyle = 'var(--primary-color)';
            ctx.fillRect(state.app.x - state.app.width/2, state.app.y - state.app.height/2, state.app.width, state.app.height);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 20px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText("你的应用", state.app.x, state.app.y);

            // Draw libs
            state.libs.forEach(drawLib);
        }

        function update() {
            if (!state.animating) return;

            let currentLib = state.libs[state.libIndex];
            if (currentLib) {
                const dx = state.app.x - currentLib.x;
                const dy = state.app.y - currentLib.y;
                const dist = Math.sqrt(dx * dx + dy * dy);
                
                if (dist > 120) {
                    currentLib.x += (dx / dist) * currentLib.step;
                    currentLib.y += (dy / dist) * currentLib.step;
                } else {
                    currentLib.connected = true;
                    state.libIndex++;
                    if (state.libIndex >= state.libs.length) {
                        state.animating = false;
                    }
                }
            }
        }

        function animate() {
            update();
            draw();
            globalAnimationId = requestAnimationFrame(animate);
        }
        
        resetButton.onclick = () => {
            if (globalAnimationId) cancelAnimationFrame(globalAnimationId);
            resetState();
            state.animating = true;
            animate();
        };
        
        resetState();
        draw();
    }
    
    // --- 8. 编译与解释 ---
    function initCompilerAnimation() {
        const canvas = document.getElementById('compiler-canvas');
        const ctx = canvas.getContext('2d');
        const startButton = document.getElementById('compiler-reset-btn');
        let state;

        const STAGES = {
            IDLE: 0,
            COMPILING: 1,
            TO_JVM: 2,
            INTERPRETING: 3,
            JIT_SPOTTED: 4,
            JIT_COMPILING: 5,
            JIT_RUNNING: 6,
            DONE: 7,
        };

        const POS = {
            javaFile: { x: 80, y: 100 },
            compiler: { x: 220, y: 100 },
            classFile: { x: 360, y: 100 },
            jvm: { x: 500, y: 50, w: 250, h: 300 },
            jitCompiler: { x: 625, y: 280 }
        };

        function resetState() {
            state = {
                stage: STAGES.IDLE,
                progress: 0,
                bytecodeLine: 0,
                hotspotCounter: 0,
            };
        }

        function drawItem(name, x, y, color = 'grey') {
            ctx.fillStyle = color;
            ctx.fillRect(x - 40, y - 25, 80, 50);
            ctx.fillStyle = 'white';
            ctx.font = '14px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(name, x, y);
        }
        
        function drawArrow(fromX, toX, y) {
            ctx.beginPath();
            ctx.moveTo(fromX + 50, y);
            ctx.lineTo(toX - 50, y);
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(toX - 50, y);
            ctx.lineTo(toX - 60, y - 5);
            ctx.lineTo(toX - 60, y + 5);
            ctx.closePath();
            ctx.fillStyle = '#333';
            ctx.fill();
        }

        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Static items
            drawItem("Compiler", POS.compiler.x, POS.compiler.y);
            drawItem("JIT", POS.jitCompiler.x, POS.jitCompiler.y, 'var(--secondary-color)');
            
            ctx.strokeStyle = '#333';
            ctx.strokeRect(POS.jvm.x, POS.jvm.y, POS.jvm.w, POS.jvm.h);
            ctx.font = 'bold 16px sans-serif';
            ctx.fillText("JVM", POS.jvm.x + POS.jvm.w / 2, POS.jvm.y + 20);

            drawArrow(POS.javaFile.x, POS.compiler.x, POS.javaFile.y);
            drawArrow(POS.compiler.x, POS.classFile.x, POS.javaFile.y);

            // Dynamic items
            let javaFileColor = state.stage >= STAGES.IDLE ? 'var(--primary-color)' : 'grey';
            drawItem("Code.java", POS.javaFile.x, POS.javaFile.y, javaFileColor);
            
            let classFileColor = state.stage >= STAGES.TO_JVM ? 'var(--primary-color)' : 'grey';
            let classFileX = POS.javaFile.x;
            if(state.stage === STAGES.COMPILING) {
                classFileX = POS.javaFile.x + state.progress * (POS.classFile.x - POS.javaFile.x);
            } else if (state.stage >= STAGES.TO_JVM) {
                classFileX = POS.classFile.x;
            }
            drawItem("Code.class", classFileX, POS.classFile.y, classFileColor);

            if (state.stage === STAGES.TO_JVM) {
                 const targetX = POS.jvm.x + 50;
                 const targetY = POS.jvm.y + 80;
                 const dx = targetX - classFileX;
                 const dy = targetY - POS.classFile.y;
                 classFileX += dx * state.progress;
                 let classFileY = POS.classFile.y + dy * state.progress;
                 drawItem("Code.class", classFileX, classFileY, classFileColor);
            }

            // Inside JVM
            if (state.stage >= STAGES.INTERPRETING) {
                ctx.font = '12px monospace';
                ctx.textAlign = 'left';
                for(let i=0; i<5; i++) {
                    let color = (i === state.bytecodeLine % 5) ? 'var(--error-color)' : '#333';
                    if(i === 2) color = (state.hotspotCounter > 0) ? 'var(--secondary-color)' : color;
                    ctx.fillStyle = color;
                    ctx.fillText(`bytecode line ${i+1}`, POS.jvm.x + 20, POS.jvm.y + 80 + i * 20);
                }
                ctx.fillText(`执行次数: ${state.hotspotCounter}`, POS.jvm.x + 130, POS.jvm.y + 125);
                if (state.stage === STAGES.JIT_SPOTTED) {
                    ctx.fillStyle = 'var(--secondary-color)';
                    ctx.fillText("发现热点代码!", POS.jvm.x + 20, POS.jvm.y + 200);
                }
            }
            if (state.stage === STAGES.JIT_COMPILING) {
                 ctx.strokeStyle = 'var(--secondary-color)';
                 ctx.setLineDash([5, 5]);
                 ctx.beginPath();
                 ctx.moveTo(POS.jvm.x + 80, POS.jvm.y + 120);
                 ctx.lineTo(POS.jitCompiler.x, POS.jitCompiler.y);
                 ctx.stroke();
                 ctx.setLineDash([]);
            }
             if (state.stage === STAGES.JIT_RUNNING) {
                drawItem("Native Code", POS.jvm.x + 125, POS.jvm.y + 220, 'var(--success-color)');
                ctx.fillText("⚡️极速运行⚡️", POS.jvm.x + 125, POS.jvm.y + 180, 'var(--success-color)');
            }
        }
        
        function update() {
            if (state.stage === STAGES.IDLE || state.stage === STAGES.DONE) return;

            state.progress += 0.01;
            switch(state.stage) {
                case STAGES.COMPILING:
                    if (state.progress >= 1) { state.progress = 0; state.stage = STAGES.TO_JVM; }
                    break;
                case STAGES.TO_JVM:
                    if (state.progress >= 1) { state.progress = 0; state.stage = STAGES.INTERPRETING; }
                    break;
                case STAGES.INTERPRETING:
                    if (state.progress >= 0.05) {
                        state.progress = 0;
                        state.bytecodeLine++;
                        if (state.bytecodeLine % 5 === 2) state.hotspotCounter++;
                        if (state.hotspotCounter >= 3) { state.stage = STAGES.JIT_SPOTTED; setTimeout(() => {state.stage = STAGES.JIT_COMPILING; state.progress = 0;}, 1000)}
                    }
                    break;
                case STAGES.JIT_COMPILING:
                    if (state.progress >= 1) { state.progress = 0; state.stage = STAGES.JIT_RUNNING; setTimeout(() => {state.stage = STAGES.DONE}, 2000) }
                    break;
            }
        }

        function animate() {
            update();
            draw();
            if (state.stage !== STAGES.DONE) {
                globalAnimationId = requestAnimationFrame(animate);
            }
        }
        
        startButton.onclick = () => {
            if (state.stage !== STAGES.IDLE && state.stage !== STAGES.DONE) return;
            if (globalAnimationId) cancelAnimationFrame(globalAnimationId);
            resetState();
            state.stage = STAGES.COMPILING;
            animate();
        };

        resetState();
        draw();
    }
    
    // --- 9. 分布式支持 ---
    function initDistributedAnimation() {
        const canvas = document.getElementById('distributed-canvas');
        const ctx = canvas.getContext('2d');
        const startButton = document.getElementById('distributed-reset-btn');
        let state;
        
        const POS = {
            client: { x: 150, y: 200 },
            server: { x: 650, y: 200 },
        };

        function resetState() {
            state = {
                packet: null, // {x, y, isRequest, progress}
                serverState: 'idle' // 'processing', 'done'
            };
        }
        
        function drawServer(name, x, y) {
            ctx.fillStyle = '#d6eaf8';
            ctx.fillRect(x - 60, y - 80, 120, 160);
            ctx.fillStyle = '#aed6f1';
            ctx.fillRect(x-60, y-80, 120, 30);
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(name, x, y - 65);
            
            if (name === '服务器' && state.serverState === 'processing') {
                ctx.font = '30px sans-serif';
                ctx.fillText('⚙️', x, y);
            }
             if (name === '服务器' && state.serverState === 'done') {
                ctx.font = '30px sans-serif';
                ctx.fillText('✅', x, y);
            }
        }
        
        function drawPacket(packet) {
            if (!packet) return;
            ctx.fillStyle = packet.isRequest ? 'var(--primary-color)' : 'var(--success-color)';
            ctx.fillRect(packet.x - 20, packet.y - 15, 40, 30);
            ctx.fillStyle = 'white';
            ctx.font = '12px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(packet.isRequest ? "请求" : "结果", packet.x, packet.y);
        }

        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            drawServer("客户端", POS.client.x, POS.client.y);
            drawServer("服务器", POS.server.x, POS.server.y);
            drawPacket(state.packet);
        }

        function update() {
            if (!state.packet) return;
            
            state.packet.progress += 0.01;
            
            if(state.packet.isRequest) {
                state.packet.x = POS.client.x + (POS.server.x - POS.client.x) * state.packet.progress;
                if (state.packet.progress >= 1) {
                    state.packet = null;
                    state.serverState = 'processing';
                    setTimeout(() => {
                        state.serverState = 'done';
                        // send response
                        state.packet = {x: POS.server.x, y: POS.server.y, isRequest: false, progress: 0};
                    }, 1500);
                }
            } else { // is response
                 state.packet.x = POS.server.x + (POS.client.x - POS.server.x) * state.packet.progress;
                 if (state.packet.progress >= 1) {
                     state.packet = null; // animation finished
                 }
            }
        }
        
        function animate() {
            update();
            draw();
            if (state.packet || state.serverState === 'processing') {
                 globalAnimationId = requestAnimationFrame(animate);
            }
        }
        
        startButton.onclick = () => {
            if (state.packet) return; // Prevent multiple clicks
            if (globalAnimationId) cancelAnimationFrame(globalAnimationId);
            resetState();
            state.packet = {x: POS.client.x, y: POS.client.y, isRequest: true, progress: 0};
            animate();
        }
        
        resetState();
        draw();
    }
    </script>

</body>
</html> 