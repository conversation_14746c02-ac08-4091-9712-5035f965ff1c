<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Java核心概念交互式学习</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            line-height: 1.6;
            color: #333;
            background-color: #f4f7f9;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            padding: 25px 40px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
        }
        h1, h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h1 {
            text-align: center;
            font-size: 2.5em;
        }
        .section {
            margin-bottom: 40px;
        }
        .concept-box {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
            background-color: #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }
        .explanation {
            margin-top: 15px;
            padding: 15px;
            background-color: #e9f5ff;
            border-left: 4px solid #3498db;
            border-radius: 4px;
        }
        p {
            font-size: 1.1em;
        }

        /* JDK/JRE/JVM Section */
        .jvm-container {
            position: relative;
            width: 450px;
            height: 280px;
            transition: all 0.5s ease;
        }
        .box {
            position: absolute;
            border: 2px solid;
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5em;
            font-weight: bold;
            transition: all 0.4s ease-in-out;
            cursor: pointer;
        }
        #jdk {
            width: 100%;
            height: 100%;
            background-color: rgba(52, 152, 219, 0.3);
            border-color: #2980b9;
            color: #2980b9;
        }
        #jre {
            width: 80%;
            height: 80%;
            top: 10%;
            left: 10%;
            background-color: rgba(26, 188, 156, 0.4);
            border-color: #16a085;
            color: #16a085;
        }
        #jvm {
            width: 55%;
            height: 55%;
            top: 22.5%;
            left: 22.5%;
            background-color: rgba(241, 196, 15, 0.5);
            border-color: #f39c12;
            color: #f39c12;
        }
        .box:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(0,0,0,0.2);
        }
        #venn-explanation {
            position: absolute;
            right: -280px;
            top: 50%;
            transform: translateY(-50%);
            width: 250px;
            padding: 15px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.4s, visibility 0.4s;
        }
        #venn-explanation.visible {
            opacity: 1;
            visibility: visible;
        }
        #venn-explanation h3 { margin-top: 0; }

        /* Bytecode Section */
        canvas {
            background-color: #fff;
            border-radius: 8px;
            border: 1px solid #bdc3c7;
        }
        .controls {
            text-align: center;
            margin-top: 15px;
        }
        .button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #2980b9;
        }
        .button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }

        /* Data Types Section */
        #data-types-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 15px;
        }
        .data-type-card {
            width: 120px;
            height: 120px;
            background-color: #fff;
            border: 2px solid #9b59b6;
            color: #9b59b6;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            padding: 5px;
        }
        .data-type-card:hover {
            background-color: #9b59b6;
            color: white;
            transform: translateY(-5px);
            box-shadow: 0 4px 10px rgba(155, 89, 182, 0.4);
        }
        .data-type-card h4 {
            margin: 0;
            font-size: 1.2em;
        }
        .data-type-card p {
            margin: 5px 0 0;
            font-size: 0.9em;
        }
        #dtype-explanation {
            margin-top: 20px;
            min-height: 50px;
        }

    </style>
</head>
<body>

    <div class="container">
        <h1>Java核心概念 - 交互式学习</h1>

        <!-- Section 1: JDK, JRE, JVM -->
        <div class="section">
            <h2>1. JDK vs JRE vs JVM：它们是什么关系？</h2>
            <p>我们经常听到这三个术语，但它们之间有什么区别呢？把鼠标移到下方的盒子上，看看它们的包含关系吧！</p>
            <div class="concept-box" style="justify-content: space-evenly;">
                <div class="jvm-container">
                    <div class="box" id="jdk"><span>JDK</span></div>
                    <div class="box" id="jre"><span>JRE</span></div>
                    <div class="box" id="jvm"><span>JVM</span></div>
                </div>
                <div id="venn-explanation">
                    <h3 id="venn-title"></h3>
                    <p id="venn-description"></p>
                </div>
            </div>
            <div class="explanation">
                <p><b>总结一下：</b><br>
                - <b>JDK (开发工具包)</b>: 是最大的集合，好比一个完整的"工厂"，不仅能运行Java程序，还能开发、编译、调试程序。<br>
                - <b>JRE (运行环境)</b>: 在 JDK 内部，好比工厂里能让产品"跑起来"的最小环境，包含了运行Java程序所必需的东西。<br>
                - <b>JVM (Java虚拟机)</b>: 在 JRE 内部，是核心中的核心，好比工厂里的"发动机"，真正负责执行Java代码的地方。
                </p>
            </div>
        </div>

        <!-- Section 2: Bytecode -->
        <div class="section">
            <h2>2. 什么是字节码？Java如何实现"一次编译，处处运行"？</h2>
            <p>Java 的一大魅力在于它的跨平台特性。我们写的代码（.java 文件）是如何在 Windows, macOS, Linux 上都能运行的呢？答案就是"字节码"。点击下面的"开始动画"按钮，观看整个过程！</p>
            <div class="concept-box">
                <canvas id="bytecode-canvas" width="800" height="400"></canvas>
            </div>
            <div class="controls">
                <button id="play-bytecode-animation" class="button">开始动画</button>
                <button id="reset-bytecode-animation" class="button">重置</button>
            </div>
            <div class="explanation">
                <p><b>动画解析：</b><br>
                1. <b>源代码 (.java)</b>: 这是我们程序员写的、自己能看懂的代码。<br>
                2. <b>编译器 (javac)</b>: JDK中的一个工具，像一个"翻译官"，把我们的源代码翻译成一种"通用语"——字节码。<br>
                3. <b>字节码 (.class)</b>: 这就是那个"通用语"。它不针对任何具体的操作系统，只给 JVM 看。<br>
                4. <b>JVM (虚拟机)</b>: 每个操作系统（Windows, macOS, Linux）都有自己的专属JVM版本。这个JVM就像一个"本地翻译"，它能把"通用语"字节码翻译成当前操作系统能听懂的机器指令并执行。这就实现了"一次编译，处处运行"！
                </p>
            </div>
        </div>

        <!-- Section 3: Data Types -->
        <div class="section">
            <h2>3. Java有哪些数据类型？</h2>
            <p>Java 是"强类型"语言，意思是给一个变量存数据，必须先规定好这个"坑"能存什么类型、占多大地方。点击下面的卡片，了解Java的8种基本数据类型。</p>
            <div class="concept-box">
                <div id="data-types-container">
                    <!-- Cards will be generated by JS -->
                </div>
            </div>
            <div id="dtype-explanation" class="explanation">
                <p>点击上方任意一个数据类型卡片查看详细说明。</p>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', () => {
        // --- Section 1: JDK/JRE/JVM Interaction ---
        const vennBoxes = {
            jdk: {
                title: 'JDK (Java Development Kit)',
                description: 'Java开发工具包。它包含了JRE，还有编译器(javac)、调试器(jdb)等工具。程序员用它来编写和编译Java代码。是范围最大的一个！'
            },
            jre: {
                title: 'JRE (Java Runtime Environment)',
                description: 'Java运行环境。包含了JVM和Java核心类库。如果只是想运行一个已经编译好的Java程序，有JRE就足够了。'
            },
            jvm: {
                title: 'JVM (Java Virtual Machine)',
                description: 'Java虚拟机。是整个Java技术的核心。它负责解释执行字节码文件(.class)，是实现跨平台的关键。'
            }
        };

        const vennContainer = document.querySelector('.jvm-container');
        const explanationBox = document.getElementById('venn-explanation');
        const vennTitle = document.getElementById('venn-title');
        const vennDescription = document.getElementById('venn-description');

        ['jdk', 'jre', 'jvm'].forEach(id => {
            const box = document.getElementById(id);
            box.addEventListener('mouseover', () => {
                const content = vennBoxes[id];
                vennTitle.textContent = content.title;
                vennDescription.textContent = content.description;
                explanationBox.classList.add('visible');
            });
        });

        vennContainer.addEventListener('mouseleave', () => {
            explanationBox.classList.remove('visible');
        });

        // --- Section 2: Bytecode Animation ---
        const canvas = document.getElementById('bytecode-canvas');
        const ctx = canvas.getContext('2d');
        const playBtn = document.getElementById('play-bytecode-animation');
        const resetBtn = document.getElementById('reset-bytecode-animation');

        let animationState = 'idle'; // idle, running, finished
        let progress = 0;
        const animationSpeed = 0.5;

        const drawIcon = (ctx, x, y, label, icon, color = '#3498db') => {
            ctx.fillStyle = color;
            ctx.font = 'bold 24px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(icon, x, y);
            ctx.font = '14px sans-serif';
            ctx.fillStyle = '#333';
            ctx.fillText(label, x, y + 25);
        };
        
        const drawArrow = (ctx, fromX, fromY, toX, toY, text) => {
            const headlen = 10;
            const angle = Math.atan2(toY - fromY, toX - fromX);

            ctx.save();
            ctx.strokeStyle = '#2c3e50';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(fromX, fromY);
            ctx.lineTo(toX, toY);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(toX, toY);
            ctx.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            ctx.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            ctx.closePath();
            ctx.fillStyle = '#2c3e50';
            ctx.fill();

            if (text) {
                ctx.fillStyle = '#e74c3c';
                ctx.font = 'italic 14px sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText(text, (fromX + toX) / 2, (fromY + toY) / 2 - 10);
            }
            ctx.restore();
        };

        const drawScene = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Static elements
            drawIcon(ctx, 100, 50, '源代码', '📄', '#34495e');
            ctx.fillText('.java', 100, 75);

            drawIcon(ctx, 300, 50, '编译器', '⚙️', '#c0392b');
            ctx.fillText('javac', 300, 75);

            drawIcon(ctx, 500, 50, '字节码', '📜', '#27ae60');
            ctx.fillText('.class', 500, 75);

            // Platforms
            const platforms = [
                { name: 'Windows', icon: '🪟', y: 200 },
                { name: 'macOS', icon: '', y: 280 },
                { name: 'Linux', icon: '🐧', y: 360 }
            ];

            platforms.forEach(p => {
                ctx.fillStyle = '#ecf0f1';
                ctx.strokeStyle = '#7f8c8d';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.roundRect(400, p.y - 40, 380, 70, [10]);
                ctx.fill();
                ctx.stroke();
                drawIcon(ctx, 450, p.y, p.name, p.icon, '#7f8c8d');
                drawIcon(ctx, 580, p.y, '专属JVM', '☕️', '#f39c12');
                drawIcon(ctx, 720, p.y, '机器码', '💻', '#2980b9');
            });
            
            // Animation
            // Arrow 1: Source -> Compiler
            if (progress > 0) {
                const p = Math.min(1, progress / 20);
                drawArrow(ctx, 130, 50, 130 + (270 - 130) * p, 50, '编译');
            }
            
            // Arrow 2: Compiler -> Bytecode
            if (progress > 20) {
                const p = Math.min(1, (progress - 20) / 20);
                drawArrow(ctx, 330, 50, 330 + (470 - 330) * p, 50, '');
            }

            // Move bytecode to JVMs
            if (progress > 40) {
                platforms.forEach(p => {
                    const currentProgress = Math.min(1, (progress - 40) / 40);
                    const startX = 500, startY = 75;
                    const endX = 580, endY = p.y;
                    
                    const midX = startX + (endX - startX) * 0.5;
                    const midY = startY + (endY - startY) * 0.5;

                    ctx.save();
                    ctx.beginPath();
                    ctx.moveTo(startX, startY);
                    ctx.quadraticCurveTo(startX, endY, midX, midY);
                    ctx.quadraticCurveTo(endX, endY, endX, endY);
                    
                    // Dashed line
                    ctx.setLineDash([5, 5]);
                    ctx.lineWidth = 1.5;
                    ctx.strokeStyle = '#27ae60';
                    ctx.stroke();
                    ctx.restore();

                    // Animated icon
                    const pos = getQuadraticBezierXY(currentProgress, startX, startY, startX, endY, endX, endY);
                    drawIcon(ctx, pos.x, pos.y, '', '📜', '#27ae60');
                });
            }

             // JVM -> Machine Code
            if (progress > 80) {
                 const p = Math.min(1, (progress - 80) / 20);
                 platforms.forEach(platform => {
                     drawArrow(ctx, 610, platform.y, 610 + (690 - 610) * p, platform.y, '解释执行');
                 });
            }
        };

        function getQuadraticBezierXY(t, sx, sy, cp1x, cp1y, ex, ey) {
            return {
                x: (1 - t) * (1 - t) * sx + 2 * (1 - t) * t * cp1x + t * t * ex,
                y: (1 - t) * (1 - t) * sy + 2 * (1 - t) * t * cp1y + t * t * ey
            };
        }

        let animationFrameId;
        function animate() {
            if (animationState !== 'running' || progress >= 100) {
                if (progress >= 100) {
                    animationState = 'finished';
                    playBtn.disabled = true;
                }
                cancelAnimationFrame(animationFrameId);
                return;
            }
            progress += animationSpeed;
            drawScene();
            animationFrameId = requestAnimationFrame(animate);
        }

        playBtn.addEventListener('click', () => {
            if (animationState === 'idle') {
                animationState = 'running';
                playBtn.disabled = true;
                animate();
            }
        });

        resetBtn.addEventListener('click', () => {
            cancelAnimationFrame(animationFrameId);
            progress = 0;
            animationState = 'idle';
            playBtn.disabled = false;
            drawScene();
        });

        drawScene(); // Initial draw

        // --- Section 3: Data Types Interaction ---
        const dataTypes = [
            { name: 'byte', desc: '整数', size: '1字节', range: '-128 ~ 127' },
            { name: 'short', desc: '整数', size: '2字节', range: '-32,768 ~ 32,767' },
            { name: 'int', desc: '整数 (默认)', size: '4字节', range: '约-21亿 ~ 21亿' },
            { name: 'long', desc: '整数', size: '8字节', range: '很大' },
            { name: 'float', desc: '浮点数', size: '4字节', range: '精度约7位小数' },
            { name: 'double', desc: '浮点数 (默认)', size: '8字节', range: '精度约15位小数' },
            { name: 'char', desc: '单个字符', size: '2字节', range: "如 'A', '好'" },
            { name: 'boolean', desc: '布尔值', size: '不确定', range: 'true 或 false' },
        ];

        const dataTypesContainer = document.getElementById('data-types-container');
        const dtypeExplanation = document.getElementById('dtype-explanation');

        dataTypes.forEach(dtype => {
            const card = document.createElement('div');
            card.className = 'data-type-card';
            card.innerHTML = `<h4>${dtype.name}</h4><p>${dtype.desc}</p>`;
            card.addEventListener('click', () => {
                dtypeExplanation.innerHTML = `<p><b>${dtype.name}</b>: 是一种${dtype.desc}类型。它在内存中占用 <b>${dtype.size}</b>，可以表示的值为 <b>${dtype.range}</b>。</p>`;
            });
            dataTypesContainer.appendChild(card);
        });
    });
    </script>
</body>
</html> 