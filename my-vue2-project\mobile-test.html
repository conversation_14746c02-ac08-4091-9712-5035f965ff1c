<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>移动端功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #409EFF;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-item {
            padding: 15px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #409EFF;
        }
        
        .feature-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .feature-desc {
            color: #666;
            font-size: 14px;
        }
        
        .test-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #e8f4fd;
            border-radius: 8px;
        }
        
        .test-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #409EFF;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 10px 10px 0;
            transition: background-color 0.3s;
        }
        
        .test-button:hover {
            background-color: #337ab7;
        }
        
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .instructions h3 {
            color: #856404;
            margin-bottom: 10px;
        }
        
        .instructions ul {
            margin-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
            color: #856404;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 15px;
            }
            
            .test-button {
                display: block;
                text-align: center;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 移动端功能测试页面</h1>
        
        <ul class="feature-list">
            <li class="feature-item">
                <div class="feature-title">🎯 响应式布局</div>
                <div class="feature-desc">自动适配移动端屏幕，提供专门的移动端UI</div>
            </li>
            
            <li class="feature-item">
                <div class="feature-title">👆 触摸手势支持</div>
                <div class="feature-desc">支持滑动切换文件、打开/关闭侧边栏等手势操作</div>
            </li>
            
            <li class="feature-item">
                <div class="feature-title">📖 沉浸式阅读</div>
                <div class="feature-desc">全屏阅读模式，专注内容体验</div>
            </li>
            
            <li class="feature-item">
                <div class="feature-title">🎮 底部导航栏</div>
                <div class="feature-desc">固定底部的快速操作按钮</div>
            </li>
            
            <li class="feature-item">
                <div class="feature-title">🎨 移动端优化</div>
                <div class="feature-desc">触摸友好的按钮大小、字体和间距</div>
            </li>
        </ul>
        
        <div class="test-section">
            <h3>🧪 开始测试</h3>
            <p>点击下面的按钮访问知识库应用，体验移动端功能：</p>
            <a href="/#/notes" class="test-button">打开知识库</a>
            <a href="/#/notes?filePath=example.html" class="test-button">直接打开示例文件</a>
        </div>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <ul>
                <li>请在移动设备或浏览器的移动端模式下测试</li>
                <li>尝试从左边缘向右滑动打开侧边栏</li>
                <li>在阅读器中左右滑动切换文件</li>
                <li>点击"沉浸"按钮体验全屏阅读模式</li>
                <li>使用底部导航栏进行文件切换和轮播控制</li>
            </ul>
        </div>
    </div>
</body>
</html>
