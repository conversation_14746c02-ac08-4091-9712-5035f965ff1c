<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAID6 互动学习 - 磁盘阵列容量计算</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .title {
            font-size: 3rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.8);
            font-weight: 300;
        }

        .section {
            background: rgba(255,255,255,0.95);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 1s ease-out 0.3s forwards;
        }

        .question-box {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            text-align: center;
        }

        .question-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .question-text {
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .option {
            background: rgba(255,255,255,0.1);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .option:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .option.correct {
            background: #2ecc71;
            border-color: #27ae60;
        }

        .option.wrong {
            background: #e74c3c;
            border-color: #c0392b;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }

        canvas {
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            background: white;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 30px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 30px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .btn:active {
            transform: translateY(0);
        }

        .explanation {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            line-height: 1.8;
        }

        .formula {
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            font-size: 1.3rem;
            font-weight: 600;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .disk-info {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }

        .disk-stat {
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            min-width: 150px;
        }

        .disk-stat h3 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .disk-stat p {
            opacity: 0.9;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.5); }
            50% { box-shadow: 0 0 40px rgba(102, 126, 234, 0.8); }
        }

        .highlight {
            animation: glow 2s infinite;
        }

        @media (max-width: 768px) {
            .title { font-size: 2rem; }
            .section { padding: 20px; }
            .options { grid-template-columns: 1fr; }
            .controls { flex-direction: column; align-items: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">RAID6 磁盘阵列学习</h1>
            <p class="subtitle">通过动画和交互理解RAID6的工作原理</p>
        </div>

        <div class="section">
            <div class="question-box">
                <h2 class="question-title">📚 题目挑战</h2>
                <p class="question-text">假如有 4 块 80T 的硬盘，采用 RAID6 组建磁盘阵列的容量是（ ）。</p>
                
                <div class="options">
                    <div class="option" data-answer="A">A. 40T</div>
                    <div class="option" data-answer="B">B. 80T</div>
                    <div class="option" data-answer="C">C. 160T</div>
                    <div class="option" data-answer="D">D. 240T</div>
                </div>
            </div>

            <div class="canvas-container">
                <canvas id="raidCanvas" width="800" height="400"></canvas>
            </div>

            <div class="controls">
                <button class="btn" onclick="startAnimation()">🎬 开始动画演示</button>
                <button class="btn" onclick="showCalculation()">🧮 显示计算过程</button>
                <button class="btn" onclick="resetDemo()">🔄 重置演示</button>
            </div>

            <div class="explanation" id="explanation" style="display: none;">
                <h3>🎯 RAID6 知识解析</h3>
                <p><strong>RAID6 是什么？</strong></p>
                <p>RAID6 是一种磁盘阵列技术，它使用双重奇偶校验来保护数据。即使同时损坏2块硬盘，数据也不会丢失！</p>
                
                <div class="formula">
                    RAID6 容量公式：(N - 2) × 最小磁盘容量
                </div>
                
                <div class="disk-info">
                    <div class="disk-stat">
                        <h3>4</h3>
                        <p>总磁盘数量</p>
                    </div>
                    <div class="disk-stat">
                        <h3>2</h3>
                        <p>校验磁盘数量</p>
                    </div>
                    <div class="disk-stat">
                        <h3>2</h3>
                        <p>数据磁盘数量</p>
                    </div>
                    <div class="disk-stat">
                        <h3>160T</h3>
                        <p>可用容量</p>
                    </div>
                </div>
                
                <p><strong>计算过程：</strong></p>
                <p>• 总磁盘：4块，每块80T</p>
                <p>• RAID6需要2块磁盘做校验</p>
                <p>• 实际存储数据的磁盘：4 - 2 = 2块</p>
                <p>• 可用容量：2 × 80T = 160T</p>
                
                <p><strong>正确答案：C. 160T</strong></p>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('raidCanvas');
        const ctx = canvas.getContext('2d');
        let animationFrame;
        let animationStep = 0;
        let isAnimating = false;

        // 磁盘对象
        class Disk {
            constructor(x, y, label, type = 'data') {
                this.x = x;
                this.y = y;
                this.label = label;
                this.type = type; // 'data' or 'parity'
                this.scale = 1;
                this.opacity = 1;
                this.glowIntensity = 0;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.opacity;
                
                // 发光效果
                if (this.glowIntensity > 0) {
                    ctx.shadowColor = this.type === 'parity' ? '#ff6b6b' : '#74b9ff';
                    ctx.shadowBlur = this.glowIntensity;
                }
                
                // 磁盘主体
                ctx.fillStyle = this.type === 'parity' ? '#ff6b6b' : '#74b9ff';
                ctx.beginPath();
                ctx.roundRect(this.x - 60 * this.scale, this.y - 80 * this.scale, 
                             120 * this.scale, 160 * this.scale, 10);
                ctx.fill();
                
                // 磁盘标签
                ctx.fillStyle = 'white';
                ctx.font = `bold ${16 * this.scale}px Arial`;
                ctx.textAlign = 'center';
                ctx.fillText(this.label, this.x, this.y - 20);
                
                // 容量标签
                ctx.font = `${14 * this.scale}px Arial`;
                ctx.fillText('80T', this.x, this.y + 10);
                
                // 类型标签
                ctx.font = `${12 * this.scale}px Arial`;
                ctx.fillText(this.type === 'parity' ? '校验' : '数据', this.x, this.y + 40);
                
                ctx.restore();
            }
        }

        // 初始化磁盘
        const disks = [
            new Disk(150, 200, '磁盘1', 'data'),
            new Disk(300, 200, '磁盘2', 'data'),
            new Disk(450, 200, '磁盘3', 'parity'),
            new Disk(600, 200, '磁盘4', 'parity')
        ];

        function drawScene() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制背景
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, '#f8f9fa');
            gradient.addColorStop(1, '#e9ecef');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 绘制标题
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('RAID6 磁盘阵列结构', canvas.width / 2, 40);
            
            // 绘制磁盘
            disks.forEach(disk => disk.draw());
            
            // 绘制连接线
            if (animationStep >= 2) {
                drawConnections();
            }
            
            // 绘制容量计算
            if (animationStep >= 3) {
                drawCalculation();
            }
        }

        function drawConnections() {
            ctx.strokeStyle = '#6c757d';
            ctx.lineWidth = 3;
            ctx.setLineDash([10, 5]);
            
            // 连接所有磁盘
            for (let i = 0; i < disks.length - 1; i++) {
                ctx.beginPath();
                ctx.moveTo(disks[i].x + 60, disks[i].y);
                ctx.lineTo(disks[i + 1].x - 60, disks[i + 1].y);
                ctx.stroke();
            }
            
            ctx.setLineDash([]);
        }

        function drawCalculation() {
            // 计算框
            ctx.fillStyle = 'rgba(255, 255, 255, 0.95)';
            ctx.strokeStyle = '#dee2e6';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.roundRect(50, 320, 700, 60, 10);
            ctx.fill();
            ctx.stroke();
            
            // 计算文本
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('容量计算：(4块磁盘 - 2块校验) × 80T = 2 × 80T = 160T', 70, 345);
            
            ctx.font = '14px Arial';
            ctx.fillStyle = '#6c757d';
            ctx.fillText('✓ 可同时容忍2块磁盘故障，数据安全性极高', 70, 365);
        }

        function startAnimation() {
            if (isAnimating) return;
            
            isAnimating = true;
            animationStep = 0;
            
            // 重置磁盘状态
            disks.forEach(disk => {
                disk.scale = 0;
                disk.opacity = 0;
                disk.glowIntensity = 0;
            });
            
            animate();
        }

        function animate() {
            drawScene();
            
            switch(animationStep) {
                case 0:
                    // 磁盘出现动画
                    disks.forEach((disk, index) => {
                        if (disk.scale < 1) {
                            disk.scale += 0.05;
                            disk.opacity += 0.05;
                        }
                    });
                    
                    if (disks[0].scale >= 1) {
                        animationStep = 1;
                        setTimeout(() => animationStep = 2, 1000);
                    }
                    break;
                    
                case 1:
                    // 高亮数据磁盘
                    disks.forEach(disk => {
                        if (disk.type === 'data') {
                            disk.glowIntensity = 20 + Math.sin(Date.now() * 0.01) * 10;
                        }
                    });
                    break;
                    
                case 2:
                    // 高亮校验磁盘
                    disks.forEach(disk => {
                        if (disk.type === 'parity') {
                            disk.glowIntensity = 20 + Math.sin(Date.now() * 0.01) * 10;
                        } else {
                            disk.glowIntensity = 0;
                        }
                    });
                    
                    setTimeout(() => animationStep = 3, 2000);
                    break;
                    
                case 3:
                    // 显示最终结果
                    disks.forEach(disk => {
                        disk.glowIntensity = 0;
                    });
                    
                    setTimeout(() => {
                        isAnimating = false;
                        showCalculation();
                    }, 1000);
                    return;
            }
            
            animationFrame = requestAnimationFrame(animate);
        }

        function showCalculation() {
            document.getElementById('explanation').style.display = 'block';
            document.getElementById('explanation').scrollIntoView({ behavior: 'smooth' });
        }

        function resetDemo() {
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }
            
            isAnimating = false;
            animationStep = 0;
            
            disks.forEach(disk => {
                disk.scale = 1;
                disk.opacity = 1;
                disk.glowIntensity = 0;
            });
            
            drawScene();
            document.getElementById('explanation').style.display = 'none';
        }

        // 选项点击处理
        document.querySelectorAll('.option').forEach(option => {
            option.addEventListener('click', function() {
                const answer = this.dataset.answer;
                
                // 重置所有选项
                document.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('correct', 'wrong');
                });
                
                // 标记正确答案
                if (answer === 'C') {
                    this.classList.add('correct');
                    setTimeout(() => {
                        startAnimation();
                    }, 500);
                } else {
                    this.classList.add('wrong');
                    // 显示正确答案
                    setTimeout(() => {
                        document.querySelector('[data-answer="C"]').classList.add('correct');
                    }, 1000);
                }
            });
        });

        // 初始化
        drawScene();
        
        // 响应式处理
        function resizeCanvas() {
            const container = canvas.parentElement;
            const maxWidth = Math.min(800, container.clientWidth - 40);
            const scale = maxWidth / 800;
            
            canvas.style.width = maxWidth + 'px';
            canvas.style.height = (400 * scale) + 'px';
        }
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
    </script>
</body>
</html>
