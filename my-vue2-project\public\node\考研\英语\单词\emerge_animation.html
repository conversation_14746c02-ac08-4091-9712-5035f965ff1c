<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习: Emerge</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap');

        :root {
            --primary-color: #00acc1; /* 青蓝色主题，代表水与光 */
            --secondary-color: #00838f;
            --accent-color: #fdd835; /* 黄色，代表光芒 */
            --dark-bg: #003942;
            --light-bg: #f4f8f9;
            --panel-bg: #ffffff;
            --text-color: #263238;
        }

        body { font-family: 'Roboto', 'Noto Sans SC', sans-serif; background-color: #dcedf0; color: var(--text-color); display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; overflow: hidden; }
        .container { display: flex; flex-direction: row; width: 95%; max-width: 1400px; height: 90vh; max-height: 800px; background-color: var(--panel-bg); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden; }
        .word-panel { flex: 1; padding: 40px; display: flex; flex-direction: column; justify-content: center; background-color: var(--light-bg); overflow-y: auto; }
        .word-panel h1 { font-size: 3.5em; color: var(--primary-color); }
        .word-panel .pronunciation { font-size: 1.5em; color: var(--secondary-color); margin-bottom: 20px; }
        .word-panel .details p { font-size: 1.1em; line-height: 1.6; margin: 10px 0; }
        .word-panel .details strong { color: var(--secondary-color); }
        .word-panel .example { margin-top: 20px; padding-left: 15px; border-left: 3px solid var(--primary-color); font-style: italic; color: #37474f; }
        .breakdown-section { margin-top: 25px; padding: 20px; background-color: #e6f7f9; border-radius: 10px; }
        .breakdown-section h3 { margin-top: 0; color: var(--secondary-color); font-size: 1.3em; margin-bottom: 15px; }
        .morpheme-btn { padding: 8px 15px; border: 2px solid var(--primary-color); border-radius: 20px; background-color: transparent; color: var(--primary-color); font-size: 1em; font-weight: bold; cursor: pointer; transition: all 0.3s; }
        .morpheme-btn:hover, .morpheme-btn.active { background-color: var(--primary-color); color: white; transform: translateY(-2px); box-shadow: 0 0 10px var(--primary-color); }

        .animation-panel { flex: 2; padding: 20px; display: flex; flex-direction: column; justify-content: center; align-items: center; position: relative; background: var(--dark-bg); }
        .activity-title { font-size: 1.8em; color: var(--light-bg); margin-bottom: 15px; text-align: center; }
        .activity-wrapper { display: none; width: 100%; height: calc(100% - 100px); flex-direction: column; align-items: center; justify-content: center; }
        .activity-wrapper.active { display: flex; }
        .game-container { width: 100%; height: 100%; position: relative; display: flex; align-items: center; justify-content: center; border-radius: 15px; background: linear-gradient(135deg, #1f484d, #002b33); overflow: hidden; }
        .control-button { margin-top: 20px; padding: 15px 30px; font-size: 1.2em; color: #fff; background-color: var(--primary-color); border: none; border-radius: 30px; cursor: pointer; transition: all 0.3s; }
        
        /* 词缀 e- (向外) */
        #e-box { width: 100px; height: 100px; border: 4px solid var(--primary-color); position: relative; }
        #e-door { width: 100%; height: 100%; background: var(--secondary-color); transform-origin: left; transition: transform 1s; }
        #e-object { width: 40px; height: 40px; background: var(--accent-color); position: absolute; top: 30px; left: 10px; transition: transform 1s 0.5s; }
        .e-game-run #e-door { transform: rotateY(-120deg); }
        .e-game-run #e-object { transform: translateX(120px); }

        /* 词缀 merge (沉浸) */
        #merge-water { width: 100%; height: 50%; background: var(--primary-color); position: absolute; bottom: 0; }
        #merge-object { width: 50px; height: 50px; background: var(--accent-color); border-radius: 50%; position: absolute; top: 10%; left: calc(50% - 25px); transition: top 1.5s cubic-bezier(0.68, -0.55, 0.27, 1.55); }
        .merge-game-run #merge-object { top: 70%; }
        .ripple { position: absolute; border: 2px solid white; border-radius: 50%; animation: ripple-effect 1s forwards; }
        @keyframes ripple-effect { from { width: 0; height: 0; opacity: 1; } to { width: 100px; height: 100px; opacity: 0; } }

        /* 完整动画: 潜艇上浮 */
        #emerge-sky { width: 100%; height: 50%; background: #81d4fa; position: absolute; top: 0; }
        #emerge-water { width: 100%; height: 50%; background: var(--primary-color); position: absolute; bottom: 0; }
        #emerge-sub { width: 120px; height: 40px; background: #546e7a; border-radius: 20px 20px 10px 10px; position: absolute; bottom: -40px; left: calc(50% - 60px); transition: bottom 3s ease-out; }
        #emerge-sub::before { content: ''; position: absolute; width: 30px; height: 20px; background: #546e7a; top: -20px; left: 45px; border-radius: 10px 10px 0 0;}
        .emerge-game-run #emerge-sub { bottom: 45%; }
        .bubble { position: absolute; width: 10px; height: 10px; background: rgba(255,255,255,0.5); border-radius: 50%; animation: rise 2s forwards; }
        @keyframes rise { from { bottom: 0; opacity: 1; transform: scale(0.5); } to { bottom: 200px; opacity: 0; transform: scale(1); } }
        
        /* Canvas */
        #emerge-canvas { background: var(--dark-bg); cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <div class="word-panel">
            <h1>emerge</h1>
            <p class="pronunciation">[ɪˈmɜːdʒ]</p>
            <div class="details">
                <p><strong>词性：</strong> v. 浮现，出现；（问题、事实等）显露</p>
                <p><strong>含义：</strong> 从液体、暗处或隐藏的地方出现；摆脱困境；（事实、想法等）显现出来，为人所知。</p>
                <div class="example">
                    <p><strong>例句：</strong> The submarine emerged from the depths of the ocean.</p>
                    <p><strong>翻译：</strong>潜水艇从海洋深处浮现出来。</p>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>词缀解析 (2D)</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="e-game">e- (向外)</button>
                    <button class="morpheme-btn" data-activity="merge-game">-merge (沉浸)</button>
                </div>
            </div>
            <div class="breakdown-section">
                <h3>完整单词活动</h3>
                <div class="morpheme-list">
                    <button class="morpheme-btn" data-activity="full-animation">动画: 潜艇上浮</button>
                    <button class="morpheme-btn" data-activity="canvas-animation">互动: 微光浮现</button>
                </div>
            </div>
        </div>
        <div class="animation-panel">
            <h2 id="activity-title" class="activity-title">欢迎!</h2>
            <div id="welcome-screen" class="activity-wrapper active"><p style="color:white;">点击左侧按钮，探索"浮现"的奥秘！</p></div>
            <div id="e-game" class="activity-wrapper"><div class="game-container"><div id="e-box"><div id="e-door"></div><div id="e-object"></div></div></div><button class="control-button" id="e-btn">向外</button></div>
            <div id="merge-game" class="activity-wrapper"><div class="game-container"><div id="merge-water"></div><div id="merge-object"></div></div><button class="control-button" id="merge-btn">沉浸</button></div>
            <div id="full-animation" class="activity-wrapper"><div class="game-container"><div id="emerge-sky"></div><div id="emerge-water"></div><div id="emerge-sub"></div></div><button class="control-button" id="emerge-btn">上浮</button></div>
            <div id="canvas-animation" class="activity-wrapper"><div class="game-container"><canvas id="emerge-canvas" width="600" height="400"></canvas></div><button class="control-button" id="light-btn">点亮</button></div>
        </div>
    </div>
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const activityBtns = document.querySelectorAll('.morpheme-btn');
        const activityWrappers = document.querySelectorAll('.activity-wrapper');
        const activityTitle = document.getElementById('activity-title');

        activityBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                activityBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                activityTitle.textContent = btn.textContent;
                activityWrappers.forEach(w => w.classList.remove('active'));
                document.getElementById(btn.dataset.activity)?.classList.add('active');
            });
        });

        // e- game
        document.getElementById('e-btn').addEventListener('click', (e) => {
            e.target.previousElementSibling.firstElementChild.classList.toggle('e-game-run');
        });

        // merge game
        document.getElementById('merge-btn').addEventListener('click', (e) => {
            const container = e.target.previousElementSibling;
            container.classList.toggle('merge-game-run');
            if (container.classList.contains('merge-game-run')) {
                setTimeout(() => {
                    const ripple = document.createElement('div');
                    ripple.className = 'ripple';
                    ripple.style.top = '50%';
                    ripple.style.left = '50%';
                    ripple.style.transform = 'translate(-50%, -50%)';
                    container.appendChild(ripple);
                    setTimeout(() => ripple.remove(), 1000);
                }, 1000);
            }
        });

        // emerge full game
        document.getElementById('emerge-btn').addEventListener('click', (e) => {
            const container = e.target.previousElementSibling;
            container.classList.toggle('emerge-game-run');
            if (container.classList.contains('emerge-game-run')) {
                for (let i = 0; i < 10; i++) {
                    const bubble = document.createElement('div');
                    bubble.className = 'bubble';
                    bubble.style.left = `calc(50% + ${Math.random() * 80 - 40}px)`;
                    bubble.style.animationDelay = `${Math.random() * 1.5}s`;
                    document.getElementById('emerge-sub').appendChild(bubble);
                    setTimeout(() => bubble.remove(), 2000);
                }
            }
        });

        // Canvas game
        function setupCanvasGame() {
            const canvas = document.getElementById('emerge-canvas');
            const btn = document.getElementById('light-btn');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');
            let particles = [], light;
            let animationId;

            class Particle {
                constructor() {
                    this.x = Math.random() * canvas.width;
                    this.y = Math.random() * canvas.height;
                    this.vx = Math.random() - 0.5;
                    this.vy = Math.random() - 0.5;
                    this.size = Math.random() * 2 + 1;
                    this.isLit = false;
                    this.color = `rgba(173, 216, 230, 0.2)`; // Dim light blue
                }
                update(lightSource) {
                    if (this.isLit) {
                        const dx = this.x - lightSource.x;
                        const dy = this.y - lightSource.y;
                        const dist = Math.sqrt(dx * dx + dy * dy);
                        this.vx += (dx / dist) * 0.1;
                        this.vy += (dy / dist) * 0.1;
                    }
                    this.x += this.vx;
                    this.y += this.vy;
                    // Friction
                    this.vx *= 0.99;
                    this.vy *= 0.99;
                }
                draw() {
                    ctx.fillStyle = this.color;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                    ctx.fill();
                }
            }

            class LightSource {
                constructor() { this.reset(); }
                reset() { this.x = canvas.width / 2; this.y = canvas.height / 2; this.radius = 0; }
                grow() { this.radius += 1; }
                draw() {
                    ctx.fillStyle = `rgba(253, 216, 53, 0.1)`;
                    ctx.beginPath();
                    ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
                    ctx.fill();
                }
            }

            function init() {
                particles = [];
                light = new LightSource();
                for (let i = 0; i < 300; i++) particles.push(new Particle());
            }

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                if(isEmerging) {
                    light.grow();
                    light.draw();
                }
                particles.forEach(p => {
                    const dist = Math.sqrt(Math.pow(p.x - light.x, 2) + Math.pow(p.y - light.y, 2));
                    if (!p.isLit && dist < light.radius) {
                        p.isLit = true;
                        p.color = `hsl(${180 + Math.random() * 40}, 90%, 70%)`;
                    }
                    p.update(light);
                    p.draw();
                });
                animationId = requestAnimationFrame(animate);
            }
            
            let isEmerging = false;
            btn.addEventListener('click', () => {
                isEmerging = !isEmerging;
                btn.textContent = isEmerging ? "重置" : "点亮";
                if(!isEmerging) init();
            });

            const observer = new MutationObserver(() => {
                if(document.getElementById('canvas-animation').classList.contains('active')) {
                    if (!animationId) { init(); animate(); }
                } else {
                    if (animationId) { cancelAnimationFrame(animationId); animationId = null; isEmerging = false; }
                }
            });
            observer.observe(document.getElementById('canvas-animation'), { attributes: true });
        }
        setupCanvasGame();
    });
    </script>
</body>
</html> 