<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式系统与SoC学习</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .flex-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            flex-wrap: wrap;
        }
        .flex-item {
            flex: 1;
            min-width: 300px;
        }
        .canvas-container {
            text-align: center;
            margin: 20px 0;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        .controls {
            display: flex;
            justify-content: center;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .component {
            cursor: pointer;
            transition: all 0.3s;
        }
        .component:hover {
            filter: brightness(1.2);
        }
        .info-panel {
            background-color: #e1f5fe;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            display: none;
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-radius: 5px 5px 0 0;
        }
        .tab button {
            background-color: inherit;
            color: #333;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
            border-radius: 0;
            margin: 0;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #3498db;
            color: white;
        }
        .tabcontent {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 5px 5px;
            background-color: white;
            animation: fadeEffect 1s;
        }
        @keyframes fadeEffect {
            from {opacity: 0;}
            to {opacity: 1;}
        }
    </style>
</head>
<body>
    <h1>嵌入式系统与SoC学习</h1>
    
    <div class="container">
        <div class="tab">
            <button class="tablinks active" onclick="openTab(event, 'embedded')">嵌入式系统</button>
            <button class="tablinks" onclick="openTab(event, 'soc')">片上系统(SoC)</button>
        </div>
        
        <div id="embedded" class="tabcontent" style="display: block;">
            <h2>嵌入式系统硬件组成</h2>
            <p>嵌入式系统是专为特定功能而设计的计算机系统，通常嵌入到更大的设备中。</p>
            
            <div class="canvas-container">
                <canvas id="embeddedCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="controls">
                <button onclick="highlightComponent('mcu')">微控制器</button>
                <button onclick="highlightComponent('memory')">存储器</button>
                <button onclick="highlightComponent('bus')">总线</button>
                <button onclick="highlightComponent('timer')">定时器</button>
                <button onclick="highlightComponent('io')">I/O接口</button>
                <button onclick="animateEmbeddedSystem()">运行演示</button>
                <button onclick="resetCanvas('embedded')">重置</button>
            </div>
            
            <div id="infoPanel" class="info-panel"></div>
        </div>
        
        <div id="soc" class="tabcontent">
            <h2>片上系统(SoC)</h2>
            <p>SoC是将完整系统集成到单个芯片上的技术，将CPU、内存、I/O等组件整合在一起。</p>
            
            <div class="canvas-container">
                <canvas id="socCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="controls">
                <button onclick="highlightSoCComponent('cpu')">处理器核心</button>
                <button onclick="highlightSoCComponent('gpu')">图形处理单元</button>
                <button onclick="highlightSoCComponent('dsp')">数字信号处理器</button>
                <button onclick="highlightSoCComponent('memory')">内存</button>
                <button onclick="highlightSoCComponent('peripheral')">外设接口</button>
                <button onclick="animateSoC()">运行演示</button>
                <button onclick="resetCanvas('soc')">重置</button>
            </div>
            
            <div id="socInfoPanel" class="info-panel"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let embeddedCtx, socCtx;
        let embeddedComponents = {};
        let socComponents = {};
        let animationId = null;
        let dataPacket = { x: 0, y: 0, active: false };
        
        // 页面加载时初始化
        window.onload = function() {
            // 获取画布上下文
            const embeddedCanvas = document.getElementById('embeddedCanvas');
            const socCanvas = document.getElementById('socCanvas');
            embeddedCtx = embeddedCanvas.getContext('2d');
            socCtx = socCanvas.getContext('2d');
            
            // 初始化嵌入式系统画布
            initEmbeddedCanvas();
            
            // 初始化SoC画布
            initSoCCanvas();
            
            // 打开默认标签
            document.getElementById('embedded').style.display = 'block';
        };
        
        // 标签切换功能
        function openTab(evt, tabName) {
            let i, tabcontent, tablinks;
            
            // 隐藏所有标签内容
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            
            // 移除所有标签按钮的 "active" 类
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            
            // 显示当前标签，并添加 "active" 类到按钮上
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
            
            // 重置相应的画布
            resetCanvas(tabName);
        }
        
        // 初始化嵌入式系统画布
        function initEmbeddedCanvas() {
            embeddedCtx.clearRect(0, 0, embeddedCanvas.width, embeddedCanvas.height);
            
            // 定义组件
            embeddedComponents = {
                mcu: {
                    x: 350, y: 150, width: 120, height: 100,
                    color: '#ff7675', highlighted: false,
                    name: '微控制器/MCU',
                    info: '微控制器是嵌入式系统的"大脑"，负责执行程序指令和控制系统行为。'
                },
                memory: {
                    x: 550, y: 150, width: 100, height: 100,
                    color: '#74b9ff', highlighted: false,
                    name: '存储器(RAM/ROM)',
                    info: '存储器用于保存程序代码和数据，包括易失性RAM和非易失性ROM。'
                },
                bus: {
                    x: 150, y: 200, width: 500, height: 20,
                    color: '#55efc4', highlighted: false,
                    name: '总线',
                    info: '总线负责系统内部的数据传输，连接各个硬件组件。'
                },
                timer: {
                    x: 150, y: 150, width: 100, height: 80,
                    color: '#fdcb6e', highlighted: false,
                    name: '定时器/计数器',
                    info: '定时器/计数器用于产生精确的时间延迟和事件触发。'
                },
                io: {
                    x: 350, y: 300, width: 300, height: 60,
                    color: '#a29bfe', highlighted: false,
                    name: 'I/O接口',
                    info: 'I/O接口允许嵌入式系统与外部设备通信，如串口、USB、网络等。'
                }
            };
            
            // 绘制组件
            drawEmbeddedComponents();
        }
        
        // 初始化SoC画布
        function initSoCCanvas() {
            socCtx.clearRect(0, 0, socCanvas.width, socCanvas.height);
            
            // 定义SoC组件
            socComponents = {
                cpu: {
                    x: 300, y: 100, width: 100, height: 80,
                    color: '#ff7675', highlighted: false,
                    name: 'CPU核心',
                    info: 'CPU核心是SoC的主要处理单元，负责执行指令和计算。'
                },
                gpu: {
                    x: 450, y: 100, width: 100, height: 80,
                    color: '#fdcb6e', highlighted: false,
                    name: '图形处理单元(GPU)',
                    info: 'GPU专注于图形渲染和并行计算任务，提高图形处理性能。'
                },
                dsp: {
                    x: 600, y: 100, width: 100, height: 80,
                    color: '#a29bfe', highlighted: false,
                    name: '数字信号处理器(DSP)',
                    info: 'DSP专门用于处理数字信号，如音频、视频等数据的实时处理。'
                },
                memory: {
                    x: 450, y: 220, width: 150, height: 60,
                    color: '#74b9ff', highlighted: false,
                    name: '内存',
                    info: 'SoC集成的内存可以是多种类型，如SRAM、缓存等，用于数据和指令存储。'
                },
                peripheral: {
                    x: 450, y: 320, width: 300, height: 50,
                    color: '#55efc4', highlighted: false,
                    name: '外设接口',
                    info: '集成的外设接口包括UART、I2C、SPI等，用于与外部设备通信。'
                }
            };
            
            // 绘制SoC芯片外框
            socCtx.strokeStyle = '#2d3436';
            socCtx.lineWidth = 3;
            socCtx.strokeRect(250, 50, 500, 350);
            
            // 绘制SoC标题
            socCtx.font = '24px Microsoft YaHei';
            socCtx.fillStyle = '#2d3436';
            socCtx.fillText('片上系统 (SoC)', 400, 30);
            
            // 绘制组件
            drawSoCComponents();
        }
        
        // 绘制嵌入式系统组件
        function drawEmbeddedComponents() {
            // 清除画布
            embeddedCtx.clearRect(0, 0, embeddedCanvas.width, embeddedCanvas.height);
            
            // 绘制标题
            embeddedCtx.font = '24px Microsoft YaHei';
            embeddedCtx.fillStyle = '#2d3436';
            embeddedCtx.fillText('嵌入式系统硬件组成', 300, 30);
            
            // 绘制各组件
            for (const key in embeddedComponents) {
                const comp = embeddedComponents[key];
                
                // 设置填充颜色
                embeddedCtx.fillStyle = comp.highlighted ? '#e84393' : comp.color;
                
                // 绘制组件矩形
                embeddedCtx.fillRect(comp.x, comp.y, comp.width, comp.height);
                
                // 绘制组件边框
                embeddedCtx.strokeStyle = '#2d3436';
                embeddedCtx.lineWidth = 2;
                embeddedCtx.strokeRect(comp.x, comp.y, comp.width, comp.height);
                
                // 绘制组件名称
                embeddedCtx.font = '14px Microsoft YaHei';
                embeddedCtx.fillStyle = '#2d3436';
                embeddedCtx.textAlign = 'center';
                embeddedCtx.fillText(comp.name, comp.x + comp.width/2, comp.y + comp.height/2 + 5);
            }
            
            // 绘制数据包
            if (dataPacket.active) {
                embeddedCtx.fillStyle = '#e74c3c';
                embeddedCtx.beginPath();
                embeddedCtx.arc(dataPacket.x, dataPacket.y, 8, 0, Math.PI * 2);
                embeddedCtx.fill();
            }
        }
        
        // 绘制SoC组件
        function drawSoCComponents() {
            // 清除画布内部（保留SoC外框）
            socCtx.clearRect(251, 51, 498, 348);
            
            // 重新绘制SoC芯片外框
            socCtx.strokeStyle = '#2d3436';
            socCtx.lineWidth = 3;
            socCtx.strokeRect(250, 50, 500, 350);
            
            // 绘制各组件
            for (const key in socComponents) {
                const comp = socComponents[key];
                
                // 设置填充颜色
                socCtx.fillStyle = comp.highlighted ? '#e84393' : comp.color;
                
                // 绘制组件矩形
                socCtx.fillRect(comp.x, comp.y, comp.width, comp.height);
                
                // 绘制组件边框
                socCtx.strokeStyle = '#2d3436';
                socCtx.lineWidth = 2;
                socCtx.strokeRect(comp.x, comp.y, comp.width, comp.height);
                
                // 绘制组件名称
                socCtx.font = '14px Microsoft YaHei';
                socCtx.fillStyle = '#2d3436';
                socCtx.textAlign = 'center';
                socCtx.fillText(comp.name, comp.x + comp.width/2, comp.y + comp.height/2 + 5);
            }
            
            // 绘制组件之间的连接线
            socCtx.strokeStyle = '#636e72';
            socCtx.lineWidth = 2;
            
            // CPU到内存的连接
            socCtx.beginPath();
            socCtx.moveTo(350, 180);
            socCtx.lineTo(350, 220);
            socCtx.lineTo(450, 220);
            socCtx.stroke();
            
            // GPU到内存的连接
            socCtx.beginPath();
            socCtx.moveTo(500, 180);
            socCtx.lineTo(500, 220);
            socCtx.stroke();
            
            // DSP到内存的连接
            socCtx.beginPath();
            socCtx.moveTo(650, 180);
            socCtx.lineTo(650, 220);
            socCtx.lineTo(600, 220);
            socCtx.stroke();
            
            // 内存到外设的连接
            socCtx.beginPath();
            socCtx.moveTo(525, 280);
            socCtx.lineTo(525, 320);
            socCtx.stroke();
            
            // 绘制数据包
            if (dataPacket.active) {
                socCtx.fillStyle = '#e74c3c';
                socCtx.beginPath();
                socCtx.arc(dataPacket.x, dataPacket.y, 8, 0, Math.PI * 2);
                socCtx.fill();
            }
            
            // 绘制集成描述
            socCtx.font = '14px Microsoft YaHei';
            socCtx.fillStyle = '#2d3436';
            socCtx.textAlign = 'left';
            socCtx.fillText('SoC集成了处理器、存储和外设，实现单芯片系统', 300, 410);
        }
        
        // 高亮嵌入式系统组件
        function highlightComponent(component) {
            // 取消之前的高亮
            for (const key in embeddedComponents) {
                embeddedComponents[key].highlighted = false;
            }
            
            // 设置当前组件高亮
            embeddedComponents[component].highlighted = true;
            
            // 重绘组件
            drawEmbeddedComponents();
            
            // 显示组件信息
            const infoPanel = document.getElementById('infoPanel');
            infoPanel.innerHTML = `<h3>${embeddedComponents[component].name}</h3><p>${embeddedComponents[component].info}</p>`;
            infoPanel.style.display = 'block';
        }
        
        // 高亮SoC组件
        function highlightSoCComponent(component) {
            // 取消之前的高亮
            for (const key in socComponents) {
                socComponents[key].highlighted = false;
            }
            
            // 设置当前组件高亮
            socComponents[component].highlighted = true;
            
            // 重绘组件
            drawSoCComponents();
            
            // 显示组件信息
            const socInfoPanel = document.getElementById('socInfoPanel');
            socInfoPanel.innerHTML = `<h3>${socComponents[component].name}</h3><p>${socComponents[component].info}</p>`;
            socInfoPanel.style.display = 'block';
        }
        
        // 重置画布
        function resetCanvas(type) {
            // 取消动画
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            
            dataPacket = { x: 0, y: 0, active: false };
            
            if (type === 'embedded') {
                // 重置嵌入式系统画布
                for (const key in embeddedComponents) {
                    embeddedComponents[key].highlighted = false;
                }
                initEmbeddedCanvas();
                document.getElementById('infoPanel').style.display = 'none';
            } else if (type === 'soc') {
                // 重置SoC画布
                for (const key in socComponents) {
                    socComponents[key].highlighted = false;
                }
                initSoCCanvas();
                document.getElementById('socInfoPanel').style.display = 'none';
            }
        }
        
        // 嵌入式系统动画
        function animateEmbeddedSystem() {
            // 取消之前的动画
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
                resetCanvas('embedded');
                return;
            }
            
            // 动画数据包的初始位置（从MCU开始）
            dataPacket = {
                x: embeddedComponents.mcu.x + embeddedComponents.mcu.width / 2,
                y: embeddedComponents.mcu.y + embeddedComponents.mcu.height / 2,
                active: true,
                step: 0
            };
            
            // 定义动画路径
            const path = [
                // 从MCU到总线
                { target: { x: embeddedComponents.mcu.x + embeddedComponents.mcu.width / 2, y: embeddedComponents.bus.y + 10 } },
                // 总线到存储器
                { target: { x: embeddedComponents.memory.x + embeddedComponents.memory.width / 2, y: embeddedComponents.bus.y + 10 } },
                // 存储器到存储器中部
                { target: { x: embeddedComponents.memory.x + embeddedComponents.memory.width / 2, y: embeddedComponents.memory.y + embeddedComponents.memory.height / 2 } },
                // 返回总线
                { target: { x: embeddedComponents.memory.x + embeddedComponents.memory.width / 2, y: embeddedComponents.bus.y + 10 } },
                // 总线到I/O接口
                { target: { x: embeddedComponents.io.x + embeddedComponents.io.width / 2, y: embeddedComponents.bus.y + 10 } },
                // I/O接口中部
                { target: { x: embeddedComponents.io.x + embeddedComponents.io.width / 2, y: embeddedComponents.io.y + embeddedComponents.io.height / 2 } },
                // 返回总线
                { target: { x: embeddedComponents.io.x + embeddedComponents.io.width / 2, y: embeddedComponents.bus.y + 10 } },
                // 总线到MCU
                { target: { x: embeddedComponents.mcu.x + embeddedComponents.mcu.width / 2, y: embeddedComponents.bus.y + 10 } },
                // 回到MCU
                { target: { x: embeddedComponents.mcu.x + embeddedComponents.mcu.width / 2, y: embeddedComponents.mcu.y + embeddedComponents.mcu.height / 2 } }
            ];
            
            // 显示动画说明
            const infoPanel = document.getElementById('infoPanel');
            infoPanel.innerHTML = `<h3>动画演示</h3><p>此动画展示了数据在嵌入式系统中的流动过程。数据从微控制器发出，通过总线传输到存储器和I/O接口，然后返回微控制器。</p>`;
            infoPanel.style.display = 'block';
            
            // 高亮相关组件
            embeddedComponents.mcu.highlighted = true;
            
            // 开始动画
            animatePacket(path, 0, 'embedded');
        }
        
        // SoC动画
        function animateSoC() {
            // 取消之前的动画
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
                resetCanvas('soc');
                return;
            }
            
            // 动画数据包的初始位置（从CPU开始）
            dataPacket = {
                x: socComponents.cpu.x + socComponents.cpu.width / 2,
                y: socComponents.cpu.y + socComponents.cpu.height / 2,
                active: true,
                step: 0
            };
            
            // 定义动画路径
            const path = [
                // CPU到内存连接处
                { target: { x: 350, y: 220 } },
                // 沿连接到内存
                { target: { x: 450, y: 220 } },
                // 内存中部
                { target: { x: socComponents.memory.x + socComponents.memory.width / 2, y: socComponents.memory.y + socComponents.memory.height / 2 } },
                // 到GPU连接处
                { target: { x: 500, y: 220 } },
                // 上到GPU
                { target: { x: 500, y: socComponents.gpu.y + socComponents.gpu.height } },
                // GPU中部
                { target: { x: socComponents.gpu.x + socComponents.gpu.width / 2, y: socComponents.gpu.y + socComponents.gpu.height / 2 } },
                // 返回到内存连接处
                { target: { x: 500, y: 220 } },
                // 内存到外设连接
                { target: { x: 525, y: 280 } },
                // 到外设
                { target: { x: 525, y: 320 } },
                // 外设中部
                { target: { x: socComponents.peripheral.x + socComponents.peripheral.width / 2, y: socComponents.peripheral.y + socComponents.peripheral.height / 2 } },
                // 返回到连接处
                { target: { x: 525, y: 320 } },
                // 回到内存
                { target: { x: 525, y: 250 } },
                // 到DSP连接处
                { target: { x: 600, y: 220 } },
                // 到DSP
                { target: { x: 650, y: 220 } },
                // 上到DSP
                { target: { x: 650, y: socComponents.dsp.y + socComponents.dsp.height } },
                // DSP中部
                { target: { x: socComponents.dsp.x + socComponents.dsp.width / 2, y: socComponents.dsp.y + socComponents.dsp.height / 2 } },
                // 返回连接处
                { target: { x: 650, y: 220 } },
                // 回到CPU连接
                { target: { x: 350, y: 220 } },
                // 到CPU
                { target: { x: 350, y: socComponents.cpu.y + socComponents.cpu.height } },
                // CPU中部
                { target: { x: socComponents.cpu.x + socComponents.cpu.width / 2, y: socComponents.cpu.y + socComponents.cpu.height / 2 } }
            ];
            
            // 显示动画说明
            const socInfoPanel = document.getElementById('socInfoPanel');
            socInfoPanel.innerHTML = `<h3>动画演示</h3><p>此动画展示了数据在SoC中的流动过程。数据从CPU发出，通过片内总线传输到内存、GPU、外设接口和DSP，展示了SoC内部组件的协同工作方式。</p>`;
            socInfoPanel.style.display = 'block';
            
            // 高亮相关组件
            socComponents.cpu.highlighted = true;
            
            // 开始动画
            animatePacket(path, 0, 'soc');
        }
        
        // 数据包动画函数
        function animatePacket(path, pathIndex, type) {
            if (pathIndex >= path.length) {
                // 动画完成，重置
                dataPacket.active = false;
                animationId = null;
                
                // 取消所有高亮
                if (type === 'embedded') {
                    for (const key in embeddedComponents) {
                        embeddedComponents[key].highlighted = false;
                    }
                    drawEmbeddedComponents();
                } else {
                    for (const key in socComponents) {
                        socComponents[key].highlighted = false;
                    }
                    drawSoCComponents();
                }
                
                return;
            }
            
            const target = path[pathIndex].target;
            const speed = 3;
            
            // 计算方向
            const dx = target.x - dataPacket.x;
            const dy = target.y - dataPacket.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < speed) {
                // 到达目标点
                dataPacket.x = target.x;
                dataPacket.y = target.y;
                
                // 更新组件高亮状态
                if (type === 'embedded') {
                    updateEmbeddedHighlight(pathIndex);
                    drawEmbeddedComponents();
                } else {
                    updateSoCHighlight(pathIndex);
                    drawSoCComponents();
                }
                
                // 延迟后移动到下一点
                setTimeout(() => {
                    animatePacket(path, pathIndex + 1, type);
                }, 500);
                
                return;
            }
            
            // 移动数据包
            dataPacket.x += (dx / distance) * speed;
            dataPacket.y += (dy / distance) * speed;
            
            // 绘制更新后的场景
            if (type === 'embedded') {
                drawEmbeddedComponents();
            } else {
                drawSoCComponents();
            }
            
            // 继续动画
            animationId = requestAnimationFrame(() => animatePacket(path, pathIndex, type));
        }
        
        // 更新嵌入式系统组件高亮
        function updateEmbeddedHighlight(pathIndex) {
            // 重置所有高亮
            for (const key in embeddedComponents) {
                embeddedComponents[key].highlighted = false;
            }
            
            // 根据路径索引设置高亮
            if (pathIndex < 2) {
                // MCU到总线到存储器
                embeddedComponents.mcu.highlighted = pathIndex === 0;
                embeddedComponents.bus.highlighted = pathIndex === 1;
                embeddedComponents.memory.highlighted = pathIndex === 2;
            } else if (pathIndex < 5) {
                // 存储器到总线到I/O
                embeddedComponents.memory.highlighted = pathIndex === 2;
                embeddedComponents.bus.highlighted = pathIndex === 3 || pathIndex === 4;
                embeddedComponents.io.highlighted = pathIndex === 5;
            } else {
                // I/O到总线到MCU
                embeddedComponents.io.highlighted = pathIndex === 5;
                embeddedComponents.bus.highlighted = pathIndex === 6 || pathIndex === 7;
                embeddedComponents.mcu.highlighted = pathIndex === 8;
            }
        }
        
        // 更新SoC组件高亮
        function updateSoCHighlight(pathIndex) {
            // 重置所有高亮
            for (const key in socComponents) {
                socComponents[key].highlighted = false;
            }
            
            // 根据路径索引设置高亮
            if (pathIndex < 3) {
                // CPU到内存
                socComponents.cpu.highlighted = pathIndex === 0;
                socComponents.memory.highlighted = pathIndex >= 2 && pathIndex <= 3;
            } else if (pathIndex < 7) {
                // 内存到GPU
                socComponents.memory.highlighted = pathIndex === 3;
                socComponents.gpu.highlighted = pathIndex >= 4 && pathIndex <= 6;
            } else if (pathIndex < 11) {
                // GPU到外设
                socComponents.peripheral.highlighted = pathIndex >= 9 && pathIndex <= 10;
            } else if (pathIndex < 17) {
                // 外设到DSP
                socComponents.memory.highlighted = pathIndex >= 11 && pathIndex <= 12;
                socComponents.dsp.highlighted = pathIndex >= 14 && pathIndex <= 16;
            } else {
                // DSP到CPU
                socComponents.cpu.highlighted = pathIndex >= 19;
            }
        }
    </script>
</body>
</html> 