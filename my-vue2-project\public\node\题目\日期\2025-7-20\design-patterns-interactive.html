<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计模式交互式学习 - 桥接模式专题</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.2rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .navigation {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            animation: slideInUp 1s ease-out 0.3s both;
        }

        .nav-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
        }

        .nav-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .nav-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        .nav-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .nav-item h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3rem;
        }

        .nav-item p {
            color: #666;
            line-height: 1.5;
            font-size: 0.95rem;
        }

        .content-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            display: none;
            animation: fadeInUp 0.6s ease-out;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
        }

        .demo-canvas {
            width: 100%;
            max-width: 800px;
            height: 400px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            margin: 20px auto;
            display: block;
            background: white;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .explanation {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }

        .explanation h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .explanation p {
            color: #555;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 设计模式交互式学习</h1>
            <p>通过动画和交互深入理解桥接模式 - 让抽象与实现优雅分离</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <!-- 原题目展示 -->
        <div class="navigation">
            <h2 class="nav-title">📝 原题目</h2>
            <div class="explanation">
                <h4>题目背景</h4>
                <p>某广告公司的宣传产品有<span class="highlight">宣传册、文章、传单</span>等多种形式，宣传产品的出版方式包括<span class="highlight">纸质方式、CD、DVD、在线发布</span>等。现要求为该广告公司设计一个管理这些宣传产品的应用，采用（）设计模式较为合适，该模式（作答此空）。</p>

                <h4>选项</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #dc3545;">
                        <strong>A.</strong> 将一系列复杂的类包装成一个简单的封闭接口
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #28a745;">
                        <strong>B.</strong> 将抽象部分与它的实现部分分离，使它们都可以独立地变化
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #ffc107;">
                        <strong>C.</strong> 可在不影响其他对象的情况下，以动态、透明的方式给单个对象添加职责
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; border-left: 4px solid #17a2b8;">
                        <strong>D.</strong> 将一个接口转换为客户希望的另一个接口
                    </div>
                </div>

                <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 15px; text-align: center; margin: 20px 0;">
                    <h4 style="margin: 0 0 10px 0; color: white;">✅ 正确答案：B</h4>
                    <p style="margin: 0; font-size: 18px; font-weight: bold;">桥接模式 - 将抽象部分与它的实现部分分离，使它们都可以独立地变化</p>
                </div>

                <h4>为什么选择桥接模式？</h4>
                <p>在这个场景中，我们有两个独立变化的维度：</p>
                <p>🎭 <strong>产品维度：</strong>宣传册、文章、传单（可能还会增加海报、视频等）</p>
                <p>📡 <strong>出版维度：</strong>纸质、CD、DVD、在线发布（可能还会增加APP推送、社交媒体等）</p>
                <p>如果使用传统的继承方式，需要创建 3×4=12 个类。而桥接模式只需要 3+4=7 个类，并且可以灵活组合。</p>
            </div>
        </div>

        <div class="navigation">
            <h2 class="nav-title">📚 学习目录</h2>
            <div class="nav-grid">
                <div class="nav-item" onclick="showSection('intro')">
                    <h3>🌟 设计模式概述</h3>
                    <p>了解设计模式的基本概念、分类和重要性</p>
                </div>
                <div class="nav-item" onclick="showSection('problem')">
                    <h3>🤔 问题场景</h3>
                    <p>广告公司宣传产品管理的实际问题分析</p>
                </div>
                <div class="nav-item" onclick="showSection('bridge-intro')">
                    <h3>🌉 桥接模式介绍</h3>
                    <p>桥接模式的定义、结构和核心思想</p>
                </div>

        <!-- 设计模式概述 -->
        <div class="content-section" id="intro">
            <h2 class="section-title">🌟 设计模式概述</h2>
            <canvas class="demo-canvas" id="introCanvas"></canvas>
            <div class="explanation">
                <h4>什么是设计模式？</h4>
                <p>设计模式是在软件设计中常见问题的<span class="highlight">可复用解决方案</span>。它们是经过验证的设计经验的总结，帮助我们写出更灵活、可维护的代码。</p>
                <h4>设计模式的三大类型：</h4>
                <p><strong>🏗️ 创建型模式：</strong>关注对象的创建过程，如工厂模式、单例模式</p>
                <p><strong>🔗 结构型模式：</strong>关注类和对象的组合，如桥接模式、适配器模式</p>
                <p><strong>🎭 行为型模式：</strong>关注对象间的通信，如观察者模式、策略模式</p>
            </div>
            <div class="controls">
                <button class="btn" onclick="animatePatternTypes()">🎬 播放动画</button>
                <button class="btn" onclick="resetIntroAnimation()">🔄 重置</button>
            </div>
        </div>

        <!-- 问题场景 -->
        <div class="content-section" id="problem">
            <h2 class="section-title">🤔 问题场景分析</h2>
            <canvas class="demo-canvas" id="problemCanvas"></canvas>
            <div class="explanation">
                <h4>广告公司的挑战</h4>
                <p>某广告公司需要管理多种<span class="highlight">宣传产品</span>（宣传册、文章、传单）和多种<span class="highlight">出版方式</span>（纸质、CD、DVD、在线发布）。</p>
                <h4>传统方案的问题：</h4>
                <p>❌ 如果为每种产品和出版方式的组合都创建一个类，会产生类爆炸问题</p>
                <p>❌ 宣传册纸质版、宣传册CD版、宣传册DVD版... 类数量 = 产品数 × 出版方式数</p>
                <p>❌ 添加新产品或新出版方式需要修改大量代码</p>
                <h4>我们需要的解决方案：</h4>
                <p>✅ 将产品类型和出版方式<span class="highlight">解耦</span></p>
                <p>✅ 可以独立扩展产品和出版方式</p>
                <p>✅ 避免类爆炸问题</p>
            </div>
            <div class="controls">
                <button class="btn" onclick="showProblemDemo()">📊 展示问题</button>
                <button class="btn" onclick="showSolutionPreview()">💡 解决方案预览</button>
            </div>
        </div>

        <!-- 桥接模式介绍 -->
        <div class="content-section" id="bridge-intro">
            <h2 class="section-title">🌉 桥接模式详解</h2>
            <canvas class="demo-canvas" id="bridgeIntroCanvas"></canvas>
            <div class="explanation">
                <h4>桥接模式定义</h4>
                <p>桥接模式将<span class="highlight">抽象部分</span>与它的<span class="highlight">实现部分</span>分离，使它们都可以独立地变化。</p>
                <h4>核心组件：</h4>
                <p><strong>🎭 抽象类（Abstraction）：</strong>定义抽象类的接口，维护一个指向实现类的引用</p>
                <p><strong>🎪 扩展抽象类（RefinedAbstraction）：</strong>扩展抽象类，实现具体功能</p>
                <p><strong>🔧 实现接口（Implementor）：</strong>定义实现类的接口</p>
                <p><strong>⚙️ 具体实现（ConcreteImplementor）：</strong>实现具体的实现类</p>
                <h4>关键思想：</h4>
                <p>通过<span class="highlight">组合</span>而不是继承来连接抽象和实现，实现真正的解耦。</p>
            </div>
            <div class="controls">
                <button class="btn" onclick="animateBridgeStructure()">🏗️ 结构动画</button>
                <button class="btn" onclick="showBridgeFlow()">🔄 工作流程</button>
            </div>
        </div>

        <!-- 交互式演示 -->
        <div class="content-section" id="bridge-demo">
            <h2 class="section-title">🎭 桥接模式交互演示</h2>
            <canvas class="demo-canvas" id="bridgeDemoCanvas"></canvas>
            <div class="explanation">
                <h4>实际应用演示</h4>
                <p>在这个演示中，您可以看到宣传产品（抽象）如何通过桥接与出版方式（实现）连接。</p>
                <p>点击不同的按钮，观察<span class="highlight">抽象与实现的独立变化</span>。</p>
            </div>
            <div class="controls">
                <button class="btn" onclick="createProduct('brochure')">📖 创建宣传册</button>
                <button class="btn" onclick="createProduct('article')">📄 创建文章</button>
                <button class="btn" onclick="createProduct('flyer')">📋 创建传单</button>
                <button class="btn" onclick="changePublishMethod('paper')">📰 纸质出版</button>
                <button class="btn" onclick="changePublishMethod('cd')">💿 CD出版</button>
                <button class="btn" onclick="changePublishMethod('online')">🌐 在线发布</button>
                <button class="btn" onclick="demonstrateDecoupling()">🔗 展示解耦</button>
            </div>
        </div>

        <!-- 代码实现 -->
        <div class="content-section" id="implementation">
            <h2 class="section-title">💻 桥接模式代码实现</h2>
            <div class="explanation">
                <h4>JavaScript实现示例</h4>
                <div class="code-block">
// 实现接口 - 出版方式
class PublishImplementor {
    publish(content) {
        throw new Error("子类必须实现publish方法");
    }
}

// 具体实现 - 纸质出版
class PaperPublish extends PublishImplementor {
    publish(content) {
        return `📰 纸质出版: ${content}`;
    }
}

// 具体实现 - CD出版
class CDPublish extends PublishImplementor {
    publish(content) {
        return `💿 CD出版: ${content}`;
    }
}

// 抽象类 - 宣传产品
class PromotionProduct {
    constructor(publishImplementor) {
        this.publishImplementor = publishImplementor;
    }

    produce() {
        throw new Error("子类必须实现produce方法");
    }
}

// 扩展抽象类 - 宣传册
class Brochure extends PromotionProduct {
    constructor(publishImplementor) {
        super(publishImplementor);
    }

    produce() {
        const content = "精美宣传册内容";
        return this.publishImplementor.publish(content);
    }
}
                </div>
                <p>这个实现展示了如何将<span class="highlight">产品类型</span>和<span class="highlight">出版方式</span>完全分离。</p>
            </div>
            <canvas class="demo-canvas" id="implementationCanvas"></canvas>
            <div class="controls">
                <button class="btn" onclick="runCodeDemo()">▶️ 运行代码</button>
                <button class="btn" onclick="showCodeFlow()">🔍 代码流程</button>
            </div>
        </div>

        <!-- 优势分析 -->
        <div class="content-section" id="advantages">
            <h2 class="section-title">✨ 桥接模式的优势</h2>
            <canvas class="demo-canvas" id="advantagesCanvas"></canvas>
            <div class="explanation">
                <h4>主要优势：</h4>
                <p><strong>🔄 分离抽象接口及其实现部分：</strong>提高了系统的可扩充性</p>
                <p><strong>🎯 提高可扩充性：</strong>在两个变化维度中任意扩展一个维度，都不需要修改原有系统</p>
                <p><strong>🎭 实现细节对客户透明：</strong>可以对用户隐藏实现细节</p>
                <h4>适用场景：</h4>
                <p>✅ 不希望在抽象和实现部分之间有固定的绑定关系</p>
                <p>✅ 类的抽象以及它的实现都应该可以通过生成子类的方法加以扩充</p>
                <p>✅ 对一个抽象的实现部分的修改应该对客户不产生影响</p>
                <p>✅ 有许多类要生成的情况（避免类爆炸）</p>
            </div>
            <div class="controls">
                <button class="btn" onclick="showAdvantagesDemo()">🎬 优势演示</button>
                <button class="btn" onclick="compareWithoutBridge()">⚖️ 对比传统方案</button>
            </div>
        </div>

        <!-- 模式对比 -->
        <div class="content-section" id="comparison">
            <h2 class="section-title">⚖️ 设计模式对比</h2>
            <canvas class="demo-canvas" id="comparisonCanvas"></canvas>
            <div class="explanation">
                <h4>题目中的其他选项分析：</h4>
                <p><strong>A. 外观模式（Facade）：</strong>将一系列复杂的类包装成一个简单的封闭接口</p>
                <p><strong>C. 装饰器模式（Decorator）：</strong>可在不影响其他对象的情况下，以动态、透明的方式给单个对象添加职责</p>
                <p><strong>D. 适配器模式（Adapter）：</strong>将一个接口转换为客户希望的另一个接口</p>
                <h4>为什么选择桥接模式？</h4>
                <p>在广告公司的场景中，我们有两个独立变化的维度：</p>
                <p>🎭 <span class="highlight">产品类型</span>：宣传册、文章、传单</p>
                <p>📡 <span class="highlight">出版方式</span>：纸质、CD、DVD、在线</p>
                <p>桥接模式正是为了处理这种<span class="highlight">多维度变化</span>而设计的！</p>
            </div>
            <div class="controls">
                <button class="btn" onclick="showPatternComparison()">🔍 模式对比</button>
                <button class="btn" onclick="highlightBridgeAdvantage()">🌟 突出桥接优势</button>
            </div>
        </div>

        <!-- 实践练习 -->
        <div class="content-section" id="practice">
            <h2 class="section-title">🎯 实践练习与总结</h2>
            <canvas class="demo-canvas" id="practiceCanvas"></canvas>
            <div class="explanation">
                <h4>知识回顾：</h4>
                <p>1. 桥接模式的核心思想是什么？</p>
                <p>2. 在什么情况下应该使用桥接模式？</p>
                <p>3. 桥接模式如何解决类爆炸问题？</p>
                <h4>扩展思考：</h4>
                <p>🤔 如果广告公司要添加新的产品类型"海报"，需要修改哪些代码？</p>
                <p>🤔 如果要添加新的出版方式"APP推送"，又需要修改哪些代码？</p>
                <p>🤔 这体现了桥接模式的什么优势？</p>
            </div>
            <div class="controls">
                <button class="btn" onclick="startQuiz()">📝 开始测验</button>
                <button class="btn" onclick="showSummary()">📋 学习总结</button>
                <button class="btn" onclick="celebrateCompletion()">🎉 完成学习</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentSection = 'intro';
        let animationStates = {};
        let canvases = {};
        let contexts = {};

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCanvases();
            showSection('intro');
        });

        // 初始化所有画布
        function initializeCanvases() {
            const canvasIds = ['introCanvas', 'problemCanvas', 'bridgeIntroCanvas',
                             'bridgeDemoCanvas', 'implementationCanvas', 'advantagesCanvas',
                             'comparisonCanvas', 'practiceCanvas'];

            canvasIds.forEach(id => {
                const canvas = document.getElementById(id);
                if (canvas) {
                    canvases[id] = canvas;
                    contexts[id] = canvas.getContext('2d');

                    // 设置画布尺寸
                    const rect = canvas.getBoundingClientRect();
                    canvas.width = rect.width * window.devicePixelRatio;
                    canvas.height = rect.height * window.devicePixelRatio;
                    contexts[id].scale(window.devicePixelRatio, window.devicePixelRatio);

                    // 设置默认样式
                    contexts[id].font = '16px Microsoft YaHei';
                    contexts[id].textAlign = 'center';
                    contexts[id].textBaseline = 'middle';
                }
            });
        }

        // 显示指定章节
        function showSection(sectionId) {
            // 隐藏所有章节
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // 显示指定章节
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
                currentSection = sectionId;

                // 更新进度条
                updateProgress(sectionId);

                // 初始化该章节的动画
                initSectionAnimation(sectionId);
            }
        }

        // 更新进度条
        function updateProgress(sectionId) {
            const sections = ['intro', 'problem', 'bridge-intro', 'bridge-demo',
                            'implementation', 'advantages', 'comparison', 'practice'];
            const currentIndex = sections.indexOf(sectionId);
            const progress = ((currentIndex + 1) / sections.length) * 100;

            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 初始化章节动画
        function initSectionAnimation(sectionId) {
            switch(sectionId) {
                case 'intro':
                    drawIntroCanvas();
                    break;
                case 'problem':
                    drawProblemCanvas();
                    break;
                case 'bridge-intro':
                    drawBridgeIntroCanvas();
                    break;
                case 'bridge-demo':
                    drawBridgeDemoCanvas();
                    break;
                case 'implementation':
                    drawImplementationCanvas();
                    break;
                case 'advantages':
                    drawAdvantagesCanvas();
                    break;
                case 'comparison':
                    drawComparisonCanvas();
                    break;
                case 'practice':
                    drawPracticeCanvas();
                    break;
            }
        }

        // 绘制介绍页面
        function drawIntroCanvas() {
            const ctx = contexts['introCanvas'];
            const canvas = canvases['introCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 绘制设计模式分类
            const categories = [
                { name: '创建型', icon: '🏗️', color: '#FF6B6B', x: width * 0.2, y: height * 0.5 },
                { name: '结构型', icon: '🔗', color: '#4ECDC4', x: width * 0.5, y: height * 0.5 },
                { name: '行为型', icon: '🎭', color: '#45B7D1', x: width * 0.8, y: height * 0.5 }
            ];

            categories.forEach((cat, index) => {
                // 绘制圆形背景
                ctx.beginPath();
                ctx.arc(cat.x, cat.y, 60, 0, Math.PI * 2);
                ctx.fillStyle = cat.color;
                ctx.fill();

                // 绘制图标
                ctx.font = '30px Arial';
                ctx.fillStyle = 'white';
                ctx.fillText(cat.icon, cat.x, cat.y - 10);

                // 绘制名称
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText(cat.name, cat.x, cat.y + 20);
            });

            // 绘制标题
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('设计模式三大类型', width / 2, height * 0.15);
        }

        // 设计模式类型动画
        function animatePatternTypes() {
            const ctx = contexts['introCanvas'];
            const canvas = canvases['introCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            let frame = 0;
            const maxFrames = 120;

            function animate() {
                ctx.clearRect(0, 0, width, height);

                const progress = frame / maxFrames;
                const categories = [
                    { name: '创建型', icon: '🏗️', color: '#FF6B6B', x: width * 0.2, y: height * 0.5, delay: 0 },
                    { name: '结构型', icon: '🔗', color: '#4ECDC4', x: width * 0.5, y: height * 0.5, delay: 20 },
                    { name: '行为型', icon: '🎭', color: '#45B7D1', x: width * 0.8, y: height * 0.5, delay: 40 }
                ];

                categories.forEach((cat, index) => {
                    const catProgress = Math.max(0, Math.min(1, (frame - cat.delay) / 60));
                    const scale = 0.3 + 0.7 * catProgress;
                    const alpha = catProgress;

                    ctx.save();
                    ctx.globalAlpha = alpha;
                    ctx.translate(cat.x, cat.y);
                    ctx.scale(scale, scale);

                    // 绘制圆形背景
                    ctx.beginPath();
                    ctx.arc(0, 0, 60, 0, Math.PI * 2);
                    ctx.fillStyle = cat.color;
                    ctx.fill();

                    // 绘制图标
                    ctx.font = '30px Arial';
                    ctx.fillStyle = 'white';
                    ctx.fillText(cat.icon, 0, -10);

                    // 绘制名称
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillText(cat.name, 0, 20);

                    ctx.restore();
                });

                // 绘制标题
                ctx.font = 'bold 24px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.fillText('设计模式三大类型', width / 2, height * 0.15);

                frame++;
                if (frame <= maxFrames) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 重置介绍动画
        function resetIntroAnimation() {
            drawIntroCanvas();
        }

        // 绘制问题场景画布
        function drawProblemCanvas() {
            const ctx = contexts['problemCanvas'];
            const canvas = canvases['problemCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 绘制产品类型
            const products = ['宣传册', '文章', '传单'];
            const publishMethods = ['纸质', 'CD', 'DVD', '在线'];

            // 绘制产品
            products.forEach((product, i) => {
                const x = width * 0.15;
                const y = height * 0.2 + i * height * 0.25;

                ctx.fillStyle = '#FF6B6B';
                ctx.fillRect(x - 40, y - 15, 80, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(product, x, y);
            });

            // 绘制出版方式
            publishMethods.forEach((method, i) => {
                const x = width * 0.85;
                const y = height * 0.15 + i * height * 0.2;

                ctx.fillStyle = '#4ECDC4';
                ctx.fillRect(x - 30, y - 15, 60, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(method, x, y);
            });

            // 绘制连接线（表示类爆炸）
            ctx.strokeStyle = '#999';
            ctx.lineWidth = 1;
            products.forEach((product, i) => {
                publishMethods.forEach((method, j) => {
                    const startX = width * 0.15 + 40;
                    const startY = height * 0.2 + i * height * 0.25;
                    const endX = width * 0.85 - 30;
                    const endY = height * 0.15 + j * height * 0.2;

                    ctx.beginPath();
                    ctx.moveTo(startX, startY);
                    ctx.lineTo(endX, endY);
                    ctx.stroke();
                });
            });

            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('传统方案：类爆炸问题', width / 2, height * 0.05);

            // 绘制说明
            ctx.font = '16px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.fillText('产品类型', width * 0.15, height * 0.1);
            ctx.fillText('出版方式', width * 0.85, height * 0.1);
            ctx.fillText(`总类数：${products.length} × ${publishMethods.length} = ${products.length * publishMethods.length}`, width / 2, height * 0.9);
        }

        // 显示问题演示
        function showProblemDemo() {
            const ctx = contexts['problemCanvas'];
            const canvas = canvases['problemCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            let frame = 0;
            const maxFrames = 180;

            function animate() {
                ctx.clearRect(0, 0, width, height);

                const products = ['宣传册', '文章', '传单'];
                const publishMethods = ['纸质', 'CD', 'DVD', '在线'];

                // 绘制产品
                products.forEach((product, i) => {
                    const x = width * 0.15;
                    const y = height * 0.2 + i * height * 0.25;

                    ctx.fillStyle = '#FF6B6B';
                    ctx.fillRect(x - 40, y - 15, 80, 30);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(product, x, y);
                });

                // 绘制出版方式
                publishMethods.forEach((method, i) => {
                    const x = width * 0.85;
                    const y = height * 0.15 + i * height * 0.2;

                    ctx.fillStyle = '#4ECDC4';
                    ctx.fillRect(x - 30, y - 15, 60, 30);
                    ctx.fillStyle = 'white';
                    ctx.font = '14px Microsoft YaHei';
                    ctx.fillText(method, x, y);
                });

                // 动画绘制连接线
                const totalLines = products.length * publishMethods.length;
                const linesDrawn = Math.floor((frame / maxFrames) * totalLines);

                ctx.strokeStyle = '#FF4444';
                ctx.lineWidth = 2;
                let lineCount = 0;

                products.forEach((product, i) => {
                    publishMethods.forEach((method, j) => {
                        if (lineCount < linesDrawn) {
                            const startX = width * 0.15 + 40;
                            const startY = height * 0.2 + i * height * 0.25;
                            const endX = width * 0.85 - 30;
                            const endY = height * 0.15 + j * height * 0.2;

                            ctx.beginPath();
                            ctx.moveTo(startX, startY);
                            ctx.lineTo(endX, endY);
                            ctx.stroke();
                        }
                        lineCount++;
                    });
                });

                // 绘制标题
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.fillText('传统方案：类爆炸问题', width / 2, height * 0.05);

                // 绘制说明
                ctx.font = '16px Microsoft YaHei';
                ctx.fillStyle = '#666';
                ctx.fillText('产品类型', width * 0.15, height * 0.1);
                ctx.fillText('出版方式', width * 0.85, height * 0.1);

                // 动态显示类数量
                const currentClasses = Math.min(linesDrawn, totalLines);
                ctx.fillStyle = '#FF4444';
                ctx.fillText(`当前类数：${currentClasses}`, width / 2, height * 0.9);

                frame++;
                if (frame <= maxFrames) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 显示解决方案预览
        function showSolutionPreview() {
            const ctx = contexts['problemCanvas'];
            const canvas = canvases['problemCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 绘制桥接模式结构
            // 抽象层
            ctx.fillStyle = '#FF6B6B';
            ctx.fillRect(width * 0.1, height * 0.3, width * 0.35, height * 0.4);
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('产品抽象层', width * 0.275, height * 0.5);

            // 实现层
            ctx.fillStyle = '#4ECDC4';
            ctx.fillRect(width * 0.55, height * 0.3, width * 0.35, height * 0.4);
            ctx.fillStyle = 'white';
            ctx.fillText('出版实现层', width * 0.725, height * 0.5);

            // 桥接线
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(width * 0.45, height * 0.5);
            ctx.lineTo(width * 0.55, height * 0.5);
            ctx.stroke();

            // 绘制桥接图标
            ctx.font = '30px Arial';
            ctx.fillStyle = '#333';
            ctx.fillText('🌉', width * 0.5, height * 0.45);

            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('桥接模式：优雅解耦', width / 2, height * 0.15);

            // 绘制优势说明
            ctx.font = '14px Microsoft YaHei';
            ctx.fillStyle = '#666';
            ctx.fillText('✅ 独立扩展', width * 0.275, height * 0.8);
            ctx.fillText('✅ 避免类爆炸', width * 0.725, height * 0.8);
        }

        // 绘制桥接模式介绍画布
        function drawBridgeIntroCanvas() {
            const ctx = contexts['bridgeIntroCanvas'];
            const canvas = canvases['bridgeIntroCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 绘制桥接模式结构图
            const components = [
                { name: 'Abstraction\n(抽象类)', x: width * 0.2, y: height * 0.3, color: '#FF6B6B' },
                { name: 'RefinedAbstraction\n(扩展抽象类)', x: width * 0.2, y: height * 0.7, color: '#FF8E8E' },
                { name: 'Implementor\n(实现接口)', x: width * 0.8, y: height * 0.3, color: '#4ECDC4' },
                { name: 'ConcreteImplementor\n(具体实现)', x: width * 0.8, y: height * 0.7, color: '#6ED4D4' }
            ];

            components.forEach(comp => {
                // 绘制组件框
                ctx.fillStyle = comp.color;
                ctx.fillRect(comp.x - 80, comp.y - 30, 160, 60);

                // 绘制组件名称
                ctx.fillStyle = 'white';
                ctx.font = '12px Microsoft YaHei';
                const lines = comp.name.split('\n');
                lines.forEach((line, i) => {
                    ctx.fillText(line, comp.x, comp.y - 10 + i * 20);
                });
            });

            // 绘制继承关系（垂直线）
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            // 抽象类继承
            ctx.beginPath();
            ctx.moveTo(width * 0.2, height * 0.36);
            ctx.lineTo(width * 0.2, height * 0.64);
            ctx.stroke();

            // 实现类继承
            ctx.beginPath();
            ctx.moveTo(width * 0.8, height * 0.36);
            ctx.lineTo(width * 0.8, height * 0.64);
            ctx.stroke();

            // 绘制桥接关系（水平线）
            ctx.strokeStyle = '#FF4444';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(width * 0.28, height * 0.3);
            ctx.lineTo(width * 0.72, height * 0.3);
            ctx.stroke();

            // 绘制桥接标签
            ctx.fillStyle = '#FF4444';
            ctx.font = 'bold 14px Microsoft YaHei';
            ctx.fillText('桥接关系', width * 0.5, height * 0.25);

            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('桥接模式结构图', width / 2, height * 0.1);
        }

        // 桥接结构动画
        function animateBridgeStructure() {
            const ctx = contexts['bridgeIntroCanvas'];
            const canvas = canvases['bridgeIntroCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            let frame = 0;
            const maxFrames = 200;

            function animate() {
                ctx.clearRect(0, 0, width, height);

                const progress = frame / maxFrames;
                const components = [
                    { name: 'Abstraction\n(抽象类)', x: width * 0.2, y: height * 0.3, color: '#FF6B6B', delay: 0 },
                    { name: 'RefinedAbstraction\n(扩展抽象类)', x: width * 0.2, y: height * 0.7, color: '#FF8E8E', delay: 40 },
                    { name: 'Implementor\n(实现接口)', x: width * 0.8, y: height * 0.3, color: '#4ECDC4', delay: 80 },
                    { name: 'ConcreteImplementor\n(具体实现)', x: width * 0.8, y: height * 0.7, color: '#6ED4D4', delay: 120 }
                ];

                components.forEach(comp => {
                    const compProgress = Math.max(0, Math.min(1, (frame - comp.delay) / 40));
                    const scale = 0.3 + 0.7 * compProgress;
                    const alpha = compProgress;

                    ctx.save();
                    ctx.globalAlpha = alpha;
                    ctx.translate(comp.x, comp.y);
                    ctx.scale(scale, scale);

                    // 绘制组件框
                    ctx.fillStyle = comp.color;
                    ctx.fillRect(-80, -30, 160, 60);

                    // 绘制组件名称
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Microsoft YaHei';
                    const lines = comp.name.split('\n');
                    lines.forEach((line, i) => {
                        ctx.fillText(line, 0, -10 + i * 20);
                    });

                    ctx.restore();
                });

                // 绘制连接线（延迟出现）
                if (frame > 160) {
                    const lineProgress = (frame - 160) / 40;
                    ctx.globalAlpha = lineProgress;

                    ctx.strokeStyle = '#333';
                    ctx.lineWidth = 2;
                    // 抽象类继承
                    ctx.beginPath();
                    ctx.moveTo(width * 0.2, height * 0.36);
                    ctx.lineTo(width * 0.2, height * 0.64);
                    ctx.stroke();

                    // 实现类继承
                    ctx.beginPath();
                    ctx.moveTo(width * 0.8, height * 0.36);
                    ctx.lineTo(width * 0.8, height * 0.64);
                    ctx.stroke();

                    // 绘制桥接关系
                    ctx.strokeStyle = '#FF4444';
                    ctx.lineWidth = 4;
                    ctx.beginPath();
                    ctx.moveTo(width * 0.28, height * 0.3);
                    ctx.lineTo(width * 0.72, height * 0.3);
                    ctx.stroke();

                    // 绘制桥接标签
                    ctx.fillStyle = '#FF4444';
                    ctx.font = 'bold 14px Microsoft YaHei';
                    ctx.fillText('桥接关系', width * 0.5, height * 0.25);

                    ctx.globalAlpha = 1;
                }

                // 绘制标题
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.fillText('桥接模式结构图', width / 2, height * 0.1);

                frame++;
                if (frame <= maxFrames) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        // 显示桥接工作流程
        function showBridgeFlow() {
            const ctx = contexts['bridgeIntroCanvas'];
            const canvas = canvases['bridgeIntroCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            let step = 0;
            const maxSteps = 4;

            function drawStep() {
                ctx.clearRect(0, 0, width, height);

                // 绘制基本结构
                drawBridgeIntroCanvas();

                // 根据步骤高亮不同部分
                ctx.save();
                ctx.globalAlpha = 0.8;

                switch(step) {
                    case 0:
                        // 高亮客户端调用
                        ctx.fillStyle = '#FFD700';
                        ctx.fillRect(width * 0.05, height * 0.1, 100, 40);
                        ctx.fillStyle = '#333';
                        ctx.font = '14px Microsoft YaHei';
                        ctx.fillText('客户端调用', width * 0.1, height * 0.13);
                        break;
                    case 1:
                        // 高亮抽象类
                        ctx.strokeStyle = '#FFD700';
                        ctx.lineWidth = 4;
                        ctx.strokeRect(width * 0.2 - 84, height * 0.3 - 34, 168, 68);
                        break;
                    case 2:
                        // 高亮桥接关系
                        ctx.strokeStyle = '#FFD700';
                        ctx.lineWidth = 6;
                        ctx.beginPath();
                        ctx.moveTo(width * 0.28, height * 0.3);
                        ctx.lineTo(width * 0.72, height * 0.3);
                        ctx.stroke();
                        break;
                    case 3:
                        // 高亮实现类
                        ctx.strokeStyle = '#FFD700';
                        ctx.lineWidth = 4;
                        ctx.strokeRect(width * 0.8 - 84, height * 0.7 - 34, 168, 68);
                        break;
                }

                ctx.restore();

                // 显示步骤说明
                const stepTexts = [
                    '1. 客户端调用抽象类方法',
                    '2. 抽象类处理业务逻辑',
                    '3. 通过桥接调用实现接口',
                    '4. 具体实现执行操作'
                ];

                ctx.fillStyle = '#333';
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText(stepTexts[step], width / 2, height * 0.9);
            }

            drawStep();

            const interval = setInterval(() => {
                step = (step + 1) % maxSteps;
                drawStep();
            }, 2000);

            // 10秒后停止动画
            setTimeout(() => {
                clearInterval(interval);
                drawBridgeIntroCanvas();
            }, 10000);
        }

        // 桥接模式演示相关变量
        let currentProduct = null;
        let currentPublishMethod = null;
        let demoAnimations = [];

        // 绘制桥接演示画布
        function drawBridgeDemoCanvas() {
            const ctx = contexts['bridgeDemoCanvas'];
            const canvas = canvases['bridgeDemoCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 绘制产品区域
            ctx.fillStyle = '#FF6B6B';
            ctx.fillRect(width * 0.1, height * 0.2, width * 0.35, height * 0.6);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillText('产品抽象层', width * 0.275, height * 0.3);

            // 绘制当前产品
            if (currentProduct) {
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText(currentProduct, width * 0.275, height * 0.5);

                // 绘制产品图标
                const productIcons = {
                    '宣传册': '📖',
                    '文章': '📄',
                    '传单': '📋'
                };
                ctx.font = '40px Arial';
                ctx.fillText(productIcons[currentProduct] || '📄', width * 0.275, height * 0.65);
            }

            // 绘制出版方式区域
            ctx.fillStyle = '#4ECDC4';
            ctx.fillRect(width * 0.55, height * 0.2, width * 0.35, height * 0.6);
            ctx.fillStyle = 'white';
            ctx.font = 'bold 18px Microsoft YaHei';
            ctx.fillText('出版实现层', width * 0.725, height * 0.3);

            // 绘制当前出版方式
            if (currentPublishMethod) {
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText(currentPublishMethod, width * 0.725, height * 0.5);

                // 绘制出版方式图标
                const methodIcons = {
                    '纸质出版': '📰',
                    'CD出版': '💿',
                    '在线发布': '🌐'
                };
                ctx.font = '40px Arial';
                ctx.fillText(methodIcons[currentPublishMethod] || '📄', width * 0.725, height * 0.65);
            }

            // 绘制桥接连接
            if (currentProduct && currentPublishMethod) {
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(width * 0.45, height * 0.5);
                ctx.lineTo(width * 0.55, height * 0.5);
                ctx.stroke();

                // 绘制桥接图标
                ctx.font = '30px Arial';
                ctx.fillStyle = '#333';
                ctx.fillText('🌉', width * 0.5, height * 0.45);

                // 显示组合结果
                ctx.font = '14px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.fillText(`${currentProduct} + ${currentPublishMethod}`, width * 0.5, height * 0.9);
            }

            // 绘制标题
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('桥接模式实时演示', width / 2, height * 0.1);
        }

        // 创建产品
        function createProduct(productType) {
            const productNames = {
                'brochure': '宣传册',
                'article': '文章',
                'flyer': '传单'
            };

            currentProduct = productNames[productType];
            animateProductCreation();
        }

        // 改变出版方式
        function changePublishMethod(method) {
            const methodNames = {
                'paper': '纸质出版',
                'cd': 'CD出版',
                'online': '在线发布'
            };

            currentPublishMethod = methodNames[method];
            animateMethodChange();
        }

        // 产品创建动画
        function animateProductCreation() {
            const ctx = contexts['bridgeDemoCanvas'];
            const canvas = canvases['bridgeDemoCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            let frame = 0;
            const maxFrames = 60;

            function animate() {
                drawBridgeDemoCanvas();

                // 添加创建效果
                const progress = frame / maxFrames;
                const scale = 0.5 + 0.5 * progress;
                const alpha = progress;

                ctx.save();
                ctx.globalAlpha = alpha;
                ctx.translate(width * 0.275, height * 0.5);
                ctx.scale(scale, scale);

                // 绘制创建光效
                const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 50);
                gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
                gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
                ctx.fillStyle = gradient;
                ctx.fillRect(-50, -50, 100, 100);

                ctx.restore();

                frame++;
                if (frame <= maxFrames) {
                    requestAnimationFrame(animate);
                } else {
                    drawBridgeDemoCanvas();
                }
            }

            animate();
        }

        // 出版方式改变动画
        function animateMethodChange() {
            const ctx = contexts['bridgeDemoCanvas'];
            const canvas = canvases['bridgeDemoCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            let frame = 0;
            const maxFrames = 60;

            function animate() {
                drawBridgeDemoCanvas();

                // 添加变化效果
                const progress = frame / maxFrames;
                const scale = 0.5 + 0.5 * progress;
                const alpha = progress;

                ctx.save();
                ctx.globalAlpha = alpha;
                ctx.translate(width * 0.725, height * 0.5);
                ctx.scale(scale, scale);

                // 绘制变化光效
                const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 50);
                gradient.addColorStop(0, 'rgba(78, 205, 196, 0.8)');
                gradient.addColorStop(1, 'rgba(78, 205, 196, 0)');
                ctx.fillStyle = gradient;
                ctx.fillRect(-50, -50, 100, 100);

                ctx.restore();

                frame++;
                if (frame <= maxFrames) {
                    requestAnimationFrame(animate);
                } else {
                    drawBridgeDemoCanvas();
                }
            }

            animate();
        }

        // 展示解耦特性
        function demonstrateDecoupling() {
            const ctx = contexts['bridgeDemoCanvas'];
            const canvas = canvases['bridgeDemoCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            let frame = 0;
            const maxFrames = 180;

            function animate() {
                ctx.clearRect(0, 0, width, height);

                const progress = frame / maxFrames;
                const separation = Math.sin(progress * Math.PI * 2) * 50;

                // 绘制分离的产品区域
                ctx.fillStyle = '#FF6B6B';
                ctx.fillRect(width * 0.1 - separation, height * 0.2, width * 0.35, height * 0.6);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.fillText('产品抽象层', width * 0.275 - separation, height * 0.3);
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('独立变化', width * 0.275 - separation, height * 0.5);

                // 绘制分离的出版方式区域
                ctx.fillStyle = '#4ECDC4';
                ctx.fillRect(width * 0.55 + separation, height * 0.2, width * 0.35, height * 0.6);
                ctx.fillStyle = 'white';
                ctx.font = 'bold 18px Microsoft YaHei';
                ctx.fillText('出版实现层', width * 0.725 + separation, height * 0.3);
                ctx.font = '16px Microsoft YaHei';
                ctx.fillText('独立变化', width * 0.725 + separation, height * 0.5);

                // 绘制动态桥接线
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 4;
                ctx.beginPath();
                ctx.moveTo(width * 0.45 - separation, height * 0.5);
                ctx.lineTo(width * 0.55 + separation, height * 0.5);
                ctx.stroke();

                // 绘制标题
                ctx.font = 'bold 20px Microsoft YaHei';
                ctx.fillStyle = '#333';
                ctx.fillText('解耦演示：两个维度独立变化', width / 2, height * 0.1);

                frame++;
                if (frame <= maxFrames) {
                    requestAnimationFrame(animate);
                } else {
                    drawBridgeDemoCanvas();
                }
            }

            animate();
        }

        // 绘制其他画布的基础函数
        function drawImplementationCanvas() {
            const ctx = contexts['implementationCanvas'];
            const canvas = canvases['implementationCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 绘制代码执行流程图
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('桥接模式代码执行流程', width / 2, height * 0.1);

            // 绘制执行步骤
            const steps = [
                { text: '1. 创建具体实现', x: width * 0.2, y: height * 0.3, color: '#4ECDC4' },
                { text: '2. 创建抽象产品', x: width * 0.8, y: height * 0.3, color: '#FF6B6B' },
                { text: '3. 桥接连接', x: width * 0.5, y: height * 0.5, color: '#45B7D1' },
                { text: '4. 调用方法', x: width * 0.5, y: height * 0.7, color: '#96CEB4' }
            ];

            steps.forEach(step => {
                ctx.fillStyle = step.color;
                ctx.fillRect(step.x - 80, step.y - 20, 160, 40);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(step.text, step.x, step.y);
            });
        }

        function drawAdvantagesCanvas() {
            const ctx = contexts['advantagesCanvas'];
            const canvas = canvases['advantagesCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 绘制优势对比图
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('桥接模式 vs 传统方案', width / 2, height * 0.1);

            // 传统方案
            ctx.fillStyle = '#FF6B6B';
            ctx.fillRect(width * 0.1, height * 0.25, width * 0.35, height * 0.5);
            ctx.fillStyle = 'white';
            ctx.font = '16px Microsoft YaHei';
            ctx.fillText('传统方案', width * 0.275, height * 0.3);
            ctx.fillText('类数量：3×4=12', width * 0.275, height * 0.45);
            ctx.fillText('❌ 类爆炸', width * 0.275, height * 0.6);
            ctx.fillText('❌ 难以扩展', width * 0.275, height * 0.7);

            // 桥接模式
            ctx.fillStyle = '#4ECDC4';
            ctx.fillRect(width * 0.55, height * 0.25, width * 0.35, height * 0.5);
            ctx.fillStyle = 'white';
            ctx.fillText('桥接模式', width * 0.725, height * 0.3);
            ctx.fillText('类数量：3+4=7', width * 0.725, height * 0.45);
            ctx.fillText('✅ 结构清晰', width * 0.725, height * 0.6);
            ctx.fillText('✅ 易于扩展', width * 0.725, height * 0.7);
        }

        function drawComparisonCanvas() {
            const ctx = contexts['comparisonCanvas'];
            const canvas = canvases['comparisonCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 绘制设计模式对比
            ctx.font = 'bold 20px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('设计模式对比分析', width / 2, height * 0.1);

            const patterns = [
                { name: '外观模式', desc: '简化接口', color: '#FF6B6B', x: width * 0.2, y: height * 0.3 },
                { name: '桥接模式', desc: '分离抽象与实现', color: '#4ECDC4', x: width * 0.5, y: height * 0.3 },
                { name: '装饰器模式', desc: '动态添加功能', color: '#45B7D1', x: width * 0.8, y: height * 0.3 },
                { name: '适配器模式', desc: '接口转换', color: '#96CEB4', x: width * 0.35, y: width * 0.6 }
            ];

            patterns.forEach(pattern => {
                ctx.fillStyle = pattern.color;
                ctx.fillRect(pattern.x - 60, pattern.y - 30, 120, 60);
                ctx.fillStyle = 'white';
                ctx.font = '14px Microsoft YaHei';
                ctx.fillText(pattern.name, pattern.x, pattern.y - 10);
                ctx.fillText(pattern.desc, pattern.x, pattern.y + 10);
            });

            // 高亮桥接模式
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 4;
            ctx.strokeRect(width * 0.5 - 64, height * 0.3 - 34, 128, 68);
        }

        function drawPracticeCanvas() {
            const ctx = contexts['practiceCanvas'];
            const canvas = canvases['practiceCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            ctx.clearRect(0, 0, width, height);

            // 绘制学习总结
            ctx.font = 'bold 24px Microsoft YaHei';
            ctx.fillStyle = '#333';
            ctx.fillText('🎓 学习成果总结', width / 2, height * 0.2);

            // 绘制知识点
            const points = [
                '✅ 理解桥接模式的核心思想',
                '✅ 掌握抽象与实现的分离',
                '✅ 学会解决类爆炸问题',
                '✅ 了解桥接模式的适用场景'
            ];

            ctx.font = '18px Microsoft YaHei';
            ctx.fillStyle = '#555';
            points.forEach((point, i) => {
                ctx.fillText(point, width / 2, height * 0.4 + i * 40);
            });
        }

        // 运行代码演示
        function runCodeDemo() {
            // 模拟代码执行
            const output = `
// 创建出版实现
const paperPublish = new PaperPublish();
const cdPublish = new CDPublish();

// 创建产品并桥接
const brochure = new Brochure(paperPublish);
console.log(brochure.produce()); // "📰 纸质出版: 精美宣传册内容"

// 动态切换实现
brochure.publishImplementor = cdPublish;
console.log(brochure.produce()); // "💿 CD出版: 精美宣传册内容"
            `;

            alert('代码执行结果：\n' + output);
            animateCodeExecution();
        }

        function animateCodeExecution() {
            const ctx = contexts['implementationCanvas'];
            const canvas = canvases['implementationCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            let step = 0;
            const maxSteps = 4;

            function animate() {
                drawImplementationCanvas();

                // 高亮当前执行步骤
                const steps = [
                    { x: width * 0.2, y: height * 0.3 },
                    { x: width * 0.8, y: height * 0.3 },
                    { x: width * 0.5, y: height * 0.5 },
                    { x: width * 0.5, y: height * 0.7 }
                ];

                if (step < steps.length) {
                    ctx.strokeStyle = '#FFD700';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(steps[step].x - 84, steps[step].y - 24, 168, 48);
                }

                step++;
                if (step <= maxSteps) {
                    setTimeout(animate, 1000);
                }
            }

            animate();
        }

        // 其他交互函数
        function showCodeFlow() {
            animateCodeExecution();
        }

        function showAdvantagesDemo() {
            // 优势演示动画
            const ctx = contexts['advantagesCanvas'];
            let frame = 0;

            function animate() {
                drawAdvantagesCanvas();

                // 添加闪烁效果突出优势
                if (Math.floor(frame / 30) % 2 === 0) {
                    ctx.strokeStyle = '#FFD700';
                    ctx.lineWidth = 4;
                    ctx.strokeRect(width * 0.55 - 4, height * 0.25 - 4, width * 0.35 + 8, height * 0.5 + 8);
                }

                frame++;
                if (frame < 180) {
                    requestAnimationFrame(animate);
                }
            }

            animate();
        }

        function compareWithoutBridge() {
            // 对比演示
            alert('传统方案需要创建12个类：\n宣传册纸质版、宣传册CD版、宣传册DVD版、宣传册在线版\n文章纸质版、文章CD版、文章DVD版、文章在线版\n传单纸质版、传单CD版、传单DVD版、传单在线版\n\n桥接模式只需要7个类：\n3个产品类 + 4个出版方式类');
        }

        function showPatternComparison() {
            drawComparisonCanvas();
        }

        function highlightBridgeAdvantage() {
            const ctx = contexts['comparisonCanvas'];
            drawComparisonCanvas();

            // 添加特殊高亮效果
            let frame = 0;
            function animate() {
                if (frame % 60 < 30) {
                    ctx.strokeStyle = '#FF4444';
                    ctx.lineWidth = 6;
                    ctx.strokeRect(width * 0.5 - 66, height * 0.3 - 36, 132, 72);
                }

                frame++;
                if (frame < 180) {
                    requestAnimationFrame(animate);
                }
            }
            animate();
        }

        function startQuiz() {
            const questions = [
                {
                    question: "桥接模式的核心思想是什么？",
                    options: ["继承复用", "组合优于继承", "分离抽象与实现", "封装变化"],
                    correct: 2
                },
                {
                    question: "在广告公司案例中，如果有3种产品和4种出版方式，传统方案需要多少个类？",
                    options: ["7个", "12个", "16个", "20个"],
                    correct: 1
                }
            ];

            let score = 0;
            questions.forEach((q, i) => {
                const answer = prompt(`问题${i+1}: ${q.question}\n${q.options.map((opt, j) => `${j+1}. ${opt}`).join('\n')}\n请输入答案序号(1-4):`);
                if (parseInt(answer) - 1 === q.correct) {
                    score++;
                    alert("✅ 回答正确！");
                } else {
                    alert(`❌ 回答错误。正确答案是：${q.options[q.correct]}`);
                }
            });

            alert(`测验完成！您的得分：${score}/${questions.length}`);
        }

        function showSummary() {
            drawPracticeCanvas();
            alert('🎉 恭喜您完成桥接模式的学习！\n\n您已经掌握了：\n• 桥接模式的定义和结构\n• 如何解决类爆炸问题\n• 抽象与实现的分离技巧\n• 桥接模式的实际应用\n\n继续加油，探索更多设计模式！');
        }

        function celebrateCompletion() {
            const ctx = contexts['practiceCanvas'];
            const canvas = canvases['practiceCanvas'];
            const width = canvas.width / window.devicePixelRatio;
            const height = canvas.height / window.devicePixelRatio;

            let frame = 0;
            const maxFrames = 120;

            function animate() {
                ctx.clearRect(0, 0, width, height);

                // 绘制庆祝动画
                const progress = frame / maxFrames;

                // 彩色背景
                const gradient = ctx.createLinearGradient(0, 0, width, height);
                gradient.addColorStop(0, `hsl(${frame * 3}, 70%, 80%)`);
                gradient.addColorStop(1, `hsl(${frame * 3 + 60}, 70%, 80%)`);
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, width, height);

                // 庆祝文字
                ctx.font = 'bold 36px Microsoft YaHei';
                ctx.fillStyle = 'white';
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                const scale = 0.8 + 0.2 * Math.sin(frame * 0.2);
                ctx.save();
                ctx.translate(width / 2, height / 2);
                ctx.scale(scale, scale);
                ctx.strokeText('🎉 学习完成！', 0, 0);
                ctx.fillText('🎉 学习完成！', 0, 0);
                ctx.restore();

                // 飘落的表情符号
                for (let i = 0; i < 10; i++) {
                    const x = (width / 10) * i + Math.sin(frame * 0.1 + i) * 20;
                    const y = (frame * 2 + i * 20) % height;
                    ctx.font = '24px Arial';
                    ctx.fillText(['🎊', '🎉', '⭐', '🌟', '✨'][i % 5], x, y);
                }

                frame++;
                if (frame <= maxFrames) {
                    requestAnimationFrame(animate);
                } else {
                    drawPracticeCanvas();
                }
            }

            animate();
        }
    </script>
</body>
</html>
