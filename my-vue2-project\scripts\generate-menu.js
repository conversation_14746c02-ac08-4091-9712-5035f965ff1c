const fs = require('fs');
const path = require('path');

const menuDirPath = path.join(__dirname, '..', 'public', 'node');
const outputPath = path.join(__dirname, '..', 'src', 'assets', 'menu.json');

const icons = {
  '默认': 'el-icon-folder',
  '笔记系统': 'el-icon-notebook-1',
  '知识': 'el-icon-collection',
  '小学': 'el-icon-school',
  '题目': 'el-icon-edit-outline',
  '题库': 'el-icon-receiving',
  '数学': 'el-icon-data-line',
  '考研': 'el-icon-reading',
  '架构': 'el-icon-s-platform',
  '单词': 'el-icon-document',
  '408': 'el-icon-monitor'
};

function generateNode(currentPath, relativePath) {
  const name = path.basename(currentPath);
  const children = [];
  const files = [];

  try {
    const items = fs.readdirSync(currentPath, { withFileTypes: true });
    for (const item of items) {
      const itemPath = path.join(currentPath, item.name);
      const itemRelativePath = path.join(relativePath, item.name).replace(/\\/g, '/');
      if (item.isDirectory()) {
        children.push(generateNode(itemPath, itemRelativePath));
      } else if (item.isFile() && item.name.endsWith('.html')) {
        files.push({
          name: item.name.replace(/\.html$/, ''),
          path: itemRelativePath
        });
      }
    }
  } catch (error) {
    console.error(`无法读取目录 ${currentPath}: ${error.message}`);
    // Return empty arrays if directory cannot be read
    return { name, path: relativePath, icon: icons[name] || icons['默认'], files: [], children: [] };
  }

  const node = {
    name: name,
    path: relativePath.replace(/\\/g, '/'),
    icon: icons[name] || icons['默认'],
    files: files
  };
  if (children.length > 0) {
    node.children = children;
  }
  return node;
}

try {
  const menuTree = fs.readdirSync(menuDirPath, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => generateNode(path.join(menuDirPath, dirent.name), path.join('node', dirent.name)));

  fs.writeFileSync(outputPath, JSON.stringify(menuTree, null, 2));
  console.log('多级菜单数据已成功生成至:', outputPath);
} catch (error) {
  console.error('生成菜单数据时出错:', error);
  fs.writeFileSync(outputPath, JSON.stringify([]));
  console.log('已生成空菜单以防应用启动失败。');
} 