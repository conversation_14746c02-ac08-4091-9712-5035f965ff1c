<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考研长难句词缀故事动画</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(to right top, #6a82fb, #fc5c7d);
            color: #333;
        }
        .container {
            width: 90%;
            max-width: 800px;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            text-align: center;
        }
        h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 15px;
        }
        .sentence {
            font-size: 1.4em;
            margin: 25px 0;
            color: #34495e;
            font-weight: 500;
            line-height: 1.5;
        }
        .sentence-translation {
            font-size: 1em;
            margin-bottom: 25px;
            color: #7f8c8d;
        }
        canvas {
            background-color: #fff;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            cursor: pointer;
            display: block;
            margin: 0 auto 25px auto;
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 25px;
        }
        .controls button {
            padding: 12px 25px;
            font-size: 1.1em;
            border: none;
            border-radius: 10px;
            background-color: #fc5c7d;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 10px rgba(252, 92, 125, 0.2);
        }
        .controls button:hover, .controls button:focus {
            background-color: #d94462;
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(252, 92, 125, 0.3);
            outline: none;
        }
        .controls button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
            transform: translateY(0);
            box-shadow: none;
        }
        .explanation {
            min-height: 100px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border-left: 6px solid #6a82fb;
            text-align: left;
            line-height: 1.7;
            font-size: 1.05em;
        }
        .explanation strong {
            color: #fc5c7d;
        }
        .footer {
            margin-top: 25px;
            font-size: 0.9em;
            color: rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>

<div class="container">
    <h1>探秘金融危机的种子</h1>
    <p class="sentence">
        Moreover, I am <strong>inclined</strong> to the view that the seeds of the world <strong>financial</strong> <strong>crisis</strong> were sown in a hot-house of <strong>complacency</strong>.
    </p>
    <p class="sentence-translation">
        <strong>句子翻译:</strong> 此外，我倾向于认为，世界金融危机的种子是在自满的温室中播下的。
    </p>
    <canvas id="wordCanvas" width="800" height="300"></canvas>
    <div class="controls">
        <button id="prevBtn">上一个故事</button>
        <button id="nextBtn">下一个故事</button>
    </div>
    <div class="explanation" id="explanationBox">
        <p>点击"下一个故事"按钮，让我们一起通过动画故事，探索这句话里隐藏的秘密吧！</p>
    </div>
    <div class="footer">
        <p>通过生动的故事和互动，让考研英语学习不再枯燥。</p>
    </div>
</div>

<script>
    const canvas = document.getElementById('wordCanvas');
    const ctx = canvas.getContext('2d');
    const explanationBox = document.getElementById('explanationBox');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    const wordsData = [
        {
            word: 'inclined',
            parts: [
                { text: 'in-', meaning: '向内，进入', story: '前缀 in- 表示方向，朝着某个方向或进入某种状态。' },
                { text: 'clin', meaning: '倾斜', story: '词根 clin 源自拉丁语，意思是"弯曲"或"倾斜"。想象一下山坡的斜坡(decline)。' },
                { text: 'ed', meaning: '(形容词后缀)', story: '后缀 -ed 使其变成形容词，表示"有...倾向的"。' }
            ],
            fullStory: '<strong>inclined (倾向于):</strong> 故事的开始，我们的思想就像一个天平。当<strong>向(in-)</strong> 一边有所<strong>倾斜(clin)</strong>时，就代表我们对某个观点有了倾向。动画将展示一个天平，慢慢地向一边倾斜，形象地表达出"倾向于"这个概念。',
            animation: (progress) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.textAlign = 'center';
                const centerX = canvas.width / 2;
                const baseY = 200;
                
                // Draw stand
                ctx.fillStyle = '#34495e';
                ctx.fillRect(centerX - 5, baseY, 10, 80);
                ctx.fillRect(centerX - 50, baseY + 80, 100, 10);
                
                const angle = (progress - 0.5) * 0.3; // Tilt angle
                
                ctx.save();
                ctx.translate(centerX, baseY);
                ctx.rotate(angle);

                // Draw beam
                ctx.fillStyle = '#95a5a6';
                ctx.fillRect(-150, -5, 300, 10);

                // Draw pans
                ctx.fillStyle = '#e67e22';
                ctx.beginPath();
                ctx.arc(-150, 45, 40, 0, Math.PI);
                ctx.fill();
                ctx.beginPath();
                ctx.arc(150, 45, 40, 0, Math.PI);
                ctx.fill();

                ctx.restore();

                if (progress > 0.5) {
                    const textProgress = (progress - 0.5) / 0.5;
                    ctx.globalAlpha = textProgress;
                    ctx.fillStyle = '#c0392b';
                    ctx.font = '24px Arial';
                    ctx.fillText('in- (向) + clin (倾斜) = 倾向', centerX, 50);
                    ctx.globalAlpha = 1;
                }
            }
        },
        {
            word: 'financial',
            parts: [
                { text: 'fin', meaning: '结束，处理', story: '词根 fin 来自拉丁语 "finis"，意思是结束。后来引申为"处理账目，结算"，因为结算是对一笔钱的最终处理。' },
                { text: 'anc', meaning: '(名词后缀)', story: '名词后缀，构成 a finance (金融)。' },
                { text: 'ial', meaning: '(形容词后缀)', story: '形容词后缀 -ial (...的)，使其变为"金融的"。' }
            ],
            fullStory: '<strong>financial (金融的):</strong> 有了倾向的观点后，我们开始探寻危机的本质——金融。故事中，"金融"就是对金钱(钱币)进行最终<strong>处理(fin)</strong>和分配。动画会展示一枚硬币投入一个象征"处理/结算"的机器，最终产出金融流水的场景。',
            animation: (progress) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const p = Math.min(1, progress * 1.2);
                const coinX = 150 + 200 * p;
                const coinY = 100;

                // Draw coin
                ctx.fillStyle = '#f1c40f';
                ctx.beginPath();
                ctx.arc(coinX, coinY, 30, 0, Math.PI * 2);
                ctx.fill();
                ctx.fillStyle = '#e67e22';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('$', coinX, coinY + 8);

                // Draw machine
                ctx.fillStyle = '#2c3e50';
                ctx.fillRect(400, 50, 150, 200);
                ctx.fillStyle = '#34495e';
                ctx.fillRect(420, 70, 110, 50);
                
                // Output
                if(p > 0.6) {
                    const outProgress = (p - 0.6) / 0.4;
                    ctx.beginPath();
                    ctx.moveTo(550, 150);
                    ctx.lineTo(550 + 100 * outProgress, 150);
                    ctx.strokeStyle = '#27ae60';
                    ctx.lineWidth = 8;
                    ctx.stroke();
                }

                if (p > 0.7) {
                    ctx.fillStyle = '#c0392b';
                    ctx.font = '24px Arial';
                    ctx.fillText('fin (处理) + ance = 金融', canvas.width / 2, 280);
                }
            }
        },
        {
            word: 'crisis',
            parts: [
                { text: 'cris', meaning: '决定，分离', story: '词根 cris/crit 源自希腊语，意为"决定性的转折点"或"分离判断"。想象一下一个十字路口，你必须做出决定。' },
                { text: 'is', meaning: '(名词后缀)', story: '名词后缀，表示状态。' }
            ],
            fullStory: '<strong>crisis (危机):</strong> 金融世界并非一帆风顺。当走到一个需要做出重大<strong>决定(cris)</strong>的十字路口，好的决定带来繁荣，坏的决定则带来"危机"。动画将展示一条平稳的路在一个点上突然断裂、分离，形成危机四伏的深渊。',
            animation: (progress) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const y = 180;
                
                // Draw the path
                ctx.fillStyle = '#27ae60';
                ctx.fillRect(50, y, 300, 20);

                const crackPoint = 350;
                const fallProgress = Math.max(0, (progress - 0.5) / 0.5);
                
                ctx.beginPath();
                ctx.moveTo(crackPoint, y);
                ctx.lineTo(crackPoint + 20 - fallProgress * 15, y + 5 + fallProgress * 40);
                ctx.lineTo(crackPoint + 30 + fallProgress * 10, y - 10 + fallProgress * 30);
                ctx.lineTo(crackPoint + 50 - fallProgress * 20, y + 15 + fallProgress * 60);
                ctx.lineTo(crackPoint + 70, y + fallProgress * 80);
                ctx.lineTo(crackPoint + 400, y + fallProgress * 80);
                ctx.lineTo(crackPoint + 400, y + 20 + fallProgress * 80);
                ctx.lineTo(crackPoint, y+20);
                ctx.closePath();
                ctx.fillStyle = '#e74c3c';
                ctx.fill();

                if (progress > 0.6) {
                    ctx.fillStyle = '#c0392b';
                    ctx.font = 'bold 30px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('cris (分离/决定) → 危机', canvas.width / 2, 80);
                }
            }
        },
        {
            word: 'complacency',
            parts: [
                { text: 'com-', meaning: '完全，加强', story: '前缀 com- 在这里表示完全地，用于加强语气。' },
                { text: 'plac', meaning: '取悦，使平静', story: '词根 plac 意为"取悦"或"使...平静"。想一想 please (请，取悦)。' },
                { text: 'ency', meaning: '(名词后缀)', story: '名词后缀，表示性质或状态。' }
            ],
            fullStory: '<strong>complacency (自满):</strong> 为什么会产生危机？因为人们<strong>完全(com-)</strong>被眼前的繁荣所<strong>取悦(plac)</strong>，进入了一种沾沾自喜、不思进取的状态——"自满"。动画将展示一个人舒适地躺着，对自己所处的环境感到非常满意，忽视了潜藏的危险。',
            animation: (progress) => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2 + 30;

                // Draw a simple figure relaxing
                const p = progress;
                const smileFactor = Math.sin(p * Math.PI) * 10;

                // Body
                ctx.fillStyle = '#3498db';
                ctx.beginPath();
                ctx.ellipse(centerX, centerY + 20, 80, 30, 0, 0, Math.PI * 2);
                ctx.fill();

                // Head
                ctx.beginPath();
                ctx.arc(centerX - 70, centerY - 10, 30, 0, Math.PI * 2);
                ctx.fill();
                
                // Smile
                ctx.strokeStyle = '#fff';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(centerX - 70, centerY - 10, 15, 0.1 * Math.PI, 0.9 * Math.PI);
                ctx.stroke();

                // Zzz for sleep/complacency
                if (p > 0.5) {
                    ctx.fillStyle = '#2c3e50';
                    ctx.font = 'italic bold 20px Arial';
                    const zProgress = (p - 0.5) / 0.5;
                    ctx.globalAlpha = zProgress;
                    ctx.fillText('Zzz...', centerX + 60, centerY - 60);
                    ctx.globalAlpha = 1;
                }
                
                if (p > 0.3) {
                     ctx.fillStyle = '#c0392b';
                     ctx.font = '24px Arial';
                     ctx.textAlign = 'center';
                     ctx.fillText('com (完全) + plac (取悦) = 自满', centerX, 50);
                }
            }
        }
    ];

    let currentWordIndex = -1;
    let animationFrameId;
    let startTime;
    const animationDuration = 3000; // 3 seconds per animation

    function updateButtons() {
        prevBtn.disabled = currentWordIndex <= 0;
        nextBtn.disabled = currentWordIndex >= wordsData.length - 1;
    }

    function animate(timestamp) {
        if (!startTime) startTime = timestamp;
        const progress = Math.min((timestamp - startTime) / animationDuration, 1);
        
        wordsData[currentWordIndex].animation(progress);

        if (progress < 1) {
            animationFrameId = requestAnimationFrame(animate);
        }
    }

    function showWord(index) {
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
        }

        currentWordIndex = index;
        const word = wordsData[index];

        let explanationHtml = `<p>${word.fullStory}</p><ul>`;
        word.parts.forEach(part => {
            explanationHtml += `<li><strong>${part.text}</strong> (${part.meaning}): ${part.story}</li>`;
        });
        explanationHtml += '</ul>';
        explanationBox.innerHTML = explanationHtml;

        startTime = null;
        animationFrameId = requestAnimationFrame(animate);
        updateButtons();
    }

    nextBtn.addEventListener('click', () => {
        if (currentWordIndex < wordsData.length - 1) {
            showWord(currentWordIndex + 1);
        }
    });

    prevBtn.addEventListener('click', () => {
        if (currentWordIndex > 0) {
            showWord(currentWordIndex - 1);
        }
    });
    
    // Initial state
    updateButtons();

</script>
</body>
</html> 