<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaEE遗产系统集成学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
            animation: fadeInDown 1.2s ease-out;
        }

        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
            letter-spacing: 3px;
        }

        .header p {
            font-size: 1.4rem;
            opacity: 0.95;
            font-weight: 300;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .integration-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromLeft 1s ease-out 0.3s both;
        }

        .quiz-section {
            background: rgba(255,255,255,0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            animation: slideInFromRight 1s ease-out 0.3s both;
        }

        .section-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 30px;
            text-align: center;
            color: #2d3436;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .integration-demo {
            text-align: center;
            margin: 30px 0;
        }

        #integrationCanvas {
            border: 3px solid #ddd;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .tech-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 25px 0;
        }

        .tech-btn {
            padding: 15px 10px;
            border: none;
            border-radius: 15px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            color: white;
            text-align: center;
        }

        .jdbc-btn {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .jca-btn {
            background: linear-gradient(45deg, #fd79a8, #e84393);
        }

        .java-idl-btn {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .jms-btn {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }

        .tech-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .tech-btn.active {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .integration-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .integration-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border: 3px solid #ddd;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .integration-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .integration-card.correct {
            border-color: #00b894;
            background: linear-gradient(135deg, #00b894, #00a085);
            color: white;
        }

        .integration-card.incorrect {
            border-color: #e17055;
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
        }

        .integration-card h3 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .integration-card p {
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .quiz-question {
            font-size: 1.3rem;
            line-height: 1.8;
            margin-bottom: 30px;
            color: #2d3436;
            background: #f1f2f6;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }

        .quiz-options {
            display: grid;
            grid-template-columns: 1fr;
            gap: 15px;
            margin: 30px 0;
        }

        .quiz-option {
            padding: 20px;
            border: 3px solid #ddd;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: bold;
            font-size: 1.1rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .quiz-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .quiz-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102,126,234,0.3);
        }

        .quiz-option:hover::before {
            left: 100%;
        }

        .quiz-option.correct {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            border-color: #00a085;
            animation: correctPulse 0.6s ease-out;
        }

        .quiz-option.wrong {
            background: linear-gradient(45deg, #e17055, #d63031);
            color: white;
            border-color: #d63031;
            animation: wrongShake 0.6s ease-out;
        }

        .explanation {
            background: linear-gradient(135deg, #e8f5e8, #d4edda);
            padding: 30px;
            border-radius: 15px;
            margin-top: 30px;
            border-left: 5px solid #00b894;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        .explanation h3 {
            color: #00a085;
            margin-bottom: 15px;
            font-size: 1.4rem;
        }

        .explanation ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .explanation li {
            margin: 8px 0;
            line-height: 1.6;
        }

        .highlight-correct {
            color: #00a085;
            font-weight: bold;
            background: rgba(0,184,148,0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-java {
            position: absolute;
            width: 50px;
            height: 50px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            animation: floatJava 15s infinite ease-in-out;
        }

        .java1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .java2 {
            top: 70%;
            right: 15%;
            animation-delay: 5s;
        }

        .java3 {
            bottom: 25%;
            left: 20%;
            animation-delay: 10s;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInFromLeft {
            from { opacity: 0; transform: translateX(-50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromRight {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInFromBottom {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes correctPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes wrongShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes floatJava {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-25px) rotate(120deg); }
            66% { transform: translateY(15px) rotate(240deg); }
        }

        .success-message {
            background: linear-gradient(45deg, #00b894, #00a085);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-top: 20px;
            display: none;
            animation: slideInFromBottom 0.5s ease-out;
        }

        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }

        @media (max-width: 768px) {
            .tech-controls {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .integration-cards {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-java java1"></div>
        <div class="floating-java java2"></div>
        <div class="floating-java java3"></div>
    </div>

    <div class="container">
        <div class="header">
            <h1>☕ JavaEE遗产系统集成</h1>
            <p>深度理解JavaEE平台的遗产系统集成技术</p>
        </div>

        <div class="main-grid">
            <div class="integration-section">
                <h2 class="section-title">🔗 集成技术演示</h2>
                
                <div class="integration-demo">
                    <canvas id="integrationCanvas" width="700" height="350"></canvas>
                </div>

                <div class="tech-controls">
                    <button class="tech-btn jdbc-btn" onclick="demonstrateTech('jdbc')">
                        JDBC<br><small>数据库连接</small>
                    </button>
                    <button class="tech-btn jca-btn" onclick="demonstrateTech('jca')">
                        JCA<br><small>连接器架构</small>
                    </button>
                    <button class="tech-btn java-idl-btn" onclick="demonstrateTech('java-idl')">
                        Java IDL<br><small>CORBA集成</small>
                    </button>
                    <button class="tech-btn jms-btn" onclick="demonstrateTech('jms')">
                        JMS<br><small>消息服务</small>
                    </button>
                </div>

                <div class="integration-cards">
                    <div class="integration-card correct">
                        <h3>✅ JDBC</h3>
                        <p><strong>关系型数据库集成</strong><br>Java数据库连接<br>连接各种关系型数据库<br>标准SQL操作接口</p>
                    </div>
                    <div class="integration-card correct">
                        <h3>✅ JCA</h3>
                        <p><strong>非Java应用系统集成</strong><br>Java连接器架构<br>企业信息系统连接<br>资源适配器标准</p>
                    </div>
                    <div class="integration-card correct">
                        <h3>✅ Java IDL</h3>
                        <p><strong>CORBA应用系统集成</strong><br>Java接口定义语言<br>分布式对象调用<br>跨语言互操作</p>
                    </div>
                    <div class="integration-card incorrect">
                        <h3>❌ JMS</h3>
                        <p><strong>消息中间件</strong><br>Java消息服务<br>异步消息通信<br>不是遗产系统集成技术</p>
                    </div>
                </div>
            </div>

            <div class="quiz-section">
                <h2 class="section-title">🎯 知识检测</h2>
                
                <div class="quiz-question">
                    📝 基于JavaEE平台的基础功能服务构建应用系统时，（　　）可用来集成遗产系统。
                </div>
                
                <div class="quiz-options">
                    <div class="quiz-option" onclick="selectAnswer(this, true)">
                        A. JDBC、JCA和Java IDL
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        B. JDBC、JCA和JMS
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        C. JDBC、JMS和Java IDL
                    </div>
                    <div class="quiz-option" onclick="selectAnswer(this, false)">
                        D. JCA、JMS和Java IDL
                    </div>
                </div>

                <div class="explanation" id="explanation">
                    <h3>💡 详细解析</h3>
                    <p><strong>正确答案：A. JDBC、JCA和Java IDL</strong></p>
                    <p>JavaEE平台提供了对不同类型遗产系统的集成支持：</p>
                    <ul>
                        <li><span class="highlight-correct">JDBC (Java数据库连接)</span>：
                            <br>• 用于集成关系型数据库系统
                            <br>• 提供标准的数据库访问接口
                            <br>• 支持各种主流数据库</li>
                        <li><span class="highlight-correct">JCA (Java连接器架构)</span>：
                            <br>• 用于集成非Java应用系统
                            <br>• 连接企业信息系统(EIS)
                            <br>• 提供资源适配器标准</li>
                        <li><span class="highlight-correct">Java IDL (Java接口定义语言)</span>：
                            <br>• 用于集成基于CORBA的应用系统
                            <br>• 支持分布式对象调用
                            <br>• 实现跨语言互操作</li>
                    </ul>
                    <p><strong>JMS不是遗产系统集成技术</strong>：JMS是Java消息服务，主要用于异步消息通信，不是专门用于集成遗产系统的技术。</p>
                </div>

                <div class="success-message" id="successMessage">
                    🎉 恭喜答对！您已经掌握了JavaEE遗产系统集成技术！
                </div>
            </div>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('integrationCanvas');
        const ctx = canvas.getContext('2d');
        let currentTech = 'jdbc';
        let animationId = null;

        // 演示不同集成技术
        function demonstrateTech(techType) {
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            currentTech = techType;
            
            // 更新按钮状态
            document.querySelectorAll('.tech-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.${techType}-btn`).classList.add('active');
            
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            switch(techType) {
                case 'jdbc':
                    drawJDBCIntegration();
                    break;
                case 'jca':
                    drawJCAIntegration();
                    break;
                case 'java-idl':
                    drawJavaIDLIntegration();
                    break;
                case 'jms':
                    drawJMSIntegration();
                    break;
            }
        }

        // 绘制JDBC集成
        function drawJDBCIntegration() {
            ctx.fillStyle = '#74b9ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('JDBC - 关系型数据库集成', 350, 40);

            // JavaEE应用
            ctx.fillStyle = '#74b9ff';
            ctx.fillRect(50, 100, 150, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 100, 150, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('JavaEE应用', 125, 145);

            // JDBC连接
            ctx.fillStyle = '#0984e3';
            ctx.fillRect(250, 120, 100, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 120, 100, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('JDBC', 300, 145);

            // 数据库系统
            const databases = [
                {x: 400, y: 80, name: 'Oracle'},
                {x: 520, y: 80, name: 'MySQL'},
                {x: 400, y: 160, name: 'PostgreSQL'},
                {x: 520, y: 160, name: 'SQL Server'}
            ];

            databases.forEach(db => {
                ctx.fillStyle = '#fdcb6e';
                ctx.fillRect(db.x, db.y, 80, 60);
                ctx.strokeStyle = '#e17055';
                ctx.lineWidth = 2;
                ctx.strokeRect(db.x, db.y, 80, 60);
                
                ctx.fillStyle = '#2d3436';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(db.name, db.x + 40, db.y + 35);
                
                // 连接线
                ctx.strokeStyle = '#74b9ff';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(350, 140);
                ctx.lineTo(db.x, db.y + 30);
                ctx.stroke();
            });

            // 连接线
            ctx.strokeStyle = '#0984e3';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(200, 140);
            ctx.lineTo(250, 140);
            ctx.stroke();

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 标准数据库访问接口', 50, 250);
            ctx.fillText('• 支持各种关系型数据库', 250, 250);
            ctx.fillText('• SQL操作和事务管理', 450, 250);

            ctx.fillStyle = '#e17055';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 遗产系统集成技术', 350, 300);
        }

        // 绘制JCA集成
        function drawJCAIntegration() {
            ctx.fillStyle = '#fd79a8';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('JCA - 非Java应用系统集成', 350, 40);

            // JavaEE应用服务器
            ctx.fillStyle = '#fd79a8';
            ctx.fillRect(50, 100, 150, 100);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 100, 150, 100);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('JavaEE', 125, 135);
            ctx.font = '12px Arial';
            ctx.fillText('应用服务器', 125, 155);
            ctx.fillText('JCA容器', 125, 175);

            // 资源适配器
            ctx.fillStyle = '#e84393';
            ctx.fillRect(250, 120, 120, 60);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 120, 120, 60);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('资源适配器', 310, 145);
            ctx.font = '12px Arial';
            ctx.fillText('Resource Adapter', 310, 165);

            // 企业信息系统
            const eisSystems = [
                {x: 420, y: 80, name: 'ERP系统'},
                {x: 550, y: 80, name: 'CRM系统'},
                {x: 420, y: 160, name: '主机系统'},
                {x: 550, y: 160, name: '文件系统'}
            ];

            eisSystems.forEach(eis => {
                ctx.fillStyle = '#a29bfe';
                ctx.fillRect(eis.x, eis.y, 80, 60);
                ctx.strokeStyle = '#6c5ce7';
                ctx.lineWidth = 2;
                ctx.strokeRect(eis.x, eis.y, 80, 60);
                
                ctx.fillStyle = 'white';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(eis.name, eis.x + 40, eis.y + 35);
                
                // 连接线
                ctx.strokeStyle = '#fd79a8';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(370, 150);
                ctx.lineTo(eis.x, eis.y + 30);
                ctx.stroke();
            });

            // 连接线
            ctx.strokeStyle = '#e84393';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(200, 150);
            ctx.lineTo(250, 150);
            ctx.stroke();

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 连接企业信息系统(EIS)', 50, 250);
            ctx.fillText('• 资源适配器标准', 250, 250);
            ctx.fillText('• 非Java系统集成', 450, 250);

            ctx.fillStyle = '#e17055';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 遗产系统集成技术', 350, 300);
        }

        // 绘制Java IDL集成
        function drawJavaIDLIntegration() {
            ctx.fillStyle = '#00b894';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Java IDL - CORBA应用系统集成', 350, 40);

            // Java应用
            ctx.fillStyle = '#00b894';
            ctx.fillRect(50, 100, 150, 80);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(50, 100, 150, 80);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('Java应用', 125, 135);
            ctx.font = '12px Arial';
            ctx.fillText('Java IDL', 125, 155);

            // ORB
            ctx.fillStyle = '#00a085';
            ctx.fillRect(250, 120, 100, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 120, 100, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('ORB', 300, 145);

            // CORBA对象
            const corbaObjects = [
                {x: 400, y: 80, name: 'C++对象'},
                {x: 520, y: 80, name: 'Ada对象'},
                {x: 400, y: 160, name: 'COBOL对象'},
                {x: 520, y: 160, name: '其他语言'}
            ];

            corbaObjects.forEach(obj => {
                ctx.fillStyle = '#fdcb6e';
                ctx.fillRect(obj.x, obj.y, 80, 60);
                ctx.strokeStyle = '#e17055';
                ctx.lineWidth = 2;
                ctx.strokeRect(obj.x, obj.y, 80, 60);
                
                ctx.fillStyle = '#2d3436';
                ctx.font = 'bold 12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(obj.name, obj.x + 40, obj.y + 35);
                
                // 连接线
                ctx.strokeStyle = '#00b894';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(350, 140);
                ctx.lineTo(obj.x, obj.y + 30);
                ctx.stroke();
            });

            // 连接线
            ctx.strokeStyle = '#00a085';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(200, 140);
            ctx.lineTo(250, 140);
            ctx.stroke();

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 分布式对象调用', 50, 250);
            ctx.fillText('• 跨语言互操作', 250, 250);
            ctx.fillText('• CORBA标准支持', 450, 250);

            ctx.fillStyle = '#e17055';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('✅ 遗产系统集成技术', 350, 300);
        }

        // 绘制JMS（非集成技术）
        function drawJMSIntegration() {
            ctx.fillStyle = '#fdcb6e';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('JMS - 消息服务（非遗产系统集成）', 350, 40);

            // 错误标识
            ctx.fillStyle = '#e17055';
            ctx.fillRect(250, 60, 200, 40);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 3;
            ctx.strokeRect(250, 60, 200, 40);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 16px Arial';
            ctx.fillText('❌ 非集成技术', 350, 85);

            // 消息生产者
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(50, 120, 120, 60);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 2;
            ctx.strokeRect(50, 120, 120, 60);
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('消息生产者', 110, 155);

            // 消息队列/主题
            ctx.fillStyle = '#e17055';
            ctx.fillRect(250, 120, 100, 60);
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 2;
            ctx.strokeRect(250, 120, 100, 60);
            
            ctx.fillStyle = 'white';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('消息队列', 300, 145);
            ctx.font = '12px Arial';
            ctx.fillText('Queue/Topic', 300, 165);

            // 消息消费者
            ctx.fillStyle = '#fdcb6e';
            ctx.fillRect(430, 120, 120, 60);
            ctx.strokeStyle = '#e17055';
            ctx.lineWidth = 2;
            ctx.strokeRect(430, 120, 120, 60);
            
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.fillText('消息消费者', 490, 155);

            // 连接线
            ctx.strokeStyle = '#fdcb6e';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(170, 150);
            ctx.lineTo(250, 150);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(350, 150);
            ctx.lineTo(430, 150);
            ctx.stroke();

            // 特点说明
            ctx.fillStyle = '#2d3436';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('• 异步消息通信', 50, 250);
            ctx.fillText('• 解耦应用组件', 250, 250);
            ctx.fillText('• 消息中间件', 450, 250);

            ctx.fillStyle = '#e17055';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('❌ 不是遗产系统集成技术', 350, 300);
        }

        // 选择答案
        function selectAnswer(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            options.forEach(option => {
                option.style.pointerEvents = 'none';
                if (option === element) {
                    option.classList.add(isCorrect ? 'correct' : 'wrong');
                } else if (option.textContent.includes('A. JDBC、JCA和Java IDL')) {
                    option.classList.add('correct');
                }
            });
            
            setTimeout(() => {
                document.getElementById('explanation').style.display = 'block';
                if (isCorrect) {
                    document.getElementById('successMessage').style.display = 'block';
                    // 播放成功动画序列
                    demonstrateTech('jdbc');
                    setTimeout(() => demonstrateTech('jca'), 2000);
                    setTimeout(() => demonstrateTech('java-idl'), 4000);
                }
            }, 800);
        }

        // 初始化
        window.onload = function() {
            demonstrateTech('jdbc');
            
            // 自动演示序列
            setTimeout(() => demonstrateTech('jca'), 3000);
            setTimeout(() => demonstrateTech('java-idl'), 6000);
            setTimeout(() => demonstrateTech('jms'), 9000);
            setTimeout(() => demonstrateTech('jdbc'), 12000);
        };
    </script>
</body>
</html>
