<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三年级数学 - 除法互动教学</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: 3rem;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        .subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .lesson-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .lesson-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .lesson-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .lesson-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .lesson-card:hover::before {
            left: 100%;
        }

        .lesson-number {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 40px;
            font-weight: bold;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
        }

        .lesson-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .lesson-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .demo-area {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 15px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .canvas-container {
            position: relative;
            width: 100%;
            height: 180px;
        }

        canvas {
            border-radius: 10px;
            background: white;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.1);
        }

        .interactive-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .interactive-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d2ff, #3a7bd5);
            border-radius: 4px;
            width: 0%;
            transition: width 0.5s ease;
        }

        @keyframes fadeInDown {
            from { opacity: 0; transform: translateY(-50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-shapes" id="floatingShapes"></div>
    
    <div class="container">
        <div class="header">
            <h1 class="title">🎯 三年级数学除法</h1>
            <p class="subtitle">互动动画教学 - 让数学变得简单有趣</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="lesson-grid" id="lessonGrid">
            <!-- 课程卡片将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 课程数据
        const lessons = [
            {
                number: "02",
                title: "整十、整百除以一位数的口算",
                description: "学习如何快速计算整十数和整百数除以一位数，掌握口算技巧",
                example: "80 ÷ 4 = 20, 600 ÷ 3 = 200"
            },
            {
                number: "03", 
                title: "两位数除以一位数的口算",
                description: "掌握两位数除以一位数的口算方法，提高计算速度",
                example: "84 ÷ 4 = 21, 96 ÷ 3 = 32"
            },
            {
                number: "04",
                title: "两位数除以一位数（方法一）",
                description: "学习两位数除以一位数的竖式计算方法",
                example: "68 ÷ 4 = 17"
            },
            {
                number: "05",
                title: "两位数除以一位数（方法二）",
                description: "掌握另一种两位数除以一位数的计算技巧",
                example: "75 ÷ 5 = 15"
            },
            {
                number: "06",
                title: "三位数除以一位数",
                description: "学习三位数除以一位数的完整计算过程",
                example: "864 ÷ 4 = 216"
            },
            {
                number: "07",
                title: "被除数中间或末尾有0的除法",
                description: "处理被除数中包含0的特殊除法情况",
                example: "306 ÷ 3 = 102, 420 ÷ 6 = 70"
            },
            {
                number: "08",
                title: "商中间或末尾有0的除法",
                description: "学习商中出现0的除法计算方法",
                example: "618 ÷ 3 = 206, 840 ÷ 4 = 210"
            }
        ];

        // 创建浮动形状
        function createFloatingShapes() {
            const container = document.getElementById('floatingShapes');
            const shapes = ['○', '△', '□', '◇', '☆'];
            
            for (let i = 0; i < 15; i++) {
                const shape = document.createElement('div');
                shape.className = 'shape';
                shape.textContent = shapes[Math.floor(Math.random() * shapes.length)];
                shape.style.left = Math.random() * 100 + '%';
                shape.style.top = Math.random() * 100 + '%';
                shape.style.fontSize = (Math.random() * 30 + 20) + 'px';
                shape.style.animationDelay = Math.random() * 6 + 's';
                shape.style.color = `hsl(${Math.random() * 360}, 70%, 70%)`;
                container.appendChild(shape);
            }
        }

        // 创建课程卡片
        function createLessonCards() {
            const grid = document.getElementById('lessonGrid');

            lessons.forEach((lesson, index) => {
                const card = document.createElement('div');
                card.className = 'lesson-card';
                card.style.animationDelay = (index * 0.2) + 's';

                card.innerHTML = `
                    <div class="lesson-number">${lesson.number}</div>
                    <h3 class="lesson-title">${lesson.title}</h3>
                    <p class="lesson-description">${lesson.description}</p>
                    <div class="demo-area">
                        <div class="canvas-container">
                            <canvas id="canvas${index}" width="300" height="160"></canvas>
                        </div>
                    </div>
                    <button class="interactive-btn" onclick="startDemo(${index})">开始演示</button>
                    <button class="interactive-btn" onclick="practiceMode(${index})">练习模式</button>
                `;

                grid.appendChild(card);

                // 为每个canvas初始化演示
                setTimeout(() => initCanvas(index), 500 + index * 200);
            });
        }

        // 初始化canvas
        function initCanvas(index) {
            const canvas = document.getElementById(`canvas${index}`);
            const ctx = canvas.getContext('2d');

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 绘制初始示例
            drawExample(ctx, index);
        }

        // 绘制示例
        function drawExample(ctx, lessonIndex) {
            ctx.font = '16px Microsoft YaHei';
            ctx.textAlign = 'center';
            ctx.fillStyle = '#2c3e50';

            const examples = [
                '80 ÷ 4 = ?',
                '84 ÷ 4 = ?',
                '68 ÷ 4 = ?',
                '75 ÷ 5 = ?',
                '864 ÷ 4 = ?',
                '306 ÷ 3 = ?',
                '618 ÷ 3 = ?'
            ];

            ctx.fillText(examples[lessonIndex], canvas.width/2, canvas.height/2);

            // 添加装饰性元素
            drawDecorations(ctx, lessonIndex);
        }

        // 绘制装饰元素
        function drawDecorations(ctx, lessonIndex) {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff'];

            ctx.fillStyle = colors[lessonIndex];

            // 绘制小圆点
            for (let i = 0; i < 5; i++) {
                ctx.beginPath();
                ctx.arc(50 + i * 50, 30, 3, 0, Math.PI * 2);
                ctx.fill();

                ctx.beginPath();
                ctx.arc(50 + i * 50, 130, 3, 0, Math.PI * 2);
                ctx.fill();
            }
        }

        // 开始演示动画
        function startDemo(lessonIndex) {
            const canvas = document.getElementById(`canvas${lessonIndex}`);
            const ctx = canvas.getContext('2d');

            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 根据不同课程播放不同动画
            switch(lessonIndex) {
                case 0: animateWholeNumberDivision(ctx); break;
                case 1: animateTwoDigitDivision(ctx); break;
                case 2: animateVerticalDivision1(ctx); break;
                case 3: animateVerticalDivision2(ctx); break;
                case 4: animateThreeDigitDivision(ctx); break;
                case 5: animateZeroInDividend(ctx); break;
                case 6: animateZeroInQuotient(ctx); break;
            }

            updateProgress(lessonIndex);
        }

        // 整十整百除法动画
        function animateWholeNumberDivision(ctx) {
            let step = 0;
            const steps = [
                () => {
                    ctx.font = '20px Microsoft YaHei';
                    ctx.fillStyle = '#2c3e50';
                    ctx.textAlign = 'center';
                    ctx.fillText('80 ÷ 4 = ?', 150, 50);
                },
                () => {
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillText('想：8 ÷ 4 = 2', 150, 80);
                },
                () => {
                    ctx.fillStyle = '#27ae60';
                    ctx.fillText('所以：80 ÷ 4 = 20', 150, 110);

                    // 绘制小方块演示
                    drawBlocks(ctx, 8, 4, 20, 130);
                }
            ];

            function animate() {
                if (step < steps.length) {
                    steps[step]();
                    step++;
                    setTimeout(animate, 1500);
                }
            }

            animate();
        }

        // 绘制方块演示
        function drawBlocks(ctx, total, groups, y, startY) {
            const blockSize = 15;
            const spacing = 20;

            ctx.fillStyle = '#3498db';

            for (let i = 0; i < total; i++) {
                const x = 50 + (i % 4) * spacing;
                const row = Math.floor(i / 4) * spacing;
                ctx.fillRect(x, startY + row, blockSize, blockSize);
            }
        }

        // 两位数除法动画
        function animateTwoDigitDivision(ctx) {
            let step = 0;
            const steps = [
                () => {
                    ctx.clearRect(0, 0, 300, 160);
                    ctx.font = '18px Microsoft YaHei';
                    ctx.fillStyle = '#2c3e50';
                    ctx.textAlign = 'center';
                    ctx.fillText('84 ÷ 4 = ?', 150, 30);
                },
                () => {
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillText('分解：80 + 4', 150, 60);
                },
                () => {
                    ctx.fillStyle = '#f39c12';
                    ctx.fillText('80 ÷ 4 = 20', 100, 90);
                    ctx.fillText('4 ÷ 4 = 1', 200, 90);
                },
                () => {
                    ctx.fillStyle = '#27ae60';
                    ctx.fillText('20 + 1 = 21', 150, 120);

                    // 绘制动画圆圈
                    drawAnimatedCircles(ctx);
                }
            ];

            function animate() {
                if (step < steps.length) {
                    steps[step]();
                    step++;
                    setTimeout(animate, 1800);
                }
            }

            animate();
        }

        // 竖式除法动画1
        function animateVerticalDivision1(ctx) {
            let step = 0;
            ctx.clearRect(0, 0, 300, 160);

            const steps = [
                () => {
                    ctx.font = '16px Microsoft YaHei';
                    ctx.fillStyle = '#2c3e50';
                    ctx.textAlign = 'left';
                    ctx.fillText('68 ÷ 4 = ?', 20, 30);
                    ctx.fillText('用竖式计算：', 20, 50);
                },
                () => {
                    // 绘制竖式框架
                    ctx.strokeStyle = '#34495e';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(100, 70);
                    ctx.lineTo(150, 70);
                    ctx.moveTo(100, 70);
                    ctx.lineTo(100, 130);
                    ctx.stroke();

                    ctx.fillText('4', 80, 85);
                    ctx.fillText('68', 110, 85);
                },
                () => {
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillText('1', 110, 65);
                    ctx.fillText('4', 110, 100);
                    ctx.strokeStyle = '#e74c3c';
                    ctx.beginPath();
                    ctx.moveTo(105, 105);
                    ctx.lineTo(125, 105);
                    ctx.stroke();
                },
                () => {
                    ctx.fillStyle = '#27ae60';
                    ctx.fillText('7', 115, 65);
                    ctx.fillText('28', 110, 115);
                    ctx.fillText('28', 110, 130);
                    ctx.fillText('答案：17', 180, 100);
                }
            ];

            function animate() {
                if (step < steps.length) {
                    steps[step]();
                    step++;
                    setTimeout(animate, 2000);
                }
            }

            animate();
        }

        // 竖式除法动画2
        function animateVerticalDivision2(ctx) {
            animateWithColors(ctx, '75 ÷ 5 = 15', ['#9b59b6', '#3498db', '#e67e22']);
        }

        // 三位数除法动画
        function animateThreeDigitDivision(ctx) {
            animateWithColors(ctx, '864 ÷ 4 = 216', ['#1abc9c', '#e74c3c', '#f1c40f']);
        }

        // 被除数有0的除法
        function animateZeroInDividend(ctx) {
            animateWithColors(ctx, '306 ÷ 3 = 102', ['#e91e63', '#00bcd4', '#4caf50']);
        }

        // 商有0的除法
        function animateZeroInQuotient(ctx) {
            animateWithColors(ctx, '618 ÷ 3 = 206', ['#ff5722', '#673ab7', '#009688']);
        }

        // 通用彩色动画
        function animateWithColors(ctx, equation, colors) {
            let step = 0;
            ctx.clearRect(0, 0, 300, 160);

            const steps = [
                () => {
                    ctx.font = '20px Microsoft YaHei';
                    ctx.fillStyle = colors[0];
                    ctx.textAlign = 'center';
                    ctx.fillText(equation, 150, 80);
                },
                () => {
                    ctx.fillStyle = colors[1];
                    ctx.fillText('计算中...', 150, 110);
                    drawSpinner(ctx, 150, 130);
                },
                () => {
                    ctx.fillStyle = colors[2];
                    ctx.fillText('✓ 完成！', 150, 140);
                }
            ];

            function animate() {
                if (step < steps.length) {
                    steps[step]();
                    step++;
                    setTimeout(animate, 1500);
                }
            }

            animate();
        }

        // 绘制动画圆圈
        function drawAnimatedCircles(ctx) {
            let radius = 5;
            let growing = true;

            function animateCircle() {
                ctx.clearRect(200, 130, 60, 30);
                ctx.fillStyle = '#3498db';
                ctx.beginPath();
                ctx.arc(230, 145, radius, 0, Math.PI * 2);
                ctx.fill();

                if (growing) {
                    radius += 0.5;
                    if (radius > 15) growing = false;
                } else {
                    radius -= 0.5;
                    if (radius < 5) growing = true;
                }

                if (radius > 0) {
                    requestAnimationFrame(animateCircle);
                }
            }

            animateCircle();
        }

        // 绘制加载动画
        function drawSpinner(ctx, x, y) {
            let angle = 0;

            function spin() {
                ctx.clearRect(x-15, y-15, 30, 30);
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.arc(x, y, 10, angle, angle + Math.PI);
                ctx.stroke();

                angle += 0.2;
                if (angle < Math.PI * 4) {
                    requestAnimationFrame(spin);
                }
            }

            spin();
        }

        // 练习模式
        function practiceMode(lessonIndex) {
            const canvas = document.getElementById(`canvas${lessonIndex}`);
            const ctx = canvas.getContext('2d');

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 生成练习题
            const problems = generateProblems(lessonIndex);
            let currentProblem = 0;

            function showProblem() {
                if (currentProblem < problems.length) {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.font = '18px Microsoft YaHei';
                    ctx.fillStyle = '#2c3e50';
                    ctx.textAlign = 'center';
                    ctx.fillText(`题目 ${currentProblem + 1}:`, 150, 40);
                    ctx.fillText(problems[currentProblem].question, 150, 70);
                    ctx.fillText('点击查看答案', 150, 100);

                    // 添加点击事件
                    canvas.onclick = () => {
                        ctx.fillStyle = '#27ae60';
                        ctx.fillText(`答案: ${problems[currentProblem].answer}`, 150, 130);
                        setTimeout(() => {
                            currentProblem++;
                            showProblem();
                        }, 2000);
                    };
                } else {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    ctx.fillStyle = '#e74c3c';
                    ctx.fillText('🎉 练习完成！', 150, 80);
                    canvas.onclick = null;
                }
            }

            showProblem();
        }

        // 生成练习题
        function generateProblems(lessonIndex) {
            const problemSets = [
                [ // 整十整百除法
                    {question: '60 ÷ 3 = ?', answer: '20'},
                    {question: '400 ÷ 2 = ?', answer: '200'},
                    {question: '90 ÷ 9 = ?', answer: '10'}
                ],
                [ // 两位数口算
                    {question: '48 ÷ 4 = ?', answer: '12'},
                    {question: '63 ÷ 3 = ?', answer: '21'},
                    {question: '56 ÷ 7 = ?', answer: '8'}
                ],
                [ // 两位数竖式1
                    {question: '72 ÷ 6 = ?', answer: '12'},
                    {question: '84 ÷ 7 = ?', answer: '12'},
                    {question: '96 ÷ 8 = ?', answer: '12'}
                ],
                [ // 两位数竖式2
                    {question: '65 ÷ 5 = ?', answer: '13'},
                    {question: '78 ÷ 6 = ?', answer: '13'},
                    {question: '91 ÷ 7 = ?', answer: '13'}
                ],
                [ // 三位数除法
                    {question: '246 ÷ 2 = ?', answer: '123'},
                    {question: '369 ÷ 3 = ?', answer: '123'},
                    {question: '488 ÷ 4 = ?', answer: '122'}
                ],
                [ // 被除数有0
                    {question: '204 ÷ 2 = ?', answer: '102'},
                    {question: '405 ÷ 5 = ?', answer: '81'},
                    {question: '608 ÷ 8 = ?', answer: '76'}
                ],
                [ // 商有0
                    {question: '412 ÷ 2 = ?', answer: '206'},
                    {question: '615 ÷ 3 = ?', answer: '205'},
                    {question: '824 ÷ 4 = ?', answer: '206'}
                ]
            ];

            return problemSets[lessonIndex] || [];
        }

        // 更新进度条
        function updateProgress(lessonIndex) {
            const progress = ((lessonIndex + 1) / lessons.length) * 100;
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = progress + '%';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            createFloatingShapes();
            createLessonCards();

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.key >= '1' && e.key <= '7') {
                    const index = parseInt(e.key) - 1;
                    if (index < lessons.length) {
                        startDemo(index);
                    }
                }
            });

            // 添加鼠标悬停效果
            document.addEventListener('mouseover', function(e) {
                if (e.target.classList.contains('lesson-card')) {
                    e.target.style.transform = 'translateY(-10px) scale(1.02)';
                }
            });

            document.addEventListener('mouseout', function(e) {
                if (e.target.classList.contains('lesson-card')) {
                    e.target.style.transform = 'translateY(0) scale(1)';
                }
            });
        });
    </script>
</body>
</html>
