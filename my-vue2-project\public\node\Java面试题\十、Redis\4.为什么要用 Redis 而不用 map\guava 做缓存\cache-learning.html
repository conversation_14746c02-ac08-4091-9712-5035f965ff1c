<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存知识互动学习</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .title {
            text-align: center;
            color: white;
            font-size: 3rem;
            margin-bottom: 60px;
            opacity: 0;
            animation: fadeInUp 1s ease-out forwards;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
            animation: slideInUp 0.8s ease-out forwards;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .section-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }

        canvas {
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        canvas:hover {
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transform: translateY(-5px);
        }

        .explanation {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #555;
            margin: 20px 0;
            text-align: justify;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }

        .interactive-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .interactive-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .game-area {
            background: #f0f2f5;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }

        .score {
            font-size: 1.5rem;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 20px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 缓存知识互动学习</h1>
        
        <div class="section">
            <h2 class="section-title">📚 什么是缓存？</h2>
            <div class="explanation">
                <span class="highlight">缓存</span>就像是你的书桌抽屉，把经常用的东西放在最容易拿到的地方。在计算机世界里，缓存是一种临时存储技术，用来快速访问经常使用的数据。
            </div>
            <div class="canvas-container">
                <canvas id="introCanvas" width="600" height="300"></canvas>
            </div>
            <button class="interactive-btn" onclick="startIntroAnimation()">🎬 播放介绍动画</button>
        </div>

        <div class="section">
            <h2 class="section-title">🏠 本地缓存</h2>
            <div class="explanation">
                <span class="highlight">本地缓存</span>就像每个人自己的小仓库。在Java中，使用Map或Guava实现。特点是：
                <br>• ⚡ 轻量快速 - 就在你身边，拿取方便
                <br>• 🔄 生命周期短 - 程序关闭就消失
                <br>• 🏘️ 各自独立 - 每个实例都有自己的缓存
            </div>
            <div class="canvas-container">
                <canvas id="localCacheCanvas" width="600" height="350"></canvas>
            </div>
            <div class="game-area">
                <div class="score">得分: <span id="localScore">0</span></div>
                <button class="interactive-btn" onclick="startLocalCacheDemo()">🎮 体验本地缓存</button>
                <button class="interactive-btn" onclick="addLocalInstance()">➕ 添加实例</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🌐 分布式缓存</h2>
            <div class="explanation">
                <span class="highlight">分布式缓存</span>就像一个大型公共仓库，所有人都可以使用。使用Redis或Memcached实现。特点是：
                <br>• 🤝 数据共享 - 所有实例共用一份数据
                <br>• ✅ 保持一致性 - 数据同步更新
                <br>• 🏗️ 架构复杂 - 需要维护缓存服务器
            </div>
            <div class="canvas-container">
                <canvas id="distributedCacheCanvas" width="600" height="350"></canvas>
            </div>
            <div class="game-area">
                <div class="score">得分: <span id="distributedScore">0</span></div>
                <button class="interactive-btn" onclick="startDistributedCacheDemo()">🎮 体验分布式缓存</button>
                <button class="interactive-btn" onclick="simulateDataSync()">🔄 模拟数据同步</button>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚖️ 对比游戏</h2>
            <div class="explanation">
                通过拖拽游戏来理解两种缓存的区别！将特性拖到正确的缓存类型上。
            </div>
            <div class="canvas-container">
                <canvas id="comparisonCanvas" width="700" height="400"></canvas>
            </div>
            <div class="game-area">
                <div class="score">正确率: <span id="gameScore">0%</span></div>
                <button class="interactive-btn" onclick="resetComparisonGame()">🔄 重新开始</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let localScore = 0;
        let distributedScore = 0;
        let gameCorrect = 0;
        let gameTotal = 0;
        let animationFrameId;

        // 介绍动画
        function startIntroAnimation() {
            const canvas = document.getElementById('introCanvas');
            const ctx = canvas.getContext('2d');
            let frame = 0;

            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制数据流动动画
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                
                // 数据库
                ctx.fillStyle = '#4CAF50';
                ctx.fillRect(50, centerY - 30, 80, 60);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('数据库', 90, centerY + 5);
                
                // 缓存
                ctx.fillStyle = '#FF9800';
                ctx.fillRect(centerX - 40, centerY - 30, 80, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('缓存', centerX, centerY + 5);
                
                // 应用程序
                ctx.fillStyle = '#2196F3';
                ctx.fillRect(canvas.width - 130, centerY - 30, 80, 60);
                ctx.fillStyle = 'white';
                ctx.fillText('应用程序', canvas.width - 90, centerY + 5);
                
                // 动画数据流
                const progress = (frame % 120) / 120;
                if (progress < 0.5) {
                    // 从数据库到缓存
                    const x = 130 + (centerX - 130 - 40) * (progress * 2);
                    ctx.fillStyle = '#E91E63';
                    ctx.beginPath();
                    ctx.arc(x, centerY, 8, 0, Math.PI * 2);
                    ctx.fill();
                } else {
                    // 从缓存到应用程序
                    const x = centerX + 40 + (canvas.width - 130 - centerX - 40) * ((progress - 0.5) * 2);
                    ctx.fillStyle = '#E91E63';
                    ctx.beginPath();
                    ctx.arc(x, centerY, 8, 0, Math.PI * 2);
                    ctx.fill();
                }
                
                frame++;
                if (frame < 240) {
                    requestAnimationFrame(animate);
                }
            }
            
            animate();
        }

        // 本地缓存演示
        function startLocalCacheDemo() {
            const canvas = document.getElementById('localCacheCanvas');
            const ctx = canvas.getContext('2d');
            let instances = [
                { x: 100, y: 150, cache: new Map(), color: '#FF5722' },
                { x: 300, y: 150, cache: new Map(), color: '#4CAF50' },
                { x: 500, y: 150, cache: new Map(), color: '#2196F3' }
            ];
            
            function drawLocalCache() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                instances.forEach((instance, index) => {
                    // 绘制实例
                    ctx.fillStyle = instance.color;
                    ctx.fillRect(instance.x - 40, instance.y - 40, 80, 80);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`实例${index + 1}`, instance.x, instance.y - 10);
                    ctx.fillText(`缓存: ${instance.cache.size}`, instance.x, instance.y + 10);
                    
                    // 绘制缓存数据
                    let dataY = instance.y + 60;
                    instance.cache.forEach((value, key) => {
                        ctx.fillStyle = '#FFC107';
                        ctx.fillRect(instance.x - 30, dataY, 60, 20);
                        ctx.fillStyle = 'black';
                        ctx.font = '10px Arial';
                        ctx.fillText(`${key}:${value}`, instance.x, dataY + 12);
                        dataY += 25;
                    });
                });
                
                // 添加说明文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('每个实例都有独立的缓存，数据不共享', canvas.width / 2, 50);
            }
            
            // 模拟数据添加
            function addData() {
                const randomInstance = Math.floor(Math.random() * instances.length);
                const key = `key${Date.now() % 1000}`;
                const value = Math.floor(Math.random() * 100);
                instances[randomInstance].cache.set(key, value);
                
                localScore += 10;
                document.getElementById('localScore').textContent = localScore;
                drawLocalCache();
            }
            
            drawLocalCache();
            
            // 自动添加数据演示
            const interval = setInterval(addData, 1500);
            setTimeout(() => clearInterval(interval), 10000);
        }

        function addLocalInstance() {
            // 这个函数可以添加新的实例到演示中
            startLocalCacheDemo();
        }

        // 分布式缓存演示
        function startDistributedCacheDemo() {
            const canvas = document.getElementById('distributedCacheCanvas');
            const ctx = canvas.getContext('2d');
            let sharedCache = new Map();
            let instances = [
                { x: 100, y: 100, color: '#FF5722' },
                { x: 300, y: 100, color: '#4CAF50' },
                { x: 500, y: 100, color: '#2196F3' }
            ];
            
            function drawDistributedCache() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制共享缓存服务器
                ctx.fillStyle = '#9C27B0';
                ctx.fillRect(250, 200, 100, 80);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('Redis缓存', 300, 230);
                ctx.fillText(`数据: ${sharedCache.size}`, 300, 250);
                
                // 绘制实例
                instances.forEach((instance, index) => {
                    ctx.fillStyle = instance.color;
                    ctx.fillRect(instance.x - 30, instance.y - 30, 60, 60);
                    ctx.fillStyle = 'white';
                    ctx.font = '12px Arial';
                    ctx.fillText(`实例${index + 1}`, instance.x, instance.y);
                    
                    // 绘制连接线
                    ctx.strokeStyle = '#666';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(instance.x, instance.y + 30);
                    ctx.lineTo(300, 200);
                    ctx.stroke();
                });
                
                // 绘制共享缓存数据
                let dataY = 300;
                sharedCache.forEach((value, key) => {
                    ctx.fillStyle = '#FFC107';
                    ctx.fillRect(220, dataY, 160, 20);
                    ctx.fillStyle = 'black';
                    ctx.font = '12px Arial';
                    ctx.fillText(`${key}: ${value}`, 300, dataY + 12);
                    dataY += 25;
                });
                
                // 说明文字
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('所有实例共享同一个缓存服务器', canvas.width / 2, 30);
            }
            
            function addSharedData() {
                const key = `shared_${Date.now() % 1000}`;
                const value = Math.floor(Math.random() * 100);
                sharedCache.set(key, value);
                
                distributedScore += 15;
                document.getElementById('distributedScore').textContent = distributedScore;
                drawDistributedCache();
            }
            
            drawDistributedCache();
            
            const interval = setInterval(addSharedData, 2000);
            setTimeout(() => clearInterval(interval), 12000);
        }

        function simulateDataSync() {
            // 模拟数据同步动画
            startDistributedCacheDemo();
        }

        // 对比游戏
        function resetComparisonGame() {
            const canvas = document.getElementById('comparisonCanvas');
            const ctx = canvas.getContext('2d');
            
            gameCorrect = 0;
            gameTotal = 0;
            
            const features = [
                { text: '轻量快速', correct: 'local', x: 50, y: 50 },
                { text: '数据共享', correct: 'distributed', x: 200, y: 50 },
                { text: '架构复杂', correct: 'distributed', x: 350, y: 50 },
                { text: '各自独立', correct: 'local', x: 500, y: 50 },
                { text: '保持一致性', correct: 'distributed', x: 50, y: 100 },
                { text: '生命周期短', correct: 'local', x: 200, y: 100 }
            ];
            
            function drawComparisonGame() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制目标区域
                ctx.fillStyle = '#FFE0E0';
                ctx.fillRect(50, 200, 250, 150);
                ctx.fillStyle = '#E0F0FF';
                ctx.fillRect(350, 200, 250, 150);
                
                ctx.fillStyle = '#333';
                ctx.font = '18px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('本地缓存', 175, 230);
                ctx.fillText('分布式缓存', 475, 230);
                
                // 绘制特性卡片
                features.forEach(feature => {
                    ctx.fillStyle = '#FFC107';
                    ctx.fillRect(feature.x, feature.y, 120, 30);
                    ctx.fillStyle = 'black';
                    ctx.font = '14px Arial';
                    ctx.fillText(feature.text, feature.x + 60, feature.y + 20);
                });
                
                // 游戏说明
                ctx.fillStyle = '#333';
                ctx.font = '16px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('拖拽特性到正确的缓存类型区域', canvas.width / 2, 30);
            }
            
            drawComparisonGame();
            updateGameScore();
        }

        function updateGameScore() {
            const percentage = gameTotal > 0 ? Math.round((gameCorrect / gameTotal) * 100) : 0;
            document.getElementById('gameScore').textContent = percentage + '%';
        }

        // 初始化
        window.onload = function() {
            startIntroAnimation();
            setTimeout(startLocalCacheDemo, 2000);
            setTimeout(startDistributedCacheDemo, 4000);
            setTimeout(resetComparisonGame, 6000);
        };
    </script>
</body>
</html>
