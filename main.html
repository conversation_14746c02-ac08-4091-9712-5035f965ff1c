<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>嵌入式处理器与MMU知识解析</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            color: #333;
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            max-width: 900px;
            width: 100%;
            margin-bottom: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #e0e6ed;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .question-section, .answer-section, .explanation-section, .animation-section {
            margin-bottom: 25px;
        }
        .question {
            font-size: 1.2em;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .options label {
            display: block;
            margin-bottom: 10px;
            font-size: 1.1em;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .options label:hover {
            background-color: #eef2f5;
        }
        .options input[type="radio"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        .correct-answer {
            font-size: 1.1em;
            color: #28a745;
            font-weight: bold;
            padding: 10px;
            background-color: #e6ffed;
            border-left: 5px solid #28a745;
            border-radius: 5px;
        }
        .explanation p {
            margin-bottom: 15px;
            text-align: justify;
        }
        .explanation strong {
            color: #34495e;
        }
        canvas {
            border: 2px solid #ccc;
            display: block;
            margin: 20px auto;
            background-color: #fafafa;
            border-radius: 8px;
        }
        .controls {
            text-align: center;
            margin-top: 15px;
        }
        .controls button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        .controls button:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        .controls button:active {
            transform: translateY(0);
        }
        .note {
            font-size: 0.9em;
            color: #777;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>嵌入式处理器与MMU知识解析</h1>

        <div class="question-section">
            <h2>题目</h2>
            <div class="question">
                以下嵌入式处理器类型中不具备内存管理单元(MMU)的是（），嵌入式操作系统（作答此空）可以运行在它上面。
            </div>
            <div class="options">
                <label><input type="radio" name="answer" value="A"> A. Linux</label>
                <label><input type="radio" name="answer" value="B"> B. VxWorks653</label>
                <label><input type="radio" name="answer" value="C"> C. µC/OS-II</label>
                <label><input type="radio" name="answer" value="D"> D. Windows CE</label>
            </div>
        </div>

        <div class="answer-section">
            <h2>答案</h2>
            <div class="correct-answer">
                正确答案: C
            </div>
        </div>

        <div class="explanation-section">
            <h2>解析</h2>
            <div class="explanation">
                <p>本题考查嵌入式处理器知识。<strong>MMU（Memory Management Unit）</strong>是存储器管理单元的缩写，是用来管理虚拟内存系统的器件。MMU通常是CPU的一部分，本身有少量存储空间存放从虚拟地址到物理地址的匹配表。此表称作<strong>TLB（Translation Lookaside Buffer，转换旁置缓冲）</strong>。所有数据请求都送往MMU，由MMU决定数据是在RAM内还是在大容量存储器设备内。如果数据不在存储空间内，MMU将产生页面错误中断。</p>
                <p>MMU的两个主要功能是将虚拟地址转换成物理地址，控制存储器存取允许。MMU关闭时，虚拟地址直接输出到物理地址总线。</p>
                <p><strong>Cortex-M3</strong>处理器采用ARMv7-M架构，它包括所有的16位Thumb指令集和基本的32位Thumb-2指令集架构。Cortex-M3支持线程模式和处理模式。在复位时处理器进入“线程模式”，异常返回时也会进入该模式，特权和用户(非特权)模式代码能够在“线程模式”下运行。出现异常模式时处理器进入“处理模式”，在处理模式下，所有代码都是特权访问的。<strong>µC/OS-II</strong>可以运行在Cortex-M3处理器上。</p>
                <p class="note">注意：Linux、VxWorks653和Windows CE这类操作系统通常是功能更全面的操作系统，它们需要MMU来支持复杂的虚拟内存管理和多任务保护。而像µC/OS-II这样的轻量级实时操作系统（RTOS）则设计为在资源有限、不带MMU的微控制器上高效运行，它们通常采用更简单的内存管理方式，例如使用内存保护单元（MPU）或直接物理地址访问。</p>
            </div>
        </div>

        <div class="animation-section">
            <h2>MMU工作原理动画演示</h2>
            <canvas id="mmuCanvas" width="800" height="400"></canvas>
            <div class="controls">
                <button id="toggleMmuButton">切换MMU状态 (当前: 开启)</button>
                <button id="resetAnimationButton">重置动画</button>
            </div>
            <p class="note">点击“切换MMU状态”按钮，观察虚拟地址如何转换为物理地址，以及MMU关闭时的直接映射。</p>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('mmuCanvas');
        const ctx = canvas.getContext('2d');
        const toggleMmuButton = document.getElementById('toggleMmuButton');
        const resetAnimationButton = document.getElementById('resetAnimationButton');

        let isMmuEnabled = true;
        let animationFrameId;
        let virtualAddressBox = { x: 50, y: 180, width: 80, height: 40, text: "虚拟地址" };
        let physicalAddressBox = { x: 670, y: 180, width: 80, height: 40, text: "物理地址" };
        let mmuBox = { x: 350, y: 160, width: 100, height: 80, text: "MMU" };
        let addressPacket = { x: virtualAddressBox.x + virtualAddressBox.width / 2 - 10, y: virtualAddressBox.y + virtualAddressBox.height / 2 - 10, size: 20, color: 'red', text: 'VA' };
        let translationProgress = 0; // 0 to 1 for animation progress
        const animationSpeed = 0.01;

        function drawMMUDiagram() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw Virtual Address Space
            ctx.fillStyle = '#add8e6';
            ctx.fillRect(20, 100, 200, 200);
            ctx.strokeStyle = '#3182bd';
            ctx.lineWidth = 2;
            ctx.strokeRect(20, 100, 200, 200);
            ctx.fillStyle = '#2c3e50';
            ctx.font = '18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('虚拟地址空间', 120, 80);

            // Draw Physical Address Space
            ctx.fillStyle = '#90ee90';
            ctx.fillRect(580, 100, 200, 200);
            ctx.strokeStyle = '#2d882d';
            ctx.lineWidth = 2;
            ctx.strokeRect(580, 100, 200, 200);
            ctx.fillStyle = '#2c3e50';
            ctx.fillText('物理地址空间', 680, 80);

            // Draw MMU box
            ctx.fillStyle = '#ffb3ba';
            ctx.fillRect(mmuBox.x, mmuBox.y, mmuBox.width, mmuBox.height);
            ctx.strokeStyle = '#c63a56';
            ctx.lineWidth = 2;
            ctx.strokeRect(mmuBox.x, mmuBox.y, mmuBox.width, mmuBox.height);
            ctx.fillStyle = '#2c3e50';
            ctx.fillText('MMU', mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height / 2 + 5);

            // Draw TLB text
            ctx.font = '14px Arial';
            ctx.fillStyle = '#555';
            ctx.fillText('TLB (缓存)', mmuBox.x + mmuBox.width / 2, mmuBox.y + mmuBox.height + 20);

            // Draw arrows
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(220, 200);
            ctx.lineTo(mmuBox.x, 200);
            ctx.moveTo(mmuBox.x + mmuBox.width, 200);
            ctx.lineTo(580, 200);
            ctx.stroke();

            // Arrowheads
            drawArrowhead(ctx, 220, 200, mmuBox.x, 200);
            drawArrowhead(ctx, mmuBox.x + mmuBox.width, 200, 580, 200);

            // MMU bypass path (dashed line)
            if (!isMmuEnabled) {
                ctx.strokeStyle = '#888';
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(220, 230);
                ctx.lineTo(580, 230);
                ctx.stroke();
                ctx.setLineDash([]); // Reset line dash
                drawArrowhead(ctx, 220, 230, 580, 230);
                ctx.fillStyle = '#f00';
                ctx.font = '16px Arial';
                ctx.fillText('直接映射', 400, 220);
            }
        }

        function drawArrowhead(context, fromX, fromY, toX, toY) {
            const headlen = 10; // length of head in pixels
            const angle = Math.atan2(toY - fromY, toX - fromX);
            context.beginPath();
            context.moveTo(toX, toY);
            context.lineTo(toX - headlen * Math.cos(angle - Math.PI / 6), toY - headlen * Math.sin(angle - Math.PI / 6));
            context.moveTo(toX, toY);
            context.lineTo(toX - headlen * Math.cos(angle + Math.PI / 6), toY - headlen * Math.sin(angle + Math.PI / 6));
            context.stroke();
        }

        function updateAddressPacket() {
            if (isMmuEnabled) {
                // VA -> MMU
                if (translationProgress < 0.5) {
                    addressPacket.x = virtualAddressBox.x + virtualAddressBox.width / 2 - 10 + (mmuBox.x - (virtualAddressBox.x + virtualAddressBox.width / 2 - 10)) * (translationProgress / 0.5);
                    addressPacket.y = virtualAddressBox.y + virtualAddressBox.height / 2 - 10;
                    addressPacket.text = 'VA';
                }
                // MMU -> PA
                else {
                    addressPacket.x = mmuBox.x + mmuBox.width / 2 - 10 + (physicalAddressBox.x + physicalAddressBox.width / 2 - 10 - (mmuBox.x + mmuBox.width / 2 - 10)) * ((translationProgress - 0.5) / 0.5);
                    addressPacket.y = mmuBox.y + mmuBox.height / 2 - 10;
                    addressPacket.text = 'PA';
                }
            } else {
                // VA -> PA (bypassing MMU)
                addressPacket.x = virtualAddressBox.x + virtualAddressBox.width / 2 - 10 + (physicalAddressBox.x + physicalAddressBox.width / 2 - 10 - (virtualAddressBox.x + virtualAddressBox.width / 2 - 10)) * translationProgress;
                addressPacket.y = virtualAddressBox.y + virtualAddressBox.height / 2 - 10 + (isMmuEnabled ? 0 : 30); // Shift down when bypassing
                addressPacket.text = 'VA (直接)';
            }

            translationProgress += animationSpeed;
            if (translationProgress > 1) {
                translationProgress = 0; // Loop animation
            }
        }

        function drawAddressPacket() {
            ctx.fillStyle = addressPacket.color;
            ctx.fillRect(addressPacket.x, addressPacket.y, addressPacket.size, addressPacket.size);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(addressPacket.text, addressPacket.x + addressPacket.size / 2, addressPacket.y + addressPacket.size / 2);
        }

        function animate() {
            drawMMUDiagram();
            updateAddressPacket();
            drawAddressPacket();
            animationFrameId = requestAnimationFrame(animate);
        }

        function resetAnimation() {
            cancelAnimationFrame(animationFrameId);
            translationProgress = 0;
            addressPacket.x = virtualAddressBox.x + virtualAddressBox.width / 2 - 10;
            addressPacket.y = virtualAddressBox.y + virtualAddressBox.height / 2 - 10;
            addressPacket.text = 'VA';
            animate();
        }

        toggleMmuButton.addEventListener('click', () => {
            isMmuEnabled = !isMmuEnabled;
            toggleMmuButton.textContent = `切换MMU状态 (当前: ${isMmuEnabled ? '开启' : '关闭'})`;
            resetAnimation(); // Reset animation to reflect new state immediately
        });

        resetAnimationButton.addEventListener('click', resetAnimation);

        // Initial setup
        resetAnimation(); // Start the animation
    </script>
</body>
</html> 